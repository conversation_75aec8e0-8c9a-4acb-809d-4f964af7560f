{"_id": "@babel/plugin-transform-parameters", "_rev": "141-bec22a8a2778b49e7e88e463879545d7", "name": "@babel/plugin-transform-parameters", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.27.7"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "06373f41e0cad52ebbf2beae0379645759aea3c6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.4.tgz", "integrity": "sha512-w/lbxsAluAJ8EhLQ7Q5JGHc5PFd8Caz/w3fHBShhrRHXjQiuaESUD+PQFGZidfwOKZbP31Swq2Pn3SqshZeTXQ==", "signatures": [{"sig": "MEUCIQCXm6sbiEBd/qd97pCVMT45IVsUV7kYNoxbDIiAcnHWkQIgFy7ojnutvEqIrnzdVgVcpoV8EUkQHiVgPEq2Qa+PAac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4", "@babel/helper-call-delegate": "7.0.0-beta.4", "@babel/helper-get-function-arity": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.4.tgz_1509388582923_0.6625759026501328", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a5f9842fb020f8927bdde29360e6f33ed3d97c03", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.5.tgz", "integrity": "sha512-Zc3Nzvu6IiKfA3nqk+qjPdrv58/4Vl7thxqy+aRnFQMZMTsaiGhENjfnIBzTNHdFitEBqYGbXwNBHSqWLwHSjw==", "signatures": [{"sig": "MEYCIQC0oHw9g6BuHwSSN3eaBCHA16pCyXQorF5vo2lkPn1ZLwIhANtazzSSxbyeWHL6PCgTco8UHZYAk9qHmImdoNetFXrA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5", "@babel/helper-call-delegate": "7.0.0-beta.5", "@babel/helper-get-function-arity": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.5.tgz_1509397079356_0.5225971634499729", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "98cdb5d361f6c28a210a17023d6f26ac9ec4973f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.31.tgz", "integrity": "sha512-g/EvlxyCD4Wy/qd7twifX7cBMuE0Sdz4+g/wNGLRdQXgu5vX7jjEanbmD1dd6W4byRL55BqXq9fBYDjM0Yw4Fw==", "signatures": [{"sig": "MEYCIQDCFHN04NLvfditlO4UswImX8njck7jEEqP18Mj3NUT3gIhAINvbHHJoJn5r63aJlEJg2UZ7Y6o4/NBhbo8PTDCgN50", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31", "@babel/helper-call-delegate": "7.0.0-beta.31", "@babel/helper-get-function-arity": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.31.tgz_1509739472841_0.5407448010519147", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3138d3684860d72831d9bbc7ee645b0d5e687f13", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.32.tgz", "integrity": "sha512-LSp9l0n2Xayn8gidVGunCRtyxEAagSahqZgncw3ERkOrwTNlpfE6eaZTqxp/r2kx6kHYt1md/OIaIAxGhCw0+A==", "signatures": [{"sig": "MEYCIQCDAvaPTTh6GFIFTEeRyYrvpFWj2Clt8Sq3qgJY+tZnRQIhAI5Zq5oravZ35Yu9ILWWT9eJDexpqcIHUN1ohndUj2Jk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.32", "@babel/helper-get-function-arity": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.32.tgz_1510493648300_0.05253313505090773", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7eba68099624d312084d5d26c3c3d74b2cff7eea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.33.tgz", "integrity": "sha512-Y2FGq1GQn0COUWqcojwDL44Hfp599skuMtg0qWgfoNK0I9Ta1iW7rmSwN2k0Lyh1BIBYGd+1FcxJtVkXCnzqoQ==", "signatures": [{"sig": "MEYCIQDh5gzIXThBnja1ZHub8WwT4Q/2UZGfIaI7DdGQCj3wKwIhALMcr3u2I0jIgjr6397S7e3yZ7zKbt+DWIUMQf9F3upO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.33", "@babel/helper-get-function-arity": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.33.tgz_1512138566548_0.15552277443930507", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e45d973d264c7fb92e71ec190f9be67eb4715764", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.34.tgz", "integrity": "sha512-pEQtoqpwaEgE7ZIqgLJmJTrKIJ19kaUr2ExoxujbQe/m96eJF7d6TtBBLbpHbJEgGBbjzWI7uRQRSnTTfoomZQ==", "signatures": [{"sig": "MEUCIG+8K7gdstxPptl/j5yxRibrdRQR2OaJBuRw9u1TaCtWAiEApfbIHcz45U01bQGURq82z/GOLCQmal7zP5f+tBwChvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.34", "@babel/helper-get-function-arity": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.34.tgz_1512225625673_0.09878251329064369", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6c44edc8218df6afc635d65593bf391a0482993c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.35.tgz", "integrity": "sha512-mkDK9XjLcJ66WsfjjAbCzEsJnn7KRKt9osD1MvnVPJ2v2QaRGeh+eHKlENisRj6itk/zhxpcTe8jG/ObnM1PNw==", "signatures": [{"sig": "MEUCIQDDwxHzh4N40+qGflJfsRQwiqXajH//8AUA7SM100XPXQIgZ4Rx/fBifDX4kiafyhPlo4v15Rat7H0oBiQ4QcatXrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.35", "@babel/helper-get-function-arity": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.35.tgz_1513288112873_0.858028685906902", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8096155c1373169299d1eb3e3fd8c113943cf478", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.36.tgz", "integrity": "sha512-1y6X6on5H4AWudyJD00WvCvuUNnKUHeG/yJpNQW5bgmJCZYhuvs4lgCZlwKO0GuoymPULGjOToUun3agCX+39A==", "signatures": [{"sig": "MEUCIEI+F2Z5CuyngvHzOIlLNNIeug0fQeAoGqwf28yJRrV4AiEAv0KLLYdw55u0XKNu9CNfUw49Fv1i1VGzysLhxpEcIU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.36", "@babel/helper-get-function-arity": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.36.tgz_1514228749142_0.8523423920851201", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fd91c729acd665a86da0b93041e38ea9669b5244", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.37.tgz", "integrity": "sha512-0s0tMa6lTgxZeqz7Zt7SKpRIOaENCf1pKBcCW8ljtAgUCV/OlUh8e6f0UpYeKE4m4vpQKAJAl2oCKI5ZPSV9SQ==", "signatures": [{"sig": "MEUCIDzhRF0eUt9Gec+OmBVxQPncUmTo+0IiCqnGiPF/1EplAiEAr/1a1bda6qpHsREvJXLz5qhykqKOJAyVOOciSU9664U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.37", "@babel/helper-get-function-arity": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.37.tgz_1515427428294_0.005886078579351306", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7cbcff7d4add9e1d58a4b6c340d1feebd31014cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.38.tgz", "integrity": "sha512-D8aOMPvzGSJ+7uDPoanWlZNM7/F6F6kffvT7f63qQtjktikwusknduy/4jXhEWWeOaQcUcDbirvxVT0abTS6SQ==", "signatures": [{"sig": "MEYCIQCyM/51YttNacCl0fIL81mMCTiVSGYLEIZ7QmnOitc/XgIhAL9MgdxH+f+rer2vuEpKvEiX2ykYlZ2pUlIRgIrUcG4G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.38", "@babel/helper-get-function-arity": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.38.tgz_1516206767270_0.36502413894049823", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "bcd6f99c7839f52fce3bd05e89841d187f1d4ef8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.39.tgz", "integrity": "sha512-x4o3mZSQ5ilS4LzkpGhiGQGkvQeR0DQXmbtwtt0Mrxe3k67iXRdNe9WnZQQPwZgKFopgM8sqKyW8jwcnGIkaQA==", "signatures": [{"sig": "MEUCIBWptgW+F/eJ3KgwCeIbpGZQnMbsFHcj7uZI9ZQ2EIeqAiEA3pnRMoeTJGJrB4TdOkqXoP3GzZYXBGe+ULKsZY++AA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.39", "@babel/helper-get-function-arity": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters-7.0.0-beta.39.tgz_1517344129593_0.7684497963637114", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "efa366fab0dcbd0221b46aa2662c324b4b414d1d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.40.tgz", "fileCount": 5, "integrity": "sha512-JShFDeKEzwwTB+pHcUuLdX9zPi98sRekvtdCEOt8UoF5pzW02k1XdsVOckp/PzcEdoGAgZiiI1PFkJZ+xanfPg==", "signatures": [{"sig": "MEUCIQCu+g93BSAFZFgujIptAMVuWJkjZgdNYFiQfW4jxYlbcwIgaC9gc7WtPktbjmhHpQG2YWEGO0f0bX814tQ9utYNobk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15528}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-call-delegate": "7.0.0-beta.40", "@babel/helper-get-function-arity": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.40_1518453775064_0.6769588040795766", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9a46ae4fe623b394a81dfddf465dc723730e2a46", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.41.tgz", "fileCount": 5, "integrity": "sha512-9D57M6mIe4z0EMVmQHDpLxywf20bgHgqJSluqFbcQSZxIn+Cr/uO2hnRLEhMC1OsG7sPl10XAwXz8cqgXFQp1Q==", "signatures": [{"sig": "MEUCIQCZgDjOuPKu6ba8Tp1KVyvNzCjocJ9gP9/w8Qxl8+FuewIgJrePgiRQ9JHBbXJyMhRAgNAplrpQMlmBI87NHSoC8hQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15736}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-call-delegate": "7.0.0-beta.41", "@babel/helper-get-function-arity": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.41_1521044827238_0.8497326963985135", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "58434afb01afb0a3aa82402142807fb70eb3fb56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.42.tgz", "fileCount": 5, "integrity": "sha512-z2iXwael/zMHkOvvkJSirg1jIxGA00JOIwdERB+x+VGxLfLb+1IdyiytVw9+w5RTNSVAGYt6R4jhvUdAeQwMiQ==", "signatures": [{"sig": "MEYCIQCLT5Ri0vYhL33Z2XuJFX2qdLYVSj+a1pXFYtjcgZ/9TgIhAJIYwwXkL2dsXF0eK91WMzIpL3DZxjsiLdy0/s5NIpOL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15736}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-call-delegate": "7.0.0-beta.42", "@babel/helper-get-function-arity": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.42_1521147135745_0.44655654709592385", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "02822d1030daa7f555a2a02fcd7ca460a8bf3828", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.43.tgz", "fileCount": 5, "integrity": "sha512-Iq/G0GQjXaIFFynZV35ThopUsAquXN2FyWwUJrLreQQlbsSGPfp1qtQkfdllmjKONz7CbmZ2gIh/b16srwKqGA==", "signatures": [{"sig": "MEUCIQDt42nfblqpr7Yl4afb286WAxImB/pGb7rFCw/golwH/QIgNnSfbq5c2jgpWAzEs5twkq2r04ROYyWSzas5vjOn8D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15968}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-call-delegate": "7.0.0-beta.43", "@babel/helper-get-function-arity": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.43_1522687744827_0.4896921637990468", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "19eaf0b852d58168097435e33e754a00c3507fb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.44.tgz", "fileCount": 5, "integrity": "sha512-TDEBU2tSAKC0HESeVPwVY6Wlcwgpml5ZymSqxqY0Gr7ei7PTON2O7zmntfmyiigN3BoHxuXnqFqwaz//3KxtTA==", "signatures": [{"sig": "MEUCIDqnKLz0BxKnvxIHmV+m8NoMJ4FANpm7qfoNUrp2cpAvAiEAw32oTciQA86YxGv5kQrLbARylLcwGVveVJTLhaiHEZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16351}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-call-delegate": "7.0.0-beta.44", "@babel/helper-get-function-arity": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.44_1522707644488_0.25225112991666454", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cdd15f00f8394231f565d6ede3eccb98815bd0f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.45.tgz", "fileCount": 5, "integrity": "sha512-sqXR9ODJquT0vzdT70m1nJ9eFfA29cfnUDsl2xISRaURrp3gHiD5KimiE5M6bRJijZc6lWieJr63PxOKBAD26A==", "signatures": [{"sig": "MEYCIQCZen8KRQ97gao5avOOqfp4PQqV5e55SC8dZ4QsW5VvVQIhAOS6USZ/HYs+DQx6jUi6r1QtdDQz6zrcXPzOqh8Sqy/D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3iCRA9TVsSAnZWagAA1fIQAJUtCzXjDwzIprhmrBhg\ndFnjpUQIREhiPKAFLkjnpIDF7rHCzgTj0ad3RPQyUQkD/7KtgEupS4uVFvLA\nudSrCib/zYgIvUmP+zJGRYuMLkRmqaLnCYgzZeyuVu7aLzVkJ4MRC7RMynRR\ncdxaUfeJBaHLsdugs1/Tvh01JYEEg4v4klKYKM+kmylgoCSq3+i5yYM1BZHE\nZo0w21vp5Jtp1xbxDU3SLKiYdg9ZL9nUzVcs8S77/cGtccCljOBYWIXUwSBx\n517Xda/dBEhojX/3Pi/+WqXvZVdTzR+EPJwBvWBKxsO6LaUzmUduOOepNueB\nFgJxDqwEb4UeOg7dShMF+zoRRKRC4nSzFxhf0r1qohktkryiXzUI+5cYobFI\n9GbsXsM4hWpxoht0Y8etpdZCPzqUPfyK9jzn0Gb/m1jwpXATKi3TmTckz09p\nyM/PLTybtesfymJhSdma8kZc1u3WY0G8hEo+Zx3EoH5roqE1VgM1Ry26Ubd4\n6S6ahlX+1FDX0utGDUcq0HxDaOjERwDtOalTH+z3q06fN76pQb0U74lDewlB\n9bXj/Brd4xPRbaL+AtVlhk/mAL+VGl/CMTJ3ah3hCG2TyNHDsG7bOtgM3TIp\nQo4KqbkGFgY1058K15Yu7ZdGTdaZ39JxxlXUILKxVk9GYAFsWtVPAHFSdmsc\n6+j3\r\n=e7u0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-call-delegate": "7.0.0-beta.45", "@babel/helper-get-function-arity": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.45_1524448737694_0.6995603783551734", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "33bbd2e3bd499d99016034dcaf8c6b72c2a69ec3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.46.tgz", "fileCount": 5, "integrity": "sha512-RnkdYrayTlQ0VFoyIjvY/cCp/1lJJkYE2lFcRNg6+Skd3g41PnocsHhQ5NUQjMNogL+RnNan3S/2S/i7S4zm+Q==", "signatures": [{"sig": "MEYCIQDcS0I42FoMUnqyM/hCGd+UDTQ8W9DtHseFhJ8GfktOyQIhALR9qK8n8kHyOSxLvIvl0n1i+hRS/jrEI3yWv/5vMU91", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WIJCRA9TVsSAnZWagAA2PEP/0a+cUqUlgzmt011g0Cz\nQOZmQhQw1WqxD68YKtxH+T0Q01Fyw+qjdKznaV6NgiZbUGIQ3WuIPoHlMJkW\nwOuWXrSpdGXo3YltGF36QUOEtKKIOmBi3H4PyEvWP2J0Jo1OU+DdoBun/hDb\nr+tW+Vlw24Nbwiscor+WiOz6/NiidquG+/qqSR5gemGzkhP4ETZLTs5a1idk\nQ6bXCjGtiFXv+Qcho2I45qHN2XsStbDxs/T+NejrUZV/bLB5243agBYMNnI/\n9U7dYuDCg1OQ3sq05SzcCIYVEaF7RybhTZMbL1zUba2xoRcX4p5CBaS1MrX4\nnXfa9f/2X7MNaRjhLccd18wbavfP/Cz63SLO8TIXXAZVdVdFjy3n1BQLZ4Mr\nDLB0gnissGKc68XoGZqFl+R7gB15EFvRMFP85z28TMGV4ftT8a/tQbppkLCm\nrHTG8NTbtotRB/QaBKawUY00eIeW0VEqAzgynYSYOEYiMxjVDOLQ+HWfuVZl\ng/RfiPor5nAvKN4S1narEJaxYJixigVgx5wPalYs++fw/B+dtwYv7Ash1W7Y\nwXr3rYawEWDmuNXfBCwFp7fAhsf2f+Zuz8g/lJW2FCgQO3jBoQ0sGwrJqhle\ni3M5M+V1WlvF9JTxnrwKnN2SGeaIcJZvWJEx+nA42BXjo4QN0ClC62tf1/6V\nJ3HJ\r\n=oUqQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-call-delegate": "7.0.0-beta.46", "@babel/helper-get-function-arity": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.46_1524457992927_0.9970851145137514", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "46a4236040a6552a5f165fb3ddd60368954b0ddd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.47.tgz", "fileCount": 5, "integrity": "sha512-UzQG8draO+30Y8eNEREuGBfmEHLL7WFxOjmTBbaTrbdOrm/znCUThqcuNz8cyn2nrZbln7M/loQ3stjf9Pt9fQ==", "signatures": [{"sig": "MEUCIDoyJxb9mGuJlygXlruJgwlCAm/lf7s+TyjCdkJ7hlYhAiEA19QKjz4UMJ/uOvIkJ+FRfqCMj3xKc3gNPm867TuURb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+idGCRA9TVsSAnZWagAAqvoQAI8/xEGCObgyr4HSS8Xd\n9+H8IpS1f/jUYaUvN/vVtr+QMtAkyvwk6E2cvIwlc6aLhYUwaxAPX3om5Y2f\nI9s1A1YbFNR8RSnufL+dAcuoO2UaF6A4/wgWW/3A6f1kOCQRZgLY0fpOBfP/\nwQfbWY9Qyv/pGa0d1VpRS/tQhNVh7whSfGWQREdYJBRWYejkj9UQTgEkEZyD\nZ92iE52aDi7RvM0LAeGdSSrj6CqFasKYitAU4O8+11X3C1V+0L4EfBIumFvP\nyZCGBbrqCgDqh9WwowZRgoz0GOMJEYCBCKxnCicKgKlM6dM7eO/TPSX8fU21\ndLw9hP6a7Gg3gxrM25QMsxiz0o9dbNB1oLQUBmL/rg6zFWHSgo5QbGSFEczy\nyn6ilEp1dku/h0m53IPb6IAzSXTDe77LJ135d+FywAaQWYpqFhC0KmqaKLF7\nZ+gv2qiywjOYcrkamLjsPz78mstIOpudVafQj/Yl1ryczhRJhvKYBCFmxlaT\n1f1p8KAjgC3PFL9DZbMv5feWk0pSvjKyPLYEyLN9hPXGemqVCYNvrt3GLtA0\nlWJ3r2vjqe18uwSGT4JuHkRVoOiYPIkjAU/rNWdphm7SS8lUrwZBTM9u0PXO\n5WVCatBTpE/B+o3E+0HufPGKOwzOK7ZJj1aMsk13p4nQcKP8umHM1xMqa6ii\nfg62\r\n=8DmJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-call-delegate": "7.0.0-beta.47", "@babel/helper-get-function-arity": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.47_1526343493626_0.020217244318619487", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3da4ff25135b0c6bdfcfe39c059e0a237b8c2010", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.48.tgz", "fileCount": 5, "integrity": "sha512-sNCDddJmmdXKxweyxm2CcQ1QRLjTeoUyrI5Vmn1X2pepwqKMMEujai0cyruEKAmQqKepAJ0PIHsW16mLcRf5FQ==", "signatures": [{"sig": "MEUCIEkYaYMkY7qO4Yj1LeLgV+8aCJ1R0xs64CV2n8uiCQRrAiEA68ldPjhf0z/QyG5LprUxOmdyhXr+Rr7C+QIP5tJs+Fs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGVCRA9TVsSAnZWagAA1mQQAI9WCkpiRFnZJoL26HFA\n9wYjSS4bT/fezpXkrh9p/o3PsIxVDNDc6d8BPeioa6z0dPrbuL9XDstdrZvP\nc3+bKOAlb7FexU/UbqF/DNVTeoYSQMnAZgDnkWnlCDdk+xSlOtZtFywhY4m0\n9bHctPGMLj+0UIAo20C8d4HeuI76V7EMn87fVSH4v//P5YsbS5l9Q0NkC2Rj\ngiZqCIQh2wHvaKJq0KtcPGpoj65OwpcFhi5xe/m9JM5ApTEPRPYUtdKtIKG7\nEVPBWDieeuepf3yOZ9/XxaQG5dEh/XAFViVJ1NloO/D5RnNVKRk6nXvBSafS\nFgyKSi8R/5yO4qHmVSlv1AZCtjVQDryP7NAgP4vAgK27dNejv4NhLcyZV7dp\nEEIjq1nM1YQv+XaUkclH3AHuQTr4Y/Og1GYpcPSLl3z46vzq1igki48xgHYA\n5WiAEkkNM6xyTsOl4R7fclgkjo3h4xUQP6f4KyqOFy/HS0d9YQIOuXpHUmui\ngyprFqLrPe2UeA0IgubYBjL16i/NOhlHS/Ud12ejnaukcvSbEnVtuA/ojv05\nixIW8O1xEebsn9HGFeh1M4l6476q0+2ej/futGNt+oc43I0emero7Ecpomno\nwvuJ98ZZvSV0igNipSNlMknZnC8pGd3gqIY7Cf1uWuNnhGDdUYeuWiVQbzw4\njmXM\r\n=DE6U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-call-delegate": "7.0.0-beta.48", "@babel/helper-get-function-arity": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.48_1527189908109_0.7667695986118745", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1cad71a2a33281e5efbb1a4623a964c073ce9a2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.49.tgz", "fileCount": 6, "integrity": "sha512-syLHLOB699S4NBgpY8JqaU7xsMMehDkZFfJ3/AC2sn94mg5XlX9BlIqajVm82Yrs9qGa0643to5iGDSqQb/LoA==", "signatures": [{"sig": "MEUCIQDp4b8fr4MGlrGi2dS6sS7JTlbulK11Gq4+FccsuLSbUQIgFMENVXGuRTL86zohcWFBO7fBMsX07EunbkO/q6JF/Hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQvCRA9TVsSAnZWagAAU7EP/0djMK9qLdBBiRvmwGAw\nxXO2Mn0jjsoCVu6lNV4xC7f2KL5owjShjP9TIjTx0UwQPOVRRrLVNy3UgsvK\nrHmHwTbMxb+j06iRaPHkY5huy5VMZUIzH9i3FqU6+NXamxZHi2CDT9LAaB1C\nK6qSSsnAs740QpO8zIDqLwhCfwhN19jy3BA/2J8paB2za0lJ0U0sk3xFBzmd\npTDQRdZnIe/2SEPN5KKSYTCgcQVZ9/4vmL/Trkoetoiw5rJpCLLVmSYwJJhl\nZh/L6n7ok/OWXbKFN9uPF4OwxpGmxMkyQU99voYYbiZtRnLHd81DfHbrkkrZ\njzCMdIT0ILyxsJAKlNHxgY6x84a9JjfmtEhwHjQxQ91Q1hc8v3fC9O4NyQdk\nsYCNutbkC1LPhuGH7RCWR6DYxNaS22Wis+DCNs+pTM+cP94QKOaHB9rCCt1C\nVa0wsyaststSVYqzcw+iLFnhtNuxeaNV4tgj0VmmqPJ/0jIlUwbhX81hwjVy\njqZpmpoIbonm3C5O5QbMhmW2tMnNxTyTqiF+n+QELkbQu3BC7pOkvqQ3L8Ao\nrJJm5Gn6aNP/hiKjq9hPVwn2Ktl9LduQ6gFKwmpl+nGkshF0AehHYa/hZI0k\nOdFWX40B/PuRpFvffbPKJkerE3IWkxppVpJYHYrrYXXAWBJIv9/3NLURcoqN\neTCX\r\n=UCZc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "1cad71a2a33281e5efbb1a4623a964c073ce9a2d", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-call-delegate": "7.0.0-beta.49", "@babel/helper-get-function-arity": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.49_1527264302917_0.8848361271867986", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a92fcdcaf0adf4ecf88a4e36dcf8c35cddad8ba7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.50.tgz", "fileCount": 7, "integrity": "sha512-+dTLrwGUIiM2Y0bLR9fDDz77IlWJTPcre4GFo1BCy9WW0ogj6/MYs4u3HjPpwcmYShmkt4lvKY13yGzmFsLCEQ==", "signatures": [{"sig": "MEYCIQDza4RFxKn3cLF/+ufnyVapF9+kCdqf9iC3U7cyGT4gXAIhAOKWSqyUkQzMhbdOFXqs5bNCIUH5ohN5EFx1hioMcg+m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14314}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-call-delegate": "7.0.0-beta.50", "@babel/helper-get-function-arity": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.50_1528832900014_0.9445582596403181", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "990195b1dfdb1bcc94906f3034951089ed1edd4e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.51.tgz", "fileCount": 7, "integrity": "sha512-jfxpQdnLVOgizoVl6pQxtxU+KDOlgUZ8ka/Q88iVfiQ6YTGuv+2T8qXv4CkT7pcySlXhcebyHgu+JlVbqkL0Qw==", "signatures": [{"sig": "MEQCICegJzptIpIh3dC7tdY74kwj1RY6iWD0Dz7Y/Y+AS0PRAiALBYOgl8cSHrwuwQPv74strf6b8JQMceHHCrGrpNMK1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14328}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-call-delegate": "7.0.0-beta.51", "@babel/helper-get-function-arity": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.51_1528838462419_0.9365747888279474", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "42be565751b1b4ebf861dc6bc8b0aef4fd428608", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.52.tgz", "fileCount": 7, "integrity": "sha512-86axkzNB5O6r9nRkliZNN70fVCe+5Q07ly1ug61N6GMriBVHAFasuGH4EZQgaayEERUNHa9Gpufy9Zh8xrJ4hA==", "signatures": [{"sig": "MEUCIEZrZ7FCAwIdNjpMHZIcKiEEyUN+/i1PZdoylM7JbD7XAiEAiPDSPjq60Zyezg/0f1s09yT5oxZpaPghxPo2irFYs0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14327}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-call-delegate": "7.0.0-beta.52", "@babel/helper-get-function-arity": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.52_1530838792413_0.3700844195973978", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "efe60cec8ceca0d19d5c6fa1ae79bc4e33279d56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.53.tgz", "fileCount": 7, "integrity": "sha512-X9zQrth/hIJ8EQFIF4lc+I16MmgEBoiog0izTC37wwXHEMtC9UWtspfDNVOIwuJ5vqKbQn8arqCUCh63SSG90g==", "signatures": [{"sig": "MEYCIQDgVx8oEgc0736jIGer8PeLQTBVVHSfpimiIeyZ/X9MHQIhAPzXn1H3yiOQICoCcvqtS9Xo6vSmYxzMd7E29Ve63TEQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14327}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-call-delegate": "7.0.0-beta.53", "@babel/helper-get-function-arity": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.53_1531316455726_0.8066538085112525", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "76306f19b9acac6cf13721af15ecb9f382864ff7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.54.tgz", "fileCount": 7, "integrity": "sha512-I+d6qmqx7WvUXTsU/nqY6AX8Y5by1whLTuzweyVemPzM4zbGN+A3+Nge8Qo7OnsNTvEySXSzCrpOdaoOJxLE1g==", "signatures": [{"sig": "MEUCIQDorJyQQxsYzbAM/RGjMUFURngA5slLp97YS0aXCtz0rwIgCqg6Lykan8z1Bm60jxeyBGVsZpS3ZjGbhvsTIltsDR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14327}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-call-delegate": "7.0.0-beta.54", "@babel/helper-get-function-arity": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.54_1531764032729_0.45590568600144654", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f211f18a560a4d928d9649da11c28dd89f15effe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.55.tgz", "fileCount": 7, "integrity": "sha512-7xnolzAaiDfLF1bXN0XRCA/tjp7DG9PST6WDZbevJoAcO/Fu4eIbANv/7tcuMIwLmKqShL4kpjV5EnQSCAPgZw==", "signatures": [{"sig": "MEYCIQDX457bc+M4kbdJyHur7EouAsY/9LzfZjnLgDNwkMGNkwIhAPvYiqFIDkPj465kgke4hFMBKdo0jP21UkcUtLG5CCgT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14327}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-call-delegate": "7.0.0-beta.55", "@babel/helper-get-function-arity": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.55_1532815717275_0.9421580275119414", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9010f8743e8bb213e14c1fe9f801588e509f46b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-beta.56.tgz", "fileCount": 7, "integrity": "sha512-zhGeuH/eY3kDVfFCWpyc1pWoIyuTZghqazsqJkUKwJMHoqVZuwEvNmpPi9Hhbn+W+LOFt8MJv5dY+kgfbMlwAQ==", "signatures": [{"sig": "MEUCIQDhScTniJT/UpcsWT9+w4QJu2EYkmfF1sXhfPt7j5MSRwIgLqRCpPXXi5hS4f92b8IWOQL2qjIkJg29n5hSOJyE2YA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPy/CRA9TVsSAnZWagAANgUP/jt2K+vs2oz6zIqCaMR2\n8wpb1BPamEwxrIbGBYQhZE9b+UCk5uHTkFeQqbhwsPLuNobNInP4KFME0cS4\nSQSKOhBuc2Ilzb/unEhFSmyOGe4RzUsFeIkrMvmS3mtVdUn8X6HZepo82kfh\n+x8+W7Gajvvdsp11B7yPYX4lwvenRkB87atKumlucx4DPoAaqfToobCfQGQs\nW3MACi5JK5Sf8BbRtoRKaPTc1qSMQcHo3skOtxbL6pJVISSA9r9KsGzf1Onq\nUDqnlWqXf3RcEUiux84Q/nELXOH/Y9jANGBkjaUa4QwYV5L754J5E5rZd5u7\nxXTBUA45yv3WOpOGF+TFdzoboCrrS//x8sBuWWzMiKTgbOs/6/zOmc/eyn1I\nHdLR7yb4SQFmLoliE4nEa7An2ighiXq0Qvl7mSdc7IY8N9lXrMKD7bJyjugt\nJIK9h6gfxFwuVw+Gb/t5HZlIteSJCEHYYVGzp9vXscPQ9g/r/za2u4WlJ7AM\n7GFRApi9LoaHREADAWkBGaVnpdokZ1/mzlzoXoJdaSZcWYeMpnEuxNfKMmUz\ntxMntmQoSbJDzhUZLAc7QPCfpTovoShqcXV3G2kHYp59HR9gheb3i5wgUa26\n27kZ0WBqV4IVbiDmLhzlGOtiUXOKyYcd37JGmXbpFqulwt4P3/itxmkkMjn3\nMOK5\r\n=od1K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-call-delegate": "7.0.0-beta.56", "@babel/helper-get-function-arity": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-beta.56_1533344959435_0.016088867055942035", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2da3cbd043551b861543ff035370c9a07573a687", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-rc.0.tgz", "fileCount": 7, "integrity": "sha512-3dP7tat2g9GexS2tMvbPQTRPkzgEb0bjlqRYjg9SCyWlrxvPLtEiDgXo6JzQc73+rBYV1ZhT/KCXolZeKcUMNQ==", "signatures": [{"sig": "MEUCIQDnvMsbH7K2far4MGTB68/cl0EZiddw2KpZNifZRsFSJAIgX1swehwJVELeYD/1rKxoh4EPlTUazGIehwFXk8jgvnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUoCRA9TVsSAnZWagAAGmIP/3TQrUqEL2KQLm4g4lGY\nzZSRmo4wuPTtuZCVdA7jujFLsvE4G7TdEtihB6Xiubdphu2vo9QZhLZAeqvL\nMh/YyU3uR5XBm6bU8MeeZDYVnf+ioOWZ5A7Ase04LvQb5tvSyRN+akso0DNR\nacb8U1pbxM03i/2vTtJJGbQOVh+ND/0u6R3NxfvqmZLFVieZiV1+gLempwI0\nPqPAz+Fqkr+yEBcT89f8bS3TyTrR4vXpKL2KGapgb6YQ4lSWU3d1TGgAakYo\nnqNI6qCRdQJlFDb1W7PnYF0DdIhuPtZ5nr1VicAgf5tERVz0F6hLb/u6Y+iB\nluNQBsdko22h/LUmk8NvG9PSOdeqsaaxuYKZ7KlUN5niHJjuIKttDGbdMl0r\nmw3Cxyrkl7TTM/s9xw3ty0KzY/+0rFETqpj54R681VP1CVHLYO3V5x1Mh35W\n0UxFNFc9ztiZC+9LduozFO0L2bgv0w9loMCxjmAHX5Hbd+tgN0U/GaB8i6On\nllu8m1qcg6Xg3tBW5klk5iB/V83jN5kuRX9TsJ3qR80KApwtRAGoyAd+9ifS\nt28mBk3Z/cI4vRJvBUlymQ6d4cC1W3dxZdvhpDshdrg+zZOFtZeYp2R/DO7o\nOuIQtyrpp4JW7/MvYGQ7FQM+AFcwbRPqnLooIBisKnBIbsa9MOF20ZfewABf\n8fw1\r\n=bGVg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-call-delegate": "7.0.0-rc.0", "@babel/helper-get-function-arity": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-rc.0_1533830439777_0.5462780809506855", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c3f2f1fe179b58c968b3253cb412c8d83a3d5abc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-rc.1.tgz", "fileCount": 7, "integrity": "sha512-PKjm+xf23XvdP0WRj/fIiP3xa5DYOg6qd0150Mpu4JvCIci6vrWvkc+kU9RtwkXLycWRfzdSnnyuSZABxPAP8A==", "signatures": [{"sig": "MEQCIE9NeYkID9KzoJ+pqJMVBtDkXqvMWV/KmEJUWTongSXsAiASrglPvQ/DeW6WybYHgWSZkSkSMLa1rWSTQmWLc5x4kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+lCRA9TVsSAnZWagAAmCMQAIo5zvFKZU9VpemNOsrC\n6iHkOAsjtcnfeUjxuolp24XIpDE80U7HYoSkE42XwLVQbJoVD7j9t5fPvD/K\nSAw/Em7CyI2Q/lcAVsbbzTLmx8TGqO69Kl/uqhQeZOM3FtQSsCon5HjXKARs\nz8ZvvjULDbv1u6sYzNCN+NXjP91zTsNO5NSfwaWrbrrfJ5Rb/JDEisbO+orc\n+kxf+kxWUv2T8NwJUc9XcGI7piNv7LD0OQg9iLPF1rI52B29uEyfR67kjh9E\n/ChUxXVK/DIdPvwha0ACzur2noPG6Q6mITUB0hzsfAS0fO0qEWQOOa4gn30Y\nDe/XRcjBrWdUNc7tHqhTH+TazpbP47HYN2t3GMpy6x6DIGnWJei4REPnZWtS\nOZcPI26CNz7bUam5OSXvH7fMTIoUj9Pi0owzj4n6pShmRYkyWWjhHUlOMi+1\nkTbmGo5dqEDgLWHMVLqRPwIKNwt75rasFLEcBxsGYgY1OJZ+B//wG4G3YxTw\nKx8v4cYM+o+LyzgLjWPNY8y8ecoYGkwQcvsnKsHX1g3yFPjPkpmzDlQ3Neti\nfo2D9kmnwmh4OEje9ChZC6uZ6OL0L+NTyKUtNnHLR/Eq3LxQk19hGXRXAc8I\nABqU4ohJPDOud0m8nZBGE4YKAdR22J45jgGx7Cqq9tbDgfsSYZudJwenyKjo\nfwm9\r\n=1f4o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-call-delegate": "7.0.0-rc.1", "@babel/helper-get-function-arity": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-rc.1_1533845413079_0.8769201752906441", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5f81577721a3ce6ebc0bdc572209f75e3b723e3d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-rc.2.tgz", "fileCount": 7, "integrity": "sha512-jjl/rEU3LAHPWdlNokNex0sOZ6ytEw8+f1tvSZCGuSzmqGZbMreLOLB1HFSKRCsu6zsQNYY/in2h11fgQU40yQ==", "signatures": [{"sig": "MEYCIQCReeK7r+N8KOQPEx0OIWp5MxC/rq/xiC43Jki0NCh3LQIhAKI8yFiuz0ux8xQPf/GF2vXUpFI7JAQIgVGxrLXngxqi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdeCRA9TVsSAnZWagAAvF0P/3/vEcSK89BYGafzIBZr\nzHBd33kfgxg5fQVuK+OlvVzocTUHEfEAqFc5NrFkHsolSdI680kq4Rbzt6wE\nkNDwHaqiLMN886elqkUF6LD1plCRqYtbkpRn7njfWCb+nLDPjjpqyZ5kwDHa\ng5YNDqixcwVacqwnXrjveEEXTLF94q9AF0eiai8iGqRiXLZ7U72Q1g5I9tfO\n1pYvoMRbKvTDzH/2JWN/cAAh3M+ietVAVm4p0HRWArZR6YmAaD0qrOykIyzr\ni32JEoRVsXmHvrp33DMA9Ak+DSeCouuR9dI8A47B/Blwj3BJTA2577FsBwKG\nm+/ui3PjaTXwOrXk4pnk+9vZju+5FAzVv6ie70gmBfC7hiunJziB4S9s7J2H\n9m3c+peSrjRs4AdIazslAHBgi1tye4qnN3em7TofukFlL/NwWL8uK0CILD2c\nuTw6HJJ7YJLz4bnDnMPWa552/oLryfPz7agdlo9GFHYWTbrYXg8N6yUzjzyF\ndT3bx1IdjAzwICrU/4bpHPbKnN9LfUM+CBBrRMtu/cA1J0kru1pfcdkofkz8\n2VEDQj0Q2VaNWKtisNzls0vxLcagEWYxb80dq4S0rkuXaqVxMByU13Kl4nVn\n7NGlMCMy474M/IvK1cjr8lPbo3Mn8uA7KaNCy6uFip/gFFazEhtNx4IpeP2d\nhQdr\r\n=CSo2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-call-delegate": "7.0.0-rc.2", "@babel/helper-get-function-arity": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-rc.2_1534879581478_0.16484001138889304", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a4a6a013c98376f521f231e8710316903e1a00e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-myARJgnDsCf2OsOCemHyObdgkjgq7yDxsI2ecU4ulTevulxwTHYQzghpbqslCkA9va5/cU7vO32sC05cYRyl5g==", "signatures": [{"sig": "MEUCIFIVCZuZbWnoz14c8S69kh8xZY9UaP+KTTfF6BbQe3qDAiEAp8sKgkwnExeo6Zx9VW2YYC2hh761XStZq5CPl/r86m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEn8CRA9TVsSAnZWagAACUgP/04b3XGYicYsizc8Zi4D\n9VKMQ7Szhc9hCHrOwNJuE5xEgg7ch+1tLYEOU9WFnIvJoPB/tKm4JlIK2ZxA\nZvnih0fEyYgKMkWQcDnzrQW3pasdeL+S1ikE/VHuIlEcSsoYtYdbZG0sbFCQ\np+fZe2hLF54Wex6wixhuntKNJokAear71hK84egItBMDJimU8YJOw2ZoACHt\nhlbcbnFTeqc0ewyR66Fr0wj+/8Ie0EvrZbSu8lexLKtCn0qWo87eo4KYUggM\ng3m1XOUD17KgDRbsIGFhXoXVpjZuLeibJ9W5I6UrEcfu8d1lzQb1riKXmYa6\n4qrHwcqOT6sLg3IA4EZbwzhq00uUKy8FI9Y+4kZnGpI4RN9F3/2GGsmS1ku7\nJYRP+WkrCmEZhGDgu684LRaDXf0PLaczuJWi0m4X8m6g04CeLf68J7RxDuWg\nBDDlaLuWQVexyGgkDKNSjcOvKojU4PA8LVQIkSmDVkJNdfPrt7FCRsfatxkw\nZjECqg2HzJknJ47Vt4E60u8WW0w4P8FG7uv6G5yLomV+vFDSKzxQZcNFQv2R\niTiMUEDTnRTKLO+J8mBC4WXksMfx7FZTqU23yFs87tp4iS2e7vtyzjmdkQcV\nfa4VF+TWS1RrfRrOvi1Xua2WDkyxZYZ1PAzsvb4EZ/Pj27RHLiG4Mhy/qbE/\ne7Tk\r\n=7/3B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-call-delegate": "7.0.0-rc.3", "@babel/helper-get-function-arity": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-rc.3_1535134204118_0.37134494753385194", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "444a54d7910917b865a706f687966d762f432e04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-oDFIz6DoWD0MSc4ubBstAdtXVX3w2EswI/j/CunowlxwUrKyzpX6hhk7aGupuAEEiSjdA1G+QIx9ww2HNZou+A==", "signatures": [{"sig": "MEQCIF0FEWPduqnEnUrFtdCo2rUtIgwR5AMA9qOeb5UFPe1KAiBVleEuQ0Vzw267SEka1VaqkMlehmaxy+saiQzHwSD/rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhC+1CRA9TVsSAnZWagAAYSQP/igOLQObNJ5h6E3bOZ8I\nX5tWPG+Dh4zlDsa4fmUc9UJ0xuWFvjFYxdEbhjusLxZLwnXd/CfXdr5zQ4gQ\naLux0E9MINjxnQZBARHc3Qh5/3u0OTel+4qFS5APqw/bj6YUO6NorOvnmuMi\nJstbsk2wesO/H9g4DJ75XtvvOPalrw3ffh0KSz61Lj5ZZec5zSQnU/wcRAVX\n3ParO9Q31zQ6MdM0zDPCNcQxKviZlJ688dIDHvWHNvXMWwCj7WeHkHVw0TYn\nd/2Qgq67B2H/t/Ptui5RGGDi9qZsWwFgixAK0zKqCqbDymmXJ/pXB7wM7Kf2\nRHHEQrlTG+dAXLem7E5vxf4oialmNPPkgO9lZyWmrf/7rcjKU5xrM+4Gagfi\nwHxnyPqDWf3Cwj6mQP8Q8Xk4a6O+QVEOUlgBg5c30jR6xPEU8YKmBdj/wYAq\nGos23LBNQBldDEIGiRudJhVNscuWyf38K56ir6hm0VsgvOAhewMkREIxVyUI\nLGq8dJ7AamkeJapMFz87YBfz4H4vWCzXA4Ao1WKumk+eZOmk+OT/p97YcNt3\nKHsqukgHv1aHX8grKVLJYaz5kqcuQf6l9DPKdNdEw+vm55BJYV6W5qG9DoUM\noHg4YkLT9QMRvNnRoZdSbP51GKqz0iMfOI9e3b3/RXh8pJmGRVrUnGnTn6ae\nqBXU\r\n=ZBbW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-call-delegate": "^7.0.0-rc.4", "@babel/helper-get-function-arity": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0-rc.4_1535389621241_0.12290650531027825", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-parameters", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "da864efa111816a6df161d492f33de10e74b1949", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-eWngvRBWx0gScot0xa340JzrkA+8HGAk1OaCHDfXAjkrTFkp73Lcf+78s7AStSdRML5nzx5aXpnjN1MfrjkBoA==", "signatures": [{"sig": "MEQCIAsMzBFmtzo9wNaBAXAQzbFU4c/Lt6GMyyh0BKj4oDETAiA0HMuZO9oxyBMc9+0RwWKmHuNhblGgCoty5jiD9du7Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDsCRA9TVsSAnZWagAAYz4P/35KY61b9WLg+5GuLZGa\nxXNXgf5/l3UgUr32GQTUXoT8aQ+9/Xib4yiyV60We4iOqE9Q6yaEloq8Jfhh\nhXwYNI9OHGAduL83/ViB5/sjPglywaio84bE7sBMMLcLSqPlUN//U0Xhq+4J\n6cjX+VMXUZuD1mArIkb2of3Y880O+Ksy0LR7kmz930H7ZTVSyxsfAGi4S/tq\nhSHf82Hy010Hy8IT+EXBvdmGJ7O3Qk8ImGCk7pWb9m0f6QmW4xa6bZvtE2Dy\n1mlBIEP/OaxopAVlIjS7qKA5+SpYIsS7yUjyf3QeTv94zG4RWwnKEjKh51VT\nRGbdUI6rt0KHc8zNTrA7FW5TgKDzKB6JDKN6qY4GO0BhL0oJGfeYAXt+k3Bv\nINx88y7E4sHfKbYfsmO5a06/bzOVClCy3KeMC1e1Syedm/u57oaKTM+tWjf6\nzcL++7PAVOAahfQwCcRLVUo4PQQKIiFMUIBRYq+x17ZUOQXbBRVyeb3wH/89\nKgBtdC14PjblPHkRVExx1FaR0/dsnacD6mGM1AwPbaeKAAYptAQoNRlUfLwm\nZvCGUj57WYhiMLlxG89vtjgKShWDFEJyU7vdgfXHvleeot2N67qPPxW1Oicr\nK14HFGajTe1Ps0lmy2q6LrQQA3Q2la2ykoe8SAMvCKGSS+OmVMMEJFqDGZjz\nJ58/\r\n=Wx1D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.0.0", "@babel/helper-get-function-arity": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.0.0_1535406315765_0.06182980130635163", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-parameters", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "44f492f9d618c9124026e62301c296bf606a7aed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.1.0.tgz", "fileCount": 8, "integrity": "sha512-vHV7oxkEJ8IHxTfRr3hNGzV446GAb+0hgbA7o/0Jd76s+YzccdWuTU296FOCOl/xweU4t/Ya4g41yWz80RFCRw==", "signatures": [{"sig": "MEQCIFy2cnUWnwzSedP+0ji7YB24TUCqlcpAqQzbcwU4VG/PAiBSypCyidNYQHBq9gsuQMTA+4yXdys0nTTqM73UbHqGsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAESCRA9TVsSAnZWagAAbi8P/1Eetq4BwLZWCkq6dsr2\nSgUXKCZuSX5v8on8AUKJlbSHL9cINupCA74ERhN5s6dI/NZICdkhyJsAYDLv\ntTHXLhrSgnJDCtbrul7HVuth1CNSoWY8QrN2O5PoqQZzjNhmJXA2UsWLUv90\nfxqpvEp0X1zcdK+j9HdmCFRYpppnUM6udk8Js4qow+eRcEJU7ozolYfg6CVX\nGGwbsqZ1vPCbc8op0JROCybvea61+ovrrYuk6XFBrZXy1UzdJJipxcuUvb0q\nMqHOQgo6o7nL6wn0jAVo+PnKG4rNpZWkfH7+J16XNlCdauPoJvqEs7fVFe0C\nhIOtDIzOJ096f+BwpZ1r9H0RhhN1cetOCxVJIOESHMoVZAA5VVohlXqQJmsS\nSP0g1D/QyCBWGWQRnqU97M4v6K5rtrbBFMOHRmItrGZC2O2eBvkwdEUQKSl/\nNz+C7xtijwmAyL/lv5SG/PSk1CmmHs4q3phHWD9C4ebZLfy76nUJx6ZjGSjl\nXDdQjUX6ypR0+45JAH3hSUrloZVmYxZYBoEvUag7t/sDUuoTIf+WRU5jRyuR\nvzALbYINmvioT/s1H12GEyBgJNSUXF7GLPRuXzIB5zNc1C6HJpwbn4koCvi9\nubeIcPOYdbm+/9ggGM4N5e1AFHfUNKRD1m6+yFVDKzvtmMSWMtNpy1zfqtrG\ntDKX\r\n=GOEA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.1.0", "@babel/helper-get-function-arity": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.1.0_1537212689373_0.20408445970203992", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-parameters", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0d5ad15dc805e2ea866df4dd6682bfe76d1408c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.2.0.tgz", "fileCount": 8, "integrity": "sha512-kB9+hhUidIgUoBQ0MsxMewhzr8i60nMa2KgeJKQWYrqQpqcBYtnpR+JgkadZVZoaEZ/eKu9mclFaVwhRpLNSzA==", "signatures": [{"sig": "MEQCIGOGw+SHltHTtt+Ccg4iaww4zje9Gx8fQLm2sxc6ezwtAiAaJGRij3kldTMkvExuajTLizamETSyVVygCIOXdhUJgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2RCRA9TVsSAnZWagAA25QP/R7Z74VZcGla/eie1uW5\n87l5J+jTWlHb+jj3/VnLsN3r1u3zMAQHru0iqyVhLHrUnJg5eReMwGmkfVIX\nYg1O7JrdlI2X23At2DIQp1ZztcJHaM/+L6pXPpBhh06tJqagDv9fCCCQXTgs\n/qCqy45KqnJem3VIMeRaTlPVF4tCdJ5w8Hen08dSzw3DZP2TP8jlnAiSeG+n\nbIlnEpApv6PejxHeBhkq/3bUSKl7zM6XTKdrfs8fbNbstYBe/HSLjWSouXTy\nv7jl1Al1tD4SPl0JCTO4/uaiyn55AHnSS27AsNzen1R6oTOnDVdK14vk4/Iy\ngBrS67obWdQqQmfpmv+q65AHkdpC2j+/VJ1iZUnQgywDqGuZcCxwKgnmtD4d\nZCxlcc06eTIuky/dDb5rUcpOOzhaN8HrGm7dS+d+0jOBW3LiSN0SxnujWQje\n3Cu+2Hh33W0OT0BWZDesZfB0H0eQj1shyXhvTAvD+5cG3obk+gzSDgAFEiXz\nBRwqKNIkoRrNZ8BhugJig7uEBertIRObSzGG4A6OVQetGjwZRuYVFCwUt1uw\nF3NQE8aSUmoJzj32pEA+dhImhZK9b/jywrHtOoaV4GUtEHcxr410B5tVkNi9\nr/mVl4dHo4Hf6ElfneBHWzPn9L+55booOKCtFibOcyJZVuQzUElnJOrgs1dK\nPmd9\r\n=yiL3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.1.0", "@babel/helper-get-function-arity": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.2.0_1543863696263_0.2307957706656516", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "@babel/plugin-transform-parameters", "version": "7.3.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.3.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3a873e07114e1a5bee17d04815662c8317f10e30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.3.3.tgz", "fileCount": 6, "integrity": "sha512-IrIP25VvXWu/VlBWTpsjGptpomtIkYrN/3aDp4UKm7xK6UxZY88kcJ1UwETbzHAlwN21MnNfwlar0u8y3KpiXw==", "signatures": [{"sig": "MEUCIQDVS/M36NWfGwaxrwENozSuFEsoEjD5YsD2Iecv2Z+RfQIgcL2eOqPdAcrdGI5bx03bc/Eyedr5QPaTgJsYHqYW4Ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyu5CRA9TVsSAnZWagAAuKEP/28lbtX1koBqEbQIGGBK\n+3y3Y9kHfcA7hWlXJtS1/ZH2oxoWxjpz9ZIP+AvzjZTrqL144tAhZxP3Zf/K\nTwlpiboXbuq5K6DSXQSB/aRwCafLFORsx8jsao3FDbVzUzUDghytGY4XCvHi\n8L5aRyUDHTll3h5QHdMMh0GA8z3t9QrlQSg4k0bNbon7ShYxsi7MjnuQzSX4\n2WaAWZAwYwpPgLoQcVFcc8nG7xC67W51P/JVwKmjcQgTuq29y8poMTvA2vYn\ntdS2wAgv/0kUZyGJ/q5GfEAaJ51pIwGqrMnz2f2YvveKtXIx5bUwZJ3xMBHf\n4QHjccsuNAZBB+0hS3ODl990kHwDipvvZn7m2dPvF/3DTeSIWghCMg0n8MUf\nDLIT0zRJrRWGqZI/XQPEKGmUCRcIgQwogmkwSUM5TP48vv9QrUK8jyruIIao\n/lT5wSyOVWVg5m1pS0yzftOtd/VwSr7lIbe9VRz0JJTeYWD3QdUyAAcOYYmG\ny1qaI5RYe5fHaPeA4QwYJ0FeimYk5druzJR69E28DdZ9jeo1+OLfnCNQf4tQ\nOpsNCTAi3kw0hr3p/Jr/MnS34wVqvKCe7BYwV8mKco34KGklPyHsSlGb2Tkn\nIDzcNrOXEzynsv5qNnGOLdeHyr4Osjvc94vgF4lE0Q/WgWXKYy04vGYkl35/\ntsWq\r\n=6hpL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.1.0", "@babel/helper-get-function-arity": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.3.3_1550265272776_0.6154581060635385", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-parameters", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a1309426fac4eecd2a9439a4c8c35124a11a48a9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.4.0.tgz", "fileCount": 6, "integrity": "sha512-Xqv6d1X+doyiuCGDoVJFtlZx0onAX0tnc3dY8w71pv/O0dODAbusVv2Ale3cGOwfiyi895ivOBhYa9DhAM8dUA==", "signatures": [{"sig": "MEUCIHKlxrERyJWLUUrixz9+oP+dsuCsvfgswFw/c5Gi9TkFAiEA2sD2rnCm/gUSGD9qTgCsYAREaiLmawLz3Ls9/+2Fg/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTjCRA9TVsSAnZWagAAr50P/ivspSEu3m4MhcTOFcQG\nsazMatRYyajCOUsv4yH3wmCV/8OL2PUMgv2ni/WY+ZlPpHQcGBWx4OcxxJzx\nrpCY1yKlfbxrlyDwrWUx2shnUuLm0cH6b1u1SdHM2As9U5Kx5VigeZo1QcxE\nO1nvWfE4y8gHgsQUtRZ9Vi/MuW1BCmawa/hiGWjlMnnZNkgnqml0OUd6NMmo\n8gZ8sCRfF1nK66xyjbIlt2plpQM2a+r+ToxNkpiFO0OXjToj22PL4ygBkdBa\ni5J6HtUvMgEGd0zlOjiX3Em0PyjaGGmMgjwZwlUhWXGCG7BeRZoL1Ll3oIK/\n5EryyS0/k+c8wVuOZC4zykfcLbrAKissiaeFR45XEgQhzO0FDMpgGFgQLEVs\nS/rLZTWCrPVL9sClNC2mXTA3VUNw8q3cCeFRqHCtsPYjLYu+R0nvHRaqqXio\nJsPYeKsdxI8rurxlAyOZDhyHLgeYPJZ/I8XyNr55R5UItMMmnTtIVNYSevZu\nlxiEUP0QeAMfI6y/WJeGXXBrRpqg7jeqcXvorYv8fCGv0+YmsU448zD9HXrO\n9kQkngKSnz0Xd+V4kqhUI5QvByh7dl4K8X/1oUYal7Qr61vRZ1z6VcSJ6OvJ\n8gIMfStoSV++oenllix/u5zCsiN3qzq3FHKgYzldjo3wkQQP9bkKeWEuaD5r\nhkLh\r\n=+2vE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.4.0", "@babel/helper-get-function-arity": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.4.0_1553028323171_0.9481968589130281", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-parameters", "version": "7.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e5ff62929fdf4cf93e58badb5e2430303003800d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.4.3.tgz", "fileCount": 6, "integrity": "sha512-ULJYC2Vnw96/zdotCZkMGr2QVfKpIT/4/K+xWWY0MbOJyMZuk660BGkr3bEKWQrrciwz6xpmft39nA4BF7hJuA==", "signatures": [{"sig": "MEQCIF4egt5biEWM3M11v4XlQPOIH3yMVR6Mj0XDB+Lh6NCvAiALP9qnObNaFjQG89XAFBCoj8+kmxb+ZZiXFZ6UTiqBYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75MCRA9TVsSAnZWagAAnawP/RbCUEJ3I/9yZByf1hLD\nqlRaTBN1kSUExIKID9V8hv/OPPixZlHy9lL5XLoAm2P0gir6m8WbY3BErtWG\nX2uOyhhJH4xaIVaemU9tP3vMIIbAl8S526m0taffCItWAWJUiFG5gzXyf2fG\ntPw7S6Zat+GbOcptpMNHX1zG2d3RK0MAoGq0NFfnN7UO2QOaLwr2xz1s2+qA\nTds0cmWKdZ1TA4/JhG/e1K7gqFv4vYyFYaDLAmeN4aDy2K/T31bXVFHo0IkV\neUf/8iOhWc8VYe2jBC9dvT29NsG2kSKhfDzsmR8XPVZ1snlSSD9rjzvmH1EF\nI+YywMD0oaLl0gVMET1/nU3eviWpnZAdojB6Tfqv/KWdaeuK3DLYtD1GydHk\nP7xZn7W2awg+xhcH3vToMtJwhspWrcmpqOocskRmk8BFxZlmwFE+jEgkuCq9\nly6Ww8qenftw3KNA6u2uM5PimYJ/228+NEcWOlEpNfoCKOQyPoIOSERjzh7g\nP5L6NA5td1omnLpJhxnuc741xowSnoqmdR6rSQNplU5HQDyxr67mqgXGAov+\nCVFSaGWgv1LL0qhV4H87X6auJzsiKhobd6SIdt5SE4Ud2CUxFmXzII8kdJ8M\n+msgEluKUt2HhMFnBFCQTfSbkxB5Msb+MKCpA/5+1IFIP/Ctoc02UbGhGKnJ\ntkam\r\n=gZ/+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.4.0", "@babel/helper-get-function-arity": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.4.3_1554234955134_0.567337498956417", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-parameters", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "7556cf03f318bd2719fe4c922d2d808be5571e16", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.4.4.tgz", "fileCount": 6, "integrity": "sha512-oMh5DUO1V63nZcu/ZVLQFqiihBGo4OpxJxR1otF50GMeCLiRx5nUdtokd+u9SuVJrvvuIh9OosRFPP4pIPnwmw==", "signatures": [{"sig": "MEYCIQCgr43Ja6q2UH/lXeC7C4kkm3cPMbjJndbxkutbdKoAygIhANvRBfGFkp+BZDtZI4v2nFZxsMap6x70lvKqOZTmrd97", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3J5CRA9TVsSAnZWagAAC+gP/ia9G8CeyMsHYKDDADcK\nfZP+n/nkcavFggb8APkK3MYyQLM4bpn2nkVJvizL0XkzWIg1HOn7nEFUoGby\njKEwcUUHiNWgGbuC+eslRIPjvzu5CBo6xAv1FkPkM6rwPWxjRCOHPHX/l0oV\n9fbTkZ37a6SF6GwAm9DEn+2rEi+XNUVvxwre9JkSkzhR211T9yNkO3AtLgE8\nJiNUMcr+BSHJlFDPdxhvYkiUlIDoO34YO5Mks5THo4/Cb8kvQN68vZA+FsEr\nOEt8Y/9WrTgu2jVkQbFOhemeWZgWEP7XW5yTeMxOJWu/vKv8eYouoX5lTpty\n1UEK8WwC2V1cWE/U63OeCd0gx+5lSTWctJ0dOpc+wEFeOlu3a1yR7bwNwFnW\n+Zgz9cd+rofIRk4UrOb6CNR63KCrvR8n4FATWKYL7kAsQLMUOB22tgALcNpz\nKpZBJw3tDgmkHxTmZ/0OaPGRyWdYwrfWguTeRBhsolW/w4gTS+XfQHEUx5xU\nBJvg2TMBmfwBcfNmiwl62GcU2UwMsutPnA9lnvy45ybeUuE8c1g2dO08c2bZ\nrO0FV1Yoj/mlw3vvmTiQxYJreUO3yH5Ns8IYtb9HJffEuQh3AdUPUIiehqmu\n4FoyGUsb6uGxS3Cdpndr92VCgPrtoadccr7a7pWTLthElfnnlnMU3ObeYSIG\nn8gU\r\n=aPOz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.4.4", "@babel/helper-get-function-arity": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.4.4_1556312697101_0.8544308174832591", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-parameters", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "da4555c97f39b51ac089d31c7380f03bca4075ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.7.4.tgz", "fileCount": 6, "integrity": "sha512-VJwhVePWPa0DqE9vcfptaJSzNDKrWU/4FbYCjZERtmqEs05g3UMXnYMZoXja7JAJ7Y7sPZipwm/pGApZt7wHlw==", "signatures": [{"sig": "MEUCIQDMnO6cadmkbtTlEQN5KepVlSXs96BwknqkMI46Qds9RgIgMiU4IF+MJQr0Q3o4oZmER2IcG8dQKyo8882A6NCu5vI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBqCRA9TVsSAnZWagAAr9kP/iEYhRHRFnByYwiN67Wa\nPwcyM3xEEV/yTP6OchdNMctqF/VBhv5ToByKBO3K9dI9M/bR+kUsaoyuawDa\nRw+QnGPFo1FNpDe06U9tN3e5Rd438ramls37OVvbvl2G/wEEE/8ueSVSL9tE\nIvMhFwNMH5WDlfx9PLPlLKWEqKNQWIP42YVbKU/zi3iMZka4vSzlnFUXOq/k\n2bhC/O5Da1A1UOF/Ya8dkBfurwcxH8PjTO60gS24zAQTz375QEnmpsGRS4kZ\n5mkBbfuW4rFqr/US7t8BmVhybyZg9Ae85L4ffUOoJMe29NmtWH3p3Qfb9V4n\nmayuEmr5fzo41FGQYLc89T4QrY7DhnlLQYS7b1dsL7OrvcsYqM+QKPVuwMyt\nMI1d+2CUEfkVn/mF+voKHC4oGybHtugv/lP2c2MTvYLn8hNXRGiHQJuchRi8\nenOCleAdzG0qpdqMPyZ+gF4+FIh+JTQ9WiN4bM7NlymOQjpOj6pIkFyrFh2J\nyVQdrFIGV6RxUtERKLiWn6i05pBhDr0lSrTWssLaCQGTHUcpHuik3U2OlWuS\nQ0XQ/ecpLf1sGQ1S+RaZMLouHQ4tKA11Ul9qyPDsNWV0/Q/BQItObllcPCjh\nNCqrKO5Fg/8vaLzv9pmM9JZyUyRpmPlzbVUmuSAdKNQ/GIYuiR+kdNlIgwGf\nNh2u\r\n=ogz5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.7.4", "@babel/helper-get-function-arity": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.7.4_1574465641953_0.7725093727327956", "host": "s3://npm-registry-packages"}}, "7.7.7": {"name": "@babel/plugin-transform-parameters", "version": "7.7.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.7.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "7a884b2460164dc5f194f668332736584c760007", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.7.7.tgz", "fileCount": 6, "integrity": "sha512-OhGSrf9ZBrr1fw84oFXj5hgi8Nmg+E2w5L7NhnG0lPvpDtqd7dbyilM2/vR8CKbJ907RyxPh2kj6sBCSSfI9Ew==", "signatures": [{"sig": "MEQCIFPlTs5w9LLWL28f6sOJ2RcM3RKAVceViI0S6fkurMynAiAW6SNE8KNVkHjX2vj6F+ptFUW7noO3oD2ifIdkiY30Ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+snyCRA9TVsSAnZWagAAjKcP+QHdCXqq8U2zlIU5hqOE\nIluOw+d9OqRkTKB6rv5SaO1sH0DLrsdkZsPd9TnyepEnpSla2tWjjfXx2mwx\nG7xwd2VSClq8i1g+ZinToskWXSDGqOlxktc2VOvM3gzERMmC+ROQUqH34+BU\nB+oRMiBFT4oWDnsu+/v2U/al0JlL8r3b9xF+zN8XTbq/39u57GmnAxGaiAYu\n3jxZUKFTrNkKYM+KtHDw6QR/ZG+YbJbACDz4trl+di5Z8ANaNMarilrvU9JP\nFJCDGlRNA1uzLcINq5JV+6lIb48IjHy1Js6Wir+7hXwhABi0NFLNlKc0+RNj\ne/Cg9/KPsa1P0umJJQrZ/Xw3e1wQHvTrGHL3DJn9djmPiBwrox2YMJw/etmj\nJcmeXXL2TvtEUPyy639eDwXUL812MM0d8UkSsp+0VdMOLNbpyf6ibcE0Jf1A\nN4xA7WHKpyy3EOce53vA00CyblAXH+m1qYrDNccyDenv5EIdw+T2DdfRCpVJ\nxdIM0hDnyc6Ozbxs4O5s7vyVZSOuHg0b7EnG0ebzHkjDWlLv/l3ZbcVSPX8x\nkBy52hZWRbs6tEWQOxfRmyYI00ifGiEsdgzB4Lq84uhyng0hkt5IrMJye5k3\nSjUV0DEi7II7kNK3FN6XJpDOhI3wMF86e4TSAsoCH3kyQQOydub/ZDfi1rD/\nBBLY\r\n=833L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "12da0941c898987ae30045a9da90ed5bf58ecaf9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.4.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-call-delegate": "^7.7.4", "@babel/helper-get-function-arity": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.7", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.7.7_1576716785764_0.7934099130270271", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-parameters", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "edc1531beed51fb8a49e0a3f11ca6b508d083d6f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.8.0.tgz", "fileCount": 6, "integrity": "sha512-9R2yykk7H92rntETO0fq52vJ4OFaTcDA49K9s8bQPyoD4o3/SkWEklukArCsQC6fowEuraPkH/umopr9uO539g==", "signatures": [{"sig": "MEYCIQC446aTbKBHbW2ho066+TilEKMSkzecBHyI10CkdTtJ5gIhAOuQ3mEhycbsKOjGKNP7nodVSpFFTG1Stozq4gv9OTJ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWWCRA9TVsSAnZWagAAhpUP/1cXtjG8hygmDUcyLuhp\nleSQGCODpBaWpVIkB3rJ8PypPWrTxXCbfAYgIQu53swF6PCo3cwaiAX2c1TS\nTeuae5QaiSJpx+FKO+dXaDxe4KsYp1bbr9LtYx4WRBBq0xBLwKWe/28Tlew3\n0IAfdidpT9Fi3eXNmmnpHlF04C14dFe/dtKjXjnukWEobJ1BltOL93bX9LTW\nbqGluByHOQbI9JxGNeE6i+WOoxGWneHNFF130I6VsB5M6beVMrs3X8qeEsTD\nxOF/WclIl7QbVS0MyGPYMvt5ArAjf5scdfMxA9xCzi44HMyymeIWvjL00OWo\nB7s5vLEPpFD5mXdtCr0n/gtWJYkoJW/BOcmEWfQjVfY+Vy/jmwSr2lDqSIHn\niVsw3zxa2CUQxN68Sii3alNuWC/LEw7XvWkmMjN0scfx/dWKVvNPU8emQ9c9\n1eiNO1cJsFo1mqxMqcpx2Onc9g8Y0altuKktZSEtPQXT+hpx/NckQvZJCDI8\nZI8eH1MgRmKkBzQK/KszhJfpRoLJIjswqbEJZiYSbd/eGOL2yHVQKsIGWbJO\nrz+grJ8Q6A/TZchrhb3cMU8Vemw8Au58x7WDJz43cxrUYNr7rhHOGqxbpUlX\nfej7cpTDoBeQ+TeXRZ6lHmzuQ8z3srjvUn74AyzaJwY+UM5HLP1NDH55CrwF\nvn5Q\r\n=Xr9i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-call-delegate": "^7.8.0", "@babel/helper-get-function-arity": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.8.0_1578788245923_0.6183745883262968", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-parameters", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "7890576a13b17325d8b7d44cb37f21dc3bbdda59", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.8.3.tgz", "fileCount": 6, "integrity": "sha512-/pqngtGb54JwMBZ6S/D3XYylQDFtGjWrnoCF4gXZOUpFV/ujbxnoNGNvDGu6doFWRPBveE72qTx/RRU44j5I/Q==", "signatures": [{"sig": "MEYCIQDFtDIQJv01vtSbroBQjxDbt2MvVnYtWfRTqIlK7zIkoQIhAL7pG4ofokKS68s7+rqZkKjtoMaY46/euaj+JroPkfsr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORGCRA9TVsSAnZWagAApAUP/i9p2MvOZzDlxRyB5rDL\nBmrzlfE5FWvrrStiCJSE4+UIEqsS/3deKs/8mDF2pCdDPHZ4RM4E3U0hrnL3\nLLUx4XTLOAUH4wK0cFd/dffnjEnd3dRk6aNFG60z3hWJujcwmFv0lPiaqHRQ\naT03rajDdz+egkBrpSfz7Y9Xeh699D6dqO/XpZ8P1Cat9E7O1Y5Maur7urRe\nMWvUiBQh8nXgJTcvIuGbVj6TJF5R/dvKtt4NpNpursVhoaCRlQueY5ahf7/G\nISzKV4jmb1GpJQEodqVvE6WIcmMekaPrIRFt8o9n+/YcY23EcCIJwArM/OfK\npkf8L6ato4Db5ACfv/XU1Fo/ZyXxsHwtP/82+USMvADW9hQMgSlvzyRCEy4X\nd1Jis/HPg2GAFYt8ftk3nRaWd25XE/35Z765zs0+PIKkrozSZhIbTXBA7j4L\n2mzA5eTTeb73aGvT42Ke92Lp769VLPuPADrt83Nwbwg1X8Ze00Nx9J0JH4Cu\nvkPPT8/YOamVQgOWB0kDFb9DStkTkwpiDbkjhnKfIbCY1mAk3oW/IsSxsOrg\nG8dB7wvA1bdVWXrCxCa5eCKosbT/STb9vEyzQmBs6RFrXa0LndpvxvDDSSEd\nLDqb9wcknERrT9WQ81H/baFbcKEeX4apYEoPHZesocq/RDSVRJibRsFk62nk\nBgEV\r\n=HbGj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-call-delegate": "^7.8.3", "@babel/helper-get-function-arity": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.8.3_1578951750175_0.6539346777018242", "host": "s3://npm-registry-packages"}}, "7.8.4": {"name": "@babel/plugin-transform-parameters", "version": "7.8.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.8.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "1d5155de0b65db0ccf9971165745d3bb990d77d3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.8.4.tgz", "fileCount": 6, "integrity": "sha512-IsS3oTxeTsZlE5KqzTbcC2sV0P9pXdec53SU+Yxv7o/6dvGM5AkTotQKhoSffhNgZ/dftsSiOoxy7evCYJXzVA==", "signatures": [{"sig": "MEQCID8tn2FU+X1RL6C+Fj42K9u6KeO/uMz9ofmOrgC2Lc2rAiAW4UsAjtCK3We2SBG5G+xPUyponXSaJEyb8SmVM6d9mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs3zCRA9TVsSAnZWagAAvxMP/jGD/s8cqX90RkirJbLJ\nVtEb/8nqMrY9grPhCcxB526rsad4hEA4B8tX7zgxw4Lbd5kgc+TvyZTBQvrA\nsipzlN2m+AaXqjGID83zEw/oV+/YYAOQp48Gu0xkmjeJZEbq6rTiMMrTbTDm\nLVNiOmaC2gmLOwMw3eWCt6fx/DFkoTTPbnC0jcmNAvOhzf9HsciG4vPJPj+9\nXTeHMX0YlV45iwbCCftr+X7/fvwHER2PnAyUi6KjEcOzgN67Bf3z78smd+Dn\nQVacbVBJVq/6vmrhK24PXEgY6qN//rKVuIZUoBDczSywWw9z6K4P4HXfauzf\nnHLBb+Dldmb/T6Ym6S75igreD3HarB7KllhOrOrzf+qwfrGn1eSdEus+b6zJ\ncYJJKFBSevkoMKJQkaFvWuM6fAgKKxPOoaG4XrKXBqgsnUO5l9sa7MpeAU0L\nZsNNsf9ltEss0Xdwuw69vIMGa9mqs8+5pZaqIhOJKI3d8Yk8zKfoLMfaYLCW\n+6Uop3ieVf28qWUVyEihVg0t+xZ6OiyZISqivQbt+L7nvXgTEhqzZCMn2Oak\n/u9iBUcQ0H5iF3dL5vXTmCPja16HPIDER0q3Zq2SrkeQfYkuRM1gRbchmjMd\ntsOTDCGh1qj6PllZcVc7PkncaBj4Tp+uwS883H0qReroUK7mVBBYQ6weijTV\n6yXk\r\n=1Glg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-call-delegate": "^7.8.3", "@babel/helper-get-function-arity": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.4", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.8.4_1580387827218_0.4066228480895795", "host": "s3://npm-registry-packages"}}, "7.8.7": {"name": "@babel/plugin-transform-parameters", "version": "7.8.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.8.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "66fa2f1de4129b4e0447509223ac71bda4955395", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.8.7.tgz", "fileCount": 6, "integrity": "sha512-brYWaEPTRimOctz2NDA3jnBbDi7SVN2T4wYuu0aqSzxC3nozFZngGaw29CJ9ZPweB7k+iFmZuoG3IVPIcXmD2g==", "signatures": [{"sig": "MEUCIQDFoZCkJ49uUEbVqETeddXSCCcoC7U9eKtd7ZvmTkGkCAIgTm9pjSAX2nZuhvydUEFayEiw8gVm0ebFj3RqZHBr9xs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFw+CRA9TVsSAnZWagAAXzkP/jstkDWvcfllsBfD1/U3\nlPsP0L8pUNHZuPTVQpLd6Mq/xAmqrg7L14Cxau7dPaFT9Krx8/5QtkVXuQyi\nO+WXEwJO0OBPXMRkcqYFdhDAfUA5z9flR3fs8IJox2Zb/V/Rpa/37qQIwbUj\nehK9usX7JjmHV/8nyL+a4aZabQoY82kZgNMIATzkdrii2yBBUdrZcu+b/7gD\n5Kf1GzSYWBcdvDwHf08Pnk2hJPgIbvhmDq/CmxqGhn1yjpEabufWjHGK8Wwb\n4MlkdsgpFi7iXiK7uSM+QHUomCN8wE1UEFXEce2gZK5YAhEYmcyQKMlf7JiB\nTVg1x8pAh7ZVcaRmjLwD3g5HvIfky02DEH7JqXn/Bobo1+/5Stc/ya4gqsn4\n13nVGg91jFBeDyzwjfbdoTf+pjg9tTp+mMZ44UBRxI231rJV2T99iOKvSNqf\nntVg44EBbzCPPg8x8buBLe0tfW2v0Kj4+kSDgrA0YMw9Mgaf6b46SAJLmnG+\nj94oMeqSyZmcRJGaIZSyxNa0MXxh9JHH9fGKbtpa6ddwwTvmj6Lpm6RGVt8D\nklE1DopKEKgbQTZTdhW2/4DSzAv6Fn+/9S6pdixbl5yY9ZJbcdaHNJ1Fg0Bj\npztBKiAJSOHWXUgF6H99NJl5osCCThDXpisJQdurNwGa9WOIxP6CHqQTlLR+\n/AK9\r\n=NQHG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-call-delegate": "^7.8.7", "@babel/helper-get-function-arity": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.8.7_1583373373848_0.3533284842601978", "host": "s3://npm-registry-packages"}}, "7.8.8": {"name": "@babel/plugin-transform-parameters", "version": "7.8.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.8.8", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0381de466c85d5404565243660c4496459525daf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.8.8.tgz", "fileCount": 6, "integrity": "sha512-hC4Ld/Ulpf1psQciWWwdnUspQoQco2bMzSrwU6TmzRlvoYQe4rQFy9vnCZDTlVeCQj0JPfL+1RX0V8hCJvkgBA==", "signatures": [{"sig": "MEUCIQC6kyw73RfckIC9v8gjHs0UWXONuTgIUx/cf/so96WCqQIgeRDQLwO+Y8VbycZQFRt5gbcVhbA4os4RrQM8Np4zQb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaoQbCRA9TVsSAnZWagAA5qoP/2iajd3W+KveNu/ZmJz5\nmS8yZbi4kF+h1FZXB6+teN638D8dBO73l2aZOHAOG/VZPHJaHDcuA/eixwCS\nV2Y9WXVQKNdIFCkpbBFtjfl7n7jh2NatcU/27mUMPbXVNMQygtxDXRVWaZJ/\nPYFMYClnPdSef4Q0LsIH3hgWHrFnpWQIrjES2ZCsPyTexr9JeJ1UhR191qy2\nKzglb8HE6BOQuVg6VVoJMGF5/ufytGlaqhBgeR9bwLVaEoKVZEtI7CwRrXCS\n0XbhkW/iKK2pAWkNcLLJUCNFRRbK/RuGTy4dQwW5lr7ngRG4h9A3Wc3y0agl\nmQECwSkar80nc5K37aKGBye0BXOTR07iG3t6zK3HkHfLdkEUGZAWmfGkpjFQ\nG6mRp+iU0mAu6TCr9U4+VLa+YTEDpSoWQ8I7M+7z+kKCc3Lbg1P1zxEI3MQg\ncGRFjGHXwvsPg85fGRB5oHZtPbhY7L4zPbGqDYjVQ9rhdad6HgcCqJunnzz/\n5vryeUTMGTMuucIZgcNJQkBxrZyCcL7jxzJR4jXfsMzjAfwgUEOOU1R/T1m8\n/dDvFFyyPEIvIYwP8Vn4eBEzj+AIAxW+x7feyA4kk7SbOJitg/cC/390ITY1\nP5k6ke3Uf33M0zy8gNUDy7ToXH+FYfaA2s2NvQvyle5SP1ZD3oErqHYz+7MC\nIjxn\r\n=29hh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "c831a2450dbf252c75750a455c63e1016c2f2244", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.9.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-call-delegate": "^7.8.7", "@babel/helper-get-function-arity": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.8.8_1584038938675_0.958303042375918", "host": "s3://npm-registry-packages"}}, "7.9.3": {"name": "@babel/plugin-transform-parameters", "version": "7.9.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.9.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3028d0cc20ddc733166c6e9c8534559cee09f54a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.9.3.tgz", "fileCount": 6, "integrity": "sha512-fzrQFQhp7mIhOzmOtPiKffvCYQSK10NR8t6BBz2yPbeUHb9OLW8RZGtgDRBn8z2hGcwvKDL3vC7ojPTLNxmqEg==", "signatures": [{"sig": "MEUCIDAye8+5tr1ku3Q9lqoZ2XlL6O8EhQdC9OWcqDIMNCTCAiEAiYZgse2TAVdJb9b2ROwQsWmAV0x9RJLtKAIO+ld17BU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed0XZCRA9TVsSAnZWagAAN78P/R3Pn1YetLt1DcTokj1Q\nkr4Dfire/kB/4sR2rc6/KKP0yeNAI6QdkA7UFCe2ExGHcL8sWxOz+64TYB0k\nUMPZB6C2bpcXofZYZrJc7TETK3i5jUeH15SWfAl0ucaeA3eR6AfsxVzMlfPr\nIYO374iLiTnAwXe8osuMkqE1eN45x8qNcl/x0KNNC5BwK4TpjWYkmIYjcABv\nrUyecscx5poZeKdtW1m6rfWYTlN5LYLGDNVeNA7vvhKGtyYJD4Rihp3P27Hd\nStjpgL12H2PzM8hiirPlVNGQqSsLB2rAGbT1csSLtJi1Qgm8h9guP/Omqki5\nyIsysKdrbCtwR+/m0BpgOrprhPAg7bXASEedcA8QPJ8ufxrRFc+5Yyi9P0fc\nShqBvUQaM1RJd1yA2GjLpzMvvqvJB35x5KTyPu9+6l8YTffdBxLKVj6muIz9\nkjbLyImz7B7uXthkGw+OoehYXlHCCqx50KgdTzJBzpGCilI76I5j9XYuQGyh\nn7RwbeGLuZA0qOxraiPpTxt83V5352B63BUQCW9m6BnsltGDtsFFRuFt0tlC\nu2gOBF9terZoABZjJHv9LEghuRCM+5mLN9skj9AMpfV2Qj8O8Gy+gUHdOJyF\nBMBLGbS2griEH9vPzraO0T4zygPAUBx2fTOKm92Cc0IHohzHyRu4b9uRA+4a\nDWGs\r\n=E/fp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1ae85560a7e1c24e80aadd04a44e0dfa3e2699fc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-get-function-arity": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.9.3_1584874969030_0.47160905744204173", "host": "s3://npm-registry-packages"}}, "7.9.5": {"name": "@babel/plugin-transform-parameters", "version": "7.9.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.9.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "173b265746f5e15b2afe527eeda65b73623a0795", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.9.5.tgz", "fileCount": 6, "integrity": "sha512-0+1FhHnMfj6lIIhVvS4KGQJeuhe1GI//h5uptK4PvLt+BGBxsoUJbd3/IW002yk//6sZPlFgsG1hY6OHLcy6kA==", "signatures": [{"sig": "MEQCIEl37hnP81sUHAqirIMM5snpvHc6P1HwfkpSDdLE3DAoAiAg3IPeQuT3/QVDY870BD8HXLlfSO5vwbZYmE1tjRUzCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17667, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOaCRA9TVsSAnZWagAA84UP/24KkDwjbbnUrz0tY902\nZLUTrhHIXJ05mR4CFYRNET0n2QpSADeXZmlJttweoftyvJhdulWJwzWe6KLG\nlL8lNDoUno6xur3uTYGDiUNRWKTIiBs/Epco0zlZN9CfbaHL1cQbiUjYb0pU\nEa5J8O/gnGDt16j2yaBAkrhASKV/3O013bN++95s4vcuVaHMwGnbJFRtvOOP\n1suJiOU+xvgpQolNf5mUL4K/jDRItLiZoSbiNfgpZ0oD4fpuvuT8dT6o4AZW\nWKZczwenpvj9vkWbbKR93sxreAHzY2YfYNMD61ikikEYEmPicwKcKpt4U0CU\nu/2bBT8LZtMNMtjis+AmTbd9y80etM3dSHHymafTW0nn1uw1DZCMWdY8kGVV\npJV5kSK/JU/uPxE1DbL+qiNv8fH7PfYgdRX1ARgmP8gy3Dm1+x/hHWXmvH41\nJRRkCSEKapLg5YxI/UkAbNHx0LST9EK8JIKPqIrXjPCLgnHGIJMZh2dOBUCz\n/nP0XZW0h4m7Yxq4GJiGzZcrREkYIWRPmapI/dl64U5bBwpC1c+zgeR0recz\nX8fJE3z8yRf0JrgVQ+qGlm4+kbcqtp2CcN+aFgV4hD0WP44Fie/eDnMeCEvR\nhYih4xTBv83TAtk+utO26+xBm1fq43w/rq4Y0WY1zI/8d5aD6eQyz/LMkr7l\nLQER\r\n=NG3s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v10.19.0+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-get-function-arity": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.9.5_1586287513451_0.5146808027577858", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-parameters", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b25938a3c5fae0354144a720b07b32766f683ddd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.10.1.tgz", "fileCount": 6, "integrity": "sha512-tJ1T0n6g4dXMsL45YsSzzSDZCxiHXAQp/qHrucOq5gEHncTA3xDxnd5+sZcoQp+N1ZbieAaB8r/VUCG0gqseOg==", "signatures": [{"sig": "MEUCIQDym2+Al7BJV3k183bULdJi52VfIU6xg4XLXbm0SxcldAIgMxLfO5uGSICYizble1FHQQvpSw1S4oFZwhNr6qEYiGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17720, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS7CRA9TVsSAnZWagAAHjcQAIP+whZdXXB/Y4pECVwG\nefIpM1bxgPxeINJTqBXNSOWRBxRXENPKPtq3S/LPQSbPKhEDr/3O54EvjyOK\nOmyc5A9vFx1WuM5568qL2ezP+sL5XpnurtTx8I31c6emEnf4+FQRyy7W03sW\nlRrIiM2MTgGFHpR9Sr+7jDfN29g2zHXfySXbE69v/anQVBEr5ngD77CerTom\nwLSLh890zEpzp2MEl1SxOcWlP2dQDViEYEzbhEM4Fa7g2FxWG9YsEXC3hT+E\nXVK8G55lrKPW24g+lRmuHuOMcE1OJtIKZRHpzm60RZoqfoSsZ17otf/Nb7uL\nN2wh3q/WGAwnZBoxoPSR9m/WpRgvLkzz5ajkMFqPG1EC1OhQz6OSrj1nxAt7\n4G/NN0etEme9kjrzNn4f17Ht0vPnznKAMoAHU1Vg/QVIqldDlXBO7XwGsvtR\nTwXv4I4YU5bg5b3sOTNaT9J6TCEoQenI8ZWAldxLjbPViFnUtxIx/HC3dXiF\nyi3eHymbHQ/5x+77elKYMIy+beT86LsdiJ2uCZzWNRcPgDY/AwDiE9Kf1e6A\nlHO/zBelG0Wp5NfyYlKG52y1luyjo5Lzf2+ky/VpHnf0rNutv71zeXsnXCna\nTbU8ArBWLylC6vlYWz3d7zn+fGb1HpLbMfhMj1q2st31pMY9KnROoCcCmRSl\nDwbE\r\n=GsH1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-get-function-arity": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.10.1_1590617275035_0.04897174310096464", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-parameters", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "7b4d137c87ea7adc2a0f3ebf53266871daa6fced", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.10.4.tgz", "fileCount": 6, "integrity": "sha512-RurVtZ/D5nYfEg0iVERXYKEgDFeesHrHfx8RT05Sq57ucj2eOYAP6eu5fynL4Adju4I/mP/I6SO0DqNWAXjfLQ==", "signatures": [{"sig": "MEYCIQC1tbPGRkYCUVCoOXT+GNdJSIwX7zhjqmoiv2fENw/dWwIhAOXHKrMZxfAqaqCytrwyY2DOtwhJU9P/Y4elfXYOaEhI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpNCRA9TVsSAnZWagAAuVAP/0fmUMZ1Lp4sHLsjzAc0\nfqoAfK5JUVSpiJrlkhPEt1bUMCA3RbEyHvaNN8Xc2RzoBHdmTuvxa8I4Br4D\neFa9wO/FymgmVjRL1C4TSikbJy9upLQhc1W3D1ayqfa1U8ExVfnuEGAZF4py\nIt+Tv5P+w752g1DuEKK4aQXYwyqbnFGUBbpDIObqHGHe6LqOd9OT3krMmfw1\nKUQEovbrlTcErlYHBD10auDWv64YH6s1Esk7fXGTQXuj+dNiVZ57m/emEaXI\nwXWpn1YasOQ4I0Zc1EHLGdqot0stI/YvOq/9dHauOd8xzSDnPWmN082K2sGr\nDbizDKCtLr+LXhxgCcVha9ca9ogTFHwCMBmVyCjK6AMFFbOQgNa9wyYufYkh\n7H+FJXQ+N9ewt138EIfTHr0Hu3JCwNk218ieTlRLEJTdyUx4SyXDy2V3HWwS\nmMIEtDI3ykeLS7hnA981aSLIqLrto8sMyrXBw66b+eIArTHonZxaenYhRXte\ntZLkkNdJPmUebSKvIwdYvuBAJjKchP1jdEcT9jyliUG5oqsU/DkGF3Dl228l\nlQA3ytIbFeLgz3j6nhwq1CUQWoPGmcO3ruiitR4t8OqUDYEEPi6sof8hs7hK\nANaqhWgNjtkIYPTmeX6VEjRO2MkP/ZNFHB4pBJBkjH78Mbi1vwR7PqYjDAPm\nozB7\r\n=HwC5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-get-function-arity": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.10.4_1593522764903_0.1922677781118678", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/plugin-transform-parameters", "version": "7.10.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "59d339d58d0b1950435f4043e74e2510005e2c4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.10.5.tgz", "fileCount": 6, "integrity": "sha512-xPHwUj5RdFV8l1wuYiu5S9fqWGM2DrYc24TMvUiRrPVm+SM3XeqU9BcokQX/kEUe+p2RBwy+yoiR1w/Blq6ubw==", "signatures": [{"sig": "MEQCIC6nSTjKueGNy2tRxmWuhWxGI1vL0P70qDujUjo4pDtWAiBgimt5xTOHVBBVfRbLjIoIG/bVo3aUfzKwEG9JMNOAfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbMCRA9TVsSAnZWagAAIQgP/j7ULl6tjUtcYKURvmQq\nMXX/ZNZxw42mmVgil+ZJNcf+46kOBKAg4e0FD+i+6F6PSgKKQ3OHLoZFeTWD\nEkAsDa1wqurqgfA5iyXdEeqt8e0WeOtxX3dLNR6A30L967FRrbWYluvFHKxq\npm66vxXOyZsBPhB3ER+FivH5EvvEqNXsfiXEzQwTj69ADjCH7ujNHP8m+8Bf\nZ3s4JWiJF5J0TLRU8DZlQD1vvEZdrayCVchr5v3hGb+caPCuVqAcscDhQsrf\ndpykkN/J5CGn9XSg/mbgnPnvW5aFphIXM+9adRKyZwTmwmceY1RNGYLwtziP\nYzD/vi6x3Qwg0CZsFaDVdtAwPtvWYTG86hXic83MUNLANd/3G69dxSUBz34X\nnFozFQaFDIqmM2gpuGHMCg3x+woKvVsCwFoMVvsJweCNikYS7+vIBhsEcSJf\nofcfVWFqQ/QwQI+tfUHKqeQfVLcY2qEI51Qw5Kuh38nElYKK3ntlhRP3jp8N\nMnCmzZM87rbxJfkY2bRdYRSBy1cc6nK5vx6WkrAoMP7cg8foNzFJjtbrkljM\nGqCvGAkm2merlAO5fDVD8YzAxmYEjfyEt7YG/eyN+V9YLC612ItQdLfyLjMc\n32bFdqv6zldPAlUildvj8n5e0Y4ulq9fOuOehjcE5GRqsPq+4xZj4XGTnP9S\nfZjo\r\n=Y3Dd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-get-function-arity": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.10.5_1594750668117_0.8259383347673961", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-parameters", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "d2e963b038771650c922eff593799c96d853255d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-xq9C5EQhdPK23ZeCdMxl8bbRnAgHFrw5EOC3KJUsSylZqdkCaFEXxGSBuTSObOpiiHHNyb82es8M1QYgfQGfNg==", "signatures": [{"sig": "MEYCIQCPFCrIrmUtkvLCFxNe8f64PI9n/OJryBi5czPfQnzOQgIhAJ/1j+DnSCur0sxh356xW9XY7V1hcWCeI6vao31JesmR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/QCRA9TVsSAnZWagAAufoP/iE/SVMYF4UvJFLTCa7G\n7FEua0Vw2yxU9134IzQYRpsP/24D8gn+AUfRx4YxMQ7fQNnVNkZu6M+onFhU\nN3QGWVSIc4Er6DIFzYLz6JXWPjHoPonK5kjez80AqL+GTHgORUP97UPn/oKX\nbmC3kwrBSH2pZiyrWfBrt32JDfKCTHATDtQSiNaCNy2DgC3UdOc1xoFr2KN1\na1AfQZ1uw36ev6VSOZDQVp6o1Oa/qw44vKWopmtz+cuVyuimI89eT6nQUmw9\nNOaoOpN88DQDbhngXdMlh5bg4sc6JFeoaN90Jy3QMVgFvSkYAGcXecYTMoaC\nUdTYUBzC50RvNPBJOoy91G97NjrugdMvR/StyMSBPhz4HC8J6VxNofgDS6HR\nAQx2LynODW2535pVaoRDIBsaA22l3YaJBcifnsNb6LJzWcV5ew6CXeXh7jsc\nRGKnw7IjO1pG6H+e1DLuNxaj2vrJIC0Zs2fN0D5HW/Xlc5UuezpUviYs6NZW\nJ5oR/UdEQ/xeqZZEmbdXcYwUJc876NoEq36NORVUQt7hkYkYajRuqg8mlrA8\npRi56V/WM7L5bJS2KQ6i4rWqy6VjXlVX9tXcuOQpRX5LMg/cVEU8I11vHAZn\nSDa0YH6jN74FDF24mbPBvinqWLMpE9imv/G24Ori7cCxLBy/yEmxAUlwxJnB\nSAue\r\n=pFpc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.12.1_1602801615967_0.8205285589310984", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-parameters", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "461e76dfb63c2dfd327b8a008a9e802818ce9853", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.12.13.tgz", "fileCount": 6, "integrity": "sha512-e7QqwZalNiBRHCpJg/P8s/VJeSRYgmtWySs1JwvfwPqhBbiWfOcHDKdeAi6oAyIimoKWBlwc8oTgbZHdhCoVZA==", "signatures": [{"sig": "MEUCIBCApwi3HPmE8nh/DDKIYTwxA789dE++b2OSrhqAXogpAiEAgxjFaHYvkxnyDNMM8upqv0g05EywpooekdEe4S7CVTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgjCRA9TVsSAnZWagAArz0P/A4CoAwhYWls8d6JJsxa\n1tLKINj7ixo4RjVsVIFLsLlpfc44e2NwF81idUuHjtXMLkyYlSkNwhNqJ495\nYOgnHDqSviAujNuZX7bI/HklUEYb346tZME7xVknaN4VLm13giAZC1gASylQ\nVfmS9GWr5TcbgpIB0r0JSRkx6yvWYBixandShdxNy2ZnUpMhp2Shhw8dpFJQ\n6heDaf5CIwA5qNCVurjE7dhHzo0eLj6BXQl3LKp14lJSCLbp4Euw2CBftyti\ngSQ/ifCiI+aFqwKofzKtpEB5nlJQdVQ8iFUtjQo4HUtQ9LfxonMca/Og1t3S\n0NkHpxvmBOAv9eGb24qsmCZ/hN8Af1WhnU/EbwYC22Bf08D6v3GxbOwXs91Y\n31QtcVNHbv8Ftf+VTkl4nTljfqOukAStnFpLymfV4pRPEW4XZ+9DU2DgvUtf\nqb2JmB2aAaOgYbhCagAChjUOy8ey+knfIunfBcmuHPPnoi/dpz1NxD3s1Mu5\neb/SP8LuWECZFJd4RP1ywgYrD5UE8/3cBEZkBsNU4vzH4DBb6OSU50v7U5yM\nzX54LfuKytXFkBUwr01LayMJHf549V/+D8OboWoafUJxQ2nyTAdNHfeQF7zE\nX/7cABDg4PtsQoGrfWEuP7Jf2cHQCa3rp/FOBgdsqUtJGNbZ+QPPZcBHlguP\nvQyv\r\n=KS6i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.12.13_1612314659039_0.2999376497160058", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-parameters", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "8fa7603e3097f9c0b7ca1a4821bc2fb52e9e5007", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.13.0.tgz", "fileCount": 6, "integrity": "sha512-Jt8k/h/mIwE2JFEOb3lURoY5C85ETcYPnbuAJ96zRBzh1XHtQZfs62ChZ6EP22QlC8c7Xqr9q+e1SU5qttwwjw==", "signatures": [{"sig": "MEQCICY6UXeh98BSioF9E5uGy7aN0QWq90qv7e454KolBH17AiAibXfDA0+WWG22pJeXePX1c6w7gPIoxqvxLFhhepo6yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUXCRA9TVsSAnZWagAABwsP/RgO6N3P3QQNTkDYF8XY\nQSezMCAB2bsxzrQC3qd/Izdleu7UQMGAlTKq4SHDAgu1uVNzzCJF+soeLGb4\nU1b0EcLpp+sJHaNqnOfLH3/ZXIuwOCtDWwdxojt+MhU4sWvUduwGwXnABR2V\njd/LwxOBpL848LdMVEnI+N8RELWsoUrnJg3+oqLnOEEwxrQyQwfcg24wYyCx\nqtKqy+A3jjwaAceiAIzVGRe0z4XTUkJBXLdUxcUYpjFttXKmh1NZgZ7VXRQw\n8EBC23VZ0GSiTrUuu6MdFbRoHdNS3MShHTYDnfVaorj7RmA+ybCbrPM1+jeH\nHzccst9QPEuIiw/3Eq8VKYwmxvJfPqniMA5Iwlx/rRqq1y7ZGAsdXa71sY0o\nG8gM4p6Ps4CD2Zm3f0Pva2i+nG/e7qYkD47BRJSIWB5zjiUc9AefnPYTSSJH\ngWfdPMCrS0fInRfMtC4PgDYIkbPu3bLPJCKJDNRykFRrtLZpfhaRUZrIgHjn\nTWpQGRIEvJi9ed2Y9nEpBqRw7cikfTmZj3Qdx1FUb9b/KsNivRabgrYUWB0O\n6A7nEWBYRs4nIG94EfCL6cqYWWqMQV4DFN4EDLULO1lay66GemutwnJkOvrR\ngIoRVxSjw8lTy6U4rFLXowswFxMGMq0jkl42h0rIGQsa3nDfBQ7D+8iD4hZb\nRjfX\r\n=T6Tp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.13.0_1614034198688_0.3863947555972591", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/plugin-transform-parameters", "version": "7.14.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "e4290f72e0e9e831000d066427c4667098decc31", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.14.2.tgz", "fileCount": 6, "integrity": "sha512-NxoVmA3APNCC1JdMXkdYXuQS+EMdqy0vIwyDHeKHiJKRxmp1qGSdb0JLEIoPRhkx6H/8Qi3RJ3uqOCYw8giy9A==", "signatures": [{"sig": "MEQCIF18ifL4Ks2iWQd6dkl8vZ2gc4UbKuKN4poWRcjoO9DuAiASdH75HbkBALGWgcP2ym79lZCcQyzGwfj8wwLUZiN6gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvFCRA9TVsSAnZWagAAItMP/ip7is0XZLAWDw8rnsSS\nsz2AiakjYPnLTPVpYDZMe0emVj8Agsw+wlSb3An9NtFEwoPON9W4Gx4ciE/6\n+xbEm/hQxSLmneek076pryQ39yUACS4lAwBXxJwg5IwP98D+F47J/+5NOoKz\n12k8up6JEEY0xgevoLa6HzcjYRJ3mowuXnaEXRa82pvIOUVDypuKOFuMjvTl\np6fbzkQbpqU52q7hvgE6WoUceh8ACWyI98qoktULmbu0fUKM7AMGR0BOBOY7\neiDg5YbIEy5VO/+HW0+6UZAt72E0vxAtEE+LqEAswV15Zy1tOktCv+tUgctY\n7Ib2fyogDvexy1aKXIrwu7JFPQyCuezr2cHrj8qseK6loJNQXZKO66lNbKjg\n6hp/cBl1HO4++lVOJ9bVIwaVfYg53BFpSU2wSasbK+zgdRV4xRb1KdwuthQu\nnBCiygRxRT2bweGl8nJroVrE2Vf6WQqsDrLFFHZU5AJQ+yc4eJjDGmZO1uK2\nP+45LbZ2AXR7HYPRAzxt+Ov6fkVBHb5uNeJhD5kteHDJQMRUNiuPMtRIVh/U\nr+N57gOL39x+E90mUl/vJQgXgBfcc6KsfBCOi45aurVVq+Mn7UPEQB5/HM2b\nGCFmgt86w++AEdit3XVUKS53TXKGW3ArQejDsK0/ElhEaPKsWgRudUq/DRah\ngO44\r\n=RQhO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.14.2_1620839364637_0.37263732014217865", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-parameters", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "49662e86a1f3ddccac6363a7dfb1ff0a158afeb3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-Tl7LWdr6HUxTmzQtzuU14SqbgrSKmaR77M0OKyq4njZLQTPfOvzblNKyNkGwOfEFCEx7KeYHQHDI0P3F02IVkA==", "signatures": [{"sig": "MEQCIBr9nGKqfI00cwjAjgQgJtNSVu24CMolXlOrZ3fUKCWyAiBHvmmiDS64PCAl7S1fKg/9Omd5DTY82ir6+leDOMcVcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrHCRA9TVsSAnZWagAA25gP+wbQTIwEkwBKS+rXaxUt\n8VDsN2y7Dytj/Jcrm79aE9/gH3s66sewa8bKGUpYsLLn4BN1UnmG4tu1BJXk\n5+wq9TsCxQvGf+qljIQZFyZvYoiJnltc80GHL1Etn+K2Rc2tRkAqAjOYjAPw\nPvn5hTFkh09x5eLBQSmKeQ7BdFFOJJ0RBPo4shsqcJokaMufPSl/JBDbFm2P\nbrDhzz5AM4OJUMVDzIs32nXiw+zIFgLWwuy4ZIXwOncWQxm8wkk3HyJAHsec\nNLfEHzcJfRTb6m3MXKzu5SkBKQTeuMAmegoOjruq3UUz/oL/jD9UBaZUzl/D\n5isVAUXI15GJx1mtaEzBD/x4lky3G6AGvYDhdy14HznMYyv6TvVnW/5X8trZ\nEfDheR/VU0RV6UZqJ8hKOe4mCEUeCVciatma+LTgLSgD2NiYQ0VsxID0uU5V\nPAgNwYoQbMDbbo4xZKJli9sYBADJtozYvLjy4cjlkLhSJpCSB1byD2HRvO3l\nS95WUqVMST8G/wCbTxrMfVUGqQm8RI2MWlhLXWNjluE2UGVNlF6f5mIazPBa\nc5EsE1HocJ/jHJP9P/XN3eBML9ILu7IAdhKqyt0jw5b3QD706mZ2+/JWH+OC\n/9vdmjThDoNKWIOxuRHCDRSxy7XYfVqyhLD6NNGp5+zGqgeJ3wzPmqb0EhT4\nLYEP\r\n=59YF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.14.5_1623280326928_0.6538985982321444", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/plugin-transform-parameters", "version": "7.15.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "5f2285cc3160bf48c8502432716b48504d29ed62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.15.4.tgz", "fileCount": 6, "integrity": "sha512-9WB/GUTO6lvJU3XQsSr6J/WKvBC2hcs4Pew8YxZagi6GkTdniyqp8On5kqdK8MN0LMeu0mGbhPN+O049NV/9FQ==", "signatures": [{"sig": "MEUCIQCwQux5E4kyZ2ECaeNZjn6NAcd7G6ZOAKh4htG3wwAMkAIgQ/gtIYG7axzTLMelHz374yYHkONE2AZFpiiMJMu3S5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSNCRA9TVsSAnZWagAALq0P/3SA8r+0iXwSMnhPldwT\nqC/Y0Ec5H1Jf48vKZ01edfI2gLEAcsRJ+duR0ZQovZebM8E2UdAvi6y0Fbwj\nlvTuMIrCe6bVyvyz1ia1345LRH9S4HMqd2yqQX+lZZWqRhcyiTDAX2y2TVLl\nzy8IQy3VAr22G/I4lxPpuIiU5gORze9JpMNyurMV5tRfWY70P05V5ifauXK+\nvE5MrJPS1MHlMv8287CGaUPLsaVpZnKbkES7WOzldIuJROClqQRCxmBqtcKo\nq3/V+5DzcaRq0Wcj5aTp9xholw3yMy4PYD0x2NYzi/GG1mQciGwl4XYfHd0s\nG5qtkwAXG8ZkIkRqQIuKmXW9bzNOUXz889yT1bQaqi5VVaVhN4Anjlq0DAAd\nrrlLy9uhAI7pVElCCaK8dk0QOJmxv8/VwcmIz7irmF+Yslgpn6rsrMusge9F\nY6BEIFpQYiplOXIgOg3yN88VVTsyb6+OnI/B/DX8Pvmco7wyc4j1idPsqHjW\nyr8YIwy4LpXkD/5g0Cn97Mpn6+1NN4WbN/JeCFbTpeamcbSitOY+qUfIe47T\nh4FBqgP3ele+kuYBHHxmidhQJiv0525IIwnQ0j6mPRoHAEgWn7kUUvWIBK2b\nB1vX5ZIwcgPyJ8CfZK8xPBGLrdYs3WgpogJdhUDP6zBD3pLnEvEVsSPGGQ9I\n6Wkk\r\n=Qpu7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.15.4_1630618765018_0.9500267103373563", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-parameters", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "1b50765fc421c229819dc4c7cdb8911660b3c2d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.0.tgz", "fileCount": 6, "integrity": "sha512-XgnQEm1CevKROPx+udOi/8f8TiGhrUWiHiaUCIp47tE0tpFDjzXNTZc9E5CmCwxNjXTWEVqvRfWZYOTFvMa/ZQ==", "signatures": [{"sig": "MEQCIHGfOGGtuF0i41Tq5Uz4uns2G3ifVcsJO5oJmyohCAsTAiBSRJ9d0LDe69WeP2vrVI+xN61H5cksUw7xC9r9sVtKjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17874}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.16.0_1635551251389_0.05038906350948946", "host": "s3://npm-registry-packages"}}, "7.16.3": {"name": "@babel/plugin-transform-parameters", "version": "7.16.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.16.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "fa9e4c874ee5223f891ee6fa8d737f4766d31d15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.3.tgz", "fileCount": 6, "integrity": "sha512-3MaDpJrOXT1MZ/WCmkOFo7EtmVVC8H4EUZVrHvFOsmwkk4lOjQj8rzv8JKUZV4YoQKeoIgk07GO+acPU9IMu/w==", "signatures": [{"sig": "MEUCIQDj88OMLdlgWf2DC7Xx8qvwchiCCoNSkwJnL45veDCX1QIgQlbZbVTzuvb0wRigNy1QmUs5nuAwJ2CgD+A8P5VcARU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17926}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.16.3_1636494782838_0.22798113046427892", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-parameters", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "4fc74b18a89638bd90aeec44a11793ecbe031dde", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.5.tgz", "fileCount": 6, "integrity": "sha512-B3O6AL5oPop1jAVg8CV+haeUte9oFuY85zu0jwnRNZZi3tVAbJriu5tag/oaO2kGaQM/7q7aGPBlTI5/sr9enA==", "signatures": [{"sig": "MEUCIQDNmZR4H8QVU1nMbdxI9GEFhwWMAdbB3C+NfYjg6u74HAIgQMMeNI6aDjWwqv7YWZlcZAe3mP+xZAV6ic5YAXPJCUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kFCRA9TVsSAnZWagAA6e8P/jHtnC1zJ3gxyj23/0dx\nKI2PqKVy20p/BGRtk4p7MSA8QsGPXRjXoVRo8293UR2ezALYeBwajT5vTyF3\nZfxEkhRHbtslCr/FBgQ/GOclHFU32LApc9SfQw+2qK2VBjYnHL/X0pt+l+JH\nBmo1lEVjZzIypJkMeZRK5wttDBGJn+doaZ8x+jpzhb4au/TSK3JB8plgXRkJ\nDWi/XTQWm07jB/qX9sKw1f8SpN3+wnM/AaISJcIAg1nPTM5UgNvhwrjF952U\n51IUx7DJ8sbUUgxwKs5BdSZE3l7x1l2U5do9tlmsDNSdJgkuABdHF0ZWaHpl\nPOrrxOq/PdyFdMeexADRU8IHRknZUNqKcQJCspWOMxePVV2JWaXR3zLOkLZi\n31aL/n5gA+7tyXO5qUafliVLGf4DgrXgrUX+j1Np0m16NzSLUfXlLTUgkCVB\n9Cr6cUR68NxlczEZslfvimkY9Q3vLwZOJZ43ZcFQf7T/KD7EHyGpTZ4teOAG\nJuHNpEiNSxWTJZlhyktZqHFHMqYWv0n5LMheBcEALmM93OwAP8kIjrxlB44m\n4ZwRTEu6jY5TUVIZCeOWm1r4n4NnaYI+mpiDdUh9Cy7zOBBSf/CzCZHTALHn\nIpXTh9PiYepTo6jqMTgEtEnvUlXD9I/taGPgPqs2RATFsOXJabFWhf9QyfBn\nwKV9\r\n=V/h9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.16.5_1639434501262_0.5013419182412595", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-parameters", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "a1721f55b99b736511cb7e0152f61f17688f331f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.7.tgz", "fileCount": 6, "integrity": "sha512-AT3MufQ7zZEhU2hwOA11axBnExW0Lszu4RL/tAlUJBuNoRak+wehQW8h6KcXOcgjY42fHtDxswuMhMjFEuv/aw==", "signatures": [{"sig": "MEUCIQDxHFDZR1v8e0MwhoGMh5TC+3Y+wcmWAsIkavXM60doqwIgZj+0x14IvC/keToGfaHCVlJ1V4MKqi/AER7fsVDWrjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0qCRA9TVsSAnZWagAABWQP/1rJ74xBIAC+4P7QzBdU\niq2Xi8VhppEtPwrODRGl30THKEOhK/LsX+AGJnZHjz/uaJDvKeg8FkkwCXe1\nwb50p1w9a9ILrCXykh4lVVQf60ZE+KwEyK33DKDfB3um1yWm8U5JYwyogNqb\n3U5pNAcdT6FwvFaz2czKXZ1WjY+53bkilAU2zMB4e3RCg+pmA+vCUNutwegj\nr55D19mHQHS78dSU1liJGIUS73WIxpfslj3iHYohA8mrvFzyRv7ZxvpFG1Tk\nt4+ub1tua87Q64ImqTdeFzFX6UzqOj6De+d6o3gK01Gq519bg8PSdY29UJM3\n2ZG63d3s31Qla5RKGTyX/KMfjVdF2xacR5/FH43sn1Oun+75E7fYdrZAxUJ5\nyXzThkOwbcPPoDMOYsHB9EQcbtvrTkxU/QRgYvg9yRt1Nv8+RCaIFBPa5Y0R\nBeWrLGoK4pBl8YRF9Y4FeA7hrqc/Nrk5Q+FDBbm3lgrOTu9M1cFYSxHeDoaQ\nDfFVJ522hMQWkA90jDgo7j3xWCFc2EZLOA+EfCdYZiNq5fCGuQAWALyiEaWL\n92OXtL8bTgZ/R8SJAB3quxtzRuUKWkfJjE+vw2piObIuhEr93x3n5QffREED\nRadR5bvQ+SAfz/uEkTzMTyQqdypHeOmCcUlzbhTRqYKrN25jW4vgGttUEXXZ\nONvO\r\n=Boq9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.16.7_1640910122767_0.9498482973344953", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-parameters", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "eb467cd9586ff5ff115a9880d6fdbd4a846b7766", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.17.12.tgz", "fileCount": 6, "integrity": "sha512-6qW4rWo1cyCdq1FkYri7AHpauchbGLXpdwnYsfxFb+KtddHENfsY5JZb35xUwkK5opOLcJ3BNd2l7PhRYGlwIA==", "signatures": [{"sig": "MEUCIC2tjs705Tl7SV3EqgBwnOnPmmlFn94ZeWLi2Z2ImvZMAiEA8TOWSMV3HbyvMuy+o3trTdVqeQ+K2RuF8Yj3ZmudyQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm7A/9GgRkHa45bGUD5d/6oPdSm82uLLnOcXGtTdDfc03HyBcZFlow\r\n9G9O7x5hdvwKE3DH6rSrtcWKRbf4g+K3BfG1hD1c/JdM3BmWx16t1sNUU19s\r\ngZa/97ao0ZfqAYAdi9pNvSL5gLUBMxZtt0MEBR+UtBpPXWo36hpYlfX/WFR+\r\nvI43wU6ZhhVGBO64aA93lx10HCWegIPy9X6C34wDK28sSS8w5W5zOcRbVT4V\r\n23s4Q9pQmxmX4yo4mBKpr6FlZgK6zscqa7P927D2x4GdeHyrcx1UQj1Itarm\r\nL0WrVg5DEpLJdiNCd2BfOwna4aC0pjuntsRJZxxvFUGzg/RyYthThcd2U8v2\r\nr75/+4ki87XfYt1dxQMyk4orJafNnNZO3bOL3hT8JllLs7POKA7J5lusw/hK\r\nGBFes+hpckz+SwH9eUH5Xs4BFTqd4xBi8RLGILkPuFN7KnIFthsj5ry7R9xw\r\n7B1KMfLD1UBckJZdCOXGl8f1u7FlP6q3iBL19TzgBjwUOFOOXd4bPHZ8bgo1\r\nSyGjjfJL6LmqsYA4Lyez88omdlNDOfVGxOmSCyLzhl2uutz9Hu82Qoo9IceH\r\nVQS29Z/b91bC5p+jxybFO359xH4akzH0dXrcJHSrqyifsrzy0BH3rA0mitSn\r\nngIezJQZMbd8ACT2+4y6GyrRGTLH/Ai8ZFw=\r\n=4aUR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.17.12_1652729577172_0.980164119331195", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-parameters", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "cbe03d5a4c6385dd756034ac1baa63c04beab8dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.18.6.tgz", "fileCount": 6, "integrity": "sha512-FjdqgMv37yVl/gwvzkcB+wfjRI8HQmc5EgOG9iGNvUY1ok+TjsoaMP7IqCDZBhkFcM5f3OPVMs6Dmp03C5k4/A==", "signatures": [{"sig": "MEQCIFNPYlykp9NbId0OXsukQtGLKtuA3LAPuHUoOhtRIMa3AiAw9LXeZqpFUk6nR/DSxeYjYjfxDKVGAbd4gtfphbWzGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpneQ/9EhtA+VTClDQVlPwI3XV4aTMYvted1gM/GyKTFFlaWpt/8iW5\r\n3XdcqT8g+TJFbr56nMHCFy0Wv7Lk9DJY0XDE82KmyadDNlcHFbtMQ3UsBaQp\r\nPbosBLqJnnUVUO0TbT2sHAkFPAtyAhst5Chps3NTGBvcPH2cp1ULZz3eVos8\r\npr2yRNZynf0IoGvepClvVmld3cUyspXgD2zm+uVu9ZsFOiEdwUlTobiu060c\r\nLAhHDbvl2TAJUHxC1R/GbcqC3dGKUpeVxhFozG9CXfJYS/fZ6TkWW4hFjp2k\r\nuBtqOUqyu3thROS45d5qzTTCbMQfwT4vX12iGel2lD9uf9tt8JKeCv0kx077\r\nyRfrHltVQkM0DURT6TA3SdnAqZbIqRTvi7QBS46ITXDgXsyZCmBSApx3GZvR\r\nwmJcXUwAL4p/fMHmQSqgOvBS26POYOZui+H6fLaH4axS+9sPwwEnhVmj9guF\r\no0AUuaw2A36X8pNWj9BNv7HaKvDCUhuxepK5XP3dn4SLY3jIf1CNdQfrXUDC\r\npUHKVwhG2Oerx7oHCedA6IF3eQXgUjv4MqMCn2PoCh8j83tJE7XVUgZIzydr\r\n1IDClcX9gTuQH29RMjzO3l7eQDfeb9UJiMxb/q9fZxtOo4ULNYeiRP/tHpY4\r\npRcv5LAsphEl743uSt/y6r9GbcQ1njPvdB4=\r\n=QKL+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.18.6_1656359401466_0.253786314358603", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/plugin-transform-parameters", "version": "7.18.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "ee9f1a0ce6d78af58d0956a9378ea3427cccb48a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.18.8.tgz", "fileCount": 7, "integrity": "sha512-ivfbE3X2Ss+Fj8nnXvKJS6sjRG4gzwPMsP+taZC+ZzEGjAYlvENixmt1sZ5Ca6tWls+BlKSGKPJ6OOXvXCbkFg==", "signatures": [{"sig": "MEUCIHuO/sIul9XY6HvBMiADyoLO3cKbgyBnW2cv0AOgywypAiEAg/9H42qs8JqOZv2JTE51Pv94g8Gj+n9eK1eYnose/ZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/mzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruGA/+K7Po2zGx5F+L4gCxP4CRX08Raf+pvaj4EJ7P0+44XsyNideT\r\n+D7VJA+HzozDcQTRcR6snIolemgk4hFciiDp34jooUml6T5jLYYhb6LHNIVl\r\nIaOtotSpqqZiJehKzfyO4w/XxEVUgOic0aQOD56w+xoqWWrrrirId12GMFc+\r\nN3IpkfViwJsW8YyQ9dyHJm11z2fp37PT/WdDSTt+DiLusfEF5uJO6jOQyOgV\r\n6wRC1b2VLFe3pP+wyaSJ2foDxXosZahs0vLaU5bjQsHoCUvvINY3uQvXoVer\r\nhF5aU960Smryccfkpl/KyDvH3hs/Vh87S8Mjv51jBM4WJ2p/v8B7ChQoBN4/\r\nu+5BYjzO5ncjM0kL/jrs59ruRxufYHaR7lK+rEPrkUxc3PsbBUkfHi3qhp95\r\nK3U/XjysO6Rw8Dsh9cMY+gW4kQ02MOYOiFZ3Vp+r3i9egdm0Q2GREtWnrBmF\r\nZDnC8E8H+L1qPS3kUxDVDPNWOhpuFm3ADnIPWWmhHloNz1YNHA3NRpMCBCmu\r\niPh35CVXvQoN0srPMV8saIhgdLelZRgf74UEyR2DRNnsL5tC5PfFSZ+Ld4Wh\r\nbro+SeJWWg0d0+AsODRxJKFCUNjW7F0we0FpD9wchxcaJSyt99zDhZ2NBZNi\r\nfvvtkDnyKjKfpRk+fTolA48RwjHGKwEMH5U=\r\n=4G2Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.18.8_1657272754949_0.4352967038474649", "host": "s3://npm-registry-packages"}}, "7.20.1": {"name": "@babel/plugin-transform-parameters", "version": "7.20.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.20.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "9a5aa370fdcce36f110455e9369db7afca0f9eeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.1.tgz", "fileCount": 11, "integrity": "sha512-nDvKLrAvl+kf6BOy1UJ3MGwzzfTMgppxwiD2Jb4LO3xjYyZq30oQzDNJbCQpMdG9+j2IXHoiMrw5Cm/L6ZoxXQ==", "signatures": [{"sig": "MEUCIHiCsd3wW8vTvbevxXfsmg0f05igjgA1mjd2T2vZDoWaAiEAlJ77rbNQE+ohOo8ft55OpgDEPfG6DUYvP6c6WLffUbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYQI1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtvQ//dZX2CVUlg1qg0ts02nPtsuyK7fuY+kK1hJlhqkn3WUzNg9M1\r\n2ktuQAA4/xJpA51L3cjJduqwuNYgSEszvGbi5omsFQ3UFomTcJAh1Qt38Rr9\r\nAYpVBd5Ev0IUZV8hDbmTLpRXg9d646DNfTEVrBFMU0hEcVD3TjF1UQRYsdxV\r\n3htaqYRG8VTvg0zG01Zj1HUP2TrSSCzejk6xv0DCHQOOMjeq2kC5gyzP0RIs\r\nfDMOCm+ZYia8gF2rTxyd4fOBp2fPXqTZJykQ3YJFHZTo9CFLzJ/X3KOsxPzh\r\nLPmEQ2haxTwBAKULdcQgarvujfCZBD08VtTUT/IMK5Q7Zige39Hr5jaGLI78\r\nII7VnFuJT+WDj7LqnKoI4emqFIR7N+P9MFrnOW8UEGd1aEBsDxHfjkwdKpxM\r\n7T+taFeI/CrojUE9E7f3yQZvt1wLfeA68XEcv4POKReTc6AXtBpUD1ntn2r0\r\nEvl9CCsE+Lk0Ouy8tHDnfBTWExK1Cw4Qz6YzXwyL+mVIX2rXylQvl4NC1eVs\r\nbPtT2R4Tcdz5d+pEoXhRdV0ouzl3AKv+2FHZMZPnoo5Ixg1NHYB8sNb5ZdNd\r\n+HSJod0vosiOmh+sG0QI/hLWrRSwtNAK0ccOtFa8KQ4W8RuREqJ1UC6LSPuW\r\nL93hXUrZ4/TzxguVyV3Tq/xoPQQ5VymByOk=\r\n=Z+S3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.20.1_1667301941295_0.21637819130283775", "host": "s3://npm-registry-packages"}}, "7.20.3": {"name": "@babel/plugin-transform-parameters", "version": "7.20.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.20.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "7b3468d70c3c5b62e46be0a47b6045d8590fb748", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.3.tgz", "fileCount": 11, "integrity": "sha512-oZg/Fpx0YDrj13KsLyO8I/CX3Zdw7z0O9qOd95SqcoIzuqy/WTGWvePeHAnZCN54SfdyjHcb1S30gc8zlzlHcA==", "signatures": [{"sig": "MEQCIC0uam14PIpHWDT61HsPVKAsFZf/AZfqjtBz23NVn+5cAiA1d4SmwW2pnPGQqFJFw6qUjYxzNpyIccPfSFqyfP1acg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaRRRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZVA/9FpcB0dRoKF9oHYQ0YsPxaL43c+chRdBY+ObcRyoNEwB6Qlaq\r\nnmlp6YFrlisSHR3zutaTJizMaOzVe2wjE/pI9VYR2BFFgidah0hZp9wH2RsI\r\nYESsfjlqgIbi/lhn4lGH5S+tcJcuj3dtvtepfc91WnUUGE6jdj1doph8lod4\r\ny/+UV40EdG9rXh02b2dGP6psv88GCYU6qdlMZGVfaSeDyx1WUiaJBEC+w5wv\r\niq0OOHE7ap++S5po7s5CASKNzihy71f4xGDUhaerCsPFJxroSxdWNCH8zJ0c\r\nrXcq1UW9xqj9JrhQVAwHNhLtv1C/gLGP2wNySd6knqGr+bSAOWBoVGmABtlp\r\nsRsJrhD6gMp305CFR3Z3qUTdZ4qRwUNBNZPr1kL55isqmr2NJrhFtaO1PvBv\r\nn+OmicYp/31UrUD5nQQhhkGgccnr2oO4O9AujEG4IzFe4jMxNC3RF0WoA65o\r\n1f6JDBZvqXkMMeF1G/RaEOUB58UXIzfKvzwjh0a2YrNsbHB1c99kV1hGI1Q+\r\nc7w1xi8i+coI0HdqkZnjZYk/m6uHa7UBo/tBLW4j0JPR8vfSgW7skpS83oCM\r\n7enfS0xMlCGOLdKsgj1T8RHyQYGeHETbvLYmDWW5WZhLXanwDdg2LSVcZ9/C\r\ndYPm4rDJsP1UIfrzfB3vSgayo8/VjYFSJTE=\r\n=uvys\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.20.3_1667830865357_0.2651049198572466", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/plugin-transform-parameters", "version": "7.20.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "f8f9186c681d10c3de7620c916156d893c8a019e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.5.tgz", "fileCount": 11, "integrity": "sha512-h7plkOmcndIUWXZFLgpbrh2+fXAi47zcUX7IrOQuZdLD0I0KvjJ6cvo3BEcAOsDOcZhVKGJqv07mkSqK0y2isQ==", "signatures": [{"sig": "MEUCIAeZU2MCjQeyIZggujs8MYu4TYc412vuYrojnSBsdj/3AiEA0AHos0H8Fkxpl0omJHukC4GmqViToe1KLRxmHx5DE3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Mw/+J+dSYaHYY08GTyTgypEwbeiTd7AR88kG1YqTziBPzQM0dHzq\r\ni4/txGur17TLTBYFXSwJ66VBoiCzSt8U4u6GSfW6areHRXhQxp66irhRLPKL\r\njWoAXyk3vFO30EebC5nQzh18CNy+M14TxItdjM2xTomG+0US1MyY5XaqfHMX\r\nxBbWtZbO8rHGPzvw1xveR0dd30HYQ5lXxp9uhtR5T9kZXfufNcIA8g0WCPqG\r\nZLUJtlJeJwBM20WLfN50gqYpeSNRifSJg6VzwSA5FLFVoUbBmPicfybpEPug\r\n9O+ai7UwH+51UR+ypcCPemEVuYh3/sQw2ssCkVRiMRIzWirj255MMphD6QHb\r\nAQy573jAyePi2JQSPTMXfjx2Mgig03lwzStc1Swr6WH3aIKt1128YFRdKrrf\r\nM37X8gzzTSLTl+kHYCubtB2CzYpgZczoZKELia1f1JHOhijdr+KwKvTa02vm\r\nK4afqEERLT9s8HLfGEcEXy1OamE2jiJt3DSDBOh1JtAfLE6yRL8Ty1KMnrdB\r\ntW7mhm/hf9bLvKv7QTaN0Fah6QbegqKtrZRo+sJvESx1CYLKsAbPznJjRGie\r\n67mvVMux/4NBPlyNNEQyvf9NTuxKRO4+iVevrC7PqZyj395ElUXyfAzpTocN\r\ncC1F4ngtYmNV8Kdozgho4lVAt1wzE3nkfZc=\r\n=inIh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.20.5_1669630363486_0.16452637926466607", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-parameters", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "0ee349e9d1bc96e78e3b37a7af423a4078a7083f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.7.tgz", "fileCount": 11, "integrity": "sha512-WiWBIkeHKVOSYPO0pWkxGPfKeWrCJyD3NJ53+Lrp/QMSZbsVPovrVl2aWZ19D/LTVnaDv5Ap7GJ/B2CTOZdrfA==", "signatures": [{"sig": "MEQCIEU+0+pPl/I7Es9fj7/VTF050Wx8unpCBJc+XM/ZyPuAAiBLNNZsS8CphUyiF9DtOgoHGPQOpq2Ow+kiCqmg035LgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCcyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOQQ/+MEEdZyvkPWjWd6eRyINVRle7sY4tekmKcUcYhOGhI43BUp5O\r\nSIXVUMDcOe15LmlOIvdbLoNU0KF0X8jjMQNFX/Zr3FBQlfjvRvH3P9Ob/S0q\r\nYq56JjHCRIPeQZXgBj7EnAR7f2lZIuDqaIm0GIt9+q2qfAV1nTg2QcGm6Qs8\r\nyxWdLPDH/ZBxQxt7fKy4MQ3qoAQb5X04nGQDJkDgtxhajmHg4NO6dSiH4mI2\r\noYZagsOwT0sg1jyOrv+wvhVk23fdaXNEc/DlzSpqmuYbtubUVCW+IYx4Ub5K\r\nubQcd2RoFJLHLCfD5KhwddbsNYfHU4OokvPHQj+sKN8u2sAxZblksWpHoRYS\r\nMZRwrMMzcRwYcuqizfa3RoWRYN5V9icFGDlW3048NEnztgRJhIcijHOn0RU+\r\n4E/Ho6tlxy5nRj7X/Yy+nzC9JjLe3rIdPOqBr2iBdgA5JSTMMvdCQW29ARfP\r\nv5sUwUf/BpkX1Vuz0NK60GnEO2f6usk6J5g//ldVk8EaBSpEpZ0lSi5BrY3U\r\nmuAI52iBCzwc6//LLX71G3BTGIWTHNoY4kpCAIzJnT8B3qEpWtoj3fI52XlU\r\nuno8QBP9YA01lMxS6fYlSgp40PKKe8UYfyGnrs6ktwcOeS2ZjJULdbfG2j37\r\nsuc6ekmAT2gjTQXgM9N8YYOI51LqHxLdsAM=\r\n=XRaS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.20.7_1671702321885_0.7966015329322915", "host": "s3://npm-registry-packages"}}, "7.21.3": {"name": "@babel/plugin-transform-parameters", "version": "7.21.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.21.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "18fc4e797cf6d6d972cb8c411dbe8a809fa157db", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.3.tgz", "fileCount": 11, "integrity": "sha512-Wxc+TvppQG9xWFYatvCGPvZ6+SIUxQ2ZdiBP+PHYMIjnPXD+uThCshaz4NZOnODAtBjjcVQQ/3OKs9LW28purQ==", "signatures": [{"sig": "MEUCIQCkUonT+fnSnYknfUz9EENnul75cgaLXkhc/1cfIZOFgAIgHEAxtzHDPtqu3X6IY4vy0vKi0N5z0lwbvmPuDKkV4nU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIvUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokvw//as+8nZ5red/xrzvnOqtYlgIAazfRDWOqD6HBSjg2nAa/vkAB\r\n85ZweELFzyyC4eFnHwwccfy1nZF7QiEjeKY5FGrjuJfRkaSY4oqExiGdiFF3\r\nXM4nVr4gqWewsNaGl0FTmFs8Xs4A3jWklnlv5zuwJG3uK9ioQ8zaKyo/11cw\r\nrZJNjTTYgj5CUF9+yhZWlfw3N4d3jmnjtusrDRU8nZJ7sDOR/Z+h7JB024Rl\r\nNOWw5cy6a2J/4T+C0FC7ceDf1VF7E7/XK3ebgYwUWuNkHrbhb7dZBNQcANzF\r\n3oikRV+v5awcwphhwSuj0ziZUqZeNt96EnM+JYKr7Qutqhrs/NsJSeccBxVN\r\nn+N76KfagjRzah6jTzM7H7u1BOTFDKDSlGCD1M/9n5Z4HjSSEENmj0R01+FF\r\n6fgNAIJTXYuZjFqvFXs6dUeu1XjJ9oRnim0XAtiMSQXvTIWeW7bd5OBbKY7X\r\no9/RhCCjFVQQTSEPE/LwY3z3+mlEf98PO/yMEHXst1PNE7khr+jIaCMBqRGc\r\nkBFBOuqlH1YfrSBMsYTYjz2w5YTrhm4ZYAI9lRL8xYpmRmKg0khNpUOzSfTV\r\nRMyJ28RENEF8li27ZfmSHcdgvHvdeV5bo0IRXnmsLsHFYJ5uCOJcpd14bJnS\r\nWiRJN/GTRgxAbNKfs0OeVk6Ly++7UcBhkTM=\r\n=yXW0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.3", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.21.3_1678805972589_0.31763061917868063", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-parameters", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "2acdcd7b0262fd21383db8482f98ae284e1b2880", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.4-esm.tgz", "fileCount": 12, "integrity": "sha512-cQz543P5Qjoz7C2o9o+dH+k8sML8n+yZz7eA1c2FpGmziM50gzuSyK7TDRCTo8RrQsXtdG4JQmiXX/k4PZcHSg==", "signatures": [{"sig": "MEQCIDQesIsB0ABcb5M26cTcgBn1NWd+TMYO3kK+Z12PW/nrAiBkkaEARxkfHTh+PxKGx5scfZtsUSabrrcmCY/P6g3y1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxfBAAjp9uJlVa8aLtlt3s5K5rSE1d2pUWTvuLr1zFCXP3Td76gnTq\r\nBVZifdj+Mih0CHMR2MG3xCR5Qg1Wmo6ZLJe2VHhc145fIpFZBoPlVuwHztei\r\n0As8FCiY5wK43ojjUQZ5agyjI15LSw5VBNuA2WSopWiWZq1wgC4HJcfCF6cz\r\nOwv0CJ6kQSe17rEzsA2jCwO7vOTjejZMcgF/AQ5wTQxjIGXsYzBYIHYOd76d\r\njOiSJNXXdfdgmMGE01H8V5zMbUs3m9mJctVRRS+OIwE5xjeUOA6uaT2MGSSt\r\nayDqDcapkIyRnjsdSxT9GWLHvGBnmaFSKugdjsRdu6cOlB2ezgy949CSRbOB\r\n2E+u0fk6GOo54X9PjAAFhr/xAzxJaBN6mbCC4eqKMiE49uTwAlxHSSWS2yUA\r\noYKBtFQgl/a42K4mlTUNdLrGz0vDh/rONgI4uvN3bOpci2am5y3YX74Ls2LR\r\nJ8p27CI9ub5PPhe53SI7rIzZjHEIf5pUiHxff8qT6YD0dMz7BkBkHKTYzWRL\r\nhX+FQ0+4+Sc0/2yhahM60FzUWIkDQyguP7BMv04+xjB5PfsEQ5O7D7uFaU5O\r\nIEWTzDXRvec8FPBF4MpZ/3egSSNgM+FZ9daK83Iu11+Xe8PLNyfRUmyysDQa\r\ntkb6PO1nrc68BzjNH0tS8B/wgd8zXnPY4BY=\r\n=s60n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.21.4-esm_1680617365701_0.6807434454323356", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-parameters", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "d61b8ba395a0a765ba831a3e3de8b89a95ad1f00", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.4-esm.1.tgz", "fileCount": 12, "integrity": "sha512-+W<PERSON>uzFRSuNJJZs7Fj4lI67ZVLyH0ldZH1Tsj9hZBV0uNlEyittRxVZ1/2AoUZeSoLNkdOuNWNcar0uV3wo/dig==", "signatures": [{"sig": "MEUCIDv/+z5y/WARBunSwYovlG2gU47A4Gh0KdQzWCaPeRkUAiEArc4viwvVPFvjDIfa18zuvA3guW9nwqj7MeS3v/rKVd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWVQ//aBWwpeLRo93eNHM5FDQNmc2sxr9O4oAnNZYORD7D8m8OfdRd\r\nvsIwKzoMlwTUAqIr/W5BvV7hWLV8LxV1o5uk2lRyDDYFaNRlYLbJfv0ritqq\r\npSTviQtuWB9OOPv+nARgWIgEMqpse5ba2BHlkF0cTljpbBs6rotPjj5CO5+5\r\naQtF5q2P4mgEAzi1yt40TrbAPPgPXoEzQRhKI4EKSuUCSjCPrDdl2ry/8Cmc\r\nuLC1osmHa7tHKpFmOkcitdtNc8V9MB5JIROcqWC9CdHDF+rrJdtuJsudyCv7\r\n27dlX02OpaftvncaSPX5H7HQEbt929XOPjlnrMO7eyT75zJgOH6x+4j0cHFg\r\ngUn0kR7HQRYudpriiRjB+FwaIowEhsPrUSKWvlIIS/obQhCdTPLhs148rotP\r\nZHJ3weznphczGjodPbOsnxlAze9fPlgfmmJ5Dq6XVomTMRcVAyB7fKCwiF1L\r\nV9QhNCxB3xIic1JGqqzG8RQ4xfoy8fKBieum+ba+PURqt3ZX8l7yYm/y6zwY\r\ncJwj49RuhQhbALX7ooyWjyS6oxZW5Alqq36o18TE6GnNlhunJvk490Zm0qa/\r\n4ZsrFuEVbDJ8qAFD9h97RhvkAErUzxUbM3UyJTKPuzUhwxpPlITx4W1AtY12\r\nPD2joayJ1bNkO4UW6Ij7k8QhhyX/zEgWoPk=\r\n=6RK0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.21.4-esm.1_1680618076876_0.12165225545207314", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-parameters", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "c24af4c6c4aaea26cd41e4c6b41733115e12e0f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.4-esm.2.tgz", "fileCount": 11, "integrity": "sha512-AfFL9UhJS3AEmpCJfLBc2mlDemTVnP1WqAVAaeE0E4UuWA+7dFs4GbEBHpGKHEKSY4D4Fu9yysGJ001Q0P0vSQ==", "signatures": [{"sig": "MEUCIQD3IWqQc5efaTPzq67RvxZ5hWN7rf46Wys1Ceh3V6t0NAIgMM6esTEUrkI5wIEURD/xLPJmA17QkFlqEaIo+qx+xjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo08g//ZsW7emVTscMMpkaOFo9/ThAU/wwSaSpb6iIMO0C9g+l//OA6\r\ntyOVLGke3fD11Vvfh7bDhaIQn7QWdZQJxVvdhDVtlhGIuSjteqzPHvWgDx4U\r\nbNP+yvepPlIv6UixK6+6rqKQkcqaKN5azeEKS4aIhrUDSEM5WJTiTG6isfeI\r\nCOL7vQFi4/ZywVx9ILhqv9kNMliTRw9UVBGzpuvSFk+sLUUNnVQ0lEf/yTMa\r\nm5Wr8GezZCSRzG7T6ooOGWR5aZLLSS6tyk4kBnu96ogi/F+wX20rjLBH9EA5\r\nSTyNS1gkk1p0Wf7No1fCwFftJKjKxB1FkqBIJYElsSNxSrtFK15JlAKaSF4Z\r\nt5XFFchlceMiU+ngaNHZrGJVSYHOnOtFxzR9Aqs9yetoVkTTY1EWB+Co9job\r\nMt6yC8BKsbISmy3mGqIT3MOcQweLXG5Riq1JTNcOMbR4Jp39cYssyfv86weV\r\nVYh+1Me9v5jogOCz767NbFekpnm4hupuRgvR0HoH/ACyMt63M5WBzXEVcPfs\r\nUkP6Y0/1rkzHu0RCouAUdq7mLlJiDHDqCu/vj1628j+ebWOAR9my92FjcW/P\r\nUKAPtKG2tqyR7PU1N8UE3hrY+8/iWmunaJFSoLzAHrAsbAYjeuqzhNljhplu\r\nm60Z0kh1jHPLk7nuqr9RWJAorOt57sL6m68=\r\n=gC22\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.21.4-esm.2_1680619157400_0.05114183688710061", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-parameters", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "27973fcffa0d1c9d51a03fe5b28846331b581869", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.4-esm.3.tgz", "fileCount": 11, "integrity": "sha512-htJFZPRi8hm0kHcG6+uM6Z979Ch2UiObnbHscVJpOFm2rC/XrBASNQ6AGfjvm4mijMnqMLqPV77ZR4wfTIkrPg==", "signatures": [{"sig": "MEYCIQDNO3HwAfxanhjw3rrjyG5PcRbBXsD7VcsfqCf1FTAmQAIhANclrztOTUT77+/x1swYj/LD4ueSYYtIFgcpkKlryw7N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNuA/9EEF2flX6WcFAWRHv86sEdq62Ri8FzDZ0LMtDtapkeWO7bx8p\r\ngliUBuuVWkfD7d9i8zLChoSipQMkUEbXq6dkYHmeUGK/P/qbA9DJQHt8o5mS\r\nFQMVWO1JMJ6NuWksDcyTIHKkY0edND1Nx5tVPITlGoJK7xM34VKNiUxfZzdb\r\nEyaKwX/tHMQFbbehjSqEEwGTw1HnAZbPrYOPKu7y86fl/W9zaej7fl6LrSt1\r\nwqooJ7DOJl0c+wB48OAn54czgD8gFerfgVPqORML/I8L4Ls3fjI/QFFy+Syq\r\nrsyG0KJ/l+NHReGxb+zyMnEcGnxhs/9gPB/k6Sg1CEd+/CXzcWr/QD2ED94P\r\ntAMDgQAkmIEa9zaA7P9s8WqiRZtT7OMCKLJyvaexz8AiXw1mkwEzTeuqd3v5\r\nFD8iv8uCPXuGzuk/XUb3LX6gfZDb8eUJY7u2+iJ2uLbYmEqJUQ1grQyl1+xy\r\ns0RVph2XfWQmJqeEir3yxkiZhzk9abJ3E2ovVlt0MF9nRA11Ghvcm0oo5cSo\r\nPJZZZuhKv82dn9w7AR38wlr4/dYZuo9oX/bO23qSzyGPynfD3AlrPm/qXM6v\r\nr9r8dUXyK3FYXLLAkFiefPQJ0ZrS0B/R6tHvmEif+fohT8Bhmfa1H5OagJiQ\r\nSO2NMA8uek7agcLFQBHREOTYvSVJup34ClA=\r\n=6I2F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.21.4-esm.3_1680620168529_0.16594254016366383", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-parameters", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "69591526028b6b9bbc84dc6592477c7be1d2cf9d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.4-esm.4.tgz", "fileCount": 12, "integrity": "sha512-DvhhLW70fdycYHEzY65sZw5z1U+Pzt7Klc81PcMkvYdy1tuLbulDFuVgdS1KriUGe28FDHgwsICDDjn0QYBZyQ==", "signatures": [{"sig": "MEQCIGJgMY/Y9iegjmCvRq02lMsKshiTkNuZYKq96kjwZawkAiAqe7mZKQZZM1/YHA0Ftq50UkW45f/NY5GqfpCY8CFkcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI+BAAjYyHEsizq9LuDK2qUACTuFnXMcc4i51lvCD2EbTR/ApX/HzV\r\nFPvrVN/3ukxoAWo9UvTag9hI0RVecEz5GF4QspuW8pHW9EzCWKKZweIkyanW\r\nNWQMTs2vGPOmjTvc0S9o0X7z3eJAYGV2rywEUjcXyyrqhSET1v7219Cpw9i/\r\nWi9eNwe4vCVoxm0q3UW1/nd47gN5ulWg5WRGZJvl3SRjlv4KBhFWOgS0MXeX\r\nHWiSbBfFXhe6vpeaQTMfX99zMt80XvVx46Ej1wy7dypXYCvqwGajP9EtoQsn\r\nqBlxbj5H7nF5ylQRgwO0dc12j8k+dAa3XXj81VRBaasFaTf7gbMmIT1bmd5R\r\n2qtaQRLEyYgvHOrZblH2dPAMuGSyrNpoM9OVH2k0dNfpK5mo5t1JcHBpliEy\r\nYuYAnh2wjvv3Y62mdOasC8MMcuJ5EEowf259GlYrsVCA2azqUkKvxjZF9vtJ\r\nzrSdN1LYJPZQbNF6OsAY7f1TFFm+ykd4C12n6M8zFz8bUI/Xxfe1TfLD33mk\r\nUScbGIxYciLEx7dajWWcqtRSgbB9IY4qbd9h8cKsQxxcDly0qex0/vKF4quh\r\nYwJbbA0W+9koUK+qhdQhc1rZYcWIhT59bZIV9oaqX2LFVv5dMaRdnyw/Xjaw\r\n6baweqIcf4Bhkc1eWNSavO12HAsWZy7lDR0=\r\n=pSVn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.21.4-esm.4_1680621201087_0.7678489195499558", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-transform-parameters", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "0c1941e4aab5137ae85a6e5ef86b3ba137ba165e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.0.tgz", "fileCount": 12, "integrity": "sha512-hlRM1lu7xeqW8EKKg9ByHwnCEIy0dNPd/fwffpwAck2H3C5mQCrWR9PdrjsywivsFuVAbyyAImU58vAR1cXrEw==", "signatures": [{"sig": "MEUCIQD5ov/zgD5Q3R0y+5wxmXpJXPx8Iu5F3FxnMhd1htzXWgIgZFgZcfwX+4TbJHchFoaXi4Qjif+kzEB7fjPuz2kaZvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64684}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.22.0_1685108717165_0.40403375100614514", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-parameters", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "24477acfd2fd2bc901df906c9bf17fbcfeee900d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.3.tgz", "fileCount": 11, "integrity": "sha512-x7QHQJHPuD9VmfpzboyGJ5aHEr9r7DsAsdxdhJiTB3J3j8dyl+NFZ+rX5Q2RWFDCs61c06qBfS4ys2QYn8UkMw==", "signatures": [{"sig": "MEUCIQC8CyQ3kohthkNvdxx5p7czW33KVG7BeP/WBCorG4Ey2wIgSpYCX3xUQgyS2Lfet+ih2V1RVUJgB7dVmL0WwbPhvfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64888}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.22.3_1685182258813_0.7504788268276319", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-parameters", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "c3542dd3c39b42c8069936e48717a8d179d63a18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.5.tgz", "fileCount": 11, "integrity": "sha512-AVkFUBurORBREOmHRKo06FjHYgjrabpdqRSwq6+C7R5iTCZOsM4QbcB27St0a4U6fffyAOqh3s/qEfybAhfivg==", "signatures": [{"sig": "MEQCIB0OYRd5CXd84w82ADm50955jwScX2e5alAPHrgyJ1R+AiAZ1b1SGCDF6CrouMyPsJchiY/Qy29AP124vSXez3witw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64888}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.22.5_1686248479313_0.07010199916150661", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "e5fb2108518a5aacfd7c653cef7e17d2cfb14545", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.0.tgz", "fileCount": 11, "integrity": "sha512-nqbC4ii+/vkTIMRk/TBmg9RcV+pEUdHRrsW+2f+i011cHHOV+Q9inMP6w3a4mGHrhY1AZaL0Sej6SnPH3Lj/oQ==", "signatures": [{"sig": "MEUCIQD5Q5faCdbHyO52ZCq0Y/aKg6UgM+nsRQprMN2TgssPpAIgJEBR7naVC3DTMSQ17Rz1o9Cee2YkbfUMEl3q33ThcOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120802}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.0_1689861594117_0.28263836373302786", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "ac0c307e2507c86a885d08caf896f3ee6c32ae4e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.1.tgz", "fileCount": 11, "integrity": "sha512-HCvol7SRmRINS/hSofrSf5UbTqyJiJ5aBdILg9bLO21tsip6jUblA0a66HpKonEtjaAXHd6wWR/ZvY1Hvjs0LQ==", "signatures": [{"sig": "MEYCIQD3RZ3AW7s0RmyjyaPHB0gC5FwbCbAY1iW5JuYUREVM5gIhAJ6kYKnDR7tTqcRqswGNm5T8DfR2TRXW124M4pnLmVCd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120802}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.1_1690221115033_0.8008295528610379", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "426fd1a6ccb11a2fecc7d657e7d417cd9ac7b9ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-ON6JG1q8g9DOcmXPKVjIerXWpZJIBlNy1Ml4mGE3YDhtAVZ3bMpeFWQZ++DDHgJ1POolmQUAkxgMX1m2ozJ89w==", "signatures": [{"sig": "MEYCIQDpc5aMNbRFMrAzX2psvBllAWTEQGB+mRuWQX7nAi64tAIhAIYGn6ZAfb8VHUyFuJ3eWzrkgj7gzxy9yQgof7TWfnmv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120802}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.2_1691594094348_0.22525672855113532", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-parameters", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "719ca82a01d177af358df64a514d64c2e3edb114", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.15.tgz", "fileCount": 11, "integrity": "sha512-hjk7qKIqhyzhhUvRT683TYQOFa/4cQKwQy7ALvTpODswN40MljzNDa0YldevS6tGbxwaEKVn502JmY0dP7qEtQ==", "signatures": [{"sig": "MEUCIQCRtadPmTtdSSsDB0Ketp9PAs8AT2MCzFUTIdO1eRYQRAIgQI9BIJmnwSLMDY9QF0MWAIZGMfLxxbUaqIVOVvH3xVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64911}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.22.15_1693830306384_0.05918161874812111", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "447d6809e23255b68282616233970e5ecbd45cb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-I2<PERSON><PERSON><PERSON>Oui67lGDkX66Qvn0re6WLMkhNaQG6XOGQT84DZ3a11esNDo71xp6WyVMYaNHdJdNDW36Zz5ffS0wjY/g==", "signatures": [{"sig": "MEUCIQC4WqUNZSMPRhv9I9mET1T8jYswUIwXKUP4Kvtg0HEoFgIgDSRFxjRafSco+agVmvINlhNI3i+vkkYtCoJNkzd/N+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64147}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.3_1695740211057_0.6660557333943473", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "ac2817ae49bab3f0e2a0cafb795b7eb4f6f64623", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-4ZD43OPWiilvVtaabbt6gYehRzbc5uISq5+DJHWChw7cfT47B5siloLgtmB/OSaL9hRcWZGjSk/E36rHoVdQ7g==", "signatures": [{"sig": "MEQCIEnGVMLxGF4oDcO+y8Ik3Bahi3YDeI6USTbYGMCSq2iRAiAGkRo7GeUuXGl91HodFFcvwkHOYvDQo4/rvcFtcUBsXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64147}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.4_1697076376865_0.8923761488682931", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-parameters", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "83ef5d1baf4b1072fa6e54b2b0999a7b2527e2af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.23.3.tgz", "fileCount": 11, "integrity": "sha512-09lMt6UsUb3/34BbECKVbVwrT9bO6lILWln237z7sLaWnMsTi7Yc9fhX5DLpkJzAGfaReXI22wP41SZmnAA3Vw==", "signatures": [{"sig": "MEUCIB2M2o1w9nS7l5NcH/VkJuy0JCb1uz21/VxDkKWIU6H/AiEA+hbqkncH3hp5EjdzEHOzAXFhn4UOR2+ZkC1ipfsJ+WU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64967}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.23.3_1699513435541_0.6514516720468797", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "0480566bda539de5b96b3558658aea87c99d0d90", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-74yP8QzmdZKs5HmrB6VsPSMaWXW7eLOGHxOzCOmH84mYWlKjY7JyCX3fF5ijQZFdKKlAnZoU9/l6ReePXfIchg==", "signatures": [{"sig": "MEYCIQDUA7og6vOim7uXb3C29odsb2W5ocFTlO2H2K6OPVeVFQIhANO5kma2hzFzR4sIgV/o4qPqv4LGYVnNsiiyj/DlBSYH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64260}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.5_1702307927740_0.3101204563160158", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "bfa1fc04ef6e367332e5251a83aab4fe2c1b89fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-eQlg8JGHYqzpwc1rCp5pZcyPAu90SVkxRD/ZX0oLINSDMic7/nrA4jqm8nRdNCq0FX8Zf7kyMgS5LGuJmp4uRw==", "signatures": [{"sig": "MEYCIQDbYFz9+I6o1ghRSceeFpXi7gX8r8R/7FHdme4hvIDScAIhAO+YDbYyqj4zTnazpivNurAAHj/uKdmp8Bo6jj1DYH9W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64260}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.6_1706285644486_0.6629116736880145", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "4909ecdc8c904ea806b7245da60eaa015380c7b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-sQO/ZXfZ2zdxjuiOg503RVKvv1QspVxEQ6tC6edOrV3GLUsrMabbyyyw5fVGkfDnetLeU3Bo3r6PQd+CUf0mgg==", "signatures": [{"sig": "MEUCIQCxrO9522JsA20ggFDzaPKVtwcavrbneCNXeIBr7c6AUwIgXFxAJTgyPaWulV2l0qlsWjZjAwtABe9sM/0xGqetwss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64260}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.7_1709129091845_0.7023960029346192", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-parameters", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "983c15d114da190506c75b616ceb0f817afcc510", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.1.tgz", "fileCount": 11, "integrity": "sha512-8Jl6V24g+Uw5OGPeWNKrKqXPDw2YDjLc53ojwfMcKwlEoETKU9rU0mHUtcg9JntWI/QYzGAXNWEcVHZ+fR+XXg==", "signatures": [{"sig": "MEYCIQCNmGe5iBkdTsFNknLCznVDP7D6yto9cKVN7XC/JRV81QIhAJUS+FymsIK8e5v6hbpkzcbzD2ct23MOtSF8IPOXk1e8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64946}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.24.1_1710841734474_0.09463927211163381", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "0e7c6430550cb835f4b4acb0d244dadd45478b9e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-HXr0DWvR6S2J7GBUaRL03J3fEbvga1xinjubiIThk95Y5atQ2jUVONfclVXT0bQLI1UCOXIhQiRY582Sa68U1g==", "signatures": [{"sig": "MEYCIQCfyc9eBC13BkFPyAt9Go3PK6McRHtpDTl1GhSD8DmRNgIhANtYK9DL0UbTh6q2I5GhpAnGvgi49aKfgGKVJABFRuFx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64174}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.8_1712236789459_0.15163661604942136", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-parameters", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "5c3b23f3a6b8fed090f9b98f2926896d3153cc62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.5.tgz", "fileCount": 13, "integrity": "sha512-9Co00MqZ2aoky+4j2jhofErthm6QVLKbpQrvz20c3CH9KQCLHyNB+t2ya4/UrRpQGR+Wrwjg9foopoeSdnHOkA==", "signatures": [{"sig": "MEUCIHHNGnJWd2V/Muj0Xfxs0DIlXOvz4vauqGSSOR4EBCMsAiEA2Tazig7w4ia5GLPIU7stGUAhLcpcPeFDY+q302aWLvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131538}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.24.5_1714415658939_0.04905069245212346", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-parameters", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "7aee86dfedd2fc0136fecbe6f7649fc02d86ab22", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.6.tgz", "fileCount": 13, "integrity": "sha512-ST7guE8vLV+vI70wmAxuZpIKzVjvFX9Qs8bl5w6tN/6gOypPWUmMQL2p7LJz5E63vEGrDhAiYetniJFyBH1RkA==", "signatures": [{"sig": "MEYCIQD9u3N1TMwdFlu+mRkpoS/INxNONLIhoYb6w2TfH3qc0AIhAKVjuv8GKNiST1JqS1ijhImAqflLNYKt3QDGDB+m56nG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131679}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.24.6_1716553470912_0.8829942844990726", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "6fc7bae97f0982df8fcce3e865eb712a59898619", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-dDSYvyLWWb7qIMZ6irVFhIz/b7/D6XQJ3VzPgtb/M/rt2bhtlYWapq3bnCTKS9NhWo/+gc69xm1tOI1MK6bbXA==", "signatures": [{"sig": "MEUCICrKB+0aQhPk6WF0gV7FJcILyOioXB2Io/SZ2hEneSxYAiEAxlax6OuTiDzzmTVDsMMls611BEOcEJyNu9CpzVtrnfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131610}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.9_1717423455578_0.4804835012442199", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "e601241762bc899fb0830d014f0f5017da2c40a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-qqpF1kfiG5amoPH044GRMfPx6ikVmf/MCP9uxY267DyFHySnLKNFxE7pKhRMiKrb0JeLh647gGXsG5WFSHcogA==", "signatures": [{"sig": "MEYCIQDrgs9VA1/DbKsutDLEtVu2Rls9H9jerg/+gN2NI8EESAIhAJntO9DpbFvDxhp/KA9j8swGnV6VUzdUNw02TxD6YkeN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131617}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.10_1717500005966_0.27286221865908855", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-parameters", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "5881f0ae21018400e320fc7eb817e529d1254b68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.7.tgz", "fileCount": 13, "integrity": "sha512-yGWW5Rr+sQOhK0Ot8hjDJuxU3XLRQGflvT4lhlSY0DFvdb3TwKaY26CJzHtYllU0vT9j58hc37ndFPsqT1SrzA==", "signatures": [{"sig": "MEYCIQDG3urT31EChI9AUMWdE1OUQMqvfoPIT91yX0DTaG9RCwIhAPErOnsLNlkZLLAWRYJVRqw972h+oXtVPbhOobBTiCwd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131640}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.24.7_1717593321586_0.6488908007616476", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "1875d48bd7ec565a13ee826d97a42235fdb129b2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-//7c87fXlrJF+2Q0jDNusH7A7th2FjcEGMY1We9pJImBPTljLD5jbOClntSmV/PZ933jSYMogOLQ3oHSUtE7gA==", "signatures": [{"sig": "MEYCIQCdg3X5hImmwraPD8gFUcN4nAcOmqSAJsYjVV6+jMfTOAIhAJ+ajjSIKScDNwzX4WVtjxadaclZHWNlpPYvTSbieHV6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131508}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.11_1717751731231_0.5839351464554514", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "650d08307dc2d1e9f0e8e6c01cb95b2d7525cfc3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-jstZx3uZmDE6yqAg35LSKTroLrlG89ufOaQ/O7UCFxRvaX4m6qQ3swcfzTAaU2jnXuPLE0FeIqV4AEzrteZfzg==", "signatures": [{"sig": "MEYCIQDDj9rX/CBNzp+IqMk673JJGn+XCQYpPFu+5zAyuHuaggIhAOdtsqEdv+0Et0022YFX3KKk6SX5zgwvnXdDPBqJV3C8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128250}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.12_1722015207985_0.89704815527782", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-parameters", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "80c38b03ef580f6d6bffe1c5254bb35986859ac7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.25.7.tgz", "fileCount": 13, "integrity": "sha512-FYiTvku63me9+1Nz7TOx4YMtW3tWXzfANZtrzHhUZrz4d47EEtMQhzFoZWESfXuAMMT5mwzD4+y1N8ONAX6lMQ==", "signatures": [{"sig": "MEUCIEADfe4H1TM5S5qK2WAn/KI3QnkkoJXdXaz8kAw++UzSAiEA/qsyV13A6+ggZdF5sNoaPXuFdKMtTmnjUgeVGt1I7lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136153}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.25.7_1727882087985_0.012606467046010206", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-parameters", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "b856842205b3e77e18b7a7a1b94958069c7ba257", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.25.9.tgz", "fileCount": 11, "integrity": "sha512-wzz6MKwpnshBAiRmn4jR8LYz/g8Ksg0o80XmwZDlordjwEk9SxBzTWC7F5ef1jhbrbOW2DJ5J6ayRukrJmnr0g==", "signatures": [{"sig": "MEUCIQDseqEx9AK4n9VtnnkAFHBfuHLOq9J8tv/C2O1hQpLLTQIgJu5Y36AH5kWitjBEkdUDRLxM06g71ZtM0L1R3fBqmho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65125}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.25.9_1729610466633_0.22679058357952098", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "c3421f722a6274d42e49aa13b95568f770baa84a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-ExA7iIW3UtPiLhxYWPyx9HPvrmUM+210jaMk2gd1O9N+YV+nycawg6dBSuNYPoh1ZCQdB2U3/BmeTABJNlD2gg==", "signatures": [{"sig": "MEQCIDlkK8QkpE3WhKm7gk8eKepeyG0oP2TMCmeua2t0tTlzAiAPZh0/tFI5fvm4qRf412CYt0yEXCCpYCLFPOF2Hy/0yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65115}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.13_1729864449902_0.25246444519687805", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "afae6fe59cdd3ccda05f2889958e25163c31fdf8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-gSbF88iqunnQNTiw8yQJyExVUFxHtGhTkjoAiZG/OqAPIY3QLNVm/gdO7X2Eo/AJs9rV6ywVJUdlu0qgVy4LlA==", "signatures": [{"sig": "MEUCIF/V2wG7oQP0cWSKaTAq4wBl2nFyGeZnNWAQe2VckbfoAiEAxXv+i1QY0cVvB28dQ27IvIxe9SEXt5E3ePoEQQM/MeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65115}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.14_1733504040801_0.15454127832797804", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "4db4a9c710d80b7bc7233bf95f5e4be3bd12977e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-7CdEZgvyqWNL7idIzTvvEq/07JTYvnATSD2p4e9KpfHSo4HceKMFC9TQzn6nRY/0CVMxch6TweneT42XihOh+w==", "signatures": [{"sig": "MEUCICfV4GpsRX1UpVaVSenSObxgJ330z/F9Ej/TJbJlGu1VAiEAg7YqMvVQYio7Mk2nOeoq/sD26rdElnB/y9GwK+JAeek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65115}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.15_1736529866648_0.5500422357383108", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "2e013e55b0736601b787e88b88f274a7ad2f4b24", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-tcNXKB6Bv5wDVo2IKJqI7Wx3CRI0Hib8/l5G/mT8ut1+PRbDqZ1n/Xf7QXMLNHnjHiEPimJR8BLqZMCjjBsi8w==", "signatures": [{"sig": "MEQCIGVgUrxhfMwEcF6rsK6vuQ1wjRkTuBxk/rAM0aoHzK6+AiAi6xsUuQ9z/uIj/1p2j4v0uQpFhEEJJ3eDpC60q7nc5g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65115}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.16_1739534342469_0.8802164383102524", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "d8dbedd62a5f705c9094cfc209a1cd5f3490b527", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-cPS10i4/2DEtttMMuNlZaApB0bQ9o4chKt5lfXNqXThd0tNnC2ty5Qj4C2BtxyPfkM6jrQWUcx/NgFOXfP44rw==", "signatures": [{"sig": "MEUCIQDNRHxPCAXFMDHADCW9AtxVPtZLsfvAw0Vyc+YcLZowaAIgYo2ltEWpM7ErK+YD1HPY7CnKT4JayXnI2AUnmFRI5Zo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65115}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-alpha.17_1741717493965_0.7154523740002465", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-parameters", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "80334b54b9b1ac5244155a0c8304a187a618d5a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz", "fileCount": 11, "integrity": "sha512-018KRk76HWKeZ5l4oTj2zPpSh+NbGdt0st5S6x0pga6HgrjBOJb24mMDHorFopOOd6YHkLgOZ+zaCjZGPO4aKg==", "signatures": [{"sig": "MEUCIEGv4Gqzm1FSpP+6lZjbAMKI/UW4JXlMz4PQTsNdnxeoAiEAioerPiBH/fVSl82fNsXRL26FVAEFS5bMid4KCQjSPpI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65125}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.27.1_1746025732256_0.5989023777467248", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "16a94dd19f106a941df1ebb030276d966fc68f88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-rksed39pJVBRtwQjLGSRaNLEgLPfPy+7rQ56QtLwwwvuPziQ0H8snFXLrk3OM4AvNTk4JMCoqVjE99yefJ023w==", "signatures": [{"sig": "MEUCIQC4kdnz2WjvNVXCk8yoI7fyPGrYFQLvcHTlfUQt9AMRVgIgftbJL4kJBewLAhxPy6MsBwWexALnIGfAd8Dw6UhGurc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65091}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_8.0.0-beta.0_1748620264271_0.23798440568681345", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.7": {"name": "@babel/plugin-transform-parameters", "version": "7.27.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-parameters@7.27.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "dist": {"shasum": "1fd2febb7c74e7d21cf3b05f7aebc907940af53a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.7.tgz", "fileCount": 11, "integrity": "sha512-qBkYTYCb76RRxUM6CcZA5KRu8K4SM8ajzVeUgVdMVO9NN9uI/GaVmBg/WKJJGnNokV9SY8FxNOVWGXzqzUidBg==", "signatures": [{"sig": "MEUCIDnk9JMIllMQIEaR+7XgwF9advTvWJxOQNuPTIDegqAgAiEAwP9lseekE2aqOz4udS+bvVgs6wLTWUycuQjfAE2oiUU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65170}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-parameters_7.27.7_1750946597523_0.613193063567522", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-parameters", "version": "8.0.0-beta.1", "description": "Compile ES2015 default and rest parameters to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-parameters"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-parameters@8.0.0-beta.1", "dist": {"shasum": "073c2355cc461941aeb3d7a278675e07f64a0603", "integrity": "sha512-MusSqeOIM/ptT39LrlBueheRL2gHmIIuZR8JGQ+sO97Vx1zJR9pvXj8HVTcTfrYJAv9kAXqkJadKIZ0RSJiQiA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 65136, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCiu7wMSlh//xt7LWIAT9ViryZz1Md2EMhFsZmlMEWX+gIhAKbW6ztmFAM0mV1hx4f+QO3M4inocsvKtWYek+XCXfMN"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-parameters_8.0.0-beta.1_1751447056822_0.8369858039036568"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:22.989Z", "modified": "2025-07-02T09:04:17.232Z", "7.0.0-beta.4": "2017-10-30T18:36:22.989Z", "7.0.0-beta.5": "2017-10-30T20:57:59.412Z", "7.0.0-beta.31": "2017-11-03T20:04:32.891Z", "7.0.0-beta.32": "2017-11-12T13:34:09.163Z", "7.0.0-beta.33": "2017-12-01T14:29:27.377Z", "7.0.0-beta.34": "2017-12-02T14:40:26.592Z", "7.0.0-beta.35": "2017-12-14T21:48:32.961Z", "7.0.0-beta.36": "2017-12-25T19:05:50.051Z", "7.0.0-beta.37": "2018-01-08T16:03:48.448Z", "7.0.0-beta.38": "2018-01-17T16:32:47.333Z", "7.0.0-beta.39": "2018-01-30T20:28:49.788Z", "7.0.0-beta.40": "2018-02-12T16:42:55.145Z", "7.0.0-beta.41": "2018-03-14T16:27:07.302Z", "7.0.0-beta.42": "2018-03-15T20:52:15.786Z", "7.0.0-beta.43": "2018-04-02T16:49:04.885Z", "7.0.0-beta.44": "2018-04-02T22:20:44.559Z", "7.0.0-beta.45": "2018-04-23T01:58:57.744Z", "7.0.0-beta.46": "2018-04-23T04:33:13.082Z", "7.0.0-beta.47": "2018-05-15T00:18:13.682Z", "7.0.0-beta.48": "2018-05-24T19:25:08.999Z", "7.0.0-beta.49": "2018-05-25T16:05:02.971Z", "7.0.0-beta.50": "2018-06-12T19:48:20.086Z", "7.0.0-beta.51": "2018-06-12T21:21:02.504Z", "7.0.0-beta.52": "2018-07-06T00:59:52.494Z", "7.0.0-beta.53": "2018-07-11T13:40:55.792Z", "7.0.0-beta.54": "2018-07-16T18:00:32.774Z", "7.0.0-beta.55": "2018-07-28T22:08:37.352Z", "7.0.0-beta.56": "2018-08-04T01:09:19.535Z", "7.0.0-rc.0": "2018-08-09T16:00:39.855Z", "7.0.0-rc.1": "2018-08-09T20:10:13.188Z", "7.0.0-rc.2": "2018-08-21T19:26:21.607Z", "7.0.0-rc.3": "2018-08-24T18:10:04.182Z", "7.0.0-rc.4": "2018-08-27T17:07:01.318Z", "7.0.0": "2018-08-27T21:45:15.847Z", "7.1.0": "2018-09-17T19:31:29.457Z", "7.2.0": "2018-12-03T19:01:36.432Z", "7.3.3": "2019-02-15T21:14:32.935Z", "7.4.0": "2019-03-19T20:45:23.364Z", "7.4.3": "2019-04-02T19:55:55.281Z", "7.4.4": "2019-04-26T21:04:57.248Z", "7.7.4": "2019-11-22T23:34:02.158Z", "7.7.7": "2019-12-19T00:53:05.871Z", "7.8.0": "2020-01-12T00:17:26.027Z", "7.8.3": "2020-01-13T21:42:30.426Z", "7.8.4": "2020-01-30T12:37:07.413Z", "7.8.7": "2020-03-05T01:56:13.950Z", "7.8.8": "2020-03-12T18:48:58.792Z", "7.9.3": "2020-03-22T11:02:49.154Z", "7.9.5": "2020-04-07T19:25:13.639Z", "7.10.1": "2020-05-27T22:07:55.207Z", "7.10.4": "2020-06-30T13:12:45.036Z", "7.10.5": "2020-07-14T18:17:48.291Z", "7.12.1": "2020-10-15T22:40:16.066Z", "7.12.13": "2021-02-03T01:10:59.198Z", "7.13.0": "2021-02-22T22:49:58.829Z", "7.14.2": "2021-05-12T17:09:24.794Z", "7.14.5": "2021-06-09T23:12:07.088Z", "7.15.4": "2021-09-02T21:39:25.140Z", "7.16.0": "2021-10-29T23:47:31.536Z", "7.16.3": "2021-11-09T21:53:02.954Z", "7.16.5": "2021-12-13T22:28:21.404Z", "7.16.7": "2021-12-31T00:22:02.938Z", "7.17.12": "2022-05-16T19:32:57.339Z", "7.18.6": "2022-06-27T19:50:01.677Z", "7.18.8": "2022-07-08T09:32:35.145Z", "7.20.1": "2022-11-01T11:25:41.489Z", "7.20.3": "2022-11-07T14:21:05.535Z", "7.20.5": "2022-11-28T10:12:43.657Z", "7.20.7": "2022-12-22T09:45:22.085Z", "7.21.3": "2023-03-14T14:59:32.743Z", "7.21.4-esm": "2023-04-04T14:09:25.848Z", "7.21.4-esm.1": "2023-04-04T14:21:17.062Z", "7.21.4-esm.2": "2023-04-04T14:39:17.540Z", "7.21.4-esm.3": "2023-04-04T14:56:08.796Z", "7.21.4-esm.4": "2023-04-04T15:13:21.303Z", "7.22.0": "2023-05-26T13:45:17.369Z", "7.22.3": "2023-05-27T10:10:58.972Z", "7.22.5": "2023-06-08T18:21:19.582Z", "8.0.0-alpha.0": "2023-07-20T13:59:54.333Z", "8.0.0-alpha.1": "2023-07-24T17:51:55.193Z", "8.0.0-alpha.2": "2023-08-09T15:14:54.556Z", "7.22.15": "2023-09-04T12:25:06.620Z", "8.0.0-alpha.3": "2023-09-26T14:56:51.259Z", "8.0.0-alpha.4": "2023-10-12T02:06:17.164Z", "7.23.3": "2023-11-09T07:03:55.785Z", "8.0.0-alpha.5": "2023-12-11T15:18:47.966Z", "8.0.0-alpha.6": "2024-01-26T16:14:04.633Z", "8.0.0-alpha.7": "2024-02-28T14:04:52.001Z", "7.24.1": "2024-03-19T09:48:54.592Z", "8.0.0-alpha.8": "2024-04-04T13:19:49.627Z", "7.24.5": "2024-04-29T18:34:19.121Z", "7.24.6": "2024-05-24T12:24:31.087Z", "8.0.0-alpha.9": "2024-06-03T14:04:15.740Z", "8.0.0-alpha.10": "2024-06-04T11:20:06.117Z", "7.24.7": "2024-06-05T13:15:21.870Z", "8.0.0-alpha.11": "2024-06-07T09:15:31.418Z", "8.0.0-alpha.12": "2024-07-26T17:33:28.175Z", "7.25.7": "2024-10-02T15:14:48.225Z", "7.25.9": "2024-10-22T15:21:06.992Z", "8.0.0-alpha.13": "2024-10-25T13:54:10.128Z", "8.0.0-alpha.14": "2024-12-06T16:54:01.017Z", "8.0.0-alpha.15": "2025-01-10T17:24:26.919Z", "8.0.0-alpha.16": "2025-02-14T11:59:02.632Z", "8.0.0-alpha.17": "2025-03-11T18:24:54.142Z", "7.27.1": "2025-04-30T15:08:52.436Z", "8.0.0-beta.0": "2025-05-30T15:51:04.439Z", "7.27.7": "2025-06-26T14:03:17.719Z", "8.0.0-beta.1": "2025-07-02T09:04:17.002Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-parameters"}, "description": "Compile ES2015 default and rest parameters to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}