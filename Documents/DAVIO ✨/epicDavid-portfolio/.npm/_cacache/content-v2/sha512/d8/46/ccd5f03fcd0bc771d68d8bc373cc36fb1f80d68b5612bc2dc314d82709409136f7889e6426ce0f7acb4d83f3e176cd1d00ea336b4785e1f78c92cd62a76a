{"_id": "@csstools/css-color-parser", "_rev": "38-49016ee8bef47f43dbac83cc86095269", "name": "@csstools/css-color-parser", "dist-tags": {"latest": "3.0.10"}, "versions": {"1.0.0": {"name": "@csstools/css-color-parser", "version": "1.0.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a0e88b34ec8b8cce75f65fbadf9b784a2daa39e5", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.0.0.tgz", "fileCount": 39, "integrity": "sha512-u3JrK+pQIGGnXe+YhohWwAwOum2y25NRdEjRQFD3moMnOJgmU/nj8BPAF6DDQAooy8Ty9RNKiAh2njuqwMgUNQ==", "signatures": [{"sig": "MEUCIQDJfi0UrkWSjBrj5RK3JXcHxYGcJXGO+6e8GdYYGJikWQIgAhFKWqqJwjFuSlJYb0YkvxxR8j/7afHrkhbNEZ29lbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHqlZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1AxAAlBmjpdNQQx2Rhc1xXQFms/WdBz9alikgySQ1ezhjYmXK/P1U\r\nUuPknT/oWlwTdAp3WvbaNh+jYd34SUTAQUNkfc+INpbHqo3bkcmoCFL0yipZ\r\n9RufuwjKrOZUh7CEuZPz8My9uhto2POQsnF2OUjhlu3DQe64Y46qKJL6Lj05\r\nc4T/onR+3QiHIQayCAQH/2KR7J54yCWz4GY1IdKK4Cc9VccHI2asat/8Z5nh\r\n46j0rXbxDcQQBtXOaKUELY9M2rqhT7aB0T/LhlRhpWt6aIpKpsE5140fO/D4\r\ndwQ+gm7IsbQFe5pksvsDvSGn2fa6CcIdIbS9KecMZ7amFlpdlesOG8zGRzCv\r\ngUI8M3iS823yBmXAiZhF287ibBgpjagR1/0UsePPur/7HvDwDd9bUsUZCcQq\r\nJNCawSbtVUrNDHgoYjTUU5Gts6ZfBMqBay8VS3sjVl6PylthFCgIMXISvbZH\r\nqNe9tAWIaGRvh69maLqq6o+1Ow+HUYuyVGsUvxQBpwaCy2MYvzsMu/Leeoma\r\nONzS+UTmpl5Bn8QWERyfaAW11bt0VdWdJTgVuIz1ktQ9/ZrSr+CcaYpnr9jF\r\nLTQNTe689UX1cwCl+4X7rzPQBuvp3QQ2Y46IwJ2tZdDeqRbxKVPV1THbXQ/l\r\nwZIHNeQwsA27FJ1+KuReyDm8Zd6SZ/b58O0=\r\n=XWhR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "3517f3837c9efb00a29952e112105082901e0e2f", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.4.1", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@csstools/css-calc": "^1.0.1", "@csstools/color-helpers": "^2.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.0", "@csstools/css-parser-algorithms": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.0.0_1679731033643_0.6377760727223072", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@csstools/css-color-parser", "version": "1.1.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.1.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "717d544aa5c5f77e3fb71e77f1500338c1ee7436", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.1.0.tgz", "fileCount": 39, "integrity": "sha512-jRpIhjThaH8jxuJ8Q1H+jai/dekP5952kzLHTuN+rPI48eF2esf/18TMb3N/HtEgmnybhfiwUO6Ph2OkHi3jpA==", "signatures": [{"sig": "MEUCIQCBcymUVjspfSBqMiqvCFb74LaEKpq51D14mcpSHcxakAIgNlZ3jhmymNk73SX7CCI5BzWQjYkRuoPuo3hor974n1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkItFaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdeA//WGc2Aoo74YhPXU/VHIOPdv/jEuRtigl8lKpaaEoyDni9Fdhf\r\ne6Ygu1EOyXy4FkUTS47VKLNs+mesTc54sclT68tAFJ8QXnzilYM7JK4LP69S\r\nLTU+XX4Cv6dY9W2yn2h4TOyNyZJ9oepYj9XBxqqWNHOo0tDENsvHuaZxTI6Q\r\nuSu5gNHEP+hRBWTzTbOkg+2ye591EEWhrj2nYB18xvQjg+2hq6p9ipDNqPJl\r\nmYrkwRLzTmkIYuW9uILxTyU5aSFdWz5RBHxDPYLkjgeSsuK9NEwGyznbRzwg\r\n1c20Y6HKEWBtIm4cDR9Rg1twgeHEUD0043V4sdiI4ozo7d4M50IkgnGCF4ft\r\nQl9iCLzfWH7nemqnDhnUjtuKkdpp5GsI815tEy3AI5VllUqF7ZW45Yj1h6va\r\nPUrB5cB7ox0KYJVLEcg0wbpy2OPD5wD8SiH3S76MzBDxMtI3mNepwjl92PVp\r\nTZ3XolR0hTRsUP2ElqF0cK0Z5VbMRI2jlDpZP2LyA9omALQoukEkgRIhXzMU\r\nHtNlnJrfJFX63jHHkKk5kUdu5iK0tX1cCtAIxN5tqUt6UUL5WhXeEKledr6+\r\nC81gwV3BroIUEj6AqDnUXrWiuZg1wEGVTT9LrOHUlF0BSH4g8YT9BGYtVpRB\r\n8Xzyrnvcml034pDOvCu9VywzitqTEd5cmo4=\r\n=ryko\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "2b409f48abb21a9d27c9d5aa70fcea27d3d8095a", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.4.1", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@csstools/css-calc": "^1.0.1", "@csstools/color-helpers": "^2.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.0", "@csstools/css-parser-algorithms": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.1.0_1680003417820_0.44992544611364105", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@csstools/css-color-parser", "version": "1.1.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.1.1", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "9462939fd2f4b484e677f7c04eb41c720c6120a4", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.1.1.tgz", "fileCount": 39, "integrity": "sha512-PahxBaci2HZ2ue2eqaIS6GdJHU7KZhw34pKvH0sqcUI3Wo5pbXtLAJdsVsHMDc2NfSMnYqgSDI4tYaPhjrC8NA==", "signatures": [{"sig": "MEUCIB5EZMaIY/n2aOc+QZCMCQTH5SEkvlVF6Q3abY/yVF/FAiEAjrPwY4X5GLeKyIcH0U8/CSzXjt2fMS9xS48zvyO4MZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM79oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM/Q//eJ6XHqHYg48gZCSMSgoFxix/Ljp715+GtLad2yxHjo9c5qYK\r\nuEtUvYxlG6vSHzWq3fx0uZlH4PZqayrQVTMhCVlXU3QZ6C/gLHz2PEvKqi6G\r\nwNWiKnHCtoui8NEfCnMbdpyXC/wZ0sQfgyItX5kuZyFQqNkq6yC2RkbGU5PU\r\ngIea71Z87ftBx8FyuIkZJ/zt4LtYsUQS4doRxSLZEI+A789KRCiIqngMWkfJ\r\ndO/g8qLYwDcE6jyKbih7Z2dRYBjzx1aE1Zq9UjszeM74VFcSWIG+F08cGQWQ\r\nrxQBQv9KZFUEeKBZ+5Py4hLodliciLUiyzSijtOFramE2Pu0mh5twzHiGo2B\r\ny0lV2lryxW1C9kuOjP9L5iJpZ2pUYvPAob8AzpbMCFr1bc3BGFhn9espFQ2K\r\nCojeb+zJ/rISsR79qxPPCs6xnkQ7r/XCI+TlEo0Ou+H/7tcwUSAWe4HFcUMg\r\nTptyELSPr7lCaYfxZC/DbDTR8+TPjTXVfkJVKMks8W335sY2hgx0fxEux5ew\r\np2BvpSdPJNM9M3er6pYdti9HjCOWKMcQdhMY1p9fZe/n7FxbYU+E+4R/jgvp\r\neGOZ7W9kSbqQw6sSdazQo4kNmhCn1NahTQ6wHrF0SY80//S5sUK2yHlPhF08\r\nZyuUwqi8cUEkRbEgI5ig9woem1N4iVr0RIU=\r\n=LH/H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "95e5ae1b54125b4c2f5cfa5b03b5d2a7e29f0cb3", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.4.1", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@csstools/css-calc": "^1.1.0", "@csstools/color-helpers": "^2.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.0", "@csstools/css-parser-algorithms": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.1.1_1681112936200_0.896858308267618", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@csstools/css-color-parser", "version": "1.1.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.1.2", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "e5956c0fe9c30d9f228b0e37173ff61f0dd89dad", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.1.2.tgz", "fileCount": 39, "integrity": "sha512-MjW/VspbFSkvbuou7tUUu2+FAlAR7VJ/PA69M9EGKltThbONC8nyW33wHRzNvLzRLGstZLEO5X5oR7IMhMDi0A==", "signatures": [{"sig": "MEQCIE8tUZosW8xEhsDZVophXzCkc59UQN3nXi4O1yoV6OBaAiBsLW7DkoTbO4qyM1Hp0aL5w2pZbxKkFZhzaPLLeQww6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM+0OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBsA//RfcAA9lTZz+ZmTh3SYhJ/vugunftaiPR3IxMvzsEG3i7t8D0\r\nW0QatpQfotffOE+CuIcp4QvBHb8ZAj9S3xDQeuFkY9m2/DukwMWcS9c64tJx\r\npWdhXaaG0R8WP26isq0ZkZiwaBA6++Hh992O5tFU7xXE5jSxB60HzRE50PUV\r\nHJ+HjPoOe7tsR2y+zH3wT4ZfFP53OLBAHTCeO6+fKyNJtyB78gTLPxPh7ki8\r\nL9Fd94iReT/7oPyvJigpM1OVKR6WR3gM0cMHD90n13RrGBYjyJGkqYWsr8Xd\r\ndbbcSIOkrQxf19dbA7Qv/3V0oIClBbZ4ndHbZ7E5IgzGO+eJBtqfAVZoyFKe\r\n36+QKDVG7SpSzPKQ1fVz9V4N7d9M6JJboNTAGO7YS5vGz4pCg5ei625pY3BH\r\nUyA0cet/5WzMn01St34YnCNdTYpvBu3gOHFPAth/km3zByuRrGqOl/qV5KC7\r\njTQuzLKy7747OiN8URp7Yhr2XA9xN/Y26NwEotgcoivDv1fw6dCdEfbVgFKr\r\nI5OXOGQ1esKpLunhxITiUJm21gIAEDKkVOCp0HKClyFSobH4GpaqY8A2halD\r\n91JkR731teC25Z9eVNYVzyDOLuy40+eCsJmwljWLqTeZxWw9mH9AV4QZGRKn\r\nR6pgNlCUkXauDivQ05fVRqxFGLy4OSSrsyY=\r\n=KA9K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "15de42e2631e2c032b0a940652c7d62ce2fd955d", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.4.1", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@csstools/css-calc": "^1.1.1", "@csstools/color-helpers": "^2.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.1.2_1681124622715_0.5956696131798609", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@csstools/css-color-parser", "version": "1.2.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.2.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "30243b2fe912e1da0787e7d093d25a9ed40a73b5", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.2.0.tgz", "fileCount": 41, "integrity": "sha512-kt9jhqyL/Ig/Tsf1cY+iygxs2nu3/D532048G9BSeg9YjlpZxbor6I+nvgMNB1A1ppL+i15Mb/yyDHYMQmgBtQ==", "signatures": [{"sig": "MEUCIQDBs4ejr7H0q0Dt7mL5mx3g3E6dX08amQRUPfsZgkPzKgIgYQUdWz4YHmKtdfutuo41S5XUHZlFFybMUT9kM/RqXh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95975}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "334232058e3927b4a63037899e664aa291605cf3", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.5.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"@csstools/css-calc": "^1.1.1", "@csstools/color-helpers": "^2.1.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.2.0_1684511494730_0.6659677169503078", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@csstools/css-color-parser", "version": "1.2.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.2.1", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "96d4bc4751d0f10e5156b8c15c3eb144d3365883", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.2.1.tgz", "fileCount": 41, "integrity": "sha512-NcmaoJIEycIH0HnzZRrwRcBljPh1AWcXl4CNL8MAD3+Zy8XyIpdTtTMaY/phnLHHIYkyjaoSTdxAecss6+PCcg==", "signatures": [{"sig": "MEQCIH6L3Pj/4C5Jz8cV++xY7V8d2BE/ygzpCNHK8iGcr7VlAiAyj5GhSFJsWO63badPh+gKSxKTYwBwck6/GixgtW0sPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95876}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "b4220ea8abba1943860e644a8077f143049c789b", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.5.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"@csstools/css-calc": "^1.1.1", "@csstools/color-helpers": "^2.1.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.2.1_1686735041423_0.5051339232297445", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@csstools/css-color-parser", "version": "1.2.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.2.2", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "730e69eb72efdcfd644f8b14fcdb816b69a4c290", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.2.2.tgz", "fileCount": 41, "integrity": "sha512-okEA/PWwtUn/7Koy0QoDs85jGOO0293kDyYdVoLgpwt2QmMJECYZotxVjRZ5SdReVGPwecUyeHeViw1uLewcpA==", "signatures": [{"sig": "MEQCIGUgeGE+xdsjnNhexBL/57wWUVKmDMkeDfxEN090vt8jAiArgKA0QlgoROntjEYGfJTbQfEUiPMYJJMF0i9whUIAhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96297}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "d5c5e3b78e7e1ea9f95e58b0b10e406d30b7ea0b", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.5.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"@csstools/css-calc": "^1.1.2", "@csstools/color-helpers": "^3.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.2.2_1688373056858_0.7946650697424715", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "@csstools/css-color-parser", "version": "1.2.3", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.2.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "0cd0f72c50894a623ae09f19e30bbfb298769f59", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.2.3.tgz", "fileCount": 41, "integrity": "sha512-YaEnCoPTdhE4lPQFH3dU4IEk8S+yCnxS88wMv45JzlnMfZp57hpqA6qf2gX8uv7IJTJ/43u6pTQmhy7hCjlz7g==", "signatures": [{"sig": "MEQCIGP+ohsT1JyhMAZtjLu6Ham/aYOqkSpENOxOlwgzjN8NAiBJTIYqz9OvJSOY2GrykbuQuafz5D4xla5WNJaIYVR6hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99052}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "8a31614cd2e828445fe34df75b68d80f1996268e", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.6.7", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"@csstools/css-calc": "^1.1.3", "@csstools/color-helpers": "^3.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.0", "@csstools/css-parser-algorithms": "^2.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.2.3_1690215690905_0.4437685028909675", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@csstools/css-color-parser", "version": "1.3.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.3.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "e360fa8abbb64556475caf55137338cfe4ba6752", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.3.0.tgz", "fileCount": 43, "integrity": "sha512-jgudbE+TXZLssSTGFRCkJF9gAM8ABZ2c9/gbLupwA8Y1SpcddxK2z74/MOSdWuboUHbshei8uSQNbp9Wu1Bx+Q==", "signatures": [{"sig": "MEYCIQDiJC8/PwqdJonLz2CXjbFktbDnxWmsM8H8KtQOZ4OdxwIhAM765G8VYqM14TX193i+QaOuudW57MDcToM8Zu/qYS5a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98119}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "11793e4e49074ced9cd0056b98ad5920854feadf", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.8.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"@csstools/css-calc": "^1.1.3", "@csstools/color-helpers": "^3.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.0", "@csstools/css-parser-algorithms": "^2.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.3.0_1693221338306_0.3211510898731915", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@csstools/css-color-parser", "version": "1.3.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.3.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "ab37d833e65595c8dae05c6ec44aeb87f210b76b", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.3.1.tgz", "fileCount": 43, "integrity": "sha512-cehc/DQCyb4hL4fspvyL7WiY+uAy8Iuaz0yTyndC/AyBmxkNpgtSgCSsr0aR4vkaSFVZfNNVlKbjHFwOsPGB1Q==", "signatures": [{"sig": "MEQCIGFsTgI5/qPfmC850p5x0lm1Qi9GvaiyLfT0Ggwh3fGFAiApnP18BpWynSLG6pqBs6Gt7mnLm/4jDTIVU/FBeM8tYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96628}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "9ce68b51639947f054ac8f00e0ae0f53518e5e96", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.8.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"@csstools/css-calc": "^1.1.3", "@csstools/color-helpers": "^3.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.0", "@csstools/css-parser-algorithms": "^2.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.3.1_1693674468107_0.582916701809506", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "@csstools/css-color-parser", "version": "1.3.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.3.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "ec6ac35e24a34e1f37eb3d366a2ea637bcc7c7e5", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.3.2.tgz", "fileCount": 43, "integrity": "sha512-YLCWI+nm18qr5nj7QhRMGuIi4ddFe0SKEtPQliLf1+pmyHFxoHYd0+Hg+bRnbnVbdyCTTlCqBiUvCeNJfd903g==", "signatures": [{"sig": "MEUCIQDm0bTuXyj5+PqaP4SvXX9OuUNNtR86kWbDbvFG2psdAAIgFzLVUltWdEq/QQo3K+w8bwcL9ajp83zoQWY3bgrM5SE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97386}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "65d66e99e95fd60d0e042590470c4d087c80b319", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.8.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"@csstools/css-calc": "^1.1.4", "@csstools/color-helpers": "^3.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.1", "@csstools/css-parser-algorithms": "^2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.3.2_1695584717604_0.9682423190516596", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "@csstools/css-color-parser", "version": "1.3.3", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.3.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "ccae33e97f196cd97b0e471b89b04735f27c9e80", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.3.3.tgz", "fileCount": 43, "integrity": "sha512-8GHvh0jopx++NLfYg6e7Bb1snI+CrGdHxUdzjX6zERyjCRsL53dX0ZqE5i4z7thAHCaLRlQrAMIWgNI0EQkx7w==", "signatures": [{"sig": "MEQCIBOVc2mAYLHbYr/Dbn5mEdgkqaYLRAWNaHx63j+tHQg4AiALML+GuFcaF3gxkq46p0I30JlHp5h8J7YlVBmRhtC4gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98426}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "2bc53ce2c764974f4b5a1aa25c94b2075d264e83", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.8.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"@csstools/css-calc": "^1.1.4", "@csstools/color-helpers": "^3.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.1", "@csstools/css-parser-algorithms": "^2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.3.3_1696281553386_0.07375860596022132", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@csstools/css-color-parser", "version": "1.4.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.4.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "c8517457dcb6ad080848b1583aa029ab61221ce8", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.4.0.tgz", "fileCount": 43, "integrity": "sha512-SlGd8E6ron24JYQPQAIzu5tvmWi1H4sDKTdA7UDnwF45oJv7AVESbOlOO1YjfBhrQFuvLWUgKiOY9DwGoAxwTA==", "signatures": [{"sig": "MEQCIAjqoQ6CWESw8mh39rm55s4cvFFmMscTqAqPC0yUfLc/AiBglUIkn+X8FMa+El6BJLfCL5AANBM9sMB+sBsCYrvVXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99208}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "07b70206d2d53fb159751589bb8ee99f6ac1877b", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "9.8.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"@csstools/css-calc": "^1.1.4", "@csstools/color-helpers": "^3.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.1", "@csstools/css-parser-algorithms": "^2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.4.0_1696872697044_0.21789324259289766", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@csstools/css-color-parser", "version": "1.5.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.5.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "545fe1586d4927cc2614d62278325d37d8539b35", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.5.0.tgz", "fileCount": 7, "integrity": "sha512-PUhSg1MgU2sjYhA6moOmxYesqVqYTJwcVw12boTNbDX7Af+VK02MkgvmBBY2Z2qU6UN5HOQ+wrF0qQJGsTFY7w==", "signatures": [{"sig": "MEYCIQC59iJaQ0+sCzsqvgG2Jfis6mJ0phpOztZlMI0HzW6lmQIhAK9SsCHDWN9KOCjrKN0nYEotGbVCL/wQv+CmJE03o4Sn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94282}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "bfff9ee5e174630b9717ca45933ed869845845e8", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.3", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@csstools/css-calc": "^1.1.5", "@csstools/color-helpers": "^4.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.2", "@csstools/css-parser-algorithms": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.5.0_1702682652554_0.6698313983552722", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@csstools/css-color-parser", "version": "1.5.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.5.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "bddf5513a7327c511c9e1ec682419dcad1f91869", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.5.1.tgz", "fileCount": 7, "integrity": "sha512-x+SajGB2paGrTjPOUorGi8iCztF008YMKXTn+XzGVDBEIVJ/W1121pPerpneJYGOe1m6zWLPLnzOPaznmQxKFw==", "signatures": [{"sig": "MEQCIEj3V2jrN1vl94XTxH6JawOEOFcdCdRadbLB22OVuBFzAiBdq+xohMowcgsvXopTjIiU8aJjio2brdgMLyy9VKDc9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94876}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "582d489723c0470a031a107f45852535fa096540", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.3", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@csstools/css-calc": "^1.1.6", "@csstools/color-helpers": "^4.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3", "@csstools/css-parser-algorithms": "^2.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.5.1_1704040373604_0.9124957910009084", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@csstools/css-color-parser", "version": "1.5.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.5.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "4fdf8e23960b4724913f7cbfd4f413eb8f35724b", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.5.2.tgz", "fileCount": 7, "integrity": "sha512-5GEkuuUxD5dael3xoWjyf7gAPAi4pwm8X8JW/nUMhxntGY4Wo4Lp7vKlex4V5ZgTfAoov14rZFsZyOantdTatg==", "signatures": [{"sig": "MEUCIQDCS6JeU+ea3l4LQW95OR7mT+umm75HIwR2sM7QQrIJ7AIgCmgy14oZbhNOxORbpSaI34WgJSJevgVrj0Mt6zJ/9Io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96281}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "2f2271e1bea11998270b9ff0e9165cb37886f95f", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.3", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@csstools/css-calc": "^1.1.7", "@csstools/color-helpers": "^4.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3", "@csstools/css-parser-algorithms": "^2.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.5.2_1708330181922_0.8665053902446183", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@csstools/css-color-parser", "version": "1.6.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.6.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "03b9fd3bfde91c452856f8b222539a4b26f40999", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.6.0.tgz", "fileCount": 7, "integrity": "sha512-Wc1X6jZvGhT8Bii4jUF6tC3Je3wgDFg7D/SvGKndrnakDsCPk4TMxtt4AQHyWdMBrBJ1hLjXbppaXgP1DUIpBw==", "signatures": [{"sig": "MEQCIE3a14CtWTIloywV9UFiXPUuBvlN7hk43Qt7k6CDsifZAiAQWqPxpqGC0IipyFMPzmwz3Aj5VL9R2dik24pqU5vdwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97943}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "232d5c493b8049d52bbef2d7ebb017156a36b2d3", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.4", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@csstools/css-calc": "^1.2.0", "@csstools/color-helpers": "^4.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.6.0_1710356235789_0.9374441629739836", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@csstools/css-color-parser", "version": "1.6.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.6.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "24ceca6776542afa3c471200b422e562613191a4", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.6.1.tgz", "fileCount": 7, "integrity": "sha512-1HiHqZVJEjL+vuS/4VyUyZPTeWCkgJw/mqbtry6+ONQU88IL+5GswPJYvlKwtng8G1S69v2Yyu9N8zy52tz7DQ==", "signatures": [{"sig": "MEUCICUATZpbSLtrCYk+dNJpYp/RQvwAVT8TCBy6iuFJjBJcAiEAscGSIJmAOMeXR7DmStNl2SrvrNVRidnBx65enofrlc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97338}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "9d34ea961ee74c209260b72e68f15cb3145b7ff3", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.4", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@csstools/css-calc": "^1.2.0", "@csstools/color-helpers": "^4.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.6.1_1710596614926_0.28482993138461477", "host": "s3://npm-registry-packages"}}, "1.6.2": {"name": "@csstools/css-color-parser", "version": "1.6.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.6.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "d5ce6f7704886daf3b9fe359b89986711822b4fe", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.6.2.tgz", "fileCount": 7, "integrity": "sha512-mlt0PomBlDXMGcbPAqCG36Fw35LZTtaSgCQCHEs4k8QTv1cUKe0rJDlFSJMHtqrgQiLC7LAAS9+s9kKQp2ou/Q==", "signatures": [{"sig": "MEUCIDhb5c0DZAWzquPvfKhVAc59k8uUl8XWoJlVbxrn31HlAiEAvP3WxoU3ork9wQT31MGOc3FC2Cm8RYDr894zHDcUmns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96963}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "00f70308ce8d9ca2b949231db458310650328dc1", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.4", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@csstools/css-calc": "^1.2.0", "@csstools/color-helpers": "^4.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.6.2_1710598121127_0.06663014246263632", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "@csstools/css-color-parser", "version": "1.6.3", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@1.6.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "813948662e3010672990f2366b94f6174ddba285", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-1.6.3.tgz", "fileCount": 7, "integrity": "sha512-pQPUPo32HW3/NuZxrwr3VJHE+vGqSTVI5gK4jGbuJ7eOFUrsTmZikXcVdInCVWOvuxK5xbCzwDWoTlZUCAKN+A==", "signatures": [{"sig": "MEUCIGGekMOZo4XAbr7/YBZI8GMq9zFQh9TcsFJc+XsaPizzAiEArlvvE70ngGGn8ggwoLFj3aNEZ+ZpiDdPx2c9WyHwAKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97057}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "7bdf11d54a4f0d9551fd5d96560f2a8b9c1b4b78", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.4", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@csstools/css-calc": "^1.2.0", "@csstools/color-helpers": "^4.1.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_1.6.3_1711891110847_0.08469879969601646", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@csstools/css-color-parser", "version": "2.0.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@2.0.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "8e75d1b4a857317f537b3c0a223be0ef1735bbdb", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-0/v6OPpcg+b8TJT2N1Rcp0oH5xEvVOU5K2qDkaR3IMHNXuJ7XfVCQLINt3Cuj8mr54DbilEoZ9uvAmHBoZ//Fw==", "signatures": [{"sig": "MEUCIQDbQJ6MJSeZ+3st5CVZOhJAZo200apCpLbIfx9MyaJsFwIgKpxix4ndG1V0IT+4udul6YKM7ptmGBrbxJMWuBbh+Wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97778}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "9de801f028233822a6a6f4b5f023eb3507e28176", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.2.4", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@csstools/css-calc": "^1.2.0", "@csstools/color-helpers": "^4.2.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_2.0.0_1713709607388_0.7560516368025265", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@csstools/css-color-parser", "version": "2.0.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@2.0.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "e506ebe0a610fbb98a78b4f53d463163e0d569e9", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-k8X1L7i70Om2EqabY0sG56Qoi8UJutSFMeRvX0zp06u2ir9EHTdJS4/lYSbo/HCZ5VP5jy87aKAMi+hru/RL1w==", "signatures": [{"sig": "MEUCIEfPkZniJNUbMkOD9o+mpp4swQsX2IdC6JIGZd18pYP2AiEAguuFkjKMgEV/sQ1pALpmLj/GUJigFxexxrS7TA3y2as=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97746}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "77cb560bd0c4d08ddeeb99ac80db3d08f67a0b85", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.5.1", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.0.0", "dependencies": {"@csstools/css-calc": "^1.2.1", "@csstools/color-helpers": "^4.2.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.3.0", "@csstools/css-parser-algorithms": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_2.0.1_1714838871216_0.7491540818611375", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@csstools/css-color-parser", "version": "2.0.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@2.0.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "bf60de403889a2726f964a1c9b1ea5593e889f5b", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-Agx2YmxTcZ7TfB7KNZQ+iekaxbWSdblvtA35aTwE3KfuYyjOlCg3P4KGGdQF/cjm1pHWVSBo5duF/BRfZ8s07A==", "signatures": [{"sig": "MEUCIQClU3ta7y1+si3+QaunmO9j4v3NH/7ZOg0+h+oxcKQPQQIgFCAxBcxdQH7Z9cyZvMGNAGpJ02uKd9FP6qHPjlYi9UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98069}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "bfc08bc8c20929c363eb88091d1dcdbcd8ab4b25", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.5.1", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.0.0", "dependencies": {"@csstools/css-calc": "^1.2.2", "@csstools/color-helpers": "^4.2.0"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.3.1", "@csstools/css-parser-algorithms": "^2.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_2.0.2_1714857305253_0.6150699629818082", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@csstools/css-color-parser", "version": "2.0.3", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@2.0.3", "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "6bec8b780e6792affdd19aa3986096ec8a6a6ffd", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-Qqhb5I/gEh1wI4brf6Kmy0Xn4J1IqO8OTDKWGRsBYtL4bGkHcV9i0XI2Mmo/UYFtSRoXW/RmKTcMh6sCI433Cw==", "signatures": [{"sig": "MEQCIE37vk6PWsUw13DtaVnQbG8z5PINhpQycxndxKSm2AOqAiAK10Bkv63uBjWPOI6DzsafwRZBR8pwS89fVyoV1gef9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98492}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "bda50e09d42678a869e541ab18e7050370236406", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^1.2.3", "@csstools/color-helpers": "^4.2.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.3.2", "@csstools/css-parser-algorithms": "^2.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_2.0.3_1719698368738_0.7451909772864937", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@csstools/css-color-parser", "version": "2.0.4", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@2.0.4", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "e2d9597add2718b5e91514247e35d6fe216a1798", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-2.0.4.tgz", "fileCount": 7, "integrity": "sha512-yUb0mk/k2yVNcQvRmd9uikpu6D0aamFJGgU++5d0lng6ucaJkhKyhDCQCj9rVuQYntvFQKqyU6UfTPQWU2UkXQ==", "signatures": [{"sig": "MEUCIQDq15rhszGT5FH3OOmwQFjtPNliL/nRt7fqXcvJD3cbvQIgfuyGQUCxXTvsv+QvdErc/TahW1/b6ZlxPJfe9w4EdaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98430}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "41730892735564de938ddd305dd8fefb894784c4", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^1.2.4", "@csstools/color-helpers": "^4.2.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.4.1", "@csstools/css-parser-algorithms": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_2.0.4_1720258684823_0.9946034631114711", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@csstools/css-color-parser", "version": "2.0.5", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@2.0.5", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "ce1fe52f23f35f37bea2cf61ac865115aa17880a", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-2.0.5.tgz", "fileCount": 7, "integrity": "sha512-lRZSmtl+DSjok3u9hTWpmkxFZnz7stkbZxzKc08aDUsdrWwhSgWo8yq9rq9DaFUtbAyAq2xnH92fj01S+pwIww==", "signatures": [{"sig": "MEUCIGz5qZsnrdwqCAlxrCsfDCSn4c0PPtBGLSm7UNn6ONtAAiEApxtmBBuHZiLiGcoxi8Gcu4K4QBGp1q8zE9cuymkpxXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97782}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "d14262dea738e99c5c1c86b70ba491d4723c1a1c", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^1.2.4", "@csstools/color-helpers": "^4.2.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.4.1", "@csstools/css-parser-algorithms": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_2.0.5_1720860571861_0.4740698040007656", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@csstools/css-color-parser", "version": "3.0.0", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a5c56b3efe5b129792b28f54c23b7679fe9a23e4", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-F/A1Z3ZXH4fU6+29Up4QAZtewLmWLI4hVz6hyODMFvorx4AEC/03tu+gFq0nMZSDafC0lmapNOj9f4ctHMNaqQ==", "signatures": [{"sig": "MEQCIAbomMXZUnjiJ1/9OiWpglkSOi6Ts+OA/IHG1lVY2bUfAiBefc6P1Ih0qIMz9rvRWfgoAK2clx7z6EEAxJ8x9t0ybg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96766}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "149046bf2a7dfdd1e1b1d0fb85baa07b2fcd3c06", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^2.0.0", "@csstools/color-helpers": "^4.2.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.0", "@csstools/css-parser-algorithms": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.0_1722721336544_0.7959887803735486", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "@csstools/css-color-parser", "version": "3.0.1", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "d666b0238e6e49ee9de2b27b63386f8cedd3ed6d", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-++7I+Z7S/BWedPlR4z8aW1zsvtJFufFbpdPwdx5+W50dq5EYLV3sulitSNMry0BNmNMzeczdQij/f4C+ch01vQ==", "signatures": [{"sig": "MEYCIQDzkV3i/we/Hn++MNk3bT4uWNcmh8weW+zUBTtIkqO/JwIhAJFJgiWbc4fH3vS1I+NRBxJ0kVXx5A0QwTtsYfU2OdhY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96252}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "1023eaffc562a9002c95e5433d77d9148e7e66be", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^2.0.0", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.0", "@csstools/css-parser-algorithms": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.1_1723638298772_0.8606869219456474", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "@csstools/css-color-parser", "version": "3.0.2", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "710abb97142d58bcefc3a5e032a55a246895351c", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-mNg7A6HnNjlm0we/pDS9dUafOuBxcanN0TBhEGeIk6zZincuk0+mAbnBqfVs29NlvWHZ8diwTG6g5FeU8246sA==", "signatures": [{"sig": "MEUCIQCjK4ut8ncTR2ZuTka1kRzTjKaKbvZjyhqJIrw3D0FrDAIgfL75/E58aNeZ+7gLQ6LgJrx2F8W36Oa35L9vHM2IXro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96869}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "f580ac3fe4f0dc15e341f43af70d04cc5d67f01c", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^2.0.1", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.1", "@csstools/css-parser-algorithms": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.2_1723996245272_0.1810794132110145", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "@csstools/css-color-parser", "version": "3.0.3", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a4ddf82aeafc452443851a3f108ecae52533e25f", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-mnOTQ6KbQ6GHfdVHVTNXffroW0r5P5531h73bIyEzWAScGjMPQi+1XYgAydYVaZiKeXlQ4GHG9dnBWq9h7xFIQ==", "signatures": [{"sig": "MEUCIERXUlFsDDiws3dlrVzqIUqQGtZzoLEBNaQ5wbIKvfE3AiEA9JgxP5+S+uXSrXQeOU+WmnFatELvGoafbc5G76x5vkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96870}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "00f4e2871054b7c4648a22a06b53d2d6e9fb6b97", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^2.0.2", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2", "@csstools/css-parser-algorithms": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.3_1728563260946_0.9892329015549062", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "@csstools/css-color-parser", "version": "3.0.4", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.4", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a7ac2ab53de48258beb78f83b26c77da2e4cc603", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.4.tgz", "fileCount": 7, "integrity": "sha512-kXviLfsxXmx2YcUPd478vuJd/s21EFTmxcgjC3danRhLa2zqfqZMTRonwRRSckezmgn7nlOCXpk3tZAKbFeihQ==", "signatures": [{"sig": "MEYCIQDW5EI7+qVjQpnfOLNda2gPybqeJUAUAosuogvz5S+/UAIhAK8i1JP1W+1qFPA+WvhEixEFfgeWvRcuj/gx4D2nDLXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96638}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "969c72f665e1707cb6e361326e157fb4adff940f", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^2.0.3", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2", "@csstools/css-parser-algorithms": "^3.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.4_1729720106729_0.20552807890232971", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "@csstools/css-color-parser", "version": "3.0.5", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.5", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "0dc6c8757ad913c60f6e871ab907fbb22de30e52", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.5.tgz", "fileCount": 7, "integrity": "sha512-4Wo8raj9YF3PnZ5iGrAl+BSsk2MYBOEUS/X4k1HL9mInhyCVftEG02MywdvelXlwZGUF2XTQ0qj9Jd398mhqrw==", "signatures": [{"sig": "MEUCIQCjuzvc0AcuxfrsuLdbYk6ocdvXuCWGdaIdEmo0Yd/oOwIgYlJx4irC1jCHzNmc57pMimL+sr1cRkJdvxshhegr7YQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96870}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "6b36fe89375c8a90971fabae0dad5282ab54eed0", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.7.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"@csstools/css-calc": "^2.0.4", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.5_1730497749743_0.9206009482572803", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "@csstools/css-color-parser", "version": "3.0.6", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.6", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "e646838f6aab4618aeea7ba0c4921a254e180276", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.6.tgz", "fileCount": 7, "integrity": "sha512-S/IjXqTHdpI4EtzGoNCHfqraXF37x12ZZHA1Lk7zoT5pm2lMjFuqhX/89L7dqX4CcMacKK+6ZCs5TmEGb/+wKw==", "signatures": [{"sig": "MEUCIQDxMd7cUG+bNHdlv3ODkmz7Er2q0y5SlD93iyASJe6y+wIgLAVCPOrxMmxh0yL7IgFITX4JX1pUzdKCu+sxYqKt6RE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96383}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "3f9c3d55a7b2540f68d9f85fc7fba027113f75fd", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.9.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"@csstools/css-calc": "^2.1.0", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.6_1731322802758_0.1574667795346949", "host": "s3://npm-registry-packages"}}, "3.0.7": {"name": "@csstools/css-color-parser", "version": "3.0.7", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.7", "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "442d61d58e54ad258d52c309a787fceb33906484", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.7.tgz", "fileCount": 7, "integrity": "sha512-nkMp2mTICw32uE5NN+EsJ4f5N+IGFeCFu4bGpiKgb2Pq/7J/MpyLBeQ5ry4KKtRFZaYs6sTmcMYrSRIyj5DFKA==", "signatures": [{"sig": "MEUCIQDF8EkQ1hCwyuFkaqfJLEXNzHIJQSDgC5dXlYws/lWHEgIgdcXHnka6N0KJW8Xd1VPQt+6jyQsSZsWsZLs0mQRy7MM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96536}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "d022541b9643f6883bb7a775c65399885aca1641", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.9.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"@csstools/css-calc": "^2.1.1", "@csstools/color-helpers": "^5.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.7_1735321379273_0.5733529816304623", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@csstools/css-color-parser", "version": "3.0.8", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.8", "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "5fe9322920851450bf5e065c2b0e731b9e165394", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.8.tgz", "fileCount": 7, "integrity": "sha512-pdwotQjCCnRPuNi06jFuP68cykU1f3ZWExLe/8MQ1LOs8Xq+fTkYgd+2V8mWUWMrOn9iS2HftPVaMZDaXzGbhQ==", "signatures": [{"sig": "MEUCIANI+b1/XDb+ZP7YmpcEELbhah50BeS6nrMdwN64RAjUAiEAinE8Oi1m6iPgHuOkcv+l946Tli2/icV/MqzOd6HQ6Hg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 97192}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "1e7b98202406e4041a9dae1629ec9272138fd77f", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.9.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"@csstools/css-calc": "^2.1.2", "@csstools/color-helpers": "^5.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.8_1740330427210_0.19189411343220497", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@csstools/css-color-parser", "version": "3.0.9", "keywords": ["color", "css", "parser"], "license": "MIT", "_id": "@csstools/css-color-parser@3.0.9", "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "8d81b77d6f211495b5100ec4cad4c8828de49f6b", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.9.tgz", "fileCount": 7, "integrity": "sha512-wILs5Zk7BU86UArYBJTPy/FMPPKVKHMj1ycCEyf3VUptol0JNRLFU/BZsJ4aiIHJEbSLiizzRrw8Pc1uAEDrXw==", "signatures": [{"sig": "MEUCIE8tB6N5z88QdaEchLTMT5JGgtFUzvlV5vxD+bN/SaCYAiEA3JAVe7XVajkbsYWm4JSvilBfhCegAYJRvFdmLWvjev8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 96785}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "32a4a80c20a82d21073638bf1a5540b36f6c0de4", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-color-parser"}, "_npmVersion": "10.9.0", "description": "Parse CSS color values", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/color-helpers": "^5.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color-parser_3.0.9_1745079300423_0.5429839821932709", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.10": {"name": "@csstools/css-color-parser", "description": "Parse CSS color values", "version": "3.0.10", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "dependencies": {"@csstools/color-helpers": "^5.0.2", "@csstools/css-calc": "^2.1.4"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-color-parser"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "keywords": ["color", "css", "parser"], "_id": "@csstools/css-color-parser@3.0.10", "gitHead": "3892dacec1cd48d40a9c39a4208f1d17521b0277", "types": "./dist/index.d.ts", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-TiJ5Ajr6WRd1r8HSiwJvZBiJOqtH86aHpUjq5aEKWHiII2Qfjqd/HCWKPOW8EP4vcspXbHnXrwIDlu5savQipg==", "shasum": "79fc68864dd43c3b6782d2b3828bc0fa9d085c10", "tarball": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.10.tgz", "fileCount": 7, "unpackedSize": 100274, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEJY13dhlA5ZXEnwisEABVQz5hFoB6XWopSvubnvNaBLAiB+xRiSKn+vJtRwa2EMG1O+yR2hkC4DrHsIXNP6Fjpd9g=="}]}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/css-color-parser_3.0.10_1748343050295_0.39744889699957575"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-03-25T07:57:13.586Z", "modified": "2025-05-27T10:50:50.742Z", "1.0.0": "2023-03-25T07:57:13.779Z", "1.1.0": "2023-03-28T11:36:58.000Z", "1.1.1": "2023-04-10T07:48:56.401Z", "1.1.2": "2023-04-10T11:03:42.891Z", "1.2.0": "2023-05-19T15:51:35.196Z", "1.2.1": "2023-06-14T09:30:41.595Z", "1.2.2": "2023-07-03T08:30:57.011Z", "1.2.3": "2023-07-24T16:21:31.129Z", "1.3.0": "2023-08-28T11:15:38.487Z", "1.3.1": "2023-09-02T17:07:48.341Z", "1.3.2": "2023-09-24T19:45:17.828Z", "1.3.3": "2023-10-02T21:19:13.520Z", "1.4.0": "2023-10-09T17:31:37.323Z", "1.5.0": "2023-12-15T23:24:12.746Z", "1.5.1": "2023-12-31T16:32:53.814Z", "1.5.2": "2024-02-19T08:09:42.093Z", "1.6.0": "2024-03-13T18:57:15.940Z", "1.6.1": "2024-03-16T13:43:35.157Z", "1.6.2": "2024-03-16T14:08:41.345Z", "1.6.3": "2024-03-31T13:18:31.170Z", "2.0.0": "2024-04-21T14:26:47.555Z", "2.0.1": "2024-05-04T16:07:51.374Z", "2.0.2": "2024-05-04T21:15:05.439Z", "2.0.3": "2024-06-29T21:59:28.899Z", "2.0.4": "2024-07-06T09:38:04.973Z", "2.0.5": "2024-07-13T08:49:32.038Z", "3.0.0": "2024-08-03T21:42:16.722Z", "3.0.1": "2024-08-14T12:24:58.930Z", "3.0.2": "2024-08-18T15:50:45.417Z", "3.0.3": "2024-10-10T12:27:41.137Z", "3.0.4": "2024-10-23T21:48:26.941Z", "3.0.5": "2024-11-01T21:49:09.947Z", "3.0.6": "2024-11-11T11:00:02.932Z", "3.0.7": "2024-12-27T17:42:59.473Z", "3.0.8": "2025-02-23T17:07:07.417Z", "3.0.9": "2025-04-19T16:15:00.664Z", "3.0.10": "2025-05-27T10:50:50.480Z"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "license": "MIT", "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "keywords": ["color", "css", "parser"], "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-color-parser"}, "description": "Parse CSS color values", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# CSS Color Parser <img src=\"https://cssdb.org/images/css.svg\" alt=\"for CSS\" width=\"90\" height=\"90\" align=\"right\">\n\n[<img alt=\"npm version\" src=\"https://img.shields.io/npm/v/@csstools/css-color-parser.svg\" height=\"20\">][npm-url]\n[<img alt=\"Build Status\" src=\"https://github.com/csstools/postcss-plugins/actions/workflows/test.yml/badge.svg?branch=main\" height=\"20\">][cli-url]\n[<img alt=\"Discord\" src=\"https://shields.io/badge/Discord-5865F2?logo=discord&logoColor=white\">][discord]\n\n## Usage\n\nAdd [CSS Color Parser] to your project:\n\n```bash\nnpm install @csstools/css-color-parser @csstools/css-parser-algorithms @csstools/css-tokenizer --save-dev\n```\n\n```ts\nimport { color } from '@csstools/css-color-parser';\nimport { isFunctionNode, parseComponentValue } from '@csstools/css-parser-algorithms';\nimport { serializeRGB } from '@csstools/css-color-parser';\nimport { tokenize } from '@csstools/css-tokenizer';\n\n// color() expects a parsed component value.\nconst hwbComponentValue = parseComponentValue(tokenize({ css: 'hwb(10deg 10% 20%)' }));\nconst colorData = color(hwbComponentValue);\nif (colorData) {\n\tconsole.log(colorData);\n\n\t// serializeRGB() returns a component value.\n\tconst rgbComponentValue = serializeRGB(colorData);\n\tconsole.log(rgbComponentValue.toString());\n}\n```\n\n[cli-url]: https://github.com/csstools/postcss-plugins/actions/workflows/test.yml?query=workflow/test\n[discord]: https://discord.gg/bUadyRwkJS\n[npm-url]: https://www.npmjs.com/package/@csstools/css-color-parser\n\n[CSS Color Parser]: https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser\n", "readmeFilename": "README.md"}