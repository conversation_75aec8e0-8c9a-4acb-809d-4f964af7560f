{"_id": "@typescript-eslint/project-service", "_rev": "51-e5df13ffb5ad41a573f8b4627943d03f", "name": "@typescript-eslint/project-service", "dist-tags": {"latest": "8.35.1", "canary": "8.35.2-alpha.6"}, "versions": {"8.32.2-alpha.12": {"name": "@typescript-eslint/project-service", "version": "8.32.2-alpha.12", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.32.2-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "4affcea3d3d0e2f3a1b6c90b27e7705f075767ba", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.32.2-alpha.12.tgz", "fileCount": 12, "integrity": "sha512-nl7+raox8zEn4iSPK6uBkU5XkGJuOtom22vZNflOHE9nnTqp5sjI1YBV0q5fS4vXcbbXXJQt8oZmSkGT6DvvFw==", "signatures": [{"sig": "MEQCIQCEgdGAcSrGHn90yC/aBQTi7+4oMAmhNZkw72/Hwj3x6AIfUT20cppuljZax5xSIUvSpBH1NFjSdNSeB7L2oYJedw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.32.2-alpha.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14991}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d2ffec7988976a07495bde7270630db1a0fe1d32", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.32.2-alpha.12", "@typescript-eslint/tsconfig-utils": "^8.32.2-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.32.2-alpha.12_1748354132416_0.6180458173114209", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.0": {"name": "@typescript-eslint/project-service", "version": "8.33.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "71f37ef9010de47bf20963914743c5cbef851e08", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.0.tgz", "fileCount": 12, "integrity": "sha512-d1hz0u9l6N+u/gcrk6s6gYdl7/+pp8yHheRTqP6X5hVDKALEaTn8WfGiit7G511yueBEL3OpOEpD+3/MBdoN+A==", "signatures": [{"sig": "MEUCIF3ApMep1e/1mjsC2VyaLNjOHvU0p8vg7B/S465C/RiEAiEAoFN5XNiv0oMaZKlWqMB1OR/qM1tUGGXlDVlqQEIgbPM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14964}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d2ffec7988976a07495bde7270630db1a0fe1d32", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.0", "@typescript-eslint/tsconfig-utils": "^8.33.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.0_1748366497082_0.1615844794149115", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "4ee9e028c05c3f4c0a8798c3c385b5fd2e09d826", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-UI5aMGOkL4FaicHIbD/HTteQ3svXtL4RtXYmznXF5hh+ogNLpc2z5RIyHs8JoXEyHPlRPkimgOEStl/w0S1JQQ==", "signatures": [{"sig": "MEYCIQCEiZR5/YoKE7hOcq06eSABWUkMzQ31679TpHaUvb/jjwIhAN6IW1y0BtmJf42gctTBlQZLmQCkUnsIV2tWwv/uUCMH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14988}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "02ebbe10f6d9fc6a415b33a76bd76eb5402083e5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.0", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.0_1748524588954_0.354976823621294", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "20443a622ba21340a6da435a3355491abc27f917", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-BO9hGSnv5n2pO36QdbjdF9TYdy3XEX++gBu7zoxANV9dk7ZrpwWrIHpnWKLwPBhtQ7/0n1jJQaYcDZA9vQ1Z3A==", "signatures": [{"sig": "MEYCIQCm7UbuR8b+LDR4W4hFf9rWEt2SOkatP6kQ0aXsB/s50QIhAMY0PrND4eNkCjjHF48WhooKXoDxXY3Q+jIY0S8Bjx1v", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14988}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "294fb238ced1d96d379336ffbb27122a66dd1bb4", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.1", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.1_1748525377374_0.8814667522230781", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "9e530205f91fc225ec72843727c887dec2a4a04f", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-stHyIQrbiXGB9VJ3f6NFKu8SmYQJYrpu63rapNCNzvIDqGBqnd567affgbq5atM2uSRvwGSO3Oc3uegCy/stsg==", "signatures": [{"sig": "MEYCIQCgepLIkFrHEKc3w9st+xvqm512JG387GvW5iuvxIokIgIhAN/XhfS2W3aNZPIRgBH12LGt2lxabcDs/ed2nv/WiCqz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14988}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "702cea862db34b980e580f46314141c24024ee47", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.2_1748531671039_0.07273057588912257", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "5f7c102ac2437a521ea9ccbe99534dae886e05fd", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-2af9OUb2KDOI26TE2o9mgDEOoreCti2RFA1k1QT5Df3zz0KlnSrmhzlLVFFk8JqNFbQy8v3XfryQN7A+PYBKEQ==", "signatures": [{"sig": "MEQCIFA7+wrPa4vidN+J9xjYrVwvOVriKMzFn84UxtCfGbk7AiB0qAdopoaf02NkOfUU/rPdg3V1gBI+9xUvGwPgzZiMsQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14988}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "4bc72143fd73614d12ea7bf38a9e223f60066da3", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.3_1748589431884_0.8297070840032168", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "3ee8823f67853785a3dbcb95c9006f9575937921", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-<PERSON><PERSON>+RrBxkuir5EwJ8NIISq+HXyx9QkwWvLn/EZu8eEN87K0l3bReuvNV46wIIF2s9NJxyJciPpzowThbGckTbg==", "signatures": [{"sig": "MEUCIQD2xwqsrb04GCYYrhIM9rgBCsKd4eh+6QNCN09IUiXvkwIgTrEAYr9RoT3Ma41aBEK4X7ulqjpmnIMro/Ifba+Yl2I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14988}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "e4933170f75b3a8a0c9bf3985fb4d2ddb6e4b4c6", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.4", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.4_1748679799382_0.6293363497260356", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "15354f4f127d47c3f22f9751a0bc34c90e60d550", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-/tVU1WUzP3+VDdtm3aoGBlCuYn7yLmJAOmQMRV3TymPe/UElT5G/UO6kS7ptF1agk3855cxYCjU1/Gt9hq4F5w==", "signatures": [{"sig": "MEUCIF+GeuYX7IcBl40lM69z8CW0uiRQ6Ecu7W3C6WjDDUI6AiEAvjnwUQ1IOLRkWT+AS66yCwiiI+BZD595FXCGuksBimw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "9d46857e1377980bf4878fb273d5ef3848075bb5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.5", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.5_1748777270368_0.4560502713453487", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "3f1750837fa4548e3328dda49e4e07561ea00570", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-T2xJ6lqQ7yGG3k2Ulqn2w7/SGqMSxYE3qJOjRWTvrHmamj+J7sgLpaF9rC1wtPkt7Rvtjt/dvspT9rpYo1TkMQ==", "signatures": [{"sig": "MEQCIG7KfpQSOHLM1n6BooDMdgEwc32gnXT5bOKJ5cr2d/w3AiAbqlFrXMeOQzJc4OpKf3TOMR467gST2H1fCDXxWpKOcw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c14bcac24268636dddc8c75f85f66b42e8dbbf76", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1-alpha.6", "@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1-alpha.6_1748873588052_0.011852546697939559", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1": {"name": "@typescript-eslint/project-service", "version": "8.33.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "c85e7d9a44d6a11fe64e73ac1ed47de55dc2bf9f", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.1.tgz", "fileCount": 12, "integrity": "sha512-DZR0efeNklDIHHGRpMpR5gJITQpu6tLr9lDJnKdONTC7vvzOlLAG/wcfxcdxEWrbiZApcoBCzXqU/Z458Za5Iw==", "signatures": [{"sig": "MEUCIQD5c2KrOBVE1ZKTgErX2YArm0PgVWeJY9WZMsjMLfrM3AIgFBBlXTyU0ITGtVGdwcRFlG0Zb5C0PZPyye0iMozo2xs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15028}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "936f35022c1e1357da82c4b958b7bff2563e2075", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.1", "@typescript-eslint/tsconfig-utils": "^8.33.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.1_1748884773037_0.03527614410829916", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "70f0178f6879da0419710a55d65a091d7804455d", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-hZpU6jb7mhZyRAqx+na8proawgexU4KM10dRLiy2xxsVZMvNbiurtCMWb9r0C9SrlFP9txsnZxjNVUdCdF8mUg==", "signatures": [{"sig": "MEQCIGX+JsUBux+WdujWvIrd50a3RLWXfemlsNOeFkU0wzMuAiA3An/T0bw8rtUD6j3lQGxQkRrKFtEg9EPKf07XU8Ly6w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "936f35022c1e1357da82c4b958b7bff2563e2075", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.0", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.0_1748885526057_0.08258797253733063", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "a249476ca18d4c081d712ea870b811c7eae1b797", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-NN8mKbgqX25O04sPn3kgrcB5rORIX5daviHzhNKl8PjZh+F2y4ME8kFF1Cf8VviOZjKu8oaqQInolSzhJ3QPtw==", "signatures": [{"sig": "MEUCIHn5ZcQCfs8qnQYaPCj/akNsqeHFZhDERsmWMhjmRcyhAiEAxnubuaLsdBKacHmdJT8kQdnTYPRhvLlctQFjdf7w+vY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "0f5c59c1b116ced6aaff7b2c632f924b2ca49596", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.1", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.1_1749140996652_0.9668931088580712", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "383ee4b61fbacc372c74c1333ca2fc774a30dc56", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-/z9g8/sYPBKXLJjm+iWixWCJeJDpOEhchuItNKEJUOAwSID30Fp5tK+Nyye7WtT3+hTC5Duj98aBfiR7BmjcdQ==", "signatures": [{"sig": "MEYCIQC1hztnnF6jRsE3H8rQQR50+pK/k5pYTBw1yQc9R2gv2wIhAKJbQmuSEkr9lSKLzEIALmr++ZzBsExVW0wLxYYYDOq4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f5eb171473a248fd076c5a223c41643f144d4787", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.2_1749212277718_0.41355156081018807", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "321a1429bde072924ff930808366aaedc21321ef", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-D9+kVhlS+NErd6puTBgBPkjhLC0p+c6X0jz14XUNgXX7IlRWYJig58Ybknpl906bSslFl6vonrhzIunZm/WPTg==", "signatures": [{"sig": "MEUCIQCYHAUW/KLKBgra+IgYW1C9rHypSAZyErIz5yokMLjbFAIgZ4MfOCxCyhGhZu5R14MLkZJKRQhaO0a59MwXeuPj9zI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "1e0ba6253cdecbbb69b37537599aad9a21ed310e", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.3_1749265215564_0.07342672413466578", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "a0629fabf3373a7f0de0bff2ef33dbc90a26a9cc", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-kd04UFwBKQCEeVCo/wmomvq+RObVAKwENWBNB8Q9XSFEQh817zhCFoySsd+Gf9rQ/RByNGSDm2hoS6ZLBQJyNg==", "signatures": [{"sig": "MEQCIEMBpQuH/f3dwh7MoOMhNDUc2nbGaABYaJ/+QKoE07whAiBtlFhcJwIJbjuWJCXmjP0WbyZkFIJbo6rsu+ADjNMTbA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2df6f99883b8ed2e762c602f80a05c854aef87dc", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.4", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.4_1749453084895_0.6905814905834604", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "6124c22255896c42c5d6ee0b8d54acd8b5de8c63", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-YbUBxXmq1zi6pbOCnfU9wksKf84RWaKhm62zP1WfeVCQI+dMBQZ0A8/34nzc5NMBTPhKG2vfKUnpVPjveS/lDQ==", "signatures": [{"sig": "MEUCIQCMmruoII6OMTEO0qeKfXmpk0X+qLRkyFUBPsTg0wSLdAIgLL93m7qfcCQwLvCGzkYXCDgb+/siCTaOMUff1+q+/QU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d159a211d3970978f7311e5227462f30a53a060c", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.5", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.5_1749453900872_0.7808495997193807", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "e0977ef9b0417eff955360a13a42c662ed59e7b7", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-3<PERSON><PERSON>Vs4k5avpQlaMk2pglz10PljZZK9G841J1yMY58KvVMcSy1f68z8d723Jkfv3ozqL2wSCfw4NfhVU15mefQ==", "signatures": [{"sig": "MEUCIQCFDvRD+baMz1n2aVgDt+v13d2jHyfUgmXBGipVVCVguQIgEEKHP21ydhg+oSjWiPO4ZbL7Nn6c39a1BH/mQaBKB2E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "685e530478362c9e5a43db01aadc200a361cbc6f", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.6", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.6_1749454653981_0.42583328855962277", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.7": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.7", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "9d9e4a064d5b15372a3c1a4a3965081e5faa5e41", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.7.tgz", "fileCount": 12, "integrity": "sha512-FwKwepqSQX7KREPik7iD7XWdxAB0zs7s9lXXEjsBdz+vkV3ZBkSxNE/vKfrZh7qYRPecDJ6mmWYT+9b69mSAXQ==", "signatures": [{"sig": "MEUCIG7goP1NpYj/UMKVixSvjXZ2i93kdMqIF8EH3fZDBrI4AiEAlKNPWId3+FiFbKVutTsrUffhiP6h22yqPbARIdk0ZC8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "b2eded83f68d8e1685d1ea5e826bbfa891086b7f", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.7", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.7_1749455063339_0.3394721494024946", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.8": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.8", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "7824882f82ef5b1243ae187c7fbef47ef94697c7", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.8.tgz", "fileCount": 12, "integrity": "sha512-D1idHFoXHr3sZO9gSnltq1BD5K19+gONH6r3hwcMMOLPOccG1d6lLAQh9uu2dbHwJ4iZlbtDeEF55fxNyN8RRA==", "signatures": [{"sig": "MEYCIQDq6iLBG7+lBYq+KNq+noGUHiFuSlJKuWZ+x9JeMSJrRwIhAM0bESXNByNBc2xvxyCbkCzgmQYKI/6kkz1++bK6Cgwm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8208974140a1e658e5234435836476642e9a56e1", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.8", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.8_1749468593041_0.7045012867511631", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.9": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.9", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.33.2-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "05cca6e5c46b4a29c3b08686eca199c760fe1f3d", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.9.tgz", "fileCount": 12, "integrity": "sha512-ZraJH61EAapNVft3cTzfyJ2tgnX//B0VKW/prp+UX2iiDuQCikAWB1DdrkJ5DRzZGD/fTxrK2Md7xSc1xHZnhA==", "signatures": [{"sig": "MEUCIQDS38MD1A/Ok3eGJ8R0vXS0eQOZvvg8pmlCbXBCN65u0QIgXMzp4WVe0LBMYsPfZjLnp+Tkc+KAH9x2Ie/glW14B2I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "9b598778b4033e2cd5bdf3fa6e1af32c84a34a0b", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.33.2-alpha.9", "@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.33.2-alpha.9_1749468997516_0.21813173326277946", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.0": {"name": "@typescript-eslint/project-service", "version": "8.34.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "449119b72fe9fae185013a6bdbaf1ffbfee6bcaf", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.0.tgz", "fileCount": 12, "integrity": "sha512-iEgDALRf970/B2YExmtPMPF54NenZUf4xpL3wsCRx/lgjz6ul/l13R81ozP/ZNuXfnLCS+oPmG7JIxfdNYKELw==", "signatures": [{"sig": "MEQCIAE1a/0hVWkLJlUtinzOWZ50ObmvnYKmtdrVwX6Ngvh5AiBuwqWSCilTVuTCQ95CZV+hh2lotZj4H8Jz/2smWEqUXw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15028}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8915a477608892596fc6ed2bc45dbbac7f41a361", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.0", "@typescript-eslint/tsconfig-utils": "^8.34.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.0_1749489525522_0.3132994898594925", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "55450653bc5026cf2f0b0a2cb2adbb0045dc698e", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-oKEGzC5KRwOxQlPsRH0TE83QF9hBM7Nd9f+Z0NkGUuA4Scp1MPOxYpzQedGcjpTLtpozH0i7q06qVCY22AlF9A==", "signatures": [{"sig": "MEQCIDY8UPyYaAsrOSCSCHemP4/+U4z3sql7EeZkBNmmBaHHAiBChWvWFS5e4gu1rkKW+29YupqrgTsmbgSJ90624fEe4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8915a477608892596fc6ed2bc45dbbac7f41a361", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.0", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.0_1749490276507_0.7849202643405693", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "77377d778e8c8f6a2e6a7b362119eb7818116a1b", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-AhxkPRivRTTiUw4GAWODVwJy17tjm3IEOtQFLMWsPGnF9VaHxUr2Cld2aC9IGMEMHr2GCl7ebesdgEeMp6mi4w==", "signatures": [{"sig": "MEUCIQCLIlYfTQ/rZCAUfuTHyUpJ0r9/3hDLC1ljubwQqvqRGwIgU3eFWNX0tbTDEIUpTd8sFV5VcjON7nXcapOuXnalW9M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "445514aa1c9a2927051d73a7c0c4a1d004a7f855", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.1", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.1_1749597970826_0.14491991145322602", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "d38b6653bb4145fab1db19e98b168216f26378c5", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-v3+RBVmfoieGv6RR2+CA+2DuFNkM7/Et2B/N1k/5WgINj9Nth3C4HRhCKIlWvIZvYa2ACj3GFUV9iWNULL+CFA==", "signatures": [{"sig": "MEQCIHedNScc2cz7R9ludr6ZKFnmCY/oR7kwE8eLGnw3JhrAAiBJv8e0To3twByT1MCBlMtQp1Dk1LCVFZL3RcjzCtv1RA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "de8943e98e188d83801ec1044ffc69451db1aa63", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.2_1749859065758_0.5253278537166235", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "e028542aef72047f7f9aae2c7c1eef6b22736316", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-OhA0WukQ5TJoCPyUuy4KgVLWxeXvIBsbrlo9i79NHIZkX+0nqhpDXQy64lDsz/y3S4aox8RFkMzoPq/Li3kC1g==", "signatures": [{"sig": "MEYCIQC5b621PqaKfvsF/jy/5W3FkUSOpf4y4wCWr2uOCWO6iwIhAIV2G+inhjw6BmGmQxUv72hYheU7Oo/3calB8dJbA8g+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "af94f163a1d6447a84c5571fff5e38e4c700edb9", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.3_1749938165624_0.17442314310233376", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "6454412e0a7aa03d579dd0a5f6c75ae9821f7676", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-Cz5zBr6TXNSBzNFGj2viP1my+53Mh7iU1HU+HvSHEK17ZcIjpn1FU2A8cvClNK0CoSTXRwF3UDv57KsE02x4Lw==", "signatures": [{"sig": "MEQCIHNmI3ag0m640pfTOZmtxwAaUtL+thCFFvRYf6K6tGbbAiAVs20BPl149aO/k8XPULax7iaFlq11uUbg47bWWEA8cg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c971881e7af3e8e91f45391e6653a2815f725c3a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.4", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.4_1750073656145_0.8955916284625582", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "35e14aaacff9831314f7d47ec9bb5ed7f9fcf59b", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-QgPU3XpcN+XUUL7lUbtbMzT0r1i5kFIeo7exRxNeeAme5y3b8zilyETggPWJUBbiQkHY3L4s0+I8yFG9qiBc1Q==", "signatures": [{"sig": "MEUCIQCpVHN6Ixz5/Ru4CR7vHJtkRIUCuA2ss2FFjG55asqWegIgH1ld+608KMo6ZHJ9oa+hP74iRRu+03IIeNVGkbe+o2Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f9d4d10c2330b6d646eff148b7648a84b7387a1e", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.5", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.5_1750081239565_0.8904283800137323", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "52f36c3edfa66352bb461d8e3b04096c02e0ef55", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-mpvdRf53iVxQSjkbJsnGtZALBTSAkFeShYSo+kFWZXpwyzOtIsS1ISRTgwudyT5Gbc4D0+e6GJ4rI4IA5QKhhg==", "signatures": [{"sig": "MEUCIF2TfhhhRIGZgyQIMebDs0HPKW2axEtfrbgh59gEPF2JAiEA2Fz0Sn+BRMdLYD6mg4hh69aU2ucBlAw3ipf3BBbua/g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8a69a33ab1e22c7e4b3727aa004fb58b98fd4a3b", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.6", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.6_1750082012237_0.19115493812732964", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.7": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.7", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "8f16e32039649cd5f36e93597d5050ef34ce800b", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.7.tgz", "fileCount": 12, "integrity": "sha512-7<PERSON><PERSON>+HDPli2QMVWWmFJ8r35oRx6r2g5j7W+LoIoJZ09r9wDMVaHrbcQZigTAQKowjklOTF7/oxQvZOKH70D2g==", "signatures": [{"sig": "MEYCIQCFUQJ1laoPReDBACz6kiR5Xt4D/vj94OFqefM0iEpChAIhANCmxEkXm6av39tb9dqwtkEKDV7l3KwizV737EE+ZSLo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2fbae4863fc39279f61ba77bfe01e080a5796072", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.7", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.7_1750082819412_0.13132084178359738", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.8": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.8", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "a1479e69348e311a98428e33a36b0fbbd94b9dcb", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.8.tgz", "fileCount": 12, "integrity": "sha512-0syHs/kXb8Y/9jjIUKRqX51tIIgmwOxnwFIL+y345gFxnOUuxT8Vcy09xhARLVH/rrmehEkGePK04w4j7G1r+w==", "signatures": [{"sig": "MEUCIQD4hjaOvaL+WBk4mbDfG0n2wEksf2gkzr7vdqvWS9TQ5AIgOISOxWRaX/iVp2/DSo6VxRdf8zD5FkYkK2ih8wwbmFI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3a8369d2c5798ef3187c8ff412d409e2d5e17726", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.8", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.8_1750083518518_0.48338070023151714", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1": {"name": "@typescript-eslint/project-service", "version": "8.34.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "20501f8b87202c45f5e70a5b24dcdcb8fe12d460", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.1.tgz", "fileCount": 12, "integrity": "sha512-nuHlOmFZfuRwLJKDGQOVc0xnQrAmuq1Mj/ISou5044y1ajGNp2BNliIqp7F2LPQ5sForz8lempMFCovfeS1XoA==", "signatures": [{"sig": "MEUCIQDcFNAmQ1D73qLuTgjZjnCZE/ywGEpKyBUeLRAno2CZhQIgGqcpwi8/JKh1koOGrLs9SqzZfjLIska1X31ANLh2hms=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15028}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "ccd07914d933c3f7a284c9a7df5b1d6d40495fc5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1", "@typescript-eslint/tsconfig-utils": "^8.34.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1_1750094367003_0.4597727850989952", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.2-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "bb13fefc13d77da22e8817a4b54a22ec62523853", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-zMhINy0qe0OONI4ja2UKj+n3aWwtivvu56cY2s39Xal4phf7Sg8sjFwJgq7IppSXmI30EwY6hqP82+UFQhjqbQ==", "signatures": [{"sig": "MEUCIQD8+YOgREslVmvWBfg+QbuluBK1v227qreLt+BUeFeMEgIgVxmJEyLtaUWoSyWak2ngvwzlYBH+lW0vH+2rLBYyKRs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "ccd07914d933c3f7a284c9a7df5b1d6d40495fc5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.2-alpha.0", "@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.2-alpha.0_1750095135083_0.5049115101507413", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.2-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "0e9d418478d5e929853ac77e01b931e7d6eddb6f", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-N3zlET/lYZU5tzpBJGqmcUBZ0RQNB4J9mNj3jW6M9FHPPWAJnTXklSlaLnIutHCOwjOk5NdsJgepF3S9cxlsAA==", "signatures": [{"sig": "MEUCIEihkzCxenhmir8vnfRZckMpRB/pOnPaUh38fpZFSDl0AiEAyDkI5rbo4+StswbkkeTwr/ZnnYEUE8wQALMkFuxFJb8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "76cc62cb37353711b64e3353bb62e2e0a688d629", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.2-alpha.1", "@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.2-alpha.1_1750212375989_0.2732291713459116", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.2-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "1283dc6f1dda97cb3835696e1e4943ce3c12af18", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-X08M6x9wS5anWiGupyfV2sL2+upYRtoahZJxNke1sXb3ymrZBPXK6exHKtBseFLidFMZAoMisPvt2YfdCbP3Zw==", "signatures": [{"sig": "MEUCIDi/4NpVXD6jqFaJk8tU36OChqIgM4dNTC1g7qCrr56EAiEA98lX5aLGimiA2Mc/VmPHwXsy/IY+GUyWBHw79Ijnkt0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2e35e3a7bf03654730039ec432cbf445819057fd", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.2-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.2-alpha.2_1750696939580_0.867848147981273", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.2-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "f16bd34e6a23cada317dd7271c5ba1fe573688a9", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-O68Hz4Wma3ykURKOxz5fSnW2pCWeVT2bGdjvcl1Nm1lqjXWTXYaUPEMhTEg8m35wo1TkQa693UdYyvAxN0AEAg==", "signatures": [{"sig": "MEYCIQC3aAatPcU/HBJIlRMIQmWJ0mbAsjd47uHqNpTgz34z9QIhALQUF6llYn6gXGw9GyADsNy46dMdJtBfgnaNaiBKdKEm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c273e038fbd525232a8896786db28e9705cf205a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.2-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.2-alpha.3_1750698786853_0.4382617602214187", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.0": {"name": "@typescript-eslint/project-service", "version": "8.35.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "00bd77e6845fbdb5684c6ab2d8a400a58dcfb07b", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.0.tgz", "fileCount": 12, "integrity": "sha512-41xatqRwWZuhUMF/aZm2fcUsOFKNcG28xqRSS6ZVr9BVJtGExosLAm5A1OxTjRMagx8nJqva+P5zNIGt8RIgbQ==", "signatures": [{"sig": "MEYCIQC+j0ULtezBwHOHtGFBhjsZLA+UrdI0GOyX5Rxm2/lEkgIhAKIUhzDrzAFrgeT+u/49jT0f7PEorWR0qrGuZvigSD9U", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15028}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d19c9f383a2e345656b601aa42ec250293609019", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/tsconfig-utils": "^8.35.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.0_1750699173264_0.40435574143264796", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "d9ae1400f23f08ddad3e5989ff161a7e56eacc52", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-YcLl6msX0Zgv5sitcIM4tsbJmGn+CU/FUcG3cv0bBPlW5yw5G65DMlYG+OJ8S/GY4349Tk//ODhfWrj3iFKAzA==", "signatures": [{"sig": "MEQCIHddPMTyv7MPmgxf7WlJcXwOQRwqxblzrzL/6wPe/eZFAiAAjdcE4CL2xUbnf2YN6b5UFJJOGJFVE7xDPRS+p342mQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f000a1f0c77c8275ffe7ea92e04a94275e73396d", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.0", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.0_1750699572066_0.950852372518276", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "1fe7d68785b54ec992a7ddde8130074f23ff47eb", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-BCQqOPBWdQh6imtg8cKiwmmE8uiQRkbc503xb12PrNpv64CmEuT+Y19iEWdr7tCIwNB8VNjT/p+WuPAzoa1mew==", "signatures": [{"sig": "MEQCIDdGgKwR91rDtDHBr+bQoot+ZS1vK+D6kobKY5b/WDIBAiArr80p26Nnhpt9C0g+F+Oz7hS2SiIe9wIsjmHeLYO1ZQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d19c9f383a2e345656b601aa42ec250293609019", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.1", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.1_1750700363469_0.9408405862829095", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "91a110147133483bcd5fab254415ba0fed227cc0", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-EVDlaPGpQH+HfGXf6lVkzcUYBGyuEUeM4QQ5WTW4yG/oo9N0cNRf3KJx6r9LWTFeMyvWyvoJFfo1To/mNmMmDg==", "signatures": [{"sig": "MEQCIFrv6lINF6pZ67QqkW7Of/oedRgc980Kopzd/Cn95zxOAiAS1YhozoESYCgSx0prMv4Rk5a4cadAn0GuNUPTEIH6vQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3ecb96f2d2203b65ce0f2e2eb48c1ff5e875bdae", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.2_1750802810943_0.3266072447958801", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "309af16113503cc22804705138566ba5abf1501b", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-C+fjIh65GkCskykAXlWOLquKmjaIHPxp6xat67l9AYoxQETu9bpqPH5kvPa7JGZqqVQz3yq2Ozw2fB07A1leMQ==", "signatures": [{"sig": "MEUCIQD5fvD1zQXgmbEtQVOwGwhlxT5syQFgStBU4BAfYwcdtwIgNkJP7JTevzkz5bgQG2yxm+U9ap40KBTJpLhu7Co8AIA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "9dc91aaff7c55082a7a768788664d625f8283500", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.3_1750803342810_0.12386886631379301", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "e528382534ab3d29efcc6ee90947cd1ec710d438", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-flPIXI2Fs2cynkTeegvI6kHtgUIbaGyEZw7ybw/yhuwhemKPDTzchjYv0NQtRy9w5Gh0EKwYnn5J9AvcZ4CMHw==", "signatures": [{"sig": "MEUCIHBbjaZ5iAj7LCkTQJ8oW53h+xfDzr5Caz6qKoYpEEWEAiEAgB63DaU9OTn6/Ohff4wLTG6mMR4bTFCEhGdqtxxfvAc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3e545207f0e34611f528128fc699b25091bc40b3", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.4", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.4_1750853727339_0.1848706057641485", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "ad7639e5df56130fbf8b9ad09c3a9e90c6363bc1", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-gLkXjvyCznj+7i2Xl+AJdX5ezDDeguQWY5dly2ZVG6pHllS95JEPoV527I1eGB3seP60ZFd3Wbs3lbOChnauKw==", "signatures": [{"sig": "MEQCIEvKZESNaEjOc9sikJzWtpNXLogNMBLMeqAl8EsdyIlDAiA7J4yy4gg6mbeb1P6JzNFlw1u4jKYNVy7q6ApVFZixXg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "20f8564b82b225c8d685f5f06284516f1f22b32a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.5", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.5_1750943220494_0.4843126427050546", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "31dbc45f3ad24008e200e0cc5cc626ef248e250e", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-gfjw147v8S70ZIoyZtN9mQPha+YMLmUjPHkMNjceeCgt/FUqKCu8Etzh52fwyKV95bfFkGX98NKC2+0nENlNRQ==", "signatures": [{"sig": "MEUCIBIG2ry84YPhSVaK6FmGeM01lWaFHQcqEWGAfSsw/RA2AiEA/eIV0MHzQiBusmoUkMmcixtecpkUpWt+6Y4agCW9yKg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "db32b8a82d58eddb29be207a5f4476644973abbf", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1-alpha.6", "@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1-alpha.6_1750945529973_0.7390106551451201", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1": {"name": "@typescript-eslint/project-service", "version": "8.35.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "815bb771f2f6c97780e44299434ece3c2e526127", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.1.tgz", "fileCount": 12, "integrity": "sha512-VYxn/5LOpVxADAuP3NrnxxHYfzVtQzLKeldIhDhzC8UHaiQvYlXvKuVho1qLduFbJjjy5U5bkGwa3rUGUb1Q6Q==", "signatures": [{"sig": "MEQCIDSi3fj9ugpZNZrqcu7wN8AudAKAtdneEGgT9sLOHk2bAiAnWkubw/B7YA3NxhX6EFl61xkULf/uZxfZZIraLFxnKw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15028}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f9bd7d86fc39eb2957de7eefdcd3ab9b6c9dc4a7", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.1", "@typescript-eslint/tsconfig-utils": "^8.35.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.1_1751303934097_0.920468560443171", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.2-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "76dba5b2dd75c010240ccc1c8a4b21815f87e953", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-ondZ0Kq69r7MGf7ZF00qzqry90FmOjgFkgFcV7DjZlPBOu/DQnFFyQ9gKQ0w/0mGxAfDfhT1mWPjkH82Urot0Q==", "signatures": [{"sig": "MEUCIQD3watIVuIEbBrN1k9EmBNtWkT6KPcVu1uKFFX+VjHU8AIgDzKwbijSYdU8SBAstFeyyJXayycPxnUra8V6YIwjurM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "e2ecca60d9e8486bb13e98b3e1a65d529bedef03", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.3", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.2-alpha.0", "@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.2-alpha.0_1751362001199_0.3211689095385817", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.2-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "4643c0c73135d64b9bf56273a18c99cebff29d27", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-YrLhM85DI18wxXNYSPqRWtZRHT7OFLJMVUuqANIVjqnGu59e9uurtgI3Z5Ivcv+XISgByppf+BMEj2dmURIMjQ==", "signatures": [{"sig": "MEUCIQDuRJDoUjSHjcuRsBL+FGZ7FhUn6O51iMhdGHZ3ZJxm2wIgAJ8s/4bOYqSMqRWwlWi6eYnXsqc1TSOMk8vQJG7SWcs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f391560ce3edeeecde056420284799124ef1d244", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.2-alpha.1", "@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.2-alpha.1_1751368081109_0.7648994138741798", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.2-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "0a1336e2f6af6e26e8d36cac4435ba92025e10c5", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-lH+PwHG4eFRBPSfZTiro3cEUDTzJkTRrA2SJ1j7UO9BNsaVsOdVVML10G4Ws2pVHUSddcc4Q/SdV05dqbsAcDQ==", "signatures": [{"sig": "MEYCIQCgRHhYG/f7Sr6sx3sb8Tpyn12ped+3i5VuylISqnb4UwIhAOjljH/GpT7iMLdvsOFAAKFvundACg4ZWbu4/32hon1a", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "50a3ab6ea8ea44691ff8fbe16c1a4f46950fca34", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.2-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.2-alpha.2_1751369538607_0.9674669661470692", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.2-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "5613c10ecf87fab24ce01c9d669fd24fed259f9b", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-f+/KcAshRfFCea7TMAupRGI0f4rcA7IYQHMz/7yoa2hQQwuXYxMeqt6rNI7SwQGPJtJw7cNXDTDkl+n8FfQb7g==", "signatures": [{"sig": "MEUCIQCpyd5SMJFwep3aiXv8FgVRS6X7mtjHIDrD/K830ACliwIgGmvtDMaDtQOXS9xWM22nQ9bAWs4DZQuinHI187jEqhA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "cefd4a93d80f67a9b8a892529dee4ff96adafcfd", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.3", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.2-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.2-alpha.3_1751369976938_0.014403139251423669", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.2-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "66fc86e245a2b4d74d0343ed90ef88f4c50a4caa", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-9CfudOhoEYIS8GMLFFKXCQo7IlU8uyYF3pcW/0catx7zT0bCCYCxAXY/CQ1knQWvW07o9+O8txS2H9n9xAWo3g==", "signatures": [{"sig": "MEUCIHo20H6UTeusn1S7mSr1ntCZuCDLENt1qdRrgj7pyM0vAiEAhEEQXtBL91Nn+kF4AhdVeYjyXGU41FvYmXgd5/+WUJQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c14c98e8f7487d7cd13551b34f039dc278a40092", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.2-alpha.4", "@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.2-alpha.4_1751371737109_0.02298643585724469", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.35.2-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "753a5e74403b6eabad71b95995bdb103c7685457", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-ToYyZmSc0d6UEasmUksFcbEKCYhvctNfjVzJHbdu0/ru+ega0+6DkimnW4b6OinPsN/APzP+2h8WfDmC7q5FoA==", "signatures": [{"sig": "MEUCIQCAghRHu1UFsyHuyzSVxhazg+Kpom03QbsqAeSFoPScWQIgD/wD4F0h51MOc5dcZ8IapON0IG+jHrGMSnBnrY+iz5Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "5b378e290e56eefe638f22444d55bc5c0744c28c", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.35.2-alpha.5", "@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.35.2-alpha.5_1751378286952_0.9586521696401367", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.6", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.6", "@typescript-eslint/types": "^8.35.2-alpha.6", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.6", "readmeFilename": "README.md", "gitHead": "7ec793193d7b9c6c9928191e462c54b10b177723", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bKpHb+bZgM3HMT5ugUT7I6WB5bMZ1xncNH98M7653X8JAs5WC9nVFwgXpqGXwIA3rPX76CJ0Zx3w5tQc64mVMA==", "shasum": "45f2d48a1d108aed40ec9f391e2c593b3e4aab7a", "tarball": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.6.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIE7LNDUKuyNLSvH6UGqeF0oacNcsTiHlTL9C6oFR8/qIAiEAwzDG2zF/24oAf5c9WDclkBQGIup8taZTp4hMyFzkZVY="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.6_1751735797485_0.6872979301776461"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-05-27T13:55:32.281Z", "modified": "2025-07-05T17:16:38.194Z", "8.32.2-alpha.12": "2025-05-27T13:55:32.590Z", "8.33.0": "2025-05-27T17:21:37.262Z", "8.33.1-alpha.0": "2025-05-29T13:16:29.149Z", "8.33.1-alpha.1": "2025-05-29T13:29:37.541Z", "8.33.1-alpha.2": "2025-05-29T15:14:31.226Z", "8.33.1-alpha.3": "2025-05-30T07:17:12.111Z", "8.33.1-alpha.4": "2025-05-31T08:23:19.551Z", "8.33.1-alpha.5": "2025-06-01T11:27:50.578Z", "8.33.1-alpha.6": "2025-06-02T14:13:08.223Z", "8.33.1": "2025-06-02T17:19:33.217Z", "8.33.2-alpha.0": "2025-06-02T17:32:06.262Z", "8.33.2-alpha.1": "2025-06-05T16:29:56.832Z", "8.33.2-alpha.2": "2025-06-06T12:17:57.877Z", "8.33.2-alpha.3": "2025-06-07T03:00:15.772Z", "8.33.2-alpha.4": "2025-06-09T07:11:25.062Z", "8.33.2-alpha.5": "2025-06-09T07:25:01.041Z", "8.33.2-alpha.6": "2025-06-09T07:37:34.143Z", "8.33.2-alpha.7": "2025-06-09T07:44:23.504Z", "8.33.2-alpha.8": "2025-06-09T11:29:53.261Z", "8.33.2-alpha.9": "2025-06-09T11:36:37.693Z", "8.34.0": "2025-06-09T17:18:45.685Z", "8.34.1-alpha.0": "2025-06-09T17:31:16.689Z", "8.34.1-alpha.1": "2025-06-10T23:26:11.046Z", "8.34.1-alpha.2": "2025-06-13T23:57:45.965Z", "8.34.1-alpha.3": "2025-06-14T21:56:05.815Z", "8.34.1-alpha.4": "2025-06-16T11:34:16.327Z", "8.34.1-alpha.5": "2025-06-16T13:40:39.728Z", "8.34.1-alpha.6": "2025-06-16T13:53:32.408Z", "8.34.1-alpha.7": "2025-06-16T14:06:59.650Z", "8.34.1-alpha.8": "2025-06-16T14:18:38.717Z", "8.34.1": "2025-06-16T17:19:27.231Z", "8.34.2-alpha.0": "2025-06-16T17:32:15.255Z", "8.34.2-alpha.1": "2025-06-18T02:06:16.193Z", "8.34.2-alpha.2": "2025-06-23T16:42:19.754Z", "8.34.2-alpha.3": "2025-06-23T17:13:07.089Z", "8.35.0": "2025-06-23T17:19:33.437Z", "8.35.1-alpha.0": "2025-06-23T17:26:12.263Z", "8.35.1-alpha.1": "2025-06-23T17:39:23.667Z", "8.35.1-alpha.2": "2025-06-24T22:06:51.122Z", "8.35.1-alpha.3": "2025-06-24T22:15:42.981Z", "8.35.1-alpha.4": "2025-06-25T12:15:27.543Z", "8.35.1-alpha.5": "2025-06-26T13:07:00.695Z", "8.35.1-alpha.6": "2025-06-26T13:45:30.142Z", "8.35.1": "2025-06-30T17:18:54.266Z", "8.35.2-alpha.0": "2025-07-01T09:26:41.376Z", "8.35.2-alpha.1": "2025-07-01T11:08:01.271Z", "8.35.2-alpha.2": "2025-07-01T11:32:18.772Z", "8.35.2-alpha.3": "2025-07-01T11:39:37.099Z", "8.35.2-alpha.4": "2025-07-01T12:08:57.265Z", "8.35.2-alpha.5": "2025-07-01T13:58:07.107Z", "8.35.2-alpha.6": "2025-07-05T17:16:37.671Z"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "homepage": "https://typescript-eslint.io", "keywords": ["eslint", "typescript", "estree"], "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "description": "Standalone TypeScript project service wrapper for linting.", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}