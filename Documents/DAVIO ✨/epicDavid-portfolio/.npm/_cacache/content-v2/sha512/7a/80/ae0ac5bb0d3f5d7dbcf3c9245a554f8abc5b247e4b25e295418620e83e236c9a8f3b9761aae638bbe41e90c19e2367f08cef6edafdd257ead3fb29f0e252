{"name": "@testing-library/react", "dist-tags": {"beta": "11.0.0-beta.1", "release-12.x": "12.1.5", "alpha": "14.0.0-alpha.3", "release-14.x": "14.3.1", "latest": "16.3.0"}, "versions": {"0.0.0": {"name": "@testing-library/react", "version": "0.0.0", "dependencies": {"@babel/runtime": "^7.4.5", "@testing-library/dom": "^5.0.0"}, "devDependencies": {"intl": "^1.2.5", "axios": "^0.19.0", "react": "^16.8.6", "redux": "^4.0.0", "history": "^4.9.0", "jest-dom": "3.4.0", "react-dom": "^16.8.6", "react-intl": "^2.9.0", "kcd-scripts": "1.4.0", "react-redux": "7.0.3", "@types/react": "^16.8.19", "jest-in-case": "^1.0.2", "react-router": "^5.0.0", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "react-router-dom": "^5.0.0", "react-transition-group": "^4.1.0", "eslint-import-resolver-jest": "^2.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "beba5ce576d1b821bd9f1e18d522f1eebf04f8e4", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-0.0.0.tgz", "fileCount": 30, "integrity": "sha512-2puTWw9/JLrKYnU65S8Tiz7wGBD6Ko2isgwPbwWPcCPyI9YnaAVpyV7dUaBAewRXcxDFK+mbx62bFZkhKuZ08Q==", "signatures": [{"sig": "MEYCIQDtILa60xyNFFp91KsrRJtWpT5gHCEGDM12LxXfj0HimwIhAJ/cbRugQGn9yx4LtG/jpkOV9iYxr8IEJQUn8bglkZ8Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1057505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8BSeCRA9TVsSAnZWagAAxtEP/iT0NLCJHcu0wwcPWyLx\nuNBL48iUNqQdTH63el1SLZmIRZ/1AQAj9gBqcukQ+KcsXqTJapxci3VcPZGn\nbD3MhzlTxD1noXNV0joQvJg6vkFGH6J0fholrj7l6nOtNzUkR0VP8iT9v2U0\ngiesivVRGQm9leWXrFiJCpzkz5dGi4iFQGszEbr4WWRjZIa/CWVCLypann/3\nILFygJSRyNlvg0aIiPqCHYkBnI5atWQx18jpX2nKjROJP5YnPujduCXmpdqw\nCNPEm/5yFT7krNT0yu6A2hQaAce+6PIL1/QPe6gFQPd/45iek3oYh1FaRYcB\nzH7NRDZluNpZP9S05GstcQni7954mKY2p5qvpo/yUQAaUWgwijzLjnMryJAA\n6AzogEw2H8VzdDRH3DyHrfx9aP8KCwUDBWPjW02yTdtkEUWWsOtREoyba1Qq\nHcaYY/aA1NNcR1ac/iTK+b5aXjWK1AAvT0kA2VxeKuIPsjJ3qrC6rRwq5ZH6\nASOZ2QQ5OfLoB7BP2Zgpbhxxhy3disTmASY/y5qh8IYlFPedkoEt4metrdyL\n6FJP7zYtFa36ePxypL3MG6nz7zqrcmOtBO/1Z2Vfd80JDyezrcFC2CzKjb1z\nBkIXzUaNGY2eZgfVAPVxIh6V8Ts4Szy/PbcO2kcqZKI878eRaRHVTeKJB3qa\nWY7P\r\n=d/QG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.0": {"name": "@testing-library/react", "version": "8.0.0", "dependencies": {"@babel/runtime": "^7.4.5", "@testing-library/dom": "^5.0.0"}, "devDependencies": {"intl": "^1.2.5", "axios": "^0.19.0", "react": "^16.8.6", "redux": "^4.0.0", "history": "^4.9.0", "jest-dom": "3.4.0", "react-dom": "^16.8.6", "react-intl": "^2.9.0", "kcd-scripts": "1.4.0", "react-redux": "7.0.3", "@types/react": "^16.8.19", "jest-in-case": "^1.0.2", "react-router": "^5.0.0", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "react-router-dom": "^5.0.0", "react-transition-group": "^4.1.0", "eslint-import-resolver-jest": "^2.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "7398b46de10be4056dffc5488feb27707e7839fb", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.0.tgz", "fileCount": 30, "integrity": "sha512-9FmnHuwUolUO+TR9hkA/Uqzo5UfqxFjz0w19Fpzq4VK8iqt6rjrfxB3WlZAYGuEXqsYFKmfMIprL7chqlH3JCA==", "signatures": [{"sig": "MEYCIQCNITB3HypsrqANadtRV8/+/qukMms+upBGJQRPflFqtwIhAPt4bIivjr09Y0g+DtwABrF4iIa2j81X06NAVbF0f0pO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1057519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8BdCCRA9TVsSAnZWagAADSkP/ikCDmICvAv8BKwKkd3L\nLYtGhO74Prg40HsMvJkkuWVMRjoB+dzQUGLuE3TKNPpR1Tdk6H/9zoEUcB7e\nvT1M5cJNYSlCJphoT/QrhPvL/SvBj8sn5CXkjsYwgYKv/2R62J6fqt8+7Teo\nj8KmpYUXBx1PWRBA1MFvrHsHkGBZr9vNmZmqmEHH6gkam8tTSI/JHcu5LXYE\nCIlxqrSu/QO7Rhlp4vjIbcI13tgfs4lup/TiyNW7TYgGG/Go0/X7SWQfiT44\nn4SqXKKRdqOvgfW9+2m5JH6vcG7ib8D8lHuYiuqHQGYP+dEebc5jH9ywujEM\n/hQLbDeYbF2uay6JGbfsFjb4KPLq7sxw9AusXcJYN0oEVoA5AIRk5yAWunvc\nqFccLajppgU4Ecmww3D4f72A/gozClljp3R0JYLIcWIwk0I7mAox+oTLfBFL\nuoK2J/jqTkJohexHOC+otog1xT7yvCihG5g4a2u1DAeoTFWcWox4o+pj1SST\nHQcZV3o+Cdtjd0Yn6SXTz/ZDf6FXpzhyhDXCr/4rqW2juZ0EvVm8Wt0+OZMN\nyyDPlMRlDVozNC6BS/GMrrEf3mIc3IGDTTsFjdAedcEy/5XFdtfjxxLcWi5M\nsD0EpTeuw9Ri/F8tq1hcFK5gsBXhrL6R812MZE8EHcypssELhpqokFEF0BKy\nv0ji\r\n=LarG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.1": {"name": "@testing-library/react", "version": "8.0.1", "dependencies": {"@babel/runtime": "^7.4.5", "@testing-library/dom": "^5.0.0"}, "devDependencies": {"intl": "^1.2.5", "axios": "^0.19.0", "react": "^16.8.6", "redux": "^4.0.0", "history": "^4.9.0", "jest-dom": "3.4.0", "react-dom": "^16.8.6", "react-intl": "^2.9.0", "kcd-scripts": "1.4.0", "react-redux": "7.0.3", "@types/react": "^16.8.19", "jest-in-case": "^1.0.2", "react-router": "^5.0.0", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "react-router-dom": "^5.0.0", "react-transition-group": "^4.1.0", "eslint-import-resolver-jest": "^2.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "91c254adf855b13de50020613cb5d3915f9f7875", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.1.tgz", "fileCount": 30, "integrity": "sha512-N/1pJfhEnNYkGyxuw4xbp03evaS0z/CT8o0QgTfJqGlukAcU15xf9uU1w03NHKZJcU69nOCBAoAkXHtHzYwMbg==", "signatures": [{"sig": "MEUCIQCUIkZuDHEuoY53Uci4OHqcztuj8QcCHFN1G8+sQzYwbwIgJsUdBPSSF4Chv0NDK8guae04/gPjERfjD80fP+tIsq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1057368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8BkcCRA9TVsSAnZWagAApnUQAJjW2A5ytIpAQZquCrhk\n2JnrOTjm81LCGnYXv8THQNFcQSOf7v911uicGAy7KQEmZV4n2p2ZWtt8tyQK\nxB+beyoE/jJG4UlP/JZzUlJaSBEXAbb0Ow7pym+WWG5YeX0VxJtWIuM3yz1O\n8f/fQIuBehuruT3KCuVio/4I2/xNWw4+nInHZZJbshgjBfx/XSn60KE8Baty\nfN3XmJdavp1jEwgMYoK6dNXXD9KwZGY/fv2hHVER/ll+KD6x5rfKaLz9+urg\nBRqGbhpNQZ4z82XbA5fWypdkTb1xQX0jAkKOzCglViPtOsfaIgKERGiMsKK2\nn9NRYr9h8fUpYpn0OPlWWp8isUyycQtxE5uzeAmex1JgPi+1+W0E6R/RBZeT\nH4Ge3g+qnoqoXSIcR2+sBbG3A50GxqwQOID+T3jTTyp+adDQ//kLjVifOlfM\n/d4yJuOzzy3QFeI77wCoVcH5SDUBDeh40HKGhKHs5zeolY3oVcrsEGlJePAB\nX9p1/gFFRdzSRSUf7OyohLPYJVeWkqAx2QlTTJ3hrkfoR0gQ4Ni44RrOHled\nrkwV6PwAxIOkWEywK9sqaVEgQMrFaM4atYmtdKbir5M2tIJpD3Da2XuvdiUM\namtGXjJV4NonD8YWmuoPy4aOEKrnEQ/a8gr8DRoDHAULi1T9UJWsRNYSGMiG\nlql+\r\n=A5sI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.2": {"name": "@testing-library/react", "version": "8.0.2", "dependencies": {"@babel/runtime": "^7.4.5", "@testing-library/dom": "^5.0.0"}, "devDependencies": {"intl": "^1.2.5", "axios": "^0.19.0", "react": "^16.8.6", "redux": "^4.0.0", "history": "^4.9.0", "jest-dom": "3.4.0", "react-dom": "^16.8.6", "react-intl": "^2.9.0", "kcd-scripts": "1.4.0", "react-redux": "7.0.3", "@types/react": "^16.8.19", "jest-in-case": "^1.0.2", "react-router": "^5.0.0", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "react-router-dom": "^5.0.0", "react-transition-group": "^4.1.0", "eslint-import-resolver-jest": "^2.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "e5df2102fca261b887ca5da803f3e520fe5eb8aa", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.2.tgz", "fileCount": 31, "integrity": "sha512-CPne8wzJGs3mcUA4W7Mo/WVtjsZto4/54s6vIfgWQSDIxBGbJ3qipikSq3c9Jg8Sbncj3bTIoIrzbg+NYpQphA==", "signatures": [{"sig": "MEQCIE+lmA5rYL/K2j0fj0j2MBGov3zMXrE2lok/tEGD3W9OAiA224pnHQNp5VImBWCF3033NBLiT2PwLfvzwtay6mZ8fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdERt7CRA9TVsSAnZWagAAZBUP+gLd6U29//rrUa6vYt9t\nxnNpGDPl/b8mGr4b/QNdrb2GEke1gVBdhTEZDqMgYbBTypQHMtgOawl08MS5\ngpzz223M/fXYFlg10Ljcy6tgM8TRHWzwDhCfFtp/VzerOLeqMBLP5Htjrs3P\nO60vLBgfkN+iYdNpG2CM0Lwn9COjGKo1FKais/brBzKr4v76b2oyX8diQcSO\ntD1sKp+2u0Tnfch5rgR6MqXHdPXKyVRCYEhJ0GYKrS1PnJ2ZhrvOtDAQFXyN\nQc2I7Ck2o77SquC299tOXB1ZO07P9M45XQe3OEWj88DAJVMnBu2df9FUgOwQ\n8O0YRSricOfemAZzV9VOEv0bxch97ytiapzV0pcTxdvlAoXUhYK+/jvViE0U\nQHh9uk6RWcHbOZcLAEqowYXOAaaaL4ycjpxTjn1urq8vwqC17aIynvbRcfub\nElmJDLbBF/0/At2XiLHZsDnaFOalr68iLZ7+NlQU/tM9cI30ITQ90t8dlLJB\nbNvBIjNU6K00RgrPnE0e5sPVySxg0hyfPe4TggJ2KLqtt2dVqBfRWpD4hLaw\nMzyfwWgNB51bf8Tvoy0UPXbWSZ1pM54bWEok3vPxPDuv/N00Xodg6AVvxb0o\nWMAC2McLd7VzFhsox1BO+blAVhZJcE5kbOIN63X+ONQbU+BlVrABdWK+xCsv\nUo6L\r\n=rTn+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.3": {"name": "@testing-library/react", "version": "8.0.3", "dependencies": {"@babel/runtime": "^7.4.5", "@testing-library/dom": "^5.0.0"}, "devDependencies": {"intl": "^1.2.5", "axios": "^0.19.0", "react": "^16.8.6", "redux": "^4.0.0", "history": "^4.9.0", "jest-dom": "3.4.0", "react-dom": "^16.8.6", "react-intl": "^2.9.0", "kcd-scripts": "1.4.0", "react-redux": "7.0.3", "@types/react": "^16.8.19", "jest-in-case": "^1.0.2", "react-router": "^5.0.0", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "react-router-dom": "^5.0.0", "react-transition-group": "^4.1.0", "eslint-import-resolver-jest": "^2.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "96e1694d6b3f52d066c3c8c389fe810c4661d095", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.3.tgz", "fileCount": 31, "integrity": "sha512-GnBcl7LbzR4jjyn8G4thg5Eq/VC+R5OTjFWS5vUP9JkAUnSqg7iOoljBjzt4YubDdSN8F7JgzjdkXNkwZ4XZEQ==", "signatures": [{"sig": "MEUCIQCz+8E/5gxhOdvwFYdL0zS4HszcqIz5Nb2rZpS27sfSXQIgdY8pXqcMgcH84xFZOnbRLD8UKjvOTv45q5NCPLNT64Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEho7CRA9TVsSAnZWagAACyAP/0yrxNPGalt4dX6XoWqf\n5OMWf5J0LDCFemRHe7kLSummVTAA7x/1DHR55rTpYGAZMAHELDlQRXWs11OH\niaVsXtlrUlo0sDtlHiRDsBOam0EHsacoWSoB2bULPOGQkAcIjzWv7U6iQKCr\nCmz+mw/bTAmRjcOLQbyeU6XppSIMqnMPrDhSt8cy36lhbYUgTSVoEPGgvayC\nVJKkgVf7QKgtp++NcxCYbYzafzUZX7gbQqGQi0VkGPBCA6DYNUWj5Phio7Kh\nQwLo5OudxK50zRqvQfvXZTDdHkvjVL6KX/zKTIs5Aw3Mts+3BLzCUP6Jp56x\n9wI7WMMENSSe86vNE5dCBYdHvP8sKnAmyBKF6bI1S8cf4pvI03bfkx7y31wP\nKfDx5er+x2Zbm+e/tUDO2jEZN0KA37+j2J9oDi1VKeizIBKGFJmgiqy8s1+Y\nr91toAjet75sSgJBQbHMZ+p+Xl1xTHnMl8vWG/xsprurzBn3Eww14q+Zbpkv\nb6q2HP/0KQlyIzRjwP12r9z8AtMv7i6P7y5ZNSZomhaZTP1L5rJbgH5+iv8z\nhLflRk3MxNnoJu1odce7ZSwCrsLiyDORU0/OMvj0ppnwGn5VxoM9o97sQk6n\nfFQ0XmO3i/0p0kNIAy5Om1dL9KQr+ly7TNtZOBi9aQpEH/ye7GkAhSwq7yGc\nxrhQ\r\n=s2aw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.4": {"name": "@testing-library/react", "version": "8.0.4", "dependencies": {"@babel/runtime": "^7.4.5", "@testing-library/dom": "^5.0.0"}, "devDependencies": {"intl": "^1.2.5", "axios": "^0.19.0", "react": "^16.8.6", "redux": "^4.0.0", "history": "^4.9.0", "jest-dom": "3.4.0", "react-dom": "^16.8.6", "react-intl": "^2.9.0", "kcd-scripts": "1.4.0", "react-redux": "7.0.3", "@types/react": "^16.8.19", "jest-in-case": "^1.0.2", "react-router": "^5.0.0", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "react-router-dom": "^5.0.0", "react-transition-group": "^4.1.0", "eslint-import-resolver-jest": "^2.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "6ed405ba88b625ec53d7cfa78c038a950bafc1fa", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.4.tgz", "fileCount": 31, "integrity": "sha512-omm4D00Z0aMaWfPRRP4X6zIaOVb0Kf1Yc1H5VE4id9D0pQRiBcTtmjbN0kZgT8rQGxHhVAuv1NuwFwMTwKzFqg==", "signatures": [{"sig": "MEUCICNO6f0mP+M2XFwrEdL6qT40s4Xml1WuuBiDJeZT6scuAiEA0m0dmPHM7fg7MAo2eJF/C+BS9tYAXjmn9fIPo8v1EvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEi6rCRA9TVsSAnZWagAA2mUP/jKDSejfKK1TEZYOGPWn\npndR/OI9OZ9iGB3FAcWLIKEbM04vhWUtidgF29Lxiy4G/rR4EHH+fpV+MN38\nixVrGTSILg3QsnkKSGZDBwlS5BN437vk/OLiHtmlvchC9qfjb6c1LWDkm4jt\n62WJ2KtHf2pqnj/wgjnvepTItqh/arotV1wCyI4baY1yD7mHsriXOpZjZ9R8\niBDTaNz/PlIeR11GQurPQxLqbt3AoHRMxPwQmczmgXo3An0UZmLgT8pGMu/W\nPZFdcWqexn+aOjs1hT6OWTEwD8TyR8HQ02kqEsxbJ+tTmRPczSjv6N4Rh8OC\n2HmSMg1Dy5TwRsoLYTvgLmTrh7wfipD/IR0CylydKq9Lwx0y6tG2tjL1f1fx\nxyp3tbaXnV8CziTk97d0nJEP3ra0RTK0Tv/xsKf6WjyDQNOvuhyiEyDCVRSf\nhCz8R8dKjoE/juhxJ6Roi3alLqHZJ82MryLd3dt7u52z8Q/lgJAM6YkQDvU1\n6fRCBsk7I8QPt+k1QJXv28+6p6f4+PgRWn0K6+vXk3PFSJo6+PKN1UuCo1Fy\nSDgVcdi4Ujb9r0e8fFcS6gEq32aur/6wHwj+1smccOSXzZgVFfBFn9OqheGd\n2/SMuzuM5N9Q1hZl73GOD1EqwUCWakOVpDAVF1NzuI3Xo47XuEk0/impXddh\niXE3\r\n=OBDY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.5": {"name": "@testing-library/react", "version": "8.0.5", "dependencies": {"@babel/runtime": "^7.5.4", "@testing-library/dom": "^5.5.4"}, "devDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "kcd-scripts": "^1.5.2", "@types/react": "^16.8.23", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "2301011a8c5567eba59691860df19a3cfc9d7425", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.5.tgz", "fileCount": 15, "integrity": "sha512-2EzVi7HjUUF8gKzB4s+oCJ1+F4VOrphO+DlUO6Ptgtcz1ko4J2zqnr0t7g+T7uedXXjJ0wdq70zQMhJXP3w37A==", "signatures": [{"sig": "MEQCIF9VqPy1O1zK+SsZqAnCUUvHEjeQRjOOAQyxiTJGXR0OAiBK2Kd79JiV1TFFd46VYtSHpqJNqdw6f5Ny9vL3ORJbrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1673619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKL6dCRA9TVsSAnZWagAAwvYP/RmR9vssYda/WHO5WnLP\n6wTjECKJKNNXwLD8HXocD8UddYjrjJlSDaQDrGpa3VcMi3AbEhnMhATb8UtR\nI96xiAR7RGSjoaqDYWKvDouKjL6zLtL74KEVzha+l3yisvyZrBZb9VtZdQSE\n6Q0gUiYU0vBqQOWlNOYfUS1pNdxn5mAGnp0jAJ4Q+gc4CoTqi1mtNVJWFPZe\nyP0Hfw5SGuuQPsQujiUMHIk+sIXb5VpP6if1u+fle872O2U9NM8HE1gzCCBt\n1chKiiY45pH42kD4dV5GNAzIktuqGJCW74ClCQLe36d3E3n4gRSJvVJEVf4c\nPE+XVwkjwcyI8dAcHujBn/GBH3BdCnyvW0AKosLbkK8rBt2gfFQU6rh5ghwT\nMDziyXXNKh/uJ5Dc7/B5WH0gg+ETwKidW+cyEUlBE9GIVRi+8JJJQJ+RgrKX\nQLAKuWiEuYOTySXXpOqW6NXwMubwpt347+4Kb1L5LdbqJX06F9LpQ1VTuL95\nj4Rf0OtRB+0s5rvP3RZLY/oagoDVhON5YG2GPAZOTRoMSMfpF3SmkdaFDFki\nxmXM3e6edNlxVGb2bcvvFt4ycnxJKEBHQEcLkr8YY2cOlPj+cE/JFuFAEuJc\nivDMZad7j51dlNF6qsNU93Xxu8MRshSlC7mpyXBu5Z38q4dj5tpU25MOv8qa\n8RWQ\r\n=ypCt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.6": {"name": "@testing-library/react", "version": "8.0.6", "dependencies": {"@babel/runtime": "^7.5.4", "@testing-library/dom": "^5.5.4"}, "devDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "kcd-scripts": "^1.5.2", "@types/react": "^16.8.23", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "614340646f32027426377dc6e5895a566eeb61b5", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.6.tgz", "fileCount": 15, "integrity": "sha512-/4oeS0eatnHvAf9yuxIg1/8pjl1OrtWN7SLpDFEdxFjllIAEL09xXhA88C93vvlN+8enZa+xlXJ8ecohECT6Yg==", "signatures": [{"sig": "MEUCIQDHCoAdDFFAsS9N59wVY37OX2qELetPiIpD917TIz8zHwIgTqhgR5zVhC0uf3LWwYxViRCEgLsJe3YG7nPft6PxsMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1686258, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNvWYCRA9TVsSAnZWagAAxXYP+gPovnhw2AtqRJtQNuNl\ny3hWInt/oDS6qrvob1U7cVycnGbWkZqZPAyJ+UGDl3ttDvKF/Ry/x67CMxMs\n+ft/Gkp9IYvYML1rOOYUYH/oF2H9nwvGo2KDgbiOXbe1WcR9j27iNyzOzhnF\n/RQjs/v4SAkHDV/wIvDhpldqxiIQa+ZpyHjoiVwyQtRtv6Iq3hWYaMtkzFki\nJw05S4ma1NJJqCTGrLdFxHgm1R3ofOFFybAs86lBY34tsGuG/utoMRUA1BQo\nEUlTWyex0KytgbVDQcl0bMjVg+roFIMETh/2AvLtJN3dfqU0vEl5EcOdzd+p\nCdaePb3bAee4O0bAuvpxsw1tm99DrdgfCMPT9lWpvwdrPi2bP9jpx7oyzIBY\nxlY8Qss3p5+4ojlh3IlDbM5JAY4waxc+2Rqc2BtMjSJLAmXgcEBiIzChTtrF\nCV9ZVIG+BETnyP6ssMqwmtYQXFg73Xcjood0v+wKthYqI8+CErNv/6QvWkrE\nvWk1Wn2KZsK6neKVgb/ngeLksKNLfNafOuoxD+2SMEGGygb3hQTNpkwQZamx\nol1t+ZOOqYsCiBpBzeJlplkukbDb9+t4z4ExF0DWEZLsSAnKlkKTPrIy0xzE\n9psaQsqrM6hOh9OuXNx9e30jNagGQcm6+mSF3ruCRh5so3NfNBn2ImYx8VyC\nZ8rI\r\n=6jJJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.7": {"name": "@testing-library/react", "version": "8.0.7", "dependencies": {"@babel/runtime": "^7.5.4", "@testing-library/dom": "^5.5.4"}, "devDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "kcd-scripts": "^1.5.2", "@types/react": "^16.8.23", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "b5992c9156e926850a0e3a7c882ae1aed83b1c77", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.7.tgz", "fileCount": 15, "integrity": "sha512-6XoeWSr3UCdxMswbkW0BmuXYw8a6w+stt+5gg4D4zAcljfhXETQ5o28bjJFwNab4OPg8gBNK8KIVot86L4Q8Vg==", "signatures": [{"sig": "MEUCIQCXR88mv2b7IiWQSZfEuyAm2NVv+pwyboBAt9VNVndTrwIgDQreHBC3RYcPb7HcAhZkxKCSEofspw5NapbAjZy30vU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdOwyFCRA9TVsSAnZWagAAJZAQAILvBssiS6n8gFYC8PhW\nNyXZjpdXI/FqTi3OILB5SbRkBF10WWDf3fWIhh+xNHQlNIOw6/q+qHbRhjQh\n9R1EUEZ+HnGgxXNznLgSJprqAtQKWz1JdMbft1XbvkA7n49HkL6Wl9cU8o8l\nmaZst0id4snWw7J0dwNDsAZArv8RIl9OS2Dcg5aBDlqU3DQvRPYaR3iPIEfi\nDhWvmNdyYNJ4q88hi7NwMXgZi+2+k5ALd+c63HL7XIuyXX4uZxre6aYDm7vc\n9AsASsP1QJVHFTCFaQ8ZO5AUBZQVXdp3J/0ufC3+Tg/Vvran86MVCwYG8hLK\nMJAFoc2+i4Aze+zer7ggDRqtoXvGXWoAOv5zJeURCUlCS8iRK652QBNjBI60\nu9g6VwZ4kHE5GjknVoGs9OAx3aHNPcB5lPSHRJZTP4lx1MSwPD/yFHadknxC\ntJVmgrWhH9oHGJziH7WQm68VC+W/FiUToyUHDT+REOAB/uOoq5ZM5Z2gxo1B\natFdGn5IaEtam68uzTTfBucBVDdjBf54iXH4BmVSj/Gettc4lwawQ35T01Du\nuGs5zrBJXzzGkQCusDxyxgpYgfSxkj4g0HGvqUw6RnImVDMOgPlF6ebihYao\nzJzIvOLOowFnrNugzIxxWEkxC7Q9wpyGbwlCpdFPP5Q1FvU96d2naGrnHE+0\nq+gO\r\n=fIgM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.8": {"name": "@testing-library/react", "version": "8.0.8", "dependencies": {"@babel/runtime": "^7.5.4", "@testing-library/dom": "^5.5.4"}, "devDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "kcd-scripts": "^1.5.2", "@types/react": "^16.8.23", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.4", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "aeba59adaf2b9258117bea927428a8fe4240db06", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.8.tgz", "fileCount": 16, "integrity": "sha512-GvA5rcyT2TsFozqijEejXI64P4mMmzX2qkEfb7JNKrkdFXLiXdZqSYr/96PerLpsN23gJkKicAhqqT8qo9Kr3g==", "signatures": [{"sig": "MEQCIEEF9RSNLqrvi2qBuxRdFK0oFFoGasvuQNpS9cAhpy8EAiBxo3NfVTHmBKgiIZY61RjwqcTF3yWCKfrEOq61zH2c/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1691823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSeChCRA9TVsSAnZWagAASbgQAJY4Qh9hdQQam3pekspy\nz3tZjjvBNKJ1NAVxKcxSIMYMJ8Y3/fljMkOP1zxSHzlxVB/8sIfK5otGeXEG\n6p/qaH5H6QWZ6ZuA3USYkTsSY7f3VKbuBbArV3WXc/Umii2X9zbNX+b4YNbI\npRP20VoRq8ep6YqSGUo3KONg1pGCO8xnraiPczek5lJgm+SciTmHtk/2ZOuL\npersBY1VgJ117oU4md9p57FrubQV6cx+gvQvCMv6RJ/wF9Yxgp8HMVSkTHr/\nrSSXaNFMvk78epj9kpFxitNPWctzeDGh8F7KHhmBgTf/Cnz63SZpPlgxTjql\nvxGaYcSoAbXZIGaOwUT96D2rF12moq/9uAWe0m8xt/wKnPnNDui5ttYzCA9J\nFPM6lmKqB/yuQdN4To+o5l83enhQ1S5x/uS42VsAjC9LkcuZGaLR0L9STJdc\nBnWWgiEhgb1QKqii6jtM66TQeDKbuCcuyaEr9znvXKKxy6gvAu/CnbTX1rvC\nkSIWEzpjUMOyzOZLL2d3rzMVufrfQuCoJtvAOa80T5pCfrrdqMdwUENprnsR\nQZLB1TeqdEHDETvk/6LZV2KF3BogZzGRYWyZCPvr813/IKntqJLC9sJkNHHc\nE3M+CXyhH58Bb8f69/KajzZk0KISJLuo/tP83s2giTJicS4jQqXsuMzdyMJw\nJ1Hg\r\n=lP5j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "8.0.9": {"name": "@testing-library/react", "version": "8.0.9", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^5.6.1"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@types/react": "^16.8.25", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.5", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "1ecd96bc3471b06dd2f9763b6e53a7ace28a54a2", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-8.0.9.tgz", "fileCount": 15, "integrity": "sha512-I7zd+MW5wk8rQA5VopZgBfxGKUd91jgZ6Vzj2gMqFf2iGGtKwvI5SVTrIJcSFaOXK88T2EUsbsIKugDtoqOcZQ==", "signatures": [{"sig": "MEQCIC13grYkHMGxpN35LiY4up14Wo06ClknTmxz7OmBalwvAiBOTklExv4+8ZhZUkaT8GpN6t1KKIJvEf6LFcsuFCOqjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1690395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTKjRCRA9TVsSAnZWagAAX1IP/RCF25TCMy+5kKYd1Qlf\nfcNhC658Ri/Hp/ycicmM4JNsPlC2yT0/Kmcof3yzb2GJUlr11YKe9owQX36m\nBx5X1p0IFTcBMS2l46QYZ5N9NfYy9dA+g0o5VKQcyWjrA0ocWoAfaVuy3QVJ\nJ0ih+Ts9V67HF7dBMhGw6av1FJMAvDYPlxqqHtHw7dm/C6lvMsC5PpcLlgsp\nvTsjOJ3FmHj0k1rAG16fJbEWrJxpHbvHV5p5za8iCcuFi3xmWJ+/FEqPc81R\nexdbcmSxn08710FzcabbZjB/KfmNqXHjz4uVTfqv8NAFTy0GQcVQvUCGUErH\n+tco03Y6p7Nugz7oLQEdU/i4frzaok0C7nDbK5r8HJOwyjFNh6NfRHK8xxbg\nyahBVwKNMeaAB0CE5/byIQz6igpuFgSC7kYOH6/CP/vIXz+c1PFfbP4thFdD\nd5VYsE7dJz0VKJIfm9/uuPutQ3wGOqOcDs4lZUAV6FgxfrL249IcesquwJk/\nIZ93GQum9NLvd9FEuf8y0GWI/lsurmlXR0w964PFexE4zAOO6SQEc3VQj05m\nAgSKg9B1vgq/PCD2qp+1VuW45R9KKrrZ7brFqMvOJjgIGMxgEvrFtXWm4By8\nnMLJyCaB5L+60VsMj8Me3T2ng7hHqHt/Ku0ukpMzbbshR0YKS0dyiuW7GSvf\n7Bg4\r\n=3Dn2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.0.0": {"name": "@testing-library/react", "version": "9.0.0", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@types/react": "^16.9.1", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.5", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "397f63a04948df79fc17f4cdec8011f8ef3d79a4", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.0.0.tgz", "fileCount": 17, "integrity": "sha512-eQDcwIOGqqd3mfP/hym40w4VVHHfMvRkvA0qzzcuPb8eeiUVB0Qc+wm453sIgvsQZ2HpPDQvJEVOYF4tJJ6rsw==", "signatures": [{"sig": "MEQCIFa2DMTVUIEmzWeGknbCAieJKP/fsyrHemEPOFPSmM3OAiA0w0TUu3/GiLK0WX6B7lHbU5tWOs6OYEMYrUiC+EevVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTdC+CRA9TVsSAnZWagAAh3IP/1OjrfqHCmaH3tkw1UuE\ngBTOtQ16H4IuzqTuAGE4keuPyAwvj2d+aZ0TfmVIMNtxqMb8W/hitcElKJ82\nB6IhsD2O2KEJicOSFMTZVsiP8TC0oLjrP2QxsJTxkdHb8LWTT/jXKhMkoEj+\nwV20FJOy2SlBN/XLsOULxjT51Pm9I+HqE0q99+9ERL4Z8Py+LFRPNyoOEG40\nMVIo4XHbicdXI2cvmiPSxDxLS57qoM5mud/5m4ANg2R5RvG1zaqZ8Ni1xtlH\neJeu3vR+5fa2ekmrUci9gc0GSpHSBvUF913cH9Vc1uTNg/G3P6ERMicWcBmL\nKvr/lJ/xG+8TFy8xwbrbaVRd5Pc8eDP8JX1o6c8iviR0gvIinQAdxGWWpI7d\n08tmLMR6EQQmRmB9DWhoRBQUF14XVZ39DePvp8T0hHsf6wBgQSB+WV1FpfUj\nhcWGk1QUOPTONdTRNL6n6L6c5W+/GBQ1VBo9wCD5XHnXhkw0XD5b/YFHdNo3\n2qYoBBdTss/RQbhq/k66IaHs2LLZe4J3hX7vKSeO3vA4TZirYRMipu012y78\n1XEoiTfJnRQLrkxbyxM9HX3z/IlfTdQt1h5etLvZhdW0V83fI023dJ9Jv8C5\n1miz/EtHabKVF1hZhNCN0t3gUPISllFnFBYU7zZj5o7JgrhUe1UkfIIixY43\nK2fA\r\n=Nfvt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.0.1": {"name": "@testing-library/react", "version": "9.0.1", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@types/react": "^16.9.1", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.5", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "5b908daa237b4c772175baf875c4ce7170f682af", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.0.1.tgz", "fileCount": 15, "integrity": "sha512-py1d+Yjhhvv7aQXM8b8F4vTlOY/bkt5bidErTyzl7CjgGFM7eVc48c9UyAUlLkSFVFDU8UlgeW96e+mXG9tzIA==", "signatures": [{"sig": "MEUCIQDhmkTUUg6HzJ7MtlhlhGzQ9vxvQ3vlE2aIyLzWnl6K7QIgFNkB15oM69PfaxPlbMCSsWh3CXsdgSvc/2Aoctybj6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTdRkCRA9TVsSAnZWagAA/q8P/0Ev2qKNGtRJrm1O8IEE\nO/mg9d4XXi9N1T5e9j/qLoNZo/YS+L81YFcG7EPIxfMJOsAYkevB9pY0X1jQ\nFqoDLw5nXmAmje/zNw5RsG5/We30L3A43RQWBJ5/zYO2nwkGZECLnECe059E\nheC1b0Q/JCclLqwrDosRaQ4twOUQq1BfZxQLj1HwCXql45xWr8cZH1qk6ZD8\nzNDSIIDGgotA6E1XQAoSZJ7mZOB8nqSow7FuVsJkMOWVDxvhqwM/3VO72dVE\nxoh6n0ibmg27EBIFvd6Vz/cVfdPcIPtLbAm4zXBg9GQ12uqRVfMJGurwY+We\ndxlxhw47SNLNTR1xcN5KrZJH6fuJwAIkSJKgme1R6/b1s/hrX8v3Pa2Wx06E\nAQ5bKRcjfY4sxxhIyaj7aaHqJh9XfvMN7oVgAUV/5bjNhpop+ymezNvD1sD2\n8bBRKjCcWErqc6fhw/lr47QAg8aJqDKbK6gaQgRhsb8h1lOs1dzGejzhkzwu\nAlxfWm2gt/fNn2OqlsiG0hujDbTVzhxz5dWW1G//jY6OqT3k14z0hZfyLfrL\nFL2QMqoraFxl8aNmamO9+mOUxtl7OPMTW2iOk3daIfT3f1WEEKtz+a5IHPwV\ngixJSP5CHSHT/kGBJeQ9bv54rJKeCHMsHsm/guymwmQpZ+aKzurvzJBXzECC\ns6Lc\r\n=Dxps\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.0.2": {"name": "@testing-library/react", "version": "9.0.2", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@types/react": "^16.9.1", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.5", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "6164cef63e9cd1a721ca8d5f2edd23e6387057d2", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.0.2.tgz", "fileCount": 16, "integrity": "sha512-bM7NczdgG9p/Uni5kTAACU8ofuwbLsI4X8bKjuUAxnrQ+fX2XMbo2juur1sYn9wcNReStxbZIXAwcRcUBi5aug==", "signatures": [{"sig": "MEUCIBON+R+wDrn5OfRRGPLw3uaFc6vZb8OgVNLPWuWFHts7AiEA3BCJbvaPsNwq2nCRs1dDx6fYpi1+l82XwxhwGBfRR3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTdnRCRA9TVsSAnZWagAAVZYP/R5YFmJ4a56scbwJIc6z\nh09WjvTH34S0L4/HnQHsIWmyAmO+Auikpsol8oPI9BiITdzNI2aOafbe2M3e\nm7RjQ0YCyqebP/fhQabKmw9omwd3Y9x2S06dhVrwTSdEVOzs+D9AliEN2hF1\nuOtj+nS/jVPo5tG71fwy76q+InOriYegsVfkUidwL356Q6kW93ZG4HmHp3go\n31pZyUbLg6DRD3JlEyiwkf3hYLNftTe6Ygr4OlK8w8E1XpwYhqKgRUhE/uWu\n8DiL1bbTtS65QerWOnwzqNk0zM8uUtWGrviR0j5h4g2HoSRoIAudCzrECCE2\n5F16l59pgmpfsr6FSy+YBiD6XZK7zelztVOHcc7hOakFvYuFJ7G7/4Lg/Cb7\n1vqQeRwx1dNvMuQ+kGfApjDcKYgnn9VfkggRsm3uqylDlckuEDDINcoLh5pl\n4ZNqDDyemiNPopPwQSJ1A3B3gQNSAnodWofqPGxuQe6BusR8w7AdwaqhgSH0\nEs5SdAeIu6wylEDR/l88F27t0LR02Q3Lq4UOngefE8dsrftRHfIBstlXxjge\nX4gO1Js6PEyARfCSBwywmpY9224gQebDieEkVedDhKZTFi5/LA9hcqE0IZyN\nq2dHxGpR520RDofMHowGFXiKvweUmOet54v+AdFYRdvOGbtdsNfb3ddVuCAS\nwnQX\r\n=C/tX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.1.0": {"name": "@testing-library/react", "version": "9.1.0", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@types/react": "^16.9.1", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.5", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "c00c4ff90c5f9015365f63b7759a57c428b6ab56", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.1.0.tgz", "fileCount": 17, "integrity": "sha512-XMtU8AmGX472c7+qXaaVZ2QpDKPA56vRaVEjlFBPDL8im2erM6DF0KJlwzLcc2wrvfYkcMDbreFyL0vdalubrQ==", "signatures": [{"sig": "MEUCIBxIePE+am065WZ7Ns5q8dcr6fRgMIYeWd913HZuUo9mAiEAtcqr3Lgk+ZOFBu8AUIDK+lCtJVMjXllYJGJpVH6obPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTwElCRA9TVsSAnZWagAATbgP/0FTaQiMZ/i6tUIAqhqM\nUEeksSQUVdRzff6ubR0TjbLB0BqwT8G9VnEQBKWxVtLeJZzutvUAiYI6AuN2\nWh+GxsKVw7q09oMbsgsZtxFa05BRerUJH9yxETuRGAA+bvh212nRISdremKp\nGsB0bI5XjwXyCIgpwz05rjRiB8VyIOkzKeN++5SjaO+21879Uad1vZc2C0+Z\nFUIFwXTDlTW70OkS57P328dOzad4azYvAZBSJykQdv2zeMUgxYrlpHmS367q\n2oklDP4V9SvvXiL/LQ7NczsIzidwmOjJbmBBGG7/CzFKZTrfYlJ5ALDJNPXU\nl5y6d4Rc6w4m+JgX7zvbi3asPIKEG6nUpknBPvx+kRRbTG6f4SiOirhL5xKu\n1FbQ2YA8A7UK/2pyzxALmjYGiPe8RUYk3emE8cXiPq07C3MSxA8gwN2jPQA0\nz+aDFtOHM+wGinRBzq0HKBnXW6lTo5GTDLpgIFNAjSd4IsZaqlaIfTg9b1jS\nGzJDiJ1/IGS9cV+C2JY3IHisg6zaoNN+hNmi3hA+UW0t7lWszvWAaDiVk5A3\nPqSn5FS8tuLLPy0ejCd9tQXKxuTGUtuhpRdx2mOO1qxrnSMsv81WhjLTYJvi\nlFcmtLgqR4/WSLKtaqxFSscG+n10FlCe45jlI7q7XT4OY4tBjBgp/JwCmJx0\nF0rJ\r\n=C8QO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.1.1": {"name": "@testing-library/react", "version": "9.1.1", "dependencies": {"@babel/runtime": "^7.5.5", "@types/react-dom": "*", "@testing-library/dom": "^6.0.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@types/react": "^16.9.1", "@reach/router": "^1.2.1", "@types/react-dom": "^16.8.5", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "ee5b15c02b64cfbd14cdcad87fee2ed5f28fc2d1", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.1.1.tgz", "fileCount": 17, "integrity": "sha512-mjX9l/onA5eVe8/4/obe7ZAw05U8s+kinXVglySVOMJo/oCSam9D9Dcg+aYVGsuBEuYV2W9m2LTP4KbNZh8BOw==", "signatures": [{"sig": "MEUCIEKqucKVwl5/x+3QGoLttu3N6aWs3KnVZKnDP67IFs7LAiEAyYJCJ8nhAGAu1p5PWTc97YnUoWf5BtLj/3avKW2ZOaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUfnBCRA9TVsSAnZWagAAa/QP/A7ETGYx1C97jPzhLHGt\nRaj8LcPQtmfBfoldIVN+4Nj+vtpjf6ogUK2hpXgEpDW+rPMzI5HdLT+Szfaj\ndiVJn+s34VdCRc9Np60GCfVlp/MuFOlHo8TJHMOCVtLXDJF3Gr75xhhDiEW9\nfmArU9ZeCo0NdRnLVDFuEsaHGb8rUSoU9ZtNCM1ZUEp6onU3r6ZEZRC3kTG0\nWnB20xpEG4zO9Jo6jhHPzwKkCw3huBQlUfEj16CWD76Q9L7ftjd0mgwtBQV6\nA25G8ute0ldbzIZpmYUcwNh8HmYdHOSP2jDX31/AHB24BSAI+EhxUWHMN/dX\nxhLABzKI6c7VUvf11GAn59XrvdLVtjsMPhQ8A2y4kSqL4bu1DMCmJMP+ERTU\no3B3x2v3di1ROABBuAmODC7noCyRznASp0WLMPFBVC41KK7wPiZ6h37p8EVe\n+a/PVAUgbcS0PNx0umIzYvGZzSJilHpysAsNtwcDapIboo4d0qy+pwudvEdV\nCPXdhAcjm/RvveDrO51Sfa6l78LeLOGXwMD81gburIKCH10l7lDMpddKBtze\n9rVcgFeo/wcsq6tOxA1UUymDbmAyFhoHI8V9F3lVyRbGcDu/fBUbMfVI27/P\n/Mf8oZFeCLdJXs9kR4sDb1M+LyUtPzSwPfr3I/btsDzo+PtQosfDffnjkaxw\nYwRm\r\n=Qd0G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.1.2-alpha.0": {"name": "@testing-library/react", "version": "9.1.2-alpha.0", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "77d80d1af50c42936a59bfab3243dcc6710ef928", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.1.2-alpha.0.tgz", "fileCount": 16, "integrity": "sha512-6AAD7oJ6KfDre6zweLLwNqyz/AtwykW8spYOpve6vqiMX0Y2nruILvm6oA0jSIzuvFIrXowLmi8sP3Ms/6MyAQ==", "signatures": [{"sig": "MEUCIFVxUboQfaQ1n5aZWNFsmeG3J4h+alqn5aKK0J3dtQNtAiEAye89tTBFpkNdlB6rkxqo7VdBRBU0dM9mZVC2UYAV4ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVa+TCRA9TVsSAnZWagAAdBIP/1QeE90UUZgkpph2oDI1\nIlfABc4JmZvKciBhGYKvAeswQCWb3oX5bbcx/NGk2aIYzc3WllqRWO1BAFKU\n+YetMhfpetDzfNWBlMkrbjfb9YawG2N9b9tm4OOeBZ1Ds+wBR93C9FA1u1sR\nRXzt8AEPH6il1VXoAbcaLw5SW+n7j5vXU7TjVphow//n+kjvxznrC8oSpPt4\nfyOhcuiWXwL2D1yebFSlP1oHXah9RwlBl0GKYejd57dIMtt1dnIcBRHM/utA\nSUW/yeIJgfYWmA6f97tE5+aIrUCqf5CzjSNMintQCCI72HdWXc4Cof/WuyjE\nZD4TBak7P1w/61HXgeyTFAWT61ZNIKfUERsvhCZ5HMjI91BLSqp5wbnaPnO+\nT61FkgDTFzX1oAjcnMv6ydx4htcjRdO1UmYbMHHT6Zq2pOTEv41GFP1pitk3\nil2xXiITo5Jeo7PpzJ74BSkUaOTgLUxiJCAEaz3IyNoIXZrnPR8jGNob0p/f\n95IJKfnOs920FcjvhnTdufOGPK0iSfWcvWv8L+MIdhchTZDBCBNVffsFI8hJ\nl+3O6MWKuWop6a0FD0vzD/tF8zkNczHtdQ0CRAffyUCqM5/a1JWJ7O/MMpRx\nStsSpgUaulDXIjCoJshXhQ2JeBW1pVbfMRAhDKwzireosUvTOWMPSw+QnGcF\n1044\r\n=9iq2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.1.2": {"name": "@testing-library/react", "version": "9.1.2", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "7a69e67f2c6137606bbe91dc6e66eecca5f6bc49", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.1.2.tgz", "fileCount": 16, "integrity": "sha512-di3awgq9VYfvXO/ksW9glDEl+Zo8ZwdSv9KMAoLRp/FrpahB1ZouJwlyh8GZSiPc1r+KTpdon6eBFRSQfXaR/Q==", "signatures": [{"sig": "MEUCIQDxzWKp1GXQVnrOGgSXzN7WvWBZL7+5yqXNEZl5mrdC1wIgTXt3w8B7kIIS5LEtixgvv2ZGbHClmsH1+ir4aiCf76U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVbMYCRA9TVsSAnZWagAA4g4P/2b+uKgcXueNW7BgejeD\nSICxr1x167dddBDH1M7knIuAIyVv7KMS/omc16pvJbvSt/DdnAVPdwQN4R5i\nc6QLEUM3CcfS8nXhaHYuP1yN3RPt2VN/071phwGZ5qlDfBI/W71cDM9KUF+r\nClydmeeLbfxZlCf+29DBscO5gOoyIp/YqSHob7v82/1CnEbHeUvhcLZhFFKk\nDQNxJciX6nJJPONVXDHcGGdP2VPCbdsRFqvkaxy54tp/z/DhL4CcTvR88z6s\ny/WUYag+PNv9P8hF/Zyjsi75Boc2opmcbBUxS9BVBz7WuuZToSKkqtEIp4et\ni+TUkTSOozXMFx08HX6mRNABznZjo/metwVV4oTLYMgKyXjGKb63wsybV2ln\npsg0NLomldjyzxc52vaqanyKy6tOKNTbNxqHu5rfqL7X7dhPCxq7XR2PNTHJ\n0OIDFHNdcqkIfTM8UHEgGn5LLkJzTCa/kC397RD6ryDiirJN5forxOy9uFb5\n4+u/pvOTfFNlmCA1JrkrexYMidPqE+/UzKI0W2VnmZNijlHZoAt8JEGgbV4W\nwjcBq0BHj7UG4zdjCUMg4kH2fxUPbKgqKzcmS1dwaSS81s4gBM8mThkySZoC\nFn5ESdNxwuT06gZEHV0tpfGbn9Eextm8HSHghm3PhU4/xHOajRKlzGEoiNUk\nfs2u\r\n=Y6MG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.1.3": {"name": "@testing-library/react", "version": "9.1.3", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.0.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.5.2", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "3fb495227322ea36cd817532441dabb552e0d6ce", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.1.3.tgz", "fileCount": 16, "integrity": "sha512-qFVo6TsEbpEFpOmKjIxMHDujOKVdvVpcYFcUfJeWBqMO8eja5pN9SZnt6W6AzW3a1MRvRfw3X0Fhx3eXnBJxjA==", "signatures": [{"sig": "MEQCIDK58V4BRvnK7j1Gnbkw1MmiFxr5y/4jXOqbkU3t+zBtAiAu1kMcHCXjif4EMeLg/r3SeavQcciwrYgyZjPoqE6BNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVxxMCRA9TVsSAnZWagAA+nEP/A7ljmSM0VRM8kTuUTTy\nJuWGE6DyEe+jrMGjWMhmrgfxntfc5zSCs5C1ff6VTchLyMemWRh1eTgCv9x8\n5bQItzLxeKKBAIAqPEBJJQs87190DJB4T9p6M3n8NRbXmPQqU5fuXY9VTitg\n/k37HRBKcDqpHioNyO7Zachrj9qPbnWmh1dW64H6TxF6fN/99dl3GMCqLO9Z\nK94oE7/rQFXd2L6aEdqPs50yZbrM0lNsFG9SuJCFTVPjemgqi5/hf8oF9gBx\nWclq8gOgCfP9NGu66qMXULoldZTsNOm11HNOtzoqiZ+vCw65X88GY01N4mVv\nWm7RsZ5tV/rQJM/P3IzFogWeo+IK7bhXSPbn/cwuT6AV3Q559qxWpWDFUNtf\n2NQwFmLOfREi/A5RQWfy93USRC8Z0ApepEHCxfxulzy6xhwF5s6APeExABY/\n8FT4A4nuTpdnxyn6ppNxExVuj+Xpq0uvDrDYCZzpmHrB/xaG6xKn+uu49nbb\nHz9h/WxJ9yEDHqmaSze/13Y/QdYpfVpQYBbOstjxZFn8/ExkAQ1skW6N8MAF\nBsRrrtMoEaUj4YoiKjJAXZOhffcW/yXjlJ6untSF2qauiAe7BdHUH6u8l6aT\n9QsZ2n65eBl5LlSNRP9iGb47xMwHaRHq41e5On4H481vEW4g6k+AEs2riqVE\nCy1D\r\n=vkFb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.1.4": {"name": "@testing-library/react", "version": "9.1.4", "dependencies": {"@babel/runtime": "^7.5.5", "@testing-library/dom": "^6.1.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.7.0", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "4cc1a228a944c0f468ee501e7da1651d8bbd9902", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.1.4.tgz", "fileCount": 16, "integrity": "sha512-fQ/PXZoLcmnS1W5ZiM3P7XBy2x6Hm9cJAT/ZDuZKzJ1fS1rN3j31p7ReAqUe3N1kJ46sNot0n1oiGbz7FPU+FA==", "signatures": [{"sig": "MEQCIBbwTXsErVsPmdmKV9iXdtM+GB5GkZoj6YMuTaMUkT1vAiABlHZ0pAvGZQchbgl0jsdR9vtmgdULJ/HEfeLAei4EXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1691361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbt7aCRA9TVsSAnZWagAAXcMQAJAwSyeN4SNUJZ1b/4HC\nXVPIqce8pXOemohvZBxQQh/vZMkZfHat6OhfGNFMlOxayypWJ6mcRUtLyKlm\nXV8Vl14N6XskxNcj71htub9pBA4mPT4k/LNhQ6utYI5ZOzyxiLp8pqHokkWV\nlwXbQM1C/+Yo0+KKI9ajMtTK3id5k27O7EROE7zksjzODBuL7N1MaputeBFa\nlP3saBKvdl2we9roObK3mZ34+y04McHKDJGtHcjgUON1JXj8CNmr6TPZwf8X\neJbmP9443HHC2Es3R60nBYTMCeIJIkCFKx1PQmdae5QvclMcXcMIv4SXsgVi\noOpgy7VCauVNlum1WzmfgzOyejZtKnfoeSQp3qoPa0ZZl2sc/ZlWX2namlcY\ngldhWCOcD2YyrfFy+DxRa4Ag3X2WmOEaeQYYAKBHZOjJL/Y3LrKl6xcCmEre\nRssNu5LR8RpcJuCbRZhpyOnmMKLA99xfP9cA3VL7q2saNEVarm0OkH/Du2X6\n6PJW2GJJmEeJGdbl2Pm0l6oM6ZjNjuv5nIrLls8zelXmCmAEVrgyFrYSk5nX\nP8YnAexCloU6Y0BZHABA8PZCbbXp7cG/wVPcc3WRlbAcsOOFPHGuzX17k2DS\naII9MV50hcJ1H88txPqK8NQhvMTEG1E7MLd9rKvPCzUTxUCk7nac116s5EN5\nClFu\r\n=HFGU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.2.0": {"name": "@testing-library/react", "version": "9.2.0", "dependencies": {"@babel/runtime": "^7.6.0", "@testing-library/dom": "^6.3.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.7.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "143ad2d96b03c3c334e47aaf33cc2c9b7d007123", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.2.0.tgz", "fileCount": 22, "integrity": "sha512-Hr87KZflfI+vPZjgyzBKQHolQHiXGU5aTGjQSCJdH/yGPbm+IzrvuWPS97GD3RUH3rSS1IXq1e2Sn8Hmyw2ctA==", "signatures": [{"sig": "MEQCIC0EDIKieqhxJArJDBZvIzAXMngU6FJW2IIjMCdB6N3pAiBy1bY1u9zo5nq+KBOe6bSYzE9g3Uc4YHPOvy6nP4Bp6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3333034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjURtCRA9TVsSAnZWagAA7AEP/3RlblqGiGsVOnhnmIdH\nQfoxUZ58ewnOL87J4Cm+00ZPpYqAk/ugP62PrQcLm8RW51u/y4F38/PK+Xt5\nKnsqen7Hrx5aBJRBXBcWtK3slOFDwXLItjIJqG95HZJ8oAgbE7vSWlOJdeuh\nFqE5f2CVUhjCXyrO2/N+ZfaAueje0wmI1qV4zFgah1v5xVBD8loO0tcFzpYY\n36lQxhMPykUhFFo0UW4iv3mXvicmMLy2Oeqw4Inp3+NPZZqYc5mVgpTGFYk2\nRu5RVTvMDE1DFpfIWFhCpYsws17fXjweK9li49VpDQC1hoS2k/+s9kp8rFED\nnHmp/2xD8dk5Pc+FjKH37DHQ5Q3kXYZZl/CbNT2ycS0oXwDDdt14jmkcMnIu\nec3VOzk/ksLfu7pINxDlkwlugfnJNoJm/ttBeTE4Ij/bDSOL7Xf6B3HOm2ee\nHFlRtVHIK18NPYX/KQryinLTeeN1SOBIR1pELOKimzbZxiFF5/2QpaF7EJ3u\nGz2KcdGThzOp9suyl6kZHBl9prI9uZ0bOeh8vXTcdTHmwMQVUbmsB/HPs0ZT\ntD7/tFXWHTyccupyuVP9Qzk8Sl6cPcWsg0mLrcHJZjL8WnK+k1fh7AO/8cex\nfoPHA/vBlYaHHvsrVtJRm6cQBTwYw/RoMedBVT/a8intyMNLww5OFAKDUpUE\nUveK\r\n=9zkt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.3.0": {"name": "@testing-library/react", "version": "9.3.0", "dependencies": {"@babel/runtime": "^7.6.0", "@testing-library/dom": "^6.3.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.7.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "1dabf46d1ea018a1c89acecc0e7b86859b34c0f8", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.3.0.tgz", "fileCount": 22, "integrity": "sha512-FTPCwmLo0tLtP50Au2uGz4/N1BcJTnBx4StDVHZ47zPMEj1/+J2rk/RTj8SLoHRKWCtcmhN4wRmudOXQNP29/w==", "signatures": [{"sig": "MEUCICzgM+3BeV97OfNgBPSqu5cFtG5OWxt1kWLF4E75jfDcAiEA2SP97fr9qhJmYku0F1If9ZoyNTsa29wTzHJTrYO8XBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3335715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlkqBCRA9TVsSAnZWagAAtJoQAJw+8ftYI6rrPGbwtj9p\nywpvbluDa8Xvwyet5xk+SjBJvUfweZoI5I5em34MPjloetNZ8F2YZoFlaMUs\nuTfYWRSHkojbvaRW4dh8q1YCtyynDu5TwHNzWhwtdFPzN6LcoqaAVHZSovzu\n1zkchc2+gdOAWpBvlhsnGJREi5VcTuYXiP74ISX98aRJvrrxlMrbNcQj1J4t\nTUOZunO8/VJkgOnSdC5qQHPu+RD3R5gAMK7vl6IGAmgFdMCCUW947HY1o0o/\neY/WCcgF25Y6H1iLCqFAt9N9ehF7SukYgWhH2a0kbHo6Nwoe/zbtKkLSRexc\n+dJ5ZWfyu8GeMFBPUH6byV/7Wa3dr1tDcR2rchkYsvSXWaUgQ/dCRFV345vY\nzWW7ASg5H73BDKOW6GMT6HSVB2KkvBTKs5roKYP5JKQ9aOVhh73bx7wFJXPA\nd7yWklD7WqTpvpV9wsTzIV0tBrTGoePT6CxCHe5H+0t2WKZ4Bmgc05a41qgV\nRMahGEqIBrrcGN2vJIGb4vzyESKa1AFttlleDOGW2u2507ftiUxV0kIqtQLS\n60PiukaUTR5WTrnXqNN2cmBfAGTS9eZm1Pv3xNB6DVM4JxuMzV+oNQ5b0ZtG\nBIFDy1f4Hgpj1G4OX9ITmGJlNHF2ghajapCwKUCnWJ3Q/Q+Qgjd5Rep5G5nk\n5OQ0\r\n=xH7Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.3.1": {"name": "@testing-library/react", "version": "9.3.1", "dependencies": {"@babel/runtime": "^7.6.0", "@testing-library/dom": "^6.3.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.7.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "96573fb9942e1ca0ed1ec9984ea7b12b66945963", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.3.1.tgz", "fileCount": 23, "integrity": "sha512-BXlYrbxTkifNVb7rIC8EHqYXgG/rBeULqG3V0wbEAuSaZ7n5ERX9Bcp0i+9EecrNpLNPwR0cIxdKNp6qVTZS9A==", "signatures": [{"sig": "MEYCIQDehaiENlXkeHDxGsemac51RXB6r7k5svjYke76rfRJJgIhAJEpNFfAMQInAwlJvL6EvCVv9UCw+QGwqyE061sfxEil", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3346929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduc+ZCRA9TVsSAnZWagAAfdIQAJ/HJisbgRVwAAdU7CJx\nEwzNSt96adfw6odZxaRUDhihwphzBmQh7z1fg7dvDJOnXpoG6VesUDmrnA42\n1fZRnbVw8Lf/hffr0l7J8KfPY0YIOiqKOLIb4b6chmriPfKYQLMN9BiotZ4c\ndXZVLoPJWVHD3KMsOrzFtnzYu9OcHeKG6bqgUTCUr224zal+QBcioieeP9XG\nS6SPUUf7wGfO8KTVCcTW7BK/t91nal48Ges0CoCaf+h6WQhAkdoucwhEyBOF\nYxhYG9cn4MerewVNRSbtZxcehKDCHazP3tlcbJYIrBb4QMYfaJxZ88Mcrx6C\nU2Y7SFvxoY/NhYlIhbRAD7X6V227WYnkfSpI6Xwh00Lkvipen8C0OKJQAnrw\nZcgje4ed32/AHM+8TAeu3CX1NUC8pfa3W4vtNyQtUzJ28xCA/3VUYUMsNeQD\nyg8nghYoVAUb7vNo5QOIpTOr6NDCuRRZl+9+o89n+XiCOrnoFpcfhFDlEqSp\nFm/H2/aRV1ziDYW4lUtpubEft/9Ml/fbnZRVQdokGVTpWfms0jIjwm/o5Ve1\n58E+WCXTu7QEyyGjpbLX1FWlY5Y80Oq7VviAhSbn9aYFxQI3RQZ7RjD0QR02\nveOuUAvNv4h//5J6ihPphBoYW9uKt7l5qJWWxuQYn8iqgPKcoBRrTvnmPKEz\nQXEy\r\n=zWyl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.3.2": {"name": "@testing-library/react", "version": "9.3.2", "dependencies": {"@babel/runtime": "^7.6.0", "@testing-library/dom": "^6.3.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.7.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "418000daa980dafd2d9420cc733d661daece9aa0", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.3.2.tgz", "fileCount": 23, "integrity": "sha512-J6ftWtm218tOLS175MF9eWCxGp+X+cUXCpkPIin8KAXWtyZbr9CbqJ8M8QNd6spZxJDAGlw+leLG4MJWLlqVgg==", "signatures": [{"sig": "MEYCIQDoJl5xJJU2eskiWumYOFIclnLS5NSKhUT+N51aY0MyHwIhANfaIxYYkqmRMBsZOOL5jyCx78SOkXIiQPqV+Z8+Qh5H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3347497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwD1YCRA9TVsSAnZWagAAemgP/3PO1Zr1yTRO8TjFSMar\n0iuOvxiZ8wM4oZ+r1jA9G7WBP6L59rrbRWJGImbqM0/J5kxfjwXHdScMO+XV\ngR/kBxkcsrRkHuVovoZEd9tyTAwDLPYkDSUtuF6oRVAhrtJ1m6bZ+esYeIUT\n9EIyjm/DdCemyj0qo1G16RhKdopfUupnNu+jq38YZAdA9j0nOL1FcgdFhRQO\nGUU4psoan4rlBfnrLlphVXk+MoWtdjiV8pEgmbYlRgVbZmNnoS8DACFIx9yC\nzdcNi/VPlHaqaBNArXa3TQsJqJnUhp+n0WW3Q0LCn4O+nKRo0P091g7emdqD\nPDDanudbSFYJ+jAId4a9mKhy6DTcb/K6HxCHye/ssxcBJuWY63gTB/jbpppo\n2w0OppB66g69tM7OJKYjnf1KSQAfg7lXs+3sviiPPpoS2G+5DA2tLzaUY6y9\ngUnptYdfNfK48nhUBnkoyFhhsI98xKB+tSX3ZYMMfJaG+QhcZqaLfW+kqkp/\ner25yn7AlPeopheRXVlO5obbgklRRT0fDAJ6mb8ylBKD3sacixTUnU3EH+fU\nKRdIV8a6NR0fHBdkViiyUo4W1yGyW63yr2Izdna/Apu3i2Ffwls4vwXIM5vn\nH8MNqU7z/7tbMra83iY8HF8YB16YJTPOaNp5x0yFQtpfP80tRJrnDmeogcRp\nYPfk\r\n=a+td\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.3.3": {"name": "@testing-library/react", "version": "9.3.3", "dependencies": {"@babel/runtime": "^7.6.0", "@testing-library/dom": "^6.3.0", "@types/testing-library__react": "^9.1.0"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.0", "react-dom": "^16.9.0", "kcd-scripts": "^1.7.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "87fe594adb02cba4d0e86369d025ea13ad570d7c", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.3.3.tgz", "fileCount": 23, "integrity": "sha512-IuoiJR/NAzu9EuT3Fqs92sRHe/9egCipar92wTnXe3fMloWy0Q7JdAXaszzbv2ogH30ztb6Axp5XW63vOTd4jA==", "signatures": [{"sig": "MEQCIAKDouRsZ+uAYNVoyByJ6FjoT4/7Hy56ud4z5fIetMt9AiAqQrBvS+O6UySXWY0kQQk6HaK9liRnLXG++nSJ9AU6Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3347497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8IToCRA9TVsSAnZWagAA3hYP/jL96648k/+IzLUfgPaw\ndBhtMArWavREC61GXSyHtVVV5q8+encQrpX3qb12bhAHzPg/UV62cHgCxcrp\n21vEyaquzW5hYTsCfep7s8BJE41it5Sbjsr8oFIIAjfei2Dn7Ob3TU4VPFXY\nU493tmrn7cD0mhFHm0cyUq9lhb1AbK0qPZl8ZzLECTnLiCDArZpyKJGTR12A\niO3tirvSwFRslB5taKQ7bUoJ31M/p3UGbLyGyJ95G/Z3eFQhVCuHyZ7BCbj3\nU0MB83cH+T8eLMKj3S+0FuwZVQ4B3GUh9sxKPQ7IKsmA3PvXZG3xf7/ScKDw\nyvO25zhYNwqwUsvdyzKq8a7BRErzdoa/l63QLCSkAq2ILN4o0sMYXvMdUQn4\nzfMURDzmfoe1i8jb1EWpZpPfhxnGYLq8jW3md/mS7HZ+Kl5Na5BmwtDRJrO6\nRtwMOWYMsd3xDrvgyJ1YN/PcO4QMSohmVWWqX8wCEqRpnlk/+8tkJcKtyf/a\nSza+HfhRmHBOsTEE9vh1j5XHvwXMSj/xDe5JBkjbhH2O7I6rtr6xH1jXjOn3\nmHpMYKXRjRPfoWWeldtbxYIDgAowaVm43sk8THEJUBf6d1HO4WKshefohoPo\nyDDdTxiSwcMD3RlS+1PjkiviXpIi9gPPYw58eRUJc8M8x0uI6rURE72EaOXz\nP54c\r\n=GiOt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.4.0": {"name": "@testing-library/react", "version": "9.4.0", "dependencies": {"@babel/runtime": "^7.7.6", "@testing-library/dom": "^6.11.0", "@types/testing-library__react": "^9.1.2"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.3", "react-dom": "^16.9.0", "kcd-scripts": "^1.12.1", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^4.2.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "b021ac8cb987c8dc54c6841875f745bf9b2e88e5", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.4.0.tgz", "fileCount": 23, "integrity": "sha512-XdhDWkI4GktUPsz0AYyeQ8M9qS/JFie06kcSnUVcpgOwFjAu9vhwR83qBl+lw9yZWkbECjL8Hd+n5hH6C0oWqg==", "signatures": [{"sig": "MEUCIQDiKsBkYo+WxCTc9wxyURwkPRketwx/GoTh7ImUTu6QrAIgN3nmS5MeY8D29bs8RI85zn4NPRoopyDO86clFMhANdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3387846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd87C+CRA9TVsSAnZWagAACyMP/2cqSm2xl07OAvpq+hiu\nkGqIW1dyZalhDgA+5Mc5Bu1JJnLIk8OWowfnkgwHC/bchNLLe+OnFbUiuUex\nWrNNn7ILgkTSugkqFr5dhGZk+uK1ZHE2XC6nbtIwenpk0AEMdWauspWT27ec\nkP1J2eULdmARESAWKIKDAd//CLf3hzW5M1hog46lGizhcBuJ4md/2fhfViYF\nl+C8Up/aBdbFlBCF/rg3FYs0javNaZH2Tuth4GnW7TKkiA3kunH25je7OA3t\nFQiYAmh14ZsiZ67Rw08lXS3ATukI6RdmWtR7H8g9tobcNe4QCxTlWXJ3D/fV\n9IIi1YLpUGiBFK1bgT8FLMY7XbhPeQ9LEtUPO4Yt/hC+7otYQ7O/t/4pxE29\nr5xcEBPQwD/Prg8tt+xwgu1DHDeXDA+UD4XE3NXctn4bxmZb4Ur9UQIRMbWh\nXKqC2tpQVuihYIIdMkCl9dKcGh5IpTKxg5UeM1BfL7cS1kG7bfX7I74dYFAD\nFlWF8xFMFdkMYp6Tk8g+LdSNd87uJeLYE39AEi3BsVGuX7/aKPoiX4x9Jc3c\n4B9VxjBBYELU9fbIri+m3Sljb2cR3XpX+8OzV2lYVUtI/QzEJu0sXzLd7QGR\n7kdd0Vt00V7M42p5H/+QG1bhWvEK5+18qJrYdeo7fr+XztPAyFT1XlOt3Es8\n7Ikg\r\n=fl1h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.4.1": {"name": "@testing-library/react", "version": "9.4.1", "dependencies": {"@babel/runtime": "^7.8.3", "@testing-library/dom": "^6.11.0", "@types/testing-library__react": "^9.1.2"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.0", "cross-env": "^6.0.3", "react-dom": "^16.9.0", "kcd-scripts": "^4.0.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.2.1", "@testing-library/jest-dom": "^5.0.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "955771568aa3216107d307bfdf470bf2394308a0", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.4.1.tgz", "fileCount": 23, "integrity": "sha512-sta3ui24HPgW92quHyQj6gpOkNgLNx8BX/QOU4k1bddo43ZdqlGwmzCYwL93bExfhergwiau+IzBGl7TCsSFeA==", "signatures": [{"sig": "MEYCIQCGGy+btKsrzUnhKgxI/SRwP/yEdLD8JGap5OVTllbSEgIhAPEzCGaq72WtL+EG+YdZPZNEATFDjaHU9QTyIE0Km0SY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3405666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUUzOCRA9TVsSAnZWagAAT8cP/i5WthGX5zGh8RpQOJlG\ntNgM+CFMXNuNkhAH63ltm1PjtmcU02MpygCVjIoXk+79FMy7d5U2hchq7XJb\nr5nJZryRhrYx+Y8dnO8Jx/yqOKVJ6YMbWc9MV3kfuWWfWJaUu7pH6YUj622b\nDL6ugf2Un0D/M3ocJUvHjxiP6JadFPK4Wo98wky04KjvhsDpkZ/IueqhmQmN\nGi/skVMV700/EeCQun9pCXgoJcuLLT86MJmObKUXcTCfSHXfhWRVeD+89Qii\nBwn0jsPiyIvL9LU7C0P/BE6GgXGrMwukW0LXOuAKUIbDwWoDNq6Ukf52Dm7b\nZHL0QRpsswDuRU6mzmw0QSDuEkOTugckDKs/PokBl84krZk1CwQwL9gjVX7Q\nLZf/d7ibbFwvrDQc/D+hvnvZAAlVKZlH8/XQcblyUO53BLrOoDyTO9vzMsQR\ngCV36AQ8EtEakSCS6piNYNr2T1Oau34Ci3fOhPv4tE+YEWFyOKGOuKLXrze2\ng1RkP5uIOENAURL5dhgNY1lC47RVgzR/uvy52gKiy8UuvmmojjZSLz45r3jJ\nrxBNiVAegbBtfTQCT/EGeyNfHxvOl9c5PXHKsrnRU4rRHPSprxp/NZBj9RKS\nyDkvGWcutz8clEPlp+IO1/IFmfI+lPExyVBnJRvj7MLJy9seBiunPliaQhbE\nevmE\r\n=6gCW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "9.5.0": {"name": "@testing-library/react", "version": "9.5.0", "dependencies": {"@babel/runtime": "^7.8.4", "@testing-library/dom": "^6.15.0", "@types/testing-library__react": "^9.1.2"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.2", "cross-env": "^7.0.1", "react-dom": "^16.9.0", "kcd-scripts": "^5.4.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "71531655a7890b61e77a1b39452fbedf0472ca5e", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-9.5.0.tgz", "fileCount": 23, "integrity": "sha512-di1b+D0p+rfeboHO5W7gTVeZDIK5+maEgstrZbWZSSvxDyfDRkkyBE1AJR5Psd6doNldluXlCWqXriUfqu/9Qg==", "signatures": [{"sig": "MEQCIBBy/D3PnXzJ3Qt5viDoR585ZRZiPkK8vf9uJ8sQka9KAiBaHQiAuGNIWjmkXgLlyWpjFZNverpoSS/+WWW2SEAeuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX/7tCRA9TVsSAnZWagAAMqsP/ikCZCzinxWpgkXVx50U\n6VenmvGodRiJmq6rMPTMcTuiQol+TL061BVIaS+HbajQ542wSs6JQxMtui2L\nyc0eMOs2iPz7oQPScOqRlODj5bBnk4QB0AxpJLO8UPSpXo+t09W0KhkaKxN/\n4j5HdQC29c2xtOVt7kiKaBTosP9AcLqDOdC808030k2VjT7fJKr5wSuPn2rt\nZdNaFhQ+7R9SCnyigPS2ZMjPttkZ5P4i8s2E/Ojb6fC+VfmIDeyzGu1STMBC\nzZ7d29QvYFYOCO1lZtLNENUWCF73HcxKuiQEsJhWchJqPNmgf5oLbJAeiuE6\nHQqF2q/SB9AyVlx0/krurrklh1sV0EKYRp47S6tGqPuJhdY/Gtzr3HczqXdI\nf/iyDJe3U3+7jgJ4KMIFCLkddVW8a+9q2AvbsnQnkTwUOjc5mQZv/lS1sUKL\nNKho3v4A7QkIxQyFDNtvWtQ/rXR21/6dZRX8JpL1wwHLnncctvnXasjYoMj5\nKLfG8wiqOIOn+LVNBDFABUNRvEa3mkiGzrCI3oVhnKIzjRAmEsHRvU3RqDkr\nwJfOr1xhskuDiT7bSb1Dsnnp6WMjf/eVkcE5kMi/99g74AIUA4FZ+mGmM5uR\n1kXwgl7CLsGbrff2aM3d2znjbagEsKwXe6EHfqCab9xth283ac6s1SqtTDAq\nIP8p\r\n=cTfR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "10.0.0-beta.1": {"name": "@testing-library/react", "version": "10.0.0-beta.1", "dependencies": {"@babel/runtime": "^7.8.4", "@testing-library/dom": "beta", "@types/testing-library__react": "^9.1.2"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.2", "cross-env": "^7.0.1", "react-dom": "^16.9.0", "kcd-scripts": "^5.4.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "3981788da52646f3262c7ff55f0060eaa8deaa6f", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.0-beta.1.tgz", "fileCount": 23, "integrity": "sha512-ORWPXPA1mlBcZRL/YXKMnlGDuLE3liMdJDCmDcZrYJx5Ls6X+odejru7gv9uersWBBG2dWMfo4AAb+p8tCOaUQ==", "signatures": [{"sig": "MEQCICcSXN03zol8cWoWv0pWfJrwGXUs7/HoIzTJndcyZZg/AiAfwl1Z9GDq+vqRt0bRWzRMexIWJcGRDu2DOlrlRRMh1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4341812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYAGVCRA9TVsSAnZWagAACPsP/RX+FUB8c4HbHJEt1zdl\n4E7zz8vWLBQiw2nUlMahKZXqzoSQIpaBjT1Qsi1cqT2b3p46YHdhYj7YVFnt\nDXWQ2oGmumQ7VkuE7XnXIWR0AekanwL4Cee828y2Nj/M+NpLBsBufVGn27cy\nq19ND4B3z7jUNtJSQDBTzi9HSphQjvrybDbMKVLqB6ZdicXdZ3KPdQ5ANdQ6\nkATLiiuuH49k6yTcUXtxL8eBy2l0TAQIQAnRC6YT2pKeIRv6AVuUeUFPZjAm\nq2BbNlq7Zf/V1UdinyHkd8sXT0DMLUadDIRMWXDC/FVBBDytIAdw0fp8LQ63\nL0k+ZqJY9ASt4C1i8xkzMwdPaWclz9NhKq0+RsICVmf5RE+bV87yBPL4OLD+\n6236ShW0I3eC6KyY7hMaDOKtQHzCEyubg5njdh9mOboAQhFQvu2wTsUSKks0\nbvH3XyScEx6+QYaH1jALwDps7gWIB/WPrUgnNFoamI4E0YqBZ9RHkegKKKXA\nc16i/taNmMZqFJ3uVI2ssRvJjKji+QxT/2ggrQ/ZEVhtFdLtjT1A2klPOzxx\nCMjt39zqskwGAQHRDEWBpOJh+bkj0dsbAMR7o7ssO5M7wR2SEj9UM6yvPJJl\nTxK/GVBtTjXq5T0xZRrcBluhCN0yZXWIXk7Z77Wny0HMNw6WIi+ieOife4kz\naphx\r\n=YFdK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.0-beta.2": {"name": "@testing-library/react", "version": "10.0.0-beta.2", "dependencies": {"@babel/runtime": "^7.8.4", "@testing-library/dom": "beta", "@types/testing-library__react": "^9.1.2"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.2", "cross-env": "^7.0.1", "react-dom": "^16.9.0", "kcd-scripts": "^5.4.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "e054af2c3234fd3b10af405e47544d09d5a34fcb", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.0-beta.2.tgz", "fileCount": 22, "integrity": "sha512-umk6gT3PNWHp6AAlZkR5hJJrhvguMlHwiucmPu5ZFyb/E7c1s4QDXyIrn9aos6eaTa+ZEEKGl3xtjHUUD3d4qg==", "signatures": [{"sig": "MEYCIQDtQUP7yxlFZ1DpIFTNCxBYa5na0oUSALAFvcMzSZUCCAIhALpN8sC50aA0kynvFukEyxREF9+wkzlq3OIMKvQyjMEE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4341496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYAgCCRA9TVsSAnZWagAAM64P/0eTab8lZ6hrjh8XV1Gc\ndgR4qW3MgdBnazJYn4VZkArZohWQW1UF4ARzuYPtoGgFEwYWO57MH8g6zSfy\nDh2J92bLJ+ua+nNerO/YGzLZQO9xI6MBY46thcget1ngpJuE72v/Owi30CEw\nOUnNQEbN4kbisSEubGjFN6m1rl608iQyGZZT/1DGToPqw4K6kJxxw9kc6LaN\nqR/id7s+bm2vxrJLzDiNkNRyr8x/dO5+H4Ub+YSP1ZnGsMUhdOBvstE7cyE7\nOvRz9a+LccuMLn8usPXJ7Pwz5SsQ/viAuug9nBLVx99X0Q2KlDObw6UTG9rY\nGedb4qyHRbLOz2q/E1kgSDXUYNOWBehiF4fK7sVo4uV1JFhzm0IT7V1u4Nyr\nG+Nwi0wUryE1X9RUiS9zZlYTaBFMVdY+GDXrF4oyYRVbzapO6eWvU3yntmFe\nAtJDR/23Zptj52xzLc/na3dD3eoPaEnMoeIqVXt6b4ToVZxvMADo4p6S9fjc\nqbqHB00ToAdddBfA/FoFcJQHoN1FdX9I6SO9yeOLxNYVyYHAokFRbldYxnSN\nzlyBFHWEpUc7uWqZtvWWnfODwYcUNVt8I8wU6Gkhep4sQGdU4eR9dymq0n6g\nlM0dGVCAtOrb1YwyvT65zTDQZ1sibpxRuGPuDsqb9mIdnNkiqG65kT76xpoR\n8U2h\r\n=C9zy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.0": {"name": "@testing-library/react", "version": "10.0.0", "dependencies": {"@babel/runtime": "^7.8.7", "@testing-library/dom": "^7.0.2", "@types/testing-library__react": "^9.1.3"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.2", "cross-env": "^7.0.2", "react-dom": "^16.9.0", "kcd-scripts": "^5.4.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "a07338270ffc9e20f73a1614114288bcd3cd15a8", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.0.tgz", "fileCount": 22, "integrity": "sha512-5MhwMCRqYq6uK5A6ZMZRUeZT79shhK6j9jMrN/NZMWkNNolK+LNgEYk8y+r7zBlU9rg/f531G2lyQrw/Vo74rg==", "signatures": [{"sig": "MEYCIQD/SeDZh7bcyTEizyyQQhMdBtEztON2Z722Ngg9hSsIiQIhAM4tGBMja/Pn3bmdxatJ4KYO3e5ws9CnRVWRzjm8iDiX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4353979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeasUCCRA9TVsSAnZWagAA7ZAP/13cYBouAjLRQW4qd8tx\nCq4Ha5WjUYzQ4uqmdO0zROhMdZEsxkGjx90ZvD9zkevDpD+Ad01d4zEc48GD\nt3uyy8NachMvCXHLDoxBsCvIQ9Whdt4jiJjQgFwPI42nupfv/mNij+RlRPsZ\nWVhXe4Mall/RxaiT5ukujp/uAt6aa8CqKSInwg+qwNv8MZFx36yL5If60nvR\n5VkhvgPhHY1Uz6jbOOBF6xP0jj99mJG7UJvAgBroPnEhEdC64PAGXH10XAE/\n9xbEhkIdQmSI5tK6IyEFS39WjpjUpZ1mTOLnP6FQveiFIjOQNh5JYMYyVwsY\nOpW9usH6aky7M95mgZXcHjZvLbItF0kFCpVQ4DBJO6yQbiIjOEhbSxnIRa79\nHNl3tY79X1f8VAu0JoldfhmFV1Q7dK4Ehhu3rO6dBfIv774dTgkcDLNrWaYz\nZuYoDCVagQQcWS2ygbBA3avlnlymfWbbfQB4j01bb9D73mm4JP4xHVEcTsML\nVVW23IEy0GdvBwN+6E2NoVnprDSkU73EynkO+RrQaXxHqaxlW96rb/xLCWup\nQJJIWks29CNcydtQPFYQ3tpcVsYaQALlJBmvHBcW6WWtGwksikTmMPvyKjqB\nLpFnCJPThbcMQDfLp2tz5fwf25RyJf1on8ITdWy+6V27X17fKtZSwmWsrr91\nG5Br\r\n=Y/62\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.18"}}, "10.0.1": {"name": "@testing-library/react", "version": "10.0.1", "dependencies": {"@babel/runtime": "^7.8.7", "@testing-library/dom": "^7.0.2", "@types/testing-library__react": "^9.1.3"}, "devDependencies": {"react": "^16.9.0", "rimraf": "^3.0.2", "cross-env": "^7.0.2", "react-dom": "^16.9.0", "kcd-scripts": "^5.4.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "4f5e2a8836257c5bd3df640b21d7bea5a0d83ead", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.1.tgz", "fileCount": 22, "integrity": "sha512-sMHWud2dcymOzq2AhEniICSijEwKeTiBX+K0y36FYNY7wH2t0SIP1o732Bf5dDY0jYoMC2hj2UJSVpZC/rDsWg==", "signatures": [{"sig": "MEUCIQCqUcFJY55fEdNWf8r0D10VYS9E2M6JQy3412t3/XUB7wIgcWS/hALP4qW68bKCd8tdiqUOVAkIHZNdqABVVaHcDSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4353976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea4raCRA9TVsSAnZWagAAnacP/i4veykCW0fa5TPf386T\n3m4GsCJdbfFDTB5vH8du3tGWqnisI3Wgg3Yt+kN4eKjLOOcMmZFWbSXTNgJY\nMXVbORc76pcXhmq5iOt+YcdY9C8VX6qtRKwtAUYQuFBBmdiuEanLcI7RFnqO\nYdmMVrNC/vhTGfaTyYXe5leWcSHHw7bHl5fBzKda6mCCI7Qr+bknj6fNtwmi\nITc8Nq6EUHIOkqAHPf2uoCVl723d0SuigogdqvzZfwfNnmOOR2xdE6P8noeB\nrCY1DmDtWOABhhnmODRWQhS7YNaJ/GH0rgbjeTRBdI+Vo/VFcbVvXKjlsxb5\ndQyTqvUbfITYH0UD34BJhX8nyXHffoSUYu7SSxUzNByoUNOxuZw4m035pbIH\n6ONsSRLbD5pwPFLXWaUeQP1lyTooVoEJrBL6N8rV5tJPo58qj3lEzbT75pQH\nGKNUrldTp1EBR9U3Yzvt3y8Cea/uK5ItpUqvL8/WMp9XMVFcNfof/IF43FxE\n/cCwSD50hNei94qrLIP6r2S9GMExsie6BgA5wZBT1GG7uGynzL86WrfxyAti\nk0sdvKkJEWLpAxicS+XgHC/HSZqejWIXAemlMENx2VcnDE6s05XNaBIqdmJl\naQ2qT7+XiyifXXX+epVbiTlcjlLAEStlEZ/RVmy1HtE4DCVNLtnMpgv0qvVp\n+HEb\r\n=w9l/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.2": {"name": "@testing-library/react", "version": "10.0.2", "dependencies": {"@babel/runtime": "^7.9.2", "@testing-library/dom": "^7.1.0", "@types/testing-library__react": "^10.0.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "cross-env": "^7.0.2", "react-dom": "^16.13.1", "kcd-scripts": "^5.6.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "8eca7aa52d810cf7150048a2829fdc487162006d", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.2.tgz", "fileCount": 22, "integrity": "sha512-YT6Mw0oJz7R6vlEkmo1FlUD+K15FeXApOB5Ffm9zooFVnrwkt00w18dUJFMOh1yRp9wTdVRonbor7o4PIpFCmA==", "signatures": [{"sig": "MEUCIDxv2IWJnPWPu6TFhuEc56bSpFvq+RW5brzrEqoWsb1TAiEAmb0TsLUchURXRCk4IV3S+3m+cmMjk/WKghr6TgQ38S8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4564598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegh3uCRA9TVsSAnZWagAAsIEP/01F8WtHL+TGbRo3iiP2\nkdAJClz22kVAzXaF58iiV+WrIYIAVkeIWTbhtSQdl0jaJ3PO5r+h8uXwuICl\nvGo+2fsMsbCvuevR/D+/T3afgYhoEJOTXJQRYoeaDCaidOfBp81vzYMSHfHa\nnh/sa+ZOjtXVgemU3eyTSFRpenj2IfLJ4PfXpzLtX2eTG/2KnBpIlM2OekOZ\n4kIywd9C+fwHuaAcwd7OvRwzS3tsvlOBmxXkFwIErserc0AbVA5V8cuxZMba\noBt5bPskrxr5nic/d6omraFvn3hifXirAKRlGC6+Oh+c7vUI0BdLXJAwpznu\nI+6jq+msaAjqhw9c4rh/J5eJ9PcJWlWha5tPPUUs6HfxZaBrY0HGOKJRfISh\nU2eZzJPmhGMZtIRG5CEkRG3ao2M+QH7a8i+faYQV4XHIRt2SIcRBKMiKDu73\nY7FdG8p9zBlE1wfFfk58TPykHasAGJOB8RsOjeLAsMkK2VtYAVFTpVh183fG\nUe35/7qNOMM+kxp/kFrSTsi6CtSojEICrYcp5AMRwDdpZ7iJLhqPUx5jgSKf\nXYZFQebJDNmZK9Qhw57tYJaKqOCf9Hd2/m43H8wxpS+Ly79jwb8nbd0Z6IdB\nyguU7o2/oITrusGFkLqkgbWjpjEid3UiZrNRK5uJw2Geww4R9phhxfCwmDqz\nJMFv\r\n=fUAW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.3": {"name": "@testing-library/react", "version": "10.0.3", "dependencies": {"@babel/runtime": "^7.9.2", "@testing-library/dom": "^7.1.0", "@types/testing-library__react": "^10.0.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "cross-env": "^7.0.2", "react-dom": "^16.13.1", "kcd-scripts": "^5.6.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.1.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "7781adc75cce172f8cda28faa77be29c6270ab43", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.3.tgz", "fileCount": 22, "integrity": "sha512-EVmd3ghDFBEjOSLnISUdi7OcLdP6tsqjmTprpMaBz5TreoM8jnxGKIPkLD579CE0LYGqK01iffQiy6wwW/RUig==", "signatures": [{"sig": "MEQCH36PgZujxC1Wk7IAO1vwhfqeLynjGUoK1d15YhZJJJACIQCxBOq8QaJE4K4TDVCF2Z9+Pe7j3j+K4sXSJEId4262LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4564847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeneRZCRA9TVsSAnZWagAAjxYP+waHsvy4v8WvwoOjGO76\nHL53t+Rmrmod4fxWh/Z85X4rTRxKOV9UfmeEx+GAw4PnMtUigCw1Rrxd5HKG\nQ3Ia4ldVzaavvXQqOpM1scJmZGaJEPyuGs+q/+N/71Px73SYEPnY8ci+FQkF\nEtVQPAPjMHHRQgazvpSAn+MxITeRb/1UyturoRmF5nxGoNsPbVPq/kvE6UQc\nwE3P4LYSGukTOZjGsh/6sZwWw8sy6pQtoR1T8D5ohIQHW5hyPsYtplNbFWSM\nGKyIm0mqSzS2ZnvwwegkSlTEZTwiugCFeYFQy+Jv1lEFX4U3/tkdTiDbQ6ze\ncIyD4EkdWoMBrNNC2oJHJqbdG/Gcx6jSbXcmJNCMyt1g3p1D2oJ+wr8mvhvf\nGfRJNeDFxuboAiV6ZDZmIDR3Si8q9ZdyNr3VjhSiFqJwmAdRzpzpDtqhpqyy\nIHa/b92MN5nUMRckeZ5a1yHgUqo/Llg3QlThU5HeD58TNvtmrxpmhRqQjWzJ\nbHk8OdzMi1jJD7UWcn2uRVCX9OtoWa7/Fp1a5r3EHW/0VV1IkUc+R00RpOLd\nWMQ/3baiJY89Bw6wB/lap/t3M/FVGjWhJoNCZhRyaC8ynjX0jtgjpLKd2YsZ\nu0cdGkbWYj0EK/Is2dkNKyXNbYP3CttwohWeKprGoSWXkMChurWC4x6ZWVpn\nvzsf\r\n=7/Y2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.4": {"name": "@testing-library/react", "version": "10.0.4", "dependencies": {"@babel/runtime": "^7.9.6", "@testing-library/dom": "^7.2.2", "@types/testing-library__react": "^10.0.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "kcd-scripts": "^5.11.1", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.5.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "8e0e299cd91acc626d81ed8489fdc13df864c31d", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.4.tgz", "fileCount": 22, "integrity": "sha512-2e1B5debfuiIGbvUuiSXybskuh7ZTVJDDvG/IxlzLOY9Co/mKFj9hIklAe2nGZYcOUxFaiqWrRZ9vCVGzJfRlQ==", "signatures": [{"sig": "MEYCIQCc+eE+ukd8ZaWEFx9DQDVzlX1PM5X+r28eIxRMGrEtBAIhAMKP+VyCcL1zM8+CT4eA1tBHYA4oAbzdKa3UWrDwSPh+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4700916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqvkjCRA9TVsSAnZWagAAakUP+wZTfBrHGOAZkVbMJ9FU\naHbPWQeLhrktxlJLDYJHOlCKycay4JJPzkjNZxp0eG3hR8KZ2lhHxmjq9hXH\nJ5i4RX7dcraBlBspSNtHeizUC/zS/Lomm9d18rlEFYdfcy6x1NL0HtiISPvR\nilAYS/+AJIO6fHeFmeVlatQ8pxqDjjUIEGFT8S1Fvgdpfwu4xBugkWYxHjBE\nMXH+orCTIG0Uz297c/Jy2Pp5dUMh+5ROfHDlmAyJ+QUX9fxgqZtuB91Zykzh\nqvN5pnN/w0FQYe0SQd9JaWzzaRh7P80lYNrtBL5TLiM3TVum35t0jFlgqNrr\nj6VonmjpQQUqNqw91ap3ZuSRFZ/UX6Yuu4pbviZP1T/rFS5FIgHy45+ZrOGg\nW9Mv6TCHKtl2fIcGcZ11O2XO7DbEhfb6NQUihkFrnBmq9nw+XbX5KUQB0e6p\nzx2y48Ae2MRnN1GwtpILxIunMqGjqfSyozG5WZhFl33kLcjpBNzpeqtgAN28\nGbc2X9zkmXwxg494TKZWUCwi68KpMMRo8koGCExJDH0UUnlJEEUbxyylUOxT\nUNUOIdmZikBvS7fatdjwmhOKnj2UeeKbmMrGjIs1VgrHkLjEMSXNpBQbv/Yp\nyr2p1o2AUpeoma7PCX8v8tOgrdUqmw6nOCK/Hl6N+HE4csjG/cf1yF+3I4QD\nFqCu\r\n=/PmU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.5": {"name": "@testing-library/react", "version": "10.0.5", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.8.0", "@types/testing-library__react": "^10.0.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "kcd-scripts": "^6.2.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.9.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "78c77a1d583777615bf0a8b8d7550b876de9f409", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.5.tgz", "fileCount": 23, "integrity": "sha512-ExpCW9rJcs4Fd7oxWBpGim2H8aLa1u+wh3mS0vOR8iQFObiIC2wlK6x8Ty8F3relX6WfDAeONEDCt7i0nLBdEw==", "signatures": [{"sig": "MEYCIQChOzvH7NwocLVVsC5xOz8bExiBW4aRtOylewQAAjHsqgIhAMuZYDL5qrkZXjknYFlyyrnhBvS/pS+3trMqM+QtRW18", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4807334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1ZF2CRA9TVsSAnZWagAAuiUP/3JDMW8wy5iaBnw4z7h5\nh6wDXKOtCETZkV2RIPMf4pprmyBQ3k12mSAghlEmKZAj03O3LeQ0lsetTvx3\nepiriioIdYY0iP/4uK2Yp+wjHu3QDR1JVFatOOfAxRqCtyXX1jk58VQEwVjJ\nAiSm7XLprqu5Vk2lPoHZZm4l+9ydJgM0VKLZx50GfcpmdymkH++Pa0qBaPT2\n5wiqncurGbWoLpwAi4dDmZe5dhgIl6IcukQSvbUQ65rxp5Aido6/NV6RX72w\nn67kMbezRA82De5ynVTvQ0SSAsBU/xQ9/eAK8EkI9btkrQ38nIAAosQcipej\nVdyE0b+C8ZMBVt+ZQHIJAvv4A/zAy32vPM4S0F8XLEY5/Pf4UT418R5xgQb3\nriPmoGSN4kVzSI09qk1rHterWrxPklC2J6T6O2eaBtalqwlBZ18jI2vm9A+T\nxcsJc8wjq9yes6PRG0jvmcrmrcxweYROO/3YF0qgNCO7w6d+8XqLk/pU9gDl\n5pQxpd+uOTuP2vPzL74OUvuCUGHlHoDDP6zMHJKF5gdVVe8wCrse01sz8DH/\nnfHqxy9kf+qP4OX/ub+928wS9r1AtNsX8Zgw29JDLvfmskLGgraUeFz+Plcv\nwj0jzlZDpkk9/HJ4+6KE0OZuSFxVq/H+XlLjxZsiViB1RD5xkWzEZFKJ4HG6\nRndL\r\n=O+II\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.0.6": {"name": "@testing-library/react", "version": "10.0.6", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.9.0", "@types/testing-library__react": "^10.0.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "kcd-scripts": "^6.2.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@testing-library/jest-dom": "^5.9.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "e1e569135badb7367cc6ac9823e376eccb0280e0", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.0.6.tgz", "fileCount": 23, "integrity": "sha512-7cZ2sHN6zTW1b/pNKzA0icZozshOOuiEQq/zCcf4LUCNGKAOnGCxZDQI7qjpO6lMITmi4Qs0VU1j9Cd4Z36e+w==", "signatures": [{"sig": "MEQCIBThfnF5PN6wH5bS9NrzLRs25twjXndU7LulzVy9IKlWAiBDr6QzMu6NxFuTOwWTvP8c7ZdDIAywl7OAhqMmq1bnzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4807475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1s+GCRA9TVsSAnZWagAAVB8P/iyTd0U9vOWQl2GQSXkq\njcAgDmM9zOahfM2LDZNe/OPbOuaIiggXkX4KxeKKT1MXhVGmJhbzw1uoWLTl\n4iHfiunHNAij/iByO5eTcIhukEEbd9YvupICTxiPt5Gw5hWRDkzDKrsdKXeE\nWRJlpn0A9UpIpIan+OIzCMOs0TcRNSriAkJs9UMRjdVchUGW+DKWN/4/26br\ntPYGzlT2jBaU/mbc+cjv77IqPju9ZbBjviRkmXWmstksMB7baPxGtYxRo5Ef\nQ0BvleXTXE9B/Ih2phcb38xb2npdMfIsNh3iXpUfLOXnIWMItlQDSkO+cPVy\n5XmaOR7Xpo+z1UfRC5t0YSilwukx/41c9nQfHo95BiQD2LuJtCwdl9da4ANC\nlrTlkkXIOlEWA7kiWG1oqtx0GxCPLfLK+6eYVYK52lgRHkSSPD+pamxHaYh1\nEikS4amokFe4m/Zmvs7G8nsDuWg9vcRyLKonHf+zX448CqrtPXwNVyWGMgZ6\noAYiextBjfLvJq9K0E2MtinXG+FYhYRWyZoe192AEfjS1f2FyIqBP5zcMimw\n9188X9NKrzIZwvZY5mF8r5FlNdvQHywrg2tLtXB1EEQcBvQHQUBaIVnQIUKZ\nUtKUKY9/Zj7YLiFuhl5MEqBUZw+U5WYDxkiuLtjFBy9KYgnoNPugrrr1ggdY\nFcz4\r\n=UmoJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.1.0": {"name": "@testing-library/react", "version": "10.1.0", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.9.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.9", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "kcd-scripts": "^6.2.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.9.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "31af99093b104ffaec877cb29f5249bc33b98204", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.1.0.tgz", "fileCount": 28, "integrity": "sha512-7b8GHD8IMJ1IzSpUZ1r0JLQRj4Jp9nXlKuEXkS3tgISZciO9n7++4nHCLsCK38eAB5zsBUa0G2kR+Sh3aYDqjw==", "signatures": [{"sig": "MEYCIQDDqM8B6ZJlH7o9EiD7qZUyaK05LudOMwGXlES9nuvytgIhAOp12JH79jw5ZNaUzMNzCBXa76tUmVJ+5CQ94zDMF3yI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4811870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2EazCRA9TVsSAnZWagAAYgUQAJAl4FzyfqQe0SJHH7oN\nZk3yYfOI93358OZ73gfHJvZT2Ie7PrqRZw0UxFgs3kc2dTqZO7STzE21jZMu\nuj5+z8Rc2aLzZgwGg+7fmehznaz5btMJlUUyZhO+b/hoqEHbtIsvbjCIiXVi\nVpw2+aA2uvPdQD0LrnZB9PY8ABTsfGEhpFhGZJ/uzmTFgAbbOzb7olvQLwBR\noNBPX4+57pmGTSzxYOj1kEnpkqpqueGJ8cuEfD0X2JN13UKJwLrzoC/dOcnv\nT8UzVGlrcGjRZTwUa7/zD41dReaSZ+gYcgq9Y1SkRxlRRCetN7Irhmo5+DDP\ndI4/a6sVsmDVFzoLhiceY8Yj3wH7z0XqezSI7QBkflAxwfvEosh1qyjYektl\nv0TKwKPvGDa3S2BKVfmMAKaUW2uaf+02JkR62FU509qZhcSvIgjPNwnQazhU\nyLlwbQMiPbyzVQ7/Qzz2sS6yPhcbMea1zwDWr8nkIYXVy6WrvKb5g+oMiC4+\npjB8vD+YpCQ5UqPYFWBuG4PKxagyqjgjkJZPMSnRFasr3yc0hzoXJtDBRJIQ\n3ZVqPoXB5r0cCnr3PgG6+V0JVXIw7zjFbLTCctW1pPTzLAad0KzHN2Anifxa\n9/rjbI5vWGxwszfuPnajT82Z0oQtmOYa1qbIltxJ0OZdd47wNv24qZhdCKK0\n/QMd\r\n=sKqc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.2.0": {"name": "@testing-library/react", "version": "10.2.0", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.9.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.9", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "kcd-scripts": "^6.2.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.9.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "dde740cb7374c93cefec4faa750c4b50dca7bf3f", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.2.0.tgz", "fileCount": 28, "integrity": "sha512-TYQZ4vz0lGCGRgFqQivrtUGQhAlRSxHlYB0sDFJ6h2BZ0IrgRMF3EDQixn5UJk8oMsZJuE1HNnOA0yP4Ci2kyA==", "signatures": [{"sig": "MEQCIFgIKc43OXHbvJBmKwNLAsGhZtqtrhpKQIFGIPXUppF5AiBgxr0+JrBBoLG4vpNvaPf8kJ/xpVfMFROiujzGvrmLrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4816039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2P5CCRA9TVsSAnZWagAA2e0P/iqD9CdrHoa4QmF8iT9e\n3ZgqGIxfH45FgjP9njiBgwdbKJpfOBXyunkiy0iBvyTr03TMR3C6JriZsXJE\nPXyjtnrnhKyWigKxy8TwmuPRTDf7MVzaTwKDLLwCgvgrxoAVEixLz4NLqSzS\nXMFcIjFLePUEBkoBF/cyI1LabXv5uoDvj4bgXlzPVu83QuMzQC0S0nKGJPf4\nJTeYHpzkpd6/PrpccYTT1tRnI1BbQoE+j818i2hMvU+2FaYL84KR7mSkLuz+\nAFOyn7HbZ6e/eOiVNXJvIBoa5HX4d/ZkntKVwCzM8B2gBlxxiSWtIBlybnEE\nLJKziGuXM+weBbiVrzPEfDumZJumzHTYDKz7NDTBZkqjBVK/ZN6jnU21peOI\npzkiAYJK47IwbV0xqA4PTt9sR4lYeFQZm8nAquAkXY/i0IAP9oRJ/anWwGP4\n6Fcuj7kZhNHGBgKvIlL60OlH0itzrax91Q5F/mdxwGPwSE32h+YmstlfjOLQ\nCnKGUJ0+7CKG2N4bBaiJIw4kpYgd6MLipvKfEy45F7a2OlOw3KUawWgtACXq\nJuPRpn+bXbYvQa8JCaM7lDB2E0EluY2kqV0vlWnkhvVhLA3qdJGZwQuXJoHC\nSC7y0diQMavd+PKSxWoqRBjnqcG/I2ihVIaAMlRAEnqveENn+/4ekS13oM5W\nMGDN\r\n=lO8y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.2.1": {"name": "@testing-library/react", "version": "10.2.1", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.9.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.9", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "kcd-scripts": "^6.2.0", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.9.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "f0c5ac9072ad54c29672150943f35d6617263f26", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.2.1.tgz", "fileCount": 29, "integrity": "sha512-pv2jZhiZgN1/alz1aImhSasZAOPg3er2Kgcfg9fzuw7aKPLxVengqqR1n0CJANeErR1DqORauQaod+gGUgAJOQ==", "signatures": [{"sig": "MEUCIFj7SMXXdBhczBRwKKHbJsXrPYFBbFo+sRc83aCK53hCAiEA9xDbPZ5ETXgdVcFR0ZxJr9nrq1eJGnTmWnLuqOPk/bA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4818014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2pxeCRA9TVsSAnZWagAAfJQQAIpF8Ic/xGEkwutUIxfK\nswSi22cAVCeJK6Xjwk8nzduDN3fX/uw6k1J0FICTBJXZ33YC9OfFCKOH1ZVM\nXvSzVcAaRbtf6yQRrN8Kr7sO00LCjlh5K1Cu68AyzRbcNQbzdfunawe1ELDo\nRU1waIJbrQMio64qvR0Goa8r9eAB209zQyMYedcZJOOvFzbbI5IWCdGAIfJk\nacqxGOxGMQAMwngbu0+CbF3HWZIrePGmocMBN9YaTYeCffkYWjiWmPS3H1hE\nhEiiZbwW5DLX8iruoUm43gVN1XD4PCiK0liGcwJfOodxRE6FpDsL5PL2LsSn\n/5/TZwjpb9LigLCY/gt3Xo/RX1AU7iESlZXdqm1yhzRFZf4zb88mDSA3HdBd\nIHGMqHKtRARK349Yne3l5BNxB8hy5vAQFeAm5686ilww+WHymCO+4jwc6wkW\nIBUmDpQo+ZrH7PXamMIzkQaPcyc3Os1zh6bh92hezV/tbfgvyvOj4hZ9V9Q6\nTfIPjBNyMdE2x9ISDpolLQqroJ4r3CLyIiU0VWTDDIEDwJ+GrImYLDUjdMk3\n4DeUCWqSA2k34jYJvJgHjWLOXLjw5wk5gNwnATb/uW1MPUtL7C+L8ASlXSwq\ngp7wod3Fh/E/bg8wqKBOMdRFxcd8Z7ID6v9sxvEHfYaLAWh76CSr8nDigc6I\nOKi2\r\n=6TcY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.3.0-alpha.1": {"name": "@testing-library/react", "version": "10.3.0-alpha.1", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.15.0-alpha.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "fd99a481c3cd2d3b4ddacb4422a0d78bc19c73ca", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.3.0-alpha.1.tgz", "fileCount": 29, "integrity": "sha512-xRA9TLz/uTwGFTxST8xy9p4pe2RPy1VmbBCPzjoGBxvP454AafPM7W7ydtOAII42/cF75GAGvhgsaMkvs3L3kQ==", "signatures": [{"sig": "MEUCIBjA7fxpVnHjS1iM3j19rj2DKmHKeyhNztiqOy9O+MJIAiEArif3Tsj+5zLer0qsn/Qcx1gYWxPuoyRtEaw+Fsx16g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5574835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5Yq8CRA9TVsSAnZWagAAwtUP/Au1CmsD7BvUS7pYX7Sy\nH0M/BjjfGW2gsMd4SOof9Oz2uIsgdoW6Nra6AC+56UuxDAg0m7zj/am7Jsxo\n8PoWRLTkoyiN4EL5m1oQ27dbSy5qtu29TiEjYX2Emjva83QRrmmmwE1GV4F0\nVxhqiT/I5y43RyvPFb+wLAFdGLm9SDtQhX7j8ghL0R+hDYG4j2BwIVyEgI51\nGBwMKJQmXYCCktDku7PrWH0ma/uFrMy/Dt++mf7giQmMdW/SF4TfmOoZWXGJ\ncnHdgSIRYxqFXgs4arPiI8yEd7A7+pxFBf0QAXN6SYNthPTyt2Qcq/BOuhHl\na1RV/b3ofcLoWItUIVIgHbvJbtIc1GKW/1gcTeqU1cFX4CCemX/DUV7TAD7T\nfe2OKeDHoaGOgl9lfPmDN9HIGjGlUiwwMDeJIlL4ap6PzSfSB6ko+OmqjE/C\nDzKr69JFL6LJJLyuUqxzacqr69dXS4RJ6XnqT/d7tsV0v3LNK9/8qGLt2abw\nOaTJ/vugMKG7DO+JgUjpVbS4tmeigKN3l6yAR1hA4YqJps/KzNGh7G6pjRMH\nSz4qa95uYjVDpwTjPSbhHq5b6ouvkdOxN6k0foe0SSvf9ILc15z5Wse/1OCV\n5a0lLYpwlGlICsfcmwJOuLfFFZHl0Yn7yo3IxBFc6DVa2uKCskXBI2gmEP3J\nVksR\r\n=1WUp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.3.0": {"name": "@testing-library/react", "version": "10.3.0", "dependencies": {"@babel/runtime": "^7.10.2", "@testing-library/dom": "^7.14.2"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "d615385b8d86ef4d76142a423755d471b3673295", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.3.0.tgz", "fileCount": 29, "integrity": "sha512-Rhn5uJK6lYHWzlGVbK6uAvheAW8AUoFYxTLGdDxgsJDaK/PYy5drWfW/6YpMMOKMw+u6jHHl4MNHlt5qLHnm0Q==", "signatures": [{"sig": "MEYCIQDCDhbDYuTK07mgZLGuIIYylhPe2wckmNvHXVVNA343NQIhAL6rD7SlBfjwFBtCnahUbzmiDUSoYdeP06edViSWA8tY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4845477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6rUyCRA9TVsSAnZWagAA3eoP/jjkAIYwZejEb6LMc3oe\nY5qZUGcse2t67exEhAG+I/dfpUFverVOrvJmJCvsk+IFYGqWmudDP6cUDPHY\n/QzUX3fafhe5gFCtc188irl/lnF4ZfogmQQB5Dj28D7oOoVGgReuH0eyzKmc\nUVksVGjdwKL5rcXcaB6oexKOKOpiBGwsD7SqN3WTR/jvT+ErDxyGYxmBfViw\nhSCsiKGKtNhLP46658KYpBrIk/XZ0cyNnoKYCYqm//DMB0LmgASncaaw/2b9\nVW7ds+uFnTRn1XU36eW+swaoS70dUczaJaomRIT65NhsJGn84/alyUzfcgdD\nqB/1N/Izm2Y8UHlzhWRWyZrPsqhJHB0FeTdu0Bxh029GED8MNxCbKp2X8kpG\n2Y8zIreX/v6uMbTGW1SEUb3/Sjz0SHEKJDVkvoY/0JkvTXDGv9s1QhNCiWQb\nZPewXTKd+0AFvWeG8Ffevsf7yy1PGJiTuvr8vjIVfy+8XR6YxrNUrlWIfB7h\na3MvvxC5HRGRz8+j8bW5vzx4hZ4Eh9QmjmlDJUYA8bN3aAiMWOtYDLU9f4vi\nXxe1vK7QzW7sKPe2W7d93VN0zHvCkJfJWYI9QBWsk4ew/giEFTbkAEi/UM53\nqYKwNhL1Ld0ujRQUEy9YbOA39f8rf675mtgfCZITChDzWNr+3zR/QRBT0XBU\nxv2o\r\n=u2Rd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.3.1-alpha.1": {"name": "@testing-library/react", "version": "10.3.1-alpha.1", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "7.16.2-alpha.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "6428a38ee98b747d801c18cd9af208032ffa79bb", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.3.1-alpha.1.tgz", "fileCount": 29, "integrity": "sha512-A8TjMGtRqs6nMaopvrYXA1xuxCYn0nJX2uz2kVTrr96km5mJk8/Am/dQ+5whX4Nziy12P0Od6cTzCcEYIuy1tA==", "signatures": [{"sig": "MEUCIBQGuDC2vwXV/klcPqkQ07DWIP6zr93VlUoyME2UDPpkAiEA3htPw2C2ZlsVMq9IU7CvoqrRMsOADYOlCRGy9ycZZBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5077625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8YMfCRA9TVsSAnZWagAATkEP/1iC3dGlRhCTg2bv34F3\n97KIMifVrfT9PtnsHkQMk+R/Q+dwUpZMXI10+NfM2FPZroFQJjW72WV91kkC\nLD4aIjOsdCFf8009+KPy2/lPz2fzRitv77fVu5E9y7zHxaJ6seXGd4W7klVS\n79+tgT2HpCT01lVev6jGBggrcRWeTRSBQXXSwTyYZhTQ455+xvXnNwv9x4VO\nPrwV6hDv4RJjVqxkhHl49iiQxEtMl9WYoRlVzYTFTgI+R1zelvneR3RnibHc\npF0s28XHwSgS2Y0NjrASWpwUc4lMhZ6hWaBiZOJjEvRqAXPw8Qb+ODLF4avL\n/dlJCmgMZtNa2+qk9SAHUdLrOFDIMr+MIgyHCD4NtGeXse+/ztWEI0kptpas\n+Oe11VujKtdJNeUIPqcSpd8jtzVts3xb2aWjwc0RyRqLFcz/GU76NnPaq8iq\nKEVbtoDZw4M1+yqN3B9D0xQQ86v7xKePwompvph+ttX+ywHhVLFoILPRf7kt\ndisU+0/vlIX5+xg6SqLTO+1VhDO1jbvJ13n67zkjrC4sFhLL3Zdoz4TUTkUN\nsZphONBheuDXQnweT+KQCCvm1SA14CHt44R5237YLd3xN2V0IdW5RBUyGTWF\nup34bE3BQ3xVL/sWoEpGYdFci/WzxTFFxlsDFKKQJb6pHPndJcsFXDz6GLIo\n7Dsw\r\n=v2Qf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.3.1-alpha.2": {"name": "@testing-library/react", "version": "10.3.1-alpha.2", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "7.16.2-alpha.2"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "fa40b31875cb84d3fee5c4812df4805240913928", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.3.1-alpha.2.tgz", "fileCount": 29, "integrity": "sha512-Lk42lpG95MspiKur8Q8GPlEI+Kl3c5lw2NSDWaw0P86RZb5Ebi4XTCrasAXYj7VEZSiOIM57k/5hM0/O6LPAxw==", "signatures": [{"sig": "MEQCIQD5N2GlDMiSCuZXbeQtpA9FHugokBUOlxrZ8lC56KXfnQIfQMGnWGxw7utNDPE9BIOVBxYElSU79Byr2sREnobinQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5077625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8YjGCRA9TVsSAnZWagAANrYP/274TSYOvoHJltA7fu2D\nFOmSzSzWkHLwZbKGTF0idEkZksqMll9Mp0lUjLgd09ov3+q6Z8DlDuXso6gO\nSlHtwQ68KkA3Jl3Jw0WH1+pRvTx7yzbRMZPrrP2NH4LjVpsRy968H4bOcezO\nDmxmXJMfg0nM/2l4dJtDHgx/9glqy88HbkC6ru8+RmKvf3MfrzLhoZvIYBaq\nWTPPqKvLbY/mkE+d8gGNyDwZpHyVNXwzuThy3Jso6M9A5ZEEziKR4idWXCN2\nPJQ3adYrGpYVmXhz5bZvfF5lS04g2kBZkMQjkpTiuMuSOLUjqzkWHTO6FbdO\n62I9bC2AfQj9dzMFw7UjecyHCUjXwL2oQzJ/0L74nRjd7hN1pHvuxj048kKX\nf8fIHBdWukFK4D6OMjk43YbzqOdQbSMYJbp62IggPmkEvC6hqkQ5gViBeJPa\nVzIh+PSwIk8bwh0a/2gSnYVvErHEEKUPkRbdf2h+lPItkJnfoI+K7+m8ZkJV\nl/UrxRmpj/ModSwryOtnjzQ1+1AMt1DpFG+gTPMzFhojnjnXMmgbcK7uKsi2\nFwuUfphY6N5JdhxVIMI/Bl0cupRgO4hJVvrXZHIk9oh/czuW8h8/gEJf3WFs\nMadlRop2vAIhxp7bLESAeQa9ZRcRmYnokSBokZBJDFWwz85rbr0ils245/kN\n9WTI\r\n=fAOW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.0": {"name": "@testing-library/react", "version": "10.4.0", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "d95302ddd256bc3ebd0b99c445bb236e4d9bbf36", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.0.tgz", "fileCount": 29, "integrity": "sha512-koZCPOzH5fFXN3MPeQx6iT9o47U5y7zpyiEZlG3xP+XSApdHQfsx/PrsmTiPfrEjU/1DSaX75arjMcdkbnT04A==", "signatures": [{"sig": "MEQCIFE2uGMX+VIQGQYxuWdWgLjZT1plV+PC3LA0qYR6hSXoAiB9bWbGekahZpJ5CvAK3e7hjXg294EkhQP/5Yu+WRTdtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5100387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8i56CRA9TVsSAnZWagAAmK4P/23axdUW9xPkJ7vubQHG\n9y3FxtYXzgcjBUeUudxSagTAiKU55zNULkO2KinXvDIc5HxfhiqV7WSfrEvM\nmZ8U+k7KVETc8ftD0SvK5kGgAog28Ab1/RZDgKjbiAd7dpYirEzogW0Reu35\neSjj0zJ4ZcXb71PFBphwESc5H8I0oQesypLuV9j9jHaxuGJwq/d9i1W5S+jE\nDPqNAkndwP0M2FSeIOG5o9A1IARxhTOUNNrhRfBvaMDo3m/Z3Cint/542WjA\nNyJMjcmOP5vgZsiXTsb8pTcMPabOfmfHaFF0EEI7O8li4pcgpZNVhSRRMa/d\nqpPg75JAOTrhWRhSx1+esaEja8MrXfymIfZa33lr1zVNmwol0capjKJ9lbvl\nBsDwpVJSdacEhEvz2h4jvTbfSLU64m3E5yYYcgCx2U7rm+WTT/Wlk1i4tKsI\nI1OAgByPw9ZqfaM+AWXs2KCstxaeZnYCHPvMOfu9in6yb019lKc3A6H431lY\nlQ/S6wPyKLLzMeaQkwr+nwLjYm0Px6W4DZueVJ1rPr4bgKsbWOqsIBTPUt4J\nn0iYzND51hzyvtLvIqrd0PlQwidtmUcs+pSzYRTqzZERY7avHxRXbKONkgxi\nlbwZuWLEPshQuGT1v/lhixGW1Tw+LLL8LEzWa3sqXVJeLqgiDax0/toST98u\nMS1D\r\n=B0Mb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.1": {"name": "@testing-library/react", "version": "10.4.1", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "d38dee4abab172c06f6cf8894c51190e6c503f61", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.1.tgz", "fileCount": 29, "integrity": "sha512-QX31fRDGLnOdBYoQ95VEOYgRahaPfsI+toOaYhlvuGNFQrcagZv/KLWCIctRGB0h1PTsQt3JpLBbbLGM63yy5Q==", "signatures": [{"sig": "MEQCIEuahbS96Hz5iACUxBjcBwmHUOeIY2tO22zBDY6wISbpAiATu7SG9JegmFr1qjQjGwXrY3i11ktgAbYEtyR/zehG+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5104882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8ntdCRA9TVsSAnZWagAAPKcP/REWCLdoI1P/KqV2xNjw\nPI4IEwdg/M3LcLgpsEAc7Zlg6XVrlVZMRl31hnUtiC+XaryUHO4hjYgfnGDb\nk5d92q5bqx7ZIrH1/2O5iDJOnXYu9ixKdCDDz3jmiJ0Le5YwACGll6UIJzNY\n6exQQKpE4eGP1GC0tYWVXqxKyv6KWhzokaQpDVOT594H77VIdSIxcQ0Z0k0p\nYWO0fyJQOI9jpJC5cQ+DIhpByjJtSt0yGh8jBv6PDjyJdOW/ltn+eGgspF8S\nrJCC0qVXH9qR5YOUMGqHdm7Zc9a+n8xgFk+PQJQNsKkn/ynlWx1VmSMS1CO3\nFBAOspnmrXFRqkf6JjovB3tXooIX3VV8cg3bpBhZUOdFPGMlu2BkcBjVex5W\n8j1QXdl1HrjbwcJAtAGJ0MoYER7/IhWSJJLLHaBW+uRBATHO+NOsh6DjSrwI\n8DR9S7y8yoS6DlYhpNydv6ORwpRpR5ZKVLdqwRnYfImxXRkHbUA800S2EEOc\ndvsBvPxPnlBZ9DYLJUT2LAyCGMK27mKjBd2dpkkGE7OnrTioUIj59naxdiVT\nqQ7MikKsWbEkiIIWaflgF2EwUBMzOmu2ARZeoEucoHo9WKIIoJ8kyKhBbQKS\nxmQDp3pC4sWdsymK7+Yr8k9b38yVfE8/ruoFZMxPoszMAbO6mF5KJmnBlNWe\n1NLt\r\n=jiu8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.2": {"name": "@testing-library/react", "version": "10.4.2", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "9f1ee0051a516469d4afaf350d2a467ac19cd57f", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.2.tgz", "fileCount": 29, "integrity": "sha512-srhsdY+Mq2xhjE/ha8xh0U9YJSCNYbOhzPsUiNvxy4bgltB87PjuxDtkWMWwWJOIkngyJaMgoJuHm13EOq4V0Q==", "signatures": [{"sig": "MEUCIBdQH2JOVjH+itbZJEwAumm4QxXs81pkpK6paVJNQ0dqAiEA6lUtxzmDm2HcmjQpsXl5VD00kcL74HZSTHeV4IjqHf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5112429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe86YQCRA9TVsSAnZWagAAGa0P/Rq5XMHq5LfLxEV524NI\nfvCdHihYsk/hbF/59dZS8wPooFMEngK7d/k5i4ygn9D+MKH+tBWsfMyts248\ncFHAInbO4prScc0QRfx6R5DrZ5w2BHKfxzez6soj47iHs/7bXHaXYk9t8pIV\nM/5A0G6Ay3yaVRzQrM37w7LbIIBru4D61QTH3wsAkKAodR/YUbIQePlsSa+p\nyPkw3camYkN4kATrGAybTxCmGRxi0wxN2XuQg3fu9AL/EStcc2sbH4jITIbe\nehdpwFzZIjANoK3vdNSJ87Gu+iJNbLVCaeSHxRFFyjhrW6qWLBXjAzJTvNE2\nIXHJBf3DLVH5E8zx9B2rhn9O/AuTgXVB7cd38gB1/XfEDsE67bpmTBeVUrUl\nN+AQULv99ya7MgnNVHTSMKpHEp4Gl4YOiXyulEvxv/IWEtkiA61nJF9ZRAKN\n8F3HqYmpxNoWrqyUkd3eF1//AoYTcsC4RRQfwGFvrbpqPEYtwPAVWZquExqF\nhgugUXtOeaMsg5eNE/jVkBhciYatMwlPpqkiAa/0JG302QqJPUpIKLmmaBAz\nOPBylw8xgN1R4XeuqOD59tCbS+YgxIPASbAnOgNnggDSsaQZYtZKzHjCBGzI\nHdidgY43qlFNBemGgnvSfQc8GNNAJiYLWw8028uchdzzvFmNaIExEG6jaFdN\niAfW\r\n=wcOl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.3": {"name": "@testing-library/react", "version": "10.4.3", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "c6f356688cffc51f6b35385583d664bb11a161f4", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.3.tgz", "fileCount": 29, "integrity": "sha512-A/ydYXcwAcfY7vkPrfUkUTf9HQLL3/GtixTefcu3OyGQtAYQ7XBQj1S9FWbLEhfWa0BLwFwTBFS3Ao1O0tbMJg==", "signatures": [{"sig": "MEUCIQCHmnM3O+pNIcBIl3ViQeU39g7oRciN6ABORn/t8/z1HAIgP3gvGFGc1gWQx4e/v5SNnibpk48BqeTppKVKzx6jPn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5113461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9Cd0CRA9TVsSAnZWagAAuhwP/0IaJR+f7u4ip29xLR5h\nEdJK3AhusNqHrQ8BkTgvJPuDVBXxB/iVbzItjI3lXqFydCygQp8XwDGAUaxt\nXo/hUFUHlyvyD+H1apXQ1g9fE9Wg1I+XXvGreS8P4z2FUttooGdJQqYRZuwd\nFiw+t3bw9+qWASwqFV9CrrP2ZzycAGLIvLOhlattrehYQ6AHHmqI4e0qTBWb\ni7l/o0TUc/FFsYDeJBwzFWeP7etSYKgTqc+dAGPM0p4+e4sBYB9srSItEaZ5\nKAP4iKrnHP7bWDlmCZJy8N/+2D9gGJDhkSXBfBY/CDzXgE6mvA1IYJMG+Yag\nttmbYgin+njnqmaq1IwkUR+j0ZRyTH0Oh/mYhkpn6oT5YKHH2gDzo8OfZFOd\nna4EXHAxr/SEzmL1pUUbxq2zIe8bJv9vbSOZhUoidHbTt+cyN3thezZMqJfB\ndwmjQa3i/D7XzlYHEnzIBYKqetCeNcUy9Fd5GxsrdTvtnc9ZgIVZIqh3DdqR\nnfSxqWmCYbNvwdb3rWDzDEg1P2ZMIYZVFDA0kD/0VpJtf5q+5juj/dUwkezb\ntZMR1GWMUZK8gp0NCgBaamkPudVhHwNj3IMb1L/OiMmLwRZiK7mO0vBdxT3f\nXy3Xmh9sR0zg41e7KABCsrkzPvqPHczKrRSD0ldapuhQP68KTxUBqqf3uwrt\nGZU4\r\n=hU5w\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.4": {"name": "@testing-library/react", "version": "10.4.4", "dependencies": {"semver": "^7.3.2", "@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@reach/router": "^1.3.3", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "f05edf827df288c6c7b19fe96fff2b33cddbb809", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.4.tgz", "fileCount": 29, "integrity": "sha512-SKDQ2jBdg9UQQYQragkvXOzNp4hnCdOvXyZ52rg+OXiiumVxkAutdvvRzBF4PrbvMQ27Z6gx0GVo2YQ1Mcip8g==", "signatures": [{"sig": "MEUCIQD5vJzxIXf27KUygOAIcCZaswaC0OOmMilTp3sRTYG4xAIgaWrrxL0JVp34BLZVNOq0FEB+9hi3ztsqkUse+sdDAVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5499773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAg3jCRA9TVsSAnZWagAAwfQQAKDcJ1UCCX8no0R/KLZv\n4MYaornDu5ovqXIjM+Hjj+HnR5aaM5JHVTawkmk0XeUkTnZoraM6vzt1rOem\n3g2f9Zx9Q8g8b9wJECHaofRwFWpHLHZNKflQC1WBX+70/dHtlUHwdcOv672X\n29lXG//D/UmC1ghQ324b7Wk02oBqSikM3scAWPROhTpxBOEjo0Qlb/PH/ICd\n8WkqPlbd8QxiHBegbWcLxuKkae5cZN3kIBPGD525wNLR0z7fBpt+zc2P1Veg\n4oyTSOclDavoLzf6onqV4IHgJQHxEpK8sc5zLGrzl75x5c74c5tZpNxZYQab\nWJaBs4uJij+EympWUfup2hi3tyv6hnufB8U0160ALDOhvZO2ErDIxhsWaBcI\n91x4wcegwhdZvM8KzAJ/TQKoCNIuBLB5QKkokNAMvKSLFjuBHHnvLdoydcMY\nNlc8JM7n0jwNdN3BbI2TTyHES/F6ah2p5ZcBjpoSOilBlnpeDjWlQ/Zuz+Op\n69VKpGF7FFsY3UoBWt2QjwfS2nZzXGWWXgDI2ICTLrapVjtTFf49TiBO+cQs\n5P5/DzqEAPplrpiSx4Q9bNcVEkYEsDt5jQU+WNM5NmqUF8MvSWIiV8tLiMED\n7CyBHtLhrj2g80R4favHmCcON1hlwD3B6d0u3yYxdX0b+PvGVY/ogeJZL8FX\nOGMK\r\n=u7eM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.5": {"name": "@testing-library/react", "version": "10.4.5", "dependencies": {"semver": "^7.3.2", "@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "e66e004d6beb60a26b4e5d4e4d9989738d7077f3", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.5.tgz", "fileCount": 29, "integrity": "sha512-M5A0W4VphBiEm4vgnq7vHC+/e4Bp/3iIOAWap1FtIiA+Zom6BtXpY3RSTOqc8bZsCcu9gFBZ/lxaiMW6uJddWg==", "signatures": [{"sig": "MEQCIBwofLYGQQiistoCCRXHemdAd3SV8hlOzKonSFTyQArjAiBTdfFjaprriCMVYGRzfmiGYXznOcEf3SXe3GDqWcufyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5499368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBhbMCRA9TVsSAnZWagAACT4QAI5Cvv+kGxLn4pCNnNWN\nb6bl7eXqOZ0cPP3pw9fkEWUE4Ey3gNzSzRO8X6dA2RSssMR7UYQ3/O6YzTKT\nM17D2OQBxe4c5pZvWpgoA8Z4hCi7+6uGl352EGBCyHKiTO8Iw5FVnDhX+cc5\nciuic43c6TAwBz1M57bMrJunqxxVzJG4MrTVYB/QIM5n7EHTMCZ55U4L126W\ntkSjJ+MrrvFHJSqqgE303oCSNlBXqQwr35DfGhoWkR+rcDt/ldPtbfxRGBjI\n2I/iMSZfKiPtHK6j6S+i0iT1v0hSVbM9u8GiwVnqm+BAk9GlBzqvfZwcQn59\ni2rQ0nJvjajTpl7WAextnL8QrvZY0wrOw2QspVOO6rxlTBsz53IaN8GTPO6O\nitSEvfwF246K4RGWZAh3hEMSZif+Ok3Br5ZuHEERsXTtgfj+opdvQ6UD8Jxn\nsmaAWn+Z2Ghpfky2mT1aJ3jDb2FQyGw/CUnb2wJw5gx0HKDBY/qHB5bTwxL5\n+4D5eWtI1SoBJ6j9WFd8K6yJqMSN1gaVHq765pdq7y4G62Z8EvMSZh6QM7zh\n5KB3s7BU2qEUpISfdC6almMgY/Bns0dA6KYP8OCe0z4Ff2AGrO38ls/lU3dM\nQGZfeSA/fxnjIZrpMjlQqwz3kLZ3HL8+nYQZztAwV+2CTN73TFqmjfu9aDj0\nNLlC\r\n=yQwv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.6": {"name": "@testing-library/react", "version": "10.4.6", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "70a7cac35ac8256c22a8cb0c4779e0efefd218c6", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.6.tgz", "fileCount": 29, "integrity": "sha512-pVcm8v0HxCEzrtasbC2bdhWM0P5X9o8a/xfBugC97uVVTmhVeGHj+5CdE1JYi/i2K+mCyeq5YqzHLW/gB+K12w==", "signatures": [{"sig": "MEUCIHeWMK5eyHHsHKKB15/YLZWrDeiF1NwX3WMIQyYQC0huAiEA0pxUPwgP/guqcukamXPxfZkwEzXJYRLZY+wSuHK4KGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5123833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDKacCRA9TVsSAnZWagAA1xwP/jsASlsLs9o3UisuteJN\nQCvkFFG9Lh4PLLWw4UnzfkLt5dClBthx3bHkiKiLaAsSYkfpGDJmvt72ZnaT\nM68YLiueB1lndu0fBo5wF9Ar6844IpzpVHerlRFWk9iqubXXDxswMYg88A3N\np6vhIiLuFJo4xz2627DZJ+IdlQsIomsaUkUPdmGlLBmxAoSwKTYNiA+y8ILB\nsoyiEodlVOg92yqEyuKiRRnmP1ToP3pRCDefd0mG458S/UcIVTyL05C0pxmP\njUuIIc22E/h2ftwOhJ3muBiEbM/sLFitAn8pW3b+oah0lttKm6FIfQt5GCBR\n+aVZUNqs6OAagRGfPuRn74+EhyERTvxHgdoCiGDc+DiO7QgOCDumQ6Iega3b\nWl5R15g7JtRF57Hsgd4cigmgLMT6+YivM/a6DgbJRWPVFf0jlkWSKhLw880K\nmNNl/q5T3h17oJxGvvUOM5pzpAByn43ZXcnoJV0bXQ9hHgcueg2D+bQm38xk\n4v70HhSs/pY+jcGSwgvuOdS0/jCOhcRgn8oVJBQgLsNwqtR8aHp0FUACSE98\npTVgSr4M8XX5nyrFfQwgQwX84gMTZ8zXhlpuR8QWEnw1dWW7DrxOn5sQWhy7\nogB1g3LLCSCbzL4tHJhyUo6tZzgvtAMMc4bxOM2S06OuGpLZhijm6SRcyNv5\npffP\r\n=pPdA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.7": {"name": "@testing-library/react", "version": "10.4.7", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "fc14847fb70a5e93576b8f7f0d1490ead02a9061", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.7.tgz", "fileCount": 29, "integrity": "sha512-hUYbum3X2f1ZKusKfPaooKNYqE/GtPiQ+D2HJaJ4pkxeNJQFVUEvAvEh9+3QuLdBeTWkDMNY5NSijc5+pGdM4Q==", "signatures": [{"sig": "MEYCIQD7Bq2fiyqEpwIRKUyrd5UzhigJ56uCCHVyuIvCTjFJQgIhANJMH9j3LRjWeeTTGuJFpZfo6ptUND0Kd9iHxhj5r2Mg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5124400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDwG/CRA9TVsSAnZWagAAhl0QAJA1LpPokFk/XkPq8eHS\nxd+yBgQFc89nY8V8TmgqNN+0VHnIYWA1wwj9WTq3pTmzaTQckceL8xsQLEBF\nt22hgHnxsPdpHG51fOB1VOu2QFXue1LLJQza8Ss9vn578spXWpKq9+ThvwLH\nextiVCwpXMNPSc7mtMGGKgZxSLu9RGQvowPAoUwlhTdk5roJ+gksGrEDWCLt\nNgWTx0HICS7kJ5gDLlc9XxByFJnT9Qt2HcYFqpE+qO0IUAqQIitGuPn8YSbL\nEaVSn9fC10HG8ZWvDScsGACebsQ3TlZGBaV3uuBrTwkz1G6+WEq18eqAZMGU\nlqu2KgAtMT9szvBn29eQBSizATdIMOgpPKRvPCokWAddczQcalHyMgA96snE\nVGmwrDXMlCN3Xg0MMvRqkCUSBOKkZnUneS3B1uTjuH76PFnq8bUoUBsXivqm\n4+XhUnQKYcy4ZyES2Z+4tLDKBEjD6TUnXwVubC1Lg7GKVHQr/Ja+0o42KiHD\n+8VcXBRWi+aM9O7qCI774I4R0kamqfvDiMX3NJ4pR2V60Q51kcINccZhrVQw\nQ5+4p9pEYqH36lyw7YsVXrYn6t7JmOiWyAHLU3Us3XQWGZiFHW/qLn16IEj8\nDwTdWCJ3BAv0EMufisyv4XI2yeJb+S/42gN6fFe2N3Is4FPsLJTWTmieYd0B\neehE\r\n=ewJD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.8": {"name": "@testing-library/react", "version": "10.4.8", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.17.1"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "5eb730291b8fd81cdb2d8877770d060b044ae4a4", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.8.tgz", "fileCount": 29, "integrity": "sha512-clgpFR6QHiRRcdhFfAKDhH8UXpNASyfkkANhtCsCVBnai+O+mK1rGtMES+Apc7ql5Wyxu7j8dcLiC4pV5VblHA==", "signatures": [{"sig": "MEYCIQD/d4yhHrQTefnjFp4mNFTCCSJwEDlG8vDfxLADDDft8AIhAMw783AHSFwt3ReZgcoDKVVluLx3O02+jwAGXkd42A/+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5195768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKhYCCRA9TVsSAnZWagAA8T0P/2MAepObPKQORBfPKeaK\nMaIJpIvDnY0+6L95Y5FydWSHyhhCS1+wryMFH4SC5BzwXrdHoe8/6tgLIhFw\n2Zfg26GQMR2MdK9EVAGXNFbdDiAZv45oMK92i4ncjZl8gFEJuJxksJ6tp5cN\nPdkwl0OYIbXu0Rs9pXx0wyKfd4V43NBhF+GEGMNE0AH1cNLWIhc26Na2Otvv\nq5IQOq4GpACMp8acZMz2Nu2OoBvC0TysItdU/SR4df1ANvHtnF9+2Et4u8x0\nDV9fcXsUqtvdFfx8yRCNf6ixKylIuEGc+JSlTQJRpBwtqOwOPgk2rlQ9xJk1\nUmwnrcWTYPx1n3du0Dt4O3V3+c9PvfiFYITG+yBCODL6Jv750heB/SVIdal2\nUL4WowOMie80+iCr1QJdh383vJkG0mmn1oaGeE1qofv6FF2NU68xz+uk78DU\nNfWDkdpB2PbcvElX3p/9whj59yPKSaviH19SU1cI8Z28vnE65FQbggkIxu82\nM5V2qIbZIwr7T5WiaVIT+gHrfRsFWu7zfhupMZKCj5VWgqN1zBHMz8hVrBgT\n6QHLNDgKq+6ATR4AmZ+1xhq+URxi3GbX5pwkQRxU6YqoQIqGnuxNopCIkpU8\nv4RXrEgjK4xDwrviNO5AhEzrjueV8GVCtuiOLgams4DU2Kj1MG0wagVk14VX\npBNH\r\n=6ora\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "10.4.9": {"name": "@testing-library/react", "version": "10.4.9", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.22.3"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "9faa29c6a1a217bf8bbb96a28bd29d7a847ca150", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-10.4.9.tgz", "fileCount": 29, "integrity": "sha512-pHZKkqUy0tmiD81afs8xfiuseXfU/N7rAX3iKjeZYje86t9VaB0LrxYVa+OOsvkrveX5jCK3IjajVn2MbePvqA==", "signatures": [{"sig": "MEUCIAGlQ/r85t3Fq2Qr057WCl41BuVnNUO88/0KXau+ytngAiEAxZFQz06SUr2dvAgj/N2RLEfT61etN6powwzIkzXcWT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5220424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfP3AVCRA9TVsSAnZWagAAAowP/2/shP3BFGBCgzrz/4K2\nol122meZROOf1PSvaTzLTNmUflXNz0Rd4NwKWuEi5UuAI9u0GH656DaQe2rY\nhtcRizNoUYbq99ozhZNqOISrKl9saZVSd5aaTXYfTTEjrFb2MzTmcUDDIKoc\nD3SiQEybhP9pyMUh1vcIJPmF15Y7iVgnUNDmnL6KlbmRkZL7+/MZVIDVIKQK\ndNqXfSFtZJgmHqH2fOmfNJeJflKW4c30ULESDfLPEry+hJRpkRwOfZdAR71q\nAcrZ/j53JXHKpSDJyvulvX1wZvrzD/H2XZrVTPAwZWpDVZGdena5FVmH4T2t\ntO3x9JapgVAm1jAHsyyDqJXiHWA3OZBu+vrIdmElHYxgDqxoNK+ldS3JKZKI\nsECL4A/kwxgrm9QYig0K56wUcOwDQqIdaJVU/jysddxtquWhACtrz5h1OciE\n8J+61+ha7mvvNAbWhCSAvlYZB9eMWpLMrVa5efNrL/6kwbhf7AZH+uN1+vvn\ncH6m8pV/2ghEW3p7Onbe7tmIkDLALsPQxkyrR2KGTq1riMC/GBr9tAZFFH5s\nOmbGWRrpJf3j9jdevCJ0+nll1WOcdJLSq0PYfm/s0NKRkNMK2hbL6QtSFgh3\nszYNEh8Hcl5Emh3D+JHFHxd3/7FrmDec6z6ka+O4P/OMkmUafoub08Wu491w\n8oG+\r\n=QCx/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.0.0-beta.1": {"name": "@testing-library/react", "version": "11.0.0-beta.1", "dependencies": {"@babel/runtime": "^7.10.3", "@testing-library/dom": "^7.22.3"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "3.6.12", "react-dom": "^16.13.1", "dotenv-cli": "^3.1.0", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.10.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "14b4a418f8e9a1fb48ca7ac5d69b4a017a4bab54", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.0.0-beta.1.tgz", "fileCount": 28, "integrity": "sha512-tbuEjYCQiYGIYw7IG2V5uCwo+E5rpZXd5bEHH+FcLVncMvCOenWC5mMwhgt1YPaLJyBtYeQ1MoDNvpMFKm1rpQ==", "signatures": [{"sig": "MEUCIQCP5mk3V6s5bsRyZMLowZrAN7ySoB2pD1AhVS8zVRYnYwIgY7w2a72//yMppQ6RkEveDYDpa+CDHkpG68qrfoL8XVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5183871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSx5MCRA9TVsSAnZWagAAhC4P/iMHMz793qdfu9acYFLE\nOex/61s1lXF2Uf8H3X7igw7jGOH9zw6IM0l1FFJZoYv2RGZDteMcs6DXK+9P\n2lYi+bSFirngmfbaAkkvG4A1SG+I1lC7gxe29zSyH0iu17/uEwxhk1tZufho\nMBi6GrW7xVf+p+AQABF4dSGSDc8q6/foCpVcPBzhAndJ4pQxgn9E46fXHVdU\nn/9a7+giE+7/xHdZNsaH+OlS72hB14gnWDn4FWs7h5FwNdLscWBJjfFadPWP\neWLsQNx0fzhUHc/WS7NAABG8IoRhAgYNI4FkVx6zmpwotg6D7pfmgxDX+SR4\nuNmgiYV+mvPfPX0isssmjq0g4uP0MiLGm/rnlRDwzfx0kO5/0Ihl2bwzEU4c\nFcq+dA5i4Fqlxp5mpQYQ4TlV9cLaXOaR4EJIcKq1tzR4WEgy6A1Ddv8zeXQH\n1hjemakpRx4YrF5ZGRBWxj62WVQr70gmScb2wg0tboUUAHBz/P99PK5p8/6G\njmFz0lshkjLL/eMBNkxmtGeO4CR/pJGs4+bTPK0mhOIFzjNV7jzBJSK/zxpo\n4eLX/BKo4PQev2tPVHVxW2WNMmBKIK7vIZQGwOaHBPZWGxxkGHw6kQJppZJq\nhLAeiAn3BAxqGcUfJ9Tw0lzKf8W+2PwIiUeAw14zqkR/1XeDCyi610txfPaS\nqaAK\r\n=0l0m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.0.0": {"name": "@testing-library/react", "version": "11.0.0", "dependencies": {"@babel/runtime": "^7.11.2", "@testing-library/dom": "^7.23.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "4.0.0", "react-dom": "^16.13.1", "dotenv-cli": "^3.2.0", "typescript": "^4.0.2", "kcd-scripts": "^6.3.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "b8ab18752dbb09986925cda73da789c3cdbc4ff0", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.0.0.tgz", "fileCount": 28, "integrity": "sha512-C3CL59UljaWe4f+/0vdfTKz54XPGod56egcdQLysowpJ/KUMyr5rhY2z+qRzbSlF73Soq3vHFGm+DTnl98s9fQ==", "signatures": [{"sig": "MEYCIQDFUpFe1JZPlJLzQyHLXev+ygR2O425f103LUxL8uznOwIhAJY5s/PLcMA3lqJM6UwkQnhKVnUdVu/e1PDJCzS99Tm3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5185335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfT96gCRA9TVsSAnZWagAAgzEP/iGkVLZ8TYkie03UUXCN\nBnR4sea3rDRvwGsu0Smz2ZJmNTmJaxTM9eOSgp4xN/0hdU49YrmZX18bxaas\nQh9m+mVGgUIJbOkuwfkS9jhQ9UbuaVHUjtxcS+9DsOnrtrhEnMwIWR7IlwkY\nBLe8KhnKr4JENlCVDb9Rv/1LFYHhhM3wbVCxE2QEb90b4m1N9XuU+a6q+DmQ\nezBzlW3q5hndB0I4BTtl0hTuP+8V45m0K4fUvE3geJcZrT2zDbmH8EKxtv07\n37kooVzv/u9i/Qt6tPFbDMMLguc+mK+ETN/NNSRmmAhFkRJNqu0jKjF1HYmj\ngmdEkGJATerCvnXYysygxvea9dp86qKoeS8r1YmM4g/jEyELjIlxuQpOGyRS\nTBb0KCz0DCApnJG7kj7AkpKCtalTWAmEOXxadHPh+EiOahDbh+k+uoOCHKyA\nSTTQkZLfJwL5I3oTN9SfNo0GWpjBBVCOhVORJAbaanbGGHYgVibnAv9jhQmI\nItCsXSNB5f9xvFpFrWn1llaNOmBBqZsCHc7XWA9I/tFtPgwJqhu+8qs1berF\nY0c2Tj586KgNhM1qloLMy27CuLwNnTZGON+MAaeyuzph6xc88DRpMR2H3tS7\nWFK8wrm4RlVingRqPA0amrWrLLc70ynnsYoyGB9sPzGiLuik6SGfcTLiRQ0C\nhK7Y\r\n=Si2Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.0.1": {"name": "@testing-library/react", "version": "11.0.1", "dependencies": {"@babel/runtime": "^7.11.2", "@testing-library/dom": "^7.23.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "4.0.0", "react-dom": "^16.13.1", "dotenv-cli": "^3.2.0", "typescript": "^4.0.2", "kcd-scripts": "^6.3.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "6aa0b861cb5c72948d2c11fdb2f72840d17b53a6", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.0.1.tgz", "fileCount": 28, "integrity": "sha512-dpN8pNLncmDX97NIVPjdpj4qYra7+ODk/JeWF4F+qaL7pU84YX7YVHK0pM4zbqTKKPypc0aoL8hrhJ31qYRmVA==", "signatures": [{"sig": "MEUCIH+FG5RSDw3IKNElc5O4x/bF5q0emhs5JkKmctc7wLGGAiEArGd16cbRd569vV6ewAyq+fwjE8nvsaU14i0zwALZG5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5185667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUI/gCRA9TVsSAnZWagAAyi4P/1Fmx33qxGCwlJhFwqC1\n437HM98jcns0U2E9k0KxWUK85msFZM3oX5G9DG7BDexJFH+GSXr/csTAdTy2\n9lDhG24VRJeyk8TyMzmDTMOFErS4oP6jdj+URXBS5TvJSUL9mrOASRJFN2o5\nsYzxS1I9//SYwSxqBbvOy84gqNRyNcMq2Yg8CdDBnid//KOYOZwsSDuOsIiU\n5ty2nj99rTekG0tTMdv8eIlEM+UnzDEMPaRnw5VPHyx3enxuiQggkg6eEHS7\nTSQ+Ghoa3uO9WUXR6GfJdQ8m4E/RL5WHEQnQYi3XlTUVCy6Sdn9vo0289oNG\nrKAgiErLkP0Wla9WHt6Tfwm8eGqlwF9ld1Vv3Z86XtrE79TlWQoQIcQpZ4Xv\nbhOsvEERPckij+8nlk8y+4MpeksRGJPvkNoVTwHjBe6eJVrAIYBIIY2RB9un\noxyhikgiC/Fa8rO9RT4i2TegQ51OBc2HrgVlVEZk1II66PoIsu19N14e/DOA\nTTCjPmJo0KqUpCXBguTTZ8Z9Uj4cRUOIOunXC+vEA252jJuHuj6+OrHo4nEn\nuScH0mhhX4vrNWZLpB5O1EK/ewWVVCtUnU5qL4tddbw05ogIeHn57eEYI/ll\nQIxrMQznJtPWPS2aIn/XA4ydOSz9kKfYYeMvVi4wjKbA9CTh/fZeMT7an0G+\nxY1x\r\n=rXnn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.0.2": {"name": "@testing-library/react", "version": "11.0.2", "dependencies": {"@babel/runtime": "^7.11.2", "@testing-library/dom": "^7.23.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "4.0.0", "react-dom": "^16.13.1", "dotenv-cli": "^3.2.0", "typescript": "^4.0.2", "kcd-scripts": "^6.3.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "4588cc537085907bd1d98b531eb247dbbf57b1cc", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.0.2.tgz", "fileCount": 28, "integrity": "sha512-iOuNNHt4ZgMH5trSKC4kaWDcKzUOf7e7KQIcu7xvGCd68/w1EegbW89F9T5sZ4IjS0gAXdvOfZbG9ESZ7YjOug==", "signatures": [{"sig": "MEUCIQCzPHAHG3yYIwuNEOkXbwesrmFo+gWzdIgNvmqadZGApwIgJxtuh4YzwsteyFDP+e0yD5ipgZq3obltOf3ObOeXB1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5196469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUS3uCRA9TVsSAnZWagAAx+4P/3FbLhiB300I5t5NMtdc\n6P+j3rLiu8aQuLdmEapxCAgvLOJJPVWrV0HhkAUkxcTbsp3Ba75tH6fSzd6X\nuXcYNgCmDklFvL9cjQ9hjy+eMSBQX8CkcjPqkAjMaLk5N3nAHKRjE3hindN4\n+EsMNcgDCp+fI8/oMJiYOpaka1iasG6fTaN2yAG48BHnKH2Ojbo0G7fs8E/X\nEUeq9rTVJPz4R7v/ZfK49YfaEaNpJy3VJWKU6mh+7KEJt89j8oz3oVMQi1J9\nhBY2FBZGEhIBYSahC8YjnJDY4cllskhiljxzNRYOe7CZvmRitgokGyut2kyW\nVspcFKkkB31V/Gagd2pxkaWSqBN3FZNLnpjecEEJ5pOq9XW9wltwbX8QwPEk\nocUtlIWG4tAJFcoPVCXIlCOHvI1ObAq95HEM5hHwLn/jMHg2XR/YeGrSN6P1\nZH2TbyqK9XkDqTguwaq5mBjajhsj3kWSUCqUl1k0cN87/DYtQuf/jbYJDXjH\nNSuklny4W5i5D+CKCPMce4LGCO20eEPsyuDCcvlJPQT/uxyMW5Co1oIYOdYv\nikY49kYNbru4TMJUGjV7KhTi5y/t/8vVXn9Z4yEjUmlY7cMMvMwdaT1R5zl0\nOELUkzjmgNPoxHnIR8OsJg3UmQS8lQJJIi/eBCtZ2PmQZTlgt0E8Q9FTkDYj\nbHyi\r\n=rihu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.0.3": {"name": "@testing-library/react", "version": "11.0.3", "dependencies": {"@babel/runtime": "^7.11.2", "@testing-library/dom": "^7.23.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "4.0.0", "react-dom": "^16.13.1", "dotenv-cli": "^3.2.0", "typescript": "^4.0.2", "kcd-scripts": "^6.3.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "2df00089587462847c45aebd05f71d2e609372f0", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.0.3.tgz", "fileCount": 28, "integrity": "sha512-3T2aAZSpKtORY2iHt9lg2PRPoQWNYstsooRCTSERNGRkvWLXmffE617Nxh9MeNlgkVj9StQLWuPzhAeNC2pjfg==", "signatures": [{"sig": "MEYCIQD/tnEiCzBORcdwYAoHEiswiHTjhVlFZhkrUVvk+Y5fVwIhALEH3+UBeoUaYz/SyAbYz4zEbCAB4ehMArQYv3FUQH6Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5201964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXkMDCRA9TVsSAnZWagAAjYEQAIyyPe+uRAeyhB1gc0Kx\nI3x/C88bDwWSm70LBKFrRXGAD5/e6efnr8UsIIDqE9H/tsfBjKwgVgMERLyX\n89za11UlH6GQgWOLMt7lYEySHASLx3sCOsYKR/LBboKmOmXcmdp5qcYqulTy\noVLngJwMqIttrIBBI+SwP3yZaHEjt3Yxuj/6zg0v6P4uaTiPStVnIfnsJtmk\nFGFpgLlQDR7tu5iodgESBsUw11pwRd86gxSc3rNzHKVLdW6ekntAvl7Vd/YK\n1FWdJpJ1SQLs2OGho2GkG8bUDC1mgjDTVVHTps+9lqaK7kf0sstbibc/sPSk\nGwuDNCKQdpunPPgbfjkSzYU5RpW+5AgqHoROrPK8HzmnzyWIokkV4owvMMEo\nXna+WhKh9FaEwBO2Mcme7A3EhEPrpQvD8jJLdJAOmQfn3dOK35I0b/u0i/Jx\nczsGJGWWX8jz0+gHcSkxv7HLTxZXr8DUt/i0CCauZDrAwLSYmNT1irclyB5Q\nyFqFXDR4xIw7zg2mbQ5Ezfhqm6xY7MSg02hN6rQWS20RZOnvw0MWQDYR3V03\njSAbvfszwJSZUHAEcUkAKYrlMQjKxlc5DR63FiMM9/ugsmWpnT82Jqx2QGzY\nFjDI4gJZfP56Z/osg5oThzTUsgW1K6d2P+XQ1UYhXmGocsNTzhimCbKisiSR\nqPx7\r\n=q/Ao\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.0.4": {"name": "@testing-library/react", "version": "11.0.4", "dependencies": {"@babel/runtime": "^7.11.2", "@testing-library/dom": "^7.24.2"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "4.0.0", "react-dom": "^16.13.1", "dotenv-cli": "^3.2.0", "typescript": "^4.0.2", "kcd-scripts": "^6.3.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "c84082bfe1593d8fcd475d46baee024452f31dee", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.0.4.tgz", "fileCount": 28, "integrity": "sha512-U0fZO2zxm7M0CB5h1+lh31lbAwMSmDMEMGpMT3BUPJwIjDEKYWOV4dx7lb3x2Ue0Pyt77gmz/VropuJnSz/Iew==", "signatures": [{"sig": "MEUCIQCH7e71pLETCH09mVbm4bjRMLOuS4j0fm4p0D0qPvU4SQIgNYfeTgEHa25dHBEvp9aCkmOpinD8m4qiu3ZlpFdl/A4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5202274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXmkGCRA9TVsSAnZWagAAFcYQAKN/8uLnWtktYwgh760m\nR3TgQjK+/HWTxSXoSljMCHhl0MwwHdxhvVPPeBXgHTMcm3xHdSx70tBGnYGK\nMl/uypcR3e06D8ej1sql7KV2GxcKGHlJBtvG8j7f6o138q25t26qhHlYGngU\nmP0F0uXB4kbFoGBYoRPTY8fjwLaO4DUf//1Kkr7xlrHOE6PnMetR/EO4CCT5\nryDI1XkidZQz2dGEcFNOqaqqZlIfSISOna00cqVQocA0800gzxeaM2zbQVVD\ngdZqFldSHjvy2ofack7rKxJKxGXEAgdlMZTQdsMAldtKJpPsH3+lGRJ6b348\nMU4cSTAiXv7vEMuq2P/f99lW7Ygu1jnmnVr4NhN4uB5RVMC8oPqrUrGu3xx/\nwDTw8KTFT2fSgf9SHkvS9t0cLetBwivFaURAjedO2Re45IpG+3pHcf8GmRMv\nDgK3r7obgObFUDiD3DuQJ11ocqKBwtUyKdaMIhZhpkz6MVLZYqA83eK0pyC2\n/nien3DPEiC7tqdmh05q+bnWOYzz3hYfLBKD+hcxRZBpcTNAOVirm0RWMdQh\n18xE9+7NMhulgOTBvhEA3wSa3pG0L2k/miiI9ASBkdlV3gkOZzIYLmffEpXN\nDDnvIUnk5d0Y/zQzuW2frywvwG1b5//3fyoJkpvOv/5zfCXjBvsHozPOPtH+\nOOJK\r\n=Hj/u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.1.0": {"name": "@testing-library/react", "version": "11.1.0", "dependencies": {"@babel/runtime": "^7.11.2", "@testing-library/dom": "^7.26.0"}, "devDependencies": {"react": "^16.13.1", "rimraf": "^3.0.2", "dtslint": "4.0.0", "react-dom": "^16.13.1", "dotenv-cli": "^3.2.0", "typescript": "^4.0.2", "kcd-scripts": "^6.3.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "dfb4b3177d05a8ccf156b5fd14a5550e91d7ebe4", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.1.0.tgz", "fileCount": 28, "integrity": "sha512-Nfz58jGzW0tgg3irmTB7sa02JLkLnCk+QN3XG6WiaGQYb0Qc4Ok00aujgjdxlIQWZHbb4Zj5ZOIeE9yKFSs4sA==", "signatures": [{"sig": "MEUCIQCQlAu+KYH5iQhYetKzi8kaIOhfizuh6E0ocCCPTeGmAwIgFyeM8nR/F7p7UswDcbj/BCOquwEbq6++933bmHTZucs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhv4HCRA9TVsSAnZWagAAVxYQAJTDsxQEkDPfYUo2h71u\nS5Oqkv0b94X7JoIHqsGvjT/A16db3obW3P9QDCeXKon852zOttHkiItoCj7q\nqD1S5atgiwO8lhL80qOMa+3DK8D6+wM6r5DVpyE4xIRuK1rjnIzxDk0iU02B\n8PvcIaDElbdas0/xXPRxuXWBSU17aIuRRV8/qEC/NH/nyBAcFxjOxy/f/6/s\ntZl2N/Ks9bHzg818u8CSxJKntw4tt/cZ/5my60MdRzIL6WYc62yloSqr+5eC\nkuf/TJNN9bZ/4EbOxeRiiLsmvLTgeWg44A0S5ooQ6KEVQ5Get1eb0UCB2j35\n1dGnJzREzgypFYsC9uSEswpa1xLaSvQGmMkTy+ruNhtszgmId1S693TIPeAP\nBnq6Oyg4kBqOw7qYGIxbzTyrgzWJgroxD2hlEXlqfnFIX/FiWDswp05OK3Hy\n6nvYjO0V96gOqbzxBycGGjt0nzi0fXSrItgXe5KP1iPndEiolAVNisjd3d8d\nQJSD2vK55s7qsOvA8IJCYEWys4TsAfGYZcFx99hgjM2lzaPKx9qHk+SxcloZ\nEXb+1ykdLjVr9fAnm9Hujj7kS1poGN4mhORAsA5LN26Kg0m0acd4n/pKm/9s\n1oVaL1Zy7B/g9Gx7pf7KDWBWRoblQZpZlYUxeMPhPImxQnUZvVPWfAcUksP2\noSns\r\n=jPEI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.1.1": {"name": "@testing-library/react", "version": "11.1.1", "dependencies": {"@babel/runtime": "^7.12.1", "@testing-library/dom": "^7.26.4"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "dtslint": "4.0.4", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.0.5", "kcd-scripts": "^6.6.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.5"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "226d8dc7491b702fcaac2d7d88d42892e655893a", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.1.1.tgz", "fileCount": 28, "integrity": "sha512-DT/P2opE9o4NWCd/oIL73b6VF/Xk9AY8iYSstKfz9cXw0XYPQ5IhA/cuYfoN9nU+mAynW8DpAVfEWdM6e7zF6g==", "signatures": [{"sig": "MEUCIQDphdVKqeMgirqqfoxMALcmtL80vKbkjsNReC5B/n3lMgIgLo6hEsvDMBfPV39obCyo0AyPAjjPbCPE+ta0Y7t0fRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5427851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoa4KCRA9TVsSAnZWagAAey8P+wcrRnjuXdpOQ+Gtj59M\nu6IKdGwxzwraL2iNjGh69JsY110rImJncmQ+yzKTIyaWgAQ/xjqwoFJXHWMv\nSeTmx12jwPhpTFMk3p4LM1U1aSMVllDECCSWRmVfu9A7Tx6rk756wa200Duv\nryjujUdGVJrHY61NFloI7UXkCH2O4QYnEwQaL241OOHPhAjsYr1C+w/imHcJ\nmG+0F1N82XY7+d35t/jtJ1xJ5nXwQAuRysu2TFIDqgZoxMqCh/8zseziVjRK\n8vbw1NCE0AAHbV8gS2z5yDNaDBQI/ETPqakhyYuadRE8EUfQuBoAqklE0726\nB0hhha34QoP/fJX6tl0Us4UImuXuFsuWBSeldn1QO+enWFjG90jNI55mii+0\nT2o5u/r4kTarchOXvP5JUI3KwJjQFykttEIro9txt1l0u1ExCKfFHK55QsJX\npNMO/HDhFNB4hrvqAgeVpcNDS5J6M+25MH5BMMPtFaYP4fjb4eHdYWrUi4uB\nq/lnL3STWRlvMzpvnHOoR0OotVLlyYXbco5PIubA9cxRSrPAaTIKe68Sl/XW\n/4YzAM5W+TPxoMTycg6wnA6LJHwNJ17hfIPy86XOsXe+xaL15xxA3HNs8sTu\niqoMEvMMZTh+ejuTDRi0Sm73G7lGUgrvbTNKGDMrGRrxO2VPeypHp6lAXrOn\nAP24\r\n=8OXG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.1.2": {"name": "@testing-library/react", "version": "11.1.2", "dependencies": {"@babel/runtime": "^7.12.1", "@testing-library/dom": "^7.26.6"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "dtslint": "4.0.4", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.0.5", "kcd-scripts": "^6.6.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.8", "@testing-library/jest-dom": "^5.11.5"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "089b06d3828e76fc1ff0092dd69c7b59c454c998", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.1.2.tgz", "fileCount": 28, "integrity": "sha512-foL0/Mo68M51DdgFwEsO2SDEkUpocuEYidOTcJACGEcoakZDINuERYwVdd6T5e3pPE+BZyGwwURaXcrX1v9RbQ==", "signatures": [{"sig": "MEQCIHEiyIWQoDiGTE6S06xRihFKW4TNo+7u3NwL9e2svn6bAiBRLvMbbOfNXOdJzxs5WydD2pGvl3KQ9ChCiP1myBhtng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5447078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfq6l2CRA9TVsSAnZWagAAoWMP/2N2/lTi8Hz/vTYUj6RH\nHIykD/0az2rb5dqBSP2pgQolIY+5+EBcNeogGkW105naQTDTeQrLMJ7e+JyX\nFV11c8X8lJ6TYdtmvC6RXxWwP5NGgbTrz7rRM8VXaRbAPn3JTG5Fstj26IZX\nkZZuuRrHgTktHh+tBvt3zi5du1eCuYX16V/TDATT+4F2V3Uvb85puijhsuoJ\nb+VKdG+y7MsXBSRc0XnFF8OXPvEb8RWQYBKHGXhXc5YkDOY6ouIDw13MRtvq\nYAzO6gmiJAFjy/MB+jPw7xda9Noam8cUKdY69XPnaBAXHVBE1cA6mVj2XA+F\nqpXqVnp69PUq0l296fvMrueVYxKYrvUmfOdWYvo8fbKwmKt3nQtVp9sXYqOj\ndix0pV6OR/kLFDH2pjfSEvLkTRHIHEKDJO8xwZtsxCn8rI6LfK9gcqur6otF\nD4huOmZjR0Q5d8jGtBAXbimRS2BN/86FqjzUehdLcePNNmToWcL9bN4Jd8UH\nS5q263mwtHsIRX31h0ltWU1vQ9+QqDrra0Tfu40SC1eSwckSiekd/zffYKfA\nWlsQyAkgTM5wvEwZ7QsPvajtSLd09Vv3lyYKTt3beq57tN3JJgtZOyLmhOtK\nFdfRUdRgZ+WClRv0YQSbgStLJE5BPUqZ699dqY3SaTB7PisCJMix8XmAkxg8\nC+Y1\r\n=KqyE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.0": {"name": "@testing-library/react", "version": "11.2.0", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.27.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "dtslint": "4.0.5", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.9", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "ce977a76b6342ea95c71ccd6de3012b1635fb559", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.0.tgz", "fileCount": 28, "integrity": "sha512-90xKYJzskZ7q/AoSuWraQL4EGZlr75uZvDt3nrO4M+rugN02zjO45tmOBq/JBOgDiMIL1tkhHioKXjJsVaSINA==", "signatures": [{"sig": "MEUCIErlaJeoTRhG2ovuh/cM4An5taWaSW+UFjPBbI0XsFBhAiEA35NF8GTAX0pL9q93yck3ytyoX/H7cNQVBB9KKlhFsV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5444243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftVALCRA9TVsSAnZWagAAASoP/jcupy1WG67sYSucw1J5\nMEcZ2sal1XgCEtqWMrQhZZVO8NQgyw6qz7ZbnhLDAzXukQwcRzNuf8lqOHG2\nEmM/gOmeWDlo57AELKZ4Obb9AEWwodbbYgeQdyAfMnzJRnbI6g7pOv3xIIho\nyKEHJLyFVHftYr7KqJZLm5ABGjcLYooEh84g2lzfg4G3RoX8AwqtavT/WaDn\n3HPwnohX8tFaRVQobUPiV/5upGST9RmDqWfakluTx4zhq+QukWlFQmtd+WG1\nd+O07ji93DnDM1Nbm8sL2dAZ9nP46BZGwYhgqACVsAj3Bh4FvXxTrYpFQmIh\neny/Py2z93M/YK7YpyusIFXSR+AYzJyUn3qKDB87JbjOQ+HB2DHuTfQNTitQ\nfCxVwa+pgvoaohJK/5VdoARnWUgD4J2f2ALCkU+BrWIBOZRTIbinI/8jZkan\nLxWgMmhrQG50e4ZH2vrHxWq9Ay0XpPTlbgdjq90jshsVwl3dbkH66wT/HFVl\n7XDB+54lEkaKAVMvUK3wFR84bi0DyTwRfMO489wKP3FhT6ij1WGkO0KjJp48\n5nluVgtYfqxBMh5l6hy8Clg06outaeQd6qtaSaqPNATm6lWnaYiziA00QCq+\nuNpTmK7rEyzGJNe73e+fiU/iGk/sGVxMZu/bX9dwfx2LIuBSkUJBRcwoOvsO\nwB7f\r\n=XE4Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.1": {"name": "@testing-library/react", "version": "11.2.1", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.27.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "dtslint": "4.0.5", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.9", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "01b3d2dd5768ac27402b5d7e41c87e605be3c326", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.1.tgz", "fileCount": 28, "integrity": "sha512-/rKucr9p/mhMongaeTXwgIRfDnsAUu6LbfN+moNUn2oU0Kw5a7inN5vGvPWv7Ef0YndpERAfODjeseUIlhzRHw==", "signatures": [{"sig": "MEUCIF9ycOkyfadL+rkEAe3zzDJph8KBXcbnutJ9NZD4WPdYAiEAqCdIlbFPaBF+TpJPX5p0gaHLISFrt5epBcUklJkrQRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5444219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftkzlCRA9TVsSAnZWagAABu8QAJeFvM+hXrj/uNQVKAkd\n5dB9dWG/qruwjuLuVwHQXjINhk559ZjC4UDUTX0UTfQAcyrcZ4N3jUxd+X7C\n61Eze+e4q5H0vVjkmefmaZPjP4oBBQwKWwnn9PnUqKfBE0AkH6gMdkepNnua\nNkStwMwbfTpLiMqwJagh4deM4+EOm2XoMD4EkKuxf0MjT3Zv9GKDUrnjf0jw\ns1f9xQNZHD1k250vvBnSMa8dn/6V1yA5rXj4sf86w0JRZhg7YK9flDUTiITU\ncu1OCYbpngfhocQJQfVjATN+PRX6H40+5H0ujpZyzcDhIJmiEeh0wdghj53T\nuOuKjDdB1vAG5HrIhvQxj+RtO0p8gYcvPHc6IlLo7QPw0NIaR8qo2/TrWLSn\n2Lkasld0QLly1/BZ8DeHxH85kVAvVRKc6KzDs/+pBb2I00tDD1SSK7tmNO6n\nC4Gbq6OVUx91WEp14et3Rp/DuxxEaX/H+A13qGgKYkmDYhL/VwAMDXMtDwyU\ntmwzIcxPU9k1jg3qN+5K7rO5akwJTFS11Gec2CGQGQQW025UFcNSMsUwENzQ\ncMj44JX2Ffq8kZXFz0WK/Kho5WLXQfBMC1IQGZNhC4o4jLQU4NF6g+PtxyFa\n6IDpQXib6rEtHv7ML3YCy83b7+khF/3iWeZCu+rJguSif0ODcQgJNBdsX043\nbMSv\r\n=kEeG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.2": {"name": "@testing-library/react", "version": "11.2.2", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.28.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "dtslint": "4.0.5", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "npm-run-all": "^4.1.5", "@types/react-dom": "^16.9.9", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "099c6c195140ff069211143cb31c0f8337bdb7b7", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.2.tgz", "fileCount": 28, "integrity": "sha512-jaxm0hwUjv+hzC+UFEywic7buDC9JQ1q3cDsrWVSDAPmLotfA6E6kUHlYm/zOeGCac6g48DR36tFHxl7Zb+N5A==", "signatures": [{"sig": "MEQCIAWhMML0gU5kpsEIDW/Yz81h4PtBt/e8RnaR8nsaTCwXAiBt1UScwN72ut3yaQFGhmfpEeDFWeN0yq65oBZg1V7Gaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5457327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuFLzCRA9TVsSAnZWagAAuQEP/A0S7a+m8LpAj9WRVkM/\nwGV29Y/98T2kLm1aeQVeh+HZ8H2woCS2tE8/dfFChXAFrQ6OXEQ30Qgm2i0R\nGWC3Yep3oT1IVE+ADp0ZVAGL2aaJxqwrm8Dc6uenasxkDUSiuSy+P2UTToel\nlC+76xfewS/dTcI6a8bcPiwIWZwjI0rfD23KFgPM3u1/HL1QczncucfVwhEb\n6mD/R0AUcoaK+V3wawN12Q6U9vopwrDXxYm96VJyMzFt+u47PuBYvMg8npDH\nXGDLvPNY5BlBHaUXjt5X2SpBDhtdeUapuiUoHWlg4js+l4HkRDpoD5MegkFh\nr8gMOlGSA7y1CYvjugzrXlxZUZt2IZPSjYG9sqgWCC3Z6d2HN+oK3+o9zpIe\ndmPGt7abeK5FzfH4B4QSBY4HAUYuDna7OC7iXZ38qjq2GNiCL4ywUyz75PqD\nKdXAU/HjW02Dq+SQzgeK/h+S2t/6VuCTQnjZ58jzCuhAJcGbCII63oXJh0Mb\nqJn0czL5JMPAbjqlz57a5iw4YyPzvIXakPtKZfPudaUYPnnuTp9ZC7yP1FbT\ns0bJbJ5pge/4SUztoYfeHc24Z7mGl0ggvleYKMFQNB1eBXmBNtgzZdQVYb3o\nPBgZYlHVJGvGVRghrvd03cHQ3B/jigwCzVrPZBznyegTZgsfZg+/1ojpJZT/\nayRX\r\n=0kXw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.3": {"name": "@testing-library/react", "version": "11.2.3", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.28.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.1", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "9971ede1c8465a231d7982eeca3c39fc362d5443", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.3.tgz", "fileCount": 27, "integrity": "sha512-BirBUGPkTW28ULuCwIbYo0y2+0aavHczBT6N9r3LrsswEW3pg25l1wgoE7I8QBIy1upXWkwKpYdWY7NYYP0Bxw==", "signatures": [{"sig": "MEUCIB9gkgNKCewl4Eo0XdROjoCCh/k3NzOoHDNunwVFh/lXAiEA2paLZpdTJQjVr68V6dBT6iADGEqRg9uMWGevjm5cnBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5463121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf901gCRA9TVsSAnZWagAA3d4P/iSrFkkdFlNmLRHjvEkt\nostAcwcTFOLwOuV9u1KnQU0Hf2yaJVu+/5Le/BbDOrL8cHOXjMhdp6+Kser8\nJG5vO2eVUCPmBwzN1wYuDszKvxonldY6FtzJNdAumr6+us9LD8WflSdggg/t\n+9iARnF27g4HNnAfdPmyr1J0jpi/sTS32SKnXNJMzuLcgYAWNyPgOQR5CWOU\n5OvC+rK3L+M4RraQ4Qvd0VYeSKAEdHciav5RiNBvy5QzEQxvq67xmoMH4lNo\nn58ZgT2bNpfAo3CH+OxW/FzXZq0aaU1LquPCSsJkuZEP/yjQ5cKmDdjRWt0M\n+W6V/hoqJhwjGsj6vu2PJ2akfnNW+fp0cQrUNEuiHqBd7mzYA5LqYPywep+U\njmMbLjK5leF+e6FuEGx9+JhJMSoEu5cO1rfkdYuOmmCSZUjagB9tVmQiigrr\nPXOstQtXv1WgL0K9zCIAEHWJhluNgOlHvg+O3fEWeYJMLy1uMXl8CEljIjRE\nD8b/c3zd5LMomPNN9tXwFahGq73XaisfwtReeSfW0ENddsKc77ds6OPcKqrK\nATh3rpKXx5EqPiCEftPWR3OmtpPkMi4b4ts1qqLWr3wACZNHpEhdvvCJrdxw\nT7QRS5gBFKCsrRtR7KogkQAW+Fd4srIWj87SKMK76Q0A3J4Al0XzpmpOWA5o\nny+l\r\n=aE5T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.4": {"name": "@testing-library/react", "version": "11.2.4", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.28.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.1", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "85e39be292973c49d638df20dcab5a7e73cda2f1", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.4.tgz", "fileCount": 27, "integrity": "sha512-0j4A3eHc89g6ymLhvDzwIwTCCKf0voO387ACvJvH46YrBUFt1E66PvxSXH/u/kDVhLs8dtSPFCM06Bjo4sUojQ==", "signatures": [{"sig": "MEYCIQDlpHln58ufhrNnnC5IK0VO1bH4b0xmqO2bYaFw6Frs7wIhAMq4Qwi2HYsqWx9n3QiuRYaKZY4p0EqWo90rH4kO0zRs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5536262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGRzPCRA9TVsSAnZWagAAJucQAIVA2JTZpdceM3qu0ArS\n6DWjseC2ghfsMu7tdIHH4ERj1J3I0FYylSq4QsKiIIlARQk7ter1NCh8ul5Z\n6TOZcpRwy9+6RtmXXB1yZZeSk7Cbstw8Ofc5wRj/0dPohfqh99SveimrKngM\nT2c1B83BasTowWdqd+es7/6W/Sv6QGcwIdnq0uJjQ9qIOdMW4Q4Gf/fuUJ9q\nA9u0b618oFbn+N/FfDH/y9xs3EyCn1nMNUl66WBUjo1B7DvBW8igOeWy6wGV\nqqky4yUjhDaH+XvmWJBfo4pC9GgoNcQ8DNi+MP9b7ZiJkLKu0no2z/7H/shO\nK2gC+uFIPpLblu1rGNJmoM8yqm0c33Mu19u6586o+WFpJNlcGtpG9yOhFXzv\ngKjX0ZbTe3j6jt1TSlGSn9D7DcXKe/llF6HNEhBDR9OD6JSht6ZNFLjuiLS1\nfywZmZqARMW5ZEjahktb08Cr0xcNN1083XGlQd8egMjD5XGMa6jBcYXBhPKb\ntgUQFERkE5iMY96kQ7RvbpNa3h+eWdi6AO2E3hR932KUMzyVdvRhF4EpFW3m\njqHzOAVUU13SwbZ8jRM2Gx48wOcszxuCN7k5w14NdOo7034p2D+lthqsFKxl\nkhKLLJtKDDaHlmaWN83cZ7Y8weLkUDFreWfR7OIR/ZMz/9nwMzvDNq8wiLqU\na9KC\r\n=xjCP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.5": {"name": "@testing-library/react", "version": "11.2.5", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.28.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.1", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "ae1c36a66c7790ddb6662c416c27863d87818eb9", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.5.tgz", "fileCount": 27, "integrity": "sha512-yEx7oIa/UWLe2F2dqK0FtMF9sJWNXD+2PPtp39BvE0Kh9MJ9Kl0HrZAgEuhUJR+Lx8Di6Xz+rKwSdEPY2UV8ZQ==", "signatures": [{"sig": "MEUCIQDRVsa2Uzq3wmuUDcOGrCTYjWEFO9F5RINnlp6P9dOocwIgaqhMaHFlXOC3lbV2EGqQe7uOfeq/E/++poC0h8IlC9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5537161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGYCLCRA9TVsSAnZWagAAVaQP/A8l1jWRFr/gNcdgVgfO\nYr0NS5k8kMCpMrTfL24fiHJod1Gv7hQ39mxewsj03sj4+zpAkcvZDeiZEriL\noCee6X5xC9a1jav5xIJW5coPBAtKPVnVLZmUqJeQon2Ay3SDprFTpcsuv8Zv\ne1aeXni/dhFnGccIFqu/CovjvC0sSBbWIPKzmqJePjBIyLYfRvmmFkzPtahe\nb67IT4/MWmlrrR22jeP0ENCvdsUznHHSXB3DRKq9Wd6w74kQ8aaJV2n8WW7y\nBlUVjLX83yM+MXNu70xU/MHFB91mcnNy3X5Hkt/AAApQVa3udee+S1j2HlaF\n2oY8M2tGQkfm4obG1KakoBldRIZNVeESt4cxeebqbs/nenQfEZuC4F8E0abz\nuyXZ1jhnmh1MrNaFZ4uq7SS7fLl/cdzUWInriD+sDTHc8Z5aBDuyH/3e9lye\nxvkauF4SYvX3mBdu8MdMGof4e46AvMBg8dcmeSYQOGzo4m3F1zgzGPBPRRFH\nFm7KQP2BBcr4jmB+QOeHwtKU796EPsGUKQ1mq7R+yRQetQ3lwF6AT2CqOsFN\nr5EplHOt0ypcFWW+1P9usRUM9x9LLgWJLpKFEIXabLQ01KK3FE2y02xhmCbR\nBCSZ9ub5qEGDCMcFugRd34bAQCv04PZdlQsPN1cU1c0EFqjGjNOMHxX9TSPq\nOQTe\r\n=oQsB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.6": {"name": "@testing-library/react", "version": "11.2.6", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.28.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.1", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "586a23adc63615985d85be0c903f374dab19200b", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.6.tgz", "fileCount": 25, "integrity": "sha512-TXMCg0jT8xmuU8BkKMtp8l7Z50Ykew5WNX8UoIKTaLFwKkP2+1YDhOLA2Ga3wY4x29jyntk7EWfum0kjlYiSjQ==", "signatures": [{"sig": "MEUCIBdDWFhq631b7//EXmuxKLitHycOfcH3HFr88G3c9wsdAiEArO+/1QO6Zkr6SowW6EYn0xeFUHp1j2L8seD8SVjKTh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5548352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYvwqCRA9TVsSAnZWagAAvLcP+wUEk1KGMpcvqvpoWzQo\nqIPF7lvYx9orCATFRhe/Iq6BS9PsA1QlxWE7xc42Qw3v1xmTUvqqaoZN1Vpi\ntV0m/zubfnYhbLwL99odvGuLKrEtM6i6+Rk9EJjsSFUJ156w6P1z1oTn0axA\n3AY5+wp9q5rWR/rnKFXUpancYSgAs452TlWm2jgAefT6D3ff9Urm5lt+wbYI\ntVnIFW0cb5g0NLBK2DzGWucM/xSgAAhibNv44ndtPBof6BJtcYVYjdQaqwD/\nwqmw6RjoWLaby50nOAI4qpjS7nzA0xxjU/o+CevnnjvH+4GQOSyJi0Cuf7Le\nbOk0IiP/TiOeolKyV6ebFa3Y9V8HsRlKGQ3YbFzhE1r9Tb9Am+3cGLlV+cbR\nGJ/3dn9HfHWYZ+TdThUh8knFMbbMz58+Bvjhbfr6BBc6rLsXiia0lQ68kCQB\nZauRsWTMmJvV1am+/zsH6AFiN2JB/ZGECt0Ofovu1FYNYdi3fkLDJSSpLAVe\nNO7shFHzqoUPQkXG+2BwyoWU8l7WhvPPEeG48yQgxsh/+M9ebtO8aSWF88co\nx5B8FfdWrcFT0AycjgVQUbY+Dway148yOmjkOZYVzjYD3JNlmd7X5VWF3yUY\nje+ZEG4aew8E0pH64TSBH5W4lUSum/OF2/S6uvPNx0T2yWoTZp6nWiZlrSl2\ng1NU\r\n=CZsO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.7": {"name": "@testing-library/react", "version": "11.2.7", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^7.28.1"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.1", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "b29e2e95c6765c815786c0bc1d5aed9cb2bf7818", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.7.tgz", "fileCount": 25, "integrity": "sha512-tzRNp7pzd5QmbtXNG/mhdcl7Awfu/Iz1RaVHY75zTdOkmHCuzMhRL83gWHSgOAcjS3CCbyfwUHMZgRJb4kAfpA==", "signatures": [{"sig": "MEYCIQD+gqERKtzfrrL72UWVTavLTZdFVTR9W/adF4zZ6+TjoAIhAKfCsGC+VvzYEujgjsXvxUOHnUokVdSbnzLIslQOSUGx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5571940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnnDJCRA9TVsSAnZWagAAObsP/i+nwp/KA9vcfh1quxXP\neOM5/OAEmd1tgFgYZ1trqNUA+DA5kR6ZUGKfcXxjOMODF7kw0mpnk/I1v806\nsVar1kmjLbCNMldOqc+LkzwXV79+VBPv++k/rPQAxe8TH6sJXMPq2WNbttji\npVYGOXplpiU1fcuxGhJkPTy7K+EpzL2XyEO4nt9IQaiaEVs1n2MZjAdM7TJC\nedCrn4KHyMdStYX41WclLBoxwfPDVJNVCvTc+p9/efX2AKPu2jBUdrYk34Bc\ntEezmQBqwazNUOdHXGvPyzWjEQXssGMZe4jCr/Bq0RV1AN0KAttL2YNIGvP9\n7ojVgoz2VNXMfnrabjXyIQ8M7qI7TmUf94wAFr43I9w0REeCV7l+Q/BUVwMY\nXVVG3TqDk/gMj7ePY8uRxcV+TLFuP0Pob4shhhJ96Ydpwn3CW33ZjJPIowpS\nWXy0crZBZRYOAELphArrXnWsnHPeNYiAd5sqJZXq8i04bNIQJf9iQ+/C7tIf\nDttOeV87o1ajQCCFgby/CWsEbbhjEJR0yTxVoTmbzh+cwKpaEFh9ltNenN0S\nzjGlVhWIWHTszcuX0+7CXHegzqt6Nt0qkybD8qv25dH4xveQyiCDt2C0DO9V\nKBqJR9X+l6KwT6AcgGGGt03Twacue/UVi7RXdOzstEtgrm3k5+PLlrq3mTW2\nKaad\r\n=7FIL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "11.2.8-alpha.1": {"name": "@testing-library/react", "version": "11.2.8-alpha.1", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0-alpha.3"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.1", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "1ee98d8ab7252b6cf2b46666dd3361f438c1b862", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-11.2.8-alpha.1.tgz", "fileCount": 25, "integrity": "sha512-ctzZ+aCf922PlRxhfoP0YKRUi9jyGf0J3CGqk9W4sdmq6PFCGFzvzd4OpqFwmzvdI5NeTdpvvml1mNTP3n7Gvg==", "signatures": [{"sig": "MEYCIQDGg8JBZuagpWSysFKT6MTbvjSoIbLloSPkZCdebD8KAgIhAPdFsPl4fx3lOIxwLsK9Cj2akNVOYtc+hO9av9x4RUdz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5522275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvzm7CRA9TVsSAnZWagAAnkgP/1YgwAi5C4Ki2YSWy7bH\nGBxjPXC/7Otqi47I5XI5eDZZBQVt4oVjAvq4iavfatJbSMD9H81liDqRhmdN\nzaWGshKGXerz0HmHwgbjpV3Vslw+Qpar388KxmaL5+AOd6ON+NwTaC13w7md\neoLRdmT5F61ayuDpFdX1vXhmCa31GKLJszEygJfGNzlKUbc1j/mfu9iPWi1x\n2T23ROtGI43/FMFp99v8pTbIqQDTIseP79R5XoYu6KFeFGsg60MqhRAsjDl9\ntMyLB0Oi6KXHO2sW0YPWTT4XwLgrqrpnAyyxzsVpVjXIioHvJ6PMf3/Gw20q\n8Yn+KeZgglE+Ppyfb3jNHVG9pg2pPsEmvjcT9ZeyzIp+2Ps0K0dostUr+C8/\n9wTCOWgyktYugGxiPa19xw5y1AKU01dVXQI+ReswhQsUSAXZK6YFtU/G3D8k\nGWnGKBFloCoI2t9RT8Jw6ux6D5o2Et1wRqXdcFsFSuytCzrQgweyEc9hM5Ap\nzYhXG6UZRyFVftkDGUnls0uUumKJ6eIWAyM2b64AjSsKdB7EycTUfVcBW7b4\nJTkTGQWF4ernUf8TasUYH1Z2pwol+PtoyWFyW/Fp0r02b6wc/Gckmmlq4+I7\nKQpWh5lZPdHWsiqb/fshBB2itrjbuezRLl6H59kmqHiZY9XSZQ2KtklPAgM6\nnLI0\r\n=L5Ke\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "12.0.0-alpha.1": {"name": "@testing-library/react", "version": "12.0.0-alpha.1", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0-alpha.3"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "63fa992d0e11e6643479b0b331aacefbe786d30c", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.0.0-alpha.1.tgz", "fileCount": 25, "integrity": "sha512-GfTXvCdWeoi0Uqa51ewGRRZNsbHjLhndt9L14re7xSlnWGDX28wdgWdnW2+SdrnCRc8aJZvPGay7B6ue51zy7w==", "signatures": [{"sig": "MEUCIQC5I3Fd/UkZhK8Q9rYQhxy3+Pfu6EFKNiYDwx5VjfJzbwIgCwKgeJusUJQfc9D+JtF+kXxZ8gup8RwgCqK+UMD9n8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5367521, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxH7jCRA9TVsSAnZWagAAPOYP/1vRwlCYE9/UrXt44yIN\noXPzoPsG5hsK0L4V8PThqTYbPiUUvw+jGV+Q8miPexTSZ8e9NnNhLDHXyVZF\n7sdYES6/9V/dMysDpzdADKAMTCIGzfCgGVeapFMIzz4KkZeAsOoyt7RRFuJb\nS88NV1ExmHVMMmFD7aYdNTtpNN6L2nImDTs13llYN/y/aI4wlni4OL/vC5Yj\ntTgiMvmyaX55BKpsN5zKs8UJu5LRoUFGiRKEOQOiNH2jPhMbAx2DG+SKKFo9\nvAr9H98WnIgjdnANqCsHDPZOiC/AHDW9Z8Y6V4S8Ew65w6G9f4ECcj8xxa3b\ndfOttQOk4nRruSorhVMxXTx/6SnBSyc0d2KhVa7wReBdpQgouPQgZots1DYC\nuRaYMHAJQEGTUN1dh87Pfjbz464A5xBEKeDBF+38DWt5TDIQRwkdBPC0VsRx\nPAMEBUb0uj1oq9X7ZJx9/yLWzzC9KVFPX14hamFUDdR2PFJm/pHKvmIZ6+SS\nZRcSNv3Sk/RIXzqv3T0e1SNibwySsewbN4+qYgH2TZsk5lBlgDtJW9dw7f5v\n3CSNTfoFiTofaUjUBBLqc5W0HpPbNDs8Kfjbc0oOnSKT2434O8HTi4fqQrZ9\nePdFVMAZzwEg0wcp1AM9Vf2uW3Ltn/jtkHQUbfQ2qIsKmX5opyaQIsOaozw1\n6TLH\r\n=wrUX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.0.0-alpha.2": {"name": "@testing-library/react", "version": "12.0.0-alpha.2", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0-alpha.6"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "812d8683c9ee334880dc12f7a24562189c2b138a", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.0.0-alpha.2.tgz", "fileCount": 25, "integrity": "sha512-tk5MF5Vo9IE7XcEPkwX1d7eBp8l6CsBQKTdT0VQ4jlrEYSm+z8oxnr/Aa2UTTR7GpdBujV54oOZj69ntQXOreQ==", "signatures": [{"sig": "MEUCIQDIt8PzWqBRUQp+GFVO8BP8kgqmJcIM7pQUKkw/Fk5fLgIgGu32+3EEroFaSY6QEMwl3Qfjs1XP+7W/+WlhzR/OG28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5367521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxi0GCRA9TVsSAnZWagAA5oMP+wZnCcMlRatZR4+Nhyqt\nk5HCv0LBwEes76MvJV85MAn25p9aRAcQfmuxUKyOcVbHTc8CUlpMPsZ2AowX\nGoViFbi41UWyOkaVJI2ri0HAWLemO4Y8CZpdwfFhBCkVavo5bHPTOGiodb/D\nBI72miPM9u1HWdewYux5u5yjNgEXhD5btQqMC39LtYeKDx62l/8HKKMFypHC\n/Yk30gPlF425j0//jRlrQr58YAq1axxpb7imG/mqdWbpz/gTr7dlc5TqUH1v\n3sK8EJH71jx3RAuwi+BF5H4xJ2wn5BeAd7B84+ns69FXtFObuEzwCZTCcmA/\nX7XQRz6EsD3L5IrA0jM0zPPpEY4pQXT65veOGttUKYj/qsHJEje29g9p8eya\nG5s85dxWJkHtdBYDJQlqHLD9FdKwCQgWy2+j3QMAL168gYzcJ2RxrZzYLvZe\nMVDPTYXbsnHv4aAggzjxGhpLtS9HuBEcUAlPV1XdYV8I09yOO4OiOBKKn7NO\nGYrYRgGMQzs5Ou4N2BJMIUdXTDdfqks2mnVqBVt0Q3ySvj0xvHDG5MPz7qFm\ntn0s8Sn6AeTh0UJL6+BugfzL7csKejVCfaY48faOa20lulkxQGN9tj7mVq88\nDGbRMO7YHlrSA7aEJlz8A+hI+Z8nuzYCi1Ssf7Iz8ss6SFIcu+fXpFPao0/5\n/kAn\r\n=zb5i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.0.0": {"name": "@testing-library/react", "version": "12.0.0", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "9aeb2264521522ab9b68f519eaf15136148f164a", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.0.0.tgz", "fileCount": 25, "integrity": "sha512-sh3jhFgEshFyJ/0IxGltRhwZv2kFKfJ3fN1vTZ6hhMXzz9ZbbcTgmDYM4e+zJv+oiVKKEWZPyqPAh4MQBI65gA==", "signatures": [{"sig": "MEUCIBiZ3dyfier8RtbPKsb35ab1yJpcrn1a+6lz2bTkGhVSAiEA2HiJDjJ/xgNhAtxXWRk/gzWO0F0vyOXY/anEMw4JXj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5367973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0tZQCRA9TVsSAnZWagAA86UQAIj71kwiOgyB3S+hz5F+\n7IzVgAECHu4UkVL5OI9y8gKQm/zH+aD4wrcsxcfKIzpbpIHnAkzw9WidGWUq\nk2IXWqPUQcxNC66om5tiLruQoxokCd1D2rdZ7E71/p6mSTh8JOIlVCqCAsTV\nf1PZTqz5NI9fun6d9CufV9dRIHAYGUvGvAQmHs8G/CARTgbOinm5vOvEdqjH\n144Q3n52d490yRUgRJ4D0hH8uhLkKm5t6sJH1sw3AAX9ERZjebwVYFaEOD/L\n0UiVVvmvSNOceqZPbxlboXoqhvf8bD95hFNCM3lOMzpWuXCzXMIQsQ+2/4EV\npeym2om0qGjgQbDiBNMsMZWaUC7p4do5mg1HWkr7RgUo4YUi94eWxtTZc5eK\nRwy9xOy7qXU0f6/Om3N985HIlsmIS438N7erNcp9/YzMlZRWTgdnSQdgd5bt\nyqpV+lZgg09jp244EPr2SEARgi1EeJugSd2iT0kRxpooTElc9nfM6oiYtfz+\nKESzjCo49uK3whOx8HwB54XT68JbAsMI8mUYuZ3ijqr1u/dXh1Zk1RMmgkt8\ns9qXtcGkOOHbU17dLuYJ4dxMiiSWrD9wWbTmX1jO2hn+TpzXJcBYCjwvYSie\nniU1oceUXW+gfBWKXqdr/gqxFClBa7w0mqccTR3krtfKyxFfRdzqSZpANckd\neQnG\r\n=RYDe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.1.0": {"name": "@testing-library/react", "version": "12.1.0", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "3e9a4002b0b8f986a738a2f88fc458b5af319f35", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.1.0.tgz", "fileCount": 25, "integrity": "sha512-Ge3Ht3qXE82Yv9lyPpQ7ZWgzo/HgOcHu569Y4ZGWcZME38iOFiOg87qnu6hTEa8jTJVL7zYovnvD3GE2nsNIoQ==", "signatures": [{"sig": "MEYCIQD0F+lb+HR4OAfe7HNxp6MMAIzKj7ghp/CKfy5cmewFEAIhANvBaPnQ7beAI+NmeqJh2zdC2aKNb+fwTfcP0uzdVfCU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5434919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPH3VCRA9TVsSAnZWagAANjgP/AsQE/nvwKAl7kFOTRAi\ntvslBuUgQaquSP+iGPVMIlqDvwnjLoGdx3XO18TuD108JpOXMFQI49rK8Ncu\nJnPfkBXFvwvwYsbdqHBhzpHZjD+n5+ShuDsMuPD6zT2AT04L5522piwMxzxN\n2P22ULvsdQGPb4tC97zIOFUNe8KdshpQvLKM7ltLbYRAMsEIsDAg77ovssPA\nfgWNs6HR9Aq9tnx+aFEqtvaCIIwsMv96cqJi7rMpbQI1YbWiVODcQpbeuGZh\nXIplwaZDcuY7ruOxNiqvPaKDQTK6cBeBz1FTwm5d2ETCcYopzWxy6mHEBnRS\nKq3inGnDXbGaUkUwTvpzWfCHmgj2frH8dzKJ0FgiLRAnaEJ4eTm4HBnT53mK\nq9A89HHlbOX9rH9FegYEs7JNHTV1pROYJ8wcjZir+Jhgycqx6Ky+xosFDE2w\npobyS2TvA7GUSdpSCNQzChX+FVFO2k3YntBjbdc8rUQabPSXdmG7kl+Rt+k9\nbLguikI8fxpn77T3AU1Fpu4mKUA6JUnQ/Ls73Hgo/N1CQfJAqUsFzBmRN3Ie\nzWiPvy63w0ju0A3FIgQbpSUaNoXIGqP0HYtwY8PlIDUN7TZesO3JOSx5VKfj\nvdM3S3f9mhgqKe9WM3hC3EgSOStoqiN/D4fgTr7o6ZokoMKNUDNhgDx6RX0Q\np0Kb\r\n=VZpT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.0.0-alpha.1": {"name": "@testing-library/react", "version": "13.0.0-alpha.1", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "e6e1515ee55dd2b8efd35a532368ec40a7af45df", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0-alpha.1.tgz", "fileCount": 25, "integrity": "sha512-v9iYmsQAp2dXY4OU23Xmk9qTvUy9rWYpEgQASDQ5Tp6BHc057SVEz/FSuesNbQSsRx8Z74bD11V/jgps24nLRQ==", "signatures": [{"sig": "MEUCICe8ScCNzWyf0IK40dSmp0E8CZAPkpzHkDXAzZ68MlH/AiEAhyg5kKe4Ksk4REIXTHNUGa2gKo4lu9cUoLNPeSYcXiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5476613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPxGgCRA9TVsSAnZWagAAQI0P/1NOp5j0ZSojwyYu6PsC\nMAfnWS/55Mlv8vaHKABg6Dw5+Sow8gPS5hchtN1DBfFFPCiIFk5+eDHjy3XQ\n6VpD35dJRO8joXCCzVVBL+DVKq14tTBjDzUNvqozUojKksMD82TYkjlnoEnM\n8G2iHLOCb+iv/w5ynhqikZ+UwtY+W7dQ26YXsjnvydXByS0ZKrfig6DG9+Ig\nlaflk6vfJMApWeYSHBxZ0OWoRFwXiToNF2F39uJVWj+u6YMlThEY1CXJmKcx\nT6+9sDqKmNSp4lTjT93sdaVVbCDd83ad5Jn3qhxSSyk95yCE3MZfbEU+H6CB\nOQ0hrinfAeU1XTUf9wub9+2OA89wJZEMYXWG/sjtGq/F0xSYZlvjEgqlVjwS\ngQ3wjT4n8uewh9ttKcFqYariMJwVmDj39IS23Kj7dVGviHPT2wMe1Asf9TrH\nvlA3qQ+6FD7hNfmM6eeDtzIZZ0CQQmC4NHwtxqkIXOWNChYPD9mxpiawqBwB\n9gqQ1vsFH7GhrB/JbD0KbOGzsc6hVGqmJHtl6mxKQvZwWmvamJrRb7Ne70jq\nMF7n2MdCxaQYUBr2EgJvGgTCN7FMYpHLKy8Slspa+X6wMecU1uQIyOCbyIXs\nAhOsT4Su2I1W9qBnwAxDRZufaflpPb33pNfsOgnpadDlECZdvJl1PBbQO6JW\nizVj\r\n=mN0z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.1.1": {"name": "@testing-library/react", "version": "12.1.1", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "e693943aa48d0190099acdc3928a751d73bcf7d5", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.1.1.tgz", "fileCount": 25, "integrity": "sha512-JDyWbvMuedEpP6SPL4Cvbhk59TVxQ3pwuR6ZfJHdRsHuxDd/ziSMA3nVM3fViaSbsQhuQFE/mvFrPrvQbL5kRQ==", "signatures": [{"sig": "MEYCIQCUTtLljnl+a1J2oPovGH+d9/hAEzSnytHsaYiow5o37wIhAKSl/DHPQvmuSkm/YW/aos+hafasgqdBeCKRfFy8jcqW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5480452}, "engines": {"node": ">=12"}}, "12.1.2": {"name": "@testing-library/react", "version": "12.1.2", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "f1bc9a45943461fa2a598bb4597df1ae044cfc76", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.1.2.tgz", "fileCount": 25, "integrity": "sha512-ihQiEOklNyHIpo2Y8FREkyD1QAea054U0MVbwH1m8N9TxeFz+KoJ9LkqoKqJlzx2JDm56DVwaJ1r36JYxZM05g==", "signatures": [{"sig": "MEQCIFTUvfU4SouGuYC0cqbvm+x1BhKw27XnWnABYsNWSm7KAiAjO4VmZB2OyHngIpTDW2ZZRLfx5S9OdqtqDZyNoQs5uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5480460}, "engines": {"node": ">=12"}}, "13.0.0-alpha.3": {"name": "@testing-library/react", "version": "13.0.0-alpha.3", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "46bb30793489e5bf22e90fc128407e305bb66c82", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0-alpha.3.tgz", "fileCount": 25, "integrity": "sha512-QSZ5K7D4Q4Xa0n2Xtf/mXfeVsjANkOPspereGIzh26Ro4bsUGPFtHdYjzbQhD+w3K7zEoAnh6gIIPhyB04BPog==", "signatures": [{"sig": "MEUCIFNjlB/eNYnhjbVJvdQCrTNu8rPaCUmg2oRSp90AlQ+RAiEAjkzgIp/u5l33fIhkY8AFFytcHT6ZRHD2U0IrMMAI8sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4353231}, "engines": {"node": ">=12"}}, "13.0.0-alpha.4": {"name": "@testing-library/react", "version": "13.0.0-alpha.4", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "00cf104f3a9b90d07691497abc50ade74e534d98", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0-alpha.4.tgz", "fileCount": 25, "integrity": "sha512-SY+sz4juZDcMlAaEI8kYpdi4qv+fGjvqLHETVAcL13Q+N9PFtsdmNJnVu3Ez4V1dJuR59y4vZlSJa/U69YyYBg==", "signatures": [{"sig": "MEUCIQDA4ABh0N2njrFmm9DL5uI2V3qh+8ALTVhNyemUTjN0ywIgcggEqz4bKHeu/1TKcU5ZTBXEJrno0n/LQ6ymJTt6+mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4357187}, "engines": {"node": ">=12"}}, "13.0.0-alpha.5": {"name": "@testing-library/react", "version": "13.0.0-alpha.5", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "29bfc36b550e2a1025cbebf7254d5a0a46cb58c5", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0-alpha.5.tgz", "fileCount": 25, "integrity": "sha512-QrxKC/7pTE0ze3wLZNaenGJqsLcbAJL71XqU/ryJTL2pqZBjiJHuxZavl2ZQAxnBQkDQF9oh9my3bKPstWfnhA==", "signatures": [{"sig": "MEQCIGZZKi2CwgGsP4LSzbpj+OUv+7yza7248z3drP293lidAiAUrc/qNnRz3Ja/km4T8dR1AFmouMbYzxMgRnfQDqYTpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4361198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnQa/CRA9TVsSAnZWagAAM/4P/igYei+rHxEnZ4Q91aeU\noW2CBeiiR7CGFm8PzRgIvulLDPRY4WQeqkNd0VLikurf6p4IS0KHeAaFbMrh\nMc9AdrqAA1YUxG7Lssr/ePGqZOwwKVIi4ht1NG8RV6JwmLQyLgbRgm1Tx850\nuoM/jUHI5BwKjmwoynjDEvQg/H2yvZvhc079xcFlU5R2oPXeq0clP+rRFOi3\niai2t0uqiPYqF4DQEZStSFMR1y8b00oE6TP7Xn2OBdNCF22LAnOr40DiKPmY\nz57Q6h+GMSeX5UA3zqTdRAWSnxC7DFl9SbBO/lGfiLFUugZDM2Rm99QyICIz\nqaa8/QnRaqVtfVY6zYbbV4F9mSL5i6ESwEo/flnywfRkWy86mkkl7hixlXGp\nvQFv2bwhjfGCmdYyDw0yviBp4044QDrjrC5jdZz0Do7vrtCdQtH9HY77tVY9\nv0S5DvengE6BIe5J2Y4FJmHdcTTnGWIZzXQqeyXvl+X6UMT8VoLL99t531Of\nMi+DiuKdySWZDtyCFZd8+JVSzK9FNvFOS57t0TRa1YrYkVJ/qkc65mvPoeou\nj0gZiSL4EDwpqgYksuweLOUYOCbOOktD7Hv46Hi5pLCNuU+dxxTPDM9dB/5s\ntS0FkOAviITITNcPI17sBHbhu5CEYDmJG49Ort1NVmy5emGhdR2gE7EDo07u\n7tb6\r\n=vqjJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.1.3": {"name": "@testing-library/react", "version": "12.1.3", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "*", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "ef26c5f122661ea9b6f672b23dc6b328cadbbf26", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.1.3.tgz", "fileCount": 25, "integrity": "sha512-oCULRXWRrBtC9m6G/WohPo1GLcLesH7T4fuKzRAKn1CWVu9BzXtqLXDDTA6KhFNNtRwLtfSMr20HFl+Qrdrvmg==", "signatures": [{"sig": "MEUCIQC/7HMomhOW1hYJMf9f6BwbFqi5nobeUbMagItfyJp4MAIgayQVEgQlrviIObxmQ7SIvA/siDSJzF0vlxiJ5Sq7Aoc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4272357, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDC99CRA9TVsSAnZWagAA5zAP/16cD9a05AiKU13XqJxA\nIYD8lYch/AwuZNChDJphwgcJEzWow/eTLg4Tiq0zaf2y8f6lQiCu7lAtY+jg\nD6UHJ2dvTFKLJqforZsYFclmC8sbmEXUz72CoX2AmIJrqREuMGyFUY3bCOsS\nc6hD6K21wDH5NL6gCsV+OEv8Aht1edE9mZp6Ocl9zee8eifKFCYiQEM1eUag\neZqsfsaCFF2sYMVpveDK/XR3RfihtG6owShISUBodRyQS6Zza/L8HD4Lq6f9\nJpe88IZgcHTGcteDpzIQM4jt3Mns7zlroMQ4cuYbQKSRi00DHeFob90uzPY/\njAzBUK49pbK4ngSYbbKPn9d4U269uKF8Hxn3QywG2AbZd6RK5U0vj983FEQa\nlUK4a5gpiUFV+JFfNHkyHmFMhSMzckfxK4KTmw28FBxmv6QuAXuEPXG5cbWb\nQuIEm/HW4rJ5kFSpQH6SwyPrbjq1VH9mHjnOmqpNBBwjWksPGPPtGBFsijis\nIEMCzuG74a1l1jYz9A7fuW7FV5j5MSg8x4hIRNWVm1lGjcXOmXT6UKu1CpST\n6nYIC3FpDbVpkel3jNtGUmpbUs/fbBYAJE/l5uQAYBfQgayligkj+XyN4pi8\nYoyB4LZib9bKCP2qt2leMdwfpgCpwU7rxYfSGOZTYovgiDWU93N81YsMZ11Z\nSGwQ\r\n=FgW4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.0.0-alpha.6": {"name": "@testing-library/react", "version": "13.0.0-alpha.6", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "18.0.0-rc.1", "rimraf": "^3.0.2", "react-dom": "18.0.0-rc.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@types/react-dom": "^17.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "18.0.0-rc.1", "react-dom": "18.0.0-rc.1"}, "dist": {"shasum": "6bb543f524a8408bebe81f6c866a8f95951f6d43", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0-alpha.6.tgz", "fileCount": 25, "integrity": "sha512-AVJTnwLlnjvXDNe91P6Nt9pN2fMS4csAzTmIbOewja+LVKzhlr53EONhv3ck0J3GzSZ5MIN5qL3BfISX/Wf1Jg==", "signatures": [{"sig": "MEUCIQDvv32VuLsjb6h+j1/xGHZtZ5PF4+bP7aHAKCw+hb4UmQIgYT8GA8s71DpYsPHOe3VT+6KPUDV0/Np994hcvq4k2ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4460701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIoBMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC5A//XBXeP1b0XRam+ruw3CkzUeCKiE5AsCrJ8VBc/OceyGTG8zHT\r\npZNQe/bCk2abTjrRh9mc7/V2QPPF8OtibITLHKKhdImN0EUxXmQOyXh8572f\r\nARy1xZ8CGS7YisPyK32Yjn8q3xBxKuoLdQVnl3rhVnswOLjrQp5qzT1XIDby\r\nAjVmQbTyLpzPQ2toGHHsbHY7EaExjAPeyPuAiww+E7ulveJja8Frj4WUj3D+\r\nBJZ2/3Y75WeO73JkGJn2yEF9ACKy+fgDI6/bwSUsJhfXKWjQ/HDLnSQyGQh/\r\nwCFlKpUWRDHtTu3mz79ZXTlzirUO6mL/ykVSU+6Dje4TVK5DgoH2Ozk4LHuh\r\nm4/ZknnpM2F3EgIw3ctDO/PplB9XKyCa3ngO9HCPTAKOKkXlBh1WpAOGSZ/G\r\njMlYX/wl6pCf3TyNsdCyQC3OuxTFLlsDow0oGv40FvO2coMq+qIgpQq7EiC+\r\nNCA+oiC6A369KtZrAOUDrZrNVN5PjdLp3lQTe+ZiPm8ycKPzX+6Tinoa+rLq\r\ns0/pQWCCPS4grCf4su6OtFC5ucoO8dfzVXD6NRVraitA0YgZQL3xiZ35WBC9\r\nkiGTWXIDqHnJ9I20RiFhzaFnegefEFVFI/CWi41uwt09lQU74bOpxFfSju9Y\r\ntt8Bl7fpwb6voNFzEIk+MFx+4cfhEEhFAJU=\r\n=5P07\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.1.4": {"name": "@testing-library/react", "version": "12.1.4", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "*", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dist": {"shasum": "09674b117e550af713db3f4ec4c0942aa8bbf2c0", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.1.4.tgz", "fileCount": 25, "integrity": "sha512-jiPKOm7vyUw311Hn/HlNQ9P8/lHNtArAx0PisXyFixDDvfl8DbD6EUdbshK5eqauvBSvzZd19itqQ9j3nferJA==", "signatures": [{"sig": "MEUCIEjjmhvYs13DfLSEiw796glSYLgvB6r+gksT2AIb11C1AiEA/K/8FS4ZLBkiwIGtTTEu2v9zln1DJNbwFrirJx4MQYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4442303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKLukACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotBA/+MLBzQAbEaqVwRDXOiTfO5dpiV/cWOB2G7dtz9GtRxEHgEEfv\r\n1CDiXWFqh/AhMhzclhJCWhhmrPyE+a7MTv1ICBHQDd60yinvai4guHBpSNJU\r\nFd+844k0KXSHeFYTKH5aJQVoPr0gNkqn02J2ImA1Y+zjoAyltXghtptzgHuh\r\n0XpbhXOwME9ut3A7vOkoEZpoXiGBVKQzl4QPKnOgvnZWfHcXTeRQ09B2Gk/x\r\n3xNI77vv+yqAP4HXZNGnFRIILuZliObFxuU/4n+cxwQTn20IhLPu5B3oXIHe\r\nPupC61TtQhRPa7thulOtK9EGD24V7SDBGDwHZaaR2w+ezsvtu1FDVO3bvDSO\r\nsJoJKOwr62hIUAjD2MEAaEoZFjZH1glOHOc3GEVySj9rZb7AT5rcZC0zGnMz\r\nSxo3w++gvs9Lx/Hs8HY5DY6gX+kC2jCsS2IvWHeTQ4IR0gg7kzPrRGQ7vLhj\r\ngLpQnFRg4FuyPi0V8JwwiOAXQRfraUjB0E2nLXob7oPQcuYC5O/Q4sdbgw+0\r\nzSaLxHau+gJQ49ox+ZS0sQhynDI6p7l5y4wNwwzdS8ef0I6AbAuBhuWUJTxZ\r\nG39A6UYSiHvTvoqDn3z9KvGmG642PXuWuV0A5vZ0sNfW7wk1GK0FCZVDGUcj\r\n5szYEqQ3iN/Pd0ATJGLs/0iTLkQTwkthVyo=\r\n=zqvE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.0.0-alpha.7": {"name": "@testing-library/react", "version": "13.0.0-alpha.7", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "*", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "0537af3a8d45edd925fd0275aee7d409285ef1fe", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0-alpha.7.tgz", "fileCount": 25, "integrity": "sha512-8Y1PvFczthSeow1WZ0Cq6WbOzpVGLGyEQTr+lL6BwXqAf2IykVY8ZN8V21Ch1cTlbZuZJI5UV3gOyrOHrw9YZQ==", "signatures": [{"sig": "MEUCIBO84+k7BPQUUYacT0QqxrGzYtT+5i0Cvdjp/XulvKnkAiEAyU62fW/p6AYXt58YDK1Au8O8Q/FNlPCwMdTVSa018B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4470741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRHLqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJZQ//Ygv8TCgdwQycWE49PZ05jHSrLpLSBuJ/DB9mZs1cziD7o85W\r\nCr6RyqXv5C6XF0sRvdTsbJ/g3Hf2QyUxXtHwSYWY23BwVV0tlRYaT7P9yJo+\r\nPTK0KA8nV5+GOXRlSYSbIDvGvJTkrkY6G3+kF1n08V1NqjTIGmGd/yyVsgaL\r\nBJzjwdzY+nr5pvukfqA31a+VPWo6pygSo+w3F4stVez1zeLyiEMbwe+OUkLC\r\npEdADXZeIRiLX26pBKyhrVCPQSQu7nO+1XpBEZAre+AITigenzhIJlba/LUS\r\nKCB27k3UW8NZgmPJCHn1Rr+Kbtq4fRnwuxjBRgR5/S7iiZE7N0espBlRpC8b\r\n5l4wTXeqwP8DsNpAGz6gQCTTS/GOTejzHaEG/KmdiG/Vjys1oYBqOkUw7FRb\r\nD3zWlYtdI3xdynH7ClLHNnZ2atrT2eW57IOvj0Xi+m0sN+gES8pbEOt88ADZ\r\nxJzBj7Q66amRSnRdSt1IMiBBTeIek9ws+ZIDJuZlS2q1e8Kqj5nsTFjVpywd\r\nh2B1KVD2rRuMRlu5kHrRmmoSbkmwxvo6jkx3GCNnYP9H3fvXBY1B4Fsm9RBt\r\nb/aXy8SsC9UZyTqDUocLtEWXU6FnlSQtQ0an7gM1m8g7bQ7SL33sBV48FfUm\r\n8qUOwKcU5xbGhZ7z9jORcod9klXTv4mD1bY=\r\n=xo9O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.0.0": {"name": "@testing-library/react", "version": "13.0.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "*", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "8cdaf4667c6c2b082eb0513731551e9db784e8bc", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.0.tgz", "fileCount": 25, "integrity": "sha512-p0lYA1M7uoEmk2LnCbZLGmHJHyH59sAaZVXChTXlyhV/PRW9LoIh4mdf7tiXsO8BoNG+vN8UnFJff1hbZeXv+w==", "signatures": [{"sig": "MEUCIHkv2OdebU8ACEtkwqvjdqRE66LbEoK3M3XbnJSWzSijAiEAxUVJ54JR+nb6G73cRuvkWVOY3hI7sIrsduRH0VVQ/xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4470733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRTU1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxlA//TOueykYGSQ5FUylqKDLKXiz9H/1x1H45WA55GYY6xa4Z/jCz\r\ngyrrqRA23BVL8kVlfKAzAYztX8rXBuhm4CARWDmJHw3f55CCzNzUzCY2QEt9\r\nkj15env+VTfwGHaO7RW53nozHQzANE6N0WSEoa+zpulal8Iskyby1evlwgNO\r\nMuGhWfoaHmZ/lBR/5uULhJlZ7kOHQjdCcajn558v3LEXNoPumBh0Hc9tu1Xa\r\n9VCzbjnhYtS0ZJ305di9QOK+9m9kO4GAAMaAZFnZGlrypMSkc++eoUi7pVc7\r\nlLi41kXOYTu78aaHFEL4UbeGtIhxGSFpln9tX56JafFcubn3O+pq6FiOfnwq\r\nD4IWevUlMiFbR5jmwEect9Gknef7BBzOPeUJXzAkC017+nO+gma9fD/1c3NM\r\n+z9LnqrDCm4f7Ks2eaVotnaw56YqijrxuLydMoMXUNwunGHYijoYLoDRewoK\r\nqgSLwRY/fzoGeXBqGt6uZOE0SK4Z36LE7RReH2zGFB7UAG5DtToBU2FsEM1b\r\nZbKX6+Wo9hMi7FvHiUgmRn2up7C1M7NP5Yq9DQXhN0dDqhruKYrr6woN1J//\r\nLiVnukB+h+BfQMk8wWAoXGLJWDkE3j9WqRMzBdEwQGllb81J8JwCZr9DtP/S\r\nNnHAqRxVhvUtbhmJgOdjLvy78NDljyb3c1M=\r\n=kmCt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.0.1": {"name": "@testing-library/react", "version": "13.0.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "00d223e182923d341a9610590561fb9dd1324110", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.0.1.tgz", "fileCount": 25, "integrity": "sha512-zeHx3PohYYp+4bTJwrixQY8zSBZjWUGwYc7OhD1EpWTHS92RleApLoP72NdwaWxOrM1P1Uezt3XvGf6t2XSWPQ==", "signatures": [{"sig": "MEUCIQDUHPMrQSbt5sKZ3GEUh8Ly9EAnNcCRbfNkzFVRtHfC4QIgLVg1VbSn/XrZzrbOt1peGLqB6L8b8VlghmSOHL2VyeI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4500385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVG8PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfrQ/8DyWLbOnCI0RqN2ErvwrfHQRf1sKUYFv8j7DcDlpyMYCAYJ5C\r\nqe4u5gClgMV14u0kfgatim++1bDal2kAFaXF5T1FOT6ua1zZhHuOcwTTkUMz\r\nkN6XtZx/IMyzqHlKoa86F0qCkXejNxCl+HtH6330er0VsGii3gbVYFxyfNtT\r\ng1H1KB6OoBsol+i3bJPij0SgvTlOuIEtDKqUpiQCAOhgkAOWpmc43L/7CFgB\r\n6jJ4TwcX0TlIUqgWg+L8AIlo3PSODp1vDAMwH3JJYXDizdu3k/2MXytf2qvC\r\n9Jpi6lVIyC5q7Y8XKTpVlYxTG6KDJ39D8v7QMvQyL3xkpugeJyepPmf8PXTE\r\nEAXd+IYdVWeCVR5a6IWNav8vU/GJ4fM8Rwve1OXP8uwFkRB56i4vZTtg/K0Q\r\nDRS2En+KwTu7hoJe7Y+oTrdKuvzENZAUBbBz04k4dkd8fhUWC5U/RFUPj/am\r\nTnQWycENS3tUoseHFUhQIVCWmRp/NrMLnJCMOk3Md2Zm0008a744b8DWbzyg\r\nq3XGWiHED5cB6OY0JIF5qoELFl3pcxknXp9sYNod3fJYnpOeIq0vu2ChlMCw\r\nMDMzbdhfSAX1r4qkkPxApbiPTDggQv74/oEnOuiJJdMGar9vsw74HeaLPd+/\r\nyhUQHFBqBfw1wui8+J9eXJl+UKpq5sKD+3E=\r\n=LtoY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "12.1.5": {"name": "@testing-library/react", "version": "12.1.5", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "<18.0.0", "@testing-library/dom": "^8.0.0"}, "devDependencies": {"react": "^17.0.1", "rimraf": "^3.0.2", "react-dom": "^17.0.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "<18.0.0", "react-dom": "<18.0.0"}, "dist": {"shasum": "bb248f72f02a5ac9d949dea07279095fa577963b", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-12.1.5.tgz", "fileCount": 25, "integrity": "sha512-OfTXCJUFgjd/digLUuPxa0+/3ZxsQmE7ub9kcbW/wi96Bh3o/p5vrETcBGfP17NWPGqeYYl5LTRpwyGoMC4ysg==", "signatures": [{"sig": "MEQCIAsj3Cmm+MC6PkTmgBQKhiLBY0uECEi3AzpLUlGeMCzZAiAkFUTHN6+liJ5wj/qRhNhWcA4M47rZftA0c4/lXG5XWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4480597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVIwrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxuBAAnBnhMrUCpcwFlVMOXbPz4mDJMeyBOJt7pNEX81cfiJGItTxh\r\nwA4mdWQcwet+/wdZgIW8ppfUsj5HQqOCbGtcKuCzCK+d52AmoTPct2A8WdCF\r\nI/jKV//WsEcCC9dob3WvtTyc6EVEUPHIiLhxricVxNIQW2HPJ2OeQKJh6oi9\r\njtkVhKq6uvqJn1xOMCa2SeEhv9s5VzAkAuOPdb5pR7ab2GsZ0kgcx8h4P4p9\r\nOQQRV+sp6r1PVgqpEY77g9+qnF48Eq+D6J338tCej2DhNuqPH9Oq6mzx6ZYi\r\nwwbMT/p5ILzsZdEJEmsAhCKO4WzQTaK+zJPMnMxFMC/OTHEKWA+sM6M4RhOD\r\n2srw+PDpqp4AIowMFw6IQXzBh6Q9z+qy5ZMsTuo4ffTY9usv+kfXBUtahsxs\r\nPds05AcC8T1droVc9Z6oeywWJsOyHwrnERIFLuqgzM5jicEe7ObU41v5hvsz\r\nJ1zRwxllpL9uRjMI23jtqh7F+Brde/cC0IasUD7CtRVwMl++0eAS+EvOEU4q\r\nCrhZTheAOxgJynSvSJbHEI5bQrmtuJgErhmJVbFSn53OLD2KyRUU4ABWSQJQ\r\nuYgqCyGkM7x4v+pWnVcoCpS4McR5JzFIMazssbPa4ZyhLebZNoSX2EA2BVIv\r\nxu2lR08tZMDmfRf92+PylSKI3QAdkh0c0bw=\r\n=GCXj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.1.0": {"name": "@testing-library/react", "version": "13.1.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "31fce8d37f314daf4c8096fdcc280eeeb4552923", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.1.0.tgz", "fileCount": 25, "integrity": "sha512-neStnDZdhkvZNNmPhhhi8+BXg3YCvjNmd8yGdr44VLVcFUDPForwokJWpDRCh3DvuX/M37Pt94fLwkM7aNut/A==", "signatures": [{"sig": "MEUCIHQPcGyRymUbY+hLcFYtZEZ/0zHbi+juTKJ4gtdZlyT6AiEAtoCWcgMLdrfT6XdqI8cZRDWd2zQPPPbachRnLpm/EsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWcCOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqExw//dtLII5IuHPVgu7WPNZ1DpmF7342F/ZsOEtCG9D30Ls7fiFdY\r\n1dmKzzUB16+niaYyjlAiYgpAr+iyzLrCV8w8wB/e6H30683qdP04G1t0kOP4\r\n75a2K9njxJY1FtYLozYTeXvxX6P4zJKK6Map/RHF9zsKF5aVKRZiH7M9Yeex\r\nJrkEdSFchiFW/D7N0uoKsOC+NQ74MPvizoEDG8vp1ikZ2bOV0laxRhcwQFEB\r\naO+PPvRJz8fradgyxYWEK2NA1BK2jgW8yicEfqA9kuQmHMZXcJoGVZWMgI7r\r\nbVwhFyYC5ToBU0IKcYOZcYgMc82Z3qr/BNVZS/Msw0g8wPj8C78pO3ijGXG/\r\n2X3ivbl+nYYwUGdaqfvHgUtYFZoR3n4qH4x51a0Bx2oA03deI6tLY/9leuk6\r\n+biSsv1YPhCylLgKh683/jA6pzqxaLoE0rW2/6YPheT6tRGeFjyFqsyPEjJm\r\nBKjWxfbLtBz3A8OtaGduUKmzc8WL+hFIn8gn4e24m4VXQWXCr1QixIoDjtKM\r\ndh7uRfwYeN5nnt023HmxWl1DO6y0Rj6xtMMVKtlJ7FHej+0579yUzeb5ZiO7\r\nDX+QDvKDMuImqt9sJI0gy/iRZj991Ro0qGbxPiFjfViX35UsmF4dG9y+xSks\r\nRNgGBFUxkRHFmcDCvc2NWr9qIGihTVq94nM=\r\n=dLB/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.1.1": {"name": "@testing-library/react", "version": "13.1.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "6c1635e25acca8ca5be8ee3b19ad1391681c5846", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.1.1.tgz", "fileCount": 25, "integrity": "sha512-8mirlAa0OKaUvnqnZF6MdAh2tReYA2KtWVw1PKvaF5EcCZqgK5pl8iF+3uW90JdG5Ua2c2c2E2wtLdaug3dsVg==", "signatures": [{"sig": "MEYCIQCgIgMokJyX+a3T1/6jf/avn1KRpu1nvulrEWytU52jbQIhAIMj56hCkimKQIYMgc89DxouUQjQcOt0x3LfeF3bO8GZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWe2FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCEA/+KRYG5D0P19POsWMZ4/NPpUrvGRaYEDZPuauF3r4j3a4cYpRu\r\n9zBxthjjS8R/ITwXovujdbCLBzWgjWxAvrBbW2NQmdyvpoGRGzegYeJ+R2Ou\r\nog7V5NEZOlN0FAYzKmmR5HShp2gM+ktN8nrAE/YFlFmq19fD0yZegcPd0q5H\r\nGwXvqI94ZofqXj/fArcz8I4bzs7cAApZeAXQuz1CHl6Yr7FE0jMnIxx2pYnX\r\nBBHreoJrXu2gFEDF9dt2PHypvBA8OkJlWvXHJsTNjak8f3WYA8XjCvoXVOl3\r\nOECMFGjZCPz+gUe6NjIPb9EPGCBs4duvA6Z6e0FVk289aNSzZEkU9Tn8pACK\r\nZvokryYMii0cSFEL6qJ8z8ZjJg/pQnfouVmzROmQLx29M90qWArTJKdFBVVT\r\nrqV2rLSLwp/KSBmXKDv6ludqdwfBnJ3KHye5Ea7/D6hCnz7bFkFFRHz4jqXT\r\n482eyd8wVZJuar+p+x+KbQJOxpPjezCVgaduKrSsChVXFPTUWfQsCTJwU0Yj\r\nehqh8bDVNCAfdX8aKzrqGQp+Sv01B2xwZkYgJr5VOjVaAd6FKgIsIjoe6hZM\r\n3Lh5A1tNkcNenLsm0FHmDDdL3lZ8aiTpQ8uVXTuJ7UnQ3bHLdSc1LaaYldnp\r\nALwnQjxF1WnpNftriHXM8cEgqzYUDUs5528=\r\n=6LYP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.2.0": {"name": "@testing-library/react", "version": "13.2.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "2db00bc94d71c4e90e5c25582e90a650ae2925bf", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.2.0.tgz", "fileCount": 25, "integrity": "sha512-Bprbz/SZVONCJy5f7hcihNCv313IJXdYiv0nSJklIs1SQCIHHNlnGNkosSXnGZTmesyGIcBGNppYhXcc11pb7g==", "signatures": [{"sig": "MEUCIAMM7kuF73a8QAYnHbzkrhv6SpznrYQF5P6to2n0PmewAiEAmc/+m4fRkeQCQA0F+YzXprUNtWzIXMLIGSjIbirS69A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4491718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicXcQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEmA/+OSGTb92aRmsOJZ/6SuFkNTebsDKj6heuWosOV58c/f3PzSE9\r\nuWlGf0C9Qccn6/cMiAzTeOedWtYmHXiLWXWfOeOC96hFMXkGCJ1MDsH1Nb3R\r\nxeRHpr4oKboLfSzNc5D8Lw09s1U/pEiENo6rFeLCqfl86UJPZ+/VcJM+v9F8\r\nMnsILyfRtMtSCfq9Lil4fcbDDMOyKgtZyylJkIOeDV5XLoCtxyRo5jjIJ18Z\r\n4A32V+FNsxnjDCO75Ioac1nhCOh8djQ9/i6noovDDTULGzHOWq1U5XH8436I\r\nW9/xBJ7NfNjYHr9IPCVdRTJRjGLSy3Q5wdtduO8fXA/44TjgShZWcSqgjEcp\r\nF3Gkh6mxB7e+XBMIKQZ5mIqs3aySYxmwgEc5ZMGC8Jx9/QqAXogNE4QivzV2\r\nu2eYfy8ABh5t+xSlaXPTnnjBDmX0BY8r7HIaPr0zWGpSPvUlgWHTice3Tx5W\r\nTu+1LAQl+Ni2Gw83a+S9sLqMDz44/m+ZxC6glcbj1SKJVxK/b3TgX8M31v9j\r\nmmBD4TEmKBochNiXBTJeteELVwrZbtK7zkt37ErPIsSTqYVwGV2E+fnpY5Cb\r\nr9jr9ZlI8R+SU0xvGUulKxMAEZIBvB3e2oVT/Wq5K0v5C+RMA2j6AyRMEGZ9\r\n3f/prVzEVj8i2y8LTmFUZ1U/DmbnLwtJgi0=\r\n=5Lvh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.3.0": {"name": "@testing-library/react", "version": "13.3.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "bf298bfbc5589326bbcc8052b211f3bb097a97c5", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.3.0.tgz", "fileCount": 25, "integrity": "sha512-DB79aA426+deFgGSjnf5grczDPiL4taK3hFaa+M5q7q20Kcve9eQottOG5kZ74KEr55v0tU2CQormSSDK87zYQ==", "signatures": [{"sig": "MEYCIQDe+711hCguu/GI7IGBweXVau2+Qb3/KsrVT49eMBEGdwIhAKmHjQiWIViskx3g++v/k0gb1+fhlwam+w7lWZudh8uf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4430624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikdvnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNhQ//dGLma2Q0xKsjLETTDv+QFcgZyQeyjskbqWfC2x3J2aMM7Etp\r\nY5//pcbNq7LTr1DH1GH+02FmCpSYgPgQ+IJ4weOPXfccVUyy71u9z0cyVxvz\r\n9ooxHRBRF1iebB4ReXsu1NYCfsmx37Qc3q7zNi0Di6AwkVcrhz2IZDrT41Wq\r\nzlHvcTpYAbaxdB10folnO5av7VC8kOL5kik1vThnE/n+OTUv8eZTBgS7wgXC\r\nL2yYiFOmcPEiVrpkgC1mVjeJf0EbIAD1ALhu95iNZHIiB2k8AASENul7Q9YS\r\nJwnmr/kzaJYBrxtYR6ixLd54eKDgYRExXktY/e9lUdW+6IKF/lMCA/hgnoZ/\r\nuSPU0UhPMmshB4d4WH7gKJQtxfTWK5yAiHescQw48EUqmlx7m4swVdOlZgdV\r\n5u2ZWfqB3/ny1mZz0xM9HiWowqHbF0W9KHvOo6r49z6REpKCTQaM+ps+tdT2\r\nfrnxiiYmIdQKUpZWw89foBUBFVaRnJ0GkJhExqKeUuB5yCU/tsArnZxtlvF7\r\n0ep/C3DsvZih8Dsq7cUD3L2FsWcltxf5x+TJxYq55Z4wl6zBHncudV3/JKH5\r\nzIpEqeqc+N6sPRVf097Gv5W8GEqNI5pgSMZSmtmi3wENugHUZfo5VwQ1QEms\r\n4O9QrXd0Cq9ZByxD9oZhEfEjwEJusB9MutI=\r\n=CTaV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.4.0": {"name": "@testing-library/react", "version": "13.4.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"react": "^18.0.0", "rimraf": "^3.0.2", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "6a31e3bf5951615593ad984e96b9e5e2d9380966", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.4.0.tgz", "fileCount": 25, "integrity": "sha512-sXOGON+WNTh3MLE9rve97ftaZukN3oNf2KjDy7YTx6hcTO2uuLHuCGynMDhFwGw/jYf4OJ2Qk0i4i79qMNNkyw==", "signatures": [{"sig": "MEUCIBYQh45APiCLxCi8t2SUo7DsdCfka6TFIx8LeEU9KMJWAiEAmZeF/qELC3SdY42HRSB+ZyKs906yvAZP0GAwJBhc3Yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4016439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFNfYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIYxAAjEqgliUNJqoT8SaiFnKFZMqdlN6uhS1NQzYTWV1/h5AsOMgA\r\nyM5F6+syxQBita/nsRZCJdNXDQYU6L9u/S9yFa/gJasuiBElOp+01Kv2iCDN\r\nlcXpZcm6qRk/eSTM+Wr80veOmNos/oceDr+E6045O0k30CMqc8OodSRYBmv6\r\nTbleBaBC1k/CK4uuNsNuNDTDL5TkFgeBoWnum/9SRCh0yIE6wkXnv7ln4peS\r\n8TTG5kN5vlkB81hLwpPfHslQ9He4HpQzV3y8tJrip7nidr+SB2JAX7cTqDtB\r\nba+P2zA0YGm8wnxb10XH/TQC5TCmsqxs8mW1RohIeIZzr62oGhqZ7vKrKc+o\r\nFQTG0GiQKqgOQ3wsfJ8pJIfv3JL5SSJO881py+fxb39M3W/soa0PFhZYWIeI\r\n7YaZm2ZlINQ/hEMpkWAj5C93xO566GPVOMxvp3ipzQNkC+pd+hs+ZgtJdDGe\r\nTWk2SCCLVcBcivZXutGZODU+ye3mDfMxzy0Sgll0gBH0Fu3M6Ta1JH/5acpZ\r\nZOqyu8n8JI6xnxoiA1jp443y1u0q6jFLab9rDtWKSA/gE7RMd5j2tngkDv1S\r\nAxWb8zVRzXqRbqKFIwirIHIezJfvfenUhU6WtATAy8SMLig8aXps5hn6ywyb\r\nPGpCdCuLcEQeFnQKue575Tr372qM30FWmo8=\r\n=ulyl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "13.5.0-alpha.1": {"name": "@testing-library/react", "version": "13.5.0-alpha.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^27.5.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "64006f110aa685537b316c92b9155a3f84d43836", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-13.5.0-alpha.1.tgz", "fileCount": 25, "integrity": "sha512-0Qt0vfi3SdJPC4FJHY6pfatShvjNNgGF2BRWJQeqrM7dUoc1rCR2MHN1d1nfLq6ILOF1n4cdgl3cy7pY5/oXlA==", "signatures": [{"sig": "MEUCIQCKcZVhrO1MY56I+w08LSq7CUrad8LhnJqTCYrnMReFtQIgVkfwojQjKV/Pn+8gSdj4YALdIUNpJWDNwKj2OjkaqGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5283661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2J+zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKsg//bamp1uEs/f6xF0ZI9NBTGm5KYeBpLXWcMkllB7dH9tGhkzTR\r\nSnddzfoM5j+q3KkdMEgby2RZ+/+mVxQ6AzRY1Tb/Ny66VieWDuh0/YKNUxkp\r\n44L36Ce3frvktKKTgivOjBK3mmeIwKgVgaidkZcD/ewDuclI4hNGNJJvAoUL\r\nYAIadSB0pzFoFgqDLrODMCfO7GKm2i898zqpI+WDxU4DsFZxnKtOYr1wVZZk\r\ntlh/IkxR7vw0XOylPD72lg9rABsnRlp8cTQSuWLoBRyk9Al+nl2SldYzC+ft\r\nmCfPy5c9LMAR7VUXCdHx4Ew5T/T7U7skNiSLUuQR0RwcVz4roHgcv3fhGzzz\r\nlIYhXMc26NPuYiuC8dQ9AOCBEMi3tnjzKynEyu02weIRTxm+u9upA6lutqzM\r\nYS9mUPLU3TY2UdU0OkJdyYjjzVc/y/NrfLoilMUZCaAE1KgQd2sUQlo9MJjd\r\nVoSw3O164H5Mhaz5muhLjLVIhuxJlWuHEMQZAVR3HyOz86Azgq38MhZiqH+x\r\neezoLE6KN2Zr6tQMWGPeTlzvFW6ThqSDcR76O5+5M+HGbVxJa/Z/e4Vpe+oH\r\nGC6uK7P42YXsOpZduLoXlwg9Ie8kbAJ3aIrjqmEpdfn/lTyvzgDNonlRK/mG\r\n63IH9meIkJ+YdCpCrllqHaaMQ0lLkLVJJdk=\r\n=oKL4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "14.0.0-alpha.1": {"name": "@testing-library/react", "version": "14.0.0-alpha.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^8.5.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^27.5.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^11.1.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "b80df897de26cd4fc78a63c8763046c58290573c", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.0.0-alpha.1.tgz", "fileCount": 25, "integrity": "sha512-H5Hx4m5cV7nJUHk5w/vCwLAMc5upz7rgBDjTFoZI60bo/w++xaYhnqdtqy1IJ79CraOuX6CpoMtoLMGGdISdcg==", "signatures": [{"sig": "MEUCIQD0XQ8m3neD2m5aVEXanD1UMoATcHHm/l2TJsraLEkGsgIgNISLQk8XFlYZBviDKu8ZeLYtFgvDB41TTKHg4KzPcOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5283661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2KDyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmphaw/8DJ8ghTOnwY833gKGOY6d50J8RRlhF+8zxqY9nJTFU0TfVuUK\r\nWxqu0ebbpfy2IKJP9uwCIlqHJxEgUDhb9n6o597CJUvvdPBWFrnWdDnfuu6S\r\nEdo+dIBQJZWgWCDq6RnpCvzMcctO917oS3WN1An6S9e5mQD8Hin2myRT9ZE6\r\nOgUlXVBTQFenbXHhM9W2ioceO5cXqJhKLoaFDzxKhXBAjfvn9rFbxBS28Tr2\r\nNGExMZvs/qEG43d6xcrkM7xtrfzwWeEUhDE4AFoMNWZC1NDF9EvjwW5IWtOf\r\nkysJyxe3co4o6oPuA6exfLo1zdC2ODbxDi20d2rVfXgAvAF8y4nAZ6ig/EcI\r\n+mye7vVGC1OW+0w87Ny4Uhr4NFVDFGX1tMhGR9x4zmYfFBr9yr+hVVBjHZ2Q\r\ne9SzqokGXzmSfAQIlBGsyDOoG8i8KtQ5PwkW7DVVVp8ugu7zoqIRhBr8oCIj\r\n8C7IA4Is2Dy794Nv8+8KqrHzIoIKdTyxScynmGdA+VmGI/khUTeVaJ8FyG29\r\nEAzvulfCUU9cEJGv5lT6fSJIGCuah7kiI9y0u5vRoAvunNwBtu+JIVCupfYr\r\nL/qUqj6bsW/45fgmFNKEKJots/512VwnRFhpMCB0ww2dFLU8erjaUs+IIA4z\r\nIP6iYsm8rBEyX2N2Pjn9XTqwvvZaGL5g3bc=\r\n=a4f2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "14.0.0-alpha.2": {"name": "@testing-library/react", "version": "14.0.0-alpha.2", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "c29c8f5a768b7d06adeb65d3496385a46db73f8c", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.0.0-alpha.2.tgz", "fileCount": 25, "integrity": "sha512-xkzMlPAg9xdrlByMqRW/SuQwHEyENNNn7dAtViZYiEzy4TOBsGDaKHhyjHH+3n21bqXVsNuQ4Xzv5Fb6+gYRRQ==", "signatures": [{"sig": "MEYCIQDrgEqW4rjlzxUfsw167jZlgaR2Jz3G4YW0t1JgDySmnQIhAIk5iSrwZrISSq6wJEu1+XZC5QXgbH4CHkcrEKG4+59O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5272657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7rEUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGoA/7Bf8Lk6E58uqY0lBmAkE+lWgkU+YpOwwALcZuO28OfnoMbEzf\r\niMj8kQ31UrVgKnbFZwO2po6t4AwwjeR82kt5Nis91JhBLnYIjKOCJW1gTFMl\r\nvvdAxUDiQE5fswj8cZUAnGNClNMKf6LObbwwjaSQ2tc2tpeJWkJTFwSRVQA3\r\nAeRHuNgAl5wlhs7XiAtijw0GdCix4ucrVgks6axwGW6X03g9jfTqGny1q4V1\r\nyxKKKonTqzyqoFMYjg3IjJidkuFuXUcxV3P8VU4gjyM/4TpNyvBUsGq/f5jE\r\nKsANlvKocN4xqo37YJE9DH2qjEq36g+a/yVvBErqKzYOIsdlG7ta3tXO6XSO\r\ngsL3gYvsBDDqZmStowGZsNAho5DcYwXJy++QzblFY/viPHt7z/YmTLEUuGRn\r\nkPjm/v+chCTN9ZIEkP4la4vtX1AGoJ59yJb+hgXmJheiTEjy7kL3Jfim1V23\r\ndXjFxbZGbDkpi6nKQfoCrnDys4thw1mkxwvaYW1gYgADqcRIJJlM6Z4DmreE\r\nlPukPb7Nb1baHEuoeDUZNWYHb8mRg9ibki6oNhvY6OXHcKNWmoIATObNhpyu\r\nDw+7FAryREHerj1l/naFzmHJE6evkuUXH7dvMx34DMugCexOLq5eczIc494d\r\nUyyyfeEl3OuXnyZ4IV94HhMXy4EquxmDNqM=\r\n=RvWB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "14.0.0-alpha.3": {"name": "@testing-library/react", "version": "14.0.0-alpha.3", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "fccd5eb214c69bfb4099987dd6d69ff87a21368a", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.0.0-alpha.3.tgz", "fileCount": 25, "integrity": "sha512-INtYnpLluUELDZ6dSUQrSkwj3NOovYoO8JTUTDhGX/r9EjzJn7GZNP+/xj3oA7VMdyYNERznBmjefSAsK4JhDg==", "signatures": [{"sig": "MEUCICdD4HexOrj/kVOQIsMu56f4GKXeeHghIevd16muUG9LAiEAi+kZTZac+5yneveIKTysTwvMXrzY/ailF/vDCE2PoaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5284882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7rNVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn+w/+OKK09Nh1hbyxwelbor92cjigvZ8d51Tx2h1sPPXZwgMKpV0b\r\nmNn8flVHxDUorRzE0GVRR9lVuRvY9qrWPLH5dn12iYfxcMqRBbZTYxefGBYn\r\nWAaAibL2CuIsSk+K3iHO7qFYYHS2/+7goHgOmEkY9U2XMKWdLMvA87qrFhwi\r\nqsST87LsqHtTsjvjwgzSH0MJLUqZtrxsFM1r8YjlrhC2IL98Lxr13JxLBf7W\r\n0E/hrOhUIwWRlClRlZ8AtXh/vpSbCnZdwkFj6toVX6BJir9ufuf5qewA3kmO\r\nRqftGCV2rtlgAaqAMG7oXl2Mnl556gqVnbBkoUunH/2a6F5bwiuIN5grXcO8\r\new8QZCsuPs2yJ0US0mok0pxKHzOR2d/E/TZTy8VVoa/17ToSmTbhry8HPGD9\r\nENErfD4CVZBe+Waq5Ax51GPQ4p03CUqsbLXvPx63XgGDfX4Fz0Kq8oV7rK7m\r\nZlRQomfBu3sp6LMmCfG8QYszdbql/YsL3RkEQLKfvaU4cWZm3GP3hUvQ8RCl\r\n3JL99lxo80W/UzKHTQMOGgLYn9ub5ys+mxR4Wzm5gM8iDZjZKitFTVxrJo4L\r\nXiDxbpXC7/MmUDjCFHfIdD70oWBAYQDJfPuguwn7UUjFzhwDmPIwp3CAoW5P\r\ntfw4ckU+Qrb1PD28ET9YCvERfC4n6jbC/rE=\r\n=uVAb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "14.0.0": {"name": "@testing-library/react", "version": "14.0.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "59030392a6792450b9ab8e67aea5f3cc18d6347c", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.0.0.tgz", "fileCount": 25, "integrity": "sha512-S04gSNJbYE30TlIMLTzv6QCTzt9AqIF5y6s6SzVFILNcNvbV/jU96GeiTPillGQo+Ny64M/5PV7klNYYgv5Dfg==", "signatures": [{"sig": "MEUCICHjfuveKeMKQjLJWIbcThkiCK3PpYxih8GtNaSkCV4cAiEAyw/F/A/N3ppvK/AXiqg6tT8P6MzcxpAMPHvJ7uNiVho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5284874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7rVZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUyA/+IxXowvYiAX9pN+t1DbnHylta5AjvQGw3/DbupqVhQ6YTY8ni\r\nsC0lV+7s01KCGpGek1z6Vm3qf1ji7cHKpKZ1uK/7zaMOEnsnnFTrDmmsbC9R\r\nnrhfveusVQGIapWQzmQKpQnZAazMvKCi9kj96EOZ+va2PsZ2TlOtdjLp23ud\r\nDmpmwCAzStsLO5sXQXee93RQfYNhyQlVsu2DyVy1ZsbwZOtjsrYknoPxBBG0\r\n3HywmK2dIOGYuRDYHunniIhyPkdaesTuoKk75jRwLPuKRx/ou5oTK4MVs7/l\r\nYiSBpKs0HzMBIGjvn28WN6P7nkEEJE8brtk+ElGwQLIl9GUJWtA5NJvaUnl2\r\ndPqZ7df/5MSYdVrwSLY2vhX9iytxzB6nucAQLr3/CeuU9mvH1lj4q2lBaHDT\r\nvhOGxtTmwHy9Mb6MOkGBj8ssj/RZSREgQu5Mhp29KP0zY3DuuekXjOlEt6dt\r\ndLnJD3hzjcPPvLE7Pt+NiUdo70GLK8y18V2SP5GR2SDotjvQg+n3f6TNy+5m\r\ncHpPSLI4NCUnZejceV/CUYcnjvvpiPfcaHmQYniEVcLoa+2HLjW6B2fxfIyI\r\nffCQ/06dW/gCU+VGgyv/qBAqHfv8HjjarKue+0as4MhhAhHdEG/f59Mxd04f\r\nZW3GaA0/jkgtD7cc2FajEsweAZzWEQiuh/g=\r\n=hvXo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "14.1.0": {"name": "@testing-library/react", "version": "14.1.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "01d64915111db99b50f8361d51d7217606805989", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.1.0.tgz", "fileCount": 25, "integrity": "sha512-hcvfZEEyO0xQoZeHmUbuMs7APJCGELpilL7bY+BaJaMP57aWc6q1etFwScnoZDheYjk4ESdlzPdQ33IbsKAK/A==", "signatures": [{"sig": "MEUCICedZizP7oAv5g8Dl/xaf43OlJ/RY7+4h6VkzqwXDtrWAiEAvAy2OMqbWXTFW0J1jb06CHgxDEZ4ILmLuSoM0tNmxhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5390436}, "engines": {"node": ">=14"}}, "14.1.1": {"name": "@testing-library/react", "version": "14.1.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "38f2115766ae53e0328f1108fc18e458845b2273", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.1.1.tgz", "fileCount": 25, "integrity": "sha512-XN9qioSzyYvfH+O09pzc9HAhZ2dSuTZLh+EmHkp1OKKxe13wAf617EKLuQ5l5S5dlfgPf/p0MHTeRkqO8Xz8qw==", "signatures": [{"sig": "MEYCIQC9EB/WU9cOMS36sO/Mod8iKFuemSrzPIzJ+Guu/zkmkgIhAM3C5RlYCDvNtceJtUv8GRvnLrJ0mr8RqfmlTChA1o9c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5391929}, "engines": {"node": ">=14"}}, "14.1.2": {"name": "@testing-library/react", "version": "14.1.2", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "a2b9e9ee87721ec9ed2d7cfc51cc04e474537c32", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.1.2.tgz", "fileCount": 25, "integrity": "sha512-z4p7DVBTPjKM5qDZ0t5ZjzkpSNb+fZy1u6bzO7kk8oeGagpPCAtgh4cx1syrfp7a+QWkM021jGqjJaxJJnXAZg==", "signatures": [{"sig": "MEUCIApOfbNFMg8sTYmmAZmC83pJPQ7SwN26GSbHRhMTiPlTAiEA1E6Lunaioy9QRzOxynBy5DJZ300oUmGzj5V+36lCxVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5387644}, "engines": {"node": ">=14"}}, "14.2.0": {"name": "@testing-library/react", "version": "14.2.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "256add361581316b7ee9e60a585aaed9c8c19655", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.2.0.tgz", "fileCount": 26, "integrity": "sha512-7uBnPHyOG6nDGCzv8SLeJbSa33ZoYw7swYpSLIgJvBALdq7l9zPNk33om4USrxy1lKTxXaVfufzLmq83WNfWIw==", "signatures": [{"sig": "MEQCICIVvPJtLBbfWh5ZByOVvPJgr5RrGlL+7P1qM1qT917IAiB4u8zOeH9tyv/M4/BaxQ6A6J7+aOj5PwNTVnBmCLot+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5403808}, "engines": {"node": ">=14"}}, "14.2.1": {"name": "@testing-library/react", "version": "14.2.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.4.1", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "bf69aa3f71c36133349976a4a2da3687561d8310", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.2.1.tgz", "fileCount": 26, "integrity": "sha512-sGdjws32ai5TLerhvzThYFbpnF9XtL65Cjf+gB0Dhr29BGqK+mAeN7SURSdu+eqgET4ANcWoC7FQpkaiGvBr+A==", "signatures": [{"sig": "MEUCIHWQyxch8k95XGKSyYaPbHNdm01XCurdoLyGK5sdAh9tAiEAm51NrcPe5at4ezxK7LnRUilzObnQalfBAm7nU9dSXJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414483}, "engines": {"node": ">=14"}}, "14.2.2": {"name": "@testing-library/react", "version": "14.2.2", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "74f855215c57d423282486a395a4348a837d3c5a", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.2.2.tgz", "fileCount": 26, "integrity": "sha512-SOUuM2ysCvjUWBXTNfQ/ztmnKDmqaiPV3SvoIuyxMUca45rbSWWAT/qB8CUs/JQ/ux/8JFs9DNdFQ3f6jH3crA==", "signatures": [{"sig": "MEUCIQC/j/shReioXQPbsJSmiokyXF0U9HcFe4jYiUP6JfAxuwIgKjlOW6twWF8IQ2woQts9RxNjhKjsz63UxrmEkxb8H4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5423713}, "engines": {"node": ">=14"}}, "14.3.0": {"name": "@testing-library/react", "version": "14.3.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "8183eb5a5f465b5b8cc495fcbd9bad0a16b8dd3b", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.3.0.tgz", "fileCount": 26, "integrity": "sha512-AYJGvNFMbCa5vt1UtDCa/dcaABrXq8gph6VN+cffIx0UeA0qiGqS+sT60+sb+Gjc8tGXdECWYQgaF0khf8b+Lg==", "signatures": [{"sig": "MEUCIQDHBGWABNcWULVDSIMoQEm/3vHR3ovCCcH5lWHtS16FBgIgGx8BbK5f9b2OMML2yzj6zE7qy5nuAd5OUnU/RAuQZoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5433252}, "engines": {"node": ">=14"}}, "15.0.0": {"name": "@testing-library/react", "version": "15.0.0", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "13cdc21aa83c60c48bd05cfec3d7725dd775c6c5", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.0.tgz", "fileCount": 26, "integrity": "sha512-Nb2Sq8MoSvGVdOGGppRqXeafLtvXCOwiOQtcbqCfkpVNZZNUqIjeSOwrfJ59zBfmZbAn8PitnWEzUKD1YwMrCg==", "signatures": [{"sig": "MEUCIChdavFn0j/TGTUoAP0Fbyms9eN+j/Xv4I1JuXpFk+wMAiEAq+P7xz5F30T/jmi9RcHvTEw0wc9YS3vNYl0pC/uDuxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4294443}, "engines": {"node": ">=18"}}, "15.0.1": {"name": "@testing-library/react", "version": "15.0.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "d67258ddc7b175e00aa9d8ddeada60ff8ac03ff8", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.1.tgz", "fileCount": 26, "integrity": "sha512-I8u4qqGAuBg7C1/kRB9n7Oz9Pc/UHEkmiQRbJziSG8B13eZfAcAUn8TSP26ZIvfSeb68CngmtZbKKcRqcQKa3g==", "signatures": [{"sig": "MEUCIQDITNBPELesm/2yZImL2UyIfD0orzxbIrnqyw2hOXky3wIgMWNL1NhIWUaqX+iEjd7/bKDyxVJ2N0eNVfLX1VDToZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4295026}, "engines": {"node": ">=18"}}, "14.3.1": {"name": "@testing-library/react", "version": "14.3.1", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^9.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "29513fc3770d6fb75245c4e1245c470e4ffdd830", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-14.3.1.tgz", "fileCount": 26, "integrity": "sha512-H99XjUhWQw0lTgyMN05W3xQG1Nh4lq574D8keFf1dDoNTJgp66VbJozRaczoF+wsiaPJNt/TcnfpLGufGxSrZQ==", "signatures": [{"sig": "MEUCIQCLjXXmhl/YljjkvNvhLPDXGBMMjlc1rXP2xxB3lo+MIAIgKBFGuuvuds5iJKS50zJ+vxVV697oOLUqmPgdvxKpKOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5433835}, "engines": {"node": ">=14"}}, "15.0.2": {"name": "@testing-library/react", "version": "15.0.2", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "d0fd7e9c41b819557639acf5f18e4cd1007ec295", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.2.tgz", "fileCount": 26, "integrity": "sha512-5mzIpuytB1ctpyywvyaY2TAAUQVCZIGqwiqFQf6u9lvj/SJQepGUzNV18Xpk+NLCaCE2j7CWrZE0tEf9xLZYiQ==", "signatures": [{"sig": "MEUCIGsu5ZaFpx7jo6dkIQRxrCzOQEqzEBXRvggGNbtSIpx0AiEAzSBHeOCaguyM6AJocN/wQuNE0aifDHPAC6+ZuChnN3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4299712}, "engines": {"node": ">=18"}}, "15.0.3": {"name": "@testing-library/react", "version": "15.0.3", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "891f55596d8464d4db90458a99aa64fa1105ef9b", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.3.tgz", "fileCount": 26, "integrity": "sha512-lrfuttDGLJbpwMZ5Staz/b2GJuyQQUHEYffK2oL9DxgoeIPxFIquv0TmzJyeI0JQkc+WJMvcRRmpP9BtWlMbgQ==", "signatures": [{"sig": "MEYCIQCHjUsN0V4/O0wd2plhxb52HGX2Au7Wb8NamzT8oyu34wIhAMJv8fykg+C8NeHQv1JnbwccltqNj5kUE9A62AlSAJCt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4301573}, "engines": {"node": ">=18"}}, "15.0.4": {"name": "@testing-library/react", "version": "15.0.4", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.0.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.0.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "e5e83f79863c4897719ae3191af1e54617f60dea", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.4.tgz", "fileCount": 26, "integrity": "sha512-Fw/LM1emOHKfCxv5R0tz+25TOtiMt0o5Np1zJmb4LbSacOagXQX4ooAaHiJfGUMe+OjUk504BX11W+9Z8CvyZA==", "signatures": [{"sig": "MEYCIQDP9iPUFPG76i6cy+g1OVI+32TO4pEvdLZJXM1fUSx+9QIhAIwn30n/w6tNWNGBow8eQzNjR5pFHXJenG8b5eWzAxXT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4303366}, "engines": {"node": ">=18"}}, "15.0.5": {"name": "@testing-library/react", "version": "15.0.5", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.0", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "71bcc875e1142c3924cac7bdd7dc9a6db0bb1d42", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.5.tgz", "fileCount": 26, "integrity": "sha512-ttodVWYA2i2w4hRa6krKrmS1vKxAEkwDz34y+CwbcrbZUxFzUYN3a5xZyFKo+K6LBseCRCUkwcjATpaNn/UsIA==", "signatures": [{"sig": "MEQCIB1GETA7yEm92B5s2ZK0MuGUo/GivXi7Uy21zTAPGG7+AiBHjbDwrcu3EiLmlqIHtAKAeImXutWot/4AoQdMzPkwaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4302895}, "engines": {"node": ">=18"}}, "15.0.6": {"name": "@testing-library/react", "version": "15.0.6", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.1", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@types/react": "^18.3.1", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.0"}, "dist": {"shasum": "76be2e9e6da98c044823dfbc9d62ad3f10a3a401", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.6.tgz", "fileCount": 26, "integrity": "sha512-UlbazRtEpQClFOiYp+1BapMT+xyqWMnE+hh9tn5DQ6gmlE7AIZWcGpzZukmDZuFk3By01oiqOf8lRedLS4k6xQ==", "signatures": [{"sig": "MEYCIQDpHElTKJRsjr1mK1DCOI7EiOhuDqZ+lTxlONqEUxuWUQIhAKHaMNIPxxpox2hTbnxF7uVajmeJC9Q1GhzHBBFPJXCY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4305980}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "15.0.7": {"name": "@testing-library/react", "version": "15.0.7", "dependencies": {"@babel/runtime": "^7.12.5", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.1", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@types/react": "^18.3.1", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.0"}, "dist": {"shasum": "ff733ce0893c875cb5a47672e8e772897128f4ae", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-15.0.7.tgz", "fileCount": 26, "integrity": "sha512-cg0RvEdD1TIhhkm1IeYMQxrzy0MtUNfa3minv4MjbgcYzJAZ7yD0i0lwoPOTPr+INtiXFezt2o8xMSnyHhEn2Q==", "signatures": [{"sig": "MEYCIQDz7Kil8f22nGrRsQMsPK4x0bNhufj1FtHnI6wzFKQ/SwIhAICEANLTtbdFjpHYIcC6gs04BGH4fv+mI9Npa0qKUGmp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4306086}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "16.0.0": {"name": "@testing-library/react", "version": "16.0.0", "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.1", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "dist": {"shasum": "0a1e0c7a3de25841c3591b8cb7fb0cf0c0a27321", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-16.0.0.tgz", "fileCount": 26, "integrity": "sha512-guuxUKRWQ+FgNX0h0NS0FIq3Q3uLtWVpBzcLOggmfMoUpgBnzBzvLLd4fbm6yS8ydJd94cIfY4yP9qUQjM2KwQ==", "signatures": [{"sig": "MEQCIGo5DKje0U6u1mOHdOCwEDyJIoAa8JerUYm7imcum1ldAiB2h/p24t4LFbRE08E55AkiQFVUWxlVywU5cZhqzbFiDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329275}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "16.0.1": {"name": "@testing-library/react", "version": "16.0.1", "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.1", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@testing-library/dom": "^10.0.0"}, "dist": {"shasum": "29c0ee878d672703f5e7579f239005e4e0faa875", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-16.0.1.tgz", "fileCount": 26, "integrity": "sha512-dSmwJVtJXmku+iocRhWOUFbrERC76TX2Mnf0ATODz8brzAZrMBbzLwQixlBSanZxR6LddK3eiwpSFZgDET1URg==", "signatures": [{"sig": "MEQCIBQr1fPvOIARMERA6SWhrVgQ4S1tN2Ag1TJA8wo10ojxAiAPc2maIPVV5zgFI+TDkRsSb6OEUqyvr16Dm/d3MXGQFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329425}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "16.1.0": {"name": "@testing-library/react", "version": "16.1.0", "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.1", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.0", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "@testing-library/dom": "^10.0.0"}, "dist": {"shasum": "aa0c61398bac82eaf89776967e97de41ac742d71", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-16.1.0.tgz", "fileCount": 26, "integrity": "sha512-Q2ToPvg0KsVL0ohND9A3zLJWcOXXcO8IDu3fj11KhNt0UlCWyFyvnCIBkd12tidB2lkiVRG8VFqdhcqhqnAQtg==", "signatures": [{"sig": "MEQCIFILIj0J4mp9PWHXO1VFumw8OwCUs8VAKRUWrJqq59X9AiB74CMg/PhTzUkWIgJXKBOp5NkRkLDreHdEbLqi5iqfmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329455}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "16.2.0": {"name": "@testing-library/react", "version": "16.2.0", "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"chalk": "^4.1.2", "react": "^18.3.1", "rimraf": "^3.0.2", "jest-diff": "^29.7.0", "react-dom": "^18.3.1", "dotenv-cli": "^4.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "@testing-library/dom": "^10.0.0"}, "dist": {"shasum": "c96126ee01a49cdb47175721911b4a9432afc601", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-16.2.0.tgz", "fileCount": 26, "integrity": "sha512-2cSskAvA1QNtKc8Y9VJQRv0tm3hLVgxRGDB+KYhIaPQJ1I+RHbhIXcM+zClKXzMes/wshsMVzf4B9vS4IZpqDQ==", "signatures": [{"sig": "MEUCIQCfSI7DI0+GmQ2Zn8JMHy4tyAHP5faNl1xgy9GHJlYIHgIgI7hDGgpleFJsDKQjoWRHeQjzBKPqlk3wyhynzX0+Ayo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337147}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "16.3.0": {"name": "@testing-library/react", "version": "16.3.0", "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "chalk": "^4.1.2", "dotenv-cli": "^4.0.0", "jest-diff": "^29.7.0", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^3.0.2", "typescript": "^4.1.2"}, "peerDependencies": {"@testing-library/dom": "^10.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dist": {"integrity": "sha512-kFSyxiEDwv1WLl2fgsq6pPBbw5aWKrsY2/noi1Id0TK0UParSF62oFQFGHXIyaG4pp2tEub/Zlel+fjjZILDsw==", "shasum": "3a85bb9bdebf180cd76dba16454e242564d598a6", "tarball": "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz", "fileCount": 26, "unpackedSize": 341724, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDHc56hV7PmqYkpFb0XyMtrwxhIOaJ7t1Ew5NFhDvb1rAIhAK5q03AJAuhH1RzfkDZWIF/QeIm/8/toNCITEIFhWuW6"}]}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-04-02T17:03:39.139Z"}