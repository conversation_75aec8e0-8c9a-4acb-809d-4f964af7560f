{"_id": "normalize-range", "_rev": "7-26ec3d33e2aefab85442fcb7aea78a5c", "name": "normalize-range", "description": "Utility for normalizing a numeric range, with a wrapping function useful for polar coordinates", "dist-tags": {"latest": "0.1.2"}, "versions": {"0.1.0": {"name": "normalize-range", "version": "0.1.0", "description": "My dazzling module", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/normalize-range.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "npm run cover && npm run lint && npm run style", "cover": "istanbul cover ./node_modules/.bin/_mocha", "lint": "jshint --reporter=node_modules/jshint-stylish *.js test/*.js", "debug": "mocha", "watch": "mocha -w", "style": "jscs *.js ./**/*.js && jscs ./test/** --config=./test/.jscsrc"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"codeclimate-test-reporter": "^0.1.0", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "jscs": "^2.1.1", "jshint": "^2.8.0", "jshint-stylish": "^2.0.1", "mocha": "^2.2.5"}, "gitHead": "4087f4ced3dd2aca0408dc38b8dc5348b46b52a1", "bugs": {"url": "https://github.com/jamestalmage/normalize-range/issues"}, "homepage": "https://github.com/jamestalmage/normalize-range#readme", "_id": "normalize-range@0.1.0", "_shasum": "7dc10354319857a4bc115926c2c0b46584963102", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "james.talmage", "email": "<EMAIL>"}, "dist": {"shasum": "7dc10354319857a4bc115926c2c0b46584963102", "tarball": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.0.tgz", "integrity": "sha512-4fsB/sNmYhDAIP6QbzL9PmwCvnLfWL0Ln69oYIN2oZENUe4rLxjlXjEJuT9idgayeSJA9sMhp5mWsCTVIIpONg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB2CC+fsxUBSR/x5AQclibqWsHjYaroDVX0t6mecusFSAiEAuOk6cKVrq/xKaJuUVp/RbahS2+ly1bHziRjjNc0uCTM="}]}, "maintainers": [{"name": "james.talmage", "email": "<EMAIL>"}]}, "0.1.1": {"name": "normalize-range", "version": "0.1.1", "description": "Utility for normalizing a numeric range, with a wrapping function useful for polar coordinates", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/normalize-range.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "npm run cover && npm run lint && npm run style", "cover": "istanbul cover ./node_modules/.bin/_mocha", "lint": "jshint --reporter=node_modules/jshint-stylish *.js test/*.js", "debug": "mocha", "watch": "mocha -w", "style": "jscs *.js ./**/*.js && jscs ./test/** --config=./test/.jscsrc"}, "files": ["index.js"], "keywords": ["range", "normalize", "utility", "angle", "degrees", "polar"], "dependencies": {}, "devDependencies": {"codeclimate-test-reporter": "^0.1.0", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "jscs": "^2.1.1", "jshint": "^2.8.0", "jshint-stylish": "^2.0.1", "mocha": "^2.2.5"}, "gitHead": "1a09c330c6d4d45003c4324d10ab5a2784e1206e", "bugs": {"url": "https://github.com/jamestalmage/normalize-range/issues"}, "homepage": "https://github.com/jamestalmage/normalize-range#readme", "_id": "normalize-range@0.1.1", "_shasum": "a40ffccb98fe9d3a2ebb52014b0ef42973fe0209", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "james.talmage", "email": "<EMAIL>"}, "dist": {"shasum": "a40ffccb98fe9d3a2ebb52014b0ef42973fe0209", "tarball": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.1.tgz", "integrity": "sha512-E40wR+fwvuCNoAPhlT89paNVG4Dx+QFWq0P5RPjHtRU0UDWW1Zc4tYGnFqOYCrprKvoUSz0zIW0sYz6bh5gY5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/UFjvwwZGgQcE0y9dcb3t2X+D4WzSxZTxSPaqZdOIQwIgXVUyEpzKlSUVKYlirK2CltJztrd8qLEqwGvmKYTaNu0="}]}, "maintainers": [{"name": "james.talmage", "email": "<EMAIL>"}]}, "0.1.2": {"name": "normalize-range", "version": "0.1.2", "description": "Utility for normalizing a numeric range, with a wrapping function useful for polar coordinates", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/normalize-range.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "npm run cover && npm run lint && npm run style", "cover": "istanbul cover ./node_modules/.bin/_mocha", "lint": "jshint --reporter=node_modules/jshint-stylish *.js test/*.js", "debug": "mocha", "watch": "mocha -w", "style": "jscs *.js ./**/*.js && jscs ./test/** --config=./test/.jscsrc"}, "files": ["index.js"], "keywords": ["range", "normalize", "utility", "angle", "degrees", "polar"], "dependencies": {}, "devDependencies": {"almost-equal": "^1.0.0", "codeclimate-test-reporter": "^0.1.0", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "jscs": "^2.1.1", "jshint": "^2.8.0", "jshint-stylish": "^2.0.1", "mocha": "^2.2.5", "stringify-pi": "0.0.3"}, "gitHead": "4add1b491840c17c80cd0ca8145505d663955861", "bugs": {"url": "https://github.com/jamestalmage/normalize-range/issues"}, "homepage": "https://github.com/jamestalmage/normalize-range#readme", "_id": "normalize-range@0.1.2", "_shasum": "2d10c06bdfd312ea9777695a4d28439456b75942", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "james.talmage", "email": "<EMAIL>"}, "dist": {"shasum": "2d10c06bdfd312ea9777695a4d28439456b75942", "tarball": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/pPGFro0Dc2jy+Xczxvr24YzO9s1xQynEBZoe2bi1MAiEAu5ZcDlfU7EqSGImCiQY9jpsumxIQCyAlpFAuAnfjDw8="}]}, "maintainers": [{"name": "james.talmage", "email": "<EMAIL>"}]}}, "readme": "# normalize-range \n\nUtility for normalizing a numeric range, with a wrapping function useful for polar coordinates.\n\n[![Build Status](https://travis-ci.org/jamestalmage/normalize-range.svg?branch=master)](https://travis-ci.org/jamestalmage/normalize-range)\n[![Coverage Status](https://coveralls.io/repos/jamestalmage/normalize-range/badge.svg?branch=master&service=github)](https://coveralls.io/github/jamestalmage/normalize-range?branch=master)\n[![Code Climate](https://codeclimate.com/github/jamestalmage/normalize-range/badges/gpa.svg)](https://codeclimate.com/github/jamestalmage/normalize-range)\n[![Dependency Status](https://david-dm.org/jamestalmage/normalize-range.svg)](https://david-dm.org/jamestalmage/normalize-range)\n[![devDependency Status](https://david-dm.org/jamestalmage/normalize-range/dev-status.svg)](https://david-dm.org/jamestalmage/normalize-range#info=devDependencies)\n\n[![NPM](https://nodei.co/npm/normalize-range.png)](https://nodei.co/npm/normalize-range/)\n\n## Usage\n\n```js\nvar nr = require('normalize-range');\n\nnr.wrap(0, 360, 400);\n//=> 40\n\nnr.wrap(0, 360, -90);\n//=> 270\n\nnr.limit(0, 100, 500);\n//=> 100\n\nnr.limit(0, 100, -20);\n//=> 0\n\n// There is a convenient currying function\nvar wrapAngle = nr.curry(0, 360).wrap;\nvar limitTo10 = nr.curry(0, 10).limit;\n\nwrapAngle(-30);\n//=> 330\n```\n## API\n\n### wrap(min, max, value)\n\nNormalizes a values that \"wraps around\". For example, in a polar coordinate system, 270˚ can also be\nrepresented as -90˚. \nFor wrapping purposes we assume `max` is functionally equivalent to `min`, and that `wrap(max + 1) === wrap(min + 1)`.\nWrap always assumes that `min` is *inclusive*, and `max` is *exclusive*.\nIn other words, if `value === max` the function will wrap it, and return `min`, but `min` will not be wrapped.\n\n```js\nnr.wrap(0, 360, 0) === 0;\nnr.wrap(0, 360, 360) === 0;\nnr.wrap(0, 360, 361) === 1;\nnr.wrap(0, 360, -1) === 359;\n```\n\nYou are not restricted to whole numbers, and ranges can be negative.\n\n```js\nvar π = Math.PI;\nvar radianRange = nr.curry(-π, π);\n\nredianRange.wrap(0) === 0;\nnr.wrap(π) === -π;\nnr.wrap(4 * π / 3) === -2 * π / 3;\n```\n\n### limit(min, max, value)\n\nNormalize the value by bringing it within the range.\nIf `value` is greater than `max`, `max` will be returned.\nIf `value` is less than `min`, `min` will be returned.\nOtherwise, `value` is returned unaltered.\nBoth ends of this range are *inclusive*.\n\n### test(min, max, value, [minExclusive], [maxExclusive])\n\nReturns `true` if `value` is within the range, `false` otherwise.\nIt defaults to `inclusive` on both ends of the range, but that can be\nchanged by setting `minExclusive` and/or `maxExclusive` to a truthy value.\n\n### validate(min, max, value, [minExclusive], [maxExclusive])\n\nReturns `value` or throws an error if `value` is outside the specified range.\n\n### name(min, max, value, [minExclusive], [maxExclusive])\n\nReturns a string representing this range in \n[range notation](https://en.wikipedia.org/wiki/Interval_(mathematics)#Classification_of_intervals).\n\n### curry(min, max, [minExclusive], [maxExclusive])\n\nConvenience method for currying all method arguments except `value`.\n\n```js\nvar angle = require('normalize-range').curry(-180, 180, false, true);\n\nangle.wrap(270)\n//=> -90\n\nangle.limit(200)\n//=> 180\n\nangle.test(0)\n//=> true\n\nangle.validate(300)\n//=> throws an Error\n\nangle.toString() // or angle.name()\n//=> \"[-180,180)\"\n```\n\n#### min\n\n*Required*  \nType: `number`\n\nThe minimum value (inclusive) of the range.\n\n#### max\n\n*Required*  \nType: `number`\n\nThe maximum value (exclusive) of the range.\n\n#### value\n\n*Required*  \nType: `number`\n\nThe value to be normalized.\n\n#### returns\n\nType: `number`\n\nThe normalized value.\n\n## Building and Releasing\n\n- `npm test`: tests, linting, coverage and style checks.\n- `npm run watch`: autotest mode for active development.\n- `npm run debug`: run tests without coverage (istanbul can obscure line #'s) \n\nRelease via `cut-release` tool.\n\n## License\n\nMIT © [James Talmage](http://github.com/jamestalmage)\n", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "time": {"modified": "2022-06-22T05:32:01.991Z", "created": "2015-09-12T06:18:30.740Z", "0.1.0": "2015-09-12T06:18:30.740Z", "0.1.1": "2015-09-12T06:27:02.731Z", "0.1.2": "2015-09-13T05:20:04.639Z"}, "homepage": "https://github.com/jamestalmage/normalize-range#readme", "keywords": ["range", "normalize", "utility", "angle", "degrees", "polar"], "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/normalize-range.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "bugs": {"url": "https://github.com/jamestalmage/normalize-range/issues"}, "license": "MIT", "readmeFilename": "readme.md"}