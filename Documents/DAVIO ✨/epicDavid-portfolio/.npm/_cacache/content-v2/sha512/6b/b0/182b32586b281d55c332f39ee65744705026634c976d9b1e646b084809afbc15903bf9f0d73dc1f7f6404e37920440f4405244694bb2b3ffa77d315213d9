{"_id": "@babel/plugin-transform-duplicate-keys", "_rev": "115-21aacfc513204be1a4ad4e89b05d9cd6", "name": "@babel/plugin-transform-duplicate-keys", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "051c98712b07a75510f5278714e548d138745a15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.4.tgz", "integrity": "sha512-a+46on+HVqOMLxSa1+yRHkKB9cEK74VdJlzDd3bAzzoA1zkgJH/NBPYkFWDcMQcrNH2LYYUlkSxj7zf+7qONQg==", "signatures": [{"sig": "MEQCIC6TgmeLFrQPWIO7k2kl4hhKEHrb2LInIPf1hmnQ6vQdAiBf2gnBZ8fMw6A3UOfcwIozxNKzXHe4nWullDR1mWBKIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.4.tgz_1509388521445_0.35266457684338093", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "818705aeaeec642de718fa4f5eec25b595d7fb23", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.5.tgz", "integrity": "sha512-uu5Rqq6O/dZMvS7kf54WfdTEgRcAV2rg9DP0dup0OXzOKJQPb3ayFey84H9a2aTlaZ/I6HKomLpzCtxY2Ou+Vw==", "signatures": [{"sig": "MEYCIQCH70fy8Esk/uExtJAxpiYIvMLeZVnkbZgnGusRC1GBgwIhAN4jOcU8jZx5CcMoc/4qjxue6N6Eg6/r/WUIqnWgGFR3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.5.tgz_1509397018398_0.8964017622638494", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "bcc94294c6da42589fb39bc318836419dd061334", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.31.tgz", "integrity": "sha512-nx25QHOLxc4hNsT5OlKveTqSmAWkBYmtA0ACnbUuxtHmKirEEAVMLQBgglpJ2tE9hbt/GUOs9CjPt6gAL9LP9w==", "signatures": [{"sig": "MEQCICM2G7ocfbEjXXe5t+RD0XlbAFMVKV20Wg/G+MlcUNtSAiBbGXz1qIy7/MeyeHXnuVYb8eSDOjh3Wamx7bwNoMD7Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.31.tgz_1509739434240_0.22006331267766654", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7bf14af38b50007de21ac3d26e26843dae726a03", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.32.tgz", "integrity": "sha512-Obktgcju396fmESjnailU4qZnoYbNzQWx7JiRts6/dXFbM6mYfPclUpxy/HuBbn/0oR6qRAMuPrAJPD0yi1+Wg==", "signatures": [{"sig": "MEQCICByRBStbGxdSOSUzvvtRF4vSHH9mVe280GMl2unwwfsAiA9RfUPqWu1c9dUJ4WG+Pq+IB8QsR34+bBbAdooeSaiCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.32.tgz_1510493592949_0.48799351952038705", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6330d600dde091763f9c1f8f2fc42a32d085fe26", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.33.tgz", "integrity": "sha512-o9TGA6Jby+nRYfHR9vCZ8o/f5uaZfPa5xyvac0xIVwu2l4bjMDEassgwVWeyCAI7IS3HLqDiUK0TKoO/j0/oPA==", "signatures": [{"sig": "MEUCIQCsK9MNojbqV+KOJRBb1cc7gzdqXjSPQQAWUsXOqlm0SAIgJYVqDg4MjWkCMW76t2lermWLv8N3frW5o9DufGj3Rio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.33.tgz_1512138494863_0.1571552366949618", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "83e9ab1f519d0d69870c14f1fbfdc87c8720f17a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.34.tgz", "integrity": "sha512-v+LVs2wE4GnUexdFiINF6KrBptyOxE5OArl3gaBUcdIgLAh/CCHixbLr9VaJPaGlFf6D66K/s9KYNxXkgHQvBQ==", "signatures": [{"sig": "MEQCIHH+ag1LiSkgL1jh5qeeCTEE5CwF52fm8n6zP9RZ+oGmAiBm8PQSucvBsxP2RcsqcJ033cB/H20FNoId/7NuHqVMow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.34.tgz_1512225555646_0.056096386164426804", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c0409a2d2d8a4718b0abea3a8fd136fdf3b4ff3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.35.tgz", "integrity": "sha512-K6/BsBT+oIScy2rvGWP6WWWB1ayU0FNV4rsvHGx2mS4o5G/OmFz52PhViDQxWEoNbU7+2Z263CNgjelzPsX5oA==", "signatures": [{"sig": "MEQCIGq7VN3wnXyryYUzyOsyAUU3wP6wWWpM4fHdTuULsi+fAiAKx2ps4qnlXiaGlQ55iCchWc0Q3Hea/XG1npv7F6RNNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.35.tgz_1513288062451_0.03817579266615212", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4d6904dc7ed27d24bc184f81661698b265e8cd08", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.36.tgz", "integrity": "sha512-MgFmoRipky5qNgeMf1PnYmfYIXpydlIVDbpskRYT5mOzi7q+demNWdA4rRM9W5vbLFagwTuOCPFZ+DyITq2vLg==", "signatures": [{"sig": "MEUCIQDP+nxoXAwTwqeCMPhQ04WyDLg3DLtyqkEFQZzWR7UGXAIgW+Y0gVBsZ5Joap4UBOd8EY8UXuXTn9fRYqWzHKmTQMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.36.tgz_1514228673131_0.4590614796616137", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "beb7b2e36af714ad25eb10d84a571a42cca10c9c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.37.tgz", "integrity": "sha512-PV4bEKfWz9lZiXYB7hFr9O1b2TYxnIo1mv43DY84nfjDFkLuHrW6SLTz4r5ImgL7LMj9eki6caLI6Bn4eiJ+Pw==", "signatures": [{"sig": "MEYCIQDPPu7YuVctV/f6iV+jjMA2+IvzFeTPU2KXsj+zd6u9CwIhAJ4CxrVf8or1IjsEwOjgKi/TgxjsPvYjm45aWNRMN6sB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.37.tgz_1515427347646_0.7979456058237702", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b120bccd8eb4f2886e8323e41ad118bb244c0fa8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.38.tgz", "integrity": "sha512-FRHj7ukkoPV1yZEU22BjdlJYEox5voH6FPxrqJHiTYtk89h8PmKotbnYjMagNniAQ+tZ1iZYY229ntrgNN35Ww==", "signatures": [{"sig": "MEYCIQCAUD18NXDH9p9CMdnwgcS5Dm0OTT7FUCs0BmB33Fr8VQIhAO67JxKnl8j7kyGZaV9xcUMUKjPxzik69/WuFbdlst5Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.38.tgz_1516206710576_0.953702125698328", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5c402995e883d186941e230277df7f89c552f961", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.39.tgz", "integrity": "sha512-CsxyL2LHeVqenYc4keeI88oOMv2369h6VZ+JqnndU4U5IM2Qq53XXd4LEmFP0IspOH2sb4VRZ7TiIJ2/NWy16A==", "signatures": [{"sig": "MEUCID/+B66XPsrYAk1yaSJVPYr2pcQmtBfCAlhYcG6dzaHJAiEAs47YCwED06CKPzq4wUk0qZtfWlp8vDfo8lm5gLRd6e0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys-7.0.0-beta.39.tgz_1517344052119_0.7463709637522697", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "91599be229d4409cf3c9bbd094fb04d354bd8068", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-rxEyRbU/iEGR99oBMoer5QeGWLMhT3Kq4a8B03DFLCBpGLv3XirpSGC/Ys1YhUKAmEio4jIcVVI8dRBbcVeyDw==", "signatures": [{"sig": "MEYCIQDEaOuFSEBKlpwAYZH7X3UAw8U+1fUVQB4y+W5s7A6HvAIhAPqHuIoE6aBw/W/1Yv8UuqXm8M9JaurUvelAHaiMRr7Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3669}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.40_1518453693419_0.6681217651065181", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8aa99958326aece8b2c6b9e6d75151445c732ce5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-pay0IqubQ7qcIkanRGXYAAJYNa/7cPx+Hyau8qvXTs6Lhwa1MquATcEztFko2dYPLgvaaT+h8yyD9CidE3qYaQ==", "signatures": [{"sig": "MEQCH3wIVXnfywFchCe61k8Q8F8ujC/lg8fT67WaCDMBbCQCIQCayCnn6oSn0Qm6c87GweDwaLhBfLfn+P8Bn+r6zdiFkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3904}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.41_1521044766904_0.2982593532337692", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9678ab9480c6120e9b08014371c010bed481485a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-Nze+na6pnoZdrMtQv5Ct/i3031ib+kctDwt8KgcrtnOr52qUlbogPe1jY2WxpwSMtiowdPY93XsRFZeq6ogMTA==", "signatures": [{"sig": "MEQCIFqKh83ko1xPksjxTWYwz5r0Jc4/OQyYrjigO0vsKTkoAiB5vU4DNvh7k3vrT+o6ada11iF/0tswZ1Liq299W22uBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3904}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.42_1521147041140_0.019271477203342657", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f0766c4d4bbd706df45112373ec618349b449582", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-9uqs+YSoiojlP679ip+6ImWQk5/RRxS79PYWQc7DTT4UAtbsyTUyDDPV/BqumkRnVC2ir2V5LCplxGYRWYLMMQ==", "signatures": [{"sig": "MEUCIFNPXM5TXgxTfo5kKN5c21g5eLpDYgzavgWurd8wQliGAiEA4Y6C75e3ixhkJb37BLttnJwLyv3zf66xv4h/UpAnOuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3690}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.43_1522687703518_0.8345330121306829", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e945a7990d9adca4f9b58a7af46cdb1515b925b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-j0qCx+zKF/iZXJBGm9QRJ9VCQuiFdZJHTueGPuzb2oxNMj2hPv+Uf2SOidxglbvHaGmDoODs6M0dVXnHTGoy9g==", "signatures": [{"sig": "MEQCIC+YWzHvYDXWLCB2MGa5iPBzHOPClmV8e8NUExkfwvZhAiA/N8EQLHT3vG4ZhZ0eO4Xt4Pj03rrQWlHCoCgcwVYbrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4151}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.44_1522707605374_0.3524262107149334", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8e2f61c14cb74015e85e896589650b06314540b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-5dY1O/4YIP/BoRcXv5C3Ug58eN9kHt87hCOjgchqbZZUh98AHlSBuCYu5fK/Y/OdsmQsn9aqWwgMUW24EDVoTA==", "signatures": [{"sig": "MEUCIQCYKQNNoYKKKvjXLHSGQG9wV0B4yPdvPfSLP2RfxXpuzgIgDVSoyMfzSyO8CamRnv2Vy/PhEdbL6+Klwoso+zWRaK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1cCRA9TVsSAnZWagAA5HcP/3mEMYGmcIueotsYdJON\nfqq/FtmgB3IFgmV3uRnc2lRi6PmbYWwEV//YoqxF5ZtHcN2XN7v+pxqcolsl\n6Hp1S/r0YYiony0R8rHdV+2tmHPgOHrAkYK2toJEWRU7s7KYhNf9jgiNd/qG\nh1tyGbaBFByOcBkYDa0SvNIjmzGTsIQCJ9eDqhZMUz6fovO7Ac1cyeuLaZ4Z\nN3GWpFDy7rU9GLU7pFATRJvPhY8vMF4abtSE9R0vk/LiyJ6pdSezL64Dqxu1\nkdCRvIis8al6gMZ2HYvpUNheK1170qoSbvYYFb7hAVHgwS8QJvyUX2MW9hrq\nosm74Rko1tKur8RKgKEutH9sZvXX2qX8d8dSCRRotNHoHMrXOXG+wVfkBeBK\nLRRBL7Fs3qZ0Gi1OS7v8PeNX+aj0HvJVhBMBkVomFD1hh5tJ9LU7g4F9TfLB\n5bGkDlJCwoE9p8xANhtSc5/k/gli0ftPdiXliUDtyEfFiV8BreYYae+yxhoN\n98eCnLmtyzJK3bsJgORKiAxzfZilyrngYjupn2T/4DIPGYgExgXBau6aPoUm\nCiYKzfILrhb5CDyVhh/fYx/mDV279448dKOm/0IzFf9vtmV8x6psKbquH8Yu\nTaRU78GKMYXLQ2x67TJACXEL+cbb+LFQG3/2xS/9VftSmcOeeQwGv3+HXng3\nTGCL\r\n=fBIE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.45_1524448602919_0.5267011491370144", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7e94e42099b099742617838237b0d6e1a9b2690f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-mP2+3QQ+ArIMX98zVYSC9XBzV7A/Pxbz+2hPcEAGVeakFYm5AeTkcVHRQzzA21v4ecl0L5LE1XWX9yeK643CWw==", "signatures": [{"sig": "MEUCIQCdemqG13LtyWcmQIggZw0FkmlcD0WCJA0WmxAsBnq/LQIga0UKuoTRDhJPPNpAPZakxCg7wrFOR2QCWMbZjvgLe4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGLCRA9TVsSAnZWagAAhwIQAInZTZTfhtMV0gQKLj4b\nc5urJnP2no76MyEL0qN7mGW5t1OriE0NRcj6aQ9SNzYIUzqsVlEjjZIqE9J1\nRm6VV7KL1B/UO4ruCO8fQq3zgtIWhQkE66qkVq2rTwZhJE4HLJA3jPYJZcBb\n2/rRVpWeHL4htd2lBpaK1QmK/kplygtV6DAPCw/IVAXEVgpC/WS6OZzr6VEb\n5knd6tJ3vUVnC4rux7DZc2vp4c02CZID4D/1VtdEJBgEYhxxcYfEiU41LnJu\ngYujTAZPL/XXC7CaScFIT1wkOGHkKoa74IZsOW49tyXXroUIrUJd/YGQIdqx\n8tjJTaMb6yz1HH0ybXqguGOAJu6Npud4EUlnFVjV2+WXkcrXMdD1N7usQLAN\nftTjTkKea/670QCWE1jLDSFKwKwpq+LWLe3cmvclEndr+shEMDc7A68YxU08\nz/ju+UpEf7CceNVd/mAOYxGQFadjFnRr87v74SsvYGaCZM1c/2O+GhGIJCry\nSqvx2JGFJJdo2AbntgTPvBt8Hlnqj4zCa9BuAo4dGhydHlva+voXxU8jfrjo\nFznPhGR80ZvGrH5wNvUb4oGfZw+5Hp8AQVH/LdWTU3AOgm3q6bI6FLu9CuCz\nByYmjKQtIdoafjRqT1LUQxhtSLnjpvxYJ/aELHNb0iyCMUow8u3NgTCGMDu5\ngaa6\r\n=TWMr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.46_1524457867299_0.15612420781589464", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4aabeda051ca3007e33a207db08f1a0cf9bd253b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-r3xNVYTLVasjqTowIr6s+27oc5n7A5TKbB0/4u9FHjF7ONTWaggO8UFbbj07DOJ4Ll2RkigrZA8/D+w2nJ+XlA==", "signatures": [{"sig": "MEUCIGoV03q8OuCfpiC2NBinw4MlOVefJFty3/JV9jZwcDc+AiEAp7b/Ba4ptiu0resANuwBFStGmXHJOpXviYQ+/FvVxsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUTCRA9TVsSAnZWagAAg0gQAKB3HySH8jnw8eC4EPEW\nUdjevxuMrbleF6Ctmh22daJP1UpHHvR5RSoW6q6QZf3Ze8AIizjZK6lGZX62\nv7Vni2dSrzAowXFA17HJ4CjDSQURbw5UA4FBBLmF/5XQmOnBXYufvqocpD3j\nvJpSsKG3lqYhsXaWnT1sN1OOzib1pzOFVWtZWWCSvG/Vq0PAan//BkjLp5HH\nutbx6gBZUZwMNttjuFbNfoNNmMdadiCh9VkUaMrMJ+S9youRPMPIC+UNI7NP\n5SGRV646ITw7ajry0PcKYP7qcmT50UqPh3kd+ACBsWN2xEnQ1AGF09hpsERg\n/kfpgRG8+opG66QhsfhyC6Crn9Blfi5SVTyDoEmbZiBNameKcbK3KykUaw8u\ntqzDVtp6AKkp/qrsy7GStuvgisG/cvlN699akA2bGxS2gJUBnvp4jy1smcYc\nrAh00vlaGdZ3fRAaJwmvX1yBkEgLieSSWSt7/8JJ7+3Re94QbxgHgb7AgDbo\nYIF1WiBxxOv+isLKSUvq32ZiM/QSpM72Z2EezufiokTtmaJTnf4M793B/7tD\nIoO3ypKWKzF1ucHHm/XC2dUHvwwQWHUNcYy2ew9deWcAuFETXkWsA5w0tpNs\nfYcAt7AOG3m822w4IE1LHvc0MZkxzQ5jtf6IdNqPCA5CeONSix2muMNn8Adj\nxdi8\r\n=MPhX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.47_1526342930515_0.5920075134060376", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5499d8e525d7df7950df0a9b496cdf45de92d38e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-<PERSON><PERSON><PERSON>+bdOvv6saJnp4NpIbk9wKPH8dax8ogLykn4V988PWGKmKjBT/Vw9w2pv9xsz3zYYBYsK2lZWkXW32BBNfg==", "signatures": [{"sig": "MEUCICXsufoR9KGIPGpHlfiYu0dyOJhLlkwNwz5thNS9xpY4AiEAx1tLgqCcb1/MHqI1Fh9nQqJrDbwPXJZoqI6sRC8wZKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDsCRA9TVsSAnZWagAAYGYP/186ZMD7oUVEeH7bBgnf\nipnMD/Wc5jZGxxKlmjHW7306IVx9JA+0NWTzqSG7jWzIXVZhAiujYzOU5uUh\nwJp8QVKMLStD3P9z2TZV5GMfhvI6KDXFffDo/C+En9dpzPOFXaQBXuJKiL7r\ntFiinmY9KUSxua8EbBK7L4AQ0KmyB0Nts000zkY/E/IEa8O049KJujEoSVGf\nvD7BMeCDIIKZwzyqcCPrR5KY+nxjyol6BPvlRVfxpmtvSqSqepkaPoaXHFB1\nuv2AGOp64VAabuhmmRSdKPYp3OsVqHtrK/+4xJqdrwXlpgZc+CaTc5id0VKQ\nBOEZoCx5PGSYsqSmdglU97YQnDvnhZa1vOlY7g/Nyy211grFSYmBUIc2VLL/\nfiHlO3HXe2RgrFgjfPcuvM0lYUzBAKThwnyPoUF57tNh4japm2wCj/FP33nj\ntl4uJIkn7YWnNbkNqbZ89NlBayETraBFLxLQie+a0+WWS7oCibgoKI7lybRe\nqJiz3Q+MVwojnqy9ilowr9MnvmXvkxDOmZBD2CT/lV7FgLWUIcsz/IloBKtT\nY8Hsmh7bA1aQV8LhqfOqEsxiZmuWIov2ZIWg7TrIp0eUr/6yVHEoR7r1UH0v\nVf4JWJ6DvEZkcndfb/JxXTL5J2NXyTbAcfaEvLxBYT3x0IGe2wwamWj4AVBQ\nk55t\r\n=G6DV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.48_1527189739511_0.9239330398330181", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fac244809ddecbf095e375558ccb716da1042316", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-JXoGlXJs7SRKTmpOoP9u6T3yUlZW8F2Sn0+BswSxF9gDvtT3WI47VJhkX8zKEYMIw77kMqWxagxXGFkvpZPYyg==", "signatures": [{"sig": "MEQCIHRE69Vu2VujyCo5+GXVbnKrRnVhIqZK54HyFZ3qqpduAiAZTOWqdVdFaAAUByjDODW1IJ/wAuSjFA6lupMhxbdX+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDN4CRA9TVsSAnZWagAAkh8QAI6mUEuLJ8IgqmUOToFM\nzwUUe7cMV9JUlDNDtnMweHF8UVC29fKuCh2hmWS2pbUupnw02pPz5tNkErnZ\n4dje8JDsFV7XMLTdXWVzLHuNuV22Uh7fqvte9x6Z5p/H1LE3iETqsFlT66Mo\nzCVhcnkvUh5+hliZ0rkH8JjDNlGzhC+s4AAKZmbOX9lxUUtpkR+PXGurct3a\nanlMRRtPK011bHIc/BZQ4PLkBP9Vfd+TwFvVbQFM9wvHzBYxaazXPTZz3trA\nuQ0zDKnK09N252ZZ8W92ioNPk9L+7zs8FgQnI965DoSovnP4UI44wwb35JTP\nkGDmnt3Rt8WuQOnV4ZyaNm8u0qwEBFjC8fix1M6JC3nA9lEitsLnOt4ArW90\nQCIbZEpCXP7wckcczAtWWtvcffmDB3UYb1PDmU3UvhrOgpLCzNAmW4jYx7JB\nlljMXQVVyJQueKnXtQISMInE1PQpJPcuuHnB9vSgt32yps9vgxzQFNJM4XZ9\nRzQfjgM8oiMng7DXKBvYD0VorDBhxDvgtk5Z5LKppcZ67lLXKacXk1Auu9dj\nH6jHVtuZxFuaJn8nWKryl0NvxxXnG3jUfGfAB5jTu8W9KE/wu+h5GV3QJ7W3\nokHzc6J8s72reSOChPnWeZFwonU+YVxs9+AQ106aFUSadhdMxSjkHMcpEf5q\nqUbo\r\n=Db6b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "fac244809ddecbf095e375558ccb716da1042316", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.49_1527264120397_0.5135545647323618", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a5e26ebb261b6089430431a0065b63f7e8dfed5d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-mMDSiwQ3XPt4VfQxNhFYcDpAOSdKE6ntSOjMGFBV33U5IvsMXVz90v2AxWjH3BxAWR2fppmMBf/IUw055q1JUg==", "signatures": [{"sig": "MEUCIQDR1R+g5khDlWbOKqKjrfjO4oMEsAn7WOtMj+nl/QnXsQIgGOW8i/KcwPaAzgCODV+H3rZF03aAxquO2go73lXJLHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3049}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.50_1528832835435_0.7491406432205108", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "541eaf8a97d14a9809b359d8f548001f085b9b7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-pMD7XF130v1kD0sGB3raC2Hs/HN+6uu0gZ+9o6SuuluF18NtLEkGA+T686s+IZl6pMevL/ImrcbQsG09tmgXmQ==", "signatures": [{"sig": "MEUCIBA7vkMsl7CTJP+KOe6NyLoVqhBNKrcN/5C3XyLrkjh9AiEA9X1Et1zgwcj0z7z0FxlRCTS1Fbl60u6IPQmq3dc+xVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3063}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.51_1528838385807_0.34289992473910225", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "98dccf5199a8be89eb159c316f68a4ea44f99ce6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-kqRHVUhsXpOUyT/4irCvT4X7omeq1kR0SnacLkAErIk1T951ufyzwGZeZUR0UjlHhpEOOi7hoIfDISveyXcQ3w==", "signatures": [{"sig": "MEYCIQDvzwNtlGgjTTDXI7pn6lM/SfIQPE1DzzWEldFKJKo0SAIhAJeD6fhcqQcQONHF1qbAfBPFfkVKmt9AP54zH9G3OBw3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.52_1530838764331_0.18705554149524461", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0f559913abfa18239ca4e08f73eec36c5e57b81f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-Laqs9gb/pkNVCZBugOYRhz8qaxz+XMz9CjKpF9eMrET1FToh0I2lp4yRKUHc4VPHd+oolkWvN55Hi28Ia3QOHQ==", "signatures": [{"sig": "MEYCIQCz52a14drjMQxHhbf8VJKvzO8Ggi704hoG0qLT6+TDawIhAKJMPWdQCXvE0qGKsHliR3POhPtvqeCxrK9OApuESjY7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.53_1531316414435_0.3830936954748698", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4b8f4fb349902a800679191f59d0fa53fca49400", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-Vidv75YndRD2VCgASPPCXXMUNucPfzyrfv1hq0QthXfJtER+LDz6Q3UyiDbjbRacpneK/VW0dLBylaUgzL7EMA==", "signatures": [{"sig": "MEUCIAHRePBo0vj0LqGAcL+NuNaUZNnTRHnUt43BLcfoTOdoAiEAzgjtrNtBANuOHEQcyq1yXLOlpgWS9y3Ru7TFJFxGMTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.54_1531764005460_0.16472398590670623", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d1300c60703d5b5205f65ea178b7b5715d0b9687", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-/VTrLLtA5oO99rMHjwACuhM1TwJN9zdoX0qzOJnxenA2/IXt1GRi6sShoWdjpDHKtUNkf08PHa+D15M0i+KHlw==", "signatures": [{"sig": "MEUCIQDo4hmHL1Sr/E1guBecZSJcAtNb7no0rep6lE1lX3GvcwIgMAOeEq8Kg/ghdHu5fSu6Gr6Z0z4wZrUDh+n4cdm2uEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.55_1532815634765_0.7473723002953903", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8217b5a3c3ef078009e64989b0c6bdb3a5ad9960", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-hnMT8itOVeLuLGtuE+SBpaCyLj97nq2LJwu2Ud6O6Nag1iswDp2MMgTYmFPzPF465LuP4cUp5bmjZcOvFkkoHQ==", "signatures": [{"sig": "MEQCIH7qBjKuco5Ts8IATk0nCkSBsE7UojYSd+acQQubqYM0AiADixVPRWLWQI9okfNXCigO8BDZ9vzvWNJkPPyVct8LnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvdCRA9TVsSAnZWagAAwBcQAJLF6bByGqUDi/PQaPRx\nk5j2uwGq8Em1lJ/aTKtCzh8JtQFWEGxoUmscLg1a3hCz/VWaEu0RJ+KsTxKS\nY6sISaXJrC9LlXoOoPwlCmnxWDWGG62PJaQymfD4XGbhQZARr8wl4TTJSJGn\n/9rRVhn0QXm9SCvYGB71iFssIvrCI3gBGYR+Ebyt08OBprzUxN9KDedtEqmV\niNRBy86P35A73ssPj8xgAUPFQrNG+7Edhn4ioJoZcroxKIi0M3RQ5hFCmFG2\n7HVz7u6P0ZpPVWoOhhNb5NyUPiWyfS7dFlyElMSFUMPzHZh5eElfH+OlXmSi\nkmRjZI7R/tOpLfFDwENqDlvb0VREX5rpPIqMIxYVfozYVqSEfgi4sqtY6j6j\noUIJp98oZPKZLn/FXI6Sy3PM7DFWK0aV/DTlkmTA2eSO0VERVPFjCJ6JqLMH\nbFwh3nWevz/pAwUQElyxJ1hiaZlf3XczQVW2FRdtocgyNOuliTt3ZI75uiJJ\n+7QBmzPEG8/uLRf2SfJ7DGJCIaXOdAeGmfr0pb09AglpJzt17XgC3kAMH0VC\n1/0vDj0nYnY/Vt+/bn1YOlQwTSa5Y3JRIdjWYcarrFSuigV+he/Rb3RHkWN8\nnfDwkuRpt1zDIrDEiWITaQ/MVRQ8W4+CVzOvJmeKeBN7oNGzqpRMmVb+PH03\nbRZ0\r\n=xNsc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-beta.56_1533344733277_0.88692270645754", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a8c7522be5d4a49aee298a3f5ff34bdbf540615a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-4dz3aNTPiVGGt+wMtR5WlD/jBVYUmnV9aBYPLCX8r0nlGav2PnIkcoHyRvEuxKb/Pezc/pZvXm25mCtR395ySg==", "signatures": [{"sig": "MEYCIQCY6U9IAslSM9w/IfE/ckelWCkI0yM3XO4P6s+Mz9XiNgIhANdi9zpoXoaEmfaUxzJuscDWhNKvH14fvhHgQ1hlH+SB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSVCRA9TVsSAnZWagAA92UP+wdUCm6Taw2CUxa07WuA\ndgv4PEULeuRmD+0oBIOu3bxC11KDSTKHPvI0skUhAmiOUvWlKroFmXkLgUlq\n0u8Dwo+8eN5MnpL4WwFIJRz6AqqZq2fVTmT/C5JpyJyVpBbSu1vRjw7v2O8k\np0nx6w2UWSyE7L36Y/7ip08t4XiU5p22tBlNxETqS35yxleK5l0LoL4scxrE\nQUWCvb6Gq69Ie8JhdQ+J3tjL8Hxof9OK7tko415Yc6mgapgSloqwMbNMDCVQ\nXp9mGziVd9VQ/KgBbujPvf2oFjAD4MWrCki12TWFgW7hNwhs2Gm2cpEjq26m\nuxrT1T1p6cQq/GH49eBJXc3C5GRivaShWduDlPcvw6rT2eRNK42vPmYP33H/\nE1XSAbEtRn+1SMcuS1K8OqKCnBPXq6xddUv5RyzXi+D3ZpXuWFEqENrMV0pd\noDhclS5yUX0fA/gQghvmhc5yDiSbMi846m5Pd031c3q165j4sCCvE1pEkmjX\nT9WjB7nXcVJBIgBpJN5KzcJHVps0IQiax5nP5ZaHFLPLgUMxHjJkVYRsNL5E\nzw9SN2pTAJyEZephu7mhuXG6P3WPai26cs4d5iXuOBK40zOcXPj8qrdkE+D8\nrgxcttPmPd7n9/ERUC30FKW+y6KbSVosrFt+VBtz1S8/GwAMw6kX+HW82bGb\nA3j0\r\n=mmCz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-rc.0_1533830292477_0.08855013346995388", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "59d0c76877720446f83f1fbbad7c33670c5b19b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-cWyoUi1izJk5JbWFG07GZrZyZgG+DW4axPKI0MA+lSAxjP8VZwFUhJyjT7R4bGN81KTVv1aprKclQnKxN2R0Lw==", "signatures": [{"sig": "MEQCIEeghqSlERH3dQDBs4y/HqlnmBpnHKSWn2AWEMv/cwCJAiATJTsGREde3CvFrmNrbJLt5UGQam7fnTLE36sfLIWqmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8XCRA9TVsSAnZWagAAi9oQAIbS85+dCV4RhsJ1Ruks\niWh7PnknzgLGH//ZmFqvDamqwwUhCMcsqgosgaF2TUISD7Es/8Z9yATOe4pQ\nLeX/fAa9gH6CYUsL6LiO2ZZPghFBJrH/t52No33Axr3nhAAEBZEiorgOd+2H\n+HnklWVsIxznospvBfDf4tf1ABuyEklK89W48+jlYM+U1/bJFsfW0QfiFyiE\nrkibNn2HAWfOcCLMMcwvXCe/JkgiAmDwfS0OuUHzR/JxKoLRHtuxdPvHNEGD\nMBkx+GPQlZK9zKEgt5YfqUOyEe/FwjQQBEwFE9GodpO3kQMaKnH9NWE45eFg\nAx+LHd7D3S4zcu2GZL05B9XzlyxDiPdF+yTGiJTyVh11WVKMEVXjHXgL/6Bl\nDlduPbK+07V8KNaiCyOMxkCIyCoWKGktpvW8b9uJzdZ4mFmnuHyUFad01N30\nJsdS7PwXLVjqXzi6VOH/W5saIAWXfOWrB/flv85ZoZ5pp9bBNQlVsGOAhQa6\nwONf9QRkfs2SmcR63ncKZNdYqyPANB1i9OqFq53TzIOifFhwACTyXUk4FFUx\nou5MvYPdaHL490ATnWlxCpj8j3oTR4Ttly4BKnJawjaWJo5+mUVpfzpXk9yw\n6yl+eVGM5sTrUtzzLei6LTq4k3j1bhBzdr89gAqHEK3+pVDd4yPscOea8rWS\nEQqf\r\n=g1z4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-rc.1_1533845271168_0.5280630033684248", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d60eeb2ff7ed31b9e691c880a97dc2e8f7b0dd95", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-CeEHFlCgeZAb57buALjNFmOYgfblKuQBK2ti09nk2PkhJE3+uf1LbCrLra9sU90lOl474AinQqwkmYyogmkjkw==", "signatures": [{"sig": "MEUCIBq/gfaR6F05ECfSxOMp4lt9H67dOmtq5cZ7oNz9fXCtAiEAnevdUP4p64u60a50Z6vZbLHugeeIbqFONxdNdsBcawU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbECRA9TVsSAnZWagAABxUP/iR8d3G8a7XKUmq0bp/o\nQqtmKFqDA1g19ohoFBGSah9cONOfmpQkIajIKZbl21EAiR4+UFcgpk5cqGJY\n5Wzu4SnPmrJz0yBocIQajAR7ByqQ+JzQaQ4Keudw7nuKb7AgCxisjXWnJqeA\nu3xvHdNUDaTVwcNxFG9F0NZ+D7MvQfN6lf7ICOPabtcN4EMvPCEVtEruvC3p\nyZuk6OK8tBeDsMW9Ral7N4kG+gtgs7aPt2eQJVpNw+9jIn1YtT6C5CIPhbhS\nzQxnET0kgZg+nu6FNS/VLndESxkAh/UQ85gJLz4x9lPnqcZ2dyqDm+fIN1yE\ndOXkewQxSXCGEQ7Th7+x9A1aUzD5Akg/LYwSQKME/i4P/aNxEm61xtFhKaKq\n+3r65gGUbWiJkiTGRTzjWMueX0TetVD92Yhu/zUwbIo+Zz5OpTfbZUlzOPxG\nRrCJxN+gMaLE5LakDmarSoDBwTU8N+vCBOlyT5o642cI2IF53mLQPKeVIz1N\n95WNthqphdRzDpgEvyqoGsDZdDtzjTBhA1TOXiiBT0mSwwdxJz7S+zV5apK+\nA8M9LXv4TuNq+KStcBMP1HMpvMbTgWBOc/oC/m9UJXvuDo51MTeLYbM6YPAK\n+xVBkxmeejJ/KTzKRtZ/YzX0Q4HAkMIuJJosg8+hnkiM+U1yKt+UVsm4xO2I\nTTYs\r\n=afUs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-rc.2_1534879427967_0.9563872864979555", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "39bc3d271d392d8b2c34a9a9dab66f4b68641c3f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-okl7LEV4JMZfJMOO6gCOFDMonr5EB+jM64GddAf6ZZoOrETTNlXbD4a/uyvnN3ffG4Un+dB0yeTbsrT5OqgNUQ==", "signatures": [{"sig": "MEUCIQCUl4icAxHHutBC9TjVyEtQh9EfOMrOkY5YRCKod8npXgIgBQmTCq1qNxaaUQZWsCjHfdD+XDH+Pc5cgGhScmn3/lE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEl6CRA9TVsSAnZWagAA0DsP/j+nxfpTKs+PwAL1pS0o\ntWaTZTaz+wb2M5BgMYogWWBuThgGpl8g9RO03oQ9s1YPp6l4TNWEWEs1PyWW\ndoiVrc05KtC3AHoEah2TPBsCxptWSjyTTXbfTSmUnu6w+izXydrJcC/fIZUQ\nBmZEUVSV1CzMtARuyYJOFSYyCTWSUJHsUO8o7JIlkhMJHYmbH8YQxPf+nQwg\nL7hn65SDWWL9JKoJAb/F1uzzaFuMxFjHZPpUPVL9WBu50sg2F7lUUY68FzzU\ny+XTqO+TJ0/ehMIssuqET06UrHAjUDEDEdY9lznOujyVPcG37fh4R9qWmM7B\nVcIpZygQbAXh7tpop30opELqCe59JyGpBC+0Qq3OoFVeknIrYMBsRGdK0w4W\ndsqQn80S2GsXJPQMzsZILIxU/l1deaj0zYHeWPZMJ8HVYUHuq9Hv7S3vtC/u\nm3e1Btc3DRooE2H74A6DL4vNnH93R8YPehIzb6LJivJHZ4RFDOz9rlu2pHLK\nig1bXLElOVWD/fpLhCTJC1yV5F0WXsbcwcMrDlnwLbuw44CIDsnBbJlWtmhq\nh1BxLl2oS8qPDsTmjdHrbE2KT0yRsW3VknJEysTtZd8yXT7b08k2Zn0ZKcd8\nK13BCmMFmMV0rcYnH7Y3jRUSIHvV7mCLriNdio6pO9dHqV4Y9Plm+ZZUbEep\nzYBj\r\n=XR/x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-rc.3_1535134073954_0.8039192918022355", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "225b39197284129a0ae887a1b09e739c98a48ccb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-6ut6DPDIbAYZ1OZUzLzharnu0LB+ISJVeaj98o0JO/SkX74MZfGefIQJDv3LusHX1WbnwQJOyBENzx4cxpfcoA==", "signatures": [{"sig": "MEUCICPJm6Rn6KKmu3JdRDfRvW4regCoQeXrNMr8pUUlvObwAiEApzN3ONdHexQly8G67uj8FwyQfB4yrMA29vgVGChLDm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpVCRA9TVsSAnZWagAAAKIQAJMMhwhroqv2qGF5hRis\nTiK5FhVnxsBPzEcuYZCU7qCyDybzH0oI60i4NmXuRzEQ6D2TPOrT4xl43ezx\nJbvCCAzeI4k/EGD2sbiY8YBZw3azJOS3NNPBHPydKgs86mZjmQ1NonOQU7Nf\nEISvkB/dYXMUksOHdFXMkPJtsaCcpRe3LeMwURGPTFQ1Tblq1Tjo6xlnzNxT\nRmhrUHrKY8egpSl1rk3nqlvEnw+6IT2i/WlNQ1xtDsC0kSsjMbU5MC6gRS5g\nlToVl8wojntlhSBFVppDYJoYS8yPvAW4ZTUbwZUEsQM7A9X0ycwvSLlJt0a4\nFSvMtsIerjAJpzW8sDhU0KucRQWOjh5ZIj24AQ6ZviCJ+hApZYi9L/Wl2Xsk\nHWodHocfWoFA4ni51gogLTIhbON2XOBRaka9oNDN+vkWwqV0rx5K91xaIKvW\nB6lrcM/J8YOXebPeIA7e/20Bx6JPpPn5aKutRm8Y856UYy9rNLS7b92Nmiz8\n+RpSQAHVWpg+HiIWvCyXIADlPmDwMQlQkVDc2HQ2Zm/c3EaI6wIQMDFFsvTK\n8UmobuF9aLFOsKbG5M0gmlStlW3TawC1jxPVXeZLWFybM+Dat/c1CEXRLmNb\n8gTNeulBda2VmV6MR69GLRU64mTYHBfatVwSGNrfNQAa1iX4hZFtOsLqXPw7\nSj56\r\n=Sb3V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0-rc.4_1535388244817_0.012280901659380783", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a0601e580991e7cace080e4cf919cfd58da74e86", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-w2vfPkMqRkdxx+C71ATLJG30PpwtTpW7DDdLqYt2acXU7YjztzeWW2Jk1T6hKqCLYCcEA5UQM/+xTAm+QCSnuQ==", "signatures": [{"sig": "MEQCIEvVUQcfFoWtyiejLrMoE9ZNMLHYBIpIJlRV4DmyWbYWAiAxAkcbs9tNfzBu+unqk9uXcbpSsz+2w7OSF87S4RQq4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBnCRA9TVsSAnZWagAAL2kP/2SzoYcHAANLvRl/XBqa\nNJs8gAymKYFRHLijTyvMCQKugLE5hUVKvpwbQI4pZcytCkMU+PARNsye1LC4\nqqKAuJONXSoXN/HoJ5Xq9L+jBAWZdgX0VJo+cV7qz6eweTRcbfUEDOc8DHU4\nWPukOdmwDT4SKqhFs1qOtV7iorLP/c2AgQTv0uwJSanuVJlCknC6HcASs88k\nir38/jbTvLMrVtNE855rfA5g/m0dw889aIC1x31oAKiSLg7o+qowEj7MSepW\n/gF43N7skTYpo2QT1EOKUGafmMu8UA4dEqPqycrrvMQSyNPwEmo7zQf+FFcv\nM1xmrFUpWgXzueTzHkZhUrfRh9wuk8nV2HiHlaAn97hJBVYRM9epXdrgX4Ku\nGKeE15vUF3MHDnX6TNpAEaqp6IA2Yw6Q1F+orhj49k7GE3QgPYE4ZM+YhdbS\nVSx8KUnG5oF3PKxOTj6U29LR7i9IDztJpxxmJNqNQlB65mzDB1DeYD6RJSyN\nJ5CWci9qHtRqFcQSjisHdGMTDyOQ2bXlJ3LFbdq+reXJhMzYQunfcU4ItIV9\n0F1kinAGwcO2YSARQlUcW9RpH+nbdznwjacnuUftn0dAJcYzlQQiIJE/2TIz\nE0FMSw5CiJ9aJHw8yAJl4ZfU5sz1LgeOu8BCc2GN7lH/N1FRySzfP51BGF+O\nXah2\r\n=whHp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.0.0_1535406182590_0.6598574842029534", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d952c4930f312a4dbfff18f0b2914e60c35530b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-q+yuxW4DsTjNceUiTzK0L+AfQ0zD9rWaTLiUqHA8p0gxx7lu1EylenfzjeIWNkPy6e/0VG/Wjw9uf9LueQwLOw==", "signatures": [{"sig": "MEUCIHL01JCG6z00HChFc0wZx02aAQIVUAJpC1KpD0urXz0PAiEAljb5CURtCK+IJBSDKQlkp2arP3uNPSfb/+h/UdXUa8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1/CRA9TVsSAnZWagAAH5MQAJTRIMy4THuocRvVb7z8\nQBfDE0w2PuIQl9igiESFiZTH4o2rM1E/GzzrW5871d6eXQoXPWphgH1Q1+Lg\n3CQJhVOJEP48koBkqVyWWuW5BN8TmOWETpNXdGvQ+XyTaoDQXdnKOhhmLpsG\nuW5GD0BzRwEr+geP5N8zadOVtSNNr7A0L2JKz73VWKHDtvdeUVcmH60OaqXH\nvSkyhf+xmRkQxN+C2dYakFayo3gdLea9Om342THafPDmbUHoH5fpiNu4R58K\na073K+eLsheRuNgYwcTHJXGq69JySh5fKm/rzUyIR5LDU1xlvhx8GFjXg7ZE\nzou4mOiecPgGy/DDtG2CH/CiSq+cLgrmAIlC2SZwmYtvMozZZqc1M4xcd/vI\nbbv+f3DFhK6eFWDlKq7PAvoV8Uu1hNflmHtAhLk9SEmbrNDewCzE9fAtzbqK\nZVKQxpZMWyO7gki3GlASNLif9vsPj+VuBX4NVKfxoRjCiKD+PkFmz9FbY1xd\n+4gpMiBq8ujqPo1JUSDsSupLE5zLYHkOXeWDTw85a4vLUS//aC2q3BQEEfVh\nkSkDwDDp8vaHd0DAHOnN6Pq2IoiCD9va/epKguckjSSv9yHSHeO6k6Dd8oP3\nMbHPpMhA+rvwH0HTX8qPMUvqW0IkxG5oyzRv9Nt8Gr/cNpU+zMTREg5bvzRR\nzAKy\r\n=jmQY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.2.0_1543863678325_0.19010292215302882", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c5dbf5106bf84cdf691222c0974c12b1df931853", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.5.0.tgz", "fileCount": 4, "integrity": "sha512-igcziksHizyQPlX9gfSjHkE2wmoCH3evvD2qR5w29/Dk0SMKE/eOI7f1HhBdNhR/zxJDqrgpoDTq5YSLH/XMsQ==", "signatures": [{"sig": "MEUCIQCQ5IQykJgX1rrXU2iiSQ3dkYxLY4aWu3WwxJiZrVLpMgIgLGKZU9ydgDH40SSqzmEZBqW8xFuuEYUnBbfu67HQoqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffZCRA9TVsSAnZWagAAEUcP/2Kdtlu9hm0GhNgixSov\n1xvWX3mQ/3NBlk8OYiYe4Ffqg7WcTzon015qzHUMfIaaMPVZ7MVBBOTLQuxE\nSWAdXsnIBbcsLoDNJYRuB6IDkKXdMBpXihMoSKdnmHhensRoaoOAPSLiqRD0\nBMCVGmwbt6uS8a36YaQjSjtXMR/NRW3XtSWqFmkn+RyJnGTtxS3RN6UnlGL5\nYcd6NRStRuCQ8mDbwwB97oVJGdwHCLtDYdSCNuDZvT+3QbbChkpCoSBxDQXL\n8kmglsaXb/ayQUfBlnCL/v5R6TckNbAYY0i4FAgZTlcPmqIfO5m1ZYsgHgbi\nwgw1HrSLDxQh2TlHTcnq2uRbrZoBoFYe0sW2KFiUKO4bXUGXC0JSBgcQLr0D\n7iDUuhwjcF0HMyHruhfJQ9sNn1gYBsRXu1kLus0ECe1HbLZScs3z8evEKDBZ\nONK7iR17sbNKgSkJg2kHwkZtsZuk3JsOnlXQaPSSFM/DbXSGkHBi0PJs87a9\nDnOTmcq9ufAtwJ+rX/BSsGfJClde0mKUME6pCH2T3twtZykipotXxOiWbSl0\nIkqc0ICW+7tgml5P8kSrbbUg34Re9DteQ1DBWhcZB756ti/X/dlh8enrc0H/\nwdGxK/1GOPl0XmPxTyqfv8puUvsvU/B/BYdcDPLyjWGFDm+LUssg7+MsWhJ0\nyP8A\r\n=ahhD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.5.0_1562245080492_0.06324628345998895", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3d21731a42e3f598a73835299dd0169c3b90ac91", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-g1y4/G6xGWMD85Tlft5XedGaZBCIVN+/P0bs6eabmcPP9egFleMAo65OOjlhcz1njpwagyY3t0nsQC9oTFegJA==", "signatures": [{"sig": "MEQCIHIeGp/7A7s7DeMR06yOkoal6fzMPi348hqVKgtOqVHTAiASkeAMJyr42WfFwAOTIYjuVw///xDMEjbLElU16RWfPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/4CRA9TVsSAnZWagAAf+oP/i1o7Hb5v3JoW75phBQY\nkCy3pMoXq1DYIt7SNWE45cve4/cH+HnfSZjS4yXFl5LvSjea4o8VZHd4VnXk\n5n4DWSK3eJ0kkwTCFLlp1iz3QTDYokiUAxW4uBuCT4ld7ubdYHuLlX21ZyEV\nP1uPB1JWEcylIoQ13FQSzvX8talO+QmX8umXmto2JqWhslcAunaXKrjPPzMR\nxfvoUT5Dg/JL7FgR/2s+wYTn+o/w/DiWM/ZS76wBt4TnG6rK/UGXgbjdGx3Z\n9i6VFRuzpA1HTqWN4Vl5TQ9bf81vtxzathBqbkCbG28VRvSK8pyk5Pjvzwo8\nN1/EOyPMvYg/dYtjgrnWW3HFir9lGv54zLyHAQIWiNyE8cBP5HfEa4r3Nxsa\nhpo5Ih6dkx7N9VpYlyN8ESgJ5UhwLFXwB9PC6pKQmlI7PWnfGU8cfVEYy85Y\nqvt/r82Ck4bxtynWM+p5DE5d9Z+iXHUJvemRIHBsd5Phvge7biK1NDupRemm\n1TBa5jqTsgg2CsXY/ZBr6aivKtMyHW0RpDJI8xXV5g0aLkV8CGgLmktN4oRt\n2RYLRZpLLZiIG9uGFLng/17ykHOuX34Z0z7rrGC9EedgPfdQVnHXF6WB8Q4H\nplmIcpBDAOdrdlQjtKiz8s7Lhgl5nB3rR/BbSC+INHajXJyjYElXUGoLUvaS\nAvDH\r\n=mf9/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.7.4_1574465528325_0.9513737879323658", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "913b3fdb5cbd35e3208b017dac5ef335ef6b0d65", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-REtYWvpP4TDw4oVeP01vQJcAeewjgk8/i7tPFP11vUjvarUGGyxJLeq79WEnIdnKPQJirZaoDRT4kEWEdSWkDw==", "signatures": [{"sig": "MEUCIQDj1ygRzP8dvYY9M/kHh1Js9bbdPIjUYHPRbiVlIsEMIQIgCfo+EV0LsK40wRTkSxJxMIR2aOj/qmEthtewS81YFT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVcCRA9TVsSAnZWagAAyCwP/R04eCvOn5hmLApG9ydu\nZg0aFu/n20dV7bjtGDhnGd4XPCAoIqtkIorwuMtUpVz6wZqk5ESiY9cwyPfa\nLjENb0CaeQZQWH2jmYsAdhSI9SDSDatYZ+CqhHSe8jo3omdXChi1csuN1ZkU\n97GLxbsHRhDDBeClTnyYnQrdIRCoeIsWLuMotcrhU1hQUpsriGJO0bIkLk0x\nBV/ztDe+PH7NhJQRsUZVGm/Z7/9OT39OqJ42y2x3esvB2FDSFRb9WiggKQLf\n+sUvWuoIfFiJxoFTroa8UAjFMh+Jibino6dCFcAJqy+Gof8ZGfvuvSC85Taz\n3vdIDlmSdP8e0GVC58OgKxSjngyZMYwFoKIH0QPZR+GLbun9gLMbpD0+unO/\npdF9QCW9mZsy84GmZFp2rkU+k4iHK/9vT+AVZ5M+AWFRXfRU0vrfQtBbFkf1\nPiZYfJGuToY95n/z5BzXcOnlCZps16zA2Y4RcMmIbIS9OvU/ieX2uuYoQsO1\nntT6znFG1as9SwntsZK0iCGh68LuUz0DagBGBAPEEsYFuFQze1AksglwT458\n5qxuPjC1xxOwf5xrreoJMSKKnwALSxHxcHsWG47ppjYAEA32C/cKYW7JqYJD\nxVqhWdxjE6LsgbTOMsmvCE+Q0CnTzm2wmoMPx/zR3wh6sarDlFTBRS1wU+37\nlUwU\r\n=M2y0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.8.0_1578788188378_0.0030642469654982296", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "8d12df309aa537f272899c565ea1768e286e21f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-s8dHiBUbcbSgipS4SMFuWGqCvyge5V2ZeAWzR6INTVC3Ltjig/Vw1G2Gztv0vU/hRG9X8IvKvYdoksnUfgXOEQ==", "signatures": [{"sig": "MEUCIQD1R9ZPjvd6YPCzkFnnYkjiyWqJc+iysEsfzmdXzWCD7gIgH2Vy05bfOQQijx0YsWvQEakAvPAfshn+5xV6wFP2Zp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQJCRA9TVsSAnZWagAAvfkP/RApZCCU4spBYws+qtx5\n8URxmQoP9eMZR5gQryvmU3kxB2Li9SZH08lzF+fHRxG5N0dFNL1WxdJ8uaej\n0uq6Zufh53Cd1gdRowJ6Aq6fKRaqNLjETe9EoA69nSjtrfNNZaLgmgt1Du2f\nqcvpP+282+ohck4gJb5obkG4h/VIUwU0R1tTpCUBtteokt+fwsrSPQ+IS5lz\n28zlkPwTp551+BpCKAtAx4j/Qtdznx31on3bkvg38pp5H2gnKeK2uBQx+9O5\nrK+oxpyqWkexPAXJKfQvCUhiPOnTzRZblel3fK9BAXDs/S122FKFUkEFWT4z\nP5j4b5DLIOIenb2cLcip45OLqzH8l21faxCKWBRVuZEAiwPg8y1ExoRJDh4/\nyUWWI44/eEUXotzWeI/xzxrnlyns5NIRMDJ/bkE9ITyA7IdEiGWzVEAKiPww\nY/r1nMO49+Mag8f/JZKAHqunZwkX3Sx3la/bc/Rm6mwNd85SdaFKveHYYtuq\n5hbJWLjuiblfAOV/kq0zwHXWMKUlndeOr3j6XGxYCbJAG5oJqRz8ppLtCp8J\n+d5tEYxNDn5VID5wvUasLP0+0xH+ZQ+9Qn0Wa1yaoB8ufrwHZn79AHPOnYPA\npNwZUDQIsEYW5jPt7ukwnaRONkV7v741M8NfRNLHa9tQJgR02lSAZ9/iWZQZ\nIFKI\r\n=AvN3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-duplicate-keys", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.8.3_1578951689100_0.1898147652243507", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c900a793beb096bc9d4d0a9d0cde19518ffc83b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-wIEpkX4QvX8Mo9W6XF3EdGttrIPZWozHfEaDTU0WJD/TDnXMvdDh30mzUl/9qWhnf7naicYartcEfUghTCSNpA==", "signatures": [{"sig": "MEYCIQC9ywxgujgzpxtoUIEFFGvuogVMzxN8z/OCXwuRTLqTHQIhAJObKhq1rjlKqcrsHMW4Zt7V8zCtGC7BvFO42Jm74x1e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSaCRA9TVsSAnZWagAA12sP+QGEOpuxRSceetUuEe8l\nDiRr4OQTDTDeXEmxeJC4XnVLulnzs0lXVJGuLeR2ju56h2eJkrpLdIwJXBuo\nlbp5j1zkgymtASJUkwJ6lIhu2LyMvrz7XM8NIPtqmQnzahYa0u+2r3DNhQSO\nS5a5DZv/qKFVMizNnTvBvXPuLrmiiiFmKtor0Y4SgnAR+Cc3W68oCloqcZzk\nMmkE1Ys3LQFq7qwsee6yiVrg4mOG9Be+/RcxaJaB77ig7toQOI4kZZWVHlE0\nZ3wjpS3vRZlP7Yc50ixSqYUtrJjT868ANW2YEHKuxfWQCVRCijZKxpqR6rzk\n/+xDj37B2wrYQKDvcCsGZEbg/xdnIxoZs0LR+/ATutxKGm2ogPhM2cDRjYTh\nvIz/y8jz0SeHRuIlfOS6Dv9FjIZUbJDKvLxhGbkb5MIb5Ix+GRDM+lNcSqHh\nG7Iqt6WDPrv3EuIXinNOI3NN7SJ5jDggkd6n1SQkkx7owB/iGPNuhPEmeJgC\nT00ZJgNJUTtyW27sBcTEMXxcNGy/ueAtQh78osjGQjf2tIfvUPzS4Hf8cVLQ\nW0qhNAThGv50NTFWLeIXqm65LboJavmYazELrFU4qC80+1avtsgVsPTCP0oV\n9Ag8We1jOIF0EYO0WhcY3pMHZoM1t5cQaeT34Zodg977HkQXc8Ku+enl4lk3\nR6Ya\r\n=n16M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.10.1_1590617242154_0.17273438240959038", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "697e50c9fee14380fe843d1f306b295617431e47", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-GL0/fJnmgMclHiBTTWXNlYjYsA7rDrtsazHG6mglaGSTh0KsrW04qml+Bbz9FL0LcJIRwBWL5ZqlNHKTkU3xAA==", "signatures": [{"sig": "MEUCIQDz1I1oQjbiUMg05DlJiuRpL2KCmg4cDMimvdvjs18B/gIgK/enNeDJnctimNU71jO0d0JPAtMpn5mFSnhsnON6WFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zogCRA9TVsSAnZWagAA7E8P/jpKDYK6/XPlcSSnuh7+\n0MXwikdLNF+j89T+cX1YijV4KkR7cy+lzWF1/HXcSS0jz/k7PIuNwYr2rJKB\n1mWIcX8amU+GookIUDtJn+2cN900vQbh1UXtenxkklrLO6lIHForqo+NXXyC\ngdmNUxgGEVgObWPm14kBnexmRV1rti3+3ev2OFz+yCw1MI4tYzE/mwm2a/K7\nZtr7VbI3DvO17VJ9A9pnTuy1XsA4yNecDZLS24tyFrV5wbDD6t516EtZxGr7\nYmBMDbZxzcnxq9YjACIu3m07y3UU75DdLiKBN/SGxhRY1wTzc1JFYtJRuq5Z\nddbkosCO1iDJh8B4ltiGJST4eghGQ1gU/AwGfWfPt41ja7Y9hipOiuBAgMTd\n35Zr9szTkr6G8yCAz+X+40cuMaq3nFpYESi5ohxmlRhR31VNC8YcGZgd9tFI\nd5o01/Iv9koP7/rk+7Lp8US8r887EPicIjc1aDqrE8IoWsVR0uMMbiELv/ig\n9n5K22mgTOFPfVyUh8+T+G7LHmZZES9wQvZLH2oHlkqZ6LpMgFjVvB09UGhB\ngxDy7P25HJQJwXuOpALh1eNpeNr3aKXZTfjFI7izVDWL8ouDUi0JP1W1JqwB\nIbda9hObh4JugL8rKgDdBBcIEhTMCQ6CAcTJ2DXFgXd88HhDc2Pm4AH09OPG\n0GAU\r\n=dBWl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.10.4_1593522720090_0.9360807548116381", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "745661baba295ac06e686822797a69fbaa2ca228", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-iRght0T0HztAb/CazveUpUQrZY+aGKKaWXMJ4uf9YJtqxSUe09j3wteztCUDRHs+SRAL7yMuFqUsLoAKKzgXjw==", "signatures": [{"sig": "MEQCIB5xmI3cLZOhBEq4qYPKaPg+j2hOHBWSMxwZjG8df5ByAiAYlFcIRvn5X+b18oiWy4xWWC7yI7xRcqcOOZupeU7Tsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/ECRA9TVsSAnZWagAAInEP/0WhoI2HGDtLlJ8kRb1Q\nHAyAr+ri02dD+ZKOykTmoUNFcnc0KaQeBK6tyqgMOETVRc2n735MTrkXDBrF\ndklY3z510EWYYupyeYOsKsoetxFHfQwBzvmfR9k5DxVpc4+xUberpHi/rV71\nd0Lmv66QT7YRDAjnoeDnQ8JIzLlttIJ82r8kSRYSLvC2JG9p2P+nP6ieFmVu\n7sOgvn/6Smc94TmUwy6N1G2AnddSOVS3fVYhO0D0snnCf2A++lmVrzHlo/wl\nOQ7j2NY9M/Yx/AsSAx7D2VMZ9PEy9C9CymHKn/b74Mk6GlWwOjI2i4qOL3ua\nxn1zd1sDMBVqViMPDwLS78enHfaej9NX8U9FQdDeaEa0xxmVsSxQPHtd6aQg\noD+L6V/yLQlH6hC1Ccr/7WaU50WeH60vsmKUmozdg06cFNdCZmi+kuhtlcWd\n3LCqLOAjKKV03Z3gkf6dteHVaz6NAhYawJh3iie+HH3O5drGB322eV7n96Jl\n9joOvrS7C3OmPseC+Tr+NwH3pA8aS/ZFNZniXfIcaZ98A968FdJ7P/TgrvAO\nDkxykt8x6FfDVv7MyYdQJ26N1XHUNh61XBZCfb+Oc7LhTSbqmMp2evocrYIC\nHO2S8EOCeLZr4yZdegdCj/cFeoCDoIP2DoHQzUnJRZ3ey/H10XW+1gyLQWzq\nqRW9\r\n=nJCf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.12.1_1602801603744_0.9227251316181233", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "6f06b87a8b803fd928e54b81c258f0a0033904de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-NfADJiiHdhLBW3pulJlJI2NB0t4cci4WTZ8FtdIuNc2+8pslXdPtRRAEWqUY+m9kNOk2eRYbTAOipAxlrOcwwQ==", "signatures": [{"sig": "MEUCIQDcDgjJsbbobX3yXmaCkvaRv+A/axZ+tMDABnufnb5vYwIgMw0MLoWDq9xkcU2IvTZtbCvdOgI4Syu3XETxOgegOUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgaCRA9TVsSAnZWagAAXtUP/0YKns9ydCuZ48adYpkO\nZWwgI7VJCMbELP7Ggf8hjPen5zyzWNEKU+eRxGADSnbtWQohkpPzjb2QUkxr\npGeE9ycY9d242Kc23fGJe3Q5Z7bT1s6MvO5NBOHtp+FlupBtJqdSSpkQRIGd\nrDLL81rgJ6JcTiYSgNgMeWTQR3VqiMfp82DVlYgMz4NKoyAk7cWkiM3Ld1hq\n7ovaJWi6p/M5UOybb16xvr05lWRfaqsE6EfgxlsuPBHesESS0Zt6C2IoDtqm\nb3yjrNuAY1iPETJZv0bjASCoO/MqpfBQNjoeeGk5zq/rIPfv0jeb2ntlg2SI\nWYfZvGJmy10e4iwV9P2k8hehhfH6oGgRfjaLQD1wujcNGg1bFQD5MBxRZchD\ncxaNPioZIqrRLbpPXvHaQ1zffEbUt1mKU5rgU7BwiMahFa1cFp1jn5b+9aNb\n0myyk5aBvH+Ve6TtQSRNVVIfKgAFl55akhO1hh7rFnGdF4MTqsN3DRaPd+R/\n12IIj4+WRzUuqlhtlg69lRocYNs+IEeRfDZqV/PGhddrgZIDwSbsSVZO1CwA\nf7TbgHUMZh7l36pIOQ4F9NInwpaV+oX31/lZ5ycjpbwL0Re4i8bYQO+Y1VfB\nvKrRF25VpH3EGzDw2KkzexprPGDQ0Fg9vaHVGUSrrtzCFXHc0aggjByhH6jL\nFvG5\r\n=ocph\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.12.13_1612314650419_0.9962300721648429", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "365a4844881bdf1501e3a9f0270e7f0f91177954", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-iJjbI53huKbPDAsJ8EmVmvCKeeq21bAze4fu9GBQtSLqfvzj2oRuHVx4ZkDwEhg1htQ+5OBZh/Ab0XDf5iBZ7A==", "signatures": [{"sig": "MEQCIGDykhxCvNu4Z1E3tWWI1ZN8ffhoRPO6S4+iEhIJG7a1AiAFuDgEnc7ipCLbFIio6aYhfM0+8dPbYDgpe+VizGYwtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrACRA9TVsSAnZWagAAVH4P/RaJLfeqtrKnMSyrb1ZW\nquK4SHuW5r9gOVUOApBdAFul84qEdlRid8cc5jMjc3GZtKyVXz6wY89Fk3AU\nHXkrA5kTnr85zDh2y4FYiBbeINUDEIDqcQMcnZbNSYXQuxW2eIv+hiOGsKXJ\n0JdraFO64gCbWcbtp4HqgaIAFMbh9vVKS8NYn7MAeENr5v+X510cqrAS9pe8\ndNeBcL6UkYX0hujZYEkkPxxL7k9KIwWRyaYicdw5MyN7w4G92JxhtrXdh1wP\nJL3szZ0dOpYtk+aYbqUkmrZV4/IIVvXnBn8E4QebQdp3NdNDKqtoVlB1amLe\nSKced4ILgU8YZ7Os1EwVRv2rf63kEUhHzSIxiwkWerpuLn84CYktqt2W+n1Y\nhlkNS9u/Q/x5E7uLywr0ayzxkq5KJ+cQV3gsPkAgrlxV2CSxz9QmMr7qHinT\n1/hOo0dIZdmqVMjBgBop5v271Af+KdlHy/l7CyI1YXXcmrClYNBFzuBm0rOr\n2xrMKG71x4yKV2d3S6nRRYkw7N7+CAfg6NGC7w76gNXrfdSEb42mE6s5JiZ8\n5BIi+fG4+apfJIAtfsz1aWg4iR+D2PFLi7kj+c/vUWFg19rzTwuvs2qXoCgG\nhRgfPMOFHKWGAX/zr+fc6z2wN70RwaSZgLIleqhT0ZrhNcKi+K6hEJsRSR7N\n+IvR\r\n=sGXW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.14.5_1623280320527_0.7052654671990408", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "8bc2e21813e3e89e5e5bf3b60aa5fc458575a176", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-LIe2kcHKAZOJDNxujvmp6z3mfN6V9lJxubU4fJIGoQCkKe3Ec2OcbdlYP+vW++4MpxwG0d1wSDOJtQW5kLnkZQ==", "signatures": [{"sig": "MEUCIBJsXp92WXyn2vKNM2PS2T8Qr5geSbZFZFavo8KR2FReAiEAoY/ULACk+gg/Sz3Det8WLgZQIvM7PjQntoc+4MHqDVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4238}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.16.0_1635551249480_0.5296748170203012", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "2450f2742325412b746d7d005227f5e8973b512a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-81tijpDg2a6I1Yhj4aWY1l3O1J4Cg/Pd7LfvuaH2VVInAkXtzibz9+zSPdUM1WvuUi128ksstAP0hM5w48vQgg==", "signatures": [{"sig": "MEQCIGb5nLSMFxtg37hjtpFROSdy26wpS97PMyr5R4Rrk0dBAiA2glTKOFnadVzqM3cPpWQobxCAdYm5oWEgI4TQ/2vQWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j7CRA9TVsSAnZWagAAo6oP/3wc7wt4Bxr7sFwkAmEv\nwsRqcWxgzR+kzXbEJsDkFEHTsGgB5MID1P1wpADGiwZybVIQ0hLfd0JSkv2r\n3/mb1AU2Ci3ESui0jc6fyJwR8T8bBGPsxMRlU3BBmnim8PHMltHqc0RyX82+\ngKC0tLyYZi1aV/ausMttQyOVhtj2sNu3vbeUGgDA5G47KTEHm8yTv0gzrZ0c\nt62uTrnOPioYEgLTG8OE4aTBqe1QcKMAVVconBPR9plKL4krtwCiXnrQq2rW\nloIXAbqPWeHyspxvDizUBBb28amoxelw1BADaD5lC3Y0kAl/hF9a5Kd1SoAS\nRA6bhbdWEN4MNqqh9Plhe54vdxKqS1bryWMPAx36Ry7pJ/331Ur64lyGPr7V\nNAdbdQU5HS+x/vyCjcFL2i37Kw8AHjyWzL2tH3Wg5kcayl2EyE0i8vxQVyQf\n8qqQZ8AlCC/ArRLQJmljnHBL4oq2m/2MXB3CD8BvqtIxK7N9/l+FubpCoj4Q\nAFG4erZLlJzT/N5r8KWETqt9rw0XiALFFBtHuXVJ6QywQYlDmu/FG8tA8DFC\nXJaR4lVdkv4KFxIP8cAoyeiagMAuj7vNjUhVcal9bDftB/drlwjJGYagXeTj\nCOv/YHKDbHpbE6QMeL70ogzD5+VpwKi3Ss/klufy2RnZhsuEBjQphhqUz0yD\nxpxG\r\n=4lXt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.16.5_1639434491765_0.05341065159433378", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "2207e9ca8f82a0d36a5a67b6536e7ef8b08823c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-03DvpbRfvWIXyK0/6QiR1KMTWeT6OcQ7tbhjrXyFS02kjuX/mu5Bvnh5SDSWHxyawit2g5aWhKwI86EE7GUnTw==", "signatures": [{"sig": "MEQCICw9o4zzWzkuvPR0xCbuYEthQN+B+hftVlONxLe4q8lcAiBe13brA9tLT39EMvo0cntPyc6yxRUoC79XnI6fd28iCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0jCRA9TVsSAnZWagAAUnYQAItP8p4wUM5pgRrpMmBi\nCDUHpwtJa6uukxuQkLC5MXBTL9k4/wryA/YBdH+5u2ppPkvsLZt4DfZNmOTC\nJXWURw/2UOLTJa8FA4+aAq6yHWT/qj+jTULm81Kf9trJTXs0O3Mt5A1XBKGA\naZzjMeAU6KYxQEgz/lLseAkm4zo4G4Uar8J/d8x8gQRWVf7rAqzj5G9Hb/08\nTEUl2bpxLL+n7ojmK0oOVXLhAo/xJhm5OqoCJ6fMfTbwRAC8B5aUuoLgH8E6\nNbXRUCQVQaIcII//qVgGeKBPYY5SUFpw/DTv879pxVQyyMaP2vam6L2ziBgm\ndohdqgCBI1ivtfMP66ZiUSiljFB2IXKLUw5fhXgA0hhDOJvyTiq7JliPp9de\n3mMU73EyjWLF+g6MO9hgZgiFiE45B5ysmrwAyeG2wM7kkr+1W7W7WK6GOLcm\n4ZrecVs+eFBC47ALJb4JeQj3QgeihfsS2C/Koy5WWxgXqn9vgQ0nAoep3E/D\n4wD4VPGTe0FGL8LxuRUUmlA3Qo80zfGyiVzFmakn6p5AfFT082o/68RD2IBa\nd1BjRIvF6UbptRAiBHMaJG+NIUliIxrepoV27ePmelkbNSttRYIRfWyti8EM\nT4LMw0pK1C+mEHLKyD08wTP/8h9IE+/47bOT9Tj6NOZh8MXOtcLO8zp+E7Yg\n0a/u\r\n=GMwb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.16.7_1640910115672_0.5674869049972213", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "a09aa709a3310013f8e48e0e23bc7ace0f21477c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-EA5eYFUG6xeerdabina/xIoB95jJ17mAkR8ivx6ZSu9frKShBjpOGZPn511MTDTkiCO+zXnzNczvUM69YSf3Zw==", "signatures": [{"sig": "MEUCIQD14DoaisCNBf+6AwfcY2B1KfSzoqMZTnMJ46htNHqhPwIgcukyuX7fAt5Eh6VwLBStgDqMW8/00FDcuh9RZMN+SUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLeQ/8DOLX6jF2lZAyNJpVkSSjiHwWoQYbAmi8YK16QxObNsamRWkd\r\nKMoY4cXYVsjcUsL9KE74CulRotXChHnnxa4Rfz+En2SQeoVHzagMlqf2/RPc\r\nmnIfuuU8hv7otMAvhHUCaVcFicQDsaewlSalctLsM4WJtvJBheV3a/J+qsZf\r\nuiqoV9SH/djCbkub5/LSAiDFgcnKDSiEq9wZ/gh/6AEi6JYMJmhSfzwPT0/5\r\nKU1q2rN2FQWp+4FVJbWvgTq4bLA9n6ei+BZpyVcMUQu39bh9ftuiOmzkZgF8\r\nCWjISK6s9fg5X0AftYdJn2XtDz15gPCfDQsc7YjtD1XjrfTqH5Oyygavrazv\r\n+QOyyG3AgfiCC5i/ATlcbvEz2VYhckGF2GnPUeeCW3FH9ppb/t3NNL+6pWPL\r\nfXUvgqFNGEY9M0SXs/kcKdi817B3sa2ZAbt3vBJSQRkf6XAf8jzx/XR4groB\r\nSI1ozTmsFF22BZ68PtoIeTW+9ryf4XHwpe2W7qaBqQS7AOEOnPG9uUjy7860\r\nDSd4we3OkKfmJbvrR4BAsJe/WPBuzzKf1v05mtFjBYM5anhqGY9nZblYGqDL\r\nXDF95in4H2yB0qz8LNtmRIhmgd/VGc6bmrkv6jRRLRaX1JvSxCn8NF06gI6C\r\nBIVDJtcTthDLQyQ0d3yDKu1Rn9Hej3HHxKE=\r\n=75cA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.17.12_1652729570256_0.4881633062929802", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "e6c94e8cd3c9dd8a88144f7b78ae22975a7ff473", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-NJU26U/208+sxYszf82nmGYqVF9QN8py2HFTblPT9hbawi8+1C5a9JubODLTGFuT0qlkqVinmkwOD13s0sZktg==", "signatures": [{"sig": "MEYCIQDxoj9h257EGiHpVKkTNuup2ItC+bXpH4UM8cad6/b2fwIhAMq5s9PKVq25G1mF9ucLBGlp6kExeqz/V1AIKgjsEDgn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHFBAAiCDXWz7mVJ8ctLn1LhZA28narj59MiMi5ds1WmrrveZkwbJC\r\ntAIKIe/FUz6J6Z4qHPGKOCNsn4CvTCBLEQEzu582sbu/KUUf2wD/q7oo6MTT\r\nZSJTwr4OZ46c+CGTtNNnUtnnKHFX1JNfwicJYyNNKUJKwofvdLmQh1DI/sGU\r\niDy8iCfrRkqy1uI9nm5WZzI6BZwbQi7xo0IEuxKjyCeQUFYhzNDyREVCCA63\r\n8MeeHO9wCykiFL+m5EFUF7emN8y5diejfVuLyuk2qTATf6bgmtjUQBwAkdY6\r\n9uKibcn6Q/6gQN5PNYjCkwWbI0+Hr9u13sMMemn5UT1uS77OO1hKhixBCmOV\r\nRxjImacM5gbEjLAheGqgpWVLHAqpJAmlsCYCkXpNbFJTy42klcQJwQusmifs\r\nmVK7hvTK1cJgdazpoYUEik5bNzcEAoe/n1UQuvSeLO+VYqAu0EA4Dai3uYg0\r\n0r0a+XRf+znBZEfB3qPSCW3s4akQViJuS4TxiHJvDAJ1snyJB2VypiQI5XrC\r\nFcZJYfZt9criTez+DtuNrJLC0mCiTaea2+HFWlWDNQx1RQqYd8tFELtJoXq8\r\n6ssz4wlWrShteQewYrMOzAuMeXEFZ4hkxG1IAp16gSF6BW+SmrlAiAC7Niio\r\nc8EMPJcAyUPeXu58UuNy1V9jOXiyNsu1e8c=\r\n=GTbl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.18.6_1656359399176_0.6428248997337451", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "687f15ee3cdad6d85191eb2a372c4528eaa0ae0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==", "signatures": [{"sig": "MEYCIQCIKeBs8fF/pJU/iTAEgT3PRHtItEcfnZNpncjTi6cKJwIhAJvfQGkBt5oAsawULlGRKJT+B+f6UgB45SpvphsroN4E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbFQ/9Hnd88Wut2OPrsoz7zRJBuGTbr+dy0laOI/XhV1uxJsKr1wP0\r\nVGSl88vzoGu/WGxqwzCAuVyD2X5myepAmeFBiEtVIQ9fyHpNmPA0mnjZKTpd\r\nE6ut4iaV5IUldcsorMrJfsEWncl9A85cVWodvLcsuVKwABmYAflSjzr+Dvbt\r\n94PB65al4MOQ+Bs8cj6oGTOv4Rpu7U6fYchL27IZYIaFHEKaWmqIF9oLFdck\r\n1scRf6AcffKOMic7axIA8pucHt21H1CVZGlGudm2YAtSDMblM7KO2SolLkV0\r\n190MPMpDAXYnbowdwhZNhlSagHIuh5QF1AwLcq7YMIvjtRUlZ1pLrK0+0H00\r\nrr57N1Wb3seS1VIzY8piHzV8DcSJOLODNbWNuh1AxRGGiul+BQbh6l+4FLew\r\nSgSK8VAGzNP4+MCLKOEbYlY8o8eLMqUJc4KIS4z5zLIkrKQ6Rx1xE/Xlz5wm\r\nO41pwb8E2ZhbQOBStyjTeKnCwhnM4k3yDj1ULzFWQkc1612K9lKyTdOQq4qP\r\nErvaisCNTLE68dRh7DbaB2yJOnyX6h01TK4EBRTJ28tkcyK4NgFu28f9HRz0\r\n6zzYp/rUFeOfUMs36SjjpXME/J5ubs9WiazRQapcGwSQG6K4YUnovxvsdmn5\r\nn0fL0iHBcwx1l52fZkfaAmH2dDr42k50Lc4=\r\n=lTEN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.18.9_1658135850751_0.7283397540859045", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "38e2a7f2c7dfb20985279a49ac569faf53e39afd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-TeA87JgmY5y5Ep154Ot+JnuD3tghCdxLE8gSK8EOjEvz0Rw5+ZW4DVKGCbGTCuphPwBh0CBkyboGOeg6ybQPjw==", "signatures": [{"sig": "MEQCIHseD3MxLP696JifMIbou0EsvnDYwBMUpnH4UokjfZ0iAiA9OjNZ8d4YTo+nqQwdJToz65Ma03GIjsHKANPQ+lJWAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrU7g//VpCVXE/yoMX5qUZLx1HNhcaYI2HgbzIHtF5x/9+waKLammK+\r\nUxMuLncpCRqm1JbvnKssleDj9qfsschqwQEJpu9dc6X9hgKxXw/VBUDPBH11\r\nR8zDbBDux+1P2U3UGpdWMs3Imw1FTHqlCOzLh49ZZNfxeDNC55gUBCvP2QCF\r\nZH9htK1RQJT+uzqmRVLFH9kQxtGF92FhKuqZyFX+Q2H6o6lc00fHhIOffsXB\r\nwr7evjULfbA0ABHGpOBHpzqVzGvJFTX0fuGOZPQiUpAHM07S7VVVjTWFEn0y\r\n/fUntX8M7+XVAFyuM8LJMKtqwddUiE6qRjlwjOgwnEFPpDxPHAkdxQkeaSi2\r\nEPRvmUMHi+IPYyd1Jq4wGttPEx3D8sdlElCzBHDM2ZXnK/SllVG59Ga3ScWY\r\nmv7EI5M84B9AqGW498/47VBUVRQ8t7ZHh5f1yIiUjo/y0OPDKqVDvirS1laD\r\nh43mvScWFhngIOvKwhdYODLuK5/NmpDUE8vDs55FN5/jWKEso1BOYu3ET0Px\r\nL5WSToHs3jHQvcLZtNybrTiIFjKpFnTWNauadlJfyeWzGKfzeLYuGURIDP3y\r\nbd1G53JNr//IBE376u9GAeRgiVIkwxGYaCvQ9376sxrKKfE1id+5ZyUaG7db\r\nNs76D5o3FZyaozEv4UmSFH1G5f1PbtalWKU=\r\n=feCY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.21.4-esm_1680617362125_0.9732770171325666", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "ad04322eb6b2403ba1e41d854e069bb3df3fef95", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-gZd+hpuHn08CP19b3OMRqcvgU85M6CsSKVk55LcWpQclYsKZymIDT+ouCo0Ij3G7UetkEIfFt+QWBN1+rNKIKw==", "signatures": [{"sig": "MEUCIQD1FcmtD6jRL05rMkSmLbRTEX568UM2KjbemdQcwdQmcQIgE3xLutPxfBAkaBgmRCL4eSBczNJADUEJq9vjIXmbfwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDVRAAgQhjscnfzlx2tMUwrQ8s7Xi7uw1YNvjeGM3LBX9rRaa5cXZ2\r\nvs+W+hIwqy7H82BVTHCLz/Dtzcl6tmJsFda9CCSoQuubqyrQUKl1TFhKEzPp\r\nzNzpHbssFMfKijutduYcQE8P8HhS+Fdz0jZa3LcLPNax7J+9ESrCUjyScz5q\r\ntCNatp5J8xs4kG6JINxZr32K7lRJxCJ5CzyrZ5NaY/kKy3tyN86UM6WvW8an\r\n5wYoJllFDtQcteYoZMM2XXt5d792kMPVKKbO4Zr4cMnaB4cjUrnvnIlC3eXz\r\n5eLvJ/x3cBTtxoRaEy4y+x4BEsXiQGxvkQcZMNy9JYsl68cDQ6fbQ6580/oA\r\nqtTyKWr3Tu5R5A7MIt7N5F9yg4jRKe+honJ2ygTQHe+CMKV+nUSuiDDtACq/\r\n+Ea+n2nkNc0NtS50ul6igh4PlMZJDLSgBVKjfizhNoylwxf273/6G7VzTWf5\r\ns7+a0wuBrtg4qaQEosantg8XWEKkAhpe2o8/47iDaURQKLkkj8z0DLjJ+bAj\r\npdwa4M8xZvMZ1S/BC+umiFj/bW9qWABywOOUna6sh1HLWfRIQ4uBeKg11gok\r\nUVmxExdFbKBIFKr8M8l/kdq6xwpoPqjEWZfn0brCb1/y4Wn8NwO9m7/dCcG1\r\nqKXAGYPTEvtsBZh72J/gstKB8/iDL5KAeVo=\r\n=nSXx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.21.4-esm.1_1680618071338_0.32330507724199675", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "f69f1bca533eb5a6f23b3a3e0b932fcbc6107a60", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-4wiQIq4zCgB5W8N8sB0XzWcdC4bVlSBoDtsbGNLUDWm1HK2InGxr0cAch4DyK7YM9S+gwQfmgShtYWUUc3ubdg==", "signatures": [{"sig": "MEUCIEAZAETxhfIk0JwY8R+FSbkFkMR6w/N+1yPis7VzX43tAiEApYPspFaKyvp0MBH5xPtG6d5Bnd+JAlJh7RRpg+zcYbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrPQ//dyDcbFQwpOYnvkIMslHxzyLRt5XnoWiDKnAjPue4EYre+3oc\r\nIdY8wviMsOXMv1/RS0T/vEoQQWTrw/0FS95Rv/oh2H/tV++gK2iScpOd7oqM\r\n57/ewjf1XRz+30rV5O/OA/Yy6REFb5FkoA8UMVVQcpGWJbpflmF3/F2MCPik\r\nlRKvymFot1aOjfheX0qmpZHHgb0aY+3TrfO0PjkzmAm7856i3Zjplyp1AVBS\r\nbx1GU588o2cfsFmD8AIFc/kb0CKJjpmnJ2OrXDEjtmqmyxr+gtAbO1sclQCJ\r\nmfALiOfFV+YdTlWof7asB1QCzOzQvdSOdjCg8X78o2AAcs9U2fVm0ZXPUb21\r\nXUqJhstPesyfKuR7wqWCtyNLYKm66qxMorDhz1l6dL2cTbmx7x9AhNvS1HJk\r\ndBn4YKe7ZgMu7cxFpOLv4RyTb1r8evRk/5ov7tk03fryVe/HS0yRrg2MXXD4\r\nVFGpFs11HYOtvKlAbeBwk4tngiRoVgbkVCbkw1Tehh2PokKl1EUYBnxwhgNq\r\na2ZJNtM3HN7l8PhmVxQ9zwMz/ahD67lkL2GGrmDUeI6Rbfl4GO0DcKNA9HsU\r\n5A2ZtSo2J1sk1Fq0Z82rEwjDArQcGVibNjbcMIU93zNTZiDC+P0RW7WgR1lA\r\naE4/gaoshhbN6HJVhYloec04WzKFbbQCKOw=\r\n=5xM7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.21.4-esm.2_1680619154458_0.14275384823500437", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "ebb260158f059f85a29e1e5fcf8ba240917b3c4e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-Kf1f/7nYt55afq1OobMDbA0F+dpiJCwchohB6idwiNuqaGCJuTTQrtGdh4bxQVW50y3/bKX0ZE0APMhNb+wL2A==", "signatures": [{"sig": "MEUCIQCP4Y3+Df8CjcLn6PZcRIdQVIYkTemMFXNq2ic52dWDNQIgMAza0RU/PFzVsb/x+BFbEvspAK1EcJM1XP5FM5DEP28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojhA//XsVyNfMmVFPb4WGRZ+eK3rlATYJF5RwhpnkpO8PzVw800kCS\r\nslas5NPL0fRdfYhcH1XH7mU86Uru3tqeWn7Au2gF2WJuSoTv6Gsoct5/k82u\r\ncgbutsk9wTRxSqf6Fv6t/UCdDgGDX5V30ZNEPlT8hpjLPVfdrnQTWgs84upU\r\nP4KwFWtB1JnzV8n+g8iffPQqDqngzPcY/vqqDOCo8PjF4sMC7+vn9x8+QfTU\r\nsT+rsxYkmFFNcOHLCC6b/nSEREu9O8nR0i750Tv5t//t/YyXkMF7JI2ZLsNz\r\np/nueStIzrgnKyjzUqm9OVUd6A4kfafNGJNH/bVaoroxbpk+yJ+UgVTAAVNn\r\n39ZhewH1ePk8yokunSv1v3WXcFRL3iVYH3iL0G4pTF/a07nXuVras/GF4W1s\r\nM7WenDXOZA/+rQkCh0lBn0zH/ogQxiJAy4Iw/YqpklVmJgYbSmx9eYxFVF8g\r\nPEC8rGJgyELV3gsqWDpJPgoNPdzfDsRnfmJjfNfZfIgCl5fCSou+22NQ83sg\r\nA9pGKsxvck+yPRuZkewh64ct7RRUHh3OIR9v9pcm2AVylAOjhU/ptU1mdc7g\r\n55aitIL+/12vF0fhGChkQhoSlC3AMb75bM4X0RhoQw2hdn4PlB/u+3whGZ6l\r\n7JJMTbd7uUWnvpc+01A1/OxxC4YIL/q0jeU=\r\n=zvHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.21.4-esm.3_1680620165962_0.9257024570114254", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "b0a87c5c6415a86c1aa6b2c62b9ed6db52c0cf62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-YSeAsP0IsSVXSQFlIg3K8WAUDQywaQCK0NdH0Mj/96+8b1x+Cur0vmOMcUnBBHhL9VJzDlvTlDHQHIkKnWlblg==", "signatures": [{"sig": "MEUCIE3P0eprV5zbQFmqVcqRxGrCG8JPVVsRcz99p4jZsgl8AiEA4ObEFEIu+GrwuffXltq5UsA0b0+fguHhzatjB6m9hEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNTxAAjrObh49Gx+I7RL9yC4J25+WvMZ07wohiU1XnqxS1O7W3FRSw\r\nU07lSBswca891O/ghyt9ZifMXi+cO1z7eHcsq3sCTX6KoIsIk9Mf+nxn2kS6\r\nYc7IetvU7N+Niu77P2IQ6Su3uxMgDHq1w0YP1AQNpb6UJ5zIxJ6HvDUKB6dt\r\nb/inVk3HEkKKweHGkJaGTeDOHwL9Ntd2Gf2J7/gn7rw7DfwCFtXBT4X1Wdw1\r\nmqZlPMJ2Zy0oQKyitSVnIq6M39Mj1bjmAED0sJfjJIu6v6XbFPk4NQ2OZu1m\r\nnenq6rGFP2vA070Lfk8rVlDd7SD6zJot2oTXdH7Y9NhLpIlCKiwxPzEl+qx/\r\nVYPqsfiXehXS6/K2d0s0S+sJ5mh+Id/GFBVmrYcahv0Uv/YMZfVmQHOr/D0P\r\nC0oXl4026GylwHMN1qJaXlt4PY5zO9BRxbA2HM6717htIVcUNMxGVugFPjk2\r\nPMmhRO8uydhgjPIrnwQqiGHF2A/AkSAsheQ8ONRBKBDzuNBSkfMAKSuMSv6Y\r\nAeu726o7JM6v0lS/ktMuYTqlHKnhbZbFpBAzy9gqI7vYV4QvmQeJP8EVB/7V\r\nCpN5GyehsluhLivDJtYkie1M6YZku8OOOo5GgE1hzAhKWLJr27SH+dJKYWBT\r\nIbjUxRVJuk6PL70hu6J3+goEEKrz3ReBoe4=\r\n=C7KG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.21.4-esm.4_1680621198429_0.4361356401644152", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "b6e6428d9416f5f0bba19c70d1e6e7e0b88ab285", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-dEnYD+9BBgld5VBXHnF/DbYGp3fqGMsyxKbtD1mDyIA7AkTSpKXFhCVuj/oQVOoALfBs77DudA0BE4d5mcpmqw==", "signatures": [{"sig": "MEQCIBifY4yAcrhTWaRdcSygnpt44ajcGH04X/PVz74aKLtrAiAepBT99U/5zvPVsZCJUSLMQWQ5RcvycJPkcaHVJNRrbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9104}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.22.5_1686248475127_0.49349650713671656", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "111106c3d86549b4050e6777a0bbd0c02235dd99", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-Xt1nwlKzr4h/corUPzeAWm0nF4cCUMGY4u83DX2UaJ0mhCX5TdNjgKGoz7Gxpyw+Pz8o1C3AiumCV/5b/BO+dA==", "signatures": [{"sig": "MEUCIDKFakU5Xiq0JLOQ4gT8cI3zIMl7IewAZBQf6OYYfS0jAiEA0WZ/IQrs+IewpEM0Q992TjEsW1Ky0aPD/eXT1RzHeqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.0_1689861588980_0.09178846059999879", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "533f51de75a461d3d357ba813c45260e33f13b6e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-r2WlsktMoXwr2+wJgpaNo7bAFaKEYBH35qiVawsGqmbtsd7Gw/AE2vE/oX3otHZQN+p40Y6KIsEA/sEnohOpxA==", "signatures": [{"sig": "MEUCIQCoaK5oMQ11deKgRQecsAmBFUGWXjczlrx4l/ukX0+U0QIgU/sU4Qdz8okqWcRZnw/2SZFuZSsJFlFAGAsxdSiaVi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.1_1690221096740_0.02770224191295223", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "197d894f52761d6afa6851bb2489fda6bdc1104d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-nlvRTz4/YKAMrT0ERpzSBtGqjGuG0LdKIJL3TxxNbJ21dHY9dZWptlTYAjkUjhkB3UXhfHqYLp86D6BxeCRdrA==", "signatures": [{"sig": "MEYCIQDmAjjMkqz+feEqQNEH8GgqAjKtMLfRmPfj0fh8AnT3CwIhAJtJHdbamo+7xwJJ5n7n+jX0CitNCg9eTWQGF1rqxQ4i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.2_1691594089363_0.7703003753668969", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "fd147d0ccb1e8212716db3d4b9675b559f81214d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-pjqZ9XKtFNiKDwYhzGHhsY3e8YMQXAipUzQ4WVCJvAbfH5YU97y9AW8fgqo9y57lAu/VZVwlxoStQKd3d5joyA==", "signatures": [{"sig": "MEUCICaBsZ1eztXQDw7NmEFUUz8puJeezJuXGPQoQN2rWTNtAiEAohaxexzMNt/jeZUXi9rA5O2XDSB82F4hV/XBZEcMFVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.3_1695740205187_0.5175395929423185", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "1b5b36db78e4ddec0b7e95e3e4deb1851272101e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-NkCLJ+F0j6RMAKG5Yv8cpxcas7gTic2sYqec8IFXDGxcy+kUWz6JSJgXJ1vsLx5xt6HGzI1rSykWfmWKsj3+Jg==", "signatures": [{"sig": "MEUCIQDxjM0vgJy8hJYWTGfOjoiNYD/6qQBWlnDXpVpHxBVL0AIgdyfrOJ8M3K+yqXjyCrPhc4hSxmX3YKj5Zba8w7zIdLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.4_1697076371068_0.1529587628412279", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "664706ca0a5dfe8d066537f99032fc1dc8b720ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-RrqQ+BQmU3Oyav3J+7/myfvRCq7Tbz+kKLLshUmMwNlDHExbGL7ARhajvoBJEvc+fCguPPu887N+3RRXBVKZUA==", "signatures": [{"sig": "MEQCIF57aQcOeCSBXA+eTZ2wqqM8PCFD5Ezi7ZRUKX4FDv4nAiBjecxj4ZJxCKagAuA+9OBV8AMdemNbDigFRVOw0xMrag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9182}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.23.3_1699513429151_0.7779172324394763", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "7c5bf2e0dc3e6cbb1501cc6de2984e462566d9ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-hvc0jEWAjgluFMXX7elpyMclU5ROyALZXz/lCv2/1mfIa721qQSivukilgdFAxNensFielkZMLvSEHmrPeOeCw==", "signatures": [{"sig": "MEQCIETWQuViNerfXD0BXxMtFmO2uKtD8TojjxVtyRIZozOuAiBcWsJwQ8iNKOgblCIUkIgcwSUF/02OE2AJa09W5VrfTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9321}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.5_1702307913745_0.051478442561136095", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "09fe92bdd37f83ff6f7a85b136aefd445f87df22", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-TnFSYDS+AqOL5AnfsOCANqrWKkzIHvnmFZE/IWmxRkP3NAOodyxp5WsjLo/sSKDWk454RPkB/NMngB3pZrvMMg==", "signatures": [{"sig": "MEYCIQDagYYEdLKpdCqQvAz7bwRosXgvf8rawnbB1BHLiOg72wIhALlapqm6XQPgbtQmG6O0hOaUQU7Zu99eUQhDQ2wC4rwD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9321}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.6_1706285635617_0.567528665706819", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "75fc17904ca56ed20e95595810d88e9c6a5ad09b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-qPL7DC/wyucS6QiA/VQ5z2i7OdNVRxVM49WiXFtGsdQ7ZzsHGSQAxgcy9Hfn1HhliZyLZiZnFQg0sLSg8EZaRQ==", "signatures": [{"sig": "MEYCIQCVmOGuSdu59HrKCOMONoC14ez5Bo6nhNu4RFQTJPCj2wIhAMVF3ligv0HuxllYWDXilWJSbOXPy2aHP7p3ZtCw2Wuq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9321}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.7_1709129084520_0.6914720387016273", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "5347a797fe82b8d09749d10e9f5b83665adbca88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-msyzuUnvsjsaSaocV6L7ErfNsa5nDWL1XKNnDePLgmz+WdU4w/J8+AxBMrWfi9m4IxfL5sZQKUPQKDQeeAT6lA==", "signatures": [{"sig": "MEUCIQCRY9R8yTsIOrIIFFe381RJAm4wC0W4oiN+vEGH7KtHQgIgIPS5dKmMVFqpmvj+IBjsDphajtcZiYko0/sgmnA/1ZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9113}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.24.1_1710841713773_0.17108799444256317", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "20e8a03cd0e9030807b383432a8c404a7909b8c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-tjsUYpO7r24kA9envhv/+pcSkgpb8+hhMexTTqG8vAn/tL1OK1srYULEgiUnnxDVzAHQ19Mwphg99iqUl+QRlA==", "signatures": [{"sig": "MEQCIBwchUTEL9QxXj5BbkGN+IzegrDcCu7+S8FArvtpKHuIAiBk/VeBI9PtU+PH1U/Rmq/NP5QVE3mXelihtIrm1PQH4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9235}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.8_1712236784996_0.253499382318616", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "2716301227cf7cd4fdadcbe4353ce191f8b3dc8a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-/8Odwp/aVkZwPFJMllSbawhDAO3UJi65foB00HYnK/uXvvCPm0TAXSByjz1mpRmp0q6oX2SIxpkUOpPFHk7FLA==", "signatures": [{"sig": "MEYCIQCViewo2GO6pI6WBMaTKszPUWAnuCn0hDMJ16tNq4LKWAIhAOpnOB7Lmnd46ulhyYBv2YPrC+OfRYcUCVETSB6bJy9A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75038}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.24.6_1716553465940_0.8065898583267908", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "c4c42b676dcb75f52a279777e10d942f46132793", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-DNNGgdjMZlB0xDCp1UyqZ2LVstRu8eYfxV2II7ZehXIf9ce5DOzc9SJeGyr2aKNPa2i/7ZhroUbI3to41K5chA==", "signatures": [{"sig": "MEUCIHj3eFBGZ98Yg30Ovm0qTJWdcT5QTIV78MgmerJCdn/LAiEA4mR7oj2HkGZs3kC2dnPcPKGdvrwgsiiZlLxxFuJhhTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75470}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.9_1717423449802_0.8130708409920446", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "54d6b6ec3429da48c183d7d438eacf8975ec7f1b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-b63Em1NzYWUnysvD8X/eWtAzk70swHBCEyughN8NZWn88ukYR1ZRyAX/ZKMzV8You38uKIAEH8tjNA/fypqhng==", "signatures": [{"sig": "MEUCIQCZ+65I9kHeqs6BzlVKrcjYHQVslyTIdzUJZlV8f8/6TwIgKIjtK4eOC3SLtfZ7yxX7H87GuhC7GH/LD2dLyvw8gRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75477}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.10_1717499997027_0.4601496683354038", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "dd20102897c9a2324e5adfffb67ff3610359a8ee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-JdYfXyCRihAe46jUIliuL2/s0x0wObgwwiGxw/UbgJBr20gQBThrokO4nYKgWkD7uBaqM7+9x5TU7NkExZJyzw==", "signatures": [{"sig": "MEUCIFZ7XD9i3xay0dNpi6vKV8cqCPRb9EgLBEsTRVNBt2FyAiEApz20oWTFefNb7Ps1r/veV51LPGhgKGwQYdjIOJipZ7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75034}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.24.7_1717593316939_0.5853983391153004", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "24ed43417910dfe24357603ec45f7e81922c1572", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-/txvQOHuipz7/Ua8SKSUBtmhJAvPvH+M1BksJqhHQWUh2R17Wo7s8QlBY3+zk/gzZXrBuwuV+hwkmKX7BG0x3w==", "signatures": [{"sig": "MEQCIDK00BqV4S1Pa6H41wXu+kno510aDY3t1AuuJQ4T3SmPAiA7+c6ke3cAEF7Uw80We2SkifrRWQp6ZN4sPOEOjr1D0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75366}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.11_1717751726610_0.6615810004406173", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "006d42bcd761cad6291660504cf5bbc9912ef340", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-sGeu4B5+f34gq2DYLYJUgHMRtau874Sv9Ig7haOtrtB5n1CCfjbSzofb0XluRaEm2UeY6GlozXg8AqDaXSL7rw==", "signatures": [{"sig": "MEUCIH09KXYGXYY9euqMDk/AzJwahN9KULdTHNvLzlm7X9cTAiEAhyk/ldEddpyWSeAJf5FfGA3nC/nvn99Ygal2lOzcKrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72162}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.12_1722015202435_0.8673145721505804", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "fbba7d1155eab76bd4f2a038cbd5d65883bd7a93", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-by+v2CjoL3aMnWDOyCIg+yxU9KXSRa9tN6MbqggH5xvymmr9p4AMjYkNlQy4brMceBnUyHZ9G8RnpvT8wP7Cfg==", "signatures": [{"sig": "MEQCIHMLW4CsOAbbKm5vT+EjJME1FCbEyiAjR4lYUxyBhOkjAiBDKVVreqS1ZGamunTzmGL2/Qqto+zgPQx+XK+L+bGPMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79572}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.25.7_1727882081631_0.6747429982210962", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "8850ddf57dce2aebb4394bb434a7598031059e6d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-LZxhJ6dvBb/f3x8xwWIuyiAHy56nrRG3PeYTpBkkzkYRRQ6tJLu68lEF5VIqMUZiAV7a8+Tb78nEoMCMcqjXBw==", "signatures": [{"sig": "MEYCIQDq11fWVWkKvBUDg2qn89TkhDNc0do9JOobGYoHbK0iAAIhAIDhoyavrkx70EqB2itC/XluGJwRsW7GUyIXDxuN0Qa7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9113}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.25.9_1729610459668_0.5051589651359252", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "de3fc95aae3d99521c1d00975b8e6733cd6f360f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-2RVBQDSc/OpUpOHn+KFZN34swsr7sj4zA8zBO7lPmOLh5/x91uCFVRwH3ZPV68jF5I6xjmdUHqLFKskenRST2w==", "signatures": [{"sig": "MEUCIQDteESa6fNZculS1EwkuVqpyyhrRsJ5H5J70OempSMP5QIgcGXa5kBJxoRbBcoaFmil9ojMDk0f9haUR0/ExXbEAKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9573}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.13_1729864443388_0.2075386623991713", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "2704c5367f6d1c5f7e76d95ca9d0a3c7f2681579", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-L7tgPnm7ihxDFZ7e4iodijqarQ5jK1Xlo+Xp9PZMB17tKBSYFa3SfsTIIPHWmECsvX0CQIb+Ikqa0p8KEzUNuA==", "signatures": [{"sig": "MEYCIQDS+n3CJYDAD+Qd6KH7oqB6Keqd1BTeoHawD5Dwzd8/YgIhAJxhi+qXp+cn4YJW3SHOt0UmXYGjm75FZHlbJjc99lC0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9573}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.14_1733504034602_0.7815508436041687", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "99ae3ef0bad28d5db6bbd65c74d1230a3bab5ba9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-uRk/TtBVFTpT2RIyjMVHw0rqnMLMwO2vnwu6k/F5OV1sLzoX6pa0Ln057wUVfOYlLAnfbp3oxKsaxnVZajNFqw==", "signatures": [{"sig": "MEUCIHkUnxq45xubJBmwjYU5tU0Tbkc+xUBXG89+Hm75UqZUAiEA5Y4B8xVgNL8b84VjRvBMXcBbELbuwi2nYrneKIWn9T4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9573}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.15_1736529859910_0.6698018923245468", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "ca9bc34ee3e2d5b8b865b0418aa28621ce8d9486", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-Tz6o9ohu2c4+6ji3n3zF3NUkr0f9Sou+JgxTkb4RFZ826rtfoFPfUKTJZYz6L1T5lzBaDJoIN5tJL9zKpVtI9w==", "signatures": [{"sig": "MEQCIDkIAip3yxRoNkyDOEGPha9qFwUR4vgUDIwDWtr/jdhmAiAsTqxhS2zrVKA9cMW4ohXDcrIt0uBhE8Aq+ktdLjRuHA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9573}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.16_1739534335959_0.057953117855179315", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "742eebaf6e294e057b43f06025c64e55247a7f54", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-c6M5V2vH+II/CBWijzK9Yzf7hVZwIpbNdwYPbAktr68XJMbl1jLxq4makTzcAADBSAcnC+wHTLdGdhUth05QlA==", "signatures": [{"sig": "MEQCIGgtcX4fqdZb3fFyptl+rYGAH6lp4k+ieZCzH0NQvbJWAiBCBdVWF3WH/s8eqOsgEhkvgxJ9zWsYKuvcxGa1qcZ3HQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9573}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-alpha.17_1741717487358_0.2420196326368731", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "f1fbf628ece18e12e7b32b175940e68358f546d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-MTyJk98sHvSs+cvZ4nOauwTTG1JeonDjSGvGGUNHreGQns+Mpt6WX/dVzWBHgg+dYZhkC4X+zTDfkTU+Vy9y7Q==", "signatures": [{"sig": "MEYCIQDcRh09SXedkMKNS+jq0Bq3Szg6yAqISk4Bn4FRULKuoQIhAMtOlYgmCdnCRT5MH4oE191uM9iTb1Wp/aqeA6G4Ijxe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9113}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_7.27.1_1746025725828_0.6993442064201014", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "dist": {"shasum": "7307d1bea47987b173074e5cbebd8d7d50de8d04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-pnzCqII3lEYbP7bUZqiHfzbd4Aa0ux7YG7D0ul9TA91gKX91BQ8J+tMaa+pfqP21QdY8VTRmBeTPBc+SpbveRA==", "signatures": [{"sig": "MEQCIGowHDqC+R6CyeUhL8NqDJLzQQ8+XuI1xH3VJdwdrctDAiBkZeRTFts9jmoocKXJ/JsqUsGfCK9720ChQzagAec21g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9549}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-beta.0_1748620257604_0.5953034433428472", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-duplicate-keys", "version": "8.0.0-beta.1", "description": "Compile objects with duplicate keys to valid strict ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-duplicate-keys@8.0.0-beta.1", "dist": {"shasum": "0c63d3fb2203916c5c9988726d6b3b2c1d3ae9d9", "integrity": "sha512-n6icHywpxbWb1JrT8PCxhP3tiv3mPr7TmStwko+MexTMWY7dxyhQVbNH0n3wfgLrG7s8CB/gfeNnfDa8kQGdzg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 9549, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCHeMjq/d9LBi/CQ2XGMxULK51e3b43bzV2T3LjZN2zEAIgAVxJewy3RntG7YjJgCjgpV4drsPdYhoejwC4e07kthU="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-duplicate-keys_8.0.0-beta.1_1751447051359_0.42116104497099305"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:21.578Z", "modified": "2025-07-02T09:04:11.765Z", "7.0.0-beta.4": "2017-10-30T18:35:21.578Z", "7.0.0-beta.5": "2017-10-30T20:56:58.476Z", "7.0.0-beta.31": "2017-11-03T20:03:54.296Z", "7.0.0-beta.32": "2017-11-12T13:33:13.881Z", "7.0.0-beta.33": "2017-12-01T14:28:15.789Z", "7.0.0-beta.34": "2017-12-02T14:39:16.556Z", "7.0.0-beta.35": "2017-12-14T21:47:42.502Z", "7.0.0-beta.36": "2017-12-25T19:04:34.005Z", "7.0.0-beta.37": "2018-01-08T16:02:27.731Z", "7.0.0-beta.38": "2018-01-17T16:31:50.650Z", "7.0.0-beta.39": "2018-01-30T20:27:32.187Z", "7.0.0-beta.40": "2018-02-12T16:41:33.529Z", "7.0.0-beta.41": "2018-03-14T16:26:06.984Z", "7.0.0-beta.42": "2018-03-15T20:50:41.842Z", "7.0.0-beta.43": "2018-04-02T16:48:23.570Z", "7.0.0-beta.44": "2018-04-02T22:20:05.412Z", "7.0.0-beta.45": "2018-04-23T01:56:42.965Z", "7.0.0-beta.46": "2018-04-23T04:31:07.377Z", "7.0.0-beta.47": "2018-05-15T00:08:50.676Z", "7.0.0-beta.48": "2018-05-24T19:22:19.582Z", "7.0.0-beta.49": "2018-05-25T16:02:00.485Z", "7.0.0-beta.50": "2018-06-12T19:47:15.479Z", "7.0.0-beta.51": "2018-06-12T21:19:45.854Z", "7.0.0-beta.52": "2018-07-06T00:59:24.409Z", "7.0.0-beta.53": "2018-07-11T13:40:14.473Z", "7.0.0-beta.54": "2018-07-16T18:00:05.508Z", "7.0.0-beta.55": "2018-07-28T22:07:14.823Z", "7.0.0-beta.56": "2018-08-04T01:05:33.360Z", "7.0.0-rc.0": "2018-08-09T15:58:12.608Z", "7.0.0-rc.1": "2018-08-09T20:07:51.215Z", "7.0.0-rc.2": "2018-08-21T19:23:48.045Z", "7.0.0-rc.3": "2018-08-24T18:07:54.047Z", "7.0.0-rc.4": "2018-08-27T16:44:04.977Z", "7.0.0": "2018-08-27T21:43:02.666Z", "7.2.0": "2018-12-03T19:01:18.475Z", "7.5.0": "2019-07-04T12:58:00.620Z", "7.7.4": "2019-11-22T23:32:08.431Z", "7.8.0": "2020-01-12T00:16:28.520Z", "7.8.3": "2020-01-13T21:41:29.269Z", "7.10.1": "2020-05-27T22:07:22.276Z", "7.10.4": "2020-06-30T13:12:00.227Z", "7.12.1": "2020-10-15T22:40:03.924Z", "7.12.13": "2021-02-03T01:10:50.523Z", "7.14.5": "2021-06-09T23:12:00.644Z", "7.16.0": "2021-10-29T23:47:29.807Z", "7.16.5": "2021-12-13T22:28:11.913Z", "7.16.7": "2021-12-31T00:21:55.825Z", "7.17.12": "2022-05-16T19:32:50.399Z", "7.18.6": "2022-06-27T19:49:59.381Z", "7.18.9": "2022-07-18T09:17:31.014Z", "7.21.4-esm": "2023-04-04T14:09:22.284Z", "7.21.4-esm.1": "2023-04-04T14:21:11.483Z", "7.21.4-esm.2": "2023-04-04T14:39:14.700Z", "7.21.4-esm.3": "2023-04-04T14:56:06.214Z", "7.21.4-esm.4": "2023-04-04T15:13:18.695Z", "7.22.5": "2023-06-08T18:21:15.291Z", "8.0.0-alpha.0": "2023-07-20T13:59:49.148Z", "8.0.0-alpha.1": "2023-07-24T17:51:36.974Z", "8.0.0-alpha.2": "2023-08-09T15:14:49.565Z", "8.0.0-alpha.3": "2023-09-26T14:56:45.414Z", "8.0.0-alpha.4": "2023-10-12T02:06:11.312Z", "7.23.3": "2023-11-09T07:03:49.305Z", "8.0.0-alpha.5": "2023-12-11T15:18:33.893Z", "8.0.0-alpha.6": "2024-01-26T16:13:55.779Z", "8.0.0-alpha.7": "2024-02-28T14:04:44.667Z", "7.24.1": "2024-03-19T09:48:33.932Z", "8.0.0-alpha.8": "2024-04-04T13:19:45.165Z", "7.24.6": "2024-05-24T12:24:26.117Z", "8.0.0-alpha.9": "2024-06-03T14:04:09.968Z", "8.0.0-alpha.10": "2024-06-04T11:19:57.199Z", "7.24.7": "2024-06-05T13:15:17.169Z", "8.0.0-alpha.11": "2024-06-07T09:15:26.738Z", "8.0.0-alpha.12": "2024-07-26T17:33:22.600Z", "7.25.7": "2024-10-02T15:14:41.800Z", "7.25.9": "2024-10-22T15:20:59.845Z", "8.0.0-alpha.13": "2024-10-25T13:54:03.540Z", "8.0.0-alpha.14": "2024-12-06T16:53:54.759Z", "8.0.0-alpha.15": "2025-01-10T17:24:20.089Z", "8.0.0-alpha.16": "2025-02-14T11:58:56.153Z", "8.0.0-alpha.17": "2025-03-11T18:24:47.559Z", "7.27.1": "2025-04-30T15:08:46.021Z", "8.0.0-beta.0": "2025-05-30T15:50:57.790Z", "8.0.0-beta.1": "2025-07-02T09:04:11.525Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-keys", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-keys"}, "description": "Compile objects with duplicate keys to valid strict ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}