{"_id": "acorn-jsx", "_rev": "68-48d582fb7b3118365fed66f9a6330c88", "name": "acorn-jsx", "description": "Modern, fast React.js JSX parser", "dist-tags": {"latest": "5.3.2"}, "versions": {"0.6.1": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.6.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.6.1", "_shasum": "2ec300be688fa8af44fe4255ca9cd57384df624f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "2ec300be688fa8af44fe4255ca9cd57384df624f", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.6.1.tgz", "integrity": "sha512-IN8xQ1BuxAwFR8/kjeRSo9OO7xmDOHg3Hz+r9cMHE6/sPxwZp+4Kno/ilmEJ91KVILUHx6F9fH7OLN484DENeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn5byWHrtGyuWNxbroBQCMMu9u0zmyZDXhNTFV9APglwIhANYx+IaK3iAQgjfO388JrYY2uHgfqypalVQi8HQozYp+"}]}, "directories": {}}, "0.6.1-1": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.6.1-1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.6.1-1", "_shasum": "755682d4b5f425939058376081d6caf117a8ea42", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "755682d4b5f425939058376081d6caf117a8ea42", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.6.1-1.tgz", "integrity": "sha512-futa33MeXcequAyBgFipAqc7vqefcI+qEzARAhRBHe8Jio6Q7Dx0H+ZNVtsddhyzac2L8zdjd6r/qD+Q6WKHNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFjiASa5XCBP/USpFuvKjNgKyjHJf4rIzawrUdrJGMxpAiBDWkkcHJNPrpGg9IzyrDaGvLawvKXZy9u93S3r/0qUwg=="}]}, "directories": {}}, "0.6.1-2": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.6.1-2", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@0.6.1-2", "dist": {"shasum": "9a7f513072c838274cb2a2aea4eef446f85e956a", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.6.1-2.tgz", "integrity": "sha512-WXsk+4e5t/dz5prqutfCRKjpcZqH/Pz5aez7YVcOGChaFO9LtJ+/8m1aF9hCstoqHj9WPQuUlcoDOfoByuIQEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhrS41ehF2FHGHXiuueVPtx7khnXNCNaEwvztBiDw9SgIgIuanafM1E3OngipN1aYrLpEqd4TsJVaIXzZtaJCTvU0="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}}, "0.6.1-3": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.6.1-3", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@0.6.1-3", "dist": {"shasum": "428c006d920a36f5cb0f6e82a9f18aecaf68295b", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.6.1-3.tgz", "integrity": "sha512-x9Cbe7z6FT+b4BUrNnmV9aOs8JChq6bsH+5V8+18GlU3Qh3XTW+pmBVg1WJykXAR+yBr0ySz81aEkEIYOnCgEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHsyJleiU0zCr3ggfD6fbh5RiblJuMpPDWGUdDmQYajXAiEAn/a5JRFrFk9lkFsAHniXB2C0KFMmDVUN14+wZGJ9gUo="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}}, "0.7.1-2": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.7.1-2", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "zsjforcn"}], "gitHead": "fe805f396ee3691836a50f8add9badac5e6ba084", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.7.1-2", "_shasum": "0a32e1dd73f9947d13838190a95abe3c315b3d3c", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "0a32e1dd73f9947d13838190a95abe3c315b3d3c", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.7.1-2.tgz", "integrity": "sha512-+Hb8CDEHlN8hNx9gsTki1bb9bgcvFM+OLL6QyhUZG1bR1PQ2zKAdyDTNtcnrdkmZI/Oon3jsfGorMJr38bsy8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDt3FWFO6Ma/mCytYtZRlw8Wxy/Gi5KxB1w4JgA87OurAIgZCgQQEW6r0fV+Abnih/jDjtYXxIG7YrRlxCn9JUkAo0="}]}, "directories": {}}, "0.7.1-3": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.7.1-3", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "zsjforcn"}], "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.7.1-3", "_shasum": "2fe9af5aa7eed1a621c4f0ffd736b6a94aba94a4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "2fe9af5aa7eed1a621c4f0ffd736b6a94aba94a4", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.7.1-3.tgz", "integrity": "sha512-fP/F6GvUNMZ0Mq9ScBYdRxSW7PsBs2fUgm9p6mNe6jz4cMFMbTt6DolMbBCuel0jJv++kkycmZpewUEmlQo2Qw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8GJ/JELenuVPGHDGBLO19VBFFVTYbPA+WM3AMffY1MAiEA0kJBVckkMvxKjde32+X8u2EGLlUtstgf9TiMfzEYCpY="}]}, "directories": {}}, "0.9.1-1": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "d4372f56d7e7f434577d4e35ceef1ee1bc4b75c5", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-1", "_shasum": "fa04753cd8453de76e0c15877aedfd2235c47e25", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "fa04753cd8453de76e0c15877aedfd2235c47e25", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-1.tgz", "integrity": "sha512-CQDctI5pIlZJ9unvGuUzCQ/Xybr1nWtmsFlhIT6vchmac2Qc7gUuCaJa6XMQG5rvVNzG6+gCIj0iECZN58lXkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/HfRroCRuPHbhASNApCsD3g4RvFIBjIlU9NYYbWZv+AiAT14N7DpAagvEZ7eJ6Ic0UEYm4EMzWzapBdFHkEDnpmQ=="}]}, "directories": {}}, "0.9.1-2": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-2", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "ef2abffb21a1fd01c32560a3a5e8c5ba313c6ae6", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-2", "_shasum": "6ea730b440cc0b79dc9c2fd65360e88fc821733d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "6ea730b440cc0b79dc9c2fd65360e88fc821733d", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-2.tgz", "integrity": "sha512-VLynpFh/cUALA9HNJQOZDY6GCxDgbKd3cn2Hlj+wkZy8dSeqyxgw3PuzEFY7kSKoGt9LeqqrnTRcmCKtfp+C/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDA/AybTkt0Kn1g84scSqcXpuCSDXgNKl3XSuxkATK4+AIgW/CNkJyxliypOgPCf3QOrCvFmF5kvTsk1h9j5ImBBDs="}]}, "directories": {}}, "0.9.1-3": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-3", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "55f339eb2df3b0f4c39aac6505eb912b02e022a9", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-3", "_shasum": "ff16f2ed2afd0613ed1165fc019e457d104ccbbd", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "ff16f2ed2afd0613ed1165fc019e457d104ccbbd", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-3.tgz", "integrity": "sha512-mw5TV0h2Ct51/RBo8JGIGjO5rZ0QeQvqmpi4mdT+M3HwtBCp19A0Xu0R0N6UFQE5JoeTvlH59J455nLRLsfuIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH7uPuovC+J8YhWcBDTA4Ks4fv4kZSmOSAJlRz8hTIqlAiBCBiNif/T3sahtJMu/VufSIIZ3RbhspIas+qRVYb1YRA=="}]}, "directories": {}}, "0.9.1-4": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-4", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "333628294ed3d1071c2c88971536fa2641c92534", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-4", "_shasum": "ef1c94c5521660418285825c59063089c4d1fead", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "ef1c94c5521660418285825c59063089c4d1fead", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-4.tgz", "integrity": "sha512-Q6UOJ0+gmJ6O4G8yOrOXaEvFX8yi3+AndsGqdtsIGNyLQYj1ftCJuiYLwNPoDc6a5LOO9px0PueSzW7pcAJAwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIArfQVY9qXIGPi+D/BbRYo49oHbRQ4IFgSPdNgk+MyyYAiEAtID/VTgAhX3KbjHKtjKP8wUMugy3VcGxkKzm9T95IO4="}]}, "directories": {}}, "0.9.1-5": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-5", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "1ad4ce2048133e21d906d8c76954aa7c837796d2", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-5", "_shasum": "589c70e5f829385aae9b866f237ca62c7d5a98ac", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "589c70e5f829385aae9b866f237ca62c7d5a98ac", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-5.tgz", "integrity": "sha512-E5W96UgR0Bkle8jJ19WHdto7wZZYPJIFnLUPYwgoxLdSNdY4KHM5RQm6z+3Evn0EsuvgyDa5hbMqrSrpKFaN6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLcDGWmOEBXfcxnSHI8gCb2etQjC3VfxtrFQny9lvEEgIhAKM1egWSkt3HGekqbByIEp8JqyUFKm5jCS5mIl/4tzkb"}]}, "directories": {}}, "0.9.1-6": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-6", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "de1ab73ae9aa39b37c54d531872eb2d11af9e80f", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-6", "_shasum": "5a328b43f8450e5800990a210f39680464165f4b", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "5a328b43f8450e5800990a210f39680464165f4b", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-6.tgz", "integrity": "sha512-AnDAv18V8aEiEHDttvRHPwzuO6unhxkJED2x0X6Y3Oe0q86J/OlOAUqimWD9PriHwCeK/RWT/T0yxX2dkZw8gw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICB/RBBgUeaRuxrbYds/recFELXSUou4FfnfZzL8szkyAiAZbdzhq5KEhqjDZLREL6dlNxQJdVgqSA0w9bkTLZROXw=="}]}, "directories": {}}, "0.9.1-7": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-7", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "529bbe8b93ecac3f3850afdccc745fac624c573a", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-7", "_shasum": "e045f7de8624610130b75b36502661c015b987c0", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "e045f7de8624610130b75b36502661c015b987c0", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-7.tgz", "integrity": "sha512-0Yk4pyGC4whSBop9qRHHnhaZt2GbkG2ENGXDH2gUQ9X7YArzllRIAjfUI4V2xo8X1Jom1+cnKrsXmueOirJ8+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuzqCrE3pyj8wfcdd5NAi9grUXV+NJ1gOsj6AwIUJZYAIgZMUvkBSJQC/IWtJzNDha0/KG3Fh8DqhE7RIDHtOVb5g="}]}, "directories": {}}, "0.9.1-8": {"name": "acorn-jsx", "description": "Alternative React JSX parser", "main": "acorn.js", "version": "0.9.1-8", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "http://marijnhaverbeke.nl/acorn/LICENSE"}], "scripts": {"test": "node test/run.js"}, "bin": {"acorn-jsx": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "gitHead": "2378bc2cf9f235eea314afc340302bac3554359b", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "homepage": "https://github.com/RReverser/acorn-jsx", "_id": "acorn-jsx@0.9.1-8", "_shasum": "b855bfb1c0b6f237ec48cf0bb1216d71659c698a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "b855bfb1c0b6f237ec48cf0bb1216d71659c698a", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.9.1-8.tgz", "integrity": "sha512-CIny/eaB3PRoqm8/ZP5v0ypGA0L5CCqvBaix2HikdBrqh2pcS973/8GC7UbzsbBXMWZAbAAhcUpPyah1zu1T/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNSQqZ2M+tCXvYd+wt8YCIo6I8u/JqpMasCn2LLiz7bgIhAKcuDqLbKSh/PPIaHEq6hSjUnrRRm55XlbfAbABDv/Lc"}]}, "directories": {}}, "0.11.1-1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "0.11.1-1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js", "prepublish": "node bin/without_eval > acorn_csp.js"}, "bin": {"acorn": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "gitHead": "de23a869ae266b566b0f052b0a76b8acdac35c95", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@0.11.1-1", "_shasum": "026f45bce2145e8091099622a407e95d74dd5a03", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "026f45bce2145e8091099622a407e95d74dd5a03", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.11.1-1.tgz", "integrity": "sha512-q4X9V+Z8f7OMvXEAW7uCshdKqBSVa4auKrFw+zvuDDojYy0eQrPFd7FemDADfrHnOfdFCbpMVezIFcccLV0Cxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEY49deCqIemnwOiq9+eaHTXAjBrGee93WbMv09xsgvwAiEAmbZhWi9JsApc+/FDORYpmtNbWL6cT12+PfToEm23XnI="}]}, "directories": {}}, "0.11.1-2": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "0.11.1-2", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js", "prepublish": "node bin/without_eval > acorn_csp.js"}, "bin": {"acorn": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "gitHead": "401e6c4f30d6ef5e325fffeffbb35f2afa8c719c", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@0.11.1-2", "_shasum": "bc6d6160a9a0ba8b5822cedd854c59752286abc8", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "bc6d6160a9a0ba8b5822cedd854c59752286abc8", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.11.1-2.tgz", "integrity": "sha512-Gn5bBFSpUeL8tJUoSLOtViaTPycuF7S1cZbn60Trnd9Ar0l3ez6AoROeOoinOU2+1Ju/fJ567Ql9azJqQa9zjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqBEkcqW1De471An+UUBZBbKc2Gk4f+BrzwsvxlkrC7AIhALe27VxbgEOcv81ERPOuTNuV2uOrOE7jz6Gv78mIcLIk"}]}, "directories": {}}, "0.11.1-3": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "0.11.1-3", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js", "prepublish": "node bin/without_eval > acorn_csp.js"}, "bin": {"acorn": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "gitHead": "55ae051c81917a81dc8e46beb7026f151ed9e41e", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@0.11.1-3", "_shasum": "d7a6ad6fa6db809bb64c67bdb717752e6f722825", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "d7a6ad6fa6db809bb64c67bdb717752e6f722825", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.11.1-3.tgz", "integrity": "sha512-Dd0JIURFCiKeeSK+Gm2ssMOCtNtAGjI7DYE2pnC8ThBOkgRizPBlvigDGlaA/lv7AiTM9XYBjO/OKW93CbB12g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF6RvawXpDt3zR7ikOEZE/0qFf9WKiLJKbRDXPBVcn6BAiEArwAV33hlLAzCingdGHRWPrNkYCibx1BkjKcnDvUBBnA="}]}, "directories": {}}, "0.11.1-4": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "0.11.1-4", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js", "prepublish": "node bin/without_eval > acorn_csp.js"}, "bin": {"acorn": "./bin/acorn"}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "gitHead": "3cbad64c1eef88d28564ceca6cb991a8e0248406", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@0.11.1-4", "_shasum": "1b41ad1b5e9b8134cc8c874bbbe6048de950faa8", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "1b41ad1b5e9b8134cc8c874bbbe6048de950faa8", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-0.11.1-4.tgz", "integrity": "sha512-K5ELFkXeBQLVP7qfZ3X37aQ6taw6Kpyp/Ba7yB/8hoG+zTU+68bYa/H1XdQwJ7ib+5lnKsTgTSLmpqHNQbPwVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWerA13CXlbvAEKuYv5NVVH4UcE9Eq2aa5cppVJJFvmAIhAODAKULfNahgA1CvrI0ew36L6pJUq5RYa5fH1GcWpOJi"}]}, "directories": {}}, "1.0.0": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "1.0.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^1.0.3"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4"}, "gitHead": "bb0049107bae267c23161e77b36d5faea196ff67", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@1.0.0", "_shasum": "99b0f8f05b2f09c066ce43d7ed3632c3d3fc4e19", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "99b0f8f05b2f09c066ce43d7ed3632c3d3fc4e19", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-1.0.0.tgz", "integrity": "sha512-KG7aQ2koXccpcQVs4uCllNsBXM0C/tJHnUkYmrKIJlWo5ZlSLJ4JDkjzF/ajd4JnTQVWYcV16PXZHVYFklNnDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHFSXGC9VsEbEB4gx3TYqUGgzs4HTySyyTIYPiBTbVGFAiBgXqPqZens6OeVIh5O9SsBbT0Et+3Qwhi6b3uJ/ndcYA=="}]}, "directories": {}}, "1.0.1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "1.0.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/RReverser/acorn-jsx"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^1.0.3"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4"}, "gitHead": "63118d26a3f9641ffe610719edce6c2abb8919dd", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@1.0.1", "_shasum": "e6feb4f7d8b131f1f450ea98601927a03688dc3e", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "e6feb4f7d8b131f1f450ea98601927a03688dc3e", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-1.0.1.tgz", "integrity": "sha512-BFJWG5zwZdn+Ug7xKeB39mzvbwvFqG+RVhKDBLgBIJWWgWFfgX1gRmG9CihPI7zESyvOwAfrakgWtvHi9F39TA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDK22Sx/cnwpuUiM2ioolatnQolBWKZDWCcKzF+KbS0cQIhAPqZezLX4x/IKwWBehKEUqb5J41UVeYDArLKqaEoTgJP"}]}, "directories": {}}, "1.0.2": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "1.0.2", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^1.0.3"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4"}, "gitHead": "393424eb8ce95bf77e72e3041847fc9ad2445bac", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@1.0.2", "_shasum": "a71c5b4cdaf165663b119ccbba4b87a7865dd02d", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "a71c5b4cdaf165663b119ccbba4b87a7865dd02d", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-1.0.2.tgz", "integrity": "sha512-IhvUKkRK33+G0gZM432ixsuOVclug3CuU8UCJN4N0TqCitAQJvz75RVvMFjBQ2ZLcRfw1Fz/6Zfe0LbsvAGFaA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGEXD7DmlV9weYj/Y9mpaJO4RN7lnuyU1skzIfW5cNb+AiEA9aSP22G+VoYK8zQxGE1AbXBO9Kg2h7oL/Ne8Yq4SdG0="}]}, "directories": {}}, "1.0.3": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "1.0.3", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^1.0.3"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4"}, "gitHead": "a4395c3f83528f8abe67e6355083e74cc3508755", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@1.0.3", "_shasum": "88bdad2b6141d70c6d2941f161e882da6f16aa7d", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "88bdad2b6141d70c6d2941f161e882da6f16aa7d", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-1.0.3.tgz", "integrity": "sha512-lU5bANnQiKuid+Wgz0b48UIGIb7KPge82DZjhlTGhwr+WQoX9juEAuLy5CB1x91kf2I81nK/q9K15sXlvVWVMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHkCA0Y00QkgR8t8JfegVBLXbonp/XNK1DMMvt2fH2gWAiEA8/QYT9yEC3QWSxUYkgE9z8gJANDcFQL/C6sZu+UNLAI="}]}, "directories": {}}, "2.0.0": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "main": "acorn.js", "version": "2.0.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^2.0.1"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^2.2.5"}, "gitHead": "72d617472d54ea2ab3843eef533307c9129a95f5", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@2.0.0", "_shasum": "9107057ad4524eb19be95912d406b2c4764e3f17", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "9107057ad4524eb19be95912d406b2c4764e3f17", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-2.0.0.tgz", "integrity": "sha512-fYsaNYT4gD1G2yivM+qrgiL7Upia9FabPzPxxArdCZNtz0j5/45LR9bMzNoht6uiZp2D25M1s5UGOY6vjJrQKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICB9YTMZQ6HUJvOHHW4llC/IGvOMKJadjdVHBBoGTrpCAiEAk6EZbXTEX+dT8EEP9fZzpesn+CcX5k5/Bt3RNXaAeB8="}]}, "directories": {}}, "2.0.1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "2.0.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/RReverser/acorn-jsx/master/LICENSE"}], "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^2.0.1"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^2.2.5"}, "gitHead": "cdc624d83767518d174a7873054ff9f2984eb83c", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@2.0.1", "_shasum": "0edf9878a5866bca625f52955a1ed9e7d8c5117e", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "0edf9878a5866bca625f52955a1ed9e7d8c5117e", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-2.0.1.tgz", "integrity": "sha512-rbNtu2WkMJAZNnw2rh35whZO2e2N8Q1Dp4PBf/pKJAals6uFbPvVgVcKZ8poUnrkF50thOea1ApmF8W56apnwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKQPi5MBrg+6BLfPk0xiRfwC2t/DpQ0BfLjP5YHadaBAiEAjIqPnySxSDZCAyhxEga1Y+J6K8PHz7/LF6AR/q5PGyQ="}]}, "directories": {}}, "3.0.0": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "3.0.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^3.0.4"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^2.2.5"}, "gitHead": "2b52a0d62820ea12bd95c0a07942cb39afffa349", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@3.0.0", "_shasum": "c17ca956693571ca3ea70aed415008116cfbd4ad", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "c17ca956693571ca3ea70aed415008116cfbd4ad", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.0.tgz", "integrity": "sha512-fDpcean4EebgM1elneboZCdRPIPRQKkWorhLmosMQ0ZespomH6UXp9eqDz4bj6pY+BcLdLzdzXPSffBqvYf25w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZm4o62zPQENcYo9yL4KAU5Rd6TITVxvM+rt/w0CTBmAIgGm/xYMhwAOAukKQvVLGzhTYewBsQbayyns+XtoKr0QQ="}]}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/acorn-jsx-3.0.0.tgz_1457985965089_0.5892153568565845"}, "directories": {}}, "3.0.1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "3.0.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^3.0.4"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^2.2.5"}, "gitHead": "05852d8ae9476b7f8a25e417665e2265528d5fb9", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@3.0.1", "_shasum": "afdf9488fb1ecefc8348f6fb22f464e32a58b36b", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "afdf9488fb1ecefc8348f6fb22f464e32a58b36b", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz", "integrity": "sha512-AU7pnZkguthwBjKgCg6998ByQNIMjbuDQZ8bb78QAFZwPfmKia8AIzgY/gWgqCjnht8JLdXmB4YxA0KaV60ncQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHS0huUSLz2W2jKAcumtniSv0DQcj9jQPwGa2qlEyufAiAzy0WRBIHUdx+4EQMASx3mEDRW9ShsoEVVEo5nZc/VZg=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/acorn-jsx-3.0.1.tgz_1462206645285_0.17844340158626437"}, "directories": {}}, "4.0.0": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "4.0.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^5.0.3"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^3.3.0"}, "gitHead": "d670fd434d6880674aeac9e9cfd97f8711eef548", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@4.0.0", "_shasum": "f85ae6ca92f396ce07317011a159b5e8990d426b", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "f85ae6ca92f396ce07317011a159b5e8990d426b", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-4.0.0.tgz", "integrity": "sha512-EybQ9EJQAEHdbC01C2Ftnrmk10vhNS6GK1s1pIbj5ZG5JGT+rh/DDvD82/NwUPzkNBBsyuYWwps+dhe0Mt/z+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtJhNGskV9ljjqLznj9gkngdNk4dM1zpdmWZCMUQh8eQIhANSuaTMZ6x04Ai3eOFfpKPMXrrzJQMtIkWX7SkuFth0R"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/acorn-jsx-4.0.0.tgz_1494760709590_0.11505925818346441"}, "directories": {}}, "4.0.1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "4.0.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^5.0.3"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^3.3.0"}, "gitHead": "924f9ea043a5627e03f1e5e7216640b352d24605", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@4.0.1", "_shasum": "ada5a01573727a237774ce625564d079ec9de12a", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"shasum": "ada5a01573727a237774ce625564d079ec9de12a", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-4.0.1.tgz", "integrity": "sha512-Xp5fvu8c/svlPnNbX/kExjC19DY2Wxe4P2cA/lLWAe4DzbtVe4oM9XDAx2iMukgix7e+sBmRytsNLyR1Bk7PGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEeesTgHH2tn1qbA3QX12f0OWwA9wmgs86CavcS7srVdAiEAtByyOqfEZpULoz/Dq3pwEmLQQzaS1WlzqDquGiNhokc="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/acorn-jsx-4.0.1.tgz_1495062204906_0.32292856788262725"}, "directories": {}}, "4.1.0": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "4.1.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^5.0.3"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^3.3.0"}, "gitHead": "1dc3245e5dc135a1f542e0ab2e959b97d2316d72", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@4.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uPzAC9QsMO2BU6c3/S0n+GSPweDu14A6g7GsbF4F7qOFPhrxFGNvd0ergQmoB6aoHPoS+rPk3m5rvSOaaguntA==", "shasum": "e687194645bcde55e7e7e9b1859644351a27d13e", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-4.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWh4MRimWbi6qu/pOWGh6oGP2Z5y7PFP8LkNmsjqMdwAIhAJCNWdLqoPSeECJjYYN2/8MGhz3LcYiuZl8XFbH/mBqj"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx-4.1.0.tgz_1510364148376_0.15278526255860925"}, "directories": {}}, "4.1.1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "4.1.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "dependencies": {"acorn": "^5.0.3"}, "devDependencies": {"chai": "^3.0.0", "mocha": "^3.3.0"}, "gitHead": "9dd0a0d3628a309f73e73835c74e56a882ff84b4", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@4.1.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JY+iV6r+cO21KtntVvFkD+iqjtdpRUpGqKWgfkCdZq1R+kbreEl8EcdcJR4SmiIgsIQT33s6QzheQ9a275Q8xw==", "shasum": "e8e41e48ea2fe0c896740610ab6a4ffd8add225e", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-4.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGtP2HkpHyqdsw/mGwDcOGw9GDupZVXYhhBc4dsdxVPXAiAdm0h+mjtSYjiNMG3MGm0/39VlOd8p08E3/4A1Mv9nqA=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx-4.1.1.tgz_1516143912405_0.03429930028505623"}, "directories": {}}, "5.0.0": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "5.0.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0"}, "devDependencies": {"acorn": "^6.0.0"}, "gitHead": "ebf23ddee644612c8e59d99732d1c605aa7fc1e1", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@5.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XkB50fn0MURDyww9+UYL3c1yLbOBz0ZFvrdYlGB8l+Ije1oSC75qAqrzSPjYQbdnQUzhlUGNKuesryAv0gxZOg==", "shasum": "958584ddb60990c02c97c1bd9d521fce433bb101", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.0.0.tgz", "fileCount": 5, "unpackedSize": 22771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpjciCRA9TVsSAnZWagAAGi0QAIih1UrJJb3Rm3t7TMld\nFEnJpjFJ10ShKLkFu8v6TmHz8jTTJ4rpadUWSP8nInpeHLrmx+u8MKwFqkC3\njpNOERjHwfeQk9QhhdIUTMZ0HBrcDckNooDRD0bMTPZo/NjrECHt9RuRElkK\nLhdH2XvT0N+Pe2l5hvFmdKs4eMwdO2/B5Y+Qpb6/oRmX29EInQIMgjxBep2a\niIx3X11NJQeAmVgkMvXbuOV42zsrILWH1y5tSJBFb5bBZyNV7sEgq5tpO32/\ntAYUwTSweZev8HqT2MLErrEtfFJC/wGaw4+puOayc9Yo5UfUiYD9Q24vNO39\nIhLNq3fPwLDL7p08a1z4KXN1DNKjgG60XLkrX7CzL5fW5V/3SeR20i0/ccuc\npShRYnUnSMsLkf4pO9zRvy18JGvvUsLLkpniHtxujty9pmS8l1/GROLu89DM\ni8lIiCuBj3qJberjsAwdUBJZvjpVMQJEm4BFpmyb5MUqAmciEwgoqGBCHQ7+\ne1WVoVRrwzBvtOYyxTOVP0qxiLfpGO6Je6Zw2NsooSf/UkAW68CBSL8L8J4Z\nZu49rF0fiaxXuEiVocaVD2gUFKEIZSSXbj3DFdbRRGMSjMwB8V9lD8tYPzFg\n/lei/CpBuXT/w+OvP0AUPpbjqvI2p5w9W3AJlrisBUtxTIyc6a3cfvE/yXHD\nDpbn\r\n=5jmD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDvUcvrF4qlj7RxgIKtqgNqFamtpobewExObaL9F9GUAIhALSxs0MhP54iKGRbog8TuBDd+OTWce7tPXwP5dSvFbol"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.0.0_1537619745622_0.9171464255099009"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "5.0.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0"}, "devDependencies": {"acorn": "^6.0.0"}, "gitHead": "6697d3407221a58e23d040b4059f20eb045247a5", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@5.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.0.0", "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HJ7CfNHrfJLlNTzIEUTj43LNWGkqpRLxm3YjAlcD0ACydk9XynzYsCBHxut+iqt+1aBXkx9UP/w/ZqMr13XIzg==", "shasum": "32a064fd925429216a09b141102bfdd185fae40e", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.0.1.tgz", "fileCount": 5, "unpackedSize": 22988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9HLSCRA9TVsSAnZWagAAXRkP/RVLsnph2jeZwGXojoNW\n1mXtRp4avUnmVQk4KeXLfAWsqTS3zsuhftTJjM877Q8ZownOWonTJ8GKm0iF\nyi90AXfrJEqsX3YU/gU32Cqi65RAJvn2F4E3toePPI2WmylH7zUmdiTJSnkb\nacV1EDhhWcYNCgbfqpTQWBFgBv+0PSiBVlber1m1AKbyx2PRdpPK0tNm7d4e\nVE2MESifqlio+thVkv4R65Go0SMjEK+oE0TvYD9C7dhj2tM8q3iuyweRHUTg\nfhGEUlesnosUFFK4Qb4KtlskbovsC55mpvcWXy0pssIbRlaWGRbzi67zho4n\nGV/25LWMdlNgcJj8nKwo1I2RU2gY0ni2amfHZC4QXsJdebvZF45MhgR3/8cE\nUbV0gjyU4zUXQ1LcfVlD8CXUJkt4OrGW/m4pBSbnLZVcbCWbjBNIGxmST2U8\nrKlRPGDFI4YofVsJKUk90CicZlIHaTo0fUVklsF0au1kIn/8bprp97RkyaCg\n8H64Q3uOM2hqTbTWn2tT2xOaK39pW/lWr39PfBjzvLYetpkt8KrF5cxRgKIu\nS1+CUf5LJ4d+OqLPm+QJ7bZQHe2xum71FnQcV4NscsKiCzA5+GArS+FXVcvL\n76eyvXsY1UAmE4QzE/0KmEJ8wqibikNwzOPJJsbGLmgt+k+MWg3fbglQZZNz\ncbdK\r\n=qDvT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGHXcFYbTrEQOCID17Jrgc7EjfWHbHKygrL19Zy5RKwwIgf/DYmTJGdiwpd7gfn+Qcnx76V1vMX4LVgOtpSCsodBY="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.0.1_1542746834018_0.26727646241608105"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "acorn-jsx", "description": "Alternative, faster React.js JSX parser", "homepage": "https://github.com/RReverser/acorn-jsx", "version": "5.0.2", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/RReverser/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0"}, "devDependencies": {"acorn": "^7.0.0"}, "gitHead": "af46d395ba45ca0e098a4591a4b04609a483d105", "bugs": {"url": "https://github.com/RReverser/acorn-jsx/issues"}, "_id": "acorn-jsx@5.0.2", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-tiNTrP1MP0QrChmD2DdupCr6HWSFeKVw5d/dHTu4Y7rkAkRhU/Dt7dphAfIUyxtHpl/eBVip5uTNSpQJHylpAw==", "shasum": "84b68ea44b373c4f8686023a551f61a21b7c4a4f", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.0.2.tgz", "fileCount": 5, "unpackedSize": 22998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXAJ8CRA9TVsSAnZWagAA1qgQAI1/28PCAzrjhSHNZhG0\nPZ2YDKN+WVhSN01CL4ler6pVDgPRLuYVqJskNSCGKNr1h2svVaYJIdiuHYPB\naCaCXhfEf4P7b9C/6RyN7II5bIWQbh4TbFJqFi9tKrQtIWrVdkt+kgf2Shal\nXCBcnrjoz2wXJ6alwVcMFgM394noyRx/zWO08A2HXuzL0cLf4scPOG32S0kj\nyHlQftZq4eESlIChTtDEcmTEk1LanisBsx7UkSKwO8whn8JC6mUQ+xAsNwsF\nPKQJ4BR8yGULl2uLceWCZeR8JIBA3lRSYtPEt4JEQZRhido18vOSoN95G0vr\n4m3bRHupK8myuUDMH5Zy1owP7uoDAl0KwjCguGfRUyILvwCT/ereX/L/Kfxi\nqktlk529bXAgMzb52znZaVm3N4b0ozL5kF6RcEJf9y4sYrUn9tSBzudc/zZI\nZzlJ3XnhEf+xp231uVLfttHgAZpK7nW6AsQQ1FLvATrlmIZEOiDwqgzTzbpH\n0FXblm8cS4jE5chg51DCCHOnXIHRGa+gVb/5LpeAQxiTMgPMvzVXnUACvaaa\n9A1n7Yx1t4hCyQxwpEXy1M0qvpkTw0YV+fr6hLFNiHvvZgE3/t38YoGqrRve\nLIDtPiEEq6kEcqXlHis94o1eMcHlqXJENITkxIypQmbxXjrJd6NaMfEVEqDy\nN3KS\r\n=9uXw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG/ymsV/MTrtB65tfNMrdo+5IwW+AotzL+CVxgqetRhJAiEAp4k6WILlZ8CwLjrX+QJQEX/hrq2RNPdp92BToFiqVwg="}]}, "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.0.2_1566311036133_0.322719430473583"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "acorn-jsx", "description": "Modern, fast React.js JSX parser", "homepage": "https://github.com/acornjs/acorn-jsx", "version": "5.1.0", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "rreverser"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0"}, "devDependencies": {"acorn": "^7.0.0"}, "gitHead": "e57398c73a0609692ca83214943046dbe7871eea", "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "_id": "acorn-jsx@5.1.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tMUqwBWfLFbJbizRmEcWSLw6HnFzfdJs2sOJEOwwtVPMoH/0Ay+E703oZz78VSXZiiDcZrQ5XKjPIUQixhmgVw==", "shasum": "294adb71b57398b0680015f0a38c563ee1db5384", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.1.0.tgz", "fileCount": 5, "unpackedSize": 23811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdo20lCRA9TVsSAnZWagAAVjEQAKEok1PAueiv0TfHi4km\nBmLrS5K7OHK/ypAwXtoNjcddSPfddYqdu74lGhxg2Cf71KbFqxJNMfilYeM4\nGpIQkUPVQHAvxZSjvFddBSBoUdytyd3q03O/Tn1IO6igQ5T8tAq1hfP80YOa\nHjZVcdKSrwL97rtZUGz7lCudL1L9N3SVR+RsmScUS+E9R13+LEgJqoZIrSEn\nGmV3KirUf35oAdMtE7NLmANRawQ3vES5AK8ava0YrE84ZtdnXygnOSi5Tnux\n03G44Mymngy9VW+F2XhbXwrLbaWv7g5Hq+HRBRq2XNPM1QmWNtAHS6ksPwKa\nxOZ0Z9lEGYGilaWeZMMrkYC4QiSAwwOmtdxP0BcsUAmaiSEvWNZjV1EVOEX+\nYzR0gaS/7mfF3Q8LG60WACP2eVpJZMQv2Voo2tHaGgnO6Xj45lzI+KQS9hn8\n2yjUwq30YR+AuJDiL5ATcWREfRgBPGKoUBnMDia8MMq1cLkbgz+eD2AJXePq\nFaadiporS2EyOTdHw+iXTGhzWZDYIxUQt7hfB+dL2zf9PO2Gby4k6xanY8O1\nc3eGQDd97ycDtp3zBJuARE9AkfaIOtvUfg1UMdZv0BGcs8mzhJort/X0VkDR\nEAsRulmoNh9sL7tNCZs35JYeolhN7TXAYksJK3+B76OIzJE44qOJ5H2EVN/R\n1++s\r\n=I0wG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICQoad2WOXupcwuLdMbsJBdxd2idoDNdIKOERJVqjHZ4AiEAtPQRb9wAH4Msl3MVMD3n35DZ+clw/Vg10CF+XFc0IEg="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.1.0_1570991397260_0.722650080104436"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "acorn-jsx", "description": "Modern, fast React.js JSX parser", "homepage": "https://github.com/acornjs/acorn-jsx", "version": "5.2.0", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "rreverser"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0"}, "devDependencies": {"acorn": "^7.0.0"}, "gitHead": "c30617bd8d3763ee96fc76abfc0a9bb00e036d68", "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "_id": "acorn-jsx@5.2.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-HiUX/+K2YpkpJ+SzBffkM/AQ2YE03S0U1kjTLVpoJdhZMOWy8qvXVN9JdLqv2QsaQ6MPYQIuNmwD8zOiYUofLQ==", "shasum": "4c66069173d6fdd68ed85239fc256226182b2ebe", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.2.0.tgz", "fileCount": 5, "unpackedSize": 24093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVm8ACRA9TVsSAnZWagAAumYP/2D+9b7KyJrQ2gg8fyP2\ne+JYVSeT54Uv9ZXg1sHcmsIraBfdJWBQ+Gy2E/mtCSBDycu4w3inwA4zAapm\nr6STcdS9+X6LET+0Wj7Si8kn2lougts3zV4RYWZyZQ/5n/57lEQARzWwjsYs\nROydXknvvC9LSO5eith3FtOkhpKHsDKz5pFRgcyOoer292J66WJUKG0kyNpK\nwPqoGcChXZlKYNnZqF60KRxURXxYjOsxoGxTmn4u+VGQMcP0Rv14EWDGzZPq\nymAf5WA+9De0RoM2pERsgLm9TyFJXXae8ZjXejJaIkZqWsa3WZAwlOqfBaAn\nzWh/AyrftROhCnifFUMzTGjNacF/Gw6lbPtqsdDmg5Vs559JcPnGOn35/Iyh\nwfGFYJqvhCdwpAzzCF/EPXYIGc2WrtV9C5WPDu/moiH73eDylIp5ZfuCwKj2\nEw2GvFUjCVUgj8RmQE4IjEeGOcuXAjtWItPg2fxTvejtwN1SIbIrClLNmzld\n8CqdV550CcJsTVjmsXBLixO+BPQOr1+jDmUk1Zb+o3g3GqUK1yRWxdOw9x3R\nZ+e+U2MHiOZXrrWnh3l3np9aFwVfqFEwprrVawUmm8FcVMAFp/KifDFuRh/L\nwq08IEPdQVbMqN/vS49mtir5VjxBMER94xNasN2RAHqi+x3MgXYB+Xd4BarC\nSomV\r\n=h2+0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDoZWn3wVeds8aUWXjr99qqE2WnsJU7Vev/k0L/En4L9gIgLTW2dMsgWalFN5AQ9bVIWQtWHpsCTQYc4+p1wQlDxfY="}]}, "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.2.0_1582722816398_0.4091437217881111"}, "_hasShrinkwrap": false}, "5.3.0": {"name": "acorn-jsx", "description": "Modern, fast React.js JSX parser", "homepage": "https://github.com/acornjs/acorn-jsx", "version": "5.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "devDependencies": {"acorn": "^8.0.1"}, "gitHead": "4242df8c46286636bd42c88e9a6bc3aa486570c1", "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "_id": "acorn-jsx@5.3.0", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-LWteK/m/DC0Vqise5T1evVEGE7la9nq1JpR7OSQ5mIxPyW/rnRbSnPURH2ReOaU+VpW8hvSQ4p6wOz0+/aun2A==", "shasum": "b036594d592ef8435fe8f819867f033aca219a6a", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.0.tgz", "fileCount": 5, "unpackedSize": 24103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVnBJCRA9TVsSAnZWagAA2QkP/1HZpuCncaOD/YIRBbu3\nJCZBeVdn2U93HPY5cbUXna0x4QTdMYiBcTTXmlBQggsWYVjHSiLGC1ytfY2W\n/ZYE5X+gBioj4XC3LiXamo2YxZYxJwzHKfhYUcUxscgO9liloG97Z0Qq1rNb\n9DA5/Ya01N8q4DAW5ZZpGkmOENbZP3gbOu7FrtfC5U+Cof87pDgVRfpVYrLw\n72J12zrsAuK3TM+YGARU+YUyRouYbPgUCvRBdWcGqNVEeoDukkGCEKsQVccn\nllLhMj8lFtKQ4aU4p+p1eq6oWyDwwNAOEe6Upv18TpAJqBQLniZiHEsDhhMo\n7uCau4hQxIoQGQa3y4r5C7zdCUikrZey16E5DO5akl/6OtD5DL+uDv2WveUD\nFzlYY6ngu4WdhhsKSo5YRTN3QUrx/jZBJLR49C6jmpoevvXkSXnwUrkBfdzl\n6tC2GiLnqGOeXsMPq+oxpvUr2VLvdNZDkK6ClqVMmZ57Zpdlvekm4cDKwqOB\nxjxUaoI1UCKWZHJq+ZT2wiwgD77Nc85k3IoM70lVuxv4r2mHMmxwqdugShAb\nQviND2hq49iA07Yeytp2WyVSvpAKiCoiKI6QWUD14ZOzLk1QfvXb5riI/34a\ngnOD99p/28axYmTCsugLLjjEKzc8rEHJHscAWHEcGjnNLxyRqU8e81wfVBIF\nhFCg\r\n=Ka2s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFJu1ZMc0fJDP4fAkY/TGpqdUEQ0pTSCfq57ohkIdhI8AiA4s9k49oQELPMI1lQdQJtB/WZW0fuk5mg9DLJkiE+TtA=="}]}, "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.3.0_1599500360738_0.2599933968037076"}, "_hasShrinkwrap": false}, "5.3.1": {"name": "acorn-jsx", "description": "Modern, fast React.js JSX parser", "homepage": "https://github.com/acornjs/acorn-jsx", "version": "5.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "devDependencies": {"acorn": "^8.0.1"}, "gitHead": "52a64313bb95aba5fbfddd1b0256aa0fce229bb2", "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "_id": "acorn-jsx@5.3.1", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-K0Ptm/47OKfQRpNQ2J/oIN/3QYiK6FwW+eJbILhsdxh2WTLdl+30o8aGdTbm5JbffpFFAg/g+zi1E+jvJha5ng==", "shasum": "fc8661e11b7ac1539c47dbfea2e72b3af34d267b", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.1.tgz", "fileCount": 5, "unpackedSize": 24123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVnGgCRA9TVsSAnZWagAAC0YP/1SUgzCZhrZMdOEeVj7w\ny728ZaFRbRXd/HaUO+EsNbHKMZ1TXEljOCYc4VzraKwPcT+O7WQc3sRjVPxW\nlByA6nUeNG1lFV97UhRLQCtpX8DSZJ8OvSGh21FV+TbCAXqMJwUK3nbWUJnO\n8SKkHwbdR6mKcQ6uL0L/4fHZWVuAz7Uh9sKbFOpk7g6j9+g/pmxOh7BEMSHK\nmzlGYgYXz5PvtvXA2UvyT622M1LkeaQSSOIGzSDQWaVeeAvdcKGejgpiHVwZ\nUWQV4PaIcKOEQAftAq7499E+U5ZUsW5eVJB4xDRqA2gFkENRvpKzJVyg8Z8b\nQTdXVjNXj6bBicpcWGzqULNN4NJqNkHm8A2Y+lJ33TSbJz0OD7RrNnZRzdy6\n+tOkadkS8LC5zokLOd6SOjqbPvOVjFPuU646ST3rK5aMiTwcPj+puzu5HVsE\n5IArLWTnguPOvg8GwqwboEqv6qxwsfVIn0inpqyLt98NUZ3OjDVTbdEfbutC\n2zj5rLrxm2oGFBd8FlsOcev45BIcAr98NKjXFtcKU9HTwUDANpSc9AGZebKx\n6G0m6+98oLDiqO29oJDm/6eOqBvWBa3iYZbuZYk3uV/nWeZkUbUglcDHjPZQ\nlHVpy5MReBIYVuHDyuOiTLjo4ZDL7tpuj+Z+z//rIZ88Tdz9val2MnPka4Rv\niVtO\r\n=sTNh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA/KqTS6pqpKGfKhZJ2cNudGHRiq3fOfvIAC7LktcV0gAiEA5S1Ff5TWvggOcXAray7YkuajKumWlhiJrjlo9LcXx2w="}]}, "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.3.1_1599500704168_0.02755487933276779"}, "_hasShrinkwrap": false}, "5.3.2": {"name": "acorn-jsx", "description": "Modern, fast React.js JSX parser", "homepage": "https://github.com/acornjs/acorn-jsx", "version": "5.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "license": "MIT", "scripts": {"test": "node test/run.js"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "devDependencies": {"acorn": "^8.0.1"}, "gitHead": "f5c107b85872230d5016dbb97d71788575cda9c3", "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "_id": "acorn-jsx@5.3.2", "_nodeVersion": "16.3.0", "_npmVersion": "7.15.1", "dist": {"integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==", "shasum": "7ed5bb55908b3b2f1bc55c6af1653bada7f07937", "tarball": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "fileCount": 6, "unpackedSize": 24385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg56NNCRA9TVsSAnZWagAA5mgP/1sSkA7Gf7m7A+yKu3qn\n23R//HOdw4MUi9E/4MbxIxpu6hbMvSIwcBvmqjhJmFehNdleh2i9j7XdzfO3\nJxRbnvynhUo/n+UkRy0IyjDV7q+zy9yhUepO29aXjZ1vutyAiI5Ov5WVUReK\n+2jNLPUPIygwV5hRaXtv1SHhRpDe2NyglZQUcB6H72d9NewuyQee8W5lxOTv\nUiP1R25Qd/Uu9fQcnk8s/wpghmOh77d/vKe6LFLj84cDSwURColVcexZ0OWS\nDaaSau17eeWjGB5F6yF/JXkYKPFC1Xdm2YvIx4yTpRU7R3qxcYyZzDLN1mv0\nSbC5J9nlkTBoFsfRRf5izhkFgzvSK0K2IptGxa9A/JZOXnuIxT9r+mw9kqn7\nw04CZEEI+KeLlGwrJQPAQZbZ/HKSqSGHphav3jWXaFbGJxRd4Y6wot8Q4E3B\nfelcUg/vbBMqVtPWYPCr89FCGTyaOe/+PCxg0ed370yIOdTtnBE2j6cQxain\nc5IuDUjNPrQIu2j96FCBuMPDfr3L/u+OUc6R8W3h8zWvzfKDVuFVO3aXCxdw\nkHCBV1zuwS4quR3VLCKlMa+O4L67d/e0aoUg0efCKFGbiNx89tD8g5RoG5ql\nPH1GjUKlHGoQ76xrhXXga0PzSDI1nKfukiJuNIZiE7Xym+ecZXMnL+lJPfZc\nz0tT\r\n=2TXQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHCxbG6An364TA1DIqzXTBiPm5jjXL5n9+2sUAF87jXwIgE4XovKPhCUzO+DAHVSGQcpnSicFlC8WReL2YJkb2ECc="}]}, "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-jsx_5.3.2_1625793357384_0.4248602644946817"}, "_hasShrinkwrap": false}}, "readme": "# Acorn-JSX\n\n[![Build Status](https://travis-ci.org/acornjs/acorn-jsx.svg?branch=master)](https://travis-ci.org/acornjs/acorn-jsx)\n[![NPM version](https://img.shields.io/npm/v/acorn-jsx.svg)](https://www.npmjs.org/package/acorn-jsx)\n\nThis is plugin for [Acorn](http://marijnhaverbeke.nl/acorn/) - a tiny, fast JavaScript parser, written completely in JavaScript.\n\nIt was created as an experimental alternative, faster [React.js JSX](http://facebook.github.io/react/docs/jsx-in-depth.html) parser. Later, it replaced the [official parser](https://github.com/facebookarchive/esprima) and these days is used by many prominent development tools.\n\n## Transpiler\n\nPlease note that this tool only parses source code to JSX AST, which is useful for various language tools and services. If you want to transpile your code to regular ES5-compliant JavaScript with source map, check out [<PERSON>l](https://babeljs.io/) and [<PERSON><PERSON><PERSON>](https://buble.surge.sh/) transpilers which use `acorn-jsx` under the hood.\n\n## Usage\n\nRequiring this module provides you with an Acorn plugin that you can use like this:\n\n```javascript\nvar acorn = require(\"acorn\");\nvar jsx = require(\"acorn-jsx\");\nacorn.Parser.extend(jsx()).parse(\"my(<jsx/>, 'code');\");\n```\n\nNote that official spec doesn't support mix of XML namespaces and object-style access in tag names (#27) like in `<namespace:Object.Property />`, so it was deprecated in `acorn-jsx@3.0`. If you still want to opt-in to support of such constructions, you can pass the following option:\n\n```javascript\nacorn.Parser.extend(jsx({ allowNamespacedObjects: true }))\n```\n\nAlso, since most apps use pure React transformer, a new option was introduced that allows to prohibit namespaces completely:\n\n```javascript\nacorn.Parser.extend(jsx({ allowNamespaces: false }))\n```\n\nNote that by default `allowNamespaces` is enabled for spec compliancy.\n\n## License\n\nThis plugin is issued under the [MIT license](./LICENSE).\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:31:22.849Z", "created": "2014-07-11T07:44:05.808Z", "0.6.1": "2014-07-11T07:44:05.808Z", "0.6.1-1": "2014-07-26T23:53:59.156Z", "0.6.1-2": "2014-07-27T23:23:52.975Z", "0.6.1-3": "2014-07-28T20:51:35.965Z", "0.7.1-2": "2014-09-08T21:00:08.329Z", "0.7.1-3": "2014-09-17T17:52:56.946Z", "0.9.1-1": "2014-10-22T11:10:30.915Z", "0.9.1-2": "2014-11-13T17:26:41.665Z", "0.9.1-3": "2014-11-15T00:31:33.158Z", "0.9.1-4": "2014-11-20T10:24:43.056Z", "0.9.1-5": "2014-11-22T09:52:22.611Z", "0.9.1-6": "2014-11-28T14:53:01.554Z", "0.9.1-7": "2014-12-12T16:48:33.605Z", "0.9.1-8": "2015-01-11T21:39:31.034Z", "0.11.1-1": "2015-01-22T15:29:32.666Z", "0.11.1-2": "2015-01-24T22:36:42.253Z", "0.11.1-3": "2015-01-27T14:31:28.250Z", "0.11.1-4": "2015-01-27T16:29:17.112Z", "1.0.0": "2015-04-30T17:43:16.772Z", "1.0.1": "2015-05-06T07:38:40.228Z", "1.0.2": "2015-06-07T19:31:54.051Z", "1.0.3": "2015-06-18T10:17:12.178Z", "1.1.0": "2015-06-18T13:08:11.210Z", "2.0.0": "2015-06-18T14:54:18.309Z", "2.0.1": "2015-10-15T20:54:51.592Z", "3.0.0": "2016-03-14T20:06:07.321Z", "3.0.1": "2016-05-02T16:30:47.734Z", "4.0.0": "2017-05-14T11:18:30.687Z", "4.0.1": "2017-05-17T23:03:26.935Z", "4.1.0": "2017-11-11T01:35:49.266Z", "4.1.1": "2018-01-16T23:05:14.901Z", "5.0.0": "2018-09-22T12:35:45.839Z", "5.0.1": "2018-11-20T20:47:14.104Z", "5.0.2": "2019-08-20T14:23:56.266Z", "5.1.0": "2019-10-13T18:29:57.363Z", "5.2.0": "2020-02-26T13:13:36.537Z", "5.3.0": "2020-09-07T17:39:20.831Z", "5.3.1": "2020-09-07T17:45:04.313Z", "5.3.2": "2021-07-09T01:15:57.512Z"}, "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "readmeFilename": "README.md", "homepage": "https://github.com/acornjs/acorn-jsx", "users": {"fattenap": true, "flumpus-dev": true}, "license": "MIT"}