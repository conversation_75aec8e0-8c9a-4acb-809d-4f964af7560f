{"_id": "@babel/plugin-transform-classes", "_rev": "149-e8278cbcf5c43be0477029fad660d93b", "name": "@babel/plugin-transform-classes", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "733b26dae8b66679844d12464949f243dd30a717", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.4.tgz", "integrity": "sha512-fYMzGpafsfwnczqSMDtPEvEtimEc1PyeK+ucIF5fBHIIPqANDuBEitnt5L9oSoGRTLrwX6FcsE6b+PR9/K5iWg==", "signatures": [{"sig": "MEQCID5V+pQx3E37boZO/fuL9SfkASOaHjUn9KGUot4DPWPTAiBMBXCGa7VMfAOZg25y4/PxMlLLHT8DtKuC6SSSmNBnbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4", "@babel/helper-define-map": "7.0.0-beta.4", "@babel/helper-function-name": "7.0.0-beta.4", "@babel/helper-replace-supers": "7.0.0-beta.4", "@babel/helper-annotate-as-pure": "7.0.0-beta.4", "@babel/helper-optimise-call-expression": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.4.tgz_1509388592163_0.8387221228331327", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a91ec0c235d694ac97db5c4bb232058b4c9462aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.5.tgz", "integrity": "sha512-KeG3QVPz+MJsty3of2tXaeK6+2/lX5aQczBcF5c8PO6gW3vDqM/d0whnBXCGRfeRevbxKVCg9aLTMX+Gl6Yniw==", "signatures": [{"sig": "MEYCIQCmpYUqoRhfIJELOZ7wBMxI72bXYYLccKShpi7SMNvOFgIhAPTpQNtA4LmXbnOnjro3ZN5ygeRwbJMZ0cPLjpm57h6n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5", "@babel/helper-define-map": "7.0.0-beta.5", "@babel/helper-function-name": "7.0.0-beta.5", "@babel/helper-replace-supers": "7.0.0-beta.5", "@babel/helper-annotate-as-pure": "7.0.0-beta.5", "@babel/helper-optimise-call-expression": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.5.tgz_1509397090061_0.41028118203394115", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "25fc0eedd4277110853b9d00dcfee60d75ad9904", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.31.tgz", "integrity": "sha512-LjUV6wqfOw8jazAjNJGtsyqGN6QNkKjdnYIKbqYYHfG+v2GcIAl/f2VwnoQhV2oqTdZl/4vK9uL+0B2TZdxSFQ==", "signatures": [{"sig": "MEQCIGsTT9+zyrAHrpPjUDPjNz5kcC0tcjr5jXFgvhshW3MoAiBb7xWZnTyhWGw6bqchROu6ktjMYr1b1Ktxqpe15JrcKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31", "@babel/helper-define-map": "7.0.0-beta.31", "@babel/helper-function-name": "7.0.0-beta.31", "@babel/helper-replace-supers": "7.0.0-beta.31", "@babel/helper-annotate-as-pure": "7.0.0-beta.31", "@babel/helper-optimise-call-expression": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.31.tgz_1509739475920_0.39327105693519115", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ccf71f14f08963afe551a89c1a3d3224918f28e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.32.tgz", "integrity": "sha512-cgI+ktFC6FKbWI+AGA4/ia2sE/b1AOpFcYk8PpnNAOLp4IS+OtpGqntDVBXvJvnzo/V6+afrh6sl9WrIz3hYpA==", "signatures": [{"sig": "MEUCIQDJ/WLGb/n7j2fGH2pEjWCA8n4HuXQpms/D0iGntTo12wIgJGBqTouIbE4/TKfyteEw4nyJavK0Wlks+/x0lF/PYvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-define-map": "7.0.0-beta.32", "@babel/helper-function-name": "7.0.0-beta.32", "@babel/helper-replace-supers": "7.0.0-beta.32", "@babel/helper-annotate-as-pure": "7.0.0-beta.32", "@babel/helper-optimise-call-expression": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.32.tgz_1510493647821_0.17350191995501518", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "317b526f2afb2abb14f82ce333575b179d7aec0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.33.tgz", "integrity": "sha512-N1KWuTdHwJv8Yq9zzNorzfieKpx+hcDfeD9Wn0l7KIGJeFzI8HORmNNTz/oTBG2z9RslgHNhsUFHbyMG7x/pag==", "signatures": [{"sig": "MEUCIQDCvFlvid1nBrPgHlAlwOB2knZv1yoFgUAN/FnFq38hSwIgOpxmrfDiZlMzG7ToatPnuHYzQrKlRNz/Qr3Ma4Ul7FY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-define-map": "7.0.0-beta.33", "@babel/helper-function-name": "7.0.0-beta.33", "@babel/helper-replace-supers": "7.0.0-beta.33", "@babel/helper-annotate-as-pure": "7.0.0-beta.33", "@babel/helper-optimise-call-expression": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.33.tgz_1512138565047_0.2373313440475613", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2da769561a87e1fe25f66cd548970e70bf058a3b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.34.tgz", "integrity": "sha512-t7l8FnjgpYOIYAmdttb50EjaQxqt3tRBbzR8jJ1Q7m6qDmaY+pt0XjbSDE63vg4jjWL9N/2IkTWevgd0nbyoGQ==", "signatures": [{"sig": "MEYCIQCDr7wtBgtLGwLXJfJ1v/riQifvfT18jy0tuJJeCLTDkAIhAMwWiU1pHwEQbGyt2djB2n/fpzf08XYbR/HrC+MHbrgm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-define-map": "7.0.0-beta.34", "@babel/helper-function-name": "7.0.0-beta.34", "@babel/helper-replace-supers": "7.0.0-beta.34", "@babel/helper-annotate-as-pure": "7.0.0-beta.34", "@babel/helper-optimise-call-expression": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.34.tgz_1512225625767_0.09926213603466749", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ae1367d9c41081528f87d5770f6b8c6ee7141b52", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.35.tgz", "integrity": "sha512-D71nw+Brh7IWSHiW4/JDux5EhT4gyMYG1WJVjaXl6D6DQhOFlZf5otUVrVX6IxEQaco3B2dlEBDEt/UXvf9E2Q==", "signatures": [{"sig": "MEUCIQDJ/k4J8wOtO2VH59qqDdR1VMIFuqtCAso91vvKtQSOJwIgJSe8w3ZpJK0G7X/OCHH3bVspSslnMgy2dnC4ZEXLPuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-define-map": "7.0.0-beta.35", "@babel/helper-function-name": "7.0.0-beta.35", "@babel/helper-replace-supers": "7.0.0-beta.35", "@babel/helper-annotate-as-pure": "7.0.0-beta.35", "@babel/helper-optimise-call-expression": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.35.tgz_1513288112644_0.8242637796793133", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "98614835cb6bb7356aa8d18d2cc5a109b2da88ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.36.tgz", "integrity": "sha512-VamoOsbL4mJ4xAHvTcM8RvTFhFQ8VXc+M3/VeFgPGFAZ6gOb7dyjuluE2G5CzPSQstMuEoCcoOawTsjtgLWl3w==", "signatures": [{"sig": "MEQCIBw75hMxLon/WrYHUbCEnFgM6zXOOHqIcTlUWk9n1ASnAiAdU9SwvPmoMLizhDaJKGwOsH6ts9a+Hae91DvimYhFKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.36", "@babel/helper-function-name": "7.0.0-beta.36", "@babel/helper-replace-supers": "7.0.0-beta.36", "@babel/helper-annotate-as-pure": "7.0.0-beta.36", "@babel/helper-optimise-call-expression": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.36.tgz_1514228748151_0.49089773348532617", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a933772d1ea55ed16ebe2cb85a1f6f7e482b4e41", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.37.tgz", "integrity": "sha512-MvmMRNdevuWuL6QcvA3G/GYsUwWEtHKrxdjNs8YXTG/aGQ7P3vbbZtvE+z1nBpdVl5/ojuFa1mf3zHZpWkrVdA==", "signatures": [{"sig": "MEUCIArJN3Pa+YCXuELrk72WrY2FLmaqsm1h8pitH68KVjmSAiEAvlERD0unPDd2txH+VAVXflrATz7+Uq95sRrSPdPetag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.37", "@babel/helper-function-name": "7.0.0-beta.37", "@babel/helper-replace-supers": "7.0.0-beta.37", "@babel/helper-annotate-as-pure": "7.0.0-beta.37", "@babel/helper-optimise-call-expression": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.37.tgz_1515427427980_0.9343030638992786", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6237a40053f5e9b7e220431195d7342b1f276095", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.38.tgz", "integrity": "sha512-aqGomD8WO9Ne/agQBxXQy8XhalSF3awhOs/NmicxtttXHblFxEj14HSio1gWKfKpt2Er1d2DnWOD3i2o9X9hQg==", "signatures": [{"sig": "MEYCIQDQfHZsPSp+pwLP0KoMmTa4oNIAkbpXqauEeyopZAoB4QIhAJP6Viz5XZJ9935oAL0yufVIwsRkjBX8okYn02f/uIA8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.38", "@babel/helper-function-name": "7.0.0-beta.38", "@babel/helper-replace-supers": "7.0.0-beta.38", "@babel/helper-annotate-as-pure": "7.0.0-beta.38", "@babel/helper-optimise-call-expression": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.38.tgz_1516206767070_0.6766396348830312", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fe10e8cbcb6914c2b5cfa072d6ce8d9271f52a9c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.39.tgz", "integrity": "sha512-AJZYiskVcTFEB3Vja8EvUovHxBXP/TWTGQGDt4ZIww/7U4QH2YDQb8X6cDiDsPlWcalLwAR38xKZacrAkmpnaQ==", "signatures": [{"sig": "MEYCIQDwOI1X/Bev/blCDP374V9fNuiqQeQCvZjB+1Wp8J3xkgIhAN4VpqNlFrB+3NoVZzPYVW+oDdbB7jrGQM2jukLYcwl1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.39", "@babel/helper-function-name": "7.0.0-beta.39", "@babel/helper-replace-supers": "7.0.0-beta.39", "@babel/helper-annotate-as-pure": "7.0.0-beta.39", "@babel/helper-optimise-call-expression": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes-7.0.0-beta.39.tgz_1517344129348_0.3932464**********", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c7a752009df4bb0f77179027daa0783f9a036b0b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.40.tgz", "fileCount": 6, "integrity": "sha512-yjViyoOYJtt2vLDai8jluxl9quOtq/Xq4GTjT9uzy+mOfUTE77dcJySMGkWHE52Mu3n0TSI09ENBFYykpvXXDw==", "signatures": [{"sig": "MEUCIBVUbLkIgWhxUiwU+QJYzIsn09GnpfIJ3iXQsd/nI7lkAiEAvc7WfD4UcghL7dlWS4Gz/E7xPaZT2SOYI19Al2XM+3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27257}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.40", "@babel/helper-function-name": "7.0.0-beta.40", "@babel/helper-replace-supers": "7.0.0-beta.40", "@babel/helper-annotate-as-pure": "7.0.0-beta.40", "@babel/helper-optimise-call-expression": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.40_1518453774300_0.5899609585621739", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bc5f1e1d7eb1c0a763cc912834f8cbaf4a12b31b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.41.tgz", "fileCount": 6, "integrity": "sha512-ba9vn5U+DQ2qsu0cIMoFwExys/eSv6kb/PE5O1xJjhnVS4y1mFpw7zi2zSW+/hFb/T0/Yri2P9mB4W5PFYpnQg==", "signatures": [{"sig": "MEUCIBUPlX4+0MAlXWAe/jSHZTfG7PcO+7Yf9TdPxbgukgHBAiEAhErvcOCPwvDcOTjcQ5v6TJnNVEWghn33aUi0t9UJuXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27343}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-function-name": "7.0.0-beta.41", "@babel/helper-replace-supers": "7.0.0-beta.41", "@babel/helper-annotate-as-pure": "7.0.0-beta.41", "@babel/helper-optimise-call-expression": "7.0.0-beta.41", "@babel/helper-split-export-declaration": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.41_1521044825984_0.46101361735580304", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3b9fdb2e36f9f16b011a2ddc4ebb610e3dc9edfb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.42.tgz", "fileCount": 6, "integrity": "sha512-0GCv1wNyfMXKuaulype6+TF02Bxq/zQZ4NUbn2w9aQxzIZviAe1jcA7IRrNN2eVQL9L4oi8N6B26Wf8xFoBNrQ==", "signatures": [{"sig": "MEUCIQCSDR7mMJrdKWc9dixSE/jhYRKUjXZv3AGVy/fYWBkbfgIgXXURusLqeUsq+VaVh4lfIcp/WHqJTmCmAuT6pWKw3hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27299}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-function-name": "7.0.0-beta.42", "@babel/helper-replace-supers": "7.0.0-beta.42", "@babel/helper-annotate-as-pure": "7.0.0-beta.42", "@babel/helper-optimise-call-expression": "7.0.0-beta.42", "@babel/helper-split-export-declaration": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.42_1521147134533_0.1896599889618864", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9c367f9b6cd8fb0453dd19a556fa4ff6c2ddcf57", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.43.tgz", "fileCount": 5, "integrity": "sha512-/SeZGeF0fPGlKEyMgaKKgagL93GSy8qvdRj0RUTbKJs3C0UriCKL39/3Uxg+EL8UAhVz2DOIOSTpCI8X/mZR0Q==", "signatures": [{"sig": "MEQCIFuoCE2aor9e0WK0z7IlduFsbhQQ6MwIqEvN8ytcPQ7jAiBdE4p4d5PE1C6lqFh4c7K4VXllpBWgyZ7NCIABW3wbnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26379}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-function-name": "7.0.0-beta.43", "@babel/helper-replace-supers": "7.0.0-beta.43", "@babel/helper-annotate-as-pure": "7.0.0-beta.43", "@babel/helper-optimise-call-expression": "7.0.0-beta.43", "@babel/helper-split-export-declaration": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.43_1522687743510_0.07542845970605683", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5410fcf6a9eeba3cc8e25bf0f72b43358336b534", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.44.tgz", "fileCount": 5, "integrity": "sha512-oQE40NQ9HBg3KJppRrG0AFYmb73mVJUPmFjUZtuMlFJWV4kAyPwfGC98MDJ7fFjGZLIWesJD7yE+eh0e4N2ssQ==", "signatures": [{"sig": "MEYCIQC1jl5uNZHf62FihcfGr92aZCl5OlZHrXTQ4+wIqAHumAIhAOTkDl0jNS5dMy+oFrx07PjklBCYgd1fqYtPSOYwlBkp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29567}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-function-name": "7.0.0-beta.44", "@babel/helper-replace-supers": "7.0.0-beta.44", "@babel/helper-annotate-as-pure": "7.0.0-beta.44", "@babel/helper-optimise-call-expression": "7.0.0-beta.44", "@babel/helper-split-export-declaration": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.44_1522707643616_0.5710959404018019", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "052b70994099c736ce05369d41152c591a7cfeb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.45.tgz", "fileCount": 5, "integrity": "sha512-uTkWdNCSbA9+93oWqRh/OX7/ynSyVT5A55pgrjKnu5ZV8zd+GvvcyAaBt/xdj/BQkd7MkjVkhsjxwWVERWi6FA==", "signatures": [{"sig": "MEYCIQCr/AnI0RLJA89V1OGLMPnsTuL4QRL9KiWWE6uxi92KkAIhAN1swYow7uObQpvso/2mtr6ud1KY6NPul8NpSDStrE9a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3fCRA9TVsSAnZWagAAaS8P/jqCBl7Dlj4HnrrvkD9M\nZuPnLlKx8mB7KBoqELZTP8GGYdOfI3BZf9tBd+PSugCAznMEJeU0Rv9whVQy\ngP2LyZZcAtxvEVJ43lV5Tiu5Qqy6VEfU2fjCkUILXs7bry0Fo2GHs1rT3C1s\nCDy7qzTcGleEMPldDtrzncEHXhDmHcHVJj0ohJ15VEPFza0aakPjgg/NA4QQ\nxmH9DPyunl7X7fPnXS5Seon+wl2BMPyGoBKZxZKsmAqMEiilvdWkH1QfHpQ7\nCAgyccPV9ifEtfXd64DcsBuJArzGJJqrAo+wG73KnC24hAfDOQKIGLmXwgRr\nBK5cqEwX519Tvip32bagPcYTb/n5NZu6+7JHTP2tam60ALkKb7vb1TLcYxhD\nNHvXzqqjDxiFAezYNgXI/U9VBjQXzuwuVhi0CkizDFCg0CcRRNKGeE6Uc4ls\nVGKXSwFm7QbeyimdMQfOqPP98TcVGpDU85acjrL9gNPSNPKafIS3+RTL71Ba\n2gm7Cwgzmra6Db1J79jbmtDI8c3hZOCZiGsEDsY/aXEC++kG20QZbrV/zfKA\nYLtTPmFnsvfFMYCHobn0Px2cbSCveEb8X9tUxkwBNCxzKcGLwPfHauH+6BTT\nioVMrWjVYGBaWCCu/yzESAH7GlWbkZVe0w3HdlMgqfQs/h0RSwo0cmW3uP9X\nw/H9\r\n=ik2j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-function-name": "7.0.0-beta.45", "@babel/helper-replace-supers": "7.0.0-beta.45", "@babel/helper-annotate-as-pure": "7.0.0-beta.45", "@babel/helper-optimise-call-expression": "7.0.0-beta.45", "@babel/helper-split-export-declaration": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.45_1524448735052_0.5314100140337781", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "00c856feda2ee756c4cc6ef8c97d17d070acebf7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.46.tgz", "fileCount": 5, "integrity": "sha512-EDp/qQAURfrX6hNM+VrLSSA+cGiwDeZL0ZTTt6a7PNSFABCw4qwKJHx3Q7me1oV7q3U/GJwPS4Aym2QTDmNGvg==", "signatures": [{"sig": "MEUCIQC5wrp8NZNaxuHS/aEfnc/LuZ17GTQM+eZlvDlTcrQhgQIgJtTr0Sh/IoPB/BFfHRgYvsoepNSWbOz5np3dgH2utaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WIFCRA9TVsSAnZWagAAGfUP/iY+n4ktuwAqhNHhq4i3\nG2SanxEvCNuXK8IHUr8sbHWcBcZACnCdeQrBp663MA9DwWOgc8SXs45HoSKV\nqcnpWRD8NSQ+JUS415e1AwB3Qwu/VG52dH7BiQTlB6mRj+0ddfy3k36lyqj0\nPxooogxDVAlmwgniuOKWrirrufEHwkcXt86QexP8DKTLtPLn3vaWUqwx/DlR\nsqWRq//GSfGZsm7qAbtsqUa2vGs8Rkz5aZ/mzdkhVaPBmyshPYLuzt3qykT2\n4OEnkKkPFz2+c/YGTHqStTe2oFn6WCdyjYti1OZC+XUHo7ccpYbvRYqkIBpj\nl+K6dBAguzg8wzJCJsgnjnzaK9i3RIk34NHyXt4FgRHcgF6f8i2oyX4aqBAY\nsJtZ+JxsnfuYRbVH9goMMsZpoTidrnbMTx4OV5GbRzgLzRRt+uh8tNiJ0nrG\neEnBlKzBCLtyv5MjwtarnVHGvvmV4dt/avZ8rhoo+w2+q03/AnLLHUekaVhD\n2lkpU9QbykqMXfNLh8xFHG6uRfHrMs0o3c3YgDm/gmmrg85/XdGVPr/Zbsqh\nkAkNexrHLxCqI/b1FXHPy36j8TgNAaetT0aOoO9ahc9Vt1yAW5lruM7oxcC5\n5dG9fR0D1iYUFyvgL/bs2pZxjwnuKYCdVTSKpk2mPM/vLQ3nU7pRtqW8vF4N\nL7Cx\r\n=ppQr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-function-name": "7.0.0-beta.46", "@babel/helper-replace-supers": "7.0.0-beta.46", "@babel/helper-annotate-as-pure": "7.0.0-beta.46", "@babel/helper-optimise-call-expression": "7.0.0-beta.46", "@babel/helper-split-export-declaration": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.46_1524457989475_0.7719328295741645", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7aff9cbe7b26fd94d7a9f97fa90135ef20c93fb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.47.tgz", "fileCount": 5, "integrity": "sha512-hzW/jL6TPBMHJXeXwzuxMN0PFAfjVD0UzATHrFSejY5A7SvhWWrv1cZ3K0/SzCXJ9LpMdxCNiREvVjeD/Tyx2g==", "signatures": [{"sig": "MEUCIQC0Z+dm3RNisg/4d1sZV3bFeUek4+L/ZCuR9X5KipJQ3wIgHFi6RRSaOLu8pwto9SmIQvQGpW/6EbRORmzL2+TrgHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+idFCRA9TVsSAnZWagAA2o8QAJAEYm2bFYgaEt88sJll\nQz+y84lctBxm/KQVQaROiy0YNuSqz2Q3UVwJ+hvarsYcssNS9Y3rnMpHmPdO\nYfftGTEsYpEK2WbSIz7Dcdklm+aebY6UWnbZtLn6yD7DG0gxjwP2y01blO+Q\nGPCooya8a4oKi2SE412C2JuzjYj+X8HDhbCLFNfPGOqgVrSOuxTowoHtlEJo\nlCMsf9fpzRhZvQ3qEsRD8ZwWvRIThYK073g1brfpjyvOF/0q9rh44K5I8NXF\nvb6NENPodiGzb+pficseTuDED18SaygYkbxTcNifJIuTxgSzh3RFTgeKSmns\n62D27V+wxPoISyT+HsQCFa7ZXQ+BgiZnM50e8uiAJqlohv2fY5aCpbf+Zfxy\nYjXiOBWgZ39EzmDcj2x9MDbIiSQcegw25nnMa2cdiZWg0ITCliMlnZtpTGnZ\nv2srd0eEa0Loyq3U68AEK15NFpSvZlYFnaYSbDBRqkDB33LHEzViFpSFHzw7\nCqYUBL2+E8SH2QIqPq6KKVHsUOj1e9kXlPBIarpRNf++XSV0Lc3OU2IBaSeL\nNh70HhQA5mbDgD+jZXGImljjuUFS7n7DsNhuTBGg8JqSQ5GQ/P/rKpn1V6dz\nGW8RlUS9jn/Up2IKe7SGt5IrDe7jW/lD/YhK21+IHyFoc75iqWL31KggQ5OX\naL0W\r\n=nbDV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-function-name": "7.0.0-beta.47", "@babel/helper-replace-supers": "7.0.0-beta.47", "@babel/helper-annotate-as-pure": "7.0.0-beta.47", "@babel/helper-optimise-call-expression": "7.0.0-beta.47", "@babel/helper-split-export-declaration": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.47_1526343492758_0.9753365871437745", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5eb5e71bc660293abbb4b9751e3e02d88319e785", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.48.tgz", "fileCount": 5, "integrity": "sha512-1m6OeD+zNheuTtcQcGW8/nk78GVzVo3xho/Gwc0U+/QbfsDRXoVEAWEvR3cuUqM+LrteWwR2hdpMuCahuY7vNA==", "signatures": [{"sig": "MEUCIHjM2NnAQcghKWalz9CwQURkcpoUlQO5j8MKepAyNwB0AiEAuQ7L0vQbfJ35xtxxwcJ532IHdyoKgM+Xc1tiSzQGzjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGSCRA9TVsSAnZWagAAWQgQAJ3rwvedbuJ4mSianb3L\n6EEZyg3TfoTO0ahzoSIa+7Dfp1+7FmImHozEIVb8Zq1enZer5jA6UAhlFyz/\n8ddWbQmiOfj5K0o0+eTk+QvATJrzW2J2aA3+HIwGY41BvmLt1UamBig1LnYy\nqur3gVNWbNXQmZzDpiGCtHMzSWo/G++Rw0XV8m477x+ROQa9KHHRq/xSV4yB\n5WeZqG1C0s/YLa3K81qTdDdJXUbI4qy7vOhKS/8LGxKKtVtbepfh67gho1K/\nQgDvVbDhAu6eoFfk59mCdYQmwOLfRt4c92SkokMm9Hm47UcM888p2loC3eZ+\n0Ls3vbyWLmZmwizQwS501Wg+g6856nqnBIqWRLuER1QRBjAV/MK5JQ4YqTog\nwLtni0r+eOK/pf7z/oCO1zvSpPenLsk70xYIWUJ4lameMr9DguM30LfcVzNx\nSy2XyYlWWpkSNNK9HtXNdwc6qZM8PDzyi+SDM8N81MUIxFxaKejlgzd9LsnA\nFwwO2SKe2PDl1Yep5Lb1SaWodpdSgDwUJZe81BIzfy6miGAuw5wpVyolRy/7\nQGMLaVQtIecZsUowCNeKIfRfIvMlN5sZmKar+h16SC4g0sSVuOfN0SXMhf6f\nUdV38EthBdwcehCehmIBj6RAyFOZD1dv7/BTjbVE9PByTJEed9IxJUZjHkOw\nSRLN\r\n=A/OC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-function-name": "7.0.0-beta.48", "@babel/helper-replace-supers": "7.0.0-beta.48", "@babel/helper-annotate-as-pure": "7.0.0-beta.48", "@babel/helper-optimise-call-expression": "7.0.0-beta.48", "@babel/helper-split-export-declaration": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.48_1527189906198_0.9075206722523417", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5342471d2e6a3337332ea246b46c0bddf5fc544d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.49.tgz", "fileCount": 6, "integrity": "sha512-TAaQ8Pv29o6nZ8qQjxeCdQHuU1CdhYXPKR3IwLRDdxMDoa3sG82kR0ZbOiM1rqaHMfq6HMr/GURulx7+nbdqsQ==", "signatures": [{"sig": "MEQCIAex7xdB8u7laAms5US5NMC/ElZUnJmNbCmPxqJxtFlbAiA+8euShGmWWMntEvofopRfddnLMYRgw764pBRMT3HqrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQuCRA9TVsSAnZWagAA3/wP/A3M1cSwzPXW5lleA00x\n2h20uA3QDa8K5mCFCzq5JD3X+e4fPTFD0xjp53twVI+ZK5cpx8vfzML7CWUA\nygNX4QrWjBOcDNBiePTej18hH78mO4SEh/YmCLCLM5j8PAqq4AEt6xSUZ6vb\nNTrWHtYBRZvoJu/TVccmfXO0sK+cmKpQc1V4eekHz5H7+nIGiM5euOTs5W9T\ndx/A+ukmME8IdgZ5gC9poPg3K5MpBNt2vA3y+UKSR2LUCqraUmMlBTRO683F\nWghTLp3TgiLUvEdVtUExitoq7mEXl5tqdtqrSLv0FPbq9RzSki9Hg1SSIC88\nNW2cxglvZ9LpiviXU72O0IqtxmVpxLlB2sR9IDQwy2lFe1B8ZE66jOnn49Ad\n9Z4PCSSA2Pewwj3sTG0W4Nd1kkl+K6L/d2OZMnIOWRsXwR0mYdqFKeXOW6Ik\n98rxhE8cFcyevY3XmRp+NyUH9T/P85jqnQXTMmH6ILyDOnCsDng6M7pFlaV7\n2YC84khp/Crpl+0CJfDXCYk+nTu01ESp/DpFayNI22SS9qZIHgvv6N7G3G7W\n78vlV1T5hT7HlOtVxI6bwf/6kXNLLE12PNLdRuJDP3N14fF9a/i8eYbTeE66\nLwoQSVTBFS0qVgDG8JPXTZRuUMfvtNHedj7c0x00KBKKOKpMI2inwX0nkH3y\nkC3F\r\n=QoUd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "5342471d2e6a3337332ea246b46c0bddf5fc544d", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-function-name": "7.0.0-beta.49", "@babel/helper-replace-supers": "7.0.0-beta.49", "@babel/helper-annotate-as-pure": "7.0.0-beta.49", "@babel/helper-optimise-call-expression": "7.0.0-beta.49", "@babel/helper-split-export-declaration": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.49_1527264302061_0.8131129751991513", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0ce5b8bc96937c2e26f96b533d9f0fc85730a587", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.50.tgz", "fileCount": 8, "integrity": "sha512-oHz3XC4Sv8GIXWXzvSEN7CbxCFl2vkG8NUOEKXLKGUoD4B662UMyv8SvfD/D/FOQOvSkDtFrZtMaImIh/dLL3Q==", "signatures": [{"sig": "MEYCIQD9m+fSxOWFxMNkcq5+4B8XF7TTzGag3nqSAAEqEswT9AIhAP1dOJQL2WqMo+zrHKFongNdXvzPh/L4b1VpjQxu+qj4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24407}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-function-name": "7.0.0-beta.50", "@babel/helper-replace-supers": "7.0.0-beta.50", "@babel/helper-annotate-as-pure": "7.0.0-beta.50", "@babel/helper-optimise-call-expression": "7.0.0-beta.50", "@babel/helper-split-export-declaration": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.50_1528832901748_0.8039169074654595", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "043f31fb6327664a32d8ba65de15799efdc65da0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.51.tgz", "fileCount": 8, "integrity": "sha512-sjzF7KDfGX/jMJ1RAPdupYJDCVvpANtWICknKUdPVaMz22egy1wk7/l885XC+gAJjF8+SFkqOku006bKTI8xsg==", "signatures": [{"sig": "MEYCIQCvMwUTxRIJ6x42rvw+Yh5j0a7kW24au/lJRjLnsDlSCwIhAJ4AjMx0J7DzmCmvrC8X+N+J3kHtZfigFQTCvhLz8rPv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24421}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-function-name": "7.0.0-beta.51", "@babel/helper-replace-supers": "7.0.0-beta.51", "@babel/helper-annotate-as-pure": "7.0.0-beta.51", "@babel/helper-optimise-call-expression": "7.0.0-beta.51", "@babel/helper-split-export-declaration": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.51_1528838460161_0.6423719974642559", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "08b1b664a7769b685c3ece2f3eab01832f272019", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.52.tgz", "fileCount": 8, "integrity": "sha512-V8qk28x7Dd+pb+GLLuuOXhTasPEhsHjwcewVIa2LoKNikhjULtF31bS5qzuOPdpD7LhBdDJFQTZZCLIdHoNeJA==", "signatures": [{"sig": "MEYCIQD/xIpsdSqVTTISnFq6wx4dYBRdeaHyn3Gmimukp2fSbAIhAMC13BStUs3GQWMv5cdnfrIQg2+8gdv2A9pgc2tJkgMC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24420}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-function-name": "7.0.0-beta.52", "@babel/helper-replace-supers": "7.0.0-beta.52", "@babel/helper-annotate-as-pure": "7.0.0-beta.52", "@babel/helper-optimise-call-expression": "7.0.0-beta.52", "@babel/helper-split-export-declaration": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.52_1530838791262_0.3905755544942313", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5dc2ec31bf1e98066acdf0c4887b7744c14bec6e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.53.tgz", "fileCount": 8, "integrity": "sha512-Mv/3NVDJ40aawBuwg+Yy776Qynmo8FRM4RLtGk+TyIH9PKw83b1jL0Gxa1OvzXjBiizq6oQLOhUvWnmh1uSL5A==", "signatures": [{"sig": "MEUCIQD0VgPrVCsZEFCjokCThy+Q6nF+tJZG8ljSvNHRMAE93wIgNKpsVY4KQLgL887F2hLYp36W0I4CAWtRTI7q+nRf290=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24420}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-function-name": "7.0.0-beta.53", "@babel/helper-replace-supers": "7.0.0-beta.53", "@babel/helper-annotate-as-pure": "7.0.0-beta.53", "@babel/helper-optimise-call-expression": "7.0.0-beta.53", "@babel/helper-split-export-declaration": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.53_1531316454286_0.7573259402960977", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b15781d2e499ce25438e73fea2fa5a09858568ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.54.tgz", "fileCount": 8, "integrity": "sha512-plyhnaenTVJkpJbPCf9q3xOs/PXqtHiIN9JSDXQVxQMzdl55OYPcn9HmvYmjOgMwJ1DZ4vDqtew8ibfFmRv8Xg==", "signatures": [{"sig": "MEUCIQCJUcdX0xQpEp+gHPz0LqsxAEfoyZ/wzppKVOoWX5NLqQIgTfSjU1lVHD/44GuRMCd5SLG6eD2/4/0Isx8/nzu+RcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24420}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-function-name": "7.0.0-beta.54", "@babel/helper-replace-supers": "7.0.0-beta.54", "@babel/helper-annotate-as-pure": "7.0.0-beta.54", "@babel/helper-optimise-call-expression": "7.0.0-beta.54", "@babel/helper-split-export-declaration": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.54_1531764032283_0.5352012856347708", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fa260266943f7a1e144ef9783d9a07e987755022", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.55.tgz", "fileCount": 8, "integrity": "sha512-LHkpv0z2YmlOCbNyJQ+++Ey9+hP0AOP5uY57JjdoieQwfI/lu5zmEyyYBAf18LSP0DrF3R3SZXfiPqH+abC8HA==", "signatures": [{"sig": "MEUCIB7C5okaV/VYe0ibmOcrjSesdBfIruelNa3hzbGZUM/sAiEA9Wgmi+VoiZ1cxuuTJ9KYtjWrntDXxW4Dwm6T/gUcCtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24420}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-function-name": "7.0.0-beta.55", "@babel/helper-replace-supers": "7.0.0-beta.55", "@babel/helper-annotate-as-pure": "7.0.0-beta.55", "@babel/helper-optimise-call-expression": "7.0.0-beta.55", "@babel/helper-split-export-declaration": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.55_1532815717025_0.06676149622605143", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "99b405dee3fb466c09c1e0874e795f4682ee690d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-beta.56.tgz", "fileCount": 8, "integrity": "sha512-uq0Nvjlkt5gpF+dlvJ1yOZu8liBfOp3QoA/hrP7LZ6XzmYwZOhIUpUbouvKjgvybWiDmNDGcELeC96CL/mtV5Q==", "signatures": [{"sig": "MEUCIQDvUK0HzIbe8K26yhcX40GPERYqW3Hzb5O/h5pmDuh7ggIgIX6FYvIVZwa/rAfbwyYggrVKP1g+EnyS7/wucH32PaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPy3CRA9TVsSAnZWagAA7H4P/0HPfwFXdwO4FdJXxXdT\n6brZuugPYv0RWv6Os+gJzgalFLqfSOYHJHw/ZwzY/g50HWETu/HOuG49vaWo\nSHlqwn6itZwcYsiyaEZQW1d+C+JggaJsTtofcF0OHBH4zkxjzAxrZRBKyuLP\nuhDf2vSvq7gJmiCJ3Ai01hgXx/GFncy9JmaxwYx52miJz8dFNnRA6a6TRxTG\nRUUHgLcVLH+5S7b4R+xjz7iiHnXC2O7GydtYHOpWdVcXnQJrnTIf+HhMNp/r\nh6KYV4HHRocNEixXkomrP96Tmti9HEWuK+cw//+8aL8qQOUt096ZERM9010S\n0KHj3+909ZNAsi9NyV9gH475jxfF8s4wpiQ8Frix2Oxf01DvA6c1zFoMCt1h\nblsQEOVT6qXSbA6qEN0/v/wvrgJuMRDrWDLO5K8vsHbVdDa9c3uc337E0rhb\nSQzWUsmtYBTUfg5pVQ/zICkweMBwuUxjXJ4PbvNX3ilCvc4usnonBS4eOx9E\nBOwD0gjOOBvLHRdVFXL5I+9FVGypycn4TChjwLdSwcmfNu1syKYdh8IxDASg\nm++j8Fpe0o7qA2EYaAVhfLAzT6tmWN7qIwPNAV/Axt41ynfb1pRO+tK0+V5/\n7/HOV6uhtLsCbVTYrhC2yiW0eIj9+KKE5tQ/pYLt1s5QrgwD9/1ZE+7XQkjw\nr7oD\r\n=NSFa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-function-name": "7.0.0-beta.56", "@babel/helper-replace-supers": "7.0.0-beta.56", "@babel/helper-annotate-as-pure": "7.0.0-beta.56", "@babel/helper-optimise-call-expression": "7.0.0-beta.56", "@babel/helper-split-export-declaration": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-beta.56_1533344950712_0.3371561052285519", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "871b93a4339d301080d6c0362471ff5828539b20", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-rc.0.tgz", "fileCount": 8, "integrity": "sha512-ekaIv/J7T+FaVif+zYE86RV60m9OvPmlo0NITRtapFWdkuNOJMikUrh3D1geuLGwpYBkxRUi2CR6GH/Yza/E2g==", "signatures": [{"sig": "MEQCID8OvW2EGjynq6U6Li6qqNp7i3veRZQIFy3dHIfwpVnoAiAXEesZw0mN8OyqW108StX46akIUepuKhGOxt4kBd/6mA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUdCRA9TVsSAnZWagAAAwMQAJTPWBtYUoUmow6cTJtu\nwaH4OpWK8OnflbfrkaoJqx+WuaIVMYIKr7MFqon1Z6XCzkfU4xQK8ZtvSdnZ\n3tgABoa4kUstzVah30dJD3rKSx+VJD7WmLKQqOCY1l7BWyDYf5GO7gZkNOmZ\nYWT955DjkMY3/dnFy6v1XDn9SNPoCf2hWmz9rjW/3yEeci9BJF7kjAxkwS2G\nJkt9dneka78PDmpqp6v66wq21jC9sxCwReMjtr10WSEHYFDDVFbynhmQkPV2\nJ88FhWBHEB4dbmYoFuGbZLoaULhuyDd9Kez50tjHVCtSfa1QgpWjSoNhpojC\nROMeJk4htIXTAmAQz/Z3r0M6z7eFNR3jSRks+TfFq6z3FWgdyLHwcZdT73jJ\nc903RH8rKKZxQeXQh7qv/DdYfCuNZvvYEHjNlcMdZXU8fWGDVGZeXRtVQ3vO\n7HYoUAL+O3A264eLVCIki2PLBaA5usQsT+4bXvUDybNjLS65V8KEvx0MT4T+\nkyycqEWSiqZYZWzgioZ6qCtv+6H0U3eWBpw/9RSqTR/LwKActZ8uK7+tW/Nl\nQadepPGu/bUALj94RUjfA02sGmtqgGJN90oli3W+BXRuwPo0jKYUUxguG9Ap\niypx8mzGRsmG2e0DkGj9TDsXmmL735Fqs+/Qjb3oZoC1yFEJyNeBuLhkIx30\nGPWn\r\n=0W2u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-function-name": "7.0.0-rc.0", "@babel/helper-replace-supers": "7.0.0-rc.0", "@babel/helper-annotate-as-pure": "7.0.0-rc.0", "@babel/helper-optimise-call-expression": "7.0.0-rc.0", "@babel/helper-split-export-declaration": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-rc.0_1533830429354_0.14034962692372677", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1d73cbceb4b4adca4cdad5f8f84a5c517fc0e06d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-mPXMbQR8zNHMXvaJ71wQ7iPcQLHPv12XjWwvYkDjtsEvknDQ2HWA+UYZGVpZ0bv3jLQIZuwc1kZ6f5vSsavvog==", "signatures": [{"sig": "MEYCIQCRTV0LpSxZIFhFB+ctXNvJsNz821yDzGLB2u0OPLAdgwIhAK3wTaRdkc8UeNQKRRuHUswIplE2df7N2swq8BffFUTQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+lCRA9TVsSAnZWagAA4PkP/1LiH7aN0kEaMykVvCpp\nx5nUMryXQ+jbBlyYCvqyxpP2wH/wx6hcrot0kfXK5K5TN4r9vznzvQXVq1AY\nPjXZFQBAq+CKMH3VkoFcYxqP5XbfzP8lPdvaXGFDCy0Hx8G2kqiVW033cyv7\nOWD0+pWpKVMjiBo2eX/qZl1IA27UPclaEUNDlTu0HbRyVPjNTHXRxnk3+5u3\nTH2rlIFaY9uxW5XBIcl8c5IgtoY7351hKxxKsjHvuI6JcYYPo7ncU+7xU5Ia\nd3N/UyA7BWAwhWT7LF7Y1TcpPKin6NabOHdSRcAMKfGFWtO9oW4SnBv57+9K\nashBxMKkcvaWu4Eb2ABZtQheN3guNtTRn4uIjFyNXnTK/SZDVdpjuhwwglpd\nef+3tM0KWkRJPC4nZqqNhovo+QSvK9i+MzvClx2XqrnZwjRcP+6c7TxEEoGg\nY/mJkY/BkBZSO+mkD+5YqsutWHapvDP+Skk+1Y0PkljcgPEckygXHTuSAjwr\nx+p9jCQZhouuVVXN/Vfsi7VOQv5TGHrAMruz+r/WJmJ+ACEEH3D+bnzDB/Zs\nUDswo9C6dZiT+UYqOIyRyHIERQTRGLTRJOeSMfWf98sfsxiApZ7n3QfHpC6f\nZFiUltPBtoCHnlWRlpJbsobLmApvfNgE7HyiRxhoXvZ/KyXyKcWQWrmgiOdi\n1H14\r\n=ZSqi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-function-name": "7.0.0-rc.1", "@babel/helper-replace-supers": "7.0.0-rc.1", "@babel/helper-annotate-as-pure": "7.0.0-rc.1", "@babel/helper-optimise-call-expression": "7.0.0-rc.1", "@babel/helper-split-export-declaration": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-rc.1_1533845412533_0.5259741920512397", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "900550c5fcd76e42a6f72b0e8661e82d6e36ceb5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-rc.2.tgz", "fileCount": 6, "integrity": "sha512-VGLIza5QhmNBuSQ7wRYl5vSjjb1jdYIJcJsJ7Kgv5QvAasFbjjVAhqiv3/LJae7IJqetjNYN9V+2Bec64399tw==", "signatures": [{"sig": "MEUCIQCBpMPmm3xSkq9qEti565FPgx1mP06WLqqLQ3wiWBwHHwIgdbq/kOmBhc8hVBtQUYILVclAkuV6AVCGr61ylhGzHmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdYCRA9TVsSAnZWagAAn6cQAJvyjasv729+4SAR4i5Z\nTQUYumMGz/MmaoFInPBjEj6KfnPq0A0ei+UepFpwHdq3hwstaxdLVQx+ahXb\nE4efXWGLiKRw0s2ZW/tfcMRTZLlja3rrz2Bep/iZCcsA5AtgPhKvOff2YiKO\nyqDAVTF208mJglZ9bPT2fnIPjxtHTlqX+qjy+edmfcA5rDT/OhmteX1i5KSg\nyqV6wrDxnlyqaoJ56QwiEl2WVEUOgKCh9QeZu5xmwy+Y02mRnu+XoZ5xlL+L\nwLppAQ8thmvAoolb6Vd8JnC3e0EOa5WlGNSUibPjVhR28sErVJybCKo2TZOE\nXXwFId8FAn8S0BL6jbjd87TskflDRedEdZmYnLzqQ9+0Yb7cR6uhDjhy/+vh\n2z6j2la1Ck+m13gNZKkIZ6cGMyroghflYdgqhVUs8eWqI5XX0K85AhGlOsLW\nUZJZazKY9nd9pqHSa6h5AEEmaHJl223nOSctmqHa4lwyjS60eYho4OK/NxIa\nvHtzZ35SncvMnBuUcdbrQn0Gly2LVdS+lmCINLcT8cpI3HNH/4+jev/PUzr3\nsaS4KVmKcwWLBS6L9W2OXedfjS0CcZKAnZPFLA+LVn6tBc+XK3KaJ3B13+91\nw+DXCaIssrxSRQSTDq+jnt/f3CbKk7YphcCayuNkup8DT+vySBazKPxEOCzw\nWXTz\r\n=vY0s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-function-name": "7.0.0-rc.2", "@babel/helper-replace-supers": "7.0.0-rc.2", "@babel/helper-annotate-as-pure": "7.0.0-rc.2", "@babel/helper-optimise-call-expression": "7.0.0-rc.2", "@babel/helper-split-export-declaration": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-rc.2_1534879576373_0.6722572888200833", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4bc40e0ab871169e72a847a1d031fef8c34f3fb8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-rc.3.tgz", "fileCount": 7, "integrity": "sha512-YukadsMOJreB1iZDntIEq6t3Qh+sVJ9xZZnTB9GlcIqf7oiILSQQsZsc8wQGl9XmYXd4hA47hQZpWN9tDluIJQ==", "signatures": [{"sig": "MEUCIFG0yBQdnG9cgQRLUGRhSBKTDBMGA+j8+lMYiPoxK6TAAiEAv5Qhy9iBKFp9Qwgcd6rEVR7iylWSEU31EHL5FOwd81I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEn5CRA9TVsSAnZWagAA2O0P/1Jixc2g/kTbxX33Wrw3\n3Nha2AAjaPSa13RFUYMWIOMyhKSsgemZskxQ+Mgv70F0xIl1VXhKVMNnr2pc\n7MchG8XfiHGuGWb2ruyM62zvN+B3pAEVxSy+GLvDZL/PBwLTWEAwiIkPghmJ\nPaKlM7XDqAk1A095GkZc+lvSGavcYvp0AcqREKgNeEsfeU+D1rEck8sJzHvL\nmbsII5nsFe80u3He9rcBew+Jzsu92aPDoovEpa1msYuB9DixC9lOH5yprdgI\nLbOLBhCN7CdfySa1Xs3D/EPpVV9nsFGqvb1BVgktljNdFVVm0XVQJzR/pyp3\nKz0SfALKYIYl4Ii+vHadOrMeNb/W+sDLhycDgOwZEL46QBCNYRf/IUUG+Lsg\ndwVIcM7g2/uZRv721qA43rtnK/o31ANBN4CC1xQU9zHPCrJYmtPHq6A3//nO\nl0wnvVTsBh/tMItYED7fszqmK5wRJB0spJ9Jmze2uHAsbQFPYf1zulaVNiA4\n3j36rtxUUkKPNTkcIvEWuCdXgI/igOTCQrn2r2h5ojwhtuYGqBT0dTSHFVzc\nuFyC9myuJA08hC5NNxI7KwwD19UxtwNYWCuTZ6R1vsNdbZkTFQ9iznAAkUqx\naCsRsZDWGcIltpqBot0x/pLXm5elDIUok3rG95zs6Nr4pq2sIKzqOZ4E6D+7\nJvRf\r\n=1E9q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-function-name": "7.0.0-rc.3", "@babel/helper-replace-supers": "7.0.0-rc.3", "@babel/helper-annotate-as-pure": "7.0.0-rc.3", "@babel/helper-optimise-call-expression": "7.0.0-rc.3", "@babel/helper-split-export-declaration": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-rc.3_1535134200169_0.9773603396098582", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-classes", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a989681c85eefb03272bc381456175e368d32cd7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0-rc.4.tgz", "fileCount": 7, "integrity": "sha512-cVTWum5Vhqk6cq4BQU1FDAOCJkhOqORu2FySUJCpFQcYRSwgMEXKgccuWZ7+V8UtYQ1Nk1eLdwC7aktlRY68QA==", "signatures": [{"sig": "MEQCIHDqAWxnZU0oKWOxpRqHtRLtI5F/wyIV+x5cWsodlLe8AiBwlYuiyZy86s+gBwdtPg5SR+L0eon3XjZTuTmEajrXoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCr6CRA9TVsSAnZWagAAwX4P/2AfFR9WzCyB4joJbuNT\nU9+Xz/uUobmTpEXoeyRUEnJ624paEj3A3BKv0K9GiEtJAxXX0Et4+vhzOeEw\nyQrVJUV0rJ7O+oo3UR4YpK20841wBsp1FAJZKqlIozc+oXXSSeR4zzWC1NNi\nexAMPXKrWmhavcMjur1pmZiZV2y3JKod44SyX74dsz8h/IJnvBv6NfX4B59W\nXwKbaUoYAllJYzK7I4sz9FC+wgRD3sB54+GACVBKmZhJwJQS1l7JIEkglFvD\nmmnjXiSqF2muPq+AlbWlcFT1Mk3wkzwPgiQ5EVZ7sbk9sZQhm4DVlUqgoFRs\n7sbhbn4pVGyEzhx5EeZLjJNSf3NFNSAMGSgDYBmcd90QLcevr5p1hL8b+fdY\nvarPh4Mjtm/mxlBjFOJHchBJqWQmuoCYOAA9bUx0ZErhTvuCLGub8KR86hJK\nLggwJyupywUcE0ahKwvjA7WJ2pq5E8exBC/KxWaKyIxH/ajDfzKO+lU7/E6y\nZQhf+M0KWVndlv+LmOm++wQUKv3XCJgBHe7zgdmkdw3YLhcfGk0zL8buXgwz\n8Hb2gqxFFeyA4EoJp4OYLyRcMpJtLYdpgOdpQh1MuOWbOIfnEQ5S67BnIqGV\nrtVJB5E+XYU/NN1C47e1mD3+oGBSk5jxmQiiMu27RXGyDNonPCZ+s58lFxvl\nVTtj\r\n=1gXK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-function-name": "^7.0.0-rc.4", "@babel/helper-replace-supers": "^7.0.0-rc.4", "@babel/helper-annotate-as-pure": "^7.0.0-rc.4", "@babel/helper-optimise-call-expression": "^7.0.0-rc.4", "@babel/helper-split-export-declaration": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0-rc.4_1535388409561_0.04312618064762952", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-classes", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9e65ca401747dde99e344baea90ab50dccb4c468", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-8LBm7XsHQiNISEmb+ejBiHi1pUihwUf+lrIwyVsXVbQ1vLqgkvhgayK5JnW3WXvQD2rmM0qxFAIyDE5vtMem2A==", "signatures": [{"sig": "MEUCIHrVfsvOzX7fWG8IZUFsLgJKg6MXLmNfhFERqouV2du0AiEAgQ62vahuFeGuFEYkIzGe5s1nLKv/iDt4wsRGXDOTEjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDrCRA9TVsSAnZWagAAlgAQAIm64vAKYTUOE0lgE1Go\nUpavkpUMSVHWmhYIvcd1+gzml/mmPppkRG0IR0IMumJgJfbQWR/0Xjy6C4KL\npB/S1fJ7CwWZOxHheQAhkox6IL8YzC/3VPxnTTz4TMpgTBI+Si++u9/8A3BY\n9d9ROJm2AAy2H5L9b9lv2hL0qTBs0aAIxnESbGi3Id71i0JsobZqvADDWhMu\np00ISxlJ1DmH0TC7K9AYSHBH3BOrZtaLIpdpzIQXse2b9B/Bdhf5AqOvJKKY\nTYlos+HVHSJ+gvBTLGMCXrQqMDL4M4LUmkCeP/Y7FCc3+GinG9wKNqBFV/vf\nVgC0e9Loh+s8SejCjr6MGrtSIwG2l/7hp4p4Im6YCipogn0Elz7hvLgvOwhj\nYSnp3QsVqASX4rUGeywGrX25N/Oo+biggz/CNQ35SCqoC0np5LLO99iz4WiH\nQQSh8RRqws3XKK2XM4zSK/DmVQCG2gUzvTc82ycwhLfv0MJpXTk4h+X7xYZ1\ntXhcaoa+6DIk1zQwer86+eUuziColNBO9iI8LnCtTEebmv+Z+s8B3sewum/2\nMG2Z8ToHJuWzrjUjRfojLu4/nisJra5c/iKrcqQykdQOjZosfrQQVzspeFSF\nmFbtRWxSUbNfpoKoMZU+pYWjagjbXE/Qsxpz6gYz2Krj6jaStr5RLu5TBRRs\nl9hc\r\n=/pYv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.0.0", "@babel/helper-replace-supers": "^7.0.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.0.0_1535406315071_0.968139918068402", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-classes", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ab3f8a564361800cbc8ab1ca6f21108038432249", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.1.0.tgz", "fileCount": 7, "integrity": "sha512-rNaqoD+4OCBZjM7VaskladgqnZ1LO6o2UxuWSDzljzW21pN1KXkB7BstAVweZdxQkHAujps5QMNOTWesBciKFg==", "signatures": [{"sig": "MEUCIQCE1JNChr+1ntZmqxqB6iPg5sEUfx2MlErFi5J4zLL4/wIgDyii4uO363DJRQob44GdIEtLzPa9c2AWErEJNfEMT0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAEPCRA9TVsSAnZWagAANPkP/1SZzDoQUmR+pFEqcGeo\nNUeadDxSUwvho+8pntCzwPVuAgfwuzhHdDTaCe50zatfqaZofHnuIH9t+Rnf\nRdT3Xm91dwF7dRkmymjk5+wBa8WxGcHCgVVd6tzZga6rvppQS6+KY8Y5G2Yw\nBhCHeZGVKYuafPL03w7+nRqBI9CmontTdFBxhTHUnpm8St6ZiEnJgNeNTz90\nKElfUGngS+0kna1WHwdQAQwvDpNbWHd+ksGFe+lFVGjZrqROO+KQCBkQXOVU\nueWxcwWibIqlUb/fx7uxHzShmwNA1SSNN2NarKqYE0B63+4nmn0lFRHaOM3h\n2NEFmGRdAr4/4xSURhID6OaR/so17VQVnkodB0j10wx+C7raPbUel6J0rNbl\nF4yJoi9tPL1gmP13LiYSltFC7DnO2zYdAnxJiMuhvTxacC7Y4Fux9lm7jWWB\n11CxZzZo8R8vbP7zFbAcgV2GwZwSZfJLahj+T4fd890p2P0W/LU5Y8a4TPF8\nfc2sxdmmxeZf0g74aBu0am6bRQx5L3QB4ihftjaBKfW5UkGX2PA3OCz9kANY\njA2SDmihI+eso0mFMazNBtsG/h09xrqGNX6wiaHmWMQG3ZqAgVpkibwCMmG2\n4PCxAZGNH0m+5D4p2Al0VNlCSSqr5bO9+rO8GZhD9FQtn+isMC4EUHxq1uF4\ndfNG\r\n=qdXY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.1.0_1537212687115_0.5938052251303909", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-classes", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "374f8876075d7d21fea55aeb5c53561259163f96", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.2.0.tgz", "fileCount": 7, "integrity": "sha512-aPCEkrhJYebDXcGTAP+cdUENkH7zqOlgbKwLbghjjHpJRJBWM/FSlCjMoPGA8oUdiMfOrk3+8EFPLLb5r7zj2w==", "signatures": [{"sig": "MEUCIEtxhFMYRQvRfW0EwcbmLqXbnY4YWeENpL+E0iSnwHHiAiEAwmZ0RKQu7trH6PzBqqTzTO5rilgqOXevZZliDM5aAq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX15CRA9TVsSAnZWagAAmJ8QAIalNXhbtvmnNWdHE9SL\nM9FMFXwKGwkaZ50lYkYybDyGiZ2xbzgxvRbzj5WSpGNg+Z92dH3dAZlF62xa\ncNbEDWX49Ks8Rh5YBqiUSA093VjaxPiKYqdpENsa57F0vHpQE1/DexXbONnt\n1XquVGCzQqIaSrPPobmQeFwZxCDrBf/njtYNr8z6HwEcZykfdE/SS2hr4VNW\nfDMUZXsyZZamY8GwXolZNNWg95mvIJ8yVEo5lzj33idk6uiWzvQ7c6TqDby3\nlrJqCjGDLRtbQAML2ls6bVm2Ajk/U9wdWRwrmgypVuloA8jzDoY6o/MjTV6S\nEjmpOtkIrPrcrDduX5cD+i5KzqdGsk8kUXxQ3HuX0bHetS9IJ8u5QEHxQmMa\n2DMk62Puzz76MolxlLi02a4kG1DLY6Dyfy8ijaeeJFYj1SwL0vXp+y4bsSBH\nrvQJ94xwiAVc6xWIJjs5c0L2AdCAPNy24z/zLY77xAExLqujSMaaJSG5UF49\nogv5P6xdJS9tqTHYGwKUKPIRrXtFYWIMJbu2jJZnHObUxk9sD+nTpCwskwxa\nVzbQFJJIBWACUTWorO8GBBcaA68LU1CWWh2BfCEIqF+YGmnDZDmM6kpgersX\nCCL4LdCDKM/jiVDwsPoME/k8CMB3yuLsj2pCJRzEDbBDasvXJYNA89x2/Bt8\nubOn\r\n=ePUq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.2.0_1543863672560_0.0889654306147114", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/plugin-transform-classes", "version": "7.2.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6c90542f210ee975aa2aa8c8b5af7fa73a126953", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.2.2.tgz", "fileCount": 7, "integrity": "sha512-gEZvgTy1VtcDOaQty1l10T3jQmJKlNVxLDCs+3rCVPr6nMkODLELxViq5X9l+rfxbie3XrfrMCYYY6eX3aOcOQ==", "signatures": [{"sig": "MEQCIB56kjkoZqktv8poI2vxrJEpHO0gbiA6t9FHUljwMmJrAiBtuI+OoNJ2QWbWeER26XXk+oYbyDg7FpLZrdoeGci8Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHcCRA9TVsSAnZWagAAoS8QAIX7ymmfx5JEbXnwownE\n85UeeBwQsgeBUFON4UyWTQcsTYjsc3qvdqpM/wiBt2QUIuUcrgam7PS7u2U/\nsVMs1pZy3S/F8f0Bma63ewcKMV2ay23H+D5U2PWS8QwTtSDYjJdyyU94porm\n/IizI1QCIJXApLdFzT4qT+LxkMHuq/Uo2qGZSUN2rXvdg19YQLGcnKinrmZd\nGIdv9wH861Y9s11Lk+OGtYB8MGKYj9FQWpcb8IBD0c5XmWpymKnuXzUeMsRg\nAxIbzMqpvOrzVO+mI6jPv7kmGxeLVhnd1LT1Nzj/9XFcv2mDjZ0O6y2HcAD+\nf9uEiSuDX3LkCqGaZZ5aqyBTCmT9f2KQu2UvzMGXYAMFk5HcP5x4XiAdr2hI\n5bzzR3lmf/kDLCrSHrbqEZueXVXODfV5fVgzt2TyemSYy0XgGnlauoX1C9qN\nzatzNYGgOCFjZmyxhKpvTTK1dlOxkFT68828Afv+H5qp1HiuRJBrluAsshyH\nYYAfwomwkaubhBm9Y7t+jef0DlJF35KxI0bbEowoIXoAlEGDRDdPe0+t0kka\nM9RzOf/urlgKLQWpyWjEhr5du/A7amrNQRG0qUI0ccx9pEunROj5bOfZ0LoR\nN13icJ+0Q+UVq45zQhQijvaG3UCXiJHe5JC/QDZYQa5cCM68P00UVTooXRcP\n60nG\r\n=pmHn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.2.2_1544868315603_0.4018570210079815", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "@babel/plugin-transform-classes", "version": "7.3.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.3.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a0532d6889c534d095e8f604e9257f91386c4b51", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.3.3.tgz", "fileCount": 5, "integrity": "sha512-n0CLbsg7KOXsMF4tSTLCApNMoXk0wOPb0DYfsOO1e7SfIb9gOyfbpKI2MZ+AXfqvlfzq2qsflJ1nEns48Caf2w==", "signatures": [{"sig": "MEYCIQC8ORGkBMt07zK8IsK+eX0nEdk82DrClzwLzxC3JVphCgIhAMgxqLIGadMqr/ZQ6QSI1qoa+Mm9rvmjHUBGruheupqw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyuzCRA9TVsSAnZWagAA3tkP/0oRaKP61RK6MF88AGS8\nVdajiNQiEwP7ZuQ05f8Avkd5q3e26TVNAysokS5iK6kR5afWu9wxrT1Pd0FE\n71T5YyC6NDJqWdux7ICtNZQDQQozmSEG7DveuvdLyq9c/OgRjYtCkT6bDtzP\nustajw6M1y73A/RMmK26qcoDCYaIcUInhA/v2001hPguP1H7+2kd27d6c36L\nQH6aikv088JZBJyYyRLINdkXe9gDDBiBcnmQL95K51CqFLNG0KSc5r0Tfhad\nFk6+CV6maqwMtuT0WdtgtRbqZA7NbEs8/vIUzvtSks/lw0JVkWCFcNs/GQDi\nB3IAUAG29fIhhnTZC5/4Cgqi5Nun6EL6tzhWsxjLKZRTBODu4Gw/au80yWB8\n/TkDQJmwzgh0SBYZ2JD5VEkBbawyd0/WTX4GDKp52KYwKbMHB6sLQW0S6Ook\nb6RZ4GmyeeolLs2l/rc1wyUBt3n5MyvpRMjARXOdmMzta+/Bo3Mbq3jxZVin\nhrDCDuq0FNsIL/SRVMsrUKtuss55H2NRASIg/bC6CGJDzsnNyTuMFSTdCuX4\npSNVAKTHWv/1VUmb9XdAYV3tngysrELBwX1uVIKpJ23vt0jVFMenTd1Zpvkx\nlmtlOmbBhcFBz4e5lu7R88kdoqtxASxYPLYqgyoDDx/e9WLRBxhraWRBqYCT\nDM7j\r\n=Ah4v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.3.3_1550265266818_0.22330156872327667", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/plugin-transform-classes", "version": "7.3.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dc173cb999c6c5297e0b5f2277fdaaec3739d0cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.3.4.tgz", "fileCount": 5, "integrity": "sha512-J9fAvCFBkXEvBimgYxCjvaVDzL6thk0j0dBvCeZmIUDBwyt+nv6HfbImsSrWsYXfDNDivyANgJlFXDUWRTZBuA==", "signatures": [{"sig": "MEUCIFFGihmr59wIdrC+MiIvxbuZz5PUQw6PicLGWlm3bScaAiEAkhPmPB4BFYBHq/Pf8azXj4q00yN8Av3vMhQMuFF5/BQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDWNCRA9TVsSAnZWagAABBcQAJqRU7W4QKD5G9IwWz1G\nouwBsyCQjMDJZfbB20O7gCA7LVcE4iEiIAto+75i9XerAYmTUyrXHPtbxfWS\nioITN5+32MJj6AxwgiggvH81jrzZtnytrkudqxKn7RBKQ1Sn82tL7WQQSiZY\nP4KnVRBLYu6SuPtlOMr0YuxfWOh7rE+LU2kRfERWIix9o0smPY7h8ebHO3rA\noh5LQXDlGa1rnlTh5DcELw/98ex/eJ9Pv/q3uUlSHq4uW3JSYwjU/1FlG0DE\n140dYEdGFYF4z57I95MgvXXsbrx8ATiQyv7D+pD4o21TT0oijJJ8FXo66PTe\nHFw6rhdmUzd+Uz4YbU8SkIXSbfmIe6tFd6/AVNNuAqgGWzv6W8aTPMgEUbWe\n0rfsDT8JYCMZqFkrWax53r77wNj4GE8HxW1jIkGZxxMKPAUGX4OlRgvFP9Ck\nYSkYck+mRquQV6svmrRzeWhoRrSR3ZIy1mzSrL0zybygCEtCnP3apwMrvEUJ\nuaslaao289DxgFaJUvNPcAy5zKjilgcsIVLkFCIWnCYJrEz2oMvaVnszwQL9\nTOQshph3w4xHrMvn5MtvAji3/ZYzOch3aeNC3KH93ithD54locMujFLnWM5X\nTA9nBbJ3adZDNn7bI6IIGcZGifZukqsl5tB04hevP/cXJyPtGCJol68oWWg1\nDlXr\r\n=Ahh2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.3.4", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.3.4_1551119756922_0.3680768470284492", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-classes", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e3428d3c8a3d01f33b10c529b998ba1707043d4d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.4.0.tgz", "fileCount": 5, "integrity": "sha512-XGg1Mhbw4LDmrO9rSTNe+uI79tQPdGs0YASlxgweYRLZqo/EQktjaOV4tchL/UZbM0F+/94uOipmdNGoaGOEYg==", "signatures": [{"sig": "MEQCIDOlGIBdv4dUjl93C46heY/6L2batB/zJgvhGGMygC/BAiBLaDP7bYR1CzwwC2GOpG8Qq99BSwDIDny9KvWqM7ea6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTjCRA9TVsSAnZWagAA3FMP/1bV0P9zwbpjpEPp/KCD\nAinsT/UW9fUYA/W9e7ISkeioG+f/nAiBnP8q07EfOeAk6MXA0h6CAeKSrhKB\nf1+Z2Ji7T4qwABtwxzKmpLECby5HQF6MsbFbdAYdUW7fYxcx7lX+x+cRk2op\nFDTQWe+MmiLZ9t1J4XkQpxcePdXhcPYx5Vqc2pN0adjP8lSYqW3GUTfsqIZj\nXdp03ybzhD21aqYx9dxisU2/sYoDaiKnwIxVkcVxo90o1qtEJms/Dox+Z91R\ncKxTwB3RlvHHVYqGiAzPETGuuqIUq+Tsye9wBliIMEZNxfYO53/K9+FoGp4Y\nz7J+O0mWZQROAbAdnWlfi47wPbLgUvKSkdGZI3fcuTccZTd9G4uIOz1Kh3Vk\nWFgCSHwRfAL9CUdttEd34XdnYU3+/lGNle08LD3WRBAR1Y7tplq2XpqPl7EP\nByJEmH5D/UY9xODDRxKFcafvQhbBKlvxeYXFmTg7oMsMwWcv/lTlg0FTXy0d\nDaoT/DhRlvko1Ce4ipGt/OGyIe2m6WqNVkJEp7KdOHr4XKQ31zgZQPAmojhG\nWok87eAQH2bTwJVXC8M3wVpvevIN9ZrXXe/GjbBVcyJDSl8Ao5edP7Zc3bnm\n9avSfg7Fyz537vcdO+NVoGB9f8qHXxR7TMOG0YFZamnxOFDdNN136wAfrisB\nnS3w\r\n=D10f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.4.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.4.0_1553028322620_0.2760719556369273", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-classes", "version": "7.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "adc7a1137ab4287a555d429cc56ecde8f40c062c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.4.3.tgz", "fileCount": 5, "integrity": "sha512-PUaIKyFUDtG6jF5DUJOfkBdwAS/kFFV3XFk7Nn0a6vR7ZT8jYw5cGtIlat77wcnd0C6ViGqo/wyNf4ZHytF/nQ==", "signatures": [{"sig": "MEUCIQCI1BfCI3dl04wsTzX7RvbrahQUpivHB3REshhdDbHB2QIgd0ijnFXznHDTKFT3yPjl768PzYWlf4jHQnqSlNU1eak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75ECRA9TVsSAnZWagAAr34QAIx17xJhHEKGIBylm41F\ns4ILUPFBByWH4tQ5qrHMFwX4NELWROJFUtyjfkna2JcjMjxk7bO2EEJY3yE+\nt2vaRkOsTITclfGgrXshw7aijhWLmFuXqsZ4srS8x/fxty04sn2rV64ytquR\nYkFEoQv8Kl4FaICPZ4w/k5rj+ABcOE40OO6Ref8prtssnpXN4nFFlxC8EBx7\nip06AmvZ3REXGEySUBOinL0bdE1KyeXl+AZcQqZHYc5mtkg7CRqPz/4jNGpk\n7qZ733ob3TETIFDvqe/gdLlfwCHwtRHvGKkectlGfz+4Ffu1uaN1Io7ivtu3\nvmcWK6yAachEMgybO+oIUE3Wppacf31K5jvi9U8X+IzKR5uM7OjNRsAgs6pQ\n9tkc0zK/yMik/WnWo0Bw+WG4K3pHgZla7GMlC6yiwL0LeBiUWsi1GAyp+NGz\n0CwsKyeuyM9EMim/QzhSlJ/bjcvAa0hGPdcDds6UF0j4SjQewCHGp0KwCJy8\nJKqhy8eMmVVr4iRVhcCWacAnF92NSlnbViFosUdG6OYUdRjoOb82lZUUsE/m\noWbBMm+5v8RUH7O86NUvuRtXJmkyBMtKk21udgt2OrSE/xDE1o4qwa8RQO4I\n09tfJVqQVJqcmuSWK+SHW+IJKjvSSfQCpxcAE+39VGDqd5ibrGp9FOCXKfja\nQ+2U\r\n=SSKX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.4.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.0", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.4.3_1554234947624_0.028293420413408876", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-classes", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0ce4094cdafd709721076d3b9c38ad31ca715eb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.4.4.tgz", "fileCount": 5, "integrity": "sha512-/e44eFLImEGIpL9qPxSRat13I5QNRgBLu2hOQJCF7VLy/otSM/sypV1+XaIw5+502RX/+6YaSAPmldk+nhHDPw==", "signatures": [{"sig": "MEYCIQDvtIwKOsmz64FE/XJrGr+VadGQAxVmwPOoPfZ+nvUOTgIhAMkszjt/vVwcmViBEf4vGblFW0NMlMHqh/kNvAJjRo9I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3J5CRA9TVsSAnZWagAAjAEP/2tUYekLpro5TRVzJ2sh\n9LpN0a4QE27bmSTzMP08JSL13Fftb59cnyuQLaaozNarnG1UXFuF7Lp07pAU\nCro0nkd6qlTxbu4sty1VNufVxMfGRi/r1nfQs+MXm+VNh/vil3JduwHe0dY2\nqrdeDFamTlFJ8e224rzj8nNpK3cN1dlTjwViHaY6H+psoeTGSCPortSE5S1j\nG3nuFIHPhR/wRYIpCxx4A/t3LdImj75KQkLRlyZnK8H32howAkjnWPXK0fjD\nDEDhLsECI6K77g6Uqs9XE3MuazVORqgSgDtlQl8YH9bBV3IOi8tKKw31KsIg\na4gpoNX3m501GHO52GvWazz/k8GhsrNEwaQlkgG3/yqsdrqEtrACpEDnjsHH\n01m0WfHy/NWCHxF0uW+3xs8HfYci7ZIHJrdshJPtmb1iVtqqZS1mCod9WW1T\nGaX0Sd+oC6nqlUIVxY0zrHPFW1Qacmba2TMIqD/9u5YJ31PZ368OmAC83RA0\nqq+uwCYwLvjm2XbfOyd/kojuDy4WdC8NxwzRlmTmqPNIhd6QacSQfeU4PQXJ\nU9DFPWEj7/omU/im2XaOgYDr/9RX/HRYDVJAqUqVHItv2tjEIvzcPIM3ZE4i\nX5ogwajw3Bt1siZ+iIySRl/O7S/DpiY/MUgHguHJ6vFEgBLakvIRuZmnJY2f\nFtHd\r\n=wcUr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.4", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.4.4_1556312697111_0.8188235180535635", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/plugin-transform-classes", "version": "7.5.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d094299d9bd680a14a2a0edae38305ad60fb4de9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.5.5.tgz", "fileCount": 5, "integrity": "sha512-U2htCNK/6e9K7jGyJ++1p5XRU+LJjrwtoiVn9SzRlDT2KubcZ11OOwy3s24TjHxPgxNwonCYP7U2K51uVYCMDg==", "signatures": [{"sig": "MEUCIBauekEu2ST01RqMdv7UNf5wJOatjBrIiP2iMIo6RuKgAiEAkv9V0eyFd9O33ntovmP5be5tnaHJHjih17sw/DadVqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FxCRA9TVsSAnZWagAAwucP/jKT4LUoNGuSvaXmZMFU\nTxgKvozXd6yNm9HwijT9RY3k0xqD1KNQUMlsFyn3RmflSr42H+tiQjEJenJf\ngzfE9E3j6mNfv4gE2edqwaJgFtao7wAtZtSiFzHhTbhvgtq4iliuJX3Lx4mS\nMKcafdtcOX7l+giuU5ZMq1Quj8qG+FBc9Kk4H4rfOixq2PyD6Sl2Fs+g0qNz\nB5K6fxO3EpDTtNowAs36ZEWHyYcILfvQEKXTHhysnnqZrT4TWhdLeYp/vkZw\nC3B0LKwe0OgJ+eD5VX/cqpNhFQvy2qlAGlzfTuHwZ0tMHuqGjzm4PBl0NkRm\nuq7/TmVoopgOpwpvDz9y7hBH3k6ypt80qupwbnIHbwtJJEf2LoJLvWIcQvdS\nTaUBM5O2iX65UjQWCuCR3QJ5/hxcPZf3N63wax/IpkFC/7IX3w+30oxyaXb3\nl1PybzAPxrCipieTWa/UUdDqXCQ+9x0cZKOvnk8OSaK9KWD2I+wYr7cekIDn\n22l56iLPurXEezqqkKjdbBYTo7HkN5J76ZJO3AeJ32qGWF1vTIIsw6qHIv4U\nT9hxMq7VPi8luRL3fvhnxiuKFPbwHJ+1JbKRCG2ClrHN2YQ3Uv8ArHkQJMV/\n8nEDrS2b9JyCCtnt9h2kPRAEG+M/jBmFX91CxhtJP+QNo8V609/Rg4RGQxBj\nwLzQ\r\n=i8CX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.5.5", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.5.5", "@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.5.5_1563398512939_0.17688755416527902", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-classes", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b411ecc1b8822d24b81e5d184f24149136eddd4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.7.0.tgz", "fileCount": 5, "integrity": "sha512-/b3cKIZwGeUesZheU9jNYcwrEA7f/Bo4IdPmvp7oHgvks2majB5BoT5byAql44fiNQYOPzhk2w8DbgfuafkMoA==", "signatures": [{"sig": "MEUCIHgrVHDYMe2i9kQy9KXzb/tFwlLicEmblmEw7m/wnD6eAiEAi2A/O3WdzFLzUKQJLYXYkt8QPeC2ioXj8dw+0y9O+q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTICRA9TVsSAnZWagAA458P/1uXgcLjych3JB/iAikY\nE7TpXAdkITI77MYZam/cpeocMwAI5KqxD5aFsGWU65KiASNhFyr/jsMr7pUd\nz7Tov02qsNCZCJtxVuonu4zegtB/m1uwP9Z5Z3Ma4hESYDsrkvjGWLK/KaPr\n8+D6bYAmK5VHcx5cbCc4GVXTbB+Fqkj2mlOP+kvXjl3it8Qye0px5noZHbXb\nWqztAd7Weosddovhc/jzLQWcZruR0z8vvYKNQte0OU0ZPcSjgynFPUFn4sGz\n3lIKHb6yXzZ5ridix68Bmc+jCuyZHVD5OCKCe+9CvpJstVyBfRd1Sljc0j45\nVeOgQL73UBT2/elL7TeK5LQBuQXyQssedFPdn5GiXDCAIxhbE+08zIJUOj+9\nSIgr5YkuGquKlsNCopoaKu+L1t35jMfH1Dib01q7wm81isyk0rOqbN3mTEcW\n6Nh2eWXwkDiOg9GX9uCTZsmLOAb6fkVgjYOkJU+QQuW1kNsn8jHa3JYos6bV\nDqaUgiZZFBItlT80PiVS2G1R39cr4OSWZkFhHJQCNU2AFYrH8T0s/y+UiC14\nfQEpA9B4wQJfaII5ZBRCgf+IKc5UVr8kZikZaJUwChTwkZJclDVKNckzru0Y\nfJAY4G7wJHP73adVbSZu6c+TchxAC2yLIugf6o2+NY/nHQLzYdKVkGkBoguV\nVuHf\r\n=gH5p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.7.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.7.0", "@babel/helper-replace-supers": "^7.7.0", "@babel/helper-annotate-as-pure": "^7.7.0", "@babel/helper-optimise-call-expression": "^7.7.0", "@babel/helper-split-export-declaration": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.7.0_1572951239983_0.23961155522514344", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-classes", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c92c14be0a1399e15df72667067a8f510c9400ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.7.4.tgz", "fileCount": 5, "integrity": "sha512-sK1mjWat7K+buWRuImEzjNf68qrKcrddtpQo3swi9j7dUcG6y6R6+Di039QN2bD1dykeswlagupEmpOatFHHUg==", "signatures": [{"sig": "MEQCIBg6NK7zUlyB8FOqQ3ERjc4dP3NC2/PY1ZM9g8gU5xr6AiATDXo3i39ABaojEy1rhbTuQAcnN9qW+2FZEgvYCR65fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBhCRA9TVsSAnZWagAAC9gP/jyWwUvW5CkkiLukjH3i\nPkj1VACAmQ4d/QcFzlH1PBGmDalDXOMW5dNDBQ8W//Bt3fkjrz4FXHHMiYpX\nrq1F4qxc9QqrwYcV19EFElzly3cNaQziuRvYshg11ubzW1EvN0Hb14f07g26\n/D2aVwQhyEA3wDOBOIOoBHb9o55kRdHBbVj9lXOEyUl9nEpIVBLKzg8xci7Y\nYAyKv/ZLn138ILesV+QsQMaHM4OKl7mkBrdWfzwr6Fin8qNL4GfrZsuyz6Qu\nBdtgNpaMxC5yvOnz9GkjGAqv8GRNnT0Y2jbKtekKFAD7k0kqFfOpyhxPwtCT\no83QGMWJHazuc8pXBYKwvmdKVloSrdQ24AKfZ/+FKDAMG1YwwNEcSUrg3+EB\nUX2mN1GVwZO1Fzlb4FXY2aOfiBe896SuB/zRtLo72FxSFR9RuPuPZDhHOJDk\nqcc9//WuxiHzLVUmHC5HKZVkShk+2PE03HPyutD2y3rIE4fnlC2njedoyJfb\nTofF7RSEOhm+/0LDIwueBN90Zf21Of2xriwuepc/IIy0yeSB5ROHMZviwY2r\noUgdfNRIgddLIAjbHApLXuZJQRi1Rh4UQbcsUkECcaQKf3Ph7Zfnzgy4a+LK\nmSQKgK8zsrMbvsSCjGraPQd2uCoOipWj6uSdJk43npgiQHe8eMkQDkzg46KC\n077L\r\n=0eJe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.7.4", "@babel/helper-replace-supers": "^7.7.4", "@babel/helper-annotate-as-pure": "^7.7.4", "@babel/helper-optimise-call-expression": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.7.4_1574465632694_0.8702379636881872", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-classes", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "eb4699613b3ea3ccaf095bb0447dac55f7454fc9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.8.0.tgz", "fileCount": 5, "integrity": "sha512-18RLDwKtGXCLLbf5V03GojebPH7dKYCmIBqQGhgfZDoYsyEzR9kMZ6IxlJP72K5ROC9ADa4KPI6ywuh7NfQOgQ==", "signatures": [{"sig": "MEUCIA7j/J9Y/VxyoA+vvOHB+t/Aw63z8qh2R2maWengz9OIAiEAuMUkAOmNOWF0HDv+gc+qjpc6IEQ2JBNmWP2FR4sguLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22627, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWdCRA9TVsSAnZWagAAztsP/023L3GyIDZpRcmMG7Ku\nYoDh7VM/J+q/f2xCqm/pzqpd+FOaFnXh7Ibuxu5P2kVbA8aeas6YqroEvnih\nsEibF7p5dfFU3HpwJP35z8FTtv5lVOV7WgHJnBXOnXZ7RrsK3ltL0OXCBpgF\nEmH2IOnWO2KDd/hWB/DXdt4SU9yjWxkqwPLwbWXYHELDZHv9NyFWKDMm9xBd\nH9vju1DhFZsSHPmIKBG1OAD75zG+4RNGWYgdLiyfrNP2hmDVbwE0vdGFAgE1\nlydI6EYJkqrQ/FlFXoB85lxR5Qng1KZ8FBMPUV8PTIPig8z8qdtcc1qYa8lF\nGS8QN61HEj3BITuuP+5RnfTC7SxyAeMHG1e0WkLaHljCpd+geS45GbE8WNeg\nGCf+UV1Yf1oyBPn1vyj29/RhkvCUY4SSIukelJaLtMg8oPtCGjpcbCxwAvPK\nlpwU2lUXv40JYLRwQxZIiNaP/Yv4BWZpCDXGZCyTMIWx2xyegkabP66Ij3Py\nU4gd1mbebVOc1D9WZJ9fBbsRx/7pqFqWMu9fu91VnMjUJbAkJ3uuWIEzghP7\n1uGQ4c+1MVzWOTOUPfru63yX8yepdgNqxMS+KtmQTptWHppZVhyBN3I/onbj\n2q/Esq2BKrN6RdqE07mxF2zPF+eBOx+wZMYVRjmlPHyQjbdQtDHXAXnY2v+C\nfnXj\r\n=sY+2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-function-name": "^7.8.0", "@babel/helper-replace-supers": "^7.8.0", "@babel/helper-annotate-as-pure": "^7.8.0", "@babel/helper-optimise-call-expression": "^7.8.0", "@babel/helper-split-export-declaration": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.8.0_1578788252761_0.865515693447467", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-classes", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "46fd7a9d2bb9ea89ce88720477979fe0d71b21b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.8.3.tgz", "fileCount": 5, "integrity": "sha512-SjT0cwFJ+7Rbr1vQsvphAHwUHvSUPmMjMU/0P59G8U2HLFqSa082JO7zkbDNWs9kH/IUqpHI6xWNesGf8haF1w==", "signatures": [{"sig": "MEUCIQDG9iafrKEKQgTIrVFpziXN5D3BcTUlTrzAcdOujdj+PQIgJySJQJD0dnBOf/8VAwClG7uSl6bAgbwm1zdHGFQGxX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORHCRA9TVsSAnZWagAAoK8P/3sTc97GsnFGGMOX/fhC\nW3ve3SJVoGemVLPTx8xCeN9bE7riXDhlB/1T0lMYEGj9nRSovje6vV5gSK86\n5hFAQSAwWXwSsh8Ae20AprmEZkfNg3T5qblaZaB6sGntE+qFhiEvbySJ0xsl\n7/lRClBUFpzIuMgc/nwfICxs+Bcde8LfwG1LtPsc07x/KbtmiyTpLUIZTtLn\nZ6lpo4+wtGczI7qnidIb99P2M3477jOB6b4N7HMcFtOpQFbCnOPWCWS7vFvo\nrQ8nZuUgU2spa2c9aPmG8FF9alw6/57jdwvJmDJnNga1btN2MnLf0s0yfc+6\nHL434JNTD3YqfY55pKZZqAb+uEmjpPbBsUbnn8IibGBt9ELR9xqK0b6hSzxV\nvWmhhyMHb9Rt6HH6onFecrIYmohZEKjphBSAjAjKT3DhTcYaIuwvT7B75FI+\nvxcmjkE7Tih0CB+zz21U4KkyRPiCz8/ivJnEGpdTBGNUcx/MJmxcHh3cyp2k\njze+V8GzDAgZsfM7HoYRvOoffnia3JVB+ny2B/YnZvcUy33C4pVHzDxnX1c8\nQZDpenvXipbd9xiNUIQS2u6jdTP07WyF8HYTAQ10iGJAUm4Q+ZuG6b3OBt1S\nGx69sjDDpjZ41JDrynv5j08lYLQ1/meNso8DUXbIv/P5/AJ0/UiJE5OHe3Ru\nbvOO\r\n=nv4r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-replace-supers": "^7.8.3", "@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.8.3_1578951751287_0.9485839452365861", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/plugin-transform-classes", "version": "7.8.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "77534447a477cbe5995ae4aee3e39fbc8090c46d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.8.6.tgz", "fileCount": 5, "integrity": "sha512-k9r8qRay/R6v5aWZkrEclEhKO6mc1CCQr2dLsVHBmOQiMpN6I2bpjX3vgnldUWeEI1GHVNByULVxZ4BdP4Hmdg==", "signatures": [{"sig": "MEQCIFE+iwoKQCXKmd299U17N4lovPSvj9+pG8n981m2zCeAAiBKBRb1vni4LUdPx2tCk4Nsy5kewIn1QnrV4YUY2D43PA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RaCRA9TVsSAnZWagAAxNgP/3nIC+cFxwAexMECXoPZ\n0tZDGIX9DVHiW17l3X2Wk7IBgoNLAUa6fORbwYBNrARYnTOEmMXrP9oo9pch\nIkOK+8/pDtMmlM7qJyw80FiVG4nsG47+UNKJ+z5LEquCnWs5sMwcksQaRMaQ\nYJpxfdAZyGdIsbdUC9y5U3EuHzLxsD2axmMjHNqwMfb79YNtB6xXw02X0Jjq\n2oHjnYacE2FFX391qKNcGeRIxvLSp4W/I3md4wRPM3W6/1rbEVKIy2AAOcHo\npV/Kxs5JyEtuZxFKq4YTV2zCvQ9n0t6nd7PjrKLD+M2mnbJLEyP/bpeIrpeP\nB2s4xCT1/u26WnQEPz+KvrW+v4ci53aEzucslEfgVKWcgNB44/fpBpPIet54\nqgG2MMoPnSPpTApPOcVvS8VhBA4IAjv4y6h2eVYz98lhqGcZnXG7wGQp9r+O\ncwvDEilCVSgDwaTlmGLGsl7ehLROpw2mn7owwp+7Q37Iy8ddU5iAyOwKI62T\ns+G57uXuGsi6QdT0C4ovPh2sEQCszWJ1ri/C7PKTshN+c9bGhfLlDR71ciCh\nOYJ0K/vWEr/8wsFpXFMT0FCvBeQ8kznv4qhPZcVZIbRJTZw/r4VMtCFhD+MQ\nmDqoEvucgZaffOIYC8POmY5H/DDiSeQEBttU1IWAaXNIzMIxHo5xfB8aUHoM\naVFd\r\n=Nm0L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.8.6_1582806105772_0.20779439833863722", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-classes", "version": "7.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ab89c175ecf5b4c8911194aa8657966615324ce9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.9.0.tgz", "fileCount": 5, "integrity": "sha512-xt/0CuBRBsBkqfk95ILxf0ge3gnXjEhOHrNxIiS8fdzSWgecuf9Vq2ogLUfaozJgt3LDO49ThMVWiyezGkei7A==", "signatures": [{"sig": "MEYCIQDhhdfsrlAaOcoEvCzYAqwXtqHhW4gM2OOTt6ELriSLFQIhAMYTi/95LgnlAfIM8x0sEXiYm1VHLkjPF4KTBbOFbylF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOcCRA9TVsSAnZWagAA/IMP+wYQ5WpwDTdvNlTczo7J\nhahTAcr/kvYSVrIqShopBNHQaxCeFHIecEaJgsm8h94e/QUgBArS2rbyE09J\nL4guX6OCK7FXiVE/18YS53ziOsr/+JvB9uXHe9Jgb1LjKg9KEA+Zhrd2LbZB\nwLlb9hXjlwO1LWYGYP+o2j2xsMlzwaNEohTU5qT5euzyM8HLU3/R4844CM/k\nYlhQ02MCbh2eJIWGHNHgIiZ72JfqfNdsU+Iu8M3CXpzPoWCktm8O2biAyeTt\nKg/WcjDRwvfwTCWUhf+LRj2JPZ7rh42vPXYnNMnS46DwUy+l+BRPXqacb0T7\n16+2a+YlpsK1JMBSPoGDGbvnNBg7nUbp1HItFkojF/D2yknuYDo749VhdNEW\nAC29Jf58fshDPVwybJIzRLUEa7TBnAH9jtep2vSSE3kN/Rowwb9BhMWLml7N\ntCV6Gv0dinKX/33x28DAtotpVjfZYCIwMD9qUkvHCcg9hsd3Hjlr9okSDtZ3\nv1gbZ37s/vqO/8nbF3jpnTI/1TWyx3bxCWsup/bMJFlX1wCCWOJed4fB6biI\nDTzqTnd4wxmKK+irKwKppJO/OHUDIOTTpMs6ZXjY8jenwE68cgwvoKuoJEmY\nKKEPnLmAsKUt+P06uUyLCUGPPhCIuWQO0BIGg9xQGtRpZqPaaCuQmCh3h1N4\nudZ2\r\n=TNna\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.9.0_1584718747891_0.3276155389327893", "host": "s3://npm-registry-packages"}}, "7.9.2": {"name": "@babel/plugin-transform-classes", "version": "7.9.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.9.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "8603fc3cc449e31fdbdbc257f67717536a11af8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.9.2.tgz", "fileCount": 6, "integrity": "sha512-TC2p3bPzsfvSsqBZo0kJnuelnoK9O3welkUpqSqBQuBF6R5MN2rysopri8kNvtlGIb2jmUO7i15IooAZJjZuMQ==", "signatures": [{"sig": "MEQCIGvG7gPxMxPIQyNy8lLDVsNMrSnNjeGcLdPz3mkhfdjDAiAIgWUAZtpmNU1+ieJO6t6Q6fzy7zDnhWP+QZPUIOhi2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJediE5CRA9TVsSAnZWagAALtUP/2/cc4ZZlYHV9i25pw3U\nmEimrLTG6USNAxvL4jzFQjskZqKVURoQhYy0V525zoZsZEI6HuvTIx/J9Gef\nXyvHCRruZLM4YAZ5uhbf7fMbZJeDsQLbY7hxNhfcXQ2XPJ6/ZIa9hdHekZ+5\nyL3c58YHItP3PCFvebB1C1isJGGfbne6X2F6P2eG/1Ix+2M+qDj8iDURzHQ9\nEKmsrRYO4pl8/H136xyTwiteH0NvHSCMKv/USdqY8p8VlBPe6qUDueLZyVLE\nc+WphUWonuX7kK4dYGEJI4cizDkP+D6wYo9qMrQtExMzUQM0QxTWDGULFdea\n6UPv/vX9tiOABtgizB7I+ygmwy0emnn4aUzb2Nj2fKZ/yz5SC86q/TtQrlec\nKqUu+2n4M8BDNJUyCORg5FmsxJmSClpQZJQsfYkTSSWbJGS2JalQDHrD3/JT\nTh6Zmoyrs169v8Mzk+r7pANWPH5TnSobiB3B2C8YR31TgbbHovm75+/NqsFy\nSJrKjexEzbDpIzJuzBXdRODoRNEz2guAu8t4Ly4ZttrTP3dUmZZoVC9U2PiF\n0B091bnugyIP4WTU8ZtALwkD/h7vAS9RHdZ24ck3tqIptm5hjdDrk8mS1JR5\n4W7dd6jnMWyq5ylke+JzZD+WCcG5mS+10HTOMrPzXJODw+76rYioSz+tKVDR\nPyzz\r\n=hQT5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2399e0df23cbd574a5ab39822288c438f5380ae8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.9.2_1584800056656_0.07376768028099834", "host": "s3://npm-registry-packages"}}, "7.9.5": {"name": "@babel/plugin-transform-classes", "version": "7.9.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.9.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "800597ddb8aefc2c293ed27459c1fcc935a26c2c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.9.5.tgz", "fileCount": 6, "integrity": "sha512-x2kZoIuLC//O5iA7PEvecB105o7TLzZo8ofBVhP79N+DO3jaX+KYfww9TQcfBEZD0nikNyYcGB1IKtRq36rdmg==", "signatures": [{"sig": "MEUCICHk0MKOKsz9mDUvRCpI5MdIGhh76AOVBD4x/wr7QJyvAiEA/fp1p2fA1i47vg3pZN5E/XvTYRqr3wjNCdtIPJz8TFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOuCRA9TVsSAnZWagAA1PcQAJI3ZuSr6vwcCHWUty6X\nia8wijuiLuMneuPV1wki3pRJJHlBgJ4gCdRWeWyoIjLzE7YWiB7fKknIXzLC\nPHW0yJQuSQaKFI9ZQ41dLiA/Rme96GJiwrAWVwcIzRKgNbOLCrKtXVI7Fcqr\nVj2cQmthf9OBGT2CRhFSbM9jhsK+84H31WH560rHF6ehD7FKYUY+lWimU8yp\n5sx2+9O6RI/PUiFwS5R7qOpjSQ2x0VvU+9BTz+mqaBV9vOKFy7vZXoCRqW3c\n1TwPkpnldJ+tAR5hdfoGTHYltbPuUnVc7iQVAV1nZ2PSG2lCSH3Qawxri0ru\nNf3K4suee5cYeVCdr9sU+rbKRQtubNiuNznjgOlE3gsJkcQ1BVw2m8Hw7Pz4\ny1bV+oPhxxNfNUcfDVabiZdzu0XKq4FRQaF/uO9DlLC5aBBbDFysn2oG/jqn\nbPhgL8SikwIgBBXA0XezdIQykduXfCsY3KMp6ghc1h3kEJrzkrQyIGH05Ncb\n+G4J2x37ALroOpc1EmLqRPk2EnYd8qZVd52O/682I0P9xQ4vp2D2KJ4pqTQ8\nVQTIgFgh676XtOvCKujQ08gGUQMIt4m1r0X8V2t48ueYcQC4NbWxvUqS0gcv\nAXitVwFJZW6pAHhbUQSqba4NcumBIYjcF6+sD8y6wW0pMaRD8+FbCA1QXcSY\nXL9j\r\n=J9ED\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v10.19.0+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.9.5_1586287534197_0.5383700808694805", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-classes", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6e11dd6c4dfae70f540480a4702477ed766d733f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.10.1.tgz", "fileCount": 6, "integrity": "sha512-P9V0YIh+ln/B3RStPoXpEQ/CoAxQIhRSUn7aXqQ+FZJ2u8+oCtjIXR3+X0vsSD8zv+mb56K7wZW1XiDTDGiDRQ==", "signatures": [{"sig": "MEQCIE4fpOQxzCR5cpp74Khdeejqoqn1ii8XUthLep+knYJvAiAmG2wbCOqo8vn99W7LcP34UEE0sJfKxEYzY8oivVIrFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTWCRA9TVsSAnZWagAA8/kP/2/7JAAD2YtpvPQ9Ey13\nBZPeFKUGDidthLrQZpJs1n+R6REAAjPL0HbFfIgsSNNKDqZRg+BIGMY/YW9B\nc0oOVdK7zj+Dw2ova7AeInqE1z6Otjds8tAeawNgTChMfYmd86ljq5t2Ii+S\n97iHXFBM3l8mDD2DdcDf9EOCXT1LFxemDkCKOy8FE2G/oI3H68/x3KimKZH0\nEDLaAsTPa8QVXQ2Fjwr8yrys9BVab2vb4s2buZL0rU/a+GDvT2Uh6R+jE0vX\nteINHnKtbDbXMY19W6t9xJ4Mmu9r4atXToegn5yf7ZbRZi583jsB6MUMd4D2\nEK6m+CVwQvwPUVGFED8ZUqE2UViHjweM/e6yLFemRwEs9NJqQZATj0nrhjG1\nXM4yqbthIBN9ae2ZIAHK/x4SRqapVZraUejERlH0JkhZj2WivzJ6xhyTv0sN\nb28WrGQxbi4bzayk4RLUl5DtUidVAfrBimeIejiL7/2YRkyguD8GuPK9JrqH\n3WMG/q3z2Lw2W12781mXJ9+Bro68w/qrqk0MdtcF+S2iOzCs3V8VtixQ+y4a\nc4ZQN01kn/UMWUJlo2AScQAejMTa+3Amu/KDYrVYuRCJ9jHUHYzPiEiXW1kS\npglKOnrCm+Lys++OeCdYo8EDHQMvAaK0whlWer0U+eGhTSpeHsARAYecptB6\n6k40\r\n=i06t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-function-name": "^7.10.1", "@babel/helper-replace-supers": "^7.10.1", "@babel/helper-annotate-as-pure": "^7.10.1", "@babel/helper-optimise-call-expression": "^7.10.1", "@babel/helper-split-export-declaration": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.10.1_1590617302229_0.46257606297448084", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-classes", "version": "7.10.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "8d9a656bc3d01f3ff69e1fccb354b0f9d72ac544", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.10.3.tgz", "fileCount": 6, "integrity": "sha512-irEX0ChJLaZVC7FvvRoSIxJlmk0IczFLcwaRXUArBKYHCHbOhe57aG8q3uw/fJsoSXvZhjRX960hyeAGlVBXZw==", "signatures": [{"sig": "MEYCIQDvHglAMXj0aW5lGuSgOVt+WcIRXBom0go6fcv7ulQ3hgIhAOebD+Nksx7GMps9Mo/vlE6+xbVv/22ZQgoucHU9qEkK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYaCRA9TVsSAnZWagAAf64P/16jXv/JIZO5HtD25oZo\nYQPrAaHDFnz5SApCP5BzmkQPtgt613L8E+0QLJ/2tcmkMK42z8pxmcZ++Zh+\nN7+6FkWIfmFvP08C7ldRK1vF+TYuqmG/9qgmsuZk1tESRUrpDghifmB/caAn\neNCwYJKfJviZmHTorAZyl8ol7NvQEvJJmsTLk63mmhNfP3gyHrWmWXB8bgFh\nf1BfGHfmGakjMlV50oPeg+/eEerpRY/2xo7ZPOzUZQW3WS6ZMP380LRSXLY5\nOE9wgnC8KK3olshrros0T8L6sg7mdK7EKxXflpWBi+XWpDEhgkIDieZ+Bgbq\nwij54yqs1X6v8EIYtMxpnOsN9wjb+UXtvgafs6VEzLO6T0neulyaIkgG1Kz4\nW5+tCBDPG+xVBbg3XGQDE9AU0EyfSi+VOd+ev6GXQB6f/owKHpgn/aq+cv/P\n6g63XFMqn/3LE6LY1O3Ub4pdM8nkquBekFa2zt55TWWyTXK3dtBbKJhByV0m\nDat3WhqnWPjxivAaomquvCPIe8luCUGduY9VXufAQC6QTQdBwsY8SajlHsY2\nYkYdpnUipFQNW6Wu2iSmKg2TFsxT4YGNnx1DCCRYAKsiE1Pag17ZATbtMA62\nQrZ+QbrGdV84k0fEDx93tMmGTGQYbDKqm2x+4QAaNMcwC9P6GsJvZz9omFvD\nDoxc\r\n=NDRR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.10.3", "@babel/helper-plugin-utils": "^7.10.3", "@babel/helper-function-name": "^7.10.3", "@babel/helper-replace-supers": "^7.10.1", "@babel/helper-annotate-as-pure": "^7.10.1", "@babel/helper-optimise-call-expression": "^7.10.3", "@babel/helper-split-export-declaration": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.10.3_1592600089695_0.5530068290221892", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-classes", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "405136af2b3e218bc4a1926228bc917ab1a0adc7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.10.4.tgz", "fileCount": 6, "integrity": "sha512-2oZ9qLjt161dn1ZE0Ms66xBncQH4In8Sqw1YWgBUZuGVJJS5c0OFZXL6dP2MRHrkU/eKhWg8CzFJhRQl50rQxA==", "signatures": [{"sig": "MEUCIQDG/5Y8YwtVHhyFIB8BdIngFFGkT74vsc9EdMDuNq0wMgIgeu+cDofU14cvdyrrPX0GSIH0GyszNg77GO2DTUQxxBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpzCRA9TVsSAnZWagAANOQP/iPTtdcS2flSCB32z5qV\na4bLEIX/w96VwKmZQVugN0KaslpAp7gaBu7/Mtp94Gn6sKekrRWAgBo8T2EA\npQfoHJ/J73X/MNFhzYf8Ho++XpZ5pyq4A8L4mnAa0NxF3q6hI6sg4pBL9/WY\nCNCe1rhuxu/DdpE7omVU0+erbxNsVCmFUIXU09CWawefp0o6X4wb4Lq7QWOT\nJvD5yrLNjBkHm8RFXMdfRdQWDOMnn2MykThKPMmsuQzdfIFqb008PREa7lOW\n4M7sFm1GuNiTz3cwmNSON887+QY40IsWSlaJB7lfitht8zJ3gOGzRKSHnGpv\nYGh2mlTfMXhDjLIgqrPncfgEodNXfpvz3kzSxBX2RZbaM7fR8zwzyiPT6LF4\nKAiv4JfcQTrjlxJQFir7mF7CV4mhv3hjPpqXI5+WQKbXYyv08cKZg/n/Qo/n\nEpN6ACUTY+1z8zO/E9+ArjqdVDjKRCkPBNaZg/6aMu89ART6pb6YDmI+6116\nbaNccCVMHH1qJJ3aaA9Efg36BSXhdvYJrvh4clPIlez3Qsl9tbnf6r7edEPC\nuvpN2baGFk8QzXHd5hEryHC8YEfP8V00NqcI7tQz68UcHcBWD3TI+xGUKj+6\nRu5nd7KtYxR/66Jg69bDOR859Su45aZUabplVuif0FZoJr3kpu8sVyH5bC+x\n7XIE\r\n=/PGR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 classes to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.10.4_1593522803184_0.9279760234456993", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-classes", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "65e650fcaddd3d88ddce67c0f834a3d436a32db6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-/74xkA7bVdzQTBeSUhLLJgYIcxw/dpEpCdRDiHgPJ3Mv6uC11UhjpOhl72CgqbBCmt1qtssCyB2xnJm1+PFjog==", "signatures": [{"sig": "MEQCIBSXOI8spmw9Qug6PuSgjYM9tAiH9SFOx+5mAD9ZSSiPAiA8VVqBjueY6EUnD2zDUFT64ChkczoDw07AXNbP+/t+/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAyCRA9TVsSAnZWagAAAKsP/0xN24Gm5gjK8HMVhKNa\nzpGgkBt2Sduqt/oaM1SNmjNLdKbz2xVQngVojgJnFnhCnxBibXWmQUAIi8YQ\nZObMoCiZgurOUi8NJzUMSvQsVzg3eKXBmCe/NIdD4keMwjFhygjt70XWBpV1\nvSVM4ocg/n1eg2s+Ym30jXZM58U7/btpXiJjIj4PcF7WpyZRpaCMFfcyJoX9\nNVwf+tE4eN184c07B4eb3wPhR9fRznvakJLUxB6v/QKrRNTLhG7iw1DXONwW\nXK5GRkk9HnF7fBpvvPo0P8f5sa+ZE/pb00K8YcXuiL6sDk5CV/zDFCoKN10u\nlTemU2fFcNgVwK8Ef7onlrS78deT4pC6ylfmhD3gkXwEaDpVaYxb2O0vxtJ9\ntTMBTgvwCdAZdVfkdQ/H5vVx4mJaUDto4o8A9HHRPcXpgYCow3diW/SAGeRk\nRt7Ck5l/FrQhVzRRIX6t7NSzkoLv0fLvUry+mMDY0vp4C3knzX6peTksfJvC\n4ikjAijYdoYEz62vSzCN6wIDxQ0IMW38t/4xVFIxnXxZ0rqmG50oPOmxkfX/\nbZyDxjoxNjDf4fZVffNgy7zwADzoYtLeGw4bYMf7x6sYYsg9UZl8cp99EXIq\n+1wbF4sr49Z/ZNz4BvRGCadkD/y3WXks1zqcXcr0P/ApouI9Q0qSqwGvsglB\nw1+1\r\n=Ju88\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-define-map": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-replace-supers": "^7.12.1", "@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.12.1_1602801713974_0.3574662171405767", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-classes", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "9728edc1838b5d62fc93ad830bd523b1fcb0e1f6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.12.13.tgz", "fileCount": 6, "integrity": "sha512-cqZlMlhCC1rVnxE5ZGMtIb896ijL90xppMiuWXcwcOAuFczynpd3KYemb91XFFPi3wJSe/OcrX9lXoowatkkxA==", "signatures": [{"sig": "MEUCIQCaE3tq20f22QXbGWlzPQnEWUpnBtPpRs+x6VgwDgZhjgIgXHULmw/wa8yYlM+AS2wKPToR/7hy0x48JZwluBaltos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhgCRA9TVsSAnZWagAA2Y8P/jVJ0JwP0Qs3YfEvyRSZ\n8IPlBkN0mIDPoH7ktAQiw1AtF3ZF/+sDICkTPcLbnuDvpSxb2662xkXskIYM\npwsT8czGjPmSh/k/w+bLVBs/lqAdvgkxV87mQ0w+GW8hpEHsX8MY7wVMkYP6\nr8zH1if8oSmLxbShRWrnPREB+0LUifEClakg4rdMkRI9sgcXuNhKnb2cylMN\n8qbS4FvjyMRu5hQPC/vA4zjO+vK+3C9Q9NSJrJLPUP2oT2LCTKGdu9zjl334\nq4Q1DMIyBb9F5FDnFFjVLA5Vud7T+ciDaOdmbnOD/m4duCv5aVfPhZXyamdK\nGnr/65iQjJXG9np6dGmRuhUb/OZR3/N4P/Bq1pBxk3CmklRNAAv9a6NJBVmj\nC8EMmgDSWA7NzOlCxBLXUAtxvZ6KCaEhd8iK0uW9p2EnAu3IS7FcNiGjhSLA\n1khEL6s+/rHx6KJxAsFvh1e5h/0qs4Bm8aEBhQ4aNJmQn24jM+3c6QHXFGOX\n1yUobWwW4V16p3ELGgM5dNxEcjsqDs+cqSdMdt5cFrKIh3mjzWVaW2Uh25l+\ngJpkP1BbDMeJlMwOPi95F6X0NSFhJZ0qwDhSZmQopsRdKP40TdIrZQsemRV6\niCa3zt2ntU9bV4YITcOhEzE6gLcKlLTY42LCz6iPwX4IGvQvBkI2eeUZYA0p\ny86i\r\n=9Jlw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.12.13_1612314720508_0.2803951638400002", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-classes", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "0265155075c42918bf4d3a4053134176ad9b533b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.13.0.tgz", "fileCount": 6, "integrity": "sha512-9BtHCPUARyVH1oXGcSJD3YpsqRLROJx5ZNP6tN5vnk17N0SVf9WCtf8Nuh1CFmgByKKAIMstitKduoCmsaDK5g==", "signatures": [{"sig": "MEYCIQCv1LWrQzuKa8LACSEsFW1iyAJwJYCOVK6/6bdxTic9BwIhAMBwlapJp/ykWMrqcG4X6loMBFD89JbCw54EgUpuyvPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUrCRA9TVsSAnZWagAAd6UQAJKirEKaFzwgA67CWDS+\n3P/dWIHlJlPRg1yWv8ekY3aQowRcDWtEfkubefJuMNx+9Cgg33o/0uM1t6JG\nCWhNW/QaMsBtfIMXDnNQlG4LEV6yzwgjR7PPpkPERkmAjG1x6LjiPQK3eGSu\nCRoZy86tdnCCHaek+/2J+REq+9c8kjMLBvr5weOCcegeLpBlQPojuSm6p9a7\nVgyS3pUcCAbUSEdHvRDqrK0qJ2v6Wn+DtjiwdEBU2zyphOw1pwDdNmw+OyXC\nkAlz4mOrb2vCc83IH5byX/504FlI6RxVa7AS3bVQfxsql+8UKq7Y9eqUR2qR\nf6id5CSgJ6lkHPbv+4hHwC5poo1CIHWf78I/8nReRPWY2Yq3zse54swZhlqp\nuvEcMZFSDLmvfCdXXo5L6iMtDDx438L8CjhNVrYqqAXb8ExA7fot3nPPsIWj\nV/scy395lps5a6V6hYnAF4XphZW53W0Z0s6YRTrKVokyr5YdyFRtVYlLLxgl\nEwgTcQSnhaW5BT0UnT8coQCBgI5XOoMNtmOdWUIqUn5/+Vh3JP4FRNS3u17A\nwUhamzum1mGB1z3P0y9wkwPxMHJsPT3DAup5ptjG/+ptXcXHU3sqTdl6a/B/\nQGLkwZgO1TwafDfeguVdxFxY3DfzidYyM7dumslJJ6eBLJuOAh0XLDdRO6zs\nF9tq\r\n=q5Et\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.0", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.13.0_1614034218575_0.07995194978804987", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/plugin-transform-classes", "version": "7.14.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "3f1196c5709f064c252ad056207d87b7aeb2d03d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.14.2.tgz", "fileCount": 6, "integrity": "sha512-7oafAVcucHquA/VZCsXv/gmuiHeYd64UJyyTYU+MPfNu0KeNlxw06IeENBO8bJjXVbolu+j1MM5aKQtH1OMCNg==", "signatures": [{"sig": "MEUCIGjwSsPJeRSnxzHcHtdLs12+QGzm8y2/aVGY2yNeHtT7AiEArlgS8OUoB0HM3cxgytd4SErFVSA2MG2oRl8B3CgHW7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvQCRA9TVsSAnZWagAAx5YP/3iMYbtv1OdDO0ArpKKs\nmHV36ItmfALJ8K/Sd1K6FaLszNo0a9c8IyOoMW5mRx4mN8dJr0s8fdmbgMFd\nl9Qtbw6yW5B9BQ8IdGPcDOHJxwG4SgDQwrIevahTn/9WYzqX2clRdFRFuqBs\nZ6mfjcC7JAXN6mc8BeLO/MZA7jzR5QUhLqf+zIYFgpOIw73XPTP0uDVSBzfB\nIY/CKvxUG+npGGZdZak+TmMhs51x/mF9JRdpl4+Z6pqhnzTZZde7QWPBF5/S\nXNffOHUXdSexusx/v5P2Qu8lPPyjjtLLXqKR9M1frZmowgEotKcaSUTLYRxf\ntRpKM8vaejYTu0BtAKyH0HRYc2k7dxz09qPx/TcTD1e0tUb0O+0gXs+w5ZW6\nd0IvZsNXDeRQaShqnx5v70xE63oteMSj1JlyDmgKr22c53IaqZicmRJYBIu0\nbDu/7Mg0+8uq3rxqM/NNPnbHeYnoC7KMSN180F/pZWqDoSHpBT/DbJESk9oY\nPo6mYFm0dHNb9/jjgEnNpxnt2hrSImKjfnl4Bi5wnRQHQ7ipUgYWF92izw+Z\n9Y71oiKSRNgwy1NK8iN1Dp/p70I/v1cAUyyvyiBq3xwRSYIOBrYM9ET2MxQU\nb+HYcc2LE5zeXymv/9NDiuwlG/Hf9tUL/spsPnrCPxy/JGsEaAGABgAbF+BD\n2GWI\r\n=slc0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-function-name": "^7.14.2", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.14.2_1620839376442_0.4929595761462626", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/plugin-transform-classes", "version": "7.14.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-classes@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "a83c15503fc71a0f99e876fdce7dadbc6575ec3a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.14.4.tgz", "fileCount": 6, "integrity": "sha512-p73t31SIj6y94RDVX57rafVjttNr8MvKEgs5YFatNB/xC68zM3pyosuOEcQmYsYlyQaGY9R7rAULVRcat5FKJQ==", "signatures": [{"sig": "MEYCIQDs3iu8bKxvJNDbls1MFt5kifKPWO1ud946N56xQAnqmgIhAO9H+L92NT3lx3bEmLSVzvxL1UKo5Qwj92dt0aVcbtx2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGWCRA9TVsSAnZWagAA4OAP/jav8FliDWNq+24IQ8qO\nuN3GDWhFNkm+RxvmgWR2VF2KXJZbNXnPTj1gVrCezOg6NzU6nm4I95SO/dkb\nl5xguye6iSwOH1yillHk3z5G+fUTX5WkWUktc5BiXyEUsKmoPTn5qW/X2HTI\nw2DqfVu70i883364CiWLc40/tuGjX7mi8+kjv4Z10dXfV4PghrE/lf6Sz8C1\nqKcmQVcrSFueAGDxSLvkOq3UanhlU3sWtrNo7xWuCQxN1sUDd6IVBckZPnkm\noW5G/6qQxQ3rM8SHKAOjaLe1DvIj45WpmThad+Jw+GakaDgj/OkRgXJWTX6z\nvny6nERJ1ZKp8ue8cYyXcWjwptXlVawqFWiOM22/UQlfY4VAzDqdXoUaQMDD\n+jEQHhpFvgV4olDAF8JGtbW0h5BZ1dCMvXbcxBanDR5sm32Sv/lMHpjiBww0\nfNCZKFbDU+N8wI+7xHEML8XXqr16YniOemcMq+frP7u1JObbx6Z8T5CTB93k\n1VnkN3l/7lk6i/JkMJdsMyTN24oQFfLLA51MQtRet9bWR+HTfdccv74aTkDM\ngvAybjP6XGWzPieLhbv9P4haM81tDVn+C5acg/ad/NJU7MnMJQHgrmGptvEM\nBbIwnGXiw52Yf99pKgbuVPI4osqjUbNwlob6d8/Te2vye3phuj27POekpVfN\nGFKj\r\n=FlIN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-function-name": "^7.14.2", "@babel/helper-replace-supers": "^7.14.4", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/traverse": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.14.4_1622221206067_0.881173786418735", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-classes", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "0e98e82097b38550b03b483f9b51a78de0acb2cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-J4VxKAMykM06K/64z9rwiL6xnBHgB1+FVspqvlgCdwD1KUbQNfszeKVVOMh59w3sztHYIZDgnhOC4WbdEfHFDA==", "signatures": [{"sig": "MEUCIQDVCiKzqcmaNB3H9ecVfVj3vW4wx3n10V36kuy4O5DSEwIgIhrdOkf97sqPu49WH1SeofhH56O3TC0eo6xWFuzbvPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25451, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsICRA9TVsSAnZWagAAnKYP/20hzKebOPHjqtNLF5XZ\nem3RnXnlr5fvRJqPJjsbIDamZENqzrsgvaFpM/UarNN5719999Xf+qFsnMEU\nqc2S1SidQp+A9l4zHJAkB34EMmRx0a4DJs9Ej7LQbXa5AZpBv2YkpVKnwyil\n0EOieqfC7At94Zplc3Mg1cb9SPfHiXmTGGTkOEJg1aVrc3pMbsKD5hykTLPR\nw5LbH/XO479aW3nlmJ1DX80dPjKBHzwWs2DY8RYB2NUIbHgaxFItY7T+DHfr\nMtT8o3PDWpflEiGrE0QSm1S7ReO+d30v7tYSqR7odfq8Rv81NSESZwgUItvP\nzY60Tw7qpW1KuG5TLMPTGpioKfScsrQZGKrIzalq8VMRSCyGt7IEitBV2p/b\n+z7ml7aO7U6wdG9JhpRNuJHznktckOC5HSEm/LNB5fgYYI0tD7VQWgZL8TRM\nsNzbGEMUD3INSeCwuMi8YLB0vSKWK+OxzKbvXBt/KofWR58RlIFupyLcaIfw\nCV4knClWomzAHVljf/KlikGPA+JtyO/gUzOqmuJ5DWY5i9Dr8jEjNI3NwUq9\nOegDHsaCHKe3EpUSBfDihETTrfeEltC8AyZnfIXyKfdwXBackE4DkCIrnn0V\n0DUIFKBu3eMsVijsIrEcyAOftYM8n4fgO1Q59bAKEZtod17lN0ZeUK6Txigu\nXDVY\r\n=wQUW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/traverse": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.14.5_1623280392475_0.8315530457516318", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/plugin-transform-classes", "version": "7.14.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "2a391ffb1e5292710b00f2e2c210e1435e7d449f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.14.9.tgz", "fileCount": 6, "integrity": "sha512-NfZpTcxU3foGWbl4wxmZ35mTsYJy8oQocbeIMoDAGGFarAmSQlL+LWMkDx/tj6pNotpbX3rltIA4dprgAPOq5A==", "signatures": [{"sig": "MEQCIFs2HRN1ZGHuD/6MJFHA0oRZaMkYYifbHF/gxNdWIer6AiB50WBhGjbxPfriAl9CYjOw0wk6ZzOgxnjJhXEOd3JaKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLrCRA9TVsSAnZWagAA77YQAJoNNIdVTYQtPMHlOO7u\nissSKAlHAimflbUj8PZqqfBeBKIHgPkLiow7ROHo+5b0kSDLlvGQsXicsX8Z\ns3EuSpzaaSYX3JA5az21WCVCpjxHzxwzBMaVSS7cTv0vtETs8WsLT/VIqoEy\nBOtsGd/eiTVExe7Hsy1PmnnNUyvFlL2s32Rtzh66Svi9acrkH17mk64USGLf\nBkiFKYXU4t5U9UwmA9CoSigCsRuHaV3N/50vNJsJReeQiJHTf76N+beiycEm\nWHZxbATRHZTAX7GPUK2fK/L53NSFZpHjXU+hG11757oc6MCJIlX0Q3u8N8r3\nPXGMEfEDiIXoz3b2vykSTTqmP/K3M43VFqZ6xWRc5jq9YHcHYE2i6egW63td\nz3PN6dy7bz7nVehUlAKangLBxGm+Csy+cgbPjTkcVo7I6yaRvESL9UNcM6CE\nZdeQvYT7WGbA+WxCzforzH5+Xwa5pMjLURGDPcWB4gm1vHzym3TCcCDfTNAF\ncDdUrYnLICQYI2T0gm0Rshyuy0oOxRkFJxYXfT1EZfZ/b0Dt6qDt8XFX4S/a\nNXoASZJHuQ5cidjl3BkVBOY9UUAloaKUwCuh/bwA1TlSsZl1zuy81SdcxtMM\nuIhmio4ypmjBbLY28RLHjp5393K2bDYEXXToLVOkR/ckD9fFy4aUgJaSX5BR\n1VL1\r\n=JH+h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.8", "@babel/traverse": "7.14.9", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.14.9_1627804394931_0.3199140238202831", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/plugin-transform-classes", "version": "7.15.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "50aee17aaf7f332ae44e3bce4c2e10534d5d3bf1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.15.4.tgz", "fileCount": 6, "integrity": "sha512-Yjvhex8GzBmmPQUvpXRPWQ9WnxXgAFuZSrqOK/eJlOGIXwvv8H3UEdUigl1gb/bnjTrln+e8bkZUYCBt/xYlBg==", "signatures": [{"sig": "MEQCICokIgYkD5VNSbtpnol41NSHIBoCXEnssi2uwCoGPn7LAiBmPvJ0VYA4M2VtAp2vtG5n6u+nCsBp8GuqVp3QDCwjRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSoCRA9TVsSAnZWagAAC38P/1axAneCwb3ZAbzWBXQ4\nbz0EVjHKxYgLSvvkOzMZIYo0JOBUwjtljUvlvDrRWF0WMc1NM06vbh4c0N2x\nt1hY4q7o8l+3ST8YfgXOeiCkLlzuVfZlWQYEr/OpYGYbEs47AqKwznPdcTQB\nIzkEslZdaQ1V0L8p89tzaebw6n9OuiopWo1wpFFlmBQlbueIq00d4TrQ3QPd\nrCNB75VhVMq44USTHsHtGQ1FN468gdp7jrY7cSUV74wnLxsdp6f9A4Tpx1Uq\nx6Nx14W+B/eFC/LFcasxHgUp0cQ9vE19klFRSxurkEnifuLinq+6Km/IV23h\n5hbEkpB/9KZ08LWbD6k7Kj6AFdJEtIKk4PJl5GpsDcM4sfaZS58MRy5olVah\nwY3J1CyWAOdFtGXGrdNIOzpwHSxXVOygi2gYUyQTVCNgnnPKr7zFHWwujfQj\nKFHejXTSfFtv8MNrsn1hk9SkF4vcgwA40vW69nZOWilPmyL4CXL8UltH8JO2\n724bKVgUDgnYG4LRqWmEFheMUZWSqfhwKJN0RmAmB5XogIJZoKcu4bLGw1++\njvQieiEzfIqopuipE1qaMc99HBNRUDotK8HjrWATluriqX9F4pu6feIMyMfe\nuvEqEaQ3IIAdQgjIEUlZHtv50gzCXaoh+Qz2RvBNZ0Z+Q6+sZyKBDEHR8ygn\n3Vwy\r\n=QT6V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-function-name": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/traverse": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.15.4_1630618791913_0.5312303223980428", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-classes", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "54cf5ff0b2242c6573d753cd4bfc7077a8b282f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.0.tgz", "fileCount": 6, "integrity": "sha512-HUxMvy6GtAdd+GKBNYDWCIA776byUQH8zjnfjxwT1P1ARv/wFu8eBDpmXQcLS/IwRtrxIReGiplOwMeyO7nsDQ==", "signatures": [{"sig": "MEUCIE1UiOL7HrBOyHu3edJkdT8AidAGV6o12VoBzggLM6N8AiEA0dbIzQAmgHGb528+1V/tUiRuet9Fxa4VfVQbGGq/sG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26056}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-function-name": "^7.16.0", "@babel/helper-replace-supers": "^7.16.0", "@babel/helper-annotate-as-pure": "^7.16.0", "@babel/helper-optimise-call-expression": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.16.0_1635551276165_0.8637343808500015", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-classes", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "6acf2ec7adb50fb2f3194dcd2909dbd056dcf216", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.5.tgz", "fileCount": 6, "integrity": "sha512-DzJ1vYf/7TaCYy57J3SJ9rV+JEuvmlnvvyvYKFbk5u46oQbBvuB9/0w+YsVsxkOv8zVWKpDmUoj4T5ILHoXevA==", "signatures": [{"sig": "MEQCIHN482Zzos3QFzxomu0vRspEGfXLb4MxyMLgc3AbYRJlAiAuB0IUdQFzdQsNRCDTAITOWQ43S5k43TNQM1PqyGpQnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kkCRA9TVsSAnZWagAAn1MQAIL61usrCXEENlmW1MTU\nu80uhMwf39++A69VF46TMulZ2FyxTAP01k+mSXoqFxW+yeVNjpr56wgtQjBr\nCoOZN2q4vkDiF2Jyn9bsG4Iiy1KO6ki8oIfPEPZH3L4TTJiNeIwcHSUH8YTx\nAQPGpm9iPqOTzDeRPGlB2HpuwzoUwcYfXALUgF/lrTSX3bZPn1tU2r8Vv8TC\nOSdFgytDtTkSf0xX9BshVdjvDFZzXG7d3c2xwC9bJ8Q7E/Jmeq87XrV6cNEy\nqicSet6tHAFnJNv5DhYb1oD6msXZyhbvlFV4vhnwJ9NnS7r9s9JZ3Mh/J170\nCh6U/bACp6MM7IOhyaGB1E9ehaFuPV207IuC4KuSh0oUDHAK0ymxrUJE43BD\npojTjpn5sHbU/0gjbsZt31vHDk9k3z6YUqLnaMf+1n+EdwGN+Y2Y2/ssR531\ntS+KbbYsVtgHzjKl3XgrT79IDLPgc5VvVgWt068Jk8/x829e3Y8Tr2oqyO7Y\ntBMryMZrWkoMMmCz7RQ34/XXfjEtdl1SU8IOCQSk4ecu5sDzc+cnl8LZDRXg\nsRVxIj6oVAKKT5r5fYKycujxpy3qKy5WW3l2oIfTHX1prU6K2ibgb/4ABFDt\nHv1fM9sl82w1vw//kQEnDwzPK1htsVXQv5ffnWHapDS0HkrPLlBJtJuhSO7Z\nHSw7\r\n=YL9T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-function-name": "^7.16.0", "@babel/helper-replace-supers": "^7.16.5", "@babel/helper-annotate-as-pure": "^7.16.0", "@babel/helper-environment-visitor": "^7.16.5", "@babel/helper-optimise-call-expression": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/traverse": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.16.5_1639434532231_0.36318752875592475", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-classes", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "8f4b9562850cd973de3b498f1218796eb181ce00", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.7.tgz", "fileCount": 6, "integrity": "sha512-WY<PERSON><PERSON>38SFAGYRe64BrjKf8OrE6ulEHtr5jEYaZMwox9KebgqPi67Zqz8K53EKk1fFEJgm96r32rkKZ3qA2nCWQ==", "signatures": [{"sig": "MEUCIFNtUDNukIxK/ctWJ7fcV42gGIRJ/UiiB/f57J7hTn1mAiEAsbN1Wa8pMMkuELLLBwVeccMSIvbfwTCVHSCIjp8YMTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1oCRA9TVsSAnZWagAAJ1gQAI3lMZMcmaTv7a5pBZFg\n2idZWDQkVFsAI2VEf7DtD/alnqINbWz5bJMpqs/jdxBbbeBv7LtWg8bAFPkR\nQ1mGVOQIRw3Ah4hO7h/GCcjjNYXqHcO3CmeJqqxkDOP1YSkS0YI2+Mfr3fby\n9uUnPpqzoZmxll04Pth+Y7KQxiS4rVFLe3r2P2S1lsT16Nm/1jYk6Wm5OO31\n9WaE28rpcLErYQMr3rDGkHXUwdyYvql6s4/W1yPXwORg6AlBF8teB7CvF+Cm\nahxPyklbqHavupLaKR/izzVMaX7ZdFc/PpUt9M0v5hTdWTIU+5jt4BJeq8Fs\nhGueSSaWExeTKiOezOwWQhZpAIUSeBb6ZXWhTZoehVtuEJURsZturbFXEZV0\nM+viGkyRRTHk2uBwqCPlgmy8VsVJon5j3pOX+t/ivel9qQPT8ZniroiV6PPd\n3WBW6lFvX5Y1aEVo4VCl41X+gGOIfErDLfbUqgAuhAekeEqx0qLsP5r7x6oH\nqGDB2qLUanZVlrUz3ZvYPY2+8bRAncigxv4HhEpqWeFnRd/6ERdTeej5erOt\nmNM7imTTGfHRGS16+wWiLqabZ9s07Y+jdHpN/QU/vZ0Qu00Y7AIxONb0IJS8\nQCFnw0EI4TUEgiEY1iqDOcNpUvfEJHXC1FEUuCf8bmTZIMkjRl4Xl+LtPUxv\naf5a\r\n=ii87\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.16.7_1640910184670_0.08229367092450746", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-classes", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "da889e89a4d38375eeb24985218edeab93af4f29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.17.12.tgz", "fileCount": 6, "integrity": "sha512-cvO7lc7pZat6BsvH6l/EGaI8zpl8paICaoGk+7x7guvtfak/TbIf66nYmJOH13EuG0H+Xx3M+9LQDtSvZFKXKw==", "signatures": [{"sig": "MEUCIQDwaokEzmMfUYd+rgxGrI/PqSQ4N5Wpfy7YSx5EA2V/VgIgbWJSV42WWxu9jbdjWoh4kgKi8pk/X3w6VA21YXDmKAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx6w/7BcBNjb9btmWTIJFaVnQITLHiftIXVqRKxS9gFVTPWxOcsTYZ\r\njgc5LN0cEtlLkiM4NNbkmJz25Xl+U64n0DRYE1L/mu2sGQGndHLLiOQ25Lhw\r\nK5TpjMjDXEVKgW5DtuQxb5xDblNrTCAAmk38O6QbxNw8nZvv4qRULSqKQsx9\r\nesJzO8NQiErmRPy9tLNB8MpN/++a69LF/5XRlzxPdYtfTKvaoq5WGpHdgtJj\r\nGK32NaoPxyzumBv/4QBTfs2wVZysC2ZHRFeON8LlAvdXy1n3BcezFCAkRVzY\r\nEoAXduPSfWsHX2Y+FrFJE5ZArl12eFLilgMivx9fRylCLpumxn4DSYr5MeBq\r\nV89KSrVm3wu9zgv3t4cg5ow9jRT96DoSi0g38xp0APSdH6qAA2DesooMrEKM\r\njQywRSd+mkrZEoUX9C/X80XVs6T+Wb/6wvngHXouAvsH0BmcPqWKS9z5JuIL\r\nC4ntYXsjndXw1KSXdX4LGOzbJOUteHzKhAIrrkH8o/oQyReJUdhnn/hxaXbe\r\nC6KSb8KZ98f8RinGG/ZD/4c+gKvIXxaNQIg6uRmS50wl9qrjiyIfw3HBxLzz\r\nqBSiO2e8SlkWh6pDo9IJJHG1vMN9MbpzSZgCYVkM1i26IxwPvkz7e+04KqQg\r\nEoSyeer3tbt2iL96CptKPlf34Go/nAmRTyI=\r\n=bikI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-function-name": "^7.17.9", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/traverse": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.17.12_1652729570263_0.12573117895065256", "host": "s3://npm-registry-packages"}}, "7.18.4": {"name": "@babel/plugin-transform-classes", "version": "7.18.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.18.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "51310b812a090b846c784e47087fa6457baef814", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.18.4.tgz", "fileCount": 6, "integrity": "sha512-e42NSG2mlKWgxKUAD9EJJSkZxR67+wZqzNxLSpc51T8tRU5SLFHsPmgYR5yr7sdgX4u+iHA1C5VafJ6AyImV3A==", "signatures": [{"sig": "MEUCIQCqsqkTK9smr1KfAkdhip2XfT+gboUygBb4RyBjk1+e1AIgEFH0A8Ntk0D5EQD4BN9SCHqnjuGRsvLHBOYND2jW8cM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/PxAAiq3mRmlT6ZmYodPdBI6TVPvAX/m7phLrG6Vki5aV0jIRgZbH\r\nIHmZFzeVg4T69MpxkfcMUI4cIDin8gaOq4lmERsvGFhtCLQg6m3e94ywYMWh\r\nlNFp81/an+Gsr9QKFTnlfCaLxJ0+xtvKyjrOjVLBc4g6Cx7UINWRZGWVeLZG\r\n2l1uDJupqR3Ll00sNzMkxpc92TVEujzQmFnh4J3mWZ+o/bfgwYpLWHFioP7W\r\nwwOmRoG3PI0HiiiZms1TZJlFOCZ0PS2gFG7zphqSfSvg/D9aRnvWElx+/dTC\r\n4Abt/Cb3W7/LA7FHjDZf966TW7/3z67S42QaAAMIGgyJLDeJNtOFfXei9tna\r\nrU+I1aRhuneHFmYVyYHZq6kXgYSf8DEFiXy2G7OGD5FZCEaiGlePgSjwsSTu\r\nZd0PXQxjjqIyd5K2yS6BEMI0bp4CDNaX1C/w2r0gBOuZ2QISUyG1vgv+PE9l\r\ne9su+Uga8VwbXK7V3JJrX2es2G5qpvVHYTRURp4xUsiDR1vFAYCXoRQa2RKA\r\nUhHkyFmbStrRAu73ckiygyMo+4MPc7I9gYyvEkU5fo0o1J1qbI45JDLz3EQq\r\nLrcln7+RHZ9XnPuVYN089Wz4DAcX8nf4WNu2L/RyB6JGEQDbMETSMt/hxPEG\r\nNDXa4qMOkACQ6wzzNmE2b7Hf3nyCyeE/YMM=\r\n=tuz2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-function-name": "^7.17.9", "@babel/helper-replace-supers": "^7.18.2", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.18.2", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.2", "@babel/traverse": "^7.18.2", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.18.4_1653861011464_0.7143265125588543", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-classes", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "3501a8f3f4c7d5697c27a3eedbee71d68312669f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.18.6.tgz", "fileCount": 6, "integrity": "sha512-XTg8XW/mKpzAF3actL554Jl/dOYoJtv3l8fxaEczpgz84IeeVf+T1u2CSvPHuZbt0w3JkIx4rdn/MRQI7mo0HQ==", "signatures": [{"sig": "MEUCIDgyz2lOpn4ZX2DFRmudMlE5VfUVbyvJ/ZAPj6kpD3+lAiEAlrGCEy4g6XESXiEKiEElLiVWYWLR+GE8CGbBGZst8jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXQhAAmTgDKEVZy/uySsoERLSm3fL6AVeA/Zo24C5coog88qvBA/6g\r\n7w9RDQpo+zJCAKWxueweuumCo8cpryJbdyhE2MKWgLn2ZTXLKaKdu/lTVJCT\r\njNVlMSwiHOrrVYrQbINRZQ2zStuUJFBs9H1rLzfifQHoalzmyEeiGGRypI57\r\nHmDN6rwnjUahkv2zl41scr6NPDw1RWehbu+FT0t85rYzUvaiBJuy93IiKLkE\r\nhrAHDXgtVI1apS6tlPtLLVgbDoxlnDVRy/4dGVBVAMyhPiyGtHpMowex6MPH\r\n3geaGTQUhLw2WWWO0YNmGh+WOr584fIRflEGsWTmw3llm6BN3E2aiTLVB0gA\r\nBPnmDjQOfEz+J6zrS7MWr3DI9YiNQhZkwb0FHE0REJoK1caRMHhC2VlqxCNF\r\nRSOAolqxJ3DJaf0fNmjYPm3h3a432oED5PdMAG/qhVyDVBk+zDinLCYYfeVB\r\nwVLOKHYgeeUO1uC/bZ1ZgiPpg+ZCZgXlIvBNnY3w9mxlni0cw0lKH1GkHffc\r\nzYOsf0Ir4BLYdzJkrMPX2wiLSKUyHmjZHOOd1UiTopv7RAG7j+PnJMDYeRth\r\nbOTsjkg09adcMipM6J/HldtedBNyXHHVvNdrRn3Eo2HHz7EFvriyszOfK3a4\r\nSSiUzgnIMPy4KV/uHjUiOxlTaD1lUUnZtQ8=\r\n=AJ5D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-function-name": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.18.6_1656359440337_0.4136805730791331", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/plugin-transform-classes", "version": "7.18.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "7e85777e622e979c85c701a095280360b818ce49", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.18.8.tgz", "fileCount": 6, "integrity": "sha512-RySDoXdF6hgHSHuAW4aLGyVQdmvEX/iJtjVre52k0pxRq4hzqze+rAVP++NmNv596brBpYmaiKgTZby7ziBnVg==", "signatures": [{"sig": "MEYCIQDQiLDwEGPsb3JXTvjWMsPU3e/esnfFfvIUDdtK6zvKDAIhAKS4RLwcUqDy2fUZ8Q3jUn+l1+zysg3mjb1lD+meYiJE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/myACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr82Q/+J7VnHCOmdDyaSQztj2p2gr7ulSiYhqtq8RoXVx2hqZi5715n\r\nLV3e14R9IkZre+XhcrxQHjm45siedLGFCrRrs6Bnh1KhnpihfO/AuUDXXHuu\r\nXZV1ltk1xVLDOeGivJFsk/Ac/i69/LU5JK6SJGS8C+FBxKa5W0nmTHf7BD5c\r\nAslwAOqzCaG1bf/Jdtsnh7rqMBaso7VDqz3dU040xoLt4mLb12RbWa4fKH34\r\nQdaXgokoKkv5zP2TMWrcA07edxQQx2gzalruDn7Z8IkEdyEDInPIMe5U53Dk\r\nOe48HNC8TJ4kCMC0hi2u/lHe/3Q361dGNRNDdjtZ8HZMN8Wej0bqINPG0j+d\r\n/tzj0XJJY5rToLgHjqrmOdO2PxAszc0BR5isy5sKVZF2OCrvlEBbVkR97Hr4\r\nFZ9AgnBIAgUmlwmuQxHBUsHsFcqPBMG6BwUrizTwoL4qal6C/E/qNtarBS6C\r\nMMSAtne0Tv5SyHC8ZsM8Qv4FUD8Vhz6JybdTemEBj9AhAqyGAN464PFfIYSm\r\nwEwL8BAOGWgrdqWugQ2Uac2nSDBsW9ceCTKjcLLO46zLGIcv1UtYy8+FAjLt\r\n9wgW6EfprLFSAnw42JDrr04XjKhkiCWrM3KhCI5+V0K04LJcemECm6Ezwmwf\r\nl0THzlVds4C27D3XbbxRPOmMnd3e++09+/Y=\r\n=K3Yz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-function-name": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/traverse": "^7.18.8", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.18.8_1657272753774_0.8036379697818652", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-classes", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "90818efc5b9746879b869d5ce83eb2aa48bbc3da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.18.9.tgz", "fileCount": 6, "integrity": "sha512-EkRQxsxoytpTlKJmSPYrsOMjCILacAjtSVkd4gChEe2kXjFCun3yohhW5I7plXJhCemM0gKsaGMcO8tinvCA5g==", "signatures": [{"sig": "MEUCIQC0P7za7iThBgMRop3Wnz0Deswzb/Uv2CKRML2+w2ktuAIgQTJwlbzq3SPFV/xSoslBh0VHZsTJeGNmYwGjeKwMjnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrehA//arMi9Y+OQrKUgfgOFQs2Rz2Zywpb0AARSOCik+kRgJqVTbh9\r\n9+xzk87Ff6vIh0MAcwUL+AqfF5dWVTpYCjoE/wDCGnYSqb7b4bn32XrVfq5P\r\nw0eKOnDy5NQDHgdVaf1JjhJ97b2TyHVKbwHAYpu8W1Py4lDOle9WlKRzDyZs\r\nJiEeNSonXuJuUyF7aIsDNoSeiTghA+r+f3m2rXvdWjhDwMdekeLRIsb0E2le\r\nEvVlQS3MTJ/JIrL+s4pPvjdcHcFPv6FJz+baKNSb7dGjPL6udpjoQqovS4ur\r\nXLhCAUkp5gO3Wpmswf/r6S4YgXL0jAqGo1fUZhmIEGNsV86OJkitA6PQcTXD\r\nJMevx3Zdc75I206MJ8zumJkRImsVQD5chbZ0moDhTpoq47KKZsQAqPCnmmWr\r\nQ5qIvAFHC/Vs5ENYME99eB0hWvyv2Dntn0jshPWev+LciIDtPr8FV6J5jt8c\r\nj6Q0PItwXr+lBWI6NROlpZXQbC4vl226/S8miNHlHJvKd4actwdQc1PdhFl2\r\naFD0jKK2hA0UYFl/UWtDQktlpzetJX2sLMDLL8ViZoWdVY8pW9SVgW08WgbT\r\nQNBAijyK7xDxpwMV+mhUXqrTjnGkLuF9OnYq3JAGO3zSwf1WURatS/mSxYN9\r\nHciZJRVpJGvXkSi0+x3AWZrfxM4s4KhGbXk=\r\n=db7m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-function-name": "^7.18.9", "@babel/helper-replace-supers": "^7.18.9", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/traverse": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.18.9_1658135861914_0.8250071334722646", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-transform-classes", "version": "7.19.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "0e61ec257fba409c41372175e7c1e606dc79bb20", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.19.0.tgz", "fileCount": 9, "integrity": "sha512-YfeEE9kCjqTS9IitkgfJuxjcEtLUHMqa8yUJ6zdz8vR7hKuo6mOy2C05P0F1tdMmDCeuyidKnlrw/iTppHcr2A==", "signatures": [{"sig": "MEYCIQD3sr87SbrnKQ5BtUlarCxsPY3B5GtuUZFYuvyv95BIrwIhAI1JxJgjypjFvCEDBvQyj/YTj5jmZcxOun2d9VDFgRC8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1lg//amanMlxrIVC71dlqKu2tmEdOSN2aVjU9OW82ZJ2sVdJ555kx\r\nrtNLI1K3tvYx9ELkXhOz9iUCfYrNWeY40i8S1PMJbi+O+7pogcLckDV2LYiE\r\nzd+8e88FiaHrZUzwVlT+BDIb900XLXL6CwBRq1O0Z2vYWPwqphTZII/SAVHX\r\nFE1SUYbmQk6X0eMhvNtAtnH02YGWVg3VYfYMF/X71eVOz6ZJbAZ4e0YqACD/\r\nSOWaRjaPLnstd0z+E4gSbGCDxkFAjhlRI4ulE5kTBc+r6XUL2iK8GVfJlvpr\r\nk8OFbwFLHuY5rawKC6ffZ6WjnGaPtVu/HElmAmByr1FNV/qSeYcdVTPqDoQb\r\nIEcA2rrNqgmki+94r56qrhwyVZbm76883xreMQspCLVJLd7wXZbAl0yBx9rz\r\nh8jrjLrJr8ecl7N9n6alQn1d5ju0sanSkS0FezdIuZtRn8GlnP+4z2cvQRT6\r\nzjt6lF1IlPzRgIp790z7XHhLIBY86feXaUroLrzJwA0EGF+evtl4MVjR9hC3\r\nnymfYup3uwjSC0Dbj+Dw6afWdDJSxMAfjk0FX+svbGJ0x0h+MnKdYqlA2+zT\r\nmbLq/w/LF7nvWziaTf4PcQTgB5o8J894ykQJjx3itx/ei6jq8+y+JF7/HKZm\r\nzTuu2ZN+L2xjk73U1eL3NVzRMvYibo/k6Uw=\r\n=cmUy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.18.9", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.19.0", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/traverse": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.19.0_1662404539937_0.640653404338041", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/plugin-transform-classes", "version": "7.20.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "c0033cf1916ccf78202d04be4281d161f6709bb2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.20.2.tgz", "fileCount": 9, "integrity": "sha512-9rbPp0lCVVoagvtEyQKSo5L8oo0nQS/iif+lwlAz29MccX2642vWDlSZK+2T2buxbopotId2ld7zZAzRfz9j1g==", "signatures": [{"sig": "MEYCIQC2rhr5pkj9Znk6+ywQ0Me18UCLjTjQjddurkirbvZALAIhAPwWdSBHEz3kEN524tcaJDE104vlWV50ObWM2c9WGI35", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIeg//QF7Jrkyq65Z8bljExn7Z/AkLg9DadZO9KanUoS3CvuYtoxQn\r\nrfhsTgBGWkYvwzc3RXkkB+LRYIml2J0Or4TJzOdYVZV+rAlreBKFU6Y5mawA\r\nqd9LmLTtMGOz8K8BEOkov8mekV7NbdqKPAvmBRBh7wteXEjL+Gpz3DNbS0yF\r\nMCT0OwC66BtNx583bTbRtOPrW9f46vw7ClgHDvoc4wvTphLhM17RSQhd6QRk\r\nvVhbatiekWxjDZwSBLYO49idoeWrh63mKwCmFIjwCvr9YB4r7RFWosPjVEFd\r\nTjd1SpESCdSCUeowxdsYtRN9ytXVG3K7nKq69Cdoye5S2bcX8RcOZp3RxOal\r\nPYmrxbNBNPpQqmHU9HQMocird/uOthsptIFQsOAkyf8oNciaDe5LNNybOoI6\r\n50NOAWvbUlzCGSMEkuwzzG2Vulpl/oCSpe68jeIxD3HMeka1o99l69pgU4ZO\r\nujF5d7en2ZwHYqQwDjtIn6+N/Uxl4RMKg129rDp7k8BNtxIxHfZ8afo2OdJw\r\nXZGO6dB3SwXDGAWPR+lRG0N6TxepnYCDtSc9/aodfx9vW9SGKP1k7AAAqnaE\r\n+BmnIIEwtilYmQ1FL4jwdMNTSE3wtWtGaNzrRNKpgqR7Ad4hFE8hndqMUVr+\r\nuN1V6ZyTCIj3Jkf0qSBfA22j3N+HqHvWFns=\r\n=1XTr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.2", "@babel/traverse": "^7.20.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.20.2_1667587867602_0.4411748207243571", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-classes", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "f438216f094f6bb31dc266ebfab8ff05aecad073", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.20.7.tgz", "fileCount": 9, "integrity": "sha512-LWYbsiXTPKl+oBlXUGlwNlJZetXD5Am+CyBdqhPsDVjM9Jc8jwBJFrKhHf900Kfk2eZG1y9MAG3UNajol7A4VQ==", "signatures": [{"sig": "MEQCIBbzRpesa11OaXKT5wKwVjGOWLq/Afh6rAaqBbOl+d78AiA582SRq3Veee1Kl+MyGMV7+T7SaAD2of9Y2VNF7Ejvnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUkA//WQ2ttbWhfd19eqn6TOFSvwnXiVHXze758us8MMb9y8v3Mz7M\r\nW5I5CFmSrCqviQRddWWNsAK/NMgL+sbrqpqsLoHAXomLyGfuwzaZ/S45qEEL\r\nUQ4OIVcCvqOKbmU3Ork6w8SmNLH+int8Xz6+5yX3xunbNG6heCiB8Y0SyoXg\r\ng/4nddk/e9BsayQ2NXBEm/jpL2C6L9RRQv2BBTN4V9ql9sc9YjJ9k/ua6IG5\r\nxChmYr1gFHlhztgLjqU6dhBhzLb+A29CZR3qgBHVMooKzl66x9dc9FO//Lhb\r\nywKWzxjdpNHGtVtytzlXYE406OOZbHQutAZWB+xNFGdoVqtjUlFCj3Cw3C6T\r\ntDSwge42N6rVy9y3a7/OFy2inHfyB8iPIaadnwblPC3f7muC6IX9u6ovQ69K\r\nojThPA/lAAWA4hwgNHhVt183rNJVGQ4RPobXFzI9h8dDDuyzY9ph1T1TWZMz\r\nl5+wT1TEOf/7ZWJjA+e5Er18In+kCYoCZo7qQ5Pexrc2TRsDjn42tfDUxHgp\r\nTfJwTMqsuKfBTrdudBHSc50zkiE8SbBJm7hS+/IHjSVnoLl09j2MHgHNNhFW\r\nNvMp5PsNghHj9KkTPDmXdgcECmwaOkSYD+Owl8ks3aFvr8jpKs9Wt1kyWzPh\r\nI+Y+GNHdqHKVou9MTuwCHPAvngtoKt+mpWE=\r\n=MkcZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.20.7_1671702337326_0.9679345271453832", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/plugin-transform-classes", "version": "7.21.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "f469d0b07a4c5a7dbb21afad9e27e57b47031665", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-R<PERSON>hbYTCEUAe6ntPehC4hlslPWosNHDox+vAs4On/mCLRLfoDVHf6hVEd7kuxr1RnHwJmxFfUM3cZiZRmPxJPXQ==", "signatures": [{"sig": "MEQCIH+6J84B4GEUtLVyQH2gGHfaXR4mxkUhgXORddrP9kAHAiA8mHjKWqeExW1UlWNXTtmbp8XAdqUH/xwd7ojrVRHMRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkTRAAjU6lc+RldfQUIAWliIa5Uyjq9r5lYeYGmP90UmSAX68qsWbx\r\nBQF+feXYglx3stS0tVhVAy8AIGd6YbPRv1QQNxpHMvXSXPJPqwdUR17mpzMg\r\nj1+dz7NThNEXYaVVF9DLdTgCggqZhK5zhIwon7zzJzAwnJne73DqeiDCbu37\r\nUK49XlaDGN2Dmcrzqc3Ykz6pPhbRSk25YwCtSTYTd4mwOmEJXXUNpmOjVJfm\r\nPaRXWexSl/znps3dDAFEf494B2AAt1gM5I/mzJgBpuUq1BxyGsGOfwxTFO+X\r\nE0BjSsM99SQgny6iZATZkrgKT6Ai1bXU2u/bpAFdzW45xJ6mL4yH7MSnKAAE\r\nwLnU4KTshU8vm2tCukZA/9w8OJCEZQTq4RPhcUPSBp7Xw8+cT7pqkq7nUlQh\r\nd0T8B2zbq1iZkP2iqEjGD2pDzIlrMI0bpPCbmLd5cGyBJApdYvotGOAJdARz\r\ne0Vcv8/CZuIklmgDGauOCrxG00NeMqpmMD5i6Gviy5L0fRJcNqvF8AnJZXGy\r\nFZJxF19pZOLqKygqK85QsJB/nDdf2Mleg/wogeL0zYSgk4dzuXc3RWBJ9o5L\r\n5E23yilFs1BOsvyK9RAWYL7AIJeUmxNNg6sJZvqfaIbl9fnaz5Xk41SjhjEd\r\nSYWQr9RP3sLUuQRWz9KtvEhMuK+Or5d6VAk=\r\n=JQ9A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/traverse": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.21.0_1676907073914_0.7978412881355086", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-classes", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "a8af0bd01467fc1a715d38da36fd9f1b8a5f0dd9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-Erm4Njn+aaT7pkKJ/WrIuU6qsMzOzT0a08LyiaA/H0PRVO1KP2L8OUW4xCwZiLuXdLErNlowU5X7A/zL4Ye3+g==", "signatures": [{"sig": "MEUCIFmklesSQq91IwqQfIYvM51xYpE0UC+x+KzBG9A/lD+nAiEAl1/LvJCFrpyzDhqBhfAQNFpfiABJ2pWzL2PjvPmXJ+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMiRAAkH9WjoYLBUHe7lVsyUsyQTRLMeZxo/RYPwvtswqvhiIPtx4+\r\nhG56zgJRvi5kFrmH8LrP8sErsGCPQvFvCAJ5R8nkMzTN36N9MXQtiEVqy8Wc\r\nXZqgsNoL8UoUgT9xqJwgK1v38TpraqubO9RLQZcb2Xdo2bEdgoQAZKz+E5ZR\r\n2mgRHGFhrvp1goiEjnTaDkIVTQUCPSdkvj5qNzq1MkR63rG5KiqPQWeaB7ae\r\nf9U6Rz6DgfKS+uV7ta8W71VuQevBs0WwYTSCaRxf5uysEPomt7/z8cgbjH7i\r\n+fJGcGHpPf9lgm0ZCr0LyTs52CB7NZlRnsbptKlSYYD8KXD287c82J9Y64sR\r\nCtfFcuwgX2hqAiCKNDChTkbE1bsL5u3K4WTMoMNrR2vp0BtsLmUVKIU5GH2O\r\nvZ9ol4F2FqJVBtqITpWRcyL0d8Y+0OlHKzYdbBR82ukABZJvctzTUrWpagw1\r\nyhTRZYwnhnwhkQsSdEfF6RjBe/f/GGA7Y33ZhgUiZWagEu4hT2bLGMpW61cB\r\nLy8pd6QPwJIqiWQo5Yc0jC8UQ/Hx9ipn+eM0RB6lPABX3L8DXrujAoGOaUD9\r\n7EfTu4sc6Jgr3eZEvdBOwV1u4587eQuDBcIvlLTbnlfQS04UVtq9DWrvZUpw\r\nh80e7m3DlYM1MXnbUO9z2AkuruFXOkAYz7Q=\r\n=93IN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-function-name": "^7.21.4-esm", "@babel/helper-replace-supers": "^7.21.4-esm", "@babel/helper-annotate-as-pure": "^7.21.4-esm", "@babel/helper-compilation-targets": "^7.21.4-esm", "@babel/helper-environment-visitor": "^7.21.4-esm", "@babel/helper-optimise-call-expression": "^7.21.4-esm", "@babel/helper-split-export-declaration": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.21.4-esm_1680617396958_0.22146666885557686", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-classes", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "d4ed3ecf0b55e87a7e7eca2198de66457ba57e2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.4-esm.1.tgz", "fileCount": 10, "integrity": "sha512-XkUCYoCgbSIEeyIYRvk8fC82z39Z7tq4FzMccmFFaX8MMhwmH8rrc74MFF8RGPD+BBrXrOf+L4lvNQJ6ubjKjw==", "signatures": [{"sig": "MEYCIQDkTdXJHkGdIuSjQ5q+nMlVeBVn+Quve6FlDoZ45amLHAIhAIHi9iZoF9J8ALSv5VOr9+vZ4m9NF8Zin4L69AjfKpQw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7qw//Y1Kif9vz2NqNess3M1RrpcW68auDyIzcuJgynkZAHhYoF+Py\r\npDJuIo7lzyNk1GBN06bz3wq5X3ZPS7lK/umch2Z8lwb4bq7BhBdkbisKxolj\r\nnvEVongmSSKWFX2F+hEw4Qfw9twu8UbS6rUbPz1fOneEsMR884n4b+zrnsHq\r\nQzkV5EPiXLF9z0u9nvFfuG6l4s93XRwsnWn+9hUpmtLTFRzGnb//qhzwZ4g7\r\nkS+I4QHIRCEm4x0MsLVtXoJ/e3GMEZqnkkBlY5aC8HUAFwCYOUlHyGX54bo0\r\nX5YteN/7lZhc8VFvqZLbLStZjwMyFd2+zDTTVp0fFzFoT3c3gOBwmmu0f8LQ\r\nfGrxftMTVVgjg/p9GxV59JDdoCZp539UsIcs7vE9+qMeI0CS50DB8TKGlSMw\r\n6ygqjLOFPgEPku2NBRNrO5DfcwKFEX1z/QkTQC9k9AbFUqMhBvIwG2QXP5SD\r\nksDNLfxWWpT75uTaL+s7f5ONIukiDwn1KJPyGGtlHVrt4bjbZ/9ZG3gLcASZ\r\n9dL0V8/qajfQDltOLpGLAeP7CRestG2eVpU1CS+h8BKXrUV2yBjSZ6SNWxKP\r\nV3yq4O5hxaQgqfSPM0Q97h4Ccu7g0mvuy+pqJXEnZejDWVPeuHgTxPxwx+4r\r\nhuHqmE9hrfeIMcaQuAfFdFBmWBL4ZMGr/qk=\r\n=n7dx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-function-name": "^7.21.4-esm.1", "@babel/helper-replace-supers": "^7.21.4-esm.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1", "@babel/helper-compilation-targets": "^7.21.4-esm.1", "@babel/helper-environment-visitor": "^7.21.4-esm.1", "@babel/helper-optimise-call-expression": "^7.21.4-esm.1", "@babel/helper-split-export-declaration": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.21.4-esm.1_1680618114798_0.4071001467057298", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-classes", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "71fd3033a1b14bc666edaebb00e1609d542732ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.4-esm.2.tgz", "fileCount": 9, "integrity": "sha512-9heW/vnymgtlEVEkqZddQcj/BZ1f67lx4v7q5xd+ZOH6owYXBM6ulfAE7iiELYhWkmxAo9sXyyZ7Rtxf68Xe6w==", "signatures": [{"sig": "MEYCIQDqfbZWQHtvBA6x8YpZa7SmRhUkpK+Azoz6hQQtf7J11AIhAKfzWpaCfJ2CSW57xfyot/evMJ7UjUAgQLensmFG5dio", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHXg//Y6+t8sCTMfw7N1omJ90DtnZ7OLgi7wxOzx9t/oXJ5hDrzlOH\r\nZUaAQ5UbxOUAXRL39ZH9rJNP1GAqhMnLXezMTdGhHn6ZXNnva70Il91K1z71\r\nFd8Zs5/GnDVf7af3Kw7g/T+RVe8VESMUGd77d5MBUDHQIIpdrGCfCFkZexKA\r\nu7uLYiIxXSqMLb1c5zzWVViH2WAI8qLzcNJECFstRxJaMHa7D950lqxDBfK/\r\nNJg0APqvMxWNjbdBRQ5pdCoDNgWkGI7UiSzl+IU3i1v+4TpefGUyxxXPnlsU\r\n0gzv25zErPhLCpxQlmpEf7ckpzonRaJ8gIHHw8oomSi7OXFEcFA4FdK8zptn\r\nx+ay4JdQnLmLOXsxlQ41HbB809gWBdTP52YnRblbAln8MS1ZIf12/Zj7uscd\r\n0b4hviztoXLHtq+uJ0h8iYWT1MmLAOLIOCX78TcT8y2EucEMaw73X1T3gRr0\r\nUNBfdzNY4blumTajSwtWOT6KR+E85UdiFpo9yhO2q5DIlU2Plg2COCKR+GYc\r\nw5wuOHEXM25by9ipVw6spVaYdD/QodcwEhoQ7c+kFJveoJxGr2cx3jlPLXvi\r\nJQPP73gYNbBw7TbIGzhkK0sm/wE7GNGnUslye3cnAQqGIcf7CGdF6MRn3j2D\r\nRMJ2OhoSahTtqOLSVaX7JGxX7I/zT2dQbWQ=\r\n=ayhY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-function-name": "7.21.4-esm.2", "@babel/helper-replace-supers": "7.21.4-esm.2", "@babel/helper-annotate-as-pure": "7.21.4-esm.2", "@babel/helper-compilation-targets": "7.21.4-esm.2", "@babel/helper-environment-visitor": "7.21.4-esm.2", "@babel/helper-optimise-call-expression": "7.21.4-esm.2", "@babel/helper-split-export-declaration": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.21.4-esm.2_1680619199143_0.8106267379519254", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-classes", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "1e3a497d5aabe2ce7e2247225b61ff3c2f5cc209", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-MdUOop+2/mJZP1NMg8qtTgoiLzxzcR+Qm0gsPEnEglk6Qm76j2l4BckyA8jzY0zfaaRbA6MKc2WT0rxphRFifg==", "signatures": [{"sig": "MEYCIQCrBPvUkczoto+MXuiQSi2uML4a6jfUWB9vK8jWX3lOEgIhALgLdAxR4cFkHjYJA8R+3dLFAXLEmuG9v1I2Axo6f2Vx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrC8A/9Htz6dfWrtrGbtA09DtqCXtuFMVhctk8GYnq5uu0Zh29LUYO/\r\nssGfgdmN4EtHlYinwTd6igyNBFrIKLbSQs3J9tKy8/aLNJQukxUhXKd73F2k\r\nerbJXlFFcaS02wVTfb64Svp2MFFVSsX8juinPRA+YLnf1ViF++jhjxbB7W0g\r\n9KpnV4Ajjqz+l2WdycQCl0Ur+Fo+5sdgn60JUbqbWRvNuQgjCl5pzLCdyMug\r\nbu2m1D2HSupiS0OBxP85FNAbfDEDV4kYnkSOkd4FupfZ0Ry6N5eeK9w8/Nl9\r\noKd8C3jKelGFino0fSAqfpq3lQslTLMkL0EGoJ0AkoDm//4ThbUxfRIvDScz\r\nb25DonQ9J/pICXA0HmssGDbT0bceVTmE9BXmQK5nxyBVW2As3GFVktH0RlZk\r\nbJqL+mFrRf2p9ojpK5vBEtLk2Zg+giYC6WzFVRgn/fVaGev5sOgey8jQ0aXn\r\njaY66UEQkg4rCyLjLRRAEoUmIfAkIEPqKF5k59gC4SBpRhXFn/jHZGgl3a8j\r\nsDttbq+nt90wXJ7x0TDmKvb63FoCi30N0VWgo0JEtMSfBs1GxJ6952UInsn9\r\nl/+RBJ6I6LTFcXR4fhSeEWwfQCVYe0hpl9P4EJyqzplKnxRs9LSJw01k0LSg\r\nKsoedX0DrXx8UOLSi8lS1R/ylfPxEYisrQw=\r\n=6xM0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-function-name": "7.21.4-esm.3", "@babel/helper-replace-supers": "7.21.4-esm.3", "@babel/helper-annotate-as-pure": "7.21.4-esm.3", "@babel/helper-compilation-targets": "7.21.4-esm.3", "@babel/helper-environment-visitor": "7.21.4-esm.3", "@babel/helper-optimise-call-expression": "7.21.4-esm.3", "@babel/helper-split-export-declaration": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.21.4-esm.3_1680620201655_0.056399713626804626", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-classes", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "aa444d0f178207c73cfc906c4ea380ea7ce43ec1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.4-esm.4.tgz", "fileCount": 10, "integrity": "sha512-6cWj2xsk2ATIIKWJaaPQ/1d5iPbbbQ25MtNkTOrlXF8pvYqaTrYsrtm8fthw2sNAMToF0AEwVkgkMHN7elXfmg==", "signatures": [{"sig": "MEUCIAwumRdOb93QjFmS5XxfcvBB8uEmFGnO9j6kFq4VSy2uAiEA6RdCOYQ7ugWbBRGOLrU/AFr3/45EuVG2NRi+hPlncbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSUA/8DMw+Dl2QMJsTNZ5jAv4moSTCq4QFIwMlIGbESoyTdX8pE46z\r\n1V1r/rVqLWd2fX9k8DmBniCS3jOTeWLmaQNUny656qBQHZI2u3ZPjP0TBkJp\r\nStWWLqIvBPpICOduMXtAiNEDPOknxWBYacqx8BxXwL7BG6oPO9lACr3OPOsS\r\ngofI2EYVDL4EdwKdlby5im1CKZoolv5iMsOrkc4E6xdsk9Pbw023ssmLLJmP\r\n1VIn6Q/uOuO9uRx1+xLbIWq0/qjumJJTdm9pC31bIddgLinOBUBcI8cyk5bA\r\ny9NLoJ+afVLlG3EnNpL4eWRqNfx2rIWUYeUjVvt1nZ8zVxKKjXGYV3Cmov+O\r\n0B1jVFZJ3GhdH6cAzs7I8yiy2WJrU3Db9WhgJmi9esK/bC6QVKlyd+BfP59H\r\n9BB/9DEhaBuq2kLdKZ0KY7zIPjMA1QJL4160rC2SF34x1vekoYhKYfeB8Xhx\r\nop3inpuYscWPHWT2jliyyV8PWYiiauTi+D9fUys46sD5fQgMQCU2VFPOv3Kh\r\nzaLny3CwTBvwRAY4wnn2z+tPROhpyRXpkOjA1Fn4aTrE+94TydMDy47MIn8N\r\n4EHVamon3BxogxxO5gY46UH6ec34d9Bf4p/MWGqg5ZChE1huX9lKKJtgNeOS\r\n+BR2ElAKfSukPaRnG6dh/sIV4Ix653ikTxY=\r\n=wmt0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-function-name": "7.21.4-esm.4", "@babel/helper-replace-supers": "7.21.4-esm.4", "@babel/helper-annotate-as-pure": "7.21.4-esm.4", "@babel/helper-compilation-targets": "7.21.4-esm.4", "@babel/helper-environment-visitor": "7.21.4-esm.4", "@babel/helper-optimise-call-expression": "7.21.4-esm.4", "@babel/helper-split-export-declaration": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.21.4-esm.4_1680621230128_0.7388199266082047", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-classes", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "635d4e98da741fad814984639f4c0149eb0135e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-2edQhLfibpWpsVBx2n/GKOz6JdGQvLruZQfGr9l1qes2KQaWswjBzhQF7UDUZMNaMMQeYnQzxwOMPsbYF7wqPQ==", "signatures": [{"sig": "MEUCIAC2j2UL7K3s2glNUrOhXIqmSxa7e9d+3ZXuUmnTer07AiEAtXYHvlZYBPVJunT6BErC82VtkYP37wQ95OrkA/Eki7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85231}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.22.5_1686248505580_0.22222133783857623", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/plugin-transform-classes", "version": "7.22.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "e04d7d804ed5b8501311293d1a0e6d43e94c3363", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.22.6.tgz", "fileCount": 9, "integrity": "sha512-58EgM6nuPNG6Py4Z3zSuu0xWu2VfodiMi72Jt5Kj2FECmaYk1RrTXA45z6KBFsu9tRgwQDwIiY4FXTt+YsSFAQ==", "signatures": [{"sig": "MEQCIGfV9CM4IiCdnds0lIG/piqg6VbPEjstELHtoRB/5nPjAiBNtm3x0PCjM1+ZiGOOceJJMzuQ8Ugw9A6yqlXCjIPdVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84898}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/traverse": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.22.6_1688456940441_0.8824604733697832", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "4c176a861ad9a36132a1b9d63c47c333ccb2eb7b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-2sViyIO9ybP0/qj17etFWqengWnaAyBbdKKC140miM0jAaK8WxKnXOtEIOKnN+bPy4uaqFXwfgKX5eG1GLMnSw==", "signatures": [{"sig": "MEUCICq+RMno7IdpQbT83pzVo5xdB9iw4livxUcup/lwYIRMAiEAuBploN33jauS49TBu5gcEP3a0w69MawJVnRK6WNzVV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-function-name": "^8.0.0-alpha.0", "@babel/helper-replace-supers": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0", "@babel/helper-compilation-targets": "^8.0.0-alpha.0", "@babel/helper-environment-visitor": "^8.0.0-alpha.0", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.0", "@babel/helper-split-export-declaration": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.0_1689861626576_0.6083333748112809", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "88c12b551a8da8744418be8bc689632a30ed2859", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-2dafLw+9TR4CkMUsnDz5uzmUoOfFHBUasKuWpHvUqKCW+ghrX6joN+WFSztubJudpARFJQ/6DINQdeW/Osyd5A==", "signatures": [{"sig": "MEQCIADCREEKOg99YY2KJnFVldfjomTE4ucnP6qc5uzU65EIAiAA11++Gg0ZKsKq+iOJLcD4MF3s0FDT2N+cnQMavTCm6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-function-name": "^8.0.0-alpha.1", "@babel/helper-replace-supers": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1", "@babel/helper-compilation-targets": "^8.0.0-alpha.1", "@babel/helper-environment-visitor": "^8.0.0-alpha.1", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.1", "@babel/helper-split-export-declaration": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.1_1690221178974_0.5848458147595017", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "052e7355c195edbcd10e4632a6bc80e5bc2f324c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-ShQ6aRDCgimbQriUWG7WXY8BK0CdWhh65zfUa6gnOznfDHDCKtkUlSk5ha7A8op+inrLDgyzO+wXKt0C8+Mv3Q==", "signatures": [{"sig": "MEUCIQCKuXM7fnFGWW12U8o4dUtqSXUWsRU+CrQFXh9Suj3zcwIgI2GPcOvVliTGQK68+MBCKIs3EybQRmZF5m1xmp7rgYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-function-name": "^8.0.0-alpha.2", "@babel/helper-replace-supers": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2", "@babel/helper-compilation-targets": "^8.0.0-alpha.2", "@babel/helper-environment-visitor": "^8.0.0-alpha.2", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.2", "@babel/helper-split-export-declaration": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.2_1691594121685_0.19925284846000446", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-classes", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "aaf4753aee262a232bbc95451b4bdf9599c65a0b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-VbbC3PGjBdE0wAWDdHM9G8Gm977pnYI0XpqMd6LrKISj8/DJXEsWqgRuTYaNE9Bv0JGhTZUzHDlMk18IpOuoqw==", "signatures": [{"sig": "MEYCIQCAP66s5qt+YAlxYbxyTRZtjAJEOSh1uubeO7E/QN2VbwIhAJAngd6hUPhRdLEOsL0bxLrVkZD9Ws7aS/J14rDvAqKJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84914}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.22.15_1693830318232_0.6914090875878487", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "91db9e294f856c76f38752308b57ab8c96b2da88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-4KZzgJU4uw3vzc/qda7ZOuumrA24hh7C6mfSk5r7EReUQEKGN6rK9bJ5roXoGMZ3lgm84V022Qe4LVaRs8ugJA==", "signatures": [{"sig": "MEYCIQDflCW9w8aZlazuCMwwzYhoSQB8W5oTcaUEnrDHPXcyjgIhAKCidJbt1n4c1KzHsoQz7sN1ttbE33eTw3eaZ6i6uT2W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83751}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-function-name": "^8.0.0-alpha.3", "@babel/helper-replace-supers": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3", "@babel/helper-compilation-targets": "^8.0.0-alpha.3", "@babel/helper-environment-visitor": "^8.0.0-alpha.3", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.3", "@babel/helper-split-export-declaration": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.3_1695740255382_0.1042278597892714", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "c84f45514ae2e163d79df6a8ede866c28a684d6d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-VnDGk/nzRZszHFOgllpA+175g1774ihDHSwdDP65npDmTYyEgWqUAqkGDxwhKGrbp2j1m51IVbxntSlxqsBNVw==", "signatures": [{"sig": "MEQCIE8fdnxftMajfDr/FbOtWPtzRhDN8XeiUcgeoQP/pUzUAiAxsnp8OFUE+5exY0fKEEAxMIPheQpp4oha8xDh1jQT/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83751}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-function-name": "^8.0.0-alpha.4", "@babel/helper-replace-supers": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4", "@babel/helper-compilation-targets": "^8.0.0-alpha.4", "@babel/helper-environment-visitor": "^8.0.0-alpha.4", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.4", "@babel/helper-split-export-declaration": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.4_1697076408333_0.6157992243133832", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-classes", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "73380c632c095b03e8503c24fd38f95ad41ffacb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.23.3.tgz", "fileCount": 9, "integrity": "sha512-FGEQmugvAEu2QtgtU0uTASXevfLMFfBeVCIIdcQhn/uBQsMTjBajdnAtanQlOcuihWh10PZ7+HWvc7NtBwP74w==", "signatures": [{"sig": "MEUCIQDG5mOgPyFpBZaVgnsv3OStjmBP3IvFO0RtVYpolOb8MwIgDO9toXi5N7wCbiVUa3wjHesg1K2ynIXMiqzfYpmvXMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84995}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.23.3_1699513427833_0.36978774582981533", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/plugin-transform-classes", "version": "7.23.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "e7a75f815e0c534cc4c9a39c56636c84fc0d64f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.23.5.tgz", "fileCount": 9, "integrity": "sha512-jvOTR4nicqYC9yzOHIhXG5emiFEOpappSJAl73SDSEDcybD+Puuze8Tnpb9p9qEyYup24tq891gkaygIFvWDqg==", "signatures": [{"sig": "MEYCIQDj8rtGERZYgGoWPVkokl/OKH+eqSLoY4C+9vE9oMgUPAIhALhSyYtbJgeSE4fGrgR7advyPdwLMMXhfn5POi47/C7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85122}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.5", "@babel/traverse": "^7.23.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.23.5_1701253539345_0.4048084238501528", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "db0a7cb3756951004e9128a24db0768df18c2557", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-cq+OkHO5RvIKzVgrbJxlP7jCCbVN2bBFG52sWbEaD079OqPDmsNt8/YxjPBs+jhSp7ELsncP05h/KxcnnAyWDQ==", "signatures": [{"sig": "MEUCIGjZ7+sZ2qkaOSeldQJUa64QDDqaAC2cETiiOZ13yHIOAiEA6DEYrX2N1Zk0NCbWLtsdrFZ8I1fFsog5upDyzhHs+C4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83981}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-function-name": "^8.0.0-alpha.5", "@babel/helper-replace-supers": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5", "@babel/helper-compilation-targets": "^8.0.0-alpha.5", "@babel/helper-environment-visitor": "^8.0.0-alpha.5", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.5", "@babel/helper-split-export-declaration": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.5_1702307981634_0.44113569331540203", "host": "s3://npm-registry-packages"}}, "7.23.8": {"name": "@babel/plugin-transform-classes", "version": "7.23.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.23.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "d08ae096c240347badd68cdf1b6d1624a6435d92", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.23.8.tgz", "fileCount": 9, "integrity": "sha512-yAYslGsY1bX6Knmg46RjiCiNSwJKv2IUC8qOdYKqMMr0491SXFhcHqOdRDeCRohOOIzwN/90C6mQ9qAKgrP7dg==", "signatures": [{"sig": "MEQCIBITUFbLiFgzX8b80mqGEZDUPJZ6/H7CTz/7XgbpKJkUAiBe00HQqqh/rF/qB/06/ASke3HL5NrlIYCFJoo2T6994A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85591}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.23.6", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.7", "@babel/traverse": "^7.23.7", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.23.8_1704739332082_0.8254297341767589", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "36587e4b189917e2a4cad05eb01b7c63844addfa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-sdBpWPVsLm+6ng9aDf/+LpNOAaX3FSTWTnl6B45Sjs43pXOB5bP1/gP+0Uutb6TpfpfVLr4olojJrqilQBiFjw==", "signatures": [{"sig": "MEYCIQDdwUKq1DB17b07IEucN3EEiF9ECnBQa73h/D4xPajQ4gIhAKvO3epRSa54NLyKOrzdj9Da1Yjr2obukanaOkmXk5S6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84418}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-function-name": "^8.0.0-alpha.6", "@babel/helper-replace-supers": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6", "@babel/helper-compilation-targets": "^8.0.0-alpha.6", "@babel/helper-environment-visitor": "^8.0.0-alpha.6", "@babel/helper-split-export-declaration": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.6_1706285680521_0.9697963833919963", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "3d58750a904b37e81ae58c10c682d1d0e9086b18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-WyGJyAGcXfJSbcKsAyZZy4bTJ6Pc06Ws2qgy7BLl6QoNVUUTrBVt6hLN/pyDQTztORrW/ok91jGgQYZzUOTDTg==", "signatures": [{"sig": "MEYCIQCwKjEqyK0lntmP6++gNnNwd2SvQgaYkHIKjH4YrCqxxAIhAJIoRBpyU3ipclocZZiGONX+tO2KN05+RDf8X0Bqlhvf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84418}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-function-name": "^8.0.0-alpha.7", "@babel/helper-replace-supers": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7", "@babel/helper-compilation-targets": "^8.0.0-alpha.7", "@babel/helper-environment-visitor": "^8.0.0-alpha.7", "@babel/helper-split-export-declaration": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.7_1709129142508_0.2052581083317877", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-classes", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "5bc8fc160ed96378184bc10042af47f50884dcb1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.1.tgz", "fileCount": 9, "integrity": "sha512-ZTIe3W7UejJd3/3R4p7ScyyOoafetUShSf4kCqV0O7F/RiHxVj/wRaRnQlrGwflvcehNA8M42HkAiEDYZu2F1Q==", "signatures": [{"sig": "MEUCIQDYjgM8pPZWFUUGDcClmON6vERsLOsJnfp+wHiL6jYppQIgEC7jzDmcF8GRtK6qiu1mJAPMmUZ1U9WPa33dsSK36FA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87366}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.24.1", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.23.6", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.24.1_1710841766948_0.8516534757263823", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "370f27e6dfa0ee46e8a9f75a32aa4580b0e1dbb5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-U7y7XTqHoYEPCTBF61Mm+iVsZ/g+IqcLb96OeBivrMLvHNI1xn3RjjktzeVWlakE4+sF38noefSbPORoI9D0BA==", "signatures": [{"sig": "MEUCIFOSYvNtq14a6UjQiNfxyuG2uC/dLoP7bGALXeXWS7wlAiEAk2nzX+WBNnqMlaDcM0G5WTiSR+tZ9xt2rm4M0bUNDqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86229}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-function-name": "^8.0.0-alpha.8", "@babel/helper-replace-supers": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8", "@babel/helper-compilation-targets": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8", "@babel/helper-split-export-declaration": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.8_1712236818477_0.38581702272483764", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-classes", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "05e04a09df49a46348299a0e24bfd7e901129339", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.5.tgz", "fileCount": 11, "integrity": "sha512-gWkLP25DFj2dwe9Ck8uwMOpko4YsqyfZJrOmqqcegeDYEbp7rmn4U6UQZNj08UF6MaX39XenSpKRCvpDRBtZ7Q==", "signatures": [{"sig": "MEQCIF+sai3fhpTdgDP+TPi5RC9IFiZyCkM8mhuKgC8q626ZAiBqjdo2XudzRTrxZduqBa34c4R3IvQ+v5jQn86qU59PGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160307}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.24.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.24.1", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.23.6", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.24.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/traverse": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.24.5_1714415665176_0.22387520929188653", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-classes", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "0cc198c02720d4eeb091004843477659c6b37977", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.6.tgz", "fileCount": 11, "integrity": "sha512-+fN+NO2gh8JtRmDSOB6gaCVo36ha8kfCW1nMq2Gc0DABln0VcHN4PrALDvF5/diLzIRKptC7z/d7Lp64zk92Fg==", "signatures": [{"sig": "MEUCID96MUvji6v90r73frQzOVLfiTY6fcTZOpU+FzHP8rEnAiEAgHIgHt4cNOHGm0DdFMbzJLW63Pdbq+yJjSgshgIHQTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160472}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-function-name": "^7.24.6", "@babel/helper-replace-supers": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6", "@babel/helper-compilation-targets": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6", "@babel/helper-split-export-declaration": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.24.6_1716553509257_0.3600128195155641", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "07bb09868aa7e18716fccf91368d4c2ce06fcd15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-We5dpqWBJw6i+nqlzi48qgWqFrTynN5XWheFGSjxNaOxnvxk2YYizHSb14OiNPlvBUBj2Q230I8B/CqOHYgM5w==", "signatures": [{"sig": "MEUCIF079hHwLOP5l0E0O7OdEV2GRcCCRTYZ1U+zc6bMaB1OAiEAuQokHn5uraHnC8li23CXH81w+Vlts7R/8gRBmOdgDoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-function-name": "^8.0.0-alpha.9", "@babel/helper-replace-supers": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9", "@babel/helper-compilation-targets": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9", "@babel/helper-split-export-declaration": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.9_1717423542544_0.04541522273619569", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "5027d18d120e8de87a5765bfc0ef93ca82b9a0f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-dtk2eYvdWTlBznDeE8aIP0oeXB3Ln7Ol6oxD3iySgSbDQJtJpcq1P+CEdVyZHfiBzKls+bEcnQKJvyIrKkZZDw==", "signatures": [{"sig": "MEUCIQDnAANwZukH6Fm3BKsL/nHUzwZvOOgX0poQHexWwiUdRgIgRppfF65v7D3SJHHvRTFJqqSBPjBcHBQ2mr8I7B0BONA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159662}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-function-name": "^8.0.0-alpha.10", "@babel/helper-replace-supers": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10", "@babel/helper-compilation-targets": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10", "@babel/helper-split-export-declaration": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.10_1717500042094_0.5236642942131762", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-classes", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "4ae6ef43a12492134138c1e45913f7c46c41b4bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.7.tgz", "fileCount": 11, "integrity": "sha512-CFbbBigp8ln4FU6Bpy6g7sE8B/WmCmzvivzUC6xDAdWVsjYTXijpuuGJmYkAaoWAzcItGKT3IOAbxRItZ5HTjw==", "signatures": [{"sig": "MEUCIGWCxDiCs/dKdFzLt5U6o+5PKnJG/jxDDwSp2eaOkN1bAiEAibrQ7D15EBNNfh0BfIUwq7nX60w2drJk+8oaXieuQlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160444}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-function-name": "^7.24.7", "@babel/helper-replace-supers": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-compilation-targets": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.24.7_1717593355566_0.5317693277200448", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "b16477dc1b614c80f508845a61b79ee8291344aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-puG5Z/oL3Zv6HLdP3OlOH0MXEsWkI/M9hkKw6K2UazRybPZ4PdyjaLC6KIdpg1y8rUEDyb2CgX+BOU7FeU/OeA==", "signatures": [{"sig": "MEQCIFuRAJCX7qUnHVkO+3fpMVZUVVnzmy1DXgNXDqdAFqxrAiABQ5vPyAsf4BnD8lhRrnp3FNF8aluXb3srbUFU+8+1qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159553}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^13.5.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-function-name": "^8.0.0-alpha.11", "@babel/helper-replace-supers": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11", "@babel/helper-compilation-targets": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11", "@babel/helper-split-export-declaration": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.11_1717751766226_0.39188688733163235", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/plugin-transform-classes", "version": "7.24.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "ad23301fe5bc153ca4cf7fb572a9bc8b0b711cf7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.8.tgz", "fileCount": 11, "integrity": "sha512-VXy91c47uujj758ud9wx+OMgheXm4qJfyhj1P18YvlrQkNOSrwsteHk+EFS3OMGfhMhpZa0A+81eE7G4QC+3CA==", "signatures": [{"sig": "MEUCIEbGYmspJUzem6t8gNkx4ybxXt9GQjx+Ru+A9sulLa2JAiEA3fHI6c1J0+JYkClgQ9erv+ngq4SBR4hczOUtdwObTkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151101}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-function-name": "^7.24.7", "@babel/helper-replace-supers": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-compilation-targets": "^7.24.8", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.8", "@babel/traverse": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.24.8_1720709691940_0.8111354692218491", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-transform-classes", "version": "7.25.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "63122366527d88e0ef61b612554fe3f8c793991e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.0.tgz", "fileCount": 11, "integrity": "sha512-xyi6qjr/fYU304fiRwFbekzkqVJZ6A7hOjWZd+89FVcBqPV3S9Wuozz82xdpLspckeaafntbzglaW4pqpzvtSw==", "signatures": [{"sig": "MEUCIQCGfdx2j2BVJXwsH4/OjyH4XUKfwUx9Db5sunLXylgo+wIgP9ZUpUwiAkGBAkPXUaYRpa7CggGcBT/gA+qNvHhv4Ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151851}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/traverse": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-replace-supers": "^7.25.0", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-compilation-targets": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.25.0_1722013172151_0.5773068517578048", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "662bb5304cdb32f49b1d0d33107aa9f8e2f2caee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-pILyqyN11uL6eKiH+hYyZBtHTatwRqIEH5UFpKlr2FOBLEKehCD7a5ws9emhKhW8/+JpmYriVCQ59/YoAwwdgw==", "signatures": [{"sig": "MEYCIQD0VwCVeK68rylwhjkbFa/aZPyk78cHeA1gXCWNDNbVugIhALDZed+niwPHYbUdEoG4uJWgIA7yQKMubQ3pI6pmgr7v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150273}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.6.0", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-replace-supers": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12", "@babel/helper-compilation-targets": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.12_1722015240458_0.4317029602238531", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/plugin-transform-classes", "version": "7.25.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "d29dbb6a72d79f359952ad0b66d88518d65ef89a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.4.tgz", "fileCount": 11, "integrity": "sha512-oexUfaQle2pF/b6E0dwsxQtAol9TLSO88kQvym6HHBWFliV2lGdrPieX+WgMRLSJDVzdYywk7jXbLPuO2KLTLg==", "signatures": [{"sig": "MEUCIBewcqlV9MxOzL7H7U/i8pTByp7a6T8UblfJkWk/ODVRAiEAmJR8nhh7A24D8lg1DPeKEw8+aDI5bddifuDBeGT8gk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152046}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/traverse": "^7.25.4", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-replace-supers": "^7.25.0", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-compilation-targets": "^7.25.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.25.4_1724319275471_0.4214457214306164", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-classes", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "5103206cf80d02283bbbd044509ea3b65d0906bb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.7.tgz", "fileCount": 11, "integrity": "sha512-9j9rnl+YCQY0IGoeipXvnk3niWicIB6kCsWRGLwX241qSXpbA4MKxtp/EdvFxsc4zI5vqfLxzOd0twIJ7I99zg==", "signatures": [{"sig": "MEUCIQDwmtW32OtEGaEdi+Xg1H9itI/T0RdaYQ0jKjOx8x4J4AIgb1doDviv0CnqcdtwX9PZXwrm9bl+2D9WWLiJeDBMmYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162775}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-replace-supers": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7", "@babel/helper-compilation-targets": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.25.7_1727882130752_0.4438339480015818", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-classes", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "7152457f7880b593a63ade8a861e6e26a4469f52", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-mD8APIXmseE7oZvZgGABDyM34GUmK45Um2TXiBUt7PnuAxrgoSVf123qUzPxEr/+/BHrRn5NMZCdE2m/1F8DGg==", "signatures": [{"sig": "MEUCIQCwyBHW9xcZ1WyccSZ2VDrfx1o/09AR38JwyNr55BpRjwIgYsnPWoZiRHkeI6BOTIAqP4/ZKjOwAjjV6uOzXEp/6Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91170}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-compilation-targets": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.25.9_1729610506374_0.4591057833222174", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "ec6f98877de7fa1a54467ddd1a861fb4ed88a6a5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-fA7RDd2IfkG3ltpz5j4T0Re/OKxE79VdEzl7FiHPz49ASiUmbN+4ICMqUEZishsCd58IxXhLARoN7CKFN+Dzmw==", "signatures": [{"sig": "MEQCIEnSrra6nXSuiIHBQeEDYFmADQkdhzEx1abtKENQf2EQAiBzaCXN18bVmTECqY2K/L/A7bnZMlh1WppksmrR01AxOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89772}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.9.0", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-replace-supers": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13", "@babel/helper-compilation-targets": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.13_1729864485473_0.09939869590618078", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "af027da1fb079aff0fe23e4b91842c7a8bb341fb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-u93/fTiTuiGpsPUOSANre+eVQYu9dZkggpM96QGYLH5+winPK/1BsiBYfVVPk7EExZ1Mvkfelk1/By2LRDm08g==", "signatures": [{"sig": "MEUCIAHZT6hXR0sJC+T7riU3sklh1yNK4ERe3Kjm1V3Hi6s9AiEA4ezBRLLarxnMX2gsQAB4xwJVnyMxVC3pytwmuEKBuZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89772}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.9.0", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-replace-supers": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14", "@babel/helper-compilation-targets": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.14_1733504074861_0.6001932955444211", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "29ae0c4d54dd9120efa1b445e0764f6656d3cbed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-OBUwlXFd7dOmgbkGLuKqMR6XQcXLlglbestOfspJ5LDthN7pFucXMbQ5g1PtySwJXmDt+9CJwbRUdXSJN63X0Q==", "signatures": [{"sig": "MEQCIGqqHjMW6X5zuiCD17D9xyRXovLf2H4aS3unnnWWxvx/AiBp27VnWTCwrwZolvt4TdfESYNNpDMAfDeYYV58AxldGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89772}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.9.0", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-replace-supers": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15", "@babel/helper-compilation-targets": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.15_1736529904544_0.26259461836845754", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "c3df7ce4ce743c69afb3b4d306f2e3ab4a41f019", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-MoGO7NavoB/7CN1Xzi0I1gZa8M2c//41hqYX4KVIjl8kYVE5XpeeNnHzlzVqGMbYxn6H4d4QoTaRl/Ra71gLYw==", "signatures": [{"sig": "MEYCIQDjnUjlosVemDU7NYulIUXQ4Ah7HRho+DrNq5yXRV9uUgIhAKb0KQ5KdaU4NF8ZZCDcRPHxdJECUkial34loj5/M0K/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89772}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.9.0", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-replace-supers": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16", "@babel/helper-compilation-targets": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.16_1739534379088_0.6165231978183965", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "551681fd36349d0aca24750a423bf35c30c5e8f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-8ahELlzdXLZzpHmYncjLuIAS1COX1rsu1lzoM10WWoeihGySEH451NZ6/ISi6xClwWSEm+6fjMlYh3A6UPdyuw==", "signatures": [{"sig": "MEYCIQDEy+ztMd6i/MNnRxqAufptor7HQJ9+G5qjo16R4aL6PAIhAMq432/fa7Egsqalo3PT122xBJ2Al0buOKCQHPtmw2rn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89772}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.9.0", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-replace-supers": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17", "@babel/helper-compilation-targets": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-alpha.17_1741717532824_0.07022697878582806", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-classes", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "03bb04bea2c7b2f711f0db7304a8da46a85cced4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz", "fileCount": 9, "integrity": "sha512-7iLhfFAubmpeJe/Wo2TVuDrykh/zlWXLzPNdL0Jqn/Xu8R3QQ8h9ff8FQoISZOsw74/HFqFI7NX63HN7QFIHKA==", "signatures": [{"sig": "MEUCIQCpHZRh4qgTl9pQN1OOJfTlUxK5cYlmDzoAX/eBY27WcgIgB2ENh2VEc9UdN63u/xdDkCVIfRRYde8DJ8lt3gWgs54=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 91170}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.27.1_1746025767427_0.3603172348749415", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "afafaa73224ae7493419651c8155538b034af656", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-vgwC/kbagjdM1lLeAxk831FovkC8gKIdrE5XmnyjgF6SbyamYPadfTcbXF353mXEhu8Rt4hRunv4zQAdWguNNA==", "signatures": [{"sig": "MEUCIQDOHy03ipUPw2XUqu9LBMBieInoF0Y80xpKwFmowC05ZwIgB4lpPp550gna7jJWq2uxd3ncz1CMPXezhSacFb8f0Ws=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89740}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^15.9.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-replace-supers": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0", "@babel/helper-compilation-targets": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_8.0.0-beta.0_1748620304855_0.7087510615151675", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.7": {"name": "@babel/plugin-transform-classes", "version": "7.27.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.27.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "038af7e7c346821cc74aff1036c1f762308cd2d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.7.tgz", "fileCount": 9, "integrity": "sha512-CuLkokN1PEZ0Fsjtq+001aog/C2drDK9nTfK/NRK0n6rBin6cBrvM+zfQjDE+UllhR6/J4a6w8Xq9i4yi3mQrw==", "signatures": [{"sig": "MEUCICKHE2RFOj8+bJaSYzrZP+A10ngYRpAq0JYmXQHfz21qAiEAnMDjTPiCYSnfHTvwCx5JxtS4FqnWvTqm7RtL3O9iZfU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90826}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"globals": "^11.1.0", "@babel/traverse": "^7.27.7", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.27.7_1750946603714_0.9881307525721332", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-transform-classes", "version": "7.28.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-classes@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "dist": {"shasum": "12fa46cffc32a6e084011b650539e880add8a0f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.28.0.tgz", "fileCount": 9, "integrity": "sha512-IjM1IoJNw72AZFlj33Cu8X0q2XK/6AaVC3jQu+cgQ5lThWD5ajnuUAml80dqRmOhmPkTH8uAwnpMu9Rvj0LTRA==", "signatures": [{"sig": "MEUCIHPobQnn4CqaJ+V6kAvNaRFohKYctn07fC3/D7gnRNbnAiEArWtwJrzMbJA0WbMs/HBf/T6aM3lpGXMAkiPrrOYmehU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 91277}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "directories": {}, "dependencies": {"@babel/traverse": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-classes_7.28.0_1751445507824_0.7308508065474055", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-classes", "version": "8.0.0-beta.1", "description": "Compile ES2015 classes to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-classes"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-compilation-targets": "^8.0.0-beta.1", "@babel/helper-globals": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-replace-supers": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-classes@8.0.0-beta.1", "dist": {"shasum": "7d3a8863c9e3d327d3ac432e976c0eb606384ceb", "integrity": "sha512-qbXXaMeuoHsYXKSfo28e4KGYHUs0qeib4hvxinuJp/6bt8rcpfyb5Tx/E7F2UQbSnA/YmiDYVjRJyvvYddzL5g==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 90116, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDWmYmubSp/OTgk/inG6CNKrkQthWRZCziN/g+vgq4aKQIgSTsIeZWjbkdyXHoUU4eWXxDsJyyXrlXuNuLf3A5WuzM="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-classes_8.0.0-beta.1_1751447087285_0.5238769908267631"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:32.255Z", "modified": "2025-07-02T09:04:47.738Z", "7.0.0-beta.4": "2017-10-30T18:36:32.255Z", "7.0.0-beta.5": "2017-10-30T20:58:10.115Z", "7.0.0-beta.31": "2017-11-03T20:04:35.984Z", "7.0.0-beta.32": "2017-11-12T13:34:08.721Z", "7.0.0-beta.33": "2017-12-01T14:29:25.975Z", "7.0.0-beta.34": "2017-12-02T14:40:26.637Z", "7.0.0-beta.35": "2017-12-14T21:48:32.718Z", "7.0.0-beta.36": "2017-12-25T19:05:48.207Z", "7.0.0-beta.37": "2018-01-08T16:03:48.256Z", "7.0.0-beta.38": "2018-01-17T16:32:47.150Z", "7.0.0-beta.39": "2018-01-30T20:28:49.445Z", "7.0.0-beta.40": "2018-02-12T16:42:54.461Z", "7.0.0-beta.41": "2018-03-14T16:27:06.069Z", "7.0.0-beta.42": "2018-03-15T20:52:14.637Z", "7.0.0-beta.43": "2018-04-02T16:49:03.557Z", "7.0.0-beta.44": "2018-04-02T22:20:43.670Z", "7.0.0-beta.45": "2018-04-23T01:58:55.147Z", "7.0.0-beta.46": "2018-04-23T04:33:09.518Z", "7.0.0-beta.47": "2018-05-15T00:18:12.822Z", "7.0.0-beta.48": "2018-05-24T19:25:06.290Z", "7.0.0-beta.49": "2018-05-25T16:05:02.123Z", "7.0.0-beta.50": "2018-06-12T19:48:21.842Z", "7.0.0-beta.51": "2018-06-12T21:21:00.244Z", "7.0.0-beta.52": "2018-07-06T00:59:51.318Z", "7.0.0-beta.53": "2018-07-11T13:40:54.384Z", "7.0.0-beta.54": "2018-07-16T18:00:32.351Z", "7.0.0-beta.55": "2018-07-28T22:08:37.092Z", "7.0.0-beta.56": "2018-08-04T01:09:11.066Z", "7.0.0-rc.0": "2018-08-09T16:00:29.452Z", "7.0.0-rc.1": "2018-08-09T20:10:12.620Z", "7.0.0-rc.2": "2018-08-21T19:26:16.433Z", "7.0.0-rc.3": "2018-08-24T18:10:01.314Z", "7.0.0-rc.4": "2018-08-27T16:46:49.609Z", "7.0.0": "2018-08-27T21:45:15.139Z", "7.1.0": "2018-09-17T19:31:27.224Z", "7.2.0": "2018-12-03T19:01:12.767Z", "7.2.2": "2018-12-15T10:05:15.812Z", "7.3.3": "2019-02-15T21:14:26.995Z", "7.3.4": "2019-02-25T18:35:57.076Z", "7.4.0": "2019-03-19T20:45:22.723Z", "7.4.3": "2019-04-02T19:55:47.789Z", "7.4.4": "2019-04-26T21:04:57.290Z", "7.5.5": "2019-07-17T21:21:53.045Z", "7.7.0": "2019-11-05T10:54:00.111Z", "7.7.4": "2019-11-22T23:33:52.839Z", "7.8.0": "2020-01-12T00:17:32.892Z", "7.8.3": "2020-01-13T21:42:31.492Z", "7.8.6": "2020-02-27T12:21:45.896Z", "7.9.0": "2020-03-20T15:39:08.008Z", "7.9.2": "2020-03-21T14:14:16.783Z", "7.9.5": "2020-04-07T19:25:34.393Z", "7.10.1": "2020-05-27T22:08:22.326Z", "7.10.3": "2020-06-19T20:54:49.914Z", "7.10.4": "2020-06-30T13:13:23.289Z", "7.12.1": "2020-10-15T22:41:54.119Z", "7.12.13": "2021-02-03T01:12:00.610Z", "7.13.0": "2021-02-22T22:50:18.677Z", "7.14.2": "2021-05-12T17:09:36.558Z", "7.14.4": "2021-05-28T17:00:06.161Z", "7.14.5": "2021-06-09T23:13:12.605Z", "7.14.9": "2021-08-01T07:53:15.052Z", "7.15.4": "2021-09-02T21:39:52.119Z", "7.16.0": "2021-10-29T23:47:56.292Z", "7.16.5": "2021-12-13T22:28:52.440Z", "7.16.7": "2021-12-31T00:23:04.779Z", "7.17.12": "2022-05-16T19:32:50.437Z", "7.18.4": "2022-05-29T21:50:11.661Z", "7.18.6": "2022-06-27T19:50:40.503Z", "7.18.8": "2022-07-08T09:32:34.055Z", "7.18.9": "2022-07-18T09:17:42.084Z", "7.19.0": "2022-09-05T19:02:20.127Z", "7.20.2": "2022-11-04T18:51:07.784Z", "7.20.7": "2022-12-22T09:45:37.416Z", "7.21.0": "2023-02-20T15:31:14.045Z", "7.21.4-esm": "2023-04-04T14:09:57.129Z", "7.21.4-esm.1": "2023-04-04T14:21:54.965Z", "7.21.4-esm.2": "2023-04-04T14:39:59.326Z", "7.21.4-esm.3": "2023-04-04T14:56:41.817Z", "7.21.4-esm.4": "2023-04-04T15:13:50.347Z", "7.22.5": "2023-06-08T18:21:45.764Z", "7.22.6": "2023-07-04T07:49:00.615Z", "8.0.0-alpha.0": "2023-07-20T14:00:26.776Z", "8.0.0-alpha.1": "2023-07-24T17:52:59.181Z", "8.0.0-alpha.2": "2023-08-09T15:15:21.928Z", "7.22.15": "2023-09-04T12:25:18.449Z", "8.0.0-alpha.3": "2023-09-26T14:57:35.548Z", "8.0.0-alpha.4": "2023-10-12T02:06:48.627Z", "7.23.3": "2023-11-09T07:03:48.067Z", "7.23.5": "2023-11-29T10:25:39.476Z", "8.0.0-alpha.5": "2023-12-11T15:19:41.858Z", "7.23.8": "2024-01-08T18:42:12.251Z", "8.0.0-alpha.6": "2024-01-26T16:14:40.674Z", "8.0.0-alpha.7": "2024-02-28T14:05:42.693Z", "7.24.1": "2024-03-19T09:49:27.122Z", "8.0.0-alpha.8": "2024-04-04T13:20:18.658Z", "7.24.5": "2024-04-29T18:34:25.352Z", "7.24.6": "2024-05-24T12:25:09.500Z", "8.0.0-alpha.9": "2024-06-03T14:05:42.739Z", "8.0.0-alpha.10": "2024-06-04T11:20:42.306Z", "7.24.7": "2024-06-05T13:15:55.804Z", "8.0.0-alpha.11": "2024-06-07T09:16:06.412Z", "7.24.8": "2024-07-11T14:54:52.110Z", "7.25.0": "2024-07-26T16:59:32.395Z", "8.0.0-alpha.12": "2024-07-26T17:34:00.680Z", "7.25.4": "2024-08-22T09:34:35.694Z", "7.25.7": "2024-10-02T15:15:31.045Z", "7.25.9": "2024-10-22T15:21:46.714Z", "8.0.0-alpha.13": "2024-10-25T13:54:45.699Z", "8.0.0-alpha.14": "2024-12-06T16:54:35.038Z", "8.0.0-alpha.15": "2025-01-10T17:25:04.713Z", "8.0.0-alpha.16": "2025-02-14T11:59:39.269Z", "8.0.0-alpha.17": "2025-03-11T18:25:33.009Z", "7.27.1": "2025-04-30T15:09:27.604Z", "8.0.0-beta.0": "2025-05-30T15:51:45.042Z", "7.27.7": "2025-06-26T14:03:23.886Z", "7.28.0": "2025-07-02T08:38:27.970Z", "8.0.0-beta.1": "2025-07-02T09:04:47.488Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-classes"}, "description": "Compile ES2015 classes to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}