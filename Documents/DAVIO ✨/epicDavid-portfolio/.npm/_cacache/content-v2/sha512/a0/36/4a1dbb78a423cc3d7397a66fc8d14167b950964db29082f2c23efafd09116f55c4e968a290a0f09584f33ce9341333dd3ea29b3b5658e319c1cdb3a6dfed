{"_id": "@babel/helper-replace-supers", "_rev": "144-3fe0a92d3291ec733f5a93849cb8f6b3", "name": "@babel/helper-replace-supers", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e8d798e099ce7c1ebdb303db0829e506fdef3437", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.4.tgz", "integrity": "sha512-ZlFknrkU0OlyAsBvpPRxcWkyPJlRIyoOCdz0xES19n20A9fOUGAdyUr3uHT4bs8XokL636T/HWh4ESQjFdhkVg==", "signatures": [{"sig": "MEYCIQCakS679h0Yw50WNT2VlX2CxXA1fgWgmxRLMiEh7nU38QIhAM6pd7QUTtsTlnf0oC4rOG7ibXpwGNIIcxvqJWKj26kh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4", "@babel/helper-optimise-call-expression": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.4.tgz_1509388569536_0.16520296433009207", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.5", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cf433c7dd5a8a9f5d022123e08141b18c71a77ee", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.5.tgz", "integrity": "sha512-9G8Gr+08OitRfoK92u7FIWfJziE0t0O6W2KAA80smFG195sYSMKkv3TFumldAsaSS73eptwCBDnV10MiTDft7Q==", "signatures": [{"sig": "MEYCIQCJnWNL7Zv3q3ptVG3GIZazaDhJvdshxf6aEA75A/4gagIhANHoEOxW4TIcdzyIypEL865KRzvxt8dbdUrziBCQxn14", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5", "@babel/helper-optimise-call-expression": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.5.tgz_1509397065583_0.3916688575409353", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.31", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "47094bb5ad02fe855c740bee1979285975527c24", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.31.tgz", "integrity": "sha512-QVGDrDDOG7WHVr0Rhkbh5o/ZzfpleHXHipwLOgcHbevF0fiBIjIrkGqZOXKRNWRlEJOa0U9PapbqQXqgeTyaMw==", "signatures": [{"sig": "MEYCIQCsqU0iU3l2PFRcZxCYDquEftYOa71qlIha+ja7UtZ/RgIhAMOiVnJxeC78KBUQzRl/zRr88QxfHOQNMyJy23URNDXp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31", "@babel/helper-optimise-call-expression": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.31.tgz_1509739464784_0.3805858572013676", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.32", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "988e341dbaec263a32297fc97ab8c2eb19eb2edc", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.32.tgz", "integrity": "sha512-zyCvhY2WtD5zAIJs599C+ivbbtpBs6pGeVN0SEf3K1X5VvqjfUvOkc095pGEgdDJnaCNq3zWnOhOlDXI2x59Iw==", "signatures": [{"sig": "MEUCIFBFNOtugA5mKWe7zcPN0GJIFGjJOKKqJQfKNtWCRBF/AiEAlbY47X/NwhZ0cWzjPPsTmx+AGrkVYFXERpQrAIRzhDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.32", "@babel/template": "7.0.0-beta.32", "@babel/traverse": "7.0.0-beta.32", "@babel/helper-optimise-call-expression": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.32.tgz_1510493638764_0.9555854944046587", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.33", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5aafad1045e0649d4f91771d43a4892d8a1289af", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.33.tgz", "integrity": "sha512-VenQVQw7Ew16afzf/DVuSGxhICMSiTXcku6ESi+aNde5r3pZOwr+AxebjDeTUQPzZj+R4b+Wrnfpmt/RSjXNvg==", "signatures": [{"sig": "MEQCID77iorTzsKwX7uo49wDfSKFzNiLPZOyqGMnDwnvebOBAiAfmiypjZPRfn0reJMAWY2mmXQM4MCO/NEDOqqhgMujsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.33", "@babel/template": "7.0.0-beta.33", "@babel/traverse": "7.0.0-beta.33", "@babel/helper-optimise-call-expression": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.33.tgz_1512138555836_0.8962003274355084", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.34", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "888ae50bfd5b1d5f326066b555aa269855c776f2", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.34.tgz", "integrity": "sha512-eCp5EyVYppTdY3OK9RK0p7GZH1x7PuXQzazS7sM3y2qwdrqOEEspMChV9aDub4yMFewhgqMCUkF+SClLjq8yJQ==", "signatures": [{"sig": "MEYCIQCedhsHDa8kAp2x9Sg2gqf5Hxz104P8/56c8zVgGiRJ3wIhAKX5jOvd+yDD9SPYGTnLBIaHzEXv5LRXZROB0QIjH4Bc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.34", "@babel/template": "7.0.0-beta.34", "@babel/traverse": "7.0.0-beta.34", "@babel/helper-optimise-call-expression": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.34.tgz_1512225613839_0.018846330465748906", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.35", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fb59070aba45002d09898c8dfb8f8ac05bde4d19", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.35.tgz", "integrity": "sha512-ez6sOMdXeFzGlg2Qbyi//2nbBrftC7RzMpN671Hd87ITP2af3feEWYEKC5O0EXLCcgaNBzNntkScRGV9ez03wg==", "signatures": [{"sig": "MEYCIQDLYVJHUD+if97xLMT3cAKh8J17jH+HhUAq3t+POIL1zwIhAPJdVhMkOkjv5OG+Pm5VhKY++8qR4jPzC8BveXDbqwiB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.35", "@babel/template": "7.0.0-beta.35", "@babel/traverse": "7.0.0-beta.35", "@babel/helper-optimise-call-expression": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.35.tgz_1513288105177_0.8528643315657973", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.36", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9335d0b409de8df79686484c8c14101adf4b4c01", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.36.tgz", "integrity": "sha512-Dj5BG7qgRleK/97JNCarkif3avBf0ddSFIzqdYOTCquPVrDSWUMknZ5xsFVFpWMO1SXUhtZdBQDRHUcE7DsVdw==", "signatures": [{"sig": "MEQCIF25i3NGTc1eQhhZvD6Q2YfRKg2ETc8UdJpPicm5L3GKAiBxBvvgwsIqgMag15bc6JAg6m5J89RpHEA6lfvHBW+0Aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.36", "@babel/template": "7.0.0-beta.36", "@babel/traverse": "7.0.0-beta.36", "@babel/helper-optimise-call-expression": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.36.tgz_1514228736548_0.7442284394055605", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.37", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d77c3195d502d7a7e2145b227d3a07eec91d19d0", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.37.tgz", "integrity": "sha512-dah3xesAo3+5R2a5bGbPOa9FJf6rrlNI1s9U+mVyU98KTBE5HSAWdBm5dbP1tzO61tjQuvPFjZoOOAAL8LwovA==", "signatures": [{"sig": "MEUCIQDAjR7ciXg373VGSxD0sdGr/959eqh0gi6/UE1tgaiPHAIgFwoydrM1BYYlm7k0zfb9f05ShmfL5ck7JYQtY93N4CM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.37", "@babel/template": "7.0.0-beta.37", "@babel/traverse": "7.0.0-beta.37", "@babel/helper-optimise-call-expression": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.37.tgz_1515427419741_0.40242961049079895", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.38", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "386705195271678ca7bbca9a0783173fecf14410", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.38.tgz", "integrity": "sha512-0DcCEsI03e2lkF/teZaTDHOIkYq7ATwXmGc51LYQkzgRbmEtwk5e0GxxD+IQPy1ZW4GATsIJ0i1yt4cPaSFbpA==", "signatures": [{"sig": "MEYCIQDmQB0/5GMIak+GP6CHllf1D3Y4Q9AQo1T4xQpPAuOA+QIhAKr+ksL2WNcuCjP294PXqYjyMP4wbbBS4Fj1tbWXaMAO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.38", "@babel/template": "7.0.0-beta.38", "@babel/traverse": "7.0.0-beta.38", "@babel/helper-optimise-call-expression": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.38.tgz_1516206759428_0.7122592476662248", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.39", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6ddb6167ad18904fd43d9f0df909b90fe09f0569", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.39.tgz", "integrity": "sha512-YSnyT3vl7gNW2PmQ4+wA7ggDZGVYIXKfvhlXHSylcbsURul20jZayG6N7sHNTcvNyu9ATVCzkhXITw5FZ7i7tg==", "signatures": [{"sig": "MEQCIEhUmHnqbu6m5f0QDgskQML6cW7NWYMZKI/yAY2Mw3nuAiArHVNkrLDIFGbt624IJ6BLIojadHHQ0FAcpKBMaphHMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.39", "@babel/template": "7.0.0-beta.39", "@babel/traverse": "7.0.0-beta.39", "@babel/helper-optimise-call-expression": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers-7.0.0-beta.39.tgz_1517344123491_0.2835892280563712", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.40", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2ab0c9e7fa17d313745f1634ce6b7bccaa5dd5fe", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-Nu/5wpUV3rG35RzOq/upZlm61cP0lSAtmNkJLFfO5k2zOGCiHRczD1Y/xKqYOMl5f2iZmYw9fANi1jE4odMIIQ==", "signatures": [{"sig": "MEUCIHISDmRcZjRohlt5TWkQ+2wxWRnIvgScMBtz4fqOLF3wAiEA39f2c4lulDoMntBJEbs8oBmocFzY/95EgjkUMRKGYgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8269}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.40", "@babel/template": "7.0.0-beta.40", "@babel/traverse": "7.0.0-beta.40", "@babel/helper-optimise-call-expression": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.40_1518453762319_0.16829150729503262", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.41", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3c0c4a2e1f0cdd934ab0ef31bf41376f32f4ef74", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-qgucFzqLLyS8ncQJlWzC7oCfiLn23Zb0wCoQDWpOaBQUO67rO2Z96R1Zm8PZKoHHlAPPuSmcyV7IMU0L9eLTiw==", "signatures": [{"sig": "MEYCIQC6dc2N40v3TqonEy+xnW2kXs64ymnDtNzs2czudWUEAwIhANOb3knuaCdoLvPqu/tUs2UUuNEi75XksYaA3m4PjEzQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8269}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.41", "@babel/template": "7.0.0-beta.41", "@babel/traverse": "7.0.0-beta.41", "@babel/helper-optimise-call-expression": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.41_1521044810339_0.03916211509686485", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.42", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fd984b6022982b71a1237d82d932ab69ff988aa4", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-5OwdkTm7TaEBiBMOUV97j8a2goD3+avek9EOl/UdE/CYdtdQ/8RPdUPqtqXApay30aZ/EjIpBItcNlBtt29WBw==", "signatures": [{"sig": "MEYCIQD6WwWDRP7R0NRGAMwfneJmLJywvgihqeiLhaGF0IZvIgIhAKP9H45mxWvOQ94wUH4RcjgwNJ0ynrpzhKFsN7bWI+3a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8269}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.42", "@babel/template": "7.0.0-beta.42", "@babel/traverse": "7.0.0-beta.42", "@babel/helper-optimise-call-expression": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.42_1521147125593_0.2310444586472351", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.43", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f2ddd601c9e478398aa690b2881058c417ef0be3", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-Gnmn3xmPQL1UisxywpNLMHSMkdVyuXOCyZ9AwGLSDGgrQKSCBiANYUg9UFDfzus4Z7vamfik80jAUhRQoytzFQ==", "signatures": [{"sig": "MEUCIQCBfMigYn/styPEJ2QWv/Rp/ItgAfTtjpclqdPC1SQHCAIgdq9ZTvJzc/GIBOgt5PntJYbB1ywnYU0/qCp1/QnETGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8078}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.43", "@babel/template": "7.0.0-beta.43", "@babel/traverse": "7.0.0-beta.43", "@babel/helper-optimise-call-expression": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.43_1522687735471_0.3777270276305609", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.44", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cf18697951431f533f9d8c201390b158d4a3ee04", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-8PTdsyYE+Fyn8Qy0Da7YnmXhm+vOI73YFHcrAW2s81g9Ae5F3ZA+RvQQe7RPP2mi+Jg/GqXGPUmHYqDpQ3pT9Q==", "signatures": [{"sig": "MEUCIAlGAgYoUq4ho5DD7kyV84j0NVw1+5CE5I4j9BPP4BJBAiEAoqnt0yf9bvQeaEgLj9PfhQNejQln90Zr51Hi8l0PQUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8635}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.44", "@babel/template": "7.0.0-beta.44", "@babel/traverse": "7.0.0-beta.44", "@babel/helper-optimise-call-expression": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.44_1522707636404_0.5040893167494997", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.45", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2fb84dba66f6da02571153b8873a83263a94c30f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-HjcNaqA04cN3WNgJTHqLn7eYGb0LHGPcT5/iFj/AhWRQswa6OeaqUD5BB88z9u7YCL9jMRYf4m/uXGOFCkVOaQ==", "signatures": [{"sig": "MEQCIGDyjvCBYRJM3EwAwPibzRi3c4JEpOlNtGlaKx9HI74OAiBbaGBlk4HxC0PvPwBXCHgxfvdsyDMHomS6Xbn7zOoj4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3BCRA9TVsSAnZWagAAZmAQAKN2/JyB/q0+hVWf8aQ2\nFRrJqSjRF8wb6Kvniq0ZTFS9smSYSfb7yFEP2M7CkFRAygWHtwTEYh/puOdB\nhpLDTgBcfGtuwOPtZ80Ht0zpSysC0VJ6GFO/9RX74IwqWHA07G7eYfIzguSH\ncVM4vnhyhgsPIUlLxs9DoKIXAj8qEpjoHg6ajAP+Te50ovIasMIigX8zvVB0\nJ7CAE2xOwfBS5QZUrPQggTk8RMpsP3cq8nyF9YIp4dsxpBjP3m0xKpvIMgJk\n+8QtH7aPoMIlXxpFLqBRzMUh7/Akx21ESoZ3BAoXEC997UlllPFMUT3c/++5\nKEU6lQt9Wjx8S/GvYD35ZK6ODDB+OsplTnHbKxSn2iMf6zKQY29LNMgvB1E1\nWKfJ/8xp4dHr3D8OOUe855Z1MKW0+xDSE/cql25L3Bp2/7Vb/DYjTpKKc6+6\nHxrLvARoTn0pptw8XRQo4F3YTUlKa3q2rApnqp4d2es9RmIzZD/sUFZ9U2v9\nxo2nbYLhtsjL3CJ77MLO2oyvZoyj86r3T835rhKF0UuHgHcOzwklMjGe1aM8\ne2i110DzSxiLbKPeVlA5tzfsZFotRj49ytJ3YsEJubTwRZT+dd1kr1NggOrk\nRf2rKnAEtCieuqvjBarpNzOD8hCKHL3AGgXtVyTQQ/WnXHxUblrl9iyU2djN\nE69X\r\n=1knT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.45", "@babel/traverse": "7.0.0-beta.45", "@babel/helper-optimise-call-expression": "7.0.0-beta.45", "@babel/helper-member-expression-to-functions": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.45_1524448705178_0.2744674921881649", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.46", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "921c0f25d875026a8fb12feda1b72323595ea156", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-FSpK3QKzb58oMEccanHzg1djsYHhGARl08i8BQGBoOyHS6Df+4/8bsQiTnc59Dz5sJoZdb67nKKFjgMsMYi6Kg==", "signatures": [{"sig": "MEUCIQD/gMFR5VA/y8eLZqPPznf0PcR9cg1UA3boA3aB6/09eQIgdDa/WkuMj7btDOz+6bqYgQeOa0pJq3QLWAkUUPNcfPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHpCRA9TVsSAnZWagAAATYP/RbGLHrffbSRnbk9U8mi\nLFBtoWO6OVcDy90VEojgHJrkWcjqU4thmvSoxiVZ6qwOa+Yz/isgxTlsNpEd\n4RiIvn9H+PrK5+HsqsjQSZCUYtSUcWT67PFN29ktG2/9TfrJMY1hRbvWAekI\nF7F6acFoYLCbQ+ch2wmgllTj0dwDdkc1CmRIFDY87S62tUFSQsQNTIWzXYuO\n2Iic2rfZsiKxaq0Wo+kXs82uA5GyLbtMqcKZLachOvDMVRKs9UqOs0JywH00\nevDIPb8tU/BvycZjtfWzhfZn8+GNedH1ef8/9mfjJxf617TqkkgQrsNdsh/F\nAV1moQcLpCxIgMlK73zROJ4znMSuhg79dCXfqXfm9CK70J3p9jw42+f62Vvd\nmHgvKksjEDTYY/rI/ZTYSv3ECml/ZrfvSOtVhKqtAq0nZKucSUo9kBH7A+4q\nnFRrbepD+wfM7PtDXHboOFQV4fng5gd79DwnOGD2GspsXLi5ZqMtmGAd9ZGr\nK5wyUURsjCdoJ+FFK1O5LJ4hX+pGqgx6WQbL2XouVGuPDNr8VMqK+0EIckLA\nmWdYJ6Pxj8fpSU5srtvXcNoNtKLhkymetwA5amqd0tYydoedGxC4F7fUxT75\nne/HK2QLAoqpj4Kn0sAA2cKkGsYYRZFHhlfvCOTdgk7GR7w4L1mmlBTm+v/s\nIwf1\r\n=0Kmb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/types": "7.0.0-beta.46", "@babel/traverse": "7.0.0-beta.46", "@babel/helper-optimise-call-expression": "7.0.0-beta.46", "@babel/helper-member-expression-to-functions": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.46_1524457961192_0.8634802615053276", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.47", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "310b206a302868a792b659455ceba27db686cbb7", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-yf2JAD1+xNTjavqazqknRgPfd6MbGfvfIcAkxWsPURynAwOMSs4zThED8ImT2d5a97rGPysRJcq1jNh2L0WYxg==", "signatures": [{"sig": "MEUCIEU7aJb6Efq/QJJxk7L5eeMoX6n8fIey2rIQ5Rx6Bjg6AiEAsw8dotzX8HjLyRKPXKj4paFcpLPs52VjGFZ14ZT8SMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icqCRA9TVsSAnZWagAAVmUQAIhJxdOmSgXAG9KpsFyU\n/te/WhokvCTh4EcLHAoG1NBKC3ZOv2Im0QlUByPV4EcpbNEgjli04xXJYfpH\nlkqtNrlQyG8V7B0v58p4JIqkKqyht6edG9Azl07WFEhfdLt/IvmsprG2Tyqz\nD19puFeFaMzzzmA1nYMkvYZnY7RQ3uquyZurzGYobBUwBqKK8F48t0uv0qIK\nWsXCRAalL9PFzGV5tH0az/m4jEUz7KbJX46nZy1GOTzOWl3aJhPo7td5UtYU\nx74LAj1urOa/nPScmcubWBH2r2o4GiEJ/HpvPHiGPefC8XBhV9VbPOS7VaRl\nnEHEvlAUxeVIEo6L3x3LXyIbDSnhkqS4nGaVbRUbY0KFwLgP9t5SAea31vDF\njsPZBUBNPstmZC5H119Ux7IXUZpdwX0Ev4cfkHldKCorCqrKOlrybJrKGXZ3\nAcek74kV7qR+FwC876+iMUPac3eUZvhbIjYhQtZiBTpH24KQGgAg8PBi5N8u\n9ff31W561+3fXPVExwHnz9JzSh+tBsrAxvWEw3IXBCypwHuVPD2HHzzz97So\nsKsMHddP55VrrgGlE9ML6Un2Y0K20KTZ9lykbe+Y0cskjrTGbalmVDlXcwBl\nOEx8xfT53nt2Lz8Im5c/xh/bxMUnEKIupmZqKGGJq83ucRZj+anvdvdUm0bi\ngyGv\r\n=cwXC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.47", "@babel/traverse": "7.0.0-beta.47", "@babel/helper-optimise-call-expression": "7.0.0-beta.47", "@babel/helper-member-expression-to-functions": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.47_1526343465808_0.4106615217292304", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.48", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e84909342553c27d744f738dd3abd2a50316aafd", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-QKksfM+sHfE/qk7DN1U+n8nqCO5IcLgBciJxT2ZGtq9aogHMK+rEFxIq57AU0T7hn9QE6iOjGSmnmT1JLWkr4g==", "signatures": [{"sig": "MEQCIHy55jrRNxJlamvpMdYUR/jpsuGIdMz9psMCM30or48fAiBE28RnyIU1XpUk6UUJcaIhetRsYGFL1pN4XMXjuJGlhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxF3CRA9TVsSAnZWagAABuYP+gL1CMccu4GZUkBy1pCf\nxvdQ6oA8hdOVYGS3VB4+3CdBKvhbOj69fIxp4+QAZvwj3ubMjvSSv+s8ZilE\nXsJVCEUDoNS5xYt5IGMly8f9hf2tN8FOGfhgvCSjx9qeUJvfeg6cCr0tb3fr\nBf4sHx0XSkgp82yt1B4WpUjBoHEeVwHDeq0NpH9yNj9gg+NVMJzpcwzjbj8+\nNSYnaHHTNQ+wzHLteZ4ZepxG/frhadxMp2EqrI6XfzrLQJae7SP6Xqlu8rmP\n5rt72C1SVqZeAsjIGVj96oXVLUyAfJxnYgduDOzJRgPAepx4b1mPn4XAI5iB\nbHy6khMgvroUL4CzHLVXZ87IYo7guRnIiL0N50i6vMIZALF9LreUrGNETVgx\n/KN561VvYHNuMdtkqPhZnFT/mLkjxipDxnZkC02MEm1GT5XQvvtmk+WZDLBI\n6J11no95/vimA+KhtexpNkswAnW3pdp74lT980VcsOZ0F2pAKF94xeOls2V8\njQTiy+RWQ1br5BcqeKQPbiamIeDR2QyUHUiOucmq8hsyWv9NSVcd0Q+feicQ\nNoOv3wnk+8LrAQHvY1j6DKpG+QoN3AEJBDyV6Y/gAAXkjMqOxxoIHgD0O1ha\nl+Awf0TjdqCu0it0DFwJNZib0m8r/sw4xYYz2thgA6bkfMdQrIb8PQ4xIQFC\nX6qK\r\n=PwDH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.48", "@babel/traverse": "7.0.0-beta.48", "@babel/helper-optimise-call-expression": "7.0.0-beta.48", "@babel/helper-member-expression-to-functions": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.48_1527189879221_0.09618985608816111", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.49", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e7444c718057f6a0a3645caf8e78fb546ffb0d9f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-rZoZWdGdKqD73D2FxGtfgkqMYJUCuHi3izxj1QiD0rkTxXIoIB0mjmyJba4q5bNb3gLvRCqNiX4gYtP3FwEEOw==", "signatures": [{"sig": "MEUCIQDAcKGo+Ic68/M3p/uv9yNf8EOjPsF/yzLFXj2yBa0Z7QIgf9y8EyB3ypCDI5wFBZ1bqtvZw9V0JypFcmeonjqlRl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQECRA9TVsSAnZWagAATbwQAKO4yGMost4VSoG0Qd+F\n2MfOC9jUSjVVJMqrLl0gh4ZVuiUyWbDpMD1uGPP1JqWWoDZHQrjJc4ROnque\ndD1NlTriJanobk+0dgITUN1nfQDJYmoqjxD7tCDC20SPWf7aN/Ui3P2kQ0gA\nKDBRGIjdOSECkTBWKrXcU6Vptnw8lz75wW/ofv4J2QRHn8NGGw7eXZvf70iB\nQK8VNVWXCvb4Z4p/Fg1kFOP1/fqRJ2dpdy3HsawLxs4ssgqIELBMD0o3FCiG\nbzTcSYjv5mx3vLkzJBLPpPUcC6gM4Bv/KUjXJ/YvwpCCRPjQRqqAVBcfJrmI\niqh5ZOKkZnjPQJUAlktIABS3N2i1i/W7pVx/gsuIt1pYNRlMUXS3EVpIvHSm\nDFOq+evgfagnB0+4PbeRNe5wyTs3LmBvaEbJXIVGQ6zu8TF/Prrv90Sap0yf\nhQk7fr08Fh2OnlWa12M10pNZZUK/p3YRUHD7paTbqvWL+dW346L3XK8d+tWi\nJBpQ0msR6KpOKYHkezgzvypvPxhlwsO0WJ6gFP4hwsWjOxNpglnPabDas6+b\nvksjb6hoezFkmdRg8zQzqtxaCAaEuTJfTLH7ObNARWb3uS/5xMjcAX6qT+3M\nB035rHEQz+T0/5fRGMNrCMbCgz9dkGlEGu0A6c3f/Mu1tx8eBHGpybNvw2i9\nRbOV\r\n=K/hR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "e7444c718057f6a0a3645caf8e78fb546ffb0d9f", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "3.10.10", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/types": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/helper-optimise-call-expression": "7.0.0-beta.49", "@babel/helper-member-expression-to-functions": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.49_1527264259555_0.15140390160884754", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.50", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6e49a25bb0a0cebdb3a3b9c59f51d783dd8416ca", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-PGy0DqwSqk5/98dG9P5OvgC5yrl8SDol4Svt4LJqfkbYHGxF+dlQ4YlRT1eXHEclH05+YSeJXfNjM6H4dJZpKQ==", "signatures": [{"sig": "MEUCIGdagzbzr+UCJa8bGCtME7Ki7oVcQEjYlTMgGnvBGbphAiEA597GECABI3wHITem1EQ3OjobZvbN5emuJ3jOhPZmYzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6703}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.50", "@babel/traverse": "7.0.0-beta.50", "@babel/helper-optimise-call-expression": "7.0.0-beta.50", "@babel/helper-member-expression-to-functions": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.50_1528832879259_0.7882775081123223", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.51", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "279a61afb849476c6cc70d5519f83df4a74ffa6f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-+VcR8hOKSCYk2Mp9wgt7k++vXPnfFwxJY+bxZn23f4RlIXpVOvSKTvynkFbHkV9o5Vi+y072aRi7PFF2AjIkhQ==", "signatures": [{"sig": "MEUCIEr1NONJVzoesHlqxEYRS+XyScOT/RapZUhL5V2kl32IAiEA9+MC0SyQbdtUpwDk6i7EgUbZqJShcldyGieBD+RhmKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6703}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/helper-optimise-call-expression": "7.0.0-beta.51", "@babel/helper-member-expression-to-functions": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.51_1528838439803_0.18579520923942905", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.52", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5c648a77fe263fc7993d3dbb44ccd617ef7a6cd1", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-MEOl3pweH9FNYiZaNSDViIVYgwvA3mW2UNLsUpgt+GBJ15+SmDm5ztOyuczg91ZTvbSDqvKrQhp3L4feHUGggw==", "signatures": [{"sig": "MEQCICYQ5JQXQAB12XN33QJlVn9Bga6EaWCiVVmorIrYt3CzAiA+dxadHHAUdhUxoHVrKY5EcvG0HY7oC0fIrTUtUKiIBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.52", "@babel/traverse": "7.0.0-beta.52", "@babel/helper-optimise-call-expression": "7.0.0-beta.52", "@babel/helper-member-expression-to-functions": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.52_1530838785610_0.5513126393624994", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.53", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "339b5bdc102294495b1a27c558132306e1b7bca7", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-/KG2wmojlGgtuco35Aq5RYftukhXiql4dG7ux+oAnpi6wALb6BjPmUWJe5m57lNNQgLtCfQ8596j0h5GZu65QA==", "signatures": [{"sig": "MEYCIQDr8rkMHUJIH6QJacfRJx8ASWs1O7OoG5Rj5ghXpkmXEQIhALC+d0GGeTmsqOGd5MhvffFZxns6QXS1A4kOukFflaMQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.53", "@babel/traverse": "7.0.0-beta.53", "@babel/helper-optimise-call-expression": "7.0.0-beta.53", "@babel/helper-member-expression-to-functions": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.53_1531316444847_0.40962763686210035", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.54", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "901f5a1493a410799fd3ab3e0c0d29d18071c89f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-YdhbCAaDSfSJqRkTFubPyeNsiW/1Gn/NPrbTelqkvh9skKJWblk1H2CnxeF2VXF6274T9AjbYUdtPn00ylGrnQ==", "signatures": [{"sig": "MEQCIC5zK8pjRXMlnNpYo9lZzhLQqxBeB6KfAzZzLJH0++2SAiB3W/XAIu0QicFW1wNUcjZH8+U5NXoGLGLMDm7p0pGY7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.54", "@babel/traverse": "7.0.0-beta.54", "@babel/helper-optimise-call-expression": "7.0.0-beta.54", "@babel/helper-member-expression-to-functions": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.54_1531764026456_0.30961730173052304", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.55", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d588ad863990f35d8b0f67aa94ef8eec24171855", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-B9fZog0k30pC5SKKgmLu9AWFT1UNxRKRQ9sdk3kmxu6LaNkWbAT7n6rZQ3q21JimVWZMi7gZB5OY0J40IUdx0A==", "signatures": [{"sig": "MEUCIQDwHpsgq5Qc+Et4tcQncQmQyb1We4oJukmuwOINn8gURwIgFtzinJRJltS4fS42tOct74M3hXv+RM6S6n+jAgKgaAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.55", "@babel/traverse": "7.0.0-beta.55", "@babel/helper-optimise-call-expression": "7.0.0-beta.55", "@babel/helper-member-expression-to-functions": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.55_1532815671624_0.2517834339770424", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-replace-supers", "version": "7.0.0-beta.56", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "52f25c16b3dee1f4e8cd96eb4d1d7f9af9db18d8", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-Pv0a8XYWeYLMgzx6BiKYMkBPW7ilDeKmKnPfMD+sCsTRDMZl9DssqnkkSwGxgAeuPwZ9opx18r5EzYPTgt0k4A==", "signatures": [{"sig": "MEUCIQCMGTcmhIIW7lZjLSZKQ7Fax0egcCaZ3zGfJt8VAcwUHwIgeGs6CxFrHM5ylGBiAx6DKt2kC1g8h0YZXe8hiM2ywR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPySCRA9TVsSAnZWagAAqY0P/iJ7cRUj6rY6NTlhqjte\ny2hEucDHw6VCcXSrwvoo6mjqm4VDC+cJMI6zM/PojPc2hw+JQ9Goo6WkvKOA\nIh9u9pyVcPwIM3rYYIulRklqexqiJKpda2MzMKg4XyhhMxbDqrsdIu654u17\nzidx7wno+j3+eLO51TYtotcfrSvb5/AM8VoSiegw3r7P4GZzQzAJtStwWCng\nqnHtsl9pUTVb8DmWWdAe2Ge0In0mK/Nr8iET/wPpgAwANOxV7eiDn+b6j08R\nIR3h8xu2jRNZEVsNskOwrbAhGWAnUNR+1uKdCQqnCemnwRGrzTrALNwcQL43\nTC0z+n4iPICSQa15zAbr2OY4T+iwd2KF3DQ/QA8Cw9dUfY5xRsfTsrEDx6if\nztD7xc8pkd3p4NpPfK89eAwKYybtqEbqoiqr8qHeJE4aEkx5BqDPOfgISL8q\nHIAYqNWPE9mYZa86EhGLsyYPM0X0X46XmtdoV48EviJ4k7ZGikQQZwZp3YI4\nNtavsN2cZTVAwkof16tm1zzJPs4BfW0mXT4Y7dDx3oFI3TVdU+1+GuU6oJOo\nqIKUlTkU7wFicjzqtMxEhCS1qHjnCpDFKu+BL1qBRLgbfku/X8InrpVeaxPJ\nXOpNzUB1rCP4bpQjHMz1EMFfP9C7QuxoeMkWEJ93QTEq2jK7TTTbB93K/q+K\nOw3F\r\n=9GDG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.56", "@babel/traverse": "7.0.0-beta.56", "@babel/helper-optimise-call-expression": "7.0.0-beta.56", "@babel/helper-member-expression-to-functions": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-beta.56_1533344914239_0.4836267710055071", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-replace-supers", "version": "7.0.0-rc.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6987465ea727b588faabd8f72b527863ffdba4d7", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-7ovS1FrH2fwrdon254VztLQnynoYubO7lI9uA6nNtFkyK7W9DHf5RhJNhIpYjWRaOD301PGzU8nTo1ITVdGCRw==", "signatures": [{"sig": "MEUCIHpI5uIy6b58X/PeV/gc1hVPS3vYwuvkVe9SxXgwaGo7AiEAlcJqaZnz5A/ZdS2/fUu4yNHYPih5mfzG4Hd2KQoNlbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGT6CRA9TVsSAnZWagAA4bsP+wQP33Due55R3LO7S4vl\n7hDkIiJY0eeZqy42W9/N0G4sfxOhPpXtla4Jc0Ym1VEGkjtpx5jcoOxzxc+I\nO917/eGXjaummB9fpKaat4appZNSr14ipz85kmOGI03b8DrGzz+9ucZlJmnL\naf9V7Ro1SomR+M3U3EyRRioYsmx9VoNk8+MPNcf627jPr/SCBCTKvDg2VUgH\nYvpa8/KFgb2JQZK474X2WHkS3JpJmwae74C3D8SLZDNctnZXE7wDcxNASjFb\n7MW1Bgem6E+l40bvMzvm2nkAt/dB9Dt2Guz4u/lKzwBDQ6v62KfgYorDsanF\ndq8jWgdIruJbVTrbSOSlrpt71532Gx9K/YO43clhxdKKWCR5y+oALJgSExUN\nZ6WyZdRlXAt0eh+xago+h6E58xGsNoxsofhd4eKMDfmpvY6N7U9FOnBTiRUl\ne7DYL3Ic6HBk/BsFeShH+zA7I6cYgMly+FcY2TOO+OysU1SiGUAjXLLJdjVl\n6j/TOoiWbnooGmFP6RanC9gRqlpIOJLVUrdHINH5gS+Ek2/Zgj1dHIvh/vHI\nH3LmTKR8XUPx/e/MGGi4ueNNTl8lIkFcavxVMZ2Fq8RzxDv+/ev8+T2G5TTE\n9KvMWyWiJnDMSMDug36K9y7Egouc0xm5v5ej+5UitMzMfdl5uVP/qG6PQTrp\n6+Hj\r\n=SZHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.0", "@babel/traverse": "7.0.0-rc.0", "@babel/helper-optimise-call-expression": "7.0.0-rc.0", "@babel/helper-member-expression-to-functions": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-rc.0_1533830393440_0.01055469161624134", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-replace-supers", "version": "7.0.0-rc.1", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cab8d7a6c758e4561fb285f4725c850d68c1c3db", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-mcv+NKCazZfdEw7yBe/xROekR3qlFcy18d//mJTKnZb7xx2qFPjZAafkeIlpvzNHwd/WMTHShC4+3WjOL8FD5g==", "signatures": [{"sig": "MEUCIHzYv5P2vxoaTTcP/q+BvJD2wKYg+sjcldjo+LHUP9LBAiEA1y6K3Nlrcp47YAjfC4Y4HNRnb5SVrqSpQEgLtjcMC2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+LCRA9TVsSAnZWagAAbVEP/iiZm7R/Vy/o8KtVlAqn\nhIhLIAxX72IoNulgXxYhXKwrg4UmD3yC4fc7YrCV0eAMT+1HEsIQb8Rjaful\nUbNqd80pWJOsB5OScVIPml7XmNiNJgfxRpxscAVS6G7ZL/Zd99JozU7R1zf1\n7xxKH6LYbhZJFo7lrBskJa3KQi/fqDBOzIQpeFBPQWvyXOywwbPwtohAqIHZ\nq3scK5Glf1KI/Dx5NsBlHLURcIXVFzjshbwV2Eon1Y7lijt8l7Ut6pejTfJK\ndL4M/EWhxkPdpKE/TJalNFPgob2EL0FqIsdGTdE4/pUxIQoeb8241oUTus3L\nqXry+ZiYVcIxbf/tH9Hbvl5Wo5vow2V3L3dOxD2nG8xmuAn3yP/9pX5SyB4t\nZoWYn4+rJ4hGlwNWyXMjNrU5zSGY9UWF1JSw+OX0eKZsJyQ9I3mBCdGGfR+R\nsvy8Y05FYrCQKCl9+Mr9b8jr7C2xfL1/8+9I/ZabUDzzLCKLnf6nIigPZBnt\nnI0+vFIHS4YMFSOnT+dQ/JLNBkr2yDdfBjbDUVknQUKLqqhceOhTZGoCtPU9\n50QWX/hd/HAxkPBNy1Nli+VyGwcrO3zyOeS8IKRVgAqt2sCfn/QeE6ozmSCe\npaTUG75D4O36ebaV3DB+ziteNN1p7g2ObyA2exqLMsVUthUBFjtYIPuWbKOC\n2w/e\r\n=ooLL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.1", "@babel/traverse": "7.0.0-rc.1", "@babel/helper-optimise-call-expression": "7.0.0-rc.1", "@babel/helper-member-expression-to-functions": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-rc.1_1533845386732_0.49852747276131026", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-replace-supers", "version": "7.0.0-rc.2", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dcf619512a2171e35c0494aeb539a621f6ce7de2", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-MdMAYjNacQ3pCMNvAs31Nkp4UZTAE9ahMnJvkqIjgB7YgpNFfRI/8BX97SyWKe4qlNrkVxCE6WrY2s9nJBh7uA==", "signatures": [{"sig": "MEQCIGnID+tgOpBxDkgAMe2V6mF69NNVINwdugdbwpWFRlObAiBD3ib408n5bybDm7Xl5W0DNnSF+wBD7OmbVUqtAnQZtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGc/CRA9TVsSAnZWagAA0bQQAKROtEahXD+o+sPfVLUS\n09LTAjysSvy59U200Nh/k17zokHehX2AF5EeyHv1+sDG9RH5hZnzdIY5VLnZ\n35X98GtWwEq27cp7e+R6JMmyx8TWJ/nA+UOfWa19nklp9b1a47nxmXpp1C1K\nZMYB0UEqtLd4ighBF3HqbpAXtQpF6mFZWkVuv/jHHHVxeLeUERv7ooNrukN4\n8jIsk/7I97CXu1EDVdB3v2I9L7CEYjJD9+goeHzFY665lGM/hrFzvhnKZ4v4\nz/3eY4CjW9oou5Zn3b52f6HDCQr045xcd8uOnsbUtMAEM1b3CeFiDU0p8P3k\nnDzD7UmeyqfJ4zcKrHP651BeeuttuEP696OudlZur8iiQ9vqaNTZuJ5nnXwO\nWjHNzKt87B66Rj2OS7uzMM5YcuIs9I993AzUcUIVRxrtZWoErj3TQWCX4pdo\n6jWXnktNjQJrWQLfEguBFvCHVklZiJU42RKnWFKxccgvvNhuM2snDadvVomd\nPDrCmEMjhKhlT+8y+Tyw59tYLQaBWFblqHPXTnE91aSTpjPSE8ESb6w77tAJ\nMjkdoJfeh3ImTWxD0o+YTNoU3bbnZmTt+OJ2LaCWAk7EECHZziB04moMWhBe\nA/Qqd2fva9rCfYnGxP2VRz8TavkrBCxhkfKCNl6mrvSsYoDf/zA+vgTK7lcF\nei/n\r\n=9O9Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2", "@babel/traverse": "7.0.0-rc.2", "@babel/helper-optimise-call-expression": "7.0.0-rc.2", "@babel/helper-member-expression-to-functions": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-rc.2_1534879551438_0.1094344157885001", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-replace-supers", "version": "7.0.0-rc.3", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0172cdf556093b8e0245041bd2d76a12af756bf8", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-2SR0htUMoW8eO8cg3XVnQUP4rTsXRt82nBdaWEp3sUJorSsBPKYAqqJoW/fUO24CRt3/rtFhrTrVUsdSbBGXrA==", "signatures": [{"sig": "MEYCIQCs5jWmMgk3L4fm8f58S2WrswiqUABMdZAz15lYo20pLQIhAM/V8FCbpaiZ4uAHnPAIpqDNSAd3GT6U+6rWposyPJ43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnhCRA9TVsSAnZWagAA3B4P+QC+FIzSqWBJ/bLQZiIs\nFhfH+Tl7NkQSeU5T5uMR7nyUOqcRR5qA/xShAtbkU1iseKnyK6Ky5KFvB/5o\nl91iAfJvA97KABEi3/nsOc9+Z0qN0xK4l5QRaDico3f3EIcHYEcJW9KDteYE\n94VcN4C0+zQpSW9r5bhVvx0FfCiXgVzuwK8zHnox8A+ZdUOsjanGnTYrGFWX\nRCz0j+dLzp8w1pWiLYDla0sVUp27wULNWpMYdUJ/1mkjsne7JcY96lwID7VH\nITd2gaAujZASMr95OyzwkLSfNXUO0M5o98ZDeT+cumbC3X0uHucxZBYl0xiX\nI9iKzXGw8SMpS6RlGqaKV86SQJGhraL61lEni3YJ0isNXsctNnoIj16Dt02b\nCiyy8CfcBOjlsZr3EIj2+IXIgY4NNRN3ZXWhWmXhF5K/BxsjyoLbzUYi93b2\n21LtSFU3hn736DqRJFXtI/Y7zc0nI7fxV7kgCrNjz4rdlYL4wlwSTLIiY1OB\n4sGu5nNO1QgbDSTFAwrDqadhhYpCjQz37YcJffDwOVgcSAKMw/3r1kLJOzhX\nrL1fDOVEGFT2s9q2y8j745M/F4JghVAXRb9LUzpm1hu5BPqzTyMARyBPR8qg\nOCFRT7UEWyYISTnudY5hpmM82upgaeOAKifWnvh0dyFgygiqq+Uzsih+BE8T\nX2qe\r\n=Dhr2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3", "@babel/traverse": "7.0.0-rc.3", "@babel/helper-optimise-call-expression": "7.0.0-rc.3", "@babel/helper-member-expression-to-functions": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-rc.3_1535134176853_0.7934151405934571", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-replace-supers", "version": "7.0.0-rc.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7ae2b3520f604eb3d7041520deccf468cec571da", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-3hu1sL79T0oqgaq9OMi5z2vScj0s6xWWwqIcIk/W+bZZdaCUsIeLwjvGZOwWFkyLGGmnH5XnnfEw10VDInYUJw==", "signatures": [{"sig": "MEUCIQDG0CSOlzXVrGH0UrS4ZKwFksiUkR7erCfctShNlRXZGQIgfWRk/xcKwHu7YPpiCADH7hzuR3qbMlKF7PKsuA4vxfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrbCRA9TVsSAnZWagAA9dcP/3TYGF5k9eg3QCQYEAZu\nI9mHUDX30dQQAnW3uHsHmORpLKp2jn2zMjWIOF1KfhaANWSG1lxFJDYTXYYq\nOxeAB8ZA00mcsKyffyTkPjlx4Xs1Zc/Q2U1YfDc9OMzwIhy2ztFk2zI6fFC6\nRyA1iZoVe+xwLbr0fuMGJ7WDJ82k8sL0TEJ4Oueze6hSmA+Pqua3Cli4rJd2\n6PCsQWiegrX2XMQu3zfbvKwa64Iub+nlIC9IuYvHiTWjfjQTcgP5HbwD++ga\n83ibwSv6BWTcjEv68tVbP7mxrTvuCSa0+xMIj4EyecKSIoyAWvMnB3a4E9Jv\nDFJ/Wr9dm49nEP9SL67jMA7xSCVcXH9tVxtp6wGItDI07ru9GJlB1xPHgLiN\n7kdGTcDRQtrBOsQN2H4iT07W03WEhy4FMtWtkY1VCoSJR1IQocERYtYr1lgc\nz8L5MVE0vme6H3kaCM0JMQCPQcV52OFI8UH68s0+Jy9NU8J4scEybjau3K2/\nLcpsScffBf1YuFPNf/8X8RS74mu4DcexWIlIo95Je/ijPFJBoP2TnJIsD5e9\nG1cnLsttPsb4TC9N9oAxntD5KqMR4zfiDt3gv32W+mBvv4Zk+jFxyRlZTsR3\naBsH3wUv90ufFYiMXWosujE/h7WAxwraLNjIUOk7V7e2uhJG4QiPX91b+iLq\na9Ta\r\n=hUbZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4", "@babel/traverse": "^7.0.0-rc.4", "@babel/helper-optimise-call-expression": "^7.0.0-rc.4", "@babel/helper-member-expression-to-functions": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0-rc.4_1535388378877_0.842663326963335", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-replace-supers", "version": "7.0.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b6f21237280e0be54f591f63a464b66627ced707", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-fsSv7VogxzMSmGch6DwhKHGsciVXo7hbfhBgH9ZrgJMXKMjO7ASQTUfbVL7MU1uCfviyqjucazGK7TWPT9weuQ==", "signatures": [{"sig": "MEUCIGWiIPZQpsTiX76egyQsly7SkrMtwLXWv4yYF+LI8Lj3AiEAmZ1nbXBx+mai5eD/VnRInvU4CUay6YVisxRwyi7ZlD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDRCRA9TVsSAnZWagAAjlEP+QCOQw9fRurZPhYbftsw\nJJDcqZJuhYJf/b3qxwelYShbK+3CZzagRM+NXAkvPb2g6bUY/qAalKMvQJG4\nkiMGtrDd36A6Iz1yumHsgfnLz48pTVIEc9pEzDwtI521agmUwvi8C7UDpfSX\nx1IZb0c9dMEZsKAVXEVqxUr6LWDef5OVk1+nZC1imf203hmpI8ik6jlP46Wq\nEBgEm5WQ9U5bRcndQgiIZ1aExI/fGYDwdLTzk/ZshB4cQ1i9DOJ0BSMuRlQU\nxxMc8SIZLYJaM6Ixiuh+zAhlC83Kv34iP99DLKtBoIOszut6rD790CJmp5lB\nybQn2w01JAzy/foi5bOGSgDBVk7KyxyOcQj4f0qse4Mwe+sJg6lPynRAx3An\nAx0/b8kPMLAic7mEAwr0XTUUUhy0UVzxqTdSJB+mRr+85JfTTDNm3PIUfxoR\n/PQTtNT7p7G+wZ1Ti8vNOqeaqul81cCPUxHO9qQWqufPlQSozeUzNFXanmUV\ng9hpczqMVc83hwqiCKWaIp5+Ose4kcDQNRTnbYNtq1M32SEkwW/Dnunr382P\nNcxmjOwI4Z3uuiGxpLK9/NVSCGa1+zapdJzNq3t1t0kh0G65Ji2jR3eZ3gkr\nsbDOSKlH/rVY3Wrbm2FD6rD4WLicBQO+CbpV73ea4cqL8yH4nHO6jwFIWZzT\nRYVa\r\n=fJpw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.0.0_1535406289115_0.5882860847738554", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/helper-replace-supers", "version": "7.1.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5fc31de522ec0ef0899dc9b3e7cf6a5dd655f362", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-BvcDWYZRWVuDeXTYZWxekQNO5D4kO55aArwZOTFXw6rlLQA8ZaDicJR1sO47h+HrnCiDFiww0fSPV0d713KBGQ==", "signatures": [{"sig": "MEQCIH6YUPsMrYWGb8OrBO9Tl/Dm7RL9UWCcdX6J5/QGk2LtAiA6w+h7k8CLSpd7srlDdaDkMgHjl+rUD108J8K5/uqwiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADxCRA9TVsSAnZWagAAD9cQAKRbmFEMbfvwrAq+Tosh\nKK7FmEW9ljtk+CVXlxm+TbpD4AifSh3+lKsHtmeNpMHkALYWRkZ6KSSpG+ls\nBAWZLWQ+vcAnksS79KzLxo5WGaQocPP2InADk4N8RKUkXEzZKdjQgfs2i7DL\nfDDA8BTOgbzaHvZiiPCQTk/pJTUpoUHsS5oFqwhLDMSqtMV2OD3pL+4nYrZD\nFwKj3xEm66r+2KJOzSB5iFY13BYhsHvfvp+5sOvS2nGezFgLn+xD/jZgVscn\nA/KOIIr0xF6cMuq/6W8soEIZKHKUFijm+sZjx5nlbWaQnlkCbgEE3Q4pyJir\nX3vKZrsrtVolQWL00hPm/MbSE2A3mp1h29Af5ltkFHlqpWvOU7GbHyLW0nwp\nae9arkZuPTtFbIuUmmQ7mTdZNa75xESsh8eqio9Badr6SKnxuaHwU0LMBAAp\nTx9qfcVbc87MbHH2aat43eMwAuD0ke4ycEY68dDtpfMqIa7BBiMW/O3P0moQ\n3mY/1gMg0K+a94Z4e9u66GxxjazRAGJaXIJ2PoGc2HcGIPreDj/280CjW76j\nKwWRYWosFe6+7y3ET1dkOG3KJqsuQ3VU/F2rJAiPyCLBE74jjxIZcrIJf+hM\n73WEjJVYw7cL/97DFmfcEBUKawEAk+VrgyWNSJl3XJSJYqgI72QXo4/c4N/z\nhDVw\r\n=5TXl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/traverse": "^7.1.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.1.0_1537212656165_0.13841033697533822", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "@babel/helper-replace-supers", "version": "7.2.3", "license": "MIT", "_id": "@babel/helper-replace-supers@7.2.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "19970020cf22677d62b3a689561dbd9644d8c5e5", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.2.3.tgz", "fileCount": 4, "integrity": "sha512-GyieIznGUfPXPWu0yLS6U55Mz67AZD9cUk0BfirOWlPrXlBcan9Gz+vHGz+cPfuoweZSnPzPIm67VtQM0OWZbA==", "signatures": [{"sig": "MEQCICK6WwM1Wi9JLsvWogppw+ZCI0+TLUqTI8ELRy5zg4x2AiBvG4nG4BT6DKBjGFM+bpf+4HSiJKz66JoIXwVi+hSsAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3lPCRA9TVsSAnZWagAAZmIP/0Bc8QIlhsF0A8jcYy5G\nN7CcDXXDHFlthGUgxBCNidGUoNmfCl5hw2SG1qdD3qIUfujym9IIMN7SdoPl\nPPs0mzFn3vFtzQvVz4YkOzYd5uVaK19dOLUXOhu7xKA2iL2BaMdJdI/BhRHF\nOcPiLY4WE2Ij0BgVtEPRQrLgv9IeL6PaEyMrgsa3xxerzYYCPTEV23YnTPpu\nzCOyXEgWxEoU+YCea1CAZAJZJe7pwdVe25tGb10+Ts+tK6egSgc11al1yz5U\nYUlw6tSDsl1GeBec0Tny34T48RPV8+DQpOqn9/tof//H4uOhQ+YDnrwocUUT\niXon+PFZIrFkNyLJQheB3FLZKxMax3+l31ibQs8nVywsz7ml2Y2Jgwo1fNx+\n9dKpftpiZdn4NDgOZtKt12tMXVSV53xLcXU0Gu6GaBs3862ls9aIKlutiTXX\nq4UgirfVp/c8yU+67YFSwiAUh8YN5EyWhbdUHVhsHo5GicLCYOVtaY0svVHx\nXH2mmMRy6ERL4RFJxnM3WLuTp+cebh7hEye7hT2utncQjOlGtxSEoHelPmfr\nTqaJOFfuRh6VUWDSxfDo3zoBwP+r+d/2gz8fLYnFF1mxLTjd+x6b6UYWkrS7\nBqYN4C6bLRdhAqFLiBoP5Utv5UUb8qp8xCF7pNb52bXv2MilKWAF+pd8grhX\nDKnQ\r\n=JvoF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d35f2ad92b322c3bb9c6792e2683807afae0b105", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/traverse": "^7.2.3", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.2.3_1545304398556_0.7695515770708181", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/helper-replace-supers", "version": "7.3.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a795208e9b911a6eeb08e5891faacf06e7013e13", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.3.4.tgz", "fileCount": 4, "integrity": "sha512-pvObL9WVf2ADs+ePg0jrqlhHoxRXlOa+SHRHzAXIz2xkYuOHfGl+fKxPMaS4Fq+uje8JQPobnertBBvyrWnQ1A==", "signatures": [{"sig": "MEUCIAPIAVVE/ccim2eRioSyYYI4GMWbCtbAN0hwrsd3W+r3AiEAuBfm6XAbEfsa/LGd0yY8kiwDEeyw6zhqkecJHKIuqhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDWHCRA9TVsSAnZWagAARyYP/0UZCeRU7oWRTTXugKHM\nVslIdVyBZkcJj8wI3FlYQ2YxdAJJMdXp03lE7NW/+3Zxx6T0v7YM9V0B9Xbx\n6hUgZ9aUdI0IplgGALQP9ayY+DbwdQf/HQZwpnoqrYvt3Ddig8Jhk0ljzeAh\nB8GKYLDr5WLObZcykRzrO238DLrcFMGEDZAkpGVMZRlyz/vtKOWzts3agkYL\nASx3WW9AT9zIE7doH5OxxgSoc3u5kJtvejUJZ8A2Ol7twFYfq5M4cHFZT4aT\nYkt6/yru30Iaz7igPOmpO19c4TfQGpDGdh8+id5D1ScbOpKQtkgrHlxvqr8c\n2aZhNOEatzwi9nH+SNRKBKiocbst2IHK5pHsNKM5zm+QiqvLGtbyENHpNsBl\nu/DkPAsxgRTuZobaw2F9EXDTIEjplOwhXZLCITkCm8LfFZDbKkVSLLOh+d/r\nbQb0xD4dXuv7ZB62APGhnJlxUEKCU0x39sAdQaraNQEIBhyc0WNKPrnFC2xK\nbIJB4TcaHfvSWAbXP0Seh/wN0ae0o9U/BJDujwpTIGk//GdYD5z8U+V4IfZe\nW+rGqfzoZP+JWuRhVXmQZMVOAXcw8WZCLqg2imAuQ+ecm2H6DgK4tSo1Ohwu\nQT37RICaTwkv7oVm6qIKi0xWozuMonzgAsG/XIXEalQJDiUXzzeFgmLNrmFy\nSIwP\r\n=KGtE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.3.4", "@babel/traverse": "^7.3.4", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.3.4_1551119750494_0.7830755649202308", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/helper-replace-supers", "version": "7.4.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4f56adb6aedcd449d2da9399c2dcf0545463b64c", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-PVwCVnWWAgnal+kJ+ZSAphzyl58XrFeSKSAJRiqg5QToTsjL+Xu1f9+RJ+d+Q0aPhPfBGaYfkox66k86thxNSg==", "signatures": [{"sig": "MEUCIE3HQpJf6o7dhm4ypOPuaqp/v6gtZpjmKdmMTUNYqgQpAiEA+nn/oXs0g+NE1a4pel+i0cizxdwNTKF3ravM4bkTzGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTcCRA9TVsSAnZWagAAba4P/3IEXVkLGWmgtyr+m37U\n0nyjJ8N5x36Y4e620CssOTFZ5ypB7LDwFhh+5KgAS7Xphq6UX/1/Pz+wCMh2\nbj57HT8NxzP0RQa+gCD615snaXZdEO/NacUNm+hhqYmxRCZgNFWzOAPRSSTO\ngvT79rYlpluEkUAAXZyNQZD2HgXH0gEcESpyLm/6GcsKcvJ1oR8ueg1//3jf\ntuXsmj94qg5LHdK9Zkqc5/qppVpO1G4DJSoAgeQ5xw8Kxx7ILiH4zIc<PERSON>ryl\nE<PERSON>q<PERSON>tUqKZCA41QVoLgB0TQTbg7x2KJ5pCx9QVaUB+VgYQvVT0scDy0p7f/6g\nM1ekoUWtai9VL+oZDNrhSlVPzM/+maS527AnZlIQgqjJhhgIN2da3uD8l9Lj\nqW/PQIfb7qzqBWbcCiJ58WynMZhK7RNg17TDCvYAx9pAI1mnLY5WdHkB87k0\nMO+eWT+sH293QcVBg8KlO9fmCImaI0Qwhz8a6mzyTQc7K+IHbqONg4pmwxkH\nXGZSDpjFI0M54JLAbDhRXlZu7pY3KdSOCha2ejq5kBYVe1DxvbS6kNX7Nee5\n1X76xkhv5xRcsjChm6NJA1YoJQ8ySuY+w1+OZnVgzBMbLCHN6lIjfitkaOo7\nepppdLTtAAS3FB40CbcQeZFZtfAjuZmwwMlnzhv9Q4nMz9Y+njm46NgQKZB9\n0b99\r\n=4xB3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.4.0", "@babel/traverse": "^7.4.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.4.0_1553028315465_0.8686387245705951", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/helper-replace-supers", "version": "7.4.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aee41783ebe4f2d3ab3ae775e1cc6f1a90cefa27", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-04xGEnd+s01nY1l15EuMS1rfKktNF+1CkKmHoErDppjAAZL+IUBZpzT748x262HF7fibaQPhbvWUl5HeSt1EXg==", "signatures": [{"sig": "MEUCIQCMpoFAacG7xVPdxIDF62RVcoqii4yyvzTAZv8A3VoPfwIgeJ6N75saAAjE1RqUNi8ff8ThVMlyqPvT81uZGaqkFyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3J0CRA9TVsSAnZWagAAYBwP/0oRmWqq/rEKPPabR9PB\n3BxUoWHCXwew8vlgw2F4Q/ooJQ8DTwMjFkCrJTBIPi/1dyw/ni2OnwSC+To5\nNcsTbtjnjIcgi+U7iVktNJEDkcTUnLFxhpuHx7xvf9uQry+8FkE9pK4+9GnP\nO1IKti5FcqfeFPzrh83OIN7IEC0k7LcDXw4I6qT70rkAgywuCq7smiRC9dfx\nB/ACzZKlcKRcsCJjh6CCOm35QGarsm70EfrLI2gWplhNiQLet8DmorNNRQkX\no9NWTge5dwpL01PaFmTUcoEpmE+1tA9t/7UnpDb7hxx+PJ/Og1fA4Uokqb9f\n6h9M/gi9dAUG+QTTocl4uG+yrSfyLvm8wFHZw1vyp1z94prVBWilV3ElYEMz\n7dQTuejv4Nb9j4dKys0I3iA4REmrIEEJ7CBa0OUzo9OZN45phONvR/uXA5oI\n8S5aJBaITsntRL+sL3aEz7IH3tT0Yl8i1UOn4M0uOZbiJbPy9zzp3Hk6iJP+\n/YkrGBd2Yt3ZGHoWwfko0RCkfvqNA+lfCpMmaDi7eY8K+igtO4TXZrt5i9/m\ny4WDAB+TisyRSEwv3BPyvyFfLfSFzUvIVe2eRUNTFSy2mppXIjEDkKqkLnc1\nmPO9j7XZ7PHvyMzKw9frVLqTYrWp+O6CEamzh9dVEIldXZQSNpMUwol2ScAu\nuu63\r\n=3EPt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "description": "Helper function to replace supers", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.4.4", "@babel/traverse": "^7.4.4", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.4.4_1556312691324_0.8289236067499024", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/helper-replace-supers", "version": "7.5.5", "license": "MIT", "_id": "@babel/helper-replace-supers@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f84ce43df031222d2bad068d2626cb5799c34bc2", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.5.5.tgz", "fileCount": 4, "integrity": "sha512-XvRFWrNnlsow2u7jXDuH4jDDctkxbS7gXssrP4q2nUD606ukXHRvydj346wmNg+zAgpFx4MWf4+usfC93bElJg==", "signatures": [{"sig": "MEUCIQCtwN2xVjETSa5RuLwZoWMpc4FOmvC7RftANVWqhxjD1QIgKz8x/D3Dwq4Q6rbiE7AcMS1ySxxx8y2xszhiB28nHa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FuCRA9TVsSAnZWagAAxYgP/1m7UzwBLWE2WjQC68Xr\nZE+jlEORXD/NOZr1LRoG8WUjvq9FwmMVq5bk3ErS8a+Y3Jx6SCJugvhMDl0b\nyNValPiKqCq7n/BadwsRUG+vxCpKqyng/48+FCoXDX7CvDgdTi05N90eDy68\nHJjcDhI1IsbNpVcZe5rCcNWKSitwpQJMNT+k6B5aQ1WEdeHwFnLlN4P7X3QE\np9kKAZraYjrC0O8ek736HPHLMwxy6Q601HDizxf2dTkRGobd5j5lQ7tCCIfO\nkR8RsnHeVnYHcSsbzkTad7W6otYLyGs6a9WUHc0ebSO8krWm8eN0UuNuo8LH\nKrkbQeP/P+lJD+A8gFj79bq2cvOP/p7DMpl0Ay/cn5GR84R4qW9dinbDhCjm\ntavlMqekDFi46R+3K0bMsS/tukB8qW8DB/Vax8W98nqMAH3EP4WIF31piAjc\nLga3gUD2bLIdG2yR9oQCeYt0u6FMkhTxAORWe8mikW39iTvvWlJMP6HAhW3J\nKk+Jyazkwq7Px52neG0l+20zT7q/xsmupatbwgBHl8kyTvuP7W+N6Orfz18O\n7kICReBurkVzVzMKU4yFkxjG7SSvxdgDHnXpJhtmfN3PjF+vWkvWZye+8vHM\nzQ47oZGCuKi4FXbFXpoljqGwAB4n0EiqZQ4C4x41/RUSzsk4lpyvSHoqOvOC\nuhC9\r\n=aWY6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/types": "^7.5.5", "@babel/traverse": "^7.5.5", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.5.5_1563398509334_0.33207375105138426", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-replace-supers", "version": "7.7.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d5365c8667fe7cbd13b8ddddceb9bd7f2b387512", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-5ALYEul5V8xNdxEeWvRsBzLMxQksT7MaStpxjJf9KsnLxpAKBtfw5NeMKZJSYDa0lKdOcy0g+JT/f5mPSulUgg==", "signatures": [{"sig": "MEYCIQC860fiHIEgRkgKXjJuJuFuW8bEeWRXKUc09vq8qbig/gIhANdrCR49Oh3eqEB2+3UrMBletAEB+ElykKs0edgoqd2C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTECRA9TVsSAnZWagAAIzEP/1k0ayr8TxM+BhWQJuxT\nB8HcPcldQQI6Ktg0ZZpn04tdCgN33l/MIAfnWsqNo1jSaVOQtAQpdjl2RaAN\nHLioLhRIXLrqpVAf3qbkeIXTKxHahmK6dwOKA8zIzhX1MYeOLW94bxG4z7T3\n6C1b11va6naXyNEhkc5VULAfqJqsFZf9XsNO2XfIbUc2Iv2GadN9tMnRvf2+\npdl3cFfUyVIKCAm+lEmynmgKyXCxFeN++AagEpcJ+pr3RIUwEBl06LI3PQ4B\naZDtpeQyg2ujBwdAwk1qJsrzi/9kAJMOBmb7olfOoCXaO6WneIOlXSkux6FR\nDDH00UZUEFlAvzAFABGvmf7oJULhZo1XBfBQ7wp1ursCLFYaKWeS67gDdJ/u\ncmKKyTbi7G5mwWk4aCz+hAbyt114pSB4k7iLj6V9l82WXVIxgwmio2v9Ep44\nFwVOByOV968P+nMHbQcxWdjohtKL/qQ4ZmAInjz3Fi16xXL4nI5TuDrqzMWs\nV2d+/+PvGCrPlFI+sAtY5D5AuNBHMeJd7U7PPiXNUnZmHTa23BWLA4Bc2aT+\n+j747vL3rMLSFEkRQ9jO7lhEZ/4Jeeh+vHXMAmpZfjTI49wO7P59r3sCOQPJ\noyDontM4ATAofMc3bA20+vHc1aRR9sHPER6HJ8Mxd++/urMjJu+37ZJ+Gxwc\nyTlo\r\n=00wS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0", "@babel/traverse": "^7.7.0", "@babel/helper-optimise-call-expression": "^7.7.0", "@babel/helper-member-expression-to-functions": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.7.0_1572951235864_0.46017877062358936", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-replace-supers", "version": "7.7.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3c881a6a6a7571275a72d82e6107126ec9e2cdd2", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-pP0tfgg9hsZWo5ZboYGuBn/bbYT/hdLPVSS4NMmiRJdwWhP0IznPwN9AE1JwyGsjSPLC364I0Qh5p+EPkGPNpg==", "signatures": [{"sig": "MEUCIHYFD15NjxE7mLrwydG/ReB4cpPv7WVjxXa6Xa7okMkpAiEA8Kw0pmy1qz99UeI44q6NlvJZo5GWJPRjmgKQXReBGXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBaCRA9TVsSAnZWagAAazYP/3uNCAYRygCosj7C7F5I\naLqPksg7kK/Jg7OfXbkfo9VmZnkQ6bYkC0ozlBHaHtEYdpX6IqPVMGuYnmBk\n8pJwalA0Qx24IK8tTw4AUoiwhEbX+vzNZd0QLbRv/4McLU8tXr6H3dVS99sc\noA6UORSmLsgW6i4zv9fkKMmo4EwQhhWk0ozx3Yy1lUSN2RzDZ1BZjCQCj0ID\nAb62T5bLFOjY7GPPy8id2LUyYDmgESb8sJBYJychV1isJD4+ywSxqEkmLjzJ\nzt/NIxiqHJqzf7ivXoLv3+/zqwAs0fZ2Qj/4E5C2vwm7ZY1DJ5xlkLcaaQ8Y\n4kxyNSSOBHiG1lbYg+cb7JMVVtEFH0BDWKA41PpRnhXdLHoTunEAokntX2zA\nuV2HJ/bvtXtotVifmNj3Q4ipfN6hu16SpeYGbSEfC8dtO01si6erK8RdsGGQ\nR4f6LHvxpEYcpiB+cKcy1pWQXeAuwTrXlCxdrACY1YVef4XGDptYnd/M41U4\niIh6jIUxJRHpwlEkw+YxCL+DPqB8n6FkutoXctWi4sSx0XcSnSjeu8sgplFQ\njd2V912PiMnwJSDnPZv6do1y0V7fVSTWGBqsSDDqWDVwdFW7dxh1qmNMwh5M\nRbGZ7dHIuRYdTY5Rk1q98TAW6kWdEMp7lneV8qrj8x9HYuqhA1CCNwbLSBDG\nGE1R\r\n=hLnw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4", "@babel/traverse": "^7.7.4", "@babel/helper-optimise-call-expression": "^7.7.4", "@babel/helper-member-expression-to-functions": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.7.4_1574465626277_0.876316353609266", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-replace-supers", "version": "7.8.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d83cb117edb820eebe9ae6c970a8ad5eac09d19f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-R2CyorW4tcO3YzdkClLpt6MS84G+tPkOi0MmiCn1bvYVnmDpdl9R15XOi3NQW2mhOAEeBnuQ4g1Bh7pT2sX8fg==", "signatures": [{"sig": "MEUCIQCgeyX5qDWQAbR9heqJ63v8wNAxI27mxk1hXgyeDpJCUQIgazEUptLx+z5WqVmc3VnMZ75U94U1hKO3LyGQ2M/7n4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWWCRA9TVsSAnZWagAA7vsQAIV82mb8WYgv6ke5dZTo\nQxvQ/kVKVgDyCF/iPVdjqWZQdcFyKpezAV/i08YZj5pqz5jA9urU8pdk3uxo\no0S6eW3hY+rBqLSwSW2lUKgGcLpmr3wXKhWNbpTdPN3xqo+4liG2WNIcTJkY\nt1zUu8R24O2RCanIbJDSdXuTQzdlXBdOL2/DkcMMxlKf6I+moaeBSuVGr/9l\n4ihNdgHpsWszyJphKxRGYi0XLRCdG02p8hl52rrjxpCo0tmXufewLXOwInRp\nbOrHDglXx/THWY+Y7sykPx/vGH7vwW9Iu9nD56T7ixyfBpyE3eoii4iDTnzY\nWemxyZPib/0c9XijKIKDgZ8wpU9tLmUqI4K9ufEX2jAXwR9CPNLfnbExELvj\nYakLZT8fDOH3N0x6xtEM2s87Rt3kT7wm7GNXJoIHakb0DtS1nirylctONpWh\n7xiufO5Mg/Dla0kUvQqAr6fcCbdpWMUGeZQ51qMsGn3d+xHLklRfvhawPmog\n9z6ME+ZkHcOqbn7vXfypt8/6ewLfxAZkNXOjFgdF2JdBger8rtT/8HXeLmms\nYHrae6OPoR8DEz4Z3IaXnQ5HdN5sztRrp21uioD4HAGc55hOoKIZ8wRT7qrh\noU9nNRUFlPK7t44FGSm3lU76Til1xr3iUkYWaKS/hiWMZ3XWsm1O6ZaZy8lw\nnT4I\r\n=4hCl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0", "@babel/traverse": "^7.8.0", "@babel/helper-optimise-call-expression": "^7.8.0", "@babel/helper-member-expression-to-functions": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.8.0_1578788242862_0.911972211655536", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-replace-supers", "version": "7.8.3", "license": "MIT", "_id": "@babel/helper-replace-supers@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "91192d25f6abbcd41da8a989d4492574fb1530bc", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-xOUssL6ho41U81etpLoT2RTdvdus4VfHamCuAm4AHxGr+0it5fnwoVdwUJ7GFEqCsQYzJUhcbsN9wB9apcYKFA==", "signatures": [{"sig": "MEUCIQD2prTLBgzqajr+w9NLqCKDLhPHOglXc2EjHPqo17btiAIgdPlg0YK4nx1N9jeZ8ck+i0PgvzTMxVv/ll86Wg2O6/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ9CRA9TVsSAnZWagAAND8P/i+0gXLsyzBphyq3sdX6\n2NhluuSRc8WSFFsma8aIsjlzadfXKxINcuIWundZgZCPfWo+5T2vzZqA7zeC\nYkqJqMcW7nF7ONEMxxLIN6imGdbUEfo11Nb+hR1DD8r5OSpgNZ7O+e0kifgb\nL+JkhTSy53pa69czU5/Bj0u3XSesFnVGVywm49zMjNb/Pc0YecZOpF7I2QlZ\n1F6LsVxkY3KKYCDZeDK1horQUJDnLLInpftksGkcZ7DIXabE/HiUZZeepTVG\nKUkvguUsmqOIlLmUlPjgflgxb/8Cpi7gE0cOeGw607sI7T+7OtvahweHGd02\nD0chio9Zw0hVnooLCpaq7nYwgoqsq81CXB1MJKHkckeZNHNMWCuQFT/XP0tC\nJkKjaaHf9Uj7IvJeuvDKKflCdfr6K3Q4NcxBuu3SnlPwKTqRZtZ2DkGpwfO+\n6WHAa5XCiXorwJwf5OyCf08I2gEhOIFmybNwpVG1OLyQENsmw2ouKrTt69xj\nTJnIrih5G373HR6hIO/PTg9GgAQSIIAUj6nqrwhIG3st9tgPYnBNOsjGwHmH\nOsJxJPBauTP7ad93wyp4N8RT/pHxPBJ5OwDC6ZoZeJ6aLsq1uT8GXq+aVgEx\nyrEolYEBaFjVzf6C0H97ggGbeeij/3Ni0xlIAugBRmMjentKtLcdlt+6hfd1\nX8sH\r\n=/zFF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3", "@babel/traverse": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.8.3_1578951741548_0.6347751901753393", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/helper-replace-supers", "version": "7.8.6", "license": "MIT", "_id": "@babel/helper-replace-supers@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5ada744fd5ad73203bf1d67459a27dcba67effc8", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.8.6.tgz", "fileCount": 4, "integrity": "sha512-PeMArdA4Sv/Wf4zXwBKPqVj7n9UF/xg6slNRtZW84FM7JpE1CbG8B612FyM4cxrf4fMAMGO0kR7voy1ForHHFA==", "signatures": [{"sig": "MEUCIQCPMKk4vSNNgsmAVdJhWtiNbwSH8YioZA8tUOCnQyLRDQIgEh0XTWp4zPEwFz49XhBQeU8f5m/sqejrXN6VlQPr1g8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RWCRA9TVsSAnZWagAA/EYQAILlaJIQXTdOZMqwUa+s\nCB1u4nKo+UW29H4cae9we1GRr/o6iAG6iZneVb43lqzXnXWMvvb6xaRoZXi5\nQku6o66N+tv6oZdbUuK5VbUg5Yti7xXHZ2aLt/HJO7yC4A4duFQBpXZrpIS2\nsKY0+/92IKyXAqnNiCPhFvdB8O1GOiK/F7KxD/98ZwcxfYbPlXK4TEIRZioR\nXOh2DbqprKbN+H0EmGTkdMhbO/mEkva2GOt2CH9njiXPHuLWeTFvjULqT8jM\npNVBFFJPp4mybd1ai7z8liOEbjlpWoiSy8s1rOwqyuVQTNAu2spK4sjbfkmY\nFSXVl5t1eASqa8jVwQI6gLtykgoM+ukSoXeYSiCwnyXMWZ5SYF/5yoxXTi7N\n9E0CrvcmY+QcJvd1CxwrnyY7483OzSnODi+L03BbtI6QBOWySKVyonP9Ys+f\nfWKHJwYJZtKIbHNIDlwAA0QM2eWJw2jbXZBJZ46bJOG6qguDXwiloEnycKXw\n4MSZNApK0UBuw+bLyENpD+GFkCVIXcCPkNr5RYR+kIPWLMViQnMF8kUFzoFd\nf4OT1LbS+l7y1OGmKkwh+rp+W6G3z+jLA57kZHlB4Y7Sl0vBsvGKOn1Tp7ya\nsBhfQL5Ab+Gy9J3WyMSlk9kryjV/ODMb5Q2kKGsSJ5BMi6BkJpfDvBQY14oR\nB+uP\r\n=SDg0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/types": "^7.8.6", "@babel/traverse": "^7.8.6", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.8.6_1582806102284_0.14869766236148885", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/helper-replace-supers", "version": "7.9.6", "license": "MIT", "_id": "@babel/helper-replace-supers@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "03149d7e6a5586ab6764996cd31d6981a17e1444", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.9.6.tgz", "fileCount": 4, "integrity": "sha512-qX+chbxkbArLyCImk3bWV+jB5gTNU/rsze+JlcF6Nf8tVTigPJSI1o1oBow/9Resa1yehUO9lIipsmu9oG4RzA==", "signatures": [{"sig": "MEUCIFQXKQxtsAUM8ZNXsjcDn+kP1K0hVfPD8x1AdGzlE3rGAiEA9YVJV1dI50iUJtFCQuRcsvJ0khc4ctt0MO1oXrK9EEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmdCRA9TVsSAnZWagAArZIQAIwhEgssJxQrJdMi3kWH\ng+5jjUDgOnWNEJvB+5QWczZcPI51lX5myfBh7l6rpz2w1Lj19evb3OfVZcPC\nOKeuSGGuW8VFaAIdogM8j3x9gV32kell0ruKqicRFZLj4bnjYhPB/tNrTbmz\nVGTi+aNFAYEPRv+3FYFyQJyb/V0W+FkFVhh/OFscBNQteMuEK1LmAkOzJEFh\n3OHf/fdkRSaoM0d9G1jNg6OmR7OHEGOjEXMYpuMZCz0xpqW4nF2OJhTkiALa\n63UBjrDr8rQ3l/PxrBthJPKxidIIEIdgQWya4N7LaLKvFURdJgmgj9dKlCqt\nnOZ6AW3OMXYGXeiGIdaEeLH8/3dEbkq2U2Xys13JgBapq6sv1zBvnQ0KguD5\nD0hAetScfZSWD+ENCjqu1IxZmoPNfyGlbMIwEFJxfPEFQim0PNYbetAsc57S\nnJ6uBzCGucRb5oRjPp44E2QWWpk+uM5GgRpPubfgPria9BkmTch+kXYMWYVq\n/GnZXJZSYAP2jp2usq4VgTSNBiT2ust4JxGX2Vse7bVjSxE2HWkWQBOiiGlj\nbfn/YMDlHIDrpPZNYrbeUMwVaZyFX92uhW4id2Nt6n7AjNr35CtfMNdj6zA1\nejOklvSGyEZEemCRCxMPPKgwxx2BvSxTNZvpVHaR0PDeKjVZmMT+pE6o2AWg\nCNgD\r\n=m65v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"@babel/types": "^7.9.6", "@babel/traverse": "^7.9.6", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.9.6_1588185501416_0.6557420334225943", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/helper-replace-supers", "version": "7.10.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "26bc22ee1a35450934d2e2a9b27de10a22fac9d6", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-erl4iVeiANf14JszXP7b69bSrz3e3+qW09pVvEmTWwzRQEOoyb1WFlYCA8d/VjVZGYW8+nGpLh7swf9CifH5wg==", "signatures": [{"sig": "MEYCIQCINbQ2efup4My+7+nDSluk7HE4juO0BL7gnbFc4axmPQIhAItvQabE2dCVfWg8w9mIQebQTrgNj+vyNnu0igcuCoCs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2RCRA9TVsSAnZWagAA5AsP/jaom3shhMUmS3Hncvzt\nDuGFM2x+CdisFFNh9zCiM+Pu4gQyRO7BRByee0vJetx+IRTH7/Q14Wtg90l8\neU7W5/WOhOMr8hv5Ax2YE6eOKQcsje7hAXXqO5tcHcuNTjuIAKpcJhPJyshT\n+/J2wNentkeTm52ANNNIZTrMLZaNxEHTk3O6feMfp4srxgrVaDvxkx1KaRi1\nmLrzcwl5WKRCyzC+R6zZYXDtjolZ463HbdCOVMjHVrQHX4ChqT+Lp81UCDAI\nRsFdWKinG2qaGMD3b1vsU+BYf/UTIcQj/j0Krbnjg4/C80a6oH3etjcKXWd8\nL64b0+YBQQjLb22eJnw1IifhaYyZF8c1nW8vBLetKqXH6iK1/xITR6nCwZqX\nOiRiihxFB84YA/o0kjcdGGr/JXccsDJbh0YbiDbBtTGx9w/p6+NHl26uZqPG\no4xAhEyLc+MOYz+kKPZoVhRB31LH78Yr7TzdfhT6qYQ1EZ9X/+/EOlO6gjht\nwYEzjH6sJFgcxykknYtv28go+ZWpE5uVi8HJCmtwchmpP+noK77BwQs4/JT8\n/y/hpmRCLUAkiOBW1RA26lVHkF+qDKNJ7ZqHRjEH/oC1Kf6zhVmrKowkHPKy\ndHECSzp21hAvW+fWNTjSH96Ma8laUKVDB/qeliJyZ5uNhYv55MDL6LpVe+ly\n7hp/\r\n=Aq4E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-replace-supers", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/types": "^7.10.0", "@babel/traverse": "^7.10.0", "@babel/helper-optimise-call-expression": "^7.10.0", "@babel/helper-member-expression-to-functions": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.10.0_1590529425285_0.8931920984935111", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-replace-supers", "version": "7.10.1", "license": "MIT", "_id": "@babel/helper-replace-supers@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ec6859d20c5d8087f6a2dc4e014db7228975f13d", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-SOwJzEfpuQwInzzQJGjGaiG578UYmyi2Xw668klPWV5n07B73S0a9btjLk/52Mlcxa+5AdIYqws1KyXRfMoB7A==", "signatures": [{"sig": "MEUCIDu7XY48LhOsTCQyPATe3R8IbPeqoZmJpwo6pyxwiJNNAiEAiISfkA1UgP3j6pkQMeiNSET6Skpt949xUHkr5MYROAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTOCRA9TVsSAnZWagAA1qIQAIIAiCfCrF5AKtreebTx\n6bDE+HnRd7t8VROVs1I6TAA6lpv+Avmd2voW18uHsSiubSU3nycSa7h0hEC0\nEVGQdIoQv6/xl/kGJjrGD2fOvNBWH+H1qkV75WvTzhvE7ejab73xv8ZP5st/\nKONdzF/BWR1cVFvOgU9dC7YzVHJGVlRcO0EOTthEDwzjXaqmYFL4u2iysIp3\nnqXDiN1bizhCFQsOvi08xIjdIFxsut8MbZdDFwnDmFMiTysCTgYjNnGgfqIT\nJuQcxbqRAGjZr999fbDVX00+kCl9GgVH/6UKrJjAM57AwQ6JhBSJkaSsJTnj\nkU3aFrliVv85v9EV5EBFv53LJ8GikyP0Pctt/KSKpYsMHNLbOp1SmveSyfgU\nchvK74g6++cNUQnwmqugvRXZH5vihwqk8v7/20oBnobjd4WB1HTjR8cYTKbW\nFCSM2ssoeyk80Q1NfjuX7xFPQNIpApFNW9fhyUpO25HK023zDOXH7t/kJGQw\nbBEhvoCb/YhzLK8m68VDrbA2FaCuYEClYRW9WSxAobv4iaynCfC3wx+7sGcE\nS6jvNXNT9NY1cQVkJd3JBQnokkYMYlqKlYgl2pYEoBH64KT3sdpirI+Jl7PO\nRRLIyxhgX7sAQYMF32tuNjBq5zJ9Pr/WMeBqlXmeen/G9almwMUEzZwGrB0a\nYh5/\r\n=8Sr6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1", "@babel/traverse": "^7.10.1", "@babel/helper-optimise-call-expression": "^7.10.1", "@babel/helper-member-expression-to-functions": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.10.1_1590617293877_0.6065596644264584", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-replace-supers", "version": "7.10.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d585cd9388ea06e6031e4cd44b6713cbead9e6cf", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-sPxZfFXocEymYTdVK1UNmFPBN+Hv5mJkLPsYWwGBxZAxaWfFu+xqp7b6qWD0yjNuNL2VKc6L5M18tOXUP7NU0A==", "signatures": [{"sig": "MEYCIQD5owxV3n0Hqjb85ns5QXhU05DISy/68De2A6E9XHodIgIhALAb10KWC2t4Ohp+33WkvP7Ikx4V2Yy/a4IZOj19tBKk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zptCRA9TVsSAnZWagAAw2IP/jREkfLeEq6QpGzwW1St\nW1Xi/da3CQmfV/076ahy9KZKkyMyuo5/tU0pP2UCBlgpYu7N4JeNYeDr/jCX\n0wkfnmETOsBCFEPJI2p0djNOPN89xNuyj/YJBNMqcgWbrIkiB/tj5XA9hQmh\nrpogyyv+9TWs3f8RcpeXYgVHHT+fm4FOQRlMRnnvBhHqvRPIrbt57qtnYr0N\ndfk5TBT0Iph1vKau8jrotSilLDzWVE72IGlgmmCFCznNT8I7pGHzDXBhiIXT\nONTLFlrrsrm5AbR0KGLSvxx/49sjQMCK06KMPSYwt8qVic4rid/du7xHfJ0l\novkQIoz9fb3b7dKdLVrkX6T0ycVIvSvyjLJ5ficVngVXkTJ9DI75bSfepLdI\n69TOTW+aVIiANbGTzEJ3WNoq2Tw0J7ayRS0KvzQr9hnROmOK35qV2/AiJj1q\nR34jviI4BuJoYvsuXw67pcTn+wMf7OFQrJyLq4NxdydErzOkuMbMxMpp0Xl+\nhkzIQkz8a8bUjm9ULzWsnps/A3IDjAYp+YPCUw32Xe9zqhwHHT3UNXg1jJXP\noK0u3RBg0FV7dgQqD/iBActjYbGCZkN+5yuJtlDPmL2JKPeS53xc+3vqWTut\nMAHpPEC8WG6LV3DjKwG97+MSQbKzCD4aJJ0e4w5Q9VwcZSLavminOGYsEDyL\nN+ES\r\n=iNDb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper function to replace supers", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.10.4_1593522797430_0.8469946162291035", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/helper-replace-supers", "version": "7.12.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "98d3f3eb779752e59c7422ab387c9b444323be60", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.12.0.tgz", "fileCount": 4, "integrity": "sha512-9kycFdq2c9e7PXZOr2z/ZqTFF9OzFu287iFwYS+CiDVPuoTCfY8hoTsIqNQNetQjlqoRsRyJFrMG1uhGAR4EEw==", "signatures": [{"sig": "MEUCIGxFLVwVR3GizggDTt9mi7TOyleQ3EJdGHSm8+E06+9ZAiEAl35NXIJZJ2IB5n02nkl2qTGCB2seUVQKX4i+lZpiXg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mMCRA9TVsSAnZWagAA1bkP/0skoH+LUUmhFVgNSrSx\ntWlCwgzTJSMK3akb4oR3hl/mCVbm4EVLt6Im9iMfNtIDd5HqYa1AxcG8crd7\n7xyVR/5S+8iuBWFlwnWpJnt05nD0Pkt5UaP0rRjAuMxYhllDyEafPzCHZKjS\nvN/nfIdKXDC3ZABMQR6YybIwzODtAU6DzEZpDV3GiXAHmEx0s2RyvbAK/Ssp\n5az27eJz6Sn+Du6FM3nRMzD+iwHaYnSAa5/wP0WwjPWU4D7c345oUSuy+brb\nVn8XcO4iK4N096DAVsnJkilrS3zXYbusy3C0TCHU5ChhpJWnHNOM8DMS4DY5\nRecDMAdDu+a2udIo/KM0OcV/uNsyM6LxsdjY5HErIVKOlhsES/4Hy3d1aiV5\n0emDYLRH6F5YiS0q4ePWjlj/BIuWtI7wr8bJDG9eMFLD6iGZA3B5JXHkblts\ngnh5Pka29zrL1oqi3r0U6udKq3rer6j+ZU6EuODLz84753gBbiL1p1zLmVGt\nwGLs5SbaqFFzI/jW6G+DjmWZ+kit17XFAf87YiMitebeUWTLmlGu4S6s8F7m\nIPjtQbnX26meTJkkOxLBZbX6CcM0MIyLP3lPhNFJv1T/BoLPMQK2COiR2uv8\nZztLD831c5NeUYznxb/Va2E4+1xHVYxTgNwWDDQzLlTGizIL5u2vWTaVupzr\nWPkC\r\n=Rj03\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.12.0", "@babel/traverse": "^7.12.0", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.12.0_1602705804420_0.8204304692339683", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-replace-supers", "version": "7.12.1", "license": "MIT", "_id": "@babel/helper-replace-supers@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "f15c9cc897439281891e11d5ce12562ac0cf3fa9", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-zJjTvtNJnCFsCXVi5rUInstLd/EIVNmIKA1Q9ynESmMBWPWd+7sdR+G4/wdu+Mppfep0XLyG2m7EBPvjCeFyrw==", "signatures": [{"sig": "MEUCIQD2cj3FtihdwRUFzqzenaiM5jsFzzKYHP5Baa+ZweukFwIgPEWlLFxAs1YkEEMg+e/aDqPfnn5jWTFFQArqsaNPupk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAfCRA9TVsSAnZWagAAhwEP/1gZcME9ydJV8fr2MkT9\nTZQNzEODoe9GJC67dslsO/cAFHT9rDQ8YuQ0vN3hynx5BTc8e7DLDraAF0m4\nWo429ygQ1Vr7JoyDAMAu5+sw/3num3IAc/13MuuO0a+fxIzj4CtpAD5WNmu1\n3vQIHtwG54uV9D5bksrTTfLRFgNqrX6KIdxgTCQXriWHr+CdeABUa1ulsh2h\ng5KEf8h32Cr7fz/BoOkqZarIVEI0xoogXj2/AY1crDxUGsiEv4WxftR73HzH\nEabgMcvJgqmt7++WjIPKij/8mt/z64hugFuZ04JNxUhPz78rHjQ6ZbwzywCF\nhk81D2FldD3/En5hANr72S9T2QAOkRQswxgOkkYgl7LOVR60yfrersvL4brt\no3G4XG6KyVgnzg0EnpPEJI2NTW7JCkRJdza6oSUPP43wcRvo+vOZK3odV1DV\njldDV+i9fnghgXxjKmnUX4InmcHN7wvuOboXMr7Ln81v9xHktbIkiuZ+kBOU\n0pC0PO0/QlNuETWatQD3h2pbteKHj51DIpEo12Tp5dv56tMI+KLWTAqK5ABL\nsJGLEc5nrcmMTUaQrHLBXZ7Ai6SGvmUqNOe/lDXHpZLCOsLrJyysV4NM6yN+\n/deF0iQBdU7hrbxgz5XaNKJmzZh+4ayprrHaAVi1FJjDegMV4ucqUmPSsxRo\nWVKY\r\n=Eqm0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.12.1", "@babel/traverse": "^7.12.1", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.12.1_1602801695010_0.6880337238734877", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/helper-replace-supers", "version": "7.12.5", "license": "MIT", "_id": "@babel/helper-replace-supers@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "f009a17543bbbbce16b06206ae73b63d3fca68d9", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.12.5.tgz", "fileCount": 4, "integrity": "sha512-5YILoed0ZyIpF4gKcpZitEnXEJ9UoDRki1Ey6xz46rxOzfNMAhVIJMoune1hmPVxh40LRv1+oafz7UsWX+vyWA==", "signatures": [{"sig": "MEYCIQDuCDmPyByRlOhYYQPG9IuZXJC/aTLn2kZqTqS1fp7oowIhAJcNdyIz0W8nSRU10Ti+A9jzxNGfqg3Oje2rrT/szCIK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodsACRA9TVsSAnZWagAAavIP+QF8zw/kY56fkt05oUCS\nC4AKWOvCiGEnNfi/blwPTDwOIK4xU6JspX2xTvG6ZSsordWMYzTvw6y1tkdM\nnpNYwL3+uIUn+MrecDWZaMzZoCzZLEx1Vwvu2F8XhRbXH82bJFr5t4mxGmgb\nwuzrzk+CoT3T4/7NWW2JOQhl163qPJc6nyIKP+BN4I5szlCJwrDE2vnPVkvr\n7BAJba5541oL4iECTKMYXwOVKibzDn83FU3pYgtEsFEDPYGi27BcJwekH0II\nFtfK/QcI31cFbjBDbQKVArOrLw/7SZdBAg7B7XI1Q0jT6+h43yqIgDqplC8w\nZNCPSmwG/h6gxBAUKxW/wR1aFi/gsI7cAJ4Ek9yaimqgNJXVKHpaWoMqIVsp\niNZzmsZkMkae9CYJ3pE3WChVRTK/YXgsAQFsfUQJ3GV0V20hy8wHuQi8sRjm\nXZ8W+uSBSM5rhzaIDiSjghLIr34kDTgAHC08+S9SpAYsBlUngJV54AsLGeeS\nYpTIAbh4PXVeMmpR741E2uFLJU0gZg91TegUvuJqWYwotH7ate47sN8B1DtG\nOLATRxHkttVvPtogH3lnuS04raw2XIKqsJwdnv8GVN7UbEgpCDVB9QZulLEE\n1HW0YNWjp5Aznw1DeF/s8DgH8vGEqbfMn4eg1D9sOajxJi7Zp/6aVDFPZPYt\n+4i7\r\n=9f4I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.12.5", "@babel/traverse": "^7.12.5", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.12.5_1604442879881_0.4459506188942812", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/helper-replace-supers", "version": "7.12.11", "license": "MIT", "_id": "@babel/helper-replace-supers@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ea511658fc66c7908f923106dd88e08d1997d60d", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.12.11.tgz", "fileCount": 4, "integrity": "sha512-q+w1cqmhL7R0FNzth/PLLp2N+scXEK/L2AHbXUyydxp828F4FEa5WcVoqui9vFRiHDQErj9Zof8azP32uGVTRA==", "signatures": [{"sig": "MEUCIQCHzQMb7S5Ajai8422n5FPLfVofRK/Ckl2GHxyq/GF8JAIgWTr5Ntkn9uFXJAVhpa6GluydShFgot3Znnf8PfGX9LU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3jCRA9TVsSAnZWagAAJhMQAISqa+8y3m/wd7ztYcFb\nbLw6OdeNiWyk9bfZRZ8s40jZxrMu7KfuTzYiYQeWWKMiKjUtnHT8k1r1vROc\nrKNh4DhQqDxaXpWfgR/DVJ9VeW8HYJdOHJUzWZKw9Kz8Di8jixsEKkKu7CTk\ngWJ1aIC+Wks7h11ks1WKdyM4Mu4aU86B2XeiBH3VDs2WMr9mXU9DQe58Rki/\nIlH7wd1/2ieQEpY7LuQFlOLMDQWnr+8S22/fM0Bv2mFA+l9ycinp9Thwp4zh\n0g2QM/X6+uX9ZPSHiUx9HK3fZ+/q8oO+AtlfkFUewupfpeI8N1XgJ91dqXem\nJ6M/Ixd68k1afoPypcuHyXA/X3RyKntBjuxJonRA6lKWxVsyl++Oyl+UZ2IW\n/0dlYI3XQji4L2MWXdH5lKx8TsIvHVqpy56LrWMR62HBTrMDXVz3Wsx9ZOi6\nvBUSFt84QjPCn3WcIan2cOhikLIDr60Ad/LBWnYJlXALfRBfov0MZCH1C82N\n6RpoluZI41w/UnpbNJcIK5NpXpInWw/aGNnFBmrZztFXYViAeIrSq0WN4VYo\nt67urpCsNr6z8qotbT84iKfP/R/8kwjk+v7R41ZPG1HoR1eFEdb5++5OsFll\nNnoS2wTF5phdlJq6jlTbEanPyG+VsyjPaND5RTcQ38pTMCtBnLrk4MjOcqcM\nMgU/\r\n=rn+J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.12.11", "@babel/traverse": "^7.12.10", "@babel/helper-optimise-call-expression": "^7.12.10", "@babel/helper-member-expression-to-functions": "^7.12.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.12.11_1608076770954_0.18876286161354172", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-replace-supers", "version": "7.12.13", "license": "MIT", "_id": "@babel/helper-replace-supers@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "00ec4fb6862546bd3d0aff9aac56074277173121", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-pctAOIAMVStI2TMLhozPKbf5yTEXc0OJa0eENheb4w09SrgOWEs+P4nTOZYJQCqs8JlErGLDPDJTiGIp3ygbLg==", "signatures": [{"sig": "MEYCIQDliNwpVZu//MNNKnzml2mM7zk5hAIz0S7aGHeG55x5ggIhAPtHK0uJDaTUUeqez12oUmqNxAKRQT7snLobXPC4yvTl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhZCRA9TVsSAnZWagAAGd0P/jFva+rNRxgU37bfDlsN\n9RherI6lfEIK5R2yPZbP7GVrArCDlAR4XwRw9q58qiWSbA2Jhrcj6zkWqzTA\nigLuXGFBtg/B3113ezQb1PRJJ7ymX88s8Ub4D9J+LrphaiMj6AIzCoUB79NJ\nWMZP5USpXSz+fQR3NhJplBv1yclAZVaXq4s2sVzcljAam1cP5p95gcDAjLet\nwBHORT1oSSYQ4D0mUezf24I2i5VMW9AQneWpr1BH5VmC6wL0LjDwoB+lDztA\nQ1NyQllARrUwSxQTqrEyaaWJBuexvJDw1LwQOYg6sTQ5e5U9XHaOl4BmKXjb\nhxhPuJWun1NEf3qyVS+l9Gm+g/J82YCje0fNTpXTKia0XBqIDaygwOY4LyJj\njnbySrGjGlNTu2Qj2NIvNquUWNgXQKsIg4rscbTEPGSZkj0BrbzjRHMld62a\nxYOkTsEZuVmaouwJMCFHFaPpnxZd5lCH5uE/GQYX29RzCjtilgdAMTDmqcpJ\nvw1Ax1Pk5SEifDjx4ivxAkZqrYrJUP6atMEI/gJOV9iGXPvTB44ce47mudTy\n6sSHNGqlVSlCzF2dC1k1PIR5uC0UY1W8Lj/PO0O2yuQIBhesDGZ4MnSBr0q9\nwo5bKl7F5XtHboCg6xcu5kFSMa+uYPM/E245u4+HlWbX2wWBqdpcTiRsgR7x\n19JL\r\n=Rxsv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.12.13", "@babel/traverse": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.12.13_1612314712987_0.3231753149288248", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-replace-supers", "version": "7.13.0", "license": "MIT", "_id": "@babel/helper-replace-supers@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "6034b7b51943094cb41627848cb219cb02be1d24", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-Segd5me1+Pz+rmN/NFBOplMbZG3SqRJOBlY+mA0SxAv6rjj7zJqr1AVr3SfzUVTLCv7ZLU5FycOM/SBGuLPbZw==", "signatures": [{"sig": "MEQCIFktNEverfBbuO5wnPiK18C4cvpfjFu9lu0FDrmDa9BUAiAykDs+ZYbq8c04ofzt1hwt03vQGd3vuC79FDsdfvmakQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUmCRA9TVsSAnZWagAAo9MP/ieIBZZVOYDeBbQI0wfb\np66ioaEQlsGCv6ftSw2lTb/2H+KEpCuNWfdPduTNTi4gO0B4rYTj66Mgod72\nJHXybdASYc1vrdHoURt4QvAWR95g82UxS6sVMS8tERXjGlKWmlNAQyo/u/oQ\nuJPG4+EqKUt0nOreUdwBifoWuILCiYXro/D/EtJaEbcPelMkKgFWBU2+3Ksi\n8dTJ70OlzRVVzoMLGxHQ4+kUm1sd2jgvAA52z9yUn/Ox7+sRnC3tL0oejkvK\nZK94W5HZYm6L7kNPvMtSMvq9W+uxy9Qp9mbAANxI+JLuE+Sd3VR8HoUnkva6\nsC/vAsrI5l3CCW9yUDffgEjSNDNchaDYtAYe1hb4P9k9ML5GQLwB3QyK9gHv\nzVw9TNdDAwNp9RQH+I7c6HvaUXV4m49bpALe6kQf3FNzKCoflZwk1+RjtBPQ\nHMFdOUXIFFsFohqgdQ7VT7oLTAiErtVpdvKlv3OgOzAKqJU6NS9NPMIEz+Z5\nZzig+QfWJSEkr2Ld2p+RFH7M0dXuEpYMseDbV/9e+cvDYwY7aLJyrzC3HDHc\n0Wu3xSVoieWBeRBJys/XvkKXKRBo/WfpFlZoySO4ExERGe+TLE0yrz4ZEDVk\n3criKcswHCvHuzOV/Yll6KVx6s9rVAgqkWWn6xx2lD1FIvJAsf77IUuZ8YN2\nxKM7\r\n=cci0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.13.0", "@babel/traverse": "^7.13.0", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.13.0_1614034213703_0.7862382147789859", "host": "s3://npm-registry-packages"}}, "7.13.12": {"name": "@babel/helper-replace-supers", "version": "7.13.12", "license": "MIT", "_id": "@babel/helper-replace-supers@7.13.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "6442f4c1ad912502481a564a7386de0c77ff3804", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.13.12.tgz", "fileCount": 4, "integrity": "sha512-Gz1eiX+4yDO8mT+heB94aLVNCL+rbuT2xy4YfyNqu8F+OI6vMvJK891qGBTqL9Uc8wxEvRW92Id6G7sDen3fFw==", "signatures": [{"sig": "MEYCIQDIsuhYco4l/BFEAMGOWlnbSy6DRb5eovkhZQvoU8HtpgIhANYQNQD1E6+6HiK3MCNiBkgqu6xULCcUbMC1xqc/uhM0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLwICRA9TVsSAnZWagAA2CQP/0tAg4lOqKU4Txr4ETvJ\ni6PqSNlErYWrASnOFa9SleRGASucch04aDIlMa6T7ch4CIWYKJuXbXu1jOP0\n9Y7gZyoYXuriW3OHMlzsiuV35vtvrdXJCT4ZOd8TfG0fnhkQ05xOMcql6Esd\naRKy3s62d55DpxdbrIxJo5YsRFdCt4E9T29BkdtBQDTC9X9lni+WcFz1yVJX\nUDtcQDk1l7oXuvB6XBm29Wi8WlQce+TCXhWvV3f84cbtom3/lvfUCTqLiOBx\nJWnBZgqDlgb9dl6J+xOWfEx0N6DpDKfHa78tASGbshu2CmfZhx3aC1T3Vr5x\nMiNH2GWKfhXN4xBqoBZLj9kw+kRxqUbpAiqIX2SNNi34EeXTr83QidwWoPwI\nmnlWsdESyUrsei0p1fVb88miGQ5bsr0WC1uAYAOQubirOp8iji8FzYWQkPT8\nKWCFBsn/yhM5CZ/TSdgP6VeKgvOmWuydbt9a6U1HC2wq6laIOYQ1gp7oReiX\nT41iq3FMbM4Fovf1gMIqrEHirEi8UZR2rmgcj3H7KvOjmTibQzmN+Hq7+1EO\n7a4CLEzbIqmSDZyJwW7OzWnAqO729IE1mMRm+EO5ljvCA5uIyMh0Typ4/jR4\nplES80GdJ2Gx2b7ZoOXUsyApWDtNHHsvahR3vQU0vHBbtXXMovCM3z7opH3t\nllJ6\r\n=GR5J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.13.12", "@babel/traverse": "^7.13.0", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.13.12_1616428039691_0.03355341838588166", "host": "s3://npm-registry-packages"}}, "7.14.3": {"name": "@babel/helper-replace-supers", "version": "7.14.3", "license": "MIT", "_id": "@babel/helper-replace-supers@7.14.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "ca17b318b859d107f0e9b722d58cf12d94436600", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.14.3.tgz", "fileCount": 4, "integrity": "sha512-Rlh8qEWZSTfdz+tgNV/N4gz1a0TMNwCUcENhMjHTHKp3LseYH5Jha0NSlyTQWMnjbYcwFt+bqAMqSLHVXkQ6UA==", "signatures": [{"sig": "MEUCIGIdS5IJqOBKXQt8sRyqDCcSVHpVZnVyNdtwqqHaKAzMAiEAwzP9+SoLx1motBgmijKKo6YwBBbYV/8uNzdTUVkNqtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotWmCRA9TVsSAnZWagAAZ/UQAIFNWwtqjd3KAis7rNAY\n1WIZaKX4fW2iYU4qdV5MSVU6Zl8rbqnkQHE+ojpdnQgB22LIsaF9hEYEV3DS\njVCc442iBNrKC0mOSn3jXhsY1fqZYT5h0lIEHtHDBoPgXRljQkXMhR+QIo7e\nSgP88zeRo6YVILJXT4rxuU3NqQh1J/vow+cwZ0TvktcPAXosvsKuKX+AsgIF\ns1TYLwqCyM8t2+g+zKeoNXksTUEaG8NTEQM1iOOXJZ43E4HsSnDidVQ/YEfG\nzoAEIwFK4EupWlqk9g0OSgJrJ/YgFW9OpkCAoIy7b4RCOfo8PedXbxImbQN3\nKPkmyBk/WyS7YiCirU+hv4M8Q/T4m7qkmDNjmgz1D9U1kDU0j14AhTtAcgKo\ntO2GTzr3KycBY5x3XlHXQYlB3tcFqMZuLLZnD1bhciMD8EsVzw8kpGsu11Sg\nudqYIK1FEe6yyN56X8VP2VOBzEGCmZvR0myVtyK1gkjsSeb/1CPYuqDbuT8X\n/eYRcmRhy4Ks8LJIs5u5zY5OVeN8iDxB8FV6CvU1K/hvPgeaoCAPZSbnyhm6\n9bpD3dl/MoGwYA/45RCJG8psbL2ATEVVFEEMXA6ksOio+H+iyMqd0Itkr5mY\n7PqQIvEXgvx+B9gPqPhW24SyHj/RImGkVpmEOAdKdPyi6ZaFhXg3zVG/5Ljb\no5ga\r\n=RKAh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.14.2", "@babel/traverse": "^7.14.2", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.14.3_1621284261652_0.3473367323064078", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/helper-replace-supers", "version": "7.14.4", "license": "MIT", "_id": "@babel/helper-replace-supers@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "b2ab16875deecfff3ddfcd539bc315f72998d836", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.14.4.tgz", "fileCount": 4, "integrity": "sha512-zZ7uHCWlxfEAAOVDYQpEf/uyi1dmeC7fX4nCf2iz9drnCwi1zvwXL3HwWWNXUQEJ1k23yVn3VbddiI9iJEXaTQ==", "signatures": [{"sig": "MEUCIGWgNMSL8yIvFsNsh1qDHWF1lazzN2yUjud/0MgaTp9tAiEAmM1A3mbmLsP9U0pBq5+/sBrWVHb9dh0reIfhrLTMXjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGRCRA9TVsSAnZWagAABuYP/3qRebGsT5o5zpzoCUD6\nOByf7IIrxLjrkjNSgHMt6IQRLHsLmZxLCqUmxYmhJuxf7g7+PSbVZHY5F9hE\nixsPSDBK/JLrcLNgErYl/0SpgpOgm0HLrn6HaIH91SLbVFwn9f+9xuP3yXa6\nPlcuk7rUmdLVUYkTDB3mcRpaJujj7l289EtPMwrqnHNrqCBtF4g0r2nGLtWi\n8ORypuea6mJfLGj5jz9eokjpyekrJT+VwNY2EDXnyQEhxj2c+7WfbS3oca13\nEWdKLawmcU+0bsZC1hdTheUpI8PJBU0lbz5G2rWFpGKqmuZVm+sxzeA8g6s1\nIiQB+J4z99UHyPArbsANSNXIowjj+mGYiRvvTZWWSXPumwACdUdajMFmIZ5o\nVe3osYkKytZ8TokPKVX/Dp85dKQ6/KrR2KlOysqPW4lgJdwWxq+1uP/5G349\nsJfK2YGQBtGieRxU7t1LVS49x+thavJ1bLNBtRXf5i7gfh9j2AWqYqTmDBzb\ngVBB3CwRjU7Y2Y83dLFe84KOLUY4cWj7+7bCSpAb8z8iQyM23GJ5Hqvr8ptt\naBpWMGXDrq2uoQX2kPKotAa9GNBq/cGrHZKXl8UjcKZaNSg4bOPs4v8vqFDW\nH33xUc62qfzWdOkG7GfEhbZW94XWDQFfBBDLjAFEq1YJgjTrfEm1tsU7RALQ\n+ilH\r\n=l6kR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.14.4", "@babel/traverse": "^7.14.2", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.14.4_1622221200640_0.4581938999740127", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-replace-supers", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "0ecc0b03c41cd567b4024ea016134c28414abb94", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-3i1Qe9/8x/hCHINujn+iuHy+mMRLoc77b2nI9TB0zjH1hvn9qGlXjWlggdwUcju36PkPCy/lpM7LLUdcTyH4Ow==", "signatures": [{"sig": "MEQCIB6eeUTHPi7U17EDT5eRcAdEZSVNB27UIWha6DfPeGrNAiBLo+miJJ5z0H0Qw0OtQlgRUO+eX+isL6Qz6QksWiqGoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsBCRA9TVsSAnZWagAAKFIP/i79n5cp47zLeQJk+h2I\n/lAqb1p20GSALU0OYDOQoLHVhncDCJRRpx8HS/ty29gyqC2zRIUbUVJpkfKZ\nf5g9RDzB3gA2wvRKev0GAus25RceINVecH3n2FL09lqVXnJQPwMuJmH7IhWA\n81UBTXE0usTER4lIBwLN5zrKmSjg/cnhrTpW/jr9WdqnLBVzx2QiTOJT9sxl\ng/neePd/H6BBCIkfV52YOvOxc0rFkCS8kk4JTdkxolwD59gB3CLvZC7TXd7T\n3VFCdHs4FwxX2WEhiwnexFCcPeLqNhO+sDd6KY1iQ7IYbn+ZzZwrJfoVQqWO\nmsO3i9+KVbfLRzvLlYzuoYaBJ+P910DTUjg6X5ISozMa+67xfv8Rmh8nCD7y\nFoumLwgihCHRpeVITSEykOrDL2hcI8UnPFdsAt8fm3PZADqO7bl0fc04YulN\nQydrFR8z8DQs4wz9D5ucPIWY0z/MqdbSuCxm3winV6XRcblXEJ6dV/nGIwV7\nHAXpZRVHEJfcK/23CgbwLq3B0QOS7q7VVJdhnsPiLKxejMpGlkgJwcgR86mo\n1phkOsYLv3FZ89/bchgmzqHrGHaaqHi44pUBVT6mccEVXJEsggWW2Z19YBCZ\n1mUXuy4NQ89J1VlT1NOXh5wLB76tnGZSFui1B7Iwm3fe9wtXezYPVzJag/Vf\nPYpb\r\n=v/2u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.14.5_1623280385501_0.6541134042123065", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/helper-replace-supers", "version": "7.15.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "ace07708f5bf746bf2e6ba99572cce79b5d4e7f4", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.15.0.tgz", "fileCount": 4, "integrity": "sha512-6O+eWrhx+HEra/uJnifCwhwMd6Bp5+ZfZeJwbqUTuqkhIT6YcRhiZCOOFChRypOIe0cV46kFrRBlm+t5vHCEaA==", "signatures": [{"sig": "MEUCIQCaBGkR9SZKKFHSJvmSYwgdNAkeF1aPasnFUAhBPqGxogIgb/D9gy6m638rQMoNw1jza8NGlAxFK7LgzCC0nRi1F+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLlCRA9TVsSAnZWagAAXqIP/AiNS3QEQeKr29ORzAUB\nck5m3U22dlGdhW3GBeRRdC/fcttxUyB9sm20ZQNEOxPoNr4viUSiW5YqPt/k\njDgb38Iq3efA+oKl0llw8N9DO/usRFEm+e2QBtejNYBXCPW4r8W1EoPfhLxv\nM+v5J/1U4G6BjB+SJE1TkAyGm4wINnm6N7loKaoTHcAKk/vh/5QvGbfMrrNA\npqYUAF/0nx+uNkFdnQ632OQsNESRwaCZcOmtJ6Gb5haP1W1F24Ms7v0TDFiX\nB+EUF38Gy4aqMNSd+QKAF3cZYAJfDkMX25UJyZwSV1HCnvnyLpsBbH5604a7\nJ2FflnpudVk3BAj8I0UOhsI5Jyhyh20/N7vnKnX6JWT2GdIoPMSl7tGVzsQ7\nHkQsvj4Cb0J3cihGM1hYyeY5f8rSh9lwROa+oFbEyy4oh20nvBVQZOY7tqBz\n4SbBaff3+K8PzSX/R24cxR10a0iSa80OL5ey1iNdBISTkhc9X8usH0MUvDrl\nreiBsapIHG+VvVZyw/B3WaM8nmVmQanHO4rqBX6NFrhK5R9LA+er3aOpETLc\nDyPzJ6kAqpckvEH1Fxc4ZsuuTKpxI5P1/UTjwUGG4O6DMtsi1V0BKIj3Pfal\nCZxf2cuYcAwjhHWEssfzslab1tVcAsINAayFXNy8WMHKKmd4F5kGTktRUcoJ\nvmIE\r\n=h+Ng\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.15.0", "@babel/traverse": "^7.15.0", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.15.0_1628111589364_0.9082506098237519", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-replace-supers", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "52a8ab26ba918c7f6dee28628b07071ac7b7347a", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-/ztT6khaXF37MS47fufrKvIsiQkx1LBRvSJNzRqmbyeZnTwU9qBxXYLaaT/6KaxfKhjs2Wy8kG8ZdsFUuWBjzw==", "signatures": [{"sig": "MEQCIGRvubyHjS+ij7AxiuOdqDdTvdR6nBepb5WkOoNUWmSfAiAeWNLSejpQOVattU9kwPmNAPvpVG3n1JwuVssIOXNvyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSlCRA9TVsSAnZWagAAddwP/jVt7vXh4UM5T7m8i1hT\n7SWePF/vZS+TqA+vyQ7yV/wNdjD3gwhV9w1wMCCqwWctsArXuTnBiqAyoHCF\nhUiXgxb268oUWev6SUSLcZbIGWyWKkRU0G8zQ1bMgvOPBApIv3uQZWha0YvZ\ndFDs/VG4dAGzCx0EANgjbVnk37DHlBlrCavwHoFN1ppcJ+2NNnfMXcnujEC6\nzzUHUO+eUxzTt2iVZ7VQ4gtL6rkYCgGNVzT2+vBddD2yXlbJRp34Q9vP6U/g\ny/NWWx2OlWj96ocWUs3G/YxSEh+RVDxb0VhUnPJbwXWDa6nP6Rxh5zs8gdPg\nIJhmCN1K7fXJZ7ZhPvtr4krzcvCq3fV2xrWmjTWRZheqaLB1Y0JcThYoWr52\ntQ4HPTPh/S27pWGvNYbs+VZkZsBYo/jdWUIFloI4CEVT9q2CgnAHNc3fbNn3\n6liiHjvhmLzNkDSHXTX/4fh/rxTDF7MEjHURTdC1P3jkzbmneiyz6CwwPPqn\nmzP3MdcQrO7jwbPM8Uh8OH7AA0bO7NugjmKvYvSbKwc+cQi2gFuLhE5810yA\nPOpGpaT3IG7OfYmtE8q0svePOg1Zlb332ok4abLMRbUFHcy+NH5JJzWPJ9F9\ncK/nUYnX+9xlvgvmbtVy5CudcUqBAZ+95SgTjx0Un5/eJV/F9xWZ9224VKDp\nQPC8\r\n=8Tlg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-member-expression-to-functions": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.15.4_1630618788951_0.11053839775384455", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-replace-supers", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "73055e8d3cf9bcba8ddb55cad93fedc860f68f17", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-TQxuQfSCdoha7cpRNJvfaYxxxzmbxXw/+6cS7V02eeDYyhxderSoMVALvwupA54/pZcOTtVeJ0xccp1nGWladA==", "signatures": [{"sig": "MEUCIQDERTjreeYTSlHi9gpN4slznJzJf8n9HWB4G/40N4UWEwIgBj/GziwVtSJHVOAkgI1SJ6dUNKpgXJmhb/nrxrlVAtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9862}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-optimise-call-expression": "^7.16.0", "@babel/helper-member-expression-to-functions": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.16.0_1635551273519_0.8301334899784152", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/helper-replace-supers", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "96d3988bd0ab0a2d22c88c6198c3d3234ca25326", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-ao3seGVa/FZCMCCNDuBcqnBFSbdr8N2EW35mzojx3TwfIbdPmNK+JV6+2d5bR0Z71W5ocLnQp9en/cTF7pBJiQ==", "signatures": [{"sig": "MEUCIQCwkqfBpwpxoNj7ApePmhTdT0kU58HLTwpKilOOLNHPugIgazkEo8ffBgiGcawV9wx3i8cq8EMYPQm4Kmwr3tnMPvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kXCRA9TVsSAnZWagAAcvcQAJaY8MB3eXP4D/3eBsGn\n3yuUqVOS5Y6Cmc2IE1O4Ueg9OMI7yF5BvdAu8ZSh9AMSFOLY1JyEBx/zlVs+\nUkgCMT07kPWqENMxQaxq6GPzRrDqJYbZX7PV8r1KcUisAVOiDozRDWjMLx+D\nmWVmTnk5sARcAneAHKouhlOFasg/PhGfsE0KZt4xcBFUJwePjpleshBPiPGv\nudk5m0I6XuGJi+t4xld7CBw3LoB4MrMfCOjZ8mK1VEsQoPYvKgCaeR1t8X6B\nf1GZ4jxwq5Sp7JZMy2q11L8TULadCN/n0zB3Fd/svkeHz8alF4SXHEnDggWK\nYa+ml1ZIBLNMZtiQHXaNOwp2T2evJuADfS6JMxwsFP+5/MJPFdjY2z1HEuFP\ncVFlpbDjQWzwGBu476Upau+prLhQNajc/FK9l8bbiTmHg/BKymgHMelkN7Su\nS6IW5x2UZvVtqwnrmRFpfPhbCviMd7sKsV9auoTtItBnGEdbjNK8tYXJ0hWz\n5g+K90kYghhczUY/u8AvBT0v8z4m9x78ko6rWPCa0qDNHHoas9a8Jv0G2CMo\n+a5C3fi6Vd3gjL1DodgNSKhmdZRmVq9Uxf/pahWWrC/U386YRgklFj0Hiwr3\nBZCXqsikOULsRKkSSH/8UjLzPFbetYb6Rt+W+IvKu7FEDXgR5g1mASYJkmGX\nkNqT\r\n=pQKg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/traverse": "^7.16.5", "@babel/helper-environment-visitor": "^7.16.5", "@babel/helper-optimise-call-expression": "^7.16.0", "@babel/helper-member-expression-to-functions": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.16.5_1639434519124_0.18992704209581213", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-replace-supers", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "e9f5f5f32ac90429c1a4bdec0f231ef0c2838ab1", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-y9vsWilTNaVnVh6xiJfABzsNpgDPKev9HnAgz6Gb1p6UUwf9NepdlsV7VXGCftJM+jqD5f7JIEubcpLjZj5dBw==", "signatures": [{"sig": "MEUCIBp1ljaoO9z6kKv5L/sf3R2RJ0ZJT0g5mE6dbOnn/KrcAiEAzI8BqgEKaNmYqDvD4U90iw54pSNi4GIe3FTgEEXdCtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1hCRA9TVsSAnZWagAAeYoQAJ3tjmMCSLwXIDhTEP44\n3xLXINVSc2857C38T0Nwdizj7BwXYXtCeGYuZV5Alrexfe+DlehYGZsx2fFG\nvmPGeIshMcQ0/sruOLvhbE/wi1435oz2Y9Gl+PK4xXLMvuTX8y5gU2fqYOog\nF4cOoV+wtdiJFlq5c0cnRJVnZofM3tIW1hu9rssl7JuVvH6POogaQfWWDzra\niJbTvZxiSjvah+FTTMJCdCRSMGH9M1M2TwPs/C9Pg37GigSktCsJxW7envTU\nNZpP/qPbk8gGEZwr6s2n7ycZNIa2RlorwMHxTUUpwp28SH4eBRb8POg9g/82\nMwyWJi7w7i8NzvCQh3aUE3ycXJx0JanfQKmn+gggGc0RQQPmcihfHaJPPyRY\nGvjHUGUaWd2YeBzU39vvuD0FY6dLxrs6NYMv2PX3FLGoTeYdWL22uZHBl1+w\n6m2WfgD1BdLpouit+rItmsoD+7C7JQM6P7C8F5k+oaZ1mts5M6yP9FPjX898\nmnPeJzxvN1FccASC9fk9nCxMME+6S9ItiK9DAC0hmrxm4407G9vRPRbXY9dK\nHwzjzlO/po6h/vigIwDx60dhLSwIlXvtT9uMLJmt2DBzcgwzQVkoy5WuAKtV\nR5+eGupxBcdbPIpVbs9GzcH3Kn+3wBBDCwHyC+abJDXtTB4ubzkCDcuWU6nG\nOyh2\r\n=SgyQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.16.7_1640910176872_0.0602374365512901", "host": "s3://npm-registry-packages"}}, "7.18.2": {"name": "@babel/helper-replace-supers", "version": "7.18.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.18.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "41fdfcc9abaf900e18ba6e5931816d9062a7b2e0", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.18.2.tgz", "fileCount": 4, "integrity": "sha512-XzAIyxx+vFnrOxiQrToSUOzUOn0e1J2Li40ntddek1Y69AXUTXoDJ40/D5RdjFu7s7qHiaeoTiempZcbuVXh2Q==", "signatures": [{"sig": "MEYCIQCEvxTgHt3D7NLuOXDNQ8rcsBPirB2PIOqIqjp9+7r9kAIhAPYPAmNT7Ljk2M2XQQUZzua924R+K9HOPCPfu7ZZcErA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijfPzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxNg/+PZqP5zeDRVDDMKisrDqgIkiazpmmhgg76Y5SrhNJc4Tb3E0i\r\n4EoeJOO2f12CKquujRWMIHIDvR2GdhXlI5Lyw4/DFYegt/3aXuiERq4yNyKc\r\ngM4H1DqZhz0SXkOTixK3N+NoyyhWbhn1iq1NlNkWkZbrWID1RQlmt/gYKzFC\r\n+7xfwM+4Fl50G2mSUQp81ivfJ7wrBxMBcMZ7YNoLF/7e2tUbjFfZbx2K+qjZ\r\nIQwpao2S1NItEAioM8pypA8LvPCP64RhnEz4qTgqQyvTg0+TkPoRPwVTPTN1\r\nGCS9apZM8mZN39q4u7eeuqDkx3iSgnXn9mgY9vUHl9c9mUeuW6MNgy0ws3T9\r\ns4nv0dl8VhPc8PPCY+PvELyf/ZnOVxrb7eeFS+6sOSX+SP4Sg/BWSxCxAyRu\r\nal8Jy6mxP5ALRCXAtY6ND3+p2Lmam21L0wqhozR7hpekMDKH114UythbGV76\r\n4ACIG9ESuierMrcoKptzivgE8LPl8mJzDdKF5YFe3Vh7JvJ9tZvFFJlkFD0y\r\nSuu+RtGpD6kxgV0d62Vf71z8w6iKwzoYEOrqqYHwdMIcgYMbOY0FWxjMva/p\r\nAiTaIsWXYJyfYfzEbNpoDJV31+CUYcoz+/xL+IInUiS8GdmHKJWLalcaIVbR\r\n7XwVT8JITlrJtKPtusU9b0PpJTyN/heh0ik=\r\n=kyMG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.18.2", "@babel/traverse": "^7.18.2", "@babel/helper-environment-visitor": "^7.18.2", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.18.2_1653470195432_0.005600643583053122", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-replace-supers", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "efedf51cfccea7b7b8c0f00002ab317e7abfe420", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-fTf7zoXnUGl9gF25fXCWE26t7Tvtyn6H4hkLSYhATwJvw2uYxd3aoXplMSe0g9XbwK7bmxNes7+FGO0rB/xC0g==", "signatures": [{"sig": "MEUCIQD8JpEV7mDJX+8/E4z6M4g7NNwfyRaRxQ0xxAxClQ2/8AIgJPXq3R+AYKOBjkHc7OavbzqSOmAz2Va+b/rDhJvN2u4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgXg/5AdT7FsorNJRjctMlNB8zGk/6ROvPcGZr8S92bXEbEtNIBuip\r\nocNu806xvffSkw0HYqCj+Izf9J4tGyEdAIoBk6iixSHSWz6uKSHvr8zBofPI\r\n7GTdXK54vWGAiDNFPeoWbMAUfwZ9wFGLZf10or/jAkL4sYiuVylZ026UDiup\r\nPf0gg3/D/PfWVxM7Jup805Lrwfj2IHBNr+8AoeeClrI3CwcjbEGlhdiRQZzH\r\noZebn8noJzTsFqpae3y9X2MHilvFL8N4ETkC2LFJYTOaqnyOYP4hTj1N2D/i\r\nNTJBvI47Q31VdZWgHm2N+J1PBNeRbQwDJA3OV3pk7tu+0WtfGau1lj1nWPNE\r\nqgvcAiRGSRTblOG8uMCqQ2Gm/t9jxdzaSUvP0Nq6yXf03NzQE94UKxM5Hc72\r\nGTKUjKpVkIG6OnFYy4sgBCwllMW5dCW80wGohHnQ9d+cQx6hpqJKkRnvZ3W7\r\n2CYXMCIQRAW0+daT/HKBRuyMFge9jvQkgnhWlqoinLZLXki5OS5unsESoBHG\r\nhxCQ+m19Cjwexa3vng+G37ZaMaoxIaZmqHDrdHniTEs9K9o/8vwy1ljyaqX/\r\nfYdYXD1iLx8vQ10fIAXHk+XHkDlst1/z1AU2D4vWihyvURoemPlTRaIE/OYK\r\nFUbpwfFp7En9On40b/D3E9Z3Pel08500Bhs=\r\n=iGPR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.18.6_1656359433241_0.5713733319644674", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-replace-supers", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "1092e002feca980fbbb0bd4d51b74a65c6a500e6", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-dNsWibVI4lNT6HiuOIBr1oyxo40HvIVmbwPUm3XZ7wMh4k2WxrxTqZwSqw/eEmXDS9np0ey5M2bz9tBmO9c+YQ==", "signatures": [{"sig": "MEUCIQDEBHFpsxF/1glDjjc8PgrcghaneuPspNLbHlKZ6F7IFgIgTkiP04g7SPQrGZTTjSDyWdsLtimIzK/dw4cmsTdLMi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYkw/+J8HWuipPeb4JlABtF9rvBZSVQsvFMr1e/jodEjHduaAibFMA\r\niiHWE+NX3zV8y9c4KAEtOVtIq8eV3L7s2C9/0k+YeAbozMvC7mt0Odqmr9YK\r\n7S2nSgJIYozGKt4mpeeHxazOhWAzreMnnLEKH0/EOJnOuGZK0NBn6crJkgQT\r\nvbtZGL++KR+69tSf7stfRfQjxhgW9Xi3x0f8are4jCg10vWRVCuMwSOPszTm\r\nYbvonBoCzzZ6dCbc95NFUAvHjjzfyjVlkWsw55uQJaNcsKY27MHQDbDGezEo\r\nDCNx2ulb3RdLzYpnzlOxfXc7yKcVepCEiSN7Zftd2yzio2k7+X4hc12+WKIK\r\nJcVuWOUo5WWrvJx7vcZa2/O5+bHfferGjf3zq9PWtZUF5LdymgVIMO4VNwJb\r\nyFuhR8DnK4YDp3bNr8NiAq1rjyql+vwsuVeHTtfgm+np1O8kMcYhqTrZorve\r\n7Tn3zyrCb/9LRweV148FaliSWDy7qvWbrjhUITNCfeAmgXzpSifM5yOA2FZZ\r\nN/rWHmizoL87y6CbgCVm6q+h9xd7RVmqWSIbjP+nbnO0Y7Ck21tYy3/fNLS8\r\nsE6ZsoFj1IfZ6pokboJC70DgCD5HIjmNNdg1AWCYW7lpFSHE+mv1berDkRo9\r\nqCMARkX1AmF4jvGILVSt8LDiaNxfQLf/nx0=\r\n=Fnr8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.18.9", "@babel/traverse": "^7.18.9", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.18.9_1658135859917_0.5641348149837833", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/helper-replace-supers", "version": "7.19.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "e1592a9b4b368aa6bdb8784a711e0bcbf0612b78", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz", "fileCount": 5, "integrity": "sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw==", "signatures": [{"sig": "MEYCIQDeujZt4eWQk4ixNNt2mIFNVXLATbKhnHvMpUxzCFoM5wIhAKwxlCYyGZvIQN5DA+GJMeu9CTWb1VUOpzB/nEGnudzy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQ5BAAo+DbtmxjR7Y5z4IOPYWS2RC0Yx37w/xXKLvq5HicZ5hFIfaK\r\nK82s/TIOIst0HXV9ff93Lx46Jnv748JJJZtpvV/BqPOM6asczXoIjhKzPqf0\r\nSYArtPa2fRcvNgy+sagrg6JGy8/Ra+Qhmm0FEARUiWJUnnUnJqB5AVQYgvkT\r\n/bw101MIGSr6Epdi1mUx57s4ENTeVWmhRiasKCc5rmgqWRSGWDaO/9R2e5el\r\n8oArHXYePWMEaTG1HSy0STxZiQwluMLyt9fDewMcxfT3JB8Mfz2nwYOSfCls\r\nihHtpYr753OZpmANIDM/oIx5qGI3Qtwn8PWJBWMx7DwhkbqPxfuHiPWTQ0ZP\r\nFcRYzjyENMrKDEzaYElT98sUivqNxo2WzZFcy0g/HZkV0emDWAhmnCo6mtzP\r\nllwudj4uKxZITVP8epVBG4B+sOUidk8gOVrfXxf7ZE9kjlo+cCoARd+X5DaN\r\nxC9Wn4y0d4utGrCP80bRsasNWHScCstnh968aEo3hLnHHbNpz0sLO0SkgRUt\r\nghOoV9fTAsKjHS9j9Dacg9U4pfpzIMBc6VSTHrXQCEPyPS3iA28iify7D3kj\r\nsjlvyFHO1W9LEn7a5lUhlwbsQ62BZftXQE26yKyAvGwMpbgA8S1YTDaHWXB1\r\nMK7bbNUgON16EiXC1Py/91RrUj+CVud3jfk=\r\n=y3My\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.19.0", "@babel/traverse": "^7.19.1", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.19.1_1663169356158_0.49978641915383326", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/helper-replace-supers", "version": "7.20.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "243ecd2724d2071532b2c8ad2f0f9f083bcae331", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.20.7.tgz", "fileCount": 5, "integrity": "sha512-vujDMtB6LVfNW13jhlCrp48QNslK6JXi7lQG736HVbHz/mbf4Dc7tIRh1Xf5C0rF7BP8iiSxGMCmY6Ci1ven3A==", "signatures": [{"sig": "MEQCIF6ZDEg+xULn5vJjEQ0f+4ccXsht2UUMkqQUmKdHSHa4AiBcQDSJTApxH2bNp2bSOIobfcskTFSlaDJLudExl97n7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnlA/9EDw53zF9v+mRAD1lBLSO9rjwkdR6jxf/UY7lGX/2dNww4E4z\r\nKeEsaRpuSGL+fPWEm4cjsX/sSRm1MMp9TlHZ1F/0s49oUoP6i/EKJ/Ho78DQ\r\nugXR6Pt3IRrueZWLD6a1ClrjfXGvhx+V/qJ2zfsme8tXezk98HtnT/HR6rhj\r\nnpyxey99td/l8BkCP8ilqLnEODGBwUMCHZualFHlM319vq6leJPsH1cjwrSA\r\nx1j2lu+vrwQhCt3nQHoCFoOE6tAldr+8U8vWMR6oNP2fKNR8g2Wkunsv+CKy\r\nuWCX2wyQOoey5VHHKLBJ203zzqTSxMEIpSfrpXb+0kTPHA8LTokQTHuvWZFd\r\nV+EDblSepVnIW+mA3vq0ey5+mT4Ox9BMl8RIbELyD1DcRusQ/eBM434yWS7b\r\nU8GKnyYoCRA8qCrX0f9ib/cbPWkpe0x7mUMHBibKPmzinvwuynybfb5/zGcx\r\novNdxKt0CiARpsec84BPI/ZxtPUkH+dDbFWrNuh08XDKcEnAnvnCZ8rzM8a+\r\nNi7QEVDrynQMHCf3Bdbbq39pZZmzIjB5WyFrKVCxjlBOS5wKnojjdqYMdIHs\r\nBmJNDeiIBX6j+xm7wl3lviPNaAo4YwTvnun4aoxSTK1TbAgYtRf7hoUE91j/\r\nC1sqYtGqBv2TdBSwvFYVa43ECXb4vRJv0rA=\r\n=OZCf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/template": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.20.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.20.7_1671702336225_0.9987291129761426", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-replace-supers", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "a71db7e6ebff56ac7f2bfc0656e6fe7b0a01b644", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-a1wknHbjX8v0JhehqsREhcGp4H0gwqouuGcPazyrER6RmiwT118HoxjZNRTGxoSF4uo19P7g1QvFqo4L0WQNGw==", "signatures": [{"sig": "MEYCIQCIETQtV2pXybCMX6ihdPdCvGKFa3O/XfsXbmFJkUgj4gIhAIaLi7JlzHjMc/fulTW/ks9muNJmeqps9p0EprlLozyy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9kA//cArPHO0WHYibSzjQobRJqbq8d0D5DId3kGPYE/jy+TpOcCnd\r\nZro7rIN8+g0Hp4O7gi3E6TvWFXiPH+Ag6FLl/YB5pn+Ki2N9RbfgdajLbcRV\r\n3d5tHDv8WfWQDtoz4p/b98KmmI6J+YW6hHV0buFz0u+enupjOEi2c2C7ciAr\r\nXzCAKDgLGmykdS2RuLe7BR3MC2O//qpXWH1KFSukUadL//haAXvTWU5igYJY\r\ni0I7owfuyOLU8KDpIrdst/HC9PX5k6/ADT1snO1/SnrRRH+PdMwZ8kqwWeWQ\r\nlG2a5J3MWNWYlauVhf7blDXrrZOncqtk0lPXVJGveglCPWbK08q7MoNlKoZF\r\nSF/PY6Nn5oIJY0xu3BhGLIxZYqr+Vbm91nYLzjgAuCMZ2BHc9FYStyDgxtdV\r\nLibak8tuALEZbTjSXLXotkJcvm9jytbHpa0ZHQWfFfgula4J21Atq16C/Iom\r\nE+uWHAHenTLeZJ/bh+M93ezBobjTv6cRH2eyfdWac0p0NtRJ/brh/aOr98TK\r\nsXgXyybHk9xtATy5klkG9yMG3y7evvWHARYg3eAkZCmyn9JTNc+PEsGvi2ZA\r\nS+5INll0fSTzZDuOYBJAvaljFHyus49yNDIBWazRZwM2Lka4pKOf/MX6AC1J\r\nbdAsCiZPRHYzW5l5FOkwkec5U/eYKye2dMU=\r\n=iKwP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm", "@babel/template": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-environment-visitor": "^7.21.4-esm", "@babel/helper-optimise-call-expression": "^7.21.4-esm", "@babel/helper-member-expression-to-functions": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.21.4-esm_1680617394220_0.3877563858071995", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-replace-supers", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "8c83c5f7440398b2385caf2b050f6e5a1984f67d", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-X6GGkkuYU/tdb3JA8/5No6aGnm0cUqs9+rHTbNaFEQ2wFMfvrW1hCTfB48yVEPOUcrFt2OtIfq7Cpv/joUUiTg==", "signatures": [{"sig": "MEUCIBKZmpFgddMrbM6uCW3aA1a+74kztw3uZ1GdL5/+YMamAiEAs4VFUHjCfNyDIw/Ntfw9zXcLxWDFZRS4vjz7oDZK5jE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqXw/+JIRAT4w/vW4mWrAdng8N/Q+4k7WC6Sxcl5qohJcJbyUcs7R6\r\nUZVSB3rbRiNK0fU3iFoJGy4Si483c8TAAcjD9+zIoMr9v5WLLVINSe3RplWb\r\nl5ZsbZiyxl6fmaSmmJ6UY+ZwVYT0QXCz3uHVopPER53DFAUPzDGYQ2oCtVhT\r\nbSrjfbfXdlYjYkB0M9OEbfnFF29XlGMSVDqnj3fqSCRTr9GxpmaDr+g5Idhb\r\nJ7wLBxTWV0uBXzFtHDEBIFs5Ksc5QjJJmyYzNQJYLLafMRRUlKm9QYbyQnw9\r\nRcSl8OYVIVvkUee9VxXfr/8GvkfeMidke75b2PRo+uK09jklKY2ScokdoreO\r\nJiYuwEYXEF9BjX8+re28x6D4BysUA7YwD4W6PrqbaVGC0yBaBoxr/T4wC+Uw\r\n7KD0UoLiZ+0MYTEj61bRjF7IL25xXpf5/Unr1wT00iI2/X1zzGMrrM9PVsC8\r\nEZ4L9YEUM/C5kDJachwqUiG7o71r3jzoxoJ2Z9dIVzmKQqynuqoXZrTM4Ue+\r\npRvDNW0JA5B0jYI9YKarXjvPYMOqYyTaF/UmPj1NSZE0+IcbIpRTNAJYEdB3\r\nWE8+V+K2qWF3+36bTxI6eR7qhzCDSumwugo0QLMsuQH1oh7fyOktvpIAGifE\r\nHSFSufhZUdKb9rkmMOTSmHPDEyec7+FXCJs=\r\n=FuOs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1", "@babel/template": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-environment-visitor": "^7.21.4-esm.1", "@babel/helper-optimise-call-expression": "^7.21.4-esm.1", "@babel/helper-member-expression-to-functions": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.21.4-esm.1_1680618111781_0.6188474267214235", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-replace-supers", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "a963dcfdf4a144961edc41a5746fc40caa61d80f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-9kXssWXsnAPtuInmm3hlIUEq7F5oaE93SdGj3CdvJu8/+F/3PnHQqStWzGl1l2CJkEsonbbuANpTAoVvHB66ug==", "signatures": [{"sig": "MEUCIQCKsdlA3hpYkjjB/DYVW7JkXY7RaOjoAIccIof0caN1QQIgBQVYBx4L5RCbJPCGC0ksQ0qlCQZPgYftx8iOXiEUe0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHYA//Y9DZmxOKWtg/cFTHWNcNAzo9WRJjUAt36arwNVJ7Gqnke4of\r\n4XjSHOQdaU8Pk1x1YAJHgw0xrdGsCLlDL6OksO+fcvwEdNW53endEWnCT7cS\r\nzc3YJXEhI0XH8hZmuvOzSwiusTeuWhuu4LQFrFGHepw3muDZKDUbvGBij4/o\r\nsigMTNxfDNk1kWkXo0MCTu2z7Jl4JGRYZR8C1++ZY0kF/5Yamfd9kh/umEyi\r\nFw7w2msDtRQ6cL55uDQq8u3SmWERIpxsSw5uIsdNsiXsecptSJwTWEzTbWkG\r\ngXdv2fVMHLx7uDj4SzkgZv5e57ixQBB5jr8HcwwQrFnjExCHlmrJPNQ4qpEl\r\n7xshgGLODdLJ4eXnT2ulNPQVn4+OB2y7CAIsKJ2MDSFgHmJ7AsHurr8FWLMH\r\nwUMrW6NwLjBEIjuvmmVNQ8esbzTzhW/v+OVu8UrSnVcHTI3K9vMCq/seLU79\r\n32g50Zqp1/atfpJhWk1SmriOdZesIT1LYCXcnu0zKfsj4bmVQHweDZX8bvuN\r\nMlpdsO7iGIHJlk1ayrnf99eN8r0eDPrrgwd7OGIbDisK5ZkGwmY/W0BQmNOd\r\nN7aYrteNDxr17KDi1Vs/L6DZvOJWB9LYpJBujFNsiAs4OSv6vxkq1bt8QaRl\r\nTqXgLFD/oTrYW38FxedgU1IdJej2Jf6OGlQ=\r\n=qV3c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2", "@babel/template": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-environment-visitor": "7.21.4-esm.2", "@babel/helper-optimise-call-expression": "7.21.4-esm.2", "@babel/helper-member-expression-to-functions": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.21.4-esm.2_1680619196056_0.2389510852459038", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-replace-supers", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "9e697eebc9a5ba49dbf62249e3034fcbafaa02b5", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-NfTv1pka0LXelLIdzDkw+jdZ0PYoA6fCu8rC4dmkoyYXmzJm85epLSvfAAe4wXU2zGrjsgfgelMhPI9NKRU6BA==", "signatures": [{"sig": "MEUCIQCvZDncwNtiT9b7ckHIV/WaIb2n80lBuMY0yoOIceJhkAIgDZJtvUjQuL6XMFIOXjFxWmOfCTIUsZ6T7sMYK13dMOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLjRAAjVMXCd8s+PF7yg0nlYbfZTta8r/mCnBOg1lDTHBwvmtnnfGy\r\nb6GyO7EbSHN/i7TXhyfNc4mKnaLZr5LwDzx1QFxqQg4WL6uPyE7eeJNjGYQS\r\nvFDi9ur8ZuqqGrb5Gqj4EBQl1XYZKhp6Y3PGgTMajPFgQ9z0uaiX0JUUbmsZ\r\nKKFHuO26KhEVwdQR6dUpdfLFy0N5KPCe6I1R8fgt8l5fBySVj42PAnG5HyXe\r\n8a2A1FknB5DhMSr+6up6ck3Q8eVs/dtOoHnLcpKqD/yabPWwGH0iRRYHyjo+\r\nvyceNhCi7PdfFoghwMn5l/hpG2CQGG9xMMlf6670amjYj8FEMOaTWE0e9TQm\r\nIkF951rVc1IK7QEFfpjEz081v7/Yx3ydBb3hTEXXvo/TjLxG1V3mE5T96nFK\r\nkiOCFmHkAPbd8IHhNE05/E7FONjWy2lNF1ehXphbA2MHnrngnPvgFvCb0CjP\r\n1YJs+GViFq6jfqXDqUVmFiNzKKpjaj6H8ZNc0crqzrnR2qckMkMorGuslLsQ\r\nJaUoh3ZnYitDgcUKhpVXmVKCmCGnaAF2m3lO8nm6b22QBEM1EodeSBdI4GSd\r\njZ6h0K8MRKgaFGJcm7CI6N+oLL925EYFPKzcL0RZs5uYXVgiog5Sbh8WfKy/\r\nCCW8iLFLhhUGJ9ngMEhhzT3MISPPiUFu6YY=\r\n=t2TP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3", "@babel/template": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-environment-visitor": "7.21.4-esm.3", "@babel/helper-optimise-call-expression": "7.21.4-esm.3", "@babel/helper-member-expression-to-functions": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.21.4-esm.3_1680620198854_0.34916195833938657", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-replace-supers", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "4debb0002adfccf89d5fe28996b6b031d6fa4fa2", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-pKowr5jG6NUAnZdyRwbBCLn0loS6lTwLoF4OccatlWpUZ2Rk4LgqctigVI/Lx4oHY6cBVM/QTYlMBrdeW2IuNQ==", "signatures": [{"sig": "MEYCIQDJMq0pnsSrQ6Zhc9eAgzB23BTfNTkVX611B4hFnkD2MwIhAKeWQvHZ6MPtmoMjqHGdOPWw6eLBYG3IWnvNv/xamzlW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8ag//aW4Ig4HxEpb7TcEAS57lCQRZOI1ryj1htgtrZ13Yn8UtU9Qd\r\nCHH0HuPCv7zk0ZMwG4z3pXvatFQarg3M14cBjRMzhty3ugYzfz7TmEZmNqXt\r\n9UUUa0f9JOMIRITjYMDQq9h3fzmniaMM9ZIz/vkx0JZ6Y5/2MLZaeJLnhWxN\r\naygWQCR69pSTORd7j8rjwkrWSpA5LKAZbaw6GWwS9bdpody+UGhKDTVHRf8E\r\nJEuD/BDSxd+6T95oU9TtdjSsusub5gxWnrjpsoc50u9WECV1O8Pl/uxfLKI3\r\nBgKEhRqWp3Ls8l6/ykipGph7QbU27pSNs/aSAz+X1DZFjP74xWsOorpOoGqg\r\nPBSmdp90wdOlVsQVyRCl+Nzcs3TRiIpyikMXgITM8HQFdZosgNUEJ06ouuq0\r\nHCySm0UVnri5gn0H3vRRUv9WIsMeBxr46FkrsZR4y7mIq1QZzh8T3ys1S7Pt\r\nM1gtrxD9MmVwEKYLOv3gcMcr+IurPL1bX+7IX9JW2EN9tPbP+BpjFPXaEUMX\r\nsNyjCGSTaE/XhKvUQQoNwBo82iXaHu5tBtE3btx89HvggZlWh0lCaAn0vOOV\r\nI2cXFO8iuy/niuJZ33NOMjxu+B/Cqtd9owKr8/UNwtYeRR28JPMfdnqCrCW2\r\ni/COZhvk2dP82R0/kV4rbgd4nk9nZZ01kXQ=\r\n=d+DR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4", "@babel/template": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-environment-visitor": "7.21.4-esm.4", "@babel/helper-optimise-call-expression": "7.21.4-esm.4", "@babel/helper-member-expression-to-functions": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.21.4-esm.4_1680621227531_0.4392569439644807", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-replace-supers", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "a6ad005ba1c7d9bc2973dfde05a1bba7065dde3c", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-/y7vBgsr9Idu4M6MprbOVUfH3vs7tsIfnVWv/Ml2xgwvyH6LTngdfbf5AdsKwkJy4zgy1X/kuNrEKvhhK28Yrg==", "signatures": [{"sig": "MEYCIQDkh/oDLz7xEt094pQQgj2y2h/qOuS9A8XYy18Xh7EfbwIhALdAkVd6AgHX1Ru3O9w5OnjQDNcTzl3aD64SZsQE5r8c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHHw//bSl/RDYf0c5dn4NXoNBG0uoi2rKiKosJiN34B4TyoXgDm9s5\r\njHojNOAWz7VBVzmVkX1+AnNh5S2SHeLnRmeqlm9edT6LNm6vCRC42LtKEkBt\r\nEMSGV30Ao99Qw9XdQWjEieYGH1okqT4dfwg3UGw2jtGXTio2LxaK6GKbv2E8\r\nwBArwGcY0NKJ9B5IRJqQbbu91Mk8qr4uvlc7skrZsdtGasQ0kIORpVWsCSF/\r\nhUEdas9l7byAqTjhZf9WmvCcWbQpjipWluCuIj9yqg78MMBl0CWAkkeho7Ag\r\n+5i/RH6/Unx+Isc1DKkfS9p0gtsZdtgVEnv8GOKV6YEVfL1NdkhKJwYEpCaI\r\n/wfQ6yHUb08tqozsuNb2OTQboSKZDjucTaguTM/5uP4vgqLv+CXUEUz0ACWf\r\ngrE0KnPzRRV/MHsQ6jU7fNlw67na8LqEMbgDolECmJx72MI1W12VkJ7U1mpG\r\nzpu60om7KQY3YJqc/eb+QVG7we+/JaUhjAip5LYvM86QffbRBX9LSE9yWfWh\r\nYQ/UDX8MUqkJTMmQIkoqDWZe2gOkQBQ8ZarhsJKLFjpx8BlZ3Vz014UBNSdh\r\nU8yIuON/XSrLATK7G9MX7WDEl9CZoTbI6hyB0qKwS5cz7Ta1UF5gQksDYQW6\r\n+BOVoRvaDErsyNuITEIEB8JIGQFZU+QQbPg=\r\n=PU5y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.21.5", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.5", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.21.5_1682711428045_0.7559913309354862", "host": "s3://npm-registry-packages"}}, "7.22.1": {"name": "@babel/helper-replace-supers", "version": "7.22.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.22.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "38cf6e56f7dc614af63a21b45565dd623f0fdc95", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.1.tgz", "fileCount": 5, "integrity": "sha512-ut4qrkE4AuSfrwHSps51ekR1ZY/ygrP1tp0WFm8oVq6nzc/hvfV/22JylndIbsf2U2M9LOMwiSddr6y+78j+OQ==", "signatures": [{"sig": "MEQCIBYnGc8fiF9mhjiChuy4kjgAjioAZlOR3ohSIFaQnaijAiAZGlTMqYs32dM9y2uoKJbe74fFf7uQ7MhAM+zoZQMZOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32254}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.22.0", "@babel/template": "^7.21.9", "@babel/traverse": "^7.22.1", "@babel/helper-environment-visitor": "^7.22.1", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.22.1_1685118895570_0.6922019433468598", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-replace-supers", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "71bc5fb348856dea9fdc4eafd7e2e49f585145dc", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-aLdNM5I3kdI/V9xGNyKSF3X/gTyMUBohTZ+/3QdQKAA9vxIiy12E+8E2HoOP1/DjeqU+g6as35QHJNMDDYpuCg==", "signatures": [{"sig": "MEYCIQCGrMdH7bMd5Y574yygvtKNCJzfFS3CGAoqoP/oVSLbYAIhALAI6RqkIWWa2r8F7Pz5bpdI760o65iuntpcuJIF3A8C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32246}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.22.5_1686248502471_0.1160254138036172", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-replace-supers", "version": "7.22.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "cbdc27d6d8d18cd22c81ae4293765a5d9afd0779", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.9.tgz", "fileCount": 5, "integrity": "sha512-LJIKvvpgPOPUThdYqcX6IXRuIcTkcAub0IaDRGCZH0p5GPUp7PhRU9QVgFcDDd51BaPkk77ZjqFwh6DZTAEmGg==", "signatures": [{"sig": "MEUCIGPxhhnyEvfN9aRb7diuLMC96/ORR650LyH3xi7iavTsAiEAglHIZE3U79Z6ZVOP/Q7on4mqnFROnQXpx7pTxPjXfmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32022}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.22.9_1689180812818_0.5356897715504498", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "2e023a287f6a5e3642c4394393fdbcc3d131f971", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-0rRlE+G2ukOgkgX+5YGh2KZ54GfEbkUKxh3etUIT+gWbjn19PT+O8mLesJyepZG00VGScfOifWoUyyAzO6g7gA==", "signatures": [{"sig": "MEUCIQCx/9ZGYIPewCZob4Op5NJBlRqeZaDIjuZ74STPVikDXwIgdmvjxSYc79psjHFLeJsz3ewTgdbQktyyhQPLHLng0O8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31404}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.0", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.0", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.0_1689861611741_0.6959934736588405", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "1d40e1d813c99cdb92d184dd3e76da6ba0dcf410", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-7fm5ZGLf/u99usZswrk2iiG5jU3JYkGo/BPozLwmDEsx9ZuvcRUkjY0sREWe1k+Urdb0NL4HPqqV+hN21FkVRg==", "signatures": [{"sig": "MEUCIGkOnHvLx4T9A2JfT6v/7ATKUbgJkCsuKLdidxNQGlGHAiEAh0z60rrYdFN1iJBQp6wpyqQK44KaYR5wFAuX2HMAmRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31404}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.1", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.1", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.1_1690221149446_0.00831279137142027", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "6f8c51128e5c220b6b5df8b0c8703a11fcdc1e51", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-ksWYNdFrAC2EMHQP3xSsf1oKig3XIw4xHc+NLJLMQLouRG8VZ1aiTMOO2+yUDRaYjCQ1SGTuNGRYSAbmH4asXQ==", "signatures": [{"sig": "MEQCIHhevV9J/cKC+vB7q3yIC93M0Z2gy4v4VElrMky61OZlAiBN+w6wWrHKTyjnYAYZaNVCyNgRdxaNn6UKpsZ+TwH5Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31404}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.2", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.2", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.2_1691594110274_0.37857297219674746", "host": "s3://npm-registry-packages"}}, "7.22.20": {"name": "@babel/helper-replace-supers", "version": "7.22.20", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.22.20", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "e37d367123ca98fe455a9887734ed2e16eb7a793", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.20.tgz", "fileCount": 5, "integrity": "sha512-qsW0In3dbwQUbK8kejJ4R7IHVGwHJlV6lpG6UA7a9hSa2YEiAib+N1T2kr6PEeUT+Fl7najmSOS6SmAwCHK6Tw==", "signatures": [{"sig": "MEYCIQCBtiUTZtgizT7x9yi17zZDiz3JReJ1f5SqqJlG1m8qBQIhAJcPizJEb3SbaA72tbqdTRmc1PVAmJr//NrfqVutN4Tm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31921}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.20"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.22.20_1694881722435_0.31155140975982554", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "2f4544cebf3582c1231ea79336c354c8e136d743", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-Lpisg7NEQFaWLIo8mxQ9pURlYZyvh+tbO0x0VAArmJ30ByZIk6iF8M1BN+hPeIGrchS58ek5ZL/Byt6p88UbpA==", "signatures": [{"sig": "MEYCIQDR6pnuyh0JnoGBJIwL/QMozioQL5tYBZtuog1oQLNHzAIhAKikWO+6ju3MDUUwRQKZ4cRLfuxqmFAxWBwllgT2/O/L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31352}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.3", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.3", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.3_1695740237608_0.6669000539766459", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "86e3a72ac2693c662a3deb27b699f3065b3fdd0f", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-TrVDxQ1oY5RiXMJDEiWIfsneoMsFcArp//8UErkSKSmgalQOYFX347df6FV/mQBDgSwfxkkqtFOJepqoGxeARw==", "signatures": [{"sig": "MEQCIBHyFv+S5rewd5HEViOXbeJN6eGByOoO538jk9U2x8jAAiAcU1pieWWer34M4I/T4ueIDn5IhB7k5/ufQvEnt0D/qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31352}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.4", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.4", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.4_1697076394593_0.2208377536523305", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "5c200f989046ab796761323e7ec5df9c9528fee4", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-sV0/DSpDmUNbeV8Bme/ZiZjEMSI6F4YIhVSMXkOkz3zMzv2GVYKR5+YizlouyNVCCxnhADxc6+l58YPridtxJg==", "signatures": [{"sig": "MEQCIHZBol1HMESnybWr2UgGZIEhVou/r71KgbraEfm/MNe8AiAFp9iY/RXUa9ZfH13VWjUojdnJ7/gDiErHx2CVHgz4mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31352}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.5", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.5", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.5_1702307962633_0.36522645395667896", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "1df008ceb2ea798e3b819b59d956e3581daf1eaf", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-sdDtLiquuVSNWneha/+rRrQMS23wTVPENGrAog/5Fv3qhuQwHG/0mhr1KCnodj7Jc9Bd42c14yKVFiPZs23zhg==", "signatures": [{"sig": "MEUCIQCI2F8Iz0/Gbwd+38ttEn/FEQdcXPH+w8D3oZL2MdxjiAIgCke7lf1zqcDQpNPnALu+qSlp5sR/g7QGCWYWUIZzzl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31352}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.6", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.6", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.6_1706285661979_0.2734023939605228", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "a28ee7899507f858f7ad6435733573d4c9da5b2c", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-Q7kVuHbPxMrhUmNpgoO7CCgcFXyiqlSlnWlU2VG6zEPDvX6xyvYC6Gnpj5LWVc8uQ3PrbBWYBwxseWTOJg33KA==", "signatures": [{"sig": "MEUCICz3HQgU7/TPFAunYaSLWx30rsU07NVlwAPXeAKN5HAPAiEAiUpdckKJQjsTxqkK9boCloVIw3Dff8cZlKK0u3l4b3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31352}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.7", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.7", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.7_1709129122676_0.7814228974027908", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/helper-replace-supers", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "7085bd19d4a0b7ed8f405c1ed73ccb70f323abc1", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==", "signatures": [{"sig": "MEYCIQDMGj6H5fLSTmX4Dgxj+HvDSSCKSNOe8+MkiD9dVbg0qwIhAK/jGA7qXMnZ2JzpKkEwaAIlO7pXXMrDLUf9irv8CYlm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32172}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.23.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.24.1_1710841656587_0.07647964505778893", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "7bc7afe37131c5dded34a54b0e836ab7ae3af90d", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-JUXXiUuzRGtdS9Zs04AkgFE9Zm6IQIieQ0f4c+rqbM+6LZgFU5xfLnaKmRmgST4jokSjaoZeDsHiHStUO/uBrw==", "signatures": [{"sig": "MEUCIGhefnR67sT63mhLOUUniVb2wIRG7RaPjX7RIxiYUra7AiEAhsECw7dwMoIHXrptpStGf1PR8BYa8GvjyCjzm1+anO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31629}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.8", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.8", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.8_1712236805741_0.42176704517696306", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-replace-supers", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "3ea87405a2986a49ab052d10e540fe036d747c71", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-mRhfPwDqDpba8o1F8ESxsEkJMQkUF8ZIWrAc0FtWhxnjfextxMWxr22RtFizxxSYLjVHDeMgVsRq8BBZR2ikJQ==", "signatures": [{"sig": "MEUCIAU1p5TpudgE67CTjF6UJWfxii0QlKA7tD9N5BH0v8ZQAiEA4+iKlxxL22LhgTPqP3ozFyG+JpEKjYoII5LSeksIfUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98562}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^7.24.6", "@babel/helper-optimise-call-expression": "^7.24.6", "@babel/helper-member-expression-to-functions": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.24.6_1716553493604_0.6769921346265706", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "323a89caa82f87bdc9fa60a3962fa1ef8e66a612", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-ZfYCNIbt7OJ8qbLZPhWpDG8sx04ehaw6k0c3o5WH9qsb3CqK7c6ZlUf/WPdmFjwv6a0d7+P4tLtJvv+UkgNKSg==", "signatures": [{"sig": "MEYCIQCHM01PE7TPQFZjlqj8rjq06BmzdSQ73k5GRjYym3+jTQIhAKZ3UxZudbXmFuyI995TzieSG5XxykyTSuVUiVVZY0uS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99121}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.9", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.9", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.9_1717423520209_0.9521578950355907", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "d6aeaada33adbe1a77850985586a5dd37f17b679", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-M8xQWGbGPhguu8OUfRBesHl0P0cIHkoNt5ezvscTEcSClygSePrYKBO2DYlNkNXNnqCoY6yjQV8Z8KooYHhW2A==", "signatures": [{"sig": "MEUCIQCtI6+Aibe3ghcVROnDuSofHkV5SwzNIxQ+3X28NxX6YAIgHlcSy/rc9JV5aIMpbcMbrXnW8YU/fsfZQb5/ZE/U6Z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99127}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.10", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.10", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.10_1717500034293_0.3617931122570377", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-replace-supers", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "f933b7eed81a1c0265740edc91491ce51250f765", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-qTAxxBM81VEyoAY0TtLrx1oAEJc09ZK67Q9ljQToqCnA+55eNwCORaxlKyu+rNfX86o8OXRUSNUnrtsAZXM9sg==", "signatures": [{"sig": "MEYCIQDzFYD5P7PKs3KV7zGlktKokQqdfrmJykuK1opDno9NaQIhANUSO+JqyGqmME4dkBHFerK01jt4Xawcwgsg50RCLhTx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98558}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.24.7_1717593348607_0.2768041977890028", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "35da89577d8e6c55f12e0a4f4a09c5d3d7910794", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-yhYW+rH9GrcTlYkgqttDrBq3RK89YwEaHtFkrV9Nxwo+bR1dgUcT8HUWmo3u2akEaSU5ttQO6lmTslOwK2cH9w==", "signatures": [{"sig": "MEUCIQDBBSdAPFOjHCDy9ip6gJCfEcx/9IvMPFbNR9DMaDOuZwIgYMqPjaCz5KOlHaTdeq//4DcpcktLWITpFGKxMpnboUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99016}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/helper-environment-visitor": "^8.0.0-alpha.11", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.11", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.11_1717751759085_0.27308365350210595", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/helper-replace-supers", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "ff44deac1c9f619523fe2ca1fd650773792000a9", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-q688zIvQVYtZu+i2PsdIu/uWGRpfxzr5WESsfpShfZECkO+d2o+WROWezCi/Q6kJ0tfPa5+pUGUlfx2HhrA3Bg==", "signatures": [{"sig": "MEUCIQCu5YFsd/KF5lpxFQQ0draAIYNB0C/w5A221wycGcecvgIgSWaLrvMmsTWoNdRJp9oMaKh42U3EzcvmJhilFmuweR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106233}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.25.0_1722013169034_0.9385809822438778", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "7d942062de128be5656135214dd0a48490ae8819", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-J27W3J4RU7vsDddlAgzos6qvB+MtJ8smPYzbPNIqvzncKik0nGtdytqNTiWU37wA8+7I3sZi64KaAVIfmUhKkw==", "signatures": [{"sig": "MEUCIQC0xANOLq8W8OerOh03brgAaTRDvz1ukujk3lXcaO0VoAIgFKowpMFjqt5yJ6U2n316BmD//LITYoOBGYzM90SOCjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101122}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.12", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.12_1722015233170_0.3298810085586872", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-replace-supers", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "38cfda3b6e990879c71d08d0fef9236b62bd75f5", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-iy8JhqlUW9PtZkd4pHM96v6BdJ66Ba9yWSE4z0W4TvSZwLBPkyDsiIU3ENe4SmrzRBs76F7rQXTy1lYC49n6Lw==", "signatures": [{"sig": "MEUCIHmqhc98siV470zesQCfUkin+TpSP0feGwVTN8jOmQNNAiEA54w+7TUzvVrT53Lm/65pv83zDnkH5RY6uxZLG9Eu1Rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114084}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-optimise-call-expression": "^7.25.7", "@babel/helper-member-expression-to-functions": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.25.7_1727882121539_0.5644001700790915", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-replace-supers", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "ba447224798c3da3f8713fc272b145e33da6a5c5", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-IiDqTOTBQy0sWyeXyGSC5TBJpGFXBkRynjBeXsvbhQFKj2viwJC76Epz35YLU1fpe/Am6Vppb7W7zM4fPQzLsQ==", "signatures": [{"sig": "MEYCIQCEOrDdUXxTo4y9MgNr8vfzgYBDxct/YP9LA02TSzjgRQIhAJhwvcWdwMEhJVHM9i1hbQfP3EqsYuUOedDh9mtkh20G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43306}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.25.9_1729610497486_0.6260069768096059", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "86f5c30ea342da82ea405c74d1a7c73348c9411e", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-5+ml2t6u/usB2XgviW6QgO6T6NJKjqeGGJB0que8DMGjNSaO6hRKPlwKP0/C6PSl9BL2O7zQSit8cI464wE4LQ==", "signatures": [{"sig": "MEYCIQD07wn0bYrelssGaFdh9a/aqtRrnRykKKj/a4vux8IjlgIhAMArsrDw1AISJ+FdbESEQxo/dRd6Ge62ZtdZ0lOSgANO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38205}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.13", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.13_1729864478247_0.36977056491466964", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "4b1189a47b147bd9948d0ed436e0195a6800e8f8", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-9rqPKTRaurUQD5BXDIpXoGR1lRnU2PP8p773NVgOWIOgw8/7w/nUsjBFsUndo3K5qGcVvVv/IWEFLELbzkYuSQ==", "signatures": [{"sig": "MEUCIQCnk+BCMR+iDMPwLqzbPFqDh/idpIh/mEf7sM1v8tYBLQIgQPcBwK2q/nHFdK9ZvqBSwhwDuEagLFWqsHfCf83tueQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38205}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.14", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.14_1733504067801_0.024314886789958967", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/helper-replace-supers", "version": "7.26.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "6cb04e82ae291dae8e72335dfe438b0725f14c8d", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.26.5.tgz", "fileCount": 5, "integrity": "sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==", "signatures": [{"sig": "MEUCIGws37BcVwoUqKE7txkJH7AGnw0ZjIHTXykkG9Lt1Cu+AiEA+mAyFYgZJSbARdo8ezTKT/D5InGuttuLQEqikmSymT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43308}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^7.26.5", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.26.5_1736529117926_0.5694482821915816", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "fcf6312281c3da047a362bc92bcfca0138051489", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-VlCLnS7dNCd+nVvXqKRzbwsM3NTNNjqo0wxJZvNOJlV7q9F8aV06PS7y/SelYtlRfooIZy7l++idQj0kOdyr9Q==", "signatures": [{"sig": "MEYCIQC/n8LWkLZnuDaPXD4YaAfziTgjEpU4SreUSrEweapWYAIhAO+2e1RAqGw3Fx6PplydmR0vDFGgXP9KTIYyw/vcmBCZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.15", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.15_1736529895450_0.4143080354372217", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "3edf65d337c30d719edc5ec86503034c5f6fbca6", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-oit7/LcHShTJ+zekVIsNf8d7IUZAeeAK5Qbg+55fF5jLAlurGvPph1TW6/hncj7GHQzo+wSXG1aaBvEXX3DVZw==", "signatures": [{"sig": "MEYCIQDBRzf08J0RHyM2BBCldQeexxiqhw98b66g+JFdF1dgugIhAKtag/9B9Cs1FVUFJpmqr0qSx4XwEBXuWcaZ+K34sWnL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.16", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.16_1739534372109_0.3844228146228752", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-replace-supers", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "624386ae3b7c0665ac5c0c0b3d3f9efe203d5b89", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-X+XYO2WLRx7OSQr4qDmXa5dunwRDRILYZNn/ZAB52lNIE1fnArqcapEFyro1F1jkLHnHYD6du0u1LG3Lu/f1mw==", "signatures": [{"sig": "MEYCIQCOow6rfBgyK3IT7oipT6ZyM6KRjYighE5bw9eSThLm6wIhANaVBH2ruw+kSxW93uuOAU2ds/FaK54ik3pBAW3vIkcc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.17", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-alpha.17_1741717525242_0.7091489469127468", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-replace-supers", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "b1ed2d634ce3bdb730e4b52de30f8cccfd692bc0", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==", "signatures": [{"sig": "MEQCIGB89kLWTW4QJFb0ZB3hdh0DBemvGNbm4Xssjh4o6TbAAiA1VAnrkpimwSgp1icPQJTH6GPDy6Z2b6VvJiYF6hpvTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43308}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_7.27.1_1746025760588_0.23608507319741978", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-replace-supers", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-replace-supers@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "dist": {"shasum": "9a11d8a6395c657d4de8880b7c314dd137989082", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-w1SN1ZQDF3j7R/ZsBFDstqmACA34+FfwCnnJIt8qacYOmZjOFcFXlJ6XDQaCfZ37SoiWn53/8fsnUdElJ4ZIsA==", "signatures": [{"sig": "MEUCIF690qLXzyGpOoAHTzWnrws8GuEbWA/bvE9iEAiArsRPAiEAvpy1sGtq8QMijJQ4vXn18JBicUNepi4/6IsVZbg7ssM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38184}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-beta.0", "@babel/helper-optimise-call-expression": "^8.0.0-beta.0", "@babel/helper-member-expression-to-functions": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-replace-supers_8.0.0-beta.0_1748620297129_0.9158176950344772", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-replace-supers", "version": "8.0.0-beta.1", "description": "Helper function to replace supers", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-replace-supers"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-member-expression-to-functions": "^8.0.0-beta.1", "@babel/helper-optimise-call-expression": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-replace-supers@8.0.0-beta.1", "dist": {"shasum": "3beb9b22cc6827374f1d5bd02e8eb110261b7901", "integrity": "sha512-g5n5DYHDSN9ZqUhKgVYDAW4kIpy4uVdVoez/u4U46dhMr7UPnkDJr0I8kCunpu9Qk9w34elS/NgQcDPGHCY7BQ==", "tarball": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 38184, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDU6PCeGTkCoBxRUR0HJAUW3EgagGAfshNShYLOqlKl7QIhAPmsFfRUuHy4mnxAoZnH3q5q8rWmN305RzqxXBjKJw99"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-replace-supers_8.0.0-beta.1_1751447080491_0.6547396376388104"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:09.683Z", "modified": "2025-07-02T09:04:40.892Z", "7.0.0-beta.4": "2017-10-30T18:36:09.683Z", "7.0.0-beta.5": "2017-10-30T20:57:45.641Z", "7.0.0-beta.31": "2017-11-03T20:04:25.488Z", "7.0.0-beta.32": "2017-11-12T13:33:59.634Z", "7.0.0-beta.33": "2017-12-01T14:29:16.110Z", "7.0.0-beta.34": "2017-12-02T14:40:14.708Z", "7.0.0-beta.35": "2017-12-14T21:48:25.241Z", "7.0.0-beta.36": "2017-12-25T19:05:37.378Z", "7.0.0-beta.37": "2018-01-08T16:03:39.810Z", "7.0.0-beta.38": "2018-01-17T16:32:39.815Z", "7.0.0-beta.39": "2018-01-30T20:28:43.613Z", "7.0.0-beta.40": "2018-02-12T16:42:42.379Z", "7.0.0-beta.41": "2018-03-14T16:26:50.396Z", "7.0.0-beta.42": "2018-03-15T20:52:05.782Z", "7.0.0-beta.43": "2018-04-02T16:48:55.579Z", "7.0.0-beta.44": "2018-04-02T22:20:36.461Z", "7.0.0-beta.45": "2018-04-23T01:58:25.239Z", "7.0.0-beta.46": "2018-04-23T04:32:41.335Z", "7.0.0-beta.47": "2018-05-15T00:17:45.900Z", "7.0.0-beta.48": "2018-05-24T19:24:39.295Z", "7.0.0-beta.49": "2018-05-25T16:04:19.765Z", "7.0.0-beta.50": "2018-06-12T19:47:59.317Z", "7.0.0-beta.51": "2018-06-12T21:20:39.878Z", "7.0.0-beta.52": "2018-07-06T00:59:45.654Z", "7.0.0-beta.53": "2018-07-11T13:40:44.903Z", "7.0.0-beta.54": "2018-07-16T18:00:26.535Z", "7.0.0-beta.55": "2018-07-28T22:07:51.662Z", "7.0.0-beta.56": "2018-08-04T01:08:34.324Z", "7.0.0-rc.0": "2018-08-09T15:59:53.680Z", "7.0.0-rc.1": "2018-08-09T20:09:46.974Z", "7.0.0-rc.2": "2018-08-21T19:25:51.554Z", "7.0.0-rc.3": "2018-08-24T18:09:36.946Z", "7.0.0-rc.4": "2018-08-27T16:46:18.978Z", "7.0.0": "2018-08-27T21:44:49.166Z", "7.1.0": "2018-09-17T19:30:56.382Z", "7.2.3": "2018-12-20T11:13:18.660Z", "7.3.4": "2019-02-25T18:35:50.624Z", "7.4.0": "2019-03-19T20:45:15.588Z", "7.4.4": "2019-04-26T21:04:51.455Z", "7.5.5": "2019-07-17T21:21:49.470Z", "7.7.0": "2019-11-05T10:53:56.118Z", "7.7.4": "2019-11-22T23:33:46.432Z", "7.8.0": "2020-01-12T00:17:26.538Z", "7.8.3": "2020-01-13T21:42:21.684Z", "7.8.6": "2020-02-27T12:21:42.402Z", "7.9.6": "2020-04-29T18:38:21.534Z", "7.10.0": "2020-05-26T21:43:45.401Z", "7.10.1": "2020-05-27T22:08:14.013Z", "7.10.4": "2020-06-30T13:13:17.684Z", "7.12.0": "2020-10-14T20:03:24.564Z", "7.12.1": "2020-10-15T22:41:35.184Z", "7.12.5": "2020-11-03T22:34:40.009Z", "7.12.11": "2020-12-15T23:59:31.053Z", "7.12.13": "2021-02-03T01:11:53.102Z", "7.13.0": "2021-02-22T22:50:13.957Z", "7.13.12": "2021-03-22T15:47:19.897Z", "7.14.3": "2021-05-17T20:44:21.757Z", "7.14.4": "2021-05-28T17:00:00.750Z", "7.14.5": "2021-06-09T23:13:05.636Z", "7.15.0": "2021-08-04T21:13:09.544Z", "7.15.4": "2021-09-02T21:39:49.069Z", "7.16.0": "2021-10-29T23:47:53.665Z", "7.16.5": "2021-12-13T22:28:39.308Z", "7.16.7": "2021-12-31T00:22:57.027Z", "7.18.2": "2022-05-25T09:16:35.871Z", "7.18.6": "2022-06-27T19:50:33.393Z", "7.18.9": "2022-07-18T09:17:40.102Z", "7.19.1": "2022-09-14T15:29:16.402Z", "7.20.7": "2022-12-22T09:45:36.384Z", "7.21.4-esm": "2023-04-04T14:09:54.473Z", "7.21.4-esm.1": "2023-04-04T14:21:51.982Z", "7.21.4-esm.2": "2023-04-04T14:39:56.228Z", "7.21.4-esm.3": "2023-04-04T14:56:39.031Z", "7.21.4-esm.4": "2023-04-04T15:13:47.733Z", "7.21.5": "2023-04-28T19:50:28.235Z", "7.22.1": "2023-05-26T16:34:55.806Z", "7.22.5": "2023-06-08T18:21:42.661Z", "7.22.9": "2023-07-12T16:53:33.160Z", "8.0.0-alpha.0": "2023-07-20T14:00:11.922Z", "8.0.0-alpha.1": "2023-07-24T17:52:29.601Z", "8.0.0-alpha.2": "2023-08-09T15:15:10.487Z", "7.22.20": "2023-09-16T16:28:42.640Z", "8.0.0-alpha.3": "2023-09-26T14:57:17.767Z", "8.0.0-alpha.4": "2023-10-12T02:06:34.800Z", "8.0.0-alpha.5": "2023-12-11T15:19:22.877Z", "8.0.0-alpha.6": "2024-01-26T16:14:22.138Z", "8.0.0-alpha.7": "2024-02-28T14:05:22.913Z", "7.24.1": "2024-03-19T09:47:36.742Z", "8.0.0-alpha.8": "2024-04-04T13:20:05.909Z", "7.24.6": "2024-05-24T12:24:53.778Z", "8.0.0-alpha.9": "2024-06-03T14:05:20.401Z", "8.0.0-alpha.10": "2024-06-04T11:20:34.477Z", "7.24.7": "2024-06-05T13:15:48.904Z", "8.0.0-alpha.11": "2024-06-07T09:15:59.220Z", "7.25.0": "2024-07-26T16:59:29.210Z", "8.0.0-alpha.12": "2024-07-26T17:33:53.366Z", "7.25.7": "2024-10-02T15:15:21.705Z", "7.25.9": "2024-10-22T15:21:37.789Z", "8.0.0-alpha.13": "2024-10-25T13:54:38.403Z", "8.0.0-alpha.14": "2024-12-06T16:54:27.968Z", "7.26.5": "2025-01-10T17:11:58.131Z", "8.0.0-alpha.15": "2025-01-10T17:24:55.627Z", "8.0.0-alpha.16": "2025-02-14T11:59:32.448Z", "8.0.0-alpha.17": "2025-03-11T18:25:25.470Z", "7.27.1": "2025-04-30T15:09:20.835Z", "8.0.0-beta.0": "2025-05-30T15:51:37.294Z", "8.0.0-beta.1": "2025-07-02T09:04:40.629Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-replace-supers"}, "description": "Helper function to replace supers", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}