{"_id": "@humanwhocodes/retry", "_rev": "15-959e79c5f4aa647b2ca322dfdcc6c4d8", "name": "@humanwhocodes/retry", "dist-tags": {"latest": "0.4.3"}, "versions": {"0.1.0": {"name": "@humanwhocodes/retry", "version": "0.1.0", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.1.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "f5442671107e06b8d67a85f3cf0064b9f70fa5f0", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-xI2dTCQ2MgnemVbWPq+jz4EWBUjCKo4GK6fwmTiHRsv0bNLrAmpq65J1VA1GFvPH/BLioXwG8C0Tg8SpTYOSMQ==", "signatures": [{"sig": "MEQCIHkqbe2pMubwjppP+QuAM/V3PNTDjEH/6lGLhAM+uS2iAiAbD+LqaoacyXaWUmaBJgnaogPXjm6JBVgOB84MWuPmyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39832}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "49b8d63aa5045799ddf108aed477516ffb33408d", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.10", "mocha": "10.2.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.2.2", "@types/chai": "^4.3.9", "@types/node": "20.8.9", "lint-staged": "15.2.0", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.1.0_1705450716457_0.697928852688638", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@humanwhocodes/retry", "version": "0.1.1", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.1.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "89e9c7851884280b6912b674f84a0e90262f337d", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.1.tgz", "fileCount": 9, "integrity": "sha512-FIvUfTwPKMXUEtM/rTtjrDTpD06vU8KKxllGMkh8kgYQh6QLuBjyFy56MQJhyz/iwpBuhpQ1vNkYssedf48HcQ==", "signatures": [{"sig": "MEUCIQDAuHDgH/le+yNZDTbSyNKGXpgUTW7UyIvhpIaA8pMKhgIgEvAyHEHfrwrPq3R51oZDH5cUWQuaAKcFcWZoXVD3OQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40232}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "9f94f86d08cb96b58a352b49cbe51d097052f8cd", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "20.10.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "10.2.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.2.2", "@types/node": "20.8.9", "lint-staged": "15.2.0", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.1.1_1705450993460_0.28836449363744876", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "@humanwhocodes/retry", "version": "0.1.2", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.1.2", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "40a27617c44b56d2bd620b5b8cc31dc052ffa595", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-JNWGHkYfWI0+YgRHOwkFKjOuelfypQtp0GSx0lsOP9jU1Tj4f8k0x4dcaJSEZTp61THZJ+f9PJRh1GzYlQqHOQ==", "signatures": [{"sig": "MEYCIQC2+dZ4jgWh2aB5vgks5Eed73NTkQQGlwxkLLzrKEJSMwIhAKAP9pbqLYPk34/GnRyJNkIivj5lsqgdxzhdNQaMl7xH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39760}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "3d6b97534d953b6f983933854085e206c79cc672", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "10.2.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.2.2", "@types/node": "20.8.9", "lint-staged": "15.2.0", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.1.2_1705529259318_0.638113755313563", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "@humanwhocodes/retry", "version": "0.1.3", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.1.3", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "14a3688e03f3fce7ec5c93e4269b3b1d3af3e918", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-2IPnKL9lUTR23r/xrjoMZ3k+Z8sWNNH4UvTePeOZ0q84VeqgRoMN/+c2WigHWSQxZZEDXUm1CH08cPT6/CoxYQ==", "signatures": [{"sig": "MEUCIQCcn5UNzuAxl5H3JO9sZdZHcYhUf52uiKw2rx3c4rWWKQIgS9wU7uWQMfe6ZUSwzPLN7aSqRsCAvwqswd7zhAsxF4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40189}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "03cc513406444a26f0853607990fd1b554e051f4", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.1.3_1709336720698_0.015677504032278522", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@humanwhocodes/retry", "version": "0.2.0", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.2.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "d1af43b33c27d1eeda9132176e1da51f5e4afa28", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.0.tgz", "fileCount": 8, "integrity": "sha512-j1nt8hWsWRXzUPTl9rpoMBE/2GWLBhZPEWiHoLKz+RNsOAZ1CmVHgmcDK15uLe96JSjfyiLhsnvt190Ph8R0Hg==", "signatures": [{"sig": "MEYCIQD2UxAUE7QkIM2hqdu41dccr+KEEM4t/nVBxL5upv92/AIhANNPkL0gCdZQDpfVnGpn9Wtkv3SLsABCr1YYOrn7gXrY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "6751860f10144121c8cafa30626416f4a64e6d51", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.2.0_1710365207870_0.9919451936067103", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@humanwhocodes/retry", "version": "0.2.1", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.2.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "b2d28f1b8e4f24f4313fb6684ce534f3f25b5ed4", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.1.tgz", "fileCount": 8, "integrity": "sha512-9x0wUY3wAq5qnniIlic/70FTYUKfRCkCcb49CPzcE/kf1sUQ+2bk195mGQ5Nc8vuvFSW0cbx5TFue9B/if1poA==", "signatures": [{"sig": "MEYCIQDduORGB27/W2pZtFCZiXgpq11qDBYtvaUPBGhEXKnRlQIhAN5qi/nuNr08yHmsqGRXaRjYphj8JGnEkZbGoYOVxicA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "78081e879db030eba1a2be32ab4db2df9912d058", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.2.1_1710365822300_0.06534498352591056", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "@humanwhocodes/retry", "version": "0.2.2", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.2.2", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "f43dc06e4c9d6407a9c6a86699565a730cfb8a62", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.2.tgz", "fileCount": 8, "integrity": "sha512-tFbq5+l0BruPg4JvjrwEBcRLEHe8fC+UcI8tWJgmKrJlKtY8/JQjmRuECSOZxFnnzTBHgCbLvJ58jRyzvZ5uaQ==", "signatures": [{"sig": "MEUCIQCAg42G+8jrSdNqn6/pPPuAC/dbZ5JjTtxbPKIyqt4k1AIgU2MdhKGE1VSvCx3Cm1XsmqvdFDYPA2SVl2AYeJppANE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "8f8af136e9aa67ed3974589d4fd7923a50156835", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.2.2_1710380930332_0.9773043545299844", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "@humanwhocodes/retry", "version": "0.2.3", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.2.3", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "c9aa036d1afa643f1250e83150f39efb3a15a631", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.3.tgz", "fileCount": 8, "integrity": "sha512-X38nUbachlb01YMlvPFojKoiXq+LzZvuSce70KPMPdeM1Rj03k4dR7lDslhbqXn3Ang4EU3+EAmwEAsbrjHW3g==", "signatures": [{"sig": "MEQCIG6IYS7ov5dqgBmXVTVKg6/nxnPWdtyJ/jqanRqPz3L1AiBqlcOvczLzNUFpO/U4rpLYNTxtCuuzaDgSfC7MdHJgJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "699caf0c84a7a120e9955c0477747ca255a2130b", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.20.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.2", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.2.3_1712956309084_0.9161130234225017", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "@humanwhocodes/retry", "version": "0.2.4", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.2.4", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "4f3059423823bd8176132ceea9447dee101dfac1", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.4.tgz", "fileCount": 8, "integrity": "sha512-Ttl/jHpxfS3st5sxwICYfk4pOH0WrLI1SpW283GgQL7sCWU7EHIOhX4b4fkIxr3tkfzwg8+FNojtzsIEE7Ecgg==", "signatures": [{"sig": "MEQCIECVZdP+Uu8rMexWJxAaqgzch9Cg5CdPctGxWOYBTBI8AiAmiw+0dbfqARatUY0b2OHigpshRL7ZR3hRgt33dOb2RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40551}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "8939455c93240091876d8a0ea7c83c6f9697b230", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.20.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.2", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.2.4_1714747759738_0.38905362565473056", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@humanwhocodes/retry", "version": "0.3.0", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.3.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retrier#readme", "bugs": {"url": "https://github.com/humanwhocodes/retrier/issues"}, "dist": {"shasum": "6d86b8cb322660f03d3f0aa94b99bdd8e172d570", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.0.tgz", "fileCount": 9, "integrity": "sha512-d2CGZR2o7fS6sWB7DG/3a95bGKQyHMACZ5aW8qGkkqQpUoZV6C0X7Pc7l4ZNMZkfNBf4VWNe9E1jRsf0G146Ew==", "signatures": [{"sig": "MEUCIAm7gkfCgiieJD6KeNUyTBIxl5ahCZ3Ko22oWe6IMHakAiEAiR7zU2twKZpzpPINd8hTDpArIfuO76QYkNH9BskS4wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44273}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.cts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "ae1269e520d59ae0457c1a114fbacf0462b07807", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc && npm run build:prepend-type-ref && npm run build:cts-types", "prepare": "npm run build", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "build:cts-types": "node -e \"fs.copyFileSync('dist/retrier.d.ts', 'dist/retrier.d.cts')\"", "build:prepend-type-ref": "node tools/prepend-type-ref.js dist/retrier.js"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retrier.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.20.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.3.0_1715287979598_0.15536536631940612", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@humanwhocodes/retry", "version": "0.3.1", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.3.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retry#readme", "bugs": {"url": "https://github.com/humanwhocodes/retry/issues"}, "dist": {"shasum": "c72a5c76a9fbaf3488e231b13dc52c0da7bab42a", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz", "fileCount": 9, "integrity": "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==", "signatures": [{"sig": "MEQCIBdUz35OOPbuYuBD5p5FiLorvp9uEs2rPT8CBbE2ey7dAiAILjgIu3jtD3duONO2LR0oj9vvLHqgg7U4/Sc9IPW2CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45429}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.cts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "77ab8face0d554980ef02efa45aec051a09494d9", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc && npm run build:cts-types", "prepare": "npm run build", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "test:emfile": "node tools/check-emfile-handling.js", "build:cts-types": "node -e \"fs.copyFileSync('dist/retrier.d.ts', 'dist/retrier.d.cts')\""}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.20.4", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.3.1_1728058924179_0.45158350421001336", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@humanwhocodes/retry", "version": "0.4.0", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.4.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retry#readme", "bugs": {"url": "https://github.com/humanwhocodes/retry/issues"}, "dist": {"shasum": "b57438cab2a2381b4b597b0ab17339be381bd755", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.0.tgz", "fileCount": 9, "integrity": "sha512-xnRgu9DxZbkWak/te3fcytNyp8MTbuiZIaueg2rgEvBuN55n04nwLYLU9TX/VVlusc9L2ZNXi99nUFNkHXtr5g==", "signatures": [{"sig": "MEQCID28OOR4M8uf1Eekc88/HPYPIJwLEPuhrG0qRXSv+k7PAiB/W+2SbawR60hFFild4DUxnJJovxqBXclophIqX89MkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64678}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.cts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "90317d3f133d45583296ef740fb8f8e2997c3903", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc && npm run build:cts-types", "prepare": "npm run build", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "test:emfile": "node tools/check-emfile-handling.js", "build:cts-types": "node -e \"fs.copyFileSync('dist/retrier.d.ts', 'dist/retrier.d.cts')\""}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.20.4", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.4.0_1730408957381_0.5217005452142083", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "@humanwhocodes/retry", "version": "0.4.1", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.4.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retry#readme", "bugs": {"url": "https://github.com/humanwhocodes/retry/issues"}, "dist": {"shasum": "9a96ce501bc62df46c4031fbd970e3cc6b10f07b", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.1.tgz", "fileCount": 9, "integrity": "sha512-c7h<PERSON>llBlenFTHBky65mhq8WD2kbN9Q6gk0bTk8lSBvc554jpXSkST1iePudpt7+A/AQvuHs9EMqjHDXMY1lrA==", "signatures": [{"sig": "MEQCIH4awP2Lto0z+1vPEZAz0IoT96rmosVV1EIpX0IOlo2GAiB+EX0U3NVIp8ohhPQ27R8m7UFlQcg1CJ21Tq3ay+4T/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64873}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.cts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "b3137b435b826fb4e064e352c84bd09a54fba7cf", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc && npm run build:cts-types", "prepare": "npm run build", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "test:emfile": "node tools/check-emfile-handling.js", "build:cts-types": "node -e \"fs.copyFileSync('dist/retrier.d.ts', 'dist/retrier.d.cts')\""}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "18.20.4", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.4.1_1730827559681_0.020963044431089317", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "@humanwhocodes/retry", "version": "0.4.2", "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanwhocodes/retry@0.4.2", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/retry#readme", "bugs": {"url": "https://github.com/humanwhocodes/retry/issues"}, "dist": {"shasum": "1860473de7dfa1546767448f333db80cb0ff2161", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.2.tgz", "fileCount": 9, "integrity": "sha512-xeO57FpIu4p1Ri3Jq/EXq4ClRm86dVF2z/+kvFnyqVYRavTZmaFaUBbWCOuuTh0o/g7DSsk6kc2vrS4Vl5oPOQ==", "signatures": [{"sig": "MEUCIQCtJZpyelUkS6bWH19of8/9kAADuKl8p4sm1dn7W8sA2QIgSpVn4yC/Sgv/EMPsqpsRQ25k/IZ/yyRpi6zpOwY24Kw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 64979}, "main": "dist/retrier.cjs", "type": "module", "types": "dist/retrier.d.ts", "module": "dist/retrier.js", "engines": {"node": ">=18.18"}, "exports": {"import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}, "require": {"types": "./dist/retrier.d.cts", "default": "./dist/retrier.cjs"}}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}, "gitHead": "998e62785c155fb64d733584c27911dc9a544e95", "scripts": {"lint": "eslint src/ tests/", "test": "npm run test:unit && npm run test:build", "build": "rollup -c && tsc && npm run build:cts-types", "prepare": "npm run build", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "test:emfile": "node tools/check-emfile-handling.js", "build:cts-types": "node -e \"fs.copyFileSync('dist/retrier.d.ts', 'dist/retrier.d.cts')\""}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "gitHooks": {"pre-commit": "lint-staged"}, "repository": {"url": "git+https://github.com/humanwhocodes/retry.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A utility to retry failed async methods.", "directories": {}, "lint-staged": {"*.js": ["eslint --fix"]}, "_nodeVersion": "22.13.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/retry_0.4.2_1739987106535_0.487607405717444", "host": "s3://npm-registry-packages-npm-production"}}, "0.4.3": {"name": "@humanwhocodes/retry", "version": "0.4.3", "description": "A utility to retry failed async methods.", "type": "module", "main": "dist/retrier.cjs", "module": "dist/retrier.js", "types": "dist/retrier.d.ts", "exports": {"require": {"types": "./dist/retrier.d.cts", "default": "./dist/retrier.cjs"}, "import": {"types": "./dist/retrier.d.ts", "default": "./dist/retrier.js"}}, "engines": {"node": ">=18.18"}, "publishConfig": {"access": "public"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["eslint --fix"]}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}, "scripts": {"build:cts-types": "node -e \"fs.copyFileSync('dist/retrier.d.ts', 'dist/retrier.d.cts')\"", "build": "rollup -c && tsc && npm run build:cts-types", "prepare": "npm run build", "lint": "eslint src/ tests/", "pretest": "npm run build", "test:unit": "mocha tests/retrier.test.js", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "test:jsr": "npx jsr@latest publish --dry-run", "test:emfile": "node tools/check-emfile-handling.js", "test": "npm run test:unit && npm run test:build"}, "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/retry.git"}, "keywords": ["nodejs", "retry", "async", "promises"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "devDependencies": {"@eslint/js": "^8.49.0", "@rollup/plugin-terser": "0.4.4", "@tsconfig/node16": "^16.1.1", "@types/mocha": "^10.0.3", "@types/node": "20.12.6", "eslint": "^8.21.0", "lint-staged": "15.2.1", "mocha": "^10.3.0", "rollup": "3.29.4", "typescript": "5.4.4", "yorkie": "2.0.0"}, "_id": "@humanwhocodes/retry@0.4.3", "gitHead": "e4cd0fc1148c51de3df40ff8b40f975f5cabf6c9", "bugs": {"url": "https://github.com/humanwhocodes/retry/issues"}, "homepage": "https://github.com/humanwhocodes/retry#readme", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==", "shasum": "c2b9d2e374ee62c586d3adbea87199b1d7a7a6ba", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.3.tgz", "fileCount": 9, "unpackedSize": 65711, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDMCRthHuR66AlmhYiZ+rKkXT3JnOBdKjwGKorPTvCdnAIhAN3JlApaHn2fVTLplXStspkNNpyDBbUw0kQTkm05GLkX"}]}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/retry_0.4.3_1746627957166_0.5721062228196618"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-01-17T00:18:36.363Z", "modified": "2025-05-07T14:25:57.572Z", "0.1.0": "2024-01-17T00:18:36.772Z", "0.1.1": "2024-01-17T00:23:13.690Z", "0.1.2": "2024-01-17T22:07:39.450Z", "0.1.3": "2024-03-01T23:45:20.888Z", "0.2.0": "2024-03-13T21:26:48.068Z", "0.2.1": "2024-03-13T21:37:02.475Z", "0.2.2": "2024-03-14T01:48:50.464Z", "0.2.3": "2024-04-12T21:11:49.261Z", "0.2.4": "2024-05-03T14:49:19.891Z", "0.3.0": "2024-05-09T20:52:59.730Z", "0.3.1": "2024-10-04T16:22:04.392Z", "0.4.0": "2024-10-31T21:09:17.595Z", "0.4.1": "2024-11-05T17:25:59.934Z", "0.4.2": "2025-02-19T17:45:06.712Z", "0.4.3": "2025-05-07T14:25:57.372Z"}, "bugs": {"url": "https://github.com/humanwhocodes/retry/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/humanwhocodes/retry#readme", "keywords": ["nodejs", "retry", "async", "promises"], "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/retry.git"}, "description": "A utility to retry failed async methods.", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "readme": "# Retry utility\n\nby [<PERSON>](https://humanwhocodes.com)\n\nIf you find this useful, please consider supporting my work with a [donation](https://humanwhocodes.com/donate) or [nominate me](https://stars.github.com/nominate/) for a GitHub Star.\n\n## Description\n\nA utility for retrying failed async JavaScript calls based on the error returned.\n\n## Usage\n\n### Node.js\n\nInstall using [npm][npm] or [yarn][yarn]:\n\n```\nnpm install @humanwhocodes/retry\n\n# or\n\nyarn add @humanwhocodes/retry\n```\n\nImport into your Node.js project:\n\n```js\n// CommonJS\nconst { Retrier } = require(\"@humanwhocodes/retry\");\n\n// ESM\nimport { Retrier } from \"@humanwhocodes/retry\";\n```\n\n### Deno\n\nInstall using [JSR](https://jsr.io):\n\n```shell\ndeno add @humanwhocodes/retry\n\n#or\n\njsr add @humanwhocodes/retry\n```\n\nThen import into your Deno project:\n\n```js\nimport { Retrier } from \"@humanwhocodes/retry\";\n```\n\n### Bun\n\nInstall using this command:\n\n```\nbun add @humanwhocodes/retry\n```\n\nImport into your Bun project:\n\n```js\nimport { Retrier } from \"@humanwhocodes/retry\";\n```\n\n### Browser\n\nIt's recommended to import the minified version to save bandwidth:\n\n```js\nimport { Retrier } from \"https://cdn.skypack.dev/@humanwhocodes/retry?min\";\n```\n\nHowever, you can also import the unminified version for debugging purposes:\n\n```js\nimport { Retrier } from \"https://cdn.skypack.dev/@humanwhocodes/retry\";\n```\n\n## API\n\nAfter importing, create a new instance of `Retrier` and specify the function to run on the error. This function should return `true` if you want the call retried and `false` if not.\n\n```js\n// this instance will retry if the specific error code is found\nconst retrier = new Retrier(error => {\n    return error.code === \"ENFILE\" || error.code === \"EMFILE\";\n});\n```\n\nThen, call the `retry()` method around the function you'd like to retry, such as:\n\n```js\nimport fs from \"fs/promises\";\n\nconst retrier = new Retrier(error => {\n    return error.code === \"ENFILE\" || error.code === \"EMFILE\";\n});\n\nconst text = await retrier.retry(() => fs.readFile(\"README.md\", \"utf8\"));\n```\n\nThe `retry()` method will either pass through the result on success or wait and retry on failure. Any error that isn't caught by the retrier is automatically rejected so the end result is a transparent passing through of both success and failure.\n\n### Setting a Timeout\n\nYou can control how long a task will attempt to retry before giving up by passing the `timeout` option to the `Retrier` constructor. By default, the timeout is one minute.\n\n```js\nimport fs from \"fs/promises\";\n\nconst retrier = new Retrier(error => {\n    return error.code === \"ENFILE\" || error.code === \"EMFILE\";\n}, { timeout: 100_000 });\n\nconst text = await retrier.retry(() => fs.readFile(\"README.md\", \"utf8\"));\n```\n\nWhen a call times out, it rejects the first error that was received from calling the function.\n\n### Setting a Concurrency Limit\n\nWhen processing a large number of function calls, you can limit the number of concurrent function calls by passing the `concurrency` option to the `Retrier` constructor. By default, `concurrency` is 1000.\n\n```js\nimport fs from \"fs/promises\";\n\nconst retrier = new Retrier(error => {\n    return error.code === \"ENFILE\" || error.code === \"EMFILE\";\n}, { concurrency: 100 });\n\nconst filenames = getFilenames();\nconst contents = await Promise.all(\n    filenames.map(filename => retrier.retry(() => fs.readFile(filename, \"utf8\"))\n);\n```\n\n### Aborting with `AbortSignal`\n\nYou can also pass an `AbortSignal` to cancel a retry:\n\n```js\nimport fs from \"fs/promises\";\n\nconst controller = new AbortController();\nconst retrier = new Retrier(error => {\n    return error.code === \"ENFILE\" || error.code === \"EMFILE\";\n});\n\nconst text = await retrier.retry(\n    () => fs.readFile(\"README.md\", \"utf8\"),\n    { signal: controller.signal }\n);\n```\n\n## Developer Setup\n\n1. Fork the repository\n2. Clone your fork\n3. Run `npm install` to setup dependencies\n4. Run `npm test` to run tests\n\n### Debug Output\n\nEnable debugging output by setting the `DEBUG` environment variable to `\"@hwc/retry\"` before running.\n\n## License\n\nApache 2.0\n\n## Prior Art\n\nThis utility is inspired by, and contains code from [`graceful-fs`](https://github.com/isaacs/node-graceful-fs).\n\n[npm]: https://npmjs.com/\n[yarn]: https://yarnpkg.com/\n", "readmeFilename": "README.md"}