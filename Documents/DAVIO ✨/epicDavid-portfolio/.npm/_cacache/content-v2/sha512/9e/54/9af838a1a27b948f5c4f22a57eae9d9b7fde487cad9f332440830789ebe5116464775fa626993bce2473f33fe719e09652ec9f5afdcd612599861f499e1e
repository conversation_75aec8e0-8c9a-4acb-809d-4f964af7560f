{"_id": "stylus", "_rev": "594-00f6f59db969be159d37834dae4cad56", "name": "stylus", "dist-tags": {"latest": "0.64.0"}, "versions": {"0.0.1": {"name": "stylus", "version": "0.0.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.0.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "102c5826838e7f8cc6e582cf990baa72c33fc7df", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.0.1.tgz", "integrity": "sha512-FA5IBXw76vp5Vo3w5RmhRDydoZN4XJbhRq03hMsu5kBp2EnieFZkTCimtH10WqmSOpRUGv4+Z5AelfFEi8lkHQ==", "signatures": [{"sig": "MEUCIQD/uKSYIe6VUvaSgriVcUYTP8sKTEy/j6PK52jzmZ0mzwIgH5hMQkrHHWQniDEHkC/YJZUflssUhdQ0WMCK3jeb+w0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.0.2": {"name": "stylus", "version": "0.0.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.0.2", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "11b930e2dc00268062cb9e46ba1bfa04b2c7d5ad", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.0.2.tgz", "integrity": "sha512-OlgaCGoVB3xcCdKNT+QQiJUM5bh8PahDGRh4B9mOrOnAxUX089TrFdMdDFX254c+qB5Zm+c9R4L1th4xZfAuJw==", "signatures": [{"sig": "MEYCIQDilKKAWN0dxOcE60AkDyC2zFamvgebQtpfxuLASboVMwIhAKw+VKDEc9cGwVqf2Z6pInxP5R2XTjIc625kWw5FiMlK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.1.0": {"name": "stylus", "version": "0.1.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.1.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "7986729d88a3fb4585a866f4e03c068c44728833", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.1.0.tgz", "integrity": "sha512-ew6NK8/Y1aNtmjh2fD72oTau5T50+XcQq12egJL52hmQuBGBhL26kjQC+W5VBDxc0TzrNN5DDrcIAT/DSdAKjw==", "signatures": [{"sig": "MEUCIHB/chBUQ9HRBPPQrqK1lVFyEeI7nZBya7n13ZEjlXeqAiEAzEAsXwVrK504RGXoQHwA90Aah5rQ/uO7VhhqCfXkd4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.2.0": {"name": "stylus", "version": "0.2.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.2.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "d60a2475260e053cf87b277cbf073cd4e1da55df", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.2.0.tgz", "integrity": "sha512-iAkDEw7cwqt8a4AUMOVPN1WhPm9rlA9k+ckSIhkgnDq8+GBLfxRqN7J2SHCvsdHWQlZ3o5wjSfCMbI1sHx4PWQ==", "signatures": [{"sig": "MEQCIDnnLSHHRcacnHGIpEdhVkcv/FLMY+c2jAepVZdfQOvxAiA7I21MqV8cWmi36MSXh/dDCvEV9JF157AEdtVJ3It3zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.2.1": {"name": "stylus", "version": "0.2.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.2.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "b3c1e51208fdc83ebe782fe4ad8b0ce65891f1ea", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.2.1.tgz", "integrity": "sha512-OoSl+K+lFtUjfHG0i8PtCSaELiqJABmCisvUCRi27r9wVxayFmySqViddM8XCRhTXO1WgSnggIBayg3WxX9Reg==", "signatures": [{"sig": "MEUCIQCCEV3BQT09fpR6Gog7X5MALtW7aMX2xkTkE6kZZViKggIgCVgk/hwpijSUY84+Z7HRlMLlJ3seilh39YvH0KdOkB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.3.0": {"name": "stylus", "version": "0.3.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.3.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "a616c476de59cfa4e66c3d53ff07d604232bd3c7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.3.0.tgz", "integrity": "sha512-Gp6foZoZPtdQ4+qgTk+AUQ3gaYk3rRUOtMqn5RwoxYr5+d0DpBBWC5qP7nIvLIOpabn0wPSClJGbnejWUCxiCg==", "signatures": [{"sig": "MEUCIA4AkJIsQfBdQ4KVmIz6lhXxjV4qUeeV7QXwvxTCD6ucAiEA2KpJY0CmvqiTYjyWz9zVeAlGcE23oBpLT2dgdHsfths=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.3.7", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.3.1": {"name": "stylus", "version": "0.3.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.3.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "89eae9842b9e47a6c6b2b47756cd645275f2d4e2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.3.1.tgz", "integrity": "sha512-MTSro0eMLD9AOlptWldDWTD8zP64QPPjoZcQoxWc83SaQrld2KxRFRWJ8Xmdf8VcO4eGhsw0c0PIjB1g8lhFpA==", "signatures": [{"sig": "MEUCIQDXFt+M1R8jPUHB1NMlKE2nHyGUoWT8spNIwDdmnUrUoQIgQmQvlJdu70GSsEeRYMYKvhZfneof2W6yiT35BHkPKVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.3.7", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.4.0": {"name": "stylus", "version": "0.4.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.4.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "52c9ad5d45643bb54bf2549ee9e1871e521575c1", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.4.0.tgz", "integrity": "sha512-KT5bj20GYB5/h328ciDeAIbiTvb7KYHBaIlv/9XhcHnpS4idFExaW+lfydMzb8x3PYwQrXKaUWKYoKD9H2PUCg==", "signatures": [{"sig": "MEYCIQCSCFwE2/Scc/6ju9PX9DGYUnQmHMWNkvsmrEYfvOVDowIhAPq51TW3NaZE6VhEH8X+Rx2SFPlV5p7DUTsrqgngZCjW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.4.1": {"name": "stylus", "version": "0.4.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.4.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "93efcd1db687bcf950054ed86f5c063805c83113", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.4.1.tgz", "integrity": "sha512-A9+PWEp1JBmXQSkb2diZQQ5f4DFGk/iS3MfzaVWIiR+/HZr9uj0my3IjUI3vy3nhfokqHu4ihdA8FWkkaJddkw==", "signatures": [{"sig": "MEQCIFxDvqd3beJbaP7AqFJRcSND5OpO9MuovsImgcpT6MFqAiBuz/Tr1mhjFW4AweEhVUV1fBM0B5AqsN9uqetirFQyHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.3.8", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.5.0": {"name": "stylus", "version": "0.5.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.5.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "4db4b72288181fc8e0dc996defaeb80c4340cd55", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.5.0.tgz", "integrity": "sha512-Bj0HatM/a44awBATOdDtJWXK3yyORHXeTZPBqMInSjGDl0EppdWfyK+WJh4JYX2qS5ysiZmBn607L2dE90LzoA==", "signatures": [{"sig": "MEYCIQDFI6pc8CqZLHtdi1UeFRcdlCGXtJiRyHicvHSmOWmhMgIhALVrppVNB/aJGN4ioSAnQLdXr5SqOpS2cbsONIe7+Qdm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.3.8", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.5.1": {"name": "stylus", "version": "0.5.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.5.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "52f3c59990b51203c80706a42371407fd09f020f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.5.1.tgz", "integrity": "sha512-Qho6j6JrOM/+SSAtTDde8HsqSzkAIkCRCkAE7pswFjylqQbmCM/mISWKEjx66PD+pRW4qX9eUk51Qnvj7wzCBg==", "signatures": [{"sig": "MEYCIQDHjBOBEOQ3ECE6F4V/Lecvx63/5HOHloHC1GA3ycb3MQIhAOJHKKMtwLLVjvZLHMjoJz/G5qwjQ7Gj8sB2gz9TCmvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.0", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.5.2": {"name": "stylus", "version": "0.5.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.5.2", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "c737a5a8540ed1725f1878eadb58994ef89e2b44", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.5.2.tgz", "integrity": "sha512-Djp+csQ1/KSOhmS8b2bCtoMdHVb+Qeqqiz+nj0HXEe8is7a2XU1BsTsCf6LYDs8/tghInqVRO3heosEvS2/r2g==", "signatures": [{"sig": "MEQCIAQsD3UkdcnZto+xSIlT+orG1OrrfOWJR4pxxbQhs4wiAiB1yQAjYEXzkqNUr0ykwwRjUn5RRIx10SBjIl89M5DnlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "modules": {"lexer.js": "lib/lexer.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "colors.js": "lib/colors.js", "parser.js": "lib/parser.js", "stylus.js": "lib/stylus.js", "nodes/if.js": "lib/nodes/if.js", "renderer.js": "lib/renderer.js", "middleware.js": "lib/middleware.js", "nodes/call.js": "lib/nodes/call.js", "nodes/each.js": "lib/nodes/each.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/root.js": "lib/nodes/root.js", "nodes/unit.js": "lib/nodes/unit.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/color.js": "lib/nodes/color.js", "nodes/group.js": "lib/nodes/group.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/index.js": "lib/nodes/index.js", "nodes/media.js": "lib/nodes/media.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/import.js": "lib/nodes/import.js", "nodes/params.js": "lib/nodes/params.js", "nodes/return.js": "lib/nodes/return.js", "nodes/string.js": "lib/nodes/string.js", "functions/url.js": "lib/functions/url.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "visitor/index.js": "lib/visitor/index.js", "nodes/function.js": "lib/nodes/function.js", "nodes/property.js": "lib/nodes/property.js", "nodes/selector.js": "lib/nodes/selector.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/expression.js": "lib/nodes/expression.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js"}, "_npmVersion": "0.2.16", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.0", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.5.3": {"name": "stylus", "version": "0.5.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.5.3", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "c147f6c0df57c95767d64d26173b7d6ac50c8a36", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.5.3.tgz", "integrity": "sha512-0bLC2WXVemQLQ2lj0jnoHe/bRSleAVPGxC0RGYXEva/59+n37Cx5vmuJ/7L0PRWaXmXVemlKN0fxk3ILNcIMBg==", "signatures": [{"sig": "MEQCICxnyPH+s0GctKTP2O6TAcqdrHmUtuxHzLqmoo78vWBtAiBbJxHYTWBr0Ud/eyWTHRX5sgsZEFRMRvxmVPYynHUyYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.2.18", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.0", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.0": {"name": "stylus", "version": "0.6.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "fce868e480a5bc9bfc424931046c06f363736af8", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.0.tgz", "integrity": "sha512-4StLSnOSSUHiX39iVy3tumDaoLU7T9D5tUs1T/a8G5tlgNs5l/mnvjKbtvfc0QjjA/AZzXAQQeUJF3rVYqNpYg==", "signatures": [{"sig": "MEQCIFPBi29rVmgD11BBup5sY/jHguYh3q5I2BH8kdkfDaCsAiAWeZDDMfabLcgF5kkgBLzRZIDi6lg4RC1wM5Sv80og7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.2.18", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.0", "dependencies": {"cssom": ">= 0.2.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.2": {"name": "stylus", "version": "0.6.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.2", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "29830c2c52b16003663998a9fe52b33db5dfc49f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.2.tgz", "integrity": "sha512-Qq/XM4/lKTpTYjaRHehFVdH4d5fAUDVNhk0jHC3Z6/ElzobGwxbGEtWoAAKd5k8r+kiqIEjG18zsxW200Y90xg==", "signatures": [{"sig": "MEUCIQDJPH9pjZfMV1Pc3WPhtJvthlJtS9mymyhHpwXfsxYnqwIgCZu+1al6dg+BjMLkCVekv+gCoZdr5fVc9O7n1ZRHGro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.2.18", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.2.6", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.3": {"name": "stylus", "version": "0.6.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.3", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "bb6dad18bb94fd0b8c490c88d0de2edfb29ca3c2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.3.tgz", "integrity": "sha512-8LFgGPNGIOJMswt5KBqhrHLkLrJUQCoILhGX0ndKboEWtsnDoiWWyq4QAv6lDn7xSdRd+zTqgSojkHdaZM3svQ==", "signatures": [{"sig": "MEQCICTYGlRiFoAFb/Xyp3du6+9ukK+9X5c91kUCa4sU9UPCAiA+yeRmVn8w50BcOAswGwhosoZgCIlxw3d2GVaSjD0DNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.2.18", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.1", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.4": {"name": "stylus", "version": "0.6.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.4", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "96925628e5b4712397677b8c36fcc488fd0ef31e", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.4.tgz", "integrity": "sha512-e7q/RLHuAB8pvf6k4GcuC1LHgH2AhOsbkzQpuEUc6WkfxNFrnb5DYuHF+Bmce0ZEXGKhD+O1z8XpDdMK87jX/A==", "signatures": [{"sig": "MEUCIQDT7FNR7LZ786NT33deddeT47IAzO3DVJ/QaRSNAVEr/wIgAr3w5+6bBUjxMqQhQjkvtQi9bGsQWqIxISZ2/ZJTaTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.2.18", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.1", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.5": {"name": "stylus", "version": "0.6.5", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.5", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "5554f6b058cd4874a393d5e8b936b0d6b7bb5d23", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.5.tgz", "integrity": "sha512-uQBtCzhZbsyedgAGjSUH9JPMJWZ+AKDKwBjx+fUJSVO3+axwZ30Mu9Z6IO1xQSQKeuazzFjaHUSNgPdr/4EKgg==", "signatures": [{"sig": "MEQCIHM0fF8qEga8oKCxqscHm7ls9Uxiwa0Y6ItbVw+ABianAiBWrmHNm3v+7qK/KK/lc9ALe9l1nZ0oiVZmzuZmJeDJzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.2.18", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.1", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.6": {"name": "stylus", "version": "0.6.6", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.6", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "5a15f7c378de4a646719df80ae61a796dbccc10d", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.6.tgz", "integrity": "sha512-ah4/SByoRSSSrsjVJSwym3gxxBjjT/1M8ArTQIcdDPiSAC4AzgqL53Pw+vKvmesJKwNTASNl+v95j5sixuvA9w==", "signatures": [{"sig": "MEUCIQCczrbcEWy44NiGAtjz8R5X6nqkYCn6Uxs+ISy0L44v3QIgbwNsXsKbqN+/YXZj05pjgpX0fcHREM2orcqfbJi4rVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.11", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.1", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.6.7": {"name": "stylus", "version": "0.6.7", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.6.7", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "7be1c6ee01e326abc7b98cde6f36802d4a7494e2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.6.7.tgz", "integrity": "sha512-mFGRZvdk5ZXfqxDh8Rg+zhNXZmbJkJd2Q4GFDsB1X3nORDZFcE6BwGtMxIc6u1I/IiKLz/5O1pp6QhsLX/zJbg==", "signatures": [{"sig": "MEUCIQCTE94fholzvwhS1fCO0J/LZZOjvTHMZF7LtBlPLLTpngIgbg6sD/BE0uqDaHZOa9X9W1UbKL9oAuG40i9rlz1fIcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.11", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.1", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.7.0": {"name": "stylus", "version": "0.7.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.7.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "f8269356cf59f8e5f0100f626afb20f6ceff06b2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.7.0.tgz", "integrity": "sha512-IeuVAvswejHD2sz6JuUgPC3xQXVmA2x5Tr1PR9gZBSrgQgw35xS4nUGZspUCRbmemnSALXbEzv9RF7PYAJkvsA==", "signatures": [{"sig": "MEQCIA5j6IS4pfZaZ2cRD0cezEkZ5hAE1x3yoiYT74Lg5/tuAiAaUtgCKNtUFaaKaSvRiwKpDxfVb7XyYI9JoeCuLjY71Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.11", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.1", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.7.1": {"name": "stylus", "version": "0.7.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.7.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "c83a5e834bf336d75d127a6402a66b177d23715f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.7.1.tgz", "integrity": "sha512-HRINll3zJZYbJlUvy3MIaptv/a9Toawf1I86SuMBhlwsPvh9uuIuIS6iU/T9MRVq2mxsSTXDqLaFYekHss1MRA==", "signatures": [{"sig": "MEUCIBIJabV6nD2BtI+kAKAPZU6QUFFgRWAlPLb3SUdpSOdiAiEA/5+C9IRMkkPWbGRV7Fx0fenHeqSP/jfIBe0n2cp9+vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.7.2": {"name": "stylus", "version": "0.7.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.7.2", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "dde792c217af509f41db888e6e72bd55059ec1c6", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.7.2.tgz", "integrity": "sha512-BZUTR9e6Vpd79D+fkxUCA06aSd5NspCvskPF7+ft5I0KRW0uIlq8oLCfwfbQkK97JVRwstxYjppXniptbWQzzw==", "signatures": [{"sig": "MEUCIARv+H+pePf76h4fBxUlhypN1pYQB9b7jCq+7eWslxzdAiEA6wgZvI93S+1ugGnnjfQ1q73xd1EB1PZ/YAGjvK/AOnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.7.3": {"name": "stylus", "version": "0.7.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.7.3", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "1f11ca1bbb5771c10d657e38da2e30a4d8918904", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.7.3.tgz", "integrity": "sha512-s7gZ419Wd8eC64LD42s0wTz0JoZ4D7aGZ+q0zALqaFKp7El2qe6gG+M78ByqNdPQddKyrExbKynbKWrv6n6fqg==", "signatures": [{"sig": "MEUCIQDQs6Y/X3aEKDQ00+hOmbSE+dthwj66CXqfcT3/mDIrlAIgcLVawB+jABat0tIWLHoPKAI2fPMlb5mUfC423dAWES0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.7.4": {"name": "stylus", "version": "0.7.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.7.4", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "a7a85477fe906bc9367aec4c9d11d6371433a85e", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.7.4.tgz", "integrity": "sha512-D4iPGPsw4bBk/T55aSeCE9ro5R+am2yqn3AAJHBZ47/+UKCb2P28jQX8USYMTEzeV1XhSSbfkNLg3Qfs0E1Yaw==", "signatures": [{"sig": "MEQCIHLy3WWynBiexhIRxeCUpEtH2HZymC3abJJeH27B3W6cAiBPa7jNVF4ELhfZsIWJFysnQXxHMgs672umEFZm4xrAmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.8.0": {"name": "stylus", "version": "0.8.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.8.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "72e08462108320ac05d36a6d2b2749fe13d23857", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.8.0.tgz", "integrity": "sha512-So29rdYq6japdXVCXy21PHH/77kn5tGPygKh1r+hLjcWqrBLkvr9BFE2O3eDOGmcN0BEV/MKBX8L8VL7Cqvw7w==", "signatures": [{"sig": "MEUCIHLMrkDi3BcNH9qMhJ2CtXrsdbJhQQ+2lKJCgkOSLZEoAiEAklRUJ50vHoClH0r9ii/6vbdx0INYo+oTHuCRYB06pv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.9.0": {"name": "stylus", "version": "0.9.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.9.0", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "3662b65d87fbf7fa3cb63334ce915a4d00a1c719", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.9.0.tgz", "integrity": "sha512-pirtM8M3uFYo88E3NAzYnyJMfbSL2StP56q9u2cXEwlkRq91O3TDp1mD/72XlTpi6iHBYasOwisBCZallBayxQ==", "signatures": [{"sig": "MEUCIHB3ZDiBF2+vu8hCMJnfuoByuNzJhZJ8ryCwFktQ1iNJAiEAuG30auTGH/Sqor3HQCKTKfCxFtBh5cJyD5q/+RRDbWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.9.1": {"name": "stylus", "version": "0.9.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.9.1", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "1ee4831c2f76c4d4b0e0c6971d6d90420e896292", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.9.1.tgz", "integrity": "sha512-MO8G7yW3TsoAxEAw8H9+w7RVJz52Ek6WiUVFLPawJyqyMy1vbN4UZgD+szt3zoKjfAUe39B2WVhwbIS+p1b9BA==", "signatures": [{"sig": "MEUCIQDVI1Xz4fmZKUhCerYY4i7FCxHI8gZBrAWCi+2khXbbFQIgHG6cEU724mgV//SGfN17k4CHLkS3U4cVU+pW1LJzZkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.3", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.9.2": {"name": "stylus", "version": "0.9.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.9.2", "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dist": {"shasum": "ee7c1a7fb74d6211bd64eca1003e3a9c1c3fc019", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.9.2.tgz", "integrity": "sha512-3qSXQTJAS4JDUBXo2pUsfpcX9hvcbYTiSje2+8Teh73TlZ1eQ0WWsrWlN8d+6S8KS5yi1gngQvI9Fjhu+dyG8g==", "signatures": [{"sig": "MEQCIBT+RfdrQyB9KAIAxGqJDcf5vvzjbALGB8N7B8V1U0zMAiBIsGjcfA6G3yr1nKjafHP3yVTt7Fll694BjzdeqtnNKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.3", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.10.0": {"name": "stylus", "version": "0.10.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.10.0", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "814f106cf6ff6a37418fbce8a39e9ca7d1d2bce7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.10.0.tgz", "integrity": "sha512-KOiH0lpXqRkddJ1fIx/3+movjtugXwkTQW/7jCTl6zGejs1Ki8toXPuvnF9g7Ql8dfQblgbBdpoeS//DGPj1Cg==", "signatures": [{"sig": "MEUCIEREkQAwBiphhe6H9pTkM1O7nVrwGT8Lwhr8/FctXLvcAiEAzoY/cmUop1nGfiRDGgoqA3H2IHxe3wqQH1aQpdDXsHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.3", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.0": {"name": "stylus", "version": "0.11.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.0", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a48653a64f3d2d34123d5d0cf27f2a7010c8bc66", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.0.tgz", "integrity": "sha512-TWIR9f//Y2oYwB6yXtOtmKuX3Un7lcZFNWEPKvzeVee0HpvWFiEFYdTQ4/3p3WuMMSLg7eUgn03klrWhXr3JCw==", "signatures": [{"sig": "MEQCIHEPL4vJRwC04WZcobvbon9YfdVx2lu/l9hAjE41J5UfAiA3UgD8r9aQnhu2Tmx3UI3cKSn+GoQ8Wv19PyBSNK4QOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.3", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.1": {"name": "stylus", "version": "0.11.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.1", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8aa94a8e82f88b955ed828c5b93715b61072a6d5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.1.tgz", "integrity": "sha512-jIxI0M5FpmRDcDNTBsBfmSkKvdoSHrdU/PDwO/do+kPuCmeqvJUFIMxVX81dJWqiQCnofWZPp2e02ylxk2Kjng==", "signatures": [{"sig": "MEUCIQCf0GNxsQZ8H7TKNhkfanq2sGLYtO6mCXQl+eWw8x1rfAIgaErvXZEdLK6WF1HV8vQvkoxbWycmEyeIjc3AdDTHZK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.4", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.2": {"name": "stylus", "version": "0.11.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.2", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "602dd4aaa878975cff1a07d54330bf5ab80c4d48", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.2.tgz", "integrity": "sha512-c5yBvVJagxMAsREz299uaCIe65EP929MPI8ScDkNa7dR1+TzLboMZcqJjlqzcTNelxa5duKngiurKLPvc+HLKw==", "signatures": [{"sig": "MEUCIHHbfGS/h8mozWT/EkrnSxBD+V9zeYfS2AFv90rGxwdsAiEA75YsmllM+oQwr+/rdlv0hWAIHWdk2TJiWVDFYHsWmh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.4", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.3": {"name": "stylus", "version": "0.11.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.3", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "b3d13e11d5bd1a207dc77d687beddd1d732911df", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.3.tgz", "integrity": "sha512-UIGU5pw+g/itFAv1Xi3zdO/lt08ye+MTQyDzXJqsxgGj28HLEXrqwQT1a/ZicSYCxEnEOQwy+yrUx8WWQVMrNA==", "signatures": [{"sig": "MEUCIE2Q1hrpUogPOEhe5BMw8OOGFrJZKm63YiO8ji98rZWTAiEA3JIHnK0ZsSoNTgibUyzfrpfbfsP/VWtzcqFRRx6o5T0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.5", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.4": {"name": "stylus", "version": "0.11.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.4", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "149b8480cdc5da7a82388f1dc8f169b26b84b094", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.4.tgz", "integrity": "sha512-IIUCUXYNm3ZJFZU6FS6A3IsznDMFYjsGp9rYyWIjyCLp3ks6Hm2Tzv3/M5mr3f1eMP7rkcZSf+Nbo5lDSDA/eg==", "signatures": [{"sig": "MEYCIQCk4HN9detTX04vLyA4kHARmkWoe9MXqbdMMAvefYsaBQIhAMU9GmWT7Sds0BAJY4YCEkzTdIamfdVbEcbuAKm+/F34", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive language which compiles to CSS", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.5", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.5": {"name": "stylus", "version": "0.11.5", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.5", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "eb9576473d32208186d9bc06438b756e892eb856", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.5.tgz", "integrity": "sha512-jQ2LeM3D0Dk+bKDLfhvWufKrv1QHyz4e8UwS4F1JvHc0b2ZghvjBCcDCCBRa7lwetrXrvOGU11d5JwGg69Fi3A==", "signatures": [{"sig": "MEUCIAHGDc+n1iqLrfGnIh5617osDL5vPsDeO2tCja99eTOjAiEA9qVTOcnNJIsdzXonndAbmPEigl5q4AdqZ+eTafaFSbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.5", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.6": {"name": "stylus", "version": "0.11.6", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.6", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "b2802c7b64daed3c5319bd480d988b3ead42aade", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.6.tgz", "integrity": "sha512-lpFHulieyPa97sVBW9Ct2FPfDSPTgD7xpzK2/7kcHew8BQmsEwirOHdhS/uEbVOXg5OUJzN3+7LP9XE/hFnXow==", "signatures": [{"sig": "MEUCIB80fa7VJIMpvcAoCqyd4ojT/vCVniBUEyhHyRPXKeaLAiEAyd1yjTOiZ6I/6w9wb3Tqyz2nEcNIhejrgV/YiFUN1Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.5", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.7": {"name": "stylus", "version": "0.11.7", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.7", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "7955acb22b9253a617f2a015717281cec300e294", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.7.tgz", "integrity": "sha512-qTYWHr/i/Fxi3UILDPxx1zBYreUEUA+566EpeUZgzLEsIUv01cC6EjC0QfKSjBiigL+Rl3ZkkhuW1bEg+/4Gvg==", "signatures": [{"sig": "MEYCIQDKQVwu2CMgIEcwjqHf8unX1RaHuMcimNDFWDjLBUs7dwIhANLFtkUBFGS/R8nbzN/oaAo8QUYAsVfhnPSFmQKivf7p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.5", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.8": {"name": "stylus", "version": "0.11.8", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.8", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "2910f56c092b1802863ba77a51196dd3f62121e1", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.8.tgz", "integrity": "sha512-vGEw2QcP3oIabxqeAn2zMiL2lgvHTNRx2vbv5ivgZEb0XECbBcMYYRiFaPQ8Qq7dWZ4VWLsTcma4AiXuJXlArA==", "signatures": [{"sig": "MEUCIQD04DuKWq5vrcWT0SPK2GE2QIB8ZHapmYtMEMj6MTCLyAIgEWvKabkmVBh/1APAXYqCPq2Z746p06fY/EbrSCJxVYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.9": {"name": "stylus", "version": "0.11.9", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.9", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "e11085c54394dddfd1ecc458b31f36a1511889cd", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.9.tgz", "integrity": "sha512-oKqmH6Z5wGQVbeMpMHRpm10hflegA0zEwF/24OtGzu3/chZBa9B+3CTE5DDdlGVfedNY7p+/OMhAJrj/jrM6hA==", "signatures": [{"sig": "MEQCIEc2FKGLNq7s2TYB50fXPsFnabBioe+HNBCs3NfnCaYRAiBpDFcwvTEBYO+u90LNpcKn11rloTrW12/8so+GSZIq5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.10": {"name": "stylus", "version": "0.11.10", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.10", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6903bd0205a800b93b6e445072d801475680f59a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.10.tgz", "integrity": "sha512-J/UIdRdhKmwEK3uCcUTQJgMnTRNl2ZMQs2ygMPSBiaYjVGx2O01u5dsqRyJhTSNOT69B98SIYQybLnZTmvEf6A==", "signatures": [{"sig": "MEQCIGWNIfp1f6PjJBEvgwQUIkW9q5prhQU2QjF3m0CyLAsYAiAJHdvAryi3cXE0R4dnPH0iz7T0fNwxemDh1j4ccL03zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.11": {"name": "stylus", "version": "0.11.11", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.11", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "cd2d4cbdfa0b77817c1a58b2614e04b8917ddcf2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.11.tgz", "integrity": "sha512-kRlsDfwXaGiBcImR77g8zFKpfStbwxUlML6wAuQPuuWATO+nEKMMfPETX3KZc2ghsMbFKNOf6EtQkw7KuvKS2Q==", "signatures": [{"sig": "MEYCIQCQ1LXpXsnQXO9Mq2D0tDWc76S+cGK8hfpbIbOxRY+W9wIhAPTmq6r7W5h3+Q4Encba6+yCneFpu1Yz8GxKhBy7gh+M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.11.12": {"name": "stylus", "version": "0.11.12", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.11.12", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "2bd059d75df5e37b02b692527b8f9635f45b62a6", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.11.12.tgz", "integrity": "sha512-l5/q8xy4AyE/vfYyLSsgBHruXCd60RMqR0f+b/Lup3plpeejpgsbR+P90nepBijdfn81v60VRdTG71PwTOZl6g==", "signatures": [{"sig": "MEYCIQCBi3eXkfrdmzLFq199xANgE68ZonEMfiL7GWnV2IXV/gIhAOWElc9vm9rfYl3s0krEVZFRPRkANcWbx2TUVHkId78p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.12.0": {"name": "stylus", "version": "0.12.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.12.0", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "e1b938486fd4fabaeddc23378c1b33219d0ea63b", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.12.0.tgz", "integrity": "sha512-/BAl6G0ryr3zZPekMhWqPkCshJjNK/Dk+jpNXkMSwdMwzEo2t6ddxpMBkGuAwEl/fX0RSYPaZQzb2q416SiUFw==", "signatures": [{"sig": "MEUCIQD3YUhyk42JNCDEDKYrq+4NR6Lb3lXSOTZ7zMMs/bVfdgIgHcHPR7D7vUtwOl6oEI35i07iXr012FANXDA5hb4B5Uo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.12.1": {"name": "stylus", "version": "0.12.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.12.1", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "9409351cccf304513ac9db87c86277032e54092a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.12.1.tgz", "integrity": "sha512-94C0YXArMRNrRKK0A8VeyzwYbuL2OGj/G6IduzY4DHWpHptXlyMDaBRr82UvO8GL0lmD2GsVhLCizLcaSsG//Q==", "signatures": [{"sig": "MEUCIH8GYoSsBxaY/txSRKD0hTcAvZqDBH/mz5mqkm42luq7AiEAj4TFJxVQHNCumGfz0RG97aXRNscLG96AZUkczkVegCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": ">= 0.2.4"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.12.2": {"name": "stylus", "version": "0.12.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.12.2", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "52987d4a416fdd390c3070ff89a4e66ebadf8fc5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.12.2.tgz", "integrity": "sha512-ry2MpDF6ZOjLJUeBA2pHXR4Kp+CnGCKlAqKcqZm87xRa0Hk/cmv4SrXJBulVGbXYIu/p8zxlKXxrJs0dTaMM8Q==", "signatures": [{"sig": "MEQCIG5AduiwuuPooRWdPgcRGmOG58lWMddJrBHHilSGJ/yoAiBAMXrkcZ6VhlzLQabzvxKVV1qtvSs/Ne4wGJ8iZLvJSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.7", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.12.3": {"name": "stylus", "version": "0.12.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.12.3", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "165aac072d2ba06bf8cee4046a7aeb373a0b810e", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.12.3.tgz", "integrity": "sha512-HhbjiKiNReutWvCxlUPzS6S/Xtuw5ku03KIVD5ZmfwdyXchco5XBRivgv/Jh7faMVYPYAVK7ORYXjFrkcOFV1g==", "signatures": [{"sig": "MEYCIQCiX2wseZcTCOfB2JQdszG+uQ+rx4mrjU03j7Ez3JIu9wIhAI1Ao+q2X5A/aotNMEWeGsAkGgOHjejBR/AX7OkBQOv1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.7", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.12.4": {"name": "stylus", "version": "0.12.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.12.4", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "ed2dadb8f40a238fc3d7b8e691ee02ac5ba36e5d", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.12.4.tgz", "integrity": "sha512-QNUOhhcJ1r+SVlfPKoN1vL9JA/hRqYcO23fHtJTknLXELSFljUlFfV/DfSCGrGFU5B1IRps/o7LVNdra0jP1zQ==", "signatures": [{"sig": "MEQCIAZuln2T8YKijMqqvUToNSBa5FoJPnmxoR76F9/R/3RBAiBm2YWe0z/6oaJw5jmNS5P1Kfa37WqiswjS+y8/0s+yeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.7", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.0": {"name": "stylus", "version": "0.13.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.0", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "37b69909544a0aba26a2914cf18fc6d80988c7b8", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.0.tgz", "integrity": "sha512-ue0TiH2m3PXZzhiLSmp+ieMMHTLvry62DNVE16mYZtemozHvnVJJH9kJ7af7RonoADXwhX/PD9RscRORAVl5WQ==", "signatures": [{"sig": "MEYCIQC9fkZfAguZAVYddrsaQKSIZKhlV38VNRLH9nu+6WCuUQIhAIKTXqwoEUu9iJIlMv+xpXqkGNrQjUPJr0e5yIyGIsXV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.7", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.1": {"name": "stylus", "version": "0.13.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.1", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "32274037e1ba4f73ec50dfa0e70526052a41dda1", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.1.tgz", "integrity": "sha512-ik6k6KDGVONegWLUXTzqHY/X/XfuvdAXqmdes27SX8ZxEipg+ejlyfi89qJZQIrLKm01w67INBrexrBbWCrMTg==", "signatures": [{"sig": "MEQCICqGTLM0g8CIAUxJMs5CgNxjnqO+OUZaSKdWbVCSApoYAiBJGnSUCs6d1TQurdaRUg41FGmXUfG6qCQytQhWahyOPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.2": {"name": "stylus", "version": "0.13.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.2", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "52afd1860ed017b47c6b91c5052bf98562df2768", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.2.tgz", "integrity": "sha512-GQRgjn+ViXvMN495kelUgXy0VIKPVQi98nAtVxNz1i/O/mIwLjTEWI7qGz9TA5aFspnXMPGQBmK+m9kA8uy+/A==", "signatures": [{"sig": "MEQCIANNf12juDSm5L/XKScftT71MyOW28hUhwbydFs0UTNyAiBABBUd7NZLqCMXiAtSERLBTkE/SbX2KSoYKSXYL7FFIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.3": {"name": "stylus", "version": "0.13.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.3", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "5930eb29f4b148bcc6d5432582f10f0bf4ead75a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.3.tgz", "integrity": "sha512-rCR+p3lhaPHq4sBl7e0cx3FdE8w9m6PdE8y00FXZt0Qq5Fds137i4OmXPhC339VIvKodI6r2pG15SgMn8s0Kbw==", "signatures": [{"sig": "MEQCIBttvEmLisGmoZ2ipGEYC3m7lT5l0vwxfg+Xl4xNBiIWAiAq5TIi8TyuoAv+fF+qXS/cQz0DiNvOeqg9euZaZpog1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.4": {"name": "stylus", "version": "0.13.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.4", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "08a679c40456b19642429b6a99eaa2a2045147ea", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.4.tgz", "integrity": "sha512-gSxy0IpERLZjUkWqUMOHo67mhg5pCmkFfaLqnYg9ON3BgzvBZDfOLYf8FsP2VHrhZEkauTuQgCSdZp+qIlvRcw==", "signatures": [{"sig": "MEUCIQD1gaxZI4xFSBikZJrz967tSXr4tlzvy9+n+3TWFno7jwIgUy6T1qME9VAfSrRQlDaUSAiFM/lz/UkZAYiNAV5IX3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.5": {"name": "stylus", "version": "0.13.5", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.5", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "214590b38388ea0ead05c7b9f1bb469aa31cc9cf", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.5.tgz", "integrity": "sha512-vXBQTdf14gGVe3pyXDFhkMEL4W3EPUiO4EMcCULd9/e/z8hiM9p5QUnrlCMDRcIxlUL+PQ7Zg0XD7X8sSMFj6w==", "signatures": [{"sig": "MEQCIGGwiNzYzfVGes5uFUg5og6KDek5vW2LiovExYEE4t0bAiASwCtGbVw45vFi1GESYM9iLBzqvSHPhNuT1cQSxBGn0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.6": {"name": "stylus", "version": "0.13.6", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.6", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "0b42e5cd7e7fd79809fcf5f5b521ac2a8754b345", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.6.tgz", "integrity": "sha512-GxeQhe7fQR4Rb3h0g3pVlFSh+60Pb9Mz94/uNW4Ozc9GbAGHi34qgtetUl4ZFuHk7WpHGEMv65fupUH52lCwjg==", "signatures": [{"sig": "MEUCIEoI3m4NgKv8WZHwPEI8bWsDlcEuj4xvWctj5NcCvE+1AiEAtbwniCGd1xThLKi20bhjFZYcPveS1gRqRpb2o4Sr8NQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.6/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.7": {"name": "stylus", "version": "0.13.7", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.7", "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1f69251854de2e41164db4f2b1f5ab92aef31fa5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.7.tgz", "integrity": "sha512-V+GEzTTTF3nq+IWRV24MrbZfgVZwr+75KjpxTWVz2GZs4TZjeN0tlOh0Hz3If7f+yPpJbmTRW7v+U1Ki12Aj3Q==", "signatures": [{"sig": "MEUCIDVheD+Q3QXSXUIG5g2LWDgoyK7PX5p6sWg4+x6ApcqOAiEAlEUDCviVXYIUBTSCQJ3+2wXID4DxTmAZVObVLRo1UeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.7/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.8": {"name": "stylus", "version": "0.13.8", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.8", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "555ead6c018c9dd4763eaf1af7c183968703599a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.8.tgz", "integrity": "sha512-bf8lCFCuWemd/vtjIKvRyuit9/IMXRbFraCWosOg/nHTt88WCCe9XBFbBJWn6j+BqrZtob0e8cvOqhlSDfN0vw==", "signatures": [{"sig": "MEUCIB+ZYJpL1tVHZ9LyYfXTtuSU9luq/3mkBtN3T2Zjg5xFAiEAm0V1TWmR+kRzIMq4jiHck3gpvZEOJe4z4wFXlHYRa+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.8/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.13.9": {"name": "stylus", "version": "0.13.9", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.13.9", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "7472e4d753849833583ece0a13d3352400a53547", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.13.9.tgz", "integrity": "sha512-lgok3FwQUjTd1yrQMKgm+JuEMyH4/qyGRfoH2sVwTOU2ISZgFccl1FrOsL1QfT4hqIb03Qb00fYSPeEjCONa+g==", "signatures": [{"sig": "MEUCIQDxj/uD7CSiFk8g+xlVG8HdBgolZ07cJogAn9GYmXylYAIgJ7WkEjicp1VbcBUFYPmMVPkyrVoHWy5DIAjSKztjMI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.9/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.14.0": {"name": "stylus", "version": "0.14.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.14.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "f5c21b8f96e9c579d1e2bcc4f0d4b887c414a8e7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.14.0.tgz", "integrity": "sha512-cCtWwEvuw/K1hY8H+sHP42RpTzUw+hVAInF46XHRKZ6p66JJlDHBRvLSE7vC6H9Ka6xD0sp/KYazqDZgGBLTgw==", "signatures": [{"sig": "MEYCIQD2TfLRbtnbhP8K00QEjsDr0LGTd8n0r9TLd9lBpBZQxQIhAKGJZpBc/QVNlU9i8Y4VKYW7QhrTrzH60OHgb77pG8YA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.14.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.15.0": {"name": "stylus", "version": "0.15.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.15.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "c0a608a0da676dbaf4653c741fd1c8b033794501", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.15.0.tgz", "integrity": "sha512-Si3RUUiTAcLiL6F0rM9zahrLMJQlyj9Ue5Jh9e9vOG1r7aSD1ectAmc/X0ogxCpewtLvR7Pd7/Aw6JqghTsZjA==", "signatures": [{"sig": "MEYCIQC6D0cqLarLMPHU/Yt6dCuv+Vr8Mx9Vu15o/bQZSBam6wIhAMLr3ohDejitu01iFEX4cu+2SR8FFChAh7YgtK1N+/X3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.15.1": {"name": "stylus", "version": "0.15.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.15.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "671556400c58386d7e94149fe8012735334268ec", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.15.1.tgz", "integrity": "sha512-+bchftj4XYDqFO0YsQW2yLSexxaSd+tuIciBRiGebe5wEJUkEbXv173eXudYodRDlax8IgK2O7CAZUp9QQje9w==", "signatures": [{"sig": "MEYCIQDRAxRLP55J4b0uQeC0Zh7b+zGFDgvkuVPCMoPEsRfcGAIhAMs2+k9wj3Sz0+TV3mrg99q9CfAZb7PQUQ3jq3QksOXT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.15.2": {"name": "stylus", "version": "0.15.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.15.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "af315fe3bce7d1ec97b82f62d1bb84d771647447", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.15.2.tgz", "integrity": "sha512-sG6L9PNkTbNWseGE3ViLlYKzWW8DwZgriN3GIqz19NUd7s5A4J4yQgRRXZftE3DVCpFsFqrt/+urHUkk7fBBOQ==", "signatures": [{"sig": "MEUCIF+6mBSCyXgMbvYYMurpZfOQNAJTyxpd/FzDvyKVjx/RAiEA6dslXuKuJMIkJsXjCIJ2CzLdCSZuPR+UM29jPxh7voI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.15.3": {"name": "stylus", "version": "0.15.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.15.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6f09fdd69638945e12bb76b387c37cbece7c1b1f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.15.3.tgz", "integrity": "sha512-yQwS7V9wo4OFepB/mSKI98n0fSaVe1LlvSB7RDFOcQBveepCZZjysKfu6pDfmQVYXEBcQgRU3y8c8vpbhp2tGA==", "signatures": [{"sig": "MEUCIQCr1KT8ysKZpkZo2OIr+XUPlp+FfmWVHt9XlKe9tOKCoAIgDdIzCMFivfzfQ/Ve41biG4hhr6hKFgU1m+OtvtdBctY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.15.4": {"name": "stylus", "version": "0.15.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.15.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "63ae5b3eb72edeacbb23265c034eb56c1cee07df", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.15.4.tgz", "integrity": "sha512-KqmADhkWXlmu8QKU2vQzJx7Tnj/rcu8XJEyJvMoR+HT5qDW/J/IWDF2UMuPHDtHDUMgIt7nbhzbU678WMvL+5w==", "signatures": [{"sig": "MEQCIGzTlMdCixILn0UW5Cxh4vOVDXymfxVCvxd5r+d+PySmAiB9cgj6Qj2HDkKu+ZEcb62/SxESSylG0lZTA/JL+10apg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.16.0": {"name": "stylus", "version": "0.16.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.16.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8bf10acd782d9375dc6a6786e95b20f211c2b6ef", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.16.0.tgz", "integrity": "sha512-jISZoUIIp4vbbRVf2BkzVQSlJiCogNsuXm9pZtKsJGuewaZA+4KTeNlqIqqvP2gwfeocrPajprA2tT7e+veyOQ==", "signatures": [{"sig": "MEUCIFnD80kbn3OY6BKXgC6qZFO12USxtS9xW/PCwKag+HuVAiEA2uaLYSzUZ10ayhExHamKoSjC5ftEXirKZbqiws/IW7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.16.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.17.0": {"name": "stylus", "version": "0.17.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.17.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "85e43dc681fda7329350f412c6de2192121c7f79", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.17.0.tgz", "integrity": "sha512-iwGFoYAIZHvXCWjsV7C0t0eGABJY3gOQcRA9Bh7L27kTLqMzjyhEcfxfsrqe+My1CUasjhgfCp7IOQEIwd7NqA==", "signatures": [{"sig": "MEUCIEohCQzpNLi0C4FXX9hGl2dciU9D4HYvkSp52BuMnWvJAiEAxs4ch5FxTZ1KnhG/TugLEaJZurvqicKqJFcFmvepeS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.17.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.18.0": {"name": "stylus", "version": "0.18.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.18.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "0e181844911cea1af80181fca35c49534e58b5c1", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.18.0.tgz", "integrity": "sha512-R1XzU0lm/qHS+IOsdA1sWoI4LKDJvyTSAxxI2swoDs7fhjIVtdEMA4u8BkugANIzPhli7BKuBBP5y05tcG70FQ==", "signatures": [{"sig": "MEUCIQCtuAVBhrA6vLSrhqxsNsQH2OPgBMXzbsFRcQzP8KvZ0AIgI+sg5j1LZUj/3j5ZX4AXE+Bg2n8EzkknE1G/5nnMRDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "scripts": {"prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.18.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.0": {"name": "stylus", "version": "0.19.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a0368d41ca68c79eb9a59ea9bb05ce59fde8c214", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.0.tgz", "integrity": "sha512-oT3kWuZmMZPBabOVWbJmFkh+NvLTDRtBWReumrhZFAW2McusjDeeCDrC7YYTcZ9HfoxRrNKDHxeHd49TzX5FQA==", "signatures": [{"sig": "MEQCIHAewqpxrYc2Hw6N+eGH42rEJuuERNp1FHfEE6T5Mg2QAiB7j5bbLC2ADFVpgjPHACa5L/GLunkID77adNDC9e4h1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.5.10", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.1": {"name": "stylus", "version": "0.19.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "467122490c9c49a1e79b662476a7ef7242a6a1e0", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.1.tgz", "integrity": "sha512-q5Pt2+w02Z+73Lpc5lD8sijvEXa11Wfar3Pp81ugzUTtNE7GxdrBrk1qL4uWT2k+EWrXMT95Ka7ubnmPoctihA==", "signatures": [{"sig": "MEYCIQDM46VDrYHkF86+eNLIWEB6kS+oUR7p+kENMXPgmKwtuAIhAN0d3H1CL19WEA3XM0DBQ+MDh/5dq/DV+PZr4K6fkxRn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.2": {"name": "stylus", "version": "0.19.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "b27c23e82697aa01e08ebc7449880a3655a9a1b9", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.2.tgz", "integrity": "sha512-253YK5dzvOZRMnz5dxsG8bKoyTsIVc5a3LIHTVW1Ocipks11ZIX/Yk69+4NxQLM7RTLyxmKq9xC9FUfLMx6pRw==", "signatures": [{"sig": "MEYCIQC6v1JFzJD6D0Y14u30oHcpl6A8BmJY4Nsye6C3xYgyegIhAPTsFsvmSD9E0yu4sa9/IK6RbI37XK9JJ/O4i61H2Iso", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.0", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.3": {"name": "stylus", "version": "0.19.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "ecb1e81d57e5b322adba59c75349145c3534e9b2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.3.tgz", "integrity": "sha512-5lHZYMK+x4GHliqDXSgWxTsLxx1/263Q9cagxCVtNnGIHpqSIcKO0UiN8hYhdR09etsvatseuAFX5bsaa6EXVg==", "signatures": [{"sig": "MEYCIQCMLVeXEFglkHadA8C/p0uyI05aUoIDwi9M920M2gaLhQIhAL8izs+lRTb4fXmE86FMoTj9vMf1XCSP08SPqHkLc9/i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.4": {"name": "stylus", "version": "0.19.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "213f285e3052d3c5164dfb64fe9b55872d514d36", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.4.tgz", "integrity": "sha512-Id31m1WuJzqkz4YBN8juyi+U/VzilvFnsJ2v1w7qAHU7lsLKuv6HzVGkXxc38LSG30Z2/ffqXEnINUOX/CBiLg==", "signatures": [{"sig": "MEUCIBLKqA0vwx/wCj6haaoigykoOdN+hfOM03XLuyK/cug/AiEAxQsEKsF+KYF705zmTkbEO5eFtIUuY4y4sPeZcmSyFxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.5": {"name": "stylus", "version": "0.19.5", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "0a2ac440736dbfe17c2f78a3293ca8553e0b24cb", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.5.tgz", "integrity": "sha512-vMdC0ZybAY9gIDwnO1IAD6gQn+jA9NjNulLgdoNirCiEATVYXjeHIAUPatw8U/+lxPqnZlURzfj3VjvdPkA17w==", "signatures": [{"sig": "MEUCIQDH1unTPdgtHUBKLnKZD1IuaAjdvstp3wEMXMk7jzxdBQIgXmOVs4RotmuTTBcT/ZCFZyOfzUxcYNtvysYHbM4BNvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.6": {"name": "stylus", "version": "0.19.6", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "e227d788df628aeb0bb3e855c534f1cdfe7cc7c7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.6.tgz", "integrity": "sha512-lEAEiw9xF42KSxHSSg0JX4c/TU31M570286E/1RuEejJ80c4mPt45FOKsMTU36ivg26aeApz7zkOMXIaHLLZ/A==", "signatures": [{"sig": "MEUCIQDbBfoK7osD+R4Ih0OaUVFjf0S3ShGdWoNXNGLeBSY+jgIgRgpru9FXG3/qr89YcEQuKvmXI5jq7wG3MC3LO7H1FRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.7": {"name": "stylus", "version": "0.19.7", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.7", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1f4f9798be218cf57035d110f83ee6b0d1c712e6", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.7.tgz", "integrity": "sha512-7cnZOXmfayq4n4WrCBxgcyR4qmkA71tQdtYNITkl8AbAiVpoX2uxNYalqVsoGvZFFVwHzgySqRSQQSLtPr+jVg==", "signatures": [{"sig": "MEQCIGd2N07MzfOi+pO1ZyQArGP5BlUdpej5W6nbcd76nYc1AiBCte1RJc9HeBXQ0oP3p6Gzf2+NbU2+tVIuI3hiZHdSAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.19.8": {"name": "stylus", "version": "0.19.8", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.8", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "07bd6940d66043dacdd5ef5a7b4423ea753f1254", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.19.8.tgz", "integrity": "sha512-xQnGG9Rl36Bc586WDA967uFfRrMOCp/wtyY/zz912KhcJn/pANaNWDyksVN/9nHUcxZrguBRur+ep/3EcsRHtg==", "signatures": [{"sig": "MEUCIA5a1sVkwQ744BDYyDVbH+6hZ1qbldJeTHCXAvquFQJmAiEAk7a32RRKtvLd5HDXO/GKtUbuxaE5n11mA8ZGDsifLTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.20.0": {"name": "stylus", "version": "0.20.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.20.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "3793c88361dff3dd98b440b8e4c93fa38e442c10", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.20.0.tgz", "integrity": "sha512-yNdTgHkGoUHC3o89QDTkAUgTP2r/WdV6fnc653+3mG5030ql4KVjOUMEy7gfV0EOfipXnNbYNTn7cO/iaRHqfQ==", "signatures": [{"sig": "MEYCIQDoyPz0b/vcvsGkoszmfHpoP1OA6gESRx9Y3MwaDAObwwIhAJ7jtn23IqvdEP8XnbQIJmMM3VLDgoZOyR94915boBZK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.20.1": {"name": "stylus", "version": "0.20.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.20.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "79e739091db75b72972ac34d69d51fdc74ebd62c", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.20.1.tgz", "integrity": "sha512-H2nXzfMX6bGZ9oA+zhDtYQ1M0SDLWZVJ7Kpyeq6IvkdDrgaLi3SnB/hM8XZesW+YqMsb6qvsP4Uc5IHBVqx5KA==", "signatures": [{"sig": "MEYCIQDhvce/a673cdGUDoLP7U1ixGVxEmmSGVMMu/qG512J1AIhALI/ME6RAikpLPIIwZP1SMDIDa+DJrGu/5HK/KtGeevb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.21.0": {"name": "stylus", "version": "0.21.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.21.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "7c857d4c4d34e669bbe6a3ff3a6946a3a96e51e5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.21.0.tgz", "integrity": "sha512-clrVbMg0ewIpZ9RC0OnPJTlhgC2W496NTFNx4quXa9OON4h1o8mwprgUay5W58UdJHchs1tfCXwzhyf5UqDV0w==", "signatures": [{"sig": "MEUCIQDAfVLaEkzq+PvcG5K7W0vm3ATCFfJUsp7nxmkb9W+57QIgdJEYwWkiP2xfWzBUCDn2vP8oKTdkpQWHyMUZdWBRvXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.21.1": {"name": "stylus", "version": "0.21.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.21.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "395b3b6843f5bcd2418e869c151ddc8d7edfd5ba", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.21.1.tgz", "integrity": "sha512-pUgb1lOUwIwp88DcIe/9ZMeeLwyHDjSwJVZsPz2ey1KBmylZGNvmb3IJibSwTJYmqxfmkTUWVvAj6QFpor02Ag==", "signatures": [{"sig": "MEUCIGpVw4N4OssPLGLZ4qyV4J9nZ4wWO3m8M0mRxhN1AxaFAiEAi/WQG10EHQy+YqW+6sIqKDguwFyllHFCsZ7lTu7CKtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.21.2": {"name": "stylus", "version": "0.21.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.21.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "3f4560f70a3d310dd09fd5b16b670e12f0159e64", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.21.2.tgz", "integrity": "sha512-tBVGHa+tFHRqLEji5EMUhUgOIUCClDrc1y2Q2TxJhCQgDc0Uk0JH6NbI3dfiWevnDfcz7KtGLpd4CZQQLOdx7w==", "signatures": [{"sig": "MEUCIAdeBLeB4qyJFq8xlZVtcD5Qsq2WtFnGGZo7SQipjBc/AiEA8/UjLg94CqDozomCWW7dbAUjl3g6yJIKWLgkY96rRYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.0": {"name": "stylus", "version": "0.22.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "d0e73245116414fabd605a41539a186d47d94ad8", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.0.tgz", "integrity": "sha512-FzuxGbXQ/KMPN6xPAe081ATWEjKiZ85WeTkDfaw+qUb6iPcw99Q2Y/s2SrNCTtQkwAZjAW+Fg6n3oqFYiLNdIQ==", "signatures": [{"sig": "MEUCIAvu0xzoLPH5xZM56c7PV+u20S8JiitHQtsif21VsTICAiEAk8b/l5sF12MOrpWuqWopqejkK8GPnH1mQE0BqG0lFuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.1": {"name": "stylus", "version": "0.22.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "f95ea8da05dac52eaac109d35a90f2974c6bfb47", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.1.tgz", "integrity": "sha512-52LJ63MxBKtGU77VSCRrN7xcgK6qjirOfKRc4yWguCzDJdwf6lBszXX21zsCXB0+wKH4OgsWAsTK4bBWD27pKw==", "signatures": [{"sig": "MEYCIQD4qHjKxUaUGjItKOq70oZKh0xLT6vxkKIxKlHzpOAPkgIhAM3IaUXbQBy0PAsJFFy6cPYMrBqAI/XsozH+U0MR12Tz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.2": {"name": "stylus", "version": "0.22.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "e001a07e400ea9c3137988f32ed2102da325f219", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.2.tgz", "integrity": "sha512-70uQl8DMSJJiV16/m46GCWsRrWX+Tye3z6flPyC3mcI4yB3fSZxyxzKxUrXL68edw+rK+RAj8wTw0ZVhyN4ZiQ==", "signatures": [{"sig": "MEQCIBuzaThu1ur56hQ3jaFomvDt/8WBFvV9r02DR47VnBNtAiBkZNA8g7q67JTF54NtSwgCpNHsxhPlPuN/kBRMO5H19Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.3": {"name": "stylus", "version": "0.22.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "192f21ae054049c9356ccc5104b2d75aa1c8d3f5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.3.tgz", "integrity": "sha512-CKjcbR+Yr0ftip4iCNEO1iqOg0ep01p6J3n7z0Dt02AebtyUS8E2spNLXNzfvR8Au5gmWqcmcyLUcfAXxYMByg==", "signatures": [{"sig": "MEUCIQDSmfT/ug+aE0CvtOQL1F3mJn6xImgc5HEPdznM7vC2zAIgJoNd7rZZvzgzD54fQ+gVlqiUq3JO92YG1nnnjQzNsSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.4": {"name": "stylus", "version": "0.22.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a180455854790b25d3be42ea698b4b1d8af5a8c0", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.4.tgz", "integrity": "sha512-uUAe4ZHdt3GQqbfeX4iYAcXy7fmDkW5xRsBx8bAEFnuZsdIaImQhbZkC23grC+OA6j/gaiNI412WnM2MtK6eOQ==", "signatures": [{"sig": "MEUCIAhmSUEl9iqDMtDaCiQUsKHbSzCHf2INK3/WN0VGfFzGAiEA21vXNF9Lhq1r+0/GYxuDf5rz6mRAmSvgngbTKN51p9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.5": {"name": "stylus", "version": "0.22.5", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8ec2168edaf9e9d8d002fe34311ec30c57097fb0", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.5.tgz", "integrity": "sha512-/sNKmZrFPERJzQnTqI2KwV1TEug+NEn63qOVLfPI7ICmhQiaFk3fHea/5rAxnSXs+hAyrdffhaq6jcp6oyePfg==", "signatures": [{"sig": "MEYCIQDxj39XQE/uyv0myDr/8YnGJttDSBNY3iSEDcK48wUbKgIhALXlZncF7duyQ3+rvireYoRxiTXRn6FPrzHlxu+nm4B9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.22.6": {"name": "stylus", "version": "0.22.6", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "392c7c16ceee48eec0404e4adda43969a0865ae5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.22.6.tgz", "integrity": "sha512-RDBIydAJb4eAF7nCIb4n5j/jP4TwPCQBlHSt3tWsemFOEc7dDnsTPqVUnsRZ1V84RViQ+IA2MIWwyxTV9MkRaA==", "signatures": [{"sig": "MEYCIQD2bclc93NxR8sIqcr/V3CxgSVDsGqm08f4jB3SLqeXBAIhAOBhTuzJwUWHLb7F6UeREbf69QEMr9k8zrhquOq/xyds", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.23.0": {"name": "stylus", "version": "0.23.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.23.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "f87bd28a290defdd16a5e24d873a63547345a749", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.23.0.tgz", "integrity": "sha512-Mc9cuDKklCuHMuZYV7s8ONmaHrjrJ5w6X0e48MDZH/zzb21+Z6iD6Jvoc4uaZMUzk8YCjxSKDDmtuoCHpAw6Wg==", "signatures": [{"sig": "MEQCIAOnH/fFp+G3hm1Mxu9PRhmMSzsExPo+KtR74VnnNRRgAiBKL6GvO911dTdDnvEGiFo+2sifBVF67d9Ii38XcNZvAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.24.0": {"name": "stylus", "version": "0.24.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.24.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "bc3cdb7b1ec89ba5b50f785c30a295b46bb3558d", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.24.0.tgz", "integrity": "sha512-ltQPJQ77Qu95sT22wtv6Eh9vbZPiTO//nRXQQLlmD/rvaiW/KMC1a8R9F9bA9Wt+m4dXDqh9moYG4lKrn5/u1A==", "signatures": [{"sig": "MEYCIQChxMrGyhlW80nXhVVeVEu25ijztSiJuDjTkBVEDPH5EgIhAO8V4o3q6Y3G5M5hBjWq/Yu6iw1Fqf0aWEQkRbQeLwy7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"cssom": "0.2.2", "debug": "*", "growl": "1.4.x", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.25.0": {"name": "stylus", "version": "0.25.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.25.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "2ce3846bf5492f05156d25331073b7e5c8471bfa", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.25.0.tgz", "integrity": "sha512-oVwh0haj9sqvx9amf+0lv8zkLN445sHvZFOruH1PK2VTNIxIQLKLo/L5lhP/SVYUGV8xLEoaMUKCdM6daBjEcw==", "signatures": [{"sig": "MEQCIAheCePLgEugd2RIuis5z2c/zHL3A3jImxqv9Mgv20HvAiBWnvGp3bkwd+dmfNQCU9ezoaBjiXF2iTHSSWBlv+z6ZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.1.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.26.0": {"name": "stylus", "version": "0.26.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.26.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "cffddc827c091fdb278f03209e4dfe09e51e6eba", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.26.0.tgz", "integrity": "sha512-IXH5ymwZXvCan/G+Rxg0aI93lJlYUvHXYhPOgUBrebzyZ2GJyh/XxpYipO1X8FhU5RqmhGcdQy0J2+UBQTVwMw==", "signatures": [{"sig": "MEUCIDjTTWoemuS8i4FkOPZNjNtxWypoQ5tsRfRBx7EmckI2AiEArqeXIMCRMWzw7+YKPb46Ut8P8o7EjEUMWFr1MKS2ScI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.9", "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.26.1": {"name": "stylus", "version": "0.26.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.26.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "9738d76f477d644c5fffee4367e7231de5bb0af5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.26.1.tgz", "integrity": "sha512-33J3iBM2Ueh/wDFzkQXmjHSDxNRWQ7J2I2dqiInAKkGR4j+3hkojRRSbv3ITodxJBIodVfv0l10CHZhJoi0Ubw==", "signatures": [{"sig": "MEUCIDMYZ+3YWLZeQWua0fxv0XuhRzPv++T2ZTxUGBS1NG5mAiEAiod/FQsD/GqXqTiamFVrJQpMxR6q3RF/23YielgrbLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.27.0": {"name": "stylus", "version": "0.27.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.27.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "f0eff1158f4fcd484933cf0c9ac753f0f84f1219", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.27.0.tgz", "integrity": "sha512-vNgjLltadUCtB/pGX1HCBdu1i0ZPXLkX7BsNIS+16wJAkGibb6CbA3RudBhLrZpTY4NFcVXmWSQaNc2M0FuJug==", "signatures": [{"sig": "MEUCIQDSAfoiJXuqpF3MxTwLexu/K0mQz8e+kbbGvSZVgjk5twIgV5+p8ezUrTf07cx/SBZsv44hyBNP2fP1V+hBKtOvvAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.27.1": {"name": "stylus", "version": "0.27.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.27.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "123b3a95255e423f5404826bfcff4ac1ca5be298", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.27.1.tgz", "integrity": "sha512-OgZmsFr5mmVg9udhromDW84c9nzwyGA2in2LlmNBd+EPX8S4tnE8wqJCxa6a65sVIHEClKdWFAEjYlpNazn02A==", "signatures": [{"sig": "MEYCIQDhh2Tr9dHtZ69enabD5uCoyG91ACI0u2aD49NEhW/5ZAIhAJoomyyb8Y/FA4iCYsQWTPcxDzdT9qgwi76hXJAZIOOD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.27.2": {"name": "stylus", "version": "0.27.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.27.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1121f7f8cd152b0f8a4aa6a24a9adea10c825117", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.27.2.tgz", "integrity": "sha512-A09OMYaAcFsJK/Vk7ZBd6oVAQT3cckz5ErTBxhJinbkkJ0MFO788cgjwznA8tGKhAP6yswJci8+adWTalaJisw==", "signatures": [{"sig": "MEUCIDKONuTaQbTM5v2rpsUvxb1sUB1sL+0nBMXsOUORGXtGAiEA++4AyuNWP97BQpGmuPmmRR3Noxt5KvWPd2cSnLOznxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus.git", "type": "git"}, "_npmVersion": "1.1.19", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.28.0": {"name": "stylus", "version": "0.28.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.28.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8e82b4b1c70ad87aabe9c8992a5c1f68469c69d0", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.28.0.tgz", "integrity": "sha512-9G8QY6aM5qZxKVjk0+viTt8Me9Aoxk624CiLen2ekON7RBvXMQ32U152cfJRSwSYFX/vkaxcNdDSZi5mqRWKKA==", "signatures": [{"sig": "MEUCIQCOj9PtR9cyLOHcxbOkk7w7rLmRRf1KpSZnLrfnGW2i9gIgWlXH1+KL1DEcA+e/TGeoPsyHY4aMifQHHBuG/D5PqvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.28.1": {"name": "stylus", "version": "0.28.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.28.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "093d4a9d9508ecb33aac4eb802bca8a594733091", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.28.1.tgz", "integrity": "sha512-weUCjC8Z5SmG7TXQ3I44aksIO9arGpgxiGzZES6NSMe9MoSI8QsWH8rdqlU6ouqCIg5I41h96dzWYPXg1zoNaw==", "signatures": [{"sig": "MEYCIQCC+6ThLl1vWzdNJMggiRwwNFCnMlhC3e9cWXQfKQi2xQIhAIWoyE3IqmHABj8FWRKI/YcxRV460EGrKl+VWYT+NZvb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.28.2": {"name": "stylus", "version": "0.28.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.28.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "d4c67c69e1b21e139143cdecf27f205b3e69e587", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.28.2.tgz", "integrity": "sha512-nGord4MkvdV9Xo+I3cuHrhm+D7pP/S8el92jd7QGdewgV4Id/F/ji2rI9nhyRY9NA648npP7acuuvCaNGCF/Uw==", "signatures": [{"sig": "MEYCIQCY5bMT8YNmERH69EMgF6KAodoACg/cUBy3+Rp523whFQIhAJQL3kjlCZcyA7eLpAKd1KfPDiddfAnnLLWusOQHKvz+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.29.0": {"name": "stylus", "version": "0.29.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.29.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "508a8d350a270e23e1900c20ac5604b9a190b611", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.29.0.tgz", "integrity": "sha512-bvhqBONMi+nFyyjONXIs4OJygsxyY/bF6KjYNgz9SUZF7lnJOdBWCNOaXwOkFRcWrAp0vx00dSiwkGt2Muv7Dw==", "signatures": [{"sig": "MEYCIQCFXa1rzif4PxYggPxHwSPFwOy7dClkShF1AP4vKuHrpgIhALBxRfy7vpAfQHaNOlEG3fWtHc8LnN6fsX3uyMKsgNpj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.30.0": {"name": "stylus", "version": "0.30.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.30.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "17be0fe69c0690ea6d221988d6481a5fd7bf37fc", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.30.0.tgz", "integrity": "sha512-hHrGqvaHNCoGvc3DLHc1Sf8RmQIfP+eDFFsfI3lpR35L2ot6EjwocZyzcIz2RWOAiRma0s8icwojJyfYv0uOyQ==", "signatures": [{"sig": "MEYCIQCNH+FGu07J533kCP1s0Szc3n/hO/0cwGr+ILZfP70QEAIhAM0/ucLKjP37d988DoYhH2F67T/7tIQ5DZawiP0Bzd6D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.30.1": {"name": "stylus", "version": "0.30.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.30.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8fce4fb3940f4556e1dca1c43ce8be2876c80325", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.30.1.tgz", "integrity": "sha512-Ar293Pn/IyYacuNEA8kDe0/aBmBKUCN7cvxL6sSDM/fiscGdvXo2qypwLNtoAyp+UTOHJz2/049Z0Ej3FVpi2A==", "signatures": [{"sig": "MEYCIQCb7chi9FgU+c19IDGRFjmC28YO6ZAZIfwBtOMCsUyaRwIhAPc8+g+cXM1sc/LG8xvREupfctythjEOaVyWjvg0xOkY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.31.0": {"name": "stylus", "version": "0.31.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.31.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "b4f807aa067f071ab707f2c8ae143ab3211e51b2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.31.0.tgz", "integrity": "sha512-BJ7iU9iUNOj/HlTuUF9SHh994ooIrOr/yYxzzQzpsweulT0HPyL4vtel9/QJcvq+xwy3ff7sf9Nv25kATr2qww==", "signatures": [{"sig": "MEUCIQCECCBV7Nrrt7x3uUUlMfmGQgcQmxi7QVsE4FBjV9jDPAIgR0pO/r2HlJeO0lJqv2HEjgpdkunjbdGAtqP/pcak0HI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "_npmVersion": "1.1.65", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.32.0": {"name": "stylus", "version": "0.32.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.32.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "ea1d489d2beb3b92d08c9bcd94dcac8e509762f7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.32.0.tgz", "integrity": "sha512-38ha6MVBpvwO1zjbfJIKIAMdegs8zoxFzFFfOGYQ8HV3aCymk4C6SaiL5QMHondKi2ZvwP1CUc2OZZGEkcV18Q==", "signatures": [{"sig": "MEUCIGgHtJnH+GGk5CBWZVCKgAOVK7+VsnM/7TF6hfpANCMSAiEA8I4r0U/1QDt2A6FIKWRY+Of2CUL1LsCuAO1t4EXvUCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "_npmVersion": "1.1.66", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.32.1": {"name": "stylus", "version": "0.32.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.32.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "3d28275bd9f181085f8dd0fa52a8559696978212", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.32.1.tgz", "integrity": "sha512-QAUlDBLxjrNpf4dT991DOaXcxBKCJuICYAso29Q0RV9aI+REZIER4aRMfE5DIkV1meSiVccO27dA1Uhu+5gzEQ==", "signatures": [{"sig": "MEQCIHpzrJKTK8WkKQeR1bLfAC7W1K8LLo64b6xnlbaAz4XAAiBY8z+dee0oWEf06iOf8Ja5RnNL+xmocOpYdInvxzBbfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/stylus", "type": "git"}, "_npmVersion": "1.2.2", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.33.0": {"name": "stylus", "version": "0.33.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.33.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "60fd9bb5eef822430402a03fa11cb86816add1ab", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.33.0.tgz", "integrity": "sha512-6U02Ks7+3/cJUvhnRzF5e7SeUSc9VvbY1/7n7XMmbyq/uKENWai1afcerxBe64AEYFbS6/ie/mP6tLhDX/6a3w==", "signatures": [{"sig": "MEUCIFl/TiQSfNtIU6uvQMY1aQid8JUYvfffLHR2osDMO7EQAiEA9LcUNRjvYOwND5Hr//j7Sd2ECzMw5/2Ndn4yySusgV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.33.1": {"name": "stylus", "version": "0.33.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.33.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "10aad6776b66952304fb27e162f74de29a40ffa4", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.33.1.tgz", "integrity": "sha512-+TMmKIpaP3CLtU98439R1xLxsVwVCfBKJPsuUtcrhBNbSa4I8ZUQaDsBfDrwxs5qjD8GcQAD1t9tfp7S5W0/nA==", "signatures": [{"sig": "MEQCICtlTtglnVbeZW5bjb3wzCzGqqU+rnhEDQvQXbu8AevwAiABvKVu8MDPpUtQixNJy/cR7OkFH5MqWw6x39xgNg2WuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.34.0": {"name": "stylus", "version": "0.34.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.34.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1299d1ac3e3e34659437903a724f6aefaef2561e", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.34.0.tgz", "integrity": "sha512-OqA8AbQQRrSMyxeRHZNOvVJbFp/prQWg80QRMqXw6otopuWR+65RX0vvrrcv+liEiqQzb7R8LJ5opvibJ6cFxA==", "signatures": [{"sig": "MEUCIQCKx/KV7fbzCUKLwQD/V7BR7X6GaUMpGlgs6Beas/wWXwIgajYS9TlbBaVSnT2ZUeNZvXmxynSx5urjxE0nYAYqIkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.2.11", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.34.1": {"name": "stylus", "version": "0.34.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.34.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "937d8502a3be4e617d5ad493f204c70a93d95b14", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.34.1.tgz", "integrity": "sha512-iN9lVTf04KoMydURjWoTcB6s5d5qtP7yfOLdHrffiD6aIuqGUqKyO+CsH/NCN+8a3cKn5d8IUHzk81hNoMlrtw==", "signatures": [{"sig": "MEYCIQC5d1THgCV1KkUcakbnMWGVEIWKo7sG9QCu63KvDQr15wIhAM4/W9O6zDb8Ac1fI3pL2i5N9r8OsBr3EnLVl8223FzW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.2.11", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.35.0": {"name": "stylus", "version": "0.35.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.35.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "4ecc12e592bc6e04b381f72ee4c2c7a7b96320c7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.35.0.tgz", "integrity": "sha512-lVnB+ekv3DkiTXmkfXco/Tlw7P4+x5gj1KQZSXMktNtpBd3ZWmudmU6zIRSf8sQJFXAubx5HYf5mdkZYx1EqeQ==", "signatures": [{"sig": "MEQCIEVIFlukvEWCGmJCNC6sXKl2lucGDtAFlPbOyPEo9RSAAiBKUpoBW4zq3WzdJRDYEWpPAk9Hf1jag2nLJwjUlNfNRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.35.1": {"name": "stylus", "version": "0.35.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.35.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "ed4f4cf378c2f883b04633f01e5c76ffe976084f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.35.1.tgz", "integrity": "sha512-3uU5tf7b3AsbgXoGfaPoj3DeqNbCgMhX5o1iMTRFizGM7vP8T27DeWomfOfHgzjPS/t/cSdcsW5RifGtUjHXdQ==", "signatures": [{"sig": "MEUCIQDeb8It/U/0vII8sfI8RvTgNbwD5D8hWOAMVHVsmUBr0AIgKypXl1moLTysK/eQg0Bd1PTeiuJXjZHY9ID3BB9odFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.36.0": {"name": "stylus", "version": "0.36.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.36.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "733e5ed75336c386da42209eda6f4ec814360789", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.36.0.tgz", "integrity": "sha512-ispkGa+zAtSDXr8KRYIqzWzuvedre/ZgnbRzxRA/5NYMSAyTc6OsBPl9Wlp1MBvTSPFeoteo0qmpkMqKlSsXUg==", "signatures": [{"sig": "MEYCIQCwS+clq03G+2RuZ9lqvSN9+q0UMVarJG1psZVIj/DDggIhAIG9Bleh1B9gkpo9GLo7zp1wzSro7LffVN+52pFKILMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.2.11", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.36.1": {"name": "stylus", "version": "0.36.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.36.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "64e493933eb5f7347e941b37046f5ba533d3d262", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.36.1.tgz", "integrity": "sha512-2sww6SLdwl0qvoxu+tDcaF+xVyZNqx9wrTMRmgEMBTujOPbVoYHwNmEnpt8MbjoklUNYtnKglLNtFkrSMzjfiQ==", "signatures": [{"sig": "MEQCIBOMiTfslzrXSdqqt49QcrGsQFGlO6N9i0mZd/zAM4NfAiBGBD/5SI9bXdVoOApvn48apJ4JHvTVEkeHlM1esL7Olg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.2.11", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.37.0": {"name": "stylus", "version": "0.37.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.37.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "3f18ba693960e3408e2951b4a22e337c33d239d8", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.37.0.tgz", "integrity": "sha512-OUxmQGwyVdjW2p+Xtz1zNlevRhgDVhmStG/95Zhf3MLnx+vBLaMZq/X8D9/ZLjVSk3D0vT/dlIPCy0mnH3Hh8g==", "signatures": [{"sig": "MEQCIH+rzj0ckyVtwHCcvJZfJBumt54xGsvZcnPvU/wX3MZIAiAtV0nKzq299U3l8MgAokxT3I09+/DIyG42R73K+WJ1pA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.1.61", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.38.0": {"name": "stylus", "version": "0.38.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.38.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6bd0581db0ee0491251639d338685f7232ca0610", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.38.0.tgz", "integrity": "sha512-+LZa5qjRMlrM3QbUMC9FXiudhEYhZVazXc9gXxSPIAl8nBXUoVrlG282IAxVNv4KC3DtSpW7X7lUMsT8vP/CZw==", "signatures": [{"sig": "MEUCIDsyqoRvYB1hzUDvccxcnGXVBpBpsjH3GDzDI698lieyAiEAwxQEeP/uo4YvM19Ql/kPwasUaBO0v22yRBSm+8lmqjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.39.0": {"name": "stylus", "version": "0.39.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.39.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "d2f3665bb4aa12a10e10fb04936d2ee80deda544", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.39.0.tgz", "integrity": "sha512-KvHJSbwFbmhpzW+k8EiQsvpWJxrTYEY1pd39z7VglLYY+Y/lGwtFDtA7QfVHDvZApuaQ/WlYqQesxehGA40Bzg==", "signatures": [{"sig": "MEQCH1NWGSM9Z9K1PavgSVJq3HUAnkFyIL9yVJpb1QyQFH4CIQDX34EO9EUofn9z7K7AHdlgJ6SgzX0AEd3y78Lo6Tg3mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.39.1": {"name": "stylus", "version": "0.39.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.39.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "3eec9681781b483fd8b133a89b9fdcad6a3f1c6b", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.39.1.tgz", "integrity": "sha512-E0Fo4M8eB464rm1lOyC4esf2tMqUQrGaWOG9lOwji7ImvSjP6/lYbTlrsJ8B8+AYJPhTK4yOZc5ErUzlF1YWZw==", "signatures": [{"sig": "MEQCIDFkDbWALRiOKwpaoRBSn1NoNjphyMw7l+uqtlXEqrSzAiBQlb2MtxyDog/Nli7zcvPONyniKOYNJO6gy1LtaR6vPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.39.2": {"name": "stylus", "version": "0.39.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.39.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "af39ca47ad666314ba3171155499826f5b387c90", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.39.2.tgz", "integrity": "sha512-zqLiNmDyPcnaS56QepzG79Mn49rF36H2e6xAspkGEvAusGIq82l0b8a2Fk3odmvHzYn9LJ2tAZf792RQbsESOQ==", "signatures": [{"sig": "MEQCIFUUJrT53DHGTFf9tufRulX1475A/E1/QXMuQhavqx+OAiACRe1+IV+ruE5FXTYspEYwDej9u028Q8y/KpErtC/m0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.39.3": {"name": "stylus", "version": "0.39.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.39.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "45af2cb47935f1736a7ef905b86508309c6ad16a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.39.3.tgz", "integrity": "sha512-t3CXwroz9jEMOm/ynat95xf+lUo9L0YmAzi16P8lHRGHP3ze63euiX8zkqWfE26fB5ot55mU2OIjZi2dJZl4Bg==", "signatures": [{"sig": "MEYCIQD1I2SmbZLJ13L4hsdzF1mfs2Mqd8esM/uEvy5neqLVYQIhAJyADK9uhV7nTrF9wq1Z5jhfbv9NMDB2AkVbox1aPrOp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.39.4": {"name": "stylus", "version": "0.39.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.39.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a062854b51497675bb246f976d68b61c8297d6c7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.39.4.tgz", "integrity": "sha512-XUBXIY+77H8qCpqXjoaj/prVVj2E23gxEK3X0kNvhuwGq/Bma1NtQcTmyj3yPruQMXlk2l580yZPuouWx1i9Fw==", "signatures": [{"sig": "MEUCIDcnIFR+RiWWO5V38Vb9jKxC0gXx2MRytRNdqhcybvZnAiEAsZsrImuLAQZRG4jm6NFlvvCi4zTTZl7qwvZkHb4e+Dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.40.0": {"name": "stylus", "version": "0.40.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.40.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "497b6242f3952417482acad65627a6988125b634", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.40.0.tgz", "integrity": "sha512-wzHV45kpjEQpghbxIvfVBjPaf8OglxLSW8FyMxYDONgbXTQl0O+/yMje+nAfDc1NngTJwsBbfgfv0UpKOLstjQ==", "signatures": [{"sig": "MEUCIF/MEcoyjPA5MhY40e5SQ0jVppAyoRYG7aNrLb3gfBPtAiEAlBMgvLULgcL4+zULxTJtme7ZoJ1WutWuqOfcw1bhbhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.40.1": {"name": "stylus", "version": "0.40.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.40.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6b7508b1e22710a8ed9e483da871ac0bdb4a01ea", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.40.1.tgz", "integrity": "sha512-MzNS0hKBKQFc4kd6l84xWWk2bWGMfkBZzTOuTE4o8qPWHKadX3ULXWlqmMUhn5FM2DeCxqhzELnVMUvoc7jFbA==", "signatures": [{"sig": "MEYCIQCMB294K1iPRsfLcFrsFFBHM4lSprZMjEAg8kd/fdaUhgIhANyh3NSsltFCgOHOB/RgiG1LBekmlWdFWpVCWxiwZ3+1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.40.2": {"name": "stylus", "version": "0.40.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.40.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "4b4f7a2d8a6aeecfe0a62ef0e2d8a45a78b1e2bd", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.40.2.tgz", "integrity": "sha512-G4XsxOsoilMEx1UXeVyfmXU2SbGrWUCUQ0LdA55YePujkIshySn9dCeGZEN3YzNgObDQ07rF8gF6uljAK+pF2g==", "signatures": [{"sig": "MEUCIHo5w6poiAeLhNoXthAriWBr5mlcjgil4mroInZp3K1LAiEA69x8y04oS/4vjbLuSbphTpr98FIViN09j/K9HsjWMhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.40.3": {"name": "stylus", "version": "0.40.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.40.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "17e566274ebd6e56f23a8928fa38626c4260ded3", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.40.3.tgz", "integrity": "sha512-ryduhCPEb8aEArV/ofe8BBTU1ibLeb4ytqBjG9iyLsrTn3X8uQp3CNMLu7/1TnOHge5/pBO2sC+Ej7tIiCKpqw==", "signatures": [{"sig": "MEUCIF4T0zRzUy7zRvWZrs4EKpvcaTmT2MNLAw+PiGsvuR/2AiEA8CuQudk2HZS4HD5red87CoyMGaMGpzEUnIldfdPS6fU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.41.0": {"name": "stylus", "version": "0.41.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.41.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "b9609d5663c9624d11b28952f3e6b23cde10ac28", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.41.0.tgz", "integrity": "sha512-W+1UEXUCGb5Aew9LRWsc0zAvbwnyXO8yLyn2X8d2nTyzQRHLCP6AXkplAIC2bL2KhIRsiPn7EHYjLTQBxjdVaw==", "signatures": [{"sig": "MEQCIE8nZzNFJyXWkZCyk2uNw4BqGbX67V52kujAPddz4dj/AiAHpe1bGzjzvvaRSv7ffVnMqUhWAAxoBUT8qc7W5exy8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.41.1": {"name": "stylus", "version": "0.41.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.41.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6737ccec7e397b913a54996d024ce00a9ac13185", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.41.1.tgz", "integrity": "sha512-DkVwaqr78AbqHRvoPielLyjFR/b9Kv49Hivle8iz4Nyu0TXpXH2boRZcC/umhwBdVap3ZQTIqzu7xE3T6i6IPQ==", "signatures": [{"sig": "MEUCICIH/0hJSwnL3UgoICd/ZE/CVsholWZR48WumIB4VlDqAiEA0AxMw1/ZHAnzXCGexD8Bxff+Pc02TZrIOesx4s1ZDdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.41.2": {"name": "stylus", "version": "0.41.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.41.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "68bca2b317d32c85e7cdf7afe7ee044da9afe983", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.41.2.tgz", "integrity": "sha512-13Yr1eAHNioVcknfTSFULX4y0tG3OEz+6uC9ct7Av5zRyU5JNO70Kxb5HT91fzK61kG8O35GsvXvpgGckbbo8A==", "signatures": [{"sig": "MEUCIFDfQt3959O7YrCkweQvfZMnZdDLhVEDKapCm+QG58MfAiEA6kH+DF+xVQCXrCT2oMrlEqipNtP4ap9eQcIFs1xqDtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.41.3": {"name": "stylus", "version": "0.41.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.41.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "0a49c3f2f4f6b6719e7cca823052b9df87acaeb7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.41.3.tgz", "integrity": "sha512-fJGtHOBOsq1qY2Lv1BiJOfDk+iXoO0sKDCCDIX1VmU3vJ1ObVHjuTqBGiihR5uBb4Y9IvyMqZw3r+Tkf90kdSg==", "signatures": [{"sig": "MEYCIQDL1USsDi2SsECL8SEzCeAZdlRV0qOYaaRv1twsLUReogIhALHseP4oZwzRdwpA3dU2yLQoDhyH5oJkyHJWNJkRprEv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "cssom": "0.2.x", "debug": "*", "mkdirp": "0.3.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.42.0": {"name": "stylus", "version": "0.42.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.42.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "5dc4d50dd205dbb7e6af6bf7f19546da24148e44", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.42.0.tgz", "integrity": "sha512-8/EdUPr45fomGeDF/KRKekE8MwaPHYE+UIiCosmGtSHKw169PqyzDVY5s6uub8cdH0BSyYcZ00XzbC8azVIg7g==", "signatures": [{"sig": "MEUCIBUXh/LZm8Ut5raDVFKzEE7WIvD4RnKRBkPsu8ANBGkZAiEA7j8XFKgQiijbS7ocIZ8pbzo5ocKGY66RjbnizO3Vg3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.42.1": {"name": "stylus", "version": "0.42.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.42.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8e5bfec922e8d0037748cd05281d53ca602b6e4f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.42.1.tgz", "integrity": "sha512-Vjr9zeNY6OSQzf2hYPF5mZeCOJqpGkP3SGc1ZpQqRodSG4P35hml0nURxOeYrSs8kS3B5lOyAzeVzNXNg/paMQ==", "signatures": [{"sig": "MEUCIHFuRDTaXD+wL3WiUq4/g0q1WTogOF6zYHqBH5nSh5/9AiEAk6y/J1J7MmIXwwbfIfPMROnZt/p5ZMITnDbrYUiwRTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.42.2": {"name": "stylus", "version": "0.42.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.42.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "bed29107803129bed1983efc4c7e33f4fd34fee7", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.42.2.tgz", "integrity": "sha512-pB4+/jtRyNyfeMNRNLwy9uSkcROupmv8vLF+Z58gg3Hvh2soryxeMOittCYynGjUa2J72WwtZLlymHkYRGr1HQ==", "signatures": [{"sig": "MEYCIQCEqtcnvXV1uWaKt7LerPfjhCAsIzmpYT+3hJjjEUGolgIhANnovOTkUWQ2kmIDRlUNJlZXh9fTKqeR/C/80om6YZQ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.42.3": {"name": "stylus", "version": "0.42.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.42.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "23e8b3e71eb7f60bc4a657de498c8feb9c366c32", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.42.3.tgz", "integrity": "sha512-1eoVC0Nf3daJHrNf0xW7CrFBXBfx2MoDIeHe5EIzeqfc0dTWCjDKFMWzVcy7pMl7PQTjYVX3refUPIOFbcEw0Q==", "signatures": [{"sig": "MEYCIQDqH0rC4Ss4XLTqmXhyF9KgnO/A7Kn+Ba4ckqNmTNhiFgIhAO3Va/mR0LqmOgzsOXoNKfzUrxExxFoSv00ywW7m/pyW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.43.0": {"name": "stylus", "version": "0.43.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.43.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1d59cbd9d2bc90dc54e3e61de24234710c8cf43f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.43.0.tgz", "integrity": "sha512-tX2kl2MuDCOkCDqzF9aOx5BUYrk/wysTGfz3PwjeWwB6xza0p869dAOF2O2iUBU5QqnQIsSyVgRrIKTe5sgFiQ==", "signatures": [{"sig": "MEQCICNWX2CaQU0+aBVHZ/K+meBPgzBbkutBP39UdC/qHFOuAiBzLaQalMbTX4Hrf9M09eDKdBPqMg0SmJQCbmuPmiivmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.44.0-beta": {"name": "stylus", "version": "0.44.0-beta", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.44.0-beta", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a4ab58e8f0b973e1561d023494a0847b0b25d5f4", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.44.0-beta.tgz", "integrity": "sha512-cZo/n8qW/kjtfFSIQPPvQCpeVvOhCdkCVUnZeyB0Cnj2t3VxMC4MKju4OPsa46aSWs6jKVtRTvM6gSE8eMTz3w==", "signatures": [{"sig": "MEYCIQDm2bDX7A+zpCGjkJn7oNUwA1591fPVuLYpIpWI45IOUwIhAJeiLOIWFtwIiH4fQdsmdoU0qgKKqMGmHFVURQ4SJT5k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "circular-json": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.43.1": {"name": "stylus", "version": "0.43.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.43.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8ebff63d41aba631d6451cea5618c7e982891da9", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.43.1.tgz", "integrity": "sha512-sryVUx1q7FJTUSVLGxpCQ9af+5hPL/wsfD6MJfLnx4Ksy6Dnfe6YaLtak5PiVlimKLOfU0VklIlbxSNijijwpQ==", "signatures": [{"sig": "MEUCIQDydZxkDTnOt6FFR48nxhukVpyFiaikvngeDw100OInNQIgfwj+qo39q9qouQABWjgBEECEFB6zG3Jdy69ZIO63xnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.44.0-beta2": {"name": "stylus", "version": "0.44.0-beta2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.44.0-beta2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "fdfa244abf38d1d077c6a4fe729bb20ee928f513", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.44.0-beta2.tgz", "integrity": "sha512-Y+sisIeUf9RGsoE+qG8ILjthNnhWjDUvjMEEnAQGzgAnUs1aQ98r3Z5q3PZy40zDbBNvWtVhTVdhIBX4LCRdkw==", "signatures": [{"sig": "MEUCIGvxYmim1ATPD/mFljJjbgMgJMiUee+Fe90w+JMpsio2AiEApiy0mGKwZ4nEm2xJrbiU4l6HcuGopLMIkKMn8WQRRMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "circular-json": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.44.0-beta3": {"name": "stylus", "version": "0.44.0-beta3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.44.0-beta3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a602b30f57f1692cf0f1fb664e60a14e26102cff", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.44.0-beta3.tgz", "integrity": "sha512-wfBIU275/ZkqcVWYOOEMck2FCn2DYuBOJ/kfKUeVxZIJpVvfx16pdd6KFyHNNC0zvyDep0KQ3ZljzzdaMSs1vA==", "signatures": [{"sig": "MEYCIQC4gb+iqKYoZuOfPh2M6xrp9DvpfFTrE1PsxFxh004C+gIhAOHfsTRDJxqUuRuwoGa3ayHk858cVwvwZDMJEzqisTDU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "circular-json": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.44.0": {"name": "stylus", "version": "0.44.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.44.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "c6544d75290377986822b5aeb71899dbf756fd72", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.44.0.tgz", "integrity": "sha512-g5lno2VktKDwGvjTw/iGswS2m/844bxtJCL1CcKJybWRckTtnBB6fYI+BegXsJVoA5wuu7FzwRdZZA7PJiZkpQ==", "signatures": [{"sig": "MEYCIQCdk7/ujbygk121arV0I42xb9LSsKTrpgsRje/bDgzGZQIhAMgOKbf1bmXYBE/lrPceOiO7sAwkJFrcf8EUlFL/A1Kf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.45.0": {"name": "stylus", "version": "0.45.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.45.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "e6c6ea624a14332931fc7c6fc0219b8cbde9fa76", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.45.0.tgz", "integrity": "sha512-cNZKmnChEdXfXwWlQq6qOI3GFTh6mL8mOYwekLUL5FdFXjE5pESVzSYCOGGYzj/l05d9wM1J4/gHjbfKzucIEg==", "signatures": [{"sig": "MEUCIQDRR2BPhsHBUxhJG7lc9Ug0e8KDgtzxn77Z2FatNocwgAIgVmU7QvG11XC2+LBrlOIPL14Afg+Nq5GkfKk1kU2nvSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "e6c6ea624a14332931fc7c6fc0219b8cbde9fa76", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*", "jscoverage": "0.3.8"}}, "0.45.1": {"name": "stylus", "version": "0.45.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.45.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "ef69f62c984a480adc0c9d4aaafb2382a389e453", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.45.1.tgz", "integrity": "sha512-hESfrPjsbRdLp6bQd5wmv4STeZbzgzGb2KpU3z6vkZCc5sa+TfjaKc6QBA6E21eDAH2wZJdAmw5e66xeASGl5g==", "signatures": [{"sig": "MEQCIF4NRwHUUat5e7TlJxmpIrAK16fAWSAiFC0mc7uLnPYwAiB4qNDlH2DaAh7p6zAdYTKNLoGp+GnbGbtJIxcVtv+HhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "ef69f62c984a480adc0c9d4aaafb2382a389e453", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "*", "jscoverage": "0.3.8"}}, "0.46.0": {"name": "stylus", "version": "0.46.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.46.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "44483046a4051007e56406e4306aa8ebd5ea33b8", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.46.0.tgz", "integrity": "sha512-4svKA1Q5NVg8VZ21VfLDDw3BpK4LtyEmZo4Ln2gf/QCtRH36dmhvbgdYZV97gYsxeqbmo/lwHm/rFWaa4BiIwQ==", "signatures": [{"sig": "MEQCIHE2hOnj+03/qJODMgfGVnRmWw4jFj4Ntn/AxGPIQju2AiBcUnKWrytSmU0uQQY38WYOxuTyw4YCxmLiHki9T5vicw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.46.1": {"name": "stylus", "version": "0.46.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.46.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "72abb57e674056f073e134793192a619c4f46b5c", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.46.1.tgz", "integrity": "sha512-S99VfYQhMgTcz2qQzzLA4jKv5vBG/dUmOgp/0t0XIVETnhCc8ziplzFHXdFc/Nj9LfjQSE9EJVVdkMb1t7RxCw==", "signatures": [{"sig": "MEQCICvMaXkKSNQIq43jIMbGuCf6tXcx9JeF/YHbnuO9iG4fAiBkviFeZkMA8dE8lyxKKvh3cEO7qf6SjTlhJyPNdhRiGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.46.2": {"name": "stylus", "version": "0.46.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.46.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "9e6723aac7b582a9de6a0d1eacef20967c02e70b", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.46.2.tgz", "integrity": "sha512-rODJI/FWxu41vsIwPcV9ghhBR7y8CWMSWkY5bRJ+0cUErEiU/PrceZBmLXuLoCm0g0AeKIvCWfV8VBYW1tc5yw==", "signatures": [{"sig": "MEYCIQCwy2l9jhHxvhvARsbHmGa5+7niL8LSc0HMoKFbl77sxAIhAKdcHiWhAMol3ZI4y+3NJ37OPg01h88mz8h9AodabWFC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.46.3": {"name": "stylus", "version": "0.46.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.46.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "0bb5d602f63fa8088ad3f8affba2c1eb925c4d70", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.46.3.tgz", "integrity": "sha512-iMcg/luUhMUAdXn3hb0lPgE1Qd/s4uTiub/7mKsa/5utazrGq5V6LrzLFu1KHgjqSAw2VPn1e6UfY5fPql31Vw==", "signatures": [{"sig": "MEYCIQC5ZAHA52PNNN6HQ3yHv5C9tR7FzL2RsBwWbxrUVkkKOAIhAPz8LyTVuaNoEnKMweaciZQFcym7d2yrgj2dYB0BDION", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.47.0": {"name": "stylus", "version": "0.47.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.47.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8cbffb7fd9453799d9864f0f27b0d14722f0647f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.47.0.tgz", "integrity": "sha512-w95B4vTA15u1roLqnGUMeTAGTl2XxcVwTfSTJ35LrC7+leb8p6xiS9wtd+Awg/Zu+jv71jFBQJCTDDILgSOffQ==", "signatures": [{"sig": "MEYCIQC5hg3S9QQsI7ytvwEt2lMuEJsT2sG8/MM753to24kd6AIhAM+0EIOmgP9eorrN/Oqe0FFud3C5TV0zLZjr3GLDKEaz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.47.1": {"name": "stylus", "version": "0.47.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.47.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "2822873555f8dcf920a4cad5b9e2813c4de68f6a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.47.1.tgz", "integrity": "sha512-h5u5mbC19tQZMP818UhYPs4IlqHaXR7eUHZpQYUkoVQeFiLyImEtf/GzeUavS/LieXj2rvPddppr6OA4qCW1rw==", "signatures": [{"sig": "MEUCIQD4Cs/K3XOOWD0tI0JrY/R+ffVf+S4z0S0gicTaB2T/DQIgJGEG2mqsUeqEkgZfHyuBCmt+mHWIWteZBQcdytc6qGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.47.2": {"name": "stylus", "version": "0.47.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.47.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "41267ec953d2cec34f71b2aed899218ca913cf58", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.47.2.tgz", "integrity": "sha512-aSM92mvO3d0HMZmF5/itoqGsrqcjY43jedaTRTWKT37ELQaElkfinNeQz0Na6QZSkp+ewsinfkSa1gMxFwKtGg==", "signatures": [{"sig": "MEYCIQD+11S7hSWXTiLfoWlYyp5op457kuK3+zyykUgLvM5V2AIhAMPTJaj2WyT8qU9u5une9esm4mSDhqToCikWUXlBKKT5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.47.3": {"name": "stylus", "version": "0.47.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.47.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1bd905c6701f80653be1fd15d14807cc462be9f5", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.47.3.tgz", "integrity": "sha512-zRzjghf+l0KRPe/l/fJ4d+yA/h0XiulucE3WjzuzF1KY72MwoBNwhbH9OnWgzQQ4mjvwy5LkeMyav5Nqjcfm/w==", "signatures": [{"sig": "MEQCIHczquN+vc3dcu3ykBxqnf6eS14StwbomOSqrno2+F/qAiB0coAxkS1r1k+C/88Mkue/2s8eh0lMnM/MhPQbe7zB0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.3.8", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.48.0": {"name": "stylus", "version": "0.48.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.48.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "a8de8341b1cd89efb9161050bf87a72d65485795", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.48.0.tgz", "integrity": "sha512-SlqWSo252UkFVsg8jQ9BEfFwPzbOtcwnvaaMR8IvckspJ1umQFpKD7q0BZPo3R3Uvusqsh635H/s9pyTalGLrA==", "signatures": [{"sig": "MEQCIB6tpwpUsSeSEdn+CaNrQacQZFo/JY85loChFfcCfoz3AiBLH+ZXsdTAZf7t58okVF0vFTirGZOUHQGW94wwQ87FqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "a8de8341b1cd89efb9161050bf87a72d65485795", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.48.1": {"name": "stylus", "version": "0.48.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.48.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "612c1b8806ce2c35f37cd3a57ad932c883f7ff10", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.48.1.tgz", "integrity": "sha512-U1+gtuQPLgBoqvte6DUkYx0vEfNDszzE9FDq+JphE7F5FZDbx8amp1CJT0PnHV+odcArOP37ZuHJzr9XREUq1A==", "signatures": [{"sig": "MEQCIFgifxl7wEdj7QELOD4S+0FbSSYWewuZSJfmnGXHGLm+AiB0FuXuLS79CSgNPdXV/f0OE58NbSR990QVIRowXc0g8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "612c1b8806ce2c35f37cd3a57ad932c883f7ff10", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.49.0": {"name": "stylus", "version": "0.49.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.49.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "d1b03b319f59acface0e489e6424a155b05fbc63", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.49.0.tgz", "integrity": "sha512-NdKmzdW0obNXnpeRScwe/DMNl4E4JbabfCnwwu0Qp+4l3RaybQAHMjFa3NA5igrbhWKE3pigUhPxo8hWpO2ZeQ==", "signatures": [{"sig": "MEUCIGDGTcQ0DP1FgiTMGXqiagcgzaKvzTWxMyyQpsrnM3bQAiEArLH/3TMQ5OC0ZRcTGaGATNcKmGE9aJm89xO0BKKNoxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "d1b03b319f59acface0e489e6424a155b05fbc63", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.49.1": {"name": "stylus", "version": "0.49.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.49.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "c915495675ca77847a7d4285dd6d6572bae34448", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.49.1.tgz", "integrity": "sha512-9QseIO3I0piYooYcbCV+cuQZiOPf5TRpF0t8id+gycPeSq7qDr4DJofvbay8DZDh7wamlTqzxywSxqcF3U3IcA==", "signatures": [{"sig": "MEQCICXJMkI+wwNyPDxr4G2he3uyKZS64RkwGTG3x7LrRNZ5AiAUqX0uLNaGlLhqCUghN2XIVzWU8U0OfefV2mwvtFsg7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "c915495675ca77847a7d4285dd6d6572bae34448", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.49.2": {"name": "stylus", "version": "0.49.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.49.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "c72a9ea9d904d24bb07c8fd609e6abc28620000a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.49.2.tgz", "integrity": "sha512-2tkKOIbqHR+4ZCXFVVSJp9Ha1RoJdTkqZft70M1Y/ZnP11Vf6siSmdAZPY9t4YCVbC6KNooZjLojh+hiZauU/Q==", "signatures": [{"sig": "MEUCIF0QP/oibE0aqh998+T00oZZQt9Xvlk98n1LUOcewY+VAiEA3X48pXD3h7KYD1wXhkYzwGz3jy/a9hIs+uOHhqO8FP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "c72a9ea9d904d24bb07c8fd609e6abc28620000a", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.49.3": {"name": "stylus", "version": "0.49.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.49.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1fbdabe479ed460872c71a6252a67f95040ba511", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.49.3.tgz", "integrity": "sha512-HPoTajgP1PNFFXW7IjTS10XiY6TMlRe3aippyI9npFMjuZLAdzMSqnrKC2OfiBPLsROhazOlkYIvN73HhX1cVg==", "signatures": [{"sig": "MEUCIDzPOOQh0bQDsvD5o2AW1qefI5tYSFmfesFho+QkI6nmAiEAzXa0e3ceOSmO6pEsP7vAmbmPFeiBNijDFE3xlose7P4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "1fbdabe479ed460872c71a6252a67f95040ba511", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.50.0": {"name": "stylus", "version": "0.50.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.50.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/LearnBoost/stylus", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "2391f0df1ce1dde55a5a8df26b6906a9425ced05", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.50.0.tgz", "integrity": "sha512-dSlDuUxGknqfsIvnK1XeMMYaurIDsUPC3YVw83mEiAij3TxmQr7pZInM53khp8YkZLfpx24zNYKjweQYpo+2yw==", "signatures": [{"sig": "MEYCIQCZu82P5oe1zS8il6NrlDITbjEL2/pRsl3Jd8uL8qKdnwIhAP/C0GfgDOmBbGDsAn/edhRx5rnYyDLBkRIB52zaWunx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "2391f0df1ce1dde55a5a8df26b6906a9425ced05", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/LearnBoost/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.51.0": {"name": "stylus", "version": "0.51.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.51.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "1c7466ec305ef0141ba9980a2fc7d2b0b76d48e6", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.51.0.tgz", "integrity": "sha512-ndFueqdVhg+U5gDY/6xI+RV8Ygj6DQO+29AHrxl/pnRnPKL00zofmXDsF776SvIT+ZgG2SQ/y1kZybSNTE8zZg==", "signatures": [{"sig": "MEUCIGkHnEB3moCMxVDBHUmg1FfSU45RPS9reQPij1wCBPxGAiEAuu2Vdu10bLGheGGGvJeRNVIjDJQgt4479f1HYltO8R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "1c7466ec305ef0141ba9980a2fc7d2b0b76d48e6", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.51.1": {"name": "stylus", "version": "0.51.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.51.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "d75c405c1d87d5e00ca5758d311cd79360f0fb99", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.51.1.tgz", "integrity": "sha512-PVtonRerc0BBjcDga8EM6g2Fsp6gcj4LzR8TjuQSd6+yjz63Ik+KhsrjjagO+HdN9/Acoi9FQMvB//IjDwEWhg==", "signatures": [{"sig": "MEYCIQDeLS2DGZjyQOS1NhAZ0VT8NFvvkzw6wf7kuY1Je+u/vAIhAJ89DMBRg/jsLJyFn2ENNSOASojpPd2bQ5gdSSY9TkrS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "d75c405c1d87d5e00ca5758d311cd79360f0fb99", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.52.0-alpha": {"name": "stylus", "version": "0.52.0-alpha", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.52.0-alpha", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "811bd7862602de3ae620fe7520fb6f24ddd3ddc2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.52.0-alpha.tgz", "integrity": "sha512-BW709qNUq32HfLQM+2j8/OAfZzld9ST74nNh2iT95NZBS+uh+IuGaDwEKjFQIp/84drQnxS9wgN2kldqdNWaew==", "signatures": [{"sig": "MEMCHwhlQFMKlAEbNeGjAjAfBYU1579HmHnWUD3CrzYfx/cCIEO/3vdfU+ZYVuUeiIv33iIqIAaRG0Kt7ZXeKTIx2irq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "811bd7862602de3ae620fe7520fb6f24ddd3ddc2", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.3.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.52.0": {"name": "stylus", "version": "0.52.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.52.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "46cdcc0d3a12b703a1c459464b0fc8921c48dd00", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.52.0.tgz", "integrity": "sha512-QYDZxV4qp0+cv4IBsnuifelsBYls34/dVSLp4nxbtZ4pBGQGzRye9zKdBtztrHlCXuh2tNjABPAhb4E9OrTQaw==", "signatures": [{"sig": "MEYCIQDpXeoPRP5BcHvQg9t/ot02ap9Gx5lpv0oRVNfUJQvNZgIhAIZs2Wiq2ZX411oJ8FL+sbzj+RxJXEQmi0QiRJXr4zIa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "46cdcc0d3a12b703a1c459464b0fc8921c48dd00", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.52.1": {"name": "stylus", "version": "0.52.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.52.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "8c847864dcfb63cf60c81768e31dff54dd897ccc", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.52.1.tgz", "integrity": "sha512-EWStYomRIa5aK8KSrKOdtoBzB+k/+fW2cDw/pPnKmhGVghb2h74xOoGtrATUNBZ+g4jgXwXR/yhAVX4g9aFzYg==", "signatures": [{"sig": "MEQCIAFbEtVKe4ABYw4QFqVRJobTtXx4f3chK7YYVowwXC4NAiAdMK9GdjXNcK4Hv875avFwWy40Lx41Qq9drDkp+P5Dnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "8c847864dcfb63cf60c81768e31dff54dd897ccc", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.52.2": {"name": "stylus", "version": "0.52.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.52.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "963ecd7feb165306ca10a7fc849a2d018a991510", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.52.2.tgz", "integrity": "sha512-hJFI0JW3UzKFDeqm8+h93TJ9wTsSEsIzirBoSHAWedtrmZJlPKDzaG/QXzM9SxfbdWMQFxsJEONBGttvhSDU6g==", "signatures": [{"sig": "MEQCIAEOBl/E9NS6HRHQ0v1b7aRC9mwdj2eEOm6UrQgGDVRQAiBQStHKd4Kv54fXUyw3y4dzEt1DDIBy6cLEOb64bqadRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "963ecd7feb165306ca10a7fc849a2d018a991510", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.52.3": {"name": "stylus", "version": "0.52.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.52.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "f24e6c2f7def49301a0dd7e893a4cfab3b88ad9f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.52.3.tgz", "integrity": "sha512-bR534+ptkyAkQLh4rVlDBmlyZO7NiQ206Z8UQNVXpquPWfWmiP+uf7v/H//4E3TFZ3IzjoPTGl7jw9Vp8C9JJw==", "signatures": [{"sig": "MEUCIQDXJQyjk+tTVxUXtViccoRzvIwzVipi5bxq9eW7LW06EgIgN0VwS57kW/9++LP4/Su7ckrHPQISshXyrlhD9ERvbd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "f24e6c2f7def49301a0dd7e893a4cfab3b88ad9f", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.52.4": {"name": "stylus", "version": "0.52.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.52.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6551b5f0bfdcf29ee7f0fe0a59b7eb6ff26d2539", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.52.4.tgz", "integrity": "sha512-m7qTTjX0/AQsTF9j16JPfZTzS2nTTBVVfP/naU5pZS1IVsbIY0LVBsi1GRG+h3rJx3FsmFMp43eB5+e00coH1Q==", "signatures": [{"sig": "MEYCIQDxBpKfjZMcenIDGa5JTl5H+13o9ajgfn15cM5m+rUrxAIhAK3BqfWnK0DO1VINmgcD4v1fAbkX2Ed+lg6GRxZt4/f7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "6551b5f0bfdcf29ee7f0fe0a59b7eb6ff26d2539", "engines": {"node": "*"}, "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus", "type": "git"}, "_npmVersion": "1.4.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.53.0": {"name": "stylus", "version": "0.53.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.53.0", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6b51e7665097f8dd4a6965e14ceea5e4b9fd724a", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.53.0.tgz", "integrity": "sha512-it1ZZSCdSeohZJGWCfFWuKbI9KrBl8g4VlV61d9gyl8yf3COoOk7L7S87yWZrLbczFAPS3FJdZsYiR6SPwhsog==", "signatures": [{"sig": "MEQCIFhBOph4Gp6/bJlK08jqqwOx7Db44IyvwXmYlv5s+aJeAiAWd8+PrHA7I9bhXR7u9i8WTIZGuD/EVtiZSyR9WNA1qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "6b51e7665097f8dd4a6965e14ceea5e4b9fd724a", "engines": {"node": "*"}, "gitHead": "a0b7cc61feb8abf361a1660710f8f96aa8aba309", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "3.3.5", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "0.10.28", "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "2.x", "jscoverage": "0.3.8"}}, "0.54.0": {"name": "stylus", "version": "0.54.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.0", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "f28bd6c49cc9f93632355853817c28ef3e7c756e", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.0.tgz", "integrity": "sha512-mJjvdHo2UauH54Rui9/Xly7kY2Twyxvt1tVJA0bLfKEJ8BaTSvznHQYdsE+wXIPOm5rSj0DHyoAhLZLmgIAElg==", "signatures": [{"sig": "MEYCIQDuQ8Fxaw4wLo6GZhnuMTnaVICjorl9J4CRrhWAz0BeNwIhAK3hPqYR0IJuyBqO1xhqRLHae2EuJ0PGtv5D7onVmgAD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "f28bd6c49cc9f93632355853817c28ef3e7c756e", "engines": {"node": "*"}, "gitHead": "26e04d862a9dfbbec9a39bc4747d9c66d5eadf19", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "3.3.5", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "0.10.28", "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "8.x", "jscoverage": "0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/stylus-0.54.0.tgz_1457194328491_0.033713654382154346", "host": "packages-12-west.internal.npmjs.com"}}, "0.54.2": {"name": "stylus", "version": "0.54.2", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.2", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "77f0da93cde5a55ab917496850b7ab5214474a0b", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.2.tgz", "integrity": "sha512-tlymppNSjDAvf0ySJ9Weg2EyyUDzShfh0TGLbLWk4Sd8FmuP+QUlSvuFxy3CU2YlxnDQVoA76tIs8ZdzZPIk1Q==", "signatures": [{"sig": "MEUCIAEaPl2tvUZCkfimdhQcn806sAQoe0F8vFKRhQ/0IIFjAiEAyPjHfIqej0BzHd/1xOMTSAg8jp+um/MMlWSM254sQEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "77f0da93cde5a55ab917496850b7ab5214474a0b", "engines": {"node": "*"}, "gitHead": "0ac8abe202160172c3b1bf8cd28cbf17eedfbefd", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "5.8.0", "dependencies": {"sax": "0.5.x", "glob": "3.2.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "8.x", "jscoverage": "0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/stylus-0.54.2.tgz_1457731608401_0.5718708764761686", "host": "packages-13-west.internal.npmjs.com"}}, "0.54.3": {"name": "stylus", "version": "0.54.3", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.3", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "6343fee541fae60531a8da459dc9437700f0f4af", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.3.tgz", "integrity": "sha512-4eb8eGPKZLFVpMY+l03IhFUJFwGbNUJ2WxQZdEhKkb8l3R3uzcuWstcvfpPcGhnG+1Fm9NfxOW0W/GtBt9bCmw==", "signatures": [{"sig": "MEUCIQCc6WLSoxrYx+p5XXKSMV6M7QAXpkUcRGCoUzFp86ed+QIgOTK+CQzaABmGfQyxK+0ebKTna4VlwM1Bb+6QPGNFWcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "6343fee541fae60531a8da459dc9437700f0f4af", "engines": {"node": "*"}, "gitHead": "91f280af8ee1312c62cad87acd4e27fa76456146", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "5.10.1", "dependencies": {"sax": "0.5.x", "glob": "7.0.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "8.x", "jscoverage": "0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/stylus-0.54.3.tgz_1461701513747_0.8110062405467033", "host": "packages-12-west.internal.npmjs.com"}}, "0.54.4": {"name": "stylus", "version": "0.54.4", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.4", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "b0b89201a20b653029223b5a896664e590dd9a1f", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.4.tgz", "integrity": "sha512-zjyUaJ+okzUlEsEpcMMK9BBNQo4qlXoL5lmquuOnNBs3LPbjSFf/245AOAgMbQnBQxDlteanpDDrY9R75EBmmg==", "signatures": [{"sig": "MEUCIDYX88ZVd3ShOPTfNUAxCdB44k1GDq/NrZzNipiLT8+TAiEAsHjWTI1nJLqKm9tCCjEST/xu7dLt0l4L8N8mO1X2xgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "b0b89201a20b653029223b5a896664e590dd9a1f", "engines": {"node": "*"}, "gitHead": "9d341936eaa56e3758a6b915d5e195423342625b", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "5.5.0", "dependencies": {"sax": "0.5.x", "glob": "7.0.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "8.x", "jscoverage": "0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/stylus-0.54.4.tgz_1461777061500_0.7129299163352698", "host": "packages-12-west.internal.npmjs.com"}}, "0.54.5": {"name": "stylus", "version": "0.54.5", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.5", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "42b9560931ca7090ce8515a798ba9e6aa3d6dc79", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.5.tgz", "integrity": "sha512-4Yzg9aqLf3f4sDvO3x+Fbp2V634j9ikFGCFokIPYi+7Y4IG/nxAiPUs95MRlo+lPdTsxAs9wCzEclmPccItISA==", "signatures": [{"sig": "MEUCIQD81FcveaxTYA6PAr5GZcCdDhE/o8EnD59d1uk1a7LbggIgPKLueuHZOdBh3vxpLRwXOi1bA5KHFjvbja4qW0eYvpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "42b9560931ca7090ce8515a798ba9e6aa3d6dc79", "engines": {"node": "*"}, "gitHead": "5aff5ed0e3f482ed48f30110e24fccad7f5559ab", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "5.10.1", "dependencies": {"sax": "0.5.x", "glob": "7.0.x", "debug": "*", "mkdirp": "0.5.x", "css-parse": "1.7.x", "source-map": "0.1.x"}, "devDependencies": {"mocha": "*", "should": "8.x", "jscoverage": "0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/stylus-0.54.5.tgz_1461796984140_0.8051077802665532", "host": "packages-12-west.internal.npmjs.com"}}, "0.54.6": {"name": "stylus", "version": "0.54.6", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.6", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "e8d0c540efd748075c763763f955946ab36f0f66", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.6.tgz", "fileCount": 153, "integrity": "sha512-6s7jEoHKSdAaEtfOnGCNJWy7rcgxPf/pdSUUCusnfV6qO28OmmOLAUYeIJIgJlXWfWTwDO9ytRdd8KhxIdB4EQ==", "signatures": [{"sig": "MEQCICmG2UWlXvuhRokHPDzvcZpg3vuHynllTboc/L3rhd2uAiAX3ziPPqEtucbA6zatlggG6XQuj4mLdubYL8/eXHL/bA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdW86ECRA9TVsSAnZWagAA3U4P/3UnRU+NTQdhRXTQov/H\ndp9MCM2RUg6vaU8o/rDNEdgPJs7LfyGgg4Fg2BzUAtiXHULskrfLoqShE7UL\nByZZcmKisYMbKFMz6tF3vvcEtkdn5QCqsi7TDx23J4+Z5DlEg+GoiU7JBUGE\nKLUwbT2V8qB5ccEmRtxJb7Tbd6E2WX/PhTz3QL7FlPqXotZWpl4dHki12e+R\nOJpYTu/Gj0fe1aDh63gQfNgBNannP0gJ5DvtEtTSlj1o0qFouUA3WRss9F4K\nTQdfc7vua1vG0q6VT8HKyoLZqbcin2Xz72o6o94e6GURkgeJeI/ZCiohp2EE\nyTpTa5ZaZz1m4do9PUC74i65lXBU+U7xVeVTKetDUNyxyQIqOa5+S6pYzOVe\n8t+pkmK8dHm+tRr7EghdrNm0sy6ko4k4HN+vsWrwU0TFRz8siN7zjJ3UONdL\n23NvuSmwkoSGlw6M3mDARGZ5KSq6rhkGz0wUvbJ3c9d6TYEqxQhcwxJ2oo9u\nWSgato0fvUUC8arh/+aG2QGmDw/HUOWKSOT9BRF7QsRkQxQ0bbI4ISSXZWoZ\ncbA4WHxDbC6lHzSHEZVxRsIDCRwP5s7xbb3s4jbuDLi0qPPaMqQdDEVLrkKn\naz5Zwi2GXWP1X0iI+JsYyKHkvveNiySeAMPr7JjtobPs01lCaWdYMQLAc0S2\nZsCD\r\n=EEEi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "1b434aba52e9477412b7d98fd2d3a872a85b3d49", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "xdan", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "11.6.0", "dependencies": {"sax": "~1.2.4", "glob": "^7.1.3", "debug": "~3.1.0", "mkdirp": "~0.5.x", "semver": "^6.0.0", "css-parse": "~2.0.0", "source-map": "^0.7.3", "safer-buffer": "^2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3", "jscoverage": "~0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.54.6_1566297731733_0.20898580020757085", "host": "s3://npm-registry-packages"}}, "0.54.7": {"name": "stylus", "version": "0.54.7", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.7", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "./bin/stylus"}, "dist": {"shasum": "c6ce4793965ee538bcebe50f31537bfc04d88cd2", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.7.tgz", "fileCount": 156, "integrity": "sha512-Yw3WMTzVwevT6ZTrLCYNHAFmanMxdylelL3hkWNgPMeTCpMwpV3nXjpOHuBXtFv7aiO2xRuQS6OoAdgkNcSNug==", "signatures": [{"sig": "MEUCIEgM06KcH5mpImYbsfRW+SxXVZsVtyAwu627Byq/IDOPAiEAll/PVlqMfEQbsZIyKXUWaQDJTMl9+H+KfJGMdEOcJ7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXS2pCRA9TVsSAnZWagAAb1AP/0sAFJlLFgmxJyYSNzc/\nNJcoHry8b/Y/XSxDVb8+8CuOfiQ8M8YoZI3YonvkOLGt2Wiw9EQBKOS/F/19\n52qV7WOZLWghSjQ6teeeeBtLw5+/q2JpVNmi3SUpYn4M1kK/zJFovkIyrwzu\nOWpCa8BfzAkfjuW+5RIbXoNZBq1E4Zr7lzeEoT4WdSKc2Cla/AdTWfurnkhk\nH0dlyme5cQ0JQY4Egx2HDv7FucqZokhAetohRw/xcsmT4rn8hz1ekSfqJ7v+\nb8qRl9M0UlqiL7YHQqDqNiJYB9baj2o5csXJO8sMNeOZcUFQr6OSZehiphlD\nmXm9Uhp74I2Mz8jQQz63GB6dav1f9i9A/xv66Tjjel7/BAit4gTpok3p73r9\nFFEOcdWamq1vbpHNZIBreLPx/BMZBqu5s6Kvt1YykQtKW2eZQ2UECUKD7zEF\nUvbszqTBIr5R1vs/d3fv64xTo3TEoUgzXTgmH0GIgE148k228j+Sh41CKi5F\nG3QBmLqdH25MbVq2ZhdRDOosdFKkvHCfztz3TXz2joxmLSJ05dhcUDXgS0nk\nTtpixdVTl3HfHfOy0oTymva0ml2FM8q2mQ7zhe+tUlDnIQf44YWtdTp3unbt\nFMNoOTUcNvMzaA0E2CscaY/bLjb5bLxnlqRHKLa7uQFkdHvrMi6yiUU5Rmt9\n5qk+\r\n=SFCa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "97bf57dd9439f33f2bda20c36be82b7e0d4e3da9", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "xdan", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "11.6.0", "dependencies": {"sax": "~1.2.4", "glob": "^7.1.3", "debug": "~3.1.0", "mkdirp": "~0.5.x", "semver": "^6.0.0", "css-parse": "~2.0.0", "source-map": "^0.7.3", "safer-buffer": "^2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3", "jscoverage": "~0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.54.7_1566387624892_0.6224406881342215", "host": "s3://npm-registry-packages"}}, "0.54.8": {"name": "stylus", "version": "0.54.8", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.54.8", "maintainers": [{"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "3da3e65966bc567a7b044bfe0eece653e099d147", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.54.8.tgz", "fileCount": 141, "integrity": "sha512-vr54Or4BZ7pJafo2mpf0ZcwA74rpuYCZbxrHBsH8kbcXOwSfvBFwsRfpGO5OD5fhG5HDCFW737PKaawI7OqEAg==", "signatures": [{"sig": "MEUCICgB7nfOUK6fI9w98ZTJA6nM0rllwSIKVQbQtLmD1kWGAiEA6SiFCAdxM9649eFBFu1+Ktmv+ZmtuBrkwSVzIi6a03U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEEMdCRA9TVsSAnZWagAAgEgP/1jGmpmreaQt7hrQ+Dab\nXxycmXLisBrdtwmQUUbCmGubSPmI39VilJuh7OlIfeANYdl7TX3EWH/Gx1i6\nA31zNLpFUBCnnkMbcSrk6vaOsGZxt4Jb7qBDyZpesaa5M+zDUEBFUN63FtL/\nOM3fD1d9Qww05Gc+OY0ElkiedT/hy2mHGOCaPIEK+ts4Q/nIqAPyp6k66H8J\nl++6L7Wa/rZktfo5JSo0rNrxlWK3Wxb0lFI0GzxLYB5l6/ZHfcHH5hCM+MiX\ns2bjX+zQEyKQEEHpxKayZ2kjw+jmZFmCPQgQlJhSxLoWshHG2nGaVUuODh6K\naX6nqPkgYuEYkai+XTfFJLloZjWZkDf1NJEJprpzUPvMciLZUh1CmFQ1DKjN\npI/uI3vXBdEO5e0v5dhbf414pvPoIDysolD8/e7IuJVGMsXqiFU4EyPLox77\nqw2N3HcNiwP+4seRirNurvlX/a59elkiSvQ/AccQaax/IWUUcQN1ogK0EhRw\nMuxLx3X6hDoVWjvJNHEU2gzaPl5k1uKwAQ2ZjeAe4kDa/DyioT+I+eWreyue\n+7NXBHzvkuhmyWEr61xQF8mM32qFZiWw/WSpnbSNDyWfUpEgDWH61FB1r8f5\nG3uAQigTdJBiWTmX8zBLXl68VP9mKR/RT0AxNc+xRiJutS4uL+mi2Hy6km7f\nsRK/\r\n=9LtF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "0087d4b977190fb83f075b8a5b0bd2a0bc3cf631", "scripts": {"test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "xdan", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "13.9.0", "dependencies": {"sax": "~1.2.4", "glob": "^7.1.6", "debug": "~3.1.0", "mkdirp": "~1.0.4", "semver": "^6.3.0", "css-parse": "~2.0.0", "source-map": "^0.7.3", "safer-buffer": "^2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^8.0.1", "should": "^13.2.3", "jscoverage": "~0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.54.8_1594901276651_0.15631931239008923", "host": "s3://npm-registry-packages"}}, "0.55.0": {"name": "stylus", "version": "0.55.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.55.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "bd404a36dd93fa87744a9dd2d2b1b8450345e5fc", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.55.0.tgz", "fileCount": 144, "integrity": "sha512-MuzIIVRSbc8XxHH7FjkvWqkIcr1BvoMZoR/oFuAJDlh7VSaNJzrB4uJ38GRQa+mWjLXODAMzeDe0xi9GYbGwnw==", "signatures": [{"sig": "MEYCIQC7OGc/1Z9WubxM2cOBTZDZHCThYbuUxhYzHlV4g60RMgIhALLlB+n+frPXDbr2wj+1r46YTW/7La/4vVvzR1Fsipju", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMx2CCRA9TVsSAnZWagAAmYgQAJRd+94CvBeDeQJrecNF\npZ6Bpk2tS4aHePQGCWsLZ4lb9Rqd0ub3ZhK3iOMLZPRGQFZ1Vhzgxa8pPB31\n3IMT6ad4PPTRDv+wZT9rCx2cYktdziQ4VYw/wkTuyDdo3AiuHQzppPRgEaKG\nGLNIrr1aykLsbNXcYtzzpT9VyFxKlAyltyOQu3bvOM6oaR4bbB2YB7r7EaSS\n/R9l4+30ibfMOc6BAJ01K4DpzKQaRaImAd3umESS/5lwtE1TyQIzrO39ipO5\nQ4fpiMlcC0BunPvD0ka6yunMgg4F2d9XlfGk4Bnkbz08EPtqNrQAwPQYxmlF\nndbB1WcTVwBQBiA1KoaMtaKA8kjs9a4h4FUNzKaCTZWk3CDijRAXaurEcvz+\nji+qd9kc8NmaL5DfkwPKHg4pf+DhEIdkb5iIs33qM8m76PZ3vEV3nLp5VG54\nyyU0DcU2QeLyp6E2gx4/7Z8bBqo4qysqh9svKU682iEk+ebSOk8Y64SaJnzP\nkrnTomfJsMXqXvP8XDhS48GUWOd3aKC8eJS2lt04ZXIxlvHaeA1BRBSyFk9F\n6znUaNZuk3EY+z6plJJXXxLb8VycZTHa71Z3Q1lLN1WfkFghFkE3oaRRRaAw\nT9vRfpLUZARKcNMlKxpdWvxJbYQgUaRlIaVvq1Xa4lKsvarM7BUItMjWIbQj\n69eA\r\n=oPrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "9cb7635af60bf918ed8c2a990efd251e2e825975", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require chai --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "14.17.4", "dependencies": {"css": "^3.0.0", "sax": "~1.2.4", "glob": "^7.1.6", "debug": "~3.1.0", "mkdirp": "~1.0.4", "semver": "^6.3.0", "source-map": "^0.7.3", "safer-buffer": "^2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^9.0.3", "jscoverage": "~0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.55.0_1630739841468_0.8592832464821087", "host": "s3://npm-registry-packages"}}, "0.56.0": {"name": "stylus", "version": "0.56.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.56.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "13fc85c48082db483c90d2530942fe8b0be988eb", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.56.0.tgz", "fileCount": 144, "integrity": "sha512-Ev3fOb4bUElwWu4F9P9WjnnaSpc8XB9OFHSFZSKMFL1CE1oM+oFXWEgAqPmmZIyhBihuqIQlFsVTypiiS9RxeA==", "signatures": [{"sig": "MEUCIQCnkhBy1WwfewBm14mgGLqSBLsW7uDgl1aMvOzo9NEYAAIgGDfSUG+2QYxZf1gkM6WISs16agvbdqv+kWioTnn//cI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvUSzCRA9TVsSAnZWagAAgRIQAIIMP0Iwu/P1lDZb2Ok1\n/aMDZ304PyUKRChwoQb12Y5TjQMj7vJ+vZamSvxIDBzcdv576NU2WLOKc1NO\nOK4aXN6VZQ/rk5oDGKB4aXy669jH2V3QAth5tQ96Yr9/aFj1S63Ih/Irr6q+\nCX5QA6RB9DckXLhQbf4EMYOr5bdp/7wmZSbOZzAV5o1eQm1WgoLTjoZIS8n5\n7WxfAfYMC4zByErp/fBuEB6Iq5NS/qPR3tkptPCuYPuRYatzMPWyGrwxTSrl\n6lKy5fv4KEZjDWmolQsCirluLg3+tcMiOix2FsBqtgGS7LIK0/scQhFPCwPm\nF9eFbZYCsSoAaXhdN/WxfdZg9mZ6V8tuQLtlhOWUCZrL9bkDoBIOqOHtiG0m\nWjotNQZL0MpMicpp0QB0w6mJHcAR2YQNZ2FNPz0/g9ACu+XFpSQVk52/nP+g\nTMjlqgaaZQIJxEJNDmfmyh8Tl4kUFFmLsdl/aeVylM2ayBoboKMU1KMgNQyr\nNPl8fm1bVWFm28VF23oDlpRVPSoZqXg0IXBFS7kEwIiLeh6kjBv525z23rHZ\nR13RxmEh25i7Df+3ZmxNT06/vbE6M5GG7WVEZT4yVKaA8x8ij+2o+/MY9Xqv\ncpMl8QcDkLkOabTrOUDPHCuP41GBBwWfdWDWwIghOgqT8zy7iWuMXTLMvcsk\nMo5f\r\n=fF00\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "fc2e6306a2fcfad6f1213693b26254bee3f1de6b", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require chai --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "14.17.4", "dependencies": {"css": "^3.0.0", "sax": "~1.2.4", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "safer-buffer": "^2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^9.0.3", "jscoverage": "~0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.56.0_1639793842844_0.7308729074233775", "host": "s3://npm-registry-packages"}}, "0.57.0": {"name": "stylus", "version": "0.57.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.57.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "a46f04f426c19ceef54abb1a9d189fd4e886df41", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.57.0.tgz", "fileCount": 144, "integrity": "sha512-yOI6G8WYfr0q8v8rRvE91wbxFU+rJPo760Va4MF6K0I6BZjO4r+xSynkvyPBP9tV1CIEUeRsiidjIs2rzb1CnQ==", "signatures": [{"sig": "MEUCICV1hbrh/2d6yBW7IkZLGSDekN7qxH/1bxsPwPHN69orAiEA4NJ6GjOcNALsayeNoSRWfG9XB4+p++wT19vReICBJak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiEKtgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGcw/+M0Iq0yEPjZWWyijRlxtVEwWQfOWlAbSub/PTjGaqKZimFWJ6\r\ni1F/ohGvXFX+YuLkpE+7f8Rhz1lNsZsVsaOPCKyYu3B3KQK+EsgQKPuGB7kG\r\nxI02fIbEzSmZZfcXpsirHpO9+SBcdF5hd3KilQ9k4khoH0g7Mtn6V+LZw7kS\r\nGbwdYfvSPiKsxrLoudcvYSPXb9ey/PGvdi2rTp3c27LJV8+WqtfmisOrUpMO\r\n+NB0ekH4Ix1KAUW+RCmBA92x7bP7/Nv31QNrwHkEqrbrPPwoYWKZZ3O9AY/t\r\ngdj5v3QF+C6FZ+0EVar8hwGJ3vh3Ppa/EnL5Ihxw+85usxyiUMrRNAIi6t7f\r\no8WTpdqgio4mV13H5RBGdjG+1wtLhwhQ5kBPExgITtiy8bIi2VscuFUhETZG\r\nFhZy62XufYyjJnh0BUDBXafzLKM22au5HEFkWO4/HhfEOTDXw8qvcSJuQvNY\r\ncM78+HsxmSjgyxwcqBhSgSIJI67M8HyptaPDUPA3YSo0DCkyCpCsfO4rEh/f\r\ncqjvQu2su5MR8CT1MNTQubeq96vQgZykDyFrFMfYhZb8AAfxkKga8wNaL6b2\r\nf80O79R4+nfIodMIm3C0iQeunNx4gKfS7y4aoqJPgsFH4/sMieceyu7D4X1f\r\nyqI3ZJ6GU5L5vC7n4koPMek0joT993b05R4=\r\n=atv0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "bc1404aa1f6c03341bd76529c8cf4beb4f3d99f7", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require chai --bail --reporter html-cov > coverage.html", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "14.17.4", "dependencies": {"css": "^3.0.0", "sax": "~1.2.4", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "safer-buffer": "^2.1.2"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.57.0_1645259616701_0.5520700629008688", "host": "s3://npm-registry-packages"}}, "0.58.0": {"name": "stylus", "version": "0.58.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.58.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "1125d8381173356b22c675f165c17da3ec81f5ff", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.58.0.tgz", "fileCount": 144, "integrity": "sha512-q27Pof0PINInv/gTO1AH+6C1VkoedEOPp38CB/DFWdSgu4fBCwnxM8cZEm4M1zU+Jr6rmZ75hkmqk2QyDze10g==", "signatures": [{"sig": "MEQCIH1nw1mrd3PYPvdDHkbACJi0mMzAYdwyvSLGD7sdUwLiAiAzi6ouHyhHGUiyAW33i1+iumU80ZH56aWsC7BsKnE6KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 445017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikdSAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOjBAAl5HdPIj7R8d5PDYKsnTc1lb5VlUHhpNAVjLFXx874Cp9HHEY\r\nWpIFIc7JVmaXhUlvUIRF86OlHd09grAN4JHgjGiwXTruMSG1bPXTBotOGqko\r\n9UHIdMN+tkOVVtXX8MyxsrJHjFwPLL0+aGoZgBCPNr+32AjtFGYnyDeBaBO1\r\na9M4K29I92Gj/twAVUZBYADfSi6NRZtDePvK7jyfYONA6t4q0pqDp1n99CqC\r\n5Np3/pU7nd9OdobCgD1g/mdDSiX7r3Q4mR63nvd3Pkglnf6oZaYzwmbUt2+G\r\n+Mn7vsDDFVFfleehLXfCA0oEnGHJER+MOgyfstWNGTwXrUPzKeOsoyELk/1A\r\n7z/+3muvT58mriD26g145TOZ5zkrybBm95X1J8DIQu18I/gHiEAAH3x0VIei\r\ndk/wgDrQn82quh83Y/b/oOnXj5XKLLBq40tApocJhwsTY92Tli0lJVJZ706l\r\n2/WAPnOEclgNi7kMiehKsCbUB2GOIEgS4G2jB/KPkrOPzEc0zKivV4x1GKYA\r\nkuc+qBrOx+I9Qqze8uolSpfMOwU5HU9q9l54edHvZoHEymXyJLjyatVfUsnx\r\nZA7Rc0QfYy3RoUL4n8c8WwdvaovewFMyV5mNMBiKtNhHAYyMDRNZlVBd3QQb\r\neT+qXPrG630fYt1i+ut9xluZs/FS38UNtHg=\r\n=SZBg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "914505ff73b457eef80713344dc072170ae8337a", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "14.15.3", "dependencies": {"css": "^3.0.0", "sax": "~1.2.4", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.58.0_1653724287754_0.12240211397907674", "host": "s3://npm-registry-packages"}}, "0.58.1": {"name": "stylus", "version": "0.58.1", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.58.1", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "7e425bb493c10dde94cf427a138d3eae875a3b44", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.58.1.tgz", "fileCount": 144, "integrity": "sha512-AYiCHm5ogczdCPMfe9aeQa4NklB2gcf4D/IhzYPddJjTgPc+k4D/EVE0yfQbZD43MHP3lPy+8NZ9fcFxkrgs/w==", "signatures": [{"sig": "MEUCIBUREBADn5+PLvcRChdSrGY8760f2sfrQkecEhX8JaVZAiEAiUoU8S9bL+cSEm6iEBzSqUGWm4QiAMuSmwif5KhiekM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilbxsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNtA//bCtg/Lp7n8arc+aKtiA3E2eRznI/ZCWZBLtQgMBDJ12nTUpt\r\nxjfhtuWAmG3UlhSikmuc4iWQtmcZNG+lzYptUP4CLqa+zOaE73EMkLa5j6lh\r\n2eAcU4+u3AEbZaWJzJ+ItzasxcIOlQ4u74nBWwsLSfdPFZ8yREXMIEK4naMv\r\n68o1yAiyboGmIlBNWPO8h5ivjH4cJiYZrKu873Krz4+kGWOB22+QifbA4Zuc\r\nfit5yc6lEhUiz215c4+cCha<PERSON>Z3EwZwRhgMg5d7u+xNyoMBzwI4ybu2BRbyGt\r\n2A+OD/QtVOlP4VjTJO4UO6bq/KC/9NngE9pQPHlnLc6Zop/pDSn11vpKsbuc\r\nVl7rtS4pvSmog8QcliV1OQgYKF2+DhOfNi0fhCJmwPDJXYCnIPw2hD37mLxf\r\nwFXhCD1PbHuowwoALVJeRKRbWfbJGR8X957BDjta4U2ONvSHTaEbigAuyUKV\r\nfn6pErFQ6sNswo60x+edn93G26baFsLlPRCi4yxOFuDZIQfpPscwHbPNHqVM\r\nFzE90SO0mEmZ5sFa13ZutaygWgPDu9jAaqAg9xBxvIx2o2IxLYrqxMVDt+nR\r\nSuEh9fjZXj9LV9uGOjCh5L1y1J/USGuhzxoUH7I6vppfJmGQtiBMxG/tIoeh\r\nv5nIRg+ujpfsCf6wWKxUKQAlFuAi9FihbRs=\r\n=qySn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "gitHead": "52db72cdfa57f6e3545d68a10976ba334c4ce851", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "14.15.3", "dependencies": {"css": "^3.0.0", "sax": "~1.2.4", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.58.1_1653980268522_0.5937306415525998", "host": "s3://npm-registry-packages"}}, "0.59.0": {"name": "stylus", "version": "0.59.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.59.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "a344d5932787142a141946536d6e24e6a6be7aa6", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.59.0.tgz", "fileCount": 144, "integrity": "sha512-lQ9w/XIOH5ZHVNuNbWW8D822r+/wBSO/d6XvtyHLF7LW4KaCIDeVbvn5DF8fGCJAUCwVhVi/h6J0NUcnylUEjg==", "signatures": [{"sig": "MEQCIBDeiQsaeoucR9d27DpPF2pDt7BqMESzdIcti0LshDc6AiBCh2DMptvL6SL1iUnOQJQNl7ziB/D52357DpgtsPYNlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9xjKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZIQ/+O9dOMsA0JYe11eKWr1SQ7VrdDu6WxIRdBpc1rxLAlcdyT3dy\r\nnjkXH+nFeSIScpbSu+MEVX5LJWbnLEweZ3GgZ+niRF1qz6QdmrnG0D2vGChL\r\nk5aLEARFf9/aoEiqrL/bMUQ2kBA7NyeaoawLBmuPHDhmvjjT19myXZJPQDOJ\r\nVXFX+61qUa14ZVkvpnPK7NYVnFd5PKb2JRVYGy8V1/+wRLeFgj1p8jtNoLmB\r\ncLd1J6/qPYIR9vwR6KdF0s/SHuQPif8ZaMtVVbilTakw9OTUerhoA5dhXH4a\r\nDE51f4zG/5vc8RAv73Nvd0I6HzAhsQqV3ErYvsiAhapAv8DD8RKObIT8w47t\r\nN6bEcrgbgp6GTxj5TJz4NSHrtu5jgW7WCxzNAB6lwjZAk0rUM98PnktVTnYR\r\n5x5jzHbDd9OEnJG+EMh9VfJRauF2DLcURFED1utjzysNx5Z//3gqZzcNwXdz\r\njdyYbIkXBn4Qe72kRRRCa6QiK+zLp+Upb4Qy1cAHSbGZdqwU/7WfurSn0XSO\r\nt/Mwy69mJozM9F/pOVEy4tlVv+tLk8kH7o1g1p7ujQGUw/WeaxcbTztacb8Q\r\n4RmOnEbRi1PlxF9JBK+0PIT8p3zIDlYRFe7ay+goOyLfcDSYXpqf6w6F3aZi\r\nqi3qWf+R9a33BFL/RCKSI0ebhmnK+p/EfAs=\r\n=lk9r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "*"}, "funding": "https://opencollective.com/stylus", "gitHead": "8e29ff39fb563f91ef4eaff9b2c9f29e2a1910ec", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "14.15.3", "dependencies": {"sax": "~1.2.4", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "@adobe/css-tools": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.59.0_1660360905846_0.3704175935070799", "host": "s3://npm-registry-packages"}}, "0.60.0": {"name": "stylus", "version": "0.60.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.60.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "0d75f3772929185d580d164d9394b2dcbed21083", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.60.0.tgz", "fileCount": 144, "integrity": "sha512-j2pBgEwzCu05yCuY4cmyp0FtPQQFBBAGB7TY7QaNl7eztiHwkxzwvIp5vjZJND/a1JNOka+ZW9ewVPFZpI3pcA==", "signatures": [{"sig": "MEYCIQCNtKlNu9VIw31oQQplP6GlpEN+pGvNRcTq5FmCyo4D4QIhANMylwv3llKh9dr/laK7g15vNSS/9kpw2AKE2zhK7SaC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366495}, "main": "./index.js", "engines": {"node": "*"}, "funding": "https://opencollective.com/stylus", "gitHead": "74efabf971b7fec4fccd93eb6fb9caac1d621bcf", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "20.5.1", "dependencies": {"sax": "~1.2.4", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "@adobe/css-tools": "~4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.60.0_1693371571127_0.2438799278555832", "host": "s3://npm-registry-packages"}}, "0.61.0": {"name": "stylus", "version": "0.61.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.61.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "d90a4334486a1622cc6d3d5e878087376dc478a6", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.61.0.tgz", "fileCount": 143, "integrity": "sha512-oaV9T4sRBiQfChXE0av9SrLD+ovEdQiWzPJ5kwIeYvMhjUDJnZtdubAG6lSSbaR4sCnoT6sw411IOl5Akcht4Q==", "signatures": [{"sig": "MEUCIQCYOEWOjeLlue92sFOuWEZh/xVE3OYhT6uL5RMsV6D0IAIgMiSUrbA6cdsrdeUozhpUeqfKrQR4h+/HfSZSUzHZoqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365817}, "main": "./index.js", "engines": {"node": "*"}, "funding": "https://opencollective.com/stylus", "gitHead": "566a9f2062e6ee58e1c8510d3feb13eaa3d1d9aa", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "20.5.1", "dependencies": {"sax": "~1.3.0", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "@adobe/css-tools": "~4.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.61.0_1699083321003_0.8483383100287085", "host": "s3://npm-registry-packages"}}, "0.62.0": {"name": "stylus", "version": "0.62.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.62.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "648a020e2bf90ed87587ab9c2f012757e977bb5d", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.62.0.tgz", "fileCount": 143, "integrity": "sha512-v3YCf31atbwJQIMtPNX8hcQ+okD4NQaTuKGUWfII8eaqn+3otrbttGL1zSMZAAtiPsBztQnujVBugg/cXFUpyg==", "signatures": [{"sig": "MEQCICjy995vbVp0RnHY5UnOYeIE0S2r3kBUFfvi1vs7zJ98AiBYpkqjFw9pe0WxFkTHJfOUvnkz33/8q2yhJC6kAfJJxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365792}, "main": "./index.js", "engines": {"node": "*"}, "funding": "https://opencollective.com/stylus", "gitHead": "49e32667d5e1cf84fb28635dbaff0cda156939d3", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "20.5.1", "dependencies": {"sax": "~1.3.0", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "@adobe/css-tools": "~4.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.62.0_1700282693608_0.8212811458059563", "host": "s3://npm-registry-packages"}}, "0.63.0": {"name": "stylus", "version": "0.63.0", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stylus@0.63.0", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/stylus/stylus", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bin": {"stylus": "bin/stylus"}, "dist": {"shasum": "511e8d56f2005b09010fbc1f62561c7b6f72a490", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.63.0.tgz", "fileCount": 143, "integrity": "sha512-OMlgrTCPzE/ibtRMoeLVhOY0RcNuNWh0rhAVqeKnk/QwcuUKQbnqhZ1kg2vzD8VU/6h3FoPTq4RJPHgLBvX6Bw==", "signatures": [{"sig": "MEYCIQDklwQYE2TUEnibSXcZfS7IA+oBm9fXl0FaCYaWrzXsLQIhAOaLPplDJG7CjLMHO69j4PnFwWt5JEqYyGrYweY4drEf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365596}, "main": "./index.js", "engines": {"node": "*"}, "funding": "https://opencollective.com/stylus", "gitHead": "c5b0b90476d102ef730118099707b9fa026a4313", "scripts": {"test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "prepublish": "npm prune"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "browserify": "./lib/browserify.js", "repository": {"url": "git://github.com/stylus/stylus.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Robust, expressive, and feature-rich CSS superset", "directories": {"doc": "docs", "test": "test", "example": "examples"}, "_nodeVersion": "20.5.1", "dependencies": {"sax": "~1.3.0", "glob": "^7.1.6", "debug": "^4.3.2", "source-map": "^0.7.3", "@adobe/css-tools": "~4.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/stylus_0.63.0_1709615122044_0.6494594714976967", "host": "s3://npm-registry-packages"}}, "0.64.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.64.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": ">=16"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "~4.3.3", "debug": "^4.3.2", "glob": "^10.4.5", "sax": "~1.4.1", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.4.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "_id": "stylus@0.64.0", "gitHead": "d6f55c19bc0181eaab472752af2504dc2448a44d", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-ZIdT8eUv8tegmqy1tTIdJv9We2DumkNZFdCF5mz/Kpq3OcTaxSuCAYZge6HKK2CmNC02G1eJig2RV7XTw5hQrA==", "shasum": "af99253f1254c851528c44eddc3ccf1f831942f1", "tarball": "https://registry.npmjs.org/stylus/-/stylus-0.64.0.tgz", "fileCount": 143, "unpackedSize": 365731, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhKM2v75DlRJcOeZRzjZnMm3ir/VJaP9QEfzdvUuKGJQIgQwSK+MJX/V2rpTxeANAlsHOqXIik1MgQpT4K4PuBF4I="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.64.0_1729408330302_0.05604398839014957"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-01-31T18:22:09.655Z", "modified": "2024-10-20T07:12:10.674Z", "0.0.1": "2011-01-31T18:22:10.089Z", "0.0.2": "2011-01-31T20:14:16.038Z", "0.1.0": "2011-02-01T17:39:42.414Z", "0.2.0": "2011-02-01T19:48:31.360Z", "0.2.1": "2011-02-02T17:09:29.991Z", "0.3.0": "2011-02-04T17:40:16.656Z", "0.3.1": "2011-02-04T18:13:38.616Z", "0.4.0": "2011-02-07T19:28:21.209Z", "0.4.1": "2011-02-09T18:35:08.711Z", "0.5.0": "2011-02-09T21:30:15.589Z", "0.5.1": "2011-02-11T23:31:47.118Z", "0.5.2": "2011-02-15T20:47:56.977Z", "0.5.3": "2011-02-17T22:28:58.396Z", "0.6.0": "2011-02-18T17:03:48.821Z", "0.6.1": "2011-02-18T18:05:50.074Z", "0.6.2": "2011-02-21T22:28:20.839Z", "0.6.3": "2011-02-22T19:24:18.863Z", "0.6.4": "2011-02-24T17:10:04.320Z", "0.6.5": "2011-02-25T07:03:49.817Z", "0.6.6": "2011-03-02T03:34:11.784Z", "0.6.7": "2011-03-02T04:13:44.622Z", "0.7.0": "2011-03-02T08:12:01.183Z", "0.7.1": "2011-03-08T02:52:21.828Z", "0.7.2": "2011-03-09T02:34:45.795Z", "0.7.3": "2011-03-09T17:43:43.337Z", "0.7.4": "2011-03-11T00:05:29.814Z", "0.8.0": "2011-03-14T16:14:08.768Z", "0.9.0": "2011-03-18T18:46:27.156Z", "0.9.1": "2011-03-19T00:00:02.730Z", "0.9.2": "2011-03-21T18:08:38.694Z", "0.10.0": "2011-03-29T22:49:17.956Z", "0.11.0": "2011-04-01T22:38:33.701Z", "0.11.1": "2011-04-02T00:05:20.252Z", "0.11.2": "2011-04-06T22:16:50.594Z", "0.11.3": "2011-04-08T23:37:54.020Z", "0.11.4": "2011-04-10T19:05:58.786Z", "0.11.5": "2011-04-12T14:39:59.565Z", "0.11.6": "2011-04-12T18:22:53.874Z", "0.11.7": "2011-04-12T21:49:27.287Z", "0.11.8": "2011-04-15T17:12:00.736Z", "0.11.9": "2011-04-15T21:15:21.703Z", "0.11.10": "2011-04-17T23:04:02.268Z", "0.11.11": "2011-04-25T01:58:51.175Z", "0.11.12": "2011-04-28T01:54:37.091Z", "0.12.0": "2011-04-29T21:41:58.390Z", "0.12.1": "2011-04-29T23:31:00.911Z", "0.12.2": "2011-05-03T20:45:32.180Z", "0.12.3": "2011-05-08T17:29:25.795Z", "0.12.4": "2011-05-12T18:04:58.189Z", "0.13.0": "2011-05-17T18:11:51.309Z", "0.13.1": "2011-05-30T17:51:10.925Z", "0.13.2": "2011-05-31T21:54:08.613Z", "0.13.3": "2011-06-01T22:22:54.429Z", "0.13.4": "2011-06-22T16:09:34.637Z", "0.13.5": "2011-06-27T17:40:49.858Z", "0.13.6": "2011-07-12T18:41:01.577Z", "0.13.7": "2011-07-15T18:10:10.183Z", "0.13.8": "2011-08-01T16:00:48.314Z", "0.13.9": "2011-08-04T23:06:41.335Z", "0.14.0": "2011-08-10T15:59:28.376Z", "0.15.0": "2011-08-16T04:41:11.745Z", "0.15.1": "2011-08-18T17:48:31.922Z", "0.15.2": "2011-09-07T00:27:17.880Z", "0.15.3": "2011-09-14T16:02:12.784Z", "0.15.4": "2011-09-14T22:09:30.777Z", "0.16.0": "2011-09-26T18:44:13.086Z", "0.17.0": "2011-09-30T19:09:17.621Z", "0.18.0": "2011-10-21T16:39:39.975Z", "0.19.0": "2011-10-26T18:41:17.028Z", "0.19.1": "2011-11-08T16:03:39.616Z", "0.19.2": "2011-11-09T18:11:51.128Z", "0.19.3": "2011-11-17T22:28:18.518Z", "0.19.4": "2011-11-28T17:22:33.465Z", "0.19.5": "2011-11-28T18:32:58.864Z", "0.19.6": "2011-11-30T17:29:02.874Z", "0.19.7": "2011-11-30T18:37:33.771Z", "0.19.8": "2011-12-01T18:51:09.548Z", "0.20.0": "2011-12-11T15:23:46.350Z", "0.20.1": "2011-12-16T17:31:54.513Z", "0.21.0": "2011-12-17T18:32:32.383Z", "0.21.1": "2011-12-20T16:51:26.901Z", "0.21.2": "2011-12-22T17:27:40.011Z", "0.22.0": "2012-01-05T00:24:05.383Z", "0.22.1": "2012-01-08T13:59:39.626Z", "0.22.2": "2012-01-08T20:25:15.846Z", "0.22.3": "2012-01-11T16:52:02.470Z", "0.22.4": "2012-01-11T21:27:01.259Z", "0.22.5": "2012-01-17T04:13:43.876Z", "0.22.6": "2012-01-20T16:47:02.172Z", "0.23.0": "2012-02-02T16:43:17.272Z", "0.24.0": "2012-02-17T00:06:18.726Z", "0.25.0": "2012-04-04T01:29:34.803Z", "0.26.0": "2012-04-27T17:39:56.982Z", "0.26.1": "2012-05-07T16:25:51.580Z", "0.27.0": "2012-05-10T19:07:15.837Z", "0.27.1": "2012-05-28T16:13:55.768Z", "0.27.2": "2012-06-19T23:56:32.124Z", "0.28.0": "2012-07-06T23:31:10.125Z", "0.28.1": "2012-07-07T15:41:37.712Z", "0.28.2": "2012-07-15T18:49:20.878Z", "0.29.0": "2012-08-15T15:56:56.327Z", "0.30.0": "2012-10-16T04:59:42.380Z", "0.30.1": "2012-10-17T18:49:49.394Z", "0.31.0": "2012-11-24T18:12:13.619Z", "0.32.0": "2013-01-04T16:32:07.278Z", "0.32.1": "2013-02-27T23:22:55.618Z", "0.33.0": "2013-06-30T16:47:23.732Z", "0.33.1": "2013-06-30T16:59:07.546Z", "0.34.0": "2013-07-12T10:15:06.757Z", "0.34.1": "2013-07-12T13:40:18.904Z", "0.35.0": "2013-07-28T21:32:46.974Z", "0.35.1": "2013-07-29T17:39:10.995Z", "0.36.0": "2013-08-01T16:13:05.798Z", "0.36.1": "2013-08-06T10:56:35.447Z", "0.37.0": "2013-08-18T20:04:13.997Z", "0.38.0": "2013-09-24T13:14:42.222Z", "0.39.0": "2013-10-30T05:40:17.857Z", "0.39.1": "2013-10-30T15:40:43.748Z", "0.39.2": "2013-10-31T07:28:38.811Z", "0.39.3": "2013-11-01T12:37:28.893Z", "0.39.4": "2013-11-03T20:19:22.960Z", "0.40.0": "2013-11-05T07:37:25.321Z", "0.40.1": "2013-11-12T11:08:14.379Z", "0.40.2": "2013-11-12T13:06:42.851Z", "0.40.3": "2013-11-16T16:40:13.825Z", "0.41.0": "2013-11-30T19:05:19.877Z", "0.41.1": "2013-12-08T19:47:55.088Z", "0.41.2": "2013-12-10T09:43:40.593Z", "0.41.3": "2013-12-12T18:47:46.524Z", "0.42.0": "2014-01-06T19:25:13.708Z", "0.42.1": "2014-01-18T20:07:39.831Z", "0.42.2": "2014-01-30T11:44:28.411Z", "0.42.3": "2014-03-03T06:24:18.211Z", "0.43.0": "2014-04-05T19:19:12.929Z", "0.44.0-beta": "2014-04-06T17:48:22.179Z", "0.43.1": "2014-04-07T12:00:24.030Z", "0.44.0-beta2": "2014-04-07T13:18:11.849Z", "0.44.0-beta3": "2014-04-14T06:05:57.568Z", "0.44.0": "2014-04-23T12:01:14.802Z", "0.45.0": "2014-05-11T19:20:26.505Z", "0.45.1": "2014-05-16T00:24:52.933Z", "0.46.0": "2014-06-03T08:33:32.333Z", "0.46.1": "2014-06-03T21:45:35.568Z", "0.46.2": "2014-06-04T07:17:08.661Z", "0.46.3": "2014-06-09T15:47:22.699Z", "0.47.0": "2014-07-01T17:30:12.731Z", "0.47.1": "2014-07-02T16:28:38.051Z", "0.47.2": "2014-07-19T13:18:53.466Z", "0.47.3": "2014-07-22T13:09:36.577Z", "0.48.0": "2014-08-20T18:28:20.224Z", "0.48.1": "2014-08-21T11:27:29.305Z", "0.49.0": "2014-09-22T06:39:58.683Z", "0.49.1": "2014-09-24T09:54:45.087Z", "0.49.2": "2014-10-14T12:08:29.597Z", "0.49.3": "2014-11-06T00:12:31.572Z", "0.50.0": "2015-02-05T08:47:54.130Z", "0.51.0": "2015-04-23T14:06:49.706Z", "0.51.1": "2015-04-28T07:28:48.258Z", "0.52.0-alpha": "2015-07-15T13:31:09.120Z", "0.52.0": "2015-07-19T17:26:47.395Z", "0.52.1": "2015-09-03T12:01:48.333Z", "0.52.2": "2015-09-03T16:14:29.287Z", "0.52.3": "2015-09-03T21:15:23.479Z", "0.52.4": "2015-09-04T06:10:44.513Z", "0.53.0": "2015-12-14T13:54:33.275Z", "0.54.0": "2016-03-05T16:12:12.151Z", "0.54.1": "2016-03-11T20:13:58.121Z", "0.54.2": "2016-03-11T21:26:51.061Z", "0.54.3": "2016-04-26T20:11:56.278Z", "0.54.4": "2016-04-27T17:11:03.771Z", "0.54.5": "2016-04-27T22:43:06.844Z", "0.54.6": "2019-08-20T10:42:11.894Z", "0.54.7": "2019-08-21T11:40:25.172Z", "0.54.8": "2020-07-16T12:07:56.864Z", "0.55.0": "2021-09-04T07:17:22.051Z", "0.56.0": "2021-12-18T02:17:23.044Z", "0.57.0": "2022-02-19T08:33:36.884Z", "0.58.0": "2022-05-28T07:51:27.957Z", "0.58.1": "2022-05-31T06:57:48.694Z", "0.59.0": "2022-08-13T03:21:46.090Z", "0.60.0": "2023-08-30T04:59:31.328Z", "0.61.0": "2023-11-04T07:35:21.179Z", "0.62.0": "2023-11-18T04:44:53.874Z", "0.63.0": "2024-03-05T05:05:22.258Z", "0.64.0": "2024-10-20T07:12:10.493Z"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/stylus/stylus", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "description": "Robust, expressive, and feature-rich CSS superset", "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "readme": "<p align=\"center\"><a href=\"https://stylus-lang.com\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"150\" src=\"https://raw.githubusercontent.com/stylus/stylus/dev/graphics/Logos/stylus.png\" alt=\"Stylus logo\"></a></p>\n\n[![Build Status](https://github.com/stylus/stylus/actions/workflows/ci.yml/badge.svg?branch=dev)](https://github.com/stylus/stylus/actions?query=branch%3Adev)\n[![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-brightgreen.svg)](https://github.com/stylus/stylus/pulse)\n[![npm version](https://img.shields.io/npm/v/stylus?color=brightgreen)](https://www.npmjs.com/package/stylus)\n[![npm](https://img.shields.io/npm/dm/stylus.svg)](https://www.npmjs.com/package/stylus)\n[![Join the community on github discussion](https://img.shields.io/badge/Join%20the%20community-on%20discussions-%23754ffb?logo=googlechat&logoColor=white)](https://github.com/stylus/stylus/discussions)\n\nStylus is a revolutionary new language, providing an efficient, dynamic, and expressive way to generate CSS. Supporting both an indented syntax and regular CSS style.\n\n## Sponsors\n\nYou can sponsor stylus ongoing development via [opencollective](https://opencollective.com/stylus/) or [paypal](https://www.paypal.com/paypalme/iChenLei/) !\n\n<table>\n  <tbody>\n    <tr>\n      <td width=\"50%\" align=\"center\">\n        <a href=\"https://automattic.com/\">\n          <img alt=\"automattic\" src=\"https://user-images.githubusercontent.com/14012511/*********-40ca295f-a0cf-45a6-b24a-303496552499.svg\" />\n        </a>\n      </td>\n      <td width=\"50%\" align=\"center\">\n        <a href=\"https://www.mojotech.com/\">\n          <img alt=\"mojotech\" src=\"https://user-images.githubusercontent.com/14012511/*********-0a090b42-89f8-4651-9506-b6aefac57c66.png\" />\n        </a>\n      </td>\n    </tr>\n    <tr>\n      <td width=\"50%\" align=\"center\">\n         <p style=\"font-size: 30px\">Your Logo</p>\n      </td>\n      <td width=\"50%\" align=\"center\">\n        <a href=\"https://www.paypal.com/paypalme/iChenLei/\">\n          <img alt=\"Paypal stylus\" src=\"https://www.paypalobjects.com/digitalassets/c/website/marketing/apac/C2/logos-buttons/optimize/Full_Online_Tray_RGB.png\" />\n        </a>\n      </td>\n    </tr>\n  </tbody>\n</table>\n\n### Backers\n[![Backers](https://opencollective.com/stylus/individuals.svg)](https://opencollective.com/stylus/)\n\n## Installation\n\n```bash\n$ npm install stylus -g\n```\n\n## Basic Usage\nWatch and compile a stylus file from command line with \n```bash\nstylus -w style.styl -o style.css\n```\nYou can also [try all stylus features on stylus-lang.com](http://stylus-lang.com/try.html), build something with stylus on [codepen](http://codepen.io) or [RunKit](https://npm.runkit.com/stylus)\n\n### Community modules\n\n  - https://github.com/stylus/stylus/wiki\n\n### Stylus cheatsheet\n\n  - [Stylus cheatsheet](https://devhints.io/stylus), very useful stylus syntax code snippet for you\n\n### Code of Conduct\n\nPlease note that this project is released with a [Contributor Code of Conduct](Code_of_Conduct.md). By participating in this project you agree to abide by its terms.\n\n### Contribution\n\nPlease read our [Contribution Guide](Contributing.md) before making any pull requests to the project. Included are directions for opening issues, workflows, and coding standards.\n\nThank you to all the people who already contributed to Stylus!\n\n<a href=\"https://github.com/stylus/stylus/graphs/contributors\"><img src=\"https://opencollective.com/stylus/contributors.svg?width=890\" /></a>\n\n## License \n\n[MIT](https://github.com/stylus/stylus/blob/dev/LICENSE)\n\nCopyright (c) 2010-present [TJ](https://github.com/tj) and [Stylus maintainers](https://github.com/orgs/stylus/people)\n", "readmeFilename": "Readme.md", "users": {"af": true, "fox": true, "dodo": true, "inca": true, "rhrn": true, "s4g6": true, "tg-z": true, "tztz": true, "ysk8": true, "aaron": true, "abass": true, "akiva": true, "alimd": true, "cygik": true, "imanu": true, "kewah": true, "linus": true, "m42am": true, "makay": true, "meryn": true, "octod": true, "ph3nx": true, "skrdv": true, "slang": true, "slurm": true, "stany": true, "adamlu": true, "bemace": true, "chaowi": true, "cypark": true, "dainov": true, "dlaume": true, "dubbya": true, "elisee": true, "leesei": true, "mattms": true, "mihaiv": true, "orion-": true, "papiro": true, "potnox": true, "tcrowe": true, "toogle": true, "yeming": true, "zolern": true, "akarpov": true, "awayken": true, "bigluck": true, "chengsu": true, "deedubs": true, "evkline": true, "glencfl": true, "itonyyo": true, "jaxcode": true, "jesus81": true, "kaashin": true, "kontrax": true, "lex_nel": true, "mobelis": true, "nohomey": true, "ruzz311": true, "sjonnet": true, "ssh0702": true, "xeoneux": true, "yanghcc": true, "alexkval": true, "danday74": true, "dimonfox": true, "dizlexik": true, "hexagon6": true, "jgoodall": true, "jotasies": true, "lenville": true, "mariod3w": true, "mhaidarh": true, "nodecode": true, "slang800": true, "snehlsen": true, "stuligan": true, "vilaboim": true, "vrtak-cz": true, "xgheaven": true, "ambdxtrch": true, "blitzprog": true, "cilindrox": true, "daviddias": true, "dbrockman": true, "dubielzyk": true, "fgribreau": true, "freebaser": true, "isenricho": true, "kulakowka": true, "langpavel": true, "mr-smiley": true, "schneider": true, "sjonnet19": true, "thunsaker": true, "tjfwalker": true, "21xhipster": true, "byossarian": true, "cfleschhut": true, "charmander": true, "coverslide": true, "delapouite": true, "double1000": true, "flip4bytes": true, "icflorescu": true, "jswartwood": true, "kubakubula": true, "monkeymonk": true, "quality520": true, "rocket0191": true, "seangenabe": true, "sharkiller": true, "codylindley": true, "flumpus-dev": true, "joannerpena": true, "louxiaojian": true, "m80126colin": true, "octetstream": true, "strathausen": true, "abhinavr2121": true, "brentlintner": true, "guidoschmidt": true, "matiasmarani": true, "nickeltobias": true, "pauljacobson": true, "paulkolesnyk": true, "runningtalus": true, "themiddleman": true, "wesleylhandy": true, "andrewbaisden": true, "ferchoriverar": true, "gillesruppert": true, "jordansrowles": true, "marcelohmdias": true, "pablo.tavarez": true, "renatobalbino": true, "chrisfrancis27": true, "joshuadavidson": true, "mike-feldmeier": true, "shanewholloway": true, "stephenhowells": true, "thebearingedge": true, "userzimmermann": true, "wagnermoschini": true, "animustechnology": true, "dopustimvladimir": true, "knight-of-design": true, "daniel-lewis-bsc-hons": true}}