{"name": "redux", "dist-tags": {"latest": "5.0.1", "next": "5.0.0-rc.1", "alpha": "5.0.0-alpha.6", "rc": "5.0.0-rc.1"}, "versions": {"0.0.1": {"name": "redux", "version": "0.0.1", "dependencies": {"ansi-color": "*"}, "bin": {"node-redux": "redux", "redux": "redux"}, "dist": {"shasum": "3b1f03d791f34ae91686275703084e95aa265332", "tarball": "https://registry.npmjs.org/redux/-/redux-0.0.1.tgz", "integrity": "sha512-452dMmmuXgf1m2GSjOZCBQ6llhsHSAB2hinFe8JXzB06bCWiI+XSL/yUA/V5mSXg2mgvhXkf9d/gvVk5li9uvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMkxtIcmhWa4rqQy6jX/AIlwxxQK97nDiQONMRbovE6wIhAKy45q9AQDN8LJj/f5aWEKuXNc59KcxEkxCo7ho4Za/W"}]}, "engines": {"node": " 0.4 || 0.5"}}, "0.0.2": {"name": "redux", "version": "0.0.2", "dependencies": {"ansi-color": "*", "commander": "*", "coffee-script": "*"}, "bin": {"node-redux": "redux.js", "redux": "redux.js"}, "dist": {"shasum": "f2f07e84f54949d42b1bcd45bbec7f4c6acae8c8", "tarball": "https://registry.npmjs.org/redux/-/redux-0.0.2.tgz", "integrity": "sha512-54P4ZFddC+vhp09a67A1CcCReCNE4OcLJJtUN6ckCAYN1yxjYiB/eBNJUA34IhOgtTjUu2Vfk3OfUy1gWOk4+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDI/xCwf9KwQI1Jl16LK779knjq1qF4hyKcER71vcrw8QIhAMsQx4XmgBNn2TLhDGwrbPhGdBZwXQ+O06bDa4kFzoF5"}]}, "engines": {"node": " 0.4 || 0.5"}}, "0.0.3": {"name": "redux", "version": "0.0.3", "dependencies": {"ansi-color": "*", "commander": "*", "coffee-script": "*"}, "bin": {"node-redux": "redux.js", "redux": "redux.js"}, "dist": {"shasum": "00cbe023f75edc854499a20f788e78c1ed14bce0", "tarball": "https://registry.npmjs.org/redux/-/redux-0.0.3.tgz", "integrity": "sha512-tJK6b7XmkT/3fCr8gkaFIqQpdZUplpvXG0sDCGEzPOKGZ1TXYrlIeFs8E1XYXeffsO4kebMCY52Uwi823fPo0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHTQLOQGxb6VbBd/UchqtRsUYjYhQTqYHPyVliAtSxANAiEA5FrVRjK8OrAz/eiYK4yXNF400A6wmbhDQ9w6gIBKaNA="}]}, "engines": {"node": " 0.4 || 0.5"}}, "0.0.4": {"name": "redux", "version": "0.0.4", "dependencies": {"ansi-color": "*", "commander": "*", "coffee-script": "*", "yaml": "*"}, "bin": {"node-redux": "redux.js", "redux": "redux.js"}, "dist": {"shasum": "1b92500a27528bb8f3c40e4d5900f8419ddb6bd4", "tarball": "https://registry.npmjs.org/redux/-/redux-0.0.4.tgz", "integrity": "sha512-/JTYda1ke9cRk6gwCWzmGDnTz8rn1D/OIBXpCh8BQelkn/VtCZ1t+4cmqWxDAuKF1q7QJpT0IGQCWPone9ZKsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF06JVzVxL3bjSoNhBm5/sMXTj0w4qN8zi4XTs3otup2AiBpYR2ETGoBlN3ohynRWZQSCg0fSgGldr619RmZgfJMrg=="}]}, "engines": {"node": " 0.4 || 0.5"}}, "0.1.0": {"name": "redux", "version": "0.1.0", "dependencies": {"react": "^0.13.0"}, "devDependencies": {"babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "3a7c821dee358abf21b81e1ed32e21b96452393d", "tarball": "https://registry.npmjs.org/redux/-/redux-0.1.0.tgz", "integrity": "sha512-C+Tf4Q5slPEanm2gUOth53kQFsD7tSVWcwcFSSRsuu57ttZf42otnJDW3boUJ4GR6I42puxnl0eEx28AgWYi/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqIitqFcEj52kiFWVmjxhRdNdDsg5SO19i5E35SZXeFAiEAju162VKvIUyPTTclMfkKisyWHWhGGz3N5HULlzWoKqU="}]}}, "0.2.0": {"name": "redux", "version": "0.2.0", "dependencies": {"babel-runtime": "^5.4.7", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "e694c3a4bcf0a70484b86bdec1d99d1bdfdaa2a7", "tarball": "https://registry.npmjs.org/redux/-/redux-0.2.0.tgz", "integrity": "sha512-SwcfK+rkNWBrMjJObYwktq2ZpTRdpHfiX0e66+2x0Zyww/nSB+WMnB9oSewwZJ6+JIL0lNx3qID/d4wxPt+EUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCG1m2cfG3GZe3lxR/An1FhGhGahn3bbsndXxLI3o/o/wIhAJijXBgG6wT0JZoWKrX5QahKIKmabQRikN6jcOJ01QER"}]}}, "0.2.1": {"name": "redux", "version": "0.2.1", "dependencies": {"babel-runtime": "^5.4.7", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "3c391181d183991e4e87983d81adbc437ea8bfd7", "tarball": "https://registry.npmjs.org/redux/-/redux-0.2.1.tgz", "integrity": "sha512-9mCuBgycjzTG52NPeSb0HsJUy8hiIV32PUZR1n2ROa6f5YrlLLuWAw7r1zJ79SYcZ4iwMZ94wxygt3hPa74J2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4lLGvVCfWKgNGfyXnRxl/yLLj49xMBC+236FF4gRzCQIgDh8NVC1Gc4CdHRtNYVslQONJJ8LI7iRHJSnk5q38ikM="}]}}, "0.2.2": {"name": "redux", "version": "0.2.2", "dependencies": {"babel-runtime": "^5.4.7", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "1538bd475f50b906482d8cdf4721e08834c5c060", "tarball": "https://registry.npmjs.org/redux/-/redux-0.2.2.tgz", "integrity": "sha512-5SnnIBiLEiavrhmaK+M6rH8p1vYkBs7JqpWD/7yiXvsvI8qZRggZvQCL1Lv2M/I6fPqjzvq0KDaxbyJLjjGvOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWYNTSltv2x8f2On6d695gDDFZ/mWMYOK9pxnJL/sHRwIgQw7b2hRLnA5UxLXfb+Ci0KGNwuN9rMdmyvTghuQKNQ8="}]}}, "0.3.0": {"name": "redux", "version": "0.3.0", "dependencies": {"babel-runtime": "^5.4.7", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "cf3bdb7caf73143001b7740bd6f791532c3cd3d9", "tarball": "https://registry.npmjs.org/redux/-/redux-0.3.0.tgz", "integrity": "sha512-FNeNBLDMkEzOyn3AxxULKXDFJR80nfQVVYmu5eZcMOxjdcxPOKDjNdYxduAzMxCp2ChYMI44r9aw9VuGJMmTHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC/DG50kKgEMUxmPtIBb30j07PYO347S0TrG8kePwzfBAiEAtIYVp48lu5OJ7dV690opy/PvsF405xKlFYGzNWmrwQ0="}]}}, "0.3.1": {"name": "redux", "version": "0.3.1", "dependencies": {"babel-runtime": "^5.4.7", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "26f42c9e2514138b22135aaecc2d85cfc949712e", "tarball": "https://registry.npmjs.org/redux/-/redux-0.3.1.tgz", "integrity": "sha512-6UJ7pcx1WLynufA32WId9sULxVpODlkxBWX8RUdK6Ja5B755eCaTcAUKSkVY1VaTl+wXxaXDuBxt9oYAzrfwpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqKIt+X5HmIrbRey9JE7vZ/gpoepQAJXYVcnwBs2/OUgIgdQI0y1No8kSxC51bz7pKz2qavkdJWjTMQgNk6puZpVI="}]}}, "0.4.0": {"name": "redux", "version": "0.4.0", "dependencies": {"babel-runtime": "^5.4.7", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "2f8b08793595f21b02cdee57dd58f7006e38c586", "tarball": "https://registry.npmjs.org/redux/-/redux-0.4.0.tgz", "integrity": "sha512-X3NwgaWR2BRhCdji0B9ikugIJHxJv7TegjidiEIZKZSt4k2unOXlY5LDLPv7CMKr+eYeGAjyXO/R2Pp+5hE+Ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHf0DQtnuXP9839jf1PDbZUt/iUjI2YMbWpatywz2cUuAiB+yoTV8fM/q8M+vSn1uOhGU3XLHpjXu7/i30617Z1CEA=="}]}}, "0.5.0": {"name": "redux", "version": "0.5.0", "dependencies": {"babel-runtime": "^5.4.7", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "9ce445ec4d88c95dbfd06ae25193d560f923d255", "tarball": "https://registry.npmjs.org/redux/-/redux-0.5.0.tgz", "integrity": "sha512-H8Gbw61bI3fwSIak4cdO7oEvCmfSzK3eTjjHLtD4OKbAZzdcKW9Bp5cKXxayuiLtQOAF76zlk3rhbJAxONZCyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE8qb4OT2Ix/fcHQVFgWrAxETIBXzQ+BUUCmONH1LX9YAiAkxuSreCivM6tsl4cRw5cnyuFf0s+MoC9SDAbFl8v+7Q=="}]}}, "0.5.1": {"name": "redux", "version": "0.5.1", "dependencies": {"babel-runtime": "^5.4.7", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.9", "babel-loader": "^5.1.2", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "f88c039797152017c6c2adb26e2db5bcac01b40d", "tarball": "https://registry.npmjs.org/redux/-/redux-0.5.1.tgz", "integrity": "sha512-6TxGHKJEO6KYw7dm9gb+ycsHIMPxTQ5xTamZ0MX/BdbuiD/T/QY6BmzLitXbpA7ujbor+CM5TvZjcXn86qU8HQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFuTq2/80LrdRED8QaAQkVyAMH7vsy6R9ExXGxRpDTSdAiBpA6RSHKm6u+KXmvlEFxDkcvmBckf7WQ7cSH6Sdh8L9w=="}]}}, "0.6.0": {"name": "redux", "version": "0.6.0", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "d7c85a57359b7fbfd0f9e678d1170ba6514d9972", "tarball": "https://registry.npmjs.org/redux/-/redux-0.6.0.tgz", "integrity": "sha512-Tcr4bY5Ufhd9NMsTev6ZkZj8mfqlvIcDRR6/nfqWLfZFTCNpU+h5YFlK+gvawPvicbOBsxBq4CbTz+/v1cs3nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOQQ7NS6/45mCULaCvSg/avenfeCmtVUzgJ+unpCwJgAiBJFgbJSL+/ZEKQnBHEQ1vvYsmnR0SrhUTXjrQg/AKbAw=="}]}}, "0.6.1": {"name": "redux", "version": "0.6.1", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "a33a74660bdef1cb16225b7d33979083f393faac", "tarball": "https://registry.npmjs.org/redux/-/redux-0.6.1.tgz", "integrity": "sha512-J4BBcXJzGAhhngkrl4TkG5Cl/wl5Ckycfd7eSyZ0IBMTVUiTgBowdZCyNYBnkPb1is1JWsgvf30fYoi4CCPZgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhkWPzmWO7NAQ2ZhsiV8k3i0C68F4Aclny2WK50JkRtAiB7cVQ4kXV8CQqwbjXmQdxMa9qih2Ev1QKyCY+k91lmhA=="}]}}, "0.6.2": {"name": "redux", "version": "0.6.2", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "f94bf4cbe06af625c67a0b6351ef042c6503b0ef", "tarball": "https://registry.npmjs.org/redux/-/redux-0.6.2.tgz", "integrity": "sha512-u0VyeaRX0mZroF2gjD9ghSvhyf/RHhug4ZoNGT3WiCF+/b9vJOolBHzX/Y/0IJ24/0HLdjEL5H5jHvoWzVhSJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAW0TMCm4FalZApHY1zUXTw8dpBDgh9FHPqDIL7T5UGNAiEAyDmHHcx6znShTgEyWd6YZzAe+IKQglH9PqUgmPizpf4="}]}}, "0.7.0": {"name": "redux", "version": "0.7.0", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "9bbd2d496aeaf2a208c50a26d995bb44a7de1254", "tarball": "https://registry.npmjs.org/redux/-/redux-0.7.0.tgz", "integrity": "sha512-FVo/A1sde9p5whztMVitdw6hd4bjrAdPqKyS9jQDvTtMpH5R1M5W8jxqG6Jsu5C/vea7pjibwThhVTPJkU9aGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTq6gcQ2cKiAKIHvZ+OIO5IcaVRvu2LFALZB4hVKYzXAiEA7haCDedtJ2jF9njSnPu1zrX6RG+kvXMLXus+TuuRAcg="}]}}, "0.8.0": {"name": "redux", "version": "0.8.0", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "17aebbaa5aa7353707f34d0410a9da6de0c5ca94", "tarball": "https://registry.npmjs.org/redux/-/redux-0.8.0.tgz", "integrity": "sha512-l0J1qcYU+mDv+GIWxfyrx3yIxlorLvmBOYwVvtXy1CsvIN+7ApStyTZrYlCHJdPch7HdT8h1m2+dzERzs6Z17A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEX8kp3RG4OviA7wdejBUgkWD6RCCl5kbS8vgiICF4B3AiEA87+nmkIhMmm8hfCxCot6QagMc1Q6OJ0e4Tl9DkiUbjg="}]}}, "0.8.1": {"name": "redux", "version": "0.8.1", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "a3776528509ef8e23b392ea5ade563544e2cecc1", "tarball": "https://registry.npmjs.org/redux/-/redux-0.8.1.tgz", "integrity": "sha512-bzccuRoI7x/EW+/xzcKciVjuoPMrHrabaCZd4zE1l3/z+/EP3KeKotEhD/1Vua99noTNL0rjpOX3r/sxsncJfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFs5+L4RKDw4QlDIE+nC88rU4afukRNfUzuddp5Ig8AoAiEA9nOpLvr8DUnmOqsNUj+wZf+8vX8Qei0XHmXGIE9vGgs="}]}}, "0.9.0": {"name": "redux", "version": "0.9.0", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3", "react": "^0.13.0"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "4736ce56153595a953a6dd9652fdc5bfa1e1f4e8", "tarball": "https://registry.npmjs.org/redux/-/redux-0.9.0.tgz", "integrity": "sha512-tBRnR3uQ9OZUxzhdR6yRUxyovLzuQmBMjvSXSHBlkwjq30RZMVRvSyuPpuFp9KUE8iYt4ZjqJ4JiwL4hP1WdPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2CEYaQaGBUQaoJEdbtzrSWcCwADwyv2VdRblOjAvxjAiEA6dax7yUh6l7vqWXeORwj6ASQcYnJAylqWg4w1s6FF+w="}]}}, "0.10.0": {"name": "redux", "version": "0.10.0", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "istanbul": "^0.3.15", "jsdom": "~5.4.3", "mocha": "^2.2.5", "mocha-jsdom": "~0.4.0", "react": "^0.13.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "98722a4e56b826f92580fd18149680cea3050810", "tarball": "https://registry.npmjs.org/redux/-/redux-0.10.0.tgz", "integrity": "sha512-bvY7pslncz88r+YYkrawKoN9pyfqvzNos1LHfhoV0Wjrdy/znaI6aVoCTF2LEby1YNjXvf6h/Gi0v0YYAYJa7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRYx2IJYu2xgDCNHLXGQx0PEbRxN+kuf0zwUA+NMGUhQIgMAQH7u6MTEjiPYGt+7HpA+60HpDH0aJ+MR32RSvWyFk="}]}, "deprecated": "Please upgrate to 0.10.1 to fix redux/react entry point being in ES6 instead of ES5."}, "0.10.1": {"name": "redux", "version": "0.10.1", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "istanbul": "^0.3.15", "jsdom": "~5.4.3", "mocha": "^2.2.5", "mocha-jsdom": "~0.4.0", "react": "^0.13.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "78a0fcc41878020d61ff242c38475fa75b41a479", "tarball": "https://registry.npmjs.org/redux/-/redux-0.10.1.tgz", "integrity": "sha512-YQ<PERSON>upjhH6xY0CkIHn1qujvXuozXKkR9zVDAEnCSAzTmKOQb8Szyz0GY2W8aG23o4McWKKBC1jjVtssuxPm5TRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGnLUEqCs5+kXrQHklXboamiiUQRkLLeLeuxV/DOMKi0AiEA50c7x+45C5orZyqPZO94crccHTEErFj1ij6nzxcMsGM="}]}}, "0.11.0": {"name": "redux", "version": "0.11.0", "dependencies": {"babel-runtime": "^5.4.7", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3"}, "devDependencies": {"babel": "^5.4.7", "babel-core": "^5.4.7", "babel-eslint": "^3.1.11", "babel-loader": "^5.1.2", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "istanbul": "^0.3.15", "jsdom": "~5.4.3", "mocha": "^2.2.5", "mocha-jsdom": "~0.4.0", "react": "^0.13.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "c9bf976137faf3a9acb65c6fee22ff328bd920e5", "tarball": "https://registry.npmjs.org/redux/-/redux-0.11.0.tgz", "integrity": "sha512-fMRvCOp1DdUCHfkU+FizZ0DxxFAGBsaqdw2iD5usjTadFCvcg7oHiR4YWgylPxVkQF0B02vlr/8L59+pMVWwyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDezRYoS3gtu5U89MSsMgjK78lITZcoX3pbAyseIvfC4QIgXgEPzO9vhLX12UpABk00MozNHG/lpGJmoTsYFrmn7jA="}]}}, "0.11.1": {"name": "redux", "version": "0.11.1", "dependencies": {"babel-runtime": "^5.5.8", "envify": "^3.4.0", "invariant": "^2.0.0", "lodash": "^3.9.3"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.5.8", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "eslint": "^0.22.1", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "istanbul": "^0.3.15", "jsdom": "~5.4.3", "mocha": "^2.2.5", "mocha-jsdom": "~0.4.0", "react": "^0.13.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "7682540320c61a31e7ea709a8ec3da4cafdd3e26", "tarball": "https://registry.npmjs.org/redux/-/redux-0.11.1.tgz", "integrity": "sha512-FM4ykLjrB88jlcRIXS1RAKSJRw9WEQ1gsU9XFKugRgl+pko2Y3AwMsu2uOW0QDxoAXC3eQiJZ6BOItTvSEgoBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDV3+pGJynQoL1z3u6HDSuefZocI2cAEhExrd7hP56YhgIhAK83n9KOQpzJxCAPr6N1ScGPjbt315GHVJ2EUkQq4N15"}]}}, "0.12.0": {"name": "redux", "version": "0.12.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.5.8", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "eslint": "^0.23", "eslint-config-airbnb": "0.0.6", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "istanbul": "^0.3.15", "jsdom": "~5.4.3", "mocha": "^2.2.5", "mocha-jsdom": "~0.4.0", "react": "^0.13.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "0099b2f92d1136c5286b976771c5d018ec5971a5", "tarball": "https://registry.npmjs.org/redux/-/redux-0.12.0.tgz", "integrity": "sha512-kI/Jgz5zgM7AiWI4ZaIIaUGUPVBaa/in01ax+BGUccxy8SFYhRFLajFAOrcwckq7gYToSKWbKtOf833rgL35og==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVS8zN13TI9HWvgZyzZCF0VgK8+Ik9EABpFMn6XFeJEwIhAIQqpXDzBordQ2KEe/qfzMSj4+ZVQcU0T+K841tuPKtf"}]}}, "1.0.0-alpha": {"name": "redux", "version": "1.0.0-alpha", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.5.8", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "eslint": "^0.23", "eslint-config-airbnb": "0.0.6", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "istanbul": "^0.3.15", "jsdom": "~5.4.3", "mocha": "^2.2.5", "mocha-jsdom": "~0.4.0", "react": "^0.13.0", "react-hot-loader": "^1.2.7", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "6f6de5defbe886f7117eb1656b1704518d39de97", "tarball": "https://registry.npmjs.org/redux/-/redux-1.0.0-alpha.tgz", "integrity": "sha512-UyCt7pJMcIzGTHLx5oUB0vyO9B43f/XRjmzRz8xgEyCuk2ohosfXS2+mBQd/X9DYH6qYa2tVEuAO5A0fRPHKkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXXdytA2h5sI0dk8zZf3rVchmxa/zXfoLpcm2wyvKaVwIhAJv9rYlpev9tyRgur4to5STl4oekFvvsmV1V3a7oZMLV"}]}}, "1.0.0-rc": {"name": "redux", "version": "1.0.0-rc", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "eslint": "^0.23", "eslint-config-airbnb": "0.0.6", "eslint-plugin-react": "^2.3.0", "expect": "^1.6.0", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "f3542be9406e963a460385deb3a1fcea008839b3", "tarball": "https://registry.npmjs.org/redux/-/redux-1.0.0-rc.tgz", "integrity": "sha512-XzhkJEn0L8wpvFrpzxLjf0ungG0sCWvkmtTISIZP/iDlJ5dRh5MRwZl+BdGxshQsHdy3Wz10p7DWhC1HWxv5pw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC22Yt8P/GZ+RaVaxaZijKiNEdc1pl5G8OZaH43DA58UQIgPiEBqFLRWQ8GmXtCTVhyOxKstRTd9TKxRVKw9wMoPNI="}]}}, "1.0.0": {"name": "redux", "version": "1.0.0", "dependencies": {"invariant": "^2.0.0", "warning": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "contextify": "^0.1.14", "eslint": "^0.23", "eslint-config-airbnb": "0.0.6", "eslint-plugin-react": "^2.3.0", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "c22fac40c347c40d7961869f6b28849d9df758e6", "tarball": "https://registry.npmjs.org/redux/-/redux-1.0.0.tgz", "integrity": "sha512-yYWqLtUJ2EVHAfpz96skVbiC0TySyN8tSNWDdF6YsrCLlcLpIZdcqZ5sVTwEbvRRRK4FMHv1tGTY1fDRYiH61g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYuAY2ETlOLz39QA77WuB3EFuBOlVIfd6+o6tiQADi3gIhAPk4JgaRI+S+49DImZCOO0ZrXD1GWxvL60FD7NVioQHy"}]}}, "1.0.1": {"name": "redux", "version": "1.0.1", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "contextify": "^0.1.14", "eslint": "^0.23", "eslint-config-airbnb": "0.0.6", "eslint-plugin-react": "^2.3.0", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "23944a23d6b9c715f826844331f3763fc7dccd3d", "tarball": "https://registry.npmjs.org/redux/-/redux-1.0.1.tgz", "integrity": "sha512-dvdZfLU3zwfn/Z5lgrbqAj9OznFaob5LFDj2suxsgPQNbusr4DNUwUfizR7vZH1gUVtiPufMf7BUxE9e5kjRrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHKSIiisgJbxIo9y/dcT0qbX8GuRi5uRn4BHDzBfKwtDAiB64Z2/VYn3NmS7OlhZevpVYj0/JIOfieUSpwMm1c/mrA=="}]}}, "2.0.0": {"name": "redux", "version": "2.0.0", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "contextify": "^0.1.14", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "^3.2.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "38923965e539496d5466f226f0a58c2238ec2a62", "tarball": "https://registry.npmjs.org/redux/-/redux-2.0.0.tgz", "integrity": "sha512-7RVtyNKgfuj80uPVmb5QUM1h9S5Z7h7g1diquDUd2phoFYCc8zdCE2wr0wNxUjgApDzmHk847+Th0ys7K5jiag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKjI9+Ecs/FCUtixDisYaSEHJKrXK+0FeYtTIhEEODQQIgNACm1jI66vPE6PGWlrGQwxLXOPQoSSw1nbd1srE6zz8="}]}}, "3.0.0": {"name": "redux", "version": "3.0.0", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "^3.2.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "gitbook-plugin-prism": "^0.1.1", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "dist": {"shasum": "8dabe144f9df23cfdfe4c3309ce6f4a3acf71a04", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.0.tgz", "integrity": "sha512-Mu733UiwHiJBItpqcD8iVbQ/YB8c2C7nVtbMmQlf+gu7jfuKm3sZWeUKalCWAkQL0ewcqt9cF0jw0zG98RjAgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDIua3I0ZK8o7ShJERvL27eAH6qMtB3+IaCRqqmy7IBaAiEArNkXx7RiQ4ZWojSIGlAXUUfsVAgmlAosic16DqRWKr8="}]}}, "3.0.1": {"name": "redux", "version": "3.0.1", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "^3.2.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "gitbook-plugin-prism": "^0.1.1", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "445a502f8262406a7d99aab57c542865cad542c9", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.1.tgz", "integrity": "sha512-HBUokOefooMPVvFAbYjRrV7t6J9tAhca7YN9/PPUSQHdu8U+Gp+6b/hYyPVM+JEtsPrmMNE/hCkhxByYuExgmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnYPow1YR1iTCeeU0+SCS7ZNw/j68+IgAkftjm9I2IxAIgXjm02hWUdF+RzfDavfkG3bvmoMFCRsjNexzuWTZzy3o="}]}}, "3.0.2": {"name": "redux", "version": "3.0.2", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "^3.2.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "gitbook-plugin-prism": "^0.1.1", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "88295e5a4f123ee1b153b3588c0b84f115164459", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.2.tgz", "integrity": "sha512-VJsRTIffvxCq5i6AlvXTBgeP3/5X80zWrZz5KED9rNh3V3kEFDJ/i7+2wIXJn+Au+6lrLTzm1Z3HfIrjIEfEFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfUpT0Cv+YtGsD/joyNYKq1fCe4/0FHsbNcK6lQUr3LAiEA9BGv8T26S1DQd8Bf9Sr8328qJJDd1qaZRP9aGvg/i+o="}]}}, "3.0.3": {"name": "redux", "version": "3.0.3", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "~3.5.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "cf60cc323ca00fcd15fe76197232df3dc32f568f", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.3.tgz", "integrity": "sha512-vp0BnEAsBdUd1FCsHD6deq2MnRuIbHViQg4HWA8wZusIhMsQ1OfofHY3AUZwWQpxNOApyvaFJ5iJSTpIwWbuGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGJ+MpxQ45BODluyToZ9f6AoBhDil7yWo9P6is9F4Xa2AiBpppryzuMLqHNehkzUzToWeNOPzZ1li7NMFiZdCH0F8w=="}]}}, "3.0.4": {"name": "redux", "version": "3.0.4", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "~3.5.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "73019374f7a324765e4e33f367c2730a785a3305", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.4.tgz", "integrity": "sha512-0UYdmKZFGTG1JiFF9clfJ1St051i+5YwFbojybCVEeszzs9/6W1JWpv17Uh0yCAcVrAx/rt7/e6eLgAN8LMpqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEIEidzQWAYQKWcjMwRiDplhKeVFh+aX8+Knhfp8MWcAAiEAnx7HnT/FU0bRbl5DIGCNk9ua+c7cGu3OU3oTf/k5FdQ="}]}}, "3.0.5": {"name": "redux", "version": "3.0.5", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.7.1", "eslint-config-rackt": "1.0.0", "eslint-plugin-react": "^3.6.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "f3f23f780b98c8dd7f84b9187ab5f86fe90199b8", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.5.tgz", "integrity": "sha512-YQMjqzdn8smWmX62G6q24mXXXJgDoawatjmjIJFzfmIiAVu4bX32TjJfEu8tV0p/3xtaNZuu4jAVfiHubkvI0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHW/msIUiJ95kKYQKM6Xk6ZQToIVgK8HLLNfHSQUiytAiBAz02pE+tggGtaT5awS4LSa4MAaKT0pf6qxVTsN8AF9g=="}]}}, "3.0.6": {"name": "redux", "version": "3.0.6", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.7.1", "eslint-config-rackt": "1.0.0", "eslint-plugin-react": "^3.6.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "f02db603a8996776f68ef38f571c25705c9ba325", "tarball": "https://registry.npmjs.org/redux/-/redux-3.0.6.tgz", "integrity": "sha512-UT9WHa5F6+1nEBDu+rmiKf53EIBYhXXmZ+8gFd51ypPJi1Qi4fysuWSZaC+s4apxQaBLEW0zHvcpcwE+3WnIYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD67q3UPZJfv/esqJUZNbNnUs+0I8jB9C5v2iiGCnT03AIhAMFijv7r+Z+OJxJuxPDJjTHqJPrGBbOln5OCgeJmAd2C"}]}, "deprecated": "Versions before 3.1.3 are known to crash on IE in some cases. Please update to 3.1.3 or newer."}, "3.1.0": {"name": "redux", "version": "3.1.0", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.7.1", "eslint-config-rackt": "1.0.0", "eslint-plugin-react": "^3.6.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "9073ca4b7e32381d8bd77a66a28b3db7ef8a9ed0", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.0.tgz", "integrity": "sha512-yN3/pE73dDJ9plksY9x+ahKHsvjV5K780F+5AnfM8uf3YjfxDdmyt2GvfrUieuH3LSFt3OMxBQFsqMCkAC2qNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDho6XmZs8LVBeBv6sDtkcxuJ4doo2Tu4zRSeXtHbLUkAIhAIsXFSb1+QVWssBNHP9HRpPsrzAHnlfrRU9TSg174rf2"}]}, "deprecated": "Versions before 3.1.3 are known to crash on IE in some cases. Please update to 3.1.3 or newer."}, "3.1.1": {"name": "redux", "version": "3.1.1", "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.7.1", "eslint-config-rackt": "1.0.0", "eslint-plugin-react": "^3.6.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "e50b70ec3670f24a6ecae1d660d311835bf2ecc9", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.1.tgz", "integrity": "sha512-P/WKGY/amBCwZUT4Ly2mrk2eKHedXR9yC0zAaWHzkG9QHKBFy/LPaiErPE2rvcnC+Q4GowMpPamp/KWbBH+UsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBn9Qj3+p1ZhaYqh8fVLrCtWvDaWhelYRNehTXgdf1PrAiA5tN5jJBb9xDe8NyUHx3dOY05mtzuBDC+o0/PdLWAm5g=="}]}, "deprecated": "Versions before 3.1.3 are known to crash on IE in some cases. Please update to 3.1.3 or newer."}, "3.1.2": {"name": "redux", "version": "3.1.2", "dependencies": {"loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.7.1", "eslint-config-rackt": "1.0.0", "eslint-plugin-react": "^3.6.3", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "0af2bdbd343d8b64e9f3900491f6ece5c75431a7", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.2.tgz", "integrity": "sha512-JPxIoJN7E6QgWtm3gbLw8OAgnuUS14ZddfDTTQs8T3g1oj7zhJlF1QMuAcdFjUm1BFbmqvZj3EpjQiUuIjUGNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCu6VLRqT6PyDuUwGfVEiSvTo/CEZddeoCCxU3Ll3qECAIgHSY55fOilBq0BrxrLqCGT2aKjDYBsMku44sikUeLlmg="}]}, "deprecated": "Versions before 3.1.3 are known to crash on IE in some cases. Please update to 3.1.3 or newer."}, "3.1.3": {"name": "redux", "version": "3.1.3", "dependencies": {"loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "df84c60d7ae63108cffffaacb7a7a17e56c0c739", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.3.tgz", "integrity": "sha512-rSnLRGu/KzAQ5TUFNoPOSgpx53eOjoU7chVLBaZJGzvpMQrDHPGgDb6rrxsB/7nVpb/09SDxuoPv4uYC0w+nsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDIczgLXTOIzJ9crtXUDvRtfpKNq+1Fwo5BUwGHjA/DAiEA0aLX+0PNZnj2RU9+ONEAb/qQQPDhpiucf0t3NFuSiGA="}]}}, "3.1.4": {"name": "redux", "version": "3.1.4", "dependencies": {"loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "acfbdfdab6c6d221fc19c6ce9262f6aa314c7f09", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.4.tgz", "integrity": "sha512-UY5hS2WyOtYm3jw0rDfIeqv7AgHoxxoszXG0GC9R8qnLcr+veQ0dFMWVN+Gls3sxdag54L8aaTjJcv18ZJNMpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFz1IM9VL3KQJgFUfKmj9Woz0aIr+/jxHLXyxb9dV0UBAiEAmbqdHQpBQyNEdW5KBDhZ1We8WDztW+wgc7SlTGUGJ6o="}]}}, "3.1.5": {"name": "redux", "version": "3.1.5", "dependencies": {"loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "6fcbcd7e6b881f24ee9b8df975007b174f827d7c", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.5.tgz", "integrity": "sha512-oU7kKF5UBxyK20LSbBhwDatHdhxGgX5gM64u21m0PzIAnOnqa3hxZIJCuhyf0wo0uEo1HnkX9CZy3AHlKAqxNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgm4+ns13Jjk0J1nqjSqlK6E41YL3/WkBYP36IEcfWEQIgLe4k7XNwC+28s0Zq9CweRp5eyTIwfkqnfCr+wsEK+/k="}]}}, "3.1.6": {"name": "redux", "version": "3.1.6", "dependencies": {"loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "ff5f207c55d6a7d5680fb26c4c451079a8215b94", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.6.tgz", "integrity": "sha512-CZMqP/qzTHQwWBXU7STvMbdxwfLgHsJINFkyjzW7wXPmUhDqYYgrrKQ5S08fIxRp0HxDaN74ibPkOfz4+5tvqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBeZUbBxRHCv4q+W6p2KsvYiNXNP+kiGstccG1av/9t4AiEA7ji8vL97Cdlux+heN7RFj40DuzoJeD1HzAlRVQsu/ok="}]}}, "3.1.7": {"name": "redux", "version": "3.1.7", "dependencies": {"loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "cross-env": "^1.0.7", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "5ed77f877682a7c488e30199a9c72dbff7862848", "tarball": "https://registry.npmjs.org/redux/-/redux-3.1.7.tgz", "integrity": "sha512-3gAWPWBb6NR+xMm5QLFVgmHVXG6NjdhmgISAOSp4eA9WA1cxpXVG7gP+QWzvXmfi4+Yo3Y//81+19sqmZWo08g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKbRLuK576Y2B7ArqxoGcPQOTU/sS0UQFYnFulMwOwJgIgJDr9DMK7YLSbJ5Js1d9SMc/qnh/bKuClhs4TBTFI21Y="}]}}, "3.2.0": {"name": "redux", "version": "3.2.0", "dependencies": {"lodash": "^4.1.0", "loose-envify": "^1.1.0"}, "devDependencies": {"babel": "^5.5.8", "babel-core": "^5.6.18", "babel-eslint": "^4.1.0", "babel-loader": "^5.1.4", "cross-env": "^1.0.7", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "isparta": "^3.0.3", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "74b8a93fc4a9ab55769676b12d426d155dbfb122", "tarball": "https://registry.npmjs.org/redux/-/redux-3.2.0.tgz", "integrity": "sha512-N2YsIB+3cRb5jr7PwfUffbaE03XwWyU4vcPb8athQvSkyexYt8KSc6qkwitVYad7M8fqFedtjsdaSp4z7DYEGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDb8105ZdZyC6wQlqdq674o5F0FwSDjbFed5UsBCwAYMgIhAI1sPMZKsHYy4vlleltMEkRuuOnR81ZPLRUvLYfD1RmV"}]}}, "3.2.1": {"name": "redux", "version": "3.2.1", "dependencies": {"lodash": "^4.2.0", "loose-envify": "^1.1.0"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "33f2fadb7f78d3483da6b95660b411ca637a1b76", "tarball": "https://registry.npmjs.org/redux/-/redux-3.2.1.tgz", "integrity": "sha512-+lpx6BMTMMFFD683e6LYDWFACFlwIuZ0Ozf1+NMoYG3VmLVLhjS0joGXVmD7+RaYkGpokGEj5+Xj9WSanqqobA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxTN58qeqZyP/AmomaGULyiuOFVgEy7sGcvApg9uqsTgIhAKC7W90vvU9iDBymh3Fg5JpX/MErD2/DrgnoSCxHhxoM"}]}}, "3.3.0": {"name": "redux", "version": "3.3.0", "dependencies": {"lodash": "^4.2.0", "loose-envify": "^1.1.0"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "76ac202913546f0b6baa1c9b402d602e27b8eaed", "tarball": "https://registry.npmjs.org/redux/-/redux-3.3.0.tgz", "integrity": "sha512-pH3j7B0iqcnrBuxJFjJCJ04cRScpVnfZcC1bHmrZfuth3pIRKN58lOtIbHOzg/+f4acPw3h0uauGee984wETdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDayes5JUIoevndVaXcBv286PCOJdd8JjE68ixlrCwJqgIhAJ5rURrL4vxjEimt1G/DygDKboY7RJ5equ+DWIzsdGns"}]}}, "3.3.1": {"name": "redux", "version": "3.3.1", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "webpack": "^1.9.6"}, "dist": {"shasum": "716df8004786deaf01c93ae396c84fc1041e424b", "tarball": "https://registry.npmjs.org/redux/-/redux-3.3.1.tgz", "integrity": "sha512-YtxNXCJleQGYpukH5uLtfVt1MMzA9RYyCqDli40Jczf1NcuuRG/0h/br1++zDVEMFzFsc4j6ZG8kGBwt0Kt/FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID7IJhj+tLpbQhgN0Z2e2UFNMVn4/ZqeLAKD0G40xP33AiASIKRpkyNLWSWU0a8MY3eyJiojUa8k36jDRi9f6csUpw=="}]}}, "3.4.0": {"name": "redux", "version": "3.4.0", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4", "webpack": "^1.9.6"}, "dist": {"shasum": "2f7dcfa026f35dc039bfe939e77e8cfa7dd35e17", "tarball": "https://registry.npmjs.org/redux/-/redux-3.4.0.tgz", "integrity": "sha512-9luPXIZ5t/e5xjXphuDa5/DGf0si0pwth8rurXNCrPygOSQfejJCwI2JfzR93t+BMgkibAuBK7swFlzWIzQY8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBEDkHuX7io9rx0Ir4eyGjB3w9oBEsmCYhZ3/f6loirPAiEAq43vucwV9UuiuSwqt0cC0vbHcZCd1tOl2t2+P7YDJys="}]}}, "3.5.0": {"name": "redux", "version": "3.5.0", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^0.2.1"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4", "webpack": "^1.9.6"}, "dist": {"shasum": "8b440a2eb44982e13e32a8e3974dad5ac7b8ef71", "tarball": "https://registry.npmjs.org/redux/-/redux-3.5.0.tgz", "integrity": "sha512-yDY/ZUOzLh9K1kirVdEmyV/Cp0zVZ/nPilw0PF/6DmkyP9C2fC+Z+8uqPPb2rqo8WotBBXeaT6nigF7pmyzcKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqbT47BzagqybJVjMvZ6w+USGkbUWM3y1K7M9tZiex6AiEAyQ8mlW5pJkFpgix8eSVTWY4vBP4iwXxED9u9sh1vzU0="}]}}, "3.5.1": {"name": "redux", "version": "3.5.1", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^0.2.1"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4", "webpack": "^1.9.6"}, "dist": {"shasum": "90e543c042fbfc1370101d72b0ef94a45c98aa88", "tarball": "https://registry.npmjs.org/redux/-/redux-3.5.1.tgz", "integrity": "sha512-2sABhtsvtp9uIvdaPqiSZ+hTWhqXXIVt3gB6HK/AIMwsiIlV02qZHF1i3sK2H+bhlNdxdhvxytTYicsXp4xluA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcB8IQMzpehUlQPw3a0MfNHYf9aReleNOUwQrQiYFwNQIgZT9Vc38jQMlfKWpM6OlaQJLbPEPJIieYtdLk+yIWGXY="}]}}, "3.5.2": {"name": "redux", "version": "3.5.2", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^0.2.3"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^1.0.7", "es3ify": "^0.2.0", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^0.3.4", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4", "webpack": "^1.9.6"}, "dist": {"shasum": "4533745e970b647ec26066a83aa30e9e26faf843", "tarball": "https://registry.npmjs.org/redux/-/redux-3.5.2.tgz", "integrity": "sha512-44G4d/bVSuQb1T1b9hb+Xwe1kWGP0lyQUKhzdEymNGU6LxRPASac0XaY0pv/71MPzGfzs7i1QKz0him5zk7C2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8Mm4puZYO4JHwpF+U9v0+eNiEtt/qwUpxqocUEHp6zAIgIqxOvnPpeNdaTcLnqGDnCHwQrSdqKxa3xHZvGk8j7Fs="}]}}, "3.6.0": {"name": "redux", "version": "3.6.0", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.2"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "check-es3-syntax-cli": "^0.1.1", "cross-env": "^1.0.7", "eslint": "^1.10.3", "eslint-config-rackt": "^1.1.1", "eslint-plugin-react": "^3.16.1", "expect": "^1.8.0", "gitbook-cli": "^2.3.0", "glob": "^6.0.4", "isparta": "^4.0.0", "mocha": "^2.2.5", "rimraf": "^2.3.4", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4", "webpack": "^1.9.6"}, "dist": {"shasum": "887c2b3d0b9bd86eca2be70571c27654c19e188d", "tarball": "https://registry.npmjs.org/redux/-/redux-3.6.0.tgz", "integrity": "sha512-fN5fXL4oTWTqAWoyooSfddnGPEsligiXOsdPhBqNqr402h9i9qZtRHTLnEeEEMCv6a0WLP2SZ1AWQ6krAYFPaA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcBHuJRtt8+bzU6dK7zjkEMKF+uGcPwfvZCMGtspRBMgIhAPli0UKatkJbL34jIEIFy5ji4frFyvTnhqpD3q3R9HhT"}]}}, "3.7.0": {"name": "redux", "version": "3.7.0", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^7.0.0", "babel-jest": "^20.0.3", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^5.0.1", "eslint": "^4.0.0", "eslint-config-react-app": "^1.0.4", "eslint-plugin-flowtype": "^2.29.2", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^5.0.3", "eslint-plugin-react": "^7.1.0", "gitbook-cli": "^2.3.0", "glob": "^7.1.1", "jest": "^20.0.4", "rimraf": "^2.3.4", "rollup": "^0.43.0", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-uglify": "^2.0.1", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4"}, "dist": {"integrity": "sha512-GHjaOkEQtQnnuLoYPFkRKHIqs1i1tdTlisu/xUHfk2juzCobSy4STxs4Lz5bPkc07Owb6BeGKx/r76c9IVTkOw==", "shasum": "07a623cafd92eee8abe309d13d16538f6707926f", "tarball": "https://registry.npmjs.org/redux/-/redux-3.7.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFHea4KY38ktedNvtdN3z9QNIHzr98dEPu58Y1x37kooAiAB77lItV+Xi1G7g0sRetG3fFzBiQCopp+omvq1RMhCMw=="}]}}, "3.7.1": {"name": "redux", "version": "3.7.1", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^7.0.0", "babel-jest": "^20.0.3", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^5.0.1", "eslint": "^4.0.0", "eslint-config-react-app": "^1.0.4", "eslint-plugin-flowtype": "^2.29.2", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^5.0.3", "eslint-plugin-react": "^7.1.0", "gitbook-cli": "^2.3.0", "glob": "^7.1.1", "jest": "^20.0.4", "rimraf": "^2.3.4", "rollup": "^0.43.0", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-uglify": "^2.0.1", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4"}, "dist": {"integrity": "sha512-iEVTlORM5mv6xb3ZAOyrVehVUD+W87jdFAX6SYVgZh3/SQAWFSxTRJOqPWQdvo4VN4lJkNDvqKlBXBabsJTSkA==", "shasum": "bfc535c757d3849562ead0af18ac52122cd7268e", "tarball": "https://registry.npmjs.org/redux/-/redux-3.7.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNRVRJhihL4lq4Ip1XqtGyAE3nUNsZd2yyG36t2jka3wIgKhzQri0lBN0iMfi7CXEAKC6592+M2QBL6siKezOwu28="}]}}, "3.7.2": {"name": "redux", "version": "3.7.2", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}, "devDependencies": {"babel-cli": "^6.3.15", "babel-core": "^6.3.15", "babel-eslint": "^7.0.0", "babel-jest": "^20.0.3", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-register": "^6.3.13", "cross-env": "^5.0.1", "eslint": "^4.0.0", "eslint-config-react-app": "^1.0.4", "eslint-plugin-flowtype": "^2.29.2", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^5.0.3", "eslint-plugin-react": "^7.1.0", "gitbook-cli": "^2.3.0", "glob": "^7.1.1", "jest": "^20.0.4", "rimraf": "^2.3.4", "rollup": "^0.43.0", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-uglify": "^2.0.1", "rxjs": "^5.0.0-beta.6", "typescript": "^1.8.0", "typescript-definition-tester": "0.0.4"}, "dist": {"integrity": "sha512-pNqnf9q1hI5HHZRBkj3bAngGZW/JMCmexDlOxw4XagXY2o1327nHH54LoTjiPJ0gizoqPDRqWyX/00g0hD6w+A==", "shasum": "06b73123215901d25d065be342eb026bc1c8537b", "tarball": "https://registry.npmjs.org/redux/-/redux-3.7.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFKn6uuVQ38/IFr3mBXELNgEwvh274mROmvAOx+az96AIgZ8olxhOnsUatnL23ZRX13S1JQgydxeRAETBHgj0kUEI="}]}}, "4.0.0-beta.1": {"name": "redux", "version": "4.0.0-beta.1", "dependencies": {"loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-eslint": "^8.0.1", "babel-jest": "^21.2.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "cross-env": "^5.1.0", "eslint": "^4.9.0", "eslint-config-react-app": "^2.0.1", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.4.0", "gitbook-cli": "^2.3.2", "glob": "^7.1.1", "jest": "^21.2.1", "rimraf": "^2.6.2", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^2.0.1", "rxjs": "^5.5.0", "typescript": "^2.4.2", "typescript-definition-tester": "0.0.5"}, "dist": {"integrity": "sha512-pM23av98/yFupfQlXf0BJnWHMBHivG5AM5a9OMG8VDPFGFsGm80blpIKzySdfJtws/2GsDcdFcptrOdvfXnwxw==", "shasum": "fc1cf2fc33d23a1a201d00235b3e27a331261ec7", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA6zhcovKghPGtSLGIYAfgxUBkIG/NIbqRUSxssEyTU2AiB4vvqD5V/M4QbbY35bSUx7XOqmSMVuA5oWcFU9t6LUSw=="}]}}, "4.0.0-beta.2": {"name": "redux", "version": "4.0.0-beta.2", "dependencies": {"loose-envify": "^1.1.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-eslint": "^8.2.1", "babel-jest": "^22.2.2", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "cross-env": "^5.1.3", "eslint": "^4.17.0", "eslint-config-react-app": "^2.1.0", "eslint-plugin-flowtype": "^2.44.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-react": "^7.6.1", "gitbook-cli": "^2.3.2", "glob": "^7.1.1", "jest": "^22.3.0", "prettier": "^1.10.2", "rimraf": "^2.6.2", "rollup": "^0.56.0", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-node-resolve": "^3.0.3", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^3.0.0", "rxjs": "^5.5.6", "typescript": "^2.7.1", "typings-tester": "^0.3.1"}, "dist": {"integrity": "sha512-suHXzW67Gd96RJlTfIMEvMczhqA6F+vJ3ZNK31ysIohNuUZj5//LX/aqmJJ4w5va7Tq5W6K3NNycT0/gqCXSHw==", "shasum": "2d83ee301b7de7ec9fdd2c6a4b8f6ae846632a55", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.0-beta.2.tgz", "fileCount": 18, "unpackedSize": 139234, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICps3tgx+SNQb6Z0UlM7IpsJ4mMFXNA2MBB7uquBVH8mAiBfr1SIKS/uqAyQKvgjklMooeEdpfYKaNVoHY0zCwEw4Q=="}]}}, "4.0.0-rc.1": {"name": "redux", "version": "4.0.0-rc.1", "dependencies": {"loose-envify": "^1.1.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-eslint": "^8.2.2", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "cross-env": "^5.1.4", "eslint": "^4.19.1", "eslint-config-react-app": "^2.1.0", "eslint-plugin-flowtype": "^2.46.1", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-react": "^7.7.0", "glob": "^7.1.1", "jest": "^22.4.3", "prettier": "^1.11.1", "rimraf": "^2.6.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^3.0.0", "rxjs": "^5.5.8", "typescript": "^2.8.1", "typings-tester": "^0.3.1"}, "dist": {"integrity": "sha512-5IP07Fd76jNX0edDrWpF09DLNtOoe1ZOCEIqg402jrvBBWLQ046c1uMlAC9imapuOLVTv8pYvJHuzN7wydrPCA==", "shasum": "80ec04db9837711dbf67e8f1915726dbcdbcbd88", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.0-rc.1.tgz", "fileCount": 18, "unpackedSize": 148032, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPXV4gv6ryqjgXm7qQRjbLJF7u1c7YFeJQaTr3SmR+UAIgAlFgXy76lKACE9NZFXZb45kDqAGhOiweBDZQScAquSE="}]}}, "4.0.0": {"name": "redux", "version": "4.0.0", "dependencies": {"loose-envify": "^1.1.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-eslint": "^8.2.3", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "cross-env": "^5.1.4", "eslint": "^4.19.1", "eslint-config-react-app": "^2.1.0", "eslint-plugin-flowtype": "^2.46.2", "eslint-plugin-import": "^2.11.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-react": "^7.7.0", "glob": "^7.1.1", "jest": "^22.4.3", "prettier": "^1.12.1", "rimraf": "^2.6.2", "rollup": "^0.58.0", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^3.0.0", "rxjs": "^5.5.10", "typescript": "^2.8.1", "typings-tester": "^0.3.1"}, "dist": {"integrity": "sha512-NnnHF0h0WVE/hXyrB6OlX67LYRuaf/rJcbWvnHHEPCF/Xa/AZpwhs/20WyqzQae5x4SD2F9nPObgBh2rxAgLiA==", "shasum": "aa698a92b729315d22b34a0553d7e6533555cc03", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.0.tgz", "fileCount": 18, "unpackedSize": 147490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1koWCRA9TVsSAnZWagAABvMP/iDDVvhi/YgHjHodWqC4\n0u1SXRAcSno2Kh1mD0E11K6duSKyVGrT/ZlVD888dDuuwoeFzfJNtCxEJ2ae\nTSvD88ODFNxhhd9+oAojeBh6uRZKpRJCzVrW4Xw1C97ioaZoz4QY493HfRvs\nCQSmsDAraqDI6VamuDqdvCDuADL+hCCIlCsR2aQiCwiJPx6nPjWSCBjdGsDw\nuxS+k7Ys+g3srZI2b1O7QIOWgmrfbO89B03mL1UXGJ70mS0e/7vkSC6+a9io\n0qdsXGNdhk2+5NN<PERSON>hbzceaQ5K9eIZg1haH/s0NSZtk62ZqZ0kAsdjl1kLpcB\ncmyt20ZyRlnKTVhCL2R3XijB/djhVY6iRQMIC3bxjqu9DxOAm0gMiGd/sVJ5\n0SJuiYkE0rEpIVqBSQB3Rbki7+ril4JqF3e25zUNWR/soS73w4N17r0PbZOj\ny2r+NzzN3uwEV8Hj92KVQWg8sDuBnRAWwWTolEpeS3gbVuwttDUrdAF7lViY\nRmhDBoC4O+XBkVITCWwKxtfMVVeyVUCF+XruVihp6qcKwUNO7qivlMAOCUJr\nlc2+1NP7fUFmiWuLia/0vADQDwsV0R8//8PWQU2NX8UAAgDyLoBT9Thxxtxp\n94PbRaYcMADWw3MYnwmGXEUZpEv4B8DVTX84FUexOOo5isxGiHwXXf2nfoMQ\nV/+9\r\n=D3gG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAVRrsqCtjroWKK03MnqL4jNYDPMmXLCcGunTiUmTt5zAiEAvNRew535QGkvHdcX/LnUiROpoWuQHc5yY6P5SAjRM30="}]}}, "4.0.1": {"name": "redux", "version": "4.0.1", "dependencies": {"loose-envify": "^1.4.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/node": "^7.0.0", "@babel/plugin-external-helpers": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.0.0", "@babel/register": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^9.0.0", "babel-jest": "^23.6.0", "cross-env": "^5.2.0", "eslint": "^5.6.0", "eslint-config-react-app": "^2.1.0", "eslint-plugin-flowtype": "^2.50.1", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "glob": "^7.1.3", "jest": "^23.6.0", "prettier": "^1.14.3", "rimraf": "^2.6.2", "rollup": "^0.66.2", "rollup-plugin-babel": "^4.0.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-terser": "^3.0.0", "rxjs": "^6.3.2", "typescript": "^3.0.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-R7bAtSkk7nY6O/OYMVR9RiBI+XghjF9rlbl5806HJbQph0LJVHZrU5oaO4q70eUKiqMRqm4y07KLTlMZ2BlVmg==", "shasum": "436cae6cc40fbe4727689d7c8fae44808f1bfef5", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.1.tgz", "fileCount": 19, "unpackedSize": 154572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwXUKCRA9TVsSAnZWagAAJ2gQAJ4iqJyHm47nH7l6ucUB\nmyRgxvzVQisuXI/bJ+gt1U2cJsMqaIpQFzUXUUpNK6SROqaEDbC+ghzRGOLe\n2wac2IyuiRyNubm4Njaq1gueBr7UxHbOLfQxr8JUN00SGCnr75JYOAJW0KT6\npUuSfgTRm0XB3rwZkC5mbPrLZpNFMCcYyaC/kpIB0Cbekv0fkhfzllCSnMz8\nNj6V0Fa6XqS6NnR5jnqLGsSor499xqXNiz83msxwUcjZRst+OEJ4hjEshlMt\nC72iogL4/CFVVXLOZE1c1GhK85BbpujeRjekQz9qeNwOXhA8fBtA59R3PAFU\nhNJEtCVdPTDfZQDmP0ETI++hxKuajoH72kjjxJtOaZhklqDiictHkgn/h/TR\nZJ9QbdNVZMY6T8feuPpncvcwNPiTkPV36jA2SazT+dTa2Q8afDnhiSMFqp8r\n/JV3Gaw2Mg3XF4UUcaEzrPoW1S5FsODKt2uohFIvM4jA9aKYwLzui2MB4Mhw\nk5BpQGGY15M6DF7FwsJ/mkLziHr0W+/XZf6/N91b1ZjRnpNB6uHoo8hq15F4\nHZGa6FzKnv6qGHEFcmkN86T6lUAXOZ60BfUiddQIgR4od3Vn++QWwvgNBJA4\niR+pdoCuEfQx0W0SKxIZ6cFp2IGcpgr9bXMsjGZ0BYJLrXpAbQEyzPn7qrJB\nA8so\r\n=wuBs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBAuPaZADgIpUIh4GTORzRbRcwFkqsAx49oGO4XAsAb2AiEAnxYG1JxYZrOuyku2Td7aFOPOwUjN3bse4+CWqBsuyXQ="}]}}, "4.0.2": {"name": "redux", "version": "4.0.2", "dependencies": {"loose-envify": "^1.4.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"@babel/cli": "^7.5.0", "@babel/core": "^7.5.0", "@babel/node": "^7.5.0", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.2", "@babel/preset-env": "^7.5.2", "@babel/preset-flow": "^7.0.0", "@babel/register": "^7.4.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "cross-env": "^5.2.0", "eslint": "^5.16.0", "eslint-config-react-app": "^4.0.1", "eslint-plugin-flowtype": "^2.50.3", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.14.2", "eslint-plugin-react-hooks": "^1.6.1", "glob": "^7.1.4", "jest": "^24.8.0", "prettier": "^1.18.2", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rxjs": "^6.5.2", "typescript": "^3.5.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-oAiFLWYQhbpSvzjcVfgQ90MlZ0u6uDIHFK41Q0/BnCfjEg96SACzwUFwDVUKz/LP/SwJORGaFY8AM5wOB/zf0A==", "shasum": "597cc660a99f91412e31c96c3da10ed8ace0715d", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.2.tgz", "fileCount": 19, "unpackedSize": 159891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI/e+CRA9TVsSAnZWagAAkzsQAJ+gNAHFZKbff/sD0kI3\nli6Ub24BM5G2mKV3USo91caHwqAB5DGTqrpIfnYunx0y+r7QinAECqlw8bJb\nbMGHiGhnC6+KHv3syPyU7t22peR1Eern6X8LIBonKc8tdlEfeSLId1Y4UP1M\nlhcvNCECZcVKGFMYDaIysoT87nzWjiCWMFRRYwtP+9izwd8IQA0hwOj4nUs6\nZNKTGCWblWmROtZ3kvgDiDVSRaectvtNs0iXuinJR2e2msVR9A8pwgFhFKWd\nT9v4g/si52KMcvGD3Zf6LAAdeeTyDQ6M7ly8afV0V0Tz4h1azLnlda+qcCPT\nXkYdDY6Z5P2TE9UN+BStu1Vw7QJra1kN5xMu6avKu0/MnwFz5ovOf/8bP/Yy\nQ7F0ptvUIqw2G3IdfxJW4n7LJdfnBBeb2ak5dTyc3LJGAJyCvF7zuH89PNu7\n25VD3fcQeYL4mFz93AOksreSkKhxY185eTCG8zdgCsbAgx8YwkchlYkSyM9u\n2dHzaHJbazODfD81P7tbeDFktvlK5u/2H+j+lpn9gH6wYTdsXdH/mesnUn45\nCfz3Y7IyxOJhED1z+oTjcE7WFBr+07CjDaQjxCezEJsoHVW/bzDp3JtixQQl\nMBf/j/hyXyNV1thXKkhMt+XV6FdC4uN3/JxvQHe5xaEQpKCsnL0zs9LKKnQS\nPQ5e\r\n=jnba\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2oJCNRpKCZPL2TpGdbwBVwX5SA7OdzehUMPZDHPdV+gIhAMOOKtNRClD/qAmUKXEkxpkrMWi/AanDvmoE6GI/HljU"}]}}, "4.0.3": {"name": "redux", "version": "4.0.3", "dependencies": {"loose-envify": "^1.4.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"@babel/cli": "^7.5.0", "@babel/core": "^7.5.0", "@babel/node": "^7.5.0", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.2", "@babel/preset-env": "^7.5.2", "@babel/preset-flow": "^7.0.0", "@babel/register": "^7.4.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "cross-env": "^5.2.0", "eslint": "^5.16.0", "eslint-config-react-app": "^4.0.1", "eslint-plugin-flowtype": "^2.50.3", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.14.2", "eslint-plugin-react-hooks": "^1.6.1", "glob": "^7.1.4", "jest": "^24.8.0", "prettier": "^1.18.2", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rxjs": "^6.5.2", "typescript": "^3.5.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-v/Iaw67Pe+na+cZvcKvPxAKT1ww5kM+M09fmaCndCQC4Lo434AYb5975HJgJlp0D7dJxfYaLxMD4VwfpLOZ1Rw==", "shasum": "0ca18be085e6cf6ed50e445a125f85e8b26b266b", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.3.tgz", "fileCount": 19, "unpackedSize": 159714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJLuOCRA9TVsSAnZWagAAsvsQAIoYE6oYZ/60IysnFBqM\nnTQ8cXA+7JsMy+EplerR0i3Q/1Z0wXuzNHPa20X0uRb1/RRy+a0ZN5xuz6sw\nI0atx9yQXryMpyXxrgnOFhWQZdE6a25bZY+cj9IX63xQxqsH1RpA01MTktlb\nltBzQrX4tvJp5GpoMmjyfeMgeL/vc2NssQomtpY1UaO34gJGizA/3UJF8o1m\nbYI6ajxiOkK+R0R5ALkprwd6REU3xuNpiEAg5Pgmt7r2Q9az3TOdDZOGHxfh\njtM3W5y/hN2uLPQkTUZBE/ZeCIyE6HU/L4uErhB9u4f1rgBmU8hxkXqmiGKZ\nQl0QR5srcCu/1Eh6AmomhkK06QIW9+SjoehCLNOceDBMzT3WRRFY+Dx5EABF\nn6I7aol2enP9vSPP7VsunSciAB4GKxLgUwSw4yhZH3IAbaBPOCe+h7E8mKMh\nJ4Vqk7NT77oNeTFZwS9Xp3p+UtjiFOnLpxuwLwDhbOPhWlFJE6SXfYnmvKbb\nVpQxMJ2ShqPPJviuGiK7Oe2vJkO0rq0n29UUZW3iQh4KVEIL+yf7Zz2JhRX5\naXqdlTgWiguPZDeuNH6vF/2aN9PErq8nd2SfpbFjQNxhREQM5tmhxQeF2rY9\nuRPCaH7XQiJHzQWjpE6dKSxoFniennwSc6mciULYldZJ+8MFxYu8EBw61Vw7\n0TRy\r\n=Fnup\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGEAxLWttQeMEg74WnW/8dnYGeN0U9TTKgaPtxOcR0YrAiEAqmG6H2ImUVdRv7x3RUmIAyD+TDFu0t2ecLpiL0b3/tQ="}]}}, "4.0.4": {"name": "redux", "version": "4.0.4", "dependencies": {"loose-envify": "^1.4.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"@babel/cli": "^7.5.0", "@babel/core": "^7.5.4", "@babel/node": "^7.5.0", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.4", "@babel/preset-env": "^7.5.4", "@babel/preset-flow": "^7.0.0", "@babel/register": "^7.4.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "cross-env": "^5.2.0", "eslint": "^5.16.0", "eslint-config-react-app": "^4.0.1", "eslint-plugin-flowtype": "^2.50.3", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.14.2", "eslint-plugin-react-hooks": "^1.6.1", "glob": "^7.1.4", "jest": "^24.8.0", "prettier": "^1.18.2", "rimraf": "^2.6.3", "rollup": "^1.16.7", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rxjs": "^6.5.2", "typescript": "^3.5.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-vKv4WdiJxOWKxK0yRoaK3Y4pxxB0ilzVx6dszU2W8wLxlb2yikRph4iV/ymtdJ6ZxpBLFbyrxklnT5yBbQSl3Q==", "shasum": "4ee1aeb164b63d6a1bcc57ae4aa0b6e6fa7a3796", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.4.tgz", "fileCount": 19, "unpackedSize": 159645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJjY+CRA9TVsSAnZWagAAULAP/ieb9UIxve7HG/jm9yiU\nIt2FbKK2zWoVdLm409i0g+qKEAgZ2F/F4S103OlNtysWFQZ1h0Z/+DKtrBQZ\nm7qdK/IRdtzIV0nlE1Ssuj35sY1rMQLpFuoABN/kkjMJReedfU3ziczhilcW\nEqkBfo+cx/hTlE090ft/2lSnHcsMkHaZCQUUi0v6MiL6eb0GRr5vMSeHOStn\nedcUFAhH83OkgACsDt1nNMYLNjblccbOe14C/LxwEoZGncrFCoPLaZ/b1FlT\n3Da3NPFBDnpDJEs53INJLWNzYyrkemgSrLqCv2uR9HFKm+fqfZtcU1exhkVa\n0cKkgjhIAltrYxMrioiXEm+2K9S+s6CjQDwcBZ/uxGafv4UwChQxY4Tn6FP5\ntPdBv17OcDBdBnvKvdWkRqWRNDy/tO2c8UNle3i1Iy/LR9pGskqkZeHG46uw\nM6VnOF2ZsmEAGbdiw2ODluZYGNiQJj04dviHpWu1N2xQPsp0FDnEdjXShhlh\nsWAYcwRm/ub4OwAWFRB8CIBA82XucbZijPpSGclxchcHxsioAcEJEOoKPR0u\np2QGnVZtipcmfWl1xySpKmCHqeII/CFdT+Fa2wfUw6oGBaGAVmgYbg9GdCMX\nXzVSVA0RMwvduuXcIhb5jbiMEewTKe3SupANZlVQysuXEG5F3Ut5NQ3NX2mX\n5CRW\r\n=D/8+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrLwpYaAaHKHbUIHYqGkoiqY9wXyEfSdwp5pNhQwZtKQIgI8cOUcS0dr/Imjwn4TTixZek6dOTfUQD8b1NKNIsupM="}]}}, "4.0.5": {"name": "redux", "version": "4.0.5", "dependencies": {"loose-envify": "^1.4.0", "symbol-observable": "^1.2.0"}, "devDependencies": {"@babel/cli": "^7.5.0", "@babel/core": "^7.5.4", "@babel/node": "^7.5.0", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.4", "@babel/preset-env": "^7.5.4", "@babel/preset-flow": "^7.0.0", "@babel/register": "^7.4.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "cross-env": "^5.2.0", "eslint": "^5.16.0", "eslint-config-react-app": "^4.0.1", "eslint-plugin-flowtype": "^2.50.3", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.14.2", "eslint-plugin-react-hooks": "^1.6.1", "glob": "^7.1.4", "jest": "^24.8.0", "prettier": "^1.18.2", "rimraf": "^2.6.3", "rollup": "^1.16.7", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rxjs": "^6.5.2", "typescript": "^3.5.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-VSz1uMAH24DM6MF72vcojpYPtrTUu3ByVWfPL1nPfVRb5mZVTve5GnNCUV53QM/BZ66xfWrm0CTWoM+Xlz8V1w==", "shasum": "4db5de5816e17891de8a80c424232d06f051d93f", "tarball": "https://registry.npmjs.org/redux/-/redux-4.0.5.tgz", "fileCount": 19, "unpackedSize": 163076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAXaFCRA9TVsSAnZWagAADRQQAI+kbGUuCrKmw0IQirmx\nJ72079zb5MWTmG/aTBP2S8OD8XYSxsUGtM+YjtM3yNYOGpVn/l8rjovSL5Pu\nbBxuo7YGgAvsqXFcAO6MXK9sLls4WcFffeg7dQwzvMPbR4x2ZL05WZIOMYOd\n0K373ycgqx96trfZc9XSOvKYrd6p28won2LwrzRUw4QyxdfBdUBVarWp883O\nqrWTVX675FfxbH8nMfW2o5hiDhr7iEDYlrcK4EinYoulxHmfCIeHvfuAfTWD\njf1EhuCzskaDXkbf2apnOIXBZvdF3LEKiaa8yWBXrX5AcRcfIfMg/GGug+rR\n0qm6VUuiJo7rAUn1POYPK93jbJRQjv1V74rhxNcnMFgxFvo/CosARIFAz9mO\nJC7y0nUjsGm9YgouekZyfY6nnzSptrOqvz9SCmFqNmQ+CimSAJ9uFTxF9ql5\nP5g1OtlEvzDX5xQ13uD+qEmUQ8d/stY4RYNx2vBFnG2VNKF1rNYt3kdhkZ7N\npif7IyFDBQQeyWi/eky2ZXB6Lzf+ZWJjPQeaEktgLEXljcUQQxqUAU4R/0bU\nN8k/NzNwPTA247t1I9X4sD9iibPK6ZAmVw6ifspCTo5+iGn3BGBnNhBgAi0p\nEWPV+7T04hogpTcyuB7wjaZqA6Wrz4qFg8O34xy5aZD7925kpMDNgKvPph74\n623f\r\n=du86\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9wDDPlGYp6f5zXXDterhWvyPS7mcTQSs1XnBhHjGldgIhAMZNYD+aJX7c/ApVHRaBpxba0O12ity5OgHYnY30YobR"}]}}, "4.1.0-alpha.0": {"name": "redux", "version": "4.1.0-alpha.0", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-nshYN0Qq2+XvWwly4IxOtKfkjmDQ1uW8UE5FA7Prb6JDt49Xgt9Myur6Z9FbIx+CLP/4IWzMA/aNO0t1dHHZVg==", "shasum": "1b4de609ca8acae7f1ddf11fae21b709be33910f", "tarball": "https://registry.npmjs.org/redux/-/redux-4.1.0-alpha.0.tgz", "fileCount": 22, "unpackedSize": 170061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgaRSxCRA9TVsSAnZWagAAUWQP/R/KM+JLHkrGR8DAw7c5\nwpjJx977WlS9BoqaKybD4u/ZZBf2pGMlLrtxqS3M5z+L/SzivYpK5Fjv/CnV\nyfEkc6MV/Cphar5RjG5j0946/GKrlThYuWB6XhofT1tAR8cfQtSwXa6du91/\nRYOhd3HsFb5ibrZhGSerB/0d9f1p6Qhtkj7jnfdQeqHt4nKuCIYmKxLK/mCv\n83Xak5O6O36HIgh3gafkAaQEDEpf3itOsmBvXQU7nDxfrLmuCD6fVeBW9nWo\nTgpGViDBolfKrBuSsoy85YQ4ZxshCqHkBXg0hOvovP+L+GtMe/RL4sZNKuvY\ncB+Vs3tJUvClpmiXWLD8nJppXadof5vI6Gyqf44IG/l1gjzcclLeNx4OoQUL\ndfx+TkB5KXV37JVPynHfJItgL32xOvha043YTAdKHbRnavT0muo1UtNk7Qt2\n96LCD+qyf10zf11LoSuF/T8lvNIAd5LHYynofuBNmqEbJhd1plxCVjJeTMGx\n+xojGxWPsT4I+fREtbcdFAvkJttDrEfu73ABYNdRPqQbwQlSUisjgFkQQbeT\nP1xj9pQ4Z2cgP9x/MibqtoSxWeWqmgo2bnBRqqMZJEQ/1EBh9N7HhCDrhNTa\nhN1qu1X+lznW+7G1GmBHh4w92INjzT44fUbHsSuJAngRCbaddq6TU/FJWsH/\n6FpV\r\n=u6QP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0XeE2mC/hJ/lW8rEUSvDdix+Um9NEL3QMHS6qBMGfIAiEAkNlkAM15fN6eWPWX5zT7plnV9acEBCKMoFZ7ot9Zu/8="}]}}, "4.1.0": {"name": "redux", "version": "4.1.0", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-uI2dQN43zqLWCt6B/BMGRMY6db7TTY4qeHHfGeKb3EOhmOKjU3KdWvNLJyqaHRksv/ErdNH7cFZWg9jXtewy4g==", "shasum": "eb049679f2f523c379f1aff345c8612f294c88d4", "tarball": "https://registry.npmjs.org/redux/-/redux-4.1.0.tgz", "fileCount": 22, "unpackedSize": 170053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghFkTCRA9TVsSAnZWagAAATAQAINkr2NQf2h+3AgGnfKM\naHoa3QYpORn2Figzv+qpZXFvN5rYxbdNEBzAWBFO53gsV1XzUqyeIodlicYM\nvKUuk45kYPckxo6dLJ8mwyENkAuRo/uemxIPOElCxduGerjZgJXV5PT0pt9Y\nb+5umBSQEvPY5KV4TiRRFiACR9NSKBrhSj1sn2XMvoxV10JohatQGY44L/Vs\nBz3zMqno885oriOZHUEmbLe6vL75+sBgvCiTBGZmKKu5wKFZz+TCpz3RJIVm\nW0gdCoGOc2iOmRMgCiCkJgvF/aaqKMmUcSB5Y2Z4VjRQE4b3Zyt75RTdBrh2\n6fTAla6tnzMmtTPSgq1acrSjfaZuFYtxDwu11y6yJF6qlZKs0wMYohilugHi\n52uYN45wHTAWRyzYu8VNYBaOogfb1Q4igc1vxOSYULpa+/heb6ZflkH8a6xI\n6vBHgxABWa9enB2FJAfg00DYb22bL5gzJfiykPeqam3eAwwHtcgT93cgoMfA\n/g45mYJaIOH/NsQhOcuh4nSpWi0fTXo3W7AM9yGCcPxLrpIvKU9RpedwcY1s\ntKv8I0FjjOdr81QWuDXehiP5kI7g+Y9QDUcXm383OR+nEpMjG/tgNdSaJpto\nB1Nnrg4+xKjTClpmPYVYOI9Shpoit8hTLibLqX5dOiEXzDFPRzQJyRkcJq18\nomnN\r\n=E70X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCt8IjY2tDmRD1NM3zNgrcSXja+AtkypIdmF9RPWXbkYgIgJ55NusQokrqUMZ4DXaBMaeJwYHfe8wt7EfVegnRkVOc="}]}}, "4.1.1": {"name": "redux", "version": "4.1.1", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-hZQZdDEM25UY2P493kPYuKqviVwZ58lEmGQNeQ+gXa+U0gYPUBf7NKYazbe3m+bs/DzM/ahN12DbF+NG8i0CWw==", "shasum": "76f1c439bb42043f985fbd9bf21990e60bd67f47", "tarball": "https://registry.npmjs.org/redux/-/redux-4.1.1.tgz", "fileCount": 21, "unpackedSize": 169028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCVTSCRA9TVsSAnZWagAAmf0QAJtsQB6X4WG8mRRzI5ho\nD75z2J4gJjeigRIZ6AeUuT84hUeXFDY1pL6XhRLhKlBb9rZNgyQNQ+th2ooy\n0vfCX5peTxEV2B6cxfsZ+mkj4ITaTKDdFGXPBAe0DhEbDTxQkkNUbJMeafYf\n9dzstfN52697ePrwyPziCd2sabxqPqW8OptavYQrhWa1Q7otA9j2058GdxHk\n1eB9BIS+6BFoXi1wcWrXx4vE9kMEEV2uNhI91LeqjzOLHesozlYVgp2RCJjF\nc4EaL9LnIXyfj+DOhi/hNN+oZ1ukCThLu60MXqFf2/ZX6TIJ2vgIjuc4pynk\nYF05faKYxursDIabZca0SUxB8Uqwkgl0fQYhd4Nzn4pTrlYBbKf7Gt6y9lB9\n2C0eFttq5DHw6feAU01ENcqzbIJqzxE+U1TB5h7vbtxXDg500AdnmeCmBGo8\nWi6XMUL1vYQ3Xt3Z8YJXoq/bQr45tPVxRoxSkvLdSMr9BU5FtFG7oMsNJHkr\nXnwpX7/9t6+UkPzntn+CRr8Uc3Uco8ve9Bg7GP6okiRN9e0wRknrnHsw69MJ\nwIQtyw6y2B47Po/0+PUSB96IFbub67pXCWY7fdvXcQpiIATA4bAdzMIAeI++\npKtmbaOHXYHky3+lff5KQKcWGLHcV3PhZ/mnG5mBc7u13L5Uiv3CXNyV74hD\ncfcU\r\n=0FS4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGjcueInkr5gXH+9dOwptS56J7ktvqmw43cOwSumsjxAIhAPB53g8i4u+aFQ6Sws58If7jr1VVSVsFmTbJLG9Z4qNA"}]}}, "4.1.2": {"name": "redux", "version": "4.1.2", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-SH8PglcebESbd/shgf6mii6EIoRM0zrQyjcuQ+ojmfxjTtE0z9Y8pa62iA/OJ58qjP6j27uyW4kUF4jl/jd6sw==", "shasum": "140f35426d99bb4729af760afcf79eaaac407104", "tarball": "https://registry.npmjs.org/redux/-/redux-4.1.2.tgz", "fileCount": 22, "unpackedSize": 169357, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEI5PtZNb+T55Rd4I+he6xPk08DG3hOdiVgdpzzxYqvbAiBdCxrFwZipG7mlh/oTzgo6kD9jLcqdD19jIOZ8Pz1Jow=="}]}}, "4.2.0-alpha.0": {"name": "redux", "version": "4.2.0-alpha.0", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^26.0.0", "@types/node": "^16.4.10", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "babel-eslint": "^10.1.0", "babel-jest": "^27.0.0", "cross-env": "^7.0.2", "eslint": "^7.26.0", "eslint-config-react-app": "^6.0.0", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^5.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.2.0", "glob": "^7.1.6", "jest": "^27.0.0", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.30.0", "rxjs": "^7.3.0", "ts-jest": "^27.0.4", "typescript": "^4.3.5"}, "dist": {"integrity": "sha512-tXZOrc/4pL+clsBLp+eEbfQRr0nnzc7tIixZELZeS+wOOEbND/tMJ5nDYsJhkdqmpwxIYnZN6rZSQe3d0PcQKw==", "shasum": "6ea2d3e8cc5a24213f0c00a6a91a276bd78c1a79", "tarball": "https://registry.npmjs.org/redux/-/redux-4.2.0-alpha.0.tgz", "fileCount": 40, "unpackedSize": 179124, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH2rpD4HT48wRwARnNFWPZZkdSS6XIVuQc6fHXJi9CAvAiEAkyEkwwhAiZD27Y6OSNOAkVPJrWuOWHpti7v8BTZhJi4="}]}}, "5.0.0-alpha.0": {"name": "redux", "version": "5.0.0-alpha.0", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^26.0.0", "@types/node": "^16.4.10", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "babel-eslint": "^10.1.0", "babel-jest": "^27.0.0", "cross-env": "^7.0.2", "eslint": "^7.26.0", "eslint-config-react-app": "^6.0.0", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^5.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.2.0", "glob": "^7.1.6", "jest": "^27.0.0", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.30.0", "rxjs": "^7.3.0", "ts-jest": "^27.0.4", "typescript": "^4.3.5"}, "dist": {"integrity": "sha512-9NQVVttmTiwECalBRd6sKWW4e8u6ekR1rlfsHy0ZOU95kcDpKp4enL8xbNZkKgT7GhnNmlfNZp1HWlHs+XZaww==", "shasum": "a787df7b92a69af70900c84586fc2dc89ca97ab5", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.0.tgz", "fileCount": 40, "unpackedSize": 179017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/zfb4kHwBp21SCLwSRJcJQBvefweZaVV2daiEE/0iLAiA0xjgK8dCv3ZKnRpr3OTBN4aOg59inWTDO8WPb2h/Tzw=="}]}}, "4.2.0": {"name": "redux", "version": "4.2.0", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-oSBmcKKIuIR4ME29/AeNUnl5L+hvBq7OaJWzaptTQJAntaPvxIJqfnjbaEiCzzaIz+XmVILfqAM3Ob0aXLPfjA==", "shasum": "46f10d6e29b6666df758780437651eeb2b969f13", "tarball": "https://registry.npmjs.org/redux/-/redux-4.2.0.tgz", "fileCount": 21, "unpackedSize": 178599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBWa/gU8FOdWqAZyU0ufdPU2iSYITY43lH+7Cld1IUFgIgdd41oVGdtu1Mb15XPdBCvCOAX0oUeRC1nBpcjbk6XQU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXd5DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/9BAAo+hUNoK1zX9PB/wQG98KqmRrtFuSiwnemrGw6ii6489WkS2r\r\nUJVB9jRXWDDzK40HhreBpa8etbOZymbNj9FB9GiLrmCt/5czFluWZwx4kxQe\r\n9MuISRkO3tYF3CEvfs97fmCfsVr+4zP6DovM0JsVWrj0siZKhJYNQTNNlJ7l\r\nPO2EiE5j/310d7BUfINS9b1E92xA+gmfQniDyqmT3s/Z+youHXXjU23cpXN6\r\nQKu2z8Sv70rvy1Q8PdInIu51P1ysjV0Dn2Z0p8yVj/uJp0mMU9DAXj8AUOKp\r\nDhP0zt78ZcSMk7u30qBEPlnmHqHUJhb30t6bXRDDId+ds3Op7+jOKgnKvsrt\r\nqxjQOiay88gjMWBH7wiMptaMEL2h6Vy+1tPF5vKGwMlWzTuyI5C+kHyVSca+\r\nfWnM/Wv1bahiVsigaAV4ZLraKOuODdufvP+FU7Wziyg8gxx+Kk24UswTtVvR\r\nYwvkbv0nPvO4RllY80QrR/CHCD2hRwg5XlbaQc2HX2dkpA18o/ZSY7chS/3n\r\nM8chf9jUlrAnnMhIRyt7wHkKjmX8dPeAlmSJbEHmHc8Yw30t7FcK/CGW4Yma\r\nZPoKLcbH224IqQ8kU0rzRFqmOKwXoZyQu6xMI7u45GD44MyIQYc93750vm1a\r\n8El6AFz2+pXVQPMfoiqDN/RLcml6SdwIzpg=\r\n=E3f4\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.1": {"name": "redux", "version": "4.2.1", "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^2.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "dist": {"integrity": "sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==", "shasum": "c08f4306826c49b5e9dc901dee0452ea8fce6197", "tarball": "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz", "fileCount": 21, "unpackedSize": 175632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAvDhSe492LZkkKddau3Mlm9NtZgLqpRHY0on5YqJxB9AiBkmNOgtjyxw5C3PqU65HGqYf6Pai17Zr8mQvbKywFOBg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1ZnGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozAA/5AdnX9Lrl1YITJDqgCe2o5mVP/uUXxVxB6mo3yBfEICoNGUt7\r\nrXf4T7bALOstKK7pedeG8iW3RQ4HwRVt7ORK6utkqwncA64HwjJ8+LXFU+Ip\r\nghF4q/J3W3gNDS+jx8H7FJjjHEgW7ZeJWqY5SdXdKwQDzlUdDVOk0PFELZpR\r\nILJq8ilkXoINYKJ8fAK2grn6UPJOq/Vbj1QRX7NobY4gbCOwdkxQY/+wIUJ<PERSON>\r\nKcL8W7GbXLNHWD6T2UJxmAtWrhAkdNCHf+PruDs84xHKMEgDQUi3vdFbl9CH\r\nN53IqCpZ9H4717JL8gZyaMEWPSd/Uf2C4FCG7jGQk4QF7R/kVg6Zyb1ST2a+\r\nQvEwRFHh3bRWSM9MFRQkaDpEtgQtIGlTzLB9dMWE+EX9xm0yox+R+a8cteGJ\r\naZZF2cjNX8IgjNFydzwLE0+uhQ4hNoIURgAcOKiBVptfNUvLWV+8nVyzkqZL\r\n2L6SP468vFaBdZl5oTrFMl5L6sExbkVY3Nvbo/U67c+wH8QVrxMKKrvIayCM\r\n1HmZXlf94N8kH2QQ6VarW01Cgt2tzoFZEkGMaKuQlg59FmV1rB1xdHcPlEab\r\nFUPXFCjfkOXqSU4ORCygGXJ4A7qaiqDgmTYRP9VccSRO5Qt+krJwkYZ6dQ23\r\nnR2QmZfBgjZ8PvP7VkCoaAnT6WvMeCYE/o8=\r\n=9SRV\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-alpha.1": {"name": "redux", "version": "5.0.0-alpha.1", "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.19.0", "@babel/eslint-parser": "^7.18.9", "@babel/node": "^7.18.10", "@babel/plugin-external-helpers": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.19.0", "@babel/preset-flow": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@rollup/plugin-babel": "^6", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.0", "@types/jest": "^29.0.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "babel-jest": "^29.0.3", "cross-env": "^7.0.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "jest": "^29.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^3.12.0", "@rollup/plugin-terser": "^0.4.0", "rollup-plugin-typescript2": "^0.34.1", "rxjs": "^7.5.6", "ts-jest": "^29.0.0", "typescript": "^4.8.3"}, "dist": {"integrity": "sha512-uhaB2F5QRTv0YtKGtFDe7wjQjhBCP2TI4tpjW/O+iChhrtRh603GVzcgkAVFvRRRp3L9wcfhlSnEnqgKz5ABaQ==", "shasum": "84b2c39ac30548fa8061d6383b3c2fac65f0bdc2", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.1.tgz", "fileCount": 38, "unpackedSize": 154003, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHuc/4+2I64fV0K20AqiHDWavhV5gVqscMd0ZKa8mD5AiEA4yCkK1RCrTaGXg/gc/US6JBO2mKkzg8SiK6GUJTOJCU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1bmBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPTQ/5ADbaXS6vHqAnUHWEeYuVIaqee4MXucU9VlvBcLnnZnZBnH0i\r\n8FbkbuVEm7YhVT/9pZZPoblBuScILrO0nYSo59dhjGopmDIkSL1Eni50FMyd\r\nP/mqIPpdoTf3FN9kLod3ZgTSWO96E4vfNjC+n+UmAXa1lCoPlUjbmJ+9HYlH\r\n2+5VVsIMYi+7Nc2LwzfIGuI3vHmtFn/EM6mT4hXstaB6gAtbezUPGUuGyFZf\r\nj4mz4Tq5HWQe9PTVVXl/CK7UXCfbGcJDDgzOt77JKIf/efs2c8o7TvmDE1ib\r\nBHV+URMIZbynftMABTmsJWmPvlSwuaU9moNPJraGiabA0mURnPFPlKL+yQfE\r\nUuEylxZT5gLOgI4XpLnerrvrST1KTuOfpsDMpuqlSpkQ/RBn6RguZCgT7q+a\r\nORPYOVA8jlSJFY9r1OV2relw7BBknH97fWbJCKtO659qHf5RqRvzUrZCVtkg\r\nZ7d1u03OsijivOfzrFBwEsfPZ0lCpvBGsJx55/1ekFpXjcCQI3gqACfGCPcI\r\n0F60654l93wZD7tlQoSui9ExfsqRawI32hDYd35KxDGzqvmYlZyKrEStuNOo\r\nb+GCmOsEnUEsfN8776Hu0SVJMBK70NfyLnS9gZSLh1vlYzo6wyZDZsPn2nEM\r\nUJQvB8esqEcn/Um+O61cq1DcE5F+3UTzKfQ=\r\n=MTaj\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-alpha.2": {"name": "redux", "version": "5.0.0-alpha.2", "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.19.0", "@babel/eslint-parser": "^7.18.9", "@babel/node": "^7.18.10", "@babel/plugin-external-helpers": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.19.0", "@babel/preset-flow": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@rollup/plugin-babel": "^6", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.0", "@rollup/plugin-terser": "^0.4.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^3.12.0", "rollup-plugin-typescript2": "^0.34.1", "rxjs": "^7.5.6", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-elxHLhB3SFrwq0N9f4P/jC25JqmPcilnI4NnvZvUOOrSTc2dIl9w10wgc4m1OGEPXdumqOGlpykQeD1rhEwIjg==", "shasum": "73f9da989cd13cc685adc56857647584a278727e", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.2.tgz", "fileCount": 46, "unpackedSize": 152435, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6+0yGS/b+eoxGdQoOR+DAeK1ZhQb9O1pLPTCyPI3vOAIgWW1XbJDtKxKJEr0O7DqkkKFhAlmFeTlKBY64qH/5Bl8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6aljACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfBg//Sgw299G+ICaw/eWWQBAU9agCkwrXvTx/ISwbgnRN9xdsJ4j7\r\ndIczDx3MAMRHLI0WNx6c8RNNaAV1NLLjViMh1mwntMWYy2kfeewtd87NfqRR\r\niaNvdBiLGKpSuXSeNEPGpOjkbVnJ988U0r+YJad97wWRMd+WVB6KKGof0EwN\r\nzn0islD7Ay3H0rlWqxn0zSa7Q1kuDwIGBEVKsFb280PlenysYgyHTe0SisZ7\r\nhhyuyr3eLcfxv13+NCxjoB4rs5IOjwjLnG6XVZLzEPMvxIafkCAuW0IJKydl\r\nd8MLds7kTSv1wFg+UDJY07Nyx94A4n+5fvoPJ2iFJ4VzR4Jn/2s980mrqRnr\r\n12yaps0MjL8iZTIneUPJDXaauq5SHjlQwmI4QFQt5Di3XsciOTxcc5sAz3X5\r\nDVZ4UJ0vvwAJBAumuPvMY75G9o8dTRVTZZwIuRpJkhBahDrmWyrBdyNwP90C\r\n5AUs0PM224AfcrUIDRqxj6udjdEZe5IdzGJPgu+idz6KYwS8AyNBKmZxMYF9\r\nfZhpw//NlH/WI0h7op0JdyIlS0VmiPta+bB/LLLmRBI/HfkMCFoBNwioj3hD\r\nLfnryz9qePEWV3A+3DUzyIzyidGyg951d/lzVbAgb1Q0rkkJcoq/G5EH0hBu\r\nmByB50ONc//g0zXowSZPs/PGsl4tnK3WQTE=\r\n=ZcMw\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-alpha.3": {"name": "redux", "version": "5.0.0-alpha.3", "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.19.0", "@babel/eslint-parser": "^7.18.9", "@babel/node": "^7.18.10", "@babel/plugin-external-helpers": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.19.0", "@babel/preset-flow": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@rollup/plugin-babel": "^6", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.0", "@rollup/plugin-terser": "^0.4.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^3.12.0", "rollup-plugin-typescript2": "^0.34.1", "rxjs": "^7.5.6", "tsup": "^6.7.0", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-+lhNZesulXEbr8Cv+XxFarq7q1i0K/4MQ/ruapoLI2idoU4vZpgV9rV5NxKWJleT0R18IubNLesroXYLqqz2Ag==", "shasum": "aec36f92360d4ef97599697436079abb081217f5", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.3.tgz", "fileCount": 26, "unpackedSize": 277673, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC9HPVFh47ZkTCx2ELWS6hftCTPIMLhov9h0QTAd1M0NAiEA51pOH4VHUqrQwKGH913oWMOmWKVNkN4V/GfN4f1w9e0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKiAnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbKg//T3JP3Z/kwuBJA7tP4fNibHqqNBNSYCuQjR1eE8LNmkjQHg4J\r\nzkrwAPvnFfXCLc2DJZTFVPP0gmsgCqy/Y1JzHuYPwyWsIgIdlKPYlW5RRXvc\r\nRqQ8GwPP6itICqJLdKjFMxlrMg6RkAjNPomM//XRX0ZsDqIpZ8tTTA3BCqk6\r\nX0K5r9MXMc9UyWGBZTqyJzCtkiZEsHL6wgWlk7Hdpdj8o56AS+ebkqqBaXaQ\r\n6KLmab5tpqh/FihGSOj4caCYszIIdzF1OWzZAvnffXruX5Ok+3A+NH1dx8Pa\r\nVQP4Ls63rW5qhNdp5Bavnbeb9JVLQF+8zWUndl+xdD/KpRKve5EGsw9I5HaM\r\nJStjj+S+gOFXyppom63MGVkmuXHMRZr+xFHW3ptCXttP/uKzp6s33Np7tmWW\r\nmXOKdpxz46lasvm3LKNazzAKrwvsnRlppw4Imsx+e5xfyCCqsHlScRHUU2uW\r\nWSNXED6sLqEFJAqX6DTCO9paXusayJOd6qAljQ5ylk7PJJUc8svfdMOOxRj0\r\nL4BB54zi4/Eq/calua8aPwfuaK8ws6LHtLt1fE9Kx8bJTfeajmhWFTSahYsP\r\np9SEzWVmnS1/4H0jtfpOpkgqNDcdN8PNKaavRNtt2fEYfxKiy0m5k4jtWSKq\r\nhBfQ45xGCGq5+gQ5rZCwWYtuNfZdXyfm/wA=\r\n=GA6h\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-alpha.4": {"name": "redux", "version": "5.0.0-alpha.4", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "^6.7.0", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-iqGowIXvDHEUze3XLKjiAbKSNF0lkQr1WVEQDIH+6SAK88VfdMEWM/4bZhI6WGmjC6NKG8KCBZ0fqPD38v/0Xw==", "shasum": "9c592ed2f3b46ea4ac2557c81e39b5b1d28c241a", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.4.tgz", "fileCount": 27, "unpackedSize": 293084, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAgNWdyemwgBd+ThIWmeW0klM1FiDtCyR3OpGu7K2fBAIhAJCJtmmWiI1Ef7jg7mMwfK56qG/RXBeniz3rIJ5PesO0"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKkVFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkyQ//bR9JHToJPe4mHLddelFR/PgtBjItE+cYY+KN8OW1I26hHno7\r\nQ3uk2VYYZ4lOQov0RSkDREkHj2pPwNukeM7P8aMRrdr/6dGtRfEnhULYQ/dc\r\nlHRH8eEDnin1KfiWD+fahcikHMZ0b64ywBX/dlSZRb4ikEF2CG3njmYTWxSv\r\nPi2PnrGUpNQTk6cKarQpLI+T6Vf26P1c4h+G9437mB1cwJcfpH7HpxP6Rjp+\r\nZn82MMeuPrbtO7mYLAwMw2FQDL2JVXhH7B/CIBfNteLTygG6YHRwrZ1qW3UL\r\nhDOySzGlx6CqTFvFNqWsoA+7TCvTlfJooSxgArF3rLMvqUxscyXoTyTQAkCh\r\ni134tsShLJihIVOF/PNDpbvZF3mJB4qrnwvob3ZgZylHMAyFqeJCWZ5smvAk\r\nHz0bG3+tQauF0jMYfTZYbFFjCgttUkMGGSKgF10Kyg4ORY4VwTOpkvh76pDo\r\nlTCDaR9to9dyy/w2lsG5EjspkFGlUwXAfBY7YFonKw4awwBzX8OW2eB5ci3e\r\nZH2g701+u9cmXgWg/vIvw7XIvVRoKXpgNWpaj8799lI3/krCH7GPBoiNEZfA\r\nuEIIBWmMdPvxu/XWA8odzjcY02C1LmV/tFx6crzjtoqJc+3HTbtJXXazDL45\r\nuoUW15QlHq99x1vQl3m5eJAAQfLCCzMO7kU=\r\n=O+Ij\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-alpha.5": {"name": "redux", "version": "5.0.0-alpha.5", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "^6.7.0", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-PIdtgTJ5LrldU/BD5/z6RYzri0iFpdDUphKWOFCP92XHmnnO0CKydtwYQZm3ptuaEduEne9248G3xwprbD/daA==", "shasum": "1f7abfa7b5f704514a2940a93c49fc654d29ec62", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.5.tgz", "fileCount": 27, "unpackedSize": 291912, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmMsMM5DhFIUi07Kg6Elcun7S2BZTar0cXMysG8mk1lAIhAPC+eeBSvqBDAZ91Kq26eqMM1NNnPop2oIZ9zSiPsCL6"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPE4UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIYA//S4xClmh+KSWPzOPbG6SjwJeHvu/j8Czs+pP4cR0rJERKYpXS\r\n0j0mOPu9szfztDiBGKUMKAn85UXdCDiB/oyLRN1YDtslYmrUMjFpDXjdp0jf\r\nOtoiuCvkQhDcAKFIKx/xYviZqTnFwvCg1dcwI40eAzJc0jSlN0c6ysHGENDO\r\nJd7aRY6QAE7O3KnwgjNKKJ+JiHT8rLPOIDGfuXGvMj555qRRLXQC+5LLG+Ew\r\nm3lG53/7CciukFLjXFGYhCuKsLZ4omn3gstaT2oIjEKfJX2jDLqmWoT6KHzw\r\nwhffD3eOdxbqMm/K0ThiUvutHKcVxlkKuuu7TRWAWs89C1oAmwLv/N9DalhX\r\nDfqf3gdyndY9ZXEbh1M8loHuqN1PCRtY1UCfAOUNh4jQmBdd7xxW7osQfyT7\r\nt/JnQ+qfkvFvtEfeyfUgNj043/dewvdDnx9cXcjl9a4kpk5bm4RQabXGGdul\r\n9ujWwMQrWpL9zOjhwbfQBv3zT4zQt9vxvFgtKdMv1jZ6GYPqN9KqHO3ILbgL\r\nKw0mpscm5TJs+0cNhHt4c+lpKwyrOwwnt7iEpO54dN9/AJuNVSTkWcg1qLoF\r\n3xCWhJ7q0rhSlKBYtg6HMiFJbWzH84RTDAmu2zkz3IqVlS8+IrlLKEjFvW+k\r\nEN9I13trcoLRtX5WjhkDM5DXPhMEbcsjam8=\r\n=LxYO\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-alpha.6": {"name": "redux", "version": "5.0.0-alpha.6", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "^6.7.0", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-ALwfRz1WAqQb1N2IrAopQNNTjQ8UlEn0u8x7REC5p9rQw8j+17rKfKtJs+UGBweLwDk0RGK0lVz+vZpkRtRUiw==", "shasum": "2b06dc26c0268340f22933fa56185f3941764264", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-alpha.6.tgz", "fileCount": 27, "unpackedSize": 294571, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHExs2BxKhwdyG0h0ffC6D4V/21nJcNoRC0W0iu8x8oAIhAKcKafDuesBWwGqMg63pleoHzSxnX2yxXdXmwsKRxd+E"}]}}, "5.0.0-beta.0": {"name": "redux", "version": "5.0.0-beta.0", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "^6.7.0", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-RHSGHIiJ+1nkuve0daeveubiEdloy+DkYkP63uHk2FHpP18kb5umytsPU8TY8Lw8sLjL1eFg0DD5yf99ry/JhA==", "shasum": "166684e56effbab9b4f6e2a1a79c6c21ee983f01", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-beta.0.tgz", "fileCount": 27, "unpackedSize": 295415, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvYXuQOmQBTW2BbAZd821OLoTvAqXAWsdp25Zx2kWkTQIhAIW3DsN9pzC+ku0rVv3AiVakwZ6whcpcmsQ1qSPq7Koo"}]}}, "5.0.0-rc.0": {"name": "redux", "version": "5.0.0-rc.0", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "^6.7.0", "typescript": "^4.8.3", "vitest": "^0.27.2"}, "dist": {"integrity": "sha512-QBfAlLf1FPQLbsrkTcfWdCijzN284/fQEiAkjS/FTlq244fSBkrh0mDsMQrCIfxvRr2tOC3NQvNUJjYV+izUcA==", "shasum": "b80b206603dc3d76e8a15ac424d16d7363f8abe2", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-rc.0.tgz", "fileCount": 27, "unpackedSize": 295413, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBEeUfgnZWcQR4AjewXYHIK9a0i6ZX2IGHTr3z9u+Jk8AiAiKh0NUA7ekRVP8dWYGPjAJXcoFnZn7VqqBIWOrDDZlQ=="}]}}, "5.0.0-rc.1": {"name": "redux", "version": "5.0.0-rc.1", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^6", "@typescript-eslint/parser": "^6", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "7.0.0", "typescript": "5.2", "vitest": "^0.34.0"}, "dist": {"integrity": "sha512-/MWJ9K9v/r1jAzYw9MJIP2rA2RvmdI3KJ3s4ZwCzeEtFGcVaIWInS0xuFF490nRe35sRJTqfXWtkV0UxC8Pv+w==", "shasum": "794eab0f4e0502ad7ebc7c2e46066ac297fa53dd", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0-rc.1.tgz", "fileCount": 28, "unpackedSize": 298056, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEuQoIPLB/BFviJs7J0MFV7hvUnntyaNLN15BxOedyO6AiEA6D0K4MlGZBVSsDGggagkf8tmYhfibTDjMi05m3uf8DU="}]}}, "5.0.0": {"name": "redux", "version": "5.0.0", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^6", "@typescript-eslint/parser": "^6", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "7.0.0", "typescript": "5.2", "vitest": "^0.34.0"}, "dist": {"integrity": "sha512-blLIYmYetpZMET6Q6uCY7Jtl/Im5OBldy+vNPauA8vvsdqyt66oep4EUpAMWNHauTC6xa9JuRPhRB72rY82QGA==", "shasum": "29572e29a439e094ff8fec46883fc45053f6736d", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.0.tgz", "fileCount": 28, "unpackedSize": 288560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG4QmdLRQdfZy/3H/FRhKF2gvFsSe2wHspeOCAHSyMY1AiBfL8J67QjRgurnk/K67B9NPpfKcFHRaDQKfSUWlOD8pQ=="}]}}, "5.0.1": {"name": "redux", "version": "5.0.1", "devDependencies": {"@babel/core": "^7.19.0", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^6", "@typescript-eslint/parser": "^6", "cross-env": "^7.0.3", "esbuild-extra": "^0.1.3", "eslint": "^8.23.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^8.0.3", "netlify-plugin-cache": "^1.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "tsup": "7.0.0", "typescript": "5.2", "vitest": "^0.34.0"}, "dist": {"integrity": "sha512-M9/ELqF6fy8FwmkpnF0S3YKOqMyoWJ4+CS5Efg2ct3oY9daQvd/Pc71FpGZsVsbl3Cpb+IIcjBDUnnyBdQbq4w==", "shasum": "97fa26881ce5746500125585d5642c77b6e9447b", "tarball": "https://registry.npmjs.org/redux/-/redux-5.0.1.tgz", "fileCount": 28, "unpackedSize": 289804, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/redux@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDN6CQPvxOJZK1LYwjTIYc5iOmuRxpfMhN7ToTDvSYTLAIhAN88EJoSQzmIm8vI99Lw8BcjQ6IB1H5B4HEZ4OvNWoCb"}]}}}, "modified": "2024-05-06T13:38:57.062Z"}