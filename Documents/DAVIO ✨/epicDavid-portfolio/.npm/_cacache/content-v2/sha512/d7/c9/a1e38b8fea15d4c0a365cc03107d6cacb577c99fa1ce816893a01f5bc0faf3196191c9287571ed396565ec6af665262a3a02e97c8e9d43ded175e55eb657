{"_id": "es-define-property", "_rev": "1-a657896e70009de2081c1ca06b9a0f64", "name": "es-define-property", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "es-define-property", "version": "1.0.0", "keywords": ["javascript", "ecmascript", "object", "define", "property", "defineProperty", "Object.defineProperty"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-define-property@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/es-define-property#readme", "bugs": {"url": "https://github.com/ljharb/es-define-property/issues"}, "dist": {"shasum": "c7faefbdff8b2696cf5f46921edfb77cc4ba3845", "tarball": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz", "fileCount": 11, "integrity": "sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==", "signatures": [{"sig": "MEUCIQDj6k58wRMbGIEmCLfbvWNTxaf4Mtm8yFaNyA4e7Yzy4gIgEKNrP5cNIdmzzm8kH38WYHU3V7yniFzrvY4UZw3BosM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11798}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "0bf3fdce5a39949109f9ffcc81cb60d16317129a", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/es-define-property.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "`Object.defineProperty`, but not IE 8's broken one.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"get-intrinsic": "^1.2.4"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "gopd": "^1.0.1", "tape": "^5.7.4", "eslint": "^8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/gopd": "^1.0.3", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.2", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-define-property_1.0.0_1707791514214_0.019314970069257864", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "es-define-property", "version": "1.0.1", "description": "`Object.defineProperty`, but not IE 8's broken one.", "main": "index.js", "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-define-property.git"}, "keywords": ["javascript", "ecmascript", "object", "define", "property", "defineProperty", "Object.defineProperty"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-define-property/issues"}, "homepage": "https://github.com/ljharb/es-define-property#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/gopd": "^1.0.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "^8.8.0", "evalmd": "^0.0.19", "gopd": "^1.2.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "es-define-property@1.0.1", "gitHead": "50ef129225ae17336a774f0eefc4e6bc88c79b8e", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "shasum": "983eb2f9a6724e9303f61addf011c72e09e0b0fa", "tarball": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "fileCount": 11, "unpackedSize": 10217, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH/pBSmlamyOCozp2OeyVsrUaXZg1RhKL84mx3n4cs/QAiBTY5GypLDSdVwrhJ90CfLKo8K7MzG9eojcR4wPRWj2IQ=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-define-property_1.0.1_1733508961978_0.6145947741890689"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-02-13T02:31:54.213Z", "modified": "2024-12-06T18:16:02.393Z", "1.0.0": "2024-02-13T02:31:54.424Z", "1.0.1": "2024-12-06T18:16:02.148Z"}, "bugs": {"url": "https://github.com/ljharb/es-define-property/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/es-define-property#readme", "keywords": ["javascript", "ecmascript", "object", "define", "property", "defineProperty", "Object.defineProperty"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-define-property.git"}, "description": "`Object.defineProperty`, but not IE 8's broken one.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# es-define-property <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n`Object.defineProperty`, but not IE 8's broken one.\n\n## Example\n\n```js\nconst assert = require('assert');\n\nconst $defineProperty = require('es-define-property');\n\nif ($defineProperty) {\n    assert.equal($defineProperty, Object.defineProperty);\n} else if (Object.defineProperty) {\n    assert.equal($defineProperty, false, 'this is IE 8');\n} else {\n    assert.equal($defineProperty, false, 'this is an ES3 engine');\n}\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/es-define-property\n[npm-version-svg]: https://versionbadg.es/ljharb/es-define-property.svg\n[deps-svg]: https://david-dm.org/ljharb/es-define-property.svg\n[deps-url]: https://david-dm.org/ljharb/es-define-property\n[dev-deps-svg]: https://david-dm.org/ljharb/es-define-property/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/es-define-property#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/es-define-property.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/es-define-property.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-define-property.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=es-define-property\n[codecov-image]: https://codecov.io/gh/ljharb/es-define-property/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/es-define-property/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/es-define-property\n[actions-url]: https://github.com/ljharb/es-define-property/actions\n", "readmeFilename": "README.md"}