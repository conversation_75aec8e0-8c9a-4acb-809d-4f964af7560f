{"_id": "@babel/plugin-transform-exponentiation-operator", "_rev": "114-bd9f1e0835b34aeef323c58e201f1de4", "name": "@babel/plugin-transform-exponentiation-operator", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ac08f28cb2be2cd9cf292ba61f1b468dd839635f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.4.tgz", "integrity": "sha512-x8Aqtag0JLMyi7dnyUL+rsrwVE5cSz2rXKHfrFELXj2N9rWGkBiIFX58TNOEGu8zPwhuYPiJHqAtj60PXlB/sQ==", "signatures": [{"sig": "MEYCIQDLVsQIZVxxHtQQq3j/SnrrXDIxpyfia4cogkfMpmi9RgIhAN4u6CfW0FKJmINo+93JFUmRqRT0nZKKKf9u5IBJD6jA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.4.tgz_1509388592019_0.9527770688291639", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "037d37fd41eba27b090e7792fbb0957980c2416d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.5.tgz", "integrity": "sha512-Rw5kW1ipy0wKx1z1phiPoCcsNXIA7fYhhkxNFnECKsmteyyjHRxk7uEgFqHMcL4EHcayLO4XdsJoO5p0nuAxww==", "signatures": [{"sig": "MEUCIDspJ5DkRcSJYoa9bluP3CB565eoCkJ/AgUu3MW4BSPgAiEAglbh+c5GckX+MigR9ECXYpz8i+luLFiE/JPjyPIr55E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.5.tgz_1509397089674_0.0937352585606277", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2b6090f2138e180acb3246dc1a13dd4a78600383", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.31.tgz", "integrity": "sha512-Vh27vPuGJCINeQXGg6MMrrccBy6/EqfVB9Qmp+8jXpX28fmQoJm+Su1uIeXhNPclVqdXdphpSp0ViIfc0AeR6A==", "signatures": [{"sig": "MEYCIQCPXDIFAA668Y33akyWKJ1+5GNdBELRnU/hRy8xS7NCWwIhANY4tugfltJ1xQdWeO0yqsfbuJAvljJRCSyu0uCV2EgL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.31.tgz_1509739478701_0.5357420609798282", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e7d6c516d783b6479251b8b12020667f1d0b06f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.32.tgz", "integrity": "sha512-Xvyx5BdBL2Y477bDPjmyb4eQoMxoqwW2g2hlwssVfHnvLP0JqM3tMB0RNfAE2XjkIMoBNVUSwVVCbH8XFWEWxg==", "signatures": [{"sig": "MEYCIQD0QYmI3BT0iiDHnwRK1Y/Of6Oq3AkhwxIdhADSG005qwIhAI+vpWycJEDZ88Tin1XjWNSLin5upl3vhaosIiMGoMsk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.32.tgz_1510493650948_0.22562264511361718", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "67e58864e80cb47961613adc02a5c58107f592eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.33.tgz", "integrity": "sha512-jUcnbxugZG6YnGFEfyOrllzfsgtBZcS682ZpdjhSs4ljq871vpmalogxEBxNgF8O0kXdnZsNMIVP6S1qwWx+Fw==", "signatures": [{"sig": "MEUCIQCeDrEtMTLIFz+iR0CPUxGYjIt/A51vHevkJEAhZc27PAIgBGLjOekZYcPtse5x7Jpvplkk8tQS3Kq95E0caBN4004=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.33.tgz_1512138569423_0.38479531882330775", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7fa68b8273695e87cb528e47ab17926ba0104a83", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.34.tgz", "integrity": "sha512-RibodTBl0AywXHMEhI0UUywlLiWiP42ACvun4+QOSkOQRMj+PKkyNbC/Nc7bgE8vgih+LrnzJ0KXI3lvHs6q8A==", "signatures": [{"sig": "MEQCIChZf1nddhfR9ADxWamWEaZ+7FHWnCa6M44pP/y+5EbvAiBDYhjD9oIYD8iYIsJg9aNEPntU4aTMQKdBqvxt7w5YFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.34.tgz_1512225628560_0.7958857379853725", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a5470fad798a7052596e19fefbb6f391b17ac6e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.35.tgz", "integrity": "sha512-EC1kqX9stnSzfC8pUxjJZRkeOY4dPtDQtDQBhQVwJZG1NkinJPVlzs0/r0kCQUtBfpmmCvZwKYmsnv02wvqQmA==", "signatures": [{"sig": "MEQCIHIr9O5XneIXQHwDcTRU62PKNwjb7Im4sucSiCuhPhdAAiBtkPAipZXILKi5dUeGSexLKeYmKmVCYm0/nkBoyByB+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.35.tgz_1513288114492_0.30010547186248004", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3c61b7412a38a8718f554f6a31e655eee9c2d182", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.36.tgz", "integrity": "sha512-Xw80I0Jpf8Rv++hTEetvVKcUE2+nZMJd1zEbcioDcdkLy6pr1GEkUcB1GAvKu7TSDOWis4niHaDDnRpzIs/p5w==", "signatures": [{"sig": "MEQCIGebx4unTsirmHCeyI8/6BwfGEtgpfQ0+vUBP7kQNEbjAiAIMhG02lKILbBcYLVB7lssXCMCEnfTyQtVnoknBbgm9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.36.tgz_1514228752478_0.820149339735508", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "dd654935596337ddf7982f0461bebbab7fc963fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.37.tgz", "integrity": "sha512-Q45cpfk84OCCrd8dqdisb4VmfpU6YnT2WGU1FQewjhvmJ3bKieiUUq6fIadZ5GKSgUu1+W8P4rLyLxuFYYsG0g==", "signatures": [{"sig": "MEUCIDbW0Ez6bA3UFYLwqPO88IYXBV8riMRV58nQLe4idgCfAiEA/jKmMPJ4PO8MVVohozm0MP528JE/xY5z1cfbxgzuAQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.37.tgz_1515427429508_0.6464445046149194", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3c23f57a6f8d50cfcd13270269bb35de21aad666", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.38.tgz", "integrity": "sha512-6FyrtyZswMw9rhf25hXoZEGWhuleB8zWdzU7kmgh1Pztm/Xv4tVBUHMevbOZIt3KCJQucpwhb/T58FrZ/mPwcg==", "signatures": [{"sig": "MEUCIQDI+PYcc1wCa3ZTWL9fcP8Z6DaX3KPZPpT8DNfYYT5IwgIgEH4Enrh4aLfSxilxTDDj2QIDxdFBW4UEMcxUV9ZMWFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.38.tgz_1516206769553_0.9410925533156842", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d0115ee85e3d895ead38c6fd149df223bde6e069", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.39.tgz", "integrity": "sha512-GBEipvew7Avp5tQz4bQMWe9MTOQn9MRxFGPoVtklxCyV1Ghajf5S4452/kSledaBtzMH3CRqzxs3DlQ200Wm3w==", "signatures": [{"sig": "MEQCIClxduEusTIp1zuRIOvfigh18VoJrB6ayV5HTq3utBV3AiBT94Qb9OzyJ/o6Va79DL5R/jieSRzyrT2zdqzrAXZiEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator-7.0.0-beta.39.tgz_1517344131202_0.26467803586274385", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bf0bafdd5aad7061c25dba25e29e12329838baeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-nh9qIA4P1wQczihazVOvTpkl2EBfoSMfkM6/21p8NBY4GxZJcEwT1O1nke/+RLludUekHqXHGH+9ekfEfLwKRQ==", "signatures": [{"sig": "MEUCIHaWL5X76lNbF/Wyl9PRHxDu+CgHNRqfnODzt9auQy9OAiEAzHIy3ygYKRi0novRkpkehhvm2V3RoBd7htiH/hrb7dY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2330}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.40_1518453780080_0.7543393487621635", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "325fa6e2d53c9ee4e6757853c693a93a54ce0cdc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-5an3gs8E+AojQ1/x1hL4spCkmv2+r2PU32tSSnayQJkc6MWfkxpzCkIbleuvwx3Vapv4moTFKNYgkuSLLdUKNg==", "signatures": [{"sig": "MEUCIQDKPwjbwOfEsozrg7e0oWvZlRjAc4KtLnQ80sa51AWTYAIgQsIlYbqA8EQAo98KmERGCzlnP5LWJIMyKjy4mvu6miQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.41_1521044829551_0.9213638665170247", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fe637583e8d00ff6d63461e274a63dd2f373baf5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-ylnAziSEho282hxQROK5tbNtxm0sSoQaQS74OCSE52eFeDNqSNGapyiNiWMZ71gqs8q5aqYsPgfa/vTccLT7uA==", "signatures": [{"sig": "MEUCIQDAgkQPrBHotqG0Ajwlt9hvoo6Ver17pDoEHjlmVuJ1CAIgWVne9vZDePC1o8hhQJaCw7hrwumaIqjdqOcgV1QUhX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.42_1521147139548_0.26186250165455993", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "52f1710bcb61c6fffa82b8c9ddb06604f6bad8b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-Yl7I0AdmqMk9p6qQJSmlB7AWtWB32CWkzP/SF3AgpqnFyaqUjJ8nfU+05WsLorflk5mA8iLP7Os3/JoVyb1BDQ==", "signatures": [{"sig": "MEYCIQDeKrY+bRzpzfz41ackXZflr6yCzCTgWzUvX/7jwiqLIwIhALPSusL3JQGu/fu/AzRjwWSiRD4l/PY9VJCRvcmqjf5z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2883}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.43_1522687746557_0.7487446623484522", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e6a9699b5036a7a75274e1546c23414ba945a135", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-6muSWR3UxdAnVnk8a4rxESNQk7F+djM+oeKkETMgWbw6TyaNaTD7OYkKGTdyTT5jn2UO97sPdgOBIBdnzQsKQg==", "signatures": [{"sig": "MEQCIGVJ/+9PjGW24RSxDwpkA+Vkj80kDjvb4i+49YOJJ/fFAiBpdBl7X1b/znHgwVfVdWta7JBOv5qKowCB9FqP5ejJRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2967}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.44_1522707646624_0.4306881779000522", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5ada1abf016a596a89b74922fe8a2f98dbacde7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-DUPGvRzZhcQm1rtiPgcBf2Mtdba/HfBQxvcnGTQ8tNa1tmhLfWoIUnUWSsBYpHBjqSUb2XvkyVmjJBxWOQoN6w==", "signatures": [{"sig": "MEUCIH3b8WXyRx3iE4INYQsKKbixal3ZP1aTPwygXWIr1rRiAiEA2EvQwhz2bvXppkJqZ9kAkmY80x0O5lxPtC70k4qWc4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3oCRA9TVsSAnZWagAA63MP/1HA5Ip64+aMafZynXAH\nLgcaNYztVdr8TMTbrVjjL1rWRtTpx/v81XUz+Z2twsp7+u+D8DLy4FgF5OM/\n1jfriiWujmd7iCrnS/zIDxmYbsUy+gvFggHPHl7xzKbUqkc+6yXwUb6wagUA\nypF159YvAolCMYhn2o3AQBbpjnDucwpVbP1xwr15Lti8bXD0Avy12375xHTr\nwySAiwRXQ1AabNm2g3ySYPxgb9AkJeE4N67n7bYj8idl5Pz5GLlriDvCf+Q5\ne5nbjpxWa52v55rp2uCFiDSgF16LxepT7RI3EGvBu1ynh+5mpP8IButTqiZ8\nRoxI5/JTomVBeggoW5RtRc1G1maclnv2PPagKuoXXLO8VvvQfoe0EUtn+uoO\nlmht9p05Y9w0CbJOVRADcDaStaw+DBoruHnrJ9izJduGa3juWibjRySLv6cp\nrDlOq6/73ypfI19OY5r2KCHPc1Io02tujFg3YI75DPxIs8SZSpQIf9O6PPul\nbdRkraAzYd4sSxUdRIVxG7YTJRDHF8VHj33IdELiqpwORlUNfTDxfaZzoFFr\nfmUvDr1gwSX/qvqswACA/ktTj9I9PqQ+f8VEdRJZWLerkgoIE5+XHxrwbIjP\nThlmrJS336kBdr36cbQnWGSpFLdQnkTvW2vje/Y0jHbaF2Ul5MPfXF9U6jXD\nbeHo\r\n=1Vuf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.45_1524448744054_0.21175222706983265", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "95ae2e03456e417d2f5eace6d05a8fccb7af1bcc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-acomgoNW/fwWSmBlhH22C9Eyl1Y/vADBSqzyIRWJGpm4frLhd49QQgKXbRGRHUDxyifXuZDF9+3pRhEmi7/HXA==", "signatures": [{"sig": "MEYCIQDnIHC3kgMkGn3SGDlSdzs5im2Da0EJ++gCVpQA6+/OZwIhAL0ZXOJdt7MWU9XW4HtXo3MKBiOVe2KeKv4XaGhpB9io", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WIOCRA9TVsSAnZWagAAVEMP/RcLXDiwsx9qteQEhSLK\nBC9IShyWTLaU/wasIVnCZS7qSOpUEZEtbdnQLwYjJlL5P85u7kmD7kQAbBZo\nituxK86yofzRFzRBQ3er4LSBBWvcI+hCbj5aW+FoZ68iZqoGzfdiSc1Maym8\nzr5Z7hcgNpvQ4+BWpqGUEYDNBI6kex9W7djREFBiY8o5Y5Ch14cOk2Hzf3UM\nC46hpmtoGArH+51eISYvVJlO4hBzVPFKvfc9ScucKJOAYPLmOI1yOuNF3ICe\nB72UNNEIGag3tEAdynzQfLO2Y3OBx7ORb3IJYMk7uaw3AC3gKoZ4HYfS65Vt\nc9x6tkGyPUY3r8dg4y7oXh+vuCIJZyNZEPv6SZwVlJF6VV0nEZKJVrv9qjzE\nlk+e/U0rlPeE8Oz2MjCXYJEA1mLwHvpkQjOmBD/QbtElbsugjyqr3L8oqpUR\n6+7v98sfwN0T+FGweuH+374ULvTpgrJ2kAtc9M4++E1NEpK+As47XV8f3XSD\n7nWeVZUAgbKbyRaMd50NWAwFcZGUKWDRzpWamzkFM3qXtkjGJA2mZFlLqH3f\nDNtUiAc8JORyGikV0ZCIvVwQRfavG50ayKxa8uE97ihYFeQ+/DPPE6S2nGRT\nuh3gp1gfs6zx7Zv0i0qe+BjoTRAQGxefUxjTlIB/h2XmnwFVuTzu+B84gygJ\nuX6b\r\n=0hND\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.46_1524457998091_0.2191787685943749", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "930e1abf5db9f4db5b63dbf97f3581ad0be1e907", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-vyGG3kLIXpMuaPL485aqowdWFrxCxXtbzMXy9p1QTK5Q/+9UHpK9XoAVJZGknnsm091m0Ss7spo8uHaxbzYVog==", "signatures": [{"sig": "MEUCIGklRWOIe6Yo/bPSiczA1RW7EU9eG4wwQwg/1KqdMsScAiEAvetN8A5oPdgoee1jNok4Gecn0I5bLWu16zauMXMXBKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+idOCRA9TVsSAnZWagAAvV8P/A5dYKJFa9eUVesuPA/S\n8Tv05jckfSW/NC3sBiQ6+G9GDDKYKF3vXyDc0+5q+n08nroRve1DXeeeCcIl\ndGV6tajshcyEG0A4pLMhV92HYhdhHYSPBOzIU1g+DP0xOFPr5h9hiewfOaS+\nV4VJTtAsMkhNV0IHOnBd1k5UFDRgoO/TDtzuybD/QVXTXcrtqXkJ8n+hzM/3\nlnhaIZMu/eJ+aiCrjieI2ou4ujcFE9JK1W3o9t4yBPdpXFKPFjx+A5BrbHjC\n1krdVVGBDoRv+ip+IuyC5pYCwBG2QYshCRuVzxNBcanliI7uN7VspubYPwpN\nXSEZKxmjptdFpES1TtGd6PRk79GZoofgjalRqp1aemEHuIelgFha0bLE6TXE\n8hhv0733/IxT6CLY8DYAIpPcg1cy6tFRGq7zajFsBxgeg6brjgOUtpp/DAT4\n8mb/Rztb5neYdchy94hM7I4UlV/ReTGBKwMcaVhqScQFm+EDhtZcs6aCnfxI\nDE+ux4bMveI8/dlDzSgAPf1ovH9MXO6f/sYO9hm2YbqzBe2NyfAUarQS0xPG\niiUq7tMbujvwtwpldh3RtanrU90f0JboDGjAGf8llyMhbDeAQa+InbUpPh8E\nP75FMYngrA5hJkmnMjKTydfR7IFOxbXMPBWJuVTnpk+mCwrGOKvbQMjierh3\nQkel\r\n=+pmm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.47_1526343501492_0.4371888994018793", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b7cb8bd3ee3186d5b271be492d55847e220ba93b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-oyUXYa3WMzVTtJIp2cjDBoFME8DRlxW1yBZVOdtI0MDLIkOrZjHMGVwCUblQgT10qfpiMfRYKJTN1t8zx98axw==", "signatures": [{"sig": "MEUCIQDDpdJDZOeFGUSLVPAUUt6uIJvjXjA/aIT4DBIxy3tpcwIgbx5xWVgWzvOn07nJHKClVpZxgeutEDe2/+OZ6+1wck8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGbCRA9TVsSAnZWagAA3jsQAIcMvEILIuVBud5u+Lv5\nG7bQ9ROfy7IR/b9WzjmnxERpYsaMiAXt9vC6AmqwRYGKjqKoaXfjiyquj2ij\nM2mNr4b8lVzVtJAlIMyyrXGW9ypkz/d8XjcKPEhxZYICHdbjOSj9cvd1peAr\nxqwZssc64k3QBI4EWvH7q2WOxAkk3p+VUlPY6P/Iw9v0y+p515qRln2dNO6z\ntZ06rh/O1ZIlYxcoVytupZMAFN1R9gZ5aQ4GBJ0qzjhs2joB13h0FRRPfxN9\nuM+jrhxGOmnxIh9kBpwXpxqwY+/GEvcGF/+BQLgGq1mD5lard2EWtQ3aq2IH\nURXsFsoYZv4v2xKVZ9R/0z4GUTX5cjLEPRrfwsFbcpxUX8ZabHObmc4Rb6D4\nmyx2aDTyfYG1Y8PGdmDe2BLe4YOGzF3K4LX+2GBI9DMdppHDpwu7p6+7Yedq\n4hB1cPrj62xy9PCbzF12c14lZ4+NFINZG5fZfrCKBBwi5wRSRWxX1XVA6gtu\nfsXNAcjZki6dBaqKwABwLlzd5SclKTT7w1Pr9mKH7oGL/eGDcMDkuTH6G3n+\n8Wv9HK/CMMMLnWLBjw+EnLoiSSKd2cRU1B/H/0qpN8UZjDMT3Z0gSzAAys9Y\n/wCBzpXylFXsidKN7ADcf7a4SaaDBxmuc1xpIl0Owr3KiXKP5ZMMe1QAs07Q\nfpfK\r\n=5K5+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.48_1527189914827_0.4707560653263776", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "457b2d09004794684aa6e1b04015080b80a08a14", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-S<PERSON>KSEIhfiszZh5iWPIB+PXGTUWTusi1A+4DMcOsKDuBm+9uT4eQklNJwvBgor6P7jRnEL8OvHt2TPdDf/RTKQ==", "signatures": [{"sig": "MEQCIBLydwbvQCagLw5wcNIqB1m3hdRrkYsIxI7C3qh7+sypAiB8NIQOGGm6dq/KnOR+dHoNsZs2Wl66d+riAq4UF+3wKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQ3CRA9TVsSAnZWagAAchIP/1HKW7LeOx5fkY7a9V2Q\nmRHHP0LYKhvSPp/4ZwLVs0ar/YzunwsJIfyDK0qCeyuhAaDlA/ppYN0RtuoV\nvY57WXSeceTvqMz/ptgH34na5EChVA4VV21a5RVq0C1c7Q1E8GXTrh4lk5F5\n5b5BN5QA/MUu1wdIdssbcqVys3ICDACUwTzamsw7al+a6D0P7cTg37YyT1Sr\nYvEnBYbSEJV4wAD084CfjKySPP0XqSmKUoTTBjbS/TmFwtPB+RxDl8kaXe3k\nGgCA3pDnWL47g6ZjX5H6UK9tA1YM/hhUhsF7gvvvniltfg19IiC5ifA9zv48\nCnJ6fYZ9W3+6tIF+T38si9q0L1BPSOi0eslFRuIylPohA7WzJdQNBjwTODTx\nVGDFDRhDylTHKZLMqsj7PZxEVYVxu3ZJxXJxjez+VrapuQXjI4cxG0DJ2uiF\nlf0MfhLYRP6ZhoB5N0JGk5JbBFNWQEKejU2xxdESKRhcy3eDSiJn1ojhuVVw\nt75/Jq9BdqMkYSdfXEJv1isN4NY+XyKg0Dmxri2uswtJ4Qr58LSfsuqTr8aZ\nV1RiowNaDCOcYC7QpTL9qC1T2COkv3tmPxPSelKexjV860cMCReP0YyAtOXh\nn4esoSWj79XDJxisC6dL+K4mwbHg/iHzFRO+pG9LW7wjGxjiKEEX9iQnzZX5\nOSWO\r\n=PItF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "457b2d09004794684aa6e1b04015080b80a08a14", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.49_1527264311423_0.630009744010992", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7a7b619cf3bfe0517a9f1b54700fe563fac229f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-WnF1zkNwXM/0Y5IwsUPhGRdGqFj3BVh1FlESSgM+pETVK5rFDcLzQct6cdINhhHri8ne7LItjb+ByRNuiGli1w==", "signatures": [{"sig": "MEQCIFoStPTy883QWG7GmZbsbAPytIMwDpHATizIApCeEgugAiBZMAJ+3RDEFPQJQRybgzqFcOhyijlUoIoQPiDC57bh1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2357}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.50_1528832903534_0.2910488482491522", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "04b4e3e40b3701112dd6eda39625132757881fd4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-WVFA1KteDd8P83zVoaQI95a9pEw+GoX08nk+XGD9ov9KXekTihETXcvgWfxdODyM3Y003L4ABmqHuVXvNY8pgA==", "signatures": [{"sig": "MEUCIQDvGL69qrT3eUFZEnuwAoNx/aYVpMXeJ+ObXqSCQblzRwIgBRuiAKFL5kC0Z3iOu0S5VF5kL4VJ6pgV2r75mImBs1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2371}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.51_1528838466701_0.9420939651734688", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e65ca848b586bf4d2b2fd184ab75383fb5567277", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-a4f5y6rjBY/JmD2NXixdELy4N1LZluLEzT727G14Sm92gel3aECi9VRRJ73g+Y61XtY9OHYt6uTUwKfTdzDHhQ==", "signatures": [{"sig": "MEQCIGvdtNgfgY6TO8DxiOhmVb+Hl4qq1IhAvfkmVbDzrjIFAiA932OWqwzOCDHCiBQFfLwox6T1OGfNc3FsyKwpFb+Gsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.52_1530838793793_0.8992239830126738", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3e267179204c77519d8417a9b199f252221e8d95", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-iGQPeQc/hE7mrONSCLJ6kI8oab612O0Gpq7HK3162FieMaZ+ZtlxZjw6hS/C4jHxvLTkf1mA1qqtNgjbT7kXbQ==", "signatures": [{"sig": "MEUCIDhWYfDZpz5Q+rG/VV8aaRCk87osZ9siOIkTGPoneTo4AiEApnvNOkwLuAGkITr4vvGbQEuwz0js6WOHfhF2dF0Q9nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.53_1531316457000_0.9525389497561492", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1017096366fb43ebca8ed8d8d0cdd1ebd64febb2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-rm6Jv87nBonpyPFnl4lWFGxX9OJiYUzbt1ll4M6MhSMqSuFsD8mDvk7I8Xih2xQU1sOVtxK3CeFKyQSYBoDOlQ==", "signatures": [{"sig": "MEQCIAvML+r01PF7h5YYEsqoMZq6CKasm1tScQI4YpMGqS+OAiAyVXX2YkUm8p0TMZ7jiTCHm0ARVDazVJAIPmql668SeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.54_1531764034073_0.3439285060913504", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ddcac0ea80e6641681a473a703093cd2f623a59e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-qIZCw3NLB6jDb+dkSyUaoHarBL77y4+nShxnjBqZ1DsVZmKtcIBoZcINsjqL0XAGfu6Ds9fOT7fwZJ+zajhaEw==", "signatures": [{"sig": "MEYCIQDldndsUGVbQDV1FC/u2WQwuJwRAXgS7uZ+q6KeqZfFVAIhANbHYGVMcSxq+cuM8DYbCKtSdT5EGtrcsxZwkAAqY4BI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.55_1532815718547_0.7795857524941208", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "da1c786c70af6249d142771d408dc1abb2fb8871", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-t32wjgnTXwOMDd8cIUSpYu3k31EqrYTJsZf/Dw44RDT6EXJzeTKchMsvwd2n8vf02OzjO0a9/qXF5dKl4cK0HQ==", "signatures": [{"sig": "MEQCICZYXgi3N+dUG5cu7IzykQPYvsezh/EySbhBDnsWvs6uAiBls2J4W9URcDzHInh9iOuUlLe1So+PGoJO3jjoOG/uSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPzICRA9TVsSAnZWagAAGSAP/1RcITcPi+k4vPu1NAlt\nwe5o8Og/Uh8i1ueTIhIpWGDl7IF30DMVzeAMH3ucfjhS3fRyAOWRR7n9QrYx\nvF1aenQ804fhQQXWgIrVX8PjW8P1m8gb9a2c+YuBV5+XqTMDKiPvYMNs1fgg\nR+Zrsr7GTBjvztyHNv7xSAWNkG47FdkkrevFg+ooHmYlxa78q1zfnh5d+4Yy\nFar3cFuNVG5tlsu3XV//iMuseyE3P3q58LvykgQ0hfIv0Dup6ERwD7ibN94K\n6FI6V1aKOZfAatyPOrGtkm9wdwbWiyad2srqR1DPCqA0BYA5OV9Fjxx/B2ir\njy+okM1J1MqGw9l1+iNo/r1PEiCy3qOW/XD1G4Bsb8NHRSUOotZu1VHXzLNx\nVqjkMpYcK4+jM3Az/ihO8ObBDkfDxAtC+UfqOk8XwrFEvtzNgcgTRl2wF2Hi\nmO73CUagU+eWwIH+c2VtUwME427V8UD8njZ/FukKQ9ybHsEMzg1M0IDiuFdY\n7JDY6Z/YSrt+R7TWDdM5WPCHlKhlv7N90AzTzxbnZW79BiQi7nS3BhFjXk3a\nvbj5xGB6K9aOQztfmuQr08flkQEXVQBAX59/wBZyA/cX39Tixrsvbc0UJfdR\nV6JLgIk8IX6QW8Zb7l9p9Y3ecCXOdC62C0+/BAtXsEPp2Ekj9lH+oCk15L1+\nhY6g\r\n=6Dcw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-beta.56_1533344968343_0.031625878960490406", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "997c4ae71e332495a527720ca5c1981a9edef34b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-ijhKRqYinRZ72z969LdvW9+eoLGwVMrt81oc6DISNWIcH5DCsZoLlBM3QrR0PmOr9Fbg+2s2YRyDA9j3Pyr4Nw==", "signatures": [{"sig": "MEUCIQC4sDM8sPwkRqOrohWkV1kIcTO5tHzjqCfLSCqdRA08+QIgF9COY3mVbjxGLxosoof86+1iPVWFc62O+YezP2PzcCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUsCRA9TVsSAnZWagAAk4UP/R2SDkCFfWqicisJxuow\nX7JMzHcpy5BLG5MIxFF8p+XDSQJLpQ7VoYQ4uU3QmtglW2wGBvywAk1rn1yh\nde8Q6LKci0cwxnLWbniYMbjAYkIBO4fPdqPV0XEyTGhC4qV8GePifTEs1dyU\n/RihH/oImnorNiT07O1OEn6yOghOBttJ+Ek/vFGtyTWoZQ1byelAPdQbdNK2\nQfys4scEwrvbO8t0EUFRfI0idFn64wN0XEYtrKM3r4IbdKG38HIlXiI5Uy8S\nRddI7FGOwvT9+qhT5O8NbML2mtTZqR5sO5n5PtD+LIGx17Aq4Qx0gjcoj09A\n6UUrozTHBj+Z2s9ohFwdwDWxvZIyzU45TrvLk6dM3XuDAUkswcpfLRtC3H8o\nGDozIFa56Zip266VK0dQulpgaMM+GTf/Rye1U7JSI9fGr1tBViwuYZj+E1Mw\nWoAhLzAF2r+luImlVUBh0BTrryc76SO9BHWBvBau0hMLJgJsSkK/FW7kO0V8\n4xY5X7qzViP0gcRy7lat4f04utMLMimuRgtNrqAZH+fEtjxWftwwyhHp+95N\nBZciqYBBZlWxYH316nCa15K4RoR31610aWlkKWskHPq+Kuxq18Yr81Unwqy4\nlgBpmb+J9hdPzJWoroSEoEYavVH8fbDzyaUzr9CHyiSt2iOegZw5XAWwiDwt\nV0h7\r\n=VnXF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-rc.0_1533830444463_0.8951718916595579", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b8a7b7862a1e3b14510ad60e496ce5b54c2220d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-5lc0nlX8TPdkHSIX3/3jMtqvvJfzcARcev4qqsaVkXWQ6XNrNnD8ExyTEVgoGhr5Ppz1wA0ymAK8W33uGeKSOg==", "signatures": [{"sig": "MEUCIQCF7fxGIMVqfXXSEr/HSs7mlFtc7MNaS2gtMQ+tsObzrAIgIAF/o9FoB6G9D3fQbNHDOaqI64wdIfJ9iznOySgB9DM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+sCRA9TVsSAnZWagAABF4QAJawHDMtO4TeZK73zI7P\nZVtwt8L1Qx0y3aQzCv619zqCg6w8f/MhVq/Vhw/uQxdTyQICdEjARIO/VCdF\nUsb+RfmH4HyV0F385EAwe5iq4MRel0/QLsfP6e6S6hOrQ9WlzCgxlV8mXKJw\nMDeVUrnFAwLjp1nFapqmY3zXsoUPV+WSFyXtol15xESgbEMfo2gK9OAjt79z\nxa3bmzNpkMGL6RxMi8fo0N3r6FG1dcwy8BcZNR2K0kpVLE12g18DLIuBcgBk\norWmMtmoyebhyT2+LhNiG5tMNkAULsfnG2JJDdYhnAdKoUZ7IawtITx7GmFU\nJuYqei2qBDLs6q4t8W9g6H8BmYFtHoYB2lSpdzkotqg577LadqB0ac9CBJ2t\n2D/rllrcu8DXytemef+C65HdU/2DbZ6M64UFNeZcUHfhtdnYps3ugxt33q1l\nxMu8TB1Zs/+V91SgQJFKevChgg65b7KGjTfxOFLjrswA9R0QBuldM7smICSm\nAkdFxY5dYf1MEUScH+Gt0wtzodBa87eNaWeUwsG6x4Auv/9mF02YPeyBAOaD\nKlYDWWXdAzLGkoQ6hM/8UR69lB0TVE8PvjG0SI/yn6uIdSQ+sVtxFYfD/ZC9\n2eKqidr+TBXwKU72mlvPx98fMtAESYHpKhyQH0uxOQ9OF/SJCBG2T+crNNmC\nrbzn\r\n=nePb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-rc.1_1533845419786_0.3141746417402729", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "171a4b44c5bb8ba9a57190f65f87f8da045d1db3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-VnotPddF7jLAQK6QgBNNwzhPUsxxullq+BEFaDKi1zvXnNGqOcgJ+fmemrdfGUoZWV82Zv2aRehwswPkxPcK2w==", "signatures": [{"sig": "MEQCIDhl7v2eEV/OycBzM4kDA5aml8e1Vf6a5232iHzosh8uAiBja4EioPkWzLJpzVYG78TlQ5tUxpaS4oeN4BT9v3WypA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdiCRA9TVsSAnZWagAArtEP/0IMnrO6yNIC9Y7K4pj5\n5bzsD+LgoZmHclRERv2L6wUI+s+meKHxLpzKpyHLpyKAOUdC0QyW+BdSEBoY\nZgX9gyt62wx/z8QQxT6pawhOnKzxN0JLd73WSCUTILLz+eUw8boJgWOq1BiN\nG77cdsbjXlbjlT0vF8TqUrhq7ONZYgWLACr2jTrf4Iy2/E+OS0eDJD5TQLBm\nSOOhKT/RyC5rn5X4L+B5FPp8enX+0qUAqPHjdJUAxtFPUnCHlq+YNHrxXEBb\n1CiEkB4wDOZOlwyCzRgfSk9CQ7GwlseImq1ljABZjiTRdDRKACVt/tUdr1af\n7QtQtUGvN+G/N49oPU8lTH9AMUD7jXh4Qeix2oOeg4lQTbsb31Wxid5DZT8W\neI2mODvb/GG5FdOq7uhW1ljnG/DlaHgJAcqqM95RjH2Zcs94xRPWUU/E1CEW\n62IX3O1tflY9hu0CsKJBfNdfglIezNBMsHXH+RQIUVTxWHZBzdIVSjJXsRbu\nj+FAs0y+M5ibc+82me+6e+xDMVVBNlg8FGPtqrKxSbvayj8NZkcFR7j2/oWa\nbKllNH5Ftz3epbZ9OBaxjKNz8AP81g3kQLSkl+gEFHW++qwn3KCb4a3Pi/o4\nsYMvPb18/0j98UGYxPaWt9NKAo35vsIIi1tOle589vWpiFi7OW53AlwU0N6X\n0CBr\r\n=+3tM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-rc.2_1534879586066_0.34090790372151036", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "494cc71920f8e31e02f99548c40935cf699f5be5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-eQBweWSXf7wsn03BA7Ksq/f6bWLcPdYM9eiN1bMWWfurdxlmtXoDuW0EB2KDf195W5S2y3O2wZQWRW5Z+xZNEQ==", "signatures": [{"sig": "MEQCIF+M3guRuWAw2QcnBED8dOf/phKUhQ3pbhY2vv6j94f0AiAdr2xGDQMCmSvCMk2nKTwP4wqZudUCG5pkOhFX3Zw1mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEoBCRA9TVsSAnZWagAABO4P/3x3qToGSLntEWanJBKK\nsSjshxs3bFaEMkPBonPfi83ASNWLbD7hId01zOqgQRz8bIicPSQ60FEFiQDy\nebrLUABSpyQyQolms5lehchij4bem10IRmKS2HqrAc77QhW0eHoGnAZ+rZr3\nUB0wCmc22Mv1v8pepDd9TDuRe6FCcdQZTr9pT7w7V19EC1tu05XBGaN6jcie\nCpCYEa09w/KrwdZs7oAfKMi0eRT+4ISoBZWHuUzOjFhY1DYfsyUqn/wfX/Y3\nLRNbMaDE/vTrqwFAv8tJ9/FL/oemE+4g87J7NmD+rBvgDSmQwTVeDe+8RkLt\nH7W37uwoqfeKUMKv2zGmTWupoWwr0ReNPje3sMSdAUrTHfE2EAQXyPspfp0h\nz3cNsW9PXdiyewyJrHwgLEET5bmpnxdPfIStqHhwLR7OVGu1OB6qqnPJsOhe\nq5zMgj4mH2GBxfjdRbUFOjihlOYRYYHpJ8Z9vblg0OoxIKsWAEjJbJ7mrKOA\nEnRhfMYI2W+wPvhdkyEejHHKJmZuhKn2emcw5HX0TewKzVCVbADGd56dZbFH\nkxuMjpGI+o7sQTOLWO+Yj6Sxa5GDafaU8lu+l7T4uET0xYZsN0IOBo+eKu14\n9kr9y5qKX9IgA2t/mLj7NCqdnCpOWCGMpxRzAktLFVltcp0nPqePneKsU/mR\nFWrO\r\n=TBlq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-builder-binary-assignment-operator-visitor": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-rc.3_1535134208836_0.6822656040833361", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f2b06d02b89dbd4a159aa9cdb34f9245e484703c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-cF0UJqqnQ/em09Xu27CbP/Eqh7xvxECoRpMJOaECrzHHH8GmKx2aV6WO8ixctOI4wJ37Cp86esvxpOCXYy789Q==", "signatures": [{"sig": "MEYCIQD8UFaO21XA3omCEb3BeBbu080Ftqu53mOReBKvD+7aLwIhALyOzOuyIlNKXWt+vca7SJT0gMQF1Layeclj/ZpDVPxX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCsKCRA9TVsSAnZWagAAAKwP/jHP+u+279jrq7DA9GVG\ntdH1jtHaAvD7f7bjOg19FrTQ9TmV+yWTV4EwFJjHvuMkdO90Fxi2tiFBJ3J9\nyaK3XjzMODmu8n2dZA4sGhNdb4WiXVnybpbjFQ6YIjetkVFzOmcFmZE1u7bV\nNT89WziFweyVwg22hPl1ZBSm1HeZSNkHd9KNvCYqEDdFX3ErLsopIFD68mld\nvcLbAWGezW8JJ/zf9MQQLV7sxZDc1QjzaJltYxguTQiL1JO7RwEqzlo2t/BN\nWfq5cUSq9cZs2sSUbrQkfAI+I35NJ9DZA7y2G9SEV4R8u5OxnoB4Gln0qhn6\nKOOniv7C95un/vuU3zgtZjBPOBphIX5fw0CMR/xRuArAu8L0IN4m3LGAsmJ8\n9W5e8ACrYgBjXUw245GENpffc8ynnmLxz1qdhqHb0Z5Kac8l+5736eXQqmt8\npdowfIGmH1j2fDlwCxaWkwEhcecu1lmtyTaT90m+zLhz095eOLV2xcyH42mS\n7m5DriHTeo9xymgo7iCOz7qQCyihbsgbLPcleQK7kRAllVsdX7ExFwqUNtx8\n5KWocjbaw2F/4cVxj1sCa/Afq99qeAwKqgWUwVmo1rNQKVlG6JDNED/zgjqj\nhbGJw5/6KpaxjBuV0FXA3NyHX2cZoxXnrmECCpPWB08JWLj890E3oU3kECEr\nY1wH\r\n=jyV6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0-rc.4_1535388425516_0.8526135178555085", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c51b45e090a01876f64d32b5b46c0799c85ea56c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-Ig74elCuFQ0mvHkWUq5qDCNI3qHWlop5w4TcDxdtJiOk8Egqe2uxDRY9XnXGSlmWClClmnixcoYumyvbAuj4dA==", "signatures": [{"sig": "MEUCIE5VY07XzUJ/sn/AxgR78vk7xj8QEZaw8qNZor8940a8AiEAo490k+ZP3rvODG21pjpcGBiFdZkKJbShe/6HR5iy2mc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDyCRA9TVsSAnZWagAAZaEP/iY71OP7Tm+n47ls6swm\nDivdongEcy8RVi2/rLaCbKpAuNJNV/lH0MBuck1f7jNqAPBJGTvfong/bSPo\nUnBWsFEpjvYnVbHnzr4LL5hxnPrCvX3LltmGz8zV1trhZfUo035lbZnixvOv\nE2HQi+/kVTmE4ib5XEo+SRp2XNW8ffAbzviHXFKN3M0d9qIMRLe3BxGKFmq+\nK+pRyXzFFDiqNiITRunZS7i7Irk1lCwXryIGpOghFMm2xnJ4N5c70uik2wdw\nkwGrDXJplce8RcIPZLi2eIhXZ1/va2B5r5RVQAUNSK0AVnrVbrkpUR7Flopz\nmgsu1ET2DndVNPTXUWBw9eGA1/w9IEkpoYGUVZagdQ/jKN3vRvoQ1+3xkrSX\nHA1Ftui1qQLEzIHMhpV+nqsZiOZql9hEYLdcw8HjE+EQxhNMcJQZ/F9Jfput\nDyi3/L3/c7LPkDOw5PsK8orxHD271oPhnc26R0VEl/d/olZf/jUVwmky5x3j\n0/tGXYtwV8Af1K1Jd+TfUq8bMUtA+t7CwabwbyNBnfANNzytsenrXza3XbLM\nCY1HTRfXNiySCfj+HYwGi1VbHYr9aBUPks+OVBTB1FvgpZGepbaAKh+cdG/S\nmT9W1lulAiYb2fICR3seYqH2jmdIXmDrDtL4tKPh52/625SnXLAYP9CwYM31\nFZZK\r\n=ODkW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.0.0_1535406322279_0.7951447063433252", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9c34c2ee7fd77e02779cfa37e403a2e1003ccc73", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-uZt9kD1Pp/JubkukOGQml9tqAeI8NkE98oZnHZ2qHRElmeKCodbTZgOEUtujSCSLhHSBWbzNiFSDIMC4/RBTLQ==", "signatures": [{"sig": "MEQCIDRsXzIm5QY//f0ecr1H90R0Yv1jpT/6fKS480F0Frl7AiABrJHLLapFvt5GeUDvsfRqtxIFUfOl9DczItZy6dtNxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAEiCRA9TVsSAnZWagAAPXUP/2kpDmTwP0vpxYrKq3Vl\nMhQnknd0Lz9m1xxN+0970Dzv+uY976HXJTKKZ6jqpMWuQSIzs9XRxC31cZjU\n7Gombtukrdl4sQd5IZKSb7iTjAm5vm1SHuSZI+txL55YIB02NC2HUqNBQBo5\nZG6kzAnLin5MIdIzg9ByXhFZjqLhbJoMJPmVGovvXihlZIL5K4QQ4yzhnZi7\nafsE6AGgNfPsuM/TfKBTEDSngCySZczGsVPq85sB1k4zgTofU6HhO+G8Cmxo\nH2eps1Pu8C7IClxG/nbCFuc7ItCvLhWvf/Sugd7uJe/870gZVQ6VjcnOC81/\neCxrGYmO6E+o6pjf9HyOgI5rEQxtI2UNplPLonoPY/6UtkS4y4PC9S506i6s\nvYJ4Itpku3j02iCSOvPT52TmhpCDZob+vtdIo5m9AkI0v/LO7Qsro/ijsvhJ\nbzlJ1CzFNeVLT63nfLFTAU/+3v50swXw2L+x4DHmiCgX6aNiLDE/Uhf4iZsZ\ncenrIZcR9Ivf/ew2TjUuOUMkFaviFmGbapzoXMfMSXoFFy9YDai1t0+bgla5\newA7jTrYPD1jI+a/y6j+boEfayFLZ+Dm6ART2qRxdKFS7D8TS3KrhNZLSX+z\nXubS0iMVb0xe1CAXhNO/GdMJOzQ2WQmuoLn3m29MR9OkRr5sOJPBPjqCDYSi\nzHFi\r\n=R+2R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.1.0_1537212706087_0.5551505273343202", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a63868289e5b4007f7054d46491af51435766008", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-umh4hR6N7mu4Elq9GG8TOu9M0bakvlsREEC+ialrQN6ABS4oDQ69qJv1VtR3uxlKMCQMCvzk7vr17RHKcjx68A==", "signatures": [{"sig": "MEYCIQDlK8QV6zPqpggTH0dMxqB4wY85J10sXHizbhAOScibOAIhAN3KXubOobq/NZU09xJt2lWaUEa1AzS+j08HfuPGCeZX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1+CRA9TVsSAnZWagAAEWkP/jQkHSSgl0IiThtx4ryJ\nbYypQeMndpbsfkRMnFBK09ujvfLoikXA1yLBkcpBW8RIkEThKGccQcVCPw9c\n0IDpet3TyM70S6ujas4b5xOLtKMcNQ6P77UlEEM8r68b97uR+bbonVZLMUm/\noxHLhdQ/tYV4gbcV6qbuVc9DGS5d6SspRNgAfSqpz0/USKRL9/FyKWrvD9ET\nuMfBhryNfpVC/sidG9z1ftPlwLHwTifBrEeO+v2mK0laCS6O9axRzwFBTLEr\n5DxomuFc7GfaB3PHTjLpne+eNFlbThkjAT0Fsj5yc9IOPQWIAkZZeuFUu2Aq\nC9tl+4p4GyuYuweyr4p916w677IqoET3P7URNhQ6lPkhz3OoEcTGddnJT1iI\niqeFTEdWircUH7k+euNadEn7F9p4iWgNREcyBUU3Ro8/x33QeN0KYE2EXl1h\naBZNC/mZstlPp3mcQvcTlysUzpp7UimwYc4bao6C08XPsTId/Da4+R4u8Cfu\nx2GYcOLVtxlwj/+0Bikt3nc1xUbqELZGt3x+aqvJNMR4cWvQUaEO55KF7Izk\ngd3B5ly4EFlAx9TLhBfYXJMSvIdP9z4DyTH9JzTbSrrVtUTTZG2pfkryxebz\nELFdrzAc6orNMYB5ln8HJTcer/IGwkdSuFZq9F3uaKmt/omPDNrx8j1TTyCx\njpS2\r\n=fyIO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.2.0_1543863677913_0.7178665000800666", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dd30c0191e3a1ba19bcc7e389bdfddc0729d5db9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-MCqiLfCKm6KEA1dglf6Uqq1ElDIZwFuzz1WH5mTf8k2uQSxEJMbOIEh7IZv7uichr7PMfi5YVSrr1vz+ipp7AQ==", "signatures": [{"sig": "MEUCIEc6cFiwh7yAJEOOsjFm7YJn5Z4C5lg98tHb7wV9uXRAAiEA/bi+cOLfphDs6D1G3PeNFsZi39+JFAdBHIISbnMhKrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBmCRA9TVsSAnZWagAAjsAQAJMjybZXTVdekPh/n0U+\n3q0sgJ6R2i+Tbiqfu/UEJ/rI/AG67ZRp4mJjIgZFPyD8shH75MUOfkl1qCvF\nmlUxRVyyN4c03ZFfldLMHEiN0ntXk3Ur+qHjNtU6kXF+ggv9Zp5EGdD6BFsv\nJDgD185up7pkoHkj8uhQxj7PVVq4bHEpGlBDNGHehb5Bijdlh5kY/gkZ9zKQ\nL8YPYOATsYgR5yj0AHZX7LqidU2B5KupdN4QizUAYjeGHPmoUfnWIXo+PVlz\n+kNjbuY/5CxeU0HS6nOyGYfE54gNzn0RgCSQfgfbUxx8UD/WkuYx9gBCqWxK\nrMLBpNaXQPkR/MnTrUbCL+eby8yhX+PmS1dpP5y8zfWwTKu1VnJBII2/BQzd\nQBBLkun0vSQGbiXYeqgT2AYPhamuCcbLbYtl4et5jPhPuSv2g6VsMM7ZPuHO\nTKbfcsVf34SE6n/Oe1AYGpGg5aLdoWiUWs33YkDMMeO03lkUpaU6u1ZddXSV\num+dlVcPbMxN+ycc7+DFZmS/wFUlCebnKf06D/AYBp8be8MwERncgY523xnZ\nyyCHm9TnCetjUGfQaUTUYu7/vsnJeTUQXH0aioENU7/C+jsI5pi6Vo955Eyd\n6e/fvsOZoD8xiVabm/xJKXRPWt2EoJSGpfSWg3lNm1DUWJ//5hkl8FKEK5Eb\neZs2\r\n=NlDZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.7.4_1574465638355_0.2966710506869903", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "72ddf89e1acfac75482992b8976df62f8ad813c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-vaDgF3gPLzVcoe3UZqnra6FA7O797sZc+UCHPd9eQTI34cPtpCA270LzopIXS3Fhc3UmFrijLmre9mHTmUKVgg==", "signatures": [{"sig": "MEUCIDAvdAss/1gL02sZWtjJAO/WelMXfAcLuSjofv8RPY+dAiEAzHbRaDkRGrBb9JqPi+7bfTN6wu9zOBdY/g4vLJMT8iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWfCRA9TVsSAnZWagAA7XUP/jTq8W/OGNzUnZKqnW6q\n1qjC2hGa5D9/yvik6G6mvEWsZ9InOS5lIiVcIPf9ayEz0yqYG5IAY7U844YK\neMzdZ2tsZBoql1jxHxjQUxsYNqP5I8ONcMlLJxwOGdAaYgcHEn5Jr1ruUk34\njp7tleDYQ0rUL2dTo3Ne9q6SdEbBrwm0AbtDrg25FyHrWR7x0Wc0hljFeQ6D\ng6YoXrzKe6vrTIyryJhAHheHmmF7J1zelYt+hJOAgzY9kUmi//VnhiCW8Noo\nDYzX9PiJ5/kPqvCKikmGdj+dgH4W9n5Z3KURaDVWOnrGT+8X+NeTSNaf+s+x\nSRSFQN38HJGzN8yyeRritbNl7i5BXvWT7N2wb4lvqX4mQWGEXTJejx7KzblK\nTgVuaNpgwSZwkY/s3hQl1rS7FjwI/KAProHnlbIgHCSFywlEQmDRi9oGnEdF\ncUnc/nO6ZEJSeVQCcoQWhYD6c2MGuugvbTQVwA/9cSgk3ta6kIv3snCXOZ89\nTKtnQjHH1swNhcwp616PdCkt423KcYtigMtlddU4PUTU1Vd8Gnnu2KMMiCTY\nadx9UgXJmgsSek1a3HtkKc7piVCm0yK0glqEtEbdYvhgnzmWqy3M3K1Jzc/J\nfBzYa0OinuDylLpGDAoQFR2cNi/6fJQqTT0AnSf8nnDWWD0/kAKmJp9ub66Z\nTcS/\r\n=cpfS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.8.0_1578788254765_0.09021964660422355", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "581a6d7f56970e06bf51560cd64f5e947b70d7b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-zwIpuIymb3ACcInbksHaNcR12S++0MDLKkiqXHl3AzpgdKlFNhog+z/K0+TGW+b0w5pgTq4H6IwV/WhxbGYSjQ==", "signatures": [{"sig": "MEQCIBJjVfOMkQY5sxoiKXVaxOABZDA4N6xXSooW2X8VwgfcAiBTIP4HVHmsiet4piN6QWR2oJDpzLGvSxCUX/0kiVb0qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORGCRA9TVsSAnZWagAAkI0QAJ/5m66dUeMrAyaYiRJa\nBFnAvc0sK/YDyPymqIUBsLZa8gw3CfqHvHsKFmf4I/QowQOq1Ge4KvPj1eIE\nFXL0RFCAFK5Y79FAJmlP8/wdwj9h4g0toIJLGM/uCCo0kjdiJjSBDzA0WYN3\n6A4/0VEAvLQQPuPBIMm6eYZgZKy4B3dGuihAkY3cCVQ4++zlAj9so1A39lo3\n0/gtv1UE/4eJZXrgfZ769QXgp5JH+uLxaFh2BbTfDD2u368y1NjsvzRGVJ0E\nZ4KHoF9feN0mNPI1JCB3WVo4/DfreMG6//YfrpQbnq9gY9RK+29ApoRh0KsE\nPxXqFzxfLAkzstRnoqHm6t3CPDf37cgHf7raTH7lo7kaIrlXTxkwCzPfY4qs\n2uX+6X0NwxTf+HwQAMtsUA2giDJT1coRCRPi921yQaLn6GIVp26dQ/xMJ0Gr\nejnv3i3T54vaFtzIDyHMUPsGn4afn/Vjbq6qvOkbRCTYp65gCsLHgO69Jjco\neGktb1/SWBMad7mpfyjJrPsHki3bpeZpjg3K5bW99gsZMJuTNTuPikJWNNn6\nZ887Za4gKNA1ghmRZ7RGsYmtfKsPL6TiXMfVGg8fEWiZVvKyd2RQWPEbO8e1\ngSkHnlbUbI6gLhczTzgnoiP3CiBZCrMLhq72qMUC4bThmZR6rAe7EaPJrzLO\n2ezX\r\n=qkrZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.8.3_1578951749660_0.643268973552805", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "279c3116756a60dd6e6f5e488ba7957db9c59eb3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-lr/przdAbpEA2BUzRvjXdEDLrArGRRPwbaF9rvayuHRvdQ7lUTTkZnhZrJ4LE2jvgMRFF4f0YuPQ20vhiPYxtA==", "signatures": [{"sig": "MEYCIQCaQCHz2MarMsXRD+jdgbt4JFSCvEPqLwu7t+WCA2//1gIhANq4FA1Gm681Hgc9ijvAjME3HD4xraxYsk1mk1fOs9ev", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTbCRA9TVsSAnZWagAAFaoP+wcaHzK4sKWn/PQfhZcC\npvkI7CTRiijtXC7bJT4x5KYrHCf9DhqyApdfGPsYr4h2jaGDG1cULqaCC2XN\nSef+YQmTUtyN9kdKJJZFktr9Hxt2/vw+TARu71tMb6udTaW2yMwTLQJm22xz\n6dfb182etkm8dFXMYvSJsgFBL1gb8Df+7hBAcDfX95UBVL9wQQINt2+uzrwJ\nD4uiEUa/5HmcXJX+fSuG5YUlNhzUSck9Q5k7TZ/mMr6RSAv45oW/XLaIUbKl\nDlxQD8VTrfBAffkd9xuZGie0FzYnuQRALFjDq1q2v1sg1kFhXJ9HUcBUeT4s\nLji9XmHyjb9+ocmA+hlAAXVa/0OnBaQ2wHgDPeFEKSRQg5lfoFmsfpps1BWS\nwBaMctTJfQwjbrcoLThOjO0n0/HsxcKd5a7s7xYLrpU9T3HzH8RvfExCL9MV\n1JzBoBSa4Kj3jdjRqCUqMDQCy+pyBulbK+igHasPzUIES8MlyCP/A/gfopkr\nfJhm6G/nq1FV1HBe0aKgUSCooKbUvAW7AYCwIiMSuKh2p5FGhJ7nt0eLjgzs\nwVvvL7s7wo8AyMuKrlth87Pj5ZByZjxSnJMsoGyht2WBKs0GzJ65nLfbz+Uc\nhXuPdBowb7nco9mhK9HMJLJpt54W0s/y57pZPnGK4jBtqM7RlqwGF9QWe2hj\nRHt7\r\n=Wecx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.10.1_1590617307331_0.5626883953308728", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5ae338c57f8cf4001bdb35607ae66b92d665af2e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-S5HgLVgkBcRdyQAHbKj+7KyuWx8C6t5oETmUuwz1pt3WTWJhsUV0WIIXuVvfXMxl/QQyHKlSCNNtaIamG8fysw==", "signatures": [{"sig": "MEYCIQCgPTBq/wonzx2twMwPqRGUIHCvlIStf8ulyan7zj7suwIhANtAK1DeHubaltD3BG7GhRVz5xQNvgV36xjZR5saeW36", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpzCRA9TVsSAnZWagAAugkQAI+65ZglLP60zX/Vfrdh\nvHzWeSwOX9xfXzSxkGHRyFWFc8oTKpc9VjuhEegdNogQEwkepuPLjKY+lnee\nc8JePuvjTfFolWsByGTt2CqiyP0S3gIXSDBFb2NG5CkwLwE44ghrleia0b4H\n50xAZpE5kr8w/pE8tcbd+2fs2NULAPEUpE58l+KKWT/vMzuHhd0mjR31wBVa\nN3uXA0Nx6f2ftW0Iho3t860nl56RqVwiK/6KJIk5zK0yh7QJcVsEDhlmXKJr\nhuafxt43s0eRNbzCEjM4Fk2z5KBrdOWQNtsbYkzGiPhuyiywuJwcSWdl+Ukj\n0sMoX2+UemQpAylU215jKo3BDJ9bI4KEYUv3iFbRpcHKSqwaganZbhZnqNgo\nDzqIZqlVNBSSmUzPBBmNtTlgIGo6BkbI21D763UdOGjwVQZ/TOHTC1DarJmz\nJLV8DsUTkadKKYRX5jFyWu6gAZKoovhgulWie67PTuF3+Md2IFM5SjSrgeAO\nn4VJGWnjfIpJi9zFpP6yc7DvP8/w31cB6H5E/G2UKk+ka88GZv+YwUgXBPbo\n8O8FAxSHcF36WCylq3B5z/c3+cqhwe0ofnVHZ7Y6C+3TXX5PgA2elOn6qW1r\nNeKWEnFjt/FBPi7PaS0RBj5qMdkejvucyEMbUIx94jB57B/rDgcyxZ2n9aXl\nbBXS\r\n=hZrR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile exponentiation operator to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.10.4_1593522803315_0.45862752030961995", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "b0f2ed356ba1be1428ecaf128ff8a24f02830ae0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-7tqwy2bv48q+c1EHbXK0Zx3KXd2RVQp6OC7PbwFNt/dPTAV3Lu5sWtWuAj8owr5wqtWnqHfl2/mJlUmqkChKug==", "signatures": [{"sig": "MEYCIQCqPV9FxU7NRK+BIaHkzTQDBSlgiebbepsUj91vheYeNgIhALRsHJOwvUqYwbndOZywvqD5NI4FCXmh9zfNvf/EidNz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/ECRA9TVsSAnZWagAAKUwP/jc2a0DlKp/3Q+QDxWef\nOxYIstz4CAjBHuaGXj5P8zQbyygU0YODJwe8K/shNKGWUmDt/vu2xR4VdXRJ\nxyXn2mVrfG9H+H9sidix2Vr6yHWAjJrQUGsUd1iE5zrzmD6qEXoeE27COZBL\nXrQI+yeGAHLwH76+WGS3HiGHGtWv/00YQrQx3tlBn52MXkstLIlzT/eS6kAn\nvJm9qiKMKP42y/NhJpXzzgEqNbouICtDdi5KlphaZembCQaOPR8WZq1tyQzG\ncPtOCX8cJeIfW1wS4WIuLXNhZLZFNYhYzIFBFLqAYMpJ2RSbuQ47U/vZRGBN\nZ6J/5hUknNMa/3L6GhVBiZJpLRTwMAB5oBvr5bRBXYYSnZYi58MfhmzMNEw+\nW4i+X+vsPVQHlNZRhWKDUpLMaCo5y+KtxXvLNpVtaWIeHvCvjWFFpzByYD00\nz8fiNTXlm8V66M2F0Xvb/PREJfyqXMIQTdh8+1ZiUK/k+ygp3zVg+D2W35zf\n3yiDHYaykLWhNDIWokhlERKkAOT8H1BS7Kgg4uLZLXukXQvOiI1uGAPfoNDp\nzvOHZnlKB3Pl8ej8KlslIXex5/yNuosTlWAq80vBJU53Q03WAV3RlFyOJzFt\nezazFHWhavGAMOau8C06IlgGuY4pyFf82QoEOBFj2x7179eKNSE0wWP6R3pv\nr+Bu\r\n=JasU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.12.1_1602801604397_0.6094560990304532", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "4d52390b9a273e651e4aba6aee49ef40e80cd0a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-fbUelkM1apvqez/yYx1/oICVnGo2KM5s63mhGylrmXUxK/IAXSIf87QIxVfZldWf4QsOafY6vV3bX8aMHSvNrA==", "signatures": [{"sig": "MEYCIQDOHYe69qsjA4Gyhuv05AMfMsVA/wNoBrkqP7Ednb/GLwIhAJwK5UreqFQmQMSPIJmpUoqncmfYNfFmSMQ5rq6DejwY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhHCRA9TVsSAnZWagAAxg8P/11DFkRWRAq7JN6P20c2\nD6OpqZelqNDKYazUo6FN6P8M1aLRLtijDOdQm55ry2IUNFlTC52RcOpfGwIt\nPaA0V5//S998xwmjyQkzJ3FtpIfOCyRKyFLh/JHoTBGIyXv8lmjr/wmkhLCN\nvAEkr+5r/Up3Snkb3arYjzg12Gbbtt3CaFABPdxYMuJ463BwQXpKQJo94jS/\nfLbXQglOnKy9wqQLEObrhJXkxf+j3EeyAVkIPWHLV4ULzmDku+nOiASsUGzU\nwrWiTLDPF+U1+cjenyf5ycAUaKdwg15+d4JVjCA4Sht/6k8Ns4eJe6QM1TYL\nJgdme7gUbtoZEjLqcC9D9lxj63VJuL3BnBvM+kgxDV4/ZIvV+5GUUV5mWsok\nAlq8jTtFy2N+v6LsYkK7nIgYYvOaFZjM/1fcgFD7N6sIJEAVW4tMvQufJXge\nrpUXLRLoKs+Ye4RaztE0gxy9hJjmYNQ430yU+RAbhqYY3l8HU0xlCs3DY1Uk\nE4JtJQSPLiXyKoCFPHdTQt5/dd/uAGQ2OeZrs5hBrbX0G37mnIQ3dA7K7Rk2\nsxo0xo2CnTst3H3W/rwt4cshfNgmeN9kGXdBkKrgmf/0JdixJmng749gSgXq\nFxJSoMWYcpQVkSNp9gYXAbFs9eF8EdJI956bjbLiuK0c4DGCOpHJ7hM93G3X\nRttb\r\n=F1c5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.12.13_1612314694697_0.6205866087485077", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "5154b8dd6a3dfe6d90923d61724bd3deeb90b493", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-jFazJhMBc9D27o9jDnIE5ZErI0R0m7PbKXVq77FFvqFbzvTMuv8jaAwLZ5PviOLSFttqKIW0/wxNSDbjLk0tYA==", "signatures": [{"sig": "MEQCIBIL7liEQxIUdao5toTMM3IRSGZk3fiDlDw+/jQnVri4AiBKgmkP9ne7q2H5jZvC/GfeMycWpWXh2HBRxJIXRMhUxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr3CRA9TVsSAnZWagAA63oP/1MTYRrug2MuQxBbiAhj\n5qFhjO4TXrNDN2u0ByjUVXiKGXUfxhL36iMXR+zaSUVin7syOFH2sqOPRUl3\n3gECNi9DxcwgzlTd+KbFViSn9Cj8DrUBrXBrYJhWJLoFu0Ox+YYKjraahaB9\ncfzPUWFXdGSk1+pobaVZEV+xEGfupVXV9YfJ70+QgWPwNzkLeRC2iXBka5gz\nmJYMuGDtvrC4b161Vcak8FSk605b2x6FmEQsqlxYfOAe2OkJqDgdB4CNVgvM\njT8/XfI92IeCeAHUGp96IwBiXXcFlg4bTINQqNhNiJ6JL5CZkTfG4nJWyI29\nJg+koIm8cx19o7ghyM2AMJaxN+pRAN9Iwvv2cdYk3W+to+YiZwYZoNC5LLs0\nU0viPBWimq17opNY78Ty6dAiodaZ06AcgtD2jG+buPsHeZ8aTCYhIFWhEbKy\n/YChpXNxpHnIP+ZWioFIJpDEW4WBLGOyF82OA3uHcCLOSI4OPK4Byhxe116g\nNEZgudpOP2nfrTYLkRAt2YQEr6hGn2JLGz4gYXa3OWHDLs1l4jBfbdc0HcGE\nn6LqHYuMTUQSibVphGnD3sp6a2v02+L7z2J6YypyFEMTKlT7YWqz/Z2CNDS6\n5XqHzjoE4QLAo8eBNonkgCwBC99H2WBBFsPQAvFXWLOwG4UDvAQvq4TdNwun\nftQL\r\n=MHBm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.14.5_1623280375297_0.8957360650393305", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "a180cd2881e3533cef9d3901e48dad0fbeff4be4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-OwYEvzFI38hXklsrbNivzpO3fh87skzx8Pnqi4LoSYeav0xHlueSoCJrSgTPfnbyzopo5b3YVAJkFIcUpK2wsw==", "signatures": [{"sig": "MEYCIQDpLfWq2CWwnFaAYqj5vjPfdU3MKmHudC72Vck2OZhmSgIhAJMd1bvF7f+wJc+KoaQadCS0EHEkYs+j+yI60dRes7aM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.16.0_1635551270707_0.23013835234818036", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "36e261fa1ab643cfaf30eeab38e00ed1a76081e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-12rba2HwemQPa7BLIKCzm1pT2/RuQHtSFHdNl41cFiC6oi4tcrp7gjB07pxQvFpcADojQywSjblQth6gJyE6CA==", "signatures": [{"sig": "MEUCIFrJCIfDnfDo0tWGagiTHi8ihMtBQ8ruiIYcMs0SBJraAiEAwcFJcQr+k32FPp4qpoc7x316qpFgyOp7vDFdFWI1JzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j8CRA9TVsSAnZWagAAEFoP/3jFwdn/QiGAD0YHBF0+\nb6bhAdcOZCDR4idGiXAX9RQdDKBC04whqBeqk3NapMwWtvhwIIwH4Tl+LUGj\nPeE/N9we4MQJWtEZUXIHdjXSqoh5uzlLVN/SijiD0oo7wOX3hW0FGkI9xsdY\nFKMa2JqtuD9kHAF7JRGlrmc4YkaD9M5Z2Y7lFqV2jh5mbsRgOvUVHkPjWwVO\ncDn117aF2XOLcAXnj8lf6B+KsEcP/IoqWtf1SnvXQ3DnUCtg1WgpXafXFogc\nIXHSDn3w6yD+eZtzE1Z0kbLZSU6LekLoOA1YARyT+EJ4KGnbbgRjXcZJ8sB9\nPp4eHy6G4El1XATivs8ibeSEqpx7nLjziNfloKHQRXHK6gPLt1OzQiQ6SiKM\nIzJfuMaD106AmSMGdQd3xoAX1aO1Z0DVMV5bLmWK/u7u7GFywbNkD2KS+1Ej\nGHrzsZ4sFAc1a6ggWqC8ukXOCb6do6baX5YNqGPaMnDCWBIgUb2wOg34QY70\n/R2fOEYOsxo6ukV+pStQnEcj7ZR5UUO4cgeqcaxg9V3zrIM49J6+Y9hxIC9y\nnbhctoePMDotcDKDMPAde8jPfeHjBKbXFlaIcDyRMGfrkYmpmOfjYOnYk51b\nkAInnRZRvbu2VlbFWCPmrTa/okcOjdWuhNzBK0sc6n7JCqG7Tk4W4zEaM7s8\nLaK+\r\n=6BuK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.16.5_1639434491943_0.2602302107882981", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "efa9862ef97e9e9e5f653f6ddc7b665e8536fe9b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-8UYLSlyLgRixQvlYH3J2ekXFHDFLQutdy7FfFAMm3CPZ6q9wHCwnUyiXpQCe3gVVnQlHc5nsuiEVziteRNTXEA==", "signatures": [{"sig": "MEQCIGVAA7U9wf2XNFm/ybFA2sPCXUpbyzq7RJOm9qKaqWY7AiBNfafPPDFq7U4UNn/I/wJ6+jBSiEqJfXF3z5QAIJCLqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1SCRA9TVsSAnZWagAAv6EP/jKfcybA05F2TLmd4DjW\neyTRJ0K8NxDPBGpCREQD6/Vu2Dh9Q2vv6MbioHJ5lA1tfcYCwPbGQhAswYf9\nZm8xo+HOGH3ezDb9CuTD838YeE/xAxUjaQSdYu28ydiIpOlaxLqn0KFSwurP\nujuH30eSeVIsI5irxQALHvkog/NznakFfYqrgZvn9WbvnIBLkOSgc9Tiq0mU\nsvNAKVaNvpqeuSC1G6L2qGiPBtgyJDOmWpjY/JMgmhlhwmx1jRa/w1zaNM1W\nwpGjbRa31Onf3nwhSGg6gJHoQthZdEVN3QdGqMdIVs6Et1XKQWADdLKdwXsp\nWjHnKQlsOvjkMkX2Zst6mqhyKErTtegR+GL2QKyw+404HJz4CXg2q5z7RVpt\nwojNiQ8jZRiCE4kLKg6XOJ6dFNUJ36xx3h3Cr77GGohXDj8oViIdurxd2cep\nX+f/q1xN7LtBWlZdFC6+6pAF08I7m7SvkN1CvmLMRTjD1C8OkUr+HXkMT7fI\nbfjH6mmNfAMGAR6IiqaURJBgX/AA7t9iEWrpzcYdODveR//UazC5nLWbPN4m\nfsg5mC9z6oyWhuJ7lsmIk5ti9ljWeqTrSmZQkqDntXqpT7lk2hXk2+7ADC0+\n2oQ7FWcm/h33MwIytWwvUu/g57DDUmI001a7/7ptnZ/MxOwsBaINDRo6nsug\ngREn\r\n=PKyx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.16.7_1640910162735_0.9964393990994493", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "421c705f4521888c65e91fdd1af951bfefd4dacd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==", "signatures": [{"sig": "MEYCIQDDJ8KqR6Qz/nl2K9WtOwjDOrrmvUCNNxc/qQJbUf4wRAIhAMA2yQ8RFM9PJ7l3fxLeottsoLfChSUGdLjufaA2S9PN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP2g/9HNuc++ZrPb84Pw2zPFhfJizrbsJsBGznrnEe/LP/OuMsLir0\r\nYrgerSXZiD39L0qSGdchaNoXcwQhpwN3G5VgfwbNeM+GVmn+Bqxh97ezphl3\r\nyl33K6/QfQ8X/ThQZbhn46CUIwnyoTrD6khRKDW81hFx7tfQYO3E5VAU1jhP\r\nt56BbUK9lDuX/baoEGJTBAdCFobwho1ekRqj323oS8+sqm7JFsHYAdx5ZLxe\r\n3MUiBBMBI6neOodlsjOsEqUdw6rgGAGXsJVUbw/9gUx43YUjPrA+olPzNIhK\r\nhVj88yEnmrP1b05gaLElp6xnygPZkA6CdAPHwZOcmTfuH3INWLzuZUgKi7vc\r\nANYwn4vJdq6Vm796YRlobq6cEcH4PM+ufyJsg7rrNJuqVUbT0+atUVAnyJp7\r\nJpRsF5l9+kOxKscBuV92aHBedNR9h+ZiJsDqqRXv8+PyUZzBAcjrfy+tdCMv\r\nVzRpgYCDQ9iVHujRuWlHVZJTXaD8W5WdYuvsXzHLffZ8mWJhjFBProS11yX0\r\noGuDWGphHLzYuwxr8nYFCE/JwvkL3rOen9ztPgrzhkNYdmCkLKwam2yXEMBQ\r\nxaFfd0Lqxj4vxt5jb+A6vN0qxJb8X8T0LgsfdK0sTBl1v+Lf+rhiRGTlCGaf\r\n0+wFgYMzzm53DGJtSw2aHVbhhOOK2UoZofw=\r\n=0ux4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.18.6_1656359426065_0.9815949285813019", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "59b583bff3332fe4596d81668bd061fd53429bb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-/1N4t09npz+oBRwL2HgvqMeGAK7Ffw22LZlmW4OCJNmJGF8Shm9Sgv8iWAqHv5LXGX/VQeFespTrQq4/qZGxvQ==", "signatures": [{"sig": "MEYCIQCdxciZp4Q/2aeRcGw/O9ArlWH+wqNlviBknMSJcR9KyQIhAIJuUpFCKN5eInzDYal8Zm6UFqunIDrPDzCmErzYe2s4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvPQ/+PZ/y5ELOkuFFuJ1Gymm+HfotTx/i69B/UtpRFwaMuhyJ7dJ5\r\nvSwwK0xxTpn2Ni9/3PgG04N6h+ogLpsahmdDDz9LBA/+0ycLxVVs59UbmJvH\r\noAQD/k/ZNDpBH86/0+RamI97IAt7dUx6BVg5sEqmOyy1D2qxiWKlCL51A38c\r\nqxHsFCQd5g6Z9HR5kbYfSTpHEzdX0JXemzqH/2n4CXfZKBsexTuiJsVzD0a9\r\nqS/evj1cJaX8H4yspAGnrhnk/h8qLmD317B6tZ3fu8aBaZTOpysOIII2K+T9\r\nkYmqka7myLwBwmIOFNruiRDcGBkN0ZiiVhSrcJEVsgg+8ll3xZDitZTf8Ub6\r\n/YjTfgSC0ubDn5EARWf2G0j/XEDl9GPYDsvHXvzTFFr7BSyga7wZbZ5ZlKti\r\nIMrlefGffYsgGMKo+ocp0dK6GYBUAdIDaux/m83qCV33P48quS/f8mHQHlYX\r\n4DVEL78vn73mVgRCho5/9BWQzOKVdZv9WWHtOJ8pLuAjtON0jlBi/qMjIbJU\r\nnhw3Ohx1LPcyX453s7IuRP3ZBAbuo89MqsQXWiez0RF/4B10+QXR+BhxeNN6\r\nG/rK9T3+DF1Hqqc3A98yhQI3eMVIz+D0S5pqna8xqRU6UXFLLrQKa+klSerg\r\nUosLaEcI9EezH7pkNmfoMMgKlK1qepD23JM=\r\n=oIn5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.21.4-esm_1680617390135_0.48668368006350504", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "2d51e122a8f9c7fe54b23ea317c6f5de61428658", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-7i6nwjcT5VU6aMUbRb61N5jeFUkSuM8ch10eP9G8YGHTchZ9zwX5d/wel7k9b6bcnX6VaSbTL2R/s4htPx5ilA==", "signatures": [{"sig": "MEYCIQD5BbGneq7AvUGftzES/HLHeQ5djZjJp+OpFUSVPvmrVAIhAN0BeqV3NS7I5JFYYQj1AYrxkXPLIEbovGGxdiLUIHhO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop6w/9GyUSOKbgpPxAugsVVjTSXg2FUs6LeHVAht4T9jibQMXIu7SM\r\nXZ3YXhSu/k5KKGLgiqJBU3PKtFJH3uEKrgtGZXJjIBhH5dJGJajlyAT4TDnB\r\nOy0qdLGdNba/1w3LCzgEwuEykMulxKhbJOvLHurDJV2pDNT2Gv6teGCEcRX6\r\ni2HHMONQAmEaBs5mYgTH2xiLiVf7kpFaPF4pm581URKfS3iVPIbnYc9cJKyX\r\nc+tUw7nIfasOQnn1yxy+GNRwSWNI/9kYNIi7Gk2noyu54OoZlKyyqVuKvBfa\r\nYfr+YIGQY+YiDydWKhTAVjGInXfJ7EEBVIXQ+a5uys+mUfcaq3IIfAT8NK+H\r\nH/b/X/KzvgFNE13S+Q5pqsKu4oUP+c2TDJhakifwvI2dkWHpFSZpkHXvEhEO\r\npvQFH6hzlXOvdfqmXjo9iR+pD6sRmouo3gdZPMq/NF0QpROQHAKQBwwxJRpE\r\naL3xK+U0Yf7O2EhwHsITJ5ht4dTGqWc1sFEHEivcozadXON1WZadIr6KdjNo\r\nBdx28u7B4eCCCa95N7LUIAWoRg/5mpVUJHqRb6d6Vqqu3vS9BZWxM0TgCiud\r\n4ic1JzplGyIlr53ZssgNzBEGsfa2dnlxjL+tX8IPSIYc61SRS3faUBpr+9yY\r\nSJEv3SXW1mv9SwBCRG81bU0BO4GMbUjOapw=\r\n=WgBS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.21.4-esm.1_1680618106957_0.9773149253613678", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "6e9a743331ce153f7c93a411c0b3be2760a0c269", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-1B4C6N3o3NDi8kkhYZDYKOHKywosqxMXnJjdoMnUiv/xvocFWlQ3PTLy/zp5F5gjBJHnNAoTwH0Wm36dJdyHYw==", "signatures": [{"sig": "MEQCIAkHEGnnQn2N1DSDYE/D/76vEI5yIoXu5m6UxFYwFdxcAiB7uexag5yy/gihX953bx1+eCEwb6dLVqZtpc7eKbUGIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvQw//SqeosERFREpIdwx4CuhAlDfM4x6A8CUN3bfwI813KDrWEvPm\r\nVkUlQcBgyDzjJJuNPQrgwzhAL0clu/gJxNniJ5FLZC2Bkc7CC+5qmSXM9iYA\r\nGrTvAt6FmgXa3yE60MMN91ONBePhsBKa+Kss23NiFijMNRixucjltfQKtQuU\r\nQ/RtLSuWt1b1FoM3XLO2Q8mLVACdwbup4p0PMQMly97qe3RV6cFtbDRo1VhV\r\ne5nbpx4cFv66X56Yg6wH28ie7jHEDuIHT0B59IX1EyB8nAoegQLND0Y7zp2M\r\ncmJaMBTVwKZ9feWfmqjOh3/8t2dz4MuEcLAg0tZzbLoWLwPoQh0Y/4JnAEZf\r\n/EHJnYOW9KWtT9mwMb68zTAg7g1pE51PLJ/PjUT6M69SkoqsMXdr3PkamAGq\r\nPcxCNp60VJNg4MBhzHc6kZPnAnFsy/OqxIoVT2JZC9W7SpFPBr2TqGfJ+RV7\r\nz8qnpD+OJtz5r6q7uhnBVRD8u2gO81AbEe9LW41OJN5U8D3uH5EaET+gQgBD\r\nBWWkvgQZRK1KxB6yK/nBNYzhCa6E/VFLsIdQQZdrVo5RrXqygFUIdvHxGBpp\r\nga4vDP1MrTqjkxk7oc3KyOFKQpzse6/FkZhZkCrDAPhTOFRQuULw31U9mYiS\r\nHiFLmjQ5rg/nVEpHig/LHzObmVoPvshfnu4=\r\n=MNIg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-builder-binary-assignment-operator-visitor": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.21.4-esm.2_1680619185495_0.7595884198712803", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "1ebab287ecdce5f2a298b16016466d89b6433a9d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-dlwtT0q6csjKc1+rMLb6GaPAu8bqfuMIIkyECW70eZliwgFfmWx5hKl22gEi1RykBy2xEFcZMfJ7AglaRKnvXQ==", "signatures": [{"sig": "MEQCIH2A1B+p04xa9NScp6S4v+8VhY+UcUn+EW75QLwW+7/GAiAFGOS53TWevXbrQnSC2244fVszMmrvgsn4J7spIbRbHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2jQ/9EoWIfOY/uV6JT8FsgA38KBwO/RNRAntoLi0Ux76G3yjYif2a\r\nkKGPk7loj6ZavTeVwh7IKtcQIL8zQJ6/Hz6KsPbAChdw+qeyx8JsHktCuiQR\r\nnQY+OIi5ZhJh6soAXAzesF99u9Q9ky9gv+eq37ImVzKDSy3LYUg0wmg2fCjk\r\nPsLHEbcjYaMenhXL+14XtomLSYZl6m0ky4TBFA6txlsbeLL1aJJjllb5sYtg\r\nlppz/XLsN7voCQTTsN5Gc14qKCqGCA3p7DCJurqaS084oHcassQzfSgzzLw2\r\n9cSCSrft0QpzItYTIwC98qcaFsxvnN9DjJP8VRoSw2QIzau1g3UWGpPG3q2h\r\nseUoJ6IvBXte1QJBx+Rx44wO1oiOguibAe3tDg4vA8oNaZLULNw4TLm1SrSu\r\n/3YXluaWpVrc3X3nDvZhKScgWnBbzIQ/bEVScud11m8RAwNBzmmo/W9OHRw0\r\nECzeMeAVHuXYs0OUurKxRyIDXp1vtLo7K53isvKXIBN6Re/Mea0l98iguicM\r\nXXbPgEO9WUwSe6RVTh7MDD9zZBon9nYt3X6QYSTQatAdk1Bntob0zk6jR+m9\r\nzm/G+rarfHiX1V9KWNM/nWQvA0q7b7U8QJhTqDfKaqCMQZUEqWrf3aQQCFll\r\ndrNYK6HVAc5OByCTy8L8lhVbfn+5IfhflX4=\r\n=hbl0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-builder-binary-assignment-operator-visitor": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.21.4-esm.3_1680620192397_0.3928904564345197", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "e9aa627ecbd515075820335be64a65b74314d0dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-RQuaS0tvA5fK2DWLxHddl4pQAcB8evwgVytkIt6dzJRM4FIcbFgXl9sXD7tTy5I3rqZbruFRonZFWclNQrEbow==", "signatures": [{"sig": "MEUCIQDbzXpNvabnJLena9ktpPVbfondpOEKw3vBKJuvMj9GDgIgeZkvxGhJLuIG0alOmpsRF+z/gJjL2gGxi0hftkKJbNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLCQ//QiHUurAcY6unBgmufy6U2vW0ZRe1qP+TlPet/Iu8rerxrQqH\r\n309fjUTp5+s2RBxYzqC/sycmOHE3FnaXZ4oj7gNcSwEVvxSpjHKNHCUO2QZx\r\nNAnvCCl0VfjTPw9aF6VvfNsgGDnfQhw5s0nTBGZGnCRQvfungmxirVeJ22QK\r\nGDu5jRllXVWiTKW2+RT6b/6FO0ak0HI1Yiy2MpvGsaPPrMH2YdHNFLMOnv+C\r\n4jvA4Y3x7AtyfDxo/HNE0qBBlGLmomgRyzzTGcI1/dGkD2MIM51lRJ+1BW+H\r\nHBP6ekmNQjHewK2MCsAdrewjf42pfOpeg2Hj8dX3v45hb4lnwnbg+dsM9prD\r\nLYME9miWCNVU/bWIQBBI+Ds2iDMOqjP4+Vr9oIGxH/wyar8EB69B4k1xkEhP\r\nxOuZ/PPTWcbJqLT+6Wb03mO5umZE2Hx6aYZRzZmc8GZezAoBtL8cgdUd57Lf\r\nNcwn9l+k7m9XaUjwm3gQ4L1XVS8BhlQ46HorjT2iibKWiGCFkyahpJDlpj7z\r\nNKduUEM4JOd9sqPzmVmPtqNJuoOyM2Q2nCCQLB8eLivPy1vY44YTOuAh6jMU\r\nP9v+wLk/yMU24htp5eim3w809jLCyvXUJDUDIwYsTdIPdIt0Q62Ka0bJxIc2\r\nmjIiQMB2J8nJzpvO8YpSkyZQ0OH6T3YSKlA=\r\n=tpVk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-builder-binary-assignment-operator-visitor": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.21.4-esm.4_1680621223006_0.6221383428293159", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "402432ad544a1f9a480da865fda26be653e48f6a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-vIpJFNM/FjZ4rh1myqIya9jXwrwwgFRHPjT3DkUA9ZLHuzox8jiXkOLvwm1H+PQIP3CqfC++WPKeuDi0Sjdj1g==", "signatures": [{"sig": "MEQCIG8OhfP2DIt6AsDkugYoo1W7/EgFMfmzOU3iVb7Ss9nCAiANDtsq16SuM4E0HghTyTfyvfn6NPW37B05+ZAJcIRanw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4939}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.22.5_1686248493125_0.625272216388961", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "f8de17d69a623cd66529220ed2ad3add6548cc82", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-XACULfgJJUea0c2JNbiu6RS1oH4m5JmYUE45h3A2bCSnmB9bffKn0sJa4NUmoYyWMVyIW5G52jHnwbkjJaxCvw==", "signatures": [{"sig": "MEUCIQD2no6vIZyacfM1u3FsuaNWQttBx1rduylJCu2KJwwQ+QIgQeo85Q4xIJ/TKlFl+IZ0hs1YGKUPvjMjEoty+qRT11w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4631}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.0_1689861615066_0.1832879562416594", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "146e9e670fda86ff07fbec34540d6da5f1fedfe8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-TwVAfLo+a3r32Zjx5qvvRmjf1QgR6/fWh3dQQV3kL0YncNlhg+DAzgg5ASIIHFGtQKdK35zJ8w2B6TudSZbZHQ==", "signatures": [{"sig": "MEMCIH5hMMFcOg4oBcWa1zEmFjTEzc9KLPvCwZdEYjs2yyEGAh8IilZKv2+T5aayKlb4y0QLiHEv71UoFmey5Skh9Tv6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4631}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.1_1690221159082_0.6459903190303078", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "e7163a6b6769f099cf5c0fb39b303999ffa81df9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-GhZmjvRlyeHFBc37SbFZ1tRGk+Um5TCc71ZnnULr+9TVjYOiX73KvUXAM8CxHaiG0UuUfjrDzngS37kNP+Ii9Q==", "signatures": [{"sig": "MEYCIQCBmTfQvpYScPE3RWVV72Nc0pB7f3gsKAbiRwRiz9N0bwIhALyeZu9w/PV1gggWV2z+Mpd1G7qeDbbe2jtw/xzJr+Kr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4631}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.2_1691594111736_0.25841602763638405", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "215efb5ea3c5669a1e658d6fc7bc49382c5e352d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-K87DilBgxgv3/4mP4f8jTjJGP4GMg5p3JcUnLhGIj2Uu6Lmdu4/5Ucz3DRDNGbHrJ2HehWA/1LhkVMV5IVA+ZQ==", "signatures": [{"sig": "MEUCIH4ZkIvl2ITKSOmT+x0iInuXL2KxPHqQTAVQJtxJGYYYAiEArCV1AQNx0LKd9+IB2znDyJ6vkZzUqHDRfvHuvasZJcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4631}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.3_1695740242483_0.9720143522872948", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "26e9437331e68bf0070dead2458f6cfaff1e8a86", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-paGaJgVi/kvqcjX3r03M8PjTpC+OxgB3csJyq8v5dz/eBKlun3OuN7A21LHlz+NwER5pIt5Uh+doI61uP0xhCw==", "signatures": [{"sig": "MEYCIQDvWg5vTh0W0vz9vNQ1JgBybg7M9Skc2Jj//tt+ZZgfggIhAJFLT9N7SXUgCZKL+0cZPzpRJixiEElLczzagbvGNlpt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4631}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.4_1697076396509_0.6065365061191499", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "ea0d978f6b9232ba4722f3dbecdd18f450babd18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-5fhCsl1odX96u7ILKHBj4/Y8vipoqwsJMh4csSA8qFfxrZDEA4Ssku2DyNvMJSmZNOEBT750LfFPbtrnTP90BQ==", "signatures": [{"sig": "MEQCIDRwEMWCeFN+SzcOHYtPkybptkcnJZoI57fhERJsPeSBAiA+ut1yPWtPVzyKKx4Kbr55nFSLQN0oxY1sPyCRKDyptQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5020}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.23.3_1699513429702_0.5821801143641592", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "3c6480ea82bc435861c3e66ac24797b886d0699f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-lukLNvRVOl7b07roOHSrAh6o1qEhrnBGts2JNB62UrmkOsSfMAjKNePpepOHYuRwXaX9gQNJARhoXlgNKkJVMA==", "signatures": [{"sig": "MEUCIQD5NnYcnNlfGL5tGfBKTRF0Cx5b09phZZoCRXKDItk9cQIgD67AMaSu0Nddr3wkPHp6a58gq708d/ptAgufnXxAMIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4744}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.5_1702307965000_0.2528942067680622", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "c8d6384564aad0a6dcc629a821aedd10ccd1515d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-8N0nH6j5ne1g1BxcuG5a9mgJMYor7HI5kJkdjGz9aeeJDxr4iL3YycRA9BV6T10yiN3kst9LmFgaELBM+JNqHw==", "signatures": [{"sig": "MEQCIC7+OHjIgA32Uv8h3C0sb/Ab6XjmlvNCuTe9qaPFupMAAiAYJ8d/c9u3KKzfkpVJSw8kCAlY9n9clxucTgURCJJIUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4744}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.6_1706285663929_0.5621358285178195", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "39727858c790a51ed4c3cf7357eaae2c447aa2b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-72j0HgWL5nsiPclvKTBnC+urRAJDohgfu495OZqvTvwcTRMVJcri2zrzfBNsJKO7e8KaZdMBHRnxVBRkGsBxsg==", "signatures": [{"sig": "MEUCIQCzV74kZQ1KVn56RWavJk8fw1p+xS6PhE+J1Xqi2jCX7AIgOWgF6ZJqiHB/XMW4cMhlqy1pL1EA4rzfmoHy2+PewdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4744}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.7_1709129122850_0.3747657762699732", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "6650ebeb5bd5c012d5f5f90a26613a08162e8ba4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-U1yX13dVBSwS23DEAqU+Z/PkwE9/m7QQy8Y9/+Tdb8UWYaGNDYwTLi19wqIAiROr8sXVum9A/rtiH5H0boUcTw==", "signatures": [{"sig": "MEUCIF5m8neNrIhV7ycErQngpTtDhIixzzL1uBONbmjt4RlHAiEAziGP867IWkTKqZDx9WTcZsb2skRR7V6Pspxr9K0WtPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4951}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.24.1_1710841714834_0.07283991097221021", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "202fd2072ea2dd23fd80ae431fd68f94aad3c68a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-m5OwWtEo/rOqJo2emb8/DN5t8jdTbjlNjjySd7MB7sDmdlbjvyEXTkkO99kQGKM3y+jnUYTBv08/ZP4m3JOztw==", "signatures": [{"sig": "MEYCIQDvRFLa4vVhLTgqU/OQhg8Kmyt4CDuuF7wQdx/c7o3nDwIhAL127yYXf1zYPT7aAN9e3TcvSbi5utT+6/jsG30vlAVY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4658}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.8_1712236805997_0.33969645416678484", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "011e9e1a429f91b024af572530873ca571f9ef06", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-EemYpHtmz0lHE7hxxxYEuTYOOBZ43WkDgZ4arQ4r+VX9QHuNZC+WH3wUWmRNvR8ECpTRne29aZV6XO22qpOtdA==", "signatures": [{"sig": "MEUCIDAtJ9U6hJeqEFpJobfsrQMwmrUzXCEBOVqOd5KRnrnpAiEA1aGpyXNq2AbZuTPqRH08ww1xHTI7+Bv7uG80dILzibk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71223}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.24.6_1716553493733_0.7459104723732415", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "18ae17846094353ab111d51649ae58670992fa53", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-u56gVwgEm9NiFVlRMzANyhgaa3nBQrvrnGaTaXMIpPoAyqsGZagwCrEAG0DEdLF9RcGQY3sI7gUfF1Vkh08ggQ==", "signatures": [{"sig": "MEYCIQC2LyWu7362we1scraQSRWs43ZPn57cDmXbIkgYhYxhuAIhAOQIkiksYvbGmdcCINP+CSZWZEuq/IagnpXejuOIgIz3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71208}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.9_1717423523678_0.10912672737570928", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "5a26db5cd6e523d5338481504be849f42c078ca2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-rvNnlL04PdQ7xQcnIZZuphNPD32A7OwXDPVrv4szIKjwxfhTRQKM2tlmzMpkDmdJ5o5MfX6viXN2l6KTnMDRaQ==", "signatures": [{"sig": "MEQCIAokQVkdcN561tG3Fy7aK0m+3jsYxlY0eof3dDep2hTPAiA8j35YmsG6+Ush24on+8JEZTcM+D9tV+/QEPDAojrXdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71216}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.10_1717500035663_0.8677187073635728", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "b629ee22645f412024297d5245bce425c31f9b0d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-Rqe/vSc9OYgDajNIK35u7ot+KeCoetqQYFXM4Epf7M7ez3lWlOjrDjrwMei6caCVhfdw+mIKD4cgdGNy5JQotQ==", "signatures": [{"sig": "MEQCIHCEMg6PFQPq/HQmzqCwQbdJyd6YJVjuryUT10OgKB8aAiBRV8tWy4XgbjFv98NbTBfP94HmXt/0WcSNwQyHLP8+iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71219}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.24.7_1717593350116_0.8098963187376331", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "525582348953d15860b1cffad57f59905a70f0d2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-nDTOKY3K0R7a+penN8JC9smIstn1B6jocOYi/3ucROZ8V2BOQemeX9NvktGKhM/HPNBTgKvWRugDJK2nzagvtQ==", "signatures": [{"sig": "MEUCIQD5xfjFyGPZtosO9PJFZFGAbhEyrEhxfLKRrotzh9/ZXQIgJsvwKj2poXkIGhmlpWZQ1rt/YDWpShKjvvel8fPThx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71105}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.11_1717751760413_0.49795622571556697", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "1cba42d58205adc1230e52dc2f2fc85cb3e9dff2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-dHKVlmPDLO/zk+zWhQ1fpUolqYHVZ5wCWyeH/lfjmOCThBbZ1EHSPCGvq+nDBsYP94oRrCtBR0BMFAlw93JlZg==", "signatures": [{"sig": "MEUCIQDCfxNVn2m1yGs9+DOnAKlXE+uJcbW3CkbRDXdONhzgFQIgIP2XEO5O6dCCkbiBfAkWRWCOI1LvfBJkDKoAQWN9y/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67889}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.12_1722015234534_0.16880313447993367", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "5961a3a23a398faccd6cddb34a2182807d75fb5f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-yjqtpstPfZ0h/y40fAXRv2snciYr0OAoMXY/0ClC7tm4C/nG5NJKmIItlaYlLbIVAWNfrYuy9dq1bE0SbX0PEg==", "signatures": [{"sig": "MEQCIA3PhIp1o+ATQnBeTBK9gmCXBRsY9Z/HmQIukmPg209ZAiAuGEyqUrRJr7CX/2gXD4QUm0kABkNGCDYLrlyW4jew4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75744}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.25.7_1727882123470_0.26300805268737837", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "ece47b70d236c1d99c263a1e22b62dc20a4c8b0f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-KRhdhlVk2nObA5AYa7QMgTMTVJdfHprfpAk4DjZVtllqRg9qarilstTKEhpVjyt+Npi8ThRyiV8176Am3CodPA==", "signatures": [{"sig": "MEUCIQCCd8z5FXUYDuxXfL6g8VTax0DQ4m7pzX64UoSwPAyDLQIgcgMdfiF8H6Hs0JWpUp4WEUELZ3Fzi3YM9x6pXKeYrzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4983}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-builder-binary-assignment-operator-visitor": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.25.9_1729610499410_0.540309422073598", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "f40c6fbc2614fbfc854bf8b5038546878855bae9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-q5BZPx6FRYcr2RRkyqR+pR0gbSJLhm9JWtXHyAmVXZXc+nWMQTi/FJZPr4Qjcy/26va7SJ5qDSbESnzo99RI8Q==", "signatures": [{"sig": "MEUCIQC8dc2023vcKQv2wSpdkNL1WW/WTfd6z3/oBN1THc0h2QIgRltb0DLyPiavw3teKWHCuPoDPULxOvIQKc5xvm2knz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4988}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-builder-binary-assignment-operator-visitor": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.13_1729864479921_0.20406450952243826", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.26.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "e29f01b6de302c7c2c794277a48f04a9ca7f03bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.26.3.tgz", "fileCount": 5, "integrity": "sha512-7CAHcQ58z2chuXPWblnn1K6rLDnDWieghSOEmqQsrBenH0P9InCUtOJYD89pvngljmZlJcz3fcmgYsXFNGa1ZQ==", "signatures": [{"sig": "MEYCIQCqxU/cm6HRjtX3+B8mgYUhUVYdXfRH0oUgyYETLGq7iwIhAKKIWHI3FqtbY4vZk3jILEm5w+iC1gK1YTZS77F296dz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11489}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.26.3_1733315733978_0.6244188492941656", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "a26b4feeca2a31d7de1fb742ce2746de9b014ffa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-YqTXzCMjfUg4yEQ52C4WMqK3jck+SgGrd72MWp6UzVr0Lrc6Zb7tLKI5eF/WFQGnGoM+E+2A+OrGIrSnB45sUA==", "signatures": [{"sig": "MEYCIQCpJQi+cOdt0M4YD5NRUGurVJlly3/Dm4m8suitr0I6bQIhAKYgkA0cZzBl5EXewQPjBPZl5rrqtZMjyK66ThqNghHb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.14_1733504035338_0.8748517894247783", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "dbf15894da5c8a932ed8ea6ff9aeb74fee08dfd2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-p0/IhIFzy3SXJNJi1FAAm725tl8auLeSIYsuYJPa8wQUfpzVbTGe53zdRciRnTQSuSI584FHdCxpC63TDYphwQ==", "signatures": [{"sig": "MEQCIDHQ9CXH5+R/U3419vEXprWnkx16NMIoTSb7jhiwbv+WAiBbYYZ9sMG4/A+/zWMogxzpNswo9gTeEzv0cdid0Jw2cg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.15_1736529860286_0.38476723637496746", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "92df286001baa79e268f98fb5254f9c5dc107fd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-YUFYKhoLhFMgaaKz0yC1fuFWUFx8Mbu0amBWm89+U+j5CSvucbkkzz4ekWqq5M/Cs4kBKTZ1xFJ3Ku0ejNq9jA==", "signatures": [{"sig": "MEQCIB2I2qLnQdb+bUB7UTcQog9X5ACqiJpGb3AQXbENUyM8AiAporT4SghZFvKWikU2610nDdcxl5yevCK/eUY+qDVbDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.16_1739534336468_0.3263514519960513", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "185268158fc934f5d8c9f7659babdb6dc494d068", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-O6XUW/FvnmFe1iTdYaz+RPoQ43zEKDInBAjtHD35bSSXm3BBt0oxUiFC9SzxDSzxhO8toeuYrsS+0QgEYHkGSw==", "signatures": [{"sig": "MEUCIHK/ItLBm51FpyuIaNIsQAW0ZAojQ1otE+GT/WfzfYbyAiEA1f+vqGJ2c8pnis3XXgBbc8Ym504WKo7LYsTENKBEalo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-alpha.17_1741717488864_0.06406491085557264", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "fc497b12d8277e559747f5a3ed868dd8064f83e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-uspvXnhHvGKf2r4VVtBpeFnuDWsJLQ6MF6lGJLC89jBR1uoVeqM416AZtTuhTezOfgHicpJQmoD5YUakO/YmXQ==", "signatures": [{"sig": "MEUCIQDvR78B8WbXBjqqdooz578cGLIR3CxBTEltGovv4vsJ5gIgCY/6z4Rti8oN2FSC2+kEAQc2Pvj4ZvNmudlQ1zxzThQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11489}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_7.27.1_1746025727003_0.9525649833680203", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "dist": {"shasum": "49127234e83d9ca426dcce067b471e13012b7156", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-pA/s2wMGcvwb94USTA/sDavHT+vFqmzyZ+OVUi9+tbfSxFqqHJYetRXlsW9TS2wBBJp1r1KutkQ7aFrcdDE1wQ==", "signatures": [{"sig": "MEQCIHmb18kUPoEevFRJyFt2p/Qppoh67u9FYKgM47DcCYJ5AiAR7xkPSZA7J4u3wEzh16MiIwwLGwGoRfYQ3UPLZDyDXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12053}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-beta.0_1748620258744_0.19501227939547938", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-exponentiation-operator", "version": "8.0.0-beta.1", "description": "Compile exponentiation operator to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-exponentiation-operator@8.0.0-beta.1", "dist": {"shasum": "cbf59bff1c5f4bcf2dbb5e098c22016ab76ac02c", "integrity": "sha512-1E0q3iEGV/GiX9+99dMqpQxK9eiw6SUTkuR3e06vOYgvVAgFM/ofAJI81OHMuvScfje+zVRNhdbDsurljf51RQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 12053, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDM2krkX0bEEA4mhQSSEYnQBX1oCSlLUHlRjHcKJCk++wIhAM7Ax73bRqv91fCrPOiOr+YBGDcsncinsgij3U1Uw+wF"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-exponentiation-operator_8.0.0-beta.1_1751447051722_0.9258433375241275"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:32.088Z", "modified": "2025-07-02T09:04:12.196Z", "7.0.0-beta.4": "2017-10-30T18:36:32.088Z", "7.0.0-beta.5": "2017-10-30T20:58:09.741Z", "7.0.0-beta.31": "2017-11-03T20:04:38.784Z", "7.0.0-beta.32": "2017-11-12T13:34:11.774Z", "7.0.0-beta.33": "2017-12-01T14:29:30.272Z", "7.0.0-beta.34": "2017-12-02T14:40:29.483Z", "7.0.0-beta.35": "2017-12-14T21:48:34.586Z", "7.0.0-beta.36": "2017-12-25T19:05:53.456Z", "7.0.0-beta.37": "2018-01-08T16:03:49.874Z", "7.0.0-beta.38": "2018-01-17T16:32:49.610Z", "7.0.0-beta.39": "2018-01-30T20:28:51.562Z", "7.0.0-beta.40": "2018-02-12T16:43:00.204Z", "7.0.0-beta.41": "2018-03-14T16:27:09.596Z", "7.0.0-beta.42": "2018-03-15T20:52:19.650Z", "7.0.0-beta.43": "2018-04-02T16:49:06.607Z", "7.0.0-beta.44": "2018-04-02T22:20:46.710Z", "7.0.0-beta.45": "2018-04-23T01:59:04.129Z", "7.0.0-beta.46": "2018-04-23T04:33:18.174Z", "7.0.0-beta.47": "2018-05-15T00:18:21.595Z", "7.0.0-beta.48": "2018-05-24T19:25:14.912Z", "7.0.0-beta.49": "2018-05-25T16:05:11.481Z", "7.0.0-beta.50": "2018-06-12T19:48:23.578Z", "7.0.0-beta.51": "2018-06-12T21:21:06.930Z", "7.0.0-beta.52": "2018-07-06T00:59:53.993Z", "7.0.0-beta.53": "2018-07-11T13:40:57.082Z", "7.0.0-beta.54": "2018-07-16T18:00:34.125Z", "7.0.0-beta.55": "2018-07-28T22:08:38.889Z", "7.0.0-beta.56": "2018-08-04T01:09:28.442Z", "7.0.0-rc.0": "2018-08-09T16:00:44.559Z", "7.0.0-rc.1": "2018-08-09T20:10:19.866Z", "7.0.0-rc.2": "2018-08-21T19:26:26.223Z", "7.0.0-rc.3": "2018-08-24T18:10:08.905Z", "7.0.0-rc.4": "2018-08-27T16:47:05.553Z", "7.0.0": "2018-08-27T21:45:22.331Z", "7.1.0": "2018-09-17T19:31:46.245Z", "7.2.0": "2018-12-03T19:01:18.101Z", "7.7.4": "2019-11-22T23:33:58.472Z", "7.8.0": "2020-01-12T00:17:34.883Z", "7.8.3": "2020-01-13T21:42:29.816Z", "7.10.1": "2020-05-27T22:08:27.447Z", "7.10.4": "2020-06-30T13:13:23.480Z", "7.12.1": "2020-10-15T22:40:04.560Z", "7.12.13": "2021-02-03T01:11:34.824Z", "7.14.5": "2021-06-09T23:12:55.411Z", "7.16.0": "2021-10-29T23:47:50.848Z", "7.16.5": "2021-12-13T22:28:12.095Z", "7.16.7": "2021-12-31T00:22:42.868Z", "7.18.6": "2022-06-27T19:50:26.208Z", "7.21.4-esm": "2023-04-04T14:09:50.296Z", "7.21.4-esm.1": "2023-04-04T14:21:47.130Z", "7.21.4-esm.2": "2023-04-04T14:39:45.696Z", "7.21.4-esm.3": "2023-04-04T14:56:32.528Z", "7.21.4-esm.4": "2023-04-04T15:13:43.126Z", "7.22.5": "2023-06-08T18:21:33.298Z", "8.0.0-alpha.0": "2023-07-20T14:00:15.244Z", "8.0.0-alpha.1": "2023-07-24T17:52:39.226Z", "8.0.0-alpha.2": "2023-08-09T15:15:11.943Z", "8.0.0-alpha.3": "2023-09-26T14:57:22.706Z", "8.0.0-alpha.4": "2023-10-12T02:06:36.714Z", "7.23.3": "2023-11-09T07:03:49.873Z", "8.0.0-alpha.5": "2023-12-11T15:19:25.197Z", "8.0.0-alpha.6": "2024-01-26T16:14:24.115Z", "8.0.0-alpha.7": "2024-02-28T14:05:23.015Z", "7.24.1": "2024-03-19T09:48:34.982Z", "8.0.0-alpha.8": "2024-04-04T13:20:06.154Z", "7.24.6": "2024-05-24T12:24:53.905Z", "8.0.0-alpha.9": "2024-06-03T14:05:23.829Z", "8.0.0-alpha.10": "2024-06-04T11:20:35.840Z", "7.24.7": "2024-06-05T13:15:50.348Z", "8.0.0-alpha.11": "2024-06-07T09:16:00.559Z", "8.0.0-alpha.12": "2024-07-26T17:33:54.820Z", "7.25.7": "2024-10-02T15:15:24.013Z", "7.25.9": "2024-10-22T15:21:39.633Z", "8.0.0-alpha.13": "2024-10-25T13:54:40.119Z", "7.26.3": "2024-12-04T12:35:34.217Z", "8.0.0-alpha.14": "2024-12-06T16:53:55.506Z", "8.0.0-alpha.15": "2025-01-10T17:24:20.507Z", "8.0.0-alpha.16": "2025-02-14T11:58:56.638Z", "8.0.0-alpha.17": "2025-03-11T18:24:49.029Z", "7.27.1": "2025-04-30T15:08:47.209Z", "8.0.0-beta.0": "2025-05-30T15:50:58.941Z", "8.0.0-beta.1": "2025-07-02T09:04:11.901Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-exponentiation-operator", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-exponentiation-operator"}, "description": "Compile exponentiation operator to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}