{"_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "_rev": "12-10372fbf26cd50613e6552da2a39b160", "name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.25.0": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "7.25.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "cd0c583e01369ef51676bdb3d7b603e17d2b3f73", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-Bm4bH2qsX880b/3ziJ8KD711LT7z4u8CFudmjqle65AZj/HNUFhEf90dqYv6O86buWvSBmeQDjv0Tn2aF/bIBA==", "signatures": [{"sig": "MEUCIQCuyXsuRR7w/q6yrnLlry32FqDnYB5BLyEjefG+1ia0vgIgMpkisBfy1OGYaTWv0q/2H2ejZTv8YALjHj8+rLoQpsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73477}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/traverse": "^7.25.0", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_7.25.0_1722013160042_0.3743714096885753", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "932f4a36e7f11a04be8a36ebc80ee883c0c5689f", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-uqpenb868vgy3sORCtPowemBjIJ33TrOf0wfXbi50J2zu5pL5c/Yk0G2VtVzvJ17CHqB394VACeLfr4AO99JXg==", "signatures": [{"sig": "MEQCIFmOA0tm8bt7x8t8utl/3farRKI+ubwpRJlIWnNYrEQWAiAEwRW/rDC2HqITgO3+HcX0x+7Dp+8FYNs0h3eVZZp1tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73609}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-alpha.12_1722015190454_0.030099456458916096", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "7.25.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "a338d611adb9dcd599b8b1efa200c88ebeffe046", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-GDDWeVLNxRIkQTnJn2pDOM1pkCgYdSqPeT1a9vh9yIqu2uzzgw1zcqEb+IJOhy+dTBMlNdThrDIksr2o09qrrQ==", "signatures": [{"sig": "MEQCIFmM9PE6vaXsRqBLuLBhpeU/QJDVcUh8y30lXWhQm6GzAiBIqrmU5SqUq3IZDbYd0sSXjGn/K+VKUz1JIDl4wm3dNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81339}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_7.25.7_1727882068888_0.5769678349670777", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "7.25.9", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "af9e4fb63ccb8abcb92375b2fcfe36b60c774d30", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-MrGRLZxLD/Zjj0gdU15dfs+HH/OXvnw/U4jJD8vpcP2CJQapPEv1IWwjc/qMg7ItBlPwSv1hRBbb7LeuANdcnw==", "signatures": [{"sig": "MEQCIEgxQqbUXj7XtVGkYNbfpo+wNALRXXG9Wb1Nmki/OXtCAiB33lakUUAnXW3EOu08wFPb30j5YVa2YXngeoiyrAqheg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10842}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_7.25.9_1729610443239_0.33375637683859116", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "d7aec1d4ad5edcdc15f7ef3da1f93120ea77d52e", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-DQ/z0UhoCJbBFeqOZRya9j7LxNzQW1FtMZtnMOcpxJMW2hawlsEYbOtvsfn0jqoajKfOtl46slU6wdGScectzg==", "signatures": [{"sig": "MEYCIQDtkDxtccAg/OZJOeAWET9xOUJTID7ojxnuRGFWJ6GUJwIhAM76xY87Bviwu3Vtq+Sdo+muLbk0R9fGi2NVc8mU8Pku", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10982}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-alpha.13_1729864428423_0.6062416146038794", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "f6b7620d2a11a5a52f268f54f74dc550f1286a1f", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-VE2AKyeOsYIOeqNfb8mjsjgRmliABXlEFuoEKeeaJCzJ6vI0iaw4ZCpxy+JoEL6Yb9jT39Dw5Swf6se9grrQ4w==", "signatures": [{"sig": "MEUCIBe7DU7qqXkODxZNpfkJQuyJ5J0UwbmBGhnpkGjTBbKHAiEAhnOccWlrEYtDJlcI1xOqqGsUbleQ8qJQBCjnylJYVoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10982}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-alpha.14_1733504020417_0.36132300363942016", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "f8fb73f9a270e87e7e1cb69f7ada6c9d668c5b01", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-iknVD8aE23DbsGNGZyEhvsG8uvCv5YJEOBcsCMSuEeLD6FCKYAUq1OkvWBieyhA3MILZwYhFi49vR0EG5vI8Ew==", "signatures": [{"sig": "MEYCIQCYxumAw5H5hYeJPMRCfRPAaO24QouTyBqU4S1QECBckAIhALF1JHBAwgh3XTUZp3jRgputCpdUVDF0UZNR9sxlpZto", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10982}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-alpha.15_1736529844363_0.38310461178446475", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "7b6bde0757897ce49f9b9538a8cc981867401507", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-+QBeZ+Aay7jda5mbzNvhad5+GXVYTxZWfF9Bm1NPPKWU0Zvb70DpihSi6JVNm85zfs2tqlR+1Dzxi72ppZ06Yw==", "signatures": [{"sig": "MEUCIQCzVyADHSY30URpJExkU23X8FPOpIBFANugft0tbayYAwIgc+Fq+k1f1XyWbtPVn1weC5OZ/YQaLjt20fnf5rMG6tE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10982}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-alpha.16_1739534321621_0.18134889248869435", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "94885ea8a19a87ca97a7c401e8c994fd38647cb8", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-L7sgKKBWvIdSbRxrISB/fl/uPZQbvCZdDTG9B8MElZLj3JNtZuJxy2sMz9baeDSyIeeTjbLjSF3oj/zKTvUE2w==", "signatures": [{"sig": "MEUCIQCcNnVk4kEbQ7inOS3eq1COdRT7uVPFg1lzQBNUGwiXhAIgTptw5hIGqLH5rjN5d6CJLtOxPQjOj6fGBT9+YEZ+gpU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10982}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-alpha.17_1741717472124_0.24467646858747982", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "7.27.1", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "43f70a6d7efd52370eefbdf55ae03d91b293856d", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-qNeq3bCKnGgLkEXUuFry6dPlGfCdQNZbn7yUAPCInwAJHMU7THJfrBSozkcWq5sNM6RcF3S8XyQL2A52KNR9IA==", "signatures": [{"sig": "MEQCIAT7YDKQGWxWtJNPRgLPSUQWvETyfioCbtoxlj+bggAiAiA6jpuEAqVOly/A/RPCi6no4RDW5LCKJW61ucpNSWsyHA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10842}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_7.27.1_1746025711730_0.7693402791024939", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "dist": {"shasum": "800ee1c2f3f11b3ec68515d86faca64ad58520bf", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-kc/uJoddqjuI5L0eKKPLPc0jpFgatM7AsoF4X8wqxmw5Awk4jYUk/7BjvGYw6RK9zOne+nNUDyiCEBndnV4gVg==", "signatures": [{"sig": "MEQCIHSS4rXfIi2zWLOAH5VO1t54t05kgB0jSGyWUEKRGrjKAiAY0BFJEXyc0sLvO1dIdirJJ2ggraTj461VnV6ka+CcGQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10956}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-beta.0_1748620244928_0.4990801830128446", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-bugfix-safari-class-field-initializer-scope", "version": "8.0.0-beta.1", "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "keywords": ["babel-plugin", "bugfix"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-bugfix-safari-class-field-initializer-scope@8.0.0-beta.1", "dist": {"shasum": "0f5b94d8e07f8a5fa11600150e5e5aef29774f88", "integrity": "sha512-IgUX25TEqxr94Caq3BfQbXB27KEsCHVydbdkPSIahgrlhNWg4kOW/40YGvND55sAYysYbxzlGiWLP/OE5LgDng==", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 10956, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGXnBAFevTPaM0Rgj7eCI4o8497Z4r/AkFgVp653FSQAAiEAxB2YQzP/tremufrnVxhwFMD7dDwXJ09nd9uJEqwNAXk="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-bugfix-safari-class-field-initializer-scope_8.0.0-beta.1_1751447038925_0.9002052508429377"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-07-26T16:59:19.910Z", "modified": "2025-07-02T09:03:59.308Z", "7.25.0": "2024-07-26T16:59:20.193Z", "8.0.0-alpha.12": "2024-07-26T17:33:10.601Z", "7.25.7": "2024-10-02T15:14:29.183Z", "7.25.9": "2024-10-22T15:20:43.492Z", "8.0.0-alpha.13": "2024-10-25T13:53:48.596Z", "8.0.0-alpha.14": "2024-12-06T16:53:40.607Z", "8.0.0-alpha.15": "2025-01-10T17:24:04.548Z", "8.0.0-alpha.16": "2025-02-14T11:58:41.762Z", "8.0.0-alpha.17": "2025-03-11T18:24:32.311Z", "7.27.1": "2025-04-30T15:08:31.918Z", "8.0.0-beta.0": "2025-05-30T15:50:45.093Z", "8.0.0-beta.1": "2025-07-02T09:03:59.089Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-class-field-initializer-scope", "keywords": ["babel-plugin", "bugfix"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-class-field-initializer-scope"}, "description": "Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}