{"_id": "@babel/helper-skip-transparent-expression-wrappers", "_rev": "42-d02a3651cc9f620e61366e4d9a7da3cc", "name": "@babel/helper-skip-transparent-expression-wrappers", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.11.0": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.11.0", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.11.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "eec162f112c2f58d3af0af125e3bb57665146729", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.11.0.tgz", "fileCount": 4, "integrity": "sha512-0XIdiQln4Elglgjbwo9wuJpL/K7AGCY26kmEt0+pRP0TAj4jjyNq1MjoRvikrTVqKcx4Gysxt4cXvVFXP/JO2Q==", "signatures": [{"sig": "MEQCIBWg/OFGrgbRS+dd+JhTa/IxVvfC2O9lhdqL2M2vvMxHAiBHbByROiiZdf+U5aSsYVFWN5j7xqSYLhWRPlzsz2X4cg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzs3CRA9TVsSAnZWagAAnrUP/AldnaXJmK8Sw/glUx2b\nZ+njzJokyNr8oe05VMG+ly/pM+o0c2fHP8mUuz6nBx7SG3r6LB9Ag20KLNZI\nM5DTGEQp71RDSLMw1t0OV6dVId8KGCPMEBRMfTea/cPRpzSwkcHYWiMNbB2o\ncmwaLM1vC2RC5Jxn+UjTnHBWJhLeL+elf+BLUR4GF/Ymwczo7Dk/4HkO988p\nts5Z/Y9PQhvs/+YZ7uB4bm6zOpO86hf3MMuHmuf12KogcviGNYJI32BM50P4\nrvnfSxNXSGtGsboHKSmIPTZIfPxIL2jw9GnavBslEj0IjR3Zs5KUQN+c9cbv\nQEAoKe09escXEhfS3yz0IfrmvLXCvfuNBcsSXvLiEN7tCk9pSQwGLK0WdS2U\nmmWbPwhCKzflFGp0DshYF5zHrWX7etppsGtMTW5WwFuf3qhDa/IY/rPQhE2n\nusu7p9JwULEMGR8aIwl0q0J2dyDCsHA9ntzzKyLGQb0/pin5Iy/pbh6cySjy\nXHUoRQu8Y/U3G/MP4QzLFEAA89G+7r3QsyGoUv3XcKkT+ZTkZxkRINuBCpHj\nPoh9l2p5GgWnRQwY5YWCINgymL5I6Af0rvE4KNB/gEHDh2mTvZ8qT+MATV2Y\nqnfyVloQcLMSkO2mlNuWYnbbA/OyeGcqyZBAxR0FgPKN4lHMNN58JXWGdIJ1\nz62c\r\n=MjY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "_npmVersion": "6.14.7", "description": "Helper which skips types and parentheses", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"@babel/types": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.11.0_1596144439346_0.4443770267811984", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.12.1", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "462dc63a7e435ade8468385c63d2b84cce4b3cbf", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-Mf5AUuhG1/OCChOJ/HcADmvcHM42WJockombn8ATJG3OnyiSxBK/Mm5x78BQWvmtXZKHgbjdGL2kin/HOLlZGA==", "signatures": [{"sig": "MEUCIQCJJsFYcsmAUfW9EHewOsi0o8Db08J6Fild2kpK9SUj6QIgGLv1rgbf/o8XXr/a6VbEvr89+iIcbDh15CEKDYEjyVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/8CRA9TVsSAnZWagAAyasP/io1sqmYCqmj6Wi51vNm\nC2IhtKHuoEGtEF0ldDnKOLcStuCybWwWcWKzUKzmMsATV2PUZs/cYiRAAXu2\n7+yFIhiGJeeAZv2RHbXDaxGBNq6DLlwaUZckkvVc/5rAxioxJ8BQ0Y27llIY\njgKS2oL/T+Q+QW0oydY1+dw7PU4lD6mreSf8/0DUCqsAEDnzUjTPuinDT2j9\nikrTKVbPYgadH4K6XOMvagT1bLkJNEEEqnzc1UlQIXJVzFvWE71Y+EyH5btM\nzqULa+smZJqNfV5W2/PmKXEnOjUBQL2/qJFmRl1GWaK5wwdPBWoh0QZDqi3p\nrBeCRGUbt90CX3GefzkAZwmGejpO9AqcT7un+LQseQlzFn6dmKZ0t3AmR4VL\nEM5MKch6RNG7IngKv34UwY3YiqBWudUnbwcDCME3FWDvknOL9alsRQsHbfry\n2MVs/p+XH7UCof6Tf9JvrbQAKHEyUE0ix3zLIdpJKniokd8XSWkEAIhd0lVy\nyJcS8PAGcPV+BISDE7RRBRJkne9R0vHn+OsWfrexfb8h4scPdKSEb1NvwFDG\nwqHzozdZLNiOQPiNBBMAA9EFBvFBz+/g+shQQxPJzh1NVM357mHeZiiZVv47\n8qzskK8XlE2kaQfe8unfvKpfHzqmi4dXLdoNvUz4bxE6qHn7WsORCXgylN/d\nkM3h\r\n=JXND\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.12.1_1602801660159_0.5805440487798985", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "96f486ac050ca9f44b009fbe5b7d394cab3a0ee4", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-dmqZB7mrb94PZSAOYtr+ZN5qt5owZIAgqtoTuqiFbHFtxgEcmQlRJVI+bO++fciBunXtB6MK7HrzrfcAzIz2NQ==", "signatures": [{"sig": "MEMCHwrOn83X7WxLJ3qPDYLBetFp+tZPMPw1LZvLoUGzxVcCIFqb+SZlhHQU91NtoHDxKIv5TuSfoRr3GHt4tbfxdVEK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrbCRA9TVsSAnZWagAAlW8P/3c0IQhJ+mbB0BdPPMTR\nAo37nLLEStIXCBNqPAG2acN19pRvUHruwHvQeTHQYL5Zij1WZNo1FIRzw2fd\nRbXMXe/C+k9zoaxzA5DhknYaRNQlYN7p2HLK98oTDUGInznQu4ZRubp+rgXR\n8bI3i3Xag0kgZXwwVP6YpgxIM1sCNa39LCyCjAHDza7dnG9UkvhT8TYU7ewo\n79AzI3SN9GRoBpBliVc5SshHXdAtjyDQmm0Dm05UFsMw5zxYHXNdtyD5Jqin\nRP6sfCyfwvhIv5J+jq/grnXp5dWsSa580j1w7hlmpzyra8mGNN0fVN3pVhVv\nl//LB85JdD334Y5uWmwCkEd1zcHR4cBRR5FCC93LMs808evHeKFivZRVy1O2\npb0Ob3xnrglRSaZdfUp0aBHal5WoMfHTpp/K7pJ8lMfx2+Bq9Si9U/BenyVp\nULsrBESnXDMccMuTZwuIqWcVune8XDCVbOgmoibg0DcALtlXS8nvAuCE4zM1\nNKre2oSxMzpF8nShplsNzGqkKcWFJWfYtLFEaQVJLXt54oMaMM896J3E2SBu\n7HXoEaVPtpAxo12CBs/MQ0YuX4/srgwHdfxhOUUjB4SNxEVrd1y2oyUOY4yW\nhw3bcuNAzvV/cLVHsNu2gl/kjqumcQq9OG5JWLim5KWOmqcjgVx+QKWU0+QA\nxbeT\r\n=78Zw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.14.5_1623280347615_0.0806834335561204", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "707dbdba1f4ad0fa34f9114fc8197aec7d5da2eb", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-BMRLsdh+D1/aap19TycS4eD1qELGrCBJwzaY9IE8LrpJtJb+H7rQkPIdsfgnMtLBA6DJls7X9z93Z4U8h7xw0A==", "signatures": [{"sig": "MEQCIAcGtJeuAIy7+4M2rUNO7nuKyDoKwR/OkehpRkqsX0C/AiA1Iw81NCbrbYw9fD1z/6pQ+OvOQhLQA8tKVhb2hkvRPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3018, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSZCRA9TVsSAnZWagAA1vsP/jDrjhd+YlJJJZ2OdgCg\nqgbW2ePIiLyIEMDJqih3rPxBCMLZ7UNfYc5035FzfyBj7A/3qE6dLMdGncbR\nFB6UFCtgc05XFHH5GK7B4d5MxzVbGy+3+NDgTBf9gdoGrlCBnfsP8FTF3nLO\nDjwITVsxhjy3jXH3EridCLytdohjBEZ17+WoGRteY8hmp48war6vBLwGkM50\neVkQqZmjASnPcpP/OET66lSON5APcV/gLZK62TnR6QNKunxb4vLa0WY/vtDP\nSBBsgVOvyxpTZLw4FcODYzKvVKnrS0KSJGw2PNtX4eC3U3R1mFZ4uYsuEbp+\nbw/WxqOLzZqeNvWFUrnDSxbbPCtXTas21/0Ll9fiLNpZ7wjyUQ8qil6itILt\ndKtADsKwIZiyZyeA4gkNY415GU75sD/tCfweYziD4NwDXvyx4f5bv424wXjk\nCAtBcIygzZY8P6r4vOoMwQsYQ7G/FpDq1FzmOA/m4o67B7ikbPlT3Tf3WgZa\ngX5Ow1/ot/BvGnrd9aTIrQvHBc+AOX7nexz36ZiQYsntAC50oyIP48dhSLQJ\nnRInZjVpe82sLPPyW2MApHkYHACerMQIbk/az2VD+7TGluvCyfkRW5hcqEaw\nSPvKBi312/41rMSCxcqCCvRW1ig8vGUoWvpFS9p+DLeAhq9C91lcdUf92SHo\nRryZ\r\n=4McV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.15.4_1630618777146_0.5872838864267615", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0ee3388070147c3ae051e487eca3ebb0e2e8bb09", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-+il1gTy0oHwUsBQZyJvukbB4vPMdcYBrFHa0Uc4AizLxbq6BOYC51Rv4tWocX9BLBDLZ4kc6qUFpQ6HRgL+3zw==", "signatures": [{"sig": "MEYCIQDUPoMJvdExSis6loWb2/rpd1Ps2XppMi4z/vn5Yy0uYQIhAJkmwaA1j2KGPdUISYyRU/HdHB4Pl1LkpsuvRdm6FVzz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDDCRA9TVsSAnZWagAAaIwP/jC1+KY+UpcEH9azBZnl\n8gXeCgl5TN+K4Km2R/H2BR3dirv8hr27JZZixMdvgrE3p+HdIS/aRHfEfxb8\nxgvomEI79HXgInu8lHxetVrQOM8mkovg2HhWeG/7Y9ZjWJphB8lzrTD0i4Rb\nwAg+AZ9btiz3zp8HhQj+13LuqD9aPLf4ki/snvapta8ieWavc8B8U0rDPYY2\nj7rAvUMKVGc/3GaLJz2ZFR0c8N3g6VJTCZb+uQ4nVLZwqwejpBPQGf31m6Gl\n4Bs8sTtVbCWFNOc8KbSKKOKCUYDEYBNC+wm84pwmxC5S6eQSjaBXhrbVbK9v\n9fNAmSSx8A+kNGoYTMBN80jld8e2FabDV4e0Fs9JH/g/ZJ09MlUPlT9FF0jR\niVfIpd91a4OSU0BdxM4beu4xildygysJ9WLU6z/FKwYQC9+lAziJrkVBr4m2\n7hzJF2QZY/ApWaJBSg8q9Gsr9JQeJ7Wtn3mmb6yMW3NSq/31/T0GU3U/rHYV\n7jFZ8oVVU/5cXsTg4WLYTKY0/aytwFNVWKPqjCML3Of8Hcz2o7WKhU4MuA49\nhxu+5VM99hmkjaiPIAgB7+3jswn/kOg81GFmA/fg258S9evsQ0Qw8QwDm8ts\nG41I/VXnVViTSyYWGq6O9Roej44th7ijMCmhgZbCN1xPBISVrJbKCxDdC4yc\n0Im0\r\n=skGZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.16.0_1635551262962_0.04526206667058341", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7dff00a5320ca4cf63270e5a0eca4b268b7380d9", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-4KoLhwGS9vGethZpAhYnMejWkX64wsnHPDwvOsKWU6Fg4+AlK2Jz3TyjQLMEPvz+1zemi/WBdkYxCD0bAfIkiw==", "signatures": [{"sig": "MEUCIQCGpwLXhfmf9qbbOB4x57bG5PiStLZiquPENABkUFM5AwIgWJSjYlwwvPIF55+wW+3zspUa8jHYG1L8RESD+jugpcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjQg/+LaBbXHW2SCHX8I1IS/56DDUai+qgDgU3oe4o9ypFAdgDZtRX\r\nrLco2UH2+UgIoILN3cX6ninwaH/wwlcVzcHze0v84anYL9QqMpouHy0rVynq\r\nv+svCcA8JUnv0+m5TJ1ggxVPZIM/2+Rde5b9Ph25xbXwWPsl7Hfy6WBUsi6r\r\nJrVBrgrCtBBXchsGgsx1CQQ0BXbIoThCd7pFV/8k1AFuxkvI/brnfTmfp0cZ\r\ngrkDLMpGpgJy3rCIDL/OcMFrw2WJztTBUYNWGJtk/nG1HYoK/lQGVQS0jOWh\r\nBsccBrDup4oEjyRX2f00r1WKfzkneEEdxYcAZPvoBQ4zKClmgHXY9cgV9YGs\r\nGzOed7AbYrC9j9sg5ZY8JuC6w2z9hI77l7YEqAL9RjHP3vH8l1BG+AA9Zu2r\r\n0l9hf/4Q7z+CXtoWgs7et60RTdCIGZSCkGXPdATthaFziFpLTffZ+F74UAsQ\r\nFT13FhT90R8bmKFAK6UCkgtKQmOrEqNFlUV7UbrxptJYtZz9QcOYaJj+XbpS\r\nkrNJzR9tiYaOBq+h2o+PrOvmQABdf9LZrizU4jCu3H4WuugjRr1j+i0pTxgS\r\nVWTXnOQergdbfB6mpdU/bch5ZBVJX6BQLXuKJO+lrOaOjSfKdoNEFPVvI00o\r\nu5jP5sn3YVy0vC0TQmkuo14lzGkCe2exmUs=\r\n=5BYK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.18.6_1656359411320_0.6798539614416255", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "778d87b3a758d90b471e7b9918f34a9a02eb5818", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-imytd2gHi3cJPsybLRbmFrF7u5BIEuI2cNheyKi3/iOBC63kNn3q8Crn2xVuESli0aM4KYsyEqKyS7lFL8YVtw==", "signatures": [{"sig": "MEYCIQCtvRltoLYUA5JFlCD6KXFPkzu6//rVrGrUbDZkCIMpTAIhANMYm1gqzY15l8vOO/KUPTN07lk4EXn7JU67qumiSYXi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolXxAAgP10VPQp5Z3mQsEypkXwKApntFqmzLtqDAyQ5SDbxfQeWgvF\r\nuN0fJG+fiFmiIeM/YzCfvcRzXiBIPayI0MFO43Koaa3DcbSVCYi8jOuMuS22\r\nkxjiQuVb0ReToRQP8u1O5UQs5MBCJGQluy89GKztoiCsKImtsuCsJpD80DIk\r\nOCfbTo5qBRmzYdv6bJaAXi1xHHLBFi/eCBSJleDkm1qn3EDBXeYwP5i6E0+w\r\nO37iK75q4Hyr9dkkwEMOLCpjzFKW0Q6fk55i3ItDFrm95k4R68V6OZxvSR+X\r\nheLO1gOxyRlM1fawONkCPtOYFADLoONPBXL7h3UTAxWwqaXyESP7QwX3QDjK\r\n6qAVKwfyYJ43Y8JmtcWt1FKs3P96js6xCJ/g4jYQsZfwJxlW4n8poQQPulJu\r\nPTE2LrjXDeS/q/62aZBRsJ+jqiExpCq8g7e9hiGGDoJxN3dHGuqwvlsBgLyj\r\nxk0DecMtxcSRF/gRqe5WdDz2ivd+/Nlq25lxgCLXmZOutGA4eb8kX51hlgL7\r\nGK+CZsRDwRZt+ob6913QrFIMDAZj3dmvYS9Zap2PWTdh5ITK8ZkgqoFIashi\r\nKXRciHgI09ZU2l4t1pWv8d5/1Q6YKbBGBt+Kalmiz4gFNLHpm39X1muyNYdK\r\nxaNfqcce8mF6jOEylqSI3iWXrIqQ6O376h8=\r\n=972z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.18.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.18.9_1658135847883_0.06367197016359483", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.20.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fbe4c52f60518cab8140d77101f0e63a8a230684", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz", "fileCount": 5, "integrity": "sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==", "signatures": [{"sig": "MEUCID3KRmeZNUf6STi1lN20q6lYxxkWmSQ9YOI3P/+AZ2+RAiEAlwvkZgin0QUMwwVnIprrYNbkXwwXxwEtQDWordMJIpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroVA//Wb3cACDJCPh5dktmafhOmJsM1tRm+8ocxl6T6oduVIL3gdq2\r\n0WR1vcNFlTxqIkQ6AjswvpeiO2mItvdKGnqh7N0UlKIr3WINq+Mbj6NegnZA\r\nwXe8y4XfhJkObhiuNYzL4w9bdC8nP41bpCV3NjukfixYMXwQ4uJmozDWnOVJ\r\nPixU2SLdWrNiAVCtwNsxa5Q1Nm/dCjDgeSHamudgAJ2GW84pGDNzOGL2RXM5\r\nX7WwoNMjFF4u8B/DlNFQoUcVcqYu76fIaW1ViDieQXNiLQAPu7JEonOfJwQw\r\nmGTJksoar4iq1v5MxYmJq1+RqZ4J7/xkbZ2ttLTSsH9dSJJ6wlma2I+qx15Y\r\nuD6EZ4Bt22gqLUGuHNkTyJ0lGR25sNeecdda5wC8lYZub6l1hdiUpJHZOrHk\r\n8ePurLJFzLPhE8WXLTOP6DHW/1zfAQvaW6BqV+HBJvDrKC9lAo0Z/crjZCud\r\nER8smdtTEI8uXQw6AbPDf7mfhCsTZ5JPy4XlNtjnCVlkWAS1kSXSIhtMrRuT\r\nFOqaZpCwmwssThbnDbHIUW1fZUL29Gna/sPHgzO7BcEqToX2Egd1+EHo1gUn\r\n1WdDZ5SlEiyRhDr1o5c28QuUNToTqo9//vjCdlHzW6oIBOOW4P+tBPxiMd/t\r\nWCx0EOf4Nq2TnJO7zqo55txBugwwc1+xEVc=\r\n=Sj/w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.20.0_1666876759529_0.2715513799472271", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6ecc513d77532b9d492c8904f1346fe6cc6bc42c", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-e9gheHx+TSlwtkTfYpC9Rc6qm2PBIT0TqcIn9lD36hCrn++1EVDK5CC/6hA70vEeNeRPvWGiklZjhKWdXPCpQQ==", "signatures": [{"sig": "MEQCIBgCwBaHiuVhhWZsPvDhHVjdy3c/WTNcdzw7R9ulx72SAiA0CbeUEB2wJtvjFztrGmjDs/DII6LK8zNDl/+JvdicXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqx3Q/+Ig4G5JaHmJHcSkwnTWl9X6y+H49sOE3CsUvrQCjcgF1qII6r\r\nn3QsVXxm7QLwND6KiZQzx9iB2YV8omITmW4nMAEhzVv38GTWGqHTKErodwbJ\r\nRC/Wqt9r/UrFdH+1aC72JVQ+zM5r6Fd3EelyNp6Lyu0LagPnSUBcot27Rp+G\r\nGviFW6Ghdl23F+OqZ2s4X+GZ9PKL+nvoEiqwdrrbMJ3bOSn85KUxvBOqf9AY\r\nnLih2jaOM/0ZAJxKZo5XLayWrzzJ31y7lH4tVrF45Obh1dO3G9AhnwM49ei9\r\nE0nYTduywpBfd0Kbvi6RTrzFMihBx8PeNA0wzydJob5VcbGI/aVQISJO+HTM\r\nE/FNpI+1Ui1ZF/kFLKKE2Mu/szL2vFMRv810vkEESbeLhjQKAyewOCbF8ve2\r\nLYAsXpXoXQRePYg1SkbOg59DglMQcJ73DwAETeF59DKHTOw/cUbA3FaLGt0c\r\nzZ5b4wjyg66DwNAPy92/xO/mCwIXUpO0P60Pws7bUUAbxlOVsYXvck8Inq8j\r\nKVIFDr7dQZVQLCCMStKBOpgDjG1TB8xOCPivzmJCx90W8NIB3cxBC5V3hzSi\r\n6Pbkvkpp9i4AesJv3xMYkNhclzkht34F6PK6/OD6jQBZQ7/CqvzPH9PoTSNN\r\nbgw6S5oYAZ+Mt2hGNWN+lm085RfuKI+k3xw=\r\n=Q1z4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.21.4-esm_1680617377087_0.6977664974635684", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8443d95a7a656827ec535f02abd022b4ec9af015", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-breun25JEJrsza+yhZUQZ7WnaH7wwWS31Ql1LXIw5FJGQT221FV+Yxi0LFQ//L+23ypl8jsvmVIBVwAlcjtXWA==", "signatures": [{"sig": "MEQCICeJ8iHcM2dbQZsNcB0RIAEcWWiBbo/WfL7wFYUCKoQmAiBA0PgmUHQxDP9eFKx7CP3S3Pwdultpky0R0zdshCaiow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRyA//a+gbj0QGFkBZS607xhHPLFsf1QCLPGgcclaemJPg8A+zaSW4\r\nRljmXURAjIwmo+4zHn9MhYnDoQXD2I6RUh/oZdDuqsuQjI7GTE017x+D+NzL\r\nkkNwVdOqSqFqVrLEFvNL7bbiUe1AoyYfEzy7VfsCdyn6Sa9d1HI9OSM7NPyW\r\nIShnxHWnXX0C2a9O82irZTum53J6V05J6WuyIVekfLsQ8HXAZdDcUO9iTqwx\r\n84r7VJU2nDyivfL7zzJKfWGGH3XkrJjDJ1v/+qFY8w64se3R+OZSizIXCTVV\r\nR/YC0PE6M6BJgWGn3EP3ppdI07hzo3c0gWt+BdfadLnJbikQxzbwklpBTVag\r\n9w6+ShGoEgywKwlNEOZpaw0Tq4aYYOgTgaOCXdHrE5ZCij4bYxfo3QZQShCt\r\n2L5bOBpGd7Xczu9Z45ABRTKmSZ9UKulR/WTZQVt8pIaezRWRPtZfhGTos/vf\r\nWiPyRPpUqJSuC9tooKHBf9ruUAsXgsU9gSrEf1HweqpC5oThmAoglbyh4jf1\r\noFU2UlG85+t/t683WGOhjhdMqNgZrOyDTUGLpcVTw4cOGrMAHAPfEsOerDKV\r\nVi8OaIkJWtBmLp6lKEUl9//56dktCn9WH9AO2wckOmS7xfMTOtVv0ZOP15hP\r\nX/UlZZi2ckWj0BlOAf/nQLXlpwWgZD1LiEA=\r\n=SN5g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.21.4-esm.1_1680618089777_0.6537504451704801", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b24d30f52525f97cc03606f9d225498bee9b8e88", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-Wd4DRMJXp4fUckbHUl9i/vDsQlLYf2DQE/YphMo+ShPtcv5Q1kPdnI2GVchLghLGQVF6uKELLp1SNEedNfG5zQ==", "signatures": [{"sig": "MEUCIQC/r/LUFI9Kq5LOyeysNPYhsSaJlzELtJMScxHPpplrHgIgGwNim7X2i73ju/KosZCCbSrTLRo/xeMho0h7Ge9rKdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDahACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNOQ/+PlTLq/p4HQM17QFOr3c+IqNk8dLc7gBeoq3JX5cR+SuN3une\r\n6oQufblsoEBUo03NT7g2nLBDxaoTo56hCQgVnEer6JDk7qOLywOotPafc5Ek\r\nhB3DQIeHUvbVvCkYsjoTUGwnUqu2kcGAAnAVJ5mV8H0dnTes/SJZ1311YtPc\r\nK7sGyF9e5oqmzNwEDzGUQq2rj8aCbs9uF/UXrat0mm4yxBsubtrV9qUS7pu7\r\nCKy9eq4tkFD63JEjAB3moubrXVXL+lhj1KQVlcZn1EgvUgMaUmWLzDl2vqrU\r\nbvoD9UnrWG+rBzrN2CHrCWa5sciN3skTuNFLJ2hdBkr/pOCahsz76HSbIR3y\r\nJlOY+WSOHGouD6msSP2HJyf7K7xz+mCoEgMzpF0atHbbssPUDokTuoObd2Ka\r\n0lM5mTA5P3ykNtdEtVeAn4w12trQNHonbsMeRZkQKzOYnzkKf+bsTbxFyvxO\r\nAzc92KFFtdAkuxefYwiAMyleqAyD9J7o7jEu3E9hV6377R1nwEqoAja1uvPr\r\nN6K5GFxEJdeTg9YKnqom2l27Ygdrc1dHmYpM+NdJIXOhjBNoN3NNblCuR5I3\r\n051kFSK4xf+6zex/U4GpFIvYVT72q7VcJmxKGtLNKx/DInYGANJ98d3SL5SM\r\n5ESSGnlTWtO+IV20PHNyZUasGkW2cYjKMUc=\r\n=fIVc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.21.4-esm.2_1680619169771_0.30644339041062674", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "dd91be393ddf3fb61c8a0b651514e09a77433881", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-Jw5/bU4F2hm8/o9mR/4FdKc6HmcVu9T1cQpWcoNSF/hsVg+MUdV4mxMd1dB2xevctE8IPyJBA8S97wVSq/Nqbw==", "signatures": [{"sig": "MEUCIQD36BEMtOx5nzlHwcVHK/OkNjfrgK6cM5WsRfuezD2kWgIge/EUM027sT7FN4/UhbcWHoW7cnjjMlBFtVaUfHZS9iM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogJBAAlUyG4P4qZUC5KNSqZz3HEdOIvTRCsVl1hbd75ZmstEIDyMoa\r\nOFXx5m+Vlj/fsHuyc9x5P3en1qgLsWOV+KT7ehNRa0WKA/H9+UhsoQfpzWGq\r\nLeAnlh2gLIA+h37hWrUh8z5dIRA099K1/QtV3x7e7KtfmYQdzrWEieYps2Em\r\nLU7n8Jkmn476VwG3BIg/BOhsy4/Jt4Vdu8uJAezY1Sn30yxtc8GcLwWUY+A1\r\ntOqihTgIaTlrr8/RXkA5vBQuR29Ymzf/RWy8t5vGMYsttleGWi1BX/TdzgZl\r\nLe1ekOkh40oH74mfWb6e5BbL78/YtyVQglMMki/n9tpRa2DJdmwyDOX5U/8T\r\nfgAKGLyL43qJRVfFjeAWKs5WH2n1Z2Vd0NVxL+S6w7u1av+X4KNtw0JHmMHc\r\nEpijtoq7/UJr9IV/7IxOjsmA94zkk2PDlDLunA/q4i9GHuOHxR8NIKYKKzuZ\r\nqA+0pRAbiDc3Hp9wUvJc+mjropnJI6RgOtel0RkA0rRlQqdauO1E1Csm2FAE\r\n0uYiWsJ3x0aS1+t/SEVuorMAOs3FPU7DGJUIOBl8AC19saZzomdC9df/L/oh\r\nAI7U8rxztgADSzmtgD0FBtQEZytgMw5W2lU8N3+e0x/+bzeqYSjl633aum2s\r\niEYn2dRuxjOVTFrfaY+84CV8vtCR1xegyaA=\r\n=uCdo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.21.4-esm.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.21.4-esm.3_1680620178947_0.7837257723699813", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ffb95452e6619abae7348c1bead95d9b8a3a7a74", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-m10bRe3JWSBNCsprhIGpdj6BwqU5aTFdW1pnzUg8u7V9lRs6EIohdPFJIejUd81u5BcJC87cIUldnJ38fMKNjg==", "signatures": [{"sig": "MEUCIChyIfuIMFpSXMUR+eCpi3CAt4Mc57zGSTrQIt/RCdOLAiEAnzqulYFFXRGtPUGFODk+8FjlXSmiHnDwnKmz2mg29yQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrczg/9HCyUX2Z+8gbH0t3qlnb1c4bFgxuEb8OsV6zoj8OKUQPGYepy\r\nZa74K2Anplp9Or1okMMMZaCXNeeiR2q0rh3G5dsF4LX4q3wA/5KBEFROrMMe\r\npnXeI1DCtPKpwBrQeLDSHPP+TaPI4TYfZt9HgVYUbUHsxdxDcotD+n4cGZEz\r\nJ/o0DOvqhRd/6pU6cYrg8may0EEEm1jNSwIcjFhQEk837ghuGfv8oCFjihdZ\r\nv9G1dJ5j010DtlLDRbW6pkBARaaDOjQzQPR53ard9wzGc2mcFsQFtNf6baZf\r\nFpeCJl0OsBpBeC34iGQ8KlN4oPhCXQKCruKWEWbCho/Z6aRR6/Qfvcig4k3Y\r\n4FlnBqBBosm0dKY+lHYLN7BF5OtBScTufd7U7Rzr9oMpp/ulC5+rU3eWHp6P\r\nhkgaRPerE7teY7e2sWpchaJAkrr8sLBcHgiBIVtiqyIYND8gje84rGWKiwz+\r\n3mmJnDnw66tttM6W0V6P85qd6Oa9CN2QlgchIE5vG2fyDJ0+EUYWse2an8Xy\r\nmqPvBVgarShr3Ssg0l6vJ0tQ9fxMhOCLhat/j8c3VIygl+A/WdCjWUKDlQ/A\r\nPnpgjUZUMlmPFQBmfxl+W2se6AZVbqMCWgJGkuQN9gz9MEkyvyO61MM9Agsi\r\n1gsI32tkiI8AZNe2Pfg2KgOJFCsHV6eH/Yk=\r\n=q11b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.21.4-esm.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.21.4-esm.4_1680621210153_0.5870567636796491", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "007f15240b5751c537c40e77abb4e89eeaaa8847", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==", "signatures": [{"sig": "MEUCIQDfmPsHVLNr6EuwZmWnVZ7PXr2as+ky4c5P0DcDVAxn3AIgA/ejmjd3+6EB3vfKKF/quBsyy+CA/4vNHVrlNborTCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5955}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.22.5_1686248487753_0.08137414703615287", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "996c60cc1478047376a2a3ed4fdf3bf9cca9a0ea", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-rAzFA88MzVGtgsQXPIn9cKdues/4yw9zXiJBsKu56sri4Flj9mPRDdp8P7AJNNWhJ4IbrZzLdoKuAJ9geyGhnA==", "signatures": [{"sig": "MEUCIAqtMFRV4sgqkenZpofFRbLkfiSxjAzAkH51vBV/T28VAiEA+UuXKe/zak0lc7eXxGeAyMcCyJC2GhDR/n+iHR98MEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.0_1689861605469_0.6267564918426467", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e8e21a7e1ac18f8f409151beaa59db9ef49d678f", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-0+c+D8FLls0hr7AbyIgDHuvJI5qAw03bVd0Mj7r6U/nVVteAmFFQnosvNiFNAv38ledIGG6aoDakFJPRLEjl1w==", "signatures": [{"sig": "MEUCIQDb158nIZyVM22OfkmU0DjH5H//XaXCPvt+dPXfu30+dAIgbo7mEO6WkE/+IKENs6aZXGa6coIyiOrHE1xPr7Bs+zU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.1_1690221134889_0.31769426168517034", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2ef1bfc3f948050553434703a3228e6953e9cb61", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-IIC1b6py89gCwxnWORmhfLw8zLscxXkP1kjkmERFm2qw4IQbQZs463wx9bjhbOxNGneiW5VG28lg6TzUKecacA==", "signatures": [{"sig": "MEUCIGaHSvYJuuB6Wyfb7EI4I8V8WY7aIkI6tmzjYIi8yn70AiEAov+AXiR070omehIsrV5Fpii+4U+D57kxP4sIIAm4PXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.2_1691594104775_0.6948284825311393", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0a2f18c170ecc7f2839863a4e632982cffbe2b02", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-RngPAkRzjzbxCKBjsUoK/kerr22LrypToHq8I82aFjM1u+Nwd49AyZPr8bC9Qvp14ESUFJmBH20D3C5+9zlNug==", "signatures": [{"sig": "MEUCIQCCAyufimf6JXoO+bqb72MQ4hglNzsrAWliQCSJBR4XwwIgVsmNTjidcaEq7nMiqIivdXmvQN4vluF9/s3hT2dCZoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.3_1695740227959_0.16851648869930647", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "dbabe7ce301fdd878e34f2ace7b339c0f5de2ad5", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-DpdVUCK9y2qomxeDFm9h9yIjFSEAyfskPdnRzjcR165rVOe8jFs8nMLxg8cD+rtICc3SmMEvvComvP5OcAIS0Q==", "signatures": [{"sig": "MEUCIFKnx25gQoD2D4guDXp66zRcLRwmFKpxPtKqlsQZ5A7aAiEA8zfrKoX9jrbYi0c+lwr8ALjXT3oPYTB24aBx8I/oaMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.4_1697076388881_0.3383431493127871", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0ebfe5e00c210fc5671335b942d1d7cf540f676e", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-w5bP1nXpmYoWEu2jaacWc2t8yrPaqv2djTHfP9In7MYenl6Ap/eJF/3ZwHF5IqvdyBd/fyZrGQZQzJU/joa3Ag==", "signatures": [{"sig": "MEYCIQD7kaZVdShQwfYRGpizToFQVIvP2aaVe6xfgJskfl7FpgIhAPaD/zciera9BgmjHrYWI1zFcEvlQVXA0mIIPMG0WA7P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.5_1702307948779_0.4922181260140517", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "98d6cd0d8c5c470b68da658c12ab9fd87b807eb7", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-UllG1vHNbAefs5dN7opRIWnrZ1LY0GEgDXGG4AFyfiDg+0H6pl5CwrxPvwcOvz2kgcmpdOYh4E6N54YMxEIN8w==", "signatures": [{"sig": "MEUCIGt4XMXMR94ASciVSvCl6NDCkvGTnj2++bViZqoqZa8nAiEA4tQ5PE0xLg9dN65UKsP9X0K60XVXRe/OgVNOB2XKRVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.6_1706285656867_0.10987569757383464", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a8e0ac43d7e8a5ba128662911cfbae6ec47a7991", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-An+C/T0K44N2BciUTarvrU9UQ6DM1EoShL2HOQr/DkEA+Y5DMBxyP3vGFRJnYK9VGThrjlafAY1uBtlzXhA0oA==", "signatures": [{"sig": "MEQCIDKTOzyP7YRTOr1UCcZ/L8Ng3khZOBJiX+oLUw0lx9c1AiAFp8pGndMlGeXx3d4t7Lpa0xdmBr5NzXIjNWewU2D8rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.7_1709129108318_0.9785495832793354", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "565c78718409263ec60b17735ebb3bfb3bab1b40", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-I+4jczNCSR4SmoBwpbdzljk8QCzT/FcYS7Oqbqw0Q9OqJSklchHZZvKvjPT8WHZCy19p1SYINFOWxx5aXZpNzw==", "signatures": [{"sig": "MEUCIFDDDTOSpdGFQmGJHm4qID3rVlPdIjA/327dV470IbaXAiEA3gGKV0Lcf+nFrUtnV2NWaRkHaLByrno2XugpiFYPTmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.8_1712236799140_0.43693029541128614", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c47e9b33b7ea50d1073e125ebc26661717cb7040", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-jhbbkK3IUKc4T43WadP96a27oYti9gEf1LdyGSP2rHGH77kwLwfhO7TgwnWvxxQVmke0ImmCSS47vcuxEMGD3Q==", "signatures": [{"sig": "MEUCIE/WNT8fVzh+rQ3/dmUS1Rd9yC0GeSvXA1wwWfLkS/5fAiEA0poccTEC6sn2I+y6lKlhiQhLy6EgmIMTriETYqE0LB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58555}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.24.6_1716553482544_0.9914436322787243", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1938f401bcb3fd1f3d2bd112893fbb78aa361be4", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-GmaW4UmqaXJhGEX1je9FqjqgZTNGHyvr+vMMqHs3T7LbzqaFqAhpdxs0QP3hWeiPWPARVd0qalJGqkLOVfAsjA==", "signatures": [{"sig": "MEUCIGfOMcFxZsiVvk0uUvfMYqhfg6BH/5/oPUe73aiFqwuQAiEA0EegpiXpOuNQQDORE0EpCuiIPXkz5B9fGWaiFMEP3po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59129}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.9_1717423513957_0.7174756220335763", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e2b46f19a22435397a6ed767bcb6f32af4b80632", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-Mm167DeNH8oWjYfzT0Nxz/4EMIscrrcQrKXSABKsxMbFKRNorrSJzqgQR1nbnfbWgA4mmZPMrJWYe3yPdf+cpQ==", "signatures": [{"sig": "MEUCIQCETm9DIPRO4O2tCp9t2FGdbEfd08IgY1/z2nPyOCtnmgIgZFAOwdT66PMTXvs6NwMIeIibOuF4Xz2iDih2KJ8QBWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59132}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.10_1717500032948_0.2803848720031876", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5f8fa83b69ed5c27adc56044f8be2b3ea96669d9", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-IO+DLT3LQUElMbpzlatRASEyQtfhSE0+m465v++3jyyXeBTBUjtVZg28/gHeV5mrTJqvEKhKroBGAvhW+qPHiQ==", "signatures": [{"sig": "MEYCIQD0RrcLdgmfrH4zsfaCGVU2cV9HbCsPERLZ+vcsnxjErgIhAPeKmN5iM2761oL/V0IsGOhAD84cL9SiJQ0qowGPtDWJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58528}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.24.7", "@babel/traverse": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.24.7_1717593347211_0.8674947263920856", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9f8f6167de6a733704cf93b248cf4f3bec134337", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-exOlv0zti28hLtUS0DdLDb2JbOQCBoMhZciBpfHWPpZPYdHVVzc4sc3YqsZro5S3Zr+7HgGP+G4hoqUC2TSImQ==", "signatures": [{"sig": "MEYCIQDpIqETVeeHle0I1h4vriY6FqU3GNALelKlG6knyu0xPwIhAIVSPKZVIPBYBYDRhIgkWncsDUp6bSADxSrXIWZk6iMd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59021}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.11_1717751757566_0.3274442374826161", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a91a2fcdf48500df0f2aa582c371ac7be3192128", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-8EcPc3KAdkqOO8y5POfgR8P0ljEC5ft4B43o8paJK0l6P1Ouo7+V4VrY+3JMjiSKSaNfAl5ZH2gwiptdID9tJA==", "signatures": [{"sig": "MEUCIQDDpp/edDEEspeQUdS0A3lcGnvdPILgzZWRNLOKN87WEQIgEDMIzaG3bpaTCQqkh8CSyT0paYbhoExxtXwU9w/6pcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56766}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.12_1722015230337_0.7176868892101549", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "382831c91038b1a6d32643f5f49505b8442cb87c", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-pPbNbchZBkPMD50K0p3JGcFMNLVUCuU/ABybm/PGNj4JiHrpmNyqqCphBk4i19xXtNV0JhldQJJtbSW5aUvbyA==", "signatures": [{"sig": "MEYCIQDZY1+oLYG87Q53wB4Le4SZkw7ABn36I/YlRx/jK4ywiAIhAPyFn5xMouUA4AAQr2HwmGnC96QDjMjd9reFNIB2HzuI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63795}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.25.7", "@babel/traverse": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.25.7_1727882117937_0.7432360209551194", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0b2e1b62d560d6b1954893fd2b705dc17c91f0c9", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==", "signatures": [{"sig": "MEUCIQCqfBKHMA7l8tsMceuRSEtoym1tjw9/xV4xd1/VBP0IIAIgOwpKp9TjTQu6eaLXcypBqG+jwr2eF3kGmD+A4TZF1gE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6001}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.25.9", "@babel/traverse": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.25.9_1729610493702_0.2556083689911852", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1980a6bbfea26e846f1ed0964244c67865a17be9", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-/KyYt8vVvVB5SwC+0WL8PhLddHtIIHH4qbTCLNg9r4r2T2vjiLiWBxu1GySKbw+WFsGt8z4OLcMulC42aTf9ww==", "signatures": [{"sig": "MEUCIQCeMuX6SP1YyRV4u7xmBSTHkA43md2OQkVDh4t00A9CoQIgZRuv82XctDo20fo3+naenNBLiq+ocvHPXTRdRfPikag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6605}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.13_1729864474076_0.28135318746641813", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7be3dcdd8b391d4b1c444b7efcde3727177a0f55", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-l9zF19Al+awjtZxJUl4Z19K8EjrhKNxMJJ3IJQT8YIj8nbQ73RXQledW3aka/voj2w4xiqkRV8RyePl1sCJg5w==", "signatures": [{"sig": "MEYCIQC+s137MvMD/rNV6wY13q3U+FzwHQ1b7Btyte4jn2uOKAIhAOyH8FU8JyTV5NhZsOImX7FXZTDfU9VFg3JTOCQ7K0Cs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6605}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.14_1733504062946_0.7437522998011148", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "92d5ee22332591e41ddcc6a823a3d17721dc733c", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-Iz9UjAzwScF+9YHJ8iYHpwFQqGSa3sdqHErxA8oSrFSH3pfVJ48npSp5/QWYtAQ41fomMnxJvBuo7nU0GWv/cw==", "signatures": [{"sig": "MEUCIB0oRbPe7QXT78Y4/Hq/2TlTgWxdcC/Hct5UEXhQWHLjAiEArjk5y/dRmN7OYXKLtPM35HhtJ35xigU7/pHIHgK9CWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6605}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.15_1736529890903_0.03786485576826193", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "47390d04f0f1ca87805277ad7b001a7753fd7ac5", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-DSG0jb2hLtjZ+98txJtiiIDp/WXvR/t4MhEVdfHbf79yrvJsVbEH7+s6+CTJDRrfX4lFZnNmtf/UrIPI5qk+bg==", "signatures": [{"sig": "MEYCIQD6LsY/yo9ZhHiQdvZys3Df3tKQjbSM+ABmr+fn4G7oBQIhAPcFVbgC19qDJLvRoPl01XqI7lIy7bgfkx6+Neigj2/W", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6605}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.16_1739534367084_0.8181139287334132", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0548a1e6d661cdf628c0dcb0398aeab2c5e02cea", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-Pw+m95IjuGSBPFcfp1X7cyDfsKdW/CwBlogDH+4rVgOFrV2fOLouZPAwr+l+TSU4BbXXJE2kcFZMfb8IsNXczQ==", "signatures": [{"sig": "MEUCIQDwjeBXBQIYd9xuNdrV3mk1b7ATjZktm5TWw2VuS7o0wAIgUYBOn4R0sF7JPZKaUeJA+hSV23sN+x6g5l2dZ0lBWIs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6605}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-alpha.17_1741717520096_0.3389851625607814", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "62bb91b3abba8c7f1fec0252d9dbea11b3ee7a56", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==", "signatures": [{"sig": "MEQCIA2iehnsPGP6aM4yxlMv1fDQ+JxWi4/TcVPK+qAY5fYGAiAJc9O8r1jKfd7YT+jOfX2KH0Ruk0NOVkRQwbtJDNDPIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6001}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^7.27.1", "@babel/traverse": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_7.27.1_1746025755736_0.08841287552796628", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a91466118f9627e6404568b9af633e96e6737ee1", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-CtthJS39tkukpWEIcPGjzcfVy1/lU6kBjDjPyDQVMArdW3ON5EfoiCtGQqbiv9BcxD9aFz6ylm9uoL1aLuVxOg==", "signatures": [{"sig": "MEYCIQDp9ZOi5ATq8/tjJpnpYpuR9Q5DLmxkoaEb8k2wzSQemQIhAKf91OkGA9kR4JCZOINeh6bubveLuMdUJ4CKU6U+gKGo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-beta.0_1748620292607_0.8627604497322494", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "8.0.0-beta.1", "description": "Helper which skips types and parentheses", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/helper-skip-transparent-expression-wrappers@8.0.0-beta.1", "dist": {"shasum": "a58e8b96bbe47e611406e2b46ee115deaf8da5be", "integrity": "sha512-GV+E4R9M5BTjQgnin8NwIFHnNl6F+Mqk8weI/XDG4/gOWNiT/QjTKVnHu4h0e/BhDfLIdx22gkHQHkzeTnBkYA==", "tarball": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 6588, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCWMF89E5nFcK6Mox++UMbd9ODQjaLf/LEiiIz0nzgNmgIhANin8/VP7d0G+tfqf0uRJRmh4GyODL3Tvkl2Ns6jlf8l"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-skip-transparent-expression-wrappers_8.0.0-beta.1_1751447076195_0.9219634515059447"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-07-30T21:27:19.099Z", "modified": "2025-07-02T09:04:36.560Z", "7.11.0": "2020-07-30T21:27:19.508Z", "7.12.1": "2020-10-15T22:41:00.332Z", "7.14.5": "2021-06-09T23:12:27.731Z", "7.15.4": "2021-09-02T21:39:37.241Z", "7.16.0": "2021-10-29T23:47:43.108Z", "7.18.6": "2022-06-27T19:50:11.528Z", "7.18.9": "2022-07-18T09:17:28.044Z", "7.20.0": "2022-10-27T13:19:19.705Z", "7.21.4-esm": "2023-04-04T14:09:37.279Z", "7.21.4-esm.1": "2023-04-04T14:21:30.009Z", "7.21.4-esm.2": "2023-04-04T14:39:29.881Z", "7.21.4-esm.3": "2023-04-04T14:56:19.091Z", "7.21.4-esm.4": "2023-04-04T15:13:30.341Z", "7.22.5": "2023-06-08T18:21:27.913Z", "8.0.0-alpha.0": "2023-07-20T14:00:05.713Z", "8.0.0-alpha.1": "2023-07-24T17:52:15.043Z", "8.0.0-alpha.2": "2023-08-09T15:15:05.064Z", "8.0.0-alpha.3": "2023-09-26T14:57:08.243Z", "8.0.0-alpha.4": "2023-10-12T02:06:29.097Z", "8.0.0-alpha.5": "2023-12-11T15:19:08.988Z", "8.0.0-alpha.6": "2024-01-26T16:14:17.012Z", "8.0.0-alpha.7": "2024-02-28T14:05:08.457Z", "8.0.0-alpha.8": "2024-04-04T13:19:59.312Z", "7.24.6": "2024-05-24T12:24:42.803Z", "8.0.0-alpha.9": "2024-06-03T14:05:14.153Z", "8.0.0-alpha.10": "2024-06-04T11:20:33.132Z", "7.24.7": "2024-06-05T13:15:47.409Z", "8.0.0-alpha.11": "2024-06-07T09:15:57.718Z", "8.0.0-alpha.12": "2024-07-26T17:33:50.512Z", "7.25.7": "2024-10-02T15:15:18.158Z", "7.25.9": "2024-10-22T15:21:33.897Z", "8.0.0-alpha.13": "2024-10-25T13:54:34.277Z", "8.0.0-alpha.14": "2024-12-06T16:54:23.196Z", "8.0.0-alpha.15": "2025-01-10T17:24:51.145Z", "8.0.0-alpha.16": "2025-02-14T11:59:27.276Z", "8.0.0-alpha.17": "2025-03-11T18:25:20.252Z", "7.27.1": "2025-04-30T15:09:15.903Z", "8.0.0-beta.0": "2025-05-30T15:51:32.796Z", "8.0.0-beta.1": "2025-07-02T09:04:36.357Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "description": "Helper which skips types and parentheses", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}