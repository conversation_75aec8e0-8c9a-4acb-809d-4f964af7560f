{"_id": "@types/babel__traverse", "_rev": "650-f25ff2369b2d4be5afb6943de3522a57", "name": "@types/babel__traverse", "dist-tags": {"ts2.8": "7.0.0", "ts3.2": "7.0.8", "ts3.1": "7.0.8", "ts3.3": "7.0.8", "ts2.9": "7.0.8", "ts3.0": "7.0.8", "ts3.4": "7.11.0", "ts3.5": "7.11.1", "ts3.8": "7.14.2", "ts3.7": "7.14.2", "ts3.6": "7.14.2", "ts3.9": "7.17.1", "ts4.0": "7.18.1", "ts4.2": "7.18.3", "ts4.1": "7.18.3", "ts4.3": "7.20.1", "ts4.4": "7.20.1", "ts4.5": "7.20.4", "ts4.6": "7.20.5", "ts4.7": "7.20.6", "ts4.8": "7.20.6", "ts4.9": "7.20.6", "ts5.9": "7.20.7", "ts5.8": "7.20.7", "ts5.7": "7.20.7", "ts5.2": "7.20.7", "ts5.1": "7.20.7", "ts5.0": "7.20.7", "latest": "7.20.7", "ts5.3": "7.20.7", "ts5.4": "7.20.7", "ts5.5": "7.20.7", "ts5.6": "7.20.7"}, "versions": {"7.0.0": {"name": "@types/babel__traverse", "version": "7.0.0", "license": "MIT", "_id": "@types/babel__traverse@7.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0e6a0a8cb938ed26d0d90e83456335ddc6efd2c4", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-Hzt1BoZy9RO3V/Kh9UusdU48MO2W123PTPOyproNDRTsA6WUr2i/r1de7SVClUhAt+LJV2bDuMsMvno0IwCGHg==", "signatures": [{"sig": "MEYCIQDdZ+vbQep7ba8p6m3xfkP860/J90hE415r5l9QYVLFewIhALehsX0cF0o/4alluyoJ99ar2AlC4NRLLOEJAlBb+YmE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWPn+CRA9TVsSAnZWagAAhQ4P/itnWsCcP/HeprCghmP8\nq4Ik8m4mXKQS7/b7xZXGmZM0fvU0MzE5TL+WK9cWege/IAlyGiaE8XM87xfS\ngTUsxIwe7BNk2tc8MRU6JrsMPVt1aQSsO1tPVr2NdzoR/i4RGsc2yH+vhICP\n1UrmnRFx6edNHlcPgB1mcR8V/BI4qWM4h9Nl+/yiS4leNzao4duuoz6sG649\n+yi7B/8/+dWUdBNvpXoSiv24QGavuHvk//ymKpEXVO2NRYgJD/h2XAOT8KBy\nQ3AuBTM7L+8RLjbK+fV8yiq5Z2cpIAuQBzCNr4eIPuTJlN+8F/hZAo24TF8x\nSH4nmxrLa3GTC+QnDS3MbKADxJgtgBALBT8kBaBqRY+0BgQEgjGjxY7uvik4\n9XSPyCOhta12sZ1eiN3+X6zxs77AWE0y21o2Eiuh86vU1b4tudE1zXJDXgmN\nqgGLU55nm/unxyBISep3mJtpF1QST+YlfkVz+hBWP0OBSTLA3F6YwO/WrnyD\neJfbGIn2tXVmXDsZ8uBQeNke6WOaxqhExWSECVeS54idrkeUCwjAS1Mmyjvq\nrQGWIMOOPWPCh5z9OvXFa17Ucs1L6Yse/hp/EQEYdDm/HfUdwIZF+C2PJZWb\nXmOan7pt4eQVDjkb8l13BG/ZS+zAOAwzJcx1P9+QMMn6/7CKG8N8l0lY3jdw\nETL2\r\n=chYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.0.0-beta.54"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.0_1532557822091_0.11362697663210075", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ec82d040d6f366d77729e072cdc4c4136c8332c1fd38794251c7e638a9b4d752"}, "7.0.1": {"name": "@types/babel__traverse", "version": "7.0.1", "license": "MIT", "_id": "@types/babel__traverse@7.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "eba50b11a6d1c51b1c9bd1e3dc0913c6115fb05d", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-ryaI9XccEwVYlORuKsXF7NXvsdXAa375iY3K3z5G5sDevn/+9s+oVLgbrcOSLIET19vYMuryOrCYtSu+l0t3OA==", "signatures": [{"sig": "MEUCIHFspLkyb51UQGoM51YXgOkoxcRACjzcryY17jMSsGhfAiEAja6TzTmHAyJAaysat9Dg85tPePAwEHP8QppZabWaJXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7fpGCRA9TVsSAnZWagAAKEcP/ieLx1jOUfrerqabK8w7\nPVxlTY7anhcVu8V+h3i2AaTi/9+Ycx5Olv/wIJFIysicTvUaCUbHEtFswdAC\nRP1jAbaqk7rRhgnz95dmOpMfk0MvKoM70j/UhGLeHaoq4hs1yQSogKN6oVqX\nxuMfcT/9LyZlxuf5Tu2SLb2otkhLCnxhavKLRBScjkCNK95KwUmjXCFPx6Pi\nHtCLpZVQNAYqjzVDP90RyvNqIrIPQP+V6iMM0QCU4a4Qw2HJApymvMluxf0u\nsL3leIrkRvH9xpDnWrc2GxB+LGx7nDh6NQy2iLrj6Pvq7GeZ5YY3pIzXJERO\ngf33v5rctkywZQZJEE3T9ETDxY6O9Tpnab++rRgd6+Yi26Eyy8A2qdXAIP4e\nwU5jTTAPy3o933nRckhikhXPdbU1uhtQIBSGoOAbW0W58qgXVTYwZ8LKR/Ck\n1cefSAGabF1MBqi8CrinIwTqDP1pno+tyG5V9BZwViE7Wcq8QJfn4WJtx/OY\nokje5tMLMFs9YxhvHrTOdTMXFANgczICHMSABOYr/HBJd14KevFCExGD/ErA\nQCvH5XXWVyKerHthc1mBpEbv6lyr3H8dbsLSaUgRyXEyR1d5TBjsL316lJw+\nTdCuQKo8+7DsXA/rb8Kso4xi0hjTPwwlPcnhfxgZtVvLUnuml6BZmrqgd+md\nq1kq\r\n=+hOc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.1_1542322758150_0.6291844627576375", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "49876fce1515c9116ffff984520c21a8fdff32b1913203e6c6b0d5c53c95942c"}, "7.0.2": {"name": "@types/babel__traverse", "version": "7.0.2", "license": "MIT", "_id": "@types/babel__traverse@7.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "647310e429ec868cf5b8ee6099bee8aeae00b841", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.2.tgz", "fileCount": 4, "integrity": "sha512-4a8+NaxVb66ffL1F8KEW2p9LbYsIFOwGg+OpgcOKSv+qLPFOFkNjjKpSduqxG0RMTROR172RWPuxcZdWe6SwtQ==", "signatures": [{"sig": "MEYCIQDUcnDCFesNWRWEPzJKfCdEJbelynq7Ht25z+YYUtHz9AIhAOXlnco6389gXjCGEnsnXqqFlS51DDF5Qj7RWjM4RLDV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/rprCRA9TVsSAnZWagAAKIYQAJ3QGwe9ejJucIihLcqd\nLfLD/kyXMP7gwrHLusYrLFWBlFsZvjFbenNUYuZRMf75rfCf0H9YMUUTVmo3\n8JJn4lZXJ97A4jBIzDOsgVop9DSEqpCDL02/+vEfsNh7cSBN4AyTc/Ssoysp\n6Y+7gBTBFUTQrVcu65cSO5MFyGzWnnZrRz6HMd6f5MwOItDhBNCTrZf+DDit\nR/5VjzS81d36OR9U6t0yjpv2SUXrVE8zS7tMvhQ9V5VKeQtcrIAcfCNLbjaQ\nl+BRdTNvR2a58wTLrBWYFUYpIx3PgveOXHMXw+0i2OohqRxWGrl9ctFiAu8T\nUlt//aeBSeqy3lU7Y4I7QDqBOs+ARZgG+FfXN6wY4+YnnTkovfdoIF5+x76e\nWDx1qL5TYCZY3qJ8qP+nmNb2ZzOSYX8gSwONtmIBnp79ZooZTdo4rbbmOMDt\nJTfXijRBHQkJ7v6zCqHOEZu2tWc5AuuyjLSfJaz040r2y2slnrFz/ChjVuqi\neQj4LXpqTQVVdLKbW4Yr7xhoWwaN+nhag/R5uBBzP71pwL2JmaIeX6HgjYK7\n5vKGaDyvFpE/NiwuR3GSz9YtKASgcPU30FrcLB24ccwlN986ivAR5iBYKOpF\noBRpRwx8JtM9HOLas4EO632znNS2dqp2GUocNzPL6QiXWVZ6nc07TvBER8uH\nxy/Z\r\n=0xpk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.2_1543420522276_0.19466686331348448", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f2eb9196031acc84511b99978ee945691698d989ee7975e58f99cbe1a1821942"}, "7.0.3": {"name": "@types/babel__traverse", "version": "7.0.3", "license": "MIT", "_id": "@types/babel__traverse@7.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "557b972fbee765cc0a07604dd2412bfffc9f2059", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.3.tgz", "fileCount": 4, "integrity": "sha512-ofbx/5B7apFTkYjxFNtOWhUYKrJWMLA0MQxnHR/kuNB8Hlnkf1Ab22uvmJS1kfAvQK7PXkgC7C04JXdhL8NbFA==", "signatures": [{"sig": "MEYCIQD9d70LuwvArp1QW4iSmEIRD9hp33mMItJ3oSoUhb6ifAIhAJDGy6Y4Xij0pRZnm794cSJvt6BAUdmb6Z7nDsPE6T8G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcACejCRA9TVsSAnZWagAAtjoP/Ai4tvW9t7Z/QOAM82Xj\nV8rZ1GRrh6sxUri1Hf8t9w6eqNRfh7sTzd6hBtNfQwxiIIhNs5pJejGUtMdL\nT26/dR2wWPlLruUSP2o1XBRrtYPTTNlfRQ0uNlND/WJC7jzTsUyRoEcGEHge\n1S15TcEySktmHKvN1YMQ2Le0F1GDpV0SD43xSUHp9fwlus3WnUtM+QNbeD5U\ncVJBFrNt4vSoK989JkM7rm8tOnLg6DQRfwxxY46vND2PJJ03HOvkjY/6d11q\nC50nhwJdDXSLtMjn9Bbwx0ZOQNEnqxXlan30bwWFieBaPdazWv39/k2DQZ4Z\n/r8gfsXO0T+9+kfGLVFxMhnM+khI/AunBSxmATR1lYTDtyLg4qIshiFIQgmt\nsbzGxer7K3dMf1Id6iknOvYJ5eS7Ykfw75C2gmr4O4zZw4Hel4k/YU7AmWkg\nj81VY7f7YFR3TH4qwGIqDvSihlRB5u7tNw4Uewoko/iFA6ri4tzUb7zkjwH/\niw9Yex1qcsbFtX1WSWyD1GqBvlLqXP7r/elVcY2xkng4qxo7QiZZ+MVUI8BS\nai4HkpYtainSHRS23ToWTy2yqhnxPUfaFByxHHG/gxluCrEQNewHyZyPhiIp\ng9+HMzjMxZV8ngN6LjBfNY8ZWKQCokUCFYOB3S0zvrBEhrmRRYCZcyiilN0U\nDEYh\r\n=4bO5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.3_1543514018413_0.2115311835572271", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c748214f28823ab49d0085881df0f3b0eb4d291dd36a2a9ad89eec856eda7240"}, "7.0.4": {"name": "@types/babel__traverse", "version": "7.0.4", "license": "MIT", "_id": "@types/babel__traverse@7.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "de652399bd8493ab712e4d6b68031b7a2f72ad5f", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.4.tgz", "fileCount": 4, "integrity": "sha512-2vARZYqR4Flf59WtqMfa9GgbOjK04xLZaN9+CMf7Cs+4cAhxZBP3K9LYRzsWxOQe402VvqX9+7DdXxB72ujEOg==", "signatures": [{"sig": "MEUCIQDnLhdcEvZubFsG4r1SJQOFC9IbcpwAPrrUzmeJMURwBgIgWU6ib5X5zpZxJu4X7bFHoSup3Yf/K5OwgbgPpfL8UAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBHd5CRA9TVsSAnZWagAAP/gP/i5E9eMzizjqfT922NsI\n19W0e1r4/KWLQf/ZZutImE9jjOvszDe8Zgmaf4JfVX6sN/oEZc2v0sAmGDgx\nr9KfgoJXtPLNJQ2jgnmyjjyx3JiBjbXMuhuYg1qwEyIlAIgTScksyNvwUWq6\nyfauncMOqRzMV79Pfd87QM+l0WyQISsWgQ356IC24HjyVu6ApjjXy4XSeOGO\n6DQG+pvBb+8mAYoswE0H2A40JSPwJEtGW9yZHDH/6PF86R0ibKIfBzAB37Hn\nUxJGKtJnKJrmA6DU5VpQB/Ual5h2Au24n75gnGwJTYvQzct9gYFzsLuCSchq\nnCCycnnCEp/edvTjdQTjcdzw8DxKMbBhiuXTAp0DbnrGk3Au3qBFDH8dJ/6d\n03kMJCVDlGOHCl/37Kow3NGAUrMHW2/bGwqY4EbzaPzrK+CEv/Fr2GyzQylM\nLpMXg9gawdAoUgpkXemOkU/QyJgo7BHgHGLkKdP8YLsYlBLKxmT+ab/5BEXg\nUs7QQNWiqpLuWxaOn8hI+kzPhYyh1FjY+4y+BslpjqomzaeNbvxMULOXK8q6\n0/EXg7ojgSFfog95wVPNzbVrC0LTo/rQf68FHTBJfjVM1wozsPFoi+gmtg6k\nUe4mgGRDTIXPMoIpstOGBXJ4P28lqhV4X7cIqUsNQ5MP7SsEW2B4bdwPQHe3\nCPLw\r\n=/lzM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.4_1543796600681_0.37792494809420507", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "aaf7d783940ca51afbd94c510281fcac1a54180b94b54f58bea2e3815fda0c16"}, "7.0.5": {"name": "@types/babel__traverse", "version": "7.0.5", "license": "MIT", "_id": "@types/babel__traverse@7.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4c679f9d134b520a5cf855f253741cf014a5f097", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.5.tgz", "fileCount": 4, "integrity": "sha512-tg75CuSqhqzn2UVnHriabZ4XBqTnNGNPisMKBAALFlrUhtr7Q09oOGfR+sf6+5Dg6gC6KsL/1UfP4c5z/Hum8g==", "signatures": [{"sig": "MEYCIQDCrNSQC5x5gLUOrrSy+kq8pOkN5S2yH74u9r/Wt5DJcgIhAN3MvfeSult43hbRbOr/nuToeH3DmA9Jz0zKG+sET7Wl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTzBpCRA9TVsSAnZWagAA/L0P/2rCdOE/ET2CukFKS7tr\nRNHrdXB8dio0bo9lSaQ6GIxnFwWA9sdMlofrJgnQ0To18lvpByCV9LwGGPOZ\nIMzmcXrC/4vG5kh5jdL3SuYhufQlrQzod3kosD5lpd4FUN6NGSO72foIpDCK\n5BB9wCVXVsPD4fOLMwbWI9M2BCj5Xs6gqFvgruFsxDbNygvI15+KShGpHYdY\nQVgU4fuTBUTkYniOt+86BX7QAx0ug1LrJmJbdvTsDJrhfZ1DzIImAG+/G7N2\nDhrkQrqeV2rBZ+AACQae4nbVkGJQAVHRPtfMXjmXlJyiMz7A5frtUC9o3Yjt\nfhDbSlP4KoygIDLVuRu0tRAaH/C5T7LUs48RdkfJYB0vqclz2kNAZ84PjpiH\nEtEectiBJXlpQoYQJDeIOGbTeEQO3A58/Avi9fOtqyBy7hKj4FtQ9KhPW2i8\nMeKMqYrt0rx9FfdM1E2hnkEza2lf15tfuMgfkI5NujsQbr2675u7jLEkgKG0\nyvGhXwQXanrw1VPrDBSztMM4K9pLGGY6PfCMEEwC4NfW7f/lukhKEXE/tLvV\nS6LfjTFD9fNwIMs5QBi6eK2njwyrxDBg8637rK6oIQbAVb0tMGVVZRXwU725\nGfy9BQeLykq0LVzwDgdsZa7lYcROhEDi/vSXbMlM6WPaxlOOZiVQVMbGN3n+\nYZN4\r\n=g4xy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.5_1548693608771_0.6582991806332039", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1f4c68d02e439639444aed402e1d7aba70c4455bcca57235759296fe8bc8b095"}, "7.0.6": {"name": "@types/babel__traverse", "version": "7.0.6", "license": "MIT", "_id": "@types/babel__traverse@7.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "328dd1a8fc4cfe3c8458be9477b219ea158fd7b2", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.6.tgz", "fileCount": 4, "integrity": "sha512-XYVgHF2sQ0YblLRMLNPB3CkFMewzFmlDsH/TneZFHUXDlABQgh88uOxuez7ZcXxayLFrqLwtDH1t+FmlFwNZxw==", "signatures": [{"sig": "MEYCIQDU2/UUg4zu2o0pQf+Dy7xgQVDHxEi1z5Q0H2ty1Zo3UAIhAKUNaRu/Re072DTx0xJfimojYU40KFACQVyH81FHaaQd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZIdQCRA9TVsSAnZWagAAJBEQAIW5nz1a4k6h0HobqrvE\nkUf2BaZ6QVzECsskicjm5OYCJXkBFQYlpVY5DtusVoGkYRyJfx5JRxLjVDt7\n1s/egzmH/qLhrzTc/0hnjLif7u4XEFYHxSMwJYAA15w1YwDNoCd4YiPY5rEG\niNXWf9fL8n+r316xrQpTDYSO8oacLwyIEO2i6wHskVoWtP0DvZJts+ol59Yd\nmJk+wbKnpqDIGso5grZKJbjUoimT6xbNfdXKw92ZVnr3BGSXoXlormvn/wBO\nCnX+rw30KGOTOv1t7jWzhlcAHEF9w+vFT1jJXHoAsXIIqLjjJJiXuUEAncdL\nnUQU7jbvfOk2F8pNsEx0llI0gieYW0X0jffeBkC2goAGrIQFu0mOJnnfbTRL\n4AOE9xusk9/4cocdIYKJvfBFgJD5WlkHX/v/YzYZSoQ81ukcusDrNx4O/w7e\nEgQXjpDdkNdHA+w7hLdaH/vuAFBJ+n2Ug+nyCfnyobzYPKXRuHM392PS4Lhx\nW/qJ3bicOCwQOk+4ZvLVqsBGESL8JrjXdqxfZZAPoXt3DrERLwkDzBOoWoG7\n95MTBdEAF0tVB42AKJMYmsE2M14NSxmwErMUFNtM6mAcCLcrh521RtL+zz7O\nkk3cDCCD/6u1QFl8ZLVBllXbo3aYcKinrlDbkJQTl4+xGKIvrphzuSgHw/Uc\nEJvU\r\n=uxmd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.6_1550092112238_0.1043876803801651", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "25da84650a4407dd7d7b3cc742518e1e39969ae2ada18af402601fb57c3cace6"}, "7.0.7": {"name": "@types/babel__traverse", "version": "7.0.7", "license": "MIT", "_id": "@types/babel__traverse@7.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "2496e9ff56196cc1429c72034e07eab6121b6f3f", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.7.tgz", "fileCount": 4, "integrity": "sha512-CeBpmX1J8kWLcDEnI3Cl2Eo6RfbGvzUctA+CjZUhOKDFbLfcr7fc4usEqLNWetrlJd7RhAkyYe2czXop4fICpw==", "signatures": [{"sig": "MEUCIHvzo5OSGzJQz8VkSwXQcHawHp1wIPKR2qI/CD0REbxVAiEAlxkSsSokA32h/NNjJhQNqlBAs5LZevvVZFxsDlas+Xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/wtWCRA9TVsSAnZWagAAiv0P/iTQ4A47k1gVuIijoNf1\na2qfpquRk/pOCbKgkI2kTNQmeDtYmkVQxBcDdla6iAY1fJRwBsxIsg/BsWvN\nTy1EsdY2qt2tw/yE/4A1i5mT5aKuu+LHMhZ66cd2WHsHhscKip62v+t3lp1R\n35q9GBO3ujDKN+TK/8XVCcDfbLA75NMdpWwo1CyYHBGbV6zwxM92Wo/OWozX\nF0ozAXuTxqzdbXyixRVzakDe3ZpbgysP9UL24uB8c1uQtdaBob2VEvE/TAUn\nu40m9x7yWq2VlOMmY8J+QEijypS1UmgGwfzVF6i8Alts30myd+0U3LxmZGRx\nttPKHvCkVPsWVJsTIHW50knqEGAiZeWzKBuyILh1CSOXghdq73y+3hPtqiXx\ncIyxxwGjTiGM6zpg6DX8kgYL+mkxx+Z7CWtOILZan4rZgYmiAZyyJi7U3RZt\nTe6WX76XWcvVCRfrPjyX37+nGTFNjkQNMJkQu3n3PdWjkhqJvLllRFH7Ra2h\njYHkuWd/9x7N5d04EAtr7tP1MxswruXe6T4yONR+DYFVajtMo3n40brHMTkO\nMu0H/cAcjXnnqM35nx37eEGWF9jb5oktjAGJxLJgq5bH5Iul+rGxPMBNAaLR\nr+JgZxKVN3y85fTHJ/sR0VdybLhKIcHDMkda7jfKkUqdBikG5j4CmSuPY5vY\nBf9U\r\n=GZ9F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.7_1560218453757_0.8217770701262888", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "37e6c080b57f5b07ab86b0af01352617892a3cd9fc9db8bd3c69fa0610672457"}, "7.0.8": {"name": "@types/babel__traverse", "version": "7.0.8", "license": "MIT", "_id": "@types/babel__traverse@7.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}], "dist": {"shasum": "479a4ee3e291a403a1096106013ec22cf9b64012", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.8.tgz", "fileCount": 4, "integrity": "sha512-yGeB2dHEdvxjP0y4UbRtQaSkXJ9649fYCmIdRoul5kfAoGCwxuCbMhag0k3RPfnuh9kPGm8x89btcfDEXdVWGw==", "signatures": [{"sig": "MEYCIQDnoYQcHFmeM+PWPXJHkyWI9zq5HKyWY86lvmvaSA4rIgIhAOGvekPoL6ap06T5cYUzgsEAevhWcvFuJPxLw6+cTHmh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1HHsCRA9TVsSAnZWagAAvG4P/0JuN/Xpd+0gPq0nZdii\nUhHuLdgCTGgsH5XqRk6PkfXss2DdGnI8g/+yc6qATlJtdx1KSzIYUQ/Nb+bq\nymF1QqIXHGZoPi9UNm18iMeArFH+Lpr2DGHfEeIRVQgclHN1WZseSGVwgi6f\nURTbKXXMSLRebwf41dipS4ejzKf1Dz85zQ17tCfiAjEuC+M26k0i5uk5seDq\nbCtJk6AZIUein62bBRUse7ElXikzWYfrUVEqPpLpGce3ZnH+/c0uB842a3BS\nmeUjkDPRTI4XrPAcxQb6e6u5X0iH6K/0L3XzPjkPYfZcRGdiwK+vg33rDSNB\nV3Lr7UcCwEdDAyWOS00YAIgVrk7rkKdTDHC44xRXYLxlr2H5VbJhumwnZViB\n+mx5ershNHI1SLvJhG5NIVanCIphztV6mEyBV+QI1lh4yngcah/6HWuNci3i\npTp4RKyRzJm/cBFTM2rtfSIzLp5XZ8+uXCmOWecGRjiBMRxRk6WkQCgrpfwn\nZIsDSiC7thmhmDCkB5e3Bog/p4Ki927TYqwI2ejgjdBBvCjgPl+pmhF2hcC1\nLzoJtKYhDZMbv7DRSIs1c3iMmuE4FvB8nrHARdLOyJ7f39k8VFB8ynD5Y9pX\nftkRA1JNtCj8xYJTQIFfMcHwO4r09xkd0mC+ZBFHO1HxF+LfQDD58Vgsvhci\nL31y\r\n=GhaQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.8_1574203884455_0.7814885656389232", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9b5b72051081a25b9f1276449bed6884b88b99d0c4372bd5026fd48e1b5ab780"}, "7.0.9": {"name": "@types/babel__traverse", "version": "7.0.9", "license": "MIT", "_id": "@types/babel__traverse@7.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}], "dist": {"shasum": "be82fab304b141c3eee81a4ce3b034d0eba1590a", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.9.tgz", "fileCount": 4, "integrity": "sha512-jEFQ8L1tuvPjOI8lnpaf73oCJe+aoxL6ygqSy6c8LcW98zaC+4mzWuQIRCEvKeCOu+lbqdXcg4Uqmm1S8AP1tw==", "signatures": [{"sig": "MEUCIDMVVe0AngLCLIkh9aaopadWCWkNjz6BN1w9QD4Gxx/mAiEArkc2Nct66BbqiNWKAIBDyISYf1QoNmxzisj3nCNqeBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTtBICRA9TVsSAnZWagAATbYP/jdN/LEo85Jvo4WVi9PO\nOa8Nxkh4CaaBvlkQ6WM6OtAedMn5iIDF+qVGwcXWkNQiQY6buc7d/kqyZAyo\n+AM//SQHyQHmzZrg9tHi1N03TyFfiMWSZbTHnm9fSptmEmlmZuI42xWic1R9\nCe9/N5Q9hn/9Ecvv8TbwtOFJN131aoi9HT55mxROjngaK70tFj8FV7q6opu7\nTZrR2xSUC4wEXq1K+vSaL8wq1n3ywMpjmiXqYFR16xG5px27v1PU73/hpppP\nlL9n+cnynDk3UCz6AxTYv1zSlgagUBvGh25MRP3YW3lTDDp5GOhs9Ow47ahl\n9UrtEeZkzARngQy+zh6gdIZkY+VHA6GCYF3GE7ZC1kLB18YAfESXke55N9rS\nQdaA/keurqMJMRXNA0VsbCifzbKtuq3fQd171BmbEaVp7c2/3fk6ZR0/pWPe\n1DUOQPLPmeYuKEItVTy+Jhy9fNbuSaFFlSL58Sj3ztVVggVwyo5h4sABZ93K\npyq+JQhF2QBvZwzqDzm7meWJCQX3DFd42yjFI+B69KP+FuXxWmnkDZ6ZkVVk\n1hgLNZYM4hXeEnfJG2sfal5CToogBfhpIDtenZNU4T1YayBkaWpXk7r660u3\naOUGabDlZ6iLAzjbyQsQVdQt4BvRMe1x1vfXjrIqi16E2hP1a+aS3NepGf5r\nj+B6\r\n=j7gn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.9_1582223432389_0.16731559621191017", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a9e8dcc5bbde1b027c9262fce80377a95145b0c709f8d4893de0a88d532d4a4c"}, "7.0.10": {"name": "@types/babel__traverse", "version": "7.0.10", "license": "MIT", "_id": "@types/babel__traverse@7.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}], "dist": {"shasum": "d9a99f017317d9b3d1abc2ced45d3bca68df0daf", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.10.tgz", "fileCount": 4, "integrity": "sha512-74fNdUGrWsgIB/V9kTO5FGHPWYY6Eqn+3Z7L6Hc4e/BxjYV7puvBqp5HwsVYYfLm6iURYBNCx4Ut37OF9yitCw==", "signatures": [{"sig": "MEQCIBwkNgR+b+pYDFWnOEmPgzeCGSx3EzxkhVrRrUrUkyw9AiApeQnZVaqTLIYa5yIi0GF0wF/cdmJ21QYcDIXlAvN0Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei34zCRA9TVsSAnZWagAAWFEP/20RQMlj1xkednRL2vRg\nVXHg7/UocCgYTQUn5Q633PBlKcaMofI/YHjZFxa3rWqiHU4T3DwPuTwDpw/S\n7tQyKmK80BYKQKwljYnNuXNavSrIpB8TE7TkV7eY+cMPSVmnvKnqRZ5k9JJk\ncQfggKqV0M56yYbDAvZ+NXzZgLKfGhTF+1yZp2LHNh8QJkcS7v6prk7uVZb4\nAucIqtstYY7DEONbQaZnbg1UoZrju0nPbkTo14cpNKoLmdWt4YgnK1nNo1Ed\nQd7IO8zFgziUaz43GROrqlus3ESdnMMoj5Kp6tdY13DLB4Da5D7wpP9QQjEU\ntSgNWC0UanL9Vyg9ThBghxRwyVd/SJumfuAp1AK5LWQPRGf/X9M0VFwtsDWI\nXeSw9hdK/YAcAlXnpdKllz3tacGNsRX8xmwgOd4vHFGQy/o+SyXBhbppH5QY\nCjESiLTcep7Mu312VP79Z1sCDN94OQH2HPX4MvT0teuz/6fShwTDMWQ/VXZ9\na3UgOo5fhKBw/NjiPU2q+Fc4uxxTtAeCzsqC7q/Ki62qMHyS4+wUW8jfnLAN\n+EEl3zl16qCi2jbfTCiuhO+Tgu/zjttCNBDgI33jo/VNA1QhNaz5CSweW9PJ\nW3OrkRmWHi1ZAUowuYlkPdgIxRtDGJuHcANHrNgShpfuReuUpYSPhMW44ooY\n2nAk\r\n=31OI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.10_1586200114664_0.6245733668215769", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fe6e56e3c2de4994d8953091fad2b308dcb631f61e0350e10a109b581f887529"}, "7.0.11": {"name": "@types/babel__traverse", "version": "7.0.11", "license": "MIT", "_id": "@types/babel__traverse@7.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}], "dist": {"shasum": "1ae3010e8bf8851d324878b42acec71986486d18", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.11.tgz", "fileCount": 4, "integrity": "sha512-ddHK5icION5U6q11+tV2f9Mo6CZVuT8GJKld2q9LqHSZbvLbH34Kcu2yFGckZut453+eQU6btIA3RihmnRgI+Q==", "signatures": [{"sig": "MEQCICmw5krvVqyuN5ahCNWWlO8N3f+gWZxymU4wHOami4CcAiB6uD3FzPRe2IcMkJREY2ZLxg1K3Gqg078NZtln7MJAog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep1IKCRA9TVsSAnZWagAAjqoP/17a4h56tX02QHGY+n7H\n2Du/V4GKIIJimVQ7/Oc9oD4+vSpJx01xQnD1nRCWwfuZrV5oam8U5rdiRt8i\nVF3yvtxl05NlO461JhfZRi6Y3y1hU+CQ8mmdS5ne3F0HhVHhOOQc+3MAqaGK\nYoVwsRCQS4Zh5CFrnh29FwleUABrIJapdKo7jtRvBGEKUBCztAT0lNAEG5uT\n03U/KXatQniIeSF65A/KFP7gmskl0XGxAXxlQmIKQOmTEf7KEFESOsXTEHdR\nV6T8lh4aNSu5hmCjdxQa0PkJgoaMt7X2ePgWk+VcBCUCnWsmtF1BL29MIezd\nie7OfJyRhy8H+sUVG2Jfj8z+UZBPDO1mf6Mg/Ez+DTrF04w9cQN30UY43WOm\nD3r/nV4+nbaYbVOjjZ+mhxTGwcP2ZssKPGp+3weTCS3HlsXmE4Bo4LGk2Dj0\nzkmtqaRiDIolPfPUDcY42etMAgoSH9O+ITy0teiBLRHKUdqLFF8uvxvofOql\n59guIXR9l+f07pPyg+YiGFu7Gz/I7v1Vz2lX1lo8fDTQ2h2M6NCATiiMIDp0\n5D7abUT37/PzOoUVYCJwVyMa+dWF5xg0PbABiYU9mboENQudDtzAGJkgcFG5\nj5yDrasOl1MBzMlfm2MzUTPGT+8o22mTSW05aVYJHuzuKvTEo3IrFKNB30LJ\n5qt/\r\n=D7Bd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.11_1588023818113_0.7236970697164933", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9dbc1fdf9406edda1d6b63b712e5b312a5c2aaa27ed9ce8e70a6f6130c7a65a3"}, "7.0.12": {"name": "@types/babel__traverse", "version": "7.0.12", "license": "MIT", "_id": "@types/babel__traverse@7.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "22f49a028e69465390f87bb103ebd61bd086b8f5", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.12.tgz", "fileCount": 4, "integrity": "sha512-t4CoEokHTfcyfb4hUaF9oOHu9RmmNWnm1CP0YmMqOOfClKascOmvlEM736vlqeScuGvBDsHkf8R2INd4DWreQA==", "signatures": [{"sig": "MEUCIB2z3T8lpwOKffSo8gNxUh6g+VSvVw4g5jFHqfU9XHn1AiEAnkW0ZgvgQD8x3/Gm4SkNApX9M1j0/3LiIMmIwkMDuAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1ZVaCRA9TVsSAnZWagAApPUP/2KuGwH4J9mK7RaDDcCu\nGOLwHTvRpc7DMkbS/bskuKovWepSTsVXwxJYjdto77nYNTCjJdFEAEzJXMmu\n38AC08BmdRxJdMaAr/udf3ORn6G5V0ZMqId8xebqpuwQtRSTPm+a2P8RCpTa\nV4d7pS6sZ2LfpBhtilOIgW6pV3xmBb47bgcY04Bi0Z/NVZkR0TFhmHw7GyJW\nKwVBpIEpapVu2RmGCXvz3Lu/O/zsrXWNpHvjTO6hYaRa/K4LvfORRfG38B9j\nt222FTzveYcnWbDMkohIMGMdJpe0P6TA70rXWuTSLrJ2EsZn5ydIK8FHH7Ag\nZyfzLgPDE6/q4fkiUtQzz7CrVTALFrqT63WKKZMyGL0cM5GFaa1+yilcRK40\nr1svlFl2oegdHkupjTx+woYvOrKlGqE/tjrN4ufLHNQv5SQEf/W8gEpu9xjV\nUsYGdGz3d3Ma6vRGTkxqqYoMEd2lrBx2nQfxI6k5IKE5qF0/KEvxU/J8Wm/k\n96NHIepgyuEqd++4kA/W0Tsec3FA0X5SeGm+QEIX+uP6xt3psmPL9dOsOOYW\nlQ2ZJqzSpjOXTw/v3jji0O/be9cFf4Sm7cftOrMMn20vYiJZo35hyDdpDTu6\np0f1IAbfTwEyg21RswTccB01FNrSDRcZuwP2pIWc5XWjKh4aGP8lFt6WAYpB\naJqI\r\n=/Fa6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.12_1591055705595_0.46025689953572124", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9554baf41b59ae77fd92863257012fe8f1598be68b3dd2368f9b6a88ce6a7e13"}, "7.0.13": {"name": "@types/babel__traverse", "version": "7.0.13", "license": "MIT", "_id": "@types/babel__traverse@7.0.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "1874914be974a492e1b4cb00585cabb274e8ba18", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.13.tgz", "fileCount": 4, "integrity": "sha512-i+zS7t6/s9cdQvbqKDARrcbrPvtJGlbYsMkazo03nTAK3RX9FNrLllXys22uiTGJapPOTZTQ35nHh4ISph4SLQ==", "signatures": [{"sig": "MEUCID1RTEnxCLdDZ6YI+LaFyICUPtU90+6QeBEnkSgw4xZPAiEAkC3FH5G+sMRGwcDHBMAoZ713FGRBb4CJGARPVq0T8p4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA409CRA9TVsSAnZWagAA5KUP/ip/wyC4rBKneMDoZ79N\nUWYEiwWHK2jpOkLdHxHyUrVNaaYRI41vMC62DaFMPZS4K0K5P3C/UmNFC3FP\nAQ/DPrjjVdWhSTGSYwgOAZY6EhandWBUT5gurb+GUXjEHm6ubaBHFYHAZzhq\nht2VD0KhQwumqHT/Uls0515+9JzSOpyzL7eHN+XbRdiM6G0LpaZKWDBI5WIz\nxZKN9Mg8RcP3h/ePib0E0Dw19ZntFmXrRM7bm3Rjz4L5DICXHOalIVSwJpgl\n185xpm7t74d5y0m2wEIdXP4qVxU6AN8N5JyyH7vJZHIgcj1TWcX907jCp3zN\nf7vHLWMwjajGdiBbLRONzVl/Mj+2F1y1Tp0FH5TM/lD1XyUZoDHsBPtJikM3\nmjASDNh34dSXdQ/HjkWt3K4AlKVn8z6iUJKFSzhmU3Bn1zDm14m0rStNYdLa\nFwaA6SOBJY++odyC7pFTuxFrz544XP+R+tRJJMkFOQRSWiv0Ny5YAaQkDdqe\nHtTiXUpTgkR6vwNH/pNI2/u/4iMe+1Uuyn/z6G4T18wYd6w0HG6AQBvs8xPc\n0F6y6EkxTCDR+vdVXnNFW5Sc1OJLFfk4YNeAyKOq/lhXZTOo5jJ0fwdnuWBU\nTiog84u+urcHR5d1ilnIcOxPa2S8a7VfcnQTkkxpuVblqUevCYESBPqnZlwh\ne/eB\r\n=Fh3n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.13_1594068284485_0.7958607289872932", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "463bcd9c36d32eb0717f749ae1d6c8002ce786ff8d74b5ba2dff5469507b4dc2"}, "7.0.14": {"name": "@types/babel__traverse", "version": "7.0.14", "license": "MIT", "_id": "@types/babel__traverse@7.0.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "e99da8c075d4fb098c774ba65dabf7dc9954bd13", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.14.tgz", "fileCount": 4, "integrity": "sha512-8w9szzKs14ZtBVuP6Wn7nMLRJ0D6dfB0VEBEyRgxrZ/Ln49aNMykrghM2FaNn4FJRzNppCSa0Rv9pBRM5Xc3wg==", "signatures": [{"sig": "MEUCIDBKNhnSp7caocHiglxpM3t+b9d0c3JhodvKC0RKbJi0AiEAu82Zv6zJtWV6uThFmrub5uDCoU2avdLVtASU/+1uFoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV/FHCRA9TVsSAnZWagAAhwoP/1f1wFSFG38Y/CQbnwgU\nkQ2hVmOH0O9Y72vQfC53PhB5gukQz+xqd/Bt+GCnSU9mFzI2Ie+9xnvfrlok\nwETv4DqJUqcDZ2RD42uzco3hVHc71x9mFp3PM4wbI2mLAccFnRBTbyaHh/gR\nHn+HIXPjEhTp+/CoTO2om/ntb6X2zrqVRTCfg629W/5Ipm+0zt7JY7pOQ4T/\nYYr5Hp9a6hLgbgyeZ/3fhii3Zgexy2YKoV9iZtTgdcZ8pbZtRUO9eaY5A9HP\n/+Of2uJlxIIGqtsvNOh88jjdwF4LT8IFC8Lv1NtH6n5YqMsmXe9NfU8lfUeW\nkChxg7fWGx+L8vZt8GglPC/oeqd6SA2PgA+dYLTn0eagdTYWJRyuk/tuOaVm\nDGOLigVHuzzhxu1/CnDWbNV36VYqM9BdN4oYBqyBjggGSKP+WtfN/rDNBZZ3\nv2Nagy4gFVpbFWSNYkkrgaST02OE6rAE9x0pp8IqXe8LZKRVBf/vwbvGRMfm\n535JuB7fje2SZadYDy6lhG3c7VuDbDE3PHHNVWjYI/NJ2aLlqqdr1MtbW7NZ\nLyVhjDks40DPEeJVwxjI1OckZhO4img+Iux4+SVce5EV8HUAdKDy5xaMKXiP\nD4oKhZX5+xq4cXGrk+NDcQnBUrpV24+7NFmEu95HIpL+G+N4fjnrwMhPJZm3\nAm8U\r\n=BrNp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.14_1599598919498_0.5448533791119228", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0b136e45f856a4b165beec0b4b7454213491af06b5ede13074922c88720c6372"}, "7.0.15": {"name": "@types/babel__traverse", "version": "7.0.15", "license": "MIT", "_id": "@types/babel__traverse@7.0.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "db9e4238931eb69ef8aab0ad6523d4d4caa39d03", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.15.tgz", "fileCount": 4, "integrity": "sha512-Pzh9O3sTK8V6I1olsXpCfj2k/ygO2q1X0vhhnDrEQyYLHZesWz+zMZMVcwXLCYf0U36EtmyYaFGPfXlTtDHe3A==", "signatures": [{"sig": "MEUCIHCbirp6kPlT+1CHvh+Mw1cjPI2/5hXQ2bPvlB1zbIyvAiEAifpLC/eJg9U9Ep05Haf1KXZvKVvDuWegKjeXBR+9hd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfboRvCRA9TVsSAnZWagAARe4P/1KZ7t1Sq21ix7qqDFWO\nHVKUhf+t4YkAfwpGbxFU3QMdzmrqSgbSqKUMX1p4Os12/GMIb7deVa5eTrxW\nd5vJKvMAk5JiwEAqYus9biBVXXs79BDbgMNbvR6GAOv5pfR5MyDZpLE2S5b5\nHN6GmOU9kfj3/sV6/TjqYJWaspXQZnGoHpeoo2O/euby5b1mvQeZaNyT9lHw\nlNlXvasmHMuf+CbVce0GSc4LWBk6F89AxxxODLyXPIEklPMPO/KfMZIOSnXg\nXBmMuPqLrhYPWGEoO1Blncsk1g9dA5kQsLQsHkmPA/Kc8lL3ZPKlqzWl3RvM\n9keKPp0qfeAsZyZXdfIIirIEv22Db3031EcomFv97F0AcF6n26aL42XFf93q\nTTt70lJE54GIA8uKedqNHxhDxn11qdmFwvwGpocM7A6m/EQOUBoGebGDnc7U\nN8yw9eyFAz1T1s1py5Pnt7NX1emmklDxmJRvvGpq/twa1A85vdsOHfCZ9AVP\nRhu45t4IGgMCqT+e/8MCPYKOoeAnxOwE9rfmc62571y7qHPUd0pAzwkQAPfJ\nyTqV4nkDzrB+rWyhhzmr7K7Dmdk6fsH+zUOskqH+4RtF4aCcV9VZYXeSMY4g\nrPFuDWuUmjx72CAnwAR7Xhr/JXZjmO8fS+3xVpK3bxa8BXrJ+hCmL3LrMl76\nCi6m\r\n=56cS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.15_1601078382826_0.7360952056628904", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b641c5a48237d080c85b2821d25a5c49c2677fe8418c25b0b0d1df7b70cbcda9"}, "7.0.16": {"name": "@types/babel__traverse", "version": "7.0.16", "license": "MIT", "_id": "@types/babel__traverse@7.0.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "0bbbf70c7bc4193210dd27e252c51260a37cd6a7", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.0.16.tgz", "fileCount": 4, "integrity": "sha512-S63Dt4CZOkuTmpLGGWtT/mQdVORJOpx6SZWGVaP56dda/0Nx5nEe82K7/LAm8zYr6SfMq+1N2OreIOrHAx656w==", "signatures": [{"sig": "MEQCIEY1dCsc4nUCK2GX1IWXX6mB8edsbZaj56PVEXtZvHE1AiBWDq9PUSHOYhNs3Mcj7YlIP19qWYQBPnADvGh9vPFY0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfv1wFCRA9TVsSAnZWagAAvMkP/00kEV28nUuCdtjXSxaQ\nkIklUOollCNWb0PSHLDjbu/UMvMh7QF6SaU2KBEtmZJqtpsDd14zm/h1M2yC\nBjHnJutkvhAp/GOVGKb2A4FQXtFhv/tCNw2SoQjIyWO4cUP9uekaTuV4Z5IU\nk5KL/kwY5w9p4eHLSVBV0C3htacYNQZDjNwAihvzVBd1lniGpalnSpETmU0w\n9uyd55RZAzaUiOrXDZi+RGCHWwzjdvR7N9KGGL2LBrGU6OhAxb+0hF9+g+jc\nrV+F+Cn3L5aOwmfyLARL303oeq+aVnv9gZ7/SrDYWtxA+uYypJuNgjPtujyV\nSZkle2KgBlS7zmofFoNcUPrW1mDbXtsSpQfbf/On8a6jOd6a1S/EuD0ZNYyW\n85jO8xPl69Yzkn/b74z8Brmotnc88LoUjO4bHOsPG4bIbDBctADb4pb+OuEP\nUOxdHxlu0sP5fH9lLpFfRZZdJWvE+dAW/ynqUZy2EjtuWK8Q8QVhSkvajeY2\nCxRjBWbom/GUxebBqZkn26y1gz3pVK5n0PvpCYaLluTnGa7ya/THDXAdvAGJ\naJx2S0Wm1d2cIlpSY27Yn1GwmC3hndPlxtgyMgD4OGlrMDmUcFfeV0XOF2y1\neB3K3u8IgGPmQ7fHqp66/aLZW7H/8jrnWMWI36AjpqCYKJGYN/09umUt1YHD\n8KMr\r\n=fUKM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.0.16_1606376452742_0.2714972398331241", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "677cb4ebd169452007905821a075e8ef3a039b55ee915ec7287453f99b5cc8be"}, "7.11.0": {"name": "@types/babel__traverse", "version": "7.11.0", "license": "MIT", "_id": "@types/babel__traverse@7.11.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "b9a1efa635201ba9bc850323a8793ee2d36c04a0", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.11.0.tgz", "fileCount": 4, "integrity": "sha512-kSjgDMZONiIfSH1Nxcr5JIRMwUetDki63FSQfpTCz8ogF3Ulqm8+mr5f78dUYs6vMiB6gBusQqfQmBvHZj/lwg==", "signatures": [{"sig": "MEUCID2CY6e2P2Y4e40HRz6EOh49fJ2AgHlA8dzDJy6NT6jKAiEAhfkYyCwdJLfGjflJYFVJbR3O7DoeB/pxOFN00wVp3Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz1SACRA9TVsSAnZWagAAHmUP/2sD5Es0JVOuYwRgx5SO\n/YhFBoJkYa+jFT2KLGYRl1KcxwNlPlRbf8V40VrblBLwfkHtL74ZqUf/0xKF\naSwd3+6NF1wfwI+AhElEwqWruILjkTEaozXjI1XpgfhiwhfsnX2M2jY531Ul\n0K126JuZu6bGWEBft2CCILIy8K4J1RGV4qTm0VE3HLOsMHYd/dmobOcL58JZ\nBmVyvH96ZIAbsrb889r/VowZRgC3WRGLOOSzSXqHHO6UCyyQQ6bfCuzgMC99\nNEepKb7XYdT/ZGtoONk3eVldP+B6HEwCFTf6UdbyVvZuqImA1xnDVp9r9xS5\noNt4E68JkVDqxPhVEEC14wKsNJmoyKk6mgiKy9e1a4F8W0rHIo0gDp5aNyGt\ndrtmcWHCYIH7K1TCeZHKncnL2K/UCCP7fiZallrIobdGg6auanIOrUZQVe65\nciUdd5GPGQ9b+0mpe5aV/UX5n1uZH6hN/YS4H/hXEkKsH5dM3O6lqdYWovx6\nIzt9XLkJQJNTfpdBP1hb7E+w3fAvf7iPdpQVjT5zDMK/UM3OpBMUt1Id68RB\n8UjotDYepbZ1MZYWzaQOpD5FePyIVW50iZkggjghRIRgiwIOnitAfNd7yBoi\n8+OoAR5QOcad9xnvNIRsjI8w09wCISifD6tHdpq6NOi329eYVT0HHSrBm4tm\nusp0\r\n=oNLK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.11.0_1607423104365_0.394069882091552", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d2026b87ca1b6d5cf953edcc61327342e0aadedb036ddb748b019104161345bc"}, "7.11.1": {"name": "@types/babel__traverse", "version": "7.11.1", "license": "MIT", "_id": "@types/babel__traverse@7.11.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "654f6c4f67568e24c23b367e947098c6206fa639", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.11.1.tgz", "fileCount": 6, "integrity": "sha512-Vs0hm0vPahPMYi9tDjtP66llufgO3ST16WXaSTtDGEl9cewAl3AibmxWw6TINOqHPT9z0uABKAYjT9jNSg4npw==", "signatures": [{"sig": "MEUCIQCPG03BbWO8cr7g6AsKOOFM5zHhu5CiNObXQUbmlfxSDwIgQTLXrOqBJmuP2wHpviZFrErzf81Q1Y7c1sM8Nt1LTjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTIi4CRA9TVsSAnZWagAABboP/3RWFbK79u1SiIZgnr5k\naY7IkO9MobHgwBAZL6KVvEISLS1VN1mFg0cKiG975n3HhNhF/BnV6FzEQp6R\nslBwbpB7PKSd7pyM4w2Hb2YEnusNES2YtWApj2TibbmITCilX4KoxlgaUQZ6\nOo61Jceu+jxH5X3MUIQ6jktc34o8KvNFB5zMsZoba0G8C908P0VxMZk2EQhg\nraFf6Z+0HGbjvc1q32c3O4QPbUUjpRjzTaywpumrBD7wGyyCj2eEuUqiQke3\nIZHd9JETzh5HaE/gs84vom91IHLbAqK2Nda+8C5RWiMazHzXNvwkO4X2SS8I\n0DzPoZKPCkdw0dxwdbGbXylluBYFlROrUyCIX6r6HxFX+o2BjfY31aIth6RZ\nR/pqufRigtePMHxCT4T+mvTVJSuJiTKEl6ZZMHeK37JbJNU/piSGrHWpP9Ps\nkgg9fnnSUaqLvpLOxhEjR/el4UaSg+EK0D+8abHRbFx3EP7bCfUlnnqbWC/i\ny5FtfZX7dGM29U2BM3RNqayDc3AlFk9pyn3PFWS59H4bplvXEOSTnxoUdD8C\nPlvmU+oNywFq/z8B+HLiIPDYO9pkzfnZH8Tj9N6nZcV9ks/29XVuiSt22prY\n5KFRC1+W4mP2tvH5yHfTGzqJr5QyfyFTDja3rkcfjVknwE8sEFZvZx8Sdsay\nawVl\r\n=7tog\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.11.1_1615628471806_0.6241413847698765", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6a2fa1486fb4377847c440a470741ae1f96fe3571b07b3d04030eeaed9521315"}, "7.14.0": {"name": "@types/babel__traverse", "version": "7.14.0", "license": "MIT", "_id": "@types/babel__traverse@7.14.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "a34277cf8acbd3185ea74129e1f100491eb1da7f", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.0.tgz", "fileCount": 6, "integrity": "sha512-IilJZ1hJBUZwMOVDNTdflOOLzJB/ZtljYVa7k3gEZN/jqIJIPkWHC6dvbX+DD2CwZDHB9wAKzZPzzqMIkW37/w==", "signatures": [{"sig": "MEQCICwDixP5ZisZc5wiPkI7oTubEOyBW9iOJpjq5Tsf4S82AiA/qVq+6ILmyi7HcmsZNwvCt056ynYqcjxp0xVwapylFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2H2tCRA9TVsSAnZWagAAqbkP/1THKSqRQKd54vm3brv0\noUfOBCLVMbQsjG1alVEL/JrCyEAZuHicuQXNRDmIV2jUem0dIcBWzbvfs9BE\nnR00fpXD6Jcwzkjb85ESaqiVv0MW2tBDezTKAjUiMZGZt293Nj7YWNb52oU1\nJd13HgOGYn6N4uwdFH52+jfS5kkfn8LoL3sIz2TzEXl9az08mO6SGs5dVv9P\ns3Y94UAuHV8scy5wLnVtqfZ7dEBQMAlYalfUArK6p1lOoeIhuO/KKSrzS0tp\nUjvZG6UA3WSF5QYMUwJecaFWeaFKoSG4Owg9N+TsixeMT2u9arQwwsKPgyRU\nPQQMt0C1L4Dh1q1wu1gI2VpKb/sD7HwOFcHrYH5ZIwUOF1UQl9yiNhbDtqRI\n1Mfn2j0e9aiZahcMMC/5FFi84abmo3bxrQhjR6YcASAkHRyN9u4EU4zuNgGk\nLIsqoKBG4IeDb6gRHnjEM31ATtgWo+Sy6oRx+EFbKbcIotPc8wsJd2TG28uD\nMb1ONCGeH3qPJNuzm+E0ZiXGBGI6bueWRQrwwntjdRANca0JvnJCtdeQjI4b\nNPtFGax8PW9wGeJECs/Uetz+LikjRE9FOSkemJ+l/Dw7YVTezUo9hRmc+oBg\nOb04+oczssTkpXYkI2bRY/kBu72E+6sDdzwDDBgROKg+fxulFqkm0tX9kCP3\nLULf\r\n=3jmK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.14.0_1624800685120_0.6885378748985502", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "018a94bdfb68ec320c195ade1ed72b7e91dbc1ae0de5f8eea2d3ad225c6967e2"}, "7.14.1": {"name": "@types/babel__traverse", "version": "7.14.1", "license": "MIT", "_id": "@types/babel__traverse@7.14.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "46c2f9501a7a8f0596ddfd365e08c15285a47cce", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.1.tgz", "fileCount": 6, "integrity": "sha512-DomsDK/nX3XXHs6jlQ8/YYE6jZAuhmoGAFfcYi1h1jbBNGS7Efdx74FKLTO3HCCyLqQyLlNbql87xqa7C3M/FQ==", "signatures": [{"sig": "MEUCIQD9GsSs6nkgmw7FS4fCY3xl0pKOnoCQuWFzxSxe6uUJxAIgWqsgvNO9LdBNko7z3YWdZVQFD9E9ujQSbIDhXOf38jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5J4ZCRA9TVsSAnZWagAAVikP/1duVu/U/EnKuG2bbNi6\nM15y7hAhXwjY8dWWs6/A5FV2IFQdpPWf80Kzz5a8Uagv6h2eZmZkN7tpKqIZ\nf88vjkTL9mw88wlIBLR/Luw70p25yFJar2+BOnqrBaLJaTHpla0lsRBLkxxf\nY9+wdWNkgQIfhauFZlKiRkicMnB/nQD/HclIhksKpXc/kOMpQBo8l8f1Jvlt\nrjQdgRdJv+MNnM5+tXv3gbPR7twLTmpKGv7Q6nKB+peHkK3jrBVwC8Z2UbhH\nmv+TMvC48BqX0CV7PLfqxhoO0j4xb7XumYt/eN8MlU+0hyP0B7K1IzwWhwnZ\nALBsIPwLOKQck2+G/Vs0JLOoFrmZlRc/SCACBnJNNvGsp8mX5gKdzo5MmXX/\nr4lMaSfPbH2KHbq2TXNPasqAlzyOKNICoiyEwUofqAVwpc5bE6ZXgZXafWVP\nYXQNn2ZjNge8Bh6inbLLlbm9OVz8WA2CSis5J88er6OZSNZ9Wa/4dYNEgaiE\ncnprYUlREUFigVHvLuC83j23MmjSWlyQiZkBQAfBTt5mxWAS5cC/iXntIsK/\nMRc9AcS6/IMlnf4TisNkhoWodOgJtBXtjsUKULDWExn/AGQZVUJ+wv7TXiHf\n3IQXbxb5UkowXwxhXl1V7xZVQY3GVMT8ykYEVvonghUWJrX5ZIgK7rwlcVce\nGWXY\r\n=5Gyt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.14.1_1625595417500_0.8674248527160946", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cfba5af15311ea4d54ce047c1682ab2e092412306271db84b56d4aec74387468"}, "7.14.2": {"name": "@types/babel__traverse", "version": "7.14.2", "license": "MIT", "_id": "@types/babel__traverse@7.14.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "ffcd470bbb3f8bf30481678fb5502278ca833a43", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.2.tgz", "fileCount": 6, "integrity": "sha512-K2waXdXBi2302XUdcHcR1jCeU0LL4TD9HRs/gk0N2Xvrht+G/BfJa4QObBQZfhMdxiCpV3COl5Nfq4uKTeTnJA==", "signatures": [{"sig": "MEYCIQDkXZDsbR4hEtx3h8N8IL6+4qDlNaBOke/waCbNnr2FGQIhANegZcXMrdRuc3gWHTXQ0l2aJ7ktSlay9MIXVNl0IBHE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5wwrCRA9TVsSAnZWagAArpQP/j3aFbRwXVfjQbJYiY6Z\nP4gjO8tF/WdpgrJSdGEtYVj7A18njR6RX1JYNXvM0omQgY7TuU7lZ9MNRO7u\naX0r7TjjXgMQGRUke52Xg+JW7209Iyfx0nLaNd3iX/q4IXZteIDAWriSZ2gA\nT67s1KBML1Q4rALrZrOCsz28o9eKvS4RK14WI5euSmZPHzy7HPQt/fFr48V2\nhUVgEyv5WcAldO+xyhLpylTxak9iDeABjwNNyRN3NNEoJ+jtYrIXMEAuWJ8m\nX6aOO10UH5wbU2Y7m3uWkA3ZJrIMkM3HJVjwfLQ4SyI/+KTxMqh3/+41wFGF\nUoNyivFGeZAtfiF2UChv0kn2GWoY8VzIErT4IAiBFCQ2wV9rXmXZPzyHo5iI\nnJaqm/MDvjY8WAa4DRP2aVX6QYrh78f9i5ReOfTg95aYXCGoY/mc694/1axC\n3Aq8j09jLweecSt0yblJDcmx02aoUY1ojIe1tr5+MAlULQrMrPxZZD8qElxa\npefG6Ck1Pk7ULDEhIMFtEopNihUXsIecquIYXRj/89zyM6lSxyk1z8DqeL1l\nP/jTcpKX2NsIn9om8kflYlxrcXTBRhn/YRUGyp8eiHophdMVEcuZUfoEU1CP\nD+wwciksmGtRPAZeZKs20ny3E9iAcPR6yGssFJVKDyRS1c7E+lM4K5i/+m8V\nD/3Z\r\n=SAMq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.14.2_1625754667409_0.30339928048144094", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8d70789a3891d0ba0e17f2c02325bf15b6b1061033e0a6071451a524bb833c92"}, "7.17.0": {"name": "@types/babel__traverse", "version": "7.17.0", "license": "MIT", "_id": "@types/babel__traverse@7.17.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "7a9b80f712fe2052bc20da153ff1e552404d8e4b", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.17.0.tgz", "fileCount": 6, "integrity": "sha512-r8aveDbd+rzGP+ykSdF3oPuTVRWRfbBiHl0rVDM2yNEmSMXfkObQLV46b4RnCv3Lra51OlfnZhkkFaDl2MIRaA==", "signatures": [{"sig": "MEQCIDa1pBsw1CgfNrFiUT9vg0ZvnmyKdM3S+nZULo8OFyBbAiBeGEMjWyIPI9E3IyPkSGq9FigQIz/Rf1DgpM1IfIyDQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCk4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJng/+PEpV6TrPa8vWL+j3cvnD+IBDpo8a9rTLtKu2RGL7RRZQ3zXL\r\nHGykB2QSMiGSiSzPeBcesoWG6b+De4RmGT7A/FgGxlM/UrjgmfABwPPnroJ9\r\n2NDWLju/hQT1DuYtHS9CkQkxDT5HerV/SPJ10bnB3iRZqF+chIBvSjDpVB3V\r\nRI5vKC4cdc87pPlUMfwXEDE3cRHlHPKpTDiIvfuoXSuc39CQTEUmpgMZ8sKv\r\nbiU4NBXZslR1pXRQQIyhr3qSHAx/J7YfHrHUWEj97KBHiEphAcbGT9A21s8S\r\neLnRGhlUlMg6D/8s8Qow14jUVmCW/WxksqeKhSqQoFiNTZc5eOBwMCoSaRYG\r\nXon5gD0MK/5xNyK88BUF187vtpCAi5rz2zfiLx8TFdgXimAkDPHtTsScGpsZ\r\nyClg/Q/wlbvPUEI4J6BCMSMOwqD2py6F63ncNl+V2s+7G94a/84NV4u63XKB\r\nMH6OdA1BLGmEkzB7moCkQMNw3XdjBQDBoqF1XAf4xrOVYFJZ4sY/sYHQLROp\r\niBu/dW4hYcvdXXYCA5ZHsquco9I1YFlSrxmaifMCBYTFHAdvLNUKh98YVDc+\r\ntXyywRFU4P0Spx3lJ7PTJ4qr0r74Yz4bAm6VcHDzZ8r9x0SbyppJxli8Q3j3\r\nBDKmLEyLjXq6y1rmvv3MOUYKsQMwAgRmR/o=\r\n=yALg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.17.0_1649944887948_0.7912383562746848", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5f5b5e6b162185e039cb9f12c510c5434f7775b443146f532d696b38c3b839b7"}, "7.17.1": {"name": "@types/babel__traverse", "version": "7.17.1", "license": "MIT", "_id": "@types/babel__traverse@7.17.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "1a0e73e8c28c7e832656db372b779bfd2ef37314", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.17.1.tgz", "fileCount": 6, "integrity": "sha512-kVzjari1s2YVi77D3w1yuvohV2idweYXMCDzqBiVNN63TcDWrIlTVOYpqVrvbbyOE/IyzBoTKF0fdnLPEORFxA==", "signatures": [{"sig": "MEUCIQDYPOn35EjQgV9sJscv+s10JFWT0NElRiOt5YzP8biEugIgOWtbbYLKa85ylNWeR5uzdQJNnCHF66F62HCxXR5u63s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaEi2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrViw/9Es0Cl0IQ8ig8VwlqBlTU6+oZ1pfn+EhgZdfin5dw8kG/dU7C\r\nzwvGkIvSvD5U30aI5qze+iUxvxzjblSLfYzonmch5B95UfTu4MojeLrWYqc9\r\npnIA84eQXUYL2QctyR/cTPdb4+OuhIPWh/0ibcipFqvrXByrvlAGtY7U6JZ4\r\nkG7YvniL/EXz3udRAuaxaPMS+YAFcR7YrtvPdsWPnR4hEqrb5gWnhmqAt+1n\r\nbOT609mPcgam7Mf9W3GcNL1WL5TvA/OcjdoFAKjhkxbjEuhxI64MWuPoEr6X\r\n94UTiK75rP1+M2NflkKauX7BetM1UNPXhB0Cai4q4b4kTrbQlRLNysY8ygOL\r\nOwkIS8zTKTKu27l3DynKgfC+LenHGuovW82Yqm111SuBzuVIB3nL6SlO6ZUN\r\nHQs/Tk8ut0k32aH6vR4S5UGGB+hEXN0SdIx7uI0XR6q2lPV8GcJhzWjL3ux2\r\nRM43UNn+FiTp12SWT8ptJ1mll7upK1pqO6maQlH21wKmL+SzeKXPtRclywH1\r\nIWCG6gNvWAwiwf+WUVmc0rjADTgW76b4I+ik4LrtenFJC8zBDT3S6aeR+KKy\r\nqiPgsw0+QW1YZDs3HwiYtV2eESq1+9eynFqBX6MFE8E1zKBK68SDCpR+e4I5\r\nndIyQ3ORY5OPqAZ1rd2f2+eEPBQtuF3dI1k=\r\n=oHz+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.17.1_1651001526469_0.8395557786353491", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d430eccc1da4dc7d45845169edca878739a291e250fc5ad783710089241b0e14"}, "7.18.0": {"name": "@types/babel__traverse", "version": "7.18.0", "license": "MIT", "_id": "@types/babel__traverse@7.18.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "8134fd78cb39567465be65b9fdc16d378095f41f", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.0.tgz", "fileCount": 7, "integrity": "sha512-v4Vwdko+pgymgS+A2UIaJru93zQd85vIGWObM5ekZNdXCKtDYqATlEYnWgfo86Q6I1Lh0oXnksDnMU1cwmlPDw==", "signatures": [{"sig": "MEQCIGX7MIpQ1cSjevHrPBR4x5zoEQ8vfxDk/KCEl4+Gr55RAiA1vFEP8oLdNdz41sKxe7lHVnFD6g9NsRwQ/onPs+OpDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7DrmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEeQ//XpduOxI9hk9Xe24xK16Kk/EI+S/SIW1U4iUo8utbO26fWIv4\r\nDqTeWCnWf/2VBFW8bGW5xIg6jIqxRHCRgCsNSKPFnJ4BF86rRKctUh/1Hu+1\r\nHfivFQvWSfry+6XPL9rzUUcEOKhvwzFdPpzwOfcO488PWX/Ewz8TuGbk8wEz\r\n0z1qcQSlhLCC0nmLzTGtJ9EaeOFbiaQ9MeNFq5PM2T1VauJ0BkdaKdt56NO7\r\nVV1qlI8P82zOlHNm3/3pnTnoWomkFMdN+9REpnUof1joQpK1QUMoxQsv+Lcm\r\nqOZs3OofZfeJZrD8SSOT8XXaJwopCnd4JKPZFV0S2Pb/csdgkVWttwkPA4qW\r\noUafU+tV6nTBOFpvLZTUAPnh9gG0K0L3ieFaRuGMxeAgM3vN7vWlNoJi5JED\r\ncI5A2wKlqDm83eSYGosGTUTNJY5/UwCGrwiVqp0gL/qntn0M66qDvFHv6m3z\r\nVNKVO1/eO6MbOgn/daKAL8NOnTapRSV6nGnzXWJSXIjYvlXUB7V0WfsgDk8R\r\nC0GzhCBmyg0Pa6ndu13FIhfa66YClfteas4Ne1Zsx6Q1AuB0txYzgU1Abwiq\r\nXRDFmGm/TwkRVSjPvcvSQso5kfXsRsla8iQVoNrGQ8pDKRRcIh169Z+urweU\r\nlQ09XFyJ3NIfZn0yuRClalECLeS07sT1xZ4=\r\n=CccZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.18.0_1659648741904_0.7236979348751054", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "de5297f4ac0934f142c06ea89ad287c44a105cce9bd92e931fecb79090e8e487"}, "7.18.1": {"name": "@types/babel__traverse", "version": "7.18.1", "license": "MIT", "_id": "@types/babel__traverse@7.18.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "ce5e2c8c272b99b7a9fd69fa39f0b4cd85028bd9", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.1.tgz", "fileCount": 7, "integrity": "sha512-FSdLaZh2UxaMuLp9lixWaHq/golWTRWOnRsAXzDTDSDOQLuZb1nsdCt6pJSPWSEQt2eFZ2YVk3oYhn+1kLMeMA==", "signatures": [{"sig": "MEUCIB9Vuphxxfl9udx605hbj2AuNRQq1vf1RSeWjgQoYORBAiEAjUatxMIPsE5n2p+51hipAEBv00vjW9lm5GzF6QFW52U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDUWeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhyA//a4awTEOJqj1z/6/stlbuBIgWMkxLTN4KfNpzqrEcQELBTNQ4\r\nyppP1h1d3689qfiq49sKC4RpZ+MZmg+fGt4E3jWcCmdeoSFk4rpmmNsqoX/p\r\nbxYiuT6vicE+x6H/kt8cKR5jfm9dT5wveDFm2yF5e9RJBcyPuVRKag29tLTY\r\nZF1XToDT8r1VAe/W3pW0Ipqzda+N/59jZvlqHI4aW0bZhS8lDMzj26CjaCu1\r\nBVnVvRXb0nqQ1tO19eKdIyXC0FoBwbjK2tV2AsWV/hqp4ejRtlKIzjWvkn/8\r\npB9ctMw0Nb5OYevFG17WsBNr3uSZF2onfZ6plo/kxlEnfXu2h/o0kgC8w7dQ\r\nejGMHYUcu2FhY6nT1SenlgobxzKrG176n4DaUs4AinefZj3ioXCF4qNrCVXW\r\nD7LeZEYuBGyjnGCkyAKztAGCiqgCqvX2phFjBu7vDz7mC4x8DTFe+E5A1AAO\r\nDZm+6XnZEn6NDfxaa0/2UuClQFvXtLf2BINXEG/BrKeeK7QAH2dPPOGpVW8/\r\n9u/d0cjDZXio/zPZMNrcTBrrdVnKN3f3Fg1NDvkS0AcQoDbb/H2cl/uTK/wJ\r\nuRYANUzBJIuGXKDsww6rimNu+jDa2nVraTpDPd74e7znG6nDOxwmNvlhxsLj\r\nrkYWNKLjp9TQVM1Eo6+S1NsJiXoefNtgyN4=\r\n=SvhB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.18.1_1661814174690_0.6577653257051244", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "61325de9f0b74cba96eaeefac07933081282586792f85c40749bbd2a07de6684"}, "7.18.2": {"name": "@types/babel__traverse", "version": "7.18.2", "license": "MIT", "_id": "@types/babel__traverse@7.18.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "235bf339d17185bdec25e024ca19cce257cc7309", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.2.tgz", "fileCount": 7, "integrity": "sha512-FcFaxOr2V5KZCviw1TnutEMVUVsGt4D2hP1TAfXZAMKuHYW3xQhe3jTxNPWutgCJ3/X1c5yX8ZoGVEItxKbwBg==", "signatures": [{"sig": "MEUCIQCIWs0aMRQbCKgxnlOzH5eZUZXU2aHBXf6q4TNp/H026gIgdPydrF8tN5PIZgjHL+W5G6eCvue+ddmuI00PCis6Gjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLkI3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDbxAAnk12wtudwA9uNS7Bq1iB5wp1pE+GcCTFG2Jv8J/mFrrMtFPk\r\nXycltGGzU8JgA6pNoxYXa1q9YfsEnGQVpxPyG1kx/OSxG33YG5jsCnxoZHHm\r\nYKLJk6dZ3UPc/kFDD89FEfIlqtGSDDbzgOnHsBrbTmHQTsWYY02BGE/K8ruV\r\nUTbWpZmWLnXUbC2VwmfZEvOvu4CXjp+R8w3Tg57RA/vaCo19HlikSXCZ5b0L\r\naIi4vJaBMlC5g/DZ3qshev43+siEdsOcI3APeiSoe6byNQmI0sQtVTefzrlq\r\nMZz8LqU1WAMWU74XbWIjo8havdj6cq7arBHoG6/3zzh7Rj2GIyqUSA6FRJZH\r\nTjKsiM/Dm4xyGnauXw4WjykgulLsHDeRyTKGo+I1pQeTXcm2JvAvQn/wbXyF\r\nMCpIlNuMbYAQcZN/wzg6+oKMOkGG5NtdDVsMZ79FBScxxbTHw+4kKGJiUl5v\r\nHYIXXSyd6o7ufryH4VDEOUZJACT2kAl4WX3GCom1PWHh/IGsxGQjB0yCCpRb\r\nsoQqA4Si+Dik6sE4t/se1qXMFWo+EbDt1tlbwOPdZYc/KUSAB1dsNZh2OOjP\r\nOI1TTC4f9BKkpTSGDfNadqNoqklm7ZID/sGb+Gfa5HIwTDJI+I0issVPbNlu\r\nlT3xBAoGxVqEnTSnzdtZohl33t58peH4z3A=\r\n=Q8sm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "typesVersions": {"<4.2.0-0": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.18.2_1663975991446_0.4971444019337359", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "958350175760d50fb378267c7e49c9ca32d502c40d30b9d217a61afe36db1b3f"}, "7.18.3": {"name": "@types/babel__traverse", "version": "7.18.3", "license": "MIT", "_id": "@types/babel__traverse@7.18.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "dfc508a85781e5698d5b33443416b6268c4b3e8d", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.3.tgz", "fileCount": 5, "integrity": "sha512-1kbcJ40lLB7MHsj39U4Sh1uTd2E7rLEa79kmDpI6cy+XiXsteB3POdQomoq4FxszMrO3ZYchkhYJw7A2862b3w==", "signatures": [{"sig": "MEQCIFa2my/VwYt5bDU1Bw/N6R6XythqWI5aJV17DXkKCurpAiBM0qeHRyqHkWOud88hlaxKKvBIgCFKUVzcj9hBXxLtVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhOm+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSNw/9EwnJ9ElX3AB9MunHTszFYlvjingEo/MG+z7+LEpeqephl1Cu\r\nrzXksCLOIqRSLTlEuLvEGoBNgJBt+BC5htXjk9a2B0O6CPZ7+inqiotkI+Ud\r\nZLDls1miprUUPORGUwjim9AvDrPZyWbxkzw916tmEseAena3Ftp6CGyPkAkx\r\nDANiml7wvOcb89bqUrsfVldhka01JPsGPSxAqH0NV7ib/iuZ25Am2Y6yaL26\r\n0X0fG+Pju4tX8oZa6/8tlhQg5lbf6HT/5fSf+hcYwMlCp2422FHMMZ4EbrZ1\r\ndTKozUCp4EMmiw/MMe1Khe70hh6QQ6YpDrOexHks/vR2pOJ0pXpJqUfXpZhj\r\no4TC3xTq2XKAXtphUQD3jMSwHx2hu74sE10aOpiuif0RGWieZEikdByDA/Qd\r\nwEgWzqeYzHj/Xnexig/80fdnVGNqjCRWQlmX91cEWRczc3R0AscWuv/244nx\r\n0jMHo/uAh2wZUaqWCs6hJLR+1kayjsbagKUG/ziazCcAZfArXVR6zkHstO9q\r\nZ5JrRw4DyqiEZjgaZkhAFruU9Gbn8/G0vUzY5D7bXbIKP+ACAOzKZ5Y6SLF0\r\n/Waa4umyXP5QQwaU1JA3bAN6D9UN3AxtNhc+D+GMDxwNFBCHKK4giPc/7Xez\r\nL9HdXWgKCzRTwEc3t0OW2ItFzVmKbMtPDLA=\r\n=XSst\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.18.3_1669654974758_0.06929880434792168", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "794d0cb7b862c906728252af03d0e0a3f87677ffe6a927640a79bbd482121d29"}, "7.18.4": {"name": "@types/babel__traverse", "version": "7.18.4", "license": "MIT", "_id": "@types/babel__traverse@7.18.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "0fa6be6c182288831be8c31c35dab6d6ccac47c5", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.4.tgz", "fileCount": 5, "integrity": "sha512-TLG7CsGZZmX9aDF78UuJxnNTfQyRUFU0OYIVyIblr0/wd/HvsIo8wmuB90CszeD2MtLLAE9Tt4cWvk+KVkyGIw==", "signatures": [{"sig": "MEYCIQDwTjmAkZXK9dwO6s4PXX3W7SMci9WAe8NoSim2YQvA3AIhAM9DAQe/RxltkOug+IGOvQ1l4g7YvmLYHZMJQri3iUg8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSDj0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrayBAAgacK+tKEtTQF7JNFuKRWAATtJfaWWDXDGulw/mAezxNEDhzA\r\nlL0dIvgX5JB5lEyb4bq+o73yu58FL870QFX83om+ypRwwXdJngATO+38HIgk\r\nNbliLv8iZDwWl5rE8eE+NXHwZ7eElUSVEVNyXMMNXoK7oecwiTZ3uKWjXxsJ\r\ntwlP9xyDt+W5P/CrI2Q2LOY5vWeUu0NxVcyW6dQuZLNCEddOJECs5udkeXvO\r\ngv6Ol5cnZjAt2WNAyb+mYJHUq5kMxFchRzGqsslc9TvXHn4hwAxXfla1duU3\r\nDDUTRtW+pfcrEYV14m5nLAxdhgEkuBj567gOrRYdxmBXrcyHYpPWc0YqF58w\r\nMPzYA1X5FlvX3D0UVrTlhprRC4Qu+zfgbXVy3zautXZrcyy31tMWRkxZlGVN\r\nT0wVcLY7sz9apd3Mm5obRbXBWkEap8z4O14JwQkes+yI7yTyXsIxkJbxFzRp\r\ni7xlpjjTVRtqQtIft61UvRtl1pNfFJUBC86/vM9i2yCg4X3dPW6IjoTEH5Uu\r\nv78WR38IBvadLGsmStxVq56DXMM/UYyh8G16YF40+b9Yvd3zLFeqtEMoP4fz\r\nbdLIMOfilI+Nh/HETKo2/mJlSIEiD1IyqigIv/1DLk2JOyGp9Wb+K6PeGbpZ\r\n+4o9XK8SKOPSZHcrWqV/bWlZnJiMtDIA5z8=\r\n=hWLl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.18.4_1682454772593_0.4871625542509932", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "98d58bb0352e7d8aa887b4664af8d5c80797a41eaa1b5b7826e720988fea559a"}, "7.18.5": {"name": "@types/babel__traverse", "version": "7.18.5", "license": "MIT", "_id": "@types/babel__traverse@7.18.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "c107216842905afafd3b6e774f6f935da6f5db80", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.5.tgz", "fileCount": 5, "integrity": "sha512-enCvTL8m/EHS/zIvJno9nE+ndYPh1/oNFzRYRmtUqJICG2VnCSBzMLW5VN2KCQU91f23tsNKR8v7VJJQMatl7Q==", "signatures": [{"sig": "MEUCIQCpXrl8gq7mmE5pMXLNcfOxlfvXoUQpo7+Dgv6zqGfK4gIgfzFiRxwTF6FQGpxCYvt1IeXEB3jNHnMyqd3OmSoi2Lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSVIwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq08w/+PpuvLBaeMuYwlfz8Qq5dJa50qBmOWl5pyHiatVcu4XZxpYvB\r\nE6tYoYzkdPpFfkrBaAQI3Vi8bkGnrhHV0tOkzDY7egzzNEBzWCmixzKkQZGd\r\nhZFThrsV4lMnRo7P2QLFQhzHM1XT1MzLKFuPvqheMJSQKSzsSw6FaLfYkCkj\r\nal6KyqVHlw5A97WnFzz+oxyLhWzcBNpVyt6JW+CnSsEiwhfIRwRo0P83nBts\r\n8nez0XCdx1vydFuHDFckeWJglYC/0VlF9t8N6FUFOWt8Rr/aoNsg7FBd3NZc\r\n2YVKfgCR3lwivnkxVMl9VmlFKZTk/EscHnRPj7Lkva/b2va8i3uajI2EVvIA\r\nPMVpbSwCNuuWV6rA9VCFtHgHYN5XfzCMtETex9YV6JeTmLAqcumgF1S92vnI\r\noW22QCzeVUeRI37eCuHNrDCbUwMqS2cRqrQ7jZRWYcub4m0qyJAllNLS73Fr\r\nDpDZVf2sPoqsC9VVIdNToxt9lpFlM8wSpf9ROGucFXql8/Levk751JosXmsx\r\nbjzrxVndry7XsUn9vEiguB9+K75QaIsnJ/pgUd7uXKYoyJeFuByWBfEB+mck\r\nk5xYI5yNh8XEm1HZpBAzYZMy3aPCqoJD+87BuCr6pQD0SIQgMBcaZnWOhtQj\r\nq/2HhZixaUxfh30O3I7dEqEv2N+qvUrrDIw=\r\n=xvky\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.18.5_1682526767928_0.22059535003228503", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "794d0cb7b862c906728252af03d0e0a3f87677ffe6a927640a79bbd482121d29"}, "7.20.0": {"name": "@types/babel__traverse", "version": "7.20.0", "license": "MIT", "_id": "@types/babel__traverse@7.20.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "4709d34d3eba3e1dad1950d40e80c6b5e0b81fc9", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.0.tgz", "fileCount": 7, "integrity": "sha512-TBOjqAGf0hmaqRwpii5LLkJLg7c6OMm4nHLmpsUxwk9bBHtoTC6dAHdVWdGv4TBxj2CZOZY8Xfq8WmfoVi7n4Q==", "signatures": [{"sig": "MEUCIQC7uOoH4qQ7yqNBExkS+C0y/MRVbSfRPo9wq5LbAyP9FwIgd8C21LiTNutpc6K+EnOlvz/KyBKHbOLipSnygDq8as0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164684}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "typesVersions": {"<=4.3": {"*": ["ts4.3/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.0_1685037791356_0.37580064643039357", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "481d6dc0d96730da15b3d642d418c48b1cc33fa620f6f03dd0843dffa8d5b4f3"}, "7.20.1": {"name": "@types/babel__traverse", "version": "7.20.1", "license": "MIT", "_id": "@types/babel__traverse@7.20.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "dd6f1d2411ae677dcb2db008c962598be31d6acf", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.1.tgz", "fileCount": 5, "integrity": "sha512-MitHFXnhtgwsGZWtT68URpOvLN4EREih1u3QtQiN4VdAxWKRVvGCSvw/Qth0M0Qq3pJpnGOu5JaM/ydK7OGbqg==", "signatures": [{"sig": "MEUCIGSXw9AHDqC8wdtmnVJIHe93oCzXv68SxqedVn/ulvDbAiEAmU+o9umO8rrE1Eezeuf6opxxilWRo7v48sYTo1RwGuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84783}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.1_1685662369408_0.19263954425691665", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "58840239b2cb51cf9cbdc8fcff5170b0acb4f147d98040d4af1e883878991e8a"}, "7.20.2": {"name": "@types/babel__traverse", "version": "7.20.2", "license": "MIT", "_id": "@types/babel__traverse@7.20.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "4ddf99d95cfdd946ff35d2b65c978d9c9bf2645d", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.2.tgz", "fileCount": 5, "integrity": "sha512-ojlGK1Hsfce93J0+kn3H5R73elidKUaZonirN33GSmgTUMpzI/MIFfSpF3haANe3G1bEBS9/9/QEqwTzwqFsKw==", "signatures": [{"sig": "MEUCIER7cHsyt4374b1nCamgByZ0oG8HeM7965FH9NlluXEEAiEAvow9XbAJxt7G7OJuDCWUiOlWM67VDeM85wEzBfV48qU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84858}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.2_1694805074226_0.8919169925424555", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ad52fbb6e7bb74b5fbe483b684018114ed20c02d7e50de332f9ca9cf77436e16"}, "7.20.3": {"name": "@types/babel__traverse", "version": "7.20.3", "license": "MIT", "_id": "@types/babel__traverse@7.20.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "a971aa47441b28ef17884ff945d0551265a2d058", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.3.tgz", "fileCount": 5, "integrity": "sha512-Lsh766rGEFbaxMIDH7Qa+Yha8cMVI3qAK6CHt3OR0YfxOIn5Z54iHiyDRycHrBqeIiqGa20Kpsv1cavfBKkRSw==", "signatures": [{"sig": "MEUCIEFmQX+yIJirVxZl5DZ+KsTSPmDOAMlxB9U6xTjVxeeMAiEAjCl1bEDFsyHirvZhbVKEJrKTIMzd78JabSg3RuWBOmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84109}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.3_1697583402624_0.18528472096805215", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0a1a9806168064cf13da36f29145e8d317a8e1e39c4a5163f57d902f548fbdfc"}, "7.20.4": {"name": "@types/babel__traverse", "version": "7.20.4", "license": "MIT", "_id": "@types/babel__traverse@7.20.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "ec2c06fed6549df8bc0eb4615b683749a4a92e1b", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.4.tgz", "fileCount": 5, "integrity": "sha512-mSM/iKUk5fDDrEV/e83qY+Cr3I1+Q3qqTuEn++HAWYjEa1+NxZr6CNrcJGf2ZTnq4HoFGC3zaTPZTobCzCFukA==", "signatures": [{"sig": "MEUCIDC8Cx5q/28wqlqKxdtWdpyyuwY9NW2CTF5faOiIxPkfAiEA2+sK2fJFY1iDQ1fsA8FLWVMIVSTKshtVP0M1tovSe5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84109}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.4_1699314014705_0.11944935292188275", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2696ae572d8385f796b1cc12ccacb3eff4a194d88a1cc0d1fab41adccddfa411"}, "7.20.5": {"name": "@types/babel__traverse", "version": "7.20.5", "license": "MIT", "_id": "@types/babel__traverse@7.20.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "7b7502be0aa80cc4ef22978846b983edaafcd4dd", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.5.tgz", "fileCount": 5, "integrity": "sha512-WXCyOcRtH37HAUkpXhUduaxdm82b4GSlyTqajXviN4EfiuPgNYR109xMCKvpl6zPIpua0DGlMEDCq+g8EdoheQ==", "signatures": [{"sig": "MEYCIQC9J3Uj3QpV7lQ24GAw51oLlWr+sHlT5uQtU+jNyjuC8wIhAMqAuV8MEVgiPG8pTrM4qa7hEyHPtVF9vupvSL1HhULl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84125}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.6", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.5_1703941613537_0.90757918506075", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "093ae30325ad46951b36b01e9456bdd07eac99904597c1a48fad5e01adda62a3"}, "7.20.6": {"name": "@types/babel__traverse", "version": "7.20.6", "license": "MIT", "_id": "@types/babel__traverse@7.20.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "8dc9f0ae0f202c08d8d4dab648912c8d6038e3f7", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.6.tgz", "fileCount": 5, "integrity": "sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==", "signatures": [{"sig": "MEYCIQC77ZqyE2k2z7/8OdvA0o8+PzoYdnzT4yQGnPHetNHAmwIhAJ9/Ay6o27FmnJbbLkBucy2r/H2x+s+/YzEFYLGoFVMR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84139}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.6_1716325633141_0.42751756349270864", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cbe25b5500aad0414e291c229529da2d7ee3ac79072c3c2f95bf1bde4d81316c"}, "7.20.7": {"name": "@types/babel__traverse", "version": "7.20.7", "license": "MIT", "_id": "@types/babel__traverse@7.20.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "dist": {"shasum": "968cdc2366ec3da159f61166428ee40f370e56c2", "tarball": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "fileCount": 5, "integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==", "signatures": [{"sig": "MEUCIEp1IlQHNRcxAg/vyaqrrNiqLHwMW58hFStLR/PsuX2zAiEA3Q63BbgGlQLZ9/yWg3GBmLecGdpC/BM6dUsmFq48RfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84954}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "directories": {}, "dependencies": {"@babel/types": "^7.20.7"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/babel__traverse_7.20.7_1742976156850_0.7642132022715233", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "e58d29a4d5c39ba4fa0291c8c7d5abad18881f7ed9f938feeb97726ad48a0544"}}, "time": {"created": "2018-07-25T22:30:21.866Z", "modified": "2025-03-26T08:02:44.652Z", "7.0.0": "2018-07-25T22:30:22.183Z", "7.0.1": "2018-11-15T22:59:18.281Z", "7.0.2": "2018-11-28T15:55:22.458Z", "7.0.3": "2018-11-29T17:53:38.588Z", "7.0.4": "2018-12-03T00:23:20.841Z", "7.0.5": "2019-01-28T16:40:08.891Z", "7.0.6": "2019-02-13T21:08:32.418Z", "7.0.7": "2019-06-11T02:00:53.902Z", "7.0.8": "2019-11-19T22:51:24.613Z", "7.0.9": "2020-02-20T18:30:32.523Z", "7.0.10": "2020-04-06T19:08:34.866Z", "7.0.11": "2020-04-27T21:43:38.225Z", "7.0.12": "2020-06-01T23:55:05.736Z", "7.0.13": "2020-07-06T20:44:44.734Z", "7.0.14": "2020-09-08T21:01:59.618Z", "7.0.15": "2020-09-25T23:59:42.949Z", "7.0.16": "2020-11-26T07:40:52.903Z", "7.11.0": "2020-12-08T10:25:04.480Z", "7.11.1": "2021-03-13T09:41:12.022Z", "7.14.0": "2021-06-27T13:31:25.247Z", "7.14.1": "2021-07-06T18:16:57.699Z", "7.14.2": "2021-07-08T14:31:07.558Z", "7.17.0": "2022-04-14T14:01:28.140Z", "7.17.1": "2022-04-26T19:32:06.637Z", "7.18.0": "2022-08-04T21:32:22.062Z", "7.18.1": "2022-08-29T23:02:54.862Z", "7.18.2": "2022-09-23T23:33:11.604Z", "7.18.3": "2022-11-28T17:02:54.951Z", "7.18.4": "2023-04-25T20:32:52.788Z", "7.18.5": "2023-04-26T16:32:48.179Z", "7.20.0": "2023-05-25T18:03:11.547Z", "7.20.1": "2023-06-01T23:32:49.613Z", "7.20.2": "2023-09-15T19:11:14.430Z", "7.20.3": "2023-10-17T22:56:42.865Z", "7.20.4": "2023-11-06T23:40:14.964Z", "7.20.5": "2023-12-30T13:06:53.867Z", "7.20.6": "2024-05-21T21:07:13.278Z", "7.20.7": "2025-03-26T08:02:37.052Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__traverse"}, "description": "TypeScript definitions for @babel/traverse", "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rpetrich", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dlgrit", "name": "<PERSON>", "githubUsername": "dlgrit"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/danez", "name": "<PERSON>", "githubUsername": "danez"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}