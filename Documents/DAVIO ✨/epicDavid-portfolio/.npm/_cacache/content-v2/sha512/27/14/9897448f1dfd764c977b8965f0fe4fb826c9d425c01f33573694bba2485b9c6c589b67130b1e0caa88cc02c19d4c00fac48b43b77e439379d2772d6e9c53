{"_id": "node-int64", "_rev": "30-cee384dfe672fa80f9ea664f05a6e41d", "name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "dist-tags": {"latest": "0.4.0"}, "versions": {"0.1.0": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "./Int64", "version": "0.1.0", "_id": "node-int64@0.1.0", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.12-1", "_nodeVersion": "v0.3.1", "dist": {"shasum": "bafb5e952bcca078594afef9111c911708e5e42b", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.1.0.tgz", "integrity": "sha512-yWF53Zk+xVY6Iiv2+k1JGnjjl6QFVCi3cVs4O/T97dmkU5A5q3LgndVd4eH1KVfaOK9jugeAy6FM+SGvwVLBxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIyt4BZwAejiT6Yvz9inMy92O5ZpRw7kDwotc816Ix+AiEA5JUq9HZ2bCHc1F1B7IXl8kekcOwezMnto6/H8K6/dHA="}]}, "directories": {}}, "0.2.0": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "./Int64", "version": "0.2.0", "_id": "node-int64@0.2.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.1", "directories": {}, "modules": {"Int64.js": "Int64.js", "README.md": "README.md", "package.json": "package.json", "test.js": "test.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "a360efe26a9082bc16888a7277cb6d7627165f94", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.2.0.tgz", "integrity": "sha512-+waWImFK5ofJnl78ay5+kukbD4FDzt7JZJwutmaTHVJRsGZn+O/gJS+8TlmStc4e5PYVj7+1oO1v4vs0iMv11g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmDrkstvUpVB+PWcuumx6gM9Glbdp3S3VUUEDJwSkbBQIgTq2EaoZ4QJRZWmGA3MmZpO+AsVR+j4KclXXgKhuEngg="}]}}, "0.3.0": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "./Int64.js", "version": "0.3.0", "devDependencies": {}, "_id": "node-int64@0.3.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"shasum": "894bb7c497e7c614b52ff840519b6cd660222fe2", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.3.0.tgz", "integrity": "sha512-3zx+/MeBtflZ9hm4X8/Hh1a9XGzIxTKMKdC69uJ7+39mBTuNgg/8/oyQj5WGDwphGvsXRxXxEG1vGGgZ/6ePoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARC4U/rK4z0TNF/OhvbmPyi29wwlVvz+HdoV35aSwq6AiBf+SVTsFiy/txtl++BJNjJbOoRVZyt8GmKD64dXGqVqg=="}]}, "scripts": {}, "directories": {}}, "0.3.1": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "./Int64.js", "version": "0.3.1", "_id": "node-int64@0.3.1", "dist": {"shasum": "49e56926247ad0008575db10a5e3b41369189506", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.3.1.tgz", "integrity": "sha512-NtiV2jWVVLzCYjVqzU/UIfhX6WyYE/E0SXXeJohOaU+aMRuQblS37QTTTrNM9YtqvG5hcjVKBngB//KnkEhviA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCztX8bRnlKU0KFTNeAwMTRxYNA0sPhPSOFwo2PVBXJigIhAPsa4D97LeXMqMz741Ct0RVXJOJ6GRfnDbxfX9qgNTN2"}]}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "./Int64.js", "version": "0.3.2", "repository": {"type": "git", "url": "https://github.com/broofa/node-int64"}, "gitHead": "cbaf8eb9afe013cb66b3849498484f2520579ac7", "bugs": {"url": "https://github.com/broofa/node-int64/issues"}, "homepage": "https://github.com/broofa/node-int64", "_id": "node-int64@0.3.2", "scripts": {}, "_shasum": "dc03748aefe60dbc4733a4201ba12161269f3741", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}], "dist": {"shasum": "dc03748aefe60dbc4733a4201ba12161269f3741", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.3.2.tgz", "integrity": "sha512-kyL00s83Ld3fkAesmkKGmohsfOwpGkYaFK4CPu9A7OdXbTh3rPL/N5m1igMkpmBPnwLDg3F6DdKOB837y28puw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAYlEQix5dZXTwZz855vknJ0wt2CdXeG5U+dmR89ogc8AiArcntT1RPWU2RNR4RcjmhyiBIMmhErggitkRl44Bm3zQ=="}]}, "directories": {}}, "0.3.3": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "license": "MIT", "lib": ".", "main": "./Int64.js", "version": "0.3.3", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "https://github.com/broofa/node-int64"}, "devDependencies": {"nodeunit": "^0.9.0"}, "gitHead": "21873768fb14d6ce954507f229e3db254328a183", "bugs": {"url": "https://github.com/broofa/node-int64/issues"}, "homepage": "https://github.com/broofa/node-int64", "_id": "node-int64@0.3.3", "_shasum": "2d6e6b2ece5de8588b43d88d1bc41b26cd1fa84d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}], "dist": {"shasum": "2d6e6b2ece5de8588b43d88d1bc41b26cd1fa84d", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.3.3.tgz", "integrity": "sha512-bLdNOp5SYyqfDz/ssGHt2OTg8u+jEkCx4EoZIzprqeonFIUhlSBrKu40e/x6hIFYJx4ZEt64/9IZJyafvhGZrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEe4armndXxzqLyH9OoJbQqRq9xNh16Suk//VttsYPLaAiBBRMFSHyuJtE5jyNW/xKb1tMAsJrcU5lsaJ0mTSk0s+Q=="}]}, "directories": {}}, "0.4.0": {"name": "node-int64", "description": "Support for representing 64-bit integers in JavaScript", "url": "http://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "license": "MIT", "lib": ".", "main": "./Int64.js", "version": "0.4.0", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "https://github.com/broofa/node-int64"}, "devDependencies": {"nodeunit": "^0.9.0"}, "gitHead": "c1567475712cb1cfe100c96813c2a2a92e2b42ce", "bugs": {"url": "https://github.com/broofa/node-int64/issues"}, "homepage": "https://github.com/broofa/node-int64", "_id": "node-int64@0.4.0", "_shasum": "87a9065cdb355d3182d8f94ce11188b825c68a3b", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}], "dist": {"shasum": "87a9065cdb355d3182d8f94ce11188b825c68a3b", "tarball": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDfg4kjKAf8n+lW22IaEScPlFzwmL/aUAGYvm4EfJGwVAiEAlWDIudaBo4CVef834sQL/82VP6kUbImYrwY9g5QpRVw="}]}, "directories": {}}}, "maintainers": [{"email": "<EMAIL>", "name": "broofa"}, {"email": "<EMAIL>", "name": "jeking3"}], "time": {"modified": "2023-05-26T16:12:34.200Z", "created": "2010-12-30T15:27:28.086Z", "0.1.0": "2010-12-30T15:27:28.433Z", "0.2.0": "2011-03-08T00:00:51.014Z", "0.3.0": "2011-06-21T17:52:11.242Z", "0.3.1": "2014-02-18T11:37:28.557Z", "0.3.2": "2014-12-02T18:40:47.086Z", "0.3.3": "2014-12-22T13:55:53.834Z", "0.4.0": "2015-04-04T02:52:16.354Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "readme": "JavaScript Numbers are represented as [IEEE 754 double-precision floats](http://steve.hollasch.net/cgindex/coding/ieeefloat.html).  Unfortunately, this means they lose integer precision for values beyond +/- 2^^53.  For projects that need to accurately handle 64-bit ints, such as [node-thrift](https://github.com/wadey/node-thrift), a performant, Number-like class is needed.  Int64 is that class.\n\nInt64 instances look and feel much like JS-native Numbers.  By way of example ...\n```js\n// First, let's illustrate the problem ...\n> (0x123456789).toString(16)\n'123456789' // <- what we expect.\n> (0x123456789abcdef0).toString(16)\n'123456789abcdf00' // <- Ugh!  JS doesn't do big ints. :(\n\n// So let's create a couple Int64s using the above values ...\n\n// Require, of course\n> Int64 = require('node-int64')\n\n// x's value is what we expect (the decimal value of 0x123456789)\n> x = new Int64(0x123456789)\n[Int64 value:4886718345 octets:00 00 00 01 23 45 67 89]\n\n// y's value is Infinity because it's outside the range of integer\n// precision.  But that's okay - it's still useful because it's internal\n// representation (octets) is what we passed in\n> y = new Int64('123456789abcdef0')\n[Int64 value:Infinity octets:12 34 56 78 9a bc de f0]\n\n// Let's do some math.  Int64's behave like Numbers.  (Sorry, Int64 isn't\n// for doing 64-bit integer arithmetic (yet) - it's just for carrying\n// around int64 values\n> x + 1\n4886718346\n> y + 1\nInfinity\n\n// Int64 string operations ...\n> 'value: ' + x\n'value: 4886718345'\n> 'value: ' + y\n'value: Infinity'\n> x.toString(2)\n'100100011010001010110011110001001'\n> y.toString(2)\n'Infinity'\n\n// Use JS's isFinite() method to see if the Int64 value is in the\n// integer-precise range of JS values\n> isFinite(x)\ntrue\n> isFinite(y)\nfalse\n\n// Get an octet string representation.  (Yay, y is what we put in!)\n> x.toOctetString()\n'0000000123456789'\n> y.toOctetString()\n'123456789abcdef0'\n\n// Finally, some other ways to create Int64s ...\n\n// Pass hi/lo words\n> new Int64(0x12345678, 0x9abcdef0)\n[Int64 value:Infinity octets:12 34 56 78 9a bc de f0]\n\n// Pass a Buffer\n> new Int64(new Buffer([0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0]))\n[Int64 value:Infinity octets:12 34 56 78 9a bc de f0]\n\n// Pass a Buffer and offset\n> new Int64(new Buffer([0,0,0,0,0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0]), 4)\n[Int64 value:Infinity octets:12 34 56 78 9a bc de f0]\n\n// Pull out into a buffer\n> new Int64(new Buffer([0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0])).toBuffer()\n<Buffer 12 34 56 78 9a bc de f0>\n\n// Or copy into an existing one (at an offset)\n> var buf = new Buffer(1024);\n> new Int64(new Buffer([0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0])).copy(buf, 512);\n```\n", "readmeFilename": "README.md", "homepage": "https://github.com/broofa/node-int64", "keywords": ["math", "integer", "int64"], "repository": {"type": "git", "url": "https://github.com/broofa/node-int64"}, "contributors": [], "bugs": {"url": "https://github.com/broofa/node-int64/issues"}, "license": "MIT", "users": {"gfilip": true, "sloanb": true, "surajs21": true, "princetoad": true, "andr": true, "akiva": true, "nbuchanan": true, "flumpus-dev": true}}