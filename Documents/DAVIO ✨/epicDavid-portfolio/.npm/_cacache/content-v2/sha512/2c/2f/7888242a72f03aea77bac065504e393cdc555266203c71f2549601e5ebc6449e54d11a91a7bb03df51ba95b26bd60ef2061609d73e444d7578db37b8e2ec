{"_id": "@babel/plugin-syntax-json-strings", "_rev": "33-4b621f9a64e5be6ebbecb5b1717d28d2", "name": "@babel/plugin-syntax-json-strings", "dist-tags": {"latest": "7.8.3"}, "versions": {"7.0.0-beta.48": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.48", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4b5+8LLchCCHfnftVeivG2HuB+L/vASHvlYxEeC600tmtHx4fhrKejkc2GF64SW3hnQZ1h1X8gPV6kx5eT8AAQ==", "shasum": "acc29539738810a2149aab3843d898bfcbaa8c69", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDJCRA9TVsSAnZWagAATRwQAJLrfUia5TA8DCgbnwAp\nlcGFaivmh2eoH2MnA07nuejO07+0gv080caRgLul+Fjtk0Rdd1d4T9pEOjMx\nTlIXaPTMcpuHle9yZ7OzqjKPn0VvIhks7ieIA382RuDFQftyvAJCO/fZJX1q\nJkRONsLe4d3daUG77rrkmOw34pNphEEh995ENaXsyULQGqPQBjCVsGzDjJXa\nMEOnnqrzgijfP5DFHdTsyXfOnIbN1X9d+4s0jLVD3lHgNv9lT5da6HNqRaBo\nu7witoTDeQw4wx3fiA9m/8E9bgcZWsN6GBqit/AOiVCGeei1JwWDJhEL/Rrg\nUAO4GWGy+IqdSeAdDL6Nrf+bIU86JPa5H+sZLd3waugPmdIYmxww9Tki5RYJ\nAb5k8GiAw4b98dbYEPEeTPhNaCupVrJqUW92Zosd9b9IGpwxGa0gj1sZLFXG\ncW0SVXJQOc6Te6sWPfGBTlJChHcPvbAIvqfBGHbfWvQfk4Q6DCLjNoD5L76v\nCAX+PNC6f0BR2B5L2RxRkfqZaBQ8X9LLIo6JUN6ipMe1Dy+5MfNwFtsT14wt\n0v78Sc5XKw2oDb9gn6nusMj2/OejqwxK0fVGRlKk2vcdc8Ym2cvucWO5NEXO\nW6U1xypjhMJSZlHRMSe6aNOxSsvy2irDgiLo6hvoKnw2mRF3LLdHGmzHgM04\n6FTC\r\n=Rce+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDddLjPrD+vk7UblxZhc6ffYOONKFpFigicasThxyvKjQIgBRH4RXwY9BG7Jkp/jSDMjPiS8QWkOLqHaZZRgVTaHqM="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.48_1527189703937_0.2123972028787684"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.49", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.49", "scripts": {}, "_shasum": "c92fe78b9ff2d92c58b3d18b9ceea9cbc04a519a", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c92fe78b9ff2d92c58b3d18b9ceea9cbc04a519a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNOCRA9TVsSAnZWagAAMVAP/10vzRueLWIAdmOB2bJI\nGnzxuwv9BBh0btZ5bDhrWL4znD4WVYcC+43zse5yavCN8C0ANWwqp+bK5VYC\n0XpXHDQtrBZohkbfNdWV/pGQnCmLjtvOHCAftD7gorSfvNj73c4aptPMJaGU\n4nZ5IY6XH2fV6O5lSqpGX8DVTzufkdD4k/7L9o8fQ7Xoj/88Dnr/ilUd6iB0\nR92JmbBtd4xs5c1nMwGw8NZRUXQOeq6Zs+/Cym5XXrR3i1pt4H1SVuTAtyey\noiJgpNbIQc6EpsOAq5NpcG5PslDnYkSJjYZ/vmlVdBG0qGbUrn1QTr/5bEBs\ncOxf9mAXLUSZy4eOgcsYwE9Si8l9xAnaGU6zLiApl4AbTur2U3/ClbxhlXp1\nlio4IN/3Trv8zEzR1iG7cOmFAjYjaqsCWI+fGyboP2lZ2euPAHpBSLVuxXyU\njLQU8CQKKgSXlmlgwb57SeEntg1U8tajkpFe47NBrcFZczocxMRpowVbIA2P\n0Z2Qw34CUrEO55z6Sup5wTZ9EZlnHAViMyX4OuKjgyaouh2ZUdDshGZ8+G6b\nntqpuU+Vwj1lk5Y7CVXxDeSWuLsIkPbpthgsQqS2MX8uzrBex3GShN3wTy0i\nGo5gcTfW4LVyKUqNgytaMGBgzmcbwiMlfe8f4fnIJj0THv6IOmaFi9GOpzoU\ngY3s\r\n=VsVz\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-bE4g/TKGgX9LdRNtInIRenVP7TI1RqDfcu1zC9IQkSLGsH2mpOyUzBPCT9PQmxaBavv/uot62K5AO1yX2DlSRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBcYeqWfgoT7n44ZMWwuz5hq3V/nVVK0UWZosuiNT6AfAiBOzprpOSDtKYFRyMR+4YndfbRcbRCo8K8daNO+nX0Y8Q=="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.49_1527264077801_0.15312994804979652"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.50", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.50", "dist": {"shasum": "fab36d8fa09d85b1f808535b2e623f2796bd098a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1519, "integrity": "sha512-zOyheWuv+bpYX8Jz/nqpXjyc/IyiYTtxgAnm4jiSIDNexD4O6Kx2iqcJsGBvoy/E8Q2cpm8tJfJEyMKjY0VSgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDztE0G2qH3s5pdBuvaZlvFo4rrOmfjkWgx3AfmeW9IaAiEAv6r1MLzo1fE415Q6cFyd4lDjShU/tU/ySoW8cZ2Tmqg="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.50_1528832826287_0.501802183626668"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.51", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.51", "dist": {"shasum": "9b6ecd20817746c2cd643459bb545bfdbfa461f7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1533, "integrity": "sha512-aYEQ2PoUH91GMMplSjK5PZEcTd3rMnJAhDor7OQyeA5Xum1wD/a7yh7w0Wp4Dpbw6h3QYpl6OYlC4KfCe191ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGG/L3SINFn38o1fRS+XY+Vd8xND2GwKZIiDOyFHRwwKAiEA+8g0Vr36INcZXFSJ5pAHoI9RklZrM3hynqClrdC1TqE="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.51_1528838373158_0.11131039355246841"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.52", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.52", "dist": {"shasum": "b2215255a919c628cdb9f0ed4eae8d8e69c759f4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1532, "integrity": "sha512-ljismKZU6oJrusN1lHdd0goeq+5ySdG2othhDGUKM+HNHOJ2vKCh8UGpdmQM3TEJFVwC9nje+UqUb517b8nAqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHF5ZeWp60L3gx2rVa0FqbHnaNZNti0K9n8nsO6OM+ZbAiBuc0JEeAAlkxP2TEkXTTdPhaTKApUK8t0A5h8ZuRS1+Q=="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.52_1530838759230_0.09474150208388332"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.53", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.53", "dist": {"shasum": "bca120c4489af97d9f47b541dee3f4fc4afa8084", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1532, "integrity": "sha512-+n2gDWfKrlhwYlCsOuHL2Kd/V6wFwfA1KoA6tQb/6F9sDLI9kqn+dUgFWy9L97tZd1oOi6Kj/j60GfDe88ENTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkKjBh52BXkz0vOjGvkQk6JlhcSfWm4vwfXvSS6y9JdAIgGNivWGiALyCFz1ah7T26lcphMsScuseiK9zTVZmOWWY="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.53_1531316409405_0.5015740171263081"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.54", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.54", "dist": {"shasum": "7f53c35362f359e5881e21278ca3359dc0bb7055", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1532, "integrity": "sha512-BJbxxSiqW++wBGVvZCOxbS0mRxLKyUi7y+BiWz/hBa1WNdF8Nw3GzjEEAVaa5kk5aZexUEB1TNtj5GYxNEoX2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXoOWdRHIrFiQHNulqVYz7cCBA3VJw5Y4O4ZJf9WvhWAIhAKCm4FX6dvGq+ob0gKntl7LyrcwtgnW9lW5oj8h65t50"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.54_1531763999301_0.013768658021830582"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.55", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.55", "dist": {"shasum": "86644d0aff8263d52bad029f261bae0e21b3b7bd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1532, "integrity": "sha512-l2KV5cUYBeJELeDJd9YCKN0sLkkQxI8l+RwF/qq1pgkKeb3HwGofRgzHLVTal3WkXNYMEeQT3CH+YFffmL4pZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxS2vfGlnldaquWYF7WaargFafY/+hHKHqAl9mnJD1BwIgKWjyLKd39CjxqR9FcyBs5Ar6GC9kYx06GA1y5WZijvU="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.55_1532815624422_0.08444587404001114"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-beta.56", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-beta.56", "dist": {"shasum": "fbd792bbcd8cc0df407d8be45ffdc59e00b52a46", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-beta.56.tgz", "integrity": "sha512-TQyWy89jngGffx0o8WURU1PHU/hDcacTE1+8cFWml+FOLa50rw3Q9236V0Pl7VOEAm0StkYGZf3glwk3U7Olvg==", "fileCount": 5, "unpackedSize": 1532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPufCRA9TVsSAnZWagAAmLQP/0kEl7FDJtdDykJ/sZWL\nEWnB+UReAjU/f0+QPpiRLhQGpOYoo2La0jsUBW26i2/v1C/bU9y0Uw/1T6Or\nRaWntTdlmKKjT+hoSNKvT3CqKVtOb6BWK33Z69EnQwwNlvo2gFhfaYvIE274\nALtuwHrZvLQ+/aS/Y3Bc2pIDWbclkUp7NMgzfO0iObbaM5VpSUUywjaOP87j\n86j3rviUR/i2fpWLanqewjSu+gPRS6UPkZgZRYbkePd6Csab4xazco6jlsxv\nD83/WjhrlhcrJ97xhx0It+475MG6zZTs9eQT5P1EDBUZ9RuiG6WhZGzHNHyu\nzdEdAKUVxhKrOqxkKEesWX5VxfAXFj/P7XY2LjqBgpTHfkIoBnzuV8PsQ/iT\nkOFMP7vzUfE9XLxC7O9unbE+yfKKMIgpThHrvHkMo5/3IjohBrxN9uL3+4y4\nz28iTPu79CseCHeTMnOkbyhMvWnZXQdU+EgJkEA4GBGcu0jVSiwO1pGfZ9/6\nJOQ7hq//s2oTAXSsVtE9Ey6mLM/PhB1ILyBiDuNuB+/TcIzrOTXG80HXYQFy\nmD4WmD2/lnamMvLNfowht4cc7qVlRa25mmcWHid738qwHrHICEvfkYAacG/0\n9D8SiVg6mDxOIXlzWaAe61CGfd4xQKbn2TT4+mQLK/JZKNFlF4wlzBMF9XPk\nNwSv\r\n=Piu2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSx+CeTfiy2oVU+nUICtGqC+ZJ109mITufzanaWnYF5wIhAK4zzunrFjCYus72ruqr4WCXIKotCKAHpxUYWxb1RBkv"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-beta.56_1533344671084_0.7106464400669199"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-rc.0", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-rc.0", "dist": {"shasum": "07eb5ac4e0f5992549c3ae78bab9ecbe9e022416", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-rc.0.tgz", "integrity": "sha512-tOJVPcYMnr2Yk+3p/JuAspfGMD8oYNFitSy4Ia+l+lC3qwfvkWjv3Z2EINRlNfJHBNBhnsUBTcOgsvZdc70czQ==", "fileCount": 5, "unpackedSize": 1523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRxCRA9TVsSAnZWagAASY0QAJYVxPNjBOU/MeFHhet2\nTk3pKIjO3TTmGqy4mqXbwq+SNmdXWkTMdgUhPGheslDWXH1JbEwQoMbMvexL\nXpaAiIqErmGeqw1+8LlGpkWOtpcrlKYjQCV1heoLCh4Y2nAXE6phtAgaawhQ\nd6QMjyVWlctwXGxYXC6A6R1UDdLOsIyX/OU6irVR1RH+JkemKaRyGvgh3xUP\nOkSrZixH2svXjUHiY+1AII/mayFAmFqwouijTq4vrSNJGO8Gqu5+FpFep4Ef\ni7C29oNiCM75eaL6y4c1rLGI/FXKN2D32GUKoh4BJCZZAzyjWpgTtIal+tRG\nlccD9yTZNsXwwmB0dzHwL65dAEPL8E22AEBU9LaLEutSRS7klUhcHT6gBQN3\nGQme7fWV7kdljjD1+/X6w8ShB3ftPxkRINjxLrMiVk4rkP7hX5W66SjPMV8o\nfkGGPzEveVzpzc0b3OU7he9s/G1ZC1QORDZhg4MatB3meyzII3L3g4VdrGWK\nDxxPCW9VeAGyDgkCyQC07/PssQspMGt+Ez4ooQw71qNInac4WqzsqF08+m26\nxUNsD5H22DpO6C30JVbAhfE4kHi+zujL3j3Rd4WZtICXyEpa8tCoxmRK5maN\nH91DJ1eOG9u5FRuJ/nGdRc044lTl+uGs9+IKd2PjwGLd16YCUrqpeUrsgn2a\nPvUP\r\n=ifGP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDsify9gavnNMuz1qucCJwZaDKNgI62QcR3yY3qnp+aAIgM6qMfeC/rYG1ksi3qf7m3b5tUhHV6a9zVXXQ/NOeE3E="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-rc.0_1533830256955_0.011560356504270564"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-rc.1", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-rc.1", "dist": {"shasum": "695773425de0197327bccd917db1852d11563cf5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-rc.1.tgz", "integrity": "sha512-ho5vlvDBreZ6BxbB241TkxCCfaubu+5oIroWHB4bMjoers94zFPWysI65sT3wTcJusSawPEHkLt/chXaPBAauA==", "fileCount": 5, "unpackedSize": 1504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ78CRA9TVsSAnZWagAAZPEP/1R8/d6fNrPAC3SFhSx8\nNClET8bmbE2UpybMtMfGM6cOgJ18A4zJj2uHVXK38BaangPnXypodGX7qoeO\nNQ8akslbrStTnaZ+nrL0840HKyqP4T/QfccX+8ELAcRtvpUHNlclTWESDZQi\nyYNvxF5gxhPw+hBSlKX0x2dz0n5m7kJ7Q+dwnJCx/JmmhbfhY6tcXq0paJ4H\nqVIOov4qtt/Mwp2mlHujtuKNoDBCcjZcyoaqsM4U9l7q/eiKeus7N1LhB+s+\nKduYyKqWZvXPXutPcyDQNVFNkG+B6oIOiOV4itxwU4kHdoIpiK9dHsLCf9rC\nR4QvJwQrNIvDgsAnMWuLJbQck3a+hHHiFNtEHweWppuoMbSx0N9BqDtHHAg1\nKxZ21DCcWm6YMxZSvRlAueMc+sU+BJXo0wtHXl5oRucIY9C9Zm9UnKoihiPd\n34RahF2Ox4d6LutjwPdQK+fvHSloS0tP+d/YkJ0td3joZD1sWAo/POJCDt/8\nV+3ZItlUCeFaoisIdjfoQrLP7O0d6X2JCcGjDlQoxmAMqL2CM4yics6+Vm4y\nQdclMgYU6AvX9QFLGBaBYO2vtdNqjT0lU+btbIF3+8i/F21165VoCY6buPeu\nuID+lSOmRA41mjKa3WyxNFcuEsw2UnIjtfTDqTGNHAir/JxsVQIcctlLMtph\n2UZS\r\n=xGCf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjdpfjYXjlxunDehlvj4HOP3XLsX/ItmtfDEJYuLR+7AiACltaqhSSZ5kf+mtNCvXlxNMOfE67cAepK8rpaRq+CBw=="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-rc.1_1533845244133_0.7505529320324567"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-rc.2", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-json-strings@7.0.0-rc.2", "dist": {"shasum": "6c16304a379620034190c06b50da3812351967f2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-rc.2.tgz", "integrity": "sha512-CvojPyEfHjkwwHJATQe/x7F3xBuaB3wfVkOAvsnvGiLTnUDx2E1kYPsvPGoEfWgdS1zkLGlwtPH621bS3rYKQQ==", "fileCount": 5, "unpackedSize": 1504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGamCRA9TVsSAnZWagAAVHIP/iGlX3WCvoKmAPX0OAJw\n0gFtRD2JWNEDcXGsD/JVGlkIDA647Dg9zvdaQPxPLZ7hWQGPLgppfCO3Flc9\nl0vHMgYBo4Nd1PEJ6FLMo83og6zFJtL4KL9XdQaLh9W2pEVrrVpO10IQP31R\ntfRh4YZ/VKQnpaeuyuluDL4NxhChuiG7mUUDAJ+mkry6M9lGR9KdpOtxdRkp\nas9wgxPVdxxkBoFZtxCMivNWpjlCqNtTsOt8Z7R3spIl2jfnGThbb6eKlvOJ\nsTdH4SHNyisGGX6UB+jZfK5s/fuxnzt2H9y3Vmdsez/e2j8qL4Lx2Kn5+SxB\n/jat4d0PUD2U2i1eq+eeQ04vKvGpvv47UMqYYs75qBO9KxSD68jRVDa6i5BK\naGKI966Fn0xBbuuojewLqaHgdy8nWLCy5C4Hzswnxj+y43gJWtQIrF8gU0yG\nmloYGMlOapHAL4PK8lQOneRgDKLk0YFbXXvPypUIP+PyHZlmiThmYoStOUzY\nwz5X3Zscmaeeevg/YE0bONmilERA/zlMUHTgrJrwG66l8oJ/iEzY79Gp6nZV\n7RXUkYUehEWf634DgZ4BuQeH9Ib5aIzHsJd1U6Y/tQ3miFZt398c2SVKZDeE\nVjmqjxQ/C3jU/2yAP3j916LBHz2VnTxjF5T3JwJgWQ3DJDaHmYs0HbkWhjjS\nCrLL\r\n=xeG6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxn4Co3X/6IGyPC2sT53Ow8St/FgU3Y2k6oGCrQKzxhAiBXbunuewTPTZW2AKJXsmEYgT0K5gmxGo9KauEbmONo4A=="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-rc.2_1534879397622_0.5191055591662428"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-rc.3", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-json-strings@7.0.0-rc.3", "dist": {"shasum": "5dc7b1f713de140451446a6c36739c15de857a95", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-rc.3.tgz", "integrity": "sha512-5FI8WWV6OvP8nG1oy++OCRqZTC1o2jj5A/WAe62y5p+2RFu40wo8kGfCPs5V/K74lVjVrGG40SlP0Fa81O2Pyw==", "fileCount": 6, "unpackedSize": 2603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElXCRA9TVsSAnZWagAAhjkP/0RUJaPirJX9K49eT03G\ngKvUwfMBDlrpbALY9zGi9///efMnR+drCL//T/bOw+FnhzoYq7c+wRpVcw2s\nXBDZnG5j8fU3VasHmog9zcLrRExm7Rbq3ec/0YWzpaY9qG7ACADoRlTZl3qq\nQhRxCFdfV0mB5hToBSuXeKGGuJ/MD7t2gKFZ+qr1pUJBvXVBAwF8XQIhqisq\nYYourdKV8NXcEuR7xk8EIAerx8hbEM5f8iSv/gU/h3/zJAD50BcHHbvkOSL/\ndGQkAn8eEAdxqcTJ1a7Xic1vkhrp1EbvsWYya98IDko1a/BMFhJt3ggVBLt9\nuIlMVO4jKgR1I80iUVDYZHPbtWo28zZk1VdwkPOlQExVT7+ZrW8/ThBrkyO+\nPzKzTF+FQ8mlMcM5LNOonJlt7FIXYIP6lci7Rc6HUxs2cT0KIsvGB10DzrUL\nDTjeIxXzv4xGQQNNmtlsbkAtMexLVlDILAMlx0Yd5cDl8jUhEes8I6ffGPBi\nwXnkYZvLVFBm1auyfqJn8sRsPul6A6EgoHNMf6lAXXDTP2r8XXlePGLaowqq\nHV95DPbO/qTOpu4hBIPOFb0bnHYqsHCUZne2nDRKRj2gab5djGnfZm+ZrKqz\n76avU0BXwQX4lEQRkZYhGKfd/p0r3k4h59+XDKuvjab3rbRUwD+ggwKvgztV\n2HiF\r\n=QkpS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyRou64cNXDyGr29U6nKW2WkYDUsJOf9qQ1HeEO8uCMgIgEGJeCFacq4bca0R6jEzaLJHhVFA6XyqDdT/NQ+wD+Po="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-rc.3_1535134038650_0.9254743990167678"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0-rc.4", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-json-strings@7.0.0-rc.4", "dist": {"shasum": "6e2e96a0d8b6ecfba1e97471194da13e5c222e4d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0-rc.4.tgz", "integrity": "sha512-BLSjM4a/0cBGncZZt1hk0Wu2FTzEz/XGVdswjjlijqIm0ve07xn4J+b+2nHZdBpIYz2E39QvD48rQ6KWfVHiSQ==", "fileCount": 6, "unpackedSize": 2605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoyCRA9TVsSAnZWagAAw90P+QDTGCwUS/JGnB4V/YLz\niC9DdR1O0H65KzgNpRr7iYXejdc1V5lgzeYsO9/nQZxfuS8BNd3Ym4BPLtid\nfj+nXakw2d14b4ZHTHh7RflzsEb7mLPnWhtzUKRu3ZyTq3MscnhaC6kAx8QG\nASNwnZfUfBKVO3HYBAmhP49+M7LgVlu4hsu1FiX4u8RWs2vFNh5QU86oLNcS\np6A8d6GA8nU9CI9RI6BHemxKQHnMQuKtbauzdPjPvtwZAgOPhIrj6PghHvl+\niyqDY4HNLfWtk1Y9sMRLGRVRjMnAwAgmp2kAS0I5zmZe9rko87oIO1Mca+1Q\nL/7gMvOqMARYNnzev/pH/lDB1/Zj7J0wyqd8to9H1xTJIdvaTd/5R+/oAt+Q\nwFu2WSHnuOyzteE2vDOlocoaIczbigCnQVGur77YuGWwtm8toThwwlMAeaHW\n3gY3CBWEcLaHYhax6QNwzs/N4dMYYNGW+fBh5cIt8AfjIdxDJQFaVnzNAMyh\no9Rkkz0ttVj9tK0fglq3tpk9iMWjFKNsfxPxeRqmYZjNS2g6LiLY1CpwMsK7\n2i4aF5QSOy7sci3X43HPRrwvRK5vgapfbSuaiMSixDFft25HBtNlXgGLrzBz\n/cjQxyT0Tx9aX4CCV+94AeNIDW479yjdmme9fyAYHgWqwsOuS22sXe9NhLra\naZiq\r\n=5AFe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuTBcZw58J77tAdywzh/99Wtf+BsMoeKLYHaRyAjqILwIhAMWgu189oLtz2iofd8067BYok7mljOc8ynIneC/Fwvse"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0-rc.4_1535388209602_0.9208628777420953"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-json-strings", "version": "7.0.0", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-json-strings\n\n> Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings\n\nSee our website [@babel/plugin-syntax-json-strings](https://babeljs.io/docs/en/next/babel-plugin-syntax-json-strings.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-json-strings\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-json-strings --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-json-strings@7.0.0", "dist": {"shasum": "0d259a68090e15b383ce3710e01d5b23f3770cbd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0.tgz", "integrity": "sha512-UlSfNydC+XLj4bw7ijpldc1uZ/HB84vw+U6BTuqMdIEmz/LDe63w/GHtpQMdXWdqQZFeAI9PjnHe/vDhwirhKA==", "fileCount": 6, "unpackedSize": 2590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBOCRA9TVsSAnZWagAADPAP/3UrRmrEtNS7TF9PoVgg\nMh4wj0PXlP9AfGqLulTK0DY2D32t7O8E/sywPA+5p4AVi6qh0O4FI1UvFAKU\nUyFOU+PiczOEFsZAej2aTZrQvpV7ja0T3/CC1v7G5YaGy5DJPyxh1lUcrzZj\nvnM9YUU3VUoUmC62f4dSCP6TOzr/caXNnFlRfJP7U07YPtDb+gZ3ov6Kt2Gz\n8UaHBCuLz21X6zypaKafZhJtAvYshhnUmBq7J2L+dYTD1/71VTk/pu5sBgj9\nTgiGJkj1DKtB6fiL7D7/w3bl8EeB3TQE8OQWwKsHAkRwMIT55BQ8NTP1UILm\nk3+Qg2J0q63hGGtVQT9Y3gS1wUydI0xgflGuFT/Sk66HTEP395Xc49DtjPpc\niqVMn2MHyxFmyWnW188ky0hN5dLA+WRQO5xYA9Y8iYROFVodMbmFBMe/uNNV\nBRNk7Hf6ZrfM3OOXRf6GpndqSA0LxTZL+Qc9ogsljAD02HLuM5JCv535w3SC\nXLdEJOHw9OQlNsIELdoNPWd5Exkx0L2nUjlpLSC1ADnThmLNmtAJz60dfALl\nRFRSDVFlVXP8CKwNgl0xqbw97yP8kbuX1iGv6P/SiYrG65O2OJxzTG0xZdMd\n3uwBWuPdO1avSONwmkUrB38foTmonua5BXgaCtX8b+50ok2MjMSM/yht99Xg\nt6mo\r\n=gYDH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEZBR4d23qSKRVqJa4Qpo/o2XG65IsSpB/3qMIthjEHQIhAJEpt/bV07aD8KP+jq0ZfoerrVxJ8Daff6de90AnFFlj"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.0.0_1535406157969_0.3787524312271924"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-json-strings", "version": "7.2.0", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-json-strings@7.2.0", "dist": {"shasum": "72bd13f6ffe1d25938129d2a186b11fd62951470", "integrity": "sha512-5UGYnMSLRE1dqqZwug+1LISpA403HzlSfsg6P9VXU6TBjcSHeNlw4DxDx7LgpF+iKZoOG/+uzqoRHTdcUpiZNg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1lCRA9TVsSAnZWagAAukkP/2YqIveyotl9P4PvF2bB\nLBXn4vKlhXkE/JRLe3ccyENjNB4XV4r86BdClEiznE0xqyUHb0AP0Sx7i/G7\n1tRceNFnZfAxnQOyb6FOh1ktpSd8nNtUc7B4DkkYGN6TNFnSsdyxF+pSYEsr\n5KCh6OQqZkiFskv9KFibih9YX9hGs6/tOX3wMC6BfJy+nSvkUfs6UK2azHCV\n9nVO8vuiErmG4R9v7qo4IIkT3aSQUubspuuA1pMMc4+8glUanznxxxjlA/+b\nTLptt1dVBTSxw+yrZP9cp2cpYh70N5jlc3l5nlDNYByjEyYddHDBTrrpXsos\n4YP0TGIRi8+dupR7ORFUooaXt5GUrqkY43/qJ1+Xpdcn2wIPPD6ln9sa5q2e\nBicpR2lle500Woa1EpsfpBxiw0DdLNk/YeyD3lOad9qSDHtsGh2chQ6DkWVr\n2yAtHfm+L9JgqJhSCW7hQEiuAUIhmVH44KIOhOzN3vnfm6LI6plQX/p9B9Q0\nQGWEwnGk0l6OkGwlErZXyE/XskRCjYR3IcB/cDpCiovaXubgloSKSd5MpP7a\nw8l6NFdCgAFTfSqZ39er+kyqfYNLoRHyDYYVynvxf16Kr6wTp92+M+RldnPv\nCKQFViddJV6GgOqs4MyfYCdFr53Ud/cpIuZSzmpOf16jWz4omVvRCZIl5Yrg\n8YXR\r\n=hUqv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG/GaSuWlr5W7MPe8DcO1GsC5Ykz8vnE9GMHYYLScueWAiEAux8W+flpFz6QbTRzBjbzSH41Zm/8P1F6SbRlJcwU7yk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.2.0_1543863653162_0.9861020685481723"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-json-strings", "version": "7.7.4", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-json-strings@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-QpGupahTQW1mHRXddMG5srgpHWqRLwJnJZKXTigB9RPFCCGbDGCgBeM/iC82ICXp414WeYx/tD54w7M2qRqTMg==", "shasum": "86e63f7d2e22f9e27129ac4e83ea989a382e86cc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/zCRA9TVsSAnZWagAArLEP/i056udQblFj+orGxQ9T\n3pgvdeCVFwgcXBpokixQ6cIvIHQhNusvrlli9EPU8ZWRleHccYf2f+g8f2dE\nW7NkOhU9SrE9z4nMbVMH/hOFLahpUEdSk8c6mdjzg7KwSfyslf/FrK0nFVib\ndFeEQnJ+RSknb/z+ksJ0AzPAU2agY14kgcL3ts6giHfl2dV9YA7CN2+cprsd\nvQTYBwE2ECqw7AD/oDGz0PkPnsEujOFb/XZvRsAAZJHdgEXd8ya3ZUOKYCqN\nlVR8s3GtJp6SzRxdxUJlqUTcLjqe2zRPryV5+okpuKsgVgly4YMBTsG3ROmb\nPt/qL+1aJ6sI4B0y2Jl0BBasU10V35mP1V+jNVYyIHWK+7QAxiBqEiASClGH\n8Ofo5NSm0F5lD2qv7LCfJDAD/C9tMGAwTv5BhiKq3dDY6U4p+/AxEMwCI7Dm\neWy8KkCfyb/AavCMjrbpkhbr3aL7ClPi9HHdVWUWzVrBece4kbdI99X1BNE5\nHi+0YENabyKRWtrPmu0x0vUfxA/7LGWFPo2y5mLpgOZZrrsasIHZL/URxtTO\n8r3DuXMHgJ0z+9XRZFbyEjf7GkRl8XXlQObOVTX12VjsWJbs8WJnMcYPGU0P\n6Uy4gFqjozgBMzofZf9q80v+laMjRGg/TfkLOV0y3G2Sg07bslUyTIlfl5r9\n+nT6\r\n=i7Zx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICOWnRgWPbPGA3DSql0OYgRPwnc2Xp3XSUwZ9s3nu5lMAiEApZJI2MD/fg8K/GplqdEpaMAsq0Zl2Nluoi+LpOzqJro="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.7.4_1574465523372_0.7705838756652972"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-json-strings", "version": "7.8.0", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-json-strings@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-LPykaAbH86L5NnDfCRSpNxtEHZk+6GaFzXfWEFU/6R4v69EXQr6GOp7hwH+Uw0QlYVN++s6TukTJ3flFcspahA==", "shasum": "7f798eb7e8cfd3821388120679d23d530bae6e53", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVRCRA9TVsSAnZWagAA8B4P/A/KIpLaJTBzQq4VwmY+\n3V4JY1FngrvPG6HrCtpHQJ7s4VWoUR9UL3nh5ffcSA2fD9Kmc8TY/WsTMteF\n5V6g0z9XPNFPKT5gWbpdcD6jKFyATuZ7jXJW3rgc2RbPjy/0TUyeHj3bNJ7a\nJ2MEqvEpfoYIfUfm53bTdMGGV9+swFAF8JKTEq3hJ6lcP5LnS7hGqQV7QqmL\n4yYXcI292EJ6LNRxVkL50O7YMBiarLwcpzMIW3egwaN3DvMwyKScYGmXPiab\nZkEbAuSKkKJWMjn5FbwF8r8E8izm9JZiGney/C3RllThtzOqtKJ1w+SXuVwd\nRB2g/j6m6NEhqOILUHEWu133MojiZy4j63CWTF4DzxIyK7aP69E46NaKjTU0\n5x3MdysVZkDY7D1OpsadCN7CM71JXFn/uGHoLiD2PR+qpMHycZMpBSS5zigd\nJPIM5+GnBwwta028Y0BBjOMg4OnL/nn4yWQAiavFYHdKEhhtSCdE/DXCS8Dw\n+ZWfWy/j6PpX15O3FljNzB76sABs6NOWD2B257rQagk097mcRKyUfwMC+WgR\n7HcJS8fHCAwRW3C0KL+aznon83XK3KGwu9N0p/vbW+YXW7J6CKfPkwUiaXib\nEPwd2+LfvSUNRUR5FKrwH27Fc9Y1gxS+Uqb4/RBFlr/xThWh/NegRuV4fkzX\n3XXt\r\n=KKLW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARxZHexi0VjvIVGjFX5xXwHHSTRNrD2g3N7ApTY98XyAiBVsgOvrOQV7WLXHFDxFyTcihL//fCpW8czPjhMsZocVw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.8.0_1578788177432_0.4159074073382736"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-json-strings", "version": "7.8.3", "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-json-strings@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "shasum": "01ca21b668cd8218c9e640cb6dd88c5412b2c96a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN1jCRA9TVsSAnZWagAAbpIP/3YPdVlMR5QB4ADGoZCq\nu7RqdPfoCJWCImpOs9lJP8loEPdboUrkYGQs+qwyamYDlQdwRZgeDAvvdDNJ\nPH7DGBTD9WDXXQqeo0lBXs/FVdb1/l+3JTjaFH0S/8MAC75lA4tEbKYIRNS4\nxMCA9XvI560ItnD3PgHZeMtr+DDAk292e9qZopI+Em/jQ9N6ip5q0+ef7kcM\ndhIKlebWaUkF4qQvRCnUgFlVm6bdZO3rkxxj59B+CVBAHHjbcU0BnTlgqdXD\nauiPjsU8uMwK2GR0xSfZBW/L2MH60mgxeHEUCaZZ77oygc1iR1bc88OdSiv3\nGoLQ8kOo3V1tShzmm4iY7hMVZRA21GlZPMxhfyo4ZKhbO5g3unbkc5q1/Y/N\n9JTyBJG1rUJmawptXsX5Bbs9MeKYlm8jte/izJ3HbXH9/NQFI63MG9Q+mk50\n/CTBFrRPJLoIg8zxQhlRVu59FFzwW6ztkfYLdStTQhJOiJXFovY5MRoTXqeA\nBrDLVxZqib5gHtRfC1ed7v1c0CXJgB6yB/35ZwQR7RcEEuyux4kHofOnZ2fh\nR46z93VWxXR/UUdLiFvPgGjoWiMR4fzaYDxLDMsEv29jKydEd6ys+N93Lh92\nIV7UgSf0xIo0JaX9YKFSWmrDAG/bBvMUKqotLsytsnWVJe8Xj+rGChTvgjwc\njo2o\r\n=ES/y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGDp/PZwJ1+fAk0XGG8gcgYouVXmKSqXCle0rnYDFkeAIgKIE2xBkgdP0q5C1Zx+Wwin/rKEsz5ciNqxaOKTuygoQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-json-strings_7.8.3_1578949986701_0.10842303485616012"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-24T19:21:43.871Z", "7.0.0-beta.48": "2018-05-24T19:21:44.820Z", "modified": "2022-06-12T15:01:02.526Z", "7.0.0-beta.49": "2018-05-25T16:01:17.878Z", "7.0.0-beta.50": "2018-06-12T19:47:06.574Z", "7.0.0-beta.51": "2018-06-12T21:19:33.229Z", "7.0.0-beta.52": "2018-07-06T00:59:19.290Z", "7.0.0-beta.53": "2018-07-11T13:40:09.446Z", "7.0.0-beta.54": "2018-07-16T17:59:59.374Z", "7.0.0-beta.55": "2018-07-28T22:07:04.475Z", "7.0.0-beta.56": "2018-08-04T01:04:31.181Z", "7.0.0-rc.0": "2018-08-09T15:57:37.019Z", "7.0.0-rc.1": "2018-08-09T20:07:24.197Z", "7.0.0-rc.2": "2018-08-21T19:23:17.735Z", "7.0.0-rc.3": "2018-08-24T18:07:18.739Z", "7.0.0-rc.4": "2018-08-27T16:43:29.760Z", "7.0.0": "2018-08-27T21:42:38.051Z", "7.2.0": "2018-12-03T19:00:53.280Z", "7.7.4": "2019-11-22T23:32:03.536Z", "7.8.0": "2020-01-12T00:16:17.555Z", "7.8.3": "2020-01-13T21:13:07.023Z"}, "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "license": "MIT", "readme": "# @babel/plugin-syntax-json-strings\n\n> Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings\n\nSee our website [@babel/plugin-syntax-json-strings](https://babeljs.io/docs/en/next/babel-plugin-syntax-json-strings.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-json-strings\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-json-strings --dev\n```\n", "readmeFilename": "README.md"}