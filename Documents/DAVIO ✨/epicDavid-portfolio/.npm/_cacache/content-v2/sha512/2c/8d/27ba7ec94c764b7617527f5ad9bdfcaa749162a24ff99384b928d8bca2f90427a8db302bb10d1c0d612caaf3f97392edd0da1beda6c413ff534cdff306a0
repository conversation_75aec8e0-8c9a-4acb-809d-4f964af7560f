{"_id": "@babel/preset-modules", "_rev": "16-3e47db6bd13af00259b9601dffeb1904", "name": "@babel/preset-modules", "dist-tags": {"latest": "0.1.6", "no-external-plugins": "0.1.6-no-external-plugins"}, "versions": {"0.0.1": {"name": "@babel/preset-modules", "version": "0.0.1", "author": "", "license": "MIT", "_id": "@babel/preset-modules@0.0.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/preset-modules#readme", "bugs": {"url": "https://github.com/babel/preset-modules/issues"}, "dist": {"shasum": "d8a3b52e62f08a8f778d7e93ef027597f7f3efda", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.0.1.tgz", "fileCount": 1, "integrity": "sha512-LXUOpRqws7wUmBZTeLnEpbRyUr/N+YLu7JvAP8n9ogIqcVwN81IkLgBwLlGJ5QR8W4UYcvO+06PfZQIWbMF5mg==", "signatures": [{"sig": "MEUCIDOXBSt/dO4ArzSN7FF8Nm7kPdPjr69+c456PlvUyTpVAiEA6/vqR/cgllSOi6wgu92QQx/X3lU3RoyQtvm2MWHwSH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxZU1CRA9TVsSAnZWagAAfWgP/1V1QktMVEWaNqBIWSp1\nZubHa5gHmISGGLo2Gv2a76RxhCXsNQQW705bVx85Tn3eF67aKj4BN09HIBMf\npcdy6GY+HqfZl9ieD+U9EwDdgsuM2zK2AqZs0f+Vt9VM0xRNDKXAnqhcdcn6\nqtAJLI03pq1mlgzGpWPrVb3/xMOnPsg8AI4036SCl6tM4HmmlWe8f30IdplQ\naGpv+JrbmYHNnKEg8nL1szk1pim8lJna1hwW4lZV07Ej8nLjsQQLRjS/UfWx\nzlRkYNjTbhypDweolbvE/fB6/7ehba+S8CmDU/MnmUgXBjSZs/vFvJ/lYv9O\nuJ5NBUcHzXRmnZVhpDaJeptn6HNviQeSF2GlDojxvq0ZGrRwaPBJrtSjRRfa\nlBIR8l8FcuS4bZ8uvMQGlSbrJ6qS3hxHfZjIpF6T58/chTzsMYgU7tigCtEp\nvGXmlx/XVn00KFA+S8cF+LN6Bt8ivejvN5UBTTiFdAjavliaSoNlY1U92Iem\nCLhsYEKb8npPhFsRi35VXIL8Ijtw4JVKOrPV+9qzUNG0Eci9FzL4ja+w7pQ4\nKFASPhkhX7Latvs0Cz44vW6uzu9iFLL8wgShnqG7m1jO3to9aaC8qOxuTbNL\nLHZwf2p2ha1zViXmvWAv8/Q1D3hlYh3x1zVzViXqGV5/ZnYCtT4W0bjLtOse\nfdeI\r\n=myIz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/preset-modules.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "", "directories": {}, "_nodeVersion": "10.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.0.1_1573229876680_0.23887120925989036", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "@babel/preset-modules", "version": "0.1.0", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "bbf47ed5530f8d663f0eb1c592864964b7cdaead", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.0.tgz", "fileCount": 16, "integrity": "sha512-6t3sNgGW4AhaR71sfXCVUq/4BlWDn1QhRbECD6CMPkMPxUhj66d1ss7xllftdi4WnrJWJ1RD3jDQnEqMcAFd5g==", "signatures": [{"sig": "MEQCIE9t0uL4m3FuluoKI5K8koJCnnubtigMx31d11pwrziiAiBQ+tSsFdsULKutmqjTz0rh5RDre4jxL80oU4UgXzFP5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyom9CRA9TVsSAnZWagAAl/EQAJJlN0C2+jnkt9n6MS7f\nt7giiBhBEzdFZ30WxzHoprtu4aBpuho6s7WTW44VGLYF/LIySlu4fc0NtoJn\nAh0h6konifNziL8FGNXFOYalZ9bYsWGdlf4U0o+3KDhtvWmfNCsDePa1luVw\nSmRB5JhxkeYlTF+q4ZNnCrvlJa95lMW4UFUv1RUb9YMDehC/eqoJlum6Y7JE\nMbNuTZvdxaU+R8IYZP8+kMPN4kF60VoryAbU44uOQ7xhIwYIjIvkEFrOybYf\nNsZyu10+JHFOKgnksmb6l4jbhMRsssNmzA7SoRpjH2Oh/oJhuywE4Nt3EfQc\nx/J0vwvVp1tyyQVKgK8kDLnUf8SEM50EcBUTR0V6SYVsGX8EhO4FktS7aR87\namzQipsN98ptFHdBkWf0hp8ZPG6g9hVdaDdbV488akoSg+x3jPuDe9ZaKiXn\nMuKcaeYTyp1OOMQ0d+L4BXY0u1awwQv9g4x5zh2JuZM/zBgncFJ2/aOLOHsa\nSoB+gg34+gjuNXE47vBAVjBXjLwbJC+wPwogvqdAFO1IK9gCNKlsI1Vz7CVv\n3ZERNcktuqxSa1DBF1szZuFdCBq6m1UB5hFFKd8Eivsph6htUduxzbMthnqR\no5JCQKAXUWFHu4kESqa8wMW+u2E0YiutofN0fGwvvCBurnAdf9yqS5d7WCHj\ntBoz\r\n=MCkm\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "8533b724e01d79ee90ba6b9e233d1d2e3b56fe36", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "_npmVersion": "6.9.0", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "10.16.3", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.4.5", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.0_1573554621025_0.49078242073227685", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@babel/preset-modules", "version": "0.1.1", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "add61473e3182771b36930c1312f3c56c114e406", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.1.tgz", "fileCount": 16, "integrity": "sha512-x/kt2aAZlgcFnP3P851fkkb2s4FmTiyGic58pkWMaRK9Am3u9KkH1ttHGjwlsKu7/TVJsLEBXZnjUxqsid3tww==", "signatures": [{"sig": "MEQCIDaowWYyHq1B4LoI4V/WLoJXNyZVxtK3cUocDbqY07rbAiA6yap5u5+tlWt+XZmVwuDLrTaQ2qoUX8ERYEYV2yeHTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1E6OCRA9TVsSAnZWagAAy98P/i6GpwfMeS+hK0/ykDki\nRAk8+ne4/ZOlmz+E7j28HzXtQWk5NPhZqgJxx99Ku+wvEmX69CBKMfuxVLkJ\np00OVmFyb588dDtB8ELK+QcsfyOSu5yJJ+8w8zWwJTrRD9GE+zF0nB/QgLIh\n78HXLcxwhmdbukLuzXfrbWSs7OX8CXYw23CY5rItMs3FXpfSremiLgJX4Esy\nuWYRR989FrFbFMEv1Xk/z1DfrxI8WHf4EnthbHz43yyK9EjqQKW9oHw1MsNq\nTFr6yCy1yjl0uvX9BIy+cZKlYogiUgweZHRLQVaNaAtabuOFtCPM87F0IjHM\n+fg1dqee0oCfxS9OzNWJXJUSI4KiNLckxjgMDlpVZyVSGwv7s0y3jufaNUsS\ncUjvKL+Ha2JjXGCa7T2xyDz/w+uCYpZBWFtLBASVKG3H1mu66/QSGob6+bnR\n97qu0kaeoC1Osxjk4J3C//ZuLd7mgiDuV8UrdYCViXQrGh2d53IPuKbvwYQN\nnp+arGp0H96jhIMjKWQw8fRhOYfElsRHd4HQ+Gn3nPWvtZkNMxWK05oBR5Y1\nzhgqIjgFGgvAjSeBRmLx2pjunjfs2lglcYVPDxh5HPh3/zyV7+ONtJiwvsxd\nqQzMfoHVNnez2tflZedRSsSJWjV2kSsZMIHSzmqDLicOyKHtqzkvkVWLT7vJ\nzSm2\r\n=Sfri\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "9f54f57ff26d6ef83c1522cd44e1f8ad15968ac7", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "_npmVersion": "6.9.0", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "10.16.3", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.4.5", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.1_1574194829799_0.18941528332929702", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "@babel/preset-modules", "version": "0.1.2", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9365f51343ee69d99351b2892ff7479ef4505e78", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.2.tgz", "fileCount": 16, "integrity": "sha512-FMhghLBAnMG7Dh0C/xgpX+HbzkrDDK9BdgS+NUuEzcro+ySmKKk2F9ROnqRawHS5aR/AH56rVtL/LM2vsy+81g==", "signatures": [{"sig": "MEYCIQCO1Jqhpk8WGtv33OlZIhct29L7udvq4liOa9WeG7tecwIhAPzxB/qtg891mpAs1Hq6PI7RJ2LltwMc/ZV+A+jD4Sw4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9ZBoCRA9TVsSAnZWagAAFUAP/3BUg+E9bXxcFBftBm4x\nNttLeyNm/HDOwBIJzoW+y+hh5R5XRWQjQW35EnKkgknaIvPXhD4uQzlYbnyo\nJO5KhHXcdIrP+1s00AKvwiDqIJNu1dFrQkm8Pm1lUAAD/fUz9tuIP3hhmt9V\nIxMLKqoXfLHw8FvlL681VPACtOB02psi4JkUeXEi1fJkZMDxeOhNj50g0CMf\ndaOoX6u8nKaZPvqn9l3UQp3WpJcISFbfa4KhJC1qlOg5ssNUciFkD0I3ybjy\n0VVaEGq49U54LEFZjpnfT2nzuq27QGIgKxTLG7FssYKfq3TFCwd9AGaaJQ/W\n6IDoWrVOwxIBwqQmgEhFv5jogFdgd61UZscoWB7lPcX4PblKpGJmT03E2wkq\nAiyRBAnFl/T2xMHuqYerf6/+IMD163ojnrxaguyAVEMH1UgqB7eEwthpANJy\nCKwB4kNGFTD6RRFpPwrwb3H3/MemRQYUlTPnWsIF/cymAfkXtrE00bcuVD8k\nJZYQ556lFGBqQiTivVovrp2BfoTCY8/M9qFhT43BpHStcUBQUonv2UoEGoe1\nL2sxdwJYSSRdG7lWPIo8kz00XYrxuRepcV9TyB/Tsepljy5uvXpB+16i21MM\n/zbf5XIS1NMVXOysG2bm3BmGCuupd8eboZBnjN4bJRXNgTlv/COzGWgN2qwo\nrkFu\r\n=nLqb\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "dd4a8795fede71a50b711da79129bc45caa8a6b9", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "_npmVersion": "6.12.0", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "12.13.0", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.4.5", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.2_1576374375719_0.3203059905873651", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "@babel/preset-modules", "version": "0.1.3", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "13242b53b5ef8c883c3cf7dddd55b36ce80fbc72", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.3.tgz", "fileCount": 18, "integrity": "sha512-Ra3JXOHBq2xd56xSF7lMKXdjBn3T772Y1Wet3yWnkDly9zHvJki029tAFzvAAK5cf4YV3yoxuP61crYRol6SVg==", "signatures": [{"sig": "MEYCIQClVfmO8PfCcMr401N0mMtAP13FHix6R9Q1EPz8Hn9+QAIhALkV2T3H472N3tMtJEHwYpZarHkj+dludPClrLggd4tY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQaWoCRA9TVsSAnZWagAApMEQAIduOLILf0NuSk0P9JKI\nX5s8KPBHmsYLX2gL2kwkg3Z2LeothzwayyKooaMZqqefVG3dOHWbC3VZw8F+\nzHK2XWYhPlslT5QhFA77MXZuk0MCF3TbiTZxibEE0XI/OLTGfJDKlUHCpt6K\nJY2T1T4458D95cA8qQk6JUwwOe4AZa+sRLJ/ZEh+Imb2E1rpPHcJ50nDQd11\nE9yhEAqAt0yazbdS7/BnfP4QFbWmP1Ntp9qkLZCCq/FV4/b3qL+FP1kmTgzB\n7eo8ZFDvUzgewF0JrstVhaTAtsAFVqAQ5ZRKEam0ep0VmkSkgcAyph3tdBcY\nLM5o21eAA6xkgClg90TT4ApN/fv1Dh1m04qc2LIKKPN0bWqFK61YYuJy8CH6\n00LNXxExm56+jZ1EBz/JsBDwge3Bn0cdzwdV5qydUu0K0RFUsddpL1NQSIXy\nw5+pgKd0iaEOCNe9P4mNyNZxrnBZjM4xf9NPjELrm/0jX6P3MkwOxFbjePyZ\nqkNfWGPIptyHFa+dw/MjKypeT6zzExAeJmGse/d4/n3SsWv8Qz6RRE13HJSr\n3F0wN5fH9uBsXEaetJt1zL6M7njShDYjW5SvCm8sGeNUzQnbcJpHzxBl5ZAf\ngljIOKsfRdpHN7gpdhQAAluESF4WE/BeRF6UNyn8ds6nLuNzkq0dRU/ejXHa\nDMy4\r\n=PKmY\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "2dc7d028ebb0296e8aa399d4e8ef97add494bd80", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "_npmVersion": "6.9.0", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "10.16.3", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.4.5", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.3_1581360552146_0.9719504435532262", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "@babel/preset-modules", "version": "0.1.4", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "362f2b68c662842970fdb5e254ffc8fc1c2e415e", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.4.tgz", "fileCount": 19, "integrity": "sha512-J36NhwnfdzpmH41M1DrnkkgAqhZaqr/NBdPfQ677mLzlaXo+oDiv1deyCDtgAhz8p328otdob0Du7+xgHGZbKg==", "signatures": [{"sig": "MEUCIQCZ4T9o50ztXbfztWIGX6xF6ZMaZ49CCHApVGcIJ9XOdQIgMszLEPnSH3qAIi16QQnkyvCmqcofbZhDn1nDvKaBBwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR/UrCRA9TVsSAnZWagAAA0oP+gOzWVXahuYw7Nx0zjpO\n8p9nn1P8IPt3jUAaO4Tu050ILi1Kk27xSsPzPVPhXS/v3XVOxOt12/sj81WM\noPP/MJYe+0ZKyjkaBncmFI5uaQnC9+JwATQ4IvYRe83STU8TdcS7V8w8SoyC\nOWLt1a0qkHNYZeKbimT8zCJ9G1s3/bGnfw33AT/efJxI/rdM+rIzjcwbLVtu\ntMoxRFNw5UapOEaWAZugaw01wo4H/8d14xDv2EWGaDczkivzdCGU76YzU+fT\n8G6nGebzxpc38w1WUXq6xtqbjSWlgf9S3QKy+L69efgzxdUJjdJHtI30w3hl\nuTm5PsguZS20BWU4fWi8R+SENuerIZPAfTdMh61W9XoYuXFr9IXIBSdVBEyn\na3PCTBVtWTFb18Ta/ONdgmEJkwSEhcjSsvQnWemTpthMKncNgeC2k/ULOeqZ\nvjgNK3SUmf8k0clV1wB/qw6cbugEZylF8220mbjYsZp89xl8yBwsTKWnWflS\njc5+deZdl5mZQgB00bGKZFEIcb5/ND2EIbedScACfQEGOhfRPLlTpO6Ss8Bo\nGCmEm6Y0bRQ6qUEmWLbydgWdbAEXVNGZNsNFThd4kr/YGot/e2jv/3j0Ddq+\n8voaFjjEXiQDcJpRjBXy3dd9KwdBKbFsDLuGophwhxUpto1LMHwuBpfJBIit\nkHfq\r\n=pvZM\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "ab96e9d1c3b26453c7a64db1234144b0a9ffa964", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "_npmVersion": "6.13.4", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "12.16.0", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.9.6", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.4_1598551338814_0.9268684389459543", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "@babel/preset-modules", "version": "0.1.5", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/preset-modules#readme", "bugs": {"url": "https://github.com/babel/preset-modules/issues"}, "dist": {"shasum": "ef939d6e7f268827e1841638dc6ff95515e115d9", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.5.tgz", "fileCount": 19, "integrity": "sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==", "signatures": [{"sig": "MEYCIQCr9eOqrkun/GtQKulD6MOGbKYIPtzc6kS1K1wK+KiZewIhALhqY9A54ok5xgTOHR887Zjbk9Vc5KQDwx4KYtsq6tfT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHEBCRA9TVsSAnZWagAA6ZIQAJ5vIHoBPSXf69E+vB2K\n1pw1YNrN4B9+EU9FVcDAFeKS2rzV8bIi5FqW0zqpbPRL/L7oDC9qLBMb0xxm\nY1sF8c1WLIrPiOt5+ecWc20sU0tsJazVtbJ3EuD2D+SFK4JzMG4HhxCBpvNp\nn6G0rL3fsGyK31SCeZkw2QCterwTVLAQzoImwTC0Rz6IsKV9k2QHmEjD17j/\nBm2dV9yAoNl2zfarq9kFOWdgu1g4wIB4H8xgFolJRDY6q6CZ5w4RCT+1O7UW\nxvSoGvgt7hvrt7zJlTUMBvf1lxgIID1gVRo32u+xdWC4OHuX2lgUu74719Bo\njm6h/RyDAQhCFZq/qFeGNT9Xr9Usw6TNzQHlRU7FPmOQ+MHvNqPw1R2M6fDS\nAK+mViJNKh3UmCf57BW4qD+j8eAPdmU67hm+Vy7S/qWbeZFb1igym4zX15Zf\nl8jS7MDSITUsRF4leJW7h575CuHP1nCOCW3+LPbalX7gFD9atk7VQwrUVNxa\nyydxVtkdG2rSvsNlwYC2CSiPcswd2Eo9RWhi4qPNgKkYF4okzsZijQkeiuye\nIGHZZ0GkW9kJshG2sKmGS9VSaov6gLdyOVJwvh4qOgjy69WMzZqX3d/e/OKc\n5Yx4ejCLYN6gbjzVmzc6HNewwJ3xJHXlmmmaY8swWZrHxhSXBvi15sgv9z4t\nyXh5\r\n=kNgp\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "488d2199bab1fc2bf10421edc6660c86d30b2dd0", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/preset-modules.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "14.17.4", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.9.6", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.5_1634835953456_0.8821608824900036", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "@babel/preset-modules", "version": "0.1.6", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/preset-modules#readme", "bugs": {"url": "https://github.com/babel/preset-modules/issues"}, "dist": {"shasum": "31bcdd8f19538437339d17af00d177d854d9d458", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6.tgz", "fileCount": 19, "integrity": "sha512-ID2yj6K/4lKfhuU3+EX4UvNbIt7eACFbHmNUjzA+ep+B5971CknnA/9DEWKbRokfbbtblxxxXFJJrH47UEAMVg==", "signatures": [{"sig": "MEUCIAUsKNkp3opVQLUKe1X1dtK0fHTRS/mYn/GMZ8Aaem1OAiEAywlF3HGAt1OsmLBxN581+iC7fQGx4CtdX8lZGP9nFa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38828}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "11b7ae9c396bbd354c06a847487a451bb09a371e", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/preset-modules.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "20.4.0", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.9.6", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.6_1689930757049_0.9700719175959847", "host": "s3://npm-registry-packages"}}, "0.1.6-no-external-plugins": {"name": "@babel/preset-modules", "version": "0.1.6-no-external-plugins", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "license": "MIT", "_id": "@babel/preset-modules@0.1.6-no-external-plugins", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/preset-modules#readme", "bugs": {"url": "https://github.com/babel/preset-modules/issues"}, "dist": {"shasum": "ccb88a2c49c817236861fee7826080573b8a923a", "tarball": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz", "fileCount": 19, "integrity": "sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==", "signatures": [{"sig": "MEUCIQC56JxPsgZvKtgrrRrZc+EHtQ0T0//T0lWU5U5aNuGdtwIgKmvM7lqYUV2w6PTiWarJ7PPj+0S0cVFAv+IkTVqJWPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38576}, "jest": {"roots": ["src", "test"], "testEnvironment": "node"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "readme": "# `@babel/preset-modules`\n\n> ℹ️ Starting from `@babel/preset-env` 7.9.0, you can enable the [`bugfixes: true` option](https://babeljs.io/docs/en/babel-preset-env#bugfixes) to get the same behavior as using `@babel/preset-modules`, but with support for custom `targets`.\n> If you need to target browsers with native modules support (like this preset does), you can use `targets: { esmodules: true }`.\n\nA Babel preset that enables async/await, Tagged Templates, arrow functions, destructured and rest parameters, and more **in all modern browsers** ([88% of traffic](https://caniuse.com/#feat=es6-module)).\n\nIt works around bugs and inconsistencies in modern JavaScript engines by converting broken syntax to the _closest non-broken modern syntax_.  Use this in place of `@babel/preset-env`'s [target.esmodules](https://babeljs.io/docs/en/babel-preset-env#targetsesmodules) option for smaller bundle size and improved performance.\n\nThis preset is only useful for browsers. You can serve the output to modern browsers while still supporting older browsers using the [module/nomodule pattern](https://philipwalton.com/articles/deploying-es2015-code-in-production-today/):\n\n```html\n<!-- transpiled with preset-modules: -->\n<script type=\"module\" src=\"modern.js\"></script>\n<!-- transpiled with preset-env: -->\n<script nomodule src=\"legacy.js\"></script>\n```\n\n### Features Supported\n\n- JSX spread attributes are compiled to Object.assign() instead of a helper.\n- Default, destructured and optional parameters are all natively supported.\n- Tagged Templates are fully supported, patched for Safari 10+ and Edge 16+.\n- async/await is supported without being transpiled to generators.\n- Function name inference works as expected, including Arrow Functions.\n\n### Installation & Usage\n\nInstall the preset from [npm](https://www.npmjs.com/package/@babel/preset-modules):\n\n```sh\nnpm install @babel/preset-modules --save-dev\n```\n\nTo use the preset, add it to your [Babel Configuration](https://babeljs.io/docs/en/configuration):\n\n```js\n{\n  \"presets\": [\n    \"@babel/preset-modules\"\n  ]\n}\n```\n\nIf you're implementing the module/nomodule pattern, your configuration might look something like this:\n\n```js\n{\n  \"env\": {\n    \"modern\": {\n      \"presets\": [\n        \"@babel/preset-modules\"\n      ]\n    },\n    \"legacy\": {\n      \"presets\": [\n        \"@babel/preset-env\"\n      ]\n    }\n  }\n}\n```\n\n### Options\n\nThere's a single Boolean `loose` option, which defaults to `false`. Passing `true` further reduces output size.\n\nThe `loose` setting turns off a rarely-needed function name workaround for older versions of Edge. If you're not relying on `Function.prototype.name`, it's worth enabling loose mode.\n\n### How does it work?\n\nBabel’s `preset-env` is great, since it lets you define which Babel features are needed based on a browser support target. In order to make that plumbing work automatically, the preset has configuration that groups all of the new JavaScript syntax features into collections of related syntax transforms. These groups are fairly large, for example \"function arguments\" includes destructured, default and rest parameters. The groupings come from the fact that Babel’s transforms often rely on other transforms, so they can’t always be applied in isolation.\n\nFrom this grouping information, Babel enables or disables each group based on the browser support target you specify to preset-env’s [targets](https://babeljs.io/docs/en/babel-preset-env#targets) option. For modern output, the [targets.esmodules](https://babeljs.io/docs/en/babel-preset-env#targetsesmodules) option is effectively an alias for the set of browsers that support ES Modules: Edge 16+, Safari 10.1+, Firefox 60+ and Chrome 61+.\n\nHere's the problem: if any version of any browser in that list contains a bug triggered by modern syntax, the only solution we have is to enable the corresponding transform group that fixes that bug. This means that fundamentally, preset-env converts code to ES5 in order to get around syntax bugs in ES2017. Since that's the only solution at our disposal, eventually it becomes overused.\n\nFor example, all of the new syntax features relating to function parameters are grouped into the same Babel plugin (`@babel/plugin-transform-function-parameters`). That means because Edge 16 & 17 support ES Modules but have a bug related to parsing shorthand destructured parameters with default values within arrow functions, all functions get compiled from the new compact argument syntaxes down to ES5:\n\n```js\n// this breaks in Edge 16:\nconst foo = ({ a = 1 }) => {};\n\n// .. but this doesn't:\nfunction foo({ a = 1, b }, ...args) {}\n\n// ... and neither does this:\nconst foo = ({ a: a = 1 }) => {};\n```\n\nIn fact, there are 23 syntax improvements for function parameters in ES2017, and only one of them is broken in ES Modules-supporting browsers. It seems unfortunate to transpile all those great features down to ES5 just for one browser!\n\nThis plugin takes a different approach than we've historically taken with JavaScript: it transpiles the broken syntax to the closest _non-broken modern syntax_. In the above case, here's what is generated to fix all ES Modules-supporting browsers:\n\n**input:**\n\n```js\nconst foo = ({ a = 1 }, b = 2, ...args) => [a,b,args];\n```\n\n**output:**\n\n```js\nconst foo = ({ a: a = 1 }, b = 2, ...args) => [a,b,args];\n```\n\nThat output works in all ES Modules-supporting browsers, and is only **59 bytes** minified & gzipped.\n\n> Compare this to `@babel/preset-env`'s `targets.esmodules` output (**147 bytes** minified & gzipped):\n>\n> ```js\n>const foo = function foo(_ref, b) {\n>  let { a = 1 } = _ref;\n>\n>  if (b === void 0) { b = 2; }\n>\n>  for (\n>    var _len = arguments.length,\n>      args = new Array(_len > 2 ? _len - 2 : 0),\n>      _key = 2;  _key < _len; _key++\n>  ) {\n>    args[_key - 2] = arguments[_key];\n>  }\n>\n>  return [a, b, args];\n>};\n>````\n\nThe result is improved bundle size and performance, while supporting the same browsers.\n\n\n### Important: Minification\n\nThe output generated by this preset includes workarounds for Safari 10, however minifiers like Terser sometimes remove these workarounds. In order to avoid shipping broken code, it's important to tell Terser to preserve the workarounds, which can be done via the `safari10` option.\n\nIt's also generally the case that minifiers are configured to output ES5 by default, so you'll want to change the output syntax to ES2017.\n\nWith [Terser's Node API](https://github.com/terser/terser#minify-options):\n\n```js\nterser.minify({\n  ecma: 2017,\n  safari10: true\n})\n```\n\nWith [Terser CLI](https://npm.im/terser):\n\n```sh\nterser --ecma 2017 --safari10 ...\n```\n\nWith [terser-webpack-plugin](https://webpack.js.org/plugins/terser-webpack-plugin/):\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        terserOptions: {\n          ecma: 2017,\n          safari10: true\n        }\n      })\n    ]\n  }\n};\n```\n\nAll of the above configurations also apply to [uglify-es](https://github.com/mishoo/UglifyJS2/tree/harmony).\nUglifyJS (2.x and prior) does not support modern JavaScript, so it cannot be used in conjunction with this preset.\n", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "4485d32090cb31d280491c43ed9297ad7479d68c", "scripts": {"test": "eslint src test && jest --colors", "build": "babel src -d lib --ignore '**/*.test.js'", "start": "concurrently -r 'npm:watch:* -s'", "test:edge": "npm run test:local -- --browsers sauce-edge-16", "test:local": "cd test/browser && karmatic --no-coverage '**/*.js'", "watch:test": "jest --watch", "test:safari": "npm run test:local -- --browsers sauce-safari-10", "watch:build": "npm run -s build -- -w", "test:browser": "cd test/browser && karmatic --no-coverage --browsers chrome:headless,sauce-chrome-61,sauce-firefox-60,sauce-safari-10,sauce-safari-11,sauce-edge-16,sauce-edge-17 '**/*.js'"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/preset-modules.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "directories": {}, "lint-staged": {"*.js": ["eslint --format=codeframe"]}, "_nodeVersion": "20.4.0", "dependencies": {"esutils": "^2.0.2", "@babel/types": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "eslintConfig": {"rules": {"new-cap": 0, "no-console": 0}, "extends": "developit"}, "eslintIgnore": ["test/fixtures/**/*", "test/integration/**/*"], "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "chalk": "^2.4.2", "eslint": "^6.6.0", "if-env": "^1.0.4", "rollup": "^1.16.3", "terser": "^4.0.2", "webpack": "^4.35.0", "karmatic": "^1.4.0", "prettier": "^1.19.1", "acorn-jsx": "^5.0.1", "gzip-size": "^5.1.1", "@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "babel-eslint": "^10.0.3", "concurrently": "^4.1.0", "pretty-bytes": "^5.2.0", "@babel/preset-env": "^7.9.6", "eslint-config-babel": "^9.0.0", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "@babel/helper-fixtures": "^7.6.3", "eslint-plugin-flowtype": "3", "eslint-plugin-prettier": "^3.1.1", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.2", "@babel/helper-plugin-test-runner": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-modules_0.1.6-no-external-plugins_1689960984654_0.74528123379091", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2019-11-08T16:17:56.553Z", "modified": "2025-02-27T16:08:01.052Z", "0.0.1": "2019-11-08T16:17:56.773Z", "0.1.0": "2019-11-12T10:30:21.233Z", "0.1.1": "2019-11-19T20:20:29.918Z", "0.1.2": "2019-12-15T01:46:15.871Z", "0.1.3": "2020-02-10T18:49:12.253Z", "0.1.4": "2020-08-27T18:02:18.944Z", "0.1.5": "2021-10-21T17:05:53.643Z", "0.1.6": "2023-07-21T09:12:37.279Z", "0.1.6-no-external-plugins": "2023-07-21T17:36:24.876Z"}, "bugs": {"url": "https://github.com/babel/preset-modules/issues"}, "license": "MIT", "homepage": "https://github.com/babel/preset-modules#readme", "keywords": ["babel", "preset", "preset-env", "modern", "modules", "ES Modules", "module/nomodule"], "repository": {"url": "git+https://github.com/babel/preset-modules.git", "type": "git"}, "description": "A Babel preset that targets modern browsers by fixing engine bugs.", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "readme": "", "readmeFilename": ""}