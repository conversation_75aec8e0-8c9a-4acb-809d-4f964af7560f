{"_id": "@babel/plugin-syntax-unicode-sets-regex", "_rev": "8-046cd030c99be7e507527f83f3ba4d65", "name": "@babel/plugin-syntax-unicode-sets-regex", "dist-tags": {"latest": "7.18.6", "esm": "7.21.4-esm.4"}, "versions": {"7.17.0": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.17.0", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.17.0", "@babel/helper-plugin-utils": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "_id": "@babel/plugin-syntax-unicode-sets-regex@7.17.0", "dist": {"shasum": "1b5391d79c914ebce61ba9c5cad0eb8ce541edea", "integrity": "sha512-uQn2x/KBRAayuLw6RecjqgvzA44f62VfHpzh9qYj1A9Sm7f8bkhuiZROs/ZPvCtDg5V+Ba5AlTWT/UjH4K/dUQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.17.0.tgz", "fileCount": 4, "unpackedSize": 3374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4ZCRA9TVsSAnZWagAAvDUP+gMARTXusA48r02bEXq7\nDbFdILN1CBrs0Zk3C8/rYJWfguc0Od78FQ59Z7ch5F6H+6haNg7mN20EQ1Yb\ntoayqBFEDKtTnNeelrP0f2tUeMa+WcdguRaah2WUehWmPg9909tk9ChJWXvk\nHtL1Vhipv62x5vVgJNYLQb35fzJs106BiB3uSmkEGdO0nEF2SDaSvGqk1mGP\nBhxRl6sXqyAJg09ZjekelTC/TfiBjNU6EATxU26gQCAEdetkBIlzGfIHwWXH\nm4bIeo8K47EMgTTuOKq0BUsXHiQWOyygPy0FgdTbHD6UmKTYOT9C98tIt2iq\nlr5lbtVyXRD4Cw5zaFRbBj03XAP6Degx1bPoXS1kMx2WDb1yIcqyeaWlEguP\nPcMLKi+mBCzHFMNA5igmi2K/AKHo1KP5cK60nzwb33XDTFYjI0TQZWyTNBMG\nK4ema7HWztuWQtn03NK1UM3bHBiljJjAdttaXL+TzouPl1VuigJYVvSLuw1E\nJBv3gWzDWcpXUhTF3R9rUGqlaR3QLkmLSVb0OL3zGG4WoWGP5cFfyVDSuKZw\nGedwLW6S35eRWZDYzT5eBww9DnNroO3P29c65M3VnyUBajSzxTNCQg/vtjDD\nopTgTCaTfYbtEvBYkbDcOfLQ6yxZsWJzw2/2aKgFezf74oxruJQDfcXGZ1pE\np3Fx\r\n=Apuu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDytqDJgraZvJW4myLR+kGu8UcoSrWwwsVUZjZ+MO6wgwIhAKS7xwuiJAm3DfoZkT9+AeiqMC9xUGqMYW7NlVjibNZ/"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.17.0_1643843096872_0.4241733545531752"}, "_hasShrinkwrap": false}, "7.18.6": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.18.6", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.18.6", "dist": {"shasum": "d49a3b3e6b52e5be6740022317580234a6a47357", "integrity": "sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "fileCount": 4, "unpackedSize": 3396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXFl7iqRSq8LSmmOTxpSKQyyOTQkRBdvZ6L0t/NnryQgIgI3PAVkGMhbf3Kkjw+BiWqfH/Oje4a50sjHVPHd79mfQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP+A//XO8ifBMVqeaKtjrKSCXgChC+RSOfBy2W8Dwdxq/zCmXXzdZC\r\nfugAkjz0dlN4HzrqXoqjZq3tbED8vaR7IP3PdLG4Z/jdwthr9jgO05tNEsMQ\r\ng1ZJRbfNhN6RI0wQKZn33zfEoQevAIWiIvUEhcTTuna5WCt11IEk7upofGiw\r\n7YaMx+EgxDkgNosSoQQiZSd5qWvMrBRmIM6ORXXMvfpRgN0kEvx1djduX4BY\r\nB53gvJzM0PBV0O6yppieEUzy9meGlFPy7l3Sp3iYHqaReb73KmReNAaSeD4o\r\ndfSRGKGj8Hb/qd4OG0Nw3KKQNulnDVVuTw5DylvenNR2PEi8nm06WBPKeOPp\r\naxofDjwZ72+ZuQOAZEYQCsB0bqZcj18JVZHmpmikWzYTNL12jajdjejBdQF6\r\n3sn02GCshcJvWCqFCsV/5KJXsHBm6R/34we+FntRDba6oWQY2cTCoYTSp/Kn\r\nU3BqTUxkhQ6KGjcinKJqnRwcjp9Ow2siK+dg2bLznSB+vsbWDp/GMqIM3C7L\r\nLHohhSoVwXb+gfFQaVuYU4OruO7vBhk8uBLufoFJx8LOUAbPaV0hJYVLV9UR\r\nJqguv8e2yuoga5IMftkQClO2EyKcDyFeuz8/hZLafyJKLKFrInHm7EM+sfIo\r\nC+kfUMJdtW7/0niGDvmKf1ZOKU3LJceNJQU=\r\n=WI9z\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.18.6_1656359425779_0.8264780714868813"}, "_hasShrinkwrap": false}, "7.21.4-esm": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.21.4-esm", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.21.4-esm", "@babel/helper-plugin-utils": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.21.4-esm", "dist": {"shasum": "87cee03c11f9b2e7ce904bed5851a969afdeaf72", "integrity": "sha512-GkWzjlScTM6yG7O4WINydI4xT5OM+rouhywmxwWMLm5uWoM39TK5MoFll8x2tbsODs90+GytR250OKD4q4Li1Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.21.4-esm.tgz", "fileCount": 6, "unpackedSize": 4694, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBuGZR4dzdqvFt20d+9kSDUmArMIOxVBeD7ghEHwC86wIgc4squ/2mdo5PazUI2NL02KED5/jrzApvr0bl2QZkGXk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhTA/5AH6yK6UJWuEOWN5Lh+vHXzUHKK38qqFX8PpBCyFvAGi6+uJr\r\nU0FY2Bg4UBKZBtCMB3mDw6wJpLAf9H/9nrMICGMLhZn9Fn8i0wAhkzDXWYcG\r\nSREBykwTQsLe/0r9RyShzmSTNNeE9T8HmCY5s7r5J03kbed7qNk6agmdoXI7\r\njRRMJvrY7uvaQr1XqutLagftGAOq4UiJeYtzkSwn7r507f9zxrsDEqZrTdwk\r\nMDO8FYktEvJG9sSAnb3MAapsmPOzMGDMdGB9lBz36botjY+rmP9E0Qbbk8ym\r\n982R9u+LqX4Sed1azCJZIyTJO8Qg4YNJgnWNNOAIFDRmJEccUPeDi2X5G/pP\r\nGChndUbuXrJkt5L4qnNaaueqvaGdoDcopiU0CJSeAnufkrnc1slI09zQNnmG\r\nJTKwu8hcSsOa1c9rHjpRWlJCD4WsR+Z7mXiJBCYwAa4MMRF15KUPY8x57UR3\r\nnoHqh7KKIl+SDzOEpcdE5SrCRBQmdyGyNdtOyTBbB5quIEYgwebFCWCtS7hS\r\nFM+fvSkI5/lzXJ7Yz9LDmhgx0DF9Y4ENMkPAKG9KO///acSraEmps73AxAfb\r\nLstKEfZ5gjjBWFrpmjZIyX0MinZQEowdI9I5D62ob1iiAkzPZRSICeArzdfW\r\nKEUJAIJURybpU5QWBN+H+q41vedlyVF1rgE=\r\n=Nf9j\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.21.4-esm_1680617388715_0.42021442077341664"}, "_hasShrinkwrap": false}, "7.21.4-esm.1": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.21.4-esm.1", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.21.4-esm.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "type": "module", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.21.4-esm.1", "dist": {"shasum": "ea7a6a6c52226a5e569b807f0729a95a53c9694f", "integrity": "sha512-WwIzYl+TJaM8ccGNCT4ZRTMMB2dj/KT0WyVKs4cWPR9mUpuU7nZMzx1fd+OlAcHmNYLJaNOlMU82PcRhYdLRYQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.21.4-esm.1.tgz", "fileCount": 6, "unpackedSize": 4333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCg87zRQOXl+USJgwh6VVmBquGsQSpCpGXvXEIEvKThhQIgSW8mZGr0wSl6vGJAsL8XyF/H7Gy6e++OSvlSv7IQQxk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/KQ//XcgmeAkcPtuoSkMNbHAHGMOwvTZvS3JZbDhfBR6epQuW3cA0\r\nUmF9MiQgzu0UXFLeGz/pvioE3XCEuK2XYPFQ25UGpHizk2QBypdQxCBrMw1C\r\nqAWHCtvkgIVWHma0R6uRqCDt3XG7cWdqFVUV//7DrGS1Hbtm3sUhSMlFY9By\r\n2lfmDluXJkeKpz4wUp0QkSpYlWrA7e3siRg61bJJxrinoFfQeZIXeB1ObzCS\r\nbQ2iije068o+66DFfWhbHiFdExKTxztLNlntrnV/TbmCZhfVA2OtnnasT418\r\n5Kdn1IAtAvKRGD2onCuLbISvYK5PLfGiFeHIEm9DoAbdl/B4kZb6Va6kFZ8v\r\n666RcKh2r3K0yvVCzKe6U7c9FLe2RimqoWX2wvG6vf43XUGv31nFGrw1QQnl\r\nJW8O+ZRC+gOzzoC9lPmOYc6WSzg+GWX9L4nZJGhqLPud8hqTKBYp1RmWv1rS\r\nCLsSWLZGtkZefiv+z2tvK6bYHj6Vje6631pKNbMWmhdIoJpqaGJGiXAj43pY\r\nFJz0e3y4wJncYtybpn/19hgawRycUGtAGAL9YNtVdXZRhjisntzhuGeB1iMF\r\nTxmDP3U6Wpk6dXbKpim5uUAq++JXoSVyv+xMpaOtj5KQm/Uz9RBycx6aLnH6\r\nPPzwaDXEfsFoC9Ibly/JeLYAqIng9hOxNDg=\r\n=Cr0w\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.21.4-esm.1_1680618106207_0.20175626334234975"}, "_hasShrinkwrap": false}, "7.21.4-esm.2": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.21.4-esm.2", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "7.21.4-esm.2", "@babel/helper-plugin-utils": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "type": "module", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.21.4-esm.2", "dist": {"shasum": "4e8c1a855540b01bd1b303f0156ea20d37fdd131", "integrity": "sha512-r+Zz4cZ2ZAGSXNE4R3zdGglzdpikCSs3yOPy4RRLXsGgqgjHZgMKCr0oA5rYwFUopdbWeMGu+81zPUV/A6QHpA==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.21.4-esm.2.tgz", "fileCount": 5, "unpackedSize": 4310, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjQstHcqZGI80t2dMg6+nwf+HsmBGFplRvopUG3lPXPAiBHAbEL0YBhd2iYH3Q4ZJXxWiw6iMR/6tE6929xe0AQnQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDavACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEUA//U+w5UBEHDV6OZizVELJ6Bh5Cfwb06B3tOcOXXWYfyFCnn2rL\r\noB70hF0A19I844csZRksJhOBWqOYS7wwgEjTgRRVjbWjY7zq3mbH7kT2zNMG\r\nDZoehx9mrmYoF0pJI75+8bRiXdL7ugBeFVgtpNjKvJZz8CB/pvsami6czNP8\r\nn/W/S5KPLFiNsstTZA6EvuNIHBoEVDwWqapnpUro66jDX3JGHgy/WLbHU9uh\r\nw3NUuC8Zjo/pw+TKPiz4NZxOmyIGfz3ajuTKkIqRlGZpd+UVmJ88TsocgrEa\r\nXrVWQduQhIxB3O0+G9Y1gJL0vtcL4HcdwK44PjVzHwu6u2hRYbi9vHwEeic2\r\n8GCBIcxykVgJyHryZnYYHnk5vEw6dn+l4I5DithxAa/DhCcISrcOqpdLG0ta\r\ng3L7Igss1rFWe1X9Z3w8rZRyA+q5nbqdEj9mYL+DQLMg+Y65G3cCsH4I+ILI\r\nTFPuhDnZgeh0xQ4EnFOgNFdceVt3JgxiLNvYjEp8z+cn/hb9NOTqtqSSomLB\r\nAkiowpKTbKoCMija4iVs3N7BUHmp6/d3DxSRoni3xvbDHBjG4rLrVakkEQjz\r\nRA0/3H78Y/MZIgrMOHamqRvgqL/FppyjTM8DszyiZvhwITJ0bTpZnacg5PoI\r\nSdBJ2w1Xe6+zp7572VkpTj4ZhtlIMZGY0tA=\r\n=lAKb\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.21.4-esm.2_1680619183478_0.9982928403036138"}, "_hasShrinkwrap": false}, "7.21.4-esm.3": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.21.4-esm.3", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "7.21.4-esm.3", "@babel/helper-plugin-utils": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "type": "module", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.21.4-esm.3", "dist": {"shasum": "00334fef9d5cf31637eb396471ce740bf42702ae", "integrity": "sha512-49EyjLpIZZz53wUYGzuWSHZtbBbGxejhhN7mLWlJu7HM799wHiZs2fS8MJmdTTdYp1xPgx6YE03chkRrwjMBYw==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.21.4-esm.3.tgz", "fileCount": 5, "unpackedSize": 4681, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9xXJT4NUvb+87xjlQUKDYx8eshnzOAxJ6b5Uuj7HUmgIgeDd1nCxDV0JFU8WVdMrw3VeBFzKYtvWk6quOSvQe18o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUxA/9FnPlkWCBOIeYhVyDMPAsERqhnrbJ3xOtTdd4+MLAgfG7anGl\r\nc12LgQjNW98/JHGfJRps/Evb/ayfxQIeGb1WfdBudVkfPUIhrUZYF38qRZVj\r\nLwHWplFAoSbWSpEbk9vN6qqoaMxWsl6qb/Q4dI3Uvo+9AUJ+Ix0ScKeklQnv\r\nHvrGYTWm6Ez7k2BHCyCoxk3nfaLWGOQee/j1gQ2O85HZ70aX9v2KJqm9LqOZ\r\npp01/DBolTdkK9/V/6svL4BPObo4b0JvHED5DXcHr5at1px4HvX55HJyA2nJ\r\nlYDWwK3pnXy05g+dZpGt1b9tn+3gLupEvfXuYD8hdN/lD/kdSXmTPJOkhIF+\r\ne/u47g/jV38YBL73KW1+X2LX5uLCciRtheu4qYjUUBNPlPLuR7umDP6NehOd\r\nwDgOH8GbZjnfbnXlFDWkZ/gmisdaiv/rg78wo2pqoltUQwUP1p2bBzdL938n\r\n3JvtlrdQVPiFQOdCf+DPe0QizLMCO+s/UaHP+/k2GSExJI5DvkWPC8BNkmM8\r\nKMXljESN6Jh0KQjMYF/6B3eCCpYEEM/FZsW4brU7fdtqp8EfAQWqyUIT4wk3\r\n6YQ0wx8kvGkZW6M+Ch6Bpd8DMftFjlQi8zA4oDP9QVkN41octiFUUzqMzHBv\r\nZscRxMNtUnXfe16rZHem0e1b2JCQqZ0HUfQ=\r\n=fE/W\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.21.4-esm.3_1680620190950_0.5622881046942729"}, "_hasShrinkwrap": false}, "7.21.4-esm.4": {"name": "@babel/plugin-syntax-unicode-sets-regex", "version": "7.21.4-esm.4", "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "7.21.4-esm.4", "@babel/helper-plugin-utils": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "type": "module", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.21.4-esm.4", "dist": {"shasum": "976d7ab487efae5345d1cfddb8f7314731be141a", "integrity": "sha512-XHRDYZYod7sB1iYXVfLGJpJFMZ8Ov4hr8F+HBpCYtGj/M2TYwtSWT6G9OnDHfna0G43RQhJoDuqm13r8Zvjx7g==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.21.4-esm.4.tgz", "fileCount": 6, "unpackedSize": 4330, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzNEQpXKbybAUCjwnnFUj2PiAEBUvJwa5vKSdTlEm1WgIgeHZMUuH6KTBGkW9Uh5Lt0OyhZGJoFrZQzb87tocSoVQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZfw//XHbHNUspFedX8C+iU/vuFguyEeO9US9pvmHXQWlaFw6Cfe83\r\n3wA9+qc2Gneu4W2Ax3hFljCYPa0wvqFu14BweVW7vy5XiftS7SuS+ypg6HwN\r\ncPkgE3Qn/1x8vSmvanm6wjpYxHSqCVhoYt8M8pYWVKNQFgt91dZXf9LsY7Nx\r\nbqpA8dOoJ0Z0RW2NfBKtDXntZAf/VZzeaMNF8V+Hy2AMxIIlAjCgKihxOGok\r\nksywhUbHI1d03FwPNroVE0UmDkRw+9NM/XFggWzVQJZ+Xv5RYviuOszBleWp\r\nYJzJQTwCcXbrI0hHlXGf83enf8gQ4Zs+zOGdCLQLkV/y6u5Wejh8gdFQWSh4\r\nIW+q1Qfwmfr6ZhSnabk5F/kPEUULlaLbKtGltAsdgEvTIx4R814uTYOTHjGd\r\n6H3k8CHtk/QMIw3FjUqXth847VSlcWUQuF3dEgGaq0fkJho76/J3VW3edcCv\r\n63zg8K14OdcC3VV04JgfX76jgR9FTi9orsgyQ8nvxMe93mYMT0dDOrqVTGrN\r\nIcO8+jiLosqt7CRCvcR2idK9Ab/n2NxuXObGs4gkCrE9D6EL9z/ALlgkHFnO\r\nM05SjmXIomjrwqCakVqhg9ykIYKh81qsS+/cqyBEutau7sHM7BhgIP1wMRn9\r\nG+n9rarZjDsnKzwCV8eVZWNR3+cZlviWaCk=\r\n=Mh9O\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-unicode-sets-regex_7.21.4-esm.4_1680621221776_0.30272501281988196"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-02-02T23:04:56.812Z", "7.17.0": "2022-02-02T23:04:57.057Z", "modified": "2023-04-04T15:13:42.192Z", "7.18.6": "2022-06-27T19:50:25.901Z", "7.21.4-esm": "2023-04-04T14:09:48.899Z", "7.21.4-esm.1": "2023-04-04T14:21:46.354Z", "7.21.4-esm.2": "2023-04-04T14:39:43.854Z", "7.21.4-esm.3": "2023-04-04T14:56:31.107Z", "7.21.4-esm.4": "2023-04-04T15:13:42.001Z"}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "description": "Parse regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "author": "The Babel Team (https://babel.dev/team)", "bugs": "https://github.com/babel/babel/issues", "license": "MIT", "readme": "", "readmeFilename": ""}