{"_id": "is-core-module", "_rev": "34-c2ed5fe03d74862955865c40f431184c", "name": "is-core-module", "dist-tags": {"latest": "2.16.1"}, "versions": {"1.0.0": {"name": "is-core-module", "version": "1.0.0", "keywords": ["core", "modules", "npm", "dependencies"], "author": {"name": "meandave"}, "license": "MIT", "_id": "is-core-module@1.0.0", "maintainers": [{"name": "meandave", "email": "<EMAIL>"}], "homepage": "https://github.com/meandavejustice/is-core-module", "bugs": {"url": "https://github.com/meandavejustice/is-core-module/issues"}, "dist": {"shasum": "32b0c727ff0819937576d799e479e548c7a5af7a", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-1.0.0.tgz", "integrity": "sha512-mSrwntyCQERmUodWLqXUCSrspXYgTdyT+I0L4dVoYAElZmbAHV4AnDLei+LqudAEYcUM+5IH+u+Ii/tRqNoC0Q==", "signatures": [{"sig": "MEUCIAPu9DS2usuiYeWq2tKTzst/JgTSORl/oCL0xD/JpwDgAiEAvD4NsE2s7sr0Wm5MQYx+sqh17ya1MnqIWvEdBnKv/r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "32b0c727ff0819937576d799e479e548c7a5af7a", "gitHead": "1eff0ec69798d1ec65771552d1562911e90a8027", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "meandave", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/meandavejustice/is-core-module.git", "type": "git"}, "_npmVersion": "1.4.25", "description": "check if packageName is a core module", "directories": {}}, "1.0.1": {"name": "is-core-module", "version": "1.0.1", "keywords": ["core", "modules", "npm", "dependencies"], "author": {"name": "meandave"}, "license": "MIT", "_id": "is-core-module@1.0.1", "maintainers": [{"name": "meandave", "email": "<EMAIL>"}], "homepage": "https://github.com/meandavejustice/is-core-module", "bugs": {"url": "https://github.com/meandavejustice/is-core-module/issues"}, "dist": {"shasum": "9e3d4c3e8ff2e091f50be0aa27548790a75d84c4", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-1.0.1.tgz", "integrity": "sha512-NB4efq3BafJeiZLUHVwjFAgJQgbmOMsitj+vjMo7GwM9iTuaeZDNVq0pnLaACageG+3WBZk8I6H/hZ04SwgTvQ==", "signatures": [{"sig": "MEQCIHCt5wb1urqx+olpE4VOBUUsQC5iDUchHiHdh48NmSOmAiBo42rtLmmdhjMDn/lR1/AJ2DOYO9qNAJj0WP3WZxgTiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9e3d4c3e8ff2e091f50be0aa27548790a75d84c4", "gitHead": "f21f906f882c2bd656a5fc5ed6fbe48ddaffb2ac", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "meandave", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/meandavejustice/is-core-module.git", "type": "git"}, "_npmVersion": "1.4.25", "description": "check if packageName is a core module", "directories": {}}, "1.0.2": {"name": "is-core-module", "version": "1.0.2", "keywords": ["core", "modules", "npm", "dependencies"], "author": {"name": "meandave"}, "license": "MIT", "_id": "is-core-module@1.0.2", "maintainers": [{"name": "meandave", "email": "<EMAIL>"}], "homepage": "https://github.com/meandavejustice/is-core-module", "bugs": {"url": "https://github.com/meandavejustice/is-core-module/issues"}, "dist": {"shasum": "132b3156a14eab498207717bc2facb68aa4da772", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-1.0.2.tgz", "integrity": "sha512-sWGgrRN44OhCJ2jUlL6T4bvSv1knx0yNNNVpJ21sor9JBRlqAADZvsS2OjsLa4pQe7KHmaOZ/i1loysbOxtgvw==", "signatures": [{"sig": "MEQCIBBhP9PYh8V8zVYe/e1rQPYngH04sj6nxmOEcun/SHmfAiAOK5Zqr4F5pbHUwS7dimV28DuFzxjBK/49i4Kl0uOFeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "132b3156a14eab498207717bc2facb68aa4da772", "gitHead": "66fe90f9771581b9adc0c3900baa52c21b5baea2", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "meandave", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/meandavejustice/is-core-module.git", "type": "git"}, "_npmVersion": "1.4.25", "description": "check if packageName is a core module", "directories": {}}, "2.0.0": {"name": "is-core-module", "version": "2.0.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "58531b70aed1db7c0e8d4eb1a0a2d1ddd64bd12d", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.0.0.tgz", "fileCount": 12, "integrity": "sha512-jq1AH6C8MuteOoBPwkxHafmByhL9j5q4OaPGdbuD+ZtQJVzH+i6E3BJDQcBA09k57i2Hh2yQbEG8yObZ0jdlWw==", "signatures": [{"sig": "MEQCIBKnM8m2Ju6/W9Z1tBbsjOVwHHALUKjXdR+q1vjBgNg8AiABonBAR2S8zbhmbo05zxKcLn2Mlt5xFH1xnkRKZkpR1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfc3gqCRA9TVsSAnZWagAAbXoP+wcsbNs4mEHfLwx8VTXE\n2ccVknOWvB9F+phRUjdc6NBbYHeniOqHJZefsK+P0jBagwAG2BRPsa8FcLAC\nJ+7ToQbAX2/iipHIoIgJcbBuUPvx1Pg6FoIHBHefvV1ojCWeDaIs/3ky1xqL\nrtKxHcuJP5li9nspuodw2nW6cSUy9qi+4N0TwYNEJ57EhFdU56UCLnT6u+FO\nwf8D0vMbYZjROzprUC6K93mBnPePUE4eVHvYVr3LcOQew/QcadcalfO//6q6\nM7ljdSIuHhdeowK/lUjGyoLT8E9UXQ2haztJu0laea7xLG5PQLtDOLoyentm\n/52zkk6wpReabpUq38qdqDGEGRsSWmmSZUkQSRy7h1q1aIsQEo6pHZub0KLN\n47yIYJgxc1n3BP8tbzhcvDgt7F3twrFw3TsumEwAtEq36vtyzNPC6N2v2g+C\n1tmxxSIVPZeXl4AxXJsuCMUMn4l9/L2SCZpjEjJORubeCa5bo020Z2nZHUJ7\noILOHOEg7ZNaGkutKV8vbP/GoYKivht87CZbTfmrP+XNnyjvk3EUM1nUQlGT\nHdoz8cZGBLCp4r4J4AFlIEFdmkZ1Fp38+R9WbnQiz0xb4imPWQwQXxqpwiH9\nT2M3odC+wmATEw22SKYuWjYthORQ5voRrXRRXHXsjCBl4oqYlANcNL3/FiAw\nc2e8\r\n=Ha4U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0888a1197cf90f9a06b8b0de6c5161dfb4e817c4", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.2", "tape": "^5.0.1", "eslint": "^7.10.0", "auto-changelog": "^2.2.1", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.0.0_1601402921585_0.818995529661108", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "is-core-module", "version": "2.1.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "a4cc031d9b1aca63eecbd18a650e13cb4eeab946", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.1.0.tgz", "fileCount": 12, "integrity": "sha512-YcV7BgVMRFRua2FqQzKtTDMz8iCuLEyGKjr70q8Zm1yy2qKcurbFEd79PAdHV77oL3NrAaOVQIbMmiHQCHB7ZA==", "signatures": [{"sig": "MEUCIBl5SNsnCE+e1cZtVvIG18r9F802JmWqTcXTgxwRzPZzAiEA9h+81Fjl4xiygz8WpmXmxcDwvMCCqsGNu7GNB86+5tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfowuoCRA9TVsSAnZWagAAMtcP/0TZ2zJpVcL6u9Rt3Vgs\nBEt1BGqMf4vDQHdhu7bDtPcy92Hp+Wb2c5WDDlJzS1v7T1++qZ0ARIyLtSL0\nTlHitO3yh2qcl0KTDtUZYxV7s1EYjY/R7J1tF6d12bqjISm/h76L9KYlpHSw\nbE9GyzHV/0iCrZX18kk6bkuDOTW95vg5GcXgA/+q2oJh6p3zyuFnwsx1iiXF\nZSY+UiJE22BeSNJEKxNfP8MC8pOenGpMlXud7KDXR42yZciM/yV2sUPh/TdO\nC/UT3DUtVMT4SHUyeDepEdXRRPFrDUgfjxv4t7sbXA9W8hoRaCCOwetsyAAP\nHZG3hQmUvbt9lVQxapl4hxAst+V3RK0dWwOcoTABTWui8GfZgBxQIWeyis3b\nBOmWQ17aCBfjVoAcmP0PP5tsPM6z47qPQnciKQSD2HpCmmkuXyIDKVJIRZCM\n2cecLEdS/YWOTDHIqmHi9wjdA8QVwrJ2HpMrliJwN/BeQsQWa+BuiZqh6O+v\nMc+ZnqgW6fS3aQXEdfvSqLGzd2bADWpx3DSbnBuE97CF1vjHl31N3H7UCohc\njPSFoqLbctKpJyhpLIkHicvP5s6UrG4MFym581F93MDxwrspdZfiehG2Ozqv\nNNbk/34Xi0uR6d9GmBEBy9lnHTcQvkLwgyqGwSruskWq7m28LfCn3auOLsyD\nQjGZ\r\n=v56V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7a97e5660f274c2e98339b6a2ba3baa74bedfcfc", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.2", "tape": "^5.0.1", "eslint": "^7.12.1", "auto-changelog": "^2.2.1", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.1.0_1604520871618_0.****************", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "is-core-module", "version": "2.2.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.2.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "97037ef3d52224d85163f5597b2b63d9afed981a", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.2.0.tgz", "fileCount": 17, "integrity": "sha512-XRAfAdyyY5F5cOXn7hYQDqh2Xmii+DEfIcQGxK/uNwMHhIkPWO0g8msXcbzLe+MpGoR951MlqM/2iIlU4vKDdQ==", "signatures": [{"sig": "MEYCIQDQ23P++B1EXDS+91XOrAoTnFSRKCGabkAODxc5NSbMtQIhAINkx6ZbF0zv8K+k1SkxLrZ6GvI8MvWiqscCivbhzUxF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfv+xgCRA9TVsSAnZWagAAz00P/0mX1C0etJG4VjFkU5el\ngGQiKmzKft5ofcW6rOpTPRXkDXL941T9oI8sAuDAMBCw9AA76tqaukBMdaEO\ni3DY9+nD8x+OVMxiCD52dtGHQHDTY4zOt66a+GSt/df7FX9GODnDnkwV8+Di\nbgdBzn5VnevLxN3j+yISOO06nrcCXZaQ2vhnYduKSy4WUAbjDElkb23QL4Wc\nddXImyEB2OaiCs7hiJX59RB37pMbxISB7OonIY6vI7qcbbq6zINdLbUpIS+Z\nTHoUTYKvuZWs9tZe99CzsHv/o7w+g4emehSackOt8d38scVEBWl9wM7ORzG7\nXcH+t5/ulJlZIlakGqm+5EnS5vwmbiswAyeCENt4Xtv/hRbGjp6W9lpGXNoX\nrmP6FEomQM0obp6JErMMx/3AjcjZcKR+qmBf6dCDppch7Nvtz9gCl3WUeLML\nZg9Ur3f+L20+goBjDp2eGrpIaVwXDPPbaKt+QFWv49CmvP0f8Lm6b8LBf8cI\nDUARrCRarIjPjee8N1f274uialjY5ovsNFyRQa0C98Dd2qEJiKs2T8UJgRT8\nuhuFgsydwk0da3CFlHVXkoeMD+ENOXsVoyqQt53qxqnWhCUAmWEIMYKd4gL0\nrZjPodNA932i9+f1w/BC04fJx5lWOdpIxBMhUwVHnYSsqDh4H2nNGbWI22pE\nbvAu\r\n=3z7a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "51aca38cdf05e8d6264e28dc7af7a483270e18ae", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "safe-publish-latest", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.14.0", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.2.0_1606413407944_0.10093124372925821", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "is-core-module", "version": "2.3.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.3.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "d341652e3408bca69c4671b79a0954a3d349f887", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.3.0.tgz", "fileCount": 10, "integrity": "sha512-xSphU2KG9867tsYdLD4RWQ1VqdFl4HTO9Thf3I/3dLEfr0dbPTWKsuCKrgqMljg4nPE+Gq0VCnzT3gr0CyBmsw==", "signatures": [{"sig": "MEUCIAheaTLK7vkn5Xa+uOCdRuAvwyIaut7oN67voPKnxGV4AiEA2jVyo0rnF8TMWuY/mGqhDfGUn6yaLeQC0ZDjkvMEWRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghIKNCRA9TVsSAnZWagAAErgP/0+SVeevj9uN9pL9tH1N\nRT6pC4bAqlZqot3bQXPcB8kjKSayjHAL94qP5O6bwKQVV7WP6HlPi/HSbkVD\nZnSws4ZeeEc2EMPMUflsomMZr+TIHnDfQcryV96DAyOaGYTb6tkeMsr3EkLg\nylibgfj2QtNeSXpZ8ypk576JeT3s0OFBVZCdoe6OuuZIBc5XjWS1kQ/Qinv9\nUFgydcAsroxsq9pyf+0s8OoPEbL7Kgkh+lHr+ZyE7Qm+2kx/wV4K0aPBApy1\nnPEgel/yZMt9VnL0hY10KSP7DDKyBMuZMVsO40mU3RIjtvbzCxq64xn8JAkJ\n8L7FLSTHQAEvV76OSnz7XJUDEBHROQsRO/pnm22tewbp2q3YnHO9U5prfiL0\nMbeOMD45TVSkAs+2c+8IMPYs6dugBSpr1BmvYKqXSqGdzNcbjES+WvIAYz7K\nYKREQJf02LUHrMxiRSmgYG9rb5f0m2+vP9whv3lpjLzLiF6Y1JVHmkocZldg\nKPTkGqJtUg+bezJQZSf5Z5SEjTQklDu6av1M0ty7jQAZHBkgbRtWadco8zAF\nd7nMuMoQ+3piNbnJxSFimb40Op5QS8day5X+tK9UXdcmcfAVdmXguHUYEBCy\n8Fhe7Et2ENQ5W6riJg7T/TpmrK6+kufOFo+25O/tiE7kcUpJOppbfB9RU3ZY\nxNdD\r\n=wyOb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e08737d65b8eaaea892f26d8b7c73f7663fb3a4f", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-ib-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.25.0", "semver": "^6.3.0", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.3.0_1619296908500_0.14135566931527", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "is-core-module", "version": "2.4.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.4.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "8e9fc8e15027b011418026e98f0e6f4d86305cc1", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.4.0.tgz", "fileCount": 10, "integrity": "sha512-6A2fkfq1rfeQZjxrZJGerpLCTHRNEBiSgnu0+obeJpEPZRUooHgsizvzv0ZjJwOz3iWIHdJtVWJ/tmPr3D21/A==", "signatures": [{"sig": "MEUCIQDfGeyUgGjoGECc1EMcYCs2EkLq+mbFP6fp44epNaSdNgIgcYVfRprluPVoTRbNUiNudisvZQZSVa17stZ/gyMLa+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmEbxCRA9TVsSAnZWagAA6MQP/0VD/CAd0mztX4latgDj\nZnJ4pqs5SKyR6m/C+w2Xx36C+9BjbX+7RjseK2U7fCdgX9Pg56H57Fo8yIeg\n0bqf+XR5Oap3gFaJSzYjdx838q2a5YpHcrelN7Dgsy2BV0pKHo6qoa4NOyS2\nhD/UBOIbzfgzoXL/aIHrFKKWGIVis7jfwFE+3jY+hiirqvfNQxeyL2Zf7E/4\ngnNQEGMljLJDLbQ+L8gJLq6ORewmkef18Sqj+33WSx2F0Im+jfOM4g+QYH56\nKolBJby+JDlF0fo29Y70e1TqABhIiQcZxABNJW3Na+R6WNE+pXXv+PDnj4FO\nlsy+rQTnSF430qOfd9Jf+g7ibb3+AffWCof20ThxhZsdF8UkDNVoHPAGOhUZ\nkuoovmsIESWiKzl5bDQfOoLWqg4EKQJslDpmoAl7PRLuEtTBB2Hu0bMbDhUQ\nyHb+I5t3nLWU3l3kg+nW+hyz13DPFc5VIc1HdltP5BUmPIjh4NzehuhfDlyi\nH8odx3Vw29opBW9MRmgvaK8rwtXLFfw0Z5yW6yXg+sBZxQIxRxyZV30NoecY\nSO17Pv/n6lE0y+4p0zmQ0qkSSVTCyLJ8Kzf7eATNvI9sMRgTuOxOGGMlWQs0\nEA3z80iCLaYjfP32K3gOGRMdbaR1QY65U3Gh6gKgpeZzbT5IiwE8jtF9Nqfe\nYKj+\r\n=pSu+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7a96cd96957cae116dc0d2883727cee11953276a", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.26.0", "semver": "^6.3.0", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.4.0_1620592368778_0.****************", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "is-core-module", "version": "2.5.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.5.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "f754843617c70bfd29b7bd87327400cda5c18491", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.5.0.tgz", "fileCount": 10, "integrity": "sha512-TXCMSDsEHMEEZ6eCA8rwRDbLu55MRGmrctljsBX/2v1d9/GzqHOxW5c5oPSgrUt2vBFXebu9rGqckXGPWOlYpg==", "signatures": [{"sig": "MEYCIQDBfnG5UbaDVqv21QC9ZO3eWQehLrlQ5Bwgq7bb1sAxnwIhAMDpkhnXsY/qE5FjkEMtCWPTC6HyWu5yXCeaBmgCJJHP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7TXPCRA9TVsSAnZWagAAoHUQAJFoGEdipAHObhzCZo75\nuso5uU+oc0AeOM+bbGdfuzWKS6tOOG+xDIGXFi+Bxx2ybroiDaGDmfOnLohJ\nMc2JERUQzuMTK5WAAGBXKx3+FiFtHO1l2zf65Gc2Dtj6MiSQn9z4MgrpnuBF\nGkSenFMdO0TndZ78dav9cp9/uVkqkqhiaUZfyp8Dt0JiAXGegp3p1rPOZLOk\nDlmdafBT5J6bXxCruvRYsHu4VrPbakwFfROZqo0pgzDOb5l2eZiCQKqSI36l\nL42g0zN4sS5r2H2YS1csGnWA4nI5GxOfXWiAPeLw98d7g4rosngxpXc5FcrR\nNfRb9Zd0aYjv4VzJK+bDgJr5Zm4US+CYobjhLmtFX96HsET4XeaQvH0U0tiQ\nhjSKk3ru015fauwKtXY3h2TejNKgk1IKhr/JVAzI49QqUgRa5TAHYYpbQ2/a\nl7VuXX3cjGX0zrV5zwpBWIz+ndXFlF+ftDWhuM/6NYVa9iCya3JbSMr9ONYO\nT3drUzF1gMyTbOwqBzOu5MTvBJMxfLwUjYGe71I+IUHWApvipu53LY89iY0G\nOhcTNQgrqrD/QWJjSm9jvgHxnRFzd1Vt3nj2uClDUStPe7jQBfPjaGjoTyH0\ngWVuAjGDNHPPQ1ysThQGkuI0J3xvaSloBmImXGr1VKxjEym8cnOKeXW9uswf\n1tYJ\r\n=OcPy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "18437846906ed6ba5146838b6ab0cad05a74417b", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "16.4.2", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.30.0", "semver": "^6.3.0", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.5.0_1626158543436_0.9225462032281682", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "is-core-module", "version": "2.6.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.6.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "d7553b2526fe59b92ba3e40c8df757ec8a709e19", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.6.0.tgz", "fileCount": 10, "integrity": "sha512-wShG8vs60jKfPWpF2KZRaAtvt3a20OAn7+IJ6hLPECpSABLcKtFKTTI4ZtH5QcBruBHlq+WsdHWyz0BCZW7svQ==", "signatures": [{"sig": "MEQCIB/Wdy9uhuwHCOuWEQfUdK++byCvap9BFiLgQ00I3O1YAiAobi2VCapbbXbLfc1635sa4QPEiUqUommcoUnovpkUFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhG/08CRA9TVsSAnZWagAAsFUP/Rl9QLxIdPMZqNG+FFSW\nPm/5BrxoZJS1clOoX3sBZhAgIsi5cKCd/o+hfdCy1l6KZBQQpXz4RGjiR34R\nEWUCUh5P9Vj3a/ZUltVct2WtFhl2HMUKuLl7ebckkzaiFibIkabedoCtYxI7\nQ7FQXqtjLk8eLe6SMUfWVIT5RCQafeZu2WuiFOoFZL/w/uPlasFXIvCr52QT\nouxMMWHQ/9tZeZvBBHfygRUNdrOZ0B7tfWvwsdeiSkDk3DvSkvnzptIMtOE9\neJ0lCLRdrBgrJcEC2CZITnNmqeFvu23YV1+s+yt8QUh1WiGk/fy1VLg25JJ3\nixiMnzJDXvKVwNLYiF1g9nTH75FGAvZJgDqw4mbEiduxuzxtLxl6eBPK5crB\nwjFHkYveNp+LRDySdjtzyVDj6y1ghH0gzeY8HxVNCU95HXt+KA2vzDOzWbHN\n34coUOjf5LaW6F0BpCT+fGLHHIuVPvH8lQtZ7/mNj4Q0DGiSQwq/y1rEGqGl\nX2x+uxaiIvhhYmRuaOrwsWvAG3o1TMqh3AO0gHtKB1dVHZysKZSupFk1pE4p\nCl57flqLVfXvsSO2zFzqC42cF7Ona8h0IJH7DVGMycM7se1TviXVmadTK8rT\nOJvX80SvaYgPsVlT5KEbc+/2RU+dMxQu6No39crRVlv7EAOWxWaZ5IaQ7qdz\nm4jm\r\n=Tnd0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7fc1e8584058d277e47c9a74926131f79084ea93", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Is this specifier a node.js core module?", "directories": {}, "_nodeVersion": "16.6.2", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.1", "eslint": "^7.32.0", "semver": "^6.3.0", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.6.0_1629224252806_0.2639213152583815", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "is-core-module", "version": "2.7.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.7.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "3c0ef7d31b4acfc574f80c58409d568a836848e3", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.7.0.tgz", "fileCount": 10, "integrity": "sha512-ByY+tjCciCr+9nLryBYcSD50EOGWt95c7tIsKTG1J2ixKKXPvF7Ej3AVd+UfDydAJom3biBGDBALaO79ktwgEQ==", "signatures": [{"sig": "MEYCIQClSh1q8/n5gcpAxHMT6xQ61Mx8naajbHwgiYZiyX1HhQIhAIdDVjEvr56G2Xo6lcl28R2Dv4zqaHrRXiQ7uIUeigzP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24329}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3c581d210f39331ee5459aa9a416e773c946ca63", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "16.9.1", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.1", "eslint": "^7.32.0", "semver": "^6.3.0", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.7.0_1632759856033_0.9572583746609107", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "is-core-module", "version": "2.8.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.8.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "0321336c3d0925e497fd97f5d95cb114a5ccd548", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.8.0.tgz", "fileCount": 10, "integrity": "sha512-vd15qHsaqrRL7dtH6QNuy0ndJmRDrS9HAM1CAiSifNUFv4x1a0CCVsj18hJ1mShxIG6T2i1sO78MkP56r0nYRw==", "signatures": [{"sig": "MEQCIEsYghUJlrGbUPPJzn1nRHr1Y9JkGDJPNt2SJXcfWGI4AiBLFPe/WKLHiFhSzfzJu4cKpBJ5pM0oplWSl8ADVEdq9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24971}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "04143b5c4c11410011b1dec1786285415b6af859", "scripts": {"lint": "eslint .", "test": "nyc npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "16.11.1", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.1", "eslint": "^7.32.0", "semver": "^6.3.0", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.8.0_1634234038757_0.8927399937281386", "host": "s3://npm-registry-packages"}}, "2.8.1": {"name": "is-core-module", "version": "2.8.1", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.8.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "f59fdfca701d5879d0a6b100a40aa1560ce27211", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.8.1.tgz", "fileCount": 9, "integrity": "sha512-SdNCUs284hr40hFTFP6l0IfZ/RSrMXF3qgoRHd3/79unUTvrFO/JoXwkGm+5J/Oe3E/b5GsnG330uUNgRpu1PA==", "signatures": [{"sig": "MEYCIQDR0axxXkArfC2liDRKqhIS0yu6EwRp+mCPSHwpaF2ObQIhALckYt5mif0S/JQRc4YBJfYSzq3nJOy4Fn2Fx+ZoWnFK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1gXKCRA9TVsSAnZWagAAhpsP/juFFmDeGjPF8MUqGK1o\nJIpJlblfSznkPIIyAJMFe3ktDyO5ecRCfcUPg0yXTFyAIjVykM/x8HjSZJyN\naH6g6PLW4UJYftQcqNLEtWxbUiOUaHu5QAIZle2tHnlmoXrSXjpfc19KCACc\nNLs+qdrL9d3/O4FWD37upygT+0/BTb0oweAOSq0E4O91ooi8acg8VupbQbSo\nhcnDgVHquXzCPG7XQOi8N3h6ZRwRRIZ6ZxQrKUERGpX0Qt3tZG79IW3rfLqb\nJO42WEc962j2S05BsGoRpEsr4RTkS86vZlCQp/1gQiGBTqVAmlrUWSTz5JQJ\nisw4ipWnr9kam1fTZi11/u2hY1DH9a4ujBwYw4Kjpm7rvA4bPmL6itIS07Ki\nSi/RqggzDwYQ6gqWKEkOfC67hSLIFPhrR5bnWJtPX17c6h3H82pfJGdCy/tO\nQSJgQhWJJK6uPW685Dv6Lq3KzPSnVOqaPabdpnxdF7f+N0HWreIR+Vi+/y9o\nMogQHx5PdKI/yZIZAcRNvAg+nMuqKJh5tpgZ/0vi6lhRzyF2x+GsNHglVMMO\nVN3tFTloHsc9M0s+5zaYpULdfzAMLNhWPZDN/QgRftwUPD4G34JaKmx1NsOk\nVbbrA7ByjaY2v/6G5HOGP8SKfm09B8xwwsipi7DpN4wZAPQT+ADm56XxKkwD\nXFNm\r\n=+95Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "d6a52cc00eb628188ca62af6da9bfbc55115ef58", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "17.3.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eslint": "^8.6.0", "semver": "^6.3.0", "auto-changelog": "^2.3.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.8.1_1641416138024_0.*****************", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "is-core-module", "version": "2.9.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.9.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "e1c34429cd51c6dd9e09e0799e396e27b19a9c69", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.9.0.tgz", "fileCount": 9, "integrity": "sha512-+5FPy5PnwmO3lvfMb0AsoPaBG+5KHUI0wYFXOtYPnVVVspTFUuMZNfNaNVRt3FZadstu2c8x23vykRW/NBoU6A==", "signatures": [{"sig": "MEUCICYum6rH0BJ70sfAJzF/nkELDnUsh8yT0Tq7Md1+H74DAiEAlzzsfOzSFTVfsd8Ipy8mMdKtVSc9KzveoZ9ftAh0ckY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXuHrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDTA//eewpfYwiRLLWz4UtjiLW/xOljumRKoUDzW+QcB/bBqSMoW/H\r\nqPlOyuIzs89RCGGgx8SqrzBGXwTiyvdbF6UUQBkzru/xk4k8eo8AhQ7G+Nrv\r\nMD34VsYzbotpfoVFoZeXKtGk3CMmIVD/1f8eI6dXm7K13WTERgxIzWYcFg0X\r\n7YEFePocXrFUQzIHZw8HYFxkLr14hsmb81ocSU0e33ng2GhWgAsCBNHj4uSj\r\n7NWujfauNOPLYLSN8iFDA1xfYRJKgYyi/6TgdqZbhpL6gn8n1oLJ/+uNgG0C\r\nbR0tWXBTz9BcvARdEh0cCMyBolJFa87oYLF9yciQgl8ooPdAQVn4KH06tnHw\r\n2/wwLJ1IXMC9muTndByuMQrqasQHWhtTFpe5MOHTCEDhQpce2UaLcgdqaOLC\r\n4LiSqE7lFJpc5nssHfNBC2Lpvng1w3KgTIhbeh+8vUYpXam+H3ftG6qfTJ77\r\nEFssWH41rvP5NZWzR6dLvxqZ2ZRBfnk5VCFS9XF9LTTjpTRovtnS+hMuD1Of\r\nOGowbXRknDjsw1ofcBE36jigB6PSc+aR7ngNx1JkWgfRcn6fFn2CAiInSbdq\r\nvXfvkGcWD8Y9Qgc7rtvr7tLUWGad1JJExnf936rgIRlTIfQ3+Mbm37n2az32\r\nXQzoUGYIW++HVsAzwHK3njCCjeqEfop04qc=\r\n=ujsY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "652a4cf0b27137db164647ab299cfa2eb8886110", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "18.0.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eslint": "=8.8.0", "semver": "^6.3.0", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.9.0_1650385387077_0.8884023023191567", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "is-core-module", "version": "2.10.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.10.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "9012ede0a91c69587e647514e1d5277019e728ed", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.10.0.tgz", "fileCount": 9, "integrity": "sha512-Erxj2n/LDAZ7H8WNJXd9tw38GYM3dv8rk8Zcs+jJuxYTW7sozH+SS8NtrSjVL1/vpLvWi1hxy96IzjJ3EHTJJg==", "signatures": [{"sig": "MEUCIQD2UkSxRDT+ayl+EkqXEXol/3iJ/Hd0XwQmwMrhJnnQqQIgLBSugAfZJYkcNCa+zt+CMjYpECB2edFp043lb9+2C/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi62oUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWzw//UAQMNcl/q51QmjW27n4t7erCGWSjGjgS+j0J1ziLgRQFZZbX\r\nhyIqBmcmwDhvrLkN/VBo6X3Z4xxFJ1QQ9xnc+w7A7GUGxx6x75ZIc3SNdrxv\r\neERDsr5tPIIwC+usOx3qdaRwAVSHaI/9j91cye+Bvzl6BQaB9DKj7bAdoOKv\r\n4uzdLaLsgrFzEBdVG0NRVsQt75H4hZDUDqtiqg+pnrzJbXkgTak0HO5gIYel\r\nDpe5aU7i9/JBv+a//ZSeOub/3mQBtDqDg3I2Pi9NROL/JGZ462vcnfUCZwcB\r\n2ndXOXt00/g/isy3cObLKNcvYCQ35pyjE2bmYJkABu9YrDg2EkMWz1WIb5kh\r\nzWVbcmerIO3TaO43ZiS3LgggScUf2DEZzP9WJ1JT1vydmySnGrDC40kiAg/E\r\nBXsk9dcQOLvIqnrZ8tY5mU1r+nwNE6f0ZfW7SHjYKUaqPSOmc7exf4jU04+9\r\nuVvlUmkfkJ7nkq6ZW4V6tg1enymIdBPmF/Svp//eymPfrpeH7EIIwZgSBZ3H\r\nEKMidYeoIrufmcMd905rWJP9LJthILE+TfSt1X8QajYh9IlDFGDwSLjFKSUS\r\nC8A4+uXIJZzOCW8VsjCzBtwtcyk6XUwkSnCvJCGXzoA4O+cpxNr8RmFNBTof\r\njenViReEAkhSb+xQapDbFC87Gu67nLERERs=\r\n=OKFe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "bd0bbdf0c9300cb3ff21902d9fce11c833da2c51", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "18.7.0", "dependencies": {"has": "^1.0.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eslint": "=8.8.0", "semver": "^6.3.0", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.10.0_1659595283974_0.9531364810534915", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "is-core-module", "version": "2.11.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.11.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "ad4cb3e3863e814523c96f3f58d26cc570ff0144", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.11.0.tgz", "fileCount": 9, "integrity": "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==", "signatures": [{"sig": "MEUCIQCXnXQD8t5dtd066XWvv3S0dF60E2/Os1bp/K3yqjr+kAIgZudZe7XVrwSkAXzNugJ2VuxwU0IqaRhrhBBKWhNMRBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTt09ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsSRAAlMAGnwjnPDIG60GeTuaCn4kbor/vDSO4gdMAgquG8AzBZu8+\r\nnsk6OV8wAiQ9gCprmwz3pRzVlRda71b+sREQ/yXe/srr7m1CgI3Y9KNzi5h6\r\nW3J9UnEWe7ya5+j1vqlGjuBRFE7rOcmkux+jWKpnm8yf+vBYrCinO1cF99TG\r\nweXgDmN/Vu6Ra2e6PetD96dfi5dM0ed8VH09K15OVmETX5S6iMkhOU4l3wC8\r\nvbaloKNhJpKbIs9mXqdUT0RWsTJ7FgvyYzN1OVjYcbQXlTstSrR8LJ7hq1T1\r\nL/hdzxJhwAWqtPG9KkXoUH8Lpp2f5CSnebEAH5R9yzR9TSQsX2N5k5FBy2lO\r\nMX99hJnQXmbk6A4dKTrtEg3FMQR695aNaiB0QdTf1MdPJLCph6AA9j28ho2A\r\nGnz3sjDW8j2WLtsh0nCp8aLHj6onGwKqHvT7KEUaAhkMamACDYYP9fbgv+C/\r\nFSJP1R9RHzLt0GAcsrfSE58fI+sGiVU9CcjCIv7Q13wF3VcpwhkbYog7lBQ0\r\n1QmTMEAgVkDX+fNmjZdOjcoCZau9VG1fC9ftCI7VswujNTt39/UoVmXeEUZK\r\nray0LJavs84nYIV23B8cE11pbLiSCT5xzwtucaM/JqSIj3gHg2yiMH18qqGa\r\nd0Rpa2npyZH6CoOmJiB6z72R5l0S2+iYbeY=\r\n=9BnL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "12cfb2d3bcefc3de19264b3a39208d29f5432516", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "19.0.0", "dependencies": {"has": "^1.0.3"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.1", "nyc": "^10.3.2", "tape": "^5.6.1", "eslint": "=8.8.0", "semver": "^6.3.0", "npmignore": "^0.3.0", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.11.0_1666112829392_0.17125828222085593", "host": "s3://npm-registry-packages"}}, "2.12.0": {"name": "is-core-module", "version": "2.12.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.12.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "36ad62f6f73c8253fd6472517a12483cf03e7ec4", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.12.0.tgz", "fileCount": 9, "integrity": "sha512-RECHCBCd/viahWmwj6enj19sKbHfJrddi/6cBDsNTKbNq0f7VeaUkBo60BqzvPqo/W54ChS62Z5qyun7cfOMqQ==", "signatures": [{"sig": "MEQCIGjkrJyZ6nCXpHH9KJgG4ekkMQTKQqUlALXuiGfJSLUFAiBs1KZQGLQJiRsnt+vzjYzFRnJk1zUB3ET/aGWOALNNPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNGJjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEoQ//RIc7Q9PjqFSvdmixGkeFa95tjEhvELJm08DylSuzDTD4bSfO\r\nS040yZsOp2Ep6Nru6MVUHQE93oROQ626M/TD60MFIjgQFo+QTmqDfPNOfc6K\r\n4uXvRCXHBAH/a1/60GZa3etFI6rtHYzR5p+JwDIY7qSHbB/VdjgkZRVp4v7f\r\nKu0sm/eoK5Or9uIDWfxbsL48b++sZGEHSuiN8bN78XODYtBRvMFPv52XfJ1T\r\nw/kItu8EYkCdYPogBsrtl2QIBc79sSfkgbdKIhJjfLQNwMT5UfhF09NCO8oQ\r\ndLdQwuxFhjCRTjRa4Dre+zac+Mtbj8K5TpQeeIVSwK2mcdRQZeIObnKiCPs4\r\nUtJnXllZKAk0nbFV2aLBw3l02+l8aYXfZY8eQwqfsZqLb5PtUdUfIKtixp3C\r\n4nMGYMXj4G+TDz97j3lPXse4cS8z2cpEAd9DBGy335QSqn6itXPEOhV5Cx8K\r\n/RCKlj3B/ZtcHnnVjSosPY7SEks3tlfZsvb7hqGtnflHUr9H/kYO426jElXS\r\nCJxR4ihZPgKcIJ0ExjaUNSDkDMbGBm+FDe+DklD0DtVhl/FP9cxxZ59ZO2nc\r\n3NTOIgksrywI+5vgimVFtfADci/yxNJ3hmsAZoR1Rg0PjOfk6Z6wBMLcUEC3\r\n8FJTLZyOghqD6eMY/3kTeor2ChguaHUfvaU=\r\n=jfnZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "29272c9932f79f43d1ac680b78d130be9fb1a676", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "19.8.1", "dependencies": {"has": "^1.0.3"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "semver": "^6.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.12.0_1681154659149_0.****************", "host": "s3://npm-registry-packages"}}, "2.12.1": {"name": "is-core-module", "version": "2.12.1", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.12.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "0c0b6885b6f80011c71541ce15c8d66cf5a4f9fd", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.12.1.tgz", "fileCount": 9, "integrity": "sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==", "signatures": [{"sig": "MEYCIQD3NzzpRWuWnCjjmfHngMI8JXvTXqb1jirEmjA2MYc16QIhAKlExGcMColC5N7MXGU9uYJkPfZh8lqnbjt4RewlSpSA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29292}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "77d33fb50747b9b6a89841542f6628fd00fb2cf0", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "20.2.0", "dependencies": {"has": "^1.0.3"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "semver": "^6.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.12.1_1684256724721_0.34969833744100876", "host": "s3://npm-registry-packages"}}, "2.13.0": {"name": "is-core-module", "version": "2.13.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.13.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "bb52aa6e2cbd49a30c2ba68c42bf3435ba6072db", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.0.tgz", "fileCount": 9, "integrity": "sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ==", "signatures": [{"sig": "MEUCIHPDtHTQgbtRjq+UfGtgWQqz7GWOXZ7JFa+prgnSDJ2zAiEAyrl53cPdt9kmNf8KRzntMSRcj7tWXFAeaSK9LAqxVmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29815}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "670c38eaecccf743ddcb520a4bc80993c6bc4eb7", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "20.5.0", "dependencies": {"has": "^1.0.3"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.6.6", "eslint": "=8.8.0", "semver": "^6.3.1", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.13.0_1691190664497_0.9021294748126674", "host": "s3://npm-registry-packages"}}, "2.13.1": {"name": "is-core-module", "version": "2.13.1", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.13.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "ad0d7532c6fea9da1ebdc82742d74525c6273384", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz", "fileCount": 9, "integrity": "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==", "signatures": [{"sig": "MEYCIQC1LvrEsf5IBF6xHinrTuhdLGkiqC53QdoRdrCTK0+ZQgIhAMbeBX1jbQOglx1+D1pE5CmAGjtTgqiTJi/UimQmrX8R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30239}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8317b311856a61935d7257ad5f31f9b0cfd13b5f", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "dependencies": {"hasown": "^2.0.0"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.7.1", "eslint": "=8.8.0", "semver": "^6.3.1", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "mock-property": "^1.0.2", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.13.1_1697864269163_0.026580204144382336", "host": "s3://npm-registry-packages"}}, "2.14.0": {"name": "is-core-module", "version": "2.14.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.14.0", "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "43b8ef9f46a6a08888db67b1ffd4ec9e3dfd59d1", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.14.0.tgz", "fileCount": 9, "integrity": "sha512-a5dFJih5ZLYlRtDc0dZWP7RiKr6xIKzmn/oAYCDvdLThadVgyJwlaoQPmRtMSpz+rk0OGAgIu+TcM9HUF0fk1A==", "signatures": [{"sig": "MEQCIFrZL8uk8+HQHRrFzTKTOFdGec0hMAu1UDYUA2AdgplXAiAJknUY1MJfGGwDzvbFmLO3ui3sBF+hPqRfvHEY0uVRIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31063}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3531bbfe28a137943a47607be95a239753bab820", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "22.3.0", "dependencies": {"hasown": "^2.0.2"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.8.1", "eslint": "=8.8.0", "semver": "^6.3.1", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.14.0_1718915583183_0.6411148053601121", "host": "s3://npm-registry-packages"}}, "2.15.0": {"name": "is-core-module", "version": "2.15.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.15.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "71c72ec5442ace7e76b306e9d48db361f22699ea", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.15.0.tgz", "fileCount": 9, "integrity": "sha512-Dd+Lb2/zvk9SKy1TGCt1wFJFo/MWBPMX5x7KcvLajWTGuomczdQX61PvY5yK6SVACwpoexWo81IfFyoKY2QnTA==", "signatures": [{"sig": "MEYCIQDUd18JRyH04SSfo2UG3Ygye8mGhxzxvo236R60vTCNiQIhAMTFnRE8JoL9Zm6LevWDjWf9BzHmLNDVjkmTHGUL23xI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31459}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8ec336652e3b0237dcb3b42360d500b9e776794d", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "22.5.0", "dependencies": {"hasown": "^2.0.2"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.8.1", "eslint": "=8.8.0", "semver": "^6.3.1", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.15.0_1721248418090_0.1720772272216744", "host": "s3://npm-registry-packages"}}, "2.15.1": {"name": "is-core-module", "version": "2.15.1", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.15.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "a7363a25bee942fefab0de13bf6aa372c82dcc37", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.15.1.tgz", "fileCount": 9, "integrity": "sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==", "signatures": [{"sig": "MEYCIQCDRONkmmbtezk4HVvmw4d9cmoXB6pB7Cv3qLtHWm57XAIhANUm37zew8kXGfBQAnaMlSX6jLkKbJ/oI6lvkYyZAE2i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32706}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9a0ab3c874c6cf9c5682f35c3d9dadcc9d0093b3", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "22.6.0", "dependencies": {"hasown": "^2.0.2"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.8.1", "eslint": "=8.8.0", "semver": "^6.3.1", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "mock-property": "^1.1.0", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.15.1_1724269937690_0.16731255767892006", "host": "s3://npm-registry-packages"}}, "2.16.0": {"name": "is-core-module", "version": "2.16.0", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-core-module@2.16.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-core-module", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "dist": {"shasum": "6c01ffdd5e33c49c1d2abfa93334a85cb56bd81c", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.0.tgz", "fileCount": 9, "integrity": "sha512-urTSINYfAYgcbLb0yDQ6egFm6h3Mo1DcF9EkyXSRjjzdHbsulg01qhwWuXdOoUBuTkbQ80KDboXa0vFJ+BDH+g==", "signatures": [{"sig": "MEQCIFvEDAH9OrBRRWAntcIux/KNZ3sC21Tm6WaP6/Pt31UUAiA6bxrdiPaIepJX+qrztECEXoymirQdzmH5Z2TNzJR7Jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33228}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "672bdddabefdc60bff6783ec843a732eba4b1340", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-core-module.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Is this specifier a node.js core module?", "directories": {}, "sideEffects": false, "_nodeVersion": "23.4.0", "dependencies": {"hasown": "^2.0.2"}, "publishConfig": {"ignore": [".github"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "semver": "^6.3.1", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "mock-property": "^1.1.0", "auto-changelog": "^2.5.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-core-module_2.16.0_1734122836718_0.3649056505583339", "host": "s3://npm-registry-packages-npm-production"}}, "2.16.1": {"name": "is-core-module", "version": "2.16.1", "description": "Is this specifier a node.js core module?", "main": "index.js", "sideEffects": false, "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-core-module.git"}, "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "homepage": "https://github.com/inspect-js/is-core-module", "dependencies": {"hasown": "^2.0.2"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.9.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github"]}, "engines": {"node": ">= 0.4"}, "_id": "is-core-module@2.16.1", "gitHead": "7c4284853357b2fcd49d42010d5a2b8a8420905f", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "shasum": "2a98801a849f43e2add644fbb6bc6229b19a4ef4", "tarball": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "fileCount": 9, "unpackedSize": 33475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBrGR2MUJsUstAY93cEDrzi47USefGMwVXvpO21ONyhSAiAOXKjmAhX60Ic4o5pEVZtdzyl3j/IDHy1EC1oV1cMaSA=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-core-module_2.16.1_1734816206995_0.9080496812420369"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-09-29T03:21:42.420Z", "modified": "2024-12-21T21:23:27.438Z", "1.0.0": "2014-09-29T03:21:42.420Z", "1.0.1": "2014-09-29T03:36:28.359Z", "1.0.2": "2014-09-29T03:38:15.852Z", "2.0.0": "2020-09-29T18:08:41.736Z", "2.1.0": "2020-11-04T20:14:31.722Z", "2.2.0": "2020-11-26T17:56:48.104Z", "2.3.0": "2021-04-24T20:41:48.630Z", "2.4.0": "2021-05-09T20:32:48.938Z", "2.5.0": "2021-07-13T06:42:23.569Z", "2.6.0": "2021-08-17T18:17:32.930Z", "2.7.0": "2021-09-27T16:24:16.228Z", "2.8.0": "2021-10-14T17:53:58.949Z", "2.8.1": "2022-01-05T20:55:38.175Z", "2.9.0": "2022-04-19T16:23:07.238Z", "2.10.0": "2022-08-04T06:41:24.202Z", "2.11.0": "2022-10-18T17:07:09.553Z", "2.12.0": "2023-04-10T19:24:19.335Z", "2.12.1": "2023-05-16T17:05:24.917Z", "2.13.0": "2023-08-04T23:11:04.700Z", "2.13.1": "2023-10-21T04:57:49.460Z", "2.14.0": "2024-06-20T20:33:03.354Z", "2.15.0": "2024-07-17T20:33:38.229Z", "2.15.1": "2024-08-21T19:52:17.873Z", "2.16.0": "2024-12-13T20:47:16.910Z", "2.16.1": "2024-12-21T21:23:27.215Z"}, "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-core-module", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-core-module.git"}, "description": "Is this specifier a node.js core module?", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-core-module <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this specifier a node.js core module? Optionally provide a node version to check; defaults to the current node version.\n\n## Example\n\n```js\nvar isCore = require('is-core-module');\nvar assert = require('assert');\nassert(isCore('fs'));\nassert(!isCore('butts'));\n```\n\n## Tests\nClone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-core-module\n[2]: https://versionbadg.es/inspect-js/is-core-module.svg\n[5]: https://david-dm.org/inspect-js/is-core-module.svg\n[6]: https://david-dm.org/inspect-js/is-core-module\n[7]: https://david-dm.org/inspect-js/is-core-module/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-core-module#info=devDependencies\n[11]: https://nodei.co/npm/is-core-module.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-core-module.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-core-module.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-core-module\n[codecov-image]: https://codecov.io/gh/inspect-js/is-core-module/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-core-module/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-core-module\n[actions-url]: https://github.com/inspect-js/is-core-module/actions\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}