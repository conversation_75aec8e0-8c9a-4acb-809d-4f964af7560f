{"name": "react-redux", "dist-tags": {"previous": "4.4.10", "previous-5.x": "5.1.2", "fix-test": "7.1.2-alpha.0", "rc": "8.0.0-rc.1", "alpha": "9.0.0-alpha.1", "beta": "9.0.0-beta.0", "next": "9.0.0-rc.0", "latest": "9.2.0"}, "versions": {"0.1.0": {"name": "react-redux", "version": "0.1.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.6.0", "rimraf": "^2.3.4", "webpack": "^1.9.6", "istanbul": "^0.3.15", "babel-core": "5.6.15", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"react": "^0.13.0", "redux": ">=1.0.0-alpha"}, "dist": {"shasum": "fdc7b3baeba23a912ed67af0c74cf6660c2455ac", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.1.0.tgz", "integrity": "sha512-CunBqQBGrGjafIxqmReNeIQdQak7B3v5b8i55wF7g+9iKX/ZnEDZ3ade+b4derMNwmv4QsiMhsIMoHmg29mGtA==", "signatures": [{"sig": "MEYCIQCkfcNzejn3YrHoPaXTSUpGqD7pn62owyeS7Sp0lf9xjQIhANzK2KS3j6wQsaiSwObZFrduCVLb0elEDghy5/70Jlaj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "BROKEN RELEASE! Use 0.2.1 instead. Thanks."}, "1.0.0-alpha": {"name": "react-redux", "version": "1.0.0-alpha", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.6.0", "rimraf": "^2.3.4", "webpack": "^1.9.6", "istanbul": "^0.3.15", "babel-core": "5.6.15", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"react": "^0.13.0", "redux": ">=1.0.0-alpha"}, "dist": {"shasum": "9c7c91d614dde18d117adfb7fb0667912a51d60f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-1.0.0-alpha.tgz", "integrity": "sha512-7/YYs6DHFpRO/81BeMJlPWtXK6iSGgBPRQiXvZReFANQ+iJ2BAEmUdvLaG2bEBaJSrcgLBt/v4EScq2XCaf2yg==", "signatures": [{"sig": "MEUCIQCuXL+L2P6WD26AMLAYZw5H6IpVfG4TbZxRmSvHQ4NCzQIgBlTiqvaeVVYjVwhSbfFQjOy4fk13sUCSghVyBAIZK/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "OOPS! It wasn't a great idea to call it 1.0 alpha because we're still iterating on connector APIs. Please use 0.2.1 instead."}, "0.2.0": {"name": "react-redux", "version": "0.2.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.6.0", "rimraf": "^2.3.4", "webpack": "^1.9.6", "istanbul": "^0.3.15", "babel-core": "5.6.15", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"react": "^0.13.0 || 0.14.0-beta1", "redux": "^1.0.0 || 1.0.0-alpha || 1.0.0-rc"}, "dist": {"shasum": "51850d990762ac85f7d82bf11b2b3fe3ddaf2c7d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.2.0.tgz", "integrity": "sha512-E6a8RQ+9nPcSJe3AfYEU3OTvDrAOPxRoDksI1jhtfj2VyDw292PgN2b8fnODGt/TnMHoi9JOiQYsPu8sIHH1Xg==", "signatures": [{"sig": "MEUCIQDm2OG0ZRxSike2giFLsMueDomzgyaXf01mopMJbhqK+wIgRrhPxDiwOuI00jLXcbFPnAbRb2eSxT3ZnX4H8/aM0kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.1": {"name": "react-redux", "version": "0.2.1", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.6.0", "rimraf": "^2.3.4", "webpack": "^1.9.6", "istanbul": "^0.3.15", "babel-core": "5.6.15", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"react": "^0.13.0 || 0.14.0-beta1", "redux": "^1.0.0 || 1.0.0-alpha || 1.0.0-rc"}, "dist": {"shasum": "2c9b7f31c1c35baa34bbdae8256d1fcc9a19433b", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.2.1.tgz", "integrity": "sha512-yxibc59pJsyOzx3lyX9AszAle50B0rbEc3MehwdCkVyOdtF2+5maDHnzxuv1tVAGTIp4KoUL9V9PegPbWOczAQ==", "signatures": [{"sig": "MEUCIQDn/+1w4xCm4xxDDcqJlzI9TUbIL7fI0JEK3PAo4IRPHwIgWmpRnq0xEz8NJ1dbvL+rmWRHH81ZTl0sXwrbRP3SJb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.2": {"name": "react-redux", "version": "0.2.2", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.6.0", "rimraf": "^2.3.4", "webpack": "^1.9.6", "istanbul": "^0.3.15", "babel-core": "5.6.15", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-alpha || 1.0.0-rc"}, "dist": {"shasum": "509e742550469796a10e76702dfceb901826a305", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.2.2.tgz", "integrity": "sha512-/qltXD8gf2R76HjipF4cLBk7yLKik2aLaUFJs8myGBmyxZmbaW4iAsxzTu3EeeBx5MVwYSxVAfZZOKdkF+TQ4g==", "signatures": [{"sig": "MEQCIA+/gDSvLmgNLf+DkJbdhTlDn6iLkg9bdi6i5CkDpV4mAiAVUOYNmU8+MB826w4F2LyhBrtJksoDYAVVclHQG7a3/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "react-redux", "version": "0.3.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-alpha || 1.0.0-rc"}, "dist": {"shasum": "67fe33a0a9755ff1ca7fd2b0e3da109526feb7dd", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.3.0.tgz", "integrity": "sha512-msV5Aixhu5wDj3oEr/OZdIL+qzKrRzeB5ArjNONnStGymheS5ttqLr1LXrOwrOkCMwXxu3eMstaCutgRoLV93A==", "signatures": [{"sig": "MEUCIQClDQ73ZMk9NpquetWHOK9I5MHiL6kCJRTYXl8MVN2H3gIgBgJy/YmfN0dJoNgDUOuxVN3OnOk7wBRu956ki43U/+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "react-redux", "version": "0.4.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-alpha || 1.0.0-rc"}, "dist": {"shasum": "514e06098374211e6c780f7baf69a0b30d2f9d87", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.4.0.tgz", "integrity": "sha512-ZKkXGXGYohunZiJX8We73sqH/nNxeFrPLgy+FU4wfY3WRfsRwVbTfkcBZX8ftzQbYR2AXBYdUs/Zetwk/gNk6g==", "signatures": [{"sig": "MEYCIQDd2Cd23LMKq4k5QqHVgJXOfrdetObSZxcEejAN+r1dIgIhAKvd+GcDrWw7XKhhkNEuDgrHyYH6ZFQo+lppMd0cnjTB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "react-redux", "version": "0.5.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "f775a2e2609fc3ed10db9da93e02cad0990d5824", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.5.0.tgz", "integrity": "sha512-wtTF8EOOWN3wMFNtIrqMEiasFdVLqdOAU//UUGX+VURPJdFR+2gj9SnBYw6+K7GqH2DyHJsvZbeU01jXczXKJA==", "signatures": [{"sig": "MEQCIG0liCidjgESC4PAcTD14C+bkeIo2o07fENsJbIHTaOcAiAPTvb0nfYl3u2iRyyhNyxiO9LP8Nmd60glPnu+L7UHzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.1": {"name": "react-redux", "version": "0.5.1", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "c83f68aa8cbb0963f5fd1a5f5768be4e70ae489e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.5.1.tgz", "integrity": "sha512-IfTpFtsLYV/gS1Lz3kef0OAC0gAyJlS2/atw41iNJoo/uLGKnIVFNMwdlE1+KNAFyoqSRJy1moHvkYL7da4+yg==", "signatures": [{"sig": "MEUCIE0t4H+hFwwY+GQ+M62+Zdlc0SuEMKyEvZgdVLBAGA6PAiEAoqA/I9N+Up8VoWFgO4kM+HQl/FHMsKICEl/9dxb9afA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.2": {"name": "react-redux", "version": "0.5.2", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "1213be87fd8b1643ac36b62a34d8d25bb8130587", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.5.2.tgz", "integrity": "sha512-J6B13lw/+qqXaspPms5PtEtzqbQbRPL1jPyUW7a9yZWOzn0nMGccgY4b0v38c2A2Zp4COmB358w9Ytz75ojAww==", "signatures": [{"sig": "MEQCIEu5Io5gAJjWWBGLqBr+n1noLfFIRRwaDuOFNRI+t2KXAiB0DlxzEbpFv1/g/hAItVIZuABrF00vEbbrVqsf/Ga1RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.3": {"name": "react-redux", "version": "0.5.3", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "b100277e02ed9dc0287ec60c3a0ed6bed7303dec", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.5.3.tgz", "integrity": "sha512-9n6CI2NP4kxk/Le1u9uCcBPxWnTl/IYHvjdOlbIYssLnJRSOzinCYfJo70KH9u3Y8XuGB3ub0MYSopX5nSktug==", "signatures": [{"sig": "MEUCIQC1X7CLGkCWf8i1GcnGorTFTSfQ/R5qVf1S5dAavZv6pAIgIcBZnCig2XytPwLNpOHfV9ub0pUiVsGzU4X25eaOq7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.0": {"name": "react-redux", "version": "0.6.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "3420effd021a5407d252237a69629b47cbb89c0d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.6.0.tgz", "integrity": "sha512-rFfU5gCOrdb4d7iZ+dtMRrsw24q3MzJrax0bR8EJ1MR6TupD2F01jNKtahxq90n6iHrD+Xy2/uOZKXPbZw3+8w==", "signatures": [{"sig": "MEQCIHjYKY11AlmbEj0/Jj/7SjOBto+dPC9zsxh94cTwCpIxAiBvcIRKxvQWBpWlDxRBNS89VsQk8s8MVgFdagC2ekbRWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "This release breaks React Native, please consider updating."}, "0.7.0": {"name": "react-redux", "version": "0.7.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "332628502070bf54041e8bbebab835ed22d6bc8b", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.7.0.tgz", "integrity": "sha512-O1dvGAxGmrNxW5k8RxmrfwzCswx08lQmDOA8RE9HcLgAUu8ATJwSIE+88eE0a/L2+MA0NgKiAymFlYcPxcsNmg==", "signatures": [{"sig": "MEYCIQC5RYmHVU/lklffQ5TRh3e4TYVn956cuTaE6IDcpIBefwIhAJaLQh+XLxbIqebrUIK7/nz94tS3XBqDkPZGOoMcyqAl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.0": {"name": "react-redux", "version": "0.8.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "d5fbeb71a2e6102e4401694b4e2c0c1546a09e44", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.8.0.tgz", "integrity": "sha512-LxDjN/m+HECfyVr3reFFIA1tPBl8lMdAUq509WLdZVArUvaI0IidPXat0WEwok1eAMz8IQ/JVf8m9MB7y3lFxw==", "signatures": [{"sig": "MEUCIFGTq11i2hPWsZB5AEDF3+IVYaYneZ2OKt0BRba/ODnNAiEA/QRI+l5djFDIL5b/g6bS0cfNHFnaefMi13Y5WxQG/L0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "0.8.0 is deprecated because it has critical issues with updating. Use 0.8.2 or 0.9.0 instead."}, "0.8.1": {"name": "react-redux", "version": "0.8.1", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "61b9d1b097cf8250533e5ed6ae21e9fbad16ed65", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.8.1.tgz", "integrity": "sha512-RVUCU6ZPXSsB1dku5hp6BtKDfcmm7Wu/ekl3AcjlBXue43q6qnYOembFYgNxuoXoTrquiM9fTSthzXVcrxDWYg==", "signatures": [{"sig": "MEYCIQC9SDgYcTRPMwUTFQya1LfU0du2/x/X2H4jwVZ7JgzBZAIhAKJrbQNOhz+3VqNJ4V+xiaFs+7+4gq2HOJWu6rO90nI3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "0.8.1 is deprecated because it has critical issues with updating. Use 0.8.2 or 0.9.0 instead."}, "0.8.2": {"name": "react-redux", "version": "0.8.2", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "4b1076349e5e7ec1559157b441233e81b75c5c6f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.8.2.tgz", "integrity": "sha512-VdbKx3OEE7zA6xk+uFkkLAag/cihlIg3ga6KbnT6A33njwGCAsYUwzHq2Nf1Nt9oF/zVvMISkvRspCeirv47Xw==", "signatures": [{"sig": "MEUCIQDl3NnjRhBboRMov+/iZ3DRFU3YbmXStBLp7n2dxcRNHwIgFHUiIa4YnTs+xn0iKKYqOx28S2qdUNZxTuy0WBm57ZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.0": {"name": "react-redux", "version": "0.9.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.5.8", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.13.0", "redux": ">=1.0.0-alpha", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.9.6", "istanbul": "^0.3.17", "babel-core": "5.6.18", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.1.4", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "6e075d755689e6ac42f6c765516a997c0d5f7cf4", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-0.9.0.tgz", "integrity": "sha512-v71io1MbDQKdtWQjTpZY3waCjcxFLzdUfki5N1DuiDENp1TlztN7qQZJ10iKVTVPoajsyPOSYc3phfvUkGHYtQ==", "signatures": [{"sig": "MEUCIQC7AZpOyTg+2gMo8kKFLZJnftXjVe8xJPOGC+r3UJCfwAIgJ3Wyni/w/JG6UJLN6NouZtyLlfZgC2YkQle4M3duLio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "react-redux", "version": "1.0.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "redux": "^1.0.1", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-beta3"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "41f0eabd324a6d2e5a4f2a55dbb47bbffe190101", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-1.0.0.tgz", "integrity": "sha512-SOsmqRRCuM/CZUUIhZBI8c8fVsOizlAe+O9GnLICWGcyw9k+f8ozTMTbb2/d62FZQ/Q6YXceMZCDKVBH6ghjGw==", "signatures": [{"sig": "MEYCIQCvXzMAA1dnG9agoKX1P5hZiN4iysE5SiUswahrG6/nLgIhAPi/oQrayJAPXAOwMYHX7wSkHn4Dcbo1vIhv5lkiUtF4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "react-redux", "version": "1.0.1", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "redux": "^1.0.1", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-beta3"}, "peerDependencies": {"redux": "^1.0.0 || 1.0.0-rc"}, "dist": {"shasum": "c4c4b689ffd35b27651fb8e87b2c8bdc7bc57040", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-1.0.1.tgz", "integrity": "sha512-rlfSY/gOGU8dLeVRix9c8p0qeL/txEgX4zHkD29AASfq04lCGfy3d+bRGHEATDD4VjSKskKU+UPu4tSaZlhNEg==", "signatures": [{"sig": "MEQCIBP7JupghhKT2TcnI/7747s/M7OzvFCoJ0XSNWUXeXEvAiAjRT0cqICq/lmoR5p4vaN5+YkLds4OVJ7yStykUnovpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "react-redux", "version": "2.0.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "redux": "^2.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-beta3"}, "peerDependencies": {"redux": "^2.0.0"}, "dist": {"shasum": "c8ec1872189e2668ca0baa7d38e08786840b099d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-2.0.0.tgz", "integrity": "sha512-lXZCnr+RKOHFwCVpevVyKQ3kMBTSu+EvrnPuOm3onjgIc2CaTLmSUuvdkiVU9fo1opbGk/xv67rXTcdalGwVog==", "signatures": [{"sig": "MEQCIG2ycxXxbmFjGRO6t3trU16JLZ3DZgH21odctXl2/Z1pAiA0cRSC05Qoi0+1J8HxmY83T3DLxUWBEcM/jl9zjPab0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "react-redux", "version": "2.1.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "redux": "^2.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-beta3"}, "peerDependencies": {"redux": "^2.0.0"}, "dist": {"shasum": "8ce235578e3eedba3c0cd94099b28686b7f43682", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-2.1.0.tgz", "integrity": "sha512-OQE0+SMSGvM6b6R0+eZ/QQztiinwpA1eoNxAOVCQ1R2kAkICzJVrUaURCL5jZOpSqogkHPol0uWP1AprHIxdjA==", "signatures": [{"sig": "MEYCIQCssE2GVX2lSuUEsE7LjODBMZPCgQRuD3VO+L8l4J0sfwIhAK86cg1jzEiYVMx5ykOtHOhAdo6FB9YBQlmMyRM7Lnks", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.1": {"name": "react-redux", "version": "2.1.1", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "redux": "^2.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-beta3", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-beta3"}, "peerDependencies": {"redux": "^2.0.0"}, "dist": {"shasum": "35beac44a11708166ab9b5afc129f497205b8f3e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-2.1.1.tgz", "integrity": "sha512-Cmh6ZceuQw3nvNqAkjrvhfD9pFe1KgWrH7q0zuVCfMneOlVtUk+GvzLTN/V04ojsKG5x23IRtNVrQ+bNJOWmpQ==", "signatures": [{"sig": "MEUCIQDxAHK1Vj5RlPxWo4XqC24NIdiiAB9FlRQingrifaq4jgIgUN/wXd//WuZgK7PTqyp0P+HVR1dD3pmBEv5Z0tSXCA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.2": {"name": "react-redux", "version": "2.1.2", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-beta3", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-beta3"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "4b26e02a7e049d1dc8ba5200999a3e153b28afc7", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-2.1.2.tgz", "integrity": "sha512-8iWXv0Qri3fbPBcXs8BklvJaupdwhO8cJc5ImR0wtST56vY8a3ttm77f0MU9z67AMLkkgnMTvVjzJshgYGLdZQ==", "signatures": [{"sig": "MEUCIQD3bP258PJi7p11MkpKMqEJ2URtcX3drDcOkMA/JPxa7QIgCOuBdknRjGT5hKf7aVRxtN0wPrDIGdHpUjQOPrhuSX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0-alpha": {"name": "react-redux", "version": "3.0.0-alpha", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-rc1"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "0f539bad4bca7837aa9a81ed73191c4c793d3510", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-3.0.0-alpha.tgz", "integrity": "sha512-MJZRIJYUjPjAInDGPaKKNC6iTVFNxB0cniDAO1Pay0y1jy0KCH3DopXcsrJY8tS1wsXwUNcWQtiZKhbX4RD7tg==", "signatures": [{"sig": "MEUCIHkrGm7OKH3M2c+hVw7MYEvqypRUs9oqgfB17+Els93NAiEAlYIX7UBq9iN+ybsjf/YOU3YS5Y2tMsPh55hfkoJHktU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "react-redux", "version": "3.0.0", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-rc1"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "6aa0f9be5fa8fba1513051d4f61459c2871ff7fc", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-3.0.0.tgz", "integrity": "sha512-7t4Gz+wXqFoimz6d1m8GjJ3k6DyDLVXIoE+H8LHHGGKouTmRjXH3Xw7whRnaXlNwNHzjXkbo0IQmDNlCuwkyoQ==", "signatures": [{"sig": "MEYCIQDf41ZMCydb7O4b+OcQBtfYW3quR5cqCwd7C7LQ8+Ro3wIhAOnA+RHjod9TYYYuiImSUE0febzS1d52n8sUB/ZGZMJT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.1": {"name": "react-redux", "version": "3.0.1", "dependencies": {"invariant": "^2.0.0"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-rc1"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "7dbd7ebfcd82b714d778f5b450b50f4e6640f244", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-3.0.1.tgz", "integrity": "sha512-P4bDRWiD3WZ+eugLpcQn/vYjZ8xUIaWlonHH7jpe2i0BSy9rD0kDhlVTNLbccdZ3tf5ZF+y3pOONqZsKM5GUog==", "signatures": [{"sig": "MEYCIQDn16nR1z08Hxhpzxt6OqFDO/Phz9PSct4a6xkd0iQxhwIhAJSgMPch1NZYWAV/NP6pp6iEdfznzEn2SOJGumB6FpCC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.0": {"name": "react-redux", "version": "3.1.0", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-rc1"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "04597ba448ad79237c412e7644769ff5921ce53a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-3.1.0.tgz", "integrity": "sha512-RvsjSBdI8o9tcJy40ffVwWQWDQAOAsPDlqpFHlO8XUtcdzBJcsD/mXPK+rYJGu7Br1nYmhzhu2MfrlGnUcMARA==", "signatures": [{"sig": "MEUCIQD6HsHk3Y2esPJY6tEBE3cfCb+fnKriqKn4ezWOvllOqQIgKxGXQWEgM1Laosd7IFZR90N2AYx/tP4os8JnHrkIBXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "react-redux", "version": "4.0.0", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "fc32f6aa16922ef1a914ffc3dc04f613f6bc4346", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.0.tgz", "integrity": "sha512-SmRF3kZg687Og9PVVrSefhEKKKpnbHMaV282nUMz9/+3RpOEuSGaipY0WCKwFs6STRaCTBZXOWNSbFAJXBmn2A==", "signatures": [{"sig": "MEQCIFrxVsRvobz1BtaqZeRwCKmlbRLi2MZAZcMf8Ppnzx++AiBa1jaNlX85tTXsAag/IhzsyUvWV5GlCzRmCnP+oV2zBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.1": {"name": "react-redux", "version": "3.1.1", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-rc1"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "e00e19716f00cd8c9ab3791cadeea53a34dc0695", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-3.1.1.tgz", "integrity": "sha512-LeV1YEMFlegdM+5JnZk8iU39voMItv4TY5sCVW5wV47Xv2DhfvqOrISyuUcH+xJzNwmTrZHJHtEc6li7fmUWow==", "signatures": [{"sig": "MEQCIEr26akRWUC9/amttfbHXGZXNN9yOtxUwhgwte1d6q9BAiBOw0e+WNYoUedIh/szUFf/3iaLoP32/Bgy3bFrqlUU6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "This release is broken and missing 'react-redux/native' entry point. Please use 3.1.2 if you're on React Native, or 4.x otherwise instead."}, "4.0.1": {"name": "react-redux", "version": "4.0.1", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.15", "react-dom": "^0.14.0", "babel-core": "^6.1.20", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.3", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "3e138bf8edd57ef234165387b4518da22f239966", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.1.tgz", "integrity": "sha512-cp0pJgOLI8jevaoL75MJ9nuCheNQrozn/+B5ippME3fVGVLwL3olFy1z3o306Bg7ccvj+/ynBej1Favp+sHSVw==", "signatures": [{"sig": "MEUCIAdelZz47teGOnIkOkT/tvwNbOVdtD5mqjxUMjXHerZUAiEA+cospEA9XwZiAXRqAFM5TaxxCX7N/WWVytSvgw5WR2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.2": {"name": "react-redux", "version": "3.1.2", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"babel": "^5.8.21", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "redux": "^3.0.0", "eslint": "^0.23", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "^3.0.3", "webpack": "^1.11.0", "istanbul": "^0.3.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.22", "mocha-jsdom": "~0.4.0", "babel-eslint": "^3.1.15", "babel-loader": "^5.3.2", "eslint-plugin-react": "^2.3.0", "eslint-config-airbnb": "0.0.6", "react-addons-test-utils": "^0.14.0-rc1"}, "peerDependencies": {"redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "2a888b124e9d01144853612acee19f81d498a836", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-3.1.2.tgz", "integrity": "sha512-+cMUkkZhwW82YawY3uaZc45g3WfAA7aaPJp87lUkkQ7V1DGypfU5P/suNmGiZTO8x8SDyOV8+GEQ2bcEOx/K4w==", "signatures": [{"sig": "MEQCIA6aktSpcp9OlFkHgaIvxypigd0fwpf65bsrerLpTPPfAiBrisrRnCNtzQVX4VlvFkTxzq4WGcEwmkA4aRlEcPe1/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.2": {"name": "react-redux", "version": "4.0.2", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.15", "react-dom": "^0.14.0", "babel-core": "^6.1.20", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.3", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "b3e46d12f861be39ba6b8ffc0458bb7d5c90f3fc", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.2.tgz", "integrity": "sha512-ZW4/iWwvZcoYlYMqT4umP8oIsRNhcnMBUyztNj2rUZa9WbqEEVjJWdSp9zYDmeNgQtdcvAaUBewpfR+rsNd2XQ==", "signatures": [{"sig": "MEUCICwca/D1znjDiDmnp2ymPKs1iM5vU5g47BjBmA8Vho/aAiEAxFq1kISMrXuEQI4ElQ4e3ftzmYqCBuTJc+d6rLRvur4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.3": {"name": "react-redux", "version": "4.0.3", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.15", "react-dom": "^0.14.0", "babel-core": "^6.1.20", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.3", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "d67e6b5f495ebc38a2bb17e8c583a2f0a44860ed", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.3.tgz", "integrity": "sha512-yD7uTnNqJnuD8vEOYuVb/puJv1elpHuP2K9INudr8ykhMwAg5MWa4ZDRYbJzf5D6uYoe+u76PE7PYu31LbbDZg==", "signatures": [{"sig": "MEQCIFEhqp/7AUB9YNWEoEF/gHG6q9r5DlXfd5j6eWJgTx0YAiBNkrN4atWh1B7hDEE71PDIS60NbYmzwS18Yo8v0kFhMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.4": {"name": "react-redux", "version": "4.0.4", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.15", "react-dom": "^0.14.0", "babel-core": "^6.1.20", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.3", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "f4fa72b97b57ecabb174349085a2597fb82f5a9e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.4.tgz", "integrity": "sha512-hc2gDfVWPkIMNSka5dLP4jjw6zZd9Y6PGrG55qyXFCtJQu1g7ki5AE4tkNVOHYmo3Km8lu1PQwQY/I74huTaTQ==", "signatures": [{"sig": "MEUCIH//qgoF8nr0CUxjsWzQxDzWMv+rTI3EUhVcxr4CKQRbAiEAqb+D761kpVB4thjZ99bpV9xOOHz8Cb80inYympgNqlM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.5": {"name": "react-redux", "version": "4.0.5", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.15", "react-dom": "^0.14.0", "babel-core": "^6.1.20", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.3", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "057979781a3383b37eeeb3c017e083b7c8986a5e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.5.tgz", "integrity": "sha512-L8p0Pelt6maR8XuW32gori+cJ50SiCmA5yGvUWmcM26BQBmJnNzAyUAEQLSmwIoAEpJmYrbtZF/lBo2P3jO6jw==", "signatures": [{"sig": "MEQCIE1SBm/wQDpFKo1552k0KQs0g6QdSG9TbbxtTDoXUuZYAiAE1l/xfBJPHD8+3x2uJ9LYrgVouIO2+X0T/Um5jIHXOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.6": {"name": "react-redux", "version": "4.0.6", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.4", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "083ce9eb632cf73a5eb9cb28df77147a640e6245", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.0.6.tgz", "integrity": "sha512-bH<PERSON><PERSON>rerZEkMuPu5j8Cg5uAxZWSpcx119cA67ak9LsCZG5E5CBnJZBuprLPXtRGQgRaT29hsrNSydb8k889qXAA==", "signatures": [{"sig": "MEQCIFmzmngIpHHISicowT+EOr6yXYWrRsVGoPVL4dDCGINsAiAGhNShl2z0pZfU+cKqFlW1HpdwvrqSqDXvk5FSp0hEdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.0": {"name": "react-redux", "version": "4.1.0", "dependencies": {"invariant": "^2.0.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.4", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "8286cabeebf21e2171f83eef4c40b9cda009ed7b", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.1.0.tgz", "integrity": "sha512-/yNO09NKlT3jzOYLC7tDOfoli2CmOnyPbMselOehGUSPCGpA7oYKRvmcHUrRm/Drzt8haIaM4nG4yLLP6klD3Q==", "signatures": [{"sig": "MEYCIQDjq7V7kUQwYo21xorABEiq7ZN+/reLfKxszcyD0JPToQIhANeDcLInGOHJ1nFw3Y6ORpnsE3DOPXycAYfMZ5xdJADy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "This version breaks the build on Browserify due to a missing loose-envify dependency. Update to 4.1.1 or newer."}, "4.1.1": {"name": "react-redux", "version": "4.1.1", "dependencies": {"invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.4", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "9a0fd12aa22b8043cd82a19415b17fddd2e18b53", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.1.1.tgz", "integrity": "sha512-rWLv5WO12K/sx28V/GAkwA8LlcljKL8/8Pa4zwU3zXVng4BfzVhsT0dLzActvfU7+0Ri/DqcWnuOSSZ9iKeB9Q==", "signatures": [{"sig": "MEUCIQD0vcGsFaVLX3qYo18fF40aORLL2dnDKquBC6gxd2DTCAIgNHLvfG8gZVA61DC5wmlau4G9J4o82JdGS9dylNmlVBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.2": {"name": "react-redux", "version": "4.1.2", "dependencies": {"invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.4", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "a8f391c4361e27dacbfde69d034a3b1ad2ce0c45", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.1.2.tgz", "integrity": "sha512-24rB2xpQnA+qrAi6nuJ7kJhXkF84NEzQ9IrawBVzYbWILjUMqpSpEHgHLxjpVaiIn/j8rdRoG1Z7cQM6V4K3KA==", "signatures": [{"sig": "MEUCIDrFndzIMGRT8xrc2QVOcY5obYuG0h1d9GcgmyY0Md4AAiEAq9pK7wMBUcRddhEwcTK4X9yX4TpP1/UiPlNAAG6jCVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.2.0": {"name": "react-redux", "version": "4.2.0", "dependencies": {"lodash": "^4.1.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.0", "babel-preset-react": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-preset-stage-0": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-preset-es2015-loose": "^6.1.4", "babel-plugin-transform-decorators-legacy": "^1.2.0"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "e3538426dd479a48ae23393d0c73570eb7ce5a3f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.2.0.tgz", "integrity": "sha512-5is9+522UA6ZuVRTvn+JKRzhqWUcjhpvTz+6naRfyE3RnF9bDmQ+6M477jgnqWaBuFCmjWcejaTiqbu+RJScug==", "signatures": [{"sig": "MEUCIQCxWzWMw+3cJM9MXIEBulzHx9TyFkBXgGuDUHLcVxECsgIgGjYio/sYLpmxc4x4EZ9hIghKikyTN9WI1q1sQLEihgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.2.1": {"name": "react-redux", "version": "4.2.1", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "d651a9cf98e07fbec2b41adb83bc0a92c37f08ee", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.2.1.tgz", "integrity": "sha512-F0I8le2VefbIUEI1aP+Fxz5PKly+JTj++AshdRcke4Kx3wW0i/EgcCQuf5eWSRYzchR0Tyi0DFB0E70SoZbbQw==", "signatures": [{"sig": "MEQCIHNpyZc7laflHDzw/JvIVUKT85uixLRZAgyPAmX434w6AiA+Vk3jxTHVStb1Ac+nAyBoOXcZd/QoIPxBgRQDuVQqSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.3.0": {"name": "react-redux", "version": "4.3.0", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "ff7cc13687755891cc05e6cce3ea74ee42099ccd", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.3.0.tgz", "integrity": "sha512-Y778gkiYCSGJkkmFcmvIGVPKl/5k7YSDtpTwtQVIigKrevi+e8p8380eTvF2Xuuy7VCCPIsZ5C1LsGyaMlFCJA==", "signatures": [{"sig": "MEYCIQC4sX9PcpPfDzOHt3tr2jOIDlYiOwx4slMLHMhR0UveSAIhAOyZiIyLNz+xXfe1e46lVdOCmS/n+ZTSyx88CRlUC3bw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.0": {"name": "react-redux", "version": "4.4.0", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "c272eecb6552a077f91a007ec08880ae1748dd7c", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.0.tgz", "integrity": "sha512-SNL4cwt1KYBqojAWPNhnKb0++fHyBl2kqrMeAS3peY3PqKcoohQORQk/Hgb7n4XIxZ9yDzjUyzCxzS/95cbZQw==", "signatures": [{"sig": "MEUCIAQSmQP6mzbcE8K9E/bb/v6CgdTB6nJRSyT2PjzQ+NpYAiEA3y3QGdvLVNpkMfb2iCAMN9iQbR9QN9dJnz6hYslqG9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.1": {"name": "react-redux", "version": "4.4.1", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-rc.1", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "f0be889d3dff0b9caf9f4e31fdcc0d0b481aa311", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.1.tgz", "integrity": "sha512-menbOuArFoVErisUaFb6OXvc5px28g61sY87zC8uycT5J1H9TM6ECK2kK4x2uyJ8gZ9qeDshJ44rkmDGGaczjw==", "signatures": [{"sig": "MEUCIQCaU3CzBvnCBsgR4OYKn+RRw0rccVm9iUZi1UIpm+rj0gIgGvd8McVy1JK5gQvyL6qXsqR/zxBlncLlu22E0vCj5Pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.2": {"name": "react-redux", "version": "4.4.2", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "963486fa0fbebe50b8f3012cb1f9179ce29973f8", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.2.tgz", "integrity": "sha512-zn209qP3mZKXGQl3Ues7mDbswjHI97OqSiqZD6IYLrUNy3/0+j4mhc7T4q4ecbxpAESM7V4D6R9HCNl671tbsw==", "signatures": [{"sig": "MEUCIQDyoJVvouxhrP5raVLXACdPhQV5/oIsnVwL1YTe+4sifgIgUfqJZmDuLRTqvjZ6j1/P25rBFuX5r7Xau1cQ5OtpBAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.3": {"name": "react-redux", "version": "4.4.3", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "5fca05f00110562929fcf0fb3ce4a5c493575c08", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.3.tgz", "integrity": "sha512-SzOSts8AALPkCoU0V9L3BrJ/DhXLsiUmGyWL7h9TY0hZiAobE12jHtLEfgM46JDleGa5waopM8mVGivUwEwbtA==", "signatures": [{"sig": "MEYCIQCyUatbE8Bve66aNL1ktPa/aymkY1e1e7QlxklQHQXttQIhANOkvbSsN8u0rbLL24M6sAfFHoahSWXZjf5Y0KPPu6lG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.4": {"name": "react-redux", "version": "4.4.4", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "3b5aea75c852ed6d7ca28840e0fae697e1f95a69", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.4.tgz", "integrity": "sha512-qT7O9VU7nDGPhOZe1X6liMi2AWBS7UR8QvbCLLLl/0OKoMvkaFcGUs9Pkvf/eea+6jpHZTB9RfZfpHHBfu4jiw==", "signatures": [{"sig": "MEYCIQCxsUyGM4aP518+nRskZkUyQLduuE4tyCKZR9EU4JgqEQIhAM8kp7B5NCi9yat+PtmlG6FHcJX0UIkRyX/w0P+ekaBo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.5": {"name": "react-redux", "version": "4.4.5", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "f509a2981be2252d10c629ef7c559347a4aec457", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.5.tgz", "integrity": "sha512-iE/O/i8XkBWZjHm2MdXDZYL4kUnwyaj6iVnc3xzzmnJEk7cQYPp+zExN8gSnlO6X53ST1SfoQ4PJf5aX7PNZ+A==", "signatures": [{"sig": "MEQCIHKmcajfC/XJ4dDo1+yUEDjy1DcuNUDOgyldJ4ejDwFNAiBOOjqx9fpKq5CP7OzZ7wHyttt0r9NRq59xZIrYVxRQOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-alpha.1": {"name": "react-redux", "version": "5.0.0-alpha.1", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "0117b7c6a63952c96d2cac7e4793ae8d0d9e74f0", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0-alpha.1.tgz", "integrity": "sha512-P0R7IbhVwZbii+FXClv7tiQIePiWXn6uccjpZvir9hVE7jgAcRZ+tsvProB9jozGNnrm6NXZ8Ay/6oDunQf7eQ==", "signatures": [{"sig": "MEUCIQCYgRWR26CMTTWH46YbYy6xKvhqsQOVgnq7Ylwjt2v5dQIgIQqBexy+mkDWM5GKUuzYvstp4mUfuUXvnIRWcGf5J/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-beta.1": {"name": "react-redux", "version": "5.0.0-beta.1", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^8.1.0", "glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^1.13.0", "babel-plugin-istanbul": "^2.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "59fc0e07abdd8c5aa04b218024e9132af923808a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0-beta.1.tgz", "integrity": "sha512-nbIvVX+twdDcpJSNVjzjkMrDp1I5e/aThUxgkTYYVSyY8OA6eTzY/fE4wy63Q6swrHHtTBjBs7aWsxlQAGAp8g==", "signatures": [{"sig": "MEQCIBT53inXY7b2wdj86nm299ghyLaJBxfh5MIIsXhMCcM1AiADEOT2+XogSWEkJvRUMWTnQmR/NPXcN2MHJA/nlXqGLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-beta.2": {"name": "react-redux", "version": "5.0.0-beta.2", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^8.1.0", "glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^1.13.0", "babel-plugin-istanbul": "^2.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "3ae2fcbb65f05c0427127b80090c7d27c3bb283a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0-beta.2.tgz", "integrity": "sha512-LzkmF0TC7kmkfURl46E9IBCo9hnFlm+A05TOIMCqHon9rx5p/nCwIicSWKfAlk5uEyIHp4wk32AH1bs/8FF22g==", "signatures": [{"sig": "MEUCIEvHYzTXIJ3VG44KnLV7W0xtjDhpQ8Vb3S/SCEmVflo+AiEA/RYGiEzsazbA474eBVjdrOZxKBF8LwsyRA9nUHcuXBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-beta.3": {"name": "react-redux", "version": "5.0.0-beta.3", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^8.1.0", "glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^1.13.0", "babel-plugin-istanbul": "^2.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "d50bfb00799cf7d2a9fd55fe34d6b3ecc24d3072", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0-beta.3.tgz", "integrity": "sha512-vriUVk3tqsz/CrY2ZUTICFcrwgWAlLu+wfmiIC1c1V21Y8pWCFA1PUzHMDbzhKhph5kAVB0o3LQxQC6w+do+Bw==", "signatures": [{"sig": "MEQCIG1QLhgF+MrSbEsMD0TDVeNz6PMA7RMkaNGXI+cHL+fRAiBB9ztqJCz7Ab+zdlHVSwjEzadZxob0m8CakjT3R9M86g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.6": {"name": "react-redux", "version": "4.4.6", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^15.4.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "4b9d32985307a11096a2dd61561980044fcc6209", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.6.tgz", "integrity": "sha512-ejQK8m0d/laf5ArGG0RRuL2ycOnNijE07sRQyjkDTmTUyCf7QGUtQj5Mjy/pam46P9fyUaZz9UsGXaEwi9OFZw==", "signatures": [{"sig": "MEYCIQDNWqqSwu/JqS8auDBrBqSDnu/Q4EZzd5Zd+VJXgMqijgIhALMZhJY4LqynpNqVpy5SNFdPCVy3/+SlM2BfD5vv5Xl4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-rc.1": {"name": "react-redux", "version": "5.0.0-rc.1", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "0bcbb394ab6bd96bc21685af54a9f7fec0fa80e7", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0-rc.1.tgz", "integrity": "sha512-+iLvsscg7bO+bS6KLmOdONBAAw2FmR6XySU1WVmbUo9vz/e1ll0LE0WoTkgtsIfWBm/bZvhm0JBNNKBHHQgiVA==", "signatures": [{"sig": "MEUCIQDeYqAK6yzxYH5Z6+eWyV6j+Ho/l1yTdUNI2AiYcgbW1AIgNJKzqfO6frlAuF/M/Ak9rPeeNyaAJoGBoxg67kcelZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-rc.2": {"name": "react-redux", "version": "5.0.0-rc.2", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "a2e228c6c3e66581e9e710e73684c4a8ccc6a63b", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0-rc.2.tgz", "integrity": "sha512-MWxmw3opjdarLIas6J969+yivAqdUnz1FOIWZxtP3/sfzMObpLYQVIxmy9vWrfptRvUU//reDZjetIZijNeQmA==", "signatures": [{"sig": "MEYCIQDMmwZyfca3yEDpJZJcBpiJ9P9vBUFdhgenYDIXk3CjbwIhALMnlV8RDKmkXGbN0Xu/tj4/51VGOyokFSeY8C0kwU+O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0": {"name": "react-redux", "version": "5.0.0", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "2693227b19876b6e0047f4b94eb4910f43b677ca", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.0.tgz", "integrity": "sha512-QyEfhZGkRtTEzue1aX1CsXlsQlyXwEnMMV8/FD1tADii6rkBT6pO9cZAbYicm+8Ld+jVvXAVrDXem6K0kqEmkQ==", "signatures": [{"sig": "MEQCICQ/MwGsPG8xDtecOQVZ0tX+UVPLpCrZEbX/G/Gpb1hNAiB7/vsp2PEt2WZoY6vDDoan1f+lzUj78qtq0UEYxraTXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1": {"name": "react-redux", "version": "5.0.1", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "84a41bd4cdd180452bb6922bc79ad25bd5abb7c4", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.1.tgz", "integrity": "sha512-Cxr5kWCy07/XboZR1UTUcp/Br5HbXM+Xr0vvbCifUclaDHT8cHTOrwjp8rfJQMB7HndWly+cfq9dsyBDg8bDIw==", "signatures": [{"sig": "MEUCIFLb6Ho6i7Ky5Gm51GWTMeqUw50zlPtZ3tC2Hji/af3ZAiEA+yUty9RVy+PNEFC6/b+rkd7/DkOjC+5vbS7JcXhBCB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.2": {"name": "react-redux", "version": "5.0.2", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "3d9878f5f71c6fafcd45de1fbb162ea31f389814", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.2.tgz", "integrity": "sha512-+/cCgchXN2Q1qbHSMRpQnW/uIr+Rg1xxLvPlFjH4QXGIWzeACoF1P6GsKnAunfCLUQHBPGDx3La0ZxNj60jI0Q==", "signatures": [{"sig": "MEQCIFCAEWTh5vLyQP0jmLKdxRiWWUE9Hp5xQn32b1e/06Z7AiAH51INz5SXpAifl10HQgqFKYdlZ11ndtAgG3nz0ZfiDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.3": {"name": "react-redux", "version": "5.0.3", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^4.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "86c3b68d56e74294a42e2a740ab66117ef6c019f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.3.tgz", "integrity": "sha512-XNguUSFvjBcti5d1vWvLURETiSLPZW1+ugveIa38SgD3aczVqwENNjjC2jtBRMBOziJZDsnmneCLB+I+pkLNTg==", "signatures": [{"sig": "MEQCIHea1ah+noKvhNit3pykHx7AFmeHU7XFQRZILy6dVENkAiAnHejpQJXowPLu5LegnCtJ/LG7+p1nMm4WePyZ+Uav0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.7": {"name": "react-redux", "version": "4.4.7", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^0.14.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^0.14.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^15.4.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "74fbea055e81591aacb14dac0dfb80c6428424db", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.7.tgz", "integrity": "sha512-aXoINF4pIu9FivWVqOlJ0J9q8I/JR8pz+bjE6cI+lIg2CQGmvIeDx5gy0/oVoeDkG1Wl9+X/fLC2QDrgXEzKZA==", "signatures": [{"sig": "MEUCIQCKBrJUJyeIEtu5og4rE4a12eWuMb8ZsvF77oPP052BtwIgE5Mjnh6oSP8V9xfCBMZr5JfIn8apygREWcCHOPDSKfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.4": {"name": "react-redux", "version": "5.0.4", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "prop-types": "^15.0.0", "loose-envify": "^1.1.0", "create-react-class": "^15.5.1", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.0.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "codecov": "^1.0.1", "webpack": "^1.11.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.0.0", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^4.0.0", "babel-plugin-syntax-jsx": "^6.3.13", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "1563babadcfb2672f57f9ceaa439fb16bf85d55b", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.4.tgz", "integrity": "sha512-svqdv7Yl6KM2bjOTRBGF73XRNEbOHGlvlg9hoDEe/rZJGUAseHuTLfJsXY0Pbb+ZQBQ+5ZPRVsf2tauNts4ujg==", "signatures": [{"sig": "MEUCIQCE+1ZmZca+XLqvaVIE+xtCFghnpwezvrgS1npfzTyYPQIgNud+g37jQCIcGBV/8/A0C4BiuxaNrl+Jq1mouiM0Q3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.8": {"name": "react-redux", "version": "4.4.8", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "prop-types": "^15.5.4", "loose-envify": "^1.1.0", "create-react-class": "^15.5.1", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^15.5.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^15.5.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^15.4.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "e7bc1dd100e8b64e96ac8212db113239b9e2e08f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.8.tgz", "integrity": "sha512-VRWfz+wSPQmnTaWXL+Y9GNA3mc7Pjb9b9IKp2kDMZNmcwigebgqdU4q2dn7wmE08iXr9hirwXGV4ONsBUKY6Dw==", "signatures": [{"sig": "MEUCIQDnK7yO2z9XhkZG7BwC8zzRk7C1ebBxg2uKY9rBCeH/4QIgbdvNtTl/IAD6T+yB6RB7Z0vrlqLr7kAsE0Tw7+4svZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.5": {"name": "react-redux", "version": "5.0.5", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "prop-types": "^15.5.10", "loose-envify": "^1.1.0", "create-react-class": "^15.5.3", "hoist-non-react-statics": "^1.0.3"}, "devDependencies": {"nyc": "^10.0.0", "glob": "^7.1.1", "jsdom": "^9.8.3", "mocha": "^3.2.0", "react": "^15.5.4", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^3.3.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "rollup": "^0.41.6", "codecov": "^1.0.1", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^3.1.3", "react-dom": "^15.5.4", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-register": "^6.3.13", "eslint-plugin-react": "^6.1.1", "rollup-plugin-babel": "^2.7.1", "eslint-plugin-import": "^2.2.0", "rollup-plugin-uglify": "^1.0.1", "babel-plugin-istanbul": "^4.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-commonjs": "^8.0.2", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.0.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "f8e8c7b239422576e52d6b7db06439469be9846a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.5.tgz", "integrity": "sha512-mVVhT54f3HCPkq2h68gHw3q58DZ6uklqnQEKYtF44k4wRQNRwJhfFhM7Si0ryBtFKH7gsO1VjQ9Paac+bORlTQ==", "signatures": [{"sig": "MEYCIQDCIkJQMa7OC3KXtR6SL3cj+XWq7lmNuDUgHAivwuLNQAIhAOiOXgH+3l3v7GgXdqhp/vcpD16DYMUzGlFywzStrU89", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.6": {"name": "react-redux", "version": "5.0.6", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "lodash-es": "^4.2.0", "prop-types": "^15.5.10", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^2.2.1"}, "devDependencies": {"nyc": "^11.0.2", "glob": "^7.1.1", "jsdom": "^11.0.0", "mocha": "^3.2.0", "react": "^15.5.4", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^4.0.0", "expect": "^1.8.0", "rimraf": "^2.3.4", "rollup": "^0.43.0", "codecov": "^2.2.0", "istanbul": "^0.4.4", "babel-cli": "^6.3.17", "cross-env": "^5.0.1", "react-dom": "^15.5.4", "babel-core": "^6.3.26", "babel-eslint": "^7.1.1", "babel-register": "^6.3.13", "create-react-class": "^15.5.3", "eslint-plugin-react": "^7.1.0", "rollup-plugin-babel": "^2.7.1", "eslint-plugin-import": "^2.2.0", "rollup-plugin-uglify": "^2.0.1", "babel-plugin-istanbul": "^4.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-commonjs": "^8.0.2", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.0.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "23ed3a4f986359d68b5212eaaa681e60d6574946", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.6.tgz", "integrity": "sha512-8taaaGu+J7PMJQDJrk/xiWEYQmdo3mkXw6wPr3K3LxvXis3Fymiq7c13S+Tpls/AyNUAsoONkU81AP0RA6y6Vw==", "signatures": [{"sig": "MEQCIBU1XOLD14GWY3O771oI5szxH64bbj72gcu7TAIzQKj2AiAo/wXYJVE33ecW7ucsatNHzvG/OM6ckYalKXf4BodMaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.7": {"name": "react-redux", "version": "5.0.7", "dependencies": {"lodash": "^4.17.5", "invariant": "^2.0.0", "lodash-es": "^4.17.5", "prop-types": "^15.6.0", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^2.5.0"}, "devDependencies": {"nyc": "^11.5.0", "glob": "^7.1.1", "jsdom": "^11.6.2", "mocha": "^5.0.1", "react": "^15.6.2", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^4.17.0", "expect": "^1.20.2", "rimraf": "^2.6.2", "rollup": "^0.56.1", "codecov": "^3.0.0", "istanbul": "^0.4.4", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "react-dom": "^15.6.2", "babel-core": "^6.26.0", "babel-eslint": "^8.2.1", "babel-register": "^6.26.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.6.1", "rollup-plugin-babel": "^3.0.3", "eslint-plugin-import": "^2.8.0", "rollup-plugin-uglify": "^3.0.0", "babel-plugin-istanbul": "^4.1.5", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^8.3.0", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.0.3", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "0dc1076d9afb4670f993ffaef44b8f8c1155a4c8", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.0.7.tgz", "fileCount": 54, "integrity": "sha512-5VI8EV5hdgNgyjfmWzBbdrqUkrVRKlyTKk1sGH3jzM2M2Mhj/seQgPXaz6gVAj2lz/nz688AdTqMO18Lr24Zhg==", "signatures": [{"sig": "MEYCIQCNu8RIjLRwE5VtihYOJm9zbGdcXFJCuZI8DnwdAhkUoAIhAN+Hrp5BcNoyDTsWy4Gk9O2wLaRnSrbWrIt6ZfwWX6M8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207382}}, "4.4.9": {"name": "react-redux", "version": "4.4.9", "dependencies": {"lodash": "^4.2.0", "invariant": "^2.0.0", "prop-types": "^15.5.4", "loose-envify": "^1.1.0", "create-react-class": "^15.5.1", "hoist-non-react-statics": "^2.5.0"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^15.5.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.3.4", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.3.17", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^15.5.0", "babel-core": "^6.3.26", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.2.0", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^15.4.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "8ca6d4670925a454ce67086c2305e9630670909a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.9.tgz", "fileCount": 20, "integrity": "sha512-3XS7mjTOcvaP2H5OE/LxEgDHRuEyTZxBRlwvXHzNqYkZdYd7Ra98AimWoDSHP9OcLoydjA1ocgiZxxcqeXj0Sw==", "signatures": [{"sig": "MEYCIQD1qnvWvJOiEbrV485ply4FLLd1TIhmHIOXGGegMd6lrAIhAKbOTw9lD2jAQMrM6mAAKcPw34GwcEApmy/OgZ9l8hvc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119998}}, "5.1.0-test.1": {"name": "react-redux", "version": "5.1.0-test.1", "dependencies": {"invariant": "^2.2.4", "prop-types": "^15.6.1", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^2.5.5", "react-lifecycles-compat": "^3.0.0"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.1.0", "react": "^16.3.2", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.61.1", "codecov": "^3.0.2", "babel-cli": "^6.26.0", "cross-env": "^5.2.0", "react-dom": "^16.3.2", "babel-core": "^6.26.3", "babel-eslint": "^8.2.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "react-test-renderer": "^16.3.2", "rollup-plugin-babel": "^3.0.4", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^3.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.3.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "921a60c8beba4a60f4708c25bdb80f3494c58cf4", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.1.0-test.1.tgz", "fileCount": 57, "integrity": "sha512-K5Msy3QI3wNr5JDvwGHnxF7YVr1+kiwd4Fr78LtFyQgW87AV2BjPa9LhUx7VP7ExuQJLd7AnfCiVrlqgeyOCcQ==", "signatures": [{"sig": "MEYCIQCEMLDy8IugHybSC9TPRWk9WBivdf+ugxCYguo4Gr7tiwIhAO4t9Bwn/BJEj1o3fOOLCvDV3zJWjDibUfqLxCQC3gDS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLAfbCRA9TVsSAnZWagAAsCMP/icZOKAGYUyaAVgG/twV\nfpvqBx7ZQI5RYte18GPDf7Q85AtM7LvvdEGU3Ag7aXXR5n7eC3WwvP+KjW77\nUR8LzCws6ulVzBUIuTXF8Sm9N57SVbE4H3LbEGOl93JOyghgHOPvPM2A4QDL\nghWOCwV/WGzsTP63F9LUcsBRNaFtpOj7BLd+WrE/SLAaMD5Ix2cB8ZQ9ey3Z\ng5jAU0TrupVCpVSmV6YGlOLSSGEVyd6fwEVPHA8ZqIectXjEdyzlAgOohBch\nFeUGarlf8zXYm8qv9S2BXscaRmozWdpW6ZcwiuDAeuDsjT8bSeHh62E+FkIC\nAyRxcD4BrTsBWyD7cDYHg6hBxiLz7BdzVgzJYix9NtdyWE9L/x45172wbaw4\n/FQii4I6b23XPdVGlMk1tW5xVdvPB4Cv93iW4Nea9Yhgkm3O86qy5dYLRKAR\n0MYSuOx1nwcvUMQ9piIRkO2hHvwfxgjWZM4QSdEvCAv0IBk8sKiZi8SGUTE8\nhHTBP234ByTKIPQ48v0bCY2rQ6GrM0MuUpBcHDXxNetBZuqjmR8E3Wj6qFXi\nFBmVvvxI0WWr9IRfcxXgHwzo5Eg06wkYCNOw0apg/1A5hWtetRFAEOQb6LwL\nS54lWZeyfwV5exWGKOY+PEltP17bY4qbRBIM2AwaC8HAYsIsomL4YKEEmn/5\nJ1Va\r\n=tqQd\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0-alpha.ede6245": {"name": "react-redux", "version": "6.0.0-alpha.ede6245", "dependencies": {"react-is": "^16.4.2", "invariant": "^2.2.4", "prop-types": "^15.6.2", "loose-envify": "^1.1.0", "shallow-equals": "^1.0.0", "hoist-non-react-statics": "^3.0.1"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.4.1", "react": "^16.4.2", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.61.1", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "babel-cli": "^6.26.0", "cross-env": "^5.2.0", "react-dom": "^16.4.2", "babel-core": "^6.26.3", "cross-spawn": "^6.0.5", "babel-eslint": "^8.2.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^3.0.4", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^3.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.3.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-react-remove-prop-types": "^0.4.14", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^16.4.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "4c651e6520e253d919b8c682a6ad5b26d7cb9d6d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0-alpha.ede6245.tgz", "fileCount": 54, "integrity": "sha512-3hi0+xl54ahyXQhK+i1axn9o6kk7//UuiCtXnGM/N8yn4Ygsnx28it6AvFvO2YFyTf0epMhgDhyV0UcsNezgqQ==", "signatures": [{"sig": "MEUCIQDZlFJAATLrmT6dMSJ2Dzm4yC6/TcmynvacnRNnsjQg4QIgWt5TTWVlPwJh100RH90y+o58cphvgm3KwKRb+7pZJgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbowT+CRA9TVsSAnZWagAAZR0P/2ZiI3paLrZ/jGC1iMIo\nCqW3DutMhjG6f1aY1yJTPSJKMdVYsWqf0qltvuDXgoEDt71hz9T3/QIF+5f1\n0EhqGP4KBvq5PG8yIjtN3NpX333xAWTeKwuVNThEaFMV1I1qsFOjT68OQ2ty\nyl8WdrP/cMTSVn1dYyNWmRvevFuXle3crqk+ssDSpQU7oEwqcL8m+NSlfYQs\nUKhXP5qmwYdKzl3P9oAvU99sJ1J82EBUiEw4t83kaNExpZaiVyRJQiFMRpbr\n6B84I1BVUPhVkFUIB5z5MBU0h4AB7oX+LoqQvVLBthj14RkK9UbkQNlzm+HE\nIdKWeBbVFr8bY7SlvfufEIfJhCvsYlBmjFiJq1cs176STk/FHWPgopDsR47u\n1DHgNft4bEauVhmxbdBI9VIWHaFf5ZiDebZwqqv9IL7PlILq6CEvDdeFrWWp\n4iCfTppnBr3WK9WsDvWJ+A576V25WCXnNURuT4Cf1PCyzZ/CUnBCKN9YX7cy\npub4S4oCigy0N7ATTQuAixvoip7UrL5KWkQNoNde5x81ukVs988HQzDzZki3\nII7zwybtMps5dB7rXJslebXuN+UVduZnEbRijzsf4dIWbuS8/3XVtvRDQGsl\nyjg+j5oRXtGU96V4ImWXa4LxhPh6HRYpDk//1F9Fhz/3gPf8ileQhn96sxq1\nzPxV\r\n=VCR4\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0-alpha.9210282": {"name": "react-redux", "version": "6.0.0-alpha.9210282", "dependencies": {"react-is": "^16.4.2", "invariant": "^2.2.4", "prop-types": "^15.6.1", "loose-envify": "^1.1.0", "hoist-non-react-statics": "^2.5.5", "react-lifecycles-compat": "^3.0.0"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.4.1", "react": "^16.4.2", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.61.1", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "babel-cli": "^6.26.0", "cross-env": "^5.2.0", "react-dom": "^16.4.2", "babel-core": "^6.26.3", "cross-spawn": "^6.0.5", "babel-eslint": "^8.2.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^3.0.4", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^3.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.3.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "58c97ad3683dd391bc31d7bdf823f229363972ba", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0-alpha.9210282.tgz", "fileCount": 57, "integrity": "sha512-QcQNqfVY+5P66HI+5CrGB5v8rDxjNKpZqCd7OyWqkq+oEhXJnRsWZyhq8M9rTyfV7/Lw732YUgKZ6OxX/MQwng==", "signatures": [{"sig": "MEUCIC1o+btUu/13jYXspYmP+bQl55wT3M+lHEi+JdTdUhnFAiEAwb20dxwrJ6L+wvBHlp+hpxYWFckgJZgqT1WL+X0qUUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbowZUCRA9TVsSAnZWagAAlzIP/35YxsfaYhlHjwGLFth/\nRB42BXBXkOoIETK0/LnSzMUkr3D18izrlhCnZIF8VzBogKgRNI7C813DZ1fG\nPMBiyYWPMtQlUbdcL/06CSIQGWbmbk+MnUWwcpM2/QMNFAENmIeqbCit57b+\n885cvaStuv+qQMtud2US5FDw45beci4B414O9KzzD4dXJBC3Bky9YkpsuVfK\n4r9vDajiC2/y4L9tD0P5Q+kErdTvGWC5VGGuuXzXQQ+WoDtGudWwkbvWD3H4\nc+G6zw4AuelgbrmCYjZrrYDsF2I/enNN3xGGSG/2FPDomsuDeAnpR4jmcNF4\nvmhDazyMgUIFJlJfuDqhMR0p8x/0goQQwAtuEkB/2tqBdorGfvN/XeUy0I8e\nQicN/Z2hGz1cxqBwcZktKBorbGnyQgoZBCBU1rSE0/G4bZ5t+ZBmIbvX86jZ\ntjLJAmD4ZClztABCy3ROAuS0+QRJe7SnvrdtBxA10+89GHaiftmc4LOFtGiW\nSKjetFFG/Yp9OpBos/Fs4gFuTWggGYNJCDuQciaS94voO+5UE4Fm/p6IQYTW\nB8yZhYSEABpMSJFial2wDFoKxHp/TtsIxoplUJ451m7n3j0RDTmWs8w3h3/l\nw3nSJ2m2m3zMItKOF97iSCsHE+dMI1F5Uv+kAGJbx7XhAiiLt3oL2xR9d0kl\ngUYa\r\n=fo+i\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0-alpha.2a2f108": {"name": "react-redux", "version": "6.0.0-alpha.2a2f108", "dependencies": {"react-is": "^16.4.2", "invariant": "^2.2.4", "prop-types": "^15.6.2", "loose-envify": "^1.1.0", "shallow-equals": "^1.0.0", "hoist-non-react-statics": "^3.0.1"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.4.1", "react": "^16.4.2", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.61.1", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "babel-cli": "^6.26.0", "cross-env": "^5.2.0", "react-dom": "^16.4.2", "babel-core": "^6.26.3", "cross-spawn": "^6.0.5", "babel-eslint": "^8.2.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^3.0.4", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^3.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "babel-plugin-syntax-jsx": "^6.3.13", "rollup-plugin-node-resolve": "^3.3.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-react-remove-prop-types": "^0.4.14", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^16.4.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "c8ae15b62bacf07533020455df83e03fd0078d68", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0-alpha.2a2f108.tgz", "fileCount": 54, "integrity": "sha512-ftD3wvjV48z1Om0uDHBJNnKeliPTryBarRcI+j1vAPB6Yka/afze4ZNtBjqpTzA326pWwGUQPRfZyj13JBZSKw==", "signatures": [{"sig": "MEYCIQCCCr+MYq69zpDXVSt3FVtQrxiOwQWv5l/L753A/PooOAIhAL0Mbv8Uni6U8djlPz9bg0GHXhiiTP4EmCAJ2kh/Tu05", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbow/vCRA9TVsSAnZWagAA4BkP/RTlsb2ZIgvhpiQuijwv\n5Ze3MWQpufhOInxxIGFy6J6UfTd/fLV2UyHpGiZrnDmAzMGLsBvI2wZMr/5L\nRPe8P8ruk4MC28CcuVKjjb1mBKeJmxFRS9oCoWCD1lZKfy/9lM3INrO9JLUz\nCl9vDN/J4v5m4j6kw7jos8b/g7Jb03Pmxf3qEo77RtlXtO068oE9u3bvmO0s\n/BaTJmE4rEGOodzJopC0gxHrEyTtGRAuNRnetKvoCt3atFjqFgTvOfTrmZbw\nzrcupdVQmTBxPTkC3EQjqAQAR3QgUCMJ6Tinv0ukuj+CHrML4erhM9m2KX+T\nSiYMmSaMN+z9kOMjLXBZQmsxGJPU0o3/cCG/l8Jto8DvpIRgKqhyY3svaBf6\nMpOmk7cRGhMIbY2TuXuGgNLTkWlABX9WnuQ3P1m36jb2FwSN44m451uCb+us\nd56dujmtqml986Tyk3i7DnvMtC2XAHVIKsk20lfCVN4EX+/awqNn3bEsSW3/\nMEvXnm/dpOp3borqEGROyeDhW370VQ06aYg966GtGXJ/GaMpdvym0sLx98JB\nW/9crtmmWhrhBnYmD0mzJS/hMRNyE/SCPddQ59s1GnwNC4nIM3wt7VZTnBtK\nCKHCMJ8QwzylRzCtf9MtknTUAt4j5Hzls4qzZZb4UfmMnXiJk3k/ZjwmfKlX\nphQp\r\n=FSwb\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.1.0": {"name": "react-redux", "version": "5.1.0", "dependencies": {"react-is": "^16.6.0", "invariant": "^2.2.4", "prop-types": "^15.6.1", "loose-envify": "^1.1.0", "@babel/runtime": "^7.1.2", "hoist-non-react-statics": "^3.0.0", "react-lifecycles-compat": "^3.0.0"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.6.0", "react": "^16.6.0", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.66.5", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "cross-env": "^5.2.0", "react-dom": "^16.6.0", "@babel/cli": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.1.2", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.1.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "948b1e2686473d1999092bcfb32d0dc43d33f667", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.1.0.tgz", "fileCount": 57, "integrity": "sha512-CRMpEx8+ccpoVxQrQDkG1obExNYpj6dZ1Ni7lUNFB9wcxOhy02tIqpFo4IUXc0kYvCGMu6ABj9A4imEX2VGZJQ==", "signatures": [{"sig": "MEUCIEcLbyVQv9+689GS+uUEAi7w5kDv03+lQ9PCA1fqxkXjAiEA/4D6JBbZiOrtwl9QNVYBNBGbbazS8i7WRiQ9gzg5MnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0icdCRA9TVsSAnZWagAAgWoP/1sZ+G9TIwlswBFHNZeb\nkW+Net3ggPFA8Bux5PH2P737ZAm64gcgzZRYwsC6kNZ6454nfFZrw9zbzK9r\np/xNc1cQU3wZXu/uNAmp29cRLMuxZIciO9w8sZ6CtGlEbNEFYxXPBfJVWLMk\nycNQgGf6OxDtWus9pvqGx5nbUKw4VbJM5cVLdwJ4U1JXdd5fie2iuzjwQHT2\nyOymUwAt6VoyCgErUcQWOaHR1/2ERc0HRGgSzoGikZjkUI4ajIty7NTLwOzI\nvQJVQNzJaxRgFzLqvm9XQ75ILBhRo1pnogXgRlT3JU4aeqcezcNRX0E1LVMP\nLGDtoQaoYcA9/brWVYnxcprlUJATNgjWcXHuzVPBf/9iFB2bLH08QDJTueq4\nW+CXWzVAV40jAeHw9lixyp1LpxP05yzInqDlyXC3MrOaTCaocQfo5UpYBYHX\n364OFugYqQnvyM7meluHCME1Oyz7nq2HJbcASVBQ799uDVT0txvOTXAFjnzx\nEJuXY+wTGUr/3tX6UH9ewQqmwDaKwEWSPmdcUFmZRmIsxpu1rvSmL+55owxO\ncGVXpvszrj02rUuuMIGYSCQLo2mOT4XOvQt5Ice1Q/XxhtiTaokJI5bPkkHF\nbOmb3JkOqn3T1FGyJbyb1gUb6WT7C1l938Q0bV6eSxVd8BDC+qegyszKoL9L\nVqqj\r\n=sUJk\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0-beta.1": {"name": "react-redux", "version": "6.0.0-beta.1", "dependencies": {"react-is": "^16.6.0", "invariant": "^2.2.4", "prop-types": "^15.6.2", "loose-envify": "^1.1.0", "@babel/runtime": "^7.1.2", "hoist-non-react-statics": "^3.0.1"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.6.0", "react": "^16.6.0", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.66.5", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "prettier": "1.14.3", "cross-env": "^5.2.0", "react-dom": "^16.6.0", "@babel/cli": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.1.2", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.1.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.14.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "eslint-config-prettier": "^3.1.0", "eslint-plugin-prettier": "^3.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "peerDependencies": {"react": "^16.6.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "98491de3c8ba836f7546efe1922b31352ad25181", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0-beta.1.tgz", "fileCount": 54, "integrity": "sha512-C3V1IvvAPh2V59amNkfc6N+WWTng3lNpEEWpe16gMgTWcDHiSXKfWAiJrbb1YqQwB/6qfeI09EBBIX5GJKz7gQ==", "signatures": [{"sig": "MEUCIQCaJ+qvaQferrnRajgWKiIAv/ozB00rGK8C7aj09vEMQQIgUnlbY8aEuWXlFg4Y/JZ0eISftHQQNSCWS8SqI3PlYFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4gpACRA9TVsSAnZWagAA9AEQAIHxZLm6erdLDeaZHNuD\nWeUVy3raxRfrYIyEzH+mcmJRzuAW8TJTZgQaHGPUAMV/xX5Qs+BVTZdb0D94\nJnsGuqhPh7ntqxbd2dbjp5wbvjClAuA3VBvsltwA/ASIZiEWX5kpgy6Uepxr\nNhg0km7qIDGbGEvlDFb6Jy33Lvhhw8QLPl6f1kEXN5KnhDo53svXVqyqxvJC\n7/EfPTzm8LFDzvOAWuYKOBmvRVPUMr6Z7ftmILBzuc8XekJ1toFxqMbvJkDm\nznLPqK+IQb69kadg+egLNpu+W5kWpacJ9g5bOQJAOQwBl4GBKJv+v4PDhbCM\nGYjQyN60p9fubaFClkq97kLVEGn8tjV9d+GiMfGRGAYzG/CXQ5HIsaslssim\nmRULRLM2RAXb4ea7hNXWYCRNm8VjPPD0vP+SEI8ztTZkXmHe+qsQkq3d0G96\nFbKuURHo1L5x4oJtGa/aXSWwaoY5aZ42aQuNsL8o7dQyF2vCxQ4082Ys84Ty\nc1D9xwSr0cd6qJqbRHVLvCCcHO0Q14xLOFENQ6MEcj4D2mcyV+Jzs6q/OaSU\no+TpC0FWsJ1fCAXAHO+GzLJxrZNaLW0zfaLYf7eX9H0i/Lm3tgsVRjC4QbUz\n/BLux7CX68y8+zT4GdJtsTM1d6U9v78HWd57uwjlGD9AbIGwROvo08UBcEPY\nZyqS\r\n=LDLf\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0-beta.2": {"name": "react-redux", "version": "6.0.0-beta.2", "dependencies": {"react-is": "^16.6.0", "invariant": "^2.2.4", "prop-types": "^15.6.2", "loose-envify": "^1.1.0", "@babel/runtime": "^7.1.2", "hoist-non-react-statics": "^3.0.1"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.6.0", "react": "^16.6.0", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.66.5", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "prettier": "1.14.3", "cross-env": "^5.2.0", "react-dom": "^16.6.0", "@babel/cli": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.1.2", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.1.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.14.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "eslint-config-prettier": "^3.1.0", "eslint-plugin-prettier": "^3.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "peerDependencies": {"react": "^16.4.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "90b8d50ae71d9dc5d413200836d279de64f7c3e2", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0-beta.2.tgz", "fileCount": 54, "integrity": "sha512-B4CADCEReHgnJzRLGp4sadIhOpRZymxEW9LZ0vcoK02DeyRnQwYY3MI1IvZ6nNhphYMfDD311dpy571lI5HbHQ==", "signatures": [{"sig": "MEQCIBpAcck+u1d36QaJiZAPntsEWjq17eaya1s0++dYLpSsAiBL34VJ9KA7Y5DJVg7cmkaNaV5+DRUZeBWpjYsUTAH4nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4gwsCRA9TVsSAnZWagAAhAQP+QCdq7mqpfC6dap32m9/\nKEEf+TLUrPqwn11wSqraxOaqgmkFeeSGLFs8Fd1fBlzjavxqiPixgId6JDNL\nFUWPYai+nGwYlPuGoVKtzd9W6LEJ+W5mR9f0Te2bYjLv9ZCdf4SLzqj3MMlD\nW3SmCMArmkFtOPqeJOkRMOS3z+BAwIxaMzN+VXnAmPpvKUm7P2BnwInu1K2o\nDj3IBAsCcwaRKL5KZettjYoPQYjC4Pa97uWH1FxRLIzMllwgss0OP3DkFexQ\niJPTNWOgIm2d3ueFPfyWH1B1CBvUABtiNgzX6uPyT9aKZHu/3AmJ/hFyT2mK\nL0DharQ/SibdaOeNb5iwTJJwqI7b4xKvaExlVDzoYrs9iENOiahyT2NVnp6d\nvaY00B6sAz7cclaanitAmXGuOaKPwjGmWfE5YVgt0H5Ow463ojt9phjkZwo5\nPgb/Oa6/QGH9Haju0oSLIqlUM98LSvFeUb6148Wx9SrsCdtDtPOq2sB2TtLD\nHuWiaPlktvj9ubyOOcyYzobSI+fbuz/LqpjEQ/bGZbV61liimq+vPF4u2MtG\nHS4xbEuYIyNwd4HMjJeHx8A1RJ9DXana26G6OEbdpgo3+CYVybbg5v18pWvW\nzm3/P9FJ+davzdmST/tZE7y+sjjc/4UFFwNO20eEtI4JrDLgvkHdw3mIdoar\n85xb\r\n=dHP1\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.1.1": {"name": "react-redux", "version": "5.1.1", "dependencies": {"react-is": "^16.6.0", "invariant": "^2.2.4", "prop-types": "^15.6.1", "loose-envify": "^1.1.0", "@babel/runtime": "^7.1.2", "hoist-non-react-statics": "^3.1.0", "react-lifecycles-compat": "^3.0.0"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.6.0", "react": "^16.6.0", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.66.5", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "cross-env": "^5.2.0", "react-dom": "^16.6.0", "@babel/cli": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.1.2", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.1.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "88e368682c7fa80e34e055cd7ac56f5936b0f52f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.1.1.tgz", "fileCount": 57, "integrity": "sha512-LE7Ned+cv5qe7tMV5BPYkGQ5Lpg8gzgItK07c67yHvJ8t0iaD9kPFPAli/mYkiyJYrs2pJgExR2ZgsGqlrOApg==", "signatures": [{"sig": "MEYCIQDP3xLtMb28XzpYmbdXhOH98WS+AZ0g5tIvMCqlpR6+mwIhAMx8lf71CsShxMd/aIIn1A6dJP7sHf9I8XltJaG6/RzX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb51EHCRA9TVsSAnZWagAAsl0P/2zwUXtE54aEuT1SuzAr\n3zHmgVPt8yUxG4ToqoE/Ew5pTnkSlmHI89QH+dJLNvFQVax4FL1SKlr/9E2Y\nuwtDXwPeXegov+xG+4b91BqnnpG0Rc9Dscb19xGXu8joY45IT81WCC9MhgbP\nc3MShIomJL4z8m1TucJnGhe8/KRvabahdNqVWL3ld5lSmxVt2zWesLrW2T70\nK4UkwgL3w8unT323jXH59Zy78y6OLRMFtlFSy6aUGkXIjqX3QyUhTn6lQ6xn\n+DXQtYDWFhSxM1dNmidUiERmUT5msskZhOjDKShoA9jTLKY0V3uXS5w9Z3XC\nXnLxpAejZwct6+SjA8EcRSA45jGGWJax9/+5l3d5nMlxW6PMd2L5s58drg8V\nwMQh8L/eD+U9zfhDZtbKj0BIgYBPugKY6/Rlkxsj+iVVeAoOJaW1+EtnGcyc\noFIgEL7ghHdx4d/3W0OJpr8dOYSwiCnsdvV2+N2kZGyrfS09Ts5Wx5/2wuji\nUXxO5fqwET+uzsunqjaiLOHatLIX5u6W9RPtiqy4RoEcuMxVxwRgUZx3EI+4\nnaxojKl2VZ8nRCWkHCAovurCQjJjPCBkwNn2r9uHvD0fVq04YMbRfaib0VWe\nWHJw6a7sPM7egU10HmW/XwYznHBOlUUMXclCDHGxCxy20N1jYQMVoCAwa6sj\nQMcW\r\n=kWrZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0-beta.3": {"name": "react-redux", "version": "6.0.0-beta.3", "dependencies": {"react-is": "^16.6.0", "invariant": "^2.2.4", "prop-types": "^15.6.2", "loose-envify": "^1.1.0", "@babel/runtime": "^7.1.2", "hoist-non-react-statics": "^3.0.1"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.6.0", "react": "^16.6.0", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.66.5", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "prettier": "1.14.3", "cross-env": "^5.2.0", "react-dom": "^16.6.0", "@babel/cli": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.1.2", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.1.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.14.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "eslint-config-prettier": "^3.1.0", "eslint-plugin-prettier": "^3.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "peerDependencies": {"react": "^16.4.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "66ddb0c5a37897b4f18e94ffe3d695ab1ae49ba5", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0-beta.3.tgz", "fileCount": 54, "integrity": "sha512-cEms534CIOptDqPliL1U3n97wQWbP/uMUZLdoRlyMa74ey5o0bCPmR6Sw3n3CO8d4OQDhG+sFEc3Sb49zE7u+Q==", "signatures": [{"sig": "MEYCIQCBlqm7KX6SXyZwn/qNBRfM4WX6JkCvLF9+/G/RANjFEwIhAMGOW41cllNl4ozOpCK2jPwE0hK1LxCE0xori+JBjUrp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+FrHCRA9TVsSAnZWagAAz0EP/1A1qajCG8bBQBOkpX4b\nWMOpnIK0PZ1hxWnbJSTTYXOjwxFeCPny55WOSuk/rQgIP9o8anej9AN6E41L\nLFOr0Y+62YM1Z0sRNJpiNtEvgvjDZXQ3nVDa9lhOepUBLNu20tvFz2bAUXeN\nVJX4bzFt+K2r/qK/3w/O2R4tJH/zdOwJ71UJtMBD45YAE4Kmtm0iUykPMCXS\n6MFd4EZSon8ORqoazbo5m8N9XUEZkQ2WuPSh0Ao8ngk9Ib6UD9NTSPSZO453\n+g2TAujMP7OACKHhC0PQp/dJJbLxGhCwkoCr/CUNrQtiCQGfaJXr9L46DkED\nA0+sf5xE6MF0T9LiwkVM7ItOueAf2utrcKbXy2zYFcMcnJPDfkGwv0zIVjzu\nMHW0puA6VzBDftrNg3u3U2mypt8mf/UMqlKX6A4VgwQZlVX2sKEmA4RVQpEs\ndV0Fj3tHhpC1h6izSy+BjYixhQY+9cw2JVGBA4z/AEOW1U3rWI4uzeo821py\nFydN2cwa7Cnw85VxWavZ1P6ShTPfdjxF8vFyu9z3895TWfvaS86blSEHgmsY\nLe8VIMm6l+qPCjzYOc7Kp6I0Q4HBMc//BGyQMm5kD3lTLKsztk0pi9vYzAFs\nK05jQeGRwRN86UG6hSfVKiC8N3tuvDlOv7TBNapPKKUJehGnl087O/ZqBUV7\nzl6u\r\n=gOz2\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0": {"name": "react-redux", "version": "6.0.0", "dependencies": {"react-is": "^16.6.3", "invariant": "^2.2.4", "prop-types": "^15.6.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.2.0", "hoist-non-react-statics": "^3.2.1"}, "devDependencies": {"glob": "^7.1.3", "jest": "^23.6.0", "react": "^16.6.3", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.9.0", "rimraf": "^2.6.2", "rollup": "^0.67.4", "semver": "^5.6.0", "codecov": "^3.1.0", "npm-run": "^5.0.1", "jest-dom": "^3.0.0", "prettier": "^1.15.3", "cross-env": "^5.2.0", "react-dom": "^16.6.3", "@babel/cli": "^7.2.0", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.2.0", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.2.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.11.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.14.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.3.1", "rollup-plugin-replace": "^2.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^3.4.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.2.0", "@babel/plugin-transform-react-jsx": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.2.0", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.4.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "09e86eeed5febb98e9442458ad2970c8f1a173ef", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.0.tgz", "fileCount": 54, "integrity": "sha512-EmbC3uLl60pw2VqSSkj6HpZ6jTk12RMrwXMBdYtM6niq0MdEaRq9KYCwpJflkOZj349BLGQm1MI/JO1W96kLWQ==", "signatures": [{"sig": "MEQCIAe+1+8XIi1/Cts4GQ/7Q2x2mU00WzXJsKip75RqS0rJAiAiw2UT71yPA39GW97/SIpjju5fB6BzfckIrKyy3X7nCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcByOmCRA9TVsSAnZWagAA3YgP/2AKsmw4nMrVFhSR/sDC\nVmEvk316DPZURdzAGPV2WdIGH5VHlqg4mStFnDcseVJbO9x5auWCjzI1kRlz\nxuK47Wf1nFjNgp4EKvXyQlEzSpT+c21N+1AHo57FzZmv8cl8IMO8hUagzps5\nq46lGomywSXu5WJS3cpDyjb0+vnWBdM3epVFJ+4Y8XTszStIJnemovZl6iJs\nRXZqkkx0L3eUJT+kES1eXN8BntwIupeWqhZ/iT1+BKFsckC17Z6W4vFIM9st\nTJx2NTKyq+6X05pWqT4d7D+NuwiWYUflXi96UZghOdC9rtvW7iK6ng57EfDd\nD3d77c+oNlwN++XZluHnDtLEBa2W8ldeiTj11b0/BNq+EXZJIt8w5yvqCLTS\n8Esu1Ez08BjcPpffee57NH2wYJuZiSvtVznh+ajwbAhAQxOJgyaFbWmw334G\n573EfCeeFhQJx++zvrWMj6/5fz3EYQF6MZEvl1+mgVXteedsNmMtA4LDbB/i\nhshT2d2hd/mroazTPerShAGOG0x5Ihq0/KMwqw25kLiegQ2okzmTDoDfVROY\n2obB7N/9NrrrJ+oys2IUWB0sAu9LCdXmbqWWh9Qa44QNpQjGp+cUSc7tN6Il\nZCEB/siOBOjHIxpdc8LD7I7nJ+jLdICyh2F57evtA2mQRENrdvUq6OsKoWmg\ngjjL\r\n=mEjZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.1": {"name": "react-redux", "version": "6.0.1", "dependencies": {"react-is": "^16.8.2", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.3.1", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.1.0", "react": "^16.8.2", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.14.1", "rimraf": "^2.6.3", "rollup": "^1.2.2", "semver": "^5.6.0", "codecov": "^3.2.0", "npm-run": "^5.0.1", "jest-dom": "^3.1.2", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.2", "@babel/cli": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.1.0", "@babel/core": "^7.3.3", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.1", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.3.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.4.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "0d423e2c1cb10ada87293d47e7de7c329623ba4d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-6.0.1.tgz", "fileCount": 54, "integrity": "sha512-T52I52Kxhbqy/6TEfBv85rQSDz6+Y28V/pf52vDWs1YRXG19mcFOGfHnY2HsNFHyhP+ST34Aih98fvt6tqwVcQ==", "signatures": [{"sig": "MEUCIEy/2A/5oEKpTNZ3XT7ziO+WEO8AdPW/hGd0x6+W5yOTAiEAyrSrLwoUYoSTS9UmnKKWiRrSBcwm4zofheWjxvyQCGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbNA9CRA9TVsSAnZWagAAk48P/RZOYfB+g4iEvjFANQmx\nEhWg7y2zo6ItHOMRzCr47LTW+cg+2KOFptBhKahrOPsEXbvvh/doOfJ0kwAg\nHfcZkwDDxux3Jhk7gBskIPVYo20Aa9a/AoNqqwFtw5KArXAXPvo31QbYrvMK\nW6Wg7uYQI0/OgFRMXK1RNnySisaBveXkG8iuwQJlYCa/YhYu833rrK1LqUKR\n27DVyIXBy87ER+cGyjLTjaw5SIwIQl3fH596VONETqo3hcXGtK0YBbhoNVoA\ndqzsNjInklvcBDa1aFuqFzW8Su/QUEvdMXMb/zowVF8WNfo3MehM7TrhVuek\nh3A/UFQwTE1Cy1Dql8ZNqdBQM09T/gMaE+FXo2NKA29jsD5aUIzOybSaZjwd\nvDWVXHwb/TkZhI2FG10hUlNAfdOyEskL7iZ5TNZHM6OqKZdEsCilEsFmdZZr\n/iQaInSPpS1IBX//kHVYxk5LtvRYU4tc+61j51e5qaA+Q7bEtAcdVrvl11ie\nu+iTvk4HR2gAB4JfcWHGJuxDaOap+zGC8Ufx9c84dGnhZZQC+1htvJaNYnFj\nUMauIHNYyOoBuSu0Octk+ePgfFfXXUcbhFCqhbHQLfkrNJf6flbk9AR67GWs\nZ5+NJORuOn9w/DuvTMsjRsk55Bvlw44SQJ7MsM/n+I6f0ztJNgAGfRfeYOIK\npWLZ\r\n=DPAl\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.4.10": {"name": "react-redux", "version": "4.4.10", "dependencies": {"lodash": "^4.17.11", "invariant": "^2.0.0", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "create-react-class": "^15.5.1", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^6.0.4", "jsdom": "~5.4.3", "mocha": "^2.2.5", "react": "^15.5.0", "redux": "^3.0.0", "es3ify": "^0.2.0", "eslint": "^1.7.1", "expect": "^1.8.0", "rimraf": "^2.6.3", "isparta": "4.0.0", "webpack": "^1.11.0", "istanbul": "^0.4.5", "babel-cli": "^6.3.17", "cross-env": "^1.0.7", "react-dom": "^15.5.0", "babel-core": "^6.26.3", "babel-eslint": "^5.0.0-beta9", "babel-loader": "^6.2.0", "babel-register": "^6.3.13", "eslint-config-rackt": "1.1.0", "eslint-plugin-react": "^3.6.3", "babel-plugin-syntax-jsx": "^6.3.13", "babel-plugin-transform-react-jsx": "^6.4.0", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.4.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^15.4.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0"}, "dist": {"shasum": "ad57bd1db00c2d0aa7db992b360ce63dd0b80ec5", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-4.4.10.tgz", "fileCount": 20, "integrity": "sha512-tjL0Bmpkj75Td0k+lXlF8Fc8a9GuXFv/3ahUOCXExWs/jhsKiQeTffdH0j5byejCGCRL4tvGFYlrwBF1X/Aujg==", "signatures": [{"sig": "MEUCICx76/iQ5T6q/cXQ4dJoMBQybjaNCf4EIRYiNeOV8xGNAiEAqD3G6jav0vbaKcRGfLMhJvVSppZppKYOCSOlANYNHQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcikIjCRA9TVsSAnZWagAAL30P/0oikr4AFN6yIPMc/uIa\nMqLhwBKWeTX9jxQUq96aqth/0aSFu9+QxFVTODbsM2kof7V2hp0aBi5yOVIf\ndsL4Iz2PYwI+zyxY4GSqRYJbGn1RxE+P6vXHTPevIgRV+Rjg0SXLMIcVLcaZ\nCXU5UcTLTPHYzrVdIp+zei1tfEmgdX1wrXIPZwVH8E5oMD0w86ua4xyjBqtU\neSuydjASmtvQVmv9kwArFPzmraSZ1OF4dE2FgFurvggDcv3n1xcZ6nzgc7hC\nHWXzZ6AQ16rjkx2VvxEGJ/5KdG2IE8dT+xAz2mq8h73QCFZB5owoSq+auzSl\nnHLdQhcVqpywNNMbVm7ws1y4+iha67T3aUySwXdxwIgLR5gq2Z7EYFMDHXER\nDDZLqXU9i9mQTw1QeziaQsCO9k77zHZrZSRvOcDMLlbN/BVNMYdx9Z9T6gCH\nl9SCKq/PjX5UkPv57ZnT0r+UY6WYQXWl153Z6pjTVfRTMZFJFRL/Smyns/My\neMvpyXJlXQ4qqtsk0+Am/C/+v4keVMSF+DoY8GU40qBEMUdPVcI0Kt/bjmIK\nNzrESf1jePzGqSSWwlOPlgXcPUqd8QNHEbaliin0q5duiIK8TgaXZkCgjG9N\nYq+RUAjTqW9axmJCPhtJW7MnyimvBzZadD6g28tQ15+MK0YXDgboXZ+38Qc4\nnZ8I\r\n=2JR1\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.0": {"name": "react-redux", "version": "7.0.0-beta.0", "dependencies": {"react-is": "^16.8.2", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.3.1", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.1.0", "react": "^16.8.4", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.14.1", "rimraf": "^2.6.3", "rollup": "^1.2.2", "semver": "^5.6.0", "codecov": "^3.2.0", "npm-run": "^5.0.1", "jest-dom": "^3.1.2", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.4", "@babel/cli": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.1.0", "@babel/core": "^7.3.3", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.1", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.3.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.4", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "46ea289a0b0cf18f864b251a1d2bff4f110b71e0", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.0.0-beta.0.tgz", "fileCount": 69, "integrity": "sha512-vQSWFBeSxXm0RPuUqZMcYyN9CPapFs3cJltgUHQe943joTgcpC/nZAmiBMWLmte6BE7xYeE66diO2Z/87kWarw==", "signatures": [{"sig": "MEUCIQCbjRSOiZZIIXznV09YHGELez4jmV7kbI3m20j4zK2lWgIgeYDXKIBWyR2jcxbBrbACfc5tvsNwPjDL9QjI+whkYXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJclE+xCRA9TVsSAnZWagAAtigP/01Q8ZNL2FVREyUz4+88\nvviDjSGcVFZsjLiTDN+MJnjYC3lYy4aIvyGJY/JqyqHuhdwbDM+3B+OsB5b/\nRlr3g/mfLFlj93/w8bL66xYtz5e4TPLwuSbnZiex+5ItkbOdMnF9OHRX8/Qp\n4ufpp793Vbm8nZD6GejpgVTWA5a49EqfB5sH+3+2/G++E4Jh3yXnXujlvyyf\nJkYCpgIqNdY52RY8irCA7cI4L6K6DaF4YKDmUzxshrE1+CaJqAlr45J4jakB\np0DEaK+nlOtZeeStfYf+IdiElKHiIz2hHsyKcAJw3DMKTr/PXhrKu/SS25uJ\nafbnlmv4cyZCGMR2AaLmiJRAvuuOFBIwXPvzpNNHQZ+nicAr3knJSzL8vwl0\n+A4WqC6M9kqzdG/lE2u2vvI/KqrDUPTUhn4eGGvV78OhNPqX9w3wUBcJA7wC\nYg1tYzL1HnmHS3CLzkOit0B4DO3pURJlsIr5dxoyJhki/qZrE4Szi32MAsOo\npKQsrNyxV/oldYeXjtq9lF+HDCci9UL9xRjlDBaZqHDYG0LkPQEnrsmhEcGu\nvGCurC2U3Y/VVvgyVrSXLZP4V1K7Ycgyd/dTRR7Y+tuMf+3BZ/oczpbl/IKc\n2840DMVCu0CWorApKYMkzVnFwHOSTU5RDRc7iDyGpsn6bDaWHV7WO2kYJMJ8\nfBtO\r\n=ulDB\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.1": {"name": "react-redux", "version": "7.0.0-beta.1", "dependencies": {"react-is": "^16.8.2", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.3.1", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.1.0", "react": "^16.8.4", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.14.1", "rimraf": "^2.6.3", "rollup": "^1.2.2", "semver": "^5.6.0", "codecov": "^3.2.0", "npm-run": "^5.0.1", "jest-dom": "^3.1.2", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.4", "@babel/cli": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.1.0", "@babel/core": "^7.3.3", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.1", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.3.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.4", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "43ff36337fcda9787031c4f9cd59af9e19cb389f", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.0.0-beta.1.tgz", "fileCount": 69, "integrity": "sha512-Q1ptmtv1ijH6/H879DyJ+DoMbMjNGcafzssDXO6OzzuZXD7/jxXIhl7bS43H4DN5qjIG8EOcCGkfq3U/LynjlQ==", "signatures": [{"sig": "MEUCIQD6QyQ5FTLK6z6MsvBsz91KApVmryj7mapP6lAu9aagXgIgLmVJRsIrKHOGyOX5U9tufMHX86aJpYMLU5TQ28WUmY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpYwlCRA9TVsSAnZWagAAGdUP/j4Ul5ZAYrMOFdPOXxhM\nuOj3Y1oMbiTqqHpjqqPngGFeWwcxL7YWxiCGuodnFgj7Xye3Gz7LUJaNyJwc\nch1x361ZgHgbSSBXVvsabiORCKysgw0to0FZJ5EX8GkBHGvRHQwyKgAfhI2O\nSq1TDDGGS2Fb9U1OlhfkI+xnhM1aS7OZ+XJ8eXck/m26ogJzzhRm3rEil7g9\nBfmufK5+sGLdz6xR5b5ljwpHFNt+Fq6fw+HBIsHdcWO1K+inso0O/IYreeRd\npfKXJ9LQIt2xPsVSmmxFWBFODEpCUPVe4U2V9V3cQAW2iHMG7AcIo7Nk3OE5\nh41QPuKOoIhqFTuojy/p7FAMSvd4cuQhU56gjdngvWs/NzstnayfDU72i1lW\n5scCq4TtOgMlKwvTNGiRYmI+MLgKtzCzUHg/WeWTxaqEtJlyOuc0HJhiouur\n9jUFtu3+WmIgGOklU/HYE6+moSWwvVeM1xGZ9JW7+dfHMBcJfZV0irW3/JiJ\n6NBusSXlzXUWu9MTIIj/xvGLQmvNieDAwDRPYUZwxV8ZrYX4ylMakVdxEnu7\nYL6OuPvZ5IsycZ/WLhswWLv2d1v3e9HLRNUrf8FWeDxvqpXGzvNhI4DReEwY\nsli1qabATyaVcLdZr8O2yAjZk9rL01adLaOlNTemyObJnZ3rd6n/8SWrbSfq\nus/1\r\n=+rWV\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "react-redux", "version": "7.0.0", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "semver": "^6.0.0", "codecov": "^3.3.0", "npm-run": "^5.0.1", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.4", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "36984e772c7c0b30d777cd158ead882d09d8754e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.0.0.tgz", "fileCount": 69, "integrity": "sha512-ZVXD/uOLlrr21p57bPHcXyEJ8TLUEiHn50GjnzEmEFDL4J/Yta48B/L4iYMM+Bs525uy9HK6Bc3qPmolibgFXA==", "signatures": [{"sig": "MEYCIQC+F6QTQdXPDtopCEW/aI9C3xpyBRvKeJuVcisgf/atpgIhAL131ZNmgR/bWOvzeV6RrDCWlqDKB7eON39PwEaHnz7q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrBduCRA9TVsSAnZWagAA2Q8P+wfw6rGlEwC8RNzV/7sZ\nnS1P1aDzXnFCafwrneVlc+coRjv4IhqV7OGeZ68B+VmkLs7THedK2zxTXt2i\naXxLwgJiQll1r8UZ3BLJL/VOgtdv487WyG5PgUrbaINuf48YumqX9OphmvZr\n2TNJVYrpANaNijVrBW7yWCrBMldzR8A/SWIrXsQrZ1J/S2wePbImmRrdvnIO\nxbRFcPzfQYeoZ2uFtLJyZIp4uzReWS8PUn0UP7X/arwqgQ1DQEeQLzGt3kI5\nuWFjJbgsXEjydGXOk+qGCiWzyLzg+lemrNufu2nKg0aMg2vHGw9V7jzFTixs\nz3SNzh/A3yFWwLhMMbi3KIbs9oULB/NtcndqK4LIXMULbFbmz8X7LJ/cfkl5\n/zJpldh7+djhwTp9p3B+eE5TMJNWKvDQwm83xD3lEmyBkVNipiPfBEN+kblh\nwfZPT5qgrI0scGg0r82cKTmbOYGsxGM2ckicI21HOM+HSiyS6Gedb7Y8W10L\nxcCIJOJfhAjp+JWIIgl3xPZ1bWgr0hOlIhM/7V/FkcvoXnODls7u7telHOjV\nT8WHMR1rAXGHqPKvKNyTQ7g73oi0bL3BX65thNpK1nQfFqysrpnzUO0a6iqh\n51Xfo6SUvTpmy0BWi11QrtJBpG8LpoGBTgYC2WChaa0gkCbxiXc5XaednBnD\nDpu5\r\n=cozL\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.1": {"name": "react-redux", "version": "7.0.1", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "semver": "^6.0.0", "codecov": "^3.3.0", "npm-run": "^5.0.1", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.4", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "321285e6c85c3586d11cc066ab33dc580da599fb", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.0.1.tgz", "fileCount": 69, "integrity": "sha512-orSiI/QXtGiiJmf8lN/zVTx4hysFo/kGOsce28IUu/mu98AGemBwPTDzf64P4Vf/miRmevO8/w2RSw2awDd21w==", "signatures": [{"sig": "MEUCIAzL9SUwCUF37VrfwYIcXOXdoPHEMBZigVW3I3wY+OKYAiEAmkKLReJ23VCAG2f22FmA0el8lE3GM4iap7YyelWqjUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrBtsCRA9TVsSAnZWagAAfSkP/RNKMM1Ja/HtsuZW+G5X\nKkHJYyoJuUz7gbZBydYkR1XUIo/ne2iLMkoO+gpEG9yjfm/HqO/ZU1ytX80L\nvU5b7qPvjYcAjn1tDPya7w2lYYqsOrgWPp9xN9FMja0Y3mk2NpYk21VPdZyJ\n9zBrUyRbYA+JPOKjeufi1DNshRDkPYkfQng4344coK62I+q+0zCsBdwz1q2T\nCfa+S5/uNlc4R5XupYXmTyiZY8/tzMhiNNObElNPFWn5pDtlLYyUXvmV4LRe\nLdCk5aMfl71t8ndp48GX065bJSL1PEGYq2Jcl6Y06CNSJMVE4sE59xrOCrBG\nylDY/D+JaQlyqij4VZRJ8YvjxtIdQIpDU0svA7NYzAQa3f70FTljFbfxnrYp\nzylEH0cUdbIH1du37O76/esOZag+mVyzUNQOCZDWfQ25qEIqRxzOkk8JLajN\nNWQgzl+/vjhMxhELhd9S5HYDGvcvH4eVRRb06Zs0peKCOokgOlSg+kidmlOK\ngZY4i5whHVdFflxVfpTOTJWBKPGAjJqVjzBaEuDs8DZ1N6uLpHR2BAOvGU6w\n/vRJwnNz7463K8LO4+l2UInNbMj+VDhGWGbODS3UBFRPdf2O4AALpMMJ013u\n9WnaDcPjGoMq1ifMsuoujfr75BDCXTzNdRrBTAkk6q0oU/9FWHyXFNpNIH/9\nYFSr\r\n=JTyl\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2": {"name": "react-redux", "version": "7.0.2", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.4", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "34b280a3482aaf60e7d4a504b1295165cbe6b86a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.0.2.tgz", "fileCount": 69, "integrity": "sha512-uKRuMgQt8dWbcz0U75oFK5tDo3boyAKrqvf/j94vpqRFFZfyDDy4kofUgloFIGyuKTq2Zz51zgK9RzOTFXk5ew==", "signatures": [{"sig": "MEUCIDAhzkMNbli/+nKdEbhILxPhUQUszjDZSz+Z3XfXYRVTAiEA23PMQVvGSm0Wh1QlAN1QVhYJLVAFUHY4nXZfVejmDa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsPPzCRA9TVsSAnZWagAAQVoQAJzUchnyxFGcnia4MRs6\niY5UKW/yQgTIp360elGoVZYVfwO7gDYPGezq5niBY83H6YAv1Py7+3hFGNxq\nsQN7jeydFzjqFiLiZVMSm4ADqtfvLLUhCACCBdtj9C1x+km5ImON3lXW8gB1\n88elifwS292HqTY6jpo/3La314SCmSIVs3rD9X5aMBT3V0sy0l8wj+DCkNf1\nSykARJTAaUbLVgm3r9giR1rWSbffN7wTfE/JxOX+11NrVQFPwiepLFdPt4nm\nCbFSGrTDFoUjcMNhOoPwvrsO01697IuPR6/SFUAmCVGBglqVW5XLVDxIQyq3\nL0w5CUuhWznyYNSKp4LDVsCV0x9/GLVD7nTCooRdU2l8zKTG69hB3o30wF3u\nhPlhhIlU7XpJg/KpbI3sjabbj8aCNM02rN2q1FbxEnZXC23goOK9ucUQZYpU\nz3pJFhYqdp77STinS0ppFuGsm0uRy/Raememm3G29f0dYWMZj5mDcWjRM5lE\ndetSyaoDJsgw+v2ZiXjWY6DblwMPa7Tm5XzVcRhDIsfiPcq56T0Ml9YyBvwk\nUvilZpVfiEvP2almB7DAWE43XmJ6LbLWCK+B52PSkXwV50fD9KA+VTgWPSr7\nJG7EpoScAwzwssmNXNsc7jc7uhqnDt7mkh2mFsy5mvv+YvY60iLBemTMLtBn\n5HfN\r\n=1t88\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-alpha.0": {"name": "react-redux", "version": "7.1.0-alpha.0", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "7910202346017f7e510d537331743a6cb7d406de", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-alpha.0.tgz", "fileCount": 87, "integrity": "sha512-jrd<PERSON>jUqrjmwSMq2T0OUnY6lUYEfQ2gFZrl/q3+Z/tbQatARnjsJDd9xNHHiJyaQcZ00AtpxUGDrKg8hJXeUVWw==", "signatures": [{"sig": "MEUCIFOfe0Um/Dlg8X85N1uRLlbVJQAnUTpDC2AV4v97LQ8lAiEAhD81Lxci8yYjT84OP36VthY344rIQ7OB8D2a/iB02LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvTOnCRA9TVsSAnZWagAAcS4P/R1/sy3BdQuVygTLi6H6\ndgppW6KAFcYjFVUGxslmzCht/jJxoyVLVtO9JQglJ3rYojh0EE0g7c2UXX4s\nnP8/WcKr2EDiQTGCbyZY29swOuzP4dwRgfcNAPzlNw6eSEFDj+z7u0LzooSJ\nZzHQwwGr3F1K+GazvnmVygBmNCKqKA9ZExUuXgrHFID6lrCgorcPqPTWrGpX\nAhUbeh7RJ9TFjKvlF2fe2ojYa6GI24o4tPtR9Q+KC6tSCRwnMBXVlc3sw/n0\nEWWfD7DOs+1YTqJdvjOpnbi95mH3kztawYGs8IhDX8oxM5nFASw1YfJPwVJf\nazsuM76osPHDOwM0nUiodBH/1N4X6NnVcdM/vDGSeEewOaoH46oBviFPNIP4\n2ATL7p4e9+LIR6iabOv1wn6uwm6xPZ3br4JnzmEJ2l95iqePKdFro+a3H5lo\nkm5CwnxJwFbfzWARqjgWRZQ6hCKcSnFYK9jUNfUC5BzLKjgms/KiwY+7XftZ\nQd+4RswlnBpzdhuHBBrFL9Vcw/jwi001yZpj/reKXz6mxbRim3KdZXnxjfUQ\nKwY4wRiGBGu2yqCM2r/q6QJ4VGbqEa8aaB+Hkc6p7l8FLogsba0v6Sir8wFV\n5la0AF1IGyXNIvSbg14GfR/B/yLbuNbJopcA3YveSFzQnEQEgeIgAq1CgvGJ\nJslB\r\n=HmW0\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-alpha.1": {"name": "react-redux", "version": "7.1.0-alpha.1", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "a82e66bbaf89d7e6c98f2b502d33318b5a298afb", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-alpha.1.tgz", "fileCount": 87, "integrity": "sha512-Zxfb8xyoCx6KngsYROhOi4mDqSOFq0iWps4QsOzjbRL0L9Ls9tNpuYnPkrNbvxGJm3PUOL0BMq0buDkXD98J/g==", "signatures": [{"sig": "MEUCIBLkfwqxF08dSxxgEfB4KFzHLFI5Sx6+FL2YaLMpWYocAiEAik05I0ituz7uogO1ol+0MxlIVHUxGZVeQHcawiVZZGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 280888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcveQuCRA9TVsSAnZWagAAXtwP/22NihwQorIjN5wo7DrQ\nhgqzCyhSvlR/qgYNoeGKgcXV6uqPw0Y4E8JCntlW4rzgOhhiEuQLycNXujDv\nht+D5jfsWDg5Mhxl6fUp10ufgY4gejK/TxqR9aRUVENn1oEI7eVMHyYivebX\nM/BpRPQI8mq3I2WJNAeXuekE4X5FlVk78BL6cytjoqgvrlSLi9k8CiMWzkR8\nOwCub2+EyNacQnD9nKkCBhx0WIXGfQ26OqVU0Jel/uuPabONJRYJUnabzAJb\nXgxKQUg+WC1WmOZxYivNbyxfOeMKLg2Lh6AM90YSOsXPzTTOLtHCV+r4yG5y\nJAqX8dPWTRMoZwCKmov48mU2RKXaNcnF3GeLUOqHRA58JAIbiLCsQuvhcknX\nsm/b4AKVaDpkxrJGLjyz2TO7BtwcyxT8E/ti6+z+yNMn4xfpkTLjqmzVY1zm\ntSK+0QM6gaEkrSzc8XRjIhdFV9N6aMOo8zaWn37fVEdVG3EuAPGzX3F+cRNY\nOKdcDBxW+G6eZcXc8oG0M1nsuQHgvxv/VpZD9wlXw7tLm4ksYbM/fj1qOkP9\npr77zQVVXsbVxxJ2cA8r7f0uANjyPrEPugbCsAq6kWDjPKkSGJOF2zWjtY6P\n/LL6y8c7xK+Q1rpvXszuKfVja3u/wd6PbQys7nLYSW3ecAhIMEA/G+lxMItV\nZ+By\r\n=6W8W\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.3": {"name": "react-redux", "version": "7.0.3", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "983c5a6de81cb1e696bd1c090ba826545f9170f1", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.0.3.tgz", "fileCount": 69, "integrity": "sha512-vYZA7ftOYlDk3NetitsI7fLjryt/widNl1SLXYvFenIpm7vjb4ryK0EeFrgn62usg5fYkyIAWNUPKnwWPevKLg==", "signatures": [{"sig": "MEUCIA3pQMqaEDmXZiOUozqjA66AH7kKoU4r9o6Tr04PFb4VAiEAsbDq6qBtL2+eVMSyzCQ9Ll7klaP2LbBiYOv5g4RMGZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 238087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxdbSCRA9TVsSAnZWagAAnFwP/jmoYueXj78hcH2QpGbC\ni4NBXfpxKfXwHMUhrL60RqZf+XbisxTurneN1g/Rgzq4XKgP6CiVlLMGW28h\n1VeJeoSodCKRabTohDcCHRCSJxbdcFDAnddAPBzhmjc6yfcQ4ot4NDPlgMvH\nAnaxT+IklfaEQRIV6aLvb7bi7BaOn4jr9qXvc9fcyiHw+pRXIsl5fPaD7x0x\nW814WTlVPwlcQK2I4bY9ilKqROivPaloopeG2zE6975dLBrwvzg249JTv1qF\nAewkljAubKy/53G/0C05srlJyUztN6ORuS5Jg2WlF/nSzLU/agBloCffs3oa\n6AaLenBvtl6FhQsSLGrTTDhknvgE+ZHKoW1ikIQWmfIDuVd4XLuxW3hbDkex\nGCNK4OQqAd30rSdHENpBCseFFEphUZzZ018SnmURPHYJDN0rwvJ91c8SqydU\n6Akboswu3ymRCHbAeMBCmOXHaYb3b7JziKfYrGQFHSwtQO4/SzloUSFtqc6Z\ntPlx5lt/m0sJdHYgukvGMdR7K2ce61H+VHsH7TKRLI3iTsolvEop/Eh3eklb\nvkzheTyOalQbAi6TADQaJHtdKFPV1ZE0BGUrOJ0uALtikZDLYAwcUiwfQqkE\nqLZiUVPETzFQ3atw3yovg1YxHSgne7+ayg4kA2QdTrW/ZGPX1mYaATaapEa7\nEPwD\r\n=/wAE\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-alpha.2": {"name": "react-redux", "version": "7.1.0-alpha.2", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "react-hooks-testing-library": "^0.5.0", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "8fb677804d649ba0ca81ad8fd451cd5d33f00294", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-alpha.2.tgz", "fileCount": 84, "integrity": "sha512-ahNMzxM3lzWvkJy1au9Rz9RR91WhcJxrPzNCIIAl+IIvvDVZK20Ly8w2jjwEFFtp/X1K881gOIPjJGV+yUA9nA==", "signatures": [{"sig": "MEYCIQDSEWnoafrG1/U9naCltLFvo3HGHvpX7zU0DpPOxNbckAIhAIE2G/Gqg+XUm4i0kKsttncCcQ/v528LHLoZx4GRUZw4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxdidCRA9TVsSAnZWagAALA8P/1i/bK6m6GSJp1hG+C23\nmMvi6ByhrY0BdCpVBEr8Hnm3WiCyee4zhgajMENyvfq2SpifbbDRgZ7327qe\nzVWMVZ+x9jkpPVq4oWSBly0fELSEPIbx5pJ+KolBfYsKSXNj17Gj62W6HEfr\nZaPKJTmMESo0fOCiPkOSgruKzxMojXFROxreGADUKU0IMXHPlHejt0WtMi28\nYnt2DgPYPeaD42acQ56l2l8+pnF6Ux1JOBCVMMkBXOaFOgTt6LVfMe5N0amo\nQHGjYKpw18W8WQ88oxn0L361+gK6eQ7UjfdYe4H6rxUkDdWz4jvA6Ha46Y46\n81nonoEoQjKQzaAwiFhXDKTJFg2UvCR9FjMp5sQYfVq8sqywpOUcLkhyCuws\n/Hf5ZcZ98d510HtXhHs5IlEtQoJ5x2biTpkL+T0GP+uRWDZpdrUcuL7csoh9\nmQCkYpPQhimbxismxlSYxZSYacibRLQmW5wbNDZxD3u7OmeZNp0QGFlFl1xT\nBKsSXBZfei3x7iP2yIPyWZuOidnsZgZ8y3qhVDQkVjGQyxXoZOo6GSCOUv7K\nkA7qPH0aUst0wW+8/uLPfDDsxRTsh4R0CncVSJsgnU46CZylDiTXw9bgLEGI\nziqjhnhN/kk1FKeQLfIBYuelBzk/tKql7UNjtG9T1hgDydh4/PCGmXbMIX27\n9W/W\r\n=cyJh\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-alpha.3": {"name": "react-redux", "version": "7.1.0-alpha.3", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "react-hooks-testing-library": "^0.5.0", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "89ab7ea118bf73e867260885c7498d15a3692d6e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-alpha.3.tgz", "fileCount": 84, "integrity": "sha512-Nhbdq9SbxxhJnGVIyBxkrmInXzPaQKzg93uEwB6Z55pyr35d1K34YDSl6YI47iErPwTj+SmZX5t1HdsSHIgZcw==", "signatures": [{"sig": "MEUCIG89BAdCWE0yvS6MtUODr3Be0hRnhPfmUt++iD/1k+PjAiEAiF5pd9GO4n49Jji0wXcFyl6l+BmoGVqdpsIuX9hzQCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxdmgCRA9TVsSAnZWagAAoaUP/0Y6Urc6pJ/2Sqitk+y8\no01QAkxcs2swH33tF1cZp5WXlMWnO4iBRAuhDyGxjxtQLSwcQmrL9a4VeR/t\nQg0zhHVhCGh71usJrawdBVrVRynJ/nD4IyiQJue0UgLCRgvPBloSi76sRv7S\nqwRFuG7VGDO5WqIZR8Coe9LZ2JzLmDQfpdL547mYeEBOchNcREzDcbPLvY7Z\nynJCISBGldt2Rdt3CTkrkbiSu6KoSCP7rcITkPH/TOkH6BhLZsJPXB4kQapb\nHZ0x9PP740Z7Q1iJFkwOFxRR4jpxHWYx+xD8u25zj+n+fyA0jrnvXfSq3Jky\ny0EenBs2VwddRG/hKwtHVGhakWNUUNuAkSVjLwy5B+023ni6iMWYlFu3YeUS\ny+F3u09IO5zx/Xz4DgRxT6a5TpiQVuCCoD/02usVXS0jkeE1AUlT0FPDsSfa\nR0vqdbZcGQsAxD8dLuELghmjugje+nVXbODe94C9tnSSbMGnrmmC0CCeffgP\niaq5KHJiD9TcPLJD1KO1iiL21yoFdnAZn0pcwmBPKuIPwqZIhwL7ut0UhY4h\nc6p4A777w8bfR1MFP351PY9NUwtVzmmJBW61ncE7riM5y1K0p0MbO4U6MQxX\nSt16DyAeXDygQzPN1L2mwFp1oIJehyGnZOsYGsECAo1kwmLqB9sGURGPo6Bq\n39DP\r\n=10u/\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-alpha.4": {"name": "react-redux", "version": "7.1.0-alpha.4", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "react-hooks-testing-library": "^0.5.0", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "103bab23e336ce18951bc76a8c9567ce0607fc44", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-alpha.4.tgz", "fileCount": 81, "integrity": "sha512-Vxk6F1ibo2EC/cThTVUbvhTyltUFg7iLnEtro+QwOfOCygKpvwUtag1a8MPWPGv+BLWMXHniOaeo0sy6INhzFg==", "signatures": [{"sig": "MEQCIDq8VBICc2U1jtBtHVtXWEdHLe7nDZp91JmSeO+BQAtDAiAOLxCtc8THCV1a+/h5UXpuGK43bZnVdmkYC2gAwuAU+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 266440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyS7vCRA9TVsSAnZWagAAAdQP/3+rUteWtp605+k32z96\n9uGoDIuAJZBfiUmhP/PUijTNGKnjNbFnyDjJcNNzSJf3VXsYaDesSz8l6hKf\nFRfikEYW7/beCib0x7V+yiy6VBFa5jgvJyIqiQB0I48jAWVWEnWqgZFbu8Hy\n/mCZJAxmoBSFyx3B3w6SeGAoSdYn0SS2+zOnWlOmTmSdG9oChBhIfb2ZWQ73\ntj5WWWY8m783G32y5PfCcnvaq5+TYVsPBWzluMrlp/AKyGrsezZQlEj28TPr\nosd7aOuQuB070O+s21ewYk+nV+EzN71zFIoDjrIyImmWmtOTLxAnFWoPKinW\nlb05i/syUrApAEnnqkFAjx5NdSdZbwbMHVddpGJO7WHcV2nzX05ZGSylzFzD\nPZ/R/hDMX0Yblg2dU1obWXSvLm8qcr8QZJolVtKjnBYCuX8TBvOL7M+A3DAF\nxE5mR88AQ+7TzNhcvDsOqDdtCFG100pfLBFnBcbJh0bXPe9TmwPsrqXT2+bw\n8dkaxGiWROP/NeuTNOxvYo9RtNMP1cdyx5rEfjcevMEQgo2ljo4cjEIcxaN4\niCuSyQqYoRuJT8TqNjqjfJT77FrVrRcGe/2anaV2hIkLx9dv3rcvXnyxsNpa\nrW7Yf51ttEtykFyVwX+E2KWpvMPlnZ6KD/O/Io2nUrvy3POynZjrPFVYx+t3\neOtY\r\n=JbiO\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-alpha.5": {"name": "react-redux", "version": "7.1.0-alpha.5", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.3", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.3", "jest": "^24.7.1", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.8.0", "codecov": "^3.3.0", "jest-dom": "^3.1.3", "prettier": "^1.16.4", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.7.1", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.12.4", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.16.0", "rollup-plugin-terser": "^4.0.4", "react-testing-library": "^5.9.0", "rollup-plugin-replace": "^2.1.1", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "rollup-plugin-commonjs": "^9.3.3", "rollup-plugin-node-resolve": "^4.0.1", "react-hooks-testing-library": "^0.5.0", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.3", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "762336fd7a941ed5503ee4890e2324ce29ec0488", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-alpha.5.tgz", "fileCount": 81, "integrity": "sha512-bLDaXcNWcoiXKIBz/waf+oImYIBhR8h1YgXo4GKgBEyMJXtMqZkkNqD4w7JNlTsN2Xxo2/1E4akcmAo+wzJAnw==", "signatures": [{"sig": "MEYCIQDUc/iY92lZ92E6P7q2iljOdxhVtsVgjAMqyFIs2EUX4wIhANzbPfQJ5tLnwxnJz2mEI0WGlI8EB6CheghcrCmycUeZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc4fKcCRA9TVsSAnZWagAAWM8P/iXpeOpynlTbl1EuvUhL\nMYuE0Km5Tq7gb4Zl0F8NCxg4aPHP8GvXPJR9IA6OAhine465jLPCEylTD3Lc\nSDu+WIZlBEDNMoDiNxnM8CBXeY504tE6ZshAClqYCQ9WXHP4t0Z4lry+rAWY\n0TuXdkS26m3Vpwg2ggECKiDbug176ypuKKrkmvc/7+kIQ98o22OOUbQU8/aO\nDC5A118A1zem2T89NenBbOOs+IB6s5I1qbL2WuA4WMUyTpkbkQJWdf1tn62Q\nl94vgS5WlgLOqQ+SZoONnwsuZHf6QrHTL6TxZ5n3ml70rn8e98uQUXhQ+vCw\nhVWD8T8IxMZ5PhOSXe+OSHdalPscAN5OWZBqgsKQKyjIyoy6uCtjCbvVxHm+\n89DkkAixgxT4hWbSOrDXBrbYkaTVR5GjqAKZJrBqbsX0K0t2llHN/h4Og146\nGTMdVk+7hbuV91JuPmlnvaltAZfreiuTKD4E+uZYR3eT0MNqzV7j6XCQCtAk\nIvh+7ghSw9fVcqzoSpc47R8uqIMB3Y6v2JEreKu2c5SLpq4atQddJugxRldT\n3HiYNVHPnLo0f9PncrFeWsYsdBJqstGkFl7WfFwp3ApYEtMkgltwVRMs+b/K\nP/Xu+6ksc2AaTKNDmDmnpMDit+vyOl4PhnJrYjl6QH+xjF5x7Ai9psjh5AnU\n5+qS\r\n=Wqae\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0-rc.1": {"name": "react-redux", "version": "7.1.0-rc.1", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.8.0", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.12.4", "codecov": "^3.5.0", "jest-dom": "^3.4.0", "prettier": "^1.17.1", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.4", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.8.0", "@babel/core": "^7.4.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.13.0", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.17.3", "rollup-plugin-terser": "^5.0.0", "react-testing-library": "^7.0.1", "rollup-plugin-replace": "^2.2.0", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.0", "react-hooks-testing-library": "^0.5.0", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.4", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "af42e20901e443191ace09392b1c8b85721a5de6", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0-rc.1.tgz", "fileCount": 81, "integrity": "sha512-ULeRoensr1LZqs1f7HiWYWN67ppRuoBbB5NfSKn3Py92G7qNSnNb+GC2quMgOXXrMBmcq0C9hvPn3xwhQmUXKw==", "signatures": [{"sig": "MEYCIQDBbvNR9R0uENBaFPuocuwxY9qm+QBN4yJx7OjHzdirwQIhAP6lw7e1+jyzMTvmjZMCRQPFPu7aXRr7JXcCZCbPPa2A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7zYBCRA9TVsSAnZWagAA3QkP/3yWuDSBs844Y71fKcgd\n0J4V1xG1+ZYdVA4wOjZu5KiMpveOCJfP1/h/JRmTXbwoRKIcF+brO1QYU7yj\n6jP1S/Ldsu7Ye8daa506Gty7v94A3HKeoaMeyqso7/Nb7UAtIs1bn8Cf9NCo\nxASvsGWEIM6L9A+dsL4wxB9AaedNiH4Yq7Sx/vKSdnfAI2VNC0nazYHx3dot\nV+TUVeSmGM8xUF4E+Ta9DeOG+cdHS5R9+yZ+II7Xf/GyHpYOU6Oa4uviwZZj\nV9HGCUC5iAwYOza11hc4SRXRUFeRpxtcoTt3Kzi/GxzY0IIGKCoOOrrcssKJ\n0GRcW5l9CRlv5tvlh3s/b75NtQKCUe005nYF8sjui/lSDF/0GzA7y0HufV9W\nVe1WC1+niLXtKFa4J+7yOJEd8Ge0Ga5E1lcjcd+B6riVQAYbpejL66k6VSNe\nUYu6qOX3a/tL/GetX4TNRc8zMuDc0BWvVbaxri4Rz48IxUno07oT8GFAxbaT\nWlVUe2JZE4yBbv6cTiBYbO2ucg7MQmWE/tTepG9EfJ25tA2Cp23ofMQqCHTo\nmcPvTPOQw/QdTXuQ9Y+i467Zv2YCyoDezPxBkqjpk5fBPpzEfeUtA5qpDHuh\nBJFVNEeiMFWytewGUPCBmIWISu0jIPQd3uiBJQGlHNHWT6UWhVP6k5VxbQka\npfen\r\n=DYAx\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0": {"name": "react-redux", "version": "7.1.0", "dependencies": {"react-is": "^16.8.6", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.4.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.8.0", "react": "^16.8.6", "redux": "^4.0.1", "es3ify": "^0.2.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "rollup": "^1.14.6", "codecov": "^3.5.0", "jest-dom": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "@babel/core": "^7.4.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.13.0", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.2", "eslint-plugin-import": "^2.17.3", "rollup-plugin-terser": "^5.0.0", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.1", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.1", "react-hooks-testing-library": "^0.5.1", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.4", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "72af7cf490a74acdc516ea9c1dd80e25af9ea0b2", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.0.tgz", "fileCount": 81, "integrity": "sha512-hyu/PoFK3vZgdLTg9ozbt7WF3GgX5+Yn3pZm5/96/o4UueXA+zj08aiSC9Mfj2WtD1bvpIb3C5yvskzZySzzaw==", "signatures": [{"sig": "MEUCIBt9qmbW3af5Wr55U5kROD90c2JYq/vRIL86349Q52DmAiEA5rYi4Wqq+33Fyj6eaGFbI/B2iCufFSstu5T0f9tshJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/w22CRA9TVsSAnZWagAAJ2UQAKRASqL2b2JaPbb07skk\nrAbAy+Ic5VkkhLsDnvKAdZIJC/MESB+B7FZaX2KKdI7c+D9duFiP74rvVAQa\nUsjUOvpMwIAOFWJsLksaTkkLOBgRlgz4p+TxeP7y4s5tIGxDnBr48AwmR8dm\nln/2I5DaBsz9CPWVo4vsROa09ubsZuZDn6DxJGYyFIes+sflRFHNH6Mhu6X9\nXK6NJyyb3MZu9CYdnzVYP2VBPWBJ79HnxYxseQWVytNkVg+wR9y2I7jkj1/d\n2FlIdnKeS+FuXWfjDIXN3ZILLc7qeYCNSlBV0EK6CEedDO2F6SAAMmSDLqF1\nY4l1veHD/67IIoQi5JyTG3+GuUG9BH468G9aFTQlZhBUbUiUvD+k6F+yH+8D\n5x4obnkYesUVP105j8O9FuIV0zRdCaS6Z20kXw9v5+4BeIURsuTX0hYvHxyq\ndYbOVV73wHlvS/ObSV6bAc2O12Dkb8T+8ffXefbDEh+kk5ECJdCbaXs+5kOZ\n+jX4cEH6uGZIUZAI2hpmFlFtPhHAkIYi1yD5hKcYIJEku0+xtEu57uKGrFXI\nonjTqyAghqpeoXz8Pve+gSs5aS77PW3s1P93pm2OugMoDwTk0ShqWJw25KuC\nSDU4gTtR8s81a9O1r+B4L3nYma+/ardPVnhnqWzlrpKQruRgmPGx+ePbCogK\nV3gm\r\n=qKMD\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.1": {"name": "react-redux", "version": "7.1.1", "dependencies": {"react-is": "^16.9.0", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.9.0", "react": "^16.8.6", "redux": "^4.0.4", "es3ify": "^0.2.0", "eslint": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.20.2", "codecov": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "@babel/core": "^7.5.5", "babel-eslint": "^10.0.3", "@babel/preset-env": "^7.5.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.14.3", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.8", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.2", "@testing-library/jest-dom": "^4.1.0", "rollup-plugin-node-resolve": "^5.2.0", "@testing-library/react-hooks": "^1.1.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "ce6eee1b734a7a76e0788b3309bf78ff6b34fa0a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.1.tgz", "fileCount": 81, "integrity": "sha512-QsW0vcmVVdNQzEkrgzh2W3Ksvr8cqpAv5FhEk7tNEft+5pp7rXxAudTz3VOPawRkLIepItpkEIyLcN/VVXzjTg==", "signatures": [{"sig": "MEUCIQC5TooBa7MgDlvLOxBwDWPhyKJON8RL6Kloiw/jmRWQ0AIgQez4e0xvGY98YBE6sIauKaqSlVwaxag7Ko7M7fmIV7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 268297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYztWCRA9TVsSAnZWagAAXmUP/RNeFgvqu8PNxrUXQQUe\nvZv0YK2bOwXPQ0ID2bmeCSS8eoOBYCXFx38Y1IPsy/b8ztE3WU9GnDtLqek2\n8xvPMo34g50acTjzTLIwHopkn/5YEX1X2GPVjBssUu0v+Uvd0ubCHzyr51oe\n2MzFUPysLlfleK/VmuRG4aGysjidmapkifNmvXoIlidwmsQ5p0ttvwJhxMd3\nhu+Cq13683MLDNVUHzEGfDZ4eXg6OLNHlN/DAUvYmncXjxbqEu8bxXYJxe/H\nQjMxKRwMcsHJtl+IYXWPbOcaFUyApzUHIeIdsY3V14AmxYQWD1fEbaOXADKP\nZ0UY8XBvadUQI1pq9SR3TJMMLLpWa8Du2z2V0ZyOSQAuRHlcZDwYNK5NBdQi\nb9EThF7YHNn9NQd3yKm9OMSlDlxKTJvu7I/gLXNMrIkY1RuGiS3ABdlmqykT\n97G6tu+AA4Dv/ye9Inuce58/BGiuvZ6e7HhiseRHMw4xs6vX56l9jDzJYCHr\ndwITHJh2VOTrpYifsWApTt5mft2RAwPwQ+Q3AZ33lFL/X89EpdQxr8i4LNwd\nHjekbFn8bnOmUpDwwvMBT6MrsuGC7AdBpAa9GeuPN96XU7uzKKLySOvdkb+W\nuhbiJRonQTtEMaKxKx3PYyf9XMdme0kCpklXhE/P7XiYpn/2fenVIriT21hd\nZEGw\r\n=osGW\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.1.2": {"name": "react-redux", "version": "5.1.2", "dependencies": {"react-is": "^16.6.0", "invariant": "^2.2.4", "prop-types": "^15.6.1", "loose-envify": "^1.1.0", "@babel/runtime": "^7.1.2", "hoist-non-react-statics": "^3.3.0", "react-lifecycles-compat": "^3.0.0"}, "devDependencies": {"glob": "^7.1.1", "jest": "^23.6.0", "react": "^16.6.0", "redux": "^4.0.0", "es3ify": "^0.2.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "rollup": "^0.66.5", "semver": "^5.5.0", "codecov": "^3.0.2", "npm-run": "^5.0.1", "jest-dom": "^1.12.0", "cross-env": "^5.2.0", "react-dom": "^16.6.0", "@babel/cli": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "@babel/core": "^7.1.2", "cross-spawn": "^6.0.5", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.1.0", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.9.1", "rollup-plugin-babel": "^4.0.3", "eslint-plugin-import": "^2.12.0", "rollup-plugin-uglify": "^6.0.0", "react-testing-library": "^5.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "b19cf9e21d694422727bf798e934a916c4080f57", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-5.1.2.tgz", "fileCount": 57, "integrity": "sha512-Ns1G0XXc8hDyH/OcBHOxNgQx9ayH3SPxBnFCOidGKSle8pKihysQw2rG/PmciUQRoclhVBO8HMhiRmGXnDja9Q==", "signatures": [{"sig": "MEQCIAFF2PLt4vYCXaEfbUE3TqQdLZjxSv5x7l4FvwTtw29SAiBvh7I9G65q9xt7ebaA+FmdxJYJQyrr+yeW/jRTRYc/pQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnO3nCRA9TVsSAnZWagAAT08P/3+j42lhOfYD7V02ackf\nETo9Aj4t8soNTyb4zOww9oZFLokoPJTK/FkRk7FcJ7McWbql3nYZAtt/RWZg\n1LQ92S9sooXQK09WeZJCTh1cmTJaTYC2Pp17sVAIv7MpAhN/YehAcGkfsN0Z\nDLr/i0ZxpOo7taWqmy9r2iaNsV5z3tbUBlNM9m4R6JxvpZrCU9jsRXt3OuhM\nCHCxutAak/0oSgIRhUbn6h7SQ1avZqM0f6F/6eg0cPDtlhspqZWP4YtEDgpR\nxV9ap7qJKGguNYMrVKuNEyPQjbeT4h05whljeq6FmUB2MNf+3Ul3TFWvKoB9\nv/r7XTr847bE8SFhlVqNi+O/bN5x5ZvDbvwQ6OaH4H2/bdON61xCyyc4yNTV\n2XckZLxx58fAZIxvMYLXN//caIY+yF2ORIdg7Htj6ka2r0SXSVeOY/EeHcyl\n4aIAFJlJm0i0XN9FlYaIvKAe+cD7qNqW/y3VgYmbU0Fop0T0gdjmrPwby2Wg\nPqNBg5Pquyk94x7S9q+Zm2iEmxAtK7/0uVtSL5vd0SG5+zG3HmyedHJf0bxf\ncaTKUVfhiSsHhwxXwlYACKIepY6ZEyoIVcUPlmnRgAzGSI7CP0bj9C5PgwZ6\nteYIEF4VkxWLL1uayF7VLo7zCe7XfmgiAG1ZkM/DGgFT1pTHNM0D6s6vUtKp\nX6n2\r\n=I1di\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.2-alpha.0": {"name": "react-redux", "version": "7.1.2-alpha.0", "dependencies": {"react-is": "^16.9.0", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.9.0", "react": "^16.8.6", "redux": "^4.0.4", "es3ify": "^0.2.0", "eslint": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.20.2", "codecov": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "@babel/core": "^7.5.5", "babel-eslint": "^10.0.3", "react-native": "^0.61.4", "@babel/preset-env": "^7.5.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.14.3", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.8", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.2", "@testing-library/jest-dom": "^4.1.0", "rollup-plugin-node-resolve": "^5.2.0", "@testing-library/jest-native": "^3.0.2", "@testing-library/react-hooks": "^1.1.0", "@testing-library/react-native": "^4.2.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "9bce672f13dedb396efd23f25db8f1b31f7f1a56", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.2-alpha.0.tgz", "fileCount": 87, "integrity": "sha512-82TkAQ/NXSVrqSxICwrXv4xmSUXnDNBhUWRXrShKXEtNeMv0+j/CknyaFMrJ+05EXb+GFDrwJNRSUzg29RmEHg==", "signatures": [{"sig": "MEQCIAoS+Pb4lGlNtBa94uNKfqIWUZT7WDq8jR0uDM7UODYTAiBqTwoHi4Fvul7E5/3mhxZ/THwOuQCfH0aXKg2InWTLLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwQdeCRA9TVsSAnZWagAAI1UP/0dEmd2z3LTNG7AsBXs8\ntVopRLXzmKOmiBtkznwyVuhjLgPgdxmKb9oc1oa7UYJG+uh0O3Sjlk5ZkWCf\n5DJQbUSfke7Qu10aw/87Z+Fb9/gA4j3NAmhsTfWxsvHziU0J/xDLj4vAKyNf\neIaW6fint5lXHa6us4qywGU9Kvqtx/FFc5vtvBqdH+61291qGPJYHH202Ucw\nwnIu7L864fCTsy45ST72OlYg7CaE9Kb3C/Cw/i3L57pzW3zj2rcb0ZA5NYwA\nbw2nqAAUOIMA3gPqvmai+JpB7edC47zAn6IfDBXvsZAKjkeMNjNBfdrhy44T\nHruzx++nuu2Xc7VID5sulRqzRReTL+VZ8sL71r5U138U7GyghH7gUszSDhpH\nL8zD0HKncCOREX5eyEBXLFk+F7Wd4GC6HGUb1Up4DtR5lg/wS4tvIbWGqBxR\nzEXr3UsIsdhgiPc6VzlsgKaHzJYRbIUDsOlYzKUMJphaO8x0bjHoroBAlZmV\n0fc8RNJaGT6Dnjl6nsKMfhfffxDXYxvn9KliqPF3LeEqUzRXnc0WtMsPvCPg\nz9eGzgivG7oYaxRBzpwD5u60OIlpG1X4to3y2nZNVYYq6Psq5Mt8/pQjRPqp\nNs62wlJOl01wEnew8WbxVqPKJOAxIoNPp+gZJqho/1luODpCjbc42fwKm6Wl\njZ8L\r\n=yz9d\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.1.2": {"name": "react-redux", "version": "7.1.2", "dependencies": {"react-is": "^16.9.0", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.9.0", "react": "^16.8.6", "redux": "^4.0.4", "es3ify": "^0.2.0", "eslint": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.20.2", "codecov": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "@babel/core": "^7.5.5", "babel-eslint": "^10.0.3", "react-native": "^0.61.4", "@babel/preset-env": "^7.5.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.14.3", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.8", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.2", "@testing-library/jest-dom": "^4.1.0", "rollup-plugin-node-resolve": "^5.2.0", "@testing-library/jest-native": "^3.0.2", "@testing-library/react-hooks": "^1.1.0", "@testing-library/react-native": "^4.2.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "6a84d2ef5bb5aa74c3e9408580fe2e67c4bca851", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.2.tgz", "fileCount": 87, "integrity": "sha512-MUh28sWAN+5t8UFcOcWb7+EVvRNhgKijwHiJHkkc/gjiuMaygAvy7QszLyKvMHEOc0S664cEEsesGixTa43pzQ==", "signatures": [{"sig": "MEUCIB2Gq3VcTg3CFJQWFLGYbJZYIw+FGwS0qUfoFnW/4JAeAiEA9iDXtb1BZb3BT9GyZqzm4YQzEVbuHU1m/HjlImHAKnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwjlRCRA9TVsSAnZWagAAavAP/AkJaE11lsveylyk3x+q\nFjZdEPQOI100xD2dX+ALanjb8UoJ6MWeddQUnMZKSVWV5zVc04GpLTXVZosA\ncw4w20EFf4QbEK1tF+jn7Y9xR7TblN6P3JHULznO7n6PKkkUj+GHiRJt4qpa\naVaN70s+au+vU5ULUmh4s/eL6kY8NRkj53R4ssLyV9/1VFXW6EX5hSvoorem\n10lTCkQsiG0SxhCMZ54FK/21r03jdH2BIozyYlDq+3HUSgl4mxcQtpGqat/U\n9G6OYthuUdzwrUI2AHbK1G/DDEZDpHEayNEkt9GjbfK/mV2rIWJpVGV8XWE9\noXspbPAwQjtxWKB5V/A6coHpu+S+tyR3tCOI6HZCRZWcHWzxtqh0b9A1aiXh\nPsH+bnsWVyOclGlD9sB5exOiWrJctTLovTD8kT88rN1Oic4nbURM7b4/0W+s\n4KK6V7S7NTdMkhFU71rxVgCdURKoU2i/2QY8QScdreuN6aOL0e0eVIoN3M3k\nopIkY1jnkSfR13EP9LqIHY3/A2JE6DnjLKwEqK62YxwOzUVlzBtGYwhBCNsY\nvwPwax4yfwpbEpzug1zlriAZoIKPlY5hIvBw3Du12o6VnCvl6zLiYiz/FvQT\nSuoy4by4mFgtoVCNsO8TSlY1RyoGj+r8cTAgDFImvPU942clcXi5Y0gm6BB+\nLWET\r\n=zEUO\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.1.3": {"name": "react-redux", "version": "7.1.3", "dependencies": {"react-is": "^16.9.0", "invariant": "^2.2.4", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.9.0", "react": "^16.8.6", "redux": "^4.0.4", "es3ify": "^0.2.0", "eslint": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.20.2", "codecov": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "@babel/core": "^7.5.5", "babel-eslint": "^10.0.3", "react-native": "^0.61.4", "@babel/preset-env": "^7.5.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.14.3", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.8", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.2", "@testing-library/jest-dom": "^4.1.0", "rollup-plugin-node-resolve": "^5.2.0", "@testing-library/jest-native": "^3.0.2", "@testing-library/react-hooks": "^1.1.0", "@testing-library/react-native": "^4.2.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "717a3d7bbe3a1b2d535c94885ce04cdc5a33fc79", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.1.3.tgz", "fileCount": 87, "integrity": "sha512-uI1wca+ECG9RoVkWQFF4jDMqmaw0/qnvaSvOoL/GA4dNxf6LoV8sUAcNDvE5NWKs4hFpn0t6wswNQnY3f7HT3w==", "signatures": [{"sig": "MEQCIGo3cJYYKXj2hXAoQ0XN10/3FcZi8tmIvxBnhyZ/YT2uAiBSCFdGmPidDGLp/BmjP/5Vgc7OPww5kUWlDm/UIhhVGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwkXZCRA9TVsSAnZWagAA+QMQAI4Lmj6QKg5IQK6awNUN\nNH6EfjAH3W9jJKV6GXScZw5Xlw8XpVz1lep8wlA+4bPTgkcnmgMCEI43KSGL\nvRxhBGLTsPNo5Dqvo0VRJM3QcRXu1eP9o0Ih+lfOMuLfGe52iAzERpTg6FLJ\nDnjxNLm4DahT1mHDp54PvoSYOPtCQTr8ZXEqI9gu0clX/eWiviRBVTIxpOTX\nNiva5kCxS+iPassGKQVqd2DxvgRO8eJdJgaPYMaRaQ9cBQV6dcRLN5IV48aY\nBnhqxBt3Dit2mwZFC2IPnaS57oIG+Q5Acas6qOyrbaMWhhhTWSV6P13OYUii\nMQl6qKzEM2e/4TpcauCZnl1cn/w5yDO15GcjesEcMKYyhPZYh9SDsmfBzKCC\nI/jSX2ncU8sgly14daSpx0DpiJ5DpC1eAsHYoOmxQDZnpSNqDei3qzScFKVU\nihGZ9TFJrlba43ghbjtVQI5GF2NYWRcWgAbF0UyYbkGjihD0daZ7+HqkTaT5\nfANgzfZ/5Nt7a3mNEZ14Q9e/S1Q/4SfSfAaJHNGkmiP+2aDJhEXAJq50FbE8\naJb/EGExKvxHaP7f+bHfMMFOWWXayApux+Nk0uBx6rWjnMehmu1TUKAGCNBU\nJxiJ0nVE/oZVY7cuBCdfqLhK76/ZQNE5e3OqhIkRucgHuDcJWVjbqr7CcrUe\ny7t0\r\n=ZlYX\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.0": {"name": "react-redux", "version": "7.2.0", "dependencies": {"react-is": "^16.9.0", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.9.0", "react": "^16.8.6", "redux": "^4.0.4", "es3ify": "^0.2.0", "eslint": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.20.2", "codecov": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "@babel/core": "^7.5.5", "babel-eslint": "^10.0.3", "react-native": "^0.61.4", "@babel/preset-env": "^7.5.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.14.3", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.8", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.2", "@testing-library/jest-dom": "^4.1.0", "rollup-plugin-node-resolve": "^5.2.0", "@testing-library/jest-native": "^3.0.2", "@testing-library/react-hooks": "^1.1.0", "@testing-library/react-native": "^4.2.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "f970f62192b3981642fec46fd0db18a074fe879d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.0.tgz", "fileCount": 87, "integrity": "sha512-EvCAZYGfOLqwV7gh849xy9/pt55rJXPwmYvI4lilPM5rUT/1NxuuN59ipdBksRVSvz0KInbPnp4IfoXJXCqiDA==", "signatures": [{"sig": "MEYCIQCJjGGTqaW6bTpX/xqQBRrz8q4TZNe4vBwGaUhpy+JbOwIhAP8XaBWsMyhpNHMmITOZKnYgF35GDaIRnQhFARXnQUlA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS115CRA9TVsSAnZWagAA74IP/i6T2uWyWt73YUWteRI7\nsZfJRyIeTXgt97ruWUBH12IgBsTMhGMOKKxhaqFlW9nG2ZQmKn6mO/npn4ZJ\n1tdnVLqOzhwh0hS674ezmcUmyszliIGDZNJlHu/TIRrOR0QD8V+k/99MeOQP\nyb7JaUzAsjhtggLCsxh1OtELB9BM+EcgTu/VGUCLBrD8Atzf1xW8xwXxQYAN\nxq/my+HTMxvsVSuL1eQpqrRxtOp7w9crfjfe4xGKeR2DHf5W7pYRw2mK43Re\nnSh8x5HsZExGCAWOp2Dm3TKtuwKPbgVXhfG+gMi5wEHU5IWD6/tcZkB0wPUu\nSH8LYWN/QktT+P9CjvhQHP+tVZvOTe3G1MCaZ/BYnMdpf0JsRrussJ4Vl1hU\nLbjnazgQYcM2qREsGOrV6hcUFODYnmvt/JJBE9Z7iRSQ3SLceYWcqXXozRGT\nkNc8yUbU/LhFqIG/9vMwFI5m2l0/R2xTwcj6N/UQKBqdPokqcamnqjyg1srm\n8M6x6ILJnyByrEkk+548giJxGiC93cHilV97YcIWXTRkkPCFIw2cDwLGySWy\nHBaP0mZraej6OQInUXvTlvU+YT2KnLqCiDw0UNCAonFwC2fxGB3V0cZg1K4r\nK5HObvAEbdsptMWmSKrGgcWx/WAR/jubxAOofZxb61dqi9CoeCjHSFL+Jg/y\n5pX7\r\n=HR4E\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.1": {"name": "react-redux", "version": "7.2.1", "dependencies": {"react-is": "^16.9.0", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0"}, "devDependencies": {"glob": "^7.1.4", "jest": "^24.9.0", "react": "^16.8.6", "redux": "^4.0.4", "es3ify": "^0.2.0", "eslint": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.20.2", "codecov": "^3.5.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "react-dom": "^16.8.6", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "@babel/core": "^7.5.5", "babel-eslint": "^10.0.3", "react-native": "^0.61.4", "@babel/preset-env": "^7.5.5", "create-react-class": "^15.6.3", "eslint-plugin-react": "^7.14.3", "react-test-renderer": "^16.8.6", "rollup-plugin-babel": "^4.3.3", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@testing-library/react": "^8.0.8", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "rollup-plugin-commonjs": "^10.0.2", "@testing-library/jest-dom": "^4.1.0", "rollup-plugin-node-resolve": "^5.2.0", "@testing-library/jest-native": "^3.0.2", "@testing-library/react-hooks": "^1.1.0", "@testing-library/react-native": "^4.2.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-transform-react-jsx": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-react-display-name": "^7.2.0"}, "peerDependencies": {"react": "^16.8.3", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "8dedf784901014db2feca1ab633864dee68ad985", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.1.tgz", "fileCount": 87, "integrity": "sha512-T+VfD/bvgGTUA74iW9d2i5THrDQWbweXP0AVNI8tNd1Rk5ch1rnMiJkDD67ejw7YBKM4+REvcvqRuWJb7BLuEg==", "signatures": [{"sig": "MEYCIQC4giog4nwDCalJShyMweLbri8yy00VI/7xAki/MLYqPQIhAOGrQqaeIsNisypnp+tFc/opBrs+wkMaM/xY7/3cgjki", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHIS1CRA9TVsSAnZWagAAR30QAI+zHyskQOGrIzXamTQz\nule+5Aw1UeRQ5Z7jK1n6dyBaYG35DSzDLG091T9No2DnoTpB7LkzOu6moYE0\nihHrhnQE5d37fUQsGgQk2xgOglh7Jlr5dNOv82Q/HQyMqZOUJkCZ0XcSuvD4\niVjDkA6AQyNMofK23xuOiuqNUxUbmqaOm9WXO8fqX2tYe4cB+2ZQwPr2Ciw0\nUqOnayCYUrmnIsbHORGVyVyHO9eu42zb/7RxKF9SWO5wazqXQdtOPx+jSlkn\nYr9+3qRJ9WGdduuf9zTUi007upNx2/FyGLBV9v/3RFjfgG/tlwpbgZHkMyMI\nl2KnqIpYSoECl1GRwYmNSfF2kzSrxJ8OLfSkuNf7OB/H7+uvAEkpaV43Thq5\nA18owsi8aT2JP78CRlNBPtC8QovPfw17s1jachcTj2doISKKN+hRRhfeGhhy\naIhd1sh2MQhAwZguV/WE8ZqQgifIPIe/TZfSYZPnRfsPVq1958giX+yUAAlO\njx0UFthMUEUY9tS/CUqX8FzT2cRaX9sfoCHxbFbrGWQN1bUa/i6htHHAFceT\nRyGN/O0c2G8ecqp5Msu5NEqfYATfyHo61VnVBVJDs9colwCJ9Hdhw6ql7EFv\nPBN4GpDkkuFdut3M2WoOwu7mBUaJuVrBXuNVaL4IpiAbVrGUfW3SKDcpUN1d\npwat\r\n=jvxM\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.2": {"name": "react-redux", "version": "7.2.2", "dependencies": {"react-is": "^16.13.1", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "^16.14.0", "redux": "^4.0.5", "es3ify": "^0.2.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "^16.14.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "@babel/core": "^7.12.3", "babel-eslint": "^10.1.0", "react-native": "^0.63.3", "@babel/preset-env": "^7.12.1", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "^16.14.0", "@rollup/plugin-babel": "^5.2.1", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "^11.1.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@testing-library/jest-dom": "^5.11.5", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8.3 || ^17", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "03862e803a30b6b9ef8582dadcc810947f74b736", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.2.tgz", "fileCount": 87, "integrity": "sha512-8+CQ1EvIVFkYL/vu6Olo7JFLWop1qRUeb46sGtIMDCSpgwPQq8fPLpirIB0iTqFe9XYEFPHssdX8/UwN6pAkEA==", "signatures": [{"sig": "MEUCIBK8f+0fHtE1ee3IY3TXqL4u+91JICz/fiqaFHFgOhGdAiEAz4iV0vgg/uztGAnULYaKWggBbg2da+/VWGbDWl5+8pM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 270653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfl1KpCRA9TVsSAnZWagAAbboP/3Vn8p724fih8mXKnZkj\nuEg6z9ZS0mYqX8GuNww94LCI5SBqmAiOdBbTe8n9Rqm04IroLCj2BftVItov\nzhoSlXADBthUeiMLKAw1ph7FRMrdlSQHNZMv/ONMZ4NyBaSkOylOVCiVLpcd\nVntcBmt7BYywW0kmzpYSGDy2E//xYfgfoEcxe8kUEuCQIOVBTQtLcEsccRwJ\nIV1kxUT27Jb1NjitmQUSFxauuZ335mWILRv7ZIfnfI/r2JS90gJHgKtJHaqW\nJSEikMejLe/21YalR66X+49uhSAw/Gn1imr/xsMWWtrUOXYINwGQvWKI8U/3\nAk7QKBKMu/G+gb5HGl6ySvxYeY/o3TdA+Lc0zChZ3cxVTYeqZbVyf1lDCXho\nIMwzZuoXdk6stsv50qro7ay6sCsG55LNcIM97XSkKmOwH5V9UgB6bWSF9Rpd\nKvSrgu9FXYtXyi8y0rz1l3+QcckQx/xUy6cEUEuCujQU3hqszHPof/QJz0Rs\nbL5knHMMcPtjSximIBhYNlzGcpC/L8JjqbTK/NXFHI1XBs+5pvvuh0qRwt3Q\nSc4FFK+opgYeB5YcIM2d8mpnTxk2IfJk7l3EX8VK45kMk42TqLIYUGFI5bZr\n9RezZ8z6ddascgQiLxRl6mdm7m36K5V1Ycw8xWju6/7PWvGa6Ic5Ra60bShs\n6t6J\r\n=/AYl\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.3": {"name": "react-redux", "version": "7.2.3", "dependencies": {"react-is": "^16.13.1", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.12.1", "@types/react-redux": "^7.1.16", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "^16.14.0", "redux": "^4.0.5", "es3ify": "^0.2.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "^16.14.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "@babel/core": "^7.12.3", "babel-eslint": "^10.1.0", "react-native": "^0.63.3", "@babel/preset-env": "^7.12.1", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "^16.14.0", "@rollup/plugin-babel": "^5.2.1", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "^11.1.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@testing-library/jest-dom": "^5.11.5", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8.3 || ^17", "redux": "^2.0.0 || ^3.0.0 || ^4.0.0-0"}, "dist": {"shasum": "4c084618600bb199012687da9e42123cca3f0be9", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.3.tgz", "fileCount": 87, "integrity": "sha512-ZhAmQ1lrK+Pyi0ZXNMUZuYxYAZd59wFuVDGUt536kSGdD0ya9Q7BfsE95E3TsFLE3kOSFp5m6G5qbatE+Ic1+w==", "signatures": [{"sig": "MEYCIQCOLlyeKgEJxU6Rha7Cxx3lSq/R5VcW86HDMASk0Eh/oAIhAJV1KbFm0hP2PINYk6SZtRafaM1tpPP01LeY02XBz4PD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWTNTCRA9TVsSAnZWagAAx54P/iAyObubfqdHSBqAshSJ\nrpazjA8rz84h4uj+UTI6soQvxuu84wgVemPVY8fEqLQYdeqDSTE/WP9I/eLj\nHILx5Er5uwZK9NS7eE6cRDFEO8ocWErsSuWbqSpbgPqI9C01aLSs0giVFy3m\nrqQBBhQOp4AU2JVEVcOwA9GhdPCRu0pFfHEUsTRQODgWa0m8DlYS7SN3CXf8\nxqVqiA39w+kAj4pJxN3oNb8q9wZCfXlzTKt07OVnhP534OTlj9Gmi0h/qNxS\nmYU+s8pIqjR7vh2NIGflljJAj9IRKxg5432z4/IificwUIHVqdsxFZYCvl+p\nEJm1whaakhKWpOVH11/84CsRPrtMhgXVoJIoVtu7769qNTvQ9+wVxe1XQOBV\nP+FOpbnD1PeI7sUfDXD+X//7U5Y9d5h8slMlZPF2+sW8wpDjhRDW3f0uUP3I\nJp1ffHYR2ya5pkI+haPyLwipjJbqj4a4PJDRYAcptOKVLxBGpRa3BHnOmIOl\nqhfIh2LQMW/1gVSDS4PvvztfQeOZoOfqhJ+qKojnMC8MNu1n4tRmB8kXJJxC\nhGdBSPdqGT349r8g81ApGcHvOBUR2cq7m4Ep3UuFpO29l9O01cOgfSuqzDHP\nMrHv0i4Mvzw0gt4jOkCNtTZciA2ceb8wNUZxR1tGrAy+TBhjRGHqoR7aA+fc\n7lO3\r\n=C+CY\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.4": {"name": "react-redux", "version": "7.2.4", "dependencies": {"react-is": "^16.13.1", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.12.1", "@types/react-redux": "^7.1.16", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "^16.14.0", "redux": "^4.0.5", "es3ify": "^0.2.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "^16.14.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "@babel/core": "^7.12.3", "babel-eslint": "^10.1.0", "react-native": "^0.63.3", "@babel/preset-env": "^7.12.1", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "^16.14.0", "@rollup/plugin-babel": "^5.2.1", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "^11.1.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@testing-library/jest-dom": "^5.11.5", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8.3 || ^17"}, "dist": {"shasum": "1ebb474032b72d806de2e0519cd07761e222e225", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.4.tgz", "fileCount": 87, "integrity": "sha512-hOQ5eOSkEJEXdpIKbnRyl04LhaWabkDPV+Ix97wqQX3T3d2NQ8DUblNXXtNMavc7DpswyQM6xfaN4HQDKNY2JA==", "signatures": [{"sig": "MEUCIEHPXwhqLaA7RvSpByZToJjZeD4svkL0fNhmt7/CcsYLAiEAzBECUW87bGnrfRaRzMWXIbqz6u9vHgFSFAQXGVzvklw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghGCNCRA9TVsSAnZWagAA/1sQAJB+u22DNK4h73QMJDC4\ntUU89JtWKAF/JVMv0J2VhNtZJkONugSQvGQHdOFQeYhJIM5GezTLj01uKZ1N\nfSADKx8bxQHmYiPy/t8hTOIiyG+9toxwifnxWxUq9QmK6UAciMcObFKCR1Hi\nWe/hDTy3JhJQu1O8KuATke8c0/GfmmRFJAcvVCjj/2BMiomThEtc/rQ2xi0V\n514glMKlfAugzNdRil3gBFHAc8G5bW+k49kdj7AgenVStPX9/4G91TnTrCSz\nWdOOTKxnz31FGQsXIJ0VeqaD5Je/hhzaceTZSsXUUKUZ0Cc2+eDYW3mOZYSr\n1JnrOn0UWUCqttW3HsYRb1s+3M0zUAsJFTdXNAh8N9/hM6emWP6L+vWWhqdq\nK5AWyNvb6rVRiqa47Z95l6f/dH79BUklFbbdi95AAuT3cqYuYH5QYOiC04iM\nfFOQTHtIpd+9vZyapXHmQXAhuyXjWXXj1Ejl5pzOhDuf9ZhPPTuxG2HKUbTP\nh2Ew/Q0aqHNJN3be0TgmolGtjhNwqPhM0Mu3tspDXMoi+EiSHfsNEWeCBhKj\nq1V+m1A1eZ7ZAt9d/lBb5seNHojZ1Z4lWfPdskSmPj8o4YAVhy5YST2/AKud\nhq1pjbuxsopLk70rUxDL9z15QQG580SAXcVXgwgxpp6fxrSLEwE6U+WeTUms\nvGkc\r\n=OqQG\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.5": {"name": "react-redux", "version": "7.2.5", "dependencies": {"react-is": "^16.13.1", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.12.1", "@types/react-redux": "^7.1.16", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "^16.14.0", "redux": "^4.0.5", "es3ify": "^0.2.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "^16.14.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "@babel/core": "^7.12.3", "babel-eslint": "^10.1.0", "react-native": "^0.64.1", "@babel/preset-env": "^7.12.1", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "^16.14.0", "@rollup/plugin-babel": "^5.2.1", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "^12.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@testing-library/jest-dom": "^5.11.5", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8.3 || ^17"}, "dist": {"shasum": "213c1b05aa1187d9c940ddfc0b29450957f6a3b8", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.5.tgz", "fileCount": 90, "integrity": "sha512-Dt29bNyBsbQaysp6s/dN0gUodcq+dVKKER8Qv82UrpeygwYeX1raTtil7O/fftw/rFqzaf6gJhDZRkkZnn6bjg==", "signatures": [{"sig": "MEUCICV5apTTPx3ocIutPG1uEQrjFzBzok0Yt5mJ8uZf6XgIAiEAxxdf1rjA29ojN6CMrP0jrCqbuSCY82HiTw8RmihgOEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhM8kUCRA9TVsSAnZWagAAX0YP/1ZvEQqL0CC2wyoGUld5\nHZz/6pchmNG+pscy07AKVjjjMOs6JDp7DbvBOtgw623LONKFeO8QvzidrNzV\nUwlTZbjo+PHbEtu1CuzoWcL0kXUtVCMFQsDEMwCj1a/NQvKp5MVcitqzsHhO\nKc911DV9Jaz09NfP2x51pCcVJp9AQUGIqyaE8LemQUqS8B9EvQTtXRJGyEZJ\nohHzuFPXkGWDDQIK5Hq0xVUFgbDWuKO8WSdN3Kl1+92dJPx1wnhSy+jzY5Nw\natjyIAtQCf1wsBfxdz0ABbe12MWAExedRAQYOc6EhxU84Ep8FR3pNLLjWI7H\nwDuYJvgo83Z8k1Zfw+vSPsx7QQ2U3R/ACdgTTcpiqs4UG9/muns2LVOWjhdB\nYKWTN3t7bVnY8pkOCTCAwomPMPj32iJyzuGQ/Y7j4GUiFuPOh8ahnVQodEEu\nB3XL/9/euipjmomwW9Mis38ozuA+IvbC9tipBoIG87tSo1b0QgBpC2hePaoK\nhVcEolOKJ8XyKgcKjKTBJYUcqzX2YgCmTxw0kFwdA29hHNLWrP2qEbtVBYu/\nZaunUMwIZy7+shplbbsM/k82YEHELtN4GF0/m7A7sv4YR5ZHRfqHmebOQD3M\nO1HT4puEwtxXal3BwNobn6ok5kyu8TIqsBGMKHUt2WTJR2589Ff9Dc57IzNP\nCmz+\r\n=R13Y\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-alpha.0": {"name": "react-redux", "version": "8.0.0-alpha.0", "dependencies": {"react-is": "^16.13.1", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "0.0.0-experimental-7d38e4fd8-20210930", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "0.0.0-experimental-7d38e4fd8-20210930", "redux": "^4.0.5", "es3ify": "^0.2.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "0.0.0-experimental-7d38e4fd8-20210930", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "17.0.19", "babel-eslint": "^10.1.0", "react-native": "^0.64.1", "@types/react-is": "^17.0.1", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.9", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "create-react-class": "^15.7.0", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "0.0.0-experimental-7d38e4fd8-20210930", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "https://pkg.csb.dev/testing-library/react-testing-library/commit/0e2cf7da/@testing-library/react#.tgz", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@types/create-react-class": "^15.6.3", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "dist": {"shasum": "b4451797f3c0363f25e43c1f06f8ea6b8941d927", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-alpha.0.tgz", "fileCount": 118, "integrity": "sha512-zs5DOgbId0xbpakb3XrVng+zZXkOgsN1Vxfm+nbhWL1LdjOgjDMNOCRupHy7oIyP/DNFDGkC2EE/y5S0Algtzw==", "signatures": [{"sig": "MEUCICLN1mfuHY+iJoMRzX2Qhz5bA+0LuwzEBaNDQzPeFzWlAiEA5DMSTen+7Mik4zgp8cqCiDaiz9fgzVHu2Lb+adfUdL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 294973}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.6": {"name": "react-redux", "version": "7.2.6", "dependencies": {"react-is": "^17.0.2", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.2.0", "jest": "^27.3.1", "react": "^17.0.2", "redux": "^4.1.1", "es3ify": "^0.2.2", "eslint": "^7.32.0", "rimraf": "^3.0.2", "rollup": "^2.58.3", "codecov": "^3.8.3", "prettier": "^2.4.1", "cross-env": "^7.0.3", "react-dom": "^17.0.2", "@babel/cli": "^7.15.7", "babel-jest": "^27.3.1", "@babel/core": "^7.15.8", "babel-eslint": "^10.1.0", "react-native": "^0.66.1", "@babel/preset-env": "^7.15.8", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.26.1", "react-test-renderer": "^17.0.2", "@rollup/plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.25.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@testing-library/jest-dom": "^5.14.1", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/jest-native": "^4.0.2", "@testing-library/react-hooks": "^7.0.2", "@testing-library/react-native": "^8.0.0", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/plugin-proposal-decorators": "^7.15.8", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-transform-react-display-name": "^7.15.1"}, "peerDependencies": {"react": "^16.8.3 || ^17"}, "dist": {"shasum": "49633a24fe552b5f9caf58feb8a138936ddfe9aa", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.6.tgz", "fileCount": 89, "integrity": "sha512-10RPdsz0UUrRL1NZE0ejTkucnclYSgXp5q+tB5SWx2qeG2ZJQJyymgAhwKy73yiL/13btfB6fPr+rgbMAaZIAQ==", "signatures": [{"sig": "MEUCIQD7NRV/zM0UrbwfkCGOyG+wizXBBYUChEdgFzCIWI+LpwIgdhiTCijmqjJy02mD3OXaPzj8WWkUZoI07gW994hv6o8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 297329}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-alpha.1": {"name": "react-redux", "version": "8.0.0-alpha.1", "dependencies": {"react-is": "^16.13.1", "loose-envify": "^1.4.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "1.0.0-alpha-5cccacd13-20211101", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0-alpha-5cccacd13-20211101", "redux": "^4.0.5", "es3ify": "^0.2.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.0.0-alpha-5cccacd13-20211101", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "17.0.19", "babel-eslint": "^10.1.0", "react-native": "^0.64.1", "@types/react-is": "^17.0.1", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.9", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "create-react-class": "^15.7.0", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0-alpha-5cccacd13-20211101", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0-alpha.4", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@types/create-react-class": "^15.6.3", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0-alpha || ^18.0.0-beta"}, "dist": {"shasum": "7336f7ff8548633ace7faca51e82b38610ef341d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-alpha.1.tgz", "fileCount": 117, "integrity": "sha512-V6DYdFbhJiPYblvWTjbTLTEgw9MICEHm1BhpA/UsEupZcCTeuPHgaabQiGUzyFoa7iaFEc7OGalS97rXRoG0Tg==", "signatures": [{"sig": "MEQCIHQbLPNhM8nTXTbPrIgy4/XyJnX1+OT1u+OH+q8mXATnAiAKQtbB2SgF7KCYJHha5gAprvRmaS1yx56WOK4wL/RxmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287296}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-beta.0": {"name": "react-redux", "version": "8.0.0-beta.0", "dependencies": {"react-17": "npm:react@^17", "react-is": "^18.0.0-beta-fdc1d617a-20211118", "react-dom-17": "npm:react-dom@^17", "@babel/runtime": "^7.12.1", "react-test-renderer-17": "npm:react-test-renderer@^17", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "1.0.0-beta-fdc1d617a-20211118", "@testing-library/react-12": "npm:@testing-library/react@^12", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0-beta-fdc1d617a-20211118", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.0.0-beta-fdc1d617a-20211118", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^17.0.35", "babel-eslint": "^10.1.0", "react-native": "^0.64.1", "@types/react-is": "^17.0.1", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.11", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0-beta-fdc1d617a-20211118", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0-alpha.4", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0-beta"}, "dist": {"shasum": "5053e37775897df344e4c7d509f3d184c7b0958c", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-beta.0.tgz", "fileCount": 125, "integrity": "sha512-KdGF47bIIG5ukplcZVwQbvjchxb7lHlLrfgxVYtFGDts1HwXIQEyL947qVTry14a2ath95ixqQE88vJuANt2XA==", "signatures": [{"sig": "MEUCIQCRL5i2h+YM3WyRhl9H1xodO60FjytRns01Lqa4HwZB/QIgOhRTUVO8lj6/wBxfRQt64FiN+fYaKvcE8ZTRaqa4iiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlyfpCRA9TVsSAnZWagAA/9gP+wfbdSYPT0H2KxdzPd75\ncJwEiXVtQp2NcoBB7wydx220d3/bMyAv3ZrNIMQN28i6Gqc7ddekZ2SRJchL\np8ss8APi+dX3eTwdrJQqZK3ciXEFd0vmqfhkx5fzp26yIYRMmiI962Vj313H\nOIxgr+jNhAOjt3BC6EC+KhVOGVvZVyovMJIGNhtZm5TUT4psbjsBM/NN8ivu\nudoqRFrxbrXJoeT39azI07QgQTJMKBg8k2BaVyYxQJj27wTQYlXNO0tnmYLm\nEMpMZeq97wbME7SpGx3gyaR+3OYSyuSXzd9msFEHrlzpF60SPDOADhktkLns\nUReWz9aFC919sV4gzSfyabirDfRAMqqxvWdvj1vaUNw/Kq260XsrMZpTjKw3\nsP9yQJLtE4/vNayQZ93ILcXYRyFcVGREZ06Egd0bu8yyi7haSPMUMWpBUOIx\nNulpMQN/wRchCFJsBS7gSUpl1RV3P65xlcttN48e0SZThkjEfH8K9i+jCxYj\nXK4ehrX1QGM/YmsZL5PNW/afQ1u2y/mS+k7mM2VWBOUBxZT8uJaZmGDI+qNR\nvg9nv6ModBdvFYFb5IgbHMdrqfuQsXt1CY6LfWB20iKqDrkMdfDFOHaC69qL\nr6C+WQLNxxjdTMu4/SvbegfXtluit8cCJzGxQDDOkecsy7adRFbEmGczDOgd\nxS0N\r\n=MvH2\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-beta.1": {"name": "react-redux", "version": "8.0.0-beta.1", "dependencies": {"react-17": "npm:react@^17", "react-is": "^18.0.0-beta-fdc1d617a-20211118", "react-dom-17": "npm:react-dom@^17", "@babel/runtime": "^7.12.1", "react-test-renderer-17": "npm:react-test-renderer@^17", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "1.0.0-beta-fdc1d617a-20211118", "@testing-library/react-12": "npm:@testing-library/react@^12", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0-beta-fdc1d617a-20211118", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.0.0-beta-fdc1d617a-20211118", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^17.0.35", "babel-eslint": "^10.1.0", "react-native": "^0.64.1", "@types/react-is": "^17.0.1", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.11", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0-beta-fdc1d617a-20211118", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0-alpha.4", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0-beta"}, "dist": {"shasum": "04dab8442cdff323668d02cfa3ddce71576bbdb0", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-beta.1.tgz", "fileCount": 125, "integrity": "sha512-GX6azXYU4lK76fNQtMbNOqCjlehhNys3QPTX9Usu8phHLmwsc3DiaI1h60dnIE0ERnWEo63N9EzIpY2z5T5ORA==", "signatures": [{"sig": "MEYCIQD/aelt3E7xoAenTS6yEjZsDjIZtt8uxrLzh2JzM//ZVgIhANl43ugJC+sdZeKKArG29vtArh1lCT4SfCPYVTd/hKBV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmETLCRA9TVsSAnZWagAA9zsP/3ZP1mC5ckmpWUnUuNFQ\nvqwi9V1G89b0BulpYaKSn/ovozZks9NKHcm4wZ7Y4UAPwgNRexl0135SXJCc\nkBZwquT5iXZdLduga2zFlbUNAiPrHmTX/NS6IxbW38cULfn4Isj8/0QYxUSK\nkbc6aqqGfGGkKpNhpU4RA9NA8Gm+3A0WgosNe5jHHblGOgTXGYRQMRtkWij5\nnIuPcCjZW9IxBFg9Rp80zSoHovmYXiv9S7gnX4HXfYbjcdXPiotcz3Qy6wiZ\n/ZKIHAgQDpH1vvsk+x6qq1TjOTqXphYp9A7+LXP+Ky06s6iRRl4gRLUDet6j\nDQnWIdwSgp/LbauQ8DUDh1YSrKzlNB+kCCs8uygkBlQwg0xNJWk6fyTyHcca\n6zJjq4hocgPmU6rLQv8CVHYvBOKFM9+xzCvd8dctG6nYlTPmPs52kzq9wE3X\nD081BTkeTekpnsT6cSG0Y3DhOlmcO5/IyAAgzCiiHzYqfdH9EaZYf5qi3B5J\nQ5MFoLkU5TVy+Gns/PQpzvzJIbQ9hfq60XkBLa3f4//qvKLDe7T8Z0sjJqUh\ng9eqlwoaja7cakp5PynYdnMFjXnHqol2jwx6JdbNJP5pAs1sx7Fj0klOid+u\nqrt+S57zFPEGqO3KUEzxVl4XvOhiu14sOfldNXpRmq+6/fnddKy+owurpGg3\nT3Fz\r\n=OVoY\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-beta.2": {"name": "react-redux", "version": "8.0.0-beta.2", "dependencies": {"react-is": "^18.0.0-rc.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0-rc.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0-beta-fdc1d617a-20211118", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0-beta-fdc1d617a-20211118", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^17.0.35", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17.0.1", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.11", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0-beta-fdc1d617a-20211118", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0-alpha.4", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0-rc"}, "dist": {"shasum": "422b13e2f7f66e5abe80ff98b82b01c79b1c9842", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-beta.2.tgz", "fileCount": 125, "integrity": "sha512-TGDuDTqlY8GX4VxQiE9xMwgub6RAY9ICx8UZKykYrtoGcpGe75KbmQHO2Sh8C7i27yEq34MjXxq1WkfMePnfYQ==", "signatures": [{"sig": "MEQCIARDGRaUfM49Vksx/gE/k41lHbHgCehfkzhlFpHjHJxZAiB19a/wi/xF7FZHdnUmey5+igFg9Io5x3u15YU5U7+7UQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 308151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhw53ACRA9TVsSAnZWagAAULIP/3uMF/2HSKggsm5l8cK7\n0qzBVz3mzIhW/cnDlE7KAk+iUDmdEBcn98/Wqonqrc9YkYFEuSymjpsH1NYf\n34aGCtGT57pXg5Vv/kDIPGtATrKRvzdJT5D7jJBELAOTT5DtCWj9qBeVKq2H\n0FojVfS+cVyR+pggnUxa4fOCXEujQVsRT4d0iM7uQu5snlXnu3HnSLEAjc2r\nztAG5vfTsRGavl1AdhII2sCfn1C3xHDA0OM0Ina5SB3OuzH10m3l2X9V8Bfc\n9c+VeACP71TNRT97BHNgSCykuX+avKD9uqkoi2EeP8rAlBwHPJXV8G+IWFkX\nFC2jGeMjF3SguTX0fjJrPFqqLta75CCYXsLRcLrf5RT+3KkIooNuVrkNfANQ\ntaFLxRJQc7iwS5vihPptqU6gV0khUoVm5c0Km7znT6Ra3dK/sZyEQkn45476\nsEA8PgIzg2Az8JvIAVhXZ6ivvc/675EUcPO95yIifOCESEq1v4vosWaza1RR\ndLfT2okaRIXrwVFt+C1TjbVFRu6ljSlv+B/e8brEaw8MQg8sDvCQeC5tUd+l\ngcBJrfdeLmNwwoMtDQ4FoIO2g+TrvWNET25lf14SDbruPmqIL5eJxUSodNoy\ncYwaN5Z47Bu7B0rC4IQjA5s+JqN4oacacKiHCmh18LWe7SNvdvC14qSvT8t1\nk30c\r\n=vpK0\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-beta.3": {"name": "react-redux", "version": "8.0.0-beta.3", "dependencies": {"react-is": "^18.0.0-rc.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0-rc.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0-beta-fdc1d617a-20211118", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0-beta-fdc1d617a-20211118", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^17.0.35", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17.0.1", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.11", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0-beta-fdc1d617a-20211118", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0-alpha.4", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0-rc"}, "dist": {"shasum": "4956fa01b57dc617fb00dde457f83d55b8d74bca", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-beta.3.tgz", "fileCount": 125, "integrity": "sha512-c8mVsEXkjLFOexZOHAtwJjqdXioNK0UNhWm5fyg0rQaiEz2x5JoS+z0itJ/8NEYnPnvQHjPa+ZFLQcfo5kMPcA==", "signatures": [{"sig": "MEUCIQDY8oltwmxYzJtS0J2Vii3Yj/v+vS3ymzzB0JRdUeIbWAIgZPdTem85br3dXirbMWEt1xmV/Ln+uS71gDUrnif48jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiACVyCRA9TVsSAnZWagAA1cAP/2TaSOpwsHbL/4ffgZvl\nI8ATrDU1X4tE0LklOWAIorPgw5bF3kWkcVhiiRv/otsOVJcbe3QMwHE8cHbu\n2a0Ph30XZdWWnKzGWlWs4IK334Ltlkh65E3bWYK2yHxiBbaufp8S33e1flTA\nLJ0XWWgys5WHG5XIDzUTo9M/YwJ0VHUC/4LjIsmHFQTZbLg5jllxhQFktz6T\nAjbRb0wDcmCq65Rxbam0BxXGesg4TAW4Oyy5+SMHrFyl8rys3V9uNNSpU934\ntmsB5fXSkrWQlYaGXFegZpr2hyxVavGFnKozud8Vo6HdpvtfFqJVIIpswzVc\nfsAhR8cVr7VWzHkBCSlwU3exRsGX645blE4FwldBNL7Qu0pHJtZjzuNXidPk\nUNF43bQEaJ8pNlD2qJM757o18X6RAVKtGEjQEkEM9fdTI2PAYOmLagsWNqpj\nVmbI9N4wXRqmkzDfHEwm3TQv1RDgF+4ga2tPX6bdYbolxf95u5F3Vs7nTLoG\nS0vlqSZeKEg/q+4OUZu61BQwYDjilpoIicq3FP0cGpMmCQnjn2p7nlchHQUx\nt+vA0K01CuCoWCtJTHIxND0tk3RJ2si6ACw62QyFYsqCHV5Sy1b533b95kXl\nd+BH1oEIsNKsseCxPIKnyNHDtPIVU5X1NBmSAw8GXWw4HXyyEgQ38NFRxP5l\nlxG5\r\n=mM0b\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.7": {"name": "react-redux", "version": "7.2.7", "dependencies": {"react-is": "^17.0.2", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.2.0", "jest": "^27.3.1", "react": "^17.0.2", "redux": "^4.1.1", "es3ify": "^0.2.2", "eslint": "^7.32.0", "rimraf": "^3.0.2", "rollup": "^2.58.3", "codecov": "^3.8.3", "prettier": "^2.4.1", "cross-env": "^7.0.3", "react-dom": "^17.0.2", "@babel/cli": "^7.15.7", "babel-jest": "^27.3.1", "@babel/core": "^7.15.8", "babel-eslint": "^10.1.0", "react-native": "^0.66.1", "@babel/preset-env": "^7.15.8", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.26.1", "react-test-renderer": "^17.0.2", "@rollup/plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.25.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@testing-library/jest-dom": "^5.14.1", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/jest-native": "^4.0.2", "@testing-library/react-hooks": "^7.0.2", "@testing-library/react-native": "^8.0.0", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/plugin-proposal-decorators": "^7.15.8", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-transform-react-display-name": "^7.15.1"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "dist": {"shasum": "f5edd4e4bc34ec8787451d77d16663abf12f8be9", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.7.tgz", "fileCount": 89, "integrity": "sha512-kpstUHhXgT5HOLwzoRhDr3AWHO7H5mgTN5pX1H02OuoIMaZiOLYlul8vgan2WE8eEttAEMew8Npgzd3C6Asdow==", "signatures": [{"sig": "MEYCIQDXhyfAi2ptCNjklcDwjODtUjGU+N5SCkcWaW6WCzt13AIhAIaXhq0dQIWCcNcsFEdj8FVoSZkb4xZyzcFxwWTnBAmQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRbcYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMgQ//VSnM2U9SKQshnkqkgt0aNsfCAsKSmZxRQDGiWDiSUthu9ijI\r\nFks23W3NX4jcSVN+Q2Rt++RTCvW50B4814xDln14ak5ltMn4mD1KiAjzfMRe\r\nMXL+xiqbn0XAzdFA4RK9OB3FIKjtBQvE+ouy1TB1uqPEaHXmy3Lc20hx81pN\r\nsSX2Lc9St6BynvlTXt5cpyVoH1+haUkWsPmmPGJz/3SWWrBs6iIZDf3xVmCk\r\nWI7F8pZk1sRg3vAK0rftY6hsieLF0Q5+ryrSGa0uVboYZ/Nh3DS+BylfvJcz\r\nItXrIpi17ASpolv2I/hw1nvadP/Au9+sfXyZrUZywXzpo/tjlvq2auOG42Z9\r\nSSht1sKKZwCCMAz8bsz/21NwRUEKc+O4P+fwSm3tZNkMN1CQf6mtF/TXacwH\r\nEa1wYvVRJ+plUk4F3Yp4BBbZC9opQcIro5ZN4UTQeF82z0ki9JLV3Znxmc/5\r\nx0gIXM79ZXJY2htllmCe0HIjfnPv4aWz0UCexw3ZP2Kc+YB4+QlJBdG7WR1Q\r\n3gjELLeM/CaodWdsh6N7nZ0QSeedP1aXHC/UIwIr30v2Onh5XZx6zgyYbZWX\r\n/3EYyJccUjir57V/3SPsF4TLl678202or9vqMzPuJJniQcBS7deNg+prfox6\r\nPBLR39GChuc7QFUP3zTGp23LdUpbBV5pd5c=\r\n=2R7S\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "7.2.8": {"name": "react-redux", "version": "7.2.8", "dependencies": {"react-is": "^17.0.2", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.2.0", "jest": "^27.3.1", "react": "^17.0.2", "redux": "^4.1.1", "es3ify": "^0.2.2", "eslint": "^7.32.0", "rimraf": "^3.0.2", "rollup": "^2.58.3", "codecov": "^3.8.3", "prettier": "^2.4.1", "cross-env": "^7.0.3", "react-dom": "^17.0.2", "@babel/cli": "^7.15.7", "babel-jest": "^27.3.1", "@babel/core": "^7.15.8", "babel-eslint": "^10.1.0", "react-native": "^0.66.1", "@babel/preset-env": "^7.15.8", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.26.1", "react-test-renderer": "^17.0.2", "@rollup/plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.25.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@testing-library/jest-dom": "^5.14.1", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/jest-native": "^4.0.2", "@testing-library/react-hooks": "^7.0.2", "@testing-library/react-native": "^8.0.0", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/plugin-proposal-decorators": "^7.15.8", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-transform-react-display-name": "^7.15.1"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "dist": {"shasum": "a894068315e65de5b1b68899f9c6ee0923dd28de", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.8.tgz", "fileCount": 89, "integrity": "sha512-6+uDjhs3PSIclqoCk0kd6iX74gzrGc3W5zcAjbrFgEdIjRSQObdIwfx80unTkVUYvbQ95Y8Av3OvFHq1w5EOUw==", "signatures": [{"sig": "MEUCIQCz1S40HIJdLjru+QjcSjITXej2rvOpJCCWG27tNm0TTQIgJmrvuf57T8u28BkKDJYbK9KK/QUkOlt84/BAyg3admY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRwa4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi7Q/+Nv8cnD4E5txT0uegvaW8aj/QweHIo3YAbXRyKEDpATRsZmsv\r\nRNYf+i9xASw7tHIwts+q/80dJoQK8e0+IMXz9dJ7qfjvqCfQVkVhn+6RNzaF\r\n0dD+JcOrbqBdMgiwJ+Ef1qJqNb/ZMxSjL/WPmJVJBmoSscIeyVr18S59HeWV\r\n8A07hLujIhAarNyvTaC3iBzaVO9Uysi2XnkVNawe29PHJwzuKULOvWvBJlK4\r\nFPyXAHvUwVEmMcyFzEyQyRJFKd0vTCwDZPL2s9Cj7zy+5G66dYlXHbbNQNzg\r\n8iBOZrNvWUWKcj96lGg2mfBnMCB52yqdZRzkVMrpl2CIKHp1KWOXcpsgMq/7\r\naY4IwjklN5jn2hywWOzFdPCrl3vD93Ed8hpDYnsDjoWm+kF/HHbDYhJzn0xk\r\njAdb9VADmlhGSVW2FBfvPppWgi52lj4zY0wzhGTIqoSHJF8x2eXGexr87cId\r\nI40eSzmwLGSW+/uWjx5YHvqmCA8CRfovwGlplYrPlFvbT1ZrCQ1JG6yw6Zi0\r\nDfiXMkIwmGlBLYr87IvQQLOTPHW++TLZlqF32ZsABz1h9I8SYVV8Zwn+b1nu\r\ngxdJVhrdLUFbylaJDVCptf8B6lInyFeBykIFDQTZ3b27iF6VYk5gOCGgM16y\r\neZTs0KY8dGyHib8DvHQOEI89x5zdaZ+NbIo=\r\n=XIE/\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-beta.4": {"name": "react-redux", "version": "8.0.0-beta.4", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^17.0.43", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17.0.3", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^17.0.14", "@babel/preset-env": "^7.12.1", "@types/react-redux": "^7.1.18", "@types/react-native": "^0.64.12", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0"}, "dist": {"shasum": "28558009772592eb447fbea041bebfc4a0d78771", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-beta.4.tgz", "fileCount": 125, "integrity": "sha512-VT0u1ZFS1UWtra9iiqcIUZkPtp48Qajtye3ffeva58mBiEMttHfaD0MmEiL6e4Vxh6K8e/ftr/WTvRhRTGTALQ==", "signatures": [{"sig": "MEUCIEnK9Qs085Cud75lysCM+dnRwbdRGkUThj/X3VbnZwEUAiEAkxkMSxtU4vZfquMT8MtGEHJbBuvcBAQd57Jj0gn6G0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSL4QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmhBAAmPBjBQ077QBTRKwkQmIdUOJAwsNYZlralZ6daCZTjqYDkW1S\r\ndE/J/pm8Zjw3OjJ+cwq5fpC5GzW37aMfX1OPTBsgWQtWvzwHE1lAiJCXpG7T\r\nvKXsSk+g+ARLqPA5aLQ8ZGTPjkjF8GVqtkZXhcztp6jvgjQVHFy//Ng8sYMj\r\nl4cTNobnklF9bBomQONKl3rc5r6p/5fczMg+TCdKon27LRRVanIY+LCHDiVa\r\n3UnOM3INOkpB5QZcdCWYnbIxLKIteRQbV9b7l47a/jbqNK0cir3HwsriLuaE\r\nOximTZs/XOk95NwC8HEmRdnc8gkGDBP3HTLTp+uzx4HJ4L3KNAAcuohi2DVV\r\nGUJ2Yd9PN0CNkfZGasEaZf5U0HOXIBya6csPdHHGh9vqcrdkyJ+xHM3CC8V9\r\nwp2MKWr9jmFI6EnOstQQYwKnpz2wWOTAEZap7g0G1oSWxozuRUD/wVPfJhIp\r\nhn+xM0FG7DtM9fB4vH2UybUxlsi+lJGPhta1Ui33jkRpmFN+W2qB6lIV5GOg\r\nH/8KYqMenhZj4Qq8vCEPfjkRDDs2d4KRuzauIE+EAul5ez5jwXHVQX75RKHK\r\nPKMAwo0Pdes+0JEvJyLuEFVsJg6v0m+D5RoXrgwHxSL2tdrjyCf9m2H6ztbn\r\n3swDzuZZFqDwRkiWeHEYMzrhMiudT1rkRPY=\r\n=1ke8\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-rc.0": {"name": "react-redux", "version": "8.0.0-rc.0", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0.0"}, "dist": {"shasum": "78db2b01b29cbedfe305b99bb2ef2645deae5697", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-rc.0.tgz", "fileCount": 125, "integrity": "sha512-7o9XEvzyIg5AFN/Q5nk6xfqA3G2FeoTBKRYf4iT4WAB+XCEGiCPHUYmuluQNUNAftOAG1JUh6dktpfviKFdloQ==", "signatures": [{"sig": "MEUCIQDEWsv5kqZ+1oH7Qq51xXs5AL/z/B4Eyy+LWweFAKsonAIgO+Wc2b691M2PMd2EDMTxEwvWDVcSciYFFGlzUmhcCKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 313880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiU2R6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0aQ//YyT8c5q/8X3/srfd8Hzq1HFioNdRnRaoxpNRdeAj3SpqFXbx\r\nll47r4lNfnEa4zuNBfvb3WwzhVsIcLUYkdDexRuvv6tb53si993DAU1ZdIDV\r\nRfO4f13ywJLJZNW8NhPSKDnb9I+zqrGjXYQQjHTlPjukHhtD+VF4rmlbx8+R\r\nY0+2FUzDUkqkyJh8YqMwwrcvlhH9mrz53FeamSS0iXBrJDlY3NkLzpffUnhq\r\nH6FKy7Sso6j7Ngas9k37yqfBO92qGWJETlrRYZ6GwbNk/Yi4/5qiWy/wfrs5\r\nZoFyNmlCljX9NN1L0WLDJ4luLb/GQ71AfMhBIASnn648uZRPDwOILotFmAaz\r\nR0eU7H7t1bN84t39BsErfLmcYFXMwIe56HWkMaS+pfA563pFIP698Yoy1Hia\r\nxiyB/xns/JZpluclHMDNkbwPzz+rDzkI9AikktbSzekNcmuzbDlgKDzYPHZH\r\neXrIH1we2A23pGt0jQg9naOG5GJjxN2WTNBc5YkHUneHlmRGLwpBOJtxiDtT\r\nl/DoN8IneFljcR+jsqQJov25/66HHVGDk2l8P//QwEkwfIseE6ETOcPTXGX8\r\nLo/NwcUP/tSpyydrPXD/q9pXPTkGm2KyacauX/ev8eQK4CVmIxxIEjNoZRsj\r\nfTK1zTr8abLkoP0xLEDGIJJVAuuNc1pFNlI=\r\n=51jV\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0-rc.1": {"name": "react-redux", "version": "8.0.0-rc.1", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59"}, "dist": {"shasum": "6aa19fb196dd78a0330a855d31e28993fd43e670", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0-rc.1.tgz", "fileCount": 129, "integrity": "sha512-gGUFn/8Gx+8xp50G8tSEoc5SNfRSgJwDXfqbpq1xoLBSeezmUuZrH7IfyP29mY2HTkWx2gq5qSSYGL5GBLxE4A==", "signatures": [{"sig": "MEQCIB1SinX/JUN17CLPkNbfgibSWvDS6bUiXHM6V7GwfFCVAiAtrG4eiVYb2II45BexiWRlAmCZhCaNezeuFOmA1rWFgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiV1G2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNag/+O7oEA1WLzSdnrbM9V3caxiXC2hxVrLAXZ+MaoR0qlh/GolVC\r\nBB6zGxlR1Fja1l7gsJ3zwI79JSh2TssCjwu+f79uo445312A85fT5DLy3tb/\r\nvpa2PSuCmqtcri48I2P4AEJ1iEK7LgUISiBAaaavW0U6IIQX5KZghJdBxJz8\r\ntuUN2870y3Bw8tUaoUwYleBFBnLF9akK2YPKZt5yq8yo1gPf/cIzXQlg1T+W\r\n6zL5e61bvgs6x08aYWs8vNR9eyBB4BIzee9W+EMUd6MRc5J6DsYUgqDTHnpI\r\nryFFZ/4aeRnI3oOylr7kWsUllx0A268y9CT4FUfQvTyBPPSt1q8wcbYFXmLM\r\nc1PWZ9IUDqiu2E7ZAFmxGGQS+BF+pFhfn0lY74a17SBY+AQ0/P9PYlMfa0Me\r\nEXrZ8wW5R/2bUP5pqRdNnlYXQEjS+6hd9SDcOdKTyMiDirEgzSi4ik2xS9ti\r\nqVnSn+E6NYqCy9Hgdm1Idj45ayFXqHoQgm1vBstsHo3z8UEo1dEu9hmyZEFN\r\nkglkROOUH0zRJLu4tBefuTkj+0tvXtT2TQ1C5JywWyJw3F5YTrNv278NrkNG\r\ni9HL+F6RBniwWB42sgoyF2W6DHDADCKQA75YeuZvTNELYQa+DdAlfk7XXe57\r\nu8TiVWnaCjIoXeBz02+J4eKTCtcKTJoOgyo=\r\n=ECbY\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "8.0.0": {"name": "react-redux", "version": "8.0.0", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59"}, "dist": {"shasum": "7d98421e48c7a19c50a826d1a19ba7729c82a184", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.0.tgz", "fileCount": 129, "integrity": "sha512-zBwWgosy/MD2KKr1CtZyAzAaUa1xifJNt2mNszaBF7TtNlst5dooofVz7Djo7cxxkYZn+010Fqef/O4yxlW3cA==", "signatures": [{"sig": "MEYCIQDzZQPQ8ENAWTuai2F2oDgip+XrCRfyiNsHcFW4UU1tCQIhAKGWFyOJuhuFGm3hUJaNfx+AMt1Pijb1jPFdtp/aYLPn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWv1pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogUg//W0tqec+SDDK0rOpz0nnmbYffnb/2cXyNvj9zDFgj/pOssktc\r\noEExrzW0q78wYYB50Vy8FEm3Uv6Y5cFXplkeClAr4AAy8kfplfgwoELuWLzp\r\nlAAgrynynBhed5CWw17jqYSKyPl2weZeOXzsNNjRVEB1HOaPW0DZXS2e8KOn\r\nhD+xfBCMbXojF+x+oMEMddq4jS9005WHqQ/FKJ8SjuY7TvRoXCjVhdu9CvZU\r\nV6rNvP6RQycAYDsqdZGQxP7xDGjD8szEyU0526WcVkj7SxohHpnemm9OOQMC\r\nRE98qjZoGWWHxnGk6hbZ8rYp0eJr0QdQFBCT7/IhjDcpDSyWfe8M2+DeZtrB\r\npiw48LaK5UtFG8ewNJXhlBuGAQiLMnWpy6vUfDiYd+kJuTpB8ZmqCke+tfbT\r\nEuWHIOXWweOks5PqND4XLAttrFXJ4ktLnY+EKRaruYZ4PbaW8Ig8MaxAALlv\r\nx8STQYNshAXSiq3fZkHUN8i7x3FdD1fI+YVvUOcuHG23zhgYGTbzIjnSJwnm\r\n9QThWu07Cus0C3PZ2a9zO32u717wNvtUtHn+xTXKiZWxWuKOAmj7453RmlzO\r\ndIBKqsgtc8yp2Yj1dNJpSBlLPuE3zQuQL9tgnRpca9B05XBr5C8jyZP7u8en\r\n1X3FGNyagAWWBC8Fg4NSzQSwJWUX2kenuMs=\r\n=2ucK\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "8.0.1": {"name": "react-redux", "version": "8.0.1", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2bc029f5ada9b443107914c373a2750f6bc0f40c", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.1.tgz", "fileCount": 129, "integrity": "sha512-LMZMsPY4DYdZfLJgd7i79n5Kps5N9XVLCJJeWAaPYTV+Eah2zTuBjTxKtNEbjiyitbq80/eIkm55CYSLqAub3w==", "signatures": [{"sig": "MEQCIAXYIouQAOBXv2M+E0p8R6XHWmkPnCLxmHqPRDhevm8iAiB37pLXNt8bNLDkK9cx5KK1pDKwV5mvf+yKWK8ziNlF6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYGoiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqnxAAkYWP7qk71ghWujOx9NAcqdTC+DtRbPyYFOcx9cHb5nFbFAsF\r\nKbctrhd09GTMe/tzE6aHVu3V+4SnjocQjpNUDWfWGkLiHRQ2akogxFDAd+tc\r\nAm44fR7ZzdOihe0+rm6vuqi9GXCJm/b2LHwRGrM9NN2tZ4eGC0gSbSV67kLV\r\n1cncw7RPpZVneoEbuspKEvCMTBJ06glc72EEVCoa7Z49ig77roJyxdXlb97p\r\n8u37vh3JqasgcAnoKKXlSMyTgQJbH2T0X6qQg95Yh0lNvnmiMZgbdPjQ/Lz5\r\nMQxPwyuE5meUY4Bfsebv+m2Y5T3mhcrtIwABfPdhHt1jjC2Q9pIdoZ5NJL+E\r\nWBhfIwwFLsxYz9HEq7Aql+1jZbOcz+zObP6BMLDvGtl585aNLuKePlo/fjru\r\nsx06vIMLNgUo+AFLTeWNsuuO1v/iRTcrOcxISyguJGCOGuEWRT3xBfmDS+9Q\r\na1onvWdqSApGYZ/a0Z1H8tXac1WG65snxhEGY2tFPE6I7z4m1jVMY2hiDsu2\r\nyJdfD9QJMSyzmyX8cv5G3qqjfx6vv4rMb8vPMV1DqLL/ekpK37IR2BzxfHB0\r\nNOmV5N8SJoiui3svdVJwp3YwqjHg52ah1/QzAYHzjXRDHFAS5bV3HPuXN9K1\r\nmblGs50xaow87eXImQ0LEWn2SWB4ZxapMgo=\r\n=GrzA\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.0.2": {"name": "react-redux", "version": "8.0.2", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bc2a304bb21e79c6808e3e47c50fe1caf62f7aad", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.2.tgz", "fileCount": 129, "integrity": "sha512-nBwiscMw3NoP59NFCXFf02f8xdo+vSHT/uZ1ldDwF7XaTpzm+Phk97VT4urYBl5TYAPNVaFm12UHAEyzkpNzRA==", "signatures": [{"sig": "MEYCIQCYdF7tTI31xbksCvhewZTPwrIthXG+Poa7kG6mBVE4DgIhAN6maQyCaxqX0xhY2c/R7bloIZ8U+KTcGQYIBWgMu0Kx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 312056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiiozZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb2Q//WPOozkcmgenBsyA7PD5ZvPqMQoVMop/cQdTtrnEwMr2RYoV7\r\nBF6JxZUi6wCupQRj8IerkeghrxAnobN03m1YfnRsuoLKAv/yiRJZ4PIqog0N\r\nWgwJsfEHHxcDptlotiF7PdkT9wPcPkW5UnM4ucOe1m0igo3v3poQFW3fgxI+\r\nnpsv4kCmZrTsSJ5jKG6eXK2/tbK3a/Ls3ds9E3ahTbMLdTtMNr9G/EH23ion\r\nblm7TKqQ7KY+eJUnxddurzqENkJvybj2PP+ClV9EUBzGGWtRj8M7TRyfF2Bd\r\nJnI3kkMPdhWOexjUnJ+wQ1F89BC9MqOijHViGDrCbVeS2czgvop9MHLdbPND\r\nlyZC3c/i1I+/Q2Vg2uVGtmev9WSphVmNUlJ28fRTAMTbu5+ilqiXB0m/Dd2r\r\ncYOAuPX5SXdRTi+m0u3bhuCSiBp8pVgkbeU6769V7ULl3sBSb4G5fJGhucRb\r\niTK8uLLcidc0xMjE/Ykc0LwCI3g8Jmup7TjtC63gKdUVxrDpaStgwkwc5r/y\r\nRKOv9G7iFDNZMjfcxVg8B7TDHJ7u4KvaDMLy9y87+0ANMHhmy6jSHEfpgSgy\r\nt2YKIGAs5RFSSGBPvmvTDfZEWjhuerng/yoP9I6PmesOq7nnLoll2PNVOags\r\nlhPSctL5JL+R8vZb3u6mTnoNb9QnS81DvjA=\r\n=/3fQ\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "7.2.9": {"name": "react-redux", "version": "7.2.9", "dependencies": {"react-is": "^17.0.2", "prop-types": "^15.7.2", "loose-envify": "^1.4.0", "@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"glob": "^7.2.0", "jest": "^27.3.1", "react": "^17.0.2", "redux": "^4.1.1", "es3ify": "^0.2.2", "eslint": "^7.32.0", "rimraf": "^3.0.2", "rollup": "^2.58.3", "codecov": "^3.8.3", "prettier": "^2.4.1", "cross-env": "^7.0.3", "react-dom": "^17.0.2", "@babel/cli": "^7.15.7", "babel-jest": "^27.3.1", "@babel/core": "^7.15.8", "babel-eslint": "^10.1.0", "react-native": "^0.66.1", "@babel/preset-env": "^7.15.8", "create-react-class": "^15.7.0", "eslint-plugin-react": "^7.26.1", "react-test-renderer": "^17.0.2", "@rollup/plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.25.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@testing-library/jest-dom": "^5.14.1", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/jest-native": "^4.0.2", "@testing-library/react-hooks": "^7.0.2", "@testing-library/react-native": "^8.0.0", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/plugin-proposal-decorators": "^7.15.8", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-transform-react-display-name": "^7.15.1"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "dist": {"shasum": "09488fbb9416a4efe3735b7235055442b042481d", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-7.2.9.tgz", "fileCount": 89, "integrity": "sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==", "signatures": [{"sig": "MEYCIQCCK2Q2LgJGaogMKYryIjvuCnIrgtpONQILtatCDZaH0AIhAK+ze5Lj9/Gc6RYjliCjBBEvBfqVqWMN/XZ7RWfEvU2K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLTWzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEDA/+OQ0In41MJCelOD2n/wqpDRy6L44/8k8yp2KSEuymmaVjoLB9\r\n6cBa3oVUtv0Z1vZ2AsRs7dtkmSpWxEstctHf3pjCfWR1mVDOCQGgHjXE5vlX\r\nr7NyFeQcXtpkiFGnjhEc4rmMH37Q7bnk/Ez6tJNWQbUGfTqU8fTfwW2HYVdl\r\nonTxBYMHKWx3RLdRFVajlNWNd43VH733wAM+cZJQIIR7+qCunvnbpRxhiOhw\r\nX8WYM9h7YMvj4duljSi7x6ejouhb2/olVdkYD6lXNd9UJroNa32fldFqpmp+\r\ns4bzPPyaJv39XOi7eVfKyXmj49IJ/nyFIcx8qsmP0AuOzF7+Bd/i8AUwcrUO\r\nQnkY5pjAEk1Chrsv/2ZHungr3cOiIRt1TYAnIZGusdQv4I9YcpVozKjfuWFL\r\nBjPa5tscOG1fF8yEzoI/3IrUqq6MdautTv1c52ODjeTBrV1cDJx2HpWw5U/w\r\nFMx8qQI7Yuuw1t6tanOL/migz5xDOkmqrpafa12aUyrs5+4/jmx256H+tY2Z\r\nW+lgEbnAnMq2q+dy0Bg+y3p6yScZgUB28+Fksw05bfdQq/fdtHz/unoq5Ufb\r\naETokqFv5zJjvIV/58sHIHv8yP5PxeXK4Nk4dQXMtwbDU6y42vJfrWUd0E1A\r\nDjB34wA3+jOaRro3L8EJVXETnZ6VZXkBz7s=\r\n=ahu5\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "8.0.3": {"name": "react-redux", "version": "8.0.3", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a00d3efc04e79dd95cd9a91385ab6205805f0888", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.3.tgz", "fileCount": 129, "integrity": "sha512-3roUGeIweX0tKbNteUYW6h+tlA79uyhLjMUH8hyYfoveZ2ZWKKfQJSNYFei3CfvVWo0x4tLQYXHdC4Q2OG2ebg==", "signatures": [{"sig": "MEUCIQCKfeG062Q81OHp9FmhL46ZtVKC+BJy09T0WhEQ1AdsxgIgSkJuUOCvfQrzC0HxA6RzvOCDf1iVIVi3iMvXh1y8rvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 312517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLTyTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbKBAAnWEuogXKePs6qj8uTo7Ii1X/5r4aCah0GMekK8gYQ1HjRpgA\r\nwBGlU4bJSZLmQwFZ+xz5cxn6IopCnFweRREuvnc/hi+lYCjpbuJL5bVa1qVN\r\nixRlSIUmT2RNA1bLvuPR6BFxis2z1+ru3XFlnPs0Duf2Si0Md5TU7tzcfB0n\r\nuiL/qgj2xLUFgMuuTKki0n/EKSrqiKItiwUfhCFQ3ynWVUzbQ7IrbI4fCS83\r\n2j0bfmkDZeoLRr8QqKveeWf2iw2nILbEaJZ/K5ixNUd1/gEZoN0mKsJjqIo8\r\ngsGQAIICBC7xE+yCSf6PQjYgE92SF2d1MlWKb0Jpm9kYnXRzJPFrlyAz+vhi\r\nGy+KWy8brs8gC177DB+8xK3NQiOoZLJrtj479aR5LzmhwfljwPJKGrXvuLOo\r\nNygbJ3mQgV0YY9rsFlOmD3BqM6j7xWZTSSGxrR+BOb46vV1axw3ZgCvdSo+1\r\nMj8yLetqldMaWQQ4ku5iKBP8Bi7ssnqXcXnNC+6Nb/ujYoHPU06+GBhVEKXA\r\nu5CCRxz2RHqgD767lPcqRQL2oEDmrvbFtx4YOc2CVeLtFZ5TaBUnv1WO7Ie1\r\nS2unXMpZE7lupnEbuTiekg9BWrMNofwuzoX6R5WbMdA0mp6/shQDP/nJZAT5\r\nc60YNpbDbeIQ3oVgiVg+ulnzSD3kmyj0J0k=\r\n=U814\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.0.4": {"name": "react-redux", "version": "8.0.4", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "80c31dffa8af9526967c4267022ae1525ff0e36a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.4.tgz", "fileCount": 129, "integrity": "sha512-yMfQ7mX6bWuicz2fids6cR1YT59VTuT8MKyyE310wJQlINKENCeT1UcPdEiX6znI5tF8zXyJ/VYvDgeGuaaNwQ==", "signatures": [{"sig": "MEYCIQCHUH7qCwNEARdBulWXWdT7xrpYetwQF8HhNWuJQbNV0AIhAJ9NICZo6G5SQnPUQbhmNf//XnYrsUzyaaf7OLWevj8c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 313093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLT6OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1eg/+M3QeqWBDqYBqb/7lEl+aQmNJgL376DX89qDG/Rp8PhQIG0xi\r\ndQfzXlddZB2UcrFHCuq4W0nPFCJ+z9I3w4E1SaTsWWRxzvsRWy8X6/LceSir\r\n4mwAazPHyqBCR243aNdb8urUURiABt5lmMf9EYV+B7DhKz5LhYgTGPhrTKV0\r\njUU9IklOb5Y1cxyGe7PCWqDuc2Hn0qCN+af10fJfsk+JMCmlJ2JGdAHodD0D\r\nxo2yzj61CE7p9FX3AlwMQIhGkGLcKN2XZv4o1t43hiyqZl0kkBwTTS8cqfCD\r\njwVc6embKF/LaDyRR5xSOOV4MqiAMVxxAPS9AMrkXj3tEZn4TJFMBgEXz6gq\r\nT62+zQ0SsZj4p9HnPUbNf2SpMWrR73zriTx+hn6mwtVyMBkXUJo3urh0xdPr\r\nQC59UkQSCGCO0txbkcBmJa82Zfb93jZXfbrMpHrChQByqDe+o9gclCk7af3I\r\n6gbE8LHCdXZ8kG4kLMAtFlK8QFyXqusGc0gNJfLtD6Mg+cr65WoXlJXkmOMX\r\nWgnsIs5J78lvA0fs8Y8e4hwRHIdwt2/XVY9D7ENp8FqlUnL/4eHT5gGYz2XY\r\nxCZZJ/muXdaffdQUMQp9nbsTs9WHBWiCkrTZgqxj/c/wM5zMgGG0j5UCXFIZ\r\nrXJHtAvedziGf7xgSNmrbh4reBqlt5lgK+k=\r\n=pOjb\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.0.5": {"name": "react-redux", "version": "8.0.5", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.6.1", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5fb8331993a019b8aaf2e167a93d10af469c7bd", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.5.tgz", "fileCount": 129, "integrity": "sha512-Q2f6fCKxPFpkXt1qNRZdEDLlScsDWyrgSj0mliK59qU6W5gvBiKkdMEG2lJzhd1rCctf0hb6EtePPLZ2e0m1uw==", "signatures": [{"sig": "MEUCIQDJTzLrzX1r4siFYIH+MqgUNGXw/BTyW5mAcHxhrZLyIwIgY+bHuFnQhZ8CttLiPBZ4yCmFYPLeiFm8NiBVW+Ov+9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 313906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZHtbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0hA/+IlpGR4A07MigXE87iuOWfJ/QNMpk/qgGf72bbJcWsz/6Yd+n\r\nGkre9DEvo7T2GIn7caLBO2YnOM5zjvyyZmFfbgJIjYkqivBu2YtFArJpF+KH\r\nViDDh0PZjgeDg7e1YHhkBtkiIWgZGn/XajWAV+0Okj5j/NtOurOJE1nNYtKU\r\nt5SLq+k6ySdKt/dn6U/0SCWG31zLfwKkhAU7WMLc5ZBa/hy6tkakhhVzbz4z\r\nFvcdauVtS3b/+fMfU11oxdr5uoBePKkRdW6fUVd+4Y+i+Gne1GGpXUp8PLLo\r\nZM+EPU1mixoyIM8B3vkgW3JMkK+TNbrYIbTnULXrPfqkAJbUihHmqyFzpYDQ\r\nP2UNXrgQ7rm4wojyXUKqpErxNjacadrfsh2kDETOnOh9CWJ6V1bMLPR2H0iq\r\nOOILQd5d9Dy69tPNgDbLTa6IrlByqiEaXRiUidggcmoXTSo1QHfBHBIIN5cX\r\nwyNZFmZtz4kH5sqYmeJwtFXLM+gDTgbRgHvu4Cy1CdhxDxXEhgUDqbOSPHFg\r\nNEOE48jUMN+3vP/BVC+JF84PA/4GEAaEWhOXiCCIviXnimFNDc+Y7Nz688Lg\r\nVqYtO/TrT2U8GavfM8b0AErpoK1bgANek3LSdIiWq+dQtjeC7Q0I8U8WgnIu\r\nQwN8IpETVflXn+MfH/N+aUf7aw2n5wF2MOU=\r\n=xqST\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.0.6": {"name": "react-redux", "version": "8.0.6", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.9.5", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4 || ^5.0.0-beta.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@reduxjs/toolkit": "^5.0.0-beta.0", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "284b30e1ecd82486efa09609cfba1e8c2212af0e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.6.tgz", "fileCount": 129, "integrity": "sha512-8ESKGePH4tGVSFhhcFAnLkYZN/hdt7H7QGukR9VOu72v3E6jv5n175Hj2CSF0WSQ9xxGc9G1EmB/cdxLivg4DQ==", "signatures": [{"sig": "MEYCIQDMUZfMIpfbmNF/dkxbSP2LVWfaeqYgYoW+YWtKeckDxQIhAMUfNmpRcHYsoVKke8Zx8mkSXJ+Jl6Dmln2GWWSygHJo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314035}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@reduxjs/toolkit": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.0.7": {"name": "react-redux", "version": "8.0.7", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.9.5", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4 || ^5.0.0-beta.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@reduxjs/toolkit": "^1 || ^2.0.0-beta.0", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b74ef2f7ce2076e354540aa3511d3670c2b62571", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.0.7.tgz", "fileCount": 129, "integrity": "sha512-1vRQuCQI5Y2uNmrMXg81RXKiBHY3jBzvCvNmZF437O/Z9/pZ+ba2uYHbemYXb3g8rjsacBGo+/wmfrQKzMhJsg==", "signatures": [{"sig": "MEQCIERJaGE6kZ2j7j4hsenQYHASH9CaME7vND/g4/V8GfxZAiAYtopNt9/Ly3kcMbx5kKp/4t3/g2E907l85t0/cKQmKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314041}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@reduxjs/toolkit": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.1.0": {"name": "react-redux", "version": "8.1.0", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.9.5", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4 || ^5.0.0-beta.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@reduxjs/toolkit": "^1 || ^2.0.0-beta.0", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4e147339f00bbaac7196bc42bc99e6fc412846e7", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.1.0.tgz", "fileCount": 129, "integrity": "sha512-CtHZzAOxi7GQvTph4dVLWwZHAWUjV2kMEQtk50OrN8z3gKxpWg3Tz7JfDw32N3Rpd7fh02z73cF6yZkK467gbQ==", "signatures": [{"sig": "MEUCIQCxiVZG1pFX4W1aBQbA7aGu4xptyup7wOd5aUkmRoCV+gIgRWZ7wnIVNuxhu/9wkvz9ojPDoPIyK5brmHQkONMduuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330529}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@reduxjs/toolkit": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.1.1": {"name": "react-redux", "version": "8.1.1", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.9.5", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4 || ^5.0.0-beta.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8e740f3fd864a4cd0de5ba9cdc8ad39cc9e7c81a", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.1.1.tgz", "fileCount": 129, "integrity": "sha512-5W0QaKtEhj+3bC0Nj0NkqkhIv8gLADH/2kYFMTHxCVqQILiWzLv6MaLuV5wJU3BQEdHKzTfcvPN0WMS6SC1oyA==", "signatures": [{"sig": "MEUCID1zv2orKop8n2dsmHkwWeWjSVWGk+yzrCgWCqsW8OhFAiEAxSmgF4e1O83fFpuVUmoC+UbDG6ZMBQ1fVFjSScaq67s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331146}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.1.2": {"name": "react-redux", "version": "8.1.2", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.9.5", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4 || ^5.0.0-beta.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9076bbc6b60f746659ad6d51cb05de9c5e1e9188", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.1.2.tgz", "fileCount": 129, "integrity": "sha512-xJKYI189VwfsFc4CJvHqHlDrzyFTY/3vZACbE+rr/zQ34Xx1wQfB4OTOSeOSNrF6BDVe8OOdxIrAnMGXA3ggfw==", "signatures": [{"sig": "MEYCIQCCTsMvVEeQdwzs8pDT5qn4EQXw8raLrSITcrORfWUhAgIhAIHbDQnodLzriNDDHFnLgWLa1y57+OYGrQPqvm/VbiRL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/react-redux@8.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 333651}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.0-alpha.0": {"name": "react-redux", "version": "9.0.0-alpha.0", "dependencies": {"react-is": "^18.0.0", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0-beta.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@types/react-is": "^17", "@reduxjs/toolkit": "^2.0.0-beta.0", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0-beta.0", "react-dom": "^18.0", "@types/react": "^18.0", "react-native": ">=0.71", "@types/react-dom": "^18.0"}, "dist": {"shasum": "5325a68081301a8a8951be531bf22c9357367640", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.0-alpha.0.tgz", "fileCount": 17, "integrity": "sha512-P8DoS+xFc7GX9fhaTfROEwAciyCAfUvzhnINVzokUyaI8d3zWGoewdzNcx1kltr6C9wPTKZBYyiJMRD4eA69Rw==", "signatures": [{"sig": "MEUCIAaoxrN4JXY0wM8Rs60meVZl15kf6G01Q3c/J/qKn9R/AiEAhuNpJ1eZKOK53zASSU/lqygSk66lVfMqgYWOtfQ/Qxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 734699}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.0-alpha.1": {"name": "react-redux", "version": "9.0.0-alpha.1", "dependencies": {"react-is": "^18.0.0", "use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0-beta.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@types/react-is": "^17", "@reduxjs/toolkit": "^2.0.0-beta.0", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0-beta.0", "react-dom": "^18.0", "@types/react": "^18.0", "react-native": ">=0.71", "@types/react-dom": "^18.0"}, "dist": {"shasum": "13f459f9d7e398d512758f4d60340b5e967342ae", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.0-alpha.1.tgz", "fileCount": 19, "integrity": "sha512-itGPYrvuuoXZcMA/c6VdzaNiklyf0I7/aM9a4frs3uoRm+gSdzkfo35WV3YLN5UgOV4Zsj4T7RfMrupDghCbEA==", "signatures": [{"sig": "MEYCIQCway1h2Y00DFfKlB2e/8U0xiOlmsr1e6ilRvpXe7evrwIhAMjcBLoWkfSh4ff9ayi2xR6W4eYm+wlj9NRYFcDEGvmu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 786341}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "8.1.3": {"name": "react-redux", "version": "8.1.3", "dependencies": {"react-is": "^18.0.0", "@babel/runtime": "^7.12.1", "hoist-non-react-statics": "^3.3.2", "use-sync-external-store": "^1.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^26.6.1", "react": "18.0.0", "redux": "^4.0.5", "eslint": "^7.12.0", "rimraf": "^3.0.2", "rollup": "^2.32.1", "codecov": "^3.8.0", "ts-jest": "26.5.6", "prettier": "^2.1.2", "react-17": "npm:react@^17", "cross-env": "^7.0.2", "react-dom": "18.0.0", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.3.4", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-dom-17": "npm:react-dom@^17", "react-native": "^0.64.1", "@types/react-is": "^17", "@reduxjs/toolkit": "^1.9.5", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@rollup/plugin-babel": "^5.2.1", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^2.3.3", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "react-test-renderer-17": "npm:react-test-renderer@^17", "@rollup/plugin-commonjs": "^15.1.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@rollup/plugin-node-resolve": "^9.0.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "redux": "^4 || ^5.0.0-beta.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "@types/react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4fdc0462d0acb59af29a13c27ffef6f49ab4df46", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-8.1.3.tgz", "fileCount": 129, "integrity": "sha512-n0ZrutD7DaX/j9VscF+uTALI3oUPa/pO4Z3soOBIjuRn/FzVu6aehhysxZCLi6y7duMf52WNZGMl7CtuK5EnRw==", "signatures": [{"sig": "MEYCIQCvFCSgLtdJRwaoIBmMUaIdgs1q7HqDjOyR/Euq4NdX5QIhAJdb2PObyNuMa+Ga7hs1CZZNxXl6c3t3WtIc1x7XW29i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 342566}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.0-beta.0": {"name": "react-redux", "version": "9.0.0-beta.0", "dependencies": {"react-is": "^18.0.0", "use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0-beta.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@types/react-is": "^17", "@reduxjs/toolkit": "^2.0.0-beta.0", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "@types/object-assign": "^4.0.30", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0-beta.0", "react-dom": "^18.0", "@types/react": "^18.0", "react-native": ">=0.71", "@types/react-dom": "^18.0"}, "dist": {"shasum": "eb15e32e4780638e745cda0dbb022bdad90fb368", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.0-beta.0.tgz", "fileCount": 19, "integrity": "sha512-esuJoiavyjA9zV40E9nssp6QJsupMk7092sRh42dYxMI6am6NF5fo6vloRIhRbFgwxrvFB9u6679iokhCkVQFg==", "signatures": [{"sig": "MEUCIQD14GQq5PSqYe7Fm2NOK0Htc6VPXSpTLFux4k5/PVuWuAIgbHRUTxWDUai4u4zf4+R2so/YhtnPTQePILnUK9NcnDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 802582}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.0-rc.0": {"name": "react-redux", "version": "9.0.0-rc.0", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0-beta.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@types/react-dom": "^18", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0-rc.0", "react-dom": "^18.0", "@types/react": "^18.0", "react-native": ">=0.71", "@types/react-dom": "^18.0"}, "dist": {"shasum": "d8ec1089d16c76268406d4a1b916977fd0a99815", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.0-rc.0.tgz", "fileCount": 19, "integrity": "sha512-QUe/rtMgT26nC8CcFiuNFy2CHpRD3nPsxNO09+cgzEKSgPc5K5JGez2nFDnEGPnjD59eoy/KrdH/ekMO54zTmA==", "signatures": [{"sig": "MEQCIGQTtzdSfh2vt5RNqY1ybRBIeOp94mgXvm3k4VaYSHNFAiAnPcFX6KVmK15djSzb8A6X/nNTgQoTuRsLO757iJ/XZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 855363}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.0": {"name": "react-redux", "version": "9.0.0", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18.2.41", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@types/react-dom": "^18.2.17", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0-rc", "react-dom": "^18.0", "@types/react": "^18.2.41", "react-native": ">=0.71", "@types/react-dom": "^18.2.17"}, "dist": {"shasum": "5d3a71926ddec69a5ae726587abb7a425f01ffd6", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.0.tgz", "fileCount": 19, "integrity": "sha512-/X/wiza+G2IXbz1Ow9Gs5qK7KNdoEnzzsR7ne7jcOWJMBOx9wvraPZwFddCs84xqwilvYaoENHowQMiW0axbsQ==", "signatures": [{"sig": "MEQCIAobHhxEM/dGOjkQZF3k7qopuzYAZNn2nWhw1JyjFWixAiBodgXKQQRFtbsDWqkeZS38uGskd5tf2nOk2UoGKreFIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 868865}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.1": {"name": "react-redux", "version": "9.0.1", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18.2.41", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@types/react-dom": "^18.2.17", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "react-dom": "^18.0", "@types/react": "^18.2.41", "react-native": ">=0.71", "@types/react-dom": "^18.2.17"}, "dist": {"shasum": "bbdbdd55d5c3b0e996975269b7d6101b80487139", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.1.tgz", "fileCount": 21, "integrity": "sha512-d+S89OqyChnY2J0O8wv8boRgnGo0tjvxkMLV78wx7h2ZyJvyeOQcBg4yrm7IxY36gxc63iOCfjjQAyhohKWJbA==", "signatures": [{"sig": "MEYCIQDwwO1x9r/a22Moz0aXCTDdEzqoW8I0kUDFlQuFOs/8gQIhAIvGz+nTfIDS3ns/s1ZHPcZZxvXy+sebjUfzxR2W+MIp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1019581}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.2": {"name": "react-redux", "version": "9.0.2", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "^18.2.41", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@types/react-dom": "^18.2.17", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "react-dom": "^18.0", "@types/react": "^18.2.41", "react-native": ">=0.71", "@types/react-dom": "^18.2.17"}, "dist": {"shasum": "5654d490be9abd34b73f369d3c1f89d6b14b072e", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.2.tgz", "fileCount": 21, "integrity": "sha512-34EI42cYZxJF59Iht6RDM5xDun5EdhV8CbJcTe+mYx97XMHLNYA6RrH9r/ZOZX3CetVCYfBEU9oAY9h3sZarsw==", "signatures": [{"sig": "MEQCICOSz/vRwJaa+D0gVGv8Rp4/o0NJPbSNiydXvoUMjtaeAiBeVOti7RoLXUidcN0plULB+GvdkXBUEad2kMr28kM2Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1019937}, "peerDependenciesMeta": {"redux": {"optional": true}, "react-dom": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}, "@types/react-dom": {"optional": true}}}, "9.0.3": {"name": "react-redux", "version": "9.0.3", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "18.2.25", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "@types/react": "^18.2.25", "react-native": "^0.69.0"}, "dist": {"shasum": "3c4d77ed43b88dc15d127162e0d5c060aabcd4e6", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.3.tgz", "fileCount": 17, "integrity": "sha512-bilZStJN00qYqAXBpu/taUXv0tcuOrhBFD86fSERgYUm0+IHi/OQnqopbNalhgSo7+KMjSW5H/UTrWasXDvtug==", "signatures": [{"sig": "MEUCIQD0qBWH8+I5Pu9dqoU8sbS50i9FGvSSjCgVfnOHMoa7dAIgBfTtPsDv9xYALV2jKxOG2QQ3LLNvf+B775Lx60xCu2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727655}, "peerDependenciesMeta": {"redux": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "9.0.4": {"name": "react-redux", "version": "9.0.4", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^7.12.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^2.1.2", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "18.2.25", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.21.5", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.22.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-prettier": "^3.1.4", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^4.28.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^4.28.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "@types/react": "^18.2.25", "react-native": ">=0.69"}, "dist": {"shasum": "6892d465f086507a517d4b53eb589876e6bc8344", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.0.4.tgz", "fileCount": 17, "integrity": "sha512-9J1xh8sWO0vYq2sCxK2My/QO7MzUMRi3rpiILP/+tDr8krBHixC6JMM17fMK88+Oh3e4Ae6/sHIhNBgkUivwFA==", "signatures": [{"sig": "MEUCIC7iBMletVRR8wqTU+xpiX7kr4hS/kLFbD4LatseGUVMAiEAvS8ZPAU3hpvEIyoTdC8rWdJ47Z1AjxlnBVTt9HS4sM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727654}, "peerDependenciesMeta": {"redux": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "9.1.0": {"name": "react-redux", "version": "9.1.0", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29", "tsup": "^7.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^8.56.0", "rimraf": "^3.0.2", "codecov": "^3.8.0", "ts-jest": "^29", "prettier": "^3.1.1", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "babel-jest": "^29", "typescript": "^5.0", "@babel/core": "^7.12.3", "@types/react": "18.2.25", "babel-eslint": "^10.1.0", "react-native": "^0.71.11", "@reduxjs/toolkit": "^2.0.0-beta.4", "@babel/preset-env": "^7.12.1", "@types/react-native": "^0.67.4", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.29.1", "@testing-library/react": "13.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react-12": "npm:@testing-library/react@^12", "@typescript-eslint/parser": "^6.17.0", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@babel/plugin-transform-runtime": "^7.12.1", "metro-react-native-babel-preset": "^0.76.6", "@typescript-eslint/eslint-plugin": "^6.17.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "@types/react": "^18.2.25", "react-native": ">=0.69"}, "dist": {"shasum": "46a46d4cfed4e534ce5452bb39ba18e1d98a8197", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.1.0.tgz", "fileCount": 17, "integrity": "sha512-6qoDzIO+gbrza8h3hjMA9aq4nwVFCKFtY2iLxCtVT38Swyy2C/dJCGBXHeHLtx6qlg/8qzc2MrhOeduf5K32wQ==", "signatures": [{"sig": "MEUCIHSETdlON5taeUpZ3fT137z7Mm/AmqlNvsI7gYqXahxIAiEAxsA+20Qx3HZbPcdfPYzl2kifK7f4XECo1LXTfcbfiqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/react-redux@9.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 734554}, "peerDependenciesMeta": {"redux": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "9.1.1": {"name": "react-redux", "version": "9.1.1", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "tsup": "^7.0.0", "jsdom": "^24.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^8.56.0", "rimraf": "^3.0.2", "vitest": "^1.2.1", "codecov": "^3.8.0", "prettier": "^3.1.1", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "typescript": "^5.4.2", "@babel/core": "^7.12.3", "@types/node": "^20.11.6", "@types/react": "18.2.25", "babel-eslint": "^10.1.0", "@reduxjs/toolkit": "^2.0.0-beta.4", "@babel/preset-env": "^7.12.1", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.29.1", "@testing-library/react": "^14.1.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^6.3.0", "@typescript-eslint/parser": "^6.17.0", "@testing-library/react-hooks": "^8.0.1", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^6.17.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "@types/react": "^18.2.25", "react-native": ">=0.69"}, "dist": {"shasum": "852ec13084bd7375e26db697d2fc9027ffada204", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.1.1.tgz", "fileCount": 17, "integrity": "sha512-5ynfGDzxxsoV73+4czQM56qF43vsmgJsO22rmAvU5tZT2z5Xow/A2uhhxwXuGTxgdReF3zcp7A80gma2onRs1A==", "signatures": [{"sig": "MEUCIQDrypHeln9by0smwbX1RcNskt+hkTn6dZvwtfg854ruPgIgMLUvifnBeD7zfxRROavUDOFmiGm7QN+iOPCLmJvVv+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 738728}, "peerDependenciesMeta": {"redux": {"optional": true}, "@types/react": {"optional": true}, "react-native": {"optional": true}}}, "9.1.2": {"name": "react-redux", "version": "9.1.2", "dependencies": {"use-sync-external-store": "^1.0.0", "@types/use-sync-external-store": "^0.0.3"}, "devDependencies": {"glob": "^7.1.6", "tsup": "^7.0.0", "jsdom": "^24.0.0", "react": "18.2.0", "redux": "^5.0.0", "eslint": "^8.56.0", "rimraf": "^3.0.2", "vitest": "^1.2.1", "codecov": "^3.8.0", "prettier": "^3.1.1", "cross-env": "^7.0.2", "react-dom": "18.2.0", "@babel/cli": "^7.12.1", "typescript": "^5.4.2", "@babel/core": "^7.12.3", "@types/node": "^20.11.6", "@types/react": "18.2.25", "babel-eslint": "^10.1.0", "@reduxjs/toolkit": "^2.0.0-beta.4", "@babel/preset-env": "^7.12.1", "@types/prop-types": "^15.7.12", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.0.0", "eslint-plugin-import": "^2.29.1", "@testing-library/react": "^14.1.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@testing-library/jest-dom": "^6.3.0", "@typescript-eslint/parser": "^6.17.0", "@testing-library/react-hooks": "^8.0.1", "@babel/plugin-transform-runtime": "^7.12.1", "@typescript-eslint/eslint-plugin": "^6.17.0", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1"}, "peerDependencies": {"react": "^18.0", "redux": "^5.0.0", "@types/react": "^18.2.25"}, "dist": {"shasum": "deba38c64c3403e9abd0c3fbeab69ffd9d8a7e4b", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.1.2.tgz", "fileCount": 17, "integrity": "sha512-0OA4dhM1W48l3uzmv6B7TXPCGmokUU4p1M44DGN2/D9a1FjVPukVjER1PcPX97jIg6aUeLq1XJo1IpfbgULn0w==", "signatures": [{"sig": "MEYCIQCaDp1jrpyuPSfAiXt+jBehLZ21shML7ZJ/eznFJDCGHAIhAKmAyunDVHbcIVnl+i51wjO57mhGUHepv51BVqrk0uE4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 740256}, "peerDependenciesMeta": {"redux": {"optional": true}, "@types/react": {"optional": true}}}, "9.2.0": {"name": "react-redux", "version": "9.2.0", "dependencies": {"@types/use-sync-external-store": "^0.0.6", "use-sync-external-store": "^1.4.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.47.0", "@reduxjs/toolkit": "^2.2.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/node": "^20.14.2", "@types/prop-types": "^15.7.12", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "codecov": "^3.8.3", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "jsdom": "^25.0.1", "prettier": "^3.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "redux": "^5.0.1", "rimraf": "^5.0.7", "tsup": "^8.3.5", "typescript": "^5.5.4", "typescript-eslint": "^7.12.0", "vitest": "^1.6.0"}, "peerDependencies": {"@types/react": "^18.2.25 || ^19", "react": "^18.0 || ^19", "redux": "^5.0.0"}, "dist": {"integrity": "sha512-ROY9fvHhwOD9ySfrF0wmvu//bKCQ6AeZZq1nJNtbDC+kk5DuSuNX/n6YWYF/SYy7bSba4D4FSz8DJeKY/S/r+g==", "shasum": "96c3ab23fb9a3af2cb4654be4b51c989e32366f5", "tarball": "https://registry.npmjs.org/react-redux/-/react-redux-9.2.0.tgz", "fileCount": 47, "unpackedSize": 822524, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICyZQP/QeiE3XQT5WLu74+SyfJx8g0/3J49LTEEKtBvlAiEAzr9pqvdELMIotFx7VCPDnrDTN4JLWEwOpSfUN1fQdlI="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "redux": {"optional": true}}}}, "modified": "2024-12-10T23:06:45.941Z"}