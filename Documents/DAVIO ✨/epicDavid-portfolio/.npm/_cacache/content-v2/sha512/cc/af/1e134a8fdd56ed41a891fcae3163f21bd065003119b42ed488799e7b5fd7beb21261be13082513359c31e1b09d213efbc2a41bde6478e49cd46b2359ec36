{"_id": "@types/use-sync-external-store", "_rev": "98-7a27f7fefa70764687565ab1abecac45", "name": "@types/use-sync-external-store", "dist-tags": {"ts3.7": "0.0.3", "ts3.8": "0.0.3", "ts3.9": "0.0.3", "ts4.0": "0.0.3", "ts4.1": "0.0.3", "ts4.2": "0.0.3", "ts4.3": "0.0.4", "ts4.4": "0.0.4", "ts5.9": "1.5.0", "ts4.5": "0.0.6", "ts4.6": "0.0.6", "ts4.7": "0.0.6", "ts4.8": "0.0.6", "ts4.9": "0.0.6", "ts5.0": "0.0.6", "ts5.8": "1.5.0", "ts5.7": "1.5.0", "ts5.6": "1.5.0", "latest": "1.5.0", "ts5.1": "1.5.0", "ts5.2": "1.5.0", "ts5.3": "1.5.0", "ts5.4": "1.5.0", "ts5.5": "1.5.0"}, "versions": {"0.0.0": {"name": "@types/use-sync-external-store", "version": "0.0.0", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "ec2ebe41a1288e3d5d80aacc4a4c2873db4aafa8", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.0.tgz", "fileCount": 5, "integrity": "sha512-dtA1bGSTAyPAcged3AjG9r3BOUzznqgpZkK8HnVrlOOkZunfKnqJikykxUg3xOrBvgGbROLNptpJLflqp8KvkQ==", "signatures": [{"sig": "MEYCIQCkVqZ6SIBN0LU2Re92qEAwjVqHNWZOnkG4D+GKQK4hcAIhANm9J9h69wClqkLAQ5T0rf7KYa0nkozfiAhhzEXNkr/M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3275}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.0_1632247433318_0.5908043896002726", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e60c026f0733250c50357a4a0ee8d34df30b4907fe76c001ea7763313d8716e3"}, "0.0.1": {"name": "@types/use-sync-external-store", "version": "0.0.1", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "c7770934b794d479ff7ed166e924f003b5c6f9e3", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-BXzjGOKNNxDtAwMRhT0TB/LiPmrJ82g5CKS/cGbJUVssMmEFUbHWOOSkHi9gQyAzI934frXfFHXM72affyX0wg==", "signatures": [{"sig": "MEUCIQDnzZNSa1vgvc1Yjpx58EipOqyfy5y9DAkT32vJbn1OFQIgdN2MUq+6hPimm6RbT0YrW0Tmpx+UojLQ2gUpZuAsRPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3336}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.1_1635834707744_0.3529077348356151", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e80e48f22d2eb2d095fb4f47f18af46c4d36cbef9640205c214dca155d2683b2"}, "0.0.2": {"name": "@types/use-sync-external-store", "version": "0.0.2", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "8341da05beb7dc3b6339eaa8a1b1e8a6e3472175", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-gHIGn4QNww3pIUE97gshEqGP+aFpFAg3hQvdD9JHKqlnM+l0Q86gMAyQ0ZjibXj8QY+HaKP8cpIgxHgXt9zPQQ==", "signatures": [{"sig": "MEQCIBgClDA7yUSf+sUaH5+3tvFZtTolc93z/4xRqNhZjYlHAiBg91Zciw0Bk0d2Bcqqn3fSFWILNXxyqlcBpWoDJY83WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3337}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.2_1635847295227_0.5909570804703914", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "83d1728431488d54f597ce0f004be93e5d5daa476b5312fd48ee1b35a0fc4d8e"}, "0.0.3": {"name": "@types/use-sync-external-store", "version": "0.0.3", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}, {"url": "https://github.com/markerikson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "b6725d5f4af24ace33b36fafd295136e75509f43", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-EwmlvuaxPNej9+T4v5AuBPJa2x2UOJVdjCtDHgcDqitUeOtjnJKJ+apYjVcAoBEMjKW1VVFGZLUb5+qqa09XFA==", "signatures": [{"sig": "MEUCIHf5utLQ5oLAqawNW6qgCD7R3x0sbcjUz2dzek20XON0AiEAvMxAonsLimXZLt1zQhxe/7yf04X0KEKUyujFBwKp6us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3608}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.3_1635960729499_0.6147470797370043", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6d5f63f2be11585e2fe2496243ab4b7c3a7dbaeb66092e4b1094376bae9bb06a"}, "0.0.4": {"name": "@types/use-sync-external-store", "version": "0.0.4", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}, {"url": "https://github.com/markerikson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "474dfd4407ca2e28fd4cba90047b29e2575bf59d", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.4.tgz", "fileCount": 9, "integrity": "sha512-DMBc2WDEfaGsWXqH/Sk2oBaUkvlUwqgt/YEygpqX0MaiEjqR7afd1QgE4Pq2zBr/TRz0Mpu92eBBo5UQjtTD5Q==", "signatures": [{"sig": "MEUCIHEnhxFquJyItWBOkL2bwilLcpxa6RRE+IwwpExbVw63AiEAqA/a/zeZBg4WUyECOx02pjGmxe1CoLkERmaNe0NUv6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3608}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.4_1693426555539_0.5403179818348411", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "636ff0e6ba1556ea3dcde43514611524fbfe850c6296cf214874a0efd4136b18"}, "0.0.5": {"name": "@types/use-sync-external-store", "version": "0.0.5", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}, {"url": "https://github.com/markerikson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "a4416edea87d78115c8339f668775c5ba102653d", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.5.tgz", "fileCount": 9, "integrity": "sha512-+fHc7rdrgMIng29ISUqNjsbPl1EMo1PCDh/+16HNlTOJeQzs6c9Om23rVizETd3dDx4YM+aWGbyF/KP4FUwZyg==", "signatures": [{"sig": "MEUCICsQdqc7+tdfqgkWqmOm4XhvZbQCay1l224A47gCXvGKAiEApMK6n8uS+3L0u7BrJDh2s7hZ9u6GSJehJ9G1d6ZasC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3290}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.5_1697654961706_0.12704328596366388", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4e4df9bde00570dfc3dfe23917163f1be729e3b92e3f5f80b8c69eae1e4f912d"}, "0.0.6": {"name": "@types/use-sync-external-store", "version": "0.0.6", "license": "MIT", "_id": "@types/use-sync-external-store@0.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}, {"url": "https://github.com/markerikson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "60be8d21baab8c305132eb9cb912ed497852aadc", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz", "fileCount": 9, "integrity": "sha512-zFDAD+tlpf2r4asuHEj0XH6pY6i0g5NeAHPn+15wk3BV6JA69eERFXC1gyGThDkVa1zCyKr5jox1+2LbV/AMLg==", "signatures": [{"sig": "MEYCIQC38Mtt9gyMgoAJq2g58XdCMkyuRTAPdpGSYwmO/fBBvwIhAM8MAsT96XqdgxzVJ/kUfSrvR38saVab7w44jp0+3Snf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3290}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_0.0.6_1699382068733_0.6380309561794966", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c048e9b12d49a82481404fb3bc099a0aa28adff9fc0d9755b69bbc54901ea2fb"}, "1.5.0": {"name": "@types/use-sync-external-store", "version": "1.5.0", "license": "MIT", "_id": "@types/use-sync-external-store@1.5.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}, {"url": "https://github.com/markerikson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "dist": {"shasum": "222c28a98eb8f4f8a72c1a7e9fe6d8946eca6383", "tarball": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "fileCount": 9, "integrity": "sha512-5dyB8nLC/qogMrlCizZnYWQTA4lnb/v+It+sqNl5YnSRAPMlIqY/X0Xn+gZw8vOL+TgTTr28VEbn3uf8fUtAkw==", "signatures": [{"sig": "MEUCICfpwKD2Kr6G9tnuNnUkGwU8sRv/9uFYhSXBIDXGxGkwAiEAjqCp8T6FDxxeIJPp6xhg0ShPdhH46HjhbgCl8H6PvAo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3847}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"default": "./index.d.ts"}}, "./shim": {"types": {"default": "./shim/index.d.ts"}}, "./package.json": "./package.json", "./with-selector": {"types": {"default": "./with-selector.d.ts"}}, "./shim/with-selector": {"types": {"default": "./shim/with-selector.d.ts"}}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/use-sync-external-store_1.5.0_1743579196382_0.9690788032919366", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "a826746c9dce1a21bcd1d32612cff30605b308e08334d6983422a91a5300d7f8"}}, "time": {"created": "2021-09-21T18:03:53.249Z", "modified": "2025-04-02T07:33:22.996Z", "0.0.0": "2021-09-21T18:03:53.463Z", "0.0.1": "2021-11-02T06:31:47.946Z", "0.0.2": "2021-11-02T10:01:35.402Z", "0.0.3": "2021-11-03T17:32:09.624Z", "0.0.4": "2023-08-30T20:15:55.738Z", "0.0.5": "2023-10-18T18:49:22.002Z", "0.0.6": "2023-11-07T18:34:28.937Z", "1.5.0": "2025-04-02T07:33:16.590Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/use-sync-external-store"}, "description": "TypeScript definitions for use-sync-external-store", "contributors": [{"url": "https://github.com/eps1lon", "name": "eps1lon", "githubUsername": "eps1lon"}, {"url": "https://github.com/markerikson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}