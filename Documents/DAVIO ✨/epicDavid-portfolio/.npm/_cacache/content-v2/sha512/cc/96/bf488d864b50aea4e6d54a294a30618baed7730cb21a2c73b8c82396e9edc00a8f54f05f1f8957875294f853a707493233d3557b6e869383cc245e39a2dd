{"_id": "lodash.debounce", "_rev": "59-92a78b239bb54a013b54ae0ff63cce2b", "name": "lodash.debounce", "description": "The lodash method `_.debounce` exported as a module.", "dist-tags": {"latest": "4.0.8"}, "versions": {"2.0.0": {"name": "lodash.debounce", "version": "2.0.0", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.0.0", "lodash.isobject": "~2.0.0", "lodash._renative": "~2.0.0"}, "_id": "lodash.debounce@2.0.0", "dist": {"shasum": "cbac9a1e108e1f1e13a9644eacc091f0b28fc194", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.0.0.tgz", "integrity": "sha512-5of6Zy2B1iDglZRf67Yh0aaXTjkMZfheug8OiLCKjK3iSz2Ebxur9fY8wz3lUBoBZ3nJhUbqswyM9+3ZLKUdPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDIeg2/KfQkJLVg9nP+VCJz8nXrZVDnlNHQtv+tgDBYAiAk4onu35JJahIkQ+YlHB9TtiJdYPPrMScNCynVhd73zA=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "lodash.debounce", "version": "2.1.0", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.1.0", "lodash.isobject": "~2.1.0", "lodash._renative": "~2.1.0"}, "_id": "lodash.debounce@2.1.0", "dist": {"shasum": "acf0ee63ebc5cb2907d0de09978779d22318940d", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.1.0.tgz", "integrity": "sha512-oHAfeIN6rUDxtFSOczIqezMsEvs3wwzqf8JVQqHVRQ0UVsYlJs8pA9iNll8X9c2owBXScFjENdojGZA/Wgk/Mg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYfYAkNLm2bCPSJQGLqtRWzf4RcA3vr26DnXRJQFYzkwIhAILVhmUYtQMOujUbzZKB4XJ/eUJYWXDNndSjURkTDLRU"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "lodash.debounce", "version": "2.2.0", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.2.0", "lodash.isobject": "~2.2.0", "lodash._renative": "~2.2.0"}, "_id": "lodash.debounce@2.2.0", "dist": {"shasum": "7ecf2781937ffe87f5379e959c85418e9e267626", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.2.0.tgz", "integrity": "sha512-G<PERSON>s3SF/wIRZHuU6mmoGmWSMCiw/GZyaVNAmh2dP2eK69mo78TbmjpcX0/ygoT3oBjZGnw7wsH//lfMoCyeoXZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuoqOld7W/VqdQhUjWRtnSh0eWpYfV/FSw5IEqcChiJQIgeBzQdd3ccLcsJ4kEkwgXvuIVfWTngrHQDvJv+qg4D/E="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.2.1": {"name": "lodash.debounce", "version": "2.2.1", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.2.1", "lodash.isobject": "~2.2.1", "lodash._renative": "~2.2.1"}, "_id": "lodash.debounce@2.2.1", "dist": {"shasum": "46e0ded9b392afa45a8fb54efac564a17fe78be0", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.2.1.tgz", "integrity": "sha512-PYp1xLsI0OYKmjRq3+JV//3lxTa05ReN7W5QjMTk8FGrYjnpPfsrF/5vS/Rth7yfh9IAFI4WoE67sGFu23dkYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7wd4AgBb2xyDmpbFB/x1qc7EAX8IDwntsUZakgYUhIQIgB2sHxvY1OWcjG0nIv+MQnLSuSZZKQS9cv+4V+RvCzY8="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "lodash.debounce", "version": "2.3.0", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.3.0", "lodash.isobject": "~2.3.0", "lodash._renative": "~2.3.0"}, "_id": "lodash.debounce@2.3.0", "dist": {"shasum": "21bcb36c069db2c57a6c9ede38300105dd532b9c", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.3.0.tgz", "integrity": "sha512-2Tf93b1RCF2GiqO+SxrJND5MKLl+WROvCONJ0uRA1NQDgcWRjbehzP2uVVTNnaeaQKGvzZmpum7Vnkm0zxyWRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjrQ+l7T+Vsp7DvOrtWrSV2rGiIHWepOiy14t+NFZVQAIhAL6S8gm0l5hoEqJjKNYqJYo/8Fxs3xPiyyssQ8icypca"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.4.0": {"name": "lodash.debounce", "version": "2.4.0", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.4.0", "lodash.isobject": "~2.4.0", "lodash.now": "~2.4.0", "lodash._renative": "~2.4.0"}, "_id": "lodash.debounce@2.4.0", "dist": {"shasum": "cfb2cfb7286d0e1a02d226f29fbacfd66de2bcbd", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.4.0.tgz", "integrity": "sha512-P+uXQOeurjZzFi+qExYQjY1Jh7nlL9rLvqVkTbiGOJWem9yo1ayVEQ8fg2NWdmu6MJbghqOoeSH58nNWnpYPpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJ/3oOHSGtqER9QPqcKs/qV3Qyw2ykMNUkaG3gBtaR+AiBmr6nxpYTj3zcGhx/F5BePsyAEyZzFmT3ZcOO8QOcsbQ=="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.4.1": {"name": "lodash.debounce", "version": "2.4.1", "description": "The Lo-Dash function `_.debounce` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.4.1", "lodash.isobject": "~2.4.1", "lodash.now": "~2.4.1"}, "_id": "lodash.debounce@2.4.1", "dist": {"shasum": "d8cead246ec4b926e8b85678fc396bfeba8cc6fc", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-2.4.1.tgz", "integrity": "sha512-lzSGKuZQhTFRd4v809TAn1sdMXasHZ1TY3t/NxuXsdcvUcKfnUdnl5E4+g7PWVBdSl1mIe5jCDZbwF8Gu8MD+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCqdLbvaH19gn5PFaejU/kcEljRg1qNiLKU6hlb0F2hAIhAOTkl23T1HRtUeOxBhzAcG/kGRMKM3DFMTY+TdMVhPur"}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "lodash.debounce", "version": "3.0.0", "description": "The modern build of lodash’s `_.debounce` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.isfunction": "^3.0.0", "lodash.isnative": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@3.0.0", "_shasum": "d7b079516b5258e42bbeee8341a3469074f4ad5b", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "d7b079516b5258e42bbeee8341a3469074f4ad5b", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.0.0.tgz", "integrity": "sha512-zyNUjpnXU/+5DipUK9uBXZMJNLMxZgOPnwuft/amQzyv00a1r95Ml5PLnnfHX2QVjcLWClv2FOa6ynWC8uRpWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAQNUSj8yZaTlIGX2oi8KtbAJSFABovaTQlHejwLSjVQIhAL8xj2+vGYFDF027O6PLMh8zYINdPVN5VeXSfLa5uesS"}]}, "directories": {}}, "3.0.1": {"name": "lodash.debounce", "version": "3.0.1", "description": "The modern build of lodash’s `_.debounce` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.isnative": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@3.0.1", "_shasum": "6d64dd85c48d6e9a78ad33dbbc8bf072b5d09bfa", "_from": ".", "_npmVersion": "2.5.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "6d64dd85c48d6e9a78ad33dbbc8bf072b5d09bfa", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.0.1.tgz", "integrity": "sha512-QPXAySvseF3aKtGdaQH2cn+JDC4AVOeNOH0Qs7eToymC7IGpR4u0C5zcw4f793nXwi82jXmVsg4wEeg2hGeVYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsRlCHBu+PhwV2C81hbwdw4bJJVuXxD1ShH0PuTbbClQIgFDh1KjT399eumyf80rchCnHmkFU+itgsgQjUxbG0wXk="}]}, "directories": {}}, "3.0.2": {"name": "lodash.debounce", "version": "3.0.2", "description": "The modern build of lodash’s `_.debounce` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.isnative": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@3.0.2", "_shasum": "7bf245fd2f1ac7eff5a1c966e45990f2435a3b43", "_from": ".", "_npmVersion": "2.6.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "7bf245fd2f1ac7eff5a1c966e45990f2435a3b43", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.0.2.tgz", "integrity": "sha512-i5GhNmtgTQgcvZutBEaEbTGR3NFwNqejkXYrkeC4p6h6MkMvNwJ66DlrkUdFXvoR39I+YO1lZNnf3ESMqe13Tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLB8f6cFL/Gl50Tet9a3vAkKn7gQTAwYMmlGbGQjcfGQIgWrFKaWmU4AtOkmV+09xHc46sDNOTwp2PCb2iwwuWXMc="}]}, "directories": {}}, "3.0.3": {"name": "lodash.debounce", "version": "3.0.3", "description": "The modern build of lodash’s `_.debounce` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.isnative": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@3.0.3", "_shasum": "f696762aedfa649c937c05a64e8a013ffd219c67", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "f696762aedfa649c937c05a64e8a013ffd219c67", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.0.3.tgz", "integrity": "sha512-Lq1tdpCtj6qKYk+2aYagUNOV35TVCKhwoFx0hXd7QGbC6aPUWLkWhUdgTPOUxDGATJBKK2GtAPA2Ix0hpwL1Wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2SSHfC6T/0gZbiIZMHn3skAvJe7IzzW6T0LOy6AaqYAiAyKvELEucPPBwwTCHrQSlZwM91qPpm3PBQ2aMABWEkAw=="}]}, "directories": {}}, "3.1.0": {"name": "lodash.debounce", "version": "3.1.0", "description": "The modern build of lodash’s `_.debounce` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._getnative": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@3.1.0", "_shasum": "f6731d72c739f29bd38e5803b208c9f640868b78", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "f6731d72c739f29bd38e5803b208c9f640868b78", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.1.0.tgz", "integrity": "sha512-xWgqL5OdjVicfGtDv5YV4RsX6wTHHWWy7XELYyipHgt1GJlaeIVAwGZ0ckkTJgy/MyhIVmZR5w/ZJ/83fVdwyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCF0WofPcY99j7OW8iDd5L3q1NZ3l7bn+EFOj/GBaWLRQIhAMtDnMOp0rN/2IIMmrixNBHEyK6+ma46KWL+NYJnZlg8"}]}, "directories": {}}, "3.1.1": {"name": "lodash.debounce", "version": "3.1.1", "description": "The modern build of lodash’s `_.debounce` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._getnative": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@3.1.1", "_shasum": "812211c378a94cc29d5aa4e3346cf0bfce3a7df5", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "812211c378a94cc29d5aa4e3346cf0bfce3a7df5", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.1.1.tgz", "integrity": "sha512-lcmJwMpdPAtChA4hfiwxTtgFeNAaow701wWUgVUqeD0XJF7vMXIN+bu/2FJSGxT0NUbZy9g9VFrlOFfPjl+0Ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCI0eT+BRM1FYTheYSGedpAmtbpqJc1BHwLv+5Cj0EbQAIgCnxO26EuYpRuvp6qZ58/ruxnuFvRz7fgMGiByvx9qdw="}]}, "directories": {}}, "4.0.0": {"name": "lodash.debounce", "version": "4.0.0", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.0", "_shasum": "6fab1aac0f043ed02a81a5d16cdc408c814d6946", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6fab1aac0f043ed02a81a5d16cdc408c814d6946", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.0.tgz", "integrity": "sha512-CgB6Zee6jfb88+juuhbCr/409+pHDa/cO81jcDHxUWLwkGxwubItvxBct2BuXrkQh4mMs6WW2bvF6/SW9W7OTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHhz0+8AwSmis3k/ev+eY8mdd72BPyF/J/9L+nGEXEf8AiEAo6v/+naUAYxiZ3AU4BTDxh4yM5/pZ6Cr9nhBXNO3q7Q="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "4.0.1": {"name": "lodash.debounce", "version": "4.0.1", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.1", "_shasum": "01385740d6ac72aa0cb01fa1f1d82137dc18fc90", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "01385740d6ac72aa0cb01fa1f1d82137dc18fc90", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.1.tgz", "integrity": "sha512-weF4XhFwKZJfjdCqCWZ2vj0JPkRW39kJODisXd5w9moII54q3fXNgzEQ5+Dna1eoXLhkl79pVbfBrvacHVOQ0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICXl+ffKobRNjeMRb7M2mKpymnDRCYjEu9mYLeE2+LUyAiEAkXbPfFdQ8S0EvcUjjqZOKlX8dN0gH+r6r/dzw5ivYDQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.1.tgz_1454484357948_0.29673385666683316"}, "directories": {}}, "4.0.2": {"name": "lodash.debounce", "version": "4.0.2", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.2", "_shasum": "3d33db435b71e06dba272ffe963d7dc11eee7251", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3d33db435b71e06dba272ffe963d7dc11eee7251", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.2.tgz", "integrity": "sha512-GNIegBXsGKHRDKfgtdr1AG/RDONE/og/aXz5JgsUtKnFmNuaVaHvNXQnIifoguD539yFvzv7mROajargtjebQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFa6QMrGTyIs5NC3JmeRO07bYjTAcX98HOvhn3YJwSKeAiEAvY2DP8Z+vYqZoTEPewyITRFM1Hsz2ib2wagZ5T/NkQY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.2.tgz_1455602224091_0.12025941768661141"}, "directories": {}}, "4.0.3": {"name": "lodash.debounce", "version": "4.0.3", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.3", "_shasum": "4e5069cdda73091dba5ef040524efcd353c55e1e", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4e5069cdda73091dba5ef040524efcd353c55e1e", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.3.tgz", "integrity": "sha512-zbmvG6OGIrB8LNl9gN9GF1J/FFODL0o+jcRAYJbyDPS8WRoYyLWPJttloJX0wJsNRlbN3Tb05Z+dYFo0DleHtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbarNxmST3XjOcorVekGLfNeGVkHFSvZvfdH7LjTgzBwIgNJr5oZD9Vxb3duZy41LBf3Cd5OhiVKPcDbET+bhpzN0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.3.tgz_1455615161322_0.5149142206646502"}, "directories": {}}, "4.0.4": {"name": "lodash.debounce", "version": "4.0.4", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.4", "_shasum": "bc16888c58d291e3bc811cceab1ec0a27b548456", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bc16888c58d291e3bc811cceab1ec0a27b548456", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.4.tgz", "integrity": "sha512-gKlEhwswMZ5yX01vC9pM1sGwmsSRBBFLuL2q9ngYDqOSQ9LTF1F5UFxBjb37k0CS9uuLFQAFSiUl19gjSD8F4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB204QGmmOPCIUH2RJdUqPSm4dK/T1REWyKVyz6k6ykaAiEApCOF6ZDamc2Q7X+A0Ido4ok6DerGn7XRTQ/j3RTfI/w="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.4.tgz_1459655284372_0.1065348235424608"}, "directories": {}}, "4.0.5": {"name": "lodash.debounce", "version": "4.0.5", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.5", "_shasum": "1129604a8a7910060c1aca7f8a5b4ecedb39c003", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1129604a8a7910060c1aca7f8a5b4ecedb39c003", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.5.tgz", "integrity": "sha512-znEcLohsZtYJbgjATOwa1b/r3ZZ101betKk+bg5MEt8MuVME/ko8kbi2RrLAJ7oVM2YmRZkyM23nwmwZt/U+8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2zmJXlA7gHpGK6jyi3lYn2+qApZVCGMMbpdH3wkSC6wIgX8TfW4pPPZ7fwpo/DcH9aA7Xhdv/RlLaADco2XGP46k="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.5.tgz_1460393613082_0.3598157411906868"}, "directories": {}}, "4.0.6": {"name": "lodash.debounce", "version": "4.0.6", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.6", "_shasum": "3d9e81bb65cc7ce55f8c06d0c7b81d3db705a591", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3d9e81bb65cc7ce55f8c06d0c7b81d3db705a591", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.6.tgz", "integrity": "sha512-bvQ3Dur2OS7mlFg4XYHH8aoKEOyOubvORNQcIaYD999s/bB8b8WK03y/mFOKpkyT8K5E4EdREVxSucgREyVHQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICEUDO9W9ZfnTKmQvEfU0CHZk0G/wfPE+zyuNeab0rJSAiEArcV3lzi/3+Qi3UUgxvSoozP1v1/DtWYf9aRBjTHRPcQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.6.tgz_1460620855409_0.08119431766681373"}, "directories": {}}, "4.0.7": {"name": "lodash.debounce", "version": "4.0.7", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.7", "_shasum": "5c5f8ef9dd7fa68b36b905be8794236eca7ddfeb", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5c5f8ef9dd7fa68b36b905be8794236eca7ddfeb", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.7.tgz", "integrity": "sha512-a16o+FCYrRJd2Tui6SfrD623/dgrIXgJhKZ8vX8bcxt/MZfU0ywlzgqqEOUI5ejy5q2tb+zH6FjcZF+AGDZsmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG8hisGalZd8k7KGf84VvACxkK/i7ehpqOTahp4SXcWyAiBZLk6ZlJei7r1RWbuTklIx5uZyO3VCp53Eb1C+4EQ43g=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.7.tgz_1469457742101_0.3134150563273579"}, "directories": {}}, "4.0.8": {"name": "lodash.debounce", "version": "4.0.8", "description": "The lodash method `_.debounce` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "debounce"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.debounce@4.0.8", "_shasum": "82d79bff30a67c4005ffd5e2515300ad9ca4d7af", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "82d79bff30a67c4005ffd5e2515300ad9ca4d7af", "tarball": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdK6ASnHHLR0xjpmekICTx5Lb0yxaqI0IFohs0vu8rpgIhAPgQhv+9jjJB7B7UGWl5Bi1cSebWgEYsr3xBJaVFKR4h"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.debounce-4.0.8.tgz_1471109869557_0.3685326180420816"}, "directories": {}}}, "readme": "# lodash.debounce v4.0.8\n\nThe [lodash](https://lodash.com/) method `_.debounce` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.debounce\n```\n\nIn Node.js:\n```js\nvar debounce = require('lodash.debounce');\n```\n\nSee the [documentation](https://lodash.com/docs#debounce) or [package source](https://github.com/lodash/lodash/blob/4.0.8-npm-packages/lodash.debounce) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:34:32.882Z", "created": "2013-09-23T06:30:55.269Z", "2.0.0": "2013-09-23T07:32:39.562Z", "2.1.0": "2013-09-23T07:51:51.422Z", "2.2.0": "2013-09-29T22:08:01.414Z", "2.2.1": "2013-10-03T18:48:45.402Z", "2.3.0": "2013-11-11T16:46:02.640Z", "2.4.0": "2013-11-26T19:54:48.642Z", "2.4.1": "2013-12-03T17:11:38.473Z", "3.0.0": "2015-01-26T15:28:40.698Z", "3.0.1": "2015-02-12T17:04:59.066Z", "3.0.2": "2015-02-24T16:19:46.656Z", "3.0.3": "2015-03-25T23:32:41.309Z", "3.1.0": "2015-05-19T19:49:06.489Z", "3.1.1": "2015-06-30T15:17:59.424Z", "4.0.0": "2016-01-13T10:58:55.255Z", "4.0.1": "2016-02-03T07:26:00.336Z", "4.0.2": "2016-02-16T05:57:06.031Z", "4.0.3": "2016-02-16T09:32:43.340Z", "4.0.4": "2016-04-03T03:48:04.928Z", "4.0.5": "2016-04-11T16:53:35.444Z", "4.0.6": "2016-04-14T08:00:59.268Z", "4.0.7": "2016-07-25T14:42:25.401Z", "4.0.8": "2016-08-13T17:37:49.793Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "users": {"tsyue": true, "dwaynecrooks": true, "jian263994241": true, "mubaidr": true, "arjunarisang": true, "zoxon": true, "nmarshall23": true}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "debounce"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md"}