{"_id": "@babel/helper-remap-async-to-generator", "_rev": "127-679b14c61534b528fda493c0e8e44773", "name": "@babel/helper-remap-async-to-generator", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.4", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b9188307fe9dabe3428065cb5f8a8c44ac0883c3", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.4.tgz", "integrity": "sha512-QEcz0wExfyzXTorD3i/wqoR9TMgpYC8PZgTlnoIxZvfXqnNap5D0fU8uU/YPV1i4xyvJJp29cEdnlhZWkNQlUw==", "signatures": [{"sig": "MEQCIGDeQy5bJsuQbYHu5lNBghCZjrRkRiyh5d5fMMj2BNrjAiAaURYTNISR8/0cQNZmtqtUy5n6EUGuJS1eGLWRxk7Z5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4", "@babel/helper-wrap-function": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.4.tgz_1509388587701_0.3581151277758181", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.5", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "12fe0ec26917be6689380134dd3258185dbd54f8", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.5.tgz", "integrity": "sha512-/DZeC5+8NmxD23OmB8SxVL5lUKlgrW5TReWUNIhtJMTwSBRNMa5WliR+SuD9DiegwW+5/ViIzXGh6fnWOvH+lA==", "signatures": [{"sig": "MEUCIQCOiaHjGmjQewffCwbvfazSLWozYUW0s0luuDnYoo4jdwIgfYgj36LSG6A2vOTbUbQxX9l+hiWHDwzQjqG+Ef8J0C4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5", "@babel/helper-wrap-function": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.5.tgz_1509397084244_0.5468262496870011", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.31", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d0579c1f1c933eac8969d9c2359bcb8e9b7c7598", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.31.tgz", "integrity": "sha512-866mj+/hhCWcZUmabySLPdFtvBRRSg1sm9wIkZFaNlgcVIeq1oLE8P4dnI7dJqhoR+BxTky7Lekh9hK3RM8Upg==", "signatures": [{"sig": "MEQCIHYhZ34g9GwiPT84SvRR5b27A0N9wP95K3xp3aXzcdqyAiBcgFDyf00osg0etUI7JKnhez03i0pjT+Bb1cQBdH8klw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31", "@babel/helper-wrap-function": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.31.tgz_1509739475984_0.816710113780573", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.32", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cf55af249f19e7c9cda72a76d3ed92a249bea97d", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.32.tgz", "integrity": "sha512-9LC7+/c2ELr6TJD14frREbQFe39oTUHCilmXOA2sgCUALN1JDoWHbFpct0HvCWxeBHkQUzxBFiy5bwah7i2jyQ==", "signatures": [{"sig": "MEUCIQDApnmy4kgli4UCCO77dwhDgpFw3dqweOhOQXJCOzwRgwIgM5rccAlWLVD1nDpBPkJw9kymBXTGzBbu2jyGDgUhO8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.32", "@babel/template": "7.0.0-beta.32", "@babel/traverse": "7.0.0-beta.32", "@babel/helper-wrap-function": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.32.tgz_1510493645379_0.6658419740851969", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.33", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fc354cfb6cd3e9bb4d40742b14fa7f41ba18420a", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.33.tgz", "integrity": "sha512-BHP4DczGu4CdUbXItBECl8Hx2GxHjA1dXZ7HJeSSyM8RYbBhbszEmCMu+ZpkW9pVH7esJoj8qwy2saB/RChAaw==", "signatures": [{"sig": "MEUCIQDDpvNVyd7TLjbQVEIUZ7XvSxSvFkpQjMLoTZ6Lu5Bw0AIgeuua8ZrTIu0chi4rz7Fg+PSuREEG4ss6Oe04gKbfTPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.33", "@babel/template": "7.0.0-beta.33", "@babel/traverse": "7.0.0-beta.33", "@babel/helper-wrap-function": "7.0.0-beta.33", "@babel/helper-annotate-as-pure": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.33.tgz_1512138563734_0.23366341390646994", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.34", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "30d0ecc53e203a0d029ebc056726c3a07d545443", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.34.tgz", "integrity": "sha512-Jf/YLf55UH/llbozjmUfzDZV40WO7eUuiTrgqqqBq5Yl/ARuGDAk4RnQhUaKj7IRsSvzb6IV6qObj/boI9bD0g==", "signatures": [{"sig": "MEUCICIzHG7xuGzRwuPEg0NK3DUl0oZ6UCF9iS2T//L7aOCBAiEAzH8T6LWElWHWMdnRuyfhPCPZNzkwJcM7lYzXfVuhye0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.34", "@babel/template": "7.0.0-beta.34", "@babel/traverse": "7.0.0-beta.34", "@babel/helper-wrap-function": "7.0.0-beta.34", "@babel/helper-annotate-as-pure": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.34.tgz_1512225621425_0.5525649830233306", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.35", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "272aecf58ae20cd6d4582766fe106c6d77104e1c", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.35.tgz", "integrity": "sha512-Ud5eajQ0HRDLN5wMSrEIa+MBgrlcIjTnX0zKyG5YoGBF9jWXLaapH4XUVsyxiprQyc3Y0rAbhMihz7JxAYplmg==", "signatures": [{"sig": "MEUCIBjhoyCXUjzcGzFvDh55PeVJKU2c7ByRSfA4zwwwmP+qAiEAzcfNOwaRTsAe+Do9N/cHl5aH1CBAWHBPM9n5V41k6rI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.35", "@babel/template": "7.0.0-beta.35", "@babel/traverse": "7.0.0-beta.35", "@babel/helper-wrap-function": "7.0.0-beta.35", "@babel/helper-annotate-as-pure": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.35.tgz_1513288111040_0.09140814351849258", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.36", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9ad71354aa60a69634b7ddc7be5147d8201a7c03", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.36.tgz", "integrity": "sha512-zQkwENaCl6yEQbxNUvbkPHT6Pu4vlUKuElsebfD1ra2ljgHdJ0Q9BJH7fSV/tZ87jvlrRxXQaEB+xgqUx//RwA==", "signatures": [{"sig": "MEQCIFXEbBTgWFy7HZh5RePSqQB+gGNhP0TLAxVpPvtw/gNOAiATiWhar6m0xLSwSCc4KREe9ee1CEvZ29l3UhJDhpxdkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.36", "@babel/template": "7.0.0-beta.36", "@babel/traverse": "7.0.0-beta.36", "@babel/helper-wrap-function": "7.0.0-beta.36", "@babel/helper-annotate-as-pure": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.36.tgz_1514228745415_0.4266177706886083", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.37", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "812827a4dd35ded898dbab9e8d72ce4fe72881d7", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.37.tgz", "integrity": "sha512-1WTRJ4jZq/UAWhonhXUmBx7Vl8k3fqvA4TqksLPFrFSAaSZgYuQDsINb16Lk4zn1dFTs+82AADGEhd86mLqqAA==", "signatures": [{"sig": "MEYCIQCFk6811395O+712dgcuNIpnm/0+asIPRy4wqti0M8kfAIhAOIV0h7NGPBPcsjNfLHQzun3ZjpKQBUw6smcSdZ/aFCT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.37", "@babel/template": "7.0.0-beta.37", "@babel/traverse": "7.0.0-beta.37", "@babel/helper-wrap-function": "7.0.0-beta.37", "@babel/helper-annotate-as-pure": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.37.tgz_1515427426635_0.2627208177000284", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.38", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6e21e1520342d5567db08a033c313cfa7c846a20", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.38.tgz", "integrity": "sha512-7V2WVMz5KRLxE33p1lb4SduDxoLF8TQ4rTGi9rzpEuly8KnlLkZR0ts62hnqZYVyBB3Gq1nfOgLG56AvflBgOg==", "signatures": [{"sig": "MEQCIAT+nlTlLKnv8mCIbqeH/Ps0yuJUmeFEh9rg+0Mru76XAiByJPp2A7MDGaLW9ZZLuRfdetpBrKlGq1MtRRdL0aKvTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.38", "@babel/template": "7.0.0-beta.38", "@babel/traverse": "7.0.0-beta.38", "@babel/helper-wrap-function": "7.0.0-beta.38", "@babel/helper-annotate-as-pure": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.38.tgz_1516206765186_0.7250847280956805", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.39", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "64e715ddd24fa60e02dd139acac0d58a55508433", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.39.tgz", "integrity": "sha512-PPfFWm/piwxFYEKXfh96u2ZD/U/44R98tD7QDt7y1ddTO27v9jJ1rLOQbv6SxtQ92yXieeZpHYaE2AtBNj76gQ==", "signatures": [{"sig": "MEUCIQC9OmlP2USeYOZmJGJjMe/CV8oAoTduE5duqFgLRLGEGgIgLrO4RmXOfX6/Z9wH0KyqocMmK4nrCQAO4uFkJYy+0Rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.39", "@babel/template": "7.0.0-beta.39", "@babel/traverse": "7.0.0-beta.39", "@babel/helper-wrap-function": "7.0.0-beta.39", "@babel/helper-annotate-as-pure": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator-7.0.0-beta.39.tgz_1517344127707_0.8036465917248279", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.40", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "33414d1cc160ebf0991ebc60afebe36b08feae05", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.40.tgz", "fileCount": 4, "integrity": "sha512-RISz7EebAqaf4ofdG9LdVNfeqhK+JlFYJxvqCGMcS+Pyz84dA41MxgtjrRQdT85be1lZLI2OArvD7zDrj1psag==", "signatures": [{"sig": "MEYCIQDbTuOlev4F3mkE42H5KWbyoBGAm8btFzkt9Mb19M5EVwIhAIErqJ5BB9RXwBct8CHtJHAaNMB0+IxNrxCDAbcf6l/s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7291}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.40", "@babel/template": "7.0.0-beta.40", "@babel/traverse": "7.0.0-beta.40", "@babel/helper-wrap-function": "7.0.0-beta.40", "@babel/helper-annotate-as-pure": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.40_1518453771716_0.6288878353591563", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.41", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "212e2f3fc24635d25cc6f1bd394da1dde1f91c36", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-HihdsE0AQr0HPXtJWQYPaHacrwObQgKa/+l7gYXdaTgi7Km//DnF3X02JCjrIttWJ8XtpPZG7UiBiTycoee3Wg==", "signatures": [{"sig": "MEUCIQDMR9F6Pv8O202TIVPIIPWiy/zMd3GSLWFSbnUFw796wgIgbA3w056oAI/5losE4FdkI708ps9SrqunT0kJnhUsXKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.41", "@babel/template": "7.0.0-beta.41", "@babel/traverse": "7.0.0-beta.41", "@babel/helper-wrap-function": "7.0.0-beta.41", "@babel/helper-annotate-as-pure": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.41_1521044822577_0.1667583624743021", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.42", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c27dd7789f3a9973493a67a7914ac9253e879071", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-At+ipbHRYoN0AaafqPvTPqyVYi+beantKZ2orCYSb/AzP2+JywaWlOPH0wyXLOGzjkJX548Is4cV2wGbEG7++Q==", "signatures": [{"sig": "MEYCIQCEyl/2med1/xASakrqFqlnozub0PVwMj89FLNdIBCgcgIhANIi8UQ2Er7bIMCWoU1LQYS+/dzUtCQUi458zpgVVxtF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.42", "@babel/template": "7.0.0-beta.42", "@babel/traverse": "7.0.0-beta.42", "@babel/helper-wrap-function": "7.0.0-beta.42", "@babel/helper-annotate-as-pure": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.42_1521147132165_0.0016180961590306353", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.43", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a03ada2c7543a1c8a890321c3db8ab9a0f07347e", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-qZjhiby1b2L85ij/eS9M0bsfuPojThaEr1Cz4xxAbOUwfh4jl1K0Isz3fDZeVLx9Ul/gTbk4m/+z4CNOVbCTJw==", "signatures": [{"sig": "MEYCIQCE6QeEYUvOXMN/ktyXkk2gwv2ucuEiRGV0RpeLGjwmvQIhAPFjV70IQ5QhKYEEk9/fGWj1B8rlmXSYrpL8vln5zqMm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3373}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.43", "@babel/template": "7.0.0-beta.43", "@babel/traverse": "7.0.0-beta.43", "@babel/helper-wrap-function": "7.0.0-beta.43", "@babel/helper-annotate-as-pure": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.43_1522687741298_0.34685065253646985", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.44", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8ad8c12a57444042ca281bdb16734841425938ad", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-Jk2mwO7QOx9n5ktVx8OOuuybyCuZ+gSnd9HqkdxqdfjF+kzJ6FvQ1QUqOf3Dg1uTFmN2/UzBpFgFV4OH71xmWw==", "signatures": [{"sig": "MEQCIFwZHSjXjLDNWdfZOA/gW85tcOIh5NQyGGpdaExswBGtAiAXbInLjzRauFGQPyFRsBRwv5+H1ZV68bjYDg8NM+WmNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3438}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.44", "@babel/template": "7.0.0-beta.44", "@babel/traverse": "7.0.0-beta.44", "@babel/helper-wrap-function": "7.0.0-beta.44", "@babel/helper-annotate-as-pure": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.44_1522707641609_0.07943217222459942", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.45", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d6f983f98c018aa5071289f8ab4211dd74e88c80", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-gA5oL+ckKPt4YIXla7HpXGg+nuUu2tRTrbIsGSbR/xQ2sPHRTLvy/CZH5raR9cPuorz6aJDOgmwKkE3jJROU2w==", "signatures": [{"sig": "MEUCICbE+4ppnwL6VJ3ZRX27L8kjZaLJPUpuLmsuIHDKsXlvAiEA5NgPZbCT5IJVkKsUyo88ZlkAM8UT16785Z3lQhBasA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3VCRA9TVsSAnZWagAAuNQP/3QD0t9JI6ne3BdZ3r8T\nIDJpr7SqUACMPavR/VcLTnksaMStTFlwCmT06ESYRuVYeMGWSqKzgn/at8LB\nCBmKpP/vC7GFzDN9VzluZ/H3hDsZBSKj1D6uff8RSP5m4AOl3d6DaEyLIwF2\nqy1PNXPnE6ByKCz3sYg7Q4/UdQY48k2Yu+SqYWK0sD1olRAy623HGt2vM/og\n4EoW9ALQT6Y99L7Cr/dRsFTbs3XtNvM6Frav5gq0AGMhPGIOn+FK7HqToz4x\nSQbXeIUNl4n6ai54D4rKjE7M6buVHLF6IIZaP0MgBuedvFAV1FmMnsmZYhyM\nI34t2w1RxYGEcXwGfEblVNAUmvMqflOzB9tW0heVGCo/z9HzVFC9a3932n8u\n5Jz97XkY19YZFfX/ceL1+LtTQSYMqMjon0gwz0RtPzI7T02sRrl+6MFDwLKg\n28ocJ0WWb04VgjKzHIqh0DIKUHQGQxUBgJ/ad7ehI73caBbvt1iAc2DrrC2d\nCLQHD1Zo/s1e021KlkF69Cz/zaPr/9rmIMvXyChq7AfRk/E1UOdAPunNZa2V\n6N3D83yZWwYNRQ3oDXKT0pYY54a/B9AIeBlx5aeBbIKZ7bkF1eRw8fSmlYxw\nHv5pd0Gn3IC0oPYGF7PhiMFdm4mN+TFMoWm4zHV5ugPRkagiYxYTGNTs6+WS\n4kYJ\r\n=7yr8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.45", "@babel/template": "7.0.0-beta.45", "@babel/traverse": "7.0.0-beta.45", "@babel/helper-wrap-function": "7.0.0-beta.45", "@babel/helper-annotate-as-pure": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.45_1524448725397_0.0024644617699871585", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.46", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "275d455dbced4c807543f001302a40303a3f0914", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-YrqQ98z8AMZx8f2PGJ4YV1MkXtj+qbwbFV7MOLTiavGSFY7UrN4uQfhKEJ/4GUf4QZdTr5NEmRt0AJrWno8y8w==", "signatures": [{"sig": "MEUCIQCDTrkGuvsMPD9pAXEbvJvkvPxR6Y2cRzZc0EIYbLjlEAIgGpjgywFFU8x1NGFms7XlXUgMW+J/DKJ23YbdCtT58Cc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WH+CRA9TVsSAnZWagAALS8P/jv4pixcdaiZos2XeL2V\nZ1GsXOYamQYJ60OAn08s+8d+HxwEv3i56wBh6NneRzmjqLZGX6L9mHYPJ1X+\nzcInZTNJ+HgWrNmoJN2LmAV1yYJbyrXksTsWQTqIzQ3bbOxDB9Jf5kyeN3XN\nCnlWsCsZWOr3z3cjlesWof3I0LQLPP/kPH0CLKXcNLmbmTSXGdjzBuKfn7Et\ntD0/yzQAgL4mXKDvtOmqY2NxgbJXAmk1ov+czxjzNWRbEjythoZA67edamb6\nnVmApYuNBXIEHt282D4CG3UhvzDh8BcSw8RQlhovZD4vLM/pRauv3L7RHQk5\nbYffBUzQ3oojRs126RhFKa9PZGrbrph3Jl641K1FRAihlxVzVya9jxTVI095\n5hsqIqGzPUt//6cex0MIt+cd2teeH/Nz4SM8tSr2id/G9WMIvzRWkTxBo+KM\njS2+uEIrmFYEUyOVZ4JD3KdDfxlUrawuO5ZkAY72E6s+/A+6Y36BlGyn5zQ+\ne3Hw8yCFeqbXSRHIbYdun87jiUXhATSwROOfn5zukoOZdM8LZJvUTpQCb9nJ\nc1FocAYnhh2I4jOYWPOCiUUXFLnlFcHQyzm0e4s2c2oVfPqbMpELqRyC65ae\nxnMHU/zr1BYJwfjKv6pE1GVy0BEEP3g1SG59LL1P4n1f6lFxrRji/u4AgvJP\nkopn\r\n=Z61y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/types": "7.0.0-beta.46", "@babel/template": "7.0.0-beta.46", "@babel/traverse": "7.0.0-beta.46", "@babel/helper-wrap-function": "7.0.0-beta.46", "@babel/helper-annotate-as-pure": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.46_1524457982114_0.16051748512562725", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.47", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "444dc362f61470bd61a745ebb364431d9ca186c2", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-Nmj3lUHQscD160asav2bZ3sMIjGwGY9r6Vrriy9TqH7bmaClKUKUs5Twv0htFWfOKNFLEeY/MaqiAXylr1GS2w==", "signatures": [{"sig": "MEQCIHWL8aWOPRFnj4ZQyFgilA4F4j9v7FnfIuc8fHiFWbJiAiA0EvmHnLJZB2sIQmynzZ90H0b8BMvj20k5AKmzawxRng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+ic6CRA9TVsSAnZWagAA7eQP/jMPO1cYh1OcZKNmNNNU\naOStlBTgGKqdILCEHci+pVmAvov3Gv+IYHEFaxwbjg7YOmQn1OXvNMJap5uR\nuxGy0Y19enEdaMkcKXjHrTDWEUnVHo2lEVOIHpzAWUMPbuMxwqneGjKQz8Nb\na+uRdUlY1t1sjDHFTFilvePSWRm/9XVkRJ3pPPTVX1opA82Lqia/6kxKci38\njs8d8KX+p9nuEloJ2wK7Zg6UNDvv2zeWyayGHrhEtn/OQy9G+lCgnXzh+yeF\nZDgCdfspqP8cHD/Us+9ngZmOiSH5TcQOIFU/db683oAJvSRopToWoEaMSX7l\nQfTVWMWdS6VuURY0+TpLM5Vdfsk088BRONPs3GNoEEzey9I/oom0J3WePfPU\n0ZPcDrHArQ9V06CM70M60irjwfCbY6D4EItuTlKKB6ablXzVAAKq/lsR/XIB\nUgXR075I2cDxkEWT7Nay0WqoZFP0EsjpmrD7FG1Miea+tNZkVhvO0QL00Vw0\nvsnHApknFBXJLvVHb7EIAWH1fyd+RchjCVVqGawJtGOtUpKC7QYHUWG+wQH/\nsb6nbFN/dLj2Wjr42X7Y+FZyQc6hpZEaPB06AhfnBFamsvdz1WNeb8PqUi7/\nT+8Vg73eiPRokRCk2j90kjG3OL8UxbkIpkxbKScN+w77llsg1/EjWz3pBQ1/\n6x6A\r\n=ZCaZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.47", "@babel/template": "7.0.0-beta.47", "@babel/traverse": "7.0.0-beta.47", "@babel/helper-wrap-function": "7.0.0-beta.47", "@babel/helper-annotate-as-pure": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.47_1526343482575_0.0006099915117065979", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.48", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f0fe43824908dd456fa726bcec100874731c9f68", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-+DpwvwnTyH8A+WmD9X4Ubh3AyOp0TQYU6oy+1l5Qw2wlZIqsVb2VMoppET96siPv39R01E7vii/gsgne/Oqevg==", "signatures": [{"sig": "MEUCIQC6tWJ27+YQkbDKJscYYwxxJFvmlmaFuBogAspusp0OuAIgY6fCR7EvD/qGR7VmP/AHHYxR71OS2BqnZNyTcHFQZvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGLCRA9TVsSAnZWagAAbQwQAJtAW64mTqFr4jmIGhtE\nFDi9XNYPkcGwnZeKBkqxHnmsVY728SP40Ln5YFpT0ew/VLASbrd9XQezvRrI\nJEwfxfKfO52oHSTVYP/UlDv5zQVPDmwVbuRgjWpy/QOef5f8zxIqoTUVbO+n\nXbhPP9NdqHVfyVhpueukwuarsReSGGGgz9IF1q68hIZULYcQIFGNK+vW+tK3\n7GvPMcOWJ8xHkQe4xS39C5KJ47hCt6+F0mLch+s6ooMZEzke4lVBMwuZ7AZS\nyrFDugUgvEedeo8RPWdkNo7eVmjsMt6xUTB0lZ9vPP1eXXCtB90Y1QEcqTzH\n9YV9VnaoOBTKbHOT+MnczbuSJWyoE5xXct3FpqcRXK4ZI0QSMaSJksokIlfG\nhjAnHevtSkMek8x77gVtlCP1dUOvECEQxIBrysrd4klm5UQNzkUCcs1hY9Km\nvba/po7gJBty3Ue1UeDA64regBa1CpdHsQ/RTyIgEG+2IQY/+PkI6X3qRWvD\nQuejKItOFOFa5Kv2R8tQWj/rYzMp+uC5B2ZS2KC3Hju07gEUuyUzfKWlfFk0\nIFRm0VbmP4eh+Y5q/1T7+Pmq4/dA/D1BEeUMolLjEiYEA6UpyDQbMo7DpZpB\ngR8pCsvEmqb75P2ZtJst6o+pfaQVquPjyaqU/nHRRfUGHpJzAMCgnYS8lP70\nHDQ3\r\n=2bCS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.48", "@babel/template": "7.0.0-beta.48", "@babel/traverse": "7.0.0-beta.48", "@babel/helper-wrap-function": "7.0.0-beta.48", "@babel/helper-annotate-as-pure": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.48_1527189898765_0.43350654193642324", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.49", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b3fdaab412784d7e8657bacab286923efc9498b8", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-cPJLEXMhOaq9BXFJZSy+buiGFGDJ6pji8MDl7W708yeVyB/svzwUjpTzaq4jtkWW70ZnRpKTdlH4gK8+3Tsr2g==", "signatures": [{"sig": "MEUCIQCryamJxFY6cbZIcDoEZDiIyg4wAG6PY51xgkgvPTxzFgIgGnMnf30oiKVfX3Uhd68Dsx5dMwu8Yz2x3JHNlaNA8Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQhCRA9TVsSAnZWagAAYIwP+QDVo0M4Ir6HXh9tJzy2\n49sZcJUr2/p+xttrrDAhsNq6E6GiDtJ0U+sfP+sSq+2uAH0ooV002rvZduJS\ncl1pO0qkYVuCdXNml2cxw1+mVLErU+63Tzzufyo2gjjhqw/w0jVwAmTk5WMV\nsrBmk33gIrNTPRpG8rnHPSXRWfLXdraxDQ8G7aOT5nWoS0lHqtA7nV3k3LJR\nwPXvVowsDKc8AqgD+Uk5JsKauPPQhM6u4FHe6hopp7ewOUfAczwVIl62mlzi\ndp4Uu5WRhEi74yzyM9gD3bnDKRkXgVJobWM0ik+hXoQKBiPuMQoQWIDmikn4\ng3lQJeBhfidsgrKXtXeIwcIXgnD5Cd0eDjt65tJwf8giCkK9V1tjg/BsydDD\nKm+aMXF1t3bDpFNRwr9hFVmTLt8bIYX+vLqXR2YDT1/FZeLGwXv2uIx2YNdH\nOJaTPds8nVS9r9KuIa18ZjC3TG2pawYo7FAodtDnGCKUJfBPjuFeyw+0uxec\n0NNDupHp6jjEUG87DDQIGHXfTmzFQm9Qvlu5tgQnEc3TTmDA1IgJ3QdNqtgE\nHLKSSrHXBeRVoQp56VD0onGxJTwoFaiD4K9jiKsG45CLxZMgZL65GHCCdsWy\nxTLOgElkh1VdDe+x3XH7Q3nEog43l/gtYJeUaAuWsodWr9+DPnz0qJiwmt1c\nPOHS\r\n=xbsl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "b3fdaab412784d7e8657bacab286923efc9498b8", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "3.10.10", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/types": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/helper-wrap-function": "7.0.0-beta.49", "@babel/helper-annotate-as-pure": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.49_1527264289120_0.24140243566066788", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.50", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c5a2e419c061d6739b1d0d17e7e9d47aecc19863", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-rHLzeUtfPXRMPDKknt3z9n5y5RCnmOFtn92dwf+M9QN0I54d73hQqGowOhdVjtAmoIctOzWUV1yBwOcsfVp3HA==", "signatures": [{"sig": "MEQCICIxQQiw4fqjB8NSjq3TvnqjZhi3Pv9cXIIY4fCQU9JOAiAy6SQOwzegh6uw5XwDbJTvyfOtGO41r38q3NdunPyLJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3750}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.50", "@babel/template": "7.0.0-beta.50", "@babel/traverse": "7.0.0-beta.50", "@babel/helper-wrap-function": "7.0.0-beta.50", "@babel/helper-annotate-as-pure": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.50_1528832895776_0.14518650559424096", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.51", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0edc57e05dcb5dde2a0b6ee6f8d0261982def25f", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-K63lkrmNbMMSLK6/My5VN6rVXsfHnvInJ3Fu/xSo9DtFn1H43tU9yq8yzVQQN5y9fsEhBhHi9q5vy9OmfQHt7Q==", "signatures": [{"sig": "MEUCIQDEHfrReSh4mhy+ymyZu+WRamz853dhmCfLuVwT6FzinwIgPBKKgnl/w7IUvcEDTgIkPKwWisY3GepZ52VDeJhuwLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3750}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/helper-wrap-function": "7.0.0-beta.51", "@babel/helper-annotate-as-pure": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.51_1528838457552_0.39558566827996966", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.52", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "19cc67f464f870901fe7be85e438c770b5f41cb8", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-jP8GFVGVD7/xUNwcftEDg3iP+HlrT7+/QEYcISK/33AnxIsBXjvbhKBmjyxURtwHRNcOm7dtTwsg4exqU6+TzA==", "signatures": [{"sig": "MEUCIQCyfaB7hFOtxqORRPInIXS/kjWxmsJ4B9j+2RNV4eWNtgIgKP+fW8pGFl2JZD+8WpfLHRKUQf+mgpP5ptPzDXG19j0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3749}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.52", "@babel/template": "7.0.0-beta.52", "@babel/traverse": "7.0.0-beta.52", "@babel/helper-wrap-function": "7.0.0-beta.52", "@babel/helper-annotate-as-pure": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.52_1530838789603_0.15418604920129253", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.53", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b834a7572dec176389ffac7e763579586490c922", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-yVndfFtBJjwX1QuE1ks8FEHyJkxAXKZfykk/PPprhd4zWJZRBh0UgtV6uHcb302s2wPQ/6cUgB4GEF8z9aN2DQ==", "signatures": [{"sig": "MEUCIQCj9u8XK7fhr0/A9ZBaMwvqTKPRafwJz1kgswrcpgmF4gIgNJBbLlXaaE8lm5lui/WMEj5SkW6rPP1gzq/0dasLKM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3749}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.53", "@babel/template": "7.0.0-beta.53", "@babel/traverse": "7.0.0-beta.53", "@babel/helper-wrap-function": "7.0.0-beta.53", "@babel/helper-annotate-as-pure": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.53_1531316452492_0.04165648685586887", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.54", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "39a50052aadd74d40c73b7c58eb963b90fac56d3", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-iBKpGePnQFQWNBz/7gfMLzoPs5XFo/+OfnORyK7udBmW/o8BJz6jsykGwL2rdfIQFTfgxPA3NQ0OkViNd1X3sQ==", "signatures": [{"sig": "MEUCIQCxy2PM1tr8Yw5qfNyyE5hDpCGTIPObAs9TCS8j1hm9nQIgWt/0Zq9piG7QbzaSON5UjgNuydWCMlc/NadnxiRS7JA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3749}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.54", "@babel/template": "7.0.0-beta.54", "@babel/traverse": "7.0.0-beta.54", "@babel/helper-wrap-function": "7.0.0-beta.54", "@babel/helper-annotate-as-pure": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.54_1531764030290_0.7782408185279373", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.55", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e762d1b8f7f06121ed3e40befb1f9847d4658a7d", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-aEskWDWDQrzwKiB86ZHxLxGrPnNSrC8+nlw7MOxxdOCBCcuP2yxM9fMntlDGnr+upeO727f6eOaWibf6aAQiTw==", "signatures": [{"sig": "MEUCIFHRz0APOh/jm6gbdfdnmoyqTylA9sdFA8xMRQXOdYU9AiEAw7ieMxLezjp2gNnG04XAf09o2GyK7E2s6EROYQG0UA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3749}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.55", "@babel/template": "7.0.0-beta.55", "@babel/traverse": "7.0.0-beta.55", "@babel/helper-wrap-function": "7.0.0-beta.55", "@babel/helper-annotate-as-pure": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.55_1532815707369_0.3881360175375499", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-beta.56", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e5c8958f324395c16606b85ecdf4de1335eaa541", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-uCvdjXeEh/qzvhK61XLP5DADCM0MMxZOVdGIj5In/i9MLt9BD/EAyBmjZN0bc1dD1wJst0qInZyZju0lUUkvNQ==", "signatures": [{"sig": "MEUCIGeDYPGhf2xG7GGKufa9HVqbm191YtHW7UEHX/ivp7j7AiEAsrfTua7lXQWN7Q8EfYCm0HFic+Vur9Nk31QJ6uTLccY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPysCRA9TVsSAnZWagAAapIP/0x4ouoGwvBapRn95wqd\nzkB7ZOGylCdaCCv+MxjU5V9VdZoysTqDFlwE8s+nY7/gOoAHsLcPVwSW6AsS\nJo9KoGltOe0LGJdAj/27wcUkxJPze+nBe4pU3rMEqSz+J2EAU55K6lw9joM3\n8uhsP5bVikyWIMhIH/GzkpJpHxbEdI0MPvhdxB+QhFE/foQA//sEQyWPMrDe\n5CIJ0YFjjidc4DwN5Zw9Hxw6dwJR8Lp8kx1sj7w6rzl5msv/uqaAc2TLeDDv\nUER+0ZlfKDmlm+mhrNIeuo0LRgDpoWclOCXxca38RbJVGQPvGd9YRqhIMTO/\nenr9Y+J9d5UPjPbAuT+2swS597xNbFrKn5lLmSHDN0kES/N6lzxvADNGY2LK\n0bGsl+OTjZ2YtzgYOUxEKkB6uHQiAdZtNqRLiLTKD5Vsw794zPyJWd6jYuXw\nLJQ0/4UzoNrF7HTa805xuF8UMgVoHoBZD0dRrxmswOZxbzl9VS54jQ4v4A9Q\noN0BoN4eZKC0Ia55XtS3xBR9rvbLGW23UBxFt8EiljFksKal/bvnTkxXF/oJ\ntwj2kVijlBi0nanbrH1nkg7FWW/T0HSWO9sAE/HdA9tRjXkhhRQaTUUXXbwT\nKwdt8yFRWPpM1NGjQi8Mg2FJNwm+I/V50Qua86TPpNxHB2kmaPvQiAZXrCrT\nJP6o\r\n=cWK+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.56", "@babel/template": "7.0.0-beta.56", "@babel/traverse": "7.0.0-beta.56", "@babel/helper-wrap-function": "7.0.0-beta.56", "@babel/helper-annotate-as-pure": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-beta.56_1533344940077_0.5711147836335779", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-rc.0", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "49ad8aa956670396d843382bfced0700d91f76fa", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-2nmkOvd9abH0xNsK3vw79ooCEY9ZC8liuxiDyGEjyUPtQCdtuOw3DgJo7qV+HtvmKCGb6cqmm0wCp8aiWOc9qg==", "signatures": [{"sig": "MEUCIQCk+mLZvH0rY0XBw+/c50tPDeQOsi556lPkVTIHkDxO7gIgS4ppsiAZqVWArYzj6aCRuMPV79Aqiaf0fAmACsAVmv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUPCRA9TVsSAnZWagAAC7EP/2+wog52TJpJaE/wPjTz\nrhygvNc4fopsf1+HcfI1WogQ0jB899ng9I6EWK2tOK0hkHXrTNdQbY8Zo07E\nKz1SaqNpzWc4eGJlMCVjqP69dLI3DQM9YP/rvQ2JZPMQ2O8On5qwrYkkS1sl\n3cRoscHhqmz9ruMRiJU91t68aTzUQyyNuzpS0x1oV4+qI70lm6PLAaUnNCj5\nXlD1ayGpL9bRzeTbLkGO/OfRk0XZq7KsL2AySjFlK6+KAu2w7PBVR9cnCpxc\nIr21ST4FVsVStI/9Lp6UfxqZU9brM6S+L/2fhDBSz4dP0192nVAq8CZcZD1w\nzM1GwwDEsCyYoK91Wir4HBoRohwCGE34W+rtAPeGFoYGchnHWiTaEhC/hj4J\n2X6rq+mwx8UCoO844cmPTPAWeWtCv8YabCdFyT23Ml+S+UuJaUxbCLywOY07\nab3MuibKnlIjP9EjnMEbOVhnewEAysejhJjorHbqWq9VUN4CScbYx6ibJxYX\nsmD4TXqpXPInKgqX53Fx8z6jnWBebmGv63n0GmuDEH4+SCcH1wHnf82eGsB8\n90Oh45lup75fcZCeBkq4WXNGc52pHlW31+ACt8wd5uHs14z5gCI8LWHUctAO\n84LLs56O7NnoX/ZS1y6gfV78tskPTvRpmvY5E7izHO0l8+LUXCBcjIo5SZF9\n//oO\r\n=DTXX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.0", "@babel/template": "7.0.0-rc.0", "@babel/traverse": "7.0.0-rc.0", "@babel/helper-wrap-function": "7.0.0-rc.0", "@babel/helper-annotate-as-pure": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-rc.0_1533830414951_0.7890917389085226", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-rc.1", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cc32d270ca868245d0ac0a32d70dc83a6ce77db9", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-skROQSC2fPwmrzAEPT/M7CObnWjJGpdbNLoICZDYHwDiUDe3dk5cQsU9j3tNlBhX14FaC9SjSpCJnSRpXDOWOw==", "signatures": [{"sig": "MEMCICkSFZTCDpsZkfta7pKGzlwZ+tBxe4RSMQbOrpuGVdXTAh9srNyucc1uClSGxu/BYaREQfdYesqqrRLghgFN+PBj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+cCRA9TVsSAnZWagAAzRoP/0RqOGpO2R57tzFte7Jy\nPPA1UY+lpVDnSIg3euRaW4FtYP/DEfNzw1uFe9jELXmsqFaiRShiv66I+3na\njhJ3kuipZ3CubTY4fHWlE89WWmhFeLZADkBiHTMjMTIv5lU5S3mR19jqhpNZ\nh1yaONPEErkAmwzh59mnYrmOvoxoTZTRMCn53yoYyTN+fqUdUSxoL/X73Eso\n9ViZ1KsnGmiMCMG0GVpW+NMiZtxZxpmK51J655TAiUq+6WPLE2bpHoa36h3z\n5rU8FUDQZo9L1o4P52tDFIMsIi7VA9QZsQa6UH6qGYukieD92pyBRunM+q30\nHdHkNWKS2nHtrlcH/UYEIQjh6zon0CQCYFG9Bi03/YJOYGUX+xV4eRLtTSbN\naeWcetSQq+XwTn1x6MpBtjic2N4vVU0wb0t3Y9Yu5OqXiq+l0pRKpSToRTIZ\nVNcgC/w+IYpLvRHuc1SCefIrG9O9YkJRWIpEVyldVSFVKIdTYWWnmnj2deMd\nTZMumM3B20zfDVBF7RwASt4jYs7TXYQfWnNRasObuxw5eJdkHDOUznaTTXkv\nU5aR6lxUBfGHRWlNy89WKodNfpZRm22TwtGw8HcdHm2h8KDd474MLIrUGg9S\nJwJWl4+bfXYqlH9VXPixW4CZ8S/fegtSumdpqKGsL+Od/FQlKYiGxzwHY++d\nvky0\r\n=ZV8x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.1", "@babel/template": "7.0.0-rc.1", "@babel/traverse": "7.0.0-rc.1", "@babel/helper-wrap-function": "7.0.0-rc.1", "@babel/helper-annotate-as-pure": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-rc.1_1533845403952_0.7251733111528216", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-rc.2", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2025ec555eed8275fcbe24532ab1f083ff973707", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-JqxhwM6dK1vCEeGeKqSssjD/yEOo5EophV94lcnutP355UApapzzNnw1hJ08irqYo0nYOqpiMP5uIdBhwxmu5A==", "signatures": [{"sig": "MEUCIQCzkAJvDZrXb/5rNvFNLHZTOD1jptj+AdUIwA6rjbxzTwIgLUJpH2+0Bv0F4nabbZh5E4FNaZIisOCKvSBxK5Hym+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdTCRA9TVsSAnZWagAAqRIP/0ZW1CDh0C7oqZ9p1eQB\n6RaozesILgrY+jQ7G1UTsiY9HWCdKj6iSKDtENuPLZljKkq0BSH5M0iza9T1\n3sMH/lC3E7ZZpaUfdGWm2/U7OOoc0/ODhPYvtMOBbQRRMXiGMZKM4YcOF7Yt\nPJm4IDYDgg5d8roJK3o4NWZpbAP0R2V7B5oatQNIKcyPCv7eiRoqg8O6k97E\n84c9mmr33pkBGZ+6qlLvaMTD3gqxH6Zb3eK3Q0yIBTD6Di+LUMvVeVw7mrSz\nLauMl7RAGiH0SptoCz345V7FooOo6OKgfDW00sGtmTs/hmzh9A40LhnAwK5y\nTbQFYo4kiAdhns+rJXBljJi8Pe1P5J6n12QTUP0xBsliKsMGpnNovWEFy47C\nXN6fHDhV8qwqm9HXzhZasmZP1wuojFvsAF8YhA/ZaZ/uHBZWPTHT4mF0pNAW\njpSwSQMQ1i1o+QocTbeXqxqs42IfFqQGIE+VN43h5EAQhZN+TVzYt8TdeQTT\nLOeAZnVTmzaDVcHsKudJvncDECMVrafmEAebFeVBbrANZHq6j168V2hGNxIF\ngq3IiUTV/hdrBbzBD5WCslkhY3BTeV0EVabggn203HS/RILQVejperq9Szwr\nsJ6i5VkEXFe53RVkKdLb9M93QyQ5/uvRrLtn9uu1qzsmVlrZ5RyKdB50Y3Xd\nVLL2\r\n=pyFy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2", "@babel/template": "7.0.0-rc.2", "@babel/traverse": "7.0.0-rc.2", "@babel/helper-wrap-function": "7.0.0-rc.2", "@babel/helper-annotate-as-pure": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-rc.2_1534879570478_0.15497780612641754", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-rc.3", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "085d673b34b9e57a15325ec0b32dc0bb40836b39", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-t7c3jiKh16BSGGPQm1A8ATvj8Z53q0cGv07CZy3/p7lGb96Be8QuvXzXU2fR+oevBdO9fzpLfmEbzqzo6bVtTw==", "signatures": [{"sig": "MEUCIQDUtraZ4eASWax9LNcCwwQsyh+Ses2jWE2re3YiUUE5pAIgd6q9+3pqs9ViuBqspgZ4wH9B++rE4B+qpEQ+rIbubZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnyCRA9TVsSAnZWagAAqFwP/1jX5wUhOFqihXwcgHEe\nMuP7kD+BQ25hksa0iFYvlACLkzm0/OsdN36np164+4+QCgv2/wntcIy14ke7\nuvCnJviH63rcObu6Y70iN4DTO4y9xqWshmS2oLqmR+Rl5A/JLsnl/i0eTTMx\njrnTW2GWgOGU3k/ZwNDLb0SV5wSG3/zTg119f/Cx1v0yJ692dcWZlF4hR8ib\nAUFfsf7ychQ/pxeVEpjT2aIIBNPYTUWu5xPDZVDP04HnXoPG/uEgEQa52ftQ\nJilUEA8uUY8v2FSTrdOmnsiNySGj4s55dhD7ZKiUbRaciwcnnMeV7K2dK3ab\nhyDG2qMl4eGYMZqdxLeXnmwJfrl2ethQyXu0Tk8bW7p/hGQ/skdY3uheG9Tg\n8ILXu6M+1FvZEiqMj6Sp6cr4cytssB2uuIEckiIKryWhM+nYGTmtnRuAKqEj\n/ZitLpCVP9xbA5C0jidlty1tGKtUHDRBXjAG92z/EbYBU2ONJm1GqYnB0DSH\nZbkAoUQeJ7sUbTUMJg+bSsHf6t5uEz1keq/0TpXDFA3Zu7j7yvyqbomMcEd2\nNaPFQFLp9ZQ+GPbh1Y8i4lnbf6k+BDpNMAsIPuP4kIpsB69tmM/H7Fpm9MHD\nlnyCm75pblcnfCPRvpMMORz/0tyC4Ibu2nS5KCrpOXVG1CPeYBSMNkyY/XR1\nlsRK\r\n=I19y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3", "@babel/template": "7.0.0-rc.3", "@babel/traverse": "7.0.0-rc.3", "@babel/helper-wrap-function": "7.0.0-rc.3", "@babel/helper-annotate-as-pure": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-rc.3_1535134194303_0.9377377490415895", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0-rc.4", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1994ec85cd7d733ed350ef33d228d62eaaa022fa", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-371p8TYaoLpJJ4ml6oGQijdHGK/QL5XBKjOkEJ+UNfnVz1egCYKn3UMuKS1c7yYVvASzB3KyYwiKyxBTW5Nt4g==", "signatures": [{"sig": "MEUCIC+ybDYHd8/Ry93uzhTJYjmM7SkassiXlfdBgmIjnR2KAiEAoJp+/x+sYrINlV26uEHclv/6/WwiP8v+wUiaYQsXNWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCruCRA9TVsSAnZWagAAy74QAJwvyC/D/+ZwXta8QhoQ\ntjJQ1v2WdEmT8ecx37bhNRqDXFWe+mRb27/+9GHEZHTkZJO7niM/LF75nQq1\nUXl0vm3nuqMTLdnGXLKEjjBTj2OabSutayesKoiOgZ6hsiJa2OgnO24UcRmK\nYR38xSwtY0mpaHI9n9ydSQo+RcwVEiEnIvIXKZ0OHEWzTZC4ZY5/XpuuSc31\nyDvcQTmZhxl04FlCmhrsUzZthvwUK99VlpfoFunDS0Hr9GdiX3nqCUSSnKwQ\ns9ZjI3GjabViRTnGyokXEYe1/0Q7UWOkBiekkp3LSlPj/9wZjxXjQM/sefJA\nar9u8wAsGFpLikyxZ6EwFiT62wyGa+568NFxzAspJGzxd5R2TWfA1nJWV6s6\nJW7XXUGpZnN/iK+imufIryS0snAJheoUf4PtoLZaTqJU5pHGWrn37Oz7i6dw\nExcQdhyW/4kaVCovoeVnoSCNi1b5iPlDF+TKnWHhZcnnM4p/a7UxcUIcDYen\nY82RC27YygKBgVfIQWsNkc8Z4kNXNjiUwpVjk+ePDSJjjWKYwYw9Hd2sHRsQ\nzXDeB8ZhwIc3As5wKL5ccLtkBPbn3XC21hWA9XHFQix1KBO6vzdEM1DWu77l\n14Bcmb2KuqVxi5przxJbuxL+hQK+5Pd2wem3svSn/+ukXGJTEoxiYF0mQyh6\nP3hq\r\n=vlWd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4", "@babel/template": "^7.0.0-rc.4", "@babel/traverse": "^7.0.0-rc.4", "@babel/helper-wrap-function": "^7.0.0-rc.4", "@babel/helper-annotate-as-pure": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0-rc.4_1535388398072_0.13927920632078528", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.0.0", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6512273c2feb91587822335cf913fdf680c26901", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-3o4sYLOsK6m0A7t1P0saTanBPmk5MAlxVnp9773Of4L8PMVLukU7loZix5KoJgflxSo2c2ETTzseptc0rQEp7A==", "signatures": [{"sig": "MEYCIQDEvZKMOMmm5/h1rGeKJK3E7xwsp7sOpTngWY5DSKkzQwIhAP7w502D9PT4ME61VtKqKHxri0ih9yRvQoGGblEvAsXh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDiCRA9TVsSAnZWagAAqwgP/A7eUzE2eTJagt5MqOkU\nT/SCOBtgWX8br2j53VPKQT7a3hcTGV3Q9Wnaatz5EMknqd6M4OBrMP5klI4H\nEOa6BIe9NrL5WgH/CiGQO5wv6laku7bHONf7HWldfvDC7aieGp86qyl42Ct2\nuF3+jcgbVowdIl5ogot+QtQWBJ22SXfs5VJXaIOovK6ZUFlegYYOk/7UWWPb\nNYLbOMHjR/mHCNdHhzMkRmvX2zE/YqPjt2tR/3iekZv5IdVgIkew9pFK5+q2\nefd1PDTKIc/mfVVhLjeE5j6OrFn0PndnH4V55ZIy3sRAZnZXaeF2E0foJQYz\njR1GXa8qWLQjWGTcbcst5Z7SD/sWLJ5v9dIZWkJsZJPIsJPKiHKKnzWxgwKl\noas107hUMHN1n6bRIIsoEiL1HBVLCIImFhXlQIYm7psIceh3KFQBdm77Mz4L\noVTaVBbihYHaJP6dTl+79WgLV3yBI3y4NdzJcHHNnqDadOROx82Fntrcsbyi\ntS191/jeg5vRfWqCuML+ofQoagqNgL9LkvVPq2arj2Nl6CaLeS0ZosrXfRIC\n93SYFFISsXJMGnwJuGbQ4zrI/5OWPjNRh3YtjeAbZ6oJSIUq/RLy0n5Sbf5o\ngi1HeUGhGodgnrwhlVKzGNVv5HV3B+uoLd+c7xo7rvqBaOpFeYayS34QA0yy\n03u+\r\n=r3mq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/helper-wrap-function": "^7.0.0", "@babel/helper-annotate-as-pure": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.0.0_1535406305457_0.23024874461388967", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.1.0", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "361d80821b6f38da75bd3f0785ece20a88c5fe7f", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-3fOK0L+Fdlg8S5al8u/hWE6vhufGSn0bN09xm2LXMy//REAF8kDCrYoOBKYmA8m5Nom+sV9LyLCwrFynA8/slg==", "signatures": [{"sig": "MEUCIHyfYW1liarAj5zucou2U6d2nRKuMcuOVJrY1uMjBOVKAiEAwLNl9PEdZkmSN59ozQOmNFH92YtHcpa2onYmPO/8UkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAEFCRA9TVsSAnZWagAA3VIP/2OKfAHsvr2lyDQg/4p3\nvU1MB7ShzDh5gDrZIOzLOwsHkSqeCV0DHaRpsyx7us35o7Np2PtByjwew3Ee\n+LkJGzigzjUabPq8ZnGorf4/2/ET3YXBB73xhbw81eG3Ehf2S0t+h9xHzf8H\nB8TnAv485zlhrIZT5KT5nHNUNyDV57u7ORR7xNKA6C7INShvvbTS99YXGkh5\nfDucvGdy5BwmA6xAfu9nXvX++8Tziz1SZqXLin2+opBAScyEqZvJUwrL7Kfy\n019XMg/VZDBbySlIdSZ3V+sdBoTxjbgHBB68wYi+oVkoMA9vOBG9WuyuLjaI\nB5eZ+AqgQQ7YC4/6LUDQlpCPwO3KtIthlxJ8FCpum6T/nepLZtemXmSDfjlP\nbI70xunnqpicL0snx4QPCpszLnlGAj/Cm9NxUsKKUT8u7lIHbxi3FOn3CrZn\nMTq3/F9ufs2IQ+Oeb1e3z0FicJpykGauFHQWe2qhbcXRl2HVwOovvZMEcACy\neNmW2DUR4mnPy6yGqYMcBl9VDTB1NPDx54VqVoeK4ZzVCeILnnN7KWIxsSck\nrqaz8HqjQx4CXjgCe2OLMPdposxhCe1KvZ4zmonphLEzEwn0ASKZtzkWx8c5\n7EHo6njuK6sfDathWUT1YM6U+zTVG8xu96laCQtbNfFT7p+pIhzD+WN/jqVN\nGjoX\r\n=DgrU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "description": "Helper function to remap async functions to generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/helper-wrap-function": "^7.1.0", "@babel/helper-annotate-as-pure": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.1.0_1537212677067_0.6426357137481102", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.7.0", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4d69ec653e8bff5bce62f5d33fc1508f223c75a7", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-pHx7RN8X0UNHPB/fnuDnRXVZ316ZigkO8y8D835JlZ2SSdFKb6yH9MIYRU4fy/KPe5sPHDFOPvf8QLdbAGGiyw==", "signatures": [{"sig": "MEYCIQD9NyzqM4LLwXouYFhf05+P2ShAvGkVQgu+aYnXC0ifxgIhAK5nLSy4onzBVr+uGisG57/lYHmigr70RqGDKDZhcudo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTJCRA9TVsSAnZWagAA8XAP/0tWXBfnmR6vAEVXf3zn\nJapPvVureVcCDuRG54GfR+VAEpzVq5XN8ucPKgYBuFgVEhM0vyh6dohO+dJ6\npSYnn16jjl3H2sMTKMT/Ku6iotEGjmedwY3yduUrESlKAjezli9ve1V5lM73\nBtXe9dQ+IhkhgbYlo8OgghaT3ejdVBysmnks7xEJTTu4EQUrJnF1Mh4fqo9o\nJj+vTt4k8bA97fpc1pjLZ1jvuypFT9cq5X77T8Suv/mLz3QjAF7mPYBh+rK+\nCw74D2HJdbJ1cfR0HRXGIO9pFVCPvdKyKVy4a4sKkiYsJIW8XSSMEuehy45/\nF94pwxylJME4Cyp1mJ7mzpoZzxCK1EsFv/KPTQIlKrt2VVeft7KRo0LNArBM\njBCZnDJYmWss0hpTwc3f6seE+7UGSAAl1dTrgZxgzWWPyi3SUgtXmgwT5phg\nKP4144mYekJR9zkWEIuTyvrGvWc1ZFmLVSCnGPLodyFA3rPv4Bn/I5l1jPQY\n4buaxTG/JEuy8wwOd4SbHEt8U1f+ykECu3qXWu5leX7+8ABBar9mrKedz5NW\n67SCOh3y2Lszp0KfGWlQ4togFrOvDnm1J1RDuYKJMhr9UNZUF2Zd1MAGqPwF\nNOnccJ6kESkMckCp323uia3QU8g2QtEJ1v8BpxLvKZnmeFzD2p/I2oxh6vQj\nT/s4\r\n=D3AF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0", "@babel/template": "^7.7.0", "@babel/traverse": "^7.7.0", "@babel/helper-wrap-function": "^7.7.0", "@babel/helper-annotate-as-pure": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.7.0_1572951240950_0.5423151313218875", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.7.4", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c68c2407350d9af0e061ed6726afb4fff16d0234", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-Sk4xmtVdM9sA/jCI80f+KS+Md+ZHIpjuqmYPk1M7F/upHou5e4ReYmExAiu6PVe65BhJPZA2CY9x9k4BqE5klw==", "signatures": [{"sig": "MEUCIQDanT34sKzXwYAEMwmNo5F3bVHM6E3UoM6y0E6KtRT9uQIgVgCZuA3DhTsCuMbYP4IiHjlCZjXI83/2CiSgYoDnuLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBlCRA9TVsSAnZWagAAznYP/32P/LyftoSuwxDgy9ge\nbwzBfIRAAA9B3L6mbjac2hwUHcdQXz5vdvRXb+7NpUGVcyugo0/nJRIN4OPz\nmRfS4EeNLrKGFHKXhDSCdT7JK3sgCE09YrORXm4ifPPi7siBDIOH4VoHa8rq\nwFCPmuXsFFb1xVQV03r0Vj25XuBoCm2a69TgbpWxxGoit6+ZQZvLjrAqT+ax\nujeFdLyVW95bvAbq3Q0t8MOFt+e4+54Cn+oJESDxI8ghk0lBOeQwErL0s5Gm\nxdMsqZ5DF2y+uZjXp50veSBUMwqmBtZUxIH5xVAsjeObO0we+lSNVU1m9Cvs\nq67VJjBCywyXCBd40QwX4cccZ5/qX3H5v98ogtkI0L5aufXyxhnaKoCZjEJ5\na0IgM2TUGYX5HXGTJjM1NeSCpc2h0wGoOcGPIGLe7L5IVYowCYWXyNCJT3AA\nUn9p2BDAciWn2LR2czGG9CZ5gwjpFLj3gVQWBWGiujx2E2bNPm+u3npFXfnq\nw0kNrkgqdS+1IdTfpVxO3iSc0sIOakA59P4uBJNd9TwRNBnelvHr227x5j54\nO5aozvHPvdxQvN+NZSaqbuAdI5Q1vUKnoNOungrd6UK1flt6U/v4hsi3XujM\nA9p67Ziw+7tGR1KuDVR41tDuvLcJrlfLRRv2PEqIXDtL/DaQi6jonNYZeEL8\ndcyr\r\n=hHsi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4", "@babel/template": "^7.7.4", "@babel/traverse": "^7.7.4", "@babel/helper-wrap-function": "^7.7.4", "@babel/helper-annotate-as-pure": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.7.4_1574465637444_0.5063206532968925", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.8.0", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "034c21154dd12472717cfb31faf1713426fbc435", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-+aKyBd4oHAaIZgOLq/uLjkUz7ExZ0ppdNBc8Qr72BmtKNAy3A6EJa/ifjj0//CIzQtUDPs3E6HjKM2cV6bnXsQ==", "signatures": [{"sig": "MEYCIQDVhtiYJyZ8e8eNwDwQAb/DymR3s05pfPCZhkM8k+KkuAIhAPf3tKc3tf4ouGn1K2w4pJrqwMtkdGifK5My2OTYn145", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWfCRA9TVsSAnZWagAAscgP/0UNqrnus1K8Kfi7vEts\nQgejqe7ZdGubCz65nhu2zg52sqO5aHsecTi8yUmhnsGi7T5NbrgsCEO85De+\npTBXqeKqOsGWD3WMyqsEQpxg4xs3Ol0Q4rABU5e3cWP9Pr2vTpgCaQFqq8vA\nFGxPfFCtWnl6OqDlQFbjQLTeCXoiPemcQX9DLLhSiJp9Cn4JU87leNO4rkNU\nxT3iJXW1JGJbylLC/X6aIwjKcabKlPQCA3ovGU1VEuu92x75wySZy56kpPhi\npNnzeFgSV8wIz8Ep4jPNI7zJW/OJsVJmda3shLE/n1NHrqOw+uVceu6ZlYOQ\n7C6nEq/HCFs71WfPko+OgsX/1Be8mJ8bI+ST7plpYgwK1cC6TVNN6odyetHx\nSQdespYxdUVx2UfbXTyMbhRvR1Gm3kSYuudoLTlyq9lOvph525zfQenMb9E8\nVpUGYoirQUIz30cFG1Yji+7ux+ITBq38i31hyNrujOv23H3KNktYnhk69MiW\no/pNz+jsiyBgL1TmcHGVxRCCzA6kbvzZ6SGZP9lnZG4SbhczrRsg7P3I+UDq\nLme9rOp2CnAlb35k4TwcSOnylO5Q2h73nUMluN7uZ4kFfXm7tDZ4vHhx2YsY\ntQbl14M0/hLx/u/OM6zlGo+Wd2xzd1hVu6pdg8ckFfXII2pUgOudgtAwslkI\nEz/A\r\n=L1hh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0", "@babel/template": "^7.8.0", "@babel/traverse": "^7.8.0", "@babel/helper-wrap-function": "^7.8.0", "@babel/helper-annotate-as-pure": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.8.0_1578788254927_0.2997016620901911", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-remap-async-to-generator", "version": "7.8.3", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "273c600d8b9bf5006142c1e35887d555c12edd86", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-kgwDmw4fCg7AVgS4DukQR/roGp+jP+XluJE5hsRZwxCYGg+Rv9wSGErDWhlI90FODdYfd4xG4AQRiMDjjN0GzA==", "signatures": [{"sig": "MEUCIQCWjyel+Xc+GLIg8p9+SESehc3BRdA4QQ5wAFz7XDmufgIgOku4tvzLkpX01I+xTVJmCqTpDIJAWuO14zQzFHSTt3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOROCRA9TVsSAnZWagAAIFgP/Apf1IPATpDcYL8EB3d9\nSodjSiGz0DPr1dr68bB7sGsMqox6xjGQ+b7fwrTm2HjBEK0Zae7cS/M9ceRB\n0ctHQAl1/mQVwQWpdgLiF/8Jmv8S1BD3TanbTJ6fNuBtuGlvFarLBrhOmyfM\n2HDuK0xL8WMxc7ahYVb6lhF7BBHtalF+KifxFLYy+Uk9jVjZf7c60q1Ces4t\n+N5L5WK1h8lajyZpwfVPszBDyFNTOrVCoycqJqhxcR1BJLoppkPxKZd1XkM4\n+2L6W6kg/EkFg+cRoueKE6Q3TH9vjeAhLkm9WIK6T5SMw3B+tqsDW82cMuHU\ni6SriLi4yu8pNhm206vfNKxU/7/oAXB7hq/ae4vizdztW0Ue2+fxa/jpatxC\nlbyEQuLkkTq1zJeH8+psIt0Hx/tlZoC8DuDwTiwPUzL8keo8OhCQl8NFi1gq\n8B7+HnHt/ojJoA7egeWx2ObAhA4zagLQLk5sKKpnJPGDfGLi/JcEKYnAK9Tq\ne+rarCWNFqrzXLkMzm1LOCx+YriW8kerDNyrFARnJSasMWepd0CwmJA3BOtf\nfLiJkQnLr+8NuShwiwaF3enak67AQWJtKOtd0QVtTr4ZBofNFQZRY3Dsi3ST\ndbzyPvjNeWyy5PasWIKPzYhCUiqZkOIglMFEcybiSPOr89+kQBKV6UaWMxNh\nTNF3\r\n=CflN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3", "@babel/template": "^7.8.3", "@babel/traverse": "^7.8.3", "@babel/helper-wrap-function": "^7.8.3", "@babel/helper-annotate-as-pure": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.8.3_1578951750866_0.04708855239026821", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-remap-async-to-generator", "version": "7.10.1", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "bad6aaa4ff39ce8d4b82ccaae0bfe0f7dbb5f432", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-RfX1P8HqsfgmJ6CwaXGKMAqbYdlleqglvVtht0HGPMSsy2V6MqLlOJVF/0Qyb/m2ZCi2z3q3+s6Pv7R/dQuZ6A==", "signatures": [{"sig": "MEUCIFHgfE3ngCPt/zL3TpGQgat7Y7BPOgFtPB+eheedbnVmAiEA7hy8ZxYtXkcdigDG4Q+xOOXuq4FnDN08G4Pgm4KgAsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTVCRA9TVsSAnZWagAACrYQAJ70A3/sNEAaToWHGyxv\nFH+rjFu+7K6iCAKQOsKRg53rWhlHL35rBs4o1jAsm3y+j6yQblYwUM2zIAKX\nbeOEX1mNfS5gmrO0Xgnpkpx5U7XxBg7hUl6yIoFNEpU4leV6HSTY4D9kit6Z\nTqjrBJkJLarLymPECAGS81d9jreNcLyoRKzaR7d3cl71uQuqeAfBz3BJQ4GJ\nup3FneaP+i1zW3LxDuxsVoWhVMbSjP7xF9tmF/y6HdXSpqXXhlQXOjLp0IwC\nK/Ca50hGTF6CABZ/QUaLnRtJoo6anC5KM/nfq3PNAKqnjt1vaekk7olbY64U\nYNUBuauSKj01QcETKpggfPkeDAQnQwCe6+PqU+kmkGlLqOp9ztqcbZMsn1rT\nkuv5T8YfG/uJ0XGur0YuP5ZAypSsTKw8ZFiNW3ioEtyupjJ8SlJ2aF2qaD+J\nAz7DtoHp6mjhfYP7wHFp0/53suomwFAWta15VCgVR1n8M08m3Q4bmcdROfnn\nGRwCn76ZkqtwQKx/Bm4FBDfXBYbcZKaNC5pNP4wUlu7OorH913Rl0T+UtsYj\ny9/LPg2JGuId1odrE+F774I0nB3OJZ2gG+pmzFu63TOn5NoVb2JDpYlPErXN\n9xGOjuk9ImC9EhaIqXhCa9bW2uQRnDQKnIJ3yT2QMSuYxrzpvIo9ZGM5UcaN\nBRZN\r\n=WEnQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1", "@babel/template": "^7.10.1", "@babel/traverse": "^7.10.1", "@babel/helper-wrap-function": "^7.10.1", "@babel/helper-annotate-as-pure": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.10.1_1590617301403_0.3746490309134287", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/helper-remap-async-to-generator", "version": "7.10.3", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "18564f8a6748be466970195b876e8bba3bccf442", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-sLB7666ARbJUGDO60ZormmhQOyqMX/shKBXZ7fy937s+3ID8gSrneMvKSSb+8xIM5V7Vn6uNVtOY1vIm26XLtA==", "signatures": [{"sig": "MEQCIC4Aryh3ySs/AlBtzNct0/RjL9ZnjebLTrBtZ5HaWzBvAiAX8VAZC3nLmU4Y/+R5EkK/V26Y6x9Tpf7X5oInenb4zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYaCRA9TVsSAnZWagAA1eUP/A0jNYAjHiMLZijljk/4\n/azpgzE4nC46+RCt+ieiAbv5s3U0dcuVEGeh8iIcfjH5ucp3LqTJbZWXVCP6\nq4kHEV20DLjJYg4fseTG9ZFMj0FH+ahXzIQ/LRemPa9vZrVt+W3OTW2EdknU\nuMOK/Zo9tdvHBzMBy7fBO6ggOSZpnI0z32jOmbyQm1SKwHS1KyIg91E5Cwux\nRG04AqHyNChkVlRYN5APTBEz65pkYfQymzzm7Z64vtM7qXj4T95OW7hyA6tL\nqtv5SfQWtadaQX7xt05RvgFf70wycF8pS6XWCrQlSd4kMSibRwtBnQ214YeN\nY6J4S6RuLe+FZs2jc4lBU6+gft++76B7WT/TMvnwkDt/jeTlfWyG28VWYvvR\nF9wNYImvIyGIzPsWDfx/GqmYy2KMpRu+tBAFikzImGaEcvRhsaJS7tXr8AGL\nr19wlyFsKwIyEQMiIXfdftA4jqq2oKporR96UkdnHwU3YQSYH/NzaCDzGvfo\n9zlGBebKflkieb9ECWkBi5bExLRKnV0rrcfUVIuzDHISPQCQ+rnj+hA7UpQ8\nVaFX8nnPRCi1gsOV598X3HVrkGN2O1QhoGxdOcgQvXysnSMyoVUbljpgkC/5\nOh0bVrNvpxwkH3F0yiJ1Rs6dAegPnoBeFk/gDRuxS6KmaQ2/F6iSTYvCobOw\nkkxD\r\n=2r1z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.3", "@babel/template": "^7.10.3", "@babel/traverse": "^7.10.3", "@babel/helper-wrap-function": "^7.10.1", "@babel/helper-annotate-as-pure": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.10.3_1592600089693_0.043764808768146146", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.10.4", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "fce8bea4e9690bbe923056ded21e54b4e8b68ed5", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-86Lsr6NNw3qTNl+TBcF1oRZMaVzJtbWTyTko+CQL/tvNvcGYEFKbLXDPxtW0HKk3McNOk4KzY55itGWCAGK5tg==", "signatures": [{"sig": "MEUCIQDnB/KXBWGwhPkGsJ8jXXWTxdrV3rGVcXYfOw/bBOTDVQIgIa1szyiK488l6zdtyZiO+Jw/7Dbz0K84LowJZ2l1VFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp0CRA9TVsSAnZWagAA3U4P/AktGbvLPxHJnN7UN9/9\n0V0NqV76sujbelRrdss8kfS8D8Lufc1zK9Rb2RNPObgcsx2wfSaRbC6o9XpL\nraQjDjeMo+YB2LthZocOz+ovPzz7mRtMDR0IA5TJhymozqqn4q3XT2L8oJbp\nnIprF8l9XvBOGjhFtWhxTiterSjKOoD0arnqLqtAp6sHGpzbyC4CHUN0xPFx\nxAWPJ8ODqlhoH5O5mk6IoGI9fqgiuA5HiV/R1y4BBZF7yNrys7RFRASaT3YD\n9KWr7TDJexMH1944vmzK8nlLfcH74n7BVw4K04Yjfcb4+JT6rnO5elI1kLZF\nAywq05NsVSmQHc8D7iHtdN55/9jI9htpxAWNf7QTFWUtS8xhhyfx1YLH61Qu\nraKfW3hUAEkxEWjrEdr9aoGbcl08/eQ13+iN8XKhzr+LyVrVJCs8UwHVX0nB\nth3FdkCKvK8mB+Zf3NOoIpuMgxMTZQIm3f4KeqxM4je1uziDtR07J2dcfmIi\n+nA+BXc30x1MQrOstk4UtQQGoVxIIE1mBlT4d2KOVIBwLY8Xv1ihL/8vh9iK\n0FobkiP92Jexyq/u+uL+8moSwTfwf9ioNJdwzNhV4S6/O55sPk/Dg0MDGbH7\nEardFF3urNDTGSgrS1QAyXarjkciYvMAaCm/o/kY1r2TdBeD7RWtjiTEPm/Y\n127u\r\n=U5Ww\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/helper-wrap-function": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.10.4_1593522804059_0.36636067870222067", "host": "s3://npm-registry-packages"}}, "7.11.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.11.4", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.11.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4474ea9f7438f18575e30b0cac784045b402a12d", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.11.4.tgz", "fileCount": 4, "integrity": "sha512-tR5vJ/vBa9wFy3m5LLv2faapJLnDFxNWff2SAYkSE4rLUdbp7CdObYFgI7wK4T/Mj4UzpjPwzR8Pzmr5m7MHGA==", "signatures": [{"sig": "MEYCIQC1pAMvUiP2Plk2zb9vw15TsRrYztIXeC80vYvlPrvlzwIhANVmi8BHP1NwVjMwA0xnqbYkrQAjRnHP2BcrO+G6OtKe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPsgjCRA9TVsSAnZWagAA0BAP/3IUA3glc0Nz3cgvfqXd\nvqsIXwaOSDGHx2H/ox60X4ouVn1AYpHtj5PDp/JiLtSRhdrpA52FfjLRF+xP\nzwy4oTtf4AJ9s5ShPTSQjk0r3H+LcxMvYOgG628KxvgyBcFTC8oc9eChJ7BW\n96y9ae3yEbD7jHk1mWpwM0/8ubQoWk1+cSuO3Ig/XeF7I/9ES9Le3QfA18/E\nm7uo02qUF5rICKyKDsS4yv7/QB0uU5v30pxkZYYMgL/NJZhBcxe/AQaGXx3e\nY0V6uagD8LeJO0MRzsixykut3iUnu5t9PMAYW/TuxaHHZXwPoXgaGPU16//+\n5OePDEhUg+4ecQk5VHpQfGciNjSc2prpqSs5BHQ6dxpM64kxleSHo8b9FT+a\nNIorNRYg9bGYEkEutN+GE2TGUsxUZ4zwgXB1h8GPAwSzm28S+6NQgBWsf0vD\nqOGJSpsn1PDuXU2HEbbiKxIQYnfY/p2/HOIBYICK2rbeEY5lJRVuv+m2wTWM\nIfvAx/rOZQ6X3FAHCLP1IZsKEAUALO4JwiAvuJIyDW8E1XBgiFHnEmG5j0F2\nEyBALdCpN7/vifvnC85A6Z5a3EaVrjKnNtYKxTfEoFfVX9BNclnOFFfPrTNo\nTarbEXNQqPKuwPV2XoL1S0scDf+Lol9MLfOxo+lLIoy6KLW0ViUR9kInF2lO\nluOJ\r\n=m2Sj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "90b198956995195ea00e7ac9912c2260e44d8746", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.7.0+x64 (darwin)", "description": "Helper function to remap async functions to generators", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {"@babel/types": "^7.10.4", "@babel/template": "^7.10.4", "@babel/helper-wrap-function": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.11.4_1597949986644_0.4414878902072008", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-remap-async-to-generator", "version": "7.12.1", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "8c4dbbf916314f6047dc05e6a2217074238347fd", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-9d0KQCRM8clMPcDwo8SevNs+/9a8yWVVmaE80FGJcEP8N1qToREmWEGnBn8BUlJhYRFz6fqxeRL1sl5Ogsed7A==", "signatures": [{"sig": "MEQCIBW7+e1EhPxyOvdO3t5THvv0pdWEjO3y04UeVsQlKa73AiAfNgRHSQAJ2/DjTJDihPGioJVUWoFQEehVSzL4f6PnQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/6CRA9TVsSAnZWagAAIXAP/0+8x/mnKhGdKvwoHm8u\njvOWh5WMKIjSF/LA1B2/JkNcL/xN5i5O8glOfTzNOyAx+hC2kY24qYzaxKEH\nraiFK/vf/9WVE37hJNeYd4GtuIFUNDDTqFTs4WrD0xmDAKYV/Xh1Yg4g1hyg\nL2CKq+16bggSixP7OYIxQSz8LzA1xYQCcr8vi1c0byFFqkwo9U3rc9bNn4/H\n0DLppEE3FlFYVMJea++N3dcjHonBOvkJX1ICig+JVvwGL4lGV1r5XPz41BEr\n4br6euFOvcnxJzqRjJCVtnfCaUhKvhmAFi2Wa03F4/V4+xumrTlpV+kT4Jym\ney4hxE12fxWJX3PdE/QOEidO6a+GzTrOwcmm8vGDE3/8HfKjr9L/FgVzV8S7\n8OIZvSCsp6VSbA0oGlar2IxGdTtkbw/vhZsToysV9xxSkLXHdQQoqlBxQlxl\nGwm2DQZB9fQABSDVyi+V/tpSrQ7PkUtMzCb5VgAwozvheZTd+vSuxXtV9ccB\n5cUAHfwq2zGgs2/qZ5Gd1xUPVnxTL9ozn8Wo11hydzheFzDCM9cWVMUE3ttE\nmj2VQttepLGg/KDSkISnI7IrmKa9HKgG65m8q7cUvvgHKitMjfVCuryxAMkb\nzohx1Brcg2BPch5Nv3kkekcoHTrJdZhy05obV/3Sy2zQjVhXljWu+5hMm967\nlXmI\r\n=eDuF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.12.1", "@babel/helper-wrap-function": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.12.1_1602801657774_0.2585106964157089", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-remap-async-to-generator", "version": "7.12.13", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "170365f4140e2d20e5c88f8ba23c24468c296878", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-Qa6PU9vNcj1NZacZZI1Mvwt+gXDH6CTfgAkSjeRMLE8HxtDK76+YDId6NQR+z7Rgd5arhD2cIbS74r0SxD6PDA==", "signatures": [{"sig": "MEYCIQDnuXji6fF64YpBcBZdEsUX7wGtXLiDEyOTdPXbij8c2AIhALYNQWmn3zxWJsTtW17CgCuf+OaYFZbTMunxHdMfe1TU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfheCRA9TVsSAnZWagAAbaYP/iQ4E33ODcXoBy9/wTcK\nVst65xxAW6svXKKnE3qpoiyjN4bxreEjNxaVb0Ah8q/OyuTE84RED7lqpS3+\ntvlxSzLpW8idgVDGEygWSCe8t1N7qFExY9UDuq1po6BnK89KFY+yeZO7Kf4c\nKKx+a2KoSnUGtAoGoj9C1z13D/aJSKtd6EeDOS8ds+XmpvI5D+kuJVMcXznW\nGOTqrgRx6MMXoOx2j3xaZB12KLPIJN1KZPF80rjI2UP3dAibA7GtAWvB7By6\nuqlqCzeY0SEnLTkRJAS53/3DcK1TCKAEzERpBcbZXke8IC95+0llc1jjQDZY\nDIV6e4se35WwK+/h4Z52sCTf6ewkYawUg2mrABpRU5xVhcEriSoLxMvP5qVL\nlSENkzAN+CqB2HZsOfUupIrkwA9mf3XEcLmOfaR4TEU5/kM/wtRSuo7twy1q\nCYm3gblcmbqEvK/vAxEF1AAUNGwMv2hRVyDWV99uXgrKWOk3tsul655wYRXo\n6M73k5jNS1WPRnRJWQ84DZPlJunTl8yKXVnWRjqH/tVmvwLydqFxDNGvL2fO\n/pGNg7rYKGyOoKZWMKcxIn4xURibWqJW6zdexJy01JemomETv4u//ihQRpGl\nDCnUJn6N6RYH/IyBbRozVqyiGjPaMJzgYO978EKklVEzyYkADJdWKTj2CUra\nXnL+\r\n=5cWm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.12.13", "@babel/helper-wrap-function": "^7.12.13", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.12.13_1612314717633_0.31356739842593706", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.13.0", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "376a760d9f7b4b2077a9dd05aa9c3927cadb2209", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-pUQpFBE9JvC9lrQbpX0TmeNIy5s7GnZjna2lhhcHC7DzgBs6fWn722Y5cfwgrtrqc7NAJwMvOa0mKhq6XaE4jg==", "signatures": [{"sig": "MEUCIQDdMxiEQtWKRqthg/WztDd5C76M9vppglAHP/1YBHaF9gIgHmhtPjv0aujAcpB6aQoAeq6gzzZyqp8aaT9dbFC/Glk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUrCRA9TVsSAnZWagAAP74P/irkFYdo5wkWCmnjjKSA\n+idLcKeQwWmUO8S3Nx3PTFyC6NssLMBliHfh1eBiZpaCVskDLCoEsUUTLMOf\nX1uTRaiB1qfaKCJ0dPpKbbLjVIJwHdpDk/eORFWLFJcmbV6Zfot/cI3WpSz5\nKX/OcsAMjQhTvSh1BxkTV/O8lAl8sQVF2BUUZjFqtYB581+gFSnpeID8o82x\nt6a9HDlaTcPIQy7ZZjT33IMaTXmpnG6yuFqJ+9lbvyBr6JT2zwjc8Yz26QzF\nIqkZco2QGYPku+JClP+RT5+ovdaDe1MQXgr4LkcMnR958aOByumM8+2+V/3v\n/kHkvGO27w06MMpGGAGHrFpYmQ2bQAXrJ7R8nex83IWipiJH+imnmK6t3ohQ\naJ/sWfZqSWbtt9k7UzDGHuQa6MRHT1e9d3B3nsJSVmWgOT7OOqhSmSSPm5rx\n+1h/manpUTfeX6LLbmLUMlHUmYAGXQgKMgCCU82pdE/l1h4cUd8y0FjzorI0\nwX+NuepBrbVdLYoQhUVtjgtRzOHBWHDhT0zl0nYP+FPX2goOsjoAtXTGXJv9\nynWyV5lt1bpgRurS+3dptJB1ukgY8DMZy+GMh5AaThQlJo7+Kwm/qnqRYpCZ\nM+C5/xVqNm6b72lziFXxmw+8Kh17tAVGE1xSclZ05mqNKQu//57ErRPuDIfl\nS2fQ\r\n=8AnR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.13.0", "@babel/helper-wrap-function": "^7.13.0", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.13.0_1614034218781_0.959535884927363", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-remap-async-to-generator", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "51439c913612958f54a987a4ffc9ee587a2045d6", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-rLQKdQU+HYlxBwQIj8dk4/0ENOUEhA/Z0l4hN8BexpvmSMN9oA9EagjnhnDpNsRdWCfjwa4mn/HyBXO9yhQP6A==", "signatures": [{"sig": "MEUCIQCn0fVxwW9upmk5lUjdn7u/Z90xC6Hme/bbKP+YWkKEDAIga24OUQ3nXXQ7Dd7E2x8SMvdT2o0DgoCTPnh96EGBAqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsFCRA9TVsSAnZWagAAxRYP/1jWeDOrS6lyD9uC2JFQ\ndKqmfT2sYXcRb+RxwTuJZNeYPlf2k6lHOQJL/p9OSuIAeCFH604MVHLa8MkR\nbosBf18OJfiL75tG9xaaaRMom8WdHwwMK0xm5FY+hPM/DIu5SNHFBG3PnwLt\nKfmzvEF5xG8eGzK6hN49Q4b/tf+wvgW+ONs5FXibJ6Pa+r0eg7siB/AqFFgd\neKWKljudYhFu2YRwFEvmFFRwyb5pVdq+c/tYjcxEXwqIgcRVoo2xSW2jA6Xo\nKp1VbNSPaqzBHoUASYaKBBCTdnS9EhAdrRK2QAL6SzS1J2nJlqUD69cwlsip\nu/42pclQgtTG4NDY/TY4zOsXozX2J/zdLriyP3DAfEj4y7rNz5vX5TUY1rQG\nEGjLnZAw9yulYclhQTjsGAlqCZIbm5/tMJZFqEjIpukh4DylSRZVFAfiGKE1\nvVoF+8IyLGtlKyMq3j1CoqqcL/FkyDKMK2JNl2c0mE5lbsfedsnyoXIboX4q\nBsYPfFN9uNbAFSWS9uhgqRg9kpMP8OrTZr2L3Vi1HsUOT9DzD+oz2zjBtmhT\nzTKHTvT69HKZCvdmMXLrWpuiQ4UjTyRta3CORl/m7QC9c0AvCBpSGOpUwh84\n5PdzGtrpCG5rKCYQpbEzBVjoeHalyL+RDLMw+3OYX5t1A1EtRXSpbxrC5TTB\nUZHT\r\n=I3tk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.14.5", "@babel/helper-wrap-function": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.14.5_1623280389647_0.30222656916982715", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "2637c0731e4c90fbf58ac58b50b2b5a192fc970f", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-v53MxgvMK/HCwckJ1bZrq6dNKlmwlyRNYM6ypaRTdXWGOE2c1/SCa6dL/HimhPulGhZKw9W0QhREM583F/t0vQ==", "signatures": [{"sig": "MEUCIQCsorSEHJa4UGgWeIAb6RFHS5tG9oriOE0Bvx1ZyhOLZgIgJBbyn+6l4L99TvuKTYlcJePaACESfR/btVoCZZYFPh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSnCRA9TVsSAnZWagAAwv0QAKSIo9I6e5BtcT54xU9X\n3rbp0Famnx3yccBlgso3x4eJ/PmT4SVByD6INl2ZqaxPHbEOswR+K1it6XxI\n2HX+MspujYsp59nJMFSqEzj3wdBSFv5GlXQdq+9C8NdFhR3ynZXzcZt09Xi6\nWKFNXbhLhyGPkxgFfsURr8s6nO46nJyBADmOdzSCCwAI2MWGf2gs2ufN0tmA\nrdg8mAYC2MFFibu2JL5CCWxmJEsE9D9mnwyGnuS2C9vfpcKsbzBYDUtVr/9q\ndIRVRqG3ljduP3nuK4Q45kug7p89yKaWjztxqwgJDrd636jIwxOkh//wtu9V\neLSsFbMoDGx0WY0TjtDZky56n/XHblaCFQsLy73bLjYC+27RfeXckdAtYuMy\nwg2cNEXqZnaVaw157/o8+f94NiBzWp4hPtVvGaL6flWdgY4t/ikMSHkb7h4w\nq0iUM1uesAzlAOPKN5oqtrTDHWi/SG5zEO1YjoiDwp53jOA3nx6a2//Q1vlE\n+x3I4loG4T7uj93OvQdiI8WRxXX5nEaM3zOK3AR3ticjE+ykJM5tymqdS4QO\nNZ/40oo56mKBhPjPO+AE+jSXGLUmKNlhAmw/wd9P5Ng9j3og1mVc7EhS7kHU\n4ImrzaJBAnclPHSxDdYEYjCc54OyHJleo5hnNgj+ivpfupfMVc0Z6kh6NJFK\nh3+y\r\n=+WPz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.15.4", "@babel/helper-wrap-function": "^7.15.4", "@babel/helper-annotate-as-pure": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "7.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.15.4_1630618791845_0.5924165687560425", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "d5aa3b086e13a5fe05238ff40c3a5a0c2dab3ead", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-MLM1IOMe9aQBqMWxcRw8dcb9jlM86NIw7KA0Wri91Xkfied+dE0QuBFSBjMNvqzmS0OSIDsMNC24dBEkPUi7ew==", "signatures": [{"sig": "MEUCIC/eF0CaqFuw00UAF1lz55tGa8ZVBTzCXsYocskbBALnAiEAm+Ca9FBt3kF1VuZ+rpXeI0p3R5Fw0o6LS5mQXT9Jr0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4251}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/helper-wrap-function": "^7.16.0", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.16.0_1635551275343_0.8613695102043291", "host": "s3://npm-registry-packages"}}, "7.16.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.16.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.16.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "5d7902f61349ff6b963e07f06a389ce139fbfe6e", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.4.tgz", "fileCount": 4, "integrity": "sha512-vGERmmhR+s7eH5Y/cp8PCVzj4XEjerq8jooMfxFdA5xVtAk9Sh4AQsrWgiErUEBjtGrBtOFKDUcWQFW4/dFwMA==", "signatures": [{"sig": "MEYCIQDm2wsNwy+O+WlQ4WpjzNmW0hIHJlu1jTAfWQd33f41sgIhALHDUgIZOloLT/MfSwAx6JBxHu4SbgQs9Lchxmpr8f/b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlDS3CRA9TVsSAnZWagAAuqEP/ioLrDSSiLBeWcKeQsGv\nMPHH6zkX9EKXIaL8PZ2ukitoLsc1OfH3r7qo08EN67neTVvy3gcvnuAtw7M8\nMomqn1zCJE5vALQm+IofJXkufGuf9oYAOrl97NLJuUeEgYr94dAddUxNpop9\nUkAx+PQVIneztH0kaaOZnqm434kpik/R2e3IyJjq0orLOojBf7m90Z2EYMVx\n/9hUW0lSFa9QfqvHJgAGmmnTvtoAyBMxVrvYBtBkdu7XQmlDJ9+TwlgFaX8A\nLlQRpvO9rdSffyxgVqZZL+Ao/iZTIyyOL2dMFSZFiXggcPV/EyLbQdFJ3VUR\nIM/3ImRqt9FLK91TZKhl186G1EI84Wy1esjrH5L1xYZ5975aGs44zANo/hNS\nMzWlJR4Mu4fzbt2q0Yj9EoKvd82C1oES48A5D2mOJh9HR2Z/55nDLanw1NRo\nnXVLl3EMdZm3Ule+MZ+U4PZK5nvHJjQz+YN6WKS5cLVuNgJ2bBJupK7QVtfl\nzMOkAejzYLutzatyHktXw0DgV23iKEvU4VhyjAcczqFpHq9XbfWc2/1ar5T6\n39wkroafvhQVIBvHwDkdMmh/kkKKG89iskcsSXetIerzAzHoCFwxu2YR+zYT\n6s5eWgsQovgEavVjgBNQEjPCyiR9B+qZi3VOeLGWMNNUzMxyKCkf7ZtZ4bJ0\nfhBe\r\n=jHh6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/helper-wrap-function": "^7.16.0", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.16.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.16.4_1637102775702_0.7339774525840448", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/helper-remap-async-to-generator", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "e706646dc4018942acb4b29f7e185bc246d65ac3", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-X+aAJldyxrOmN9v3FKp+Hu1NO69VWgYgDGq6YDykwRPzxs5f2N+X988CBXS7EQahDU+Vpet5QYMqLk+nsp+Qxw==", "signatures": [{"sig": "MEUCIQCF3SY+7XTcQ2rQLXbeKWql4RsxAzIwAI1HwYxonjv/BAIgYiGxy9eNdhaTToM2IJzFwYqryB2kXBW4g2LEBWUfaPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kiCRA9TVsSAnZWagAAluoQAIulKUsxm6CZg35Ayj/d\nEjTKwRUfcnvGq3mV4MVpTULib7ernM8KDrI27j3MfBM8Y3DU4t/f1AnWa4Cd\n/jTEAlMP9yDP4yAcGrJoz4GCwcCMuzAU4p6eXWLP7fcll0yyPhMUF9jklgQh\n2JVYkK5f5r+DBDV2aQE7ZxcBSZQXfI+FBp4nYOkDPs/0bzstpuDCb/BJbPCu\ne6KwHF3caYAipxZZMkp0gOjj9jY8oF30Q6ZFJUt8rgX2tVrAV43wa6pOb0jq\nRqeLFQF6k7kyau42A2hlnxfRgh0Ih5MnSuVpOFWlUvfHXLd2f9ZLR2/DXQJ0\nzMUgSlyeiw5sbiJabuEWlSH2B2eo4TCMQa2HRHU8nhyVN+7su63LkQO0rFgK\n7S7gNpafwY29uj/h1jMphGI4Z+ECFTFm3190ey/ObitpoF63yaB2oLEoQ9cH\nZlcdeSbZJ+UmN9TKUesL45GByQxoUEsd69LpHozpmnMZ1gD5qiK0OGyWSTpn\nQl6689TNAP51l3nyoucYAOC06MlNvpkk9QQYUmr1EUEuUjyiYsIGQgqU2KRt\nATRYIZLOKLiRYAOXb+9EY/3potUmlaEX0zGgS13b+2UkzVnW35bLVeKhldww\n01sQobLutODmbwbgtqpvPIxsHRyx2nL/2DENmIaVQWvfYtFNJRRlj8YFfkGl\n9Bvt\r\n=aLmi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/helper-wrap-function": "^7.16.5", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.16.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.16.5_1639434529854_0.6241964839396381", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-remap-async-to-generator", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "5ce2416990d55eb6e099128338848ae8ffa58a9a", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-C3o117GnP/j/N2OWo+oepeWbFEKRfNaay+F1Eo5Mj3A1SRjyx+qaFhm23nlipub7Cjv2azdUUiDH+VlpdwUFRg==", "signatures": [{"sig": "MEYCIQD1ez3/qGUnfnd6sb98p6goWayBNyn2u9zj58hyF3FO5QIhAOwuXFv7OvXgkBTLOEI5AUWgNV4TOS42tGug1a85X8WF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1mCRA9TVsSAnZWagAAx3IP+QEF5y2mO8klMqqJNOM+\nuciwkrmHb8DtFIMocLpqRfvl2L18ZUyXoS37iYLPmjH8n2CxiM6v76EtlF2o\n7QPrX9xypzC3q9obSpICBpB0osvlGmAePPp0dyf1rkI7/loXLUYQgP0NBAW/\nG2gyvVaFbfOYX3SByOja0EeU3Vq4m9pgUxg7U5N3l5T6iAaR+IRjFk4BigoJ\nmIqNTkEkniZMBW4afTy2YjVjFYVfYv2kTiB41/UxI4rK8m+j7HumvIKb9OeG\nER0wjFPQObC4yTc6+9K1nUFfnQRIpXVb4Dk+PajN+MzBQqG31gA1e3JLDTRt\n5lWDvuPS00YGxxojwcYisqv6izmARJd9M618xIG4HEkM79ROwIU9CoQweEr5\nJfEUZvctSS4W+N2DcQQqCz3FWDmbEbGW3+jm0LA9Em/+CUs9xflSaMDQGWk1\nuJInIsFjLNl2oGKzU2Af3iWZ4jQa4CbV84Ihc3L65rccEAr4Oqg2ff5Eu4u1\n3/vRnz5VQeWbrdvEg8II9jpIu36j8nF8smi1BOltEqvQcd3m+tnzpUJ5pKwH\nogkuT+M4Q8rPPOuBRfXFsI0JgkPpRBDjTKU0I93C7QbzHU/iVGyHgsO5euzN\nerzPP3wQQc4Drl4hxQg2/wKfUKjReC/WWjDCSkusE2C9KknlKblGu22sGaW0\nJs9/\r\n=RKe2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.16.7", "@babel/helper-wrap-function": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.16.7_1640910182126_0.4742716006114227", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/helper-remap-async-to-generator", "version": "7.16.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "29ffaade68a367e2ed09c90901986918d25e57e3", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.8.tgz", "fileCount": 4, "integrity": "sha512-fm0gH7Flb8H51LqJHy3HJ3wnE1+qtYR2A99K06ahwrawLdOFsCEWjZOrYricXJHoPSudNKxrMBUPEIPxiIIvBw==", "signatures": [{"sig": "MEUCIQDQXJJ5OTR3FvP8AkINxiGOUfl7sj3XF8ETHEWI65E00AIgZeAMe9yqMEezkbWFz7fltG6zj+OymckQ2g6LWmUZXqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKxCRA9TVsSAnZWagAAc/EP/jmFhLFJCEAAukdse8JO\nJUGk9TLzuexg8z+MLnPmDj8ig9S5YCrqwJkcQqSXcinGTXvpOpxLX/WnjqpP\nWd6cCT3bHV8RQLZ5oTwjgOwFSY1gblPUS5T+XJnU3CrS2ZZR+ArDMPQKQZ3r\ng9cJjYQ4sdpRswvycHn3yA5/VKh3YQ0yDx7pRxP3Pepm7zBlS79cGSH8jE+Q\nagSKYPL7uhdDKdvcCPy1QTRe01Bp3EEw/Azaap1BMY9OIfhn/8quj9SonOmY\nrYaZAt70RH2O46UTkW2T+kpM310aqjjgZfQmfWCtRpZvYI1vXSxeblth1OT/\nUqfcMI3Of57vIfhCzxbccSek5YZ7ERn25WzT5DLBKHyHwkl+VQDk5FOkjC51\nFx6dd4FLOni4ukSO546V18Zs8+7moLMQzx/uT5OPjOHaSo3HS/PCOjm/B0Vz\ntdswjD3dlmi54BZtvX/aTrX9qvACFUobvttOlLhdqM21W0G+b35yI38yf2Fb\n86tKusFKZVMkww7wtOKRvfVWwO9e9oXOcdORLXXuYnqSszDOs0Qlax90e3K4\nmzxVlyidQZ0DZUXbXzefuV4UtDRwqobKVBXGagfXgc/CfuK2/Ks33uiDdVjR\n5fxycx5B3MokCjAAm+HzepI7NFQyD/y9v1x1/pm7rOGKBnfdrAl3A2Md24Uq\nAK3p\r\n=PIVe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.16.8", "@babel/helper-wrap-function": "^7.16.8", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.16.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.16.8_1641849521788_0.4999143082302513", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-remap-async-to-generator", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "fa1f81acd19daee9d73de297c0308783cd3cfc23", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-z5wbmV55TveUPZlCLZvxWHtrjuJd+8inFhk7DG0WW87/oJuGDcjDiu7HIvGcpf5464L6xKCg3vNkmlVVz9hwyQ==", "signatures": [{"sig": "MEUCIQCFOU2ckJmlcI6sGUsaesMLPJEZ8yoihJqKJMskNeHS0gIgVFaOFM6MT9+Zn2KSBasIvnAuTrIumH9MKl4FDyfIE80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiNQ//Rhh5gKw0fBWcdZCJBhQUrQBE/nXhr0G0pfGlABLVNmixUx2u\r\nI2TAkRRsmKnK6Cktw3UDlJqi5gQf8qBLQk51Z4uksr7+qlrOUZCghr5yoxIJ\r\nnGeF4ANU6Apbs7TB1xaDt7Q8oJOEIH7yrjYyXEIHoUTudHUqXfc/C44kfkv7\r\nDPBn0H2hetFEt8NJLHasmCo7qujouIK2C7wiPGTCc4na9ytFwdQSMEZdcD9+\r\n8zqgxNm+XbLqdb8b/5fWVT7yXFJVaLI9UrhZXfjvNZuiJNKclCBdUQwaUb59\r\nECRDDRGqZQI6BaNb1qcd2C4SZULhTZPZ/W73UIjUPgHoLDigEAJnZQpyHT/3\r\nixN3Mx8JxvnDPJpzvuFdXejcFfno81rK81hzPt8DaRSHegdQSGYS4q8D5KvI\r\n+1bvSVnPx7EeBvxObjgHUPYH5WSHCGHWflTqD4irxJep7uJ22i3jFUp/IheY\r\n8QF0lWvL4NLHRXrNb5apq30kS8wW3i/hQZGmh1qQVQKyuMcw3zq9IyfDHglp\r\n0A1ytXXbK3oUP2icVuMn4vV4BWazhhtNOvVIXjnToFemSidtzS7ZCUnNOjt7\r\nOmBmUXYjSj00Kf+hosz68Ds1DakgNpMfZbeMI2wzNDNGNZwu6PMjKO2FFOUk\r\nmneSympLxGL1M5YxoieQvw1lXbLMXR0zK6g=\r\n=lc/t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.18.6", "@babel/helper-wrap-function": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.18.6_1656359437329_0.5194664465435039", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-remap-async-to-generator", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "997458a0e3357080e54e1d79ec347f8a8cd28519", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==", "signatures": [{"sig": "MEUCIQDclBwXUQoSJPZ+wuEIaQrueV/ItorAEJOFZslfi1lP4AIgJW7+vhR8t/qc4WyaI/tbh9cHxic3QV/EqSe7cu7r4Zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphDg//TzbXYJ2lSvjGF1L49OMvL71PuehcXoGdvBrrvfDXA0pdlOw3\r\nzcXxh22ehPjZCl83rOPBH3/zk/3U1Ws5tnU3cqNRvWOhlxukpn/XvbCvlM5l\r\nAEdlSQVNvqy5T+4HmQSRFmNaVC3+ui4HiKBY5ioIaTLGnfEJN1i6NRUNtydU\r\nyhXZovh2HkNPc8R7GkvM21JbbaF9rPMCaEut/GcHicg8KFd2eP0xBpQ/FNQQ\r\n64UPSaaK/qSdx3ThHOZ1U9UbiAzWkMGT0Yu09t4/I5NN52Noc1CKYgEG8J5L\r\nh+iCgoBRQ7mPKv4BAoLOmbP7UYAW7sCb6lydbw3edO+hmPHgAIvGli/AKcIC\r\nFJwJPu49CF26f8pM0BBcTG8Pxtng9WOYvxQ90r7eGp9SDNF270WOaOsOADne\r\nPntm8H/rsOrmkwVMz4baRqdDWLNJdTbS2MEwVv5Reicp7D3jMH3MDkjtRrFd\r\nPRK+KDyYPYMeZmBPHPRP+llLDfb/fXjdZRqA+7N4zECOCHHJK29T91yDSj96\r\n1t7hH6fe9wAdk3zPSs/QdBXxW1u5eCh+LNwxWuzjvyhGntcmAiQiQjD8Sk7h\r\na8PFaw4a2rJm77RvQ9sZaEefiPPGEOX0uVVzl+SF1j3I+HqK/9QbpuHSSQUi\r\n1amEIjldjj1tMYpGiY/NtpEKTQrzna6uuUA=\r\n=ThZC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.18.9", "@babel/helper-wrap-function": "^7.18.9", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/traverse": "^7.18.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.18.9_1658135861925_0.8739223572966925", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-remap-async-to-generator", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "fb083b46de02382c75501f1c7b5fd0406c7a42d4", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-A7wZJzYyE8zScWctEdYcEX2EqGl84QJDUSZYwXLPKZZvOA49NZSKk+7/mVhZfszzILLMHu5SZUZz7VBhGAzT6g==", "signatures": [{"sig": "MEYCIQDnphaQ1hqQ4M2D0qTCmbH11FXOArI09E4JF/cZMWHK5gIhAJH0z8w2wthyTRppFIki7q3vgnoGPlNKKKCXwgaad8/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqv5g/+O2CiVDFd2EF156gDTE7V4UTpzKz0+RsW5x4DM0LaYEk5L3qq\r\nSME2aeI0m3ExyLt2jToOrGWs3VgLjfyUSs1lywacQ/ql3zIPN1N3VyYys9+x\r\nznpqIlXHvZEmkuK43lHMmcQ5KYsyu65pXyXi+eSS+OfJJMuNXx7mZzHPZ6H8\r\n1ORjqYlT/MRdJeZ7o+6d0hQQfFB/Lb7BJ0xow5ppsVu+/yvZy6PUtGCFksi+\r\njRaHl0crsW2vwU4H3ssbv51kVplukXNN5DbExV5e8v+1DnVHBeXNi1iQRUz3\r\nmmagpfOCWMeyk6f6gtTDdmMzhtX+Jfacz93mTuZORuXnCCYUcVzAKSx5Mq9h\r\n9ZcNN4VMG+bSuPucjMQIk2lZgS5ogm80M8Qmb/D1/9yrBts4P7fcCtCpgey7\r\nd/ytRIC3O80RqjFjjn+e7SgkTk6dhOqS3lb6DzB6YBsn0q0iCCCgcQWdCNsH\r\nD9JVkA/Mw8UyDBTvUnIrxfMvQI2/AeIDOL5opjjPErKAO8rHOrsjbfnRB4jz\r\nlp/1ghkEbKWkwUyyvPGPmM5OsQQLQyYuQAuEf2M3W7EItmiox6QcSTadXeZZ\r\nKIJToiu5E/J/PI+dXh+9yRyl0GKVCF2MORKYnnhWEjjRFyM3c+L9G/am9lQE\r\n6cgFQy/VXqtnvtpuIlei+PIVx7MvTGtcYEw=\r\n=nTn8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm", "@babel/helper-wrap-function": "^7.21.4-esm", "@babel/helper-annotate-as-pure": "^7.21.4-esm", "@babel/helper-environment-visitor": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.21.4-esm_1680617395798_0.33300472841678275", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-remap-async-to-generator", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "1656e7790ee03b41db3c8531968c7ff28baa19d3", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-2BwMCY03i1lcaQIS/oxJP9G4ur7mY2gieUp2F8cU3Tzg+Id8LrGO/Kod4I5zRml5lDKjxyNczYx+S0LaCDrv2g==", "signatures": [{"sig": "MEYCIQD4aumDpmLshBrrWqcKIXD/pJw8rCDVguH+WOAC/Erp0gIhALJD3jfKhkioKBaz7QZW+KnwrjQhhp7qb/mr++JD1VIW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqIQ//R7dh/UACZmUhV4t6LI1kI0+w1QiQGcRhOU2Ana93+5NB9IGO\r\nTzTpnjjLSb5lOxF9zBVTqjmwNkBCaVQKmJAh4+QpBj+XALo77nF9n4lv8YoL\r\nUORCDp3ARowthwlwFhOIR2PBRSeNce46Cpsn3a2D/WfoQDiVrkhZ+aC3Dl3+\r\n1cw/8FyGMxqEczB1XsBJoArd0Abn+4D6dt+x1B980/d5VU+vjDSAwMlTdJtC\r\n0YSumj42F3YDFrykK+A3orryoQTdj92qIE8tuJK8/J99aEpDEHbtHxj5QZ8e\r\nFY2JUAo/SKlyr9/6XyShqBfw+0JLn5WU8U/AK2qrzXIpsajL6u3sfWfM13GS\r\nSjvN6gvgIY4OUmBrSf6ZGUkk6NzCMvcuPYw6OFmdENyGkgSVnPGgobDL6YLD\r\nuNsBLmbVQRZDQE0/5txF0lBYdVJXTjznBBe5Ui/yf4UX0IAVGLvKr9Zr5+sk\r\nj3OhJ8FQb4wS/w1bgay1CQjGjaCME7GJw+4jCi/IV8klfgSqtfWYqYS5y4xZ\r\n/p9kFbTT3uOPKRLTklQRO+4FYM+cANlSNt2tKVTRKiLQkmM/yKQqZ7Eqf5A2\r\n9OOCTSaFmkVtwmw/neoW904PXYcJ1NTE957MJBag/LihC84zOeNeYuhDIO50\r\nF3oTo7nM//931MuY5FDdvhRiRU2HWZvrJyI=\r\n=vpfp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1", "@babel/helper-wrap-function": "^7.21.4-esm.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1", "@babel/helper-environment-visitor": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.21.4-esm.1_1680618113554_0.31964425763507154", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-remap-async-to-generator", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "c099a3395e19c9c062e246bd7c0375117a730d34", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-1Js9+E<PERSON>rmtobcqXsPnNfIE6YiP/I+mgW1ADq7RHjiiUQqScm+u0UOs1N43Kdh4ulqcSpKvnvc3s5fuL5Gg12nw==", "signatures": [{"sig": "MEUCIB1J7BEglSEF9vH+tEILUUcpKmq4K32kjQ1ZdC/lc/sIAiEA4yudRRv0MwHxIhoCxNuha4c+4pGylJbXFhH4hJHRivs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHHQ//ZBuI6g+obFzHeuqhDnN/b5Sk1MyKE44AtLrX1Me69BwMmsp5\r\ni0H8V45kKXU/Zz2HgSQsHHbgd4QDvjJ2QwObJVMqTkDiqah/pwX3WYuDWruy\r\nYX94YaEkDCP/yB1mOYr4qWbMHm43xEigRk27SU635lq/VOYPT6Hwx0Qftgq/\r\nC6DV02omVfc7VMzxlQWXdara8rf8Otv9xm7SQGA2sQUL8A9GdmmheafElIs+\r\njGwTntUnNYAlLlJWA3ex7DaKg6bQDelX3k82KslVGXEnvxlMkbdwjmy9WceV\r\n4SPtqxnwXsf8WTPi3fVrHDMP0dBaOz7W8x3THshoxZlQVOhZtUSDbiytb6zX\r\n2e8Jv9RqRd3sapmctwt68qrS39UR/qUBda1M1ZCB7D7vm8naVT/u/QKYxx5j\r\nPoAu0zafMqZrcbwUJGw747SK9TiATUVUGlg6PbMavUN3s1tv9cajBh2MCqKw\r\nPy0wVQzkgs9pKMcJf+Q0CaDI+e1jQ7yO0v/JGsmkMh2ixIe9CWG6qfmfN4gH\r\nDiPnQ7ytIUFDIzfHIfiFgXtBC+SALD1VrU4JMRnr6TEcRw9oa3f1/8y/pKal\r\nDOzG3rGcGB4BCTkBlsxSb4Sq1o9qQcm7iHq4DtqfcStcw1u317MPwo4AnQog\r\nLwNPNrn44XdkDEnlsANeDTzlmMblWFBRHMo=\r\n=edTe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2", "@babel/helper-wrap-function": "7.21.4-esm.2", "@babel/helper-annotate-as-pure": "7.21.4-esm.2", "@babel/helper-environment-visitor": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.21.4-esm.2_1680619200193_0.48263040963019344", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-remap-async-to-generator", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "ec9221d29a3b23338604c8f861b3d8b576ea11ef", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-TW6WOZlGs229a0QkOYOoZeifTHj48Dq07blRyylDMHl+HoFazwHJ/Tp35WIZJ+Rh1TSLikUPfzmFkgTOHXefFw==", "signatures": [{"sig": "MEQCIH8bvit9oSHGVop4VpHczZ7vQt+0P9eBWQpzGm66p+/kAiA5Z8WUwdHcQ+4xoKvUUeFpY7ohubnNGctUt3gTl9l09g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4Bw/+M4VBClpYgB4QUfe52K4Ofrr/x5N3TeICf+fHgPAmWVZWSCak\r\nPIniXlMY8VKjWFMq9HcUlTpK+Fs0teWqsa3JXcnafqf7zvvXtBhkGGuTRZEh\r\ni5v7UWQRaIao43ZjrTqn/yG0PF03yo02xD/dlqIebVZ8iVgffieetRTKXYC7\r\nDLPMC1UGp8uwhM9GzAMWf+ZQ/v9K3PEcUsuJCejj/l4Q4H9PmxMhDGzE62Zq\r\ntGEtdq+FcICyUszxULTM491GKfwzBeYupCiiwfVmsTbXHg4PUOsIgsrOWxo8\r\nWc5mbaby9ouF4U0HRJvkhlmp+ghF417YRlrxuaTkjy4eNcrlSgx64K/ti+6f\r\nkofEARJlpT8sWgDfs+PGOWEwwJjrZRb4T5P1fPHqfAK67yosErvA8gSNSn3q\r\nimBWFZyV1ylMgbhcZ/cPxkO+c6z82nxJeh8WFW6SU5WQ8fMytRlhj4oc+4aG\r\nFSBkwJtHeaoVMs838/EpQNCJdDHMx8Imc0eRL9BMyldlraJC+F5w+mmGat1d\r\n4pm8lI3EEJudVuhHPrfbRic7y7qOBlccI1FiugIRnR36xeNuzZxUhgpB+TvV\r\n2MliN0kdYSlbaNLvr8QBv5HVRVgU0O0Ob0gt1cLqibr34jgb6dCkuDvpLHEd\r\nGKTByoTNYJvE5i2FUMNMo/MdnWgwGzj/wCY=\r\n=l8CX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3", "@babel/helper-wrap-function": "7.21.4-esm.3", "@babel/helper-annotate-as-pure": "7.21.4-esm.3", "@babel/helper-environment-visitor": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.21.4-esm.3_1680620200364_0.8897793742762699", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-remap-async-to-generator", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "c9a0efae77a1b386cb89cbc16bf0a85bb79ef2fc", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-ijKAKoZqreu94kpAFzLVCH3VrXaKfxZd6+uM0dGs86hEj3Q5RuaY/LUhfVDsbyNoLRRSYMscTJWiA4AzY2Y6wQ==", "signatures": [{"sig": "MEYCIQD1xDOJ/KGJbn8NGD7gqT1fb21gMKhZMGf8NmSKCZ1LLgIhAO1vEjQ+7ZjFfhEtxMcRXyLzSBXENMlHx8H9NNys61P2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrs7A//TCR93eg1GyYb2TmJAHHLEwKeaM+lV8O2y2mR9grJMS5rSmVd\r\nWhZzm7baSnJd4Qx8IGlHGC8wGArR5n/IEp8C3qnWqcyVf9WlpG5EWz0c/NR5\r\nCv192KMC4Qtc47HXOzi8Rz9M+qdoRqoO1Zx+yv72ixbhF5Bd2gJFGXzj1/wj\r\n3OxnTL0wEBQ/DoC+7No92ch61266hbWe5mC9nEVCOj2HSp3Pwbrplq47CmUz\r\ncCrEWrZz1dORhgOsnW8r74eKSNjupMLBC8SOqrGU3Y42IWSVgRmnFxT7Njvb\r\nnWfZrLYW0jwecG2F4dNDTLdqmcoGWfrdSnVdJ9B3wU572Z5UO5yC2RH9c09c\r\ny7VPSVxHfGH6DbnS2w1vHPpEjupOUw501VaMxMnBDqBcKZia3/OeVi5FuKtt\r\nYHka6gHGqcH/0Z4q6cmXjpo75hLGXGR/h03i5p8l1Sx5kXzTtGMZWbGdSTRw\r\nuxXqdAdfGzR6Ow+6LTThcVPtjgrx6uLFAl2evrEedkmIpnOnu86g4suIeEzp\r\nRfStpg0THZE6jRJbhUVXJACmpDEzmg6djB226MqQqAKREU/r90U4L8pbBPzD\r\naD6mlaARirNpJjtWOgJvOOucf/o7MmlkJ+yzu9FC8y21W1dpa7J4o5fNoQbw\r\n+Ir5NbRCsd6+yiuU4afNePVradZmdX5bFPM=\r\n=PWVj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4", "@babel/helper-wrap-function": "7.21.4-esm.4", "@babel/helper-annotate-as-pure": "7.21.4-esm.4", "@babel/helper-environment-visitor": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.21.4-esm.4_1680621228784_0.49905238485757786", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-remap-async-to-generator", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "14a38141a7bf2165ad38da61d61cf27b43015da2", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-cU0Sq1Rf4Z55fgz7haOakIyM7+x/uCFwXpLPaeRzfoUtAEAuUZjZvFPjL/rk5rW693dIgn2hng1W7xbT7lWT4g==", "signatures": [{"sig": "MEYCIQDU2255DQPRPxyMVjgRX4SMdx027CydI44j34jLAggDDQIhAOkhmSOWdyjp6IHVT+NKLvlp6qcdAFjgjRkbD98Q+VE7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10090}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/helper-wrap-function": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.22.5_1686248504004_0.055737790433719736", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-remap-async-to-generator", "version": "7.22.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "53a25b7484e722d7efb9c350c75c032d4628de82", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.9.tgz", "fileCount": 5, "integrity": "sha512-8WWC4oR4Px+tr+Fp0X3RHDVfINGpF3ad1HIbrc8A77epiR6eMMc6jsgozkzT2uDiOOdoS9cLIQ+XD2XvI2WSmQ==", "signatures": [{"sig": "MEUCIQDcnLFCLiKlDSbM7hWhuNkplT4gPKavNudUvsGa338HWQIgeF+sgPzH2DwzsOydNkcJl8m8CEoe/1snyvPNqVOQ4y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9960}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^7.22.9", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9", "@babel/traverse": "^7.22.8"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.22.9_1689180827560_0.7030843752828522", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "f97ff95f1e571b9f503d8f966c8c50c2c07a35e9", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-F57q00Jd5sOmc+zqMyqUAA8IQUuZLE4QnH6vB/M/VoQ2dSzT6z6Ry6qR8GXLtBQIib4h1qhysXwhXAxn6nD7ug==", "signatures": [{"sig": "MEQCIC+R5MhKPMh54ol2wEPy6pDh5ZrdojxN+NnrgzGR7quOAiB9HwbHqABF+8EVbGzfkAw/MhI0Xh7KcC0N4/vReIANEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0", "@babel/helper-environment-visitor": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.0_1689861630340_0.024932114229761604", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "2f0ea8e47ce7f7ac8a022dfc3c8d5e37433b4f27", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-49kD3DtCnZ925/g3mtUSN0/NMggvCFixycU2TCFj0eBr8LxsPyfSwzsS4sUBcN/3CekWn0Kf7bKi8+E8h/+9+w==", "signatures": [{"sig": "MEQCIB/h/3ZEYXyWuHiNlLKEZdOQLCw5aae7kvKyanHD7glfAiBsko7hZxRisNE/FoZCB2se/ll2cXt1Q0VWVCReuVQ4MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1", "@babel/helper-environment-visitor": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.1_1690221183525_0.7530297684085845", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "ebcfd980bf3c19857a4427851baf0b53607cb7d0", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-YF8LDf6DkQw683eh0DsFPcwHEape7oKyI1fQbk5+4kwlRN31xlQot3sCYUHXrHVvS8M8YPZ6XSK+MsWGb6AbFw==", "signatures": [{"sig": "MEUCIQC7LPk2yrrUGcr6z1/KPhjyClefDuEWPDwl2B+ZdYPvogIgIiGzlWnJJ5cIzFRaBFPEQBYT+9fiExmq/H16Ofb4yIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2", "@babel/helper-environment-visitor": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.2_1691594124847_0.6625564043803496", "host": "s3://npm-registry-packages"}}, "7.22.17": {"name": "@babel/helper-remap-async-to-generator", "version": "7.22.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.22.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "dabaa50622b3b4670bd6546fc8db23eb12d89da0", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.17.tgz", "fileCount": 5, "integrity": "sha512-bxH77R5gjH3Nkde6/LuncQoLaP16THYPscurp1S8z7S9ZgezCyV3G8Hc+TZiCmY8pz4fp8CvKSgtJMW0FkLAxA==", "signatures": [{"sig": "MEQCIBeNv+uQrLfs2P07c++H48+2RUeV4TUtjvtNwl4mTCbfAiBVs0MflDQ+yEk2eq39V2aZitxPGeBIIUcFVvM8DRCoaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9942}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^7.22.17", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.17", "@babel/traverse": "^7.22.17"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.22.17_1694181213462_0.8957877370166529", "host": "s3://npm-registry-packages"}}, "7.22.20": {"name": "@babel/helper-remap-async-to-generator", "version": "7.22.20", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.22.20", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "7b68e1cb4fa964d2996fd063723fb48eca8498e0", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.20.tgz", "fileCount": 5, "integrity": "sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==", "signatures": [{"sig": "MEUCIQDsZIRjt+KMWfETqzSJFxoVQO+QKfStDczq/tR1IewA7AIgBjcBZhr7+7AS/96r22iQOV4W9qUTo4oFFiaX2dTe+Us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9965}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.20", "@babel/traverse": "^7.22.20"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.22.20_1694881722430_0.5895051406847023", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "ee1b2ad1fb246c41c16c70e01bef6d4033c1d55b", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-hWT6M2xZvPMnK2Nz3GT8BYRudooKjq7Ul6htuzuQJahqNddcK04gqwK0xAV8AVsXUZmCEqd+6dhAEjfc84y4Aw==", "signatures": [{"sig": "MEYCIQDjqlfbmHn1fyWV4H1pDNEVJBwG/726phNrUfmhXv3bxgIhANt7FPjcAy+k3YBkt1JFIWrRTaJ3RaZ7O+AsLFSEigXu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3", "@babel/helper-environment-visitor": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.3_1695740258160_0.1291459927794596", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "e592c9bce288c65e6680cbafecb9d58cdbd72482", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-H8BlsbdEotBlfH3KigM4q/TXZ374eXI/SkS0ckoezNrTVeSlDu53ZGkbKhyKsO9gCPDDbVp8zJ563Mz3oMGMTg==", "signatures": [{"sig": "MEUCIHhURWpWa+CfP5agmGLl28dQx++2HGPS92IuRLGFZ4u1AiEAikHCVzaXJK9cJ3xsM8lQxQv23da/3TNpWjRJ826/bUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4", "@babel/helper-environment-visitor": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.4_1697076411768_0.5794853066133849", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "48c8931f205de79b84ba99152ef28d5e6281d684", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-2GdtCi2Rhh+EdnsexEtT1mxxRsCLwVgvqXlaA5jIeFlZgIQg270Sq5C+Vc3qKzR8KjaYv9PbQHNKhweqSLmtFg==", "signatures": [{"sig": "MEYCIQC7NXwB+LkAY9FtFARDyXyege9bG/tChifYAyvBkN4q+QIhANJh/ASqXgLhc7YSx8HMUKv9ewHn0+e6AQRLEqWftnxj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5", "@babel/helper-environment-visitor": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.5_1702307986087_0.11693332895675068", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "455a3f43942ef6c366fca8d0c7f8fa5757331b4c", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-NSzrY897qr1zJyFv+2znSi740+njaY4P8oeNNYRjApDEetMfIWcSu7qI/8qcENF7ZW01LW9DlOlUNwBUVR8GzA==", "signatures": [{"sig": "MEUCIBxs2BbX0GuJSKwB1hEE6w87UGp+2iyucqEV4vB8MZshAiEA2dgE60oBDtm7fSd/8hoo/BO0TM6JDlmunVuI4l4UZIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6", "@babel/helper-environment-visitor": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.6_1706285689470_0.6150664745616734", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "e604b226935dd003b3fcd7adc43df54491419083", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-Xo8EQ3cgPMMG3DKlfN8vdqiLPFpd/R6SZKPT+DxUFPct6BgZY7Ldjr6M77X4HNnL3wL3C3oBixCH/3VgnhZC2A==", "signatures": [{"sig": "MEQCIFfvn2ZOE8j7SOm+MWAwnx5xwo0W5H62EIbxXO6lxM1ZAiA9eua/GDQ4VXtzUiI+IJhWmWUEVDtZWCRaHjp+1qknag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7", "@babel/helper-environment-visitor": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.7_1709129147160_0.8518007434046639", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "2e861597888d84a17ae9123c7e95001c658a1adb", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-wNSUbu/dY31t761v50pAe9wVYO+SELq3Pid7wpNpJT0mjohnldCaLGzkChf6mZCcbOl0EouzynKSOwJjskPMWA==", "signatures": [{"sig": "MEQCIBT1/7pZFiiqzczKGQrKnh6Eu8xSbEB36a0cOuSNYxSFAiBZvJqMp+vbrhE4qQDOd3gWyMf+UEEsnj8fzGVGrAduvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.8_1712236821633_0.5491218827105446", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-remap-async-to-generator", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "c96ceb9846e877d806ce82a1521230ea7e0fc354", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-1Qursq9ArRZPAMOZf/nuzVW8HgJLkTB9y9LfP4lW2MVp4e9WkLJDovfKBxoDcCk6VuzIxyqWHyBoaCtSRP10yg==", "signatures": [{"sig": "MEQCICoK1PxSpaiQ6721v4laNzbrCcC0nP5TYifB+zCor5o1AiB4UcAYB6HVykHhIHOvXxci4gC5ocoGn+PwTT36bm7AVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76387}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.24.6_1716553511972_0.991942214388051", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "77cc481bab0c7a7bd86103bfe23d2c043f4123ec", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-A6mdnEpz+BTvoWTlHnYhIrlS3SMeQdb3QOa1JSYKmMFYbORcTY7wZVguf2sAaTl1tCti3oCMdR854gH6axL2iQ==", "signatures": [{"sig": "MEUCIQDmLK+kLt1s61vS+1Cw2Ed76Z2+7gGNmL6FSZNvoTEcZAIgAfzFLerD2+iOYCGOWdZSNcg1GmV3dxwBgXhsYlWLS8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76702}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.9_1717423520128_0.11988039436068965", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "ca4cf1dc01f504b1786903fb68348c43ac95c220", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-nA/D/D4n07uCTBZNVpwt0Qz9ERFJ3GqXaP2WnnLmUdItMzsQwQpO8wMDpeqhI3Z5Mwrwt6pM+dFrqzm3A/Mp9A==", "signatures": [{"sig": "MEYCIQD3H87jXsMjkdhSsTu8Qlq2cR0w+lyNbcp9t5D/qk/mKwIhAJJ/Ta0kBAWpoIK5MNWiBS+k+0KSrbItqXDGNMY20P0K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76709}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.10_1717500034313_0.5619415584652416", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-remap-async-to-generator", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "b3f0f203628522713849d49403f1a414468be4c7", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-9pKLcTlZ92hNZMQfGCHImUpDOlAgkkpqalWEeftW5FBya75k8Li2ilerxkM/uBEj01iBZXcCIB/bwvDYgWyibA==", "signatures": [{"sig": "MEUCIGEpx/pT8ndPg0tM8BP4bCOIluLNuV5ar4QsJcuCxS1LAiEAlt9LnG0pXiGSXa9k4m1hVi/CzyLoQDz2ogCHKN2dixY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76371}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.24.7_1717593348583_0.48873635306690444", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "ab8a6dbb2514920d60cdd330ea8844f90a73667a", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-Iq+8Z3rXqzbcjew2qiX9sqjCPd/cUmSSqFOZSWWK5KDDuH1HKiH2g5KRsz7whgNIrZjd6iBPpeoyhJmVamvBLA==", "signatures": [{"sig": "MEQCIDmV+rUSv/vsGeSAPFHsWROz7L+W2Qe8Ya2Oy/y98cdvAiAicM5AUduCqUF+d9zqIfUdHMQkIuDB7+gZIWcpu83yNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76598}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/helper-wrap-function": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.11_1717751759038_0.660317408892277", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/helper-remap-async-to-generator", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "d2f0fbba059a42d68e5e378feaf181ef6055365e", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-NhavI2eWEIz/H9dbrG0TuOicDhNexze43i5z7lEqwYm0WEZVTwnPpA0EafUTP7+6/W79HWIP2cTe3Z5NiSTVpw==", "signatures": [{"sig": "MEQCICOpJRIt/jBCFEMBX1eCJusko3zkG0e5Xc82ZB06YAqKAiAZ+SYC0Dwr68LQWxfYCGHVjBsho7kGCe7+bxERt5fF2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72608}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-wrap-function": "^7.25.0", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.25.0_1722013172127_0.17162778532906797", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "3ee2ce47195bc23e2640df069e857ee8c4fd15b7", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-oUVJECSJi1XULGCyxlthIoNUbTc8A3ChQ1rmg/W74uMivM/uwmemG7DYVFWluTuVCD5l2fNZmgdOnYSuMu3zUw==", "signatures": [{"sig": "MEQCIGv2I67b3MK8XJm28VKNyFp1pJnUZcGcna0VQ//YukhbAiB+jkDFrKE0G9oBbLamJ8rwbGSuAvngOdmCLtIfb+/TaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72964}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-wrap-function": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.12_1722015233172_0.5858329373264841", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-remap-async-to-generator", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "9efdc39df5f489bcd15533c912b6c723a0a65021", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-kRGE89hLnPfcz6fTrlNU+uhgcwv0mBE4Gv3P9Ke9kLVJYpi4AMVVEElXvB5CabrPZW4nCM8P8UyyjrzCM0O2sw==", "signatures": [{"sig": "MEUCIQCGz2EOO1dsu35wn+9SzTObcZadpKbr+FgcpXbGwLFtygIgPlEP3xi7UNrqVD6wyMCxQ5th3NOcebbWS+2IB4Tdzbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80459}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-wrap-function": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.25.7_1727882121600_0.9128968608607708", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-remap-async-to-generator", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "e53956ab3d5b9fb88be04b3e2f31b523afd34b92", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-IZtukuUeBbhgOcaW2s06OXTzVNJR0ybm4W5xC1opWFFJMZbwRj5LCk+ByYH7WdZPZTt8KnFwA8pvjN2yqcPlgw==", "signatures": [{"sig": "MEYCIQCu4uGo1lljEMF1n/JZNUxPgsOrHqK884fTaASSPD94JAIhAJvWd/qhevC7M57WHwpzjWLaW7bV/2e0ATTL1z22WbKx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9711}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-wrap-function": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.25.9_1729610497496_0.21555666757534375", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "e6bedd7c978e3381707057254e35a6c038709170", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-nAtFYc0e6rN5hVBM2Ud93GEuXcrh+eEF3ddUr0XoKx1iCyfDxlud1i8jDdgKHk3STw5qgg29jNunhC+u9xCJKQ==", "signatures": [{"sig": "MEUCIBEVeNZYHKpQOaPzJEUmvP8+eXVJl6Du6KdBk8l906kPAiEA/70J4eqd8GZxBtT4DAsF4pRMDoOptKU+iVrkLZj9OEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10067}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-wrap-function": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.13_1729864477901_0.7701131687601948", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "a4cb680cc6da5a9d7a5bb68dbe79d71e23a0de03", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-qMmElos4RtyKSiGQ2VGtITP+pVCsKZp6NqVes5baxZPZaxxgiUujx0Os58IfgPAdg+usatr3Nv/GplLjam8jCA==", "signatures": [{"sig": "MEQCIBKOgIfQFDTpATV0pHXchcycTTR/OR6CQtP0zfQpghMCAiBaeXeHrWRW8in6bb+J+MrvLlpptl9SjCWxmlBxC1OC1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10080}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-wrap-function": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.14_1733504067444_0.5196001208864844", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "8aad1e18c65b364ac8a254732e29cf31c303710b", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-tarUl101n7C0bPHEJxDSfNJpNjJaEr5nx8iTwkvLkrhiraRX9zQhUaKIUC3rYeR1s5+mWcVDnqM5hhYcOiUmhA==", "signatures": [{"sig": "MEQCIF/E/Zp2VabUvwXYs4GcFjaPeSqOHB9lxEh7nXvdXbDjAiBIwSFyas7LAU7duNpKvJGw+/24PIWNwRY2k4XIpgQpRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10080}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-wrap-function": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.15_1736529895827_0.14109828830580784", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "ff96bbcef526044e7372449102ce448b5d22010e", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-5B2LHvIQxUURmsMxfVdhe4KyiIEkxGJ+LEHNz0dNxavY240KssY3RYXI2cMcJ03kXlsH8V4iYdt8RKXMOIoung==", "signatures": [{"sig": "MEYCIQCIxonSugkN0bWwIAvf3Khu/jUkvliwpGzwH0m3ro3rbgIhANCPlvPOMJ+HFYb5q2x3PDvMhteNbs5N6gTEvPIDeC8A", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10080}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-wrap-function": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.16_1739534371943_0.6312996897890748", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "f4ddde550eeb02e3d4593fd50ed0d943070a7958", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-l8dRVTTJNPYd8U0edCT0x+tKE0x33t5YIv1zAifeXZDv/WBMq8rfCFyNd324uZCR0Owt4EOaQK7wlmV7DZ6ECg==", "signatures": [{"sig": "MEUCIEHXUb67zC3pJs6RZ//j0luOr1wduY7+OACc2er2f/BtAiEA8eVXv/pt9CDzpmBEmwxqYJxDKyEPZX4pG9d//LwDrlY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10080}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-wrap-function": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-alpha.17_1741717525150_0.6048243399295803", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-remap-async-to-generator", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "4601d5c7ce2eb2aea58328d43725523fcd362ce6", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==", "signatures": [{"sig": "MEYCIQD+mGYmg/g9fFWazJHcb6Xsq0TDuGa4jV/7LhrP248X7AIhAIW6xY9/Uck9/mIB1PHm9S2PGgSYD/7yM2Ae5tKd9Jrw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9833}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_7.27.1_1746025760563_0.5210898697664905", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-remap-async-to-generator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "dist": {"shasum": "8f7c60581107ebd54ab39b9f42bb8ee43257d9c8", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-gF9oeyaqM+N8YAncNgwIM7QPyvRpJzbNvHRyqm70Ax6oN8xEfnZ5rg7yeBv/ddPKv5sRIp9Apwb72xTLvkRZIQ==", "signatures": [{"sig": "MEUCIQChEbmR2O8nME5jAN0qhRPOwSdoOB9g0AleIx4gGLg5wQIgP4y5q1tXuHb5KRo5zKchRTo28+KMbVrr86rZ0TZz00c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10186}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-beta.0", "@babel/helper-wrap-function": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-remap-async-to-generator_8.0.0-beta.0_1748620297123_0.2666078485485568", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-remap-async-to-generator", "version": "8.0.0-beta.1", "description": "Helper function to remap async functions to generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-remap-async-to-generator"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-wrap-function": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-remap-async-to-generator@8.0.0-beta.1", "dist": {"shasum": "6e05f5c8c612b5b0ee58f0bbfded232bad6e0b17", "integrity": "sha512-Le8kll5gN2vxHsm6Qh75KPGiHT1m2YH/ClXz/7sWw52JHm7mzZLIaX+zXCzKQ9vt0AaLKvoQdeQXetJSvJh8HA==", "tarball": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 10186, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDYQlztIgfzMBs2gVXmMaTiNbsmumTB1hmd+YwGjDdmIAiEAzO7PKOzm/0hSMnvy1EMdfSvodFJXGVg5Mk3BozCsxu4="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-remap-async-to-generator_8.0.0-beta.1_1751447080165_0.08745111946510065"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:27.757Z", "modified": "2025-07-02T09:04:40.949Z", "7.0.0-beta.4": "2017-10-30T18:36:27.757Z", "7.0.0-beta.5": "2017-10-30T20:58:04.291Z", "7.0.0-beta.31": "2017-11-03T20:04:36.043Z", "7.0.0-beta.32": "2017-11-12T13:34:06.283Z", "7.0.0-beta.33": "2017-12-01T14:29:24.648Z", "7.0.0-beta.34": "2017-12-02T14:40:21.506Z", "7.0.0-beta.35": "2017-12-14T21:48:31.118Z", "7.0.0-beta.36": "2017-12-25T19:05:46.696Z", "7.0.0-beta.37": "2018-01-08T16:03:46.756Z", "7.0.0-beta.38": "2018-01-17T16:32:45.251Z", "7.0.0-beta.39": "2018-01-30T20:28:47.877Z", "7.0.0-beta.40": "2018-02-12T16:42:51.770Z", "7.0.0-beta.41": "2018-03-14T16:27:02.629Z", "7.0.0-beta.42": "2018-03-15T20:52:12.948Z", "7.0.0-beta.43": "2018-04-02T16:49:01.475Z", "7.0.0-beta.44": "2018-04-02T22:20:41.700Z", "7.0.0-beta.45": "2018-04-23T01:58:45.573Z", "7.0.0-beta.46": "2018-04-23T04:33:02.168Z", "7.0.0-beta.47": "2018-05-15T00:18:02.629Z", "7.0.0-beta.48": "2018-05-24T19:24:58.824Z", "7.0.0-beta.49": "2018-05-25T16:04:49.272Z", "7.0.0-beta.50": "2018-06-12T19:48:15.844Z", "7.0.0-beta.51": "2018-06-12T21:20:57.642Z", "7.0.0-beta.52": "2018-07-06T00:59:49.686Z", "7.0.0-beta.53": "2018-07-11T13:40:52.549Z", "7.0.0-beta.54": "2018-07-16T18:00:30.341Z", "7.0.0-beta.55": "2018-07-28T22:08:27.434Z", "7.0.0-beta.56": "2018-08-04T01:09:00.170Z", "7.0.0-rc.0": "2018-08-09T16:00:15.020Z", "7.0.0-rc.1": "2018-08-09T20:10:04.040Z", "7.0.0-rc.2": "2018-08-21T19:26:10.564Z", "7.0.0-rc.3": "2018-08-24T18:09:54.383Z", "7.0.0-rc.4": "2018-08-27T16:46:38.166Z", "7.0.0": "2018-08-27T21:45:05.504Z", "7.1.0": "2018-09-17T19:31:17.234Z", "7.7.0": "2019-11-05T10:54:01.124Z", "7.7.4": "2019-11-22T23:33:57.553Z", "7.8.0": "2020-01-12T00:17:35.039Z", "7.8.3": "2020-01-13T21:42:31.063Z", "7.10.1": "2020-05-27T22:08:21.501Z", "7.10.3": "2020-06-19T20:54:49.893Z", "7.10.4": "2020-06-30T13:13:24.150Z", "7.11.4": "2020-08-20T18:59:46.761Z", "7.12.1": "2020-10-15T22:40:57.902Z", "7.12.13": "2021-02-03T01:11:57.764Z", "7.13.0": "2021-02-22T22:50:18.918Z", "7.14.5": "2021-06-09T23:13:09.788Z", "7.15.4": "2021-09-02T21:39:51.989Z", "7.16.0": "2021-10-29T23:47:55.468Z", "7.16.4": "2021-11-16T22:46:15.990Z", "7.16.5": "2021-12-13T22:28:50.014Z", "7.16.7": "2021-12-31T00:23:02.274Z", "7.16.8": "2022-01-10T21:18:41.874Z", "7.18.6": "2022-06-27T19:50:37.513Z", "7.18.9": "2022-07-18T09:17:42.067Z", "7.21.4-esm": "2023-04-04T14:09:55.954Z", "7.21.4-esm.1": "2023-04-04T14:21:53.788Z", "7.21.4-esm.2": "2023-04-04T14:40:00.343Z", "7.21.4-esm.3": "2023-04-04T14:56:40.581Z", "7.21.4-esm.4": "2023-04-04T15:13:48.892Z", "7.22.5": "2023-06-08T18:21:44.304Z", "7.22.9": "2023-07-12T16:53:47.714Z", "8.0.0-alpha.0": "2023-07-20T14:00:30.516Z", "8.0.0-alpha.1": "2023-07-24T17:53:03.694Z", "8.0.0-alpha.2": "2023-08-09T15:15:25.052Z", "7.22.17": "2023-09-08T13:53:33.596Z", "7.22.20": "2023-09-16T16:28:42.651Z", "8.0.0-alpha.3": "2023-09-26T14:57:38.309Z", "8.0.0-alpha.4": "2023-10-12T02:06:51.944Z", "8.0.0-alpha.5": "2023-12-11T15:19:46.282Z", "8.0.0-alpha.6": "2024-01-26T16:14:49.657Z", "8.0.0-alpha.7": "2024-02-28T14:05:47.303Z", "8.0.0-alpha.8": "2024-04-04T13:20:21.846Z", "7.24.6": "2024-05-24T12:25:12.192Z", "8.0.0-alpha.9": "2024-06-03T14:05:20.315Z", "8.0.0-alpha.10": "2024-06-04T11:20:34.472Z", "7.24.7": "2024-06-05T13:15:48.782Z", "8.0.0-alpha.11": "2024-06-07T09:15:59.316Z", "7.25.0": "2024-07-26T16:59:32.562Z", "8.0.0-alpha.12": "2024-07-26T17:33:53.406Z", "7.25.7": "2024-10-02T15:15:21.804Z", "7.25.9": "2024-10-22T15:21:37.676Z", "8.0.0-alpha.13": "2024-10-25T13:54:38.075Z", "8.0.0-alpha.14": "2024-12-06T16:54:27.618Z", "8.0.0-alpha.15": "2025-01-10T17:24:56.073Z", "8.0.0-alpha.16": "2025-02-14T11:59:32.179Z", "8.0.0-alpha.17": "2025-03-11T18:25:25.309Z", "7.27.1": "2025-04-30T15:09:20.740Z", "8.0.0-beta.0": "2025-05-30T15:51:37.306Z", "8.0.0-beta.1": "2025-07-02T09:04:40.333Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-remap-async-to-generator"}, "description": "Helper function to remap async functions to generators", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}