{"_id": "@babel/plugin-transform-unicode-regex", "_rev": "115-bdf97c33e55f73fe3ae08939de5d8293", "name": "@babel/plugin-transform-unicode-regex", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0c51eb93727a3b9863aa860c31b181d5cd6172ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.4.tgz", "integrity": "sha512-nRVnYpLyjcimudBwUUEVKUoZoXn/PBbfdVdB7w7UxQTP/lIXF3F0lNChsrO0qiH1SD+BQOCAP1+QjGZ6rScYVw==", "signatures": [{"sig": "MEUCIHCizLJmJIlbNuOUKuEeWBjkiMOKMtUazKNXxs7hjnZPAiEAsqkIMr9JAr2biU2UtcgUYCt5rJtDTAILBXJpp5T/m9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.4.tgz_1509388532709_0.16231998917646706", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a0a22eaf11485a53f6bd2fa0b9411f60ce76e0e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.5.tgz", "integrity": "sha512-9naMtWx+MlWvb70xesI1n2izqya5Ndq3tbrrBxaS1GgDi+kVXyVlRGz15JpJxuXaU3XI6Hvhkg3lw9FfO30KJQ==", "signatures": [{"sig": "MEUCIGdPUmFWTJWg96adU8y5XpK7+jgMsf2IHngKHHpe+mWxAiEAmwHKGv1BYBX3JpjQPqO1cGHqR59QaACnf/kj7csxgFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.5.tgz_1509397030150_0.11468467372469604", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8a51af42da8e5a04b4a7e9a9d9c757ea5bae3657", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.31.tgz", "integrity": "sha512-AkM5Sz6T7/JQL+UgNaPC0gIeY5VjrhQVhqDaMrw9leKC7DYaOf15FQZpx1eCyLwV+eySbKS91Ur3QN1RlIOuqg==", "signatures": [{"sig": "MEYCIQCd1tzB+B3pj6cNOR7WnVeAf2hqRHA8HhgLOSKI1mZdlQIhAL52mrl9cBsOD0b9ooSj/Ba1PEwp86zgx2FuoQQLwN+x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.31.tgz_1509739439762_0.9820979966316372", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "067f67f496e21e57c5944ce2dc9be986b4e43b2a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.32.tgz", "integrity": "sha512-paFzs6VyyCxqfuCVVPKe9ASZ50cKaSWwnygmBTZzKt3sdI1YX8KZ4fSgeiqHsR1sIZzJC6oLAiTajMZO8vJyIQ==", "signatures": [{"sig": "MEUCIC3EbplhBHHTkRAIehPmJSz0lA29aMmQni5n45Xk2/vfAiEA/32YK6PplVlqFd9PNZh0B+IPKSjJU4GC5g0XBVCp6dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.32.tgz_1510493624510_0.09661672520451248", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "61073db0ebd1577e99a81e062f818d423a5947be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.33.tgz", "integrity": "sha512-dxbOnQlYJVc9ehNtkEjxxEhLduaPM11Q8GbshlCTKPOb8+X6vi325ouWWlh90NXoddNeFJXdNme5pwHWZBFucg==", "signatures": [{"sig": "MEUCIQCf1Ip7REaRMnTb3gWFQ2KYTORdFPoHTxEMAan+02gYRQIgGhiRc+Jt9YjCMVWg6D6lcSPUGro0lc73Dq5dmlwX21s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.33.tgz_1512138535450_0.781272140564397", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d2ed620b8b353a276a1f77293909ad8672a9033a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.34.tgz", "integrity": "sha512-tMMOEBxLOzEJ59k3EFzOeU2RuF5cHndvto9LeBhtkrBmG6O+gZXmMSzIxv8QbWnS92ujmUIHvJSDSfgdsOHlsQ==", "signatures": [{"sig": "MEYCIQDsKRfIezeSmvud1uvMf7fbUUY15/rbhOP8u5ED2iiYRAIhAPu9Xhh+fa2j8ajJ+jPfsmh1aI+/m1xmBJVWvXzVz6uI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.34.tgz_1512225593592_0.7964968648739159", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0cb1148921dbabfb819221754b9a54d5cf8fb443", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.35.tgz", "integrity": "sha512-XOFj2eDgoRyVYxbkGeM/1MKZIG79vJgg4UOYPCuJfDF/LkASE88mKMdc24VOch5sfyK+rCr7eFwA3e7MtUfkLg==", "signatures": [{"sig": "MEYCIQDv6tBy+NQyuFvWwQGcfdSrFr35RRRVMCVKJEXWIq9iFgIhAOxHfHiktJpN1O2JYPGKGQNdA83xkuJU7G39tLy/eqdk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.35.tgz_1513288091931_0.19251970737241209", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d2d97602390e408609a61344cd7637020ffe2ce1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.36.tgz", "integrity": "sha512-FdNNjIgXAscKRNwsOg+h6JKW0ZmJvM26jtusKtCf6A7WVpxEYtBKKWylxemeHF6S1JcT6kjxPvdQyJonfsNGQg==", "signatures": [{"sig": "MEQCIGNC/Le+5r7VRhyU0WYvmITQ45wOUM7qe21c+rOvCu8TAiAy2m1tnjsPTQDo5V00VqS9k0UViV+fuPO/ENd9w8OELw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.36.tgz_1514228715674_0.3762177836615592", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b29b1b2c1282d73880993b27a9f3d6af43b3e675", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.37.tgz", "integrity": "sha512-4xssGgUAuTPUEVVCzK/g/5TNmoq9W0XEvdq1nFbqEeBSlFwygY0WT65XbDbHjjXVVSiP84RCh+cNu4seRANlNQ==", "signatures": [{"sig": "MEYCIQDtKq/qQY+2PSTTTnSm5Pvwq8jwrH/3Xwl0+F2RWGhFpAIhAPTaijlSNMzEW1bYudMZC+2sdAJyrXJ+lO71BDntHUzb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.37.tgz_1515427376333_0.31292152032256126", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "304f7cb14bb09dcf7ecd66ec2db780c209d4aeec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.38.tgz", "integrity": "sha512-yDlQX//HQusXLy+ujZvgt8JTKSU6b71/77KINIXb6utsegiy2qkJq6eoqmidpBh78ncEnK9zgtLjY3wt1PBDYQ==", "signatures": [{"sig": "MEUCIQC6G7wb/f+YU5r9xDYUKM2am9y4q8d0yUaPTcNu5X2NXwIgJhe/zURugSwyQv4d8Raotb1jRICCoQUx59ljQQXEnRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.38.tgz_1516206745443_0.00041180569678545", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cbfd3e78bb7ce04597cbde55b177f8b61b6b20b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.39.tgz", "integrity": "sha512-u+h3VOnfRMWSx9i0Eepz6LT8jd0n7bPJwhRMKhnPA/a4u8RsLcnT5ydb2F4yritQH5e/ZdV1eDe3LVlSCXZtrg==", "signatures": [{"sig": "MEQCIAvKVTDcHCaOAYMkIdtcgXv5VKSe8waENqmcdVnOF2+lAiAUfDtl+HRa1SWpdnIhhfKVUmEwrPeXL/VhDQIcjIdtnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex-7.0.0-beta.39.tgz_1517344073127_0.6319615370593965", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a956187aad2965d7c095d64b1ae87eba10e0a2c6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-+eProDq93qiYnXOy+LDSMoKF2lEQVQ+r6DF3ZZXJV5QJ3f2+vwpSqGIQy61sSkVMEaoNtYL/Jy+G8HrWFw9p3w==", "signatures": [{"sig": "MEUCIEO+hFDIL2jPPZ10KBn7eVQzqJmhSkCVcheZf5QpodHbAiEA7OAHcdVN/TrrDqmrrojogmg13S/t/F0gQICNbWhwLN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2564}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.40_1518453734724_0.49135640754324705", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "20011f4f7b45b539516566b792668b9faf706638", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-NQV24IJ4x6BjUi2UCi7MgnoTRj40fpx0xspDWKADrCuMoUPIC6XFiwgEbd7jtPUr3DPCKzPBp6X3j6jXSDCQUA==", "signatures": [{"sig": "MEUCIFg3BoZ8nTQkIL13UB2Joi20UT/xEeO78Y18jMd9iLdJAiEAnFate6KHDqGPr/YKu65HEV2sosVFuN6N2By5Ty6iHzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2775}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.41_1521044779516_0.36254274638655515", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1e7bdcf678d9a9066d06e6d334ab41ca11ca00ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-iqQ8bhkRPZE2Vl5gR7GF0x4xUVlkncMxr7ve7Rjp/TMzNbO3S2XLr+1SPu+B+U3fIPDDA4I97enw5YL5V7QFOA==", "signatures": [{"sig": "MEUCIQDpwBwKo1UvHxs4JLMKCJcxdu6aGpd89q5JXbhq0s3UhQIgUX47u0OKyhh4FKenXJrfCmhamCAqFLeEldjeOjFzdeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2775}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.42_1521147052430_0.46708734563211696", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "141826ff3e90543c2e4fe75179c6fb80a566d976", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-XXNQwQBjC/PMX0uiNhYib37TVYrkEIc7Gl93l4uTgS0lYB5jWro4WpuWy3A8333GHopqDJnuyZ9An0n3WAB4VA==", "signatures": [{"sig": "MEUCIFbY1ZH3WU2A+T4MaoRkF6S6N34uhnSM2ew9S2B6Ykg7AiEA7zxcCynJt5TsOlBs9wrrb2+yaF/i0jyFRvHaaqIw3UM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3059}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.43_1522687711749_0.3880480598027116", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d7cf607948da5e997e277eba1caed30e80beaf76", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-AD5T8aBMcS+dCyaF2WTT2zVPW86+ISxFCKZp6/Nh98SFxaXKuGtT1POvL7eoj3p18JN3mixAImmJSA7ppCDbJQ==", "signatures": [{"sig": "MEYCIQC5L4NgX77zSk6qfx8vCZ2jaiFXdk4p+fNlslWiL0b8GgIhANvgdLBA7oJ5F8gBkDwrM6H49fIuIlSh/RQWG2JtYpqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3131}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.44_1522707613408_0.30604654375233054", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dbd52a71a85f51a4d60e9debafe5364d6df5bd0b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-1AfFDchXP99xCuBUo82u/UISkPcvNvTK2CQzV4XzsQcKJPTFDY2x29Qq27PyRqHkNTnIKlQmthnj7R1Zm0R+fQ==", "signatures": [{"sig": "MEQCIAboUik+ndSj4pO1+bvTaEYDuJLP0zjed647RjZzzN92AiBPkUzRjgYhAuthwXbkMMvqFkpMIlRuyByr26tinXOvrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T13CRA9TVsSAnZWagAACCcP+wUu26Jfqa6VbWrdYKy9\nL5rmGwJC9bNteTlZDfdaECjIMYAwpMpefy39CazTj744qSaAN2CkpIaKWaCm\n3YAH/MXQ9WuzbotGpZ+a7PbKXHH/Q8LtFjRVW2NcAJmR7Gx16O6q2YA9JP17\nv/jijTVjYr/dJrWjcEtFcMV1E+nR8NTQFYgXAMtUF17ERsf4lPxZ/4tjjr1G\nOOnnc6N2GkvF2n4zw4dlS45D0Km+UJSAPaTZkNT/L9w/HCMuS8OD3UqC4TSH\nlmnB9c2XrBvRyk4sfqp02JsMzeUq/4Dcxld8u9Q6kgujul58jWk4JWahxcRi\ndwCSZ5jg0rIpggirE1xXbZ45ZUWfMjoX5d++8ZY5B1S/IFNsHdnPD7ionuj1\n/sWnDQClNoSziCcl9eI12US28QIOSBBvsEaUICPCWFTogXPiEjnDXfAt8n1C\nVLRPtDi47y23mWijH8EgbRnG7sdVQ6IqGUCrZRT7x/zElD4hv24TEqD7vkxS\n3hr8JBWp/Di2iUgU3iWALKSnuGJI71JKeuOq1Y3iLhywudt7GEn5Emx/TtAb\nZYBxHHw1n6mmMcgZhgc0VH9/3I7n42LdB2jgnZxr1rqtJ1WKa2RCYF+RTO5K\nWNA56uSGMFX3XJM1/mgOwGWP4oJpscwFbcOQQw/vqziIPsh7dzH9jhginlWN\nOCFX\r\n=4q1D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.45_1524448631259_0.44220884872962407", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "10e6edcc8eb0db71ff2f0e3fc87ed88337d24fb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-XWQ7uJ0HL/xdMesKzt1uq5CmZPBqDiEno+FBeYuOJu1IWjpr3yXfEzH/nW17KESXGRv68mLTr+fIeSwcC+qVFg==", "signatures": [{"sig": "MEUCIFuGhH+hqhDRysaFxWZq2tCsWzFQPGsSI4zyPnl6i+dAAiEAqbpNcNHLHx6/TT62p8lsbMk0MbfQCFxlInd63K8tdsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGnCRA9TVsSAnZWagAAKysP/i5Jdw+kv6BEL4sSbex+\nJe9V/WZVTjPCRSTlvs6JHIEyVfGSUlckHnYLZxkDqx+aYbXmzOwjmdH7RVVp\nKd5VgNKc2WcH5k4mBaHTco74S4t1BL9XT+4xNuI71uy6/pxHjqLRUoHPVUU/\nikInbqvccbWnmbjh53zrXAdMcjHlAw4rnJd1FdvdZ1zTYYdwjk+5p3wps7OZ\nHMCKbjV+uVMDCThN3qdl9OMlr+OquRIZ8uOYHxHFymB+7dCbGqW62UtiZ4i3\nu5XawU2vuqifgMX2YvHPhmwNpTwJxAQqIWhSj103YMuV50ix4lGcuZkvtIgK\n0zyHsAzAr6oXEIeq+BbtghCwbwdmwsmaEtt79jCGmvtE8A6FTgElxjdfIuNO\nb10VfEjSt9V+E4BPJXLM01b1VNiz+B/DclxvcjE9zhMyf9Vsin6xy44S8nDS\nFvKmsubqRt/jy4Qi6TxQNn9beYveyL2z/uhRei0K0h//1MiMz5/uzFzsx2/u\nEHTPCKpO94+N8z/75v3/ZNJ1l7IeUbYMz+vhhaQdp0MLW6hwGBGNIptbPGRH\n3Zo4zEdkHJ4PCiI9Fa2exIsNHMZEYLDD1jZ5yEGAzvg4/14EuESO9Nh4sKNL\n4xMwGAPWIAHCfo4UAAG4JeQ4yzh4hzlu5m/ajOKVF+hn88Hbr+GOdCi3TTb+\nyOJf\r\n=FhDc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.46_1524457894306_0.9219391278290867", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "efed0b2f1dfbf28283502234a95b4be88f7fdcb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-44nWn421tMVZ/A4+1uppzoAO7nrlwWzefMr9JUi5G+tXl0DLEtWy+F7L6zCVw19C4OAOA6WlolVro5CEs6g6AQ==", "signatures": [{"sig": "MEUCIFxd1EE07TI35d9xua/ZHX4qgg28QLy7xSoYob7P2mMrAiEA21U3KetXnnFMdZZXU4PhhA/Pc0wPEpsN0CPycmxulkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iU4CRA9TVsSAnZWagAA5i0P/2soltVjnVNeAhkaGzFC\noMsmVBtmyDz1BOOCdWjEL+tJ0V2KEFAZ5NEfp98ObNMELxCD0LTGBqmb2CPq\nlJc9EL41LKqvGMmJ4kptXT3ekVKdZDTpWee4uYD0TJYIg9W4l/JDqz8seW6+\n2RHVVeFpXRI+Yq3+Yytjfg+mp7ZfjvcqTvi0xqBYs0gg5/sJWD65dG+zqB9F\nhNqc4A3+dfPvZqSkdFoH+CQ8gyaGJQc2vn8pDloNLlcvEf8Vw59n+9vIPBPs\njJJUrV5MFHr0Uj55mh9f1BDSEpj1Z70UQ1efr/IjLM2kCvzMK0KjR55uN62F\nK0/2QtlmzQQ3NWx/QplISHQVyutM1BTroKcalinUBB5FwAahR+6Uwbvpn4sZ\nkrPIbFqJOSYU2z3+jkcYBEVvLZM893foahqRBxR34osQh80j+0uno8fKHYKz\nAu2ZSlpSiOAiijAoe7+s3lPeHRtHrFxWiNnHanis8Weu1MV4nSMhFPEwI9w5\ntQBDpn9Tb3qUOO8swyjXevpN+LeMYtFLnwizr4QiIR4fMFcBrXL66mXi5oxt\nDo3TDH2BmBo20xfI8mrjZc1Sri06GajPyAUg05Al/JHxhafR0NiFO3rtvgvC\nibQbYUH2hHRge43Cf6wAJRRVEG6ZIxMYce71bxY1E/HlTrisdfI5U10y1tdm\niuOl\r\n=L4xs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.47_1526342967858_0.07261985707775787", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8749c52e86e36045965e0dd4387272b5f51cad1e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-dSNSPWnQIn4eMiu3rh2+sEShc7PjbNs+Da2dCoFg6q5cDqKG2D/9EDNMNSKhqXXCp91Y+Ju0G77xgslw3DU7zQ==", "signatures": [{"sig": "MEUCIQDyFLtYwkcZgheApAg07BujJzqK1UIF61E78RVgeKWIjQIgDj0pzYSGlcDQwtg4YBVvO4jRYy2fwMtOhTQHwSryP/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxEiCRA9TVsSAnZWagAAVt0P/259nXA+wYLfypc7Iz9T\nALvhzKEv9n+GrArJReDLoLO81FDQb+Iqhid987j5tEVimtuIsKJvgQWpjhDj\nnodB+aieMI+C5zZDmutDuclvxAyHP5QQp70GiZIPG7JCPIj7ytAjz3vOhyUX\n64AWYHnMc7V4rebARwFBwAUXjqTeEH1/WPGBKjp9EPGeCdqjSFaPFrKy4DHZ\nb089Zq97EK6UtjnzqDHLFFtOpG5enTZttMa4vdB0bcPlXyP4UTjES6wAns9i\nn8setW9N1jSWcffN9AOazddAZFFiusDCjuDukqWi6L60Z8cT1UxonJ05aLZd\n3JUrzY2SCHYJYqUAbMeBnKwuZdXozePx0o4oAAs7XXn0s87bgJlYsIHy1eKu\n4KLgk15QR7MmPVrIxHiI+FReHH6h3bBWOxsDWmaKWHxWncCbV33hYDifGZN9\n2zAgejqQF1ER873+zJxLTTNnwmEzaoHA4mdKu8I5Y4yOj7cj/E+Oyk9SPIcq\nrrzhVo9Y9JMJk4EzhOjhRR5SaY/hCBbOMr/PcyycEd4K3YGH1S/Tl2ug6tP5\nk1WcCkdh7mG3haMfv5fcLntGBhP5gqPneu5VtViWhDuBTIyhv5Lpg8qPHVPS\nf90Hio6eOnapBavUViXCdX4Nf44hF1B0eVCqdrDL3FJYKEOYrRbtfloII/d5\nfmbh\r\n=dMgK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.48_1527189784792_0.9064374257232284", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c375db5709757621523d41acb62a9abf0d4374b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-FYltHdAwxsBGsCw4H1L7i16E+LSORc25nInuxhHv9xhxccDYleF7WCPU7LYDR5LREJH5c2y6cPdKC+EQ2q3zxg==", "signatures": [{"sig": "MEUCIQC1rH5Pp8RWwYUuvljFirBR56nPRgBzILwAqQ2OrgYokgIgBfJSYE8+8gD9XhnkWmnMOgtwbnGIXs0AX7yMnIFDXQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOdCRA9TVsSAnZWagAAp2MP/Ru1RlPe5DYkMbcBfft0\nF06Vd+zHL5WlYF+QAoFHTBiy2gAMmtD0ModavmfQ4x9NeR5uuCy11CmitgEI\n4lqSq9xkiem0RU9kVzFBLrC5rS1/BB6vunMFHWZ3E730tKUMq4R/xT4wFZF5\n7xIW4D7fzuGS1CZ3LjWyukHhb8OT+Ksc0Ysa+NSIP3pHusgMk5VbQ5rqp7uR\nladbxZun1/bmf9jfGjRFtTmJBE5VA013+yLkz//XEuwl6F0vAE5bF29ERp1D\ne6WPv/rqzTXprIMT9hMLXJGc1U1BysJB5HbwdjESU20kJYQas0RslikuRCX6\nO7s3ecr70vtgU/AoqeUVQ9M/Htv4rQ2jWv0du1jsBwq+drJF/WaF9EgPGVht\n1aoq0xpYc95rA75ixvHq1dihaklf1c1iSt34eLLJU2CJJI3alyytpMOqhyMV\nZlFpsgOBozJPT1P9rybT1b9UXoyizxuvyB5VI+5SMH2AyIbEOBnS2q9C3PrN\nYcAgk1UMRzDv9hlsYOT1O7pYWrPaouvTf626SIqeZ+pUn3Vj/zWD6hZH+vCs\nX2dzHs5KHDTCq8Y2esqUkywiCpWdmDgXXyPfLKpKQp8fYLV6P41iQzW0/lM3\nTTFMT0k6lOnzs0+IvgHvjDuLy+q/ag4goOrNdxhr1WM+K/b8zB3+4Bicm7QK\nt5wZ\r\n=CRHc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "c375db5709757621523d41acb62a9abf0d4374b8", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.49_1527264156899_0.15218677956421423", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "235c9801d812440c69e809ce24c76535038d62c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-DpFpyzPSQrtoBzP+xT8o2b9MpAHu2rOImnB+bv6j29cOx7QUI95OdCbhpwNfGM3sff3pcm6YDwoCJ9yw0BpHUA==", "signatures": [{"sig": "MEQCIGbRh6XjTepE6OlCKsrVy9oif317li6DfdDf0sdunRlaAiBIDyDl0X+WlStrI1UzaaS4FhAtx/56MC8MLuHb4S40gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2605}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.50_1528832845570_0.2735261588914415", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9019f91508f40b50a64435043228c4142c2cd864", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-gGWlJVzFfIPNkiJMyQLexgDCk74ExKK263tgD8TwhNuTNpKE9FMEhaePzwyCdihFvNcRdqMYkkeK7vqHDOzitQ==", "signatures": [{"sig": "MEQCIFukVxjZcYLtRSOwWECxpgkBOMmS9ZCAHoUgiYu0PNA4AiA3mXsvcXyYcucsrF5SQEcYO8DLB3MmTV3lDbC+cE73mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2619}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.51_1528838404072_0.6640555846635765", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9f95e2fd37eac65594da35e90e78262955d86cbb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-0G6K568rG7ntQPH5B5O77KzhQCgqruLqgHHNeBdZNIEVbFIQxzEANYenS0rw1O7lxmtnyhTMh7EvpzZmRSqPsQ==", "signatures": [{"sig": "MEQCIGkd57HdYqKw83Nf1NotxRogUFO1hLBy9+aMtAs2Yso5AiB6geV0voytfm+XaNTfMAB+U9tY0GLtCA8R1E8SzQJ3yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2618}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.52_1530838770033_0.4065126241594341", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0af74ec8019e7d59e38be64db7f62291942fed25", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-6DlspW3xuGi9JKzof3cqdel69TF/bE0tn7wC3tl1+VZ+BnUauEcZjlaN8azTD5YfgEUuiqWl9+Oz0WOyBf+0Yw==", "signatures": [{"sig": "MEUCICoBRcHcfDxrtaiEqGTUGpn9lyiexyd04eo4KoCwOdZFAiEA1Y94s52qLOgFdGhksUi25uoqx+UysQsre7QTFVhhcCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2618}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.53_1531316420728_0.05715090551361657", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1dc7e9150b39aaeb19fca1c863e082f6096afc60", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-Nei63wOVXm3K0/YO8RWdcavrkC4rBlUI2XUVkHQ+ImnGTbIauqpEWAjUnjqjFMs4N+6aM+PcyrnuatGJJaHCKw==", "signatures": [{"sig": "MEUCIQD431YAcWVoZ+1FZBFzVgHpgk5DuLrevD3rfJzG0IX2awIgGGVx6b6siAlvZvlrCK20TbCZZa3bLZVhiwltRY3p+1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2618}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.54_1531764011160_0.6535812918482242", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "87e7bedbba103f784a7999f82064f47c0b35c796", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-wacCeGpWmGR0iL5Av/5fnnaXHWctSY9qFhUaX4HrURWIXexOgkufITL4daLIIOcr1G5E3tCDFObgHCKYMbSxhw==", "signatures": [{"sig": "MEUCIQCAb/zSbhXEiQsubmSb4Or6MOsO0d38y00ZuI4KptYxyQIgesCMPB8aydIPUtDki2/eo1rpvOcYODo/UUaRbGtwPCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2618}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.55_1532815646869_0.36765723837571995", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b3fad34e9c5a6d4181af443713d9e38a292c8bb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-31lEbd5voBJR8ufUuOaRu/r2dxj53S/fs+VNgPqCqvOw7Ql8AuuinYvJjxbzpZ5GqAWLPX1MKBgJ+gsjomXb6g==", "signatures": [{"sig": "MEUCIQCoTWBlkVSM6dPpxe2RzGdqQ1uy11nyC7Fg797njJUlvQIgQB7hPg64qi9S5z4PGNIHS8OBEACa5uQWbdi3rfeSUBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPwMCRA9TVsSAnZWagAACFAQAIgLAZQBW3KfeXU5CKAP\nz/Y7qU0sVDKKmGa9ntu+8SBaujo1lVOoz5WoyBxeZg6e3P9Skwk1NQvyhswz\nfilP2e6DH8HylxIAGdo63+usSjO4EMDOYHQRMBMbIV+G7kNfT4eYjSYu52zU\ng0sHNmE5lBAHih5St0ifav4DfaSlP/kc7pyZQoX3PvX7EkbHnADkW5aQTWaX\n3hvLx1TShwls2WKm80qgpO7lvY1WsaSofGiv5pOeDQ7b+QaWHgaMiGQ1BATl\nS3mSj4m7g+AH5j9TRYnpgWPyJ37vsiNMSFjNX9T0Y/o0b09jZLDkShSoM2wo\nNsd4hCuxPU1RZ4V4hShLsq+aO1Jun+B8hs1Pq8eAgOxF1xTygSVwYUSdkgKY\nI/41nz6RE+16p7yI+FdsnmNT7/DQiJ+5IT6/bHzFE9jqxo5CdjldSQcH8+lU\n1978u/iTr32OEpUolJ51c/RHHHvyZZ7vTkE1WeVe/CkAc5ZxwNhhpKtdec1w\nQXVhY5gjKQpi7docIoFee17LyFCtISl/FJcrEDUipZLBstjYsopRCuFlyI4D\nGG+VOnI4UvlOGa+bx0Unrn3VTm7qi73F3+67lMMKME6engOGkTsA7gD7EVfh\n/LIPPIPBy2cDuncI2NwF285aQyUYVrpZ/pncQfMBWLy01nibR3eGnsXnBgng\nZyce\r\n=AKeq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-beta.56_1533344779579_0.666701111779439", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2140bfed2599d2784efb376503f4cf70194323da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-VBvwYOg87tWh7S35iASjOIfrjhtmT9C/v8emsjooE4FNIrLeSzG/HyLFGEdEmHLQokT1+9K41HyjUUloKqSrrQ==", "signatures": [{"sig": "MEUCIDRvaTkf0dD0RyrwpFNOWtjhT/Edm8uQiF4XkfZYdF50AiEA8VZgysZaWi2Ghi3Fk54Q8UnVuRMR69xTrMRgPS5HvmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSyCRA9TVsSAnZWagAAy14P/3/HkAS53O4AvzjZseRD\nS4fTrHtuQr+/hAlqqXDn36UUq4GM5+foif/jvsp5N6nYrnCdKYUOcYKcKLIu\nSmK+j746JBYVhLcAo1vi47xT1+hh1buKL0Tfdn40CDaJA3/pLwG/SZLgfMer\nCXhz41iwMNRHZBHgCG9JpUOw3U1EkpPWXoQIANYrxu7716FaF1gFoswTfYq+\nVHRqISSJ2MjCTsfK01prZH3/B2S99x4aUSt7sX+zw1KqDbD69aarsNuBxlx5\n/AGDhap1l4StxbKr6dfOvdDTjM/DZ0X3jjXlSy3oJXOQQSbGd1JkFl7+4KzI\nmfKik/S+cLTx2IqsqWJvgwa9+gaBGIZmLbzbXqhUxijEkTHdwLjML07YaAq3\nbfbqf9t7Hc+EOxHD3t+ddVtJQEYYpX53IoLt2mpaiqFTQzF7y7VS8rWbVSAc\nC/mphGREVn61GqvFs6xR+bw7Nz3p5Q36HMGKE2RTXLIrzNiG8yXO16O3kxIk\nIKmmHOwdTo4Bvem0PZaeUNzyJGsPZrudd+123r36HxsFktoLs31iWD1vcOoZ\nDbO9e9567tmrshkqJturQq3IT5NkAeNU/X1q8QNDj/llwy3eKfvZ2mEnU98z\nh6tqt5hMG35+Zk8kUJxOUvGtnHrkVvmIvKdILO3Iu9V5xloEWOVT9jVodOwQ\nXx7q\r\n=Xaom\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-rc.0_1533830321613_0.7432717237948898", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b6c77bdb9a2823108210a174318ddd3c1ab6f3ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-3yz7ehk0VFLqoKVV1GbTdH2sfMtYznhllkBDtnybveM6MeFA5WYCf6iWf+I/vF/8QIMDd1b4359GGWKCI+KuIQ==", "signatures": [{"sig": "MEUCIQC/1srxwmAOvIvdPss983tRZM93vi08bZXWAJcma2PyewIgDsr4CWQnTSInNmWoR7n1l9Ibm2LAaKjQlHEGRIJb/t4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ83CRA9TVsSAnZWagAA3fwP/0JCzMSV9u1QFKAbeick\nr9UKNG1RpK0LQf1Xkmphbs8L8v/qdSaKAWN0iFATUDqEXi88vIyrUZV4D/3v\nne7+skJjhtCQNRxmrcEEpX2ahIPPnub5GJhsP07Owku479QxkeVdNzbbHxDj\nyNwHYxdtDZDekOo3No0u/nqbKsuDS2FPgLDWETrB2DZpJcMY5AZyakr8/Kl5\nlWNEkIZbeiJP+H3akwKMbV5XVFKDNhFMLnVTT85BzaxZJ1wpFEPmZvSv/iDw\nd+Wagiv33RnTriTb0tr9u7ZcsTsc+nkkvt9Vp87dB+YE9qlHwX58WKdpXwlA\nSNOWiCUh4q/rjPiZ/++0RmoGZeWMXZN7nqSKzQtfWb2FgZC9cu4lI1Dj2II+\nSmWxeu4DHxyN3ywz08nmnRPqq1xGaiWxFd75q20kk4riQwxrcgyVJr+XlQjn\neulBULKwTr/0l8H7U0ya+QwK28nBmY7T4FjhBiCzhEDcPJaQgaSXIVN4G4IO\nrlVg1SWBo/394ORDnk6+E8tbimJQi6yG0fWJVGDI2KragR/zrlffShtGl182\nAtYyqPZaq+MY7EWMfzDzEkh1JmXTYniodC+mFzGNqT4VJoQnmEXJPhVBSDkT\nGPzf493A9FloKOvLX2ZmoyTcHr3GhLh07ccruE8ZTL/H880UqGiCloG1cx5u\nUzKY\r\n=JafM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-rc.1_1533845302667_0.026922796510049585", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6bc3d9e927151baa3c3715d3c46316ac3d8b4a2e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-C+28QnIFb8jmw1NegaOtVxnZpBz748h/zsxmAq+uP9J8yGXLA7LtKrFLi00Ky+554BCnqf8Ftp5Fd3NaYpPOOQ==", "signatures": [{"sig": "MEUCIQDlfykiwbRWMltzkdKBkf1/UHWXhfxLPF4nwZgORsnhzQIgc1MnJNxhmQ7uVeI1/5s4OCn5kW6qZI6Bf+a+9ruc2qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbrCRA9TVsSAnZWagAACgEQAJKu2QFssTVht7esCznw\nAjqsl+paUJ5X7hwwTRdIJrjRDbjFeMGU0/GCLxa1674YBah6T889ea5mhyU9\nfDpoiIxisdWSqoH/gWsUGpgkoXJ0q4mvDMfvoe5Wj4AALR7tvr6BmtmiPyIv\nufvXEs8uZDpNt3a7aeEUNX5K6BLVAabitWbLAA05gnRQlt3Lzq0DDQ8rYGkn\niTCfGYnnRmMGtsy8Ie8KA8t0Cis29KwYNqGCM5XyJuQHPIdlkQUQQzrLvRLG\ndZU6Q40FoMHbVPaSsgWjA8ZG9s7de4TM5IoC9LyC0qB70XcW1jQjzQ1VzLtZ\nujQNBJcS1AL9ox/NRRwTikhzHxcxaWSh8fzepEnaUmt5SVPYZ1KUZWOEMrck\nnuBYWdLVCOw0Y4dVvF45vyJWrlOhYiDA9Vbp86vPW5142AMrDSoExkjNfFRe\nMhztztJ3Z6DixbtWpSi+UQR79pLwaeSITKh8GwXuIeqfB+Ck0kKWYTU7boCG\ncmAGnNIn4WHmN5/hgUI6gXzLHnRuT4lS5dqnaalgeq7G9ZDhF7vg/SLc1WcY\nyfr4myIMTfNVnnWtY+IO0tMuA4w4oIhWZqUjWKCrIyzz3oluS4yMZ8IOROQ0\nW1gvdqnuimOH21MwHkMYmc76wjjqVGZhRIYM2AKSTQ0Uomiy4j3n1C+eNVbV\nGTxj\r\n=4VEf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-rc.2_1534879466221_0.1298763880543483", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "91764a332c1c1c5c9265668ef3004432006ce5c5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-TG+4ssdqHJOhisOEVxU8cNOWNwgedcZEpDHUxbUk/901ZEE6hrj9Ph2m/IYvlKjzOeSYu3WWQuY/X70P/Z1Xdw==", "signatures": [{"sig": "MEUCIF4ONuK5FqxWetgoO6G4hblocXTerydcf6RG0cyahHwHAiEAjWHJcBhFjzfgM8LNPFZhfHOibnalcCIe6ecuZzXCxZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmWCRA9TVsSAnZWagAAXrAQAI1W7l4UrLnG9gjLQSBs\nlhOx8/qtH9o57h1nEfbh1jqe7pKo6Sj1tl0tY2DQPfl6d7Q7Xfg04S7/P3aq\nH5EhpTsDmzk5AQyvFwhJ0jJPpKVlt4njUCFpxancqpjqUqccag4F79q6Uxu4\n/6a0g+/Cq7MqnmdU6GNBXU1WadDWUAiZHMWab/p9FD17OzwJKevMf93t4L5Y\nEfPgQH9+M4iCsiKcNpCVfecDD+8Fi1qZRIvsPnZMoKthWwZv3PBW/eU/b6ZK\n2N9HCbwkW7LGsi3xhNGjyTnxqL3d2zoVT1rtMLra2SJXqmAzmxSsPA05OMGz\naLGZvs6px0EUvfoLN16UDIKKBH0BdjCQG6RJWXI5upO15rzTbGdGCF+iOV3J\n6v76jQlcJ1kcR+0Itr0N2lPdKs/HTWUB3koVv4+TgWK9+fzu0fLJbUpJbV5X\nfrhVbqCvilFTPFnLEP1x66/kvSHXjqKcpDCgxDmvuCCnUDX4EmAdDZDq7Gdj\n55+bLco2jATsOffJ2qYwA1J792tho256jmpLNRxbhS6Ydy9EpgyIIs5pAL0k\nZs4E+qLoZWTez3aRYHWV2T0KbwUpaww1SqjnpdxLd21LVkwNPo2DoxqyRFkf\nQrUblt8WIp9PEkGPVNcUgGiylsQ7xCF0xn1EQ973WKm7esLYMQC0rQi7aPsj\nd64D\r\n=eWJK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-rc.3_1535134101794_0.05132805231920412", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d0a5ad191809138ecde4b36f76e4b94f536ec115", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-/FtU88uF0L0bpDfM4mhxymNc+rbH7rTBlYazxtpCSHrKQUxEoYXthdJE8BMeiQIb9fV33sSjMXOPKp7EPUiBSw==", "signatures": [{"sig": "MEUCIQCOvtvMUOCx+uLw3CVlvh00ux/Pod+tGCauog8hGw2dBwIgccWHzG+fk1hWWv6lztSAks4gzp+beOcYINk3W5mbF2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCp0CRA9TVsSAnZWagAAoj0QAIJDQZz0Xa3GJpoJ44Vo\n2vFhgdbmUvRnHsC54vB+gS0pJXkx5NMCTztQHwNvVazlf/f8eCbwYDS7OE30\ncLWnZxbeHf+6Z5gHGVR6kZAFD5CtdqPbXFH31KWSIQ5hMm3PIO89zvHOQCjk\nszAVQh4Ph0HH2167uOuIv57kwXk7NPm2St+w7q/A1g1M82LIG1rjfAm7qO/g\nJmv12sIhGgywBtt2myE0hAhxSWXoWtn28Z0ROy2Nuow64ZIwYVfO4usptEk6\neFojCLhPq4msvBudcPlbMiM9IK4tMmvTqXyi8IccbamRScsBbSbhLRpmaHvE\nowfvRZWnW+3F+hEV/MHvo/cSodt6OdRWbXQJTq1RpOJzMwDmxpOiwt7aCP6H\ncNua/lIJs9+cbC2JspalpLGodBQCT5PMGDrbwb3QzTM+eieCB77WbGXVAq6g\nxX/8/ayPXCJDmGPvMoJkfuTJJs2lyMgJqcHIxnZUvsje2z0DdwF5tZW4/f4y\nTfB/JoVsvR5CG5atcdk6WEKOGxuqW06NXXSpeR9yrjVL7jAH687N1ZDAJxCW\nFTMQd/nPRRKJgORv4znoLfk7BGgogeFOJr/rb5r1r1SplhCbc24CzgFNL2f8\ncYZ1NGpXlJSFa5MzNfiYnU34y0C2I3Iw/s68HsTc8HeIVV7xFjWH3z7kf/0i\nwNrT\r\n=jsN9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0-rc.4_1535388275761_0.1885790047026985", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c6780e5b1863a76fe792d90eded9fcd5b51d68fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-uJBrJhBOEa3D033P95nPHu3nbFwFE9ZgXsfEitzoIXIwqAZWk7uXcg06yFKXz9FSxBH5ucgU/cYdX0IV8ldHKw==", "signatures": [{"sig": "MEUCIDWF8qGfYoD83Bee8Ju1L7akzP1mfK/N3hhKse+hC24OAiEAlGoWCTOz5g+y36bofw24v6Bf7tYW9sbHqclsoqHYJYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCFCRA9TVsSAnZWagAAvjYQAKL6ZmUO2Amh1wjfEpld\n3biaHC+3fvmc5MF78yjcEyqMGAj0QJBLJeXfvRjplCymNPy4PSfs0Zo0acMJ\nFnLyiXqKFYd1udN31Om3IGy11BU7DVpj2/w9jS1eOAkAzey3AwNRXbh3GwQ+\npuLl3RrODj2eDwsm4FIVL8jaka7MfYXh8WzTRcV66kBnc1ubVFeCriAJY/Nx\n9cctsFSoCnyNr4xCnk/G7sugzWzhScaPnPrBOnnI27+vWssqmxa8K4FVQT4y\n5AsxAbqgowd3XNgBoLVxW3aFlQfssAXfFW/3CaC9AKm1n9CTfqTnYa7H2khC\nCy6VNDOrhjFpwAllQCfidnKBO3kqhhCvz2FJRs0/8dxcalQgss3ltuAT1HMV\nJApdu4M6ZfHjic5WMOEIKQpEaQcPOlCVGoIY5Xi1DhSPl/zaFkq6i/bMOATn\nLL6Qg1rAmQ9n3cQz9yBBI5B2pSqL7deMTe64VBuNae/DANlyJZY1egDoAS0q\n0OjL86P/klOVnNUvnPdD+GrbWpqt0fGMQz68pkfj8KTu7FJt91c1u7wwVjAz\n/bOTQDN1HQRAMmuVv5XIq4SNJ9KrQTqkRw+CIYpYWbCGDDNLdCUid8q4FCzl\n0+66nNh4c1RTgt7aR5Cv83qPQ9l3eFIUEL5DQZJLH+uuQFyEDqcSQqmG89wO\nD3xm\r\n=GRrB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.0.0_1535406212783_0.9834189089762848", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4eb8db16f972f8abb5062c161b8b115546ade08b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-m48Y0lMhrbXEJnVUaYly29jRXbQ3ksxPrS1Tg8t+MHqzXhtBYAvI51euOBaoAlZLPHsieY9XPVMf80a5x0cPcA==", "signatures": [{"sig": "MEUCIQDR+/8V0KDLUMQaLC14+dosf1nWfVZZiJZAprCCa25dBwIgBJKs8mGbDEUvZ/op7uDd9vF2srsj/toWKGPCsWZ8aEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2lCRA9TVsSAnZWagAAGK4P/jllykK1XF44V2ddKhS5\nNZ56qyS4IaRmjIrCqhSV+Y63rEDqhdku1TzTm7scTxq6AB8xzYWywE9Uy6JV\nkAC4Cz2JH228b68AA7all97TFsRo2fbs4+np+NnBdw9sxwHvxpYzux9Hjej4\nlaN1CXxa5Rr/6xVhsknF6AwPyAZvB+HFD1tzg0r+G+1Lil1p3/O/FczBfohU\n8kVssT1e8J5c7K2K999cCvTcX1f0/Ga6pWrNbNKJYXJc/l26EfK1PRsSzrvW\nck8hjWFwAbFW+J8l6N4Op7C48+rhJFdlikuYuu30u1v5+brLZQVZ91xg9S84\nRantOlUMBq30n5ZR2yw4SjeH+ZtRBKi1IRsH9HfxIrf6MWsFmpypYIhnRhFP\n+gjwj/RPP8/3kDAuc+XKsq+67aN9vb/wBd+y1JjrBdoQj8SV6pqejm1WeuKX\nwcOoeXQAk26XMeshefw7ng5zCUyJv4oYTV8c8O68JCEWo4U8soxCP/GR2uBI\n9qKkImP19cOX9epgq2EC6hI1i7Z8L3adwikC1Vhf0TK719zn3M+7N3KLJw5C\n11f/NpCHulLdjxg5zrf2roqCvG9qPzqGeiInX16ZP5tftK/XAy409nq+2kVW\nI7S0bDQ72RYRrYeLKcN9HWunGiItyCdDdws2OOsZmAmBdYmT+2SiGf4+3zOl\nnfKi\r\n=ueKK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.2.0_1543863716584_0.6513058079011922", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3868703fc0e8f443dda65654b298df576f7b863b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.4.3.tgz", "fileCount": 4, "integrity": "sha512-lnSNgkVjL8EMtnE8eSS7t2ku8qvKH3eqNf/IwIfnSPUqzgqYmRwzdsQWv4mNQAN9Nuo6Gz1Y0a4CSmdpu1Pp6g==", "signatures": [{"sig": "MEYCIQDfJdX6kOPdYBTvDQWVfQExkMfabjwA9KiH5JVd2ZFKJwIhAKbLlOPWEfXBmpewLru7jTbfWYBB391pusPrmqSKVRtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75gCRA9TVsSAnZWagAAkmQP/0FReaaFoRioXpffZAW1\nyJDtAhuzlywHEOvipCjTJkwhmIClgPkOTc8A6hJyGlU0bNE8gLYSixoP33QO\nljLLHuntAIukABFp6N7YkTLl0AEsW9JVlGrxWwH/V6X+vsA48dcicPhlT62e\nmjG0tO1/syNaWv3fKdRLXzWhPdz/Nke3XJmuh6QUTYlEiE8sfa5O1ZLuwpal\n8YfRFitRdmNvFYueKSaAk5sf1mXeGKGVpCTbKijvtiGBAG0nJXMdCCCP2FcX\n7MIhK7f0N+Yi41cs6J01HlUmZToh62lP7Z+BySOTA/vLz3d207FgBmPvuc9F\nlyYgroE0gpw//lq15/vNZ+iSJo/vbXfJlpmyCRVCA/4u/rOjMrm5wp8hgItr\ng477kAvEgppA536cr9tlP5+n3pQSsEbbCyCAXprkmDUiizRC/LH7IIigzurM\nk1AHSMiLRy8yjeo8ZzTo4ovPAs/4+nQASDsBepu3pAoQdb82+HeBksoi1e1U\n/Xn6D+r5TGJAy58Jb2XXO9neKqnb3VGuUFn9ewatGSm42K2FA4vM1EBN3Kv/\nHKZpINlbVAElvtwsGMJ2N4kyCPB/WACxLm+E9ntl9TvCpAtHNAFHIG5Rqa+D\n9JFpHvCelBmjqK92lJoZwYO4py7zKEVKJHgo09mAL/xBUlnJGnFERyjN25qm\nS2lD\r\n=/lrK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.5.4", "@babel/helper-regex": "^7.4.3", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.4.3_1554234976149_0.06406648486498301", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ab4634bb4f14d36728bf5978322b35587787970f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-il+/XdNw01i93+M9J9u4T7/e/Ue/vWfNZE4IRUQjplu2Mqb/AFTDimkw2tdEdSH50wuQXZAbXSql0UphQke+vA==", "signatures": [{"sig": "MEQCIAWUOCdQ5/vjpL2IL0/M+1qIxAtguXlxhKCTEZtzIKufAiBqmeBwDWhudfsqCjVggULINg/DpPiIP/H/BIU98BP8Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JmCRA9TVsSAnZWagAAp3cP/24+l5kq1sxGvTogwgJL\nrptf5wl71s2YNbYO31X+tuPLI2dpySg88Lzp1bLTVK/LPCuMRcoJjwI0lBmY\n4KA5372AvUm4iCAQYKMm0m/4h0cdyA8bwl08gccW3Clp5L8SjMFE93jtNv3Y\nYDeJrM0YebcuGaoLRamMVPwgetdU6nR2YlXctI3u7fQWLpxDzUGJpmp1oWWS\nr4ai6KTahomrn/Tg7B1pP5k3XgVgYcKwsG+ky5P/VgTKGg7GObBP0PT5MJgl\nUsb7NFZZm5V9UmY7JiUsuE6kFfqR9eLuN/4Lg7dgsY05HMNGvyb1SLPvlnsL\njagdMiNc7GXNd+FNFf6xcYxF8gxNv1wVYiQSOM6kEQHEagMp/ZLC6KskBNbJ\njDlfhfq/eC+OltcMwLf4kNiwjCX65SkkB7tOrd8NgRgxxJUz7w0o1+iDIRb+\nTP5krrEFlW6CrURq36SRXWhjp07Gi0MDYn56YGGCS9kbKyvi4BDOrwRh/JR4\nth/+RfBEe48fzUph8VkbbsZIlj2S6GvM8++WdcsmQrG+dT4/y/imnKSCplgK\nbKGTq9SCw4XOZ42hrguL1HJyM6MUZsKN9G5ATJmLWwJMQ3uNgTaenToxFLtL\n0tT5ki0wXeR8HHdBJiD7B2Qavd06grPDOxniknrVZT1zvcbh9aKw4BQj/2QH\nnDY4\r\n=dCWN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.5.4", "@babel/helper-regex": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.4.4_1556312677371_0.00006253104391396036", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.6.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.6.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b692aad888a7e8d8b1b214be6b9dc03d5031f698", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.6.2.tgz", "fileCount": 4, "integrity": "sha512-orZI6cWlR3nk2YmYdb0gImrgCUwb5cBUwjf6Ks6dvNVvXERkwtJWOQaEOjPiu0Gu1Tq6Yq/hruCZZOOi9F34Dw==", "signatures": [{"sig": "MEQCIG8n5TSklwOl7GAu6J0iOl0GQBOPLRRMby+1Z5FzPmRHAiBXsMGPgAecjfahIytIWaQOhNbtUpWG39dZhdYYBzJ02A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdfCRA9TVsSAnZWagAAXncQAIzb4JZesahzO72oQm2g\ngAZIKETp/anrSCAuxDu7uOfBaqByJWyFmUhfUviyiSQIBRZxowZjYVSztFgU\n5GQ31oDfsYvDn0/OvkqaHfR7lwchgsIhkM+XwQT00gm7pKSnWwDwxoq+I7R9\n1M2AZHAvum+cA1a0NFOo0iCfvgU+Gt5TJupn53U+55R5Usz7HamJgPQBe0FP\n1aay8OVB9GvE9n31ndflJx93VNO7QF0yZa+H6qZfZECs+5TcggbxD1vwTgRn\ni5QJMfsjqntYcvKDO9NIzuh10b8+oMfpxmlTvroBud3IjxkV28sY2NYytKOu\nhs8nWhsvgJKLRWc0YCKh95pBWDGtECJ8dBkWbax9qP6i0jTz7TKf1prwlDN0\n0IQeawl4JlxbQxjhANYlXZwgWu0hYPmp5U2YeRfp7VfODwqGWb2q3Vc0d35Z\nQlncjup7EREj0GLUpqkCSAvgeI5ZGcw1c0XEjDoY8sHhAw7y61XYOIsA3wYa\ns+GVSgKKlC4KSCpQnPlAzm4U4YdL65YRkzn17i1sMgQPTQmbgmXBVtujWEJP\ntL8nLAmE2wsV2roXYEe5+SMSjEG7prss/hvamc1tVyHTpqmvDUHB9fhdEyLs\nR/QysR2Vp0xE2TJ1SNxlcP8hR/Cj/yhDGY4gzAnQRIlN4QWfOCSs9W0fM9Ll\nHesq\r\n=0ESi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b9cb4af953afb1a5aeed9b18526192ab15bb45c1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.6.2_1569273695147_0.6684510221458364", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "743d9bcc44080e3cc7d49259a066efa30f9187a3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-RrThb0gdrNwFAqEAAx9OWgtx6ICK69x7i9tCnMdVrxQwSDp/Abu9DXFU5Hh16VP33Rmxh04+NGW28NsIkFvFKA==", "signatures": [{"sig": "MEUCIQDC4SgsxBqLkDzodofr8/nB6J+SQxbCqoeuXTKhnmRWFQIgRVbmpNjYtUbVe73cNwh9frj21XmcP77AznD7xtxXiE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVShCRA9TVsSAnZWagAARF0P/jUWP8AD8W2OpeLIEZDq\nE13I1jmdQL7Cexdp8Bnmg1d3Z73Q1KcIqnnIsId5jHnVB+1DQqlPFt/KhIUH\nqG2zeitKOJhhfwQbhrzUKZ+54jB/BtGY4QR6EF6AM7SW7773LWBbJwe3WcK3\njIPp7rPxsh43q2eJ1w69W0qkthYGeQYfrSLEqT7ZVdnMUGTg+4D4gQr4vLlE\nmMIR3zKFSrP2Nl4o/q/PuNVtzAnZ8Q8UwdKJsM1olrSI++BxMfzfbefbaiy0\nRdkZS4QbLd4nr2xtvG9KpSs9gXyIADuf8llvzcqTNTJDwom9h+NS7HKxM2p4\n6MrhGIoHzLNP+NjzDIbH34rAPYFLcEmBopXky9bhST6hREIdGoynSKKqBUlE\nuMGsgaQf38hj2ozQKAuBxOtP1yEF2zo8LwcVOdRqjoXj9ATcWoGcSvBuR4es\n3r7SrNXaD6l5oebFjIm+Bio/T2G75fGq+Kn/CQzMMI0a5XKlw980xNdI1a0t\n1uTxUBrjYfJxj8WZnmbkE9KknY46+rXYPng1A38mP9t5DzGdunLaClZ4CjoA\nixW28Ss/evOW+l3x27f/tRwwwxWdHt0qtsj6+VOIs22v7Roije5psx/Bb43t\nBrX+nYtG5t0Q1Vfg78LOG/kR8V67MxKMMNPFckxbAzQH/9wkKcydMZ06WbbX\nYwSw\r\n=tL1B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-create-regexp-features-plugin": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.7.0_1572951200934_0.6096503301516867", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a3c0f65b117c4c81c5b6484f2a5e7b95346b83ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-N77UUIV+WCvE+5yHw+oks3m18/umd7y392Zv7mYTpFqHtkpcc+QUz+gLJNTWVlWROIWeLqY0f3OjZxV5TcXnRw==", "signatures": [{"sig": "MEYCIQCPnXUOB/RzlhbgFip7JCv8zN09YL8/RkTG4ZNilh0ZawIhAJ5S3LcGxyCodnXMh39eW3jGmAQynZK2mf7FIuxtjXLp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HAVCRA9TVsSAnZWagAATewP/i1IJvD4QhuXBiCpAnY3\nFdx+2T8CZPr5kFUIqYKT1Uyf4ndVj7XMys7l4hy8gin6Z4TprNbjfvD4TXvL\nPt9L0DYZjWrLidqB4azOdsHO0bcouxcwwJ+2vB0v44JcGh71Wq7c+OiQFiwE\nOZKPySL664ACUNNZgVWlDLG5XJ/eUuSRkE79Si2eZmy/qqEWU4qfVXjiImSP\nks58/I2FbmHmB68Czekvjrh9c5FyXsd/8zZxY4VhaWif0ONzFuYSxfghBxTC\njsGSh6pQSSvv7SxEu1bA8uKzRsaPkuX1X5UAxnMhCeRu5gTzKNP6V++S1h+I\n4/EQhPuVH3fCWy8YBVmPuccRFQzKwSMpXaU+BqPn/G4PoAvRtixJXWrVmEa5\nRrRITM4QSofSUOj+3GEL3keiEHrkyVFQyxRtVM3u8wpaPxFMjciushqMYiEN\nFlAWRh/O3hQgEu5xBZ8vU/aRPGYn0NaY7tyDdBRA2ka1V7B96xq79BpqPZ5B\nAXW78C4RdptRqxORKBTF7Kk0g50jf0TI7fEsah9jDa0TRJMgz40/WvGixdWW\nHfuhr01HA1P6ieIryqrWzuzhx4W68YSIowyFJS5nNnbyYqbSWK9tk9SnMLMf\n3kYoiC5/PNVLYfv2qEcjZ7vK6TKhUNq83eIC0lcvtk2J74/e9BfEY/PhMvJN\njhTP\r\n=YA6V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-create-regexp-features-plugin": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.7.4_1574465556755_0.11144790961628459", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "20988246a9d98271f861be422e5a17898b80e5b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-qDg8wsnE47B/Sj8ZtOndPHrGBxJMssZJ71SzXrItum9n++iVFN7kYuJO+OHhjom7+/or0zzYqvJNzCkUjyNKqg==", "signatures": [{"sig": "MEYCIQD5juwAnDvw2eAfOiStz6sxQsJLXAy4w4yPDek39YfelgIhALiZqxbrAvW0nrVjDPVTWC7eX0I5k3HaEgvsvLVgRgDo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmV7CRA9TVsSAnZWagAARyEQAIsKZcd9kO6QHQQb3KTN\nh8IhC3VSB31AbX/NyVVjrnbjYrepiL3eo7ax4OuGX1zz5mrqTFFuHytD1sgg\n1Z1KfbF67R0ISTshY7pyl13Bb5bh/UmI1vgzprmOV6zAWBDocNRqyANJPFq7\nv4ZU0sV0V8Y/G/kGLcmz9IRnG5xAEfs40mNX6jx5SSQIZZuj3Icy9ItW0fP4\npLr5vcJLYJeY1nf/dgdVp5CNB9XfCtAYjxtbPvf344R9OcmG+rNOioqUKNty\nOuT8zarwxwcizq4wu1lFcjIIo725k75+QxTTQkgHSbyrDigRcXh43oayhYt5\nyFUH993qQUXmmtt1Xj5uWQcIduyoANZ10nRJCYwOZQ6UZ3BF3aiA2+CcZrpu\nJmc0AGRRLw3PbsBaeYNwAa9/GC7pNbaJdJ4x2sFGpbVeNGnSE3hyt5yLgsId\nWTJ+FW5r7KmJmskSvOCt+KzsZoEHWXF+r17t6zQegZCppngog2+YCE9cxpXn\nVoRzBE4uMhj5IrVKZGzIbLyLyvuS8AHl6lH9oz/FtIvdoqWneOG4NEh/8BU2\nF+UDIEx2rqsrm53/Eux67DG4r/KuaDIa/jL0XhKKxnFpGE6XlFYmrBYVjrEv\nMh+yqxsWv6KqNtzqkQ9Sn58cRTp+XlQv6a5CUuVHfo+C+pCERNDay/40Iqnk\nwojW\r\n=LN+F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-create-regexp-features-plugin": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.8.0_1578788219069_0.026972745036639045", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0cef36e3ba73e5c57273effb182f46b91a1ecaad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-+ufgJjYdmWfSQ+6NS9VGUR2ns8cjJjYbrbi11mZBTaWm+Fui/ncTLFF28Ei1okavY+xkojGr1eJxNsWYeA5aZw==", "signatures": [{"sig": "MEUCIBpkr8b1lk19mvyUP1iRgfspt7sL3rcMTrDoNmIkav9jAiEAwdk4BYrz0FFEL2zTnDq4OZino97imeWnOP+MiH3s2yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ0CRA9TVsSAnZWagAArxEP/1wBcjy7gs25Jg2/1qaB\nyHPjC3k7d6TED08MPG8b4paYBSfZzFBn+mOJfE1k1+P8mt68eThz3p/MHV9f\nkWz6kTO8DzF8wU5694M6t1v9Y73bX71wwJv1TnRQVdIomt3Q9Z89UJeSzleD\n+JBdehqqX8F/KeN7Nr/c9uhgY85GoAMWaWsA0w1RVYljDylLde0DW0Z+Mrxa\nkZo3iRnfzgLQRV7LCQl+MphahXk1On62ZCeut4fO8Glyi3KtlK01gQqUD/k9\n5gaI5+Cg7Mqd/zJFmsA/3sgiyu7S9sjDEoOigFhDoKh/AAUl3NVWOXh+UHRe\nfiI/iRXCMzRKvUu85OJBzB3ZfyL1Sxwy0MaSRCcswb3kCGyO4qXE9Cpm3Fa4\nN4BpeZrnGzMN7J3aw6NqJxd0LsKVR4mM2SDJ50US+UHMvKYzbgE35ta0mhJe\noLQYRwRRd0GpDBNl7R6Us60+DizrWQRdKBJxvKPDj7wq3En1V7aRrkSJjnRv\ndCrgGo+ZK2384kmI24UBVWLShrQD/oLWRBEYqoMWiXaKCO4MNcdHTsq5KNPW\nPD3tQg7SgiXMtyHA8y3QQlxndZWRELcmS6LgX5u+lEpty2eIRLPeDor9iPS8\nphxg68ThOJe4cjNXff/w4wi/CKXxE8rW4KKZlDsK7KQw810ZRqg2XrLfLfCH\nBmK0\r\n=SgEv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-create-regexp-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.8.3_1578951731748_0.6873408280382862", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6b58f2aea7b68df37ac5025d9c88752443a6b43f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-Y/2a2W299k0VIUdbqYm9X2qS6fE0CUBhhiPpimK6byy7OJ/kORLlIX+J6UrjgNu5awvs62k+6RSslxhcvVw2Tw==", "signatures": [{"sig": "MEUCIA/gK77+nOPVE/DuQn0X3aDGDgMQvkAzU9Yh04CsADSKAiEAxQl12z1uVbPTfXFOKTXdoIqsMZwf4SuEOl12KmP5FK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTHCRA9TVsSAnZWagAAy6oP/iXwXiSR/yRn/bKhGW7n\n/+t6f2PDfzJ4c+4rBCMose5Lxflqb958yYCgvefChoh8Sbbd+pL9Ib5HC3eD\nzouXW7S/E375XVopXccX4e16dIpP6v+GkozxeH2Gy8gcQMj+fJDFTk9U3GsW\nGGPe0ffjOB/4VCYmsIsT1OzOlIIlc5hoUuh4+dCAGjxZA2ZydY7DPqApTPHE\ntkydMmLWmFdDqrQbqRXxLY3NDS+s3pqBk25uRGGXX9gQ6Y9GiEWaF1nw5u2Y\nmBnKfAmuXB4F22zBNDd+ZVWLfwGwhR+Q2x93Jz9NmkN5d1epVU59Ir3WOI3x\nt/rx0lxA2k7xK6bwj+1R2DvpSDe+BoMulg+cnvUNr4gO9qJVsUTFh+qyvbRr\neORu7r3rMqinJPGYvc2JZv4xBbshkZyn4bT8FuVndqAxIftgqWOyLospIsqC\nz0eggWyZujDlebGP/pDoFqgnfxqpNmtCLY86dDk8GgC6a5ibi8DN+BVhOlqa\nsDGY7SSoCTAL53/lMBxaJGS0pXq2sOTld7MT/OBKWUqp6/fZOpaFM0/xA+/+\ncsFBfHVwmaTeO8GlB7/VI4DrCkkm9NHh9xzzfAb/1LzpDI3whb6RosXP66Cs\nsYLvIa12HwTHXbiouazle1Jc8nUJUE2tbHADrebvq8e7E9yqCugr6n1wKaaa\nLkjD\r\n=ZC5N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-create-regexp-features-plugin": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.10.1_1590617287130_0.9662070154108033", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e56d71f9282fac6db09c82742055576d5e6d80a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-wNfsc4s8N2qnIwpO/WP2ZiSyjfpTamT2C9V9FDH/Ljub9zw6P3SjkXcFmc0RQUt96k2fmIvtla2MMjgTwIAC+A==", "signatures": [{"sig": "MEYCIQCg9gg7v2+TOvlfyc4EWG7mKJ+u7sp+gxyATUIllkEbDwIhAPMzmxp4RhSgkVnU14paOaN/5ftC45NySvTLVEfTYxJs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpOCRA9TVsSAnZWagAAj6YP/ApI5E2PJyzWtEE+EU3F\nxfKNRbwSVIUsQoBqfg5h9TE2A5cKVv5Uk531BKg2S1cNuZJdEFPQVM6IniK6\na6VYZx1X3+T7S+0FCvLTLYhFvNCxNwoE+UuhNl1deHTgVCxgFzaD9z0KR8hD\n2EYTOM7IqeNtsRoPFfoIDP0fFI8C5EvQ4rpFdzhdG5VKrvBs5NxALJJ77vmO\nrAwk5nDqVlw3cVH6kZSb/8yW9wBKf6a8occ9WaveVIFwna7TddvINn9RzW+5\nU6oj3L79Q9YCsxa5Ckh0HScK/iAkw8tl3N0/24UwVOOZTlH+U9oDPUIzPdQM\nCNa85/o90ojSmMPLkbv/BJXFq51GxJIFoEv5Y1acYFq5nG5wR/TXFZpq9oSH\nWD1Y+OvXNNm/UnGvxs5rePx0/G0UmXK9SAVMYZHk/Rvz5fpDB3+6R7BEEJiI\nFx11EltLDhAaKxDLYuasWQAwliTKd1rz/QXtAwhUGkKWCijTZJIeOhocJQRn\nI4oKYlVonRwipy3ql8PimXnxiV33RYeAdJsHWWy9BqisD0snM+rzXCEfVSvR\nySsyu0SKYYBulauZzMLGhecqRfJThTsFaEcOX58HLy77ggBMrr/+Nd10rtgK\nvRUPiGa3GurstsQPqQSs6hsAGAWvmfnArvDkaPdTFg15U5Lz8NcynCbZ3U91\nl4D1\r\n=dqyb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-create-regexp-features-plugin": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.10.4_1593522766062_0.8687781776838204", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "cc9661f61390db5c65e3febaccefd5c6ac3faecb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-SqH4ClNngh/zGwHZOOQMTD+e8FGWexILV+ePMyiDJttAWRh5dhDL8rcl5lSgU3Huiq6Zn6pWTMvdPAb21Dwdyg==", "signatures": [{"sig": "MEQCIAHpxo4uj8/RNSgqgT/kTiTNSbQdtvrhASQkU3ek89IpAiBwaBv5dKcE2ilcP7hNPJDOvMZHRC2y0Wd8JLWjv2G02Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAOCRA9TVsSAnZWagAA2WAQAIthvMwXNT1gddiXfR8R\nzZ880fZxSEiomVojpqu07VubOKk2oPjsFIAZ6G+MRN7RiSC0gX9y50WHaIrc\nbbub3VjUhusvMF7RCbqCV+khLm1wBCCEQ5FfN9Yf2GtbQzSyiucsNXjcMUkS\nz66JTbCuQkBjrXKSVnZE/Xes6M1U9xv15uOj3UWXx5AWKF67XhXSzlw8p02K\nss7bito1rOA6h+X6KBv5b5n1y/ifsu0R3Ep8dWS5W8AaTWojkdn0C337DBRn\nqbCJhv9YoPlP7nVcFG5ZjKLiJ/rJyIAjx/JPflnug3PSfJBOUrP5njva2rtv\nPpGX2E+FjagPdGKT+Ry9KAJv4YmTANM+5yjPiHKFBGPHsqfFsYp4eFnzQUQC\nVB31pt0EoP2JcLiBorNdBR2pRq0MxgHsPzqTfGY7KfC/aIyIaxGZ8hC3QC+h\n4tZEIC2oCtTLM8F7mP82Vdly7cvzMF805Bjwe/XTRoydfNoTsT1Gq4xrbRNJ\n7gbD6z6Y7DLw9PRIqQ0I/UaQdPiXJrhRRAdNZ5gZqGrIj/6+aWxlaIdR5h0G\nN9yFhDXM6NLA9KokOxW1olbh9bbbe2uOWAbqOmGm4eyXwF8KYvI7DD80K5Ye\noUF2yCdCv+mhyF6cseiWVOXvGK+rG0cKPHDyCJa7FK5NRTOCzyDEDClMIMiY\ndSNg\r\n=YllL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-create-regexp-features-plugin": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.12.1_1602801677824_0.9745542964362921", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "b52521685804e155b1202e83fc188d34bb70f5ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-mDRzSNY7/zopwisPZ5kM9XKCfhchqIYwAKRERtEnhYscZB79VRekuRSoYbN0+KVe3y8+q1h6A4svXtP7N+UoCA==", "signatures": [{"sig": "MEUCIQC3WyE0NnYLVAZyekrekVaOAYNwKiHjyg1CEf3G22ml3AIgQAXmBWVd3Ha37KR39DTH/w+noSTf/KEmRvRq3TdTI3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhLCRA9TVsSAnZWagAAAJkP/jHsrseZhs/BQlHrk2Wh\nbty7h+TGSZ9vCrTD9VNr2xatXSCquqUSgwgYgYqr9YmdsOXBXn7vfLovyxuj\n99jBU4EiBHWT/a1LLvu6ejKFrl5cy/iuhzDjhj6et4pymeLgUuKJ6eSW78ZY\nvy4F961a0VS3byH4weL9k644kKXb+Cln2/WnZM4/51LV5rZuFfDU3xXa9Niq\nQPs/XGF58zh5XDHJalNHWL+qHb5+p5Sw6O0GG0Y5pMyqmULVqa/Le+3h4Tqm\nlW6i/xVhUU0ovthBU4gxJOguThLJ0pWuPlEWuAtCQjsOwiMXXVAJx4LANdig\nEUfYjNjzQW0FNHm99bV+E2bgn5ov12L/SLTT8M8H4oXnPNguGVBxf0h5gCoY\nkCEhBQr/qJESbMarVF3oM/lAHRX2RsWRtDRjbrTdPq+F1GXamGLzYVOGtVXj\nSFQj3dB3Igdakgx3GD5IhJYUgt/zGU979ukfoe2drfufB3fhLSuxm/IdSiQ+\nTOtpMFeLf9baNdbnEkgqg1Nw5lWBRSOzd2XQRfxltHSrPLMmRBom3srbWfk6\nQ5lAzP+u5i4qzeaVGUx57vi61qgE5QcMcqaYQ6CsJeHZQu6G/DIGxzCcekcF\nfYBVxpVcqpUJLYGsXaPPLDTtJQLnNOxqeYRzMRFeUefBXRuwChUx5tIzk5dX\ntkbx\r\n=btc8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-create-regexp-features-plugin": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.12.13_1612314699486_0.5868487682812984", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "4cd09b6c8425dd81255c7ceb3fb1836e7414382e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-UygduJpC5kHeCiRw/xDVzC+wj8VaYSoKl5JNVmbP7MadpNinAm3SvZCxZ42H37KZBKztz46YC73i9yV34d0Tzw==", "signatures": [{"sig": "MEUCIDKYLhv/7HVTsoQCizm13o/SxWUH1O4tnu3FCH5h+DXMAiEAxyjpJ7hVB2WpaT61fL+vUznvuvpydKZE3voRLfJ9cwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr6CRA9TVsSAnZWagAAgiwP/iLmrp/1v2fKI3mLCrNt\nXNHKGS0jEBko0RemqENLfvYm/WSoWXimSeap6Jtd0+PHLcVnkWWW48tZ/zOy\naBl2rVz6SjjPYW4S1jg54ZGT/BOWrJuHCcgU7JGh2a8AVuQn/rJ/CufI4iOo\n3+a2JVImKMd/aatnPaAOSP3f2KYEzdb3JObeKzJvW2DukHLNi+izCKeARLNA\nU42fyulzNRcfCGYsNNEIHgT52q6nzrrZCjuWsgTJi+zHKU7ZN2R0EhzLy968\nAssz3O/FQKznNQRIHfDSfSFZRimIlv8/tZpXwy4oT/ePAszouY5achJhbwhS\n/e2CUxJz4e7LtFO1jIxjKn2/J60uRR0Fn7ABu5U71ATIW/dIPczVovUfr/kG\nFWOGA7XohQgI1TtdAbDOecrorltjfiRg3iHFXk1jyshgO8Z6w352ANCr1/gC\nJQig4Ll8otDxefc7KxfRKhn4/e0rL3ubxFS5KcQBGybGrEdF/FcQgpmWSOhW\nd5zwOkEMF4g/wgkKwVsZ0xm4eAOMh7Y8XKgGYrk59XDVC2IjdltPs4JGOC2U\nXixNuVx71jgFpQzMWpaz1698wqXBpAWUvXHLmFkBb4aDxdvZa+feDTKtWRm6\nvCGfTIUuzBhJZqT+DaSaoeZPodwQDAJwOqJe0nJs3wKQQhEyHYF24DFHeVLb\ncjXZ\r\n=pcKS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-create-regexp-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.14.5_1623280378149_0.9477037279661424", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "293b80950177c8c85aede87cef280259fb995402", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-jHLK4LxhHjvCeZDWyA9c+P9XH1sOxRd1RO9xMtDVRAOND/PczPqizEtVdx4TQF/wyPaewqpT+tgQFYMnN/P94A==", "signatures": [{"sig": "MEYCIQCT3EhYX3NqnE8fcFzvy24NVg6rA6sJMEzmDOrqdfa30wIhAIZvSDQ95ZCPPUKKK2mBF9eZ3x05aPZ+u3XOnkk3c4Av", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2937}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-create-regexp-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.16.0_1635551271629_0.4914811480206458", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "ac84d6a1def947d71ffb832426aa53b83d7ed49e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-GTJ4IW012tiPEMMubd7sD07iU9O/LOo8Q/oU4xNhcaq0Xn8+6TcUQaHtC8YxySo1T+ErQ8RaWogIEeFhKGNPzw==", "signatures": [{"sig": "MEUCIQD8mBBt/+lzbkxHNRKDvne26qK6MntJXrJpk61Gtih0fwIgNrUFlnQ40nx0ash+XP4B03YZuyavv4OpDEKzVwtF8w4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kSCRA9TVsSAnZWagAAXUcP/i64VJMqWx41BxGm8u+I\nyssnoJE9OAnOYRxgHxjEwPmbcJhoYI/zBnpCh74NeH9+b9N1oVm8z8Cr3h5p\nTCXCRjrf0yUCPc/B4QxaXbYbsRyvoRN0gzy+LHan054/Zi4vKEREcE3Mreez\nwsTCYxpQwFdfIke28ibmxAKD5iW1bOG0FMy1tTiMbfkZ2M7SA24Qe9Rtqgdn\nXRsZaRZIAyiBRPfv28CvzqZT45feSbnhuDySGD4MFVNtCohkHfychb8cqKNw\n7rfWnJftHUGlz6hmxVWN0SyJ1TbZo9gZRKPV2Rm2O86GBjV79+WwZygn2msY\n/qvBHv9UJp3lC50Z8gf4dG8WlZ5zgFHHTpRlYEug1WBRPSoY5hKWYNdFQ/j0\n9653hK1FzRJ1MC3XR96nAPYxKLd+TL32JjM6zlMlkzmzEPlYkIqGoYPlDN2i\n9YzawMikzMibKLisdtBiz+K+L34OMAq7T8pNsHK3l7Hte6OKR7G2EP5/k4Vu\n+jAgXkzq4k9dcJHBQigF26pFSm+mcVuY+HZDhQ62gTP/I8EQHKVBYgeqMcKX\n0BHE5fRfLv7y8AxOfQurJ4vEMu3XqAQIRYzRZuCXp8OZl5cXT9ztdwsyF/42\nPpnjSjBU57NZLf33sQj81JanqaEO9hft6gNgWru7s/niGlCN7gYQTtYv57vc\nEgsy\r\n=TB4Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-create-regexp-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.16.5_1639434514098_0.9549229077541996", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "0f7aa4a501198976e25e82702574c34cfebe9ef2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-oC5tYYKw56HO75KZVLQ+R/Nl3Hro9kf8iG0hXoaHP7tjAyCpvqBiSNe6vGrZni1Z6MggmUOC6A7VP7AVmw225Q==", "signatures": [{"sig": "MEUCIQCFx7LQ1qnLvWLWkgfFMjtvmOr4aK2rrrMIrK/03Jo1KAIgEkNqIydpBQlNRBCP2uhQ+t0vJeonAupeOrnwET9JOLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1YCRA9TVsSAnZWagAAlykP/ibj/63clLBQofds1qT9\nlEpp6V1PLAxrm4ZiBGk29CZFhK/Drs4GZxoqQbWFSFmpOYvw1jZ4R4vG8vVT\naBwqT2cDoCPl5lTL89EmSeD+BcOFf5SmOc7YNrEQEJQKWsCHb0d74Kc7qDy7\n5h5uehyi1FK+lmKX9tjlWqYBiCNYXVsMKJidbKEN79cndKY2GmDZ2WgQwGkL\nLSOStEjNj5Z9RWE2jiXcgYgSXNx2cTiqk+/2tzdoAZXlrQYwJ0IEQPXShC0/\nvJlEm5vxlDIACwccxd47Rdr15LBhduQXJ0zJo2EjBqi8M7kO/Km3UxmuiT3G\nVtYLrF2fQJsqoUohAXk2nc+edN3Zjq+pPz303W4JJCx/O/acQxROUSlO2l40\nd4kE4tLop/bK5FBYhLOarAkfFU/AqeHk0feKLlDFRQEChVJUKNUP/YTab3JL\nmjkf64RDybPIwSdBFfqLSrGVKNAt+1p/ICmM8WWF2IaTa1UeyjSi7EQMWzab\nMbsc/vRG9Nc9/n8NxoJupfZ6zgZSM+B02TVfTPEyU/hina7cMxQpl4hThMn4\nZ6OaEs89ZpCahP6+wazM62MUeMqzJxpD3asJWE6UiLpRGzFbI4kkEY9Ukk6S\nAUqwI9V+nWxrMv6UEuT/VS4m5wMNRASMJSXZSxdq+FruF/NUJN58CxCAXiwh\nJI96\r\n=cIgl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-create-regexp-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.16.7_1640910168410_0.7793075432394374", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "194317225d8c201bbae103364ffe9e2cea36cdca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==", "signatures": [{"sig": "MEQCIQDhR9hiAA/MS7n13+GVx0rQ1QO6HqZIVAhSBo3KVcg8BgIfN48Kbtz7gKnKAW0rDjRDOkWo0RA8awcFODrB0Px3YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzOBAAlbcxEXRPpIyKrKQ/mufMjmXRo6pwJcpD8vU+VGjiuFnIhf4u\r\nlDe8GG2f07GAwkbs99TIRSg4TT0ZDlihANpf8B61+tGSLxQrwDgDyER2HxV0\r\nQcc3JkddWaliHOOwblXqx5lYdcCpQVhQRKNctdab6ROAW8/6GLNOf5w71X52\r\n1tOr0lx7zw7KKfiVxl69ulCf4aOEpVFDZ0JA9g4IfohDTG5aQT0/VT3/ao05\r\ntx4Guluh67TPp2juo8qNK80eB7vA21300oeP9JKsDFonDtB3btOwEF+pu90f\r\nYGaZqYb2Mv2WDi8y10LpraHaRaQirmYY0t1AnJp0i36wAZIL4PB08OzznF54\r\nkKnQlp50OzDHyCgykEyHKawqbZH9vbs2ueAIL5/nhueogd9Ta73aOGQpJUA4\r\n2xVKzO2VNTF0kOmJ9o7G8wCEmLpuGCkNwFSv+qAkD9KFhQv2zOK/ExvkuBc3\r\n7A61ez+w3CX/VfYE2RM/yNSY/YCT94qDKLc3PFOE5cuYBOuxLx9hqaERDf7K\r\nVo4FpoM0OAK5meBy2mY9TviFAOcitsN1RnfZdXHdLcW9DQPqCFsJf0pLJgCm\r\ncLjAUCZq9RAj0ZqU3EH209rDSSb6IxbWRaUxBnTV8U1+DdKgH3RO1uwrgjh8\r\n2h20/9RSqZUL/foI0BwXx7XJL1DaXOq/XUs=\r\n=tBnq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-create-regexp-features-plugin": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.18.6_1656359427526_0.5381292926643118", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "ff7ec7e35e0919e6bca6988828be447e3bc1c35f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-hhWuZlTJVndxAywnsrJKKlM3xJ3u211E/qTLNzAbf9qVRSlWgRV1pqPVHe2XfvKc+6YbgG1X4x6Ku97QrJJJCg==", "signatures": [{"sig": "MEUCIFyANRBYzqSSCX16uraRGIZDqgHhEFFMty/IpUBCILtXAiEA13y2t9bHp9vvqZFF+gT5sjok/LvB228DMdnfjSMd4gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpatg/+JSasZ4Ku+1KoeE2t2GC1hLA4yfSeVyup8sAvUsbpmaf31gsJ\r\nusek+5xKbGCOFlrjlsmmecqrF+KEr8PzWs56+Bh+RyZV9+pBuNdw0f7ASnN7\r\nEX/NueM0Ng6rhe9CWkMMvfwzJWRhDUk6SpXmMrjm8lBMX0d2lG1fLjT9jYCn\r\no+t07+KRSP8f4uPzj6/JLhW7feDJi5CtnWD6wNBkL2A+zfXonVYzl5DT0ER8\r\nH0w/LfeADdG0SFDNC6ZcQNGM0TMlc0JPwYKINA7/zPD+xCXJsypcMAF6+ssM\r\np+NRUYtE+z6nzoXmsR8rrMjMag2FylF5Y+pqyWv/VHV/KzxjBWxUURVrGNBu\r\nXewQW8vsx0Up/X//hCreI8L71M5BAsjVrZanUOxU+Es3cry3SyS/Bcln387f\r\n3I3fqoTDXH7ksaEEN73Y8/Dt16sF/0Fh3hUWi9oXK8OR4jqCgRBOdGFAIpi8\r\nW9c8DN/iCSG5v774OibHxxBD5M9jKSiXMkSBYHp8eEAaVmTaSM8JysFu2BH0\r\nRpkk+yjo4Z5fiK7GKFt1VPsQaYfGC9YqWxeUMbmaiK11TzIwNSyhCe0+lsw1\r\nyicfWf/TpGG/AAlTmks96L3ePius0rd2ix9yK97F3YPGALcTQLQg+aFXWPjH\r\nv9+QDgcU5q0YNiWUEkCDyj/LFYEpAdh4qVA=\r\n=5d+C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-create-regexp-features-plugin": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.21.4-esm_1680617391267_0.7311944067358604", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "bd36be9b5e9d338330a8fc6e3295f1560f7dcc2b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-y1bmIVT6L4lM5VVF2YgCg4yoQepRlA1RuHL9aNbP5dXWmwILgm5HCOl2fzuFwSd2Uo8TEuzzRiZl+O+csuy2gQ==", "signatures": [{"sig": "MEUCIHQdVCRMoYZM0ZhGRLxuLMDWwAJoRKdCsec/WFeLEqjWAiEAtz53UnczEEdb8srGMfIZeKh34f9mqe7P7Asxg01ZvMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQBg/9FkKvS5lG3J2eAscmfezEAw7Js+4qNV6Y+a+kMJ5aD4rDCu5v\r\nbvV8rIzq3B4uf84jjCVI2omX/FRrs1k8lnp0xECaMad9GU2mYhK/rp60USLh\r\n4r/Lx5ZXETrx6Rg/g3sDRsAOQiLUrFeJ+HhqUDAUF/yGeF0/bzjFp90zu8MC\r\n9aUFZgO5Mav1xstD58O7WjtKmENf6G8YTmSv+GnGXw+CEXoUjYWCxFDwQHnP\r\nvUFb7I3ww2oKZHHSgK7Q2+W/V2oeqiMc3x3vpt5grD2x1Egh0reJh71Qsn9e\r\nTyeNB9YvlwvJ1q/3IEqhIckX/Q379gfYuyOo2DmPPVP76SEX7iP27pUwg2UW\r\nXe8/NQ78GvUo4Tip1Zw7x/YL+6lFmLqZpygMCfculqesteGYf9OsTvhvXAag\r\nnrPzeM59eRg/nVmIWiUoWhxiH8Z9zj52ZOVbWsCLrzdGOj+LtaZKVIxdJKhM\r\nmTrkEq+MV/L//4sLRI/NPdEHhvTPk30LrIpFu6WNGLIoD5xkDZImfodTdw91\r\nnqCX1zm9oZzpgCz7RbTu6YrkwsW/IPEDfn4O3m6YohEXsHqpeYhxLdcKgRSd\r\n4RFysFW2+zF3FRwWrOR4SoCszN/7BGWxtcjmLxK8ikoJBVB7Y+I9BIGohoFc\r\nZG9oRwPEg41YwdJt8SkC3Q6Fs+HFxrbrkaY=\r\n=env+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-create-regexp-features-plugin": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.21.4-esm.1_1680618108931_0.09765303703400718", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "60ccc8914e0998d4a6af6fc71123c7da076bc952", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-Ic4hWzmWynv++ixqFdCh+nJq09HOeGqxIa66fRdI7Xof+8PaGG4X4Y10j+9n1UWzKYVRJFhYCM7Z+oTTEy6doA==", "signatures": [{"sig": "MEUCIQCTL7bOnkF7pjEEJ2rqPTmRktW2X/15pvzWMQG6Rkhy5gIgZA/lzdezbLOcwe7jlJxXGnmm6z39aXh9R6Vs63+BZbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDazACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjJRAAik1owwMX3e71MC1wGrMZzsZZHdYFJAOAzfzFqfpOhRstvXEb\r\npmfcPTg2q1GUxT+uPp6Wml7PJTT131hNXpzl9Al3uMHuNI78QXXk1fiSLboz\r\nVFJIymcPngN1vM0Y3rVidr66QPpRptFGJakJtLQycWEU52SPq9ULhD6l7Air\r\nKUSuYuBedqhG0BXyeYGyV3EEh5ODDczYO0x59Nuxi3+JNq+9SSYAIZjHaKei\r\nrbohpwR3pXRtAuHHWZcd+GiVVVrLgES6LoX0loHG0gkwGU2vr4XkJfRP6XE2\r\ni3Fy+WEy00KSe2QJHZwArL9ujYbVtuLX4fh4yML3w4U4grJ2l5weUlqW7yEa\r\nDjbBq8847npcZg2nJCMds15Lz0OR9bKHXcNNCXSiivxwbpPVsub5MBcIAGtd\r\nvGUwjzt7s5idt9s8WdTuPAe0GqroDV82cckIsm9Oqcx++dNaXQ11kfsbOk+g\r\n4EARm961CNRNjWZZQeeV959GA7r5riSy6gSOKZsAfzmZMMY6Wyxtc+kSAsIo\r\nm8sskE2Sjyb56GdL4cUFxvmrPxdv/SVZ+UYj81xNaHWvd4RFK69PTY+r1QER\r\npi/WiCuAPEajuRY0A4gliNrBR9j392ityQLJM93Z+RRiiUYO2rEl6QMfRe/o\r\n3UnQa/0QRXSktNRaV5bc+ooSKw7ZLt6pGxU=\r\n=cGHb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.21.4-esm.2_1680619187707_0.9123274336906824", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "13353a856b1d18c6807ecf04d10fa2b23cfd6405", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-Vp/FFTgvBZHwcOzAMa4ah2g0olcQNYNW9cT0qsy0yBathXyn7sA9iOCLFATh4zycBFiszzMv6fE6kUfTmLPstA==", "signatures": [{"sig": "MEUCICJ9YQ6PSdj2AIndoEiaNY6ns3GdzcVBzor2bigPjbh/AiEA+z1fUbDR1A8FJDnhcEZM6rQdeKC/w5gaCaUNG9Ntbjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroPQ//dqNXJhlsDZ0GkjUDpuZrdABSxO21Yq8oq4qdKkjLbYRyD1uX\r\nFk/M3Ao/TUO+GcwKu98wM9SVMNFwSLWI3EU13QozCGI3lYQErEmGuJOp4oIi\r\n8yELmF5GkVoGBiNkbmydZVx8NsWbnsUg+H10/H+2O2NpCKXeKxi/7qplQEVH\r\nPcQHwL/zXB7ElLJJJ/I4U5z4r5dVPgWJUkGJjj2lC7eQZDlPgSuCtw/lZIir\r\nvg4TjNBruje3+JyqDVoa5zmU+g0NE3J8y1XYrvZN6V+v2sRriEcdZ82eFMlc\r\nhbiwlHiF8yA7qncn9cyxibrEYplii+2wKVDSQDoPcUU/UZogt6bVEeom4AzZ\r\nXvbSLxv8hsw2BNJp2OWNvehTMEdIKsxxfKGaGLFfPTAO0Okd0elF4YmHkp6Z\r\nNJbpjw81bqVtO8eLHdjbEk0gkvejxiv4CQlKLENUkODkjeP4aHrPI2u1G3zg\r\norpzYFyKmp6s+qiBNqjV3hYmy8txaGcTEVjVydn5XUi6SLsmh3eqspqEPDrk\r\n+Dm5YafOuvrsG2Siy2BeS7M+M1cr3XAw4tGPcptd5lYknhD2H6iT0jf4GAY3\r\nYapF0pnmtBHpFMtwRmykfQp//k3vIwjmKbcJYYcQ2d1e0SfIuJumHf6yns57\r\n6sgDrIiP8kOCSpOiRQXzKJSRlOSK36iK8zM=\r\n=HxTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.21.4-esm.3_1680620195624_0.2358284967199089", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "6e89e2bb5fa3a712e30047a105aaf4e8e6c21ab1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-RnwDbbh4voAwUTUqN6vQYfcpmYbQ2pm7xZ9rMDBjhx34rcCHLroTxXtTibFmBPUt0i/G7Dn49I+WSAYHKc2yIw==", "signatures": [{"sig": "MEQCIHpksYjtUdn9uL2SQHARFn6q+DOmp9SUzHAdgj66beG6AiBKwMFJf8w/velJgQC3FjfvIJ2/hWXY8AqBEeznyHu64g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwGw//bvhwJE/YltGOYAnr38+TVoKwaeVIdFYwBAuSgWQdTa0Cx5pW\r\nG+IR/Lq+9K9BQ776O+bHwMCBzmVhcZFADeOz5hJlpP+znHN8jaSaAAh6oFnm\r\nQx02jvylVO4stvP1+hOyFeZ5OlF/az0N0Cw99cxFKHlhVu250w4VZvRpCBg7\r\nl2ObSIhJUEhR+uzix1D9t5cQYA/fNoUCu+0ZzulvhMNR2T0ZMzyvTlz/DAZE\r\ntVR2S2T/dGqQm1yGvKnHYrzzDdR/8AQKgPmiZRzBb6Tb7qerNCp7kKgDxDUk\r\nn9KgKEMf34buOd4BPyJUbilkbA5F6awmXj7uP+yRC4Q3uJ6egiLNNbBJP+wE\r\n9jm9c35H8AzG+gC+1StidApvbDCMf+Y8GcWTrnblQYaPzcnqEzOMw06i2uhH\r\n1ECXw5OAyROoeqEa0HC53aIVH/8oVRKVafcH9WR74HCvuq857od7rWQV7u5e\r\nd2zv7ML/NdxrFGXnhYqLS9PiKWvWKwQMca1eFTPkPs17mE7a1dlANO5nl7+V\r\n+YYYj+kFZ4I26+f/M4101H8pTvh5T0aWGJ4SBh49LLGL7rBXNxcn3DHnGocy\r\ntIyzReauxxxzwd4bTfSGEDo+yvT1HE8dzbPsxTqH9wo+N2Xdh+kyQ0c4v+ZU\r\nXzg7ZyOPRXZ0D8GSGtytL94Yrznf3g0YdS8=\r\n=StwQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.21.4-esm.4_1680621224784_0.3900162884759195", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "ce7e7bb3ef208c4ff67e02a22816656256d7a183", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-028laaOKptN5vHJf9/Arr/HiJekMd41hOEZYvNsrsXqJ7YPYuX2bQxh31fkZzGmq3YqHRJzYFFAVYvKfMPKqyg==", "signatures": [{"sig": "MEQCIGPsggi/0P70IeJloHabkL13h/1qmH7/W9sLdCXuVdqrAiANVksAvPRqazkK01cOw40GTE8jEwTuHvtxWA8xAt0Log==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3923}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.22.5_1686248499570_0.6862621608871105", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "369662917fe7c62b6b71339eadc9b93f4587657b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-C<PERSON>lvu4JsihpsOJ0IDAEnPCeX3opbjLzDKY4gqFWsI2tcoNMExOzL7U5AuwCq8UOAieWmwws2P7IVqdob2kKFRA==", "signatures": [{"sig": "MEUCIQCzupNhJjbXRtHESEBUukede3bu1cKowgppPcnJSdfgrgIgBdZrIsqawid1Atzox/8FEpHui2AtWv3q9VLQRfNUIas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.0_1689861624614_0.7966396281679873", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "b0affcef9d28a0afd0cf04cf4ea21ba045da925b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-m8y5ZcEbi+laN8fZRLUp5yF4fFbP/misxqgd1Q2XvicyUNZV/VPF23Bq9lkLDahotKdR5VQN+wpN1HAPSbfhcA==", "signatures": [{"sig": "MEYCIQCEpAobuxJtRs6NjWSnkVjz43XiE+wR0ukE8+YH4YrISgIhAI5Np0gND6ag9Ib6TCgErbmB8TaWOP1MD7R9+Gia2juz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.1_1690221177384_0.33863528728190007", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "02415b6e4ebe309d8dca9f15a7d7aa151510a01b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-aEseaL/APRLqrcreIh95OWb1APO35x0qkkVqpOeNSzZRWv4b+FD5772vH4/XKBCUiKt7ZxkdY9E9YKK5oSZswg==", "signatures": [{"sig": "MEQCIEXiDg+v7i47jP58kf2WH054poQ6EwM+ZQ87aT50YZYqAiAy19kx9lgIGCR15RRHf+kAef5kFfzWNM2OgZJnS4BhaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.2_1691594120299_0.6406757515468138", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "f2ef102a90e579698a869e14617928537552ff78", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-opHrnsSZ4oa7y/iPilyjF5W8Mth4zx+1ud2pc4KMzl9eCYt0fgOzyQCje1oGfoVnAtympInygSQvXyj7MOIC+Q==", "signatures": [{"sig": "MEYCIQCeeJ5NDuBchBtBxoFOO3M1fqUcB2afSWykDUfL4BcxCwIhANYyuawp4Y4DZFGaDjoqCANCpxMQB8dBOjuAAFsjucqW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.3_1695740253738_0.672115242559786", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "b4d37d15b2879e7dea33626ddb8bda5dd5c1d126", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Dv4ZH7qODqg3Vba39c5LA7goVaN+p/KWjiyAlfPnFUzcMXnU4MHCFC6M3OkYlqqRz+HBCtj06oZYlBybLrip5Q==", "signatures": [{"sig": "MEUCIBDygLiaYzf0TzwWYEIh4DNku36me/8fuIodMqpMn8n1AiEA9Xo8GCkx2amDN/3za7fpMYWSEe824Jg9IkRnU2Hx9BQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.4_1697076406834_0.9066718323772593", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "26897708d8f42654ca4ce1b73e96140fbad879dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-wMHpNA4x2cIA32b/ci3AfwNgheiva2W0WUKWTK7vBHBhDKfPsc5cFGNWm69WBqpwd86u1qwZ9PWevKqm1A3yAw==", "signatures": [{"sig": "MEYCIQCzbsDnnIdZoOkUYxq/KoJqK5MYSUQfXbXHkR3RYiOQNQIhAJkA8TLCjpQlFiB+JJMzKutFg4l6kAHvzH61ChwBJ1+x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4004}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.23.3_1699513443754_0.7318175825303657", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "033925731b48140d2512d7d7f8fc1efbeb6ecdd1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-xB5bwNiVgIe5+/xKS5aCbx0/y/MTBdtOFcN+6/ZYiUlfQmJ8T/OxhMvW9JgSWGtlGrtFh0gmYAloiBY0LbSLIw==", "signatures": [{"sig": "MEUCIGRhC1znhlMqyf2QUjaoexIfNeMmzSrqIOa8HlPua5gkAiEAjXx1n93vlHwjUtRFbpTtx7HExrVvxCS1i0Khbj7HTPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.5_1702307979303_0.3749450083755892", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "9fc73e8536ea340e8e5fa19766c779257ba0f1fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-AXRY1ln6aczp2mJonB99YU/INpJhanKVC04kcmwpHEdV+yEwxDYEXdiJltArhwgkOvBdlTmF7c+JuDm7UbofBA==", "signatures": [{"sig": "MEUCIHYOy7OPJ282H3E9bRkG40zNKuXc0YUEBaQunPzNjtTjAiEAwn6R25Ts9dlRZPrjN27Pcn+ONA4gOKxh40+RM4DV45Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.6_1706285678366_0.5503074334837443", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "3566feabb7b8c70c810b6b9d1677fa3222c8ba94", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-GhKD3cQnx1KGkw2azFpfKHN1o8u8FuG6dEh7PQ3S5RasO/HVBhTC+pRfdzA670iq1ulAuDjzWv7304PxuVKLmQ==", "signatures": [{"sig": "MEQCIEA/E6kV00/AmbU3mI3QQSlgpxM2x4JVKRWtkmz+2mMqAiA4j0j7QyIJ29gWlFMEnqrDlRuJspP5SLiAfqYqEfFTqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.7_1709129139119_0.49361712914071587", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "57c3c191d68f998ac46b708380c1ce4d13536385", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-2A/94wgZgxfTsiLaQ2E36XAOdcZmGAaEEgVmxQWwZXWkGhvoHbaqXcKnU8zny4ycpu3vNqg0L/PcCiYtHtA13g==", "signatures": [{"sig": "MEYCIQCju7+rAffwtBLQzkhjVgvDE8cB/6ar1IxQ/pWmmgds9gIhAMh0rmwHTayNp4jCk4HxsXhAvaBhYBrYvYBUkz+yuP1N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3935}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.24.1_1710841748906_0.09787354500155443", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "39cc4b35254c6520874a43d58838fad6bc480904", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-gD82/SmiFObd97bC9RrbuO82vXBxmdgKOrCD06UL8Mlu6lN0yKjDn8e8u3+O9sP8tCsSser3Q4wYlEbBNd3SAw==", "signatures": [{"sig": "MEUCIQDSHP/cmPLL12vfgEJW6tiWCzcgfV+5eFX1y0PpZXOB0QIgIhWVQoLNXn8aQV0WKPilWkWJt6/3W3elmQHXHgxEgCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3756}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.8_1712236816811_0.4331608636129147", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "2001e7d87ed709eea145e0b65fb5f93c3c0e225b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-pssN6ExsvxaKU638qcWb81RrvvgZom3jDgU/r5xFZ7TONkZGFf4MhI2ltMb8OcQWhHyxgIavEU+hgqtbKOmsPA==", "signatures": [{"sig": "MEYCIQCwaRFE0D+AqalTGqXZpRaeKfxQhBctcwz38R5N6FFuJQIhAIW9AhvDmoiKbfAL8T3T6CfnktsTQwSyMVcC2kA/yQ3f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70277}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-create-regexp-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.24.6_1716553507799_0.6076839000966687", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "f14931c1dbbf08eff54becb1cad22fe6ae4442ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-g5lrjdMh3MPhwKybpmGrHYV3kpISdG/nVX5j8blJsBIo/dvElJM/iphCAusZMlKZsX2ML3ZeTk/QW6wVLVwKWg==", "signatures": [{"sig": "MEQCID0YbBS3y+0rv7iyjMkjxQrpMDZw4XDzxG+WyRW65THOAiAdbUBOqPW4L5gdgE5r5tgfgzMSZ6bMDgoBVUNX/JkhbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70409}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.9_1717423492087_0.04628787843396864", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "c2fd77adf36b623cba2f3c3eba45b05c5bca045c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-aFKxXNDWTxwb5oTr6VclZbwTemJ5vjx9092r8G+JQPplXbVgEYpJRDU+VsY8ZHxZmiAVxDWjlYXxoxgIdf6OTA==", "signatures": [{"sig": "MEUCIBth7v6Rvr/5Xm59K7eBlcFgxO+/t4HAFpbmD52ppAZjAiEAr2peaIGNZc6Z3WhzTfIMRIb5CjfFwDsoz2O0STB/g3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70417}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.10_1717500028422_0.13415630309290782", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "dfc3d4a51127108099b19817c0963be6a2adf19f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-hlQ96MBZSAXUq7ltkjtu3FJCCSMx/j629ns3hA3pXnBXjanNP0LHi+JpPeA81zaWgVK1VGH95Xuy7u0RyQ8kMg==", "signatures": [{"sig": "MEQCIB6Nd38N5ZUnv5AatmieLFoVLmmXMrJuBPg7kfiHFIuxAiASK9bkarYW5vQmkrBgBf6NEr4XCH6TTtQRkffaZfYeiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70273}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-create-regexp-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.24.7_1717593342438_0.9794705774238512", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "d3aa8195090e2a8c5ca78f65b69f2b346fb1a5cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-KY31d/dOPSq2AogyPZjVRv/qGrctr6AeJ5aOc7YLVspimo8Hwol9KEoeDcA9ozIppknz3G2iGgW6/jif9365Vg==", "signatures": [{"sig": "MEUCIQDSU5djQKeyLrk1H07D6kIg/SjpdbNaPRw5JsTCyNo97gIgQ4khKs0d1P92fV6fa1g5rU3l5TD9G2Y7hCP9GAr/qdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70306}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.11_1717751753380_0.6706816476629442", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "d65102ef856bd4db15954d437fa9e1bc10db3ae0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-8teprpmvPe4Ii2+qwPcksRnhkdn0ldgnlV8BZo0tlJXHAqrNORCY6FNTo/LJ81QyQFtR8qcNzNxWnPkEjPue6Q==", "signatures": [{"sig": "MEYCIQDyjOFCke+R+l62474CbyJi04zA3Hz5e2/IKs4V7tjX3gIhALXqQ7IB1XaVGr8vE1kSKrRhYG0872FK4PwC8jo0AUpd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67085}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.12_1722015227344_0.40442301444787", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "f93a93441baf61f713b6d5552aaa856bfab34809", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-8JKfg/hiuA3qXnlLx8qtv5HWRbgyFx2hMMtpDDuU2rTckpKkGu4ycK5yYHwuEa16/quXfoxHBIApEsNyMWnt0g==", "signatures": [{"sig": "MEYCIQCMK/5Gxr9Y7oqkMnlOjsQ2i2Gi2DwLAtF7X95jp2lJDgIhANqwSdWKAnPW4fHEMmHRQCIGheHPy72Df4eDGoVuL9ol", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74794}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-regexp-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.25.7_1727882114334_0.34823349978942963", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "5eae747fe39eacf13a8bd006a4fb0b5d1fa5e9b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-yoxstj7Rg9dlNn9UQxzk4fcNivwv4nUYz7fYXBaKxvw/lnmPuOm/ikoELygbYq68Bls3D/D+NBPHiLwZdZZ4HA==", "signatures": [{"sig": "MEUCIQDF5TEVRQceiS4v8AJYhaMxsxlvQwrk290kIKR1yJR+7gIgaPgtzbfTxDTwi3voUUOdtpnLl1kgw6e8QbkE2C6Y1jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3934}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.25.9_1729610489688_0.9439875322762521", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "1e2cf66f6233e0afc3cff536375eea7e10ab8e7b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-D9hDtPyJP6yIC0xsxj8dvVt9U4IlU0PtLRwb5AUxuLSEbvojl1MNf+/ruMN8IYthQoaBOf5izQ/xE0qOwsVVnw==", "signatures": [{"sig": "MEQCICj5gXwWiGMvQdhxAO7KB5FKDumD8ScaeXETTCzILKeNAiB7f0Kx2ThQBGyn6reQvX/y543w58EeUTMge4qYCLPHYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4095}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.13_1729864469731_0.3693442902326658", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "9e17df4637e356945ae87547fd9457e31eee7fd8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-JCUgSaEyDCRp0ibJrNGRPrQEytxq1t+ejw7gsyFvy03D27JrWVJWh8jOBpyXVBqZmS++ToknjFiGvf9cIFPFiA==", "signatures": [{"sig": "MEUCIBNxezqvjZGl7yk/FbXLvXMuVLKSaKZwXdijE8biPIB2AiEA3aYkaonjvH5LWjWcNYz0Go/0haiQUVigyoY8tPkbUzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4095}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.14_1733504060281_0.5312155650129193", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "77af5cdd2124d4ca843afca0b03f3403ceba4446", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-A7FV6QBkl33R9bAJNeQ4Cow1t++HmpAdXtzdYWcPWsq8DFwYo7L7qDtgv4LRWgVXvDBwrZoeJfW92LnK32Y/Ow==", "signatures": [{"sig": "MEQCIG75ZM8vNH15uUUsHgQ+oxdtXo47cVazy+QMv7XqeIGjAiBd9zXJ1CgxMvccsDAurys/e+HkMlI6evz1eIqaF/WRFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4095}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.15_1736529887598_0.4754428797414698", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "c294bb6f6ee5f234e8b387953e5ca897f0774b1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-+EAWuxydnCvjk0uklcAJrZN56Z1fsQqVR1oDQ8NumeRtPZahWsJBQrt36pTOnz61ErGfwMmmX9lcZp8y0sA3nQ==", "signatures": [{"sig": "MEYCIQC4KKLDGnCes8B6CKDIHvfwvYMXUBM5sOTbZ2lymqyHkQIhAJ4ssLqG6iJRsp1tF4jKhw1Sab2kymLQddQZbykzottL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4095}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.16_1739534363617_0.8328194969897948", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "88ac4a1d150cbdad5a613d136439dd9eac5b7756", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-Oyvp8bHbpvou6WrLK68ofpHcFeREATDmWTQoVegbgIZBnNLdIZrasfCd9PDrmLnUfFjd1WQpeC54aEcF4zFrxg==", "signatures": [{"sig": "MEYCIQCLZpR5oBZZ5DsHi11VcCzxRUWPZXXL9DcX/UiEZecA1QIhAMV8mYsgD04pBZ9TUPQwQnnMK74iCncLRQt3Kwz4bBOp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4095}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-alpha.17_1741717517009_0.777472132463124", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "25948f5c395db15f609028e370667ed8bae9af97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-xvINq24TRojDuyt6JGtHmkVkrfVV3FPT16uytxImLeBZqW3/H52yN+kM1MGuyPkIQxrzKwPHs5U/MP3qKyzkGw==", "signatures": [{"sig": "MEYCIQC4bJ9dxekhFqhrYnctuRLFCDAZlc5WuhT7aq3k71l5owIhAPPCx8+a5E0Q8gzvWeM2wE3e9Yl2K0GIdBjz5/xCF0AS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3934}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_7.27.1_1746025753217_0.04991864784885203", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "dist": {"shasum": "384daafaba43fa9a59ddd6ad450dffe9ec8e5a73", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-dNKf7f7v/Ilv+J1jR3mPslgRzEKajJEmZs9v9ZtN6WVomCTw1bfAWxDpPnr32ZPh5yDN1KMKQagrWDXym8PfVA==", "signatures": [{"sig": "MEUCIEdVUAUKAf0xzGdj7Q9hjEl2k0qhpNqEEz55ouNP9Vu/AiEAxgLI6HoCnGB/2ccF91td0jD8oc4msE7EhS1o9/3Ol3k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4069}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-regex_8.0.0-beta.0_1748620289524_0.6839982939572748", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-unicode-regex", "version": "8.0.0-beta.1", "description": "Compile ES2015 Unicode regex to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-unicode-regex@8.0.0-beta.1", "dist": {"shasum": "30fcfe0c476505c33b81571e6401f860b26dd48d", "integrity": "sha512-MwGkJsIYo3rkTD3u+4QIctcOvDvPBDbt8H5qucrFp6wV9+Bf0NhrT6tIhbXW5LYKHc7OTJo1yV09P6wCmcaHng==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDAgCVMTk39sP59p5QPUBlpqME4uDOt5IafY/aVymMQLAiEA/6JhJnVN/dxne+Q4o4UkBD+v1IiqBobFtxfw1ftMcs0="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-unicode-regex_8.0.0-beta.1_1751447073810_0.9288491820733367"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:32.758Z", "modified": "2025-07-02T09:04:34.166Z", "7.0.0-beta.4": "2017-10-30T18:35:32.758Z", "7.0.0-beta.5": "2017-10-30T20:57:10.341Z", "7.0.0-beta.31": "2017-11-03T20:03:59.825Z", "7.0.0-beta.32": "2017-11-12T13:33:45.343Z", "7.0.0-beta.33": "2017-12-01T14:28:56.356Z", "7.0.0-beta.34": "2017-12-02T14:39:54.455Z", "7.0.0-beta.35": "2017-12-14T21:48:11.979Z", "7.0.0-beta.36": "2017-12-25T19:05:16.589Z", "7.0.0-beta.37": "2018-01-08T16:02:56.420Z", "7.0.0-beta.38": "2018-01-17T16:32:25.556Z", "7.0.0-beta.39": "2018-01-30T20:27:53.211Z", "7.0.0-beta.40": "2018-02-12T16:42:14.788Z", "7.0.0-beta.41": "2018-03-14T16:26:19.623Z", "7.0.0-beta.42": "2018-03-15T20:50:52.558Z", "7.0.0-beta.43": "2018-04-02T16:48:31.801Z", "7.0.0-beta.44": "2018-04-02T22:20:13.533Z", "7.0.0-beta.45": "2018-04-23T01:57:11.350Z", "7.0.0-beta.46": "2018-04-23T04:31:34.524Z", "7.0.0-beta.47": "2018-05-15T00:09:27.905Z", "7.0.0-beta.48": "2018-05-24T19:23:04.850Z", "7.0.0-beta.49": "2018-05-25T16:02:36.967Z", "7.0.0-beta.50": "2018-06-12T19:47:25.615Z", "7.0.0-beta.51": "2018-06-12T21:20:04.136Z", "7.0.0-beta.52": "2018-07-06T00:59:30.094Z", "7.0.0-beta.53": "2018-07-11T13:40:20.770Z", "7.0.0-beta.54": "2018-07-16T18:00:11.204Z", "7.0.0-beta.55": "2018-07-28T22:07:26.950Z", "7.0.0-beta.56": "2018-08-04T01:06:19.680Z", "7.0.0-rc.0": "2018-08-09T15:58:41.679Z", "7.0.0-rc.1": "2018-08-09T20:08:22.745Z", "7.0.0-rc.2": "2018-08-21T19:24:27.520Z", "7.0.0-rc.3": "2018-08-24T18:08:21.868Z", "7.0.0-rc.4": "2018-08-27T16:44:35.851Z", "7.0.0": "2018-08-27T21:43:32.876Z", "7.2.0": "2018-12-03T19:01:56.663Z", "7.4.3": "2019-04-02T19:56:16.246Z", "7.4.4": "2019-04-26T21:04:37.508Z", "7.6.2": "2019-09-23T21:21:35.264Z", "7.7.0": "2019-11-05T10:53:21.107Z", "7.7.4": "2019-11-22T23:32:36.900Z", "7.8.0": "2020-01-12T00:16:59.193Z", "7.8.3": "2020-01-13T21:42:11.858Z", "7.10.1": "2020-05-27T22:08:07.287Z", "7.10.4": "2020-06-30T13:12:46.210Z", "7.12.1": "2020-10-15T22:41:17.953Z", "7.12.13": "2021-02-03T01:11:39.622Z", "7.14.5": "2021-06-09T23:12:58.273Z", "7.16.0": "2021-10-29T23:47:51.744Z", "7.16.5": "2021-12-13T22:28:34.653Z", "7.16.7": "2021-12-31T00:22:48.572Z", "7.18.6": "2022-06-27T19:50:27.879Z", "7.21.4-esm": "2023-04-04T14:09:51.455Z", "7.21.4-esm.1": "2023-04-04T14:21:49.107Z", "7.21.4-esm.2": "2023-04-04T14:39:47.881Z", "7.21.4-esm.3": "2023-04-04T14:56:35.819Z", "7.21.4-esm.4": "2023-04-04T15:13:44.962Z", "7.22.5": "2023-06-08T18:21:39.801Z", "8.0.0-alpha.0": "2023-07-20T14:00:24.786Z", "8.0.0-alpha.1": "2023-07-24T17:52:57.523Z", "8.0.0-alpha.2": "2023-08-09T15:15:20.435Z", "8.0.0-alpha.3": "2023-09-26T14:57:34.085Z", "8.0.0-alpha.4": "2023-10-12T02:06:47.058Z", "7.23.3": "2023-11-09T07:04:03.935Z", "8.0.0-alpha.5": "2023-12-11T15:19:39.461Z", "8.0.0-alpha.6": "2024-01-26T16:14:38.509Z", "8.0.0-alpha.7": "2024-02-28T14:05:39.330Z", "7.24.1": "2024-03-19T09:49:09.061Z", "8.0.0-alpha.8": "2024-04-04T13:20:17.008Z", "7.24.6": "2024-05-24T12:25:07.985Z", "8.0.0-alpha.9": "2024-06-03T14:04:52.248Z", "8.0.0-alpha.10": "2024-06-04T11:20:28.573Z", "7.24.7": "2024-06-05T13:15:42.600Z", "8.0.0-alpha.11": "2024-06-07T09:15:53.535Z", "8.0.0-alpha.12": "2024-07-26T17:33:47.514Z", "7.25.7": "2024-10-02T15:15:14.578Z", "7.25.9": "2024-10-22T15:21:29.865Z", "8.0.0-alpha.13": "2024-10-25T13:54:29.904Z", "8.0.0-alpha.14": "2024-12-06T16:54:20.480Z", "8.0.0-alpha.15": "2025-01-10T17:24:47.797Z", "8.0.0-alpha.16": "2025-02-14T11:59:23.798Z", "8.0.0-alpha.17": "2025-03-11T18:25:17.166Z", "7.27.1": "2025-04-30T15:09:13.405Z", "8.0.0-beta.0": "2025-05-30T15:51:29.698Z", "8.0.0-beta.1": "2025-07-02T09:04:33.966Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-regex", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-regex"}, "description": "Compile ES2015 Unicode regex to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}