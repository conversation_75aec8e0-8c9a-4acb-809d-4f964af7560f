{"_id": "strip-bom", "_rev": "24-ae529cff38ca2125cbe5e6d480cbe39a", "name": "strip-bom", "description": "Strip UTF-8 byte order mark (BOM) from a string", "dist-tags": {"latest": "5.0.0"}, "versions": {"0.1.0": {"name": "strip-bom", "version": "0.1.0", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-bom"}, "bin": {"strip-bom": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["cli.js", "index.js"], "keywords": ["cli", "bin", "app", "bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "trim", "text", "buffer", "string"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@0.1.0", "dist": {"shasum": "c2b6bd5a206ba5caa0ab2a8f5ac2f686264ab072", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-0.1.0.tgz", "integrity": "sha512-ye0e/W8SV/6YFAOL6qx0eJUjksx8pNyuR+CCEumrGelIC7AG/Jdyo0f6DodUVUweyVG2ExDtvoNYQPP8Buc14Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuroVqXU39AXfQAdvmQz+U9+owjH++cVvg8WOLx5VeYAIgV0SuwnHvL5tkC0z8YKB7KsO2C3QPvq9I8sMb2TguOno="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "strip-bom", "version": "0.2.0", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-bom"}, "bin": {"strip-bom": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["cli.js", "index.js"], "keywords": ["cli", "bin", "app", "bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "trim", "text", "buffer", "string"], "dependencies": {"is-utf8": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@0.2.0", "_shasum": "8fea75e2715a8e7c9e0774c7d43c307e7e9eef33", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8fea75e2715a8e7c9e0774c7d43c307e7e9eef33", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-0.2.0.tgz", "integrity": "sha512-lEhKKx7afewJnGrdfyOtp330p4tH7d4Qyse/M6nQ2StmFzf79EoJuy/bhl2zuQpmqSK+QMlouTf39dN54cllfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKsTM5GncW6SA3bPtkBEbV3SocBPtAPbmd97uwMAbdgwIhAKuDXiXanh7w23UBiN3p39muTvEYaYfUiX2ypKyqJODq"}]}, "directories": {}}, "0.2.1": {"name": "strip-bom", "version": "0.2.1", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-bom"}, "bin": {"strip-bom": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["cli.js", "index.js"], "keywords": ["cli", "bin", "app", "bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "trim", "text", "buffer", "string"], "dependencies": {"is-utf8": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@0.2.1", "_shasum": "bfa91b4e4322c7ae23147cd8ab76b46c59273774", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bfa91b4e4322c7ae23147cd8ab76b46c59273774", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-0.2.1.tgz", "integrity": "sha512-XgQNVEct7SIPIs92qkBwnUmq/jC8nM76vcAizk4AbmLcZ6MGNZxKAUrgmlebiFhKjoFLuo6ZHqPVRv/LFQJz/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfyXso5g/zvTt2uibrKiPJexu1yIF4bAkNT3nDyc436AiEAr0cxczgycw4Ei9PuqnpbO1dgyO20fZuGTcABVTiZBkY="}]}, "directories": {}}, "0.3.0": {"name": "strip-bom", "version": "0.3.0", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer/stream", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-bom"}, "bin": {"strip-bom": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["cli.js", "index.js"], "keywords": ["cli", "bin", "app", "bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "trim", "text", "buffer", "string", "stream", "streams"], "dependencies": {"is-utf8": "^0.2.0", "through2": "^0.4.1"}, "devDependencies": {"concat-stream": "^1.4.5", "mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@0.3.0", "_shasum": "4a6920a8848ccc50369c3a535d809fddab4291b8", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4a6920a8848ccc50369c3a535d809fddab4291b8", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-0.3.0.tgz", "integrity": "sha512-qV+d7lr18Yx3tvwp8kPphbI740Gtyb2oRMwGBkf7GFLTo8wiDxOv8xKj0kGiZNDvzlYG9+eYhYWeWrJKQgEKTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzVkYCaIhInCs6dDGnmaU/T4ufEXkfs3fFvMtE671iDAIgXfIjBs7d46U+hjlPAFdjXgyqsL3OR0xx++Yjlb3az5I="}]}, "directories": {}}, "0.3.1": {"name": "strip-bom", "version": "0.3.1", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer/stream", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-bom"}, "bin": {"strip-bom": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["cli.js", "index.js"], "keywords": ["cli", "bin", "app", "bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "trim", "text", "buffer", "string", "stream", "streams"], "dependencies": {"first-chunk-stream": "^0.1.0", "is-utf8": "^0.2.0"}, "devDependencies": {"concat-stream": "^1.4.5", "mocha": "*"}, "gitHead": "bac8b80dceb94ce6d544dfc1234e755c779f9829", "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@0.3.1", "_shasum": "9e8a39eff456ff9abc2f059f5f2225bb0f3f7ca5", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9e8a39eff456ff9abc2f059f5f2225bb0f3f7ca5", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-0.3.1.tgz", "integrity": "sha512-8m24eJUyKXllSCydAwFVbr4QRZrRb82T2QfwtbO9gTLWhWIOxoDEZESzCGMgperFNyLhly6SDOs+LPH6/seBfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICZmV+Yszu3UHI3nNrCliB9AGlaiZa8RxQLOJ8glYihZAiAY93xkx/I+uKlQXxFPR4o2AXr7hRH3ZGJrPS59k9hx9A=="}]}, "directories": {}}, "1.0.0": {"name": "strip-bom", "version": "1.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer/stream", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-bom"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-bom": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["cli.js", "index.js"], "keywords": ["cli", "bin", "app", "bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "trim", "text", "buffer", "string", "stream", "streams"], "dependencies": {"first-chunk-stream": "^1.0.0", "is-utf8": "^0.2.0"}, "devDependencies": {"concat-stream": "^1.4.5", "mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@1.0.0", "_shasum": "85b8862f3844b5a6d5ec8467a93598173a36f794", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "85b8862f3844b5a6d5ec8467a93598173a36f794", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-1.0.0.tgz", "integrity": "sha512-qVAeAIjblKDp/8Cd0tJdxpe3Iq/HooI7En98alEaMbz4Wedlrcj3WI72dDQSrziRW5IQ0zeBo3JXsmS8RcS9jg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCpNBXZvMeN4O5pR2bkNM5I4FNcDYn3zfqj5X2TE3UDQIgUzX9xK0vPz+bxUDcxG8HwIr5scXbPIWXvDvjTDWphZ0="}]}, "directories": {}}, "2.0.0": {"name": "strip-bom", "version": "2.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/strip-bom"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "buffer", "string"], "dependencies": {"is-utf8": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "851b9c126dba9561cc14ef3dc2634dcc11df4d11", "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom", "_id": "strip-bom@2.0.0", "_shasum": "6219a85616520491f35788bdbf1447a99c7e6b0e", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6219a85616520491f35788bdbf1447a99c7e6b0e", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha512-kwrX1y7czp1E69n2ajbG65mIo9dqvJ+8aBQXOGVxqwvNbsXdFM6Lq37dLAY3mknUwru8CfcCbfOLL/gMo+fi3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG0y8MKgOsZkVV9Gy5OoDeP6E0xYEo73Lo6ueWragimqAiEAgBVxR22HXF65YUaQkRJ7eSqrZvz3KyWU0xqlBDbzpfg="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "strip-bom", "version": "3.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "8258d09a069a5d5eb3d787c1d5d29737df1c8bba", "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom#readme", "_id": "strip-bom@3.0.0", "_shasum": "2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkFKlpgi9gLzmNBdwey1xMNr5R9MWOuoQHI8CHqnvmJgIgV0povZEWmRzDkqA7Uys6EatbfC+13hv/uSjoJURzYtQ="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/strip-bom-3.0.0.tgz_1462032162626_0.6434765527956188"}, "directories": {}}, "4.0.0": {"name": "strip-bom", "version": "4.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "9aef0f38ffefca91d3852c82cd6366e5d36d6dd1", "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom#readme", "_id": "strip-bom@4.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==", "shasum": "9c3505c1db45bcedca3d9cf7a16f5c5aa3901878", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxS7QCRA9TVsSAnZWagAAltAP/0OSO7TakarHgVg+/3Vp\nSLbZjnE6pEPfkgOvjv3PjQa0WSYsPnGefSzhvzkprCXeXVHxdUhzLjn2JUMR\n+w939La+AH+Bqa6NQQlwaey8bFnn3oHjNsGoX/Y56yB+5a55pCyyu93PX/gn\nsoOxIiatgXBLcSBXR75bY3uY+tWQLm7WSGTkKMUi9CSFz2E6mkahZybKw7ra\nXzYOrIrTAdmF7BVpy3j9sZR8wbVg5BwyuJGU+C5o5MWqhhRED03kOhvEN3Y/\nF5N1CnU+XF9tDWLEAyL72MemMsRJucJg5uXGnPOfS0tR/EhiVg8QsiOzdith\ndgyPDrNvT5vidNOH1/dqGVl07AAoD64oYTHUOwY66GDFu42wmVBXntNmG6ap\n9AZz2ZdSMROj8xk0fJ7iQ6GzIDYK2L1VsGDwRSVAnYO+02ByeKwYSYoSBArH\ni71gXsaGlAwR/z4M96OdoXvLvHLWLzit+pzs4u0Npl8V75yu0x2Kn5oYEJs6\n2F8ioexslHnPuM+sxykcZlUF1bwDGO+CXOXUoKkA4Vo48yYYmM9LZv90c08v\nQ8fnyw3S5/db00wiK4KYhMZ6UJSGUYU53C1LvRxiUql+mi4Mj1iftUiTGU9E\ngmp9B4JVg0As3g9rm+qfZSd7ZhW/hpw+yJX3VvlUXfvD81ruq8Wo2d8jDmAd\nCkL4\r\n=w+v7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxeXCU7e9yB2fKk+sLB0au+KCalj1+WD7OM407sY4PswIhAKAYnWRbt0iuBXPCYD/vPrQtUotFkL6UFe3MUaywRY09"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-bom_4.0.0_1556426447769_0.5103805168308455"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "strip-bom", "version": "5.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "b80d7bc94e79b4744d92a2dc6328c91d9afe9775", "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "homepage": "https://github.com/sindresorhus/strip-bom#readme", "_id": "strip-bom@5.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-p+byADHF7SzEcVnLvc/r3uognM1hUhObuHXxJcgLCfD194XAkaLbjq3Wzb0N5G2tgIjH0dgT708Z51QxMeu60A==", "shasum": "88d2e135d154dca7a5e06b4a4ba9653b6bdc0dd2", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-5.0.0.tgz", "fileCount": 5, "unpackedSize": 3632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJged1bCRA9TVsSAnZWagAAebMQAImw4DeC+0Y/gjZvHdtq\nnsi6SKLJk4hqil718I2FK+1iEIuRBK1XDqnCMfIuXMGY9lRW3FQZZcHZ05ly\n4Li7IFXwL7+xltFhMmfQQNLV384NVeZoJFHeIkkWO512ZSJIBObd7PyRXPEy\nqVmwBYU5iAHpHRycjljchCMSHRpdoPL3+0H+SqkuvkaTDs1KmkmXLIv7xvkp\n33WUuEHHli7m9NLUilHLF5gPZMLzmECZVdBQgOpbo3eq54lEoS/i2P1c7AGu\nc0CoJnEJgFE5q3rao+jtIebVDDXYT2osVyZ11ut0SvmyttdjKDlI8W23lDBL\nDXNiexrav8Nz3HaWXrKueytMz4RNkds5kzV+2Ir48VL9rQv/M5ILq5bhbbsX\nk/lKjrSRF3lU0ZNEJ9gqXEGopItNLCtGzdq6jRBcqA61q0/iDOjTr7LOKCrJ\n9FAybOzJbqAePYIlKclzCEhGDScJLBr14IePp9vWt/xeANlJh9LLqVeB6WGy\nSl1iCPJAiRh0Jcv/heDZlcjT/4e/quHBiDe4lNv7IVl7ZikFIx96M8TJrJBq\njZcgR3YrNuFDusHIpk/e4FwgyrNz8z7b4C5L1vVFDnOmB8y/Rnz4dMPzTkt+\nOKJT3GeD/NZn5FzgWu1vVOjljKvXX2oN19apohEUhIG062vUPsdkNA9XDgLg\nhQNm\r\n=jKa7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJB+y1O8wKeJIDldi9t6arU90U0zbwuTTbtoVRVcGw5AiAH5nFSRlFiWo85QzoZyEgxiXaYG8mMujBthFmvPJ/beQ=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-bom_5.0.0_1618599259095_0.9730470456443971"}, "_hasShrinkwrap": false}}, "readme": "# strip-bom\n\n> Strip UTF-8 [byte order mark](https://en.wikipedia.org/wiki/Byte_order_mark#UTF-8) (BOM) from a string\n\nFrom Wikipedia:\n\n> The Unicode Standard permits the BOM in UTF-8, but does not require nor recommend its use. Byte order has no meaning in UTF-8.\n\n## Install\n\n```\n$ npm install strip-bom\n```\n\n## Usage\n\n```js\nimport stripBom from 'strip-bom';\n\nstripBom('\\uFEFFunicorn');\n//=> 'unicorn'\n```\n\n## Related\n\n- [strip-bom-cli](https://github.com/sindresorhus/strip-bom-cli) - CLI for this module\n- [strip-bom-buf](https://github.com/sindresorhus/strip-bom-buf) - Buffer version of this module\n- [strip-bom-stream](https://github.com/sindresorhus/strip-bom-stream) - Stream version of this module\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-strip-bom?utm_source=npm-strip-bom&utm_medium=referral&utm_campaign=readme\">Get professional support for 'strip-bom' with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:14:14.322Z", "created": "2014-04-04T16:23:07.367Z", "0.1.0": "2014-04-04T16:23:07.367Z", "0.2.0": "2014-05-12T09:31:30.521Z", "0.2.1": "2014-05-14T17:40:36.329Z", "0.3.0": "2014-05-14T21:38:20.253Z", "0.3.1": "2014-05-25T16:07:52.046Z", "1.0.0": "2014-08-22T13:03:42.521Z", "2.0.0": "2015-06-29T13:13:31.964Z", "3.0.0": "2016-04-30T16:02:44.907Z", "4.0.0": "2019-04-28T04:40:47.887Z", "5.0.0": "2021-04-16T18:54:19.266Z"}, "homepage": "https://github.com/sindresorhus/strip-bom#readme", "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"mattecapu": true, "andr": true, "terrychan": true, "sopepos": true, "flumpus-dev": true}}