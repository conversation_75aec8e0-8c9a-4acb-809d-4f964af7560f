{"name": "execa", "dist-tags": {"next": "2.0.0-alpha.0", "latest": "9.6.0"}, "versions": {"0.1.0": {"name": "execa", "version": "0.1.0", "dependencies": {"strip-eof": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "2.0.1"}, "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "4aa2ee98eaeb921a394aeca1e0966e50765b8053", "tarball": "https://registry.npmjs.org/execa/-/execa-0.1.0.tgz", "integrity": "sha512-83wxT8ms6ThmP+C3tbEf8vDKdi7xL+4zNEkYsEm6eF/9vjPwHMX91Oji9XZ3KGXbTv4W0OwRQ7n3F22X4APvXA==", "signatures": [{"sig": "MEYCIQDRA6zyJDJriIpWb2QmAXC5zIPGrNfXdsE+dkIUXKhhlwIhAN+HhLN/+0TTo9L5A4/nasSG1QoZJSvVjdViZrlnZcOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.1.1": {"name": "execa", "version": "0.1.1", "dependencies": {"strip-eof": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "b09c2a9309bc0ef0501479472db3180f8d4c3edd", "tarball": "https://registry.npmjs.org/execa/-/execa-0.1.1.tgz", "integrity": "sha512-UnD+pXMpCodPQoDLxea4c/tLdAFsd/863M7eBfFrzQwrAMx42BYDurkmB3FsLNEdMZ1htfkI9zpx8r2V9m5ogg==", "signatures": [{"sig": "MEUCIQD+qeVUalQz0nl3zRwAFbxUrbtUyjnOmk2ef+155777TgIgdaIYELk48GOao+wHgJxwZHqlHSSMF1/O/wLb5oCbhVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.2.0": {"name": "execa", "version": "0.2.0", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "cat-names": "^1.0.2"}, "dist": {"shasum": "3178cf42a19d63726f2731e30239007c19f5bf4d", "tarball": "https://registry.npmjs.org/execa/-/execa-0.2.0.tgz", "integrity": "sha512-EkjFWks0mOv3IPcrCszeG5Tuboyy54KMrg0YjldoxVDBPPQkMNzDpemyJ6uEKHn+pOv71m8e3RDeUUwxdJlA8w==", "signatures": [{"sig": "MEUCIFP9KhzPOIo4aXZNnkblfESxHB36s5CuwsnyeJQpwgNtAiEAq/PmYlBQXYVmSCMVHOBTgrapNzdePrhUosbV6b4J3DQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.2.1": {"name": "execa", "version": "0.2.1", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "cat-names": "^1.0.2"}, "dist": {"shasum": "14d4f6eb9c9264220da410be73b9bdba69c90d19", "tarball": "https://registry.npmjs.org/execa/-/execa-0.2.1.tgz", "integrity": "sha512-m3/FSRwd5ZwcWud4ElGMqH+tZOaAUasTvnfagm4tPM0DPwIzZ2ZXQ3cx2nkXus2+6OuS6KztYIOGw1e4hnXqYw==", "signatures": [{"sig": "MEUCIQDUiK3TgPnvACXWQ8/RkKhTs1AXzFBEo7oVcbvJWMtkYAIgWxpRrRGWcZq9D7Tz0ruBsAm98pceh5tXbe1grV6OKTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.2.2": {"name": "execa", "version": "0.2.2", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "cat-names": "^1.0.2"}, "dist": {"shasum": "e2ead472c2c31aad6f73f1ac956eef45e12320cb", "tarball": "https://registry.npmjs.org/execa/-/execa-0.2.2.tgz", "integrity": "sha512-zmBGzLd3nhA/NB9P7VLoceAO6vyYPftvl809Vjwe5U2fYI9tYWbeKqP3wZlAw9WS+znnkogf/bhSU+Gcn2NbkQ==", "signatures": [{"sig": "MEUCIAVQoSREmaDRHy7XfkqTmDOz1MbLJDqQ3AASMhrJA0uyAiEAgHxwR4HQOV3Mu2+PRu6FapKn7wrrmnr4uNxBoR8gUOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.3.0": {"name": "execa", "version": "0.3.0", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^6.4.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "get-stream": "^2.0.0"}, "dist": {"shasum": "a144cf33f2c1b69d4c0e29fa4c5afc956346e24c", "tarball": "https://registry.npmjs.org/execa/-/execa-0.3.0.tgz", "integrity": "sha512-7evPG+NsML0PuUTKprHnSDKBZXfouIuSLcc5WqJxnKBVpLMwrOZNPssiZIUHE6S7mW2pD+aPuVg4MNUcnqIwnA==", "signatures": [{"sig": "MEQCIDaFg8Pny1XZeHVOytdB8uy9FjCv+5HYjEn2qjQycPF3AiB7w9JRg1TFHtJOmf2I9gqS23yaR6qgeOrH5P0CAcv1rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.4.0": {"name": "execa", "version": "0.4.0", "dependencies": {"path-key": "^1.0.0", "is-stream": "^1.1.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^6.4.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "get-stream": "^2.0.0"}, "dist": {"shasum": "4eb6467a36a095fabb2970ff9d5e3fb7bce6ebc3", "tarball": "https://registry.npmjs.org/execa/-/execa-0.4.0.tgz", "integrity": "sha512-QPexBaNjeOjyiZ47q0FCukTO1kX3F+HMM0EWpnxXddcr3MZtElILMkz9Y38nmSZtp03+ZiSRMffrKWBPOIoSIg==", "signatures": [{"sig": "MEUCIQDudxHuiJP5mUijRZo+NwzUcSmFMCrEL823DK2n4xYrZAIgK0ooBmOCRS8CI/3Tq6BGiLPZ+cPkF4TGclrnBNqBigw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "0.5.0": {"name": "execa", "version": "0.5.0", "dependencies": {"is-stream": "^1.1.0", "strip-eof": "^1.0.0", "get-stream": "^2.2.0", "cross-spawn": "^4.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^8.3.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "a57456764b990e3e52f6eff7f17a9cc2ff2e7ccc", "tarball": "https://registry.npmjs.org/execa/-/execa-0.5.0.tgz", "integrity": "sha512-wtvDYrVKSSUfxb6jwgFNX8RhtE34ErP9s9ychsBAJQB5+vlpcKHMg0ea5ewQYEwXKDnQ2MkJyHLh5BqORap1XA==", "signatures": [{"sig": "MEYCIQC5HB6BnwoKnm/dKtXYIRVlFKuu7/Jmv64k1zal/kgMfwIhAOrwpX1M0p4Mzrx06kRJkldp4QVXlYCG8yP6UBbHbOfk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.5.1": {"name": "execa", "version": "0.5.1", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^2.2.0", "cross-spawn": "^4.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^8.3.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "de3fb85cb8d6e91c85bcbceb164581785cb57b36", "tarball": "https://registry.npmjs.org/execa/-/execa-0.5.1.tgz", "integrity": "sha512-R66dW/hW3I8yV77Wg4xn6zMguRPUgt59VLm5e85NrOF05ZdPn7YOfPBSw0E9epJDvuzwVWEG+HmEaQ4muYuWKQ==", "signatures": [{"sig": "MEQCIGUEPVPF6LMUrU2r2iALEZrIpWUSnOvPSgeWgXzlWkJEAiAnHO1Obp8jrcF6PSewRQSNLptwme6H7TIlo2wIQeR9zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.6.0": {"name": "execa", "version": "0.6.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "934fc9f04a9febb4d4b449d976e92cfd95ef4f6e", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.0.tgz", "integrity": "sha512-U6/rTP/MZk7s9cWW9mBADmnsoFfr1xh4zKMhEpIFxkL9xy0Mf7gQ6Frdh1fZs4mCQ1+uTFnZXAGyNKrUaG9/xA==", "signatures": [{"sig": "MEUCIQCsdwbrbq8Lm3OA78yxirpDuiGMIGvpVfwneKyk6kmqvQIgUzaJlXl1vn/EwON97EgReNSAF24HGmXQrQwdOysrRUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.6.1": {"name": "execa", "version": "0.6.1", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "79eda42ade78c387718b0aad48e0f573b5525cde", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.1.tgz", "integrity": "sha512-K/iPrbzE77EgAv5oc7yDOIMaEsdfOzW+OOthWTA0gaxrU0Ht8ropx1iwbkzwSmEAuLuxuRW34MhsVJDZi2sZzA==", "signatures": [{"sig": "MEQCIB6/BddzgUudJwmtw0a+fCFv7t+icGTL8wzNqpGT8FbTAiBwfscQL1LYfC/Zitl5MWIRtkGzcnvA23M3g582wQlNsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.6.2": {"name": "execa", "version": "0.6.2", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "04e9e38dec6b8e770cf0fb6cf7ef945260c67bbb", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.2.tgz", "integrity": "sha512-hToO3Nj6/4Kt1Smql/8BX1ZF7l+2apBz8DFSa/bZ20uXWcEYIk2/4UshrvU328gUo0nx7habO4MvdBll3UqeQw==", "signatures": [{"sig": "MEUCIQCjwzcnzIPo1wAToep+kKT4DhjkPVX3PR7L2pBX7XtauwIgSrRoYUFQwnFEI7BMZEf7HnokxyJIdg4+/e+lvhYGy5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.6.3": {"name": "execa", "version": "0.6.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "57b69a594f081759c69e5370f0d17b9cb11658fe", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.3.tgz", "integrity": "sha512-/teX3MDLFBdYUhRk8WCBYboIMUmqeizu0m9Z3YF3JWrbEh/SlZg00vLJSaAGWw3wrZ9tE0buNw79eaAPYhUuvg==", "signatures": [{"sig": "MEQCIBFa7Grkbh7FZP1CJkJt9FSuijfSZ2UwA8notKG6yejNAiAJSWilOXZDGiljBbiexWfaXRo4JIloef0GO4cYu2iC9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.7.0": {"name": "execa", "version": "0.7.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "944becd34cc41ee32a63a9faf27ad5a65fc59777", "tarball": "https://registry.npmjs.org/execa/-/execa-0.7.0.tgz", "integrity": "sha512-RztN09XglpYI7aBBrJCPW95jEH7YF1UEPOoX9yDhUTPdp7mK+CQvnLTuD10BNXZ3byLTu2uehZ8EcKT/4CGiFw==", "signatures": [{"sig": "MEQCIBDnforPODYtZLUoigWoHvhlnEl/6If6vWkOAF0D2viVAiBG/DYW1VO4g1IhTuRmZB8GpDB9DGF4MCnBHg2Nc0t6jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.8.0": {"name": "execa", "version": "0.8.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "d8d76bbc1b55217ed190fd6dd49d3c774ecfc8da", "tarball": "https://registry.npmjs.org/execa/-/execa-0.8.0.tgz", "integrity": "sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA==", "signatures": [{"sig": "MEQCIEvhknIVDwvs9eo7+ys/lJwfNAm4wRm9+v9xQfGNkZhQAiAfBg6l9jymVKiW6wkgkMIrYjmntN1iHjvoPBIBkNXfUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.9.0": {"name": "execa", "version": "0.9.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "adb7ce62cf985071f60580deb4a88b9e34712d01", "tarball": "https://registry.npmjs.org/execa/-/execa-0.9.0.tgz", "integrity": "sha512-BbUMBiX4hqiHZUA5+JujIjNb6TyAlp2D5KLheMjMluwOuzcnylDL4AxZYLLn1n2AGB49eSWwyKvvEQoRpnAtmA==", "signatures": [{"sig": "MEQCIE1v1tOAzLcbVMuT+2weviApdz8tpLrCa8/YGmKp2vTOAiBQKEiRxAEbgC5xlN26rWVKU9t+/C24GI4rDPymqK8Tdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "0.10.0": {"name": "execa", "version": "0.10.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^6.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "dist": {"shasum": "ff456a8f53f90f8eccc71a96d11bdfc7f082cb50", "tarball": "https://registry.npmjs.org/execa/-/execa-0.10.0.tgz", "fileCount": 6, "integrity": "sha512-7XOMnz8Ynx1gGo/3hyV9loYNPWM94jG3+3T3Y8tsfSstFmETmENCMU/A/zj8Lyaj1lkgEepKepvd6240tBRvlw==", "signatures": [{"sig": "MEYCIQDqlttFc0uGGZRUZJUrLyNelkQwgWUm5hHbonuqkTQ2ngIhAKWaS0IUcxbxfy32jVBhN50JJ7kbPT8kvhZbL8nAqhax", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19657}, "engines": {"node": ">=4"}}, "0.11.0": {"name": "execa", "version": "0.11.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^4.0.0", "cross-spawn": "^6.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^13.0.1", "delay": "^3.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^3.0.1", "is-running": "^2.0.0"}, "dist": {"shasum": "0b3c71daf9b9159c252a863cd981af1b4410d97a", "tarball": "https://registry.npmjs.org/execa/-/execa-0.11.0.tgz", "fileCount": 6, "integrity": "sha512-k5AR22vCt1DcfeiRixW46U5tMLtBg44ssdJM9PiXw3D8Bn5qyxFCSnKY/eR22y+ctFDGPqafpaXg2G4Emyua4A==", "signatures": [{"sig": "MEQCIG0h7WrKN78Y2vJjZSLhK+M3jC3YNyl5Dpm+UBpiI53JAiBVSN7GWtkKU4QLK89bZaokMUbM8IJ7L4tC05JG6bDlZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbenSGCRA9TVsSAnZWagAAjWkP/0L0L1ZM7PSdEOxI/s3H\nyh1oubc/ek2l3amjnFU37EzbhsDPRxEdFxmjl8m327L2SKxZbSETaf0Z/tnc\nvsoZ0Cveb32RvJCZYmQTEYmYQSk/sXfoeNlIaI/lvzndINHwakq37W8INHIz\n36SbmwSPQFNR9vGuHExGx6jB93+vkPHDHgFhYLDlGZfH05SpGf4M3JDBAXBw\ny+gdLLDmqCgSJlhH5bBlcEvHnXusX+vleyxp5Y0/hDpL+Yd1Lln4u32fBPqP\nbXFj14F+0xzXS8baBKjB/OfOF+5GsFuw3WWhmC1/EecFloYpNlMhg1qxkYhL\n/Ves1LDYxwEm/NV7mfJ3lK7dV1KpTqhmpcDwo+1ME3eiEpkHDOVkaxIAVjmJ\nckcYEQPi2XBKWdQ3qO6+1k30ZUQmV8zHPFmk/0ZvZZ4QwE//rhLh5FsekOUo\nsZcASLcvYvPHFhQcsCNk61oZGMIWoohB3FfapAylFZdn24SQhQIufNAxlC19\n8gGN29tlcfhq5BD22RJJBXOmHbrz2zrI/kx+nNqAVaeg5p5WD197KO4is0zN\nqyM5t1QIc3oHg7XsszYaLm5Er4ZCKzAq4NovEpg3lWs0r47cA3DmSFBdViae\nLWTnLT02DLv3hSmJIHkJHErnnYpQZWo9/hbDfEgOz0ci2Uvl9vLK0XM5HRU8\nqpGB\r\n=D7f/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "1.0.0": {"name": "execa", "version": "1.0.0", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^4.0.0", "cross-spawn": "^6.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^13.0.1", "delay": "^3.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^3.0.1", "is-running": "^2.0.0"}, "dist": {"shasum": "c6236a5bb4df6d6f15e88e7f017798216749ddd8", "tarball": "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==", "signatures": [{"sig": "MEUCIQCOWOrgDmqTobyyEUTdH1RO/RYmWTuj+n8k36GBDGvAfwIgDgZSiHBQmLEgjzEDxq2LEpob8Er+dVP8Q1L6+eXguDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgubQCRA9TVsSAnZWagAAIsEQAIs35D02H15nByFfN/Ht\nMtyJPq3y8M4HKouIKuHWiO1auu6fmt/uhFpzNJJfnwhJoZBUPs/OZBi2w8jr\n4zglnRGX5w323oLVFEZHWIUny6YSNnDsxjKnDkrKw6j/3Z/dAo6o/QPO4/4n\nVgyLhQQuhvNnB6z6iH9tgpxTwyVcVetWqJOBT3T4idkbgAmZAsE/m/1RoUUN\nuq/72dV8H4jomYCvKvoeVsj+RpeTuCGoQFwoN/xjkuHh0EqWp3D13wJyc8Pc\n2tS2+mf/0F<PERSON>aMFEELoMV6W6dBI320xSBgD1mc0zJ8fy//oDmjctiIKVKEQRJ\nCPiQIuKKZ2HlC7cHUnGK9KNHk3DLiHVi6gJxepzdoSUArrF4J23v7JyQxzpm\nR12+DwMIL+TF3e/+XljqfpmNlbShh1Bvmx56ih2to9JafN0BobOHRMrQcqSz\nmcnFQCpQIGx4Cck0/TaumL3mZDsZ9DS6jPKrYEeJ5MkgU0Y2/p1MIB6dbhae\nvGJRmYlDuXm7DNTDdpuUo+vTYtRWvXA+O7K1V7lwavUbJ/eA+dlwxfcAKJFD\n6QKrVKtnCJmQqfP9rwJSXCYtWQ90+gGfoNDijcpoQj9H3pqcCnM0bAKaJbOm\n0s+k2TpbCFu9sqlGAYLZl3uwV3FqdyjrAWW/zjvn/S7O6cxjiYFk/Qhn+OS4\ns9FU\r\n=B9g6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "2.0.0-alpha.0": {"name": "execa", "version": "2.0.0-alpha.0", "dependencies": {"is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "e9525fa152c1d1da165448f77505db3a7aa57c0d", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-hYFS8zsUOomvssfNT8E7U5FJ3JLQgOxSE4s1ZVpxgWzL8eZX0g+GOg/FDrIXnLMlcEIyYHtpsvskn3eHaEUBpg==", "signatures": [{"sig": "MEYCIQCeAVtvLcM/OMgnMLMEgO93vXICVrwcU3RUJNoqNCiYCAIhAL3gsiOlD7DZct8ZOWBn5rrSw6flBwIFffb+pVW0dJv5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCSObCRA9TVsSAnZWagAARiwP/3ars0mbLZWXz99tPO+N\nieaaHQa/ixJAxs2QY7pGwzL7nFG+YSeHExvwkbL7+lUhuGU+8oy57m2Y68c4\np6fX15GrMF0PpekBamHbH0ntveAwit97M3RnYm2FdZV4oTVeopur8Vik0Dis\nYdsrIscRqxdsM2L7bYr0VciggJEgynjStvCxYs8CCJTyH8VsRt03A8YRC1aU\nBaSZeqKxBZejQEm2yxkNtFO9PQXYSt0gwU2q1FUxe4+4s499Q7V8pLtqF6p9\nNFyWfeqbbHJLa+dcZBqjNi7OvBzatna3LqycY/sJKetnZeR7CYqBoYE0q/La\nF2D8utEdJFfQW9BVAcJecu1qqtQNCCdiiNQUrIdCP6yfJLsLobetu155jfQS\nI9XvyTZg7Cf8pIz7jLzLp3+HNNYBoN29LSmDbuwudTzvtqPvBBkD6vGZAVp8\nGGlYcfnOezyRILmlEa6LhNdPpOv9OK94XuZDo6IwnqYEV9cSpbPuUrozFNQq\n0v1oPeD08QexlGEsnBnS9YW8vTNWEJpneDtUNV0WTHAZjYKqClebuEjIjA9Q\niLBfS1tK3nwq2ccs+hqjAHKKKdh030eoI1HdgEb7Y5dc0dvcr5QG2GiNMGGE\nYdkHjZ+zSPeR4h68pNvbZCPc4sTu02NfClu8RiFfq8Xnei/VJ0gZINEtxiPQ\nKPSD\r\n=wTql\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "2.0.0": {"name": "execa", "version": "2.0.0", "dependencies": {"is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "5524c9739710e603e97c6dfc3f6ff6bff2819885", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-+ym7S09yUVPHEhYBsdLm53ZjCmCSeAQVtM/iN9dDj9tbvcBnCeBXTXHPWR9HXzht+vslGROteM8bSUdr4YszUg==", "signatures": [{"sig": "MEQCIBfOIBxR0PiIbNdaMG6DUIRr4TRnjAc4B039noHtMID2AiBHTPtgSsfTDXF3DzMy+O6dtvJcGnB6DuFP38rtYd1Qcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEgTzCRA9TVsSAnZWagAAlpwP/0bQY0rGMMR2Nc3aN+gI\nflCPqr4gPHOLYm+BAbN9mRX7FdligHlfD2NxIxZRt6tzm+lutU6176GGJAic\naljEbzuh7ISrTNMtQqh9ZbZygBxrr44CLp6EkzZZHO3Wcq3T6/lTSP5E1daE\nzYSA/40n/g9++k4WOean+x1A5j8oci2UHsyCi8YhBEFr9OLhZ59KU6hst/8O\n3O5saM67YIquIj7fbQtumgleeCTUDon8+73iiriHloO8vvcEQF0kvAXEoC6l\nS86R9wfbAg1/J4J9X10k2OApICs9TnBBdXYULHEjiV4Fc5J9NVEPp9Q3uKHn\nkVfrAkz9PKOr2a5Ornt8FFw57qFNj3sl1JD0a7VGHlW3c5gx+Bg05EHpAL7A\nEFmoRlsoLQpVLwhNbIEQgaR0LuKAdc3ONYJdPhxeQiK8lP7mhRFrMjWITTl2\nIIkzOVAeM/VWSgzb6GuKElGiUc0sBrqZKy7VJQ5Hp7eEPu+KgXwffhnYKt+3\nXE7dglGwswDttDvHqVBsZswxUh5uRrV/aDqZsRYURguSW0WmThV7NMps02h9\nkElOTjrWieeBQBpcRMm9G9ehRCNNDu7v3VIxCzPZfyrlBwd1ldIjHmKPTWfe\nEF/aenUFuPLh2Leq7Z4iCKD2Ldjeh9ACkNGYf5lp0gVeuA5Ou+B2d64Te9nY\n3n4Y\r\n=LuG8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "2.0.1": {"name": "execa", "version": "2.0.1", "dependencies": {"is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "546a5be56388953409cbf24972d2fd1bb36dbfcd", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.1.tgz", "fileCount": 9, "integrity": "sha512-pHGXlV7S7ilDda3eaCTcr6zmFTMA3wJo7j+RtNg0uH9sbAasJfVug5RkYOTBLj5g4MOqlsaPUn3HKa/UfTDw8w==", "signatures": [{"sig": "MEUCIC6Sqd9nVse7SRfCdYSjG6jz5QDKPtxZSrkHzRhe0gPZAiEAttyR+g5GCTb46qikWc6LOqHwOr0tVHwsoHxZ8Ul4W90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE0VKCRA9TVsSAnZWagAABdMP/i1JInnH6kQ0ol50grPQ\nmPbQTWMgKMmVinDV7QXK/sh8LDXh4BYpjIUvmeNvdCJsZ7a4yjxCts6UZmgq\npl0I0ykbZo189vg6J7JCU4gB+lRra1WoD/hItQ1Oceo1rvgqckGnsbWEAJyn\n5OClVzAkyT4divp1E+2agKPuYk33iSfPMeQqoWboqTbEIbIISV2mjlZansAH\nbPmG+zjk5sppny4uD3omoNpS/fb1WWbUhHD1xQ0xS5bWFOikO3xdIYXouci+\nVEPX4puXWliy6FkJ+7Jr5Vlzuqeh4vyxwP26yzvvybZmZbj3dUtywE3jpXqp\nI3dS+MnSBKRU0FZit/y5F4bUEizBvNVIM/0HqbDCgIt93j/pmbPZRL9/kJnH\nLvAHHI/njPfim+0X0Se4KRAy1h0baJ4WPqWN90KThv14fotw2prAQA2szhyI\nRzPHoHo8IXyhzKR0Q3nuoGFqXpK/41t+Wczjnvbiih+LgA9Orua+qdtKlnfT\n/DqXDe7PDwslk8IIceb0hCMShIkxTU3B2YsRzglyHgqtKbiwSU5qsI0tKpN7\nahOSerjkXJv8ff+KHGa3VKBo230AzQN1ErBdRwhXWAilhg+uiLG2pY1dXQE+\nf0XQFgZSjrMa4SSMVvhn24d2YJha/BnYI+Bhh2GBuraV1AYPzzj+XisgH7ft\nvjTJ\r\n=D4qD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "2.0.2": {"name": "execa", "version": "2.0.2", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "3af2650be2b719549dc011a53118ecff5e28d0a2", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.2.tgz", "fileCount": 12, "integrity": "sha512-CkFnhVuWj5stQUvRSeI+zAw0ME+Iprkew4HKSc491vOXLM+hKrDVn+QQoL2CIYy0CpvT0mY+MXlzPreNbuj/8A==", "signatures": [{"sig": "MEQCIE9sl4uQr+qPX8/tD2PDYsJoBsIu2JRSGc+mtKju2RTqAiBkq9Pn8v6yDF22X6cR6lVgqDhppV4AyTU7ereVardoJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGd/eCRA9TVsSAnZWagAAXAUQAKP7eiP/GC9qFWPmWNBb\nbTCDkt+g/8m+NL84TMqpxe9iEK/qZmGw6WZ/YPrH6Q77VAmpTQfbJ449O52Y\nG2TbhdtRsP8CHIhwtpL7CdljgrmxoUWqLvMPthVSDmY48U/lgL7pmLKXSlgx\npq1zws7f8hOX78EGYmCms1VlHYWv2WNUuYplJiHdC6x7nPxPmBdvayE++seE\nW9NQ1fkJJwvfcrhZSEMbE9V0Q5avW4eAeXkp0trdUKPWfuNZr0w0qqGfn618\nn+zSIjkF+n6Cb8uQrmy33m9KkN6VrCuR9HJ8pxjLbZexSsderHHMeVifPpAi\nlAlcKVlSPgEpZ18kOS43B8HsdmfiSLU0j+BZpnud0rj8lQatwZzbz8Hv43PF\npdppKqAKBQhKDFXgYq3wy/6Hbl3Oy3Zq86pPwMmz6Co/mz8YvObR+AvZOKZP\nma0YxBBbINRgzGwVmcNZy5ZFiGbwt5Py7HBsBDFzhmj/Tkr2eN8TzF8hV6kP\na7gJH5OsFtLgssWHCZprNybOupKnOxYV3Tx0udIO0IsnK1BVTRnk0pWRUkuq\nNCMpVTWLjVSwJjfv0G8jR3WwPPIWTtPGixtV1X+a9GmpuYmaSGpLTo2vHtyT\nfHK2Jf+wt/YFghYvoBo4BqbvQLuO83azrS3NUizJ9VyfnkpZKKomU5E+cU3Y\nFJTM\r\n=E3BZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "2.0.3": {"name": "execa", "version": "2.0.3", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "4b84301b33042cfb622771e886ed0b10e5634642", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.3.tgz", "fileCount": 12, "integrity": "sha512-iM124nlyGSrXmuyZF1EMe83ESY2chIYVyDRZKgmcDynid2Q2v/+GuE7gNMl6Sy9Niwf4MC0DDxagOxeMPjuLsw==", "signatures": [{"sig": "MEUCIQCa0E+9FboqkHoR94Hp3UNauk2ZmTTck905u9ErYxatcAIgKXakXqw4W+xvtQD1oo9DqpAyC5AUBPtgiauhyak6Uu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHwvpCRA9TVsSAnZWagAAmiEQAJa8bkwYfcJcMAS1JFbT\n7nXhQjoKnQpx9+z4NY10dVpyEAcR1DujgE+60w8yt9Vf+ionFO6QA08iyMXE\nTaeOAzcgT0lgN58sw8apxG2DniM3F0iRhe4lf5LUnATn7oBAAv6G7cNAKtvz\nv9ttFBPWXllJk6R6NR6uPsIikKzfppWxepkFZsYIsGbK1/tHVBv4oe9tH3JD\nQl4HqRQtK5Sd+5I9DxulpVm/CLgOvhUhhDWscz6znrhRx7f6cYrTzZNGsBqD\nIfVqLqxWg/cbCmloI5ufaqd7keujhMwEc9R64BOvu03HLofoIahPpaZTh90b\n3Kx9I5JuMcEsMeGQBtIOxUs4jRc+mpKojYWS1k4CHTOlaKgVWSi7WQ42iF/5\n/9h6sB+mNjnuNZNnzlmouYiJwzykJaqONRzjOu0dy+SpphtFywiSfuLJBfiw\n6IWLuLGZ2kbGlan3roC9UbpLQ2es+kTSVX/OdbcrgncGV6TThalW8wNe3mua\njI3IDYy3B6yu1x0Juo5wtZLlSYxb3jyxw5YY0dL5hOAgrzXD1YfBZ+/oyqs2\noo+ft4fTQMgtqFLqK6F0BgE1CkgmWgbJRKab/FT2YeggHGrtc5lAPFxhGzWA\n6QlWJp39HMUQhEOu0Zs/AuUU4ndQXJ2UltKyHFEpgQDAnHX5T3in8W66PTwF\nVzjU\r\n=xW4d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "2.0.4": {"name": "execa", "version": "2.0.4", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "2f5cc589c81db316628627004ea4e37b93391d8e", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.4.tgz", "fileCount": 11, "integrity": "sha512-VcQfhuGD51vQUQtKIq2fjGDLDbL6N1DTQVpYzxZ7LPIXw3HqTuIz6uxRmpV1qf8i31LHf2kjiaGI+GdHwRgbnQ==", "signatures": [{"sig": "MEUCIQCutIAikPBE0hbVEN+pew02XAViYCPcYwJyPLLqYN02AQIgfHSSEOAhPMQdyQNKLWU7EjEDiNY+4rvhC85wH2T067o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVTTdCRA9TVsSAnZWagAA/6MP/1r1dCYF5tJKR4KOWS98\nVhLZa9tSQpqfzNbSMXNkxeezDBC33T0CFbaRN3blg/ZSMqMJyxvht7tDT8pO\nIBvC84WYenDWdh0f7HshyKtYVwjhcbHWVJTsgouJHcDUfmn97G4bgQM9WRzX\ny2I90r5M72/ismLareNH9Cm/ew3Z2C4oiZZz8yF4FJ31QOwGADZn1D5CrxMn\nkO7P02rWz5uzuN6LIovy77aqUfBDf9Lc0Skv/PhXVexfFeUIPzVye7XJeatv\nzBaddVyW0+2MfY7U9SOJqKtZYVECFjwqFBrdDYo3gbMRPAUcYZOuZ64opPRS\n+wAJw6U2uRVC3V4auSh5Z2YWTWZ10apn3ctB342UOoXzk7dYqFBo0JPdq5g1\n7ooAOSDoYnm8C/Jr0PCwWXDVWOtTfL7+lGMroG/ivoSZTbtGuXWwZ4AuvI+D\n7o0dHW+czNFuQub6t8uP3cjNuX2DeWzsms3d8iwT0cJa6KEEN43qclCXkvT1\n01SCJmyrvLgIff5OKG1gslJS92t/jobiTLktJAzQCpMk/7Xp7rBrcQyCNFit\nNUSaTmaBt9WlCJ4AW8M4CxtaVam9x5gxsUfUUm38aWq3G9cDPMn/caPkSrEc\nDQoE9aT18qKIGhb7gjPD+ylgRdtHgc20Kl3/UsZq766zqr2oj2K1ygRh52N2\nFJPt\r\n=xww6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "2.0.5": {"name": "execa", "version": "2.0.5", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "5be3e2ea7e61bd038da5a0e11dc6ab2097357f2f", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.5.tgz", "fileCount": 11, "integrity": "sha512-SwmwZZyJjflcqLSgllk4EQlMLst2p9muyzwNugKGFlpAz6rZ7M+s2nBR97GAq4Vzjwx2y9rcMcmqzojwN+xwNA==", "signatures": [{"sig": "MEUCIFSnDEq74EIntdfLZlJIx7JkjxcAghsxiIRGwAnws011AiEAvfoEVEHWJgW9CF7bDmBwEEvAj+ksSMliZnDpPP9D8vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlv1kCRA9TVsSAnZWagAA6WoP/Ruhb5Zm2Xt9DAUQC8mM\n2aOMw5rY1InHESuvwnASMOGlzqf6BQOQfQ2FqL/Yj7UVQvRbDrt5c7gZxLts\nfuqVVxHJfizwqkD8QR4NByj9Btvrhr7Gqxzhe/6bjsK4/c0xttv3bge2La+2\nVGOCbi7/5RNluvieHVW0+ivkaSVzIw6QLr8VazcLwOSx/DosnoXVuluHEBtY\nz/hxamrFsmSdxqyZKwjVMmHyme3xoI5U7l6eL6g0gpsTD4qo4ewupbeIVkcI\ngvzf7UOg8N6XIFWDa4frzf8efN44kVZvtJV+zgDcrWBr7sAbAFRJmMp/LrUK\n3d5jCGEjKEB1+MxACmA8iUnk8TYhKujWFaMhf2v7ii7I9ZFTamnQtMxsFryn\nhmcwfgM+xh8ZbmccSyXMzWJlDevBJy0sZUm8y+OGTLjf1hX/Rvz5ke4aNOSV\nqmawCmGcFwGnRYhUxwBiQy1fO80fa/tmu3y/jHWWty0sRNtuPERAiGywXEmC\nsgJGo2f2lEXCi7JlDFQTIyb/9Iihxhf8XmP2dc91cPJKk5fy8uh//tH/4XVA\nHQes4NOo0O9tFGkm8tUo++t3vpiieInl6BNFaCiX6UDLV8lVBGvvCgXpouG6\nisOyXsFYw7fDaTlUZ6LtxxHLze4/KfwQzMRIBiqG4x5sbNVuGFo5RtdZQHrd\nGtT0\r\n=TZDC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "2.1.0": {"name": "execa", "version": "2.1.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "e5d3ecd837d2a60ec50f3da78fd39767747bbe99", "tarball": "https://registry.npmjs.org/execa/-/execa-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-Y/URAVapfbYy2Xp/gb6A0E7iR8xeqOCXsuuaoMn7A5PzrXUK84E1gyiEfq0wQd/GHA6GsoHWwhNq8anb0mleIw==", "signatures": [{"sig": "MEYCIQDv26Ng8cRSPAvI1O2C6hUSwdEodtTMwy/ZbZ6IlIlukgIhAOSZ2nohGzYwcBB2aMnfkByiflC2941l6o9vuILq+onU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnaQSCRA9TVsSAnZWagAASL0P/02jQZx5VhDm7oPvuJyG\nOFhgQC0whEsBhWIcAUTEBvzsXEka649Xi86B6SdnbHmIQjMoCGJ9ArpBSVG2\n46PaLLtmUCO+gZLYmS/Lb3AA1VEa8XbdYdtCdPuOAAu27lAtxcmd2H9dK2Ro\ntPSFcq/6hrWGkzips9riXKpHD4YBbUCkgVz8NHx7CQMHv9YWxI9rfQllAhIe\ngxdsbTJ7sV39ZaWvz8B3wcNRgHsZt97xgAbcg4kij1pu+1ywLJl/VX86RIj9\nT0S3b+/uZZBjJDQhWrmrGNZdvKjtAScv15QYTKXDoHuMMu45HLB3GRHgLn86\ngsDEXDLa/1jWZXgoOQrppugdBI44IUTswwdcDfdC9i11v3BTbjzoeL/HQ7aN\nj+/CwRxJ9okDidN9OsJiMlOpO9X/YL7xLq6BfVUekcXmmrWOUZgFu1UIZ9LS\nnCSF7gpysKci2ZTSDWO/rdkWG19EUqKsL6iwuoT/s6AeLNSyi46aq7icyeDK\n84/X3gTU6c2tI9WCkXtJEC538Aa0qR1H89rQaTuA2mTP8Ho9fmWvWn2HPVJc\np23BYEobG1T1XO7LHRjecJQ7jZ4l1H/8aLRNmmFe+IBKdMFEADOcD3+pk32y\nhj64Q/OK8T0MuVnXZWazUwDuko48Xl5VCikNEBqthJ7GF4E9sPhW8ZnHpx4a\nP3jJ\r\n=nKfP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "3.0.0": {"name": "execa", "version": "3.0.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "f38e3c24932c2ee0f472383b0dcbd40aa42f61f5", "tarball": "https://registry.npmjs.org/execa/-/execa-3.0.0.tgz", "fileCount": 11, "integrity": "sha512-U3sotWWSGb19r7wVY3OcUgS/S/7VXbh3DQkYfPHaEKveYhgb+GqNN0YTkpnaeI7Ho2oIQ0Na6Mt98M93/wtqGw==", "signatures": [{"sig": "MEQCIFi6Av6fipcl7X9Kxnh2gayXHytrtoUJpJPp9EkipYdNAiBGmBfgPWIqvPpo9axYWANJtCpiHDcJo6VjTs6X/9lOSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpIFRCRA9TVsSAnZWagAA8FcP/jd5RZ5TmbSC++FXpAw1\nLhkB29EG/swloWgkuJppTdYzEapz0TEYRescuThVojHY92sU4PEcyrA1TuYw\nNhxgHZu3fR/lb/5TFLLHWQKTAuHfTo3PZnHg+p97gPFK9l8xYMTthLHz/+3v\ngertpuCbY2jlZHLnesEvft2leUV9PYBzrdN27ZcUWnxBffLM4yYrGMp8La0j\n7seYeDzm6VYsOeAgM9EK7L4tr8A2hfwWidDy6bAUON2N7DPMl1i1Ltt0LlE1\nM+fxhULjgYJDrYzAnvE3Ak2YCAYyc/u20Oi5kzmjo1XiZLe5W0iJBp/o8wLQ\nfu1NTtKKSDQvtc4IMrWXKaMxuDaor0Bfa7b7+0Duyo0rFbz36LoB12po6M0p\nl+oPepubdnaeQFRG4C8ViACGIN1LXAwoBuK/Feu5KZmQ1KnE6u0TW43fG3xt\nSj2MhcQXSaEWsViCYQ1HFQ430AoyaR3DjD3z/0vj8oBmRAuerAGtcOHQn9Xm\nufcUoie+QeaI2MwdjmpkL/JZPgAh6QbDzHkILtRGEqYxeqm51NCw/CHrl3M7\n+X29TC+RTxsob8ERFKUqPrIEmov3bz1DJycKomP7Vy6pN1erPpWwJX2APcmt\njYP28U7MFL99+Piy9fPG1naX2VgfqzOSsTKU1ZWBUIV4koUlosshvQghlT31\nncSf\r\n=BWpf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "3.1.0": {"name": "execa", "version": "3.1.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "d7a4a54b1ef3784c3d528c2bb7107f5720356929", "tarball": "https://registry.npmjs.org/execa/-/execa-3.1.0.tgz", "fileCount": 11, "integrity": "sha512-KcBxdjv1JlRiHMIRSDtvaGlUb6SQ4TLqxG9blJNTo6bzYYZZBHBZPKqMmK5Eftok7wl1iwDIRofxdu8tBlidQA==", "signatures": [{"sig": "MEQCIG7LMkge9PVGyqq2YYI886DwsPXrlfm8NcbMbG514IzEAiBfYzHHwL3RvkneSWSFmhbWfhzkKClVk3VoSYM3tq0WXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50553, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdphFQCRA9TVsSAnZWagAA9wIP/1W8RLdSU53HISnp9sqa\nXF6lUqHokkHESRBomDTaN9bAa9ys1yx5Ukq4AOuwu5G/3wvmKe4EjFumIAHz\n55fC/n/v35RcxhDHnWK4aOTMVmaJw7NUuSl7UaO2+2RyAY1Fma1TSMLTKpqH\nw5xO7kpGQp3/VUctCNKYnzehJn/9mY1G1uLsygaRBuEVYXkJYmX8BvIvAZdy\nJjsgL3fn3ArdG9dUpyKiyI7ydJax5qcBhKyV3JNraNHLIW4PG9wkrxpO2d+Y\nOJJ8BWVUCmX/BF4QapgzI/yG9uxOzYwHh/PioMIwIkY7DKGrnVnRrVNVzIbb\nzIXJXkQud/lNC5dhZa9etUI52Ohhauths3vMF6t6G3Rpp/yfgQ7fOrtiK+ql\nr9lMGzok+wUji3q6DHhtt3hbdRWHpwwYoCtzLOXR/yQP4KfxP4IRMeFzF6gm\n0aJJs9KZB6aKhrfxINakaOsSCh44Ci2xxC0irOQzbRSdaaH+iyTMJsFu/SM7\nRMuAYYVG1+2vT8UuOCTim5KB05ck6SXPfeWGgpRDz/QGPOq/8pVKbRjzKzgc\nlPhqDNnPcMoXpMlW8zGzTCkL58MVQyNxs61DH9Ub5zBhjHgjShQYx3TmjB9s\nwWwNLaLEddF7jiN5vozHQMaRCTnvkCz8Z3leUH1IexS2VUIJ0KyLp2/MKlsI\nD+DA\r\n=3056\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "3.2.0": {"name": "execa", "version": "3.2.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "18326b79c7ab7fbd6610fd900c1b9e95fa48f90a", "tarball": "https://registry.npmjs.org/execa/-/execa-3.2.0.tgz", "fileCount": 11, "integrity": "sha512-kJJfVbI/lZE1PZYDI5VPxp8zXPO9rtxOkhpZ0jMKha56AI9y2gGVC6bkukStQf0ka5Rh15BA5m7cCCH4jmHqkw==", "signatures": [{"sig": "MEUCICy4yPlkO2Ngdgb+NucxYFQs/7KYVXIDl+w4NYQEi5xsAiEAx6arcg3MazIN4XKoDVrf4OeSIeLCDQaRZaRRvBiyjzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqIS4CRA9TVsSAnZWagAAbN8QAKKrF1lkitIWmzp/qLOZ\nctI5Y6obSZkUuGSgO8BntnD7vhtwxdi8FI0dkJnhFqDst2qp+JXQDL9T03qC\noIWMxEqoxH4TSth1nCFRPT7zGa7z/csuWbhS82M5q9zs4Z2MYSa+jZ5TXUgI\n+9ssIlvmmLyk2GGiJnGoxmxURO+5ASstXQ75WAaDl+dJr0lFsfs9WfVCV4yS\nWW9w2JEXalcPk4geB0Sm3eTxU08hAd9GXEhXES9QhRqJRLp7kWFmazRB3yUn\nGgtBmYMsgNWRC7Nqdwk9kgiha98MtYMpmKaVbwJgiB4B9xHWN4qs+p96ZaHn\n8bI2+q+/uS2AXwq7LMiDSHisWj7DxJKonqGFdaH0S04ST3cMaNwvNsHqmWTx\n2rvWNvP5Ds/HVIlg30E16LXfKL+CrO5XWRbsSF2SP0Jj4FQrttMCiVoiXH7X\nFSENxApwome+9YTTsymyKhWrGPYzObeRymD4L5oFHl3ar/xRcjrOdOu4/ttO\nryCLGhzcCFDWpsy4JIb9GBg2tE2gEbWYXlDEZAC2tQmnehSx3AU96h1bTyQo\nMF8qejuphMKIuhfS7nsfCRP/MSgm7jGYniOZRnDRXe0pe44XfLaXVZE00YNK\n+UzRRKcEev3HNAL3s67mE9GTtfuLHNXF60VmC+fJt0Hx9yPZBP1B/GoBAFte\n3jBN\r\n=NMHO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "3.3.0": {"name": "execa", "version": "3.3.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "7e348eef129a1937f21ecbbd53390942653522c1", "tarball": "https://registry.npmjs.org/execa/-/execa-3.3.0.tgz", "fileCount": 11, "integrity": "sha512-j5Vit5WZR/cbHlqU97+qcnw9WHRCIL4V1SVe75VcHcD1JRBdt8fv0zw89b7CQHQdUHTt2VjuhcF5ibAgVOxqpg==", "signatures": [{"sig": "MEUCIClPZ/CuFoESVdQF2RXLiGH1t0pxloWLhyU6GlTkBQpVAiEA4+O60rw11z4oG6VMeXwETGlncMywuVmIGBWITsSz6+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyoR2CRA9TVsSAnZWagAA8aQP/2xhVZOuxFLZpNM2PqFy\nDyxLqVh7MLfg55vN2a02iVQvalaC2O/XG9jRgsZYHLBnB9F7fIWV0xu3uh7w\nXJjK/X/LA6khMCGfuIBWXVE02EoMZgVrnh/NiqHCOz0CZWznvP0PhwYE6ntP\n9/KAaiLLa0b0MqwNztXHS5FPQFtmqC4yO+i3HLPKRT609PNMOthpPkBGcl0F\n4sJQTB9/fP90QX//vZ0H49Ql8+MHqmeMK8csb+G2m64LvzK/klXR3QsCXIMB\n+Q57lWke4OPRym0Zz8BT32psSSKWZ+l5fsfhELKLhzI+sgHaX/RmjmgQkp2x\n+gLNUF0W1QO7DiZB+dacLKarHGYzKQ4eA/xHStgM0qqIdAlXjylqIG0bgjuf\nv15zM2dDfUfAJNlbi/sJgf33oj6wlgJcUE4qtOC10W3sPiQgsYYQyO66rhW+\nM4aAPyvPxVA1PiV+a9U4SqsXU0sxmJi44Lflw5QWbFFFCxuViVLSsMlhfUpc\nmJdFaLxXfVpjnShLwEpn7syKToUvhovVRKPjA9bQXbFP3T7afXQbQbx4mBfi\nBePoVzG/9ue2xK1HVpOgLAk0TzFsFJ179mT1ULsxDpD/ltMCLeUK/EhWc2fx\nhAwdmp6LqhqV6uFrjHYd/zqMH+76oU3Wg+/xLeuRTogtBJVo5y6cpKraTnAQ\nvtUJ\r\n=Vcl9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "3.4.0": {"name": "execa", "version": "3.4.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "dist": {"shasum": "c08ed4550ef65d858fac269ffc8572446f37eb89", "tarball": "https://registry.npmjs.org/execa/-/execa-3.4.0.tgz", "fileCount": 11, "integrity": "sha512-r9vdGQk4bmCuK1yKQu1KTwcT2zwfWdbdaXfCtAh+5nU/4fSX+JAb7vZGvI5naJrQlvONrEB20jeruESI69530g==", "signatures": [{"sig": "MEQCIE6ehcqhBg7BSUzrtYX0s4/IsvUqdbJI/jTQJ0MjbHFNAiBPCdbHchXCH4S4mjivcx27AudeVzfdhv3T/qJJNorw/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd26qrCRA9TVsSAnZWagAA9rUP/iesTqJ/YZ8RATHdbV8J\neugZ8+uVvcdEdC0Ch0R9YK/dNDhQjgCnd2gVXl/6eQ6yqrmDs6gHZq0yh0Az\n/ULbMpSVX846NMIDLzbiNB0hkTg0k0owmDwg/6VNpNehOqDOFoixYITT9Y5q\nB6lMhXKpbO0snAgw1lwUrPH+tZShGxdvuzlY5//XVBTVHUrWOiaC95avJ3xq\nQiVQ6OenGTwGbb+AUC8w/itFfILUZ1QiWLc5+ychKoBjvAlj0fngGwHUa5Lt\nTIwxOzkPn+5ifLKuMjTtwXODAODkIMVsCBi0A61BUdPulzTHQcHGqOyptkBV\nyld7kgGX8cYlOJSnW2HdlMHsdhyUYDgQayUTAL+QJys74rr1iq5OIcymXB2O\nOMvHh6+lzs1PCJzRJHbDT0eDuAPp5PflQ1df2fhxgNEBmCkc0ro5HFQ/aEa+\nMR6xV47ICFs/9MTbyLUBTarwEpOCILfQHFnKcEf7LMNIEqFUekDZko33DTI+\nX7WU5/+J4eLkebl0jefdOIxJEn2hluw/MWb6No5/TZcGZakxmHyQMa1OLaGg\nZzGEeyPeYzQfHa+fXeM2BOn3k5ww0irsF3gL64jwoq3C2mKZgYwBu9LzvPQB\nMmTbPb+nWm2mlzXunK/wtbghIr0Uj3vc9JcJmSm1hj0SxkGC88NzogaWHRLf\nlhud\r\n=1Rj9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}}, "4.0.0": {"name": "execa", "version": "4.0.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "dist": {"shasum": "7f37d6ec17f09e6b8fc53288611695b6d12b9daf", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-JbDUxwV3BoT5ZVXQrSVbAiaXhXUkIwvbhPIwZ0N13kX+5yCzOhUNdocxB/UQRuYOHRYYwAxKYwJYc0T4D12pDA==", "signatures": [{"sig": "MEQCIBW7t869WqX+12Jxofv7a276sYwAfPF+TFK8eI7DVhhpAiApH9KhIbxkgdBSa/OBzOjaECZdFDeDASAcOMFEjNVfZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+0e6CRA9TVsSAnZWagAAIz4P/0UOdh7Dhh5LNU/9xpxy\nIAmsSqqLKHVrMgCXbIQECc5UoZbEM0kOnqK2+sHJC/tTlCOC/cs6POq/85Rf\nZJXySK5Rro5w+EZIklaMQQjfNcc2GbsdRpDbTC1XWj0sNKk3fSHdX4UL4W2+\nrQXlL5MpnOaaXKsQ94e16a0VTq4CfhGC9TH9dUv4jlO12sciA+1sqjInewQ7\nZve6BsGlo3jnY0/dzst4oCOya3qKQdY0dAwhzJ/C+vQa8Aa5ibNCNBExQCCq\nu1V2cDU6OeR/Wmrd/84dBo3JuYxzbo/2gNTnXCpvKEM75J997DxK2iaEe59m\nv0mmkQnE8gc64gn+77rVhm+rikvNBcqG0EeEGhDLFuW0Wmd9S+ImILT6biu7\nXzOfu0rJ45/YmWP9R/WMjt3LvJO5XtgRO/iwK40O/0ITAQkoQIlQhLKtB6F+\nDjNIHP5lfwJG9krAkbPrteFcS6X/FpORDQdV+2JMb/WutstRLwvaTmZ7JBpp\nOq9bcXwLxbbcImQunnzlBInN48bY9R1ZlnD8orrdkaxB2Dd9WaAUXKUMHtmS\ndnV8Z36ahQzbW/R5O0D1qkHcmdEx85ul7Wf6n6aET/ZuqE2Rh7iiMX22xz61\nkf+7QLTki7xa6piV0iFNddRJ+9RB28CY+jt9IUPHYNBagNyUL0gvUymC8uPc\n7p6r\r\n=5H5j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "4.0.1": {"name": "execa", "version": "4.0.1", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "dist": {"shasum": "988488781f1f0238cd156f7aaede11c3e853b4c1", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.1.tgz", "fileCount": 11, "integrity": "sha512-SCjM/zlBdOK8Q5TIjOn6iEHZaPHFsMoTxXQ2nvUvtPnuohz3H2dIozSg+etNR98dGoYUp2ENSKLL/XaMmbxVgw==", "signatures": [{"sig": "MEUCIE2py7Col3ERnTN1bA5JWVE+ktdJCBA2Z09VM+lqbSFOAiEA9BgXu4UP96ZVRRRVR1qHnAOsqH9jL61IRTZKjbLEqhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetWp/CRA9TVsSAnZWagAAm3AP/RXcr+oiTzm3HM5KvDhF\nJ3UeoIga0KLZQmyXPk2o6+/qLDkGin56fVfSIVjS9GZwr4ciksxyJ2ODsyOo\nsEkO6pP2DgyzAILiQ6bFL/c11mL9i7IMkP8U+0hQAZUuCWTNK3pcBVAKUVLe\nvViKyhbGZ0cr9NYCxPpxSlgJuFt0JvVZXadaDlyHSvZihQ7aTcoDF69x3Kde\nk5+spvLavSMYX1BHlJ9uRaJXbc2xl4fOw26ec+UVZF/kP0n6YKcQTUwudnHl\nN5x96N/R/JdIwkFCklILJ5YlfDN8kW+G9Ro7RBcXVK8qdaPazTzusFBySi6L\ng7D50NPVZXh+swqAMB4jAKwiC/Fc2cOTQQK2jJD1xTOoUVwaEpaFHW7+ZM1m\nlmCtNkQ9+5pVdDx7hWSXPvSXjHi/N7Ml2TnceS0R2yZ/aPdwI40su2pEoeom\nVT9lWOrPzdYgOU/RE2tCtXWtnHstfh1jvJtiSg9ZVmDxmF2yqldwBkwiCKbi\nqYbE1zavR53Bp2CCwKaNV/BNxEN3WbICn9lMPUkmn73NWehZZvcN3q9ndzoi\nqa3Xv5wXZEJh8Gy4BGOqm+s/IF+Hzd/XUchD4kFhSikzwkFggeFQkLUqsiMd\nbZLN2r6U9473aa5Lwf4KDY6I3Oi09LMvkCvni1TyerVMvBKH/S+fxMxqX4oi\n8f5w\r\n=sY1I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "4.0.2": {"name": "execa", "version": "4.0.2", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "dist": {"shasum": "ad87fb7b2d9d564f70d2b62d511bee41d5cbb240", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.2.tgz", "fileCount": 11, "integrity": "sha512-QI2zLa6CjGWdiQsmSkZoGtDx2N+cQIGb3yNolGTdjSQzydzLgYYf8LRuagp7S7fPimjcrzUDSUFd/MgzELMi4Q==", "signatures": [{"sig": "MEUCIBIENiesA1VccWYy4lvkC0AuTyw2Skw0ObaK2RcJQn7OAiEA0NcSY+6pP66WKRots7t0cMbRYY3opWiNRiFyprJOQuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex9zlCRA9TVsSAnZWagAAV7gP/2k0BefUStZ1hRfyHXnY\n7UhjUGPqDWy+jSPDr2F+6pQmPRzmDOJd/6lMgMQQY1Ah3T3sXkrCZSwyYBkb\niH3D9ezvQlQndha1/wRMW5MYiVvwL8pvtpT9ZjDVsfhJt3xwkCxQ3ehZWnSZ\nEY9LQ7AN8BwokEQQtUc3q7vzFkIVKQGY8fB3D1UGvRG4EkzOiWYeHWti6Jnz\nh+sAni2+/tmCLpGGSIyPlFfBJdihFII5qzYfMBJe/ahsyJojDgL34M1GtTIw\np6wAyjLGRzGsjwZOJUG994+enb07fouxH0kPFBIU/+0DLo3NDPMDb+9Ei6XT\nxIgei8AlwLuPRZNhyLkR6Y8UuSuVaPXNVXUVZUI1bj34lbCJZaQlEGsE6pni\nCAIPTNfPVMloQFXcL8y2lWz36ioAwvHqLEyKRNP1YnFqiPNZGgPfIJDXaHPi\nIucoDnRkEvlziIpxnWcAcc7s0GYrhEYuSj+04PMNwlBIwgQh40ClpzlpZ/Br\nV53O7llwi8u1hgCTxzsnTFbYlLrbMR0z9gsjosfkfD+AEYKcG8S8JXaHdvBc\n1JVP9ZNK2yUBO0ZaO12HOziJpJ5Rmk7Ps9D706KIVqSGhbK5+DByD/DKusBJ\nNA3F8KH8E0wRlaYzulmhPAorC4GRr5ubsMKc5nvZjYJVkwVjFpOoUBA0NLuk\nxDYh\r\n=iTTF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "4.0.3": {"name": "execa", "version": "4.0.3", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "dist": {"shasum": "0a34dabbad6d66100bd6f2c576c8669403f317f2", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.3.tgz", "fileCount": 11, "integrity": "sha512-WFDXGHckXPWZX19t1kCsXzOpqX9LWYNqn4C+HqZlk/V0imTkzJZqf87ZBhvpHaftERYknpk0fjSylnXVlVgI0A==", "signatures": [{"sig": "MEUCIQDIB8WtLwPnyK24B7Vm9IieCYCx5cH3r/k0Yb+84hE3FQIgTUeA5ebLSe09Vtqu19Z8mFhSAFX9DyBma/iRZ/peSGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBL+3CRA9TVsSAnZWagAAgCwP/R5Y6XRBOF143ZOJMkLE\nqLwjdm/hKqLlPNNLQ/ITuxk38b7m8BnugBNaetymk5lwWNDv4JJvQjSlptym\nWZT19WIherYlK7ZrZ/0qTvWkcrYpNLDioKXhJbYX/Lv/R6aGX7cl6ngWZWXF\n9mHJy8JDUKVhfHd8+1u3eTYr4gjKHAzacGU5IVMyy+yYA41vCVy/OhAcK7wH\n+LElkgTwyhGooTt3uMNlzefZ+K6M6icJVDtYPaNc+pP9yimChFMbPI6jtYSV\nKKDep6YWLtNS7Is9/cytMLMWVOl8473kaQBYQNXAcw6eSiFX3zfyxaxRhPpR\nFkhYWqnRpPhAEPi4yahlgQZuRLq8qNMe6t50MeERJTniKhF+M/WHenhNHpL4\nj5ukuv3jSDfMYCsOwcZaIRz5d3uqYLnCtciWSXZidEsWjqNwhH3uCAXuGKFZ\n4xA3t0vhT3cEmkS1i6n1B+lEZ+w5XAEFE7zfCfUKtHHAtDUUtGRxatMVfTvt\nTRoTbw2Pku+Po+GUqwF/vF86ClxHlgJE0OphYJpHJEIAmYA2WRKOsabXCRne\n7g24ZJxAAiOVEVHpUzXsHEWGjzdlhLo4iNJsctvlMuL0bJZfMv5bMpyU/8cy\nfQbi/EoYqKfKidTQPJysePOSZ+IeTU3PpmjP1XOC0R5/ozkS80aIff53NPJo\n/FPa\r\n=IppU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "4.1.0": {"name": "execa", "version": "4.1.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "dist": {"shasum": "4e5491ad1572f2f17a77d388c6c857135b22847a", "tarball": "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz", "fileCount": 11, "integrity": "sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==", "signatures": [{"sig": "MEQCIEGa8ijl8B2HUnf51F9sliUETmYjthqkWLZVA5Awa/ywAiAh+l4tfTwoPoAQv5IQP6zW4HNJzBhKmBShbr1alxgKag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmVZsCRA9TVsSAnZWagAAK/UQAJp35uVM0BD9V9c3xsHY\ngoq9gw2XDAJWsil24n8m08Ox8QxnVjnTjwcPFTXRHbcQ5cbn5quUsH0HyAvN\nxFWMn53Oh6ADkOx1DHBfwGjA7m7r23KjNiY4rhMgd/6H5S+/QE/vuF48BYsp\n19zHyI2uKkFWoVPw5DyWmJ2WP6OXcITaaYmoCiR/UiXx4y7/3G2oXICQWRhd\nK5jIcCGg7E8XMEvP0PNTAQSo2nY23sdor+e/AJCVkugEmjUyUztPmU0tH4ug\n8lvhC3baJjwYvRbJDvMXn0/OGiTYd7ELZ7nR7bSf/9Aa+5A7rUtE1BPlDIkA\ne9CzOQkAGo2DXd9JGM68kcNQnBUbpdlhvhqgFHSem0hNEoV29ZunH7XouA2B\nn3L5O6eC+5hj3Zvllj9J4sLifwCqr6vzbNlsZQexVbjPb3xU/tRQQaA+Igpg\n2BctAuCLoILSMzCos82LPTXVJsOgS/86YRAVMNsvBDbbF7YOdFDr0wHp6dph\nhErYI/GRcLOdbnjxdN2sg/R8ZOoNrHSB0Yr02wrX0v85lyc4hsejCUnfDIcH\nRrQtceM/fvLgSONPyDvEh0kVbn5xIE9QvwLXvpce4xls2DFt0CBKKka5Kpba\n+BKMUEMG9ov5LWg8XLgtBDAI6u3Fb1L0YPfZ/h6s85H2ezE9oJCKP/8Hgz7e\nSpdr\r\n=T/dZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "5.0.0": {"name": "execa", "version": "5.0.0", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "dist": {"shasum": "4029b0007998a841fbd1032e5f4de86a3c1e3376", "tarball": "https://registry.npmjs.org/execa/-/execa-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-ov6w/2LCiuyO4RLYGdpFGjkcs0wMTgGE8PrkTHikeUy5iJekXyPIKUjifk5CsE0pt7sMCrMZ3YNqoCj6idQOnQ==", "signatures": [{"sig": "MEUCIEXWMpYxrimvrLvM6fC5AXe8GAnSDbAUQA5KWsuLQkdqAiEAuU2lzGUGK3z8tWqeDvdjTzKs3A0DvxFBB+oURBzf03Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyHV3CRA9TVsSAnZWagAA02QP/jwfskNDXh0ws36liVSJ\no7MF8kUEaFlbGazuTGZVkqr8D/5nMSf4SyMFS8i7RkcoXh6ektT+LKiJ21xD\n8lhUW0VHZfvGeIhTgAwFMQzKEJzaYxCsI4Rfmvducnv/JDGga9mG+ggsiTqV\nW6ysu06A2OI6kKkBk9WdT768qpCIEnFdRskGChNsssrz77nhdoJNZXpgaUUJ\nui8gzKPCY2T4t+2J+4bLjdy5S/eyg7xRfVbm3oaE04PGxpSIcvhqA07gxNYx\n1xmyVDuG5Rzc4OaNe4sy221IrYugO32fMpKPgYKcW85WwHsAOkAIZw8dvO4Z\nT7XfSpygAqPIaudRoZSY/vgkwGv7XK04YZqOPaKnvij4QU4DrmGhKk6hh/k8\njvAlME/ARw3xwbsSMyP7PkcJIbMrV2BB8IyTmwMHWiqWpMcjJWG0OQ4XLqvT\nni2lNU+Y8cmw7uLgzOphb6fo43ARCaAw+VN/oY8Rkfhp96AfeFE0EgTHlPth\ncTHyhCbJwj+o6y8cWRcC+MzP67fFNqiadEUqd+UMwaqPc1gorTRiBLntJwnW\ngV1l9iXW2QczP5irjXdmHgoPVTRi96o/+sDkETxTFnNOr5Mq+jnQxsyoEopk\nSe86wmUgddZ0yMVX4IAlD2Iu5Ie98MEfdr3znM8pt8ifxwTDCp4QUDq18u/0\n192v\r\n=uG3d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "5.0.1": {"name": "execa", "version": "5.0.1", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "dist": {"shasum": "aee63b871c9b2cb56bc9addcd3c70a785c6bf0d1", "tarball": "https://registry.npmjs.org/execa/-/execa-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-4hFTjFbFzQa3aCLobpbPJR/U+VoL1wdV5ozOWjeet0AWDeYr9UFGM1eUFWHX+VtOWFq4p0xXUXfW1YxUaP4fpw==", "signatures": [{"sig": "MEUCIQDGnVRJfhXPDhXhr/0QXXz/3ukSyPCrjn0xI1rIoQdDhAIgcntS8Nz1/exYYQsGE8p/5FOLtus2SG2Q+ciglEZf5r8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgs88FCRA9TVsSAnZWagAAmb8P/AmRKws75AE9bQ48qhy/\nEJh0PovPPw7IpFczbPUPJkJpOhpOXPPNdda1jfP6xpYpKgnFrwFFhJhf32Bh\ntTvET2hOAMC5WdDoGi6Im/GTLDKeobFvMVK3BByUe7AGlVUu6gCMvUdMD2L5\n6iVJjMY2BEfI+gofR2nm5l1v1/L2OiEKu8MNVIvn8tMpeEG2HJYsZlVZtQ4q\nXFRiRYHyADXgTIomH7hs44J7IWbxsWivY4cYIXANy2ATeX5rHgwSBcq+7Xr7\nV7H0HofYVnvLEGEgfRr6bMPwT7jdcPQ2z2RM3DpCojDxeR/07+Jsg2yr60hw\nIsy8BMNFstXffJSFGM2xHAlKBknVA/RjoLyXmhoaUx5igtIZd9fNgduXgd+g\n+Mz0qcYm0ayhQiANjoexVK+mI34IeSUIHuu9W/Foq+O5zdAr9xSFcQNqn3xh\nqLnRNktuhc3eJpPHiHIw4hZq7iC5tIo+O3rbORJgDCmUdD1z7ap5JX5gxXeL\n5f1u79UfJz41ZD7pMPHYH2yCMz1hbAgSUbzMLAKKn8JjmwTaZazBcGJ6TpwM\nJ44ckytXt9mHwtKRYVZGgcxJYBmOQTwizcV9Et9+yCJx4acrPq1I2TwmvZba\na4x6gBkWP0l/s1lsj603StTPvV5Ts6Sfj41Yz4iUwr3f9OZJfzijLTY1uBSX\nMvB3\r\n=R8pU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "5.1.0": {"name": "execa", "version": "5.1.0", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "dist": {"shasum": "3ea50ee863d226bfa323528cce1684e7481dfe46", "tarball": "https://registry.npmjs.org/execa/-/execa-5.1.0.tgz", "fileCount": 11, "integrity": "sha512-CkdUB7s2y6S+d4y+OM/+ZtQcJCiKUCth4cNImGMqrt2zEVtW2rfHGspQBE1GDo6LjeNIQmTPKXqTCKjqFKyu3A==", "signatures": [{"sig": "MEQCIB8b3jmcpR6hnmJ4ZJYRGUD1R15zEG8nOkIqiDK5yNDNAiBmVTheoVNm1POqnE9rPsLnWcg7sj3d1OrXze+dg2UyUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgt8GyCRA9TVsSAnZWagAAWpkP/iA2TUH7t6wbXWaNs/cr\nkGfmDpgl6coHL5MLetRFjIc9kyAYPPBjkX9J6kPYd9POOk8z2iW8vnEapHqF\ny0ZVdvLsHwTF7J5Io0tqYPoJZX910k3BSbFgsUCZr9rCZ+1Rm3xbEclQX+j0\n0zxbg8VB/3xVVn4/OiDcmYWKY626Tlyh5RlZ4jXV/xGht58eY8eYTT/gvk2Q\ndbO3Cr0OXuVFiTVPDjyPnXCcs7UWfjkGUTIQyYEkmkVYxjJ4AoUTcj1u3jYI\nkufbxgCMf4nrA8BBzgc6/+kyIw0w9cN7Cp1Y4qWVyAMfnzBeChNBkfwIpr+x\nyEUb05T33ze4JEtZY9NRQe7khagc8JTQbJtfXYuH1zbn6bruUgz0HLL5Fi2d\nPdbQ3pwGAtZ/NbGbu6T1QuPvAQJICW3Xc5E9syRqdtb6jUv/DD8dbG1zJftz\nxgaD7aidIU01GfyRdAtH3HC+o9HRC4hiPsGXNCyHHDh2cJX6bUJ4WoNJQxO4\n5AEIiSxk0fwNA8/5k0VPC3buIbpVJQ+oanF2cVHBffU9czfkw+wfK2YizaMD\nHZENmGG6kCDjC6eK7hYHk9vR9UcPVAOu9QXSiPt4j0jSW9jlHLNHfBce3AML\nhJOtv2fwvvhaW87ijKSfJTSvWbQKzK20uIYVK64OfzL/nCKFOYqNY7PC4Pm0\nGpzJ\r\n=2Mm3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "5.1.1": {"name": "execa", "version": "5.1.1", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "dist": {"shasum": "f80ad9cbf4298f7bd1d4c9555c21e93741c411dd", "tarball": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "fileCount": 11, "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "signatures": [{"sig": "MEQCIEOvNhvpEYmseTp1MjgvrSHXxciPcpZaOyAIgwIBxvfUAiA1Yz4bN6X5Vkngye2sOQLtzdKK4gSDjgnQbLd3Rso0hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgulbzCRA9TVsSAnZWagAA5+oQAIeagfdu0+/+QExG/EcB\n0fkkzV4FkpGDGvn9jOEAx7lhJGTwWkBfJA1wWKtuCOHqJlW/ifzCi/FHEt1p\nmZwH4MIstuVHSXI7qLZuP7ZiOCdGcqnJgSaxTrN/9soYVerX0l2svSG69hae\nVXh3G4GDsSlTvHD/F74NF11RFKWZUWSfUsG0lBFDDscCd9L8HQL8Yhy98sFo\nxOdZuE+DulIrUAHSHbkXqrKpb6GcNajYtEpk+S07ddWtBaQEPQxpXdm2It7g\ne41AV46H9n8UN9YZxQJPGJNYhm5btM2Mtk2gcZDskqDqJaRFQHTo1vMCU5GQ\n7/k2D96Xe2SW5s9JiPIMZDrSi1tFh/WDHz8a1hbrp4K92aNEMC9j5SEh74mJ\ncwgbnqm7JlcutBeElhEUxwUnVZw/QEtfT6hhU/Yugj2bqzsg9salrcjaKPds\nHODWdShZ14dB90H4r2udlz6MVda05Dc0/Q3i3WjxHdrW+shnHo/2t6DAhmJZ\nVY1QuIj6SnBbi3W8DGZYaReuZDfRc0sbmdZ4wYZF5+c/fjHB45tqhd6/9cqL\navwxIlYlow5AoKWPIEyIGl4875A5d1Akr91FOpMD/0Va4UC7inbAJGoJ2cxA\neHthwi+2jiEYezbv2uKXA3YHRQWsbzwN7jMNosZwoCSnpW/OiUMJECr2PlAc\nlu/u\r\n=Je/C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "6.0.0": {"name": "execa", "version": "6.0.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.5", "merge-stream": "^2.0.0", "npm-run-path": "^5.0.1", "human-signals": "^3.0.1", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^7.10.0", "xo": "^0.46.4", "ava": "^3.15.0", "tsd": "^0.18.0", "p-event": "^5.0.1", "get-node": "^12.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^16.11.7"}, "dist": {"shasum": "598b46f09ae44f5d8097a30cfb1681d0f0371503", "tarball": "https://registry.npmjs.org/execa/-/execa-6.0.0.tgz", "fileCount": 11, "integrity": "sha512-m4wU9j4Z9nXXoqT8RSfl28JSwmMNLFF69OON8H/lL3NeU0tNpGz313bcOfYoBBHokB0dC2tMl3VUcKgHELhL2Q==", "signatures": [{"sig": "MEQCIBKJwe0jKB3mZDu5TtHgw85p+nL61R6YSPUAMk3O88fbAiBrY9ty21tQuTKIpdAQRUQs3Hgz9YT/jpFq+IYUxQDmcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlOIcCRA9TVsSAnZWagAAQnwQAJ0kz6+UhCrqGVQJNcqI\nhZKvi7lNauo6+WXsNx7+ZN9JW8fv7v8EuGwzoZ2CJ599I465flTOIVMHWxjx\n9G5ckDD/lLWa3WTwwob3ytbvfZuEWiZ/86v37FDR9/5NUxoDTXGikrm3ooMb\n/2iPSZxBP4ffBx8s1yaCcQflf1fRVQs3ccW+EBmhrIjVZF3cKGGPtDOm4FEy\nQ6KnHKDdwM2OTfmLZAS0b/PtlGFtG6ztoLJzAOceIwdhzbZeJiyoFcw/ZgA2\nJdQEuLjG5K3bAZaK5mJb1z62N39oO4pNIU9TzuM4SoW+W2NaRmBKnBYs0/G7\n01e4oeTu7brSsIkczY9h1FINmLPkq1pBZGM1gFWbMzny/2ZUGUuHr3dxLnsC\njThT5te223qxlL4Mqt3F5clxOxv80iB2BLz/zM8RFpOCbSu+gqhOt7//I9vZ\nNmS0GkkhDOL8e3t2ybvabfw500DROiPZRB7UZ1n8ylHAq906WBd4BdBcqtHS\nLrFLnNIBxjO05LpOZ/da2kyDEJB+21UtQraZU3W7r1sc1+Z7Qme20aQvyF/u\nngk45BBDo3cdH8LrOFNvVO1zFJm6Hb0MePa0YQPG6za42oL+ZEi2DClvodiv\nWMrBvETkSG8SUuAf+lDQlG8BhYQ6l2eaBOk9E0woaQ1JZ/dL+1eadsChJ9+n\nAPn+\r\n=Rh+X\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "6.1.0": {"name": "execa", "version": "6.1.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^3.0.1", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^7.11.0", "xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "semver": "^7.3.5", "p-event": "^5.0.1", "get-node": "^12.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^17.0.17"}, "dist": {"shasum": "cea16dee211ff011246556388effa0818394fb20", "tarball": "https://registry.npmjs.org/execa/-/execa-6.1.0.tgz", "fileCount": 11, "integrity": "sha512-QVWlX2e50heYJcCPG0iWtf8r0xjEYfz/OYLGDYH+IyjWezzPNxz63qNFOu0l4YftGWuizFVZHHs8PrLU5p2IDA==", "signatures": [{"sig": "MEYCIQCaAgmCz9upmXsVXuaUq/Ngw6ut3cuixd4Wt7SfyH2LAAIhALwkghNuvZ9/A3kIDUIyo7bwsxjJGBQmOWjjlP24mf8s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCOAiCRA9TVsSAnZWagAAc6IP/jGVQnLw9+nxyx3Qsg/S\nydsFjtCm6ExdlNnzGDWVaH/qXjKPOk1MNkikyfx9ME8c2OIdMLOCTk4EIb9k\nU4a3xe/21I2G78wHqENCVSQocCSH55hPa5ZzQ71uGi0GEVY1CUt/UgrraDHx\nxN2gL5t+nNfsKTkbA1Iwp0CaK4zJ4j1AQazQ/wGuUKe1YeX5uFwR0UUm9flO\nXJejSoqudF3PPrwz4tR55AGWbFkUKdM5Bcd6Bls02f1GHO5uF5yk5XGffmCO\n26kolZm7fDrVz5psrQqCkcxRQilBN+4YRu4QwQMzeHr5uz5vYpr8lDxP3MjR\nMH1KlSL00tqELfFvejgaOOtfQCrcHR2jBCih4EbU6UpYyIzE6v8YazSa1Pcy\nkaBk0P4ChkBTMpw5XsO9CvtImr9L9Ir+44lCvMDZ8zZ+hk4jzNzsulnwBEgP\nuKF5k3c6Rfk16Jm3aQZKMaVfwi1H0ewxjnldaXBpaPlst2IcdE/QCDacZRN5\nO2fO83UEdhjvLlBV1jFFvamZNoozoC2H73IyxK3EXQJRjJ99P5UOMkUes3dg\nAaw24bYx2N4nI4JOyLyOxK5UEfqm9Pz9zvkxOvDjdvg/cD3oCaOIdAkCEz8F\nW3p2ovxyxXuBLXK6JgBcJv7teOj1ZuAPR6EHP+kn6lnw8FbcJnCdk/9QsmBY\nRIPF\r\n=YA6j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "7.0.0": {"name": "execa", "version": "7.0.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^7.12.0", "xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "dist": {"shasum": "2a44e20e73797f6c2df23889927972386157d7e4", "tarball": "https://registry.npmjs.org/execa/-/execa-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-tQbH0pH/8LHTnwTrsKWideqi6rFB/QNUawEwrn+WHyz7PX1Tuz2u7wfTvbaNBdP5JD5LVWxNo8/A8CHNZ3bV6g==", "signatures": [{"sig": "MEUCIQDtU4E3tEf5e4DLvwqJg0dusQY0Dh1AeKTlu51/XhTY0QIgYFna3WZVKVp0YkjjmKfgD2JXfyGO6DAl6bacyitted0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5821ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp02g//cjityN/TxvgW9e05U2uxJMQUyBJZCkuU6rqmATQdIxlBFANp\r\n7zkFXKnu2dYCCNxValiBurCuqZf08XLk423h4+lwQZ6LGjoLNmPFUiNiXpiZ\r\n2J+YnMzN9JzWiBxRcq33G3os3U25GEd3BylkUl3b/Uex3VkTvkbkHRmZY0lc\r\n2WzCg0E5JM1Te3ZwmXX0lIytsiEub9GS/HcicT4zh97OMtSNcAmz7Ojt5+LK\r\nmqfbe7klXxIQ7vG+mGBofXivpeY+OTzS9cL5DPxl49ZXPuJ0VExLW4ec2Mze\r\nq0+JShK8+nIMQI8EZyF9UkYXYR1pXUyv4+q9cO0PZ2qAOtIJzziqVDJ3OJVi\r\nkAmgPiC5k5BpRN7adgIrX8RvWoFfCxbhNsj4Wqz4FNMrhZZfICIrwAig/njU\r\n6BgrzMOCM7dwoEfFBmdK19vQMRtWmlDjBHUy8/NG/hjL1FIY0z1BS9s1ruWp\r\nL5uH6b7Rwij9YqyF2vWtmXyzRzs2IujqsSPY7SyHmQuMzpCl5b15W/eg/jht\r\nG5nd1Bbihu/s6DqQVPlBxeUlsWCmGsGvr+YZN0TJe2te2Z+qrtlSAZpkDm58\r\ne3MeO0qVPQ7Shqv3XZv+xiCG4fKj3td3NRF4GitAF+5f4OP2LI+ChkntYS1Q\r\nRVM7GliNaSIzKUynA4dICHxWbw9UV9XXiSY=\r\n=vda6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "7.1.0": {"name": "execa", "version": "7.1.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^7.12.0", "xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "dist": {"shasum": "50c6f39438b7ce407e8c7a6829c72b074778238d", "tarball": "https://registry.npmjs.org/execa/-/execa-7.1.0.tgz", "fileCount": 13, "integrity": "sha512-T6nIJO3LHxUZ6ahVRaxXz9WLEruXLqdcluA+UuTptXmLM7nDAn9lx9IfkxPyzEL21583qSt4RmL44pO71EHaJQ==", "signatures": [{"sig": "MEYCIQDYrjWcU98RWlzMhFaf8uU07yqm9ZX8PBqL8wC+OOWtUQIhAJ7XEw04+O5L2rl0Ro9dxcN9sabsilqiLgXTvu3E8zoF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDRVCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeFQ//aa7S57uYBH+uv/HaZaDLdf5nqEbSmxONJpx1oRkJGCr0MiFi\r\nYSABNKzmQq86DygDrv73prwcDBcFho9hS6kWpSDr0izd3oakizO3h2yX7lBB\r\nSSzAHeoR759fj0LSSk4Nb5u5za3PMyUTP20W/kLFyXu0xe7HLDUhncZH9JJi\r\nzPgc/HMjcNS5O2ycLHc45Gxaxxgv9v/PzUA6/OG4L8FXQfqFPugfBERkqgQE\r\nZUwqCxZ/4hZSQ+t9EeN6aqI0LEiqWHfcu27v8DUHujqXLtL0/JbHQxQ6yZch\r\nccm1XnaI0Nf76QrGJebTsuxmT09Ek1PpAt7f8BgeGzGdGzexbZ0ewgrJPLhG\r\nlzCEH8xo8EL11SwR4bfOPxa/zt968Ehuwx3QreHw/nkTdsnXzcyKhDYeO1Sn\r\n+eb7+08KsI/OBqJVNSahcvHxQwxPJF0T5k99IT8nZlrzw81aCqJ9cna4piPu\r\n/rtM9oWst6+/m4flVWI6BxgSwkLObsdKdqFe+zz0qQ3Mz5Az3G+ZixvVbuSR\r\nLepb8CnxC5W1rzlhTa/IILYTriZSexZbc/RoC+uEWRQGngOT6OvVOUXathsl\r\nIafHfbCndwKJ+7fzwvrulzfGP4NJ2FXLT5A7phq2viLMOSglUsoWqZ8qp4XP\r\nE753eaKZgIxoOcc+sWBrmQGP7A6q6DQrh5I=\r\n=eviU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "7.1.1": {"name": "execa", "version": "7.1.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^7.12.0", "xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "dist": {"shasum": "3eb3c83d239488e7b409d48e8813b76bb55c9c43", "tarball": "https://registry.npmjs.org/execa/-/execa-7.1.1.tgz", "fileCount": 13, "integrity": "sha512-wH0eMf/UXckdUYnO21+HDztteVv05rq2GXksxT4fCGeHkBhw1DROXh40wcjMcRqDOWE7iPJ4n3M7e2+YFP+76Q==", "signatures": [{"sig": "MEUCIDvkNF81qGZyMhbgI4YGp/Ye6GI9SqRqlb75QyM/5jkEAiEAiFU/5PGxGAIcczZNcPPLfPAR26v6eip3lRhMzBYelx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEM6KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmod7g//YHLkwkK9fSIEePP0pWCQCa+SNiCMHsbLJuLlAOkiVcKumpBZ\r\nYh4SfKrsBegqSrTSqg4DrJAtuv5q9x89TO2TAChNyanKpw/yYxD0jP4uJPiS\r\nBuRsxdIdXv4LjrMdCrHyT3JR/OIOkzmvJP44g2c4Nhop4dLumg2agohSWNDW\r\nEtg0xAX7Ql6sBsmXpOQfrsbWbr5STFF/rRwMdVQbicTxQ/ymS0Hjj4XapqRV\r\nZOJ0gk7PZctn7CMMC0gmxTSeTjBJVI6up+wKUaQ1p2XIsMyv7QjUQlDu09Ge\r\netsPMitwBeG6CBKJf1c+FHvSu2tAO1xbZiCiHNXrkcRyzt9lnXYq8WOZP8ms\r\nhHnGY0NOvsy99qpc7R+y0UB9Mch+RW5lkWnSqSBCus1H9FCgYXkoCL9vsdsz\r\nRP6ldfbfG0KKNAwVq+bM8M3921pQuQmukkQ02sbrohNwmgxZG1rV8W8PnN56\r\nkJ/7aB2oeu2KugkPYK1lw+RH+BLHnlcOTPThuJ6PZLCE+2dyxYmY7RLFcqVm\r\nFCh9IadsVjguhT79wOfNg77q8++S23W4qq+JoF9KTasgmfb9qbqLem9SWZ3H\r\nDO6BYpBDeQ9yRcLm9EzwTcbJAZAkCX7CCT4zSJ8birdv3ZnbXE04Kf/S000o\r\ntZTRhR8WkryPBpcHYSyPM5WkgSU1ifszG3g=\r\n=B+2S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "7.2.0": {"name": "execa", "version": "7.2.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^7.12.0", "xo": "^0.54.2", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "dist": {"shasum": "657e75ba984f42a70f38928cedc87d6f2d4fe4e9", "tarball": "https://registry.npmjs.org/execa/-/execa-7.2.0.tgz", "fileCount": 13, "integrity": "sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==", "signatures": [{"sig": "MEQCIBWOqaVPJqgyWgaalrMEEHdPl9aSQEZFMFJKX9Irydp+AiBd0vNLT5yogE7YwgjHQ+5ZVU/xp69hwh73W8ZZYai8+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78941}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "8.0.0": {"name": "execa", "version": "8.0.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^8.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^5.0.0", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^8.0.1", "xo": "^0.55.0", "ava": "^5.2.0", "tsd": "^0.28.1", "p-event": "^6.0.0", "get-node": "^14.2.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.4.0"}, "dist": {"shasum": "bed67705f9966469cd93e521617868c52a4df6e8", "tarball": "https://registry.npmjs.org/execa/-/execa-8.0.0.tgz", "fileCount": 13, "integrity": "sha512-CTNS0BcKBcoOsawKBlpcKNmK4Kjuyz5jVLhf+PUsHGMqiKMVTa4cN3U7r7bRY8KTpfOGpXMo27fdy0dYVg2pqA==", "signatures": [{"sig": "MEUCIQD1pihV1ONqC2OfkXjlY/qcSt2ThyrCbPtWq7BHkCxqkgIgUBT6EtGo+xDm9GoTMfETA1LGHaPTclq46Bmk5aIkr7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79424}, "engines": {"node": ">=16.17"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "8.0.1": {"name": "execa", "version": "8.0.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^8.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^5.0.0", "strip-final-newline": "^3.0.0"}, "devDependencies": {"c8": "^8.0.1", "xo": "^0.55.0", "ava": "^5.2.0", "tsd": "^0.28.1", "p-event": "^6.0.0", "get-node": "^14.2.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.4.0"}, "dist": {"shasum": "51f6a5943b580f963c3ca9c6321796db8cc39b8c", "tarball": "https://registry.npmjs.org/execa/-/execa-8.0.1.tgz", "fileCount": 13, "integrity": "sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==", "signatures": [{"sig": "MEUCIQDjbrEvFs/4bELHS5gR5MBYqah2IIYQn9l9hDYY7/kGFwIgeRu+DUJNO9ShPW/T6vVSf+2Sq+G5d7p+paxlEMFaBTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80144}, "engines": {"node": ">=16.17"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.0.0": {"name": "execa", "version": "9.0.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "dist": {"shasum": "51d54955c3bab43849e5fca1117aa6010bcba313", "tarball": "https://registry.npmjs.org/execa/-/execa-9.0.0.tgz", "fileCount": 125, "integrity": "sha512-YqlYPdTp6UMdt0WEM8QKeBR8ORtgxWP7ZB5NB9AMlfGWg32Fg48j6uZdKfhG2o6cNJBIRPF0Ok93R0vY37oBWQ==", "signatures": [{"sig": "MEUCIQCP8NVpDQbzxdCzr2NSB3+5QrT58kQb49YvBO5UGLnyegIgEIc+eytry57lQ6nPWCIzUsstXBEtyq9AZjNGiaVqG3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 288824}, "engines": {"node": ">=18"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.0.1": {"name": "execa", "version": "9.0.1", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "dist": {"shasum": "e1a0ea8896a8975e581e7f8a905e1f66a2232365", "tarball": "https://registry.npmjs.org/execa/-/execa-9.0.1.tgz", "fileCount": 117, "integrity": "sha512-U5ck8xJmf3sVebV1v+Hh436VWHVHUfzkdbKJynd3kCP9sQRDxCY5x2Tml5lGB7XM6lpj6ATfgWWqynDt2MBLJg==", "signatures": [{"sig": "MEQCIC4b4XD6YSmwBtAo8dhLvG+D20fb89AsuutXdNDBNSaRAiBREJ/DgLdQ+sp6AUKQ8toCZuuoQcanXPexNCuXwnSH3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257907}, "engines": {"node": ">=18"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.0.2": {"name": "execa", "version": "9.0.2", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "dist": {"shasum": "9590a079f6632cc6381cb724cc03ae2489cbdea7", "tarball": "https://registry.npmjs.org/execa/-/execa-9.0.2.tgz", "fileCount": 129, "integrity": "sha512-oO281GF7ksH/Ogv1xyDf1prvFta/6/XkGKxRUvA3IB2MU1rCJGlFs86HRZhdooow1ISkR0Np0rOxUCIJVw36Rg==", "signatures": [{"sig": "MEUCIDoIm64xl4nolBzRThA6OatyL8Hg2rcBrn70dNGpRWYuAiEAovAkJES1AXKbZ9FI2wxE3yKP7RDHgI1732s2sqF3O7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 310789}, "engines": {"node": ">=18"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.1.0": {"name": "execa", "version": "9.1.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "dist": {"shasum": "c42845d2b079642b8e07d9de81db13cdb91e7a9b", "tarball": "https://registry.npmjs.org/execa/-/execa-9.1.0.tgz", "fileCount": 129, "integrity": "sha512-lSgHc4Elo2m6bUDhc3Hl/VxvUDJdQWI40RZ4KMY9bKRc+hgMOT7II/JjbNDhI8VnMtrCb7U/fhpJIkLORZozWw==", "signatures": [{"sig": "MEUCIDWT2OmV/bQow1mQ/KtgGp7lV8+gvG9dqW5YDcuo1UICAiEAz6mWXtCLw95/9JBf+RL4WjS2EsYpse24No97CSGlz1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311022}, "engines": {"node": ">=18"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.2.0": {"name": "execa", "version": "9.2.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^0.1.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "dist": {"shasum": "ec5e9de67a714d0f47ce073d37a851fbf0c2f688", "tarball": "https://registry.npmjs.org/execa/-/execa-9.2.0.tgz", "fileCount": 137, "integrity": "sha512-vpOyYg7UAVKLAWWtRS2gAdgkT7oJbCn0me3gmUmxZih4kd3MF/oo8kNTBTIbkO3yuuF5uB4ZCZfn8BOolITYhg==", "signatures": [{"sig": "MEUCIFoPpN0vGJ0t53br2Nd1+689TjnYRHPCuCGVAhhA8XvhAiEA+P1PE/wCZ77ezMPzMAFozO/9pbgVvCBEnUn9oM7t9mU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314014}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.3.0": {"name": "execa", "version": "9.3.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^0.1.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "dist": {"shasum": "b10b70f52c1a978985e8492cc1fa74795c59963c", "tarball": "https://registry.npmjs.org/execa/-/execa-9.3.0.tgz", "fileCount": 141, "integrity": "sha512-l6JFbqnHEadBoVAVpN5dl2yCyfX28WoBAGaoQcNmLLSedOxTxcn2Qa83s8I/PA5i56vWru2OHOtrwF7Om2vqlg==", "signatures": [{"sig": "MEUCIH3QI/C3kOUBeWEnSyc7kbRh6mqGyu9PyvGy3tB9U/DMAiEAmn5vqoxhl0g7vSQzTMCKYa7WC+QWfvSAvAfmt+NFNds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320815}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.3.1": {"name": "execa", "version": "9.3.1", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "dist": {"shasum": "09c86ab4dc2ef3de6d34f6568f4bad76ded4fded", "tarball": "https://registry.npmjs.org/execa/-/execa-9.3.1.tgz", "fileCount": 141, "integrity": "sha512-gdhefCCNy/8tpH/2+ajP9IQc14vXchNdd0weyzSJEFURhRMGncQ+zKFxwjAufIewPEJm9BPOaJnvg2UtlH2gPQ==", "signatures": [{"sig": "MEQCICkoAFRX7PjBYgB/x4pK+2U/GlPvZ5LG9qpB8e4eFbEPAiAoTvJ8+G5Y3CgCXfTVNCLagQx8NcxG1gs22Sp3Om5yLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322819}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.4.0": {"name": "execa", "version": "9.4.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "dist": {"shasum": "071ff6516c46eb82af9a559dba3c891637a10f3f", "tarball": "https://registry.npmjs.org/execa/-/execa-9.4.0.tgz", "fileCount": 141, "integrity": "sha512-yKHlle2YGxZE842MERVIplWwNH5VYmqqcPFgtnlU//K8gxuFFXu0pwd/CrfXTumFpeEiufsP7+opT/bPJa1yVw==", "signatures": [{"sig": "MEUCIQCOLlqA9hmVV5KowNi+OePcKiy1Agqs0x3zlyteVE7p9wIgKDC2vQ1OCpumanB+NHzAgCXFcWTXzHSDglXYON0V0go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322959}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.4.1": {"name": "execa", "version": "9.4.1", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "dist": {"shasum": "823c74244c55bca37d04c9bb996c397f4a1486a4", "tarball": "https://registry.npmjs.org/execa/-/execa-9.4.1.tgz", "fileCount": 141, "integrity": "sha512-5eo/BRqZm3GYce+1jqX/tJ7duA2AnE39i88fuedNFUV8XxGxUpF3aWkBRfbUcjV49gCkvS/pzc0YrCPhaIewdg==", "signatures": [{"sig": "MEYCIQCiJCgmf6by+EBp+TC2zC+2VWHRfMpDroxw9mCOOC4hrgIhAJA2mUue8WG1kz2nZUUNweAF2PeXkp7enS/tRrYRyuGe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323477}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.5.0": {"name": "execa", "version": "9.5.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "dist": {"shasum": "b4437553fdd084f65184b5537a9bc38eac26c59a", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.0.tgz", "fileCount": 141, "integrity": "sha512-t7vvYt+oKnMbF3O+S5+HkylsPrsUatwJSe4Cv+4017R0MCySjECxnVJ2eyDXVD/Xpj5H29YzyYn6eEpugG7GJA==", "signatures": [{"sig": "MEUCIQC/SCF2ssr3Vzv9w9gSpPle5k9rLK+t3rJuwWi1VJvDwgIgOy6JvLILcoGlXqEL/w4wsKrOmICqpdZ38P+IPp369l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323685}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.5.1": {"name": "execa", "version": "9.5.1", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^5.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "dist": {"shasum": "ab9b68073245e1111bba359962a34fcdb28deef2", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.1.tgz", "fileCount": 141, "integrity": "sha512-QY5PPtSonnGwhhHDNI7+3RvY285c7iuJFFB+lU+oEzMY/gEGJ808owqJsrr8Otd1E/x07po1LkUBmdAc5duPAg==", "signatures": [{"sig": "MEUCIQDTa9GxeQK9wvYMUkPk+y4/SiW+W0NkzKLuAyLxoQ3+rAIgF8GkNWeMq/gzKLR8FDfGUDpFVDgu0ipyVkPueonRolI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324065}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.5.2": {"name": "execa", "version": "9.5.2", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^5.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "dist": {"shasum": "a4551034ee0795e241025d2f987dab3f4242dff2", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.2.tgz", "fileCount": 141, "integrity": "sha512-EHlpxMCpHWSAh1dgS6bVeoLAXGnJNdR93aabr4QCGbzOM73o5XmRfM/e5FUqsw3aagP8S8XEWUWFAxnRBnAF0Q==", "signatures": [{"sig": "MEYCIQC4ggedKyhEYtS/7Hie5RfwO5VVgWlkFBCXHkI+kgHgswIhAMK+UC06Ixd1jAP9tC7g03CApX4Akpb7i/JpmsnI9JLh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324196}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.5.3": {"name": "execa", "version": "9.5.3", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^5.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0", "log-process-errors": "^12.0.1"}, "dist": {"shasum": "aa9b6e92ea6692b88a240efc260ca30489b33e2a", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.3.tgz", "fileCount": 142, "integrity": "sha512-QFNnTvU3UjgWFy8Ef9iDHvIdcgZ344ebkwYx4/KLbR+CKQA4xBaHzv+iRpp86QfMHP8faFQLh8iOc57215y4Rg==", "signatures": [{"sig": "MEUCICdaZVDXL5YOrjkQdnyXrcyojjP+3Utuqx8lkwmFilV2AiEAjxEIk9whm2T4ie+8kdsnKOIS9ViN5R4j4C/fWYCrFog=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 324313}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}, "9.6.0": {"name": "execa", "version": "9.6.0", "dependencies": {"@sindresorhus/merge-streams": "^4.0.0", "cross-spawn": "^7.0.6", "figures": "^6.1.0", "get-stream": "^9.0.0", "human-signals": "^8.0.1", "is-plain-obj": "^4.1.0", "is-stream": "^4.0.1", "npm-run-path": "^6.0.0", "pretty-ms": "^9.2.0", "signal-exit": "^4.1.0", "strip-final-newline": "^4.0.0", "yoctocolors": "^2.1.1"}, "devDependencies": {"@types/node": "^22.15.21", "ava": "^6.3.0", "c8": "^10.1.3", "get-node": "^15.0.3", "is-in-ci": "^1.0.0", "is-running": "^2.1.0", "log-process-errors": "^12.0.1", "path-exists": "^5.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "tsd": "^0.32.0", "typescript": "^5.8.3", "which": "^5.0.0", "xo": "^0.60.0"}, "dist": {"integrity": "sha512-jpWzZ1ZhwUmeWRhS7Qv3mhpOhLfwI+uAX4e5fOcXqwMR7EcJ0pj2kV1CVzHVMX/LphnKWD3LObjZCoJ71lKpHw==", "shasum": "38665530e54e2e018384108322f37f35ae74f3bc", "tarball": "https://registry.npmjs.org/execa/-/execa-9.6.0.tgz", "fileCount": 142, "unpackedSize": 324314, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC8RivxXFB+oyg4qzLgptkowdR0W2p8q7NV8mXOGr9skwIhALBWYZqeNkUSeH4u7HHQODGTNRZWSRubgJ5YFKaH2vcQ"}]}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1"}}, "modified": "2025-05-26T21:59:25.326Z"}