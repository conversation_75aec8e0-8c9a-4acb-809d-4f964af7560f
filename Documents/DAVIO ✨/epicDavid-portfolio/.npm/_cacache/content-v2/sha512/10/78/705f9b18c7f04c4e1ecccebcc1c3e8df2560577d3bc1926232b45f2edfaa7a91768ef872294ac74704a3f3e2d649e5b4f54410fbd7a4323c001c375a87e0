{"_id": "@typescript-eslint/tsconfig-utils", "_rev": "51-4ebf0bdf696ab7439f86470501fb6de7", "name": "@typescript-eslint/tsconfig-utils", "dist-tags": {"latest": "8.35.1", "canary": "8.35.2-alpha.6"}, "versions": {"8.32.2-alpha.12": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.32.2-alpha.12", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.32.2-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "2b95ce6e9b6554876e3c45b8765fae1717a5f38a", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.32.2-alpha.12.tgz", "fileCount": 12, "integrity": "sha512-KCcMOreMTgn/2+08PN48IIiai2BOkJqtOefbRjYZoI10PfdgSGf4Y27dfXqQaHMLx0UC3jrJNZPWpzppXlZaTg==", "signatures": [{"sig": "MEUCIQDZLKjlUdgaEI44Zo/LAZnx0hfqCz0kmaLQ7Z5w2Sxy7AIgQkiRjJOpijPuF2Q29YmEh/P9+3kjIpOYThjSCYm/fV8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.32.2-alpha.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9613}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d2ffec7988976a07495bde7270630db1a0fe1d32", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.32.2-alpha.12_1748354127982_0.2087763236781397", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "316adab038bbdc43e448781d5a816c2973eab73e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.0.tgz", "fileCount": 12, "integrity": "sha512-sTkETlbqhEoiFmGr1gsdq5HyVbSOF0145SYDJ/EQmXHtKViCaGvnyLqWFFHtEXoS0J1yU8Wyou2UGmgW88fEug==", "signatures": [{"sig": "MEUCIHx0q5RJ6vn+GuiGsQxyjzstcrhT00/eTwkrdsxHWwQaAiEAjNRXF8Y6CdmetZvtUtG1b1szURAyvKyZ0dA2Jgeg5ks=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9604}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d2ffec7988976a07495bde7270630db1a0fe1d32", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.0_1748366493542_0.5247665845827811", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "ad24be701e063c7054bcc15dd4a7ed90e40fa63e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-ORzdYvvknS+fXQuvWKN6zvZhTkTKfNjz9l83HBJ2kLwT2pjqSlbzFpkU7bs9qhycGTqy64c6yQVd0/L7zB35EQ==", "signatures": [{"sig": "MEUCIGVOxIGaTX8LmAkCrPYdBR0ltxwmlakz/+5eSbq04vaSAiEAjfHjORnFdT/FEzN25C6jxG25+c5spu9Qie6m3G2be3k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "02ebbe10f6d9fc6a415b33a76bd76eb5402083e5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.0_1748524583816_0.6569031550291291", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "d9886c19ac3882dfcca523aacb610850e9bba540", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-GioMkFmls0Q0+vVdu3EdhZPXvpMtYIvUWItqsymbtAWhVoWSUMfxFiSFXyVktT5VubXmrhj/0XrpPQ03HJlg4A==", "signatures": [{"sig": "MEUCIQCqLfitXZbLnGV3qziDLUZnjlGsST0S12Jr1mebtpbC4AIgDeBO3AT2h/eE+al3n9SrULz61jZtXcs7y6g2X9HXRZg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "294fb238ced1d96d379336ffbb27122a66dd1bb4", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.1_1748525371496_0.5600573672167914", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.2": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "cc7439ade123b2855caee424fadefed206851be9", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-RAs6IWz+bGnBZosAhBJlq3vCyDDidoLc2tfqZl1M5wu4cstoB0Pfj44Zep6RNUWUP9oYTl1Ud5z0fHVRibQRGw==", "signatures": [{"sig": "MEMCIE8JhQf6TNUqtjaKetWaZI68bEEr6Bd1J9MRUtDdiVcVAh8qbEHXRkS2tZVcC2Eskxjzbc7YnOmAPc32CkShv2cN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "702cea862db34b980e580f46314141c24024ee47", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.2_1748531665730_0.8465218297577533", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.3": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "dcfd501207b68ac8f51886d7b52bd86a693d7208", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-13y/tA/hp+mjIUcHIQ4rt36vEWW74/dllSC1WN/pMxhFnD689IBA4oNNpb03f/nMYyOzotIgmbtxzyjDHenicw==", "signatures": [{"sig": "MEUCIQC40qpAkbe36156wpTc3nte7Exq5G97BTXAGMt88hf4sQIgdB5xNSgYCZg0g/u64WonKtv96f/zTO0bRK7N9ehT7pI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "4bc72143fd73614d12ea7bf38a9e223f60066da3", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.3_1748589426466_0.4319897391731664", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.4": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "cb7b4028adba67babbe8244682b2f2139cb1bb0b", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-7MxBvKyCzZROFptLBZbD9YJP6Grxp3TcxKpystj8gRTU/kA1S79N0Xfx4mZEhMQyfk7+/sLntN+z9LOLT6PC5Q==", "signatures": [{"sig": "MEYCIQC/VaHbKMyI1hfqt5GRYiCV8yleCzqsfRc6xlFdWwzctwIhAI+PqANp8nlUqatcCZ5lQZpvP6ObQkVXusLT4ir+sx8h", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "e4933170f75b3a8a0c9bf3985fb4d2ddb6e4b4c6", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.4_1748679792470_0.6883307367620308", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.5": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "e62f3b1719093e7eb3c0edb0110334e053305d7e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-kaksF/BtJlp/Qgv9Bkqu06ejYpxsXg/QhohjFydldEK2zWPcwYCYpEDLO+KsPLteMpcDeMm1iKXV6UddP7j2FA==", "signatures": [{"sig": "MEUCIQDhPNcz433rRLArz04/HJCq5u1U1zTqwmPpa2QY3/OXlQIgB+nkAKxVN8mJfI8ERJbY1mOpW5cmjWO9FJK9Npg5fXc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "9d46857e1377980bf4878fb273d5ef3848075bb5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.5_1748777264918_0.41568225295321914", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1-alpha.6": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "9b46777ca2abacbb7ffd96266def979df3a85f57", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-5IlJSC+kyb39FO94FEo0+RZgCXwcCoVUxhCaTqJEwDjwuFq/4bLmnq6VsfHlR/0ls0G5p+x5TLjRL8LS5qYmAg==", "signatures": [{"sig": "MEUCIQDAjofuRHlo3AS8Wsx8dBK8SX96iOlYoRWyBuzN8wGywAIgByp4r+FiSigNbbvAvJAQU5NPhhy+KYifiYWc97WMr6M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c14bcac24268636dddc8c75f85f66b42e8dbbf76", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1-alpha.6_1748873584068_0.9578020231553939", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "7836afcc097a4657a5ed56670851a450d8b70ab8", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1.tgz", "fileCount": 12, "integrity": "sha512-STAQsGYbHCF0/e+ShUQ4EatXQ7ceh3fBCXkNU7/MZVKulrlq1usH7t2FhxvCpuCi5O5oi1vmVaAjrGeL71OK1g==", "signatures": [{"sig": "MEUCIQCvteOXnf8dK3xRf3+ji0GsQ2nijtXOn8I6adEY/tuRBQIgGqiuX7N5LRe4hMetGyfxBd/lEDiVDMZPSti0GCsoZSo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9604}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "936f35022c1e1357da82c4b958b7bff2563e2075", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.1_1748884767164_0.411575528015945", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "66500bc9e6f9b6a9998de578e6fa83bdbc9db3d3", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-VN0V8lGGd8e7sfYubaBg7pMqE9rY1zoE1bjpNhdP04ef6HIA8x6N1Eb2w4DnfJJ6pVup4vYss6c0TtMZ1XOADw==", "signatures": [{"sig": "MEUCIQCLjaVHYDDXHzQyCqXMGN50bbahu4jNi+9JYAsJqzSllgIgXrMsd6TfLAwSg3cD1K+mk0RZqQfp1iMBhBus6Xk9iHg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "936f35022c1e1357da82c4b958b7bff2563e2075", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.0_1748885520357_0.02773290817605778", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "507e6d9f11b9bca9a21b97eb8115ffdf792e1bd8", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-nFJrOEZ89cuRA1nL46ZghAZjA+jmcArhD8uuFjx0oYmjpe3RRv7qecmE7RmzFwO+sg2LdPt2sGSWCYE0hTH6Fw==", "signatures": [{"sig": "MEQCIBGOWjo5zofiFyXfFpf2p/Lt7OVmcsukW4AwJpedgvblAiBWZDji6yPS8gHYQXXM4ParMPLJTCm0xMIqJZyWdTblRg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "0f5c59c1b116ced6aaff7b2c632f924b2ca49596", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.1_1749140991196_0.7583675494619504", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.2": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "ec5721109812564eb610e2b7b83f7005af5f75bd", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-xPin8T+KCZcnnZFn8vEbmiT464dci8I5ZR6atDUhRDQMd35TMZUrxqAAWE0a35txV819W3RaFDUb7CzHUF4PyQ==", "signatures": [{"sig": "MEQCIG7+5S8VjlDZApm4DEBeGyv22SrFm1d39Zy//Txgk48SAiA6A16WBVAxNb+d1k4HIHc494PC5yhFItleaVmOWc8W8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f5eb171473a248fd076c5a223c41643f144d4787", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.2_1749212272519_0.6945467543587203", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.3": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "ef7881e123cf79d536c4ee14de56e1778641586f", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-atJhJnVThyiOOYrgoLwt+bB5AKQgNlHHG07x/JP+Fgg2fuoTnDCHC+rBTegDBXoXz2aq9sIXEOzZyo7unY+PPw==", "signatures": [{"sig": "MEQCIDQUyxA8PVaq/rgRQCH9TskQWa44Ey2BLVyr1vdivkriAiAJGICLvVjU4FGE7T4AJ/NmSWy7e/MOnhh9M75syQpxuw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "1e0ba6253cdecbbb69b37537599aad9a21ed310e", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.3_1749265209931_0.1313001175819224", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.4": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "0e1f320807166d088e3ecc2ae2ce46aa88f00954", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-yVg9trKsFYXb3nzmWU/RQXO6Dz2V73EGT+O/6t/cBj+cSI0tsaW8vknk11iteitITq4/8DCFSwNs9j5G4BfvEw==", "signatures": [{"sig": "MEUCIHIupaxDCK5ioveLvHpZgv6QJjR5ktrV6ItMCOi0T0xZAiEAqrCEks8SnhoTtQExCJab88BbU2CHTXs8ozRyTLgRyyY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2df6f99883b8ed2e762c602f80a05c854aef87dc", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.4_1749453079003_0.8660659479121715", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.5": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "f9a9c71735598bb213c4ac000e84876d5705281c", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-7iZ0tF9KQhWkiSvENAhbpuTfIk/v0FQG7byNogPs/mzavzuubCBhgz2Rwx9zdOCIdAkZRObKixyXd4MmcKXm2g==", "signatures": [{"sig": "MEUCIGqjk2fqZYz+fmsLlx9u4KRlylEN+7TQJ2OPMKCOiZJaAiEA2NoVP4npUGA5dTLK1liQgrreFPiIFru8CjHiMmpAolY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d159a211d3970978f7311e5227462f30a53a060c", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.5_1749453894493_0.8608134962836254", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.6": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "d3ecbde6f972939505d761aea3f38ea1b3a2fc4e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-7YjMc8rdlTfbr18i93LJaedLmQQW8SzpepwGGDIScFPpdLHedZWUuVYkXC+HZlGiPg79fXPt1a26T54N2aoA3w==", "signatures": [{"sig": "MEQCIH8IC1ayJ/PDIohIayY3kxBfQL0ypwNazOjZoJ9hhnWKAiAj+rOLMn02TLAA/isUlWvFdVph7TOHyqet9xoJPiUAOA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "685e530478362c9e5a43db01aadc200a361cbc6f", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.6_1749454646660_0.8019148200098327", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.7": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.7", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "3b530386f4c0c74b58273a46ff6486a6a0c84b8f", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.7.tgz", "fileCount": 12, "integrity": "sha512-2fM2memumqoml3OBJuVHVG3UuMbMMYBfZPKBVQ9d+Is4s0suQjxwNwCLa71P3Tj8MSviL8l187ZLmcNFtRuavg==", "signatures": [{"sig": "MEQCIDAXnKE19O3K74Y+wiSXeuXnCwyJoJLNYJYq7A1eJJXzAiAm9kcLFOA3OVdGAiL5DuRLMl4Vz9uLcWuy9WTadoLxlw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "b2eded83f68d8e1685d1ea5e826bbfa891086b7f", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.7_1749455057836_0.3961681854842498", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.8": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.8", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "156acebdf49d4a631ee165f081950bc95c141dcc", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.8.tgz", "fileCount": 12, "integrity": "sha512-OSuUD/8xh/KjSUpctxJIi1sI1iAT8vLQQ8dziuiQyXypuWhZF2h55n4CsXima7gLH33LrF2nrztP7uJhL30eOw==", "signatures": [{"sig": "MEYCIQCJP4+1wo8P+fX02kYLfrDga7iLWxshBfvxR7QjvIivYQIhAOBm+T46iphICfq74kfnCX12VPqhZeEDVOiKSzDN70WF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8208974140a1e658e5234435836476642e9a56e1", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.8_1749468587425_0.9413547647326248", "host": "s3://npm-registry-packages-npm-production"}}, "8.33.2-alpha.9": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.33.2-alpha.9", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.33.2-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "436b6e0358d578de21e915439dc9d74452f5740c", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.2-alpha.9.tgz", "fileCount": 12, "integrity": "sha512-e<PERSON><PERSON><PERSON>+/NDrsKRzUfpyr1TCWSATG6Q5Pqa0XvXlWTttFIQ6hzBEHbWWwnMFw06i3Rj5jbwcBLNBfDVDfD/Pp1w==", "signatures": [{"sig": "MEQCICh837iQVn7TLBJWo0AQCP0bdP1MKPvii+dV+XMgzlvrAiA7QNcdk7iAE3W15fF+wrYPlIMisHNPWyunR+jRjpI/Uw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.33.2-alpha.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "9b598778b4033e2cd5bdf3fa6e1af32c84a34a0b", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.33.2-alpha.9_1749468989460_0.6163024243278681", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "97d0a24e89a355e9308cebc8e23f255669bf0979", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.0.tgz", "fileCount": 12, "integrity": "sha512-+W9VYHKFIzA5cBeooqQxqNriAP0QeQ7xTiDuIOr71hzgffm3EL2hxwWBIIj4GuofIbKxGNarpKqIq6Q6YrShOA==", "signatures": [{"sig": "MEUCIQCPOOFYwgSY4xM5q7X6zT9xl+MqfppbMO5Xq+6xFBs11AIgIB8nX19lRS/watZFxPUKbMmJ+ReO54OnSHD35mAc7A4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9604}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8915a477608892596fc6ed2bc45dbbac7f41a361", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.0_1749489519462_0.7762563431648724", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "1c7c358fcf5635484b16508d2e658b5c2e61ae36", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-/lOX9i8+9jT6LCyMXXzRfE8qsxjKOMeEE6G5i7tpDNTeY4eJEvsf6OITv1RVIdeCMDMsZdF4shAm17aSFlLomw==", "signatures": [{"sig": "MEUCIGX1Tn6L7Fa9GYCm+VeZZMvEO1M41rQthTzWp42uHpV7AiEAitarfJW26bcZY5c18SiGjCZfEoB5iXCCmcmZu5B5cqA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8915a477608892596fc6ed2bc45dbbac7f41a361", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.0_1749490271218_0.9980693189958099", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "57eeb6a5d0e6c865b4aa0811a5e351ab280cae2d", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-o2y/FaOoJyCoTApmUanmb3ooGOuVavhA/tVu7fh9DIJgYeZU/qfu8pHIeHYcZwFozu8dIwnMO1VV2TSCAvFCIQ==", "signatures": [{"sig": "MEQCIC6DxZt4Xguqko56GNvc+glu059go+vNcMB23OyEJtcSAiAr9MuL2pwRxY+my1zw2gqnNrs6ZM8rvkjK26vsE9IoGA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "445514aa1c9a2927051d73a7c0c4a1d004a7f855", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.1_1749597965082_0.020529762283088226", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.2": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "fe844e3cfdf013d6a743c54d41aa462b195b3a9f", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-JvBo+vvjfYscg8yxvQ4enSH9ZMoAaTmdpQo8Z+NVGFzCI1IkN9X3sJlusOEIaRlVVSh4iR+jVgxqZ2xpuWqxzg==", "signatures": [{"sig": "MEUCIQC6u1Qc/MgpfmyAdxRyxz+Kw56tK87HrVPJij8ANZ+/ywIgNFvgNV50cLrsQprZuJaz7tpM/8MzAvX65cLl2AiPEJw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "de8943e98e188d83801ec1044ffc69451db1aa63", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.2_1749859059796_0.714888069855051", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.3": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "0b313a9a9b59d985e232d79c37a2cadc1087a2cf", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-d/7dHvp79/9dsgcppBfk0+gV4tBG9SozmHIkvdwfRH+xr6uXQ6E7uTCo1Ho+vg6TKqN5vSNxFrf3zo49PwQh2w==", "signatures": [{"sig": "MEQCIAVC5QUz1lW0UIGtzk0QirLGnMtlpH7+OJrD4tdRPFFxAiBUAG4Bp2eQvcJmCHZWvojG0o94LNruWT1o2Yuylcj1vw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "af94f163a1d6447a84c5571fff5e38e4c700edb9", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.3_1749938160375_0.9639457444023332", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.4": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "2b67632695f1f164ee6b5ee74907ffe7b1b8fa64", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-ju03u7vZfKvP0q2jGATX+OL0xSdSWliWtirZaNNtRCyaF2Hanz0kOtmfrVFtUI1LAftjSnJoaRl8H4pMvGdNwA==", "signatures": [{"sig": "MEYCIQDsA4WwGHZ800Yah7xPAnE91olJVycTVhRLDenz90NGbwIhAPPTmUV3YRNbMYp7RFgLDAnxVMejqv+OzsrERemzd8Pv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c971881e7af3e8e91f45391e6653a2815f725c3a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.4_1750073650445_0.9207043245138209", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.5": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "af2b0037e0c8ada65e77664f5aeec5f0ed138324", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-WHcdyIv4aS/kEcFZLuuYExR0bTwHF+jXuKgu07jwov3NWjYpbSsXml2w0Uz5w02ST7q9IaJge0EOxfmL/65u+Q==", "signatures": [{"sig": "MEYCIQDF6bMvn1TxC/xEPSm2HM/F4jTQ/ZJtuJTOAssxcqJ2HwIhAINxZwyP+3j3QpwZ56eGkTHD03Ys9ua07kEopldgZp9n", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f9d4d10c2330b6d646eff148b7648a84b7387a1e", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.5_1750081233575_0.2114528466796226", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.6": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "9f9de1f37c8b93948a7662f19fe6122e374d8e0c", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-TBX/Xh1PDexJYfTFU3S3Mr+iHKWSxsbXSvu7/y6ilytjFBN/U6CIh2NYL/F9FDo2k08c/DQsBCsBH1Q9P12C1A==", "signatures": [{"sig": "MEUCIBXANzPGVeuLNKKXKwCgxjEw4gbqTrDRHnmvb03QZQ25AiEAqlyDI7NU1FJXIYjFqryVTzOiMaQ/a4PhBnwW1oCn6Gs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8a69a33ab1e22c7e4b3727aa004fb58b98fd4a3b", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.6_1750082005799_0.8106335687493249", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.7": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.7", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "9ba21e4193d61355403ee333242701e2a08bf786", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.7.tgz", "fileCount": 12, "integrity": "sha512-nQDSLb5MnjyM7zjL+aa9209f//j2f4L5Wiq5uApHEn6A7Em6tBx+jXha/NzC9quHZ/tSsij3aoGH+PGnGovMVg==", "signatures": [{"sig": "MEUCIQCzZPYvvEC8CdLUzelJPoUgNE89o06z4ouG7Zbt6C4LmgIgaN498P0p8XIYgNws8Z9dpSjIUyfLa8H+pYKQcapMwFg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2fbae4863fc39279f61ba77bfe01e080a5796072", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.7_1750082813533_0.3102222974419444", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1-alpha.8": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1-alpha.8", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "b69f74858773eb0d6995d7df962d14e17503a868", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1-alpha.8.tgz", "fileCount": 12, "integrity": "sha512-JXXj1Xu/VjbshtBuiMfcNWw4mqi+MwtEPq8+yKcnM6otTfmMlrQhvsEJKjF+ifmLvgIG3wua342dpBFO0FxxNQ==", "signatures": [{"sig": "MEUCIQDROgFNgBUj+DM9dWWvj5bTB+2KAXMiyPnh+a6UJXBSwAIgIEkSW2oRmW2sL0v0pkFLexiiRPg0goaMA27x5+wSXyU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.1-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3a8369d2c5798ef3187c8ff412d409e2d5e17726", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1-alpha.8_1750083513081_0.8467695857391055", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "d6abb1b1e9f1f1c83ac92051c8fbf2dbc4dc9f5e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1.tgz", "fileCount": 12, "integrity": "sha512-K4Sjdo4/xF9NEeA2khOb7Y5nY6NSXBnod87uniVYW9kHP+hNlDV8trUSFeynA2uxWam4gIWgWoygPrv9VMWrYg==", "signatures": [{"sig": "MEUCIQCLRDJtrp4DafTh2sXYaV1qHXNEyo3qwD7DrBfdrnoM3AIgE1u+TJnKUzjnh+N2EBLLxRZGnlBaOZEFB8O+Z0lPn/g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9604}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "ccd07914d933c3f7a284c9a7df5b1d6d40495fc5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.1_1750094360959_0.8049441470037613", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.2-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.2-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "64a87301e2654a502c67b56c951f847cb0689beb", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.2-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-GVRnKLkcTbzOjy+pdHjpHSVLGWYIfMVW+KJixujYIdGHGh6o1ETNA+qeI80VFJp85vtuVa5t/+LlUkYZHQkY8Q==", "signatures": [{"sig": "MEQCIBpf7odXewS6DKkT5hMQFiHr0DBsbOp+Jxyek/g99FcAAiBzIoeJJQ+YVGRYwCmRPpB9CWxW5HSaFHiNy04SBUro1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "ccd07914d933c3f7a284c9a7df5b1d6d40495fc5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.2-alpha.0_1750095130025_0.771375075394461", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.2-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.2-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "b36074c1ad7c432955e0d9a8464155e2f3adaf9e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.2-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-Vg+ltryG7tiUey23AoJIVPpw0FYF5vEI1z0NFE5CXeM8cRcyJjLQfKsvQBluCD/3jFALur89rk4hKgveww/7ow==", "signatures": [{"sig": "MEQCIAJmapYytY90BjBSYnG73VzQ23KEbjz34MX/0ClwWxFCAiB0UO/9P2usJfhZORoIJCYq7V149QLoHemO8y2Dpnq2cQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "76cc62cb37353711b64e3353bb62e2e0a688d629", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.2-alpha.1_1750212370709_0.6336503435413252", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.2": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.2-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.2-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "57d532eb186f31c32c1669d74ac0e20cc69a9838", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.2-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-pqKfCeuVO+/37FnnXckf3879NjwpEZ42GqBF6KOnYoS0JmE6TSpGImPVFbBNPQ2CLeD67tUk0maLe8NZ1x4mlA==", "signatures": [{"sig": "MEYCIQDxVfWEsyFeTRV6V4DnXJ0VtQokVL8+HI0kVq/HimGC0AIhALIyjfU6KXT3pC/jSiSMqx/oUmOF5N9MpHmsTZTiR7hh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2e35e3a7bf03654730039ec432cbf445819057fd", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.2-alpha.2_1750696933944_0.002076666741538613", "host": "s3://npm-registry-packages-npm-production"}}, "8.34.2-alpha.3": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.34.2-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.34.2-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "ffa8a4605eb2d93bb7541c8078bd77d4921e0a09", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.2-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-poKYQ6ZdHanR9FQGULjfCJZspDY45hJVdvy0FhUqsVYp7XfSfZD3XJsxq0sHa9aJLpX+ttXv5rwfcSjXNQyXKw==", "signatures": [{"sig": "MEYCIQDXb076mbJbkT441a8YG/ZXudWR4ptqIGXADHhIqaly5gIhAKmDC5FARiyj84rYQQXYkzGxF+AtWcl4JPKU9fzQhZEC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.34.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c273e038fbd525232a8896786db28e9705cf205a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.34.2-alpha.3_1750698777787_0.17694620345413314", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "6e05aeb999999e31d562ceb4fe144f3cbfbd670e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.0.tgz", "fileCount": 12, "integrity": "sha512-04k/7247kZzFraweuEirmvUj+W3bJLI9fX6fbo1Qm2YykuBvEhRTPl8tcxlYO8kZZW+HIXfkZNoasVb8EV4jpA==", "signatures": [{"sig": "MEUCICumoKtBrHEVD5BnbWhye4Trlub3DVAMiBcsv8tMG1aPAiEA4bUTfwk9Lucj9s36w/0nh7/9HRYJqbBuOApH9dz3YHk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9604}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d19c9f383a2e345656b601aa42ec250293609019", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.0_1750699167403_0.17152891905452483", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "175f889798f481a5616dc00592adf549bccd181f", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-cxNOInrNzZR2hNFHYx67hVwP74gtYqFZdBgrX/yKZRQADjuvdnSMS8jE8162pmgsyz1IqJcTNU1QYrxQE+mbMA==", "signatures": [{"sig": "MEUCIGFr9voOtYa6DNTpduxYsa56IDjYdHqV7BHg9Nny35VWAiEA2zQfjaxiDSwwTpelTCvAgS8QYsl9WW+Z7O+ICoU6LWw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f000a1f0c77c8275ffe7ea92e04a94275e73396d", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.0_1750699565027_0.16265498006980472", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "201ff5df9fadaa387b07de97057af585b2736bbd", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-Sx4LsZvnrreR792llYcyguvOSsvgMR6mbwNCxpYGK6G1IUnEWmCK1UIV/2LysQEZ2hZJsFyEs/kokabESCsqFA==", "signatures": [{"sig": "MEYCIQCB/51zjX+ACRKyyirsK4rLQ97FENxys6o2a880DikuEwIhAMH5w8C3gtKDQOOykhrXAlwUWevEZUEx9/nPdZ2q75Hp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "d19c9f383a2e345656b601aa42ec250293609019", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.1_1750700357490_0.6542506686307521", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.2": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "aebbd82b06b63e2acd39291f9bb14536bc893cf7", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-fGSua2NVZUWYQoCo0mxau3M/ipdBsmfvrT+MiIWrqdpQNeFyZXgf0f3+Zlc4/N6DNtbIOPOOd3awZiVM7sNm3g==", "signatures": [{"sig": "MEYCIQCjEKkyH0T15/1RhPvwtKvzD6bXBYkmCwDcYVob9wGB3AIhAN/I+A5MarNiuJvECok4nRm44ec6tnmAhiWysJPTa1Hp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3ecb96f2d2203b65ce0f2e2eb48c1ff5e875bdae", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.2_1750802806166_0.39785370970285605", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.3": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "16b1f8b7e5cb4a2a91dde4faa1d418e4cc95420e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-R+uyfpuUxy9kgA5bTnt6VmMt3I639xHxOeySLTTPYknLf8UjrkGbjE0HRT96jR06qx/RolqMIwFOxd8OliWpcQ==", "signatures": [{"sig": "MEYCIQDTUsV8ox5z1wUGvb8X+laa1blcGJx2Qn24nf57jlXxRQIhAP8PCnCSr+yDa2LfPsYoSeXwepoU6KvjQTCnfniGo8tJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "9dc91aaff7c55082a7a768788664d625f8283500", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.3_1750803337426_0.029201423496925782", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.4": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "67b7df78fc27eedf45625c8d02c23d60eb1f28e1", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-UWxEY/E2bNLNFaBf1Xp+K7wwBcbfWblZpTGZNkRi+yTRJ93A0zUl+RIim2/ieUaKX9xxX8HFtZBuQcsCxtQsNA==", "signatures": [{"sig": "MEQCICa+My9EcoczKajt35TiQ2SrRCBu3ZxPNkryEdqmHok1AiAECg7XmyvyGwnB/Bq9DLVz9uiUfsKpQNl9hfXXhMEEjg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3e545207f0e34611f528128fc699b25091bc40b3", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.4_1750853718835_0.6956022578115202", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.5": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "81dfde5e5eb198c7aa6b4797bcf583ef3ce8e7f9", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-lZPje2S6nD9U8G9YwIM3PYxCxL/MLCAcSn7tmoySaB/Vlo3UJ9tcQEDO5JDc5dFdP59LRUDBY0RtQUAEwgzIeQ==", "signatures": [{"sig": "MEQCIETdwlU+Ja7IYhWzKYOeJh9YT29G3xQGMDT/KvSpCeKpAiBOzBPCM+8dtwsVr1pqhB7ckxy0bTJSUBuvYvn5Rx2esg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "20f8564b82b225c8d685f5f06284516f1f22b32a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.5_1750943215266_0.6854091325898393", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1-alpha.6": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "035d99e5583b3eb5ee375e76f784a80b01c632c6", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-0x1dG7gQ1l9/Yu+O6mndNUvK2F2sUQb+oaYimPeQklvrcweuqFkhH3RpiYscaKkDjKgw4Ql+ZimGW+a6/0KvQQ==", "signatures": [{"sig": "MEYCIQCeZX1zW9S3CaCiTjzYHsvvDbdAvJ5DGLqMO5AAcdVkeAIhAKo3h2PpLaZ05B9LgaY9ARJkq5x7mFx6IUXmxMh75yWc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "db32b8a82d58eddb29be207a5f4476644973abbf", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1-alpha.6_1750945524942_0.07880627590629952", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "c2db8714c181cc0700216c1a2e3cf55719c58006", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1.tgz", "fileCount": 12, "integrity": "sha512-K5/U9VmT9dTHoNowWZpz+/TObS3xqC5h0xAIjXPw+MNcKV9qg6eSatEnmeAwkjHijhACH0/N7bkhKvbt1+DXWQ==", "signatures": [{"sig": "MEUCIQDsiHvxjs+iqwKvh1ui127oR9CmQHZqFIy4Dxc7Bh4PXQIgMnXstn9O6arcKkKaROw6RsMOKd7CjVhzVtv2rMPq3tU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9604}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f9bd7d86fc39eb2957de7eefdcd3ab9b6c9dc4a7", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.1_1751303929036_0.7170117489262648", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.0": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.0", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "d9577dadf7557cbf1502adbe24e80724bec699f3", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-UtMn5jlJc6gz2iVocKNT88eYLRsKWwqUSC91I4ChywLWm8pSj4yXN8NT7LbohiLf7T3KZbqiQX8ag5jObXhEsQ==", "signatures": [{"sig": "MEUCIQCK3EGQmX/tjB0nWnlczSG6AcqV9HD8vy+JLQ9VmQS4owIgYN8aJV1iFUu1tAoDYwTBdpoclfRV2X3rtCxdqKdKERI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "e2ecca60d9e8486bb13e98b3e1a65d529bedef03", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.2-alpha.0_1751361996071_0.6874155663179855", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.1": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "100535137aabd882005cc0d7f0915fa03254651e", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-sAbbW76rWzOpRmUbjgdQhFz3vaFBENe+lnbpPvqMSgjib2ba4d8zgcVW9LA0f3hx9CFQE+hRooRn+3zlHgNTpg==", "signatures": [{"sig": "MEUCIQCRBg11A9/YEzVvOPnOAPGuEiEF2Kd/FNZODFpTV2VOgQIgJF7dXU5NeP6nHdImnd6NMrbNLsQMXYQCLAa3kOBmrJI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f391560ce3edeeecde056420284799124ef1d244", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.2-alpha.1_1751368076068_0.9922697868100023", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.2": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "69e1827e6f9bc4adf86ea42198327c5bdb4b40c2", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-MejSWhCb4qW3cvCd4MF47V8f4k6GYtfUK8Q+FPGAPJ9RS7cyqyATUs6okDTWD0qh8GhRpIgYvh+IQ7DOfNJYxQ==", "signatures": [{"sig": "MEYCIQD1XeURbYVVBpHygedqUyPEeZ6Pnrg3WCee9KM5ZDZ6GAIhAP8X4V7qKTDqfmFVNv3RJFKVQ3bZiK5QHTMWpa2dNlat", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "50a3ab6ea8ea44691ff8fbe16c1a4f46950fca34", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.2-alpha.2_1751369533182_0.05581557160384776", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.3": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "23855a8e71b09cf11a224659fb8477bc213e78af", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-fr+NQfmWY+NjlRGA8BiS8PeOFAot2wBOILZ5HclWDgAh8bLXO5ACj7izpsBJXzHMiH95qH3Pq0jnmpfkFZWrzQ==", "signatures": [{"sig": "MEUCIDje/fX+gTlM4c/TLiTnBYeznz4XaiLH8Syppg7wxx7xAiEArR7LwWfjOjOBF1hBTLdIF3WJtUx9QZA0Nbe3utk5QRA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "cefd4a93d80f67a9b8a892529dee4ff96adafcfd", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.2-alpha.3_1751369971945_0.16587968307856404", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.4": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "9faccecf6c827b219c750fb875cc59a5e40a3738", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-jlLrMOId60GiPiJ4mhsi4vHYjrj87Qc7cPsR+mZVJYiNzuNGHHEM2f6R3DFjzMcwEpcVDKnVU/BMgOo4FUwfZA==", "signatures": [{"sig": "MEYCIQDnAuhQn6hWbuPxZoHlEtVvIZBvMlUDqBCUN49lrIf9uAIhAIMXFLIU5vkOP/k8+7GD9NRdjA87XppCK+jbhRtUGM+z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c14c98e8f7487d7cd13551b34f039dc278a40092", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.2-alpha.4_1751371732057_0.32835203559313975", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.5": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "dist": {"shasum": "84319739c1e9dd306ed04bc1beaed882f01de75f", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-pzmPNnhFBuV09EiW3e8BFd9sMzRMNJcVfjzDLXxE5yE5LCemtL4iGuAQjXmR9KQBXP/scV1rFjC6g8x5CUrs1A==", "signatures": [{"sig": "MEUCIQCLADdXMnR+v6GfkyqXz5s/j97xH/f8OKnqMn8PcfRMzwIgWoFaRCeRCdZZ4aBlkY+EnmHcdCzH/YBS/HQWiAqmf7M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9612}, "type": "commonjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "5b378e290e56eefe638f22444d55bc5c0744c28c", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "_npmVersion": "10.8.2", "description": "Utilities for collecting TSConfigs for linting scenarios.", "directories": {}, "_nodeVersion": "20.19.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/tsconfig-utils_8.35.2-alpha.5_1751378282683_0.4778240610637987", "host": "s3://npm-registry-packages-npm-production"}}, "8.35.2-alpha.6": {"name": "@typescript-eslint/tsconfig-utils", "version": "8.35.2-alpha.6", "description": "Utilities for collecting TSConfigs for linting scenarios.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/tsconfig-utils"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "tsconfig-utils", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/tsconfig-utils@8.35.2-alpha.6", "readmeFilename": "README.md", "gitHead": "7ec793193d7b9c6c9928191e462c54b10b177723", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vW0ZT31OCYvJeNmsI7Mc6dclpHB8MJQdmi3C58DoXPLaHs0IPv66zz4263r+XWc8Rf9exCNAk5YD0lZX3Jsf3A==", "shasum": "9909221f9ac8ff544640bff54623de3b00983cb1", "tarball": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.2-alpha.6.tgz", "fileCount": 12, "unpackedSize": 9612, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2ftsconfig-utils@8.35.2-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEbF8inAdNv3X+azrkcNGYQTPx6A5Sc1DHswVbiPZWA3AiA8OkOlOZJqnEcRjflrX6UWHtaBmqKeW3seINjOYdDS4A=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tsconfig-utils_8.35.2-alpha.6_1751735792423_0.840536764899046"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-05-27T13:55:27.902Z", "modified": "2025-07-05T17:16:33.052Z", "8.32.2-alpha.12": "2025-05-27T13:55:28.167Z", "8.33.0": "2025-05-27T17:21:33.722Z", "8.33.1-alpha.0": "2025-05-29T13:16:24.018Z", "8.33.1-alpha.1": "2025-05-29T13:29:31.662Z", "8.33.1-alpha.2": "2025-05-29T15:14:25.940Z", "8.33.1-alpha.3": "2025-05-30T07:17:06.642Z", "8.33.1-alpha.4": "2025-05-31T08:23:12.654Z", "8.33.1-alpha.5": "2025-06-01T11:27:45.110Z", "8.33.1-alpha.6": "2025-06-02T14:13:04.267Z", "8.33.1": "2025-06-02T17:19:27.342Z", "8.33.2-alpha.0": "2025-06-02T17:32:00.533Z", "8.33.2-alpha.1": "2025-06-05T16:29:51.379Z", "8.33.2-alpha.2": "2025-06-06T12:17:52.721Z", "8.33.2-alpha.3": "2025-06-07T03:00:10.128Z", "8.33.2-alpha.4": "2025-06-09T07:11:19.187Z", "8.33.2-alpha.5": "2025-06-09T07:24:54.668Z", "8.33.2-alpha.6": "2025-06-09T07:37:26.821Z", "8.33.2-alpha.7": "2025-06-09T07:44:18.015Z", "8.33.2-alpha.8": "2025-06-09T11:29:47.628Z", "8.33.2-alpha.9": "2025-06-09T11:36:29.667Z", "8.34.0": "2025-06-09T17:18:39.668Z", "8.34.1-alpha.0": "2025-06-09T17:31:11.403Z", "8.34.1-alpha.1": "2025-06-10T23:26:05.274Z", "8.34.1-alpha.2": "2025-06-13T23:57:39.964Z", "8.34.1-alpha.3": "2025-06-14T21:56:00.575Z", "8.34.1-alpha.4": "2025-06-16T11:34:10.645Z", "8.34.1-alpha.5": "2025-06-16T13:40:33.758Z", "8.34.1-alpha.6": "2025-06-16T13:53:25.955Z", "8.34.1-alpha.7": "2025-06-16T14:06:53.694Z", "8.34.1-alpha.8": "2025-06-16T14:18:33.260Z", "8.34.1": "2025-06-16T17:19:21.121Z", "8.34.2-alpha.0": "2025-06-16T17:32:10.224Z", "8.34.2-alpha.1": "2025-06-18T02:06:10.885Z", "8.34.2-alpha.2": "2025-06-23T16:42:14.131Z", "8.34.2-alpha.3": "2025-06-23T17:12:57.968Z", "8.35.0": "2025-06-23T17:19:27.588Z", "8.35.1-alpha.0": "2025-06-23T17:26:05.270Z", "8.35.1-alpha.1": "2025-06-23T17:39:17.667Z", "8.35.1-alpha.2": "2025-06-24T22:06:46.366Z", "8.35.1-alpha.3": "2025-06-24T22:15:37.604Z", "8.35.1-alpha.4": "2025-06-25T12:15:19.019Z", "8.35.1-alpha.5": "2025-06-26T13:06:55.453Z", "8.35.1-alpha.6": "2025-06-26T13:45:25.145Z", "8.35.1": "2025-06-30T17:18:49.217Z", "8.35.2-alpha.0": "2025-07-01T09:26:36.240Z", "8.35.2-alpha.1": "2025-07-01T11:07:56.299Z", "8.35.2-alpha.2": "2025-07-01T11:32:13.378Z", "8.35.2-alpha.3": "2025-07-01T11:39:32.121Z", "8.35.2-alpha.4": "2025-07-01T12:08:52.263Z", "8.35.2-alpha.5": "2025-07-01T13:58:02.886Z", "8.35.2-alpha.6": "2025-07-05T17:16:32.600Z"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "homepage": "https://typescript-eslint.io", "keywords": ["eslint", "typescript", "estree"], "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/tsconfig-utils"}, "description": "Utilities for collecting TSConfigs for linting scenarios.", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}