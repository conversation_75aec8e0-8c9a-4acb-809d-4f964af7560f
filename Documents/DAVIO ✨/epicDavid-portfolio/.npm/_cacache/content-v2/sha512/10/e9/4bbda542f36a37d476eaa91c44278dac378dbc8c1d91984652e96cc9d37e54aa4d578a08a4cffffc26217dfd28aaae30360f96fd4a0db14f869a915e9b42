{"_id": "@babel/plugin-transform-typescript", "_rev": "182-da2dd4c5507f187bbee9abc3656bb017", "name": "@babel/plugin-transform-typescript", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5b16524fcd41dba7bfebfa4e6d9231fd691cd758", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.4.tgz", "integrity": "sha512-X0/7kxMrV2BTkzl4NnM/6D5RYsaOoU0khLXQ5PtyECVtg19YMuWaefcIujwkksng8LI75JHVsQkJfJ7Pz1gtzg==", "signatures": [{"sig": "MEQCIARZyKEsHbsu1v360YCzbyyVtod5iv9Jj8ZV6ThYR1A2AiB9vDqmxsZXnaX3DwcreEmezDROAwofRGGlqL4z1TNJIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.4.tgz_1509388530245_0.33997616870328784", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.5", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "db9e7ee75eaaa1e19d400e3a4829c5d8623a8ce8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.5.tgz", "integrity": "sha512-Y0DXzBwNZnvVpYrmCvlpyCEvL93UOkDO4WS5YsFiP5jIc8yIIG16rGCmdBubSF/ZbBjx5e1QPMQIMXgMIl5MGw==", "signatures": [{"sig": "MEUCIQCE5fGzextHkkvkfJTpli7sJvITZndspyIQHwVf99Op+QIgDMWEZAco2eXZukMbjHdZpSDnfJHjyWOOJMdeUpK8/Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.5.tgz_1509397027347_0.04835393629036844", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.31", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ac8f039860be56c77c3ceac78af1a1fd2e945ffb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.31.tgz", "integrity": "sha512-rJ39b6IlN7lwfB5tRAKKiUOBBb1OXv6JmcLp7UENLJRf2F3V9W9AnJRbkOGqqRpoaPsCm0bFLB6QHjS6hui8Ig==", "signatures": [{"sig": "MEUCIEDo39rr7hmr1dNUeMGLxWpkvETLo/aYoFU9PmoYBzvIAiEA5DU1dYwAUDzsSvvbexOLUBDv1Bpu37Zk6FuLEJ3O8Ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.31.tgz_1509739439391_0.4076822097413242", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.32", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f26a27addac7ecebee89dcbd5b058e7a864d0d49", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.32.tgz", "integrity": "sha512-6xsDzdBrnvdouFVmmZLdUAttI0KNjalgfJd/GBheFoEq1SzEM3v4xZZrWXXfhZgECycltIumCJg1T7mKCl0q8g==", "signatures": [{"sig": "MEQCIBkclqCvCkY3SW3T3bG0lRjbBSia0tCxqy+R/f2sM+lMAiBS8jGHaiFdPLFu6DaLQ2cmm2ZhMBrB+iXJcF+euya4ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.32.tgz_1510493624463_0.07497515249997377", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.33", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2b43897d5be048aa33bea479d1623104fb6564e3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.33.tgz", "integrity": "sha512-ZqXvLBksZZSvadae2+aRMsq9ffTBLHqOzJGT8/6f1tJHaNjYnITzF9WYQc5kNZMm9DTBgbwHo3d2CwhELfaBcA==", "signatures": [{"sig": "MEQCIHfoWICy+Tera/6gF9rA0aiRpz1mpZvEUK2RDY2dJhDJAiBGKW/7yHctfQYBSMg2sur79P0C2xXU94ndf5/1d/so9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.33.tgz_1512138533834_0.47572432341985404", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.34", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5589d964cd1f343047339a8a2ccd4f5acc61b5de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.34.tgz", "integrity": "sha512-mLk8PKXgbkMitC/3XOL9WVlgUMYndYPWgE7nvvmgrmdJDYB/L1ckobRKNkyTTFq2mAEmEMMdZQ0r7YvBSdGW+g==", "signatures": [{"sig": "MEUCIHuIN4Feq2RFbS3j4TZZubcle0MlUVbZppK+ujDLNnp+AiEAv+X+hDuW9ocM8DXbs39Ho4Dxlsce9WjBc3aB2IntDtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.34.tgz_1512225593128_0.0017356581520289183", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.35", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0602f98d4e0cb291296e1b91e826ab123094e9bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.35.tgz", "integrity": "sha512-GnEqbmT+JeJXCf0TKT7+8jzAflSOGqEgo6e3nXLSaF9Juc5dh11SsYvP2LkFvzwa1+AlL1rKvNWBTt9acZWjEw==", "signatures": [{"sig": "MEYCIQDDWIJzZ+h+zAExgmYTRL11CbyIJV693uS+Zcb6BMx6xAIhAKmpq/MCHSccoyfp+ksGZG3a0Siu7aiiSkFOzdTDB76G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.35.tgz_1513288091764_0.6218197874259204", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.36", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ba35f7ef924a2adcb66c8e7a2c22f3d696363977", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.36.tgz", "integrity": "sha512-pUQAM7p4FAn17GfiAyr1CevO6n+lnLrB72JGEAEdHiGdWbqq8raaFhMF23+hlhPLnt3tX5xOgsMFOGvT77qkfw==", "signatures": [{"sig": "MEYCIQCBsoCy/VNcxgrVSgtaugcqA2q98fNdlifJngb/NtUoAwIhAInXK6SHfI8B50u0YdW82xvVs9E58GY0sCVo4cdcP8jA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.36.tgz_1514228715585_0.7264833939261734", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.37", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9dc4f006376ca64b1eb5f14ceb2e40d290770c38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.37.tgz", "integrity": "sha512-S/Iw7m1ZTB7LP7jbPSdnIaWFC8WnBWmLwnR90fcHS3GdUv9ehzE4x2Jc4F6iklKZGs8nHNQKiKufr0FJE73ukQ==", "signatures": [{"sig": "MEYCIQC4Q7jSynTbB3nHHZMltxVqXyVdr4dZszP8ZlwKgFS4/AIhALpVXG8GuavH61m/xwo5B1WjIFtnHwNXM9yLwIg9GjHV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.37.tgz_1515427376258_0.49959438294172287", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.38", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ff08fdcef11dc76915b7194524503b17820bf95c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.38.tgz", "integrity": "sha512-kt6rfuHHxfq3WVTMDAVAlOKWlvOMPwlXkp0gBzN2JwZBTiJUVI0c7bChoZfWhDhHZS6FBdAp/fv9lGbGAPp7Ag==", "signatures": [{"sig": "MEYCIQDOBQamdoiDdS/4uHhbBST7WW9BF5MDESjZuL3b6EzSzAIhAIkORZtJuZ7DwYFK/xeR5g2G0SSa7fMCVB8+UB8pHGZX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.38.tgz_1516206743265_0.20987737155519426", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.39", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "44282884d76056c2f1a05105bc3f3d8cbf1a7780", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.39.tgz", "integrity": "sha512-cUJl11G2iNVdywj3h87EphCM5x7Wy6dBtXVtopqpSPZv98EyYTY7vpEavMmPaoQ24LjT0guyMAHHWAe2VwPfVA==", "signatures": [{"sig": "MEQCIAURM08sy5/2eSpaIH6k7/WQDbO/QW259Fxwp6D+LhaRAiBqbokGsu4fUKgReMPsELR+a+BEE3dhGDK22hqJnr/IPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript-7.0.0-beta.39.tgz_1517344073027_0.20317129138857126", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.40", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "143c5e108bbf20d3e5cedacee035ea6656dc0794", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.40.tgz", "fileCount": 4, "integrity": "sha512-7MLJ5DU6j8B76yYrfhQvV4LKXOX5AQz4Z+SlZy7tUEiARilztqE1vBymju5FGBNCsWaE27lkHSaSXhtIBCjn6g==", "signatures": [{"sig": "MEQCIHRX39kfpYJHs9GxfrE+EbaTVDOR8n49CoFCDJnZsO5bAiBL3R6J3RHnGiXkxG6V/yd8rpCYhDXWC6YX0hO9xps/DQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15306}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.40_1518453733614_0.15267372944637114", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.41", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "abb9e900af77aeca7441afccd72634172d48de4d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.41.tgz", "fileCount": 4, "integrity": "sha512-XNm8kN8Ka7UE/o5/chZnkEi6fGiHBGohwGl9tkB4NFnuSEvyJMml8AC/yI+BL0T3njJJbA8ZiH2Nmslq1hjeQw==", "signatures": [{"sig": "MEQCICGRcKbdk+ddZ6AG6yl0exIZcKNcwnWteDoZAvejWmbvAiBmwWL1tN9EQwrK4crEsJXDPNlEF3jXmGowwvUyagHNMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16448}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-syntax-typescript": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.41_1521044796030_0.8554786039560496", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.42", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e3a2d46014fd26e0729fd574b521fca4eb21144f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.42.tgz", "fileCount": 4, "integrity": "sha512-gPuKyZJlfsoxYE+MSqYPcWgDanJKBu28TMFVcPML6d308tsxtv14pGrx504KkOLcO67yeFIsosBeg8h+JtM51Q==", "signatures": [{"sig": "MEYCIQCBpS+b5HE6jgoK/wnIFm1rtsCk8lzpPo3Fo49SnRx6uQIhAJkgi1YPdAZa+E1Y6f+enkY6Rau+KESNVfeQmjAnELmN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16448}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-syntax-typescript": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.42_1521147109776_0.673434875043982", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.43", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1122ef721ef0602de4fb81bc8a85966533b4181a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.43.tgz", "fileCount": 4, "integrity": "sha512-uNbnrXNOUnWYNVfw8ZQIdGpWwWSChOn3kCcxnihg67syxiO7DDBZME8nkHaem5+Aos40jx53cNSEnGuKDWN2Lw==", "signatures": [{"sig": "MEYCIQCu0dlzG17YXNlSNszz2zrPKwMIoKQ06S4k1ugDv9qnEQIhANXNV7Z6sqO6MaM2Kv85sVewD/REzoaHOm8MocXoXi96", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14990}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-syntax-typescript": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.43_1522687724724_0.5314081169682681", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.44", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "85f326ccef4a903581098b2cdcd0fddf78c7dd47", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.44.tgz", "fileCount": 4, "integrity": "sha512-9Y4ONucWed/cNM0xgdfC5AZdvV4gwfmVemNySLM+a7KdgTBQ2ZoSKQUVdXPIGWUJnfpnSpXp56Wzn0POKGXZrA==", "signatures": [{"sig": "MEQCICASgVaQJzztHR74/JAVp+scLq7WrfiaNZcXXDOB9n7TAiA3GUFBhF+GWaKg8aWfgL31/ZQYGB0PkMF+Gh3p9HUtvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17075}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-syntax-typescript": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.44_1522707625197_0.3150456630466969", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.45", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "183749e00f1e06f1fa2cf9bf5a0f58a853f4a0a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.45.tgz", "fileCount": 4, "integrity": "sha512-yGaTteFZMPf7tzk1qHIu1ZJKT38V9LM4CV6WpzTC7bIxJP49TJyTX8SI3fRj2I9EC4wev8u3llDutbj49FSPQg==", "signatures": [{"sig": "MEQCIAWNh1KTpnauxDgeYj7+zSThK/8J1QAxTyj3yzV0pSv2AiARLOf8ynS1CONST4zIVh7rfDPQnbXdNLHMBop+oYw4eA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2kCRA9TVsSAnZWagAAKIAP/3iE+QLT9BEAp8fa3iid\nfkXhypov8r7Frb8zO0wRxFrfTuQONLkjXeGlJGp8bsmHZAlaeWzDPiZzcQUX\n5CmRqRMGOErfpmPNmVQS7gtzB2681KSn/sXZx0rJXbMsMSulqNODPJVAOX2C\nc2cC8izHbiszOR019UATTep0DmsAep/pn3aMG276UlhTun4YrFSvOAWO6Z2O\ndi7D300CPfobbnZWZvvQR3Xpk2d7meHzXWVTgqididSeDqUVyR7Vm5nTxnBJ\nqKQGtbnP4GA6xpbjM6VBtmNNY4BCeZBD6ETPyWuVxmMj3Nz2dLZ+j1D/rOMY\nBp3JC0CYYDmw3qA7M6H6lEuGEBPeuiWrOYdtKicTs8tlP0rCMnd4mtrnr8U/\n5AwOKtDtstZAq/Slp3hMm7Xk8fWI7w0egq/TxF4Ic+6zXkHq36u5VJ9S0qv8\n2pd52eqFrn/8NAHOvyXWvqmrhGxKt/Bxm4dIvFlgSXC08fB1jHrJwmuCOYLo\nvRn9orfW1QHp3uSKMN33kRI/T8D/Gz+fZDtGuIOvk15arvIfAAHhUpS74UX3\nkOK+goEy3zzOS6iVwekOmiVGKL/7YQnrD0VnnBCuLb2Ik+ki75uhd+CMBFjA\n8uRnBAUejSn50Il//u68jfNMdDJdE8H2C6YFOUR9F+M90c0mPcOU4/CEvJxX\nbVM2\r\n=NVkf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/plugin-syntax-typescript": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.45_1524448676578_0.8189905785777902", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.46", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bcb57558c87de7ae2f4f3a36322934c799bf3774", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.46.tgz", "fileCount": 4, "integrity": "sha512-ZGz0fu3marpLquKgZvr9eN6yBdnjPOJmEKBRZ/s5kplrvaIMlYA/1AbWn1zTVCv4NV4V/zg/nQRIXrx2wjSjjA==", "signatures": [{"sig": "MEYCIQDtDkd7zHWlbcLQ477ylxFZGlEdmCBeX63Sjexhm37/0gIhANY9jktc7wdYFpAhw/7SeFrXg9794MtgScTC0rwVNcGz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHOCRA9TVsSAnZWagAAkwoP/3qEeGmWbvHTFC7Rwh5A\nqBT81dJzoc7lqxMouRwv44qUtZ3BXrjKCpNjw9/+HFAl68zruuG08vjyatWc\nQG5E1pFLVsqSk33gt7m/olGQMSGnGL5dOx3WpkSt9u82jsDZ4FxEjIiLABZ2\nSmSQ5bjbukP8aDAa2Y3ipGoZ6MQT7p34fyx5GA4IG6zHW0EantW9p49Ub3Is\naC8SxjZze0acvUcOqAg0gow6VtrjolAG3ALWhfoLf6/XQYwVPzZ2z3yYQsBO\nNn3Qzxo94LbQxnJYIY6Av7Y5litPCdnBZ+hBBkzXLHZ2TcVrM9Z13a7jzGCS\nBIPobTthv56fp3dGGo/f1GXeWD8Q8DAOkJ3yXETMSMp37El2PGkePhMFmkf4\naRAcB8J+k6xpVqSL3AEZAHcXZYuomcAhHk7W5uvFTNf5fA3XgQxuzMiFBooi\nHoRgJKdaldgyOlW16+7a/asckvffHMkeQ4yQvkjtmeo8OppPH1EbTYVg0PKX\nZSusCl41/4Fu3JWOGRCZgMwbSOutBk22CvYLYUxMRdh1byFtyCea9M58pL1x\ntAS+G6BTGa/2XAEEEKZERxX/H0yBpGjeapEOxh1lTBG+44fRMa3yxziwLY7y\ntoPMbYWh+9ukrNFojaQJztYe8VFVQ5k/j3GjtV57jMXpWLCIAW+D+Q2kAtiL\nrGnJ\r\n=lLTE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/plugin-syntax-typescript": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.46_1524457933120_0.012937106198006587", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.47", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f6f46bd4dd22edef4ae42d9d3770044eddb84dfa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.47.tgz", "fileCount": 4, "integrity": "sha512-UcwdRWpLcs+a/Xe8iJXtkMnU8y+wKUA7CDMmUvxHrrCc9UFoy7xv9CLEWdmNLBDze2VHW+joXis0WP7do8PqOw==", "signatures": [{"sig": "MEUCIQCnbNH4zbzCdnmsfjIQdmBro/9MR7g5nlI3Rh7fZo/6owIgMdUZzAZkPLCpyemNsEQrDl4OlekLCgI4qp4f+8yyIB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17108, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iVjCRA9TVsSAnZWagAADsEP/3xXbEE3XGHLpzjoGWKp\nBkPSZDuzFFRMArITjs5KYNvzZL3rd1skqc+OP92hR9/u8dwyOM7R7SGPe66u\nO7iXH3zzNfq/3eylh+xjZgMF49/QIZmKQo/GXyhkD37t7ULpqwhnEigr/MZE\n3a2y/MrQ8YvmXOYhw8Kp6f41i2nddg6qiIEY7JFP7V9fSI/xzTC8mVmZz9qx\nqbLHGY/fak2H+YvfZLyEl8RzJerkVp8JWp5w7Z8Fd51KaHBnMg2Kh8+UNKP9\nkcS3VAaFjNH6rEFTJKF5H3R18aQkBdqVijG+RNt5JrJA2AfvksK1W9koEdht\nZJpK0rx04labgQ10TnzVf7t2UQm4YtKZjRQrw9Pid2b67aV53Q5E1HSpApGI\nBpBlPouOIUS7KMAFdCGiZrInIIiHOtAO/OiquPsaek/Zj0VWL1qL6DvW1Ev3\n8mE03PhQFPnT+bdc/STi82Unxcb/L/yTPdSwsgYWmz+RnL8vlKY3sWXE+TdQ\np8WcxLoBLkalCuzvIX0gNhkDQNxRqqj2EdYEsO4kyzdNXebany8spjtno2M0\ndWGpdhQEVDZXcVDKEfwLBKaZrnl8Lwd4EIOo5/IcZIICXTJWqZx0gWa9SyG8\n2D0rQFd2o060rr52119OB5ALo/53ZWuZK+BjGP+0thoILIZ20auMdYX0laFe\nZx9D\r\n=m4/s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/plugin-syntax-typescript": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.47_1526343010643_0.7627107506288682", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.48", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b93f770df8aeac19d3d4f14da0504df5e90777bb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.48.tgz", "fileCount": 4, "integrity": "sha512-GnmUKhjMjzWxRtMuXyeUYYR0NHus0cNhlyDJd98GoEqGUm9f/yb6DP4dH1YPdCum9g7IWQdxBUG8L/n/RFvfgw==", "signatures": [{"sig": "MEQCIC9t2XHSSly+PDgAX5kB1QeHFuuhz7dVRhxWwXXHcleKAiBcMnKPrTcZCSa+HQKzY0zxRtFnsgyVoYcGsfez0H55bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFYCRA9TVsSAnZWagAAkGoP/jx662XZ8krXHkGs5Rwm\nGPkZY4oZ/DvIuL/8rqEHkCM+eo4OTNxnt0/uezR70AjlNZ3KMqPFiYv8sfNV\nGV6eSfoYa0WEIVgCliCOGmsiSTASV3EToXTTVPNhtTff1vj0PZX8WVB+6DV9\noHWt1a3WR9o10pYtWLwPc3rkf8m+oP2ds+y2bwDJqSvUTK+Z+SKiZNBEZDah\n7k/nazcBSsIwyl3DmTAnnZaSY/fvZHAMOCkL+U75c++u9Oef1DbQgLcR91IT\npk38tUhiDRbZ2aARXPpcj1OtZ1JpbrxL5cBn1LPKkyGDQ7VNWl6h4uepr1Ig\n/LJ0jjSl/dqAivEqe0IWxsK2upJeVZ+JWbsHn+djyiUhbkwYkb6qmrZJk1AW\nkQ3mvQ0flo1bFl1WNLAvLQ+mew9CYsbgLXz9bfe4WFRC7mWuApSI7O/S6uvl\nx3HJYOpUhHtMCL64YzM4K+FU2ykSEmOCbPMhLxiit9sM1mYt+65hK/QWO3RK\nF6V+HwttEFS3DcMiwio5q4NtNRO+JDZtfWCy1KCmotINqL5nKt2rzC+Fm1tI\npHraWwDYLWznqR31ttEJgNpPWHM67OHUYnQmN/iQBlUftcCbPkNH7Zt4l1a5\nITWCfPSyq9YatnK79Hz7lzAtMGidnr0p4UL/f1ht46ivMj54Q5goIdy4A5QX\nWHfK\r\n=RFLf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/plugin-syntax-typescript": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.48_1527189848051_0.9982673788480472", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.49", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1f81fb756e033df6c396b2fffc1ba573a97c8a19", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.49.tgz", "fileCount": 5, "integrity": "sha512-IDIzWFU9JnjGZtznPZ+iHDn1IQnl5IG6Umv90yTYBQysP3tPVvfqHrhazid098Xhw6whpjGlWVpn15xqYuNAfA==", "signatures": [{"sig": "MEQCIFhrphGkbjjvz2hy5TtGWH7qIYbjLrQ4Ov1Nr84TzH+IAiBEjNkUYFV7j0cNJ2NMrdV35DyVny/M1dFnm8wVzOL0Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPaCRA9TVsSAnZWagAArDgP/1uJchW29TKuXPDZi5mq\nBSBvX+z8WOGHlDjW21iT86ydFXC50SmK/68+deziXgqTIMLTw224TymMs6e+\n+dqaI6GIZCnKL1jDhorW4Mrr7+Au5oI35Azf94CqB26Q/yx53ZEEHTDOKrYi\nWX1C3E4+i/t/ObMrcO0rru7ploa8lswcEmIHuwuqR4S5tQ9s/7ybGMUS6ZOC\nY0CPIxrBrRLVxp8PMylPlGnGIHjvJCmcv0eiDo0bMgd6L2Hs+FAnI1RYDyJA\nM6JCWWyyrPWKj4sCKcjlJHY3oFC/iOHeBa2yiblzaNPzFLN5dp0gWLov0EHs\nVfxN3GNwVbY9Lxa+6WqHTuHnbgiB3s2f3ZAasK2we1PmJbVJCeJ1SeftP1Hv\nTVPzqbiPNGX02A3G1uXx6J+UntoTTNu+a6yDrtyWO6ox8pj2T7vhMHcFmwA2\nJQMxBTofgfg+Rdp3jPIkCBSWEaQakPvmL/IEEn8XBQiivUlr23UNNGheMGLF\nDVmyT4yRA0v/POX5ilpeho8FPATH3gXe0RCpMtgZW4xQY3SbBhR9YxMW5j+I\nuecM8JyW9WRbGWGqf9UDf1Fs8zx4fIMOMeF+2Zx0KG8ypj7zF236Dp8JvCxb\nHM2gM1BNJMrmBhojnu4orMSlzo9TBHMx40/UIG+4NAYRtX/ynNcZI5e8gUrk\nNLIi\r\n=z+3L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "1f81fb756e033df6c396b2fffc1ba573a97c8a19", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "3.10.10", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/plugin-syntax-typescript": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.49_1527264218086_0.41922773537111424", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.50", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8576afd88144bdddbca8da3a500f5c3a318ac45e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.50.tgz", "fileCount": 6, "integrity": "sha512-6fFNpSCTDt2DKNvnK/128FDkq1Eb63UCwglq4HtMRgM6YXpWNGmkdzeZdA5DjQ8pQGo7DQ9mRtqfnGNIw3KnLg==", "signatures": [{"sig": "MEUCIQChxDaTspB27GxWdm9KygH559d4Yu4IlPxtJGPxxPZPrwIgQBc3wAcWw0J1Nc4RU+qNTxuGXZlu89VpGbJ+JXp7VlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14261}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/plugin-syntax-typescript": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.50_1528832865915_0.07627277677290967", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.51", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1d9e5ba7bf93fe37483cc321dbc9a7ebba5ff35b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.51.tgz", "fileCount": 6, "integrity": "sha512-vcQnn6KTxZYecOacMBCNpWoaf3n2sAUmYkT+Ia3nx27xw+zkJS220YXprLdgIvs0H6mHpYD3yDi+V47DIOhRkA==", "signatures": [{"sig": "MEYCIQC0ByUMmkiP4kb5VTeDGm/i1AQsXjYXJcSxrLwMxzs5+QIhANYf1uhFRyS5Tjyh6PT+O+RyB3EytKPhYW9PT93RffuQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14275}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/plugin-syntax-typescript": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.51_1528838422503_0.05239254011079941", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.52", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8361c4c002040d4e4b5f38416757c0fe4741a1cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.52.tgz", "fileCount": 6, "integrity": "sha512-8ihVdBfxQX01fHEYpBWJWw2cK0idbf8lpGcLUjKnV7Y58XHUmz4eHGA/4k4DxIBmbQn/uueQjd8A7Udvj+NNkQ==", "signatures": [{"sig": "MEYCIQDI07xYjVR5TSuO+J8y8S/vEeTua+RIqAqy9L1YX2uulQIhANPo3bNltkCTcAqWnM3LJT8gF7bD4M4IQenQ+BByH1Ka", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14249}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/plugin-syntax-typescript": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.52_1530838778995_0.8259274856536412", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.53", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "775d2a53e46fd4a7562f314fe674a6ad2f8373c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.53.tgz", "fileCount": 6, "integrity": "sha512-iaxP1/sSjIZ5wok4IUyk+fiJNT+wjIN9eSzSnrzs0/uz/XRwhsv+rXOP6ZQORFk0zUlSdzjwUkCdfvO4ABmPgg==", "signatures": [{"sig": "MEUCIAgS6WhFJ0WzQfTyiXvRkHT2BHfd5W+moKJi1gOM6bH2AiEArqWgQo+f3An+QsiSAoX1hAg4Cu1SJiy2JgGK2hHmdZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14332}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/plugin-syntax-typescript": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.53_1531316438166_0.9914818223505206", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.54", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7b614ba0dbea88b70ae82df9c429e128928c9251", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.54.tgz", "fileCount": 6, "integrity": "sha512-nwWQ4ptm7UAbiEfEpq6c+80pt9qY2syq0Z7tP+08TDYL87dzpobc3KF0jrFNL0vjHt6MlEek67rtO/L9PQ9vVA==", "signatures": [{"sig": "MEYCIQC0vgA3PFQ9VEF812qm89d5t6ZIhjVuy1GeoYqR4jL6MgIhAOfJYCx5J7WFCCCbNJdLhxMwOe+7fzIHObgpbyCQs1px", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14332}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/plugin-syntax-typescript": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.54_1531764020445_0.18208688379954374", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.55", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "290b5a6dfb7730bc167c57ffeb471267b4182557", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.55.tgz", "fileCount": 6, "integrity": "sha512-kUrIJIZJbRUT3nQsrZXJ+q9+swVZb5GlqUqrBn1gGa+0KHt9xl8KPyONY30jE61Kcb0VJr3rqXCcQwlvMKVZZQ==", "signatures": [{"sig": "MEQCIB9ds1EztgWYTa3aW3jB2vXm4+ZN/hyLDBie/rIVjoiXAiBm7fsSRTXiXboxrVQ2GcbvRCF1krrxtbhVFU4Zpxmbgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14422}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/plugin-syntax-typescript": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.55_1532815663672_0.6184685295147816", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.56", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1357852a7ef303ca002b6ed7e4712578148edbeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.56.tgz", "fileCount": 6, "integrity": "sha512-ZVP1um14KoIrB2cYbW/ddmO45EppRX8IiZq6/VHG93N8HKMkexcO2U9LsZ+SMfoJqnhGRiZ3S0VnHbyILYk3Eg==", "signatures": [{"sig": "MEUCIDqr5VN0OfF9TmqVlLqPpSVwcmsH5y/dIkwrPINCDAFoAiEA8PqhHESCckrOvYECF4eBZF7d1DvX7SO0OEmTY89FaQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPxaCRA9TVsSAnZWagAAsO8P/3t18XKKKi9fdutjZd/E\nkT0R+/DFQNM6Be+Er+zGIDvCXqXrXB2jB4QcN+Se3pi+A7a008q/75kpjnKr\ns6oI0b6boXLwQgympZ6Hwt086wB6EhOQEFez9chB4InWEssrv86b1d2vyJCk\n/bbxxqaQu6iKl4NynMnmBXz3HazQPuKLe/a3KBUhcuJGB0Pm6Jx3XTga8ri4\nDz6apH0K6VLw81fJFnc46HvAWmjmIqdbV4cjEy7eSeHGjbu+mq0+0b2MyBLo\nYT4wNplUqp5I7i1xz8IE3F0qUviVpU5TltWKvneYkCoEh9ezNAecgQIvBNm4\n44dsQ+C/eMwUeeotta2D5/Cm3FBzYhOfYHeOgt0hvy1vJJ3YaCFbD1fl4CYB\nD6UP53+SgVoMi+yCaZNN9P6UXWKLRSR/B9EZ8TRWoZaZiTZ9bo9ViQOaiaMz\npbEfvsiy9KD2HE3sJR6WEvKNhVqjLx3gWOZYtveo/P7z0ff2wxkY4EjvrB8N\nR+GRq3MPhvPUcnUtLADIkOquSvjAAhw8xHtvXtOX9trTQrzdAby62TuJTz0U\nF0vOchP3GHF+fCjmpuFfFGaBuyRGrGqxRUD6wBYz+jnpUiI6h5Y2Xd/nZ4zK\nIrJNf3Ne/17eVOdWC4600h5QABpXdf7TqiBZEiLT9BM2m6C5EvzfzZqSO3T7\n5Iny\r\n=L3LW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/plugin-syntax-typescript": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-beta.56_1533344857693_0.7943617589741097", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "63e00fba60319e5fa752c3bda2d7257ebbac3f8f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.0.tgz", "fileCount": 6, "integrity": "sha512-4vcXrkCgHGV/icfMSxp0O6bhi167vBRx05pSqyoh5cvTQCPfrhhVWcUDoRrw5r217VLsKaLILjM5HhdqzXTiXA==", "signatures": [{"sig": "MEUCIQCuFXSeIxZXh0tL7B1t3L3zGCdeP2iZwTl4O4cvU3x2iQIgB8dz5/6h6v0TKVNVIhkWH9B9Yt49TUeGEBUGMGmQfNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTcCRA9TVsSAnZWagAAWmwQAJjBeQhY+8y01WWmJlza\nkdlhFaVrNblgr9BS6IDvwzqrZz1G8GPKrq7kbE4JOokoSA0ovFjvUtWpPMuj\nXaac+sVQjrywZrfPQzVkLwsX987Yf36OkeIYyOlrdPCY831c22BPyPIGF/9e\noqByutUb0QvCzuSPChHOzA6PhAEqho2pHCVrou+k1g3wMbgtiA4WRMeLRCOk\nQRKi3FI7L6kbKSrWWbKzO4kR/FeHIQ6ln0buaBquErwMMTIC3hDsuHHYcPUy\nLiFTjbBg5imu5nTWlT1LHD9Js1lZog4RiEiRF3j+oh5bTkWw8zmSZ0uW0iz1\nXV2GRzq9ZN1iOZy1sG9ySzh8lCZR8vf4kM4mtjkTHd7cUp83ZZOc5wGOmbV2\ng/AO5Od+mRb8/540dMylfrKNRTkGX6C2VVhzA9nJUWcaFKtVN8MHdrMD8eCS\nU5nJ+82NJte2DYYswhs4WjhKO/K8nPsCfY8X0rFKWcyFoGgBysiZkJhwTYta\nDbNzF7hChF6t4bMMP305Rga2T4HnxakdiozPPyZwsIYdZDXNee1XMBfhkysN\nlNsrzuJDQdORwbPAQOdRQopTZX2+vxvam1J39yZs5P6Rr+VoapLn6Yae+zr5\nc8hrsXLUutBHeKj2j/m0veWMlHXg6bwm57IxvQ4TyJ3xjgbmcxJJFbYTyjZD\nUQ7r\r\n=25P0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/plugin-syntax-typescript": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-rc.0_1533830364077_0.24543217404246453", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5b9726c6f9ab1f8f0abd1971577da5accd232dbc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-0V1avRnl9mVOCoDXNd3NTPUncc5qgeqL4W6BdHglXjEUrk82MEg0sZJqMRXUfTCp/tmxxt0B7u9GfFg3N0xXAA==", "signatures": [{"sig": "MEYCIQC708/BUBZW2EqPnsVwgavjtLFJ7hzcnUDu1/xqNa6m4wIhAO0IQNHsO53YMdn08zwJLo9il3qmd2mQZB+vyotCU/2Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9nCRA9TVsSAnZWagAAzR8P/Rk5Sdut0nCXTeYTlLqM\n7kptL2fd75RQRyfpeITGDg4mjBtguc6i3J6BcAz4ATL5meVUtJ635jZDFb7R\nnw5y2dyfuzC+rnFFUBWVe3s2Gnf0pDNw0yNlIE6uD8ZfXLpmoEmXgkUcFtfT\nPKQnyUi0LcDI1MIm7X6TErUtwTQIfS6t5iDSgNDBGIWJuFpBV1f0ucbUQaEC\nqxFRUciyX059bp6LOR4Qpho511xNQ5x/w0d4O29/medaDewct8562s4egb7E\n/gX8uCrKLWuMeNFVVJgPTYTVKjDcyts3I+rT91tNl8dGmmHmDQbQiUEfCYk5\nC7Ob0a26plbuG8vlaQ398e5CRUYdx3HlHvH5PaWXFx1YDknexGfqAE/x2o42\nyzfzwaqmyks/Ibi4F8/1dUn1QqNu+YdTnN3S7igXfUNY/J3GMMBdqGWNbDA5\n6lwVP8VwLSTA/hwC8QfLdmZeo7vuJoFb3CKZd8gjeYzPoyAfoTYVvmBR9tNx\n0fxkcM3Tq4JsSx+zLRfuvhZ3id0hD8WRJVEcgMb/UJ4sXeFS6ARVuVMZo+zJ\n4+TVRhX7OQi9eHETe1y7X1Y6YIZK/hr6+22GG+ofs/Lu9kPCRUzAHAIftCqu\n6Ga0+m67zVSG6jbZP9q+HcNgWocPP0mOuCJM/wbGjPO05OTIJdmvf5aUoHxR\nreDk\r\n=ZueF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/plugin-syntax-typescript": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-rc.1_1533845351195_0.32295488995198873", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.2", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dcaec299ab601909c4942dff419a6c5206d54443", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.2.tgz", "fileCount": 6, "integrity": "sha512-r6N6cdkGAAdxhtC1xJEseVUynBfmnNSYuO61zgsJyO9aZ7t3JB60kIH2PhwSQK1iYOkUWbeUiN8jI8K8yYLr8Q==", "signatures": [{"sig": "MEQCIAumxPi/oWUfOX37QX2qsUeChpDI0GBHzdUBLQzUP1q5AiA5ZjStep1wUi7T4Sj7AYQqrZPgT8ia6LIDVxNCwPbdxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcfCRA9TVsSAnZWagAA2IoP/imr0GKyfgnVCyquQu8P\n2IdOpXTqpMkiTzGkaJ4lBrT5F5JGW/REwUrSaTKFyUG3milkW2F20e0FS+IH\nRVrj7G8dsqosPi4h0Q7WVnX0OXqlqoAYdELMhIAGxIoTsSDDSH6IDrDBkIT2\n3gta1/ksY0cwrDntkzv1e+5Ojl4sHqCBTNVv6CfeURk7XvgHjP7su9Tp8MML\nKPPnjlWXXtBE5b8ioa8efWePhOidIroCF0p1UpoIwiHenX6fWP99uIA1rtO+\nNbCu2BAzw6zq6wCegbzlrZ6SBjmyc++km0po9j+Jq1fBx9WD/2UbY1Ub3rww\nYIzvDs6ylWGRVlaGBe0r6Rk7B2VWJhnEMH9GCdZo4MQKr2WTLLga4tXRPHD6\n5O1c4HXLLKhbZFGiTaZ6bAsNCuBROqP8mYAsPDPpDBB4qJlnGktGBCF7G3SV\nlVNTWtU8G+DQR37eWYhGLpk+20hrwzKQdumwVZvixfx+9VOX5lA9FdEM3Pre\nGE94tNqYCD0n36Vonk7lIuFHUq+iRXEZpbsYVH7UurgfAADGeyhAV2dVJoN7\nCiUrIltRoBH1YimtXt20ym/TwhH2G/248keIL2uhOJtesyXLKQZBCyU7hmeG\n6VPe7Kyy0tq8rJs29l6bxa8zY/AvGetTcT8pN3V4EQVAqxa6ouC1KqI583aL\nmI9n\r\n=+TEB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/plugin-syntax-typescript": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-rc.2_1534879518529_0.0684577082065172", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "439d97371a55504031a11ee2f606f5153ac1603d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.3.tgz", "fileCount": 7, "integrity": "sha512-fu+Bc5iT4WjAS6tor4GOzcr3JNPva096Bnke60HaWIkzZs6LeVPsUrCgmmEauTJwB62CEV/FIQ/SNKnYajGh/Q==", "signatures": [{"sig": "MEQCICRh3j94+idAcHIXl8R8s9a2AGr0SrSdb1A/+nyNZrOTAiBeBV+ljARjusdRWR0pjZguJvLCC+nrRaauoLbtSY1RPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEm9CRA9TVsSAnZWagAAXZcP/iObavc8Xo5VlFv36kpF\nZtOJHIpucdjkRXcJeG4gPhTdwlf35vdvkUlMTFU/+1PtVqh7au7aMLtJ1jTR\n0/xl3uBgx34sN5vCWpoRYU2ST/oqXan4YtFN21MQP29UIrWgZ4NfHvcgmvXE\nLahS7Pyf8v0JoLLDIAFAfIC+sS8MJsEFfnUyrfTG+ranNO9nvhEnhvgdCNLZ\n3RjpBfT0/OjVq82nydwe6EbwVJatkluEbTUG8XYhnjRd7N5/7BKi1tLzLumQ\ngMSreTX6kiGjn0Fw8l6PiWF9MHQRMHaFs3IH3/Ktr38T91PyWTVep+DpHckl\nSYFhV8FOSojZ03rnutVr+rmFUgoQycsTelVulsafTMcN43YaTgioU5NZLwRx\naJJO2LNggKsN0wr1LqYLhF5Hgqk+iGclzNyCPl1eZKpeIc7NLUWWC1ciwHeN\nnbAFVJ4qg4Mg+RD5wVXB8OIizMgl90fF+HDJKSww1YYxxam4ZRSRCSlAonIv\neuunV7dQ9wVY7WBS0t7rOyW+5eSD0BG1qBnjhmWrQDlCpjW8rX3aHWrocf9Y\nU35+weKCDacOWJz7NMULJA26j4bLZJhDt0Ky4sPVQyVomYr2HNief5LR9eD/\n3R5rWTgj2xKOhTR6Aw7veDBt1ZeTLbJ776eOLGUGtFQsjg0SnV23tO0jhiDZ\nKCJM\r\n=DIkP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/plugin-syntax-typescript": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-rc.3_1535134140662_0.6431951904808528", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "41fd4ca88a6c7abddccd950412ecf1d5aa186ffa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.4.tgz", "fileCount": 7, "integrity": "sha512-mm+cR2UsLtEYGd6p1Vfp1jmfU3HxvpBsKIvSdzhRskFgNi1I6ypdIKIerFc2qrQ4wgGOLuGuH0NKByC/a0iVHg==", "signatures": [{"sig": "MEQCID9lrS7oAoXeOvcq4/AZwtnnO2tiI0hbqHc1h0vsBmEDAiBQYm0+44xZomFWzvF3ZSQyJbj0E6W0aI58oiyFyQwKOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCqrCRA9TVsSAnZWagAAT0IQAJUgUPipWc9gd5GmfvHU\n17i+mlBMoL7gJMUjmCAI38AyIK0cq9X0VF6LM1xF/U3LaQ5983vO0eB/8AxM\ntFuv2j4D1kTPDzhZSrF1ZrQ7GHKJSKFe8T+4KotcWGUQAS/W29q6fb9FQbix\nxSKxqsIZpDHblRFNLbmN7wgF1x9us9NSVq+/Tdac2RJRYugEUrve4AiWmeao\nSDpY3MIuJ06dusFG6UibQdpLGak3xFBErMppVwDxMWaG3Jc3v7QqKvgqnv+O\n0cUCNh3/oVZkP4SiHQmAORZwEGY3amK0ZyTOh22kyZ7MUyIXRrjUyBsxYWXM\nqIVKp6c5kI9Q4x36cNKJe0OHTG5DUSLRmR5WK+vuBYcqq8WA7i9t7GLXMjMN\nOme8FOGA9rlDEdQ9BiUwvUC9fEE+KAYbcWp9NDrFc0wnZfEwpRN8buUgsLqB\npCu7n7CLeR/EIqGMJICZ1dMy7DxNpz4X4tpzpIvdJz7UliUzEuIoYHfagG7h\ne6a6hsOV+5YfCaIInNX4/aXGCEaXxaq/v4tMNOmsX7YZy/D9EJV+RNElzajf\nE0GYxHv8FrZ2gIAPY8rDlm7jP1IjRD6e/3Wryk5FyEQd0yPOC2pdIbk6gmje\nnBTOHdNEZZvbxmDzzLDpG1Kn+eXzWI154xazg2Y4q9HjrjP68oAr0chOLzI0\nu2L0\r\n=i+qj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/plugin-syntax-typescript": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0-rc.4_1535388330868_0.5367072565092343", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "71bf13cae08117ae5dc1caec5b90938d8091a01e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-UNhEa+Wt8tq3bfLKJWsuZplYEdwdX2y8Cn1gHeIwnL7OqT6L+NZzVe6VTkR1AKqAhH7z3Krs61TpxgVkp5aHCg==", "signatures": [{"sig": "MEUCIQC+84UIKpBThWoBOKix38AWWP3RiAGz9LuSxnMfzg2xlwIgCoeAy5Jax2C74viv1RHnHjfNyOn7pFkE3jdlIueCd5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCxCRA9TVsSAnZWagAAKuQP/1NqjmS/uUFFIkI0AYIY\nWeIKH8kSW8TDUjaro3KVLftL5vBsTU00jpup62s6mvDMYFy8/eegRoQevDGX\nsOQ3W09SekdtM72GvkTh91GWPSmeGwLxblWq/sjjQxdmd6HtPfflBBU/qOAG\nfMORxXKRwjl3h/9YSMhmvOa+Srq6GB9Ar1rgNviZn6hC6MdkGF1ew+NRSeIE\n/lPF6XlidtAxzO3/Y4qhoF4EdPSnBBn5tfBv3945+5W00vet34zK/FjnRtGJ\n2SiCmzEzR6g7hfNdmiQ7yECl3whMLsCDgPGByIpfsdvkzxtXrEsGvUynB13W\ntzlcEq2i7K7tWdyIiIijZXhaMNzn1U6SdUTjeDg9DBC1j5gvTfOJLbWtBK01\n4/mGekDO3ycCVyZQrrYzWGOFn6TBqeuKgzBxuv2baL4oQBjk5vcbDiUzj6Xq\nfJM127FXSCyRBb6H4ONeVJwjSaOlinbaEkmcvbq+t8sD1hjwx0WCrY5t+1Sc\nQ7PXda8FMchsSO3OrvlYKG8IxTmYY+3up8TtZVXLoDjQc8RL4/hWXX30qOS6\nPX54CorYoSF7UVd7Wz6KyHXZ/w6cTogjw8Jog+cgK0pFH+81lRz9/M8kPzLQ\ncdcFfZ95BTdXDzuwV1RwWMI6zFx5F1m5m8ieHTxs7nuhA8Mm8/X19KXvzOwH\nbzoL\r\n=JSjm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.0.0_1535406256838_0.5286492079671454", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-typescript", "version": "7.1.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "81e7b4be90e7317cbd04bf1163ebf06b2adee60b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.1.0.tgz", "fileCount": 7, "integrity": "sha512-TOTtVeT+fekAesiCHnPz+PSkYSdOSLyLn42DI45nxg6iCdlQY6LIj/tYqpMB0y+YicoTUiYiXqF8rG6SKfhw6Q==", "signatures": [{"sig": "MEQCIHeg3PDJ7nlJSSS/xNENZldFLIjJ/qMcesRwmArsznKoAiB13j2r5gznEvJcT7NBz0LvfLPXqsRod0CrGnM/QNJW2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADKCRA9TVsSAnZWagAAboUP/2E6aWBbvKebDX4hBYKW\nghP/Mhdz8gwBpDS8ZaUXMgPijuEPqfbsF1UeJZ6i2xCNzMrmfPW6dhcWbM9j\n2bJ7CqJDJRp3SN9dF4tOX8A1DW5X9W2dS3mwfaLBqORzDpTjXFSCN19x927v\nQVSJWxF4LDyu7FQ7IQYtIUR7FPl2eEL4qOEzgBpmR/d/7waOMS8Xm1Ae3JW9\nlGuy8gprYX6kbyYsR7cuFJ9pzoLDILL1uuxQNLDO6TJpzYR2Io5OrVHWzKZO\n9b0tVFYqYl8gZ0HjEa6cpSuIwtQxzrpum9eGJt7L1RIoFC19H74L1HZsG+y0\ni29CBNN49b1ZPWJOyS1FQFAc7sV+Y+sEkKEBspz8MdbFVONgeQ1zvzKY3yLX\nEp4eU9pvQEjigYiDrQv8VMuzqvmfup1614+O2hGJrryYZvkgVBr5aZMKg9JZ\n2hlQ2c2DH40zQUvaYnqi/ICsxi1zrhnkYcncWpTt8u8XmggWTaDv7pieuqkZ\n2OSKGTbyTKPZUUqaZd/qL5LQ71oMgM6qzHlPgT354ZAD0aHwkD/035DFpKzp\nrQcxrlXg1sQ9VZSZLwhJq5r6B7kTx8Jt2eIvo743fBVD0K9RXRllK8P68FeP\nLlmILolpjqR/Zd04XTYwKiuVsxnoa7WXXSIxKEGjIGHdZD3WpSq1g2ykQsdZ\ncK8t\r\n=lm9P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.1.0_1537212617533_0.0724524912159692", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-typescript", "version": "7.2.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "bce7c06300434de6a860ae8acf6a442ef74a99d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.2.0.tgz", "fileCount": 7, "integrity": "sha512-EnI7i2/gJ7ZNr2MuyvN2Hu+BHJENlxWte5XygPvfj/MbvtOkWor9zcnHpMMQL2YYaaCcqtIvJUyJ7QVfoGs7ew==", "signatures": [{"sig": "MEQCIHvfPph9UyffZCFb3RKN0Lx/Iva2u1kAN6szaXGvVRMpAiACc329loMf+XodO0HcfbG+h8yiSltC7U/Iwd4cCIhIyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX3cCRA9TVsSAnZWagAAKFEP/2xaDfrGTJAT77kWnsX8\nAyQLUqcwmEuvAU4jieBw8E8NwebSK3zpw+3dL6RMJ40i6ZMrpL5CT8BjIs6V\nHC+KvXjeuUOAXKihH1E9PXeVJ3epg/pEZNEAUZZZz5OkzuNjgjUFoO9elBbt\n0SWfW6v/DdzMBmkOnVTnTgdzhVKgg6I5qu0lVRflKrT7T7sr8k57D1kGTJNu\ncgNsTRpoD2WhVFjuRIdHAQjc+LsxmcBrm3OLtT2rQyhxXw1mCYgRMyWfrSka\nKk4NePa4DaqCF5E5nLNGEmPBX4E3id9hsdsvYPOE0QIwAk8ovtXcMFsEzoDy\nd0/7L49chPfU/ioLFyqsaB1X47umBP7DyE/T5CR0QREF5yxSgyHvVG15az+T\nUanO0iTA26o1ffI6qw3fhaH33jWQvlLbiMBNgLgHpAC9IdLhcFMJIb4rIbTs\nuWykIW4xW87Qwlk4hwnl6BIz4Xkc0X6O/uuT4QdsYRui4kNX3/+DuKaAiSZL\nydiHxRH0nDFPgByVwYtUJXSZzjeyr1KzM6qMkZMt2B6qTnG52d8NAHi4+U9s\np+/Vq/irPD55MWqXRS1FBr5wobNHpiHZ5nooFQBrdFwXG87nhnJIzHlzAlRn\nxcLeeMerWOB7xCaihkEY1U79wIa5t6fa/9tlYuZFUdfIZaE8nlPE6YQ6WTZO\nTZbp\r\n=uGgL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.2.0_1543863772117_0.7869054438384104", "host": "s3://npm-registry-packages"}}, "7.3.2": {"name": "@babel/plugin-transform-typescript", "version": "7.3.2", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.3.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "59a7227163e55738842f043d9e5bd7c040447d96", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.3.2.tgz", "fileCount": 5, "integrity": "sha512-Pvco0x0ZSCnexJnshMfaibQ5hnK8aUHSvjCQhC1JR8eeg+iBwt0AtCO7gWxJ358zZevuf9wPSO5rv+WJcbHPXQ==", "signatures": [{"sig": "MEUCIQDhlxFE+mwMRhqLkYKaQq6QSQjxGlZqSRiV3t+6x4IXvwIgfoOF1HQeoQ5QepP/t4fRtBSm34nMqdn9xgHPXY8cZ/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWLtJCRA9TVsSAnZWagAA5FAP/Ap26vFv6a86dhJsmT/f\nHcxe9KtCQgaOkV9wjrO8XFHHJFr975v33zCKf9TtL0NvwGn1J3dZnxrlpwpM\n3q4x+u9DhO9TqBzShbRnZ8Z+wbM/4O2yJ+Tw8JgeuL8B0IBdU62weDu5+KoB\nd+yktY943KRxg2ZpFov4R3z4KlkUxxAGGBJ8stBRuF2sTx3A9FxDsEUmgaQ4\n0VWod7rh0WpXjFuIZKRWac/XbzSlEgVd+m4QS1UOuzmo6OlPKa9rBMjgwZm4\nPpbMjVWwXFCgtPKuwJsZfndE1QT6V3YobUDNCRZb/fsfns/2BMuE5ReKkUxQ\nCACNVmjb2oue7GsV4Pe88crrmyb47+YeSf06lFqfHSzr8f8D1XiBewrO6Bjy\n0jfcsRpTPwzxZnpJwUJaAl8qDA6i/UqdAIy4jp3A7REPORC4MLN5t5EGG8yE\nuDdmD2sfX59FdVeDylqATYRHkJYR/yIWUgznGzSn2aQq1g6vJBkvGK9arBNi\nKl2+o/lnA5aXhHGNVWYhEB1RUAsi18DLml+IrnZoP0YYJ0tv7jIPnzuQiI8b\nHPGt1QoFmQel+2/09EpnT9tqsIxe5RKwAoXFG8ssYaE1r4D/Mv263nVrmFwZ\nQSauRumwsLRR8LMCML556fwsd5H8yQWl4SABoHh2W1f+DETZYF2HbNZ+9WUB\nmYs5\r\n=FRkf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d896ce2b53f64742feeea27dd33ee45934cd041a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.3.2_1549318985272_0.49177200681316147", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-typescript", "version": "7.4.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0389ec53a34e80f99f708c4ca311181449a68eb1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.4.0.tgz", "fileCount": 5, "integrity": "sha512-U7/+zKnRZg04ggM/Bm+xmu2B/PrwyDQTT/V89FXWYWNMxBDwSx56u6jtk9SEbfLFbZaEI72L+5LPvQjeZgFCrQ==", "signatures": [{"sig": "MEQCIGcltryVzmiaed3pSg8eIDrtEAMhJnYpqiX82llbyb+3AiA31qyfp50QSX9qAnJHs4yDswqeF37GPhZtofYM595hfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVS1CRA9TVsSAnZWagAA9BIP+wbWvSUL0auV1WjajaqA\nSTENG2okmwAyMcwrO8Uma89ypqYAmHo6QGCapZvxxaSiYL22gS95o/l9ayhn\nD8PN9QNYcJeQSsYMJrwFetbmR8ryqjwzk50NklcG5BTMHcphFWJG0BcDdkP+\nMkXnD66u6wSbyFj2EgSKCbQwI4OgVJBz2jF0CaTDexS62euRWSOAu7PbTb14\nitTaw1sALMb+3sb9FlcgMvnb57BvSzAvtD+kz95mTQZhz9MbQqYp0ueZklhF\ntEflU9r8uvmczXcIQsipYbGsGCAC/WH2aixUluMzLsbv5zwNOZ3vnhvRnlFk\nEPNqcqwY7aQnsGv0LL1/68N32haYpyG6BA1+zCGrgxYGEFxB3dJhoFU6Fneq\nUQqoYjNv1oIlk6T+lzKGClrnVwkHZUCGQ3uTiPDBx2X0AEG9izMRIKwCuIvV\nYWcWe5c4XvEBCndc6KZVi+3eaaB2/gapz1K2WmYg9U/29jduww51w717r6oQ\nDi43G2yrvlI0Y5bzA/8c/P/7aXzxJUz4r3zZYM6ax+P5j/8pbUQmzCQ+BJJv\nNInWBhYZ5/aIIlvBC2Rf+v9Z+UUKDRTglPGzzH9GEIhpKG363rFfQcQpkWEp\nOExO1Lxn/LAk7AILhBYH9HgXXc/VQxan+tazbvoPhpB7ZhQXjYLGyKUDFDJn\n4y5Q\r\n=4hv5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.4.0_1553028276556_0.2641007494124108", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-typescript", "version": "7.4.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "93e9c3f2a546e6d3da1e9cc990e30791b807aa9f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.4.4.tgz", "fileCount": 5, "integrity": "sha512-rwDvjaMTx09WC0rXGBRlYSSkEHOKRrecY6hEr3SVIPKII8DVWXtapNAfAyMC0dovuO+zYArcAuKeu3q9DNRfzA==", "signatures": [{"sig": "MEUCIFpArubW3K2LYKrFYYTNTGLVMxJM4BWzNAK76lks2DJXAiEAkfa5n7L8XXRxvB8PbpgEloY5Hlcukaj88ko1YlwXCNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JJCRA9TVsSAnZWagAAORQP/0jxi0I7G9vklkTHuKak\njFnrH5b2Uzd/B9yzKXqaIxh/i5mbjjMisYMy3rdG6bWqoN/uxxRgS9yTIhl/\nfQZ2eZ8tvLRoqaCArqt67VM41BmQVcWyri6st1kurjYCq2OplZL9mgAeVZPY\nkEWckpn52y3kc3JtQBBOH98o3Z+2OVW40X04o6MROhIUMTJc3MFrZi+Sov6d\n32nRUT8/EfRAN0W/jZz6cPTwRrnbybmhPzLKT2usw0syjD5olaE0Fxx4bDKF\nwzD1FfLqN65+jVp76/WYKa/58bfxUC3FMVwRI5sHxv4J+bQNeKek2YJrwY9u\nu/3AK0A7cXOcNLDcyzG5LF2X/T3m7FvHvqk4a/Wur1J383yV/vDMR2XD5yj5\nPBjcj7ARoV7CN+iCp5UPWB5nZBYESt1LHPmtM4HuDU7phcFCMQ/7m+hEXZXQ\nrR/rr6Q2jJ8okVMCUo41efIUcQc5L6jZObtrWbRXGczMGHtWyuTCmVw99z4n\n2LCX6JHFe1AZIWngmkFJzlVPn3W5Hn1MUp1LNZwN4gOCgbQZRLTVI5DFAV0m\nDIczkNcTPxJ1wZmyDpM5jTvaqwUY5+QsPaSVk4eDbj8AAsu83dRXNhuhVTTj\n/OG3Vqg7uVYuJPyZ75RHo8SgXaKHv3vQiUAK32VtEHOmRM9qoxce1zzkukOg\nYmk3\r\n=7fhw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.4.4_1556312648680_0.7013568561378145", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "@babel/plugin-transform-typescript", "version": "7.4.5", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.4.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ab3351ba35307b79981993536c93ff8be050ba28", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.4.5.tgz", "fileCount": 5, "integrity": "sha512-RPB/YeGr4ZrFKNwfuQRlMf2lxoCUaU01MTw39/OFE/RiL8HDjtn68BwEPft1P7JN4akyEmjGWAMNldOV7o9V2g==", "signatures": [{"sig": "MEYCIQDdaECvIstbyqrXpC9fEF/sNyTkN4EmDYmc6ZkIrjIz5gIhAMXjIWJlhEQ2liA+agH5dtiTwvF7Q1q/eZ4ie+YaQhVx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18345, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5DlFCRA9TVsSAnZWagAAgiQP/0+enbUfIvWS/P3lpW+F\nljGdI5u4tsUh9er0cb5IQtUC7e7foSk3fkZV1R1OS/C8aoaccO6TBSXqC+y7\nJ7S+nJaF+9N3yQS+0fLPR4p17mNzXZnO4MiAKkCYmt0cG0/em820ZCmXWjxo\nSMOrtOoLLRLVQHP/qCrQDBEgvZAHUTpGPZK5oRLeu0unC7xtOQdGebDK7ub5\nOMoDsTzRiC2Gb7a0K9SOxSrkFkWrzJEBxcJyTPhWS75olpdTG+tx7BV1pEyH\nQt4AJGLYvYP7z7pg7XCthckQKeg+SzxLdmWFiVspRN00i+phCr0xn/joHIak\nsnFXf1DwLJekEwO4zp7zHoEMgljT6CI7du1IeyLHqKvZGxKMXj8POTroq0i+\nGyjs4xq+MJx9h32INppqtEbsGcNbr/1KzlKl/kWSZ9Lutm6uSTvAzhhzOX9r\nlIeOa5YqWIjH/crONN13Wu2q6jzluchttR1u1FbWA5rSjYwqr0Vb99N0CECS\nwBpA4HW1gEUixO8nO156TRUwhzL0AUe/S2xkk+goyO2NiQ8daL0TpeLfLute\nRbjRSmhfMArAj6EHyBh3Jky73AAw2hgiXb+GTdxubh2Uoh+JMbQGaSQmWsNU\nW60iv5JE/l2VFT/XIHshcrz3LPDgMgIoyEWIZkPeXZvUy7r22gmFJgoxuZsA\nA+af\r\n=gYKA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "33ab4f166117e2380de3955a0842985f578b01b8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "description": "Transform TypeScript into ES.next", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.4.5_1558460740320_0.11540746616675568", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/plugin-transform-typescript", "version": "7.5.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a0855287eec87fe83c11e8dad67d431d343b53b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.0.tgz", "fileCount": 6, "integrity": "sha512-z3T4P70XJFUAHzLtEsmJ37BGVDj+55/KX8W8TBSBF0qk0KLazw8xlwVcRHacxNPgprzTdI4QWW+2eS6bTkQbCA==", "signatures": [{"sig": "MEUCICmguOK+Lxh7fO6CH7lviaQRihz6CdTjXaIDHTNc1eveAiEAspc0CPEOmHvvayY6MKiJP8l/SuKK1e7Btrlp7/IjK38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffhCRA9TVsSAnZWagAA9FQP/iqY4mXasWNrC8Umk+5B\naVITfC9/ErJVMmYTzXYg2FUD7n/K2YRgnHjOf4TnJp6PHMcPMO3fLT2KkwQ9\nAJ0PaTHsS0PnFHRoKwuradaCpnDMUuSsQWE1u0FMeoDSwqxiU1Jw5I4kxde6\nriXStFfGBW1yde2ZCYU87Jm5Sap5PhNoIhm6u+Y/wZgezaMP//MzlZEhDKF6\nUJrIUVHlcLp0WZbJne5jSwkcl8VnipiFIUfwgtAszttzF/4eBQRuxaFmxtzH\n1MG7khVy5bciDz+LOJkjsfG9L0ZEq1kr5GJNEzFoZQICfgCpgdFU2NsYE6+4\ne7utCISdPde/XRGwglDTdHBeqr0u28r5gCU+1hoCiGKjWMHzUfDBcO4YydiB\nSPGIwWVQDwkOSMC0vEz+ifDgAi1ot9vlqemQVGkxto0rSYjbBKUxGiXUEdbo\nCCEL+pj86k1jz+Ciu7+YH0yIgIcIS8wNI0cW005HJwbsfIgg2doPP1V2dQSJ\n9Y3rGyriycnoiCF3Q7Mnf17iCDaKzIVOmXMZXL7k78WDvVkzbINoMWgipiv6\n+1WWvGZBF79frS9Plsuh26TY68e5cuJ35TIwQKp9zCvsGOL3hsHQD9+f4Lba\nQJLbjNZpQKBgMi1qtmVp5i9RhX/LghWLrvM3hIIjv20rlqUeDw0GBEBCWRVS\nGNBV\r\n=GQhj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.5.0_1562245089243_0.9118911646645482", "host": "s3://npm-registry-packages"}}, "7.5.1": {"name": "@babel/plugin-transform-typescript", "version": "7.5.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.5.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6e3d4e62ba2b7b596f9bbfa658fc24ee62461bdd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.1.tgz", "fileCount": 6, "integrity": "sha512-dzJ4e/vIFjQOucCu6d+s/ebr+kc8/sAaSscI35X34yEX0gfS6yxY9pKX15U2kumeXe3ScQiTnTQcvRv48bmMtQ==", "signatures": [{"sig": "MEQCIEM4SD3CbhhRHbK9Yw98GZCH/pIZ/BMqcaB5WU/Qxea4AiAmQU1Zlxwzny9PAryW4eJO+HX0lGw8gOVxSfnmZSwIcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIFgACRA9TVsSAnZWagAARXcP/2r4+jCc9HehUOXPGrrT\n9IrDtJu1f7VmiG2GAId4KUFrVfh66IZIXB1s6Rs91qm09UL/crC2jv8/cMTK\niEUgMgepC0doPS/fG4sE2S4ptZM43ua5T2cy8DTfuPWqCvqrHRc2Or6AafRP\nYQ8HPPVv/RimtHEiS1fbzokqpP+o6K14DOPnmWykLceXUXak3o+5LixwSEPp\n/F2I6Y1a9FhlT3EUjm77tGbiasK3Pyk7l0QOTGtTymayjubp4ZmAddxAT744\nvcohidOFdNmtRndkz9d8xn8b1KEHgLpTjfglwetVp/fgItDaS/MFVxNVIZMS\nDjusFV6W62YPL9yl/NtOgn4s8NGFiVtEffw59e3W9y8HA8WemI6eSzr3IYOD\nQ6AI3dmZqqdNNhcO47vXXTcEIDfr391mCcOUtvxmpWyn9rldeso6Xn7tZwz/\nk3z6jnYUPRa3RiABjWh0pPDGMLIHi/+xcIce8XG4FLEofMM1Mcm1ReQpm6nu\nemM9sB8HFGOwbDreznUs66Hx7jt3wFvtKo7f0oDMb2YfRxVfXieOkj8Bznsk\n3fLhOJ3QMi+ur8EOruoxgF/Gjxu9iPnoh18OXNkDVICjYJooYWUBZcT44FRo\nn8x0aROXN6dMwICtqFrYC+VYnax6jI+aidGizlIJnTfdLUQnoRVogQb9Ab3t\n6O0T\r\n=gBwW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d0519fa9076ad2b50fe0a4d745c863c61c226117", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.5.1_1562400768165_0.8398476009869804", "host": "s3://npm-registry-packages"}}, "7.5.2": {"name": "@babel/plugin-transform-typescript", "version": "7.5.2", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.5.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ea7da440d29b8ccdb1bd02e18f6cfdc7ce6c16f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.2.tgz", "fileCount": 6, "integrity": "sha512-r4zJOMbKY5puETm8+cIpaa0RQZG/sSASW1u0pj8qYklcERgVIbxVbP2wyJA7zI1//h7lEagQmXi9IL9iI5rfsA==", "signatures": [{"sig": "MEUCIGnRn9mZVX2jmQEohGt1nk3zWxIbEVzq7zmSN7/wjjzuAiEAuo3Vp3SWcz+ndD5Nn0VbWI1nMKQogoIBlLTatyvkNuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI56zCRA9TVsSAnZWagAAIDcP/0zClsmBMpKSlJ6HksmK\nm1RcGhkQVIU42UzZG4GI9HkO7KuL/GuIFDUY6TO9TPLAQSDnx7prp2uUbJLh\nh4fMuN5lSfcQImKuebKUq4JFA+8aYtFCk6JHHZB315RvksWNanNkNN0YoB8L\nrygWroiSj77PPrZZFMJlaelK5PxmMcUpmBcML89asBfOZXBm/V5R0BTRXYSs\nLnqpk0eEY0YyEzICmwrakeVxNEDAHxgp8otnasIqpDPfnFGCpwBOVSoOAH2b\nIg2jD+0P0gNmyTABlZCyrg+M87Aa161aWpHnEAgSC64W0Y9s7cRV00XgSf7a\nlGYMBrjrk14T/hdGrxI3iYiKcS+cbDLFA0bm5EzqZetyeJNhLQ2DNpRosO9S\n8t6xg4f3fiDgQ4+EQnWYy30ao1ekZhhftsRTqyXILZPOaC44udmS1QD5/qMD\neIp/ke1BhPA8D158ogpfgJxNfFW6DKlOpKlaXRszgTUskz1P+cLrs/Sken/N\nvSp0Dtims+p+I9Def16gKNZkvGdP5b8L0L4859k0UVbhhqPZgklxAOs6SaXB\nMbwVKMcqrejSh8NQiQkJOozXkoYYYixyr72j827rm9Ue900/vYa9+/Fat4xd\niyhnsqfLPfVfz1cTLCP8PDtumXHeRLdUecN+mi7lQmpbTWSA0ynZAeprBTDZ\n3m9s\r\n=tg6S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0dbf99bedb1d82b8414685181416c19336dd0d96", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.5.2_1562615475053_0.17698500094869818", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/plugin-transform-typescript", "version": "7.5.5", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6d862766f09b2da1cb1f7d505fe2aedab6b7d4b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.5.tgz", "fileCount": 6, "integrity": "sha512-pehKf4m640myZu5B2ZviLaiBlxMCjSZ1qTEO459AXKX5GnPueyulJeCqZFs1nz/Ya2dDzXQ1NxZ/kKNWyD4h6w==", "signatures": [{"sig": "MEUCIQCfjrmZ2mCpzNCTImRKYZQY0Re111nGfq3dTnk0xU/JqwIgBySmmDHAMVYwx5Ly0i1RacjM5ODV59Og5D8+iIxvN6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5F2CRA9TVsSAnZWagAAF7oP/3W0+RdZdAzwuMueoAoB\nGjPbOCbLyu5nCa4QSK6qfh+CwujxGSKDDYTnaHxTnxeG931DdcgmIPcwrIYb\nFMH1aaRg7EnnTZBHT3n2/SIS+wcGbMa2y56kfHXadu2tmbLhXvIwXS1MwTHP\n9G1SuNDtp6/wqc5ddg6hrXXCJv3h8Jbg0v+a13xAkxJoUkbTKfXwlCO8xGTt\n7dmnR/9t/d9lhHcdjXRPV/k/aICH1GZ0DBCBHRLU6XnM+Ai2fuvZKogv3ifl\n8FkJFbK0gvNGlx9KdIUtrctig9UOk4a+RHs8Nf1TfbEK65TiEpXNFGgjS2XS\ncybBHyGPA+b0wlNceq+l4ya6rj8grGcFVDUs7D4WJjKvOWLkvKS9GOY11224\nf3G3thYp4ABSa/4ME4rFozc3CAIp2H/ehqHeqvbeP6vyzhHbvPk3ZVFnsVnw\nG1SeWzsemq6aeLJGTtQKl4mb8CgBJO9DNOFiPCai2ylT1s1iIv+hxvtv1AiW\nKGitAyQ/5DZfFjYmT6kHR218A3xUWXCtp4rl+eSjD6zkwcRM82PiCYfyak8u\n7KAJUF4KtciKFfIYxeLL6ueL/B5pdGKdGXde2eEcgU/KEuOwC+Z+TX7VcL+4\nQ5JeDaGwLq9DecycB6GjKyh0xpHRLb+NrNHAE100xB6A5rTtcObiz3ah3RSy\nKB/K\r\n=rOki\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.5.5_1563398517948_0.3157584797423636", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/plugin-transform-typescript", "version": "7.6.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "48d78405f1aa856ebeea7288a48a19ed8da377a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.6.0.tgz", "fileCount": 6, "integrity": "sha512-yzw7EopOOr6saONZ3KA3lpizKnWRTe+rfBqg4AmQbSow7ik7fqmzrfIqt053osLwLE2AaTqGinLM2tl6+M/uog==", "signatures": [{"sig": "MEYCIQD2ZGwVWx0NmugxhA9kqrWVOqjws1jL5JdmU23fg8Rc2AIhAIaf3Lh56G/6ifJzIFdVsXI2IYdiby8qgUTekngJqe8E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcph+CRA9TVsSAnZWagAA9k0P/2/MBib/jePB2rKyd2qL\nvSKOjCBT0ijyz23l+31tm5zjIEDh+zzT9VcHrtLO0VTxYJ2CKeBDSMKXPEZf\nwfcX5mb0a2mcmJM9PaHLDep/WdlAhe5tNpzG2LzJcNBVRWRUE61ha5Ec7MiA\n1LULFg6yi/9VNakDSj6Ksd21pflPrVhgM7wUEa5MtM3Pfv4DIBf1YgwqymDI\nuXEjlEzcVp8/QfK0OXNf9DLO6SbHPDHEdjRR0hZxnnTd4eB0xdYo9KH8fTWq\nDZ2UylamfutLIl5WmXxo+EG1U+sXtgtASlA6bEDypR+y/zgWd6R1Lk2XoQ2D\nMdT/ybNAoAHGSS3yDRKNBIZRBsTb1RNdnvHQHiwvPgm2G/IcCCwKTXNdxmk+\nqMcJ0OoZkL7SKbyikdctKA9B0fieuSv8XRr7gTx4FAdFTYpT8AKAWJR/SePj\nJSBX0bksuyqzxjHJ5E+Hj8G6pBfhjDke6NStfUbYf2yDRfN5KT1Ey1ybxnYu\neU4KHJSCiUJIhGNx9F2cxhg+2rvdloaoUs3JnVnIz9D3o4De5pajNyNOiRJh\n26auy30IfuFINrWkoe2JW31Xl3Deh3Pk/6+p+Z3cLc8lwD69iQko+12F77it\ngmjn6lSRlQf7r/Qw1VrLjKoc2v8ch6J03ANCt2DUYukHglzy/NUQ2AQSeerY\n5x7s\r\n=+8Gy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.6.0_1567791229497_0.2774025877211006", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "@babel/plugin-transform-typescript", "version": "7.6.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.6.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dddb50cf3b8b2ef70b22e5326e9a91f05a1db13b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.6.3.tgz", "fileCount": 6, "integrity": "sha512-aiWINBrPMSC3xTXRNM/dfmyYuPNKY/aexYqBgh0HBI5Y+WO5oRAqW/oROYeYHrF4Zw12r9rK4fMk/ZlAmqx/FQ==", "signatures": [{"sig": "MEYCIQCo8tYj2h+IMSHMKmDVpiVPmbuPkoZQeIkB/sZPBmkoeAIhALxPI3JEx8F8cqHFszFZag7kwDLiMNBgs22TSsz4Zohq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhMCRA9TVsSAnZWagAA38MP/2d5hLyCrANmrZHy7eS8\nPZ8ggwOmY2/kK+K+68dzYgLjkT1qDXte4YVaiZ/qbatfyC6prtqahiSOsMJB\nF5NDRe1kqCy5UKuJITHl+mWLB/vPKksjJ4NDpkj9UE1oOct2aQMxHAzGYUyN\nYCrycgMRMVsL+kAGNt9L/LfFjq+KBhsGdhlGpV10L3UZOiaJSpo5UWlSJQ62\nu4wgGW+ju4r0XQnEkNBh8Uly5gQWv65Mo9CUFDRbiOpGsBMkwikpZmd0JVmP\nvC6/7GvRcWHkyeas7IVMZ6xsKxPmBhMOeYMS+9eojuD4lrE+VDwK1mBobvuy\nEchsvMNvCS2uLMSvqlQwxySHMEnBlWvXqJX4mmWmjffFHTHHGsaYjmG9oM7I\n3thisglCy+GSwI+dQW50INFJ02G2a3h5x4T2Z4Nj/3cZXXtQK2F1w/IcFlTc\nDBD//1iGJhjdxTBGe7wzheoG0aeCA7loS57yx1pc/VIva4X4TiHevhP91Kee\nZEy+5PEALqlXLuAfyX+cO36ERDRiZWw1uFASvh4ffqjzxhn/naXuq5mbZbch\nkFSn1CK5z+HOkK34899fLGzoFZpY2Qe0KhTWwmc2qAXmxlOsQipe25R/7MMc\ninWMVncz5u71S6dA7xqtJC/VXeGt7RjjcBFR7OG4X4q/raZ05TYcJ87csLfX\nM/7k\r\n=WY5J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.6.3_1570564171427_0.12907159793464373", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-typescript", "version": "7.7.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "182be03fa8bd2ffd0629791a1eaa4373b7589d38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.0.tgz", "fileCount": 6, "integrity": "sha512-y3KYbcfKe+8ziRXiGhhnGrVysDBo5+aJdB+x8sanM0K41cnmK7Q5vBlQLMbOnW/HPjLG9bg7dLgYDQZZG9T09g==", "signatures": [{"sig": "MEQCIF5znFKdS36unRA/29kLs2fILFB5w84QvQS7IwXPMw80AiAZnsRIjcw4PCOYGp7xnRIbQndqbena81h0tTZQ32vAPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTQCRA9TVsSAnZWagAAun0P/2hcuCgNSRZkTS8y4hfO\nxm90YiZg8gaYJyOnLk8QAXMOVVa3XwR7Lfpd0Et44AwL5cM/4iYhwCbm46LL\nvKZDNkEOCL9OTo48jbLJXD+9UQ83TMFvXlM6gjJ07EjMRGTbVRQI5DZILQDb\nQi5yBB74R3xgSmzog23QCpEcPLLWonxy2NwtA4+4t65ii+3gSor6j2bL2Ykr\n6bYOQnFEU2+MB+juF2LLLphvVpZiETBiKlBICFKAYIkhpYLvFWJ5j/i7HeO7\ncF43lNS3RJYMglLArwZW9vpYy+ogvFciz7emC6HThtqTGlflGFZPcy1Obuyb\nmlICDpqEApJRaSrm5181E8DxuqZ6xKvUQhJYK6eNSrv8xc4wcgeihvTlWkHw\nzLQN0psRWemXoHAPNQvoZNO4FZ/LJP87OsMsREnko20TxSWJ8FiPFPIf3ON0\nQ9P7FxGWac+B0BJ0JRPbfdnzKs5ApaOaXCl1nXEm9jLym5ExWkSea4Gr6QLy\nn/eq1WENlj6Kb8HeevKK98kbj1wnhVlTvctKSqcEYhTIfPsJkJwp48gtNt12\n89stgU2Uoywellv3DWc7EaBuIwZTgU6Or4B9ScHRtoQ7fp+mZJOSOCVc/lFR\ncY5xp/YPptT2w4zEkniam8NOA7s3i6dcmsB03uk03e3+ZVzfh210wITsGEHf\ngfKj\r\n=qF1n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.7.0_1572951248420_0.5096740989664812", "host": "s3://npm-registry-packages"}}, "7.7.2": {"name": "@babel/plugin-transform-typescript", "version": "7.7.2", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.7.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "eb9f14c516b5d36f4d6f3a9d7badae6d0fc313d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.2.tgz", "fileCount": 6, "integrity": "sha512-UWhDaJRqdPUtdK1s0sKYdoRuqK0NepjZto2UZltvuCgMoMZmdjhgz5hcRokie/3aYEaSz3xvusyoayVaq4PjRg==", "signatures": [{"sig": "MEYCIQChDGAeIh87f9fvkemgMy8Yjrg520oXBNCJ/VtNOZEChwIhAMcNm9Vx1i8CokPbZDUREke8zGkLD95VgD6xPV0v24X3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1bbCRA9TVsSAnZWagAAvUcP/RI1ruYAfqjSxGHKV66y\nj3U+gqF0UzieGP2nJJ4jvSZHvyw7bkoWggbUpAxsjEYIG7bWdkmGFd50BOUg\nPn8QqeGU9XEq8EDskFJ5Cx5nlSwOQ0JELhdPx9h5/y2IlyGvt6k0hSW8XeX+\nm2Br/yP6KpPewHgZf4Ps7bdx28XZ20/VdqvIH4AjaIpMlcOSBURbDeh9Ovyv\nuHV7vN9g/zrGHq9g0iLcqMaJunds+4z71AXq7i0XJ+EF4w6vE8FrcpDSO8+f\nvF7MBoL1sdPxjDGXeJzdTI+ZoxbEvwFYNv6hT0Bzy9tDeD0920DnbThG2bHc\n/iicmfMUM4Yx+yIlAf34RUG032omejDqkTGIBYDKz8XM9tMd8vD9si+pJdOH\nb0R0+WN2qnG+PO1LNMvMm1wa8kz5+6wRQc41wyGB8wedW1c1ByqAsteoQiYR\n8hq5NwvhoSLhI6ulaNBpxNJ4RMr+zQ/LMZvOWkGZJIJI4qXwylDlsCd3EdXH\n07ZOZ3U3wFxAZcO+3PDXuTsoY6r/hfk1E0KzbOukEhaqvCFZau+6DPdajmM6\nX8dARGQKFRVrvp0XWPWM8UZbus4zVz8T5PUis+Ce98MlSRzz+uRN2kR8MnOq\n8pDZYPGnP2qbozkvpuu8Hm8xZ+nhPan/P+wWiJJNATUQDLXFgPvvvfoWtWOZ\noqci\r\n=VTEy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.7.2_1573082842586_0.33168513914552733", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-typescript", "version": "7.7.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2974fd05f4e85c695acaf497f432342de9fc0636", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.4.tgz", "fileCount": 6, "integrity": "sha512-X8e3tcPEKnwwPVG+vP/vSqEShkwODOEeyQGod82qrIuidwIrfnsGn11qPM1jBLF4MqguTXXYzm58d0dY+/wdpg==", "signatures": [{"sig": "MEUCIQDrnPtVJJG0cuqHZmgisA5PS1MH5l/XmxDSPpD/YQNIMQIgF6B3aO3n/vJCPD0V8IaPIMwSuzYXK7yYSz9UGVgL0UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBnCRA9TVsSAnZWagAAIGwP/R1lfLgcYX8Oq6wonkAy\naIo81c9yYTV7tWUZ3Ach+ABrJUYhqMS4WrjbUUbCgHzXCLmSq5atbamROZ92\nppcvdk7nbr2zFCOpdyET8WLPgY2AhJbVK46o+zGdawPu5z62cxle/uWwARmG\nG0KN1khQOaXzTgeHPuMpdjW7GXUmcKbLam/Ai4xkuTMsidf5DVhXg2ayssEb\nuNkP4MVDXE5rnXRulQLjsrSw/+sJhvcLfH8VAb/OAiwMooQgHO0ILHp4mFbg\nUWhsKj6eGRBBU7/C/EpxdudjjSRHxb4jeyvOaT5c1RBtQIVsbCpR2pBliRhi\nwekH10/FAVnNLSHC/c9Or4QM2dP/UdjOi61DAGUFg2jYGoNN6tShrRhXlyh3\nZeI/u+mXRcMvqYf5NTzeMsWH/e0wcuC9768fysqFgQX9ewR8lCYyxV8pa4Yr\nrgNeB5SM49jCTf42Lu9K0lNKzr98C9K0dkayzsa9s86QnlvykZGunUqSK3e+\nTkwJbKcuevTqBVbXZ2Ugc180rKjscKUI53JQQ5vvnZqCLOOtU+wtJiqvx+S+\n3z389cdnNxq4iAv0Zw1WN9ulbqOIuzn9lMS5dhyAuNPacOEg0kmgQzPo95PO\n5LyPx56dSilyyVc8em8TH9pdgB5qRgo87d/YuYfMf3/RccFqBCNsR7U8LYZB\nZSj6\r\n=FFSi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.7.4", "@babel/helper-create-class-features-plugin": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.7.4_1574465638648_0.32563928923153473", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-typescript", "version": "7.8.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "485c9d12f011697e0a762d683897711c3daad9c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.0.tgz", "fileCount": 6, "integrity": "sha512-RhMZnNWcyvX+rM6mk888MaeoVl5pGfmYP3as709n4+0d15SRedz4r+LPRg2a9s4z+t+DM+gy8uz/rmM3Cb8JBw==", "signatures": [{"sig": "MEUCIGr28Lovcc43z/n22VwMqHxVMcmTCjhudIGtdDFAIFK5AiEAvpTHiqe4UdNmhfm70IMaZRqI4Ixg6ZNs5vhO37CZJU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWhCRA9TVsSAnZWagAAST4P/jhjwT+5oQdIqNVwlsO6\n3ERAJO3W0gEpLjOBHxRAtj1vHaHBKk6z8rPO2gkznLYDqz5r/yD5roV9xisM\nZ/RSj3tecPp3934QWCZ2XdFcqk+S0Mhkx0akVujufDODMcMV2l7Y0T6L2UV7\nR3M1fVyFd8eeEYLY13QQ1oebVhM+Alw97eSy6GH6p/NxiQShaImxwVWcHTnj\n5COPrpzC5tzV8wZ6TUk09OltkhK3QH+WcfymELAazxN3GdZGycM473FM+pby\nAx7WnDe4YFDFGEISG+DtMQlhGxBfxxSueSK8EnuKs9LsCrVZlkob/3X0BOmg\n9AlHCaU0eh8qH5OFgDXgErcLUmRjWSrAdQwBjY6+rKPL03T1xXV3zP7rt5+C\nvezxKPS0PqOQd1ZFBeuZB0xhHM/OIN6i2L5dUhUOx7sf+u6mGZb3RcQ+iRGr\nlkXto8ZRALpcUsBh7QolPDqqbTOn5pXto35Xtvzsxz5kKsTZ6T6Yu8OEuoTM\nHakKxGRoVCHywAMoH/mR9QSdvSYUSkL5xnaXXXin5tWvzcQp/TAvkSr0RUun\nzZgSWWCbnLj6pBCVbM0kATW/a6ZDp4kBX8ldoQ4F7Wp9Zj7gDi5uLHnTRigO\nkELGIWnqzb8heq/dz26tFr/GKcSdu8AyuWOYqB9jdGpyjPXyt2CPqDsRrzD7\nN7C8\r\n=hjp2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/plugin-syntax-typescript": "^7.8.0", "@babel/helper-create-class-features-plugin": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.8.0_1578788256936_0.6224206586827357", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-typescript", "version": "7.8.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "be6f01a7ef423be68e65ace1f04fc407e6d88917", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.3.tgz", "fileCount": 6, "integrity": "sha512-Ebj230AxcrKGZPKIp4g4TdQLrqX95TobLUWKd/CwG7X1XHUH1ZpkpFvXuXqWbtGRWb7uuEWNlrl681wsOArAdQ==", "signatures": [{"sig": "MEYCIQDwblK1SgwHuTWqK41cBqEx97fYOdAwNeVAcc9HGC59yAIhAPOnHgSI4AA2ekQWj15e2bhND5WyF44uWlOgEFQvVgNm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORPCRA9TVsSAnZWagAAexMP/RJf3tDXNAzvThozbAx4\n684ehfXwMtRigI8qc5p7Cky+4s6g6CSuR6umjGfPt5HpilunTUhLUj/gaWCv\nvi9+4qTiRqqXYjPaq+DVPmNL4akW/Ga+JaHQR1VDQYjhJpXihs5/c5g3FWWj\naulMPMdR3U1lYL5O4jt+JVTtX8Pn3BELKrXPwOzHmRwZ7SimhBqNwi9/HSeV\nyNgDP9rCGrdhJVAK6W5DF9adnABT1WYSHQTNXynWRCYuAUapCwKneH+5hLQ3\nFl6RiPgAV0n4rdgw+JUj1CtS09f81ZZigY/9E/wm8oEGlROxtpEm9WtZqFhz\n263Fvk7GpKs7ttQYsKyCsJyHdhugrLwLfh9smEApq673RyrgBPJM6aNX5t5L\nzMIWI7B73ttQmcqqTSylwsLh6oQzrTvG+wBu5BjDFLZ6a0duKDgyVZzPfH4K\nf+ikK/NjZBpIy+KCxCC+XFKmRc9kUvBz6HaTSz79q+rWIp8w0p44hW25NSwX\nr0LPcA5sjBGPrRXGVO/oLumY2rg+jPetnmetXzfYSMLoKaDD7dXH3bLUI0l3\n2XNo6DjQM8xkQhz7jrWaNP54OrW75cp445Syi/bIhSljgdmunrhb4gKjHDaz\naR5r5b6HmRU59AcgzipIk8BICcs9u9JI0C7gLzX2jM7nWe6FvAP70qhimkdj\nOaGd\r\n=fLG+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.8.3_1578951758679_0.49810724990409794", "host": "s3://npm-registry-packages"}}, "7.8.7": {"name": "@babel/plugin-transform-typescript", "version": "7.8.7", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.8.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "48bccff331108a7b3a28c3a4adc89e036dc3efda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.7.tgz", "fileCount": 6, "integrity": "sha512-7O0UsPQVNKqpHeHLpfvOG4uXmlw+MOxYvUv6Otc9uH5SYMIxvF6eBdjkWvC3f9G+VXe0RsNExyAQBeTRug/wqQ==", "signatures": [{"sig": "MEUCIQDzQfA08UJ5LxlFRuepR/AWQGHGdF0itWnNT4F0G3PsTgIgfxyR5YMO8BpoFiRxFITAklEnunOii1QktjcHtKgZorU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFwxCRA9TVsSAnZWagAA1vQP/jwNqPx8+F0HgBK4cY3y\nUf8jHhVpfUyR7NSdkmdd+hbTJAp7ttlIo8CtrPpI1FAOrHnIu0wh4GwVeKc0\nm3b7zdBh9DXkY0ePXd7mdlsIVpN6s0zHZAjupEzp4QD03OaS7zcjhNMBXmql\nRA5g8ctGOeI0iT6x4CMfXPcspHA60DyCzJ3qDrM86m3W9yHQDi5vWl4UWi86\nA6878NhSTZZQjPhz4zdGL035Ysu6JS6G6u+n8EjMaIWGOeB5lQpxOLRPCT2G\nfbso5QshrIy+WeSZNnpB7Ff1qb1F8IpgpK+4zP2V/5vrmbD6WkJHPLav9hrY\nX5DCl6jOmfSQ1MYftM2/ApGYIQlbG3DtD8GCD5eKXR3rZw0T+eZCHsyEL5lT\ng+8bF03avmoUe3hY5iOeuCUUB05lEMgsqgQmwWEV7VynVyf22tTv2zsiueWt\nWIMHZOKglKWEIP5G0pKiZUwAfBXxzRqqArd86l4lNBOdEek6OeviV/sV1ge/\nVJgaYyUTXedgMs0k/YjlVeYkoDlhNuQhvbzJ7FOwy0wFTTfiTDX0StPfSe//\nE7ar9ZbDdj8LhydUraJ72xIjNXQCFIfcHwWzIxznspvA7sDjNsjTGhc/Lut4\nYufgsoo7h+a+u//Hpoizb0l/HDkrSCkJxG529isOwi1Fo3ihmb/UKzq/Zyl3\nnKeA\r\n=P0E4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.8.7_1583373361122_0.6054933009914758", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-typescript", "version": "7.9.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "8b52649c81cb7dee117f760952ab46675a258836", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.9.0.tgz", "fileCount": 6, "integrity": "sha512-GRffJyCu16H3tEhbt9Q4buVFFBqrgS8FzTuhqSxlXNgmqD8aw2xmwtRwrvWXXlw7gHs664uqacsJymHJ9SUE/Q==", "signatures": [{"sig": "MEQCIEKt8GHk+JNwODzN549gZ9yFNplrkMkF3jh0pA2nuKYNAiBubzC87IDhSIDSRcOJJYLL7IWJHVvP/qoMhvtWgfcv0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOyCRA9TVsSAnZWagAAtTwP/1LYJZzUXtBD1NLLwxbU\n9k8OBtE0PUzntMo7I3BQHdMGYJpVcCAZcdYJQxZRfiJPnqNubtP2tT81huEN\n2egClR9xKVtmCxvtAhKgx4Nxc1m9AgxAtzTFKVkd73pwhfPZHCiZa1xWvxM5\n2/MmHFgMQ/J6cyGqYvy4uBe/m3eRNQIVVJjje3lrgc37Luq1kZ692wyZQiFI\nvCYDF6RLRWEsdM1DEmIsANOHurJSdL4N69MEmUfgu9c6YX+fgJbISWCm3ix3\nDm4WpPBD6jYmQdmF26Zl0dFGrleVEXYvH/P5jRqYrYZ3IvIfyR18/w1nj3E4\n0KgzAncx2XdVzMxPvvs95Kp3bDIbZ6JiE0OIVD0usYlDVNthGpEUx7ZZGHOw\nN+xgmBGrHkRRZ0HUfBOEfDLwDuCPkd/AUp5+ayTlqUnsSl0VY/MkbgAVcfqh\nUOUDDcVPHgTqdP0haLrhiQiVPQmTcvbB7C80+Ag5/jf4lwHO9HUpsvLvkTpz\nY6cwuT8H4mXvw+gsv1QUKDpdXxJGV2PVbmGdCJiJ39UV4JrqMgqHuJSHi7s/\nwAAJbyN3sOVUd3fL3ji88v7IzHsLsS9M+Q6B28xAL6GdvQ8FHCe2u6Hog7Nm\n5kHB53FA9xJTltYyKATPhq3LsZHXxH0daW60wbXCZwjMrA6X39xG5FKWaE/A\nMbe1\r\n=CzA2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.9.0_1584718764774_0.7774265903444413", "host": "s3://npm-registry-packages"}}, "7.9.4": {"name": "@babel/plugin-transform-typescript", "version": "7.9.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.9.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4bb4dde4f10bbf2d787fce9707fb09b483e33359", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.9.4.tgz", "fileCount": 6, "integrity": "sha512-yeWeUkKx2auDbSxRe8MusAG+n4m9BFY/v+lPjmQDgOFX5qnySkUY5oXzkp6FwPdsYqnKay6lorXYdC0n3bZO7w==", "signatures": [{"sig": "MEUCIQCP5GFrP8+vDPK6CoH0YAypJ/RvCI6Ffv3AgrHsNdCPSAIgBFZ9BudDbEHWUatFqUv+EXGPBAxePzjRqO4hmiboYrM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeecVWCRA9TVsSAnZWagAAHqUQAKSigdEvzmG+IlZs4ZX2\n0a8B9BjKiNf9y4CyMfPTwxfg4Wad9rraPa7m5nPYHJZosTyvWz5XAeWq7Qzw\nGeGzMttCqKc9KIM7m2UA4C/0Xkb19k2P+/gxJKltVaNhfCUelEqobApFWZuK\n3Vw+Db4hy4M90o7ZPNRKL5HGltz78RGs/S4SaY3Yz8DvYGLYMpN9Aij3znTW\n2pvv2E6K/A43b/aEKklf1ipmx9IAP7uhOOm7gpKfGW/ELW0XDIO+VzZX+/NB\nqyEfZJLcmd8T/sUzlw/iQtzgeS+wWdFCjOa0oL7V4G33QAvJVTTsyHU2NbzA\nAF0earo25wuMZjbV9KCewIKvba65Ols6CHKUMdx5M6nJ6bqv7f7Vd36E1LDV\nMzAA7rWRUwYHVePYGsxCaJswBDG5RwbfpTAMSxeyPxseLInGorpBPjKHAhmx\nDkp6GnUiOByx/PvHVHGtJuZTBNInPX6Py1w/fHLagavlE3TQJkmbDui4Uw+v\ngxIm+GUbJLjAiv7Vt58ksTah8OXUnhT0yYphooZiJx86A77cgrJjLGMUuJWf\nO2y5Qv1xKK6iuC4oFPJ4Vh4/ee9yqjz5yvuSD0hAi4rN4mW/ecmWkrA6N+c8\nq1oPdd5nuVv2uKGLottKmjw2iA8H9SUXegtdzFwLah+Xa60z9UeO6OMShpUw\nf+iy\r\n=TSnW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d3cf5fb5f4b46a1f7e9e33f2d24a466e3ea1e50f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.9.4_1585038677900_0.5675220065554749", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/plugin-transform-typescript", "version": "7.9.6", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2248971416a506fc78278fc0c0ea3179224af1e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.9.6.tgz", "fileCount": 6, "integrity": "sha512-8OvsRdvpt3Iesf2qsAn+YdlwAJD7zJ+vhFZmDCa4b8dTp7MmHtKk5FF2mCsGxjZwuwsy/yIIay/nLmxST1ctVQ==", "signatures": [{"sig": "MEYCIQD/LmB0tQOwzOLaK0KqLOGu0CSDAO212AQ9XJfgg6vZlgIhAI2fZa+PPtjYGx/62N6kY8UejvWzNgYWydZVubZyfSz8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmoCRA9TVsSAnZWagAArPoP/2YUQLFDjtkHubgTvhT8\nzbmVMTRxTg9AOXtFaaRIVIrsKvUghM2Gj6F9TTwoGvvW6QHlR0kxwpyPF+Cz\n27Mh8DmriLHgqEx7zOU89FgtM1eXWadHdJ9JCI6Hqsi387gY1l+XYeCTcGA2\n8BBxH21rhGkduWf0x631d5mo+zq0kENfydhYhJ4ZmpfXlghjCB1Py8YGG84G\njPpMwFd8AjUMV93Mq/tOvaaswqN1ljZ51Htb++mOgCxmRZZ3xrhEJuXgkGnN\nFUw38PF1Siiexe7DKG49AixWXTyTR2m72u9RN0cQhk0oNaRYNkVdktxJz98H\nz3xGfabRq/EoxL6fh0+8gCMJwadhCb+5tomw7LL6RNMn49To673ptgzjrQNR\naXTW8UpFpByW4zd+gTtm9JoOBWGmVHmY3FucsD4/C/xlAtkaJWPzt6E9xUyC\nHEESS3TJvOBRzgMfYy0ABNLMrMivASsGs3RxCRYObC27JDlTCMBup++j4yUF\nkyqF5T/l2Nv0bIV17PHZqVNi/rGckmpIaMIagLB4ztRZECOS4WrSlmZbhm3t\n5sp77IlPiCCrnAZOmGLJT1YHKSJxoYj9cDGnlOiKXk0UbklmYQvfs+AIhRwy\nYMscaJcGia3Bw1WPJ0NDVH1xTYw4jHwOp0xw55I/Oi4oQBM3vv9yPgjFVX0m\naWgl\r\n=rM/A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.9.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.9.6_1588185511650_0.111626903151125", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/plugin-transform-typescript", "version": "7.10.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "00273cddb1f5321af09db5c096bb865eab137124", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.0.tgz", "fileCount": 6, "integrity": "sha512-BGH4yn+QwYFfzh8ITmChwrcvhLf+jaYBlz+T87CNKTP49SbqrjqTsMqtFijivYWYjcYHvac8II53RYd82vRaAw==", "signatures": [{"sig": "MEQCIE4F1HGGDmFplmqOsOCYFlnBJ75m0fMs1VynvH+1/yWhAiB2A/9lB+Qrp+p4ZyEYc0y6RqQrKXLw88A3rxOlZ5SP9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2ZCRA9TVsSAnZWagAA7k4P/2pEeOrpbVdXXC8iciBS\n0PsAIHEtZmuLdjjgQgD4yplbgY2439IHdCa9CukD2jQcZXy04Jv1AgCBeqwb\nXCB5Ed7fIN6ApmHN/gUttVBv7lEzqrOAvzClG0CaCnhnwPWJ6lhoj6KpfSTC\n7ToPYwcDwE6/q5LOLmOqgZvFBuAVpEvgNZvmJJjG/65oaZ86aFaqMeh9QecX\n+M4rEq3XqA7/A8CzIc63t3xnWX34Jkr180uhJFKBVfQhjktI3p67OwPzgNQi\nAMRI+HrauVN+Tr+2E7LQc0YHPelXnoMu/0nwBhqlCbcwgl4BqLBQe5aQvYy/\ndm9VljxKnVSUDxcTweNM1Ijm4J+K0RIBKtcue1gFyj+MJkXD9XT+KTAIycvk\nQPrA9i4i8Uwi2NXhM8RUkHckOn+l6ulgn4haMUsC4DeYpXt00zizmTSNcoCz\n9XbNTJT3Z0qvwuLczWnkyAqxEsqNYxl6LWa/R0BjEMVIOXCXKxkLRB/nmpBd\nOdS7scgSw0tVuZT8dwsTjak/RRWGFbqegodOuUhumxb+luto0ugcNfVCWlCI\nRPaY0U62g8g9y0DNy432NzxFu5njaJmD8sQLtlHqNN9i8LpiKtaD1GLg/jzc\nzMc74jVyKIrmEvi879L5oBlzNVsDR9ipkBTRYE5hdGXlmejjoKeoIaGP4Suo\nGoYq\r\n=Pg7g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.10.0_1590529432780_0.6599002558638154", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-typescript", "version": "7.10.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2c54daea231f602468686d9faa76f182a94507a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.1.tgz", "fileCount": 6, "integrity": "sha512-v+QWKlmCnsaimLeqq9vyCsVRMViZG1k2SZTlcZvB+TqyH570Zsij8nvVUZzOASCRiQFUxkLrn9Wg/kH0zgy5OQ==", "signatures": [{"sig": "MEUCIQDHZIlBgzTYZ8TNDtVK6jR6c4aHO81/ZWB1DQeROIONTAIgUwziIIA+/FvF1KADhpgVokCItlHbcJU7UJTZ6rhU3Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTiCRA9TVsSAnZWagAAixEP/0vOgD1OIZUyxeEq/DQm\n04E+Gc96N1LG3TaOPLCdXD8XW6NJRUXNWJaC0vWlk3j3xz+eXipeGpdXAY11\nx2dHJZXvjpWd4ktQvmrzzosvkcWvrd3c2JVjPaBvlplFSFANulx6d631x2F7\naR8bxwykIqNSCIBFvRbeqqKYSz/WjzafFGWGH7wq6cebbMq4/nl5rEbl0xu2\nnNSiq4s2JgohSlFMDW6ebd3zPePhmpiTxu4sNF6ylVDYCMkH5E6qWTQTTCVy\nUV6wPOMY3MsKxhBkYnoWV9yfF2i/jg6ACbqaeGH9Sf1upXRU6xDWgzIKEJVz\ntcJFjuO5Cjj0PGqh8gV/F0IsSNYw1D8BZcKbc5wSQ4eYR6sZy7f5x7bJbaEK\nt2RjfKJEmojX7oi7I2UfNFIM5Z5bJE0clLVbvy4EKrFtgo4i00f/Xt9LKwCv\nw9HgMJSJcSY2NCuhTBklnXu+q2bNZ/E864BXNvHc6Z3Gs2dTj/yLMCMn1kDK\nWekfRB58Wc1lVaZYHgWgQCXhBOUyXVsC3lGfAMn1Fr4y+Ly23/B5lDMYi31m\ntQSvAzKyT7zRkJvR5uBKLcL52mMuC66IxWTRkE3e15CBqN9N/DzhdgqmwjN8\no1WJsWB5JZbT9TjMcwoHVpfSCWuxuJu8XAuNIJZzD/yMdfQTkK8J0PbiD+vZ\nCqRe\r\n=BUKs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/plugin-syntax-typescript": "^7.10.1", "@babel/helper-create-class-features-plugin": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.10.1_1590617314293_0.20759241526704764", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-typescript", "version": "7.10.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b3b35fb34ef0bd628b4b8329b0e5f985369201d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.3.tgz", "fileCount": 6, "integrity": "sha512-qU9Lu7oQyh3PGMQncNjQm8RWkzw6LqsWZQlZPQMgrGt6s3YiBIaQ+3CQV/FA/icGS5XlSWZGwo/l8ErTyelS0Q==", "signatures": [{"sig": "MEYCIQCaBs7q98KRciXmvhbZXXz+2Lj0NII4YWv4b7nak141swIhAJJ0CU7nCyrcJVlkg5XgTvsMQM8txcTJOEwVuCSyLMjm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYWCRA9TVsSAnZWagAAyPkP/0Ax1/znrTW9AkPbvFk5\niKr84EblJblN4cQhI9TM8LujlSkFGp2TQ+y+os+60maMkJvrFsbBFbs9ugPg\ne30wKE7vGJh95Cu+mSlER7Nuc81yeHhm0SoNcTb0ctJrYY+LRyUU++KtXnzG\nmqrfz+Oi3wgL7WAzo5Thw71bXrDKt5stWJZ+Akrh9+TnCAiG8QdqKszTa0Ke\n2KlS3jH/K5r9B6xj6vHfyIJVetIFPkodXpjDMu8Hb3TYS4q4ehweFxQ6NNLc\nG2l1K3iDPS6/cJluTtT9FJ6n4rSYF0s0Jui5yP3SJ4UXHUKY7IYWf8nouuDD\nayo6vVlvsosYdh1UeVfy3BHd1UtZSPqvCyYtFV1JkND5Mzro1eVK1EXLfmPK\nutT8Le/mc2S7EFcU2o7pyr/sD3wCH3P5WOkEC5NxPqlJFOMoymmDf2t9MfiP\n36To73wwFPHSZ9z+u/gEqOBCJvxqXaLB1IIafQQIpsLeE5gweCEAWX4qCrKJ\nTzYfXSY5SHMCyOB5Sb5DZ3rcMfasiCjf4xy/AFK5rt7ZRrsAsrdXDk0WVXxD\nujrLL/oNoRUU5Eoqfz+kbcf/hpSMG6Fl7YBRUpBOGxdkXQcNq4zV10tIzzZ4\nsuSqhkb6uIuK1vZ9XYiV3+zGzN9GwJCTonl/J5g+f8hJne3w4AGiO+MTYrd0\ntidT\r\n=xuTS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.3", "@babel/plugin-syntax-typescript": "^7.10.1", "@babel/helper-create-class-features-plugin": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.10.3_1592600085563_0.27337589940798623", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-typescript", "version": "7.10.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "8b01cb8d77f795422277cc3fcf45af72bc68ba78", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.4.tgz", "fileCount": 6, "integrity": "sha512-3WpXIKDJl/MHoAN0fNkSr7iHdUMHZoppXjf2HJ9/ed5Xht5wNIsXllJXdityKOxeA3Z8heYRb1D3p2H5rfCdPw==", "signatures": [{"sig": "MEQCIHJt+oGbEoB1MdQIH0ythCuIYawIUhsKCygK3Snvgy0yAiB994BV5cStXSqDdi0zEycCzlXtQw3tjon8t8my8cVAXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp/CRA9TVsSAnZWagAAvLYP/Rbb1exaC/5NjhHwuk8m\noJOr7qUrLwSIykhgBmOmASP6447Tl9HSsL6YSqStMFZYJ0wsGnSf3w4EqR5o\nvU9eaz6rGCTt/1K7ySf2Mnc7LaOO0r92or3cSFrXhJOAlCPwX9FEUMjpZd+2\n0sNPJjAtLzsUFyQ4WjvT9itr3oLJupEjinSwglVWd9zkrjpp7lg4TQ0tMlyR\nbVqCvwnoHgjzfviz/APtqGCzZUnAslEgbWEYNPozzyLy3BMYqaoXUssFwSPF\n5BSN7/Rcd0zdQQpe+TMTLFCdpAUeOebHDNASErnbzQX3bXxeYKQm06tOo1sa\nmXDngYFUR7vJegHOOfXTssJxnKmKKVDsZQskdiuToBKDO4HgJ9hLtOfxrgFe\n8oGiyvLIIH9HEWIuGqcKjEtof2lv51tBwjZFw751Fh+SWqtSekAeex+akoYp\nBE7FcIs1rbuzY6CEb7FxyxTf+6qf/rZ00NA6COB7WWwzQLJsQHOPht33ykMU\nhUSHOQ0Mg6QLMTw8i+wBzyqFRODpuT+4C3pAWXaaN5WlmAqFElmaQCoLCBF5\ncFceK/W83gPKstZGkM6bDIKOQDSVcCRH0e7xph5TYajR/vMsMAsXfnhL3Qms\n/DZpdH1If4yUvVLfETq87U+I7Ld2NXPWrQDl1SkPDVVBJDtlGDa/ylF0dWiK\nl0DV\r\n=YIXN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4", "@babel/helper-create-class-features-plugin": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.10.4_1593522815454_0.9465910506596309", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/plugin-transform-typescript", "version": "7.10.5", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "edf353944e979f40d8ff9fe4e9975d0a465037c5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.5.tgz", "fileCount": 6, "integrity": "sha512-YCyYsFrrRMZ3qR7wRwtSSJovPG5vGyG4ZdcSAivGwTfoasMp3VOB/AKhohu3dFtmB4cCDcsndCSxGtrdliCsZQ==", "signatures": [{"sig": "MEUCIH3NMLoUkCqbIb04vEzBDDlrphpW1QL8uGO39q2CCE4oAiEA6/OdxtwJEPE8+3+DY7Py3Q/PGU5wy40a0iST0c8X/yM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbuCRA9TVsSAnZWagAArnIP/jYZz4xg31saNPsm6jYq\n2AEz92JrIHTHwYoZKS0MdyiY2d+F0lKHDjsctz8DcwyWTcwvHNElAw0aDI78\nMwBTqrkyyQXdPC7DhKwHP2uqllHfAcrXY8dUmOP5ODLsAjjjJy67ISwWGncK\nibbyw/tY9R9hraS1TG1IrGzzI8a481SpMtyLPXSLmNjPo94rbsdIPQlm6uLQ\nlpCB7T3Fcu3XZ2UcBQStTJ3JrxaHK+Mo1WQhpviSAG4bjglAMz5Gqrp2pdSw\nBXc7K/BZjVyXp+zU8hdCzrrFQJ0kRs4euZHsURzFKyvjSAGopuesSmMmaAPH\nQYYxVJ8tPyhlvINAOqG1HDOvKIz3hpowcB9BItgoSnxECnv2kthP9xD0n7uo\nTiPerKiRP/TiuA68CPMYyOyAQC4/hBklLa7kkq61Ahl6Uiye1wJr4Cn13zAI\ntwh1FzTopCdKdA1SrUidKacmVCpQbqAoIrTyNxYOBH4QUSOYvm0UPPkd64AC\nR5dnoF15nwvMxKpJk5KywoMB8spxdoUyLjmq72COZ7/59tz3Iw/3RHJ2dnPz\ndyWEJOQCHyMl8e9vYCkwd+QeKkaZUA8dqauEYDC5ReDO63P+gqi53AOWZjlO\n1dP/IuaNrti8nnva5k/KAU5xcZiiD5GGAQxHrc+7YKChttlBBrPrD03bOLHz\nwGhG\r\n=xmjq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4", "@babel/helper-create-class-features-plugin": "^7.10.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.10.5_1594750701997_0.7398040033985804", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "@babel/plugin-transform-typescript", "version": "7.11.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.11.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2b4879676af37342ebb278216dd090ac67f13abb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.11.0.tgz", "fileCount": 6, "integrity": "sha512-edJsNzTtvb3MaXQwj8403B7mZoGu9ElDJQZOKjGUnvilquxBA3IQoEIOvkX/1O8xfAsnHS/oQhe2w/IXrr+w0w==", "signatures": [{"sig": "MEQCIBXNfEvaMwh4olNqwaoo3522bSM6FBbZ+QJ9EUC5AnEnAiB0OA8Z2qz6rfcQhHHCinqIrpLFJsZtim/XZEMqTyu6dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIztwCRA9TVsSAnZWagAAb80P/2qWLR+8yxzvD9qI0/lv\nTuN2iO+xfOShcDJLJCR4Tbzt+s3NVsBR0xIZ3abac5TFOt8eCIssBCXu5Y7t\np1oJLj6qeku6Jbnow4Rb8bR96zXRFwVTRKRLy8qaTU4xbwd3QnzeF3LpYPoB\nUaOUEEYy+7wku3BdorAdidO6VcFrJw+wZwLu+VmDeCAWY6LNt8HrY46oCQu9\ne4R+oVA/MfZvqMtwGHyMlqSRI52jmW62lxxcBUe49xc/H8qo7Y9PghOuECe2\nKEMAjYj/XtkCx7C7bXrzNJVQEV9w1/2eE5PIwu1J9isCND29KMysfXAAFnYS\nmt/c8V5fcgMeklvdsjAbIzvCEY6m3IkQRWXP0tC9jvJTlYPb1AOUYWvtUm63\nTg8F/K5ZyH4+Kr4vmYQa+UL8Az+APEQeJWL7DneskVPG2pjssR00PHDrvUkY\nKHNneC3kp3wQ3cL1a+V/K6dCVnJMojz1HqRgS/eci6t0+dng6N/BK1Ku/RHe\nmsVJs5edSZmD6sl49F91cR6SYDCnfFa4pbfg2+Qn7hJOuuKZK1MkIbKhpI4E\nMXVZDDq699wmDgb1gTZu0q3ZC+nPTVQmXHaNBpy++bJyKCKusGECheTqLiFF\nm4u6dmIUvtIFyMojibD9JfXDN2dDrfpUw0pT2hIFo8cIkzXNvmgTOD2FR9RY\nPQIu\r\n=G0lM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "_npmVersion": "6.14.7", "description": "Transform TypeScript into ES.next", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4", "@babel/helper-create-class-features-plugin": "^7.10.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.11.0_1596144495854_0.4208001134846491", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/plugin-transform-typescript", "version": "7.12.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "bd6422833a56e4268d8d599238f0b3c5e170078a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.0.tgz", "fileCount": 6, "integrity": "sha512-gahRNAWgE76hjI3TZPVEfV7vGjOCJi5ACd4eSoAItk/ErC114i2UHnk+1ScS2dOour0p6J6kB99hNFX2vzL2Ww==", "signatures": [{"sig": "MEUCIEyOdtCicL+6OCuaoyqKvhDQUovm+NO8yWkkOIdLhjafAiEA1fD5r+GhdA04Vaw9mU/I3g4eHIJfLXyumZaayQsle78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mUCRA9TVsSAnZWagAAQ08P/2UaPph+cWbQuki1JBuD\nTU/3lWGY0wq4iJcDOW8tyJULw7dSIupHWyXHvq2EqOuY/+31HgYRoR8GkxTe\nib1VTwH/5Nkis6aYqVgglsfUTbG5VkJUO3dO718x5FcQTiIKox0akNjgC3u9\n6J/b3RiGQf/b5XGLHcXiwUZ7K/0AYPU4syHDn4rho55+s47qt26yhMitbEup\nl7mLjYF0N7E4GkORRTzsyP4SEF4gekl2gsinJhQL0HHsKqdKYd5ryV27ZS9D\nKYIWH1dXAoIMkyWX02cvxhza7sURTG98lAjdxv1w/YYy+RqwLnn78bpOHqYi\nzqqAzxrj4JQSFh3AOWoawJIIbS8mtDnoVyTAgxD+jn1iVwiL8yPpySW4sbW2\nYHH1OBy8F2JkmZ8Mhh5xtEkj17vYC1GhzcVtQdbfpEhSFnVTCHVIWh7yiaA7\nUSUxXnoE8dJ+ckTKbNRPfZguyQsTyWJrNOVV0aBanfIHcFBOeLgJCMp3q/Al\n4hfFtntvBqNJq1qAPGKB67GX0L3Tes/vF9e56J2jfFuOzgJqdSJ5N8qGh7Uk\nYGWmeLYpj2lQ3Dsy5m7rJpNjicho1l31Sm8Q4kVUpb3uXqNFVssfvtjsH+7E\nPvWTe/Sax8i63OFseg0YUKJTLaEdrTTTb1x9t4lqPm/qQyqtePzL1fyPvssk\nOel0\r\n=flkk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4", "@babel/helper-create-class-features-plugin": "^7.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.12.0_1602705811930_0.6499995343769973", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-typescript", "version": "7.12.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "d92cc0af504d510e26a754a7dbc2e5c8cd9c7ab4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-VrsBByqAIntM+EYMqSm59SiMEf7qkmI9dqMt6RbD/wlwueWmYcI0FFK5Fj47pP6DRZm+3teXjosKlwcZJ5lIMw==", "signatures": [{"sig": "MEQCIHA72r+EBxuwAS/yFOM/VkH2hBZzbbjnUaMgdOSEJt3WAiBPE76VAeF3BUjE/Bw1UmttqU9lp+CgudJXL+acH9XYIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNBECRA9TVsSAnZWagAAND8P/304s3igpx8LsEHQVSYc\ngIrr6b6jZ0eOEqt88HyDSOg9vWK2yjIJhtnbyFg3GuAJF13NPMnOCvohVgTI\n4CnxZEDJ6i0hevxHMMaS70mGmxYcXRl4XivRcjJbaoftoHbI0lrz087z+Thf\neLc/cdVwPVHhyE+RnZHUcs6QGdhvJXPNDWn5ykJkRqpqJsF+axWU/3biovSG\nUenRiA71zzNeCGIcBSe+E41zUt6nMlV6x0CAlNYWjm20efkTI7Ro8zatmOjw\nieD0Fhow6dhzFZIYDq3NCA1jonJAOv00e6kG3+lL3eQaevZsY1fNg+9Z647u\nQf0SZ57wAELVnmJIwTfPdVN+AYK066wgeCLdL96rB2DSvUuwCYiIvIppGgg9\nzv1S8hCMGhOcZC3V00N5mmJTTcoLZLKgM9SOR74ELzuMaTrRe6pAzL9khy1L\nbqcmhN2gZnOfmoUlsSYXAB6ikNVFxVu8lWrrPjZXylwOEdWph3H8djhjHwTN\nrKul2bK8yy8RFvnKWPciYl279+ONkiP6rAmgqWzfGcqgX+g2XCvbURAXqxSV\nb37mXYl2+WP7PeSsRCPaLY4WP/2+Ds1VQ04EkL1DnbKZb1mSCOfJ1gkwSToT\n3s9gAZz8H+4DLYTIm85KIxv3qHAIlVhSR8kNGmR3rmjqP7ovawGRaFn4jXI2\ntmrd\r\n=6U1C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.12.1", "@babel/helper-create-class-features-plugin": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.12.1_1602801732565_0.6796065215548808", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-typescript", "version": "7.12.13", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "8bcb5dd79cb8bba690d6920e19992d9228dfed48", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.13.tgz", "fileCount": 6, "integrity": "sha512-z1VWskPJxK9tfxoYvePWvzSJC+4pxXr8ArmRm5ofqgi+mwpKg6lvtomkIngBYMJVnKhsFYVysCQLDn//v2RHcg==", "signatures": [{"sig": "MEQCIFSjVHxAzw1p1dEwpokshWNxedd9m360J4juTpE2kvsJAiA01epCbtuYn9MWm9OEBlaYlhJODGI6u/hrw4Lf2pNu/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhzCRA9TVsSAnZWagAAS7UQAIdn67ikPxQMNepcQkQ6\ny0ZC0PsfnQtI4c8WhvLA3m5dI/bHBrrAUKzlsWlfu4/OjJJG/QUcyTxXT7Mw\nnVmWncjSBACf5hQs+aZtvAggW3vD5lFhpWBct82t9AatOQ9KoK/y7sq/5Z5C\nOYCYvp1QnGr/GrTzeDFRK4Ie4SIv9oGGW7eJs5EyDDc2RaenZ5f8z5jAulxZ\nQvN33RilLG2fUPMoqreSEb7rqjD26ikIWIPsProtlQxLXoHZzZXm7YEv9Wg0\nSoajklByuwcKtgR2ov/Ue23yHhRtScG2bLWhDGyu0q071ccYk6oA8clu4Cx5\nl1VTwLukTMp5nHrOyrC+FbOm8agMoc9RseaWVcBUdFCa4dI33tTXChHxg7lN\nELpn9Ozx/ZBZkOaVftSaUmhpnKw1k96w7fPO+xVEtsdykrjHfEub112ePnrQ\nXBDT7n7d37okNAoNSs99kyN9rxvLIJkDkVjHVXI38o8lwh53slKg6jlU4uJ7\nc8B0M5PXCetvlS+mA1PB274lh4SZ1tAd340YTsYnJEILiqEVDp4X6C+EQn5L\nC/OJoNuYgRuF/brFg1Ub6J1crUzTBQssNaQCacpBWaml/W4vCoBvkFG10zpm\n/iujnY9mqNGjbYiLey9GrwuHCskYJFthxnbG7twZ98kqXJTHCBAjwuVgnyoc\nw7Fy\r\n=VQKb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-typescript": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.12.13_1612314739561_0.18574161988503035", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/plugin-transform-typescript", "version": "7.12.16", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "3f30b829bdd15683f71c32fa31330c2af8c1b732", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.16.tgz", "fileCount": 5, "integrity": "sha512-88hep+B6dtDOiEqtRzwHp2TYO+CN8nbAV3eh5OpBGPsedug9J6y1JwLKzXRIGGQZDC8NlpxpQMIIxcfIW96Wgw==", "signatures": [{"sig": "MEYCIQD7RYw7Wf/hJF5GlE4awA/vrxRCnARoiw17onhY27vhiAIhAJH456ITaMetXleG0SmbB9hp9QDYAP4if3mCj5eQkiYl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPyCRA9TVsSAnZWagAAdaoP/2+924tG1sGXtkLsVSo3\nBkvwSXvKQgetGlU14QbUYEWXRY12WiZZBQApfwlKPAVs/kM1W51R9bsedsVx\nFRgvN0QOLixQ7Db3c2xy/Xl8P4fpg21uH5NBJ3iKh4paO3fkzHmLv3FI7alu\nbkdjwfb5fbvpenpr/EdTW3h6woNJKZ5v0/BLGuxkmmloat63CkCt1QusoSDA\noabFax0jBfsPN1ACFmhUvvj7lDt88jeGyY5OwQAB2Sg1KOXg2DhIkPNiw3zn\nge8Xo4bL9O+kgFrITdCcJH+Sj/CDXUmk3CjMl1ITpoW5+SbEPQn1U1DgFM9d\nP/5BMobQ0E3QR+ORwJmKfZOB2usnmn2nvUhpddH5EeJ8J7QM4NxE8pOF9wuB\nTmQgR4G9nJNlD10VJ7oNpZcFgTrleBSBeXO66kmbB/l9UV8OAXEnqdqVb/rG\nMFXHP5mUHYVt/sgwdR/jq7HpvZChB2U/LfBDRqKgANrN2n3JbUsirCGz3P4a\nkI0hOTk6EujgxIw9HOXLGyMbo3hmPBX8s9HvWH9e6FzMzWoBWvfCZGlLUru+\nySEqLojrnzxEW3mD9o8DNHRnjPqsKC/iHlYOL31tsqp+49MpGY+33K+ch+fK\n66cvgzaLM3/gShK1QIwjmAJSWEti8eAgzn2DVj8Rq0Nbu25sLKK5nq030fjV\n9qrX\r\n=40/+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-typescript": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.12.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.12.16_1613083633834_0.14386968878557926", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/plugin-transform-typescript", "version": "7.12.17", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "4aa6a5041888dd2e5d316ec39212b0cf855211bb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.17.tgz", "fileCount": 6, "integrity": "sha512-1bIYwnhRoetxkFonuZRtDZPFEjl1l5r+3ITkxLC3mlMaFja+GQFo94b/WHEPjqWLU9Bc+W4oFZbvCGe9eYMu1g==", "signatures": [{"sig": "MEUCIQDnzqISSa/wGQsA40t4ZHyMx42yJED6n2WWqzAs9gV9IAIgPWVapbuTLT1Vzs/IAHY4+RLu3eGz230ceJpDShh+x44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoQiCRA9TVsSAnZWagAAAFYP/Ar7UuPxGD3nA0opUn5Y\nzjuMbzBE7Moil5zuaVZU7HdS4Ny8Xp027KQ/V2P16YcSk2j45QTl/hhDWAK9\nsTN1+folaU/jj5Kvul60O/vAm4IP473z3dOryXbQqHYU1nsetM7BxaIiYVKc\nMbNCp24Z59jNnZn94GdwoNtvwBMQfLElEEH+TDJ58CXpg0Luf7IU9VfpHV9R\nL+ZHMBcZfAfeso37z1VW8sWflK7HHO0/RLsjrrn3gOwjThxJYC/ts01OXUkO\nIAdtlRC3Q7CbsBiLsGm2FGpwAuM3GI7rVH2g6Y3xu3S0Kktsbhi5o8ORwHkZ\nqDYoj6Gmgeh6n/ZBY4aDJZSku5SME3sSmsBF1OmbEFlnUTEINSpx/sheC6f0\n2y7kUoODy+OhWqQxAXdEwHP1wNd49IqbRA3GAPpPxpulrWi9aUT7oANM8IDa\njJXCfJeGQ2zvyq2/8kZGonKBbWJ/LcJcnsHA2rWraxitTQmdJWJ1fB34NJwM\nhi2t+9DOZj+KGnBUvr/ixi2D7LhN3QO8iT8EAsO/pNtouxaU/+VMzqyPhV78\n4U7wItlit2Bfpfb4e8vF7goGjI/kGT5jVHxiqjJ2vkRAnYY5pYi6RE5e2afz\neWwdluaKcPRSVrOuFpmJ5DsAXqh5Gzw7rkmD6Svu0Gi6l/PTzsEMui3r9Oaz\nrysO\r\n=Rc5d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-typescript": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.12.17_1613661217710_0.9756007300651028", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-typescript", "version": "7.13.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "4a498e1f3600342d2a9e61f60131018f55774853", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.13.0.tgz", "fileCount": 6, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>uzaU8R8dbVuW2Q2Y8Nznf7hnjM7+DSCd14Lo5fF63C9qNLbwZYbmZrtV9/ySpSUpkRpQXvJb6xyu4hCQ==", "signatures": [{"sig": "MEYCIQDYOD/ZxrE6f7dm1+Rpf8kX+CAcQ8Iv+nOb/Ful/QDfCAIhAJPPRTfFhHMrH2UEEGh7RahxO7bUs+r73tfBzIIkVrjL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDU6CRA9TVsSAnZWagAAvxUQAJcwXugzFHa58Lj6ovyF\nrbRTB2Mdv+UhM5mB92Gl9bbO2UXzDDOrI+55qv2fSt1h36XeV7kWTikUaq0p\ngYloROtb0GeBwFyHAaB/wyw7j0zaGW6ZqlajC77GhwmqizGZOK99kD+oMy+Y\n/uZAyDUouDpV5zDbDsr/dNvl2hQ/4lciTO3rSTf/D1f7mkQjwYM7E8VLaj/3\naUe2zJ3ypvTrx1+0gkF4y9aQC9LhJgodJr9/d0Tdi/8dI8V/ASeZ9sI/T7EQ\nFO83ZB9Tt7a99Ls0YgbdS96xLnQiQ+twBYJ7eisgBoyn2qQ2sbVz14yGiAu1\nDJB8zRAiRupTRe1piaR8LqQvBCFPGZpHd7DM7XfeJetdPgTXi8PY+NT5dEWZ\nV9Zi9rntjoUFypJQMI0TH+Io6kYrFr32/TELAeXIRKguBLxp9GWxfm+XMvq4\nfLm1MEcvuC+5NRYDCapaW7l4LwqSEWGV7Ms2AndGlm+73xR9tEvn/dCvYe4k\niR70t4vvA8ynj/OQiQdKQksMHobnMnL4lsk6KD8EQLKETV8P0dspOBK72eYx\nLRNGx8827BeFs9wM2bhpdH240qvroxb/p/kPIKJn5M92H6KTPULmP2av+CQc\nshKtoJ4IODBa2Cx0gOPTgHaQjSQ88OHqQt/PAAvYPTHHG/eEnlIWs7lbmfNx\numqp\r\n=6Tx/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-typescript": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.13.0_1614034234473_0.5432341991043497", "host": "s3://npm-registry-packages"}}, "7.14.3": {"name": "@babel/plugin-transform-typescript", "version": "7.14.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.14.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "44f67f725a60cccee33d9d6fee5e4f338258f34f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.3.tgz", "fileCount": 6, "integrity": "sha512-G5Bb5pY6tJRTC4ag1visSgiDoGgJ1u1fMUgmc2ijLkcIdzP83Q1qyZX4ggFQ/SkR+PNOatkaYC+nKcTlpsX4ag==", "signatures": [{"sig": "MEYCIQCRzCpliUuM68ZbVX2TWvAv2cq1fT0LVuDo+VTK6BJWQgIhAMpwRf+yMbWJ0SNycSCLYaFxwYVHNzLd4v5+VqLI7HPF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotW4CRA9TVsSAnZWagAANFUQAJ+DkO3U82W6y1m4pJjL\n14okOs4N/uMptuafyM8RH2QZntKQnWXgP4ekh6oIBTuzutFU9JNDWYFhGs6H\nSAfA/O0T90tsYvG5o2CtpJjjYanGTiiiPVPCndBpkgPMZDnU2h/9sBmW+UAO\nMoTanNLW19Mci1QbQP88VzEIq1V5dpSPD3ibrB1Op3tWDMQ9TTsc9EVUXK71\nzKSCkdBNZzJ0uu4JfEEtWJcte+k+hs0XfRa7HZ7iRDpdQgdlV0bK7x3n+Pif\nj+u/s6c+iFhMKjnKo+MWW8gNFUdNsYjIGbtxhsXXxcEgrgiq+a1Wra2unFE6\nErlTr0XUoKVs1BXHJx3aYh/my03l81wqigSeFFj+zWzUF02/A16ZyiRW1Brh\nvKBecOcp+4gepcUzEJpSUjUdjmYd15PrLlQ+/djs9G2msp6B56mhjg4JXAY+\n0f1GWoNQW4G7zPorH1lSMUTN/6IbS31ONuP1J09KSMXjjaePPpq8pxB62cRM\nwq3IhbBDPEm2AiOcO3zNHT3o8Q9wJ38ps9ivmTMZgMNlirj8698DLYVwWn2n\nENOSWFttxMF5UBlkxxpYYM4oe3rulAXSVFALDsYF209g+vxnjkbW0tgRpuJi\nAFAp0ez9sGsOF/nPXlpD82Q14SnB178iSlkKcerzmnD8RnWu29YBoe3l2EHF\nbY1X\r\n=zEgL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-typescript": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.14.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/types": "7.14.2", "@babel/traverse": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.14.3_1621284280428_0.9127471420297733", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/plugin-transform-typescript", "version": "7.14.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "1c48829fa6d5f2de646060cd08abb6cda4b521a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.4.tgz", "fileCount": 6, "integrity": "sha512-WYdcGNEO7mCCZ2XzRlxwGj3PgeAr50ifkofOUC/+IN/GzKLB+biDPVBUAQN2C/dVZTvEXCp80kfQ1FFZPrwykQ==", "signatures": [{"sig": "MEYCIQCdf+lDzNG+IMWje+wSlhFO1K4QqfBAYOoEMT7++9gu3wIhAN9hejCATH2cTSCYDtvThsbXa+qWkprdHh/X0iOJciAx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGZCRA9TVsSAnZWagAAGaIP/012SLJwNOI+ZnaVrY8W\nbdLkRnPg5kZ0Kjao94ZfGYrv5JZJOTXhMYVmlBKI3tSI33SVPaJz0CaTSs/5\npqjDvKFReSEypgcLRETHE6odWd6X7t/ZGqmezsa6+i2iTFgyM5e4iPyfudJ/\nvdjAgRM8ZtKspEe0sSlu0H4Pff59oayMRyyDuaIMvSOhsL+zj/h2ENqw9DG0\nHx6LMlW8Ctdr0PD8MDU/F9q4ptee7na3TIs7Zr8YOQfiHD+xN8cH2lXIf8k/\n/2wDN4fhwa/SGr58p4OzaISr0xp9oBmyBY5IstRfAeFqOOH8MeLKDBDfqqL9\nvYBWfasVEUTGtlx3rXL6qAebsTchmCda1UrSyWGY9H6uHSgCr3mDyTjCnjkK\nI9/W1KUMCNP7Abwl3q2in58+7y5DfvRxR3at9dvQo/jIlRF7NP8J0p4hrgvo\nblwgp6Xy6+W0Z0b2gWT9Xy7uFNReSyq7UIIvkuA9yZyTDQOCTYjyxkzxz/hJ\nk8tNmWl/OUJpS+MHEJ++8/ByI7MZ3r3aZwqb9zQrwoT0tL1QjfWJB0/spRyM\nu4OVt44zCiLBvV3var6Zjz0Uakv3uXmUcax664TLi2wPz4qitImRhhIXTP6A\nD0rvKyzBR/pn7jNApbNjBbV3WZSB3QWn0ZW+r7YcRYayalwok4rNNaUBuh+E\nAqXr\r\n=jdiL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-typescript": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.14.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/types": "7.14.4", "@babel/traverse": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.14.4_1622221209455_0.7714506985210463", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-typescript", "version": "7.14.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "5b41b59072f765bd1ec1d0b694e08c7df0f6f8a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-cFD5PKp4b8/KkwQ7h71FdPXFvz1RgwTFF9akRZwFldb9G0AHf7CgoPx96c4Q/ZVjh6V81tqQwW5YiHws16OzPg==", "signatures": [{"sig": "MEUCIHPP4w1ElJrM6RIRPKyeNgsDNVMdezhK5EpHOpd9Rqt/AiEA8LP8X7389Sxc0xSED1UYETG3U31p5OYUjks4tThBfE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsUCRA9TVsSAnZWagAA9n4P/juvGmSs/Koop/vXxwS7\nwlhGPd0KrBz0w9aGMC0vQvk4f1QiBOlnUsDz6Ca8021FC25MhmLhVOmtm3ex\npbAp3FGmcTvNYzmmND+qMlrzuorUKdu7QADVDNw9BEEDwqL1CbhSxeMSYb34\npwE4KIIL/sCU0H542P+Cu/wOLVoQZbDA0b1iLJpd7fzjZ/TKTf4VOEiX0/SL\nMaOa5fmq3Riq8zYU6/ssE/vn5VHYqhBeZEGMgttWh6iA6/kVDzOZzaquC0Hc\n5RY1SOAS7mQFYKX6C6F8Rl+3tU6v2r10pGzqBJEjtOwP5+7ln189zk9bNzE1\nJerK691CyQKP7PC0K5oIiFRdayDBkEpO/ux4oYfYumNDk6KuRJdZ5b968q7m\ncWggYD/6UNqfie9Ly3ptbyarGimxULc3tQhiwK8Q9OgRpXyTlZ9YGyvvKbMh\nbyGf0zV1IaFdOYMeeJbZk8JdJ7gUekR5MJNWRVU7yTY0YK2EgkY1Rabxty5T\neonkw7dImxR+RjXMAHrO6igz/0WwJ1iNyvyS3CU3+1olwGuQPsfcHdWnyC6W\n4ZR/6YYI+XII2Q1L0ZQ6tFjrj6MtGv6eCm8wpWLhx5Li7qH/59glkhdskTRG\nGmMsT7N7v5tcx9uM3KhEsVWWN1A1lcGveM6NNv3dgI3FpnDHboDlnpBVIWNW\nFxlD\r\n=WSqs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/types": "7.14.5", "@babel/traverse": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.14.5_1623280404397_0.29876592107987343", "host": "s3://npm-registry-packages"}}, "7.14.6": {"name": "@babel/plugin-transform-typescript", "version": "7.14.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.14.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "6e9c2d98da2507ebe0a883b100cde3c7279df36c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.6.tgz", "fileCount": 6, "integrity": "sha512-XlTdBq7Awr4FYIzqhmYY80WN0V0azF74DMPyFqVHBvf81ZUgc4X7ZOpx6O8eLDK6iM5cCQzeyJw0ynTaefixRA==", "signatures": [{"sig": "MEUCIQDzLjntesjGJGdZgNFRkKlVg9rY6enTmLaO9+Ixm66mcQIgAwou67X9/yi+pUBfClekz4o2lYLYm4YVFJO9HKgPKGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx9CxCRA9TVsSAnZWagAAjd4P/1Bn/dU2gfkgQbyS7Hnb\neRdFm6FwwVkC59/+M9OlZpJbrfZse/vNA+IcO/UiFc36ec97/06J2hpu5vqC\n/a8MK7IY6qQXoeY/6QXqvube724bwkkLsMu8lsXpjpsvXd57v0Gd+KJaFZHJ\ndsIyD/pVlqIvd5AcwoqBIvF5oUjdtel08pvmThFdu2qg9XLtin1b7gcZKfZC\nv86FI3ldWs1C929EXKOeAJp4mbzamYMixKq2QUIIGTx7yXpoAq137ZIQvYD9\n6/6SisjAXa0TJdu5jQ7LY+YmfYrGAyaxQcbBvkmr2EvkC6w7dl3CYIkijxNJ\n6qLGBcFEdPGUgg/92rKYDJIgoBKyHSGumcRAeOYwTDd30axoovHPb/fZMXk9\nkPHQ4ZFpJSDHJehXp/5uThFg21TdCriOeeQVQK0VcGo7kWTSjVOitnc5GlwQ\nntGQCiWaHD7WEuyXclpauwbC+xi+JxdymsIuqWIdgD8mPGCAuQw0pATprXr8\nEBKtbUuwZ0vRM2vPIfm6akIujIvpUb0iMlk13RaoFDZWV/reCl2IpjZlYAcu\nIHYcr8lS35JjLFAozD4qB/vB+pcbSGQXzoXFYCo1AhYmM1/SXV8YhhUzk0OT\nkGfBIyB4dtTz4rO1cOiPzk2o9wkjBYvJ1gCFlFa0K8SQbY7FVogF518cRjKp\n7YfX\r\n=ILNx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.14.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.6", "@babel/types": "7.14.5", "@babel/traverse": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.14.6_1623707825426_0.11653203407297785", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/plugin-transform-typescript", "version": "7.15.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "553f230b9d5385018716586fc48db10dd228eb7e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.0.tgz", "fileCount": 7, "integrity": "sha512-WIIEazmngMEEHDaPTx0IZY48SaAmjVWe3TRSX7cmJXn0bEv9midFzAjxiruOWYIVf5iQ10vFx7ASDpgEO08L5w==", "signatures": [{"sig": "MEQCICpWLsoX/bJCGiURFdgRf1hQ7MGRBK5e3nvDwuetDw07AiACnzFdXUlksdu8ZHchqApICxFmQYsLroEcX5LLWLWGeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLrCRA9TVsSAnZWagAAWq4P/2oB4hYKDnDIXm71y01O\nft3C5JjcslKcIUFcKNpOxPkMNPgLUqmNh6+lWcWD47UUyPxmWeVxY5X+q8ZC\nkqzyfGUvr6nX+uVOICUzNWnlSAxaycIhh2S63Uwgh/lTD+SRcN8GoLQm7VBl\nSXHwDMKTYFlIwUms2YxMyZxOEzMlpf+zaQacWBHR95KuUep5OK2tlkpnVUbk\nH4+ePDFILSmkz0h/YRZDOwyiWrJm6QyKBJE3SYs/IYQXTlJEMj42JmS0r54C\nsn2EWv779zIV31Sf6H8GdD46HxeKnW/T2Vw5cqYbwUoXGw6jHZQzQd0J6jZZ\nFQcHbj74Vf9k7haKxZAfWehy2DNWTVWFYYwKxnTkqLQ6hP4Kksrw7OIgULw2\n6cgjxkIPRMl3ZFARSERWF9SOpOoxOvM+P8ttgNMRxO5y1KNDwzYRotKsdEXg\nk+iiRI/qN6BiyUdwvCoAAa8oMg5ox33I2sMSLcdmHSk1i85tLTfud8DPsUXF\nBCveBwPjtT+GN3v2cRA4kaRkQJRXWzdyWE43EdOj6bttTnBN9YhqTi0ndf2X\nlfH5K+/yd7Mk4dVRsHJUit7ROpDwg/k3vyH8O+Osy4p0ipNDFeuCnP1kFqN3\nTJgo/79JPV/O7I3dO9BZerFAU/S1d5at99UXIUpm2hecFqwjyKr86nkemZu2\nwJKF\r\n=DUFl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.0", "@babel/types": "7.15.0", "@babel/traverse": "7.15.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.15.0_1628111595222_0.22210284864747654", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/plugin-transform-typescript", "version": "7.15.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "db7a062dcf8be5fc096bc0eeb40a13fbfa1fa251", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.4.tgz", "fileCount": 7, "integrity": "sha512-sM1/FEjwYjXvMwu1PJStH11kJ154zd/lpY56NQJ5qH2D0mabMv1CAy/kdvS9RP4Xgfj9fBBA3JiSLdDHgXdzOA==", "signatures": [{"sig": "MEUCIQDczCbtHXFUJT/Uwy+SVCj+f55M12ctaigTlVsJGMliUwIgGQFPA0dDl04gQGHpNUqeTFWFZgPUsWwFsOm62lohYuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUS0CRA9TVsSAnZWagAALFwP/1O5X8W+wrbIgItCsRSd\nW8+WrPsanggeW1urgVa7hUHvbGvgqGqBZZhi32054+8oafhj86U0DQxiIQFf\ncg2UA9SAgCqnPrUBvzsyj21oX7YF8EuXL8VSKRf7hjrkAbf8oezZTxEhml1x\nW0sbn1FGjjoSiiru/zHSNmziY6bWjcr6LuMfvs/8MSVKjysfIREvCvMFCXLg\nEjh6g7jNFsnRKPgOAWx/vzuWLTpB9FGbfLwmq1cg90x/ovtOzlA6PiLT3tLF\n+nSSXNvJ63nUy1bRY9fVC9vQr8Z+0IwKbtxIbm6BerKCXE3N4zkh/lO0lCAP\n92Ofwsd2sp12Pl93gnjbvkOmavzd1bkBW4qTqbM7DuAgUDgyBOxQfDhkmq/W\nnwCtBPufTVAqwtwlC9qwNuJ+QY6qoWP/COvo1BiDrKCspjRwHaH082UmZ53c\nYyCZ/GVkIACSxdhhKmiiq7ikCvBiORVQO+sW+J7HODpSnddA1EpiBwo96/fy\nF7VN9uvzRx6AQlRrnaaQwNBR00D79AFVfkFAE+SVRPGzG1tOEHfLbIj5Jmwq\nogd6+9JZ+EXuTBCgqqXABcebDpHT8M7BKjDQBsNTaNjQcS6gg3X4SYC059if\ngyfb2E1SOAHrGOMddaxNIWH68dlBDJcPeLVuLZWBHE2+WvjJMhYuSXpdLeIr\nvDjp\r\n=GMCr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/types": "7.15.4", "@babel/traverse": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.15.4_1630618803883_0.18169114685535792", "host": "s3://npm-registry-packages"}}, "7.15.8": {"name": "@babel/plugin-transform-typescript", "version": "7.15.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.15.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "ff0e6a47de9b2d58652123ab5a879b2ff20665d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.8.tgz", "fileCount": 7, "integrity": "sha512-ZXIkJpbaf6/EsmjeTbiJN/yMxWPFWvlr7sEG1P95Xb4S4IBcrf2n7s/fItIhsAmOf8oSh3VJPDppO6ExfAfKRQ==", "signatures": [{"sig": "MEQCIC3gXSz7u6I7ZhO0Pr6s9qVzy6CgV8cmMTOD3m4/DR/SAiAdIwR0gcM7Krq3qaXHvW5+qrW1ML0CjcpzCt4mNArEMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30901}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.8", "@babel/types": "7.15.6", "@babel/traverse": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.15.8_1633553696739_0.7018489207836631", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-typescript", "version": "7.16.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "6af3352077c7c11ba65a4d3e4666bd9fecb2183e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.0.tgz", "fileCount": 7, "integrity": "sha512-dNUn7RSt1dykzFTquwm7qND+dQ8u0SRhZpPFsm1GYAad+EEAirNTjqu/fnqB0zVPwjcZQd//DYWszVKoCrQuoQ==", "signatures": [{"sig": "MEQCIDkuXlkTNU1c9Nd0Cr2sjQJOlJZppkNiXQlkzk2dlgWjAiBaYJNw/2acQIsYgpFcars2mAn74jrFhmbXnr6SmMxPkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32090}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.16.0", "@babel/helper-create-class-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/types": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.16.0_1635551281556_0.12720369900724293", "host": "s3://npm-registry-packages"}}, "7.16.1": {"name": "@babel/plugin-transform-typescript", "version": "7.16.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.16.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "cc0670b2822b0338355bc1b3d2246a42b8166409", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.1.tgz", "fileCount": 7, "integrity": "sha512-NO4XoryBng06jjw/qWEU2LhcLJr1tWkhpMam/H4eas/CDKMX/b2/Ylb6EI256Y7+FVPCawwSM1rrJNOpDiz+Lg==", "signatures": [{"sig": "MEQCIBiRr2mZox3HNACIj0zvQU5iRN3lBywXSLEb73ovVd/NAiAnKaDn9iqcDiUfC7pxiSsFjfwvLWjt6Hv4Gr+VFifPJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHESCRA9TVsSAnZWagAAWZYP/jG10k4+lIiESeI/bA4D\nKvQN5cZA6X4BjRe31GyewoDxC7H8vFWqjQSgQESklUTyJ1eKaNFaKtCy5aij\nvbqeoNMr/BUsWrvfu5nChIADOseZ/Rmw6w8wTvMgo+WGJHWWAyOHaMf+6S5E\n4LujM/7nEkeZB1W5D0e1Dv/NR94f9/XLCjvcw4YZ6pTAh/VR0gwVRRWcaXd2\ntjbedsYorTjXgJ7srYC4GBANJulRzEaE+gNOMxicF3cXE2LGXnSPGMtz3HuQ\nwh03GwpDfoPLrwVlKBtX/FB+qM+KnRem6N5Ex2f2T7rLEmYgNEceqEqzXoup\n2PKI05Hecg1mXJmC0oJLWurpN7xS0uQEhl12Deq+VKJVWTaF3qm63KBZO7/y\nPA6N28NdiwEJBwUUxu+oyQV5ziwjB9duiJz3nrIxdOgyVlyq0MKWajmxGTbh\nNxJM2JWdKBZw4GfZ3ixNx+sC10syY+vaAVKHNzeN3umWx7sOawbygS6ah6lE\nsSG1qKZMMtrPDfPQW4bbpK3BAilZgzvdzhWCksOgX5CUPq2tozjPCELo52mv\nBDCAc02ilTmLYAH6ZnM3UV/F8gysAeFdO1JgIGEUeYLiIZJ3ffvcHeBqJg5y\n8uOABMaajAxCZcSBDNMkZSMxXOSs2EbmhwCkFEuvg/oarKSuDddiiiu4SVdB\n4z8Z\r\n=lZyJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.16.0", "@babel/helper-create-class-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/types": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.16.1_1635629604628_0.5229451135406407", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-typescript", "version": "7.16.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "33f8c2c890fbfdc4ef82446e9abb8de8211a3ff3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.7.tgz", "fileCount": 7, "integrity": "sha512-Hzx1lvBtOCWuCEwMmYOfpQpO7joFeXLgoPuzZZBtTxXqSqUGUubvFGZv2ygo1tB5Bp9q6PXV3H0E/kf7KM0RLA==", "signatures": [{"sig": "MEUCIEik59CZoqSTv6WCglFHPJVRfPacj5YyU0ppXW1J6p57AiEA5pHeRQVNu0o2gQ7pHZyrkm1uj8nUZlVVetNuXJB6M34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk11CRA9TVsSAnZWagAAUYYP/0VqWbO3GRBVLgFA/mYP\n9lx4r+3CYypF6rWl16l9JwOBrB04R1ZWgy6d1NSnDAKyhyLEl81o3tiU42wf\n01osWBWqElz3v/pBZU4m8lEo++T801fojSno5dRsuPqc5eXFyXgsBvOSsTDI\nf4BBqhxasTNpIbrzYjwBpIbI8WTI6oWEDZmlZumBLxRL2dlp7b/PH7kbvGL8\nn1ri/owlpBp6gveGoRI1g2V00bkdkfjuKXpQTWphsTf/utt8D9fyDGNfQU+N\n0Kzc9FsSwMU7TmVxjzM76GTZN6msYcCrNGHgUHp6bD+Zc6oW1/O78jfTtLov\nrfJ2d2bVH5M8g2gyZ5K6cnsgLWku2gab8snHGMewqKTA8UaMvKcfKol1g5dw\n/r72fW4Jry1XGXA6doEkcvqQYy+FGZAbPv3s+VrqRiqh5ApToYllo/UCJyPi\nm5iqXexUnmDoaH4p4zV72dyuRF4ikdLUvR1w6K/p3yS16xPISILbVkQ4+IYh\nXpvKy9bVLSn6a14Zt3q7zh9ecWIGF/7D/rCagAS66mRURuVq4BOpAbLs1cJ3\nC4Rjj8AecIM1HB+1BXHUyJ2jtRoK7scq3U/DkmGszCp9uxmvma6noRuoRN82\nUHqffES35+f8T3GrVgYedCSZus5UtIjpr4UczpSEB/8MFTjv6xHoT/PAEUiZ\ndMuY\r\n=ePwo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/plugin-syntax-typescript": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/types": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.16.7_1640910196652_0.3685598352658148", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/plugin-transform-typescript", "version": "7.16.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "591ce9b6b83504903fa9dd3652c357c2ba7a1ee0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz", "fileCount": 7, "integrity": "sha512-bHdQ9k7YpBDO2d0NVfkj51DpQcvwIzIusJ7mEUaMlbZq3Kt/U47j24inXZHQ5MDiYpCs+oZiwnXyKedE8+q7AQ==", "signatures": [{"sig": "MEUCIBvyp4LPIETNO2SLAfX7H/xfeEvEwkI8i5CR0+xCIX4jAiEAx/ALoRmSPQqtRT3pRkdQblL2lNA/bw0wfzjDtbqfOLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKiCRA9TVsSAnZWagAANHwP/0N1LcdwyEEm5tumGQ94\npgXrMWTfSwHbRC9LnF3suZGApgmOCJsSvsISZtHHoV0pB8tdvaz2g3L0whcZ\nQXxDyV7jceWw+y4f4dDNskOrqjsUSuuoUt2EUcdJS6cno4+48pM4tu1QAGV2\n5SX+T/JiZg2JXo5Zh2rRRXE1GeQKTP8XvHPsSVpoRSzliR9m3/qKL+eql/zm\nT16+rw/Nb3YSX0WYkcHcOjg6/C9ir/d1sdaWIifmxRF6det1tQDgnV4mF3j7\ntWv0DhZWAQIMjD2jnFY8PntWYqVsxJvOhnaq0/Us8H/jEWvS2fvK6l1Md2Jy\ndS2A/sexcuZ1ZEzOhXET3QwkzFXL0daHkiGYSafN49xsnElAkqyUTaFZp7BG\n6XaNo+MQQ/GIHQz8jWnehNGD16S+w50JXXSc5vxx+zdZy4dbEoPHVrxbPTV+\nw7YqLdIHTp95hqHODe9ikc0CIcdNHyXVPUKVerBLI/95awK3Fe/xOzBqgY1O\nlt0fOGMobkZy2SQFe9jF9Ic9o8peJeHtc9/oV3x5MPEYgXrQrJJKep6WMdMl\n4z3ncT3BraL2khbKlWEapCjM+EYBzD5bnctz8lu4qtpWSz93HGRtfwMcZ7zK\nRF1R917bkSSpUsKv6e78q6hReOni6a0f+QflvXM6dZ7NEiwdwe54WWtyEEI2\npXxT\r\n=tXe5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/plugin-syntax-typescript": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/types": "^7.16.8", "@babel/traverse": "^7.16.8", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.16.8_1641849506364_0.35163891822665994", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-typescript", "version": "7.17.12", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "9654587131bc776ff713218d929fa9a2e98ca16d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.17.12.tgz", "fileCount": 7, "integrity": "sha512-ICbXZqg6hgenjmwciVI/UfqZtExBrZOrS8sLB5mTHGO/j08Io3MmooULBiijWk9JBknjM3CbbtTc/0ZsqLrjXQ==", "signatures": [{"sig": "MEUCIH0EaGM6busBSRyj25RlIfukPjUw8L/fTOHtFU2K1wUWAiEA0CS0z7wfQWJtKxvh+/xAq1hVDQdZd/Y74bIUEs0ODgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGFA/+NBSN7i6yddKV5CBQWPODaj3po6HmoZymXhAGzq7yVxBLgkwA\r\nF0glsPPjul+le1Iz0j6f3WXQkS/fM1IwMMyiOIqblyyQ1CnTuVMfZWF54tLQ\r\nCeb39BdhJgLyOBilTz4jSuElAOkETvNThnJOLxH07lh9agMhEXnybS2LAy2S\r\nGdA7LyJC2IIp0Gpxv3lxwQdg0UuP1bWsxwSu0VdvcQg1+3xhAgQBfDjmW2R9\r\n/tkrWmfdyp2xtcRzSzR49jU6xuHkhrfykFNWIUZdDSYAHavsLkCpDxUL4qHB\r\nN/zucDZtaekYTHC69enpSBoDTknvYvWbUGCqNB3oZ1rOv6KqWKgyu+P+mMlf\r\nRwK4BwLTVMfMB6ZbWQW3gyMylD8jybPRG3yEPkn4Ypk8lLBZ1Ht1pScUMmmw\r\nxDpNfDJzKHKZyKSivjDUqGm7qbHAQiEw3CiSOExdDdcKjkSesIxP+q6pZUnj\r\ntJB/IfanbQwfUDPzRWR9Xu2G1gzzu6btJ9/7B+D7pvViwQ0QQRKjhS2IAH80\r\ngoNK4PCefunFzsXMSFT38+DXK+oJel3lui1jttOhOYKCVhE58Jpo4zVBOPXh\r\nmNItKsYmkyR0oFAfFwxqKql1wGJ9HpfUi5KMqd3NKhcGjY0Dq9QjcJaXw3Kh\r\n5xEAyuIIEFMmc/a3kwIGEgdDalZ8IiVw7LU=\r\n=hD9/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12", "@babel/helper-create-class-features-plugin": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/types": "^7.17.12", "@babel/traverse": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.17.12_1652729592795_0.23189991381609754", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/plugin-transform-typescript", "version": "7.18.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "af4ee9fd68db4ca9c7fe3b8ac4ac4eebcf662a8f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.0.tgz", "fileCount": 7, "integrity": "sha512-x0DUMnDsoRNnaeWlD1JFviIpbTJUNSDRSctEvF/LIeLMsroJ5+Qa11RnAbZX9dEhnhHOJOle4S15o+/xvM7lKQ==", "signatures": [{"sig": "MEUCIQCQoe+Gutw4IsIab0s/eUQWiuQi7ooP4GN+MFC+rZH3vAIgRQe9PkEy5mx+6swqlLtnQFZifX56k4gpJfRSSwPAmhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqatw//Zl7y4HLz1tYZ4i2JKktAPbrRQNCkxj5un5R7VGhroJe84VWE\r\n0tIiuAQdSdkDDqlOYSvB2Muk3VrYH+NjOta0g8VLcPwoETTn0GiqMpx7I7Gk\r\nVuLcHi1VjSMgftfJgivWSleqwwdxR9sbXfJx0nqEvLLW/Bexez0x62ccPM64\r\nX4pt3BnJOJ6EHfhmuTFAztG9OTwwY80iNHIwKZemXeO8VcxdRe0jF+1eTJF3\r\nT3yq6KWEMqON+1G3+gpXDsdWsXAYrwyfwwh/e6nb8zbiAVVP0stZxp/Tvp4m\r\nrRvYTm0XMXbXVIE/kqb8+wsgHbb5et64o4UR7B2maXQkDMLyKNi9qlZvvaZO\r\ndKc6PoOhu97tCgfXyX4vcP/da/Afd/ZGAJ5AD1eUANJ22iwA/fKL+NUuDzfV\r\nPWIoUEvZnsygMg5Yuayqjyrd2nQJGVuOIagpMRDDJkqa6r5oZlawqqS4W16D\r\nzU9zRFqrX7LSdP2F6ChqjTsbQYBjChyCz4a4wZHdIDXE+Y8qROLroDL0CMIH\r\n4boWc+OGMf1TssG5+eAbocfLC8PhcHzaqH81wE60M7mzyHwlidQk8O0WoioB\r\n519cxFLJZVpwgCgO2C1+gpxrd0LqoFgG12eWcE40R5A785LIazq+ckV4X3Ov\r\n7W6pbErEyydxwhsal1fDinmGKHS9nX/bjgg=\r\n=KNjL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12", "@babel/helper-create-class-features-plugin": "^7.18.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/types": "^7.18.0", "@babel/traverse": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.0_1652984197695_0.3565612957282738", "host": "s3://npm-registry-packages"}}, "7.18.1": {"name": "@babel/plugin-transform-typescript", "version": "7.18.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "5fd8b86130bad95c4a24377b41ab989a9ccad22d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.1.tgz", "fileCount": 7, "integrity": "sha512-F+RJmL479HJmC0KeqqwEGZMg1P7kWArLGbAKfEi9yPthJyMNjF+DjxFF/halfQvq1Q9GFM4TUbYDNV8xe4Ctqg==", "signatures": [{"sig": "MEUCIQC3rdplH9TDL25Wc29q9HSYm3YKnpa7JmmEOggKzS4j+wIgUv7i6v5klycdccsCjDbl/k9F4yGttviZ9unLbpcaXB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihqqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8FA//dhQWnx48ez/IdlAHE+mwQIWMsMVTsK4DdzzSmopB2T48TID6\r\nrhxiu9cOk1+1+kqFOHaXTgfMP8Lh1J/kNyx2fbR9TtKP4fFCm7AygUG+CC2W\r\nD/hQLjNWaZ7hwlkrHywZ8dR0e23dSIf7hGezfE1tV3pJKMfAfT426FP09+cE\r\n4gEqcvSW5gpz4tZ3TGz0HzpCUpVeC9eLK2zhwwJbPN3B8kg5Ecl6eQeuc3Qz\r\nYSOWZ/NgoEIB/x3Us4nHR7p+YWh9o1D+4Vxym/zG+vm2zHm0oEdPKaiIQuks\r\ngy2f/HJEKNI+ay8yZimCscwhC1gdJbM8ip+ypuYKW0zhQwdlxrKfAU6BMiqz\r\nUqNVgQltFWuRFKJEJGGkA7szjsitXz9ZbNxTMmORsC/GcvBerQS2GP/PYysN\r\nPA1m0ysK8WyxiaLD4rjpxGZZHvPGVQ+cPZG7M41rQygy38kx0PxddX280vuA\r\nc1cq7y0JmTENdi+8DSh7oNae57kRbHZeJV2umr8vngT61P4o3TQ10LVZtQ+1\r\nH6VGTXhUIL8BY57oQhXhkOdlo8nw8hYQ321zPmE/GzoeoeNxKnUxEm2exAwi\r\nOIDNu1+VD9LIIt4RlOSlvaWa4rdWtTwnxWKr2NqcoMen5K4YGXklc7ZEOFi1\r\nfq5xFS05mCUslR2GMZ/msLnRf3lXyuYfZ9A=\r\n=lIXc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12", "@babel/helper-create-class-features-plugin": "^7.18.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/types": "^7.18.0", "@babel/traverse": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.1_1652992675101_0.06745721189995502", "host": "s3://npm-registry-packages"}}, "7.18.4": {"name": "@babel/plugin-transform-typescript", "version": "7.18.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "587eaf6a39edb8c06215e550dc939faeadd750bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.4.tgz", "fileCount": 7, "integrity": "sha512-l4vHuSLUajptpHNEOUDEGsnpl9pfRLsN1XUoDQDD/YBuXTM+v37SHGS+c6n4jdcZy96QtuUuSvZYMLSSsjH8Mw==", "signatures": [{"sig": "MEQCIAj/Bbq4ap5aNBMTP7X9GZ+8QbDw+MVBFLRQafkOIxHHAiBgWypFNXVAQj8DsCTLmbweBVUnW/UxlM0o8WTmLg12nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqagg/8D95TYOd2cgzhDXFoc+A+Ya+A7EtGZ9lCg7MV8AnxbrFVJynV\r\nO3/hxSmo+NZGdCbrFDmGAyyhk9907rzJrWhswRoh0L++Y0gYMJg2fZkMdO79\r\n2cQxtsodhsDd6fRYLTug+lm3QKaKC8b290kNkfUXePhN8U0tRbL3BLhNB6kT\r\neBNnG1lgzJWdhMZ0qfG8DP4jXBnS6FHT62X3VaG9p7IKHwfxIQdkxmvFKt49\r\nUi89cDB4+h5qvNVA/TzHHk1VAebfJ00Y1d64tKhF6tXCGq0HhFMoxa9icUU2\r\nxklr0GiUZu1ddyXHO+MgE9Ub5XOkkLEyLvmijYSHjUkea4sf31b0KnHSq6yW\r\nltnKCzWEYITZfZ3BxnadzSqUSJAm2B8QzTuqM/uMYp8AA+fTHssRrwQs5+jR\r\nt1kmj4JJMBHnP8Y+EL7lcSNcML0067vshfpC75A4A0fgJhdch8S45o08gGe6\r\n8z4lO9pcTY+YXDFSfeEZ+MzbKy9NVrX6YnXs7p53J0PcnqlDznflFGcQslxV\r\nYZCc9oCSdVAhdusGCuDx6uy5Ylr3BdHvX1ufv4SBgow8ENMH0X0fS5olbC9R\r\n/LdmwDE5qqlXw9Tfg1Dt2nq4U2dPQtBKHDdQcnd8oVdyZPcsGLI8Ux7ChY/I\r\nD/bx0DATasXl2lcZRd8Z3SOEXy1ohTzBPyE=\r\n=6LKg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12", "@babel/helper-create-class-features-plugin": "^7.18.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.2", "@babel/types": "^7.18.4", "@babel/traverse": "^7.18.2", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.4_1653861012362_0.631258982257213", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-typescript", "version": "7.18.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "8f4ade1a9cf253e5cf7c7c20173082c2c08a50a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.6.tgz", "fileCount": 7, "integrity": "sha512-ijHNhzIrLj5lQCnI6aaNVRtGVuUZhOXFLRVFs7lLrkXTHip4FKty5oAuQdk4tywG0/WjXmjTfQCWmuzrvFer1w==", "signatures": [{"sig": "MEUCIQDyn/e4kqbYuVjQgfXFSoW/DAPvcyhHYigwDY56oDXmFwIgTG8DtSYN7P7ALZaaEp99wgcQcXDSB/f2ZuIRsqK2Cd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugocACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSrg//Rf/lYzXqNQCAzoGrP9mRDya/7TMvcl1xhJIFMwft1UZccpbM\r\n5+naYDAWMUE8z2acvNMX+SVVWu+wuKlXSQ2q61jMbobZ76J2B8J/cnlaUDbx\r\nsizKTOZV3l9uRAyPsGIaCYcx4TnnDSyhZ/hpyMC6fduo06ixEcn69yv5qyBy\r\nQIxmF0pgv+LLRyTP/B88jhGt7ocOjldaUa3SMcrer3bLKuwTamA9p2fACea/\r\noUFYH/ds4g9ecPYOcujmdlA8npnydwM8ILy6IrHFWmwmQZIPRwcII/Woy1is\r\nTFbsZnaj92RVuEMGonpX/CPWVRpEm4DrQAYtd3aStRrChDWCqCIhMc1MdP8p\r\ndFmwYBlojO7HBWDOqVcKelDBmhhabi51YZn8dmZpwiYkN+VImqnyucP3k4f0\r\nzQCkJOUT/Tl4pZUVHDUwycbXmGbmJZ1hASobZIHT0MTPrydXVDLu5VZ+2L6p\r\n9SofQl6U+iZDx1A0KZrMf2mE9t+/syZjW3c5uuzwGYG3JVA0kTJ8zJXs0tSe\r\ntxfW2sgjA1O4bXlEkoWFb/m6SMCay0FfjCSTApiAyDdOijmhWkX/NAQM/1dK\r\ns3Uii//6mDV7XT3warzIf3+T1ylp+erCJoi3mk0exgZk9VBh9Yz0bLbwgB8l\r\nFksurcM61+RyjXCVKdqYNFkGz8MAXD8jMk4=\r\n=n1sz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/types": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.6_1656359451974_0.7779778246522508", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/plugin-transform-typescript", "version": "7.18.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "303feb7a920e650f2213ef37b36bbf327e6fa5a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.8.tgz", "fileCount": 7, "integrity": "sha512-p2xM8HI83UObjsZGofMV/EdYjamsDm6MoN3hXPYIT0+gxIoopE+B7rPYKAxfrz9K9PK7JafTTjqYC6qipLExYA==", "signatures": [{"sig": "MEUCIC1KOpG9A/l4Ln2CcRMdTc6u34GFaq7yMsVLYYh6D/Q1AiEAwkMTUuF+azyEJ+cT+00Shx9Ktd4kq1R58tm3dfVOBtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/mzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnMA//TpTCa9xKCalHlYkvNENR0tTSOKXZ1enYc7VMKNwBROns5Qx2\r\nO+/5hdZ8umtKW3lRXTCW4DYYbCsQDcQAh5I/uEyaZyknI/M/tAmVXgwPDVtk\r\ntxHsBKo+/IBm2K6KmLbOsJ2CvGwNoRxdWe8+LJftD1lbZwJOJ+IeBQ3JJ1d4\r\n+EGLl6M4zErmEVgKqpGKHLxZrzqrc5aAsdBmHH8hKAB58A9uq3ovFD808wjN\r\ntprv8N1eJXkQyGgpW1SvAbRYv55MhSYUcG2fRRi2bkc0U2lkGgh8N3sv0tTs\r\n27psHfxSYfTpQv7qaGheBrXnMeFmWGcGwYR26KjLGUiQqMa3k7RJq/Esoizn\r\nHaIH2eYBgx7Bh8lQHhJzU1sf4rVLjusXxvNTYXwaBRJA7G6+k0RmTOFDL32D\r\nEEpaqNUhg+wUuBsOJ1O4b60uRbpTke4OYqe+trSP9o80aUxLuD1AzNaT+vOc\r\nctLqiFP3c883z3xkxeCc+2BuuxATjtAMW5ASDKKlWHJVfjuL9nNoVqBa5OjP\r\nu659OvQJJfkj0d2vuDKXSO49DUZIU4EYuqRpnRu7dVA+h4pYqgJtMu1Cija9\r\n3gsxXjJ/uuGRy81bPQ0Y/vS3dabCA/rMkLZVq6flN6YpNFjjLH3d8wMAPnA8\r\njTOH7c7KBxrkn7M87KrJIlkObJi7ztwSAqE=\r\n=wSMu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/types": "^7.18.8", "@babel/traverse": "^7.18.8", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.8_1657272755181_0.5724061553891795", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/plugin-transform-typescript", "version": "7.18.10", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "b23401b32f1f079396bcaed01667a54ebe4f9f85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.10.tgz", "fileCount": 7, "integrity": "sha512-j2HQCJuMbi88QftIb5zlRu3c7PU+sXNnscqsrjqegoGiCgXR569pEdben9vly5QHKL2ilYkfnSwu64zsZo/VYQ==", "signatures": [{"sig": "MEQCIAlinOqBGB2xhqQhltjqoUFL8OvV/BXGJnxXf7uZ7R1TAiBJ5F1wKeFPRWZZraHRMpzBopXEhy1nrKVlQt8okO55Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBHw//dYv1O84cAmr/6y5p8gW2QOTlU/9yua8wVeXHt2Rusje95Lqo\r\n3vZ+PWyFZGqxjvH1D9yiv8erOcCOZPmvU/GFcDJgEG31OZC6ZelmFxru9V4Z\r\ntcHjJTLkp5VdOKVP47wTwZYMaxGnYRHdYe4QzR6s6lfXY2C7GCWIv0pnGGnn\r\n2fd0SBrNMu43YmVOIc1GdBAgmzFfTBAYY+mLUpKwHt7Q49bwJTvQmrc9pxZN\r\neeZNpon8/ysRVot26/rokOxbpPOX2eFJ57qd298L4bx4ijShrdB/GggHR9L5\r\nTF55mpEHgAMDtAs0tSWgHn7t/sq2La7AWj+eOt1Fxglhfpj/O/jeHnqOlVOe\r\nk4YwfnjQEw4phbhQlFUBKdsBRzxrGxVZWC5og4S042eZmxXP4lGURX6u/Aoc\r\nNQfk1owDbPx7xNXn/WItVNcFOKQqdSDJ6eBATWGe58kv8p3U9rtXvV+U7yXq\r\njVdndJpRkBRFqrTMnQBGxuugdxJ8xLQ4O6PjlpAaoslNa5pFGSD9gQ6uXs0B\r\n74bOTt+A1s7/INaF7ufyRfl02l+/HN1fYfWqKzMahr2Jcgo0weD3l2FvqDYn\r\neZxTt/mn286qpOFyuZoN7FwVTCG15jC68bQYvs4hD7oOcc5zZKxeCH7epYSZ\r\nvgrYv5ZWqxhZ5U0UIrd/NejmTMP11SZno4I=\r\n=/4r4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.10", "@babel/types": "^7.18.10", "@babel/traverse": "^7.18.10", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.10_1659379601914_0.122840853900815", "host": "s3://npm-registry-packages"}}, "7.18.12": {"name": "@babel/plugin-transform-typescript", "version": "7.18.12", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.18.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "712e9a71b9e00fde9f8c0238e0cceee86ab2f8fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.12.tgz", "fileCount": 7, "integrity": "sha512-2vjjam0cum0miPkenUbQswKowuxs/NjMwIKEq0zwegRxXk12C9YOF9STXnaUptITOtOJHKHpzvvWYOjbm6tc0w==", "signatures": [{"sig": "MEYCIQDSp4jcj0zyDegC0lbq0hoSsMiD12Y8lo7zGaP+Dr7k3QIhAKLB+2FwKULknWSwg36C+fdGpQv68ll8cvPSb+0FJWQC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7R5iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDw/7BYIGE1A1QDauUzMokWwTm08mqNBiG+Audt3zghBvHIs1i+DZ\r\nA5jJmwBQkFtiRqtG/yH4rMYbRQNDfX5rTSfQ/zB8HNMfq7Jl1ojJm7rx9Yx0\r\nL9+iJqbYdnBE+1e1fbTZf4L9ZEXdhNIaQqL++JnpbAStAGSil1KzrVp0htxe\r\nvWOLUNfeTPQVdtj6bnAORdv+21zn1zsBPGhA49nEt3Il7KHvGW0Fx82z3xnN\r\nFePNTFPEP86SAHzmOd30hCDiZitxZy9KtF2/LVeCSkJmmJHx4XyoqVG4vsFW\r\nrC+XRSb7LCmbuiXc/qk6o4Oe8vswOl5uZe762XRJsGKi0DDvEQe7LtrByLgt\r\niNGJh2Oorr9T6ixeH7m3jfHpOXCqCZAciMXfssT+lfqwzuu+2jO2eDsBnmZs\r\nUscaruNuTyf+kYziu5Lx7/OYHv659ZIDxE2X7y2NwctOXV2Pn9hGE5WT9EfO\r\n+mAavfMY+MgJBlmVldQ6x1aWHxgN9/eKDQciJj53toNVkPDgOa4xZjnghIiB\r\nwqUoAPr0zhaWB61XG6g67jqDs6QoiAu6MIQa7CML5FYTPvYyrKjlHBXg7RlD\r\n/GgqmpJUy2MK6Qg+T3TFhbkgpqsIWYBueGKX3Mz8DFUS8Aw11M70JmHM46c9\r\nOKLwsV3IXQR1AiG+w32takOae4M0MKqhdEA=\r\n=n+w2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.10", "@babel/types": "^7.18.10", "@babel/traverse": "^7.18.11", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.18.12_1659706978402_0.3319068332893156", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-transform-typescript", "version": "7.19.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "50c3a68ec8efd5e040bde2cd764e8e16bc0cbeaf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.0.tgz", "fileCount": 11, "integrity": "sha512-DOOIywxPpkQHXijXv+s9MDAyZcLp12oYRl3CMWZ6u7TjSoCBq/KqHR/nNFR3+i2xqheZxoF0H2XyL7B6xeSRuA==", "signatures": [{"sig": "MEUCIEhDXStJHz8sup2iW6fbEWaqbZYU930bv+IeLkbmqhXyAiEAmwv6aAz/X+PF8QyTI4mOFX3+bhT8HSsPoryi0ZsVFl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVLw//bvlUcwwOAqkVudKie8POPU4VhEf3WzvQAUQs221rBtfez88d\r\nnue0SdHojZwAXrHNlKOOK3S8rneDu3MGEsolQEUubuX3fKY6MDYBBdJLCPKD\r\noFXa+fiQ+58zm+6Pi7p4PHOE1tg9tE0vDsa+ad6C3SoLZRV2vfE7RmQdLIsf\r\nFKoxegrze8v1S3FgbQfKE5/liXAsdBxn4aS/WqcdQ1gGQC/EPJxfPjG0jgty\r\nlSj3FuKBwuM+us/Mohzday5hz9ptreiHz6D2MT5iqp2Gxxlm3sZKoB8wM5x1\r\nUiEUbNoWlfb7+l16iuOjCE9rnADSMVGnPUIzBsSa3rfSBcy/aJSA2Ez4oiPi\r\noCn4dvdvJatzqizcHW98ySr8aLvrX4kNrnF98oTQOMRdTRYy23/f+kTcAj0G\r\n6jvUiYyrykayrqAdLt1km0IeKWCibmm83UzzCa2qRKsEx0IX1zpAQ57ECJ5r\r\nBy3v2jFCsx78SuHIE8kas61XdR7/skQNfzoOcZqoSDCnRXvxoG4k+otLahi9\r\neTienQoh2hfbW5TMycVXr8XNEGbhDeUZJjynuXGygaD1IHkJ+8BieDh0OD9L\r\npzHRPWcF3Dk9t2GceT+V82f7+2D3E+uW+uDAX+irsPOSOr01SeHTUf8gcvCX\r\nvRDAJGYeQfHkxRR9K3f43ZiDujjuzfUPS28=\r\n=imDS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/types": "^7.19.0", "@babel/traverse": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.19.0_1662404542049_0.0507361616190809", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/plugin-transform-typescript", "version": "7.19.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "adcf180a041dcbd29257ad31b0c65d4de531ce8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.1.tgz", "fileCount": 11, "integrity": "sha512-+ILcOU+6mWLlvCwnL920m2Ow3wWx3Wo8n2t5aROQmV55GZt+hOiLvBaa3DNzRjSEHa1aauRs4/YLmkCfFkhhRQ==", "signatures": [{"sig": "MEYCIQCY0ICt/GpL/YzkY9B2mS34b4O1RKodSN2gqmc+TJwPKQIhAJ60A+tqlDUDZCQtZiKs/nB/vmtKJAshmjTg69eSvgSs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsYhAAhi1VBs4wXXdAk56pxB5o+00XqYYZaW7XfN1GKN+CWzCXotk6\r\nyaPGeUv9TA8B86StTVr3ls99IpEx80lMYiUEPTukd1Rby2kp9DeTQaY99eVG\r\nfyNClovcRVKGLxrQgi3VDJllDB6zHmOD716MAF5sUgML/4kfU+H593zoRc8V\r\nsawGrK4rbeI2Ip+0RVGaZOPfDSVJDGq40L0FuXcaV2xz1k+zm+6Cek7yvVnE\r\nXKvwUWFUtFz9qfg1RK/BIitJSJ6waVup8htviCOAJtjRmRd/rqi06wr+2Ogv\r\nAsmXoaU6dTLTRkFGNil4ksQEgeem+I1yhBCfJgHSRE11gpWJ04syPKcugFYT\r\nmKWkIwN7LGCp6l73hp2zJJLFhNMgCCiZHYlIvioXpQrEgYBj1T8HzshlqGKw\r\nQScHsWb/I7L25PspYssc2y/BqL8Pvs5X4+kAQNbtxiw0ejksRmh2fYNU+ds9\r\npl+aJO+qtHMF87izYxZxzzdlMt/SfI0WGboUVHa2fY4hCth+BXCE9Vg46ljV\r\noExX+R5sWltMbwP383xIEhTgo795R+YiLAmltqcVdFBdituVcil/048v9ecU\r\nc5m0DY0BCDoCil6hOUQrr+zZfXBaFO5AlKgsnyDi7Cckqce5cVA2gPSyHxO/\r\nThM1tsdv1YChnkjwgZV/VbQtL5nqgPZ05ZY=\r\n=quKX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.1", "@babel/types": "^7.19.0", "@babel/traverse": "^7.19.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.19.1_1663169351578_0.5008348114480827", "host": "s3://npm-registry-packages"}}, "7.19.3": {"name": "@babel/plugin-transform-typescript", "version": "7.19.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.19.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "4f1db1e0fe278b42ddbc19ec2f6cd2f8262e35d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.3.tgz", "fileCount": 11, "integrity": "sha512-z6fnuK9ve9u/0X0rRvI9MY0xg+DOUaABDYOe+/SQTxtlptaBB/V9JIUxJn6xp3lMBeb9qe8xSFmHU35oZDXD+w==", "signatures": [{"sig": "MEUCIQDVLfKFout+XLxZBItRu/ruEw/pNn1Gxk3j4V/VsYXPUwIgUZmTS/Nc4Y2EpDpi4tJO7vdqwmjsdivtBkDrTPL3cog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0K9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9ng/+KGbPCNuMMdc/46njytoGBWXtT0+igtys0qpnPpW7/JnsHiE2\r\n1N1gAMOU2lJWBmtxp3Lvpq2y+FKIzBMA4l6mwzaBDV0bkhXcNF2Qyd0J+RPB\r\nm10EIlIm+GTF0ffTKzmO2KnGboNXzPGG27LxuitOnIK+ISze8myi47wkiqJ9\r\npLgvycCDxTXetH18pox8LER8Fvi8edpQmcnujVb813NsSKzKjw1tuOtOtaHQ\r\nA73bTlT1BbvTry6U59Mn5sKncm+M61QEgxbIB+mI6qjJ0DtyCGC5ajGbAuGX\r\nsZzBLKQysAt59LcGyS1GKGQrrMrMJgy79OvREQgJKslVQGyjhICfZPVRxHIS\r\n+elEVdzeTfL2XEepvcH5HQJcoPfHiGLjf4g9SHMIjQTP+Hz7jylkp+Zcm8aO\r\n5AE/rfpfKFue2DcEVso+JR8bZD3JzNVq/rQAaOAMNqjsrlmn5XupxQzq1qq1\r\niIyi8gENi8S3FccQ0OsaZMgzMR84CHUQvIVbx4KY1kcUoJOVWIxyC5jEC5AJ\r\nQk2pkCtjB9P7KnzA91+qxC5r8Vi3OpS1z5NEB4ze0Fg7Wfam5g2SAfFOnCzj\r\nKFZVXc6mJQVL+PCmwY7QTuFxZHix4qT3P/ojnDf81hG7ZtLhVUzvBbZS47pA\r\nT1k0Y5X5daVfyVcCb+fYveW482+3BvNNn+c=\r\n=2/MK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.3", "@babel/types": "^7.19.3", "@babel/traverse": "^7.19.3", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.19.3_1664303805119_0.8105025575037652", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/plugin-transform-typescript", "version": "7.20.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "2c7ec62b8bfc21482f3748789ba294a46a375169", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.0.tgz", "fileCount": 11, "integrity": "sha512-xOAsAFaun3t9hCwZ13Qe7gq423UgMZ6zAgmLxeGGapFqlT/X3L5qT2btjiVLlFn7gWtMaVyceS5VxGAuKbgizw==", "signatures": [{"sig": "MEUCICIs8/INMGo9mQXG/SYYV3WkfrNyGzt/U+o1ZGLPOZlOAiEA3qVL1Z96ZkJA1CqyuASBra2OWry98wBnOPzBWvuNBuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113109, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoL7Q/9HAxHwSdbDAZXwjGw6h4jbkf/xWunjfbEgwq3fP3OzPlFmb3D\r\njoMUhTiWVngB//5OEW+6dhX+VVXboODjyFNdImF4frLKqsy8Vbzp02gT6eUP\r\n0ZxGcTbSXJ81XJQ2IVtr6A/khEfr27c6K6aFvdzmH//JcpH09Hh5b0FAsfcb\r\ndSEQtVNX/a575OIg+eoS343e2RdSJWv5T4S1KeMOUO6+4dKu9P8ropV/USQC\r\nL041C5DrxcVjqBQ3UIaXh9WFsssL+ZRsbTjXblxLpgDXUI4GV6CPu9ZCR/RV\r\nIkXBp24Lzn6HCGQwdPCxbuIows+VA4HlWKQ+MPAnVrsM+Gx/jJVCvT5LCZRE\r\nOsDbMohv0DOPg6P09doDimwcL0PCoG6F9m3YzF4m2KmuZ3wB9Y6rGsPyF+WR\r\nZb4M342K+jXkhCwywSHJywl7nf6pDTl+BOGZ6uCz1W2ozpHrKbXdAfigo80/\r\nATSwaH1FgitTLN13y+jdx3oNyNk4XwITQ1HGeCxBha6rleDOkL3YkbKDLCR3\r\nRwT4MBDIGE6wzqVehMQu/TfJ5JeAMf+esyC3zQXpiMfhts5EaKp1x/UmmNr0\r\nrE3y5+CzmcfvcDykzdPWo15/KSy8ZRwUzTQHkbYvPMgd2nHxN8CiGsWRLky2\r\nX75G7qc8tkfxXtSS20hROCiS4c2lMCFiWTQ=\r\n=BVzW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.20.0", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@babel/types": "^7.20.0", "@babel/traverse": "^7.20.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.20.0_1666876759592_0.36088295565643946", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/plugin-transform-typescript", "version": "7.20.2", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "91515527b376fc122ba83b13d70b01af8fe98f3f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.2.tgz", "fileCount": 11, "integrity": "sha512-jvS+ngBfrnTUBfOQq8NfGnSbF9BrqlR6hjJ2yVxMkmO5nL/cdifNbI30EfjRlN4g5wYWNnMPyj5Sa6R1pbLeag==", "signatures": [{"sig": "MEYCIQCQ84bDItDePfe/IqluZsPo1G+0JHG4vXnMTHKtdi6jawIhAJdwWY//gZzxC8+jCyKIXTY/hBc1QcGltr8hmQ1a1oPP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrN0hAAgptIKhrh/fxToVTFPsQFVhNVG8dNSuIXXrBE4ah9gZJAFChK\r\nLGWHX8KCR1LubTnLdWx3O5RCkfNwNPFwiPJW3WS3qSjRj+e30PpAp+Ud5Usl\r\ntuJvZZw8e4wzzbmJKy0IErlNpV4C+vXyebJKMxx9Gy+kfUmRBVkPeMjRptio\r\nYcAQZnV8JdZ1UvkO+fd/td5cQZxc3F851KKExLgOZ6wrhwoNfb8fxkq9lHnr\r\ntxI7o+IrJ4+grI9CQng0TSMmacc/2dSF4y1YFAzwUUIdWrad0KneNSn3w++j\r\nPvJTwY5MC6AUrRSNx7BerHED12b7GXdysiUtuSSCAxpKyfojIr+m7zC5si8t\r\nfAoelAWt78zo3JII9+lpcqlnWWXfHP+zjJZkxBaRUrHk++RbybkKxbsn+iF/\r\n9/uOGRnyHaBXeGgFV7lud5GAjFaqAaqCBtwYEPjA2ROdyBRnaf8ONMLdOOVa\r\nAdWMbzb/gjcfKQvy/yFw1jbpzzZ6kZg4AEALR9vzHIaNTVxgo+zQBaLeERC5\r\nXVE0V+un5pic0KcBv6nhGrTj0PVvENhsDyMlWBN7a1zgZVk89V98iGsdbhXF\r\n4jBn1f45mDe6paDA2anncvis0Cul3BtEpvs+WhH4lkqqoN/1GTjKqTjgO8PO\r\nZLasu2HXMqAeYEriu3G+tdWlA1gp957t2tY=\r\n=9u3z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0", "@babel/helper-create-class-features-plugin": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.2", "@babel/types": "^7.20.2", "@babel/traverse": "^7.20.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.20.2_1667587868678_0.1906536256000595", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-typescript", "version": "7.20.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "673f49499cd810ae32a1ea5f3f8fab370987e055", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.7.tgz", "fileCount": 11, "integrity": "sha512-m3wVKEvf6SoszD8pu4NZz3PvfKRCMgk6D6d0Qi9hNnlM5M6CFS92EgF4EiHVLKbU0r/r7ty1hg7NPZwE7WRbYw==", "signatures": [{"sig": "MEYCIQCpV3ycqV8UnpzxW3Si/H45DHENuRnW8lZRfW6PcnlelgIhAMGast/1jD3H1JLneT+H1FjJnnidDs7y05ThdqxlE2SR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovew//YD3YORs96ouLkXP5Je4PppCY7M8ZWpYH1OTsKv8BF5QRgVGN\r\ndHT10p7lr1j4Kd4TK4Hpu4I7GhYh/9/YLyxOaeo7PWYqn1vWXDULc7rm319Y\r\nFw+ahNYNamqTxRfTx5JhxmCemVzDKRIypnkUDLR0R4XgG2F74wRyXJsXuUBd\r\nWUVl3qGBPzdpaJdKHGUvgGRsiZPjfz3ACOAX1Lc9ZHnkWRoqvqEeavuNt2aW\r\nctK04GVjmW+nbng6sfXdNN3wha3KWHgHGV8rTAbe4jD/IvS0/fcWPd5uT0Zw\r\nWbaRCt70gxKnDSRnpIbl+raLhJdS6IkBcD81yiHCbcsY3qLb0EKy5Gvw3yBz\r\nYgsrdd2qC7RBfiQ70TnC4Jw4ZH7h5ID9kP6dnqvN9GylUuKcvqJlVV0rXFfG\r\nGz0umY8YgEJgtZ5+7mtE3j5n+yhGC/NsOMZeCEu6sb/G1XndOP9JYqe3e7eV\r\n4xMpZwQFnKtJIKbn7o+YsjOYzvwQ0x0WOc/Bggc/DK8sgtL5/Hcbfv3XZn6o\r\nBHdFMk7akUU5im2XJA8BaZL+rJN3voKdoowZDEkAoPXW31pUFsG2g0E9zDT6\r\nM49gRKHLDkzpVVL2TypURURdNaJCykhV0nZEwC49Tq2mrIrTlX5gefa8cHBi\r\nt6pP0lCjJnEXaQiHUF81+2foIA1IPB5gU9M=\r\n=cnD/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0", "@babel/helper-create-class-features-plugin": "^7.20.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/types": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.20.7_1671702343498_0.4275623737428471", "host": "s3://npm-registry-packages"}}, "7.20.13": {"name": "@babel/plugin-transform-typescript", "version": "7.20.13", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.20.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "e3581b356b8694f6ff450211fe6774eaff8d25ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.13.tgz", "fileCount": 11, "integrity": "sha512-O7I/THxarGcDZxkgWKMUrk7NK1/WbHAg3Xx86gqS6x9MTrNL6AwIluuZ96ms4xeDe6AVx6rjHbWHP7x26EPQBA==", "signatures": [{"sig": "MEQCIFUn+SqGVa++3mn6dU4SMVg2DFaCV8Zso0mS4gKTMX20AiBthnuGt6ZW+i8JwyFse1LX6MWtXrFNtzyOSfvGI+7q3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy/cQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7nQ/+L8pCzkn7frAUID8VQxe4nzhV7PJ80sBL6jnWKz3DZB/FKc6e\r\nX1AZKK1J68RIcIrR2l3H6nbngze4QzXKkcTEAa0X7Ty2pUOmuRyySoPuPi/M\r\n9ot9zeD1U043S8QTWABsQmnMoHDlR9GMub20ijamcRfsM2Kv09b85zeebOwd\r\ncv0l3dyVTn7UYOYmfcRTNdqlwdUjdIiShDfIiZ4IwWTWSR5HLWC0ebliZ2bg\r\n1OwnN+ZBl83pi5xwd9crHM8ngBwUTFPQHzx1Jx8AYGVSg6g7lI/bjoRDplpb\r\nifftIUP51bCBtUsDMMumVZQnNfnV78hoW7EwRRl/lpxVTWFRiaV9zsPWb8jw\r\nCtIB0rpEKbBHHCz+Y9zPXi8cMQ8h3il9O/ocWL55TLBCOIHqhJEsjwGZzwYe\r\npBopyaz0X+D1uDYnHSH3G5WjqSCNPPcm1yEFi6HsG1cuxi1578R9bnorPVdO\r\nBCMbBjpOWfeQuTwfJyP7o1RSdu+F3+iZJloD+cfGdbBZTw8xFjn33nRgBnHZ\r\nteLy7Sh/MCfBjuhD4heohOKEhaS9Z1LuHyKLtOh+9dOIpy9Cxw0tCIYMI4Is\r\ngnzP4N5IKqGMHRLAaizYLpPGiSLmIND8lsao0SOo7Z+77Ctko90FGiyvZwe4\r\n//S4QcbJc463uqzsj1Kx+CkoV1/iaPeb8ak=\r\n=QeBf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0", "@babel/helper-create-class-features-plugin": "^7.20.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.12", "@babel/types": "^7.20.7", "@babel/traverse": "^7.20.13", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.20.13_1674311440174_0.5510030747915879", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/plugin-transform-typescript", "version": "7.21.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "f0956a153679e3b377ae5b7f0143427151e4c848", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.0.tgz", "fileCount": 11, "integrity": "sha512-xo///XTPp3mDzTtrqXoBlK9eiAYW3wv9JXglcn/u1bi60RW11dEUxIgA8cbnDhutS1zacjMRmAwxE0gMklLnZg==", "signatures": [{"sig": "MEQCIEzQOBbE6TXxYMktzgmJn5/BsZLKOpm3Xl29IfwJz8F6AiBmZHF5AueJPrd14o4l8y07C93vYKCe+5nyaU2Jl4htlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohCxAAi/2EEeV2AT0KOXcs2TOl3p7r8mxWquOi617BUJHv70W9+qYG\r\nFVYIcs0MR97ShZhhP+FmW6b+zsiR/5yeqx/7ldBSyMcUVvwgIEzkJe2Ug1h1\r\nF5BKYWjNEh8tv0T+l+q0J8RBjQGVVSCeeBMuwpk8wQDlYBfkoUgo4PBOrtbk\r\nhlA8EPUwsyCd5AhrpNIQaW5+HBbv8wV3q2ld/q0yXsbNw2NF43b+MuStmTb9\r\nGkDFPZLejlfCUvhq6jc8oCdM+IcQJ53X8ZT/X0WWqePlnLenZcfJ4dqxn6Us\r\nZ9qyGaQ2xPVC3g8W5BmW8PBT04HhrzIpa4BUgfFHjcKLbbZrqCj7MLtAkiko\r\n+s7Hh867eewzVrKlUdUr0agqo9OBDPxv5h+3sYXWRlUykV+nkJ807wHS5Rcb\r\n/TTk6wWILgzz1W8dD7zuY3lyH+1ORzLl8gBKe1KxtYUDU6PfMss09+eqymza\r\nqRCpS+x3EnBOCbnszdEeMgdZnJ2hKIomrDg4KY/BgO0FmFCgCOQldjtHVmKf\r\nMrQnNYZyrpO03Dt546DMiZGA0OyoXr3Je9t8+e4KnyNBrYRzP0hCe2bVe5hs\r\n6IaI93W3Jtjdb6NU0W0tHRdFRET4L7HWhgbNBczGURJzkwI1+O7lx0LCHuZt\r\nA0cP6sTX4o85G0qEE8aIXP2J+sHaaMuY7as=\r\n=Jf97\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0", "@babel/helper-create-class-features-plugin": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/types": "^7.21.0", "@babel/traverse": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.0_1676907080549_0.9313094524411993", "host": "s3://npm-registry-packages"}}, "7.21.3": {"name": "@babel/plugin-transform-typescript", "version": "7.21.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "316c5be579856ea890a57ebc5116c5d064658f2b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.3.tgz", "fileCount": 11, "integrity": "sha512-RQxPz6Iqt8T0uw/WsJNReuBpWpBqs/n7mNo18sKLoTbMp+UrEekhH+pKSVC7gWz+DNjo9gryfV8YzCiT45RgMw==", "signatures": [{"sig": "MEQCIDp2G5BYTTrWpQVYaSFdMCUQaU5eO9mIfZNRGbbGVn8fAiBLPcM+xvKGGflkiQj3ssQMHq2VW2pevppetEepdFEfng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIvXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq55g//f/da2QJu7AavNFLK1JztF9rcXEuoW5IN0s9vsfMQSg4ndNwv\r\nySdlBoZA2xV/oswiy0KpKOQ9aQ/dLP0ykEp8gSuDzwRwvBrhzViqeCns8UX6\r\nXR9fzlHb1xitsDFZWqO6xyCLfyRViDtOTqoIGS4PonjIKS2OGMtcg9bP0Vh3\r\nMbxh9eX+EALdWX1gn75E0I7X1QMNx1Y4atAi+gx4JW2JbX0WZsBB2c+KIgbB\r\n8mHezscU2Dww3byJaeTRUiEhpEqzTDvlm4ZhVlRAwLx3COWiWm8ST/L2NQz5\r\nIcRs5f0WnUH3nnT/LPR9JxMR9b5aYF/WB+BTjNeAO0McxHsXEc/GAKBJpvDV\r\ns9pcHDtobFAyITCezVW6+gcEK9R9iLtfPsWuM6q0wMW8PDcFNPVaWdp7oCj6\r\nZN+lzoVON6kOBSBtnJTmpfSWlXWCqdcekhYVLXmwsHeA0FKcjV7MyVF1GyrJ\r\ngsmF6APP4F0Y++XuhnyNU0JqnV6xbZOk4Bf38Ct1s9BVgkoorhgHsEDpn4JA\r\nvvm0YgUziFj8h3KaVmCYshS1YPplw2U8JuYrn73kPdznz4n38sPKs5y/z26e\r\nFD2EAP3obBe9oDWQ3mRrjGWQ0OJ07SDlYzM0Z/JJIBVEOFlQ6X6MOGZsXOdm\r\nGeN40ncx3ZJ+YPzuyBt08586K8CATjExb+s=\r\n=W42o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.20.0", "@babel/helper-create-class-features-plugin": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.3", "@babel/types": "^7.21.3", "@babel/traverse": "^7.21.3", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.3_1678805975148_0.919250608078888", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "f4184330f666978dad1be7f3d341ce19cf7cf3ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.tgz", "fileCount": 12, "integrity": "sha512-dqZP5ygdMzWFQwPdCpir6i6Q0IrfSNAJEL5zG4w1xAciLIaK0DiqpxBBIY87iCO5Pj2IOfQghw+RzX05vtgnIQ==", "signatures": [{"sig": "MEQCIAYEdEfZnJtTObi0ppfW5lTTkin8GZkQLD35Rh6r4TB1AiAI6qawrSnanEp3xFTkWzfruEfi/2GNnCs8uCWALTyF3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfdw/+MBZxGmC0QaKVV6rvmhIbuHcVrbDEy3IOGWnjqt1ye8yh8D6o\r\ngI3gY87fBfaIQIBjSzyHmh5P9Zf4PSIOMmEWulFIinnIErqsTPcv5kYuueNS\r\nkM3Do7VKVpbHPsR18+wxTCuGfxRGkh89wcpDtlvfGoyOSfG6CF+qPEQQtz+P\r\nzhwzhd/Ce664sFR2ChifUCN00q8hmQhk5LEnGBU624JQwu4MHksM0Wz/Ma6p\r\nVq89R0xxe6sdI338DSm53zyCL0bxTayS0/FaNO3K98EVzQvqH1MiqgmAofSC\r\nRzMrrWCTZcxFTZeQeaDAQrPPtamTUCh5pwqt0BND+O5aufvyJaGIfKwyCt6H\r\ngxl/qTvE3xVkuEdz8EuSiuAsDuD0jam1MLIovYZNSYPg+BoHWp+fDyAtQeVM\r\ngpys9dCJ0b4wtto4qXNIJ5Vghf0CJBe+sR8vilaUUPqhDu8OYNuqflGztOlc\r\nJBzVgvM9oB31Fl7P4PeRldEzlWLliiWqJnmx9FQ2egnTv80IfJ7OF2xpEeUB\r\nbAYcljDOYOecSIQeI+4lx3xCJTQJY8/2cSMm+N/b4HOIa36AzahtZupFVyfI\r\nBPQ7qTq+T4UTYmEvGFKUKAgSA05MYCw/AdVNjTJVqBYFToSMxXRPJVT2HlvF\r\nk+gtgDnNbTiLHzqTN7L86CpJ/fmxMC3EWrM=\r\n=hlEK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-annotate-as-pure": "^7.21.4-esm", "@babel/plugin-syntax-typescript": "^7.21.4-esm", "@babel/helper-create-class-features-plugin": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/types": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.4-esm_1680617403024_0.843202779672577", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "d9cea427be531883bf6ca96028cf5c3aafb91c40", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.1.tgz", "fileCount": 12, "integrity": "sha512-NIpg8j5LbiC8em+R+03nBZ638R5HRfTAFHvZmw40CAZYwKeCfQkokLI7Uux/XZNAnsol3Mmau2B0Tlwr+BqxzQ==", "signatures": [{"sig": "MEQCIAueMLW89dGy6eTIZpn+08vqQs6zlk36fZRYWNw1Wo2UAiB7BZWPvcjit8xCjh4hKloKlYayXxVVgAJu+SGgvT3F1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFVA//eTwL8zrPj/qsKUQmJgLbvMT00t3wPmuj7zdQ7i1qPyGF+PjN\r\naFyhB5mWMKJgKx1lUnI4FnNbjeDgZXNLuUTEaGkz7YdkRpuDkVzfmMJLORQa\r\n4yjA6YfE5lhxKpdKMqOoqY33kCax2oCzLRcXj0S1ZtHSz3ZA41N8+IljXcnP\r\nqzSk8nAflAnv0eeXzKr1Vib2fo5CE5pY6WXUwfW/B16BdMK7ApauP2lCQtBw\r\nVB02gmVmf4Nby2wrCHPTNYyfimibE3Ent3UqF3Ow2colHlMV023f6yml9t5U\r\nJmpahkrfpXw4F6bdEB9psPqONeCD2h7+CBCUjbkcApHQkg57cWZYr0Xt24pU\r\ngaWzhQFWNcwPaSHyznePOTUN0cQ3Sg7i1c3YPs1qk4G1brPBIdIlRJc+YpiM\r\nC5LTxpovkWTFBD87VvRXMa8gsAOrpx85UCURbaRi0UL0Vi8RB4EWTszMjoo+\r\n/EWgFSfAUGhIKOPOKyP2LKJ3dRMgjGe/o5vwkniG1sTYVRfdtrvRIJzOSme9\r\nxq8tEMpuvhWfB5q83tzQ+0zrZYkGml2eVQ1eEh+lqLD0dF8ZoEADOFuYU0Mh\r\nKynvNauaiGzx4K+gQiso+/GEQDkKcYEaEQd/dCRlCQuVsaIwnhWtLjF5BaG/\r\nO7yD6101h6qLqWlqGP7gaYtThxP4esP9wFc=\r\n=BO0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1", "@babel/plugin-syntax-typescript": "^7.21.4-esm.1", "@babel/helper-create-class-features-plugin": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/types": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.4-esm.1_1680618120717_0.31704455566706913", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.2", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "561337e0388ef1e639ddacd2151e73ba98a821bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.2.tgz", "fileCount": 11, "integrity": "sha512-o66Vr80JO2l8NFcCP9RVWWxhdi98HMB1CBv+HxONcr4dFNUt9mWyV0iWM3fhWupPax85dqk6oGp7+WVZzF9hkA==", "signatures": [{"sig": "MEQCIC1Etx3PNqT6TjDAthAdrLF3g87gW37VjRO9gr3CnK9BAiAwn9dM5nDd9+CiOvHWhbpPuxTES0+/QPok7ADszjGoog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTPA/+PaKn5+kknDgFhL16hFiP/fYUiF+iO4V6ptM6rx8rpGSWfcH9\r\n8T339hbv/FSj4Blx/kCqbVPAktkxb3FwMFpIHvFzW1oKtMa5EMCknOhG27Ew\r\nllr3YHQ4DNiS6h5EEZCiOkFSp21g40iIQBx9AQpN1phXT/kEArzJMuTrVwmo\r\nkF2liCnqNuaGxEDoHUsPEBehnp0fj3Myms8G6rCWdTwZYvjOlKdeQQx+sl/g\r\nNFuEmAaw7vQrNvZr5PmVAkOdc9vebKA3jxcHIFqc+Ebz23OgeIh6tnMYBK6t\r\nVrVRIxdJM115N8bKCuk8oFJUutIGzTtEJoDSsBr9R9hm14qd70UiSYkHd1Xw\r\ncCqfF+6tUQtTyAaxTOFYbZ4QyP4/m4M2O9HQ+uOgUAUKTpI64TNitEsRqRN8\r\nAsMU0UVYM0IuvpZ/e6O94sMbztzvHQ7jyJiRjRS2bCCsZSwyUu3LFVM5PSTA\r\nbscf5l/AOb+AKxj9K0NhlLGU8kxD4F7R/OfUe+np27Eep3QpsYWT1iX2qfUI\r\nbFiLQvjJyxPy4W4k/CHnSnFNFCf+wifG6rwe5JUKJPjDVOsSC516XVkSsQGV\r\nrrhW6VLw2Vi4PWtdNsApVXStdUjh6Lgd+iQ5hu9cn5s/G0oLeusriPeSbvtc\r\nJTB0jy5nuKMFeDaljCHP7YvsHWrg6rqjN24=\r\n=e2UX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-annotate-as-pure": "7.21.4-esm.2", "@babel/plugin-syntax-typescript": "7.21.4-esm.2", "@babel/helper-create-class-features-plugin": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/types": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.4-esm.2_1680619206251_0.6226019400760361", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "d514dc5f9028e88419623f4d1f10301bd2216f88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.3.tgz", "fileCount": 11, "integrity": "sha512-hVIQMKd/hJ7gcT6HD9oWgFu57DH8+fNFK8zSJ9bCw+PhAoNNbmsYuLppPsHGNx5hOCoyoth954tbISqFUg+I3w==", "signatures": [{"sig": "MEUCICEBtBxCxbLTDngSSXHOdNPAHVrBjZXgHgztE4HMlwr1AiEA23smu2975+ZLSPIjf5rgw2pxF1kLgu58jKPR9aXtZaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXqQ//eCkvWYbe6w9vl0Ki9RX9DQ9lR609pZWEq1nnUUbS7L/xypE0\r\nm5Sv0F6wEqCVU14Og40159DrlITzP6qTpLemR77gtKEqeDGtuOex1VkyyVZF\r\nRM0TZt977wYLuTDdwN0tEsZa1C9M60osLBiIXr1/1lylRw97nbHHSR/cNKnL\r\n5R5AKxvfQUX2MxsE3kpAuynF8TBAhBiagFxG+fe7zwKb7inX1ga3z4QIMXgm\r\nc311Qhwc/vDh35EE/P3BQEpXJJNxN1grTyj9Exk3B/PhZP6E0Ea0MNKpB83b\r\nCrveMP4ojC8ITeOaBBvSoxO9bf2KWoFk2v4H4SusmAE2NSkNxbQx6xr5lYOS\r\ndQdWAP2iwsdQwP+H470r1fhs3jh9eAEucBitsovB7VDdmmtDPrFklUJ7AUYp\r\n//WRq0lXdaDXG91i58Gp0+/s8lFlIPrabLD3eLVu6soLIr8Nh0tl/r9fovKS\r\niepuunIiWfD/qeQhaV86LANeZL08Z9zBYwxxUtbiXx5KVXxa2IGfyVTItx+u\r\n7a3w2Vjc6HQFxYqx5T0WoQ9g8DQXIsWPNpvfnDLprz3owah+DY3p2uzFqqZB\r\nehLPiavEk3pch3/WJJMfEK0vyqJ369fGXYo6hCn2jwVVG+Hh+8prHcE+MRR6\r\n7Ef7yyuC0YZVIKs5kq+KUWNRDUSaVduI7rA=\r\n=aTj/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-annotate-as-pure": "7.21.4-esm.3", "@babel/plugin-syntax-typescript": "7.21.4-esm.3", "@babel/helper-create-class-features-plugin": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/types": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.4-esm.3_1680620207824_0.10618473637738335", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "e060203317e26c703947df3bedb30fa94f882870", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.4.tgz", "fileCount": 12, "integrity": "sha512-WL7QXRgL3I/E4WuXPoegxt2kdyyPg0zIGFVw5TKcRStgduj3XmzQZKUl5izHjP0JFAMPzgHk+xkY6otUg5QAsQ==", "signatures": [{"sig": "MEYCIQDEErc8toVzrVoeVFHXbASgfkLeJnO+krBmnKgVWIjuRgIhALejU0QGqxjjrUivut0eiGT4wXkB55uXfLo21PZWoyfy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD60ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDpQ//V6MpEIMK7I8S0A3k4idOGNNB+jKJo5q1hWruDb2SeRIuiA8/\r\nAx7n9GNKNu64kt/fzZMBjPg2xUfRuWzk9OMS/0hJWIvlCIWuhDWrw1unYxuI\r\n1x2qYttZ00z14Z5EU0HeqwAYa8Nx8pZv7PLP66T+bVssfX/CbKO5K7X0lnta\r\nEH8a2Q+e1As9Zu6X7Tm4ZWdrolqrzEX42TjvOKvL2oNsITwh49xs6Cc8/0Ql\r\ndm3D/KRsg071TYw2E5vuqxSzlWWycGW2qZ9gpa2bZmqPPtQKEPHs01v9tDRw\r\ngwxWi7bjFeg7i+0rNGjBrAJvHjsMVHv2AjL6/gqPoInP+ATbtGiAPtFKXMTE\r\nRQkb8WweCrcvg+8DLgdmP5hvCreMGd+UCTbhp3JLiXwHWC5GTTTI2vBAx45S\r\nKwOp827iW5cv4Fa+0Vyt/rZOG5ku90yCA86PIv9AmzBCRgw4qMZKjUQLVQwy\r\nvg/C78onng3n9EYi/lLX4+Q1GSweWEPTCJpocYZbrMoRS2m3+AV7A90wMEwW\r\njxvhPqLo/Ik8e7dEjDSAxyqwcZVg3PC07/JDbCncTjGgfu0qenSNZEsK+WTq\r\njTnlKt7WFWPo3gsTVYHm+U1d2DECQhNLpsaiLa+XqvPSoMpyzeR3j+vGQCKX\r\nlFSMZI3EI2N5no54N8Xfgh4+o35lSlP9FmA=\r\n=NuHf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-annotate-as-pure": "7.21.4-esm.4", "@babel/plugin-syntax-typescript": "7.21.4-esm.4", "@babel/helper-create-class-features-plugin": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/types": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.21.4-esm.4_1680621235973_0.8017598241974426", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-transform-typescript", "version": "7.22.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "d545f6d491e639078d9629f300f9363fe74e7a10", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.0.tgz", "fileCount": 12, "integrity": "sha512-gb4e3dCt39wymMSfvR+6S7roQ+OBBeBXVgCpttb+FZC5GPGJ5DkqncRupirCD36nnNt7gwNLaV3Gf+iHgt/CMQ==", "signatures": [{"sig": "MEUCIBou48BkZu97vv27Yq2CAon8Sw1zPYcSieLf8E4rUl+uAiEAzX7a68qECwi/NlmyxiRZYxjoOnPIOFX2LWTvCmti2ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125906}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.21.4", "@babel/helper-create-class-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/types": "^7.22.0", "@babel/traverse": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.0_1685108750510_0.7211225822786902", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-typescript", "version": "7.22.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "8f662cec8ba88c873f1c7663c0c94e3f68592f09", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.3.tgz", "fileCount": 11, "integrity": "sha512-pyjnCIniO5PNaEuGxT28h0HbMru3qCVrMqVgVOz/krComdIrY9W6FCLBq9NWHY8HDGaUlan+UhmZElDENIfCcw==", "signatures": [{"sig": "MEUCIFWuH82ED/FPuW2+0uTpNyj/mr9e1czq/SBTSw4dYbrLAiEAj8NE80N2h4+ZnOCF/eObsb7EGbAGAbc9H+ZSaY25s7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126155}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.21.4", "@babel/helper-create-class-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/types": "^7.22.3", "@babel/traverse": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.3_1685182260861_0.28243337475352415", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-typescript", "version": "7.22.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "5c0f7adfc1b5f38c4dbc8f79b1f0f8074134bd7d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.5.tgz", "fileCount": 11, "integrity": "sha512-SMubA9S7Cb5sGSFFUlqxyClTA9zWJ8qGQrppNUm05LtFuN1ELRFNndkix4zUJrC9F+YivWwa1dHMSyo0e0N9dA==", "signatures": [{"sig": "MEUCIEQV0XonJJGbtvj655lQjpb1GM1zk265eovDqo6q+4jwAiEA7a8e6ArUp5jAFJsF5WhEjV/aAfNGzu/935d3zzWMoyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126155}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/types": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.5_1686248510250_0.8348550128748353", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/plugin-transform-typescript", "version": "7.22.9", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "91e08ad1eb1028ecc62662a842e93ecfbf3c7234", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.9.tgz", "fileCount": 11, "integrity": "sha512-BnVR1CpKiuD0iobHPaM1iLvcwPYN2uVFAqoLVSpEDKWuOikoCv5HbKLxclhKYUXlWkX86DoZGtqI4XhbOsyrMg==", "signatures": [{"sig": "MEQCIAuYBbleMW4oTi74V2mXNGAz4antxybXXQ0YOQcuN60FAiBf5gmljTbeVHjUgtOAZzofvqODGZuTfIhnUwsAsYC1ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126461}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9", "@babel/types": "^7.22.5", "@babel/traverse": "^7.22.8", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.9_1689180839905_0.9853650117297896", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "45bab58ce166fb94d2e89cc5858a6dfd4b92a7b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.0.tgz", "fileCount": 11, "integrity": "sha512-MoAcsHFWGW9lAziHTLhDWydjpba30+4canTa4kWTwla8pIxD5AHZac/+UEHiU7n9aye5uBgRxgIxEoZc6kl+1A==", "signatures": [{"sig": "MEYCIQDalopG4ygbZ0Gu0F6FJwvdNNzyXOLIclQKdS9UZTD+0AIhAOVQIJU6qFtb1eaeX6UhxHQqSgSQApL8qaVDlMe8Wf1M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189181}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.0", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/types": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.0_1689861633156_0.580786281124829", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "33cd486d4eeaf0ce0a4d3ac85723139f355440fa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.1.tgz", "fileCount": 11, "integrity": "sha512-fCV+eG3OIxOOUWn0MGwKnPaEeAwoascjBtII4jKOMq6hFm86CkaZqmeSCAju/AgyuiqhabuoXwJANK1gZ20CTg==", "signatures": [{"sig": "MEYCIQChu9aKm4GjZde3OxCPqmhpCfXjgPMenSawmQ4o1c/0zgIhAJZvfFWhodK50H5o5sC4ijwPIToTGnbDiwc0VEYNce/R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191231}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.1", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/types": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.1_1690221185932_0.9489391741783839", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-transform-typescript", "version": "7.22.10", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "aadd98fab871f0bb5717bcc24c31aaaa455af923", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.10.tgz", "fileCount": 11, "integrity": "sha512-7++c8I/ymsDo4QQBAgbraXLzIM6jmfao11KgIBEYZRReWzNWH9NtNgJcyrZiXsOPh523FQm6LfpLyy/U5fn46A==", "signatures": [{"sig": "MEYCIQC9oPwsBE16ftvR6fetpqnFq9tBht3pqn1g6FMLrdmymgIhAPExKiBI5MqZxAq+6aGbdef/3e+zQEJxMIxBOZAddG8J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130034}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "@babel/types": "^7.22.10", "@babel/traverse": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.10_1691429121925_0.8488380383663334", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "81e0438a2e97bc05d4f12818cc5f312b5fe76eeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-jg1KMBs6HaeBpTV+psU7M7WGGyD/7bGO6kXSbaogrFZx2nFsCVEaXhtj5GvGFnGgDPae1tgkecQOexLyQ2AV6w==", "signatures": [{"sig": "MEUCIQCCuDjoZOs7CFZ5HVhV4f8fEeEaVvXaxu/7u89B9DuBrAIgaKVxi+p5aZoJfA9mtn4JMvqbHTlxtt8Ef5OXdvR/95s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191231}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.2", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/types": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.2_1691594128002_0.7275784016801976", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-typescript", "version": "7.22.11", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "9f27fb5e51585729374bb767ab6a6d9005a23329", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.11.tgz", "fileCount": 11, "integrity": "sha512-0E4/L+7gfvHub7wsbTv03oRtD69X31LByy44fGmFzbZScpupFByMcgCJ0VbBTkzyjSJKuRoGN8tcijOWKTmqOA==", "signatures": [{"sig": "MEYCIQDEtRaDDa81GksEzAE+Rj4jIwAf/AToaicd4hzQHh6naQIhALOmpBi51YAk4PDV5lbRQVU8RoAAy8P0dM8dFvI0s2Ql", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130273}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/types": "^7.22.11", "@babel/traverse": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.11_1692882527377_0.3517122347717412", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-typescript", "version": "7.22.15", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "15adef906451d86349eb4b8764865c960eb54127", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.15.tgz", "fileCount": 11, "integrity": "sha512-1uirS0TnijxvQLnlv5wQBwOX3E1wCFX7ITv+9pBV2wKEk4K+M5tqDaoNXnTH8tjEIYHLO98MwiTWO04Ggz4XuA==", "signatures": [{"sig": "MEQCICNPsHoH7BaAAlgzRw1Mcf65dv9A2721cIGJjfUf29M3AiBquP7QjntoWIb5Ik4Hyn8xjlDi5sloZtjKXaA35mB00w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130300}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/types": "^7.22.15", "@babel/traverse": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.22.15_1693830323745_0.5848341439998186", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "cd65edb2ba633fe6f6915f7fa106c7952f9016e5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-2TBJnzMCjosnYJK8ufOanfqj64d/OD0Tp5+xhKbrgZ165ak5m3KDPCEK5N+OzptoiBhki8ynS39glOBb4xXuDQ==", "signatures": [{"sig": "MEQCIAF5fjiF9EQhxBp17tCkLimE6ADTsTCyJ1oOEN9OnXpyAiArm4XpMoLotyUY1Fqb3gTYhSFE+6z0kK5+N9RJDz8mHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130511}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.3", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/types": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.3_1695740260553_0.14293112087236803", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "461da146f68ec2833e2c44b353a8e05437159bde", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Ebkzhhu4n+5Ls7GCEX6DLMADpg77EuTEHGcSnws5WKUmbZ3udbtVpr7czOkbuCUtnMFs7gVVgXlnjJ4aXUSfzA==", "signatures": [{"sig": "MEUCIQCXnV+qvRVjRBzJYNbM7E+wQAIbyNbrB2292PbTxkK0ngIgAv8xYJpdBsP1T1EBRpX17vaC1GvPyLAImHwRC6gWwHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130511}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.4", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/types": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.4_1697076414134_0.401574193420005", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-typescript", "version": "7.23.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "ce806e6cb485d468c48c4f717696719678ab0138", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.3.tgz", "fileCount": 13, "integrity": "sha512-ogV0yWnq38CFwH20l2Afz0dfKuZBx9o/Y2Rmh5vuSS0YD1hswgEgTfyTzuSrT2q9btmHRSqYoSfwFUVaC1M1Jw==", "signatures": [{"sig": "MEYCIQC0YfisP/uxmjl9Uez7agFLsQKgkvC5cTkEOHhO5pocVgIhANaVEgEbkUIzqY/BnA8dBo5wJViL0eiBvQZZ2jTicomG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132066}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.23.3_1699513454270_0.20939220500418365", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-typescript", "version": "7.23.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "da12914d17b3c4b307f32c5fd91fbfdf17d56f86", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.4.tgz", "fileCount": 13, "integrity": "sha512-39hCCOl+YUAyMOu6B9SmUTiHUU0t/CxJNUmY3qRdJujbqi+lrQcL11ysYUsAvFWPBdhihrv1z0oRG84Yr3dODQ==", "signatures": [{"sig": "MEYCIQCaNk0U41q8K+XAme/LoGhktNlKbRUCkns+4ismth7G+AIhAIQXnDUIFgWHT79dNZ7Uql87+aMsq/eWCVuKsVhYg9Z4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132072}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.4", "@babel/traverse": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.23.4_1700490129826_0.6459056858685859", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/plugin-transform-typescript", "version": "7.23.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "83da13ef62a1ebddf2872487527094b31c9adb84", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.5.tgz", "fileCount": 13, "integrity": "sha512-2fMkXEJkrmwgu2Bsv1Saxgj30IXZdJ+84lQcKKI7sm719oXs0BBw2ZENKdJdR1PjWndgLCEBNXJOri0fk7RYQA==", "signatures": [{"sig": "MEYCIQDn0vJp3ubAKzN/DN72FmrbkEIkixSC4RN/eXruAMsYagIhAPy8TYQ9Vb0CLs1Wd6dFBSXsLA06zWIj94Dres6f+BOi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132189}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3", "@babel/helper-create-class-features-plugin": "^7.23.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.5", "@babel/types": "^7.23.5", "@babel/traverse": "^7.23.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.23.5_1701253544870_0.4261027431044735", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/plugin-transform-typescript", "version": "7.23.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "aa36a94e5da8d94339ae3a4e22d40ed287feb34c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.6.tgz", "fileCount": 13, "integrity": "sha512-6cBG5mBvUu4VUD04OHKnYzbuHNP8huDsD3EDqqpIpsswTDoqHCjLoHb6+QgsV1WsT2nipRqCPgxD3LXnEO7XfA==", "signatures": [{"sig": "MEYCIQCSnoo3Qpyy29EPqPvdKc5ufCMLr8TwT0zTpcYBUyR2cAIhAOhItuYNqAWV2ZBITnwrKPQD9v6pCDZJQdkXizx3TM1C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132370}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3", "@babel/helper-create-class-features-plugin": "^7.23.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.6", "@babel/types": "^7.23.6", "@babel/traverse": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.23.6_1702300202735_0.7714935728110235", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "8d7baf5b733437242f9a163d9e488ba02efef8e4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-qtq9rG8COdWEdL0XEh5zeu6XCpotCskIi9wwK3Ozj29xjIHJdF5n/2NL8Blzfrf/Q+hjtRarwUlI6zRxJp04Ug==", "signatures": [{"sig": "MEQCIFdRzDycNspwHANrY6l8ByERFpyQGG6P2ncsQ8PF0P8eAiAICyy0XzGYk9yoxG0zT3Vwuq5zPlu7mEOzGXvd8yu7Fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131590}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/types": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.5_1702307988515_0.9212563174683863", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "208d58341b1124793fb2acbd41490b092f1c8dcd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-WqqOOu8i+Q4qxS+8iNk2ShO3mA1yBRVMRshgvI69D/Ny6DIIPPzZ6oLIIVkYYuO1LCXgFDRQm0T89o2XVDxtrQ==", "signatures": [{"sig": "MEYCIQCj3eaHuTWkIybd6tYp6KkR3+z5Do9SiGB7Zctbsf07OAIhALdkz7VEWb8811lS10dHCvZfuA1Dq9/A64gd+NKidyzs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131590}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.6", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/types": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.6_1706285693768_0.08197997016126646", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "5f202d316594d3b6cbc7ca764ad8cc659b1a8648", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-TEBL5hN+mSgbBjLtuGBy+CfBHMNimEjugApjTHavH/jCjkhnN2sDKEUAMCqf+7kL9XpMalYoKILJvMGYvbISsA==", "signatures": [{"sig": "MEUCIQCHQrXytyrPLRZAUngMRQA56F/h8pQByoqnLzIp223vrAIgZJQV/yWDXCbXcIKKpIuzE8jMLWW59MgZjDAICxLTJYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131590}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.7", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/types": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.7_1709129151542_0.07715475555776408", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-typescript", "version": "7.24.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "5c05e28bb76c7dfe7d6c5bed9951324fd2d3ab07", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.1.tgz", "fileCount": 13, "integrity": "sha512-liYSESjX2fZ7JyBFkYG78nfvHlMKE6IpNdTVnxmlYUR+j5ZLsitFbaAE+eJSK2zPPkNWNw4mXL51rQ8WrvdK0w==", "signatures": [{"sig": "MEQCIF+8RdhgyuqOt2bSvN5FlBJ1eeUjroncwLt78u2RgLPiAiBLgIfxIeg1/y2q3LonP+sWge0UWVc2kSvbQmHCzqt0GQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132365}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.24.1", "@babel/helper-create-class-features-plugin": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/types": "^7.24.0", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.24.1_1710841775905_0.07680753358386161", "host": "s3://npm-registry-packages"}}, "7.24.4": {"name": "@babel/plugin-transform-typescript", "version": "7.24.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "03e0492537a4b953e491f53f2bc88245574ebd15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.4.tgz", "fileCount": 13, "integrity": "sha512-79t3CQ8+oBGk/80SQ8MN3Bs3obf83zJ0YZjDmDaEZN8MqhMI760apl5z6a20kFeMXBwJX99VpKT8CKxEBp5H1g==", "signatures": [{"sig": "MEUCIQCZXVFPUn5ZaTnEPy+In6BhdEI/eH28TPdgrcwOvo0TwAIgUF9eEYAbHqY8JdcIsFXdr5Xpi48rov98UhwwucFx+qA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132433}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.24.1", "@babel/helper-create-class-features-plugin": "^7.24.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.4", "@babel/types": "^7.24.0", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.24.4_1712163232847_0.17228938739969668", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "45f5baaa8292c424c386ce7e9a60e98c032d24f6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-RZgXwkjnIRwjyv9pHni2fLZMYUdzKsA/fjO+aghlkptjrNK2vzRZzdw+cSGpbsyJYJ7D4zPCTPECdiCaVGfAsA==", "signatures": [{"sig": "MEUCIHX4nvCjT0vCAQm6bhQ63OmTba2O7Kf+27eDtayimG0QAiEAoXiobYWS9J0bDN7nVdYdjozLceBHiyjBW2qeHUg+OOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131574}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.8", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/types": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.8_1712236823996_0.12910280868639035", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-typescript", "version": "7.24.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "bcba979e462120dc06a75bd34c473a04781931b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.5.tgz", "fileCount": 15, "integrity": "sha512-E0VWu/hk83BIFUWnsKZ4D81KXjN5L3MobvevOHErASk9IPwKHOkTgvqzvNo1yP/ePJWqqK2SpUR5z+KQbl6NVw==", "signatures": [{"sig": "MEQCIAJyCHLQV3lY5YsaoEiilArwzG6nsHzY8XRaUpuRsYhdAiBoLo8VFmUwjcwriSn0lrHFNcZZjn3vp/Ktwrh3kfCI8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200605}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.24.1", "@babel/helper-create-class-features-plugin": "^7.24.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/types": "^7.24.5", "@babel/traverse": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.24.5_1714415668201_0.9884793440295327", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-typescript", "version": "7.24.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "339c6127a783c32e28a5b591e6c666f899b57db0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.6.tgz", "fileCount": 15, "integrity": "sha512-H0i+hDLmaYYSt6KU9cZE0gb3Cbssa/oxWis7PX4ofQzbvsfix9Lbh8SRk7LCPDlLWJHUiFeHU0qRRpF/4Zv7mQ==", "signatures": [{"sig": "MEUCIEMvJlZZH88XGpnIBYAHw3t5aXMvu/ARTso04RhHppnCAiEAutp926GQ3ZybGTC9N8dYNpKCcRCt/p0Qvj/GgfY/CCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200772}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6", "@babel/plugin-syntax-typescript": "^7.24.6", "@babel/helper-create-class-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/types": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.24.6_1716553514484_0.5679498360779247", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "4c529508800986c2759e0cc4886f0b4d997edbee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-ZpPjVrivSNk0CZD1frBgfmTHfU9cRmHuv9v0LS2416h2xyv6RO7Td7zbBe3SEH8/30fGQoJnu1eGz7RfH7u3Gw==", "signatures": [{"sig": "MEQCIDAZDhJmKxn2gIbQbnYOnUsI+D2bQXxsE5L1+XVoAoF1AiBgt3IHl04ahJjqD0L1h2hYLpmhwPd4UbuGPFhzYemiBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200456}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.9", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/types": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.9_1717423551648_0.7228346344046792", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "7859014f19e17d04d4978890d4a7d53aafab0211", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-6cf600GEGvv7JqquB4N+pf3b3hsNs+RpF/Sf0PBBXOXHCF/Xm+QARNovo3C0wqXzFn21M3fiNq0Zk8LZ2RChdQ==", "signatures": [{"sig": "MEUCIQDqk3yv7tuBuePWGIHSH4bXwM9pJh4pBLAbHDpM1jcZ2wIgdfcQbjKa8LSTGbnZGA9v09Uc5yHtB4+E590WfSUOTuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200468}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.10", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/types": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.10_1717500048051_0.024362385748515125", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-typescript", "version": "7.24.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "b006b3e0094bf0813d505e0c5485679eeaf4a881", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.7.tgz", "fileCount": 15, "integrity": "sha512-iLD3UNkgx2n/HrjBesVbYX6j0yqn/sJktvbtKKgcaLIQ4bTTQ8obAypc1VpyHPD2y4Phh9zHOaAt8e/L14wCpw==", "signatures": [{"sig": "MEYCIQDBf6LISm2Hjb7+vEF1SJTB1JIUTE461NpFleBkyahHKQIhAJFY1ny8lSbgeNIrR8jdcP1FRBkOvSAl99tks0Be9aMR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200584}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/types": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.24.7_1717593360120_0.47607866102723784", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "710fbcf83dc5f524def2f85285de26973f7a15ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-3UCIMvVdGkC4DNQ9JG0aoE7YmhYIsiFBhTGcRXg6otC55zwk3wKFleweCAefHRaAl5c8RCy++rSlgwJniyaZ5Q==", "signatures": [{"sig": "MEYCIQDhVQyUmo5UCJK/Y3REPDuJbCmPlL6IuXPflrlKpQ7/EAIhAIU+Amy55TIQ9zmCFeVTO92cA6MD7tJS76gDAMSJpeDl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200359}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.11", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/types": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.11_1717751770750_0.7251078451616764", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/plugin-transform-typescript", "version": "7.24.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "c104d6286e04bf7e44b8cba1b686d41bad57eb84", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.8.tgz", "fileCount": 15, "integrity": "sha512-CgFgtN61BbdOGCP4fLaAMOPkzWUh6yQZNMr5YSt8uz2cZSSiQONCQFWqsE4NeVfOIhqDOlS9CR3WD91FzMeB2Q==", "signatures": [{"sig": "MEUCIEGMjSr4VZyeyqWAXqv4mU6x93CasHMkaZvnCDDMJAk8AiEAr0nSyRi4pX6vNaQqnVKNd4IEPaUuvfqQszA8or5g+Pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197197}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.8", "@babel/types": "^7.24.8", "@babel/traverse": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.24.8_1720709698911_0.29801601411368517", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-transform-typescript", "version": "7.25.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "56f47fb87b86a97caa9c7770920a1967d40ac86e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.0.tgz", "fileCount": 15, "integrity": "sha512-LZicxFzHIw+Sa3pzgMgSz6gdpsdkfiMObHUzhSIrwKF0+/rP/nuR49u79pSS+zIFJ1FeGeqQD2Dq4QGFbOVvSw==", "signatures": [{"sig": "MEYCIQCOfdS/sk65VH/cD+IUdk9SeOIYradZCRMvt9uJWfPMVAIhANPLBW/2VcPKnTXYtal5P09HYP5CZ79m45MajyJuLEwe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199729}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.25.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/types": "^7.25.0", "@babel/traverse": "^7.25.0", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.25.0_1722013174146_0.8318536586939653", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "02fc4a5a08c52b3ed5365c284efbcd011edc82c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-tv3Q0cCHzJuh4LaSqsjLNCqY733RvCehfCIxq1bqPxybb9bJR2mA60BOKwayYMZPEoBxMzVvqTDtPoZgiSmfmw==", "signatures": [{"sig": "MEUCIQD4u+k049pxzggD36XRcaVHw0QcUOeECawump4UqeAMlAIgYrqCgB1dnwc3SMVPvC/Nx/q/A7V7BkXhxnn6GpFvA2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199563}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.12", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.12", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/types": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.12_1722015246406_0.032663125037052376", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/plugin-transform-typescript", "version": "7.25.2", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "237c5d10de6d493be31637c6b9fa30b6c5461add", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.2.tgz", "fileCount": 15, "integrity": "sha512-lBwRvjSmqiMYe/pS0+1gggjJleUJi7NzjvQ1Fkqtt69hBa/0t1YuW/MLQMAPixfwaQOHUXsd6jeU3Z+vdGv3+A==", "signatures": [{"sig": "MEYCIQDjy6gVH9Svdb6ElYxmzNjoB8oNYhiKIG420tGBNiTV1wIhAIXinrXB2/u+Hsxboh5LWA+GKE6FtAaVj4K1kpTgKKNX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199727}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.25.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/types": "^7.25.2", "@babel/traverse": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.25.2_1722308089424_0.7322936410790839", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-typescript", "version": "7.25.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "8fc7c3d28ddd36bce45b9b48594129d0e560cfbe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.7.tgz", "fileCount": 15, "integrity": "sha512-VKlgy2vBzj8AmEzunocMun2fF06bsSWV+FvVXohtL6FGve/+L217qhHxRTVGHEDO/YR8IANcjzgJsd04J8ge5Q==", "signatures": [{"sig": "MEUCIEBtzG7z7YHM94w0GyOwjnTIln4krthjw0NwM4xeXVrDAiEAqr4wXqDVAA6dwddLOIsmIowszNCyFYY3s2RI9L1avcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207736}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7", "@babel/plugin-syntax-typescript": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/types": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.25.7_1727882136925_0.7056501145118295", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-typescript", "version": "7.25.9", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "69267905c2b33c2ac6d8fe765e9dc2ddc9df3849", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.9.tgz", "fileCount": 13, "integrity": "sha512-7PbZQZP50tzv2KGGnhh82GSyMB01yKY9scIjf1a+GfZCtInOWqUH5+1EBU4t9fyR5Oykkkc9vFTs4OHrhHXljQ==", "signatures": [{"sig": "MEUCIERgJV+++VJKGxMg6jrIWd8JNYrIrsmOAWE6GRCMvdmoAiEAgeg2FoQJr4Bfuul+WDY4/ElvafqpxGhmggg1Tl5x+4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134864}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/types": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.25.9_1729610512123_0.5890910267114629", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "7dafbb449f931e8b3851f5a74c43b89359827200", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-ImJsxfU9x3peo0IwDo11mL0QP5kSF7M6D82JSF0VccXQZj+BF3qXvNdYpp7Yx5plv8oPIVyUH4dx7olHAyd2Kw==", "signatures": [{"sig": "MEUCIQCJANAZyX3UVURt6deg2cHflvJTYDZN1pBkvvMEbh/bsAIgTXonyx7b+fA+TE5/uLUiQ3OQMhh20vj07tIHXpLmRLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134698}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.13", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.13", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/types": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.13_1729864492572_0.9267034249354438", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/plugin-transform-typescript", "version": "7.26.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "3d6add9c78735623317387ee26d5ada540eee3fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.3.tgz", "fileCount": 13, "integrity": "sha512-6+5hpdr6mETwSKjmJUdYw0EIkATiQhnELWlE3kJFBwSg/BGIVwVaVbX+gOXBCdc7Ln1RXZxyWGecIXhUfnl7oA==", "signatures": [{"sig": "MEQCIDRhSNguiHmmZdHaZ9+B+occCJc8HKVdlg8Gqf7AYeyCAiBPwqcvmjHnAT9r6ltbemxF7BBENJds8/y38Aj+FxJIJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136629}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/types": "^7.26.3", "@babel/traverse": "^7.26.3", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.26.3_1733315735827_0.132748689530636", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "d342f19550a45e1f59fab81768f40472d566a84c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-nwhE0sG+N2RxtvmUtHLTUUsatMA7TdeGsZFWrvq3QtqHG6RxUnJRLt0dnczH+FdnO8FHltbd2BNlBx4sX+NzSA==", "signatures": [{"sig": "MEYCIQCxZiMOPM2YhYTW2NH7n0q8ayAeJYRoxDkaVr2Pgz7EHQIhAPck8EBXnxh5xzimTF3L+sLxZWKo7s+KWp14ZGAR4nDP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137464}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.14", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.14", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/types": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.14_1733504081521_0.6213235640725094", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/plugin-transform-typescript", "version": "7.26.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "6d9b48e8ee40a45a3ed12ebc013449fdf261714c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.5.tgz", "fileCount": 13, "integrity": "sha512-GJhPO0y8SD5EYVCy2Zr+9dSZcEgaSmq5BLR0Oc25TOEhC+ba49vUAGZFjy8v79z9E1mdldq4x9d1xgh4L1d5dQ==", "signatures": [{"sig": "MEQCIDcHy7zytDwdI+AQHl6bOURVqP2XXAKRds1p8LTEbmBRAiB9nlF4dd1GlRGKd6wYTlYP6bCNYCyxpS2/k2AC2vBnTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140231}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/types": "^7.26.5", "@babel/traverse": "^7.26.5", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.26.5_1736529112073_0.9371759047813097", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "4ea10af74686c4afac8cab4651a3937f06465279", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-pVxcxHsrS2r89L6cQIx67R3YLWVuAdPIUm5tkb1eAcz+mnCji1hRG4HNlscj5RHC6lvc0p24fePxJPhmJ7GurA==", "signatures": [{"sig": "MEUCIQDD8ETz2XWvZThlKl3yBfs5exobkNy7mi/YLaRG8FiwNgIgJqbPsSJVhI4RdVMUlIkw3lwVnQ7mQLzCBgDu5tnKCmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.15", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.15", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/types": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.15_1736529911576_0.5591697268554547", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.7": {"name": "@babel/plugin-transform-typescript", "version": "7.26.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.26.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "64339515ea3eff610160f62499c3ef437d0ac83d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.7.tgz", "fileCount": 13, "integrity": "sha512-5cJurntg+AT+cgelGP9Bt788DKiAw9gIMSMU2NJrLAilnj0m8WZWUNZPSLOmadYsujHutpgElO+50foX+ib/Wg==", "signatures": [{"sig": "MEUCIQCUufLJnKvMiEVk2VbeCyTZFzwvVc0r+sBICZYTEEpkoQIgeToF7Cts44t5WJDX5HyM8Uhu/PFdk3U8/IVpAtawJ8s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 142860}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.7", "@babel/types": "^7.26.7", "@babel/traverse": "^7.26.7", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.26.7_1737731093027_0.21922213676848679", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.8": {"name": "@babel/plugin-transform-typescript", "version": "7.26.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.26.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "2e9caa870aa102f50d7125240d9dbf91334b0950", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.8.tgz", "fileCount": 13, "integrity": "sha512-bME5J9AC8ChwA7aEPJ6zym3w7aObZULHhbNLU0bKUhKsAkylkzUdq+0kdymh9rzi8nlNFl2bmldFBCKNJBUpuw==", "signatures": [{"sig": "MEUCIHz3mYHwp2QF4T5j67PnKBoMCnZoImPpU+/Jj+dT3s8sAiEA4PKOhrmnpbg3TUEQF1TCQupoDX2AobVjZmCZiEd0yIM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143367}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.8", "@babel/types": "^7.26.8", "@babel/traverse": "^7.26.8", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.26.8_1739008763122_0.8949434278865906", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "d36401d343940ed144d2828b87f7d31508e856a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-hQt6oM5egh5RePZoIDp0hX3JnQxORgTY5ACq5P5f/39Sbpe7rNkDc/oJcReYVPWAZTgIs7/LC1F1GKbIeLY7FA==", "signatures": [{"sig": "MEYCIQCmIpSc7tAkmgOKACiifNi7ZSrjAD8I87ayrtpYivKaLgIhANRWez1rG6BRXIc/fnGqMkURuINIih8xS60oYahZXJ+S", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143893}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.16", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.16", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/types": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.16_1739534385646_0.6762372440747901", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "4182bd183e42bd53f9b2e37dcb231a10d88384e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-r1ZPPswYyHUwR4xfdYG6AETL8J7D4JgfmfPJvvr2t5d08HToTBDtzmtkz/JIj5BLLQo5Edg5xQ3GlxklKlikhg==", "signatures": [{"sig": "MEUCIQCVGIvrRVDlTtg9vEhW6Zn/RXbDAuelEr8uCPIqlqZA4QIgMY7Euw0TsmZDMG+iC3HwLw8NhjdbvSdYzWJ6BIqE3ng=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143893}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.17", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.17", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/types": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.17_1741717539937_0.5891665193056053", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/plugin-transform-typescript", "version": "7.27.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "a29fd3481da85601c7e34091296e9746d2cccba8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.0.tgz", "fileCount": 13, "integrity": "sha512-fRGGjO2UEGPjvEcyAZXRXAS8AfdaQoq7HnxAbJoAoW10B9xOKesmmndJv+Sym2a+9FHWZ9KbyyLCe9s0Sn5jtg==", "signatures": [{"sig": "MEYCIQDjtg5C+VKi20tN2nlFPV4AQFI7N+3jIRwV5IW5aiOJJQIhAPgEO/Jwz5fdSNNCHMK/UgNoYdti0NSfT2cDcrGSf+KI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143556}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.27.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.10", "@babel/types": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.27.0_1742838115192_0.357916109206343", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-typescript", "version": "7.27.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "d3bb65598bece03f773111e88cc4e8e5070f1140", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz", "fileCount": 13, "integrity": "sha512-Q5sT5+O4QUebHdbwKedFBEwRLb02zJ7r4A5Gg2hUoLuU3FjdMcyqcywqUrLCaDsFCxzokf7u9kuy7qz51YUuAg==", "signatures": [{"sig": "MEYCIQCUWt4r53fgx4L8+q607yCp3tcPANpoQxSHbVl5lSHPOgIhAP4RliOP6qZvVTtIXNxBU7qdD/+c47FBKjKvlIO/yZbd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143560}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/types": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.27.1_1746025774981_0.8056683398621258", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "96e13424dbc53fffde6f9d8daab36af78a8f7711", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-cojpbrnQ6LYbcpFR+NdZ+VK1Gq+ObK6OHFY2NeSdbfAo59jBoVAQizRxwtF7W7GvOhr8BEOV8rMfW+XG71ku+w==", "signatures": [{"sig": "MEQCIHeI/ywlaMe51a/G0+kE+rhiV6wJKOvSpYcziarA3F0SAiBMiixOLigeylDfHCCcU8KbMvmXzCYWzYulM2LO6mObtQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143927}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0", "@babel/plugin-syntax-typescript": "^8.0.0-beta.0", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/types": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_8.0.0-beta.0_1748620311634_0.34941208418960157", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-transform-typescript", "version": "7.28.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "796cbd249ab56c18168b49e3e1d341b72af04a6b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.28.0.tgz", "fileCount": 13, "integrity": "sha512-4AEiDEBPIZvLQaWlc9liCavE0xRM0dNca41WtBeM3jgFptfUOSG9z0uteLhq6+3rq+WB6jIvUwKDTpXEHPJ2Vg==", "signatures": [{"sig": "MEUCIEqrNQiXvdcF+/puRfGHL6czqHxWkLz4Px83mQVyo4WMAiEA/PRt9m6lvqL5s+DhfFVfP7oztDohHEdCwjrmJ5kyy3w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 143630}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.3", "@babel/plugin-syntax-typescript": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.28.0", "@babel/types": "^7.28.0", "@babel/traverse": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.28.0_1751445497734_0.14653200312635262", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-beta.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.1", "@babel/plugin-syntax-typescript": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-beta.1", "dist": {"shasum": "a638e38bb50623a2eebfcc09dfc8c2e3623dee24", "integrity": "sha512-E3ctyFhsPxhf+V2pzufJO344pWPsKRsuJ312EqC9CRVueJ81RzChjoltrd0WSCde/8E5iv71/8MPVRzO0odKIw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 143997, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCg0MUuqeqOVPvjucte8riMrIVgTKFPk9qMVnkGScSXLAIhAL38M5Fw3EjlTZDlJcKCf2C/7IVsgsU/3yRtsnHDxGnj"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_8.0.0-beta.1_1751447093247_0.5616408860006987"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:30.306Z", "modified": "2025-07-02T09:04:53.793Z", "7.0.0-beta.4": "2017-10-30T18:35:30.306Z", "7.0.0-beta.5": "2017-10-30T20:57:07.413Z", "7.0.0-beta.31": "2017-11-03T20:03:59.445Z", "7.0.0-beta.32": "2017-11-12T13:33:45.286Z", "7.0.0-beta.33": "2017-12-01T14:28:53.914Z", "7.0.0-beta.34": "2017-12-02T14:39:54.004Z", "7.0.0-beta.35": "2017-12-14T21:48:11.817Z", "7.0.0-beta.36": "2017-12-25T19:05:16.511Z", "7.0.0-beta.37": "2018-01-08T16:02:56.332Z", "7.0.0-beta.38": "2018-01-17T16:32:23.320Z", "7.0.0-beta.39": "2018-01-30T20:27:53.105Z", "7.0.0-beta.40": "2018-02-12T16:42:13.659Z", "7.0.0-beta.41": "2018-03-14T16:26:36.105Z", "7.0.0-beta.42": "2018-03-15T20:51:49.833Z", "7.0.0-beta.43": "2018-04-02T16:48:44.774Z", "7.0.0-beta.44": "2018-04-02T22:20:25.269Z", "7.0.0-beta.45": "2018-04-23T01:57:56.667Z", "7.0.0-beta.46": "2018-04-23T04:32:13.197Z", "7.0.0-beta.47": "2018-05-15T00:10:10.771Z", "7.0.0-beta.48": "2018-05-24T19:24:08.186Z", "7.0.0-beta.49": "2018-05-25T16:03:38.142Z", "7.0.0-beta.50": "2018-06-12T19:47:45.967Z", "7.0.0-beta.51": "2018-06-12T21:20:22.598Z", "7.0.0-beta.52": "2018-07-06T00:59:39.061Z", "7.0.0-beta.53": "2018-07-11T13:40:38.254Z", "7.0.0-beta.54": "2018-07-16T18:00:20.482Z", "7.0.0-beta.55": "2018-07-28T22:07:43.817Z", "7.0.0-beta.56": "2018-08-04T01:07:37.766Z", "7.0.0-rc.0": "2018-08-09T15:59:24.204Z", "7.0.0-rc.1": "2018-08-09T20:09:11.305Z", "7.0.0-rc.2": "2018-08-21T19:25:18.598Z", "7.0.0-rc.3": "2018-08-24T18:09:00.724Z", "7.0.0-rc.4": "2018-08-27T16:45:30.934Z", "7.0.0": "2018-08-27T21:44:16.920Z", "7.1.0": "2018-09-17T19:30:17.648Z", "7.2.0": "2018-12-03T19:02:52.250Z", "7.3.2": "2019-02-04T22:23:05.433Z", "7.4.0": "2019-03-19T20:44:36.676Z", "7.4.4": "2019-04-26T21:04:08.795Z", "7.4.5": "2019-05-21T17:45:40.469Z", "7.5.0": "2019-07-04T12:58:09.334Z", "7.5.1": "2019-07-06T08:12:48.299Z", "7.5.2": "2019-07-08T19:51:15.212Z", "7.5.5": "2019-07-17T21:21:58.087Z", "7.6.0": "2019-09-06T17:33:49.675Z", "7.6.3": "2019-10-08T19:49:31.565Z", "7.7.0": "2019-11-05T10:54:08.626Z", "7.7.2": "2019-11-06T23:27:22.779Z", "7.7.4": "2019-11-22T23:33:58.850Z", "7.8.0": "2020-01-12T00:17:37.051Z", "7.8.3": "2020-01-13T21:42:38.827Z", "7.8.7": "2020-03-05T01:56:01.232Z", "7.9.0": "2020-03-20T15:39:29.961Z", "7.9.4": "2020-03-24T08:31:18.114Z", "7.9.6": "2020-04-29T18:38:31.792Z", "7.10.0": "2020-05-26T21:43:52.886Z", "7.10.1": "2020-05-27T22:08:34.374Z", "7.10.3": "2020-06-19T20:54:45.727Z", "7.10.4": "2020-06-30T13:13:35.610Z", "7.10.5": "2020-07-14T18:18:22.105Z", "7.11.0": "2020-07-30T21:28:16.000Z", "7.12.0": "2020-10-14T20:03:32.057Z", "7.12.1": "2020-10-15T22:42:12.732Z", "7.12.13": "2021-02-03T01:12:19.686Z", "7.12.16": "2021-02-11T22:47:14.000Z", "7.12.17": "2021-02-18T15:13:37.859Z", "7.13.0": "2021-02-22T22:50:34.596Z", "7.14.3": "2021-05-17T20:44:40.571Z", "7.14.4": "2021-05-28T17:00:09.592Z", "7.14.5": "2021-06-09T23:13:24.561Z", "7.14.6": "2021-06-14T21:57:05.622Z", "7.15.0": "2021-08-04T21:13:15.341Z", "7.15.4": "2021-09-02T21:40:04.061Z", "7.15.8": "2021-10-06T20:54:56.854Z", "7.16.0": "2021-10-29T23:48:01.765Z", "7.16.1": "2021-10-30T21:33:24.758Z", "7.16.7": "2021-12-31T00:23:17.031Z", "7.16.8": "2022-01-10T21:18:26.487Z", "7.17.12": "2022-05-16T19:33:12.967Z", "7.18.0": "2022-05-19T18:16:37.819Z", "7.18.1": "2022-05-19T20:37:55.207Z", "7.18.4": "2022-05-29T21:50:12.522Z", "7.18.6": "2022-06-27T19:50:52.152Z", "7.18.8": "2022-07-08T09:32:35.430Z", "7.18.10": "2022-08-01T18:46:42.075Z", "7.18.12": "2022-08-05T13:42:58.560Z", "7.19.0": "2022-09-05T19:02:22.216Z", "7.19.1": "2022-09-14T15:29:11.715Z", "7.19.3": "2022-09-27T18:36:45.344Z", "7.20.0": "2022-10-27T13:19:19.779Z", "7.20.2": "2022-11-04T18:51:08.792Z", "7.20.7": "2022-12-22T09:45:43.688Z", "7.20.13": "2023-01-21T14:30:40.366Z", "7.21.0": "2023-02-20T15:31:20.727Z", "7.21.3": "2023-03-14T14:59:35.340Z", "7.21.4-esm": "2023-04-04T14:10:03.292Z", "7.21.4-esm.1": "2023-04-04T14:22:00.841Z", "7.21.4-esm.2": "2023-04-04T14:40:06.396Z", "7.21.4-esm.3": "2023-04-04T14:56:48.042Z", "7.21.4-esm.4": "2023-04-04T15:13:56.199Z", "7.22.0": "2023-05-26T13:45:50.768Z", "7.22.3": "2023-05-27T10:11:01.030Z", "7.22.5": "2023-06-08T18:21:50.445Z", "7.22.9": "2023-07-12T16:54:00.162Z", "8.0.0-alpha.0": "2023-07-20T14:00:33.319Z", "8.0.0-alpha.1": "2023-07-24T17:53:06.149Z", "7.22.10": "2023-08-07T17:25:22.072Z", "8.0.0-alpha.2": "2023-08-09T15:15:28.258Z", "7.22.11": "2023-08-24T13:08:47.592Z", "7.22.15": "2023-09-04T12:25:23.953Z", "8.0.0-alpha.3": "2023-09-26T14:57:40.754Z", "8.0.0-alpha.4": "2023-10-12T02:06:54.445Z", "7.23.3": "2023-11-09T07:04:14.508Z", "7.23.4": "2023-11-20T14:22:10.018Z", "7.23.5": "2023-11-29T10:25:45.054Z", "7.23.6": "2023-12-11T13:10:03.028Z", "8.0.0-alpha.5": "2023-12-11T15:19:48.726Z", "8.0.0-alpha.6": "2024-01-26T16:14:53.909Z", "8.0.0-alpha.7": "2024-02-28T14:05:51.690Z", "7.24.1": "2024-03-19T09:49:36.095Z", "7.24.4": "2024-04-03T16:53:53.011Z", "8.0.0-alpha.8": "2024-04-04T13:20:24.206Z", "7.24.5": "2024-04-29T18:34:28.378Z", "7.24.6": "2024-05-24T12:25:14.651Z", "8.0.0-alpha.9": "2024-06-03T14:05:51.895Z", "8.0.0-alpha.10": "2024-06-04T11:20:48.221Z", "7.24.7": "2024-06-05T13:16:00.310Z", "8.0.0-alpha.11": "2024-06-07T09:16:10.991Z", "7.24.8": "2024-07-11T14:54:59.088Z", "7.25.0": "2024-07-26T16:59:34.331Z", "8.0.0-alpha.12": "2024-07-26T17:34:06.611Z", "7.25.2": "2024-07-30T02:54:49.632Z", "7.25.7": "2024-10-02T15:15:37.159Z", "7.25.9": "2024-10-22T15:21:52.326Z", "8.0.0-alpha.13": "2024-10-25T13:54:52.771Z", "7.26.3": "2024-12-04T12:35:36.046Z", "8.0.0-alpha.14": "2024-12-06T16:54:41.721Z", "7.26.5": "2025-01-10T17:11:52.301Z", "8.0.0-alpha.15": "2025-01-10T17:25:11.789Z", "7.26.7": "2025-01-24T15:04:53.286Z", "7.26.8": "2025-02-08T09:59:23.351Z", "8.0.0-alpha.16": "2025-02-14T11:59:45.849Z", "8.0.0-alpha.17": "2025-03-11T18:25:40.145Z", "7.27.0": "2025-03-24T17:41:55.397Z", "7.27.1": "2025-04-30T15:09:35.173Z", "8.0.0-beta.0": "2025-05-30T15:51:51.816Z", "7.28.0": "2025-07-02T08:38:17.918Z", "8.0.0-beta.1": "2025-07-02T09:04:53.464Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "keywords": ["babel-plugin", "typescript"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}