{"_id": "graphemer", "_rev": "7-fb27d8101cf1659d3b7a827a8328be78", "name": "graphemer", "dist-tags": {"latest": "1.4.0"}, "versions": {"1.0.0": {"name": "graphemer", "version": "1.0.0", "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "MIT", "keywords": ["utf-8", "strings", "emoji", "split"], "scripts": {"prepublishOnly": "npm run build", "build": "tsc --project tsconfig.json", "pretest": "npm run build", "test": "ts-node node_modules/tape/bin/tape tests/**.ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "dependencies": {}, "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "gitHead": "aede0bdc9c06a27c4a69983ff3c6b3ebd6d0f256", "_id": "graphemer@1.0.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-qOya6PRWI3Nh5i5UQg3GUuBdjiNmtZpssC1yLYXsMcc9xlvSOWzWobHBT8GsIl3K7jopj3TK6UYzLnSX9SAxTg==", "shasum": "3c7eeab1a7b5300a4647b688b599122796ddcd1b", "tarball": "https://registry.npmjs.org/graphemer/-/graphemer-1.0.0.tgz", "fileCount": 4, "unpackedSize": 9044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfX45PCRA9TVsSAnZWagAAzmcP/3bwktiKWW/qcaVB9l5f\nhpqHSMrABsN4jzHBzWd9IYmwni2wZWJuH+2KMRvabw/mBGhSAdP9uvjFeZnQ\n+ynKcB67445bjjAN/tFdLmPI2dKTiCtXjWLtfE9RPYt0amLVqIvWj8Fk9QO9\nOU9iVdmFpy3w3nu0fZtJeKMOOrzzcrzuD0SZCCyWDbGmjdMEYzyMal2ZSWap\ntzbVRV47/s1hQtx8cuYTx3TF9m0wcbK7uGCWA8EcrZPvew1a5/5ypsa9gm+X\nPJX2d/CJJOe/6SrMi+3mLmMxQG1i4Ewq0ESpFJCSG6K2gwLWRp6VOw8GD+IO\nj9rJVlTFo8V/bj8dA/pnjCCfyohhf3YkKdKSM8XeXkWRr5XwimjqCpkLi2YX\nedqgCfB6m0G42wAkAG+aCmzlJGcRS9DPnypB/EJfIr3FjieELv8RWQQ/SWuQ\nHfBMjt6Kb5pFrmXphutF6u9I7nu6ds9L9izi3MvwZf3jATbe5NgWXWxrKS0k\nFR12Ta+d4q8SFXFtF4WUHCVE4hkzY5VzpBu2KeJFAOIFGeWp6yeTxk7X1JLC\n8jpKtdo8/wYV96P571QcnsE6+hSJwVmxALgf6E+gLCl4TaRJ8beiq0uOGNCm\nyTF/ZUhNXaf17HZYa1wW2LxHUgic4BEQ3M9DRF6hreZmYkutU/QNRPj/miWm\nDqnP\r\n=oySR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH7lQ/iwPWqqPFOffZFPDKjTJi812MfS3a8sfh1EUAHoAiEAztw2ghuaePTDv9M+SQBPgmPl1aiTT7qVNK9uiDNO0FQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graphemer_1.0.0_1600097871094_0.009447422511954517"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "graphemer", "version": "1.1.0", "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "MIT", "keywords": ["utf-8", "strings", "emoji", "split"], "scripts": {"prepublishOnly": "npm run build", "build": "tsc --project tsconfig.json", "pretest": "npm run build", "test": "ts-node node_modules/tape/bin/tape tests/**.ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "dependencies": {}, "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "gitHead": "24b6c49c7e24af70a261cf8167d5dec7c9c04641", "_id": "graphemer@1.1.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-d3IwVBPqslqOY9i6v7Aeajfxm/+Ds/z9Fx8PcwYn2eWxjrVKSDn+3pX4GTOegZygUkGu2qaGPttN6IWKCQ5W7A==", "shasum": "3f56689dd3207f348a438b14e6ffe5f02f26e623", "tarball": "https://registry.npmjs.org/graphemer/-/graphemer-1.1.0.tgz", "fileCount": 4, "unpackedSize": 9136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfX6IFCRA9TVsSAnZWagAAvt0QAJbGbE3QxRDmXH1z8hpS\nnCYw/I2IY0qsskFPq+YZZDWj/NcqKwstbs3P3b+eb4A0le+5WfkcfeQh/+PO\nq2Rah9yC+9LbyokpsQX9P7DJgKrEyowmw+Lmw4k7mcnQSoxfvcxukDRGNdUo\nBxp+4tK5gJs7MJEyFqOjDSpQXUOomxFZA0nkj7A2LEMDnoOBJ6/X757RcvAe\nVhhNrmwUP65X7eYFtcYtD7UnCPLgcYyIr8s6dMmBUsXVUxYpaZhACi1Fe7ZH\nbSRzh2hS6IIZHJvwTr8z+EAPz15KrlLiX0E3TdhOquk7IsPWeREd9QG7wxor\n3bDtlNtU36G+fEcI4flp/zp2TNQsLgpg+NhEHfx46e78oV2n+hDyn2OAFRuO\n39M4S896FG6sj+hWAvQFeB0Ja10tLkgum2E08IGYcNh54qPTxWQ0oUvuI2OY\n3F3Hefm/iKIoU6hTFyZjVmuI0HgIOOKNgzknRxz7z/Vj34YXxaQpciqn840o\nP5UNykIkjTcgyYSUmyhyEVVSkCnU9KnhPSEKzTc3kQZkw6Pm1ZwHx+bAIvp/\namnKMs2sQZrqFkINLy8Ra8QTvabliZ++NgylBFvDoLlO5unCWkScbuSvBZAI\nIOCpDBcTUbJSs+tkEX6qhDLapqEIrcevyMroum20w/U8b35PjGaQ03misd63\nH16+\r\n=MvMw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwExyorzeraz6QJ3ofIm6e7euHQsVINb0pKLxzY4EVwwIhALhwOGQogz+HowCDpdNnTO989exMH5K/S/cOx2ZUoTxX"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graphemer_1.1.0_1600102917378_0.5915635240249841"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "graphemer", "version": "1.1.1", "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "MIT", "keywords": ["utf-8", "strings", "emoji", "split"], "scripts": {"prepublishOnly": "npm run build", "build": "tsc --project tsconfig.json", "pretest": "npm run build", "test": "ts-node node_modules/tape/bin/tape tests/**.ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "dependencies": {}, "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "gitHead": "5170b7f7561f9ce88cb5405633bfb4ed63b29d6f", "_id": "graphemer@1.1.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-MpnucZlOUehL4juSASYPukoer5rOvKg56Gm/qfyeNjNQgtOqyIEo25/ZGz8nnYDSOecl6w03vy2CKL1DToLcig==", "shasum": "be2aa7ace23c7fdf263f1d34d83ae19a091d1d12", "tarball": "https://registry.npmjs.org/graphemer/-/graphemer-1.1.1.tgz", "fileCount": 19, "unpackedSize": 199562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfX6sNCRA9TVsSAnZWagAA4zUP/RLkcp1v+ji53ofk+bxs\nMNimNhJ5FaGhMwtrn1c2ta8e+Sbv4uRWLxrBl0MtMVy5j4v+35r5C5mLz0wE\nte0c6veE6h041X6l0qOCdtKhUjawjmEEPn8tzKOwM3VCnkHmh0aKI34/a6Lj\neeN0QHf9Yt1lHDbM5sENMxVJmN573p7gHlopWFeoA79txNNTJllUNUC1H38G\neXoYSlvvMhbrCcwo8xVaYVmoZTGFMtrDh8QjaeporPNw+eN9iyav8CBaEFbn\n2VuSVaCfEd/lMkL3dacjO71/ZRrCq1mVOEp9uoh7rlh/6l+HMqQJde8sX7mw\nnEZUngZ1VA/UAGiLMk3AU176Ne0RDkurQJa29KmLlTigzsUn518uTsI0WqeR\nkotzHyKoJXeLmv1VloPrKDHIkMRCa1RoUM2K8ULos6xiYsZZl8GazsEbhS3f\nqAVjeHvH/RGh/bYqYS6QcEtKhgj4gL21ZbdmShEwMuKUhoYx9Ik3HjEGAwpE\n/ocMplMRNE47zXOBzB84A/riyBUcX7Nw1sMMKk4GXMLqcFUlPjfK3MaWsoVD\n1s4VCNKjKz1hBidr04FZfSooe30joYmqL+9hrdaeEESCL2Hzuc99DWuWOQFl\nJEPicw15E6ckK8F38O8HWApUoci4NM/lO9k4gNqxCC5hsMaCQqSwar+JRe2A\ntXV6\r\n=3Mvh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ4QZsP1JtaRuuvFx+VX+RedEHHgO1j04pt39BMWxCGwIhANa4jmWAIbdd+S46IpENTjBPhsxUqz5cB9HGjSnaPiNh"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graphemer_1.1.1_1600105229161_0.484242751424802"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "graphemer", "version": "1.2.0", "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "MIT", "keywords": ["utf-8", "strings", "emoji", "split"], "scripts": {"prepublishOnly": "npm run build", "build": "tsc --project tsconfig.json", "pretest": "npm run build", "test": "ts-node node_modules/tape/bin/tape tests/**.ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "dependencies": {}, "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "gitHead": "50c67f64bd47a6cb76f3ed7d95bbbb74d1dee22a", "_id": "graphemer@1.2.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-Z9hIgb2gYceKHPAZGS0KCWGAmyixB71KFSmObYOGWukWQ5DHg7AU44oR4lfAKwdoKKRK3NdshCTG9/F42RKsAg==", "shasum": "6984f711609f1a16b2ba09c061f2be2a330eb488", "tarball": "https://registry.npmjs.org/graphemer/-/graphemer-1.2.0.tgz", "fileCount": 19, "unpackedSize": 797380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgE+NTCRA9TVsSAnZWagAAUnYQAJoSnQ+pDZu+QE68gLy8\nTOWmqeNj8GoNgrULjajZ8lpStRjA95h7YnPSjHWsC3z+pvvBJ4xNTaACOnIP\nHFCb7oFQZOsDv1kHDr2Wjd+kWFQYlduT4No/GP+TFJoZtFXxYvO2VonYecK5\nFJsRd+V6JAnl1BiJIkq83qRGl+VxV3kaNoONb07kEVj34qeOAimrsL+PNCBZ\ni8GnfS2LJn0fJZ2Kb/AEkYvzDTHY2IF/669NlvWXujfoHWIz/skLImTZhAit\n9NSV2+uQmQaoDejgyfZEdnfGziWzi4FAUfSvR9OcsC5OqxLVp1kHvEzb2TPW\n2Yj9wrQhpqynxs4+41RQNC+rxAYHMpjj6BOtiaWSJOIeWjZJdlGD62KYk1SB\nYJlKm2a+xNARR54Jl3U3elhjcvNaCurFnlRmuoFXCLC/rKXat9z+wnLlZGIn\nYIhkPdHfyJO+N7m7uz5BNscebptSGr8zuFA5pXBsIDOMH29GRetJfvIJFx7R\nAY+7FZ5rgL1kyMJXVtoHSZi4FMrpi1DwA3rpPi5OEfQ1OEQloCyURYfdA8r2\nYIpLE/DtIrRK9Yrg6pknUSRrwQGfYKuxOp6xOg2L/GndW3D7Biy7NP/eW5+d\nO1xcsDVzfI6gwSO47To98ZHGX0n+4wbIMNxz70BWQPW9f1FzJVY9Zq+2AAe/\nQ06M\r\n=Nndc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHiJD/Z2ljxgAKrWxv0MpIvnXlahccsJrOWyuLT6fMzbAiAddQUdum4aNZmJpBoEHbqvg25mbD1ZGwZIQNSeB1F0YQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graphemer_1.2.0_1611916115243_0.10838331884902108"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "graphemer", "version": "1.3.0", "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "MIT", "keywords": ["utf-8", "strings", "emoji", "split"], "scripts": {"prepublishOnly": "npm run build", "build": "tsc --project tsconfig.json", "pretest": "npm run build", "test": "ts-node node_modules/tape/bin/tape tests/**.ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "gitHead": "6148859bb06236f07168f98d8eac195fe5d2b890", "_id": "graphemer@1.3.0", "_nodeVersion": "12.22.7", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-f/Krp8lS3HqChBQ99mkDRpsiCsHP1PbUkkV8Y30mMnaEZfDv1y2KWbtg14xO8BnR4LUPaeVOt5QkjkXbi6nCLQ==", "shasum": "da05f48272d2840ff8b2d89152be5122f64b7402", "tarball": "https://registry.npmjs.org/graphemer/-/graphemer-1.3.0.tgz", "fileCount": 19, "unpackedSize": 804382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht3F3CRA9TVsSAnZWagAAA1gQAI5S/XUHg6Zn7b9iqdEJ\nuKGSW7i982B1pJNUnSA9C3jOidmZPkrfRI0GLC5nVrr+puOY8ORFGYLej0JB\nmuUlTnaNN91dh+ArunfGUC3Gbwb+RPequuwEIb/oTriCc+HY8yAIHwS7esSH\nRfqjW7anO13dbEqHN94cWUhMjTuPRZMP/2EreGwgOI6uq7OdV7r8uaGoEdbj\nBGiBudm+xIDt0Ssk5VpJsNj7QpAwQoy8v1uuWjcAJ7dnoJLR0nnjCTr96dTp\njv1BdNuCFStMwdnXV++MPjwzYPIlKKYpuKhAzmG3TajH5G5oMr5OQuoXeV0S\npgGHEm+lun30a2DCMH4pypGgtU/Fqy8eUpCJ5CheVrEQJqd1c3LgSuOTPVu9\n5q9BwY3GLwP0Fk2mIqlfu/B2I0wcJ2Sw9Qz9IDLImVLVj7e4GQPg7nPFrVhb\nCYATnais5BPrt1ziC7MfgtGGbyTBepz7l0TOtFVJr2uOngALksgYkgEPDKYL\nkopxBb448138qkNYTdlBVb+gs2jWPyXk+B8WtRzdVYcw4u2ymhC8wPjyfZDC\nPlxFgkxgxw/fCUeg+xA3vdBUXtLbeHdvqZR8vv3DaYCTC0dM5gvFg/fFVq9G\neoc7XZ7Cwuo6EiVRsXTLtwM72lz9P2fIL4eGbvJKApLHL2vHTRwMRMr+sZ/t\nKYGs\r\n=sqol\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGdgzjmXGgIktX6e+Gc2PB513jpPZlgd3Lz37kkfy9kPAiBJ0EzquLujODa/7I9OUIvqWd6LKLRDOAuWCdysr73hyw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graphemer_1.3.0_1639412087227_0.589658191669399"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "graphemer", "version": "1.4.0", "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "MIT", "keywords": ["utf-8", "strings", "emoji", "split"], "scripts": {"prepublishOnly": "npm run build", "build": "tsc --project tsconfig.json", "pretest": "npm run build", "test": "ts-node node_modules/tape/bin/tape tests/**.ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "gitHead": "ed86fe916dad2d90407c66fc6182d3c40f7fc827", "_id": "graphemer@1.4.0", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==", "shasum": "fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6", "tarball": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "fileCount": 19, "unpackedSize": 811810, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA42fYJZdHvnKvinuTZMMKC8AgNXW32fcs6YJg8R0qnpAiBL5yMBFIjjxnsDbf1PD0qnziYJwJ8e9WsSOHRgdKC00w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKEI2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqjkw//ZhRplSR2f2nZ/87WaYHs14aXtfr7ApwjCNQO2eGJ5M3PMLwf\r\nMYdrDXlFDcEzNFAg6bs2KqzspnWNoUCT+xu+I0XhUtyLST6ESfNihfx3ZbYG\r\nGBFjRvYLN+bVyeEPvi0IFqSINpTdbYDVdoHHZS6/jigi7e8cyjXrCQvUvoL/\r\nFSviYR/3OES37n7033WnV+3c/io4ABmPLpEQzmwMnRjSS6C1QiO6IL5m3qz9\r\nJWZGd+PCLsup8VCeRfVOIZDzPibpHKoEG6CNgyad4vgvq2uBaxu9krZSMB/f\r\nksf2mho4cHofICfip3oNZTegKflWPQXrmkjAeUb2GLSAGvkCJDnjhAOKpVqQ\r\nFj96iYY9HiPZuDwo6qfASQ8ieJ5NlwGAoW3zw7g7AP1dAzNwWdC2UoWfYsVt\r\n9O50AuuDEwHW9lhBs4t6ErLrtzLoKlxnGjep7xgdVQlkymHvaRQ3C6KPJ8Pj\r\nPvrMTh8Y5/yD3YTwa7nGBMbC37gBp0MzyBUD/YoY5p2KBwAV62p4uRs0ZSo1\r\nRsFb9zKs5RJ531mgfa5hBOH3YHpSSxTkuhGufwoktcYT1wZeTPPbDO07sIyB\r\nyXoAaYUkBzvxFdwUNY4x6zQdRztugtejQC4oUjMMcmGU5srYkgHDs1Hv6CJp\r\nPeTPFES6Na15s3YFEW8oW282nnJ4OsIDzao=\r\n=74Lb\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graphemer_1.4.0_1663582773867_0.23940933334028247"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-09-14T15:37:51.043Z", "1.0.0": "2020-09-14T15:37:51.224Z", "modified": "2023-06-22T16:32:15.232Z", "1.1.0": "2020-09-14T17:01:57.552Z", "1.1.1": "2020-09-14T17:40:29.304Z", "1.2.0": "2021-01-29T10:28:35.373Z", "1.3.0": "2021-12-13T16:14:47.451Z", "1.4.0": "2022-09-19T10:19:34.014Z"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "homepage": "https://github.com/flmnt/graphemer", "keywords": ["utf-8", "strings", "emoji", "split"], "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "license": "MIT", "readme": "# Graphemer: Unicode Character Splitter 🪓\n\n## Introduction\n\nThis library continues the work of [Grapheme Splitter](https://github.com/orling/grapheme-splitter) and supports the following unicode versions:\n\n- Unicode 15 and below `[v1.4.0]`\n- Unicode 14 and below `[v1.3.0]`\n- Unicode 13 and below `[v1.1.0]`\n- Unicode 11 and below `[v1.0.0]` (Unicode 10 supported by `grapheme-splitter`)\n\nIn JavaScript there is not always a one-to-one relationship between string characters and what a user would call a separate visual \"letter\". Some symbols are represented by several characters. This can cause issues when splitting strings and inadvertently cutting a multi-char letter in half, or when you need the actual number of letters in a string.\n\nFor example, emoji characters like \"🌷\",\"🎁\",\"💩\",\"😜\" and \"👍\" are represented by two JavaScript characters each (high surrogate and low surrogate). That is,\n\n```javascript\n'🌷'.length == 2;\n```\n\nThe combined emoji are even longer:\n\n```javascript\n'🏳️‍🌈'.length == 6;\n```\n\nWhat's more, some languages often include combining marks - characters that are used to modify the letters before them. Common examples are the German letter ü and the Spanish letter ñ. Sometimes they can be represented alternatively both as a single character and as a letter + combining mark, with both forms equally valid:\n\n```javascript\nvar two = 'ñ'; // unnormalized two-char n+◌̃, i.e. \"\\u006E\\u0303\";\nvar one = 'ñ'; // normalized single-char, i.e. \"\\u00F1\"\n\nconsole.log(one != two); // prints 'true'\n```\n\nUnicode normalization, as performed by the popular punycode.js library or ECMAScript 6's String.normalize, can **sometimes** fix those differences and turn two-char sequences into single characters. But it is **not** enough in all cases. Some languages like Hindi make extensive use of combining marks on their letters, that have no dedicated single-codepoint Unicode sequences, due to the sheer number of possible combinations.\nFor example, the Hindi word \"अनुच्छेद\" is comprised of 5 letters and 3 combining marks:\n\nअ + न + ु + च + ् + छ + े + द\n\nwhich is in fact just 5 user-perceived letters:\n\nअ + नु + च् + छे + द\n\nand which Unicode normalization would not combine properly.\nThere are also the unusual letter+combining mark combinations which have no dedicated Unicode codepoint. The string Z͑ͫ̓ͪ̂ͫ̽͏̴̙̤̞͉͚̯̞̠͍A̴̵̜̰͔ͫ͗͢L̠ͨͧͩ͘G̴̻͈͍͔̹̑͗̎̅͛́Ǫ̵̹̻̝̳͂̌̌͘ obviously has 5 separate letters, but is in fact comprised of 58 JavaScript characters, most of which are combining marks.\n\nEnter the `graphemer` library. It can be used to properly split JavaScript strings into what a human user would call separate letters (or \"extended grapheme clusters\" in Unicode terminology), no matter what their internal representation is. It is an implementation on the [Default Grapheme Cluster Boundary](http://unicode.org/reports/tr29/#Default_Grapheme_Cluster_Table) of [UAX #29](http://www.unicode.org/reports/tr29/).\n\n## Installation\n\nInstall `graphemer` using the NPM command below:\n\n```\n$ npm i graphemer\n```\n\n## Usage\n\nIf you're using [Typescript](https://www.typescriptlang.org/) or a compiler like [Babel](https://babeljs.io/) (or something like Create React App) things are pretty simple; just import, initialize and use!\n\n```javascript\nimport Graphemer from 'graphemer';\n\nconst splitter = new Graphemer();\n\n// split the string to an array of grapheme clusters (one string each)\nconst graphemes = splitter.splitGraphemes(string);\n\n// iterate the string to an iterable iterator of grapheme clusters (one string each)\nconst graphemeIterator = splitter.iterateGraphemes(string);\n\n// or do this if you just need their number\nconst graphemeCount = splitter.countGraphemes(string);\n```\n\nIf you're using vanilla Node you can use the `require()` method.\n\n```javascript\nconst Graphemer = require('graphemer').default;\n\nconst splitter = new Graphemer();\n\nconst graphemes = splitter.splitGraphemes(string);\n```\n\n## Examples\n\n```javascript\nimport Graphemer from 'graphemer';\n\nconst splitter = new Graphemer();\n\n// plain latin alphabet - nothing spectacular\nsplitter.splitGraphemes('abcd'); // returns [\"a\", \"b\", \"c\", \"d\"]\n\n// two-char emojis and six-char combined emoji\nsplitter.splitGraphemes('🌷🎁💩😜👍🏳️‍🌈'); // returns [\"🌷\",\"🎁\",\"💩\",\"😜\",\"👍\",\"🏳️‍🌈\"]\n\n// diacritics as combining marks, 10 JavaScript chars\nsplitter.splitGraphemes('Ĺo͂řȩm̅'); // returns [\"Ĺ\",\"o͂\",\"ř\",\"ȩ\",\"m̅\"]\n\n// individual Korean characters (Jamo), 4 JavaScript chars\nsplitter.splitGraphemes('뎌쉐'); // returns [\"뎌\",\"쉐\"]\n\n// Hindi text with combining marks, 8 JavaScript chars\nsplitter.splitGraphemes('अनुच्छेद'); // returns [\"अ\",\"नु\",\"च्\",\"छे\",\"द\"]\n\n// demonic multiple combining marks, 75 JavaScript chars\nsplitter.splitGraphemes('Z͑ͫ̓ͪ̂ͫ̽͏̴̙̤̞͉͚̯̞̠͍A̴̵̜̰͔ͫ͗͢L̠ͨͧͩ͘G̴̻͈͍͔̹̑͗̎̅͛́Ǫ̵̹̻̝̳͂̌̌͘!͖̬̰̙̗̿̋ͥͥ̂ͣ̐́́͜͞'); // returns [\"Z͑ͫ̓ͪ̂ͫ̽͏̴̙̤̞͉͚̯̞̠͍\",\"A̴̵̜̰͔ͫ͗͢\",\"L̠ͨͧͩ͘\",\"G̴̻͈͍͔̹̑͗̎̅͛́\",\"Ǫ̵̹̻̝̳͂̌̌͘\",\"!͖̬̰̙̗̿̋ͥͥ̂ͣ̐́́͜͞\"]\n```\n\n## TypeScript\n\nGraphemer is built with TypeScript and, of course, includes type declarations.\n\n```javascript\nimport Graphemer from 'graphemer';\n\nconst splitter = new Graphemer();\n\nconst split: string[] = splitter.splitGraphemes('Z͑ͫ̓ͪ̂ͫ̽͏̴̙̤̞͉͚̯̞̠͍A̴̵̜̰͔ͫ͗͢L̠ͨͧͩ͘G̴̻͈͍͔̹̑͗̎̅͛́Ǫ̵̹̻̝̳͂̌̌͘!͖̬̰̙̗̿̋ͥͥ̂ͣ̐́́͜͞');\n```\n\n## Contributing\n\nSee [Contribution Guide](./CONTRIBUTING.md).\n\n## Acknowledgements\n\nThis library is a fork of the incredible work done by Orlin Georgiev and Huáng Jùnliàng at https://github.com/orling/grapheme-splitter.\n\nThe original library was heavily influenced by Devon Govett's excellent [grapheme-breaker](https://github.com/devongovett/grapheme-breaker) CoffeeScript library.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}