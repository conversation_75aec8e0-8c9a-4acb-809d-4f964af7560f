{"_id": "bufferutil", "_rev": "38-e367fef0ca1ca9e06dcd217d897796c2", "name": "bufferutil", "dist-tags": {"n-api": "3.0.3-napi", "latest": "4.0.9"}, "versions": {"1.0.0": {"name": "bufferutil", "version": "1.0.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "78f783cc2c5a455c6964c8b4d272f4e22bcf083d", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-1.0.0.tgz", "integrity": "sha512-RMawClJB1CnPLl5K2l7jCRcY09gtlPEymr9NMc+N12ny79klNZdSH18xSEpUYKKZqp2tL9vTtqbBi/OiteCEmg==", "signatures": [{"sig": "MEQCIGPDVdb8G3wvad8LlVS6vZA5RQ/LW/sneYXR8bRrlzYsAiAp9OG+UBf8ClgDVyLlt1mO2TIT/HYBVeNtVCIx78HWCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "78f783cc2c5a455c6964c8b4d272f4e22bcf083d", "gitHead": "4a87625332d0bbe1a9dd0f041f8897e115c0c74b", "gypfile": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/websockets/bufferutil", "type": "git"}, "_npmVersion": "2.3.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"nan": "1.6.1", "bindings": "1.2.1"}}, "1.0.1": {"name": "bufferutil", "version": "1.0.1", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "0c53a9ffe8d616c4e2df27d00b808f7a25501e3b", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-1.0.1.tgz", "integrity": "sha512-mzomVwRbI5iFktXEsCY2ZTUd8WAUSbzwLIbh7oAVvaY22gCWVFH3h3ZSUSmURkP2NvS2sAfWyijqR5qduu5tSw==", "signatures": [{"sig": "MEYCIQDU0QMF753nFE0ZgJE2jHWm5GZqnJHjIBX2zMs3LH6G+wIhAOYMJtItPSDmV2PqvvFmR9VodliMk4++H+M4HSFEPKBh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0c53a9ffe8d616c4e2df27d00b808f7a25501e3b", "gitHead": "3bbb6f23193fae7683b61e2cae1f85ede5fb4469", "gypfile": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/websockets/bufferutil", "type": "git"}, "_npmVersion": "2.3.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"nan": "1.6.x", "bindings": "1.2.x"}}, "1.1.0": {"name": "bufferutil", "version": "1.1.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "3f96be77a7f8652ff69ef29f1cabc8b89f7fd972", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-1.1.0.tgz", "integrity": "sha512-bttEVtkre4VYrZH+ciatjApTuac7jLMQXVXzM/ymw82WFREdOrJO496C4Bkh0/FfYoBUSOFaAYF32a5Q8dyiLw==", "signatures": [{"sig": "MEQCIC+L6qpaiMAvjuwm+hmmMLscc7giRw9PRRRuJm1/ztvvAiAQNzO1cHKrxRO/EA7ti+BmdipLpMF1pwZNxQ/8Cx0dNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3f96be77a7f8652ff69ef29f1cabc8b89f7fd972", "gitHead": "0ba97de2091708438c98147d9328def70183aa79", "gypfile": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/websockets/bufferutil", "type": "git"}, "_npmVersion": "2.7.5", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"nan": "1.8.x", "bindings": "1.2.x"}}, "1.2.0": {"name": "bufferutil", "version": "1.2.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "a6c6862627def49aa6d5a6de52cfc03339698a28", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-1.2.0.tgz", "integrity": "sha512-CWEl1dQVxRJg9162tBwjc0fd3HK4mETPf2DrcC7jGUOR7+V/o5Ztd0pbUS0gxaW0bAWGvJ9RUYHQ8SS5DBHiBQ==", "signatures": [{"sig": "MEQCIH1qyDRQ6PKXipYTz32t1U6E7m/KwPdNkUiVof73x0/nAiB6HLll0thsNjAne8lEml2LxKYO3miSL+vZgvGSH4kg7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a6c6862627def49aa6d5a6de52cfc03339698a28", "gitHead": "cdb15ee02942bc88b068ed1cf5ca48ab9216327e", "gypfile": true, "scripts": {"test": "echo \"Only testing builds, test have to be extraced from `ws`\" && exit 0", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {"nan": "^2.0.5", "bindings": "1.2.x"}}, "1.2.1": {"name": "bufferutil", "version": "1.2.1", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "37be5d36e1e06492221e68d474b1ac58e510cbd7", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-1.2.1.tgz", "integrity": "sha512-rtE2s2JHFmfaldMwWFSUaPTxfxq6Um3xw9PEUK5bAfW83UTXp3WQpE7slnD2bd9GUgb0BA7JC/7ZxeBrIq+8Dw==", "signatures": [{"sig": "MEUCIQDubmLLp0eO1t9c1qQR9PdmzbJLjXki/y+YFiPHhlevJQIgDEtLolfNUfPIZuOPWLokezQky88g9sC7GLKO88kfuRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "37be5d36e1e06492221e68d474b1ac58e510cbd7", "gitHead": "cb7163377b8032fb79ddd835a549c83488585859", "gypfile": true, "scripts": {"test": "echo \"Only testing builds, test have to be extraced from `ws`\" && exit 0", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "0.12.3", "dependencies": {"nan": "^2.0.5", "bindings": "1.2.x"}}, "1.3.0": {"name": "bufferutil", "version": "1.3.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "69fdf13ad9d91222baee109945faadc431534f86", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-1.3.0.tgz", "integrity": "sha512-SFYBVhwk3e2Z6vaXyXTY0Ev0cKu50ndWvzgvo+2NhXN4AH2H+dn8ZkxzZ4kxrDmJgV8JvfeIjK5jp+tqzdNT5Q==", "signatures": [{"sig": "MEUCIQDbGjdzd40vuE9lrLzxYBKrHe8lXkXcR+mJe6bK968rzAIgdv1kSWjgJ6XUYqH9t9m822TjsNGWRV5q59PGqY6f2U0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "69fdf13ad9d91222baee109945faadc431534f86", "gitHead": "d471757e8edd0e1e0dfb570b9845a4e180f5f33f", "gypfile": true, "scripts": {"test": "echo \"Only testing builds, tests have to be extraced from ws\"", "install": "node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"nan": "~2.4.0", "bindings": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-1.3.0.tgz_1480231055554_0.3655267891008407", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "bufferutil", "version": "2.0.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "6588ed4bafa300798b26dc048494a51abde83507", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-2.0.0.tgz", "integrity": "sha512-4UT+K+xrAASFNy+ukiOqqDGqYFle7dz7dlnmPjqMATTbvq4yQB5tZ2xXnTAHhM2mOtUXOilDRv+IwG5R/fvLaw==", "signatures": [{"sig": "MEUCIQD05vc6tXsqo9+VlX8z1zzeBpVqMMmRSMSb54bkkaYthQIgKqixB7e0wLo4QXqte9hw/+U6w+Tk7BzQlEzlMDx8op0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6588ed4bafa300798b26dc048494a51abde83507", "gitHead": "6a1c1a54873e5d776db0b08b7e9b1748ed4e3f01", "gypfile": true, "scripts": {"test": "echo \"Only testing builds, tests have to be extraced from ws\"", "install": "node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"nan": "~2.5.0", "bindings": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-2.0.0.tgz_1486144318360_0.7070702933706343", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.1": {"name": "bufferutil", "version": "2.0.1", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "8de37f5a300730c305fc3edd9f93348ee8a46288", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-2.0.1.tgz", "integrity": "sha512-zJyJIq02mddcR+yqkDrw9oPQfvl55FUxpWhNca2LiCRfXTWIOOcPKieXAljTa2HuRdkqIv21lOdprakybYuC6A==", "signatures": [{"sig": "MEQCIBsNEB7AkuCCTJormmF6DavqstvEd1EJItM8Miw1StFGAiB9UTP2w+Fx5b+3eNmkx6/jHp3mupWKnHdgIINMURTHrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8de37f5a300730c305fc3edd9f93348ee8a46288", "gitHead": "4ef41fc84d9bf72f2bd965baa207f3addfab6bdc", "scripts": {"test": "echo \"Only testing builds, tests have to be extraced from ws\" && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"nan": "~2.5.0", "bindings": "~1.2.1", "prebuild-install": "~2.1.0"}, "devDependencies": {"prebuild": "~6.0.2", "prebuild-ci": "~2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-2.0.1.tgz_1486489476054_0.8282803113106638", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0": {"name": "bufferutil", "version": "3.0.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "afbb831c47229accf0b1f207d4a9942841b0ab0f", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.0.tgz", "integrity": "sha512-+1wAuwm+VBzWbSpLMy6Q3zTBa1glOuquoIMebkgOq7EMRfooTpXGFzv6U244jlw9oG9wb7IS9Ab7R3Ec7WHk0Q==", "signatures": [{"sig": "MEUCIBWem85TrLHbEJg28itRcisj+nf/kNzeoi9Ys09JURMCAiEApXcQRMbsJ6FAHG/n19a21EgrThjL8UdURnvEubD1fZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "afbb831c47229accf0b1f207d4a9942841b0ab0f", "gitHead": "dcd0e68f1cab3cd3c6bde278b100acc4235c99ec", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {"nan": "~2.5.0", "bindings": "~1.2.1", "prebuild-install": "~2.1.0"}, "devDependencies": {"mocha": "~3.2.0", "prebuild": "~6.1.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-3.0.0.tgz_1488540179073_0.6978500687982887", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.1": {"name": "bufferutil", "version": "3.0.1", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.1", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "20b2ef5159ac49f20e44bce38e7c35a6a904ee66", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.1.tgz", "integrity": "sha512-0W0xufsO39Xq9X8m1BE81j+Ljh/h0Ien8ErXtQYvdTTOPvfpe6daHTow5GSkUwaDphMiyRxGF3NgZczYxhltfw==", "signatures": [{"sig": "MEYCIQC8gII3nGb6Qh5D3a73dAs3wibYPnvMij/dV/zvIoaOjQIhAKE5FFL7twVUn5wuVevWxL0hz3juTswVk3y9USWTJytD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "20b2ef5159ac49f20e44bce38e7c35a6a904ee66", "gitHead": "abef931cb5ba41343c7c45a77bfaaef56f225a9a", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"nan": "~2.6.0", "bindings": "~1.2.1", "prebuild-install": "~2.1.0"}, "devDependencies": {"mocha": "~3.4.1", "prebuild": "~6.1.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-3.0.1.tgz_1496217133472_0.7095420539844781", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "bufferutil", "version": "3.0.2", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.2", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "7880c1c4c04ce8a13fffac3fb9ee02ac0cc0d8dc", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.2.tgz", "integrity": "sha512-CGk0C62APhIdbcKwP6Pr293Pba/u9xvrC/X4D6YQZzxhSjb+/rHFYSCorEWIxLo6HbwTuy7SEsgTmsvBCn3dKw==", "signatures": [{"sig": "MEUCIQC8ne7OPdXUDoDdx7UGp6TGtS/GMg5Ya9b4DllUESfxlQIgSi5QKHNbguB2CkXXmUrNH+q6zObMhsCNJY/6Z7+A6Sw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "a9c3cb9085d05957c2cb376a0ec3c7ef10bb5cf1", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "8.1.3", "dependencies": {"nan": "~2.6.0", "bindings": "~1.2.1", "prebuild-install": "~2.2.0"}, "devDependencies": {"mocha": "~3.4.1", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-3.0.2.tgz_1499423453714_0.2923746791202575", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "bufferutil", "version": "3.0.3", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.3", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "ce67caefde2282591e399528467fe623f68f4bd5", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.3.tgz", "integrity": "sha512-o7zTFxMkM/hujDmJKJEeK4N/NYwO3spQ7sfVjjt6Twpkpmw265WbugtpQBGY2+ZHwfqJkf++7Hfn/eWSMzbx5A==", "signatures": [{"sig": "MEYCIQDplVUPTJicOSDgwLU+f7g2Wamn05UhfL8bwLPiwpTJ4QIhAIzpTrd+imXMxHs9LAHWjIANHUKcNkTBpNOSPI8LoRVY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "f6fae26c677215c80aef55720eeb0446e2fe0efb", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"nan": "~2.7.0", "bindings": "~1.3.0", "prebuild-install": "~2.3.0"}, "devDependencies": {"mocha": "~4.0.0", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-3.0.3.tgz_1509697613513_0.7104485637973994", "host": "s3://npm-registry-packages"}}, "3.0.3-napi": {"name": "bufferutil", "version": "3.0.3-napi", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.3-napi", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "e53aabdd845e2867ad5eb8ebb5f147b0bb6e1fa8", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.3-napi.tgz", "integrity": "sha512-lIO2rGIHGyay4TITnH7lHoYmWbICvwrpVK2X3EXZlvo8+lGXMr4IRATAtzDFQJmLno3O8Ef2n5SQoGWE6A9MKg==", "signatures": [{"sig": "MEYCIQCK2l9K5jIXD56xNZygi8d3Qc2bvPjXWFPZs6gpcxIYKQIhAIRkJKOQc6J7oT9SZ8L5d1gNVQfq22wqboWtVLUOGBM7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "e1912475fb859e3153d10feab9a3760cd47acbe4", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"bindings": "~1.3.0", "node-addon-api": "~1.1.0", "prebuild-install": "~2.3.0"}, "devDependencies": {"mocha": "~4.0.0", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil-3.0.3-napi.tgz_1512394412020_0.2639196927193552", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "bufferutil", "version": "3.0.4", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "b9ea85d3749388110736d564a09ebd7cf6785138", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.4.tgz", "fileCount": 7, "integrity": "sha512-7pKiG4FmtqMAcX/rs5iJaUSNVcCCwyEuqzr50KeoEV2by/jU/YKRAv/RSCVzy4Qy+3DznHxunlprtNKLcRIycA==", "signatures": [{"sig": "MEUCIQCwBvdYMzOzz+ntApv+76Cc2R+0/Ionlx9iebvg2VVukgIgdKE3DU8uJt6q8L1bhgw7m5xpktZ3/TT6DD41LLRsx/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7798}, "main": "index.js", "gitHead": "34580b7e3f6e22234f0fcf6438fa92891b8b4104", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"nan": "~2.10.0", "bindings": "~1.3.0", "prebuild-install": "~2.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.0.0", "prebuild": "~7.4.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_3.0.4_1523013134587_0.8753577122226905", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "bufferutil", "version": "3.0.5", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@3.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "8abfc3b6da7aca6a13268b3f2482f0c7aa05fffb", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-3.0.5.tgz", "fileCount": 7, "integrity": "sha512-0fUEthLqfCkYspEuP0vmiAe+PsXslE+AlILb2rmS9I4tAdm3SmpCI69M66zQL20GQEszdbXyVN6q+cpG/yhYlg==", "signatures": [{"sig": "MEUCIQChZsicZnedjrCluLbV3OcmMBICsisb9g6+UYne8eKhJwIgJKK7xLT5OIwhrUHyj8ud3S45lFDnzdEZBJjwNESyE0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7peECRA9TVsSAnZWagAAzYcP/RUjc5RFATHCKHKPGOL7\nXHdjE1NGEIx635yOzKB10nJpEpQy022Qhg2k91vPwLfH7APW+Uf1Gm07NI1X\nsEt0PC9t9RVSWVDGggT3emcvYbgumSJej3vel0IGLSpUUsb663XNaeAcdMk0\nEU6ZrCkihEbElUvyqUczNEGjyJQ9sjGE8eN8MaDttjZk8w7UYDeNioWctkn1\nqqw/FLcknRlmyiyJt15hXdrnepDNFDDAp7M+WU7sVQ+g1g3ojlInoOb72n6J\np3FFf0Th2cZRY7LSUPEq7IyElYQmVYEqF75T0nXPcd4BPgdrJcs7c6sqzRn7\nHndKdC/joTi3ZXSg7wC6zoU3nnBoFHLUmnXGwbmRR2H3HY0XrHhvwZV2MTFC\n94I6ZpKr5dRsoisV/wZGDowUtaR3OjnO82UXsFzdN7XA5A1IdQSbBCLzy3O1\ndVH4Ar8su/TzVK/ZZ+YKhjCKO7Awx+FtaARiaSuNPWkBpBFphCQ708tp6jNO\ng9PdK/n1n3nIGBoJNn1D1YJC4Tx6Vw7sWb+xN1Vd3Sj8iY2CYWqkbAPtrE++\nBZ7Qj+xcBb9FavjVnfAbQe8zkLiRbvWzLB1llB/rHFhBi1x9BJ786VkCB8xf\ncqEyatWEfqODeXXzgGoNuQQPf4bh38dC9spMtQxKKEpcyAUUtEyI8stC126U\noMTG\r\n=wTUL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "765fbe58a9a2a03ac49119966455510c419f0b35", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"nan": "~2.10.0", "bindings": "~1.3.0", "prebuild-install": "~4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.1.0", "prebuild": "~7.6.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_3.0.5_1525585795627_0.6340993025004453", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "bufferutil", "version": "4.0.0", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "a5078160e443751a4e83b6f4d6d7e26c058326a0", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-jpnqMVLo7sqfUY2W92RC4jjj9TuiOSkjB0k43TxPcrBSntZwXUOl8Krfd3eVEdApuScpSTwYKntm/dXU2T8gnw==", "signatures": [{"sig": "MEYCIQDTthrFht15KvQ1Hqv4fgwWU3sJfsCAscU5VyDRSjdhQgIhAIv2cQA17LzACxK6JzF06IreJP7shkq/KvklUV4oAkyw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 266779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbOxfrCRA9TVsSAnZWagAANDsP/ib8VDj25Orrtaz66tXz\ndAQXhzlX/tliJjTM91Vlyf8Ze1QZ8aGDd2u2rVj4/NP0VfxemP7V9QWZB3un\n7BZigIy5l60hbt57KclTUWpnk3KRAY8w4CmT0Lh7ORrJq5x4B/QhgE2Wpuiu\nMDKBeqn8W8Qh+lCCUFgcJcz2QV4NfScWZklHXuPjp9x/wh81SdQakeoK3eaX\nkccasnJq0TmtKe8Rh4MpE4TrxROmVMmZmJ3Rkti3k17S+I8LuBr4wlenoDGK\nXOjUvb2M1AZJ/zhUCRlWNoFuDhoDUXn36WvybvN8ShV5RgKs5MaGFLNoFI64\nXKz2CWjWVHQG2GglFULBtvUnllSmFchbVaK5bvs/W+eSv6xsytyvjUcf/LRB\nPumE9FFGsIRcQorMNcxB83QQtJ0vlKOlM5CrJfhSYHG8CmLBkJKoiASvmh2e\nPFhkIOBSD+gRzHhy+rDGvvaKVHMWoRsBhtfqsfR3E/OzBc1q6Xam/Y1VCPdN\n1y87LdkfbPUzj0immn9lId22r4ng32DL/7jflbabnHyEuhtBvwGjObqunpBP\nfOSprQAG8ZFbAmkPAEYez+FmCwaOdH8xeuxTNeX3y2sUoSonIYcivtCljwfr\nns3pcn04wQ4P3FbrnijoH0B1IV1ebhRMSrdVTVgLumEXUv2SHprG+C0fZjWK\nc0Ss\r\n=dNhl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "ede3ed072ce0cacb40315b65e67bade52b8fef3f", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"node-gyp-build": "~3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.2.0", "prebuildify": "~2.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.0_1530599403467_0.6715705109378833", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "bufferutil", "version": "4.0.1", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "3a177e8e5819a1243fe16b63a199951a7ad8d4a7", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.1.tgz", "fileCount": 15, "integrity": "sha512-xowrxvpxojqkagPcWRQVXZl0YXhRhAtBEIq3VoER1NH5Mw1n1o0ojdspp+GS2J//2gCVyrzQDApQ4unGF+QOoA==", "signatures": [{"sig": "MEUCIE5J9cTzSm6LQjsfjH0sji4/2KU/Ww5Xwc8CHUrW3bGWAiEAp/Sjk3HiB0SglkgRlhQdpOOkIxWsOAEJGtbb50tmDTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 524909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcH0JYCRA9TVsSAnZWagAAkpMQAJ7WLi58+TZTG15Ngddd\nLhTJEUQxSLW15jhGHzw1SZSbfa8oKpcHRRBj65Idfbp9sNl7yG5Tnwam2zj4\nyH2LPLsINuQ+DO6JxLoRahy/xsnzLXtJTUeY3qXu5avaDMLd+biDyiCEghgF\nFh9zvlj+fje7/3zUsiIIuvOBRtciU34rXYZKXQ8PHBEn6vh6IAz6OGxZ6Xuk\niqu+8SdDjg5PG6/Wdv6VS0whJj3UySR9EOgfBnuYxbybUkNDmFuCYrYSRN5i\nFTXSGSCUj1zZwcUuLfHG4M0EAXN4N/HDEbdLQlapIn5YNQLvwPd03Whfqaiw\n+tSZbc9x5pnbr2OpYD4It91yIRUQIE/dxv2D5rte6RQ96ryJUha6IpK+oHBr\ndA8sibg5Xi3J3jv4llpy7y1Od/PNM0u0nyr5ccNRKKx+kPRXyJHQoAawPQro\nxi5JEVxlkPUYFzFiKCFsIbNGVsqndagvFTyA/FfQdYX5MBykKc04LZQV09Ei\nbw4pFGL0r0SPD+ThcAk8gztyEtw1ZwpBe8Kxyu2uhmZiMWig7+Zvs85rKfNh\nevM2WDH1i4BzjnsH1Db5BaPGjLpFj5VxnPMe5fawLDAftkrx3DcaoQDM6yik\nN+9N9ShUgdG/sgvur+MbKB4rGeg9LEe3M6oxxHsZbxilrLqU7ilhBZZn4+hm\nM24q\r\n=Rp//\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e367139397179f7f132900025e9b461e714a35bc", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"node-gyp-build": "~3.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.2.0", "prebuildify": "~2.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.1_1545552472247_0.067683796300124", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "bufferutil", "version": "4.0.2", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "79f68631910f6b993d870fc77dc0a2894eb96cd5", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.2.tgz", "fileCount": 11, "integrity": "sha512-AtnG3W6M8B2n4xDQ5R+70EXvOpnXsFYg/AK2yTZd+HQ/oxAdz+GI+DvjmhBw3L0ole+LJ0ngqY4JMbDzkfNzhA==", "signatures": [{"sig": "MEUCIGJiJYRh7HO7ENZLSNhQern3VyA0Ewuq3DHE1g759x2iAiEA9Lzh80lELWMPXe4cc9pcCsEFOKtIsMphY0swEg1LcT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 929428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnW0MCRA9TVsSAnZWagAAQYUQAKSzVByYDjxF+RqAbT7s\nQ7IyiZDRuujjmXGLsiyD0XLzA9GV1zGPd3U+DvfVQET6bI9L1gPRJ4ZgrneT\nj0iIT9am0SDA9NwWV036cROuQKOnIgJ+Md05+b7QrEfjo1qdVhDxUbrtPxLh\nF1mHyFv5QE0iJOLoYemjJs+BAb+uaCD1NbZmQJqQeVaIv7o4OFeVWrZSqo6g\noakOxO7vbfZcZg8OgANlD1p6ZMwFGUN6CsPkWC6jrVyAxfIKNxbuM/SWjJos\n+xOFiy+yIl9MzzRhCueR0goH6un5+xIS0TRhFnx43B+CV5Xm+yxnxHVJ8+iE\nakuCdddxTaG6ur7EFXXKQuyHBOzCyeAGl5XCcCucFK3OIC+gTnikDv24+aeD\nKV/XMDmtzyH0VzoCwFxL6s939umSS5zZIYz4atucqOCQ0RuJKVFW9yz12upj\ngAfzdh2Dr7vgajFd387vBd2TTcfg6q/Wb3HnhAHzKUZZ7Bqd3UIPYAqSUmjb\nQKhCU8LhOcMKjm/fnFxs432OjesXS+B5tMwJk3soE75dlMLpKdwhzUfg0HUX\nv+MJD4XsJPkNFPRw5D79FU9E7jKt4AipRhr1d0HWZv6adb6ktZEyMkZIehV9\nhZS+mZplq2uJ8CFLVO/LZSOyrP8gagGbGAZ0jdAUTs08b0nDl8Lig41MjG1T\nHlTt\r\n=SDU2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0e7d979038d020b997df653e3f974c93ba85f645", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "15.0.1", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^8.0.1", "prebuildify": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.2_1604152587736_0.2934606470332306", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "bufferutil", "version": "4.0.3", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "66724b756bed23cd7c28c4d306d7994f9943cc6b", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.3.tgz", "fileCount": 14, "integrity": "sha512-yEYTwGndELGvfXsImMBLop58eaGW+YdONi1fNjTINSY98tmMmFijBG6WXgdkfuLNt4imzQNtIE+eBp1PVpMCSw==", "signatures": [{"sig": "MEUCIQDplP/OB2wvKo3MdaEj5n6kkcF376hPRTDK6Q3GnrhhrgIgbL2Bmvqmw+qr0uWI55uT4Y9uAuPbw/5xmFEC5kxesI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 289573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8NLSCRA9TVsSAnZWagAAKP0P/2k6qW4JiSlYXfGlGRoz\ne/7dktVxEW5pQhPa033Ho+C83vTNwHWc2MTlk9WzKluY4glDSN3VxaFYWi3o\n1B+FrrLaNKDSSFOC4ziZ9N8Z0bHSk4GajwVVF9NSAvf+n8whx1jBZr1VUYp4\nREE+xOws8a1nzSyT8e2ZK8FDlCbaDHzfIsAy4iRiWCGZ/D+AiUCnQMAaNVJ0\ndExu3llbrT+jKrsmAP0lZ+qeHxZwbQTt7JCT1DJkKBw8bcKRN6tzRKL9YA15\nFdUj0WDAtozDTt9Slyl3PpweNuCpy7o0K9YwUcSCXiP9xR02k7mP/9+emVAF\nvlDTNgVOnMZYKWdOT6RlUkjhRFjljlJPez5BWy6Kv5x671FBBFOAgli0qAG0\nQ32o6k5vODv3yKnqeMIlifwUrYKjM/r8F+5gkdg43Sw0rYn06AumP1q1/6pY\n4OMiRvmZmTpyE2Czz14w1aOdFqru5lKIVb3qMqVHYr0w4n8MmflciiKSr/qa\nca8ziismpMIEyi0/qVjSosciXhjYXk1SkLsrhgHRvc0L8xmttvw+7E0kiHhR\nhn7hwMCoD0d70sWKttpeuCJebDD7iO46QAueFNN75Jejo0oU5uGbeCTO6IzY\ngbdHcsuj5C1HnLlALY6qPj+mzYLiIrKnt/YsNz5WP4nEacVn2tbbjm+OBuI9\ni+Qo\r\n=y72D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a506f9b7473662f9daea3fbd5e08cfb8c9a685a3", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^8.0.1", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.3_1609618129628_0.7610264455425453", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "bufferutil", "version": "4.0.4", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "ab81373d313a6ead0d734e98c448c722734ae7bb", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.4.tgz", "fileCount": 14, "integrity": "sha512-VNxjXUCrF3LvbLgwfkTb5LsFvk6pGIn7OBb9x+3o+iJ6mKw0JTUp4chBFc88hi1aspeZGeZG9jAIbpFYPQSLZw==", "signatures": [{"sig": "MEUCIQD0etG+0qeH1nzJtfCzv46Qi7B+dZMlVh05nYVPG2bt/QIgd6lyfb1squSJt/hy5ouX4WhyNoeKPAG6vaxrr3BPces=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436267}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "95d2d2ed235dfa42f1d4ed051621783a376dc3ae", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 -i linux-arm64 --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.4_1632596380904_0.23084643814326533", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "bufferutil", "version": "4.0.5", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "da9ea8166911cc276bf677b8aed2d02d31f59028", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.5.tgz", "fileCount": 11, "integrity": "sha512-HTm14iMQKK2FjFLRTM5lAVcyaUzOnqbPtesFIvREgXpJHdQm8bWS+GkQgIkfaBYRHuCnea7w8UVNfwiAQhlr9A==", "signatures": [{"sig": "MEUCIQCEVsToT/7HA8U8tGcHsCB7MNQmJP1cKzX9VMo9ZNewQQIgYTqt8I86L4d8Ipz9E2v+/tpWxaaH8acVPc4t3LAC5g8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406784}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "3923cf1534a9def734f2ded9d46fb095c73e8e16", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "16.11.1", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.5_1634144748118_0.48553202225554615", "host": "s3://npm-registry-packages"}}, "4.0.6": {"name": "bufferutil", "version": "4.0.6", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "ebd6c67c7922a0e902f053e5d8be5ec850e48433", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.6.tgz", "fileCount": 11, "integrity": "sha512-jduaYOYtnio4aIAyc6UbvPCVcgq7nYpVnucyxr6eCYg/Woad9Hf/oxxBRDnGGjPfjUm6j5O/uBWhIu4iLebFaw==", "signatures": [{"sig": "MEYCIQDlTokmZ3shZFj/H5t+PQDYEd799xPv+r7VQW0G46yxfwIhAPzLkP02iLuNLL4KmiJ4ul1YmxmN8vWVtqjN0JqYud3O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0WAfCRA9TVsSAnZWagAAZ2MP/156mJ65FE9Jb5q+duvy\nf2rj8V4B9FDhmeDdUiVqsR9IZa6EjYqcY6l7Jx4x5kzvT7wbZbyAsTwHpWcD\nNHUWyHLz6adFNbJer4pZQY5RP4koQSrM/29kTckpd7XtXsrtfA4nmqqoCh2M\nUksbRRzNDCmFWZM/pqxBeJtXo476SoXep5YglJCNNIUY+g60wyJ9eStwdAfV\nUOyv88xfnkJ8z6fQXcBr+bYHgMuU0PkRljv9hqx/xpf+1e5mfyul+fR5kkUG\nZ1xo69MVuCqJsEZCna+qFR8tiHXpAARSFIoozi3l1NIiB0QOhtLZOyo5uSLm\nnmnzeyUQmiX8zFT9g9dWN7I8he6soca0XfAyicktU0rkaAY5hJlJE5JBQlWR\n1RiUqDUZpt6GI/Pcuv0n/XrhyghnoGWhQlCrVFVShC+jwCbFI3xcZY3jwSKT\nteRBnavt0LfcQz4HF48hB+211IhSoUcWJ1z9aLo0cVsgzu1jGbDvrV9w26YK\nrb13PCBXVEduLQcSNHOvFkmC3G1qRMjlnWHrtM4i4OnJM77RZPVZiWYAQs/J\n14GAvvJ87H+6iZFmGOEstCKVHjMafcM+KtrteWM3ep44o5qyfC/PApb2MkEj\nnRn+v992YqLpuU4D3Lt+YAG4D/itUuFGivT19MtxjWXJ3mq5uxogS1ZlX9fj\najQ9\r\n=U1Jd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "4376c3fbbcfc4c71e9aaebc47c4c3dfdd9c3e2fc", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^7.1.2", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.6_1641111583763_0.2555090492414178", "host": "s3://npm-registry-packages"}}, "4.0.7": {"name": "bufferutil", "version": "4.0.7", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "60c0d19ba2c992dd8273d3f73772ffc894c153ad", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.7.tgz", "fileCount": 11, "integrity": "sha512-kukuqc39WOHtdxtw4UScxF/WVnMFVSQVKhtx3AjZJzhd0RGZZldcrfSEbVsWWe6KNH253574cq5F+wpv0G9pJw==", "signatures": [{"sig": "MEUCIQDTzt3sEp25mrnYpyUDt362DXMtUiDKoVZMkmCNp6uslQIgN6flOhrjk4L8uObSvjT+m754G5SF5Wu2ZKp9A5C9AY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 409448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTqxCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXmA/+JEy9p61MztVQhxA8tfEdRYzatyDGqkq6USv1+Mbr00yZkFea\r\nnrJTMhXPD6xSTXYw8r5lvf9xrCqG3FDBJmOQhnDSgcizOB2nbjx+ZwkJi2f2\r\nFUHRHj7lKF9+cwPyabY2q5IaG15vwnAoB6ya2OeagdkIGd/SIepc7K2/N3Jw\r\n91podWmc0i7PL6ZYoutb2FXloPxMAkyLXYGe5EF03kzKpnAT1rgfxJ71FxIc\r\n1Jt8IAZLEatoD60kdor1xPnaxOoSrwP+0GAMBDsain6rVMoF8mhPWMMqqwVj\r\nZw20+CjTsb7QoWN523lNRb3RXoCdz+m3fSw2GyfpvrvMgHbgaJ7mzC8IhwL2\r\nGZz7GEDsSi0nMsoj/loUUBXHGEtZJjTeYSIEIzvQRSIP+ETzRB+lAL3c+w1z\r\n/cxas9i0d5FSlZkKPzlPyzaX7ejzJgbQfW/NkY5+AdL8xb3eal0jxOfEa013\r\nqSQa0JMr0nqtQH6AxmtcGI4ppc0kNdeBiGfOA1OfJZm91/bAbhRfDNz4YUPy\r\nKE1Iu9bBE3Hs4LtUZ6UDNu6EKdDuI6uyQ8MywUpajH1fEAU8NrfC01q3AKic\r\nYK8dI9LM5qGNY8r55pI6fwg7LpXME8mmWIopwDMpTvf24JxUBNKkmrv0OLW3\r\nQLwPCt8mvow+Tebwpfq7zdi7kUc+DDlzvTg=\r\n=GQES\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "b90f4d22a8a67e1609a10bb8cd01b3e7b13e5cb5", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=14.0.0", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=14.0.0"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.7_1666100290604_0.7387029965195444", "host": "s3://npm-registry-packages"}}, "4.0.8": {"name": "bufferutil", "version": "4.0.8", "keywords": ["bufferutil"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bufferutil@4.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/bufferutil", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "dist": {"shasum": "1de6a71092d65d7766c4d8a522b261a6e787e8ea", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.8.tgz", "fileCount": 11, "integrity": "sha512-4T53u4PdgsXqKaIctwF8ifXlRTTmEPJ8iEPWFdGZvcf7sbwYo6FKFEX9eNNAnzFZ7EzJAQ3CJeOtCRA4rDp7Pw==", "signatures": [{"sig": "MEUCIQDM0qTSP5k0qtCwY6u4qQuSNEgf3xofaaisYfwbq+/9WwIgZcn0Wo3Ts1qoVSophVETp77ikVZKWj7cXQqCEeiG5pA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 414186}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "9047e45073bf93e495ed100361d270d4062867d8", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=14.0.0", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=14.0.0"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/bufferutil.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "WebSocket buffer utils", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bufferutil_4.0.8_1697387768257_0.7342052856975043", "host": "s3://npm-registry-packages"}}, "4.0.9": {"name": "bufferutil", "version": "4.0.9", "description": "WebSocket buffer utils", "main": "index.js", "engines": {"node": ">=6.14.2"}, "scripts": {"install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=8.11.2", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=8.11.2", "test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/websockets/bufferutil.git"}, "keywords": ["bufferutil"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "license": "MIT", "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "homepage": "https://github.com/websockets/bufferutil", "dependencies": {"node-gyp-build": "^4.3.0"}, "devDependencies": {"mocha": "^11.0.1", "node-gyp": "^11.0.0", "prebuildify": "^6.0.0"}, "_id": "bufferutil@4.0.9", "gitHead": "35e2eb6a14a8d58b87bc1f9ff2ddff67e2f1fa97", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-WDtdLmJvAuNNPzByAYpRo2rF1Mmradw6gvWsQKf63476DDXmomT9zUiGypLcG4ibIM67vhAj8jJRdbmEws2Aqw==", "shasum": "6e81739ad48a95cad45a279588e13e95e24a800a", "tarball": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.9.tgz", "fileCount": 11, "unpackedSize": 371859, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3AmehBfoR9GAtFg8ue8PBp2SXwUaSY+UbfvSd9xnzigIgTXIoFnlFj+69TRgVSNDLL6ZkMutkOv5Fuf26Y+U2SC4="}]}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/bufferutil_4.0.9_1735318102046_0.7756668436403582"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-29T10:02:33.540Z", "modified": "2024-12-27T16:48:22.502Z", "1.0.0": "2015-01-29T10:02:33.540Z", "1.0.1": "2015-01-29T10:05:46.769Z", "1.1.0": "2015-05-05T19:36:16.679Z", "1.2.0": "2015-08-13T12:04:59.185Z", "1.2.1": "2015-08-21T11:46:04.028Z", "1.3.0": "2016-11-27T07:17:37.435Z", "2.0.0": "2017-02-03T17:51:59.038Z", "2.0.1": "2017-02-07T17:44:36.648Z", "3.0.0": "2017-03-03T11:22:59.780Z", "3.0.1": "2017-05-31T07:52:14.521Z", "3.0.2": "2017-07-07T10:30:54.893Z", "3.0.3": "2017-11-03T08:26:53.694Z", "3.0.3-napi": "2017-12-04T13:33:32.099Z", "3.0.4": "2018-04-06T11:12:14.651Z", "3.0.5": "2018-05-06T05:49:55.725Z", "4.0.0": "2018-07-03T06:30:03.523Z", "4.0.1": "2018-12-23T08:07:52.401Z", "4.0.2": "2020-10-31T13:56:27.921Z", "4.0.3": "2021-01-02T20:08:49.791Z", "4.0.4": "2021-09-25T18:59:41.079Z", "4.0.5": "2021-10-13T17:05:48.392Z", "4.0.6": "2022-01-02T08:19:43.964Z", "4.0.7": "2022-10-18T13:38:10.830Z", "4.0.8": "2023-10-15T16:36:08.534Z", "4.0.9": "2024-12-27T16:48:22.332Z"}, "bugs": {"url": "https://github.com/websockets/bufferutil/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "license": "MIT", "homepage": "https://github.com/websockets/bufferutil", "keywords": ["bufferutil"], "repository": {"type": "git", "url": "git+https://github.com/websockets/bufferutil.git"}, "description": "WebSocket buffer utils", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# bufferutil\n\n[![Version npm](https://img.shields.io/npm/v/bufferutil.svg?logo=npm)](https://www.npmjs.com/package/bufferutil)\n[![Linux/macOS/Windows Build](https://img.shields.io/github/actions/workflow/status/websockets/bufferutil/ci.yml?branch=master&label=build&logo=github)](https://github.com/websockets/bufferutil/actions?query=workflow%3ACI+branch%3Amaster)\n\n`bufferutil` is what makes `ws` fast. It provides some utilities to efficiently\nperform some operations such as masking and unmasking the data payload of\nWebSocket frames.\n\n## Installation\n\n```\nnpm install bufferutil --save-optional\n```\n\nThe `--save-optional` flag tells npm to save the package in your package.json\nunder the\n[`optionalDependencies`](https://docs.npmjs.com/files/package.json#optionaldependencies)\nkey.\n\n## API\n\nThe module exports two functions. To maximize performance, parameters are not\nvalidated. It is the caller's responsibility to ensure that they are correct.\n\n### `bufferUtil.mask(source, mask, output, offset, length)`\n\nMasks a buffer using the given masking-key as specified by the WebSocket\nprotocol.\n\n#### Arguments\n\n- `source` - The buffer to mask.\n- `mask` - A buffer representing the masking-key.\n- `output` - The buffer where to store the result.\n- `offset` - The offset at which to start writing.\n- `length` - The number of bytes to mask.\n\n#### Example\n\n```js\n'use strict';\n\nconst bufferUtil = require('bufferutil');\nconst crypto = require('crypto');\n\nconst source = crypto.randomBytes(10);\nconst mask = crypto.randomBytes(4);\n\nbufferUtil.mask(source, mask, source, 0, source.length);\n```\n\n### `bufferUtil.unmask(buffer, mask)`\n\nUnmasks a buffer using the given masking-key as specified by the WebSocket\nprotocol.\n\n#### Arguments\n\n- `buffer` - The buffer to unmask.\n- `mask` - A buffer representing the masking-key.\n\n#### Example\n\n```js\n'use strict';\n\nconst bufferUtil = require('bufferutil');\nconst crypto = require('crypto');\n\nconst buffer = crypto.randomBytes(10);\nconst mask = crypto.randomBytes(4);\n\nbufferUtil.unmask(buffer, mask);\n```\n\n## License\n\n[MIT](LICENSE)\n", "readmeFilename": "README.md", "users": {"panlw": true, "ljmf00": true, "erikvold": true, "rocket0191": true, "mykhailo.petrenko": true}}