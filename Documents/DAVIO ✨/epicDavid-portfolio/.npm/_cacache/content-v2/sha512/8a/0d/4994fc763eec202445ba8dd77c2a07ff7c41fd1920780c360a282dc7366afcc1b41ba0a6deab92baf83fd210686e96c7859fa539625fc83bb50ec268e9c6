{"_id": "es-set-tostringtag", "_rev": "5-6eed393ee82b280131abc7117248596b", "name": "es-set-tostringtag", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.0": {"name": "es-set-tostringtag", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-set-tostringtag@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "dist": {"shasum": "5f073226190bb4684e4d6f945a5ea90e7aff9283", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-izRTRsZgSP3EpSxPE6lSzyIYD+qghxjskRHetmy15DhhPxfTohYk1Gce7sMJ3Sr+KjhGPZesKMANBZGTTxYgEw==", "signatures": [{"sig": "MEUCICc2p/ziZwUlAX2+CPNMHZ1p/mppY7IPZID5YJ+U9r7HAiEA0T83N5CT94y62DM9IGIx5c7gYsQcbQIkMzG/VoKX7XI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjo0aYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc9g//ZuZaV205fhQ5Xupsw0/GXazArnaXUXPGd0CTPYmxQN4gBHB3\r\nsKThsLjDfO+J2o2z2x3+TBVTd0ZUWXY4tsunC+YPfv+yFmQjPekhqUTS+CQB\r\nE33GQUl965FLDYID1hkPViDqOE4SlDBqaPOocKEUMcP+eCoRjMggyTV49kAT\r\nf6TbepCbYCqiCClurNPYYkK+X+ZV7ptvOOFO7razTnvTqVfvwHla6Xjs/AR8\r\nbB2z6qoxxkyG6OhxC2X5pgD2bGyGINpA8USeKdpKPXYOsmHf9wVYyit/tLEo\r\n0+AxavnscpDnMaOA0gztMVyyUVF/m9YWacn9OBTd+vv+JBRiwiwvTLFtxK/+\r\nLxnaRGgSr8eaBN96yzHpL1fYwx0YmYbDjaIuLn3+OB/anwEOglA4M4DVaIGp\r\nN9xupKJqJ/Bn3pI/4L3FiT4UZmP5kOs6rFhx3m0h7pC9pvkwidqLCgq+GFjn\r\nKXQx/5w1Y2GCZfk/Ttm+LRyIbuCJ2RMhNczXvT9vazDAelp+NUrcAEcorHlG\r\ntkyvB+BNdqNreuor60ZrR5Eg0AShBjJhhoJ/3yIa/BmziUva2dezoF+ySemc\r\nYNIVDvKfY3UPO2v1X0reUfwnwOi2bpJaYie9qD1WmhntIEe7Ou2TXjPe+uH/\r\nf8OIO/93pA7N6bImJTDqN7/if9c9suw1abI=\r\n=AiPx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "17812b5ed7c033e06a593e3cdeb88177ff6ab3df", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "./test/index.js"}, "repository": {"url": "git+https://github.com/es-shims/es-set-tostringtag.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.3.0", "dependencies": {"get-intrinsic": "^1.1.3", "has-tostringtag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "has": "^1.0.3", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-set-tostringtag_1.0.0_1671644823806_0.6180371947975349", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "es-set-tostringtag", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-set-tostringtag@2.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "dist": {"shasum": "b33fdef554fb35a264fe022c1f095f1f2022fa85", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-vZVAIWss0FcR/+a08s6e2/GjGjjYBCZJXDrOnj6l5kJCKhQvJs4cnVqUxkVepIhqHbKHm3uwOvPb8lRcqA3DSg==", "signatures": [{"sig": "MEUCIQC+h9bVwffoj+j7fJWvcdB12dHllqMDWeP7QdsH14VDpwIgU2yrFiCPy1sn2bQOg21/HMnZMjfhJfDQQ77zcF/LfeA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjo2zVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmphdw//dM8GdCNKocyeti3UYg/iuGoHE+bvBPSwo2aUytzpB7JR6qw/\r\nZWGgduVW/BX0rCCTenSYP25hRCMJ5IIhKwrpy770pVmznccA3luqb+GAi56B\r\n+wSbdfBP8MBH8pzf9dgBUUjSz6lh9a+AfsGCkadT4wwy8MV8CVIr9qIh2zVa\r\nCy0DtMficfH6o2SISN2ypKFwoCmyxkbdD7P7ilKtFjXwAyvnGWXpcqCLxQPR\r\nKruJF8bD0psan5RqT<PERSON><PERSON>6ik25ch7GP17MUQaplqq3Ng0kAqgZBZf4CzFEpZ\r\nTuWcx8oBPz/fBkL7yaEa6DSf9vR6b1XY3nodUlTEeVRC5EMpvvlK3goQNpmU\r\nsSxhRLO+lj04Ea9AELxb2K8A3DsWmxRBJp5C4/tDC/UTbKRdsQPS7/T424rh\r\nc2doBAqaP50sYplEdlGI5oYQ0hHKB/d/WwLnQzJXwXmkTilxsZCshnTVeY51\r\nkS6NIckl4JQBO5o39vGs3ENESPX1lgDMDmvWTHRemGc07V9UZBaMUX+ds5mc\r\nqOHnByqKKVzvq4POeTJ7Pn/Vv4wfAyx/jY5Q98/L1OTW3An7k/vM6fD1EDeB\r\nECIXyX3z0jXA8N0PMl3FeB0x6G6ypp4LJa/Oq3EnZYVqMXBHngShb8YGZx4i\r\notS3weLb/n4UJSNb/VIyn0tMWjui8IuAt9I=\r\n=HWFX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "ace875d92ba9b19413dbb9949cec1fd7f174b25c", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "./test/index.js"}, "repository": {"url": "git+https://github.com/es-shims/es-set-tostringtag.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.3.0", "dependencies": {"get-intrinsic": "^1.1.3", "has-tostringtag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "has": "^1.0.3", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-set-tostringtag_2.0.0_1671654613039_0.9622165880115114", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "es-set-tostringtag", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-set-tostringtag@2.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "dist": {"shasum": "338d502f6f674301d710b80c8592de8a15f09cd8", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==", "signatures": [{"sig": "MEYCIQC+cLx1Ahq8pA+d0p8a61AB/e3q/EAwQN8X/2t7pa5OsgIhAPrGwNBT275mwZzyAl1dggL/bc/GBAul1P4CFXi73iuu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtx8DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrajA//cEt4C4ZRMR9gLey87iWDD/XGJfrLmwEusDhDPrlTZqeF+afX\r\nFMjcuW4NgjN3Psbn63lJfDCOvzvKmK5pe/ZXOzkv3yzWe3VZmmbfrkxeZWyx\r\nJwPI9rwPvrgFiFLuIbRSbpMIqVqfjX+kwGe4rPLnLnbjsP01v/y16mw0R0YO\r\ndoCrIqqXYSGogPCi7zr7J17WiDBEfuApnADTOASOHhhb22GqttVeIW4vE7Nq\r\niWdaQqkTs6z4wZmS3dujGDSltsqGy53XVU1TgjgANZkCZqnGnVOcf3vAP/NR\r\nwRgnH/+Isbdv3gH3YmyqIYZQmDsqcWNn5Bmy6/iPcicGNkQZIDgsNiAUR/GZ\r\njQYLq1KVkBHF2eznlNm6rGAHDNA2Tp+9UWgiyJyBSaY8ffQiZwAouOFn8Szv\r\na2UMOl4imwgwZ88dNgEwg0OHjCvqmJ6UQtXAvpvw2zO5hlLQLGsiTJHRc/yp\r\nLjsw0c56j8IyE2B6A621uUokjsjmMOo+nndSd0+I35uFXo9ZZphWf3Z0rBKT\r\nf+oPT5PtyUVlbocNBhv2J/74m5vjD4OJOei9SCsx0rsICDmqBnKJWPLjl9Ln\r\npUMxaEtNyZsm54AzpnVrTqLtUi1zcbBfX7VsGJ57pbKt+HnhgoSbcddqJurt\r\nNkXQi9E86I1RMlIu2D1dXPUzeTtNYK6Ieos=\r\n=HzAb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "3cb0d8ae33b2982c8244648a412f9af70a720bb2", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "./test/index.js"}, "repository": {"url": "git+https://github.com/es-shims/es-set-tostringtag.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.3.0", "dependencies": {"has": "^1.0.3", "get-intrinsic": "^1.1.3", "has-tostringtag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/es-set-tostringtag_2.0.1_1672945411198_0.*****************", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "es-set-tostringtag", "version": "2.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-set-tostringtag@2.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "dist": {"shasum": "11f7cc9f63376930a5f20be4915834f4bc74f9c9", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-<PERSON><PERSON><PERSON>yu<PERSON>Zt65P9D2D2vA/zqcI3G5xRsklm5N3xCwuiy+/vKy8i0ifdsQP1sLgO4tZDSCaQUSnmC48khknGMV3D2Q==", "signatures": [{"sig": "MEQCIFgDTq2MPPFCb9ihbq03OQZwHydYJjnGXTXmgycR6e/pAiAcaL6lgu7jq9+tS/6truURQK91uAc+k66yxvLoyjrY7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9490}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "4906d498e33fd1a41c1d29295a0bb15ed9babdfd", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "./test/index.js"}, "repository": {"url": "git+https://github.com/es-shims/es-set-tostringtag.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "dependencies": {"hasown": "^2.0.0", "get-intrinsic": "^1.2.2", "has-tostringtag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "tape": "^5.7.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-set-tostringtag_2.0.2_1697866382771_0.3806135199461511", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "es-set-tostringtag", "version": "2.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-set-tostringtag@2.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "dist": {"shasum": "8bb60f0a440c2e4281962428438d58545af39777", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz", "fileCount": 9, "integrity": "sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==", "signatures": [{"sig": "MEUCIQCS3PfjKv2wpwZmR1FLXjq4VKJlbV/bzjDog+sSW7gkvAIgQeXu664upJOOI63INe2TIVsB0z3K2DtrEHwkcwe4yDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13855}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "a06f70c9626850d7ee80516b0fc98b3d9521473b", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "./test/index.js"}, "repository": {"url": "git+https://github.com/es-shims/es-set-tostringtag.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "dependencies": {"hasown": "^2.0.1", "get-intrinsic": "^1.2.4", "has-tostringtag": "^1.0.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "tape": "^5.7.5", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "^5.4.0-dev.20240220", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "@types/has-symbols": "^1.0.2", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.2", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-set-tostringtag_2.0.3_1708460747884_0.02201679593293626", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "es-set-tostringtag", "version": "2.1.0", "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@\">= 10.2\" audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/es-set-tostringtag.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/get-intrinsic": "^1.2.3", "@types/has-symbols": "^1.0.2", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "testling": {"files": "./test/index.js"}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "es-set-tostringtag@2.1.0", "gitHead": "90e506fbfe24630e6fe3871639000d2f0ba09d6f", "types": "./index.d.ts", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "shasum": "f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "fileCount": 10, "unpackedSize": 14544, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRmqFa+JTxgKAgz1nksHw8sQblfewFR7P48hhVyG3jqgIhALdBnoT0nSf0xq5iqlWTccHPPypdv4/I9vTwxHEQpOLe"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/es-set-tostringtag_2.1.0_1735793054233_0.4455930306441267"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-21T17:47:03.806Z", "modified": "2025-01-02T04:44:14.598Z", "1.0.0": "2022-12-21T17:47:03.974Z", "2.0.0": "2022-12-21T20:30:13.213Z", "2.0.1": "2023-01-05T19:03:31.354Z", "2.0.2": "2023-10-21T05:33:03.048Z", "2.0.3": "2024-02-20T20:25:48.090Z", "2.1.0": "2025-01-02T04:44:14.435Z"}, "bugs": {"url": "https://github.com/es-shims/es-set-tostringtag/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/es-shims/es-set-tostringtag#readme", "repository": {"type": "git", "url": "git+https://github.com/es-shims/es-set-tostringtag.git"}, "description": "A helper to optimistically set Symbol.toStringTag, when possible.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# es-set-tostringtag <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nA helper to optimistically set Symbol.toStringTag, when possible.\n\n## Example\nMost common usage:\n```js\nvar assert = require('assert');\nvar setToStringTag = require('es-set-tostringtag');\n\nvar obj = {};\n\nassert.equal(Object.prototype.toString.call(obj), '[object Object]');\n\nsetToStringTag(obj, 'tagged!');\n\nassert.equal(Object.prototype.toString.call(obj), '[object tagged!]');\n```\n\n## Options\nAn optional options argument can be provided as the third argument. The available options are:\n\n### `force`\nIf the `force` option is set to `true`, the toStringTag will be set even if it is already set.\n\n### `nonConfigurable`\nIf the `nonConfigurable` option is set to `true`, the toStringTag will be defined as non-configurable when possible.\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.com/package/es-set-tostringtag\n[npm-version-svg]: https://versionbadg.es/es-shims/es-set-tostringtag.svg\n[deps-svg]: https://david-dm.org/es-shims/es-set-tostringtag.svg\n[deps-url]: https://david-dm.org/es-shims/es-set-tostringtag\n[dev-deps-svg]: https://david-dm.org/es-shims/es-set-tostringtag/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/es-set-tostringtag#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/es-set-tostringtag.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/es-set-tostringtag.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-set-tostringtag.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=es-set-tostringtag\n[codecov-image]: https://codecov.io/gh/es-shims/es-set-tostringtag/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/es-shims/es-set-tostringtag/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/es-shims/es-set-tostringtag\n[actions-url]: https://github.com/es-shims/es-set-tostringtag/actions\n", "readmeFilename": "README.md"}