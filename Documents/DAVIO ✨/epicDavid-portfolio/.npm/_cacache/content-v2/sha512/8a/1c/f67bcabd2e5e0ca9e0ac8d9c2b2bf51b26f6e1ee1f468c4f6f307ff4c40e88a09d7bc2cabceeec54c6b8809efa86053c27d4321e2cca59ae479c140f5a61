{"_id": "@babel/plugin-transform-arrow-functions", "_rev": "116-5afa72487ebacc35f450c9e39dfa1025", "name": "@babel/plugin-transform-arrow-functions", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "16247b48b0e9948c32bed0b1b4e19e3031ee6658", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.4.tgz", "integrity": "sha512-XeWHtX4iI1M781Rq0W/O5y/Gm2+mabtsbOEmzw5gqXDnhva6egaIl08lgpNmL947vT+eQMJIt1Lso6788abFyg==", "signatures": [{"sig": "MEUCIQDKDdPGp6pL4i37gJAvBpQqzqHl3wIyLlzOcNrfbF3PVAIgZvEIz0upanGScjgJ0Ic4pJfSseel6I3fl88ynVoqrSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/types": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4", "@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.4.tgz_1509388470369_0.39648503717035055", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a30bd778be17fea629360613e4e1bb49cb9c08e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.5.tgz", "integrity": "sha512-aaYVyqVQa3kgp3LR6iACqnTcZcr4jZNrVH6qbC8T/yzi+6lGXOp+zD/dRBBYOOTNja6vhO7uLtAPMm72bJdLdw==", "signatures": [{"sig": "MEUCIQCr6wEan0NwJFQnBErRXcPsWmmCen6AxOJZwDRGps+VWQIgUxBcTqTz5dLoYH1+RduRsptIqN7oHj50rWaIiijkVyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/types": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5", "@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.5.tgz_1509396971339_0.849420526297763", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d6179650302257790a536e27db5d7bb302feef21", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.31.tgz", "integrity": "sha512-Cmg3T5yE2Vp7PmbnYKVfeuE1UjARB/s7m8YRq5PRL/du4LrgCpjdmoQSwK9W0UUcci5fEI2wP17169/k78bUeQ==", "signatures": [{"sig": "MEQCIEge8/nrcnFuQQaIoyDwfPPazOaoH7ZGt/Gbi/WH+y7nAiB1gLmp13Wb2qo52o8ZDYCBAwZiqjYZzC60mSVtPpR5Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.31.tgz_1509739400507_0.5721531086601317", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d935eb1780be3d6d1d16e53efb330f81e01a6317", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.32.tgz", "integrity": "sha512-Id82/I8+ZRgQuE6oRRGkgKg1P11eSZ6sNLk20OQQUk3i5u4SGeaagpDiRJ5evgE2gM7ECYfR02yyFmiclOSwVg==", "signatures": [{"sig": "MEUCIQDD/T2G9itL67I7oyAuJzVVzVy+MZVY1ZYoliPaWEGSjAIgXu0hUWqV/3TqqsD/V1LITMOtd2AJlxXgMXjEaU1yy60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/traverse": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.32.tgz_1510493588495_0.6597481204662472", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0f375a2f309ba745492a3e3c461a2d35ebc708e4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.33.tgz", "integrity": "sha512-DUBm7IJRU2L1rwPvrFABzIDRxKCb2pMz4/rFUcRYbdhnqvuiBiDuyA5d9gIX/5BFKwny8nFh6C+2Bk72SaFF3w==", "signatures": [{"sig": "MEUCIQCrbvlWAyNf7uBFKWQZLVuKyB90VgvZl1bK2yPbU0xnYwIgQ/m6BHrlKr4tJxP32FaWQ57LP39fY+FqnsmcunTzgwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/traverse": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.33.tgz_1512138491995_0.19602364930324256", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "441f116d51c14a051d4c00a926a5211be97cf106", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.34.tgz", "integrity": "sha512-ECCEH+tr0Ei5DPglMTR51KV/h1lLac0N06tf69/sKxjq+m+mLiYM2y82AgEFBtAxlRepkBt3LcnVM8AvWv+W4A==", "signatures": [{"sig": "MEUCIQClzVfPwFGW8nZvLJ9Ls6i+bnleDKCry1sbB8MM8JQeIwIgZmvd7yNpq6U4s8dHc8ewoc86A0kxVQhpsyEx+xjSbTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/traverse": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.34.tgz_1512225552366_0.3311201750766486", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c9764114c629bf1ef0037339b1b05267a5e4aab1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.35.tgz", "integrity": "sha512-PO5ZsTmBtazAfV+Q9KZJPCunmcJwyKW4YNDQuxnnPpiLx4naY+t2zcelO210RoBxTr9IdrjLOXurT/+20wBJTg==", "signatures": [{"sig": "MEUCIQDvaPD5eQ5OFkzCQDKXRG7JOKlnDjOdV2L2UZTA3pV0OgIgCLm66IXSu7N47p76Hzzmlv5lv5bSvKNgzbiqfj2IUZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/traverse": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.35.tgz_1513288059640_0.3783845619764179", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "337267be7d8ed032067736ee5e76fe9066c45d4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.36.tgz", "integrity": "sha512-lgOyrX8gPHo0rQzzfzGx0atocEdKgvq3jjIN7XBkDMW672ivZwrz8tAd/NJufNK6mbDUZm0Qq/JoLf+C4S4YpQ==", "signatures": [{"sig": "MEQCIE2HcVAqU/8sxG3L+5LXKANslsjNiOoxxhLAkUTHLC8lAiBI5kOFKLem86FR0cjekoG1mF01HOpdotCJFR2PYC5fZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/traverse": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.36.tgz_1514228669266_0.39130751183256507", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "864143f7893dd892b687219d450db5d3d2e91ecb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.37.tgz", "integrity": "sha512-I3g8EnCatGKG46ZlMIYPxE0mqswLi3cxx+68Kp5XQsKtDP0MOOEXqILZvAUPu1rrvzbpC0USE24QW5uJtXUXzg==", "signatures": [{"sig": "MEUCIQD3rOo8+z5QgURX0XyGYfGMzHReFD7w1DdYMjubFxGUOwIgSEBEfjlKXQleWrGXoGOtDOVFRae4jus+rAYhZjjwr8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/traverse": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.37.tgz_1515427346322_0.05390045209787786", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fa52742f2bf664ed0d56a5015b330880fa6646ef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.38.tgz", "integrity": "sha512-MXh/RazPii5LK6E2ve3+mmeGJAcNaUW8SwVh1a5Tt92RyXbTsZtmFz0mPsVz6Otxx/C/cj8gahHfiuBpktP//w==", "signatures": [{"sig": "MEQCIAcCIoFnp+VVNwxPRPLKEPiPqjZQKcEpXGjuvrnjHs4vAiAQzDitADGv4Zqb9WgQ/TGEtGSQbzyLSMDHLUS+Y/wh/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/traverse": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.38.tgz_1516206708105_0.975541174877435", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3e6b577392e009816c62a42fc97af10a95760a89", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.39.tgz", "integrity": "sha512-KHb7IUWC5m46+1EQQDdsFwcNBKq/1Grx51FjPX0OosO5N8gtel6O9CW6hkVUwoEYamnXGV+0lD7/cmEjvChceQ==", "signatures": [{"sig": "MEUCICo/wXX59vDxwj67ovFDxwev1R6GkkP6wJqBIdQR+ZF9AiEA/Ig75iLVL29BQbk6tqp2zqAtf7M56DK3eSg0sn/E+d4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/traverse": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions-7.0.0-beta.39.tgz_1517344049970_0.45872423821128905", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0842045b16835d6da0c334d0b09d575852f27962", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-B6wh62BErLWS3XInOUHhLcqBSK1QGdBph8E2K82EEFgJdQvphy30QXb0vwLUr8YU1efYyZXTsRA0JZ12jcm30Q==", "signatures": [{"sig": "MEUCIQDAcju0fjSDyKbxL81Gu1BxaBohmdHHwxpjrv9S67T2/AIgW5xtZOUV8okMNMaxRLl89rifcXn+Fs9Pg5BG8/YBJo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3615}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/traverse": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.40_1518453689072_0.9202731978153997", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0394aa76c3d0aa373d9085ce15a666ea3ff9d3b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-9XrsKJ0uuVqyiQ/gcQQNnAJrphixxRENzjOuA1zBbSWsquxvfg0JK7XLzoEQ+7HizjK3Edwu+AINxM3H4aOdSg==", "signatures": [{"sig": "MEUCIDwvwVqrcbYCsxN4N65U4xTf3rKxLyD/flhNyvEaOMJOAiEAgjkddtMPha7u8oKzKefMeoc/XeVqGqBJFBkkW0LQy5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3847}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/traverse": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.41_1521044761164_0.42288789546887795", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b918eb8760c38d6503a1a9858fa073786b60ab2b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-/tpAo2Ur8m32U9pBcGQ6JKplWNEh462zxCnwVKL9yVwG02lttC4QSYBvreRK1wBidDz8JgRZFGGeB9N4l23/Sg==", "signatures": [{"sig": "MEUCIQCuOtoQNj21Z69FLgNufwZN1cSMtuHKJXRz/W12yliJDQIgb+p8sdyAMEA67ZkcXYcNJ0zmE5pHGPS4vfC436ve0Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3847}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/traverse": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.42_1521147037838_0.7287128065856878", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5a4cd89f16a30c92f9ed8c2c095f402ff5bdd2a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-cOdFl3CbV1W1U/R6Mgc2LorJq+ezhnCzyFb+7yLBT9EjiRahpnGify8SFmgfUZyXhkKjxzKUu+KfebVKu9hMHg==", "signatures": [{"sig": "MEQCIAdTGLsairPKhCZfKMvuteIGB7YHNjTeidf0lR6BfQ/gAiBQVKNAo1EbXah0KZRplRAKGbrXI8Z4gJuhIZmt08kmfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3955}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/traverse": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.43_1522687700946_0.07823043118595896", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "718dae35046eca6938c731d1eae10c5471c17398", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-EnE9WACf+ZQZcLcmT26PEaIs3aWhr6shLJIdhaqZavN3CitxJvfia1q8WyCS4GO3wIdopgdeIpD2Xwe4wmJFFQ==", "signatures": [{"sig": "MEUCIQC0kSmJp1Z3nSuUtQX1yUOt6AzVpfGtM/K2mvx6c6/XsAIgNBCH2X9hhzS8KjJwrw/oGTcaTcil8YYd1ZFfrpJUkak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4003}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/traverse": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.44_1522707603117_0.004719103791003931", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "faa5903bb9e136fd421e98a6727dd2afc3fe884f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-jdJ21t4L6ia7ykd9ZTTR2VDoOjBvjC3kqOHqqXFCjqcecdue7K8uF8TC8vEVQ8OrLJgUjvbWTLayvmtLIWANRw==", "signatures": [{"sig": "MEYCIQC65+rJfgcWo0jBtnTRQp5jTJ+NBy8wSjH4zgyG+1XYmgIhAK7hEA+DS9AeQyHZ2h8K1HpEfRfenmR+j9IFypMhbrPo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1VCRA9TVsSAnZWagAAS7YP/iz5T3RkttT3AcNqYbqe\nwscgo/ypq8G06vidBpEG5MSe/lIvgsHXqVvQiYJXSl+yW70lYlFjYACEREXS\nTYWv2eqVKJ3AyaYIyWog6fs4uMClsiRDkbOkpcp4s6UXt4cuBqMtPoqlC/+W\nYM9Dg5Dwjgdji7WQr8Ks5UQSLVuIMNS+hvHcZXYjwE16YKcDNNB3J0npb/VH\nqsgXIYamOdfSbjXcCtJ0mF6b0DkZmTB18O2oB7sipC3zQVmDwHigwmfeYIUJ\nXaiCs9ugxbjZqiY7nawNOxoD8Kb/UIgnVhYvI9eRYBxxc04bW07NA4zAsrXZ\nmZVjmfNajRNnGGzqQEjF5ILwAzVjpCjbiMKCzCgqYZgqz8u6jmsvB0ycFZNB\nPEmS/XUVPfvixuIHiBv/jXx2QPCjW0UYN5EqGnwKCULOqmElVM7+mNzaUxAr\ngwxJBR4iUbjy7LHaMucw5cfYXKfMj34n71pMLwUGgLbXIan7vvQID5eS7bIo\nRfTte3tS+fKwFK4zmWFsaBBMDRWDiDS5lua89iIWINP3xASaJB8awgS+NLcJ\nR+Z633jAuWJ5zPFb3XGS+1JbzePDUKvLaL+2y4XqhcrRi0CJALSDTZY2KfLu\nZV1IyGCErqCqUQmbrUfI/KF6x9+dcJ42iSTHoxO7pQfBRdINymt2Oq2U0Id8\n0zF8\r\n=Npc2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/traverse": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.45_1524448596980_0.7610645842643344", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "130e79b1d4508767c47e5febb809f8dca80c05f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-GgeFCCMHXWRkPDXWKin76qiZh+DAYdQShmk8SmzDj6IAgPHyNqkxHN/8gsmNe5/7IWFFOKUuM9TNU7fgY7z7Gg==", "signatures": [{"sig": "MEUCIQDTORzSO161UQpXK/BL+h9So7Johi63qlxDu/TpkaeC1gIgbSbAr1NvdwCAjjEIn9adtk38DriQTfK8L6KgSqMMAf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGDCRA9TVsSAnZWagAAFMIP/0nG1i/pNrmdU0AMnZhG\nik1193pN3WtwFpIV2Yeh5JK3Xrt+3RpfkwSWfHw5yG41l/Qpx0rrUrY5d/wj\nLMkD9Dfxfj4m7XFNxKekkswVMqwONKi+nbokkjqyaLGeev4+EEbEp3jGNzL3\nPoCBGUpO26VBNhxrDZsslf8SEU3kVgsqf6dp8h8MsZ1KotPcPZJcO7i4od1G\nkhm2K5hGX9RZ3L1X3cuCfeaIJTA/SZaNZNsiHFZd/6QZbciqYSCFQnh0BCFz\n44py2ymeiDdtPIudnYUopgY/YvMkY+Xd0f5uRgL86p4muoa8JWOtNrfXJoW/\nacTpOYk2QuHzepUosZIRVCykSLo2t4R3+qxxInwObOe1DXniXca+yoOA7Yz9\n78tQwFrDlxWOVX2HupLhS7+E6Ll4kdqnHArUWiJ5vxu1pnK9EAuhNBBJ/dfW\nPy3vegPHiLhDc2/ueidd8ZEtOiFNvJFoLQem7m4/yDID6Q6G1+3t1sxLWrik\nNzYa+LJzh8XRSlQ6SLqGeSS4VSAtlZqibzDYXVBfy7xvAxIgc6MVLu8DSv0G\n2CHpcNGCCRM5UrcV+DA4vzCFmV45tel2h1+iMgX/8dVOBzcRj6NCveoMOInw\nJMbfudMhkUsCvHmquw2fz3LZ9rWvr1T0gqbOEUpt/v3J2WsQq6mO/4yMlKGI\nqMyq\r\n=wNo8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/traverse": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.46_1524457859346_0.5269007746244827", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d6eecda4c652b909e3088f0983ebaf8ec292984b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-xiU+7RJAsqx+iZqWSQQWBu9ZDTruWimkg4puDSdRVfEwgZQdOtiU2LuO0+xGFyitJPHkKuje0WvK1tFu1dmxCw==", "signatures": [{"sig": "MEQCIHQzHPuhmXTliCHni2tnxBLETS4mP7doGl3FZUlaDmDYAiBSeuZ1Hu8xwqXbGb1u825tixlmlb77zD1cgP3W6QbJ/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUHCRA9TVsSAnZWagAAOi8P/3kkP1+sD1p2lXcDR1Mg\nxFOU8g6iQLJS7oIwVM15EpO4Xb0mDaOlj5lmQWHxcUSxyjlLOjWoK+y1zIBJ\nl8TTutbpJXmof5C/eilXOnjAbpVDEOXawDvOpsYneI071LBwcAflCcJThsQ9\nyXm1Fdpros5MK7KiTbXeREjr+4etRQ5EbqjYgCsWnSu4WKdZDLP9+V585C8y\n1qiR9t8f1CsKZf63ushsImDmUvpj0qGev7zPHAcePmTlZGcVXhsgdbThCGRa\nP2xIQhldQI2sz2iORsuMAvQjSELLf+y1R8+P8p+UQyRRvkVS+MaD2/HK8S6H\n10HcHefSU6A4X+CTHS76vcKjdAQdtR3inbC69j6WM75Ju4bRSHuIgw66aLR1\naCCsGoqGkhmZuz/h31QcHv0fEE1EShvfZ2QXghs3/Ldr+91gM00VBLubOZ/8\nTSK54P/hZSCcJkLtmFLGaWA/c4sA0XpuVZnLM6YIZcV+8z5Xr2FpR6ay/Ptv\nQkVoPNwzZXyT3D2qBYKIXoJ/pbS05u91unaFZRZn+1XZEDUjJJ9CG+ofbC5I\nM4KVw39oPIP4fuezao9kPGLDHoHxd6nFkQkcfZz9ON6SsQaps80cqDWEZI41\ni8tubJw/KcKKPZRB4NPZNzth1mHEOFnDXfLShf7M/ouVxHsdHatd/tdb6I/X\n8p7G\r\n=AcX3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/traverse": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.47_1526342918745_0.47358782424147794", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d2eed4bb9566fcc06bc5d1d5f86821d2648654aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-K4HCBSY369QE30JVMhDRubPD6uK2aRtkWWQ3IXpZgs2qmfKz7U0SM/NZCnOVYRUFqwmkgiCMEg0ONMCfuhHzdg==", "signatures": [{"sig": "MEUCIQC1A6gvq6qrLAkG2/enuyPAgT8yvXMr2t4AWWlLt++8OgIgVV1EQsz3+0ILlDbr/ZFO9UVQ5EwjWYeQisAtWOmkV/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDeCRA9TVsSAnZWagAAX7AP/196QSOqVs3N6ZT8KQfd\nugSbYDC6wRYwvlAROjGI8r4KPfDY2U0DFB7WfquZ92IphJMoc0yPvmRJgyy+\nLwGTH3U9r+d5qiEnMWR9zUtqAzwX/oeEx7lmbFoTs/l9oUH7UIOoxUyWkId/\nIHcdB2yPapTCZseSDVRHDS1ihCA6RmzzBgiBJ8o/Ehk95X2hB7dvIK+jgTYK\nGwnOF5s8NMtDUbmTYyDwP7BKcBpRdadQp6GzsqdENOH+M9qLrE0xnzkLF5AM\nUYRlLaEDxHjkxZOaHf8VUvPJnU4JuRIE+g3HZITs9F5QTiAKGJTEURaQ2kyC\n7Hr3hvT3bmcLAgw8kcYNCQqHxqUNmAGWQ6FO0kHhaDUUN6qtCdtA50uj7WHt\n2zLoJxgej4rvlVO3Cui65Q6OADfAK94bu0W3TNwMlgZaJAbc3bGem17c1+/c\n07v0O5XCZVy3BbLzUdYFrLcEe9f8rqQeNNvcZYtfxp6Y+rJpBu+RWEOX60SG\ncjIdyKhaR1zRcKhQTJST4P0O1anmA19jhriUBB9iyO6qK/9Oo/KVBDsRAmN1\nQNnwvaCvgNecG3BkVHTSWwxZY+Mr9YAsJLtHX+8FrJq8LL4ZjqLOmKLjcdex\ncXk9VEnuR0S68TWHhRaILrZ48AzkDrZll4McUIP/USE/okyYMPijUxYVHE06\nz6ra\r\n=inKq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/traverse": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.48_1527189725574_0.8728223586896229", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dd3845b63c683d187d5186ee0e882c4046c4f0e3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-M8VhGMndVwMVXfS9mKBmfsTEeiXNUZHoFi4YFi0cEcf2XqugnWA2KD8j6WLK+jdArfPhnfN8DpkRPrJKJVD6qA==", "signatures": [{"sig": "MEUCIQCRgEW2vIXTgDOfAhPTEGGo5dAnyNErl+CgpnKJXAEcGgIgeUhJp6HZTnmMJ5VijFimwON5ejR06l7FFiASCj2AhFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNuCRA9TVsSAnZWagAADGcQAJMXa+qIpU1Vy2dO9ePw\nJRrzxYGqlPifi/6oH2tEq3XvOKdzPVa3IE0OmXDDn7zCeAB3koO0cd2tDfVx\npxWSXWSshVXQEOLmkZ15FyyadbzojQCnruHllREenTFcQMct01WsoiT9CR5K\n7JMsF07UNXElrHDz2OxO4FQwVCOAAW8xhtxAUyyvkFjvOxYzpkulVK0KvNkg\nb9zwWvN0BohU5GHrgwSahdLC8M+XM8C2vvv1/UzVHTwv+N7sSWHbzKIXWMdH\nHBj6eSPRcMpvG0pYzTCgnwgYPGJIPgH+loI0he/Rd0u25tJCqG3jJgjPLNKw\ne1HAJUkqDb22NNV4GBGNKPnCskwZEwBA4Led9pr1YcXyKZKEaHA6acOhkodF\n/7JKgWEYuzNxW0+HDd7ANMOTPKWlxuYga51CV1BNCYlX4dMm+vpbD6aClbgA\nqbomCFGWa5kDON0ya+J6dbcCMU+Oiplqcftj5MOguit1ZRg1AxkXl7aLetIh\nWFJmMoVZysAxq1VTn82javmxCzSsw65EwLcVnyud7h8+RzLhfnXiFFnkTJH7\nWPwXZEi3ZK8Wxem+sy2HKOm/9U4S/Tv10oyANFuo+iD9EAtJSE1u/zH9Xk6R\nJ55NE7qgOsQEl9ukv/Mk+YTIsLaE+8IpcVfu/DySgj7p7S6B8Twx3kL4x6sv\nEKYw\r\n=Zpkp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "dd3845b63c683d187d5186ee0e882c4046c4f0e3", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.49_1527264109764_0.21495164587910898", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a25c5aa94c79cb5d46d88478aaf6751c655860c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-4PuHQs37kP94uM29XhdZJYLiAR9H4cXXbVctaVSm0YDLxwov9juWzYOmN3L+r0pXyEYxX9G2rTIbhMwmNTsG6Q==", "signatures": [{"sig": "MEQCIB3bBAYCLRGOS2HGl/xLNMPJI3KpaP5rHkllhXExK18aAiBX/+uXvwoZZFka9UQDa0BMewpQev1aLJGF3JzePxsdiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1751}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/traverse": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.50_1528832832575_0.43046502589176394", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "29b9db6e38688a06ec5c25639996d89a5ebfdbe3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-4TNaj3qNqoW34HFnz7MfWAmRRTOOpDI26kIkgBZpish8hD9qt1gRS7q9f1mH1RbPxOx/eKBMvvm/5olbWsTB9Q==", "signatures": [{"sig": "MEQCIEwsh2EQZj6CqZzSGVmfV8EgDPjTPyLpYCdVuT+a53PDAiAFp+PA7GEnTAeA8Jf4shErL25nfmthSwCyIgtcdtPhuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1765}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.51_1528838381558_0.6855879806213292", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "85e7e84ccf065e7292ec60019ecb616b360cbf18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-le93dDx13ANecfYD4+3QR0WrONB1XJfIOjXXQF0GVKIcY832TILAMJ+j2iCXxatGEHTDpqIpphkU5nnIv3BZFQ==", "signatures": [{"sig": "MEUCIQC5j0vr0/Fhrzgvc4se1Pz/Izz/mgtMEDewT3SbUEMgyQIgLs6u4MoC3k8cqWVjUe1n6IrVihsQlzPIPdeMJDtb9DY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/traverse": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.52_1530838762679_0.2781225361378752", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a75f5fa8497aac1729d033bf41c250416b9d1e04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-CxYIFD+eutA6WzT16fsxGn8Y1A50iG3JVOeL8MGn51h42E2ea5xvfWnT/aAthEuKqbHR+FDyxBZ7o2lXRMwAag==", "signatures": [{"sig": "MEUCIDcH1c0tyiOR7JYbXoE6aZMZBo5UGxgnKMzUaObGfJw6AiEAmuDRkax0VJxoazzynf35Mm4ceVdAqlZv2BnWUxwo6F8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/traverse": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.53_1531316412545_0.3764264793214267", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "44a977b8e61e4efcc7658bbbe260f204ca1bcf72", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-mih1xQ+LWgta0vQEU3SFpW+cV9rjJP21jBNy6So8N0ZEfDLOAuQDQCYyC5fz1AQ0drIb9YbSlgee80ae4UUziw==", "signatures": [{"sig": "MEYCIQCZyzNr+ZeF70e7pTklVsU65Zigg1VG6Bq6gUkLv6OruwIhAPh5o8z1SM5+P1jaq0O30jeIbW4Hj7QceB7HBJid7eMA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/traverse": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.54_1531764003314_0.7824348365791429", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "eacb446ffc67e5135a4a29ac72bffac1ada181f6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-rWaiEmEa0NoWJxInQ1zWv3aOSqjZt87u2sEihflgUx6xSP81+65D0hHMZMugD/SIuWGRmcVHfTapqxCdbt11nA==", "signatures": [{"sig": "MEQCIGjxz3zbgAlSiaP+FZstsBnvzyxA0xIWHPu6S3SYC9NLAiAHAvsaJu77HdHVLWnhNxKaEkfCTPMk4moG6ADUSpkP3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/traverse": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.55_1532815631015_0.6696728156396519", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b547c40003f6795a824c3af501dc963acc6b2492", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-TkpqdTt8ivvNBoawwxwFJSHRAQAWvWRuMyQIJfdrmSGdHVaEJ8xn1MkYuORMOogtpsG+ZncmGRAyCEQeMFBPsA==", "signatures": [{"sig": "MEYCIQDxKoHz51fdyGtwWjUbdc2s/rmqf+lLXiSRSKNqWOSXJQIhAKrnz859SHaqpnsrTWqM9MNeHFtjUn3yhuxdiMiVp/cx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvECRA9TVsSAnZWagAAz5YP/R7sYUIA8/mPWJmevnag\nBADlVPUJ7bLQlDPBzl2g7b8j61ipipVSXGv9NLETs9YQFdZDvTENKQ1QlbEz\n5eLQEZKLUhE0llMQi92B7+d/vkZ9wvPHUNmcyJts8BukBrMaXYUmNVFFOnmI\nVyxh/syHUffe81814a/F8lMWXWcsxHrMEtvXEVjo9Q+rpBuWWMyPvoNpKKJU\ntSm17hJKIzIDMf7d+29MegSdEKYD2BWUco4Y9UohX4ARDUarld7ZSbDwcPEv\nuHfW3c5/b24BBK6X7Uo4dLgJAVxqYWE1lFYmctmp94yI5Bk0Fb7+jDI75nji\nqQUDc7hW3xDbK/4SEW3K0AcqjY27U6+F6vu5Wr1yy4OCRPz7K5hOtSE926BN\nnu6pqbWLyJnRO2n4duRqzPLvY5KUItKoYJHxTOa+3JrQLdXtJTj5Kfq18Yxw\ngdfMtmcv2HhdbLzmdIa9VJBegsfB5D4zZ6/NkSVWYAi/7VejcpDCTjjBlOC7\nysb7PBoQAZ+T5XUMM/xxWvYA+JS9LOoQFnOmWQUu7quUpky3663/t84s8j0Q\no+Vgyh4uISex9QVWSw+uFs5+Ujgi3MGDDf0VHDUtlhtufXxIx94MCx3+uUEi\nHtKivTRq/orYJ/+8xnDmgO4geuVywA10oyXEcAnHiaEs3TDuSnqkUEMfwE5u\n70P/\r\n=QepN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/traverse": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-beta.56_1533344707756_0.39165738435977837", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4b6e2fa6257950bc3f4cfe6d5080e959ae82a8a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-Y6WienZ+sWuNbj/FX4L932wyRTzA2tnbciqyUt5kK7gRjhGxHE7Warq3Iho1N+GiwzFd3H/6sCmUq9Z2l34qew==", "signatures": [{"sig": "MEUCIEkFQ2fu1Y7xvNLrVTrdB2xq0s0Vl/M/1sW2l3qA9499AiEA3Qbo7mzcpVUtiHGX8VtlbzwCYVW99ZWZR3VPw3OgBJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSKCRA9TVsSAnZWagAA6xcP/1gz04XdiBuGmRyP39gp\n64xgHGx1V4EFEe99oTKR6S/IEpuTZi6gbIs6lxcszpKxL8sXapWjmYF7kMHR\nwJOVjymbC7zCpyyOGhJ0AQe8YjtROZvdplnweZii559PBKyibPx6iUeu4wDI\niZ3SjIoqNtGDa8DCNHivvVDDlsO0h9XYfEOIA5Pe2+nU8TdkPnD2c1i1ki06\n10j6KR63JhLJHwdRiCNGJWisQlvLCnqDfXVzBfIgXCS04LoDj2LCclK3/VVd\nFoHUrE0doJnSv1Lqa2AiMGFZhWt19nhZSHSBD9LXSEZ+3Kg3BWHVGQG/z3lY\nncnbG7z7wD5RKK1Hl3zVRtHDF2ZTHbXhcPn5Ne8A0vtWn5iUEwhWc2dsT8ZK\n7TFyZnfyKfi8t1Ajq+4R9Xep+e5fvaqLTUa+Wuv84qh05vYdOLrEwe01G/3c\nPchPNMdtz/EtGjbu9S4lg9LOPIX97CN3J1TjILJ03wxRUEsiK2ytGMok2TrO\nC/jqKeCtmCoGUNkv+SAo0tZPfZL5AY763frhnNWS335WQ7XMQARPPhetw8lm\nfsFlm41iW1aMwSbBJ0Td5dLhydbWBw6R9yNT3eFtYjtkukqIiGGRCG9X3sFa\ne4CycmNEbY+ghL2rbSFKH5R7yX/lL3HP0VoXLiC8F4ewcQygeu1swWLXUcV8\n43OS\r\n=WFzB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/traverse": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-rc.0_1533830281993_0.4352537376643817", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "95b369e6ded8425a00464609d29e1fd017b331b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-9JnWkl+iKmjNgMFrLjfGJQm3f66SJxwaYjdsm49Vpvo9x7ADHMGMZYa5Yto9WNQBlIdtf+fhypwBcz6IPxdyvg==", "signatures": [{"sig": "MEYCIQDGjqbPUVfYIIBhyjOIrx8HXOKZqMx2kqzsLtlW/HynKAIhAPgIU3AtegdHLrEfj8rsO9ZiWPX/vhb7/LXtyUMMLY65", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8RCRA9TVsSAnZWagAAf50P/jtPijuVetKyoKDVhFCR\nQMb0RjeYpJyhUKdYK41diqxZMNcXMCznVisWqYW0HjHfAwgXbyIrvePhmBsq\nQQ3c4Xr2jwn1mFrNbizmrUTPCQtxOdpfuVGPGnarjN7IUbluaKokSpvod4KM\nivbDgOBAoyB2cJA7oEp6EFaKwk60EqcRXC7tnnKe+waWxowLanAIOhfzBVFa\nojtmOFjpUhVvXhawcVZAW/iZtYYuehzFeW5d3G3yyj9hgqx5fNm6zUvtRqb9\niedViw1txHe66wuz9OpO6ZHpOvkAJzO5Ol2oAoNRF7IOdX3Dcquxtj6d9OrN\nx2WRONLHUKDm2APOwmfxsBSU2nHef2e9e6BEvIfn06VjRuXItZv6BZAw7Zve\nTISOeW1N64J56QayzhSKpnwO60yr9eDjjzi6PiDEcuhfu2U7QAvKHxgWSgGL\nYfRCCVm6CxVWUtGTuXU9RMCoIZih0+zr4hHFTik8IVXXN671+Z8A2clbhxl+\nj9b465nUbr6PYnV7wjrHJskBIozYOMfGXxbK0MSUt61LB4hiC3MXzOcPsjLV\nSjp8Mopu7QV1TmXsuSoBqQeLN8u5lIyxkJYpZ+wSFz59CvhLbiP4jAbrgLba\nJtrRUPpCL3ITA1SrhK4jzltoUlewQ0IW3PqU+WGE1PaGk55Jky9W2nNXH0Oi\nOr7s\r\n=Z4Oy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/traverse": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-rc.1_1533845264678_0.10964251314573015", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ab00f72ea24535dc47940962c3a96c656f4335c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-wCZZpEfjPBiO7kRLzLLxcaImgHzlrJKjl5pHpTbng+btsCb53hNgE0dm8ph/YRRHv0NdGej0Mo3+kKNJHARjYg==", "signatures": [{"sig": "MEUCIE5VkYmMn4aC1mQiV03ct6YxzhOYxWcXO2e02YKDsbr8AiEApEJOdO05tLVmQQFQa0s3+eNtpWGCgFRwW3Fh3UFKThY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGa8CRA9TVsSAnZWagAAAWMP/iRPM1OBnQ/IsfEFpaPi\nA0Z0DNCimPOtB9HE5Wz7QkyhSmMqSANPBjBvTK7HPzTom7Nb+K7EXZjYhEtp\nqD87tj2cDOHhOQm6vQiBPoghDXaLp0rkeVMnAvDED9lutn8sqjgsRBpFCBGX\nG5qvjNv9muu3wmOA8m1SoiACqAuRXviIOBj8uD976II6iRpBbsiFSQ2pV7wI\nAuF0oHNZing+A6izmPdBiCvxSJnpxdmslcj+XtAHo9AtnQg7KhJ70uFK1jRB\n/xLWOiCzLUiz3zlnnrqMMIleGf5gb2xVxFLrjtuKke1AWRXKlcnfgTpCW/5S\nfsiz9ik0ABlIJJZlItZiGAzX5G3zCBeNDhIS5mq5wiNmL1L7WlG6zqOxatHD\nYwAfTk+CVTb/HWmVyY6hKWJAVG+5D9fVBP4vxh+GqYYpo48DeQa5EiH8cvQ5\nQfkc4V5sYJIPFIoQWTA1WgKH0LdsplI8ProNEkJ9R/EUh+vcNkfbd3C/QYf7\n5WJu/im1gaDoUZ5/2wT8sTLNKCxAqrQIDw48SZ26/YGSDw14kkCH3yGzzaHp\nCq4B8R6LMeK/PFY57KAD5cGgyaJDuqrfyOiMDXCF+9smvF0iaqEozHCJXEUd\nxZkzDNL+t63/W0+Wbi3zSVXrOGV3+SW730tv3cVWghTsGIm4u1XWiy8L7rGl\nj2LW\r\n=vXSP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/traverse": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-rc.2_1534879420471_0.2742764353967828", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e6c22148d0c2873522a2db8e21ba39d9db188d74", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-jaArx76fZji4PHU8mhSGyaN7e2kv8et+1wNrZrGp4RE4/e9huQ3QOnhryBerdRHGAvhop0ZpTdP4nS64p+qZqA==", "signatures": [{"sig": "MEUCIQCNSUrcEMT0JzNtc13NHjmHgvFzTErMbegYSxRlMa5PEwIgB6CJb+XdsVNE9sCvUp3xNbrYZoXl56m4/9iY5c4D6y0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElqCRA9TVsSAnZWagAAZFEP/inNe0Bu2zUmSYQ0j5Qe\nnPuD+ZMfESvXjTknW2zD7j4xZO7vQUo9m5BMhdTTfh2gkP1Y5+ls+LI3CBVl\nEfw582aEoPujXDuzrJSqN0JlpzYf13fC3qNzat+aP/1AbLp0x9TVpusxILBs\n939MLeshLyEjQXeukBEuV16SLRx+ybTrrAe26AxASKv8FU7tR6PY5GZw2jM6\nfAs03ldpTLe+uG1RSA9qk9P/qSSumQKeigmAjj60eeeIzUEBGXnDzdDCMENx\n5czvpga9dYhXg0gT5O2r7L5OMpwi/5l/ghZxaGW378O6ZmDQRgFtHHWWYnAv\nG33r24C3UFia/5RcC7wVY4pqj5w/cQoRUgSDEzQdgsJeYKAaqwQjMRLoCHp3\n5n3FSOi1M1KAGwdhisdh1lB2mZnbb+NatNieNkKVT3l1joxhq99dXXZ2aRCM\nJkl5F9loR4O8eGgu0kMU7vughk9HIEJENs50Qn/Hg7rhm/BYfn4UxVHuLhLw\nXa3z7MMBi21dQ0BVDdjGHXThf4yxCWhbXGBzbBSMXbuiKbJ9gxc8e5jMWwIX\noPgiLlk9Xnaj1PInB+x/wMd9FwqBoHag0NBoDfE3KLfF6WD1LyMPwn3k33/Z\nUjhKA+mLpyRZ1DUmXfSfP2Pu2w50YfFF+u+Wc5cQ/YOIeZw7d8hYEEdw80g0\n+Wxm\r\n=x3R4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/traverse": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-rc.3_1535134058379_0.5422010312762768", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "71266895545e92f966f316181932bf655783f5ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-5ekltzPIYDkrba11eqShE0cUCxmepd3C/pqqdVL9Qns74MPmF+2BQ3Xcks4MWqfuN88+QpgjgagJbzftcyVU0Q==", "signatures": [{"sig": "MEUCIQCcGYOviWgPcgRNCOaBW8OcobBoZ7i5c0ewd4LglDggTAIgDcAEQjM5QdMM11tARILTkk+/lLD+fvJwBbLck7J31nA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpMCRA9TVsSAnZWagAAW1wP/iWJOImYiyvplI4rWDuF\nb6gDZrUGpCr70xVMSYHpgDEGzEwi0GEcO17oWMCP6HqhaRM7Um6B1fcVclFO\nJQ9P4bqByofrtx6khf2NRCTNBkYYJIU3bfLq0JF/L7yrKG9RaziGbj9E75eS\nANf5shDeikRzD9sZitLTLB6J29S6ZlgYaB5s+83KfOvyld3JXzArtq/TPdd5\nX0f5K7qqxXxTwRu9nBRpZ2+rJ2+EPZ4gdRoffAtkqnvGimLcnU+CcFxHXo/g\nICzOu8PpKVUkm3QnZJCGkrsaGzV9TJhXGkbYOfM3lv+MnmKMg6yaozLE3RlR\nd9s8nPHzHEZqM9U5GegNV9UNwwJmsAU3U7mDun70RBwAGlGXk5c8kUXN8jfp\n+iSZ8D4kicI6IeCePU/Plp9atqOB2a/P0L107GbV+Ayl2lE3bf/tSBSdrWZP\nKiih1EK+IF/C+DPSFv4sK77FEMA4LAwgvTsGhpCxBbOvzqNfcBdshbr0uRKD\nxZlvF5JOAQCXTo3T/IcyJjxVXoPfLD/uJbfEUjsjalqRf6I8lg5a6Z2Wuj+w\nWbChaGDmwxavdo+kicC/ElBxbJ6LUXPsDAJvxXkmZfQobD4nBzTa4w+sKkfT\noiDghaxoNQu+OHr0VqON7lJkiAISwkdM83G/AqgMEjgFYNDI8l2X78XFwG5T\nxwqA\r\n=F4Kc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/traverse": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0-rc.4_1535388236181_0.07469206200655076", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a6c14875848c68a3b4b3163a486535ef25c7e749", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-2EZDBl1WIO/q4DIkIp4s86sdp4ZifL51MoIviLY/gG/mLSuOIEg7J8o6mhbxOTvUJkaN50n+8u41FVsr5KLy/w==", "signatures": [{"sig": "MEUCIDTrSCt+vl9xl/vgpbvd/OrmcOJYrj2uHhWtSujvJdAaAiEAh94tNvHYoHf3r+HkbZ4cdP1T3dLrT2GBDqzgNweAGSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBgCRA9TVsSAnZWagAAqRoP/00llCdCL2i6JMuUYPFw\nEpakZWMEDDYz/A+El4qOTf1u6B1SOgSoqcy+7OghqqaFrNgepAbgqHyBCq2N\nP84A/xlUekGAR/sfN2rKy0XrybT2UXNINLiCshXxJShoO4IU1O5ggGya3UF3\nIeAAmWbxPpeX6zkn5SXYWqHflsBLpsRTWPoUOYL03CLms80FURqgDflKS/7F\nfAc4L6rT5BC4DfWWiIs4yDKcH8iRLTbya0hbghy0pNRFTO8Z6V7SjCI98JcA\n/iqGulZmVs5FtUgAkFAfrGorv9HWpLrcM+OEMZQhiUtIYpj+FQmdSIZpmeNg\nntxzehczjIVh5n396kZmHYfSNZaRM/ByGGMR+BjVauNiGaqj823iGF31NTTu\nBa/pg7Nxya53riadSAy8YwerjzxA6KwySEPth3/lE1VGX2MnSgqpDpmwKfWK\nlORCyYs2kiRThnM6YTBTg0sMV5DQJTRWMXq2JAn1d9wueDybYiCjpUfqF14G\nhW3RLe5t09DxS7uvIZyBv33pgG1W/SPATMhQHo01n8XKsn9vaRl8Q/2rgCzz\n3yUTdM0qkD6P+gKSQuM0Hmb23kEJTvgYVzo0TgSKr+IJW5CWFYvLiK7HQAgV\nAtHdAY8hYniZsOoLL84fM4dtREN90i0GxCCfO4Wb4FaCFvhawk6Tww2kTeFI\n62uG\r\n=VOq8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.0.0_1535406175585_0.652409039359886", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9aeafbe4d6ffc6563bf8f8372091628f00779550", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-ER77Cax1+8/8jCB9fo4Ud161OZzWN5qawi4GusDuRLcDbDG+bIGYY20zb2dfAFdTRGzrfq2xZPvF0R64EHnimg==", "signatures": [{"sig": "MEUCIBMx9zEfwt8/gYgKTGoQBTGFsPKGGz/Dr8xrGqrGejj5AiEAyjgpha8qZ8PagBevhasfPOsQH0deRoYJhjYXIOKSuYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1zCRA9TVsSAnZWagAA7uEQAKAy4Zwkvnz1feAHWq/a\nYk/1DA/v03H9kh5C4+fslM7gGTltFfEioitwI+JzpgwCcdmAAxxLlwdUqhO+\nlyEQnymX8a/L7wV4E8EyBj7ULH/XmEnW/CnkFJpl98rMRFmmJuyIFZXFXN32\nMVLlGNEkEAQZ9cCUAIxpX9Pf7lk4XfTjA9YIhQFYMmv4JgskgPE9qKmOUzvO\ntb9zzi5aJEOHrYaaZtle9gF8aaqB8aQ94T4rzkVNQo2mLejD5ODzx51O4D5O\nnPuQsWdSyDn5LwC3hLkNmmQaxYuBLaRPWKxv5+9LmLMcZwMGefijL3i8Aw64\nk8nVboDpTlF6TZYyyW+E+jd9AQZiGxY+3tyETW3YgN49wVCMw9EHUsnYXz5h\naBsOFn49P8f+AL7S3kq24P64KpUsQE186Ew1LnLU4QHnkKf6NW7pmUXUwVlF\n4ZYZ/k2OgQXRBYuJGv/58EAjXryStQsn4qg7YB7KTdG/jOe9vcgUolSseddo\nUd0a5Ii2+sl/HSIDytxGjMME1Rywxk76vJnb1TVcnTHgdEAz5dF1/aXgASAM\nCqASEbVURe3E4kiCKgXYXnK+wkz79rCLlA3ANz/R0IacH8OVs9t0jOeBMGJ1\nVKauuFxt91RjzZ5N8E1TiiWri/4lRzxizKk3BVgJhSQD1Nx673zW67eJXFy4\nWIUQ\r\n=Y5qH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/traverse": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.2.0_1543863666689_0.49732836672796354", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "76309bd578addd8aee3b379d809c802305a98a12", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-zUXy3e8jBNPiffmqkHRNDdZM2r8DWhCB7HhcoyZjiK1TxYEluLHAvQuYnTT+ARqRpabWqy/NHkO6e3MsYB5YfA==", "signatures": [{"sig": "MEYCIQCwOspr9qMRL1+GLuNXmJ2UyMG4wi6AC/odFyB+L8KYTgIhAIcVHXrKGq3UOdROtZ7QXfyZsFto9Iw0PovfGmw6/k9Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/1CRA9TVsSAnZWagAAwZQP/0hN/rv5S2Ebo4QsWExn\n0N7n1qCUXYSkGXUZymQAP4bPAnqleocMa9p5yB7WRZOxo5f1yQfrSOdOhJ95\n3I2lyrHkr6LihC+mIwgDXiGCrlL69fZ/v4gJANMWzFnL/QcaTqYc9M5rV1sn\nWy0b6oSyJUQISTRJycCLOsuDhexuxejtk3j0X2Q3uwrVrTYFaTh78JBEtTeP\nSA/ypZeKYR8T1findmQfoRuhQ/YrvyWd1YEVYh3Qcy0as/yfhExghrQGpCY3\nczBPO/o97UPMTuph+kPe4gC49SRCZuDNPEQsuUD6GzAAJeov5j1dzbBIlMVI\nBJoO8Zs8Glpm8m9ZyYpuFViegBYewvHwjVlBCPUcee3VHVQ4zegEH6Efouxm\neAzOH6grgssXto4SRbFFXXx8ZhZkT3C+SGOTdXvrfCVyjl5YFoa/FQHVzRPE\n4dMoE96oXE9DOxg/rgurJijnIFq06G23HZLdc4N76/dyscJG4wqVJQbIwhF5\n5ekBeJFW6qR8qgoC3oSe4oSxbYFLwocX4+Jbp2pe08LLLL9Pp3oR2gprY49l\nj3D4v+JWsuTgicos0jFDGk31EGnD6LbHaWRewcWE4JMwvi3YekwJjL43vU86\nD6R6P7KQqSgEJwo4efGd7wQcXcCrd1aFJOKC+WB7EmYSh4uDnw6cNB485pqq\nQvjm\r\n=JRgn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/traverse": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.7.4_1574465524934_0.21573981167483014", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d98b7c425fed35f70cb85024a2b10008936631b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-9KfteDp9d8cF388dxFMOh3Dum41qpOVUPVjQhXGd1kPyQBE05FJgYndiAriML2yhMIbZ2bjgweh2nnvBXDH2MQ==", "signatures": [{"sig": "MEYCIQCDZIdAuSEsSi0fi9YlICUCifJonjDM88Wt86TpbJntJQIhAOPAUDEC6f3SYYy5Rcm+TU/XTms0pJS7Gk4SCZWlUvvD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVZCRA9TVsSAnZWagAABewP/0hlzawP4r46slPgjdVj\nIG2AwmwYEL4Wm+M39JEbCPa8mklpotHKP1JRylCTFuW9/NGQYqB6160Tlf8X\nUfqW5oH81yB68NNZVx0DJn5z/+E7/BifgCzBe9pjX/fLwLi2JjotLKVSVf72\nZ+st/Nd7+5hExHKNckf5+d82/BkRJ7Mbd2nwS6k1u5qluWqCyZ1CL4kM9oTa\nHDzLosMvrXW6AVFi4qZVm2kWGJkHNybIAGOekOznp7pX6zHEfrPkkES9jPHr\nxk0JYr+EF/BymVdxmrvrOexJOnRiaFy20JMGIQMv859nNhSiMt67QubDrQrn\nz7l+yUhUB6vgl1Lmu1h2hRnVXbxNWoHkPNVj3BIvzvVPFqZHVQA30sTkyTxP\nGZfygT0wop3NBmGBcoziaOhvrqOCxP1KiMdbCjFqmwQzNIpFgVIzxAP82ASR\nYqD3q/UZPheGVYqnW3sGegyHsNA5ruHuO42CXz5sX4WNUzdHI41iv7buUbFQ\nld8jdxpnuuHhzNRcBbJjEn6F9KyIPoFS60Y75zsT3GfsFtomeNooOnBJeRev\n3R94ddozq9f60MVt4NUzM/9Ktx/NI8jKyl+e3cYViycdD0HKWZ3MAD2yfieH\nEMID5AHRG5W7XaEvqWmwkhSkgThTB8++wkCi53p11/LGRz/29gmO4akWgylk\nSJaZ\r\n=MVxA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/traverse": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.8.0_1578788184790_0.302284576475925", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "82776c2ed0cd9e1a49956daeb896024c9473b8b6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-0MRF+KC8EqH4dbuITCWwPSzsyO3HIWWlm30v8BbbpOrS1B++isGxPnnuq/IZvOX5J2D/p7DQalQm+/2PnlKGxg==", "signatures": [{"sig": "MEYCIQDK+945SJWxAzPNxGuLPVhP22zOMgZUwk4e7b1/MiT14wIhAIlepDvl2PHH4CUt1jFN/v9mgYJt2ck9pXQiuJZC+qEd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQNCRA9TVsSAnZWagAAF60P/1Z0lE7ipIA+yxSsloRG\nL/njOY35/t8s4a6Hw211Et21qklOA6URe7F2eJfxYHgHo+2L84Vs7Z7kGgVm\n3jEfeEievDhlkxlTtehW8AUEy2U16AlLqSuf/7yMI9siwr5t1z4yhNhaNMBs\nsPD0Od1g9+6Zz1yTZ8B7Sj/Hb7TRxWj+PCgSouFSlugxrd5B3LmnYOGf1OgB\nJkaR01gqjQyDscr6z7DRbqmJdOe0/tudkCTFrKh771s7KjHADdtqxdTmUwxV\ng6HaSOMPiRND7KWAjTKhzqPYlR7lju6Zh0op69wIAiWRyioZJ0IJ/ZhRy/b+\nicJ95/x6WrU6qeYmiksqxUBF5UqKWEiKl4o9IcP2JLkBThXMdy/eouYHjeQe\ntGgLLb8ossZ0wyRrpGqhiNazBoyTmZoCSxBhFk7TW7iXrTO/Az7bYGDkKsis\nBvXsbGt++bwcz4CSFpkifsvuVaVuNpaFWIa2v7Baa+x6FM2L/nOfIktEbSBa\nd2H3M3sVQVbRkf6kH20xGLOxZxFu6rVU6ADHpZNVzlfbmIuHCZIGfC684D6R\nhJ4n6wdjlwYK+Nebd4DZcmVxItooDtfEKGMzHO04J3qSkHHRgv38ZxBMeZ5B\nr3FBogncv4bLTGOMMFc9Cp404ubtryoJubHp4aEJGy0xRySx3Q5Yur+XmRoD\nfLdD\r\n=GYwL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-arrow-functions", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/traverse": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.8.3_1578951692598_0.3654231693995089", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "cb5ee3a36f0863c06ead0b409b4cc43a889b295b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-6AZHgFJKP3DJX0eCNJj01RpytUa3SOGawIxweHkNX2L6PYikOZmoh5B0d7hIHaIgveMjX990IAa/xK7jRTN8OA==", "signatures": [{"sig": "MEUCIDGGVitQ8vtgMUQw4PaYEi1BLq6Gugnz9tIBYVsBvN8ZAiEArugTVZhdCG9vy8n38L2REYCH80Ux5ttYvH/tEXI/gLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSUCRA9TVsSAnZWagAAw3IQAKUjh9FlqnQ07KQw7vEm\nyiMTdYE0VT2+W/t0iJe5SoND6F+u78DDHLk+3HQ4LnVL0YOAAvrp4aTP9FfK\nm4mtyY5LSFNnOYMs0JFSNGEVopj0zAyZQq4RFJcLry/OoV1W/bGbazPjPDM7\nDPVpn4H8GIOhKHVJtKl5o7oCaMzrRE33nBxJMrclAflCliHO0oqMmLopHQg8\n/7KTZvhPuDpdjxcIwdS+dmyppMvOQQhHfNlQfa72GQWjYyvok256iRDNWOHC\nf5FY3PZRPb7fCkehOyunLGkzkckAX84mVLRstiwVOtiv6/gEEDZkMHqbY0XU\nOob+gyS3kQAKHpKcR4OICTlZcU/qk51bzd8UoY+WEhSFTh/frHiE7I1XHEK7\nqTXclQGjdtOY18sIKsYtJXKaYhysYb8GR0wVyIk/YbUouDi4pdoLEnh9vq0R\n+wW7nm7UqAtcFa/Wr79r56e/7TNcn0BlmCP5Ic/hnQLWPnvsHQ8mGEjdv6+w\nnLc59mKADer67L/R6Q0SJ3l+hm1l2TXRoQFmW5rsIP8iGNrcGUNTkgDVlU7Z\nfdZZ63vRGEKekxxkQ7slfk0Eslou9RXiV5pdokbqiPpePnuh0cimgkmjmljr\n6bZ0ITklMUt/gEsvkOdvkgfwDxQgWDMep0hfO/Tsq32DzbfdiWUrPC6GxKjG\nfodi\r\n=Vp7V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/traverse": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.10.1_1590617235503_0.5674178715638816", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e22960d77e697c74f41c501d44d73dbf8a6a64cd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-9J/oD1jV0ZCBcgnoFWFq1vJd4msoKb/TCpGNFyyLt0zABdcvgK3aYikZ8HjzB14c26bc7E3Q1yugpwGy2aTPNA==", "signatures": [{"sig": "MEUCIQD7il0yD2/OUgZ3xYErBQ8ltudLKBS3Bd8G8eDxvz+ISQIgNxmsDhYqPBboq0BXdv+0t7mRE2bQLFaXcus/CbXdXsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoaCRA9TVsSAnZWagAAeroP/17+q/VFm1MVMOdFjDpo\n+y24vqj3oikfZLLPUDQJrEDBbxtFFxtgxJ00xgD/TGDN30RB3tIy26Ph/NZy\nqOO9A8d6ESHuPYNIyJhJ7y7cJyK+catZ/Ttm1VfpvYZZAU0mtrEMIXx3Cr0V\nj1ybr+HmNsICvANnhm9hyJgn53aVfTIHxqbZS+4EJDZCLSJF0BBXd0+g23HQ\nF4OODjZdER/KQKumNfKKu6nTIAI2kpV4JNIC+hCrd38ms67wVPuHYJZ8sw8L\nJVWwyTunxq+TWHz8khpxw6UyGn8JGJCyO/HB9NJXwkwclNrcz1+KeSXFN53F\n3MNoBF7DRwRfL1NNJOiU2zSAO8YBGr16TX0d/fiLmle+ZBIkKWkrS7iPb6hM\nkQH1xNu8OtQsXPQfn2H/i+ycdyfhs6ELdOCQn5rIWt8+oz0q/fmcsj2O/Hv0\noQpl9U8Xcjb49upfV4em/F7cb2ErH9fj2kcwmp8MhW0bLTnCWsVgsg4MlJ0K\nmj/7LL8FCYsyFNhYg1KENhQTxkjD0E+Eqvo6RO/k8xMZlFnBSdS3O1iEvJ10\nBmdwP7F3kFse2oATk5yteKbW4yYEamUT5ZnqR0GHHOlVenw4+GdSBIsaqk9n\nMj9FlYYn4dZGBRa714e22jBRajfHFbkBeoTNuWslYppbRPWNbphG9r8MpsX5\n0qTz\r\n=4F++\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.10.4_1593522713903_0.9790572182349833", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "8083ffc86ac8e777fbe24b5967c4b2521f3cb2b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-5QB50qyN44fzzz4/qxDPQMBCTHgxg3n0xRBLJUmBlLoU/sFvxVWGZF/ZUfMVDQuJUKXaBhbupxIzIfZ6Fwk/0A==", "signatures": [{"sig": "MEYCIQCmrm5HKCDqHmVM3sfc5cW7U2kjlrwNJ7N2MZh2vEo60wIhAKLc+Ir0kVabd/6x55y7T+dBp98NYwtAXNqrH7vozHN1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+9CRA9TVsSAnZWagAAn0gQAJ+Oly3Qyi3D0bxfms0V\npBTvWMYrGQv2C1chhbCdjclwKkV9eMvOF8SFsq4TkMd3Efxx+0dgcZEd0aVn\ngtsQDdF0KFuaSsYJJ1kHee3ojGQ5WFJQm5wH0CmgkIr9EQ5ugTRP1EBdBIOG\ng8nQCx7COB2aB4CpioZ+y6VeRlfu/UUSMFD1lZNlMTMHKw9Z3Zgeh4ycS2L3\n1A+VmojWSXx4KjUQgDA6fQoBXD9vTyixxKkLX3fYSAkcxqoh3Nb77F8JiZ/j\nPguAEp6p83F9m8lrlO3O7arGzX4CHzyz9IBbkhgEn0uzytp2hVRwYDJsK7AS\nLZDGRbyxIySCmzCXgyoZfgL+eTt12QvSmocHc9SCshsUe90eNvrrcTvuok9d\n2fKtr7R06J2FQEsaZ6A1iuXmaNSk6rMYzX4BpfOVhZNu61FbmLhE95/Xg+7t\ntyWiJvzfOnFHFVFsCQaQPXY10AN8Z2qG0SnAeNV1YEnf7Oypg2GCvvs424Zf\nx7/p+q/XP4HYF30qg4L7C/gxj7y7KIa3fPNrH1gpFdVG+IVf/IE04jheAq2L\nWR9Ey9e+4vFhcvPzoRH6kMB7pw+SBFX7ZOCxzxkwzQ21WcflbFZ1TLp4GHdl\ndqrCYD3AAiPJYZHbSPumYXd17YDRjxlJCZw/TNEBkgBk98m8OPOPlSAAcozB\nSy3e\r\n=H+eC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/traverse": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.12.1_1602801596743_0.40564469582532925", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "eda5670b282952100c229f8a3bd49e0f6a72e9fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-tBtuN6qtCTd+iHzVZVOMNp+L04iIJBpqkdY42tWbmjIT5wvR2kx7gxMBsyhQtFzHwBbyGi9h8J8r9HgnOpQHxg==", "signatures": [{"sig": "MEUCIDi0gmeAZ89iw+TqKvxQoAcIPoNoNd6XhKC2SFYLyadVAiEAyk/GcFjfGXqPrGyz99qTygFaGZ/b+SldERnO/oEbG2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgWCRA9TVsSAnZWagAAx0gP/3MTQyxuA5J8zEIZhZMa\n1VMR6wNlCNSqF2UwNahvIX3W6BcwkdQLHK3IzZZZRSs+86/H23YAFsyZAKAB\nRo0XpvfUwZ4z8dyS1KK2c6d+Uav/WFas5vbotD56JXE2X1Nrlj1xz0gavqra\n9jq5bNMij7PdG+/S77Nh/u4JEngriBZqs5/RN2W1QWFvUM9DcI5oIuzqvsGN\nDuATQ9CCc+nNuWHrBOf+LlN1EYYNfjJHNG8r9jqccrugTzkYacGxkab/JN4F\n6XCuxBWCrh1/6RZiACn3kEsrhcwV8hXCwnW/jP2QkdAicF76UqeWk7RT/qwv\nAmc+lSwqGx6BtU1Nhd/O+cda+pWHAeaEJDuWSJE2SsKvpexKQjH8pSsB5UYY\nmzGh7F9Zu4nIwhNaTUGLgYslvbf6pVufcPhmTKgJZkLaCpEEeg4qKqRsxfjV\nLkqGyfMgJP4RjphcIxpMw2HNCkN/jiQxXEcgxK58pY7nV8NBfHfGm9N8naxz\nX7rCLZr9r5+oJwjwDSZCJVdXzX9w9iVIiL0TO2ONPtQHq2sU48LdLV9pxx3U\nOPDv/3zHfG1upvrD88+fpWrSyQdKggwlQgAujhVcJwijrLmwrxtQZ1HQ27g5\nGKgA/3b4u41RMUmJ9YHofCLajFgRd7+uMSKUPlbupWw0U1MeGy5pUBrL7AMp\nn4u0\r\n=sCIp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/traverse": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.12.13_1612314645983_0.4574129124571078", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "10a59bebad52d637a027afa692e8d5ceff5e3dae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-96lgJagobeVmazXFaDrbmCLQxBysKu7U6Do3mLsx27gf5Dk85ezysrs2BZUpXD703U/Su1xTBDxxar2oa4jAGg==", "signatures": [{"sig": "MEUCID75DO+KZNmkG02eMTvibqStL+tDXbDkZBGfZMAySo6WAiEAx+D9h82iAyxtWqp59Fe52izptb2WwZCSJxXcGL0Mtes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUSCRA9TVsSAnZWagAAgmMP/1GQ3fWTA9cKy9HRwRHM\nJFCPhRYs/B5E/3NSc0Pj6SWYCnBaYjxp5Fmz4TXo27RNy/ShMOaG0SAKLx2V\n7W23rvMPEhdzLeiPVQ9l8hVlDfNm3NbwRfOwRScJFmvp01VhjLGxfVRnM+bg\nUTVmWsLFlYYxkQKE4KF3nB4+UWbcRk0j8NvrwkwSIvilDJtQStjmoJ10h/Ho\nLITNFuR85M2lpShZvbGxMjJEzoSmxLHarq9L5QYDub0PKhPlTy0Pa2iEnGAa\nq/yp4KR8PBdegdPyBk8uFwbvrEBX/y0BuiG98ffPTtT87uVMJAHsVfxEOqrS\nSlV8XtBbHeFLtCACzt33pkRoVzy98RB5XBVnCtYY8GmNKvcokDSX//Ylq+jt\nODgOfYak0gU+s3zIGjZiVF1EREyQzi0HlqMgmX6tA1VZZJJt6lW4QrGva8mH\nc/7vFlFaCt+kB5qeblnFfEZtipMY6T4dQHZXdFtiw/tpti950Qrj//jfgq6H\nf+Pjb/wsGpOUlHAD2BKS6ddqIqI0whNVvF2QikUJciFhmTz5wx/DEvmHVpxu\nvBGf86HX41PWrZ0WCbzKqRmOf11VDBllkLjFFUy2KgBy8u7hdEIALsp+Mv1J\n7In6O3+5cDl+y9vTM2g8hKHx6dUg8y+lIjiB1P5TgZ8jIaLt3lS8d2zpZxg5\nOsWz\r\n=Ewqf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/traverse": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.13.0_1614034193958_0.2505066180524511", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "f7187d9588a768dd080bf4c9ffe117ea62f7862a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-KOnO0l4+tD5IfOdi4x8C1XmEIRWUjNRV8wc6K2vz/3e8yAOoZZvsRXRRIF/yo/MAOFb4QjtAw9xSxMXbSMRy8A==", "signatures": [{"sig": "MEUCIAtwn3CWaHzPzMgvCoqLeohtOkB9yvVa6PbCljo4EZxYAiEA+4wzZgk/tE3ElgU76EQfdm4hcO3GFTZ9jdX2AL3uqDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq8CRA9TVsSAnZWagAAMIEP/0hkz/+5ndCDMCLD6uWu\nbfDhUY/+RykcKYG7d79S31fuiO2Ib9A0XnRL9X1u7hmJV01zBFwxG+O6zrSR\nkVfRTtuuetaJ/eAzfP57jLRRyxbarb7o4/+b8zL2XOrUYUgowiO6jECyhlL9\n79Xyn0tmlkA7n+IYZkRG+TcFaglU0AObZ8m+Lt/6gQd8erELc+HUEPzZPvBO\nopvHxKmvMPZCbx838Z80bl6XBZBZYHVX1OfX4bQ37yteB3Fo4c1FsGJf0QAU\n/rFObowpDB0fFC37H75V55Di5yvdUyYt423G60eEFzk1m4JvyTS0QVLqWldj\nkXE8aEVbE3Nm5zFnLZvwkFrkwrBStgWNqEtYNSivwfrgn8i/HtP3h9r9+geW\nfHULLQqIUhiU246cO0gzWzMz4TYoTM6kCJORLk39SyZ7aDi2PsiUh1hGzkT0\nqAUwR/a04abiUU14jGM4Y3emIp4Q2CV6niqG24NkbrkKgSlu4i22QzbRw6kN\nyiotSKt+Whk+k+VuAb9nfdDYje1IPy6Lr9DN8nGN9+JBrNyH+VIrWzbuUK0r\nP/H2YQpsaqJotbclR9mx1vzpxTXbLvbMwMBZOlPVedfT6kS+OKjXZudxZkfA\n31qCk4Ah3M+RVRd52a/gEGZkNNS96HsQB2FTLETgjmhpj07TfPbsuy+nwLup\ndqbN\r\n=AE8H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/traverse": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.14.5_1623280316530_0.6167269229487313", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "951706f8b449c834ed07bd474c0924c944b95a8e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-vIFb5250Rbh7roWARvCLvIJ/PtAU5Lhv7BtZ1u24COwpI9Ypjsh+bZcKk6rlIyalK+r0jOc1XQ8I4ovNxNrWrA==", "signatures": [{"sig": "MEYCIQDGHDv97XAoArlwMmt1R51WogjLSd+vAmrTNXr4LDmAVgIhAIbWhds0TdYIZ4V1FYBNXHHkKf7dHQ1fETLX4PzNyrW8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3163}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.16.0_1635551248245_0.9614961518659595", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "04c18944dd55397b521d9d7511e791acea7acf2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-8bTHiiZyMOyfZFULjsCnYOWG059FVMes0iljEHSfARhNgFfpsqE92OrCffv3veSw9rwMkYcFe9bj0ZoXU2IGtQ==", "signatures": [{"sig": "MEYCIQD5MBq+EJZ0OTm1F4aQF2IIqJERE6lTZcf5V9hDKoWiQQIhAJhQYjNdaFcpb9d0tQTyd+xNfN64UqbA14CQVd98fXCr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j3CRA9TVsSAnZWagAA3w4P/iEYpiRbFJ+azkBedRAA\nMl0F5A2dF6PDgKFpxtPLmIpNZcMvGEI3dd4TJIjHbDqd5wckljXOs295Mx6h\nyPqnyOSIJ0ExCOt1QlY3WRcO2LZiCyJCU4hir8DMy0MgUWWQOmSPekx5MioM\nvDaeO+eUaZdxLRY8C+Z14zzW1dZZ7YTrJRNfRgM71cAowsqTbfZOofvAh8Oj\nLu6dIaLzh+GLjXynrUgDHiOzaLcmT+Dn2yF0n0Zt8oinoGlcrILG4IHrf9TP\n14PCPQU0lY6VLxBCPkLbMzX0Gj2NkCX5sAFFc+8zgn0E5sj/UlSgU5QDXaev\nsPMOPjnmV1YxZ1Ebz8ZhOyqNTpIz6QsEq4muA6RpQUSMY4kA8aCwunwd1hg7\ngVSrYFYHUdXpQQqrmONcJtz6if87i3v73Y0rAnIpSP6cjYMwPCPo/56JH7Ks\ntqK3arColj4/Kv0kZBTYHhcRuOdxWsx7U9TeyKo5ixoLyejGmvHCxoTagaox\npGZHa0p7INzPpCPKhdUvYDAYbyODxC3wA2eaSYSLtuviuSuHH9UsvggdL3pA\n1OJJZMQ/LtWDkUJtWOpCj7RDJ2ODvruUaiCNLYT3yFSgd4aRTfXYig+mXLC5\n8kiDpmCMZjBJRzA84GtUylYq3PUSKf/MQ6oUbFQwJJEGywAgQghvV4wWnJ6d\n0QKd\r\n=KNVT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/types": "^7.16.0", "@babel/traverse": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.16.5_1639434487254_0.32645794167891684", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "44125e653d94b98db76369de9c396dc14bef4154", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-9ffkFFMbvzTvv+7dTp/66xvZAWASuPD5Tl9LK3Z9vhOmANo6j94rik+5YMBt4CwHVMWLWpMsriIc2zsa3WW3xQ==", "signatures": [{"sig": "MEYCIQD9DZsSjzbvlvAx1YWm771lMMpiacy2U8Qyzes+l8H0pgIhAOqRq18AcxrlhcbVpm6k96xhSqqmlRyeCQ/vT3pPypRT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0gCRA9TVsSAnZWagAAW7oQAKBDl2T30MXjuJj+h2T0\n7J4hpDH2IApj+Nx0cEt4M2xhtrM9un/OMGJ/eRrJhtW388vAT9zY8qHz//tR\nCX7Fdux6C0+jZVFRcGsSDevbOvGvPmGUMkccNF30f47LwhllKVADEzbm9Efv\nttdEe/7MuXTNTp1AHJAQYQjIUrYp0mC2kwAtea1AhPvBf5i1Y+604+KFTYmN\n4mc8mpgNwJWpN0OIK/d4W+ts9KaW/nAWgXzl9t4nO17qCwGQ7+IeeTHfRBTU\n8a1V07E2ePgXJDw0k90kr3C7xKj6doH2kqg2U9Y51krUZw9CdvVcWSk6eaMb\nsLVfENIt7kJET2/t5C5MUxLpouBG9W/ghHtrP/fbf8A+ARqfuif1/zytkKG0\nfQ9uCxYTsXAlr7+6la/ZI2P1uzP7qaxwR5ZTUYl1bYAKEtefRsLe99vp/gpW\nPVotIcHV2guLSDh/ulNdchtWRoSlN+5FIT5dAhOY7qH59CQv//LcI5QEpy7D\nZ5RyxJzvTe8EI1rM/oWuQdyQ98pP0gXhwjiIj/wtjkBf9C1XZ2bL/Yn2Nhbd\n2kzkbdYvdrsFoEW5ci9RDtV19GoHdVFfzR46Q2Pak/JCvzrRhzZbDQO5LEZ+\nmUAdVqp1rjLkHh2JPYukiiknUcCWVQvcLxzpj1u1ZrnqFcaFiJAN4ZES/6aI\n+WaW\r\n=LLFE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/types": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.16.7_1640910112123_0.35335473588099986", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "dddd783b473b1b1537ef46423e3944ff24898c45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-PHln3CNi/49V+mza4xMwrg+WGYevSF1oaiXaC2EQfdp4HWlSjRsrDXWJiQBKpP7749u6vQ9mcry2uuFOv5CXvA==", "signatures": [{"sig": "MEQCIGrnKDBqTkj+Jp/CbcSUDMar56IN87eaa+NoO7ZDkoISAiBQ8pH7xiuJ+TNvo7VRZHO6kKs9MT4PesyqgRTtS8esGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLiw/+KMkUBCm/tYypbytTjBtKr6YpvMiFbn/ZF0/B1dpnw/NZQVv2\r\nt1Ivg/DQUB5pp9/JTX+4ZRwR9f956boMm6UrWxZHmGKGNrPVM9AcnpiUFvsp\r\nkcLkiOp+WKtKEepQ5Jr4uKjXTMOnyb/m8j0mWP2VNIrzdVmJAxE66GhrbiyD\r\nH76bjb0zqp7129fGb/TVrYKS9lyYz9w1Didl4eB77XmJTm9EheiAMSU7Yedp\r\nBeU2HuYBDD2dAvHVOHsEA0eQ2BFVXox3gC+IHOGi8XCsepyfib3kBwHI8i8y\r\nevln6tDtMw4cuGksp1Vf1Ve8J/zBm6jSOWiY0sVTpsLi/7DRLhTt6qYQeQj3\r\n8YCM02Cl8RaoVjOWXnus2zIWgoPQEPhC1XkAod0Q5nDgzF6l2FkCHDryfW0O\r\n3VAUA7+CHsgsci6vB5BonHnp18F0MZ0SOwY/f2z7bu6Aujxaea1q5KQIdPun\r\nnNKUzCwC4uGburqpddZse9HUO0qPwBafD2ccJ0W/vBfiicyB+pKCaIrVvsEC\r\nMkqTwQHsHo8j4iiUR69tRCjE2TkrORklaMJZQ5ITj/FupC4E3BWiW99rKqTP\r\nwtaPfNPh0M85rY84D232c24kbOIleRxIoKeOA99xSwOGeAasKiRUGiVLQePu\r\ntjNIHEkQzbbbNYVWOkGqITHMbySWvdrgdbg=\r\n=rAg6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/types": "^7.17.12", "@babel/traverse": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.17.12_1652729564556_0.6213428122366602", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "19063fcf8771ec7b31d742339dac62433d0611fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==", "signatures": [{"sig": "MEYCIQCSZD908+3Vo5zo4JeUZRlm74cSlPbk4QUwgUgE+W6f6wIhAO5ovF4rea5SusFX8o9lzCDSODi54HEpuqJd5khAVN3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5Mg/9Fph48+2EFkg/j3JyiQx3N9CSIFxODKG4E52Kenh1+eK1Xdhb\r\npMHP1XMFFm7gZ9+MfZUSUKbyEFV9O6WUEpG0V1JpF6qKtdmLPDENAXep2t/e\r\nACO1dDRebrlg7olS0F26AWjGOue/Iuc7EqomSbRLBOCv3cb6lVY+3H+L2G4W\r\nnrBQzEZN2YUXLJrO4E9pLkq9dJCgYAR/r3BV7vXRMnGHn3wHIwkb6DoioaGq\r\nOeHWGk8JTJxP06wkSFdl5sN2EDfWT1pNuAWDxVP6M0QAWIRdFr8YbfpC4l7z\r\njRSat5d40v+fAZhuRTpNg4waVLsIQ6lkTlX9Q6ry9wX1ZRwEYTNYVnopmqvX\r\nI3wdZkodyfCpLFD2vVTQNIThU7XO5SzBR2vYRr5uFraynWRCCAownTQ8fw5m\r\nTaTbt/TV85Ide5vckNR15wv9PHMAz5oxGHkFLPxqZ+r0HFbNQjyqf6H3SRtH\r\nCecIZFt7O6J/5iMR8R9+U0qe7Sa85iXnymOKCW//JB6jAk8MjYVjS6AqS441\r\n/B6UCQU2yOz+4LvlI+6y0NAMymHKcsv4p37blr986YXCBB5Nh0h343IR3zUN\r\nA+LWiasEma6a40gSa9yP8jmZRZ1WC+UFpL9g5ZN3/dNNtMgqzeaClxodSeLD\r\nyq05SvaucXEgoSnKddNiLU7S1Hl4pKKasRk=\r\n=AFJl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/types": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.18.6_1656359398066_0.9129738555323712", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "bea332b0e8b2dab3dafe55a163d8227531ab0551", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.20.7.tgz", "fileCount": 5, "integrity": "sha512-3poA5E7dzDomxj9WXWwuD6A5F3kc7VXwIJO+E+J8qtDtS+pXPAhrgEyh+9GBwBgPq1Z+bB+/JD60lp5jsN7JPQ==", "signatures": [{"sig": "MEQCIGSQGzfDXZY8SQZ2JFUWwQKpIvrvEaXRorZGjvyGnREMAiBMqgtlJYnun1lK+ynrv79M+SxqdrzhuxEfYg2WKwfJ4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCc2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptBRAAkiIx83RpTc1SJ7lsLEpmmXwbdfScN9LPQmkfA4wiCr05cGqa\r\n8sBTy1VjWG/QflDJxgChV+v2HXdU8xxjBdWBH0Qpyp6g0vH/J60tLV4podmV\r\n6WEv7Wicxh54Y1MHHCErVL5BuDpgxtc6RmScmJmParriVUY/aovv5pNuR1iB\r\nHvVoR8qaI5ARUPD61y2zhi3LKi38sQP6JldrBmhntVAJs5v+g99sBCmeVvBs\r\nyaiS84JQfrFNrE4VUrNDoCx+HNh1fbinBGRgtMfzNhU+TpU6PYwViB+qEoYi\r\nh7PZSNJUpr2Y7ernRJ+pHAoYL5B8WSgK52DJ/ql3YfLd+q0f24Mqq6fhaOpx\r\nlvQiVuvuxx5OkX6Vwn+8zkMGXdNZBW8Gw6vx/StuZflWO1CYZi6x/tp3cNgp\r\nYz9Tcfq+5YBGcHh4sb6T6jTvKrxGoTmr3mwR7k7hMGEM3Hj6uc8/DUrZe7bg\r\naoyMkzdFC2ZEYxGS4M11K0iZuNRMTbOJ9C//LsCofkZHU7AqLZjxPMvjNrpG\r\nZNSd7QthHOD+1jcrDv0epwusqPtOfZyGgdescPmxa4Mgk+KxRK9S35zC/TFq\r\nF08aojrUcVEeTmgjVFMKHhD/QSa8P+soqeqDgTTFn7VlUYMqVMFSHxzv9QAN\r\nflKvxJE0pY3uaTXec26zL0COoTgfAv3LyyE=\r\n=V7uc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/types": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.20.7_1671702325756_0.25319826340351814", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "d08c48970d923958aca502b317dc18d4e85e954e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-r09RSXDsvzkDtVOuhDMjFFemYNK2/FV3bz9kUSB+/GECQf4jeQflfyO/p3gEj6t7qvea8JHKPsaTCeLIDy2WVQ==", "signatures": [{"sig": "MEUCIQDRVIXtooLz14U6FmpJQwyatudAZeCZ6sWHddPpmbh/eQIgCNOvy7wxpMmOlXMRDAIlGN46boPvG+gBfo1qOn/mrP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHmw//ZVdgjYumAJCOt98pCAFpdbsda5G34P8VEzraynDpMSXp/Ybn\r\n89xpYzgTEh9BOZXbeEUwXBYzSwAFOA07SfWpDOuvxE/yV391ghd7C3++ztaH\r\nt0u/hQ32auus7IPpgiodUYyjMtu9nqJYmgfalBt+1P1OazbOweMOf9gZbE8y\r\nQNrXuphduAu2itcXPNkAS2PgAUbA+GtOyway+FamLXQDYHAwVjHb2Opl5lFU\r\nt1s3vfWmnm4O/NUFrunqMXSubVbTsyNwC1JmgPvq2Irw9LyGW24tFj0LuMQS\r\nvuFrV7QU4qBhiTtgOnW3qMMuPcWppfvH4Qs5nKa3JcZAhqdWqkcB3M1zgIpK\r\ns1WuiKD4KjBidXeoM6SWS8CghOfqRPw3tt9flWkHaBhKkiC7sfKLU11F9ZeX\r\ngBJ+SL/1Goqr2aAMay6EI00GyTJLESjexIShVmTQ51fOjAVvsKbvjfG070kV\r\nTRpiqbwEtVkRbQkdAH+eDVcONJpNPXtka5Go6Ko37nLE4sBiaD6A8ZOeBK2r\r\n/TQXgzfrZxU107YxTinCcTU/ObthTekPPvHnVdtdXDfqUQvi4XcsuQVyNPZ/\r\n3hjH7qaOXLMrGOoTKN8vSjG7hHKYT1cdK0OIIv/J2fxucP6wxGkEiZunOO2g\r\nYMzrCw/oOqVrAkbVwQ4jpTxUDXgSQKeuz1A=\r\n=2lj8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/types": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.21.4-esm_1680617361154_0.6934877524471279", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "272a7daf6aece4efbf03bb88bb18b332eb091485", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-+2i/LU6Rgw79ZuzoZEgBjv/hSa3/VlyzoKOTTgjs6Ns2BQ4KFWstVprvH1SfTdfhyJHgRZKsxZGWjqMJQKcA8w==", "signatures": [{"sig": "MEUCIC4EGrELAHccwET+OAP6UWdjp6IAI576uaQPqEaHEG8IAiEAnvJABsJlXVhAQEiExgGmbIpN5uzDJcalaf7Rmtl/KoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos5w/+O1TKDcu4tQXneRAfwq0nNUGfAed4eoZCtkEj9+GTfq9Ad5qy\r\nz7zsUfFIWS+CCrgg7AEDkqAwfEqpUMvtUKv7AFAU6mdHE46Ecb4oWMWYiKcA\r\nv95zlk3C+XZ2LuskTz6iR2grd5li4uIHhdRJDSS/cuXIQhZcm+ekrExgolUD\r\nY3PqIVUhvXjZhLlJ4PhIS59okJgsHxMMICH4Hy8zSbmzmaAkdRJTvbp3M9Vn\r\nTXlx8ubxexNhu242nL0viTPD+t9nxRqnyzgEsB5PzVMH6xX3yEycHQ45DjhE\r\n0DHDqM4TKiY0kvLoVJyYRh2J0GLWoCHtKD+8Po0UtXArsPDsaH1N4dbVEIJd\r\nwZE6DKqEjJdK366Sq2QkUsLP1dnkyBLde2F/8x0oga4hrr/7YpiqXG4SIl3/\r\np2jTZ+iC+yTaUw13xYwbCGsyd9ZWbYMssD/bQEMKD+5SMVJRRiYzZ0UyQg0q\r\nDH/zeS6IEqk0/T71TRx1z8DLAjMYkmzlY4cY3aLK5gzWdbWgFRhnibkqm9Q3\r\nz9k+QVAzYMnDw6lJl/c9/zwZyZxgiXCgPOCxCbBssOaW96YmmYrAbOR7QtVl\r\nadHMeywT9poiWs4486tymgjC9ERmZboRj4s9MNDH4duU5xMW+/1kgbeUBSQ/\r\nIusAuoX63ouca0OZchXp4ftU7DGyt0w27EU=\r\n=RM0C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/types": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.21.4-esm.1_1680618069645_0.6626735581993497", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "5cbecc616b20cb5b99e32bff1d942e1c14420267", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-4uHbSwMfk/rrpE6mDPaYwtb3Ob0zYg6p+gklAj60Jc8VMb3WHaz1ztAMFsHPZJZ92F0602opzsh/4nHaf9gu3A==", "signatures": [{"sig": "MEUCIAHGwQxRl3MOtOVDhr2NiWRzeQ7Xkjs+nGvUhVQt5pFAAiEA4Y/fzfNNwL+sFScNyAhVWK/JvrAYwfCOs8ub6jB+8Pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHbBAAnavWw6rSkSZJ8amBirqGYSamGlCUSGAQiFoAOmWjZU9tGLFC\r\nY4DmAHaqPfVlsOVax/KlrufmlN7+K+lwem53v/b9BGP35ihoKMimj+6TJQPv\r\nf7UInHu33EpRNmT5OV8Sl7P2u4c0WCPuDO5TlmDbl02lzPjj6SE36BB8E0OV\r\nx+9PwqA93/AmEnhqHhe9PPQCHfRq4BBfmNOSeIXFf2eRKeUZlupuVpHQ9Jvw\r\nM9IftSc0LWMShKLJd1S9c1Ff0Biibya2jY8y1J0RY9Y9XMKeUt/E2lrN6Iyl\r\nwdQASQq3Pz0AYMngkMb/IIxvX2WjnssLaAge6nvBsKt07Bf/+L6zozTK/+pm\r\nDu0bsBtR5Nob675cba8u3XP0KKcok52LjQfbC/HLumDNGCuljohQPklnC1Rn\r\n+qRspLTPIWiyuvc5TMWdPW4EcjaHGTMcM4Fu609TBXd/Dkh3jW9CImngArwU\r\nc24iq7srf2MrfP+r2iyAXd3D7mCOJaT/+nPAFRD4lp07iowtT79W4oD0UjeV\r\nFgMl3e+lvd7P3aa49bg/tUsYdkrM/g0f67t+HDFehus6Odl6V7he/0Ma29Ql\r\n8/l4WSAmLUMKz4BRRTyGQ5PxLqf9H9oAWBUfzEpOruB/lSBTyotG7ezNQjs2\r\nq7pUVdFhYdqOi7gO52H7Mq80VGjcgtShspg=\r\n=nqeB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/types": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.21.4-esm.2_1680619153134_0.4831567661245979", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "335242c8c7db912538b40574ba6b56d802e74161", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-6/eCuDQdxHHIaZVssYnQJFjShQl6Kbd2lghgTfz9Mr3yW08ry5L7S2vSrXqxb2CuhvaDIVCsmDN/hTQfI3ykgw==", "signatures": [{"sig": "MEUCIHLykREQYkvQcyH50YCiHTo5FKO2vWJhSIeGBN2DE9O7AiEAnid610gwr/taY3nYvz9XXi+Y34bFkQn7Xr2mF173m8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYRA//foUkPwAW6X5GIAwgn5eQdi3VTdgu8hbxhabE+dS1TsxR3UwR\r\nDLZ0N+mDTkwf0w+DcxEgGzBD/NVR5aWe3UZkdU33yMdghn+S85jjogNhH3VL\r\nTe0O1vlWxVjE7Yq/VZXqkTSkdA3jHAyflNDnKKca4GZdNmaLFTxFXzt9UU5z\r\n34oqxsgzSQoYYtyVFEMhosBQncZSIjfmF6uxkVQxaGFFdtH778aY9tZ2xeR2\r\nGRu0L5jT7WkHvSono4vj10yUjV94xddEYp27x+Bi+oOJMgZMAcLaO/b8k44U\r\nhvU0w2A+qYLqkIvclZ4ZlaW3Wvn2CvURmsN57qw557Z8Lrcl7NSY1HyrOP4Y\r\nJKazpwq7UivTkNkE6xZMjkQYD6F+j0mFf5djK1cNZFxk8hsqWBennCEunAOj\r\nPKY10JQi7xUwCzewFWlUaSaWmr9xbHwrIDj9dA0lU3k/dcBcvHb4gmDfVn4P\r\novL3TDs6lK4BRY59CLYZW1uJ3t2ClXJ39Sb7SpsomYr8aR3EMLDdgssLeq6j\r\nL8YaZqxvx9Qusf+4jrppDZE2gsYYteqyQwVf1clMRoY7sSPrlF8aND1/uMIV\r\nD3hZ+7PlYyowA8Q0yaUMWYkpJiAVz5WByq7L57ajS0KHZxXc90M3QPQOOiRb\r\n7gtgPHqHebA/WEKFkLqDXmWTB5K4b0V1qX4=\r\n=yK9n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/types": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.21.4-esm.3_1680620164708_0.9286424725169462", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "f38317697e46567ef0da36357dc87a813e02c414", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-c0QlQ9CSzoQuNJ3iWqXYEZptSZRrh7Zahn2bownq0CalW/VnngNObdAYQkkTVL50NJ7/drkTilJ7PX5D+TbP4w==", "signatures": [{"sig": "MEQCIA3pzsXUz5uDqqVyzu7oRXUa2NJg1VmlFpb9WptDJUl8AiAipYP8Dz0l3Ntq++BwqQ/VkSSCK0aWib+SiPk7G4Qobw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMbw/+MwDCv56wLHfrPVi12Qf9d4YWdal0zb0ASTljq/77sIy5lwef\r\nA1Ns7PDC6DR2UDOrzyc2dO0ZhG/ZALXSfsX/XBZWfnHv8o9TWqw75nqwEQ0R\r\nBFLOmcGN/h4WBGR//8xWWoLZ+M0os0sSYddbtjkf7DIOcCep6NY31OaCF91a\r\n+8VySOiizBwLz2UOZMwnATUDq0MwJsaIjqxlqbf7dKoH3d9+BuZ9ogIsy/Mz\r\n4knk/+lZFBTJz5c8CFFRCyA72S1Mj+PDNgUswD47btOXYAfAnyiET8PjYcW+\r\ng9W2LQC5yU131ZiHdokyqp89Wp9Q3+YzAjB4NNfsEHyZnzGPwM7ZPsMeY3/y\r\n9cO9MKeT4/UNBAsIgLM4AW+Qawx3GHNJMTXTuM0XceaNCcQbxwZS/5+/B9oQ\r\nvJ5/cE2HEJnApkubL0M/qx8RWc2trs/mWPIuG4qhM7bgW6PnaPo68xTOUBlN\r\nkVGSFY0UYhltalAovJw2dUhgUQeFm1GpXqLVlu2Ec7e4hjcD3XQX8wHOTsEP\r\nUG15rMLD+1O2TOdyo7MtZM2OjD+rudSpxSa2EWNp+L34enATFxg93KR1nXHf\r\nDd675CM45I98fJ80/Y//dRO/RL4ZmCNJOvqoCpLhM8ouTt/O2AXQErnJ/D39\r\nLbMmPVqWPQ4qz2mJj8PSojRORvP1vpWfTEo=\r\n=O8yl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/types": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.21.4-esm.4_1680621197143_0.0665450425313554", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.21.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "9bb42a53de447936a57ba256fbf537fc312b6929", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-wb1mhwGOCaXHDTcsRYMKF9e5bbMgqwxtqa2Y1ifH96dXJPwbuLX9qHy3clhrxVqgMz7nyNXs8VkxdH8UBcjKqA==", "signatures": [{"sig": "MEUCICCU0b3g0pmV8gU787ahTEChtfVpNTurh1TI3eHAEKWrAiEA2hnGjrcfsppbaQMFn8l/cA99vlldpuLhZKGSldnpOmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaPg//Tzmx0D+vXJmYbMHXPaz+zi46xQKz4SeyfmsaqozZJxUCY7wm\r\npRSlWd/jDjxMCuwzcnbd5cGjB3oDZ0AVLwW2ig80d0XS5cnr/bI+CZ0pLH2L\r\nCj5j80sswzH64fmYVswJ3CFoWmWtqXdt7QhaPOYA9GhA0hN+yrscZN2y1aMI\r\nIJe4Y9i1j7+KgLzBtOgsLs9odSX+XOno8EhRL9ylGCYUJiWolh50EljLd+E7\r\ns9hjhOzU2nnt5A8Tp6pXdZTS+MXcVpT1GzggU//OfLH0a4MzUiIsV/DfOweH\r\n60LRHahjfJx40CPEfBkeU7x/sJX8k92u7trGucyF7Pcm/TpigFWQSRR6lz1Q\r\nmyd4SBsCpkeiGvZEC5KWvgiFKO4mNjKEGy/lmbxya9T57s622Ch/xLrdqQ9Q\r\nV8eE11h+uWhKm/tYimx9gkYHsMV28FKpjq0Qeor9PpmmFLTIoLW0OzQWKVAX\r\nNFD4a+mGQw2fsr3szwBkc5EPvdZ8uMc+fUzYSf0OrewDBwYi32eOydvQiV77\r\nriiSfrTYH/frU/TOR7tHct6Bp1S8b9zLpAbKAGH4RvvaPDc6AtkjxIg3ce7u\r\n3OSsKDKP1GXL7mujk+ogZ7NSo7aewD2UShPbjN2xuV2F8LW+kt3fTnQNSo+l\r\nFBw1mxkT04DWFgBBeafQP7j3hGcMEjvq3QM=\r\n=h6BX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/types": "^7.21.5", "@babel/traverse": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.21.5_1682711420027_0.11976407330075656", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "e5ba566d0c58a5b2ba2a8b795450641950b71958", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-26lTNXoVRdAnsaDXPpvCNUq+OVWEVC6bx7Vvz9rC53F2bagUWW4u4ii2+h8Fejfh7RYqPxn+libeFBBck9muEw==", "signatures": [{"sig": "MEUCIQCLfHjugMM1Wba+Ema6YLSdic8o25TAIyFfJhG8cefc4QIgY0H0tnGnTKSCmQrH/nweg4FfHIwMmFFdMTFOxgR0gi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5540}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/types": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.22.5_1686248473693_0.9320108502925943", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "ecfc8fdeac713026ee75e84a9c7d9b16d6b3b0d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-DFTkGvZVWwz27vTsp5GvAb3WdYfcA3dCyGY+BaUlHxxaVXkPcwHwdwjhdL1ApLU4sxp3CrjxbOyb7n39pC6gxQ==", "signatures": [{"sig": "MEYCIQDIywzUOl4fy7W8X92xPr0T4nDqupiRTagWr7ncFK6enAIhAOxHFIa199HU+Jl8NlgYuHbD84QM550BW5NBu5bY80d5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5300}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/types": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.0_1689861587657_0.3206475914233051", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "f2bb643708c9d984a97ac750003d4dc07fa9b3c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-e/6Eu1r7gNGCxtg0GQ1NTJI5lQUVYMyP3zeRStheArwepw85o0Zi+g4hjUarrqKi0HeF6+331FfYsVb+ethJ3Q==", "signatures": [{"sig": "MEQCIBlo51QQ5Al/zySWVec3dGuIEGuEc4jC561cAXsddctAAiB7kqH9JzUagskSxE2/2GcQbyZ+5RME1RUa+BUc81EyVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5300}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/types": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.1_1690221092113_0.11772748991473381", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "7a4430b9b2e2ff7e38468dda1ea6c8bdb9d22c9c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-/qguN9TgY/JGgnTkh8uxveuHKLMZuDBmkxtH6n78q06zfnqF302N7QfFkUKXhUhU4rMPtv5NPOH+8Yo1F5JD7g==", "signatures": [{"sig": "MEQCIDbMjLn6SC6G0w3FfUuimholmCExlp0hob2NNSTbLzSFAiAjFioftkXJuBqxlypeyDcfjFKB83QKG9Zj6AXIDD+WPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5300}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/types": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.2_1691594088114_0.12027103747383716", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "b3835501a183ba57d14207bb6023c083165fc6d5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-LSZ5paGAqYBccKQPK91JphfNeMNVFMs28jK3uJo8MEajFTehMTpc/25EpkqJqXGRt3rvjhsUXDc0R6FUO3hkww==", "signatures": [{"sig": "MEQCIFn7J3KjArGeM4d13SAD9KUdyVo+ExzhRPrQf2Zc78AdAiAbxL4xmd4F/7bF8z9nHBBP69yr2YeifQ7F20xsVRkMrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5300}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/types": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.3_1695740203777_0.9196930488418029", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "3272f80c3b7215ffeaaf1d44897a3606e50e347d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-w7Gzdh7ST3xklAGqhQ/Sw0VRM1gqcRIiem7l85Ff3aa/Bfbr9SdD7zuHRZ7sPQHOQTiibM5a0uTXBR9hh3VwMg==", "signatures": [{"sig": "MEYCIQCunbDaMg7i0oOsRxabIprFzo0axYle14WZ596KCUuX0wIhAKXLCSt2JYxGhtlQnbG9mNrA09F6rsbNKS2rj049/Vlz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5300}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/types": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.4_1697076369295_0.8830572538137682", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "94c6dcfd731af90f27a79509f9ab7fb2120fc38b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-NzQcQrzaQPkaEwoTm4Mhyl8jI1huEL/WWIEvudjTCMJ9aBZNpsJbMASx7EQECtQQPS/DcnFpo0FIh3LvEO9cxQ==", "signatures": [{"sig": "MEUCIEPN95gWeSCsYixfBA4oFhqZgEnfHpaPG1gMENYB9WTmAiEA2uN4QQENzlFt2u0mXz0sGz4f2emwzK280mE9vjxPwRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5619}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.23.3_1699513425209_0.46613620803238565", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "fca6e417b5427411339e0e10219189e370e431e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-Un5lqjDx94cXfcPbsGQfQCqkDxSTv2U3Io+KnAtOeStc50/G9Z0KMZvmiAboxvDaY6ky2xNF3mSj1g30on5oag==", "signatures": [{"sig": "MEYCIQCNzl1ddTMQSDUga4PSCURXrmYhO1nPS4shMgBRBTxs7QIhAPKk1ISi8wKPWRZJ/O3lyqRBhNtecjkH8RzzI75CKQ2P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/types": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.5_1702307911848_0.6769880828486332", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "2ac3829d24d9d25e4e9320a21538fa58df918e2a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-YH+nbNRPpSANnTxmyX8B6lK09J/UWu7UpIFGioe+wObflQGABiVrjQXNmw2ZgDQLvd8JriFipiT8zhPpjBa9MA==", "signatures": [{"sig": "MEUCIQDn3I3IEpIHdgz4Ee4kwgtgNGX+NOgDO6ShxJrqUy+mrQIgGay/ebooUwcJI29zP7fF02einseq5mid7lF5W5Yj1HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/types": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.6_1706285633705_0.2664947990775792", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "b26d8ddeea4a165ae5b5c75e7ddc0dd0bbfedbbb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-nyxAY/PEyfRisyfIhMNENUI37haJzCqFKLSGqkL9Au5ELoUbNlJ/i3c3CMx1emf39RrhWDuwDTmKFCrB/pgFqQ==", "signatures": [{"sig": "MEQCIHs2sPwRvX3kYLWx5LJ43qbTy/AhF0Eoaa5G15QdgjeKAiBznkNlaBDrdEg+YKBHomLELkUkqGrdrj0DBTPfw54KNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/types": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.7_1709129082591_0.9823690217911711", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "2bf263617060c9cc45bcdbf492b8cc805082bf27", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-ngT/3NkRhsaep9ck9uj2Xhv9+xB1zShY3tM3g6om4xxCELwCDN4g4Aq5dRn48+0hasAql7s2hdBOysCfNpr4fw==", "signatures": [{"sig": "MEUCIC04Qjc3V1nn8whLhlap9Sk24Qinc2hlloO7zKDfRbifAiEAlBTTteHIYZ8FTI5yKSzHHzDYDPz/B3ntusf9U06hlnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5550}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/types": "^7.24.0", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.24.1_1710841706555_0.9187959379458621", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "049783b63cddeaa6b53057a690eecddfcea513da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Yv2Y69tRSzKuext4TPNUen4Uo5Kyi3w9HxccKkDdmw9mKj6KERIDzTt91WnaeUA3nsLMAj4I3p2gh2eFV53+rA==", "signatures": [{"sig": "MEQCIHIPYILKw5LbzIYKeDmbHCC1NCS6kL/Sh4Zfq7X/zqyKAiBnrhA+3PFjU51J+/em3azZ7/yGuWrfxn8SHwZ7qgbBvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5327}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/types": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.8_1712236783837_0.17718638075513793", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "93607d1ef5b81c70af174aff3532d57216367492", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-jSSSDt4ZidNMggcLx8SaKsbGNEfIl0PHx/4mFEulorE7bpYLbN0d3pDW3eJ7Y5Z3yPhy3L3NaPCYyTUY7TuugQ==", "signatures": [{"sig": "MEQCIHhwhoTKKwZ09hxpZ+Ihb9fSAwvcEGqiCpNNQRfJvP69AiBAk1yHxc+wdgmy9bqlVOMjNykGaLfAxKhOg+9aO10ZTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71473}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/types": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.24.6_1716553464600_0.2853182776469676", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "e4d303d4f33373ca5e5d61e591e9bd1d662b9aa1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-CJTKYoyh036HHUn7vxWZTJ2BldoBK5oXdx3sdZJfhVl0uKOrvdssAE9359/zQTAU8y0mLWTM7OQWTopW+EZtuQ==", "signatures": [{"sig": "MEQCICGFvQLqNyC5TwWlzjTxPzBZCojrOepLwr1QZ88fr+vlAiBrTLt3y4Jofc/odR3aI6v8H1pNoXVTr80ZQ5g4cxfB/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71585}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/types": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.9_1717423443557_0.5436405870726104", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "a2347028e317c85afc2b83fabfc8aca9729bbdda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-sXrz85AY1m0aY/bxhYru9rqcu73mcJ5QsgR+HmAYH3PnSpk0bV/s+mzaL+RBYsv8d/MW9V9bvAATMmO7QwrVdQ==", "signatures": [{"sig": "MEQCIC/8yefeaEXy1xI1OldBFBwmZygaHI5bUVo6/ilTKM+ZAiAvTRMumuSeSow+tTptviinirPP6y+oCWntzK01r5wFbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71594}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/types": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.10_1717499995284_0.8604050806106591", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "4f6886c11e423bd69f3ce51dbf42424a5f275514", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-Dt9LQs6iEY++gXUwY03DNFat5C2NbO48jj+j/bSAz6b3HgPs39qcPiYt77fDObIcFwj3/C2ICX9YMwGflUoSHQ==", "signatures": [{"sig": "MEUCIQC1rnLfMrMnVnKrJbr4r5OhCWj8LC3NlPEytwbW+zIQ8QIgQwv67vKRk+m2pklyd2wlbJDo5NgDQDCdeUDPkVUQ9xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71469}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/types": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.24.7_1717593315448_0.8062476237906033", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "c0bb8c1211aa05906f5320a561a74d5a94f81ee2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-14S/w/ejIL9cvN9NQywhoZhwuwvP9V1IoRjXDO8hKuUbdioDOoTd4kL64gkU25C8SgSzd6kYv1NUV/8fVeEtSA==", "signatures": [{"sig": "MEQCIDeYRpPi571W09LpcF2biqKjT0kb8LtMtJ5Cig8e7k/rAiBSrfN9/FfJR459BuqnSvtbIRe97UIS5YBDhTwl9suV1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71483}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/types": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.11_1717751725129_0.7190137729116188", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "17fd1f9c14816ce750beb77b858175a8e5316830", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-e0qV/TOdz0tq9CxLtMuKUcmqRm/H/4NPgbgrU+JNcLqyllAIftZCHCEtGAh7DK7/j0FD2CCgga6/AS4YyEu1vA==", "signatures": [{"sig": "MEUCIHozBySDj/+juvxhQTKmSX+PXEmaxtOnUb9sDOWN4jfPAiEA9AF0vXHPjIIKKAyzxTRbtn9UVli9xY/frAG+v58yyF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68275}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/types": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.12_1722015200818_0.005884615258684445", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "1b9ed22e6890a0e9ff470371c73b8c749bcec386", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-EJN2mKxDwfOUCPxMO6MUI58RN3ganiRAG/MS/S3HfB6QFNjroAMelQo/gybyYq97WerCBAZoyrAoW8Tzdq2jWg==", "signatures": [{"sig": "MEYCIQDkBmOeYSQFNhx6HrufSskuGR0N6qhCZgM8K/OQBFJ/SAIhAK2IKOp+1S0dFpwJfJWgAiixvUa40T2yNVABoEOQnbyc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76007}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/types": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.25.7_1727882079891_0.8573867390275862", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "7821d4410bee5daaadbb4cdd9a6649704e176845", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-6jmooXYIwn9ca5/RylZADJ+EnSxVUS5sjeJ9UPk6RWRzXCmOJCy6dqItPJFpw2cuCangPK4OYr5uhGKcmrm5Qg==", "signatures": [{"sig": "MEUCIQCw1LiK6GDYw79Y4PtcB18tFdOnJf4ugpEdQSLcPyf2VwIgVUbEKoDenuvmrc5S0Z1/RQD/d99SRwu5jtrfxNjCYOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5550}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/types": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.25.9_1729610457567_0.10980983176854542", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "925b2b04a0fee564247bab54b5cc977addc5765b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-JTqt5ce8iA2Bs9mcgbAsMJGOm1Omjerx92CsnnQjAeQmKYzV6Mxynbe1fms4hiNmFvEP+Qr1OBGJE/44nwVJWw==", "signatures": [{"sig": "MEQCIDb0+dZntBniX/SXI8/aX8VLhenEm84KXJVMFJTR3MfkAiBYcSXSGc2K2+Nfh3SPml8fLQEkneDoru74MhnMfiEQ1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/types": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.13_1729864441573_0.45745936643270535", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "f4eb6fa4a837ab8155e19174395fea64a9ccfa8b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-XL8E7DzxXibaE0nXgsQcfQlXSvAOjEExWHgyf9+ZXa/9ouYszSEGQhVDzA1FZmzSL6kuF0CdWxAenXFDpfXa6A==", "signatures": [{"sig": "MEQCIB1TrsG0yoKzjuhosmjS4p0nzvUk3D1X/CuD/2cr/xKoAiAnaOpl7bLsp06Kl0GPUR6ZPbneAr1DlExZ8rljiWtICQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/types": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.14_1733504033043_0.23180304583184652", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "7cdd05f27c2367bc60ee522f2f2f3ec8f5feb6b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-RY84jVlB7WPJnxSCl0jRJV39GtDpV/1VuyKk56q5DN3GoZs44rGmhpjHIkTQKnImxEJ9QpPpPqmy257q1IC4Dg==", "signatures": [{"sig": "MEQCICFAAYP/eWCVhEZBrG/hs15H9/GKHmB8UD9JJxFco1jCAiBS6KczBlZfGzhWY0rCdfpQP0oGIFdtTTo2z3fFC66A/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/types": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.15_1736529858096_0.8927561682759488", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "cdd90a58a23f4967ddda1ce232c625397e4bec84", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-c6JQGru6ZbjE4VHJyQ+xm5YkIF2ukzx5ns7qdHNp+n/fKzlgFHozVVvEEu1VkxGl4I5qqGBOaJbibDioxXCljw==", "signatures": [{"sig": "MEUCIBdXCB8oZ2vDQNTe7H0HEXPNpvbyMJ2U1qGxoSsKymBKAiEAnhdb3XXFVhmezigFNBsMT4nCJhCWuDmK9TBOmvHidYU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/types": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.16_1739534334783_0.08801157669354742", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "ec8a0d6251a5e8ae100c17464cfb59b13d9b2273", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-7k0PRBXuZ4TjHRo70/I6mS3jcWl9h44Y3h20fcs+wek1ZjUFp9j76HyMhbWzEeiE1XBLCR7Oh/5/Nz45BcUC9A==", "signatures": [{"sig": "MEQCIAwcV+0tNTRv3UpUccy2UmQKxRI9pX+meYt7cR27LK4jAiBlirG4D+uGjgN5kGY0Ac4d0Ws1hgEDrIAgT9Kd0SHecA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/types": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-alpha.17_1741717485760_0.21087411881027474", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "6e2061067ba3ab0266d834a9f94811196f2aba9a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==", "signatures": [{"sig": "MEQCIAdwjBuHgTWj7CCEjNy+06+MWmIv/j1ASPzwk1w4U8glAiABKVVZZ3k+h9OjrXU7ACfokQTTgXlrQ0q+GJD0LjO6YQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5550}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/types": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_7.27.1_1746025724181_0.03159304729947432", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "dist": {"shasum": "1a92a6baed831934143387ee8b62488d6c8f6c35", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-UxmA38HrHdO7plqKIjieyzdcZoEEeqQ8s8MOgKfCs2zPDY+LrdHAWRA4yJ1HSQBkl81VZxryl+OC67pEdVBeFw==", "signatures": [{"sig": "MEYCIQCOBv7HE13w+RvKQsz/wZqUiwJNs6J7ylWhgIR42gmFEAIhAJNTyvM6g2+yPQsKZDb0yLRT0+cBnWTgEy1bf+Ff6Whp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5660}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/types": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-arrow-functions_8.0.0-beta.0_1748620255974_0.03709496679306912", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-arrow-functions", "version": "8.0.0-beta.1", "description": "Compile ES2015 arrow functions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-arrow-functions@8.0.0-beta.1", "dist": {"shasum": "6635aa20ef4a10161919f3e71d25df69a0755f3b", "integrity": "sha512-KEvoVlNPsQ87tjnQPe6lei1kprKJ0Hu5lug6y+Ht4xB5pzVLYzW7d6N6buTyD2UzzzEVbgp6kAZq8ygtDARP7w==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5660, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHWnfgahAPjD8u5/y4i2UBnGqDMztpVhi+M+hAKBPYFgAiEA/XWKEkhUp/W7s8WziiFLZUCA0OPxXr2hmzYMBj4kHMY="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-arrow-functions_8.0.0-beta.1_1751447050036_0.15737793296042812"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:30.466Z", "modified": "2025-07-02T09:04:10.450Z", "7.0.0-beta.4": "2017-10-30T18:34:30.466Z", "7.0.0-beta.5": "2017-10-30T20:56:11.501Z", "7.0.0-beta.31": "2017-11-03T20:03:20.600Z", "7.0.0-beta.32": "2017-11-12T13:33:09.352Z", "7.0.0-beta.33": "2017-12-01T14:28:12.893Z", "7.0.0-beta.34": "2017-12-02T14:39:13.287Z", "7.0.0-beta.35": "2017-12-14T21:47:39.704Z", "7.0.0-beta.36": "2017-12-25T19:04:29.588Z", "7.0.0-beta.37": "2018-01-08T16:02:26.386Z", "7.0.0-beta.38": "2018-01-17T16:31:48.458Z", "7.0.0-beta.39": "2018-01-30T20:27:30.029Z", "7.0.0-beta.40": "2018-02-12T16:41:29.119Z", "7.0.0-beta.41": "2018-03-14T16:26:01.211Z", "7.0.0-beta.42": "2018-03-15T20:50:37.876Z", "7.0.0-beta.43": "2018-04-02T16:48:21.024Z", "7.0.0-beta.44": "2018-04-02T22:20:03.241Z", "7.0.0-beta.45": "2018-04-23T01:56:37.047Z", "7.0.0-beta.46": "2018-04-23T04:30:59.435Z", "7.0.0-beta.47": "2018-05-15T00:08:38.813Z", "7.0.0-beta.48": "2018-05-24T19:22:05.656Z", "7.0.0-beta.49": "2018-05-25T16:01:49.921Z", "7.0.0-beta.50": "2018-06-12T19:47:12.624Z", "7.0.0-beta.51": "2018-06-12T21:19:41.610Z", "7.0.0-beta.52": "2018-07-06T00:59:22.761Z", "7.0.0-beta.53": "2018-07-11T13:40:12.606Z", "7.0.0-beta.54": "2018-07-16T18:00:03.372Z", "7.0.0-beta.55": "2018-07-28T22:07:11.068Z", "7.0.0-beta.56": "2018-08-04T01:05:07.869Z", "7.0.0-rc.0": "2018-08-09T15:58:02.156Z", "7.0.0-rc.1": "2018-08-09T20:07:44.765Z", "7.0.0-rc.2": "2018-08-21T19:23:40.597Z", "7.0.0-rc.3": "2018-08-24T18:07:38.452Z", "7.0.0-rc.4": "2018-08-27T16:43:56.274Z", "7.0.0": "2018-08-27T21:42:55.645Z", "7.2.0": "2018-12-03T19:01:06.756Z", "7.7.4": "2019-11-22T23:32:05.065Z", "7.8.0": "2020-01-12T00:16:24.919Z", "7.8.3": "2020-01-13T21:41:32.738Z", "7.10.1": "2020-05-27T22:07:15.630Z", "7.10.4": "2020-06-30T13:11:54.023Z", "7.12.1": "2020-10-15T22:39:56.906Z", "7.12.13": "2021-02-03T01:10:46.104Z", "7.13.0": "2021-02-22T22:49:54.096Z", "7.14.5": "2021-06-09T23:11:56.663Z", "7.16.0": "2021-10-29T23:47:28.633Z", "7.16.5": "2021-12-13T22:28:07.457Z", "7.16.7": "2021-12-31T00:21:52.260Z", "7.17.12": "2022-05-16T19:32:44.731Z", "7.18.6": "2022-06-27T19:49:58.211Z", "7.20.7": "2022-12-22T09:45:25.978Z", "7.21.4-esm": "2023-04-04T14:09:21.321Z", "7.21.4-esm.1": "2023-04-04T14:21:09.827Z", "7.21.4-esm.2": "2023-04-04T14:39:13.313Z", "7.21.4-esm.3": "2023-04-04T14:56:04.941Z", "7.21.4-esm.4": "2023-04-04T15:13:17.270Z", "7.21.5": "2023-04-28T19:50:20.239Z", "7.22.5": "2023-06-08T18:21:13.883Z", "8.0.0-alpha.0": "2023-07-20T13:59:47.896Z", "8.0.0-alpha.1": "2023-07-24T17:51:32.271Z", "8.0.0-alpha.2": "2023-08-09T15:14:48.339Z", "8.0.0-alpha.3": "2023-09-26T14:56:43.904Z", "8.0.0-alpha.4": "2023-10-12T02:06:09.516Z", "7.23.3": "2023-11-09T07:03:45.438Z", "8.0.0-alpha.5": "2023-12-11T15:18:32.032Z", "8.0.0-alpha.6": "2024-01-26T16:13:53.904Z", "8.0.0-alpha.7": "2024-02-28T14:04:42.749Z", "7.24.1": "2024-03-19T09:48:26.716Z", "8.0.0-alpha.8": "2024-04-04T13:19:44.008Z", "7.24.6": "2024-05-24T12:24:24.833Z", "8.0.0-alpha.9": "2024-06-03T14:04:03.706Z", "8.0.0-alpha.10": "2024-06-04T11:19:55.441Z", "7.24.7": "2024-06-05T13:15:15.583Z", "8.0.0-alpha.11": "2024-06-07T09:15:25.312Z", "8.0.0-alpha.12": "2024-07-26T17:33:21.036Z", "7.25.7": "2024-10-02T15:14:40.137Z", "7.25.9": "2024-10-22T15:20:57.844Z", "8.0.0-alpha.13": "2024-10-25T13:54:01.808Z", "8.0.0-alpha.14": "2024-12-06T16:53:53.220Z", "8.0.0-alpha.15": "2025-01-10T17:24:18.282Z", "8.0.0-alpha.16": "2025-02-14T11:58:54.955Z", "8.0.0-alpha.17": "2025-03-11T18:24:45.964Z", "7.27.1": "2025-04-30T15:08:44.359Z", "8.0.0-beta.0": "2025-05-30T15:50:56.172Z", "8.0.0-beta.1": "2025-07-02T09:04:10.204Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "description": "Compile ES2015 arrow functions to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}