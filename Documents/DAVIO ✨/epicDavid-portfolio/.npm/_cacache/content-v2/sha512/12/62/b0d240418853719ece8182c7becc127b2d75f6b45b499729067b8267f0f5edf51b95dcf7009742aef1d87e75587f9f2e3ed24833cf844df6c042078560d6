{"_id": "@babel/plugin-transform-new-target", "_rev": "118-47e1558c484115a83342439b15ec2188", "name": "@babel/plugin-transform-new-target", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6a393bab873c77ee30c397aeb08499243fc4a5c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.4.tgz", "integrity": "sha512-p+ymH5TuYIZnTZKzWPiWp1fZy/ydbSaffOfGyPlMktGv2JUmpx3MJ3nPl2yZJVL8gi6RRsvRC/Cv5+VNfeeKTQ==", "signatures": [{"sig": "MEQCIELLSVYk5WpkYRA/V+Cn6ENRUKbGRKg2jJNS8TOFIm4+AiARMtWiv0fVDF4BfOdAhV2PPK7z4d1csoNUMxUbeuhhcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4", "@babel/plugin-proposal-class-properties": "7.0.0-beta.4", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.4.tgz_1509388480349_0.5587993196677417", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f1333a9f4814b6ec5a4d9ac175004f71bfb981af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.5.tgz", "integrity": "sha512-V/MokHF4C0x2s0vtgHYEnGP3rMt6F/ZJW/dN7adUQ7ZULBrD+fr65dGu5+9Oue7fbt62hFT/lAQJ/UfpGCo5Fg==", "signatures": [{"sig": "MEUCICPBGPdy+dp5/U+sy4flbWlwD18NaR7IyzS26piuc7oTAiEAin/zHsKk0UdEY1udd48Zldu2Ej+qQan/YNE6rZuv6zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5", "@babel/plugin-proposal-class-properties": "7.0.0-beta.5", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.5.tgz_1509396979743_0.11355559341609478", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a8b230b56a853dc895422f77c32c439ef5d99ecd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.31.tgz", "integrity": "sha512-k5sEhOi8NUohYPsuzPIQ0WsocZ2SxfvKWI7wG0ly4In/YY7zQBlOt2xjV9HEL94Rn09SPoFYz1YpIGfP47R49w==", "signatures": [{"sig": "MEUCIQDhCngrAj1Al6OMVfXIVe4tQL5RFzWCgMdYNZNfE8XwYQIgTYZcxkp4aWGjw6byhbDfWCFcAe9Azdkn5cb4Mmoduus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31", "@babel/plugin-proposal-class-properties": "7.0.0-beta.31", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.31.tgz_1509739405987_0.09277347405441105", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a46302c22d9ace6a875215502cad7de7a67b978d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.32.tgz", "integrity": "sha512-TC0RqzMvCK8A5/kRzrQItS4SiiNOev9Bui861PDNAQBNIJvv2MyPUpOcV5VyCsiGIw4XMDMfcU2DngfzIWq4QQ==", "signatures": [{"sig": "MEUCIQDe4ueVcDIIU2dEXjjiQqEFHX9M1x19Lz9IrJfim7OSAgIgAj4TQTkw0HYKraCl/SOX7RngwhdFBqTom8EFt9iZUIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32", "@babel/plugin-proposal-class-properties": "7.0.0-beta.32", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.32.tgz_1510493595735_0.5229132522363216", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "55fdedac83b44ccaa565288942e826c04adf78ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.33.tgz", "integrity": "sha512-JHCF9tE3c9WcPO3ab1eBXXNGrm5oMMscEe6msTzHxg5E75BKalqArHeXj1x/LFXaKO9UnvbUoJP3sM+90PCPpA==", "signatures": [{"sig": "MEQCIGAMJl7WeDyHwugW0epnIxwbPgojb2A8o7HBu0Kbq3uqAiBGWPoY1RE7dNARhvKohmCxLv+vafXqLitzrMjqRKit3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33", "@babel/plugin-proposal-class-properties": "7.0.0-beta.33", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.33.tgz_1512138498159_0.4732689505908638", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d3647cf5fd9b71dcea5325bcbca3995591e04f83", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.34.tgz", "integrity": "sha512-HC3FJS9k9+KU7ReGah9Ii7ADdF8NNP4S0yHtmp22sb9MJOCahRMvjqdvaLAGomUMEPMeo/F0YVAJPdgNWflX3w==", "signatures": [{"sig": "MEQCIEJOR23jLQ9ACXdJBdOntiTrPRrRkmlxCduA9eO4thCFAiA7FRUlsvTh6J6S+0VAThsSISV2LVIWE8Fys0e/+Ow+Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34", "@babel/plugin-proposal-class-properties": "7.0.0-beta.34", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.34.tgz_1512225559927_0.22886198153719306", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "457fd711ff6d554c9cd6fc9d5e139e0375739d17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.35.tgz", "integrity": "sha512-d/OIA9VIrXHPc5gMqJa1KKIHGlo8qmpIIQJ4qF9TUeTBoRfSyjI6R656d25pjeaswIKndKTtdfngFRhwguXBSA==", "signatures": [{"sig": "MEQCIFWiMbsGoXTl55JPwLE/ctUOrwM7BTkPmFQwm58DC8fzAiBzhlbyvkd5ojY6rMf0Ah6JV/bhVAtXL/IBLh8yJ7xu6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35", "@babel/plugin-proposal-class-properties": "7.0.0-beta.35", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.35.tgz_1513288066968_0.8844071230851114", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a6fd1cf2ae4a4efc1f0254197b8efa66228558f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.36.tgz", "integrity": "sha512-QJ/gN712/zKFQs3KdG5ph0nN9hHSfxrCY2fkEF2w+Uhe219qGqGuCTpbwMRJHk7KzeLgW8WmBcmikMIku+MeLg==", "signatures": [{"sig": "MEYCIQD9DUxqwE+KGtwuukvtNRz+GBEUPgn2SPl3SYF4JqYI7QIhAM9A/VCfAxxADhZ70Le/Afs9CsZVIso3F1GgR8CGrjuU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36", "@babel/plugin-proposal-class-properties": "7.0.0-beta.36", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.36.tgz_1514228677253_0.04024744685739279", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ba6869bfc8ffe1d409bc12eb4a5266255a7f8ec6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.37.tgz", "integrity": "sha512-5j35s6c0B7T89FQIf4q25wBvjXoH3xGv2/RZb2T776RsZI5gTFH7SGMVY1zNtoDBHRBSp+RXsD8cb/O6fETaiA==", "signatures": [{"sig": "MEQCIBVhnAtHpqiArN9HwIplfrckcuJZPzFJMWSm2CPlFFxDAiBpvfbTvTllDhF6greGesTdHoUTblgNVXpHceATbHjP6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37", "@babel/plugin-proposal-class-properties": "7.0.0-beta.37", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.37.tgz_1515427350958_0.30571160884574056", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0a44713d73993063d9166a9238ed5fe1419e7a05", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.38.tgz", "integrity": "sha512-6xzyPp1GgyB0nNtsX9GmYWGVEOCkSIuTf8yJlGp/X/vrWNNxnlYl0D4IqySerzkHo2bABCES7sw/5A2ZYVRhtQ==", "signatures": [{"sig": "MEUCID7xi7roiYA2xhT36lsa81YLhRXAvIvHpC4rhsLptn3QAiEAmdrvBA/ukKlPsPlHjVVqENMPnxlSqFgNoAr00lbpnY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.5.1", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38", "@babel/plugin-proposal-class-properties": "7.0.0-beta.38", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.38.tgz_1516206716031_0.6490368079394102", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "deaf7e20bb67f8b8ea6fdd4b2b4c51867d450c0a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.39.tgz", "integrity": "sha512-TGFYbCPJZ/dxfirv4qqJvGJyQCML5RAOPKQuMO+q82a201IZemukue5ccR/qVQF9pId6MR/cAp/M0g+JBosLeQ==", "signatures": [{"sig": "MEUCIQDNT+7SCMG2bIxkG9m/vSfcD/t+yAlq53osjlkmIgkCKAIgQmmvIu/pG3zgvnXn6Ni+asNY7ZdyM9XTEh/82QzsHQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "7.0.0-beta.39", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target-7.0.0-beta.39.tgz_1517344053560_0.2782378043048084", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ee52bb87fbf325ac054811ec739b25fbce97809e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-t4ivwZVGrVf1bhLgHcgaLhFH4loZhV5WmEKKNPEe7QnGikJBibrLmggOM1w5s6BMsHj03+j0rxUmcKLmGlC/fg==", "signatures": [{"sig": "MEUCIFTTL7dLgsZDjk1+ezi5NVvxT+wtPCYxJEiWLJSMoa/zAiEAm/9+JDR7cl3AC3v4N0yNrmdCHzizuo00AxkKMvFnVvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4283}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40", "@babel/plugin-proposal-class-properties": "7.0.0-beta.40", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.40_1518453696578_0.20940456683001085", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c0d4549ac4c740bbd4595ae5bee7ab7d959e3ef3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-K<PERSON>UtXKD0MqoZWE9qHHrceMLLHpBkdDBbnoVuMCTA/m0ONTCHc/N+gXMkX1SyQF9yKunxLm6kH421XCwrQKGVhA==", "signatures": [{"sig": "MEYCIQDo0HvrUmluTNyIUO7fog6e1QdYsAS/msFdWo8a/1is+QIhANCEr7vbUuE1LO6+0/C7PbrWHFe1HOppaeP7tsspa93Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4518}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41", "@babel/plugin-proposal-class-properties": "7.0.0-beta.41", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.41_1521044769751_0.4395828864934841", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8b309b67b6a92fd1ab6cb93bea0fa12359795c20", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-P2zwIIeTOev0Xjo//4p7ugtsWazIsycbs/T6I/ghiV2lREFxo9s93fygbXNXt5ALRPRWDthiAYIDA9n1vrgkVg==", "signatures": [{"sig": "MEUCIQDktX8uVunuqPsHzK1GLJl1skyEkoucpmF8Yro5wmEmMgIgA7AFkw1xJlxkENfcESe4q90S11RVdvxgjh2IddocWkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4518}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42", "@babel/plugin-proposal-class-properties": "7.0.0-beta.42", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.42_1521147045464_0.8952605582227162", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c59818b73616d7a51670c5e798ae263a5fccfb30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-67gcEBWPoQGyvZGS5KxQVe4iRe281851eT5AW4r2o7sros6sMSy6nxWDLdFv9/3z1AXogSoN16Fg0k9HmC70kw==", "signatures": [{"sig": "MEUCIH/ZQveqXdkeuuj84ymIMHYTGyvhpc4TVaMUrS87cHHNAiEA0r5A8GO2AhsMmGYdius3vhtiPI5RoomjAOV3p1AR3/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4767}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43", "@babel/plugin-proposal-class-properties": "7.0.0-beta.43", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.43_1522687706332_0.1495406402488193", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7f3a2c46e01b5433093430892fbce287583cb1b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-clkSQygTIEDo4GCKBI4FXvbqBTz9Mnp6gLLlYodxua2sVEAym877k5ndvoBjXT6Gvymq1YoO4Eeot8x7rXNPFA==", "signatures": [{"sig": "MEYCIQCpuoprc+7Slh35XFGLlDeasTzarXjz9WTjc0AfWb+whAIhAOuTPkUpm+oIMrrnpdPI1GYO6GgMT4BE7elHzikmJL+3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4778}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44", "@babel/plugin-proposal-class-properties": "7.0.0-beta.44", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.44_1522707608524_0.2612424774499451", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cf6d1b8fb9e1d0119964db22c725cceb44820023", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-4uHMwHxyy5CFC9nR50jw9RszIF0Q5aUc0sK+V5jGcwy4sDtkVNjb7ihDqj/LWlk3+RzkdVihhINY6xqEn742sQ==", "signatures": [{"sig": "MEUCIGBY5/lg7+vHBZAurAjMxIZbneMsCq8fybHpNPRXU+fqAiEAtvftBDrkuWtjPHO7DCElwPZX8T6M4IsK4ieB4KgOIHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1pCRA9TVsSAnZWagAA2BYP/06WL/kohQDrrVJjg/zx\n2v7fqWDBnoUZtAdb9H2FWPG6UFEFJWKQrIaxzcbKFei5JRziKswA3IMeT9G8\n5EsxIZq0CRxb2sINeAJALnoIkvQedcgqRn+wOu7tysvuASiYXjDfvBE+IiFm\ndBYYSYENG/AHNp8tp8Ze5JQDA6srVQ2zmU47tSsUjkUbWe54AqiJWrqSpfs7\n3/xcYJUYIiDYBIT51UH5j4UwH6R6PUiWMMQefYT4r8wgXjrd0o0jCiWVMq9U\n7CNbcltpmQ8nG7nlbKDW337X+lzJR2+jZ3MQHnH5FonAm3gbmSskVWQqeLbV\nbAOq4xTLOb+LI5YKjWbsyQU3pJnbPq5/y3RMolo0xZS0UiRRoJ/C+h0hG8q9\nPTVDTl61l0e8Fe/3EXA7PtGYGTdSJTvfi1/dG4Oeu3rWEeV4c1SD20XRKZa8\nR07kmSmyeFvBoXuSxY6Kk7HaJW5ixf/JtcZ0FGv6Dy5IrVCxFdLb7EWyU7LQ\n6Squ3D1+6g47HSoYUQ3CCpW3Qmfq/ZLoJSfJihBWhS3TaDVVTEosMOLKbn2p\nhi05Rofd7pbVLvQdHxnoSwffBInDa6KUViHEE0P5gyJi+yLyuBVKb1SfKMS1\nMl+9LuLyQj1X/OismbtYNvCjxnoywFb1JHV2ObL09qzpkekUUrpqy4D1jTlt\nii1X\r\n=yqIq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45", "@babel/plugin-proposal-class-properties": "7.0.0-beta.45", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.45_1524448616422_0.04635064021412871", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e3219c15a2175a29afa33b9b2f4c18dc1ae3c8cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-VSuIdVFUhlqADj/ymm7NG4BVjGD0sBWWN5sONTLAYzKScGZA58Ys8jSkl1dxeqWnMOEjzo8lTRWxRVvz8HIaMg==", "signatures": [{"sig": "MEQCIDu9ag37XE7evuvgZ1lSFAKaXpajYkvuTpJajZ3U6icbAiAYngIfMblJBhOWKvyI8C7tU/5sxxjz/iEdv4W8QrVX3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGTCRA9TVsSAnZWagAA4AsP/0/75Zv23sI2FK26r1Wg\ntNIO1ZdzFmCEOAD0TjMrKxgMQCqAhaLnKHm7mvmT2AccosdO97V48RmaYRnb\nd8saIEYcD9eqPuNcK/QfpW7Y3B7HHkTdx6NUfp2lIosDjvEipYlx/g6pFPs3\nhugViCkBzFEdpDoulRxO8AUZSICDkHTu9mvl8h+ikvCLnZC9Uycl3a/UAulj\n4KwvhW0xBGpRBA2IkvR3rhJPtVgDfH5Tfsj06FTnKx1vzyg+Dgoc069E03Zh\ntlqlkrHRzhgdHZhoWjxR2JA+/eQLYS1JfPuyn+SpXWQ5Pbi6SNAMUBn22JIl\nHnKvELkDjS3u3/B0lhXoiifYYsykVCFpAufFezAs1sbiWb0oA/yKyxMNbkdd\nSVxWSUIXvrIofXZL/Eff3LUzFx9h3HNaULOLIVlRmUVza08Q3dbGLCx9SNYX\nKKIie2d9iZUtxZ3i0e422p7N9ZlOSItBdnqT61Es+m+NwaZJV2unlEQJthvW\n5jhWUUPYaYM2C1y3i9JiMu+PejS6Mye89WiwMAnxfg8ame6V+zoaaXHvKtpG\nlv1ioHNHxK3CdFzJxBdc1JKcDFOWqN6HMIOYgZQtvt5SF1QQho23BRPYUgaj\nDQkzsQd/Ag6JcwLAHDCxzPT4IhLTAg1dUMvugLxXZBuEGxnjab8qWdzO275G\ny6E0\r\n=wxSJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46", "@babel/plugin-proposal-class-properties": "7.0.0-beta.46", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.46_1524457875529_0.5717024517091966", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4b5cb7ce30d7bffa105a1f43ed07d6ae206a4155", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-2IRZtdoPXJn7KSwrmp0xtLTZ0PnhlQxhcTZ0XZ5wfFXmsZ9vi6AK4whIZ2IXI/c2qrYK9FEYLwR5QRfL5Qe6eQ==", "signatures": [{"sig": "MEQCIHQXZBXAlR84KSK7vs10wiyifOgzMUnQ+eAkt96/bAA6AiB8/NLyEvPXQtjLiqnNIuychxQHY1S2AqboNO5xtsGQkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUeCRA9TVsSAnZWagAABDoP/2oQkcO+qWj0WtBDH9g8\nwP0efOIX+E6iMf/bTER/8pgJZliZLl+5KMMmsGHW9fwmthRSXk/3yGBfL1DA\npluTKQLV0fonUHxcX2A4ptvvtYaRjWijtol0Fh2SWHieFXC3XkEg1bbbKaGE\nmGZQ8kYi+Ss6jAys+lrewaciwZdPbKSpRfYLF/UqhfQPSqwKeHoOHAxlrhE7\npaybYMm/cZQJ1JjUYudIbWt7a82k4sInHtw8JtgtxFSSZpHYBGxUIHY7U2HS\nA11KdNRFrkSPTq/4wWL2305LG9TCtV/lxEO+OI8oxksvuz5fPBzJv+GQum4S\nVv5LwCIxiYaGbMK07qfvwsnsEU4Gn+CKKTshMOY5jFTK4d80s4lMjyQ3n9Qd\nBV1SB8BCSVxNKsP0gQAL2PkAfh4SmvvC9RSUn5BdfOxUpZ9bhzjvLIdM88GD\nUszZctqLJi2NBNkqpSiXQDu4qW5/8+LsG8ic9iA+5igcdlXl8+gTvKCmawu4\nmGaJwB4gW3Ngi3mKEvq2BcT1wF1BSfyWSXPCBCuvcyFesc8mXKCDN1mq/FVH\n36iyk3VMl1EoJ0LU5s4V7r9YZ/MDHwaqw/5/ifVhhg67IpBR3/dIQIY3sC8W\nY7nDHPHPCf71PsahiTfqwr1sWOwI/wobmuPsxGtzApAxvGqwDqzVLje2qgC2\nsJhB\r\n=AVlw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47", "@babel/plugin-proposal-class-properties": "7.0.0-beta.47", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.47_1526342942504_0.5051380574889972", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cfd7303c1824006ac6112fac61044d564f1652b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-xxKdM5RmP73ODEg7Tv9cIx/xC2hq0Z1XcRgr0/d+nQ/XzdBDKq2yLO0UsnPXZ2OyeNjkcHRkJnjk8uNrVmzBJQ==", "signatures": [{"sig": "MEQCIA/JOUrTuWrSPNhxbf0Jbck3m8dtslGuv8sAfQd7BX/kAiA+bwbw1dfbiJK1FdZZEyFht+dnmfTCRIY+tfEksOnNtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxD8CRA9TVsSAnZWagAAbOcQAI17YEn8LVTMuWHvWwT/\nvSZXrGa9VCbmZxoIU6+aqQzqo9pYY0YGcWYCNKEUW+tsAM9KegPqwtS1AFok\nwT5S3aRKJqKGzq83PghYAzbTSgxIUXjAUTAiBY3ABL5/FqcPytCCjC7HniC5\nSrDU7A/6JL4qgTWEQvVDP+cudOZbpolxhp+1TD/CO+MtNJziww+n5wcpBnI2\nZyOm2NsjDzAZ0VBz18w1DUmFl47SQfov19SvT01JPJ6hOiwr0g4D1bQbpNBV\nfRgmVuN3Izc+/ET5XhD5h4cVqkBGysO6iCl6GRu1TZ3GykJ/ZYFMz+hkpPMh\nuseDg0uCOtaq6NTLkAsYpI/FD8B5hmwOM3TmSyLWWO+5aUiTTXN5KpRE2K3Q\npaGnkMHnCIdjkL/mltF3dhkrVOP16wwh8mqTvx2IUHvExrLzzZZQyYD7AoQw\nC40/1y39lexoIn0e1a50CBh38LVOTjWU6u+jv9nUdPTgRput+BZuaOuBCI2I\nTZaaNjz5ljbP16V/MC4bZgFigMLzOyoKXuVNvepEwGUaoMxWkBI3TxorYa8y\n8H4ug/qxzgn2j8hqGC/sEXCe0JQkcxBi+RhnAd7KIh+zqtSG0v1AncmyPwMb\nRAP2mBovjmlnPiCgn3rtRLs8Deo1K0o0mPUXHHROCtIkWb57r1za8ZGrPxb7\n6M5O\r\n=yzsH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "5.6.0", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48", "@babel/plugin-proposal-class-properties": "7.0.0-beta.48", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.48_1527189756294_0.8832974494472328", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c2ffef1ebbaf724a9e58dde114e57e3e6864a5e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-ihVVaLwcEzKJTbl7NQdjAf5SFyaOkdt0aduV8ckF3MWNGQM60bfdC4xrL3aCNq3t+daq5hS8NWOtkhrtiuUJIw==", "signatures": [{"sig": "MEYCIQCm/nfI6eMs80EIF2slwZ0itTPvBvt/9/XXEwwZnaBUjAIhANCFxZZJsrLyGzb0+gOhvlQBWVzrs/JqQzqEBRAishkZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOHCRA9TVsSAnZWagAA9skP/1eg80irFYhExsIFefKj\n57rdxBuVWwSxZg6MBJkwu33JBJGgY/+8+Zw5bE8cR4PRYJh0VZKoVZSNBBYL\nynhLkySiuyc3rXgyKtZffl/MvyiKro3hpS7oRkhMNvXEn0rX1cdjRmfq+Aj7\nvAeTfZymVmt6blBbdjkC3Ex9k/mryEDrvXpCkh3onLYQgo+itxFzqgOyRV1k\nAInIGcmSa9nxM/kfKgCBInsOsfQpgk1Urodq4BTjFmj6L+gemSkIbNHnfd0C\noPxklK5kpZ9OiB4oqUIm3wEbUXqvVfmJ025SeYfJ363LnUaUk6QTxUpsAco+\ne1LrcX+XLEFX6shg8ZIPWVtTGTjkrg0DpJ/l+GOC9+NlVTMCjk0pmogvArJQ\nqUY2jLEXdEboZWEn7H8+XjHcVQJ1xGSGS8G7ccOzTtLLci9mNI2vAVpNgbWL\nlu7zVcO89B+C2JZBbl8p6OrJQwtpN7srclbvnDptxJGcBIrntzaLJN+Yk9Q4\nlcTTTJ92VJhCaUIk9y0LGiK4keSiryoL3bVnI8fNkrkJOvNHmPsF4/g+65+/\n95WTq9U8NFGOC7cHvsDTYgvLbBzqr+Zj/mNbC3eWiGH7pGslyxMThC/1eAuu\nMgBJoi8G3G5N2lGinb9Gix9WA2P34dzM0HeYl++Y9/f4+M0Wvv9Ptuj0qT6x\nXI0W\r\n=yslO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "c2ffef1ebbaf724a9e58dde114e57e3e6864a5e7", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "3.10.10", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49", "@babel/plugin-proposal-class-properties": "7.0.0-beta.49", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.49_1527264135210_0.5936012269051356", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "05816b4578eac266d02aff95b59f4e3a2f8d3339", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-3jr08V4M1pYlmZ2yB5KPzqhu1aV9O37/zMJXSOPgDDbtPhYbqA77jHjiaZ51g7u+wtLHOn2PzVxzrkY1UEXBYg==", "signatures": [{"sig": "MEUCIQCFBfAbrDi+hUCKmpJh8l6/lkQPIx+cO4p26RrtDWXGgQIgDjq3FYwzUyKCyKINK0atG1M9UVg1fGinLLlo1jk8gps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3292}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50", "@babel/plugin-proposal-class-properties": "7.0.0-beta.50", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.50_1528832839238_0.8637900496089559", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7075a106595cbfdd425ed6b830b79f8a7aff5283", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-5NFk2cDFqDIOwjS6SGCF2ShjuB/cU7u6g5M/Kb4kBxugNnTx75moEluhvcrIPN34YveW61CSxIcUi9ic2ZH5zQ==", "signatures": [{"sig": "MEUCIGwkz4ywOmEj67BsI81xz/wV9tS64f2MjC6sVgCH7w7tAiEA2tmb+/inKLtB9IHRBTPI6mE3GNY1XjsyXiabRicnsdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.51_1528838390082_0.05322774464333668", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "573f474640773cd8da2a2983291b9d6d471b08fa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-XqXsR5GXFIs60aOdfY+wDHbpFay/ysypriziG7sUJuomVK3xWE+GgLc1+9kwFipONYBma0KqqbLLhoNE77CZmg==", "signatures": [{"sig": "MEUCIFDE+ScfTaIhcCQMmNHYIxH1Wh/RyNnz4FAxCsvrYQXiAiEA/rbsO704qJ5Lx96Lg82rmI7SMQtZUSS4CTrWRNWgKU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52", "@babel/plugin-proposal-class-properties": "7.0.0-beta.52", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.52_1530838766247_0.179698062645546", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9b74b3d53b4e854cf0e360f02c2a4403071c6a01", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-zUiNKmqI2yFE6kALMbgdE/fzp1FAfkOAkneJOlBzJ0N0z/hGRCWfjT0t60vlG9tAd8w+xSjUiHib4Z3WaGM6+g==", "signatures": [{"sig": "MEQCICXR/CTBlrZ/ccVN3425Qbvlape3pgA8vDNmnCx3AFj9AiB8grzgt3Kykwx9gYA/dKtWzvfw6tESaPIYtHg60luQOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53", "@babel/plugin-proposal-class-properties": "7.0.0-beta.53", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.53_1531316416293_0.0844982917189323", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "634ee57fa805720195cd31086c973f1fc5c9949b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-8TmYlDgb6sxwS5dINBZBck9qvUc95Y6ZuPgurK0HZBT5nLjbjR8BqLB4YZwAAY7CLqka8A4+t5DkxHG3ihvAPQ==", "signatures": [{"sig": "MEUCIB9+B2o+GR6lXBb5aKQZFXD7qdzOj6mPJy+JRt1WQY0oAiEAyji3X55ldXEGaL+6bliLj9B/iV3HJ4XqATDAb0HcouY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54", "@babel/plugin-proposal-class-properties": "7.0.0-beta.54", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.54_1531764007236_0.5869860997753968", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0164ad758b68f67fc39dbef1b7d61e37f5a9bfd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-4N8a6bKP1FpPgvmnMbzcDXh1dL6e8aR/Je1lP2BBvZ56P0O59ozK5sGPGduMKnYgPTVGsxuX1Avu9FSfHUtzBA==", "signatures": [{"sig": "MEUCIQCe+QUqBtVA5kaHyhJSkf9NJDxPSjewLTkZ14CkofTIPQIgJduc5qx88mlH+zVnjf2EU0RHljlD0VmucR7iMheNwjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55", "@babel/plugin-proposal-class-properties": "7.0.0-beta.55", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.55_1532815638568_0.3555281948239255", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a8b219cece600e57cdc372b8a41c21e01f8165d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-8r4H92o1LX6xZ3gGCUwrIiMwEoOfUfbflKOzrNQig2ybhX+9thU63m0P7cIU4qgp60mzIDKj2m7bz17s6MCl5g==", "signatures": [{"sig": "MEUCIQDJNNB/GxB1CaJwg0cwZA0un0lGWAsEnCYwrttA4VF3mAIgRt1u5TdB8ysSji+OebzKb8CyAUKimRmSQHE7Lknazb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvpCRA9TVsSAnZWagAAKDoP/i4gOnuZ3ubjEv03fsZK\nsjxbCLw8zCQWlXu2csJB/gMS9NwHGN876pqHwu/Mt9CQEDR7JPrSM8Sl76BQ\nJ4a+Mel3xF5EZxNAl4H3DwLKRfSP/x+MxioIuQe3Ky3QFADowQLn/oWVUfQ/\nxYBVZRQN2ER1T6/aJehVq0QKdQX+W3h14YkgWTA9viOEQpkoRmaHOJ97trUr\nxsJiBnbeJGws0JfmKPg8NxClgmfpTBmSuF9vSpvv2LJl+CeNEiLXYCdl9uvH\n+Y2705ViLKPJV6zVzx9LdJNfv3Cctvahnfl8tKgbMU47gfxjbEOqtK0evE4Z\nzqkYvDxtM2DoZ2grJALYvehyGSZuo4Ns74quigWkpIp/hidPcRiO4PSNg6L0\nYgynNE3ALA+6Z0RUM5FSVe1MS2oJPliUEiZ1kgAzs9kYX9v+GdMbU0WTxFby\nRyEUEDRgEpnKVTCN2kcRMP5z/DssaWmT/WBBvdz1hpY5oG1PdPZvxtJwhxkl\nzbsrsstAbd+lWzhDNDNsuLkmnJdPUBFrWHnnpZC3w/kzxUYnNR1J02twdb9v\nILBeMkatk8XoUbNbQ0cb985sk6ALjbrInkuzbnRuQTFojpmywS5UqwOKWhwg\nWqM+Eqk4Iru/x1TQNJD68jli2ROK+F5pG4QPvI1lo+b/t+jAvfI/vnF/jk7W\ndA8Q\r\n=NeF6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56", "@babel/plugin-proposal-class-properties": "7.0.0-beta.56", "@babel/plugin-transform-arrow-functions": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-beta.56_1533344744901_0.20026052610968526", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c54860a0422dfc73f43b3a270df4dac664391ea8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-nG1NfmPy2dueODKidODrRhXn9QP+899XY6c4t8EwmnajWXLa8UW3vmJV0c74jIeGXYxdbEDWpf3jxW3uOA+iAQ==", "signatures": [{"sig": "MEYCIQC0iTHinW4a2ppbk8UYd+AXRpglMfFS4n0cWozCkx40gAIhAJr+tTRs6Yw54QP9wbNqqOkyLYPPYqzVT0223gQL/aAv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGScCRA9TVsSAnZWagAA+YsP/AmSgZvwJjRSVhL+8Ofp\nwN0ukhg9iQ6Hjre6X/+3gcsz4gzKApmCfRLbI1gjq9aaeqjftnbCv+bHrJVr\nt5x6pm5EyJWNIyALp2yl5NWpJPlZfkg2XCQVkmwwcRKBABvLWoccNbJShdm3\nhdtH7sOiZJPjsGDlGUzBZZ5ggjmsUS7ukdPix51Xa/lqLI9K2gPz0lSre4kS\nr+O/QHVff7S+DpHSm+wXzRIpmiMEKRvoB7wImfSJvc1wmroJYQztH8O7EC7R\nwOLuyypuJoy2JtvEUrJxmXim+ClQTkFZGOvusXvypnoRsvvw8mBCTOB2mwAK\niaK0PooEwUSBg/C3bsvEqDQ2OXPnuNjp7byJNrSyMP/PKO52NkwcQzO99qLF\nLP/AkdQzGFn1PCFnCs3sN7LQMmoF23YpiplRTPzr7yr6woUkCiWYQEEl0mxh\n7W1wZm3iVZwl6jpSUL/E5LmWafDzYQKAgeYgVV4LvQl73rFRTQT8ef63GD7z\n1cEzkspbm7NPwI8CHxywfaCQPFDEvdlVoServJ+RfY64g2VJkmcZ6Qjsn1Ed\nFXG/e7twRgxL/SI2SohUtkfVMQm5tre0X5tWHludgsuSBxiJC5gBGb2+qDp4\n4owqs8i6cjv5FpdVxbGBqefSCVotsskYrAtZrNn7ZijRGbTJJvURVOboVLwF\niAiK\r\n=ydXJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0", "@babel/plugin-proposal-class-properties": "7.0.0-rc.0", "@babel/plugin-transform-arrow-functions": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-rc.0_1533830300291_0.6615822307017998", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e5839320686b3c97b82bd24157282565503ae569", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-mI10u9cgVpTjJllgISn6SmM2H/3X1osvmgT/4sjQjYARGgEfG9khrxtI74IBRhRhtBF9VBgwhah6sYAym+aghw==", "signatures": [{"sig": "MEYCIQCZgncKPz5r+y9pcONGw9IL3fa99PJj7B6hBSAgzwp5XQIhAIO0/VYJza1yHX2JR9kiOM5bUZNlROJtu4c58x7cYqDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8hCRA9TVsSAnZWagAARLMQAIgsvLkW11wR3KqAqAdK\n5MWD+Yo25bEiGNAJKes6xx7ulCdp27AV+YCbvXyS4MO+8NTjQTnJ+wPps3Lq\nSLuiZ2AfL1xT38GB7/X3mqmbUdNRJVTw4PBoSdX0wRayPATuNF1JyyWxSNfQ\nhh4ZtUeB5dzc7yg+J/2gpL0vvjYzulIdenT11pkOEUX6f0eyrLcZHv4N3mmm\nxtvvZQUnhJpmdP5bhJ4eRDndQEADjOKnxHc5mdguqok3E3AKflROcOQ7zKGF\n5GfK0IzezBNZoohDfH2vErTjLGqtJ6U9Rhh/13LxvHrxE6amDg08ji096u2H\njwXXphE137n/Fq9xo860tBZk8HCiWGlRauu0+m/RyQtW8oGRAAjfJaF5hWDg\n+zJ62R6UicW2Ewz8rtQInWrX123kgG8oqy3mxpddhkIk/l98V5LzYWq0t0fO\n/MmlTtUrPwUdfZZnNaLX0D414h1BQpwISr7sakngl2K3+01lZSWWO+tPngY9\nsoyMIyybJ0BeWFvungu5/YlboY4QxO2wR/izxhKECvR+tqfBcersT9Np3+5M\nFBYMiZoaUf/svUacfzbSYaZ52p8kOoopESIm7WpgKcdBfLtE4ajzdY8eI1Hg\nzfSwe2CNVGg3/bEKlr+PzKTcwtPbMnn4kY8mcKsqhk9xKBIIqiVdjBpaWezv\nU9PM\r\n=076q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1", "@babel/plugin-proposal-class-properties": "7.0.0-rc.1", "@babel/plugin-transform-arrow-functions": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-rc.1_1533845281410_0.5829926805797627", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5b70d3e202a4d677ba6b12762395a85cb1ddc935", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-eZR2hgV0rIDAQIYHihqxbGaGNaci7HIOEcpgN207ytlKr0ynw8QFPLGHcu2HNJ3zPF08LltjlfREqttFbdnYzA==", "signatures": [{"sig": "MEUCIGh/rHZ0vYOILYjuwV+ee6FaZCqS2uhv1FIe7zTDrpMmAiEAio3pvW0B0maUJgURQO0tKfntiifAwqYDxQE2ulrDn/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbWCRA9TVsSAnZWagAA4TYQAJ7lSzOQFqjSMeILrVdw\nLhVRueQMzelZkcpmi3ueJCy97WYS7JnOiIzczmYprf8D8sHnQTq575LRlRPd\naQAFOnnFps9GruM/WsHohGw7k3d86X94qEmvaCAPwp1JUfV684fcPMUcdZ1e\nOr6D6ChsFcuXxS0Rz3Bv//PnjXLEbhbPUA9KPG4CHCeU6YhGL9n8WuBiW5zj\ntLUntcjgBg1rhbyzsmuebvJotuuLtqo8VD0AbPE0fZVS7YaeJSCMm6eFa/IC\nn9XsGFceqY7rztmgj01ntpa6Tgb/XsfXszl+Uytsz8ZJJAQ7TKl4t7//X8VC\njyR7l2MtwJH+mgpXxtS3eVawJCWBcV+z5R3f9mW/qpuYK1bGFsQgocHVWNYg\nRdOmfHEyZhKXOEEq/BjLJ6GfGzjBEAMz6YR5AK0PnXdHxtrqfKSJdfcRXhVx\n/ivzGFmV3DWs3KXiiKXTQsPAnhwy8Gf3WrV/okfk5nC5xfKBzZ+0KN1k3wAC\nwVLKTqtK2ovUHQiQ5Ou9Xl9P+UIBzZsgIu71SrRALPhTeka1xJ7F8xIDuz6Q\nDXjKe0q9gJ6eyhxv9c9aRQ96Ciak5c/Ca6WdOipa/bFdv+EdB112NIO+F/eA\ne+GCm7XZkkykwftSGpb7Bd+Cbm0KLB6VyFDn2wxtGXVz13AQ794yaWaPXCG7\n9QBL\r\n=JNh0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2", "@babel/plugin-proposal-class-properties": "7.0.0-rc.2", "@babel/plugin-transform-arrow-functions": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-rc.2_1534879438876_0.7844898370748992", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5d107e54bc636ebb1af2a026f45fecba47b3e4f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-iE4W6qkhwY2NB1R94yqn3EQLnJ/4gPVnzobWAR3+WYW5e5iqo5DvD+G+LfhE3L1VhSbZNjJTTZF+tkn00xeQrw==", "signatures": [{"sig": "MEYCIQDHt+mq2dbmmxlX6/UJAyBF6G0zia1BkcaSHmS8L+CPBAIhALj6yOrlEdGncOLxkSgCB3kF2S2QYig8dbZ59D2RzCZR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmDCRA9TVsSAnZWagAAV+wP/A3hOYxEb/U0hnyoxvyg\nG/SPmuvEwBDK/RpKCHuw8zXRnSS0J34fAX97gVy++hCHWgVqQaF6AGtPCRih\nEF5L/JSa6kiy0xNKMzQ9bWjH2k9JUsEYJksbMpi2LYkI1qGTSwi4A3832gBc\nCsZdBdKyjFsxNCkYt9WCrD1VW/RjH5cUW+R3eYZg6uDX/7H6iu4u7djfrSs+\nl0IMVzI+igUrV/l53XLmlz2WzA+/GeSyzygtEKoXaoDmVDYNInaLJ7pfhkvz\nSqo7NMmDup6+ydpbBLSN7DzHqaxtdxK8dNw/EmYKGMy/wMw4/eI46j77GoFs\nqnbCpYrf/Fu0cChWfNmfkUyZEQyn7lMFr3/fRW9Zbk9gMyBUDUvbkC2P18+T\nR/5woRYXShIrdoTqqTIah+3QzD3Q+ZmA1fHiiagjWMi4+FLeb6QG3d3obpbc\n9MTm3yhthtUYut8tr27dod8W3e57JyUC8Z1124k3g7PxG54CzsicDw0Ouq9+\nZE9AiXEIyEz48tQ920xUZF0TU6nrD047G9JqvXILefS0VQ6Ir7CcqeYiAA2y\nUBbBQ3lBijLWOI92nzp7uzpvI4gBDE0qu535hXnKOjePf/KKy1jAejnI0WfU\n0sRrO/Kgva73xTOrf7Dv84trKSIu4nZVBmm6cjTklyj+EClY+F3qxwRbdBWA\njU99\r\n=ZHC8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3", "@babel/plugin-proposal-class-properties": "7.0.0-rc.3", "@babel/plugin-transform-arrow-functions": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-rc.3_1535134082365_0.6678103668978308", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8e6d16ea06ccea9ab86e17249792755a34195cc1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-zQYV/AYm2vr85coIBUz3rq7Mj7rsSaiRO27YYTp/t41MLdB6W3ILWBf8f7Bm7jJQF+Uw3CLr5XzEGVQY0QxtFQ==", "signatures": [{"sig": "MEQCIArZczMknXK2kcSArhR7oLoeXR++hnIssHA7XJSpuA2zAiBDjus4yxPXEoLSlH/DkVFxFKjSaZE2nXzHD0/9E1asSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpfCRA9TVsSAnZWagAA0xUP/0YsFfWOHM0g4So4t4cZ\nzWlfXoFsU4xRNGNWBTk/MmKfW/tP+v6ij0r2JES/Z5oE4CXLmFCMoTkSKXXb\nUAJkCO6AABK6+fxXFy46dP7/kOn3fvCfiOb/JLcc9Myl+uG+n8zCzv9Yr5R2\nsL9IIYMXqXpy2G9yjqoZPd2FABbnX8DBSqA0p/4c6non96fAD1dWuRDjhaCY\nILh8U6dVO+9YmyKlwjWLrbitraLKZZuZa0+y6gYzL25BAyFHywNzwKE2lIoQ\nuJK8EWd6i7OPSmMXGl7L9uhq3RUtWFTKkgb7wKtsBmsSngpTZRRaulUwhJnS\nZ9Ye5R4ErokxhT4XL95RurFpv2wkcnvMMozvag32w9sVVlmRmz2U49EhIlj2\n6cGNBD6eWNCqrRiZb9Q+ZQbjElvWJ/lvGt5uHGYKII/lAkJ5nOk7mnvk6Ppb\nG6ss6AnoC9AHIIcZlvgy1ysXGS/4CJEgwt6N6gOGg2ROnE8vMdYjKwfWAHL+\nCQ+1AHSXcaogdN6vL/RKe0gTdXmUGcKHiK+hRp21/ITdniDQBmpZkQCr7c/m\n9wBp/iHRRozkgP9mRxX//cPvZ7Ez4eahuMUvQ+njv73VWx8vpWv2Xfm7unci\nPJj6u1mMApeYOw4+OFxdJPyRmXjoU2hCWc7ruRIk7v0Booa9BIE5DrZLtrVu\n3dS1\r\n=zHs6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4", "@babel/plugin-proposal-class-properties": "^7.0.0-rc.4", "@babel/plugin-transform-arrow-functions": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0-rc.4_1535388255326_0.08556770906661848", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-new-target", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ae8fbd89517fa7892d20e6564e641e8770c3aa4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-yin069FYjah+LbqfGeTfzIBODex/e++Yfa0rH0fpfam9uTbuEeEOx5GLGr210ggOV77mVRNoeqSYqeuaqSzVSw==", "signatures": [{"sig": "MEYCIQCgrBsEdLSDOR35LRQA1EV3wJMj4F19th4c1q36TiYD+AIhAI2odeHGLz8uZTPkqz4LgmrCyFqM/09J48w2skN6bFOU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBzCRA9TVsSAnZWagAAAgkP/0wJkOhi9I70+l/ksRTT\nWjEuWc8XBJbUIcbdFMnyCtmSgCl7ASMvc6fPPb+mng7e9nNGgvtvhRqv7/Zo\nE3iEibxhk4n104lmL8ViZA82iABgZCmXo228GZ6ZEVCa0FPwd8nAB+4mzuuA\nhXhc6wLteb2qu5M6Hycrypm6BFa28AisDFbQGQV7+BLVjdibpqwxg1pWlfS3\nqUPFoACkPacCj0jpmELwJY0ATdpfAE0VYuSSsvSNUdSILizrTfjg6t5+putm\nVUnGn43WtN67WmgMNWj5f9U96u3nLEcHVaQ3ZduBtVOL642PGE3nzPsL1M2d\nyXJfZGP9XGfqYwQVq8h7QOCXffEc4r6Hr2ElqEaVIoqNvY6u3uz8fU1IPRge\n4B2m0wLHo0L/XagFJawb5S+qy91sLy1lt4tFsqLNgSdAIMS2SMhawnk5jwBg\nrBS+ShVyyH8vKwEZEg/MKjELq3LWFwXlT2rO38WarawJqWBqmyAGPZ3Sajoq\nW3wkUUjE1Km/nk0bJ5r5iUV9P4+aWL5bTWoXtOQN9phOWY30j/3NQcEtExry\nbcupjt/v7a2LBY0YCfdAty7Ido2R8qF7Gru1I8gShU/vcWfkgZES2rbvy/zF\njQ/DNShrBcaJExFIZr+Mh26imtm7pvu5+gtYMzbknIhJGHDOmmWuUnWuvDCK\n+odU\r\n=4RCV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.0.0_1535406194794_0.7461768120322572", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-new-target", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "67658a1d944edb53c8d4fa3004473a0dd7838150", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-6ZKNgMQmQmrEX/ncuCwnnw1yVGoaOW5KpxNhoWI7pCQdA0uZ0HqHGqenCUIENAnxRjy2WwNQ30gfGdIgqJXXqw==", "signatures": [{"sig": "MEUCIQDJ6Tknx1FQFKQ7fWSDNE4k16PSxmvileL3vq+Uw4UNNAIgbP17EzCB2ZNjCVxohqasMzTplOEOEtvjigLh5/fBX8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVSyCRA9TVsSAnZWagAAhwYP/1UGAD50ZpzcxN1rqnKi\nbS5O/ABqY4i5kGL36bWCJDtxPA8c/NfhJzfJZhXjiCBGTbOVCcTB5ENPqVqi\nuwQr1n+wwgX+x6aGA43qNnZpWBR2x6nCgd1kwrRomIWB/MYsQtwL+vBWL4v6\nDhI+Dxh6xtZ+lv9P7AUQ3cAopPtCGttjUQ71z0UDJKUmx1+Wdzp0F0exXTX5\nYXKS04XK83a4nk27O2jmij4sRNnI42Xsj+flDidXiB7VAHkU80OvfiaocbfX\nD6AtBhBbczvbExoGjlvlbSMgIXNgI7llcs1f2/xTAgTJq+G8kq8jxv545qkK\ngPLcqMA60WoN7EhbzZthWaB/NUomd7b1A4M9XMCRp+k7UhOBaI+cWyWSSB5w\n98zyP6OKDestAOxTMBeojjn8kUJrvyXEwHwfls659n+9TKzhzw33yUKDfw+N\nc8SeLqho04A4h1ZUTqzz1KQ3lwYJX7wtHLDgIQk0V15C7Dlad1B43IQxTXCg\nidrXmlANkGL38c46vyNWqMSLbWX8m5CxEOjrynpSzoPti8tWB7tPkHUbECkH\nOEY9j52sm+QhfzhObGhDskRP46zWwGvZy95pvOpgzGR2iO3wX+vHWI3WVQj5\nth/xuNm5qru9i4LyN/PCfo1XHDEwQ1oqwjlKCiKDlVULBijDrJk81tcKuwJw\nY4Ii\r\n=ghDe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-transform-arrow-functions": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.4.0_1553028270543_0.7039604418083671", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-new-target", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "18d120438b0cc9ee95a47f2c72bc9768fbed60a5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-r1z3T2DNGQwwe2vPGZMBNjioT2scgWzK9BCnDEh+46z8EEwXBq24uRzd65I7pjtugzPSj921aM15RpESgzsSuA==", "signatures": [{"sig": "MEUCIBM3xlx+LL7ZW/6x7olELpcpHeP8BUTCE6iGnWU/tMTOAiEA4FiYfvqUOyPNzCTPoqUUcCVSu4mxzRt6Sr7UerK+lpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JACRA9TVsSAnZWagAA9NQP/iKmfRtEpD1Ww4aTMdDJ\nSQ4bDScQg8qHtsjvtaCzkwLS/ZfCdmVoib7G5iXaSVhqDMEAsLT2BR3Bo2T0\nGU6QnKC10MI2pEFmWRPuplP2zIDaTHSH4F4barns0nyQyEXRfEHiS9Rp5KVx\njg4lUukSqhd2/0t4glVOwRpSe5PMQ3ElxP1D4nTMi741k6nG/VEm2VyMs4Qk\nmqCYpdIUHbWKEsNdKbyRBbMyGyqv6mUJ1FmEG6W4c2WWEsyUxBNgQ6JXFfft\nILZ9fkEo+LonEuzpU3f/OAhq0qHRBoUKIlg+0FepvIeUwHB87DjMGt8kD7B3\nMDT4C7GCuhtNjvf49W3r6DTrAIxoZr2y2HS77FasMRWun03Ilr1icP5eP1i/\nSAfTy0MMRasr7AHHKAbQ8+2dHJtr0tqcJWt8riAdvhRjj11WOqoMfZDZMhde\nEqzOtIg2d9YFUBXUnS8mlRhbZJyrmDcGYYzVpdrAh/ezpz6wfeJop+gJpVmY\nIxbo0bqhZrUybs6Td5f9z++GPMCedAjg8G2nm/cz6oVz8PpR+ooIYAL4TN29\nIxXYZVddLDtQI/dXK2qYwvUZWW6yuVRVxIaJZ5UrdYri6IAJFjdyvTakQSLx\n4/Bmz1Yl/FsSzr9WBGIrMfv8n+Gyaxp1J9oAm4lNyuvSDwrnYJe5BK8MBjyK\nOeKl\r\n=KxZW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "description": "Transforms new.target meta property", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.4.4_1556312640308_0.7898185438763801", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-new-target", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4a0753d2d60639437be07b592a9e58ee00720167", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-CnPRiNtOG1vRodnsyGX37bHQleHE14B9dnnlgSeEs3ek3fHN1A1SScglTCg1sfbe7sRQ2BUcpgpTpWSfMKz3gg==", "signatures": [{"sig": "MEQCIGRgGqHneocCWzMbWbx1Nov2XJx76iB58mdRM5HrVGasAiB2KqAHFyEx6P3UPt0YVp0e53vMhz0Betm9dyNeYFqaxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/9CRA9TVsSAnZWagAAosQP/3z57z8iu7b5z9b+jZVM\nS/SxYE5fqxsy6oaqH+XNQuo82v1plicd6B3X1G9TTnCh2qcjdMRM0ffjZRpl\nlt6PBGunoWYse0+sUxLyexiOdyuwzGluJy0qLBQCOodtsuCwmUtTyrQ7LxN7\nxUGSX0Hp1qST8QFTDU59KtW0rYlPerP08WmatGC48AS6TYiP0cp+SwzyBt8P\nhd+nMQyUV/d1Ufzu5HHoF4DJjl4eSx/NPLgpzUtIx+LgouiKwLB/sO9ptrBL\nLMCgVTNZn0Up1Z6iK9q8VUnZI8y31SJXkGWZLDtb/I1311cHnV7SG6p82Cju\nfjMHY52ovOAN4idBffWiLx/t1l+0rcCfO9qJTuSVIlLWP9k6jYKQWT0ndPJ6\n/m9xPSXET7ik06qRTDZC6v6CqJpplyR7q0tGX/T7dbP52Hwv2TUPz6ofom5Q\nI7qRSS1qBZGnC4OlkRrQwKNNClXAZBGsfISrFftIEgm18bbEtf25RNvqJkTx\nWweBTPghRWmJpL3KUZdyFnZMW3/l5GhAYX9O8VwFdrc92oLXPfRkJ0OkNqDl\nIZ/sQHWLT4N/21KnX+6ojs4R3mjk+6iRFB+HWqmRyLN/4Nch5UM9TtOYHFdD\ndqsuu2BYNM+2EhAvxiXe2XBbQhZT/k8PwHrHtKh8IyFddTkpPQEIc9zRvoVc\npoC2\r\n=ms/G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-transform-arrow-functions": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.7.4_1574465532764_0.41028721373740584", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-new-target", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "54d126788abc648cab27bc9b74a8306b4158f70f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-hH1Afz9Xy/wkcxhoI0vYw48kTBJqYUhMmhp3SLI1p817iByM6ItH4LS8tZatDAIKmAQAXj8d3Ups1BgVJECDrA==", "signatures": [{"sig": "MEUCIQCuGupv/xtd/uJFQ6a9BLkxGVUBWr4eqvv/RCLdpkBjCQIgWiU+5yvroveb59jqCiVQdmJVcBKXcPyx9Cfg8oBl57s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVlCRA9TVsSAnZWagAA8wQP/RGyvjoQIBvxtH+9cXVS\n3bPj8EIdfxji6hLrvKlHrEq39/3H0yxWdEK4UAQZvzexRplSkQuFbRZJmoe4\nkSrOdJm0UGVedXJ+2gKrg52fec+6wZo5MwgCyb/HmK2kfN+/Ys60ihRZFZiK\nfDglqmwmXn5SlJPX5/JhgWoMaFVLOQlRESP0+cBOY2dghpxcB8G+SIwrVZpU\nfub2GxwKLfpigog9ddu05d8WdKh35o8Qjqz+8vEBxuBDK4u9/xiXpwBW/myw\nPOgaZYAJ/mAb7GJ9xzo9ZERGfh3hHljqvTNTICmkOpygZvzvcFclbpVF1lTi\nA944sKXIFJrUCTsY3lqdtRx3TTVkrTvZdlJCGXCqmo03U0Rs6Sh520IJhSJg\niXdXNTZyls20o6OV/dQAlXrjkIoTANK5KiQPBYBhYIOVjYDNSkXjq00kPqHJ\n3va6EBlOum9t5u0yesJ3uzb6N+3nVtEuzvgOWjzcJ4KsHszp70WlpmKu+oUj\n6adufnqH9dW4zk1ZqpHq1Gd934H1U3NflfQGTWxHupT/TzwTQwYVkX6GyJSe\nwGz4gcTiTpWa3qocOghI2UlSxJseHHEjR39J4FQm25UoRLUWuYeM8PtXiw0h\nmyODsz98bHl/kJoz3PUrSx41LowCZqhdrckdITN11eW3VToZRjrJbYDLFu76\nF47F\r\n=NFiK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0", "@babel/plugin-proposal-class-properties": "^7.8.0", "@babel/plugin-transform-arrow-functions": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.8.0_1578788196825_0.534814145965345", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-new-target", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "60cc2ae66d85c95ab540eb34babb6434d4c70c43", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-QuSGysibQpyxexRyui2vca+Cmbljo8bcRckgzYV4kRIsHpVeyeC3JDO63pY+xFZ6bWOBn7pfKZTqV4o/ix9sFw==", "signatures": [{"sig": "MEQCIGxzYwXOi1wOEbySjjfiSrwZ4cbUDZ5E18gVeLrYfhARAiBArlnSETcwoItHr2MD/MQeCkxRbDmHQLtjQRuU0VVZJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQXCRA9TVsSAnZWagAAKCUP/24hkagy+pVK3H9GHccw\nYyCfapkP6vJ7gwDusMCrHs+Nt+QV7t8jWJJtBsCwemU0n8EWGDltXbRtvAGE\nLN7fFWpL4YzhtGuy5f8x0aX7mRPvs6oZyAB5BUXms+a+HBSt82pxZ534SmOt\n0O2qJ4Nx9C3Fj0DtQH6/bQVwFbxFuBtr17Yl4hQGeK8q6NDAHGdQkW6JdtTy\nVn1M+zJjx22AOwmvE4AWInki7bJY4k/AGoPZXSmUPq5rqZ5VZyl0IFFABBTK\nG0amK/sPCZFP6biOfm+WwTweGtVvbPgXkoQSGk8VRmptypOscAlwAiiT2PoC\nEmHiSQJXicsKrIp+7+V/G6onX67hvaos7BQlVrESWxdjz6BpusQSWq66ZDEz\ngdHgko+3d7+uWf5wrs/jouZEVAyXAufVUMnP2htQpbAoAOyBwisIc29BfXMW\nMYLCb0f7tSFO/VURKG5bvKDBicEk1+/SxWkxAO4Qwhysgy5YE8/NhNZxyxrU\ngTLJMHOcv1upCPhuQAwaw2AQui6xOiCAyqlpjF4dALh1e0HT4VIWMyYxjJah\nS7wNJrOtIKfM/VVcYqCeBem276Y7HMXwd2B6rFbaxE3jR/YHynlv363pQgH8\nd0csyzlK7/6Oo1q44mZbChEC9Dy2sf0x/uNhhdjF6XFdaluOU+iKyfu+kg2P\nA2yD\r\n=zOo2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-new-target", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.8.3_1578951702711_0.594839206314367", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-new-target", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6ee41a5e648da7632e22b6fb54012e87f612f324", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-MBlzPc1nJvbmO9rPr1fQwXOM2iGut+JC92ku6PbiJMMK7SnQc1rytgpopveE3Evn47gzvGYeCdgfCDbZo0ecUw==", "signatures": [{"sig": "MEUCIQCiBefgj6yCDiEJ44FOTGijf2e0VpSDQ22WZf8IYOPxMQIgKBkxf07Wr8QK6wnE08vf9b90QzjtKbvynGVT8p8Ajn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuShCRA9TVsSAnZWagAApp8P/1RGb5by/uX4oBLEKgNa\n16DuscpK2RlZtem98FpjEnhczATmReiib6n+YkkpqGx747PwMK4m6s2hHSgB\nfw92Hl1MnKCE8H+lChYlVY/aOXe3g0/S1ryYrwJKZ3goZtBrB4xhZMpC9m7l\nQiTbrWN4MML9fSber76kaBNFHILLB3c7/RtdG/KKvuEhUJ+lBRPPQzN+19s3\nhFIZ0/4gKMK2ds9XD3Fj0hdzY97i/ZVGR7B4c7QC+4MfYx4OLH4bEOWOuwLO\nUwuXxxtM7nYrsE2qpxZstD9PuHkYrPYBp2BaphF3l1HrZNI2yu9KtwXzwzEf\nsmwf51ZwdR7Uq3j2adVoluNEn5RnljcWgpLF0A7NQFXlvmXDh2T9liaxUF5l\nHwdgW3XBEE1ooq0pypQmxqIr1VzfvhyMpl4J3k0v3jwHe7zOmWRMgqSVt0Yr\nPqy598uCnoruMF3omaJaoWlSWkG9uUXtdnDr6eoWCsIqyEBibbnSuYmD8+Nn\nizq0A2PejXXC0iLyJDJQLie5Thyfbxrid5DkPejQ7qxOcJa//yoeJYlAQLHP\nOOLyuqL5iaKaxsIBXW3nM7Ms1cNDCOvZ9jDHUFAz1z7IdwgJZ7YG2KISCRnl\n2g6XphxrRm2NGtdcU5SXGjH3esunC0BGrULqrJ4nwXY0Vzx0NldMi9Z0w0od\n5rue\r\n=IKXf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1", "@babel/plugin-proposal-class-properties": "^7.10.1", "@babel/plugin-transform-arrow-functions": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.10.1_1590617249068_0.300426504516089", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-new-target", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9097d753cb7b024cb7381a3b2e52e9513a9c6888", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-YXwWUDAH/J6dlfwqlWsztI2Puz1NtUAubXhOPLQ5gjR/qmQ5U96DY4FQO8At33JN4XPBhrjB8I4eMmLROjjLjw==", "signatures": [{"sig": "MEUCIQCGWBAMThhcep234mGQ5pp2vJ3V1vEriuQKvpAuLqDCzwIgV7NN5zrVJWA5S2sTr02DnZ6vpF5aCDhDzXFWdCB5cnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zojCRA9TVsSAnZWagAALZIP/RYGWFaJnc6VX3zz8xD+\noydxY6gj3Ql/dvaAMMxugH8ByFh5Jemxy4JpgI4Y+kXu9G2BwZMEZh0S+zpZ\nsZhsX79U61f/1mzw4u8KVzjhY5eWgDqbo5u/fIIZ6lCN5y31Mp37BuOqikhx\ng1/5LYhwBSCz2QaH0F/2YahPy9ZMazsEMUkck3aeVg6ZPd0Q4cevy5ioPdeY\nHeYB3lJkbbT464KQuHJ66aJJHFok5aMFsm9ljpOJdNQg0pmsqeA/D7g+pqW1\nsnC68d+PEyrmhJINrr1O95iQRYl0y6GCH+T05qomK4Q6CFzwYWHoD4pzSR9c\nJymllb2cxFAxC78GNxAyX9FTUkDyFurWlrGUZwfGzN2kAxohdWD6pYew9yqd\nqhcTW8MNGa0UmjB/easWHlDPo3Qjx+7fBlf/0K4eR8xeVdf5VyxPzIXPELoc\nzkaYWaME9BUrKWfBT3y+yI5Xar5yRd28PMj1Ff4QfsXoiYYZ7sdX0qMM+v/U\nlgvMLcKNyIYrpCKN+cMIHO5ks7T70rt4pt7d75z8D+yxQDf+4d9dtljDlFjZ\nNnihs82sEBfunrbrzW/PjGTqaNurSU0Vzo58q0MYTljlZcHvAn8QPH0C0Gs5\nl2OSAyHHl+wN2OXAGCCALI5DGS0xam7CuTb4tknndvcsZMTon9PvQJH0Fhuc\nzCwo\r\n=LDho\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Transforms new.target meta property", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-transform-arrow-functions": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.10.4_1593522723181_0.24545462713880473", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-new-target", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "80073f02ee1bb2d365c3416490e085c95759dec0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-+eW/VLcUL5L9IvJH7rT1sT0CzkdUTvPrXC2PXTn/7z7tXLBuKvezYbGdxD5WMRoyvyaujOq2fWoKl869heKjhw==", "signatures": [{"sig": "MEYCIQDTunYSl4LOAK8QUoHQQLtvyrPXYAAkTndtSFbdLDfgpwIhANk33UsTTW8z3mk6wRpmaea3btBOwF6XGePZDjvfCy52", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/NCRA9TVsSAnZWagAAqzgQAJNUWhOCyLKJ+IfD1ePG\n7kEEQlLRJoUS5smI8a4knsevjyLU+EPRevRyU4cs0OJJXfciOaSf+bsXWWhA\n51d+klAfJsXHEXszzN18CFEXZhtFxHMyaGdmyAhYcQt2DwQU+NCqXWjsfDPw\n0NKq5Ba2RXStNTJ00mxWhczFZNn8VG4GNk8c3+Ca4FRTG3F2QUDPrbvox8iz\nSBsj3MImIVjwcnay6trxVhpEz1RQXgGpdw+xizbJ1RfJYCmAJM2HmTbl2NZ0\nJJslDBBQf4ehpw7vlGkBRawEogRA6KnSFMxMMrefXhSgqLDXB2lWpa2gWFrq\nhDk74vomxjgCjWDEoEg96XaFunXrINMGDIWrswzL8KJa47GQxo1lZ4QbrtV/\nwdHYEped6gAQSkZjyUy0AXpfTohJQUqQ85GsOIDUV4p5H0nTdQJPDxGN+coE\nnSt6RWfKkXiKE5+Xk9WZtJgnCJu/BJsv8ZnYO7ALFbv5lOOd0ve/7G1JzZ5d\n1PCmvR8CJAj1a8gN3wBTtCqaVbrxFWy3KmJdQ2V+STJC8yChIpVjLbSEA2YF\nrkXTgksxsmggVFHEj74SuydQIpWxL2wqWSTtXl6rVZ7BIOqbNK5ariV6WgAu\n0OZ7Zt//21Nxc75wMPQ94tepBq60qtVQxgL9zo401uuZ+r1PzvGm6h4tZ6Mp\nZBD4\r\n=Q5th\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-transform-arrow-functions": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.12.1_1602801613310_0.06696347890799492", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-new-target", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "e22d8c3af24b150dd528cbd6e685e799bf1c351c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-/KY2hbLxrG5GTQ9zzZSc3xWiOy379pIETEhbtzwZcw9rvuaVV4Fqy7BYGYOWZnaoXIQYbbJ0ziXLa/sKcGCYEQ==", "signatures": [{"sig": "MEQCIDW05ceg0IqFyQ7lX/UmpTV4LE7cHQu/GVERz/uULMxXAiBBusb+pS10WDdTZU4Hlpl9A535umq7lAp+yGG1oBmrqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgfCRA9TVsSAnZWagAAiEsP/RG91rDroXWo7Li9718z\nkHse0F3214Heba6gOJePiBD/9ri3vSJpCZZhmVB28OqOyx8BnFy0i4F+c3vH\nQIxr/r8OHkHJH1cJuoTiO3zz4sPC75PlA6h3Q4H/5UweZ4JHS8pOThz1kttI\nKvuMY/e7uYCEiBDOHAvFgpSBf914Eoj268apOO9M3PzegC7HMbA9bFQiBAAp\nuPAO8vTT75xDr2eflZ/hpPZmffru1hKixRuGEh0LR0fTDtc7z0S8cCPitCaG\necYF8zsCFZXXsHdQmYKzW43Q8lMtf3BtFuh/CDO+TsHrqzhOTxLqMclVL41M\nFD7cPYh4pVjawFgH/ifH79BGWE/dz2krl8+/61rnqTa5JO/DZz4v33JL5+G4\nF3yej57nUAx/CGBTKBt9SfIrwGw4aUSCdty3z+vgISpqmFI9xHVx0zydUdE1\nKFKunrTIMJCG1Ka1lTw38h9l8pP8ieJQo5J1TJqy7wudGGU9mwhpzoApjwRJ\nDLHjIICx++YRWZiWSCR8AvcA9SK47/OwGd5BVm++wci83o5cXpxTartiWHgq\n73Aa4tgSxuj5DgZi0F78hK8Chry/08LAuB5gXB6JrKZGbMHH7U1dXuqmZhH8\nASoB1c4V0QSY8dEpzQSFP1bjXM2/L8Jj8EdhS15ucVnIGtkSy1s5eQSxcL22\ngwvW\r\n=fPJK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13", "@babel/plugin-proposal-class-properties": "7.12.13", "@babel/plugin-transform-arrow-functions": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.12.13_1612314655541_0.43457369639356935", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-new-target", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "31bdae8b925dc84076ebfcd2a9940143aed7dbf8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-Nx054zovz6IIRWEB49RDRuXGI4Gy0GMgqG0cII9L3MxqgXz/+rgII+RU58qpo4g7tNEx1jG7rRVH4ihZoP4esQ==", "signatures": [{"sig": "MEUCIQCYP1k1cqGTs91n9K2R96uuw4nYveaJEt6l5HiogasK8QIgSpYwjj383qbP8qlRPEQpa5eEfVtLz/5F1W9pCNU+K1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrECRA9TVsSAnZWagAARXYP/jm0YpthSIYMr8GDr1Gp\nNy9Kxb2nxg4a1t6lC/OD2cC6rYLg0QIaeRprG7Y+1cOCRUy0OPlPowEf7KQ8\nGeTkq+9suA4CqRmt946UjAwf51ZwV+x85pWB4f9ykvftwu7QQoa6Q8TaJkUn\nHGbblx73mEDqR85Oy92eNlrm98Z9Ens+ZNxDjwNA0j5lteASvxKlf/1NZyHf\nL3t5OUdhRLMl3PtMY/4j4vI/cHeTlI9N0ThJ20jDEH/72SdRKQI1Q7E19A6k\n5wh+XlPvML1WVQuYiEq/Uo+8Qjv3tYk3hXQcmKefbXwcDdddOxOAyZ02z2so\nzL8APyQI01mIop2JHzxTL25ZyFj6KmfTgSW0P0uFK/Ps1z+GgL8W1+GBbl2k\nu6J5Ef2tNC6iiyjmeq/7ConRRTMUhR2Anki2OLRKT25CGspafo1hlY8o/j4i\nG5KJzLSKFf9qcmxFq1Pxua9YbEJIwMBhTbAiKPCrqxhofpWbAwW2jd0FivJo\n2elAauyfq1x2INpCDx2ZNXdKCa8qo0KnIrsJL7DNPJHs74nujJiWsHCcyb2M\nH/8Z/UXCoX0Uo3UFCFQ2vDwcyxSzxva4FxeZ5ckIIFdyZ64//VpSgkkSI0Sp\nZZ0Yj9CLnb5WHiO/3M+qSnUK0q0w54y0u0KMvcd9hU9Mac5XiTIrV3bj9jSj\nYd4N\r\n=fKUB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-transform-arrow-functions": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.14.5_1623280324408_0.2947858140257045", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-new-target", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "af823ab576f752215a49937779a41ca65825ab35", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-fhjrDEYv2DBsGN/P6rlqakwRwIp7rBGLPbrKxwh7oVt5NNkIhZVOY2GRV+ULLsQri1bDqwDWnU3vhlmx5B2aCw==", "signatures": [{"sig": "MEYCIQCd3vvzjJaaQFgfmzu45AzdHXsZU4hyZnD1SdLxP7MgSgIhAJHDen0o+TKs9riBs5Hvhtevwv5bpAVnvkfGbWEmdUyY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4419}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-transform-arrow-functions": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.16.0_1635551251358_0.06466690127588404", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-new-target", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "759ea9d6fbbc20796056a5d89d13977626384416", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-ZaIrnXF08ZC8jnKR4/5g7YakGVL6go6V9ql6Jl3ecO8PQaQqFE74CuM384kezju7Z9nGCCA20BqZaR1tJ/WvHg==", "signatures": [{"sig": "MEUCIHRQmsosQqMfjWfzOebxlrlxhTrTJ3haK6ZSRN6zeb+OAiEA6FxSMKBIJWndAElAhk5OWtOXd98sxZi1FXV7kSi2E2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kCCRA9TVsSAnZWagAASaYP/AkCQFJsJaG7aEiUAP0x\nWpcyh1yYLgsHuB/N0KLN3pC8ZfemYKVIbWV1hwGem9qvFEc4QgdKGCFN0N4H\nUVvdtRqwvQRC2H88iD3F+/KQba0LMV7ld5lKXd3oUv+cvhML+ZDJKJpd0nmG\n6cKZ1XkbVpDnolWJ/INNI8EYqyj7Vmn/X/XegnjqtIJHe2NVVeNPemMgRUcG\nUHN5OJ+ImtP3Vf4VxVJrg0jlTmiZuprzjCO8XZRY7a7nc5D4RZN3KcLPgqdT\nOfVYQsDufTl+x3xdz9c10iie6flHwBYbgL6u1Y8FqSeqkVgfnJmJxCh0qo3S\nkhURVCHc/BOFUEHUBYilMfqGMvCUXC+qzZgkALUE6iYD/oq550m+KvIv1hMu\nBFnYTiS9oYBD/MdpPgYVWpESBQKdLWnmYot0cqHXWg5k/TvnNwXN61HKVi6M\nCX+vv31SlZYU8VzTjNf3yEfHXvbEYMMQSHEo56Pzi7lXMEaCroKg91El2Thx\nAHdK5EeAbR0GBFOmnQHav7YSLz0UDfIQruSe+1HUu1+fj3tg3Za7ACAompSy\ndtDIQR75QndsYrSOjvQ3AocNy5vXzaUw2qF4cl8FV+nE/16SW4i0NFRKRuer\nNBjLaLx1lmVeSDDYFt0OEwxMRbRUBUpuh8ICaDkI7o8sfisWZMckGqx3ZTvM\n5Dc4\r\n=SP7n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5", "@babel/plugin-proposal-class-properties": "^7.16.5", "@babel/plugin-transform-arrow-functions": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.16.5_1639434498593_0.5778158654615433", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-new-target", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "9967d89a5c243818e0800fdad89db22c5f514244", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-xiLDzWNMfKoGOpc6t3U+etCE2yRnn3SM09BXqWPIZOBpL2gvVrBWUKnsJx0K/ADi5F5YC5f8APFfWrz25TdlGg==", "signatures": [{"sig": "MEYCIQDmk48a7LdfmPcfWWKSRQHjM+0U7YiAPLfHFYO9ss0btQIhAMOLt59n0ug+v57COmg/YB5fRnlsZxsEug3aF5xGHiy2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0oCRA9TVsSAnZWagAAzGkP/jVslgdpIG6lPki4zABv\nbsUJTi4GrXd4Q0sWVWpg1j/GDj6atQEyp7ucEVpDQBV/aeAImdJMY3ttAK9C\n60F5D4qCilEsDanhcrkGfoEoaSXio1htixmcQc0B4tawKlPsNO2nvfRH5kJC\nI+pJv537rAdYNgcekPeBOE2uxAbuYXBzPiTaV8A+f/BbMMpdhaj/WvFPuxo6\n8fI2YqAPwy+WbSZ4xT9ACeANehif9hR0lvOLrtnUc7XjPk5Fd64X1w6mRGpa\n+ZW6HjckNX/CUzDJaEWWx4WHI/hebPnlb7Ycp4TOu1n8ROVlRzpvMFDibD0U\nuJ2f6PDMul5jQOth4GVY7STu18LpydeftMicznB2DKoUmHeKndRj5hCGpnHh\naI82BTUtSx9s7PE04LU5cSjvrrTJF60WiKmcb3LjO8v9Fc9GpJjCJjqLxVgk\n3dnfzlJ8wIAb28FxjbKQjygxV+rya6Vb6Grt6GZsDzXYQ/GgmsqqN0CPeWUd\nV8Yi2bOyLQeIrd4r28XVmkhGDDduCsS3sbHEwHMzVwCb1vochR1taAuVmBTC\nLLXKBjHug8PesACP68HxmuGIRQ2jV+NpjOyrg/EfszVVBBDHOQ4PsfL8auWq\nFYzmkdSxeOcgyi+KVtGa1qlOm1BF4xwkWGaVEclHei683XdJt/JVzsxkeQQJ\n3bkV\r\n=dcNv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-transform-arrow-functions": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.16.7_1640910120126_0.8790275511783485", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-new-target", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "10842cd605a620944e81ea6060e9e65c265742e3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-Ca<PERSON>tzk2fDYisbjAD4Sd1MTKGVIpRtx9bWLyj24Y/k6p4s4gQ3CqDGJauFJxt8M/LEx003d0i3klVqnN73qvK3w==", "signatures": [{"sig": "MEUCIQC6wpjcNJsFFN4YfUIAM5ajtZQ/GCvRWYcVOQ+Ym6Ik/gIgBoGmeb0I9xmhVdovMjWhy58LzkVwFCjsfuHACH8Ril8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmojbw//e11nTy6svYH/2AVGDyDO8rDKFPas9zs4h/2WWdIbaNIgE6dy\r\n/g9o/2jyZ59I3+lmpuq37DfaXW5eqPCq2e2kOFLuzIPoF1UOKMJ4ovnLRJ3N\r\n1AmYIBl3Zw+y2Pgy2CZsgH0LvL2S6OSIfj3vnyR6YaaAgWNWbgnv23TTiVB1\r\nkzvDzDIsVgphvsTNPb6qIq89Xo942295lUaZwmShlPNH5Dzy6z24MyjPOLuC\r\nIFvQOLGIz5Nw6SA6a7R2k4xSTGtjiqLOVAEVyq2sOUtE3d6N4ZwLQoNPPb40\r\nWUBDOp1yPaBMVg4pk891FW/xepfkR1WD2cc1XQ5upAKDchiFQyGb2glu1xw5\r\njWNueNK6L1ZMegWPTTksehwnw+WskVJPYW04UW2/K87nDyZgqd8Ug+vj6Jfk\r\nCAISuKyu45mpO5BvXfXhAufoHKr59hBG5icQ/V2tRddhr8pqghDvyW4PKy+G\r\nSMyCxmEuKwAtBs9nuV6XglkoXXHQ5d/DG/Eh75O+JNDq03tC4Ra3/zCidbHl\r\n33/Nc/XUWlwQIZw30N+dwT8OOvh1eCXSdeEM8nCDSMGyriyJ769rhpMdL6Aj\r\ni3n+8kZec8erVcSS6sOETIw+fWu4FNEXWv4pBoJeFY/UXrbzeTTFcH57eSwX\r\niCWdHzVCYuPM3SYmVH51w19mdzxjYEllFfQ=\r\n=/pg4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-transform-arrow-functions": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.17.12_1652729577143_0.08309409228003961", "host": "s3://npm-registry-packages"}}, "7.18.5": {"name": "@babel/plugin-transform-new-target", "version": "7.18.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.18.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "8c228c4a07501dd12c95c5f23d1622131cc23931", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.5.tgz", "fileCount": 4, "integrity": "sha512-TuRL5uGW4KXU6OsRj+mLp9BM7pO8e7SGNTEokQRRxHFkXYMFiy2jlKSZPFtI/mKORDzciH+hneskcSOp0gU8hg==", "signatures": [{"sig": "MEUCIQC/aNG5Ko9SPw3/T4XkTWi8nypfudL/oeUjf4iYC8RBgwIgdz6qGGJGB8OilnzsjpnJea5z7EkQFnbyXTeyb7W1BUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiptvWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrsQ/6A+2+L4UNnc3al6eSMk/BRNK+BcXjvOgDLeo1Q20eoziA+dOh\r\ndEu3/HekPfJbOWTfJKfsWD8OnSTbMZAg7Fb+v0Sb05NvSNRK1qb+XW/b0S8r\r\nIvjHOuEM+ukzJY7VPn3L/ovMXIwdMIyGHCvXVkuHkDDIr9zRP7UfKaGD/rXD\r\nLA5Sb6gY1GbltAU9oExFX/fO1nApWHS4i3fAhBOQ4D0s3bhK5zO9kZ2k3nb0\r\ny/Huf62ssaNT4l9Fm+UViPJrknFbhEVHRszhESvCLwWGwSlSTQc+ITxTxxhk\r\nvtFwojqhskKMTMXhFOXa5C0bGcdaxcx7KMDTJkBgjZasXpet2Fzn4L4zbj11\r\nYrhMRmbRQTwe+LDgWt23coPn958J/zpKoEWSvse9zlJbmXLEGDunIUdyd7mY\r\n8Y6DYFCrTO1Heb7G+URFsfYEbAzmzEWD9x6qcx+ZSAoswGfIabcmePlKyhGP\r\n11IZijE00fYYY+tPJdVt91l3GqEKrVhiwxTUXP+N/yfRRzKg79XNAg7wjdlk\r\nfzsF8AiMVJAtv4OM3nAeMPKFx4XCmJYweNunvMnoUC+9jCh6pw30ujl6gh+l\r\nJYVBIEgI59FFnFPbiQVqRxuV03XzMqEfhrRzyVOAmtkjBhQ56qCYuCPJg2T4\r\nmFiKxO+hpvXopcGyZlpNM3asB/46yCRDc1I=\r\n=01cH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.5", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-transform-arrow-functions": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.18.5_1655102421909_0.15003947141899232", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-new-target", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "d128f376ae200477f37c4ddfcc722a8a1b3246a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==", "signatures": [{"sig": "MEYCIQDnO48VdP2CUNPYXfBUSho+xZcoGosuxPxPSZt850mmMAIhAOlr2GBs5wkHKM0nfSJL3H9zfUmmWJzoIghaAdqx+pp+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqsew//Xll5ofyJp2foMPzJ6x4zYfireQuy5Akt3RqCxKYG7KtJrzrl\r\nP9RVXTfVFbMRbbCOYMUUfnsKyBS+1odYe7BzWVcJYbqItpvcZSywaU/Aqw8r\r\nmXzkRel4kzj12kw9glOHJM/nG7dIxKDCg6MSXzg4VtvZtCSYIlh1hHNOeeFG\r\nJpfYmEQPNJfVRrYRF15vugeGp+pBmCh2S/9XOyut9jf4wB+Zp7Rg0FoptyXY\r\n1ZUsZVmZhzkbRQk0Dp/pPIvg1Gcl5FXYBzyXNd48Fiw4XkmzPwPnShByWChE\r\nbQi0i+yKgNhKGKjdCApX/7sNwgYTsnjnPeN0ckkWk6zPyguYlHSh8cX8ihWs\r\nz+brPP5esbes0SWJGbuucq2hZ8zV4De/my0c3x4NtzhRPnmXsrmh4eqnwHa4\r\nbli6D3MxYVHESD8tI4E8HN0DJ/LHSrD3Da9dCTL/fw1OMy5uqSTgeaiuzr+3\r\nll4HY5Es5RAmzlEleAWDVOKXaztw19cAqEX/kVW77SFZfKG8oRe1uiBMWbH8\r\nP6X9oNJbPjzXzvcRx0/qyDaq+3AUxCZGKWK9zNckF9TDsKV/cKzmxEcvkiWG\r\ndTYt2T3pZGRlPmCrIvJAju7mAm9tfULY+eBzdm07fc7PAq5eW3e64E04ocSz\r\n2bBs3z54r8VkbRiTHu1PBf+q5M6Df55UwG0=\r\n=5DCt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.18.6_1656359400429_0.3029786753513679", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-new-target", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "e80e997f4bceeda8972e5b29c03a9ba1a71f7bb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-NTuzTws8vPZwXzIpZv6oQLnMp1rZojriKPG6IB4Ot4ESDI34nUZFuO9yHdtzQCo7pPvvRHqHfd1A6IOL4yOuQQ==", "signatures": [{"sig": "MEUCIQCSz30WBbh7mzsY+TM9nMyDTeW9O3QF68bjF5WWpqsRvAIgfg3TORoHU5pCiyjU1D4IlrZpwR5XO6b9xOeqI85U8hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpR7A//fWziIEaphufmxKiLF3C6RofpyAWi697paIAsfFrStjN0Dc/a\r\nE8G6wqnQOHJJ8P9mrPiGjcfL787VOZhOTsVtkOYaEB+3l94nItL4yvVa4mNF\r\nCQGiJc3Z7ol8t2xvJJ7rdWxGrmAN3Hm+CsZCsurxGC0fmCM1N3E9jERjVT1V\r\nW3Cuy0w6nmbvCOB6/weiF9vbZSQWG2XAt5yBVIGk74w0iZ3p8rnoWJAxU2Tq\r\nXHh7K5UylYqd/1XkX1/re2/HEVF7F4dVUMQKrIYgUFB8PdwynV0rbKPtxfrz\r\nQStDlwi4WLA8lWXiZ3atVfY/E96rpPUcw7Lx1cSAmRa4sU9c3+yYZaq3UW6S\r\ncU4ELSu0/FQMigO7f93BmS9WAbMcqV6CpmOUEas25CwkSW7LXFRjmnmin6P5\r\npHHd0o87GjvWRFeTxVpXo0q7lOBlDXijoodkTdczb3iVzosr8ydJ2a4Rjphp\r\nPmbOcO+cOCyzsRn1YVA8KFfMpPlo2+OyGUb4ws6mi3a55MVKvpoKOhHfX4dE\r\nXeA5Ia1boQmSUFcL0yte1+Rw7+e1nBAmIeS/odqqFu2/g0dRqe2mJSlCoP8K\r\ndTtUokNOljKcDXZGIaIiEHlGOo7TsXUE+VfteBghsbUry0PM6n6ENxOF0cfx\r\nISxyc1R8jJfC0z76G9RIziQKMGBKUZnpeaI=\r\n=XGM0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm", "@babel/plugin-proposal-class-properties": "^7.21.4-esm", "@babel/plugin-transform-arrow-functions": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.21.4-esm_1680617364681_0.25291969568921724", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-new-target", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "27d75fda5dc81b4d9f24e0de2deb8391e9ea7328", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-6REhoiQMLTqzi5/OzTqG7mf4PA8s3r2vFVDW8D0YjflCEi2OafXuZMjmpyqwaEBaYx/gIR50rDcQq33kdogY3g==", "signatures": [{"sig": "MEUCIQCNod8o78r5j5JxJ9r4z5RoFYHtEFtFOy0jspwrhf9WbQIgBiTS9GdvnTwJ8qlqjdLxtNX38zBhRAsdbQzAiGRnhb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6xBAAhFj48BTT0Eey0CnVPmW9AifhQL58RV+o4uZo6GffP77uE8R4\r\nJcSzwbyd8X6V9LQLBpwuJBZg9lAa4HOfT0OQlnEU2keXO6b/X1YAYQk/I9I9\r\nOEkNfNPjjd+QdYX7/94pHB1NEgCUISMlRAkhmX0jrf3EOeeJVkUBiPMQznGS\r\nHSuhF758bpL8o5Gdh4t2rTFpwlGTshL7yR0UM65H6QxH9C+wpTaqnYBEQiWC\r\nIxY1A8xjoxStzVTqMoE3VgIMlpSaB0pm+xTVqvZ71PC3PVCUiueOsOYorr+b\r\nmrdsH9mm3Gl/R8LCrwaDjEAohQ0c7oHW47Ns4eZS9jU2kjkrDfAzJIApS8UK\r\nKxKSK+FZXb+2u8CUmql85YMPvj0rks/xz4oS9XJQH1+FgWo+D+nBVfbNqLqQ\r\nV2kBbZJU/5WE2E5dl08mBG4buvTKJROdIi8vCRs2Ue5BIDLp3ePGAGrO86jl\r\ngj4zoBVzUKOvS4rU3pshiTF2G0GCagMbfzgBT6cv07rqalRxksjFskDOOKKe\r\nAS8+dJVmqDut8W9nBsV0/h2NyADCb42Wr7LrEWORAeBNTu/DLIC3fr5DK697\r\n0DDVZ+y2nZBdB6eattQnRpmcnsMKBmdMQSfX9Oln5U/oPA+3hWy9+x2/i8Sv\r\nGenGHQs7hp8tIBfLct6t21RJaU/tuPXMY8o=\r\n=xuTW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1", "@babel/plugin-proposal-class-properties": "^7.21.4-esm.1", "@babel/plugin-transform-arrow-functions": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.21.4-esm.1_1680618075024_0.050635092309357255", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-new-target", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "f84fb7d9515c34e64a2f3bb31d292821ef7257c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-YIAs0s0thGWFCPxv3De8BH+JbbWpYWh/XKwBENUwEMdFSliHOKjamjXi8u64UGbInm0OsUQl6O+vC59S+JOJXQ==", "signatures": [{"sig": "MEQCIBqkWhQemlvzJeUlci2HnIIEFP4tyYMt4ZnGYDpCjcy/AiAZjWutaMFEsv5IwN+wsEP4AM59WpmLr/mrQdYk+XEFJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDeRAAhbgUw78023g+vMxSzvbJoS5QIZ3a1nixRiU8frcqVtyireHJ\r\nix1r04HiEF0QdbKGW7D3wfn078hm5499fsG0eOTAwLfYVU0NPMCwHCtOP5v6\r\n5HV8BzPkqgIRQaNAaUX/Zt2OsDpYNBemmjp3XJHTibBBA+VmEoUGtWPwf/jT\r\nvtit5NzIY4Dvstw8wk8GHnWoAmo+bT0yqflcZFWm9RsA9WMn0XPdfggRV92B\r\nPs89K3qQTB1778fMDr+zqP6sjR64VDFt3W0Np+zpZUUq6ZcgnNbuV6HF3Zrt\r\n3Pgd8gdvnVGmGcZ1yBDWAoZg54uYfYS0D0/evfZw2R/mP+PgQlcf7990sEDk\r\nZSOtvNt2z6dBWeCda46NmEGNZmGbnyt/0NZBj8T4ie4Tbch7UbTMcODAb0c5\r\nEZkiDTzREWWF7YRfhb7BDFSDnB2qObdpONv0ofrq6kct/HCt2hWlW0cO8OJo\r\nHwM0/ujTWHhkx3tFWdLofZAvC1zoJAc3UpDvh5TGboznbyN7GRfOfJNuwxme\r\ntFeRBefX+DUycdnDLEZs5vq1LwnBNBbqAUAUfPoCTyHCv4Jp9yip6qu30Qi7\r\n5uEw7gVjaGnPjBMzLI+4V8g7KfKWAR02TQvL6cAX71n5iG0qvFP23Hyi+QuE\r\n2gixgOnEO+XZUHrZfhrve8eFhcjW1n8gkkE=\r\n=JQ5N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2", "@babel/plugin-proposal-class-properties": "7.21.4-esm.2", "@babel/plugin-transform-arrow-functions": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.21.4-esm.2_1680619156594_0.25986365966967995", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-new-target", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "a7b2ec693e92f77bc8c4bf49affd1a2f9ebd6002", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-pz3nEtA6LSP6hCRfkJHoJTrzaZWvz/r9vbch9WSx590qnyW3e5g2P3gZ4AfBYKLHANjsCOSLiKC+gJg0Tcz9EQ==", "signatures": [{"sig": "MEYCIQCcpzcANsfehQmLudEITYYQg+VKDqimTDc8/sptqqQ+qQIhAIv/h4g27t9e2gSfEPn7cyXHmo1RBwEas0vgpbCsnmRK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9ghAAh0G4qjo4bENDApOeIYvvvDv6ADHKDJSmhkAIvCtbj5YrwfK9\r\nFhZxUyJxjlevVHh3V8FDOoPv4l0YoNpfksZDsr3nrgFxlUah2eWY/PSOC+XL\r\n9+6zSmRWdU1AYPn8UU4tbXJiKV+NS9c95vE/UhGqb/4DV9orqcVPohif/tjj\r\nFMR3KmuJUk5hGyf+s36OaCJk3my0x196ehA7TTP0y+lWcRQ2shcCuy2XA+H7\r\nWlwSG+diNZQmG7P8p+61CiKWoF1wrS71TzkGI2r/A07ykx2b/JLzzVbU0vZU\r\njcc5xbJc12PLlZtveJQh+9qR+U6V4iq+dr6r9DWE3gH2d8MUTKZt2Xsia6JZ\r\np18cE90viZbzNZfWfgh+R8SLPJHy1/074wpaITRTxAluak0OCjS9tNWsCt1P\r\nnoaRNloP2/aUw+iZS+aBbo36r66w3mp99sMTiu62VAqFEaOBXDkM/mAEodpM\r\nNp3K30kdbA6m30zb2p6/51d118YhhFJG3a6/Ow7X49wqQlg3nEalQ/Taw2qc\r\nOVeEv6uRoyS5QU94EXVcY5nksUztvt1hE2FwG5LuW1+6U6LQj5GxfQDdnPbl\r\nI7nBIceO0+lLtTCeFoQIaiKxx3me2Gk6oY65u0Yc7EdqNPW0IqG79qNQBTLo\r\nUeKCK+f0KkDxQxW5yII9jxg5i2JAn8CrwGo=\r\n=bdUH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3", "@babel/plugin-proposal-class-properties": "7.21.4-esm.3", "@babel/plugin-transform-arrow-functions": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.21.4-esm.3_1680620167586_0.9139870985582721", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-new-target", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "cff40eb557d4a215fad487363130b49cb4ed829a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-/W8ze2mF+jrPA1p94sVASYeDOgObXyyARBOxQgckRFtELE/XMbnksPXeggsTQE3Y0FcSjw06kG5eTep5SGqMEg==", "signatures": [{"sig": "MEQCIDsBqOh0DvFY+Q2XA6SVhHwY7XViAxKkRoLfq6ImsHJwAiA2zXSi0oC3AWSM8uWaTtnfTlTHibTJX1d+bG3vxK5lcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKRg/9EByrNw8ihhwZBMCzllf6sQEIVo/luT9TnZzNiARhESLHOuS9\r\nom1/fUCwOjwHtuly/qMZ8hMLugEW59bErGNi7jSLs+YzzqabGlV76McDfBV8\r\n6xe7zm5nuh5JkKTzspU+iH5R8VprxDfoy3xgWaORF4OHIL38lx94JCW8J1ra\r\n1KHEQQvu6QQnlMngg/T5hcmXN5i5cpf1bWDKQm+q6TdiNtA3eZeJAE3oAflS\r\nDQcc2hg8fOozuaoLuBkbhRnfh5A23d4PEvO9eNSKVvRfVVw7y0da6G1fWDon\r\nfMzYBPq54EtNDvG5actadnWOm2kjUmc1rWdvertdS8eF3SroQnfdP2Y6WuPf\r\nXi9hiGreK5icpHcZ5nVVKVfo8EJBT4OZYVYDFuFvPoty8I8282G19gFyEeKD\r\nbBUeGgMtSVoL+ad6IdNCdjubQiLZ6fvWzKMU5Emh1FgR1nntLxxHaEpjlw9B\r\nz+2mlJpwfgIuRztD4Ry/Bf32AXuafkenQogQpT40TTNxYcsvliAjnRa7ruT5\r\nnBKU2cWPYC+DGmOpaknUIzf3ElHE9CB28g+13g8+iRujdbuYdUuiVqjGx7SM\r\nO6JrJnkvt21uy5/P/NWthtf9/BHGX9zFTnKr0wxxmqzdcR48Y8J01b2mRQ4a\r\nJMs2Ad154XfcMvPAXP/8DNZbSYIit23YT3k=\r\n=YavU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4", "@babel/plugin-proposal-class-properties": "7.21.4-esm.4", "@babel/plugin-transform-arrow-functions": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.21.4-esm.4_1680621199999_0.26186953445518246", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-transform-new-target", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "df29199eac6fe2faecd4a65b78b668bb83cbd317", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-IZH0e2Fm8XmnZTXRzoRsHBBJ7wFzfeU22iiEZCi6EumrAjKOG6AdHpsxtBezG4SCQhqRS8DojQM8+bqtOBTQqw==", "signatures": [{"sig": "MEUCIQCMIMyuHggHSBpSytDpnWT4uGAUujtrGEAa7iVvNl654QIgXPbVvkxNyO29Q1kUzwo0wpXdCIuL5uFbmwbmXLUonKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10243}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.21.5", "@babel/plugin-transform-class-properties": "^7.22.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.22.0_1685108715528_0.0682199952382243", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-new-target", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "deb0377d741cbee2f45305868b9026dcd6dd96e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-5RuJdSo89wKdkRTqtM9RVVJzHum9c2s0te9rB7vZC1zKKxcioWIy+xcu4OoIAjyFZhb/bp5KkunuLin1q7Ct+w==", "signatures": [{"sig": "MEUCIQDSWO0JyhrXkBtjMy/oVUCs+K/L4aga/ldtNhLMy/O2uQIgR7nxSfd/CzHLC7f23tHlXSUI9x/wD5PmWB58FxDOhjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10261}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.21.5", "@babel/plugin-transform-class-properties": "^7.22.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.22.3_1685182256648_0.4506717325672571", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-new-target", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "1b248acea54ce44ea06dfd37247ba089fcf9758d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-AsF7K0Fx/cNKVyk3a+DW0JLo+Ua598/NxMRvxDnkpCIGFh43+h/v2xyhRUYf6oD8gE4QtL83C7zZVghMjHd+iw==", "signatures": [{"sig": "MEUCIE8WW0XiRvqVfCMN0fFvyhwf4Kv8EJHiDF/cS0XHokugAiEA/IqcQeXE0GWZGe4fsVIumcwGfhV4hgjnXleVknrmCAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10261}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-arrow-functions": "^7.22.5", "@babel/plugin-transform-class-properties": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.22.5_1686248477208_0.5955234898253454", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "d12068c4b55fd752fd2f1b0b2b322067d8a28080", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-iN/661jBybMvbFIyuX3yt97SxLrq4Rb66HsoIrpBb9eCf5HluGLCdhyhv5cg4gOiPjsmyCKii1OK1pzzMmnIDQ==", "signatures": [{"sig": "MEUCIQDlGJgEQ+N7er8Nw+p882TSgaYq0RKfdh3nG02k+n5aTwIgeO1I0jl9h0XUJxp2onfbxYqDig9OZc6ISaeZdOmggy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10381}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.0", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.0_1689861592721_0.21908354398048013", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "2cbfb2c56fe5190e7b1848e169916abb3ecd122e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-FTMJjl57Sfjc+gc658rQzgHqEBPfOxFc+MuZSzUFgnry/H+2JSbOQRS2XJfTI5iWrSueR6ScA+NU0oOYk9dXDA==", "signatures": [{"sig": "MEQCICSKh4anM9rptzPYRDAePRCGw0OjUfbZum3voQ5Qmvy+AiBxbD1eIjc5fYzxWf64grbXk4AFHMAlxAMZFH+71HkH/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10381}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.1", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.1_1690221107006_0.9471269549423404", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "56657b9298864acd24fd62fd664d5561275b897f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-a9IfngtU9nDLcBTk9/UsGHViysftRVw4wBVwcxJizEdGVDpgLTVw6UOF8Lij96Yg+V9duZOy49DmMIOw2hsoDg==", "signatures": [{"sig": "MEQCICd1EaFPocy2OqnvOiUzZe7oYKbAk9pQ4tIg6HRaN51mAiABZknvja/ncgH881Na7WSkuol8+0QBe3zvpjSqo8frlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10381}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.2", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.2_1691594092316_0.4627422742810452", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "9c2c60793a902506a1fa0b6eb3111781fa24ebb7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-m9ggnZZHi31Ij0fFTb58RxtHibvdc2jWwDor8Cerx9Yn5s3IZLoIoT5sPrvaahXoJIhZR07gv5NUK4qd7fyBPA==", "signatures": [{"sig": "MEYCIQCPhPI7X7jhpZuwtLLWPlRHUxttjPJ41581M7thZvtN7wIhAPE3pEdQuIiW3/xaZwQ08qpvOCPgsn8tg4z2CKyaQ+Zv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10381}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.3", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.3_1695740209060_0.3001632007954671", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "b9cd101825917dca995b5d0e8bd43123679af328", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-JjINVlWPeGPEXUjatZiprcd7k2+QQnbBSEUmPFBD5qipYuJZK4IzVk9z8OUmG4bKV3wwWq/H86kVEVNfoY9ntQ==", "signatures": [{"sig": "MEUCIQCoh5+HaPzsq/uDfy75X8rgDEtnX6MP2ny8ZT3ZgiJDaQIgDMjZg5XWGaHRKO60nlkWemCxkBN4VbPzves5iNqlpqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10381}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.4", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.4_1697076374218_0.6030240240443547", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-new-target", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "5491bb78ed6ac87e990957cea367eab781c4d980", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-YJ3xKqtJMAT5/TIZnpAR3I+K+WaDowYbN3xyxI8zxx/Gsypwf9B9h0VB+1Nh6ACAAPRS5NSRje0uVv5i79HYGQ==", "signatures": [{"sig": "MEUCIHZ/WscWui+N2Vf8Qaq/ESFTlpfefSWfRKrNFDnaUALrAiEA4vz3Qcuxj3UbiBJ87CXMn4CrfPJrvilPL3Sak+FRkro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10340}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-arrow-functions": "^7.23.3", "@babel/plugin-transform-class-properties": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.23.3_1699513432882_0.2798174105460993", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "ec535787321c0d44a7de19530ff9a5626ebfe796", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-ZFBHe8gpjkqcux8zN11C06xFvIVfB8DPPXhlSSIq6ehvgMUpCJvTU7+8Z1Zag2A+IDH5xME7kwEa+4n8vks4FQ==", "signatures": [{"sig": "MEUCIQChYIx4RAbY9Fn5zHr84F8VZzV5P/L790DDOGV9ZD5yfgIgVy7xZa6B+D1pKrJNZ6Zr3n/E36rikk0L2h5ygncpEQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10494}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.5", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.5_1702307918477_0.35985059739632175", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "0cff5796e27106edaa260c2f2f901180323792a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-hKCcmzZe8pDOiN0ZjAGXWNutipKTGsWGkNEAF7ygbGQSm6Z2ZczWEM9ahRQYfZ98pnNTuNPo7rEauHuExNHlwg==", "signatures": [{"sig": "MEQCIDOXX6+E/UYFY3vYegMCCdWrD83udthLY4V9Cibf5FepAiB0H3ZdX3XdoiXFVyywl+AAqKhGfD8JoeNMHTe2cjeMJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10494}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.6", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.6_1706285637786_0.288746541465392", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "d1c2020324d9a9fc6b59abcf4dc24bc20ac98450", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-/QDCbK+LxUdUzW8N2xD/BKD6VsVBMDV1qGLU6A2Ywi0Ec7WvguU/7/w9BdBgLIHqIcBQTEqjQ0yHUcCCH7ppHg==", "signatures": [{"sig": "MEQCIBuihYw8skRsiVF268hSt/5t2xXvhlbWOPMXSu/YOAV9AiBuNCEG5LdJTB+PI3pZo8h/PUgzAnqS8rS/9npMPcVjvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10494}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.7", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.7_1709129088648_0.21375858927416602", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-new-target", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "29c59988fa3d0157de1c871a28cd83096363cc34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-/rurytBM34hYy0HKZQyA0nHbQgQNFm4Q/BOc9Hflxi2X3twRof7NaE5W46j4kQitm7SvACVRXsa6N/tSZxvPug==", "signatures": [{"sig": "MEYCIQDYXAF2dGA1xmDcVV/U5EdhZcCfcQJmzxHGViPJb8E2dgIhAJlIH1O5XyeONqOufEdeLAFivokjtfKvmGsEwcjHK/BH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10271}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/plugin-transform-arrow-functions": "^7.24.1", "@babel/plugin-transform-class-properties": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.24.1_1710841731252_0.7399181820874419", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "07b89141adbe5d642c915006b8e1a79f8379da67", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-KTpiXA2wsiTh/WWbk/P6fa7nJpN9VcmnvYBaesieMTg5w8iQ3fWdeEojQnnth7MX58+K+VdSo49ZFhQGTa+0iA==", "signatures": [{"sig": "MEQCIBLDxqHzOasH6ovEdIneg27hh/+xv2HiV9qeB/AJgbdxAiB+DNYznIya5H9/pN7ll1+nWMgiWreFMs5rT0UDVXYekA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10408}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.8", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.8_1712236787336_0.9894806747958897", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-new-target", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "fc024294714705113720d5e3dc0f9ad7abdbc289", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-f8liz9JG2Va8A4J5ZBuaSdwfPqN6axfWRK+y66fjKYbwf9VBLuq4WxtinhJhvp1w6lamKUwLG0slK2RxqFgvHA==", "signatures": [{"sig": "MEYCIQDGpg/lcQXBl/rE3CrCYsR7405XYNEjuuCrLJ/mSqLkpAIhAL2vBVZpvVy4FXsDpeC36cEW6sDQQrNmDAHMFndBgBIL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76192}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6", "@babel/plugin-transform-arrow-functions": "^7.24.6", "@babel/plugin-transform-class-properties": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.24.6_1716553469397_0.6209309756091541", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "23ada45f36a1190b20fb9238bb15bc9a532fda6e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-rAoOEcqR25tuqaA6Ifi5s/LwJ5/PtcRvtUs2Y6dzPUu5dWLX8tf5VnJn9CzGtp/TBbE2EKu+zH85wD6HMzpNZw==", "signatures": [{"sig": "MEQCIHzXjZvCOuiec80+tdd7XZqXaXdthADI99G4lwwhZB8NAiA8r2I5CvHKPonZgwTTLb5hMVtoi5YlpHyPJKG8BfYvpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76598}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.9", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.9_1717423454066_0.6517280621538857", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "ca9d7098a3b407059544d3efc90cb41d4fdb4e1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-c7qbtr9wCDqvkvtd9TFlLXUeA2ytdiVH867K/s5vTDHuDrnrTqlEkO45479x6i6xZiTGbT4rM0UDtS6evyzdAQ==", "signatures": [{"sig": "MEUCIQCSvc6KBW2kWZxcCZC5g8rS8AZYvzlbFP4Q+Y25/1/yPgIgdP4webnFeXm1jYKrmqhcOjOjKJDSoBN0cw9pGgIDwUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76607}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.10", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.10_1717500002618_0.799297926184571", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-new-target", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "31ff54c4e0555cc549d5816e4ab39241dfb6ab00", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-RNKwfRIXg4Ls/8mMTza5oPF5RkOW8Wy/WgMAp1/F1yZ8mMbtwXW+HDoJiOsagWrAhI5f57Vncrmr9XeT4CVapA==", "signatures": [{"sig": "MEUCIF4YsvVrxnl8j4LHwgU5ioooo9PPGuR58okD2lYXVcdJAiEA5NoY2Vtr/Tcj+lTzM8rNZwC6SCJ3Y3avTf+h05DMCXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76147}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/plugin-transform-arrow-functions": "^7.24.7", "@babel/plugin-transform-class-properties": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.24.7_1717593319397_0.9180612986367616", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "dd856753247bf8b131908ee023b6f549456b3c16", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-HR2qz2vlBuudMdDvz9CmG3sKKegL1sUoRxo1yk7a9X8Im1ElnLx4zHdZ8EyuXdGL8wCdjEMeK0tinBn+6b4cSQ==", "signatures": [{"sig": "MEUCIG6coYGnshdxt9qm7ugOP6sf5N/fTk9lOZHNboeLk5J0AiEAxK0FTxRJMBlBNRvnN56CujGWlmYrJZsDSCSdtZbgzzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76496}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.11", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.11_1717751729304_0.3543822284904017", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "cee92e005397feb2455e88b8e00e9faaea2ec513", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-PkItOj+QlG2Xp4RAuDGH0mWaRvqsKS4+w+JklOuIDyHmih37Oao+Z8JtKF6utmsBZ7ARz+mOgPZDdwAwoG7Tmw==", "signatures": [{"sig": "MEQCIEVwsDxKlITZBBqIdCiWbnhc5kYsgT0BJXIdV2spSi/5AiBSa6PWiXaXN14U1NMrg9XTfg1ZkfhQxT7GqC0f4Rbw/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73292}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.12", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.12_1722015205419_0.7096069493722639", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-new-target", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "52b2bde523b76c548749f38dc3054f1f45e82bc9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-CfCS2jDsbcZaVYxRFo2qtavW8SpdzmBXC2LOI4oO0rP+JSRDxxF3inF4GcPsLgfb5FjkhXG5/yR/lxuRs2pySA==", "signatures": [{"sig": "MEUCIQD+mgh7jL8YJvu289pX+sJxDCf1DCwqXyeeBoRCA7Lb3QIgb/Ae2vf4q03fVDJisozD3k2yN9IQlCMjwU9OswY4J2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80685}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/plugin-transform-arrow-functions": "^7.25.7", "@babel/plugin-transform-class-properties": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.25.7_1727882085317_0.7437068763533263", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-new-target", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "42e61711294b105c248336dcb04b77054ea8becd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-U/3p8X1yCSoKyUj2eOBIx3FOn6pElFOKvAAGf8HTtItuPyB+ZeOqfn+mvTtg9ZlOAjsPdK3ayQEjqHjU/yLeVQ==", "signatures": [{"sig": "MEUCIQDDIYypyDTEks2lw0fd++u8Tb73CTzHkva7/vuspspMmgIgezlA3Kg1Jfa2b0rPTWpkZyJQrTuy+JSVPGrcsJY4Xls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10234}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/plugin-transform-arrow-functions": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.25.9_1729610464656_0.7360411278994146", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "c6db05d8abcc7475381be667379964fa19a8e30e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-SRDrqjlsBW5M+rXUintLxGXIzP3Z2s10AWWcExtQxAhqrv/LqIVmWkPJoJODoAeatyTW9E+siG/ETGMeayaDYA==", "signatures": [{"sig": "MEQCIDDn+tAdTeJL+G0X4y8taCD3VOuf3mvV37PohhLNkJIxAiAnqEZwUMkKobQuHss6547MxdEwXYXnreDKdvDJN5uh5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.13", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.13_1729864447375_0.5545367287259573", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "18e431824d8c7ada05c4a35f5e31c24bb5b2d599", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-4y8DN3jQ76+YrFgD8tXdY/GAZVQoXXyMcNJlLMhexvlyy52LNDrbx/djz4eKoxXtH67vgFO0GUXYjOfiQeBWYw==", "signatures": [{"sig": "MEUCIQDt77L7m+bOKFcrUJSqx5wOOhLhy1NoQYzaz1A7WHDGywIgTYoHwFgX8c4ELwRfbr0tf2jH/P3qSIapdX6xUQFfSto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.14", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.14_1733504038194_0.3670790511081088", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "0a7ae9e12448574b88d6302c0a3d9da5822a85dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-3GAP+OoupneM2VGN7A/GxjXSzoFktyYEgEnpItmuOwW06YaTrD9uepJyIWFn8RiKTxnhJJ1XxT0T3JioYASDug==", "signatures": [{"sig": "MEQCIH3IGWWstM1D1a71pXW/A3Qw+zfx8WTzUDqlEFYXsD1mAiBwBgMFQdmk4PEg5/+UInxywBXZAWK5E4t9p8BpIfRA/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.15", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.15_1736529863916_0.001875833510272873", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "de666cac1d4624ad1ce0dbf6c9e05d6205863395", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-FHstOWlobnDUY8FrnGz6BQfLA/zCQo9ACEdrF9jLlvDeCbPoXdj9tQFtWyzgV3D6qMj+/V4GeHja69YN7aKu2Q==", "signatures": [{"sig": "MEYCIQDx5ITS8ECmdKI5Kuy980/7kqlcln+s4gMJDL1/d+c6kgIhAOcIYI2KdGF2wIxWnWTykEA+vr5Uqs58N0PeWdqja+tf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.16", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.16_1739534340114_0.6649999765666799", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "79facd0d971f85ea0e59c8bcb9f7fb27ad697e9a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-fcy2UYWSdZV/ipbS8Nw5UOHB0h2ol2itxj58WWgCRo+qGOn4XRyZopSE1br+rtI9umIyFRUZgIZEdjBRs30PhQ==", "signatures": [{"sig": "MEQCIHdXkIrggm7duiHsL6B4P1rTMiW04ZRxboLPcl8rCL0hAiB6NySx+jAkyhQwnqT9xxAeg8J61t1uvksrQdYDtGuOHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "@babel/plugin-transform-arrow-functions": "^8.0.0-alpha.17", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-alpha.17_1741717492213_0.13429327130074653", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-new-target", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "259c43939728cad1706ac17351b7e6a7bea1abeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==", "signatures": [{"sig": "MEUCIACW8eHCb+qYw3zKCXQcisvFcr7hrouHrIkinsJEmAsvAiEAtsIkqI3vaQG0wY/q7voQSmr/C9zhmOtWiZXtvBlx1vo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10234}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_7.27.1_1746025729967_0.126322377182438", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-new-target@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "dist": {"shasum": "57b821b669fb6ece06fba78e30359edd53278729", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-/lSVDrf/oXLIwlE3abOpbAjnBbkdKDCghSM5hl1hE0/4Sli4Ii0J4fMCKM5ZLFfaYPQrIEu6ym98KTsRfN5lGA==", "signatures": [{"sig": "MEUCICRXysNUsQb+qC7Fn3jdmKT2JeaEcUaUEfKjre7GJpLZAiEAt22/57hoaNMfeSEqPnOkFMS2d8LVzFZSrJZCIxAUBk4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10683}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "@babel/plugin-transform-arrow-functions": "^8.0.0-beta.0", "@babel/plugin-transform-class-properties": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-new-target_8.0.0-beta.0_1748620261916_0.618473383555924", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-new-target", "version": "8.0.0-beta.1", "description": "Transforms new.target meta property", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-new-target"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-transform-arrow-functions": "^8.0.0-beta.1", "@babel/plugin-transform-class-properties": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-new-target@8.0.0-beta.1", "dist": {"shasum": "e5a26ca67101c2de1aad213c404027bf8b368238", "integrity": "sha512-yQFhueV0924zJ11TVO+ToWuT8ogBC/ksVovyasbGuG6eYVWyphRJLm6NBafpDe416f6WrxeYcFEXBdHzthPGEg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 10683, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGt02kicrAmsZNE9+V7uftAYvjy3Ujykb7zPFSK0v6DOAiEA5Y+ZDO+ZeyL5+F7vfOPCmZvy2E3u80I53nIXVEpU8jc="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-new-target_8.0.0-beta.1_1751447054588_0.8371123937302778"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:40.479Z", "modified": "2025-07-02T09:04:14.942Z", "7.0.0-beta.4": "2017-10-30T18:34:40.479Z", "7.0.0-beta.5": "2017-10-30T20:56:19.874Z", "7.0.0-beta.31": "2017-11-03T20:03:26.100Z", "7.0.0-beta.32": "2017-11-12T13:33:15.916Z", "7.0.0-beta.33": "2017-12-01T14:28:19.097Z", "7.0.0-beta.34": "2017-12-02T14:39:19.994Z", "7.0.0-beta.35": "2017-12-14T21:47:47.034Z", "7.0.0-beta.36": "2017-12-25T19:04:37.320Z", "7.0.0-beta.37": "2018-01-08T16:02:31.130Z", "7.0.0-beta.38": "2018-01-17T16:31:56.083Z", "7.0.0-beta.39": "2018-01-30T20:27:33.736Z", "7.0.0-beta.40": "2018-02-12T16:41:36.731Z", "7.0.0-beta.41": "2018-03-14T16:26:09.805Z", "7.0.0-beta.42": "2018-03-15T20:50:45.531Z", "7.0.0-beta.43": "2018-04-02T16:48:26.378Z", "7.0.0-beta.44": "2018-04-02T22:20:08.662Z", "7.0.0-beta.45": "2018-04-23T01:56:56.499Z", "7.0.0-beta.46": "2018-04-23T04:31:15.646Z", "7.0.0-beta.47": "2018-05-15T00:09:02.602Z", "7.0.0-beta.48": "2018-05-24T19:22:36.454Z", "7.0.0-beta.49": "2018-05-25T16:02:15.396Z", "7.0.0-beta.50": "2018-06-12T19:47:19.309Z", "7.0.0-beta.51": "2018-06-12T21:19:50.206Z", "7.0.0-beta.52": "2018-07-06T00:59:26.283Z", "7.0.0-beta.53": "2018-07-11T13:40:16.377Z", "7.0.0-beta.54": "2018-07-16T18:00:07.304Z", "7.0.0-beta.55": "2018-07-28T22:07:18.624Z", "7.0.0-beta.56": "2018-08-04T01:05:45.016Z", "7.0.0-rc.0": "2018-08-09T15:58:20.364Z", "7.0.0-rc.1": "2018-08-09T20:08:01.540Z", "7.0.0-rc.2": "2018-08-21T19:23:59.024Z", "7.0.0-rc.3": "2018-08-24T18:08:02.456Z", "7.0.0-rc.4": "2018-08-27T16:44:15.449Z", "7.0.0": "2018-08-27T21:43:14.903Z", "7.4.0": "2019-03-19T20:44:30.868Z", "7.4.4": "2019-04-26T21:04:00.446Z", "7.7.4": "2019-11-22T23:32:12.892Z", "7.8.0": "2020-01-12T00:16:36.922Z", "7.8.3": "2020-01-13T21:41:42.884Z", "7.10.1": "2020-05-27T22:07:29.179Z", "7.10.4": "2020-06-30T13:12:03.362Z", "7.12.1": "2020-10-15T22:40:13.413Z", "7.12.13": "2021-02-03T01:10:55.661Z", "7.14.5": "2021-06-09T23:12:04.553Z", "7.16.0": "2021-10-29T23:47:31.466Z", "7.16.5": "2021-12-13T22:28:18.938Z", "7.16.7": "2021-12-31T00:22:00.285Z", "7.17.12": "2022-05-16T19:32:57.299Z", "7.18.5": "2022-06-13T06:40:22.071Z", "7.18.6": "2022-06-27T19:50:00.659Z", "7.21.4-esm": "2023-04-04T14:09:24.841Z", "7.21.4-esm.1": "2023-04-04T14:21:15.168Z", "7.21.4-esm.2": "2023-04-04T14:39:16.765Z", "7.21.4-esm.3": "2023-04-04T14:56:07.805Z", "7.21.4-esm.4": "2023-04-04T15:13:20.170Z", "7.22.0": "2023-05-26T13:45:15.750Z", "7.22.3": "2023-05-27T10:10:56.825Z", "7.22.5": "2023-06-08T18:21:17.368Z", "8.0.0-alpha.0": "2023-07-20T13:59:52.872Z", "8.0.0-alpha.1": "2023-07-24T17:51:47.153Z", "8.0.0-alpha.2": "2023-08-09T15:14:52.516Z", "8.0.0-alpha.3": "2023-09-26T14:56:49.322Z", "8.0.0-alpha.4": "2023-10-12T02:06:14.398Z", "7.23.3": "2023-11-09T07:03:53.133Z", "8.0.0-alpha.5": "2023-12-11T15:18:38.724Z", "8.0.0-alpha.6": "2024-01-26T16:13:57.980Z", "8.0.0-alpha.7": "2024-02-28T14:04:48.811Z", "7.24.1": "2024-03-19T09:48:51.426Z", "8.0.0-alpha.8": "2024-04-04T13:19:47.511Z", "7.24.6": "2024-05-24T12:24:29.637Z", "8.0.0-alpha.9": "2024-06-03T14:04:14.233Z", "8.0.0-alpha.10": "2024-06-04T11:20:02.815Z", "7.24.7": "2024-06-05T13:15:19.559Z", "8.0.0-alpha.11": "2024-06-07T09:15:29.453Z", "8.0.0-alpha.12": "2024-07-26T17:33:25.601Z", "7.25.7": "2024-10-02T15:14:45.522Z", "7.25.9": "2024-10-22T15:21:04.840Z", "8.0.0-alpha.13": "2024-10-25T13:54:07.552Z", "8.0.0-alpha.14": "2024-12-06T16:53:58.363Z", "8.0.0-alpha.15": "2025-01-10T17:24:24.098Z", "8.0.0-alpha.16": "2025-02-14T11:59:00.298Z", "8.0.0-alpha.17": "2025-03-11T18:24:52.389Z", "7.27.1": "2025-04-30T15:08:50.144Z", "8.0.0-beta.0": "2025-05-30T15:51:02.070Z", "8.0.0-beta.1": "2025-07-02T09:04:14.744Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-new-target"}, "description": "Transforms new.target meta property", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}