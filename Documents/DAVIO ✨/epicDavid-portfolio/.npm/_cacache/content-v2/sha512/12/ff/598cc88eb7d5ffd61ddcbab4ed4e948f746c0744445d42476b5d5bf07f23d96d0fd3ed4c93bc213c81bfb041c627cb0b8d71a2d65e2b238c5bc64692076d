{"_id": "aria-query", "_rev": "37-5b07f542aea7ab9c228e23d97f88fef7", "name": "aria-query", "dist-tags": {"latest": "5.3.2"}, "versions": {"0.1.0": {"name": "aria-query", "version": "0.1.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "acc2a8bf44762a36ed798ec03e221a5602d6dc5a", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.1.0.tgz", "integrity": "sha512-A7Na8vWOV9I+ZTSlvjF6Wvoa2PhEvHsXk+UixguWUc4XhvOCeaXFfFhquDG9dGgQu4nOBHWTZJCrgl4H/8dIVA==", "signatures": [{"sig": "MEUCIBWWJ/NGqkPa505xKZazAnoq1bjA2WGcrVLd+9Y2UHeKAiEA/kt8nepoqCVo1TExDMgbas4mzpJ0MNDGoOjuI9gy1qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "_shasum": "acc2a8bf44762a36ed798ec03e221a5602d6dc5a", "gitHead": "29a09a41498f94e456546f758eaa612eae7f1b2e", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib && cp -R src/etc/ lib/etc", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "4.7.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.38.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.1.0.tgz_1485219912967_0.40777613827958703", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.0": {"name": "aria-query", "version": "0.2.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "b018abe86bfca07a89243ba026ebe897055c1b02", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.2.0.tgz", "integrity": "sha512-QUlsgG7HGeV3yT3Eq2OZoxxUTWJjG1C33MpN0ur4VEQ+X7Esv0Kr4aQAh9TKZvdxiEgAvdh1KWXvkI4TNKVmQg==", "signatures": [{"sig": "MEQCIBMwdJU87AwYFq4gl6fuf7jhhYEy8d6rb6r8GPhgMK6IAiAEv5UngZY6HE64+Iz0nHL1a0ORggd2ST/FexbYflO2KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "b018abe86bfca07a89243ba026ebe897055c1b02", "gitHead": "217cf43887ae1909deff4da0ce24e104ba001b33", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib && cp -R src/etc/ lib/etc", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "4.7.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.38.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.2.0.tgz_1485222405485_0.1449339531827718", "host": "packages-12-west.internal.npmjs.com"}}, "0.3.0": {"name": "aria-query", "version": "0.3.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "cb8a9984e2862711c83c80ade5b8f5ca0de2b467", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.3.0.tgz", "integrity": "sha512-JGbH0wIcKxuv2BFTG63bWcLale8lg8ZcC77mFDDpniqI0/3sJx7Jg2BXYEdUJ3Fh9siS7dun787M/bC1ASWiZg==", "signatures": [{"sig": "MEUCIQDM/kflmSVMhcOMBFyBBio5mElZAvLjMvF6p9CDrYSt0QIgQtfBCxWuEXCXkDijQXLW84sry1qFIiUMG+nWoFUPd78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "cb8a9984e2862711c83c80ade5b8f5ca0de2b467", "gitHead": "4cc9e5d9cced1e57395381789ad079aa757ab06e", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib && cp -R src/etc/ lib/etc", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "4.7.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.38.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.3.0.tgz_1485899459736_0.1286766673438251", "host": "packages-12-west.internal.npmjs.com"}}, "0.4.0": {"name": "aria-query", "version": "0.4.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.4.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "72a70776929f33f083961d7c05ea15dd25ec7094", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.4.0.tgz", "integrity": "sha512-PsL+iMfYlGMWsLOFaRcHLuwYHhrl2AdGsywCL2fvC5Jzlec8Lz+58B+rNYJQ56WFmZLvTCrk6eRZ496Q8PCMMg==", "signatures": [{"sig": "MEQCIB8ry5A9Hf9RBhAEGQEIDXOay0FxP6sepBmgABJ5CWlBAiA241AvOfmK6yo9NsEo80pHVy6nZ9E5xfu6M9vbzpO5aQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "72a70776929f33f083961d7c05ea15dd25ec7094", "gitHead": "a41db800e5a32b3877c6d51c7f3d1d3fcc5ffd44", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.4.0.tgz_1489190877042_0.2746265768073499", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.0": {"name": "aria-query", "version": "0.5.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.5.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "85e3152cd8cc5bab18dbed61cd9c4fce54fa79c3", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.5.0.tgz", "integrity": "sha512-KRn8ouVwWsuyX5dBWeE+TZ46OiQxunOcq0t9iodz+z1c1ZHX4zfFIvNjYSTFzAog8weByeTwHQpbVYC1nu3xsA==", "signatures": [{"sig": "MEUCIG1lQ/EqvOZCsOcIHcYrRebzdJ4uFRa/VQJKQQ1T1PgaAiEA40dB/YQTzWvmVBmR+47JUNtG9TET5y6HjS4ZByOA2Os=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "85e3152cd8cc5bab18dbed61cd9c4fce54fa79c3", "gitHead": "cd925c4b2762b914a1f6c85b774b15d9c01e6e63", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.5.0.tgz_1491951488086_0.8393136193044484", "host": "packages-12-west.internal.npmjs.com"}}, "0.6.0": {"name": "aria-query", "version": "0.6.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.6.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "b13fdfc03b5f2262ba233606cbeba5dbed6bec8b", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.6.0.tgz", "integrity": "sha512-ooazqSmJPMTCODLsIP3vq7Yq505ERP291s3F6wRPwkI7j6OE8PmhWcX9oSCgChDktCPP1FmbtBaWRQ8bS01FLg==", "signatures": [{"sig": "MEUCIF9b3HghsnN/UVr4Qwy84FiJBTa/ZshS2Gj9L80RGefxAiEA7rf3QPeAnoj3InJRcQpA6doOA2SYGlvAcj0ZGSpsqjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "b13fdfc03b5f2262ba233606cbeba5dbed6bec8b", "gitHead": "fda8523f6ea1a6589fab82c4a104cf20686a6edb", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.6.0.tgz_1496274669318_0.11058309534564614", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "aria-query", "version": "0.7.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.7.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "4af10a1e61573ddea0cf3b99b51c52c05b424d24", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.7.0.tgz", "integrity": "sha512-/r2lHl09V3o74+2MLKEdewoj37YZqiQZnfen1O4iNlrOjUgeKuu1U2yF3iKh6HJxqF+OXkLMfQv65Z/cvxD6vA==", "signatures": [{"sig": "MEUCIGOeakXubWKwkab9Q6H4Bem8Ux5LXezZde9YABJ36KpiAiEAnrE3Kkrj1M07/Xe3JcD1hlPLgwXo+E0GywoTTdFqmYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "files": ["lib"], "gitHead": "468d23cb9956db8ce52bdc2863b3290fe02abf43", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "5.0.2", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.7.0.tgz_1496614843801_0.19614675804041326", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "aria-query", "version": "0.7.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@0.7.1", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "26cbb5aff64144b0a825be1846e0b16cfa00b11e", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-0.7.1.tgz", "integrity": "sha512-0c<PERSON>a6yBosWVw5Ozi0D7KWPY2fYiK5ahZp2m2AOVYHLw0haJ6rt8XAZFCQYz2T3V0F8DE+wPopWQacS79MEnRA==", "signatures": [{"sig": "MEQCIDTUEeowpbZ5j9aIIFbliqeTjexdRYviLBl8uyyVp0WTAiBlUPNY4HYHl1C8N7FqkelRJSYtKcPqfbv4e/3+4zPgGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "26cbb5aff64144b0a825be1846e0b16cfa00b11e", "gitHead": "550ba5d3ad0af59a4dcbcf7dbc72628670cf6a1f", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "6.11.2", "dependencies": {"commander": "^2.11.0", "ast-types-flow": "0.0.7"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query-0.7.1.tgz_1517439991130_0.9106282519642264", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "aria-query", "version": "1.0.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@1.0.1", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "97d82505f989ac79bff937d8e2f25b60e629a828", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-1.0.1.tgz", "fileCount": 133, "integrity": "sha512-aAYQRqanMpHedveLT2AXLj/I28NwuDbvQrzLVzMsJHXDPrP43lSCB4IGbK5apT2pfkn+R0dzVG6UerS9OcisPQ==", "signatures": [{"sig": "MEYCIQCFzVR7n39NsjITWbQK0cvrdGyJnANwuiXjxYP80BofJwIhAONeRxp4z9fQZCLr5ocsUTLwSwtDSlu/7C1rhL97QlzI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122922}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "97d82505f989ac79bff937d8e2f25b60e629a828", "gitHead": "b4da649daf74b55cdc6171a649122b2bfd43d582", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "6.11.2", "dependencies": {"commander": "^2.11.0", "ast-types-flow": "0.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_1.0.1_1522186257329_0.8501091918212147", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "aria-query", "version": "2.0.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@2.0.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "9ae0ee63349301f993ff6364ed9a1077049340bb", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-2.0.0.tgz", "fileCount": 133, "integrity": "sha512-WpfBZxteRE+TVZhfVOkWfGLdgaAqI0fIGSWVv60YfHE4ltTfCZJ1do0T9iV0EsavHPGXg7UzvtKS7Tly+/t6CA==", "signatures": [{"sig": "MEUCIDBLcin9hzFx9UHjGGPIyIoIpro/iE9mjWUHdUxpEt0gAiEAqSvwwrJ5HccicWlpL0Wqz9O5b0PBsxbVedqRbGEVTSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122985}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "9ae0ee63349301f993ff6364ed9a1077049340bb", "gitHead": "0d3e6c50111860bb8f8071d16df239af17b649a3", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "6.11.2", "dependencies": {"commander": "^2.11.0", "ast-types-flow": "0.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_2.0.0_1522186928866_0.4541209725917066", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "aria-query", "version": "2.0.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@2.0.1", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "11226224a407b1af733c01d79728fa9a4ff7fc33", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-2.0.1.tgz", "fileCount": 133, "integrity": "sha512-GZHKJ7oXevuUQbqAHajMcr1nzG/TDtrBKHF1eTNJtidVD9hanOgyV8rWpr3hzwo70wMqSENRvxibdAazcLhEmg==", "signatures": [{"sig": "MEUCIQCa9/7wL2QvpDCHTNm4UHzXveiYm1v7+dH2yWHH44WYQgIgPxoOmH7ehG4Lfy9q8F3pyNOhnzszPzkaUsyjWbD7ncc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122999}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "11226224a407b1af733c01d79728fa9a4ff7fc33", "gitHead": "f223d5004453648f8af03ac980dd9272cedb33fe", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "6.11.2", "dependencies": {"commander": "^2.11.0", "ast-types-flow": "0.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_2.0.1_1529881884281_0.10893208824497114", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "aria-query", "version": "3.0.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@3.0.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "65b3fcc1ca1155a8c9ae64d6eee297f15d5133cc", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-3.0.0.tgz", "fileCount": 133, "integrity": "sha512-majUxHgLehQTeSA+hClx+DY09OVUqG3GtezWkF1krgLGNdlDu9l9V8DaqNMWbq4Eddc8wsyDA0hpDUtnYxQEXw==", "signatures": [{"sig": "MEMCHyJbrHOG1zeELM0WYsBtxFeE4wIi04N3HSnXBKZhq1MCIHB4QA3+/5lHenQmSOgNy3aLV5EBSkcPExuqwb1it6IK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123078}, "jest": {"testPathDirs": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "65b3fcc1ca1155a8c9ae64d6eee297f15d5133cc", "gitHead": "7d8b314b4a0e90d47fd54fc4c7fa7400c8b3a835", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "lint": "eslint  --config .eslintrc src __tests__", "test": "jest --coverage", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "prepublish": "npm run lint && npm run flow && npm run test && npm run build", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "6.11.2", "dependencies": {"commander": "^2.11.0", "ast-types-flow": "0.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.13.1", "expect": "^1.20.2", "rimraf": "^2.5.4", "flow-bin": "^0.40.0", "minimist": "^1.2.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "babel-core": "^6.21.0", "babel-jest": "^18.0.0", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.30.0", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-plugin-transform-object-rest-spread": "^6.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_3.0.0_1529884473755_0.7382455553956289", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "aria-query", "version": "4.0.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@4.0.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "4b5c17cb2244d54535f83ecceab00fd739d2c35a", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-4.0.0.tgz", "fileCount": 133, "integrity": "sha512-Tz7BL0lCASzlCrGOtf9dA2xCxtGMhRxIwL8J//3WX9uqSBqEaRqWwSOUdv5ZC7c2hf6UiaZXgVswWNILGmeugw==", "signatures": [{"sig": "MEYCIQCqy1WJzudlFRKsndmNEvQr5DXGNKFvm5TD9kp54dxmXwIhALEf+kZGlykhHQxvTOOod3DNxguHPyUTRR598XyMfjZR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4EYfCRA9TVsSAnZWagAAAhUP/07R2mYPzRsXEEk5i5YS\n9ihLJvewbcsR2Q1I5an1UgzYkRg7Ml8qyl3agLhdPeJw8RfqGBxWwBiaCEjr\nIsIQFKZ0wgKDzRrtwpBx8ZIGtmaEAU/1vTrdEeMIYFwwe1MX2SayuJ1Tv4mv\n3xO/EhW2bvzHNqZkQ9rIDKEDby/4pkzcg7Y7T5IHOvlb4zv2veANr+4L/3rN\nzwpfhnZ9rSAdTlGmdqxVfFfE4G1Zv8ENF3C10T8ck/9J4zcydO8N8JC8UeCw\nnFAxo1kLJFeFCaLF0bjfyq5QAG+3cfMOwARAQ5YLHUAWa5pUI2weEj/OyMfy\nfN8QiGnCcgSrk6nlLq60vb6K6YqEHFMmHtO8ai1ThZf3mqKMq0/u8QM0lrW2\n3cSpN04tG+A8uJC1LgYipnLfrZzGK5bgm/HfrDZkd13qMNsAmKjkE7kbiPH+\nASGLPF5Seaj/n+mpOqtQXT0EF+CJcLUakeMAHlAuKsU5fzu3BB8Qid6t/vfD\nktRBFebT0g70Tl42QwcjHX3r7YomigP+tSzGuBghMTYIWZ2TInw1FXl8xJHX\nL+1WoMen15ilPPJPXCtTPywVhKnotZALEh9IiP3qzCqkT3PgAKmqnG4IN60m\ngiY6GlFvCGb4NlmwIZYRciGd7HMjpHHwB6rmCTZgyncamcGgdMujoq5hUxUJ\nul/w\r\n=mJlB\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "f3281952f9c2cb1cb740006ce220d8fbfd570d45", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "prepare": "npm run lint && npm run flow && npm run test && npm run build", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "13.2.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"@babel/runtime": "^7.7.4", "@babel/runtime-corejs3": "^7.7.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^5 || ^6", "expect": "^1.20.2", "rimraf": "^2.6.3", "flow-bin": "^0.112.0", "minimist": "^1.2.0", "commander": "^2.11.0", "coveralls": "^2.11.15", "@babel/cli": "^7.6.4", "babel-jest": "^24.0.0", "@babel/core": "^7.6.4", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.6.3", "@babel/preset-flow": "^7.7.4", "eslint-plugin-import": "^2.18.0", "eslint-plugin-flowtype": "^3.5.0", "eslint-config-airbnb-base": "^13.0.0", "@babel/plugin-transform-runtime": "^7.6.2"}, "peerDependencies": {"eslint": "^5 || ^6"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_4.0.0_1574979103340_0.8846145655997579", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "aria-query", "version": "4.0.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@4.0.1", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "c0fc2a227795e72155ea77e915f49db43e4063cf", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-4.0.1.tgz", "fileCount": 133, "integrity": "sha512-x9lbSjCwJRagydS3DYFlmXJfZRRLiHLx9jK7BMmQawq8Xzia8YicpBzkE/lvxcTGOGSHKSRe3Mi6r1kyHHUyNQ==", "signatures": [{"sig": "MEUCIE6kXWIxZvjFuF/kjDOLknsBlTS0nFyzj3R8ffShgl/CAiEAwUEA1v6FavmO3KT1H8/vt4k61bhhESIXV7/kGrHuli0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150437, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5HnGCRA9TVsSAnZWagAAE3gP/RVpf184mOujXt3mBZ8g\nqJVOy4e9O8xtPa1AYuv8bNARpQtqKYtki5tzl+qAoJ7MK2fTmU6w7AJci4g7\nP46ICsFUQLig4uP8GngRSone3K6jQsNce+6gqTd0FrHI7RBZQf9gdOHaG5Zr\nr7nuedSifQ3tRT49zN4VXdTL4Bc3If8Qg4xRYFVP9dxuaYyyzs9EDDmfSvnO\nIQrOSbgVc4jcRrO+mn3fM3nd0XHD2s4/LiyRW5Q7SnH+qtbJiJoD3+niCBxE\nmUCFHde7wne1vGjB4nLHugjAhcmDZEMX3B9jH4H6avCMWYWE/mOughXBiEgR\nZBYjfVTABzauY4LrLa7AyG64CqNdFn737xVCyZxjOsXzszzI1Iu35WoO9Vcg\nGjL+IRPuFSz2336wDaa9yUfIbxAQ8uLW6L+We8nlSWmehSuLu8c0yEK6xnAz\n9uINAfH50j0TjWbO5e0b1iGcrD3Yl2JcH/ZbgUxcDM1/B/dkXdt3PX7ymJr/\nv7N+O8gAwN71wLnekPgXiNT+NsbCqrGt+hrALzucvNSqSmHnbynzc9HrMuQn\nar2F1pFMvCikTlbROKL0VreEWAS2PqY4fMeyNZEsA8vjqTPIiuqaEKVJkArk\nXZycxZ0BHlvwhiYyL532B/miq5hrAWuvEfcUKI7BC1HXCKikULP6ZXyAN26K\nY1AJ\r\n=nzG+\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "f91d8187a0ff7f8a60892ed86e8551c4281e08d2", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "prepare": "npm run lint && npm run flow && npm run test && npm run build", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "13.2.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"@babel/runtime": "^7.7.4", "@babel/runtime-corejs3": "^7.7.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^5 || ^6", "expect": "^1.20.2", "rimraf": "^2.6.3", "flow-bin": "^0.112.0", "minimist": "^1.2.0", "commander": "^2.11.0", "coveralls": "^2.11.15", "@babel/cli": "^7.6.4", "babel-jest": "^24.0.0", "@babel/core": "^7.6.4", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.6.3", "@babel/preset-flow": "^7.7.4", "eslint-plugin-import": "^2.18.0", "eslint-plugin-flowtype": "^3.5.0", "eslint-config-airbnb-base": "^13.0.0", "@babel/plugin-transform-runtime": "^7.6.2"}, "peerDependencies": {"eslint": "^5 || ^6"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_4.0.1_1575254470018_0.709746723173255", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "aria-query", "version": "4.0.2", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@4.0.2", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "250687b4ccde1ab86d127da0432ae3552fc7b145", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-4.0.2.tgz", "fileCount": 133, "integrity": "sha512-S1G1V790fTaigUSM/Gd0NngzEfiMy9uTUfMyHhKhVyy4cH5O/eTuR01ydhGL0z4Za1PXFTRGH3qL8VhUQuEO5w==", "signatures": [{"sig": "MEUCIQDBwf4rIsmFJXgyBMTlf56UH1qj7cV1pZmtYd66pxPKVAIgPxfYEKRSXHwDb8vwt133WW2y3Es9mV7icGyRU4AdCm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePxJ6CRA9TVsSAnZWagAA5XMQAILF+lUhRE4bqHS1h6uk\nps2A5e2kCzgqutvF7tqLghnDcOGcyVgWQ8k5Qcit1ZOwZv3b8sxCpzcH948b\nEAPYVSnoQHF7q2wkOzffUx4fZvJtnt3JYcV0ogKQr2fFRonu3CAsT5ls7jEG\nW0WZW9WBq4dex8isl/a/IcoEj00wYMQs9yZbjxtqPu1f5UpQj1IUaXnfinYs\nKk9q0Rcpfk5/VR8z/c6Cco058C0lbq8G8mHIIOMphyFsBIBVgWiwNWxPy72R\nu+LwD9/67Myo8Jy/JBnDSxuFNePAOAlCvDT8WFRpKB/i6XYOuPeN2tJCerBy\nYxtc5MPGIYYBhkgFS9V8TH5WC2k3mqknmxkGJPsNqvGl+XyrYmbZMYjOns4d\nf4UQ/qnEsgKeMdwMTN2PQYtHsF1xj2RzmtZ8ehX56MX7IJXk5FdyJuusB2Sf\nGdFcD1kbP5jvreYa0Oy1tKtJH0A+HrbnCq50QzjyaP53SwGiRc5MtfOFj3os\nABHtmWUHuooihNXMTOEIbRKbcAPf7KL2A1fPcXFnaodnPSeGEFbqBIH++QzZ\nAgE7o+pxpXlqW+uTSaNS0lpX+QulQoWtww7L2Uo9azqqlrqsUblOoCPdN8VB\nmifhFjVEWwRBxiPpkrr3RRLLVWDeK9VQ16uTPyvGyhxzXF9IyU1tgn/Cerfq\nXdLS\r\n=ibdF\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "f951d04833e621d150e6eda951f08c7c1143681b", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "prepare": "npm run lint && npm run flow && npm run test && npm run build", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "10.8.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"@babel/runtime": "^7.7.4", "@babel/runtime-corejs3": "^7.7.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.8.0", "expect": "^1.20.2", "rimraf": "^2.6.3", "flow-bin": "^0.112.0", "minimist": "^1.2.0", "commander": "^2.11.0", "coveralls": "^2.11.15", "@babel/cli": "^7.6.4", "babel-jest": "^24.0.0", "@babel/core": "^7.6.4", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.6.3", "@babel/preset-flow": "^7.7.4", "eslint-plugin-import": "^2.18.0", "eslint-plugin-flowtype": "^3.5.0", "eslint-config-airbnb-base": "^13.0.0", "@babel/plugin-transform-runtime": "^7.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_4.0.2_1581191801970_0.49882111050387734", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "aria-query", "version": "4.2.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@4.2.0", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "3774158e138a84c4790b58afc17e09711071d888", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-4.2.0.tgz", "fileCount": 146, "integrity": "sha512-tpVyXGt6gJVTVwCmu8qgBkDHhvtQ/es80y6J8ziybiwQU/x+LnCy+v8p9CWOTHv3i1BMnH5/IfzMpuTcFPCMQA==", "signatures": [{"sig": "MEYCIQD50SICT5cI6DRwxT9Re+9HhG6nl1aK6dbrRl/30XOolgIhAI46c7Y5gjaA0+Yuxcp15A6NY+Otiaz6ht9xcRsfWbyM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6q0oCRA9TVsSAnZWagAAH3EQAJNqp3HOYgSH8aVAgXyj\nE3D6nQxjLsvJRUso67SkewIGl3mqiq+qaDk15JNrYxOc3IUwzHjpFf+jARTN\npqfw36/chWjCJoWiGWUodY/KXegvvTTLXXzAt1P+jGJeTJTMdjInhkKFAIu0\nGy40hKw4ipoN7AKADX1oUAUCJHOgVAAklau1Cdnd9rdnx7e6OuPwvIq0+00g\nszn4CTAVJp9B7RK7dD5eFz+dM5bbjjBllWzcwpOB1PirvYY5UoUvaXOcHJZM\n0xZottP/+5w+HfEdNcn4tjI2l+nfUPcG/vDsUsxAFTcieQGbQPFxex1H+Bxo\n+hoSUP+/EGOcnZ80wfjuvI5sZN/ZVljvw26G6TwTQkurK48lv+yXZHoV4yMS\nvZ+Vu+cDSSMIz+xy3ysbrg6foQMtr4WnsOGaxE9VWYT6cOBWsHykgfest1dl\nlB/a4btY23BiLBQ3wtmOZSIulkPVyEIeEL294PofRnt6S0c8jhduhNEEU5ex\ngZv63BUwxkAoFJfGn0KAWyhjSPUUnhIC0yosPWsKWTTZwv9mSQegCp7DRHRN\nQFtZ04fVn7UwDAEAYrfcrUgDCPrp8FPNk+p3CxaeyQeIeNe6KrW2X/vgtUnq\nvmPpT0gfPc6p9a7r8p1MLdLxdQzv/PqlCypO6JugsHc2PB7I+N1NqbKGmeuR\nEsX8\r\n=VKOW\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "3db8523ea1a2fa7f95699fef8e1722b339a66fdb", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "prepare": "npm run lint && npm run flow && npm run test && npm run build", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "13.2.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"@babel/runtime": "^7.10.2", "@babel/runtime-corejs3": "^7.10.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.8.0", "expect": "^1.20.2", "rimraf": "^2.6.3", "flow-bin": "^0.112.0", "minimist": "^1.2.5", "commander": "^2.11.0", "coveralls": "^2.11.15", "@babel/cli": "^7.10.1", "babel-jest": "^24.0.0", "@babel/core": "^7.10.2", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.10.2", "@babel/preset-flow": "^7.10.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-flowtype": "^3.5.0", "eslint-config-airbnb-base": "^13.0.0", "@babel/plugin-transform-runtime": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_4.2.0_1592438055947_0.782747180694096", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "aria-query", "version": "4.2.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@4.2.1", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "3babb6db7982aafb17fc970ba07545ac6a4f33bd", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-4.2.1.tgz", "fileCount": 146, "integrity": "sha512-j8Y+7VY59qy4Ioi1ZuXvTu9FaBLBFB9OUEM+lFAZSiq/6Tlf6q6w/aEuJlCYVvDN4cvHWCcbUkivqq7VTa6PwQ==", "signatures": [{"sig": "MEQCIGEz8lQ06UFheJPFaicl66XOywWGnu+R3d9si0FMvmp+AiBkkcghLPwr6ON+qAwJZ6JMKsn3lDe2CdceL9xbYDGc6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7QncCRA9TVsSAnZWagAAhgEP/R1n3VPF3QaTvee8D38F\npcok2gYSqVF5oXdTGdmF4zb9CpgY4/3Rgje4f6gJ304AJHJCefLwk0l2/uG9\nPCyz9JxN0RiwQONEZYajeTjUCQGOJ9IMMktQGR+mBanGRMtA1NCairVJ2xQQ\nssfZC55KdUudnIEO/x2P7mzbn8ThXKtCMuZk/KJElu2bY2U/NMMET/fOyQz+\n0Mq8paLOZUcuqwSo6j/RX7wl+HgT/VrvqCRGM4PX8GnRYKBLegV4UG8B5jhF\n2BsEm9/Yir9xQLN6tU3o3KM6eo6btKfJRQ2Sjbl5ad11QN96SjOwScO4lquR\nP9z5nasr3wO46iR7VSodEF2uhng91jHOl5dw9ikMTKII89p8z3gojjoRiLTR\nUo5tX2cZWrktHxms/zDtUg4/AB5sUbgHjdaUNs04pCFzdJxv8AxU37YeZzZs\nd3WEIjJSdIDWHiWaLmJT1ZgfiYcYggSD2rmn6q2yi3crGlyViq4gJFQCAZYe\nblccV3eo74ltrFFTXFgIdH7jL2yyUsQOrEcYEhaOM0KCItMZeCPV1hyadGNm\nU5Rr6i/+cjSi2e4quZGkrXjT5kxaIqKUjT0PykUcsIytfFh1wh+l11wqIExa\n9b9oSDxG/gA9rDhs1AwOIvawpY+CR6iNTOCToAEik78QDMNM3n+ZYzFi4tYU\n9BQd\r\n=Smx6\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "7542c36279ed8ef13e62707586554d2d7caf52f6", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "prepare": "npm run lint && npm run flow && npm run test && npm run build", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "13.2.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"@babel/runtime": "^7.10.2", "@babel/runtime-corejs3": "^7.10.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.8.0", "expect": "^1.20.2", "rimraf": "^2.6.3", "flow-bin": "^0.112.0", "minimist": "^1.2.5", "commander": "^2.11.0", "coveralls": "^2.11.15", "@babel/cli": "^7.10.1", "babel-jest": "^24.0.0", "@babel/core": "^7.10.2", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.10.2", "@babel/preset-flow": "^7.10.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-flowtype": "^3.5.0", "eslint-config-airbnb-base": "^13.0.0", "@babel/plugin-transform-runtime": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_4.2.1_1592592859348_0.8035191530584009", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "aria-query", "version": "4.2.2", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@4.2.2", "maintainers": [{"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "0d2ca6c9aceb56b8977e9fed6aed7e15bbd2f83b", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-4.2.2.tgz", "fileCount": 146, "integrity": "sha512-o/HelwhuKpTj/frsOsbNLNgnNGVIFsVP/SW2BSF14gVl7kAfMOJ6/8wUAUvG1R1NHKrfG+2sHZTu0yauT1qBrA==", "signatures": [{"sig": "MEYCIQCL4pT3FXwAoW+ZfHftQfhFByfMqBIJgKubmLNTFIhKIQIhAOEL2kXxWZfaulJrJEq07uoreKEPsJoAy6Ohj/6lcl7z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7RMiCRA9TVsSAnZWagAAGqIP/AqM8VKTmsi2tD21Wo+b\nX4a0oBKNukttAbqQtFW7mGU9jkciqaPnlI2KEn/4Edm4fmOgVjcjnpm7/jB4\nFfgoc3jc+edVeoTqVrXdhIb12/gCmwUcZG6rZfPNB6rxFSH2abWp4T9z7qAh\n/ml2iguJdvRqA9aN1ycIGNCfSyRKWn5H7/8fbuIPZGHTed3hKtfJBOcAWJei\nxcdpnZFJn2VoHNWDfP/SnSqVrjGMaKv4rbxVhqiIQzygMOK0VEGKgPPQJbQI\nNX+kp4W69YgMLSviblyUES+kUJLRNIP9j3yBZM/x4Lr58MVh+qWPmzttnS2t\nLqB0wZSnV4OG+EgfKzqaEmSQMI6X8MXOvnMRCcvadLDLK9HJLMSsD+TL7/H8\nclCwtU8/jMIAjYaEjH+gdSKk77acCd7g8W5ud/Ww3Jed2quTfhtJJf3QzjFd\ncJCjucEeLPYhkfKiYbwdO5Z9GAeptpNaiFdd2lLrH+jH34iCdtWQbLOUTQHJ\n88OxgSO1cABJToLG/gPSa3/MDaktwgykmUuhr2lEDfsCsz/PcKxk63NnB6qk\nsrkFrKqE2wIAErrcoD7mUiCe9wvfFA1RIS4C8lPjD1vM3MPnTf6cnS2J1PIn\nNah8w75POc5sxEr5Q48HL5+p9a/+5ujfdBRjjT4W1C2z9yM7F9jpvEeEjNB6\nNour\r\n=UKzQ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "25753c80da02b81554c8fc151fc6e0e288cbf6f6", "scripts": {"flow": "flow; test $? -eq 0 -o $? -eq 2", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "prepare": "npm run lint && npm run flow && npm run test && npm run build", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "coveralls": "cat ./reports/lcov.info | coveralls", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "13.2.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"@babel/runtime": "^7.10.2", "@babel/runtime-corejs3": "^7.10.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.8.0", "expect": "^1.20.2", "rimraf": "^2.6.3", "flow-bin": "^0.112.0", "minimist": "^1.2.5", "commander": "^2.11.0", "coveralls": "^2.11.15", "@babel/cli": "^7.10.1", "babel-jest": "^24.0.0", "@babel/core": "^7.10.2", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.10.2", "@babel/preset-flow": "^7.10.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-flowtype": "^3.5.0", "eslint-config-airbnb-base": "^13.0.0", "@babel/plugin-transform-runtime": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_4.2.2_1592595234383_0.15765584378645725", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "aria-query", "version": "5.0.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "210c21aaf469613ee8c9a62c7f86525e058db52c", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.0.0.tgz", "fileCount": 234, "integrity": "sha512-V+SM7AbUwJ+EBnB8+DXs0hPZHO0W6pqBcc0dW90OwtVG02PswOu/teuARoLQjdDOH+t9pJgGnW5/Qmouf3gPJg==", "signatures": [{"sig": "MEUCIG7/SdEhRGBsWY/9mAAiN+lXXQx8oqGHLT6jOn0FQr6EAiEAxz6mpFcnPQitRx8/ht3YkieCZNNfQoJliP8K2lpX7g0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2js/CRA9TVsSAnZWagAAl5AQAI78Ycgrt12GKklft3rK\nkCdGho1S8IO1yKbRDL28xr1aZDGs2ApfwWbo9N567XmD7RRqEIdt4F3ZALSc\n8dBQ3LaMNcQtWBVCkkbiCZ8+LnqC2awMPoHohDkbRtGF6fdpagRBLzHrAshs\nOLPlXtenmFL7OlzOByvHhP6aGQyyxGboE9oDcvvs2UIDpioYM1LfH8rdX/j0\n6bTfp37Z2zugoNfztp8A5u5ZVIe9Xu/mOWuY6A73CGc6AqXqM7pY/xOesM03\ntnCbL77cL9N7CygY5+hxm/gdPDGz0soI1PnpGUzmb2wX5sLo1xdBPZsYsLql\nl8BXC4EHd39dMCQmO/uElJkffEgSanftwkpxuOzX7Sft5faAB0FmG6kucr+P\nBrgRSxFm3vJbdnQjVwrAHdlLZlq8dr2+7w7gqxMOr8KyTGO6PYP6Phtfz5CL\nUnqqIDIioo8suGSmzGfrwQ9ZgEa+aNSz24rLAJSZ+OkVO8sOD4hOYgLZw2O+\n5l8aoAdNHOrnZyfIlUxyjy4dUVpqdT8KdKfqJFtq2T62TWC3QMSFCOIs1Awp\nWN2jzUDK2UDNoR/pJ+ic0Ig3A7/bb1Kcg7EXb1j2ZJUPiF1ygrNxjQP6VnsP\n/sQQvOfgAGVfD/t6Gg6JK9AneVzYY56OJia9i2eCVBEygbmnZgU8TkLPsSx4\n05Zx\r\n=UDLZ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "e6d2ce65c1895a13b75493a36811680c3656d2ea", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint .", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "15.14.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.4", "eslint": "^7.16.0", "expect": "^27.0.2", "rimraf": "^3.0.2", "flow-bin": "^0.161.0", "minimist": "^1.2.5", "commander": "^8.0.0", "@babel/cli": "^7.10.1", "babel-jest": "^27.0.2", "@babel/core": "^7.10.2", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.10.2", "@babel/preset-flow": "^7.10.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-flowtype": "^6.1.0", "eslint-config-airbnb-base": "^14.2.1", "@babel/plugin-transform-runtime": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.0.0_1633307797002_0.41389853704119695", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "aria-query", "version": "5.0.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "7b540d2d255ada22d861a4ee97d1e3f083e93894", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.0.1.tgz", "fileCount": 173, "integrity": "sha512-bb1lJcEgfLvrfoCL9yn9/KQnpjK+B3T/ASg8qkJtzvkDMOZyPgPn7EL1rmR13tadpdaPXfPqqqd/wbnLxZLbYw==", "signatures": [{"sig": "MEUCIGAuARmyRdBg7/7nFmWfgrjUb6TpQx+/CxQPY9YRWJdCAiEAzZdY4vQNUOAofAOgQjBOrWJAA7FFoLpBnbSSIh3E8tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCA3DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1MhAAhgitxWhHQqE29w9fVMPUA4OVdjPa4BVXFShMzAJZ6bEXEMAj\r\ng8s4DcV7yiV6iPKgLBbYJRB+Zl6DlvX5M0MMyjTXjLVem1bo++9pXF3q5jjK\r\nsZ22I3HlOQwM5ssROEpTOv+49RLfUssv303KbvijSAUnoQHAfpwS7hSz+B+u\r\n+ky5cOCNrAVxmJxMOduSxs45ECiUACg8HAljOdcyb7AEmKiw/1zoB9vZdyDC\r\nY3MMYwNkIklXv00Xhx27w1/t5nROq8L7BN64Rr+NexcZbvVKjqHl7qnZQx0N\r\n6np9gRqjIZ/S+B952NM8Ks8XQ8wSsBOsvh7nUnK3qFkyoB3fyHjZw0d+fOPf\r\nHTfu8OLwV2VaQQPnHeeKY6NKFsFLQl1zjzFhR9MXWyp6oU7b8TBI47cN1RHh\r\nMLJdPdjtxcGE8umc0gajxwm8RR+MJy/xVYH7442sR64HreGXZgI4t6p23ryH\r\nc1kU94t0XrC5D8gshpOVhYA0xx+uMXGoUl2xK5RJFe66m2wkAuwVdlzFfvDd\r\nyjACntwhYBZPtRYLxTL+RUX+mca9FNk16nqdhQlSAD7JQ5cwCE3jgko8i5gM\r\nwf5UApF+JKBoKEPoZLUhfDYm0CmubYRfmSBYT6idRvoGixascpzv+ZMzGRpq\r\n+t2XyN4ygsXq4S5F7NdklaV3z1d5l9GIOxE=\r\n=MCaN\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "9b52238b564ee79d91c0f74303141172372ce8d2", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint .", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "16.16.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.4", "eslint": "^7.16.0", "expect": "^27.0.2", "rimraf": "^3.0.2", "flow-bin": "^0.161.0", "minimist": "^1.2.5", "commander": "^8.0.0", "@babel/cli": "^7.10.1", "babel-jest": "^27.0.2", "@babel/core": "^7.10.2", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.10.2", "@babel/preset-flow": "^7.10.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-flowtype": "^6.1.0", "eslint-config-airbnb-base": "^14.2.1", "@babel/plugin-transform-runtime": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.0.1_1661472195352_0.27992537739093204", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "aria-query", "version": "5.0.2", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.0.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "0b8a744295271861e1d933f8feca13f9b70cfdc1", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.0.2.tgz", "fileCount": 145, "integrity": "sha512-eigU3vhqSO+Z8BKDnVLN/ompjhf3pYzecKXz8+whRy+9gZu8n1TCGfwzQUUPnqdHl9ax1Hr9031orZ+UOEYr7Q==", "signatures": [{"sig": "MEUCIQCBMuE4Q/MFDOSq2qSMoUwg7YXA3Cr/LaaHVUtOjUQ2DgIgGoN138HLS5p7TcZYmjqnEQaoa9k56pf26WWZqDKEreE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCBIbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiTw//da61e5TUQ0ZdRF82eMyJCFh7RJqbtzJEyUmkZIRUjDkplkXN\r\nsR8jPwcBJh861eHHdP/NQ7gV2GYiwajobKedPXruRMDa4k/zvifEdhuQc4wQ\r\ncBDeSnqnxQlCAd6aBcYtcpRBzEvtWP4kf4NjyRLeDSVnKXUjzF7H6HLTcyOE\r\nBPhYH4E0td0O04HJQc6kURpyCmaVm1OL+f8a2UL92kQpVmOmcILTtVvUiy3e\r\n3r1l0HHvveDTpC7yx+ZMJ8PhVn/F0GzN+IcEAdT4x3hkFmbUEkhJ+Ci2Ksus\r\n648pg03fH774EzBuM2o8j6/vZ9G1TFJMxUuBMJLA1cKDcBGFVkX19ychDmr+\r\npfkCvUcYTO1aFd34qaH/bxzpfj5cqnrQr1ZhIFtLhGDRReLFvu6TXHbu1EtU\r\nYu65Hd10i4Yqvzam3STf4XMuZiMxndBbaDMhTqWkgNO+QGp1qHc5L7HwF1tT\r\nntpuwdYnyiD6EEuvZaDp9ERMxu2ejmP9NXmZ0IH22LltJ8rXbGvI0sjKJPZR\r\nHASsh64sBFntNnLicBoc62jTnCR7XA6zZrJ2V7E4kshWB142yYsqKQFUNvlg\r\nOSWv7u++HuULyuUBLue4mQzU33/yFqioLwdDdP688KJnePwmtfOY2dSDcOpS\r\no/J8O+mcQp80FeSBqG/cslMaF6OlxigbdjI=\r\n=ykqR\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "engines": {"node": ">=6.0"}, "gitHead": "7f1fd66c5ebd3ca9d455cc7a6fee4eccd96c384d", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint .", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "16.16.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.4", "eslint": "^7.16.0", "expect": "^27.0.2", "rimraf": "^3.0.2", "flow-bin": "^0.161.0", "minimist": "^1.2.5", "commander": "^8.0.0", "@babel/cli": "^7.10.1", "babel-jest": "^27.0.2", "@babel/core": "^7.10.2", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.10.2", "@babel/preset-flow": "^7.10.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-flowtype": "^6.1.0", "eslint-config-airbnb-base": "^14.2.1", "@babel/plugin-transform-runtime": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.0.2_1661473307513_0.09194668134385187", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "aria-query", "version": "5.1.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "e930bc77378f0db1c705049fe73d90d9cb657600", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.1.1.tgz", "fileCount": 154, "integrity": "sha512-4cPQjOYM2mqq7mZG8CSxkUvL2Yv/x29VhGq5LKehTsxRnoVQps1YGt9NyjcNQsznEsD4rr8a6zGxqeNTqJWjpA==", "signatures": [{"sig": "MEQCIGaVrr7Cv1bmFRpBZqvT3jIrcPZe0gdRXLQ8rAWqdn1LAiAUnet/n9OmLg3EfYimV7r9WbqZ9fqvyUAAyVaXeVj1JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVLRvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEuw//Yasw6BZ323RkOjwn9ygsRZqNfE0iYedGVi1HSF7K3ZhYWJJV\r\nOde9kRsIsRWojDa3uaWjpjAiU3ss01mFO6g2bhN5xa15CZGzVrBPgk38Gsh+\r\ngP81yvev2Q1LOMrX3ndu1zFLiKV42TlmF9mXPROMxS9Dom9zqNdHjC7FgOCT\r\n7mF6r/VRvMHV9uSl/yXLJHK799JdpQftRcaiH5zsedl5YrGnZ5phOmFPOYvT\r\n3CNYVA2WyYrlLL2tJGqw+HXQMT2Rbi6AkUf2u1f8DOckNop1ScMPwKwqnYhn\r\nzPLqkmHnH+d5TkrihcpYVCO75RgjX3FjOqGXzgPi++y3mNyb7N3rEtP9bQX0\r\nJ+JERq6Dmyw1mPipF92AoSZ8Z6Y4BwT8gtuycx5BpZ5N8OP34qXubsyn4xw0\r\ny0nPcYNLrV/y+GuWxHqSJLnPb5ygddUTzGdyGjPDLDOqgdcWLPJ7T+ayddLj\r\n46QJTkiM/LbI3NFXZPiC0VyUYB60YJwuD41GqPpIjnmnCMAKMZaIxC6E5fK5\r\nwLDAkwoTrf7Tj/ExrYRpoAFf2qtcNGW8NQCvklworZCD9PutFpsQZnv3mJOG\r\nFGpj1WnplabtGa55D/Yv3iGtPNQc/hpXe4ugd7OADBXFsqeT5XBzYgl3N7AU\r\neK5vUMAyB0/iUbsMlLwGsTz8OaKJkjWp7f8=\r\n=O03q\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "gitHead": "1f1802d5c0d401e743d661281007643a850c28d4", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__ scripts", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"deep-equal": "^2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.2.1", "eslint": "^8.26.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.190.1", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "babel-jest": "^29.2.1", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "eslint-plugin-jest": "^27.1.3", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.1.1_1666495599444_0.647962633355647", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "aria-query", "version": "5.1.2", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.1.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "505a95b0be8b4a978b8e38bcb346a11653ba8b77", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.1.2.tgz", "fileCount": 154, "integrity": "sha512-JWydkr9MirMg2jGJstDqDgzoHqaFbv7n1ghfXYdtEgXWgdq3jz7IU3SQvtj9k3mAszQBiTpQhFdlH+JIRuGTzg==", "signatures": [{"sig": "MEQCICTCBzkMVwZ66r93jLyU5jrZxahu0qFghItWHhP1BTFCAiB2YbMLJB4diRKE3f0Pdca/yx6CQZP6OE2Lz/hNVn/qxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjW2OmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRaw//V+2Ya0ZQ2TcvmMHhwxIdiZVZ1zXQfQnmzvPuiFpHczJIlMnN\r\nml8a2O4jfgGEI8ZEMBQh+c5JeSh6KoCHYtqqNoUsfjT/RR4AN2nFiYuUnYaM\r\ndDGFyMSzFhrMKuQS9jBgDZmw6qTyUhSq8UtMaTpqfFYaCkqfIqeyUCXUevPm\r\nYc6erhIRN0EFnRaI0pgYJyW0SI7ZH9HrNdqrp+grVBaGRSDHQddPGDz5mvqF\r\nMW02C4Wrz5QG1JlQ4FckOJQ9nSOQ+77m3FSQbAk2FD8MWJOY1ga75ew4DK+O\r\nBCsEZOzV6XsL8I4vo1aiarBk+mBnSLxgfTjRDHG3y2zMwOSPSSXO6h9Ta4ps\r\nZiEfKpLIia776S/CpmR6GWQo36hsAWUUlEvvZzf6016c9YxCTRQT2C8fN24K\r\nCtWq70ic1A3m7/mbwleNkXbNXwhlL8o1L8Js2yYDz8Tt1YmFGUGfpXo1RN53\r\nBtCAyre1AnZ6H12nKggKwFEBNmKZT9/RwjnpsOFbMNmrCjV994IGMazZmz7v\r\nHuEtCR3C7EfC8jAc17LViaXt1SpYv3+aSf/CB3ZugMz+Zm/0ImrweysRGSYx\r\nDnXubGATXehc1r//l5IaLjfa+B+Y25tgnD4QWe7Rp/CKOnVFvPgdKxBXIW/9\r\n4Rlk17RRIDePSuifDyxSrbt+k1ZOLGriH9w=\r\n=4smU\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "gitHead": "50487fc738316e8c22835c25404c37c8dfb94edf", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__ scripts", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"deep-equal": "^2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.2.1", "eslint": "^8.26.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.190.1", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "babel-jest": "^29.2.1", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "eslint-plugin-jest": "^27.1.3", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.1.2_1666933670378_0.5209627744447991", "host": "s3://npm-registry-packages"}}, "5.1.3": {"name": "aria-query", "version": "5.1.3", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.1.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "19db27cd101152773631396f7a95a3b58c22c35e", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.1.3.tgz", "fileCount": 154, "integrity": "sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==", "signatures": [{"sig": "MEUCIHbjXXrVKiL+DcmFrif/zpPOQPPSItA9DVOQ4JdnOb2tAiEAqI2dt1JPP3jlk14MjG5SIV7I5i6Zm/tBcU3b86+nNiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjX1fcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5ZA//eHkkXatwVrxOLAf82Sa6BvyNuXzi3Zv0QJINkR3/p4ORxevv\r\nfBvjUiuRXo1AxxnHHbLnIFPTer3VWxamByJ8NJJTrxFo6b+btUpyxUXfhH21\r\nIM/YRV5YXrUvYeWavzzoNYUhbp9rZp3r0uFFmJQip/YV5MuP04Iz8kEfBKbi\r\nkaF6XJEhSGRQm9Gk0hu/Wk48OhlA1oVFX909MB/shBm4308HwWgBIuIpxn75\r\nNQ2cKTrPN2++feLDlDNZjYrY+2TadXuUuQe1MeTmMJreOr4z+UwKBCGiYZqp\r\nVLwbiPXzKCZaAd4qxJ+xh5v/paKrmEwXGfghhIA8h/rzeI9oEmCHkflHUNaw\r\nyF/6olpbK6jTf8+Y1aoET1Jc5K99JTNKaSGjBSQvJBxNjDoqEuiqJr7eMMvR\r\nCGbRW6YUmE7AThGx9hkatJXSmrplw3NsSaqX2FfujzvgEzq8DRRwMOiwTNL4\r\nfBYn2lvctj6ZrmLjWZH0+LljgnituHIc3VWGFotF+psP3GVmmmtg3PsQ70pl\r\nFIdfK4p+CMakomTbuDfinmJNK/XGBgP4KtYG0bH+7gEjCYtF240CnTB+CpLz\r\noZ7cIXJE3mfFF6b9dBYElBgYzDPSO5EdnUXvf1PIHSLaY26agxR/yvlxPXZD\r\nMm+QelBtAEilUILn6siSzccjzgCY8xhDr+8=\r\n=IueX\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "gitHead": "c9dd9dd38cff1ef8b3ada9f9d08347c8935a7eef", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__ scripts", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"deep-equal": "^2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.2.1", "eslint": "^8.26.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.191.0", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "babel-jest": "^29.2.1", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "eslint-plugin-jest": "^27.1.3", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.1.3_1667192796122_0.9116739082438852", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "aria-query", "version": "5.2.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "1fed85495ba6bd94ab3ef7770d46f0c5579d44f6", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.2.0.tgz", "fileCount": 156, "integrity": "sha512-WkRzi93hMI7J6SwwGaHPm/l4vvO1qy3skvo+YCXJQfcMPmiUtm6i6fq/+1PwBX3zfsD2Cb8HvfrmwT3ZhtFbzg==", "signatures": [{"sig": "MEYCIQCrmjq+QQOshzJRTmexiTG5FvH/C8QBxZw5xGX9dfMcMAIhAKE2lOG/6bP3tuHaAkMUzEX91Y9KR4D4mEVR+IyGjEm/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192689}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "gitHead": "ef150a372812d0ba7b0dbf4f784099a6d726cad1", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__ scripts", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "15.14.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"dequal": "^2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.2.1", "eslint": "^8.26.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.191.0", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "babel-jest": "^29.2.1", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "eslint-plugin-jest": "^27.1.3", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.2.0_1686614057732_0.2507038194837119", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "aria-query", "version": "5.2.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.2.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "bc285d9d654d1df121bcd0c134880d415ca67c15", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.2.1.tgz", "fileCount": 156, "integrity": "sha512-7uFg4b+lETFgdaJyETnILsXgnnzVnkHcgRbwbPwevm5x/LmUlt3MjczMRe1zg824iBgXZNRPTBftNYyRSKLp2g==", "signatures": [{"sig": "MEYCIQCb73WgEK9ypVs0VmRs4UZYGzOKl57U+A23pSjzROTkwwIhAJukqrQ9KVvEnvrR7icikoBIkdJHpQTA6aQNfEhdJDwE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192467}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "gitHead": "d7e68dd4ff5e9706aaa21c4f189ec7ad30f04b3b", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__ scripts", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "15.14.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"dequal": "^2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.2.1", "eslint": "^8.26.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.191.0", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "babel-jest": "^29.2.1", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "eslint-plugin-jest": "^27.1.3", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.2.1_1686620178091_0.536042574812716", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "aria-query", "version": "5.3.0", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "650c569e41ad90b51b3d7df5e5eed1c7549c103e", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz", "fileCount": 156, "integrity": "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==", "signatures": [{"sig": "MEYCIQC0wGg8cbG1NA7XCRMZFeGZv5MYRf6AIndp/bPEwF6RYgIhAOGQk9R9EZnOEFhii24A243i1EkQaYr2UNZ1PJi1HI5/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192147}, "jest": {"roots": ["<rootDir>/__tests__"], "coverageDirectory": "reports", "coverageReporters": ["lcov"]}, "main": "lib/index.js", "gitHead": "fff6f07c714e8048f4fe084cec74f24248e5673d", "scripts": {"flow": "flow", "jest": "jest --coverage __tests__/**/*", "lint": "eslint  --config .eslintrc src __tests__ scripts", "test": "npm run jest", "build": "rimraf lib && babel src --out-dir lib", "pretest": "npm run lint:fix && npm run flow", "test:ci": "npm run jest -- --ci --runInBand", "lint:fix": "npm run lint -- --fix", "output_as_hack": "babel-node ./scripts/output_as_hack.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "15.14.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"dequal": "^2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.2.1", "eslint": "^8.26.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.191.0", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "babel-jest": "^29.2.1", "@babel/core": "^7.19.6", "@babel/node": "^7.22.5", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "eslint-plugin-jest": "^27.1.3", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.3.0_1687639734877_0.9238746718139483", "host": "s3://npm-registry-packages"}}, "5.3.1": {"name": "aria-query", "version": "5.3.1", "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "aria-query@5.3.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "homepage": "https://github.com/A11yance/aria-query#readme", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "dist": {"shasum": "ebcb2c0d7fc43e68e4cb22f774d1209cb627ab42", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.1.tgz", "fileCount": 157, "integrity": "sha512-Z/ZeOgVl7bcSYZ/u/rh0fOpvEpq//LZmdbkXyc7syVzjPAhfOa9ebsdTSjEBDU4vs5nC98Kfduj1uFo0qyET3g==", "signatures": [{"sig": "MEYCIQDlZ7GRuhl3Lhgx6YkjilY2eC008ArcCbAlUXJbgBSs0QIhAI+SloE3e0ShzO5a4RNTqG1gyFiWu6UMeloQP8b7fv5v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176988}, "main": "lib/index.js", "engines": {"node": ">= 0.4"}, "gitHead": "ee003d2af54b6bc49a7aba231fedfd0f8b2c8610", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "build": "babel src --out-dir lib", "prepack": "npm run build", "pretest": "npm run lint && flow", "posttest": "npm audit --production", "prebuild": "<PERSON><PERSON><PERSON> lib", "tests-only": "nyc tape -r @babel/register '__tests__/**/*.js'", "build:tests": "npm run build && rimraf __tests-built__ && BABEL_ENV=test babel __tests__ --out-dir __tests-built__", "tests-built": "nyc tape '__tests-built__/**/*.js'", "output_as_hack": "babel-node ./scripts/output_as_hack.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/A11yance/aria-query.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Programmatic access to the ARIA specification", "directories": {}, "_nodeVersion": "22.8.0", "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "_hasShrinkwrap": false, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.8.1", "eslint": "^8.57.0", "expect": "^29.2.1", "rimraf": "^3.0.2", "flow-bin": "^0.191.0", "minimist": "^1.2.7", "commander": "^9.4.1", "@babel/cli": "^7.19.3", "@babel/core": "^7.24.7", "@babel/node": "^7.22.5", "mock-property": "^1.0.3", "object.values": "^1.2.0", "object-inspect": "^1.13.2", "@babel/register": "^7.24.6", "deep-equal-json": "^1.0.0", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "@babel/eslint-parser": "^7.19.1", "eslint-plugin-flowtype": "^8.0.3", "babel-plugin-module-resolver": "^5.0.2", "@babel/plugin-transform-react-jsx": "^7.20.7"}, "_npmOperationalInternal": {"tmp": "tmp/aria-query_5.3.1_1726177670200_0.9952039322145017", "host": "s3://npm-registry-packages"}}, "5.3.2": {"name": "aria-query", "version": "5.3.2", "description": "Programmatic access to the ARIA specification", "main": "lib/index.js", "scripts": {"prepack": "npm run build", "prebuild": "<PERSON><PERSON><PERSON> lib", "build": "babel src --out-dir lib", "lint": "eslint --ext=js,mjs .", "prepublishOnly": "npm run build", "pretest": "npm run lint && flow", "build:tests": "npm run build && rimraf __tests-built__ && BABEL_ENV=test babel __tests__ --out-dir __tests-built__", "tests-built": "nyc tape --strict -r array.from/auto '__tests-built__/**/*.js'", "tests-only": "nyc tape --strict -r @babel/register '__tests__/**/*.js'", "test": "npm run tests-only", "posttest": "npm audit --production", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "repository": {"type": "git", "url": "git+https://github.com/A11yance/aria-query.git"}, "keywords": ["accessibility", "ARIA"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "homepage": "https://github.com/A11yance/aria-query#readme", "devDependencies": {"@babel/cli": "^7.25.6", "@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/node": "^7.25.0", "@babel/plugin-transform-react-jsx": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-flow": "^7.24.7", "@babel/register": "^7.24.6", "array.from": "^1.1.6", "array.prototype.some": "^1.1.6", "babel-plugin-module-resolver": "^5.0.2", "commander": "^9.4.1", "deep-equal-json": "^1.0.0", "eslint": "^8.57.0", "eslint-plugin-flowtype": "^8.0.3", "flow-bin": "^0.191.0", "mock-property": "^1.1.0", "nyc": "^10.3.2", "object-inspect": "^1.13.2", "object.values": "^1.2.0", "rimraf": "^2.7.1", "tape": "^5.9.0"}, "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "engines": {"node": ">= 0.4"}, "_id": "aria-query@5.3.2", "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "gitHead": "f347e71985bbd5fc8307d487af82ca6f6b3c2d56", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==", "shasum": "93f81a43480e33a338f19163a3d10a50c01dcd59", "tarball": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz", "fileCount": 157, "unpackedSize": 176114, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDu5czYm95pZI4kaApINv8fYD95RXgrLT6Vxi0fMOwveAIhAKBaEPS5ilaZlOeKTW4ZI/TqSxknYUISiK8NpOx8Qqi/"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/aria-query_5.3.2_1726862068117_0.39492304637835907"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-01-24T01:05:13.195Z", "modified": "2024-09-20T19:54:28.616Z", "0.1.0": "2017-01-24T01:05:13.195Z", "0.2.0": "2017-01-24T01:46:45.709Z", "0.3.0": "2017-01-31T21:50:59.956Z", "0.4.0": "2017-03-11T00:07:57.300Z", "0.5.0": "2017-04-11T22:58:08.340Z", "0.6.0": "2017-05-31T23:51:09.413Z", "0.7.0": "2017-06-04T22:20:43.902Z", "0.7.1": "2018-01-31T23:06:31.216Z", "1.0.1": "2018-03-27T21:30:57.794Z", "2.0.0": "2018-03-27T21:42:09.064Z", "2.0.1": "2018-06-24T23:11:24.474Z", "3.0.0": "2018-06-24T23:54:33.817Z", "4.0.0": "2019-11-28T22:11:43.527Z", "4.0.1": "2019-12-02T02:41:10.251Z", "4.0.2": "2020-02-08T19:56:42.115Z", "4.2.0": "2020-06-17T23:54:16.090Z", "4.2.1": "2020-06-19T18:54:19.562Z", "4.2.2": "2020-06-19T19:33:54.480Z", "5.0.0": "2021-10-04T00:36:37.196Z", "5.0.1": "2022-08-26T00:03:15.545Z", "5.0.2": "2022-08-26T00:21:47.707Z", "5.1.1": "2022-10-23T03:26:39.634Z", "5.1.2": "2022-10-28T05:07:50.560Z", "5.1.3": "2022-10-31T05:06:36.330Z", "5.2.0": "2023-06-12T23:54:17.929Z", "5.2.1": "2023-06-13T01:36:18.324Z", "5.3.0": "2023-06-24T20:48:55.075Z", "5.3.1": "2024-09-12T21:47:50.403Z", "5.3.2": "2024-09-20T19:54:28.364Z"}, "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://github.com/A11yance/aria-query#readme", "keywords": ["accessibility", "ARIA"], "repository": {"type": "git", "url": "git+https://github.com/A11yance/aria-query.git"}, "description": "Programmatic access to the ARIA specification", "contributors": [{"name": "<PERSON>"}, {"name": "Facebook Inc."}], "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sundress", "email": "<EMAIL>"}, {"name": "ev<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# ARIA Query\n\n![CI](https://github.com/A11yance/aria-query/workflows/CI/badge.svg)\n\nProgrammatic access to the [WAI-ARIA 1.2 Roles Model](https://www.w3.org/TR/wai-aria-1.2/#roles).\nThis package tracks the W3C Recommendation (last update: 6 June 2023).\n\nCDN URL: <https://unpkg.com/aria-query>\n\n## Building the `src/etc` files\n\nThe files under `src/etc` are generated by the `breakUpAriaJSON` script.\n\nTo change them, edit the file `scripts/roles.json` then run:\n\n```bash\nnode ./scripts/breakUpAriaJSON.js\ngit add scripts/roles.json src/etc\n```\n\nIt should work with Node version 6.11.2 or later.\n\n## Utilities\n\n### Interface\n\nThese methods are available on each export from the module. The typing here in the documentation is pseudo-typed. Each export will have its own specific types for each method signature.\n\n```javascript\n{|\n  entries: () => Array<$Item>,\n  get: (key: $Key) => ?$Value,\n  has: (key: $Key) => boolean,\n  keys: () => Array<$Key>,\n  values: () => Array<$Value>,\n|};\n```\n\n### Roles\n\n```javascript\nimport { roles } from 'aria-query';\n```\n\nA map of role names to the role definition. For example:\n\n```javascript\nlet alertRole = roles.get('alert');\n/**\n * Value of alertRole\n * {\n *   \"requiredProps\": {},\n *   \"props\": {\n *     \"aria-atomic\": \"true\",\n *     \"aria-busy\": null,\n *     \"aria-controls\": null,\n *     \"aria-current\": null,\n *     \"aria-describedby\": null,\n *     \"aria-details\": null,\n *     \"aria-disabled\": null,\n *     \"aria-dropeffect\": null,\n *     \"aria-errormessage\": null,\n *     \"aria-expanded\": null,\n *     \"aria-flowto\": null,\n *     \"aria-grabbed\": null,\n *     \"aria-haspopup\": null,\n *     \"aria-hidden\": null,\n *     \"aria-invalid\": null,\n *     \"aria-keyshortcuts\": null,\n *     \"aria-label\": null,\n *     \"aria-labelledby\": null,\n *     \"aria-live\": \"assertive\",\n *     \"aria-owns\": null,\n *     \"aria-relevant\": null,\n *     \"aria-roledescription\": null\n *   },\n *   \"abstract\": false,\n *   \"childrenPresentational\": false,\n *   \"baseConcepts\": [],\n *   \"relatedConcepts\": [ {\n *     \"module\": \"XForms\",\n *     \"concept\": {\n *       \"name\": \"alert\"\n *     }\n *   }],\n *   \"superClass\": [[\"roletype\", \"structure\", \"section\"]]\n * }\n```\n\n### Elements to Roles\n\n```javascript\nimport { elementRoles } from 'aria-query';\n```\n\nHTML Elements with inherent roles are mapped to those roles. In the case of an element like `<input>`, the element often requires a `type` attribute to map to an ARIA role.\n\n```javascript\n[\n  [ '{\"name\": \"article\"}', [ 'article' ] ],\n  [ '{\"name\": \"button\"}', [ 'button' ] ],\n  [ '{\"name\": \"td\"}', [ 'cell', 'gridcell' ] ],\n  [ '{\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"checkbox\"}] }', [ 'checkbox' ] ],\n  [ '{\"name\": \"th\"}', [ 'columnheader' ] ],\n  [ '{\"name\": \"select\"}', [ 'combobox', 'listbox' ] ],\n  [ '{\"name\": \"menuitem\"}', [ 'command', 'menuitem' ] ],\n  [ '{\"name\": \"dd\"}', [ 'definition' ] ],\n  [ '{\"name\": \"figure\"}', [ 'figure' ] ],\n  [ '{\"name\": \"form\"}', [ 'form' ] ],\n  [ '{\"name\": \"table\"}', [ 'grid', 'table' ] ],\n  [ '{\"name\": \"fieldset\"}', [ 'group' ] ],\n  [ '{\"name\": \"h1\"}', [ 'heading' ] ],\n  [ '{\"name\": \"h2\"}', [ 'heading' ] ],\n  [ '{\"name\": \"h3\"}', [ 'heading' ] ],\n  [ '{\"name\": \"h4\"}', [ 'heading' ] ],\n  [ '{\"name\": \"h5\"}', [ 'heading' ] ],\n  [ '{\"name\": \"h6\"}', [ 'heading' ] ],\n  [ '{\"name\": \"img\"}', [ 'img' ] ],\n  [ '{\"name\": \"a\"}', [ 'link' ] ],\n  [ '{\"name\": \"link\"}', [ 'link' ] ],\n  [ '{\"name\": \"ol\"}', [ 'list' ] ],\n  [ '{\"name\": \"ul\"}', [ 'list' ] ],\n  [ '{\"name\": \"li\"}', [ 'listitem' ] ],\n  [ '{\"name\": \"nav\"}', [ 'navigation' ] ],\n  [ '{\"name\": \"option\"}', [ 'option' ] ],\n  [ '{\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"radio\"}] }', [ 'radio' ] ],\n  [ '{\"name\": \"frame\"}', [ 'region' ] ],\n  [ '{\"name\": \"rel\"}', [ 'roletype' ] ],\n  [ '{\"name\": \"tr\"}', [ 'row' ] ],\n  [ '{\"name\": \"tbody\"}', [ 'rowgroup' ] ],\n  [ '{\"name\": \"tfoot\"}', [ 'rowgroup' ] ],\n  [ '{\"name\": \"thead\"}', [ 'rowgroup' ] ],\n  [ '{\"name\": \"th\", \"attributes\": [ {\"name\": \"scope\", \"value\": \"row\"}] }', [ 'rowheader' ] ],\n  [ '{\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"search\"}] }', [ 'searchbox' ] ],\n  [ '{\"name\": \"hr\"}', [ 'separator' ] ],\n  [ '{\"name\": \"dt\"}', [ 'term' ] ],\n  [ '{\"name\": \"dfn\"}', [ 'term' ] ],\n  [ '{\"name\": \"textarea\"}', [ 'textbox' ] ],\n  [ '{\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"text\"}] }', [ 'textbox' ] ],\n]\n```\n\nThe map of elements to roles is keyed by an HTML concept. An HTML concept corresponds to the `baseConcepts` and `relatedConcepts` of an ARIA role. Concepts exist in the context of a `module`: HTML, XForms, Dublin Core, for example.  The concept representation is an object literal with a name property (the element name) and an optional attributes array.\n\nThe roles are provided in a [Set](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set).\n\n### Role to element\n\n```javascript\nimport { roleElements } from 'aria-query';\n```\n\nARIA roles are mapped to the HTML Elements with the same inherent role. Some roles, such as `columnheader` are only mapped to an HTML element that expresses specific attributes. In the case of `<input>`, the element often requires a `type` attribute to map to an ARIA role.\n\n```javascript\n[\n  [ 'article', [ {\"name\": \"article\"} ] ],\n  [ 'button', [ {\"name\": \"button\"} ] ],\n  [ 'cell', [ {\"name\": \"td\"} ] ],\n  [ 'checkbox', [ {\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"checkbox\"}] } ] ],\n  [ 'columnheader', [ {\"name\": \"th\"} ] ],\n  [ 'combobox', [ {\"name\": \"select\"} ] ],\n  [ 'command', [ {\"name\": \"menuitem\"} ] ],\n  [ 'definition', [ {\"name\": \"dd\"}', '{\"name\": \"dfn\"} ] ],\n  [ 'figure', [ {\"name\": \"figure\"} ] ],\n  [ 'form', [ {\"name\": \"form\"} ] ],\n  [ 'grid', [ {\"name\": \"table\"} ] ],\n  [ 'gridcell', [ {\"name\": \"td\"} ] ],\n  [ 'group', [ {\"name\": \"fieldset\"} ] ],\n  [ 'heading', [ {\"name\": \"h1\"}', '{\"name\": \"h2\"}', '{\"name\": \"h3\"}', '{\"name\": \"h4\"}',  '{\"name\": \"h5\"}', '{\"name\": \"h6\"} ] ],\n  [ 'img', [ {\"name\": \"img\"} ] ],\n  [ 'link', [ {\"name\": \"a\"}', '{\"name\": \"link\"} ] ],\n  [ 'list', [ {\"name\": \"ol\"}', '{\"name\": \"ul\"} ] ],\n  [ 'listbox', [ {\"name\": \"select\"} ] ],\n  [ 'listitem', [ {\"name\": \"li\"} ] ],\n  [ 'menuitem', [ {\"name\": \"menuitem\"} ] ],\n  [ 'navigation', [ {\"name\": \"nav\"} ] ],\n  [ 'option', [ {\"name\": \"option\"} ] ],\n  [ 'radio', [ {\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"radio\"}] } ] ],\n  [ 'region', [ {\"name\": \"frame\"} ] ],\n  [ 'roletype', [ {\"name\": \"rel\"} ] ],\n  [ 'row', [ {\"name\": \"tr\"} ] ],\n  [ 'rowgroup', [ {\"name\": \"tbody\"}', '{\"name\": \"tfoot\"}', '{\"name\": \"thead\"} ] ],\n  [ 'rowheader', [ {\"name\": \"th\", \"attributes\": [ {\"name\": \"scope\", \"value\": \"row\"}] }, {\"name\": \"th\", \"attributes\": [ {\"name\": \"scope\", \"value\": \"rowgroup\"}] } ] ],\n  [ 'searchbox', [ {\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"search\"}] } ] ],\n  [ 'separator', [ {\"name\": \"hr\"} ] ],\n  [ 'table', [ {\"name\": \"table\"} ] ],\n  [ 'term', [ {\"name\": \"dt\"} ] ],\n  [ 'textbox', [ {\"name\": \"textarea\"}', '{\"name\": \"input\", \"attributes\": [ {\"name\": \"type\", \"value\": \"text\"}] } ] ],\n]\n```\n\n## License\n\nCopyright (c) 2021 A11yance\n", "readmeFilename": "README.md"}