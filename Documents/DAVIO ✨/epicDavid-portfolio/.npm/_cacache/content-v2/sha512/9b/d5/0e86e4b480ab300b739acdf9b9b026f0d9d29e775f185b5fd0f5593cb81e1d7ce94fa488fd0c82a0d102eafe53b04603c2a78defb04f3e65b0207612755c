{"_id": "@rollup/rollup-linux-arm64-musl", "_rev": "127-bb9a811d1edb55ec71ae17c7968f9233", "name": "@rollup/rollup-linux-arm64-musl", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "eea295c70fb2a97476cef6f44ff5233a8ee73870", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-/2oPDPDCby4mY2P5xaw6d34Q52sb4X/IgUsoYTkDttapxtnMdQTdD55GhCJhydbozXkn4EC8jbnlQieCB+eE+g==", "signatures": [{"sig": "MEUCIFpnmuE4Zz9M0v0noo4lpKHEhJkSg9DpV790B3XeHodvAiEAvcJ3fhPm6qOqmwIMSxh3Af/ceVjQfihvJKTSFz8tAd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628414}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.0.1_1696595806169_0.7390000130734236", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "bbdda4472d932f4c275b97f6f826b4648d458ab6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-cLuBp7rOjIB1R2j/VazjCmHC7liWUur2e9mFflLJBAWCkrZ+X0+QwHLvOQakIwDymungzAKv6W9kHZnTp/Mqrg==", "signatures": [{"sig": "MEYCIQD7SecMDKdPpHR0gskD6r93p7yhY+pN9KB43xdIEEM/9QIhAIy+fM7O4cRlTtwDBk42kTE9ydxXYJvfvQ1IAWuEpAeh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628414}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.0.2_1696601925885_0.26433814688424295", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d1178c6742f4440feae5de8dbc760cb557d67f3e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-OXqp0PUZ3x/fxoT/WooO+kxO6Bxznr0lRA2qj0FjGvMEywfMTKKBcfSd3QorVcxmAKDEA5IDwD0aUPqyYDx2qw==", "signatures": [{"sig": "MEUCIEjpmgPlKGhFbp8wLe3JB+NRJa4vYlAGejotQejsY1SsAiEAuwolsyrpr4nzm71zihkxE39T3xAn7reovPxhjWHCjW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2687750}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.1.0_1697262737982_0.6994864322242049", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a1bc58cd47c2b400a582485f288fdb5c97e466d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-idxA8GbyacMdJ9HKPFa0J7Ok49O4ZKODmlN6kzMZzaAXg5xuhCTddo0Cw4MgGGjjNsDcTuWk4giuQBL2X+TwXQ==", "signatures": [{"sig": "MEQCIDvrriGQE2JIEhsDK13F6MxkkJ3npQ2n3uHFu+LAam73AiAR/LAIfQhdRrSih7T0lS0xWT/ekUh8WiB8FnGDGSwAeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2687750}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.1.1_1697351511972_0.728960338423305", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "267f801a7d8c61509f6fb7ab4849a1260101892f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-6yFpmrH6xx00b9qEoabkyeHzamKSv8c43+SgaItTcHzgaaX3HGRSK+ZZt9GokB3Cc7z4HXllK7Ts5fF5W8FddQ==", "signatures": [{"sig": "MEQCIEbhKE9GkhFVmGW9XQY8Q1JoakU6IFaDcQF9CmsiDmi5AiAarYQZPQXYyQKxJFZWy14cmA5Dl1jtCU1yAyIPnKr06Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2687750}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.1.3_1697392110558_0.1295385708949317", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "08d30969483a804769deb6e674fe963c21815ad9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-aVPmNMdp6Dlo2tWkAduAD/5TL/NT5uor290YvjvFvCv0Q3L7tVdlD8MOGDL+oRSw5XKXKAsDzHhUOPUNPRHVTQ==", "signatures": [{"sig": "MEUCIDhugdMEgU3TSkAyj/UAOnCA4OqZF5jXGuUeunDqY+/cAiEAlGjow3P+4INq7oeSatiOO89y1ItausncM+yCmUSQ310=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2671366}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.1.4_1697430853450_0.5130048318374714", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d9629d2cdd3a16a9001715e13a53de27030b9ba6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-84aBKNAVzTU/eG3tb2+kR4NGRAtm2YVW/KHwkGGDR4z1k4hyrDbuImsfs/6J74t6y0YLOe9HOSu7ejRjzUBGVQ==", "signatures": [{"sig": "MEUCID6ZQQrZqu8b5zxHt0W38ZuM7r3TaXVGjZlqeDws7aouAiEAyamRtcxpNTbGaQ/j69lXiin4b9ApSIb4FWAOkLJ/0yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2675462}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.1.5_1698485015985_0.9935700079262115", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "468530ecdf6a196055e9391e0fbcc6c68576d951", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-DjFHrawLoh/21sUhcv+Veg4ev1WDVle8izmXpUCOmWkWmeWLACfiEJa9aI/L/trEVB46/SmcRomJw0DbfzKdoQ==", "signatures": [{"sig": "MEYCIQDh6M0v6j3iJNL6q0Wjqq8NteRfsvuyCug71HiqK0PVeQIhAJpWhPDrDSL0U7YnRoBGy2s4Qw0wC2p3NOeE/HKW0yO+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2675462}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.1.6_1698731118864_0.3768267294639718", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9291c7ec1a3572e9d3f395bcfff388ff173626ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-Jp1NxBJpGLuxRU2ihrQk4IZ+ia5nffobG6sOFUPW5PMYkF0kQtxEbeDuCa69Xif211vUOcxlOnf5IOEIpTEySA==", "signatures": [{"sig": "MEUCIQDJeCSv1Wx4ux7pWP7YPKGNDLylNm2zxCfadGFUIS0OxQIgfCLMYO11873oPK3m/z0Zj+uu3a4y48XXvkpbr6YD1f0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2691846}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.2.0_1698739844556_0.8663917819679967", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "64795a09dac02b4d779819509a793b93ba7e4c0d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-tNhfYqFH5OxtRzfkTOKdgFYlPSZnlDLNW4+leNEvQZhwTJxoTwsZAAhR97l3qVry/kkLyJPBK+Q8EAJLPinDIg==", "signatures": [{"sig": "MEYCIQCVLi5NaB3yalUujH0WaDqikM0V52fyqjpatF7ZP6Lq3QIhANDzJ0dVRyvKRt7ok4s7U83JocVuSTGgnrGYG98IX7EL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2700038}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.3.0_1699042386647_0.019734702364687662", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f9cac3b14330d68b3395e14c2ba0549fe4d0b420", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-/QqGJI0Jk/Ln32EmpkJYmwpKIe+Da40zmJL8YYvJKYQWhvj7qYOJM6HntQndTWNpF5/33vpLVhngCaHqmiVhNg==", "signatures": [{"sig": "MEUCIQCjlVQVPAQrVnV4BkN74zXnsTKFIH7HwwaYunNw/xiTtgIgDzj39tDbBMVBilMOI1M7CtRSxzlZUhuXgsCot4zrcok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2653222}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.3.1_1699689477895_0.982588408523992", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4172c45a08928aa992c70d5af64cd87f5914fd50", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-u7zy0Ygzl7O5Gvr9TSNSQj+DBzvMJC7rXfyQNgZ13KwkhgJ8z0z+gt2AO4RPd01rZioMQ2/TA24XGGg4xqhd0Q==", "signatures": [{"sig": "MEUCIQC3b3jsJnEyHkDH4155ZBo04SdV1vxwngxGE03ZckvbrAIgc6DxZwuVcUp1zi/hs97d6asnD6lVg6FSd93iHVljLYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2292774}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.4.0_1699775397050_0.8654100927069623", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d452e88a02755f449f6e98d4ce424d655ef42cfe", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-M2sDtw4tf57VPSjbTAN/lz1doWUqO2CbQuX3L9K6GWIR5uw9j+ROKCvvUNBY8WUbMxwaoc8mH9HmmBKsLht7+w==", "signatures": [{"sig": "MEQCIAQtc7/dPWUaHIKz735saCWtPmSutNKkzIQ68i01/CIgAiAEegV3mn3F6ZY1/q+BUu1dx/h4NNIMKhORkA3LvL39sA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2292774}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.4.1_1699939519642_0.8535285080169097", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "149cab95107821fe4ae46d5f2c0658c5b0e56b9c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-1H7wBbQuE6igQdxMSTjtFfD+DGAudcYWhp106z/9zBA8OQhsJRnemO4XGavdzHpGhRtRxbgmUGdO3YQgrWf2RA==", "signatures": [{"sig": "MEYCIQCey6KKgGIwEpCJ1aDRQunizYVrrxBAU3CnLyj2ZuKGuAIhAMtpI5u2L+R/er2a836d1EUzFXaXKGwUYOrG3Pl7BuIc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2288678}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.5.0_1700286735526_0.16607406131148728", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f7e8036c2f771bb366ca0d8c79d2132cffb1d295", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-f5Gs8WQixqGRtI0Iq/cMqvFYmgFzMinuJO24KRfnv7Ohi/HQclwrBCYkzQu1XfLEEt3DZyvveq9HWo4bLJf1Lw==", "signatures": [{"sig": "MEYCIQCv42blx+En19v02eblrfRd8aQINPWX01Yg5qbXkZBZGAIhAP3qzB7Ucvhwi9BtUFtgqUGzImB4LL6EtF6MTCvGZHn3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2288678}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.5.1_1700597592796_0.6159391377039609", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "32117bb51bdd761b8f759c9fed9eab493154cdfd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-KrRnuG5phJx756e62wxvWH2e+TK84MP2IVuPwfge+GBvWqIUfVzFRn09TKruuQBXzZp52Vyma7FjMDkwlA9xpg==", "signatures": [{"sig": "MEQCIDK6SQt896SFQH+KjpU4zlnM348CRKRebhPvLAEymPqqAiAG6A5KReog3uH10eI9a7j8I84qMvaadX61Aqf/7KS7cQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2284582}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.5.2_1700807390580_0.9175293357130916", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ef9cae3d22c8c44ff4f271e308bf1c013348bdc0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-mZoNQ/qK4D7SSY8v6kEsAAyDgznzLLuSFCA3aBHZTmf3HP/dW4tNLTtWh9+LfyO0Z1aUn+ecpT7IQ3WtIg3ViQ==", "signatures": [{"sig": "MEQCIEZeSLTvrEZBeoKPwB36pNJNnxn17V8dFxABAfTaBhZsAiBbcnjWx9mays/fRKMEhQWuBtJ8IZh/wiqY5Ki3rbS4nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2284582}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.6.0_1701005956767_0.42681417027486734", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6a94c691830dc29bf708de7c640f494996130893", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-FfoOK1yP5ksX3wwZ4Zk1NgyGHZyuRhf99j64I5oEmirV8EFT7+OhUZEnP+x17lcP/QHJNWGsoJwrz4PJ9fBEXw==", "signatures": [{"sig": "MEQCIH2EyZyzgwpHC2JfydDigXwmTgiketUpGhWwpg2x6msaAiBL+++Gq6EKPT92t0ChJuPkzNI0ZDK8Yoq+vShtfLG8gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2284582}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.6.1_1701321792189_0.5045890832003517", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d75add714d898cee4e4a5baeb2e30641e483b0e3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-nhWwYsiJwZGq7SyR3afS3EekEOsEAlrNMpPC4ZDKn5ooYSEjDLe9W/xGvoIV8/F/+HNIY6jY8lIdXjjxfxopXw==", "signatures": [{"sig": "MEYCIQDT3k+1WDPxFMg0AYkIbRGpU8RIkXVtqlbBUlIAerpwIwIhAIDN7+/tmeh/cD1DABF9IHiVd3QVyAPKhAm6HQ6wPyPX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296870}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.7.0_1702022284018_0.24676318915531636", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "bc994c676fd3aae14aaa905040fdcde461e41ce5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-Fw9ChYfJPdltvi9ALJ9wzdCdxGw4wtq4t1qY028b2O7GwB5qLNSGtqMsAel1lfWTZvf4b6/+4HKp0GlSYg0ahA==", "signatures": [{"sig": "MEUCIQDXEdzEyhQnlquyihVDjpGU5d7UUGX1hhIN4d5Vezf78QIgUoC4Dkj/DCOvPh+TlpESKStfi4IpOYrI8VhPzwhT+g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296870}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.8.0_1702275896599_0.24838209756094853", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "61cc6516e6e92e2205ea1d0ac30326379b0563c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-ahqcSXLlcV2XUBM3/f/C6cRoh7NxYA/W7Yzuv4bDU1YscTFw7ay4LmD7l6OS8EMhTNvcrWGkEettL1Bhjf+B+w==", "signatures": [{"sig": "MEYCIQDC8pJmgsFtdWH6ziksy/oEfKed3ngjfGQYQBNQLk9QYwIhAO8eIDVjxhmPCnE/ZBhOSDdU9MaJPccFeTM7apruAkC4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296870}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.0_1702459459913_0.2406126344032795", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d3a4e1c9f21eef3b9f4e4989f334a519a1341462", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-0XSYN/rfWShW+i+qjZ0phc6vZ7UWI8XWNz4E/l+6edFt+FxoEghrJHjX1EY/kcUGCnZzYYRCl31SNdfOi450Aw==", "signatures": [{"sig": "MEYCIQDt35i7WMMWbcIUAo86K9ropYA4eVPWk1/5eKV1brLY9QIhAJoEXbCSEN7C04xK2FRWNx0XsOgcDICK/K4dN3WTirnD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296870}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.1_1702794374435_0.8697296681785338", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "82b5e75484d91c25d4e649d018d9523e72d6dac2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-On+cc5EpOaTwPSNetHXBuqylDW+765G/oqB9xGmWU3npEhCh8xu0xqHGUA+4xwZLqBbIZNcBlKSIYfkBm6ko7g==", "signatures": [{"sig": "MEUCIQCYCAdmFf32iU+Gl/kXzxNEyVD3IZb6THFac5XhZ2HW7wIgGUIl548w3iHjlt4lR3IrMiC2cmFYqA+DdcE1G7xr9eU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2284582}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.2_1703917410651_0.12497359861793966", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8cc30293277df26a3fa3dc015682edac3f663baf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-TZJUfRTugVFATQToCMD8DNV6jv/KpSwhE1lLq5kXiQbBX3Pqw6dRKtzNkh5wcp0n09reBBq/7CGDERRw9KmE+g==", "signatures": [{"sig": "MEYCIQDDNBvYIjKJEWC5+LBS/caQH+0KuEPD8FICHsRsmB2vrAIhAIY7olCuob8rZXVDRtaGmbOmiBenAA+kr1PeRXWl+b2G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2292774}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.3_1704435649946_0.515760461184078", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "50257fb248832c2308064e3764a16273b6ee4615", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-XcKvuendwizYYhFxpvQ3xVpzje2HHImzg33wL9zvxtj77HvPStbSGI9czrdbfrf8DGMcNNReH9pVZv8qejAQ5A==", "signatures": [{"sig": "MEQCIEfQFZPwKEB/2YGLuB1Q8N59jzCd1OCWSt0pMGh/aSuRAiAyxBLnNOxMEJ2bawWdX9CZPjXaqkSjDfNp6EMYYBgZww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2292774}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.4_1704523143244_0.3429187719473201", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3064060f568a5718c2a06858cd6e6d24f2ff8632", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-QaKFVOzzST2xzY4MAmiDmURagWLFh+zZtttuEnuNn19AiZ0T3fhPyjPPGwLNdiDT82ZE91hnfJsUiDwF9DClIQ==", "signatures": [{"sig": "MEUCIQD1RMb+QM6u8C+AzGNMVd3OL+Rg1rayFxbL97ocPeILsgIgWrPNrNxJUxMWoP937thG6N2oQX2mzmanCpXOBQDzy1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296870}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.5_1705040176716_0.12989791532749662", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "94aaf95fdaf2ad9335983a4552759f98e6b2e850", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-gpiG0qQJNdYEVad+1iAsGAbgAnZ8j07FapmnIAQgODKcOTjLEWM9sRb+MbQyVsYCnA0Im6M6QIq6ax7liws6eQ==", "signatures": [{"sig": "MEQCICLVwQX139EIHireJ++HnO860kyhbwGKygqPITI7RqljAiApr8DWRZV3TIvzcT3KHDLdFBbzTmTZM73X48av+OlaPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280486}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.9.6_1705816342076_0.8393743276361627", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5692e1a0feba0cc4a933864961afc3211177d242", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-9GW9yA30ib+vfFiwjX+N7PnjTnCMiUffhWj4vkG4ukYv1kJ4T9gHNg8zw+ChsOccM27G9yXrEtMScf1LaCuoWQ==", "signatures": [{"sig": "MEUCIQCj5mog/rTvxIj9h5+sKXAaInXBaHB6r8r41yn31mLAzQIgYjDynmw5ZQz/vAGAx6F4xASEMx5OS0Oc4cpZKRuofh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370599}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.10.0_1707544724440_0.010723200394800791", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4860560d18d9f18568859a800b1e79fe3b85df9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-yFW2msTAQNpPJaMmh2NpRalr1KXI7ZUjlN6dY/FhWlOclMrZezm5GIhy3cP4Ts2rIAC+IPLAjNibjp1BsxCVGg==", "signatures": [{"sig": "MEUCIQD8re8FH11TEF9yl55jx/t1+r4myuBTD1MMZY+mhQFNVwIgF27nb47t2IfVr/fNwq5Wc46c07RCauFsjRokDybZ5bk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370599}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.11.0_1707977383138_0.38779179240341866", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3882a4e3a564af9e55804beeb67076857b035ab7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-eTvzUS3hhhlgeAv6bfigekzWZjaEX9xP9HhxB0Dvrdbkk5w/b+1Sxct2ZuDxNJKzsRStSq1EaEkVSEe7A7ipgQ==", "signatures": [{"sig": "MEUCIC4NTqUWF0e1qjI6C3GzEQ+eCjSRaXW0SpMUpE4h4QczAiEAk3KO+SjX7pGEttAb4z+7Vx+9W6fqjdT0ErdobDIXBdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370599}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.12.0_1708090340396_0.6704997247581153", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d3b4cd6ef18d0aa7103129755e0c535701624fac", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-qB6AFRXuP8bdkBI4D7UPUbE7OQf7u5OL+R94JE42Z2Qjmyj74FtDdLGeriRyBDhm4rQSvqAGCGC01b8Fu2LthQ==", "signatures": [{"sig": "MEUCIGhLqYst9azbi+hCrluKhl7Tazsvf58DIgpNFrBexDE8AiEA9nXPDwvgw5WmjMTl/hJ6MZ/M+XTey4bvVVTtfwDZlCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370599}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.12.1_1709705018499_0.07147504515189151", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f2ae7d7bed416ffa26d6b948ac5772b520700eef", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-C31QrW47llgVyrRjIwiOwsHFcaIwmkKi3PCroQY5aVq4H0A5v/vVVAtFsI1nfBngtoRpeREvZOkIhmRwUKkAdw==", "signatures": [{"sig": "MEYCIQCmc2bnPXIEJ4mE8EsgNj9S+kWN1LhVJt5B7skCflx2mQIhAOMLxBgB87DlC/5JLrCW3yybfPIrMpx8GdeMjiTdlLbW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2354215}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.13.0_1710221319958_0.20599997795705138", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "36d91d6218cb3d816cea4a4ead71ac3bc63aee22", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-5eneF/eOs8GqXmj4ME9cR+c2cJf2POpXfHKUivNHn6RReP8T12hJo0PxNS4eEBRdikFMag/x6ysLYAFdGmFetQ==", "signatures": [{"sig": "MEUCIQCiFLCw/wgoqEP5IXK9Qj97lQ4dRJKBMQ8QcDRpzrYcFQIgY1vpVI5TCQzm1YL/UUTNbw/n9Us6rK2W2gbacd+KnD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2354217}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.13.1-1_1711265965488_0.32250567671563624", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "bf64eaa29b2b1e6bc9195f04bb30b2a4ffdc25ae", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-ssp77SjcDIUSoUyj7DU7/5iwM4ZEluY+N8umtCT9nBRs3u045t0KkW02LTyHouHDomnMXaXSZcCSr2bdMK63kA==", "signatures": [{"sig": "MEQCIEo+tVPHKYsXDAjh6dHUGsVitYiJaG/VXmXCLAqsW2guAiBW2Lu5o3P6e0OU0SkwZnGKY6MZwqUvgKovGN3V7UAIpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2354215}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.13.1_1711535264626_0.4835274135751002", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f1fb4c6f961d3f3397231a99e621d199200e4ea9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-tK5eoKFkXdz6vjfkSTCupUzCo40xueTOiOO6PeEIadlNBkadH1wNOH8ILCPIl8by/Gmb5AGAeQOFeLev7iZDOA==", "signatures": [{"sig": "MEQCIAF1yFg62lbING9Wy9wb+2c+niPvGr5V4oDyUA+CLzEfAiAhm8M0kur6DYc1h8W1Xe9TtI9eCfqRBwGR+y9QyUYjdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2354215}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.13.2_1711635218570_0.9512848707763499", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "13f475596a62e1924f13fe1c8cf2c40e09a99b47", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-nrRw8ZTQKg6+Lttwqo6a2VxR9tOroa2m91XbdQ2sUUzHoedXlsyvY1fN4xWdqz8PKmf4orDwejxXHjh7YBGUCA==", "signatures": [{"sig": "MEYCIQDpY/47+CC1Fo/sXnbmKO5nnhIhLM/6fwP+mgc15A7XPwIhANScxLc6k4dYsdLLRgRxZyhupbL6Qq1qaBkDlIfm1C6B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2362407}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.14.0_1712121777225_0.5864460245754208", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0b0ed35720aebc8f5e501d370a9ea0f686ead1e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-nDY6Yz5xS/Y4M2i9JLQd3Rofh5OR8Bn8qe3Mv/qCVpHFlwtZSBYSPaU4mrGazWkXrdQ98GB//H0BirGR/SKFSw==", "signatures": [{"sig": "MEUCIGZsMomK9CVdl9EY2/2Bvz/vBsPNUblr3EFEWcr3+2BwAiEA4KgPHY0JvGGSjalwAymukIc7C1NnilwAiHoHOhcWvhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2358311}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.14.1_1712475342690_0.8372308561916857", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8a2c55a72e0c716a15d830fee3bf5a1a756f13ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-cAOTjGNm84gc6tS02D1EXtG7tDRsVSDTBVXOLbj31DkwfZwgTPYZ6aafSU7rD/4R2a34JOwlF9fQayuTSkoclA==", "signatures": [{"sig": "MEYCIQDZ0FJ+fTTcs8TTYMvSfgKkkhQmywVwKhhk0wMzQ/6oFwIhALhgQXzS8465fD1MJ57/jmXqakrFzcX3taWJHtjxrqLx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2362407}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.14.2_1712903023033_0.14441028699924519", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "65bf944530d759b50d7ffd00dfbdf4125a43406f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-UrBoMLCq4E92/LCqlh+blpqMz5h1tJttPIniwUgOFJyjWI1qrtrDhhpHPuFxULlUmjFHfloWdixtDhSxJt5iKw==", "signatures": [{"sig": "MEYCIQD/aDAuCUOO9O8sa++BFB6Yl8U+I7BcAL2/J+c4IBzNWQIhAIi5DGyMiCQdJ+Xp8l1IbOUA/RFHE1JZqb4FN70ULpPE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2374695}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.14.3_1713165514668_0.9204649174643031", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "86376eaa6d65a860a046e0dfe285a51792bc2026", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-+ScJA4Epbx/ZQGjDnbvTAcb8ZD06b+TlIka2UkujbKf1I/A+yrvEcJwG3/27zMmvcWMQyeCJhbL9TlSjzL0B7Q==", "signatures": [{"sig": "MEYCIQClaNB4gKjsNMoZF5xGrOQNH1G8VXohBOlLwllwWS3s+gIhAIrULQEDei17H62/lFTGzWR1OGEFTiYKmAaCJhK3F7eQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2440231}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.15.0_1713591437242_0.06053226787827071", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fd00d2b08a045a0321776921353cee3cc6383cc8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-7UpYcO0uVeomnoL5MpQhrS0FT7xZUJrEXtKVLmps5bRA7x5AiA1PDuPnMbxcOBWjIM2HHIG1t3ndnRTVMIbk5A==", "signatures": [{"sig": "MEUCIQCXNQDnxZaNVpkM6o97k2JRTh6N+9BRt6kcNoQChuIjvQIgAqIR4AUoNpXaHyYSasMIE2REz/Lwi8cXNeKkKWtWFgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2440231}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.16.0_1713674520102_0.4122203842607577", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "86b5104635131182b6b2b6997c4aa5594ce557b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-KOvqGprlD84ueivhCi2flvcUwDRD20mAsE3vxQNVEI2Di9tnPGAfEu6UcrSPZbM+jG2w1oSr43hrPo0RNg6GGg==", "signatures": [{"sig": "MEQCIAUqgzrNW1MZf8C3/2Q07reMHXiusTMJoAn54T4OKVLcAiBvvAWfI5QtQux5ZEAgEYFwTLBQn5ISfKSvF56R1/A4Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2440231}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.16.1_1713724202656_0.9789884795290664", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fddc7730045301a7fb0132532890e5edcb23d2bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-ZBKvz3+rIhQjusKMccuJiPsStCrPOtejCHxTe+yWp3tNnuPWtyCh9QLGPKz6bFNFbwbw28E2T6zDgzJZ05F1JQ==", "signatures": [{"sig": "MEUCIH2JBLcn2R7oT+GtfALsPHyTeFX/U/I1LdM9fnl+4BtoAiEAyo+95p89ndM6njFfprJbU3qa7MRaJFIg490Xz7O2X30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2440231}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.16.2_1713799159825_0.07525850606141082", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ccbb2fc283cd4748198dced01677b70e29e1941d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-61SpQGBSb8QkfV/hUYWezlEig4ro55t8NcE5wWmy1bqRsRVHCEDkF534d+Lln/YeLUoSWtJHvvG3bx9lH/S6uA==", "signatures": [{"sig": "MEUCICzPYa8Rwk79YqN5+lmJuHQzY3P2q8Ab+jrxxpzAfiCPAiEAkICDx2h7MzjaW2H6UiOKLVe8fqzcRT56LeWsd6s6e+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2440231}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.16.3_1713849157604_0.6495731528788695", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e37ef259358aa886cc07d782220a4fb83c1e6970", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-zsFwdUw5XLD1gQe0aoU2HVceI6NEW7q7m05wA46eUAyrkeNYExObfRFQcvA6zw8lfRc5BHtan3tBpo+kqEOxmg==", "signatures": [{"sig": "MEQCIEHx7qw1LL9BH07bYbqkVChFXSz1i9/C0UkV9wxme9HWAiASEykmKsLAhGcFdK6WL6bVNj4O4fRR3DsVKM0fe6Vyrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2440231}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.16.4_1713878110613_0.3921429962208538", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c862f69b1b979bae65545c55f005ca227b695778", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-s2xAyNkJqUdtRVgNK4NK4P9QttS538JuX/kfVQOdZDI5FIKVAUVdLW7qhGfmaySJ1EvN/Bnj9oPm5go9u8navg==", "signatures": [{"sig": "MEQCIEMhieOb4T5OtqXpvrY6n4yZ/Ju1qrI9d48NavFVYN49AiBFv5D7rjUtu+xIni4vLvkqkAFmCBkt/0aaqlcsGzoJ/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2432039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.17.0_1714217394894_0.48772454357626116", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e6a4cdb552ff859b2fce275937999789ae72f659", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-ekggix/Bc/d/60H1Mi4YeYb/7dbal1kEDZ6sIFVAE8pUSx7PiWeEh+NWbL7bGu0X68BBIkgF3ibRJe1oFTksQQ==", "signatures": [{"sig": "MEYCIQDvkpHLRU9G+ZclgHr36nr7v5J171k5sgZQrptr/4G/kwIhAP7Anjte9pStUfCutHtWTvnSu0h7YjbEl0xDgiFHg6kO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2432039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.17.1_1714366677822_0.023819921492177487", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8acc16f095ceea5854caf7b07e73f7d1802ac5af", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-NMPylUUZ1i0z/xJUIx6VUhISZDRT+uTWpBcjdv0/zkp7b/bQDF+NfnfdzuTiB1G6HTodgoFa93hp0O1xl+/UbA==", "signatures": [{"sig": "MEYCIQDXHOcbq7l59uuKm+n3lfRTAAd3jItrZpt96I/ySgjQwAIhAOwGov8l6QoT7kkYguIVpYfnyOhmjRnCBA564YnZx2s6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2432039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.17.2_1714453249777_0.9145422641623475", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f53db99a45d9bc00ce94db8a35efa7c3c144a58c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-be6Yx37b24ZwxQ+wOQXXLZqpq4jTckJhtGlWGZs68TgdKXJgw54lUUoFYrg6Zs/kjzAQwEwYbp8JxZVzZLRepQ==", "signatures": [{"sig": "MEUCIFleWdHKkXo2Go4RZHVbLdqG+l5ZAkP4fZxgbobGT7KEAiEAnrLEX8rpYWTXFRFNl0Zqc+gdqZn+rbrokB6WJQl5bmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427943}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.18.0_1716354228229_0.6372350384907537", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d60af8c0b9be424424ff96a0ba19fce65d26f6ab", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-dJX9u4r4bqInMGOAQoGYdwDP8lQiisWb9et+T84l2WXk41yEej8v2iGKodmdKimT8cTAYt0jFb+UEBxnPkbXEQ==", "signatures": [{"sig": "MEQCICc4zvhpQ+mUFRPi5E2E0EOwE4/m4KBgEFsbUSEkx4uUAiBxh1EnwMruYRcf3fCWwZNxUX6y3fNRoqFw4ZyswR6wQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280519}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.18.1_1720452315795_0.7514388131158725", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6e63f7ad4cc51bd2c693a2826fd279de9eaa05b5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-GlIQRj9px52ISomIOEUq/IojLZqzkvRpdP3cLgIE1wUWaiU5Takwlzpz002q0Nxxr1y2ZgxC2obWxjr13lvxNQ==", "signatures": [{"sig": "MEUCIQCJWy3oOV5z11Ct8kAmj9lktTX9wEDbChOJ+YCS90nJ8AIgKlU4Aew8x0brjW8ibOxNZSdZl6CE+9QLghJsdMSb7So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2251847}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.19.0_1721454378368_0.015184774855924621", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3f59c2c6e60f75ce8b1090bd841c555e3bb01f0e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-89tFWqxfxLLHkAthAcrTs9etAoBFRduNfWdl2xUs/yLV+7XDrJ5yuXMHptNqf1Zw0UCA3cAutkAiAokYCkaPtw==", "signatures": [{"sig": "MEUCIQD/aFChV1OIy7IYannj11bjAt73OD5Oym91dATx+hH5awIgUDQrxyJULP4XUSqzTS6n7i3yr3/6D3n2RsQM6UJpKhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2255943}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.19.1_1722056045106_0.08332800772520943", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6d5ca6d3904309bec285ea5202d589cebb93dee4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-Hw3jSfWdUSauEYFBSFIte6I8m6jOj+3vifLg8EU3lreWulAUpch4JBjDMtlKosrBzkr0kwKgL9iCfjA8L3geoA==", "signatures": [{"sig": "MEYCIQD5EjC+g+0qT3Oj9HL4r4QRNf2qI+VGyI6otAqO8dX5uwIhAIuQWO1SoBm0aIq6X/jSKDs6QhqnkP1kED9krtHzJxKI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2174023}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.19.2_1722501177554_0.642790589184036", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "50eef7d6e24d0fe3332200bb666cad2be8afcf86", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-+it+mBSyMslVQa8wSPvBx53fYuZK/oLTu5RJoXogjk6x7Q7sz1GNRsXWjn6SwyJm8E/oMjNVwPhmNdIjwP135Q==", "signatures": [{"sig": "MEQCIAUXS7Dvh85KH0Lym77SyJ0xzc2OPWZda2wGFgrm25BdAiAe778/7DvfQlHuKUJaHV+cUf8AZL06Grjk4J5CN4f5Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178119}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.20.0_1722660534646_0.26069819629105817", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c93c388af6d33f082894b8a60839d7265b2b9bc5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-cfaupqd+UEFeURmqNP2eEvXqgbSox/LHOyN9/d2pSdV8xTrjdg3NgOFJCtc1vQ/jEke1qD0IejbBfxleBPHnPw==", "signatures": [{"sig": "MEUCIBqinHwZOQArdNikZfKKj/QxJrZHWLQluvSObVBwV+suAiEAzOTOTeHfpFXrle/Sdyn3w7n8uAvm/MSZeg4NfsiFAxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120775}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.21.0_1723960538275_0.30168345582998146", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a03b78775c129e8333aca9e1e420e8e217ee99b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-/6dYC9fZtfEY0vozpc5bx1RP4VrtEOhNQGb0HwvYNwXD1BBbwQ5cKIbUVVU7G2d5WRE90NfB922elN8ASXAJEA==", "signatures": [{"sig": "MEQCIDejuwl/yVOxDRSEJ8P4QC/mrwKX9/7/LOm6wLSrFs0BAiB2F8zDnI6Two/Ehnufs2Gl3sJJICJwgYujsPVak/1OxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.21.1_1724687657864_0.6000990207112351", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e62a4235f01e0f66dbba587c087ca6db8008ec80", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-48pD/fJkTiHAZTnZwR0VzHrao70/4MlzJrq0ZsILjLW/Ab/1XlVUStYyGt7tdyIiVSlGZbnliqmult/QGA2O2w==", "signatures": [{"sig": "MEUCIQC7FceOuoFn4rb2K3WZFDnUJWKAWC2+QzpBXQON/uMDvQIgdYaBR2fd6jKXrark5mK9ab6R6JruPYph+TWLqy+VAnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.21.2_1725001470016_0.24979383496262741", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "057ea26eaa7e537a06ded617d23d57eab3cecb58", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-MnvSPGO8KJXIMGlQDYfvYS3IosFN2rKsvxRpPO2l2cum+Z3exiExLwVU+GExL96pn8IP+GdH8Tz70EpBhO0sIQ==", "signatures": [{"sig": "MEUCIQCoHUQUrL4mrTWpBOXhPEIvXSNoCAjf4uKvUn+BZB8TlwIgHSoBLA7S+uUiinoCjb4fl5b0M/nlLEsK5yS8gicDXcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.21.3_1726124753863_0.37270467364138105", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ad209270c9937a27346fce5b0670cbdfb1e6a0a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-aQpNlKmx3amwkA3a5J6nlXSahE1ijl0L9KuIjVOUhfOh7uw2S4piR3mtpxpRtbnK809SBtyPsM9q15CPTsY7HQ==", "signatures": [{"sig": "MEUCIQCeyAWuGJ/HdD4KHFcmscfmLqYPb+o7b2ttMmANkG+5dgIgfGezxTF9T6hGnkvt+KyP4KM9W8iS5BGMyJW9WGIO140=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120775}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.0_1726721735559_0.7023748898781603", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4a6fb26503c1cba81388619ea20d87c4cd6473b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-+vZ1jrJeEEYLbMqeKDfgcl8v7zjymdAGTr7xUdQL6c4nC+S+BZHo3Mrp/9ij2qpAveC0Iaz9DIiFplcO0joapQ==", "signatures": [{"sig": "MEUCIC3juPiAPAxZ9T6yh2fpWv/6LCg3mTmZVz8lvWP71yT9AiEA4RlABcZLbnrtFDSKTTg/WTf63JY+xMD3Z+3tYu1uGhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.1_1726820517647_0.06857896684376152", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "490f49236102b97738d9406eaf5cd8d9dad35c15", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-66Zszr7i/JaQ0u/lefcfaAw16wh3oT72vSqubIMQqWzOg85bGCPhoeykG/cC5uvMzH80DQa2L539IqKht6twVA==", "signatures": [{"sig": "MEUCIHif1AbNZMEz9mToyHbfxLq7cuYJmawLW1bNOZ2PbbF9AiEA3XS4gZZhQ4Ccq64EcyGaShWlDBLP37eH9JoGwaOnAJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.2_1726824829423_0.15964752055765197", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1732654f983f2148878e2093338c6c3fcb1efb38", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-FlPQTvT1y5/uPIoJS5jDm/p2NI4OE2c7f2eTQ7ba0gXYzTRkJmowvxd0vgFqKF+6vUVAITzbN5PwXiE5Ai1WxA==", "signatures": [{"sig": "MEUCIQDZbPcPDUBc6r9c0t1XpyroaIb+BSuJGbnzjoee/6dkrQIgO+MAKwB98rT6zkvrTdto1VzChjYMoJ8v5sriybvKuAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116681}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.3-0_1726843682539_0.15570555155151333", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "128ff258dce871f659a6d262a39b619dc2c568a5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-yx7Xei61efYQ1JZMZ0WOA3vRwEHFU0LlzaEER2cVejr5EplBmOykBeHSeWnngeVNRASgiHS36tA8jlo6AJ9Shg==", "signatures": [{"sig": "MEUCIQC6vwgJ/Elwajcl62Cn0Tqs4a4u32uLzRGkeTJaiErM+gIgHNNGhQCkdA6I+jg5wT729IAqjvRJJ48fWLUM7IStpQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.3_1726894993486_0.4762280426838108", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9e655285c8175cd44f57d6a1e8e5dedfbba1d820", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-Gl0AxBtDg8uoAn5CCqQDMqAx22Wx22pjDOjBdmG0VIWX3qUBHzYmOKh8KXHL4UpogfJ14G4wk16EQogF+v8hmA==", "signatures": [{"sig": "MEUCIG6T00ewgP6DfsT/idEtvrlOmOPKwccOoS7/jzDP9THfAiEAl0eyNubDR709oL0nEefL/NaNV1XcZgxvWuzZY8QxxnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.4_1726899085975_0.06012244058132743", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6a37236189648e678bd564d6e8ca798f42cf42c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-qnOTIIs6tIGFKCHdhYitgC2XQ2X25InIbZFor5wh+mALH84qnFHvc+vmWUpyX97B0hNvwNUL4B+MB8vJvH65Fw==", "signatures": [{"sig": "MEQCIGsQlBq2zFHu1/jFxltBtgHfxzhZ4Eo33lqv8cl5UH3yAiAQHW2mXmi4faLdF4TCNq7PKmNE10mcP38GgVFFBSC8XA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116679}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.22.5_1727437700148_0.515496112674229", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a6cf6cdb340abde851b055e6d8785308ef4ace1a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-5QT/Di5FbGNPaVw8hHO1wETunwkPuZBIu6W+5GNArlKHD9fkMHy7vS8zGHJk38oObXfWdsuLMogD4sBySLJ54g==", "signatures": [{"sig": "MEUCIDjfSEjmXRRRbbj1QNsroyqhZb7+lNmekGFoRT5EOS0xAiEA7o8Kgdonva5uq8XnUskIyOzt34gSha8CskPpSKdCPRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2108559}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.23.0_1727766611072_0.7871050033406986", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8c03a996efb41e257b414b2e0560b7a21f2d9065", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-9E6MKUJhDuDh604Qco5yP/3qn3y7SLXYuiC0Rpr89aMScS2UAmK1wHP2b7KAa1nSjWJc/f/Lc0Wl1L47qjiyQw==", "signatures": [{"sig": "MEYCIQDLs8nd4skTQQnTKRP5YFM4onEd5RPLE7WxM8QEs0AFFAIhAJyiiLvhPSDap4awML2FbBPjsoUtU0xOKVCNfOpJrxOJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116751}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.24.0_1727861845066_0.07575524154900792", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "68453ad2b8a95dd2dd0d21521e4a449ccc6af51b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-zOLu7V1iBpJMIrrmZjpmAZ9txFlnGgqQMnjNmRrqmV1vQaou9SIT3qI3JE1kt+DQE8zCdB3n2/mAjIU90AfjEg==", "signatures": [{"sig": "MEQCIEjTBxlfSn6KgDkelwzw7tX7WJ4dwoPXgrN9MC9rsGfoAiAyu8aOPd3xTSH9ewV6iU3aOhUhjG4AXS9lHep19cff9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116751}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.24.1_1730011388216_0.05620430557046996", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9d8dc8e80df8f156d2888ecb8d6c96d653580731", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-TDdHLKCWgPuq9vQcmyLrhg/bgbOvIQ8rtWQK7MRxJ9nvaxKx38NvY7/Lo6cYuEnNHqf6rMqnivOIPIQt6H2AoA==", "signatures": [{"sig": "MEUCIQCEC0fjUt0v92Xa4jBhcZl2O1OdHjVSiAcvVjjwzwkjLwIgZtM31aSEHnZddJ9SM5oBgd7Jqr4KLwUvFJX0ORxj1x0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116751}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.24.2_1730043615791_0.849767815199997", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "45469794091395be351dbf4ac7d0459ed9780e1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-EnZzSDkWpYZmQnZiLXRfKwa46UTVC2LXiRPRk03rxyuV9TkfpBG6t+yrAUmT3BCVP+djFfcGhSDbsexBkSYeNg==", "signatures": [{"sig": "MEUCIAjlZBBmsyWcqXutbCsbbkCuij1PZyDmxKzvA2KFy7e2AiEA/r5iIMaVoH4A8/YGIvMOCc9iti9+HtmgjM7bPbPT5Rw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116753}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.25.0-0_1730182518524_0.12367872544527203", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9ffd7cd6c6c6670d8c039056d6a49ad9f1f66949", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-YlddZSUk8G0px9/+V9PVilVDC6ydMz7WquxozToozSnfFK6wa6ne1ATUjUvjin09jp34p84milxlY5ikueoenw==", "signatures": [{"sig": "MEUCIQDSlD3jMzf/Np4UG3iegGN/fZKkAtWl3H78yG0mSEqvJgIgBSai2JRvNitLDVG8Gt9SJEakY3Hc1luXH2etgzUjoSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116751}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.24.3_1730211255736_0.7987925747538289", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "63edd72b29c4cced93e16113a68e1be9fef88907", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-38yiWLemQf7aLHDgTg85fh3hW9stJ0Muk7+s6tIkSUOMmi4Xbv5pH/5Bofnsb6spIwD5FJiR+jg71f0CH5OzoA==", "signatures": [{"sig": "MEQCIB2KTV9TjfplBCr0a5ZCIacN+YfCvPmmraq2gFsebVoBAiAjpBtVR0aA/M7k2CJiGUviCiZvPMMgQCij4g5B0dcnWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2124943}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.24.4_1730710036563_0.024618575482236782", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4b36ffb8359f959f2c29afd187603c53368b6723", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-ndWTSEmAaKr88dBuogGH2NZaxe7u2rDoArsejNslugHZ+r44NfWiwjzizVS1nUOHo+n1Z6qV3X60rqE/HlISgw==", "signatures": [{"sig": "MEUCIF29YbgWzjmKBp9FSrDMChcTqAPGkmMiu0RZLRqq7yDsAiEAvWlCyqJYhSWQCwpxh1hB+JeYJWtREqodRCub+pClXN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116751}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.25.0_1731141449762_0.43420091511816405", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "70f8cacb255800e4cad41bdbe447432354288909", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-eGkX7zzkNxvvS05ROzJ/cO/AKqNvR/7t1jA3VZDi2vRniLKwAWxUr85fH3NsvtxU5vnUUKFHKh8flIBdlo2b3Q==", "signatures": [{"sig": "MEYCIQCdB5tb/s81xUhNwmfrrFS073iq/zakT2onlvbRKHrVlAIhAImhDnPM5rlvUNd8zIxhaXLY4p9Wb0hruOAGjvlwY7E+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116751}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.26.0_1731480307113_0.5860225489142874", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c2fd5314e23c6b58978a3cf345a47ce6630301cf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-aFRsbybgj+tVhXSgqjZAQp808ehxCbidgEuuht5yui35+cuN8zB/tg6q/p6rZec5aqYjpe9Ps1LJWj80UkMPDQ==", "signatures": [{"sig": "MEUCIBzp8v1edSFF3X23GsIQ4qzWY/wePgY9Qi9NUubsHy1PAiEA/5/m2UzBf6EFNLOV5N/Ca1vqm8HQP9vx3I1a54q6Wv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116753}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.0-0_1731481401148_0.7800731834910928", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3b2e37986d5f5fd22f583ce538a386919db9901f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-5Y9QtkfvXMk73PehV4LIrFFJm7MRNtoz106XkMnHD3GvgCnaCLdfP/elL2jg2e9e+EtkXpV55sMhVwemhS6n4A==", "signatures": [{"sig": "MEUCIGZXZXHpuzDzfuCyJa/cfLrHtbx9dG9HhRrnOSwXNui6AiEApW0VkKAQNi1QKm2X4NWYjBgLfytCcCiwqHFGHt08ihY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116753}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.0-1_1731565998853_0.07406025779860514", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f56cef574165ad1bf61b21dc4cae8f80762e8a64", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-6St9rrPSLbYBbbJAClpU4gmnO7cdZCMMzx2MT0UCIIIevoLAmsCDOAG6t3J/RgN4CPUpdaGr/UnPqQTHZ4oDwA==", "signatures": [{"sig": "MEUCICAdkJxwML2b74/Oe+cYm532FU/QHiuldsi7clMkNcWjAiEA/Sk4R4M813IZSQC8Nh9YDHBN0CW/SNfaeszAffzozW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2133135}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.0_1731667243228_0.5354103826059544", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "85a0e61b9917cffa5fca561cce88f634dcf008d7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-Dhb7Mk9XObFbQv/tZQZfZ38C8GhrjwIs/rIHQrxKWaetd3Kf3NQPA3e+JOK24pS3CNBJ2+IgOH8VrkYBmsaYag==", "signatures": [{"sig": "MEUCIGY5zzSVhWi2GIxe20+AIABdgqqC0y17dsADsC2XuFsqAiEA97dm2ZVKYTkNemD/0NyU1OYLCQtV4VniSc/zh4UvDJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2133137}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.1-0_1731677295758_0.5467398668144414", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8c1ee647a3bcbbbf362b98e2c1291545be4170ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-vC7nC2wIsQBVlEQzoUds8tUzNggXhC+eUP4bVUDBgOC4o56RW5OVbi0wfcTB0oQgxBdB7ig/o+DQvbFqauSHxA==", "signatures": [{"sig": "MEQCIFu5e6Hgbe6HzhsgWZBCIVvFcrXwK2+4pvkpY4YduHJrAiBH4L371e4vPnb7D0sbQcA7w8nH/y0xMQwZGS5dfnXjmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2133137}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.1-1_1731685091128_0.9685510172160261", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "06ca1f2588a42e5d514ff37deabcfed145865cdc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-1d5UxhZlVFnyF5rFWNXMrr/MHkBU32xfmFI/KPosuKhvUS7ge2T3Z3R5r3PlB/tv9fMETcvr761G35r08MK3sQ==", "signatures": [{"sig": "MEYCIQDw/4RwzKtuIZKX6s/941jwfufqA1OlXwofOnmGYKi7RwIhAILjPIRPUFmIrMM+GHm2eSixoNwEkmjscFfCndAgSKBl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2133135}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.1_1731686868983_0.6664681172449927", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fd555c67a9a99927ef5e904ea4b3143915d1e44f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-/PU9P+7Rkz8JFYDHIi+xzHabOu9qEWR07L5nWLIUsvserrxegZExKCi2jhMZRd0ATdboKylu/K5yAXbp7fYFvA==", "signatures": [{"sig": "MEUCIF+PVWxT2M5/Qo9lYfXmhcm95lUQEC+4zocNzHhOWhdyAiEA0+DkgJ5v66zOE/eAIjH/AoXjp64nm344puBX+oYhiB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2133135}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.2_1731691211710_0.5916520996265786", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8a3f05dbae753102ae10a9bc2168c7b6bbeea5da", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-TKxiOvBorYq4sUpA0JT+Fkh+l+G9DScnG5Dqx7wiiqVMiRSkzTclP35pE6eQQYjP4Gc8yEkJGea6rz4qyWhp3g==", "signatures": [{"sig": "MEUCIEafF6ditsd656UPV5lo8SJbLRAYRfJfiIk849H2Fe9qAiEA3vUqqZ73coioi2nsd+W1Uv9RMuMy6Nflm8yjXjq1+Bk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2133135}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.3_1731947983965_0.9950772009386624", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6739f7eb33e20466bb88748519c98ce8dee23922", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-caluiUXvUuVyCHr5DxL8ohaaFFzPGmgmMvwmqAITMpV/Q+tPoaHZ/PWa3t8B2WyoRcIIuu1hkaW5KkeTDNSnMA==", "signatures": [{"sig": "MEYCIQDIZAf52oLbrJw7D6AZWVa/1gecWXyoegFF8f/zmAyGegIhAJSCOGFJWopsQkK8VWNEKKckf5rk2ZyRW8bb/zn6snBr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.27.4_1732345227928_0.709835751059269", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "730af6ddc091a5ba5baac28a3510691725dc808b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-1xsm2rCKSTpKzi5/ypT5wfc+4bOGa/9yI/eaOLW0oMs7qpC542APWhl4A37AENGZ6St6GBMWhCCMM6tXgTIplw==", "signatures": [{"sig": "MEUCIBp1TAqsQumCcY0f5AF427PNRH/UTRBi/X5vEQxSGnC5AiEAsyE8xLqMusXrKMgukdRowFpWNLVQzLtK8I7OufNAtRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.28.0_1732972555441_0.5604986278372515", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "865baf5b6f5ff67acb32e5a359508828e8dc5788", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-QF54q8MYGAqMLrX2t7tNpi01nvq5RI59UBNx+3+37zoKX5KViPo/gk2QLhsuqok05sSCRluj0D00LzCwBikb0A==", "signatures": [{"sig": "MEQCIAyVbAR3AoYJ9a5o91kXiDWOsTNVE8/K83esZbDOwhVWAiBh0LYkB2tVPTGcYoU9kxl3UFrkAja+uEx9vlCKvac7zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116759}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.28.1_1733485504297_0.314026172223836", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b9a15433c049eac095962a225cc560167c4cd516", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-qDoDWCiXQMAI3T1Em390bEUkUY0deMhdOdj477N1tQky0kgp72AQzIFwVcD7S2Cy3/er0+lXscjCwQbZSeTojQ==", "signatures": [{"sig": "MEQCIDR8cuNDpJR9NbX4IZHHHG6qe1Fx4JXfx7Y89USQIaRTAiBdT0HCNO16lcRyCXYqv2hgDWwpe5mJKTHm3V9PoP+lpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112665}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.29.0-0_1734331201967_0.07498587947681323", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2c64767def7cd2f286d501c82b8e90e5164100ed", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-8190Iq2w9QT7h5SFSKgyPsLDMwkdzDpOy45mmpvVxBeb5ZsHKrDgiNl1h5UFba5lqyhRoYzrsUO0Ce8uchp0qg==", "signatures": [{"sig": "MEUCIEZTHTiqkvtvShdJ6mdpE/kcFZ9FIAGUw4d5a1zCFfBBAiEAr9r2EYczT7r1v+yFTVVLR/3HUs26AS6eVx7X21zTm1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112665}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.29.0-1_1734590258545_0.44228411968203485", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c0ff1688d1207e524d20176ce5911a31a8f50029", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-/VozRn5ve4+7ODwXhndsGi9HOUxNsQFnKIuVmWZ78bxRgKTtilIHUh1I6Rnh6Kpdo2/50tatrOsqHjGmh+HImw==", "signatures": [{"sig": "MEUCIDYjX5XGyy1s08iwXVJD67ZASTzMOERqwxwTnO4IIiPiAiEA7Iz9c3snuB+3hQsYHPdszV86hIDG+HYHVwZct3TPI9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112665}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.29.0-2_1734677771058_0.30861061014024527", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9b64d8b8678fd1c409ddc22035719fc51376ac3d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-AP+DLj4q9FT22ZL43ssA3gizEn7/MfJcZ1BOuyEPqoriuH3a8VRuDddN0MtpUwEtiZL6jc1GY5/eL99hkloQ1Q==", "signatures": [{"sig": "MEUCIB5pCINSMVWFgFRDQ+Yeyx4f5Or6s5sTLYvQp69mwUnaAiEA1pNBShCYWDOMqEXIeXaNNOe+8TBBiiVMflQFR7XDdRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112663}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.29.0_1734719852611_0.2736960205431638", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1d8f68f0829b57f746ec03432ad046f1af014a98", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-fOHRtF9gahwJk3QVp01a/GqS4hBEZCV1oKglVVq13kcK3NeVlS4BwIFzOHDbmKzt3i0OuHG4zfRP0YoG5OF/rA==", "signatures": [{"sig": "MEQCICpehBwWXziHfRVyM9Q4Dpd9raqajEG39Bu5CWz4LaaPAiAl9GHmRy92PplMhoZ5LxgnaKbsk7VDeUjhR8gx6d/hTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112663}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.29.1_1734765370419_0.3450001247323091", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "90341fb0dbd37685c1bae0ddc84e36c9b4b7bbec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-ptlkdYD+pUqu991ZLF5f62nkZ5fDRuVTyv2LV1X+na+3yhJLW227UVWxDn+4LV7J3UWHe8bNYCde3b3b9yxjWQ==", "signatures": [{"sig": "MEUCIGC4uJV0zJFSKRIT4TBiVZ9XO2D74iEcUzkdTlf9booaAiEA7SPcOYxMBbSCmlOm7xhiDhd+8aUK3BqH/Z6wSngWTUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112665}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.30.0-0_1734765442609_0.13073497450739824", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1f6e428864d718442735d493b0bf93aa95180d81", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-lDwOVmb18qfSW70UBYeVNdTZaDgHk6+eLEQpUFHeUQBcWqz0WHpH9trUQe/7JP6VP7R+dTlW2hnU6QHi7X9Ncg==", "signatures": [{"sig": "MEUCICp8mjcEJL1L4aTodEnooSX6yDOYK9WYlGmleuAg7R3WAiEAqt1LvFQqTaxEEYY2MbUhCheK5gkyXx1rS/Lx+NW3B0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116761}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.30.0-1_1735541544793_0.7422373008829652", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "17deb5058243bc5599164a9e0a899b0902310fca", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-TF4kxkPq+SudS/r4zGPf0G08Bl7+NZcFrUSR3484WwsHgGgJyPQRLCNrQ/R5J6VzxfEeQR9XRpc8m2t7lD6SEQ==", "signatures": [{"sig": "MEYCIQC0tcKNHSZ50fuLAWnjXL8G4DCyEuKTQczKj82eRnJO7wIhANKZOOZ8TrYWxcAvGAwiFn3ru19i2Ym+PBsO731Db1m0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116759}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.29.2_1736078870608_0.10239166959287394", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "63eadee20f220d28e85cbd10aba671ada8e89c84", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-MctNTBlvMcIBP0t8lV/NXiUwFg9oK5F79CxLU+a3xgrdJjfBLVIEHSAjQ9+ipofN2GKaMLnFFXLltg1HEEPaGQ==", "signatures": [{"sig": "MEUCIBdm7zCwYNwrFtJcdLMvDy3wyIGOCItclVIfEghEXbcvAiEAqyx4tGUL0OUtmA4GDS+PR/PB3V/m8s/f8gWaKoyecJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116759}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.30.0_1736145408211_0.7559209793605106", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9d06b26d286c7dded6336961a2f83e48330e0c80", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-i4Ab2vnvS1AE1PyOIGp2kXni69gU2DAUVt6FSXeIqUCPIR3ZlheMW3oP2JkukDfu3PsexYRbOiJrY+yVNSk9oA==", "signatures": [{"sig": "MEUCIQD3FBGswK+y9vpmt6UEKreCq34w8UayHcrHjPTZrkUomgIgfpExmmQGdoUFGjqw3NyCPFzhCgLsGOYH88wxkzd7qQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116759}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.30.1_1736246160615_0.5260619795695713", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e4a49e2944fb23bfe744c36854798060d0cf199d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-AKePyML22xoJXLjj0zShBXjbrs5HsUOckNOIxYPocF6GKsiuQ224MZtg9LpAM8cNY4WxGmMr/3KawLMGTPEmnQ==", "signatures": [{"sig": "MEUCIQCc7/0lfIxmL+kPEe9JRnQsL1xqFFfmOJ8PaQUmpIdH5wIgbjQuflk1oXtN/wAfPLYEJXjssHeMby03PLcycfsxOio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2116761}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.31.0-0_1736834269953_0.941519768560658", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b3987a96c18b7287129cf735be2dbf83e94d9d05", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-kpQXQ0UPFeMPmPYksiBL9WS/BDiQEjRGMfklVIsA0Sng347H8W2iexch+IEwaR7OVSKtr2ZFxggt11zVIlZ25g==", "signatures": [{"sig": "MEUCIQCS9/ZnaLitJcRwcPyGZTLHCY7zbK1ACop0Qwc3FgI3FAIgNktmAxwLn3di7IOEhUV7mNjfOCjRG5wHON3tYiUbgns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2141335}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.31.0_1737291415163_0.9974908932843458", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "7aa23b45bf489b7204b5a542e857e134742141de", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-HJbifC9vex9NqnlodV2BHVFNuzKL5OnsV2dvTw6e1dpZKkNjPG6WUq+nhEYV6Hv2Bv++BXkwcyoGlXnPrjAKXw==", "signatures": [{"sig": "MEUCIQCQVLRDywHA33l7fYx+syv0ZnsF2AoazBrH9w2+YGXQDgIgP/8EryO2UwIWwwUru7Ya0hIW25g/dE5cw3E4HsTDgm8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2141335}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.32.0_1737707262400_0.08053469979596728", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0012f6d288a880f4cda4e3593b4330a6eaf81d9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-6fJTlz9YMlag4kwKBsxc1Yv8tGxNq/N5z/S6syLCFbABSbfHJ/OjItE1+YbL2AHKRVIOsqell6lvC/wO5CWlEQ==", "signatures": [{"sig": "MEYCIQCkzmQOAFjB+NWdvBP7pMqg2bkxtGtcsWze9aRCExjkkAIhALMl3dai7G7W1EhmyzTHTfscuIzIPIP6OLbowZRZSsum", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2141337}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.33.0-0_1738053016741_0.5456812770039852", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "31b4e0a543607e6eb4f982ffb45830919a952a83", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-FTYc2YoTWUsBz5GTTgGkRYYJ5NGJIi/rCY4oK/I8aKowx1ToXeoVVbIE4LGAjsauvlhjfl0MYacxClLld1VrOw==", "signatures": [{"sig": "MEUCIEKHLcuiMYQjNGN6oL+H8tmfuTOuLYjT/l1MQHJK2F+PAiEAz6YBM2+Qlf1Tj/kI8d2DAmre+zvVHpKEA2SAb+KWLxc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2141335}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.32.1_1738053204831_0.5700796383068416", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c63efc82438214b18e98fac725560a73783da314", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-BgJABG3iaIeiJKDcPhj4x7XRSke4SkcrFxYKnciy/V/auEPbKu4uBhD0SUhIx+0EI/uXtelPqA6VW0GxfDHRYA==", "signatures": [{"sig": "MEQCIF2g8etNIMbejnrQ1JaX3eP+zUFYh9lr4jV+2iadZr//AiAxBOSyojnLlEje5QTMwU3M3K+VsNSFpNjmG0YB10PBOg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2122887}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.33.0_1738393928151_0.8017552702075335", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "eb02c76935ca72332eaf24bef6f9ed0e43cdd9fe", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-SXYJw3zpwHgaBqTXeAZ31qfW/v50wq4HhNVvKFhRr5MnptRX2Af4KebLWR1wpxGJtLgfS2hEPuALRIY3LPAAcA==", "signatures": [{"sig": "MEUCIA7gKer9qoZt0leaHjtJKuc9uJ3QMHksTxJ4A0cn9WBDAiEA7aA+nCqqPT2nClNhk6bEs5iY6vnCPHD9VyG51Z5xKrE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2122887}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.0_1738399231707_0.46430531423942645", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "314f56ea347323d94107b8eb0dc6cb60ba4e0a35", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-CQ3MAGgiFmQW5XJX5W3wnxOBxKwFlUAgSXFA2SwgVRjrIiVt5LHfcQLeNSHKq5OEZwv+VCBwlD1+YKCjDG8cpg==", "signatures": [{"sig": "MEYCIQDzdEUWcY3Mi/m6dP7L5zvSasrxBi/OuF9gICvBQ+cq1gIhAP0DPJ/abL1KQBqr+Lrssp+HUr6w9Sk+qjxCcoylbaz1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2122887}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.1_1738565901136_0.7439950063967946", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ea2ae28fa8cbb2af0f1bc2e45c8853a6a340f72d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-UlFk+E46TZEoxD9ufLKDBzfSG7Ki03fo6hsNRRRHF+KuvNZ5vd1RRVQm8YZlGsjcJG8R252XFK0xNPay+4WV7w==", "signatures": [{"sig": "MEUCIQDgL3CLeIlFQInMQSwr2JC/iUcUieq9CZ8kT8Q7A1ikEQIgN4SJqTocMAGHu5EVyEgxeDUOjZOCyYOdmQwAWQ+HptM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2122887}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.2_1738656610320_0.1631533316429794", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "97b231d2ca6fdeaa8d0e02de2f1f3896bedf14a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-4XQhG8v/t3S7Rxs7rmFUuM6j09hVrTArzONS3fUZ6oBRSN/ps9IPQjVhp62P0W3KhqJdQADo/MRlYRMdgxr/3w==", "signatures": [{"sig": "MEQCICtKcUTEqQYei90+AtmiFG/2wF2zneoQxHX3vQFhb2Z5AiA4ECG74aH0sDjYpl8dqBaZN5ULQdC64FGG5Lt0CjJ1Eg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2122887}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.3_1738747333378_0.0036858429222139666", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "05da429ff2975c2ba6b766354bd6588371eddf2a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-GZdafB41/4s12j8Ss2izofjeFXRAAM7sHCb+S4JsI9vaONX/zQ8cXd87B9MRU/igGAJkKvmFmJJBeeT9jJ5Cbw==", "signatures": [{"sig": "MEQCIHv7W/YUXrrfi8AMyLuuS/LwZs7+FanLMWWvxQuAZ6HwAiAbYTyJXVaLNVT3EviYMEwQ2Bgz603N3jHLps6fTiO41w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2122887}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.4_1738791079306_0.3773873099568832", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "39a15d9e3baebbb10af6cc1cf953cbec23987774", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-uOb6hzDqym4Sw+qw3+svS3SmwQGVUhyTdPKyHDdlYg1Z0aHjdNmjwRY7zw/90/UfBe/yD7Mv2mYKhQpOfy4RYA==", "signatures": [{"sig": "MEUCIQC+DFplDYmA71Z6wxdJkASS1Ypv7VRVbD5tJWgiLgnPngIgbd0os+iEwUnMWagJEbfD6UGScNyCHqYxySUa1yq+O98=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2118791}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.5_1738918390221_0.7390187375868056", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9658221b59d9e5643348f9a52fa5ef35b4dc07b1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-qTmklhCTyaJSB05S+iSovfo++EwnIEZxHkzv5dep4qoszUMX5Ca4WM4zAVUMbfdviLgCSQOu5oU8YoGk1s6M9Q==", "signatures": [{"sig": "MEYCIQDpNF0O+S4kI1qejLCL1uZ/ME6q9koPY2rfuOJexKVY5AIhAPwPSiqIppJxNu4Yg+cD+uiham/oMX9vNlgTgJCiURF5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2106487}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.6_1738945935314_0.3609801769141412", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a58dff44a18696df65ed8c0ad68a2945cf900484", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-uFLJFz6+utmpbR313TTx+NpPuAXbPz4BhTQzgaP0tozlLnGnQ6rCo6tLwaSa6b7l6gRErjLicXQ1iPiXzYotjw==", "signatures": [{"sig": "MEYCIQDnCGUHS8yZMs0vrq3ceJyLngoXnX/fBIElOGwOiOjWxwIhAOnQElG5X7dSshtPTUHGmurBCVxmSw+nCIVNYXZuyAa/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2110583}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.7_1739526848186_0.0665695431609139", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f50ecccf8c78841ff6df1706bc4782d7f62bf9c3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-KdSfaROOUJXgTVxJNAZ3KwkRc5nggDk+06P6lgi1HLv1hskgvxHUKZ4xtwHkVYJ1Rep4GNo+uEfycCRRxht7+Q==", "signatures": [{"sig": "MEUCICgJ5A5UWdZR/yGLxuyhcgtDvWCQ1vye1NKhxmUOL1gxAiEAgDhqmtL5bvg9uOjCXVgXW63SBShoGAZbl24SlVTkwYk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2110583}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.8_1739773593316_0.26954127819383844", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8f895eb5577748fc75af21beae32439626e0a14c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-LD2fytxZJZ6xzOKnMbIpgzFOuIKlxVOpiMAXawsAZ2mHBPEYOnLRK5TTEsID6z4eM23DuO88X0Tq1mErHMVq0A==", "signatures": [{"sig": "MEYCIQCcK0IaHSzeiMDrIdAW2+Rtq/0mK4LXyuqJpkbLxrL+fgIhAJc4nGJkjZaUlQbK9LkdqvpfVveDv0sNG+UirHB007x2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2167927}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.34.9_1740814364478_0.2320603503311207", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2fc70a446d986e27f6101ea74e81746987f69150", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-XQxVOCd6VJeHQA/7YcqyV0/88N6ysSVzRjJ9I9UA/xXpEsjvAgDTgH3wQYz5bmr7SPtVK2TsP2fQ2N9L4ukoUg==", "signatures": [{"sig": "MEUCIDgh2LExcAVrQsRDtv/G6vqCs/PEOrq2X+ORh0RhX6aKAiEAt+bWiqGHAn0fPKJe9CY47EU7XN9ngIJFL20es43YS8w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2172023}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.35.0_1741415093496_0.25951349386528877", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d742917d61880941be26ff8d3352d935139188b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-QiGnhScND+mAAtfHqeT+cB1S9yFnNQ/EwCg5yE3MzoaZZnIV0RV9O5alJAoJKX/sBONVKeZdMfO8QSaWEygMhw==", "signatures": [{"sig": "MEUCIAybqfZeAg/O2cMERE3CUhYbcq0/onk4GeuYArQ/cnxTAiEA8dZNT6ZP8h3+SxGbn+0Z9H5Y6wZwRUzQOh/2o281JGE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2176119}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.36.0_1742200551789_0.31212424638166314", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b5222180bb1a50e6e9bc8263efd771c1ce770b6f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-pfxLBMls+28Ey2enpX3JvjEjaJMBX5XlPCZNGxj4kdJyHduPBXtxYeb8alo0a7bqOoWZW2uKynhHxF/MWoHaGQ==", "signatures": [{"sig": "MEYCIQCSnXeqPeMI6F84tkFsmWTsZhwsp/fReGSDl5iFRcK40QIhAPdXtSrlVcQyPmqwXTRfrE5GgpFANMaREg3rV+5FSKHs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2180215}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.37.0_1742741834645_0.9609098126238851", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "00e3b646a7976752052ebc72d005808b9e7f2801", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-fQgqwKmW0REM4LomQ+87PP8w8xvU9LZfeLBKybeli+0yHT7VKILINzFEuggvnV9M3x1Ed4gUBmGUzCo/ikmFbQ==", "signatures": [{"sig": "MEUCIQCPwlqjUQYmmz0AvsKkR2xD1tNL0KwmHc6pTG7IIHgOUwIgZQjTwr/HXjt28j50qscN9kpsD3x9QDJdo7PrZRKAums=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2180215}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.38.0_1743229755109_0.1376292173852438", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "531c92533ce3d167f2111bfcd2aa1a2041266987", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-vYanR6MtqC7Z2SNr8gzVnzUul09Wi1kZqJaek3KcIlI/wq5Xtq4ZPIZ0Mr/st/sv/NnaPwy/D4yXg5x0B3aUUA==", "signatures": [{"sig": "MEUCIQCPOsDIjIy9JhvcSf+EsTHnNhs6yQMYlvJiDN/z0v86/wIgPUGJFXm1YjRCxgKQOOgRhCo5+CQcYft43FjLdsyoSRo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2180215}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.39.0_1743569379879_0.4913806414748516", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "74d2b5cb11cf714cd7d1682e7c8b39140e908552", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-ATZvCRGCDtv1Y4gpDIXsS+wfFeFuLwVxyUBSLawjgXK2tRE6fnsQEkE4csQQYWlBlsFztRzCnBvWVfcae/1qxQ==", "signatures": [{"sig": "MEUCIBTb3gg5RU75KYJjxRSp01okkcV/3Es97Oc+MdDeXxQpAiEAiguaK4aWBW9nTHJpp6JU8Dm4VW3SoLxSFydgW8nIMBU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2197919}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.40.0_1744447183584_0.0792441751955919", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "22ebeaf2fa301aa4aa6c84b760e6cd1d1ac7eb1e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-jEwjn3jCA+tQGswK3aEWcD09/7M5wGwc6+flhva7dsQNRZZTe30vkalgIzV4tjkopsTS9Jd7Y1Bsj6a4lzz8gQ==", "signatures": [{"sig": "MEQCIA61lvYULAYrK5fQ14KgaBp3z50gxLgYnDJHUKbx9xPIAiAT/xI6HzAyceokZiWA0LEYg+KCR0RzzQEBpG893b21oQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2218423}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.40.1_1745814931766_0.5696823662624044", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2054216e34469ab8765588ebf343d531fc3c9228", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-j8CgxvfM0kbnhu4XgjnCWJQyyBOeBI1Zq91Z850aUddUmPeQvuAy6OiMdPS46gNFgy8gN1xkYyLgwLYZG3rBOg==", "signatures": [{"sig": "MEQCIF7N6ymGG+aFWcrFsYP+wzhRmwT+kz+CBCkU+ZRWcMSRAiB4JeQ23VV1TIOh6baZC10kBQQhWIwpbm5gDzTH65Sl8Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2226615}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.40.2_1746516421113_0.7097375563850785", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a6b71b1e8fa33bac9f65b6f879e8ed878035d120", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-l+QK99je2zUKGd31Gh+45c4pGDAqZSuWQiuRFCdHYC2CSiO47qUWsCcenrI6p22hvHZrDje9QjwSMAFL3iwXwQ==", "signatures": [{"sig": "MEUCIQDw92ATI+0ArseIgv9qfjXmtoRq5lK+44H8BQEiqmc+zAIgRyG54g4XniBGjhFMzVC4bwOpslBuZvokIJDvs0cToyo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2230711}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.41.0_1747546419338_0.33691392187970837", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "63a1f1b0671cb17822dabae827fef0e443aebeb7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-XZpeGB5TKEZWzIrj7sXr+BEaSgo/ma/kCgrZgL0oo5qdB1JlTzIYQKel/RmhT6vMAvOdM2teYlAaOGJpJ9lahg==", "signatures": [{"sig": "MEYCIQDQ2rdNplGp6FD+jA7K26M1wmtD+i1nnp3Qq125KMa3wgIhAIvVW0jaVBunGomlhOYVTJgEQIvAWhahCItpE9Gruss3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2316943}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.41.1_1748067281107_0.7558197542127076", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a032bf8356f203446f6df80f2d516bde158dd5f9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-FxPrm21SE2/p+L/Qy2P6zPPWtbKRunZ71ChHXuZu6b//zOeDYCF7kbXiGdE7ZJgUvboLx8lQtov4jsz04zdmiw==", "signatures": [{"sig": "MEYCIQC4U9lk85Y1z6830nZwfze97xaeldiVPj+qvGC6lKIuBAIhANkrROuOZnGqObnc7nzJpGHJFSaKX+JoHQjce7sVS0qA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2304655}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.41.2_1749210040531_0.6709100695699683", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6db81ab065ef278faf83d875c77ff9cdd51abcfd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-/3NrcOWFSR7RQUQIuZQChLND36aTU9IYE4j+TB40VU78S+RA0IiqHR30oSh6P1S9f9/wVOenHQnacs/Byb824g==", "signatures": [{"sig": "MEUCIQDewOPjamsgspRixmOclVtjJyNoSdJufTVCqor8b3kicwIgcHQDFefmFKx7F+YZYbSYJGPkvMHxg87wvD8BR/PSFO4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2304655}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.42.0_1749221300718_0.6824505976439506", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "34873a437bcd87618f702dc66f0cbce170aebf9f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-KPPyAdlcIZ6S9C3S2cndXDkV0Bb1OSMsX0Eelr2Bay4EsF9yi9u9uzc9RniK3mcUGCLhWY9oLr6er80P5DE6XA==", "signatures": [{"sig": "MEUCIQDneveVe/uwBwViaMgvBtQPGwKRr2qAa5kosy6q0+pKTAIgIDrU6lOucx5XuCePr9/BJRxBtBaYAvz9UWOKrilEpYo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2304655}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.43.0_1749619367458_0.816073798145817", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "72efc633aa0b93531bdfc69d70bcafa88e6152fc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-GFWfAhVhWGd4r6UxmnKRTBwP1qmModHtd5gkraeW2G490BpFOZkFtem8yuX2NyafIP/mGpRJgTJ2PwohQkUY/Q==", "signatures": [{"sig": "MEQCIERZQzYJiDRMocYAV0R/bO28vE4AIlAmEa73dl1MZOFzAiBGFX1eNGkI0T9inaP4HxlmYQtdo43vYT3qp8Wfh+1jxg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2321039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.44.0_1750314186220_0.07833859802772958", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-musl@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4043398049fe4449c1485312d1ae9ad8af4056dd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-W+GBM4ifET1Plw8pdVaecwUgxmiH23CfAUj32u8knq0JPFyK4weRy6H7ooxYFD19YxBulL0Ktsflg5XS7+7u9g==", "signatures": [{"sig": "MEYCIQC+RS0Wxvf7R3BLm71UdjmEQvOZQnucpd03RJENrh3F6QIhAOCkabJCB6csYrosPdL+a/W4oCJm2zy9KvtIm8JGPlqL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2321039}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-musl_4.44.1_1750912463944_0.09849298244891913", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-arm64-musl", "version": "4.44.2", "os": ["linux"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["musl"], "main": "./rollup.linux-arm64-musl.node", "_id": "@rollup/rollup-linux-arm64-musl@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lb5bxXnxXglVq+7imxykIp5xMq+idehfl+wOgiiix0191av84OqbjUED+PRC5OA8eFJYj5xAGcpAZ0pF2MnW+A==", "shasum": "0214efc3e404ddf108e946ad5f7e4ee2792a155a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2321039, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBMrtROzHBhZ/2uPNxnqaajpu3FNx9XR0B1ZJt2Bs/8sAiBNkyEeyoAx6XaFWchy5EP/TuVybQh5CjFBFirVWxBhCw=="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-arm64-musl_4.44.2_1751633776140_0.029043214511348614"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-10-06T12:36:46.042Z", "modified": "2025-07-04T12:56:16.622Z", "4.0.1": "2023-10-06T12:36:46.470Z", "4.0.2": "2023-10-06T14:18:46.166Z", "4.1.0": "2023-10-14T05:52:18.271Z", "4.1.1": "2023-10-15T06:31:52.263Z", "4.1.3": "2023-10-15T17:48:30.906Z", "4.1.4": "2023-10-16T04:34:13.649Z", "4.1.5": "2023-10-28T09:23:36.293Z", "4.1.6": "2023-10-31T05:45:19.022Z", "4.2.0": "2023-10-31T08:10:44.815Z", "4.3.0": "2023-11-03T20:13:07.018Z", "4.3.1": "2023-11-11T07:57:58.154Z", "4.4.0": "2023-11-12T07:49:57.353Z", "4.4.1": "2023-11-14T05:25:19.928Z", "4.5.0": "2023-11-18T05:52:15.760Z", "4.5.1": "2023-11-21T20:13:13.110Z", "4.5.2": "2023-11-24T06:29:50.869Z", "4.6.0": "2023-11-26T13:39:17.008Z", "4.6.1": "2023-11-30T05:23:12.428Z", "4.7.0": "2023-12-08T07:58:04.340Z", "4.8.0": "2023-12-11T06:24:56.841Z", "4.9.0": "2023-12-13T09:24:20.156Z", "4.9.1": "2023-12-17T06:26:14.695Z", "4.9.2": "2023-12-30T06:23:30.954Z", "4.9.3": "2024-01-05T06:20:50.192Z", "4.9.4": "2024-01-06T06:39:03.481Z", "4.9.5": "2024-01-12T06:16:16.938Z", "4.9.6": "2024-01-21T05:52:22.304Z", "4.10.0": "2024-02-10T05:58:44.663Z", "4.11.0": "2024-02-15T06:09:43.370Z", "4.12.0": "2024-02-16T13:32:20.620Z", "4.12.1": "2024-03-06T06:03:38.723Z", "4.13.0": "2024-03-12T05:28:40.195Z", "4.13.1-1": "2024-03-24T07:39:25.674Z", "4.13.1": "2024-03-27T10:27:44.874Z", "4.13.2": "2024-03-28T14:13:38.896Z", "4.14.0": "2024-04-03T05:22:57.497Z", "4.14.1": "2024-04-07T07:35:43.005Z", "4.14.2": "2024-04-12T06:23:43.250Z", "4.14.3": "2024-04-15T07:18:34.972Z", "4.15.0": "2024-04-20T05:37:17.437Z", "4.16.0": "2024-04-21T04:42:00.363Z", "4.16.1": "2024-04-21T18:30:03.014Z", "4.16.2": "2024-04-22T15:19:20.001Z", "4.16.3": "2024-04-23T05:12:37.819Z", "4.16.4": "2024-04-23T13:15:10.783Z", "4.17.0": "2024-04-27T11:29:55.125Z", "4.17.1": "2024-04-29T04:57:58.053Z", "4.17.2": "2024-04-30T05:00:50.014Z", "4.18.0": "2024-05-22T05:03:48.455Z", "4.18.1": "2024-07-08T15:25:16.026Z", "4.19.0": "2024-07-20T05:46:18.611Z", "4.19.1": "2024-07-27T04:54:05.353Z", "4.19.2": "2024-08-01T08:32:57.774Z", "4.20.0": "2024-08-03T04:48:54.884Z", "4.21.0": "2024-08-18T05:55:38.509Z", "4.21.1": "2024-08-26T15:54:18.013Z", "4.21.2": "2024-08-30T07:04:30.259Z", "4.21.3": "2024-09-12T07:05:54.070Z", "4.22.0": "2024-09-19T04:55:35.787Z", "4.22.1": "2024-09-20T08:21:57.920Z", "4.22.2": "2024-09-20T09:33:49.642Z", "4.22.3-0": "2024-09-20T14:48:02.903Z", "4.22.3": "2024-09-21T05:03:13.759Z", "4.22.4": "2024-09-21T06:11:26.274Z", "4.22.5": "2024-09-27T11:48:20.366Z", "4.23.0": "2024-10-01T07:10:11.299Z", "4.24.0": "2024-10-02T09:37:25.275Z", "4.24.1": "2024-10-27T06:43:08.547Z", "4.24.2": "2024-10-27T15:40:16.086Z", "4.25.0-0": "2024-10-29T06:15:18.905Z", "4.24.3": "2024-10-29T14:14:16.058Z", "4.24.4": "2024-11-04T08:47:16.868Z", "4.25.0": "2024-11-09T08:37:29.974Z", "4.26.0": "2024-11-13T06:45:07.342Z", "4.27.0-0": "2024-11-13T07:03:21.405Z", "4.27.0-1": "2024-11-14T06:33:19.010Z", "4.27.0": "2024-11-15T10:40:43.521Z", "4.27.1-0": "2024-11-15T13:28:16.105Z", "4.27.1-1": "2024-11-15T15:38:11.400Z", "4.27.1": "2024-11-15T16:07:49.363Z", "4.27.2": "2024-11-15T17:20:11.958Z", "4.27.3": "2024-11-18T16:39:44.254Z", "4.27.4": "2024-11-23T07:00:28.149Z", "4.28.0": "2024-11-30T13:15:55.677Z", "4.28.1": "2024-12-06T11:45:04.515Z", "4.29.0-0": "2024-12-16T06:40:02.226Z", "4.29.0-1": "2024-12-19T06:37:38.820Z", "4.29.0-2": "2024-12-20T06:56:11.270Z", "4.29.0": "2024-12-20T18:37:32.875Z", "4.29.1": "2024-12-21T07:16:10.600Z", "4.30.0-0": "2024-12-21T07:17:22.875Z", "4.30.0-1": "2024-12-30T06:52:25.010Z", "4.29.2": "2025-01-05T12:07:50.858Z", "4.30.0": "2025-01-06T06:36:48.416Z", "4.30.1": "2025-01-07T10:36:00.900Z", "4.31.0-0": "2025-01-14T05:57:50.164Z", "4.31.0": "2025-01-19T12:56:55.451Z", "4.32.0": "2025-01-24T08:27:42.699Z", "4.33.0-0": "2025-01-28T08:30:16.982Z", "4.32.1": "2025-01-28T08:33:25.007Z", "4.33.0": "2025-02-01T07:12:08.396Z", "4.34.0": "2025-02-01T08:40:31.991Z", "4.34.1": "2025-02-03T06:58:21.325Z", "4.34.2": "2025-02-04T08:10:10.555Z", "4.34.3": "2025-02-05T09:22:13.642Z", "4.34.4": "2025-02-05T21:31:19.546Z", "4.34.5": "2025-02-07T08:53:10.466Z", "4.34.6": "2025-02-07T16:32:15.581Z", "4.34.7": "2025-02-14T09:54:08.427Z", "4.34.8": "2025-02-17T06:26:33.568Z", "4.34.9": "2025-03-01T07:32:44.750Z", "4.35.0": "2025-03-08T06:24:53.778Z", "4.36.0": "2025-03-17T08:35:52.023Z", "4.37.0": "2025-03-23T14:57:14.878Z", "4.38.0": "2025-03-29T06:29:15.369Z", "4.39.0": "2025-04-02T04:49:40.124Z", "4.40.0": "2025-04-12T08:39:43.810Z", "4.40.1": "2025-04-28T04:35:31.992Z", "4.40.2": "2025-05-06T07:27:01.352Z", "4.41.0": "2025-05-18T05:33:39.596Z", "4.41.1": "2025-05-24T06:14:41.378Z", "4.41.2": "2025-06-06T11:40:40.771Z", "4.42.0": "2025-06-06T14:48:20.901Z", "4.43.0": "2025-06-11T05:22:47.703Z", "4.44.0": "2025-06-19T06:23:06.415Z", "4.44.1": "2025-06-26T04:34:24.229Z", "4.44.2": "2025-07-04T12:56:16.376Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-arm64-musl`\n\nThis is the **aarch64-unknown-linux-musl** binary for `rollup`\n", "readmeFilename": "README.md"}