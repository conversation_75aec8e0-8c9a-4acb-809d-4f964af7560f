{"_id": "acorn-walk", "_rev": "24-b6d26964c0dd75fd596955f410ae7bea", "name": "acorn-walk", "dist-tags": {"latest": "8.3.4"}, "versions": {"6.0.0": {"name": "acorn-walk", "version": "6.0.0", "license": "MIT", "_id": "acorn-walk@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "38af18ac5f0f0df3ca8eccfd4a17ae1a120f8b11", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-JN2iGhTe+mGCNy78j+Qk58SV7BrZvanurX0ja9cVHd+25Goqju5Jx+ZgJcxeHPLzB9Soc2Xh4+HCy7s2VXC/Gw==", "signatures": [{"sig": "MEUCIQDTrq69rKdFtB1vxiBwcbROcuBiA58FUWjkW+vmaMhU1gIgJXu/ueSwT/ZS72BYKFiIXcdJ0ZIRLVfcQh51UdFYYVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm2FoCRA9TVsSAnZWagAALkYP/34BkYbczpM/Vb/JfbVO\n2iHuK6SBmZfNDKPQbk5UEqjVmQSlWavkK0wxdaVL0K2P1b0chciJb8QWhT31\nb4RmwSFGk+gSGoIQNIAbqCLJZfRY9Aq6rbzYI45fQXnWUqA9Ft8OmR/8iLkq\noB9vNSG9v8oKaL18eF6QO0ulnkG4heR26dXk7rH6WiE7zrk/acXcotkymDCO\nMca0SwKcQRB5gGs5xxu6deJPImVBQauvB5Cd9azcY6sxtzRBo8/TQ0Q3Q3HQ\nKciClBL1HUBOblV8iF0u5xYPf6j1XSoTO3BLbqK+ZzZ2VfO+9nXreOR7pqGm\n2HVseSzJtbOGr3U68LTPzjd6lUxCbLP4rFVkSew3cg1RC6TlxKrpxCnPYtrH\nG6csWwheU4hlz2QlI7tBYwwdtBIm2HOkbhoinCxQr/Jz7D64mKeNyS1qF7X3\nQ93nQGedQgvuAncnJjCLyxnf1ffnPsuIQPSoGNsNGlmrnmI05jloqzZBspU2\ngTv1kOG28vhtTALYGrTiCt2IBD7cyJp31Prm8b1AaPEBSBzjD7uGVpGRKcoh\nYv0qNqYGizxgFTNW+V+litsDDheSWxFlJMRkK29D3hmUeZArdyXaCOd5XOTM\nSt49ZNMCyMLivizdnPwX096whcP/Bp/SLHVqiglQX0K7kI5cpzVAF/8f97ce\nKGQl\r\n=t+0i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn-walk.js", "module": "dist/acorn-walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Doesn't load due to mistake in package.json", "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_6.0.0_1536909663064_0.8902855606032709", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "acorn-walk", "version": "6.0.1", "license": "MIT", "_id": "acorn-walk@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "c7827bdbb8e21aa97b609adfa225400d9ae348ba", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.0.1.tgz", "fileCount": 8, "integrity": "sha512-PqVQ8c6a3kyqdsUZlC7nljp3FFuxipBRHKu+7C1h8QygBFlzTaDX5HD383jej3Peed+1aDG8HwkfB1Z1HMNPkw==", "signatures": [{"sig": "MEUCIFNa37jXdRAZLUltnq9Iqmde1I5U2YdJnue4sxxXNYc3AiEAipvuPS0mXvHLYB9i2T27kseO7jUfkLrgqFjD/eAvrcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm2H8CRA9TVsSAnZWagAAcN4P/3Z8Qknl/3BvbCMGpBx0\n5Bpv0XGCKsGH9uogJp+Br6APYNrYj0I0roLVF6ywv1C4hCn1ELmbbNHq3h/c\nU6nbg372/eQKv8ZaFkZFH0KJLs8KJWiQ8NvW3kWKUdqjMHryhOHsXVRtu/+x\ncXUmxolhfCi+XRlYwGY+gURPw616BFe41scYNGde9ZAJXEU2NTVGLgidle2M\nfZdNunZ35Gp4nmFNjgADcx4hh1GcwFxn5cfA5/WZt9ejyHaZcSJ7eGQXqRJ/\nOrDw+tcpnp0BhQa+im0dsSl01i32wfojIvCYLM58N8pZfRvJNGtb8r3NtqlJ\ntJlJtvfin070GvQkBWhF99WoxVSUQ+wQU2e6mHA6A/88OMPM5P95BUZJIoco\nda9D8bu09KOoQOoUybgveU1FnYw/5lEMxbSur+ff0wRiL+VdavCbkmka/Wvd\nFIr7hsgD7CX9k8xCdY7fTz6E1xZnNAa5bXdbNK8/B3Z2CMNXULTXFZjw6VjO\nWb+xtzI9z5F93kXwhpzQQWZeBe8RYhyXO/yHoAswy2DX72vXG+70t6lQTUtm\nR7zLzhC2RT/WfIyOfAotT1W9p0LXdAUlEE/u24nBpdEZE9gup933r/8iaStJ\n41T1YHsDLp8wu6F0DYHCnS1OHCP0jYNM9xkCJewq4a4QhT/8V/+BATHvIkx9\nC+Bd\r\n=tAC6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_6.0.1_1536909820412_0.665815735846824", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "acorn-walk", "version": "6.1.0", "license": "MIT", "_id": "acorn-walk@6.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "c957f4a1460da46af4a0388ce28b4c99355b0cbc", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.1.0.tgz", "fileCount": 8, "integrity": "sha512-ugTb7Lq7u4GfWSqqpwE0bGyoBZNMTok/zDBXxfEG0QM50jNlGhIWjRC1pPN7bvV1anhF+bs+/gNcRw+o55Evbg==", "signatures": [{"sig": "MEUCIQCocE5I6G7Kp5qmAlRsxC3rZGY/RNo5t6Ww91OnCq789wIgPGQeYynlXCXBgkhyMsRTP7wPyHz3L8TQy3Mrm9kLmNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrcoZCRA9TVsSAnZWagAAaQ8P/2DrNwh+Dd2Cs2mOTruZ\n6Js3AVuixW+YplBANH8fVe5uVfaSfLCv9YGXV8/l/XmLxlckGaH4kXCoCEyJ\nxVZnsktSKlasqhPn2O+Go3zuiTrQE4M8HiuiP8Z/tQR6w0Fhfl1rP6oACw1E\nztcbkMG8mfWM8AfEnAXcJMHYJccLO/6OvZcCg657p5BnhU6Gnm/HUxKDEbnM\nJaHRdr3GCd0/7P4nAB2eTuEK80KS7U+VJPBndz1fkAKDB+3NlTWnZ4VxmjVY\nJcQIi1HGRffF/bm/MLp92UFJM02Z1vVNxFFqvbFLf/XCZI00r1RzevYv7gEB\nH1DqRdlNxRMBOr0Ms6gquqKYj7aT7HSSJmb/by0SBnWrF16JDnHTtJU6e/E2\n/NBZqMcKcKQz9READf6kCQSrv7i4qFMI68pgrXZIqtIaaudH0twyRpfziHpD\nJVFVHmyqbM0Evm3dPqwNqDgg5fnwjCWugojk5vc4SB16eU1rA114nncDFL1j\nPgCBxJfzi6VwO9LP6J9JfYVnWSb6oNnM7lHCklgJH5iTAeLWyMIxKBLLsOXB\n5PqtydbRsChgIaeyxe5SvjJ3rTUiheeTqEheO8WTDM4zF9qnjIuwFP1YSaoY\n9FmYbLwxJibk9HSMDAi2tflbdVuiOs9cbQWWjRKGztimk2+ZgCxhM9CK8KHX\nljPO\r\n=nwEK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_6.1.0_1538116120592_0.16263128928521065", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "acorn-walk", "version": "6.1.1", "license": "MIT", "_id": "acorn-walk@6.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "d363b66f5fac5f018ff9c3a1e7b6f8e310cc3913", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.1.1.tgz", "fileCount": 8, "integrity": "sha512-OtUw6JUTgxA2QoqqmrmQ7F2NYqiBPi/L2jqHyFtllhOUvXYQXf0Z1CYUinIfyT4bTCGmrA7gX9FvHA81uzCoVw==", "signatures": [{"sig": "MEUCIHXLU+ePq7mGHda4d2BoNWYuAe42XjJKSBq8Nt+JoqwTAiEAo0/evfGT6mdFQvSQMs78HMOfI/KaYwvXpFBaCh7ZJzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6pEdCRA9TVsSAnZWagAA3v0QAIl5YksIo1U0+joKuD/2\n2W8TTBIwK2prDL+PpnP9bp7eCMFkh3oMip82ENzApni/Cjq/QejQZaOXW1Xv\n/opEPCvGYH4YCB4ykqWccfQBMu/ihZyG4DJHzsDyJ552isWkRbHgI8vZnb6V\ndEdzcyuZ1b0X/+40Blo1LQCPlE1cmUS0aMjAESeCS7VmjkzSbo1n9/20LgIf\nUNAvBcYtC/NGQ31z5ZSzhBLjr9E42bpJfKRKxUT1iNl3TUdzh+a/f5yyrof3\njE2F38/isziXHJu2QsVSmhBVqIxHqM0mz/RI1q6Yh/3yIIQU/0DvX9Eu5qc3\nZ87P674eGw1/pzkV2a5pm405tw9W6YIV0UNj//SLxWoeveLVl5X7WmLB2MTt\na69E3dUAk2HdlH+tc4M0Cqw2Ku6yu7vmrrs5OwZHJIt407iKX9anDHWWa2P/\n69Whc2idmizspsqJadciqXjXATNzMT1PBMj81cp03tWdBh78Yoq2EVts4hS6\nGg6uwMDwIw/9XD4DB/tLUTrBWEuN0Xk4R61J43Uh71Gr5SCXy/3FhKz21Aax\nSBNAWca4Hy8eO6g2awY7nKhvFkNXtciLJOhorrDVf1DQhiAocPIAXGVviwVx\nLw/2nxF2ClMXSNwgZIhYoiXRAJswxxwhsLIOuvMNudpWweCuPP3cbOBHAVkV\n/KrY\r\n=7LVC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_6.1.1_1542099228973_0.491250253564689", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "acorn-walk", "version": "6.2.0", "license": "MIT", "_id": "acorn-walk@6.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "123cb8f3b84c2171f1f7fb252615b1c78a6b1a8c", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.2.0.tgz", "fileCount": 8, "integrity": "sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA==", "signatures": [{"sig": "MEQCICkW/XrhLz0GMYY68AIQBY+TRkqboTDGTLqDh/QAOIW0AiBGxpxT8daRQw0ukRnkEuxPcmDuLyrt4ZqaQwsOU4eZJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHZ4QCRA9TVsSAnZWagAAP3sP/iAYRsI5pBJofXWu0v+A\nRxrNZ6a7CQrcO1a7iNYfBHKXl1gkhVC69Ha8oMNZz4r5ilLafExvVVOLM93m\nexITFAb9quIi9vDMshVivFSTem16BTpdzApbbbPMFwKn8ib8bD0S5B/Yw5uv\nze5PJjS6VRpiMU+4h5eAPADFSCPoC50eamTRMY6YHDdk3CJSzogK/lR25Kek\nT+Ei3VqDaXTBLcwHXNxCVyMkETjVXRd5kxRAlZlb/uL7UY9K4E8kqK37mHVo\nUmfSPnN49aSq1S5PbvYlhFdL6b6sMVI++kFkWmbsmnaalvSJFlKnXkMxekLF\n3T0AEQoli5H9Ud7JovSlGw2vGyoaBxpaJdXX60KpmzXvM4WITA4Va3jfBjdO\nQcMQCtxdS8s77O6VBCUMVav/C3aBasOzDuNjqqfGXK1V6EGXjVpUlfHbTPpR\nQ5nZUeMMgo7p5bEphmcfzhPErzIq5ffl1+TPO0HLo5+jdwAcQbPUSTxs5ywW\nydCvtgxawOm1oisyiZKSep4ZchPav1pSk1NiJ1zIVZBfPn/TN69DhoaUOaUO\nVSanopH6xRIs+gKf/9uU8sv0AsqNXPPHSrGPc916KwuYc7gAw+ocUfJGiAs2\n2nJa3vq2dnc+iX4sYjTopWL4MXWs4szLsqWPd3dNYR+Z6exYonBlXKIOWpwO\nkCS/\r\n=mwsF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_6.2.0_1562222096105_0.2095978152429574", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "acorn-walk", "version": "7.0.0", "license": "MIT", "_id": "acorn-walk@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "c8ba6f0f1aac4b0a9e32d1f0af12be769528f36b", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-7Bv1We7ZGuU79zZbb6rRqcpxo3OY+zrdtloZWoyD8fmGX+FeXRjE+iuGkZjSXLVovLzrsvMGMy0EkwA0E0umxg==", "signatures": [{"sig": "MEUCIQDoohxCQC2yQBNDGvWapPHtcFHEvTmjkWhMfV3fkzObVwIgF9pvZTnXC3Yit4vSLwxxDjxMe2Hm/pLgDR3DhtLDm/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUm3dCRA9TVsSAnZWagAATWMP/3wjMAWy+ITcO0i/1vSD\nKa86pzc5opQ4ERBww/XMoY0ChLIPueTzFHp70m61K/5NEQqdxroGF3NO1HFY\n8t+tKaBOPeC9bJmlgOyv6bGk9q5t/GtYcju9vhjHmqjlcmpIMjkFzvO6wWRa\nKvTZTBCb9rSSecHCTGk+WrOzz3w+MAEHEpQxLD6BgNjVvFNXjeEHkKdnQGZ5\nwToWuxB5BhrwfcuJtKWeMPUZGUPtYC4NwCznFRRpJtZC1+VHPUNmvZyW4bxM\nwr3lexumUoEedI8bV0I+3FX6fcmr4eQVUXio63lDGX2KJGykBV9BtMGjBrqu\nokfDggynMlcfOeIeILFJ0sDbeV/f5F5Gcy8qieOmT8M0GE+SPpaMbADPhQZO\nauFjn7b7dDrAO9LMDWBfet90a3JiisHPg9kxpet8TE1AzJdhJLQIti0dywTN\nHQwh11amYIGcaLXRsYe5iH+CqRpcgWRNCTWEgWLVcFIDwMI+VQPiopQpn8QZ\nKA1s0VVUxZHhedKBOrtAwfcGEzbbFawi3iqhyyLJqHvZIeGQ7f/TvOgl35Wu\n+hnOPBVvqqQgZYiE4D70hA3qz1jvTOiT0xYXlI21dA4ByLngO0L17adDjawf\nqNPQOodTK0M+aA12100BeIlVHm0T/4JWiOoJ6JQtxy35AZvKjfLuUm6BUMEH\nFZCj\r\n=okUT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_7.0.0_1565683165039_0.5043107677708225", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "acorn-walk", "version": "7.1.0", "license": "MIT", "_id": "acorn-walk@7.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "48387aa9a83bba67a9909164acab4bbc5796cf87", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.1.0.tgz", "fileCount": 9, "integrity": "sha512-4ufNLdC8gOf1dlOjC1nrn2NfzevyDtrDPp/DOtmoOHAFA/1pQc6bWf7oZ71qDURTODPLQ03+oFOvwxq5BvjXug==", "signatures": [{"sig": "MEYCIQCvEy5gqlXVB1szF2huxMeQ88tyLIBgcubJ7UVVFR8VdAIhAJT48kF8fJDlc9PIIDoH/uGH4L0RfxWjaOTM0mEWwJOc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQmL7CRA9TVsSAnZWagAAQ3YP/3KXJ1qR4KH5B78o7iBn\n1ZiQF90sZMWlOaJGzWXb2xg5x9hCWTBbdt7jXNvTpNUyW8fLjJEwbI+3z3GF\nE3ook5FRi08Ap5qPkMBkpK/cALG84rlVa5fF4wfUa+ItEJEAFAn3HiLvY7gs\ncglsdjd1H1CIXXjDvpaPYU+hGfDGXrlnDdY8dCZGT3J/zzb7BKcYgKAeQrLN\neL5G2kV9p+Hwz0TT25jm4U70zTpzskuBaVMvk0vO6aQsKMtEuxI9NX1xFc0b\nAqtxvSfIkhsJDdEYyJxnyljIo2hM9hf23H+hW/oNAP6UCGbtVwzKOI0a4Cns\nOxbXzQlsx6MHSYFTt9dUQyVMibzqL3scAhEKM0hfge1V52AhHp9x78fdhCU5\nfXLfmzCpvb3gToA2I43DS/eZ1Fv1ig8He0ZSa2SRrMpT1da3FgYcxeGrWf2Z\nKyQuMUuMcYibN9zCC1Fk7kOfoJW67bvdayNJ35fzL+a5zJvREpfq8YBlESu3\nYVXS/FW4VmhP5AQyvKHf+x/cpAhckzInVWl3GJ1DsHHacsTs+Ux1dfV5WDK/\no7njB39QxSLd2UAG5oJ5u3P2kmcRxoQdQPRnjBvGXwXYgCDmJJvDd8MjdFFM\nCmWRi0LUgzSJDxS0MPug5PIYaVwWmasPDV8pdpCZ64TZwIqA4nC4x8w/AXV7\n/hXg\r\n=kiYg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_7.1.0_1581409019230_0.3071486916780528", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "acorn-walk", "version": "7.1.1", "license": "MIT", "_id": "acorn-walk@7.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "345f0dffad5c735e7373d2fec9a1023e6a44b83e", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.1.1.tgz", "fileCount": 9, "integrity": "sha512-wdlPY2tm/9XBr7QkKlq0WQVgiuGTX6YWPyRyBviSoScBuLfTVQhvwg6wJ369GJ/1nPfTLMfnrFIfjqVg6d+jQQ==", "signatures": [{"sig": "MEUCIQDfj/+z4iqXeN9Srimnr6PaJNYsUsNMV0t8965JjKIn8QIgW4IBTtaktnw6i8WMZTHPN6mLLQOvKjBdZpOEcsBYc/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRX6tCRA9TVsSAnZWagAABA4P/jgrigY6jzWbgTdklUDL\nj3eeMFloDRsDXbWYh/nSOVUB36Y74CDteTIhUyyB2YQWUnuXpnjmgLfX+xQC\nKxVcfRTYokld8kZ5vNAXNbEVxc+PMGrN/XxdqKOt3oZ+FBblGnwwTGAUWDOp\nGqGdAkyoByBy8fbtHV++lOxsg202+V9nyNFZ9bPRbdOdtsy6/GATtm0UKzlt\nda3Ve5l0DgRwbVxkIrvatRSO/R1lAytpcsakv8SAYlKs0E0zraUXF6GZ9DnU\n4/MAysYjjEcHJ0k0tuIteXUPv5gbdA5F3CmB14qWBKampagNNcpCH8H8uILv\nWV7pc57Ak8GmNIo13EtS/gZkim41MexN79FXYVDX9TsM2PsiBfDntjecDj5+\nb4at5lY+ifhNAV7xw2wodQl9/eEAk/0joZqcCvBKV7ic9DaUvyIQG5Y601zd\nA1wYwZanqrqbnelz1vCBipK7NHiwo9I9c0wnu7d6SrSIr+LQ4sYTWwmRIAtD\nmDN4/2keT3jCaTkfgugHvasX9hJBo7r3r2YpGxw+B7UrLubDfm5thiWixHJ1\nEZAQsSA59ElxFpB8cAOZvTXrJSmKaic7LIPPJZICCQguHbco4JoEPvn6YPw8\nuhpDjmN1XaGEUPJN1zhcbbWLqgnu4n/pmgL9IMWD2aTUUXXhjJa4nQ2gchQc\ndJGQ\r\n=hmRG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_7.1.1_1581612716867_0.5367422376928117", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "acorn-walk", "version": "7.2.0", "license": "MIT", "_id": "acorn-walk@7.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "0de889a601203909b0fbe07b8938dc21d2e967bc", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz", "fileCount": 9, "integrity": "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==", "signatures": [{"sig": "MEUCIByb/afIOahnbsMsExyGpw9Vyv9Lc7G9oOmnzqd13/yzAiEAoDTNuSPf/BnBj0J2peaYf7zOe8TAvLPNkpkK7FWX8iw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6bdiCRA9TVsSAnZWagAAQ7MP/1d2In1I7EDrIxQ8Ojw6\nCHUy0X1XLcO985edk0yXhUsdbuEoFf1LXGpZSYuMaF//JEdzs7r30YYhk5Q3\nctvWgbieIoen88j0mh18yKWp+w93ykMEarCiE6VmTGPIRO//yOLRdgiklM5U\nMi0e/8LfZ6U7hGDMW/EPbCCHMPySPt4Iq4vIGkWGakps6e8Zmaa0ksHQGFRn\nFqzMMMgVzjrYzRvW2Xul6oMYa/jEm+d0n61jXICDmy6aQ9Obq7CDcwgTK7im\n4Dio9+WrpDCv2/rtQR0wHQIufdeUhi4azB/7PPF+Nqhw2xXZ6nCo6/jDCKKv\nIzSN30062OUK34zHx+GwuVu4Ky/ed2ywDuQVCjmYGzVlCdUJHJTc2dg7PGL4\n1ClxRw80HQKr9V5QSVZgnvQBL1irBqZ2JEvBhMoJ/CG4S4wGmVCiDAVTOoRL\nR6+IawcY4DF4sU9wdXZwFheUd/qjYj+T2fljGZBImjn9DuYuTPznqM+o3Wo6\n1gnQUYExDzyGDTExz6rFpj6T6pwXryPqWdPMhH23o+XnJNDKXCfIEYZ28ji4\nM3asPzVfND050nBBs9AgjKTpOacWFhWgUiZLLaBrv7uaLWhzYdzBmQx3b00n\n/otypDRCGbeMNrxLNwq6sVVPGILwr6bQyBqqL6L4m+cmWDcdz3v0IZPEA8cO\nv4SX\r\n=GQ3s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_7.2.0_1592375138357_0.19661161995675647", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "acorn-walk", "version": "8.0.0", "license": "MIT", "_id": "acorn-walk@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "56ae4c0f434a45fff4a125e7ea95fa9c98f67a16", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.0.0.tgz", "fileCount": 9, "integrity": "sha512-oZRad/3SMOI/pxbbmqyurIx7jHw1wZDcR9G44L8pUVFEomX/0dH89SrM1KaDXuv1NpzAXz6Op/Xu/Qd5XXzdEA==", "signatures": [{"sig": "MEYCIQCSVICH5R9VrgJ+PZ0oYjzcPu1Mu+KFw1/UzlWCZy9qwwIhAMApSotU/Z0vw9ObxYyJCFETsuyWLQHFWXLcy+/4g7cA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM+kOCRA9TVsSAnZWagAAbyAP/is/uhNJB1fYdPtyLXta\ntuNmHEHsKFPrElpoyYJ76ke9sIUrefhdMwxeQHL5k+CPgOFovXuktJc6X/sB\nNaFUme6sfoO2XdNSwUuHOOykGWgsJukStErptKVp78UNElNcX1eOQ7Vc0QIv\nNDmD3yLJSatQJY25uNoiI5C4xXfnXei5VFOc4MQL9FzhaqH5qfifhauwc6ZT\nQMDxvevSlCVa1DysQ/oc8kdnG9Mv2sI2gqf9Qg6jaLPvmytz8rXiWnVJ7LGh\n9F//rk1Mzx/4mhdFiu7vbrKIlI13sgcrOE4NZjmmxP1Cz0k/Eu1FLpT7aGUn\nboTk7PZPL5SMKH7j6T+891L4m7nXh5Gvrr4WlTV2RfqpzIjBFtFKK4wBShPj\nz1+/9D3BWVk8WkSprnHeyZTIpdufXGOHObOYClmnecYwgDsBRXTw+meJCcgC\nScJEvcIxO2L4Ag1I/L4OOZXN+ElSrMujLobe/B1Hn5UnMFtRNT/9bStxjLjh\nVX4sLxjqS6+jRnPqIGwO89vYLLUlSjqTVSbEMji4lR0TJOLOo4YVsB9bMKWL\nuSmWk+wRUFXhZmuDUxFwImSGsk8Xow+FonLfpblY/Wc5G5d+Bm+RRKisXlBX\n7BP/Jmw42aycdd6pIN1a4bcpf056weK+++6lD+c/tlzCjYDkXj66mdxu6gZ/\nfUy6\r\n=WHii\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/walk.mjs", "require": "./dist/walk.js"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.0.0_1597237518460_0.4355543501282688", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "acorn-walk", "version": "8.0.1", "license": "MIT", "_id": "acorn-walk@8.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "d265d35db6940a656c715806a448456ee4fa3b7f", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.0.1.tgz", "fileCount": 9, "integrity": "sha512-zn/7dYtoTVkG4EoMU55QlQU4F+m+T7Kren6Vj3C2DapWPnakG/DL9Ns5aPAPW5Ixd3uxXrV/BoMKKVFIazPcdg==", "signatures": [{"sig": "MEQCIH7npAGKO+GgBtp+7N41/Cmff5b5j/O2CG1MEqaKKtJoAiBsUGXJ5qiG3mIfUB91zuE2JoD8c9MHNMjtmyTeuW8Ubw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9EeICRA9TVsSAnZWagAA+icP/iKlrct12/RXf03tNwyh\nnkcnLacwYnjGPa5gvq1aqrHckg5LYfH87ltB2PNobSTf52+TJJBV0+lYFKFR\nbFKaqI0hBarVYCm1DQlO7NVhuFZmSrGDfHo1v9YWdbqn/obODdJEy1XpE+AK\nS2MUP1fZ3KcWhWrzi/otwZvdEGSpirFKwPTygqXZKzHfzTAC33ENGlzbaaaP\nMqT//ZgxMIFvuDkNrkCoyE1Njq/OnvjbTHsBd0a79uwet3qOWvZ/RfJMZFPy\nDDWbCWDxOpxLn3lPjNSrgnpTHHokLDPISJj4nzfYH+pVHyxURFFD37/r1jdY\ntfS+DwOlLbOxM1iTxXdh2oLVST+udfSRDR9EWEWQp93q25IAKQZwRUao5HOk\na6mlnK15Y+zpFH21DRl37prD+HTWSb8Fkarmc/RVMOyL4bjvhNlsJgL6GV5v\nA8f35Kp232CpQ25cB96oLZTzZ7rYr26EioG8l5+PwTNwGDkNxTZvVrIoakya\nesYlDYFV+iYVmLIfi4fwFeB8z+Y/VJ3XPrNH74EujEOxYDGph3fqoE1uzX4y\nKhPKslEZSXexp/n8RMCxzw8aMqnJgKylw4gHBFXhvVb6McjK5ooblfsv1UN8\nfF4uPG3NoprNkpxep0fZ5mjrxQL9xfk/jDkY26od3kGaaNCIqTBITPEfBIK5\nUhhN\r\n=JrH7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/walk.mjs", "require": "./dist/walk.js"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.0.1_1609844615597_0.02595160547439268", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "acorn-walk", "version": "8.0.2", "license": "MIT", "_id": "acorn-walk@8.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "d4632bfc63fd93d0f15fd05ea0e984ffd3f5a8c3", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.0.2.tgz", "fileCount": 9, "integrity": "sha512-+bpA9MJsHdZ4bgfDcpk0ozQyhhVct7rzOmO0s1IIr0AGGgKBljss8n2zp11rRP2wid5VGeh04CgeKzgat5/25A==", "signatures": [{"sig": "MEYCIQC6tlDuLHTSg9Q89GqrmgIOPR0cmAiAozgC7CTXjl9TRQIhAM/2IL6gg7RALvPs+NXyaDAnT1gF4shjYqLU5xAn6ZPU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDnabCRA9TVsSAnZWagAAKnUP/jnKU7KVSp62qWGYdy1U\nK/8FfQXCRTJbKLWiyVyMU3/VyY6AC0z/jVv3Xqq3uTgJIF8kB/0mPybkLPYL\n2Nin6muoJULNSvyr0dLxwB5VMcWlzWxwakPycspMXo9gL3RD+ox1Y7us+w59\nOm2/0KoEBpjMU5eyzr/4ZGG52Y/FdAS7ev6mU+9TJhU28PoxO3hbCgf8xJSi\nk1T2S6qu6xoY+YT5ziB/B992JZXZHuzqE6IJyNa4S4zzr3Ci93SsdNfyxakq\nu8bC0iv4sD6Qhi+zMuySbbIufDoTnI6XKqF/VH7dxGJ655kRtcadaCkX28BA\nigRN1s/S5XASJikWFgaeZwPyBJweuQ4zY4/EVn+4DMehtNUoPj9i9BYO2U6Q\n5gRS/PvgPvLJU/AzbDfdsBldCCL2y6UgKw/RMst7+N233nFsI3n2Wh3PM2e+\nIPri5YvYol52k8amhkdok0ZN5vM03odcz1822YRlGYeou5LWB/K5aY9zymL4\njLKMTgR1fCEywpU9QBMOdBnbXWDEcHwcFhN145SnhMDVX3Es0TueHbKhedYS\n4o20FomaaeKky24LqavlA8MdECaJwmybZopE5LrnSGTF4KsqmN6qhJrP03Bk\nWFmVc4JAnvcAFWFfeb5FwTDapI5AKJ7yhSDq4pHkTSbdqo6AF+Ttfb1FGQSG\n6k18\r\n=lmSj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.0.2_1611560603350_0.5056577448148931", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "acorn-walk", "version": "8.1.0", "license": "MIT", "_id": "acorn-walk@8.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "d3c6a9faf00987a5e2b9bdb506c2aa76cd707f83", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.1.0.tgz", "fileCount": 7, "integrity": "sha512-mjmzmv12YIG/G8JQdQuz2MUDShEJ6teYpT5bmWA4q7iwoGen8xtt3twF3OvzIUl+Q06aWIjvnwQUKvQ6TtMRjg==", "signatures": [{"sig": "MEUCIDXUW0RfX2Edn74V4qkVRUMkw9HFvJuSE85xBdgrhw/mAiEA7OS6hIhLHXJr3zqrvhLcja9GIvrz9VZQDeLWsdjUaG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgg+8kCRA9TVsSAnZWagAAw1gP/1VvfrcilrKGVzX8xp4C\nyIPTFAzqi6YD5mdnt/kSb6tY0vAk0TLVGVQwjwVgdq687JDkYN/R2FvEBibe\nRTFe9llYF9WGqtA7bK7kT2X1Fc1t5MP3nKYCbkD+Ke3uG0cTgfMCvZGMU63n\nbrR2JBc7xp4196XDLhtZv7US8Uh6O2jlkElWhSW7lFKUDHNZgy7C3YgNgEog\nx9TA77HPjsvL7HbnIjFKNtKLJ4NDY5TYGllRzl9Kq4lfrdlwrs13Hcp+niZQ\ngqOVf85iLgYu2ReyiplHK+w1GHXCyb1MZtdMNedxKjR3Xn3dQaUkJXQN2beh\nk9VmSJzrXxjiZwf8RBWHjJ6FSq1/g4HpAKvc2DX1NMisfb49B/dJfgltG1us\n66mrCjJDJggcRvJWkTsIU7h66v/8n8TjJCRePz2wRoR4ABEfZW+HfhGlKZZw\nLkoNsS3qg8s+CiemktZfO0yZTvk+C/glv1D5rSUg7YVU9c3y0Fag8vAE4MHH\nTDe/nnArNFLHYJnklqYWf7pqqaCF0rLl4f75QX44NpnwylOfEZ2+5YJ/i7zC\nG7NNaXSpeywKvaR/Tr93w6nCYARKOgqWeTz/xDea0oSYn0hK49IV04gRtdJY\nMEJY6IvZgTv+pnx/6suDBdvTdqQHOtSjTf9TwizVkzIJJZL9aHFkDVtV4pWj\nY6cP\r\n=gbjB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.1.0_1619259171722_0.04208652607990926", "host": "s3://npm-registry-packages"}}, "8.1.1": {"name": "acorn-walk", "version": "8.1.1", "license": "MIT", "_id": "acorn-walk@8.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "3ddab7f84e4a7e2313f6c414c5b7dac85f4e3ebc", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.1.1.tgz", "fileCount": 7, "integrity": "sha512-FbJdceMlPHEAWJOILDk1fXD8lnTlEIWFkqtfk+MvmL5q/qlHfN7GEHcsFZWt/Tea9jRNPWUZG4G976nqAAmU9w==", "signatures": [{"sig": "MEYCIQCmUxFQ5G5d7XRhpqz9umLH2o8Ao1POUHJxbIdCeA9WvwIhAMFl8uxGL15GJ7kxrW5fSF7zVnOEtKZngDLx2KsQN326", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2sw1CRA9TVsSAnZWagAAa3AQAJc5R2anmGk+TjUpzwxE\nTPA3HLZ+e5X1F8PG2DXXrWxngRJmzASD+evCVD3ikxY2mhzKkV4Dn14ztm4s\n+ZMj1tdepLzngzVIrwWIKfiIOo4xvq4G2++bYPS0OlCM+RVSuOVfJoLOEJbI\nEkYJPbNes9SNVRDCJZrlQhXX4AZ7HYSwNsk2BQjEqvpRMD5qZ1KGZTUNfbId\nJ0zoWV8Dwk8dGg5katx+2BYt374byWHwnGGnWccmdOdFtPerveZT+Llo8OvJ\niBHAQFwQCh4THBMxPZ+0bPOjOTc+qlDestXWBrJP2AubHlO/xsPIAo45IycK\netTJthhWjMCUw/a96oUqxWD5ILd+Mk7G4mYfInXTMI3PhXNugfPZ4XlgFzDH\n24OOuc7DaBR6uzTDL1ALLJojLZOGaWjF/+3ydlco47VDZsBbaDe/jpaFawdf\nlFYIPsLdDyF2YOST/cl6Yo5yet2oxWM0QbiCkmnA9klYCDfAHa0y0Za8ylcY\nsfk55sL4VqxOlyHZqavtYMy4Wq97YV0tj0FEuratfO3tBl/liX00nv2pMaVI\n0enDkfo8z6tDe93kskeQRJeXSC0Y3/9rlaUlpYMgxpkw7SbBjJD0/+tmOeod\nBhCwuVsUSaXpIwJGR7/D1dw7ahE0a3ONGZpBhRxFbTEzKmVMvB0WG7mVZTdi\nGdB+\r\n=quc2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.1.1_1624951860539_0.6402580136212959", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "acorn-walk", "version": "8.2.0", "license": "MIT", "_id": "acorn-walk@8.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "741210f2e2426454508853a2f44d0ab83b7f69c1", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.2.0.tgz", "fileCount": 7, "integrity": "sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==", "signatures": [{"sig": "MEUCIQCI9GFPyGzhre+61vGIxmH2yee/YjihXLAK6luBBxO4ZAIgF1dTE4slv2LoDnnWaHUPsFqAgCgQ9EoQbdgI6d/DJSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNc92CRA9TVsSAnZWagAAly8QAIRU0fEQppsRI9QsmwnE\n9bldnz6C4HKuAF6rHNmh7khkVS0VcmTnFtX1jK8LVJisqx0L+qijQjdOJLOL\nSIQKSvR6SjEbFnLlDSKvJRkPOGnycJbKcmIdjDij9b+IOrRjw0wVZKRzEkuF\nLYfkR7rU9in67UcpEb2BxJ4mHQPFiDeUe+5cTPnH1AFgfTrLPh5YPRaWr+kM\nC9csPpwh13bBSv1444wIEUXK5MNb3o3LSVYgg/X/KAAhKQDvGlLY923m3hzk\nVvtyJzC2e7lu4bi5v91WctpU3DXT+DIDRs1TtgMpq0NdZuwVc4F+kWnmhhuX\nsejWQ/B927uAre7klQVvwKMNPsRRG3RbIE+dHuLVaNEFnZ9Dcj9xhb6NaQxR\nEguhLHTrbt//Pxp6xX+I5mrR0ydEK4FlxHI2HZvRl4fAylHgliIPeE2Sr3/W\n0feOsoZB2UXSND3tHFLOdLUCfmX6gseVO5tYhqcQf0hO3esk7jRXy5Uqm1vf\nhql7CLSebj5H4XjkJd/l4aO8RNaBa1TbMiAXuNc7peHzTzaW5WC8V6wovRSK\nry3kXVGq5f35qbKIXQZHHfiEtj2lf0sBNQ8Ru5ICDqZuxsuzf9yBSh7TDPAF\ngC+Mw4NH8BK1KAsIyinnt8QPUdjc+wdqiqJngNtx88A7ySsvk3dWLF8BnAo7\nhACX\r\n=Rt5V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.2.0_1630916470643_0.058057729406866265", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "acorn-walk", "version": "8.3.0", "license": "MIT", "_id": "acorn-walk@8.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "2097665af50fd0cf7a2dfccd2b9368964e66540f", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.0.tgz", "fileCount": 8, "integrity": "sha512-FS7hV565M5l1R08MXqo8odwMTB02C2UqzB17RVgu9EyuYFBqJZ3/ZY97sQD5FewVu1UyDFc1yztUDrAwT0EypA==", "signatures": [{"sig": "MEUCICyvASP7Dk5FPhEr6SzC5RaojUbxYDuNgrfTgjavYuawAiEAxZDcr4BbzrPgUHQ5If7dlv+v3At70uRacFtpXYpYw6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52006}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "gitHead": "4168d2afc59ca36ee61319703778977b6f2b0ec4", "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.3.0_1698343698782_0.6555973328415909", "host": "s3://npm-registry-packages"}}, "8.3.1": {"name": "acorn-walk", "version": "8.3.1", "license": "MIT", "_id": "acorn-walk@8.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "2f10f5b69329d90ae18c58bf1fa8fccd8b959a43", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.1.tgz", "fileCount": 8, "integrity": "sha512-TgUZgYvqZprrl7YldZNoa9OciCAyZR+Ejm9eXzKCmjsF5IKp/wgQ7Z/ZpjpGTIUPwrHQIcYeI8qDh4PsEwxMbw==", "signatures": [{"sig": "MEYCIQDyq9qgjBxmRcRokV9VTYK/GxyzwBTa1YL7vkVEH6IhoAIhALmF8yovjJjrt72psF5YiS0Bcd5jXsKqjQxmfHIoOFe4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52316}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "gitHead": "500f92162571f72b517ede0e9b135c89e2c35200", "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.3.1_1701850291398_0.27928304780323", "host": "s3://npm-registry-packages"}}, "8.3.2": {"name": "acorn-walk", "version": "8.3.2", "license": "MIT", "_id": "acorn-walk@8.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "7703af9415f1b6db9315d6895503862e231d34aa", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.2.tgz", "fileCount": 8, "integrity": "sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==", "signatures": [{"sig": "MEUCIC/aGeeAv1ksFqrj4Ih2Y69yWFqOpw5Te8HYMxabuD3WAiEAirQLFzkBJqg7JBn6DKLHQJI1U8lcO8koZCNvpSpw3Co=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52364}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "gitHead": "2f70bbf73eba9bb3fe0a4f7ad543693fc0892360", "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.3.2_1704982438313_0.3543423485467072", "host": "s3://npm-registry-packages"}}, "8.3.3": {"name": "acorn-walk", "version": "8.3.3", "license": "MIT", "_id": "acorn-walk@8.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "dist": {"shasum": "9caeac29eefaa0c41e3d4c65137de4d6f34df43e", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.3.tgz", "fileCount": 8, "integrity": "sha512-MxXdReSRhGO7VlFe1bRG/oI7/mdLV9B9JJT0N8vZOhF7gFRR5l3M8W9G8JxmKV+JC5mGqJ0QvqfSOLsCPa4nUw==", "signatures": [{"sig": "MEUCIQCDSkBGnj9uo19EHd90HndxzEl8uF+axctpRqlqmXn4/gIgB2nsXL5AHVqshuJ+/CoGNDgzGNuWvxchv5PKCOgbs/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52606}, "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/walk.mjs", "default": "./dist/walk.js", "require": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "gitHead": "ac296596b300624784b4b91e8096f1203e9dee29", "scripts": {"prepare": "cd ..; npm run build:walk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "ECMAScript (ESTree) AST walker", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"acorn": "^8.11.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn-walk_8.3.3_1718349029371_0.4161867865482278", "host": "s3://npm-registry-packages"}}, "8.3.4": {"name": "acorn-walk", "description": "ECMAScript (ESTree) AST walker", "homepage": "https://github.com/acornjs/acorn", "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "exports": {".": [{"import": "./dist/walk.mjs", "require": "./dist/walk.js", "default": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "version": "8.3.4", "engines": {"node": ">=0.4.0"}, "dependencies": {"acorn": "^8.11.0"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn.git"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "license": "MIT", "_id": "acorn-walk@8.3.4", "gitHead": "dfebfd514ccfd3114c6d3aa1c44aa2cb6e5122e8", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "_nodeVersion": "20.13.1", "_npmVersion": "10.8.0", "dist": {"integrity": "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==", "shasum": "794dd169c3977edf4ba4ea47583587c5866236b7", "tarball": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "fileCount": 8, "unpackedSize": 52296, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEOTd36EUc5RsRURiCpMhw8t9kBFz6h+nLTtPnH3smx+AiAU0rUVo5oQVbYv7Q2ZWp2JQBcGK+dhsomCxVyRhpq2HA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-walk_8.3.4_1725871258751_0.07430514448976244"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-09-14T07:21:03.063Z", "modified": "2024-09-09T08:40:59.131Z", "6.0.0": "2018-09-14T07:21:12.347Z", "6.0.1": "2018-09-14T07:23:40.533Z", "6.1.0": "2018-09-28T06:28:40.772Z", "6.1.1": "2018-11-13T08:53:49.083Z", "6.2.0": "2019-07-04T06:34:56.385Z", "7.0.0": "2019-08-13T07:59:25.173Z", "7.1.0": "2020-02-11T08:16:59.335Z", "7.1.1": "2020-02-13T16:51:56.990Z", "7.2.0": "2020-06-17T06:25:38.522Z", "8.0.0": "2020-08-12T13:05:18.596Z", "8.0.1": "2021-01-05T11:03:35.741Z", "8.0.2": "2021-01-25T07:43:23.510Z", "8.1.0": "2021-04-24T10:12:51.894Z", "8.1.1": "2021-06-29T07:31:00.678Z", "8.2.0": "2021-09-06T08:21:10.850Z", "8.3.0": "2023-10-26T18:08:19.123Z", "8.3.1": "2023-12-06T08:11:31.637Z", "8.3.2": "2024-01-11T14:13:58.454Z", "8.3.3": "2024-06-14T07:10:29.552Z", "8.3.4": "2024-09-09T08:40:58.911Z"}, "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "license": "MIT", "homepage": "https://github.com/acornjs/acorn", "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn.git"}, "description": "ECMAScript (ESTree) AST walker", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "readme": "# Acorn AST walker\n\nAn abstract syntax tree walker for the\n[ESTree](https://github.com/estree/estree) format.\n\n## Community\n\nAcorn is open source software released under an\n[MIT license](https://github.com/acornjs/acorn/blob/master/acorn-walk/LICENSE).\n\nYou are welcome to\n[report bugs](https://github.com/acornjs/acorn/issues) or create pull\nrequests on [github](https://github.com/acornjs/acorn).\n\n## Installation\n\nThe easiest way to install acorn is from [`npm`](https://www.npmjs.com/):\n\n```sh\nnpm install acorn-walk\n```\n\nAlternately, you can download the source and build acorn yourself:\n\n```sh\ngit clone https://github.com/acornjs/acorn.git\ncd acorn\nnpm install\n```\n\n## Interface\n\nAn algorithm for recursing through a syntax tree is stored as an\nobject, with a property for each tree node type holding a function\nthat will recurse through such a node. There are several ways to run\nsuch a walker.\n\n**simple**`(node, visitors, base, state)` does a 'simple' walk over a\ntree. `node` should be the AST node to walk, and `visitors` an object\nwith properties whose names correspond to node types in the [ESTree\nspec](https://github.com/estree/estree). The properties should contain\nfunctions that will be called with the node object and, if applicable\nthe state at that point. The last two arguments are optional. `base`\nis a walker algorithm, and `state` is a start state. The default\nwalker will simply visit all statements and expressions and not\nproduce a meaningful state. (An example of a use of state is to track\nscope at each point in the tree.)\n\n```js\nconst acorn = require(\"acorn\")\nconst walk = require(\"acorn-walk\")\n\nwalk.simple(acorn.parse(\"let x = 10\"), {\n  Literal(node) {\n    console.log(`Found a literal: ${node.value}`)\n  }\n})\n```\n\n**ancestor**`(node, visitors, base, state)` does a 'simple' walk over\na tree, building up an array of ancestor nodes (including the current node)\nand passing the array to the callbacks as a third parameter.\n\n```js\nconst acorn = require(\"acorn\")\nconst walk = require(\"acorn-walk\")\n\nwalk.ancestor(acorn.parse(\"foo('hi')\"), {\n  Literal(_node, _state, ancestors) {\n    console.log(\"This literal's ancestors are:\", ancestors.map(n => n.type))\n  }\n})\n```\n\n**recursive**`(node, state, functions, base)` does a 'recursive'\nwalk, where the walker functions are responsible for continuing the\nwalk on the child nodes of their target node. `state` is the start\nstate, and `functions` should contain an object that maps node types\nto walker functions. Such functions are called with `(node, state, c)`\narguments, and can cause the walk to continue on a sub-node by calling\nthe `c` argument on it with `(node, state)` arguments. The optional\n`base` argument provides the fallback walker functions for node types\nthat aren't handled in the `functions` object. If not given, the\ndefault walkers will be used.\n\n**make**`(functions, base)` builds a new walker object by using the\nwalker functions in `functions` and filling in the missing ones by\ntaking defaults from `base`.\n\n**full**`(node, callback, base, state)` does a 'full' walk over a\ntree, calling the callback with the arguments (node, state, type) for\neach node\n\n**fullAncestor**`(node, callback, base, state)` does a 'full' walk\nover a tree, building up an array of ancestor nodes (including the\ncurrent node) and passing the array to the callbacks as a third\nparameter.\n\n```js\nconst acorn = require(\"acorn\")\nconst walk = require(\"acorn-walk\")\n\nwalk.full(acorn.parse(\"1 + 1\"), node => {\n  console.log(`There's a ${node.type} node at ${node.ch}`)\n})\n```\n\n**findNodeAt**`(node, start, end, test, base, state)` tries to locate\na node in a tree at the given start and/or end offsets, which\nsatisfies the predicate `test`. `start` and `end` can be either `null`\n(as wildcard) or a number. `test` may be a string (indicating a node\ntype) or a function that takes `(nodeType, node)` arguments and\nreturns a boolean indicating whether this node is interesting. `base`\nand `state` are optional, and can be used to specify a custom walker.\nNodes are tested from inner to outer, so if two nodes match the\nboundaries, the inner one will be preferred.\n\n**findNodeAround**`(node, pos, test, base, state)` is a lot like\n`findNodeAt`, but will match any node that exists 'around' (spanning)\nthe given position.\n\n**findNodeAfter**`(node, pos, test, base, state)` is similar to\n`findNodeAround`, but will match all nodes *after* the given position\n(testing outer nodes before inner nodes).\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}