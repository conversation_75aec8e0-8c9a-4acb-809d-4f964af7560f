{"_id": "jest-get-type", "_rev": "128-4c8efc59c190fc5e2e96694a909b96a7", "name": "jest-get-type", "dist-tags": {"latest": "29.6.3", "next": "30.0.0-alpha.7"}, "versions": {"0.0.0": {"name": "jest-get-type", "version": "0.0.0", "_id": "jest-get-type@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "39e6d3bd24c2793e12a261b015e337e8b4cf0305", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-0.0.0.tgz", "integrity": "sha512-/3L7QciVZ7iHOgknoaNrddTxzjCT2CahL1Cvtotldm4QngDVI0MHPiuNBkcT6w866+975LNJUPcLwzoPFWjR3g==", "signatures": [{"sig": "MEYCIQDJc8SosCpISlqoyMQZayWdrArT2PRKT26tUOqwkC0jrwIhAPEUEI298va/pKtQ82BDqIF7qOkie93alF1VNMyUNovH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "39e6d3bd24c2793e12a261b015e337e8b4cf0305", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-0.0.0.tgz_1494594153865_0.5632392852567136", "host": "packages-18-east.internal.npmjs.com"}}, "20.1.0-alpha.1": {"name": "jest-get-type", "version": "20.1.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c69d131cc728b0ec75f8c917857788c5ff0e51af", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-alpha.1.tgz", "integrity": "sha512-rymcdnZTAvaT/z2Xj/yym8D6f/7OB9lL7AkLyadS7Jo/cdQnqYgAHOhmkdedqwhfdY6EQh9C7f7ggDOg0MXjjQ==", "signatures": [{"sig": "MEUCIQDEIZkLUdCfobb4cpnwlQCHdWeYyXAfNhTYBkc9B7nBMgIgD0JumFPro5xoAaclvrxtxkNP1bTwKttk/nJ1Zu7F98o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-alpha.1.tgz_1498644976884_0.467373906634748", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.2": {"name": "jest-get-type", "version": "20.1.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f2c137f09176788bc4ce0eca24e819daae4b6076", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-alpha.2.tgz", "integrity": "sha512-yhs8jCp3PVes33O/Xt2z7ayftyni6JS51PQpjFv19fkimlLblWqO1jGOm6pm17HLP4MAC/hMSGiiBaYeydbQdw==", "signatures": [{"sig": "MEUCIDvAcvnkMpckoFwHF3BAca/k9sD3YfZqf4cDpc7H4B29AiEAq+tA4+/XmgtfyLEz99mdrZfdzvq1MbEwfDadB1povoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-alpha.2.tgz_1498754203577_0.4748610593378544", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.3": {"name": "jest-get-type", "version": "20.1.0-alpha.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd0269156ed55e56bcb83e08b529b1f5a9e002b0", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-alpha.3.tgz", "integrity": "sha512-++aX3htlYHHa8+Y2+b7Q+U68nIkay9msuqZcT8YW2DiR+Efw/SDMZHQXTiEtU5x8UlqsYjtsGpvr6OTtiCTBvw==", "signatures": [{"sig": "MEUCIQDO5mLmRKA4k7Rj/RZLYWrEzWsHKS4P5X8dPJl5ugUMFQIgcYi3dtcUHFhi6xZMama4lLVX3YY/kNwS+ZhKk+e6Nrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-alpha.3.tgz_1498832449897_0.0831775285769254", "host": "s3://npm-registry-packages"}}, "20.1.0-beta.1": {"name": "jest-get-type", "version": "20.1.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0554af9cfc89f98cce6b32701fe213ac00db0628", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-beta.1.tgz", "integrity": "sha512-wuezZkPXxSogKOLP3U2HZov21agJoSj2NHRnh8kaJZWotaK4k++DMIl1J/F1l77N2pXeP5NFjlQ2q5Zri5t4Gg==", "signatures": [{"sig": "MEYCIQDMfH40H9hNyW0A3qLEfauGGJtmXefXgNhRlgB67ml4ZgIhAPBoSZRhzP/x4RMGG7pFlWyPRDY/NiT8AHseGoOJtGDo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-beta.1.tgz_1499942017839_0.07454773737117648", "host": "s3://npm-registry-packages"}}, "20.1.0-chi.1": {"name": "jest-get-type", "version": "20.1.0-chi.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-chi.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b6a9a01ea43a694b43f8cc774cde79b05aa61ed", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-chi.1.tgz", "integrity": "sha512-fUbeWZxspCKW2mIKISgNdviUZ8/Jm6CIyDnKGZ7Rjc10oVp/VfLzQnmo0YRCitrFiZ3sglzV/0VlnMDrE5xjlg==", "signatures": [{"sig": "MEYCIQDlvnTYZmwG+GOTjBo/HwHCaetezkhq0sD4oUxQhBfDhwIhAIIDmbJqtB/57HrxsCP8ijnO9K8jZRLD1NYQL8jHGmB3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-chi.1.tgz_1500027899639_0.08338822051882744", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.1": {"name": "jest-get-type", "version": "20.1.0-delta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-delta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f441f9e9b5305657dccf5e1c035b3c91bb166aec", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-delta.1.tgz", "integrity": "sha512-RoR1mlsxJZPlKHOdaUhwHU0POQnaQoJ13SKFHDtoTvrwW1WBP49F+cCOnF3zfING05Gv1Yx4hnR4IvSfi9m6QA==", "signatures": [{"sig": "MEUCIAdN9O7Gg0HYscUwYVVywbMT2dnnm5JWkXRJhkrAbFswAiEA3XlYI8Qp+ccQfwbaNbO0anpOtrlC86F+p2miXtQwvy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-delta.1.tgz_1500367610018_0.3292269301600754", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.2": {"name": "jest-get-type", "version": "20.1.0-delta.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-delta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3eeceba2bc8a5064901acb291887dc6acc0853d8", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-delta.2.tgz", "integrity": "sha512-9XfUvW5AbpiAtlGl90HGqwFxUa+0CM0EsmJy9MHMV53A/6ff1HeoWqL16063o9XYdeyFtuiDOTvEywyXR2Zsog==", "signatures": [{"sig": "MEUCIQCwJ3anBdXTZBUSPfbw59+bWZvnuGZp6bmlSEB3BEVlogIgZOrmaO9zSU6PewSSWYABWD8z+cOUFDciKkt2487XDd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-delta.2.tgz_1500468999404_0.6547959654126316", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.3": {"name": "jest-get-type", "version": "20.1.0-delta.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-delta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b1e0ecd3b9ebfe7d670d73be198a548457f6f1b", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-delta.3.tgz", "integrity": "sha512-VbLCsQwlYNjhscqD8NUlaHfcVVXEECIKpzkhnVXEp875PmXIWIJSv9Ntcm0RLJt7PcyKycYw7QUZJHDPK5mE3Q==", "signatures": [{"sig": "MEYCIQDylk0KxxuRrueAZck+qfsyo0VnPsFii6dTW5xW2akLxwIhAJHkJdAAno/grE3PDFxjff+++H1AYDcpIMqpuHZray2m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-delta.3.tgz_1501020743355_0.7847992561291903", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.4": {"name": "jest-get-type", "version": "20.1.0-delta.4", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-delta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79a47a67367aef57d062d68b6daeceb55f3d1511", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-delta.4.tgz", "integrity": "sha512-i0A5kpUl+De9WKFTDjwouLwOGhD9Ya6VBCr2OWpzNt8/B3GTbPnnUqa5wFwgbY2aBWplyNaEq8D6pCcyfkPPBQ==", "signatures": [{"sig": "MEUCICx/gOE/dtPdbAC5n7L7jKKPQ4EAjri5NpZ5ot3GgolLAiEA77JOs/lA3qMyNousWbA4H4HJSjIG63BwicZvojh/cCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-delta.4.tgz_1501175946130_0.7409679836127907", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.5": {"name": "jest-get-type", "version": "20.1.0-delta.5", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-delta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a6a0c38e2eb4da3082a4a0175ae0c4a18865c91", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-delta.5.tgz", "integrity": "sha512-QryUZnHzcDPrTaiiDfWk0Moqcsg5EfjfxOiaY7FNldgwPb9LeG46rU8EnJI0xCd9Fy6zQ7mWeJM9SkOS4/BuBg==", "signatures": [{"sig": "MEQCICWrKsfrdeLleIfLTgzptsIX5+J9Q5d9Kq7pLLXG1GGlAiBJxVqiM1zXurRPFaeMI8ReAr8AEZZcZ+Nn23FWx86YPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-delta.5.tgz_1501605215138_0.3403152320533991", "host": "s3://npm-registry-packages"}}, "20.1.0-echo.1": {"name": "jest-get-type", "version": "20.1.0-echo.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@20.1.0-echo.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "00e3a07d5feffc76236488aa4445bd18e66e71ac", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-20.1.0-echo.1.tgz", "integrity": "sha512-v8YszGe05oNAr8sTBfuzoIlgUnT99YXYQYkNqevlXmaUEe0dOI33FBJMBat6H2mQQJ+VWmE+s65y5UqK9BAtcQ==", "signatures": [{"sig": "MEUCIFOg1e3OPWOEjmJtw6TMiF5XwOQfuu3BnksASyz+FqPwAiEA9PYBx1oNFF+O/RcwI38t7V1Bf1Qw+I9y7odXJ867gik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-20.1.0-echo.1.tgz_1502210988529_0.11973287118598819", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.1": {"name": "jest-get-type", "version": "21.0.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@21.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fa93ce68d73abb49b2ea376cb9147f191e1a7b59", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.0.0-alpha.1.tgz", "integrity": "sha512-BrmcdFUf7pcaQoeiiHHmdzQyzZKE48zU0yFR4qECaYqyv7gzwxwLZ/BZqJEEmjJCzZ0MPqvv5Cikpf4tKSRhwQ==", "signatures": [{"sig": "MEUCIQDP8orI5hrwLiIg+AWLqLX+OrcsM2g3jGcojESJUrPj9gIgU8qvjw1K8zoN4JqnZd9dQlRowVrEWROSt9CLII6OU8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.0.0-alpha.1.tgz_1502446440600_0.4013558221049607", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.2": {"name": "jest-get-type", "version": "21.0.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@21.0.0-alpha.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "96753e5b3dfb8bb87527487dc022996f2781dec5", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.0.0-alpha.2.tgz", "integrity": "sha512-XxxcyDp9Euq5rt20u8U7YOu+iCTXJjC8ZNvMrHQjRViDst1bhxxV0h4/XxlgRGbE5Z6bgMjtgRj+V31s31B5dQ==", "signatures": [{"sig": "MEYCIQD+4xUcecQhPnhf5wJQ6y/FEMeCd587jimtkURBEX71MgIhAMQCExbkF9rNTBbud3F/l2N5AHW8/zKrvvf9IDuTPT5C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.0.0-alpha.2.tgz_1503353207400_0.8220509667880833", "host": "s3://npm-registry-packages"}}, "21.0.0-beta.1": {"name": "jest-get-type", "version": "21.0.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@21.0.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "289efdbc280298f47f99cdfd15fe4290b2d19124", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.0.0-beta.1.tgz", "integrity": "sha512-kaGzcicN2Xy8vkYSftokeaVk+7PDN0FeKfehqA+4BwMpMy/O0XXNangMjpmjwaW497tB7r+QKSQAkIZnczxILw==", "signatures": [{"sig": "MEUCIQC94SwPt6fSZqapOdCbHMF3+kYU6pS6L78OmsY75BpqSwIgQnI4Fzms6o+q65LDyP9sr7YdMFmZUNc0QIiX5CfPDpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.0.0-beta.1.tgz_1503610002383_0.9514673892408609", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "jest-get-type", "version": "21.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@21.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed8667533c0a24a4feebbf492661f23abac3620b", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.0.0.tgz", "integrity": "sha512-JE2NylM4fkuEI3W4n8+lcebJ/Fk6+msny4JgRP85I0LUlNqqs7lv19YTwbP6h3YLh7nVITndvVDixZyI/AZjQw==", "signatures": [{"sig": "MEUCIQD7y/6LQRT4KHOHIEZTn35fb7RJ3S9wqLy3ok9n9RNYJgIgAthWjjE72I203dub+vyIA+dO+eb9t5GCVbZdqqvMrJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.0.0.tgz_1504537305677_0.2963071665726602", "host": "s3://npm-registry-packages"}}, "21.0.2": {"name": "jest-get-type", "version": "21.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-get-type@21.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "304e6b816dd33cd1f47aba0597bcad258a509fc6", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.0.2.tgz", "integrity": "sha512-4KvNzzXMXeapGaMWd+SL5e47zcMn8KTWjom6Fl3avxVXnbKS7abD1p4xWe4ToAZfgNoYNsQ9Av/mnWMnZK/Z4A==", "signatures": [{"sig": "MEYCIQCSJm1Xwnc/1ruBbi+f1pcw4SvI440Rzh25YYR61vqRHAIhAPigc/lQtVJKRMqEGwMUBsELsSqvhIPMoztO9VX5On1v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.0.2.tgz_1504880346667_0.5234604054130614", "host": "s3://npm-registry-packages"}}, "21.2.0": {"name": "jest-get-type", "version": "21.2.0", "license": "MIT", "_id": "jest-get-type@21.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f6376ab9db4b60d81e39f30749c6c466f40d4a23", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.2.0.tgz", "integrity": "sha512-y2fFw3C+D0yjNSDp7ab1kcd6NUYfy3waPTlD8yWkAtiocJdBRQqNoRqVfMNxgj+IjT0V5cBIHJO0z9vuSSZ43Q==", "signatures": [{"sig": "MEQCICysCFRKOlF2ZPymSmZ2LfPVZhLfVuloUMaYEGM8bGJyAiAyVgg/fqA4JHCmdai4Est9YMmM140jeevrrOQBbG/rfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.2.0.tgz_1506457328263_0.1574959980789572", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.1e3ee68e": {"name": "jest-get-type", "version": "21.3.0-alpha.1e3ee68e", "license": "MIT", "_id": "jest-get-type@21.3.0-alpha.1e3ee68e", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c2563a79996b3ea5038ebeaac2cf07cb36295666", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-alpha.1e3ee68e.tgz", "integrity": "sha512-/Y0OKh4fpFBWehH6psUF8S8BUSTzU5Cf9AS9wfu/2nvefxt8FDGtjAr7lDypUu5AuBpe0sL9hY1aOyNBPnuhRw==", "signatures": [{"sig": "MEUCIC6UDxRhAniSc8STNrn6wt2oBUQhEvrgIGBoP/sxx5MiAiEAsylaZ/Blbs0Bq0HhoaRPnP5ZHzY7V0RUvP5y8QNaAf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-alpha.1e3ee68e.tgz_1506608430538_0.8342910283245146", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.eff7a1cf": {"name": "jest-get-type", "version": "21.3.0-alpha.eff7a1cf", "license": "MIT", "_id": "jest-get-type@21.3.0-alpha.eff7a1cf", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aad658d1da023777edc120daa7ed23336519495f", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-alpha.eff7a1cf.tgz", "integrity": "sha512-kKZDPy5M0gPkvhYbkEPO5PBloNlJIMRNlJa8SmkFuIQBNr57m+I+pWjLIiNn8mtn0mW8qKizUeFtaB/ZAfR7NQ==", "signatures": [{"sig": "MEUCIDVVeTTKr2LeZgQPOfiuWkKJOpaaXS6yBJwQuneVhsd5AiEA6PGHyJdUdRldyxS7rI4kvkCu6WCfXAGbebov8FT2qAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-alpha.eff7a1cf.tgz_1506876403740_0.2237401190213859", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.2": {"name": "jest-get-type", "version": "21.3.0-beta.2", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5151fe909ad2c86a7ffb32ed403747bd1a6a2242", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.2.tgz", "integrity": "sha512-GvypUjqq8HOvJEb3XqlCGSFwZoKo2x2Pri6RQEYGRMTH9r5j+S+h7KCmt0KztFv/agmDbRvOOyOp7Q55A0n6wg==", "signatures": [{"sig": "MEQCIHDj1+WnGG6+ONvj1BS5p+gHbzydI1jxQ3ZI1eWWB3FjAiBSj3x9EcYNySD23gMDTx+HEH6d1GHrtRDy0tC6Bf2SSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.2.tgz_1507888440013_0.2825300479307771", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.3": {"name": "jest-get-type", "version": "21.3.0-beta.3", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3705a95fbcb7117024d140346a6880059f7ae26d", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.3.tgz", "integrity": "sha512-7wW4b/MCMuqbhIStA4F7DOc0e0bfNAINqexvW4tPLvCPY3v6EeBG20bVKKgYL+0FD9OXIEgnILjtsugi+W90EA==", "signatures": [{"sig": "MEQCIFdlZemoyu0n5j1KPGM2Z+jIZ39+TpDugHRXW6WBu7GHAiAK0h4oUw7GQYGh/8QY5SV/Pn7mvR5wsSEdrnr3XFCWfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.3.tgz_1508960034970_0.9787847783882171", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.4": {"name": "jest-get-type", "version": "21.3.0-beta.4", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.4", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "248b16f9b8fe2887a0baa35b31905d73d66e56e0", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.4.tgz", "integrity": "sha512-m2oMc5O+/9jYKxB2DzECQNgD9fE7Fx+NnC/p2fzIIizyxvsW2Qk5ujRpXr6dqY1i2E19vCtJ8xX2V7MNoDLeng==", "signatures": [{"sig": "MEYCIQDFog4/G+q6YuMNSI1j75Xvs/24qAL3VnqdwWEhVN43twIhAIbuHTBSB+AGyU954PS5X84WL1a1ArZu+rcKzoswGLbi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.4.tgz_1509024406812_0.5775932497344911", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.5": {"name": "jest-get-type", "version": "21.3.0-beta.5", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c7a21172a5907852ca7885f51d68c94b468b87c0", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.5.tgz", "integrity": "sha512-rzyy1YYSqbGV6dB2HzHJdBLHAvJXOoylXabIPSjblesaskyjySvJIdjK8JlFRhISOHq/zBdZLsKgxb2rGnWcvQ==", "signatures": [{"sig": "MEUCIHwaVDnJ4KaiO8BsdnnnXitHY7vGQIQaSNEd/3CfAwPfAiEA0yQosJ4/0llcLxdOiA0QYyjHzeSzXlirYsg7lbX4wwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.5.tgz_1509628641534_0.35371020482853055", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.6": {"name": "jest-get-type", "version": "21.3.0-beta.6", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a53281938aaeaa122263203c61c98a3f066cdc3b", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.6.tgz", "integrity": "sha512-+wUA8JdnqL0toadmzwt1SaVpKoSmFBi9P5jfIUcUE7ob5Qy1/ZLXRcbx5dFbYIzA9RxlGfwfuBdT3VpNIeNU3A==", "signatures": [{"sig": "MEUCIQCAHrPLSpY9LcSZO2fU2N/I1zmcK4zU1OY60W6OaBksiwIgHYkaMwzbQIXVsd+VqrhKNyrdNwCn7H4yd1LlCKoVQmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.6.tgz_1509726081612_0.04474962269887328", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.7": {"name": "jest-get-type", "version": "21.3.0-beta.7", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.7", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5fb78280507b512294bedf19a677f84690f0851a", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.7.tgz", "integrity": "sha512-kkRlszTlAJqH/VjPpKwMnulDNqmGQvUvHPK7Lys+um8ao+rVVM5DQRk3MyUVqU/KpoTdt2qFvuvHLFufFeMXlg==", "signatures": [{"sig": "MEYCIQCFGvpa/DoNqe93qyI9fdIexESjuFOkuqViTWtnZycGZwIhAMoFrSj0TaUlpjQsZi/4B10lClOpVUI09oyRGVJPjhSk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.7.tgz_1509961179130_0.43764369282871485", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.8": {"name": "jest-get-type", "version": "21.3.0-beta.8", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.8", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2bfa715b0cd2871eb305f6690be5ea9b0b38957", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.8.tgz", "integrity": "sha512-zSwz9PwuItNhsrFFEkIv1tBvfSIXqFeRq6QQc/NaQe4XhTVmPqf1xJmNhe38piLz6prc0GsxY2iFNp+KASMk9w==", "signatures": [{"sig": "MEQCIDaissdQKQdO72JuWgna+aYIziowQ+uh+yhRoBZzAAH7AiAocDurnHa/i58cGwlCz3EBKCDKxMzPTNmaILZ7HXxPig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.8.tgz_1510076607728_0.6922241249121726", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "jest-get-type", "version": "21.3.0-beta.9", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.9", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8e410119efa0b133ff14328c7011f30e3f5bfae2", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.9.tgz", "integrity": "sha512-hKPhCsi48YZ8NQd4xNn7sFYVsd8RPgQv4G+dnfB4OUTc3GvM2g6bl7RvjkQm3R8zK057xxGhoU6wlKnNZgGDRQ==", "signatures": [{"sig": "MEQCICkA+/Fxx9wOkcPUMfnhIe3uVEItfxhbm2q+9VSivbmtAiA47VunIHfMq28CzP7hz0YrsecQuCRBkkIpnn0hUP6r1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.9.tgz_1511356646483_0.5110146526712924", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "jest-get-type", "version": "21.3.0-beta.10", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.10", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9bc33302011794e7fb8b227accbfe332b8187a1e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.10.tgz", "integrity": "sha512-RCi1hIbSj64kjRaHV1JUn3HEUFVdkOgLPYNw3imF98MAo8lL9NLO7btoF1XS59mkfeUkfMF8T694rQCtWw4uhA==", "signatures": [{"sig": "MEQCICMxg355FR2tGjZhs5Wl1PZY080tGbkdJJ9bFrvwTbKqAiBduhQvqz02gLS2yyNJSwRmmxXRSQ+w1/v71/a1myYyCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.10.tgz_1511613560117_0.8951200933661312", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "jest-get-type", "version": "21.3.0-beta.11", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.11", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b5161dcf779ed829fed2e85459483b07936a69d7", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.11.tgz", "integrity": "sha512-LA/qJEjde89xuIHE4uhg+iaH7WCSLyIhht2Wk/5egMMJlf/fCBfeXVMsTsvs7aSBEmCFjWdwsM6gw3iGLX6HMw==", "signatures": [{"sig": "MEUCIDYhJaEIDFlJHjjYhQbN5u+ogeBZt9RW7dpTTeLluZY2AiEApZFz+TNh8U7VkZkZf0NBPbqWVzYKqKIgI0eLMjFmCf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.11.tgz_1511965873997_0.27472915360704064", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "jest-get-type", "version": "21.3.0-beta.12", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.12", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1c07122312935e0c3f13a97ca6c72202f09d85d5", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.12.tgz", "integrity": "sha512-s3PcOyHBBKLOkdPBf/Jm6Hxq0DBeYLcEXCJ6A7GSBfdLJA1IpRsUWW6i1W7DktQLKxn0Qz18LPhVzSHmqQSlJg==", "signatures": [{"sig": "MEYCIQCq+IuE6D53zRSoL2f3iyA2kmgAn1kooCkYUiu3xRZaZgIhAP4LSbdbJd1uXnJzpxZXngwAxSukHHCVDfXY/pZXY4wG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.12.tgz_1512499709930_0.4886043919250369", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "jest-get-type", "version": "21.3.0-beta.13", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.13", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "87ded7850d76e152f0efd8cffc2f84720bdb33d4", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.13.tgz", "integrity": "sha512-2n5NUANtgqpKdN1crEl52OztxVobbUpM21gAIH8l6N9Ru33JhJ3cC8b/ln4CitbBZV5AyMzcAxSCDSo2ZmX0cA==", "signatures": [{"sig": "MEYCIQDn5ojDVW4NEvraCiBgqRyNraBOAdQZDimJdnttW/xj5AIhAOoo2FTXfcq0iw1g7mcEucdZFp0X6FsWbZ/1pF4tuUZ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.13.tgz_1512571022969_0.4012686333153397", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "jest-get-type", "version": "21.3.0-beta.14", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.14", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "53ee556f10beff0d3739e229f675cb99fae320cc", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.14.tgz", "integrity": "sha512-afktohht/gixMefRSdvj05opiSV/OjCDxQqvBIqc57fHG+Vb3NeLwtQzMIJJZCcbzJI1KRN3pGIRC2K4Rowdeg==", "signatures": [{"sig": "MEUCIQCWmh0SUC+i8+qPuLDTX/xwj6UIs8Jba6EHhkoGt7G9bgIgDVXU5L9mu35es995xVc4ARk9ytb5GgMnI8nJSXNIumk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.14.tgz_1513075946049_0.5434459894895554", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "jest-get-type", "version": "21.3.0-beta.15", "license": "MIT", "_id": "jest-get-type@21.3.0-beta.15", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d5a510c32683124576eaba69b66a1ba3c25ed67c", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-21.3.0-beta.15.tgz", "integrity": "sha512-bsH8LKb298cy5fvrZzZWspyRoFdKcJ4YnxmZ+akDbpFB8qa4llpWMLjGjTr1AmaWf3ytcZHesczuypsV0I+LaA==", "signatures": [{"sig": "MEUCIQCDKzCAdoDjJ06q+vycwObLJc5Bag6LmJ6Z1oGLYEvG0AIgSq2g4Nd5y5cVgIdfOLoLm7uktpLGi4rRXLH78GZn0po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-21.3.0-beta.15.tgz_1513344451043_0.06264941324479878", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "jest-get-type", "version": "22.0.0", "license": "MIT", "_id": "jest-get-type@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d49b734cb91b66204cf6cb726d81c64c77684b1c", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.0.0.tgz", "integrity": "sha512-7H/WlU3ytbn7UDFjwVIYg4vOj5+/VBtLlpTR91HW3Zs/kC2AJPdETX/AmmLmUcueYTMY0j25fRlmujB24KEQVA==", "signatures": [{"sig": "MEYCIQCQRRizqZr9J0UWDlTi7MauybkBLi+RnKlT1Mn2XrYtywIhAIOOqmZzliskJ8kA5bu6FfMWsCNCLpHxMu+oohn/1WGy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-22.0.0.tgz_1513594999089_0.9674622402526438", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "jest-get-type", "version": "22.0.1", "license": "MIT", "_id": "jest-get-type@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b80757b67dd5abc165290d039937175255e9a8e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.0.1.tgz", "integrity": "sha512-Xshee+V9LoVrvVlrS6raLvHYJOcdbmC20syU2/8DzQTU9C7rezJ19KE6EliFWBpSCB3/O5VHUNx3fV0WRszcnA==", "signatures": [{"sig": "MEUCIDL+JSd8mWRKHGRUX5BlpWaUkXDaGzxUPz1H6WlxpQRaAiEAiYXoShYB7X+ulwHRD9oB0YlmZXiRBiFGG3acqCQo/zU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-22.0.1.tgz_1513628958799_0.6871452443301678", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "jest-get-type", "version": "22.0.2", "license": "MIT", "_id": "jest-get-type@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cabfe43804d1158512c03c9e49e52baa03b4f12c", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.0.2.tgz", "integrity": "sha512-o7pFyCPzGjuUb4sGHoNBKmUL8DO5LhApk4G9FUqhqMlxgA5H8I6eOTF+2JkgkRIaHClxR8QGfmORKmEA7g5BKw==", "signatures": [{"sig": "MEYCIQCpEzZOi+GR6310Y0UguDSWYiLYaOwNm4G3sJz0iAqipgIhAI79lQB1H+o3j21k2Ws1KqPmGTL9hh9ImnA+fiXbUCrV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-22.0.2.tgz_1513691580790_0.058256002608686686", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "jest-get-type", "version": "22.0.3", "license": "MIT", "_id": "jest-get-type@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fa894b677c0fcd55eff3fd8ee28c7be942e32d36", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.0.3.tgz", "integrity": "sha512-TaJnc/lnJQ3jwry+NUWkqaJmKrM/Ut3XdK89HfiqdI3DMRLd6Zb4wyKjwuNP37MEQqlNg0YWH4sbBR8D4exjCA==", "signatures": [{"sig": "MEUCIQDFWCiPu+c6tBWfpCQAdvjWtQQX4yAJ1HpnKFsbRoXc+wIgH/q98k22bHS52i7K+DCFkZdUqozxcpMEHkdffFx7bbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-22.0.3.tgz_1513695529369_0.24661522265523672", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "jest-get-type", "version": "22.0.6", "license": "MIT", "_id": "jest-get-type@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "301fbc0760779fdbad37b6e3239a3c1811aa75cb", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.0.6.tgz", "integrity": "sha512-NGiuzfSXssBIN5PGAmKPWk29/eJlv5gVhD7un9Dcr9w+rJXAOGR/Ggx3HKJ7IArkIq0YCN4lFdbyHpB42NMUWw==", "signatures": [{"sig": "MEUCIQCkNxDWpC0Y8iKvzZ5DAJoavQ7psiAZf3Rk1M0127t+KwIgIdFXkYHu6Qm7nAQ5811YdpZ30Uu3W3RXfbzC6Tx8WGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.3.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-22.0.6.tgz_1515663998793_0.0711614212486893", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "jest-get-type", "version": "22.1.0", "license": "MIT", "_id": "jest-get-type@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e90af298ed6181edc85d2da500dbd2753e0d5a9", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.1.0.tgz", "integrity": "sha512-nD97IVOlNP6fjIN5i7j5XRH+hFsHL7VlauBbzRvueaaUe70uohrkz7pL/N8lx/IAwZRTJ//wOdVgh85OgM7g3w==", "signatures": [{"sig": "MEYCIQC2kST1w4RM75LyqLfKzRsIgU5ldaXe7Ly/Qao99Y0F1wIhAOiNggBc7Z/mmZio3V9UElOPwjg1efL8fTE09AMQbaBh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "9.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-get-type-22.1.0.tgz_1516017426671_0.8946955762803555", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "jest-get-type", "version": "22.4.3", "license": "MIT", "_id": "jest-get-type@22.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e3a8504d8479342dd4420236b322869f18900ce4", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.4.3.tgz", "fileCount": 2, "integrity": "sha512-/jsz0Y+V29w1chdXVygEKSz2nBoHoYqNShPe+QgxSNjAuP1i8+k4LbQNrfoliKej0P45sivkSCh7yiD6ubHS3w==", "signatures": [{"sig": "MEUCIQDNFisd4kYQJry8cnDwgE084VIGt860OL52/JXO/OgGNQIgBys03kwjkzqIRceWM3QLcT9hWpVYaCNLyy4jQGV0PTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1565}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_22.4.3_1521648483887_0.4171269777207698", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-get-type", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c075e9c098acfc69fd8530eadd48420f51e63824", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.0.tgz", "fileCount": 3, "integrity": "sha512-2K3aMn7akfFR8kXu5jMGR2AwTdhnAsz09PG3QD/fJs13LKLKdx92Dn0BijDOUXzlVJQsQYrJWPBXL4W93NsJjQ==", "signatures": [{"sig": "MEUCIQD8ix5676sZX6kkPf/HgBqBj3lTPtKSSrxEjXqLjlqY9wIgeFoVjT/q25GJ3SX4HELmkY/7HnN2bF6Z6fPl/2rF+S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycouCRA9TVsSAnZWagAAxesP/1wTxyDEuLMuOj5Pexoo\nrlNTxjjMMbSMiz92TpCjE/rFkAwqpfjoTCAPsFtabaCE9pIf/6jltd6a8sKo\n2jFWxYZBd+ciwVG7k9QDoyQbmGo1E+RicpFoxOOCE0crxrTh2OJ+gWDiwhMW\ngQCXraMDJIwbexiIgdHSlyAANe++/s1KWJSIscfl1S39STTSOjMv/lp4nOH4\nWQhSppla5y9OrqIZwwyBDm+UpxJNw53Dp02EN66AerhuWTaUlQUPzyuXTl6a\noEb3mFgsZaLoK/yr66ZCs6sSFMIytf3KADBcQO/8OZY2XwbC3Gl0Rw9TjxDO\nE0UvXxahSemLWfVVVEpvesX2g+6Vc7fTlIOcPf/yYqWei127fO0m8ikz/B19\nMw5OIOEJZWVBj1v5mbnO0UhzFAUlk1F8z5Tbn27ykkO8OFPrrn1rgxjccKYZ\n5kT2zh4LK0COxPQsfK/fYMHfa3vri/9Zg2PkZBxnKUzzDoKakMx2rYObVE0n\naBFrmCzKV1JlSgYPsh6kxPfruYzgjiduQyZKbU/WWOH4RhVUjn4gIQYkWFb7\nVHHAWLM74w/weHff/LHDEjqAtTZrzjlu7aURtvGt74GAEx0WjrseGKAM+IRF\naVJJezlmqX8LjkokRnPThHyGxuaaaGsO04pQPuQh+yQykBg+N4Le+G9ZOEE+\nCXUW\r\n=jKRu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.0_1539951149680_0.7009928994164285", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-get-type", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c36bcdc322ecc95b2a06afd25f1887f00c1a723f", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.1.tgz", "fileCount": 3, "integrity": "sha512-frmfCGS5d7OwJF2TU7/d3PDM41L50lpQkLtPJqMWlSNh9sr3ng5y4MtumwUIf+tuuJUG6kltnTQa8k3GRPa7jg==", "signatures": [{"sig": "MEMCIHgokqC51sif9rWrRySeFECXOlkKQEbsh2667Kz1pzSzAh9hnlOoLHjInObeYJW2EFFndP7i0UpinlGmGGYMDH5K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5JCRA9TVsSAnZWagAAXHsP/is4+H8Yh5oPExAV1JQM\nU4kXYYFO+PpJpFOUkuigmCs4V63sXa661InGxW1oRWTOCr1VIm1iC5kK34mx\n0oZrSwqCYc6YgIj27MA0w6vYUWeqT7M2NKKgn2y0xlbpIFBEeCQJgNabndHE\n00fo2sBrwwymLrWK4FOKGY2oT4y7pfyCD7ypiLs9e5VWidhyRG23tAgCKtRa\n9bJVUcrOqp/yv2OCgOcqrBGcZccc8yYnKkCfmL2I98nVVINSAKLQ0bptE4T3\n3r64c/c2haHnILPT6OsHd2YFlrkH2w9UTLEYCOoX9I/l0SCkxafNE3r4xVZ/\n45Q8TONRtsMSqeOLOQKBvSp82h/nxdjp1kcz7BIj+A26PhVjSaMTHL8lyXOL\nFBgughkaHBcIoAsjbpg+2gK0q73RrPLIrZCAhLqsGdr2ZfgMSOraT/EPKMzm\nHLlKgsoCd96M1IM77Zk8YMP3RnsxM3BEI6EcSiDJNYCJuzcv640As7DZ1h/r\nAfVjDD4CCVkrpNCFTKGI+OxfCph5S1hunu6mXj5UExVxzhYyL2HHTnITs6jy\nHPK0UekEuC4tHMhlfR6LZrMCEh5a6arGI2lCShNmgdgnV+yBblOVxp1kk9qH\n8anu2x3xchfYse+0mPcnr4xdI+m1K6nm81v7NTJNhXkKtw1DuFhUDnzwztNr\n6iip\r\n=av0+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.1_1540222536677_0.6962949418076139", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-get-type", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "458f787989bc678107b5923dc862d36269b45d0c", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-LFVJsgKfBCnxlwJNtFNwn4BRWsCgszlae86a53viD0swWFNiG431BmNz1xaBKe2VWxXd18jewnowkxtxpulg5w==", "signatures": [{"sig": "MEUCIDEETmW2DYKg88zQDKOl8GSYDeWTEu87h5Y6VrsYRS1DAiEAj5taBUm+ylMC59SsJE8bnGoVau3UdZwqbpWldHYGvdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aARCRA9TVsSAnZWagAALU0P/2BQm+BYEyyBOqyFI/sQ\nvOuECDZgUSXIBVj6NKfnjsV2UbLsZfiErzrIwaKH7r9+g34ygFKuL/ns0zfc\nJe5ouH+McWdVZ4ExlMP0lA0wwyzGcOPcMrPSbn+7LBpV1jNZNXhsORrUPALe\n1EukM5/23qSz/HI5eatlr7qsnXSKX+CidmTHCy3qwac+eckJW3dX+YhEaeN+\njRcpN3rf98uA2rc2Jd+oICy5w2YDGwkioreGYldhSw4+VOOisQw26+q5aIKA\nE4+CZBdcvuW9bw5lym6pkCbeny5D4eTZutOiyoLZqWW5nyFxGhenzRWsW9IM\n1f1AYTri11EM8K8n54rPCBWdhWv15L2NOArNs2Pm3O1yj6TEXasroVbzvSJU\nY6MNfoaxqO/h9uAP/mpl7PHJ6H96lYfEqywiH/5grWAf/mljH6TI4KxBKSKM\nj4OSpZeTLvGvsaYV1Dxl8ZKPyfO4UQq0OHxgCVUA3fjWqA+nV2qgqT/HMzWq\nVeXDhlk9lzg2hco21u8WmxoNDHApEDRHQCPyMOYPfZe1lH1cYqPIjlrc02e4\nvhl185nTBLKy0tN73/cwOJO7X56vJ2A2vzvMvuP9NiwTdI7tMAdi8GHn77lW\neK2risLiiIb3LMWQg3CNAe2TEMg91Ap2nVLb1FGX0fGzCW2KT8XKPcXOQWai\nhv82\r\n=c9FD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.2_1540464656888_0.3469129616775646", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-get-type", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2cef0446d2777229c0c2fa327d0da50507adc037", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.4.tgz", "fileCount": 3, "integrity": "sha512-MCjKloodDPKLKCJ9yQ72n+PtJtwSAUG5JRBeSWEXF7DEO2GqwYW/nY94UdmEbug0oc+oxNzsAxNMy7nluWS/Jw==", "signatures": [{"sig": "MEYCIQDRokkYapVlWgjoiKMgAEd8oKLiFE/7OrN9zz/4KAufugIhANY6nUQfFu2if5zYfeVM4To/YHyKHyRzt51N90L9olad", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00G/CRA9TVsSAnZWagAASPsQAIf8HFWR+6EDjyd4Xp6k\nOMnI1/9jdO534BEiL0s5Z0tqnTAljCmkWSJOCvUj41Kl6FgqZRqWe7yDWWns\nChX1SyrZA3euDLZXgqUZDEKUx4rdIGfAUf2dGiYwbLO6t2Of7BiqYEOYrj7Y\ntdOWYsgB4dOm58wa7EOG8pBHwWWA+7BxI13+SFBTkUvSc8PVcRflLJetbaP7\n64jqXnPzjQH9p9dr9Zx0T+F9Rb0Jfpv7P6PH2+q6E43NA7jZK7dTGTYb7kC5\nuL1bm8bFDuMXPqKjXZbuQvCVOAK0FKJcg90EHOjqqUWUxk5iyMtM0ADkUOaO\neyDyuz/TKcEVFMgUwA3IUoUg1GIK4/H6iy/9qP8CN39KWzRxgJG0IpTBFKa7\nvUMlu5ZYwp0BJ5df0zwANsVylQOJLFZ+V2thgBv27bzeYKIaeZhO9ABJTzPx\nTpsC/tSPeWrEjRUWfe2KPgyWAPlBhbSbEIRiSeco+rUwMDHBo3SmFyzkFJGR\nBg++L97uzHbt7Q2ndyXvdvsxVcFDxbq2C3yH5jiibfnmha+nrPqCDS75cpJZ\npzkfxfGP96IcTzGEYC7LzvBG/AuaS1ahO82E99mSPjT2dsxaRQ1xBpJHJSrs\n5Cbl4sGyFjL5jprJTVTwI49TS/S1dxapAzy8s/mq4GMTwQ0OgSNPJyH4/o5s\nf4K8\r\n=sXL5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.4_1540571583190_0.25594206159909083", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-get-type", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "12cd5631505d1cd6e94135a314043eabe4ac20e5", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.5.tgz", "fileCount": 3, "integrity": "sha512-7qiiUiNqnnbqn03QrwT8jZEMCcw30RdXzHgAE4Mt9fKILI/eYg/DKXuTFo8oooMpqNF5fz/Fonk+Mx5H5b4lGg==", "signatures": [{"sig": "MEUCIB0MOvh32dSdPkJB9AfvN3ZNLKOpR6Oy2qKn6Yem9+A7AiEA/UBo6gYjCDYvds0K72mB6EBg0y6DcsT76G+CP1Br7Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfCCRA9TVsSAnZWagAAULMP/2qgwqAkpF2V2S5fXIDe\nhx2yaIk+GSRmxJAI0zFwcPH6m9d5bDv8ECAjc2mtOO2P+dkJVqQ02Dj/dxVc\nMzZSxLxVOO87ndVCX80MsUx4Mbv6GGqU0kHpsFf4XpeVTQ4+mFD6Q477uYer\n3bleiCzlm279D6GB4cHymo0544P9Jzfv5H1jKyd/NgFAlz+k0ymMFRXPver9\noc9s8+EFd4lKum32dBY/1JOO3cz9KfLL49Lkzh6qrUY7TAUe10XOMe6vnaew\nJQb4BHotPikBaZVGcGEJXJ3U9ZOdoG80f422bfGoHRBWnVGRlKg04UgBNOqZ\nl7+eKIl5mG0gezU4Ubedp5qFRGGtBTWLz6ZmCnEoCJ9u8UM8yP5wAigzWcUp\nUsjpAsmHeAIJzBi/lZhIV5dxt5RaGfNUa8KQn9T2daGXk0z1KwEPt4M0MX6M\nTYdY6su2ya4yRKxpUCSG4Oayj0hyHIkN5eAYwranrl79D/eddRExj3ALPV6m\nWPp6ZGl2pm92UMpEOfvUtmj4v9h0MDzy6QftLf8KY3in/B42T+gIwSj+bwGv\nNeRBjQFeGsucyKljzTFA39UJpsLsLP4oNuchadpVBQwHvhXSlcrlYlxX/THt\nfDQ+kubj34fB9OfvwBQdMJc6Yw4LPXTUDGS+38Z082tBCLfYVo4rJaLH2BHR\n65uw\r\n=REhi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.5_1541769153611_0.5558806203620692", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-get-type", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "23cc13e4e04c61f001356529675a6aec5a2ef53d", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.6.tgz", "fileCount": 3, "integrity": "sha512-U9hmkEfO5dtccZ96iQgbPARorzyVRYWiRnrm5GO4l5iJOpK86fuUsZLjETu+cOpd72RVh00aEm/tVOhZrLizbA==", "signatures": [{"sig": "MEQCIEO2eQK7Nq50f8jN5CWsf1R0MvLaVRxb1glqlnu8J0uFAiAktcoKUOuQJj3z1W2uqEOFJ+B9bKsUB+79wHpW76uhwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5cisCRA9TVsSAnZWagAAkTkP/2ZVYGc9BaCMfxZxp8X5\nwwryETVr+6s/HC5w30BHFuadtOzcJGP7pECO92kUCr8N6EcB6mHDjnclqUfz\nVqboS/o6Q/qLPfe5X8ZaQ5gkEceM31AvaeT0atju1UhMdkfnDvyovnIF5aoX\npAJEC0a7TELjrDj3hLsQq6HdZ2W+ApmYq+/0uQx2k8eQoWXHPMq0Wuqog7pN\nmB3mqPe9IrR81IW9P5u/JfD7w9AxFs8OqsWK9Crh5+7bcmPGDIiJAnn7mMPd\ngGiGtv32/F+H0cuKk5IMepRSCj+Iq9zIHNRsjwTLOgWp+20P8IsYByUcLeeq\nt3mNLBVU32pd4XkANynaJhUjku7MQ72pUOaz3jb1stsHs1Uccm6+Hj7BdEEn\n6Azk6UMdGu2e5KuXpDzGY3rTcfs3ejKBM4AJBvdMtaj1KHRr+fdVnwmg/mEK\npwedo44PruFPNLLTETT0Lw4pKJyOlmjLspCvOY8jY624yNn3fn3OSWZDWmnE\nPZ/LeiutiHxlm/4xyA7lJ1JnywzZmVQXhhPBZnrECNycYQUj9ruI7BtKDnls\nxvCDu+1nTAoDqYA73JZ0x6D2+ejn6Ku6hUCyeZvP+0YHMxKpIPnGxyaZs4Z6\nh6Ka95W+NzsJ9wRY477YCu4SFGklfUz/QuWDmKakgbIA9f+6q9MRuwvNo4lW\nIjSI\r\n=+4aJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.6_1541785771505_0.23464750212887897", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-get-type", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b95ab5f1104b8cb9d31b31a2106568e2f767ec9e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.7.tgz", "fileCount": 2, "integrity": "sha512-qryn5C2u+bdtdg+p732X6KvixHRyG8GR5SD5iAE4NimpGF2Rn0QTUnJW+BTuBH8poiof5dp4n3wSNYMI3yNVnA==", "signatures": [{"sig": "MEUCIQDCrNOztK20DAkl/aACMwIbyPt5eUbKWfJknElCwUlXfgIgTK1+TRPqoOuNhjL7gb21IY2aySIBOpyVNR9m54HLLC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DJCRA9TVsSAnZWagAAgBwP/0AkxXX3tc7xVQFTiuLy\nroVCi+q+OFd7nwvsBMuyXCEy+/NRuFCewdXdjzTPLSV5YkMVb00bLThQqzsI\nO7uEw99bxGhvY/63xgPQYVwCyB1RYaz3m+qAoQ85ZYECsJOIDot6eQaUY0ww\nu2eL2je0YgtQstACuX8Z6qE3HXaC8uq8KntECLvmuAZA5FPdG37yzb8RxdlI\nSo3mHD39jvwl94u22/nyS6T8lvwI1baHS/7VWDT7FDiVUPptP010iObd35si\nIMUFBFlJlVDY+8hS6RM5im+Swv/q7KwagpW9OEswDLJ45/kNiHog7puQq+Qn\ndUkgzf1B5hpv5diUW0e+38Ys6zCg4QWGncuwTbeDV8mR/iyFtGRecH8V3fEB\nHAsbQO+m5hTU9Lt7CEJB3rbO8S32n/n3QvT15KQdmiYGtda6NeicY+IrPKhd\nTrLFJKirQwN6XLAdJu+Zy6xmAMS/0YOqLjP5J/WC2AOgR/zyU4MOjpkdsFmO\nM79LVBIbZYKrLzqCz1tpFKcZ/SzOs0fkROsmk26MlVM1fnx0NCkRxdhWxyCH\nYUU6HelHMeEnKr7wD+/yVyb7xY9kKQYyGD9oUD81NV4K5tGGYVkPGnzweHt9\nGOgkdd4M1mBoCbJm8V7x9J/8WvaKuqUZBfYxBMtraWpclV/AIzMxUR8gmPW0\nydpS\r\n=A3+M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.7_1544544456799_0.3304057967078806", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-get-type", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aee664bb66c44c9842186b3325e9194963f95c1a", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.9.tgz", "fileCount": 3, "integrity": "sha512-v1SLtB48VGxgjvZzwbNSz3xG651GRS1yUubCthy/jQPyovPIpIamN1z9JbOhJ88a5UkjRJ8rWmkIbgBYylDHHQ==", "signatures": [{"sig": "MEUCICR74JFIKi09rA6qW3NJrAuFLO6c3DEcGBdQXyKY+kciAiEApo68S5e94BfQPdGPozbd5qvY9Z/lyM4zxNw8/JgWLeY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlRyCRA9TVsSAnZWagAAztMP/11ctihM2wZZeHfgHy3W\n5NRmOPWNjTQXiTz6lDP6tQjlKAoTi55dbi+7KBmRSQW3xYQzpWYCppPZgld5\n9IneO6MmXKsMXVRXHTclYQWcYapeVWzUtNhFgqPSoV3rw045hzAvCp77AYmg\nKy/U/lTAqhwbDkVY+KRphXa3WwkcGHF/J7Io4YjY5nax1IYT68z5ZNkSv8+y\nq6j13JApuAG218EgX3qQ2q6TwnivIi5CVNwEQhtql/Yt2UEepChXKThgqm3T\nmjREVPXUPMq6Spb5RuLur4tGF8otIbTsTOogH5v8v4FCnMmoYHr882qbxgXD\nbrl2z2+zqOcz3/oimoZHAuN9eUscCOOXjSkVC3MMelnMGyLHLgftEdWMMxN/\nKIIqLp/jiySG4W/0QnpaMAepxEU9UsU330h28bVHEiNldJkAmbvmqa0uuM+W\nQsx0I8XKrw6g/n8gYQN318MkDorxmnuzuuu4RefBnKHS027+i+pRMkvi/oXq\nUBoWJLAHV3LW+657EMSz7zobjgAB3PTyRRDPoPjl0wtUODeP/wCSuYnU47Lm\nrA8cJJXgsODKnBTcSplFNkIi0Qxds8Gu5vpgokum3FU5WsRezeVlMXX1Lc11\nSIfp+wHzO6DmwFkZXjF2TtulDwS39VYqzfEKTO6w9iPkx7bHu3EjAWhVTE58\nicMg\r\n=CBQc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.9_1545229425746_0.853565306280377", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-get-type", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d3e331d4affcdb65975ed631e3359bcf0d936964", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.10.tgz", "fileCount": 3, "integrity": "sha512-2SBg0IYxsbh9+xOoQ1AyTUx06W37LSyCtKTX2F+/DsHZAnjkMmhoM8F+lxXgiLK1hXV+6rIRGMUkoF202KtEZg==", "signatures": [{"sig": "MEYCIQDbp6z79JjJpIrMTzqHpcULPIxyKdKpC6H5AqlGfpu36QIhAP0+MXfV4pcXqUpdlHJMk4Lk0BUi/T84GEyuWQ9S/YDO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNikpCRA9TVsSAnZWagAARX8P/ie2ukyeJwJ9z4rQ1Oxg\nZVDdLfwGxnuVevWqoR3463NszcvVgU2NeWbGK7AthHKB1aFYjpS4wYPbHamW\nsjJAVxkGGdxM2/qw4x4WD51XZU+yqhipu5Fit55htI9dNZuQWlweyI3zD+6a\noM/xLxcVwhSMc9cs0WklGeOr9ylOMi9IRiqXe/rixkAJLiWamOU/AwtuEGZd\nACS6ejtXellhtthdo+JjeoFg2AuXD8zvC7M2SD3fMTQ6AOcR1bLriWi5JD3G\n9hZs2JgruAFVMDNwTwW1ZSD/AnWsan9RyYNrj6Wpnq6SPVJY7qRR+8Va3vo3\nOAvHGYIibuP0U6R9Fkm9OQbQnFAyuiVgAp732ChNk0YHCTLZoR7ZvEin1J2i\ngXNwOjoma2uRax9TWo+cJGhauGh8fhDNvUUVbQ/NRwYphwYS6TGmootXkdYm\nSERdvtr9N5ZlmukIhqe5c5w9DkfAYcR4JuQqD7mj2p7JWHQt/TI1U9t+OHzo\nzDkxhVo/lJegeM8PBejy2whSxl4XSjy68E+yIkawqXw261UUcX2mPVS7SlBn\nOPuYWKCG7AxY9PerlCpOjF5mgv0UtrPDX7vu0BDrEZCS5Bh0l/Z2BhXK4iZO\nPgpPw8i7ZMQax1Hq3C+0nEsXg3bvuarG9dUkMVJ0dpbntFhTr1qT8Cz47wp9\nIZRm\r\n=ZBVg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.10_1547053352841_0.1503744958057025", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-get-type", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b77937f4d1a9ab4c075deb8895ae3715bec3a07e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.11.tgz", "fileCount": 3, "integrity": "sha512-ExTO2uvSqJXcrnnUSsRf5LvVeR+14hwC8zGvZeRszdsRn4QDsRo2XOyMolcTW6r519WLB3Ukkd/K8uJqquEpfw==", "signatures": [{"sig": "MEUCIQCvZadkbeVsr17OkLh789V323nvH7Ur4x0ayD5UyXAErQIgb0FX6UZgflycCjSG84lm4p0MAaXTdx1XHEg3x8F5GG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN4/VCRA9TVsSAnZWagAA1bMQAIOwdJFdWYBvCcCSHsab\n3okoFa+bxaCEs/DmT1O/BWYw8XdKiHBBTn0kKTNJsOzc0zstBlkJLrmwbpzG\n/GxaaQfg3du4YOJgWLuZHH40BjniD7vw3a9ODzN4AFnKDOTN4bM9NZPir/JN\n7Jx/P89a8n3AuaaJz63KuePm5XjlkEytZQ734ziBr32lUShM5ztMmnC2Zavr\n8XszFmtc6iRxAg40kkhjWu5NAgPUe9WADkgXpjfoqr9VIdUULZZjssnzBk/3\npxeYW/QBHwDuQIoR7Orv4tyqnSLe4XCdzMdB1t6/v+7HXc2cMSg5qOlrWk3E\nkL2tNf4c9g3zp/Ox1ogOGKqBEWVdu8iynf4OgkPTocJbRO7XHQfDq9d4WugT\nHGqzlipSVfwD1EXNgs48uWT4Bv9WUnQSxKQIz21kyXeI0GmADTt00/5FsTAA\nakF7zTYMKG2oeM/ingEp6xnxmEs0qnCuBwzzXgVsimr93bdFLw7kblLkeuo9\n6HjnG0RP+96jToXZNEQMn4VWrpvACMKt28c2LTinA1J++GvVCj7J0Xo2pURq\nBG76VuCozOUYjk9CMBfANrOz8M+aGQoXf5E1RB/88CE1QCatU+sRyf074HRf\nDc2Q+3k2XCbxvPJUXj9xrH9K9VYsaMkVFYiTe1oH+vzKfb3GbRBcsHXy2JU0\nRzkC\r\n=cVsW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.11_1547145172545_0.1843277878210885", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-get-type", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "12a4d6b895c9aeda50a2adb0c4fa61bb173a038f", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.12.tgz", "fileCount": 3, "integrity": "sha512-DqQnyief5JHdl+SKJuWT/qQ6ja3NYZlkCFROSiGyEqA97Qm9pmLcV7aayHYmTi29l8/nL6yNHa94kOTNNDcMyQ==", "signatures": [{"sig": "MEUCIQDNdlesXNs337q8G5AA2S9cDYeJCZYO+WizaveMqyVYgwIgWyi0YWXRArP2eEyBK4eCwdszM2Kd38/LvUaKPhMNYJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK8vCRA9TVsSAnZWagAApQYQAJTBhXx+ZZk/aXhcc0Ty\nWQ7Lugaw3Njq+cIpLst212J6ufG4jw3DII3KWzyFd3fdqNp6AxsXwfpAoSwt\ntTtUsJVT7n1gnbmVV7coLr48m8Cr32hEfRq03Opz7UF3Ic3nJu1/1uDwlJ6l\na/5zYULOgp9YoP/u98YBSiUMGwVotRSkJd0wf9EN3FjxkQci5jX+9tjtl5V5\nJoFh/Is9M+HGlnaXUq7PspRwqw0ndTmn64TAPgFHRuW/RMEKLmSDaLNT8G6S\nEDiBtlJQH7YMxz+WypYtgvC/knER2WBis7wywipCsZ9esWkPE6Z7fKo5QwMF\nh2BUYanqkmKvZ8j+admuWfWaLb0N26ORAd62udbnGrvwTA1VDyKFtFNNlLDq\nncbbrveRsQgLKmZOwHePYXzmkmZ4tL5uxWwzVU7/DeLMmOmS5fquuc2usot0\nxCRGUcSWpFOksC/I5bS8upPhvT1268FKP5SwVwAqXf9mnFeKDMQ3OsnFtd4w\nVWXLvG+L7kX4ILuVEgylxFXZp/fOAhpkkltsyjfqyudvAhEfBcK15sbljqNW\nqhfyPI1VnRi8FbcwMpx8Ew2IcKKR5TtaardxOihlipxFGhqTqaow6uWelivn\nf+t/0r5ShjoxtUaZjKRP3cnZPgGBj4MRezRT7m+H2Ff71ymQqpZLDXhoJSwG\nONSJ\r\n=Mz2N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.12_1547218735345_0.835976634554731", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-get-type", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f9df163cc3a66a368fa0aa0ea1970c9c1c13481b", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.13.tgz", "fileCount": 3, "integrity": "sha512-iT0GDY0V1KpKJ4CnDUpdkF6GFCGgoJu0R4tNDGM8PSXQ+XfivR+MeyDiouNbwfvggqTPEr3M2pXc2MnhRpSAhQ==", "signatures": [{"sig": "MEQCIHs4BjXjgubwtR4o2xLcR14/RC+/uHk9rzhbHewV1sYiAiA3UDdIJ7vurLsnT/WEpEcnHrJg5r/Q/p119k4UgavR2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUICRA9TVsSAnZWagAAua0P/AhfxqURpsEVlHYu3x4f\nH5/tsmp9DNFDOqkA2YbgNwkftaGBVwhL+aNK2ESaBiTmovmLw53p3/XW9W/b\nDdcIhT9S7tIf9TTZvD7IOrLLfO2Wfn/wSCgNhNGMiLWQlbs9ovdvj1HzJMIa\nKZad/L4XK3U0wciidy2oLKVqtoYPEKVx5IVK/yfA+otNWj4ZNCxiDyl0avig\nkM29oGQx7LkmFVORdOD/XZMai3xPIZ8yNEVwoahZfwXBhH+6+nmmeZ30js48\nxUUJOujPiVI5Ts664GM+mbLXg/Cz/qgQihUfJPbNABcajhOaQrp+JO/sA3Ao\nstiz7xy8ICYNrwB7P0mw4buM7xo9jCnsPLjMSEMLfrakLPbFnlbIlIKZ4VAy\nd3rtGEWW9JJYSDkPqAIzHHhKXDihdCZjpElK8c+neBjkgnMjtZnzAJldiVcD\nrEOvadpECzUmjujP8cLrWCf1cws4eHLmmLnPweyQrnIs4merP3YUf9hOeeYs\nVzj8mUn2Iz2gFFHrHre9zL8QIKFDSdc8ZssvfU6VzuogNgqGKVGGo0HyZY9u\n7kQwhwOnXfus5Z7UP/8flQd41rmwH+pcPA4Oq8aRfAhFjddNGKAWC9XDChdO\nG+kuDgtceicI5m54mzwvBYa/fKxkOKLaOUFrHHXUWP4TZ0lP5tVz1V5qQLC9\nBXKt\r\n=/hcA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.13_1548256519965_0.1590587889462156", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-get-type", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c7c8241f0eb5c728c49ac7d8ac2e563572425fdb", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.15.tgz", "fileCount": 3, "integrity": "sha512-gAU71gvnHdrRPUUipz/oHUMfRHgsZxrg4BayQDGxAHcsrtaz7kuTVsQ9iK09UJjn9Y5S9glWZHEuC69ASUjzEQ==", "signatures": [{"sig": "MEUCIQCLJxk45Srd0Y1ZjSxI8KKgYau8ZeZ1lp9di0eGgetupgIgBCYD6C1EAcbwvmLwvryXGaQCCgAMhy/ltbvGqN+AHSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftWCRA9TVsSAnZWagAAwrUP/2gR8onsencZbbGhOXMU\n+Ih9QaJ4WYXLPN7DoJP1zEEOn+/dHaHv2hFgQWucwhoZbVJJk5ikyqioXRrx\ncX6oIGFgq4iXWWyBpzP5y0GiTunvAyVIddeWDyjnkb2RP+feEV8NjmChtL6t\naoDlcADeeSJD6znsV13kzFI/tWXog2+qAlkfn3eCV1iAkUZLU1DcfV5ibXx8\n+t4jA6SrKanwpOb8vta8EcBB2F8/sJVx2wJFHh50S2++j4Csrimpj8/TGpSw\nmbYPCN76ZEY5r/WVbN16fWbKzsj6uypq7jKbVigofzeO2BJCnB+PFpqrFIhU\nq1whNWL8UV1GcevTOqHJpdvZDHMmktsY7fykdPfkUVkNq3zUlgRhbrodwpBd\n7WreRgJt5Hz3hZhypqWa78Id2wLoxoWYd9gwvFfzuUqL8DGidA0BnU1bR3pq\njusy2iQnThvzzx3ixm6zUH2S3ETObb6nSK3t/t9OS3EHmDKLq5EYjDJ/OOuF\nLRV/HmRgNXHkITmNHBvVSDsIa2Bp41vuaZdenH67wlVnxuMa/o2X2bnnHt5q\nW15MKD77A5E8H0OCYnPEj/wwPD/2iQOxc7WRCHMqPJFERiIECF/MHhj4sZxb\nq8wTKKEFq4SR1lV0GfHv4jW4l1QEe6XDgVSerwO6QKb+tGEMeHmTOLbziozL\niaZ2\r\n=Dr/e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.15_1548352341765_0.4418727113887657", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-get-type", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-get-type@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7a429dfbfb75008c82b23f1e2507d2cc4c6728b3", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0-alpha.16.tgz", "fileCount": 3, "integrity": "sha512-6I23nG+1u5bo94gjJkKEVfJtt4BqcvNlY7Gbs4rts8G4MIZCCbCD0RAV3l3nz8R+73VktLjaOLOpiB1Tc6qzTQ==", "signatures": [{"sig": "MEYCIQDrP7D6EqvdOclnstubV/cX/irwBRLcXY/1McW0bQc3xgIhAKeQOPOfWG1+/5u9Nvxk5I1hgvdjWUNqj/FapDa+bAeI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIgCRA9TVsSAnZWagAA5ncQAKIO0Ip0vyDVaxUvhk37\ncmAvq43vorxrp/ZYIemuzpjOsgezBN/0tPi690Z3Oti0JMMDR/csybxanVvZ\nDotdCUsEiYGdQUsFyXyyzyR5vNB89PV7Keg+3NxC5ijDnwPP18qJ54S91Ptb\n+9TygMH10RQh/ETOg8MdZTV2bFyrjcBhlNOK9YMoN+OcC+GmbSUxz9njNrES\nEOwvKmgHDB3rVQg0wPFkEukS7jie/KdIHc2CFCoSQYP8zyJF1lKynEUE/r6H\nH0ww0er6Mynm2uWmc6rDR9Zl99Sp8anqzWxVSX/FUkEhkb5Tqr10LgdmfXrV\n94azql/w1NdnamecRrlfITQMmqckMXXD65Z7BRQSksX8GYexKhMaTGGkvbVW\n9JudiIdfaVWQciqu9J+Q9X/DgUjzWLF6mXNW33w7DB+XJF4TcniiWPfqNADC\nut5pCbQqZN+CQtK2H5IrH6tYztWq+hKDkBP2ElskZDBJCoLTirQhEFXX27Io\nbVDJc+G8LX9HuY9vEoJJTwQfbuzmJxuE67DBUMUSRByMpfmHNYTjZdiIYQn6\ndUOlwtOE/CPXUKx1m7mV44Bb49/6mZG2PoLinRNrV8mIOp+QAhpbPRzFNzjn\nQrbKOMxbh9vyUMGslwoEgG5ezCAC0fLag0lJ0rP3C1o7Qcm9wHuU+9bouGcE\ny9mo\r\n=j+9z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0-alpha.16_1548423712284_0.35631021414165165", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-get-type", "version": "24.0.0", "license": "MIT", "_id": "jest-get-type@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "36e72930b78e33da59a4f63d44d332188278940b", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.0.0.tgz", "fileCount": 3, "integrity": "sha512-z6/Eyf6s9ZDGz7eOvl+fzpuJmN9i0KyTt1no37/dHu8galssxz5ZEgnc1KaV8R31q1khxyhB4ui/X5ZjjPk77w==", "signatures": [{"sig": "MEQCIHcirminK/syJl+A/Dm6+dXlMAHZL9sWBTP8o8MwJ0jFAiAZFYdavaD2KWndWsSfL355AqBddtQSKMfDmFYrOh+uRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWRCRA9TVsSAnZWagAATT4P+QCFSeaVRxAJmPEN0bvO\n8340TnRSMTOXQ1qfOqSxz+Pinhy0VvuT9NCX9BxQsnOJioj/gn1X24UCgDwn\n7shugaoNrygtvAdWJ41TFHVyduReF2nlKY2Y1DPRi6HxucCE8opiVJVUQEUg\nNAJmNgHMxF346z0HKaGTqeFEazqGAFSdUu1tnAHYDQaDA6G8AV+cHxe8uVd4\nmEizmDxPmJKuXtFw9VMySpPKtem4fwozSjzr+B0DwP9Oe6UGzMd0MAcRRx+R\n/zWTDLM2eoim+Zt4DTD1etNwDkB+wwQhm+Zav+5UG/D/Cq1YRibmiQDf2VIL\n4b6dNMEnr4LK03lJ1Ey9X9vp2n6F+5koiNv012mFRR+4CRMXcVHgj6c/Xmo9\nbkPVKiFKo7DXdGFrbCvup5JMWbA5BnggMbpNwVau9BuBga/Wwgpw21iEi2ta\nmyjKMfAeBjBe/IbNMDyqwFHZAZjYrcOtwmldBDyu7mUemxwLZzUox89I+BgS\nJCJJwxK4UvukJDkSRazvQjxxk5cxEAIl6kpxuL7gODillapuzzayE00o0DzD\nXIxUBBISyFO9YNPMWfA0It6V9f1u2PlGZCzgc/1+v5VaujDGQxNvxKZ36Yai\nryP/JXvRM2Zrqa8yWSbf7PCIRjd2ejxEm0pDhAQmH1RyK5J/koqaFRTYcZgK\niJIb\r\n=wrMu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.0.0_1548428688978_0.6569662514561281", "host": "s3://npm-registry-packages"}}, "24.2.0": {"name": "jest-get-type", "version": "24.2.0", "license": "MIT", "_id": "jest-get-type@24.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "00fc17a3ad08b68b6ab14ca47d4f949a82425e76", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.2.0.tgz", "fileCount": 6, "integrity": "sha512-IWrgF05BU05R7vXs+UfL9NbPfInotQWZMWv5T/jU3h/wH7qg2GGZ/hqR7zA/+n6TtWvT/E2vBxCkB/3s6pHrIg==", "signatures": [{"sig": "MEUCIElqeLazWHDQ0969StLMt5+qZ5FyWt1DeXVicr2TjpNhAiEAlOF7hjvZYg6MjiphBgswj9M3GFjwqYox55Ga86AaER4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcflwECRA9TVsSAnZWagAA21UP/3zv+Yv7FcW9auBkEVDO\npXQ44JN93DBQ1Hru7OSEK0ekwjRvp7RNt6yNSdspLAVhrZcn0b32l6HZkH2c\nVJoIjBF5Gi1AEW8Uu/sI+RfPdXak46+egVk5ILus+DbYwCvwg4x/EHOXIRaz\nJQCP47fGLFKcdW62D4FxRKcvwu+s/xPAP7ctoBDrOTtg1lOo9MusJR/z8CxM\nydxS90NLv73xHMgcaMSDvAsMvk5YGCiQSB0GVVTJFN4Pd8qnjgehkmZkw17s\nv2QKhf/O/LGoTJX1c4x7sFkcjnB/KueXOw0Gayb/zGmXugp830unHVAA1Kpd\nk44mnT0B1BUDQieS2T5ePwusCnUNWN8gdcAf+kryvnqTjbf/mfEpHbddyhBN\nFapuAPWivKNgiGWZ6GCd7vnVX8ZJz3Ns43e+eI32sUcbqeMNcWv0GBNvqu1c\nFveVjZv6bMmHwip6h1liX0Fud0WEd+2ni0PgGrGUmYJHL1gkoKe3TlvASjpi\n07GDa2wptvcBG54EcGb8emQaQG3s5veRzZ70F3bpSAP0nKKSTTO0G33chTvP\ngX8BuHoaG1DoepHqnZo1DA9L5aY7QPIqss+0ZuJrwxLIW/4i48WCkFgfQnaZ\nXtf6z/cF7kKiutbIMJFlPm3atfns/HpyTN41rYYFGNkdyFiqeVD/wjmg2xC3\n0rHK\r\n=tjbd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "d23f1ef18567763ab0133372e376ae5026a23d4b", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "This version was accidentally published and might contain errors. Please use 24.0.0 or a newer release", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.13.1/node@v8.10.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.2.0_1551784963670_0.4626002846040431", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-get-type", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-get-type@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b5b0c5275fbd761e71b5b042141718ffd7510c3", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.2.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-yiWzAxROsmEbTZferV6BV1C41Egkki52udV7WxDvH17v9wdWvDsD+OM5ettv6zjBL21ECBf8SlHPPTBWl6W5Zw==", "signatures": [{"sig": "MEQCIGIaaVf5ny7GH0N6ineqyPVU+LAPd2/mxVBUkWdlhdUtAiAHMQrOhfjaMlqipiLZg5DuWlKsE65JYMAYrh942R+vQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfou+CRA9TVsSAnZWagAA7sYP/3oz6l+d7BobsHtaFErz\nZcxoKS4rQHaJLaUxTFaNO5iy0ocsfztm+kd59HgWE2M3jJXb1j5jJbArUyl0\nF962/6Vd03KR8m++3EciFiSaEAcLvNXWyqHHxNHTCxj6fY+qAgOSz1JAnrHH\nPt5xnruK0R4NNL6d7N/naeZDlh8ZBRlEUa96m8GPqWFAaA7ybV7umHpLz3PO\nUpe4nnCoaUzdLYsxupdu5BKOM8gRmeS9QGh4lRKdtM2Gj5o+nQu6AAJtvkeI\nfJC5HF+ZWkSpfP5C5AqA08ftSWdEeXsELdl6MyyLKQB/5VUpx20v6EkIcFge\n2BBh0vnKPDd379bWBtsEWoTRKJKbsXfiKytic1nTnE+OWZHuaBBBwe58SRbw\nChvlWh4pwcWtsyQeKbSYh2O/F1As/2AJiuQFT5qqaRC5HLr++aR84dRd5oFr\nuI0vt7MEoCw3iI5Q5SoYD28/xY1JMPS+yXSze8HVg9Uq/YmpJ6QU2WJqW7RK\n/QpX/DXJxnHwMcQDNzH4DXxrcARRDCvBY3rQ1IH9PBq3KjgZ+eOGizXti1zz\nUAqKyfevlDgzSNiu/l4/Vy8FkPn1o6Ptcao3uSy1Rz66vuwK9f+8buI0U4WI\neA8775eV6KSuNB7/p/uz+NzKCKmeF890ssx4bOwd6dcPWEUAwMAUUOc41O0x\nI54G\r\n=nKIi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.2.0-alpha.0_1551797182360_0.42799133505887643", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-get-type", "version": "24.3.0", "license": "MIT", "_id": "jest-get-type@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "582cfd1a4f91b5cdad1d43d2932f816d543c65da", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.3.0.tgz", "fileCount": 6, "integrity": "sha512-HYF6pry72YUlVcvUx3sEpMRwXEWGEPlJ0bSPVnB3b3n++j4phUEoSPcS6GC0pPJ9rpyPSe4cb5muFo6D39cXow==", "signatures": [{"sig": "MEUCICiAWNBhi34P8DWbJegpRgKxBf/fXFDePAKr82EWoScqAiEAkCM2QJUgvxiA2HlcGl11kOc0V44cdbnzD2BAkjhTV9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWnCRA9TVsSAnZWagAAU48P/AmORuyljvHulb/EfTR0\n8WuMAghgSklmGeIHPGBhyiB1NBaY/WK0FLOn9lerXF3ySJJ5v+ecrMgPL96C\no46VQiTxAK/iSiNI67R5S8Av/0K/fc+MyAWa0mZ0/eNYO6P8xkcTNnYIBkm9\nMmk3WUuwP/Sy6eiBOA3XMiQiKGxy9waPNNS6Ep/dBxWq9gaDUS/Kd/WNJc0O\n5idjZZorCUm1734JhK3LXz6nVDDichXGmXG6IT76+I09HmkFL6qNGiqDG2cF\n8OVHzI/VCc+gTEF5qLLY91STE2+X5wZ07zLe/dQme5PGIgQIlfW3g5WRoZZX\nxwdhRvG+zBIGnRCRl+cGfKAJXXcjZ1aT86dPEkg8V1DxCCwmgObhs1RvX9m9\n9dfSDePDvF+Hy5Q2slRYqfAoENy9ZxjHtf3NMkYB6e9Xfe9AcoOerQruSTth\nxOAJaoWMr0veMd1qm/XgSA+frhudPl2/JvZ7tOSMhe0Y0wZg1yRq/WSwL7To\nGaqWSNhjWURAy0yPQ4aT4DIEhT/wVplXESoMyYstSAVS0qzRe2hRjYiViG9a\nai+kEOQ1sO3LjgIdsL4XMuRio3hl0RkOplLx+MS51UemOSjDM3/v4hQSg96g\nXPbU35i0Nb9wFDBHlLS6P7cE2hOZ932q6auFuoGPzHOEBHBXfSNAuFgY1DZD\naZ8b\r\n=0D16\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "8.11.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.3.0_1551963558763_0.7561308961966287", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "jest-get-type", "version": "24.8.0", "license": "MIT", "_id": "jest-get-type@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a7440de30b651f5a70ea3ed7ff073a32dfe646fc", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.8.0.tgz", "fileCount": 7, "integrity": "sha512-RR4fo8jEmMD9zSz2nLbs2j0zvPpk/KCEz3a62jJWbd2ayNo0cb+KFRxPHVhE4ZmgGJEQp0fosmNz84IfqM8cMQ==", "signatures": [{"sig": "MEUCIEOz6v8nzrKwU3eNpzEZyv2I8jEaAJxf+PF/UtVD+rORAiEA7/bBwJsGX2128hFs8YSj9z4+u7NSLs8laZgEH/gpj9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkQnCRA9TVsSAnZWagAAv8kQAI4lm+y5mJHaMUK4te93\nVituIROH/DwSaey7/hyrU58A6iY+W7UjZKrBR3IxSyFfRKPkCCi8S0ID/pYw\n6AswDwVQUJPQXsk7sdz09t40pqwkiOKgjdLGax36fs8Hjmn6mUp4X+phpzOf\nXswDW4lvXuLRgq86EKix6CxXx0UlUc6FOSEDg2HGajmmuxJZNdhouq/AMtXA\n+Qb2fX8h6j3JoSFFyxfAoeIuutGli7CuzMbu5v423LeRJfnaU6LH5nRMQ59q\n0Olfhg5Hgf8Y37PswtvOLJjUrrf6ggkVJShmgIe/9hxHVu8yL6ejtBEVcMCU\n/nskTtLgzZnZrZhj/31xOdGO7jFKs6c+ETu/PSN4AQ9qx8TKOjo6KeJUVbn7\nUYMNgbzwOOvJzTBYyYDdqaXAZufo+2XHdf61jGOd/us0/rUv2YK1sGfZRHkH\nKRgoFqHJEcsTl8SJKpMgJICBlvSoJ+TjQuyiz67ZFQMS2GCCGjVI76EBI3Rg\nOEgwDkq9ggkWzSrUqVIFMrLrqbCwQrKeAF3dcJ8Y0X77ZeNwnE7chO+nG9jM\nk3ax+5LHL+Xw4w/U7FXRkFgGGELVv4I2yMORNxPQcsbDDJeybG5cMOAKHYfC\nqb2OVREIrvhRnaV5ZJf/FELwqSC1yGI4FW4KcJEm6RjWgxMP8uL49nHfrK1N\ny8Ua\r\n=5MsH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.8.0_1557021735287_0.792997320532123", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-get-type", "version": "24.9.0", "license": "MIT", "_id": "jest-get-type@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1684a0c8a50f2e4901b6644ae861f579eed2ef0e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-24.9.0.tgz", "fileCount": 5, "integrity": "sha512-lUseMzAley4LhIcpSP9Jf+fTrQ4a1yHQwLNeeVa2cEmbCGeoZAtYPOIv8JaxLD/sUpKxetKGP+gsHl8f8TSj8Q==", "signatures": [{"sig": "MEUCIEs3fj8HGrXRmM0kKwZ6FeN4Zo7d2xTGyPpEzk5ia2xzAiEAuL24qGaUG73Bf3ueygUO0fPCHJ5qB+BQbfQGJNDFb7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAAZo0P/2RaDgfe60PWNecSu11X\nzW0xZL1e7xfF+uFBD357gIEDp8iw9faezc8nHwsXBlMM88XgUSLgT5hQ+RaD\ni1WeXVriy5fG5VBEyKI+/u+IbMPiej0mhZOms8u71yXUsgTVYm0uWjiscCMC\ne4NYj3wpMWFFe1fh6sUuCWTN+sj0ObzKkSAJBEfXvoEMgSDrTA7jivV7/vxr\nFwM0JRJCn6QtxpxMbVZc0Qj/ZjePjvMVBMJu7H7cqQJZeARCKmtu+C9vKWAo\nPG3X08yGgSY9DciXXUNi4gXNIv0wVniSWIEr2v4DhOO+X2Mhkf080/TFFB6V\nKpZ0qzznTSAEOQEAB5Ru6Rrl4X+xhNPYMSH4fz5FfxTpD/qIeIU7+BbOkUIK\n1Dgn6fo5T9FoztMsU/VEgrEdq2SJQRWrUdztmXAJpXNM93aBQnY72FJL8+nP\nBgjZZSiaw/CSa97enLKIlaDhInEfIiLLVE0h7rg0TWaEvo9IPGoxsm3GGSA7\nqqatRAQd9z6cZka13Vdo9yjoCgNy33Ps3jJ9xAptB4N2ePFieyIQJkltuKG3\nNdrsTMLku025X+2VvoYryksmp/eCUiBKJbTR65o1a5EWaG0BqQr3/g4RhLQ9\nQNWs9DDlgcT5Xce9193pFICcbD36WzFAKS7NmIauZEzBKsu630UpS7QzSao8\ntsbB\r\n=E98/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_24.9.0_1565934946297_0.9750602336385061", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-get-type", "version": "25.0.0", "license": "MIT", "_id": "jest-get-type@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ab5434170432cdb3e9e2a03bb8be7951e877d150", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.0.0.tgz", "fileCount": 5, "integrity": "sha512-cdBdr6EMdslO7u2Lo6E/kVdA/ktz73hjnXbRukPp7wMKHOZP3wodWJp9n1iujW3urGK/CbGg+fXqO3HoLQCXaA==", "signatures": [{"sig": "MEQCIGQvfmjGOavpPIEHzQSmYvy/gh1hb2yOA549sOmbGGhxAiB8Xgsodmgn4xwHgDTyaeZgs5b3dQVPUJt9T8/e4RcskQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrBCRA9TVsSAnZWagAA+40P/RxFAqZ/hibIglroDHcN\nE3aQOE/T7qsy8oyZvsFXvdllRH7fH85ANPTSSS70a2ZrZK3UuG1kLA9GqNoE\ntJVvHhZK7W+cDhuxg4PZzJxgFhv2ouDATdjEDrr10xwpJK+9u1kORqJ8f4H+\nW/s2U7lZDe2tDETQGPgkIeMg+l9gBg2T104I+ByoxSL/XeBwmbTX9FlFUXI8\n8DVLqx7o4zM+JBWmUd/F28MhFspop4EneTvhOadyiQwqCU9RTWd4EIeVP5q2\nOiSeAXoBkcSbJzF/RE2B01JjELFI2MOUZazdnGgRweT+cdqEeytR2gKnO0F4\nk2oK8hzZE9/LwnGgsScFtoat/vcmTGfHwKrKt4UDcihtA40EQ4eIb0Z2fzne\nUdauVIXYm2G5aRVIOcCrR/piAhHPDgoLk/FSJQlFvfDj/foBUIAMBvHKvyAU\nsv6Eezj+utVEZ2XIy5s+0LGM9l6afPgPpUYqWHa8+5mfDgSrXqA1N3+BjWv8\nTPjJMvb9n61gFLog8ofzGkbzd2GNB+8QulrIdi/VDg9h+tFdvstRIYgWKHAb\nmfw0Z31guLyZsz16eWqj1BrcW1LZ+yWPasL+eD4+E7zyYrye5tyB7QWX0JWb\nUYlTT3wQPQjQd8zEwlLSf4G4c8p/VNZsbkBepVdKhbvmQIxsUF8h5SaBWQtA\nX69x\r\n=/pJI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.0.0_1566444224887_0.6149158583265999", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-get-type", "version": "25.1.0", "license": "MIT", "_id": "jest-get-type@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1cfe5fc34f148dc3a8a3b7275f6b9ce9e2e8a876", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.1.0.tgz", "fileCount": 5, "integrity": "sha512-yWkBnT+5tMr8ANB6V+OjmrIJufHtCAqI5ic2H40v+tRqxDmE0PGnIiTyvRWFOMtmVHYpwRqyazDbTnhpjsGvLw==", "signatures": [{"sig": "MEUCIQCrXWqIQ0UeeE4NxNTN7bHxxXHki9g9mah/boyuzrjzXQIgGjf3O445y7gotuwGQzuZ3hLF2fTBDJlkmKSDkn8JNOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56ACRA9TVsSAnZWagAAsf4P/1DaRs31wKdAYSlLxJJk\nV3RNtEu7x6D0MIMTAIixSOiB802bNsY6BRlc9BEXW7AXy+ZGh5LrUwx4QZxx\nkQ2AAHqWzymPAsqfiZNkQVq2NHxJIb+gafW3azpShk2hm80i3E4u4fkt/j5G\nmJvYiWF4o+F5mtPI5TVKacHGkg2CQCZs2TC+mrFo/4Pjh/Pqwv2y93ozSPmJ\njwPjOGqfvt8dGRJU02/kR/ETQI8VBCH4KW8QiBJKjPRh6iP6l+UX5DonV6rP\nje1mynLaotBdilNfDt1MGXoXPEq0tahKzduq3Ffj5ACB3Cb0opQ95UZyuLPB\nnl+GVGuYmQhriy/DMV9S6DGNKD0KDS11fWnGbypIoPUMcRX9FX6p7m5aGF8r\nVyiRY1psn1ZOZ1Zhe5xovvBsozS4RXDQ8vog2+VCEcY7HWkLRpG0wXabpx0P\nc/RbtwS+X5+ZMeowicpr2F2L+8P6BJdyh64lVHoKeGhZM0ziDuVUz2pZ9ovn\nsAwVkMHWOjdKIwXs0bpCX68fMFFSEjw50CpvSP1/lwh5lYBHbIww9hCOf+S3\nkmbouPmDZS5GOKB2XnaDA156+3wEqJbX/IQQhW//fovB6ACSex1K70sclGoO\nCEC9lNio7EqTjzCfOfBKb1FBKdYIex5ZazmkFkiPiIuqBVLd7V8ha4lFvJpF\nkLy9\r\n=CeuB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "10.16.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.1.0_1579654784426_0.4726876320901936", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-get-type", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-get-type@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "74410f1c9f1e265392e2de0f0636d38f7af8e3a1", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.2.0-alpha.86.tgz", "fileCount": 5, "integrity": "sha512-kJN+FZ4xNm8eDuxpyjapz8xYmsEsiRVVEU4thb6z8cKkIxRMVsgE3am/DeQBDu+CZ3DdYH6UXGeQq+rhCmqJug==", "signatures": [{"sig": "MEQCICpuEUvFEWN3WmClie5gfdSW1u5O9nCwwgaqI3ZVK4e9AiAdL8mkL6Yo/OLb54KcklY/xDjWYxq+gWKiK8RG9FZllw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HcCRA9TVsSAnZWagAAGFkP/1/txckvvRdIFH+2s7S1\nWHWu9SOlcKPDpbEz8PXepn/juRy/Ggi2r2hgpSYvaPqNtn1XSdGg3xHJTBs9\nCh2SuyFxUrZYPb6FkDMcOC4BfD3vtW/qjdKPjBHilK8p47budvaxMxPT4CeI\n92HUUrmxxScmDbFhMfwaMRplQQ+rMyU04kF3YVALvHAjOi4wAf3yzcoCINO6\nvYS0Is6kgCuILGiNNXuosxzZosgQ2rnOu0/2qo3HC8lT7xYAlCuPTNme6lbQ\nyBvxvrE7+1UbA2qJVKKx1FFryA+k1uQ2BrMCsetY/kd/ygWTon6lWiwH+hrV\nMiBYejKClBXMLxBvu1jUYt5DNNrMtfqJIGOdHgb6vZQSMEqTVZVvpjNss7OW\nOCgtsRE+v3/oiNIRWIKv18BU6GM/HKZzMFeQMjriFfhwp2f80ORKuQKV2yY7\nqj+7rwbHyZhdPZGh8FP4LP3ryUURklaGBUatwXLwiREeS66QQeMZkYAQlMlL\nEdCrhiHxqVOo4UgoNz5tOxH2aVMeD5z7y4KrPCrMjnz38+8W1l3RVi54M2sn\nlxKtfkegijCgAaqgeFmjKKi79UxoqA6XQEYLywiwK4YA5sBKEHlbx4J4GU72\nOe1dNNX324qXn3QwyRD76KXTnTSnpAxdGWoj2z9eN+roEXQ7VRZvutgH9sth\npi3b\r\n=3d5A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.2.0-alpha.86_1585156571536_0.8012097411300894", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-get-type", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-get-type@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9cf55a0b7ae0e384bd3d544e89114d231a4b3b95", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.2.1-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-a6pCEY1aKodCBSKUxePQc5ZP/BHZC1ArRgvjvwf+bxsVdx6OEI/5b520w9CTRT/yW0VReg2EZ0hj/JXZ6JZ99A==", "signatures": [{"sig": "MEUCIAr6avvNJUIu1iuWvuNtPA/ahRUDK+sK+x5DBRjoaxBoAiEA+W4SMDzdwqr/mPWEFq1mRR3je5omcWRylfBMsI6fz7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+lCRA9TVsSAnZWagAAg38QAILhbWSgDGoxDZbJ4hZC\ncbPokaqbXPjZ8WThuM2yKis3gOOEFXjY5Ir5ughz6NbgQWEfFC79eJEJcYSL\nFm8jAXgPY4MA7bwAGKKWF/pGvM1CbPF5j1cL1fOStVRz2lE/tEQ9Q/1EW3kj\nKz52ly52g5vJW5uFMWtQ1iyVlc7Y7J/crEezgKLPr2EUkuLqRL5zP2r62xvU\nN/cBClOm1UGnrCKWETMe6tyohgSa+AbMMq9s0TFYOvkR1ntI4FdsRF+IlRnz\nRHu8ocpe2n4H5lCGBs4VKaSQ6PGvJ0gbLw22DgeJTPOxKLoyfGLXRX2Vc10d\n76wjGQt6x2Kgz6zoyiPMlbEyEjOvp2FV6W3sOeGT0L8JCGqzt0GIuIX7QFFj\nybONMRm5KgR2gLMoRsiVj1IrNM0Qzh2H3AjuwG83yPB/EKAdkavvPHB9SD8G\n0GVbZgOJJJPG5bJRvcLUKspGl/clCU4RmHwSZ71BbcEfs4l7RuzavX2bQVL7\nyLFzYJMXxtycvoCLnqAWG2izN/RR0tl+4Ktj6gXKcDULsPz94WdOBghdFEts\nBomy6Q1K7c4pCm91f4wzfKs2sVIXTRSrlTz1acqA21E2fkCRodUp7stz25ei\nB1fm1iSe/ZodcSUNKb/n9RpR4MuZf5j7NwX2KNlIk3PabpAKdXza/aEveWNO\nwLCw\r\n=YY3n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.2.1-alpha.1_1585209253404_0.18313234557949243", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-get-type", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-get-type@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f95d321a887418d3d6f68ce4560a1c2ecb529f0e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.2.1-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-u9nQXAQLObNr9a/cPIZ5RPQ6lLwfDd6CjHklpYvpHwddwg5ZSKSROs1Rj2iVmAg/1VJJMhIZSuiQBEDbIUZI6Q==", "signatures": [{"sig": "MEQCIBbXD2E+DtyxtXneqkrFF3bBRzEmC3rAjZ3Ta5oxUKzCAiBoBwh9YA4mLmjKWp1ivVDcc5hUl5z7zRZVOtduEWsDnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNtCRA9TVsSAnZWagAA8IUP/31PqfasgnYIzGdm41dH\ngSB9AByUIPw6P+N9AEUqxM16zSmeGMoRkf0pYdBG2voMU92MZUd5MaTkYBDV\nhHtEmX403ngWvVbXExEAmFHG+GokCZiM3U61BSaKEPWsXICPFpUnW5ciRAna\nTJtMXj56sqYJ/xz7oFCsCJaFJ7H2aAO7Qld4WA547lmyEjXl1LK1IfuQ1Fs0\nkrR+zpb/6qREEjJSbhTIwcigYC+OvIcAaOinU3f+d7lFN9cgqwdUJy673O3Z\nZWF8yM+PgREnG1+y0xdpAdhpTpCCP32zEa//ISim8drDAhfqdEkf3jT1bXU6\nLuxebesEr+bAoRFd9J1bS/UxOjRhYt3XlV++cT2HCc8Z0v58G4G+oJSrL4Wt\nWcOw915UsVcYP0OalhPCSm9G1F0zU1Skt9VkWQiEmXRWxspoelSY89TbEGUO\nJJcFVTWZQmZto71YhRbykNIpHD84tfeokO2zVJmBakoe6Ph49+wY1oLR264v\nYH0CY9yGOBD6qg1kNKWrK2qrO4yr3L/WuLRieW7qKbxCT12bXkkOSs0UuPjG\nZlA/dS+0Sq1I1LnwKvaJexUdCVbaLEn+8oP98BDSzsVYnG1+Mg6hOs2clNpe\nZ5fH9e6YkygvQDPD1PiAGUqNCU4qbMLMyqlTSjN/b1xyEGLOQDxEXIbtZ10r\nFUUN\r\n=268T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.2.1-alpha.2_1585210221240_0.4427659318215429", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "jest-get-type", "version": "25.2.1", "license": "MIT", "_id": "jest-get-type@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6c83de603c41b1627e6964da2f5454e6aa3c13a6", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.2.1.tgz", "fileCount": 6, "integrity": "sha512-EYjTiqcDTCRJDcSNKbLTwn/LcDPEE7ITk8yRMNAOjEsN6yp+Uu+V1gx4djwnuj/DvWg0YGmqaBqPVGsPxlvE7w==", "signatures": [{"sig": "MEQCICEerbZjWWiMCgC8Bltdu7ZvhPi/5KurbIoJenWamWEyAiA0gzuEe5135vZLXRHXmbt4wDA8fdbLi27I2OG6aS55fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9QCRA9TVsSAnZWagAAMBQP+QED10GWhPhy9TJji8x4\nfxRSLMKc02NP8aPojPo/qr/AxiF+yPjevaJt6d46sXNpZtUkwrpR2jSgpI+p\nZQWVdWEDUPhIOhIEYNrWhjX62f9lpknPLEa9z112BY9OgTFXlo+IIRIIUz8B\n4/W6AYoMBm7m6qiN1GWx/SQNm83+ZQ5FYR/9RGi4mMoHtwSlFvIKSQvtWZk8\nLzaEpC8auqjjnj4u+6VXDGk9IT0wllLhAOcfGu/oGgCjfN9xtWmgZChFnS63\nF1h6DRAxyNU7EwQ/gfzQtygRnu+2HLujn0JI6aLGTmSsLVur2Iamy+cACGYf\n/ct8p8gBEQVWfHT7QYp2wgKHIRsSojtiAWBdTOYBVBSVj/fyULIPswc1g0t6\nNjYPBOObSowH/uszmNgaz6XlIFi+wF8LyzcCyUb+ETT1OJ8M379RsMDpZmsl\nPYr0H/izUsYlErD8zWfa1TRoBNWJKN9Xt94Plnjqn4aX2Dxvl+39odhlVW3N\nVLV9bPZ/bE0hqH5zk0dwoYu4aev0nqVCDhRCHQOq8dYmVbUEAvIIHBvO6mHA\nwZX7U2a264xZZf2JIf/AdOgFPyfBbvP5NTk2AZdkcaJKTn3TiNpqJNjlrb5G\nRqnm27NeLYqUQWzLbYEJMzOqU32mXGhpL/2ijKRbubM+IRdwuYLDyrL26OV7\neoct\r\n=4zmW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.2.1_1585213263929_0.41355479925940286", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-get-type", "version": "25.2.6", "license": "MIT", "_id": "jest-get-type@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b0a32fab8908b44d508be81681487dbabb8d877", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-25.2.6.tgz", "fileCount": 6, "integrity": "sha512-DxjtyzOHjObRM+sM1knti6or+eOgcGU4xVSb2HNP1TqO4ahsT+rqZg+nyqHWJSvWgKC5cG3QjGFBqxLghiF/Ig==", "signatures": [{"sig": "MEQCIDH8Ue+RBbAFn4yqwcAnu/EJj3YkawV5GbVrY4EWqG3cAiAd9y3fp+MajMLFTJ8bvkmB+cUA57hPJXYZ38QE0idhFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb50CRA9TVsSAnZWagAAoDoP/23o1W2wGPd06BlKbSzX\nwVa3SWwgUdawU4znERGrrW+c1VvojKLAQ0YgUCm+OMjqDaaUDmkQ8LiqjmEP\nWPqY+bocQBfiKUYd37y40uj09gx0uHVxCxDhhEcsJWgvQKbs9iv66gTVOO3W\nxzmxSQ4M67Eve6qFHGZkaCyIESoUnF7V1fq/bfYY+ApKr4T20jTczHza+RFh\naKD+wKYmeyNvf3WZNUFnZAnyqHBLq1dElR/u7XaR3OEzyyH9joKf7I8t+F/4\nyi1jB8ZGJatFKadviE1Ass4JMvUlKvW7t7NBOk+Ow5gFr7O34czCYW3MDVBJ\nfXuio+6Tbahjt1dY5FO7zRwXpJjC1KARNkGQYKohgDOFDNt6xdRZIXBgyfLy\n/17zV5ts8RAzeAvul0w2aLplvXe3IN83EeePf/MmJ4LLCkVpshplxdl8Ud+o\n8qjDExE1lcM+KweFfzBEQDclJd9dcEPt1M41oYmvEyyv1lMaAMyY9t44humW\nZ60p/4cdL6NVkVpFbsANdpV8kVgdplXVDbrjMmbGhf3sHeXghVqoK8BPs9SE\nik6RUa+Qk4Isxp8KWNg2p1X6Hrp3uYEKTasOEgFG3QEi2bBlqEnoKxoSaaf7\ndeX4EE12x/1avzCWe+0thhVSYMQdu/WVE58VZsFl8zSISVcU9/qv37dYcPsR\nQOyX\r\n=Hnpf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_25.2.6_1585823347876_0.4357712091051056", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-get-type", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-get-type@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c2e8c2538776347c9772ea4f15fd31937afcecfc", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-26.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-IV502NkwuUbhlECDvgk16/psWZtMZ8ZadezZJzIZ2oWdOcxF+Cvmp0sPMbBP7RqVygj571sFD8h1VtzZlSYpIA==", "signatures": [{"sig": "MEUCIFA85QJxdSqf5P44S5/iGz8PIgY7o1zodwM1iWL+kxGRAiEAq+vN+JR7kXbZgUlJmQPIbrlbewwo16bJJHssx/Ga9x0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPDCRA9TVsSAnZWagAAZ+gQAJ1qgff73jzZ7DlJr819\nKpWJEA6Dv9x/MCv8e0vW1D0yGqmpMATfx7DSvmCLbLwE3TBknqvU0vGZ0d7n\n0Q2ni0b6C7sog7dx/VdnuiZJHFTVX/vfmM+yXtSbTYYrCGc5z0o1q+c5uukc\niMr/bdpzkVFvWanG4KZDpr+eZU8wpHMXkrqsvmCZBcyevg1XM+9EZeM6EWiS\nySBPcN3p1sG16LHRe3UT27h9PoFcJu5qftRTCANkPSChVG8BLksUg/RKSTRU\neaLFgIO3sm6Hkf0oDEQDRPu81BYwCQ+DzI5G/1C7np2pHQD5gMjwvLAPs1js\n910EUEKLtEjfvfudn+yvtMitmDA/gGJ0aDDyhZEoUXrgDOTFTu0M4SrEupS6\nn9wzDYKCbeCglDmdl6S1PigD1ZC+c+mK/ps1/Jq2bSCHfUYVfagLfGDS6EP0\nrrpyljqrBK+tRx9W71/k+TDo6cK/npIb2ep6oChWkEdpOi0F2m/C/1Ua59qx\nh1lKrnxnGHTZn/1tVp4+ezlTffm//MykvkF0XIn6/nyRkgpvGynGCaQ7ptU6\nrKHdxVhWp48n1QSSxXt5J7dv2fwHf1gYjIlnKMgB0guU7ipYy5QXrSkEXKl/\n1JDnSn6iQSP7uiwPBPXN/cRrOX5C6vkjTXmmgsimv5U8kTO8rVnMXoXtbbZh\nEa79\r\n=CQKc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_26.0.0-alpha.0_1588421571305_0.736250566775094", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-get-type", "version": "26.0.0", "license": "MIT", "_id": "jest-get-type@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "381e986a718998dbfafcd5ec05934be538db4039", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-26.0.0.tgz", "fileCount": 4, "integrity": "sha512-zRc1OAPnnws1EVfykXOj19zo2EMw5Hi6HLbFCSjpuJiXtOWAYIjNsHVSbpQ8bDX7L5BGYGI8m+HmKdjHYFF0kg==", "signatures": [{"sig": "MEQCIAtkWSaA7Z3rfd3bM2lcb/eBg2KQi/qPnrNnaMZ1sCjfAiAMV89wrPnQZiB09cB/OLY1jbwqqjYJIz1XQa8J3fBhGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ4CRA9TVsSAnZWagAABikP/RlJhRi02wZ/qmx1WWyC\nSsQucd+MlvQPHX5jDp2j24eCGS6uBm5GhytFZd2OsntllPbKlu4elan2Q9gb\nrkRFeH8ttn44FNpqYsbUMzJylBU/iOsqpr/R4tc5XpmAwFVRK9Ym40jdI4xm\nP0jiXaPig1ube9b9UBZ0zbQ3xqAl5h26cxOj364rm0QPvr5rm7i+ROH7fE4z\nyheuh2yLoBTHZHDnqG7jLGj//q012KbQHPfaPwy8Eh90pOKx7LERHkA/hi1I\ntLgeL+A6OIZXIefjUXaqQvpcdfhTtsQDeE09xG/Lp3MEn+SAxvxCvPsasLMt\nhHe7Kw9tLbL4kdDQlv9aSiLIYoZpK785AWRD70LXZdTmIeJMEfDWwSaJh4Wa\nhYyFlkxDpXqa5A4oYvuu+dWYeRQF1Ma/iGNWg8BKbSeVsBIc9LiElhP8EcPE\nNucYzks+6ncdFJX8Tqv6YBVk1/dmsS3BR3f/0+QTAVu4Z1DSRyk+ljghk04c\n8h8pcVsdF9T4izvDwMyRf7L2zariK1KxAQiQGOVJiaJQxISEQvonPcXY5xjN\nltS9B0CY/GxGAG+to4E8dj0htLxInf+Y1D5V8oq2EUlfJT3wlNmPLuvliI+l\nqGHWre7Ik8AbfTB96fIXgesZqG/dEM7d50nW9fGqr3PvM5FxJMbCwUAz8uZ9\nu4ak\r\n=PyiG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_26.0.0_1588614775922_0.6396399672201725", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "jest-get-type", "version": "26.3.0", "license": "MIT", "_id": "jest-get-type@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e97dc3c3f53c2b406ca7afaed4493b1d099199e0", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-26.3.0.tgz", "fileCount": 4, "integrity": "sha512-TpfaviN1R2pQWkIihlfEanwOXK0zcxrKEE4MlU6Tn7keoXdN6/3gK/xl0yEh8DOunn5pOVGKf8hB4R9gVh04ig==", "signatures": [{"sig": "MEUCIQCRTk5MGLLgCfG67qnciu8/mnzlguq1qTCJ2xmJx9AQcAIgQ4LZyqdtuc80Q7TieCmCro6NqoGLSsQ1xdjgBpvuKcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAdCRA9TVsSAnZWagAA+RMQAIqBJ8q3mLnhBktReYRN\nAq4Mklcsh2Lbfdj0eT5ojcTIRRQYAT5zW0qPJuogk6n4I4IfzB0dumHZFuuR\nZmE6T1PM62CV+Kb8Iw6IwQarWUr90wbGoVCbCB8Tek0HGOY93sQnvDOwVR1i\nz5o+jPJ7aU+AcBZ2D4inceBH6fw74B1hdFD/DfCYPyLvFpG78ZHod7off5O6\nErkN5KrVFfa3YpLBi+rgreEDge87Ax/R0zt1wBT3yLIZ76hyV5tE884rXWb7\nVuBUdW4XA6oKDPJWP34R+Fb7Yr2YUErxT814F2JxfEl2tbryHEb+h3W5da7I\nWWoXfBFQBRTTENj+rWFoC12lFH7ernJaL+PbHRC0oCUa1UP/9cQvtdPGj+9R\nXT8tD6439kHeBHLuIo/w6gJOsQsWstuEoYISvlp+r//HYkeJu97F+bFqkztl\nGGM3FaE92WSX+6YFra05ZC7WKNzCTcM4PYYjLdKLKhSOFgXlPgLSGmVkzCHK\nbMTtnb7SuoE5eMhWYqP/nCS4WCEjw2sEsRFW6xBLoV5fCKiA7Lpb29hbaKtO\nXNUJYe3x08OcDy90MsnAC5hh50lGi/V0Ei23X/Lxe6A0t+yl3TSt1s9IF0ec\nu/oRmn15RxMDGwNM8fa2T6HfXcdZEk+kVLc1K9VT2WpyIZIbfRgJF72HkfHm\nCyr8\r\n=NRwT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "12.18.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_26.3.0_1597059101399_0.5078686582229266", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-get-type", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-get-type@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3881f161b4380ff5dc7983a8fc22f49611331799", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-Fp0uZdy/HNhqHxhBrYvR+AjtfkQgsWTGNmJuqKfGrjn3j4ke0LTXq2ryZW+Fliix9geW8NNK3gnxQ7BfeP6sTg==", "signatures": [{"sig": "MEYCIQCESrGKweTZ0xd4Oxm/0M2EE4vKOcndephZ5C5RV4IKAwIhAIowgXdH0FOVoEByr9V714b5OQF6lqbEiBdnKqszBNfc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8JzCRA9TVsSAnZWagAA5DMP/1K+O6ET03HMpFlwAHjy\nxU4/C6QV5e8jLxO+rulhSl0vs3AayMRw1jEqd7c4943aGbi6wI9/zsROxpF/\nQRDH4Q3wYsRst5sg1BRPobkmoR9yscxKuhvhsAJzYrapCCQlcW6t/hJWA22k\n8DyYiOqLqygDhpk5PMuklipEfKk7ooPJS0hh31odi7PbqC9Xm2/pHZgG1Zv1\nppL+eZoAJgs5wdkKzkoHZNXDwnpaSiKKlucr+wmYRG+LTM5OmavtXNAw9e5h\n2JWA2dO9hz4qVnmaFz8oBV0fXP8MIkW7nMeUG6rJuiwo8pGT1D3QihOU6mer\nc2ycQ4PHrEGoiDP5NN8M4IswA8XZBShfljQIxlpeJbaWJyvuOHEacrAatjo+\nOFi2m2Y9FFa4XaC86DBWD94gKDZOU+eK1niQsY3dwmXFPeZ0DgxHhBYCG+Cn\nc0TiGe0fYYhxvr61kEneJDSASGHHAvCZmnnX5HmV+E/8xTtUA9Zo+4bZ7+ET\nZN8Hksjgj/haN7s0oQKou/0GIrQt0vdLl2wAtOOhdpCGiMWWSsn2MK6e7Y3V\ngwMa2zZNvP/JXlSTRzSpwf10pmiCoaicdxdIqm7PuDm9rmol76WOGmhpdKDv\nfS5DAbb30cOwyIpbozR+Ceh+gfR0kZi7Yyv1q+J0WQaecsYoq2IpKOCklJ41\nDSBI\r\n=9flf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "14.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.0.0-next.0_1607189106952_0.863632002173844", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "jest-get-type", "version": "27.0.0-next.9", "license": "MIT", "_id": "jest-get-type@27.0.0-next.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af6f58c4c4bb12c4661501e6f38e326a9308d81f", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.0.0-next.9.tgz", "fileCount": 4, "integrity": "sha512-uxp8fig6f4S5BlhM5UzyEAnZupYIH1bhRmD3Krkm3UA73zIsAAIsmGuVSBDcVBXbpQ97bTYTgEkVdCtMaMOBSA==", "signatures": [{"sig": "MEUCIFdgJlvEeyiEkthsNk/jl/7X0q54x01iDVvRA4I1L4+VAiEA223lyyD0rM0SYQjALVXYnqPkMdUdKkRwlnpvO0E7Xkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOi9CRA9TVsSAnZWagAAPrIP/ijfqwf9aWn+5E9zh2rC\nGjZTmxgEp4vgCEh+f0wfxwVrFbte8fddsDXQgkFpfTAzaBH29SVmgvq8eP7+\n6Cqwil10kG301MKCll6pfx/hVdMlNTFMepxkmgE1AvvTApGaFUJCbIrjpnZ5\nnwdMwe+R/OkS7h1b0oWKsu5johCizI48+XoorOt1bVwTOoXld0EriMBAEeI2\nsP8oNfVmz9zeKFEypvSK7oakHqwAmoG0hJu6jTroHBequxGuHODXs77IbW/z\n1Mblh1a2+q5uD7UXhQnj6Az6wOax2tnfXd4M57RgUlC0Ze6u2QnWm3BeX2fD\ncUj5YQlMomI9O/+ClVGwY/Pf3u2p+wmuRfR0oSEIobTOCUUv41wx5ESES4Kz\nyydhHp3/CJerxhRhMKl6F1KZKvfvuk/IKnLah3PoHiLLMfVNGmA8suhqrkJo\n1hQBKbSmEyVNrJgfQuoMpCLc/b+B4H4vpFeiRKWtGTo6VVV9TVtFfkJF4noq\n3h2/7q8tCg7TKaqbgnBL0ZQt864ETpdqZXaXYuze1tgalzd7GOfTzfY6bQT4\n3xdyW7Xnm9zDQj1ssqswHmfN8RA/bmhMdPAs3XmmgHuDM7BaBzH2XGrJGl0f\nhb86LNEOeY/8ucxdbkJXecti7PUY3mEf8RJWY3piBso9fH2Lel6YzrVXmHmN\nazKm\r\n=gi7n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "14.16.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.0.0-next.9_1620109501491_0.4278957451975498", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-get-type", "version": "27.0.1", "license": "MIT", "_id": "jest-get-type@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34951e2b08c8801eb28559d7eb732b04bbcf7815", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.0.1.tgz", "fileCount": 4, "integrity": "sha512-9Tggo9zZbu0sHKebiAijyt1NM77Z0uO4tuWOxUCujAiSeXv30Vb5D4xVF4UR4YWNapcftj+PbByU54lKD7/xMg==", "signatures": [{"sig": "MEUCIFB29ph4ojZN9vCuf7ceiHwFHbkaeQ/R7exZagPp3p5NAiEAsqsyb4wWq4QoVCW41ZqfFWJzhLMwNcpZoU/Y92BlJYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwfCRA9TVsSAnZWagAAXP4P/R7JdG/RihHDnWt+oBoL\n9dWVwSLpLVspqUzu+cRAmUVrkeoHGLss0px9vsfSnZtoOVmadlVMFw/8oBEi\ntvuwjo8IANFXdbvd7tbaNc7qC0HNaNYloI+uHfOH0+VfvL+NbR57eAiOsipC\nClRurQnOPa4Ou/kYF15i7C49//+sS+cab7dloTQ1dfhHGtd4o8P3zvlRteCK\n8JN8LwTa+MRG95nVHANO40hS63HXFs+NZGajlqGHlTS/HDWSbrRw+X3f5qVy\nW+2ZVaq+b/6dzXSvVprde9FTtz9MeERJTk6VOihTqNVTRmRjUYw/Tf2O2YYL\nUtVETZINS1PeJBzuIclFvCGhPDxtUCff1Rz+9+KNvzxSmszgGMRxuyjXwCoK\nvnwJ3+VFvpUe8fMHqz3tZh2yTH39Jnmlbhdn+0e+Kgym//1W1XHK6dYMnIUG\n/sNL4Ag8apw+5DDZeJoE5UrdtjsiQ6Ey/2G3bbkftCijiobmvc6R2WD/Gg95\nhvIP/29tNc4xSxkoXJUZB02xl6yfpjfzRW6Jqt259xjBlZFI2HdoInuqRaTv\npnkWo21+DS+bjQXCRg5zFg5OL1Pt6wzZR9imrel4oJ0uykH8WzNLp3tWTo8P\nRo7miP5K3eBW1lBvmYrdRrsZtwKCNtqouIqF+b8JamFxDJAl5s+Wdeu6Pstv\nBgr+\r\n=gCrf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "14.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.0.1_1621937183228_0.5622144213502156", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-get-type", "version": "27.0.6", "license": "MIT", "_id": "jest-get-type@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0eb5c7f755854279ce9b68a9f1a4122f69047cfe", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.0.6.tgz", "fileCount": 4, "integrity": "sha512-XTkK5exIeUbbveehcSR8w0bhH+c0yloW/Wpl+9vZrjzztCPWrxhHwkIFpZzCt71oRBsgxmuUfxEqOYoZI2macg==", "signatures": [{"sig": "MEYCIQDyY7T642njpMky5H53EFBhuvppjhrYgrmivnaLTnqUJgIhAKHDGlEdjgDiQIwZ0sy4vLRxMebOy7Vo3diDzmKw8TUt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAAD3gP/Awk0iJad2j6cPT8ojY6\nk9DfJannhksUEHMxh2+Vx80xcbilXZIC9lM0c8gvSOn1VOSaCpVJ4nDqnQxp\nOdBz5QfIP2HiiHOtcBDfcbNIINfNtP1A90CCYyThRbYv85cm3cqqwAYThoaL\n5iAolT0bbYMCw7BQTB0xKcAr5hs8sDpZceg1Y17iQSbe2Gu6k/Os8BVc85RD\nnpbdJCOKOpA2F/h7h88kV+AxZNWtqK6I0pahLbuX2Aw8/GUSFLbXO0kK+PF+\nxF70uO9+qIxPWbbLQL7pGywiqLbrq8kP91y+3dv1jRJOJ8jSUjyeszVk8oZG\nQ/7s2Za+pAavNL9O+AO+m9GxXTh78qDF/Mur31akdHCMh1hPvoGkc0P1+LrA\ngE5HyMOMvAC7tfuTORdlGJZ4yvVEJTqGLIqkCZ0yVjPASsPo5D57662qvOof\n3yVffIBwLwcopKhoFV1ACm41Hh+AA6LJPiLgmnBNdj7CCE0gjxvdLTAyq7fn\ngzscCPGFrFUzcXPWz8JAoR1/PbbBAf0Mfuds6ayI3ebgONRxrnxjJ1fcleSY\nwwL5ClvA8ckqhTvU+Ao7vcfazCxsFrwfTSc69zpY942+kZHJ4dWUxqnMsrI3\nA5U3y5d1tYwMoQPJ/gfCnq/D50ExE4qfg/nr6L9FUfl5El3uWlvqt5Je/8XZ\nDTAK\r\n=huHw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "14.17.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.0.6_1624899931239_0.8842454094331018", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "jest-get-type", "version": "27.3.1", "license": "MIT", "_id": "jest-get-type@27.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a8a2b0a12b50169773099eee60a0e6dd11423eff", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.3.1.tgz", "fileCount": 4, "integrity": "sha512-+Ilqi8hgHSAdhlQ3s12CAVNd8H96ZkQBfYoXmArzZnOfAtVAJEiPDBirjByEblvG/4LPJmkL+nBqPO3A1YJAEg==", "signatures": [{"sig": "MEQCICxcC2Czo6SCChzbPWjo3VYMwzsD7w9bWUjENkJcbShBAiASKW6eqF+8lC2RQZKF9ZnP3NVar2NcRwNwvjZ6dKWKyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3753}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "14.17.6", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.3.1_1634626651435_0.9811955069685261", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-get-type", "version": "27.4.0", "license": "MIT", "_id": "jest-get-type@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7503d2663fffa431638337b3998d39c5e928e9b5", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.4.0.tgz", "fileCount": 4, "integrity": "sha512-tk9o+ld5TWq41DkK14L4wox4s2D9MtTpKaAVzXfr5CUKm5ZK2ExcaFE0qls2W71zE/6R2TxxrK9w2r6svAFDBQ==", "signatures": [{"sig": "MEQCIEKjnT6oB34BIN1Oi6lI+oxPt3GTOfIK0mshnTvessp5AiAM4Om3HaJ0egRCeE27jeSXmuel4Cb1XRR0+4gm+Z7cIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAA7QEP/jq0uUQxKs5QBDDlidQs\nxyjyHPB4kul1ZZOIzny06qOk188FepfVmr1dLyi6nuyG8EBcpCITdlbHelYL\nFKmc5yV5XlSwxuqzO0qB7Z9XzWDNpQcZsQOcDTwNIeu2HcgmTmtZqlksGtEC\nlVgtRDe/mVcnuHsovpA6P8UA2ejys6DmmpG8lTtCbQJYvumYZyHnsA6agINO\n9GN9nWeRH5lVefD4vr7hnZz1b18B1zDZl0BpiXLNBOsjFRObCNG7j9kMo62x\nRKvebZW8Bvr2CaM3xkMvnt2cfovqHOUrYdu4l67G+tzZZn8uVmZv96Lcz8hV\ntbOMv4zn6LAFK96drFwILpuUJ6qV9B63XcwWIv+C8Ja0+z5RW0HR+d6zgknE\n+j/D+O5K8QdSfmT8zonB/ToGQPXaSDNehxaiQAf2fzBSHcbMeG3Cf5Lc+3Nl\nlq5UgtUYxQw9odliwk3bxfn1DMqWzI9XNADYS0vsEUzRadIFDC8sXQ+MEsQj\n8lunWgRNXjFDpY/LcipqWpj8ZW+ByjKAanE9GswQnXE2I96vPPF8Q/YAidm5\nVTPBVGV5Y/5gIiTEmSeRvvcdbdQ1mJgWYQI6XWq626/ZXTol7vhNbBrMddFk\nD/uDj5tldbmrUKD+9l3Z2m6trlIMi9YlRI6Nrf6A44UkkKs2DKNSm4tyUTtA\nq84X\r\n=6LQ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.13.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.4.0_1638193014605_0.5244554220726263", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-get-type", "version": "27.5.0", "license": "MIT", "_id": "jest-get-type@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "861c24aa1b176be83c902292cb9618d580cac8a7", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.5.0.tgz", "fileCount": 4, "integrity": "sha512-Vp6O8a52M/dahXRG/E0EJuWQROps2mDQ0sJYPgO8HskhdLwj9ajgngy2OAqZgV6e/RcU67WUHq6TgfvJb8flbA==", "signatures": [{"sig": "MEUCIQCawjPhXakp6hlqDwNIE2zXXYoPBcl5NB9TErwjRLocuAIgcoE3NabM6hKNtrqq91lr7ip5zes2raYGruML0BN4dHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp1CRA9TVsSAnZWagAAXoYQAJ8cXM/ieW4L8PGqWUcE\nUJT5IDxkayhFITOmxas2qOQvaucsA9bhb10Pn93d/EN8nOZiJFu7Kyu7ms+G\ndnWS7+dUx+SYrPqU5OOs2a32i/z5jVny9GOYutbAeOHvbK2mebRsKorMD5WT\n3SHTCWyGrK8D2kCMMtdAxG7qjGS6c5UaePMCEUlwBsp40Oh6QZ5IqwRgXvV+\ncaP+bVxK43pS51vqGXPQmjH3KE2s/ZKc0g0P6zAu/J57mAWSKVyihpMRWX07\nBzDv1TBr1mSsKoZ5uupWLcQ8ZStC5w946jPdAuE7ExhKVvdp6Fms3XcIWTQ1\nN1RnXjNDJmKbRr5Ao9/I9PkMsJ4t8iFBaiMkoca4rDIt6hJaq05L11YzYciF\nFiuyXHsdcmZLajqOQbLgavmeYEjRxd6ZunAkd0rdzVodawZkU5CnWUBMh7vH\nn70dLi3Opj1k/n0zXVGebk6YNQQ9fwtZjZq/YRzwmVV+xI4dYmoHtrck3kFn\nF/Bf3ggqigGOm2zkMBkN0V2la9J/9s2rAFBmZJ1wctSsCTyHdZxIgnjMUCmq\nICZFT6T2OB3N+McY02zYPAEsh1VcSX1YXqvvpqjcWLhkOWiYyAgLhU6Y/JkG\nh3NpPKQiDGRNlvWsxMZ8QEpQzfmykZSXbt4PVX9EQnXltwlMo8aqn/vljsK8\nFL9n\r\n=rgN2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.13.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.5.0_1644055157728_0.5591233727217895", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-get-type", "version": "27.5.1", "license": "MIT", "_id": "jest-get-type@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3cd613c507b0f7ace013df407a1c1cd578bcb4f1", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.5.1.tgz", "fileCount": 4, "integrity": "sha512-2KY95ksYSaK7DMBWQn6dQz3kqAf3BB64y2udeG+hv4KfSOb9qwcYQstTJc1KCbsix+wLZWZYN8t7nwX3GOBLRw==", "signatures": [{"sig": "MEUCIGMVVtg9T9TN3nPuCV0hHigia+nTQKtfBJQKR1iEWs15AiEAguEH3M7VzfisiXZHhy0J0BEGzz+YSZU0BczUFk4QupE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktbCRA9TVsSAnZWagAA1noP/3N8lB8qTH5Z7XyPSWrI\nV1itQ1Zcapeju0T7R7xUHayzBVRz1B6t2rSHPvdqEutdky/FNcw5lbIoHdgd\nWz+gh3otewPInzAZh+nDr9FXtragNqY17K/Ks7DoFRiq5asilz3IVAPz1etQ\nqOFNOM7J+9DeNt4M9AE+6R/A4eR2esrQhzaIQ8okXYr9LkJSJ1mTKD8Glrfs\nhqJBXCYaHyyf9D9RDvEQJeuus3lLIgZWy0S5Y+4qrTiS6GMKhQoXh2g5nNKz\nDhjA2paBuzz6RslGjZoPX2ymP9wA0Aa6392pafzQtm+6FrrDkZYFTMeQAtqS\nweN0mpZlrfQOuE3EG6W9fNPSKJo25+x5mwHRRr8kI9rh1bMyw8Gs1GcRn/sn\nmh6lei2JIvtbGu1YnVc9m24mIs+TKi/qWhGY55M6dfwlpsX2bcyMSpfqAdZl\nqNUw4n3llO+kvmguEk6iPi8Iit+ceBvyd+Y97le3jMfqh1drg/vUAjm1/HcM\nTc/r//Nin4GObqWRaUdjh8BrXBBYm0XGG0oCn2LpmQN0wcpE8l+n9iKob/zG\nzR5KVDrZWHIVjwMe2Jf1DZ/zvSFwa5PiZcpxnglb/XdKeaRI6saD8DiBSpci\nwgs+67XPLVc9VYktjcEPomwBxv4sUqJ4sXMI3Zv58aXj4SYfpWorRp3wqNoj\nh6P8\r\n=kURv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.13.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_27.5.1_1644317531804_0.6288969333740926", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-get-type", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-get-type@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a7abf178a30f989f8b6227a3ed02ccea995fe010", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-28.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-p6Z4kdDL2bKbVT3ma3Cdlu5FnNW4RDCNq7qxd7XpNx3ot4eeSEZkdu1GxbomNlwqh9AH+olrwnvcJjqXQOP4HQ==", "signatures": [{"sig": "MEQCIFFOFGAjouUm5M9IXbaxzPxnblFYcOUvFPxATSgopakqAiAizukNyunPJKvzCP5/Rup76nP0oP2Qqn8lIY55FOVq6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAAJo8P/3AhZcvUKDd1W6Bwj4mb\n9F2S5oAZcnQD+YQgRhcRJ+yp6BOiKrUAjfeZgsEGNUcjut3O0EIUkYbsLQyi\nhleT5rhBUMpZNot5VxF9a2XlR6p6Hl7UQA4Rn1MJQr+qT3xLjJx0K1zwiyL7\nIPPo1QUKI/saaisjq2Mu2BFVdh4PkVvhjZDC6mDJs+xmFrNVRFJuI9HenCdn\n4plaFojpXG1i6LHW3ykcnt1r/+JppXqP9qd8wSbSnmARskgWBYkY6HlUyau3\nGM4B0QaYtbQlg0NytJgSKtzmfVGYKiNZSTveXcPcgd56MuytVclV+F5s0wmh\npiJohu8fmYQwt4pwgOdiFqukBwfM0nMrPrlbGTVbZg/1EkVfbaPSoS26glS6\nrD2k07syoOewPg91xVv1rapcFbS6EhHjxp8kU0bAjz2sAQ/9wxnJq7c1Jgvh\nviAnxqWXa7pggkjjsL6PbA80eHc6o+WundYwMAH+azDtFm+lj3mAQ09MrpBk\nUJnJf+0HyAJKmM1dkU7QX6OtzHVRpY4O/apgHiMpJTJFb1bpwiw7cGdtKMP2\n5aWPb6MlHaHupKNQfZEPVXbMBtFaYII0O0F8j7TFrdO3AW4LDaXyaHQifnnX\nC4+F5QaBvRRpZgiJhXszm4qx2tWX4Ly1fChiPhZRdTMH/BsCPIwQeEvqRDho\nuJK9\r\n=fXxY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_28.0.0-alpha.0_1644517046134_0.3650150464450048", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-get-type", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-get-type@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd482f09726e492552259349c25033e03e3ad226", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-28.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-5wmg7pmvRhpCGjLmMXarPWGNgU3sEqmyJFX58hdq/u0yZQ0eMvLdwiVs0/CaRBDrKdDWA23nuE+112CwJdHGXQ==", "signatures": [{"sig": "MEQCID20TG9VLGeZZ7c5g/MXFJqQL5STOXr8MwByFks86llRAiAfUND2y+7ll67aJMgbJ7JLIx7brE4nGzwfbN1CC0cGcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfYhAAja+w730FWCiJipd9QLW1mbIXEGwmdrazl/vGjfyOgnXuaUeG\r\nBDWx6ATI7vgCrSEO0P8w/VqCckyykEazHUJynB08ChfxnxXL0fmfgtnmrfeK\r\n/gViyI8OByDxnUpEF6HtnPLPAjTeTb65OH0aOW9e6V0lEG9+U3jXJ7jzotm4\r\n/GvvTO1Q953/KQ3hOzriK8EfoFXsblz2C5q6ni0RAdiKPKfCJ6tj/qNy1T8v\r\nsu2avzAdr2ZaODk6lJ+2YHFWNRFXb3cYjSdwvDEh912UXIBP0aZ3jXe64Rnw\r\njpQ78YZoLkl25cE9rx+pWvo4yHX+M+ERtDloDRC+1oGcTVODGacsFdYEJ95u\r\n8hXFWSkvcJD87UEwQManF1spZoYS3NRaL3sq1o4ksO56aTzYWHYLF7liBWlx\r\nDZE+oyB1fgGcfzrZxybp94ld6Xe60QCPeaCaAIkjWIho36Q1Elv29lQOTEys\r\nxRz1ZWyhWQUGDVy00Yn7zexvoeUqDn+4sRHwpREr71eqROLIg3cC7I7kRTrO\r\n9VEC1e1LMuzP0nRo4o3V/4URCji6Zbr5liDg/aSdQoKuAGPoRoVnVnF/c50J\r\n8Z+dq9USBapgvrNGnSixpaOQmHEmLZeMnZRVgF93EJhmg3Ox96lg1SJbdCWS\r\nSo+WtQcEMoZ3hjmCvFGkqqRUc4F7ODBng4M=\r\n=WdRW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_28.0.0-alpha.3_1645112540345_0.48093274790667273", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-get-type", "version": "28.0.0", "license": "MIT", "_id": "jest-get-type@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8d36b49c151bc3a9e078a2e92d502e778ed49164", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-28.0.0.tgz", "fileCount": 4, "integrity": "sha512-754LtawzW+Qk4o5rC+eDqfcQ9dV8z9uvbaVenmK8pju11PBGfuMDvQwRxoPews0LCaumNmYHjcAwmkYINTlhIA==", "signatures": [{"sig": "MEQCIHBX1bqcFH52AjOBBmjod8T1HgUVq7dyUGs6OS53P0r8AiAqd9u0TijdI1P3t2iTapaiScstEa9CLt0vE9ILLPlbwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmps+w/+MUG2+5tg9jrgLIraFzVnmGbvlAleRRc1dAo41hBpRveJpaRL\r\nPW5ityReFjUNhTX2GyquxmlRcD7/3wbwNjduUwHQEuqQrPxK4Yid+m3jmz2G\r\naZZ7tdL4chzaPQCOO4dT2MeGGPY4mYRQzHMoxbw3QwznbPrargGwbrARV5g6\r\nFIYFN6LowjrYHyoEu3wA2Mt4V+ynohhJwjR0Sf1nL0ssU/rLiWZDAJ0EjtBx\r\nPtWtHn+CObFZNqlYGdVVVGFq4JhW9li1Be083xvR8ljnMmTWLfr8KR9xgZoG\r\nsqqdrW+qjMYhn4Jy75SHdDlNus06aS8fdJKvJo0uDXo17HFLZ915VqGSoC62\r\nLQgmT6BDqVoPeQ7GCyeliquQpQYuzyiulECsuzHq8+stMLPSgRw09Y+5iOPw\r\ncE/fhp0DlQEBqRkzgyz+htKVWXCCrhuXhpu8L7g6QEgTQeFkz0khfB6swcO7\r\nNEHi9b2Exouyz68TrPwVteDLpCu5R7UHpmUyvgutJxKHNDAlzHziFXwEkXAL\r\nWPQGHwxIe61oKkLvl8JdpKZzGG7kFfHhN5tct0KyqBq9RMhec0Cf0LcYVStO\r\njr/lcsNLG6fyAPdL9e2+xdOa/kiNJaNN8+GC2W4iS/NPH40DC3MQI92+KkcU\r\n9uTG0sSK6j8JPvS4Y3iLoXdcxdU2O9XFKck=\r\n=uPj5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.14.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_28.0.0_1650888481040_0.48169768784558187", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-get-type", "version": "28.0.2", "license": "MIT", "_id": "jest-get-type@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34622e628e4fdcd793d46db8a242227901fcf203", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-28.0.2.tgz", "fileCount": 4, "integrity": "sha512-ioj2w9/DxSYHfOm5lJKCdcAmPJzQXmbM/Url3rhlghrPvT3tt+7a/+oXc9azkKmLvoiXjtV83bEWqi+vs5nlPA==", "signatures": [{"sig": "MEYCIQCWxV19Ojzd5jSWkmhBOSHVW6KGybcpenP8I2m1JIYTAwIhAMzeEV2OzdagviVug6Z7Zz8pI//iHT2PfG01qg0AFoZ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowVRAAnQv/IwmT2Rd0oz5TY+IFrh4ZQg0OGzEgLQNEde6X+GW6EIiW\r\ngpE7w/IrphUH16DTcWf66uSq3FHIEgdBhjptgWIIIAh/EHhklF0LjnUIiJk/\r\nXETRYaB8lEzNXCrH/iq3G0bWfQVzR2JxQ53YAjIrXWfxKd6zBp29gKUTChvt\r\n4Cr+gVo3LTA/lAoD1XHVo1vySeQ12LziaHA/lm5RxelHL2XHtu7NSLAAmFvW\r\nN43gJ0TIlYJpFcaBHtgK2fq5H0eGwQ4oG/i6cmH5GltwCH7OfCxWP4Av/kov\r\nwXs5b+XxiXXNc6Bj6XZEH0TR9yWai9gPG4/mTVrH0UPfq7HXcZZl2TmyGTQL\r\nBF9GHKkfwKMTNawv0VpD1+dqOsofkQM0HKu8a1hTwsJ8OHqmlikiB71BXuBn\r\nkWcmDyWbGMtWU6TrLTKMBwlaaDga+Vbb2i3E2vyP/QprqWqzhW9D9igNFJap\r\nspjJtF2a6iFdxBmxTm80bV83u1ycBBHGF4/80qMSWh4cmTVEr6mk0PTdX+BH\r\nQ8k0xo2pdcNG+Wtsmcnu4/49qgrvLQ+7/LVlQ+6uiy/eWa54e+ux6YfogFiE\r\nCrI2aiU0/xiwIpGHOrd2BXlg/M6EB6Qd+A9MueE53ygg5tGjA5Er5dqvZcFt\r\n5py6J1GlQR8Mokpp/EoIwkV1izc/wjk7RVg=\r\n=Djav\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_28.0.2_1651045439954_0.9484072012091009", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-get-type", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-get-type@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af54f0281f3c0c5fb590310ec98db478cbd59f26", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-dTFHQa16YgpIaOjFEvcqAGAaLVEFqizhPmrDuhjLSc7kkQ/MjMPrk47FXRd++L94pmfnI4zYSSfx77vHsIGX6A==", "signatures": [{"sig": "MEUCIHppgbIjweOwNlSv24HZR9OuaiLM/5355ffxBessuh1kAiEA8o9EEMu9H+1DC9vddfZqA2CBvPjRAw4LQre+3P5PiGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN4A/+K+wWK9s0uifvPQyNwCTQ61oS68oljeXkCSLPdmuJteICXwvt\r\nRMn8mZJ81dPG3fBS22NVPe8rk029CTIXdxGhHo3cj3upkxX4iQCMV4G8RfSL\r\nM/tOUnaAXzQyNnUW/Jd7xIGdJIxMTV+Lrxg+HWZIlcQNVVjaALV6gH6OTliq\r\n88MdwRIZVyrpOpN5Qn6bIjcSe3ewpDoM2ksqof3PsPTH1R/7j11RGqtzy0R4\r\nIbef/P5HGgWqZnXdqHRMcmO3wlavY+fexcx0gLBhsiNE27YVZfckGsGn8Wb2\r\ndVgOAh2v/jihoTH875A1xXzeZnQVQ1qcKV1wp3AyWs4eiqOqVdraJdYAleqy\r\n5pE5HCsAxpVSRAXoxpgaw5HslFlI3JV3D574qsI/CY/MJ0XWg06CFu6sm1DL\r\nzR5Sv9QTaiNc2MeE4PLQ9/RVjuniCBvcTj8bLf87qyWIbyoCCSg1wByzwrvW\r\nOKtssy71aBZOJPjeBrAFGUZMezZ8AzCBQpcpArhOFvSphuyD526dNmGUMaL2\r\n3x8hglkw6CqDuLFl6qrpxlPGe8lYMA1H0hRVyYVKsefYD3xXQAlXKub2k+5s\r\nkXqUYWmWBJWDOjzbXlEtLbZEn0r/ir72xqLvscq784LOROhmSiNyGu2gbWlk\r\nKpvvfxyh6CDctmifK0VhRWOwBMmoaw0/bmE=\r\n=OkcC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.0.0-alpha.0_1658095625693_0.24009816943627138", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "jest-get-type", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "jest-get-type@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "14ee6d73084064ad7bf9c6484c296ba693c88c9b", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-s+yWxhB3D1CQ1px5mp4PW16GEocrwtnp/Mr4nTPQv2SvYXI5kXPzJ+3SdFMAuk7of2X9oIMSRdV1Fe7TWbIWiw==", "signatures": [{"sig": "MEQCIEon5sUa+gJsZsomZKG5UW7UanhwL8DVmUvwLYNhoqu7AiB0Y84QmkTHVk1J1lQAOQInQyCa8WSR4WmywUUXtT/V8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLcBAAmmfLIOmDv5R0y1KLJyKfSQ1I1Zajq1hzRaqDQ0fLc2UwkA9L\r\nnf0dv2RgRG8dJ1Ye9hJy9KqR8UChYw2uqaXmCYH6SE5AxZ4Z9dLs96RSPVMc\r\nmIGXYho/p5F1n7nFp680FfRxcxjmWshLzvcwHnDpGV/uRdoyd0MEMbPN05eY\r\nYoGz4PYCti7TAPJbetkaXdyAXD9FuUcEzNG0pw0ZXoBVYa+u4q3S5WGTu6XN\r\nGoTmUWSA8Hb5+nhESgfFSj3yKARzGKzde4B0hTnA9prQRnLtAeg2Qf4j/znd\r\nE8hC95g3J5QVDi4VK4m+i/ZQ9RMafQFd808Mf/zss06rMG4sUfdgdqRtZ9Uz\r\nSAa6DCjxmLOZEc0bb0AAQAhOQ954V1tn6l2vp742sHwBn61IWjNKjJVc7MBo\r\nL+jcpCOHBCFnqIbwTfw6R7fvoSwUfCfwszwFrV9gRFWLJ2oHnRNVt2yUvbpd\r\nRetpBIcf+pyWz3Nnx+p22CBBzK88DLqgeSJUgCXRM6KnVuGYit5LSv/a7s4m\r\n9rIFfmqmkNYyNcy1ASgsgo6m+55V2yXQgXGtvPkhIP0m/tfj5V3/u+CvzDNK\r\ndzkNSBv+LgGhkAE38BlWXwpM6rPhNTR94R7ucq+57T/XcJeksWXgDyOr4IF7\r\n0daOe/LNCNR8XS+2d8/z0xnbD2dA4+PfxFI=\r\n=Hzdm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.0.0-alpha.2_1659742345316_0.4364353303754003", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-get-type", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-get-type@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "99cdc101e6725ad2615c3c0af1c476856205f93d", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-1pZtOPR0YZPGSr718qOvBR2OH1ZQjq6FmA1B5KHBghzHRUUSKty82/21fAhSk0fLkUJDeenva/7i7stTmCQpsw==", "signatures": [{"sig": "MEYCIQDKRP9vrJssxkM3G1c+UW2cpCtGT1IMD3l79KM3kJyQDwIhAMBOY5evR9uNEc2E1TtZpnRQdMyzIG/dfoCwKg+kzt2U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo87g/+IO1TRJJay9mBg7fnsHOLSyS2BxwvItLc689AJNP34PvcxFK5\r\ne5XVMAVuwTyV9C+W5Y2P3R2Cm5eA6gXOMlYQp4xzFN8sFQpmb/2JelE0J7PH\r\ny5U/nE2eKN4u8hGGECciTT7wySJwyoB2J0OcRLr1YkzA3V7hdLdh+MmE8Rni\r\nEO97mKZ/2F9dwB2m3SWUV8K2Vy2Em3g4BBsBJpIBXY9o/xoFKmeOPmX/BQmL\r\nLwmaSfpZZ2TW7X6IixHs93VUhuiBQNTsvivoslDpdySWkpEU0DGWtgNaKllL\r\n53DcMJoaoGYnCzUshQH5ZwGKf6R1BC7F2nQDqVjRJP8FKHBmdJBjXwGjZpHF\r\nkm97l7iTyFxGZIR4R8dl/8kmykLFlvWfDPDv/rL6yB4Cnr6+xfdwct1akwTZ\r\nP9lwjnZG8UaIt3NUHo3Rv0ExEZftjOV1iGhYK0uhz5BGeNgMK4nP//XTJYat\r\ncP6zyU1IgIsFnZLe2uY1Bymf8WD0az5GWSU0/j0u9q/b0UmO1CoDp0MOfA1e\r\n6OKB9V7jbQ2DXk7j9flUldOw8V0WF3Rp5FHtOBZKq7ZneaaCLKNvyFPJRnxv\r\nFiIyaFcx7yAuBSxGLfpsWeQw48rGvwcQf1gOIxKH801iUiV4BWkBTyqFWMAr\r\nUZqmnyS9906GWs3Eg9anPFpsRJSz64zSgew=\r\n=miCU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.0.0-alpha.3_1659879691866_0.7400732719822694", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-get-type", "version": "29.0.0", "license": "MIT", "_id": "jest-get-type@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "843f6c50a1b778f7325df1129a0fd7aa713aef80", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.0.0.tgz", "fileCount": 4, "integrity": "sha512-83X19z/HuLKYXYHskZlBAShO7UfLFXu/vWajw9ZNJASN32li8yHMaVGAQqxFW1RCFOkB7cubaL6FaJVQqqJLSw==", "signatures": [{"sig": "MEYCIQCyD/Y1lFAsuz0mdRm21R03vegbyIS5pBi43vVWazd/UAIhAPjJ9BpqLvN7vhNwPFQJaKMiwh7hfX+OYn6aZjkue/Nt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBOhAAkDuCGFun/x7sOnASutuZ/HfMzBfA7l/uSOuCSjyROZipGaeM\r\nlT8QJKOPwgkbiCPzuKlgWgFPZWYQNbeL3twz6lrTyB43BPPtMiE65r/K1nt7\r\n3EyaJjtKDfh8EL2tV2M483NB5el+EWko8qQi5UAsAdr9XjwgoVILf6avwZHa\r\nDKqPux/90kawxfDxobOLK1nVyCmkVrEI4irSxCCvRaepaDbbtngT+yXJ/i0k\r\nb30QxMEMC7EOjOeyqW8Z4AXR9xkipJB+oXP3x+lepjtZAM8Y7na0AwKxTc5N\r\nNLHvxWLqMUkcYxpouea4oOYa+y1DwJYWyaUNiJS5TEypJ4jwmT7HXN2qj5BV\r\nynAhgkwfn3hcE4Ck0vdahlICEWgBGLQbsdbhsvP/jBDqNfdIeHxxTU2DCSwf\r\n/dISNZVzmsm6PElZuLYYrqcVgEhz6yhQauWpJrM+ZIqehPI5m29rX0Dx0tPC\r\nYrwCONNIa6GiZPin+XI7ieMV6DjpEJ+6gBedfQ4XtMN34o2RcE5JsfsHlhGO\r\ngSh7mN1eLDuRFxUsHim6ZvSOjn2BYb3NWas26SlQqvqbxJIZKsfhxMPUddR1\r\nxzgktlWivSKWkMgoYh1moI9yNlafHj8wUHA5fIUHd777NRuJo7XOWU2LgsmN\r\nd66R7Pzk722Bo/RyTZSlVgFwuiBcgYtT7x4=\r\n=W42S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.0.0_1661430804436_0.7691973086826536", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-get-type", "version": "29.2.0", "license": "MIT", "_id": "jest-get-type@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "726646f927ef61d583a3b3adb1ab13f3a5036408", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.2.0.tgz", "fileCount": 4, "integrity": "sha512-uXNJlg8hKFEnDgFsrCjznB+sTxdkuqiCL6zMgA75qEbAJjJYTs9XPrvDctrEig2GDow22T/LvHgO57iJhXB/UA==", "signatures": [{"sig": "MEQCIAJ2h4GiA6mnXCnC0L11nPsTOZ6ZvPJ56TxJyVHueDsvAiAQBdR8MlmfTAYyUdKl/iz5PxO3GgbOblyVl59O/Y7n2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokkQ//W2KtNso+UglTzxwqqfBX+VKddQ2g9IQyYrlas58j+J4IGqNG\r\nEE4sKt8lSzhzyZ2pCj71vtxnv9GfJuRtZ2X6dPqowp+CNKQsOv0rtSReax+V\r\nEu9O0Ym0ZrJo5XSdQ+NtG4ffqiryQQEG2bKwlsv2zjtFzph0E6LudneqPThQ\r\nN2wGlUqew+ArmfHdWTN47k/fByBhPaLduPePUWABAXfTDze/IywjAwMf2Z+f\r\n17jba8TdkSpxo6LRFnxtjSGiDhu/Gi/jndrn878XMTesA5X1TndWfT83ZO8Y\r\nfbqjKJAgIkVnbXl5pM0o7WjgDSh8D9eMvhtMTC1fVN2rVKZuUnxUo9sAeMEH\r\nt/bB+x+Rcuj9m1BH32iEDsxOe3XPmkmGDp8rLDb0D5fa0VvlrJylaZrh8rTF\r\njrSNcUO1Ng9Rj203UYT2FHZSg1CPBYUuCQ3VB/UhNOmXmr5kR68OVJuOmnE/\r\nkOPHlUaqG8Rrz5309UEBUOoOK/2OxSRw4Bv2dK87PSyUkz8lMwuxILUBf7o7\r\n15sYATrBtk3Ys9auXjS/1Pmp2Kf33EbaHC5tLOSgIZhXpXgbg2MNwdya4oq/\r\nomdIhETSUACAr/cmDGXuZUqpkSEmYf6XZK+LttiKzegei75gyj3qmHYPXRnp\r\n7y+TbQXzedbrMtIvmptqN1pm0Y8n6EsMGHg=\r\n=DxcS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.2.0_1665738821282_0.40745257274652014", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-get-type", "version": "29.4.2", "license": "MIT", "_id": "jest-get-type@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7cb63f154bca8d8f57364d01614477d466fa43fe", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.4.2.tgz", "fileCount": 4, "integrity": "sha512-vERN30V5i2N6lqlFu4ljdTqQAgrkTFMC9xaIIfOPYBw04pufjXRty5RuXBiB1d72tGbURa/UgoiHB90ruOSivg==", "signatures": [{"sig": "MEQCID/L1Rp+AB49IkUDJ+2jF8nfw2tI/+Eu27MhFeBg9rBIAiAFo0RDL4x5B/g4tJbFpwl0Td+JQ7WR088Mh7vH8sysuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHDg/+P6pveBAFfRx0k+q2iZsTy/m0hYR8cwu2qXAd4xHxEmySxHBW\r\nsnLbUmpMI626BGhy8/FahJWPOO5CH+SPFJ65WiRGCp/qIHobi3olV87NRpwE\r\nPIHsKJBheGwEAMqIJ3zhY+bj9c3ytJWy1btpN0kafwrqOnmnhnHPVTgHG/WA\r\nsDenLLOri9nrp9rVqZ/lS6F7wMtJJKenH+EjGCh4RbUs/vGJ25oj31sNzhca\r\nT0o2UOprLn1hHn+PnxjScw/t2QwRTbCzngrDMv8ibHWgnEjIukMQrRItjzdJ\r\nwBejQ7qxir6Sz0NoI2mtEClahQarXQkR/BU+0MNmAj04buQfGufuUmc9oixQ\r\nXclYLQEjUPKgJbXM0G1ypr134jpT1gA6oXeg6w6K7FtT8GkjOGgEnjJvi16V\r\n8EL2nO6caAC9VerwWkSvaSsIT1nYmJ2hbZsRqlYuHyya/rLR7ZCJPBdiA320\r\nxrsz2aXWfnMl7KwbOnHVNUTEf7lFVMV9CR2RhzccMKuUSjs/Q6B7n7DItFT2\r\naEy2E5Wvd+i3JgCeYJy1G7vr/IG/7plkx+ys7oi+8xKU6wbGfcAL3ateJlDZ\r\nOu2dxRQZU3Q8x9rWlZMSmU2HaMS+SFtQGkRR2tcHyjBygx5OfSSPdQc4/rvr\r\nRUfVF3Sy7BRIjEEzcKRZcYtvn0OpKceXmK0=\r\n=Oyiy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "16.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.4.2_1675777521723_0.4911634982248221", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-get-type", "version": "29.4.3", "license": "MIT", "_id": "jest-get-type@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1ab7a5207c995161100b5187159ca82dd48b3dd5", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.4.3.tgz", "fileCount": 4, "integrity": "sha512-J5Xez4nRRMjk8emnTpWrlkyb9pfRQQanDrvWHhsR1+VUfbwxi30eVcZFlcdGInRibU4G5LwHXpI7IRHU0CY+gg==", "signatures": [{"sig": "MEUCIQDIontY42YTH0nJHBAasgkBJIF15XLVAhpPnhn0SXuP8gIgSj+/BZbHe245ifl9aAT4as0w0u+wvJAGl0/e4wcgsQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MifACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmbA//bavaoHW+k7NiXCoFAzkwLtwJdz2AuA5Q/EibVP6/ozib86ys\r\n4qPZUyr6lXjPsKDRUDHKhjiAjh9pr8Fbz4cWLWmA3Rc8ShW79XioL3nzx5tG\r\nUL5vnbnDhLKpLIU7V19nADSlZxtul2xFtUjbsA9tZXO4VpykJ0uv2UwHqDwR\r\nd+dhQXCqT5itJtFAcFzdHjQI4R9rTAP4hA1gTrvFT9DWkKER6GGmNDk9/dIG\r\nKcY5UOAv5TNmFL5EG4QhLx9pOtUIiKynCes1hs3W9OoThv0NGlP4a1MM19MN\r\nd4w8wBJ6oP6wafzp9ZH1FPGcP49JvvW5PA6AX5/VTNHYr72JNDPtibJPRw+V\r\n+UhxGTXx6pve5Hb4gjh20WE3buok0s67oycQkWqZXmVCwpls87iTmW+iV3Ey\r\nN0bBH1mTls8WkJvBIw/5tI7mb56if5kjbzh8X5Q5c/aRpRXZ3rgF5uB7lo/2\r\n56ZcdE7UcJuqt5KT4SzXs4ZNjBTaS19tR1+i3gXMpCWpNGCkHJMNx6kTj8XB\r\nDLaoQYD7W1t6xuAIJ9grbtSilujIXeMUqLki9cHnmIF8XdGd8/tv+Qie/+a0\r\nasKXaJ6qa4dILq0MmGollXyN/Ry5MyjDTfCBAB72dFx1kj25fsnkDP0uol5f\r\niM8XErKQk0d0mTVrg4TaU7x5f1VbFd4FdGM=\r\n=J/Mt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "18.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.4.3_1676462239272_0.709502928514858", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-get-type", "version": "29.6.3", "license": "MIT", "_id": "jest-get-type@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "36f499fdcea197c1045a127319c0481723908fd1", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz", "fileCount": 4, "integrity": "sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==", "signatures": [{"sig": "MEUCIEJogJd5OD9bG6F/UJRDZHESZOx/pMDFq6ra/Y0kD/wIAiEAglLckbU3i0ZovCUt+GUpjhzCHJsghsMq3fxcfsDfGto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3794}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "18.17.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_29.6.3_1692621537590_0.04331502665648235", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "jest-get-type", "version": "30.0.0-alpha.0", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a606cf121a62c20006d1ca99397a8d5110503f05", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-gW5Vc9J64lhgthMb2M7rw7JaDJhZx+h7eJHmESO52WqffSnJQ9kROqh1RLLB7S/9jFJX8kdm5ZQObXKmWDTd3w==", "signatures": [{"sig": "MEMCIDiS+Q03qWF516Lu6PRSqEDoSO5K6sFx+ZB8JSr4HoaiAh9q08YTiKCezYYC2p8alfIWH9VMdvB/Z/IacWoLkNIQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4515}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.0_1698671619583_0.21362641538170557", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-get-type", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d40f5cc861fda66cbbcbaf9dffe14bd328d38d9a", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-5DQdFEE5L15cf8zMMHKDCUO89zwD7O0Rc/z/sLOL42go6YXvhpRiV2hVajs4r5aM7umqe2OURl54ReRLOH8KIQ==", "signatures": [{"sig": "MEUCIQDwD7gqleFVThQysyaCtRVCiBWHw/9ek2EutcRd0r7XjwIgD8S1PGZ22CAyltAHTsRdUMnFx17mHHfy0AqJA5f5Y6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4515}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.1_1698672766024_0.6407906911763175", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-get-type", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "c8e27da1f7db6a16e6dd77e5743b7767079ae165", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-iQDim3tMquq2s9tx2TDXOnms/jjXfu2RPXJIu/x/tx4k6ubs8t6Dh407TcTbNqOTBBdJjQVM4aWTHTzYky4BJA==", "signatures": [{"sig": "MEQCIFgofdsXkxjz3HUNOf3CQcbJsdOLXRePlkN6XolhTObbAiAsbEA93qMylo2fqxzXvLsOf2z0Iy+8gb3i4f8I1dp5Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.2_1700126894405_0.6158374583885837", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-get-type", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "dd84fcbd764d8e2b41965a507eb8e87ceedb9d2d", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-<PERSON>6<PERSON><PERSON>S51wUJf6t6cKhfWnt4UfStZjWGNsfab9XEEvPZhlTrM37eDO3ekcVJFuR1/g4QCKm9T8p0ob77dNfDV1zQ==", "signatures": [{"sig": "MEUCIQDNuYbEG2+17f5+vmz24fUkSriMiNGPMuPKPN6Wbc/p0gIgRQHfXJjX3u+Ngtu08W335pJB9i2YHBXc6aButi+AhC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.3_1708427327866_0.9943172982324151", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-get-type", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "cc12541035d85e98dfd19af60a8caab84392241e", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-FOuIavT3Gn4O+AWVNeMMlxtcLP57RdTLdip1T+3Hgz8grSoQR0sshhhUqnDBGbpYqffPR5EJWxApIa7tVR8JtA==", "signatures": [{"sig": "MEYCIQC8npNPw3H43yE0aX6hCdpX73inbNTH9V2A1i2l5wHCqwIhAKugQskU19KstNT6k1A8tX8pKFpqiCzpJQgmnhBOqarC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4560}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.4_1715550194231_0.6110525797499449", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-get-type", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "57e41a332a64c97e24c50d5fb5f1aa196bf215ac", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-ARPyHe90hG5fJH7nkxnmSCIUpeCIJcsGAUA2+a0MwptFfNJKBOYpkq/SnFaS7i994R50E4n9Beq8kUC2vVVVkw==", "signatures": [{"sig": "MEYCIQDfLLTmwEv6Pt5jkmWAE7rrLMT+gvLj8La4Gn81HvzZuwIhALsjmZjZiXEYf+7E5qsTl7zFvhvjXR4jXJOPDZVMoVUs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4560}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.5_1717073032926_0.6909963000680772", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-get-type", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "dist": {"shasum": "cd8f575ccaf00ab04ec78e1e791f9cad9bb2864f", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-lJEoQdCY4ICN6+T0lJ9BODKuqPOEpCv2NnJsEO1nmsK0fbWZmN/pgOPHVqLfK8i3jZpUmgupJ1w8r36mc8iiBQ==", "signatures": [{"sig": "MEQCICyQJH6P/hVWa1wskPyvo4VrAIaygl88KyJpY5ixkTvuAiAOKOK+EhM6V8XLWipreO6lH3zhH0x10luC/9WcBJAOQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4560}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.6_1723102975942_0.789057241940716", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-get-type", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-get-type@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "dist": {"shasum": "0f522210bd994c6137dbdf83635e6582a75196f1", "tarball": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-0Zt3y51PSw3FZxgRYyoC6TlKnL2Z2wMWS1NCQbs/CrmNEDRNpx9ZMF+UIByQgFcDT+fB3U/gwKeLtmSpNhJRNw==", "signatures": [{"sig": "MEUCIAp5kNRJZZ/WbUYwe2FukXZg5E7SMK/B2ZbgllYzsyX5AiEAnVZQhAjXoSOba61z9FdUPgwVIi0KtSzCNWIeMiX/KXo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4561}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "description": "A utility function to get the type of a value", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-get-type_30.0.0-alpha.7_1738225703081_0.6951377296985606", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2017-05-12T13:02:34.610Z", "modified": "2025-05-24T18:13:07.864Z", "0.0.0": "2017-05-12T13:02:34.610Z", "20.1.0-alpha.1": "2017-06-28T10:16:17.794Z", "20.1.0-alpha.2": "2017-06-29T16:36:44.526Z", "20.1.0-alpha.3": "2017-06-30T14:20:50.868Z", "20.1.0-beta.1": "2017-07-13T10:33:39.103Z", "20.1.0-chi.1": "2017-07-14T10:25:00.796Z", "20.1.0-delta.1": "2017-07-18T08:46:51.110Z", "20.1.0-delta.2": "2017-07-19T12:56:40.315Z", "20.1.0-delta.3": "2017-07-25T22:12:23.704Z", "20.1.0-delta.4": "2017-07-27T17:19:06.245Z", "20.1.0-delta.5": "2017-08-01T16:33:35.282Z", "20.1.0-echo.1": "2017-08-08T16:49:49.577Z", "21.0.0-alpha.1": "2017-08-11T10:14:01.473Z", "21.0.0-alpha.2": "2017-08-21T22:06:47.465Z", "21.0.0-beta.1": "2017-08-24T21:26:43.624Z", "21.0.0": "2017-09-04T15:01:46.565Z", "21.0.2": "2017-09-08T14:19:07.666Z", "21.2.0": "2017-09-26T20:22:09.185Z", "21.3.0-alpha.1e3ee68e": "2017-09-28T14:20:31.482Z", "21.3.0-alpha.eff7a1cf": "2017-10-01T16:46:44.665Z", "21.3.0-beta.2": "2017-10-13T09:54:00.892Z", "21.3.0-beta.3": "2017-10-25T19:33:55.938Z", "21.3.0-beta.4": "2017-10-26T13:26:47.782Z", "21.3.0-beta.5": "2017-11-02T13:17:22.437Z", "21.3.0-beta.6": "2017-11-03T16:21:22.510Z", "21.3.0-beta.7": "2017-11-06T09:39:40.002Z", "21.3.0-beta.8": "2017-11-07T17:43:28.656Z", "21.3.0-beta.9": "2017-11-22T13:17:27.311Z", "21.3.0-beta.10": "2017-11-25T12:39:21.039Z", "21.3.0-beta.11": "2017-11-29T14:31:14.846Z", "21.3.0-beta.12": "2017-12-05T18:48:30.806Z", "21.3.0-beta.13": "2017-12-06T14:37:03.946Z", "21.3.0-beta.14": "2017-12-12T10:52:26.969Z", "21.3.0-beta.15": "2017-12-15T13:27:31.105Z", "22.0.0": "2017-12-18T11:03:19.948Z", "22.0.1": "2017-12-18T20:29:19.827Z", "22.0.2": "2017-12-19T13:53:01.712Z", "22.0.3": "2017-12-19T14:58:50.289Z", "22.0.6": "2018-01-11T09:46:39.728Z", "22.1.0": "2018-01-15T11:57:07.964Z", "22.4.3": "2018-03-21T16:08:03.937Z", "24.0.0-alpha.0": "2018-10-19T12:12:29.803Z", "24.0.0-alpha.1": "2018-10-22T15:35:36.828Z", "24.0.0-alpha.2": "2018-10-25T10:50:57.142Z", "24.0.0-alpha.4": "2018-10-26T16:33:03.351Z", "24.0.0-alpha.5": "2018-11-09T13:12:33.725Z", "24.0.0-alpha.6": "2018-11-09T17:49:31.641Z", "24.0.0-alpha.7": "2018-12-11T16:07:36.961Z", "24.0.0-alpha.9": "2018-12-19T14:23:45.842Z", "24.0.0-alpha.10": "2019-01-09T17:02:32.989Z", "24.0.0-alpha.11": "2019-01-10T18:32:52.668Z", "24.0.0-alpha.12": "2019-01-11T14:58:55.616Z", "24.0.0-alpha.13": "2019-01-23T15:15:20.075Z", "24.0.0-alpha.15": "2019-01-24T17:52:21.874Z", "24.0.0-alpha.16": "2019-01-25T13:41:52.407Z", "24.0.0": "2019-01-25T15:04:49.067Z", "24.2.0": "2019-03-05T11:22:43.830Z", "24.2.0-alpha.0": "2019-03-05T14:46:22.478Z", "24.3.0": "2019-03-07T12:59:18.858Z", "24.8.0": "2019-05-05T02:02:15.565Z", "24.9.0": "2019-08-16T05:55:46.427Z", "25.0.0": "2019-08-22T03:23:45.006Z", "25.1.0": "2020-01-22T00:59:44.617Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.702Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.586Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.385Z", "25.2.1": "2020-03-26T09:01:04.037Z", "25.2.6": "2020-04-02T10:29:07.988Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.420Z", "26.0.0": "2020-05-04T17:52:56.030Z", "26.3.0": "2020-08-10T11:31:41.518Z", "27.0.0-next.0": "2020-12-05T17:25:07.126Z", "27.0.0-next.9": "2021-05-04T06:25:01.630Z", "27.0.1": "2021-05-25T10:06:23.421Z", "27.0.6": "2021-06-28T17:05:31.376Z", "27.3.1": "2021-10-19T06:57:31.555Z", "27.4.0": "2021-11-29T13:36:54.772Z", "27.5.0": "2022-02-05T09:59:17.873Z", "27.5.1": "2022-02-08T10:52:11.961Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.285Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.463Z", "28.0.0": "2022-04-25T12:08:01.186Z", "28.0.2": "2022-04-27T07:44:00.200Z", "29.0.0-alpha.0": "2022-07-17T22:07:05.879Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.482Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.102Z", "29.0.0": "2022-08-25T12:33:24.625Z", "29.2.0": "2022-10-14T09:13:41.438Z", "29.4.2": "2023-02-07T13:45:21.968Z", "29.4.3": "2023-02-15T11:57:19.381Z", "29.6.3": "2023-08-21T12:38:57.835Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.734Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.236Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.586Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.032Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.372Z", "30.0.0-alpha.5": "2024-05-30T12:43:53.095Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.185Z", "30.0.0-alpha.7": "2025-01-30T08:28:23.255Z"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "license": "MIT", "homepage": "https://github.com/jestjs/jest#readme", "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-get-type"}, "description": "A utility function to get the type of a value", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "rubennorte"}, {"email": "<EMAIL>", "name": "simenb"}, {"email": "<EMAIL>", "name": "fb"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "david<PERSON><EMAIL>", "name": "davidzilburg"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}