{"_id": "indent-string", "_rev": "31-0779b024c5a6b23e874acb99fb8cefdb", "name": "indent-string", "description": "Indent each line in a string", "dist-tags": {"latest": "5.0.0"}, "versions": {"0.1.0": {"name": "indent-string", "version": "0.1.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/indent-string"}, "bin": {"indent-string": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^0.1.0", "minimist": "^0.1.0", "repeat-string": "^0.1.2"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@0.1.0", "_shasum": "96aa451da9ca664dea4dd039165eb72833b38542", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "96aa451da9ca664dea4dd039165eb72833b38542", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-0.1.0.tgz", "integrity": "sha512-ByuN/NFwMlJmFg6CNiF0m0+ArZUPdUagfbGclDDKfaSydShgA5jQN+ThLRTLBZPIisPq22VM0+a9Kvb0VKdYpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMo+Y+xDqWQOuy06rMDKlg2LzOyteYiepOghhMimej0QIhALg46ciNVe8T7q5FPzh/mlJ+QeRBA41Eh42HiVu03Lkb"}]}, "directories": {}}, "0.1.1": {"name": "indent-string", "version": "0.1.1", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/indent-string"}, "bin": {"indent-string": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^0.1.0", "minimist": "^0.1.0", "repeat-string": "^0.1.2"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@0.1.1", "_shasum": "875e858ca925d1ac66be89dff9619c81f93503e7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "875e858ca925d1ac66be89dff9619c81f93503e7", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-0.1.1.tgz", "integrity": "sha512-I/7niU/eHKtHNYZgbbuLiuJLzCP2xvZeeocHwC+5loB15B4JDK1GWclrXVbpo7dH0fcq/AQBSyBqS0V67xagkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBe/qvLPM4oMgIAInerqMEb//jQbJGTkAwvxUOrK7Wt3AiAcCmnzIU/fOlJ4a3Au36NiYrXnDF8VtqMffZECPBjFuw=="}]}, "directories": {}}, "0.1.2": {"name": "indent-string", "version": "0.1.2", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/indent-string"}, "bin": {"indent-string": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^0.1.0", "minimist": "^0.1.0", "repeat-string": "^0.1.2"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@0.1.2", "_shasum": "cecfceba741dce491996e8c7002f46524d550585", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cecfceba741dce491996e8c7002f46524d550585", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-0.1.2.tgz", "integrity": "sha512-ppSgdJEz0exO+ho0mwJjuST1hAa5W819eD3n5sm68ACZPy0wdkpOjJr3SR4RJlSAiiNb31FJremRDzrdfUjmwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6Px6rSfJ1CeKn4lWyWZFU/N2VFxYbi0rdhRGNa7h6RAiEA81Ceppt5YgjX+SuDKI1KNbkTY4aO1JbpMunmt9V8vcU="}]}, "directories": {}}, "0.1.3": {"name": "indent-string", "version": "0.1.3", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/indent-string"}, "bin": {"indent-string": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^0.1.0", "minimist": "^0.1.0", "repeat-string": "^0.1.2"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@0.1.3", "_shasum": "39f058d423e0ab401ef3701bdca02496be298091", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "39f058d423e0ab401ef3701bdca02496be298091", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-0.1.3.tgz", "integrity": "sha512-KHHZlIytyyVDohMUqy/930t6n9dMY+jEjfQHy6ybOcbjjpRkBhmvkfFspYc/oJ/6MRaxP/GenAZd2dEqKIFDhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHW2zaNUV3JOQv1Rn1ch6gc0leGo/Ep+IcwJyGAuEbeaAiEAyBiCvZvT4nD7PcuC8oN+WS3+gWECKJdbjFO9i+MGL14="}]}, "directories": {}}, "1.0.0": {"name": "indent-string", "version": "1.0.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/indent-string"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"indent-string": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^3.0.0", "minimist": "^1.1.0", "repeat-string": "^0.1.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "5e00a9bc88b5994fd4767d29fb497121722cd570", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@1.0.0", "_shasum": "184c42e1d1d0c1cb00c6bdb2b424fd9a431fddc4", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "184c42e1d1d0c1cb00c6bdb2b424fd9a431fddc4", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-1.0.0.tgz", "integrity": "sha512-YwcCywwK2jwwi8DHJ4qrFL9QbeUA/CVXPyHlI6VPXVMUMX9l0YaIjmK9iESZPKaFEFHlELFTvdU9p78dkFr/cw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDaZnd8NHVBAQyoaAFxwJNJ1hIi4JyIn0cBFNKy/JTgAiA4k7UQ+aIB24yy2E3NBrx4Ws4lhGeXOG7+cy1o0K/TEQ=="}]}, "directories": {}}, "1.1.0": {"name": "indent-string", "version": "1.1.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/indent-string"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"indent-string": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^3.0.0", "minimist": "^1.1.0", "repeat-string": "^0.1.2"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@1.1.0", "_shasum": "c9bc3ea8b667511fae43152ba1a57bcd39ec192b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c9bc3ea8b667511fae43152ba1a57bcd39ec192b", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-1.1.0.tgz", "integrity": "sha512-TsdnlExMrSu85rajOh9C5eVhVOYk69D0bPDcdsTEfZWfbqOgN3PG4Mdz3fI6ILNyc8EmECS5vwX8Qj4bs8dbOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1r0booKG8gRVFcjZaVQg55PVDY2npjlyVJrYBlOlUWQIhAOyN0QUD1IyQMLlZOjau5acoAWx/ZYIhU2Pnz44nuPLi"}]}, "directories": {}}, "1.2.0": {"name": "indent-string", "version": "1.2.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/indent-string"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"indent-string": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^3.0.0", "minimist": "^1.1.0", "repeating": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "9d94356703126044b7d0cb80fddaedd35d987f54", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@1.2.0", "_shasum": "4d747797d66745bd54c6a289f5ce19f51750a4b9", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4d747797d66745bd54c6a289f5ce19f51750a4b9", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-1.2.0.tgz", "integrity": "sha512-xx5+gz5UT8x1mFx1R+LB22Jc2/qrI2VFz/pIuUFZv9OFzeLKNsjCZUeNWwGxoBkSXemmEaVFXSBaJhv+PnKvag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2WZTtcldY4jvX4JWDdNQdjFg34Irxa2RbiJD0z8S96gIgHzoQZW83OlXmxz7Mf+Q0S7ekQqo29PFH0C+U/0XolRA="}]}, "directories": {}}, "1.2.1": {"name": "indent-string", "version": "1.2.1", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/indent-string"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"indent-string": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^4.0.1", "minimist": "^1.1.0", "repeating": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "5f98faa592524fd18ff9e4cc70a700ccc92cf751", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@1.2.1", "_shasum": "294c5930792f8bb5b14462a4aa425b94f07d3a56", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "294c5930792f8bb5b14462a4aa425b94f07d3a56", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-1.2.1.tgz", "integrity": "sha512-diW47LfoZ4jkQ0VQfg3kNIKW/lrMCLeMiewIqvqXi4gjkwMbcU6cKaNGN3lSHpjrX6XqYnTsdpf/B1daCoSfdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdbdcw4dw+osm4lZ8Eryk1y0XbIvrY0YxGDv2hLjQ/MgIgD2Vbtw6KltcKdhgZZNm30/MWWRjkqUp5nuMO9w+hm4g="}]}, "directories": {}}, "1.2.2": {"name": "indent-string", "version": "1.2.2", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/indent-string"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bin": {"indent-string": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli-app", "cli", "bin", "indent", "string", "str", "pad", "line"], "dependencies": {"get-stdin": "^4.0.1", "minimist": "^1.1.0", "repeating": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "ce73faa67c3573fa81bf88796b8f4915ba09593e", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@1.2.2", "_shasum": "db99bcc583eb6abbb1e48dcbb1999a986041cb6b", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "db99bcc583eb6abbb1e48dcbb1999a986041cb6b", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-1.2.2.tgz", "integrity": "sha512-Z1vqf6lDC3f4N2mWqRywY6odjRatPNGDZgUr4DY9MLC14+Fp2/y+CI/RnNGlb8hD6ckscE/8DlZUwHUaiDBshg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHyTfwbY3TRRlvbhB7qdCCEOEjtlZDzdWBGWkdsFUHUQIhAMH0lG4iz3mUIAdJpR5PzAF0N29lSrzYIFEi+4uU8aBa"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "indent-string", "version": "2.0.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/indent-string"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["indent", "string", "str", "pad", "align", "line", "text"], "dependencies": {"repeating": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "3a0a1bf61d9cdbfa8599192b9e806bf9da83adc4", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string", "_id": "indent-string@2.0.0", "_shasum": "af509d6b6456f9ffc3aecd3ad52a15906bbb5826", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "af509d6b6456f9ffc3aecd3ad52a15906bbb5826", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-2.0.0.tgz", "integrity": "sha512-hMpRirVN1546nnT2yVPndt8xDUGX7aotrBAQ3hLgLGDtwazMRRFYsBHEMrPiBVwxnVpAt52g78hSwwGv2JaBwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXcFNAEN7WdcP4wLN3y+6BU01AqfeA10RXdTKpocqYuwIgSKyIH8CX+r3I9uO6frNzoK2R5feERPTrtXGe/UanpQ0="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "indent-string", "version": "2.1.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["indent", "string", "str", "pad", "align", "line", "text"], "dependencies": {"repeating": "^2.0.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "649f7560df23a8fd8ea330bead5d7d0058efc6b6", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "_id": "indent-string@2.1.0", "_shasum": "8e2d48348742121b4a8218b7a137e9a52049dc80", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8e2d48348742121b4a8218b7a137e9a52049dc80", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz", "integrity": "sha512-aqwDFWSgSgfRaEwao5lg5KEcVd/2a+D1rvoG7NdilmYz0NwRk6StWpWdz/Hpk34MKPpx7s8XxUqimfcQK6gGlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsiBhnyTBGjlQv52LoaqQAJQBvlThpk5TjqXwd17QF1QIhAIyRR15iF5hxZEu0g42Qj9h6T1ZDZJTV9hDvEti6xRjU"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "indent-string", "version": "3.0.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["indent", "string", "str", "pad", "align", "line", "text"], "dependencies": {"repeating": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "0d6eb0109ffcb578aaa6e5e72f81eeea6fa48fcf", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "_id": "indent-string@3.0.0", "_shasum": "ddab23d32113ef04b67ab4cf4a0951c1a85fd60c", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ddab23d32113ef04b67ab4cf4a0951c1a85fd60c", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-3.0.0.tgz", "integrity": "sha512-O2MGe6O7VuoDwU+xVeHuTuUCe0EDfxrPwRXB2+PgIIFVfZlfP1DMn7nv8c7Ia15LMrVZMN+hzq7Frzb4KApIYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgwGSpPxrTk/gAAvHc+Dw3uj5R7CWgvlSugq9TBpu38QIgWmTQQc0LOfvgMyxDogIEcx8+lw8e4ECqQxupH0ArCm4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/indent-string-3.0.0.tgz_1466711668282_0.4365430613979697"}, "directories": {}}, "3.1.0": {"name": "indent-string", "version": "3.1.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["indent", "string", "str", "pad", "align", "line", "text"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "1abb8d3dd950a6fed2008cb960d7f8466cce4cb4", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "_id": "indent-string@3.1.0", "_shasum": "08ff4334603388399b329e6b9538dc7a3cf5de7d", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "08ff4334603388399b329e6b9538dc7a3cf5de7d", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-3.1.0.tgz", "integrity": "sha512-dPdzpelBvGyi4vQ3CNhSJ86Dl/IKZ9Ggabsgu22R9jRWTwSKdRKEVZbYM2aIwOBj9vHYqgKQo88l6RW3IDL8qQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAqyJwe3NI/1zEJscE+bYRkAeUibn3L3xhEMgi6AhWqmAiA+3b+Rm0MN3EA5P9cV/B8A1w/1nGlBmtXKipp0Sfbxsg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/indent-string-3.1.0.tgz_1485362603670_0.8533929402474314"}, "directories": {}}, "3.2.0": {"name": "indent-string", "version": "3.2.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["indent", "string", "str", "pad", "align", "line", "text", "each", "every"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "458eca3f626b95bdcff5afe30d1568bf76889920", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "_id": "indent-string@3.2.0", "_shasum": "4a5fd6d27cc332f37e5419a504dbb837105c9289", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4a5fd6d27cc332f37e5419a504dbb837105c9289", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-3.2.0.tgz", "integrity": "sha512-BYqTHXTGUIvg7t1r4sJNKcbDZkL92nkXA8YtRpbjFHRHGDL/NtUeiBJMeE60kIFN/Mg8ESaWQvftaYMGJzQZCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF9KSmxrEnPqgdtW7CyRqLOQVRoDlQ0riJEv2t6TjR5aAiAk7BZIWbnIm/S0AxvNIATyAtfRC5NH4tmmAz55+E7xdw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/indent-string-3.2.0.tgz_1500831460303_0.7046717412304133"}, "directories": {}}, "4.0.0": {"name": "indent-string", "version": "4.0.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["indent", "string", "pad", "align", "line", "text", "each", "every"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "99280aa24669a3fab303bb231d6caafd7d5029d3", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "_id": "indent-string@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==", "shasum": "624f8f4497d619b2d9768531d58f4122854d7251", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz", "fileCount": 5, "unpackedSize": 4398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcttZBCRA9TVsSAnZWagAAw4QP/RLhgvRhH6lu6c73/OqW\nwqtS6k3cxc3I8oyxGMwBPsk4UTEkoLK8Yz4lrhs6xrK+yGKlChqmACe2mWLb\nBTFBIdSVejTyN7sMRm/UFfS4FwQ4Gz1mlTT/FGR36LAuRxrU2EmExVY2Ser2\nbsQyODD7cr5a3H0iRLFCM7TGOtIy+t5o4l3ktbyUclbKLakyk0SNI1SXHMrA\n7ggcy9TNx3gQ94AlNONra/F8S67Kz2xMqSrRuZQ9MP/awfcpyVDOZssr2tZq\nYF7/obaQjCRswMykj58Lo1gPEjwQeJkM48zzGuvNtFgiyjpPpX7HvZMXfjVH\ncMPcOaqpBj/wbAvCFeCkaXl29ReznD30iJOrqg0kdagvshRbx1S8SFDDoxwR\nJCCxQv7M2D8twLfnhPY/hEIwa2VFLKHyDi/v39QeVXaVsuGLvrxZLU3cIcFS\nQ8MgS6DAvfoo2biiHfvLKf7paEx1COBjY2d1p/APi3lHPlTNeC4QpOOeSvAt\n2QS0dgXOXaE7LijnJRt9kIwgoljFOgs4LHDU/ZmyR3dpjTQnrH84oKY5zJzy\n/XXouR8TglSkGruwrnoTkJ0Ro9erMGEKt5P+UqGaVNvL2Grmsi1UyW6auQbo\nQ4zoS9zcmSBBAvHhtZGxRDpr6lfnAwg4SSluYLA/1Qy3D4EcYIlBFGOI2HB1\ntSXY\r\n=83ol\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPHHuz9DpZOHrB3DpJ+kS0ACff0FCj390y/7sXNkh7XAiEA3tjpHtr67ThClg/N7z8mYCV2WW+XizZVUQN4/xy1ZPw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/indent-string_4.0.0_1555486273251_0.043579973384971105"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "indent-string", "version": "5.0.0", "description": "Indent each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["indent", "string", "pad", "align", "line", "text", "each", "every"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "475241abcb055eb5223d51d26fec37df35a36a8b", "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "_id": "indent-string@5.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-m6FAo/spmsW2Ab2fU35JTYwtOKa2yAwXSwgjSv1TJzh4Mh7mC3lzAOVLBprb72XsTrgkEIsl7YrFNAiDiRhIGg==", "shasum": "4fd2980fccaf8622d14c64d694f4cf33c81951a5", "tarball": "https://registry.npmjs.org/indent-string/-/indent-string-5.0.0.tgz", "fileCount": 5, "unpackedSize": 4744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgexZ8CRA9TVsSAnZWagAAQJ4P/RZ2eEG9N/dDoVOzXyy2\n77v8xeZG2bYW3w887CFvOWlYqEcFK6OYi3888QZuPJdsa422xt6zMmQa9k8v\nIdXt8B8JmqjspSF/FZLqHg/wjNRV+WqRWwjY6NP20RLCa7perLJQypUTOxXR\nFe0AAxNkyOVJ0t1zqBniIUC4zXsaAPrf9i1rxfUJfXZTlzJKgNIvNtnEDqZ8\nMX5FP7Ewyww1nh6Zane0203knnU8gW/JqKyw/HI/C52AoRO+gm3FINncR7bN\nWKVhtxdPzuHASuNLY3RuQlrJf6Lw9uCttUimct1b+dcT0ifoBWyo06mrkpLX\nqtErrGHofwOJpbP20hCCbARg4Mo1CO5iWmOWyPS9QunuJtIKf//JyDO+Q9MS\n2LCHzfdqrViWVt+uGykt26Z0OiXCpZJg8ucPaKFs7RJ0KSLE1yD7kd44dZbj\nI/8s6Hdo1PoQVaBOAqZNOpAU258drFbwfczueVFueXF1N44oHm0Axq8UL3nO\nfzFRaGoFBIuljCRK6uqUMignWjDtfoSX3DPZdFsArl9pA0SNrQS2aoL6WBa9\nYHPyJySK8iMWqzdbI4+Kj2JtRJvYTgV1VKsCoaGflAw0Xgnaa38335RGxBhr\nEOubNY3XB/4pEupBLK4pThCyxiT0KhytkWLtPpnfyRtbFxOuIVPbfC4KcN9Z\nDemq\r\n=oWaW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAMDsMGwH5zJ+WXT+uQZGc24moFI/G3IsabvDWk5nPIjAiBNIlSNewo9jmm8DawT8mu7xk9ZzHhg6q7ueci1UZuSrA=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/indent-string_5.0.0_1618679420310_0.18581616430205994"}, "_hasShrinkwrap": false}}, "readme": "# indent-string\n\n> Indent each line in a string\n\n## Install\n\n```\n$ npm install indent-string\n```\n\n## Usage\n\n```js\nimport indentString from 'indent-string';\n\nindentString('Unicorns\\nRainbows', 4);\n//=> '    Unicorns\\n    Rainbows'\n\nindentString('Unicorns\\nRainbows', 4, {indent: '♥'});\n//=> '♥♥♥♥Unicorns\\n♥♥♥♥Rainbows'\n```\n\n## API\n\n### indentString(string, count?, options?)\n\n#### string\n\nType: `string`\n\nThe string to indent.\n\n#### count\n\nType: `number`\\\nDefault: `1`\n\nHow many times you want `options.indent` repeated.\n\n#### options\n\nType: `object`\n\n##### indent\n\nType: `string`\\\nDefault: `' '`\n\nThe string to use for the indent.\n\n##### includeEmptyLines\n\nType: `boolean`\\\nDefault: `false`\n\nAlso indent empty lines.\n\n## Related\n\n- [indent-string-cli](https://github.com/sindresorhus/indent-string-cli) - CLI for this module\n- [strip-indent](https://github.com/sindresorhus/strip-indent) - Strip leading whitespace from every line in a string\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-indent-string?utm_source=npm-indent-string&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-16T22:40:43.749Z", "created": "2014-06-06T20:22:18.494Z", "0.1.0": "2014-06-06T20:22:18.494Z", "0.1.1": "2014-06-06T20:44:24.620Z", "0.1.2": "2014-06-07T18:52:31.553Z", "0.1.3": "2014-06-07T23:10:57.436Z", "1.0.0": "2014-08-17T23:44:34.011Z", "1.1.0": "2014-09-03T23:06:53.992Z", "1.2.0": "2014-10-22T23:03:25.576Z", "1.2.1": "2015-02-16T17:55:58.704Z", "1.2.2": "2015-07-20T00:39:38.599Z", "2.0.0": "2015-07-25T19:26:18.217Z", "2.1.0": "2015-08-21T12:35:12.837Z", "3.0.0": "2016-06-23T19:54:30.821Z", "3.1.0": "2017-01-25T16:43:23.903Z", "3.2.0": "2017-07-23T17:37:41.202Z", "4.0.0": "2019-04-17T07:31:13.363Z", "5.0.0": "2021-04-17T17:10:20.469Z"}, "homepage": "https://github.com/sindresorhus/indent-string#readme", "keywords": ["indent", "string", "pad", "align", "line", "text", "each", "every"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"tunnckocore": true, "guiambros": true, "jakedetels": true, "flumpus-dev": true}}