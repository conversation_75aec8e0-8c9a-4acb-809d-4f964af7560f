{"_id": "picomatch", "_rev": "35-9afa65d873a0b448b5d3b0ffffe6bbeb", "name": "picomatch", "dist-tags": {"latest": "4.0.2"}, "versions": {"1.0.0": {"name": "picomatch", "version": "1.0.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "ad8e4b63ed9ec16ecce02e55b7cca6eb1933e0f8", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-5RUhp8IAAODWzJMyt8caoCR0mzKjcIk8VHCmNzrId095B8KB7U+yxJ2oTbWnesWVk7BQXdRCm0JFFUCNDnokCA==", "signatures": [{"sig": "MEUCIQDKOfPtRKjlrue7cN8QKI4CzK7alep7FFsl6RxH/tU9KgIgRj7UMzGr7YYfVE84/cEym0cGTDzQzsR5ogLjyF1LoSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4DJtCRA9TVsSAnZWagAAFsEP/25zaI7WxDnR+yjBI+Xy\n7Ap79GoPUfjAFEHk2FDCwKrvg1Al84u/5jNg/372DWU8ZV3+ng89vzRImuRN\njGuBieApCBswcCl4ucI/SJCDNvhmlbSe7He0otLDiB+E5tK/mlIu4zUoiqpT\n+eZFOlXvNDZTc+NoX7HVV6nTfzT2tbWNwA5lXQZpv41C/F+xh/XieIdP+pdC\nLGcF20R4OTpwgh2QYjGtvQwaeEzR1WqYppr3yRZjF7PJ7pSOLuOVs+y61GDF\npHCmpaRCJV4irmGFc7wZ0sgvCSaaDnAtu7ICl8+kEyHGYUygQaiX8HrzVtc/\ns3QAO5fHhJVMc4Jf+QK2mtc40iiW6+GlRNco3neCLJnUbzZTHXny/ZBRjN2s\nwKb1DosiaRmARxjFcQiTe/Q/gIqeefF8x53q5n8dHfrNrK7abYiDyXbzds7A\nK9w9T5oKr/5hRqxakc3448CxP9z5ggKpjitF7K3Xqj4/AiprHm3tbEZG9NrQ\n4BK8z8eGO6jGojKfROrm9bIVY0AyznTGZFDC3Wpu09W5Zm7KV2GfK10cBWJn\nTny/H42L3Dn61J22Ua+fNUjZJ2lYsoXwrBTKvouUy42CU/3te2JjoNE5k0qX\npizVAmmFGpdRaMd9xSyxtnUf9Arbjyj0VagbAmFSE8QjUI9/FrrWwUlG9IT3\neezU\r\n=6krx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": []}, "reflinks": ["bash", "fill-range", "micromatch", "minimatch", "braces", "expand-brackets", "extglob", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "e31190a5be26c1d69651db566792878e18a6a1ed", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Blazing fast and accurate glob matcher written JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "10.10.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^3.5.3", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "file:../bash-match", "fill-range": "^6.0.0", "multimatch": "^2.1.0", "ansi-colors": "^3.0.5", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.0.0_1541419628561_0.6850721941651912", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "picomatch", "version": "1.0.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "299734ea87cc808a76fd173bee0ae32ea38e0a39", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.0.1.tgz", "fileCount": 4, "integrity": "sha512-LhU431VW6NbNnXz9K7MxRMLPov3MzO7sFw289mltXXdAQmkmi6TCZ+60t1V2qS7HF/bAPJ3WwOYtJDCJ0lp+QQ==", "signatures": [{"sig": "MEUCIAJVP09oNaGP7whNjsGXoJr+dkxJzUl16KR485ESq5KYAiEAtl+ycqw11fCTg+pTi/4AKORU7ixU8ItehGkShGAtOTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4DOQCRA9TVsSAnZWagAAmBAQAJJDQlWvO71Q8yM8cS7r\nxq+bsNgK+MFRlKp6FHcxo0AIXkojzr4OAyiBAXBP8r9e2/Hr1ekUoCEKw/ti\nyIOef7N2I9Iy1u2aZz7Ev5iy6cxYm9EyQnR7Gx8GxyM1NbmdJjdQQTgGK1/o\njB1zBVwYrAkKT9a1+kRG8ezi961m5KDwD1bg73ElqANVjL8VpfZp2VvqyD+W\nu8MwdQCHJjWzicFYVQCBc9HQWlu3u0w7yM9rvP70fq716aI9f1Dc7U41NKd8\nwcxZdTK844tsATVsNK0pq1T3UOhba0IWIeAP2DExIR3dg/ADDp80cJwpJK01\n8fJyguGc7hBtmGBNmYlSmnYbse++XYAZg0BWqBccNpZ+IboKs5BkquhwyH05\nbLc0cP2M614cjOZ8Utg6ZuFFKfmfJXi2QhwwKSRPx9J1q7oPGvJDKsIEu2nw\nuU8/JHt/UrFvemRzhg198hYWb8d6lApIcD4LFMYZpMYb7mZc66WQvMYnTx4e\n7hfy5d6tQ3fIfbbb8HR131QdO3GuNxQfdaI74dxw9ggqUaMTPBoeWzQAc5it\nlNFnHQUXKUUGqYX75q40jtR0u03V+bPStLtPwpy2Y5snhAzX2SUq9NmdhAlW\nk7DWrNZvfg1+Xj0173LOZTtrxnNXgzYX8O1NlnxhhU2MznEGoLSOGBrOr1wl\njBQx\r\n=tb/G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": []}, "reflinks": ["bash", "fill-range", "micromatch", "minimatch", "braces", "expand-brackets", "extglob", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "087aa19296fb4b5bba0baf8e63535573b6f7e8f5", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "10.10.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^3.5.3", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "multimatch": "^2.1.0", "ansi-colors": "^3.0.5", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.0.1_1541419920246_0.36685476241990167", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "picomatch", "version": "1.0.2", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.0.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "47a605ba6b76f20305331302b5d4ed731c69bdc9", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-tTQlevEXfC16KUht/5hM84OBmCVO4bc5MiJ8VSwbD13dfv8ICCjRaDo9Mi2ZgZYm5EaCojRIhk8xIl8Y3+JNXw==", "signatures": [{"sig": "MEYCIQCegVJKjSdqqCyAQJnzL/FIMojUIye7t0wAmWsp3915JAIhANyOykH4P8DJfKAtD9hCzT+b2XB3ULZUaZrit4rV0r/N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4El5CRA9TVsSAnZWagAA06AP/RHAvGr465et+Ck6lmft\nD2eJeZp1US9xm6jB3sJDLbDt1Ah6Z/Rp1++/6kio4KG9ZErcoHa79LQjXM4E\n9lcJtEg6Suuox62pZ0sZ1EIKd4gTrYA6lRsKlhk13VBEdMczysYJ96y8YrIE\nDsv+qzoUtaMuQYDixh1Ids8WbqsuoGPQ4+YeLYzIHSjvFLLtI1tt2ViLjP8v\nMa9Tm+HDm6VAwd8RnrlB8KliRLAvzDfi4cONdES0wMGelRUmNRCkueCEdBZX\ngEmiEzhqNFYEZYtuMca2zk32C/7Jt6XQwo59CAGodYLCPna8OGhV0kh++WJ2\nxGjNru4SzIzcHKMg2oho1OuAC6N1yDey0LdFdyTp1HyNE22DlmrOaTFVK5uA\ngXHYMkViDkqqPGR3gzzLDQtDVPzS3gczA2CJH6zOGVE8x0Vee4uVuFEJiGN8\n3hoAs5qi5gAop9yLNQJtW/OsuvrD4lD+Ge0TwKtHXvdvKPva75F2de7k76DV\nYAlvxnWVzmeCAIwsDtxULuk+OzKEmZ7eKtJjOW627wxzhFNbHa2Yv7lfgpmV\nQxkEJlHvVY7jkoak9isLe0mrzfvsFPJd7jfHXHGG8gp2WUJrRghshnaFZHoY\nw54AHZqGWNte63bNCrgMQxVk+RaMXC48Nhnde4Ty4DYgIIrzuolMe9E5hP/q\nn2ge\r\n=2z2C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": []}, "reflinks": ["bash", "fill-range", "micromatch", "minimatch", "braces", "expand-brackets", "extglob", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "6e17e8982e83965f0d9f468a83e945ad7da92c1b", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "10.10.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^3.5.3", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "multimatch": "^2.1.0", "ansi-colors": "^3.0.5", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.0.2_1541425529166_0.3812978102395246", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "picomatch", "version": "1.1.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "5e32a8576929be2cd3a0ce94b5b9e35efe2adb07", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.1.0.tgz", "fileCount": 9, "integrity": "sha512-ncNfzrfFY1cC3Vtdu8NPVdt5z4iNkjDT1e5fAQo0mmKXGD4nxJqSf07iR53bHJBumJJn3Ya9jgloj9mXZe0S1w==", "signatures": [{"sig": "MEQCIAxbSOEfrfSLpgsqIT1rvIbnI0ubtaoVNLaAyXU8S6FNAiBso1ZxY/cmjoUl/KQEDFdqKTFZvCjAc0xFu84MWQkDnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcE+XhCRA9TVsSAnZWagAAipoP/iYfhLJOqJxDXtn5vg/m\n/8uyxtla25xbtG9mZFFJAF38Qr5el/KsolJWFmAiXYyzBrpWLSSoljnhSl/p\nuV+JE3HClmVRj0FWh+xHS1KJIExB/S/vD+rnsltNmWaDii6miYjQmv0EH3tD\nM609MgytAYS0UypO39TpUJ/hJJBAbWD3lP6aR4IFDQjjeLwqAkc5NU6P+kX1\nUhYqu4fGlTrgEcWD3hdnY/e44jADR9FD0hZgIY9QpFBfioBzz39aYaL2G9YH\nWRlDKQ4DnEI86vfWuI97/kIH6mZtcDaHjrVnyQ6tEOcjTH54K1HHrMjbAZIh\nRqHauJVUvjp8wjVamGnqytySce65bvfpngxZ2VYTbGv6mRCRSJYm1rnZ+q3E\nnxCWvPhn0mDwZjipwRQ2h9ntNx/D4yYw9k32wf0yMluUxRkfwJuA6jdmyRDc\nmJfgxOg+BvQ6EWytJVX3MAE1r83qSr4I1GtLrVuOMsuNl64gUw75bD5VRvBc\nph5mjjYQo0oRDZwKJMDQ6l3MT5bL8VjljxPuZ8vvO3OpqdKOI5gELFCD4dlQ\nfcdKqW65rKuKwuBK0fbWAV6jXPdLgMpt0lYPy7qNfl3LRiGUTE9hF1emxH4y\njS3Cs92eO4+/sV1RuvvnG/lRReIbutn8s00IOjW5g5BVsN6X0Dm6nhFpqmXK\nSsE5\r\n=27rJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["bash", "fill-range", "micromatch", "minimatch", "braces", "expand-brackets", "extglob", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "77bd2d05830b10e8bac557beeb54d201fb007f2c", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^3.5.3", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "multimatch": "^2.1.0", "ansi-colors": "^3.2.1", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.1.0_1544807904081_0.47910854865565145", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "picomatch", "version": "1.1.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "ddd4fd27472e8d558bfb6a8f7ef36b1c1a338d8b", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-VWDOC2iibCJClkU5A1t3tUEGi4Rs73W7gyxO2enf5SCvkQoz+Q9viQSRu300ZUSRXMip/Esnt00sOffpDUFkrw==", "signatures": [{"sig": "MEQCICmSZuwQ0db82dp1XODl3/VbDrmqpt38BwwSt/FDrJ49AiAI7Sebt4KngKMm8bDqkD6eNDdaxswN/uCIn2D869l8Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFHB9CRA9TVsSAnZWagAAdRUQAKJ0P6LVuK2ojqYZunZK\nB7b1knkL6UVVa1T5obM6TVk6+mITdITV8uUWl3nLnt0EUonzwaK610Jqlxw7\nWxQ45xRyXN7SxUXq4gUq3Yj971fpGWACnFEqhzeJ0K387/DP1niLoWJzY1WZ\nevKXq3NQOoQhmhC4x7zWRljP7l7EyRv9eDOVc+Mm9++4/2y8jTvy7v8VhWb8\nWIUc7AXj9SUl3l/JuL3WF0qGLz28L9Ull8p1kRLmP5CSFU+r3X4iYg8CvkxR\nKpN4TFoRgBD2LzLVfG0t31tZ2JszrNXVmoswt2IcovHIGVxTxycG6RZrpO2v\nJBbxnwQqLDxCKUejQf9bhH+gfECpqIMw/aPTT8leoe2shRAc5A+uxzzj2Ua0\neLqIVVtJQMTYlrE7TUeIPMOxesXcLE+w3nirk/VpWaZoccU/4SpWF96cg+Gr\n0cQAGXWJs5Z+Z8VB5/Ln0ISVQsluRcxKfJxxXGqHBZe9S4+YbvEW1gDoEKD3\nqWp7qdQ5RD812q0bZJ9fq7K62g9p2DImppKfnmR7xI/o0/We1WO8b0Roz2yQ\nqxsx3YqFEFOsn9ZK6C6A0t/cwxIA1Z/GQ/Z4oxNkahYX9UhrLDxIKHq0Rl6f\n0fBTV9i0mqon9J5ii9z0eb5C3UNzATsJwCx8BaQKaK35DcjWNsKo6WBzmQre\ncJCQ\r\n=NtYK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["bash", "fill-range", "micromatch", "minimatch", "braces", "expand-brackets", "extglob", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "77bd2d05830b10e8bac557beeb54d201fb007f2c", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^3.5.3", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "multimatch": "^2.1.0", "ansi-colors": "^3.2.1", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.1.1_1544843388115_0.8869841129031935", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "picomatch", "version": "1.1.2", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.1.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "122f5efb555576410acc4bc328fb1a1eb69ae537", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-FcXFq7ttIsWpwlMLaDvVskGLNSfKB0ibgEV4KpuJACD1S45dM4ViQuGrPMxNX3mXUtg3OZ3xC5Ny+lJS1PqIZg==", "signatures": [{"sig": "MEUCIQCD1r4j3TrrYkMiGZeHpbJ4NiBZrd8e1OTjfJknFgHwXgIgPZlka5aRFaiRTTm2n97q9Xra57moB6fD2R1jMD++fcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFHE9CRA9TVsSAnZWagAARZAP/j3IJoY4yqZQ2Vqow/1M\njkdljHGbfJCuAr7TY8215MMW/8aS+ZechfxqONN8PQzGv025SyML9bODQ224\nQ04aVPgCLp6BfYt1nWv5bJ+RQ+cnwlJa7sdbvZCNJFlbI3t4A+9yFVL+aZBd\no9jXoNkdZG/fXsAj1zFMCZ/whzekIPhurOVsj7Fz97+4irwfTBwcECuPUcXt\nUCIFCK7ZFl4IZEQU+4DK41U40jGFRqpq+b7LUd3t+NwK4BfQtYFPMYK/G3Do\nmNjgyQCP7xvjQ8XY47H9H0rLxmrH2WZ8/xTPlEz67BFlM1MROgWbnH7RYJvV\nFcUOM9UktQDXpRw+A2XFDPFZ+MvJHTCExtB+l3WnGFgQzk6HhnQSrK7GVPz/\nCYcVIV+EpRBJN355UGGbs+5ymjGGtun7/lXbWbtXDADmSfK5iWEXL74N4PY7\n+yb4m31Lf8UbkWyZAFVcHvoQiV6AJatQEIYhRSxgzDAi6FcLwt1ug5F/C4z4\n1GuxnJie2k1IsnF2WympWoNYP9rsDGCH6e6mXi56nwBUyOC7HSzI6lnzXFz/\npL778o3iaLsFQgo+szoxSEgDOwq+KWCJamFTwTUaX29VdDWElbF5aj207zl8\n7IrFh0vM0d890SKVz5Z3klJlEYaaQbCq1FzQXpfKKjZcrMy+qfl8Zsxgztkg\nCivN\r\n=Ug6y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["bash", "fill-range", "micromatch", "minimatch", "braces", "expand-brackets", "extglob", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "c50cb8b2d61976e8ae4244c6aedeef63c0fc474c", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^3.5.3", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "multimatch": "^2.1.0", "ansi-colors": "^3.2.1", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.1.2_1544843580410_0.9152113891811657", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "picomatch", "version": "1.2.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@1.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "ac60ec8b6ff5953a505de2fd5b2b0a1e1d7287e0", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-1.2.0.tgz", "fileCount": 10, "integrity": "sha512-cMSUWLsIT8VCmIPlM87m5OASGmDnYJR2fEUIpOnuArPQY5p7kWfJ47wZtIOYHT8GJHo9B/QRr4m5mbsIXxZkMg==", "signatures": [{"sig": "MEYCIQDfvhuFMJGyAvn0jq+zh4Fa8fpvZi5T2ppfwiSKxY3r6wIhAMMjaxVlqmDoIy3lALGgakFq05avFot9IXfEwAFh+XKd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcntJnCRA9TVsSAnZWagAAAEoQAJnOUaVLtYsbpgX1U97F\nqoK9DgANIdBjwoAcLJa1ctAsP1Fnu0mEtsk18G9RP+tdHrUsWRLNy0ktKhP/\nKrudvB8Y0WWAGaqSdGGcJ8h2zK3PpWm+ivFpLWXErCqRBiKiDLua+L27Tgsl\nDjtHcdY7ClNI6ug8JYwlKc2tNNmVCAOZeAHPy1x4cu8DSUtKrCRe8GOg6d/u\nscJ+1kXPM2QT840KSDvDe081UFZCpFxvijiQNwn40PwQpGiVc26mcUPXXdMg\nVYLF6Q8R0ja4b/syYunwsNsGTQ+sXLxmz6EOWYVvrsKhgrQzy3g4xyN+DLnx\nXkOCkWMjBMb1rNMH/GWi7FZeFmtKbmZMPEHayRyxW2DrDfBZLA0W7SHRxYn4\nD3Bz8Rl6OdISXFa61+WUfh3eBt14WuiuEEXMG3hIE1lGG79kzSHll+p+IdIG\njR2ZZexQa8SrLJ/KV42HoADE9TWa/FsUB8PeHCcD2Vf/lDUaxAzQXgk3U4qr\nZN1PZpPYr38dPVFGxwAjJKnyWWJMefpfpF0taIaRbAsN7jEq72CJv7TiXFhe\nvSK71NNHjwSUZW6n2mR17RdmQTOD1ZKJKY1rHI3A8KT7HCQhPm9I3TYrEZeI\nYAYlj/TN/UOTG4hxclvLh4uU557ROUX7JvVCG9SQUWdP+HH9GO45cMdBfQ1b\navEI\r\n=SHbE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "15a5d0e85cf411d1331078e896e764fc102533d2", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^6.0.0", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_1.2.0_1553912422567_0.7493384972833748", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "picomatch", "version": "2.0.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "2500b6aa582f98f9f30c6d671bd48f288045f977", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.0.tgz", "fileCount": 10, "integrity": "sha512-LCp3QJ9q6YPH7u0dZTgOuxj4DW20Li5R1xQAnzsOs9R0OmadvgxiPsTB224Jh/5pnyYlb48n9nETuQCOqB37Jw==", "signatures": [{"sig": "MEUCIHkutmbEcHe5RYUTOI8l21lIRT60Hiy0z5VrTEy0Qm0RAiEA3vpxHxJOk0HUJbybVH+8BAxQYyz+zzPFqBTp8N93gds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrc2BCRA9TVsSAnZWagAAmg8P/Awi3xnQeIX66Trd4F0U\npNBHinKmzG878PvwhVuxEsJt3T+BULVHWUFoX91wddnnFM5uTti7M2dZKQTi\n7BPzClNztiyCQJTeer8gCuia03xTofjn8/qM64Hab2m0pvP36CdtG9TaqO2q\nfDxU3BvNSzS2rAtS4b32Iy1Jg2wLpxc/Fk/YUG6GOBH1AFvM2m1Yvet+3MLf\ntFkE1EZE4IfrK2zXuca9bQwpwCZMLikYCtfo22S8y8ibbdxv8nHegzxH+KCp\nK+lEDC4+L8MH1fH6gpiXSfUb+gr1dSyuCd981vgpc3/03E8O0rOmQyjArxbc\niygEFBd/l1AtE0E+niKKL7FC7mRggbMFmloKaP/5hVbFe5uHpnWFqwsRdPSQ\ntJ7Fu4U/tr310Dh9dh2/g71asaryMs3gFUeM4YsYPNRdASjDPl138jBsue20\nQXrDdNy7pGONS2/gMUv/fcM6IgAqdveNaOEYUi5+40lA81HAbYxSnEZOMit6\n/iEXAv2JaDI3ssFW9/at0YBdkbP9/RDrvnrT+kFsbG2WNbMsSUxrS3ejNYe/\nYtkunV9nkSPY3qsvL/kTtf01abr28E7Fq+UiKzE9fk5kTZshl+1+amgmd+6H\nxVZqdSD1pJDyDZt6v9jD7l+bX/UafXAR9F/KvafwH35Wznn/7mKTFb1juVeb\nuDYT\r\n=W7d6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "8212a2e641ab1365cadeab2110334e268b2a4762", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.0_1554894208830_0.7281662890740057", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "picomatch", "version": "2.0.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "5eb60e9bc20eba769e6bce404d0d4f7c5f5d099a", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.1.tgz", "fileCount": 10, "integrity": "sha512-zruX90vxmnhSGd3mzHzNL+cbnZ/CTLGGI3MrCrcoP6mSHLxZP8n7Obg7MF3ZrN9Pb1u0I+fEOiw3CDp87D8Fzg==", "signatures": [{"sig": "MEYCIQCaNNHqMjr1S9gv9HlALVTtecvEaVhDtbLMYJaiXsMNHQIhAPln7eRFcAaSVyTb2HzsrejeuX5w74sS+FQb5ZBiRmjq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrdhvCRA9TVsSAnZWagAAhN0P/2UP3PoS2bvtoaGzQRm/\nb9bm3Z+x4THEOsgkCUvY3dFcLRVMKhrgQ59xfKp+1x8ijOR/+RdjJv0LnCeS\nBS4ojiF8oaQxBhn2Nj4scZD7yySlscRGoF85YnWGaYetT1IkoSvwu4PUB1ir\nHOrGJiADPfZaZ28kdMa6Gk+STBQbxTZpLxrde8XDaCnwoewl/nyHhyLMnkNL\nz+8+XVGbPqWDf7DRrd/rZhN9OiMsd+w2o16PpA1VO2VV5bSBJvVqfElaEj4E\nvhUJ7US1mpb6QcD4V8BFb3GlJlRE5xTsaBBN4Q0puZnNEy9Sax2DftcBATtn\nQRiRz0AOFBCl5Fy7HeaJzpivOcV4CvheuzHyG72B2iDapuoNlAA1JskhjDV7\nEDllGH2TRrxDfnhMoCM2bGCLccT6UZZrO5STxy9f6nNsZa7Xib+fDCE0Kh4E\n1zBkPUCBDFO7U7BisIRpma0org04950WX0+w2MjJTdpzk3H0x8+UPr71+/JY\nQ4p51AYVqhrxQTokGioHFVvUQ0/98N/5T5t/ZnjKlAUvh24ae/fcpO7nIIVD\n9HMm8nAdxE0iD/ht/LX2VHo7SwWZlgUB0gpHpzNKn/SaO074hlkAS0pV9c7m\ny1KLTUtzEs5zfTsNEY58bsuc0YwUASPNVz5J+Gf9Cig98T5moSx90tmuWWFD\nkJnf\r\n=FWow\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "fbdeb893beb223fdc4084797c33ff88049321543", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.1_1554897006577_0.6770058205351768", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "picomatch", "version": "2.0.2", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "7369a814568197e8bfaab6a53d6a2f989cb09e3f", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.2.tgz", "fileCount": 10, "integrity": "sha512-PsJljvIvCvQjzgw2ejIP/t1z9obDXzXxcDLc4IwzKS99UzVEzRqXJS3NEK9V8cuFk4d7DzYb6N8AVKBmq+j9fQ==", "signatures": [{"sig": "MEUCIQDFvsAIS+E2fXyRw/RnqOz2hoNDXzlML+6vLG13YO5UxgIgWtASon+RQEGoO3eFTH2LFNfGn5OjnAhxc+u7hvN+eKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrd6lCRA9TVsSAnZWagAAWFkP/0WbffqeG6ciBS4Bc3VP\nvONcRjdWcnV8YTM3Z9xwBmRHmz7qSaomrgRB8arpEgDi/VqqHegiyyZ2Cxww\nPE+NLx2GeIdBSspa0bHfKfLGuckIf2ql+YaP7CdUIYC3d+R+CyKRy9pd3arO\nnmnwF4xAlafaDzqDHnkxRnesaj+7h+Apw8U4Dve+YywoyOL65aLGcEXnP5WU\nmvliVz5BUGFsu1cnwgB8E06dtPSdjm3xF9T5n4stm8D/GRGEMQoJT/sFSype\ntA0lgocHZYtPysrA5DLRNpuFLzbrrl/6p/aCiUEIQuAQks69dwfH+B1gAzqh\nXO1SvyyFWMveZGYILpr32RJeOq0qdOzo8AG0nzb0UDJsx0WfmN5e9y73eLqT\nT8BHiWSSf8H2XMpeUl39XCYpGwmi+CJpnwX1n7gXVC4tddri3320PDDn9l07\ntpk8GUiFa60lXWLbkHN8WacWGR98uuYQkMdE5Nqt7LtBjDF4lLCdRDTY4P6Q\nBiRp9FYAK4yT5XsmQjXtmJiXhuz2VCpAIRRZ13t7TTWaC41INnjlVgGcJVu4\nGXKC/YuFZftBxQPfZ7QEkhmXvF+PuWJc5uQyaFvMeaiLfmdcV2FxPFXR3Jpm\n2sOF4Dp6hCUR63AT9ITGo4TgDXzzHt+oozhUxzHFxqd+/J7hB4Y7DR7Bg1Qb\n2vAr\r\n=c5L6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "64a4660ca62b5718dd4b440242cf914fbac000e4", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.2_1554898596496_0.06921179849650527", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "picomatch", "version": "2.0.3", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/folder/picomatch", "bugs": {"url": "https://github.com/folder/picomatch/issues"}, "dist": {"shasum": "8523b368260e5f6fb5da5e45fc7a582f9a0a15e2", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.3.tgz", "fileCount": 10, "integrity": "sha512-NSJqCeCfH7loxpU6i04aBiJZv4oZFlLKTLTyoWCpJnC28Qm0T1XUfNfzwLi+vtFNGIjc9pyvx/NJUJhFQ3Ptdg==", "signatures": [{"sig": "MEUCIQCADwfyqie7vUBqqyrw7Hpp86/VFgBK0vKLurteJzJH3gIgEEGs1vjfjV/6oqjhjuSfjDbwNMNCcWaHJ0JFAWs3/SE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcreT8CRA9TVsSAnZWagAAMHMQAJ5BPcB/8/zrQ3oa763d\nsZiaWwWeep2kOCo9fFOBbSlo5EDgoTFpa9lw6QZNWnvcRebwSGepH2XBdUjI\nsWM/8s3HDUUce8HMgMGbLRd5LFTmXLqytVSUih/C+I/rifjY9mP9RVqkMFTm\nr7V4xaPUg3LXQ+hA5vpqdBHOGe10lZnwLG8U/sfu5gNo/eJlg7Pdtl6YMvFb\nHN5rqpqXKExlXnSg15yjJQL7XzycrWQtF3i05eS21YdzgJNe4Tnwj5eeg2tt\nRhezzvppeCgLvgIF7rMuXgqUoFnwfLG8vL9anwkxaPN5yqiYbbRbzfX70APX\nXIbeQ2HKUM6c1yfIT+NmPw95JM5b8Arv1GnGaCXMfWHdkohmmxo8N5SDcrdQ\nXnLFEaCfUEjgNP4QIxiVhYEmAHKRhDEnF5eHnZUbFqfRBd6WaWntm9mtdJra\nodFU/4qDSumXGq1tgUM+sM7dwNJQNpLPAZYxZGprqg6LPTriHegJ5Emymefq\nckvrgY6Jbd+6kzrCb6UxP7VFhwX3H4RY5fevDpsKyQJWoLsnGg2cv+RTPbN4\n8BYWddcv2IrXwP93PJmRT5MH3VyzS3g/wc1j/Z/wrNg+9q0ZyX2PTkEUs8Qa\nml5OYs3uxxnAHJcMTCG1p/xpwtK/65HABv2LHADzBnd1HgIcuRbSSfH6riFx\nr2UF\r\n=a9BB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "818cd93b8172663acd19d17c18b5059d5130a031", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/folder/folder.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.3_1554900219249_0.170282712670206", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "picomatch", "version": "2.0.4", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "dist": {"shasum": "b972630d6ba532d905a4d6524c095d0d2140b4ed", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.4.tgz", "fileCount": 10, "integrity": "sha512-lN1llt2d+xBz96Vp+yj0qMUVMyDsqvNSecdRDIEuh72kQi1N6ttkxPJ7zDVwKR4ehD2R3WhMKqdp/5LeRfc+PA==", "signatures": [{"sig": "MEUCIQCpCaqOxiIYC78JMhyIg5ShRL4fcrgq1xddgjP4H324ZgIgO9s1Aj7KRg7NqsKvPmnjqb52lsJdao951h3k6XIC4QE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcstSMCRA9TVsSAnZWagAASZwP/2PvlgpwLbE9Ppn1I5e1\njgUnfx00NrPlVbr2v4agPraeCXZ47/jLdgDI6v+OG94cpFlIHFUCZmykk0WS\nYpFuGOU/wcGSTSYSiwA+nDKlL6uu+7TsZOg4w1u3ZV/VP1vrvQKBfsw/Me3p\n/SmgAS5HUlquSRBq/nyYk8JL7wUkydHkVImj+0j150Q09npXihPmnWYLtb41\n0RDtx+rax60zqYv1Ie0zkNlAHtLShODBApbVlQdQe0XmQ3TGeP76EZJzN1N3\n1uJhuKCM7p2s0f2g/OfjALv4tJduD9uQ+TCD+AScOU5b6ngzb2UbKNQk+231\n+dyqNYMwX1PhxZkAOWaRF2KwDX+vy09AMGiZ8Bz/ZsHuaqXxGxIIRyzwIbEx\nPkJu3VwGircr2WqMsucbOoADD7zsN3HteB1PsGf54C5EM+ikf2uQ7KrAmC60\nWzVBbeREn3WGfm2QbwLTO4yxVKx54Sx3jJ6YHJJH+YodrTcd21Sc3RBy8P7W\n3aK/QfMiyQejQCeMjZtWzCkzlpIrJfXv67N1fAkOIeo/pxtxa5zD23G+14T5\nsxXR/2Harxxtf/Iw2GJZiweCnHUGmJoJrRRK0oxZZpBHUZNoFbq4xfyTTGrG\nv25A/lwMqghqlBtd6o8LMQ082y2bSkngqCG5SB0eGseS2RdPVplIZtBxYpm/\n1D8v\r\n=v1mS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "c7020d80433bd3a87195cff9c5fe4f261cea0f9f", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.4_1555223690896_0.8356264220137732", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "picomatch", "version": "2.0.5", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "dist": {"shasum": "067456ff321d6447ca3dc32273d7bbf19ab2face", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.5.tgz", "fileCount": 10, "integrity": "sha512-Zisqgaq/4P05ZclrU/g5XrzFqVo7YiJx+EP4haeVI9S7kvtZmZgmQMZfcvjEus9JcMhqZfQZObimT5ZydvKJGA==", "signatures": [{"sig": "MEUCIQDnMsdKiEy1mFZtLWNIx0uhKDVJAz5lszKSjNylRsv62wIgLjASV8TSVC8dzDg3Au1jbotubpklxhtqf50x3z1FZI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuz38CRA9TVsSAnZWagAAzsEP/RUMj0tauYG4PPKXE5eL\nppq6s7p5XNzBiKt8PrXRN6gnON+nqcASN3QxFu6qpivwXHQcKcnU09dnUN5s\niiArCL+J8QlsfUg3roJ8TWNPCfYm0bENBWLudtqkQV+QSXVk/C7dcPsEYjJ2\nnLzN/Q4sBSmvClohuHuBj80C6vsJNy5Qvicot02GLSOA4i/h4pG5jZ/E4BPl\n8tH39ZpbBKBLyXuIOGu4xBteq2KGNe11EPHnx2q4nehKwg8pMtPeh+TUtAF5\nhwPv3oC0iiVB4xf+JL/4R54+zzSXLdI5Mucihd+3PIid+30hV4dzsezeKux1\nwsLZQKBXr0k0vahO9sJRuuVPzqnNmFWDViaCJChrQvvnn7/oM9KGAQH6kGCH\nIpbGtOIYtW7+TIBGKBWw/Hi0sTeLk26qbegeQKXggBkG5M0GJ1UOAlN+rLQk\nH5aPO7Uh3rZGza1iatdIjERGUFQDjv1YtmZIrvy44pzAR7ezEIBypzgQBUYJ\nQ0m0NL1lmVc0MDRLmXl2YaZa3gXPVXIzd6b8CLeEyUfIb/reEvcYj7XMIbsK\nVng5mgG9w22iqzrcqLcgp+dxG5rKXzJu9kNtttaQRTCWeHjPG6POfNpFMdo8\n5hRAr075pv+AZJMoagTBxJGUCCHufdwAWMud0FVlu1MV3qwIVEPB8PISl8Gf\neE3O\r\n=xEOA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "f364c5bc3412bf029a60540cfdd5c85dcbd6a2a9", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "11.14.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.5_1555774971490_0.37326703747523826", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "picomatch", "version": "2.0.6", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.6", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "dist": {"shasum": "f39cfedd26213982733ae6b819d3da0e736598d5", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.6.tgz", "fileCount": 10, "integrity": "sha512-Btng9qVvFsW6FkXYQQK5nEI5i8xdXFDmlKxC7Q8S2Bu5HGWnbQf7ts2kOoxJIrZn5hmw61RZIayAg2zBuJDtyQ==", "signatures": [{"sig": "MEYCIQDzNIpKnfJ0GJyLZNMfaAGhZSoKgIyDShIkV+roUN/uRAIhAKoRi7Us49bqKLTXIufxWWX+2IUbFXn5UxCzVjDOeZf0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczUqYCRA9TVsSAnZWagAAktQP/RrqjicQL2Ana/FSPtm9\nBqEu3CafZOXD1G7QmzHYZ6DtTX3vzVm5Q7fSi4h7Yfuqhnf3TMNnoOGBqZaQ\niyViRNTdJZk1K073Sfd1HvnED3xvuBzm1PtBpUN+xeBzwcaxy+C2ThUeYI/Y\nRvOxgV1Vte/+wRjTQwml32BiVX3gWbstxZMh75jpYnzQxZcqMpgyG4iwSzxN\ncTrygRaaibF86GjQ7FPdYMeHTcfo0WmGp7bfg59JJh5/glhrLEYL3c7fSYUd\nY5MOrg7aSsJ0SeBvYDzWr6AIdcbbFaFGZd3gVMYuMT2gDxa7T71TW+Mw7E+Z\nqnP8HLPO0nVS6mDJMady76qw/qUBXQ4MqNjLb1R47UcYvIub49C7NtLygbCU\na/Tzf6fV3xn+dXPw8fSxN/0tP2OSzzZiSKy1/hINCT9uCYeZjNtaScnAM4Tw\nIb8d/7yTmViiZYtwWYWUc9S4HhVgfQkqoGdu93dZoUz81LIpV5rwaD45kkTi\nXpyrR6F2U1An89oy93ABUj0lynQnRVEhs3HwSEjq+uh+IoxZPKLsE4WkEs/0\n4cpVIDSU6ohzzIt4s+4SXlHOLwLGHmyWVjT6DkISQ0PXJ5R+Gks5g2+Qz3vD\nY2vI4Yq+OTVVvb5+igNuaz2VuWiv7xzGTFWQ7d9nZHmIjWhj2g0khNeeDfAh\nA3mZ\r\n=mpAB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "49148dc56587afd78ae3467af859e1f8ac49160c", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "12.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.6_1556957847393_0.5211348957818349", "host": "s3://npm-registry-packages"}}, "2.0.7": {"name": "picomatch", "version": "2.0.7", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.0.7", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "dist": {"shasum": "514169d8c7cd0bdbeecc8a2609e34a7163de69f6", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.0.7.tgz", "fileCount": 10, "integrity": "sha512-oLHIdio3tZ0qH76NybpeneBhYVj0QFTfXEFTc/B3zKQspYfYYkWYgFsmzo+4kvId/bQRcNkVeguI3y+CD22BtA==", "signatures": [{"sig": "MEYCIQC5GBi6akAEL5DjZV9cFa5yBat6CWetqR/KzOVscOmoLQIhAL6Xmb8xwdRONYGI6vLqFzMj8enCzMrd3iDZZeGwBX1R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2v3DCRA9TVsSAnZWagAArBEP/08ZY+N0XGMP6tySuJRm\n6fmAEC+W5Zugeb/z9/poRHFL2PkUj3k8QxXpanoCcySDCMn8exAWdRiiw7a4\n1cwdBS7SGowauCJRoFL9AcmRZv6e0+a+7lStHRd6kjTec5ZHN9LAVNXwhUST\n6Ch2c37HkbNcuEaO3y7a4WpLoWZkXeXiAMX0m3kCwiywUskfAnFQiEwbeGAG\nPCUAiEYrh1/MRUZ8xv4+AZJ6/OlBmeQYSgBPelyONk9NGVsVQ5I2pjGnaQsG\n1UtGhofoT/3zIfIb2sb1bU3fZbMxwEbQ6KG+Rg4lejFi2+Zq6MZdiYPlNCrq\nEcWqPWmtutGFM0ipsyswkwvUZRh+1C8hq8IUFP+n3BebsFLUjIygnXLCSFWW\ny3WfsHgnxoKgJqezB18M07yd7C1me8uo4PHMRXtVcj2Za3q+4uvb1QFYuJAr\nN/3Sfmc9JF53ToRa7/kdn96ONgrtdyDBG7e5begUYZ0DOgXIRJV+CRJhW0oG\njEPzRgUjxZqiuBSvfNdYlj8w1rVHbwrILAtol7M4I3c995Dvcpf7La3Vmsd2\nxGfaTR8VVKsqr3nQ2FXhOBWmNSCvl+gqbxesy9yQ3oU+xgZuZuGnj+RR1R66\nxafxp0UXKd5/G24UEmgkYZCznjFmY9IqiGHPyg/RsGmOvZr7m+k5TYoqjO9e\nLoz5\r\n=SN3+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8"}, "gitHead": "9eb9a71780035c5c3e2c1e67eafa3d967f2289b2", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "10.15.2", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.0.7_1557855682387_0.9874545198503017", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "picomatch", "version": "2.1.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.1.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "dist": {"shasum": "0fd042f568d08b1ad9ff2d3ec0f0bfb3cb80e177", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.1.0.tgz", "fileCount": 10, "integrity": "sha512-uhnEDzAbrcJ8R3g2fANnSuXZMBtkpSjxTTgn2LeSiQlfmq72enQJWdQllXW24MBLYnA1SBD2vfvx2o0Zw3Ielw==", "signatures": [{"sig": "MEQCIHEZEoYEq3MwRKgJUqldByhZyf+yJmrgrNEM7WhNGuBDAiBmHdnDQlXgW5Zb0qg0Q9UvTBwIA/eVYux3UFI7lkC2nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdupVRCRA9TVsSAnZWagAAMmoP/j9JIN9tVkRQlu4MnfgL\nBya7r0wnydqGmIB4oj4iDihN4P+TLDTkjp7rbY1oXhfcDLcCoN/AAC4asIC7\nbp0Q6rl/o45Ys9bol4mFee4gqPcp9TTM7/jz7XUN7GcxE1rryIJz9b5cEwmM\nqVT0oVOreuAqpBYDKMC/3nDGnwmwnk5bAhGMB0aC/+kKIlN/MRQuUZhUbd21\ngwDFQEB0kaNlVK9WXAXNEke/iN22MMs2pQKxInkNFNrC/wHNaPAx8zuGernv\nUhdUTBQxTMJiZBCW9lTHMOJeoPM+e/WgingVFXY8KWNja9IehPol3Tgq9tbf\nG2Mk8joOqdUQMZuu56DD6VltLPKF8bqAp/tSmYEiDd4/O2mOKZBKKwxlBbxi\nBp2Av88YjDoCLk5pCs6H2vV47EG+gItLxP5H01xmbNrSLkG2ZamETW1uq6M3\nxaz0lC8TcwJwnUO5TdL0HT3QjYKy1UPkNyBG5GnGulRP6gNSo7l4WqcDa2Ht\nyLNMAkqO972xZESRdjDfpLtcJV1/3FF1dZVZhq6BKbftYeIo2f5dBwy4VmPM\nMHF5pm00vvzWAtc0wyXlQxLnb1+RszLZGlh8ZzA+RIgqYxzy54mwA8GoHXgQ\nMg3bArogYLkkXlWWODkJTalVdCz6pFyqQW8dcbN2Ju1ZBUAEYwI42daNqgFc\nRhn1\r\n=AJTg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "8ba5215c7e4a69bc3ce371bdb559d21553f3518f", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "13.0.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.1.0_1572509008306_0.7569108006631269", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "picomatch", "version": "2.1.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.1.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "dist": {"shasum": "ecdfbea7704adb5fe6fb47f9866c4c0e15e905c5", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.1.1.tgz", "fileCount": 10, "integrity": "sha512-OYMyqkKzK7blWO/+XZYP6w8hH0LDvkBvdvKukti+7kqYFCiEAk+gI3DWnryapc0Dau05ugGTy0foQ6mqn4AHYA==", "signatures": [{"sig": "MEQCIEKgNYkXLfIOw+9cOY6xsfPAnj3qE6dSHExnb8GTAXrzAiBAcA8AkuRqOSCFa1BMJZA7ASBA9PYucff0jOuDLkoHtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw0feCRA9TVsSAnZWagAAsPIP/0D2fvmFV4rS447u+HNt\nyrsrQrD2tI34JeY5tzo1Rs3iChtQLmER1e3X0plfkBT7IOeohoYg9SHtxqlX\nl6FrDadfhSrcUKL/geRh5QLiDX1mfGo0LKeG8hb0jjTxFuCLSLA4yGWGGMJZ\nAfJwulDk4xzDX+j1RxE8REtTISa/Np/VDYW0pClj8JhFemI/D0cB5GlAUU73\nki5hAlXoCC7V/aDHEOKQiHdfoH05C0uDg5D8RSJTHsnR4JkOS+Rui9IaHa0w\nQTgdOdTst76iDIA851S/VZKf9Gg6jZ5v8VheZpsP7ePrgIy4w79wgHrgtgoI\nK1nppY7kF892c3v1QRkEKyU+mPqtPORWUip8C4R1guwgdoXVlBVQ7sABzwlY\ncsIgvT1y8z5UusxUvzO790yZ/BlLwbft2pBNi3dTaMruTD0ztTXtKvwkXiC3\nqCC8JRaUT0dl1dsB3rrpFV+P/Eff6V9Jp+P8gxSTH0F18Zw6ZLUz1YsdjZKn\n9Ikmb8Y97CFAtTwy7W9Rsx0ULCeIssdInVXlACyA0FDTa6v5EgJi+AiA3g4s\nlrPC/zb1iUfB0Ui4hchrQHCxpQKSzjAkCfjoXIxuETZ2xwBNZv0fYuDc5jQ5\nD3uPjWnEKPscDnhBIC3PD6f/DbcZQPsmQ59kFl1i7VXxcHw6gy33mP3eDEUV\nNVOl\r\n=cnOX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "braces"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "7944dd0f7d174b067c6f3fddbaefb0a8ae9900c9", "scripts": {"test": "mocha", "cover": "nyc --reporter=text --reporter=html mocha"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "10.16.3", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.0.2", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.1.1_1573079005960_0.8760210690317336", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "picomatch", "version": "2.2.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.2.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "e16e8587cb70cb515a698ee4a0f2df1edb0c9aa3", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.2.0.tgz", "fileCount": 11, "integrity": "sha512-Gskshv+376q+hmJbbOiMK+Jtv3bNkwnoOtVSPkcSrvsu9WuKJvBXOcSlBkWgWE0+A011wfafeWpiDrdbowddfg==", "signatures": [{"sig": "MEUCICaSLLdw9rvKW4hTfSh4aV3g46/8+8ZlfK2x488iDCP0AiEAycW+UwmBJYLgkAXSriycKUDPuHeWe48EBslsvy0nn6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEPYtCRA9TVsSAnZWagAA5I8P/36DVnHPX3v7sQtvPwrX\nXjdHfPRstr4F51OZ27dIcoLN21CV5qRmI8EhsSLl3sTv9AOhx6wm9f2cChWF\nNZF8/fQXa4C/ZtckhJWIL3GgyfXZbDIJBHImq9EeenGzCLk93FopZZPpa+z1\nt0fBON+Jr+J7/y5paTSPNVKGdfvdDfGPgSO0j0KYPYhQn1IMnBkuzMt2li5B\nQ9T7BfUJ8CL6fu/37PWQ/RnJeOFWW/cHk8NWe9/bowk0Xq6x1Jsf6MQeM55d\nEvBmTHSc1gra+4dWzjdfG+x4X7lJ5xnJiUtXO5Eld0i5qGknjQKvUAKHCyaF\n9moYR1aCXe3Yetx7TfNPQE1574lalljQL4T5rgximAJOxA8UjlE7xBmU3d6m\nBo2mjjYeJb2Y8VRf3LxweD2RMWlzxsiEssyF1+dydQY3R1pKH5QQYcugbwM6\n2ci3ISisTYqJNMEgxc0d7pWgM8XCjI1vx2uUZhkZghgHuXPz+X6V06aNdSl3\n5tNfX9+M3EVFkakjFgXzjAcGcF2/IteAgrl8miLdbPyq7iaAmpRRQRunkbva\nFbPxNyC0BPp0p3vMRTykmJfkfXZCaFrnN1B5G/BZAi3Tt43rTUtjDEN9dCzi\njTlYI8nqsiX+WHbe9APKIgmB8EZ1xYbfXIDaeHBvgnREPtDf6qCpzc+eRq6/\n64PB\r\n=O5nY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "002e806951ddadc7b27c9d8e8f92d6709d34d1d7", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run lint && npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "13.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.2.0_1578169900719_0.35959725130594666", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "picomatch", "version": "2.2.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.2.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "21bac888b6ed8601f831ce7816e335bc779f0a4a", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.2.1.tgz", "fileCount": 11, "integrity": "sha512-ISBaA8xQNmwELC7eOjqFKMESB2VIqt4PPDD0nsS95b/9dZXvVKOlz9keMSnoGGKcOHXfTvDD6WMaRoSc9UuhRA==", "signatures": [{"sig": "MEUCIQC61vmK607y9vC9hnsLOr+fZrphwKvgmY9ADfheVND9LgIgP0GWdvPb/ToFC9/xPKIzFjUz1ZFmn+G8zA25SHjQohc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEQF1CRA9TVsSAnZWagAAFPUP/iMuLEJZn5i/5ZikfVC7\nKuODpS6DPrtfaWEc+j6WjLGhLyjt4XDvSGO/5CD1T1+lTM/Ch2XsnUji7MPq\nKcQecli695M0daO8LI9NbnN3omHsoWVOKGWqEAzML+rcvvfFCb0coCaMyoyN\n6E3Y0oxIRt4Wwzbo6VMq2NBy49K+f5hJidxpDENe+P9Vcu587tyG/21MPTcE\npLOiNCkGY7p+1TN7NxzhmDJZ5u8L21U2v11MnOzLp+umOfo1x0kEbar8r5vB\nb7Ux3G4tMyCHo15ts6XnsgzZcQaznjeClOX6Swv2jPW8nrcTN27/v+LeIVL2\ngW3pTqTadCd6xkorMfOYMI3fjrVtMERI+xkWFtqD7yOTyJ4jr1ofSSssvXBv\ncMTI2sq8008pbSCrM4jPqH/9zV4Vs3B9PWL+2NckiHxLD86z5zGt7GcWmF/Y\nBkXz647xqu/dEkrM+eLnPxqwq+vxJyEYcClbXJlU3KsJYa5DtXucqtMQyR0W\nD7f4QMiBPc/DHxiXcQn26zFushCNV6wRGNzHlUl2TzZqt71jaJ+ywvGOTEcm\nZTFbGPoB3lhsjBQItFyl6vbx9n0JhSOb6nD5nCjSLHCMFerWfdFMJwNlnClW\nPI1iOe9gRzLQdHdaMCIYS5/KpsttPTN7VgGmcSRwOQoLkihXTj4PMITRjwbG\nETnD\r\n=EKo9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "****************************************", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run lint && npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "13.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.2.1_1578172787892_0.37797747768464274", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "picomatch", "version": "2.2.2", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.2.2", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "21f333e9b6b8eaff02468f5146ea406d345f4dad", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.2.2.tgz", "fileCount": 10, "integrity": "sha512-q0M/9eZHzmr0AulXyPwNfZjtwZ/RBZlbN3K3CErVrk50T2ASYI7Bye0EvekFY3IP1Nt2DHu0re+V2ZHIpMkuWg==", "signatures": [{"sig": "MEUCIDiJC682+y1pZWPf4DlGsEy5GzqCqje0s42ZURNlyB+yAiEAzZ0+9MVyRyI4d0b9VvMbryH/CipJDgH3X+kub3ZVfZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedfs/CRA9TVsSAnZWagAAFd8QAI0oUsjS5gRkdVpkhFu3\n6SzPIdB44O9vJVnG0cYu65jTNGvi5Pm2/TeBgHzZka873aJXCbWv05iLZrMq\nJ9uuLUJGxtTO5DIMAU6CKbX9LwH3kPv5XFYMREu1Kv64wq4azFj0THglySIN\n54nda1JWMy6DiP5ukuGbgOI29AOIkascwZPwtpj8uHDVJH6kB6mQH8NPMUAC\nl9w4l7+L+aOVd4epU55Gd8hp0mNrTkp0XmuWt43T8gvmPd1aW2wK7civq1gs\nYxZNqzi7sIooMlnn/AcqaHyvfh7b6kwPZG6w3qKTELlIfPFhzYDOaq9YCLIw\ne2DXO5O/1Xkf1wik9zkRFkbNPTRlG3mtqiJ77NIXr4jWBTeE/FBT0oBJxm0K\nGv7vc/h9SreU/4vNM3pZeX3S3tk2DaOym4wcjXbLiB7QlnVTahuBb5xUKEwS\nM49zBKXLGj6DyvgWnTs1yz37+yCynNAMBWDO4xcIFOvfWkqfd88+hi7Jd1rF\ntPeKxrXnFUUlayKKg13gHTVu3VCyHGTs58TDDDGhVZRZT62DTIrlkommLbF2\nKQr//pmeWX4osj1yrdkbSUbrUgIAfPgMXGOvo+a2bzxEjEJsCPswWgZ635AZ\noqUPsLsFqb4KU1VD4kP7c1WGAfywgYIhr/XNwFCURfKA891VT/68bfvB9f/y\n/f1C\r\n=CTbl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "aed790f037736f5c760f7b120935714d21503fe9", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.2.2_1584790334576_0.87664955346259", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "picomatch", "version": "2.2.3", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.2.3", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "465547f359ccc206d3c48e46a1bcb89bf7ee619d", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.2.3.tgz", "fileCount": 10, "integrity": "sha512-KpELjfwcCDUb9PeigTs2mBJzXUPzAuP2oPcA989He8Rte0+YUAjw1JVedDhuTKPkHjSYzMN3npC9luThGYEKdg==", "signatures": [{"sig": "MEUCIDFM5GRFAmJV+DTWV2D5okMbKVgjFoqgYCBUz8fNM4wmAiEAhMFPEl7Y1LyrMOrDqoJpydl/zRi565veb9fP+QKP944=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcXqRCRA9TVsSAnZWagAAeYsP+gLSq2vCdVqWAAxLLXnj\n2+QiIYmPc9tNKdV495T7Bn4YLvN69CePpbtCQOxRTD97YJ/SHYEYVoewMdOn\nCuR7wPiVKwpqQleSaDjZLpyRMHeZ0jFg49UyJqfgZMQo+MWyBKAma9c9tu3Z\nT5lH7WDSe04FKQpXx7prh+zG7qjB3DEZxmSIGerOF9W7q5r7ceQuEQ3MRGfq\nR6tYq+K4OxQmMeV92OPDVDY+hEsp08/Vcd5iGrwhzGffjixF+AbNcWd2adyU\nmsB9nbGdQgWuZbaiFfIL6pqwoYt9PW5PaptEfLIytQOlFnQY//gj9/fq+1Ph\nSXq+Ab/HjyLZjqTKrty02dWr35mh74BtGRcO3wpczeNmnXQqIqjrehaXsKJY\nE5oZdCH005zdYr0OkmKJwjsJQ56qxCPUqX7k27yBwpd6HlbVDX97JeeUGlqV\nIlkcchioYDLHCSwRbifcH1iMjCDy466n/hfKPlLS5xylI+IgqKSTftSfvd1h\naHauH1u7dPmD102Hr+xSG10gZJ1Jel+kX4+fPry+HyXKoucEHd+OcRKYsWNY\nIr44J0dPa5Da1J3pG9XMjeg7bSUyqHzGctHWOb0KXeYWLpAk4ewnEUgecEjA\nf5h9UdLWkyaWLtMI5SOczE0/DOuj8n1Drc/JOySnjT2xXXBHOCjUXAMRT2Qk\negjU\r\n=NTKo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "8839c01247bc39a5837428dbdf381fd47c564f1f", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "danez", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "12.15.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.2.3_1618049680785_0.34621149383553895", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "picomatch", "version": "2.3.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.3.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "f1f061de8f6a4bf022892e2d128234fb98302972", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.0.tgz", "fileCount": 10, "integrity": "sha512-lY1Q/PiJGC2zOv/z391WOTD+Z02bCgsFfvxoXXf6h7kv9o+WmsmzYqrAwY63sNgOxE4xEdq0WyUnXfKeBrSvYw==", "signatures": [{"sig": "MEUCIFZvPCnULbhmORLFGzgibXMC35hzLq/rOtEVKRnDtSMRAiEA9YmmWGkYA2rOKeFLdCE4qsB46A/OaxKgNpCnQlx0raU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqGMhCRA9TVsSAnZWagAAVJUP/RuLJ4J0SmAisEU1gx/h\nCeNw5wzjEgvmEdSc3Vxp00z6uFR0r4eln5HCcQ9q+sYPBeq5AOvWlk78bYvL\nEw5fcokkB+nDmeP9JcQxuzUBwvfzwVqbt2GgOKaZYHA+ueVzSacTaRXAR17m\nlLcOS/i7N53rTyJSJmEa0Lsz3jhZSJ3tDtBN45fiw1GL6SneRxifPxbrer4w\ns0pLQGyZc13dS6mJHsAyIRbo8OTFBIp12u1L3chtYaTkg2J+jU+4aVC3xk1u\nJO8SwKlmKSw1dZxK9PSbZp12jajNTxvoUHb+9uabeepuhYDfKlAQk5qcD2fh\nJ/JPhEqw32Vp5KUiECr6IkAZrw/eWNyoHMDY5Iq/2xSAYBoC5uKLw6U88BC1\nn8TvJMt9KcTeb9WlUrRWA1FVcgUqtjuuD7jGiBSBKiFcjerjpaR49R94ZmIj\n/lx7fIvWzMDA1XhtpWgyOjZdmQ8S6nq4WYx9K4NOiS/pK8ms/U1YGg4pfQQu\nI9ipv5+XyOINt4KQydN+eeYXjyp6XacRrg/fSyU+A5+0fIXXeQLne127Yfcm\no/wHH6o+MUSgFsnH8wK2gERgTkvvPHUmNSLKuzfJ4wYPsRtBuI7UIGtmajXk\nKZN4giW780aLozkZPLuefPGuQqN6C8BAvUOGji05MDlNjKslT+NHgSrQHrfO\niBq5\r\n=Coa4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "68797253c84ffaf3c5386cdeb5325920fd96726a", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.3.0_1621648160214_0.689432455944144", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "picomatch", "version": "2.3.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@2.3.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "3ba3833733646d9d3e4995946c1365a67fb07a42", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "fileCount": 10, "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "signatures": [{"sig": "MEQCIHkNdBstV/YHtO1LuMPpQpESvlgEXck/xPmvVaWTlWyqAiAlQB/bZRsKFfDsuQB/hsPD7oVyVf544JyspNpfEGBbXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0d5gCRA9TVsSAnZWagAAhvsP/3X0Wzp61vQEMw/gnra7\nCpQhnAtHPgHqZkNz8zCtuudav0Hdp4qNVCQKPcNeFHjYdJFKbFf3C97nPvEr\nVxtxYEQDvk4pkmpLqtY34oFKlT7nlezLjOigwL9GxjBlTqy35mgIx4Hemf3R\nUuKHDfHvQQ+4hH66cDlZwv3rWEMVTrkqXQblQ4ZVnAQuvnhhM1ehIUuSpw2y\ncJ+7DMNW4sbEAMWTK28F/v1IBnlcN3ENY/387Z8nqedY1oJZpmgK1iVojHv6\n29dDa+la+GR5cBQGXBXj66t89kK/7p6BQMSYUJ+FcecHBPZ+rW5rAGXJlzx7\nqw3QdUZQ5TQzcRR/4MJ1ALKEM6Pk7COdO0+RJ+ST0Jitw3t+PZkXGgG3lIW/\ngOghIdK1jqP7Fy3y82ECOwst0akaqX2I1Oe3tktn8CMvlbwg6jaPp8l7jI7d\ncaWZx+peRszFxqY5GicA42xDs1UQ0YyWQeowkLUcFswF2YihfccEZ3JqfGZL\nKPNH1i55aZc4nRX5vajmq7lCSXgRYgGZ18s/UOQXWU/BHY1+GGF4zf4h0Spi\nEHCofcLg7umyRvfvj//Ig8qtUHNUxgs7ioN3tgXq63Gdu8KYN0hU5RwTxSDR\nzboyO+txrwwtntrbZMOIL667Ndfnj9FtMfLdEjBdZBXfddOXbCJPErG6ZoEW\ninis\r\n=D34Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "5467a5a9638472610de4f30709991b9a56bb5613", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "12.22.8", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_2.3.1_1641143904045_0.8104619493917313", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "picomatch", "version": "3.0.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@3.0.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "95ce1a281a24a340c3ddc981a63d89360c6dab9b", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-G+bUnjy7wQOJBK3YS6hErp7nUOJ+SWI+JZeDEATcdAjnHZwjJ6TXm4TGOHgGScf5lCNGdT9rXdqIa0tJ3lYhnQ==", "signatures": [{"sig": "MEUCIQDlgwNuwXNcGNrSM2miknioTM5TQ6E7+nkpnomdbvnl3wIgZgQM+olhYLgVx1q12907ucYPHoxVfvtPVgU9sT5c4jA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85254}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=8.6"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "bc719c4c7ee0b1f7e1d1bdd27d60ae1779a43d89", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_3.0.0_1698541101595_0.18540257320350095", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "picomatch", "version": "3.0.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@3.0.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "817033161def55ec9638567a2f3bbc876b3e7516", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-I3<PERSON>urrIQMlRc9IaAZnqRR044Phh2DXY+55o7uJ0V+hYZAcQYSuFWsc9q5PvyDHUSCe1Qxn/iBz+78s86zWnGag==", "signatures": [{"sig": "MEYCIQCBshR2DnpauYeCpPH2PdLEzVVn/6IVwxX5cKcyKw0IvAIhALWEelCNTAwW0WwNliGRF1uV888N3KKpr+pxbwcc/rXM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85253}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "bc719c4c7ee0b1f7e1d1bdd27d60ae1779a43d89", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_3.0.1_1698567169869_0.4683729438350761", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "picomatch", "version": "4.0.0", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@4.0.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "8410b450c046948141ca7bd8adfc8cf753a4652a", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-luYHSuwP2yHKySJ5J1ujgN2gSGFP+Ua6F1dvOTusl392lTqouuf5KO3QFRbc8nBM1uWiDhltecTxnij9wIorAA==", "signatures": [{"sig": "MEUCIQCJbuXzmUIOMdoVNnsqL81eIs7X7diCc/TkH+4oa4DfvAIgUu7HvSrrNRFmED1HZ3SncfvnwcVDurFd49+2W5RSg28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85192}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "9db2a4ad919d7dd0182513d7d211393a021bb91e", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_4.0.0_1707358931964_0.759314750403971", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "picomatch", "version": "4.0.1", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@4.0.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "68c26c8837399e5819edce48590412ea07f17a07", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.1.tgz", "fileCount": 10, "integrity": "sha512-xUXwsxNjwTQ8K3GnT4pCJm+xq3RUPQbmkYJTP5aFIfNIvbcc/4MUxgBaaRSZJ6yGJZiGSyYlM6MzwTsRk8SYCg==", "signatures": [{"sig": "MEUCIHpZMEvmMlm+TOQuad1AEOJydXIMsmBfnlaYa9g7RLsAAiEAphtzMzPrvk5PaPdc3zMRFy0hXjMYtdu0DNxS5zQsZjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85192}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "6ce95f5e008590510adac57d1910e574aa65845f", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_4.0.1_1707359513149_0.6004248698501251", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "picomatch", "version": "4.0.2", "keywords": ["glob", "match", "picomatch"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "picomatch@4.0.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "dist": {"shasum": "77c742931e8f3b8820946c76cd0c1f13730d1dab", "tarball": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "fileCount": 10, "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "signatures": [{"sig": "MEUCIEtbvwmKkqlXNXSqLwMF1mRFHpVKpPVOPu7o3tmOeZbrAiEAuTHJPb1fKWoHwYrKEQpmPkOLBMChBX1HltMmP7P6PBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85237}, "main": "index.js", "verb": {"toc": {"method": "preWrite", "render": true, "maxdepth": 3}, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "empty", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/jonschlinkert", "gitHead": "d958901678e12fb10c4e65dfe31e245931e719b3", "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --reporter dot", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^10.4.0", "eslint": "^8.57.0", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/picomatch_4.0.2_1711596191194_0.31621911647882506", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2018-11-05T12:07:08.561Z", "modified": "2024-09-18T05:27:52.363Z", "1.0.0": "2018-11-05T12:07:08.695Z", "1.0.1": "2018-11-05T12:12:00.358Z", "1.0.2": "2018-11-05T13:45:29.274Z", "1.1.0": "2018-12-14T17:18:24.238Z", "1.1.1": "2018-12-15T03:09:48.333Z", "1.1.2": "2018-12-15T03:13:01.124Z", "1.2.0": "2019-03-30T02:20:22.763Z", "2.0.0": "2019-04-10T11:03:29.057Z", "2.0.1": "2019-04-10T11:50:06.709Z", "2.0.2": "2019-04-10T12:16:36.700Z", "2.0.3": "2019-04-10T12:43:39.432Z", "2.0.4": "2019-04-14T06:34:51.007Z", "2.0.5": "2019-04-20T15:42:51.621Z", "2.0.6": "2019-05-04T08:17:27.543Z", "2.0.7": "2019-05-14T17:41:22.540Z", "2.1.0": "2019-10-31T08:03:28.846Z", "2.1.1": "2019-11-06T22:23:26.115Z", "2.2.0": "2020-01-04T20:31:40.843Z", "2.2.1": "2020-01-04T21:19:48.019Z", "2.2.2": "2020-03-21T11:32:14.696Z", "2.2.3": "2021-04-10T10:14:40.929Z", "2.3.0": "2021-05-22T01:49:20.438Z", "2.3.1": "2022-01-02T17:18:24.430Z", "3.0.0": "2023-10-29T00:58:21.803Z", "3.0.1": "2023-10-29T08:12:50.118Z", "4.0.0": "2024-02-08T02:22:12.147Z", "4.0.1": "2024-02-08T02:31:53.301Z", "4.0.2": "2024-03-28T03:23:11.348Z"}, "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/micromatch/picomatch", "keywords": ["glob", "match", "picomatch"], "repository": {"url": "git+https://github.com/micromatch/picomatch.git", "type": "git"}, "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "maintainers": [{"email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru", "name": "mrmlnc"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "danez"}], "readme": "<h1 align=\"center\">Pi<PERSON>atch</h1>\n\n<p align=\"center\">\n<a href=\"https://npmjs.org/package/picomatch\">\n<img src=\"https://img.shields.io/npm/v/picomatch.svg\" alt=\"version\">\n</a>\n<a href=\"https://github.com/micromatch/picomatch/actions?workflow=Tests\">\n<img src=\"https://github.com/micromatch/picomatch/workflows/Tests/badge.svg\" alt=\"test status\">\n</a>\n<a href=\"https://coveralls.io/github/micromatch/picomatch\">\n<img src=\"https://img.shields.io/coveralls/github/micromatch/picomatch/master.svg\" alt=\"coverage status\">\n</a>\n<a href=\"https://npmjs.org/package/picomatch\">\n<img src=\"https://img.shields.io/npm/dm/picomatch.svg\" alt=\"downloads\">\n</a>\n</p>\n\n<br>\n<br>\n\n<p align=\"center\">\n<strong>Blazing fast and accurate glob matcher written in JavaScript.</strong></br>\n<em>No dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.</em>\n</p>\n\n<br>\n<br>\n\n## Why picomatch?\n\n* **Lightweight** - No dependencies\n* **Minimal** - Tiny API surface. Main export is a function that takes a glob pattern and returns a matcher function.\n* **Fast** - Loads in about 2ms (that's several times faster than a [single frame of a HD movie](http://www.endmemo.com/sconvert/framespersecondframespermillisecond.php) at 60fps)\n* **Performant** - Use the returned matcher function to speed up repeat matching (like when watching files)\n* **Accurate matching** - Using wildcards (`*` and `?`), globstars (`**`) for nested directories, [advanced globbing](#advanced-globbing) with extglobs, braces, and POSIX brackets, and support for escaping special characters with `\\` or quotes.\n* **Well tested** - Thousands of unit tests\n\nSee the [library comparison](#library-comparisons) to other libraries.\n\n<br>\n<br>\n\n## Table of Contents\n\n<details><summary> Click to expand </summary>\n\n- [Install](#install)\n- [Usage](#usage)\n- [API](#api)\n  * [picomatch](#picomatch)\n  * [.test](#test)\n  * [.matchBase](#matchbase)\n  * [.isMatch](#ismatch)\n  * [.parse](#parse)\n  * [.scan](#scan)\n  * [.compileRe](#compilere)\n  * [.makeRe](#makere)\n  * [.toRegex](#toregex)\n- [Options](#options)\n  * [Picomatch options](#picomatch-options)\n  * [Scan Options](#scan-options)\n  * [Options Examples](#options-examples)\n- [Globbing features](#globbing-features)\n  * [Basic globbing](#basic-globbing)\n  * [Advanced globbing](#advanced-globbing)\n  * [Braces](#braces)\n  * [Matching special characters as literals](#matching-special-characters-as-literals)\n- [Library Comparisons](#library-comparisons)\n- [Benchmarks](#benchmarks)\n- [Philosophies](#philosophies)\n- [About](#about)\n  * [Author](#author)\n  * [License](#license)\n\n_(TOC generated by [verb](https://github.com/verbose/verb) using [markdown-toc](https://github.com/jonschlinkert/markdown-toc))_\n\n</details>\n\n<br>\n<br>\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\nnpm install --save picomatch\n```\n\n<br>\n\n## Usage\n\nThe main export is a function that takes a glob pattern and an options object and returns a function for matching strings.\n\n```js\nconst pm = require('picomatch');\nconst isMatch = pm('*.js');\n\nconsole.log(isMatch('abcd')); //=> false\nconsole.log(isMatch('a.js')); //=> true\nconsole.log(isMatch('a.md')); //=> false\nconsole.log(isMatch('a/b.js')); //=> false\n```\n\n<br>\n\n## API\n\n### [picomatch](lib/picomatch.js#L31)\n\nCreates a matcher function from one or more glob patterns. The returned function takes a string to match as its first argument, and returns true if the string is a match. The returned matcher function also takes a boolean as the second argument that, when true, returns an object with additional information.\n\n**Params**\n\n* `globs` **{String|Array}**: One or more glob patterns.\n* `options` **{Object=}**\n* `returns` **{Function=}**: Returns a matcher function.\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\n// picomatch(glob[, options]);\n\nconst isMatch = picomatch('*.!(*a)');\nconsole.log(isMatch('a.a')); //=> false\nconsole.log(isMatch('a.b')); //=> true\n```\n\n**Example without node.js**\n\nFor environments without `node.js`, `picomatch/posix` provides you a dependency-free matcher, without automatic OS detection.\n\n```js\nconst picomatch = require('picomatch/posix');\n// the same API, defaulting to posix paths\nconst isMatch = picomatch('a/*');\nconsole.log(isMatch('a\\\\b')); //=> false\nconsole.log(isMatch('a/b')); //=> true\n\n// you can still configure the matcher function to accept windows paths\nconst isMatch = picomatch('a/*', { options: windows });\nconsole.log(isMatch('a\\\\b')); //=> true\nconsole.log(isMatch('a/b')); //=> true\n```\n\n### [.test](lib/picomatch.js#L116)\n\nTest `input` with the given `regex`. This is used by the main `picomatch()` function to test the input string.\n\n**Params**\n\n* `input` **{String}**: String to test.\n* `regex` **{RegExp}**\n* `returns` **{Object}**: Returns an object with matching info.\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\n// picomatch.test(input, regex[, options]);\n\nconsole.log(picomatch.test('foo/bar', /^(?:([^/]*?)\\/([^/]*?))$/));\n// { isMatch: true, match: [ 'foo/', 'foo', 'bar' ], output: 'foo/bar' }\n```\n\n### [.matchBase](lib/picomatch.js#L160)\n\nMatch the basename of a filepath.\n\n**Params**\n\n* `input` **{String}**: String to test.\n* `glob` **{RegExp|String}**: Glob pattern or regex created by [.makeRe](#makeRe).\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\n// picomatch.matchBase(input, glob[, options]);\nconsole.log(picomatch.matchBase('foo/bar.js', '*.js'); // true\n```\n\n### [.isMatch](lib/picomatch.js#L182)\n\nReturns true if **any** of the given glob `patterns` match the specified `string`.\n\n**Params**\n\n* **{String|Array}**: str The string to test.\n* **{String|Array}**: patterns One or more glob patterns to use for matching.\n* **{Object}**: See available [options](#options).\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\n// picomatch.isMatch(string, patterns[, options]);\n\nconsole.log(picomatch.isMatch('a.a', ['b.*', '*.a'])); //=> true\nconsole.log(picomatch.isMatch('a.a', 'b.*')); //=> false\n```\n\n### [.parse](lib/picomatch.js#L198)\n\nParse a glob pattern to create the source string for a regular expression.\n\n**Params**\n\n* `pattern` **{String}**\n* `options` **{Object}**\n* `returns` **{Object}**: Returns an object with useful properties and output to be used as a regex source string.\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\nconst result = picomatch.parse(pattern[, options]);\n```\n\n### [.scan](lib/picomatch.js#L230)\n\nScan a glob pattern to separate the pattern into segments.\n\n**Params**\n\n* `input` **{String}**: Glob pattern to scan.\n* `options` **{Object}**\n* `returns` **{Object}**: Returns an object with\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\n// picomatch.scan(input[, options]);\n\nconst result = picomatch.scan('!./foo/*.js');\nconsole.log(result);\n{ prefix: '!./',\n  input: '!./foo/*.js',\n  start: 3,\n  base: 'foo',\n  glob: '*.js',\n  isBrace: false,\n  isBracket: false,\n  isGlob: true,\n  isExtglob: false,\n  isGlobstar: false,\n  negated: true }\n```\n\n### [.compileRe](lib/picomatch.js#L244)\n\nCompile a regular expression from the `state` object returned by the\n[parse()](#parse) method.\n\n**Params**\n\n* `state` **{Object}**\n* `options` **{Object}**\n* `returnOutput` **{Boolean}**: Intended for implementors, this argument allows you to return the raw output from the parser.\n* `returnState` **{Boolean}**: Adds the state to a `state` property on the returned regex. Useful for implementors and debugging.\n* `returns` **{RegExp}**\n\n### [.makeRe](lib/picomatch.js#L285)\n\nCreate a regular expression from a parsed glob pattern.\n\n**Params**\n\n* `state` **{String}**: The object returned from the `.parse` method.\n* `options` **{Object}**\n* `returnOutput` **{Boolean}**: Implementors may use this argument to return the compiled output, instead of a regular expression. This is not exposed on the options to prevent end-users from mutating the result.\n* `returnState` **{Boolean}**: Implementors may use this argument to return the state from the parsed glob with the returned regular expression.\n* `returns` **{RegExp}**: Returns a regex created from the given pattern.\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\nconst state = picomatch.parse('*.js');\n// picomatch.compileRe(state[, options]);\n\nconsole.log(picomatch.compileRe(state));\n//=> /^(?:(?!\\.)(?=.)[^/]*?\\.js)$/\n```\n\n### [.toRegex](lib/picomatch.js#L320)\n\nCreate a regular expression from the given regex source string.\n\n**Params**\n\n* `source` **{String}**: Regular expression source string.\n* `options` **{Object}**\n* `returns` **{RegExp}**\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\n// picomatch.toRegex(source[, options]);\n\nconst { output } = picomatch.parse('*.js');\nconsole.log(picomatch.toRegex(output));\n//=> /^(?:(?!\\.)(?=.)[^/]*?\\.js)$/\n```\n\n<br>\n\n## Options\n\n### Picomatch options\n\nThe following options may be used with the main `picomatch()` function or any of the methods on the picomatch API.\n\n| **Option** | **Type** | **Default value** | **Description** |\n| --- | --- | --- | --- |\n| `basename`            | `boolean`      | `false`     | If set, then patterns without slashes will be matched against the basename of the path if it contains slashes.  For example, `a?b` would match the path `/xyz/123/acb`, but not `/xyz/acb/123`. |\n| `bash`                | `boolean`      | `false`     | Follow bash matching rules more strictly - disallows backslashes as escape characters, and treats single stars as globstars (`**`). |\n| `capture`             | `boolean`      | `undefined` | Return regex matches in supporting methods. |\n| `contains`            | `boolean`      | `undefined` | Allows glob to match any part of the given string(s). |\n| `cwd`                 | `string`       | `process.cwd()` | Current working directory. Used by `picomatch.split()` |\n| `debug`               | `boolean`      | `undefined` | Debug regular expressions when an error is thrown. |\n| `dot`                 | `boolean`      | `false`     | Enable dotfile matching. By default, dotfiles are ignored unless a `.` is explicitly defined in the pattern, or `options.dot` is true |\n| `expandRange`         | `function`     | `undefined` | Custom function for expanding ranges in brace patterns, such as `{a..z}`. The function receives the range values as two arguments, and it must return a string to be used in the generated regex. It's recommended that returned strings be wrapped in parentheses. |\n| `failglob`            | `boolean`      | `false`     | Throws an error if no matches are found. Based on the bash option of the same name. |\n| `fastpaths`           | `boolean`      | `true`      | To speed up processing, full parsing is skipped for a handful common glob patterns. Disable this behavior by setting this option to `false`. |\n| `flags`               | `string`      | `undefined` | Regex flags to use in the generated regex. If defined, the `nocase` option will be overridden. |\n| [format](#optionsformat) | `function` | `undefined` | Custom function for formatting the returned string. This is useful for removing leading slashes, converting Windows paths to Posix paths, etc. |\n| `ignore`              | `array\\|string` | `undefined` | One or more glob patterns for excluding strings that should not be matched from the result. |\n| `keepQuotes`          | `boolean`      | `false`     | Retain quotes in the generated regex, since quotes may also be used as an alternative to backslashes.  |\n| `literalBrackets`     | `boolean`      | `undefined` | When `true`, brackets in the glob pattern will be escaped so that only literal brackets will be matched. |\n| `matchBase`           | `boolean`      | `false`     | Alias for `basename` |\n| `maxLength`           | `boolean`      | `65536`     | Limit the max length of the input string. An error is thrown if the input string is longer than this value. |\n| `nobrace`             | `boolean`      | `false`     | Disable brace matching, so that `{a,b}` and `{1..3}` would be treated as literal characters. |\n| `nobracket`           | `boolean`      | `undefined` | Disable matching with regex brackets. |\n| `nocase`              | `boolean`      | `false`     | Make matching case-insensitive. Equivalent to the regex `i` flag. Note that this option is overridden by the `flags` option. |\n| `nodupes`             | `boolean`      | `true`      | Deprecated, use `nounique` instead. This option will be removed in a future major release. By default duplicates are removed. Disable uniquification by setting this option to false. |\n| `noext`               | `boolean`      | `false`     | Alias for `noextglob` |\n| `noextglob`           | `boolean`      | `false`     | Disable support for matching with extglobs (like `+(a\\|b)`) |\n| `noglobstar`          | `boolean`      | `false`     | Disable support for matching nested directories with globstars (`**`) |\n| `nonegate`            | `boolean`      | `false`     | Disable support for negating with leading `!` |\n| `noquantifiers`       | `boolean`      | `false`     | Disable support for regex quantifiers (like `a{1,2}`) and treat them as brace patterns to be expanded. |\n| [onIgnore](#optionsonIgnore) | `function` | `undefined` | Function to be called on ignored items. |\n| [onMatch](#optionsonMatch) | `function` | `undefined` | Function to be called on matched items. |\n| [onResult](#optionsonResult) | `function` | `undefined` | Function to be called on all items, regardless of whether or not they are matched or ignored. |\n| `posix`               | `boolean`      | `false`     | Support POSIX character classes (\"posix brackets\"). |\n| `posixSlashes`        | `boolean`      | `undefined` | Convert all slashes in file paths to forward slashes. This does not convert slashes in the glob pattern itself |\n| `prepend`             | `boolean`      | `undefined` | String to prepend to the generated regex used for matching. |\n| `regex`               | `boolean`      | `false`     | Use regular expression rules for `+` (instead of matching literal `+`), and for stars that follow closing parentheses or brackets (as in `)*` and `]*`). |\n| `strictBrackets`      | `boolean`      | `undefined` | Throw an error if brackets, braces, or parens are imbalanced. |\n| `strictSlashes`       | `boolean`      | `undefined` | When true, picomatch won't match trailing slashes with single stars. |\n| `unescape`            | `boolean`      | `undefined` | Remove backslashes preceding escaped characters in the glob pattern. By default, backslashes are retained. |\n| `unixify`             | `boolean`      | `undefined` | Alias for `posixSlashes`, for backwards compatibility. |\n| `windows`             | `boolean`      | `false`     | Also accept backslashes as the path separator. |\n\n### Scan Options\n\nIn addition to the main [picomatch options](#picomatch-options), the following options may also be used with the [.scan](#scan) method.\n\n| **Option** | **Type** | **Default value** | **Description** |\n| --- | --- | --- | --- |\n| `tokens` | `boolean` | `false` | When `true`, the returned object will include an array of tokens (objects), representing each path \"segment\" in the scanned glob pattern |\n| `parts` | `boolean` | `false` | When `true`, the returned object will include an array of strings representing each path \"segment\" in the scanned glob pattern. This is automatically enabled when `options.tokens` is true |\n\n**Example**\n\n```js\nconst picomatch = require('picomatch');\nconst result = picomatch.scan('!./foo/*.js', { tokens: true });\nconsole.log(result);\n// {\n//   prefix: '!./',\n//   input: '!./foo/*.js',\n//   start: 3,\n//   base: 'foo',\n//   glob: '*.js',\n//   isBrace: false,\n//   isBracket: false,\n//   isGlob: true,\n//   isExtglob: false,\n//   isGlobstar: false,\n//   negated: true,\n//   maxDepth: 2,\n//   tokens: [\n//     { value: '!./', depth: 0, isGlob: false, negated: true, isPrefix: true },\n//     { value: 'foo', depth: 1, isGlob: false },\n//     { value: '*.js', depth: 1, isGlob: true }\n//   ],\n//   slashes: [ 2, 6 ],\n//   parts: [ 'foo', '*.js' ]\n// }\n```\n\n<br>\n\n### Options Examples\n\n#### options.expandRange\n\n**Type**: `function`\n\n**Default**: `undefined`\n\nCustom function for expanding ranges in brace patterns. The [fill-range](https://github.com/jonschlinkert/fill-range) library is ideal for this purpose, or you can use custom code to do whatever you need.\n\n**Example**\n\nThe following example shows how to create a glob that matches a folder\n\n```js\nconst fill = require('fill-range');\nconst regex = pm.makeRe('foo/{01..25}/bar', {\n  expandRange(a, b) {\n    return `(${fill(a, b, { toRegex: true })})`;\n  }\n});\n\nconsole.log(regex);\n//=> /^(?:foo\\/((?:0[1-9]|1[0-9]|2[0-5]))\\/bar)$/\n\nconsole.log(regex.test('foo/00/bar'))  // false\nconsole.log(regex.test('foo/01/bar'))  // true\nconsole.log(regex.test('foo/10/bar')) // true\nconsole.log(regex.test('foo/22/bar')) // true\nconsole.log(regex.test('foo/25/bar')) // true\nconsole.log(regex.test('foo/26/bar')) // false\n```\n\n#### options.format\n\n**Type**: `function`\n\n**Default**: `undefined`\n\nCustom function for formatting strings before they're matched.\n\n**Example**\n\n```js\n// strip leading './' from strings\nconst format = str => str.replace(/^\\.\\//, '');\nconst isMatch = picomatch('foo/*.js', { format });\nconsole.log(isMatch('./foo/bar.js')); //=> true\n```\n\n#### options.onMatch\n\n```js\nconst onMatch = ({ glob, regex, input, output }) => {\n  console.log({ glob, regex, input, output });\n};\n\nconst isMatch = picomatch('*', { onMatch });\nisMatch('foo');\nisMatch('bar');\nisMatch('baz');\n```\n\n#### options.onIgnore\n\n```js\nconst onIgnore = ({ glob, regex, input, output }) => {\n  console.log({ glob, regex, input, output });\n};\n\nconst isMatch = picomatch('*', { onIgnore, ignore: 'f*' });\nisMatch('foo');\nisMatch('bar');\nisMatch('baz');\n```\n\n#### options.onResult\n\n```js\nconst onResult = ({ glob, regex, input, output }) => {\n  console.log({ glob, regex, input, output });\n};\n\nconst isMatch = picomatch('*', { onResult, ignore: 'f*' });\nisMatch('foo');\nisMatch('bar');\nisMatch('baz');\n```\n\n<br>\n<br>\n\n## Globbing features\n\n* [Basic globbing](#basic-globbing) (Wildcard matching)\n* [Advanced globbing](#advanced-globbing) (extglobs, posix brackets, brace matching)\n\n### Basic globbing\n\n| **Character** | **Description** |\n| --- | --- |\n| `*` | Matches any character zero or more times, excluding path separators. Does _not match_ path separators or hidden files or directories (\"dotfiles\"), unless explicitly enabled by setting the `dot` option to `true`. |\n| `**` | Matches any character zero or more times, including path separators. Note that `**` will only match path separators (`/`, and `\\\\` with the `windows` option) when they are the only characters in a path segment. Thus, `foo**/bar` is equivalent to `foo*/bar`, and `foo/a**b/bar` is equivalent to `foo/a*b/bar`, and _more than two_ consecutive stars in a glob path segment are regarded as _a single star_. Thus, `foo/***/bar` is equivalent to `foo/*/bar`. |\n| `?` | Matches any character excluding path separators one time. Does _not match_ path separators or leading dots.  |\n| `[abc]` | Matches any characters inside the brackets. For example, `[abc]` would match the characters `a`, `b` or `c`, and nothing else. |\n\n#### Matching behavior vs. Bash\n\nPicomatch's matching features and expected results in unit tests are based on Bash's unit tests and the Bash 4.3 specification, with the following exceptions:\n\n* Bash will match `foo/bar/baz` with `*`. Picomatch only matches nested directories with `**`.\n* Bash greedily matches with negated extglobs. For example, Bash 4.3 says that `!(foo)*` should match `foo` and `foobar`, since the trailing `*` bracktracks to match the preceding pattern. This is very memory-inefficient, and IMHO, also incorrect. Picomatch would return `false` for both `foo` and `foobar`.\n\n<br>\n\n### Advanced globbing\n\n* [extglobs](#extglobs)\n* [POSIX brackets](#posix-brackets)\n* [Braces](#brace-expansion)\n\n#### Extglobs\n\n| **Pattern** | **Description** |\n| --- | --- |\n| `@(pattern)` | Match _only one_ consecutive occurrence of `pattern` |\n| `*(pattern)` | Match _zero or more_ consecutive occurrences of `pattern` |\n| `+(pattern)` | Match _one or more_ consecutive occurrences of `pattern` |\n| `?(pattern)` | Match _zero or **one**_ consecutive occurrences of `pattern` |\n| `!(pattern)` | Match _anything but_ `pattern` |\n\n**Examples**\n\n```js\nconst pm = require('picomatch');\n\n// *(pattern) matches ZERO or more of \"pattern\"\nconsole.log(pm.isMatch('a', 'a*(z)')); // true\nconsole.log(pm.isMatch('az', 'a*(z)')); // true\nconsole.log(pm.isMatch('azzz', 'a*(z)')); // true\n\n// +(pattern) matches ONE or more of \"pattern\"\nconsole.log(pm.isMatch('a', 'a+(z)')); // false\nconsole.log(pm.isMatch('az', 'a+(z)')); // true\nconsole.log(pm.isMatch('azzz', 'a+(z)')); // true\n\n// supports multiple extglobs\nconsole.log(pm.isMatch('foo.bar', '!(foo).!(bar)')); // false\n\n// supports nested extglobs\nconsole.log(pm.isMatch('foo.bar', '!(!(foo)).!(!(bar))')); // true\n```\n\n#### POSIX brackets\n\nPOSIX classes are disabled by default. Enable this feature by setting the `posix` option to true.\n\n**Enable POSIX bracket support**\n\n```js\nconsole.log(pm.makeRe('[[:word:]]+', { posix: true }));\n//=> /^(?:(?=.)[A-Za-z0-9_]+\\/?)$/\n```\n\n**Supported POSIX classes**\n\nThe following named POSIX bracket expressions are supported:\n\n* `[:alnum:]` - Alphanumeric characters, equ `[a-zA-Z0-9]`\n* `[:alpha:]` - Alphabetical characters, equivalent to `[a-zA-Z]`.\n* `[:ascii:]` - ASCII characters, equivalent to `[\\\\x00-\\\\x7F]`.\n* `[:blank:]` - Space and tab characters, equivalent to `[ \\\\t]`.\n* `[:cntrl:]` - Control characters, equivalent to `[\\\\x00-\\\\x1F\\\\x7F]`.\n* `[:digit:]` - Numerical digits, equivalent to `[0-9]`.\n* `[:graph:]` - Graph characters, equivalent to `[\\\\x21-\\\\x7E]`.\n* `[:lower:]` - Lowercase letters, equivalent to `[a-z]`.\n* `[:print:]` - Print characters, equivalent to `[\\\\x20-\\\\x7E ]`.\n* `[:punct:]` - Punctuation and symbols, equivalent to `[\\\\-!\"#$%&\\'()\\\\*+,./:;<=>?@[\\\\]^_`{|}~]`.\n* `[:space:]` - Extended space characters, equivalent to `[ \\\\t\\\\r\\\\n\\\\v\\\\f]`.\n* `[:upper:]` - Uppercase letters, equivalent to `[A-Z]`.\n* `[:word:]` -  Word characters (letters, numbers and underscores), equivalent to `[A-Za-z0-9_]`.\n* `[:xdigit:]` - Hexadecimal digits, equivalent to `[A-Fa-f0-9]`.\n\nSee the [Bash Reference Manual](https://www.gnu.org/software/bash/manual/html_node/Pattern-Matching.html) for more information.\n\n### Braces\n\nPicomatch does not do brace expansion. For [brace expansion](https://www.gnu.org/software/bash/manual/html_node/Brace-Expansion.html) and advanced matching with braces, use [micromatch](https://github.com/micromatch/micromatch) instead. Picomatch has very basic support for braces.\n\n### Matching special characters as literals\n\nIf you wish to match the following special characters in a filepath, and you want to use these characters in your glob pattern, they must be escaped with backslashes or quotes:\n\n**Special Characters**\n\nSome characters that are used for matching in regular expressions are also regarded as valid file path characters on some platforms.\n\nTo match any of the following characters as literals: `$^*+?()[]\n\nExamples:\n\n```js\nconsole.log(pm.makeRe('foo/bar \\\\(1\\\\)'));\nconsole.log(pm.makeRe('foo/bar \\\\(1\\\\)'));\n```\n\n<br>\n<br>\n\n## Library Comparisons\n\nThe following table shows which features are supported by [minimatch](https://github.com/isaacs/minimatch), [micromatch](https://github.com/micromatch/micromatch), [picomatch](https://github.com/micromatch/picomatch), [nanomatch](https://github.com/micromatch/nanomatch), [extglob](https://github.com/micromatch/extglob), [braces](https://github.com/micromatch/braces), and [expand-brackets](https://github.com/micromatch/expand-brackets).\n\n| **Feature** | `minimatch` | `micromatch` | `picomatch` | `nanomatch` | `extglob` | `braces` | `expand-brackets` |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| Wildcard matching (`*?+`) | ✔ | ✔ | ✔ | ✔ | - | - | - |\n| Advancing globbing        | ✔ | ✔ | ✔ | - | - | - | - |\n| Brace _matching_          | ✔ | ✔ | ✔ | - | - | ✔ | - |\n| Brace _expansion_         | ✔ | ✔ | - | - | - | ✔ | - |\n| Extglobs                  | partial | ✔ | ✔ | - | ✔ | - | - |\n| Posix brackets            | - | ✔ | ✔ | - | - | - | ✔ |\n| Regular expression syntax | - | ✔ | ✔ | ✔ | ✔ | - | ✔ |\n| File system operations    | - | - | - | - | - | - | - |\n\n<br>\n<br>\n\n## Benchmarks\n\nPerformance comparison of picomatch and minimatch.\n\n_(Pay special attention to the last three benchmarks. Minimatch freezes on long ranges.)_\n\n```\n# .makeRe star (*)\n  picomatch x 4,449,159 ops/sec ±0.24% (97 runs sampled)\n  minimatch x 632,772 ops/sec ±0.14% (98 runs sampled)\n\n# .makeRe star; dot=true (*)\n  picomatch x 3,500,079 ops/sec ±0.26% (99 runs sampled)\n  minimatch x 564,916 ops/sec ±0.23% (96 runs sampled)\n\n# .makeRe globstar (**)\n  picomatch x 3,261,000 ops/sec ±0.27% (98 runs sampled)\n  minimatch x 1,664,766 ops/sec ±0.20% (100 runs sampled)\n\n# .makeRe globstars (**/**/**)\n  picomatch x 3,284,469 ops/sec ±0.18% (97 runs sampled)\n  minimatch x 1,435,880 ops/sec ±0.34% (95 runs sampled)\n\n# .makeRe with leading star (*.txt)\n  picomatch x 3,100,197 ops/sec ±0.35% (99 runs sampled)\n  minimatch x 428,347 ops/sec ±0.42% (94 runs sampled)\n\n# .makeRe - basic braces ({a,b,c}*.txt)\n  picomatch x 443,578 ops/sec ±1.33% (89 runs sampled)\n  minimatch x 107,143 ops/sec ±0.35% (94 runs sampled)\n\n# .makeRe - short ranges ({a..z}*.txt)\n  picomatch x 415,484 ops/sec ±0.76% (96 runs sampled)\n  minimatch x 14,299 ops/sec ±0.26% (96 runs sampled)\n\n# .makeRe - medium ranges ({1..100000}*.txt)\n  picomatch x 395,020 ops/sec ±0.87% (89 runs sampled)\n  minimatch x 2 ops/sec ±4.59% (10 runs sampled)\n\n# .makeRe - long ranges ({1..10000000}*.txt)\n  picomatch x 400,036 ops/sec ±0.83% (90 runs sampled)\n  minimatch (FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory)\n```\n\n<br>\n<br>\n\n## Philosophies\n\nThe goal of this library is to be blazing fast, without compromising on accuracy.\n\n**Accuracy**\n\nThe number one of goal of this library is accuracy. However, it's not unusual for different glob implementations to have different rules for matching behavior, even with simple wildcard matching. It gets increasingly more complicated when combinations of different features are combined, like when extglobs are combined with globstars, braces, slashes, and so on: `!(**/{a,b,*/c})`.\n\nThus, given that there is no canonical glob specification to use as a single source of truth when differences of opinion arise regarding behavior, sometimes we have to implement our best judgement and rely on feedback from users to make improvements.\n\n**Performance**\n\nAlthough this library performs well in benchmarks, and in most cases it's faster than other popular libraries we benchmarked against, we will always choose accuracy over performance. It's not helpful to anyone if our library is faster at returning the wrong answer.\n\n<br>\n<br>\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\nPlease read the [contributing guide](.github/contributing.md) for advice on opening issues, pull requests, and coding standards.\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\nnpm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\nnpm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2017-present, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n", "readmeFilename": "README.md", "users": {"yanrivera": true, "flumpus-dev": true}}