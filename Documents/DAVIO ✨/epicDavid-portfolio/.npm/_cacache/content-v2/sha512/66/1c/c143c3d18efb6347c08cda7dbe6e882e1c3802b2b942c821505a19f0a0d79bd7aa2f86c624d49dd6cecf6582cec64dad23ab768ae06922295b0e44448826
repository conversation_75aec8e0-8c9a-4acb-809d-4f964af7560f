{"_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "_rev": "43-158f555cb12cb004843f255e93ab1e85", "name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.16.0": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.16.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "efb7f147042aca34ce8156a055906a7abaadeaf0", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-djyecbGMEh4rOb/Tc1M5bUW2Ih1IZRa9PoubnPOCzM+DRE89uGUHR1Y+3aDdTMW4drjGRZ2ol8dt1JUFg6hJLQ==", "signatures": [{"sig": "MEUCIDCRYLveKNNWlKLfHhBWF4bcmyBIY3YQu6rUNveIRJhfAiEAz+pnFHcgPcE9/3VR77+DhvGgM9XS0Ww6zVmIVFqOwXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7753}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": ["./lib/index.js"]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.16.0_1635551242152_0.7747665909465222", "host": "s3://npm-registry-packages"}}, "7.16.2": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.16.2", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.16.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "2977fca9b212db153c195674e57cfab807733183", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.2.tgz", "fileCount": 5, "integrity": "sha512-h37CvpLSf8gb2lIJ2CgC3t+EjFbi0t8qS7LCS1xcJIlEXE4czlofwaW7W1HA8zpgOCzI9C1nmoqNR1zWkk0pQg==", "signatures": [{"sig": "MEUCIQCdjhGzInYDNEeFgUySGNDYVNAf1p/xsiKcky9bwzLj9AIgfTV2qrqo9tLMR9yklGVy7L0+QL9p9DxO/wg7+3eksyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHEOCRA9TVsSAnZWagAAckIP/jLdlBgYQwOULLHmae89\nx+5M46wjAwv2Ri7rpI20lLtCTpCxPBoWao5y39aqB7PT3LAHVHb5YeHj/2S6\nYdBUSELK4HJvTI+Ob0+oBHmMnF5ZUSD4chCeCvF4Cjo2urFRCSav+LOhrJy1\nXv7HNeRZoK8bzoFQJVXi/+a9chrERFCEb7GIvcNBkX0o3muapJkri4q1/ZnT\ntEUZJ8I8yl2yXVxXartKViGDzpesoLdcd8CSCrfLCWsra9QrENe48lIPBjOm\nb3c3bZeq/2MQQmqPQt956fllXrxtG/pBBXiq5ZKF23epcOnvkJV/bJetJuDh\n+xHS5G0Gb7eWHc84feZygCaKmn37l6HVyJUiXbILt7lmf9wnhd0sPerVRKwe\nGsyC6JX/nyQhhalInhw6FLUBkxTjbgWx0jaEovEst9j3cykRvl8eatk9lD+E\n/WiUW8oCiy3rXmxKqpZ40peumrFuRpceJg+7/cJDa0ppk0CPQgu2G3cntPgF\nJR8Vt6E5gGl170MvkVTaA83DXRbrPxyuLS+QUY2t4G87QwtzHkZyD2RWJi4p\nd1hHGC27LEi2Y7PGn5MB/7qLU9HK9avI8GgtE1fIaOn8u4pZLJyi+HSEENpF\n6ybq0qaizTPSkZHf5K+O4XgTnTeeqgpqGEFR2I8xBEb0y+q6SUy7EAtqcERu\nYM5S\r\n=2qdw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": ["./lib/index.js"]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-function-name": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.16.2_1635803814744_0.7315774730403819", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.16.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "4eda6d6c2a0aa79c70fa7b6da67763dfe2141050", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-anv/DObl7waiGEnC24O9zqL0pSuI9hljihqiDuFHC8d7/bjr/4RLGPWuc8rYOff/QPzbEPSkzG8wGG9aDuhHRg==", "signatures": [{"sig": "MEQCIFarQmkXtsnp3qUnljkm443s5yxNtfDqJIuQMjH2KYnLAiBq8tvpG0byLfgNJYtBGKwI2VeRC0XBPxp4VZ9g9xGBAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0KCRA9TVsSAnZWagAAt1IP/2xKI+n4bWYkiLIQjZfL\nUHKN1CsI1klhlwUO0tS1iotZF9oE0SqjO5X524AqotVxFjY84P11OwB47AAe\niMYEPtn2TrIY7HoHpNjGEcIoa11aJZwnSiBvth+RCGNbiOlnOIX0sJhCMDHW\nDv+e0EhFvgGCFrFjz1CHb270rttOOf/nsnaRg4zg9Lvn1Ay1McACIXQcwN6p\nLrrt4H+zGpyf4F6T3xvwGydRSToMv/k8CQ5yqXHPdMpP66nBFUq1JJISLM0M\nLHDqYS2U/zCZ7THAODqaZkhvLro2DQYQPI6lZ7SJj6Ci2UEhhpRufAhDvOsO\n9nTlW/6phKXiBW0Ny4VVTEQJqnUtsrzHAg1aYPAlZn5R9JGrIx5HtfZgzwIv\nkvVZ1Clg3T079firUhdOO1Ke7Z0r6x+/njMIFFfL2hY0NHyLiRGCe2l5OehQ\n9PUGwOgkLN/2OSJz9AIIw/rz1JIWj7NaEkN4GsAdE2lb1jZd1X+IUeZqrzwd\ngyQJ9Q6wxb6vgTWODxxTbPPClMzVlzNfDFjmc262t+ShfiYfgx/bueTi8NXi\nnn9VPhwQGW1eiE47AWRpCq7ndDz8R11+i+xfMRFa6H56VwS1YVXV8nzPPyY1\nrTCejRlw7iPU1cHr6BHUWac7WZMhNDlmqqJlz22VMdcr0Yi5RIf+cWbPyi1b\nfpfn\r\n=HAGP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.16.7_1640910090354_0.3477863515323356", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.17.12", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "1dca338caaefca368639c9ffb095afbd4d420b1e", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.17.12.tgz", "fileCount": 5, "integrity": "sha512-xCJQXl4EeQ3J9C4yOmpTrtVGmzpm2iSzyxbkZHw7UCnZBftHpF/hpII80uWVyVrc40ytIClHjgWGTG1g/yB+aw==", "signatures": [{"sig": "MEUCIBCIAwYyQChy2gWrHVjc1vZ0AUQc2fQOsV8AhzYX9d88AiEAwV+GtRzKTG+qRuEsCtgoo3StCo8PAKU1ZoKokp/v2go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHohAAjmqRIU0oLYRQNNfvZ1HKi1PLJ7I8wjrflkoeSLuwgg1ClMPi\r\nNjdtVKQ8VbTF84kae+ewlqRkZbepzEjHnDIA2tFx4X/LxwOA0MbOCI2k3Zj6\r\njp7245ADhInQnybUGLzklmMiLJNgUBr6OkFdPGxopcfstsqLqM9fj3JonSqZ\r\n8/5uNYh6pwKtr8KcwClw+dGMV5PokqndB9B/VzJaEDzpL8vfnPmN2fbgEog+\r\nT35Cpbj0GBAxN3hp9gXzr9JJKn8igCxcRhSWXRIag9P6/vYLxuV8kFKBd2eC\r\nbYAVDPNe7w0f6U6loIjAYlmple3amo8Yk7DbeWdTxfyGJDOsn28KSICuRn54\r\n3sx1JkDuYl3eZYJp8jUuuz5C3Jbq5xDpf3dEhALemjRzGKuQwNdjMf/6qoID\r\naG7laPSDJsZgm4vWlB5wbLtNPquKxfwUBxTT7i1Q6P5Um8mJEwJMzgoacnzY\r\ntLvm77Eo7M6LeFaPo2Vxy2Ak9h3yoldPgPEQK8b3UJFbZhV0ernCk3E5pUok\r\n3ff1aINbNPwi2D4M4dKZtpITxMaNbQC4/Hx6EmBvKzxJdgCCDAcWj1tNegZy\r\n6keje0g/jvaOiIpBvrbrB8svWohysY0E2ECL7JxGT0epTKiD4EVpRCE7oUNk\r\nJw4qIoowSjyyxRBSQnm5XKq7D7cUmH3R1IE=\r\n=4keR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/traverse": "^7.17.12", "@babel/helper-function-name": "^7.17.9", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.17.12_1652729537246_0.8271244524758801", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.18.6", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "da5b8f9a580acdfbe53494dba45ea389fb09a4d2", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz", "fileCount": 5, "integrity": "sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==", "signatures": [{"sig": "MEQCIFHkB+lKwF65pkq5DVr6LH83QOuYhWLK3IuR4J2ZI7CoAiA0ZWccelnbQIcdDUH5IGyuwRt4uO6wjsjDuBXla3x0FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugndACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzCQ/9EkQ+cwFFzWTLiN7DYJTNufTr0nTAVoKmDo2q5OHm5AY99cX4\r\nMFOLIMRbrfSUoDppH3P284vNRpgq1UgrmOsfEpvyeV/qOKRqDDnT32qeMFQ3\r\nZgRY1GSK+drmh49Lg69fDcepuiVf5eQd7dBCfh+S+FYjWxEyVG1qbmVKjoQ4\r\nKYxWhvrm6K4qd510vS4OR5uifEBKxzs4JH7sk+v/3xUaG4A+JjNLTHHxYq2f\r\nQkTepDmW6pxr5Lf65jEpKFC8ytj6RartY3WyHwp2oTrMzzw0rieBajR37Y3U\r\numzKBO5il4KMF5yt35toTDBBplrYZ2hgtFDk2kmJXD7HGRTA1a3OQWYKlRBi\r\nl5oeIElVl2L4KQbIybR95xBa+uTMqNngaAiN4ieBlfoec+m65aJhdAAtSxjT\r\nuPkW/rJ1tvkRZ0rd57zGh4tzfBNh2/acvghf3MRL3SMhALxnXPBc0KOrqRVk\r\nzJV8AduCuirywZzlXRum6lYYpAgEGY7CfozTQZaawajRdf8JJEdvxuZu7bOu\r\n4ezbn5sTRN8ct8r1iUXlmWK8KE8w2cgOKzniPSf276wYbZio3OGPIK825OsP\r\nzNzUF47ofrxjmXY3KdKADqGYKfKvX2CQ20tX1skSFg0lvItFXWwHCLvyjWbT\r\n395qjkNplYs3izHoxKuNYHMHTBX3VP4bpLc=\r\n=5o0R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-function-name": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.18.6_1656359389657_0.6903047341992383", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.21.4-esm", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "d20b6c835dfb4f306a2f0eb6d6a3d6d3b1c142c3", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-7S/6sQkmQEUwRZ5lshWyjYTvcSUvnMDjX8ll/qsJdeZSiFr64uEsSUiST76+fzs4Z3KSJik49hTGT6Ly/XFLDA==", "signatures": [{"sig": "MEUCICwDEIJ59jYbezfiGWmjgTscwUjo5GtrCMwVbF2kYzopAiEA8woFzKVlzs8vo/3MkHNXOAU5iN/Z6sbtDfQOOTpytWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpbhAAkm+ayBOxTIdbl3/7lwRGG9/M8ko5k8ysAVOqp389NAqKA8lT\r\nKVHZstz2INWgRTOwC3HekGi8aJpZTbPllnZXsz6zJwaRQTZG3a9SmdWt1QrJ\r\nyY6JLLhz584qFOIk/J+2iAfg2cAXvOpoE39q28v31EhwEF5Ucm4MkhBmvpkE\r\nEGtDoJY+dM80QBsTcapvYG95r3zxCIK1NvKuLE7C8z5m+k+zuv/ceZpBJ/OV\r\nkOfJTzPT2xiZYONERLOIyHycXN/LwCT9lS6BOxk10SgPxxN7/+wstFh9oCGz\r\nB4JfWq+sr3s7N9b06/b52Zb7tgpUBu1PoroJyPzdO64afsXNm+oWECRF9AQ6\r\nDsOd72yN+uwzKwqJlTpSPnPj/792DNlbbQo7JVipgiNB6tdxrokoff4RbIM3\r\nBulFwY9kFxkrAbs//+rVAAwjKiHWx5IdwagjNwpr6osT6uxYAJoOl54G2nMM\r\n9ZrEuIZy9YftyqSuw7jk7DwujhzEgYGv1so0nbm7CoIg+CpRqv8e6kr7y4Cf\r\nleHanyjEnDOl3p5yjYJufu1JutaKmPx8pJEIIB2OQIvefKrPbeq4xVjsWIKR\r\nX4SFyQIO9SYZ0/jmqYfvHpPRx0DuZhZKcQCJjpwojCUoM0I46fOlFdw5q6uG\r\nfQfMTIfXl9+8Y9GvU04Y9iW1abI4CvKxcTY=\r\n=PVGy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-function-name": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.21.4-esm_1680617347277_0.6260381521817817", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.21.4-esm.1", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "7ec80f5c9733b5c7e8d2d814a7124a3eefa6a77a", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-o+Cggy3OXCPzYFz+HcJXzRovC08q2rngk/1b+QwNW0xrS7YXOjRSeORdRAx7Sz20/bFbJz7a7v+BmLx9ApU5dA==", "signatures": [{"sig": "MEUCIBbubLdlgMS2dox1IkeSCgzKRway3DiQqnK76p4GUCLNAiEA1i1L4qTZlqwZwCNRW8o8xnRFqZPhYQad4TflaRGNWgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM/A/9FCT1bfdSetp2WcDBINqGQZi150bUFWiEy33KODFoHgVQUDav\r\n+Q6HNz7BjkeWbrv70LUTemhv6J7TrEzOuQwoEcOfLkoCoCpD/BVdXoh0dicv\r\nrxDvADmRjbJBbnZHfmQ43qnjojWBNfI1PfskKJpUbsjH+FA4lY9HCO1xRNbe\r\nqRH6QdX8I6El7D6h+wH22f6dOwGjlmszm6vpm0LRoERz3b9BhlVK8ZQscv+5\r\nUMyVPXYgCBx9boOewtru7D5wvUUohksG/VZVSUGlcaxDjxsXJiLKTmvBagCi\r\n2mfnvBGBFaXtRMhDA2curGCGGWXHTJw69uV8IGefgsc+2ilgPhoGvHQRiQ0O\r\nNhDjmcHUveqyanUk8SU/nHaeTU9udkDAIuGma2Hzq1rNWMu+fDBMvMbpJP2f\r\ncpDj1f0pt7GUK/WFi/YpVR7EVTEBUxz8tdjDULM28kMr6c/qCqDLv0/bKMr7\r\ngeg7c+20WHDneUIv9rltT9UG9UrcLEvmFsxr+XYYQu6ecjHEB8Wc2uSXCp4y\r\nBhXrcYhMt52U6Sokhsrdfd4TMybF7pznuOs76+Yna2kgtVAvWbj/rp6w8bE8\r\nHzYdyuK+l5ZTzLPPW3HX3V7V0vHjKb7VMT2fKt7brE4wFOCYRW+zw6RX+uhD\r\n8ABXKesWAOU7BnQg/PmOGkCZ8/ZKUUGJcIY=\r\n=jtIm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-function-name": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.21.4-esm.1_1680618059979_0.4323120605314126", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.21.4-esm.2", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "7875b25e13f8d488e164ff86bac8234e88d6b23b", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-NWEiFI5+DQmzVcetrptStduOiuQK8PpoL8CjH33nAxMeVgtG8LjtWhEZinM/Ok8dAuCRiM2A+LR7PES7AqUPEw==", "signatures": [{"sig": "MEUCIQDz+tyCJUk4Wf2K6krjaVEs6SgL01kEPgSUhP3qfR+l+QIgdFeEMMqkzhfAC8VjDs8qmqwbLfu6zPPop+trK21NGL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6fA//b7EZ3ZX9Okkyn3PZcd6LtRIUagzcj+HNvepyiSIrsaMr1tQu\r\n3yz34mVXvUb9X1/Z71MUJLkO5k5vxG48rYkyWaGF0JSlmH7hlFBAVCtjqJfB\r\nxFtIp3VlJu+hJy/5uDUtWJFvXsb8G2KGe+9KsAqdhXaN3U6A6HobPIA17et2\r\n9HR1pqeBbIKeIS7Tc4jPnic2LZdyK9aSv0NVE6iZt/xJpIoLXjqpFmsG3MdG\r\nnzSGeeRaCW9W5ta8vi+Tltfs63IwTfRvw3ENAulRtATxvVSI6nBVvqZ2Q5Wc\r\nuUoMiv7O31kljWI5YgFbT2gDlWayMoezVnoroCAfgdGLrWjvTVy051xR9cOI\r\nIhNZMQvFBAHw9wRC+fqGRBndXeEeTG66lP5TKTDvyE78NJMc4SWT8XxGAchs\r\nLtoVYiATmtZJNRQ/+2IxWzGJTOnohVCsnRxnGbSgPISYyshn7QURHZLPd2SI\r\norz8mgpH7ieGHQbLvD4OGUmJns4C+owKPjOeLZhZcxgH8ceXvhMgN1b9/Mfv\r\nrlFMVlloAR/ZIJCXxfPR1pF4u0sR+rlTlfH0XmCH37c0pGPoA+5fHb/zPGln\r\nv0OON00Emm4mgucH5WpFNFeYXjIUzf3tTsyiz/iUmq1Q83NhbDDJcdqOD775\r\n8Bl0dMEcqHvPSuWoePFf36gwMzlyCCoX910=\r\n=sN8K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-function-name": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.21.4-esm.2_1680619129085_0.9905274805203079", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.21.4-esm.3", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "0049b92784db8b38e7f74a46c02102a67b614d31", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-7YWrW1DTDWN27vYtk3J/ErNgavkiIzFtWOfZrGYz2J9S4FZ1K8H5XkdPMMJot8yeokU7ZbLdqX/F1ZtYhsO8Zw==", "signatures": [{"sig": "MEYCIQCzu4rPO8R63nnl7SlnoQXWTZo0oxzwm4i7raROpQyJkAIhAN8M4DHv2RtY0TKRAum+l8gRKHtD6GgaTj2ohTRi9tgb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDp5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmph1w//W1pyV+9lwcBMQq5oLV4zm3E9DPsRuwTPjeiA91o4JWRXfwiI\r\n5JFTCQIIPuU5S8guai0J5mw35UiegvqeRxbbB//XcUcx4KrQ7pwWL2qnayfm\r\n0PHICwpUa+fVPnvggZXzRGSDkqimllnaJ7AZx56no+sGx3qNSB4IWGBae/bh\r\nRM53187RTsXmaj6W20shbiUYTUbgadYwQfz7Pz3ZzhzzzLkq5xV8N93b7TXe\r\nXaweA3VTLo393FDnnbRqduBXLIRKDozj/NxRqmqoqIU0sjWKumpbx9SJIASX\r\navy00fec4zonUChkPbt7ouKyF0R1PKbdoRhzM1cFkb79LimPnFNo3n4MWAUk\r\nc0Po9ukNdBnXkO+3+DL4vvbHi4dgKhlyrteWF2UQBDUX/TA5a0Mc2/6y358w\r\n/9LrZLRZOwuwgSq38Ifry3y/5u/0X7zK4quwZgRTYHWdnTd2nnWLL+DCQbCa\r\nrtk38LEHS2AewLBQY2NRgH2sJhe/44pB7IBv0uWvYiDY53GkY0QwPcXAhXKj\r\nBaNJdV59wVT/6aLEJfaYrRtKGIwkUJUlAiOMR2uabGhuU3YhwDzZnoFnGu9C\r\nbQpuqGpy2MBCL/guLgumiTUYFaIvMnNjfO/Olb4UNGgzZl8PzMlqXzysmgVL\r\npUXbC7qJphCD4qCzpjc1B0Oib2Yxwu830YE=\r\n=uVy2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-function-name": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.21.4-esm.3_1680620153767_0.6686885725793001", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.21.4-esm.4", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "fdd58bbbcf37ea19d7ffd56e9636e17d2d7dbba2", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-7Vrb66lUUJ8s7Jx6NmjhUdX47Ul22IdK8B3wj6XOO/yOg916bn5t5uGai8+1mjqhnGjBBVoIeintqwhqu1lOdQ==", "signatures": [{"sig": "MEUCIBxaqS4w22MVsUUw7D7/BNVi9KJG2/P7q/nlyHwkD46hAiEAkOO7GjQLIZcg4GHWkd8JHnI57Mmu1iYGEUMQakRx17M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNig/9E7OkO/ffwrMc2EGb5HUWv4QzpKgd9zixm66Hkk9z2P62iizs\r\na8cgHRs1em7ltbZxMXfF4hWq9sZPdBaAisnmjEb2frVz/7jkFb+KyaNUAJjG\r\n87xDbsjveIS59NQ2unb+H2qHJVJ5Xy9rSf/n56+C4Z7RFJgWFmtXrWYv7cyG\r\nLJXlTm56x120PNlCkwcYYYdX4rdtW0mkLii+3WhdOuWm8EcW5IX+gyaVF6Zo\r\nj9mgR0b2Hx/uPFdeyEeixoRLMLf1kfsE6nG6/PTAoSaPKIZtWTOpICwgOTW7\r\nlQwxDIWKgzGKt2C3kfypsiJxyzqcb1ccW30CN6pGYi2/gBBaywpsldRerBJH\r\nuL8I74EroJEBhSubF+MeaKS/7ZKPpSDwOhMzaYkbnFQtIiaN6aRoi9xAsM93\r\nO5XoYKLdVyQwr+gqyXz/n9JWp/zxaXQi9kOGKVvtfG4svw3oPGqlrd5B5siB\r\nVccuz3/5+nS1V/06Pws2ueobuQ66RNPHJaARD96pfhm6p9CYxRYzzg+JUQ+w\r\nPZ4KdcQXuBDs2CfbVIzbUJ00aEEexKemERhiXVZsU+eUBadfscPHYzGOepwf\r\nYnNR+pwUlnf2nwQhv4OqDP4ZnMzcRAA1t3QMuoo6D+d6sXBD+D4pOloyEc0H\r\nzFSlFy30c6pJCtjBiYLDngNGG0NFx1h/xH0=\r\n=88J+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-function-name": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.21.4-esm.4_1680621187671_0.8975444334092102", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.22.5", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "87245a21cd69a73b0b81bcda98d443d6df08f05e", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.22.5.tgz", "fileCount": 7, "integrity": "sha512-NP1M5Rf+u2Gw9qfSO4ihjcTGW5zXTi36ITLd4/EoAcEhIZ0yjMqmftDNl3QC19CX7olhrjpyU454g/2W7X0jvQ==", "signatures": [{"sig": "MEUCIQCFCGfoKl2WK6zQPtXWnQYQmI1L0p5WhqJzJUW9oqPtWwIgPM3R4fcpx9+H/vgg9d4hvNF1RtIA6dI/xhVyG1cx5Sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10512}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.22.5_1686248466923_0.6106119276896789", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "47ba6fde85fb44d2b2786d416c05d2bbefed0b14", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-u094JAqYez+CceI5iS/OtmPma3Z7KiFzK+ZjrVO/LeZ4/IPy+dm0cVhobyq180Xhe8hLDfJbjulbqgEDMQPUEA==", "signatures": [{"sig": "MEQCIHghs79GVSlXMuNxIJ/olHjfPqxAZA4gunf3AvsY2jYOAiAomjt8P7iN+Mq43prXU/kCTI3IB7Zgc/P3yF8bEUERtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10355}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-function-name": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.0_1689861580249_0.845760154884216", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "4a7d8266983882fd40e0272ecad41ff88904eb49", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-ChCkA1UNNHkeGiW9veEKrhYrMyS1fVkrHNAByB/Dha6ilBAFDsr5b9HjKS75vTYyl27PPt1oW9ltDucz6cRkfw==", "signatures": [{"sig": "MEQCICKIscNmv7uJmOLbnomjUy+Xs3LpbI8pV6STQjiBdsYOAiBNDy8RCNeemx96M6i9FJcoksdNmTuKzq5Sa+B6VAihhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10355}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-function-name": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.1_1690221071387_0.6869499471278324", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "0fa557b633b68e117b306d710d4d892613b346d2", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-Gf1KPVtOADL1gRtfnEKppMQ0AGL8KQTayr0opBiyu5YS7KxeDSh7jPAjVys6FZQ/zGKwPs42w046BbUgB0xe/w==", "signatures": [{"sig": "MEQCIBy5b4X6e3+wJ7OItZHd5t+PorIMyeF1sLr9aZmZoYqpAiBHW3DjxqrzGVzqBPu8DqmoNxKZnX7UfXzaJBTfGC1K5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10355}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-function-name": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.2_1691594077335_0.8684952507844013", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.22.15", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "02dc8a03f613ed5fdc29fb2f728397c78146c962", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.22.15.tgz", "fileCount": 5, "integrity": "sha512-FB9iYlz7rURmRJyXRKEnalYPPdn87H5no108cyuQQyMwlpJ2SJtpIUBI27kdTin956pz+LPypkPVPUTlxOmrsg==", "signatures": [{"sig": "MEYCIQDz8Jg/Yj8/xYdKynjZKkZNgj8RdgEzD85ucJ9U2q9X0AIhAL7DoM/4bm4mN0AQBIy7f+tBHgG0fUOCHEobBBnCglU7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7725}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15", "@babel/helper-function-name": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.22.15_1693830303404_0.8253540557743264", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "148056b74a6e5d1fc357b00b67cac213051aa01b", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-XuTY/WeoyBsbJTM9U3T/ei3D5JZYv9cnJ7+ncJsINHuMCwxmacJnWhPsKkZZRJ3LfvINnmbVu+TE4uhr0fRVOQ==", "signatures": [{"sig": "MEUCIGcsojy/+IAYL7RzioHukL0dclhrg266HqwVKgHIfwopAiEA4YfE/ngj+mi0old2R4jfNhP6U8/EVcVvqu6ioIX5VKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-function-name": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.3_1695740192457_0.08162514459631476", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "9f0bf0158f30413ec1c81d7e02c0e3180a181f4c", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-MPgkpRXNcf8BCqAofC5ZFP1CzzvXaNYAjSrIjLxYsmEni9LikVdGQtN/5KT2k/pcPZ4ZRN9yimPzr9LqG8N6UQ==", "signatures": [{"sig": "MEUCIGziDR+ZBVMAIMRmUmzQIUeD9OpX1x3jhtwjnclsKQnZAiEA7YpaT17Mw8f1teYehzAZbUoLsnm0adhzfdFRkZVWvCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-function-name": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.4_1697076358887_0.46175340106442575", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.23.3", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "5cd1c87ba9380d0afb78469292c954fee5d2411a", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-iRkKcCqb7iGnq9+3G6rZ+Ciz5VywC4XNRHe57lKM+jOeYAoR0lVqdeeDRfh0tQcTfw/+vBhHn926FmQhLtlFLQ==", "signatures": [{"sig": "MEUCIAw+kIBB2VlZsAUW/+pLRyPaj26Gq3zQhaAM9PnKrJeoAiEAn3aVSGfZD0NYi4APsJCQ6mSTPRvt/7oWiXk04f7VhAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7826}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-function-name": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.23.3_1699513417081_0.5318244831123276", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "baa77d7380b2ee917d7660b7ffd0aa7eaa4221c3", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-0aZlPyZ8qEELi3yabkoCz4L2MQwsQ6ZZGm46rul1fDjNySTvDx7A+QPqUJKC4JFQuOcIgVzTB2tV85kbzcqnBg==", "signatures": [{"sig": "MEQCIDHM5RlacaPEELYGOSj3pmElqY7TPMzQo/bfDzDl8xAbAiBZ2R3Q/O393z/vA6AREKmS/uvdH+/De8U7Ex4O+1Ur3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7793}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-function-name": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.5_1702307901080_0.9305517154862526", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "4c3c6c2cb2a7fb76a51db8dbbeb711208e5c9d33", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-xg1W8333heJ8ETQrmCQxO5MAVdMZOTm0D/rml4NC8i/d6hFFsAAdK2NxmnwLrfSbQXNiklbB388CyvcxNvtksg==", "signatures": [{"sig": "MEUCIQD7BaXoQz3KmBJJ6CGPllaQKK8F7zcVDlTRPZ2l43bWQAIgNZ+3o1LveeRZiFfsMfE6X9oh9OM0my1xqcsD3aXHQyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7793}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-function-name": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.6_1706285626163_0.9911621759139184", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "452634fa1fab2abaf8ade361de20b118c866cb9d", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-iAEIqzhUhxXCXhFR2byDwJG6h3gsGA/+3Jx/jVqbUSupY9i2C6s6oqdBGbOmdbKS7dYCpmYX9/fkbSWxP1qdaQ==", "signatures": [{"sig": "MEYCIQD5jz5ZTtHWYUuPHHVCC4DnqFgnrd+l/LxUHVTzkBp52wIhANoeyBCOYemFt5MlmZi7sVWT0nMxSXwlkQOlxv2Gpxst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7793}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-function-name": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.7_1709129068327_0.9431018747985853", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.24.1", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "b645d9ba8c2bc5b7af50f0fe949f9edbeb07c8cf", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-y4HqEnkelJIOQGd+3g1bTeKsA5c6qM7eOn7VggGVbBc0y8MLSKHacwcIE2PplNlQSj0PqS9rrXL/nkPVK+kUNg==", "signatures": [{"sig": "MEUCIQD6qxLoxjPoa5mkg452Q9bUxLb+xOVrPJrzQKA086gnngIgbY01bVNjl7Ixs+zp05Y1BI+EgYD9vfgWPRjsSPw0BtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7741}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.24.1_1710841680590_0.14640075357290994", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "ccdbe48c9dbe0a41c927068d9f4ec55e8190e5f8", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Oc/MUqLahCVRnHOmaP1X2GkBf/yWsTbkxeTzhwDZmyn0R6TNcxwWDJCyzoSer6FXVK4/+STKdP4/Jb2TS4ouOA==", "signatures": [{"sig": "MEQCIGF3u1HtFW+Zvyj6000/b7D9M5aURmuK9CMJdSgEvnIrAiAq5ii/2IGcVhpkIOhb052IFKXc2vPvCLVr3El6XcEQBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7707}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-function-name": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.8_1712236775372_0.5592551161193138", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.24.6", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "f9f5ae4d6fb72f5950262cb6f0b2482c3bc684ef", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-iVuhb6poq5ikqRq2XWU6OQ+R5o9wF+r/or9CeUyovgptz0UlnK4/seOQ1Istu/XybYjAhQv1FRSSfHHufIku5Q==", "signatures": [{"sig": "MEUCIHjOqcdaJvwkklQ5DEyffGjeE9WzJPQHeOpFu0Lv5O8WAiEA84bzu5qc8IxX9vFyasDDZVM4CZ4ChN5SRfElw19IBb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74012}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-function-name": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.24.6_1716553456685_0.5355932875562754", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "235ef8c88491f1becb68f8928475933d356ef588", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-uRiX6EQPqzkYjhT6AGXOu9JMTvZBHa3q5991G4s4NGWDRPqeIVqGpbnIF+GKHi9sf7T6MMmg4oHs5aAqBzSiVA==", "signatures": [{"sig": "MEUCIQDEnej8jTu1PCcJ8wdsw4DyxJKi/EZJcVgg/Ml+/YqQ3QIgWvGHMqcPXzT4DG9lkUF0fq9KVP8C+wYt8N0K/LiMA4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74167}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-function-name": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.9_1717423433953_0.5014003700634737", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "3d201c0970f83307ba04c2056357f63baed1b816", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-7clGp4f0G98Lx4qrC2GE6dDPjlfuhCdHjZddD0RK/iWaKNWNK0hFrDRFSsRc+M87v7CIdu7htX/qcbKvQcsvwQ==", "signatures": [{"sig": "MEUCIFMl9iJGtSq3a6atv+xrc28NtcbjPeso+gPQTCItC2VKAiEAjTvm0hhpCMHA0Yq9y/kLvs+p6FHIWawYOOOuUrxUC90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74176}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-function-name": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.10_1717499985798_0.41365625654952876", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.24.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "468096ca44bbcbe8fcc570574e12eb1950e18107", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-unaQgZ/iRu/By6tsjMZzpeBZjChYfLYry6HrEXPoz3KmfF0sVBQ1l8zKMQ4xRGLWVsjuvB8nQfjNP/DcfEOCsg==", "signatures": [{"sig": "MEYCIQCjo8VcehAwI8mwTDTN1xDHqj6peCiPvZ67J+poRtqKgAIhAKeW0x9J2xJHfpaefvD+8XCeBZ2K4G8EZLiB192LEHtl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73947}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-function-name": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.24.7_1717593303067_0.1596330722843904", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "6a42907a82368cc72a585b178935fc735f3ca76d", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-t0oPucJnpz9HAKaGt8pjIMpOzI/cHXLP1KzGwD01iqF+ycddSDJgFi4zL2RIT+GPjBv0qI7J95ZFVPS1FTL6NA==", "signatures": [{"sig": "MEUCICwtSKUzgWwiLEWkPxNIsYKcLw02V3JM1ikQWVrLq8xzAiEA6ZRumj7uOD+XaEKyhzqjwqHUwSlBy4IktdT+wbLP6lA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74067}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-function-name": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.11_1717751715809_0.5313150317492035", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.25.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "749bde80356b295390954643de7635e0dffabe73", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-lXwdNZtTmeVOOFtwM/WDe7yg1PL8sYhRk/XH0FzbR2HDQ0xC+EnQ/JHeoMYSavtU115tnUk0q9CDyq8si+LMAA==", "signatures": [{"sig": "MEYCIQCk/hJ9OtaUUtTjMxOdfIGrYmw5G6PoNLKwWX0beA89uAIhAORtCwqJUGHHr3ap+ZqLFNhXScO7Nq0A18YE7xalD0Sk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70566}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/traverse": "^7.25.0", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.25.0_1722013160857_0.7927668688986929", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "e0714fa6e0b050a37755fff6726bdbc41e6ba974", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-07wYSadh331pq+NgLqVopXP6/D4xPnSXImlIh32q9nYTqfCM57fxQ71UKI++APjgBD/AeEoREQXKac1RWHqQSg==", "signatures": [{"sig": "MEQCIHXVnMkb2q3vFPvkArqTBKWHgK4IwlYolQQ6UUoV/cXGAiB2Or0QmKdzSNKJvhz7gHUPqmjIeLTBdcq0g0V6Z0DPdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70796}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.12_1722015191738_0.09140468259503942", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.25.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "c5f755e911dfac7ef6957300c0f9c4a8c18c06f4", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-wxyWg2RYaSUYgmd9MR0FyRGyeOMQE/Uzr1wzd/g5cf5bwi9A4v6HFdDm7y1MgDtod/fLOSTZY6jDgV0xU9d5bA==", "signatures": [{"sig": "MEQCIFTjl2db+2wh0imuHlPrLhb01hDph57v7FzOP8LfDkSfAiBVQJyEnEE9JmV4moPmO+uViw0P5IVR+36gaZy+NMl2LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78428}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.25.7_1727882070316_0.41669556227335547", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.25.9", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "e8dc26fcd616e6c5bf2bd0d5a2c151d4f92a9137", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-2qUwwfAFpJLZqxd02YW9btUCZHl+RFvdDkNfZwaIJrvB8Tesjsk8pEQkTvGwZXLqXUx/2oyY3ySRhm6HOXuCug==", "signatures": [{"sig": "MEUCIQCot88qlSRlljkVG2uvFjKd6FCxHOwgGT+oRxBaEu61vAIgB16fP8dbdE+aHIei0zluDg4wA0AungWf4xhALjtZX9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7706}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.25.9_1729610444600_0.11835950771077397", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "a625ebe766f1b491046b3d563c532921cd3863ee", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-xyIdfQ1u/ES4FOU0fvlH9AecIVrr/Ofw+UOKWCft8GHY3nZVV7QhjG3U9TcwPXiwxY7cbKT8z18qBsL9TDZ2jg==", "signatures": [{"sig": "MEUCIQCr6BpZHa6alEiE6wFdPfB5JW8wdrbN/pgTKB399kaP0gIgZQpz0PSTdl+veLVZyP3b1Tyihk3wWvVQyrLSuMTj6KM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7944}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.13_1729864429632_0.5535457124764998", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "089574acec1f7d83980e4dcb604b6c67974a86b2", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-H6CfP0vq5Pec7SFFpUMB8NSCJmK3uVxVmxPtP7wO9UjCN3uCFgr8mswXKAlaZRHZCk7+5RiNohSdLrC+a6Z1JA==", "signatures": [{"sig": "MEUCIE5IENnP/b3yLco1JH9IlwEZ3aGRGRVvJ5VSeBk9RJ+2AiEAkqe/O1lQHvYDUuDAnhUX3qROj4sh8aSKVaSsfWZr0pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7944}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.14_1733504021917_0.35562549137854016", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "430976c6321b85a8653d93af1b307d2e47f58c28", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-LTnxaEXrinUDEsl0miOdG8ufbhtTCf2dqluhH8NHfz/WRRLIW9M8UJj80bD1AwBP0M7vWSHbkYAANb8Y2+W45g==", "signatures": [{"sig": "MEQCIHDBd2cAP5Gg6wRfivXl4GJ/rqEm+bu/S1qCtMkWRH/EAiAycS3Mu2rACxgRU7L5YdknQH+tUVuHl+YpCXuC6qjLfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7944}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.15_1736529845205_0.010503092621713561", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "43a5cd3aef7b0a4c36d043ba3e979541c90c756e", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-bNoxskaLxSa8+HJaHJMSeN99DudzMdI7sRyHcchUNfcpfUGkuz7IhG4MmYjDqPz5QQPG8xFrL3SSlTo+8HroFw==", "signatures": [{"sig": "MEQCID4n8N0NenDsmCkjrYSXFkZxy6AO5Dkb1vvzc8tdu421AiAoKSI74mLCJ9u/H6yX7q75S2se0qIoM6QZ7HsEOSJhBA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7944}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.16_1739534322940_0.4175486860290256", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "bdddd3a6f3c792b6b677c33bbf1fd8e9e50b2852", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-VI7/vVivsMcYC7C42Zg63FpU/R0tKCI2U9PjrZnaiWWe4A4PQmjOkG6REMHTAcMgUnohQM6u9tAD2IiNsBogIw==", "signatures": [{"sig": "MEUCIFXsVy1e6vFMTPw24zEKTqMiV6BH+USOF09OQQSLlZ73AiEAyRf9xK996GAVbhh/321rroj5c70GKU14cPkhzGzQKfU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7944}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-alpha.17_1741717473869_0.7501449683507009", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "7.27.1", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "beb623bd573b8b6f3047bd04c32506adc3e58a72", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-g4L7OYun04N1WyqMNjldFwlfPCLVkgB54A/YCXICZYBsvJJE3kByKv9c9+R/nAfmIfjl2rKYLNyMHboYbZaWaA==", "signatures": [{"sig": "MEYCIQCHdpew3EDB2lfSSD2jrPfmct0zQ4ZHxrxZ/Ifb1s9v0gIhAKZwZB+db7AKv1Syw2lbREvJeXLAdiKWqJTw02GExLYU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7706}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_7.27.1_1746025713155_0.6004428562742681", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "dist": {"shasum": "70a1adc4361b6fed9cd05b873a569da454a3e0fb", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-GZ/E53dgj9sCSWa/fZAfR69kDah2Lkank7bMRVT4Y4xbgutYd45A9nSlhFCuYwiBNV+D8sx2cBC0dUD8EBIQBQ==", "signatures": [{"sig": "MEUCIBGEynUsGgk6L6j78nfNTJHVOPgEGVVzMaMp30s+VEJrAiEA7OfAq1KA9NzhqETklHYy24iVAN+anFN48n/40fyZy64=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7918}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-beta.0_1748620246461_0.3086799245724201", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "version": "8.0.0-beta.1", "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "keywords": ["babel-plugin", "bugfix"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@8.0.0-beta.1", "dist": {"shasum": "11f5cf193636cff53bc1cb11c57a8b32da82a8fc", "integrity": "sha512-9QX9BAW5mMgX8a5rhOtjBnkLXvOc/h2lRvMx/eRci1hf6mVBKz61yUet6OnC+IG8NtbFCDOQGW8Nbcppbiz88g==", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 7918, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDruvdA6AHZ9WIRCBT/zw9i69a06YtW+qZmV6Ful+27lAIhAKo1g2rySOQKa1bNnh35JSg1MoVqwQ9UKbemDYd8h5h3"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-bugfix-safari-id-destructuring-collision-in-function-expression_8.0.0-beta.1_1751447040234_0.7477258928267196"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-10-29T23:47:22.090Z", "modified": "2025-07-02T09:04:00.588Z", "7.16.0": "2021-10-29T23:47:22.269Z", "7.16.2": "2021-11-01T21:56:54.957Z", "7.16.7": "2021-12-31T00:21:30.491Z", "7.17.12": "2022-05-16T19:32:17.436Z", "7.18.6": "2022-06-27T19:49:49.880Z", "7.21.4-esm": "2023-04-04T14:09:07.415Z", "7.21.4-esm.1": "2023-04-04T14:21:00.203Z", "7.21.4-esm.2": "2023-04-04T14:38:49.316Z", "7.21.4-esm.3": "2023-04-04T14:55:53.898Z", "7.21.4-esm.4": "2023-04-04T15:13:07.800Z", "7.22.5": "2023-06-08T18:21:07.058Z", "8.0.0-alpha.0": "2023-07-20T13:59:40.408Z", "8.0.0-alpha.1": "2023-07-24T17:51:11.531Z", "8.0.0-alpha.2": "2023-08-09T15:14:37.471Z", "7.22.15": "2023-09-04T12:25:03.627Z", "8.0.0-alpha.3": "2023-09-26T14:56:32.621Z", "8.0.0-alpha.4": "2023-10-12T02:05:59.120Z", "7.23.3": "2023-11-09T07:03:37.305Z", "8.0.0-alpha.5": "2023-12-11T15:18:21.275Z", "8.0.0-alpha.6": "2024-01-26T16:13:46.312Z", "8.0.0-alpha.7": "2024-02-28T14:04:28.467Z", "7.24.1": "2024-03-19T09:48:00.713Z", "8.0.0-alpha.8": "2024-04-04T13:19:35.610Z", "7.24.6": "2024-05-24T12:24:16.860Z", "8.0.0-alpha.9": "2024-06-03T14:03:54.117Z", "8.0.0-alpha.10": "2024-06-04T11:19:45.973Z", "7.24.7": "2024-06-05T13:15:03.422Z", "8.0.0-alpha.11": "2024-06-07T09:15:15.962Z", "7.25.0": "2024-07-26T16:59:21.071Z", "8.0.0-alpha.12": "2024-07-26T17:33:11.946Z", "7.25.7": "2024-10-02T15:14:30.549Z", "7.25.9": "2024-10-22T15:20:44.861Z", "8.0.0-alpha.13": "2024-10-25T13:53:49.802Z", "8.0.0-alpha.14": "2024-12-06T16:53:42.133Z", "8.0.0-alpha.15": "2025-01-10T17:24:05.405Z", "8.0.0-alpha.16": "2025-02-14T11:58:43.107Z", "8.0.0-alpha.17": "2025-03-11T18:24:34.073Z", "7.27.1": "2025-04-30T15:08:33.339Z", "8.0.0-beta.0": "2025-05-30T15:50:46.648Z", "8.0.0-beta.1": "2025-07-02T09:04:00.387Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "keywords": ["babel-plugin", "bugfix"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}