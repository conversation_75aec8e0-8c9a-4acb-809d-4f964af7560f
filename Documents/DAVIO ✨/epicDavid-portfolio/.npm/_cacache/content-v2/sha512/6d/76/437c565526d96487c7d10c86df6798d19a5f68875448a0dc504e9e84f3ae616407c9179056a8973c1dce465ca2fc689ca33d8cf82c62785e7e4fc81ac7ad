{"_id": "es-module-lexer", "_rev": "73-fd8e6d9a84a0741fc6b2ce7f80438e87", "name": "es-module-lexer", "dist-tags": {"latest": "1.7.0"}, "versions": {"0.1.0": {"name": "es-module-lexer", "version": "0.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "dist": {"shasum": "9f7724d27095c0122546829da3a4981b0025d8e1", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.1.0.tgz", "fileCount": 12, "integrity": "sha512-6DHItZWK5C+5RSJ67Q1QkqlXxCWAE5tlYY2xVnd4jvwwtkOEtUZW+XVo6f2KWqOSXZtFbyV8v0mD4vD/p/NPBw==", "signatures": [{"sig": "MEYCIQDmWnZusO6DN+U7eam21x4ekwlPSSOHnpN30acJQB3AvwIhAPFl5G3ZSLV871kvZhSwtJlafsZ9vJcTgY4t1ksIEO40", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2196079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdS5t3CRA9TVsSAnZWagAAE/oQAJ3chb8JmGTqGNRPg7p2\nWul8H0thqSCpVhIcKzBk7qjRJU1ylPolAbUQEKUmJmhg70hDA9kpbcURHolN\n4F2qerRA4lpVGL3SzsiFxrdC/TH7A6XlkGQ0OFA+GXG1J/SuK8KrFThryx/S\nPmIT5+Ar+aLwWxlZah7/bDQJkdT0lOecWsF+AUj4Fkvn1EItu+m5Dp7My+AQ\nhurNRzgF2/w/OLsiaOkcdBQ0Bhg0JnKNYIadK0j0xNARGS+hydBCFiYOjHXL\n/rzWtsHx+D0gJse4lIn3y121OICIF/cemwgzbS+Y3WrqIeDLHX5YNbQYdh/O\nEmT70FOa4sZ6o1vXTO5KTKXZ1HwZxj9ODBPSS19G+TUii9KO/DarAZC24MXS\ncOXk1UkJvk8JeZDThCgTzPUlzwjZ6DDwqQukR/ViyI5fTzGlGLmiHOqF843F\n4vkVOfvcXUvJBGTn5rp+xgv4nlRuEduy8kea8x/ID22V7gXrUA80JjG5vuoQ\nsVOMN1M9S4sMTW1xiD54y8nI3BfFVIe+oIqBYfj43iw5VBCqIZ1R3PgMtmj8\nyhmejLO5r7NofMIb/570TJwmhl+ezlqhlSS7m2G1QnjBLQLt3I9Wri8n8Shz\njO73DIdB42VZHR3L/rOGu2n6z5mXnDHG4kvvA2F6Lh7v0sPfVrLTL7wGnMn0\nqKeb\r\n=yfp1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "gitHead": "f1c7b7dbfb2e4a471e3cf0ee26b4948589fe4e56", "scripts": {"test": "mocha -r esm -u tdd test/unit.js", "bench": "node -r esm bench"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "_npmVersion": "6.9.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "pretty-ms": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.1.0_1565236086967_0.2829151334540938", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "es-module-lexer", "version": "0.1.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "dist": {"shasum": "aae26c0545634ad2d4b427f44b114289c461719d", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.1.1.tgz", "fileCount": 4, "integrity": "sha512-xsn778bEYz1oIHFUtMzTNx3YidHKS11f9kaW7hcwKlpJKWvwKV74sTGzpiCW/1840HXuXUU4bW5XdoO4XM4+qw==", "signatures": [{"sig": "MEQCIA9++OgR2YZ4Crto/5oYRClkc6ZkTUtGfFCwvXGVDJBBAiAsRwLacIDtJ+LsFu1AbTWuf5bc9R9guC7v77dqdRsSAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdS5udCRA9TVsSAnZWagAAB10P/iKbIqb9IDdYlGbuhQGh\nF5qvvKb993x6km78Y0JWm4XMWqtA/xXoTKllHUFHcL1QOaEtWXf5pNv12/oH\ndjZOQLh7BEDtZrKr3Pl0STvWBu0DZm6TclMxjZV1jkm1izIqYrojisqcdXD0\nU3/qgmlkEU6kfhsrvXApEz6M8XmJZYJnpuaaUKHvJsZ9qwZIFy97AQv8EXBC\nTf2E+ZEz0h+2hAVgjYA6GfZRdhKJxl9/ZK9RAJED+ojaff6tit0d2Am7PBU/\nx+7n8PRpPe5Do9eWlLHakWfGx5pSLHxv5cvhWmFJZDJLjdQ/CX28wJ9fpXQT\nZJdpkeGhheiS/d9fUv91ZBlHbpFEbXThJzkF1GaDtTlCe/OG9apLF7IcE2ua\nifCFofwD9EysKnAk+WyWmY0IzZBj0mXKMWZAimJIYL+jTbR0OX4+DepRjW6z\nsc1jaR99HEo5jWBTlEX8y8xyvhkj8rUJ+fECqGKXccyiIrzVj/BNuzYf1net\npzadR1MICoX2sPeZFmEFnwZ0kzAg08BHI82ak45PUKNlP2kI6uW9wl2yJfub\nMlga4l/0ZkesDnzz5UVHIFyeMN6/Hm3kNbqecFoz3+kOPsOAxC1pL4+6ZbDn\nuXf7+NklCEWiztqvnY2brvYB1pJchqigvHsX0gjNq44q1EXkSNHi+HcwCpsI\n9PTk\r\n=OqCG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "gitHead": "0d869afc58b68189f1540ec86018c155441fb707", "scripts": {"test": "mocha -r esm -u tdd test/unit.js", "bench": "node -r esm bench"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "_npmVersion": "6.9.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "pretty-ms": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.1.1_1565236124437_0.20343311134851616", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "es-module-lexer", "version": "0.1.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "dist": {"shasum": "8d004b2aefa9a2961fe9e301d3201c138a3af974", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.1.2.tgz", "fileCount": 4, "integrity": "sha512-5tfcj9eXv2druf+Hs1xKPFNhN0aWIBvFoeoak1DZ3Kxz/RIdRwYdhbnVoABZ6Z/sLplhEfy/jdysu9lpcnLVVw==", "signatures": [{"sig": "MEUCIEpPzIAPa7x2zf2ttBJeAA3gqToFDpngUlXzYMDz5iTJAiEA5cvoq8vKxzaxh9QewwZKOnIQNC91FFKEpn+AeO+VfFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdS5wDCRA9TVsSAnZWagAABAEP/iT94cZOnr2aiWBpgS++\njnjT1/RSUDJPJu2ERWm7v5EIbnBGxLLFtAu81oPwWtLPdAjVz59PvVBbF1f6\nH1mkM1l8O6NMoCL8gqkokpjBhtooidYUpHGgJPbeTXXtLx7l3j/d0orErhk9\nyrFvo3Eg8+2M3fC491d5R+bhx9r8P3JA8Q4mEeLyuZ71yXOE6kVYhwK+6vm3\ncMXAabtI/NnXKhGgrex+NiAPowYJzbgP3tc+X5ZtyqqPkfrXYqG89ltSwueG\n9Ms8lMtU0zYmemyfB4ZYNz+YeBuPpo5LKMzOJxpbR9+dx25TTPPOVddRoP3e\n2K+CDRQsLl09tjezp7G8JSwWRuqMlwQCpto4QmvRitFefwaeWlxsrUBSYWWR\nqRdN7wyfwbB24E9j/8fuyzjpPZP1AXHGm3lYDHnF83HVB4E+j8xzw6kVt448\nMiOVoykcGcJhfwocSqEoayFSrJvPtiq6BiJM4U+B4jtqLki0T0XEC/ecr3FG\nH122C3gM3kkaDrdxSRTwW0cIA2jMEMU4Gmn69mWLgLUqu2HabAt93Nw5pqWf\nxFFkkzbnuLbcDeAt5ls+Obn3UDoGBq3JFjpGd7ya9AmGcMaG0fyKC+eTjK6B\nx6nA4fm8zCX9usj0kF+tliopbCYxsxJNjFR5pjlPKOq8euGLxVhOEjyKfuAD\n9fyJ\r\n=Yedm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "type": "module", "gitHead": "d85f028825a0c31e7dd8c65b8da0301aedcc03a9", "scripts": {"test": "mocha -r esm -u tdd test/unit.js", "bench": "node -r esm bench"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "_npmVersion": "6.9.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "pretty-ms": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.1.2_1565236225678_0.1286601261741469", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "es-module-lexer", "version": "0.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "dist": {"shasum": "b0e88d22dc270dc5f8c2d426e63a42252188eee9", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.2.0.tgz", "fileCount": 6, "integrity": "sha512-HAcAIT+A5ugnSgC1pS+tgVWjlaIx8HZo8fI7xa90WdAjrJqXEZO0oszJQV3q04wCD4/ZQDlgu8nOLPxMWlaQ8g==", "signatures": [{"sig": "MEQCICJ7lGO82hCsPDLh+elIvtjpvPqvEuu1oiYIw8ygOt3iAiA+ebyNvHjNqghjYmRwCNYlLwFNxPlNs3wh/sSF93IZcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUojWCRA9TVsSAnZWagAA8N8P/i00pR/WNusY24LZ5S1N\nuXvfzXpACV9ped6FUxZ2feSWXM69UxRIbQEnuvDpmm8F300WFMzUGRs1ZkqR\ncfN4aBp9qg5qdgeYUDkuTBdJ/6VCOGAVyVOXXCcz/qLP8FUCdc3hCrLkakXG\nVcw7PLwoqTR3E5KR0/uolGNBQ798sLjSaxRAnbHKKzC611mAq661IOCkk5+0\nCMFm1jpIlxV8+xroZsnOIl0Vi8BGZmRyfLSRbnstcNTP1mEG6nYqQGnPSnKV\n1g6hydEOVAUN3Sppcal2NG40QQlvNmF8lvreoQwqSp9fMSskIpdyWJ+aqDEr\nJyCUvDoY1pVJpNeiY9JHsnr/mWVchdS5cyoTUW5uztgP0iqu3w/9yqkOaup6\nRnAbSxgzDhYRBUNmc5LIxAqSfEEH3lvv1JNuD91aXUtLujOesDn2nQkruHeK\n8CYrtR/JOCrVbXRElt/pPg5qEEYp1GOQ7tYVceXf6/qxF7/zei5PIDwn/+iy\n80q9JXqp1ddxkB5YcUDVk8vbES35L1HxIUDYyp4iRVeMyZpeaD4oHxxblRLc\n7NQh1vaf08dAHagr1NNyMh12pxgLWMRNzKsK3epqs4BPUM59ZXwtq5LfZw/H\nCe8f1pyKRyg36+34XtFKE+tzb4+MGbFYkOVBoNrkbyXqkHt0lMFdRbsficoc\nyIY+\r\n=1uIf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "type": "module", "module": "lexer.js", "gitHead": "f71ac0646bea958b61bbaf85f5dd3291e223bb30", "scripts": {"test": "mocha -r esm -u tdd test/unit.js", "bench": "node -r esm bench", "build": "babel lexer.js --out-dir dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "_npmVersion": "6.9.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "pretty-ms": "^5.0.0", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "babel-plugin-add-module-exports": "^1.0.2", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.2.0_1565690070188_0.9920417866836848", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "es-module-lexer", "version": "0.3.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "993cb87c976412fa8b6bb93a57003133b4c41bd3", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.0.tgz", "fileCount": 6, "integrity": "sha512-CSkPBs+ov29eOmyOMiO1dKhOlK4gRfQx2eGw6jROLvT8Q/ejPmMtlcJ/7dtQGGgm6Vmh4foIwbYV6bb49P96jA==", "signatures": [{"sig": "MEYCIQCuloJt1ggytzFmfP+44tFokmkH549GzdR7tHb0eanT6gIhAOS1dD0OMed7j4iGazvwT/EbxYaRIg/1qSY6HedFarv8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXIfiCRA9TVsSAnZWagAAGX0P/RvdJ8hxEVCnVOaR6Hwd\nk9xhQYZ52qZlAdTqvLUiXPvgolmEdXSizSXWKDHRze4GZGezUK3lFpYBUj1q\neCEa7oAW8n+PUFIrgC/55WrZt+RScaPT5gJeV5jHfSlRnsLJoePT1dcS04aR\naTQowPgbvn+EwfBKkmPfJmle4VPV02CX3wKOFy3kyi715hsnQP0jPEAG8q8y\nOyPpfc5M+yLfCTGxOiLEyUQwCc3RqWsWLMriglSt/Ri9p20ssYf569rykAHm\n2DIIoSIr8pgRiF3mZDI+LJ0h/HYE+w2g2ynSurB9/AvbLh5XJDyJTrf62saX\nvwNDKLk8TenX/n1iYA/98Y7YBglIVCJF86JAh8NtvDmQxGIq+eIWYJmQRr0e\neeRj+2z1lq5ORACmj7AMM/A15v95uHUOM9RdMwgsOyL2Ks0cvj6KPRFxChn2\n+gNJpPF/VK41mCYp90MOx8iRXKxmiFmnP6TACqSsZ/2tuXv3ZdZXg84vW3yF\nTHn1u00Sq0/uDiobZEP9Rm05yoc0SRL74ULKlPT+ZXRr+WUkGqF8UcCXFsi+\nniNrxPJNcEQO8ctVC8Bu+1hYqRyQvxT/w/SMrQgmYaYEPA2QJUErzUiIwWs2\nxfn7eNP55ILL5qCNv9wndHZigd+ses/WgSKau7DCmd5TGEyHpP0kMcLRAC0g\n6y8W\r\n=S5/R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "10bb10bec1792d002c66d5ccb0195ac0b2d5415b", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs.js", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "babel-plugin-add-module-exports": "^1.0.2", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.0_1566345186220_0.25364749438547074", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "es-module-lexer", "version": "0.3.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "9c18cca85bd89213eb26d39402a7ca1129b0ef7f", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.1.tgz", "fileCount": 6, "integrity": "sha512-a2WuegQkIiNmwdauY97AzbQjbE8aWnpz0H72paHc1rM31x6YXvyva7I26+5geOnBRUtdO4c5x2Ock8M551PjGQ==", "signatures": [{"sig": "MEQCIHBSj4OZepjcwG6MOoc+r9RTfF8VL8ECqmD+BGaeqbq8AiB4ZsrteYL3/xwEGlTImkOinv+2LqqWrlF/CuvIeLkV8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXInYCRA9TVsSAnZWagAAO3cP/0GUCan2Ex6Z5/6Ni6em\n1tj2WtbEf/dqfp1Wlkjqr6exeSNCI1GEH3oHyrqIn/6uQiXAuWKK9Vf2eScC\n8s+BgPOKgThvGAVnniYnMMHDxHOFYT8eWOA8Evnl6FatMFmDwApXJFNDt0lT\nru+oFZDHRqX3BXBQOqCanZXc1mt1b4K0wHQ7ryu3Q8s8Shl87AXtmeRIhENH\nIcVRpIlUR4zH7qlGx7Xmenq5iXfuQ0VmnaFBhhdqNxi1VaJibvvwVHJN3ufS\nC8Jzc44F0oCfDCe894qGwxlVmy7ufpFhFRD7AqXgKVV2WLGucyZlC6TgVaN1\nefR+UrRwXgBVIhrK6WZWpMso+NJaik8BhhmeL21ju3oQioIeQKi2t4eXSALN\nlfaBgw5jiMNZRYgfjdU3C56qeiXlNjD8yugPQO+hg+1KE+Ye/6CPYxgfHVeR\nE8lRmDb8PbaigjAmlSkOdEhidIsbgoUsATUG44Pq/C3yiw7cz35NBs7941ep\noB00EG1NHP90dv8Gb/DU6oKJFUkLMlpCI4a2OEd9ciLCQhhV3f7gXta2SPQj\nkOhk+9tnZbLbcnyyWojYeRaJ3rYekibM/LbXqNyVcdYEbsq0F6GES3FvN6Mm\nmneKpYby7OJKMU4NoBFt5kTeo0gL8DAjIniNUSWTpJDhMRjwS7z4Ge790BTm\nWoAW\r\n=KAJE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "4fa9c5d1d24244b9566b911f9e80db56595fc076", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs.js", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.1_1566345687830_0.7250971844084024", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "es-module-lexer", "version": "0.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "c620ac977b1704c3b56d63ed4377cae1ac096f33", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.2.tgz", "fileCount": 6, "integrity": "sha512-UfDIt/CDOhxl89buYDZneu/olBndK017C3+PImOFXAQ4IsWxpZBJn/GWNM1PcHmptoEt/iw7u/wqc4+xkW+gdQ==", "signatures": [{"sig": "MEYCIQCREi5neGbrBfVOlb3uYhxgP9Ne5c4XAiX8Re6RZiXV+AIhAPiG0K8r6ftZqbIKdysLw7hNQJXmakdZx3dcJCTeZfMK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXS+XCRA9TVsSAnZWagAA00QQAKI0lJGsbGip0OZB0ZQH\n0bTNgmmGTW49JhXsJI+AzZZmVKkPAHAkMndercBJdcQE+oKr3JBa+iD9aF9o\n5yVvFMmbxwBG7wpQ0F2fj1mxFUwe/Kd8R9awzk3Zds99Q106xveXDCgxGoFv\nL6YnXZ80gZ/Nok6l99Sx8HId4Dr1VxMDXWkJaWhpTiujktk9tBiliNGlpgg3\nbIiXPYH3atAIqG7Zgh+m8n/k3I4LOZ6aa8KO4FawsR1ettnc8VXVaCrNPvmF\naiRhT9wtDj0Nx4lr/JkvIlWidiRhDkVa0OU5e6jZLbUQK40YuNuqDowXr8A2\nEAxy+x1ccQ8GLCv4Hkv/FkUTgTvxUkcGn2D8HVOAzn5uV+tUymcJB0Xcf99c\nta5jrXhqBWxigNxf/GmeGiK1bJ32phUe2YNBl/sHQ6p2+m6bWKNNsfDHCTFP\nxC9iuzMeEi7L+rFXilMaKDMEWoBvS/AS7oYPVSO0x0M4rrg/cx23vOLWK7AS\nssQTO5hjhdZML90HvGssEgwJQJF/C8DqW8VEELfnzoRpDN+ZJUQzlToxdASh\nmaBDunOyLBfliM76fV7ZekV9sK/9+yeJUEOSpBqXDwEBmSzBOXC0REtOglV5\nLhRv+WNMQUeG7aF1MBiwdpoyTvHisUDESxoq5i8lrDMi7Tp70RFzohsThE7g\nDyUD\r\n=0dKq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "4353e2d103b27353b9402d28dddfa20b74efa23f", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs.js", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.2_1566388119077_0.006868268105616915", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "es-module-lexer", "version": "0.3.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "c8dc0db46e31231bf7114badcd1fd2cfab4301aa", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.3.tgz", "fileCount": 6, "integrity": "sha512-ibQ7kiccHgYmjwVyEpzzeZ2Qwu+GGKX+i3NJMLwirF7S5ZdMOhcj11n+gWaMV5Y3G+yZ45BeOBOkfYWJ0JV0WQ==", "signatures": [{"sig": "MEUCIGIMFC9DGOh072jSYywEYvZge2AgXPC1gS/8ttnpmL5HAiEAms/T1Ow4c+SuxUQLOpy3jaRRr/K1TDHIT3N/2mOQ4S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXUNPCRA9TVsSAnZWagAA6+gP/iyecCqFb5ecsdz+VCZm\nbIul3gxfoFO92HkXCyBqjUwXFHsN8OrHeUfcGTvQbMbPc0rt51Arm9lS2NUv\n5xoJNXvC+vh+sz4pOt42kY9EVgvtY67fUqAIgQ/xYvY6dYvFA+MbIl417kVO\niJ0bkX43xO8veGyfsN9QKBj8DTeAq+7XxPxKJ70wuqC0gRZzRXjkuK3CqLSS\n21Uud/2q1UZAj9zS4Rp/y0LHeH+/HJhb6cn5SZ5UyEgdSmL8F9qiBysgHXsK\nH1hbN1qeloBy6NkfK0rREegj5ubGy8FlM9z7fenri+wFgMYFtQ/m7FsrZlrd\nnAF9ISXTIbo4G8xJZAR8dV/42SEeKU8Q6wvipbSyM2D0+xl+BchoKYWC7+8O\nAfxxmpgc2YnEJ9MIB4tczqKBP2C+hj/ROw5BgCmlvN+FwHP5XUN8/y3QkZRH\nV4v2p2j+Gu6wDJo4sPXho/X+CI9KecmPIZ6SicBXykd/UG+08RTXZekN5zFd\nicOSVwsS5R4ELmvwe4LVuQee96Y96zQJJaHFiTLF3u1LfCagRr0aEHsIuBzZ\nCqnX7unSi36XprodLIun4fp3r3RhSFYFC9m9daCk3f6Y+bkwi33gcQdxacB1\njT6pMIuohMGQBVI1YlGU6hVc3Qmbor7A89Y9vxgq+ahcsk9HM6rSURV0tA/6\nWJVm\r\n=VQ0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "795b5f2194e12403f59025de009c9cd5f3dc7180", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.3_1566393166552_0.20006052665234164", "host": "s3://npm-registry-packages"}}, "0.3.4": {"name": "es-module-lexer", "version": "0.3.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "555227a88d7ccc68aa3c27704b772be1a720bcc1", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.4.tgz", "fileCount": 6, "integrity": "sha512-sEjcehTo91Ef+vnYz3c+J+T1cMlxYfEiSKtx9S/6VJWa68TXuWvbOhXN3fHtWVHmvAWzlZk78EGsRnRMzQ29oA==", "signatures": [{"sig": "MEUCIGaO455XxV0gbQ/wvWCjZ222HavSse+JBILW48lh2YG4AiEA6xhp2ok7/tgTMK2Qr3IPzup5SRZmbJE9oYenhOr9xjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXX9SCRA9TVsSAnZWagAAQb4QAIOh2QFITbzisk8AT/Zj\nXs3gccArLv9JJoYyVeXkKdzDPuFAJUp4DRFc34TL7zWJPXmmGvksgBBJjBOQ\nz2Blp8IvgxWb6XCtE/xnu5r00c/1el2lJkDtN0uT40whUlOLFZsEE0Gvxizx\nSuQZVQV19z0luqPHDKuatvS6q2JYEbi1Q4eMxBVuXp3J8b2/bajVei9tEoYW\nUPiAfC7Cbx06QLdBEAAzK4AI2ei5qctuAdIz9QigKH01+NsMT1AKYNqAldUP\nOrbU0DuZIzj1Y8vcCe8HJdEiAJkhmCHJDya+Z+XpJCZXJYFzjm+kJeOZ9R+G\ncF3RkndEWoj9SFDdBCLMARMVddJh141ZKmz2YCMMc/MZsGyDrYLZvMYTwvVh\n6aibNnJD8AFZXadkVlljI1M5pTVqT4LN8ZTUBp2iOBTgfw5SYn4FvA8dxLS9\nCM+wlKmzWuQT9QnkL8S6lClw4hEJV7G0GupLm7CZXbQjwYXjeI040S1Wlvti\nqFRCIosjMKuyng8Hhk011n6v/MMAWK0FqIjlqdKI5h/oZrLtJz+eA2YUh3Gq\n95Ed80OXm7amjOlZDRepiJjnbunXA35hTO0h9e1opDra5T7y/kd+kcdK8pQr\n3qO/3wi3CA1ZBNjB2NFyaUufR0ZFySqSej5pwjOVZkLWK7T2E1Xafa5vAp/o\n2CH0\r\n=cHfF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "e790f9b7eb75070c83761f0262858cd8ddf569da", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.4_1566408529568_0.9402541432698972", "host": "s3://npm-registry-packages"}}, "0.3.5": {"name": "es-module-lexer", "version": "0.3.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "2372995cf994c75287bc3864899821c838867a67", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.5.tgz", "fileCount": 6, "integrity": "sha512-iUGTeNPl4SO3v9jBTuMe/jfTsND3O2n0DnkMp9h6q/SEK1K1rwWXq06FqB1117pErRs28wCIaeeQukEDzHH+mg==", "signatures": [{"sig": "MEYCIQDjPP2/SRaQiFeUQvxKHt/6OSDWfg7F9obbsbRy6C8uwQIhAMK/D7HAhK0xH832VnqPiGJ5cFQllVAd/FmPFUuNvtbZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXZ/LCRA9TVsSAnZWagAAm+gP/AmNHXEXz4SGkH6LSgTy\nLGjPJi81kHaZna6I4PkGExYSiP8/U6fU33LDJC/HFYEYYqp7fldLRZTjsS0v\nclZNjMxdwyC5O1pfXNb+mqQY3XIbOQo+MAY4TJmY4ly9Nr2z/EkzoPHvwl8c\nHc0bxrQROKwyY8B+GK0k23AqOrOS87hlSQ6+zgbf1q6QfZkYcAenf0ozHY7G\nwRZF3iFuW9F2tPv2rC2aQXGdBfi/n73pCxx6L1O9+F/eiCGRq52w6MOKiHjw\n9QljslLhtxd2oFNhBTUl4ySPpvvES1DB0rRoFO3jN8o4BG3qhvj6Wpq9SW3E\nZwPH9UH8N+siD/O+ku9bezMc1JKGQ+Xo5GTX7dUH3tR+7oW76Lkkr4KbOlJV\niXclHEGu1F2h685zHmTfDJ4vJXNjHCQ7kFaWpeOr/8TazGjqDFjx8LL6ru6l\nwHipCRJ/lAHC1czPuficB7wv9/m68xS5dsYIsbtGcEUi6Yb7wDUgjNyOSOB4\nj3F5wX7AJE6ggnpluv8bTfs8YBMFMYTXa0qIp7gjKsJHH1aQhgEmcpeARXmU\nRFTuZNeSCA7MRsuFWSTnrh3pBV6TeFNqYpVyXlsv7mVgoCvmUYrA0wKOwZ8F\nPnxXSeLPjQvTTL9itNEdM/eVdNhVHjAw0dARW+uIJ0dFPPRAC1wgSGpJSPD1\nIUJN\r\n=zeT5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "604e2e2b5cc3ba8c5ebd798092f7b61eb6dcb807", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.5_1566416842662_0.7323072515416615", "host": "s3://npm-registry-packages"}}, "0.3.6": {"name": "es-module-lexer", "version": "0.3.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "dabe3e3b2ca19e29b37d7df4008fe6107746777d", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.6.tgz", "fileCount": 6, "integrity": "sha512-2pzyN7X1XOogdhTI9Yc6pHv9y1/Pq/yqD8XIFzR/2JlzNeFgOVo8cHgMFNDbFpL5xcAQGNUthgd8ED91gANUSQ==", "signatures": [{"sig": "MEYCIQDco1r7nkwgATr5Zz0MF+z2IeHpaOFVrHy8m7lVq1s9QAIhAPH8l1aEDqATXP/NYUxP0uwoY4nFFpIy35rrLHtKvjKR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXptoCRA9TVsSAnZWagAA8kYQAIIHdXQYAvOqchvgadVh\nsWXgN5qR7+vauOHJYWNbjVFeZAUzd1MgXLd7fI5jjNfMn9qDEgABhXwbSvrD\nOpv/r/+H7NoP2MM8O1VfXgfdfpeZQuwCnUrler8jRlvZA5ZseXtGaQDjmx6X\n3wRzdVSqVUVkvwUqd5PYQaHyCMZ9azvy+zcvOg+TSs/Zy70BXfYPAKMkX6sE\nll6f9JU6SSrOm2LAPjeiBitYuC2yXuXXy3rvYzqdLiB5n76OPx6jxx1MT9F2\nos25ZTh/2/Cd5/yRl76AyfFhCBlAFw6K6eEGtpCUL5mUw2qBe7wuQdj+WTKv\nuVF1jYxcrPTm1ne8EXR/OappoNRp4txD7s/GxaQW1fdHWJbeJz+n6fTad5/Q\niHnmhmkfmxs/jIjC9FpdzPPyjvtMdc4cfid13C36kqovbFHHXSXC2hb1hYG8\nIiWWxRD9vUmB7TLW6iDBRTABAZBCzardLmkZCLfapWgkMytHv7Gw+Aferovy\ncwUOza71FqiwLChWHIw1/Lk3iUNJMBfp/2eOWCCp71/I/ST26Uw0mO6A2HRT\nTXgnx+qNrAsIKzlVDEklySBHojgLFlNiovib4wc8TkpKgUxIDFF7FEAO06gI\nlBxZsBJ4zuPQi6CVYdO4HcADssm47xAa+mQHyYlC8NmHk8K7RLJgAQU3cEL7\n459E\r\n=LWaE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "f8c6ebd889ce94c7f79e230dbf121cb9fb58df12", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.6_1566481255367_0.44713127468674196", "host": "s3://npm-registry-packages"}}, "0.3.7": {"name": "es-module-lexer", "version": "0.3.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "d3d4919e9d5e0717f5b699443ca8e47388c79f3b", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.7.tgz", "fileCount": 6, "integrity": "sha512-MrYrBLXJRJPkmSCP2RAyBY22HQejDc8c+p6+ioHMD2Qcgt8dXA4v5BqEuEpIU/PQ8Mgmzm4TvXt1Mb7CYKHZMA==", "signatures": [{"sig": "MEUCIAv5pWd7j4EttZnbTAjSEB3ERppqfvf0gkX62yHaGM8aAiEAnHPOUbtxgjzHI71zB5HLOEidwoCgOvJWw+nWii5byPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYtLWCRA9TVsSAnZWagAAJFYQAJp5IqXMevXJ6oj+RPah\nZU9wdcVZbf1EUo9nM9Adrq2y4uUtpy/g8d3QzRZ3F7qnl7LWO/ttB2Vlat7C\n6ciacLOc5q9Q1MhGzaDKk5SbPVPXXrAFbRLwWGNqJ5M5pGrvD6skSVV7XcYp\nL7KszIhOti3byKT9/YTxEHrXwsrO00nS1PH2X61oqiUykJmNTxvdfamiDDx0\njJcCdUacUiLuUPCv184XdiGoyVCXCe4k7MafMSH7KEyBL9ZX20FfypsdonIt\nEw3FTOH86zOvvaE8t4nXXKtXOJB281Uwyvu2Dqq9AOKR1K+GEUwplFsNhePy\n19MOhj9LR/1lRPQarbgS1xhN8lxEQ6G1+ceyz+v88lIMD8QCXu2rWVLXz+aV\nzq5D2sv8+85MOJFpkvloc/FoURFWm28LfIj0qpgWIz07oWDNawyTTZdKWlgX\npBVp61OIPfLAv47Oi5TZ6/dHXFpPByWFzpQK9rSTeSMxz+dZinY8dENJ6RpT\nncaXI6Axzo86xf4RfpQmqf5a2NzDFYCutkHKXNykCOtKVrsiMXtp8B9/5fd+\nmbSIoABFY8+4N3VKnUcTFP0IujxK1tsSZiDYRUm2+TsAZlrWSCo+zPz05Zlu\nErYOQ1jAwxkT5V+qogtwSeb8X8XeMh6lqtWEdzj3c2Vjas6LZunURrJMSEKX\njDsn\r\n=qFPA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "f23a946cf5bf349835ba084479638c1717c0d523", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "build-wasm": "make lib/lexer.wasm && node --experimental-modules build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.7_1566757589780_0.22874724915784217", "host": "s3://npm-registry-packages"}}, "0.3.8": {"name": "es-module-lexer", "version": "0.3.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "c8a6a510cc7bdb90bc1bec04e0a7fea5f2568134", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.8.tgz", "fileCount": 6, "integrity": "sha512-HYrQsqrYYUY7S37HaQdEoasa5HkFVbWFzTyMQGIOBui+Lj/iCqPc9IDPVva3VZbc6qRIvqItG+VUAqZajYlA3w==", "signatures": [{"sig": "MEUCIA0Uc7Z2j+G4LRMaSxKme1hvwjr9huI8MhomT6Th+KmfAiEA4sXXekvCeaYxi91yrc9ZNExJa0FubU9IUxo5lPxFY6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZTUPCRA9TVsSAnZWagAA5dAQAIqME1ob1vM9TOSIVzNd\nzozjVQzsmo1dEoyn4kEqHVRifentEDelYARETt2mjZdsP65ZJXCJOf1dL911\n8wstHrjEaVif0QApOUFW5hFevwM+kGf9wVNmeGkBxFtJVq8n3dQsmY2qJJ29\nH0pMoR0JljWWdZyB82W0+DEJ8mHZYDuXZAHxJbvB5GzyTZDOFaClUh1fk9i8\nlwCATgoso8Vxm/PPZyG2rBIS/m/11xnxxVsIG4T/VFHL/Pg7C88rAWPqzRPT\nv3Nsr5Ngqse7gmNCuAYHzq1FM9p/XBH1/S6hfU01I9xkl5+lOPjvk5RfqDq2\nyYPavRwZo6yNIcjrwTJOG+0bgGQrW67ZykFpZo8PB4DZGRlnuZ+982MJtVnU\nTp/CcVs2BkV5IcraCWEwnYJ24gA1FAqhKoUktaP7JCLEAZAYmCur+t1C9nbs\nSnGm8ULhmWf3JhbXG8Zr8ae6x1kdSrhIUVG3hAXGcArSoxmUX2m9I/VM1437\nq6dj4Sj1a3jzoG5vZFuRzNRsuu4ZRBcnn3yXtTcHX0Zg0TKV9PYzEz1ZPhGZ\nZbktI2iHWBP35yNcv6FeCHhqMcpSqZcEZEaGxkbPgpJdzEfCK3fYTNxBxgpb\njkZsyQ/L2cXmf6xv3QHoOmrHw5V0Uw37rWpLotcAAMMIBiglSrzPJeZI2yy6\nW1ya\r\n=MtdV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "4f0ff03df621d23e85e99d0a5497f0fab0c9652a", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.8_1566913806818_0.34956796793517886", "host": "s3://npm-registry-packages"}}, "0.3.9": {"name": "es-module-lexer", "version": "0.3.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "fbdbb35e1ad434fc71f5d83a887781fadbaca83a", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.9.tgz", "fileCount": 6, "integrity": "sha512-beowXiBsaVS208GHoJckwOxlG/RpWLLz2ioJHwkUJ3RGFmW508E5RfntV21bMjzO3hQcwZOTHewUFwnGzrBtIg==", "signatures": [{"sig": "MEUCIQDS98kkLq2mMLmV3QYZ7okdqScry2GVLp9O2zWY0XmMAQIgfuyKIbAI1zIaxS+RpfQqVzQBLazmCnCotqEj4g/uoZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZreZCRA9TVsSAnZWagAA8QsP/AjK0FslFZ/MFTLlVEi3\n09LOmTKYs9O8N3/knPrPFh1a1j4qZG+rjvQsC7Iaht6ew/bpfW+9/w3oNo7h\ncvWRGII9xa35WvoQYdNYh45NY7uA+u1FDGFwzA9QRgVF6cXI9EZ+YZ8fTyD0\nwNwbdXdRKDW8u7VrthxWrMM7twODLDfcIC3OxRfYZmA7oQ9gtteQiSGe40cU\nh6UV5XUWfyyvfZNeUCIcKccg9xrty97rCBvvh29WoWj6CviAHMbUjFUhNtQo\nPVfkCqylgU48IFyFt4kuMHEVB6xknXDhYd9wk8id0DjmUCVImIuSpnF3tpZJ\npMGSQFexeItL7aC47qQU0vW/WBooaNTXzxEIOvlZ/YRzom0t2reZJjcGg/Go\n/pj0Ijn8Qwv7T9orW+Pa3LGFrbmGKCyMKd7NO+lxfNm9UhwoMTEoXYE4j5/H\nlQ2bOuhKDGzhoZkQzpdVoJQzymYmJeiZoQ8hm1hC9jH4VyFGUbUWBgE8WM0R\nzXAIn2cXrlxtckq7k1hhiDhO5CKPnB/bvdf1BA3IveOasu171+FRGb+3jZMQ\nM6+sQ5J7l1FDqb9+BG6aIMDOtMKx8pDP5I3FKiomTWJH2NfI9OYP7/0Sfw2j\nZTvcfqSMaS2aS9PpA5xsUcDQOAfCvyLxxUIqwp0O6vGsuLsFjIiSkIc6Lf6s\nirKM\r\n=QNzs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "62dd3ead4bab5a4743ee9600aaf9a4f2cfcd9e2d", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.9_1567012760982_0.7293895945750168", "host": "s3://npm-registry-packages"}}, "0.3.10": {"name": "es-module-lexer", "version": "0.3.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "97c6c9e4d138cb083a2bb8abbe2a56137ac08532", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.10.tgz", "fileCount": 6, "integrity": "sha512-f5HlqDngn96vZYOZ5ZpLYtbOdNemp3PsoWhay9rHP92c9kgQU8X8vny3HXuxYfcCaNoZcmybBJU5iJX/ENY51w==", "signatures": [{"sig": "MEUCIQCkzuxBDTucWALQYFZ8P4fWAp/PBDAvWQsN0Ncvz8fBcQIgHcggXFDU8owLbPHebA6N/G7iAwkc2fgEzYjF05IdMj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZsAbCRA9TVsSAnZWagAAQNcP/jIzwcy881zo2Y0Zua35\noEyFE26b58TMN7O9XP1w0eC+sk4J4CaR1G6FhXb7hoS4XBv9uB/qUUsDFg5m\nB8ayH77NlvyPikcfIkHG5ErUedNAgihkZrBDxJc0SFIr7prhR94bUK+lg804\n/Lt3lOGo602MiuKebFpmmUK8HV2WOZ3+x73ZqpqTcfqLi9NgtXW995uGBmXF\nuobq8dsuVSUSqEwKURwQpr16zyM1g56uFemHF32Iq0peuExxAaXghO6EzL1d\n9LZvgOEFLxvtGqCmTXwPymdg6mtowK2iKsmP/D9dlOLMaFSGb7GoLfFbfqTi\nUyuvay1recqhNFcC8ZvUOe/QLCNvdksdZpIPUJET+MTFaCF5B9W36yxAGwcx\nYyK2R8kDCnhxPeLwSplDNqg/XSKkszcqOZi56xN/myGHfxx8G9nT0lANfRo4\nplqvO8I7tqBXJ3CT4JsY6vS7NG+TsH6cl9ZYCkJU8i1TFU8xt8suSMxnrQgl\nxgLD4oqchWQKMzUkkkUrUlHXURAs1zYa5N3bPN5F9zW57yRfLuppVLd6RazW\nuTN+lo6oHWc644eZhkven7RggcgpcoiWMQ/k0h7uBlzDiEC1VVsvvqCgXKmd\n3dZfvFqFN5JKpv9nCg2Dmd/FisydJJOod0V7KA73n62QI7lDbU9Bj4Sk8Nbs\nACl7\r\n=LldZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "5017798d0a5fb94fba751ab86b872c281937fe8b", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.10_1567014939322_0.7781891910102965", "host": "s3://npm-registry-packages"}}, "0.3.11": {"name": "es-module-lexer", "version": "0.3.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "1dda77036d9926a54dd3118f842eb95cc3bc0d2c", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.11.tgz", "fileCount": 6, "integrity": "sha512-kSdILd3gV57hyHg4itALGcnr0A0oRpRC45XHdvpLcIaRD6oDz0Gji1A58PIdy/M75Fat2LPeV4RyEhtsE86gzA==", "signatures": [{"sig": "MEUCIBtMq4P4n8dcGDRZEUbFkrCxKN+w3NQUS/0KVrFWHu8IAiEA+z9FW5BGmQbfHIUwS5A3R5uQNb6jh8OzpWW+mc5q9qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZsnKCRA9TVsSAnZWagAASagQAIJ9Ibtr5l8y98cV8VWv\nmuMGkBYP4jVuiqY9CzhtLBUTjICiikdqfkgeVte6mVosIUd3L1Cj3OnVeRpg\nwSM7wSqTgIo/cKxWxpVpWwfl8YwccqO6XEih+2LpIBv3+ZN+khaUKvVIcYFV\nMLt+ROgfG/OfVdS+IRosqv070AbNhvUZjv0JP99Az16hE1NNFBCsCdCZ8cfe\noqrFDJBw9hEjGtrcD27My6Asquw1O6EiQ5jFDx6mfbrdVZD8n6/sTSbM0jCY\nhsvKKGmMRx+3HRpM0v3n5jzfpEfvLIXXbH1o33upNdp4n49vpz2dwGxqJ5my\nXStoAcr7jAuR3RjphEaXpqfxrOx35zUPaf+oP2MLEQ7bMz8lfRfGxaRLTmb7\n6F4rX3T3fk549rbc3OtV10pVz+Z5f1ElznEauZo5X/xZ+UKfjW0MzzZO7Jhw\njdyhjbesSP4xn8ZGaQwaC1oKz+xoZJ9a3SWlZDgdGt23iMMO0h9u4y0MueQy\nAz5OUdfttJc0BPTfudOoKqs3OUIO7DsCK7oDij5dOLro5nk84ZEEMmQ+2kNM\nanJ/AJmRYw3z3MxI0P1iMJRsUrzNXy91av6Ql6oV3E3NFY613Tt1LVC4dNww\nHZB09xaQr1lzXJYAoXb4cWrTCNPkDubxF0b4HBb8dKcwHYHMQ9cbfHCdaEQP\npfjf\r\n=bZa5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "cd9c691a74b22428af451e759a2ca80c10644815", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.11_1567017416517_0.17980321592691273", "host": "s3://npm-registry-packages"}}, "0.3.12": {"name": "es-module-lexer", "version": "0.3.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "9ea4f9cc3ac0e4a7c35e8a635fd66ef15ccdacf1", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.12.tgz", "fileCount": 6, "integrity": "sha512-ofpQwR/H8XC1Kx3WLrxpsGgyCosunfjkGFDw+1yNTbmbLMg3o8PZqsHxwM9EaKoM+RMtY730FIM9hj2n3wRnvg==", "signatures": [{"sig": "MEUCIQDVOeH2d1hV8jNGc4401y1r0T4BNOcoY5b2pUNmoKMn/AIgVX/zoYAi/8ZDb5WaHONSNb/wBHn6qF1NxYiEvFpkLXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZ9rgCRA9TVsSAnZWagAAHa4P/R5oPxlqp4dIS5YPP4x8\nvnUQUbfeL7frUSCS/rxqYna3dxmTvg9ueLiDFSoIzQvh2RWjK/imFOjWKGyh\nMkq943YDa01bOQvZtKGsbios4YtDBOvFcOCVKhICASxXH5jLo197SC8LvSuT\nb9OlwUoWn/LQ7JZLN9uF/hlltUi5/rYP0HxCVJAMVKwE1p0/j/rnUdJ2iStz\nfqOdrId1IKIHH13gLSiVnDf9XIIvRwSn5jBUAM7J/6LMgMuXiBhmzUbpoqE/\nbqFDl4aOnLp493fzMTo/q5G+cL2wiA2WT5PpOikM9jVBa1jSFJ7mF+AfkxEP\nXb7JOcZzxJ0wpL5f6Ta+KW04rWX4Q+55nnWG8B0KgrjKqF5CrKaOY49B+r/+\nXLg1I5ZlQ94HE4qkSurPNxxmnqUpLRRt+XQ0tSM0WHLXQ0OfBeqXHQ2SUE9y\n8MLkOe8FFz6a5bJDxaeJC02SlzEVkrhyevNp9tmBezF8kOi/vR+bbl3CRkd6\n3V9iqccZlg7byGQuPIVK8G03CIQK4q7/6WgEhxCG48CVFw3yo/rTUwsbP2DU\nbxTSM4heGh9wlzMUblhZ/SKRJzkAC27dM/8+Osq+XJcuvtOpm9EycKnc0O29\nmzd9EbVgAddWgdqZR7vPZo/Z032AiFPVzm34Bt/c+2cthH1T96jMDzx4zr5K\nwMIW\r\n=ERFt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "3bb0168bdaf2d12be6e79ce73efc1fed7256bef5", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.12_1567087328067_0.8242655012956575", "host": "s3://npm-registry-packages"}}, "0.3.13": {"name": "es-module-lexer", "version": "0.3.13", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "b7f986efeb55a5d3ce0f3e482409ff43c462d398", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.13.tgz", "fileCount": 6, "integrity": "sha512-8VCx4z2cIGFKx715cOvicAieDBjf2B6EYq58yzctIkKnXpKGEsd+qTf+LU1KuTkosWkWH6h1iDV7LWoKmdkaOQ==", "signatures": [{"sig": "MEUCIQCwHqXumuJpvq6uYz9bVvi48cKUs9Xh+TVwSIyVjOLDbwIgJbrOAdUJwTEdM3GnW656L8S4k8SDoSs6BJm+a+89wp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdauDYCRA9TVsSAnZWagAAAUYQAIABynHt8EJcwSyiHQbE\nl7BUY3IHUA67+ihBFMRoHxR79wZyE15LXc+SuDayDaV6gZM3tfvuYoM835oQ\nSaVYMecy6K/cRfy4h4MjAMMbFEDvMHnji3DtKybY65RU/LxbPNX/xRzI2S1h\nI18ULprN3AJoGMr71OS/VtS2vviavppDOQgcpphQFbsna3aI5GyNMXShw4oc\ndfcAZbEY7cSCWcITuZn+0ilF3Twjiq3GjJkyC4jE8nF9t+oO48dwzFAjbmxR\neLnzqGaRJ6CuW1OBVBE3POhX9ZR/SrggLCU7QSfKRy5jRhYfdq0a7xu6F3zX\nCjlMPsCYTPzqcZ907hykUAj4JddDrZXVsRzuQ9VbhfkCWzfvbg2tkSfrdYwE\ng5f3K39SuK5LZPhHBaOGF7ECh0ZmqrMTJTkc9ISmB5ULaw8GpMFeQFImmqlu\ndimqnztqSw+Kot2eu87ULHx0+OcpdabPF8l4fSoKBhsVPDKAv/hw9vY+HbiE\nZtMoTLknjZaIz9xOkKOGDLovKgkMono+4XLn+RX4U4q8oDQwkZNaLkenVmb+\nAtMbokbxvfrDCNqlxdqfkq8cGD6WnM8dIQ8R61OL3qRH5TWofE+zc1IXu6im\nB7PoMrj9Vh+n3gGzB5z7aeEa7ZWFHRuNrCjxI67tFnWCR1wffOwKxvyiRptZ\nLpn2\r\n=0JPK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "0dbc4f1d6e9e579a9070e96a7c6e4060006ee585", "scripts": {"test": "mocha -r esm -b -u tdd test/*.js", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.8.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.0.84", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.13_1567285464285_0.4648028937193296", "host": "s3://npm-registry-packages"}}, "0.3.14": {"name": "es-module-lexer", "version": "0.3.14", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "091b02a3f22ae1fccf0a0c731179db929ce2d4aa", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.14.tgz", "fileCount": 6, "integrity": "sha512-g7OCaU3py+ROON+JdSQiDW5hChAgDEG223/88IW5ddLTXRz2CEGQowkwIWiK116jX28j6RTxLS+AxIP70NpwkA==", "signatures": [{"sig": "MEUCIQDpbIwbLFxrz9knsTUTRilgkZMTfPn2OhXGw/L1b2IFaQIgTFcg88KzP0OkVDOUvPfOXQl8OnmbBTWiM8etIZWmJpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNbmJCRA9TVsSAnZWagAAAQ4P+QHPHTGdzvdcWnUNrQ97\n0U1ENN9cV3yJbuAiUDx4vVSY75EAfAK/ptd4DhpO1hHd69xUUWn03HTXM/zN\nW+tMwzkBEf/V4LAd2l5tBTYQSynRPcq9NA4EyBhQIZo4OzohObC4yjztk4Od\nU0XWLmIOtMUPHe6OaT8rqDC/khHfRlzxLyG14vAwK2ImJ5wQQgwdnXq18CG0\no6tBXBgOq1xtzmSTkWKk5tLyOMuXkYbeQbGvzxzJkLza4y1jm5GT/elYZwGY\nrxI1Lc482I9jAe36gg3pGysLhEdtdwdbA5pRchNK4hA4Qvx6tGplw2hnWD4D\nWXghY8+bRIF0FzI1xqY7+q9Sve46yAsu7RbRfWmJWcI+3kSBbLDRxxLQ6Ytp\nQGoQIkvj19MQtu+kTfW+76yxh267kD7GuXaDNhruV1X8VefDIigmPAqKFNeB\nq5uZoDXV6u+dvtp8C9EkqVewKIvn1CpUIBLa6PVY9xp5w4O7KT0ecgNDSbP7\nrriQCncmZtLyp1c1Q4MdSXtlE5hn2oXNSHHp5UtqB9/JXH65fTeGYponXXAo\nVoOTsIirPbMdY5QSFWpu7n1D0o/FbbMfxeZp/wEj4VjNzssB/nXUvf9c5oyY\nOYs9EuSl1Z1e3/NsAA3pkK6v8b8isyNFBH4KnvGR0rx7uvRKdPOxxMeE1RZU\nW9vR\r\n=xKO2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "fa60b9e117ec5790c3f07c9799ce2cb4ce1bc204", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.14.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.14_1580579208723_0.23568990014253188", "host": "s3://npm-registry-packages"}}, "0.3.15": {"name": "es-module-lexer", "version": "0.3.15", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "475cfceaebe210c52a7b2534020e4c35f01da7b6", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.15.tgz", "fileCount": 6, "integrity": "sha512-2eIKccdZi3+DlUiHlU76zBcgFGi/VLLagZfLxLGlOHxF7PLXAde+VznpoR7bIxbe4PfNQGjqy+kT0uabWgwyEA==", "signatures": [{"sig": "MEYCIQDuUHMf0d4H8ec69/vrj/2/8rJx6JAQ1YXUuOgEgR5r8gIhAJddueM0l0+OoSWxt+7uWW3wuyo1IRBd/FoJrzg362XJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRHvnCRA9TVsSAnZWagAAfLgP/1PlFgviN54uML02ja9g\nPzBSimw9xpRbv2VMA49bnA5KmOlJl85Flk1QI/GY4x/jVaZxYuUCnbJ7pAt8\n4EKOdoBo0Hh2wXbiARfGFZL6qCzrVaqmE09EUJE65DYsTFEYLvDMXPQMIXBP\nDBkq4eD1u+3HzvkcLQFKBBTpjUmu1goUJw7BwX+bOoZYgBosEyihUpiWqRED\nRXgGo2dWbZ6v7IJYJb/T2ORl2pX7Xvan3PE4EpZmYBVXnQIdTajkRTW4MnNr\nARGihSX2sHfSIugCmQ7bgMGRVpOwfysc13IJcV33sABWbIlqIacQV030bMzL\nzHzJ598zNg2LgJPoV+DkOEJOSkRHFHsc2zlaTW2s371oD1kltul7A06l9Kyu\nSy5Sb3DlisYbYeix8U/BsHVL5TMOQ/UjeVxjk0r4f7sO9cuYvSEX4JzDDh3j\nD/sIUf/UN6vwts5h/8Oma5iCnvY8VUcBJtx8JHOi7IC3USCaYsxperpzBY0b\nUCf+y+HCyuswmfdgk28Fcc01LQOFUMemIi7gis3RtSLVF+v4l/WlwNnXBHB6\nWqG/GoMRTWR/BbEEVUidjHXDj5Lo42hUXqq5RBhlrc8vf1AFql9ME/YbVxtB\n+h+Mz5yVBQXwlg6Yg6K6E7gWE6Wf7MkfmpTuQQYW1LqYQAa/4Ntv/ADj1f0P\noxZ+\r\n=pr9b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "exports": {"import": "dist/lexer.js", "require": "dist/lexer.cjs"}, "gitHead": "95201e898f05fb0467db51420ffa093a1c7ebac3", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.14.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.15_1581546471161_0.3506742077689753", "host": "s3://npm-registry-packages"}}, "0.3.16": {"name": "es-module-lexer", "version": "0.3.16", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "b4de20cfea94741f77d434664303262af5ae6131", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.16.tgz", "fileCount": 6, "integrity": "sha512-/kbtTlheSFU/foXc4KxSAqlY2E+lAMGq+gwROvRMZ2900Is+nEeA2v7RPxlHtAVQnIXuXvF/vRiV8OSfYMYLXA==", "signatures": [{"sig": "MEQCIAMfGKdezs6tYgtWiQDmVELgHJCuOiUfmbTxRCpYSxmfAiBL03i6vWf6iZr21HXOva2aTnYKRmM9AlHDQ2VWQX5+Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSEdPCRA9TVsSAnZWagAA4UMQAJ/VdNv2r9rx2iZZJUdl\nSJWFBh7KaZ/PpjaL8hcOUagTu2CjyJRURGkfZLlaNzDb+IqxkriVeXQ9kfZE\nQeEHviXPE6XAEoJbgaF5KuY7kGke+8D0BcmxVnh6i9l/X8T3zTOEbeLDrf3u\nDfZRaTqOkuPVEeyL1jmeYSttJowv/Q+FlL70Q32ll7B7uJQcbAldczQIG/oe\nNavKSBLgJ4nX4Lu9YJ6CEymA4n+H0bopITK5tPcsn1z7rqVekCH3+uzP+Y/+\nCSywspwLGc6vlODUcYHOrQIHM2d/2vn7m1kOgkmqTZH6CJ/JtjYE7qDMnTPN\nmuQbU518EwaN8/JhiWACjjnGhLba2SkfBAz4YMfd6GnHLJ/LtoaRMpWAa8iK\nGL+6sKgtqZasuEMPVEEwYJDTmUpV7IZhW0mg9Gwdukw8u+MNuCPaisKwZAA3\nr7aeW9zrJhrbh3jc0otq7EcHeKpl5RBHlx/DUNywJoMuupUBscSIDsEMt6L/\nyDyOJYpueFy+MaBZDVcE5XmXphgvmbrWCst9onayAFEmo8/hG57lQByohZ9d\nWZPXEhnc64Oxc3B0f62yrBbtSV49WsJo+zT7WQhaZiqorQmA1YRbzD9y5nhM\nVpbyUF9XfX8BQTuVYIE80SnlsCG+LdTfiKj/dqZIYNIXx1SCIoohjZpnRX2R\nToAi\r\n=5fOB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "exports": {"import": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "gitHead": "5ad1d0d6a7c4907e25119e8d5ab1d67618f6d615", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.14.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.16_1581795150543_0.6307119654465341", "host": "s3://npm-registry-packages"}}, "0.3.17": {"name": "es-module-lexer", "version": "0.3.17", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "a248dec2870934d9054420fead19db095ea21537", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.17.tgz", "fileCount": 6, "integrity": "sha512-nwvMtzyEB6FhlyXBlV+BW2By3Vn2sUvlQBYP4LvdK8YpdbFQUOiBoeuB7/ip1+EbjmgNydkJ8+dIlyO09VP9BA==", "signatures": [{"sig": "MEYCIQD19OJ6Xa8cYN2V63TsMKdeE/YL16HU7uKxq4XODA/xcAIhALNCCvxaCmXZkTuYzOBA6O8ZBcOo0XWIoKlNEgVCfHuf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS8SBCRA9TVsSAnZWagAA2wMP/21gWtuWsFrp93FzC+Wj\nPNNtIYzR55FLQFmPo2e2jGe3ZYqr2RmLnVj7JrKBl+zt4gJCX6TfpoHUxdzb\nsjpGHY4FGRRjVtF0Q8pcCXe8DLzhzs0ziQZxMD+mJ0HFnrifO5BxksAyR7vh\nQ2EC9cNVmyQk2BYSvywGkCW9VwYH4uBi1ORVg65MLJ3iEWIvAL2c79ciJu3t\nyRiH/zljBfYbVzfRzM0D6AohwyktJqDBZoETi7PHL0nWR26QIjK1UNgZbotW\nZa0UZFqWHDABevTt0Spj/uIyCB2HymBEH8Fe0fM7ET3nb1PSfcZKHhhOYD+D\nkOJh+Qesz11h+86n9mU6QOiyTMPSUtNjMeUMURCVfDPDopYs/PoGxsDKt9RJ\n0Cn9gVPr0z4hNwir5zXQ05cNlUOUvAYZQakYPrOEOQiVMb8RIBlkIxke35fU\n4P65kkaOtcoXY+HNoFTva6KqxBOnYIG/R+BFVkkAHbSzUjzOWyyYSIT+9aYS\nZZyeYZH18Rz8ROJcwogzcCBKQ+8OH1hNMd4HP67RJUNhy5F3vuQszc9hZTyZ\nLES5sgAq0XTvpblT9BHIHjQvD+BwCx1aLN6YRgCr5U6oVnn8LvfclVTwQTLn\nrWnSYS4OaDB9mdwlrJ7NXLa6O9ZeeVKjfspPVhzlYFSDabcQDYFbGNUj/9K9\nSweC\r\n=eWv7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "c42605cb06473c319d9290056c6a1dd2c0a7aee5", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.14.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.17_1582023808519_0.09530629310275884", "host": "s3://npm-registry-packages"}}, "0.3.18": {"name": "es-module-lexer", "version": "0.3.18", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "b1a16f8166806c705216acb4517175bdb27d16b5", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.18.tgz", "fileCount": 6, "integrity": "sha512-xu/9M+womMLsLbr6SVRfVYvGfgUJjZcDtz9c2DdE4ip9s7OhpLpyJcyLofLnI6E1fbqv9LuYGWKBcRK6B6lBFA==", "signatures": [{"sig": "MEUCIGXW1jzAPBADuNwiFFTZhVtrAOfMEdKa1If1JMP3PgJOAiEA7hsj5edgQTMwG0SiD4uAtU7pQqS7ur+NSwOaHJfUrno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelPvJCRA9TVsSAnZWagAAzuAP/jT8NZhuCbU9BWGIr5ol\nW0OyTuosDGlyIN4hGJg9NjOzSTyHmWjTru1ToPQ9gw9Sw/dEmSGI4/Wyw0wF\n+X4vd+MZDC2PeGN5o2sH7PmXsjUBtTk/38z6IMXuBgshi0gXoEyLQzVhBrfV\nMcZqhDq+biN3B+6K+gDRPzT4nbngb8y4G7DGosJaAJNQebOp/xF93sDzzCxI\nZMrqiM8isgt3+uOZts4IwCv4fPKe3iMsfozeU97ikkn93vON/V+ifYgFXYxd\nOywF/Tj3tBgYzAOpY2h0GdalXQHwBsDXvC3JRlOavaMWi3ynHT900xHha7Q4\n49OpzXVyRyxGHPrmNkqdrUn15p/sPB43SuYlFvdeKQC8p3AFVmu+Sc7tHLiN\naxSpLP8LYIdqg+kCROkjpUEJya9v9z2aoNaIjhoyT0i3rwwAcl1MO3ZF87fm\nz5PtKan8FKayHGrst3WqXPhByLP7UOAsWWQSteuS0F/ywd6Bm6RnRzxT0VRq\nhkwlwL3AceoMZxIDdz7q1ZT6eDxmeTlTV58+rR2F8vfD+1Lw5SjoURhJW0rB\nWvj76WnSY5WUPPVaDCOn3n1joeJSlLQj7Q/AIYUtWdyJBPFD8SvtHo6OonWA\n+JexDgonYToXe+/cTxy5iVPji5apd7HRGph/Acz6KpI7VqLEA35PLUPjwlzd\nUrho\r\n=lXUy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "f23cc2eb0f450355da7ea552d87dfa5a18f1d83a", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.18_1586822089251_0.09574827618120652", "host": "s3://npm-registry-packages"}}, "0.3.19": {"name": "es-module-lexer", "version": "0.3.19", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "91065ea884f436a2e140e5006b1aaf154ab23f71", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.19.tgz", "fileCount": 6, "integrity": "sha512-EMav8JgMd66ltDkVTVjknEyjxwjnFg4dfoTJD2T8yH9qunGK3kMKsIqDDSEWied2bOdNm2zTOUcZaKTScjWFiQ==", "signatures": [{"sig": "MEUCIQClwaWZmto26MX2hGYszjoVxu3MA6JEhT9614pUNeduBgIgGKCCpV9BzpzLnhzaSly46tw9O31/s0v++b35DQgOaHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevzUACRA9TVsSAnZWagAA17cQAIz4mkR7Zmhvbki5sucg\nX9ROXkj0G7+cPtq9NYonUqGE4827/sqYBG+/894R07rWAiu5ZwVbdcdjbSKp\nv1q3JW0ucUhOsUZWVCVBCY0xekV+TByiyQKHLV8fAxRmUbRxhn4Bu8AuNIku\nkeaOh/4SrX0Fyf1LwYkYP1HUjM0Tw1lGXIw92Y0K4LifRHr2MIG8lF52YmkG\nuPnih6RB+0d0WUMUiEXkPCDdDbQ6LtYjuNHiyZq+ELMed/P1WC43eYa7D968\nHT1Bz0z2FDfg0sbr033jqmBPZDzwDk1yRBm71hTWSMl0YEFY5+Uwu3XGxcV8\nI+Ys2C3cEef1vPSI1YOM6MnV0awcSzZk0Ixj/DserVgQPibC/k/WFHzSoqpm\nnvH3uU9Q8VvtTSm53JdIQGACCQMq/7IgXcsQrZp3hCtDpzusf/vGqa/SkXMQ\nuYwothokV5OQww8Wx9ciEIn0IYB8WG0oFKL574XRXNbJOcVLq/tWFo+lNVXI\niMEhjV4UT1eaMAEqZxeJNp94lXEgotNjhHBHet73GYix3D8nsYbQgSSgExZ/\n6huLHUlKHcXezitAf0c8qBcPhhda3KHffb5IUGHPFpaLzh2+CZ/RwhRTu+RJ\nDVopY/D9dTk7JaesZCmHWw5QU46M0n3goz/dPy9+HpB5TJ9DCCd9qSrJHLf2\nfabD\r\n=HXCy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "9fa1e79796dc0a5d6d2dcc04a8f674b4dee7989c", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.19_1589589247905_0.8074739447289367", "host": "s3://npm-registry-packages"}}, "0.3.20": {"name": "es-module-lexer", "version": "0.3.20", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.20", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "13661aa6d793da74684b3353ad4327d5fa59d3d3", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.20.tgz", "fileCount": 6, "integrity": "sha512-kfB4R9F6FJ5Bgvxyk6W/u/a9cncXEGRyaD2UxyBhrF3+ty4qLzptgStLfe0J90o/qkSiiY0+yyzMcIIPvAOjIw==", "signatures": [{"sig": "MEYCIQDwj+6q5xq8nOlil61WO389lcPIXy/5W7eUVpVWyCYbCgIhAOJFiMr8KwE/aI6OlOeMJYev+alWMieGcft+bSHEAypm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJez1ixCRA9TVsSAnZWagAAY4gP/2Tu377IxK8M8MIqw5Se\nWZMBmT+SaWiergqnhT4nzfBsWsvoMB9QMPoAbPtKlg4O6yH/PZFyQ0BCxm3c\n5nHIvP33V+g4bPC5DKqW2Z0oTr8UzN7Gkfh7/rr/Wpp5QDyTCRgH4VPfx4KM\nkAzFZBuslbaMks/28Aw41NC4nFy4Zg/F3kMneNOvsxCSZIRLMXKmIY0pGLpq\nEb9vVpUYCS5MF8M1K0B7Nyo6alTB3vnG/jo16ux6AHDgWDH1q2UIrCxV6U+7\nanjcLJBGH3Hc01agYdpJE0t2RIwqdMlVaq/WsB+rXUk5/OXbZXG9vIjc/2mg\nCw5gWhYVuyr97bZQwzNp4Iwo6ZmV+6cj3rc2NjMI6Sima1IKomvNz5eJ11/8\ndGVTinbopUMdX5wK7CH8D5vTZqpNjp94qBkeFqWG9I69rmNoYpInT3MnRCv/\nyqRbrpzBTgOR7mhIFZSA3Navz5PhXPiIpqm8AX4zJnrvRsElCtOt5k7alpZV\n8efAVKsBzGRTMy9DvnajqJMmt37UzMVGYKZSWnxlaz5Se+WK22uHxrJ7zdVP\nK3YyD5fEmLo8T8UyFQsJQ3Xon+QsjTGnm/0WaHBCumU5oKBq3aOcK9LOWvIN\nsW8e7Wq6adTUubi78RG8f8B9aMfy4JeFENR6y8B4L3lFlRALMaI4A84aWDJY\nDX7m\r\n=cmh2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "4650cd240d04e739ab0c8d94e876b33949b080d1", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.20_1590646961426_0.6550552383468811", "host": "s3://npm-registry-packages"}}, "0.3.21": {"name": "es-module-lexer", "version": "0.3.21", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.21", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "980c46eaae4431127f7581d960ce060b8482defc", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.21.tgz", "fileCount": 6, "integrity": "sha512-nwMhXomqz/xmJzc7c53gTcNcbWdHIOFoBb7g8ZnFtVr5ZefAZZcbcz8Nsb0VU1xmNQGcztpfOlT0O2yLfKtTWg==", "signatures": [{"sig": "MEQCICET0hYC9bZvKDlBn68ZuRTm/VrrqbBvlh0b6wXq+sawAiBFq3Dcgy9pLEN/NR3bKZ9Tf0TvVrbIUqHXgGtx+G6m8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe42qQCRA9TVsSAnZWagAAYm4P/3ke1Dz00zOy/rttALsO\nVb3E/PITy6++T4fZCiipl2s5ObTj5J2Bmkc53w9aUr9lJ8UBnlHO2xQcVBv4\ng8DShwOy9uaAYgGosin8yDyOe5TuETtBKd7cJR2LDzOiCHBJ5aqs0mDRVabr\nuMyRThCd11WCR7Z1SXBuOVJQVuCdEHPLgTb8hikEpQ6WPuM3jTUKY4Hu/KOf\nDsvPGxe6oAscm7G3CdagBdk7EfT816RLmt3QLcTnsyTl5OlKNdRmxAaq32P8\n7xhSz1LxHw8GjaKW6vevnmVvOAuzhRaIwJ/GRVHxmjqrxsPlVJ0ixwWd7yFo\nKwllh6VfTRx7h3+qWL/Vi2Pa44Z70+9BihE/r097YhiqGMbAFRVk/FlZvNJd\n8Nk4AQf15nu7bmyzBTPUNfZwKqC3FSZUExrcuRs+DfGl2XfVW8au0FgyHYmP\nw9JzDoF6/7Ig09eCjvmrpDGDvqsEXyixxEQq9DIk08L6DL/XXNI6RcZ5NPFP\nRcLJojhhtCZa7Jpb4guTKJAnVZJVMEQdS2ERdOaDs5rIthvzlEtIceukiEA7\nrXCDugQXO+JN9kLyRrH6ZFFAv9XS/c0pfh2KAzfNO7tL9BhjycsohvQSSa4f\nxHFVGAQei32V65wwgRMUGyQ6PsVy2N4nWAGTkZLioupOPN+ctqaX04ATDRqN\n/YFU\r\n=jr4h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "d133c3c0af94c09afd7b83a6fe80cfd980bf490f", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.21_1591962255773_0.28594033839598554", "host": "s3://npm-registry-packages"}}, "0.3.22": {"name": "es-module-lexer", "version": "0.3.22", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.22", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "8bbdf8c459beca0ff043a4a6e69f8bb24b19b4b9", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.22.tgz", "fileCount": 6, "integrity": "sha512-MZVYKnbVo200unXER94HgVSZE2BOKX1Q5/bPZdSlumriutYcIrYwk94kPPtMNDwWaOg+dqEPL4KjkaiKT5qBqw==", "signatures": [{"sig": "MEUCIFpguocmL0CJpknxg1mQK8Sh9beu6mm5br7JnRKw4O/AAiEA3/Fw8wBu23HHlWcTHZ06aGwvue4kaBVHxU3YqMaUkGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5JA7CRA9TVsSAnZWagAA+8MP/jp5XwNoApkdnYp9AhAP\nT/CSI0ZRB/cQY/nJeJuu3pcRrRjV7emnP6kT/d+JHfvvLmagFWAJhyG/936g\nqb86hkVEd9Ts857qHn/gHl1/AweZHynnSFukJOfBrTdPVcHa0lPqVhHLy3CS\nBWLa3f0jxO+l5jTzcuQI+AjcS8JrBRpwTGhB0IeY9XRmQ9uddar0yxFJNeBa\n7x64ZtzSQu9aHuf63coomXZPjwaZe1LMVgVUXXkgmmzYfnpa1shD0RQOE68d\nfdVJBkEXUtnbW4X1F9yeKYtuqATcNHGLwSnbGY7Q0UWUgfS5ndbusC+mjyQI\nQdxDofmMu87s6k3kKSE0pmGBonc4Vr9hCf61ZGRswWeQkBQmGJEjga170i0V\n5777esKu3uv1ZYemetw+h5T5j2UNltNS5o5CI27RFoQbw91wCGTkZaheWWQU\nKFE/qdsoH3WrGlGQoyRKb9qiFIaX1ecpbITHSbyEJ/ZbWclkAJOXPoyCDqAJ\nUP9ezHmh7UU0GoRybGSZ/TGLq1m9QvMV/fPXU7G1ABIJmiHh7ClT8O+nVK/z\n8MRwd5elpZsj3Y+ogvmLJar8oBuDfZQCg5iU3bikCT69y21j3qRVnPy45H9P\npA9mpHsYNrdYxyLpcWMpwLANprlGGi//JgDwvDAX02kORtbeeMDaOl7p9ymn\n9/Vb\r\n=hF0a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "d1aea3783d35ba9165b917e0619806cee748f482", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.22_1592037435299_0.18626153250863275", "host": "s3://npm-registry-packages"}}, "0.3.23": {"name": "es-module-lexer", "version": "0.3.23", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.23", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "68b9eec3c288c53554e128a709f704a9df291809", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.23.tgz", "fileCount": 6, "integrity": "sha512-D/bUHdO4eGdHMbl5lOwwzU/NngyXZQ9owxURENCMPGsYtMr+6MyYyQzUhpNpuJTBKMANftk5NyKYkFbHvSKH0Q==", "signatures": [{"sig": "MEUCIAhYEoBxsoZfDBaswiYsFt78PTkdKTj6o6cS+XSAJDQBAiEAmdlej+wqPw/kgDqebgJ5dspfp16VzyX4vDDJ00jVfWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5XyHCRA9TVsSAnZWagAAs0IQAIoFqVopDo07KvP0jwvD\nAkPypE3us3O1s0kkmxcq9JeNq7vG8zPyj00vZ8f53A3KquE03eiX2giTLVj2\nYEcNIGcfpZ3qlHMrMzjW/MJSbyh1I2jPHKTYpr32Kab9tfFCgzdZkHuGoSEl\ncIJKG4//fK6FGls5eqJjABXQOGGt8asSuJeloWIYc0cyH222pjanHN1SDrET\nu7p0Iz5ODzKYU2m+2h7YONrRSllZCe9OxbipjV9fD1Hc1e+N8lulkofvmOK+\nahW7I4ZTVDOFdCRorUAEr4T2k6IzFQ1AaZGrF/xyk9rDqzJoDk4N8mkK7bEi\nMvSE9PMxD4Vay7hwIxHZOQwirVrBJlqr5UCsyZHwP0kpTMbMexSh1LHk2tSU\n3vsKfIEd2fKWhMcYD7deXbDXgAzvETr1sE6AXPkNbPsdXGEkn4tH9wLVr9rr\ntbwdL344IiWR2icVI2bZq78wkoE9HveMLPD7omjD6xRhsfDxL2M1grtIFdsM\nhoW61YBXJ/dYAFeJ0Q/S0FkDbpksmGeySfUmCqWhnUyW1WqKt+/lbs5mif0e\nkvJZuR7tAEMSdISwxCMl0x7yjDkmf+NQIrjYPVY8uvs3jxYgH7lBlk5/icVH\nXo7sP6QkwM7e5ljK8Uv7JlL6iXupNtXlpjKTb1NcyEBW95nrUj/wVh8mXKW6\njMin\r\n=3l6E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "934b92923468ca79b3f90c32d1a526d62ff97d07", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.23_1592097927374_0.7458547684868118", "host": "s3://npm-registry-packages"}}, "0.3.24": {"name": "es-module-lexer", "version": "0.3.24", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.24", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "e6b2900758e9e210d23aec2092efc13ca235adea", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.24.tgz", "fileCount": 6, "integrity": "sha512-jm/i7KdJtaMDle921xIsA/MQQOGuZ6goYxhlV+k+gQNI7FtP4N6jknrmJvj++3ODpiyFGwQ4PIstJfHJQJNc+g==", "signatures": [{"sig": "MEQCIH7R7pnuXTPmkuRRrcMFiey4ooMGHGEmmSqQuZznJc2jAiBmetD02OXW2Xe+A+DoLtcLRyyB7+IqAg05j4ZPsrvO9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5sxNCRA9TVsSAnZWagAADUIP/29vc6MjqDwLxJCGDmx/\n29rTWh9fCENq9qBvn4JFmU1ymKWi4yGx+dJX7oqs6U8AgtqadLBJvD/hnVQs\nSeEHqobUXzbCfQgd04I3pOzblPznK5KQ8plYVNTe9ypsgsA0K0zDGmJeH75N\nYB6ZtO2dEA9OOU2fy12Q6Xz+qZvsk21tdbfoyoBNykF7o0UmiqUMytMkYTI/\nMUneFz9NlfRUChINtcfK5wNRBcQHtPUAVMV4d0ycSZOY2aM2SToIvXBleB3z\nbQTVJQkyk3QbkolAr4dqqtvnbjIeGmaD9W5GR5aeCjInREYq5Y+xk7c35dKT\nbhEWzH5dB5qdhSHK3DuP4WkTaEJynCLxS7M3jITM5q1RYmt0ixtGLUwSbddh\n+ayuflqDu6MxfiIzm5MpflhIoC81p2wfRpp1VePsIPB7c/KxQIg48rJmMo5p\njfMEvI+ZyxEX9dzoFmP8N3QdSNhpJHGejBuGd7MVjfpbTvvnnveYIcAnGpNA\nV/1oclijasBAXlUTFEJD03tTGDJpnPIx979YeyJYjBbMxdxq2Byj3iUYE3Dm\nfqdDbmJhZBltK6F2jQk5I+u+M+m6H589Wj0j/+vkXQSnIv21C4zD3QRLXZHj\nYDToBIoLH+6vqK4k16sptVySqyce46SP1ah7z7IXlg02/QKZhvM7h+1YcRv7\n/s8q\r\n=UDNl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "6c678cb2d28d29b7f09463ec0edb2ac650e7661e", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.24_1592183884688_0.9344380634020326", "host": "s3://npm-registry-packages"}}, "0.3.25": {"name": "es-module-lexer", "version": "0.3.25", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.25", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "24a1abcb9c5dc96923a8e42be033b801f788de06", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.25.tgz", "fileCount": 6, "integrity": "sha512-H9VoFD5H9zEfiOX2LeTWDwMvAbLqcAyA2PIb40TOAvGpScOjit02oTGWgIh+M0rx2eJOKyJVM9wtpKFVgnyC3A==", "signatures": [{"sig": "MEQCIBPOPBHtUt9hvX7RJopxxvistyO1oJHb0qtAnMXdUUxZAiAkxMbRH5+w4dTi8pbuK8OMZvdYJogyeRjEP88VDK0PjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTsd2CRA9TVsSAnZWagAAOWAP/2NV0Y+B1+rb/amEFChA\nYUNkKTiXgxkkbBT5k/l9Dh4HBg4pKf+OukMlc8WJ5/u06OIUQQ6u1Hfcv1Bt\nvtR9gjTQGdzBXR6d23ACyNj3oG54hCj7+Z6NytS5KXPMPN5izmSMYt2dVdhT\nC5MdOK/5p57VaEKTku7HqyYiLBmntJVPRvzZ3WM5rL1ifIWOa922IRkCnVhg\nHvk+JR5jHNnLSH3fXKs17w6GNuc/7Yq3Oo+DAEFtZoPQ3svLDUhd9dkyZ558\n5HN9tfoAG/+ScaEvltO20GYg9dyRRvgtfjlhKNrK4g5K0W6VRWYVwKzIHp5x\nG64f7LHro5rreapBqg/YA1BWkdZSMJ4mrUPN0ZoaFjps9bOgZ04Raig5ZWRR\nmvkMr5yMWFTlF2zarzb0J52val1ZNLrWoaZpOFZshDmKLsJ1cHwxZIqFKm78\nLvdNBxjcR5nw+XtKIJWE+KIzprqvHJESJXtlXdMgoSkgPxUxWmelHbLa5T6X\nV5zfqgdgcutjuVyrrWVtffYUgF+rGPKudRZYmbq/oUM5oyDdmQLWF7ZUurbo\n9BG9AhX0DtMU4iLUCaxZtquQ5eVq3YEjjWcazzSZTybM135lDinUj61rBB1E\nRjaNX+PDlNsWEKXlcoQFmndWveNGfTyMkZHLw7awHEyyCBqGjmms05Jur0Ji\nS54+\r\n=Mb6U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "7e46232420d73b94a3802b09d79914fb02e7c980", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.25_1598998389166_0.9066816875273052", "host": "s3://npm-registry-packages"}}, "0.3.26": {"name": "es-module-lexer", "version": "0.3.26", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.3.26", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "7b507044e97d5b03b01d4392c74ffeb9c177a83b", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.3.26.tgz", "fileCount": 6, "integrity": "sha512-Va0Q/xqtrss45hWzP8CZJwzGSZJjDM5/MJRE3IXXnUCcVLElR9BRaE9F62BopysASyc4nM3uwhSW7FFB9nlWAA==", "signatures": [{"sig": "MEYCIQCONsx20OKfsCat00sYkKIzkx7QpmBjMK0xeaHe8PevbAIhAN3s+scnAffTLF9KRBbvXOc+ZV+StIXrnADC0MRg53zD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnJVSCRA9TVsSAnZWagAABtcP/3oPxdsuu6hVG90ak6zb\nJVayrGoyVGQnqzs4gMhmEVP4AB2goLkyedq2fsbSFVQO1QrAVq20ZsrWb2/g\navNSt4ygdd7hvTPxwrenZ2tuVU4PUwg9dBsqhFy6haZQNtVlbRG0MDp7Tzlf\ni/QIpSl7ssWn3cL9+YngfoDy2myqp6zGo4pVxTUxhRm9GtWPKS61Tjw+054Y\nRm9Oc0wsDbgSdXtl5sgp0wQlHRZVNP2AevXxIzn3tuoAPaSjrCMUUpb3vGyi\nQXet8dg0HeNW8dk0URZO2sJ/ujI6TDd7QlaAXnzgPTIBKzgTZgIhbebxCV23\nuhbgHipZ8eKDgkQIQmo4p23UHnPK27Y2IMyTBJ72xXVBhjojCNz+9XyaEzOy\nWtA5Mxruu2qhQCD3+kMQ2hYJIqubZ8yL9PsQEYcKG+kTJuZ9shNeK55UgaFu\n7bLIUO4alyiCOhUaXL0m/iU+QKbNFC6qtQSlrVOKIu2zQHwb5ONBJzPBhuf4\naDKJtezVCS0a9MZKPP+KudmE+AJXKZepZxAZj7dpVPSql7/PvAYc3x2Hn9r2\nIHWM8/rP/rIUZbZCIlkGPuTjhS1etjfXYXglpVtc3ICLzv7KgBnxK4UWCq7B\nHtDSO7gCF4mRzgzCL/WPybpQzKyszwnJ0nZZnmO1RnKiYcF/yA5qZ1Gmq1Vx\nnQJz\r\n=wu30\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "2a1d9d0d2198fc873967ad56a890b525fec4b15b", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.3.26_1604097361759_0.3159799175069813", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "es-module-lexer", "version": "0.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "21f4181cc8b7eee06855f1c59e6087c7bc4f77b0", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.4.0.tgz", "fileCount": 6, "integrity": "sha512-iuEGihqqhKWFgh72Q/Jtch7V2t/ft8w8IPP2aEN8ArYKO+IWyo6hsi96hCdgyeEDQIV3InhYQ9BlwUFPGXrbEQ==", "signatures": [{"sig": "MEQCIB1AOlqdQpy5sfaT9plCtjIqf2JXZjgubewfEi5mpNBqAiAg2vOT2bERrlsGe9JP5kcGCJXIAXIhpYjEokezmvzZVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMQOFCRA9TVsSAnZWagAA7i0QAIIYoKssBpPpak7VKhak\nXTkTe6fJPQDMQOIkCJr2b2dmXrSYCrRYV3K51CGq4lL+9aUmXRI6dHMZ0Noi\nkboBjh18p8TbJ6HTDekX2qHLRRMKeom1ZZEfx4cudpYPXg7bUPI7T0kwPmVZ\nHLXjz0oxEOjW9mtZy/eV4OhlBeYACJ0XrPpZLCO4Rx0MTWLwXuFWAfNvKvhJ\naqavp8T+1XjSVXWJz20hUpYwcXY6Tr2XlHsEvf6EdPYqljPfDaZPx3Aq/8it\nXxa/ar2pj3j3a+N0n8HMvn5+T5lnrD6nGxwbOuupb/fwZwRDYHGd06Afnh0Y\nrRS8aE1aJJifA32/NdBSa/N4YktDecp392t3jTA1QSXCpeR7o0Rww5swtNA7\nM0b5h1lJO2EK2S7CAtsidI/qiio3eJaY7hy/toY7FgP48P3m5XSJbuF8H0nL\nkV8C4uI0QVvIFNBLh2HajOAi1VHYMV/hgg+isITT5lSz4iYLH24GZVgeVN+o\nN/W/Vp9OBQiAnCSXdOVlXgK77F5U7chZ1GbQyOOVh98oCaY40i4pef3hjaD7\nn3uyoBqmWeYu9n5jhLs15NKrbqO/TO5BLLi+E27t+sRc3LqGiXE8KHV5KmgO\nWJdGAKThIh0qtMx0hJX48H68zssRDiojWBQNkFudrNwGO1cdDsACfEMjwr8z\npqUm\r\n=j49j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "b63fd7c9fe2b5d060db2e4e342d7ee94c4ccf4f4", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "15.5.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.4.0_1613824901178_0.05404311668218664", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "es-module-lexer", "version": "0.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "dda8c6a14d8f340a24e34331e0fab0cb50438e0e", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.4.1.tgz", "fileCount": 7, "integrity": "sha512-ooYciCUtfw6/d2w56UVeqHPcoCFAiJdz5XOkYpv/Txl1HMUozpXjz/2RIQgqwKdXNDPSF1W7mJCFse3G+HDyAA==", "signatures": [{"sig": "MEYCIQCAY1EADkTE1TTZQfpv6ULLJtaGp5bPYgRwiZ1hlmGqFgIhAP72NCwZDVM2PRbNV3X/2ETNYghyOMdQmy3S5HmZDmBd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP7J3CRA9TVsSAnZWagAARA8QAI75oQMv+n+DYY51ZiaL\nRWoBvtOJgy7zNywA2Ub3sEMcsZQHdC1LidGUYDpYehsyZhgDnINd1jQaJiXY\ncPaB9a3sp3EZOPLEKc3/jnMGqGblCWXTkRCP0WE/uKNX7yVYhV3M/r49mWy0\nKesnpSqNh0GmRDICZKb6MC1rfvQ026HIIpK6ts65gwUa/eb+PpBfG4wNFLP7\ncfPlkMtrP9G8v0ocAad5ZaXiMZ4kEAEP1oKZeyEM39Oto1vHy0oTFViFeMDR\nE3xPucQqkNoJaSezNyXJ1rOh3utRcWbjdRaoP9aP6WxIV/bK9bC1WNPNDZyA\nRANx9imJr76ZHjVwBZTad3Yjc9O67N0SokCGShoBEdR3AJ4Pln8C6Q+jTJ98\nAotaEZxmzBAeSsESBOwlSNeskm76VNkoVqk+lRHwpsFkMv7r7dNENvjq60n/\nB14CwFN2PMeMBnszl1K0lEJjEoPUmn2KQJN4slFl1E8Z57G5iSaWmZO9OXjc\n9Xjr45do0yLmdLb5zmroM4SSd/teA9kvEcd5Vjwo26XA+O6/qYu3eYDroPLu\nIOzWBtPhY2HRlvI0BofyIwlNJGif9e/5iI1hWQAYF3amb/tQGPO77QnDCI9w\nXfuCbxetp1uJI56DxUkrfNcybu4MVx1tjFl3uYqujp7jVZy26Wc5zQECYx1/\n41ok\r\n=GXIr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "gitHead": "f720d83e0aac69cfb0e3fe3d73911e983ab56668", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "15.5.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.4.1_1614787191358_0.7467107318831969", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "es-module-lexer", "version": "0.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "1191763b788b2d92b6ddf1e0831e43adcf2f9db2", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.5.0.tgz", "fileCount": 7, "integrity": "sha512-Sa4RlGyEC0lenwd3PmD6v9hjhQlS2fG2+Qq9oS8tcVBpqB5u6kcrfxuJzoEdH2KSGsivJRAInkWTSjHqYta42Q==", "signatures": [{"sig": "MEYCIQDMbl4LkupDpb1FrXEIDVe77B/QyeWBmVY3v4YOw4safwIhAJAQ4Nidmld4AVgczQLYk7xbC+tHd748HiicGgmC36n/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx9tUCRA9TVsSAnZWagAA5HYP/izv10Jo+rE4uln1j0tq\nwMa9Pd7QBiGUbXTU4K7Elr0JCz2/OQxku/MShz5dE2WXu5D7Nhz/R7kBA9nE\nJ8ZSN+l0JcL66Qg9/6o7UHeXAriIirMxtYxT+TC6Dqq37hg4AiuxseBF02p+\nFvo3uV3iO/wtsHdDW1woQej69AtCWfCBBzLESuXgHlL8ZL5NipCtLxcHaxl1\nNAEZjpsQniUJe9qNqxk8sSgbxm4qWgbPCWv6YcDL58WTaRN6Wyc+rIaB1GPl\nhjt/Pyk33XuZLs/+GrOQz6ECc/rQDGBVu8CUdxdLyV1QErtDZr9U+4/O/sAP\n/4XX/JCsA1w1QqWOx/kAx+Nw472dlGyTmzcGFMPOroyCoOaPsbn2uuJ9vF0L\nKv3XPBf+bIwmy55M/TXkEruYqCywwwfBjDudsc68Vs2A8SdqeTXyGcs9r5j6\nGcw4RWHtjxhdWsZbzyrc8HB1m7nTWnuhBxnicL03VXfYBifS0TOJE+l9bU4e\n7tqgRbC+WHgcxS6mqm3klAZ8x3V0KdRffNz87terH1vUjzqVY7/ds+jtqLiF\nRO7dZWYoyhjEvTm/K/pRhgsCktjvrbbZOHAzu1DvrJNP3yZRJchCvoPaVR3i\nI7s8en5E8ZEMmSGWSVlFNbIou0kATCV1KSJ4icXtlgcuWv9XGEz2N3R6+Xdy\nZ2TO\r\n=O8CB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "gitHead": "4688f769ef9aaf16869ec658104f96afab223a5f", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.5.0_1623710548748_0.9628765343727763", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "es-module-lexer", "version": "0.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "e72ab05b7412e62b9be37c37a09bdb6000d706f0", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.6.0.tgz", "fileCount": 7, "integrity": "sha512-f8kcHX1ArhllUtb/wVSyvygoKCznIjnxhLxy7TCvIiMdT7fL4ZDTIKaadMe6eLvOXg6Wk02UeoFgUoZ2EKZZUA==", "signatures": [{"sig": "MEUCID2MXXXvJSidhlGL6Et09HKVL2QI1S8GcwndW9NYY6miAiEAzu1wW7Qofp7fbi/Fx+IDjM/HNAdPiH1xQQLd5QYGMQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx+EkCRA9TVsSAnZWagAAcA8QAIHdmKq1LqZISA2PMfdV\nlYf8tuLuBTA+jhLFl7ceJmZU33C+B50a0nGrITM8ujEkLMf/Ork9huPBbUdv\nvkxwqbMCz4vT8nFbU8cSHlzvTO+U2W4FudZPHMMoKtqiKl8AI32Isynj+5Jr\nleAwE81KBr7E/iy4Dm5Kcq2MZ2I8YwFdG0eRbXsrq6xbQVQblwvxhywJwtTO\ne2ImFXLv0bavS537Y6X6x8NxG6Vo2/h+e9iYBWrOFMtrQ0qq7Ghn2+mSlCUw\nKLKrrBA4gYE5xk2gsGc71jjygnehiNC1blV/wLHE1K/LeTNVlttUMUBU8T8H\n+L3/1GPtTdmvwFCluDHMyHeukUv/Ht9+sddRp4kVNAJiObCCR5xY63aXqUz6\nNmG8an7sl8ytdM7Eh4AcYxVwlhp4CD88yaIHG1Bt3DwN0FnhcNJbgKwaxutH\n9JedwjNRgic4+Pbf+YUvL9Ph1v08S0I2yQ2hSvYTAGjDbezNLTD68CZbPBL0\nciXwvT1w1tkyxJU0q8kW0+3DBXh5IdcIASy2j4dwBwuO7cSjLjW2RmZ3xTym\nKGzfZvA/JVCip/ypmhT1W3pj4YxowphGQxn3AzTNKiLPdKaWSzZM4mVKApWj\n8aLlbV5QtlhbHbnGGLLkU6fsVqoWsslIgjNL3oBq7kJsJzI5beJZptsfGGTw\n/sSN\r\n=ARSk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "gitHead": "28175f194ecc6506896bc942938f4e8c01e3cfb8", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.6.0_1623712036356_0.2198887444573785", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "es-module-lexer", "version": "0.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "33b22fd35548f4e27c3d5292622b512b2a9391f8", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.7.0.tgz", "fileCount": 7, "integrity": "sha512-btzZZ1QNjAMk3p71kK0gBALmqmva0W+gyegNWyrmAHQA/Sf5XrkpB0ZeyUMVDDJWLQHRw1p7WeZDFd2VKvmoPg==", "signatures": [{"sig": "MEUCIQDOp5fqq9+5g51W52Fw8HJbAt2JS9ZXddLU1oaLp6wa0wIgVy1xW1/4NrJNgwFRgYoLuQQQVHxinDXDZL7FwPFH4k8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4yVbCRA9TVsSAnZWagAAY4sP+wc5cyvsTfI6UcbML0n/\nm3KHETvd/kPiBNUxdCuYIsSbn3TEaQILWmzr+c3ERgamghykI7Vn+cZx3dGf\nOCq5cB3z2v997IQpnDf469c9gpz5PHmCv2nJgRg4RHdMWkoJHeBMiQKLricT\nSr7tiBd/s3awfqTO3fLSFPVcfe8FyUSuU0qCicj4M12oP7+6BJS50ie5M5Ra\ngxvwumWOJNGDesEWv1OJF1ENQDtE7eeyLVVIPweyoYz3jhHXa99mPbTXOFIX\nxAiU8JiLai5c4vAQh+NgbVZSQdEFD+ejRyWd5URadaghL+8GRaZil9judnOT\n4cNzPb5cYnBzf3aD506E4+3F8khT80I3vQ3ksdn6uKsV2e5ikUQm/X5L9CgV\nq3iUseS+1ctGe5hQhqv1/sYE6stFPfEPmYMvXkOsBlmWVsa927+YA7i+SWWm\nhLeDhpX9QBiuYFVbvWy6hKEQ2/Hclpk51HU1xdb0ESIpx62WV6lr9CoES5tE\nPL0xORLRWr3CLwijfwkNafvAMud+DdPoKUAxBd/1NPBu80kvGf2FJci2Naxb\ndzzUNSB6iUxI1Mkl2y5fe8T2tTF8TCHCAFHXi1p9aGH9hQ9hl39ID7wEsRGU\ny+QtBITUvooEJMuxeJ/3uQZzh5hvZAGx4hfqTHKi+IjleU0QocnDFsirKdiu\n/Y+M\r\n=nnUg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "gitHead": "9d627a9515ce02410781127156594e78303190c0", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.7.0_1625498971320_0.27796065640706336", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "es-module-lexer", "version": "0.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "c2c8e0f46f2df06274cdaf0dd3f3b33e0a0b267d", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.7.1.tgz", "fileCount": 7, "integrity": "sha512-MgtWFl5No+4S3TmhDmCz2ObFGm6lEpTnzbQi+Dd+pw4mlTIZTmM2iAs5gRlmx5zS9luzobCSBSI90JM/1/JgOw==", "signatures": [{"sig": "MEYCIQCXyLkAZPavr8vErmb+jkoUNe58ZmDeklLz5n3nyHpLWQIhAL8UiXgjizOXXOXtrSXMzDlTAJHAM+xPLcCS0Gfs+zpo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg44TrCRA9TVsSAnZWagAA9z0P/0s/iu2LQe2sIV1yIHWk\n6e/EoIVjg2a3JP6bUu27qdAsPuToBGN2PRgIaQGQbpNVM/YDQO8DNYm89A72\nbOrLeVfmqCM9isfMqm7eeCFvcjMAdH+gQOiDH+azjGodD8ARyAk6AcXIHx4X\n0cTF9BoEQDtuqyiENN6U3dEFIWzTZkNAsS1FKcSPWhkbtK2nQ8pVatCHiS1t\nb8nb49bduKLpPJV+c2xEg+Zd2PubNYdzyW/GyzJ+zdyiwNF7XCwLP44lOVaT\nzB02efj4BqO83ys9POvIL0eWvDizGjn15bdDqmkxKhqXznRQbrYvlhLTmvrK\nhxx1ebmbgYit+dbr7wbJdU4PoIse47DGeOdcJbLS2zPPMP4RaPYBpjMwrLXw\nyA/YbNBs1WI4GQMif9ygfj7+H4QcwwA6bOvynuGEZ9qIMGeXMEbrVsACP45f\ntF7BhwcVVk1U/Bwesv+CYX3yfGCN68+oitPdrt2YY7X4DZLafBsQ8hPtlUHc\nW4ScJZ2jOGZH9SsxvI3k+hQo8lhyuj1leH4JOlfwswMgqAEPOpwcHpOmE/Sc\njxwCHVQfNH/06unLkBFupcMjKGkTsIlvrP2121bXblYWm2OY4Ks40iINenoS\ndjdtK7Kf5qXs2HBMf5z6K/18bSAQ+UmaV83Smicd6qVObdO3cqx5/+lA8F1J\nzUfJ\r\n=VHX4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "gitHead": "cab3846462770ff07daf080921d67841e57f7bc0", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "14.17.2", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.7.1_1625523434513_0.4793826368110081", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "es-module-lexer", "version": "0.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "cc84776eaa3e1841012f1674d2bfef173b7a3578", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.8.0.tgz", "fileCount": 7, "integrity": "sha512-Mr/DFKp55FwjVNsP+R95/VNO4JRjAMwV35vo4rrNp6Zn6Cz4adKR13tMToRw3Hx6/5PXpsMScEHn8oPo7otI1Q==", "signatures": [{"sig": "MEYCIQC29Bi6Vxbln7tQqRYcWpfaT9nHDryzlgaCbA+6EtZGqQIhAPXXQnwYIRAscwzX0V+CsiM0ge5H6aXNcuQssgFmlp6c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP81iCRA9TVsSAnZWagAAXvcP/Asf3A+MSB7yZVYNJESZ\nhdJqvu1b/TF4u4TLFucid683ViUNcPGsUru8IhS9nkMa36/AASfnJW0+tU6r\nacGFbSgM12bgKc6YYTYK6AEOcSu9I/oR0mJuWzYoQg62tnVnSjK1rS8k72ti\nHgFi9RiDmDV3/s/FhH2JaS/1nPnyyo1Xxkxt1iWw5Atv0u492+94qGw3p5/Y\n2sRAigiyyG2SWxo8Zuz6VZB4/D2+uL5KsAl6pFrmIe6BVK87i91qmRSTybgy\n7cSZ5Y7p2aMwTMpTpaQuTA3jXaXY1JskJ2FLUcLP7zoOzmu2HevVz4YiG5kO\n4cXfx73qbzpY7vc4Xc/aYW9oI+iJ/CASSYnFjWPqO8GudUacLJoHnCKBaaml\nPCtaprsoevtD3cZfVgxOvC2AQ0XcrUy1qHPLzhLXdJuTEZYzLk2amTzvUzcY\nZTiTuMPe5VP6qFMhXyqIuCv50jBs1eA7D/7XRMV0WjV+okIBoZen2pvs+6IQ\nOE02iymVPznKSasyPehT61btxxo3/kFjRWT3hoWWP6bdtz7o9HaEtdwLnIpb\nEcyUWG7u0/d70IePi+tnY3X5aaHLs8gEy+RX5Ig61J9Di1Q8vDnUMhLkgGNf\nNjBR6i6wmYhmicTIPKAHvIcLaHy2g6lv0BTRat3hOG2Ztc/+pQGm8ZjUw/zy\nt32+\r\n=cBal\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./lexer.js"}, "gitHead": "88d9a4ee13596e79f10fe4252d16f5981e2aa895", "scripts": {"test": "npm run test:js && npm run test:wasm", "bench": "node --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "test:js": "mocha -b -u tdd test/*.cjs", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test:wasm": "cross-env WASM=1 mocha -b -u tdd test/*.cjs", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.8.0_1631571298855_0.7316496917241921", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "es-module-lexer", "version": "0.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "fe4c4621977bc668e285c5f1f70ca3b451095fda", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.0.tgz", "fileCount": 8, "integrity": "sha512-qU2eN/XHsrl3E4y7mK1wdWnyy5c8gXtCbfP6Xcsemm7fPUR1PIV1JhZfP7ojcN0Fzp69CfrS3u76h2tusvfKiQ==", "signatures": [{"sig": "MEQCICbmInUru6HiQoAUq5cHETxB1ALs08sBPbLBt6hf0GPaAiBrgOXC6s86h3ynigSiLsBYtH12Zp3NeMEOHsAvZVbu5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79025}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "3ee03c8b8356e873346632777f0d3155781a3074", "scripts": {"test": "npm run test:js && npm run test:wasm", "bench": "node --expose-gc bench/index.js", "build": "node build.js && babel dist/lexer.js | terser -c -m -o dist/lexer.cjs", "test:js": "mocha -b -u tdd test/*.cjs", "build-asm": "cat lib/lexer.js | terser --module -c -m -o dist/lexer.asm.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test:wasm": "cross-env WASM=1 mocha -b -u tdd test/*.cjs", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.9.0_1632012812233_0.3414549052824527", "host": "s3://npm-registry-packages"}}, "0.9.1": {"name": "es-module-lexer", "version": "0.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "f203bf394a630a552d381acf01a17ef08843b140", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.1.tgz", "fileCount": 8, "integrity": "sha512-17Ed9misDnpyNBJh63g1OhW3qUFecDgGOivI85JeZY/LGhDum8e+cltukbkSK8pcJnXXEkya56sp4vSS1nzoUw==", "signatures": [{"sig": "MEUCIQCkTEWfoyCN9rpVK1v2UxvOKYf61uwC4uv26C3c1ycFkgIgbYodIdsLT63KSvGprQK2uew3/FE25oMK0piKXclRs5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79044}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "91964da6b086dc5029091eeef481180a814ce24a", "scripts": {"test": "npm run test:js && npm run test:wasm", "bench": "node --expose-gc bench/index.js", "build": "npm run build:wasm && npm run build:asm && npm run build:cjs", "test:js": "mocha -b -u tdd test/*.cjs", "build:asm": "cat src/lexer.asm.js lib/lexer.asm.js | terser --module -c -m -o dist/lexer.asm.js", "build:cjs": "babel dist/lexer.js | terser -c -m -o dist/lexer.cjs", "footprint": "npm run build && echo Wasm: && cat dist/lexer.js | brotli | wc -c && echo Asm.js: && cat dist/lexer.asm.js | brotli | wc -c", "test:wasm": "cross-env WASM=1 mocha -b -u tdd test/*.cjs", "build:wasm": "node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.9.1_1632832271237_0.11403011225351745", "host": "s3://npm-registry-packages"}}, "0.9.2": {"name": "es-module-lexer", "version": "0.9.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "d0a8c72c5d904014111fac7fab4c92b9ac545564", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.2.tgz", "fileCount": 8, "integrity": "sha512-YkAGWqxZq2B4FxQ5y687UwywDwvLQhIMCZ+SDU7ZW729SDHOEI6wVFXwTRecz+yiwJzCsVwC6V7bxyNbZSB1rg==", "signatures": [{"sig": "MEQCIByiH7kY4/hWGYRvWuBgnRHcql1X+5RHY75NFbYhJWYBAiAvwXBOBIsKo75wxrJMNRtozTHC6sm4sz9gMv7d23u2cQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79663}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "2a168ddf2ecb898ea6542bc8f8394d2432fa1623", "scripts": {"test": "npm run test:js && npm run test:wasm", "bench": "node --expose-gc bench/index.js", "build": "npm run build:wasm && npm run build:asm && npm run build:cjs", "test:js": "mocha -b -u tdd test/*.cjs", "build:asm": "cat src/lexer.asm.js lib/lexer.asm.js | terser --module -c -m -o dist/lexer.asm.js", "build:cjs": "babel dist/lexer.js | terser -c -m -o dist/lexer.cjs", "footprint": "npm run build && echo Wasm: && cat dist/lexer.js | brotli | wc -c && echo Asm.js: && cat dist/lexer.asm.js | brotli | wc -c", "test:wasm": "cross-env WASM=1 mocha -b -u tdd test/*.cjs", "build:wasm": "node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.9.2_1633089084290_0.9986186168172699", "host": "s3://npm-registry-packages"}}, "0.9.3": {"name": "es-module-lexer", "version": "0.9.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.9.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "6f13db00cc38417137daf74366f535c8eb438f19", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "fileCount": 8, "integrity": "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==", "signatures": [{"sig": "MEUCIQDzbkgGgFjGw7/56g/yRUCrC2uZqQnZr96qxB3ikZAaCQIgKssWByMuKsadz6V1pv/KqHYLDQUumQPAjvTuJ8z4N9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2p21CRA9TVsSAnZWagAAxq8QAIiGn1/jyw0ANUHtcu3K\ngaXxmp+9yAnUDqaU0DGHVFgpxqBOyNbwW/yoJUPUEXE0PWKXVIcOGWpuU4mb\nAHW22Hg2CYEUGcBnLUIFyKvqqPlBfmwILYGSP3soDR8pKc9GpWZUTaKWWtdQ\no9kkdQIpdGZ+y/PiUqVhAqYKn3kRVy0M+zs2WlTZKXjfWqhfR+zL6t7NV7mH\nnMvXd1OF8MTydm37za2D75KuZrPEHsIIx1X7gZC/Ow2PA7iRN1KOUdvq2xj5\naQDDqKspwXSDSikIkdNLbGXwXrgCM95LemgFvT9/BGiRBH1EAOgWxDiCas+I\nAbgtLKHp663s5lMayF1vVRv94zI4vLSHEop8XU5IIXY/9OlHLQgwfQBp6nW4\nsbUXpCXL2n7yNsuBu1PQoU1ip5eM6frJqlbidextDM5gIOGEQiuFx5sxQpHD\nmrzT2ukLhKp+zuM/+L3sWHXcmaD+hv/MywGaVVtgfwaNPLlNxnL0IdexCVX1\nSFEc0aenOA0CLwlWvx1+oNafoy+pIyczjOWBBf9h1Sw4OsLjrErqIvmyKU5W\njfPpUIuEk/6dY1+sIL78bLNu0tsOf0VxX0wCWyWHMy/KFiAwGKUDCqhIrBVh\ns8me3T+pIDF/+k5iVeaA61+/m/DLaq2EumnfYXd1v0LnlcnjYPHzfMP39hem\nukcO\r\n=v7RS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "dfe27f1c75ef95d79652c9bf5cc3e5527f13d88d", "scripts": {"test": "npm run test:js && npm run test:wasm", "bench": "node --expose-gc bench/index.js", "build": "npm run build:wasm && npm run build:asm && npm run build:cjs", "test:js": "mocha -b -u tdd test/*.cjs", "build:asm": "cat src/lexer.asm.js lib/lexer.asm.js | terser --module -c -m -o dist/lexer.asm.js", "build:cjs": "babel dist/lexer.js | terser -c -m -o dist/lexer.cjs", "footprint": "npm run build && echo Wasm: && cat dist/lexer.js | brotli | wc -c && echo Asm.js: && cat dist/lexer.asm.js | brotli | wc -c", "test:wasm": "cross-env WASM=1 mocha -b -u tdd test/*.cjs", "build:wasm": "node build.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.9.3_1633645541348_0.532771177108013", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "es-module-lexer", "version": "0.10.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "0f63b13c74de15442f1897d191ee289887bb862a", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.0.tgz", "fileCount": 8, "integrity": "sha512-0fHYovx7ETE13wPL8zG/V+ElEgSeSwsgRVOvNZ+g/Y/2YyJq75+IEY4ZBr59vUZ3Voci1jBIktwpj8oODaWa+g==", "signatures": [{"sig": "MEUCIFDt82puSMBptIf9cMoeKrr2v3qmhmFMdyVOZoY3m8UUAiEAq75LZopn2fPWoUybRL0RhogH7PzOpKtbicDucD2q1+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFEtEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrP2w//e7nuZ+EelwLlX+vuR08VY2Asq8CvnI1Fm3PlU6j4AOmk2qQ9\r\nzefcV96H5lZGZq6Cuf+4eUK1I+u97nvbtEUqcs0fqsZjSPU88AQPQAS9Z8SO\r\nExhmLpaaMdujwZ031HQQCHfetDawP4V+zfEDX3jHldy2Yh0Zu19Qu0vazPxf\r\n10r8wD6mlhNM1EWLKG3qF1PKuoARYKtgILoaiV4A6PeQzoGZL1TtuMiKVXYg\r\nlztv8N3yLKH2F6m8Re/mxy0iA5l/GUwJDM8Ciz56mL/15/BJxHI78lMIT+MR\r\n7wYGDlkq+dPbn5SDag4D/+Swhn36U8BKz6fe5PNYTEhfWoN1a1ptDW4bhqfB\r\nL+PUbyyRBL3uHFG12Up1u2JQqaZRqB+haiG3qUcXChTz6g3GClTKCKsXRKkr\r\nlJsU1YDdjCwWkQe7nJv1wZ6jv66bDD2sp7Gf2f28xVA9NL3bBI1WHQT21UCn\r\nVyTsOMjbSmU6aKUFio3kBBek+2Tyot9wDD20aVb/cNLDSr6OYDfKpGDYC2N3\r\nwtNwmY5/YnraYjSnFVCxbIMhlVCLyv+mh0esRu5qFq4/Fl5gZSXkd7w9ggt7\r\nxTiaUrdGjhnP92FgLgVLncH9nGIyV0S4qUK9eXFUvIwCzDWgNGUnNHBea2oX\r\nuo0GZ0IMl9N+xhbfjBdff4GFeBVHIekNdVM=\r\n=+Xod\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "15329ab29b339071fbe23ce0c103708efb39aeaf", "scripts": {"test": "chomp test", "build": "chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.0.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.10.0_1645497156729_0.015079029656296372", "host": "s3://npm-registry-packages"}}, "0.10.1": {"name": "es-module-lexer", "version": "0.10.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "69d93344249321996ef9e300fd8639325fba7836", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.1.tgz", "fileCount": 9, "integrity": "sha512-7zffMCLzbiuuRjL5FVs4BEIr6j+Istfrbwzrwg8de4ep1kSyZNJmmq5RQzyqJq4YhvOmAxZ3DBsZXDO4I5N71Q==", "signatures": [{"sig": "MEYCIQCWxxCzvXSF0n7PbzcZZ24Za//8hhltvDBbBw67+448CwIhAJcPyMexVDop/NQ94VBZgs3ugbEAM8OCNAy6RLMidyN+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMh+fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpblA/7BIT1mnEfitIhmv3/nX/6yCPoTnws/nxlwu3GQ2Eqomdo5hjz\r\nemOhDTs1VJIpPCa+0Fxc0T1KjufyESExLmPTkWhlVqYLXvG5c5KYOJuBYvgZ\r\n3gmorwGsPfNUGefaJt6Lix0nWYaUEy5bwQQ966ZaCoT+JQ/fySWbkeAiYwu6\r\nwyQ0A1ps/HuDgsIrLjqm85CEVJ/1OmZRbwfRrdwVCJlGwKSBR6n+KweGS8pX\r\nWK7o5lIaIxUw0380h92ClurrDrOlB2LxhZ021GybuYFaSXjoh/zug822ix/5\r\n+mZN3ARCBfck6PjWN7IuSmR3Zqz7kgopg2UIPyqMZrd/aARVhWMut5YMs6Vt\r\nJ5zzDVAjDKjDulAJUJz6do19yUsphqX4WUS7PZIO7MTLekaoOZf9BU0dfkNx\r\noSXQ6TOw/7U00rk8+TEhCNQDPUXTXkcGGmC3sIyid6kVc/0FMzycGehHVT3t\r\nehx5jl+y9XXa0rL4o56XLgKeOJzpDHa6fq3E+l6I3Zhy87rRhQIwhYTDwqPf\r\nNZ04rH4HA4UzXN1tLhYjVLoKJXiQ+i8bX9AVooZdmGbigP02iv+0bxnSVXgW\r\nn4PwJY1WLAN4mMnRWtgb+55PiLjVpSamACvDKYDTtAR9VHeMoSUfNkCoJJ1P\r\n7TudW+8auTrMkClvv+jLkJ0FdWUDT/vAr/Y=\r\n=p1Vk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "3b11f41aa1169acf13fe70809e4bbb2d7214927b", "scripts": {"test": "cargo install chompbuild ; chomp test", "build": "cargo install chompbuild ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.10.1_1647452063296_0.46169187656031174", "host": "s3://npm-registry-packages"}}, "0.10.2": {"name": "es-module-lexer", "version": "0.10.2", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.10.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "57e272f3fb7630df7160856cf68069e3cb60fa56", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.2.tgz", "fileCount": 8, "integrity": "sha512-Yscrk6aIG8IlpN+trRLuTiSI2MSJQELnG/S8g1xHnzoFZuxRhcp+Udx2ooFu1h/EIPyQvLqLUlcgivmxALfA2g==", "signatures": [{"sig": "MEUCIQCYnVXhI/DdAih0XGfTbzAhIRlXp1+SlPlpVPJU7c6pJgIgezl13WKzShmyRjhnzMA3TkQL53aKXwpS6ul+FXsJztI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMwKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY3w/9HRD8/K300TLSdC4QkgE5i0xth9hj3CL0B29pp4KEkTmuQC5y\r\ntYNVFlfntDvhpdbkv1jZT+VgjcEJUnlvcZ+rCxIGbFHviPmEkS7JIQxYOL36\r\nYUIcOQI9BUr7LQZOwRap6jzr7EbWzGn0ThAKm6FW/jjqKYHiwNhVkRWoXCEc\r\n4+V/3h8vcLn9TGo711eLnALwGdIfqBSY7cu22Jx8wE80FDandi4lelEGnwej\r\n3eFyUaFibUF9NhCH04Eq3/YqDBK4BszkWYbvV4YS5/tbz9UHpdNIC1J3MQod\r\nQCmSInx2KPu9NEt6SNDqf42r9aBjR8S1rHyxsi3d6afBJr///ZVAioiswogz\r\nIi70GqLUUc2EM58Tksvtawijv5J8t+zzMDDFok/vk3U87mgjcc2hp9fTsZpI\r\nHmaBR1Q1i6bullinFqrqEGktd52wW0PSYqvVQTBJF7lG11txoiAeq+eKTwzQ\r\nsxJf+Bt3xh7DZVrhP3br4GwUa5udTjUC5is7xwgTMVRQ6oYLy3WHQEHgGoUc\r\n2ZNOks4PknwIYtKg8W8alE3fi6VlQeWx+cHVYu2DJpM0OjXlrbt9V/uPFwuR\r\nPJUmy32Xz1tWjmOgOwqiy4Bkp5ONKhb+kV3/X/hsIpQRPNtLNjwuRCxiuYdV\r\ngxpeOSvbzP/oG6RNrzpghSwcXelsyJJIoKs=\r\n=Qr1F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "3ddb700b1102c63da32d469af7d05011e92d0281", "scripts": {"test": "cargo install chompbuild ; chomp test", "build": "cargo install chompbuild ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.10.2_1647510148473_0.13767504540446018", "host": "s3://npm-registry-packages"}}, "0.10.3": {"name": "es-module-lexer", "version": "0.10.3", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.10.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "666265887483311a37b54099895005ce58e18850", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.3.tgz", "fileCount": 8, "integrity": "sha512-jnqVQfKvbZUNmgj6l8bsM2MhIc4vjXJtaM4X6p2qYqe0GnjSpjeuerJqvUhMbcC0rJ3w/7hNT6oqLnmayZ7MkA==", "signatures": [{"sig": "MEQCIGBeGAPJu8nzQrvp63YsRWS7eIc7sBSpUyKC/eZO9FpTAiAtsjpFx9gOGXJnPhVRAL7/S4Lk1owyEVqk3DRfg5Jmpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMwkAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6mg//ZCp1Gq9hEtE5hVVZ7TGq1WEVjG9AyS19AytBYR1g/PjVoih6\r\nvnJMAr2xOFAwVyDZQpxuvTwsQuuFwDcqln4IJ7Wmplz0JpKN62NMvuAcPdvx\r\n/lP6h6U3osUPlh0MvEbNM0Zd1GvD8Y6W1+PP/xO8jQRyndR/FhQE2bXQwkd+\r\naE3hCgwRGekhfW6UvDebIdwtolHG3tFoXvgGyZumrmRWYf/7jqnKdOyhC9xp\r\njPyHHQhmY9OjkYYkNc5kZQvgJVfpLHV8RMYV4tbqHwWe5bbdyqWhi2DuHeSY\r\njnSz1cxx+9MeCkeUOiShgDcd9wtIJZRLfu9UiPd/hiCjnUuRRmIlyjzZRsMp\r\nDz0T+/cmd5qK2m7wEN98CHhnUMGK7RyLVgiTEAyF7E2A5wUZ+e4eEvhKobrZ\r\nW+Mlq7gKFewl4xXFMOqzzptxBm9+ZAzOTRAjGVHpsAJ1fPBJsalHs0VG6V+F\r\nZpgkgNH0zAJOBmgmgCKlnMIDiA117s2fSK3n5q49xc6AnKEBWjXes9YUTgLq\r\nf1/6q23vT10sqX9o/Ew44hc9tS/sfXbJhOfWALEFTgi1wSdJkDB+QVDTMJ0H\r\nnMjxtRlD18mUv8jw3HaAOd9cdRHJ+CpK+OyxrxXlCWu8jIObRTZf+l9rbFBY\r\nPvLMLucf3edHn4SYRKrKRvUzhUxd7w4YLAo=\r\n=6SKJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "9143b7f32eb112d084262e74a4ce6540006956f7", "scripts": {"test": "cargo install chompbuild ; chomp test", "build": "cargo install chompbuild ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.10.3_1647511808406_0.30737702866099803", "host": "s3://npm-registry-packages"}}, "0.10.4": {"name": "es-module-lexer", "version": "0.10.4", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.10.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "334a61489b982fd8b535ba6ef8eb357fdc3990fe", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.4.tgz", "fileCount": 8, "integrity": "sha512-n5bOGUnrmuCKyMkmHNtC1ObnUx8AgFcKWe2mbxb6jYzuK81W0Rk3Z//sCoGJuxWzos8R2w48TemGIFqZsTY6YA==", "signatures": [{"sig": "MEUCIQCcAWVLyIt5Hubg+sHB99l/8n8m4zPCKSrY9/hLeoL8BwIgOVFV7sCLYaaZI7RSDBJcSTGxmFAEYp+qVHgtUFvga+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMxJJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqN9g//UmKk8zxNr7np9+hQLClose9rJ/ibmSoPuiqzf4u77FvdStyb\r\nZB7Scz07TbHE9mXM8+5H1NlkKN6avw0a5IQbIErPn5K4/UQ+6my5zCLXjNCB\r\nCnepLbqqul93ZHvddHVbkfILOHUQ12hWjiLCnt3zMbHkZLDkeBKtU64Uh100\r\ns1rEDOiQrZ/NrUCx1Df5cBLpizatFGwS9U0ZNM3Yw3HBFyZqCP2vO4/kw96w\r\nB3s9NM1uD8lLPTI4zgatdcMK/bBEllbFQ1b82x9K7PAWxYMaudLxvKkGaY6v\r\nM0Oc4kBJ6ltu0Znsj9rWptmiAhI8tGITjGTTf1k/biGoq2YHTkbpY6D0rcp7\r\nl/oZttTKIIwcn/ow9BquqZzhxg6kDbcydqh93FUqD6VwG7773GJTUdJs60I4\r\n86wR1h6UEH/QtLa9LR+VHgbUhcZyTjAqEJHodeWPegr1Fi6MzFI8QDhOArwb\r\nSeQt3VEXUqZO9iGEnUilj4UatfubMN/Eyi4r1mqlYVKz5rKMfetwkMWF/Hs5\r\nIlYxTvXrZtE0GY2VC1lW6LD6vxxPWvQhSGyf1vEH9j4hkfr05X5hoeBUHEWZ\r\nZL7X2TIUZB3SeqqGj57nM9mkZxLUBGVh7uH/OFtvu2h+o1TMtwv9ZSCXRXtk\r\ne1MVsYGUcCMpdHczPDItZlkpwi7GrK4CZls=\r\n=2gJF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "303db3773503d9152460178bfdbbb325338d6523", "scripts": {"test": "cargo install chompbuild ; chomp test", "build": "cargo install chompbuild ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.10.4_1647514184756_0.5904463706116674", "host": "s3://npm-registry-packages"}}, "0.10.5": {"name": "es-module-lexer", "version": "0.10.5", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@0.10.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "06f76d51fa53b1f78e3bd8bb36dd275eda2fdd53", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.5.tgz", "fileCount": 8, "integrity": "sha512-+7IwY/kiGAacQfY+YBhKMvEmyAJnw5grTUgjG85Pe7vcUI/6b7pZjZG8nQ7+48YhzEAEqrEgD2dCz/JIK+AYvw==", "signatures": [{"sig": "MEYCIQCY7WjkrKfZNIOLLKcc4gJZ7xTlFmVPzZlvSMVywJGPlwIhAK6bHq/YTUymcXZQD15OkmiHdNkiY1QxjQyi9EG3XGX/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSsrsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/cBAAl+DcWIzKTkat3HMz7mExmFH1mlZFW2kpdZGRKDCpYIItNiBp\r\nu6FoZhvLEhuqVK8PlmHWxKuvMN8Wr+SR2r0Dj1FY9WWeQnhMCecqH3EvJnBb\r\nSwmdBQW0XmOCXvvRWB4wnL3W95Hmic/b2RWK7nhfN1kFaJaR5/yHQSRzQ4rT\r\nlAM+oKw1FoQnSQFwHeKA7PwgwM42R8Wg1FEuXUBPRGtK6ENUm5NWHD8Y6rVN\r\nd7nOdz1pE2A0P3+CNeiTsOu26S1kMq6AlxlDYKnksSdGbOc+MHEnlKRa4RLj\r\nhLUrBPggQgW8PQ8e5Y1KbNVNFdOs06ZxjukmjCDp/bs034wjKQ5eB5RjuqGi\r\nNZtt6emGpZn70GOe6Ck60UjhTUAOIlrIgPcaw4JePjTIjKXA2twPAVA6MWnx\r\nBN4OfNpQHS9L/e+6q0HffeW2Ceq2McOQ/yug+TvBIPSn2ReMCcsWHe6uE5K7\r\ngxDsgmqtE+exRysCWL/bZ5x/DKFK9pwVs+3U0oei5GmShz7Iw78lbtF42Muk\r\n2dQylyxHHxUtTokJNUbSmj7Xe4WeC9s6D4q8h8Ol5V/dQ55H9EkXjHd/teX5\r\nOa/3s8Vcmb/5p4NSY9sOazCi1WEmRzcxsOfuv2F2nc0Uv2O/PU9mqlSyi7DX\r\nqIV2I81wYo5LmtUOuatY6833A+SaE0ug4cA=\r\n=ygFc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "2c17934a435fac7d515b29fcc022f3d61f973a84", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.12.1", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_0.10.5_1649068780072_0.7020479160237318", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "es-module-lexer", "version": "1.0.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "6fd5541b6d0273a2807c5c50862aa58834789fc8", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-+kmskslegYN7gDGMT8RDGzb165q/I93XxWqH2v/YwS8IKWMI6Ts9EUNmLFVGmZg8MBoaF3Z1dVeeFIPRcw8ISQ==", "signatures": [{"sig": "MEYCIQDYdLcb1KS8p0yF9DO0H/XggZ/Ei9Oj1nbpnjsWhsn9RQIhAOm9BmKCnRUYYdZ3KbtEFbW5TYCjqNKeOMsY1RVe8whC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4J0KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrMRAApJZ/0L5ZgrXoRvn1Kn8YpTysdRoo/n3LI5Oa+3jTQFyw4RAr\r\nKfiFEolMb0kcBV05YqBFg4dDPcnVf8xiO+MfZpT7miiJ29EK8aXEEg/8tOM5\r\n5mFrffjt1FlT8dD6lsQMg/8Sj7oD21/utaSvaPXidx79NbPbN/hIIoxINq4/\r\nYwbo5Z5LTAOziIfmC6tPzpR2N9BOWHp0gSxJm3iREGFyPmBhP4Xd27OXSf8w\r\nks10mwBtdyefsh6i/c3ljRuoZqWbFgvT2+6ILf6MGmWWMshVUIrPkrNZRz4H\r\n7bWYIVK+eCetONjkEN/4cJmuEnfUyS0jFLcEf74jpiFaiv9gaxBwXP9Z6Uau\r\nB9TUf5wNME+OT6wj5T/uH/ADqBDb9WNc99jns61pjYuZcCOJZqWBB2hT8uar\r\nq4chShR62rxiSbOWKNUJoqdZI7pLaJ9mLtgju6MsS01MJ9cuyfjeb9AJWTAd\r\n1mhUzppm4xKHkFAhV3kAfGt9JEeU6AGmvQxk+J/68YxOzLokpNr/TCK9wFJh\r\nUuzkIKbHNYjYthNqQNP/r8sbODKOJepbryxDhL1vjkoP0ovZK5OmO7VE307Y\r\nCnM/iYO6L3BrW9546CUlEdjpMbLlJ5oPHp5QNy4db6gspGun6+CQhfKOtc31\r\nTS7Z8ja2Ty0iReq1rmhRX7+nB0Y7Dx4b4dg=\r\n=YFd/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "a7ab9e6f1f9de972525bd20a97d63234a65c006e", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.12.1", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.0.0_1658887434505_0.06547139485882325", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "es-module-lexer", "version": "1.0.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "8dcd50a141274b31cccfe8501840182a6dced7ac", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-7n1RU7PnPxQm5uCuFoYkrZMUxnpTUL62JojuLP63g524pk1LO2MLuXTQ7I5I9hjmPGdft7gmpAiyl1feAqi6aQ==", "signatures": [{"sig": "MEQCIFQpP3gEZY3mEPy+Mu2pe5nbuvxCQi6BE4e0B1Rks3WHAiBt3KxEGyIlNGJEwPII+GdXOpXY8hdTcupiynpfLg6muA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4etFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZbg/+KUlx7ISYZtpIUZbCPJmSYov9z55zU6NqU+TyhyVfVjkb3djJ\r\nTu1JGrISHrYWdTebGLuIamx5ZvITforJRHY9TBupUIvNz8OMazz2a/Tz+1B/\r\nKU6EoM3qDblf2uh0WqWELYU28bXFkpMPHwZ/Jvh6HHnDzPLVBlnzMlOghAX5\r\nbwHt6zCO9TlcdiRRBiiW8hS+7tN26d52umffCajbXOtIZnHqezYdc6YHZrlC\r\nXp5WZXY8WTyQvcdWbzBGMN955j3TMrEyscvv1tSuA0f/Y5rflQkG4AgG84MO\r\nT6AaYP9tINbBIvxMWbxp7FV3pStgKF2mdM0dzkuEFrbTkrvyn5IvmxSsuc2U\r\nRSOfSiv85gWRHRnmXI2u31eSUHcpQcy1xDV/8HIARnXMocDxeTVApDoEfY08\r\n1P+yr6p3TqcqvZqnYbxmt8oWnX2xPTiVVSExtB1Kg1nL2gOtAjiuNPaIg4Qt\r\nfnDeGppwRY9CxHgIgOkr/T16VHB0IAmITS0NJcIYEx7O6G1vICCRfiWUqDZB\r\nkHHmzEFmnOtp1gfVJvy+NltX+cdqfbg9VJzPA22V5BHHgnpx3uR9HQUJwYZQ\r\npGPBgSbdGP1BLSSfRocXTUi6ZgyvgRvM6eM4UBV6qKwFdNSxgSTDTNoWAsNY\r\npwL4mEOGLf7PsOvbERqAi8lkPxdCOXAbK3Q=\r\n=zQqJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "a28c3668119b538e18c459baf14e630bc27ff118", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.12.1", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.0.1_1658972996969_0.7521808642721621", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "es-module-lexer", "version": "1.0.2", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "89dd1355d0e8a741b431171c69b2bfecc47d4293", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-W6d+UibP+MDkF0+uUqj2sZTiyqA6v9f7/hDPqrxNYntp+NFMilA9Zr+qF9IZ7F4lcHQqw0toMlwxJxY2qm30uQ==", "signatures": [{"sig": "MEUCID7l3xInQipTT7x2tJs5WXG1/IQJSnowDpBf4LEFMPzYAiEAm2HSgGYvOiXSuOK/mCeX6zJeKkIcQ3pORRfI7AiSGpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi610gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqog//faQtXnVEKNuFivo9a9r7VqUjMMCOMyDb6UnLY5g3WFoQwN16\r\nvIB2PsmL9S8Tm9ujAW11IKunX0qVbIjBs0Kgy1747zDj+Ps9NRg4fAuIEki/\r\n32neJ9EhXSuNJ+kZscDzVBtiKCcQC1oaTm1nk022ZqOtMOsH1h53Ibfm/kHZ\r\nQV6g4Rh5GlfPXDrmnKYJK6H76YrP2cAwlU3oP4z+KglDTxNtAhrL77Q++KgQ\r\nRtfs09BwvQ6mxdReklaNUxfRtQH6NoNW0RQ7ze38G+ukx8/JB+Ruek1uJYbp\r\nMvfGBBzYCid6MbSLiw+I0HAeKvVWNGAxQxwPtVozi0v5B2yKajcGAfb0uHQ3\r\n1pu46EWhqw7MRyd3BGOQR23TRMPotBasjOXefzhhtopc+vVTm9s3Uj5R/vD8\r\nZ5etpOpDvNHuzT6MJJ/tI5WUMLB8JZeBPcdY7plOhTY6YZcQey+TwHb/qKJ0\r\nRC6JQTo113Oaucmniasy57RwadTeCyukmY+y4S+iICh3FIIRFlp4CRiR6qV1\r\nWo6owdD13UwBypMz4eGqs9rQssHLJNCJgdwkfdrD9rTWHmPHBOr1qJJ+S4fs\r\noCZLyz+mwl7tpj5VsW9FvzlLWw3d89xhMTjQ1fPdPOgKs1C44z4De4RMdQrg\r\nn7s7epxeG2UwQvqv6sXBhfrPZxf+zFglesE=\r\n=NRjo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "19ea3c4482f38e7a43e82cbc3974879bcf006b05", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.12.1", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.0.2_1659591968294_0.9934051597356179", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "es-module-lexer", "version": "1.0.3", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "f0d8d35b36d13024110000d5e6fadc8eeaeb66b8", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-iC67eXHToclrlVhQfpRawDiF8D8sQxNxmbqw5oebegOaJkyx/w9C/k57/5e6yJR2zIByRt9OXdqX50DV2t6ZKw==", "signatures": [{"sig": "MEQCIFqzCs6ogTNJG16XrVY7N2q7+KKhlCUx5P2/gp5XBH11AiBzIYvxomE80Jqzi4eOvSzyjuusrfajjhztGBuZQ2JRKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi87bQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpnpw//WEoCKdRrVKyjyU5oW4nSLU0YbCIRfpoIWzJsN8JabIzYwfi9\r\nVduAb5J+DYk0JH7OQfLcctmKvLVpeX0QOM170I1wu40dvn9POug9iMZ4gWTs\r\n/aIP1bK8TgDo7DecD8mYQxCrF3v7aVSgdSyaw2rJ5wXrTeeh9eLh0vvaQ8h3\r\nI2+mdD6RP5T8E6YUX5BRJeq8LJXengstaOmKnU1y9PUrWVk4QYWw5ARHI0JG\r\n3Iv1zu+3Wlk1K+TqOAoAHwpW9clzu7Jsb4aTlS7vaBE8bDKIbICx07EMmBdT\r\njGuLAQujOjxm9F7aYycQLBTfXptDx1VQW76s1r6HdYTTDBU3PBbZfx1G3V5x\r\nUn2ig5ppsS2r47x9Lih9hQnN5HSIWNZkJHbCglM72UzPCQQy4aKSzqU/Qcho\r\neG8kzKW8+E/KoU77yFm3qRt7IEeaKtOdlDtv2n9ggbjhQiPijZYQdN9gmaoM\r\nAY7m9yHGGaoe2LMcQnX67o+mu+q5hyYmx1m/vYMZPPJt0hfJy2KtTUtF3QCk\r\ny5odL3YXSw2uWv4E6xu/IzImBLX+fgtouEib0poWyr/PLwNAefpeTYmJD2lP\r\ndMG+Dr2087j5lp1butDTnYhd6Gyb9ctO/Lb+agcg6kbdXpXkJub6vIX81EY2\r\n+4c19DtFHtuaZW4iBL10mQ+vWqoKLDbWrEo=\r\n=w7tl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "427efdad265a477073b569a2a3b61e08a2795d61", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.12.1", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.0.3_1660139216135_0.5595862462453489", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "es-module-lexer", "version": "1.0.4", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "c46ae31c25fc3782ba3690e02212bb7b7ceca9a9", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.0.4.tgz", "fileCount": 8, "integrity": "sha512-afLYSz1TosFPpiCC0bs1k6reVEOXw1e0ZHHMq+kMcqDL8qSCi4Iw4XFcIdtGf+ojCk3g4qqsJfUI5ymZYG768w==", "signatures": [{"sig": "MEYCIQCrTvPgDbASxl63vxnbyJG+wAFhYbso0LBOgsWwku1IVQIhALFlgFtPQ/pjZs4cF+vmCpGpyoENMDy/93NA22sW8Hew", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSCB5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowCQ/+MdKQk1rxf4AA1GKaVR2XTAO9gpunyqkmmULWDxcq8DHlRhpp\r\n6TogrACSnBEyUCY5690QeYed0ON9W1D70dlodqlr4DQMduIOvESjA8V9T5/7\r\nefxEx45AOdilnqb10W9HqZynSiP8uhlj4QSUjHT5lg7QnMSAaYA1rdfk5lBv\r\n1EKLP0sSGakV08xSLjxcogcRqejg+s2kXw2JL8GLFLVjIzyUhdipW0/RUw7D\r\ny9jsqaKiA7E+t5gjX/vQmQ57L1A7nRDWuqHpfDgxUOilJbzvFQgKW+E8U/lL\r\nfG6Zk5PLio+cbtJcPg6TkXI0h50G2gj08f0WSbaeK5wQnwDlE4+JqAyuby0Z\r\nsMfdYQrYi9OUhidnl+Os3/5sRfzsufYHquPd8itwE+Pf3KxMfVYxMkISUez0\r\ns3dKM72QL4qTGrCbGgR9Z+KJAmuMs6rvna3xuWlJvEQruCk4PrXTyO60KURf\r\nNWwK9xfRzCJkegcBBIn7DsDJYFtS+5rCpOeKbsZ6V23D/2f/BYpm25ytly5T\r\nPPuEI0aW+z/p0mC/N/4oQefdEIXtCUD7cK5y/ztZfEjRmmC75kHRvzF7B5Hx\r\nuJ9d0/aqv1EubDMqhrNW1mqVOOzmEtMQHEV/5QChLEEyZHFJOqEGx+bOGQjl\r\n9itMhU675IaIo0aX3dNc333QERkkOoduWEg=\r\n=ACIc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "c3182898505689eb78fcd64351f200bc3dea886a", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.0.4_1665671288830_0.07913576844215853", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "es-module-lexer", "version": "1.0.5", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "d6d9de310a5e11cbf73b39a1d6f79e5c3df4d06f", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.0.5.tgz", "fileCount": 8, "integrity": "sha512-oxJ+R1DzAw6j4g1Lx70bIKgfoRCX67C51kH2Mx7J4bS7ZzWxkcivXskFspzgKHUj6JUwUTghQgUPy8zTp6mMBw==", "signatures": [{"sig": "MEYCIQCq2T7VMQG09/HsizJPY3wdGfw8RzEMd67Um9J6F5KXzAIhAIZ3Qrv+QObqjaDbj2QVq5zTgsrH/3uuZoczbA6+ibaB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSb9EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrhxg/+KYhST1oqI8Y7gEtXKGUbUAdaWJ+kFliLc3atK84qpKAJ9qLn\r\nxYo6gDYXt73v9UZjtAyXHMEaeojJweo4sHQscOvGq6dMhBQP734m/fJEW5HR\r\nnoGGsshRcO6Y6cb3qgYIs+P/Z9b8MsnSiPc2IlZ84aegURmLZ8Ydy2SuTnb/\r\nK+vhuY2yTSyhGsY1/6+VAgNa0yxZN8EfUL6hUc3v+kJGtkrkaKvZrKkZez3M\r\nVTofcswJafyUJRUb7sltizBbN40ZqsVB1224PBIdPXa85+ySGAxlO1sa8T48\r\nRR3zg0cN3x6wnMSiI6okPgdYebuyo1i8wzJeDaGX+vcztAhw3+ixL+lu4OoN\r\nW/Z9S+htlgmtT1uecwtENTB0U9oUUYV7L2vP6TChrCWrvTRl/vnRwAdyrJUK\r\n8rX1pUCIAIo96/ByLZzahjdSrXcSHIiaqVraLkyEbDStqO/t0AOcJBpImPqn\r\nl2dOV6zJoicQ0KJCJUI/EnqF4od82/XGx3O5xCSMxDZM8ZOZrhAT7LZSirYR\r\ng6vrSpVGnTA/Gu0g3YTq3N5+ubII6Vm6m66Ni7vbUiQNCWiTBFnhHlkgCTu6\r\no/pDSY4Pq3YXkobS3F9zLvCE58ChF8qp3wuP+9/+mg1lJaHVrjoTnUpBGIf0\r\npfiRek1J5/omZmmSJzJkeGg6sAzuY6d0UR0=\r\n=LWEN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "a912ec6a93690be992e86246d67a3bc42f5470ed", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.0.5_1665777476351_0.7200735960778908", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "es-module-lexer", "version": "1.1.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "bf56a09b5f1c6aea6ba231b0a636a0f60c410b70", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-fJg+1tiyEeS8figV+fPcPpm8WqJEflG3yPU0NOm5xMvrNkuiy7HzX/Ljng4Y0hAoiw4/3hQTCFYw+ub8+a2pRA==", "signatures": [{"sig": "MEUCIQDBrIqs/7dlGZNkqE+mZFk7zTGS4RzSOGV74Hk+wdRywwIgHhYuO6vgYWqV393GEr1OQQjD15G+02hwYy4zBbFlovk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVrEcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqsug//SnOUfP6JNVAMPycJPBEQbZwKkcQQpXdCqTJl/Bt4AZUBHJN7\r\nHIpauqvKghKgQ8Bxiuymrtr4DGh8rKhIb/MpeLjqYvUA2+I9CTR5rbgdrJUR\r\nqTxVFPAjRLtRyrAxeU+yHwRM78PaTE7FsYSbeE/yjNT5ruNidmfFjwfnts7o\r\nSaLDwrVhF/IGIxbfdLjvR9nFnuV8TJgVIjcX9fFoBK6THAdk9w6pNRnk5CzF\r\nPJoJIE1fZzE/+mMtUiv2yhk7c12/qnN6rWLS/1mWbYA8nwHzrXlW8dhDY7qE\r\nRz3dohKETuhOd4cmhEwNR++wd5ui2E0j+l2x6e9+cmL+SAEdqw2K/7PGfJ+/\r\n/h9UxnDExdbBQ3G7BrXoYKab4a2X7Zn0yQoj8J67Yd+YYzCE2/9DrcfTIVG3\r\nDT834ZYUYfAmvTkY1cL0VF0DLom3UNIYCIXoOy2f0AjeJ1oLPlidE5xdlmI6\r\nds9prDmkbGTIng8h9wfmkw8IQiTCbyb0SXeaeIQAmzqfimziA9zPzLb/rSeS\r\nj1pK7oAtmxAFJw8Y5co62Qo/7wouHJh4TPbx10wkTbq1AsKLguXzzk7kO41J\r\nHW9Im1nhB9FqMbl/2Xw3l0K+h86yMYceGeLi6TM6yIrsDH3AyRvt3xIbDmiX\r\n0BOleg/1Kel42PaRyqgfeb1WU/ZPqVr9GH0=\r\n=0sdj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "df0dab401f7037f40974a13acc62719bc9cce69d", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.1.0_1666625820330_0.48286639072232695", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "es-module-lexer", "version": "1.1.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "d4fed363dcd94bf12586266f7b80bbee6d80ce6e", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-n3ruqU8Te7I5prBd6d0darM8ajFuVNhLWvgo04hN7goWSaSrxe7ENOZitac7akN0A2o+8fMomBDsNPvW/eE3CQ==", "signatures": [{"sig": "MEQCIHNu+rRltK3IPh12Aogal4SB2lccff4Oane7YkOmunGHAiAuMPOrcR730DT+MioXpMat7S8VA5jZF8WRzCYTu9RlqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4DK2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkYg/8D68CDM3x+dkbeG8hQEBXXq0pNlI7CPxwrKeylaUOHWAzxSag\r\nPkFNAkipaxMoh/WiToPnmQOLcous+9W/tUd1ZrrbNKwNVvLuhBpsSHljF01z\r\nT069BjLhDy545kxEC/Mc2fsBXn4pkbNU1i7blC6BZpPDIRHp7h7k8yJOMK3o\r\nOvrHFSK+HR+r2j+5CvR3P1knn0eHOVTfLznydhMj3EPpzKQDdKibz6VcqOTW\r\nD+nFdwmzmHtu22/Rb97++kJQKzkQq+IQYL4XxwdvAY4Rd3nVSXx1iT/4wwE8\r\njdQRiGvD0NDae7AoKr5RHxAUlh1vHlnyPG/NvtHhbHHLnnu5v6yRjXCSs+tD\r\nqyrmW3VyNtZYYzcsJxz7hf5IYZV1qovyv5Ju02kLIeDH5cL/6UHVpTTTpq6m\r\nuNS01pTpzJYs8rdQEtyNVV6q3zKgHoFiiVtPpBIdBTxNhC3p+1oOujlM4Dpk\r\noJnX+QU71mDPNQFRcmJckwOcAT5K01F9xwny7+0ZtISVzH8kcsZXk2unQ7ms\r\ncMl+ARUNyLkwRTzvQGNRQRUypZJQHjOlrty1y4rCUplcHJQc2LXTBl+VGboT\r\nT3Ik3lpK5or35itI3uxfNoFXzGIPRPXlj0zFzSpdlkxRgpmNDTk2KQJiU7R+\r\nN6h7EUCPD02a+4SsMtpB9CE6su4p9/0dPa4=\r\n=tpOi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "82c3db65e787e9ba993f4a1cc064bed8ac60dda1", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "19.3.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.1.1_1675637430248_0.7299073737951725", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "es-module-lexer", "version": "1.2.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "812264973b613195ba214f69a84e05b0f4241a67", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-2BMfqBDeVCcOlLaL1ZAfp+D868SczNpKArrTM3dhpd7dK/OVlogzY15qpUngt+LMTq5UC/csb9vVQAgupucSbA==", "signatures": [{"sig": "MEUCIFHtrSLwa/TLTI/YoFwC0BRdyTWFfFZ9K8z81ckKRN9UAiEA3TkOKnK+WRtkzfF/1gOh3h6pjZse3D94/emxOJdIGtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj977oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ/A//ay/Dg/6DB+h7L0d/KbXLs0cDDocNIpI7onwiDRdMq0bopVXP\r\nFBJPUg6lefmqva/WwZYdDoNIURMKPVgG+wfmNioN+XF4GKqf+Ne3X5W/j7Ca\r\ndfiuufPUIvdl6Y3y4CG9bIUu3TODMt+e3o6zQkZzEJehNtFgHwvpVJj/ndEX\r\n1862s+pwHGmSt/bHl2lIYb+QGomd2Er9MnytDelg6p8jVtyjeVgoHnAdJPR7\r\nmB/o7jUiCKPTgeBNyWwGeJ2zxD4nbHzR1uT3PVQijwE5iuXI1VQ+3pOiL5nX\r\nNmb8wiLvzU/gzOo5zJGm4H0OjYsQr1610gVkMjlxvJq1r+hRPFKkMHJDTPWI\r\nm7MB960lTox6hsZ7TsQDD+sgbL7REcc5LFuYjPuMhfnPG1C7MpYCU5l9SyEa\r\nGvW3Wz/TSLfURmE9U4xrXDOvqRgNCRaRKjmdHbzw6AB1OSRDrQTqwnICaL3f\r\nkJJbnEEiimwEm2ZzWdmjdmKjyKFJ0yaxF2sEZLBnykjGe4pLhy5HKOWiKhH0\r\nwwQGKgDOKICvSecuXsYJUbhhwJHExV+r9XxDYtKQF5r0RIQnGuuq43p8H8ue\r\nHuJ7gIuiHcAEan0xSpHbya2sDxoWevEYX9b1nCnUYAlBmanME44v1VG2uQwq\r\nJuwwXiagXXFGEheB6j9GktGwJGy01lI8A9g=\r\n=saUa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "b4e89f6879d02e08572eb534754ac7bc384ac3aa", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.2.0_1677180648666_0.7161183676483425", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "es-module-lexer", "version": "1.2.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "ba303831f63e6a394983fde2f97ad77b22324527", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-9978wrXM50Y4rTMmW5kXIC09ZdXQZqkE4mxhwkd8VbzsGkXGPgV4zWuqQJgCEzYngdo2dYDa0l8xhX4fkSwJSg==", "signatures": [{"sig": "MEUCIEUm8CTYUUEO/M7mqzgArhxxQ+63im+s5oNHYSpHN9W2AiEAqTyfdQ4+PVRbke9l1oYLzymH6yF9SlEzoADGc4G/0FM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIO/WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjNQ/8CFtHDSBvILjmF0uPEKVKs3Lc0/oOMRs0061hCUtME2UNyOoH\r\ny++By2XJ/RDRM3i4aQNbda6J+PqRDmdodHFBHWrbCNdHpudX2oBspQKjzgBX\r\n2+gRjaSftOs85kJHv4iD0mIfNX7BkFtRlZHzAkrxbBoZ8EuUtSsG4H/AQtcJ\r\nvNKOlAQhBMZoUWZU9/arRwfPIfJf7FH+DC0dzYCd90kfVrjZVcPfI0UD9DSd\r\nW3b9vGViWV5VXWXaYu2YTbbeLZ9eCTHb0FhK7ebu9Axp44PeZ/zu1zpc6jid\r\nG7lIsNF7ICtkAdKnMUttUVtDHQ4+UMaR/5n4kykkSFJSv2Z5qEHC9KgpCpPA\r\nl6tpGVhAVSVA+BUyK0nUEXtNEm1NHWkpKYA0l6g5nPbNk+0Zp6/aUG1W4VZD\r\nfMloKgcqggJMOivZ/EoQawE7XcJtx/A8nIYNi+DuJuP9QfXsUqqR/ys9SIjj\r\n6oTTT0ORrJ1Akz8fKlIv+5v3ObWlWga96Asb+oTwYYQrHck6xx12BYbJGDnc\r\n3kzUPRlGfEUcBk6lszeTaxgD7dogkyEzJwkophwrBj26KbthFrbcC85FsZ+r\r\nYKnlYwjkIl/GT3niMogF3p1Ntkmax/1lm00Ize7+dRwvnmJhNqyb8QUVh2po\r\nHYAtPLkdcweP+wHqNhl5/IUyeUoJnoG1u1I=\r\n=+emN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "992d35da712a7a37af165114a564775e412e90ed", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "19.3.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.2.1_1679880150676_0.029612101334941965", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "es-module-lexer", "version": "1.3.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "6be9c9e0b4543a60cd166ff6f8b4e9dae0b0c16f", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.3.0.tgz", "fileCount": 8, "integrity": "sha512-vZK7T0N2CBmBOixhmjdqx2gWVbFZ4DXZ/NyRMZVlJXPa7CyFS+/a4QQsDGDQy9ZfEzxFuNEsMLeQJnKP2p5/JA==", "signatures": [{"sig": "MEUCIAhsNRb+8D2rqr6MQu9HMkKot8wG1msHg6DktduuQT2uAiEAgbGO2BDyvZoO+q92IAqaBWki3d0oMFAItU2NmYSXJQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86109}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "d44ad4ae1f5493a6956e226668008c9b2cd7f3fd", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "19.3.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.14.2", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.3.0_1686427966983_0.9776233973489517", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "es-module-lexer", "version": "1.3.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "c1b0dd5ada807a3b3155315911f364dc4e909db1", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.3.1.tgz", "fileCount": 8, "integrity": "sha512-JUFAyicQV9mXc3YRxPnDlrfBKpqt6hUYzz9/boprUJHs4e4KVr3XwOF70doO6gwXUor6EWZJAyWAfKki84t20Q==", "signatures": [{"sig": "MEUCIHCFjhvZGZvknfhRM/0Tinq4V/26COf/y/im4KCTyVknAiEArsRidBMgc8FydhRHDQKrDMvVqYcdHpg3At+ZC6UZgq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86086}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "605184a98713afdd6f3a16d5004c6a9fec136f5f", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "20.5.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.3.1_1694382059538_0.6066801475760573", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "es-module-lexer", "version": "1.4.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "285182e7f8f536ff5f4c57f2309836ef851474d8", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.4.0.tgz", "fileCount": 8, "integrity": "sha512-lcCr3v3OLezdfFyx9r5NRYHOUTQNnFEQ9E87Mx8Kc+iqyJNkO7MJoB4GQRTlIMw9kLLTwGw0OAkm4BQQud/d9g==", "signatures": [{"sig": "MEQCIFd/VUhP3+hwCKujPAqiF5xv66o3A/nJj3Oc6T8L2ZKeAiAImyScSaYfqjFHHrTFL4d03KD5fzrzOzAlsoGcBR4BXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86646}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "b2969eddeb805caafc9cbd05d29097f663d834ad", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "20.5.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.4.0_1699591314032_0.1283483884306913", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "es-module-lexer", "version": "1.4.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "41ea21b43908fe6a287ffcbe4300f790555331f5", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.4.1.tgz", "fileCount": 8, "integrity": "sha512-cXLGjP0c4T3flZJKQSuziYoq7MlT+rnvfZjfp7h+I7K9BNX54kP9nyWvdbwjQ4u1iWbOL4u96fgeZLToQlZC7w==", "signatures": [{"sig": "MEUCIQD10j59GyeVCKRGk2m731PcaXCBvdrwlc3meM+YShwZOAIgCp+Yl8mcsp4mofzmlfYK0ZsCqLxgMLE2aHZ6BCK8NFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86908}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "c357368bd4681011bc938ec54d48b2c6a969672b", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "20.5.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.4.1_1699748760662_0.6005862196498373", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "es-module-lexer", "version": "1.4.2", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "ba1a62255ff9b41023aaf9bd08c016a5f1a3fef3", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.4.2.tgz", "fileCount": 8, "integrity": "sha512-7nOqkomXZEaxUDJw21XZNtRk739QvrPSoZoRtbsEfcii00vdzZUh6zh1CQwHhrib8MdEtJfv5rJiGeb4KuV/vw==", "signatures": [{"sig": "MEUCIHhgXoD48OlKe2t+Q0+AtLKySzqP2d6Nh62mwS5IwkUbAiEAt72oROGPd00eiNSs+2/EH8oR31ooot8C96ykC9FX4eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87275}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "f44438cbda869d898194e985c3780abf0e08b2cf", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.4.2_1710821907852_0.7063731928685248", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "es-module-lexer", "version": "1.5.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "4878fee3789ad99e065f975fdd3c645529ff0236", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.0.tgz", "fileCount": 8, "integrity": "sha512-pqrTKmwEIgafsYZAGw9kszYzmagcE/n4dbgwGWLEXg7J4QFJVQRBld8j3Q3GNez79jzxZshq0bcT962QHOghjw==", "signatures": [{"sig": "MEYCIQD0uyR5Jt0LiK433HPFmHLu8yw/Iap4/oX3o+HfiSyfBAIhALUVqIeZgJb89ZEAQR9G3BOShEiHUdeKJYcY035yPwuT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90467}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "864ca9d5a803d2fb9f6415ca8ee626d73fef0c96", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.5.0_1711400588017_0.06875922424724079", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "es-module-lexer", "version": "1.5.1", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "1eb222f1b7ca8c09261f61566f496a6d2c9ee368", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.1.tgz", "fileCount": 8, "integrity": "sha512-VUT3hi9kX2jEAhDYkGJ1+MUizc3bdT7lWR8rB03ODn5x+w9t0/Aq7MfHCl/e5uu9YcKzYqVuTH1K4f5vP6kN0w==", "signatures": [{"sig": "MEYCIQCzWSG5OfdYGEnATZnhWVePxFMAYSCeS3q8rKdrnavYnwIhAJZg1XkSZErnQKmlFvUX5kg+s8KOCHZfj0AS2k0LYxLb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90629}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "c565b83537954f8dc7ad64487d71579bfaef8fbd", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.5.1_1714252634116_0.08193214796508697", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "es-module-lexer", "version": "1.5.2", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "00b423304f2500ac59359cc9b6844951f372d497", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.2.tgz", "fileCount": 8, "integrity": "sha512-l60ETUTmLqbVbVHv1J4/qj+M8nq7AwMzEcg3kmJDt9dCNrTk+yHcYFf/Kw75pMDwd9mPcIGCG5LcS20SxYRzFA==", "signatures": [{"sig": "MEYCIQCydKaDz64qQ+ZRWTvTcpp6UTxRAD6u4+XnlUv+eCO1DAIhAKqiNmnsxH8fO26bufaKcLBmDpfCR1hySEzOagN1l3J1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90629}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "c3a60c30386d1bd54fc531595f7e7124c748d71d", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.5.2_1714255999621_0.8320108196940541", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "es-module-lexer", "version": "1.5.3", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "25969419de9c0b1fbe54279789023e8a9a788412", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.3.tgz", "fileCount": 8, "integrity": "sha512-i1gCgmR9dCl6Vil6UKPI/trA69s08g/syhiDK9TG0Nf1RJjjFI+AzoWW7sPufzkgYAn861skuCwJa0pIIHYxvg==", "signatures": [{"sig": "MEYCIQDr0EMtiHdydjq+x1K4kKiZ65VK92X1pmdgLvB+ThHNjAIhAIkw6Yj6NKUTwMGiooDzmy+FXetCnIwtyCFgM+RbCSy0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90871}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "fc43bd7fd0d2557649e45a208f9d20107c20f687", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.5.3_1715973610705_0.980547367220205", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "es-module-lexer", "version": "1.5.4", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.5.4", "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "a8efec3a3da991e60efa6b633a7cad6ab8d26b78", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.4.tgz", "fileCount": 8, "integrity": "sha512-MVNK56NiMrOwitFB7cqDwq0CQutbw+0BvLshJSse0MUNU+y1FC3bUS/AQg7oUng+/wKrrki7JfmwtVHkVfPLlw==", "signatures": [{"sig": "MEQCIFlV/fV25LTWdgQaYPyVsME4inzMP99Om55OUCSDuZsQAiBu7zUa6IxPrzDMevooGpOzyG0M4odxEor38/hqmS3pwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90892}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "gitHead": "d70de9b010a7664a4b52859834f1fe3064e12c1c", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.5.4_1719205507674_0.24513064479475632", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "es-module-lexer", "version": "1.6.0", "keywords": [], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "es-module-lexer@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/es-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "dist": {"shasum": "da49f587fd9e68ee2404fe4e256c0c7d3a81be21", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.6.0.tgz", "fileCount": 8, "integrity": "sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ==", "signatures": [{"sig": "MEYCIQDgOqmnE2e6p1A2imkiHNafpXVA6p5lZwILOXc9BQlYWwIhALUud2QTIDvvIekE13rhoTXaNRjxm9cXL9t83mM0wVfw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91298}, "main": "dist/lexer.cjs", "type": "module", "types": "types/lexer.d.ts", "module": "dist/lexer.js", "exports": {".": {"types": "./types/lexer.d.ts", "import": "./dist/lexer.js", "module": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": {"types": "./types/lexer.d.ts", "default": "./dist/lexer.asm.js"}}, "gitHead": "35221ed37903ab44e74a11426f160648781862ac", "scripts": {"test": "npm install -g chomp ; chomp test", "build": "npm install -g chomp ; chomp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/es-module-lexer.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Lexes ES modules returning their import/export metadata", "directories": {"lib": "lib", "test": "test"}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@babel/cli": "^7.5.5", "typescript": "^4.7.4", "@babel/core": "^7.5.5", "@types/node": "^18.7.1", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-module-lexer_1.6.0_1735229256318_0.5375553836530162", "host": "s3://npm-registry-packages-npm-production"}}, "1.7.0": {"name": "es-module-lexer", "version": "1.7.0", "description": "Lexes ES modules returning their import/export metadata", "main": "dist/lexer.cjs", "module": "dist/lexer.js", "types": "types/lexer.d.ts", "exports": {".": {"types": "./types/lexer.d.ts", "module": "./dist/lexer.js", "import": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": {"types": "./types/lexer.d.ts", "default": "./dist/lexer.asm.js"}}, "scripts": {"build": "npm install -g chomp ; chomp build", "test": "npm install -g chomp ; chomp test"}, "author": {"name": "<PERSON>"}, "license": "MIT", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@types/node": "^18.7.1", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^5.19.4", "typescript": "^4.7.4"}, "type": "module", "repository": {"type": "git", "url": "git+https://github.com/guybedford/es-module-lexer.git"}, "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "homepage": "https://github.com/guybedford/es-module-lexer#readme", "directories": {"lib": "lib", "test": "test"}, "keywords": [], "_id": "es-module-lexer@1.7.0", "gitHead": "dceaab846b11fbd77b39056088a734d9f774b070", "_nodeVersion": "23.9.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==", "shasum": "9159601561880a85f2734560a9099b2c31e5372a", "tarball": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "fileCount": 8, "unpackedSize": 93440, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGAB3dTggchaf8AWr3PnbhkXozGwbJ9cYLfQiqJ3d/ZKAiEA/jgAZX9Rgd4sQwJDLGdCsqXZlm00Z+zmslSQgvhWJUA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/es-module-lexer_1.7.0_1745360527730_0.3230463088820752"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-08-08T03:48:06.967Z", "modified": "2025-04-22T22:22:08.431Z", "0.1.0": "2019-08-08T03:48:07.164Z", "0.1.1": "2019-08-08T03:48:44.568Z", "0.1.2": "2019-08-08T03:50:25.791Z", "0.2.0": "2019-08-13T09:54:30.329Z", "0.3.0": "2019-08-20T23:53:06.326Z", "0.3.1": "2019-08-21T00:01:27.913Z", "0.3.2": "2019-08-21T11:48:39.238Z", "0.3.3": "2019-08-21T13:12:46.683Z", "0.3.4": "2019-08-21T17:28:49.694Z", "0.3.5": "2019-08-21T19:47:22.822Z", "0.3.6": "2019-08-22T13:40:55.452Z", "0.3.7": "2019-08-25T18:26:29.910Z", "0.3.8": "2019-08-27T13:50:06.932Z", "0.3.9": "2019-08-28T17:19:21.171Z", "0.3.10": "2019-08-28T17:55:39.508Z", "0.3.11": "2019-08-28T18:36:58.282Z", "0.3.12": "2019-08-29T14:02:08.300Z", "0.3.13": "2019-08-31T21:04:24.501Z", "0.3.14": "2020-02-01T17:46:49.018Z", "0.3.15": "2020-02-12T22:27:51.273Z", "0.3.16": "2020-02-15T19:32:30.657Z", "0.3.17": "2020-02-18T11:03:28.685Z", "0.3.18": "2020-04-13T23:54:49.375Z", "0.3.19": "2020-05-16T00:34:08.004Z", "0.3.20": "2020-05-28T06:22:41.579Z", "0.3.21": "2020-06-12T11:44:15.955Z", "0.3.22": "2020-06-13T08:37:15.435Z", "0.3.23": "2020-06-14T01:25:27.506Z", "0.3.24": "2020-06-15T01:18:04.812Z", "0.3.25": "2020-09-01T22:13:09.298Z", "0.3.26": "2020-10-30T22:36:01.899Z", "0.4.0": "2021-02-20T12:41:41.308Z", "0.4.1": "2021-03-03T15:59:51.520Z", "0.5.0": "2021-06-14T22:42:28.947Z", "0.6.0": "2021-06-14T23:07:16.529Z", "0.7.0": "2021-07-05T15:29:31.447Z", "0.7.1": "2021-07-05T22:17:14.697Z", "0.8.0": "2021-09-13T22:14:58.993Z", "0.9.0": "2021-09-19T00:53:32.382Z", "0.9.1": "2021-09-28T12:31:11.388Z", "0.9.2": "2021-10-01T11:51:24.457Z", "0.9.3": "2021-10-07T22:25:41.489Z", "0.10.0": "2022-02-22T02:32:36.867Z", "0.10.1": "2022-03-16T17:34:23.494Z", "0.10.2": "2022-03-17T09:42:28.587Z", "0.10.3": "2022-03-17T10:10:08.602Z", "0.10.4": "2022-03-17T10:49:45.123Z", "0.10.5": "2022-04-04T10:39:40.235Z", "1.0.0": "2022-07-27T02:03:54.718Z", "1.0.1": "2022-07-28T01:49:57.167Z", "1.0.2": "2022-08-04T05:46:08.507Z", "1.0.3": "2022-08-10T13:46:56.367Z", "1.0.4": "2022-10-13T14:28:09.050Z", "1.0.5": "2022-10-14T19:57:56.573Z", "1.1.0": "2022-10-24T15:37:00.544Z", "1.1.1": "2023-02-05T22:50:30.379Z", "1.2.0": "2023-02-23T19:30:48.810Z", "1.2.1": "2023-03-27T01:22:30.879Z", "1.3.0": "2023-06-10T20:12:47.161Z", "1.3.1": "2023-09-10T21:40:59.773Z", "1.4.0": "2023-11-10T04:41:54.310Z", "1.4.1": "2023-11-12T00:26:00.883Z", "1.4.2": "2024-03-19T04:18:28.006Z", "1.5.0": "2024-03-25T21:03:08.237Z", "1.5.1": "2024-04-27T21:17:14.271Z", "1.5.2": "2024-04-27T22:13:19.820Z", "1.5.3": "2024-05-17T19:20:10.864Z", "1.5.4": "2024-06-24T05:05:07.842Z", "1.6.0": "2024-12-26T16:07:36.478Z", "1.7.0": "2025-04-22T22:22:07.934Z"}, "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/guybedford/es-module-lexer#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/guybedford/es-module-lexer.git"}, "description": "Lexes ES modules returning their import/export metadata", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "readme": "# ES Module Lexer\r\n\r\n[![Build Status][actions-image]][actions-url]\r\n\r\nA JS module syntax lexer used in [es-module-shims](https://github.com/guybedford/es-module-shims).\r\n\r\nOutputs the list of exports and locations of import specifiers, including dynamic import and import meta handling.\r\n\r\nSupports new syntax features including import attributes and source phase imports.\r\n\r\nA very small single JS file (4KiB gzipped) that includes inlined Web Assembly for very fast source analysis of ECMAScript module syntax only.\r\n\r\nFor an example of the performance, Angular 1 (720KiB) is fully parsed in 5ms, in comparison to the fastest JS parser, Acorn which takes over 100ms.\r\n\r\n_Comprehensively handles the JS language grammar while remaining small and fast. - ~10ms per MB of JS cold and ~5ms per MB of JS warm, [see benchmarks](#benchmarks) for more info._\r\n\r\n> [Built with](https://github.com/guybedford/es-module-lexer/blob/main/chompfile.toml) [Chomp](https://chompbuild.com/)\r\n\r\n### Usage\r\n\r\n```\r\nnpm install es-module-lexer\r\n```\r\n\r\nSee [src/lexer.ts](src/lexer.ts) for the type definitions.\r\n\r\nFor use in CommonJS:\r\n\r\n```js\r\nconst { init, parse } = require('es-module-lexer');\r\n\r\n(async () => {\r\n  // either await init, or call parse asynchronously\r\n  // this is necessary for the Web Assembly boot\r\n  await init;\r\n\r\n  const source = 'export var p = 5';\r\n  const [imports, exports] = parse(source);\r\n  \r\n  // Returns \"p\"\r\n  source.slice(exports[0].s, exports[0].e);\r\n  // Returns \"p\"\r\n  source.slice(exports[0].ls, exports[0].le);\r\n})();\r\n```\r\n\r\nAn ES module version is also available:\r\n\r\n```js\r\nimport { init, parse } from 'es-module-lexer';\r\n\r\n(async () => {\r\n  await init;\r\n\r\n  const source = `\r\n    import { name } from 'mod\\\\u1011';\r\n    import json from './json.json' assert { type: 'json' }\r\n    export var p = 5;\r\n    export function q () {\r\n\r\n    };\r\n    export { x as 'external name' } from 'external';\r\n\r\n    // Comments provided to demonstrate edge cases\r\n    import /*comment!*/ (  'asdf', { assert: { type: 'json' }});\r\n    import /*comment!*/.meta.asdf;\r\n\r\n    // Source phase imports:\r\n    import source mod from './mod.wasm';\r\n    import.source('./mod.wasm');\r\n  `;\r\n\r\n  const [imports, exports] = parse(source, 'optional-sourcename');\r\n\r\n  // Returns \"modထ\"\r\n  imports[0].n\r\n  // Returns \"mod\\u1011\"\r\n  source.slice(imports[0].s, imports[0].e);\r\n  // \"s\" = start\r\n  // \"e\" = end\r\n\r\n  // Returns \"import { name } from 'mod'\"\r\n  source.slice(imports[0].ss, imports[0].se);\r\n  // \"ss\" = statement start\r\n  // \"se\" = statement end\r\n\r\n  // Returns \"{ type: 'json' }\"\r\n  source.slice(imports[1].a, imports[1].se);\r\n  // \"a\" = assert, -1 for no assertion\r\n\r\n  // Returns \"external\"\r\n  source.slice(imports[2].s, imports[2].e);\r\n\r\n  // Returns \"p\"\r\n  source.slice(exports[0].s, exports[0].e);\r\n  // Returns \"p\"\r\n  source.slice(exports[0].ls, exports[0].le);\r\n  // Returns \"q\"\r\n  source.slice(exports[1].s, exports[1].e);\r\n  // Returns \"q\"\r\n  source.slice(exports[1].ls, exports[1].le);\r\n  // Returns \"'external name'\"\r\n  source.slice(exports[2].s, exports[2].e);\r\n  // Returns -1\r\n  exports[2].ls;\r\n  // Returns -1\r\n  exports[2].le;\r\n\r\n  // Import type is provided by `t` value\r\n  // (1 for static, 2, for dynamic)\r\n  // Returns true\r\n  imports[2].t == 2;\r\n\r\n  // Returns \"asdf\" (only for string literal dynamic imports)\r\n  imports[2].n\r\n  // Returns \"import /*comment!*/ (  'asdf', { assert: { type: 'json' } })\"\r\n  source.slice(imports[3].ss, imports[3].se);\r\n  // Returns \"'asdf'\"\r\n  source.slice(imports[3].s, imports[3].e);\r\n  // Returns \"(  'asdf', { assert: { type: 'json' } })\"\r\n  source.slice(imports[3].d, imports[3].se);\r\n  // Returns \"{ assert: { type: 'json' } }\"\r\n  source.slice(imports[3].a, imports[3].se - 1);\r\n\r\n  // For non-string dynamic import expressions:\r\n  // - n will be undefined\r\n  // - a is currently -1 even if there is an assertion\r\n  // - e is currently the character before the closing )\r\n\r\n  // For nested dynamic imports, the se value of the outer import is -1 as end tracking does not\r\n  // currently support nested dynamic immports\r\n\r\n  // import.meta is indicated by imports[3].d === -2\r\n  // Returns true\r\n  imports[4].d === -2;\r\n  // Returns \"import /*comment!*/.meta\"\r\n  source.slice(imports[4].s, imports[4].e);\r\n  // ss and se are the same for import meta\r\n\r\n  // Returns \"'./mod.wasm'\"\r\n  source.slice(imports[5].s, imports[5].e);\r\n\r\n  // Import type 4 and 5 for static and dynamic source phase\r\n  imports[5].t === 4;\r\n  imports[6].t === 5;\r\n})();\r\n```\r\n\r\n### CSP asm.js Build\r\n\r\nThe default version of the library uses Wasm and (safe) eval usage for performance and a minimal footprint.\r\n\r\nNeither of these represent security escalation possibilities since there are no execution string injection vectors, but that can still violate existing CSP policies for applications.\r\n\r\nFor a version that works with CSP eval disabled, use the `es-module-lexer/js` build:\r\n\r\n```js\r\nimport { parse } from 'es-module-lexer/js';\r\n```\r\n\r\nInstead of Web Assembly, this uses an asm.js build which is almost as fast as the Wasm version ([see benchmarks below](#benchmarks)).\r\n\r\n### Escape Sequences\r\n\r\nTo handle escape sequences in specifier strings, the `.n` field of imported specifiers will be provided where possible.\r\n\r\nFor dynamic import expressions, this field will be empty if not a valid JS string.\r\n\r\n### Facade Detection\r\n\r\nFacade modules that only use import / export syntax can be detected via the third return value:\r\n\r\n```js\r\nconst [,, facade] = parse(`\r\n  export * from 'external';\r\n  import * as ns from 'external2';\r\n  export { a as b } from 'external3';\r\n  export { ns };\r\n`);\r\nfacade === true;\r\n```\r\n\r\n### ESM Detection\r\n\r\nModules that uses ESM syntaxes can be detected via the fourth return value:\r\n\r\n```js\r\nconst [,,, hasModuleSyntax] = parse(`\r\n  export {}\r\n`);\r\nhasModuleSyntax === true;\r\n```\r\n\r\nDynamic imports are ignored since they can be used in Non-ESM files.\r\n\r\n```js\r\nconst [,,, hasModuleSyntax] = parse(`\r\n  import('./foo.js')\r\n`);\r\nhasModuleSyntax === false;\r\n```\r\n\r\n### Environment Support\r\n\r\nNode.js 10+, and [all browsers with Web Assembly support](https://caniuse.com/#feat=wasm).\r\n\r\n### Grammar Support\r\n\r\n* Token state parses all line comments, block comments, strings, template strings, blocks, parens and punctuators.\r\n* Division operator / regex token ambiguity is handled via backtracking checks against punctuator prefixes, including closing brace or paren backtracking.\r\n* Always correctly parses valid JS source, but may parse invalid JS source without errors.\r\n\r\n### Limitations\r\n\r\nThe lexing approach is designed to deal with the full language grammar including RegEx / division operator ambiguity through backtracking and paren / brace tracking.\r\n\r\nThe only limitation to the reduced parser is that the \"exports\" list may not correctly gather all export identifiers in the following edge cases:\r\n\r\n```js\r\n// Only \"a\" is detected as an export, \"q\" isn't\r\nexport var a = 'asdf', q = z;\r\n\r\n// \"b\" is not detected as an export\r\nexport var { a: b } = asdf;\r\n```\r\n\r\nThe above cases are handled gracefully in that the lexer will keep going fine, it will just not properly detect the export names above.\r\n\r\n### Benchmarks\r\n\r\nBenchmarks can be run with `npm run bench`.\r\n\r\nCurrent results for a high spec machine:\r\n\r\n#### Wasm Build\r\n\r\n```\r\nModule load time\r\n> 5ms\r\nCold Run, All Samples\r\ntest/samples/*.js (3123 KiB)\r\n> 18ms\r\n\r\nWarm Runs (average of 25 runs)\r\ntest/samples/angular.js (739 KiB)\r\n> 3ms\r\ntest/samples/angular.min.js (188 KiB)\r\n> 1ms\r\ntest/samples/d3.js (508 KiB)\r\n> 3ms\r\ntest/samples/d3.min.js (274 KiB)\r\n> 2ms\r\ntest/samples/magic-string.js (35 KiB)\r\n> 0ms\r\ntest/samples/magic-string.min.js (20 KiB)\r\n> 0ms\r\ntest/samples/rollup.js (929 KiB)\r\n> 4.32ms\r\ntest/samples/rollup.min.js (429 KiB)\r\n> 2.16ms\r\n\r\nWarm Runs, All Samples (average of 25 runs)\r\ntest/samples/*.js (3123 KiB)\r\n> 14.16ms\r\n```\r\n\r\n#### JS Build (asm.js)\r\n\r\n```\r\nModule load time\r\n> 2ms\r\nCold Run, All Samples\r\ntest/samples/*.js (3123 KiB)\r\n> 34ms\r\n\r\nWarm Runs (average of 25 runs)\r\ntest/samples/angular.js (739 KiB)\r\n> 3ms\r\ntest/samples/angular.min.js (188 KiB)\r\n> 1ms\r\ntest/samples/d3.js (508 KiB)\r\n> 3ms\r\ntest/samples/d3.min.js (274 KiB)\r\n> 2ms\r\ntest/samples/magic-string.js (35 KiB)\r\n> 0ms\r\ntest/samples/magic-string.min.js (20 KiB)\r\n> 0ms\r\ntest/samples/rollup.js (929 KiB)\r\n> 5ms\r\ntest/samples/rollup.min.js (429 KiB)\r\n> 3.04ms\r\n\r\nWarm Runs, All Samples (average of 25 runs)\r\ntest/samples/*.js (3123 KiB)\r\n> 17.12ms\r\n```\r\n\r\n### Building\r\n\r\nThis project uses [Chomp](https://chompbuild.com) for building.\r\n\r\nWith Chomp installed, download the WASI SDK 12.0 from https://github.com/WebAssembly/wasi-sdk/releases/tag/wasi-sdk-12.\r\n\r\n- [Linux](https://github.com/WebAssembly/wasi-sdk/releases/download/wasi-sdk-12/wasi-sdk-12.0-linux.tar.gz)\r\n- [Windows (MinGW)](https://github.com/WebAssembly/wasi-sdk/releases/download/wasi-sdk-12/wasi-sdk-12.0-mingw.tar.gz)\r\n- [macOS](https://github.com/WebAssembly/wasi-sdk/releases/download/wasi-sdk-12/wasi-sdk-12.0-macos.tar.gz)\r\n\r\nLocate the WASI-SDK as a sibling folder, or customize the path via the `WASI_PATH` environment variable.\r\n\r\nEmscripten emsdk is also assumed to be a sibling folder or via the `EMSDK_PATH` environment variable.\r\n\r\nExample setup:\r\n\r\n```\r\ngit clone https://github.com:guybedford/es-module-lexer\r\ngit clone https://github.com/emscripten-core/emsdk\r\ncd emsdk\r\ngit checkout 1.40.1-fastcomp\r\n./emsdk install 1.40.1-fastcomp\r\ncd ..\r\nwget https://github.com/WebAssembly/wasi-sdk/releases/download/wasi-sdk-12/wasi-sdk-12.0-linux.tar.gz\r\ngunzip wasi-sdk-12.0-linux.tar.gz\r\ntar -xf wasi-sdk-12.0-linux.tar\r\nmv wasi-sdk-12.0-linux.tar wasi-sdk-12.0\r\ncargo install chompbuild\r\ncd es-module-lexer\r\nchomp test\r\n```\r\n\r\nFor the `asm.js` build, git clone `emsdk` from  is assumed to be a sibling folder as well.\r\n\r\n### License\r\n\r\nMIT\r\n\r\n[actions-image]: https://github.com/guybedford/es-module-lexer/actions/workflows/build.yml/badge.svg\r\n[actions-url]: https://github.com/guybedford/es-module-lexer/actions/workflows/build.yml\r\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}