{"_id": "@babel/helper-plugin-utils", "_rev": "93-8796a92b416fd63018d6a013bfed650d", "name": "@babel/helper-plugin-utils", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.41": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b515524853b2e509409ccbcd8b0bc3586da3e7f0", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.41.tgz", "fileCount": 4, "integrity": "sha512-BWjM1lcaLiCZ+4rPBiuNc0cA1wAZPfSu9VnjJfrKf+Rj68nx9aZeYHpU4+4axOKsmpgfavipCLoHi/cpFJGEHA==", "signatures": [{"sig": "MEYCIQCDyNNc9GpgIiZ90QbOzPgLaOLaPwSpQP61v50FJEU7hwIhAP+fIsUrAnfVtmV7t31q4N4BqUZoAC6W6muisDNGnC7K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5261}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.41_1521044730826_0.032488662018060666", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9aa8b3e5dc72abea6b4f686712a7363cb29ea057", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.42.tgz", "fileCount": 4, "integrity": "sha512-hZLw8Iz9/YOxI9mgWyPOP1S84OcdQo1WFkZrS1sSf45g16sEb4dVslds2uvZgmx9BiG94PoWyABGf48Py6D6CA==", "signatures": [{"sig": "MEQCIDPjGDgl1u6uGTuuL2VOLZqWU5Dp/y8YQhTJ26YqQzcsAiBaNggvla7SCOJNGGnq2gEcWj/mhIIntajDDZGyDonx2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6515}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.42_1521147006344_0.5762704995152472", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a5c95110e9bf53b96b339661c96c37efd0960568", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.43.tgz", "fileCount": 4, "integrity": "sha512-d55BCkmGlgQXNSGNh+GyRgTuAZxBVqMu2U/uZqBt6mbPmwiu1vuJD7jZnnjGpM1c3Cm3rN9Nt4U1i7S+V/dBtw==", "signatures": [{"sig": "MEUCIBtTyh9W3sqGvcnRL7TH3pRJhplXieUVh1PFN5Gj00MEAiEApXaExXHvC2xnp8fVWZmvHVaDS6boNkC2KtmJnVxOTrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6478}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.43_1522687678015_0.49278130882194127", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9f590bc3ae6daa8a10b853233baa3e25d263751d", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.44.tgz", "fileCount": 4, "integrity": "sha512-k0hZ8w9N3b0Psmlw0bB7U9Hwqc+/hh7yOPFyLi5KAX9pRZ9i+UbTg6DxsVLVuITvF/M1UZNrq7vatrlEw/IPMg==", "signatures": [{"sig": "MEUCIQD0KFd3q5XUwH9VLQyqQCJtMUXizJYkkxyP0wu1xV1S0AIgSlWBYIej/Ct7amQmBE7ECMGDCPEXOlyjD0OCgWhqiwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6553}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.44_1522707580673_0.2124581976773141", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f4b8732a0478c358ff9cfc90b3491de76a310614", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.45.tgz", "fileCount": 4, "integrity": "sha512-F3LomSGmZGVId4qIup4nE0kNe4BWC7T8/gB72kN96fP54qfeQb29dvK65IUQjppCPv1g+vrsA0XxdVPzx0nsSA==", "signatures": [{"sig": "MEUCIE2WQ07U9a6WCdTuoQGRjsQEL7+ItFpfnzZyW/gk1VeCAiEAwz1Nl6SIxnGymknlrz5SkLAUAxdLHQ43l0MI4u7FERk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0HCRA9TVsSAnZWagAAuXgP/1IAMOWqbhwMHml28kiA\nOxLz5tCbf7t4KGwvVXzd7o7gdw3osfEYUDXTz5zEMofin4dnj8iT2NkLtZtS\nMw2OVgaptxQAh8l0PwxYg4zbKZswve+q5j0Kci7FbaoFqHYvWw4FsJIpvQef\nqHnRZUfjelu9O5fyDcxrmMXAGiB2fAySaGnPetDiMOv28ajj9OhZU6DSIu3R\nsbkpA2cXHLn4Lq8DsSo53dKd7vac2NLv/qMl8AzNkXl99dXUjp1zKyw/Z3Fb\nHSAc9hFfUj+Y8P2a94gUo8mKLgjcwQfxu+S/g0ic0xEk+bq2x/p8NTzyIXG0\nRqg5xPrD/v+ykBMIcPIpwjukCKRW1czckSiAXit8T3uCogtZSm7Pg79ZX6g+\np8HoMkzKXodzKtps194C/W4v+h1mv3s/ZtKi/VyP20HyRarIo5NfUQmGO5iM\nWTSYePx/ai8exqvyFooalC1qqwKFUg1QrbenpethChqQsDt4CnxCpf7uOMPP\ntgf7D2XG4YvAnIcJO1NOKqedcKZoHbHiQclxNfrJPYT4mmD8GuOhLWz8pkMg\nbj0Hs8dikPn+uQJVmTqRzomt2gDIz1NMOfPyLouG3n7m9MEVOHPug2CEvtat\nJk79OyFsfsnEiu49g6XoHAo6Ovuwvghl81YGUncwjJUO+7Zfn57/gKm4CjhN\nF0Ob\r\n=juNQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.45_1524448519261_0.1871793542868765", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f630adbd9d645d0ba2e43f4955b4ad61f44ccdf4", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.46.tgz", "fileCount": 4, "integrity": "sha512-eRTFH+/1rqDfzx+Z//CYk4TNwhfPQpM/TCs4CmHu2DwCPrqFnKUZLI1KgStfLf//c8FdOqx/U9EPec7s8CbUIA==", "signatures": [{"sig": "MEUCIHWW5nCKRnlQFh4Wye1evcuymfoVhgW8BzLGvlRtcm7XAiEAqYScOTq+M4MaApl1NOK+auc6oNQgOHdZ7CvQmh/8ivY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WE6CRA9TVsSAnZWagAAi+oQAJqigTCokVza05RpiQ/2\nXxL/Wqt3EKMRe/UP/618mqB9vRfN+dyzaywRSLK9jG/qg4ebo2aT7BPC9JwR\nwD69QylbI0gCFIvki9QpGGxXMrkCpH9gZG0xOahEEJgT2ygvzLfFWClml1Yu\nweB3Nj/Us+oOobDZjVo/U3sIbsr1RaMQW+lmooLAZwC8iIpFNQXPo7XwNTjf\n9dMAwoSEQV9EphnGHUC2teHy3+p0gFcbEoFnHX0bj30Z+Pgj9+eT8G7pJmqX\nYiULY5maKOzx8JC+rSZIKE+yDInEXjhNaY1+juipJjYpv/0c4QLzR6AVAccg\n0UqeZyqrLDWXL6EOPLxp4iGG+lAmNrE+K2q6jcT1ffvjhx5SImDNKjp+CbfS\nvOJFRF/o6qJSSP/lspDQcoM3MEVp7rC+htQfm7YeiD5FFUrS9w8JAweh5M6R\nbeWs5VuCcQ5oFHddYQnUPxgbGL+bS4OdQBx28fvssfGR2dvg7v1xSr4TjTcT\nnC/3bJCfdVL2/FTGBvhrN14VoMM6avMNKW4+uqj2eYytBTSPvdzyvqeJiZm/\nJJRPmwNMllD8RlDi9R3ePh2g2lp8eEiEujsKmM1qc9KHoHuNm7GRsGR9xiBp\n0MLC6RNS/RaPZaMVZW5kuoyyyZ2bm2Yez/W3PJ8sk71uWfc33sWtHCN+23Z0\n6de0\r\n=G3lC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "9.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.46_1524457785959_0.07584508577456783", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4f564117ec39f96cf60fafcde35c9ddce0e008fd", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.47.tgz", "fileCount": 4, "integrity": "sha512-GR67o8boOKVJRKM5Nhk7oVEHpxYy8R00lwu0F82WxxBH+iiT26DqW1e/4w/mo7Bdn1A6l0pNaOlNk1PdM2Hgag==", "signatures": [{"sig": "MEQCIED5rsRFo3ccCRSqHJ3aboOaaARBSv/uE6xrJl8t732uAiA8Pyer3hDPuKfnWF4Z0gfBJFg2j5qYMIhMRpGEUtfHCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iSkCRA9TVsSAnZWagAAEpEP/3cr50aoV9Yz/9AqkJf7\nXLV984F3RUvvCtusPE+WOTujTnyDyZU7BqxhCOm7fSdhK7Cre0uWp9CDv8iJ\n4tcgPAEkB7cUZN2yt05TQ5EG9kQmcpkBamgWtwF/dNaqxQc7LnaCt6juQROI\nGn47NKdtvgQ9bXiqu/lbkPcVgak9Gg94VWTABErHrLNakdmF8z9eBFr+onsb\ngXklHsagt6zqpd3rPZbBZPDerxJOVcm2rC7QAHRjPVmi6RWFztpjgSqCFu94\nY1dSfxWkNNnC9F/2CPm9Sd8AuSjjMSINlzrSr6ZP0BMMpR1JLf7Zl9PhYm9u\nSpMmPbR0kUo6s9UTYkd6EBKfPTkHkOEKOsq2gZpV7cRgW/PX54DfjTWP4OFP\nASctiZhihgOF4H5XNi5je4IpwCYS6Oc7jXNwcFYtjC48Mqg6xByj8wfpYwkF\nlthbsVuMy0wsSsjJf7bjnDGXxasgvthMUGmdF9BNcvu2Q7VxOr1zrXgHdCmO\nt6bh4wJWXKpvGsUwSamwGAZDHp1yZMICcFA3rtplvcLI1Eswt6SN67pURf/6\nWWydRiimUSAWp8Z2tyBDV0EGZCKYb3omiKz+fP1XPBdSvsal/y3GwHGJty3W\nw5fnjMxedOjUFgnJ1KnYbeO2eITsnmh1S4ylauOq0c/bnWhECZG+J96sZvWJ\nFY7x\r\n=89mQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.47_1526342820091_0.8314983592479641", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bf310f89e91d146ac0f1369562164be45edba587", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.48.tgz", "fileCount": 4, "integrity": "sha512-2ocT0LL1uVUJVnAurHl9pcHfW8VaucCfM2YTQOVJC5yrL9CYx2HqrBsXIS6ZhOJUcbSot6945fsZhUiDWegtsw==", "signatures": [{"sig": "MEYCIQCxUHmrHjSC1QnwbdBcO013buWKx39H1u3HYxmER7lwcgIhAPH+P9ztLMefxc1saElPiTsRDEmc3FGzPdwmZ1/CDLZI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBw8VCRA9TVsSAnZWagAAjOEP/125EboviVKUcgwYsIox\nuA1jlx2q8hLd93mUOeRCP3gUgG9h0dtE7ZPtBwNWiVXc/m0/WtxX8C1QOpin\nGWvWOCmDE/HQxkXP9kz0J7ythDjoccdpHm//OBsFD1Kd0eGX6mNktB1CZD5H\n6ZmWTMMSLiuj+YdzyaZnH/oFFqitE/46VmEyo9XMO6hpqYLW0w0qE2q2mgBn\nbmlxkK4eI5qBxKE2WPRtLawqQdzsje0BTICSqgTiGcZM96/ClkJeuuzIsofv\nUQqweMDWplI7TjIZ6Et9cbcAD0fNbglrmp08KCzxkFTe3rJwMtnFTMAYI0UX\nirbHoDcUWXfdkQk+Wb0+7NSA6/JWt5P9lIDTH6aRHZe4Fongs9OL8YBFystc\nX7sBwKsiR3UMJnKQK6Ov1Ieuf4ybZKa9eVqS6jFFcsN7YyKBSh04B0qeEXF4\nBO25o4SbabDVLNBBZBPt2vlqL51WNhQJw9nAWQcEKvnoSEtrEvhUma05WQHF\nLzb1XGZNPXlchM/mhRf+92wXl+Cme9uUrMWEjtmwNWBa+6L45X00i0AfNlts\nSNCwrgS/gfgjc3/b1uE8AMe9HCVNgqCPE/hAAsc2MM4OAw0BdB+ZpWOHOoNk\n4+R9P3//Yf9P2OZU66Zw/AopoCYBQEuSDrrTQ5eS+c0vZWypYoSKX0E6dd21\nrRLr\r\n=14HJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "5.6.0", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.48_1527189269142_0.5148981786844631", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0e9fcbb834f878bb365d2a8ea90eee21ba3ccd23", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-YZ7bviDh1RVLEVkEXC4qknuXVEb0O9Lqu3CzQC50i3lykRSdXzcdfC+hqkDbl2/Ol5YqpWEzfcoBe+kKDdW92w==", "signatures": [{"sig": "MEQCIELa4RVWQm4pKft0ViE4rgv0OCPuQkYhb/qWNc3aENT1AiA48UJLrFDMcaYuP2UQxDj0lKVhW5kShiwuvoyMqamr1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMLCRA9TVsSAnZWagAAvoUP/3X/An5sh2Z6kGulb6Yp\nI+wRENLxcXT3BXKN9MYZWRujFBoezde5fCFIkG4vcJfRSX4jyQtAvlY3lNnC\nXhmtR0FVgBEBzwIX44HGY01PIH8RMCaCBusSxz8KvBxiruper3pkVgCxkop3\nC7ZmHjQvAj/N8UVcm2jB9xNG2lkFBbosrQGyyaEJM1bO+Hb1vANpbhM1yHGd\nPCyPgN90h9Ka3AXyJEirq6yJpTnAlSMZ+3XvFbVsrSt/GHqbkuU1amzRGL7D\nUYceT8bqS/vx0G7S5/uHsRuetPZ/YeVHd7wsi+c0PA+jukoEjMHwnDR6FWQ1\noUCuM35OX77y73pgmM1JR2nwTwGuZUP/S+Ldt9yPuHq/UX2TbF4+PvNq4Wgf\nwjY+2fNKQUjOXmrqJMI03tDynJt6+0YpPIuNcN+fPxyofe3tpeYF3/LiM09W\noEg9cGk5NsrKOIU/LuN1Rjf9bK/tD3RnglWBpM6L7+i4EiSfaxMb+xF26dCv\nCkku+qM52tJvOIHeU4bjaE3OwrSWaLTfOScDnGJn9GgN+ntN1it+0aJFgKTE\n8bPJ3sBszusHRz/s0B6BZtcV+2ZUon7b6SuaLuNMiTRWGiPMTzQlp06KdlLr\nNe8UoAZ+WBTT5sxKIfR6qxpHQKCs+VJ2Su3RLZ4xmdeKgPr94/tQeVpyvRb7\nx5mD\r\n=OXK1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "0e9fcbb834f878bb365d2a8ea90eee21ba3ccd23", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "3.10.10", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "6.12.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.49_1527264011142_0.5593949762225983", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e1e9bc63b22e758cae771190fe5eb6e81c6df271", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.50.tgz", "fileCount": 7, "integrity": "sha512-yJ5aNIKMEEinNfO+Gr4UGJuNL58JIpznQf/prdNcsx9aTmWrbri82U39zz6HGisITf6qrMVmcxlMYPPFP2zKhg==", "signatures": [{"sig": "MEYCIQDsoZwGKBcTlKS5/sAIaIwtHC26bjT1EVBiI+T4SIek8AIhAO27FxY7shUBPWEnQ3AxoW4UwWE1MytAAtj0XAkgGo/p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5479}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.50_1528832798424_0.01287816131937869", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0f6a5f2b6d1c6444413f8fab60940d79b63c2031", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.51.tgz", "fileCount": 7, "integrity": "sha512-6BNVMJfM2bW5DqKOnk7GJswMXbmP80F8nZ3YIzm7WvNrVMazV5y5XMaCMbtgtLqcHPCCXieLhXNDSi6M6ZnA/A==", "signatures": [{"sig": "MEYCIQDZF1aLK74BhBV9HnuAA+eFWqpWLHlT+NRPYYyR2jaHagIhAK2NKkl2cZR8ydTGxxig7J2ltS+jKB5MMrWWSkLaltZo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5479}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.51_1528838344002_0.9434863142596832", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2f058c5f7c3a5fe4bc219036b2e78e11bddeb7ad", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.52.tgz", "fileCount": 7, "integrity": "sha512-lxfjzZhhWLVrZP14hwmo70ww/7ST0H981EtqwLvZ153NzQ55uJiMU1EpqPHWq+gOeErrukSDommNvimn2RQqXQ==", "signatures": [{"sig": "MEUCIQDxn9JJplj+GRRYGDt0blYi+zHIhpgCjXVzkwPGwRYObQIgDnEdyUGL61zK5rB8o2gL4n5cLin1w7bOmbVcFaUB0po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5478}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.52_1530838744676_0.4851264475559136", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d64458636ffc258b42714a9dd93aeb6f8b8cf3ed", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.53.tgz", "fileCount": 7, "integrity": "sha512-ziTIzKm3Hj8LvmV6HwyPC2t2NgSNg2T72Cifqaw3zo44ATRUeNI/nH7NoQZChNNwye97pbzs+UAHq6fCTt3uFg==", "signatures": [{"sig": "MEUCIA0gg8saM0VOZp1hw+sT49U2NnU+wCcXMbjvmXOQ8GZ7AiEAr/9IGgs7SoMNSctOLuW1s+PqIwjLFPWYuFDBZWLwkOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5478}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.53_1531316394127_0.3295980847845037", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "61d2a9a0f9a3e31838a458debb9eebd7bdd249b4", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.54.tgz", "fileCount": 7, "integrity": "sha512-vb9BR2/9DOglLN5dBY36FxbAPeJpLksAbABepfKh+GMfxF6DuKDJARUgZbgzQQm63hKfU8nzo7GjUWpXARqcwg==", "signatures": [{"sig": "MEUCIDgDAxvq8anVIXQplSO+7a1971uBfS3u+EoVtMGa1cLcAiEA2rwziOQrb/ZxIPvQSASOnxm1iaQBqpw2V3ATt6HzMjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5478}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.54_1531763984982_0.11652407562383038", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "31f40777efd6b961da8496a923c22d2b062b3f73", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.55.tgz", "fileCount": 7, "integrity": "sha512-YvlDceTiAoiZlurNjPdUw1K76tIUTGHx2wpPX27JiCCRVTUd1U51OVYrKZKuecYxWdkeumys/LjlT7vHDmk1nA==", "signatures": [{"sig": "MEUCIQCyOYTfOFGd/i9SBOC6yTPehD/h6m62dW1lnlJYnK03LQIgHElfXoWneQ2HwjKfJxZrpHQz+tGvqNm5utik8GbdsAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5478}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.55_1532815603287_0.9757457217340315", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e5f63cae8b3b716825d64e69ad8b59d71cd2080c", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-beta.56.tgz", "fileCount": 7, "integrity": "sha512-6IlFMU13X7gwnnMldDHwfc7IqngqCH/KfiU7I+GdNoZPnddmjghc87E/zKHaJpWdX1VvXCCelp2EnKq0rgBQ8w==", "signatures": [{"sig": "MEUCIDWazok/SjOUDpl1/GnynAYrQyQbRM5R14i1nNn+kgXoAiEAkgaphpfNas9IIYpKQTU1N4QDw1Z+DoZcmadUtdlBpkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPs0CRA9TVsSAnZWagAAJ+cP/i0n42AV71YAsrlZjnU6\nUZAfP5lpp2It9prC4JA/lGjs/7HzR53BVi15/DFYYXSjURF7flx9ifyj1f5t\nsgIYAWvKYeKMASLQSz74XFd04qDQDZROOcMmXiZihJ2b31hY7W6fvqFLyimy\nmDnP397BFE/b2oRA5DB509FR3NIZVYRZSqnReAuDt09mrlqpA2jA9h7GJrEi\nxT7eIAac2uCWGgeVc3iB2WPUyletEAon/Meyvs7/ku86dB9eztYv1rOHNN6M\nY/wMSs8z8VH+pydYcgUBV/CH5ycFpiUnORXYeO6tFhFOcXSho46VTua4C/3h\nHRr3kROM3Hx5tCEkRPe/k9v5FkmzTL2aGWGr3QCnpqGpp3mxsEQNYgMfAaMg\nbi5xrzzDF9VLqIDHL4bv4kRfO+Ac6W5W2Fhxa0FIb5/5CBWTjItjiUy1k1XC\nHBd3uXXi9i7971EIg9Yjl/veXl/3Zp+Q6fSESjAHqA2Wt3SSp9is95XFWXZU\nRy2rL3K8QjRmBDqQzCK0JOtAagHVzW+BNgFrWWu0Oog9fCsN9IvEFMoxE1kt\n1C0iXnOnzJc+hI231QrfjAbXgVprRkXPcYw+9JkgtyZ7oS+TFh4Ox8SQ0CPM\nOM9VPy2nGGGjFxBzxAOE3tlTCoq+IbJOhc+JrE9+zDxcLBH+Tpx2QyVVvM3z\n7cWD\r\n=1YDK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-beta.56_1533344563739_0.956882431205843", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d03f90d00bd8fc7ad7269e1621f3fb2cd4278de5", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-rc.0.tgz", "fileCount": 7, "integrity": "sha512-MoDPlcszVmD/cyHG5dyanNUIVawt3hmGFVuzvQ34yuriyNNqmPCEOUuUaO+HcDPk8Ru92JQyj1YNwgAW84Gsyw==", "signatures": [{"sig": "MEUCIF6zV5AVhQfUCdTDSTr2NmEhgfLmKnYAR6bVlmCTBrYEAiEArLVJYuKiOaJJ2XBjFVWTdzESCnsYsi9W3hcya+pXgBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGQTCRA9TVsSAnZWagAAqHUP/2z1TpKI9SY1YbYnN5wK\n+Q2F4aiWiq7+kx2/bnnWdyYOtabDvHUjc3lFAzbugPeYeOpv4WiW0M7ccIbB\nocrVy/zejhbUj2xBNi+lVHkbJ7JmwxhKCunBpoV12oUiaIxEbv+kS6BcX2e5\nvSwWKfX4HDytSISgbxwA7Euc3kPIsTLgFhhnohSumakW12OemlMIUebCV9oG\nYQDUL8o7UVdkT2TB87BjPSbZ+7LmlugUmBodcURSHszos1UfFVVCWU0vhZv5\nVZH8yaeuRLn4a9ecZnSZYNElnTofoIEL/gLLxjISR+ROs1Eh45EVh9MG2U9S\n7uxTCXWJiPvrP8959lxr54zrUtzbxYsHkyOiPMac13Mtf7eXJV1xdd5Uj0vW\n9ES2QqUf3+LqBAlmCHi7irqC98fxb7RLc7oyraAgc9eVspN9mjbznnGN2K8g\nTwIlxH/vch7EbBDdkT/OyyATbnPtUgo0S14wgsb3RprSbfbaUq7s7xtXeN5Y\n0xA722Q/ctS1m5tlz7OWP/PuClQ0avjl3ibE80Cdk3t74s5epBPVGFtumqyp\ninCAH+9/fAfVAneCdgXsa8HnQbWUz4bZ+Jwupz+K0NcE8uNSgP61KKPykLgQ\nmN9L/15owE/gWF2cyDgzdrhlRC+jX+//mxFP6dRCRhmMOhq4vdFpsREAthQK\nXgAd\r\n=3VYd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-rc.0_1533830162528_0.4455215365983134", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3e277eae59818e7d4caf4174f58a7a00d441336e", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-rc.1.tgz", "fileCount": 7, "integrity": "sha512-8ZNzqHXDhT/JjnBvrLKu8AL7NhONVIsnrfyQNm3PJNmufIER5kcIa3OxPMGWgNqox2R8WeQ6YYzYTLNXqq4kgQ==", "signatures": [{"sig": "MEUCIQDe8W4c5YpFqYFeflX2Dw8xKDmdLkyt4OjSFrKDNcPpaQIgDOOW8ACqbNsf27CXK64S+JKRnfec04OQX5CPgnwkajk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ6zCRA9TVsSAnZWagAAb6YQAKGFIsNa801o4ytAEgvK\nGmhWlOQ/J2PX7/4MG9jaYNiX8/Irm1KpDLGT/njlz7bgq0OFz7Kw3dXC9o9X\n95Ggyx8h6pCE67hQ46AOtSGdH39OLxjwun6AqddfMs3rQS0ZArO81GVk27cf\nAQvTf8StIxUQuXiUVc7Jx6GyPFkWQOfvZNaCPjc2BZGr36G7rSlTVuaFA6NL\ntBcvB4BG8pJYgsZqRF+LHNDxPNBBlEP1HnsByapIE9rFaSsWaTLowBIgpwU1\nOfKHQ+LE9AcU3OBHq7X3L3c4Furhsk5uAGdEzXeY8Hnaj+jXrk623R077e2e\nuhb9zqjeOdrR5lgMia/oN+5DO6m00tQmVfa9CE0a8ynYFNhzDasFq1GIC59p\nEZFYQ+D341xv/UcJvkpneLXSjgd8n8h0yeuXebmNa/+4ZpmQOZiSImO+ux3u\nvWxdhPizOKvrdsztayeB5el3dFVUys8aKYekF0ZUuIFAuUQY9SEkxD6fmoqf\nGpQGiRpxz5MR1XCS1Q4BcieQCENp8dHvs51jLiJqQVsiHwNvTO5FMjYPnMAv\nhwtHKKHTu1d5XRUFWptvMxz2ODXv3cfFzwinhHEALxkr2p5dLxc5qq4sZqwB\nCFZcMxpaQ1Xlqn18efA8kSo62+zEbzaogAlrZLKbDb3vwKVfXDscODnaypiI\nL7br\r\n=3oGY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-rc.1_1533845170685_0.6092487833805416", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "95bc3225bf6aeda5a5ebc90af2546b5b9317c0b4", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-rc.2.tgz", "fileCount": 7, "integrity": "sha512-kavwJYTgdd5W12MpfMJMznrfsx3sCl0HMSJ0vsPcbwT9jhF0EmYL1eyfeYbo4pToYmQgPKp+VGtSHQ3wL8N5lQ==", "signatures": [{"sig": "MEUCIQCzOL9C/+ZMX6N3Bi3uxf1jgMXq7rZve94xzZG7YmkVCQIgCfLm8JxcSle0ny21MEP7v44dANr/EjbwLDqVJ0TD5Ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGZhCRA9TVsSAnZWagAAlPYP/iNlgtuS0h68poLPaLNG\nhFcIJWKQ6UkS6jagWjR7GTigs/B8D+ouq4ZhTIzwlWRj+I/V67vJu+r/Py0E\naKDsCBS/2vDnbGZbZAgLDWAswdCIS0JPpzmkQKFuhc7tCDd9OfGN3gQh61c7\nhgP9CaXOGZdzRbKYWMZKtS2sD1c69N0i8RfiR+e137vfrRCEOG73Xow1J5kl\nCkBvrKIV/NhGk8JlZ2YiJoGdIND2N2HTEpCOeFakCEtQXKlAISdbHRhwFDtJ\nOhyX4EQA/Yvh0yJD1YWRQSCSNW+rrwqQcL2IbWWVpDxsZ3ZiI+84J1EO0LvW\ns4MSy0IGqvoZjExaarBC1AnKC7QatxtscGeYbd7Y1+IPSm7bnEHBVvoeh34d\nHaUlyzoY5aNlXm5bNVm6uDVIIj2DR1Wm3tCxVam/E7jcs2WB2vxZcRKvLj5b\nxS+Pq67A0t1g0+AMa5/w9Lb13lGBKliU4lZRBMyD/CMFmkyLm5xlztM2qia9\nDTQEFeeO2M2HPHCVBaTgzSLLOqi3qWO3n+lp5FO5vNTCWg5Wsb0STprQ0gwM\nSyfrD9T5uohEpALZn3gPO9nEHfXCLBlDfvb6nL9O32jV9GP3QHjqTtyMoPft\nPdWVq7YNZXTRAOrjPawyUxEDbAuGTUIj/nPc+CGjNXTuvSYNoX3YzJ8qvuYX\nisgs\r\n=G0WC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-rc.2_1534879329333_0.4033633011575706", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f68392896f4f3b90bdf7e72e5cc7127cdd5441fd", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-7pGrHjKfU5CXXFD4H98RscdtuwPglO0vHFlF4SzTTwa19qtdTsKsBo181YXecY9yskY6dIqdjTeFLpHxrjDbqw==", "signatures": [{"sig": "MEYCIQCVQCR3EbhVWY1ZiJ+Yfcq+mjtf6TWsUG4cSRstPn3WzwIhAPnb+9EgNKCAWcA6j+RMY4erm1FdYC4gFqHzd1w7VS0y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkKCRA9TVsSAnZWagAAPCIP/iwX1BHdFaTxjMxHn19w\nvTD0eoyjOTq8OZhv+U4V3XOHGtscY8WW09P0Tr1p9IpFi84/3eap3heNuaRs\nNU52LNPpj0Jl1DOVPWEgdJ/n45ZCUTSs6sH4elu6Lutvpz8WDyQeH+OKsg2j\nlZCjGlTSRkbSnGDUg2jPnZWhF5vkCoTQBX76dd22/eOJ6Uo5XMihQUHL34uk\nS9tN06ZfIOylZfrGHI0Gtb/8M8ulvj5J8zeqld8lUsG0Jp0lJo8VIIGbaOOh\nWTA0tmWM8RlvORGsMp/XAqoCkVw3O7YDTkMQkZOsb/SFokeHC9agMS93nb3Z\nzL6LDg/IlG98ML/FgIxZV8C+Tfd/VlC8s0EtdO2n6fuur50+Cn4L8qCy3Dek\nx2MeYPM8Cz7VoPa61X0gOxqVAh9NwUv9IQ6wyiwZ4JXuz5wqqvcYB/ASKxMl\nNDpOsQWH010xhNzD2waesfCqdjNkH1bmzP5Ims0HXcdcNrRrhrT94/C+/KWm\njpu7OZXuQaudZegS031IJYbfK2KgmH6bwSuwTqcU1kyS5EKSk7+RANpo5A4n\nZbi8fUUkat+5luCuLnSmYZUCE8XU8lNIFHOeJqZai/7PipXDZkpRRkUvnSMQ\nI+aPB3wfw0iA774NQjMGy8jXbGnlG6QCpZ4w1ZkReDLAQCsSdwVTV8bKXtK3\nCfL+\r\n=Js5v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-rc.3_1535133961496_0.6641938472129634", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-plugin-utils", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "75fc55485d62174787cb5edc8fad1fc375b7c48f", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-vqhny/KiWG+zPrRA3XZP/atYqaB0GvtpzZiPairxv1ZAWUaDwVKuFxAbRTDHWsN139Wbtwuc8IuniKYl161t9Q==", "signatures": [{"sig": "MEUCIFbX2nlWxmIOGYBFQtNS0OZgNhWhS8Jq5e6aPHIv+Wn7AiEAgzOYDn4DMOxWuirMEIA0ALqs8NeRDfDHyRMyrZbW7Sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCnbCRA9TVsSAnZWagAA3SgQAJCmBmR2AabJdkCYLlcH\nntwe0V1rg0A9ypTuj1VAs6Btcb/D6Lq95KQNFGqt/PuwXmhdtgnS9V9VXt90\nSBQuDecuP9538Fibb0NiBq7vKZOBvB4r4fz2h8fLdHB8l8/HN/ERz2aEx+QN\naYFqQOM1N+BzEZCu5x2o/vF4TwBGgrpzmxpFJo1qCWtvLMjb1zKcvmIzQaui\npvHCLhnt/a1MC8S346HJtBm21enlgq0w8fPG3lla6h2VEZaCWsgJbX8a4ELC\n0OS7hwPGihZIm27GIJnv2SqFSgKsqzCG/Ng2avQ6dKUk6L1n0CdrVoLXdkD6\n16xlejTRmonWm2r1WfPN8LW2ruBu8yYcFLpBYrePN8cj/fDYWOtVoH0ONlSP\nyukkDcFfa8+XapJ9M2rFaUilvknlx0xKmCIdeBHnB1svsdo7aeeETjf5P7i8\nccGAT2AYsq+o0vD4Fbr8ZpT1nJ1JKv9svzDiEgIbl6sQi1q8WzdIFPxlMHgG\nCEZbw0gjAozlpagYbXL4y50upcG0VJfzGAmSrxOgblLZDanz09iiHveOXtC3\nyUN5YoAwfgmP2ay2CgLSFjzauqgnV10YBtnk+3muu2fEcv5iFpcnyzTHblv8\nhgfSK4+bRizJXYQnyQALy7BEKSOzMlEsOXbqnGPZuc+chPLAS9ZeWSHJJw46\nP4Zn\r\n=8cQI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0-rc.4_1535388123158_0.8609699064006322", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-plugin-utils", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bbb3fbee98661c569034237cc03967ba99b4f250", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-CYAOUCARwExnEixLdB6sDm2dIJ/YgEAKDM1MOeMeZu9Ld/bDgVo8aiWrXwcY7OBh+1Ea2uUcVRcxKk0GJvW7QA==", "signatures": [{"sig": "MEUCIQDa/pj96M0WEUCvVF2x37PW99kebb8oK1nf8jvhQT7BwwIgHWnFBhHlgkRnNqTHYNBCer5brhe1MvckTTW/g0xajZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAECRA9TVsSAnZWagAASH4P/jdeM26fhwlPct2Ys+d6\ng/EEY4YxslsYC2OzYCHFlQuI8n3PJnTsyzKUIJCKCK+NXslAGpLHUK1sQB5s\nGfqBh6JfTQsy8uA1DkhucE9A6le0Coqhhf1GG7F9rxVTFMQUbktlHShvddR7\nmD83hhFROyfPopUPAlhYs5SJ50fMOlMjIttsvygyAy6sfoD65IRXf89Ci+ga\nnoboWWIvEGG2k+ncz1qEI+NCytkbDE4vMqwqWDQbBDp/KUBMWxa6hA7GhCuQ\nqgHDXXVFTMJWWsmuitUvpYwwxHfLzS59PNPByV3bKVe8FvnhzKrUNnqRuNuR\n5WxYiPb/dRqSHEVO1TYfyetKNJjG//0eJIzL52aoVDYevvhApoSCXQoFOiYH\n6bj7S5F6DWWlg9DYtzdniMQ6aeenqnzchODDxYQF9zyn+PBFgPYh8kOeebg1\nNdoRJNHojboIMhmTiozSJuP4fPsNkd7bp3Qj4GusCKA4lqZG/TneaL1rC158\n3UuvYaWoKoWBxWymy3YweTSZ0qu0SxjrMVl3+gOFiKFFBdsxzaR7qFxVxm5K\ng2neeTckmPJBYcmkMgPxRoBB1IHhvueC+nkJMLW/fxeshWQU9SZTMVMxh1QR\nUECMDnY5kJHzKh8/mq6t4B38Bhx+rz1q56N48Dfw7J2tZwkf04LP8LArCmPs\nzA/8\r\n=gg9D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "description": "General utilities for plugins to use", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.0.0_1535406083544_0.2817009730233926", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-plugin-utils", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "59ec882d43c21c544ccb51decaecb306b34a8231", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.8.0.tgz", "fileCount": 5, "integrity": "sha512-+hAlRGdf8fHQAyNnDBqTHQhwdLURLdrCROoWaEQYiQhk2sV9Rhs+GoFZZfMJExTq9HG8o2NX3uN2G90bFtmFdA==", "signatures": [{"sig": "MEYCIQDAXMA1vnTlGDP3HHPYeKaAXYqNl5mOLsTcycq08VFFeAIhAPSqI2aM8Vl0IjOC142w9XcCcHXZ89mnWrsm96Z2u76w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6708, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmU8CRA9TVsSAnZWagAANAYP/2V+B3/Wk/idE8UdKvlp\n2UqQuZKMw5xs5m2r33BVsfsTxRrQ3/2p9kaQLyq+8uHIyWY1XjrXtF0clRKk\nHU2RgbnilL77ZiccKt5MeXZzOPQY4XqD1qKNsgeDXZkUjT6y7+7pXezIT7Gt\nfin9cz/xN6h7V30VUA+5LibAHZrjyA5LW6HWlJ2oIzGc899a2ZVDEojjpsNm\nXv92ZoyClnKou2KXX9ieUfRyWrCUF3VkdDsPDs1ROEJq3aADh5v152nItARF\nCmSlEDdyT55+f1OvPrAmyWIyWFbO7pCogtoUwOA6u2qHGP48VlM5IC+WU84O\nGPTMQnn35sy4FKBaNssXBlK/yh4Y/YvfrOfbu9NugPcoSX1lv317FEhn5Uzw\nF6FcofsLdIrWRWMl5d5qUPI2zOj1nkmsFOKal7UyBkF2s02iJAeHj9lF/OoK\nk7rB/j06JXtgBbrPfKTdWH21SnmohwscwTYxgAJaPMSqIZanAkiPXtfZEZem\nK+ZV4uYSFoBcm/7CGX5E2f70qHLRZ0booAaWEz3OsckC2t8PWaLPZrUH2vKr\noOXxSQRMncZYOHPqAShcDpu8LuSpnEuzjdGVXmwAuvVQZAyy+bJh0KGYSq5w\nz5OApNAETgzgjQWfYEn6HWfJE/WEpEkJ5kQNmp50bzdyw4fGnskFoMKnKhjL\nh9jk\r\n=kK5R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "13.6.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.8.0_1578788155617_0.9231882378183012", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-plugin-utils", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9ea293be19babc0f52ff8ca88b34c3611b208670", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.8.3.tgz", "fileCount": 5, "integrity": "sha512-j+fq49Xds2smCUNYmEHF9kGNkhbet6yVIBp4e6oeQpH1RUs/Ir06xUKzDjDkGcaaokPiTNs2JBWHjaE4csUkZQ==", "signatures": [{"sig": "MEYCIQDJiEx2LcQwql/qcUM54ht+rgqTJQxOW3tPYS7zJGXlEAIhAJVSLD9q4FXey9jpT6uG0utdLNdivXvSE75oDkSHlxKY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOPpCRA9TVsSAnZWagAAyf8QAINbLbKKmf3QOatCj9PQ\nRprZRlmtBbzCUhPeQ9rYT0LZw38QzriINL1bXKABwio8dKDsIJnTQZ/d1J42\nsgF8NwGqwNex3BF4EMYFo8vl3+3xcxjHUUdzXhUd3dLMbUbUSXD5buWUbNE4\nvlpdOTA3RF3rnP816+bCYkHmDxoS6XBiRm6KcEpQJJBSWY975GIRQxVSksym\nsI8aIMsktAbADsTqgE6IYbqmJTqO+InTAQLkdcN383mSLFKb3VSlhTH0Wo5f\nI00d36922lcYGKvzI2v5mHwJSdsSikS0Qm17yKfqRTqGHYLZdhPW6zEpCaKa\nW05vBDpvhIFIVpdyMX6sq+D7XQOB0q5cvUUxjvTQeEQbaqkg8zUyDoZAIEMn\nB4HOObuW8PlE8KMdPe38BseDF+pFK3foH0eInjX4WwC15JfQi4i5rM+63nUi\n3LhhnjHuC8IOynQrl2U+mRNpDPV/15FVIbUsk4Qg07zc2TxnxsIcsBt2zsZE\nwwztgZOt1CHoS2KPocsYL8v5fyUHOkE0AlO2E4o6T16Fo1cQA9OwPhzqO50h\naPCmRtOKam6/WbFLAqKwvuS6Lf7XRBqBLN3AuozmD4/lXXNtghv/qaF3CaZq\nn2zCouYmVJrvUx18JIlwY3A5TmRTiJQiDNrB4u8uFQF5kQ9n/bBvcMOBCoA2\nLzLS\r\n=jpR3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-plugin-utils", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "13.6.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.8.3_1578951657344_0.9958516428875395", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-plugin-utils", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ec5a5cf0eec925b66c60580328b122c01230a127", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.10.1.tgz", "fileCount": 5, "integrity": "sha512-fvoGeXt0bJc7VMWZGCAEBEMo/HAjW2mP8apF5eXK0wSqwLAVHAISCWRoLMBMUs2kqeaG77jltVqu4Hn8Egl3nA==", "signatures": [{"sig": "MEUCIFgTD1vFA/mmRWJzNu25ZT+v2qDQYjsCHJQpP1Tj4f3TAiEAq8rEf++9jlxwzqX+7G9cO8Ben3W8J/1c2AdpzaEHzIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuR8CRA9TVsSAnZWagAA1akP/2wgItQM67DA0kDz5qzh\n7acCrVnrNnvjGawLEb753aRAE2ayR+1gLaOUYRkQWrnP9lEQEIgC6B0cClgU\nQe+UI2HG9B9nkNDgq/F75tmbzQNNcXq013YAxe1xOi7j6mw7ZxUwkZCFFOd5\nCANoasEMKfZIjGHrKGkouM83uvpBvB1jECagHiLYpWSzDmcM2UiYgk7Y060G\nMLNynqqjtB9+3KW7Tt3URI32GS9PMt8MMwHCQkge+2BAKJHJ1W/Yy6cwm749\nOPcA8BRPZC1BltTPU6Sg1ddaKxZWTHw9Zo3rU7ZUosRkPOsIuo6yYmv026Y7\nKr0HudBITKlw40z8qjgfHFBW26xhAavMjd2LIYZDgI4Wd9Ykf2p8IdeVFd5C\nfzrLtj8QNrcUbgiIZHITpWMYZhZeJXNBaG+hnJRrX9PdNWBJtHNhDgDjTAUd\nfZzeZ9sNz8hnwO3TcR8cHODse4cr4vcg7cKWSl1rgYhhST203iob6xXh8I+p\nMG1wHFx05gFNwSaFC9yCx1H0yyUIyDj9T1n8wvyiEwrCrrHbn5I9ewbE4glu\nOoRaOkjjU1ef109J1qEjR/SR2lyUnyIbfENE7dQVFkXb5FRwZfcZYQlXVuN6\nLrQzf0EuvhYaW1ipGOkf2d0yknzZhkwMBiN0Hs2KIOuYTtaeznLhJpOIUCcQ\nJbmI\r\n=Hg46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.10.1_1590617211794_0.09473798339461625", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/helper-plugin-utils", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "aac45cccf8bc1873b99a85f34bceef3beb5d3244", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-j/+j8NAWUTxOtx4LKHybpSClxHoq6I91DQ/mKgAXn5oNUPIUiGppjPIX3TDtJWPrdfP9Kfl7e4fgVMiQR9VE/g==", "signatures": [{"sig": "MEQCIHvGNrSeNNBB8c4St/kL3/X6wfvL38U9WHLvBNApnXzAAiAQ69bJQ8WVfOHTljv9ecrThCsS9xlpC2gDtiLn7dRPIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SXwCRA9TVsSAnZWagAACkcP/icTKMZxtD2Z6CSIniSM\nOVI5PbWFIfoXpPNzcRcSezD30zYmc2z2kgkLWMthyGGWW0yzfflD6No65+bO\nzaeyDARhax+9wsypl8cgz24QI4Jxk3qGCypWJme5v+765w7CpLa+thQrrbps\nh7xWvGHAeP1O7n2wgqeNIyQ0OZxXYkuJ7JZMTsap96AaE0PIO678uwIvo4Of\nxOWrsRBcfneJ+466AXYOUJCPwcLBnYS0CC19SSPEtoQxJOqslEnYxk67CLAV\nLFTu1++Jtn7Z9K1eRxLUuyEgJyx67hu5/xKUQ8las55CNE7wXV5iI0Q+pavb\n1oIf++76Uay+wr65/WcGB8zcmQ/LX+5dKAuEl7fWlCXUg8WvxSVQSGORMbtB\nxjhelh0CfkSC9e/3XrT5D7j6msVwnnzgJvp0/l/qvpi6n3UmGuEi8RlIBvRH\nSOu2FXlE+75gMm0nSQ9pvE6sRrI8Btr0tZXx4/W8e1lPQVVY8pqPS1OiYOTr\n3FIvHup8ruZtSqQ1EQLN4Q4ebPaxC/1FLJzAGPUDmJSU5K2JoOaVpKvePkwG\ntPCW11pRGtgLXQiVhvofpzTRQq/k7eGy/Cdf1E5P8k8EMAD9pepd4hm7ZQyk\ndsUbobzVniSZMmqiuH7zJ7jgg+hT2sS2yZeMoekgxIMQU3JSmJ5fgvEXsLAJ\ntQWY\r\n=UGXx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "14.4.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.10.3_1592600048114_0.7033064330594765", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-plugin-utils", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-plugin-utils@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2f75a831269d4f677de49986dff59927533cf375", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-O4KCvQA6lLiMU9l2eawBPMf1xPP8xPfB3iEQw150hOVTqj/rfXz0ThTb4HEzqQfs2Bmo5Ay8BzxfzVtBrr9dVg==", "signatures": [{"sig": "MEUCIAoFsX2IQOVH90QM5aCzF4PdlPHYWWAQVDC6UfUhI41oAiEAypqIgapYJQa9y179c0rrDa0sOTZ4DoqxR4ifAQj1NuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zn+CRA9TVsSAnZWagAAMQUQAKTRNe2MfSVW1CL4VZH5\nWOukGPKSnJSxiy/SnP1vV0j/TRhaEhaNxv9j9eEN313yhsPIFe2xREH3Wg4y\nz+bbYaKHOh7wiNx3trUoQq3QOducs3M5K8zJ9YyR5XL8EcWr2P45fIDBqdEA\n2ax8C0qkH//73YUB4lCFaYDhRZNFjC45R/0YTM8KfkhtCWEJvqlnPhEdmMda\nf0fsOO6cQUc5wniZUc0BTVRON1XhugU3yXowHd4W13aQCq7wuOFT/khZCud8\nBC3smoSBlyDtUfLU5JbQXkqQPXWNKvRi/ZvAo9XtWfsK3sdtMUwHxIXhUTXZ\nScBGu8Ur5WfPKk9wrPj2cHlIV1p/epGXpVD1OkUunmbyIU6AqwD00lsIBTIa\nWgcbtpnizOjXZErdkhNxse6+FYJoptD0sjBYnamX4muxxa3rx46o8skRQJGU\nR6mLepQjpPjne6JSXV3cFIRxkfiFgl+DgBMNcjohcQ6NJhhCwPoo7M8bZspw\nqyIOQqIIIFTMcWWiDeWvQQUMBuM+K6aAO6uVTT/tk7AD/+wF5KyNQNJK0E/9\nuZ7vf5Q7rsRdn/GWDLnO+FS0WKAwSjuSd0Mys4Ly0Pc+E3VuP3pf3fNH/vJm\nqoIvkV4z+AZ2WCvWfXZOIy8zgMYFps9rnqxq15fAjgYwv3b+TY78M4BakY+o\nYVZi\r\n=XEoC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "General utilities for plugins to use", "directories": {}, "_nodeVersion": "14.4.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.10.4_1593522678995_0.665125199733456", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-plugin-utils", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "174254d0f2424d8aefb4dd48057511247b0a9eeb", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-C+10MXCXJLiR6IeG9+Wiejt9jmtFpxUc3MQqCmPY8hfCjyUGl9kT+B2okzEZrtykiwrc4dbCPdDoz0A/HQbDaA==", "signatures": [{"sig": "MEUCIQDBF6xrl2gROLQvBSdxJHLLmk8J5BnzDTVxKfWeeUUxkQIgS3LSNiq6/ojcgVJMTTgZtpjmL6UHh+Qauumd2LlQdb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffdCRA9TVsSAnZWagAAJvAP/0rpc4rRAcJForW4UN7j\nJO4u14bs14Vc5Xb4QRKKXi3yRh5RO6jZNS9AWoUnV7RZdHpURGPPT9pmAKfK\nOa9BUr2F5+vVaTjPLD24yKezwnlcQemtRW8QsMMiLwioHOLfKdJBp0Dr5t9C\nJuLCoW6f9ntZ8EMPgqqHWeTDj+MzQOh78M0YfaMxqc9C1Eofafxvfv3EiMZr\nZ8e7AffDASlguJaAUSuJZdzGwmNCmino9etVrOjtX+Idw4YUlzBF3VnKhXCP\nRGLybhv/upshFeX6dMzeru8MMAj6zWRnlxbxrmZBy2s9N98kpvCCZOIvAoy+\nwEIQvzI36vaHn8CMgR9zi9jRzWjfHDTy2vmM5MCSFKg9BsrtbTQYf+SwAzKt\niPAEDVMLx6mHQuxvYS0t/B5rKYHuzqwpoCuWQH8Eb3QUt4rW6BNHPzrsd/1u\n00fmgSqsbZoP3vcD1zDVrEceSXxKa4e2f8Z4OwQ48cC66KWRpwjiRxHaS6vV\nlDCyP+dj4vmQ9DU28RwrFtFbKBNh2LB5DpV19NqZoF5Mhc9J2oUHNiFkxOEN\nQ8RDTJv+cgOLB1FvM+Vr3TXmQJg+B7CKnBcePAfmXrknN2eE6oM79mE1WG2B\nJ8YYlXHZ+CPtM+rhTNhgA3Fx/Oa8biS9vl/zbmtcFwUdjtkJpUKk20tEM+va\nPumm\r\n=l7I6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.12.13_1612314589022_0.5729937767375968", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-plugin-utils", "version": "7.13.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "806526ce125aed03373bc416a828321e3a6a33af", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-ZPafIPSwzUlAoWT8DKs1W2VyF2gOWthGd5NGFMsBcMMol+ZhK+EQY/e6V96poa6PA/Bh+C9plWN0hXO1uB8AfQ==", "signatures": [{"sig": "MEUCIDxawUNG9ZEPwOH/eQnK0FwFWYU1fcnJmDuWcxJbtBSFAiEAi//CezA+OgQirdpfvEjBv1SpxUxlGpOWJckXNJ1T0wI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDT7CRA9TVsSAnZWagAApRsP/iiK/S570jgDICvSrINO\nD+o1eaBNAM+PxkLEpJbBfroCErpdMzzleJ+QPGpdXQwy9ErGwSMdznAdcmQr\nj8HBmfZU+B/ch10+g8QocYwYlLXZjTyNw/F4NyBawzjvZY7aWHlbIToLtGo0\n7U8eShsw76LeJRezGZzYzrnlhdaV2LqcL8j2TwfnEIdjWlS1V/IMZNIKCZBr\nY9XEduOEImoFQjx3ZNWnVI3hm/1cd92+3OI29KHQgCDDJAQnF4Hh5xVtdP2U\nRcLU7Oz1ThWlOHIZlPzviahDZeKojmJu4mGyP5YucHtm2C4BvUcqYXSmjnuf\ndHH0hY97jHTUTp91CDDN/SqPWwDsqk5zYz2bryHG6dCNOPvx9y3LYE1xwJe1\nCQs2JkEdsNqGLO3Cb/uHRifxB2B+7qSEAsqaCAq5COmw9QF/q3AvyGEFV+wD\nn7O7TTrah/hYgLRQ7On7xVQGkGkhqyuEDBEqnlbnPeB5ofSHzbFzLA5j6pkW\nnnXJAJg1O+iyV/hUpaAcNS+s8ZlF1vXjNgk7HrVUKbgyn8wLVJ18l9CEwX3+\nOX5j1lxJCBw1I57QOLaNZUrcUDtu7btl5N1y4X+sSMyVsSW2jxGawOOqtQNM\nGpat0NxXg72B5XFvkpv54oDTltViFrAjq6QVyCrVu0A44dZyDll22ROruZYj\n35Be\r\n=XvSs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.13.0_1614034170913_0.9832839550899404", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-plugin-utils", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "5ac822ce97eec46741ab70a517971e443a70c5a9", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-/37qQCE3K0vvZKwoK4XU/irIJQdIfCJuhU5eKnNxpFDsOkgFaUAwbv+RYw6eYgsC0E4hS7r5KqGULUogqui0fQ==", "signatures": [{"sig": "MEUCIEKTFnsd/zW8JysmZ+D59Xi2IAxC63oEIAOQUXStDz5hAiEAv1iR3PonixZ/uWib2vjIa1Z7z6H78GJW0dY/QTRuSB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqVCRA9TVsSAnZWagAAhWwP/2PhX3isRgJS5nOqTT/2\nZPX2ojIHyE7eKr+TkfbQB2jmfUGnFPMp6fhODz8wHCIKUGZx7sfVCzqMwCY8\nLdmgHKWgd5KAkW7l460XXmNLc4Lt96v1l1Sy+3Cyb6kCH62FYeg2huX7PkNY\n+x7fp71EJGHES0fvCj9ydYs0VOkH/X5kSfDt3vHYsX4OVkUjESBcdtgk28ey\n38Cx88ySVO6CT5xDKtLl1dwBThCu5Dl9vhbgw3qW26BxS+8mpGGNEz+NBjGT\nyD9WtHEuCjLCMmDr2flj4HdNaK8hug9KmlLc1n6YStTsYIzrSUVdYRIURpbl\nz1S+Xbt2ZUvQulfoNn+8EFhQM5HYaCcDYu2Wa3dOF4oKtU06qvNKNttSTaFk\nnvOxBdAfYTrQS0D25vIFuGRD8XKl/lGozAOYecW4ddy5qNPKIOsPkozJzzbT\nPImKWxv4/BvU5Sllg8ygTS3RKRMgU36rbBK7IZVUQAslZA6FMM43JyKQIscW\nmzftInBoMeD6CgrMwfd/4sJF4xdCRpMSdRVpymZu8qecyOcXkf3bVkKsvlzN\nyEqW7yv+72v7JDEFo9exIY2VV5G0LnIFk5zi46IoAkhzM2ee+/pG/T9OS7y5\nr6fuxcGKUPV+3ipbDw2BQFUBQxmIwEhNoBotDAvZPcg7u4pP3EUlh29jcYam\nz/J7\r\n=JdPD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.14.5_1623280277243_0.1747157300848292", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/helper-plugin-utils", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "afe37a45f39fce44a3d50a7958129ea5b1a5c074", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-59KHWHXxVA9K4HNF4sbHCf+eJeFe0Te/ZFGqBT4OjXhrwvA04sGfaEGsVTdsjoszq0YTP49RC9UKe5g8uN2RwQ==", "signatures": [{"sig": "MEUCIQDj6IJ1UHQqGv/K+h0MD6eDEs7d69PnvBiI0dHmk+bF4wIgEzkJqXdgqUPIJGG/HpevlJOU+FxVtaCydvS/Nq/Rt2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jTCRA9TVsSAnZWagAASLAP+QDvabHdPphxE4Tcaqez\ny36pwERKceumDmqWU3+ujKWWrojCPAW4tzsDLwJY1G5ehDT6KLXTOdjSBLY4\nuS507cKKa3BCOWDYcRL+aFzG7zdOkL3Ta57tqHZFF+byqlZbC1opW16oIpVZ\n44MW8uUfjrokcF5ibk6IzTolh5WjvclCUrs1Pt4yhdJy6Gi96PMaWKlQGwI5\nfAvm9wXzzQ0NyASFUV2wNgZ73p8WBOk76t9dutU7HHGkXo2QA0A3pixMI0Hg\nVqiiQ9kR1w2Es+DCBr1e8BfHp8lqZ9y705gvkpy1BogjGzkvFXB8WZiKtx2W\nhcc6BbUD8Vtyc45d9Rv79sQNCbLbt2U9ZoTbx6xL4gOLK17NTdqgGEnbS5Dw\nrm3vYGJXJJyIupdUMJTUh8ObM7TqITf7cupntOgb3nSIkngVOlM9fDhKxPEP\nyKAyZGvgdNdtw8JVKMmMsaFAgZEZBtgfbJF+ITZfNc8cdJ9rEJ+HiJtLdSCq\nFn8D19KgzEREeI62yVI00yPpxAhG4FQcdJo6wqWAzsMtQpFK39foOx5uA7p0\n0tlaS1o+29BxGGQ2/+XS6lQYq0uCjNAyBhv858z3ihb3+mGpePsSeF9F9zJi\nm5bFfpnCy2eR6GCz36n4NgAfxtnpYX0WH59OvhkBZLtjVSi43jH1UFdHJrr3\nifY5\r\n=VwCv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.16.5_1639434451281_0.4444836128605314", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-plugin-utils", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "aa3a8ab4c3cceff8e65eb9e73d87dc4ff320b2f5", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA==", "signatures": [{"sig": "MEYCIQCQGbgTvGfJZwna+LcLofjWMygg2adGxCxOU56TbDWl5wIhAON9MOFI2uBCoorkpsJOueuMK+4lDBOjXLHNywK3BlKi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzkz9CRA9TVsSAnZWagAAXYUP/RWBoryqF0swm13ckhcZ\nms+tckcY6ahnX+2krIZ44LvQRzrQ0pnqfd1XoQugdktVxhPjss1Loy+43Onx\nPil767hQ5Eie9iZq6zXo4KXrSLKs3j8VhveECTKURxn3kTCwwnFfvBLfOmHu\nO71r7IbxVgo/zkNkYa7OSuSr0NqFqve9TTb1qJVM6EOa1bRf6ZXwxwYClbso\nhrNttoLBpaVB6pPCDnDFDk8XniMbLnLjulN7w8Zq3o69RjuvZQWUyHKuZ/va\nToVgS9TPh34xVEnntjSvmxgW+5ymcuCpGKjECooSa+5G9gUEMMDCvWv5hRPb\na/greqk3kdyOfhJTe4mLNoWmgAJ/SCe1Cedf7VJCB4C1+k1xCLRDNYZW44xu\neL1Bc4A2Q/UaP3MeEQ1BG9AJGi0CkOBhlqCH1iBNvCBplgJpq5bJB3I17OSL\ngHXLXNvzedQJ2yeLpD/8v0WcrhQDC9447BvrV8caEwGvHp99VazhNRaBmtt9\nKiAWm61LDkUfHodpM2iBNWlifWy0W+cvhm8I+BNOoww+ckT9wg6oBeKACOrv\nDqXQRSvHapsOjqONBndA+ViJCV/9yOAVqLAC/Mnc1dzb1yYpOVz4e7kdGQrO\njaHDDkSypCVddtG7S9vjFhuHHRRv0s9Y3rOculCOFEeg1wcc0mwH1/H6IAby\n15+1\r\n=EmZH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.16.7_1640910077260_0.3493731869049017", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/helper-plugin-utils", "version": "7.17.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "86c2347da5acbf5583ba0a10aed4c9bf9da9cf96", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-JDkf04mqtN3y4iAbO1hv9U2ARpPyPL1zqyWs/2WG1pgSq9llHFjStX5jdxb84himgJm+8Ng+x0oiWF/nw/XQKA==", "signatures": [{"sig": "MEQCIGbS6szL2JQ399pKco1HKhxihEj33Z0uBcf9AfTP57gKAiBC3ays4ZwZbevEirjMFj18sXCTRFHkjYZDmR4ojezdAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqa8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIXw/9FZSFFMfC3JIeUrMcfbtW+GXK2wVlj4wwXb9HBI8IVT5JSGfG\r\n1G6Jbc+ZFL3iLm+BaMIjDc82TOriIaMXtHfDP9NDEBO68mfcj2+UEnAqINIE\r\nE8M1ahcuYeuL6uT9JHWLaEJzF4jWGzRUpqUd0PGa+nOUMI+v2n/F12FRdBpB\r\nIcR6ewxgNtd1KGMFhK6UQ2jjvLSgw+yavy7bbcNpAzhsG+KcvL0ikPV+9Nyb\r\nDZNj37MaAGezOLaxgYJ+ex8xX2PcSQ/MV85oXXSJIovyK7dpTzmt7pieg2cP\r\nOzAqYFGsHiplWrcJ0mZ/acKKJy39FTN65Gyshz803YW+wB3bR7jfUtVe1+WZ\r\n+HG2JLx6CSO2UOI7KtdW9X7hhB3wxbmjjDVcmJDysQA/xEzOsFEcek0TTM6j\r\ntLvAiveWEpuLmmPgXaxXSEGy+ls74D/Ix2V7824vUTI92pP4kJnXRBP4PBF1\r\nI28gQ9euOAGms9NQGFbWXzJbowyrju2T79x+88+rUyl0dilJoOh/El1DgNwJ\r\nJ8eR3FoqgPcK1NTdmrHWpgET0yakqEdSDDZYBu6xK9X6R3IgsbBb3ucF4ukP\r\niH7TUrfgPl8sHutADVo1yCH1PWtBodhov8GvhuOTtcySgNrs3gE2fFK2E1tC\r\nkxsGPQXahN9tDtr5IIQ2LGzNzihreeJkeZE=\r\n=LpYX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.17.12_1652729532647_0.11363822798370604", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-plugin-utils", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "9448974dd4fb1d80fefe72e8a0af37809cd30d6d", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-gvZnm1YAAxh13eJdkb9EWHBnF3eAub3XTLCZEehHT2kWxiKVRL64+ae5Y6Ivne0mVHmMYKT+xWgZO+gQhuLUBg==", "signatures": [{"sig": "MEUCIBD3Mcv8diK1YK0rCqU4Aroxi0aWi9Cj3UJyBdwbZqyoAiEApj4iRQgp/iv6cXIXeb3F4R1dQmNh8qWIfAH6oEQ4q2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8QBAAlDuQMe/0DvjR2ABesVVrIW5vWxlGgnHdUndzIxNsTSGkG/j+\r\n0G5IhFFHBEMjIoHnvEdEzAHDhVuiJWBluuAmkrHeTUECgYdpQOAB8XWla1jb\r\njw7C4QWKs8GUUwHnMci+64odBFJCKJ9uaD3T/1TS0MkEeAQnaDRUZEzFUww5\r\nIB0uvZs6NMbHrUkYweQJb/lM2EFqg9N4iadLhK9fVilavtGMF7Bb1WXadOar\r\ns3ZasNEChOUsntgUxI9gGXlkqN6e8npktYF2L5iUp4oCSZHS5DuR4vcGZ+L0\r\nw+/8YrBda53qXEuW4DWo0vzqWwQ7jrngDMyQa/iySrDc+NwCg/ZuxGySp8MH\r\nzr3hq/nB+QhLeeWQtIO0sYhZqosEnW3oRZvabPInpwlhaV/LT6oBPa7aKfvH\r\nuFrPuhZQFsREyKMlThqwYVwXtdkE5+Y1kHBnMQ+MZbI3RyBq8U8rjBlfTwPv\r\n+YnvxZG7k8Wj5AWOuGAhRQC3FaUVueq8qjwmQK9KhB8eMpgglj+KHofQ8fWi\r\nycS34JyHU3Vq1s4RzPWF++G8x7EjrAB5CbYQ7+vx+CRMssUY28s9+mirM6LK\r\nLGXD2GOx9qOV8Xgt2Q34TZ7cd3MF99TZvNNy2nOgwLlJlhWcU7eai/b9TXhG\r\nSNNHNvU++8gxoSkTiOeaqFPX6G0PuJzjOlk=\r\n=/irB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.18.6_1656359384803_0.1537005894427952", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-plugin-utils", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "4b8aea3b069d8cb8a72cdfe28ddf5ceca695ef2f", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-aBXPT3bmtLryXaoJLyYPXPlSD4p1ld9aYeR+sJNOZjJJGiOpb+fKfh3NkcCu7J54nUJwCERPBExCCpyCOHnu/w==", "signatures": [{"sig": "MEUCIQDrYcEmrVr32qPkGFJ+OXHzBmswNZdHaowwo8WGpFVEtAIgGPBMrwOlVJ5tcA5ihW+br4XF/sNjcUg6gJpM+IwpJvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhkRAAo/2rpHQz0Xy0Y0oz1PkNdp0Y8IiDx7HfCJ921mGLHdiaiCXA\r\nbjmPHlBSg8hMoI19x7ojFej3vRhWnuyZAcdZaRTWJYoeJMEcXJdCpKMTZgHu\r\nkuEeROuQOy59OogoP8D7etiBcL4vt4wsRTKUOF8r14BZhY5eoTfkVumeMg2r\r\nLXa+OAyEw9JrDXX2jaNs2tdlKCal22yboRZx+f5x2DGMu5PmvyNOTMGuP8EA\r\nk1KZwMqdkkWkvMM4HjRsUvvYE+nIiYofvgzVw38U0ZMYQxo9B1+jzGbC9EXj\r\nb84sfTxh1LtEKkPxWbhfG2ccKZDKH4EUc3hmxxYS4zWMVQaqkZfNUFrw/NI+\r\nUaBnh2LiVHUcu0J7aRgQcJVqdjHWzmV3W671xn5KmOKndyPcjODX24DChJ4i\r\nxEbeWxi2RAZi8/NG6AL6wsANS3R6ZRZGXEZZYqXxwtDV/CiZnfeXWAWStoWa\r\nI4qRdm3J1B0L8eZbWXuhj627LIuUY9dmqpOTGmTQEmOQhTBKSuzJgfoLd8XM\r\n7w7WVlmnBMpNgxfqqizYE7eN69CxwfOgjP0geyg+r3J2+lbdn0qARrEvF4GD\r\nL+abBkHYm5O8enOBlcItAdYRb4bHGIjgDL/Tx63HCNpYMkixiMaYlnTVFyGj\r\n3xdq2W0khHZwNWgviB06LJTSry4x3+isFb4=\r\n=oIMt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.18.9_1658135843449_0.14623929861866602", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/helper-plugin-utils", "version": "7.19.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "4796bb14961521f0f8715990bee2fb6e51ce21bf", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz", "fileCount": 5, "integrity": "sha512-40Ryx7I8mT+0gaNxm8JGTZFUITNqdLAgdg0hXzeVZxVD6nFsdhQvip6v8dqkRHzsz1VFpFAaOCHNn0vKBL7Czw==", "signatures": [{"sig": "MEUCIQCtGPCNGVqvd1rAWinbHCjn07DptrrmI66/gdoVJAOfogIgJ1si4IHdekbw5CKpLV9s0woOTtuAffBN0XEeDcxtxoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwgw//TF4GpV6TRloVo4UvLRlpxkpRWBVjUMiWbnNVVODGcaENG7zO\r\nE2LJgg9XiFtB+66a0r9HZjrBDvn60QiX8/6Yn3a3Ds5j56NNAabo68yXwYEo\r\nkmg4SNQggnQ6wzGvUkRLSeN91tS4FJ9Aw8abRk7IH3Wrb8ufQU7D79UfGHCX\r\njHUxXznleLaCPROoOqCpFGP8TOAyIhrTBmknsAn1uR9keEBfeE4/10DtWy0r\r\nO+X+k2FhABuHgIE8Renty4SW5FN9+7jXbdhZnxGC08XTW1DPC97c+iOEN0HT\r\npNxGk2CyFAA3EWU+snxcC+HV82P0qmqG5obPuxBfDCUXXekzgDMwu84Xpx3l\r\nXzeVbUa8BPveWgJqhtf5XSW7UuHzu44OnLRwPIeZf3MYBWmAy+jhxogwPpzl\r\nrlCcfkso3IEBLGYpn1OjOD6zr88Gq3LmxTLHGMYH7zm5+UFrBvuVVDMOJ0Iw\r\nrOKXCwySkixm4pSVlNDOqCXpWVPRvBhjADd0+9fTtH6ACvzUtWMKEjlTi9Yj\r\nAqZ9hA82OoYhvDbhPXba2+5fsZKG5yf8KP6ut4qFwa879m/HWhUTW1ZtEREb\r\nHZbMpN3sgOa3ni4SYROPfsjY63d69pekyMzh8/45G9DMF9J9yqX1l2+fXQTh\r\nedXCmTbB8pgOmh99iRDEbQgmEXQjOIUsxus=\r\n=ybIZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.19.0_1662404532939_0.32857535595911713", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/helper-plugin-utils", "version": "7.20.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "d1b9000752b18d0877cff85a5c376ce5c3121629", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "fileCount": 5, "integrity": "sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==", "signatures": [{"sig": "MEYCIQDxMK7UUd6Yng2oaZeJcxFBFRnVRUupzV2lo+r+NlJnzAIhAMh3V8HSv/CWQiVhiCId3Uy2t8/edYtqnEhHuiqziQHs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUMxAAlQtMvQbNJWd7xPzcxZf6fzBq6LWndrBeMPugrJLAzpzcqWPg\r\nEanjQC7j8LHrHjhm83BmeOZPwSYX9G0E+s5m+Y6qAjZTz8usNiAEUjrC1QRN\r\nkcYHn2njYhMvHh5dHlNEwNnUtEPwIhnlvrPlPqRhBRE/q2OPQdf8QYY3ACpq\r\n5MA2XUHdWgH145OSC/PCtd4xjDZJZm9NKP6boIS8+vaPOQUKLOBDPXUoWADF\r\n1HDUA31LsAFrn25we5Su5l1ZzftcnVQSHlyCBKD2JwM30Z5Nu8Tw9u5fW/Tk\r\n7E+tPygNfPLxnJ6DiMaDWXPel0vWGk/+X+4b0r7G/rbfWq6gEjqAaynud8Zt\r\n5hPrT1nDvcLeh/BtY1nCQhUYonICLt6KKanYW7Gz5KFIv/7gJCTGSRsVEC0U\r\nXDp06+RavNOeOuY6aVLhmC2zqF0KAHC2h+vIFlgxJb6DVsprqp1ysOe/IsWf\r\nE4jKpQ7TT5PREyeD94l3areh7zVP52MPK2hB3fyLT7SfhoCEE8Dt1wP29fyW\r\nyHz0/aHLoSGENH7PNjELYZU/WBTgSDGUim8rAVRhNVRBriwgoIaBiMBgLu+X\r\nkskkej0sh+33zChhfZBQvXSJslM6MB5ElgKsf2BGWVtGMB7zrMfhrSdpL3eD\r\nPL1I4J407LPdIusgWwP2VYoFtMl4Svy26TU=\r\n=eMcn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.20.2_1667587862557_0.5644344601331943", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-plugin-utils", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "3062d295a54bc5d4e2f19a4a948bbc12b186ead3", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-lFdugCin1wDED1T6i/IUg939XyPJX2q3jjpGbB9AwxA0ybJMlQq8ltYyv63vkT3J4qRbU7t/TVEyiMB64W74vg==", "signatures": [{"sig": "MEQCIHsajKDcBscx1JBNpIzIINrzERn1S40FFFjBHWyibU1xAiApYpWQB/c3I3BWoP67L2hob1gvQVk2tP4xq4YuWX14FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC98ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprRA/+MbAsMcVsQIMle3Gp2qjuSHhNYLj2Yka8+tbRvLFMF869J3aI\r\nqMhDPlUEDxBcMSa7xCB/nfGWkLZerQlCyjOl3xFpE8trRmCKDyewqp5osIb0\r\n9w0mKEbMeG5yB0dlm23sBSy8Ah6eI4FAEVNgqZJUpqBF3OxkIADfqzhSuYD3\r\nacsmssiBWO66gV/1qHgW4KXt67fjag/HSiINjpghn+30QpA3iOOqe6DbHx2W\r\nUDwnPS7NYqayABQbhJ02lNaSaUm4vzDUq+midfbky0WHLhTvewVGeQGCyd6M\r\nZDqpKRcAg9mVX/wdSu7Bc6thvxRXtyZHl0CERY2Xf0NoQrpPVpQie2NeMEJR\r\nz4XwaApjHekuJzjk8zg60qeKD5qIBUOoJ4xVUXJGrzROFkJ7EOiRHSOq+Jus\r\noY20NFpRinEy+aao3d2cg0CRjNBitoKFGYs7TlSFJ2NxJUJrMe23GznVdD8r\r\nKt2sHAq//tH5e2vHnwVxCwlrP0z4GOUPXm9x3niitpEeS1gYyRbMAu3ROOXt\r\nhg9IXLSyUDBmp/HodRAc6F3YMe0Tc2PH8Xrrt9jJ67Zj747MUzi/zly129wg\r\nHwwwcIwUS80vA449kyVLZC5qaXGINwWHUvmEO28wAEKObq+9mSlDLMCKI8Pp\r\nLVc9RXK6TxCdms849xMorpPsycCkZjUvtso=\r\n=xtmg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.21.4-esm_1680617340505_0.021286399786184296", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-plugin-utils", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "87568cb9d1ca639a9453d5590af91afa00a3ff8b", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-iXlIysiwXjYPzOYgQZYeM/sKGOOoqDAOLWmvJDaq5hwU6O1ZJqRSUzv7gY3+/BTKkEOGf95rff0hic2yHUVEPA==", "signatures": [{"sig": "MEUCIFJmuMGHxrnWvQe9fnnRVJAH4d2LsCVarXZQWCZ2D7YgAiEAn/a+e6y/Fvw2fSGHgiuMPYtR/JsI6mUz2zE3hceALQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPOQ/8DmAdI2rSdiwBa6JtC48x+KyeMllUPsIisQBEA7488yBSGhG0\r\nC76CZpeP9nhka89BdAFbeW6K70zvGREJ16SfAmF/GxVcE1b3uGjgP8/GNA0J\r\nqInUJehkrGStQwHI611SgSB8TDMV/5iq9dK+lPGiBsDH91Br61PYzbSrSVMI\r\nPZuB5swYlmpNtirD3DxfdZQaeE21KtM2Pwb8g2y52/nsknnmXi1Vve+12v61\r\njdrUigVVjcxz+agI7txIfeYdq4VJOcFt9hYtp20YVZ9VqgVb6jPDsru0bT+K\r\niBLjNQsgpV0jgm9F9iJqLsS5VYUq/b5tBNNDPhOjHWQRAaJdcI30mx/uhseE\r\nbwrZkP/HAbqzOJ38FMUjTS2r2Zl4d/g4smTTMt26elmAUaJh9dP1Pc6R4BrB\r\n693MM7+Z8nHubXwxHNJlPNQe+GgAjxRyuCqOiG9hCRqq1FHFuLKa2/ko1RF4\r\nkKah3eHgj1h/k6JUYZYPDbJCT3JhkerntSHq6IoU9RACcXyOb0+i40HYUY8s\r\n8OpA7A5IukoTE/KGA8QNSW0WI3mOrNVTY9mlzPHnIq3R3JRVsl1su1MIsEFR\r\nkQEtPlYl0QuQQB7xkozoLpdkJnksxNAuYXc+mrfkZVVQk772X03JOcbFXoSx\r\nWRYeln93u9E9DatD5u8SyKInMzPxfIVZwlg=\r\n=HK6o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.21.4-esm.1_1680618052477_0.42451374571547773", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-plugin-utils", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "669975b59992e03614119f30dcad14a656ec9dfc", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-eNM1Vu8qRZiqsvnT7ebnj8/N9IGxMiONrNxkr1OiqdJycizNqxRsRvO/5naVtKp39OjsSCnPNaxLKh3RfIiIWA==", "signatures": [{"sig": "MEYCIQCpEnhqnsr8UQB9aOiNq/zwr4JQaNyshM3q85+OBXK4PQIhAI666j3vPcepoDnYU190LJkCfH/pt5XVIqiT9QQz6PdC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgLQ/+MZ2Jl//i5M07n77dlcrU4nOnXq7F5qgqZL5lJWH+G4c3EoGX\r\nONQgS71g+lAsv6E6PO0IbPWgFrHwMxrwhper/xN3SBdavKcKMR+TEO/1Xw2c\r\nYFxHpEcUv/jImrgxVVr5GDsVeB7P5VPmvLZEhMjRxgm8yzkIa+ptnZE3RUNd\r\nbA9pAV2CgPhziMUKaNcOdQGcY3zkiPvDln7qFrjtbDfwQdK7ZsrGFauEW/8T\r\ndw1OB65qHHXZO0acnupweKbNjJ+X2Xsvp+73UmrULwxtDkOJBMGXqrxVQ8OQ\r\nGPQBGusOCR8al5H+yJF0A5PJ2WgFLP5P7OpxgreJF/b9fIBKaM9Q6DK2MPlW\r\nk39rc2lUTwMbtQR/MWo5EmUMXMQ15uSQrciCvps5jSqro/mYUFxMO8idSuaj\r\nrMtVMWnPPXI/nkJHXgOUAg+jsIbesexUucCcnoExg2EmczPDBnWi7mvuATkG\r\nj4Um+MD+DDqSbxTnduLkC7qNLlGqLuyFbeeIL8R+DZb0BVGehitJgyNPljjR\r\n3JPf3niHlZZa5UvRX7EbVgLtcxluUZwnLJy1/UYYJgiORI1Bc+hA3ehYxfzY\r\ni4E4WHKIlcuEFDdBsL5GQkEJPLUbNjOdEIROx/ORScnYYLrPVXu6i0MbusNM\r\nlTE405TMa0gEIdv2sQcAHPsMMjrGFH/HbLg=\r\n=mRoN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.21.4-esm.2_1680619113994_0.2799787576945951", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-plugin-utils", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "7d09e51c3fb17dace91e374bac9a316abaf96b07", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-J8FnkmPIBb3vUzcKfygGztmvoYnxYPNA7Qse5b8rCnonppAp1R9Aej4v/ExaZENYMSvBKDNQ+HF3Kl6Kno9lGw==", "signatures": [{"sig": "MEUCIQDDvXUii97h9jOx/d14iwaqeDcbS/F92OrKjB/KRBuGTQIgKD7Ip2VJdiWoWzDhtujxzzZ4ZyKkCr4oK8NuG0cdh5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDpzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg+w//ZSEIz9c7c4EvbahnyQDOcukaZ/HMdzvp9Hncjx9S0DS8mDnm\r\nOOOif7XCO/5ww62VCViymkwK5J8+8ZuiJkeh40ffB6OGyVslkpaKKN0bf7lC\r\nT0gwTCwp1XTE0vOtzhNLOZrY23AFouJfgpZ2wDZIiov+c66V5dNTgKJ0Pl6R\r\n27GI+0cQu5rQZGdaR9bhR66aoowxAXgS6DtvVxtd13znvJvHhXg06M3IN5ds\r\n77JwdnD3uBaKt9WqZaJanf/5y64PkY/ZFcgrYaOn9bEif/EJTJGPMNLrIbZm\r\n6f/h9MZoxChfJAdY5ilJTh8JKYX4oSbVuayylZOO7EEOD9RkahWxVlNTsLXu\r\nSvdf7rZAlvcGaUuwg4aosROQtGUd0AnF0J0T/Kt9nMNR+IxwUis6aagDaLvi\r\n265NBcUFK0zZR5SLzBjrl+dDmamCoPq1Aos3hL2ZVuyC9m+6NiiyPX4nX1wp\r\nbTa+0tpQjNmTkiL3vreyPi5Pu6PSJguUiMIKwfliH7kIX1Bgjci+BHNEF+pS\r\n8zPKrwHNT8qtcDYy2m2wQjx+R+aLxDvl5IG6pUvK2cfhxpx6o+IVf21k1d+P\r\nrSvXHartsW/yo/KrfCUj6QzoLvSy7xfkaolEDwdwnulmYMCXRcN6Wgnh3JUS\r\nCO8gM87Pkm6cc2PH0q0xu5o/7XiejxbtGhM=\r\n=L+dw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.21.4-esm.3_1680620146986_0.34115128563966457", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-plugin-utils", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "f2e88b65f723afa33c1269e1d58b529cffb0e701", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-sBuQrazjJ1uGdLN6UhoUnBcC71cP31ATgVUn+LXCFA8PThJJvs44o+rp3t+5kYwRuhOUvX0H45T+HSbcrsSg1g==", "signatures": [{"sig": "MEQCIE0HdHl7AIC7nfOlLNthCrSGWZ/VrD8LV44PF+sJsWkIAiB3kvEwGydRPdPN4rlK9TTJLSdzUTaitV88gPzpYypCnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD59ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdPA//RjWqrePMZxNnyGa0BGNI462ahUmXGW/kh9g/HZIHK2w1xUiT\r\nzbSeuI9kxJY7Kkq201MTLawDmEyYMPN827scQ4GyQNFjKkdpiNnFvBHGbiJD\r\n1/RkztIzUtHl0Z3Bc6NRQEISES1srbkBwWm+R9NoqYED4RZ8stGDOXioswUL\r\nsfQszcpiZF4SZttGeVrsNxxTqRPIh8mnmPJjax/J9U6uuF6yq+0vOHC3pAz4\r\n7jY1+aDw6H1Pf2Zd0+rcXHiM8NYTYjPpP32KGgEjjFDq+n3R972FKEz2NHoB\r\nc7ZwujCFfYyYgpGuoecTkcr8+MlVRlsLx3hOCbJG0STPx+zQyNkdf8Jk/4qv\r\nSi1y5bBKCh0AKBlZBcFVWVfvs33IQ+3lzmhEfWpgMO4Lix8kAoQcfeuC3ll9\r\nDy3yFtDL6OPdzsJp7tugSqUaXMy6rSBzGlV24tg1AiJXY7I8jY0sP/3XOFxx\r\nxKcTVZVBKdRrOi7W2B2U0rKDQCeuJ1sjlRP/o/vRNGH5LiqlFqfuvUi/j0Sa\r\nU1qtGXJP7/AD+qeCyK3Bq3bGTDw+vouevK633x+/FF1PHbBt7Q7SEJ9WhlCN\r\n3iyk6VI6hLlMSvgfoiO1yR6zdIQFwTft96ORUBVdsLuQfYfAWxAb+VIYC+77\r\nlBjNs55Xs2AQqhZWeiumsIMkrEcVx9UITCA=\r\n=6KR2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.21.4-esm.4_1680621181468_0.8960583375620432", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-plugin-utils", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "345f2377d05a720a4e5ecfa39cbf4474a4daed56", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-0WDaIlXKOX/3KfBK/dwP1oQGiPh6rjMkT7HIRv7i5RR2VUMwrx5ZL0dwBkKx7+SW1zwNdgjHd34IMk5ZjTeHVg==", "signatures": [{"sig": "MEQCIGXIY2RFGnGRq6aU6pFx1n3f/ZpUvHMIHgwDJAPNkW/LAiBZ8jd3Ef+Sj8wFPR1EqrXMCEaXLjlsc3wokL8zuR8z8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrq9hAAi5qMOwztGdZksP0SXlCny4weJrv9zKoUi6gB0vs8iS2N23dF\r\n7uw7P1qIynys6eTu60Kaxw1nmqTNf8ocJrkBWQO6sIqmQZ2rjMJ4DNiAVy1m\r\nxtv6vC+KvqodM8RC8FMkQOe4Y/sdNLqzJIrdpCRuaPUaQeUqjIsCcH7Yx1Sl\r\nCRUvitS9FI5WYw+Vut/fVV9LbW4O27UmUXd30m6E75d85qZPM4SKYb/CtsG0\r\nxdGM59hVndPQo+dGbo+ExUs8W5f20dgQK2myqHjGsp0E4e9a5ByANi7z1cbh\r\ndwYxMt84GCv57nBC5M4SX+1sofBEWWfOBJEZza3llsyIxVjEC8TkY4UzyJw5\r\n9HovLMvGm5Jc/BltEfHSxlrrOm9bIA4ercQVr3US+BaUlJwXqTet0hVHt4Sw\r\njPFWOo0S9TH6EdPprRMhohWuzvVRuX7BOMnygU9OmA4A/+C6x0pi4iq5U/0W\r\nVLYoPwkm3lajSiD2Pf0Nn+XE2KdZQTHcXPhZCrlThsYO6awMpFL02TAJ5dsu\r\n+D99LVynOB58g57YM377pkTxSVwsTzQ/Lf+ezRUft1vilp31uCWlxFWzfUvC\r\ndc8bNvh/7GSq11I/Eg8qMgG7qACQZWzHn/5F0nnV7tFQx/ePcfnt+bpNf8Hr\r\n+wc/kdIyLmDrI2UMjoJ/dIUE+zBKEsLaG1E=\r\n=FT+w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.21.5_1682711415420_0.48936110691836276", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-plugin-utils", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "dd7ee3735e8a313b9f7b05a773d892e88e6d7295", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==", "signatures": [{"sig": "MEYCIQCY8kBh/ohQtorLn72c3Z/n3vYtsBjtqd5RPwhSNeRuhgIhAJGyFG640dd1OArXa8Vm02M3gUDGivdcuGRrETBywl9N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11909}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.22.5_1686248461118_0.723561754034286", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "c98deca0a82f38e4ea6f378fbdc30d799ff8f06d", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-nRIAUq+TjhnuYH25FpC0wq7fhcKplpZ3tpAiJ28BIrdim7hWEliANm4JUwu9l+I5Vkf/73uqL6YvSC4ht0lIZA==", "signatures": [{"sig": "MEUCIHljQOWFtb9S7wowtJ8xpZuWpLnJLQlLXn2F/JHZ4y6PAiEA/9ZDerMr2CACKvEHtvXGZAVOLks1HDbBk1oDL5dmD98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.0_1689861573758_0.3496361469989968", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "962f98936f7fa8c4dd7af2940dc6131ae3f5e5d9", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-m+zK45DtY7FCyNLxNEpKRVkWvE3lFAZYBwtIeogko7zQs5it4pKYFID8TpYlTiU7IYMcYi5RGK51yDQ4SgnUiQ==", "signatures": [{"sig": "MEUCIQCQq/78574MnOskYUUum3NqJ972Eh4or+Qp2WWBZMpC0wIgP/3lQC50DdKKwrPzoHQQdho2D2bBjvYwvKtt40esf+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.1_1690221055192_0.6410301120368775", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "a4647f5bf6c6b46366d3c22c2d9189313afc7016", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-6Y/nOckcFUtD5cXD/ruLiPMBHJ/S9VJSCrEQqqXJiOOWxI2ly7Jm4fnjvfLoW7ATsYmAIOpw8/n3CF+SbrW2+A==", "signatures": [{"sig": "MEYCIQCZcsgxc4pfG1wdnK/KSdpKtrQY5rF5lGWlhdloonvI3gIhAJyFti9kT1sgAP5e8oXxft3vOWVZpSUh9AkLz2e53Ul2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.2_1691594070297_0.7058238229957017", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "8dc62dc45d3e570f2114c18cef258b9aadc2decd", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-b7AIvEij5IeRjXnuL+sfk2EvYMlgOTNEDktrcBdMAvaUY77Kwt5oya1FK+rvDmnP/SEIAiAFZBky6W3WDNVRPw==", "signatures": [{"sig": "MEYCIQC1kj7a59V7E/TA/HdyHLogZsjNV3Ed1X0nWzWa2LD2fAIhAOqkrBAFfKew74Gh09vgpPbpxb/PI2Tk836unZ6v+a46", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.3_1695740184448_0.22757453013864404", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "b84b66091009e05d12a69fdd89bb9357616cebe1", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-v7kYGhNiGJgKySn0O6cVYdXQ+EuQs8955HkjjFf1AUCEOHVz9sihw3AHinPcN3zQMasHbB9lCje4gLPyDfNg6Q==", "signatures": [{"sig": "MEUCIF5TNOFkwO5BIIOoU6M23kZjrbf+Btw1pA6cAba/YNX+AiEAhKaJELXG3lrhyyTmG7EDwC/g9r1s1n/CfErbfpLBFy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.4_1697076352341_0.7560508445130496", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "8a6119694dd013569171f0aa02bc9a8cadaeb452", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-HoL/u7CJ2sF/JKO+mvTyvxyhJ92qpJNo1gdzyMpwLUahRj3DEQp6oL7C11sTZGDtKyBuj42z3Z79LspTgYC9BA==", "signatures": [{"sig": "MEQCIDH/jRGtP4KZ0LKvrsEj82ObT4rc+6H07d7Qlix3HFmRAiBj6Y5JH2fCXtuk70HCDYGGWecRKJlllW3ZuOf7FWFVjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.5_1702307895092_0.23766047283974712", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "884e80702bc2bdedc17b758b35abd5defd117772", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-AXFxpgcVDtryVdtrOml85yOqfq+kJTYy1pnzwyZEE1qqgGfujrxhFVsymcu7BKtUOisxVp7b9cCoEjHVMyDcqA==", "signatures": [{"sig": "MEYCIQCA5WoQCk1FEcpdfK9x7J09oIGNLG4sFxtJjTcuofp3QAIhAKwvPC9qnElw8InUTHy49fFdYhKlUwzWWc4m0nktIdPb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.6_1706285621050_0.392547181518212", "host": "s3://npm-registry-packages"}}, "7.24.0": {"name": "@babel/helper-plugin-utils", "version": "7.24.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.24.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "945681931a52f15ce879fd5b86ce2dae6d3d7f2a", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.0.tgz", "fileCount": 5, "integrity": "sha512-9cUznXMG0+FxRuJfvL82QlTqIzhVW9sL0KjMPHhAOOvpQGL8QtdxnBKILjBqxlHyliz0yCa1G903ZXI/FuHy2w==", "signatures": [{"sig": "MEUCIAZTVBFs7wTGpA7xST668z+1RHkDc7WQnf3cv7EGAwubAiEAoxLB0MItzZfjfiLJ96CYawMsF1SCCWyG7jiBD7rVqTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11682}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.24.0_1709120852079_0.05022303382418869", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "8bcf0eb8e182c3d8d63e01e391083d37ae3b4e67", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-t4kggmkLiwz7ZkdIvdwknk1oQBOJ7K12G0ciPdNYIKspJ5p2v/NATkMJy4r8SH3Vk1cUtIsFLKbHYNNSAh6RzA==", "signatures": [{"sig": "MEUCIHD/lquSnrBvHnwg3aNqjGGeSpoDgqnBCdG2VQo7xAACAiEA53wuiQvQCNZb9lSYhAM7IfmIsIL5nDZkJ6VyAwJYF/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11382}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.7_1709129045769_0.5099496532971806", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "57fdc334e2c8f28157763986bb7e0614df2ed6aa", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-PaRMRehL3B38njVXPFFLyNwotavJZBFyAYI/h26NJ4ft0ILGS9tmhTERa92uDGRX3K0pDtsyYwo9++4NjcQRNg==", "signatures": [{"sig": "MEQCIGtaAh+EKaIr79gbB9f8hTsb6XSN/W5SJc0BRJROqICwAiBNbN4sA9z//Vus0xliCZjYF11Swblf57IR0SjRBUR6bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11382}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.8_1712236760609_0.9496953217146769", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/helper-plugin-utils", "version": "7.24.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "a924607dd254a65695e5bd209b98b902b3b2f11a", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-xjNLDopRzW2o6ba0gKbkZq5YWEBaK3PCyTOY1K2P/O07LGMhMqlMXPxwN4S5/RhWuCobT8z0jrlKGlYmeR1OhQ==", "signatures": [{"sig": "MEUCIQDjvAybdtJ7fYPtcvZJlHC06eWX9ZFcdQQcACrGTpKFhQIgJomuel5NA69aE5ZAGBLSLyBTB3cgehkU+MlKzKhBZKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130493}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.24.5_1714415641560_0.6584483353107078", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-plugin-utils", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "fa02a32410a15a6e8f8185bcbf608f10528d2a24", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-MZG/JcWfxybKwsA9N9PmtF2lOSFSEMVCpIRrbxccZFLJPrJciJdG/UhSh5W96GEteJI2ARqm5UAHxISwRDLSNg==", "signatures": [{"sig": "MEQCIB/t/m0tDfnO69o+VpsNoT5tK+9XOGMdJmkaPwsg9ygBAiBEwDh1Et6JjwmDjjWittlfqm6jgDyHbKOXJTlDmU8SFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126963}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.24.6_1716553447883_0.42370085782634637", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "eca6820f1492ee90078e7e4fd43d1e6a9ab09df8", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-uAiC8P0Yj1q9OxuMlHUNQI0TwvLxSecAE3g12z6KeAD22RmIUOhu4TL3cYOtTufBpwm8raCdmQBK4/5W/14/8w==", "signatures": [{"sig": "MEUCIQCn6ix+3drKS7iUf3jEIM9lHZbB9YJvKB8Jduko2PZVZgIgb82coAWnSQgRmDc4eBeKqpYL+Tty4YIP63qLIUizzxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127261}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.9_1717423425290_0.5504505800448365", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "dd042d8e2a7c8ebf534266c77bd869e61fd1658f", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-GRG6u6zyQqwhXvLL5FvKq8CpQkKr3DYIuYZChVWYGwrFdOgBXdC4FegpI9k68uHG20T6154T+3JO6A1klJsEig==", "signatures": [{"sig": "MEQCIC7T362KbxMJSjgrskq01da7MSwJ71weHZLSsShd4AelAiBGNPRhZ9uIb4JIu9MColpVoWfQwZa/BEJKlrF/+oMfBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127264}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.10_1717499978964_0.26554084294413105", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-plugin-utils", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "98c84fe6fe3d0d3ae7bfc3a5e166a46844feb2a0", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-Rq76wjt7yz9AAc1KnlRKNAi/dMSVWgDRx43FHoJEbcYU6xOWaE2dVPwcdTukJrjxS65GITyfbvEYHvkirZ6uEg==", "signatures": [{"sig": "MEQCIE7DigZrVROkriJw7znz7/tpBtPX84VQ+vCteRnB6BdLAiA31rDn5Biczcdm/iSGTikJ3wMPzyOnwWr0dcnzZwyjaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126889}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.24.7_1717593293732_0.3019594444009108", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "24c325d47daede21cb312d2f79c81ba427768220", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-qr3dMDRS5eVtxDSv3ZPP3oAf6W6XaVbpiAucA8w0cS+eyI/r170hq6nWzG/Atl1u1my5OcDVdCWp3LAbqI0f9g==", "signatures": [{"sig": "MEQCIEizz9rET97phRBikmB+0RMtKE5Gb9t2KaQ2lviHohf5AiB6AYPDCNKlqbAq2bXg9QHqkD2Trl3PY2ANPAR6ZXJC3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127151}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.11_1717751710257_0.7977654655620827", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/helper-plugin-utils", "version": "7.24.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "94ee67e8ec0e5d44ea7baeb51e571bd26af07878", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.8.tgz", "fileCount": 7, "integrity": "sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==", "signatures": [{"sig": "MEUCIA+LeOqDN6iyoAaSjZiU/azwUkp0WzNlxpNXWurRR0ALAiEA/4RcEDqIzBjHLegd8sd3HEj1CyByxFQ7hpYHBkkY8rw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114337}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.24.8_1720709682666_0.012883708124451498", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "524f71628be81923b32a51c0cac3f6a6852b866b", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-h7nCo82iLQal+ZvAXGVJB3pqny1pHOyNbIjCfMZ/ImAcmTayAT4+WuFSIbR5Rgmjp1YYTtT0MTak1NDyzpLnkw==", "signatures": [{"sig": "MEQCIDstrRDd2Zc2/PPnKeUVbDfIts4ZqZLMMGyJszUH3nGeAiBgSwE93VhsFs+E1g3iOAlWe3xsBnNq7kZHnDQ2D5YVnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114056}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.12_1722015184918_0.02831284368636111", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-plugin-utils", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "8ec5b21812d992e1ef88a9b068260537b6f0e36c", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-eaPZai0PiqCi09pPs3pAFfl/zYgGaE6IdXtYvmf0qlcDTd3WCtO7JWCcRd64e0EQrcYgiHibEZnOGsSY4QSgaw==", "signatures": [{"sig": "MEUCIQD9bu9Sz8wY95FOJyuukJNjFoBdqEf7hS5T7ibG7mNSQQIgRRpVDj0CKHErLk280EIWbkT9TGI+Yll55IVqBtUhkdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82191}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.25.7_1727882062680_0.29727907537678044", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-plugin-utils", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "9cbdd63a9443a2c92a725cca7ebca12cc8dd9f46", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-kSMlyUVdWe25rEsRGviIgOWnoT/nfABVWlqt9N19/dIPWViAOW2s9wznP5tURbs/IDuNk4gPy3YdYRgH3uxhBw==", "signatures": [{"sig": "MEYCIQDhFRraHAf2uc71s1sfdHAIn2MgZcmtiTTb6XZcgQlSzQIhALEsjWYScUGw5TyE07O4fF+4ABX04oYc/3Mrf2iyAg7Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11975}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.25.9_1729610435460_0.8126190818715417", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "6eea3a2e0406789c2224874e419f8aa2f3c4c35f", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-ivHj4mbuxoLQie0cNdzUyOT6fHPi+vU9eFZeNpeh8SQD02V9dMt0buBouFDshwgK3YJUfl7Xcu5MVvGmgxgoAg==", "signatures": [{"sig": "MEYCIQDuHC4Sj776H8lk1tPFHYdQt4Ucw2Nk+kdWObRDT5YKdAIhAPzkYMFysGPI3+0F/605b/ZkSFyI64YRHhXNsx7RXlYO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12250}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.13_1729864422274_0.10906037778195588", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "aa1834a0a919550af850fd90cd9fa89ee0624397", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-ZqfxdbbuYmosKddlBzaGJR43oFQgf/uto80BJnLkG/wGbKPOKodxUgvNVB6R9pF2U7LqWLbTxIxKQqRNIq4I0g==", "signatures": [{"sig": "MEYCIQC9lpS8rfQxCivpXAtydIXzVoReNnr46xZia02oh6OPFwIhAOrTpu+7DwfmS8jIXS1Atsh66UaChIahcdX0WnYJPfia", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12250}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.14_1733504014279_0.7177945900723908", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/helper-plugin-utils", "version": "7.26.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "18580d00c9934117ad719392c4f6585c9333cc35", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "fileCount": 5, "integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==", "signatures": [{"sig": "MEYCIQD9nMif9Wus5K1/iK6bILn3pPIDeDLksnleAYpaAJxtJgIhAL7npnieaYcWgW36EhTRghxQwIfC2kcwQXooRFeaz52h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11985}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.26.5_1736529106142_0.2868547498856979", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "844e8e99676470bcc4fe318ba222b6df29704f91", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-EwnJymOvHSGT0NklZ6UaVwNj/CTOogR0cXfqcZ/ZXqOrU4NV2RftatuJgUGI+rRcvyLtQQsjHJWEfEqw1bD7Wg==", "signatures": [{"sig": "MEQCIEtbNvvybn7c0MuKGuvKGZYS4ed5GsctUsWhxLPxbxH3AiBTNaQvtjs08hNWMnRHaebkHZimY6lzJV1m2+TvuHbTsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12260}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.15_1736529837719_0.6796884228722768", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "7a689965bfa10c4db3021f9793fe8bb053b2fab1", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-dQQ9FNIO3kWD48IXcqU3uTdUesZEqfuJi97dULp1O6/pIeloNK1jIFAUHnrfbiFW3u2TQFVK/B8l43ReQaywEg==", "signatures": [{"sig": "MEYCIQDz71NY5OOaf5XgcTcrRpkKkWfOccT73VVuYQs/m7YBBAIhAO/em9T++V8wRzMZdz3drM2jRbxUY4yfMnAlTZAjCFqD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12260}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.16_1739534315984_0.16949598288212142", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "0be42c9bafd90a89dea1b5b954cf7de8d5b1e663", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-SgUHTwqR5f5TGRciqFF2nkp6hVvH8vcoRleDI4QVTb/2XSHmJhqug+2/cjNeRMIF62Pc7fcf/SpLQk0pbNSNNw==", "signatures": [{"sig": "MEUCIQClxj427BfDzRKcLQDt4yjPc0pi2ZyMvc43fHDOdaEQFQIgG+ERHwu55OIlFY9ebyg5co9GkEUBcqtA5kAX7Ki4tGc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12260}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-alpha.17_1741717465718_0.7339474764555429", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-plugin-utils", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "ddb2f876534ff8013e6c2b299bf4d39b3c51d44c", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "signatures": [{"sig": "MEQCIHyWTTzLzDnbJAPtKUh/YaBhXsUEpWHWF5q/509YcBnvAiBwh/evxFVD7DCEJvZCNce7jQvI0m9fNC0t7V+nwV9B0w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11818}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_7.27.1_1746025706216_0.41633030727504083", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-plugin-utils@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "dist": {"shasum": "4994b5ca9eb08ee1705e3ffdac6e0ccac9045eab", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-jvEVTJ7ify64I8g6N9fFp0MeN4/NwBS8qokV4d6LrAEpvjC2y6fB6tokOCCoFiadua2by8KJJ7k3Bhm9Ibe3YQ==", "signatures": [{"sig": "MEQCICftQ1jqb8Sew0L/7774wgblJ+sXkc+isAdFvSfcyzAmAiBq/buvwnCYYIwerbZX5ZfquaAjVqYaf3tWFmCCk5y/mA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12243}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-plugin-utils_8.0.0-beta.0_1748620238837_0.15887822346925695", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-plugin-utils", "version": "8.0.0-beta.1", "description": "General utilities for plugins to use", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-plugin-utils"}, "main": "./lib/index.js", "engines": {"node": "^20.19.0 || >=22.12.0"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-plugin-utils@8.0.0-beta.1", "dist": {"shasum": "7dbb337a3bc462a73bdfbc3da1e837f448749022", "integrity": "sha512-r+siQA9WFBy4l2jiqklI4o0k9kdreYXLoCMl5zME4y5+iw2eeKSopNCouJIO3n1g3pSPVMV9zLSNt5IPh5QKHA==", "tarball": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 12243, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIA40Cz5g4mJeavlULMQhYo8NmtdsUJVmHz/5LHxqKAcIAiAAwvLVvExtTBfHsUnqefb8Ey5SXqU5GiFVd4ttsPbPTw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-plugin-utils_8.0.0-beta.1_1751447033932_0.6658890596417928"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-14T16:25:30.768Z", "modified": "2025-07-02T09:03:54.306Z", "7.0.0-beta.41": "2018-03-14T16:25:30.939Z", "7.0.0-beta.42": "2018-03-15T20:50:06.394Z", "7.0.0-beta.43": "2018-04-02T16:47:58.546Z", "7.0.0-beta.44": "2018-04-02T22:19:40.729Z", "7.0.0-beta.45": "2018-04-23T01:55:19.338Z", "7.0.0-beta.46": "2018-04-23T04:29:46.009Z", "7.0.0-beta.47": "2018-05-15T00:07:00.237Z", "7.0.0-beta.48": "2018-05-24T19:14:29.222Z", "7.0.0-beta.49": "2018-05-25T16:00:11.230Z", "7.0.0-beta.50": "2018-06-12T19:46:38.511Z", "7.0.0-beta.51": "2018-06-12T21:19:04.053Z", "7.0.0-beta.52": "2018-07-06T00:59:04.811Z", "7.0.0-beta.53": "2018-07-11T13:39:54.226Z", "7.0.0-beta.54": "2018-07-16T17:59:45.089Z", "7.0.0-beta.55": "2018-07-28T22:06:43.345Z", "7.0.0-beta.56": "2018-08-04T01:02:43.797Z", "7.0.0-rc.0": "2018-08-09T15:56:03.471Z", "7.0.0-rc.1": "2018-08-09T20:06:10.779Z", "7.0.0-rc.2": "2018-08-21T19:22:09.432Z", "7.0.0-rc.3": "2018-08-24T18:06:01.588Z", "7.0.0-rc.4": "2018-08-27T16:42:03.252Z", "7.0.0": "2018-08-27T21:41:23.600Z", "7.8.0": "2020-01-12T00:15:55.725Z", "7.8.3": "2020-01-13T21:40:57.481Z", "7.10.1": "2020-05-27T22:06:51.974Z", "7.10.3": "2020-06-19T20:54:08.330Z", "7.10.4": "2020-06-30T13:11:19.110Z", "7.12.13": "2021-02-03T01:09:49.190Z", "7.13.0": "2021-02-22T22:49:31.068Z", "7.14.5": "2021-06-09T23:11:17.413Z", "7.16.5": "2021-12-13T22:27:31.425Z", "7.16.7": "2021-12-31T00:21:17.415Z", "7.17.12": "2022-05-16T19:32:12.782Z", "7.18.6": "2022-06-27T19:49:44.954Z", "7.18.9": "2022-07-18T09:17:23.597Z", "7.19.0": "2022-09-05T19:02:13.092Z", "7.20.2": "2022-11-04T18:51:02.728Z", "7.21.4-esm": "2023-04-04T14:09:00.626Z", "7.21.4-esm.1": "2023-04-04T14:20:52.660Z", "7.21.4-esm.2": "2023-04-04T14:38:34.210Z", "7.21.4-esm.3": "2023-04-04T14:55:47.180Z", "7.21.4-esm.4": "2023-04-04T15:13:01.639Z", "7.21.5": "2023-04-28T19:50:15.534Z", "7.22.5": "2023-06-08T18:21:01.333Z", "8.0.0-alpha.0": "2023-07-20T13:59:33.940Z", "8.0.0-alpha.1": "2023-07-24T17:50:55.373Z", "8.0.0-alpha.2": "2023-08-09T15:14:30.532Z", "8.0.0-alpha.3": "2023-09-26T14:56:24.637Z", "8.0.0-alpha.4": "2023-10-12T02:05:52.556Z", "8.0.0-alpha.5": "2023-12-11T15:18:15.317Z", "8.0.0-alpha.6": "2024-01-26T16:13:41.194Z", "7.24.0": "2024-02-28T11:47:32.236Z", "8.0.0-alpha.7": "2024-02-28T14:04:05.965Z", "8.0.0-alpha.8": "2024-04-04T13:19:20.786Z", "7.24.5": "2024-04-29T18:34:01.827Z", "7.24.6": "2024-05-24T12:24:08.037Z", "8.0.0-alpha.9": "2024-06-03T14:03:45.462Z", "8.0.0-alpha.10": "2024-06-04T11:19:39.152Z", "7.24.7": "2024-06-05T13:14:53.965Z", "8.0.0-alpha.11": "2024-06-07T09:15:10.414Z", "7.24.8": "2024-07-11T14:54:42.824Z", "8.0.0-alpha.12": "2024-07-26T17:33:05.064Z", "7.25.7": "2024-10-02T15:14:22.902Z", "7.25.9": "2024-10-22T15:20:35.656Z", "8.0.0-alpha.13": "2024-10-25T13:53:42.475Z", "8.0.0-alpha.14": "2024-12-06T16:53:34.468Z", "7.26.5": "2025-01-10T17:11:46.298Z", "8.0.0-alpha.15": "2025-01-10T17:23:57.886Z", "8.0.0-alpha.16": "2025-02-14T11:58:36.156Z", "8.0.0-alpha.17": "2025-03-11T18:24:25.908Z", "7.27.1": "2025-04-30T15:08:26.403Z", "8.0.0-beta.0": "2025-05-30T15:50:39.011Z", "8.0.0-beta.1": "2025-07-02T09:03:54.089Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-plugin-utils"}, "description": "General utilities for plugins to use", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}