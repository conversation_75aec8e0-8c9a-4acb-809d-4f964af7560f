{"_id": "@babel/plugin-transform-export-namespace-from", "_rev": "33-fe34d28e8b98644558fb9a7c2f4307e1", "name": "@babel/plugin-transform-export-namespace-from", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "345a8ebdfaf9f4ed667ef99d11e39f6168b3df12", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-NkqdpxXHZG1CbXuu31weYMjAOeZ785n4ip/yXYg/4oZxdCg1jH10iR7oPJbZeyF99HhnTxqFnis3FTlpnh5Ovw==", "signatures": [{"sig": "MEUCIDDVvnB2z7CPctzkQ4N2k2ngtTVjzUXWUycW6kbqSCuLAiEAxJvXDLatbkb8J4Wti27AIO5Puw/hRrRYKF8b/s7HI1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8120}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.22.0_1685108714544_0.8882790859921661", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "9b8700aa495007d3bebac8358d1c562434b680b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-5Ti1cHLTDnt3vX61P9KZ5IG09bFXp4cDVFJIAeCZuxu9OXXJJZp5iP0n/rzM2+iAutJY+KWEyyHcRaHlpQ/P5g==", "signatures": [{"sig": "MEUCID5KY2YzTqd+QMXp4GJo4GQf7NzQaecMjTuEzPUp9ZOcAiEAzwscnYIaoOi4CAr/mbOn1zeCSbvlOM1Vf3S73r98iwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.22.3_1685182255443_0.01606205414158146", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "57c41cb1d0613d22f548fddd8b288eedb9973a5b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-X4hhm7FRnPgd4nDA4b/5V280xCx6oL7Oob5+9qVS5C13Zq4bh1qq7LU0GgRU6b5dBWBvhGaXYVB4AcN6+ol6vg==", "signatures": [{"sig": "MEYCIQDHyuFa5mNqsaOkFFHK5vzCXPy49/yuYxQMDvJQx0ZaCQIhAPMsHOmOyCfmridBwzSqipralq5XSFaxy22rh+fgMYKY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.22.5_1686248475230_0.07095767685128074", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "c12e09e485fe47071bd13c1454e037bec6ee0e7e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-swdgz9DBpsqcel8Usqt7rd5i01gQHbwHJmVmCASvTYV7tdLl/nwIEghGLDHo8XkZJYvFGt4Qjgv5Hvw8LSI+AA==", "signatures": [{"sig": "MEYCIQCgEQ/VUTpRSGeLT4fSPVWztjaWI8JJXmtTm37Q8lv4YgIhALjQhqREmzeXamhzzQ3RJMyjTCCIYoVoQhmsHDibMqa3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7868}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.0_1689861590355_0.1895000864370151", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "4b73626ab6ef9b7f1420a9252acb27a77bfe0259", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-qypTcwG7TLfaiE1Zh7NBSwQxk3AAwbbET7FTt6H3UUYFzEKM0IlZYpabjrlP3cW401vKCCUcijere4soKaAgUg==", "signatures": [{"sig": "MEUCIFcwUpCU8EF4jrfv0Eutc1PZHGxpGyv0OaFBXbv6aZXJAiEA6QQjMeLUPVADVoXuonGr6LQYfn781BFglModBpO1N6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7868}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.1_1690221096670_0.5270773343425712", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "477a5519474f00bc65f4ce6f4547e1fd7e39d94b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-1rIDqE7cJGqAcDhKiPJbjU3tyqfmz+FLnFaB67nidOQ/ihQureN6q2iO3firmfDVaiSZE7xBq6HxBmyfWWDlDw==", "signatures": [{"sig": "MEQCID5kSreofmaAPUeq1eIDObi7wQfFtGaA5Wt3882viIVRAiAn97cgbhUqA1ex2pU2h4ys33984b9xNq6SB0yAWU4W0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7705}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.2_1691594089781_0.30743734985689297", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "b3c84c8f19880b6c7440108f8929caf6056db26c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-xa7aad7q7OiT8oNZ1mU7NrISjlSkVdMbNxn9IuLZyL9AJEhs1Apba3I+u5riX1dIkdptP5EKDG5XDPByWxtehw==", "signatures": [{"sig": "MEUCIAO+/3TA8lcaW7gfFO8QY70gLSj8OikeVunspLxRRATfAiEAk64SYG2Bwww9TzdQ+V85BNQEzRQLTKPxbqogtt5dR+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8040}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.22.11_1692882517931_0.5819019998195907", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "c1a8cac48b74d3b84d8bd569aeb1354d43840567", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-6Pxybnj4YeWm9eS0/TZ8qykhtCCsYIqQ7yPH7qoZlU7RP90huUcRPF3aSzXBrzW7Z0Wnjwbc1cV+s9JvRt0wFw==", "signatures": [{"sig": "MEYCIQD9CJkkRofdjdtkCbrprGASi4q1hOv1FVlEe/oIgwqRTAIhANi2LJgdDmodM4bWgIqeVkN5qvCZBL3vBRxizgV6Hr87", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.3_1695740205869_0.5747591185760736", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "cfe1d8b547d8ae11c2f6d77089cff6774df49c8a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-********************************************+D8A7BRlGJ4yfPY2ijt7PXJzyT3IezTe65t8k1uD/Q==", "signatures": [{"sig": "MEQCIEQPJI7r4DkTYFvblrh97Q5qmjNY3KVsureTH12WXSd/AiAjQ35kqzucIuIFBJH1ooOcDhxrdUI5qjNt3z0zwtRj+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.4_1697076371228_0.9660166486377817", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "dcd066d995f6ac6077e5a4ccb68322a01e23ac49", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-yCLhW34wpJWRdTxxWtFZASJisihrfyMOTOQexhVzA78jlU+dH7Dw+zQgcPepQ5F3C6bAIiblZZ+qBggJdHiBAg==", "signatures": [{"sig": "MEUCIQDplfSSrnWNF2+Cs0/JYMAnwo+t7LtURD4g5KdO/PqlmwIgRzG7VOLOW/7RXouJZGjcYtjoWb1aTtfYJN90rjh+Ae4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8120}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.23.3_1699513429243_0.5226933882793794", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "084c7b25e9a5c8271e987a08cf85807b80283191", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-GzuSBcKkx62dGzZI1WVgTWvkkz84FZO5TC5T8dl/Tht/rAla6Dg/Mz9Yhypg+ezVACf/rgDuQt3kbWEv7LdUDQ==", "signatures": [{"sig": "MEYCIQD5nakG2tqChPwJ1NSvdleEaPVbPpzjjLjoSYnp2MNorgIhAPXrkd9uOjJv+4EAcq/wQrZssvCjStJmLyJ5jGa9MxWT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.23.4_1700490127052_0.8652175469811147", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "e6fc76330c504e91bcdfb660c733fd8ba5c05288", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-ckc6VQkpxLkkrkL4Ks1amryYC8vAJf9GtkNYXFG2nDrYTnFnvviVl0LNbC9P7vjkZQvow+lfVpXSSAksNmT57g==", "signatures": [{"sig": "MEUCIQCz+V9N1mwAzFDwSdNhgyQx/M0YrpveAEn9UursepgiDQIgBzQ9LaHdhsQbU9AmMtVEHuxq8rbPQeT7EaLu7leccg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7866}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.5_1702307915423_0.491031309312673", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "e5b5f715365e15ac6808adab98043d39adc4fee3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-DRDNF96nLuPq/bkYNKaF/RVG+TPCuWY/6MFHaDd4NNx85rs7mh2lGQpIqiY/8mHeHIAtlYpgK0OwgQLqxN7O2Q==", "signatures": [{"sig": "MEYCIQDdabUw+BU1FUg+MDbaSg/ctVWBEkJ8J97+otTxsiSRAwIhAIPEw52/Y81AWSazMLyvJTix0QG4sqDSiGYEcoAX9Y43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7866}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.6_1706285636710_0.5276350905043716", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "831b6fb58714f332ef3450fc3a487effa028ed84", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-RvBJrQJEAk2agi+j1NMh2fz49Gx1rkTCffl+10RmSM+NTvVCxDWx3uxDPbVmpZFFpqfurhm9Md3uB/aKvb0V6A==", "signatures": [{"sig": "MEUCIQD5k7zqby6RrYcWh+58fYe+0rne+T2+tvTHC4u4D20NfAIgfqxZHrOWb6AeNRVXdDDkAx+AyobxIfdpodBdvJW1lvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7866}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.7_1709129084896_0.22154440142813447", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "f033541fc036e3efb2dcb58eedafd4f6b8078acd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-Ft38m/KFOyzKw2UaJFkWG9QnHPG/Q/2SkOrRk4pNBPg5IPZ+dOxcmkK5IyuBcxiNPyyYowPGUReyBvrvZs7IlQ==", "signatures": [{"sig": "MEYCIQC16i5itPsn4lwlF1m25rCfiSBStLcU645/w7epNyRN6AIhAKGkGeGZJZZhLFu22l7NY8zGcBE/7FpuWw/JpLU+bfj+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8186}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.24.1_1710841716361_0.7787681711406693", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "04b1acf68ed4ce6ffdc1253f4367afbe2e04ab18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-xFCMM51CZO7FscqNe0ZhvkI5GKSa8rXX+SMBIZSls/GDVSHkHUvnrqqJP7/Gtq4IZTtXJp0LslNGs/TH0g2zqw==", "signatures": [{"sig": "MEUCIQD3an+SmAMtKWFR5mF+GkczwgjmM0KdTEddtSc6YdfOdwIgaCLjwc9iN2XKKjKjlhwK8TE2HGsqeL6DjD9OSFK//kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7790}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.8_1712236785658_0.7920637210510113", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "b64ded74d9afb3db5d47d93996c4df69f15ac97c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-inXaTM1SVrIxCkIJ5gqWiozHfFMStuGbGJAxZFBoHcRRdDP0ySLb3jH6JOwmfiinPwyMZqMBX+7NBDCO4z0NSA==", "signatures": [{"sig": "MEQCIA2s25FvPLO7e5e8KAd6+XcHpWmKjoHpw7uCZ539TA1mAiAc69wVxvJA1kwJVmcqO6cJt+RZ/HUZnCHsCdpqK/wP3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74125}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.24.6_1716553466967_0.6611742728135706", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "144895e9ab9f959fe0b0c25705dc744d764d8489", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-Il4j+890xbUWecNpN9Xrv3x4HHFHGIAl/TgHkWYrlJyhueyg93bS9wXzsFvC7WRfeq3CwhxDZbplqkTAlJKhQQ==", "signatures": [{"sig": "MEQCIGcgL00So5O4tbs5AxFESmNw7q65mKrQiZPcIvElB7h3AiBvNiXeqrSFE0vdB/NW0mjQoXzGnjXtnJVnzHsFO4euIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74039}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.9_1717423450356_0.1071761549792225", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "fd17f073324025ec1e69ba2afd5545ceb3c1b394", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-VtlLBT6tRqOl4zibJ/xC0zi9Nsqe5yTTFL9PM9TZFYJ9sqqsbZbAD1IY/ALWQLm1bAk9huS4d4H0ifrXQyrcPQ==", "signatures": [{"sig": "MEYCIQC2ScGJgwVfO/Wu53Qh4qa0O2+gdvJmm1SE31apMAWADgIhAL9hifrL8RTFuIiWEatDL7S56JE899pa4bvMGjefYiOe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74046}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.10_1717499997871_0.11343205791608257", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "176d52d8d8ed516aeae7013ee9556d540c53f197", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-v0K9uNYsPL3oXZ/7F9NNIbAj2jv1whUEtyA6aujhekLs56R++JDQuzRcP2/z4WX5Vg/c5lE9uWZA0/iUoFhLTA==", "signatures": [{"sig": "MEUCIQDpaRqfC1sUqYAUEDn187qcUelgnabhXgToVrv07+XnewIgIcJ+CU3DbEyaBUkz/cmcxdxGfcVD0Voavu+RvI9lmzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74121}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.24.7_1717593316954_0.20694210892544973", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "f63eb7c320daffeff44a878d9156e945c75a36f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-7FLBg1hvbQ5EemYJR/7zub7qYQ39J4YCdyq964pk4pZSUeNlXal6TX/tehugPlwz4wEhU/wVtiLgB6qlLw5I0A==", "signatures": [{"sig": "MEYCIQDdHODAs7ATZ5TJ5PTAnqNcs/mo2xcVMDIdUk84KWOr3QIhAO1m5rF/URBBZKAedOndR9w8VWhXSRF2D01+yZb5MohQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73935}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.11_1717751726777_0.6376893909114902", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "4cf916cd654f7a829d4d2dc28c83be83bbb43ee8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-OLfV6czH09hNvnHOAzo65aFodiWTPGDPPylVPiqUFCGP+IDJrjWeDi9oOV7Xu+TjHN8H0jU0OelJKhHiaffFrQ==", "signatures": [{"sig": "MEUCIA9c0zHRZtK1BkosNkWg3SyurZea8/iHfAC+0dJTWhsiAiEAmHE9wgQZDnYsiUJdGaAmURLiJcYMN8gfKieL1CgAMrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70731}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.12_1722015203354_0.9583509974993698", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "beb2679db6fd3bdfe6ad6de2c8cac84a86ef2da1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-h3MDAP5l34NQkkNulsTNyjdaR+OiB0Im67VU//sFupouP8Q6m9Spy7l66DcaAQxtmCqGdanPByLsnwFttxKISQ==", "signatures": [{"sig": "MEUCIE2eCEXoFpdusHM/DIqIZIFxvr20Fgk0F7gK//Qeswl/AiEAlisuVj30oMbHj/U4MGdXGykwcGObfe6GRFEhZslCYjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78659}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.25.7_1727882082909_0.25326611686326794", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "d1988c3019a380b417e0516418b02804d3858145", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-sPtYrduWINTQTW7FtOy99VCTWp4H23UX7vYcut7S4CIMEXU+54zKX9uCoGkLsWXteyaMXzVHgzWbLfQ1w4GZgw==", "signatures": [{"sig": "MEQCIQC97eZg9aiso8dNz50jZ6V7/rVcZyEWDdoAngpGhVIHKAIfemDZYelYF6yskg298285w2OvT9VhfUgc+aRWJ19tTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78963}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.25.8_1728566710571_0.999887245902892", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "90745fe55053394f554e40584cda81f2c8a402a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-2NsEz+CxzJIVOPx2o9UsW1rXLqtChtLoVnwYHHiB04wS5sgn7mrV45fWMBX0Kk+ub9uXytVYfNP2HjbVbCB3Ww==", "signatures": [{"sig": "MEQCIEUacl21YOUH32N3xgIGwX8m+utD4R8nN5+bejnp0IF0AiAaTbqM1Fnz0ZnNMaSpdo9rZOcsm93Ir5QjJpZZqOr/ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7991}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.25.9_1729610460679_0.7060265707789248", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "046bbbec894fb0185b991ec6cb3d34a9a1af12d3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-ija4cWKm9jT9OAsyQ9iRZRdUUV8rnKgfUO89L2PstyD5ChRjHZtOilobPNf/SZZ1HLvOTd6sRgYn7DQ+kjU8Yw==", "signatures": [{"sig": "MEQCICP+wjOtN3RKOHLAvPiafps78tUn9izht30Is29XqeFvAiBze6fTHJSPqfvPnACfE++M56XyZTvA+ilYkl5BEeGKrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8057}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.13_1729864444720_0.9195495753346816", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "0c1267f296b00df54867b38b1639331d2d06dfd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-UB/j9AeqVWfJJEReSaGz0Gx8TiXJNV6T4YL+GMgfPv8UlbN9tctnQ/fMgiKkokGuv6/zk0IjnYHDewFCuy+PxA==", "signatures": [{"sig": "MEUCIQD9SZ9qUvdvZb6ebdgwDq3ytoFvtqduchp4BJdzaXVgagIgY+dllFdhUQ3EW7BT9FGwRTA3RymF/e+UpfcvDe8jewg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8057}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.14_1733504036032_0.4485301446327832", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "29fe1425d6ad07c6a1f8e759a8c7ed7db92a1403", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-MrXzMZehrk4V8yO15radXhW/DueWvvsgddHt2mv85VBY74tPImsowXMB0+pFWK3mtSLBkrDTMlGnOBeT5UpVvg==", "signatures": [{"sig": "MEQCIGByBzk2sPe6QHLdYnJ3sCQm+DwJgIl2+ZYzZxJ6gWt3AiA6GXZoN6QzhAklu4+zhzwG8CYF8oHPNPmArHLClJQWOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8057}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.15_1736529860846_0.9532242155056387", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "12bd5cebef0ddd61abca22fc80b3aac53c578e51", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-M8bLHoCyG/WP/+UqT+Ip4BllH+QerpNrTjus4ilTaOs57rCCrm2dk9O4gVYr6ui3E4fiw+PW3zgWGDfjE5NEBA==", "signatures": [{"sig": "MEQCICLwNDPB+KHlmbGdD1+LKPxiZ8w+xxcVrrC5KkSq+73JAiBXjr94Loal6MUvqx8XDBCHG5kShB/1mRdCPHk3s4HJ9w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8057}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.16_1739534337387_0.8134793892246901", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "fd9b3d3513f6399ae49fbfc7af57d94572930b75", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-lOvWO3G7OWSbMui3BAaIUXNjaDOeZHlCxBYzOK9MOQMEDvBqrCJzCuF0TT3GqPQnLpurlIk3BWbI9bKJa+Qk5Q==", "signatures": [{"sig": "MEYCIQC1ULC8v8JgGU8L8hC2VM2Uat7q9JaT0p61+51y9t85FQIhAL8Rd49lz9V/d8EU1sDDuJ+s/UxbeZHlBt4dwKQ1ERKv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8057}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-alpha.17_1741717488981_0.4122350042741565", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-export-namespace-from", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "71ca69d3471edd6daa711cf4dfc3400415df9c23", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-tQvHWSZ3/jH2xuq/vZDy0jNn+ZdXJeM8gHvX4lnJmsc3+50yPlWdZXIc5ay+umX+2/tJIqHqiEqcJvxlmIvRvQ==", "signatures": [{"sig": "MEQCIDGdvkzR2gPJkx7sq10A16F7sVmF72xrJXRxWlMRf6qOAiAmiCzh+bE4GPeVYpkmGx+EDBRHRZMfGxpbM4Ahcs/vJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7991}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_7.27.1_1746025727214_0.8397987650777181", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "dist": {"shasum": "97a91736f9780ecc533a6874537beb9b96059663", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-wmKbFPLy1ccg4FGjgvyHvznIteT84+qXmhsoJffy4ju6SziIZqxfqiTrCfhZ+cGMyaW47TasVMJxMPwaoH59VA==", "signatures": [{"sig": "MEYCIQDCAUHiSIf9nwh/ucPhlzOdZHgE5AKrByV3KoTyoU428AIhAPIvkruItXDT0mEnsdMuYD9w+7BnnYmklAmBQfs5d/Zv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8033}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-beta.0_1748620259115_0.6210830360899473", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-export-namespace-from", "version": "8.0.0-beta.1", "description": "Compile export namespace to ES2015", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-export-namespace-from@8.0.0-beta.1", "dist": {"shasum": "2c71b26eee31b3fa78dcab0835d13f1898e0905b", "integrity": "sha512-qeYyMgRbjkBfodEpSfx1pG/3GNMIhHZkwuXcCnV8DaUXeZrH3Nm+aQck0UXcf9bzPo/1c4dIgNvZcSH63d4wng==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 8033, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC4CHPu1ROFe3Hjdy/y0emu/YVLxTX/HCd0ME99t7qxzgIhANaBzZOrHQtNy4MnPJVrrqcC7tKss8ABBudRmGQMOUtQ"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-export-namespace-from_8.0.0-beta.1_1751447052759_0.833438825067262"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:14.486Z", "modified": "2025-07-02T09:04:13.141Z", "7.22.0": "2023-05-26T13:45:14.712Z", "7.22.3": "2023-05-27T10:10:55.657Z", "7.22.5": "2023-06-08T18:21:15.401Z", "8.0.0-alpha.0": "2023-07-20T13:59:50.505Z", "8.0.0-alpha.1": "2023-07-24T17:51:36.866Z", "8.0.0-alpha.2": "2023-08-09T15:14:49.912Z", "7.22.11": "2023-08-24T13:08:38.149Z", "8.0.0-alpha.3": "2023-09-26T14:56:46.074Z", "8.0.0-alpha.4": "2023-10-12T02:06:11.421Z", "7.23.3": "2023-11-09T07:03:49.493Z", "7.23.4": "2023-11-20T14:22:07.210Z", "8.0.0-alpha.5": "2023-12-11T15:18:35.638Z", "8.0.0-alpha.6": "2024-01-26T16:13:56.883Z", "8.0.0-alpha.7": "2024-02-28T14:04:45.027Z", "7.24.1": "2024-03-19T09:48:36.495Z", "8.0.0-alpha.8": "2024-04-04T13:19:45.804Z", "7.24.6": "2024-05-24T12:24:27.142Z", "8.0.0-alpha.9": "2024-06-03T14:04:10.556Z", "8.0.0-alpha.10": "2024-06-04T11:19:58.023Z", "7.24.7": "2024-06-05T13:15:17.103Z", "8.0.0-alpha.11": "2024-06-07T09:15:26.933Z", "8.0.0-alpha.12": "2024-07-26T17:33:23.527Z", "7.25.7": "2024-10-02T15:14:43.110Z", "7.25.8": "2024-10-10T13:25:10.777Z", "7.25.9": "2024-10-22T15:21:00.859Z", "8.0.0-alpha.13": "2024-10-25T13:54:04.880Z", "8.0.0-alpha.14": "2024-12-06T16:53:56.220Z", "8.0.0-alpha.15": "2025-01-10T17:24:21.032Z", "8.0.0-alpha.16": "2025-02-14T11:58:57.538Z", "8.0.0-alpha.17": "2025-03-11T18:24:49.200Z", "7.27.1": "2025-04-30T15:08:47.404Z", "8.0.0-beta.0": "2025-05-30T15:50:59.313Z", "8.0.0-beta.1": "2025-07-02T09:04:12.919Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "description": "Compile export namespace to ES2015", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}