{"_id": "@babel/plugin-transform-sticky-regex", "_rev": "113-587d3bc2eb0cfdfc3280c8b6d5c3595a", "name": "@babel/plugin-transform-sticky-regex", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "678c577d4583220b84aa996f135a3c6722c7d535", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.4.tgz", "integrity": "sha512-nyjexwylpqbiODSVhrdWzxLuvEwgSK+HHCBzK8dMsgjCFU+gp1Y0QQ5CVVUaSije5o5u+RGItJ9YDS2//iXjKA==", "signatures": [{"sig": "MEUCIBEEUhkXMZMS80kCY7ogzU8l/uZjAOL5LH1bJwmJSDkfAiEAx1A0tlwlCw4HT0eIr4n8tYJoQ150uwT51GLH58tPZ8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/helper-regex": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.4.tgz_1509388529392_0.9954312527552247", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "07025a77b137ae82d8a1b36a4366eedc03a48ec0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.5.tgz", "integrity": "sha512-lQ9xcbGpOnKW/nj73P5edq2UTnbhTcwSIh7j2dkFRDqlXCGCcZgHn9xeA9puXq1t1GEOinFF/Z6pBu1hCNJMIg==", "signatures": [{"sig": "MEYCIQCsPROOUYRIXvirTYNCmb3cTsc0+2xmy+3HrdCt8vXWAQIhAKrNZEZJp9m40kzquKmpNL3UWN1XQ67YS/GZ9q78OGeH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/helper-regex": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.5.tgz_1509397025069_0.17160276416689157", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4dae2706d62dd3f97d2d91d296e8842a89cefa7d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.31.tgz", "integrity": "sha512-N86hE0JVv4PWqDBt6W/++Aot4c4Grp4+9YYbGa8LZgDjJyY/cNxZtseKEo6Y0SI8HHGIAgPIqi1JdQXe2/z8XA==", "signatures": [{"sig": "MEUCIQD8wtQg7QuH6qTLtKqBn75Ai5obe5zwgI9l11kjeGaWUgIgN94wuvjCsxuQyMWMhv8Tpe50wYpRGJLhN+iFOXWm/Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31", "@babel/helper-regex": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.31.tgz_1509739437254_0.7989796879701316", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "efaf0faed8c183d8777860ae8a97c3931970779b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.32.tgz", "integrity": "sha512-HvgKJ1j6ziYbphvn7QCvTHk6K/TeqNo3dFdHfv7KgXQYxz/OQV8hxCE4SK6nqesrQftOBWeZlNotexcA8qVRSw==", "signatures": [{"sig": "MEQCIA7rowUmPVYhiltwvW5v+WaFPpAIOeSKk/dLt5dgv+L1AiBIZoG51zzKZOgNKQ5LltFxzh2JJVb+MBsRyb9L7urg0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.32.tgz_1510493623913_0.4851625291630626", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e6eb8d7c7495d125b4bd363acd2a103aa559ef54", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.33.tgz", "integrity": "sha512-gFI4OrCOZ/L5OVfFCNHjAARdP3ftWEV9ABA1OOo9LZA/RQgav8OvO4tpvJd7XyWOLbAywimtIo8G5M1NKDKcxQ==", "signatures": [{"sig": "MEUCIEitucBysh/wxA00ksI6SbSxP62oyqoziLphBWrXKWcrAiEAho+Ik3rfQ/NePLxXFfXtdJ9M8ufnH5v5FFdkNcCltw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.33.tgz_1512138533527_0.05170206679031253", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1a3b656bbfdf5a161248d2559ad016a203817488", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.34.tgz", "integrity": "sha512-tn7GJz07Z8pV8j2mCGeaqIc/pEtCtnLmCGpAfbwOBkccyEI81YUBF0HnvqHdMTLcmN6A7TXBZmaXovEOmlZuhQ==", "signatures": [{"sig": "MEUCIQCaA/W2j0skCu+fSp6CBputHETt7Pkd3tpqBmPqHQ59mQIgMxF1hjBGtzputr58ydldsk8A2N8HhXs+dZZBEmsLG2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.34.tgz_1512225592932_0.09435388259589672", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ee70fed27bf78e407d2338519db09176cca73597", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.35.tgz", "integrity": "sha512-EEzzZi61o/4VZzb5nhcNlZ7NPpRLOviUu+SpcTYIQCf1s9QdoyQ8gqb3+3x3vLhwRcKDe8gLee8INXk3Ebg//g==", "signatures": [{"sig": "MEYCIQDcXekenGzhH9fLyWRkmrDBml04Lge/7zjJfJ5RziVzuAIhALoIZ8/qqjA0yeQbukqqtNzrhIN9dLckmWcEBnIR3DQp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.35.tgz_1513288091671_0.4123945194296539", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c4cfb9ee99637698319dc0ed097560e12c48fb4d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.36.tgz", "integrity": "sha512-DcMR//iXgOhYFj1FeRXweSfO7Iyw5DMDgRxl7n/7uqj+BfjGL7X2O8OOqDQWlk8nYE7MuUmCiMLg6E52vWyxqw==", "signatures": [{"sig": "MEUCIQCP8N92oKhu3UErTT1qNZfRa9xXkltC+r08DUoN0G+1AwIgFM5hxn4ZiDrwQOeFLiStEGD0NoqkoAIcFx8PqyCy5Ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.36.tgz_1514228714689_0.11495564435608685", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "49c7183ddb19d207351d6890a251a9f095c70543", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.37.tgz", "integrity": "sha512-riK/UAjidGXKbMbzyZIwfeaRcIhmCb8a8OnZuwxwfKqJKcWMs+K7c1+l4126FTX5qMysmL1r/w3CCmyb90HSYA==", "signatures": [{"sig": "MEUCIGGnAAVs4FuutRzDjQ7SO7+mQcaol9ov7DaFov+fw9I8AiEAjg7QaTwhZ+VsXxIEqzzITltBDNXh9P7DJ9NYEoLvcl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.37.tgz_1515427374880_0.20016098930500448", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "204a8c423948c195d164dcbe3b7adf9a8e1e0989", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.38.tgz", "integrity": "sha512-kwH7JLQ+UqDJK4ZkEimL/mMrRSTS/Agm8+EzEJomgr2P2xzSI7fHQ4a6F4m6PFFk3ZUpdF2TDp1V/7q4Hy8Etg==", "signatures": [{"sig": "MEUCIAiOl6aYDGn7d1U0znRrP7TRbDygLgikxRqf7/+4r67cAiEAo4qLerizO4kei+x+GDw1szjIqKJtzG+RfrE8PEADVr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.38.tgz_1516206743256_0.36844571423716843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9e989e99efb9dc84928507adb260934f2e336713", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.39.tgz", "integrity": "sha512-K3ZbXcK8eVt1gOqeZSZDqP7j52aLTbjcBXkaPXvZFOx7wbuJDlhKqWBo5SI2KBWnnHhS6Q5x29HchDnovYGZMg==", "signatures": [{"sig": "MEQCIBmTF2JtKlU8wLUizpkkxPtWir8lKnuHjMusHvo/V0SSAiBlnthv8J8SpC1cGsd734poGp3tf5J2x8QqLJORFpiXXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex-7.0.0-beta.39.tgz_1517344072426_0.8879833628889173", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5b44b31f8539fc66af18962e55752b82298032ee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-dJPUaV2D5SwSXypaDFRJd+LIhabeaWhZ3McmNo0COn+lBINJ9iL7mYuPxnqwhM/KoBNv+vYIoFFZzT/I27K6AQ==", "signatures": [{"sig": "MEYCIQDIzcvl0CalIg2ECqyD4In20JyiIbWHLHdDkygu7KwFugIhAOHgcXRSS8jvwmov4Tlj15cSvDV19I5R5MRoUsBaaex3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2273}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-regex": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.40_1518453733087_0.20431007363908016", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "00a204ee9719c33d8a8c96526de4ec908084aacb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-60eclaeQsp6xyYx07TYiC/qOsGqtIrAF7T9vYFM8t4kF9v+/qyIWUzXSBuGICdxnt6Di7zSOup91gI/11Kmjlw==", "signatures": [{"sig": "MEUCIQCCnAljqVxqhnyor5wSVmEMWsx8bvoUE82hXpUo3KqVpQIgDBGpBZ7+fqkqFHej0bKCLMvf7SIu6dTT0nJROboMbto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2484}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.41_1521044779551_0.7171118894799919", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b0a5585ec24013dd6f0b1b8cc7a73423c4bc082f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-zQI5NhVfnSNNFiG8JhoXXzTV89aLTfHW22inWefATRmtqe64iHZ4bivBTkDbxt6X/St1yH0aZB+4XtBDZjzcKQ==", "signatures": [{"sig": "MEYCIQCNV2qzXzqoegdIWm7uZo6qWAS66AJ780qdoxU9rWgZrgIhAM6kLbZo4qXwRlAOwfPnFAjoYZ+zlB4JeaWWJfmauv7c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2484}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.42_1521147051046_0.3902634901447364", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "94e1c2e12079cc54d435a285af3fe202a864d387", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-+XyxUqJ+PCw1iIZ0g7bfKawKMxb0+fD/xPAYuOUHXpx0WFGAR1R2XJyJQop+EJgiMtQaEyw8zlUxL/drGBNPdA==", "signatures": [{"sig": "MEUCIQCazc7ZSPEXcfNdQ7vDlYjcvFBLvQOcrNW/HL5QoD1wwwIgatQNeR46njc8qbT69yq5dyrGSpExaHvAxb+AoxwwpSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2796}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.43_1522687710095_0.6725201879457725", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "512597cd7535f313aa29f31d0b60572a0374db00", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-iqByRYvEMG59aOsAAJKRuolqwQOrRIGDfLTaDr45uaW5OLKUaQiPG/BxMF0WDNcQhKIhxiLUZ2K1s26v6SpsKg==", "signatures": [{"sig": "MEYCIQCENL+V6ZHQXlIJW6BiwvW+MaYVE/x4azetXL8ZgmcSwQIhANysgCIZrJ325B1E1l+IKmcM3Vc5qPNMSJhO/3i0A4VR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2830}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.44_1522707611894_0.9135432810243806", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "276463edc8b7b43c0158cc86a75d88f04b957284", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-OGjWCLXKnWTOECKQQ46n0dJgshTo8Zn+CSn7DIDZIwlyP53ReIm9YMq4SqUOJ05YFBZNZuSIuFiI25oFNcFsOA==", "signatures": [{"sig": "MEUCID9s+fgfD47mGaGXkR1YU/C+A/2TDmGganSLHBZSjhT6AiEA0KY+Y7FKiUu5kWA3KyrreD4MJ3drM6q0LMWRPotVtw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1zCRA9TVsSAnZWagAALuoP+gPAU6cj1ojbzTpO64Xk\npbK0yT0s8MP+wLBJQeFHPYthbcnd3DnT97ajikU/QKF3+Yif6GyoOXNXb4p4\neJvkqzhThCNhmPGFRl8TjO1rgSi4i7LISFRL744i7+bkTq04gYumcJNBnwQj\nGAB6NMYPDLPspYnT2RzL7LMcTPFhXmuVnhkabPj/I93Hhw2oEj1/cBU1Wgs+\nm8MAYTtbQxPZ3BfgD+Qvat2rRDxoFs3dTPrtcmKZkuFVOqq9kjladMjwAMiO\n3RK3fzXLbhkXYpXn89HiCVhzuir0pYRVxNagzFEUDzMpT+un3cCPR/s5VVAj\n6iZeBrRXOxvJtHC++J030CVsyxgOiZgy/D9l6/LPUMFZZFml5MuQbrDfKNtj\n/KT+02nbcKo/GpzvgUgs2vNiWgqUkNA0l4dhMADqv6aOLBLHeV+qq2XANCfL\nvxyXPc630mISpGBe0DBNQbBTEZl/SesRJx2YBTa0wClYCJYNAs3nZ3qzCYPE\nipchjLTh5ha7P/C5aNhTe+RnFASE0QhM7TwXF3WrcXdmsfSXL6DKadHh4X+b\nUbbfID4Pt9WjGdvfd/A5FbLS3vioJfJwWN9e1EDGyZS+HkT8qt7HxW91zU7p\nmdrqggsp/YDRiuTtAIqQVm2ZDDuLjRb/PZbCoJtDU2b0TRrh0eU/3vOC9E0U\nosAZ\r\n=Pt0L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.45_1524448627367_0.8338195308679563", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c96c41f31272ec1cdc47dd91a22c6d75c4db70d2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-goMgOLODjG1cgFHlKACh/NT/wrnmuRi2CpXsjOan10eZce0fk9kahYz/04cqhppmwtV/vQWwH2ikAHODSD8r5w==", "signatures": [{"sig": "MEQCIBW4cJT8RsN5WJQe9X6qw2oxIzKPV5Od2nYcEU9ZhdC1AiADmFoZdF3dbMVvhIiOJKAWs7xtP1SUU+rO0+qlb9mDWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGgCRA9TVsSAnZWagAAXuQP/AmxvqLp76g+PmDPlWcT\nBdfSOTS8kx45w+QL53ADmYyxF3j/O9Ldb/qw5dYyvRnaSqz/XqEmP127UJ+s\neoGvu1DAKWkk8gy33OpZx7M4r8KxalIaYRKysbNplUznBE3wWdBAx4OB3nm7\nlDAlUQa4Sc3wxc0VMUOHhLEMmsl+azK7ewcP0k1gtgho/m2uv/kGuBYOFSNe\nHlEJeuzTJVXtm6xyw9LZ+deqr32XRpAKAQG+Oqc0kdOIZlBIJ3m5IPNyJBAh\nmWdGIVvauXB9og2k23NOTiUEnGqJyYNcBWLAag5whXhDw/UlU1xJD+snW9qS\n+Zd13e+R1jk18qjL3QxMyVMlOW1PsyJUyoKOHvYX40gujowfEPuRtwVrxUlV\nMxTjAHMnlFVuBc0FF8jcuaHAPZVjjN1y+TtUi5AQ9VI2j0LkIc5zNDca6Jdi\nQAlOcTUJGmSvu7qMmiz+d0dKPZf4Q8mX6EKU3dxTnJYXWxqM54kcPBl8G8lS\nOz4mo+iqkiVGmfVCznTEl3ushuJKEytXAwazRjKWyYZDcTtTgdpolzt1PNPC\nMHkMZerO/GQyfKLxlzTkOK+WnoD+mcL3kr+7LvM1Qsb/FbWTZc2KOZUazmI1\n3uW9PKxuXDDFsgYf7ZRt06NhGAmQcdQ1h/tolID8YXndT31z0GdmbUs/+3h6\nUvmK\r\n=06Zo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.46_1524457887798_0.2991881380352559", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c0aa347d76b5dc87d3b37ac016ada3f950605131", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-+Rc6NihGoXcwAqAxbiumvzOYxRR0aUg1ZExfyHnI5QnQf0sf4xAfgT/YpGvEgLd5Ci0rka+IWSj54PhzZkhuTg==", "signatures": [{"sig": "MEUCICxt6wbz+u6WUugR7uv49zT9JuVwNaCnPYil43rd/Mg8AiEAtIhLwz1CO5fNZLIEUjANdoVZqG2prgV0D3dF2IkvCqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2807, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUvCRA9TVsSAnZWagAA12EP/RNeOfn7lLVmAB+Bc51o\n/KFRM3yo+A+vdfP+MZnrLASb6icUiv2OUF4oDRgCx370l/1/WjS2odrSEog2\nVTMcA9z+ZaNgyzW90m7DaYSaRNDGq49hUmfLUSfGPXYvcu3PAfn+bPiZMC4n\npaDAoPHhhSHbSFxgTk+Yvvy/0bTZCt1qGe0wVZkZE6hqlxMQi8T9LJH24WBH\n2Eq0CHV3gjoLZKm9UZ0bFq1C8l8ML+wxOMkc7Lo77uPQbVgzw4wYwM7qFtdC\n9jx74OtjE9CsoVX9gN0+7PgFGcHfk/SpSfMgcatDibu9p0+XhqqaeqZz+6N8\nxmVBklfWkPAtYdIxmr/yhE6cC6N/wPaIXXpmy3U2CRyltWXWl1LF69xPcDVt\na6QESGtadMnCWOqib2QLAIbVHEeMUwf647Ps7ve5qd8I2xr6QxczqloeMyw2\nq6nP+ufFpdErUVmxI6y3EYqTpFMFppYu2Vhvu2kcfmug5sDvqYiSsrsxjHL7\nnx4VpVDRLqIhD7GPWh+MPewoQ1yAY25Qb6BY3de5LpUZsgvsO6YT9pAiSgvv\nzRXK8NsIeVhhWqfQZaVNK+3FVIKp2NXhVbR7BGanY/LuTttc/5v5CwM/BU+C\nTj+O0gCF2n6+lAFwq7FfCnrs0WOu1MmpNDkjPIbNpIoR+ChRi2czyCLz/4OS\nZwSk\r\n=k3Zd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.47_1526342958569_0.8728844918150998", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "02426d9248b6dd164188d5f63cff574e6b5728fa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-HNXgYUxMMDFGgfT9Rzi7BzpM27qdQl5akXq9X1RHRejroORwdVk3FCv4+QdxPxuLIWFkiPR55ZqMGF3W/r+hyA==", "signatures": [{"sig": "MEUCIGfBNHOhCppAHume0S7o3Doh8b0M0k3WOc0w2O03CgePAiEA7iM5AeZggb1ERZRs/9EOqFl06iZmmpy7EuZnnKa3mjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxESCRA9TVsSAnZWagAAaXAP/jMzjA4aycipw1AkkiDo\nY0p0Zkx/YvcDKHJq9ZBXbUra1XdWMDKF8fqddA8dQngX0Aisaas25QSoLUOx\n0j3h7+OyGyAPcPieyPmcjrEUDt1Ri10Va/NuqL26fDAgLiTyXLZd7DpbCM81\nZFIiO0m2bNv/e/wQHSdWDLQw4VisxEHVKrkTYr3YOiMfpEJz8OruYE2MuZwk\nVneqmSVJ1J0nIHqlvHohH90grWIkPnwxtDrQyt6v/Wv0GMpEwVt3zqsIgQvf\nW9ZCzbsqBOh/GbVO2bOfTxPHHneZeXVft4LuDl6BAz7Do6+tZyi0kJayFoP3\ny/JDaaDPPWElVLUprJWXr5OHWa2A2DBvXbwrc/31mOa3M6Afx2j8KAK7Lu7H\nJcMg6HtlergSdoPHLKP7W94J4a31rO5j36i6Rh7ceHzhEU07RvoPu1oS5R+Z\ng+thUPhl3oGRM+3uke1U8RWBJCQt4TAYXiWqEOvMt1NOAlER942RV/qVb99v\nNI7A9qsKxUttTdqnaFUnVahP9/JuQerxjVGTBwdwYFplasKdZEmcViWEeOtz\npQ6biBhsdeRzfgAnYQH8B1gsFa4nbgwYqU6AMCfVR8jPvbsJ2sNtyr/JED4b\nU8yFsrV/b0HSCzwnh7xc4eCCK79wyxC4lUM7K66969qgNzAMhh8L6C+5GEwC\ndSCw\r\n=2YBm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-regex": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.48_1527189777324_0.8115136924233026", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "08cc5b64cf6a5942a87bdd9b4a4818d4cba12df3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-Kt/FPrkFEM1w1qOBFMHHX12pKXy251/mqEdODuFgMPYEFt4zD1/65/O+0dOHmurcXlZKE1g/uHMB1qlwwt65BA==", "signatures": [{"sig": "MEUCIQDFSzlS4gNYMRj9R8MuC8/g4MRpQjX2N+eAQM0jWiXTIQIgJMbs4to0DZ7q31q8skQTOn6LPBiE1Wyr/13ubgTdl0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOZCRA9TVsSAnZWagAAyvsP/3cc/5qeGEywaNCinpP4\nXUrbgxURRol1heuDrRU29KMrd2Y2glBHce9fcnkaRREt2frEyOqav3B0Akmp\nUvCOv4wnSbgh3v7PzKvmrPnGvmvugkq0OJp5jV43Z9mT1GpuTtDJ8z89ffkG\nCopyI+WFVSNSR/HummIZeuxfbp4wRRdLLURDgyJjyur7w3+6hojQnRoGEHe6\n8h7JVBckgt1k60szuZ2qXg9NZNtSCjZQ2r8sFuFCh4GOhTB0iuGabB/C/X7z\nN7KXppMozHC7Z2bGH6v/fXU2HLEKBeQ7cjLow4wg7huv8pU/5dOy6+GB8FbP\nx60Ocy9cdGP627MW5COWlF3L2louXeFEAb+PXjWJgr7ZQPLDiISZkiZsjmTm\nve14JfmOMMmXnSwQ751vFYcYTxDRWbbr7BpOTfIKoK4Sh9PL8LnNJgUUNoD/\nqFOMMPhGv4/jVm4F1eGzXgI/HQlI1RMeh5SmsPo7RKjTAZxQmnHNTb7AO1st\nlXe6HGSC6Eh6/tvoXDPaG7DB8sds+zuF1dPOkuGCNbgxr5VllqgZBUPdXbAv\noKllUqPeT2mVD9SBaTodS/+6HXcLtmU50qwNRUk5ATfhA198rEf3tQrxJlwG\n8WBsMGyjIIHJclPPRfQHKCKsX6znJ2719lxj2NU4RNR30krCY/9gGFD4sdNk\nJ0nZ\r\n=d52Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "08cc5b64cf6a5942a87bdd9b4a4818d4cba12df3", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-regex": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.49_1527264153207_0.5704581106382707", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b2b646a214dc96c089f3c03f9e2ece207df2b15e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-o43MB2t0Pi1Oc/sN4hw7U7IiwNjsCApgw0BCSlZrq6Za5oXyEIBVmfcHONEtFibM/lc2t5iQoQ+EFXfOOX/NtQ==", "signatures": [{"sig": "MEUCIFP/9MiXX42hOpyjq6+FpzhS3ml/ZHgeq1AAo/yRlXojAiEA6GQ/8iflMgupYnC8CK+2cCujAVQzPzoIKvxH2cOcfHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2567}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.50_1528832843959_0.1519911079510885", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "48cbeacd31bd05ee800b5facbcb09c5781bd9619", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-p4yVayYIka72P6K06GMiW9lLVn8eKZNTEY1Zw7Dv3MEiLofnq6jjKVBJSBm8XKGbL32lntDaNn3I5hNHhbn8Mw==", "signatures": [{"sig": "MEUCIQCUngMZnJ4fAw5kHn5GsfD0v3tGGHYOU3X/ouzkuOdKEAIgW0SZTaViTd3WYJRISDn+YndcLqmGl4fmVoqSRuSh0tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2581}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.51_1528838396701_0.434755246613564", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5c8af3d6a48d658e0cbd6fb67631f8a4889eac2b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-QXiMDSJ1JSBebEk3RGKob4I89o9IFxirygQ4aTgvzdDT9E+Px6SngAYWjsCiwxwBFhiUsmLFbhMC2GrDbjgarA==", "signatures": [{"sig": "MEUCIDDiUCXmW5EWyClSeiRS4lyNDDIMH/WMtVFhb0rMCWPBAiEAyF0tAlegYHk56D0bJ5/8g685ynDYHqVuehQRPqPapFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2580}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.52_1530838769226_0.17942153499664526", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0fcf3c994abdd8bab59ba9782fe4d9f8a545d6e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-axZvAsF66i0/hBqtlAeVWB66OGx+EU/5cY4DEQtqd217LdbtrTpV3oynG0cZylNPeY2TCU848ojlBCA1L3PvTQ==", "signatures": [{"sig": "MEUCIQCtrpfTSe4C6ACM67S4oe6Df6pClCxHduW/mk7cEv8PrQIgfvCdOxA4ti0i91P4ETyY9H3Pvf2IhHkF1JGK7CwlPBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2580}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.53_1531316428339_0.7772271652263347", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "568f35eb5118ae96fad82eac36374d7923b47883", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-Zaym3VBHIhvEOvc6i1yPMh47Cna9Krp6/BBcWst6GgNijMdwZCwW5aocS4d6e6o8fgmt9FhSSScabyGrPyA99A==", "signatures": [{"sig": "MEUCIQC8lrwsVVuYrjXN+ltMMo6jU9+lIiOMjJa2k2C/1wIgGwIgH/XQEwE6HD9xtRe3aVFWvyM7z9o1fkpSQoWxZmwyPAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2580}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.54_1531764010060_0.09849177571398005", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d0b80b2deb8b4db03bc6459ebe79ad8b39b40546", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-vhtHCfu7kzb3wJjz3gEl8nXSBZYP5uLCrb7zZLo8b72l+OLtK2J00PV2wVRd9YJ7gUPP/7u9oFy11pMoBwFiWw==", "signatures": [{"sig": "MEUCIBIuBR8WUx4L0/PvKYcfBZOO9eQMDxQsNMuqSIJb40LfAiEAry5RMh7vgMcmvwx7r8NjU4wu4y9RNCt0kUoaJ2ffvns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2580}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.55_1532815644075_0.6929318233287971", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "89b37d31fac03f0c87b99dd7b190159d098f7398", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-VGPxbfefemuJg6/UVXf8WaaU9gIZRr31aLBsjDdYfj41N3W55LSvU+6CxCZ/ID6SfNCcQUEyBtZsWMWvv38Dhw==", "signatures": [{"sig": "MEQCIE0u3a0u9piArvuow9dxQ9RnJITzsnWOCRQj/NVmAU/OAiBMraf2NlhiqIn02fi/CUTX/uETyDRmGIoPzoIIk1y5Ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPv/CRA9TVsSAnZWagAAC6cP/1cCSAgIxfaHhvcJafUx\nxlXxx5GZN0nB5fIvAueFJiKPWPYU/svzJAYNbtjG9VUQU+IQlcA835SV6sae\nNWVa3oJ1aPJdUmC8fXQFsCe0aGp6WKg7ckLi4eGdM1tjKI0UK3AXQmBMh8Y2\nBWPV6qVysHQWIkYqTs6I3wIJA1JsHjIT7cP5WUQcZNvR2eCs42FZ/KCfncYT\nGfcCeAABBcTEBhhTJ1wt749UvO4OJyTP4b8EFhyltCr1rkwViM7c1DeOXIf9\nKv8AHr0yfhQDyVhabxNq43lOGvOWRSSfwBRp2uXWm0J5y3ghkQoC/0BUz7oM\nfMBY9YOpze+8rHSCWj88t8TrzlAOFrkDsuqYiOA8J4CjFCdAFVCW5tZuFUC5\nlAKaw7gXpx6wN8vdnL0pJGAYtQje0eRM5qsQ7Nr4VrbjpK2nhPlLB+MpwDTo\npIgZIbPmpayGYJcPwsXyZ6FD647ET55jBgtjc6v7jQleNbLysIbV4v/0QmKu\nFOlAG/+eZlGWVVZ906qlI+kGzWW6Mf3XWMExyliptBghZPBTD00cswo9YSBf\ndv4BAnI5se+VpM9f+d1pKFLYAToYOmbqeVFXkjUCos0bjYfcMIt0jlo3cRiQ\nViVgTWXBNOwdS8y2o7AHTPRnxhceOvJpActo8tlGBq6r4CgFIfB2PJxwPtA5\nrlHd\r\n=wuYF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-beta.56_1533344767186_0.29338298684759034", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "818305e06319da334dedf796437365ce5ddca991", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-vGY9/M6qi85ghGsrgeOcLY301ktorB7I8Tq8JJnRuBBLKfvxuGbiGoYXiS+mQILXAknUtS7NI28B0mCtu0I+iw==", "signatures": [{"sig": "MEUCIA3Dl+61fhSHX9KDixCo/iyySKPWgn/KoRl/sMMqP5QLAiEA57UCXfkgGNNbjFnkciGKIWWzPGjKCwHZkLcut0hHY7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSvCRA9TVsSAnZWagAAt8YP/RSiSqeSiSlkyBPu41wH\n4f9GymOGb/e7PiyPLxbMZvXODf7XFqO7JzEYivunrqE6+RSB1SUTZRKgk9yt\nm0xb+eNACRiiWgr6esxzMX1fUPPkblMnwKOPdIbiiOru2AubKHbwer5gCarO\nkxxGwz4bid5HN6iy6dxbnKwl/pz7YPsaKyGZwNBrkkXYMr8lhkX51EV6l3BL\n4Mc4Y7tUAupXwfT/AkZaojuvmEBVMWyTnrXgHvbkWW8mXUSCWUwxGj0UiRDE\nGRS5ZTuwy/L1YttnT7ADBkt5frahWHcnRJUlCaCDghyAII0HsK1vAw7Jyc2D\n6t9Wt9UfvnagXLK+q7DtOwKxZomN89mMLHkyPS1e+PzuxPlr9jOVgOrT4hVa\n1yaOlMQ+0d8+ccl5S4kOqR14OKYe4HewZbcBjlKYHZrofBmN95lR3XwSxh7r\npCd+eWrSIietLSCaz7ov5L7dMAYYsli2MAhOUG7MT0MGZKIAhGu7SC7gYERM\nlngkw/kNproeFFbu2H9/mgm4kZS/suJ1yI7yTIo2ozUcS/tllEPpBezXiVQi\nQGMh3QPv4OVLEuT/G9IAFE9ORGZ27t5ozdaIhvXscFuyhYqejRB5+OKEQpTL\n7W3SJzudeUWm1nLehpz96T1+3s71jEFjSMpaxv6pOBh0tZt/prCajBW7oFK7\nB59k\r\n=sZaK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-rc.0_1533830318564_0.8881432828255356", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "88079689a70d80c8e9b159572979a9c2b80f7c38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-sXPFGI3GTtSMxVTDwrRmgwmUcq+l0ovzUZFfAd4YK1zJQ7YQCaCjcmLskuiGM20SoteYserDADg0SrLw+8B8hA==", "signatures": [{"sig": "MEYCIQCAnu0u983qxfILbxmSSY1SoYqN5Z1QT3u6jcFK1oR58gIhAPZErVUk3x6hnTFUPdKMDVYQ8sbq0w1umk3oEXtvNx0O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ80CRA9TVsSAnZWagAAU9AP/RzMGCPbN+LRfmSAKVDD\n2+Awxbdm8PmqmffIO2jW2pbIMjZzMQRwF+ASMqptWSHWAP04lvCpOaaja0zY\nsMI9hdjP2MrVMMd13HdJ9VSRGLhPyxiEF//Z+iLiT+UVi+G7Ju0gpt364PP+\nnzeW3ospqGHJNDqZtewpvgPkhiTeECXhj3rmipazuJ9VeF84uk+hDN0RN8Op\nP0KpcZdFMXEV209bnXjoJ9X0HdRqvdrtPdPJ1llgtyz8HQeUZWkZXKqxUAQ1\n06kl1Y3BQgcN6LGXzvAJ3rSYJ+P58LHc6umIYJ6dVmsSGevAEvnbMiIqpLGI\nGG8TJWwgVUGF60Dzd00UDqKafJjXsBuFUxprRlvTndcP5fr7sgkTyN1z6o1l\nH242P/22/z1kjFI33phvMfpWJloit2IHoeNE7ytJTOWvZcSxlvmNFeCrUcNl\nQy5MTSkuO9atiU/kDbi2uwRw+Gsbz4o//M0uOZjILericYKkanPPh2lfFieE\nw92Z+UfNrBuJ6DidVjTdIRXHY1G5b/djg5HqnpA8JUywaQlINE3uhxd26w7V\n6d/g3IgaggtUbs8SZogFEALwHNR+yeb9DMgR4GFM/wtXltC9u6htXwMjGIZ6\n2uFi7XFMBkV2Eys1iviohobokeP+smf4Pmjf17O5lvxAR8Zzilt/cO+Cs8oM\nUxeq\r\n=Hqeq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-rc.1_1533845299691_0.5636451642432618", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b9febc20c1624455e8d5ca1008fb32315e3a414b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-//QPQAuZE6BqOkZNqr2GLfBE0gssKucez91+4wX3f4x0QN9lsMD6fLN5mymnVGWw7FKlM3XuPYEF8syqqJD4Dw==", "signatures": [{"sig": "MEYCIQCUv+EAOPQ8pOxzGpWNSJj+s9DBhufm0A6dSuBIdnD/UAIhAOYqZHy2HI0oRVPamPdPP6LBCBufPCSpmABfjJq5Hony", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbjCRA9TVsSAnZWagAA2S8P/1f0UDfxqIsD2Cceo/nl\nLVw0ZNeME1DpKza3f6F9A4bpVKD64SpsOnL24YppW+lye/shJeEnqMYWE+ae\n95I3S1ki1JpSmp5j/Jdrh1kM8ILVvCLIY5HXabwsBlhCSV1uemsIJD5t0QDW\nE7JDXXbw9a/9VvJ+VP00a2qM8RqdMroVAC7dCpc7fzfbvsoq0trojH7EyDoU\nI23P0O7ENg9InZe3DAU0r5ywhjQ48nj4n1MW18DVJB4tmj3LA47bhILbY93S\neFZUs9UAYS6d2Iuzof3S9wOAgTkiQ7k6HSLef2pUWhGWD78T0jTnCWBiXl3z\n05gCmfuZrqvjqiPdmCGIwMk7gsqdnp17FyFKkcx1swyaoyRCgrOSJqwhtTt1\nLgMNRlO/0BW1o9NDZcOGL9Rkwk5y2lsUYjXp1YR5PjUUFnsYce0JE2/n0OxR\nN9PDo52PMxM+odVINSkYFF/a3HAlLZ2x0Ydt9oDYgE5xwcI5Cyn3n4JyrJmL\nP/hXh2FzhF1qbVmQorB+Ya6EYlY43AGibQnBuhqPuof6gwfr87rZLONDbcz/\nxvVTH7Yb82RUI/zpM0aXzmg2NAOxcZ4pspHnxooqM8k73CxU07kIj6P8qX9y\nGl8BAAja3USi/1MuoEjhcEHF2ENZOeV47DWSmyKAnM/csOXl/fLb3OTTi6dj\n6BZP\r\n=NjwW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-rc.2_1534879459050_0.646289001598118", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d1c2d8cadb2783ee774d04e8504d05cf2b20b1b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-NH3UOZblJ4Q/K/5+w+nMyRzqkkdaJZSTtLpLFLczCizoMS5AoxRQAOzTEm8HzxHX74J+AZEzv8Vqe1/rx+f3KQ==", "signatures": [{"sig": "MEUCIEB0w8sbITLY4cmtCpd17aA526rwIF5JqBw+tzX/v1zJAiEA/TbcJ9u7IDM1zsAyKoqZ5FAVco5HbfFprmrf0H2TuWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmUCRA9TVsSAnZWagAAo0EP/1xIUJfrw8lrEEPKivYc\n/MFPivTTs6u0qjsvT2E7zh+GRaHwF5CTz4NopIFRZOxbsHbsEk0kF/yMkfaY\nMJdvAfcwVEkx/y1xC6tG5eVulD3j7EAp0tLtx79YGfO8IO7AZk/dLCDtSVo+\nGAABq+atnH01BwVX9oyK8HMoy+CLKLorVfwPhANGtk/OURK4uv4BRjlqfRxO\nsFBnzoxc8d/bPOZ0RU9KFoDgmMMomBso0PmVjPWC8SzBh6bxVKwQhGiPZtZi\np2VjblUFmRrdDj7u2lWOmkE4so2yUSrXRCk7qkoTJZjiuSR0rL7sMzwxsAee\nRYssaC4KbTynxAUi0ikODpIsugVgaHOykVmUAS9TSnef/BHR4qEw+CuNo/xW\nYqrp2zTPsLDkTqbVz+a+449cdnQf8juTgSFiqOvWqTuIxXu+zEZrShnNC9V/\n7eaLd8X04t4iyFxKMXHoAjsQhNtsNRi7yqasMr6i00yKqffcpwe+rkpGEpft\nhgF8ZmOhfKLPqfvf8ydGyduyWAHOfp6LYuA90r+12rhJn9aJAV4cRNy6v3ae\n1hrZSYWK+xB/YzDxE/+IizYqK2YDlw3/m8m99sQw6ZWXgUrym0+WAXhqKZPB\n+hb7BkZ69JpAiRhw227yGLudFnEDH3FtpHeFv9cd4DGvdZb2hZteMBCMfXjY\nHOx0\r\n=n7Nf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-regex": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-rc.3_1535134099395_0.9084723337945813", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7d46329760dd75935b160864ce841a3fa8bf1574", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-SH1hu444pKlhjC5WVs7F4AoA7tIfWS2gJDBP9RiE8ZAo9FYKTLhAdIAksARlxk2q3GXPp1sJqVkCEKm9kTW/Iw==", "signatures": [{"sig": "MEUCIBnGWbda4Q4cKfBoOft+PIWxUrasVSo9T73rLXuHmVLnAiEA1Fz2JEfo6lmtpJk8/YVEgXVjz726501FzMDUjAAB3NI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpxCRA9TVsSAnZWagAAjBUQAINKWwFOXeM0ewKxkn7S\n5eTae/KaeoJW+A2ciIXHhm9Wx1HCjHAQC/B1pcAfg6taTIYVYphYtSXZNvjE\nxRj628DdJR8zuR4TVq4+3dnmTNbxxy2AA4haxHV1eL+Z47IKWUhApxInYaop\n7KPX5B0qs++ycJZGlnXiXJ0eZ2RAPy3xszRjxCZ8x/MvIz7k12oR8/5nGUS4\nG7qBT2Fr6tlpt0nON5zmRq8c/Q0FMcpuIHt+n+v07o/x0h13Q9Ubb4cQz3UP\ncYR0ESapOu2TsG7CYz0pi3u+ruy7Rz91X/VIKi0XGru8EL7ROcWLvXuw/JbW\nhZpYgDDr08lL60IvN1U0qZqDxarfWUcA4fIbs0cL3hn88KR9Z/bQQgtF9mTy\nnBfK76ys5f9WM954yIsgaIwjS+kLMWZjmc3KyL3CvlqTLXZuoVkZKi2KBcRB\nDrceEPDMmSvXeihSSfHJaLVT4sD1D1/EVBjZYf+WWN5bfW4Ku8lqUc/TN2XG\nabcUaINIlDk+zlXti/umTUrkfgTyckqq8eQzqCo4riInnz/avYqoiJohPpy6\nrFS2CNScGxmNbvUyRCbgRKSdaFMRKpMbu+4Abs4Fi4KSDvsrdfbEZwjchos0\n47/hTIexFiK5pH3neTaP+TdPWSYpMONx6tIh4dbJF3VmTOnWKMOdFE82yS9r\nV/5Z\r\n=DQba\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-regex": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0-rc.4_1535388272604_0.21652370357088735", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "30a9d64ac2ab46eec087b8530535becd90e73366", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-LFUToxiyS/WD+XEWpkx/XJBrUXKewSZpzX68s+yEOtIbdnsRjpryDw9U06gYc6klYEij/+KQVRnD3nz3AoKmjw==", "signatures": [{"sig": "MEUCIQCe4fbcV6TzCKm+FYCajDj4r7e36c3dE13ZNCw5WXAi2AIgUtEw/yf5cMul1xWzQLt1leGs3qousty3/2B8BvjRTbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHB+CRA9TVsSAnZWagAAYQcP/0JaWzgB1+cIywJaHqw6\nRhigMJCBSYA70LMFACJgcTWqAqYLrmRRHFjkTbO0RWNoTfUna4HUs9x+vM71\nawKonPxTdSgD5Fi8vw3AhP2jkWlo6dMNcRc6wBKdID0bZ6VoNMqVsyB3LWIG\nt6vblkdGF2bCGej9HTtNmzLTMwfVcMOQ7rTep5CBpk8pT6a7fImhTDKKvcvs\nK0S2V4yyNHzuSNaeXSpy/C2e1Dj0GH57eFtKOB2ZjuPNdeS18uqlKCCYmVEK\n07xyLjbimsOX1jRnoZ6XKMnc6lQWEpuJu9gDHL2ScVld9c7j57r/Gz22LmxC\nIOrHZsCuKspWWGt/EIKp6u0tJYT+uLVIBi506YQYoqWqx4Wolfn5MB13ugm8\nYc89gU2AOfJrlQGRB473hUG6sY/8WEw7/+CIZRJPAnf3e9ZTeWn+2kMzUPNq\nY/0q8yJbcyKLhM831TWd0Vy/WIZP9k27+K4CjFS5I83dMjUVDRyeFqkWT3zz\n7QBdxsiSoo3pvJmfTCsB/RO+NzDIaYjYZ293s89xOAfB89blOpL7UidxMhgg\nOMEgp3mHLDgLSkfy1DHi4E3Q3fcLjVSqyhWUrxiVmzzbgwwJqOsuURnuQo1k\n/SBJnDJVBe+nJl+io+ZOt3l2t78ON7VmngfdhV9eLANRTF67eir3OSU4hswL\n4/T2\r\n=mIS2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.0.0_1535406205541_0.3854859578778411", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a1e454b5995560a9c1e0d537dfc15061fd2687e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-KKYCoGaRAf+ckH8gEL3JHUaFVyNHKe3ASNsZ+AlktgHevvxGigoIttrEJb8iKN03Q7Eazlv1s6cx2B2cQ3Jabw==", "signatures": [{"sig": "MEQCIGM7umnVlP9aBkPd6q0NrXAPd6nrRkbyZruE3TF+eQyAAiByPgfacBFlz31MmnqijspBYPhj0QcCAPh8A1q+lAcskA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2gCRA9TVsSAnZWagAAm2cP/30cj2NzPWQMIhsdnhgv\nJclULRzlxe33ApapJI86rxl8pkTpGl54EWvLCBYUyvn4tuP+Fxunn4DN+3gj\nUEfxhbApBcD1rzyUdDGQyA/BxlUMbAfbjYjJfK3yncEV6QO5DJ2sd5chceFD\nAkhf02krBP/zXmeBu07tl1/cPm1xQmNN8s3CH8XIXnyyeAGUKUwBu4boDXix\ncH+pmykWmxG+Jmhy4CFoUKJSTKCN01lmz06yxRu4ISHGjU4M2U2C/PRDIVec\ndq44JyO3vZzuWcmhPbMZyIZ8togGmgzpV3lc7kgYSf5rXGvMzja4f5Lw6m9w\n1U96LGQF3PZ9xae2dRMVyL7CUDYzjFdSMGGrRsGLS2NXfRRrHAOM+kZ2AsKf\ndQuT8tV4E6YYN0qLcQiF0y+bTdYV7j2B1VYxCqcqqTybD7YKL61bk29USFxl\nWddGYQ9XWKLdMyn5uB7TXQHqKAtDa8C2NIkObauzBuyK9JAzhv+xv+71Z71N\nWw64qSR5HJWbUnI6zTLRo0eeWpckuOeWvzjZNZcWU774NvnNydCOP/j6Ufyz\nghKlpgw6dtCj9a1GH3xZe6HYa9utB4qUbtuJdZIIawviVt2cVD25hwCymb9+\nqgfPQd1/AQrexCteSZZrtfXVarnYC/1kjy+dduUmUrYZInDYx5aYH+anvYq2\noKt3\r\n=vnFI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.2.0_1543863711567_0.7308044194965204", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ffb68c05090c30732076b1285dc1401b404a123c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-Ls2NASyL6qtVe1H1hXts9yuEeONV2TJZmplLONkMPUG158CtmnrzW5Q5teibM5UVOFjG0D3IC5mzXR6pPpUY7A==", "signatures": [{"sig": "MEQCIB1r31Zwr+eaWCUNun6gq03Xt8wHSxbdZWG65nFz+9PSAiAq2+kAk5QkoTNxVIj1rP/xrsF5F326cIzmIk9HGU9GQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HADCRA9TVsSAnZWagAAO50P/RvL9V8a/aYozGrwTb7v\noKYruM7fvSlzd6pROoF9mkE78oOJ0XRd4g9GusqdaMUIYBJYBxxZyZfBi5i5\nIH47aMbaTGsILJ5DVKtHWjzB+y6yKO0HzBPBLrG0GFHrdEYnlKsGW5njNs53\nSxP5gSGk9hVI8d6H6QWKMIAKZlZQ/u1atOxpUwbyAbAa0BDUYq9KALw8APvO\nqwH8DRwKsp6laF22BhV++fD8Hx6Yrg79nO6fJYWyWEOTlUx5CLMCiTzVtcO4\nH9qteYZWxZNBvrx/L0IozqxyXpS6NV/66z13Cj0i74hnETshoSBIrkgy9y92\nJiCUpsPET0xtJLZF+fiI1wgAj2qgDfBXNIAST/MD4xQ1nSU/1QWldtY7Pzor\n7QUM0fGI0TlkGK45vNBqli42fY+f/exltHVmgc4Yr7f3y89Bx/foGfBt3VrA\nwOzE6U5G9XZsfHuV+HVXSlksdEDYJCQrhgTXt0vDJP1cIGDTgcRGruA8SXE+\nvcaEB7f0emnPMKPLJFx4BgHmJhxvO4dCyS4JaBF0ZAcsexLKopVyMCLCGi/I\n6WtPdJiLAYS//+YxiB03wDDNCpaUtp0yR20KYrLurAQJkB5I3IqaY7nkq4tK\nIW1FC79Nzvz5xe/UZG0Vgl09jfseClBasd/u5QOXhV1OiW8VV0YIviKdS+cd\n7xXb\r\n=pu24\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.7.4_1574465539493_0.47722129009982384", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "98f634d133f7be471e1e6ccc613c6a95e7e9f1f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-uksok0Bqox8YeIRFhr6RRtlBXeGpN1ogiEVjEd7A7rVLPZBXKGbL7kODpE7MQ+avjDLv5EEKtDCeYuWZK7FF7g==", "signatures": [{"sig": "MEUCIQDvwnj0Yal3Yv4ifBsVEQlzUIGwlc85kB0azcnRLA9FYQIgU8tcl/NG5kZNAczSPO/q5zGAX/OkI0dF5jq5zmAAPrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVsCRA9TVsSAnZWagAAnMYP/2hU1Wu5fcVyJW1d7Q5F\nKR1eS4at9itnG8tGlbkpcxli/yP/37I+CX23+ey5RAo/pj9fKeBHB2cJm7mm\njwFW92eeHMLYziRrCXeczraNT0QkxFRGceDiDtqHUHDUtP6B0zqXw0eZkvPG\n7anhSGmMikJFATxxtJmjLVlgmheRgUTYZMT0m4FWIAtlQifc73gAwUQ5qO8N\nTAdHh/L2v3FadoB+vHfBtx40DS5O0pcGAwh/5E4n/3gNwSipJi4+679x6qjP\nYHBc12xVCegNOnUfDim0l/qiPLUCM/T+x3Tos/yqLya72TnTHsrfaHyvFNTa\nH66VcZTPxn07TUCCil5tb5vQzHdWcpsNkbQZitWepBJ/iPqagKJxTiRraiRd\ndTRpUXOh4gBQtMWhLDNdWBGXkzqkmxg/i7QYEpvfaiEK6r6sSuBhhxT04jt1\n8MsSgU/aLPqkE8lv0g3DbwFXGPAbzAe/MUm5D1SbuH3M2XFsoCMlYFuyBl7W\n1zmau4y5BQhDPti9ItqLURxe3M7AXYBYuxNVzBhEgOF8ix7gPqCrRrTwDIQ2\nus/AJM4DIWHSyydaD72xiSZYDzOHpEOU0OPED04Rmj2/kk+08r3f18z8Wgfs\n4okDNindwyjf3is0dG1unznNXDY5nsWc1q1c4bh0c81bnddKseYkYRtDipGa\nor0O\r\n=av0+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-regex": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.8.0_1578788203943_0.8877962657865586", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "be7a1290f81dae767475452199e1f76d6175b100", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-9Spq0vGCD5Bb4Z/ZXXSK5wbbLFMG085qd2vhL1JYu1WcQ5bXqZBAYRzU1d+p79GcHs2szYv5pVQCX13QgldaWw==", "signatures": [{"sig": "MEYCIQCtyHedQpOocVbr4YdPzgOjlMSCh9v0LYEae1BNJzbOEgIhAJSaM2PtiExne7PNhxqD1i6EB0m0ki501890r6ba+G/h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQcCRA9TVsSAnZWagAAJfAP+gOHAtdwAOJeX+lRS/TF\nW8+svoC2u2yfl7t0LrgMd8OGPB9cfEz28SORxIIVBLQHAUQb9eUaY9svu8Pk\nL4NEwymiyUii2hxplLK0+hoMX06hlPuIENg1BSjaSDy8StfyAfX6t0u6akxM\nAI/xhlTxnVRWlHS7YENa6p2+t7PHUXUiznewiaRDYE5uRbeyFF2DsoYLTOI+\nrANH1i9fMjowyjMl44/lPSEoEAkRcNO439sOjPILxljTUDMq8elPDzFOLzBi\ntyVvMv4+DEh5kUda3qODeg9wjDhEtgl2nh/MDUjFz+mSLLCpoYMSmMMfXuQ0\nf4Jxij3WZBXVofOK5SDzLkqbUdgNROT5zbDY+KM5mCXxPW+D5WREltgrICqf\nN458jOvQvPXQNLD8wtFkxk+qn0K/dI3FCDYUITpE9zTpFZ+5KcGrEir7+Jz3\nOP1eEHCE70+SK0/mUlykK5BZu3MvtRVr9kkqXH44qiCQnhraSTIaabYx+Yi2\nwKDHJ753YfnmlaHkinx72JcISz+kELUMnygtKmDIs2OP8mS3f44P2g8Ifkb2\n7YhxGpSNclAcj9Cfo8msy48hwyeaS5rUbptjKhLQ/qn4L/67gRNudWRhpfzg\n5mHRlyrs68S4MFfvE2rAoI+w9lRmmFyg2xPuAEWxmBaSjyUubPsG29jGNaft\n4hwv\r\n=w2tX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-regex": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.8.3_1578951707571_0.5376692344308838", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "90fc89b7526228bed9842cff3588270a7a393b00", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-j17ojftKjrL7ufX8ajKvwRilwqTok4q+BjkknmQw9VNHnItTyMP5anPFzxFJdCQs7clLcWpCV3ma+6qZWLnGMA==", "signatures": [{"sig": "MEUCIDR6gZ9KUHRTUlp03WYM2FHfg7VpKsgyTleU1zwb9O1LAiEA2cBd3wjFZ3kZNGsra6ghMy9nOaZuW9lc7TfFuwrx4/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSpCRA9TVsSAnZWagAARnAP/050tyenq1jEqGf6fudI\nwkIxFGLMUpE805VEnsCeIJO1OzbsdOAgrl5lHaeYNUtSEG1aC7EW0Rmtw84A\nmWCDk4eOBKvxITfIjiJR2Ib1bceNSS/vShpvuJ1arYyYGaD0ED4WK60Chr6N\nBryEUqiXQUhba30pCCh1xgsE23NU1AU+MtLWM8XkLho96yIp8dySkd69pMXi\nB0iV2NSg8Qc4BAGWUxdmODRhKN9aoYuleaIkCcqozUVPfYAOhLUzz/iljGEv\nftbJzWYM2skVPLns+PJQRdxZKx/I5czQ6PmVpFY07o/l+2boIitO7zGdoLJI\nhcYpGeFfZm3plZLvXcC0D6/o7lESawnrIyMuHj9CtNyV/wsuTP0WzTYNQdjC\nhmi089ctrB4oVIvRVta/CcHngqMcMSs6zxkCYHShf/jWUYJ8/NsXycrs3jwO\n4TLm7hFR4+BIGP5EmDOx/T06IaKWLurk3zTX7GDdB06xeHsJqO4Kd6unzeMN\nWFx5IxacyqURWGVTRmMeBZE7X0KHiuWXiYuHvjItrCWZszJf8xJ5UXm5ae1b\nEjwxb3+UvHPljrh9qZ2KW085EmEfAxO5Gv4dVY5PgymAYa6PYM7VQdyrLKB/\n6wx5UbBDmQ9oM0wnXJy3/NTlVRI+F/uPt620x2uQgA+dAUamYTiDJ/0QBpHx\ndaZB\r\n=Z3LZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-regex": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.10.1_1590617250685_0.4337407245593565", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "8f3889ee8657581130a29d9cc91d7c73b7c4a28d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-Ddy3QZfIbEV0VYcVtFDCjeE4xwVTJWTmUtorAJkn6u/92Z/nWJNV+mILyqHKrUxXYKA2EoCilgoPePymKL4DvQ==", "signatures": [{"sig": "MEUCIQDTY6IEG2pMhxLPTyfcekpJRPw94xcOd6dxDRn1RZR2QQIgNYw+uMeZUcSb+Qwo7iX0ANs0uE9zQgTKpJt/q/K8tnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zotCRA9TVsSAnZWagAAJTMP/jtO8mRgvfk82MCKEx01\nzEZQPPus/FTQpBFiog7aQpg4iwwOdcZmrL/Jx4EOKxprnMY7P3xPc3eLgni6\nL8saAR5UkvSxlL2tUAzz+gYsxNU1IXJ1PQfbMSClnOcMzdfGXzghaDfDo5Jc\noxpmMeReaSCG9/x4UJtH79ylHf6/zuB5RjaByQvrYFew3q6AAIkhEPK5KHeG\nh09ZQ3rR1Eqkckj/KykdNrlKldHbe672wWybPQxkqGW7YbDm1JPsNSZV1/bs\nGMnscmFe0gNMKua776sZzyA/v4qVqLq0OZWkzM9cjywIxo5LRajY70IsSIAV\nQrSWzkFlJ2jQPwrb631IvLlfgC4LLhX89s31EH5qhC/wglacXsbjK6nuhnMf\nXPAqBRjdmm/ZvcCNDoae/6ORSN+jh6RIDDau36yueU6fIvN6XHDbgOtyTurP\njJvLJaFrpqiOQpx3hzYbVdSfJMgpRjQ9/Eg1jL8rrQbRRnr53pt3ImpF2Hev\npHPqjG7bUiEXCBRzZsyshe7Xxs0obznzp+zvorHJAKT+J9B2Zw6V1KgSSejq\nhRv/L35VojSe9xd7goAY/CQCR3tKPzRCLHw1JvIHIs/+J6ZEBGtIGfXs+5cc\n2HOkIt5A/bHf1w4ETV0z0ji83PGSIDtAk3siuQW+6uAMtmwFHYpmK7ej2+Mg\nSNV8\r\n=9UyK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-regex": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.10.4_1593522733092_0.9704047088917052", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "5c24cf50de396d30e99afc8d1c700e8bce0f5caf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-CiUgKQ3AGVk7kveIaPEET1jNDhZZEl1RPMWdTBE1799bdz++SwqDHStmxfCtDfBhQgCl38YRiSnrMuUMZIWSUQ==", "signatures": [{"sig": "MEQCIHgXaVa91y4qYMf2mya+K1QKehynFmV/qMMRN5kDRmHEAiAYLLowa3pz1UpQhVgqr9sOZlvM+cvIcL6z5OrtZ+KbaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/eCRA9TVsSAnZWagAAxSUP/jMY+BIz6WO3j3Ud19LY\ny7uMq87AkaeAmC92jkLbzr67C+OtJWFZXDG+Bd4TUo+EfO2sm4NJ/d2xbPNk\nxZYLWWCuD2IdFSY+Wvbnxuyf5r1NnO31Px1WBko1IBOA3EH1OENyAPez0q11\na9jbpYlMCDov+fgdYzUDvQs9POkbXjGwGxfVMa803UqvQtGkyBtMtGhl0GyT\nTxugKv+kTp+FgK/qTmygkusiRGagmwhEuP/B4fBrOUnoscbpd8MbynFUhhuW\nnYTFXT51H29PNcKKswCfLPGyIQSFQDR2lbP6Y7BHT6kif0FesLlsOuaeit5Q\ntXu+7kJdS728Tr4bQlv6xTE7cimGJ88WSxbAvJzNGEkt/5oOLbqm0yRbgv8l\nG538hzPIQxOC3aYaE9/t14FRRu+SW/GNB3u6iOT1tRLrBV61KuGBvw3/4yNe\nUUs9V2K7y48hzd5w8oWyE2VjazoTGdHaLcpHHCvV8kSC9pkNughFf58VjQzL\nqWX2o2ryE3BDrAm12jVYUUvjZJ8FKhybgCtcTnF/yle1oS8c7uL4mdGksJ3E\n7Achs18/8aw5tZ+/kAokQnZiKAM8UpRFB+uS5CcKahJOLiMYAwgDPsv8+Sud\nWG+KguzBvuQvUyrbTrlkgYZvgbdA4zF5AnxpvrhFj1q5GWMe7SfcbXTTQz4C\n+PHJ\r\n=moZ3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-regex": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.12.1_1602801630392_0.00180365822768902", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.12.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "560224613ab23987453948ed21d0b0b193fa7fad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.12.7.tgz", "fileCount": 4, "integrity": "sha512-VEiqZL5N/QvDbdjfYQBhruN0HYjSPjC4XkeqW4ny/jNtH9gcbgaqBIXYEZCNnESMAGs0/K/R7oFGMhOyu/eIxg==", "signatures": [{"sig": "MEQCIAFpCenI/oN63kAbhqHvUfZWjQ4oqWU8DDVv3eV7XsVmAiBAMXkAu7zTWszTr0cvJzd4RoVDw+SyfhyhdAtb0R7kxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+mCRA9TVsSAnZWagAAve8P/jVfK4Dy49o5Y6nF4CaB\n8cTrIg2Lpx7bMPP4zyOsfZhNwQ6subwSzX0niM8xiiOnVBYRWV2/XWVnEcw9\nAjwri3RTXsKRvLweSTRApfibI4XEGZRnqSDJJzQHzk7GdW77lslmfcC8ystg\ngFI+UdT3oc8kf3KCybgcbyODRZDRqU79ADNjRG49LgJtobHuZ+0lqWhDBDb7\niUxH4GSQuwhc/9x0+EIigfGOPFCGLVYu0sIz4I9amJfe1eISGrxKIgoCK8AD\nrKi87/ly1JIDKrYPIMnLOxPay9xux7tV3vedzFGhvnEqTn9LehbANjeQBAjw\n2y+CBjqm3sQv69QhssqKVvbIgFJu+vt9sCQqjOqCmYMusNpxe41UTBd/vipV\ngSdtIUY2U/aBzjKqI/OCjlq6Sjyzt5zYT8RvdInRYEZGfZz+OoeKv5Eb2o+j\nkvx5wpj3MbgrCZ6QauxrPI/90ZwAGrI1kblMB7yczalXOcGpiddBzYN+OEr8\nLcr9umtgWOgaii0aA0luN2sm2MOsQi+C8iidq73lDAFu9F3+V/Dq7nfBQf1a\n7Nf6PQPj+TDa+arpY9ChPtFtECNJOFRVBT+tl6iGw1+4G7NTB8wkgUFw0ZHp\nmDMYPcx+Z4Ual9ff/rSn9vXrA8bFwvZ9Lb34uvnCJxhLyCsSoSwAmFp1xdHg\nZIfC\r\n=dEvc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.7", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.12.7_1605906342104_0.1645713515849314", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "760ffd936face73f860ae646fb86ee82f3d06d1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-Jc3JSaaWT8+fr7GRvQP02fKDsYk4K/lYwWq38r/UGfaxo89ajud321NH28KRQ7xy1Ybc0VUE5Pz8psjNNDUglg==", "signatures": [{"sig": "MEQCIFDvlew/j4aEyeYslQKYgbwZKMwQ2vrlIlZeTLMm2gHMAiAjA0AA23rlE5SWxXK+fuewVy0/cCJMaTMA770nQclIEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgsCRA9TVsSAnZWagAAxNwP+QGqI2JiPiKH99AAw/X9\nWIh+ETmc7iLSqW6PIuxQukp6koaVCR/BTJTPrFScZe4E0QWwnta+QIGGc7fQ\nP8mLcRPFiH1j/9JQZ8J41CDkkL4NiU9nEZeSYvlbZ/ExPx82RyDWRaWm14m7\n5zyT2mrV92c4W5gpdR4Evwrp0X/0nQbQoxs2BwoFJ3uN+Pt3N3wnksYmdGm7\nMDa/Tq22n2VaFvo6yry67qMw68XSuyZ/SZLKQjnSF+K0VSlysQk9sN9XZcRl\nCKZw0EAEZebdLflkoe3PqO5Ti+8gzxkDzbJJvkSS7u+z6tYTdJ7gpMm5zVWq\nknIgk0qhME4P8nkuWCZ4AbvJJk3RnLSN1kXp+Kwj5EGixYm7OzF3j4XwO9xU\nhgBBexvToni/5ZYI8Tyn+xmrT8/wjesfwHX3+H/mQbjx/ODq6EcwIIbPHxM1\n3PpFuPjkgpB8Olg64oREbEGn0ZPu2qaZU3hbV8oRLe+j5U4/pVgghUJV+alk\nqMOyg++ZCtCEJaPs+Hufjlin0irRrZ//q64iduFPVfL9prvm+RDvvYoqMMDX\nOZy8DCAx7j0NePvxqr7/q79PZl9UMTZYOejljP0OqWmz1RenOb36W3l34OC4\n4Y6Wxl6aC24pfVeBM64CHJaAqWUwdeS+Ua7/AlBPz3ejQFBR4guH706Q0epD\nmHxz\r\n=FB2S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.12.13_1612314667984_0.9541132358317701", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "5b617542675e8b7761294381f3c28c633f40aeb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-Z7F7GyvEMzIIbwnziAZmnSNpdijdr4dWt+FJNBnBLz5mwDFkqIXU9wmBcWWad3QeJF5hMTkRe4dAq2sUZiG+8A==", "signatures": [{"sig": "MEQCIA57EsJQH6SiPfToe6aCYO9CWt4r2G6Yj/TgEqaJkRiwAiByVq0GsDZqHkLA6CL0jGuuyFxGFy3P3VNWLIvKEdxhZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrNCRA9TVsSAnZWagAA9iAQAIc1EvQhk6XpHY6fcHK+\niusT/L1YsdvQ4v4YgPNempO6E5YEoXsqGXrt9qnH5jn+YzmMeQZt50q3zy3H\npHp4nAjJmHJcizCYoLjTumFyyv0c1/5wPX/swyFW0mnuLgGp0/dF0tQc6z1S\nWHNe8dLHZE0DPd2/CgJWRySKa0YmhzaOQp1ajvdDaNTSw1L0N9KrdWBUs5nw\n8SdzPzo/MLFdptk9yHZbtct9+1EaDI6UM9GnobiVbEABsWbqeCo7Fcj+YzXD\nmIRWAqL3Sj3KjFI/GRbjYFdYXFx6+cloLkOWdjYCZ4eZZL2ZnAlPEatcjbbE\n55ZWSNvrbPqQuBONrWtKqPuEH/btpMd7XPXDyxfYOKcPMTuuiHqiBZ8LSP2/\nL/VUkmXsSNtd66ETUXefmtQo0BkOnsDxRSAIivKFx+WdKcu1hOxiZYGllOod\nS5zcEQ5TLQ/MEo4Cgbwxds56n9zwNPYWieF7m+IwrLHY/crn2xFoJrsA0QmR\n1Pmx1o20p7BDAEIGlhxExlDK+nRupmYuA1soewDLOlQykuqbR/qRBlzyNDf1\nAu0FSAmJGI9IN50NYY4ttBmbxFIHv7L/q3SIxNVCcZuYdR3aU91htO/YQlRO\n3NtmXozpsrWfDO8NPTfiM16AOiubZy8hVZl4ftIfVkyyIFShOGfqeAAEEHzG\ndkEu\r\n=8VNl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.14.5_1623280333688_0.7969212979339426", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "c35ea31a02d86be485f6aa510184b677a91738fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-/ntT2NljR9foobKk4E/YyOSwcGUXtYWv5tinMK/3RkypyNBNdhHUaq6Orw5DWq9ZcNlS03BIlEALFeQgeVAo4Q==", "signatures": [{"sig": "MEQCIAcLu/gCueEyWAMrTjqvljAm0NHI2jM8JH4G49IHXKz/AiBpTnxsZ699sHQVLifvVtRVASJQm+8czJ0cK2RmxSRCKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3078}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.16.0_1635551254180_0.6926843216452943", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "593579bb2b5a8adfbe02cb43823275d9098f75f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-usYsuO1ID2LXxzuUxifgWtJemP7wL2uZtyrTVM4PKqsmJycdS4U4mGovL5xXkfUheds10Dd2PjoQLXw6zCsCbg==", "signatures": [{"sig": "MEUCIQDvP4Ih4aZYs5xO9PGYw6dYPEx03lzWBeeTkRIF74leeAIgMEcbDsaLBVR6P+1DBHaZ6gPWJ1mX5otEvIaki5V23eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kPCRA9TVsSAnZWagAARXYP/1cOwVEDKr45cMUCQRFR\nCpzKCJLg6LBx8cgOBnO5XuzKXsjG2WIP5COhiVkgEfqVA3hAiavyTKZnmLq8\nurevg8AUFhlbTPxUdnFKRf7qRewE4I0QqGm061nkzdXVunfCRzCwLERY/vkQ\nMVN0Sg74NDQ9uNYGfP7aRJccW1e7oCVFVVhCYkXvq4vIiSJZrh2UupkXNx7V\nqF86svjjlOHETT6LZhJ7ODboEEUY5VObMmZ7ldS6C8GKmOK3FSBqLH8kNrTP\nEaPnJgCc8oeaotYwgh7DZfYHqsBSobU2+/i3Xj85OjykjifRP/kpdXLp1Dqc\nvxjV/I2a2UkmAe66oGzdBzqmZjYToE1Lv88n5m5/mIAllE2QUlyalYfigGgW\n3pmPhNyloXwMrlmPF1XA5+oTXJmuBbULsf/ja/pmFbpS+u/i23qFQWzVGX4Y\n2WEub9bUuBrxw0+6bXuEXrV5OnMGHFu7sYjzm7Jg28oNS7BCWdzQR8otBd60\nNgYN9qnxdujFZENKVvnd+0V5k4hvY0mcWun3Wfmg0Wy1KFiNIL6fZfddbFhm\nKw8mXILvXLX8ilEXMJian2G2/QkBtT1ZOIs8+Wuyl8M5Ru3Hc/JKBtHMlFy1\nzKGzmGdxAvNkv9bQLj25VjaC9mB6Dkfvi+mnlUXL/uK4U737Rzfchkc3Rsql\n9nb6\r\n=Ctpi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.16.5_1639434511595_0.633872899820128", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "c84741d4f4a38072b9a1e2e3fd56d359552e8660", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-NJa0Bd/87QV5NZZzTuZG5BPJjLYadeSZ9fO6oOUoL4iQx+9EEuw/eEM92SrsT19Yc2jgB1u1hsjqDtH02c3Drw==", "signatures": [{"sig": "MEYCIQDk/FoUPxDt3HJ8L8iO2LuGdI/nsJycfYBj4RxuQkeaagIhAIMa/8IX1F8xRJUerH+uHt6ihM0dVAaKWwwb6NwEigRw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0yCRA9TVsSAnZWagAAxFQP/ilyoFQkEemxkEiBF60Y\nd2m7tQVVJmh8ujrXQYphVhr09aQuqT1ORWvRyAheJVgycCOKBPefKOPwRt9Y\nHnACJIcC80Ap9fD/KK1Fup7bRLAsN1XIVpIe0OXYv1dPXlA3C61yL6XIIm66\nEfWLz9ma0NzEkk/p/y0jfdMv1tiLXyDoRGLvxr3IViWSdeXXA7ig7MyFFcx1\nJ34pL/To4Xdhw3L70+/07BlIDecfqCblxLV/w5phRq31wIrgG7TsrZnLFZQd\nUUa2faSrLT3BQXqTmoBfvSRokGkdyKdb3HJGjv5kPOre0ICvuD3RAVbLdcpU\nY6S1Rv/M8fZa/n1dv8rFYuriqIvT6lqU0tJ10cZdUNVHU5OoKDkEk1BWkaOd\n7rSjt1n+ar7DMFOorZ4DsyJLTZ1uMBoQLMcWpH5j+TodTajsKmECKxkKoEd6\n2f2/hLg7lSAjGK6z8qgiTB45kk/iKCeWCRTcwFuYqagY/vXmfVtUww8My4Fd\nM8ax6Uq02iG0LAAmC13oMx90WMbiFnIpK4lDmRQ6qjQpeSdyRiOrMutpUtiE\noo/ZklHHUh6uSJXy8I/9kyi0Teu6OLlKlHBusA2QpdAColY3LAF2sQnhcJh/\ns8cdUCs47BCF7nai1A/gucW1x0SIgnLZe99f8KBv0mQY9h36mggIYfLo65Rv\n2cgw\r\n=Y3FE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.16.7_1640910130402_0.24148613331153546", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "c6706eb2b1524028e317720339583ad0f444adcc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==", "signatures": [{"sig": "MEQCIEJTjMkNqJSXFAfq/LslXyIg7uS7NjMn7Gf2d/mOPRGaAiBpvd5hJtMmyCaS30IEGdqiwDH+Seus0IkFDbW5L+wGWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWaA/+PunJ7kJ+irpXdB9LjtAwuXuuM4es/563bTfEqLXTSz1tRueu\r\n6HFppFi3QuQuwrp+juD5NI3vNk13m236E71DkJQzP4WRWM+ele7G0J2Si+Ay\r\ny748zRfBsCzw2a7FOsT6a902JVzVfclLaYwzP6481wgw8BbeqDMBaDTZW4RX\r\nyM//M+5kyOT2ZvhM15Yo6oCiJeZVDbap8fN3YNjCQQ05gyH6A5jnX8aOtbsp\r\n/vcuFo3Dn28vVXaTjcyolfeQKmgPLCaWcu4RYcmyCQfsEtdv9P2JveiEeWhv\r\nerzB3y81GnSnMi6QrriIxHVNGalyjtRf/cQIZ17DAKZHvjrdFN/9aS1Y5mb9\r\nhGHYE2fyZvDAyJ2jsyPS9VLQw5/9vg0gavwO6VVhThcUM4VYSVQ3dJpbjM66\r\nSMUCVI38NiUa8+MHQn9gkaquMcyyBCsJyptMgASmZnRBMNs/UlBArNlRXInX\r\na8xkURSBaFShKSyZgPAadQWiegFBYsr8YTb5DKOqOQQaGhcNmXLMaALlDEfc\r\nsY6+xI7j41RpP3fNc8qTx4rbuAdXqAvM90UDem+diWEXZfZUltFYergTd7/d\r\nXpdEWEyq4For7ibh6JzJCo/w2666i1UJv6dvciJ3npy7/XpK6ybsjO7cMSTh\r\nrvmSBl4bjmztOYvstii++qbAs4awPjZmLl8=\r\n=VV0I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.18.6_1656359404453_0.8300773520141587", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "a8e286772db45a97c5e7dd3ac0e3d62c8ddafdcc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-3fEtDQZwFqJVV5MamFmrNfY6xXnCKYl4lvCCcKFzoPu97d9ZTRt/SktxsrrevwSgV8ADTxsubvzBpQbJv2InqA==", "signatures": [{"sig": "MEUCIQCJc+oQykHNvjV+D5JwhmHP0PoJlahLwkxQrRW2FpDJDgIgIWYONQkI5VMrPqBy2RU2OG1knF6QVeN3KEZJP9PwQCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoq5w//TMzhLM93kZHFUJ8rDXsdYnywAHYN62321Xm4Tk6y3iSGklqk\r\nkbN9walw4jjAzzzjMYBBDT59/8JMr4lqR+EMYNuElE0RLRrE1LlUN/4IheUH\r\nfuPLQXPq1jWuuZLaKMFFwt0oKh2Q/BE3xwO7srHU05tjFC0kOl8a1kfdsrPl\r\nRrJO/1GC4QzT7ggXSV8FUZJLNsAJlKWmzMZjiUYKP4KHQqqF1zIubers6GkH\r\nZht4ygC0p4e+AAxmJtmzKgLUDe7pF9M5mTGzdvejFBaBUEDtSZLg/QmftBNE\r\nYJf4KXyV/GpPCvTVT5hgjSs5ozuJOqHR0H2F4wbK6dY3zOBVpc0hF2xk0A0S\r\n3tNkjcnstj3Ud6Q6+BwhaycNmtvfWZEgRh+E10xBR//3Ny7aEjv9xZpsB2Jm\r\ntlZVj6WGZfKDtUjyncIAU7HiNups6T190HBNdS0czwInNLl3pQPzJchIE89e\r\nZ80HmiC0JTp2IZEK7EtDxatqCO893TbxD/Tpg8F1dtGATR6S5v/j87FxMFGg\r\n0/xlRxp/cBrRTZKJtLViMPiNqSxPtKp8uyrYZwXvapnl6jKgEROCbpe+mSeR\r\nrkZygCemK6Muv4KQVpD1JMQ+gHKhpenuVqq+NmtpitITcYtVk6R3k5h+hL3K\r\ngCNHfA/q5u67ea7vQJyv6qBRiZlS08Z6KgU=\r\n=TLj8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.21.4-esm_1680617370114_0.050388799009359", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "36b12d76641c12672a8e4e03f5afd3aadf2983de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-igcSXO9jajlgMPbYLloBlRSw5R9LKbFq0FZ8xh1RHeTc2IMUFCdkzwQApwnFuZp150oNy82PpAmEHYLf+19i2A==", "signatures": [{"sig": "MEUCIEjBvKp/kcuWOFyc6Uymt+XVvKDhz+ONKinua0M1WpAlAiEAybFL9a5+q8x0H5UeFLpGefLNUDg1MvFA3Wk2tvfD6Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/Xw/8CT+iiV7WhBw2YS7zQ48eiucNXoZ3akUFfSAVTt3kdUzM4sbm\r\nC0nL+rFZ39URbqNpL9jAc8V6sSvIRDHgeycpbU1Ob32J7TG7yHyG44M+Ta9d\r\nYOwLUXZA7xGWGvMXxw2o5c/PCaoBJJWrOihafSfPbmmGW17mSgt48d6uc+S8\r\nA9+U2HBpAp/+lrlh5omZYjT5UpZvYHkq5mLyYNadsCjVXo+6MoPbL4Q+ernJ\r\nSwvhq8IOFqO3i4/883+fxOgAhumcIwk3VRReMrwenLFuC+a9H4VcrOF3+sKs\r\nEPmsQTyF76nkY9oRz+6ohGIvDMco5lZab/EBPsLH4LMEjqF1pwOSfxrRR6K9\r\nRsl2nXSdngYdY3UelnMpPk4geQFkOqz2KNgy5n5EFrMRNj6wx7xzQC8dvzH+\r\nGdKBICwJ/cyROTWG+AF3pXdwukni/ZXeXCYZDQqjpq4Q/YEuHHLRPQ/a778e\r\n+/0o7xlHLerVWUojAoJIxMudl6B8jUUckzqNdMS85Vcx4UmC4dLCD5mjEg5B\r\nbWcTi16PdoVzq7SK34y4hwJSNbI5XyXDJmQSoFQPnhi+3LSLdAFsOcZiTEqq\r\nt9SWxIjEqr+kCfGup5RgsD9AzPQVByhfYHpwB4Q2tmT1ZOZGymas1X2pbl74\r\neIn5QvdxkQSBkggBQfz8MuFztS2f1fiACTI=\r\n=ZqGR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.21.4-esm.1_1680618082889_0.1454776511711715", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "b09ceb0cca7917703f2f67f95cdc2eefba7e6783", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-aPuEZjD2rDZksKtwHKEkA+KZSrLMzuXcCDb6ej/AiWPRM+8nVfizScw1UIlSXsbarodLOAJsiQE9rF/AgK5Z4Q==", "signatures": [{"sig": "MEYCIQDKaVJ9Qo6iI8GMS+YfKh/lvjyvv9hrs9Gs/CKzkbJ/WQIhANwXWcsYCEFdTgmSGz9QQ/gbv5Fvf4RHaV/JzcCZ/JhE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDabACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo85xAAkd/mO6BDBz89EE3bt+2Lw03cNLCxzzVoeRsrt4du7vgQgU6c\r\n4et/yEIQs4wziyReQ5RrYpWHG86Nzy17M6ffbLNhspCQBTP1NBO3rv8kpNKo\r\np4R97LO/WmnpJIYcTcpicMNGbJW/HgUmVdY4gDEoanqTIA7B6YuyKxZou4jn\r\nr8rRd4ksAudzSndiTqV4GPX76heqpIl4LBiv+1ftmLeQr0zDb83hjgsocTQP\r\n9dh/o888NFkJ8NHLkYx8SR/lcnZHdJ7ySecDOmnflr5iKTazIlcaJO3BVxal\r\nQfm55mVqmjTr5VQZO9Tik8VY4XliqDdnz25yT+5Oyu6N5vQybkcscmV4d9hk\r\nfrXVsEGRRKAwodysPBbjNNbbc03qPTSa5BvAplF/4AywesnhDHvnDnK788Gl\r\nrRJKJQDe0C3c4ICcND+8NOgMlzoFH9jyLrzz9/hOO7CB8Lt09nrGLipEcvMQ\r\nLxTFlG+PsojS2x86x7jcPkYX/ajc7U30OCMCEOiPzrgYEfUZBWWq+2ph2Ly8\r\nfm44OGVRYn3sDK0xXzF6suWwTvoq1tE9FIcGhEMihJtjeRkCDP8joQIaMsEo\r\nHDcHFDi/GK5ai7EX06bh88odONCoMxUSKhkfRDjwByvbFFsBv6o58rYUL4hO\r\ng00vse5cXrB8XwhV7UOBWCCOlTMZpnard68=\r\n=s9uK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.21.4-esm.2_1680619163418_0.4402450004552272", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "16d3038b346dfd89fe6e6fcc1b04577b718d614b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-eb8G8WKPhhwNlUYjrjnrEj6Njph0QWPkXcREpt4B16ibmcBmgf54GIZqJxTlT3DPdu4nl33sY+bzdWbnUwBMuQ==", "signatures": [{"sig": "MEYCIQCmgw389jR/K0U80qmDGpUfT1uyMfX2BScE7HxUUUzIYgIhANOSaLVx/gBewQTbk0p9r0O4y4UEE0NilZiTf2kBQ1sv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsIw/+JfoJmlFoFarWrscznOvpeAjRPTvzeHg05ZBnLWiZB/1LVIer\r\naPnWSiXlQnkkJ1WCJkYSzfWE8KrBa1JtycdjJL0pfuDzdTq/iF5pCaMKkfAj\r\neXckNzOWlC5J42Nc6VCtPdjJtXLQbBn7YDVvXgQ8ZZ2Z/vpc7FC7C5uJ3Xj7\r\n1vWIwxa5pkqHZlEo7X7knYRRBhZ9tNDaY3QP8Mb6e0HJcW03bfz64hAQqpW/\r\nlewynfwX73GhZuG3k+7cWA1DIpTkg0WGubhBH1JlVYAkiWftHK9finDSufKo\r\nN2+NgXzx/HH4Tjk2hbhNnmc+nlPGCJNQPmY6K2JNNH7caNLRTdmbd8xKVF7+\r\nE0DQ0YiPmDa58Wc88YTMZAX8Mu7qs4WZsHI8iQdDcKHRgc0m7tLF675sXw2G\r\nhoasYD6srsFRP3fSnfoaeM8LtnbJ3wMhD8vPVTEx8IcbsBDA1xsibqAxNRUo\r\nJafB696cWd1OAn6mjC111S4LXCqNuMxRGQPoxQi6LUPbaG5kg4Ma/mIa0ZSC\r\n2kLaOB/Ewf6oxj2PTbMItAQ3ZPwXMZdNcgy71Qdr38FtsPXRlVZcA3UuS2u5\r\nmOBE++fKMsbUGlGpXHOvw25LUR7ISD8CG4CvUQoGtehlwI6va1mlSQCY5lnT\r\nWIrvnyLda0WuyGiRhdXEE40xrytr2du/5vg=\r\n=Xu1D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.21.4-esm.3_1680620172396_0.21897619721649675", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "a0fad08d1b1f3002401e3c360dbade398e9c4693", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-WkxblgpnUfAu/dhNNNhmsb+vrv62Sm6lRtwbFdT6FCLtN/YomvHfenEc2I9tNBl7Wjt6NfNUv/f/8pm4L+XNxw==", "signatures": [{"sig": "MEYCIQDeSRUfTZcv7FSjD7SPGPGeB1kzb/9UA6okONAKOsdJhgIhAKFSMcE/EgTSr+weKHdX1bIgg0tYC3B/3sCZerrtvK1D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEDA//Q8/MslJbuOCfmOnxRLE+xTAXHzDZNu59so7CmDpnzrIL8YTZ\r\n0RpsXi4JhIBbG0oFM2u2fA9Pi3bb8r0dXHvj3aPynaBbF0m3eFRqA44smmuU\r\nCDdJtAnmQk7bosZFjLD36VjJxmI/yQRfQLmzIL3nsyjNyCM+02kVn12usgQO\r\nXl6oBGdFMX0lTl07Auwh4Ej5mZW2hK4DUWWNnIS1+xE7v1Gpu2zE1qpxJt1V\r\n4bTb3iQGMQ5gUMiU3ihKI9qAsCPtvheiUCJzwXckqB3HorCCoFYeo/+kZ+3U\r\nNXjITyKm5GeXX6UywbiGnl5NAB4e5j++PnBv2AEdbH6K0YU9q4oZz3P9IjEt\r\n/xCgjdVALZBGNWiM+ICtfcCXvkACZ5RvdKg5+TALSu1mUGIa1XsSJA82Zepi\r\nPgrtr6W8jGFEzdjAjN5vVf0ok01sNCKqW6KYxBFswC5BPVPrJsDoWUcagRP9\r\nDdKD7FTdGbukt/4a52rFMI6XXy6w7SaPUJteo9k6n9JrTVV+93yFgjX3Q39U\r\ngB+53xg9JUJVqIM86S6cYNIEMc4oIahnuy1ZgslP9nMHsrLV5PQ85QYf8SnP\r\n1wdlhytCRIvXY27swFPdSj66W4OnytspM3zYy5ZmSLySJAgujGfBci1SbEa4\r\na99NZuc+6d4ZBOVunM9Hb88My1j9L68MUL4=\r\n=Q+Dm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.21.4-esm.4_1680621204433_0.311584500124765", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "295aba1595bfc8197abd02eae5fc288c0deb26aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-zf7LuNpHG0iEeiyCNwX4j3gDg1jgt1k3ZdXBKbZSoA3BbGQGvMiSvfbZRR3Dr3aeJe3ooWFZxOOG3IRStYp2Bw==", "signatures": [{"sig": "MEYCIQCxVdaZUuOpQDXtxqYYcKN2l4aRuGPL15myijGNGJv/9wIhAMRH0wHKm0gtRGDlR/8agq5GBoh90I8Gwc3szq7KEfil", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4633}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.22.5_1686248482253_0.6812479670347253", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "ff2c029ffa1b8bfbe6672195c576ac1b5f3d2315", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-fAuJ9rNzkF6Hf8l7ta2dn1zWeQ9FluCsI8111WL/GMergqJ4StWYgMNFPNAHq+wKLmKOmYAdU5A2I2r6wC49sA==", "signatures": [{"sig": "MEUCIBEexUf0ZI1jyyAg2ece9B8Sr9rt1fWZbU2Lfh8JoBuSAiEA205O8QsYdh2WKlmuU7mxYM7JHnC1hY/bkrNNv3SHAIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.0_1689861598107_0.3030073575881329", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "73d16ad4f9e00c4b28cf393585ef1291e62eecf9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-JuavoBC8POTerj5fi4VMROTIgBcKfnonBgTySmK3d+w0EQutUAkeWcJk9mEDjqMBSJX4eK/PDEaL2CFKmLkFSg==", "signatures": [{"sig": "MEQCIB3H0tAr3/79rXAU464QoBrdiACnsoIHJxAivfSsPJHPAiA81P9WmMXPZN/okCJ3tkzR22rvG8rqS3YPFdulfCe5kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.1_1690221122952_0.23258473983020922", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "80e64d6a12248f6c6a02aef558c54756410f8382", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-k8CXMA2ziNt8fRZiRh26Ocf7kWDO7LXsY+0whG/iEx/x941Dk/fRbZ0h5xEaeNpbtuT7oNXZj7a1Kv0c0Wxhng==", "signatures": [{"sig": "MEYCIQDh+p8LdsxRtM9XrNXzMaqtKFDLj8ZvV2ao8boT4bxWOgIhAJW+5yGOCr8WLYjMP4qHqXUvo4NV8KvQHEWdyXsBF7BR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.2_1691594097669_0.11245310985705492", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "0293c33851851f48bc869a53c3fa4fc09e0b93b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-YkgsXJmGozNYgqRzpZovYPOZv3xnkh5xmvbqTO/EzPAtne4//2H4f+n25ha2g8ocSvnhNIRes2fRTtFbqUXkHg==", "signatures": [{"sig": "MEUCIDGloFikzCexkpTLR+S7RU3i7UVlb7zOXxCwEoB4SllLAiEA1G/EN623PoHBI6MTip/IkWcQJidKNtRcGWlZNJkezmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.3_1695740216502_0.6721986335032613", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "b2c26f57ecafcdb255834ae0381a5c0dd8bc8875", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-sKaFRBUk+rEvsU4OIAqLYb3qath6RpSyWVsO8T2Qy/XW9g5AzhQvHKyPoDw86hNek4E7kds1IFAgw3RG9hLbVQ==", "signatures": [{"sig": "MEUCIBvGSW6LZGhRegBDpqOBhUXDiWPsAM8hqVvHXpPcbLh7AiEAnTHH/8CyBnFdb1RYhTzzGeLm3CVDvJEFKjyiJ6ZysQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4516}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.4_1697076381011_0.9543218590376601", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "dec45588ab4a723cb579c609b294a3d1bd22ff04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-HZOyN9g+rtvnOU3Yh7kSxXrKbzgrm5X4GncPY1QOquu7epga5MxKHVpYu2hvQnry/H+JjckSYRb93iNfsioAGg==", "signatures": [{"sig": "MEYCIQDDyQYn0LOQ7izUFSar/UDmTX5wlVgkEs5wKpoOghjpEwIhAOsQPcPxtAsWUbINs6EAHBxvSz2Go4g6dLNQ1VJFh90W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4712}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.23.3_1699513441217_0.08057958981099222", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "01aaf80b4a4fdb80c7a5f55163c82c699169177c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-NEqMgC/SylyuzDk9emPfkTRubTyaFN5HuIQZLT5KFPA0IXlgrH7O4+9RN8D0vROeZ3sQ5I2+W+/4TLsBb7TRiw==", "signatures": [{"sig": "MEYCIQDN0OBxiXybQ2bvbfY1QIp3GMY7tb0Hj6B1TahTK8Sj/wIhAIFYEVL3kYrYcXBMOwutC+I0TL91qOPP8U4mBqTMcBY3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4629}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.5_1702307935682_0.2002222699035523", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "2e306d000f6d33998e3ec489ae6a8083ccee2e19", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-RLgc2oEUtnb18HmfIOJNIFZJhE06ci7YGcbVmEOGbs1qTWOjfDeWj40gGvJ9MEUEYX3LBb4So7n67xRzeb2blw==", "signatures": [{"sig": "MEQCIFCoG6JqcCd/qooYMylqMmLUH8m+ZZ+hTUgXMYcL1ms/AiAyIlzw11armZdfl87bWjrVr43Q7UfYKO8hHoeVNKo+oA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4629}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.6_1706285649278_0.6759674480120792", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "48eaf0ba5447f5aaed9ae26b5883e97edb31c305", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-FVKf647vb3lwhZdQP3bpRW4KEUOwYNwfmtDPiM1F3Ayu0rwo2FLQHn1qzcrlsba7cQdH/Ok7cV/Cx/FKlOdNPQ==", "signatures": [{"sig": "MEUCIEDRchYBvJhAY9HfX5oJUYZqnRezRMSt/ARjmX+/wOAQAiEAl4dQlaWcf3PO+pcEOgqiWRYAED0cwZ9eiw3qoPupNtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4629}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.7_1709129097226_0.6359196744667119", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "f03e672912c6e203ed8d6e0271d9c2113dc031b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-9v0f1bRXgPVcPrngOQvLXeGNNVLc8UjMVfebo9ka0WF3/7+aVUHmaJVT3sa0XCzEFioPfPHZiOcYG9qOsH63cw==", "signatures": [{"sig": "MEUCIEHIm6eCBgbKw7Bz4XDgJII24xwT363o0D6Hybgr3HPAAiEAgArbxfNMYyzHPCrcFRiL5ICSeP26RowTLOCtWTXePDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4643}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.24.1_1710841745495_0.9280188214559653", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "4d35494164ec05c8efdf40a04550c184d0ea290e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-AcfdLExaec8lBVNMMrozulSdXADG1ygQSDV/F4PxKth4/lCjTGHrG4GqIYZSYHblNYphpg1Ra4A3/NNP9utrsw==", "signatures": [{"sig": "MEUCIQDkYYHvUuYwVDWyRcMPxrvrGbG2Tcn4EWaBPBRuDc1XEgIgWmJvzePcYQY4z4UHpYOTj602ojDk6wl25ws95Ja3ULk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4543}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.8_1712236792989_0.26410722516412366", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "1a78127731fea87d954bed193840986a38f04327", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-fN8OcTLfGmYv7FnDrsjodYBo1DhPL3Pze/9mIIE2MGCT1KgADYIOD7rEglpLHZj8PZlC/JFX5WcD+85FLAQusw==", "signatures": [{"sig": "MEUCIDPXSGDvDOvIzOXGOkTfx6G43YmplIbQ9qjYmbpR49dVAiEAuQNzvpfz/wmOYc0nJuUV/Sm0x+PMYf6aUdmpsV3ICHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70564}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.24.6_1716553475757_0.2357921505898639", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "7008d846e85ae0218b37d050d00984369242d5d3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-rKigSkRMT4Zb1iSL8ZvkycWQLSk0OU6aFac90MmTchefQqbXw7c8sAycfwG97pnG1n5OCem5cAa8VoDx1bBgaQ==", "signatures": [{"sig": "MEYCIQDN6pINPvTL0TpM4LLoffoFtrled/5akTPfD8k8nPvQagIhAJMiksU9bZmyDZ5lmJaMgY2bM76MG6OWXoLjwBNwUzeJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70774}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.9_1717423459693_0.3512880781317562", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "a9da81a65ab364c6a9e8eb76ac1049bef5b8d9d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-aW<PERSON>4IcLohlMXJoRQ49KSRa6Gngtir0V7ghAucZVqbYCVwXgPrwgYMYumrhUyMn9jSzCE7Y4BjoOtlcYIMfSfTQ==", "signatures": [{"sig": "MEUCIDiMju2Z4vssg1T0YB4jZKERnf8F1V2KaberB1jxWx63AiEAgTua6NPL0qFl6JdGfZab2ND7x2p7XgF0hjXQsGrkGVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70781}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.10_1717500010529_0.22333155009246575", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "96ae80d7a7e5251f657b5cf18f1ea6bf926f5feb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-kHPSIJc9v24zEml5geKg9Mjx5ULpfncj0wRpYtxbvKyTtHCYDkVE3aHQ03FrpEo4gEe2vrJJS1Y9CJTaThA52g==", "signatures": [{"sig": "MEQCIFUA8g1UNhIQegCrH/6sY0QFwfba3+5ZNYYR4lr3lfUgAiB7n9NlwoZkNY9P4It3qxYFetVFDdx3jxpdGUrGcjc0gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70560}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.24.7_1717593325308_0.8584892816181193", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "494280172dddc12af5846b95f6368397fd44301a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-D5S/GxCa0ba+1XgFETAFhKWPRS4KDJSgo3lM8qCqi/aiK2xYpSPS6H5LOMLSEfzThoclGsUFmeqwvBuuFUhTIQ==", "signatures": [{"sig": "MEYCIQDTnHm9eE10RAtUCtdhKfYWeE+je6Q/r0ddr6U6HRc35gIhAMkCFyHPvOL/pzh5ZuU4o5F1oElglyHVF0RM2DJfS2uz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70670}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.11_1717751735103_0.46722075382624095", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "39ecf448ee318617b2abd89e01f0976387624ae3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-2co2PX3RHWQXAK3+aQfceCj2Y52V+a6gOz4hSSzy8uDi2/9L1j9jatje/P515zinjzvhbhVV1zZQivLQRjGrbA==", "signatures": [{"sig": "MEUCIQCNnzMglN0ZtFCRAXxC/5stlFfhIBEd2ebsnwxF9RCdSwIgW8K7MKJZsMGvpCZa4AfBnNLdr6EAs9tT/2ID31Rix0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67466}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.12_1722015212438_0.7719439670462778", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "341c7002bef7f29037be7fb9684e374442dd0d17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-ZFAeNkpGuLnAQ/NCsXJ6xik7Id+tHuS+NT+ue/2+rn/31zcdnupCdmunOizEaP0JsUmTFSTOPoQY7PkK2pttXw==", "signatures": [{"sig": "MEUCIQC4urUXoUvhHmd6xRzjm/loMS4b25gs4bDycVRW38afOwIgREdbY947Cded1ceR/zDnV2yhzEJyml5JkmKI1Pm4sgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75098}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.25.7_1727882093099_0.05007983727873144", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "c7f02b944e986a417817b20ba2c504dfc1453d32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-WqBUSgeVwucYDP9U/xNRQam7xV8W5Zf+6Eo7T2SRVUFlhRiMNFdFz58u0KZmCVVqs2i7SHgpRnAhzRNmKfi2uA==", "signatures": [{"sig": "MEUCIB2NhtJoQy24kwSPcQPpyENqP380wIwdthnIC0yQ2IAnAiEAxzxwaA55hKlnErpLJXtGC7hDJh+yhOZfpnJjmzZ8Muw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4643}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.25.9_1729610472038_0.7393471062597532", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "b1f613ad0ef4114d31c59eb5a2f504bc99910212", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-ONmT9I6+L+Hj4qz8CwHg9J2Yl0KDhbtUqeIIW2y2g+pTSynx1AmpTvu95FhoQgKrrse5ozUHVmX3oSQg7cZWdg==", "signatures": [{"sig": "MEYCIQDGt+RLD1kxYhd3sj7iaz8KYErFiBhXoDArIr1Vrb++wAIhAM2GYnKooyHRL1i2OBwTXJ0GcEm0kF+PxMqx3o8/8Wtc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4881}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.13_1729864454040_0.8252855906786687", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "f7c50106497bdc3b83bda985c095f38de54ee4a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-eEtdqKCMQ3JFSsrD1OscK1Dg7vMEz38wCw+GUVmi1gZsleHd7UzQa/FkTSyzWlRjdIHQmIsMKEBZLqwI8z8fXA==", "signatures": [{"sig": "MEYCIQDnu6pUMKJlGgIr6bj79qJUMYd1IC1QZe/ld8bg6ghdDgIhANxyHczU3GPEAdPvlqA5GewawOom+7X/mD2SZkp1+v6/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4881}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.14_1733504045874_0.49885248769887913", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "5fb390f620a4d3eab98f4f1ed8fbf5350e413cfc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-Xij+VIN+Rd4g5UwfXGydVM/qmCzItXTen0Mbf1A25hXMRoCX9WqqRH+N7aif77qGke25pAL9JVWEiV7M8YMsIA==", "signatures": [{"sig": "MEYCIQCx6/NMjAntf7ljfDXWe4F1hNXFu7ZPYgj8VJTlAXBZcgIhAJexTJ4Xvx5ArVzNkZuiGgbY5gz9A2+ZHL2zFFEpGPqU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4881}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.15_1736529871247_0.019981918212147454", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "96950dae177f586bf643442db510976ddac0a813", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-ZroQa5ryTd7IBn3xv/79URdg5LvSQIEJiEW3jCzmDuJEyfLhTg0j56qlNbyz1pWhDiMQGOT1GW8Mucoo6jt8dA==", "signatures": [{"sig": "MEUCIBxtTMXNK4w+hkSd9Du4Eja7HNwxkrZGiFO0YCgInyQIAiEA8AvQu2bJ1zo1gMvLI0jwigkiOBHbgm7QKtFhIz3azQw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4881}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.16_1739534347543_0.9410746778830763", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "2c8c1bfd37e6d59d6802d96876983df2147d4f02", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-yGGzZU4GO69IJNp43EkENMW1+/A4aFddnHzgJ8poyF2ILxwfB5RmtE35WAyvClnywCQLqN9jElme7muU16AHhw==", "signatures": [{"sig": "MEQCICGdGJHbEgNjGlLQl/xAKsjFqf4+pj9bJhGvRH36sW8hAiBAZZdI3Mk7oeKXPX9PVHUyUmwKgdRj7fq+8KI8api99w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4881}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-alpha.17_1741717500540_0.9786440980576423", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "18984935d9d2296843a491d78a014939f7dcd280", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-lhInBO5bi/Kowe2/aLdBAawijx+q1pQzicSgnkB6dUPc1+RC8QmJHKf2OjvU+NZWitguJHEaEmbV6VWEouT58g==", "signatures": [{"sig": "MEYCIQCWwks86E/jJbewkxxoB6CDeRNPXNfw33mMt7uk8J8/QAIhAMSvfqzFnw26nJJYODF4mD0kNdHeyTahWu6rbYGoXfn3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4643}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_7.27.1_1746025737243_0.9677145677255086", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "dist": {"shasum": "51a2ea0052ae5614c25a52112c07b837f3600071", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-m0dsTphdCh7EFm4VWDrAkZJACdZ1MK9eRxHdKP7Iod5Wso3GYYu7DGMdc4ZvG5MFH9PgrBRs7u2OQRLyoY6+2g==", "signatures": [{"sig": "MEUCIQCKaOsNenilHSOD07MEw/PQLCA5QlovZWrqj0zWQIj+eAIgDpCkgfIpHtCvmb1/DFUH/uQSluvPJjpd094ay5JQiuw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-sticky-regex_8.0.0-beta.0_1748620269378_0.670829421402005", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-sticky-regex", "version": "8.0.0-beta.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-sticky-regex@8.0.0-beta.1", "dist": {"shasum": "4408960850876b2dc9549dcd2c2f649da9f1a747", "integrity": "sha512-KBx0s3Y8Z5F7dajzjqo14weSzcw2PgO5kSNedwdH9aADacCFwHJPzQcWE0B9vZ1ZvCxMkjxECrkGVPzZMtLMBA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4857, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHNbeVphGXA8ua9KOj85QRUVCxl6Nt6wUNNm99Ithlz6AiBS2TjpdmEPa+AsftrEM1PovkfHEOg4DA8OgXTQlUvdhQ=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-sticky-regex_8.0.0-beta.1_1751447060382_0.5505458856019958"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:29.726Z", "modified": "2025-07-02T09:04:20.782Z", "7.0.0-beta.4": "2017-10-30T18:35:29.726Z", "7.0.0-beta.5": "2017-10-30T20:57:05.150Z", "7.0.0-beta.31": "2017-11-03T20:03:57.307Z", "7.0.0-beta.32": "2017-11-12T13:33:44.802Z", "7.0.0-beta.33": "2017-12-01T14:28:53.587Z", "7.0.0-beta.34": "2017-12-02T14:39:53.812Z", "7.0.0-beta.35": "2017-12-14T21:48:11.737Z", "7.0.0-beta.36": "2017-12-25T19:05:14.819Z", "7.0.0-beta.37": "2018-01-08T16:02:54.962Z", "7.0.0-beta.38": "2018-01-17T16:32:23.625Z", "7.0.0-beta.39": "2018-01-30T20:27:52.505Z", "7.0.0-beta.40": "2018-02-12T16:42:13.134Z", "7.0.0-beta.41": "2018-03-14T16:26:19.651Z", "7.0.0-beta.42": "2018-03-15T20:50:51.104Z", "7.0.0-beta.43": "2018-04-02T16:48:30.205Z", "7.0.0-beta.44": "2018-04-02T22:20:11.961Z", "7.0.0-beta.45": "2018-04-23T01:57:07.462Z", "7.0.0-beta.46": "2018-04-23T04:31:27.890Z", "7.0.0-beta.47": "2018-05-15T00:09:18.625Z", "7.0.0-beta.48": "2018-05-24T19:22:57.385Z", "7.0.0-beta.49": "2018-05-25T16:02:33.376Z", "7.0.0-beta.50": "2018-06-12T19:47:24.020Z", "7.0.0-beta.51": "2018-06-12T21:19:56.767Z", "7.0.0-beta.52": "2018-07-06T00:59:29.267Z", "7.0.0-beta.53": "2018-07-11T13:40:28.426Z", "7.0.0-beta.54": "2018-07-16T18:00:10.125Z", "7.0.0-beta.55": "2018-07-28T22:07:24.144Z", "7.0.0-beta.56": "2018-08-04T01:06:07.228Z", "7.0.0-rc.0": "2018-08-09T15:58:38.656Z", "7.0.0-rc.1": "2018-08-09T20:08:19.842Z", "7.0.0-rc.2": "2018-08-21T19:24:19.113Z", "7.0.0-rc.3": "2018-08-24T18:08:19.469Z", "7.0.0-rc.4": "2018-08-27T16:44:32.700Z", "7.0.0": "2018-08-27T21:43:25.635Z", "7.2.0": "2018-12-03T19:01:51.787Z", "7.7.4": "2019-11-22T23:32:19.628Z", "7.8.0": "2020-01-12T00:16:44.095Z", "7.8.3": "2020-01-13T21:41:47.802Z", "7.10.1": "2020-05-27T22:07:30.849Z", "7.10.4": "2020-06-30T13:12:13.211Z", "7.12.1": "2020-10-15T22:40:30.583Z", "7.12.7": "2020-11-20T21:05:42.209Z", "7.12.13": "2021-02-03T01:11:08.125Z", "7.14.5": "2021-06-09T23:12:13.863Z", "7.16.0": "2021-10-29T23:47:34.314Z", "7.16.5": "2021-12-13T22:28:31.741Z", "7.16.7": "2021-12-31T00:22:10.601Z", "7.18.6": "2022-06-27T19:50:04.632Z", "7.21.4-esm": "2023-04-04T14:09:30.291Z", "7.21.4-esm.1": "2023-04-04T14:21:23.106Z", "7.21.4-esm.2": "2023-04-04T14:39:23.552Z", "7.21.4-esm.3": "2023-04-04T14:56:12.650Z", "7.21.4-esm.4": "2023-04-04T15:13:24.595Z", "7.22.5": "2023-06-08T18:21:22.479Z", "8.0.0-alpha.0": "2023-07-20T13:59:58.289Z", "8.0.0-alpha.1": "2023-07-24T17:52:03.132Z", "8.0.0-alpha.2": "2023-08-09T15:14:57.846Z", "8.0.0-alpha.3": "2023-09-26T14:56:56.670Z", "8.0.0-alpha.4": "2023-10-12T02:06:21.196Z", "7.23.3": "2023-11-09T07:04:01.406Z", "8.0.0-alpha.5": "2023-12-11T15:18:55.849Z", "8.0.0-alpha.6": "2024-01-26T16:14:09.436Z", "8.0.0-alpha.7": "2024-02-28T14:04:57.376Z", "7.24.1": "2024-03-19T09:49:05.652Z", "8.0.0-alpha.8": "2024-04-04T13:19:53.154Z", "7.24.6": "2024-05-24T12:24:35.910Z", "8.0.0-alpha.9": "2024-06-03T14:04:19.895Z", "8.0.0-alpha.10": "2024-06-04T11:20:10.696Z", "7.24.7": "2024-06-05T13:15:25.514Z", "8.0.0-alpha.11": "2024-06-07T09:15:35.315Z", "8.0.0-alpha.12": "2024-07-26T17:33:32.709Z", "7.25.7": "2024-10-02T15:14:53.324Z", "7.25.9": "2024-10-22T15:21:12.357Z", "8.0.0-alpha.13": "2024-10-25T13:54:14.190Z", "8.0.0-alpha.14": "2024-12-06T16:54:06.069Z", "8.0.0-alpha.15": "2025-01-10T17:24:31.419Z", "8.0.0-alpha.16": "2025-02-14T11:59:07.699Z", "8.0.0-alpha.17": "2025-03-11T18:25:00.736Z", "7.27.1": "2025-04-30T15:08:57.414Z", "8.0.0-beta.0": "2025-05-30T15:51:09.593Z", "8.0.0-beta.1": "2025-07-02T09:04:20.526Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-sticky-regex", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-sticky-regex"}, "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}