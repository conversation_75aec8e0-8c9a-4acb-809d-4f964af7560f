{"_id": "@babel/plugin-transform-property-literals", "_rev": "112-cdfff20171149c4e9c0b0216c725a5ac", "name": "@babel/plugin-transform-property-literals", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9af0c7f557c7572c5459bb9c8274bc00f0d06d15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.4.tgz", "integrity": "sha512-Zyz1hfNs0fZjB4vSi1qFfkTMHpb1a3GNZTVWaZtnhALnBTxdmAFtF5BL3W084bSZnkRvJRLzFi0Q1Q2bmtIPWg==", "signatures": [{"sig": "MEUCIEejUi63oOD+kXipLmWirAzxCs4d9dzRNPto97RVMOMpAiEA6GIGMIEohsoujOxOw1Iv1iAB3Q6eRPS9RGzcrX/pJFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.4.tgz_1509388484963_0.4837572064716369", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3737ea1ce6db8b1a50b15fe86ee9c9bba48aa916", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.5.tgz", "integrity": "sha512-4A8Tmep81Yo83gpTU9la5vxHv9Bzs1xEndM5aK2Et3PREkcwGC1OQoMMRUENGfGdl82Zd4iiYeLwGAEqLofH6g==", "signatures": [{"sig": "MEUCICdadWZdU8YWHJduTEXnmhKYeA2YMblQ+Y6NREBpIKP/AiEA2MUw3W5k/UKhZ74crWNiIFpCwozQp7uPNBMj/P00KZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.5.tgz_1509396982953_0.2021293460857123", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0e29aed47c6344686c96ce8b943d1566b80220ea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.31.tgz", "integrity": "sha512-2FwvtIW5Z3aHXoc+bvJlyfEyQR6FIKYAC62P+SwsYu7dEHgFmRSNERv0Ku1quKOKdrLLLM6yB5Ih/73hZo66Pg==", "signatures": [{"sig": "MEUCIArjmhKPoJeO7nw7ZyklKotlS5gd8hkziNj+M1N8nQdnAiEApuuqAuaAVZJuOhQejsEquxYs27VVvj9cJgiLpf65V30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.31.tgz_1509739407914_0.09617801406420767", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "aaf20579d5b9cd57b24f1d6bcf61f8bf5e886099", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.32.tgz", "integrity": "sha512-YgbW+X4zGaTD17U1voY5imrODGLMyNzX+I54K3IDUKqx8Rs4TvSezPMxDT4z6od4ZOTMUNP4NsL0jkgTQwG+ug==", "signatures": [{"sig": "MEUCIGdDZv9ydId1v0Z9wjA9bltisUB65K2sqG4vkZv886xDAiEAvMNtMxmQwoZzxE1DQFpEL6zZee+kIDSuXq/XkvQqZXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.32.tgz_1510493598320_0.2462337976321578", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1f8ee3224c985112df0789eb1587aa62a2e9470b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.33.tgz", "integrity": "sha512-we1ZPSF8rUsbqU9fFA5HCtW++Uboywv6ElcWJd2XYBzOkUZhblwu51aynIcUSG4ipkfUbBFVVCWUGHyzL507Wg==", "signatures": [{"sig": "MEQCIAyL1WC3sFvR9RonYL++fYFkMU/uz1D6eegbSJQiD4FEAiB1nRgNsy/IPF9HQiGzNRF28Huf8/XpIvP3/BXSYI8mqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.33.tgz_1512138501115_0.8569671476725489", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "793b61b705098c767c444711c76c6717018b4d06", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.34.tgz", "integrity": "sha512-0wYzQDPtQylZjUlVhO+1sGS5sevkhJ7I4Fqm9g7GqroQxZU8CoXjNrybsb89YfUyV01qUfwRG/NtmJk7Kfi6Zw==", "signatures": [{"sig": "MEUCIQCHApHeSHN8bzWKXidnBnEj73qhGtD4wrkDmsmjFObwPgIgUq1HNWs7SXFIK3+uqP8jpclmyogFQXfOIvBkBkswdtw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.34.tgz_1512225561952_0.38727753865532577", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5668915c9696f0d12d6287251d4109babe46f601", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.35.tgz", "integrity": "sha512-gcLbsy0k8yhAZk01Dr9G57XSRgkcqQICySMLLbwcnbc1VM+Ya2k00A2NjndZLUYRcWpQhypQ9AesztH0pbncSA==", "signatures": [{"sig": "MEYCIQDaizKy4ajXYMYIW3jsCzSbsCFBpoZf+7k7vWYbAmyq3gIhAIet8+MYZwXCyEwMNz2f1XFLaBrPtDaClyiZwuP0B2K4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.35.tgz_1513288068975_0.22273129457607865", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2908a62d112e1561886670a4d9495fa91296dbdd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.36.tgz", "integrity": "sha512-8R15bq7Mt2doZ1M/jLpQGSsE0Y6kgDOI0NQMDh61keJhS5AriL4gBhliib0cQJV/3TKpwKXeSLakuixNOBMrAw==", "signatures": [{"sig": "MEQCIAKa0a908xmnoDIzdIcVPlRhRHuh/TFK6cXX+fsHix8/AiBRRLnUict4YCmNeW/e/03Ot5wAasIDaF3k88FK4JdMJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.36.tgz_1514228679399_0.1253397143445909", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "96f68dd9ba942e9f0038d7d45c414eac412d4b45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.37.tgz", "integrity": "sha512-v+cZUzyuawiTzvD5iTwkNHZoLbw8n6q1omayUcMLg0ktCJ0loMT+P1KXYmYnXBsExfL+tbZGXEQryX66QJMkyA==", "signatures": [{"sig": "MEUCIQCuODZ/gr3bbTKHtSYVDyZq1zyoEJ3OLV+45HiD42efwAIgC5F+zvqKisDm2i3y8Rc1L7WJP9/ixj5vdLdf6Upha30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.37.tgz_1515427352349_0.7144136629067361", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9c3a024a49e6e02e7159aca165ab2139b852ff0d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.38.tgz", "integrity": "sha512-vQgj4pmc80APzb7JVcvQ7uqFc0vkxGJ3YZWMMD/6prQfaWGv9zk2PqdYTQ6Qc9gT4Mt6Kyv9HNkiJNVHHEsLVw==", "signatures": [{"sig": "MEUCICM0KwXOPELmdltrBQXpsoD6yNk3N5CKg6WFF32EVhN5AiEA2fgpKKa5v8PcUZHS/pdCSQenBP41bF9nDCfNhWIKH5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.38.tgz_1516206717220_0.07680633454583585", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2374187590e9ad8120f519bd27993ff50b2587b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.39.tgz", "integrity": "sha512-eDnbu11UgyJ1OTnWcu6JF/1E10hcOKz7Cpaj6bjTfMW/a5EjKm1sJ5+XkDLf4FXx3dG+2gNLakm3FhaZzklSWg==", "signatures": [{"sig": "MEUCIQCvzbXr5b5qUfQPfYL9zD7xmdc1t6mKpVl7Mb342Nk7swIgFSuwznM5qEf4SY2/iAF3B2oqE3FP2pFIfMIzk9wpiQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals-7.0.0-beta.39.tgz_1517344054965_0.5325346279423684", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "75d0617c9b1ef635710ccc461f04de6dfe1d1d00", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-TtfRkSpMjSaLTbKB2eHhDYyWvqLiHIkgxQirAnk4Y4Fu3doLfk0wZhDDPQ7EcnTc/M3lUpnADjFoFD1RG9PTGw==", "signatures": [{"sig": "MEUCIGBuQ7C7n3FmYzP3CiKHZizLvdhc+y/oee9Ljrs3MYh3AiEAt99gOZAuGyiMLjqulrq6e3BQK+ZsldizsmxOyUh5Ci8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1761}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.40_1518453699007_0.1875350186348439", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "564b85f0a7e17209c6062b6ffea5e4a6313fe32d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-yUl++7dz4dW+unwJwDxrpogPebcO7hGijbXOlYMFXrrpHERlPn2CqAuLvxOQzdPrnDRHYysnAI2aG1goSMMjMQ==", "signatures": [{"sig": "MEYCIQCRqAa1CYlquHxJ2m9eFrtq61RiZ3DJkufvCuXJQZQVkgIhAOoom6+geJ2CnmOZOPtS39LqoKS8an2ulITEdYNmmy3w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1996}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.41_1521044771399_0.9776525163631786", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e66227d9335bb0a55a7c561754366d40b1bbfc23", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-3dYkXM7aZ8b6x1cHUhZPKYYcF/4HppoQfythpi95tumfgZQR2NDu2mzAT/ynE84/x8Vx7NrSxuNfB6e3CJuzQA==", "signatures": [{"sig": "MEUCIQC9/x75/6amaUWn3sPjOO3TEZsDa8gTdQ7kLh4biPNjDQIgGH5RBHq44RtcAhqXQpXd1mvXMy5XTrwnFZ5dwX2vvEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1996}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.42_1521147047201_0.3655428471173996", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8b1d931066ea4d77f4bb04f9645c3c6745075a16", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-29TxaPeoVFp7Nt+ysAIg42wPIG0yQQlrfMHJEgUALF0Z4e0tKqSlmVMPrPbLXUd5uiEdPftqVA61wOW8leKg9Q==", "signatures": [{"sig": "MEQCICQg3UytD4T6sjLJY9j5M4P1/aopntda4o69STeEe9VuAiBS/GYK7c1OYYxG4+HVrXr9JgD16BizsksfyUcfj+32JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2199}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.43_1522687707762_0.5827052376215189", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1d0ea6d29c6b4ec7e8385864f04e5750db2dc677", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-jpqwHxFa3Y5kDVlq74ZL9mNKbvAkjW2lgobo9jOYuc2PMX66cCW5yp5xp8YCTsidF/ue10VZ0HpVjmFPNaSkrg==", "signatures": [{"sig": "MEUCIQCyLmJBpSBofjxvVoji/iz3XWQ7s56q4qQ5EAU7/F7S5AIgJElqb3cT/spQAlGm5usAyvEKik9Wx9CmJI8dPO8K8xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.44_1522707609665_0.021966664789036416", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bfb32df45c43f393b7407051a0bcb5e71bc176d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-3uLI9AQBNZYohxPJGO6okUIUHUhXCmp4WHl5rVgeVRKFLVHGxYiXhF/IAJb/tHn02QmU7y7z1TMU/BwxnZPFmg==", "signatures": [{"sig": "MEQCICpQ8OHhwNDI7KTEbWsPvXM1xFJsKCPuSy5qpKMT4CF3AiAuRD4AyPTeW4Jlq40f17CgubZL3wTKlpSp0x4d6gQKEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1rCRA9TVsSAnZWagAAwL8P/39lpRLYQR3Ua7FyY9Fz\ngqlXYypFPXXeXWeHw52pR4OxV5ch2ldR+0p+OQaxILVMmtv7sGE25NSc+sDi\nCXEkWWN6TnKiMQ4cxWs5KhoZ4cKz+QamJ4jsgoupsPuiJ9L6WjHRnwE5mSmi\nVVJ6Rt+p/U7j0sYgj1Z200wNJ8c/zDtv4+81Y9AG8+iogxwCAquMSznkIwUq\nUjHsev4M1a1h1CfidtJT/8O/ckwsNVmFs0sBeH9mQaAmAR/abNYgWAUxNUmE\nzuv3xsCUS5ZlW1GJU5L+xLwAI8rNs3OOP50G1h+aF9juaUZplPvE+tlZz80E\npGhNBJaAY7l1Tn/dwlyRTtjLnXEcAcy+DzKo3XnLX3Jzu53HAmowi8RxBQCl\nSMDZcSs4EHD9HJ+lj3qkCRlT0h1PQp//O9z2XvO3mntJNJft9roMoMRONANm\nsGp5doY0MSf5NqnkFjdF/XRNKKrgQG28p/qTO5on2ceGglrcSohw/QC+PPCL\nDKr7e+/x+9+7FHfxDMCwJi9N9Z8wZHRL3jXU15gu3Du6a8W5fUAmbO2FPUrr\nqqGNj84mWs+el7lrtJO1S0UFdXZKd9OUXPc7dWSW8tAnMnyPDX7WEgBPjcyA\nElFV6Z1oAPu7D7mu9TXAFOyJJC9TbzA7lCzThbw5BDkMQj/UkQuk8g3qd5fn\nlGTd\r\n=bQht\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.45_1524448618986_0.37587120241102534", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5e6c28fc7743e8a7aa80ec64486801e28fea304e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-q1jSyGXA4zPWLpJZ7rPLpbQTv27VHSHWeU16xAM5PnmI1ZrL5XmhmhAfZ4D2+W3NfXbvaEUyxPBnwBYyGPKrNA==", "signatures": [{"sig": "MEYCIQDgj9O5ld08j1Nv3Veyqm1nGQzzefMsm6rceu59/pMvhgIhAP7jaVch3gG21nLqsf149H3bv9KoinIdg0NH9h+o8Qvr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGbCRA9TVsSAnZWagAAhq4P/3DwlPvFJ9B3wsK/r4Dh\nJkAs7BycIBqjtP8Rjg3BR+fhEcEFpCljZSROlElOAwG6bZ0utLYlBunQxiGh\nW90wZ0JpLKVjqm2P4A5fFj0K5jaUeR1iBzinWKqpz4JA5GwfUi7W5uq7xz9E\nxxnhIBej0X9Omsn0Vjp4ZI/uFoSk6Swgu9x7hIsKHEgYkKFgopVjTXgGuqCK\nDU/ib0WTyEPN2KoyERScUZHmpNGrG0jYFLtCPzO3W3dmFkobtEIo89oDPYCV\n3LgnRVatxnVWcpPNxbyZuSDJvPHC1R89DjxX3Tgrdfxt3TwebNysnm4vmMLH\nRCPFmdc9JrHrnrcfSfTjCf2a3eEiqwsBajPLThc2kg/uuE1euyCT0NmwFekv\nvktDhYtCqWl/I5oXn+FMMmo7ZcD/mK54OmmeNnMJz14u992FR3YjwhQi/aZH\nmogWBtXH8uDuyzQUvRmcWyG7zqvzOI1+b8Z9PqU6/X2uIHtKW1Ixf9tdM8bL\nkaKBZbxpQRolWTtVI0e2uuWwkskWh01ZjtwZnX/Ybd4/6kW8q1Qh2C0qunFQ\n9mplGhNllhwaMZ89FWu1Z00pDhpYLbFew+6KlcbtEUhRynYnC4mug0UDrm2A\nwUcEhnEpgxFre7d1Jy8UaaaLSQaZterWg2rpXjfZSVkgOdEnRV+7R/Dw+Jhr\ns8QB\r\n=Pm+1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.46_1524457881357_0.708071094887154", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6e44852694a016d3fbde201cdb824a11373122f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-BprUjFZGZ8+f8yHco0qIWFoEez/3mGScE+kzDmWIvMOLO9dBmzvM/vxTAUv/armxOl+wg4fUDLqSF99ZxjSt8g==", "signatures": [{"sig": "MEUCIBbDknZZUqjvDbInz9qnFhLai6i6a4VUfE7smrFLFFDqAiEAjuPvEjYLByjQSrJO6ZvOMfMniFt2J1yuyEPNMGcU2wU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUmCRA9TVsSAnZWagAAlVgP/imSyIvu2Pj8lbsRszdC\n4wbol2R6/g2LZl8D+hnUADNSJRCbysuCPT1cWNKobyYHGGNPVXG6vDeOE2s0\nWj8LKiz+79ql9rWs8fKm9fLpPi2SqiA6zqiBWi/f4RKIsMsL3VNAaNNnLP+Z\n/3CUHqYSv/juz4Do6evyfdsJwlCP6Fn3xgt2kgbveqlfWNhPrtAjDD9lyTTF\nWeJFmZ3QKKmKz6V2WaBBJuR/mQLgKtsxOmVuVAYkR+GR05gIjH94fN44kQco\nkGQ2u6wct7YirzWyLSFDwaXf+E449i23euqf66ZGxxwWjKxZsM4PRPFt2mVq\nu/NPRwiyTCFzpzsRq1SsOuXN+HJQWtIFxUqrov7XxGQ+kqXUVhLOVtJnsYaZ\nHOmnUb4iEL+rG/e/d7bTWfg/kfr+qXDHA36/OSLEYIwgVIK9Mks5sQ4KM/lp\n37Y8v5E2nYdj6rDqlLRcz7WaM9+j9NgLNTawCZN9qnPhJXzBRo0aAivgLZzh\npvT/eONyErvJp8FMr08eejqBhbU32zqGXfZiEAdDa5HZLGZqONSLXXWai31o\nQBZIheNTxCM+EzxJ1n9Lak6Y1/EClOfwt9fxOXOHCBsnBLBRX2/sOezA6HTN\nY1Pi7kEjyp5XYbxi59WDfH0mCT+iTcZbTt2TLble110CAuoJ24eHWii4JyUq\nlA+z\r\n=mDJo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.47_1526342949887_0.43501647170578117", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "02b3704519a701baea82c6e2f67f3488e65f1ff6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-6QsNN5tbbHz4YIJQNiKUKLIG1U7xc69SEEUnhOTE92nQCBupFH14Kc33Q56jYx6pbCKuwMlZvxcI9JfYdRA9cw==", "signatures": [{"sig": "MEQCIDKWDjvfazO1FGDk7FYFVqSQkoM3btvJB/X9vfDhyVciAiAYUcaPO4TPAJBKamanrzXrZvLmz1i22bIUcgy+VXLXBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxEDCRA9TVsSAnZWagAAmqUP/1ZMRq1HoUAgk2r9YSkn\nSfcXVGZLADcD5CbbZsWD2tDRpQ1kPr11pNdJ9mpf2vynodlcn2wAMXzxY/kS\nglnq4MnYOQ51+phNwxAt9+pQwzLNfTK09wt06nIhpmrVWCB0ZCOQ38CFKFZM\nmjPOgZ1kgI1VYOs5GvJTIX3EWXrzUJBPmt09wQCoIXCKQO412320KyA8AZyE\nN22LvKxDNhgTkEXyt+hRa6uduuKfHIVCBZ3ftZdNN6RGh+v4ep84hWVCqldP\nkzZw3i9FLzfqa/9eRAd7CipxS2Yb3fnweEb5YIShK6RR1xvkWsAK6Qvc34pF\ntwLq2zmgK4coEaNW6GUoOp5rqRe+/ssl91BtTcTpcHJCn1v1JTHwOHWbrOv/\n+cr3S6xavSteVPXqhLhwcAdRNEKETeAC84UiJrEpqZ1KGg1XD7cDYPuWJqfq\nQ6tN053eSiG4mlRv6pqopa4jWHrLHsk0SbKWfR8cbBs3TxU69FjEb7uoU+yr\nJofyuT8b25S7q5Twrwchw/Wb4Kgy4N0bLqkXYhKoReo270X/WjWiLh4vkVcV\nl0FqpCwGOv8qztdPb6EprwZjW2tMnGxs4+Z6+m4VDQSM9p+MeqlPju69zCFN\n8Gg+iDc3Imu//S8X5XplJdlxoCOEdcjV0VISrxW9eMKIXFZhcGX6KkXCBk+Z\nCBuZ\r\n=ojUu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.48_1527189763056_0.8936877796175919", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a5452565a55399c2df4e7fd6b3294538ae2bb528", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-AZuNsYY5cfGka2YLHXdvrOL962kmEw143VLJkeI36i/vkstQfBFiKvEWR2sJG8CdivGvEwBFxFD+52zkBrAgFw==", "signatures": [{"sig": "MEUCIGgsrbsujcJ8PdkiEqGmQMlZoOtZmF0YbpjdSsFfvK/jAiEA4duV/k1r56Hc/g+OLkaSmbl7JPjcYMupX1ScXcTn+N0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOMCRA9TVsSAnZWagAAY9gQAIc44uZeb99hIyBicwm3\nfIgJE3icMg15cbHhKklng1XMmH8CQEpmhHa7R2Qohm/rYy0/c6UiFnWxu+BC\nMYK5s0OvtYfNZdb8yCf/a8fapFD+0K8w/VMsvR/hx/AedetTW7cb6Wsrvoo3\nHom9Yi2MEQw4mfT816hdgf6AnxzAVjC9RuqLUyS84F3jEqfEuS7co3RHFXB1\n6w8aStJZMxz3qhad9RU9HTlHUbzNzKilQx6XxRwtGlx3Bc1BBAk9lsMP5M2R\nhWKCxRaap/y36FX625plYxOk+kCjhjj/NYUmlEWYb6ZH+bHJmnbZSr4+IJIT\nQFLl2KIce53nLKY2VGvAkRsVFbAIxVFpBKvzFZFJccBSS8fNGn7CVAxFGrg3\n1WopzKcXsMVnq28OqzHsV5E86xbUNy8T0XaAVs0WM/8nCyUfZ4J7SvWhD/Dq\neUEEt/RV4X+rh2O2c20TRTCDwsfmIrgynFwWGbQGk0LytfQrdgtEE4ppbVIP\nb5JdTYTnScTdkEEfOClfTq3juPAwwDzyeCNrkKnoz5EKnOfzclRwQKM/U5YZ\n77lB8iWtxQp99uMi8rfeFgoDsTjhPi9tNYl0S8+vt8tGHARLyY90LqoJZhTj\nOAa2zvIc7wBZNQjILySJ9/5Z/8ELaUpM3cJPJf4rI2fds9E6zdUpmZnUbfnO\nb+t3\r\n=IPji\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "a5452565a55399c2df4e7fd6b3294538ae2bb528", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "3.10.10", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.49_1527264140181_0.26786776039620386", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "137c8358a198ffbdb93e4256c872b448692bafa3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-Bvl2aGQrVPx7fCFgdHgxKanEaetOP5w/e8WbANopkGDaIej+5mFxNbWweIKMZ6dKuNDbXtYqK9WBzvx2FKWvMg==", "signatures": [{"sig": "MEUCIQDwoVZLvNtygKxW7B10W3qeqr4Lnc8m8ZQM/yngzxGgdwIgIRLOdLA56ykyDXSFSpt9O61py/vDKj5MdT5Fhyz3Hv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1939}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.50_1528832839649_0.847196795711828", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e76998a755dfd2cbd6680b5136e5b3d424811f53", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-mcFJBdQS0IXeEyST4z+atlDeuMUY7ofUpch5HgckjdSi0gOeD6SCfA27DLO/NSMa+0kcbsRVzxucvveKXiU4/w==", "signatures": [{"sig": "MEUCIB5+4Ai/HfwyT1OrrXbig+UqZHaXFSElnk+oIcQToMANAiEAukaOUbb8dbCvpEhpnSJWp4n8ziMKgcZU2Hv0Rlam1VM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1953}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.51_1528838394073_0.3244618323246409", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "03e56ae3934f1b1d9032f5924f3f24c95ce1118d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-LaAdEKiV8FNRXnsYjqq2t+lKJSUsxxuypdyXopzchPJKswaMy6cwX08LawGjX8YLyr/viJfaIRDpXI4xw7Baxg==", "signatures": [{"sig": "MEUCIQDAbNxcUo8cB71JwR9WzZvqqYgPPFo/pPCWWGeNQWre1QIgXio3uCEJKTO+bpWwfj2SYdZC25clU/RCNkN5mIA7mng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1952}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.52_1530838767100_0.9041180958948707", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f238a729594ac04ecdd82b2842a2966f78e88336", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-41Zz9mg7Rj0vkxWQwwmGhWDeKC5WZ8oElen76geUnKM0U8D3XLutzNf79ENnaRTRXK1XoARsa3H7Pzkq7yxs2w==", "signatures": [{"sig": "MEUCIQCvuOnNY0H/dZtOsRqLMBoPEQhSNStJyCYSS8ze4Z7scQIgMKnRPR0jQCcJRXf1lB4cvWMn7e/LoiwNSSrA0VsdpoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1952}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.53_1531316417489_0.40924851761859027", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "df293e4b1187dc96f250c864d1a4e809a4740fd7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-2GcAPG/kiNv2WTLTdaYJO49eKGyXFTw9zZAVFWJTUoJZMr2V13lxXmwmvKIu638iFIxoVqM7gcP3r85iOwumKw==", "signatures": [{"sig": "MEUCIQD4gnhibNuSZ1Rcycjn+gTFRIyTXVuKUhGiCReu/havMgIgSJusugWvfN3OAN/qNknOBOaNtuX7H0jqpBubRCm3/gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1952}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.54_1531764008196_0.3036981419263709", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "879ba496d3e53127c6620cd9799b440dd7b77c9b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-RoqxSsksrEi+UBeG5YysuKABMXhfKYCo2+awEDDylc06Q1p0x5Uz44CxpFhP31o+Z1byqSqp8H05+mJFRgyj5w==", "signatures": [{"sig": "MEQCIAUKXaR4LNBuFgOwbLTn5BQO+X6GXKaP3g2mFmwrgQmIAiA6AWLXUJwc4m/hZ5BunHH9MBjwhtA7g2zHWl/erUlnHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1952}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.55_1532815640331_0.6642238575110682", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ad28b77dfe9d97258b7b867658cf68bf09200243", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-N0TAh6j6FyNHImNaEZIqRgo+vAL0CdGDeuhfPCBHJA9BUwm/7A4LMKq7JYx3JzeuqFhrNgKA3Vo4xGAGF4WnQQ==", "signatures": [{"sig": "MEQCIGSLhFxhXJzyev4yoS8AyQ71jZV8o9bja1vmI1oYf4DSAiAcy8giKeAghOnSFws6fqwv07SNFr9XZK4/z+JHnH7lfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPv0CRA9TVsSAnZWagAAY0IQAJAAY0p8/jkYHGQCS3d4\njZ4CakURMxD7tc1QMdyATYIF9BW+h9j7qt9TAMp/bNynptm3FxhlkXiyMTNG\nHFaPsptMWJUA9Mnk8hmgI94NGU+ZGi2uAaTFMEYQLd83jere6gVjU/Ho8ZUi\nllKz5nsnkY5Tly4JcFvUWQc9fQZcQU3llsiKPQr7dvDkrNtwy7IS0Wf0349U\ns5BzJgBsylgJy3mZEZZ2NkxH/6tZeOwcE8LmU413k+zfh0EcE/y2LpVoxRZS\nIj+ScR1NoRTGsqdSh0P12YG73BDS8GvTuHHEjAdS2dCcgWLJQfYZsbsfxtGB\nVZLprXRT8y1xsgxT+9IiqAp3ciNbsIDBeCts0KaVpHnkNyMuFfGbEv47//m+\n/iqr5Iic/58mmmZy+P9MKsY+ylqmrufM3eM+j3ii2plUav/Z6THkGbbadoTq\npu/El5cCRP5qsht0fMqaogIfny9ZWqmwx18iIenpwcJk/QmNvUj6kRAdb/dp\nm+azlKObaQ46UxE+YAvE9sR7YjHt7khHk7BRCkCWWTcqFhSoDtPALJUMha/s\nDXD/vHb9DTG3PU02MXXRCS3tHO+GfDBvND5VdAH82+XWHwNY1YLJIUVlQpQt\nZ0gIDV3fvA3dLtuOQAhwRk5UH1xjriRXkHnqefeD2cuLE/Pl+aEDZ5i/xtDN\n0Bvo\r\n=tcAp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-beta.56_1533344755896_0.38181110663691764", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bc31b4dddea59f5f7659b94414509ddfc12c2e7e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-CnvitGJLrxFLRHGyXlsNfn1bK9Cp8PxoEjk/f5+Qe1C4LhlVhYOipuHf/LyWovKbcJZ56+8Jm4oVsJ/bxd7lCA==", "signatures": [{"sig": "MEUCIQCK0m3hpxh6X9LeP54szhcAxQ/1/XIAbVEgiHU5KF6K4QIgbLC/tliZvH6dv1bzRNNQ1YT+s2El92iA0Q997PsM4po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSkCRA9TVsSAnZWagAA8z4P/1liqyexEl9xsXmQm2JQ\n9dJ6Xq/9z2t8qfFCi9LeAOK7XCNVWaAkLqsEbFR8mBRiw06usRZs7anITfYa\n/q/qMwqHZfMLwwbP7vONKDhYg5wZxGCn4/HjiDob8KFl4R50XC6eTawqsrNA\nMxFbzIsy0D2F+NXGiKu2jhwhCVZDRUG469vXMj0LuSTx0wVeJ1LSj9Fyhz8W\n3rfjNggx02F67h61QvFH/8LzVOqOPDpY5i6Tfwt9dbQMXszWDAls0k46BQ9N\nwF/5XG0YKgxbnOdJu7ORTotNRKKDLbyJcXNW7+UaCGaDR5co49SVSx1WG6wt\nC4/TOJGChrhssr7D2ewjPcJxLs07V2CEZTV1G22t0NakDtX45ylqzM4XR16H\npINvbtdy8ULUh/ydKDfJmenR48ldWii/Kgb03xBLgpSlusAjxibY8qSb8B4C\niWYfDC0wc4hXzTwrqgC3n4k765EzfaCrAKpzF8Lgx7kPUWI7o3jlfVn7CGCX\nGgbHA6cXwCx8mNsdzA1iAK72yY5/UHJlMcri3668mvCJ5aVNYH4GUkaiWHB9\nh7oXgJ9277QQfI1UFrZNMKSX6GG914uRAT9qZRwTbYZTTFx6jUEka4VfMrTX\nL99echOu/hDCs5wZorGzdxZpq3asrWtNJwRd/qjQs0plG9sA1QU0jjYpd+KL\nDz2z\r\n=B7IB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-rc.0_1533830307731_0.42621465690401017", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "79650ceb99de242e94cfc806c898dfc8831fbcb8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-c1njTuUQiSR3AlHJ7o/aMq5FExkt6SR7YhV/KujS36OTdu/HuX7H4per7Y7EyRK2gqFiDgEl5PwlVxu6qGXzaQ==", "signatures": [{"sig": "MEUCIQD9aHRr+FF60sj8nYNjDAXDfuGnHNrl36LQ6EUduH/ooAIgHJfsG3VoUMjvscaKVWP9DSspzQH4vx469OmolII8qMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8qCRA9TVsSAnZWagAAZlcP/2SWJeViqC9yKwAxLiKe\nfFn1C/cciPiYPNa+2EUKJAwARUEBxpjX3MI5OyvJvsc4GWuQExKyKFZY8et5\nMKU5zIkgClJcYzE3pZt2CDMTy8UMzy02LRSoSxGMnUImdCJUav03gC45/Vo/\nkwwV2PkMAOfcPhdEg1RnlYLQL02SZ4Bv2R8lO1xDDyBxxpGGGPvX65nyo0k6\nCOqSwUPK0T+KClZBXc5uWJA3PZ0T7MrcYcwTIWo+2oP7cALKb3TKXRccYh/5\naMrFcBoIBICDW1eDjK6boGMkzP78njzwI50v85KVzsdgkUngIZw1Aw1uEM04\n+ggeoLzN3zACx82og7bq7q10Zklb6mKyMR7hvxvaZnsX+vYbVX+672Uia3ms\nRYu+VNm5zG6r6ib+2k2RUU8uNTsd+Vup0E5sgQ2J82uLQ3v7Igx4ajVUIvpn\nXed/2gDPGuxH+HOriw0MZNMiLxm7Q9uZmozqbgnPDJAgjBKjynqnIrx/T6N8\na2rBo+v273MHOKgcnCpexk7DtAbv9lUCjfKBuPHpDWuWzETyKNMA8RJ/8cSt\nFq7AKKdMkfqNzuzuZJXLUbvoDLt65b2aX4zSVl469KfIausuHEac12NqKlSI\ngpkY6a1Utzv5W0MSMtvT6LI92K1MBaLuCHWNkT+JVZNN2AbAVUeN+6/ReSXQ\n+r6K\r\n=hnH9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-rc.1_1533845290136_0.20885231900980905", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b381746c00bc4d54ba25d0640bc2040037d9c6a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-Eqx07oZnXu7FCvZfom8BfgCUsOklCzc1tjdqofYWp1+n6EnRro5H1aukaT/BdDteqfQUO8XqxSc4LoRdqX+HWg==", "signatures": [{"sig": "MEQCIFTp7Ew3ZceqWyqKWM+eRmzkneaDkB5zmeLMqEXf5qbMAiAI1nQqreIhqt/LHWAxLWXpXAt0oP8xnbT2aiFTiLMqXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbdCRA9TVsSAnZWagAAeS4P/Answ3EmzvzT5s77EPWZ\nAWSt7j5TM+cbqeED4y+56vwVcpJTb1zJa/NvQXPvzdSqc0hV1ScYyDjh8+7B\nr3cklDUKg7nOgkV0BsOrwkea69GqntquXI4eTGKEGWvhXNzG3gYarASNCqKk\n4sBBkBcQqJty1ADzSxRMqWnk+r0BwTK7Me1WC67uG9ys0xQ/6XRXOLlcsYSE\nMuhU4VCa5OCIwsFYa1kY+uUEoz4FJuHsv1er0loT3n/KqHPEJoj2rEEIs4ey\nolan9c6f3ww/q9pvaxsK5H9pDvjqCxXEHqlEMeZXKWKsu67Xi6d6a3jHdATb\naQr8VwfrOxWO8wsLF8JnmrXO8SU68DiMo+8cMMWUyU3DLJ48L3fvyCSogZ4c\nRgLP1fjd7qfUGGtfHAOIkeCnWG5LwGVjiNcse3tUKPjfEdDMEuA4L6koeKua\nOLBEhZIjj8SFcLQNfPov+H37URM2Spx5OvjC5WS2ocyBbYOG+dtN68OsKpQQ\nQD2cKIH6iLttKqTCN8Ry2Z6vohUKjUp5Yz4iPV3fwofL2zaUQaz8japoD0Pu\nVV3QJNejdvzIh4uAll2XWoLN0bY4pVWDzjhyUBQhst5oMtUEDXq6id+YcRjh\ngO8pRj6YXJrYAATsgA929oQcvXUBRpcYtvAOoriC0oxr4tOe+xUO8kMIlX9J\n3vbs\r\n=lY33\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-rc.2_1534879452596_0.564439852882153", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6ed9a8b010431e2bfe4c22588a40f181354c1195", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-4ujBN4BDfxZKfXGoe4xrU/u8y5qLxQqWacJEWF0Ds1OJt7yRzRsUavmCwa+v2D6WjmCodR0gRE9Jyx5du2OBng==", "signatures": [{"sig": "MEUCIQCWMiaI5ikzMYxBKcDfsew0OSXOePPvUVqO2JlXOoNneAIgKWqRWa4kwqWzigfYwbfyuhiQeAXjWe949E+Fj0H3ln8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmGCRA9TVsSAnZWagAAe1IP/Asti/2J5KQY7ugOQ3Bs\n68/5DaYsViVJgUHDVtUnR6TpykJIMZT0wDDs2iiTXJAqgAt2qpmcWVUw7hke\neLSuqrZo0WHriJ1HtoqyN+S8GEAqrgGVy30ksO6brNR2UJpy8967SKL+mIXE\nWc5+jWyxP9Z2vyioYk9mEc7WJ3Di83PDB/EAcOuAlenZLos+rKjRhQRelZF+\n0Fq+pJkBRp0kTOOnJXj0o/13tPPaPOBFivOeuAX6dNoMiCmBVpW2+deKydo6\nvj3vuWJ5WmQlt4IZQgTRxtPBG0HrnRFBnrSBeIBR2SQdPLOhZ1PYX7mGK2bG\nbk8EJZGZ1EaSjkFqjDqHY5u3MZ/rifD+2BGjdg6D5RJZCE4IcbjhkiF7/6cv\nfM5tiw80bm/OnS3J42t3kC/4Y4pHMA9HyR7e5FCInp5x/6+VmjZEBy/LOUv+\nF5lkMBLoc72e0VRS1O/+gQ4ZS+lTcBXChVG8FPdcC+xXnXPeVUbx4nE01J3T\nFO/IZCF//eWAopg2qvdq0Phtmisjp7f64zdikVeljGQaGdcsw9u+3XpK3v1F\nTjpxVV5j8YwgWsyY7X/qbUR7wk15kZNN/rxvY7yaFY9EyklhNEMdq6FWWHv/\nHzpvdLrcDqUt49AfXRdxXQ04KEsUkarEME/szjgKgnPPi9rP71U3Oo14bUS1\nIo0b\r\n=JEJy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-rc.3_1535134085750_0.40771154313819435", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6fe48c25e85959be502b7eeb8c5747e33c37d137", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-5sHlthe1ME1pnXvMKHVOqfWuXinW6xWb8BqBnbFqEbYCaak1GzmD/Ur2w4RR3pZW0e7Cwi/7JE9e1VnfiKrv2A==", "signatures": [{"sig": "MEUCIQCDhx+jv4ogkykq2LIeXmKjOh5/EuuXDNDZtB/h4tL4vwIgarNCCHbwXLQnWo5xD8fUK88JwTm1hzw8GapRD1PohAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpmCRA9TVsSAnZWagAAy+8P/2tAhtXaK8IZnHhwWuiC\nd7xFk6cpi4Bl8pHT+IzOTbILyVkHNTcrHz7ve5ULFH9kS8KHIVfw0DgwUvqY\nDmJoONZwrTiD8eRP/O3uArVb7NU8zLy4qa7MCvVuggTWkuRGsVeQd7FgfHsi\nK7c9pBG+b7BSBkgPOU1mRDPsduoktS6s6GpC2XIE0uUl7hE5wBhXKwLsUlOX\np4myVz6OOOqpx+BHpKpmEAACzJiE5brOrf4UzVY9YY9GjJ/KaOG8FYxhTT7K\nle5lkOXI7/jfffZc6GzveBxQCUvSAVgGQlie660byilOC6h/yRH0PEk7BXl6\nrlpgc9PV2GY5yznGRYustcKh++AqHbOQnByIamGpUiERTatIUfY31bk64AMY\noTA+aG1nLzeivT+wZXELtI8K+mH0ThN4hdilt3A+NjO3t2hwmPuX6DgkuDfE\n2CflPG1VmB+3oiPqAW2CVpOMBU5QqmmGJBanBuL/OdE1JSaeFmZdLobuhRW1\ncC+pdjKPohDHM4uwCVIpVsYg6DPh6MJti8OgAU+eGxM+cD69jiBN7tkftlNX\nnv7pZP8V+E9BgTpGDrA6+VFr3GuCjS2PRTot6rkGkoUvbh+sq6o1O4gvMpPu\nBHZ9QOk8Ux2yUoa/+29eWAhPJv5lbMgLQmDakyPb4/WBjEVcl0yRMSWXM2nc\nZlie\r\n=gHsY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0-rc.4_1535388261911_0.5006063387671853", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-property-literals", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0b95a91dbd1f0be5b5a99ed86571ef5b5ae77009", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-7HK6/jB4MLpwQUJQ3diaX0pbCRcoL9asJscQfU3D1HpDwYdrH6yAUKleUNFHFyGNYBI9UeJrS2Jpx2JhtPKu5g==", "signatures": [{"sig": "MEYCIQD+A1QI+nV07CAspzw/m+8AZz1iZ65diqOGiQTGzA4r2AIhAMy9KvoVHi0MAGgrYMIUEMj3Xf2k+/p0cIHBo8t0ZQ54", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHB3CRA9TVsSAnZWagAAI9gP+wUnX9Orm51B6gzMzaqB\nnN1JJeN7rEQDl7XwFcObldUZo4mDGn7hGbowCNgTCztL9G5ZDcKdiQwT4FtP\ngZSe8hQ/2vx9qYjXFgVldr9/92XoI7XwX7TRpluWuXX23HOPSe5zQJ0By/cs\nL2ZMQeDhitGJ5fYvf2rWTetVikl0wBkrq30/km8hhK2I0SwqRNb1VV/7ensQ\nTvGpOcNQHtKyh/f61j3eZj3mj7YhQg8UcuY62mFAP4TPRFxcRShlyAPEmvyd\nTfMKLlGR9TjWv5YdM5E0QGHqQhinH7gHhCycDWKAhjboqbJNQIWWW1mpc5+y\nkfRxpwD+bERe6HXkyNXMWsY9f0zt/pkA4GzpmnDzddITGAhsARtut6TvptUH\nQPpK3MLC7lB0HESnKGTm7K56+X8gr+sasXMXQm/GkGLdh18ZNvomsRJKXvrA\n6tKpT6NIwrtqArUUeUb/UoaIz7G4TXKFj1k9WZCXmba7h0kjvmlWQ1eVGnEq\nV+8gkcJ4ajgHr7dfWXakcNFp/YM/O4OLNzU6TbSv0iJK5y8AcIvXmaiJnmZ1\nOrTx72BVMMWOuhJk1rQXsfCW5I1fAjIJ/oVeQiEQTtHehkywR/LrZebbaFn3\nfbpWu/KZzHuPLfM49uX7jx/kmV9BxRw+x65xl8SNEzuA5tLj1FHmSEkL/pk4\nGcNQ\r\n=m1F7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.0.0_1535406198821_0.08692838128353375", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-property-literals", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "03e33f653f5b25c4eb572c98b9485055b389e905", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-9q7Dbk4RhgcLp8ebduOpCbtjh7C0itoLYHXd9ueASKAG/is5PQtMR5VJGka9NKqGhYEGn5ITahd4h9QeBMylWQ==", "signatures": [{"sig": "MEUCIDTL74A8HoGYu9QDXYxBmerzoOelOZFhw1aGmjLdg3g+AiEAihniA/ROEkL+m15us/sEiNDHsjFNlfsCpMq62ms4W90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2RCRA9TVsSAnZWagAAllQQAIbAuQQTmSKwDZjSVS1P\nJcSunGYF7xuhNqpmL9DsDLZOkeAUvCfFGRC2fSn2hYgGrXR7qeXkqpBfmZzq\nn6WWGcZpcIczK/WU+eQ75kEZ28x8QSY3lFpbHWltlzbHjTUpsPFb+KG8rH5C\n6TNZQ0P6eYpo7w5uIz1V5hEYvlYux9i2QXljQ49n+NN0OEbUxnaU64o5Pl7i\n9EP6sqqRhTUp2CNnnjL3sEBGUfPuDHJmEA9j4570oM9IlSThoyiunTdmL9e+\nQAJzeTxJ6Qu1eUzwsgpvMZXPpLrpocM6T5EPVTqJFUMPcTYjhbSjR8L2honV\nFuakcGiqHyJcBnhJTaFZDSCvdyFGlE3uxz3oa54/vmYSOmmSi8kDc1mS6NFM\n7KZwiBichIohlHm1pRwVl9j0nU9V8y6mhQBp5Pmv9+QTjVIWPilLvSOr+Yzh\nfFfeiqIoL4LnvGquw80o1+o+D1Tp592sDNEg1SsucFo4ze/GWe1nezmuUhYV\n9ufiBwhJE/naG9C/ulhTbhayy/dEPh2YABYvZ4C5d3ld9G3xZk3WT/kCrGW8\nzfuLD/M/2+/8V3KCFWqav0pMo9+Y2r2aEu+hGxsdK0jbg59/ktlJ7LesLUi/\nPlNV8z1Y6qShOd1xAzDBlxEZmS1gRZuFjhdb/8QLkg5ueOJsn2mhXkYH3oPA\n/66H\r\n=Xq+k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.2.0_1543863696841_0.81654083554213", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-property-literals", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2388d6505ef89b266103f450f9167e6bd73f98c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-MatJhlC4iHsIskWYyawl53KuHrt+kALSADLQQ/HkhTjX954fkxIEh4q5slL4oRAnsm/eDoZ4q0CIZpcqBuxhJQ==", "signatures": [{"sig": "MEUCIAE4zC5r6oJaQ07MxsXKeCCCG6+yjdpGqq25KKlelfyNAiEAy7xjnMgfJVBThL9TKQRT1KuGgw/BtYf9sRsSCDVrTtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G//CRA9TVsSAnZWagAAfbUQAI7ymEDU4eLewdTq0Kv+\nbBWDi2Vx3Wy/TjXJXr6jmdLJmlhKL9UlphcdHCIQSknFri3il3qXOvygAX1R\nlAG778AHhq5o9e5pqClR4r5bIv1FXSAZ/A5Uc8ps22irhgLE1rfdGlNZv6Hr\nEBN6GaCvadnBENF+0X2jz0knAvG+0/p56hWa7gN1jRb8JoMxHKGtdiIRbsyr\n8QoBM/9/r+oUkDY9SfYaTtOaVyy/F0Tz8USxM9l5yFKkhYwyMd5B0pXqwUQR\nDzPYCNnY/MDy9z2ic0cpXqBh7xDX5MHEWcHCvc4rl9CWy/vRnTYwt2zLaemN\nRCpWE95bjhpdwWP8S3992HKwHNIbc36WynTwmf+eiZMTTIgvSQXeHuSZCsjR\nh08TsxwHgvt4/EdMcca6DrHGLc483+TkJ+q8hSO9mrZ2FSHAi70Pk83V0KSp\nGTEyHuZB5TFVVIJpovkH2U+DS0HWtfX5zDdp/uU+K6j8gombWrstMMI/PK+7\nRIdf3WZZvhxMuEbJ5j9FVsRqBhNwax+Xy6m+SCVFafQ5ZeAsO0UsxSoXCZ3D\njnRNrCXB2W54KjXzafzn40U242VpvpY6NJKjYycspyCmWdGBMHFHk1D5p2kG\n23NB4SPFYqJ8pjPm2585/u5R7hqBdRH+1KmEd3bUADyyHP1mjYQ6HfZU30VL\nWmL4\r\n=CiZv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.7.4_1574465535301_0.5163045476955832", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-property-literals", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "af7538d916935ece100e72123fce109182c01ac3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-vj<PERSON>aQlojnZIahu5ofEW+hPJfDI5A6r2Sbi5C0RuCaAOFj7viDIR5kOR7ul3Fz5US8V1sVk5Zd2yuPaz7iBeysg==", "signatures": [{"sig": "MEYCIQChZFCISV/C2d/FjNmodKeDhN1BI3cs8sp2+ltgkTklpQIhANaY/950dy8AZS2fE+r4cqUqFnpnr9PpsFHkgU2xhq3G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVqCRA9TVsSAnZWagAAoeQP/0LdPCDXSMCQ9M2eQUHW\nqCmNGnXxwtjsdFUyi3doanZHsCtOt+2/YsTT9Gkaly0rLcmB4t70Zpk0DjKk\npaZTevWRDTJC4NBPA+CuFCDwR1MdKH+Oq3OVO9lKhX2TpOgEPEqszT0qdT/E\nEF95xOQMiD1gwyf2rzNZGltN21XG/QCLN1Rne+lrn7+joJ2pk/d8K/DlT0Xz\nO++f8H1EdHeeRd8+M8iyAiAnVlIml2WQc8vZcSDcW3CAiSEDgtuMLXHKZlQ4\nu773gbAfPKtkFWR5GEJQjxCcOgsoSMShhCEgGbpgmiHjO+swZ/a/ZAzsZJJ8\nldzKxi5pFLsZtX0VHj88KfXwXT0MmsVmTI0juk/uRauE6oC9MdtIeK01z6TL\n8vrXiMYDEqQAzfIS+r9Q9UgXw1VtS9Dxd3W2yEI0Wudm3Qx35S+vNTGCjQlU\nSQL3J3gC7FdY+DLlpGVssg5nsBoWd6DhQ4qTwGzy4IkCpLiEi1fDRTNx8Ot+\ne1d3b/EFJfMO1Xmd1MnodQOIXBMkD2keUC7SFjDeKRyjDZ5H3sD83yVCSI5C\nEITfXdSWbC6Y+g5qiz/GyBav9YGGJeG8sloRjTVG3jJaUTa8Q/yaNrrBm3FT\nRlkBVGVLmc67HnNc1FMSOVWPZk+tpS1sL9zBo2mGAk21aC9+gDsoaRcxOEhG\nPTsm\r\n=r+HH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.8.0_1578788194897_0.32081150363890565", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-property-literals", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "33194300d8539c1ed28c62ad5087ba3807b98263", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-uGiiXAZMqEoQhRWMK17VospMZh5sXWg+dlh2soffpkAl96KAm+WZuJfa6lcELotSRmooLqg0MWdH6UUq85nmmg==", "signatures": [{"sig": "MEQCIBE84NOloTRXnEca9EkM5cPce6kvznQtQA2oibR7UcbHAiAflBmnaw2JpMvuK119bmfOnXDY1eZdsCYTZhBEX2Pf3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQgCRA9TVsSAnZWagAA9GYP/i0Yta0AFOv2XU87G+Dl\nfP5MCnhWiZiBwEINzqhFUJPEIH6zKiDWm8TkgwW7somrg2EGFBWiRRCefmea\nvVKBDaGOyBATA6MBkjuDDQvB5aobkcb6iW5OF75lruWB3eYl26rpolti14BQ\nhdKkHUhPwaZjXAmwQX8PT8/Ig5MU67u17I8T3x4oO7uNiv36i5BFmG8Yvcj2\nYx4NDWKp/ArNjWWh0Z6ZkSuSLIg9l6YKPF4eiOM+drV6SFEGYxLhR5IhEalu\nIIEdxeNXoxsluRdm7cBkg0mm6p92iIX/pYJ5AWtHWgRFdivuGj5mbn6QJkaD\nkJ5ksOYfkYW0NQuVBVdmHDvAgWnwz6nXiRZ/YFeDWKz+kBfkDs0raOqDhxNG\nhrO1QC+nrWBlFpeHdxK5b7oYGWTtajLSt2FHBGmnT3RutU3k/efdByOwGja7\nk4rUZ9nwmkz14uQIQXPPeeeonoZ1/kCc3HAK0Xk138+mniJmLB7rlS1uee+9\nJrNGuOuswyUFjzatkO58hdHKrn2Jyvbtoh/NRNuEl1KXyTwxfcjJaojuoK4m\nZQ/u1ngfDmDJ8pP/R0wCSkaF8hBDl8FNVbR0vCtmPNGyyVFT8F35etwhbehg\njf0T1vDWZ1+p/ugzTYhYhYRNmhvhabbc5BBQp0Evd9Jh9R3CivN++nRF/uXG\ncSaW\r\n=jAwO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-property-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.8.3_1578951711641_0.06362137136427437", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-property-literals", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "cffc7315219230ed81dc53e4625bf86815b6050d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-Kr6+mgag8auNrgEpbfIWzdXYOvqDHZOF0+Bx2xh4H2EDNwcbRb9lY6nkZg8oSjsX+DH9Ebxm9hOqtKW+gRDeNA==", "signatures": [{"sig": "MEQCIDGC2z10QsPYGUGopeHtyOm+aFEZYRtFBlrYQ008LlWPAiADifkOu30d1pCwmS8NyXeZ94MJpgKJCcQtiSV2SrG9Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSbCRA9TVsSAnZWagAA7G0P/ivWuoPKz3Sk04y9sTqx\nPJPuDxCvhsbcxhuupElPgBIEKf4r9e5Hs0iAa8C06njv3+PpyyjGdvQ9iAH5\nSq+8lhCMNdraoguwQ89gchomZdqzEalHkidBQ876l2AVvei1qmDJonc97Vc2\nxPo+nzuUZdmbXs3NMUr9OYzkTO6erKTCkhXISD1H5y/u5xkWJpnLMVY+wnRH\nnrtdafp/Pkm12x6DtGv4yj1ztHcHSmf/oXmVT+K+POlB90D57mfm2aLfboFj\nnOFsV5I54wvaCkqKZPvH9TBM7SEVsIYpyvwtdw/4Snt3BrMhkPNhrB+Dnpi0\ndJTAAdAfq7UD5JgPPCA9DpnYey08a/QcgBtu0krFjrk8wQC/38YS4w9ALihW\nxVJYAb6TCjAq8IfIawiwNft4iY9Mkj2iwqG8zJy1DDzG5h5Jux7+LzGxlEUp\n/pBXVnAkuvEkW13AtJ3xCiv4y4t9pBGwKkwmqHBscgfjE/Yb3SeE+7BSZLun\ng+L37v5iPV9qfMThXuvJq8kESGDmhrSv0Nj9gKadEr+GKa23Z/ywqU+yPZi0\nQ7E1/UoWrdWuu1bhv5juvYppAuPP+RNEXu/OANAU1hD6LcSZKL6TPhYrHxgd\n2Tan853h6JWPAlxkilS9l1vSYqhSRq4CoLy0eMYbsbhJdJbKBVmc18KH7Xmc\n8tbf\r\n=oDkA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.10.1_1590617243153_0.5369497450333327", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-property-literals", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f6fe54b6590352298785b83edd815d214c42e3c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-ofsAcKiUxQ8TY4sScgsGeR2vJIsfrzqvFb9GvJ5UdXDzl+MyYCaBj/FGzXuv7qE0aJcjWMILny1epqelnFlz8g==", "signatures": [{"sig": "MEUCICvoTcVQJGuLTZofD48+nPjc8UrhyMNiVLdaCJprJ5r4AiEAvrTpnpXBVI84uB5iIrDe8sw64imefxpHGo58nrM1P08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zomCRA9TVsSAnZWagAAhXcP/35nI/Cyw5JL6xVcQWZO\nPo0JD3HCH3UzzXntLUVLY3FmBftT6EyHliRjRHUOeJVmpyuWFq01CtsdSKUv\nDXkUcMe+I1AiVI43N7hdIAcNoIpwbUaDrfWc4wgltOru39Qehd5ESaGGXa7W\nvkBTICIBk1loCZDFundxIzh84IQPzYl5kvuRCItCmsiHmdopOrhcXecX3SyP\nK5h6ESSDGXDVPP/2oTy/mKctQ3THRMAXXKqF270eJBB6KFIrHREdYzvQhEov\n6Ib/oRhKXF91OV0/rx5wQQDCqhNz+YjjGYz2kpC+fEhGGGb2XoQYZPwGIEZD\nKBvVFVQyo5VqBud36gjJBMjSLr0E59HHBtU9SyAzPF/ZQ1CH+dtRqV2juGEe\n7Ws7JnEuyeV0+z9rm3jehW8Qbb29taqHRWIBMtZO1ixtR9Ck3yNM5EtPkiup\nsB0Mao9ByYf5qcjNhOkD04YaSA1kqlhj8oPU6qoiE1VnA3IAYHvZrZopJMAz\nPP6vu2LlVV+SJxlrSyif0hGMaTE/5Vw2OBLJKJ27aKgzwNGPVGTpJuqQbarc\nYQXsli1l49L7a//4FOUbpuqGGWoL83oixGdlcdpMXHzc3iYIjcKh0RHmCZEz\nBn6871vnJGG/gx0RLopi6jfGrW5rc7M8EjWQuabnK0cq9gXY8ugwOTfcR5cZ\nHtOL\r\n=iURj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.10.4_1593522726545_0.5820977472074937", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-property-literals", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "41bc81200d730abb4456ab8b3fbd5537b59adecd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-6MTCR/mZ1MQS+AwZLplX4cEySjCpnIF26ToWo942nqn8hXSm7McaHQNeGx/pt7suI1TWOWMfa/NgBhiqSnX0cQ==", "signatures": [{"sig": "MEQCID1GRUYOb2SA0CrYH45KoZucdURC831SfweHbOPw3zQ9AiASQUC4fi7boOLRGxLAK62s5AGLSLBhAO/9um16BjXovg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/QCRA9TVsSAnZWagAAz9IP/0WrJXnked0NfyVuFj3n\nk7z/rU+8cUGPoyaDx2Gla2tuQCJ0eBwxKux8zvwwb2zexODYLcsPn9Crsvz/\nLWKMnN8nMhvyLLw+GY5zr9sNhfnUikUxLtawAKZKNFFzm3PePDRkPp+oVJ3O\nM5/o7k4MrgHn280lYYLqzMca7sW/IkGmDCMw+vTlk2AfOJRgZrkiWc7V/Lr2\nr2+IEp+05FEOmw6kjy6DV+rrfDd1mdwQEjMvoRYMhU1cCytmzONmThR/xbta\niIjopFzhttmGPb/ufyHw8/GTYgYFf2W+kPDNcclCFS3dJtadSd6AkNRoFD6v\nsnMoLE5Fldkno/mvarciV+edzy1tJYkmMJgolN49IFCUyWxrz1o5RGX3RSrn\nyE8D4L8uIgDNniLIRcxmOVUjs15QAXfqTvluwVNrte0JgA3XKuQUtI3hplkV\nxPD7Z402BGpYDnI82O66H5jETi/MvmnVhiJPpqig1yy9trl82cgD6DZt/6Yx\n0tlgTVldYTFZtMAs00J92GcQFgiVtBnYvHU3XnlsyQRr+1aT8tpyFeHKkhhD\n+2taAhLdS95QcsMcLNgkwJqOA9vmPY7jLSbKotXaXGApjYA20jF73vg8CkHk\n0QBk4QGYdh0+6UYMnT/B2OqYXF2fa4ryeNvK+Yx/urm3mZlj4sVjq8CbVq23\nRyil\r\n=rt64\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.12.1_1602801616477_0.15870189999011264", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-property-literals", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "4e6a9e37864d8f1b3bc0e2dce7bf8857db8b1a81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-nqVigwVan+lR+g8Fj8Exl0UQX2kymtjcWfMOYM1vTYEKujeyv2SkMgazf2qNcK7l4SDiKyTA/nHCPqL4e2zo1A==", "signatures": [{"sig": "MEYCIQDaL3s1uZad4DyJ2eYlXLxbi8nh9atXhWc8AjXazq09LAIhAITFNHohmEtEBIoyEsYt4eoMy2GRTKNAXIvyOnIDFR2t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgkCRA9TVsSAnZWagAAV/sP/jLQVqVXAmZQfo1meIQw\n+IGEqeqan5+m7N/UwV47wf5ryIf+FR9aXe77g69mR/xlQ7ZEpKwE/WnpyHoo\nwHOg2tfGnAa/Lhv67rSTV+uhE1CFleFLS8vir7xEeISK9f3uM7mHZHPKE9eR\n9b6lHmRknhBJxS2lTkZZe1EL1N/Ga5DPIXEYoIAlscWf54L7IJW+YbrJNwSL\nYYLelt5dYvjwKwrg7AVxNiyZ9FSHP1fiUtlcqENzhZHpSXpp3GvTrVrY53MI\ngHCvxGGMAjp2zu7NYdKWou5IbOgLEkXB1QoO7ekQ8QX0QEt/45mcUe7L5X6m\nEKzJVXslkTaifp1RAWJMNPI2PykE9JUwkd5SzAcKdgVi6bE8gX+d0AL0mDXY\nz7X8o13B2U4jUJCVNfU7po0ROz2X87q54ukV1LIaej4cVAsJv7477c5NfSPa\nCE0CoksEAwvwK5eOkLyv+KRSpx+0kqagk5553JbKmyTjlEOfqbEBL3xSbP6a\nhb6MViYGlTJPdIt1gfj6eP/+9W5Habn7KETfSVJ9aL0C3VKsiGfMAPyqXrrb\nCnZVqQ9336FWVh7eZq0S4bRWw4C1x4/1NwZ5sC3F419zhXj5KV3NOURFO+AP\n+xQBWE6noTQftnqNLkJtX2MZwkW7eYeQu0z+zN9/iIiDKjL0hFaKCe7gUdke\nNj1c\r\n=3sp8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.12.13_1612314659768_0.3193143887735761", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-property-literals", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "0ddbaa1f83db3606f1cdf4846fa1dfb473458b34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-r1uilDthkgXW8Z1vJz2dKYLV1tuw2xsbrp3MrZmD99Wh9vsfKoob+JTgri5VUb/JqyKRXotlOtwgu4stIYCmnw==", "signatures": [{"sig": "MEUCIHqOC8FsWnZfIUhyrCgvZBPii8VKk4tGLJ8XzhluFGu/AiEA8mAyJKMHKZEth93q0Na21HVle3Si0bH3n0VEKU6vBNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrHCRA9TVsSAnZWagAAdgoQAJ8X/NICSt1Qg+XvuVUb\nBJ2VWjVlwT9Hr/tuUzIJCvidUSNX0GtiNpoxLQ35yDitSxm8kCyX25DIl1pY\n4lPMVIMQmGXDpk7vBO//3+bJLDY1Rz6+AXQXsIDEPgcjkwhEs6jSWzzE5kNp\njtSi5N6r3wHV+riBbyNjFtbmnEBrLxGGdhLIqLwkycHQSoXrzKnhnO2SUZZH\nYJEd9MBNpUy2dn9pEFLVPwo4ZWVakQY0TYEGKpS8TzHGGUfhki3fAd/aVSJz\nfKil4Ck9Kur/JD0xGP5Ae/JLJce3fkPEIMxCQ//QoHWir+LUGUN7YSNZZDV4\nRgNcuFE2j4ZHRE5fhUUc0rZR6XXR6PufVp+WjeW2J0cHNk6YBffXPZgPvpWL\n/cP+vOop9EzCybap6x/WFbP2tOxGex4ia6JtvbKBlEpQ56ljEcnqXOHYMSb7\nGAUe6CPEL0o4sPjNComCcOQq+rypV3CM5gbOTj+C3TIJX4FWYm5iRxJX9jtL\nYX9ZNv4gCzqIu6V6tAHyNPYZMESdatErRnMqtX+rdLiTdiJh6ia3wr5s5ILn\nmuaCN1u3yUDhZj4+E0SrNTYboKd22IeIqWYAOflFouYhNxEh4Rjw9KiFBq4D\n5A/a9o8sqzedSiwrKxxwY8ijPaeUgnnHBTVgMiUJtv85WCcZyUrgVNmjobYT\neTdZ\r\n=trIq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.14.5_1623280327144_0.3496133903934828", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-property-literals", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "a95c552189a96a00059f6776dc4e00e3690c78d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-XLldD4V8+pOqX2hwfWhgwXzGdnDOThxaNTgqagOcpBgIxbUvpgU2FMvo5E1RyHbk756WYgdbS0T8y0Cj9FKkWQ==", "signatures": [{"sig": "MEYCIQDy+IIPc9jpRjlhZYqWHSCabxiww2WT9BfbLACo+s80SwIhALycpV3E51ryy2V1D17LtSKTWwNqhJ1IN9wAICX6eKUC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3134}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.16.0_1635551252120_0.003910333705036573", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-property-literals", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "58f1465a7202a2bb2e6b003905212dd7a79abe3f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-+IRcVW71VdF9pEH/2R/Apab4a19LVvdVsr/gEeotH00vSDVlKD+XgfSIw+cgGWsjDB/ziqGv/pGoQZBIiQVXHg==", "signatures": [{"sig": "MEUCIGc3v0S11Rs++cW3wcDzYmAXgIJPOjhgOJ6snepdM3ozAiEAoNlSx/otQ6kFb4sDY81eEuBgKvIjuL6MxC5+48HVuyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kFCRA9TVsSAnZWagAACJUQAIEWZSlx0yJ5NBVQeyXo\np+uWT7Ovgz7ZIxocPe6vdYkJmyQxWHCO8i6cmzroUVWTkxfDlYYP8q2B7uT5\nGJBKG1mgLzssWOooFNnIXMG6OA4jtcpzZKTl/DP5EbemzvLPT/sbb6/D14s6\nIps2U/XbaZlO7g/U5NkUY21xtDAjRrmbj6llpvFon6UOFSvdpMSVeCc5Fo0p\n5aJxWw30vJECP9X+doB8z0oP3pHkTop5Fp6KvzP0ySlkf2k6j4sfgLvCD7jA\nWRBWZXqh61DrA+2v7SHTqxdNTqhjFjoGum9dnNfNGkjE6gOQHJzE62O8qfrC\nAj6E51wfidK+M+1DOB+Y0A6nfFrhz4iFaDefryD5iuT1h/3yjtegi1YyeU/W\nbXzkz4bYzp0scfyX8ZAX7SQHYYAmJUTBU4nu67x2ReJVSECY7Ds83jyPtIXE\niYOBZ5KxZLlIx+4BCo1G2PCYtP2SyqBBq2sTU2GNrm2sruGNwVvwGYstAip+\n+DbY56vXA6J1Pmt99PTtQ12mk9LbP6mEVWxlXwgkGEvEfLSpzNCTI8HZWXsp\nnv89wH2ozOoNriuKmTMkr1jK0R+pBCNnfgILyeW2R0iO355o+NDBXcsmN+lS\nxoVbTidH1SXEkIJdzA+YSR8bylXh7kCcshoP8hBx2jP0zAFxMp9ZFdu/QZB5\nBbCM\r\n=0DSd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.16.5_1639434501275_0.24639608509967226", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-property-literals", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "2dadac85155436f22c696c4827730e0fe1057a55", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-z4FGr9NMGdoIl1RqavCqGG+ZuYjfZ/hkCIeuH6Do7tXmSm0ls11nYVSJqFEUOSJbDab5wC6lRE/w6YjVcr6Hqw==", "signatures": [{"sig": "MEYCIQCt6n6laNVp23FmVN41eF0vbhgFZAJYCNpjP+x/CtRZfwIhAJtI+tiSNuR+F1XZBAQEbOPuv/7H0QVOyX1BEdVwEfMm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0tCRA9TVsSAnZWagAAlOMQAItNIUj/JhLuqX1zHw5Z\nIjG2iKk2gkXDSX6cPNKwGdWhBwz2ww+KplfFR9GXAsWnXJ5B/J7Jpxq504N3\n1Rs5H4FbPNDkrWYUQvQ+yUYKKbprPlKOl37QWCctYbptZB7Aff+IZciRpEz9\nyKzV+EkJ6eSLN9D5pXmzgAz0wXjScd5ldAeSWmBlT0IsQ8w6Rk1nJW/vfuYI\nSRfm8tq88k4TpihjBkGk7qF/O+6x9upl7yDdSqKrpYBDA3Z9fF0/9vumhKqs\n9wGJbDntFpdYqo1pb7wC47Bw7/BJOkDL/gLour1iZ69/YvRJ1pnMSoxegItS\n93MfPKH0W+7bdWDNP98ObyUvXNWMWG3ZNkDf8vcPoLJnP5619jGQoclb/H12\nrdaLBgGeFDCUgYVy0fIA3DxDZEl1j8d205a74jFJGNOvSdgk/mVHn8/MoP76\nPLuSxaUENyOG/2FmhToypNYRhRrOuyijgUDVSfhTUL1GKDveRyKq77RyGSIh\nCyBgFjU8PH8/2VtN0uQb9yYPF+tWejqicgw6jVJIM/a3eEF82j9FY2z+BnUL\nQx3HfJ1j6+7/AGfcR5H/23DScDigMb7Q2OozDfTWs9Cy4OAxKibd0FEgf2lG\n+l2vcyfvJVKgb5Uvp4XOjx1S92AXdE4FaxnnkSPzwwT7/qCXxtoZQsIki5iw\nG6XN\r\n=hv2g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.16.7_1640910125198_0.0681740138613891", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-property-literals", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "e22498903a483448e94e032e9bbb9c5ccbfc93a3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==", "signatures": [{"sig": "MEYCIQDjtmvJRsupdvx4RK3cxe6v52Sc4PYp8q6QqZBChcinQwIhAM8eMVA171PwIYjbYYi+bljXT/uWBH+XJNvtnZ8eOmSw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpjgg/+KdPIOewUrBnFByXGZqyosfPZq38ZiqVS5BURAKhnvMP+ycWs\r\n1gj7xRYVeW5NKnut4mVSsTEjYfxzjD9iNmQuMOSrGE3gVj3FBiGLCW9xo81s\r\n0kHbXLgArt5PYtGzJoKB0C3hIPqFj6BOErQnDU+Zf/kLd57ghBcNjxP8Hkej\r\nmAl8SIFFeF2KplhVS+FZjYbfLDWV8inHWbtmYkbf9c1a1ubpDoGB9GAvfmzX\r\nT3/vs8iEiNCkzyAFuX2idytNQzy0JVyo+Ijtj3uG/DMKYiThK5eSwKTRi9jJ\r\n+2WcQMtDKvJnRCyF06yimIsV4DRxlZp/H0QjXGKdcJXUJeiAyZ0ImUvpsP4v\r\nFCZmFVOifVgLOAfPVbhliVOqegmBgYGc8/cMlOuG3+0FkkViGWSvSemercA6\r\nFdcmxP0HfXSWOCk2kPdhdp1MZZ/6j1MA9OrCt/gR6OtnHTrv3YXj4sEndcw2\r\novkwPCzEEVcGQE/oXdr3I6hdUMD6Nxgg4+XIvqMH1lQe5jxLU5iqIdm1qK91\r\n/D8ndM8ZHkz0LKDIEhic4P9/v8hENtc5oNt6O/bbckeWn5CXjR5nyY+5j6SI\r\nlzMHKOGGef8rCuNuZMWYQH1+sjl19QMsbn9twesTHSOiY5H/cfkxcTnl48rD\r\n+uVNEaPoN9A9u8sp11JuW4dAZvTMNNSQytg=\r\n=MZPC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.18.6_1656359401676_0.4805777287900763", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-property-literals", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "2f0dadd7ea474f67af4e512dd186714669cf8e6a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-9ZLNcu88XZE6wFgQzZnknzK47YEBnF54carUiWMYWg2kzIkgbkYZpfiZW7VaJoC5K242gW0PjBc8lq5PDCZsDQ==", "signatures": [{"sig": "MEQCIH3k40XczTMCRoKyh3ZLu2/412+oIHgNw3IbiRGA7Ki0AiAb/AqRPQsMlKlTbv3srqeOF7dOvhVB4M7BA0wW9/WPxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwww/+KZX0PnDVIQzG8THYzIt6Gr9sYN/P0LNjdgxLt0JO1gqfeMDq\r\nMEQePWuxHjziuMo3j+p+yIRqjrcNbgMwJe4wSwW9A7gTND05aFnnCXHeMqPQ\r\nCHhepHPFCsYhEI5Vc3z5HckUUPQ3Z2FDOGuCuPTd4h/0i8MifYWAJkmry/MM\r\nCYhiD+/p1owNHEmTxjbFzAriPYTF4yuo0+jbCA8UdVHJFKuAprL+f++oQIjK\r\nevkQPNF3+GoNkLM7a7YROgOhau9rPzswJqvgkaJu4O2xBFA1ATfte3XeEhhJ\r\nQf1jdoLuQ/cFM5mx/MUfR23H5gDlj0xn6ewE7asUoUR/WtjhCtdjb4q6Ny+u\r\ngrfJtvUb07hytx7uzCC/KuxQ1PN+VmvAsouHx3ehPIDIPQe19ATHMB8mKxCZ\r\nQiHGjrE/WM3o3tWu+PbeZ4bN98ltUrcPFI19VFZZWg4BBUtlTvFchb9w3wWM\r\n4wSageiIS17dCXwWwVve3F156h6g4jnTZfnaUnSwrW+iGvQZQaNFIlFVXDXo\r\nTt4LU7Vyxd9oKxrwMpFHfUuKbMkbwwEFpUI+pMWHfk2bCh5JLTiK6MwF2kzZ\r\nza++YsuKHP6Apl27wq0V4aIjFz7g90hn+x7goFZaV16nQnq+pJRZiIQCfURd\r\neH49Hqz7QkZnCJcCDhiTHeJ3yasN4Xsadyg=\r\n=9p9z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.21.4-esm_1680617366267_0.0038050391288362295", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-property-literals", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "4d0db18c58f4038dd24cefdf314a706249cae8db", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-5KQdzkEoF2Rl4of4bGbrlKndEVsYzIAYuV0vrxmXstL9hH5KBbVrXLAbkqR9mD7E5+hPxIdEJcsBvTCGaiHinw==", "signatures": [{"sig": "MEQCIEpRTPwkjgaZxi6BRyh5eoj9VIrp2zeHMzJtOyeN6yjxAiAvtRRtJwXrbtv3WoJSqxZy7XI+25cictMswS3YyEskgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3mw/8CYxG5F/QB8nErU8wZgEMCycBj70tc/ablTLcxHNV7kC6noVv\r\nYTRIfnTaR+3Liji0paD1EgZ96sFOwvdGKlSutX4LJP7kvyPHCPtVif6d2kqu\r\nS7rpJr4qIl//MVxLxnAIr/OqybhBOJgR61cjPb7JdcMF6BW4D24zbkzl0+Uq\r\ndY0COIwTMntAc6QDTlJCtpVCyHFCcFGJlufoJqURiDY3xfTIlXCfi0GyKV8c\r\nGj77Ceh9REyYGvcoMz5G3EioGu+wPNKF1DtGH0erRcvusjUIYJxQMfeLrlp+\r\n1RUXmsqmBsd//C80IpgVKH6SJJcAMpmfoKEG/mqezunMcJ2u+X+JAF3I1yHj\r\nVw7qwnm7MSz86TZvM7EDzNu/gmA8K7Cxr6MM44lr16JWDllgqoWb2fGVYAXp\r\nnuQOaLJfPRuN741UOhmMxo1D/bEEgY1g+Jp+TDn3E3guA42YjEXs93iiF0jv\r\nZPXB7u+gg44G1fi+x64reQGbYRBwK9uIv5yMI9bDn9fBOwzrfdeAQNpuTEy8\r\n9IIy0h29AuOql2VNBDqU8AIQiA9DoxleMQt64OgA9dRyQXm2bbnTt4exLOST\r\njmHNViS0J7PcVguicAnQ9FyygpHEL62bMzTlHwQM7tilBQWX0piQpT9fn/Rv\r\neHn0KedcolCrZyhcNpNP5mWuRnETP7lnG3s=\r\n=pna3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.21.4-esm.1_1680618076830_0.23054816223055208", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-property-literals", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "bd2a7bc01da9f296ad51678ec1268db3bbe4d04f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-Ev+esG0MvKfL/q0ly6L4g2qub2JdqannxB08HkdqW1RWjuKK4i4bN/uWrK2JORDp9WEvPKu85lBGuGZ8MONUKw==", "signatures": [{"sig": "MEMCH1eik8aNnvEE+OMjNuTWqQdaMaGuzPFQb8+I6OGVruoCIG7x0YYj/+ynA2kzNzkzt+d0WgzPVmzhxao8avgJrtpU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZdQ//XUnLX+1SG2e023t9DSLv04ZgpoXmnt/9zwWGwiqDC95NyA7X\r\nHkr8Izf/dtfQAiqVORlhXjumnmaL80w4LQ9pR5iMeCsklyPt8ZLL2Oipf/bg\r\nOWGXtlWLkqnsOCD15RY3oNZxNQKhbIhKBkf+7hmXZ78TnEv2RyN3KCq1E4gU\r\n4z5q7za5ok6icNnXwDyAcfvuRNXvatp/K4/0FOEmjjgGhm5cKL8jELUVoE+g\r\n+bpLvF+vA3L1+jL3Vw48cOzKk7gwKtKP8h1E6SllYNQ+n1O9piWnzLpv1FWI\r\nje5VSvOLC5xAKLMNYjByUrYYBfklTOLll8RacPhS9jw30mauDgcDBYXKDG0T\r\nokck+rQXzhKIMeotK8lmfepoRtNltdY/bc2TGDuaKM2CRpYAQczOHypOEUxb\r\nSqnvMemPrEGXbT0jDkprAvWr3VnF2xkNooBgfZcEdYuBZvAars/fgufFz0jo\r\nmdp8H9qNqIkiWBKK1ljoK9zt+izT2TWHM8/Q3C/77V4YIGSB74yqsrFt7Ds9\r\nrVtosMUb7fsfKaB6cQ4mY2rpgQgSnf2EXZwJmUPuf42qaih3fi3GOzVQTsR9\r\niBlDQVz8jsANJXBcCmjBraiRKZAIvf5q+WKXdDL/97eyExta963HnlK3WVHp\r\nKICEns6pw3ukIr4vl+XT2feMqQqVPsjehJg=\r\n=W9Dr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.21.4-esm.2_1680619161481_0.7839949694101758", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-property-literals", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "f275d238d3657088f5195eea6587da4f728679dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-dPNjxdZw8Z4yoBycwTKEI690Hj9Vw3XwWouGqqAuUSlsupylLupzPgQlzDcderRJgPUxGSJB1YVlhLXAZNLhVg==", "signatures": [{"sig": "MEQCIBRG5mq9ToUthDtdOaLmkX5hnqlGRL8vame56YQBnN+oAiAdifactoKdxzSf0PTGbPz8qD1l0vGKvP3GIz53Oh9s4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4742, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpGA/+OPP/ZUa1TteyCyH/P5kPd8eK9/yzw5Uix5vOW3SqbiLShGFn\r\n1fUxOtY559Gsq1lSs04ivOqrZX0Wf1bLwpOQ3KxNX0edUBJGZOzZcWrxaWjP\r\nAYRCW64OdmaLIFtovBGLSSKndS+vIIWFWPHzyhzk7fApusn+KG0qHGrVvATV\r\nnRawv/gkCiRGTtTMdWixFIJnkzOjzRrdr2LS4lbg14pwxEfVm7peX4ImBaqE\r\nofsDpdSXAzcjKHffGDuXgZsu2LYfj5Dlw2y1e/3/W17rlZn9OZnwLLbREFly\r\nvxIyi+HzNOMaQ/S91vSZ+xOq78SVGkL3aDIJlJa29m4PUjaUyjJd4Ayqts0t\r\nEUZZPhkeRJUx18/RJgNN7u8tSLvuV8/Ja0G8rQ8T7f+Opi51qHBMuwNPmB39\r\npBrVjd3MKfAkqxqhHz0sv78/N8nsew7CtLtHoDVuuj4YjXId3kqms9pGrP0G\r\nBS3k+SClAJfcggABWJjSDfVD92tTP792+5L1+vE1CDz4hhPEkXi3cPLtWid3\r\ndqDVljvuTCiehfdUJXAvlnPXidX3n6VlzUeHClmGJ8pr+URxz++YE109xiFv\r\nmgNMYnPkGz3G9wAfPES4Nrn7rI0/VUtHm/JrOSoCpikOz/IUQCOQlTwtP5SS\r\nxcEFkM5vr2d2+CBDGXlBjfzwWPl58lI2Zwg=\r\n=xgDj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.21.4-esm.3_1680620169007_0.5533120104933027", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-property-literals", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "2beee1bea1e03cfbabc5ae4c81e184af571f5e40", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-PMnD+y6Ej7dJR9iq+ydr5FnpL3JXCwPnHUnGSk3mrWFNDaCKbSrVAOUwVxs6+XALiIoRoKuBCru9uRJb8Y9DjQ==", "signatures": [{"sig": "MEUCIC1rXcAe8VyLwVWaRLNThw0443C4C/gcJxmf8DmoswA3AiEAx2lkMkRAIBLnv6xzcbroQnMKpomXVFRZlvzqDzqt+0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIZg//ak8QJiDuB1oCFZvZeU+g+UxA0DKe96DSZt5o9w2118eWJhhD\r\nFP8rHKE7UBjcLOPF1UglpxKdGOsAtlQhWXNZyT9rtv/4SBpNhljpGibRfZ7y\r\nARV5FVoM6V/8uElRgGfQlk0cs2WNT8u9j9y8D8V1bog9fGHphYZ05y3cJm4x\r\nsQsuiYKYgYJE3RGew+X6cYEoiAcvJfTdNzqiymwOq7jwkQSnwEECTl1tKgs0\r\neZ8kB0Jv+fCsjET+K77PjA2UHhDzmS864D8+8y+seTW6Ks1RtZX8EuK3gwTD\r\n91JM6pz7nZYn8F7+v44jjFwF2CCgHrT7qoyU+LVDTzC9kWLNrTDm1uikEcZO\r\n45LNVRA+0G5R1xagf2wj9dmUqwZEE2cLm87+z4FOLesCIH4/QQ9wMhyLriTw\r\nMSoC2aT4nbcxfkush3fyqjgomEWm5nEwqEFEBROikS96NKkDv0RjGz0qeDHB\r\nNds8v627MSD51A/ecRWSOqJRhvWl59aTcAd/cDMm1Jg/zrqYAqd1tag8QZYc\r\nTuJuTaxGKvgga+eseQIrZukRHj7m7FHwhz+3EzDOT4Up1hdGHpSD3BizxEdr\r\nKrmZssvHK23+F26dUevoFqgyuNTUBGz7WngeBNeh/LvLWLY1DyGIddod43Qp\r\nrZHfhX+RR4LqglDiyfHb3KgZL94oD7btflM=\r\n=qQe4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.21.4-esm.4_1680621201241_0.6404620659462417", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-property-literals", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "b5ddabd73a4f7f26cd0e20f5db48290b88732766", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-TiOArgddK3mK/x1Qwf5hay2pxI6wCZnvQqrFSqbtg1GLl2JcNMitVH/YnqjP+M31pLUeTfzY1HAXFDnUBV30rQ==", "signatures": [{"sig": "MEYCIQCi0XOb0lfyPM9FolD5PQTa8dUEYqt74Xhm9nAHfUj5XgIhAJYUI4ibEiHxPmqKXuuwlXmIyxMEGulMlyg2aXL0KMVn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4706}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.22.5_1686248479590_0.4105966899174478", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "a39f90374a9149a8663404ec256350a2626807d2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-aelUyRGQEwWCVdsiouV/FI1+wXde0dpmrt9G13tI0/+SlUkQUYeXEVGtA1jP9EUKIpuhaSiPTvwFz7GNx90bWg==", "signatures": [{"sig": "MEQCIBK+fBX1fMin14D16xvk8i3SmdNvCJkC4C8X9bCeKdASAiAO9rpf4ey6vP5QEbo5elE+2PIaYPhMxwuE6BatVd8Isw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.0_1689861594622_0.585970841212238", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "bc2eeb39f5e685c8729d7b50e8bda8ffe9612738", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-N/XrDO2utdGki2n25KJ+PXPANSKOZhA8Vmjv31n6Phc1MSFX7pyX/GhZYvH7R8W7pM4kT+8gsQqQTOafoT0hjQ==", "signatures": [{"sig": "MEQCIEMFjnq9spQxQgZLiXfYgsLp59fAOp15e9wQ+9kxYIRoAiAW65KkOP2mPwafEsVpCBxc1seZyfC0zX9XmcMCQ0Ni+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.1_1690221114965_0.29768485735505834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "394cc5c1e1acf36f500ece433d9d31a58fe1d434", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-1lMyX4Q08kA2yv45Qoo+lT+hnTLChQTWthqVi6liuASQOOoyHhb6eFTufISDINUrcAeJXPpaNTvMC33bcdHtRw==", "signatures": [{"sig": "MEUCICj9q3U6M6yy7mkNxu1eLEACYCKfkXwr6o16iBbM1rkcAiEAydXcDObu+9S7rFMTRvG0X5yv+7OM+8kZZUknZfMG+tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.2_1691594094996_0.23812206751018739", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "f88487fd563fb34ea915f256b05614d21c839413", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-/0EbJrieSJQSb32tVGiywONuoHOSob27SIFyfF97ORlHZNJJVzSJ0hbwNarHc253Xy0hJLLRHXIO3hXmS3sTPg==", "signatures": [{"sig": "MEQCIC9eFvokWHkDmPlmo1UGYiIXeZSKLztHeuadP6nxSptRAiBMr+EffZp6NV76tZqqwo0H8SDEbOyY0Z7aiPTF4QTrLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.3_1695740211629_0.3856627613361028", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "0564250ac00fb2ba4e403918ec920d59f46cf4ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-1Eq5RYLwQWCDpJqzpJ4zYVuD+cZdRQbabU6QnrMh+hkDPJYEWi9QSN/0N/avEcJT+Hu5ROzvU8fC2hrRrsha7A==", "signatures": [{"sig": "MEUCIH6xM5djngMyrXy8MRbNb3p0/O7KTp44g+QL9SmRw2h1AiEAj8ER9YTtNUlVjiCau6Ihsu2Fo8pdhHcel79DKhZaHGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.4_1697076377457_0.8403021360936083", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-property-literals", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "54518f14ac4755d22b92162e4a852d308a560875", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-jR3Jn3y7cZp4oEWPFAlRsSWjxKe4PZILGBSd4nis1TsC5qeSpb+nrtihJuDhNI7QHiVbUaiXa0X2RZY3/TI6Nw==", "signatures": [{"sig": "MEQCICdH8iZuUUWQa4AQKIB6SDv7xwqFgL9km+BuEDhV8FtUAiB8G9l7N2drq5wNJypTZ5aaibG4ufQyXvbK8/ZnDyggkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4785}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.23.3_1699513435867_0.6419585074088161", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "f3e3a37c05d5510b42600956fa4c917879ceda3f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-lO99Joo45LwGhMMHCUbAIN7+G/iadf+adH1YJtBsJBgEBXNbefQGhFaAvSU69FJV7z4tXKryUKZqm7XeqceCTw==", "signatures": [{"sig": "MEQCIHwutGltfLNa+pbtheOniwGRXqeZKLwL/apzkaJsRGIOAiBU/yjIdN8SKSk7YSji1/xtUUyeiBaT6PGfc89dbzojLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4724}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.5_1702307927731_0.035852038514215145", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "d80bf2792f9f81cc7edaa7259b4e57c5a498ec60", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-JDVMBgDm1ZMIrmRwKw5179RpCk1GzqcsJkBvr0IgvQuj24Z3n+4eXkztiDwB3x3knEe90iRkmRRLmAavKT3nSQ==", "signatures": [{"sig": "MEQCIHVaFkXLbY+7sI2XlG5e2ZjIURbuRko4nvZ0u/5JstHyAiBypTy5r01fXawrnxy8tj3pbAhNddI2LAVmXBw0ZoTmTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4724}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.6_1706285644466_0.14180217493883185", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "b73cc581cfa8f37982b5d5244a0974b623d4b3da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-2aIqRlX0bIyqck8OFQFU4FZ3rCWW6gUGPwn+rutTHYUYvUZURM/LvI0ZSvEc2himY8ivCcgrsoEobU/+fD68eQ==", "signatures": [{"sig": "MEYCIQDVz/WGEEAJEhp0aWXRihsF73EySM1Yok+yRYnZpcZe9wIhAMvPbSoviXQ9y8vURBfSJEoBA0uXxDIC66Yzmxp0aZq6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4724}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.7_1709129092714_0.4996190167080168", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-property-literals", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "d6a9aeab96f03749f4eebeb0b6ea8e90ec958825", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-LetvD7CrHmEx0G442gOomRr66d7q8HzzGGr4PMHGr+5YIm6++Yke+jxj246rpvsbyhJwCLxcTn6zW1P1BSenqA==", "signatures": [{"sig": "MEQCIAXOmWwaKDY2iV6puQQYDE4DJZNXO0RynIuJb9DSRzopAiANRjHXQ6D1soRc79fxfpMzbuDkeYJOKZYbG7dGPbBN2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4716}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.24.1_1710841736558_0.22985581331851024", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "c70dc88cb85fc13f991b7d76073645dc977ba2bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-gTY521pDoab17wBp28YLHj7d/8RVcI9o5x4m+VQaTlVkeKBHdNMVA2eczNrtQXqu9VOe3EkEIKIosMcCVz0/9A==", "signatures": [{"sig": "MEQCIGsKI0VSHQtzm5AfL6gUSOPyShtslZpAX5fhwhQRbQupAiBwKpHCOkqxFp+3N8DCyaQmd8yF1/5NCa9GuavJwghSOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4638}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.8_1712236789803_0.6485329239570587", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-property-literals", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "243c4faabe811c405e9443059a58e834bf95dfd1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-oARaglxhRsN18OYsnPTpb8TcKQWDYNsPNmTnx5++WOAsUJ0cSC/FZVlIJCKvPbU4yn/UXsS0551CFKJhN0CaMw==", "signatures": [{"sig": "MEUCIQC37d1OOyWhsBE+V+u05vW++iTsEerbv8bec26ufDAfXgIgSKGi60+LWTfwMRj1Y77oP2eeeJdhr/ntmlJzpPmkwqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70647}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.24.6_1716553471989_0.5477366701166426", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "28ec924e649bc31e691ef236bccf5276b6899e42", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-j6we5YLUWue9JTczuhUfLr7qr82JnjiSnFAuzJYPgVK31xyT8rLHIaR2/OcfQRwfe8DRxZmkmp6bX5lh6dEncw==", "signatures": [{"sig": "MEUCIDsUB9osL02+lY2+isyABR9+ExVuqa/fJ0BbOJeMPjLgAiEAuPvhUkx21bgHMexkV2B/yKXZQDgpN+RlSC5r+bS3Wbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70879}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.9_1717423456684_0.1829061463773589", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "7b532226b0ee050161393bb3037a8270577e1b5a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-Dv<PERSON>xlaifjLIpBzkIn84GAZ46IbGKGfGKyoyR4YGrMqyHVCoNwg79wyws9KGdV/T7Ir0psUvvJomCbiC2JO4Lew==", "signatures": [{"sig": "MEYCIQC/0aevL5r+Bzj+7ZE/Bp4C5qGCWEA0XDgEXMyM841VVgIhAOUDEOXoDdOO9NmRWOCXSDvyEZY6B3hlyK5K9OByXRdC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70886}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.10_1717500005928_0.7970534161837917", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-property-literals", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "f0d2ed8380dfbed949c42d4d790266525d63bbdc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-EMi4MLQSHfd2nrCqQEWxFdha2gBCqU4ZcCng4WBGZ5CJL4bBRW0ptdqqDdeirGZcpALazVVNJqRmsO8/+oNCBA==", "signatures": [{"sig": "MEUCIGbVDNmkEqm9Ds4MuPKT6XHsoatXqTBbD8QvoedVbUynAiEAzK/Ieqe/JxH/Ny9STdDyolpEfJbcqAtIWhr5mgcOstc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70643}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.24.7_1717593321942_0.30068487223572204", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "afbd9c5afedadfc1aeb381c31c2841689337dcef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-h4ZWCzHw6TNBn/cvQcXeT3xgijKGuGAoDce/XUm6BGUCaFAYBXKb8454WUbFMF/anDp932ZyvNCith6ALpZxsA==", "signatures": [{"sig": "MEYCIQDEBtKa0h8/sApqWMxwrVJLC2DlCIMRv+DdX3i+XjGYigIhAO/FSdutAptfm8Ar/ZAf+e9oZRNZYd8uq4KM7UYaxZMH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70775}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.11_1717751732130_0.6637454537353684", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "dc82a428f9ac84105f5fef2c265ec60f0758b99b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-CIAkwApIWVFvugkgfLWCKA9/SNCGz0ZJ3UbP3nEchjRk72SaBliwgJnme2Pp3/a5rATeB8r8FI8e44W+waMXtw==", "signatures": [{"sig": "MEQCIDCdntIvMKCaRFS5dA6Otf7g3SuSvHJSPRQomfYihJnRAiAYOhyl5xeEOf8F8vxJorq+wJGPRhie7RkYatZgPkxRjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67571}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.12_1722015208324_0.6539832188612131", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-property-literals", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "a8612b4ea4e10430f00012ecf0155662c7d6550d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-lQEeetGKfFi0wHbt8ClQrUSUMfEeI3MMm74Z73T9/kuz990yYVtfofjf3NuA42Jy3auFOpbjDyCSiIkTs1VIYw==", "signatures": [{"sig": "MEYCIQD9n8iGtysQt0Miz3+OQS8CUzc6oNH5bDvcjYQQH8mxXgIhAPIvfMvC31OdJszYuKuCEi/q256SeykGl+0WytxKUPEE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75181}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.25.7_1727882088788_0.2897231218976488", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-property-literals", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "d72d588bd88b0dec8b62e36f6fda91cedfe28e3f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-IvIUeV5KrS/VPavfSM/Iu+RE6llrHrYIKY1yfCzyO/lMXHQ+p7uGhonmGVisv6tSBSVgWzMBohTcvkC9vQcQFA==", "signatures": [{"sig": "MEQCIADxCHRyfMNNyL/QDclPhF1uMdug6/VpUE5VegswVitKAiBuhMvPCrY/SIebUlrpuaLPE86aRkKalOtaadOedeM3vQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4716}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.25.9_1729610467543_0.785758382504335", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "2b4ede9336d8b94937ffc0c0263c960daba683e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-Pwo7g45b3s8ktIW98iIzbeBYXk/YiuH3C73wAio7KTihyJWwFBbMAxXGl3bOE4U6WaCWGluIUyIVsxVa1VIUdw==", "signatures": [{"sig": "MEQCIDVI7QarUFMGBpupifCUK/s1yVmP8SBCNG9Nc3/TKmiUAiBId0HaRubErvqvYIT5yhz6vsnxPraq4Xz6nwvSnei6Eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4976}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.13_1729864450104_0.9594810669682967", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "976dbce0becbfa934715c4f625e0f8cd6389aea8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-y2WV/VBRFdVf46AB8dVDkYx6wiMW4lZtH2rxAmdm4nj5DBKGqGZgtOC/00tATZ4hRKZfqtaOyuWyyPteENuy8g==", "signatures": [{"sig": "MEUCIQCyd67c7niIQvra0q+NYjz5gOPy8sxaavGWFvoTs4mrAgIgVtm5p7D5WttLT3LViw1tZwF62HNntZXyajDcvqq+YN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4976}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.14_1733504042157_0.24617384964026945", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "ecd5e761b2baab52448f0975eb2e60baa3830bdc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-npyfHfL7je61zVgwPVOBBvANfqD94ApMdtvmaciAYugAfNYBsEoRJ85RlMGttDa29Pdf3PKZ9VKkHjhMcKGDqQ==", "signatures": [{"sig": "MEUCIBOTt3gZMIEmQoHSVV581id6D/G7EeoTLPvzGBcL28RwAiEAnNw6H527DsSpIGBYoRIjp/dpHd2ubZwV3C3d7uKxtuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4976}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.15_1736529867121_0.6091897675510412", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "f60a186cd13dd7f281502bed9671a0143adf92ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-xQn65IsZNqRDxGAUnIN9vXcgiE/TwnZ5ACkOLpOtnb4RZOkFTGAasQS/lPe8Vu1iiyhNGxL9zp57F2QUumIewA==", "signatures": [{"sig": "MEQCIEL6mL/paMZPamkJMlA9b0OnhZArEhddjI/86oBS/R67AiBebgJy/NY1mpBgG+g5HU1GtGAob9mZFybU2UaSbwT4hQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4976}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.16_1739534343342_0.41324893121089934", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "aebfd53f93dcd0ed3bf0d434a5f29b87603f1279", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-igVwpXLJ1qmh8PPm1/38XvyeLKHUkK8ERBs0e+0LBbZ67OoeIy9sLlbchv+1A+YUvKg6xr2I0bSq64ZojAaq7Q==", "signatures": [{"sig": "MEYCIQC5Rqu9kCP8IiBwlGjVz1XAoxoolVDDATJ6JQK7sxfB5gIhANjmp4aSo4qkzoSXi8PuCLlBEIXWOqs0OThGD32oNyGW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4976}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-alpha.17_1741717495333_0.47539097395390706", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-property-literals", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "07eafd618800591e88073a0af1b940d9a42c6424", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==", "signatures": [{"sig": "MEUCIEWUWftY50AiS+hjA/PgeTsd17X5WyLB561+UiTW09F7AiEA5ZhnJ8RYtZeKPd1s3EpSfCdU/RnUBnZG9kbOWG+o/ZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4716}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_7.27.1_1746025732353_0.3407932729525349", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-property-literals@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "dist": {"shasum": "263144f4cb0e7daabbe30737c08465a526888cd6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-1/w2L+kdsvINBmLDJW5w0YcSkMHRt9fiPbbFMvapz29W+G/BjH7OjI6KyjAhtnfFRGWRFIV4nbzvMrtTH+hp7g==", "signatures": [{"sig": "MEYCIQCvZXhueLJydBXJiilIHHx7LPO9LzCmD3WdVF1DVZ50FAIhAMbH3vgGp8u9O6wi8g/veHNpU8ixxn162S65sz/Wugqo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4952}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-property-literals_8.0.0-beta.0_1748620265442_0.2645911556324618", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-property-literals", "version": "8.0.0-beta.1", "description": "Ensure that reserved words are quoted in object property keys", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-property-literals"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-property-literals@8.0.0-beta.1", "dist": {"shasum": "94f537f22260eafecb0718de0e0ac230dfdcf02a", "integrity": "sha512-zNey/6YrnNFMsmyQUvg2tHLT99RrfpDCz8vWk7Co90uaBr5ipGlk6PZGwkkL5NOkO9wWrcLnouKnT5TYm8jTYA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4952, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD44Vi7uRfaCx1uDKH3tQRvQBFMtZXLTIKKmkbXVBK2hgIhALbxcMw37QMf7YkFXmzdi0SIOpr0LAS1Hz0Adolf+/ZA"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-property-literals_8.0.0-beta.1_1751447057035_0.642777654426844"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:45.148Z", "modified": "2025-07-02T09:04:17.433Z", "7.0.0-beta.4": "2017-10-30T18:34:45.148Z", "7.0.0-beta.5": "2017-10-30T20:56:23.037Z", "7.0.0-beta.31": "2017-11-03T20:03:27.991Z", "7.0.0-beta.32": "2017-11-12T13:33:19.302Z", "7.0.0-beta.33": "2017-12-01T14:28:22.140Z", "7.0.0-beta.34": "2017-12-02T14:39:22.872Z", "7.0.0-beta.35": "2017-12-14T21:47:49.060Z", "7.0.0-beta.36": "2017-12-25T19:04:40.248Z", "7.0.0-beta.37": "2018-01-08T16:02:32.440Z", "7.0.0-beta.38": "2018-01-17T16:31:59.090Z", "7.0.0-beta.39": "2018-01-30T20:27:35.021Z", "7.0.0-beta.40": "2018-02-12T16:41:39.131Z", "7.0.0-beta.41": "2018-03-14T16:26:11.485Z", "7.0.0-beta.42": "2018-03-15T20:50:47.249Z", "7.0.0-beta.43": "2018-04-02T16:48:27.837Z", "7.0.0-beta.44": "2018-04-02T22:20:09.773Z", "7.0.0-beta.45": "2018-04-23T01:56:59.124Z", "7.0.0-beta.46": "2018-04-23T04:31:23.224Z", "7.0.0-beta.47": "2018-05-15T00:09:09.996Z", "7.0.0-beta.48": "2018-05-24T19:22:43.207Z", "7.0.0-beta.49": "2018-05-25T16:02:20.253Z", "7.0.0-beta.50": "2018-06-12T19:47:19.706Z", "7.0.0-beta.51": "2018-06-12T21:19:54.125Z", "7.0.0-beta.52": "2018-07-06T00:59:27.137Z", "7.0.0-beta.53": "2018-07-11T13:40:17.543Z", "7.0.0-beta.54": "2018-07-16T18:00:08.239Z", "7.0.0-beta.55": "2018-07-28T22:07:20.422Z", "7.0.0-beta.56": "2018-08-04T01:05:55.970Z", "7.0.0-rc.0": "2018-08-09T15:58:27.816Z", "7.0.0-rc.1": "2018-08-09T20:08:10.214Z", "7.0.0-rc.2": "2018-08-21T19:24:12.705Z", "7.0.0-rc.3": "2018-08-24T18:08:05.794Z", "7.0.0-rc.4": "2018-08-27T16:44:21.964Z", "7.0.0": "2018-08-27T21:43:18.968Z", "7.2.0": "2018-12-03T19:01:36.976Z", "7.7.4": "2019-11-22T23:32:15.456Z", "7.8.0": "2020-01-12T00:16:42.147Z", "7.8.3": "2020-01-13T21:41:51.765Z", "7.10.1": "2020-05-27T22:07:23.252Z", "7.10.4": "2020-06-30T13:12:06.711Z", "7.12.1": "2020-10-15T22:40:16.615Z", "7.12.13": "2021-02-03T01:10:59.884Z", "7.14.5": "2021-06-09T23:12:07.276Z", "7.16.0": "2021-10-29T23:47:32.265Z", "7.16.5": "2021-12-13T22:28:21.418Z", "7.16.7": "2021-12-31T00:22:05.335Z", "7.18.6": "2022-06-27T19:50:01.890Z", "7.21.4-esm": "2023-04-04T14:09:26.451Z", "7.21.4-esm.1": "2023-04-04T14:21:16.967Z", "7.21.4-esm.2": "2023-04-04T14:39:21.677Z", "7.21.4-esm.3": "2023-04-04T14:56:09.159Z", "7.21.4-esm.4": "2023-04-04T15:13:21.407Z", "7.22.5": "2023-06-08T18:21:19.832Z", "8.0.0-alpha.0": "2023-07-20T13:59:54.811Z", "8.0.0-alpha.1": "2023-07-24T17:51:55.118Z", "8.0.0-alpha.2": "2023-08-09T15:14:55.176Z", "8.0.0-alpha.3": "2023-09-26T14:56:51.815Z", "8.0.0-alpha.4": "2023-10-12T02:06:17.689Z", "7.23.3": "2023-11-09T07:03:56.012Z", "8.0.0-alpha.5": "2023-12-11T15:18:47.953Z", "8.0.0-alpha.6": "2024-01-26T16:14:04.605Z", "8.0.0-alpha.7": "2024-02-28T14:04:52.869Z", "7.24.1": "2024-03-19T09:48:56.686Z", "8.0.0-alpha.8": "2024-04-04T13:19:49.936Z", "7.24.6": "2024-05-24T12:24:32.172Z", "8.0.0-alpha.9": "2024-06-03T14:04:16.849Z", "8.0.0-alpha.10": "2024-06-04T11:20:06.109Z", "7.24.7": "2024-06-05T13:15:22.156Z", "8.0.0-alpha.11": "2024-06-07T09:15:32.306Z", "8.0.0-alpha.12": "2024-07-26T17:33:28.501Z", "7.25.7": "2024-10-02T15:14:49.096Z", "7.25.9": "2024-10-22T15:21:07.719Z", "8.0.0-alpha.13": "2024-10-25T13:54:10.324Z", "8.0.0-alpha.14": "2024-12-06T16:54:02.337Z", "8.0.0-alpha.15": "2025-01-10T17:24:27.300Z", "8.0.0-alpha.16": "2025-02-14T11:59:03.537Z", "8.0.0-alpha.17": "2025-03-11T18:24:55.545Z", "7.27.1": "2025-04-30T15:08:52.550Z", "8.0.0-beta.0": "2025-05-30T15:51:05.605Z", "8.0.0-beta.1": "2025-07-02T09:04:17.215Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-property-literals", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-property-literals"}, "description": "Ensure that reserved words are quoted in object property keys", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}