{"_id": "@babel/preset-typescript", "_rev": "135-ed2cbc8d2a7a5ad1d97494ca66103767", "name": "@babel/preset-typescript", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.4", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "61b7040afdec58ff80ef762a9c919647c9a74358", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.4.tgz", "integrity": "sha512-MLNHjJ/0+pFaGhkUcSPJQXBewimgM8UsXnQWIH7naWTqo5j7RWES8W7fJ1Nm6K8uz/Wbemka3fK8G1KKfcIZXg==", "signatures": [{"sig": "MEYCIQDfcET5nlh+VGJw9j/O25O+UFokNSjmmy4UqZBgyf/tbQIhAKKGeef+GQeCOv6NiTOfYhv7YufnlXOucoNRpVrRXju1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.4.tgz_1509388547480_0.306059145135805", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.5", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "60bbee301663f41825463855d9cf292f0dc631cb", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.5.tgz", "integrity": "sha512-tLXVrKJ9JoMR0gctz2Wm3nj00vL4M84MaUBjfeGIcx+l/7Nclj2D++12fLSdBKFxPLzlkDiUXjLgxni8nxKrvA==", "signatures": [{"sig": "MEUCIBdXahEOOAGATsS/ptovtCjlPSv7HdQ4VteC4KXuWB/4AiEApAkCX5zF2HuUJcsyTjpk4ka3Y7UmEo23PwV8qtqK7Vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.5.tgz_1509397046460_0.7988792902324349", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.31", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ab6deef18b8f4b75c7828b94a0ee1f64fdc405e0", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.31.tgz", "integrity": "sha512-3LtHKWFVihRGtjK37prj0jV/fgYuAJQMYpDsLcpp5lVDBochhedNXRJrK0edHYISrcPfrrz/oggq/Xi8akuXAQ==", "signatures": [{"sig": "MEUCICBBz5VzChHV1Ypa+EJIWhvR5tORCK7cFyxhBYfKo9Y2AiEAhY1+DzgE48KTMKG2Mm4cp5ZIwRz3yPNxfbHt9wVE8Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.31.tgz_1509739450207_0.0843143432866782", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.32", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "699055734b588693dccd8f7ecd904eaccff62ad0", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.32.tgz", "integrity": "sha512-/BZgxKxgppF8w4r2ijQRag8p6RxOeHFyGUzx6yCPpFgX9aZ3X2AR6GHyjlKJc3nszZng1enNaCMVRU0Endxs5g==", "signatures": [{"sig": "MEYCIQDTRU+jSvNV/ze/w6o5KT0pXCv1dt3Ni1ClCNa25bfWsQIhAOZlIHme161OuLEoGNlgxUiN5cFxvA9wCgtLKM0BSHWU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.32.tgz_1510493631185_0.6704312125220895", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.33", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "43b60ea11d9a18f9d6de733bccc6abdda349563d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.33.tgz", "integrity": "sha512-MxXfxyvh/IBwUmJqsBbQfpWNyKNBa4MEqFKCxaEh36ajT4mgmA/kZ+4f9x0ba2sYxX3Ww8W0eG9+7vrlKG2k0g==", "signatures": [{"sig": "MEYCIQDOMD/cXAnUlxOaecOvfv8ccl/nIjt8jkzBd56Y4OZxqAIhANJhy+z5uwqm/snr5s5PyKh2mQaZtWnd+s+llYE6gypC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.33.tgz_1512138547760_0.7005888556595892", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.34", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3df34f1de48291a63d5fde96d47ea11af018f560", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.34.tgz", "integrity": "sha512-KBTmjiWyOdwj0XmRrxzGTjR7JBSmYT2WQ+e+MhUubI5HH0JouVzl96sJoBrM1v7v0lpoJhOO+tYfwCBjTmiB+Q==", "signatures": [{"sig": "MEUCIQCGzj61b8e5ratTYLZmxNQbhPsUV3nVxpy9WTzNT9GzywIgQVsfG6M3pSj/Inx2NBYRNJqmRR+ieM+7SyM+pZQS1Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.34.tgz_1512225604568_0.7712397677823901", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.35", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b1315b0d599cdb3272f85a3a3d5bf724cd442815", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.35.tgz", "integrity": "sha512-XgC9xNZ9lr0qafkeQSx/UduQFY333lxyUREs458aIVfktem8mdVJSSecX3QwHs+ONiqg27V6ckZWyYubdxm7vQ==", "signatures": [{"sig": "MEYCIQDCC03dkvZa180fJbPZ+KLkaTz3aOVUFCGO501lANlrHAIhANBB9zxM96S0UgiJ1UICW8vEVAqVFUGysTmewAmD0rn1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.35.tgz_1513288099410_0.11422558361664414", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.36", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ae7df38f6ae889220ccb98afa8617a81e8f7f69d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.36.tgz", "integrity": "sha512-Af4e5a4eHSSPutnAXCUQ9emv96we5Rd1Qk0lo+ierWvs9UpRM79XzOd3SNDCq1i6Vqf1w7bwyPCdMuhSs4VAdg==", "signatures": [{"sig": "MEYCIQCcEIkqlAysnAEFMof1xqLg+WiK4XLIoWj10TWZpa1dyAIhAPgfc/ouv/N2Oux3dwSP5Pd7qXeglXxlyFje4RA4Bj00", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.36.tgz_1514228726841_0.5878757804166526", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.37", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fa844111bd7183be00b466aa913b0ddf085f460a", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.37.tgz", "integrity": "sha512-OHHMp+6JWV6vJxbdCd11sJ7JxNlJ+uIXv57kXg+sY98N0wy+FYyj8QomOL9L93dVYC8zHcAvjIMwegXKK2jQ2A==", "signatures": [{"sig": "MEUCIAYt6G7N8F+NOGJMdtPeSa8M8JNJlbcoOq8bOetTF4pSAiEAtzXaCoWmBlDgZYhLm52tUuo8jHZxSfb/Wl+XgS+PKsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.37.tgz_1515427414414_0.8398248662706465", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.38", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3c46ad5129db53ea4a62186a59695d4343898149", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.38.tgz", "integrity": "sha512-yMVH+tUoH4jLbvGeAG6CRfxDVNUib4zoAVOveQ4+BFNLEPShYJl0aMnrKyGaBKVHqpjvxcq70/YuPA4TeE1Mng==", "signatures": [{"sig": "MEYCIQD2PdPteeJrjwpPEXm3vaUE/V6H2xC5W4hYwXxT+h/ndwIhAPHoGxCMlvaIsJR67E2saib9dZGlt5UUe+OTH+xoh7z9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.38.tgz_1516206752288_0.6092016228940338", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.39", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "da4c7649ff77310ac5bfef465a78bbdb1eb9c556", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.39.tgz", "integrity": "sha512-YA2A0+mKl+o+PwZ7IRcc7yXj/oI3tEBJiNSfLTJZAja96TX3NZzCOXxovZ046e2Q/80nh9A1efxy1oM5BfZcaQ==", "signatures": [{"sig": "MEQCIBSerlzzeU5QsfxK5HYQwCnxXzsbtMmn8WWV/w16BbyOAiBOcqxP2/9nW7I4xBuSRIx/GiUnMtIexzLrLR46Ocz2mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript-7.0.0-beta.39.tgz_1517344119088_0.7419545471202582", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.40", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7f04a10dfe775c3939d38b6d1cdba4d087e1acc8", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.40.tgz", "fileCount": 4, "integrity": "sha512-G74AcsTo4QPTn6RhZGxG2TapvbeA7SZ7+1dNtn2FZRD5LisJiC33AlBal6MMl3hqBL61xJVcAkNadbtC6PoC1Q==", "signatures": [{"sig": "MEUCIHzbPU+9LuwbOnbYuOwdKTf7CO3PFV90E+57w0TrO/OTAiEAjHRE87YD8bS9/b7JttYCnZB63Bv8PGcnI7Xf2xWD5Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1847}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.40_1518453744399_0.668351091655645", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.41", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9227cb31ba854d2454063a18334d755b49f03e80", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.41.tgz", "fileCount": 4, "integrity": "sha512-aWkout+4tBIXPX64U86gL6fZ7Ka67o4iy+iwtwbp6snhMeeLnHcnhqlZPiTDsF2e/VKXIaQRysg9kmcc100x/A==", "signatures": [{"sig": "MEYCIQDg0Y9aLHAXHfCLTtOH4z6POrlYiOWpncHXPltMcoGh6wIhAKirYnizpjwoku+l/wguaNwMFAQ9fbzmAZVkBHEVzL8E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2143}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-transform-typescript": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.41_1521044801417_0.7389062076026982", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.42", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "adb91d387a6eee7b45918de544d6c8fa122c2564", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.42.tgz", "fileCount": 4, "integrity": "sha512-z3ZZ+bsCKFnygo7owlk4vAXOdhjhRxgyH31A1wKwq2talWx3ZjbAsmqTMBcWK4lb8XrwS2QASGYAb4BfY7zWnA==", "signatures": [{"sig": "MEQCIFbFXBhZpNGkipWDRXv4einLDL8pMXhr7MHH8cOWvEy4AiBTW1jTGkQk3Plj9MlBSqXF3N631LlcHn7qglKzi9HlNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2143}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-transform-typescript": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.42_1521147115602_0.2993737511030343", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.43", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a69180a938dc44821161725d74cc87eae5aff075", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.43.tgz", "fileCount": 4, "integrity": "sha512-SUe+7dWLxyL6r0UfJ81x7j7kZ6tR6A7A/1PWaiXPY/OquI+EIZA2QI7GLaDYpBUyxGZAZkVc/qQ1ffvTXVusRA==", "signatures": [{"sig": "MEUCIExdHag39F4sq5gWsxt0REWqrZcZH9EoRp/62ubEbkpzAiEAvq5UpISBDEbT5u2G5LU9ltmaA+jrfDwkujnw8xZIboE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2385}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-transform-typescript": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.43_1522687730260_0.841939258793019", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.44", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2b5890bba7b21df6af11c0fc23b1251bd48b2c63", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.44.tgz", "fileCount": 4, "integrity": "sha512-wWt9JM2FKA1KUeNqO80h+fnMFR41dlhrb5RXuTS12sdBaufXtUJoN0Ku9L+i/Hh77cleK5EjXX0b+a/Om1UEtA==", "signatures": [{"sig": "MEQCIBFEMIAYxeQJFj3nXvZ7kH/Zgqzxdy6NVHsMv74HQYHxAiBFyCzl/Yakml2GhNhtYMFQDQ/dMaNnL7i6EATyptZPaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2433}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-transform-typescript": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.44_1522707629640_0.8619449968285033", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.45", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8a0f94439e1df6bdfe4aa43d164079d033edc869", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.45.tgz", "fileCount": 4, "integrity": "sha512-pbNK2NHYHCYI2m6AqTwS95IwNvUp91dJ+BdYlcT8ZsPd+wwoOzbTmgmPj0onvnoKzFacoehtQJNpYqz0W9yttQ==", "signatures": [{"sig": "MEUCIQC6X2YAUMvsatlsWoeaizzpKvqi3/wN0YjZKZ8LuzRdfAIgUt2sYd6vorQiXhzx3PP6tFkGkVl6xWfNH0KHxV5Gn5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2zCRA9TVsSAnZWagAAUTQP/iFZSTswZ/JFT+OMjQk2\nLUUl3K30ioCVuKUAHSXLoN7hKoQFGByKM+mi8Nh1YfTTlx18KXW6xrzoAkKl\nk0szeqrf/ujmeZakUs4vYO3suF73eQQD+ohyUXf+nhrfoUdh2vfkZCL7OBUM\nzGVGZqko7cifKAlRJZvXM0AGpO30jWZRPMCGA8lcbux4GMeNkCPUCOeqKUUa\nrNYSUSgK12bWRVpwEO0xhSJkV2WeJKnLrdJxwcIYvCmzfBZDL8tKU7U7LKBD\nVw0DipJKf0IUl+4tciNXkUWiioW+jx8GxLlxQ9lgqyrYJWT9XAMsQhKEnI03\nh348W9YRhiP+tE9A+u/rLzPv0kCypeQ1ZN92kmzJkMnPvPAOJZq/AINrUBbK\nRGSnpVndC9w6nGMbt4Q6y5LQZxXFJwbkDLVi0pHLCdvCyFp1gErliY9uKVK4\nXjnDd4CW8Oj3abu3KKv31tkMUgehr0OOFGp7UoB6iJtNDm295mV/fY564DHB\nIpJVpTanNmO5sQcjF/e8+CBjuDNTG6VjEl6Dvs19GQiaWfbMlJBayo5FRiks\nkFfUEEmP+DLJNck999CDoMZpo4gsMDRIPV1B7xJsqVJ4sipInASuvtQcyqq8\npnr6XCdEBT82TrL8AHZc87BROI9uq5mWgLwZ4U3e0+FW08bVZ8MFVnGhwmii\nxutA\r\n=LOIB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/plugin-transform-typescript": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.45_1524448690986_0.9445015361069842", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.46", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ab14898e504c7f172cd0fbc03491f6a5685d8638", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.46.tgz", "fileCount": 4, "integrity": "sha512-IcwCQ8f1zoWP0i5xyrmKlXQaFEa+X2ogxaTGRfzPNBy9iQ97BMISR7oPJgPxquktk7WoImpGp+T5Ws1d4OcVRw==", "signatures": [{"sig": "MEUCIQDVd+Rge3F99PDZKKHoR2F9jbrI9suN25p5YqZkD2cpwgIgMwNPNKiZTOrTZO0s9wEkX8JJYNRtyeSdm4HB6PqdgsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHdCRA9TVsSAnZWagAA42MP/RIhq98vu0eVcxLlVmqd\nMx15LzdrMDkH2tu1PK/QWeLfOKlT9H9a+S3b4HNK6PZ0Id8gQ4Nh0FzmbGnC\nF9+Jy/WDGrJl1ClS8W/MGgupMa9kyI/r7NBQTq03DbjoYUQkMGjZQQvfnjZ0\nLzz17cXYuGOuzN9dQVzgPrOGHyoiddXIJDKXrBgULlzZHaRJ7TsPlWqzA/hF\nlZME3MvMyFaPlRaxGLvbrtxMbl84LNcZYwW6l2Azz+Ors1zF6QoprVyaTaR2\n0s9mE0t2+uJG1ohdPYFv5V5A9Vvq9+KinwZvcmziq2qXIxGzPruiT25Ful5W\n0AkFY+fQ2jvMZ8aUfYMo+eFTL77p8rI7EBGuDcTIXzNx2SQnmp31orQkB/To\n/T1Mru5QdKnVqRmRAYYNLWG2R/OfsVTC/SXqGs3Jth4RdMLq476rlgIZpkXm\nwovBBxKAoEJmqfs5HC0Y/AqZLZmSBZJf+5ULkXzCdskRYPi+0q8ZXFBz6+SQ\nRv8kRLrtxZmNuRkBi7l0Ij06UdK+MFgvz4XJAV0AcyU6B2VaGKWVH2kk93SM\nIDPp2DvNiWN8uinNZpRLO2LKkroOw5GzlSVM53UmldXo1jqatYgxdcndOkuI\nJuNfkhdPf56tB4NFGBWn0KccGRPsHeQR3IHKikGcE74BTtyOj1E0zKy/fB8j\nXzxH\r\n=fKxR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/plugin-transform-typescript": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.46_1524457948649_0.2949379233928817", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.47", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d81fc37dca3bea1213f2818f9df79ea0ffadd6cb", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.47.tgz", "fileCount": 4, "integrity": "sha512-WBaoHi27WxzISZv/ZoWaJhwbgAvqYg8OXkFwWWqdPG0L0ZEhztqW5g/T6WoqFL1EzuIfloLYzw6pmORhlVM9jw==", "signatures": [{"sig": "MEUCIQCiCDc0FkPh8UoaK4EfFxAhSekloZIuNZhQ0QRfQa9wHAIgYYBaT7Ksl+a5CTIjalELhW8Tyugxsk7BuFNIO0WE+Eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icYCRA9TVsSAnZWagAAfJAP/RuUQwXUDhafuree+aUV\n0Y6WJnLOdZN8ODXfPGa5ynAJ5V+aLI57w/GIP9vdwcKQnnqe4Ve6fnh/eW3l\njW6wzLG9cAQOoJWjUNO1xR2QqGqCc9mQeqxvcfXyxKNvp/oUTPCtUDh20vJO\n07p5tdz09O5oVgwasLrSwSuSHg5ezBbkRRLhnvGjv9q0DFUCKDMNd2+znCSQ\nsjc2CxitCF1QwOMy5ldz2yIb82GvPUIVIzclXV9/uEqEe3S7nH76PmgnSKE0\nr9c8i5s9MBlJWfvqgQoZp+DtKZu4oGUT+bGDoLRfu9WC1xM00WETHOirsP8Y\n7aQDRw0p/eSNt4JskjX/D8msig3KrT0tK7lQq+xq6G+16w6hYNH8a8Eol4Dt\nfJ+AWyiNEq11yZkkLURibGPzsSsVfCdHV9rdb07srQBa1AhKKeHo+vpLNHlI\n2F/EldRxb9UEgXEr3f6wYInQuq7Zw2VxRJX8nPAeHtBSeCZmkbtuL4zS6tAA\nQswMAG3d0vxNn+Nqc0MDCHwIr+gJI9WgHCtmox08IOvyPmm3mHyKkfW4SHKG\njtMAGzR5fUFyHJwJMMXLUCEDRJnELECDCDwcJAKIBh2qwG/cQtIYMDFLrjJZ\nkCrXTLjEsbY7zwHru2bjGzJMT0pD919wThqf1yhqjqJOfPbWcWCfhuQ42NFm\nGQrg\r\n=JbCO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/plugin-transform-typescript": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.47_1526343447824_0.7651556878257499", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.48", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9b076baf1c3e2bb7eed266872326891245af615e", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.48.tgz", "fileCount": 25, "integrity": "sha512-/KIwT1xbpO6VbVawitEfka8DIcpeJkaTY6S1A34G9Q/u+jztkiEWp4/gbYhajm4dW7U3Rlms1hLRmEl3QD1QxA==", "signatures": [{"sig": "MEUCIQDaaxYTXSQ5KYFFP/E8WlkVroppL8OiDLPFiSJG5PnFVwIgW2V71+3xUmZRAQ56hCZXjOf5CpnCn6dfQsVJiXPkMfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFpCRA9TVsSAnZWagAAwTMP/20DOJfI2rkto/zCJznz\nlKla/IzJ6CUAbh8B8Z9bPqFKaUKGGsxupC3Sba3bApdDonchJnsPjl70Dafa\nvSeQ51vWAxpO4ZgiTey1Znm2OzifwEm4RSP640upf0IQW8Pwy9eDWDoIlc9o\nVsHekqw9AGSgHu0BT9VMa0Knqf+ezxoKL6dBzGoHtJ5329vDgp9zn/6+X6qx\nJnw4N1l7Ey14MR34ev1on5Nzjt3S26lkG0hFxwhRnqVSE4apYdWCkibf0GLx\nEWOFateRS9n82Waothe07qOVlGf675C92ctJAp8+NySh4rrBMmYEpA6sgCeV\neGeedgStU/np1F946sTtxMmjZM+OHQdM1r98wTasxijJMz1IdBSrH0hWCO6i\nCwT6395Z3yB97ZVVliZVtrlOk0lRrsWxvXEoCx4/lQ5ZBoG13nwZYFSz7hmF\nlDcoarMplIfyEvMNBs5mKYuvCGQ44i/1A+8jGpPQWPYFtzVN5/P3JN37a3Ig\nBvCJuNcI2QxPDMygBfyeD5WPFjV5mYrfm3wHHaRaAAgzmoSMLpb+xAGqbzN3\nR+Wrv8Q7VzBtxrSce6AkwTVu01yBt5mdrBCeWQWwCCYmpDgXiHETGU6poAVw\n01E+ztqgrdRu1yCGhA8N7abjisZIUfzoaVqhkeFpT7iFRsjMi0e6KzmnF6zn\naQS9\r\n=SxMK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/plugin-transform-typescript": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.48_1527189865563_0.1853843909612034", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.49", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "20a3c4a2f815efe7416f756b433c0fd9907a47ad", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.49.tgz", "fileCount": 25, "integrity": "sha512-fQUOotg9wfZ4u41GGCTgBUpuFHav4Buv36sQ/7YTd504bLqgkjxGPsD4L5KPzI+A+FzUQp0xNTtHUTyZST1u9Q==", "signatures": [{"sig": "MEQCIAoTM5KYWBlQJ2tehvY28bGNjYEXENbMuYhjDfMpoC5LAiB0q5EJCsBrqSwqvUTUOQUmj5tvp529y1i2vyuj3sFDhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPuCRA9TVsSAnZWagAAbR4QAJ22d8zWDNqxIP5jRjbq\nrn7Q7LgTnhHX7c1higa+Kyj62fgYXFNEEG9neTnvNZS9L1IPcFOut3mLcpKm\nI98QE/PgLX0uFChKEnmqBVVMb4Yq5qp59H7RP58r1KQ9Y95hWmFom3MpzqpG\nxhJzDak57d+mDE5FFFh2U7JiPxj68+SIFVxBSJD8HITcWYz9k+hABxkdCLSe\nAp7N/HaPTLMCuwGpkglJVyvueJR3mmTvZHnKwJck7WCJH+hRJp1EGx9Xhr3C\njTpYPU2KpHmkhRS5L0vm85ZQctPKC4UcCBRO/rjN6iNqdrPCW43RvTUtazc1\nBv6wgpl13vI5+VwrNZyQBANA5+e4w7JPRkSIBpu2vaImggJUwQRiAcE9X5F9\nT7QSDIlgHY/JaITjHsQACjUpZ82UzkBaAk+tAGFn/6LvpXl5loz4oJYlj6A/\n+ECrUdbvTPtbILptL/425gIlE/UWQX3QsWHcXQIsnTZ0VNEitGNlj9Z0Ysze\n37Gfw5CifTwa67rD2EOFpjtlT6AQhH7ewPeNXXYFSPv4prVjnqy2GTdXcOLd\ntWEyq4ALc0IpEfrIC7agVFE0WWEzRh8zc/74UdiwFNVW77X0s5OT8N/iXups\n8jNSL+hLlTz83eSrhcHMsFoImV6ozotPcGlrPD8ppB6RHlxp348EKHNwCxSj\n5VZY\r\n=DtTH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "20a3c4a2f815efe7416f756b433c0fd9907a47ad", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "3.10.10", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/plugin-transform-typescript": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.49_1527264238095_0.19602893598199445", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.50", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4e96b32eb660017af1fe717f3fb64e5b7bb77455", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.50.tgz", "fileCount": 41, "integrity": "sha512-K4WBTw9H24dgLEKALs8ti6gXLB8KYPvxXvSPvbcSXng+ewWkN8hxNft7IPKfKdGbJK291sU0QfAsJc0brIMBNQ==", "signatures": [{"sig": "MEQCICc4IiLKMQ2rRNtHBElzBHuMTS0ebaZrHniCGyYRDHHZAiBmuZfoKFUjJ8kSI+S4eGeDB9oRn+M3gUpYROo7Sq14tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5244}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/plugin-transform-typescript": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.50_1528832871539_0.5642187435421406", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.51", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "95510b2b1493c6b210a499b73e80cd8a9e2f8ad1", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.51.tgz", "fileCount": 41, "integrity": "sha512-IXSJFN5F7jKWiFItzXQXaDQvjrJjrg2AiRihudU0Q0Jxc5hRQgKr+kszlpmEP0PiVgpFFSlKVv3m86zEvE374Q==", "signatures": [{"sig": "MEQCIGhIhhefbLyfNuDMUvk7J1KoiCc97i3CWkUG569bTbkeAiB5o9+HcCdQIlDerE1SVMGhbcfC6Bt5TE6uFw6hFUpH8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5258}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/plugin-transform-typescript": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.51_1528838433278_0.6832368777164255", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.52", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "34778e100bc69411463d44ffc4d53aea798b9921", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.52.tgz", "fileCount": 41, "integrity": "sha512-U+tp6qYvKaROAL4oo5FgfGFrGzs/qqN3z2h4RqoPtSCIyWrJlQxlwQZisIHZuhE+XFiztbmsKkqyZxhOUqv1hA==", "signatures": [{"sig": "MEUCIQCDJjco/Ib12kF4NKNqsXBlJ7d2IuDW3lEsX9jWCGgCTQIgI0yqf7tKWu6kNTtNJNT6wWR4L8MSFTP+nN5KtOm2fmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5257}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/plugin-transform-typescript": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.52_1530838782676_0.7263500244673333", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.53", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "15faddecce27b9062ba50e1a13c201e2ce2ab9b0", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.53.tgz", "fileCount": 41, "integrity": "sha512-M135AzJKxVHAMx0XBCjVtVXm2EWXN1RdIrwJfgepnBJJUWqyNWRnr0lV2RQU2xSpVZ2h/xGPepSaFrjfcB5+8Q==", "signatures": [{"sig": "MEUCIQC+ubIGEUvrrV1dcBLHrc1vt1gzZ1wSdmzwFTbZgYyBpQIgN7PgO14+8TgkM+gYY61kCIAkRmPuwZgRWaXbsnvXbqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5257}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/plugin-transform-typescript": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.53_1531316441881_0.024067009638654158", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.54", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7491df7c0f20d08ca63c41f78d2c722c92470391", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.54.tgz", "fileCount": 41, "integrity": "sha512-9pFL8K194CDrNeZ14wnqcF1zOCBv8Yn+YSTvhdZtD3STJb9N6a3whRvFmbbC2nWdRrxugzq8PUl4g8IX+TwgBg==", "signatures": [{"sig": "MEQCIGlVYsj20nEGFd1RJjLZQY8+75TOzCFnS4cfCyhMsC4aAiA1xoQgo13J2LY7cmz0myWwgEfDH5sT75xe737FWIL79w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5257}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/plugin-transform-typescript": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.54_1531764023624_0.40672037010501083", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.55", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b1eeffe0b59bc5cfd2a7ace9303b478a783fbe2d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.55.tgz", "fileCount": 41, "integrity": "sha512-pZhMdmvTSJP9DFDAGyV6SsWfXLgivfrz0IXV9ZAkoIcxptNWSOWMTDf0qShgKhTCE4mEYwQeR/zUv+LwH51yEQ==", "signatures": [{"sig": "MEQCIGy8QLCuTkkeMfMcw08ohR1OOc1TLonVVYnFWdcn90T2AiBh2flS3UfPQXNnuRzjKV/YlOWVInWkAWrw2dZORMyCYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5257}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/plugin-transform-typescript": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.55_1532815668921_0.4516327294178544", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.56", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "eff0414072f018014f857c072a07e524088568b0", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.56.tgz", "fileCount": 41, "integrity": "sha512-9W4dRiUUjX4hQHa9a82d54wyODor+CRkdPXmXR3L6PoHijCox5SV5JAXYx2KcmlYLaS9jefIDRjRhruqNjPfKg==", "signatures": [{"sig": "MEYCIQD8DQXaWPZf+Iwjosl1ebOPIfEIuY7b3D7BjWazXWPdMQIhAINzhAnXJ+C4uvbzb9Ig+iFkERSpvqkDq/ivSsLhlCNC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPx2CRA9TVsSAnZWagAAZyYP/0VrJ8BSB+hY+sTLIguR\nAKcHF8J4jeD5CS5Uc4myHpPDXvrLub9PTWdsxrVoduR4CCoM8Wn+G4071KTL\nvFQQH7F19q16wxL5u0ALo1iKdCciWhkduJfP/6w24e93tglmgBhWdLhOzUkd\n/QvzX6/N0fqoOBcAho2kap9lupMEuHaed1qPYY4aWkvQNWOSmqxfPwNBuBIZ\ncEwUZG9+va0t9JdO7EYlbQP+jNua0GcGgb1NrQTJwcTlzzzcaa8UtGBzyZpR\nKSVbmU2XtDQQG1XxsT53XV4bVkKt6pftIckDkYF9VH7qTdC7q/SEamVhT9W4\n7I9AN0WxcuPM1BMMzTpYQKqVqrdhRJP7/ayr2tX4kZFGfgZDyhGNQ/qLxK1c\nryJPCARRK7l0/sQnSC1yBZ55N6ccir8i/NYO7AzpkV6uqUzYI1EE/7FCCYnZ\nZ2sY8oO2DGX4mMpBcU8oXp7Reznil4qsI4TyFE/DvLzerzG3qqQOmcUrbWQh\nhGjcg+b8OijGKjvID+pvxQcRsNq7XT1b0WJAVcqPaMy9HMGqcIPRbodcGFcc\nyjOrN14cJ9YAoE3zT71Ndz6os35GFbgQF/nxSjeLvX6Pf716OZRCdH0VO3J9\n2U8WLwG4YF56854664aK0hYjjfQ3ooLtKj+iY8MHBfWNGJq6F5lUuiRINA+j\nvG9g\r\n=nKXB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/plugin-transform-typescript": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-beta.56_1533344885594_0.8967463984227066", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5b851d197afde3f5313f9d7dfab162d219e78e92", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.0.tgz", "fileCount": 41, "integrity": "sha512-fERYik3V5DAyxJUhMpta255pv50HRDQ1CEfHWdfwtLrCzCY1r8bg4Oln4nIGYAjgLVMQKeMlF6h3XlJNgSon2g==", "signatures": [{"sig": "MEYCIQDEQzzPOyEuSbewX5rNH3RDBBTPc2bbkxnq+wLugxYiagIhAMgMQJsK2MC8Y15Qdsdss/YD14ZnDM2FOWfcTjrtKZ6k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTrCRA9TVsSAnZWagAAiQ4P/0cK2ojXhQkmUR7LLy2h\nsdxoxSX4RHVxBE+wxFhr6TRcIfmUDJTYVNGDzkCh8msPwZ0hMGZPnHn5HTTK\nMyPcxfzDysp3L3X3+2QFo+WDd/cXHt3exMuTccjYeB6Jw2dPp4TqPpv5g0xY\ntdGeEBkw4UXCpAlwWn5kWAZXA4zNlSNJ3QF94xOGlYfgOKt7TFtyjFVNpAvy\nTNYHH56NW4XPqlj2RBqsE+4Z9fZ/vo3FWxcbp1RlG6qzPoM9JaR3uXlCVRxD\nqz1R7Bb1OFFvE5q8pexPMXRLOECsbIvZ9vxi294ErnIFOxGjgqPXWMbqRw6F\nMCXI2RwV4VCcgP453s/ephZwURGtwYuDdzlXfAUqMYZz7nNuUuvrjIXAOxeH\nikM0djUDH+btOoPDB1PTtlPjnBhZfDXDXC3kEJkj/oeT3soiGSJf2mJTeTLC\nx8WUhYQb0iQI72ky5ud/cMknHdWquYRMHfOqnD7PN6w8grkZqx4x5jfJORTY\nw1W8UKGjueKATQdnToupjrc41QSJPhtgJqbjPD7dONNuKBEwCU/HztyPXjkw\nw8uyVPRZU9Cj5lAhuffFWaoUZ8KaoJz37iSmYE80WEuhcmHeHlbrWcD+0N2G\nsts1BSjC9+q3t3hEJp806H0MEOUpaSeO05eetSGhuItxcSOT8hJ5beEaDvlU\nN09f\r\n=qhj3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/plugin-transform-typescript": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-rc.0_1533830378321_0.21204707967838177", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.1", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bd22fc7158fde7c1dd5bb7853246b5cf5a801c9c", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.1.tgz", "fileCount": 41, "integrity": "sha512-CxP+7usKH/yalTKhO+P9CbEM+3tJMJQjabeBfg+I/UaPxOFh7MP3ZcFXDe1/eHVEjkyTEWDEbm6VgiYAhyImSQ==", "signatures": [{"sig": "MEYCIQDCI7hzIH1akje1Davy/oMbN7yYJ9HWFviYjXShTuPCgQIhAJFi/0zIiCmRaPqX1qvOmNj66NZfT0tmtE2cmOVIRDog", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5223, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9/CRA9TVsSAnZWagAAC60QAIYje86LLvZs1SNGIrMS\n6onl+spM1Axor/53OmVivvJzaH2woW8mOjpiHkjTdLghRlpitK1iR82VP+mC\njoDg0ik1OBMf7kss6os01LoIMmRreV9mVTxd5D0kN+HHf6dv2Gz+sOh6Wfii\nx92YXdPGiZnfSSSf9+gkgrqEmRPiz07YBBbFlrZu/TJ9uIT0JanjNJbvi63O\nxZTJR3LL/DQr8jrD5gTkzJMN15JV1FYd+cQAWWbRh0viHZotqUB6EfwikgAr\nWbDDM+mdatsFeGGYCf7nJA2BMMtSUfd2rqThhl0Pv/My7nvbqN5zOW+gMyLi\nluQ2LYIvcHTQy6eBCavyUgOEG9CJ4mthq3qoZ5oTfSXAZ3f9ZIQqak68/sK0\nsN/L2JIN1XA552dVdV5gS494nSFed2bKhT1jE4KEiGVdKeulGDTPe3Hwf+Gm\nhsjKt/Lhe1URGpb8gPOYIJR0UTjEKrtpvOImmxDHBpGSdDjBTn2lOUzBxcyG\nAvRZqi3Yc9ZIC7DvfaTfyR2q+nYZlTgcFwOAOsNyxAUJXoznt62WoPLCMQS/\n8Cwj2LbmrC9QZeSokXUxTMh5CzncsRe6NrRI49GmNzqpcMH6sv0kT1u24q9d\nowh9n7UyCGOf8pfL1CV2E+uplJJWQT3T/L2ZeUr2nrivLpredPS0gpAmn0i6\nOF/w\r\n=fMDm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/plugin-transform-typescript": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-rc.1_1533845375142_0.005899863118425053", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.2", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "570594e784c864993ad5e479ad27f16c30af5870", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.2.tgz", "fileCount": 41, "integrity": "sha512-wTuoyiK4yQx+NP3Ll90lZzCro5AKgzeiQ8xzo6r/EpD8XfNXstEkUQRyuihKTmPrkL/c+J7ro67Ruhr0GcFZJQ==", "signatures": [{"sig": "MEQCIDHEgpm5TIR9/eJHqCa0wLvhtj0O33PCNn6chB3TjcpyAiA6m2MAGxzPAwUibhkggZuyMG+axUTgsVcinbpa0k+0wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGc0CRA9TVsSAnZWagAAFnsQAIoiFnw6IZJzmZLQL4KD\nTUP6W2O6MRGT72vjsK9Q/fjGtokzZSUH88P4PMqS59o+qou07MUiCkaYCrpV\nmbqxwoqQq+PLV6xnxsy2rmr+n30oPR4FmjZcm6be7p3RqzFPypdqJ23SMBJv\njVDGQvo/+vOQ4jnlLz5BU6O61UyR1hjBjauqFYcpy1foyc4G67r0NgR6j9cl\ndmXk4xiKpZFsn+gMugkrw13bwyd26NvxFotq8Uc7qdj3GX6NW+QScC16wCnH\n/Am2RlbJ/j9N/vDFCr37PXb5P941MUaE2UjktQKvEAmWK6SZ6Jm4E2CldjWe\nb8PJZlzKkjLZ3ePPwcHT4XBnMAUXrFLYCsQU4Sw4R41OWj3j0y5GkzyQb3vA\ni0xf/tdhAUZuuoIoK2C3w7bSwhNUf6jkahFIc6B9GGQ24Z5upiQJX+q5Fm5I\nIt0yZUExlko3Ll0fbXt6Uwo7LZ96yzdKeA15jFAY0vsT+PauKO/wto6vlBNk\nSeuu4Y6E7m0aJa1hNYuyE3Ck8YnI9Fr2NzK9JAzMf+/HVwcgEeQ9IxYro2FO\n7Tx5Cl+Qn/+gp9BjgxDwe2CpoUNDLVyIt87O0NMJ2hsT0a1H4TsTUvNUYTaX\nNczEA9xqRVxANTYGC/+KrS5IscArTwSqLS12S44REeM6KM4X7qLkifFwF/v4\nOnpq\r\n=Z7ko\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/plugin-transform-typescript": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-rc.2_1534879539894_0.04250946978181225", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.3", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7dc31c23d5b5dca7ed9c32e3f7ed9a703d1b5a53", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.3.tgz", "fileCount": 42, "integrity": "sha512-pDxAKCjBPJEOtCX0e84Jvl3fFeRUDH++l+daJuERmm0fyR95k4Cufc6llYvXa36J1lN4KG/Hm8WpnYxSqab3CA==", "signatures": [{"sig": "MEUCIQD19ANrgnIpPNc6rRxcAPPcB6cUi2HSQgm0AOe+nwqTHAIgLcmi2IjRSj7B+Sx2SXS+jaBQXD5Ghw2wtgDOY4Meuzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnRCRA9TVsSAnZWagAAZfcP/RaUOemfINolf4GSwQbR\nzvglZTDIBLj3hRmsRZPSYkNuH53GQlh4V5uUSg78NhvnRX6aSOayv2gnik5u\nOU9aASAsKeDtoVRre21BFNBq8gQNcUKj8egs4ZXNlhf+/X28YPyCiBY0Z6fm\n3AVRcA4ezbvAIQQVtf2eaC8Qx8AUjBKEmOI46yz3cL/sUc45X2/m5SQRIjOp\n4MKjLSW5nI6ea03s0CxfSxs7FxMMn22EMxiC9E0oO/QPaXhI+7lpNp1sFZSH\nqjp49CdWfxXzX6nnfx5y9nJfz9zO+fe5NIxF6C28iKrXmN6S5JR4sK7Jm+hV\n+wJMCVAIcBN1dtlve+f7C4rqqSNNyxKyk545dn6j3AMRTWG2LOHa67t05qQO\noapwze6mB8lHeslTuKvjFs8gyefU7xfzLXw74f+3/rYFd8d+n2zTYkO2MIT3\nD36iRimOaLCK4fga+wNBvR62bNo6Y/HQIIoKunmPmlmrfgyUSoBtUh2OwYmm\npSZZkd/QkHmjh5H78SCwtIw7l3TA1sA15b8ZkYZ7wzy6ujWY1ql6Wfr/L1Zd\n6JS+KEHWDBb0NcxtiwETtdJmzHcBehDtPAkS/xxjHAnUHCw78ocQlxtZLII1\nWu+n7+html//BCqwVCSc/tp45qHx/ucHPD3sqjlCBoPfkbz3sEmGUko3sR86\nwA0+\r\n=5fyo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/plugin-transform-typescript": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-rc.3_1535134161190_0.31462740052564886", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.4", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "28e5c8a83b9e434c0eda3958cc88a0a9bd11ba6d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.4.tgz", "fileCount": 42, "integrity": "sha512-wZiNhb5MmBprTDjgayDoF1uJpKeEUdKaMbybW2KC5yvJ1B5nFuOxK86jRtTc8WsKRjGAnmbDfhUGIKM/uiFwRA==", "signatures": [{"sig": "MEUCIQDrjoDgEqRvzmWpR5EnBzE0LU3i7hU6SpTT/U7Exou4jQIgLKzYtF4spgEUUb29yiwV2blo4LNkibt+8kQlB3kzt9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrNCRA9TVsSAnZWagAA8mMP/j0b23nT+qShXjPSxf8B\n8pOuBkkv0ffCm74weBaQstCUIbJBtZWEs3YeJDVENZPRmI+XZE0U8UetLS+T\nrHIqYnlsBjDXxrgNjjVpyOxqyKrlGjU6wUUA15ApBIjY4TD9iiYW6+FdsItk\nsOkJ+tKuIQ5qii2cXtPcGw0rQqr7goE9C+of0mu2I6Q2KHZCf4HbpWowNe2X\n4uO0eLmr8OFYEuFIeeesbL0T+F/cbFwC4uiEp6GbVpdXsyb80oT0HmvUEGAm\nVoBM8KNgIvq5N38zCT/4ief3TxnAK4bbQFfxmIXhfMHUgDNEmh8jVhA3gZAP\nGeTK0DKLWYEr0WLEZH8FILIRgiLRk5LnCn53rnR7qj8khv5UrtTWWhr3ldPW\n9SiE/vpxjd0ZI6uWNZB0RMaA0xYomjUilAxLTxxakFrbCqO+93sjbcczwN8N\n+LTkb2TzA5i2R6mk5myxkfXJoBNFRbosj9LniDJ5bXCeMxzwfNzrnpv6qxRg\nTlH/3bj04/tbFpn8oRRy+uwFLMIOFlny1nbya58yRciqK28YHj9kRH6xkqUx\nQhsVa0X0VhsNC6rIFta+9LE4H30eJVytE7mBfbz0YWoU6cnov6cvmAPgPxxh\nSa9MtChIUA3NCezcwGllNBU6xmDM0hldjP18HqDo4RbKtWo+XZfv37bj/mwm\n1HcM\r\n=Ncde\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/plugin-transform-typescript": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0-rc.4_1535388364721_0.12631747804088245", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/preset-typescript", "version": "7.0.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1e65c8b863ff5b290f070d999c810bb48a8e3904", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.0.0.tgz", "fileCount": 42, "integrity": "sha512-rFq0bsJjXJo+PyRyBeHDIUGD7+4btHzYcNbL8kgk/7UOxuC9s53ziXxaIL7Ehz4zbsLMREXmUzeamuHwh7BHZg==", "signatures": [{"sig": "MEQCID2N4IAovYXqRuUlXfHwyst3/KyhJlv9M01cgvpq9q0iAiAkdXsEbhyo6IYur68V1GeAKEuJ8tBlmcnJftCj5Hiqeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDCCRA9TVsSAnZWagAA1QkP/0+zFrnT9J4ipp1BHS5M\no3vZXYhTGmflNglKr4gupZxLRorIzbkbq7lvaK+JkwtpIZCnHbrcZb9/k1un\nqQ40eqPdZz3QqkGrw8imfWMjEltr3Mm4ZYdSMHBx7hyLwpQ26rCn7av8guaF\nR+NW5WvzYRLgvJg7F15sWoe3Yav9I0ZNec5GQ5o7K6Dv2C5zZqYqinKqoVwc\nsgorWZ8OtXJvB3OwSbqs3SkyetGD0Anryh+uV2ab0xfZg+mXnok5k+5oal6T\nNYfAk9mjAsmgK3dE9lja9ecLYsJ1EwvPxMRnS0XmWYkogyE7GzpJq/K1r7HW\n6zF5p47gsu2eOEQQS98j4g25kG0UMQfphGHrQzMg56r2/J8DLR0TTHT1a6/x\ndOPEAIUjidRy+Ek78KBgS5RXF/AoEhLZQPMNzl4iN/mTFhhHoKc+zgsrrU8A\njdfR+yFRGkyE7UTvvK5lYumR82WVqBlJX9lP3LqPuE9Wht9BJAiACMr5BooY\nVFC7tyLnhjqT1Azvzu0rU7zYgrMvcBHTiuZXnd+atATcARw/vj94LRHP4yxu\nPmV/fAVBOQ/Mrdl7pN0vU7ofEySipQmACQRbmKAanscc4y94Dx7RPHo6u9HI\nrI1xXyCNdh5Xg7dyzVdrRu9ehfpdj8OOIFHgfTVwvA6iqzFtAhNh8maNmY+1\nYI+Q\r\n=E5qc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.0.0_1535406274132_0.20327953249946962", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/preset-typescript", "version": "7.1.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "49ad6e2084ff0bfb5f1f7fb3b5e76c434d442c7f", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.1.0.tgz", "fileCount": 42, "integrity": "sha512-LYveByuF9AOM8WrsNne5+N79k1YxjNB6gmpCQsnuSBAcV8QUeB+ZUxQzL7Rz7HksPbahymKkq2qBR+o36ggFZA==", "signatures": [{"sig": "MEYCIQDbBILcVi6E27/tpxordT5kMH6rFzbBeSGNY2kn9K5GkgIhAMjz9TVOpb3Hb2I+rL/gKqXsLj6iEXBq2c79vk7ixjJ4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADVCRA9TVsSAnZWagAAUaEP/29EZw0eePikNPb7GmRq\nr52N7HyAEzamwScFUsjCseJarsWXN+vmDzlKLvLagE7zd3Xu6VdvQFBS8ZJF\nGbPAm+PELRRghln4RmT7KssRWQTzvI8r2Ua4tpOzYU4NE/P+GaVB+78r1Jqx\nzvwHIdvpnUwn0g0QgC4sNDK/o/UeesCUkcoAOcidpc26kudW+HHhzNulGjek\nVzIOzLpcpUHTDrOyxqBNRr2PMlgOnU1wekIIyHmtbpkTXDam9koZItjuhCAr\nKd5EENIWKgY1Z0tmk/1GJ5vqKX2GUT8/wPxlnKDeIzjt5S4chMxb8k9kcAwr\nRIyI2hUt2kYQt0L2IeSSANl9bDLGSfUhQ0psTXEHFG2rbY8/iaeMwvaat1Al\nyh3q9vtC80KAGHVwL3MQp2wRnpesWOAZ8rgJOokLy61SJY2mn8pjEdatT5oY\nFjA8nODJrgUH0Ere3C2TdvZ9ceoGO1pPBpXrWaPK4CnR11zU1vJGwb5c6PDl\n9RfG7d2OLr246/olZQ2Wq7Op4BuUm38qivcLggdQN4rsfxii8rRNtNrxpSCu\n54zeVmprh7fK1HJ4wcZcCuhxaymyVdlzV490uJnJQMV05modxjcEWxn5yb8N\nq356WxE46cCi1I2qyb7Egs/BH7j/PgN3f+1E8ROSEXZosVk03iHKoZxOgjbZ\n/XpQ\r\n=lx9L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.1.0_1537212629028_0.5992624590798317", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "@babel/preset-typescript", "version": "7.3.3", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.3.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "88669911053fa16b2b276ea2ede2ca603b3f307a", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.3.3.tgz", "fileCount": 26, "integrity": "sha512-mzMVuIP4lqtn4du2ynEfdO0+RYcslwrZiJHXu4MGaC1ctJiW2fyaeDrtjJGs7R/KebZ1sgowcIoWf4uRpEfKEg==", "signatures": [{"sig": "MEYCIQDtlX7Ve2BEh1u8JNDHZU5ll7quQrUhd1/0EQJ1D/G7lAIhALFijPW61RWHihXhK/3RzufMjGbPw++hfE0yOmO5uPhW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyu7CRA9TVsSAnZWagAALe4P/2T/q+mIaeCf9mdq/cVK\n7osvSFpCEOYvlTIyDqmYyEat4bEcsqc/AHOs4Hb7+7dF4xU8lG7aF08Go7xc\nCNzc4eVa4ulA491EHlpOFgT/P40G0bqjUtnz3ah6Vo5IW/W1om47JtL41+aL\n2yjETEF/Z2HEKK41lwtrG7HuSPlqdp63jNuhYy35R2pY+PJdgq/I1Gz4HC7X\nwATWE511t8jTkktmo7LiUBWfoudMKOdu4G3GyG1JtERMeZqmqyibT3SOjhpR\nAjzZBllqqHMDEUemdF1H1YMIATo5rGXdVeg/rBz0zAKrvLjZx3HrCrQivoWd\n8oFT43Ax/Yimqco7YWho14Rdhb/Ni4xy5lflDmvfg5Su6YenCQr2dq3BHqRG\nYM1WH2Dbqqr0GNdqEfo00xcKSfSW+uoYWXXy/5FEgLGXbgsqBjdeWiy+JCkt\nmklZVIgvS7YH0L53VdlDDrl+JydWUXitsxigwrBBRqzTzkRg0S25dxJMsD77\nz5SCOqpcfKQuIi7WGO4yg9BzkjD7PzqlAbI2bHO+5QsuV/0al+UukSXAn4d3\n5oPXfrzms5aqBAN0gjPA3W0MsCi623oHq8XB8k/4PDwZNU9rE0lUHLB/CyUX\naugf1fOxHN+XzFDxBAXZx3DeybkaGDv9D+IxgmE6/5BF+tj+Hzd5dq01xSNM\nROTa\r\n=2GaJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "description": "Babel preset for TypeScript.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.3.3_1550265274547_0.4836570815753152", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/preset-typescript", "version": "7.6.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "25768cb8830280baf47c45ab1a519a9977498c98", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.6.0.tgz", "fileCount": 26, "integrity": "sha512-4xKw3tTcCm0qApyT6PqM9qniseCE79xGHiUnNdKGdxNsGUc2X7WwZybqIpnTmoukg3nhPceI5KPNzNqLNeIJww==", "signatures": [{"sig": "MEQCIGU17n3b0RAqFQJwoDyG0+cjkTbdW6TwiKMMjmkl3CGUAiA57vuQuZDr7Jvd4f37K9JHlsdOvAIIU5gPY22iP7MRnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcpiBCRA9TVsSAnZWagAAtbsP/3n3zPPsDFwXlHfzx/hk\nWMDHVKPCh35jo06TMSMW6b8RVyhmLiD6K8gF4jsG20/lPr4F5FdR0cLHva1I\nriBlGCoz5KeG7SqIXOfSWjPktqdHqEvq/q2csK82+ma9GoO11oItkp86DWIK\niQrq1msfVM3PEhZbov9A3q40GEnOZO9QtaNymrlV7GMwwA/wqCZpxuHLDq50\n4J/Aiu41OfJ8Nb5d5SZ8J/kqX9TZDnCYwuPymzQX0qX9jac25+qiw4w6oott\nQF0L7J+8BenNDtrb57OXfY0lRO2pvZ9dCo/PMzL8y9NPO8xWDK9wcUMidk8R\n3ybNCA51aF+WXFLhvSbBs5yHrje+4akUsgp+JHZUpNQJxdpir+glwh0Jwpnv\nKxJRm6k7F//F5NIlSSf5SPB/p4kDc+l9NrPnMLzFlrL4I3XnRpG4QKTBE/oC\nu2mTqlzVN2yH5ziBWP76jp1lMVEnSpAtQ85pnQmQPdOofhPUt2Kj01Z7zrUH\ncOhqpNv87hK8IjmkcLwWnAFSjuHtjJ8BkTKmqBxVI/iITsSZ8DOc3ZUoCS6V\nQSRrW40EhKugCPMDKOdWIRUaTKglLt62jv0cpsdZ6g5vxymYBpd7QUNJEnk4\n3xFRWgYqkUt0yinRx7pDE1uovzAWm4UNSwZbIILNYmsoTq7Yi8ma9H0V/XDH\nEwkY\r\n=b4XI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.6.0_1567791233365_0.4542017978934523", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/preset-typescript", "version": "7.7.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5d7682d938160ceaf51c3d4239e9521ef893474c", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.7.0.tgz", "fileCount": 26, "integrity": "sha512-WZ3qvtAJy8w/i6wqq5PuDnkCUXaLUTHIlJujfGHmHxsT5veAbEdEjl3cC/3nXfyD0bzlWsIiMdUhZgrXjd9QWg==", "signatures": [{"sig": "MEQCIHh024E2wXq55qtUYx33Ouln1JD6aC6VGirqVEF2WEoIAiB4hz3LqLYGovL2tIAkXSV4CQ39EEuaIhpQj1cZLvzAdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTVCRA9TVsSAnZWagAAAw0P/1tMt5scEmn0hgGIJNn4\n+x48Dbl8S2kBrGB5tCm9luTVhGy4XCt+bFIDFny2LOGySr0qxjUtbOjOljAF\nMPO8mV+TBnnBkNhGSGtgKtMCuu29KXFcpTNWiFwlK1C+7jItydZKH/N9p4Av\nvXdIzMSCJehkL6CMUuUIl2D8loz/8Fg7p6sRaQaIuVCS3rh54q46NaSfqa/8\njH7PQlcMRH67bTzD31MMjVb7LfMPSOPfxAGWQ8b42Alo/y5Qlhsw0wQLk17Q\nv8xHCGgvnluiZCeF66kSjEcGvkPH7Iqw54/07I/waaqL1ajqx3A54RK6cxUL\n4hM1qUY9S1lnULCZvMHYLa8AAdD0OPQaY28rFfvv+UmjGBxnfWXY12WJzZsl\nx6Yya1FZm8HAzTa03nfoCblsjhVMxiRAxKJeSkNw9T0UDcO2V6IoWUD58uDA\nFY+RshbZQ4h6rn8X6AeDg0W4pyOsQdIvdc22DlhB1KyGGpdOfSJ0bPxROpWw\ndf07EjheA4WgHV7WrtsMJBUsz+irIOARX3toCqUJxT/SAASL8nVhJleA5nDQ\nsOLamLKO5vw0Ov03ZnTK0D1MGlZgnyKxKJ7BqnHZqamhzPvGG+95tH+YcjFH\n/1sIUGe5AxDSjhI8W0Vk/C0MUIbP6PTYA2FgoWD5QP/TKxmiTav+DtENlknI\nnzav\r\n=tyB7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.7.0_1572951253509_0.25708117913142337", "host": "s3://npm-registry-packages"}}, "7.7.2": {"name": "@babel/preset-typescript", "version": "7.7.2", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.7.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f71c8bba2ae02f11b29dbf7d6a35f47bbe011632", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.7.2.tgz", "fileCount": 26, "integrity": "sha512-1B4HthAelaLGfNRyrWqJtBEjXX1ulThCrLQ5B2VOtEAznWFIFXFJahgXImqppy66lx/Oh+cOSCQdJzZqh2Jh5g==", "signatures": [{"sig": "MEUCIQDzA9dXssHWRtEF50n40Z3QcwztKZpmY+Bu4ZqplEXSHAIgU94EQRjqPopH3vPchiRukHtOPUd2oEJkA1Jx+vlCt3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1biCRA9TVsSAnZWagAAR9oP/0gVywsZGJ19XjLQ6ID5\n1kVYB4+0BgwpYw5g4ajTrGtloMcuotASnZa2ciEQ32i2dqs2krHsBVNFqsCr\n94ujwx5smjSOhi6Xh6ntPdNGkuokJza3pQUw8NTGTZ/KjyfDQNvNKnX15Lbh\nbEn4i64f8LW2YOrUE+/39kWUZfIIYLHRfcxSN5tAQOJcO3+aVTH5cAXYDjOT\nZkOsydhlydydkwx53h9uxG5ukIA5v7BUCKM7+u6FXmPfVznhew2T46nZjgRq\nVe7WhoMX+VaVQxvuGQ8kP/7XZ/Xklx7T/XOabu0ivH3qAlWcMWzO74TZZKmK\nOB4FfrCo30sQP/XM55KykXCUETkTMLLHC1DTUys0QGTZxDHL/vm+WpVa1dzn\nP2i8KG7kk1BDP8o+sjDgrMrS57PGNii6chroXOfhHNdOqxcag6M5Wv+XO7Xd\n9GrXw3zABc+EzVuVgHVySLfWVdnmRpAtq8R0x7LG3NSGZSpVmHxGD9MFa0gJ\noBoEGaiWQkUcd9WIlz6fPHdM1xkXMSgT5gWiusArlCN5EDjcgqqpF0CmvUaj\nhwZcG1269riU0DE6TD9YJ7FBEzVl1F85dhw9I0p5kMWDcBG7EE4elmdGcNgR\nK438ulkH3+37mCHzXUrU5DLJ6amBcNhxuuD6qyNJ0byHD8+jhwPB7f08c7jU\nIiWm\r\n=YEy6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.7.2_1573082850422_0.6649097627600886", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/preset-typescript", "version": "7.7.4", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "780059a78e6fa7f7a4c87f027292a86b31ce080a", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.7.4.tgz", "fileCount": 26, "integrity": "sha512-rqrjxfdiHPsnuPur0jKrIIGQCIgoTWMTjlbWE69G4QJ6TIOVnnRnIJhUxNTL/VwDmEAVX08Tq3B1nirer5341w==", "signatures": [{"sig": "MEUCIQD9jLkXZxFczN4DzQepcfTK5jkxSQe0ED19ymaKBXfZhQIgH3FS3srO884VvgsH/vWItt8/ydHia2KdKzFeJ/cqzPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBsCRA9TVsSAnZWagAA0y8P/RjEJNaG3rrAS6J8Fw10\nN29BFUSe9IliWqDbTx5sIWR8qKav6e4sCClBWekHCe9AETBbaVHqU1kJPIk7\n2uK0Dq/ih9rNMH6KpQzUPxt7lYlZB96X8/s/yj170Er0qGc++xjqG1s20U51\nssSQ5htd6p0NNitp17ioKxbH2q0jCHjULIbMHJjSKldtiwGbThn2DWeUPOpw\n9EDmOKUm+BLGj+w5Zbpnr/9xPFOljtOYuAz4FXwTKgzRRao2NrOV73KqPCh5\njJTZGv4BswKhVn+FX1E8wpprfpcUS23AyYvlxXJQSTalU2MhmSp/p8Rz3Igv\ndy35iO5cZMv20TpAUOgpEgDA12ABOq88ysbHOuu/8v9vZnrDH8zLKBnJQ6ei\n04oCs8z2Gue7cOpstDxcBBCWksQwCHZKhwqXcYWy2ur2BuWlxufBVlsFZZ+W\nFN+px3mS1knltu21GkxfMq6PlNCRQCVTG030vg3GUMFxPpfS+bWbhW2nzxmu\ncJhz+bMIxwtuvqR9Wx5EmFsAIHzjd1hk+RQFSjdXt7gtEU3o3H0kjaYlGwRS\nh1aSN02pkjRIlzCdZ7WuYEBp01uVRULpWCPvNR1pIK5ZDIPzxA/0pVmJ0kb2\nleojw/zzjhswrgjsjB8EN62iJpGcMGgMDkmv0dNmCRvjjB2MR0GNhe6I/JtU\n+5xN\r\n=/qfC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.7.4_1574465643803_0.4958011153225723", "host": "s3://npm-registry-packages"}}, "7.7.7": {"name": "@babel/preset-typescript", "version": "7.7.7", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.7.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "69ddea54e8b4e491ccbf94147e673b2ac6e11e2e", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.7.7.tgz", "fileCount": 4, "integrity": "sha512-Apg0sCTovsSA+pEaI8efnA44b9x4X/7z4P8vsWMiN8rSUaM4y4+Shl5NMWnMl6njvt96+CEb6jwpXAKYAVCSQA==", "signatures": [{"sig": "MEQCIBBsKFDJXx1Z/jcKb9ouu71tra/HuEJYTe96IBt6Yk0ZAiBuYzx+inZjgeyszp3pjkS7xAmgVMl78K1C6EVzuC/8hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+snyCRA9TVsSAnZWagAA/14P/3Gcjzq0i13f1aKbydQS\nN/aSfXsnbkwA6nl7KVfK45CppPfY321UfbsbDFAbpMc12KHllnPm9ESOlfSf\ngjyMNcohGo7M0zrYCNCt3qdYHQHS0GGzYwiY4+53we/7F9n2SL2UxImVj0l8\nt6pU8KEty4t4fdXvfz3CN7TSGAJDtyAJ8TzHcFuEn63g2qlmmFU7S2F43i80\nk6EUsmhEKU1OHlTs9Y6Cndp/EYBIUW/djojKNJh43ZTVIE3Yvqx1pTBnd1CP\n7I7eAh1C6EQ4EI+Szh8sQ0K/JmcsW5qH3k+0CvChRSu2Ii61ZEyxfh5MRv16\nkRBfs6Ca0sp/DEaS7qyKQ8WF/tn5GvZvmRDfYEAGOAu5w4lY5bFpI7S4qkPN\nX0RFZVwCGUN74jdbT04EFUMNgJevM8cHXOaRgTYQoWtXNOfPjua4qebjLztp\nGGxar+kg4/RyLbaPReADU6ZOl0mt0BiOKeGBOrBxFd/rkUmAcRl/ztEiuBDI\nkfH7/1No7ANJyuiNwNpOtb6dbr4LpdxfDMPLB3sp3mI+3Sa39bL+mWGwPJ9R\nNsINCTObCga6ENWxF11ChfLKYmgmUgHfDIU5a+kfZHt1TgaFNpHyPizQCK5j\nKivcm7NR+dEWfBiWKgzHT15LAGsjTbqSaibWPFEGNkbmuFbFSjJhDGTOsJ+B\nzVUH\r\n=vYzY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "12da0941c898987ae30045a9da90ed5bf58ecaf9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.4.0+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.7", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.7.7_1576716786509_0.04001177308412607", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/preset-typescript", "version": "7.8.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f3bcb241e530e5acd424659e641189f06401a7ad", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-mvu4OmrLK6qRPiXlOkE4yOeOszHzk9itwe6aiMN0RL9Bc5uAwAotVTy4kKl17evLMd1WsvWT1O3mZltynuqxXg==", "signatures": [{"sig": "MEUCIQDeWF9svVtCDUveLBEmL83+JX8n52F/lmA3DQbVbSkESgIgU7Z4/fakeLcnFWiWMiyzgbhcpGpkgvHGvnbjstmA6Kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWpCRA9TVsSAnZWagAAOhsP/i1brXHGSkiTec/aMYNF\nPOGQZpn9W1t0Wgml6sRG8bKItle4+F5DrdvNbAqm2XfN8q4fenGewOsB24v8\nzeOEU/NWJrW9xulUksx3O/Ccaq+pI1/paBWTBByBB9Rp4xxS6tm9/MlcLHVQ\nOYqEISLiCC43xp4EF+0ZpfX/0H8hLTHO3sKkPABWyDKMPV0bGfoDKqGXSonv\nL0pM8NmSpG//pMmXaEDLxJEzXri3NxYqsCESAOt8C48SQb4kdwNJVZqGsIv9\nhlzFQSMWLmELPFSFvZo9oVNW2DCjRv9Z8zS8AAUVcW0aOdK8GCaGLeZGoxOy\nSRZydZ0GyBzU/tJ8QZbtj7KZ/0TuzjqW06JwmmGPGjUGJFBGfsFxn+uG9lkc\nMLiWnajRrod36G08kvNT4HLjY15itZsC9gv61AD5wNNADtbROdTP5h1C0SHo\n5N4uYBcXd4WhIj+NAqimCQhGAwTGiqim1MFUpczaHk4xy7NPRzM087MclLDl\nZrexGqICrWCfWovYVYqxFqEJph1qQ49eCjEg8PWjmUr+d+DAVUmiznxZqo5U\nC7NpTEO3NPwslEKSiqlLKTOoXx9w2ejBKjzyMm65ZyCAIIMn/pC/yggR/jn3\nDNoLq8w0NKK0IeOhHjfk2sWc6PwT7wZ7D6gqdC1RGT4zmqpuFz4ZvrAPh2HL\naQas\r\n=8u8c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/plugin-transform-typescript": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.8.0_1578788265405_0.538370387685978", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/preset-typescript", "version": "7.8.3", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "90af8690121beecd9a75d0cc26c6be39d1595d13", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-qee5LgPGui9zQ0jR1TeU5/fP9L+ovoArklEqY12ek8P/wV5ZeM/VYSQYwICeoT6FfpJTekG9Ilay5PhwsOpMHA==", "signatures": [{"sig": "MEQCIFS9sVAnOPOqaybrKO+NYbidmA3rm3He7nB+T62ajfk4AiAiX8kCVbfx/no5jOgl3oJoPJbtPuV2RR5pan+G+AMBwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORcCRA9TVsSAnZWagAATjMP/ROqOn1b2gJr/hiiMa3P\n56C/5Ij5vJF4/xyqWcpjPE34Rbc2QlZBlil8b3v94JAv0dRhBMsty+X5qvS0\nX9NvdgkpBoFO+28Z2cbT7NxAopvW0FNWdj61sKVTEvdmO1qyZH4YPu57PFrm\nVMnFQ4IUlQYX2TCbqSkWbQKP1s229AMHE5Zhi+oaDdBAqmhxkCY3UQndN0cX\njpoJySU8oFeIvvTk4urLH3pb+DgOjQu2I+QEOPxeAbc7IjgPIJUPx6AEtzSv\nzLQ5aqV4SKT1qp64LM+iGWfu7zYQjJh7n3MYxsRP/tzYAerd32sipIQ4dcDX\nZ6uJ2+pX8LI8W/hij8LeeCaUaKD0eBcmLrRIzdW0HYRTaN/z7BroqM3a5bmR\nhfW7uV0MG+MZ8vdyrLtLMlQhhDa8i3rUAjIL+IKarGEVNEUtY71kAhuHAH6M\nxSTwEGARdxGiyShOMEXKJLTMhoqrNUWyM/Q/UK9jETI8gPeVqqiv4+PCqbFP\n67aDJtOl23P+BSwHpqaK9YYqZDMLnj6tmkirvbVirnBO/F9G5H9gqa6NquoK\ncYywZamTLDrEU0v5dbuw2T+wAt9U52R0zSA4QS6jzGY6i+dus4RVKycZWnLT\nTMNQumlQQKiWMLUfOih9oCExawiaOvNUBEaroneZx+wsxGMIu+xZbWLCDovE\nC+z+\r\n=Ax2q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-typescript": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.8.3_1578951771937_0.4422081764724508", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/preset-typescript", "version": "7.9.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "87705a72b1f0d59df21c179f7c3d2ef4b16ce192", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-S4cueFnGrIbvYJgwsVFKdvOmpiL0XGw9MFW9D0vgRys5g36PBhZRL8NX8Gr2akz8XRtzq6HuDXPD/1nniagNUg==", "signatures": [{"sig": "MEQCIHRPnWEgfnqLGLqJ5aVHEEHrTw4YzXYLTyJZr7ZgVL5oAiAVzlyr6aTPeGhr3jJLtqAth1cJdHkpyUr8VGy/eKDYnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPFCRA9TVsSAnZWagAABHkP/0ZiY8aSYekyMQU7fHY1\nmNFGzSWTY5nnvW2Vl2AP0N0HAfWPjDsASkX5aoVLT2E9p9d3nzjArjA/CLM7\n40IrCZso4fs8lAvmjQijB3Q2rTAPeTxeNehfUFlNWX85qZbG2W+yjDSDSxx2\nBkR3goo7M0fNjKr4iPX0y/wKsbOG75S6eiLNvdfonNON42pRlemuIR9W7fzj\nzK2AydZ0F6Owj4bYe+UyEX4JQtdo5p9yVqEpbK+ku8aoAUufg1AGBFZNGXnq\nyg8XBGDlkuahq5n3FhsrirwivuuqCf0hfNpI3uPM8bDHYu77fUhbBwbrvhlQ\nSu7KIcn05eNdt0x8Eq5swxwTecm2XvE31nLC2iq3qiaQJ8wRJRYrTiwde28J\nAowfMJDAbdBXci/vMBIUN+vG+/WuR4VDwL+q5d8o0//+5YYSg6t2HpjmJdrB\n2E0xGUnf/xi4SSBbijqIJQ9N5PW1MEJOSmcSmoOI8vLB6R9czpxSt+zRZSa0\niO5x7UXcm0shstoinjl7EdYGWByxOMMK/oobDqEWFixXwPnD8pDTX0x1W+rJ\nao8YTPWGYE40LH3n/eWOGjeKIH5f5U3nGAhIGp0G7kF6lrMK0SmTkYR+Nxic\n3pnYzW08GwUdJ3xz9lJSqAl0HBvXMH+1C3Q9qiy/Ch6jEtVBES2/TKskSB2E\nEMue\r\n=/9t2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-typescript": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.9.0_1584718789179_0.642135633019596", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/preset-typescript", "version": "7.10.1", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a8d8d9035f55b7d99a2461a0bdc506582914d07e", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-m6GV3y1ShiqxnyQj10600ZVOFrSSAa8HQ3qIUk2r+gcGtHTIRw0dJnFLt1WNXpKjtVw7yw1DAPU/6ma2ZvgJuA==", "signatures": [{"sig": "MEQCIGlfQ2rsjf12FXKnvx0R5zUzS6gwXcMAooI7wkjRPKymAiAkL28BAUVzojRuk09qTxC/uHg3tuot7t7iFKY5LXzSyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTqCRA9TVsSAnZWagAA5ScP/340Eq1dfO13rtsgpvyU\nfDzeF78yI3dG2R+uwnbK/yPZHKo6wg3NexuKsN8ilLfNgE3GZ1vTkRqBzf1P\nzyJE8pdsg5ssZRqH6K8t5QF0qcJ8daJS0IvowAp2Mt6gZSvJsp5HWyaP0hwd\nInq31yeU3mwQcq8apFaTiR5k0G1GPU8T7ldun4MlAerl6AyZOOMgsEr4SdYG\nDRSPDtZcZWp3gTH7iW2H3mJuVcfYvcVOI6DytRnRatthZNH8tNbSzswFe2SH\nodcy+ympGrLtoyGHtHJZXtJjlTkGzQb+eZM2EQyDbiceqbVERegtf/zuE5Pm\numUd1u48kEpBAyqtf5NSKkDj64/IW35DNQ2gEVZbnjj/wAbaA9yoEhRwtgHf\n/v3t5YH6XZz+p9lsszl6N/vH0hws38xjRiG5DMYJXAeQSMxLdGE3tl7nt1iX\nHE04XV9WpEWC+oLtTi7grNAO5R9DKlY5+hLr99f15/GB2+xbJqD+FRIsfpUQ\nRWiLXc3Gqa3LYUS2ftQQ5NoutMjOeVq1DT5P8+PXAVVbnys1zslvVHEAAYaS\nThqUsmPVYSR0GM/bETkjeumDWmUbMnzPmk9Z1RDuW5CX3p2C3aAd2v39Am3i\n6muNqwKl63AjdRDRgJB5QbIanD58daSrltKtNsVU0DDb1NYN6HTar7djQlq+\nudyf\r\n=R0Vy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.10.1_1590617321998_0.5237821644615872", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/preset-typescript", "version": "7.10.4", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "7d5d052e52a682480d6e2cc5aa31be61c8c25e36", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-SdYnvGPv+bLlwkF2VkJnaX/ni1sMNetcGI1+nThF1gyv6Ph8Qucc4ZZAjM5yZcE/AKRXIOTZz7eSRDWOEjPyRQ==", "signatures": [{"sig": "MEUCIFSxcOQP5/mkxDP3d6QUjiMehQMZ37faAa+QOutAUEy2AiEAoPFcjTzgxRlRrD1hXjjrduz9HDg4M7zBNztyTHGIrKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zqGCRA9TVsSAnZWagAA6D0QAIOJ8jwGXdvS+VerFD9f\n4FAWhX3JiHA4j+9SYL6h/0vBD+Q++d8mmDrvQGECMRnHKf7C+2w+KIkslbIf\n0s66mz8/isXoc410Pmm2r/f7koQAywgJQyy8iIwm8Wq1mYWgXqoPPF/jbk2R\nb/eVZ0llhfqbg5Z/f7rV7r3irer3Pc4suMYSOM6n0/f5bTt3PTYmH49ZBHIr\n7lHZp+ViUVKtjk9hLvLcv4sKns2Y5prDx/kfVdJkI4gj+jeWGRA9k6QyrsjA\nzwQ4UUpj2arCzHJHn+BFYAM+YDJH6zP/csum510vsXZeIt3KQwzczca8KqB9\nAkAWcJCuAiH/LiGmt6r5dNPDDncVTYXFL9twGiviSChoqJH0lVCQB9FDOV+t\nSAu9mWTlLbMH1xYfMxO1Okje2wuHTEFZkRQhIs9k6Qz0/fEEInMSITLbjyX3\nDbWtLNqrYbar8z5qT46F/dpeAHVgTZEja0o20uS6Hr1FYG2hu9W84bZsOdyS\nLLl1UMhET4KOgAxHTJiqgJiTpSv44R8Q/Dlr/0FdlVigjd6+kVMTnLUa0Tqb\nW74GxJJ1oRTop7YhSy+YdKepepE61vRNUEpVTbGYB6D2u2/c9k8q8HzGkFUX\n4jhUNt8Sl7pFc0Ex/PJ3hwToz5ZmrOXu+CJFT1ueuiMnVY4jhgdouDptpNGP\nalvV\r\n=5IeH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel preset for TypeScript.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.10.4_1593522822414_0.8702709810945155", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/preset-typescript", "version": "7.12.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "3fe6b4958ca6fb0e086dc5574a27d0ced99185e8", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.12.0.tgz", "fileCount": 4, "integrity": "sha512-2XVy4sy/zkP4gqmXW0TzSh/QwOniN2Cy3srhsD0TRBlMTOmjaYnWCWA6aWopwpcwfYkEKD6jKLLjYMq15zDNWg==", "signatures": [{"sig": "MEUCIBGluGwtT3HzytrliCMiOceen3Ax+IX8LrHwN4mEAi4uAiEAgSrWc0D5M1DeckKcKeh8hEG2nC3w9+S1uds+P/laNXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1maCRA9TVsSAnZWagAAmcEQAI+iLJQ/BppgeagcYw5F\n2vGT2CYqoSpmQl05xZFrjD3zx8ad2jDAZyL4F4FgWZ68fg/F4lAY2PAOBIwT\ns4kYj01KBe6czsMJFVNXAu55RjxdfomOPvtQumfbu1dn76Y4RB18KPpb6aPT\niAS9xO+bQB9Pnf2U3FffECBhi555Y70d5t+uqhtm12hUeCpQfaAV3A2WWcnD\nX0ox1DE4r0MF66e/FzDx0pwgQ0rFSnZb48XvHEbdn27drqGkWHyw0sC6AZJi\nhJW45LxlDOEktME+2OnHHpa8OKnMecCjTmfMuowVxMFCIHwMBl0eGRA/0tPi\nyXAlrb2lkKOXoSFvHn4FujdNKjIsBocLOziPPswt+Q0kQs/7X6lVQgpyaX08\nVLTqbXRgaVPXciHQQidcbF3cmpaAIW0FiCoJIm9rQ1vmshH2cDC94gLQOb/x\nAXsN23/g18E5Syytl6zWgbZTVEt8RlAaPbZ9sZ3dPUyNJ+OCZbniFjZF3Aqc\nO0VrdmHgpHML0x3YuOE4i8ZOR2CzUhenrZ/5vbhZNL7tIUX6602x8j9QKqre\nURjVW8554/s1lIh+jie/ed9yUMxUJoPrxPDPmj6+qJRtL1t0dmtdmYr5hzDQ\nl/leE0c2mSBhdO2yr9Tm1lovVUQ02UFl8LGTNa3uraziS+ZBa1rZ6lSZKUcR\n2zJ+\r\n=n7WQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.12.0_1602705817821_0.8264422405008944", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/preset-typescript", "version": "7.12.1", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "86480b483bb97f75036e8864fe404cc782cc311b", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-hNK/DhmoJPsksdHuI/RVrcEws7GN5eamhi28JkO52MqIxU8Z0QpmiSOQxZHWOHV7I3P4UjHV97ay4TcamMA6Kw==", "signatures": [{"sig": "MEUCIQDrpCXxtygppdPT59CdtcE35BciNKTzeG2vwqF6agOFZAIgfBK04/WZ7GaX6Rs8znJS1fEFj4aCSiOIRTj0NkeD0ns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNBKCRA9TVsSAnZWagAAigYP/jQc1iPAgcbKmVpXlRQC\npGPUEgAYW0ZZPvoydTHl4+q+ngUYzdQj4jwZfLeUyj8OyflssqbbExjSyhCP\nOy1Bqf6NTG0PXnMxwNCkW6j2GFimmSS0+y/qQLKwDF+WI6vNK1pudTnjm8RP\nM2ZMlMDO5hpVaW6J4fo5bJxBlcaTQ/N2C4/4oDQp3EZj58JSkIHVhRsG+cRZ\nSoyQk0snHcdkG1SBhbMg2aOeRdDD145iH80EAqWE9+URkPSploucODwEr5xN\nta9Axtzl7dQWT+q/O6BFUc5Jgo50JU64j6qeGhAk+GQ/74sSNiNgiIKg3YD/\ncZ8OB7fXHQF9drOYz+yphOKN1x2AVbdWtbhNtFeJd1gGj8EAMCD/TXc9h2iW\nmINIFyD6S4sFP1cxRAEBDjoA64/fjvJ5kA9/6HTipRoxoTJZHzBAwAVpPEx7\nSsDQbluGq8w1AKWxVGPO+PRHihah8xxfBZwf2/p3/u2qhyPrFJ96v4xkcnmb\nxQZHK810CB+lWl//pTAgE/vg7zJ6Lj6x3HDZBozRMsr5/sHZtocMBS1y1SF7\nCsNbj9XZqhOMxgGgcVo9rTBXYfpFrGULg0DBhZ0kDlCdGt8TKgvi3sIRxs1E\n2k6j76kOK4elOCqcgHZpyUIFnleUOtqInxHbwtHc7sjZNPbESKhv0hpP5yaN\nxXOY\r\n=d59E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.12.1_1602801738307_0.9789432890667651", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/preset-typescript", "version": "7.12.7", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "fc7df8199d6aae747896f1e6c61fc872056632a3", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.12.7.tgz", "fileCount": 4, "integrity": "sha512-nOoIqIqBmHBSEgBXWR4Dv/XBehtIFcw9PqZw6rFYuKrzsZmOQm3PR5siLBnKZFEsDb03IegG8nSjU/iXXXYRmw==", "signatures": [{"sig": "MEUCIQDKpeILwW99s/ysl5zu/xEhMq+L281cKKKzMmVYGIr2xAIgZ3+sx/LCDAFX5l2ZdDB+2wC8w+mcZT7MiHnQCw5vP8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3959, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+mCRA9TVsSAnZWagAARp8P/1B2WVr2lDxdKvissr9o\n2WEUAH5HZuJPLq/59XJJAkQL/OyQxcb9gOzX+lHlMLhCweFzDnZlhhR0jG/r\nsR9ej5foJZuMXyNV3LIyr/XbP4fWOpVBEEWu6aqb9On2mP3kcJp86WXUyKKP\nWGaqbbfmrimaJPNri7syeaQdCA/ijY9qUpSofxYRdGJYIz23VL6kPr9VG+un\niLWev3GPB3D1AjzrKzEMwAxHZXucZ1lXWSk5J34ve0SLrZpbti2XAYsU+z29\nCS+sDI95yAsWE9cMC03cmGnBbAQRdy34MMbRWsJ/4RmamV9EoVBzG3Z3+9yR\nhAVQkCRySDUEQCw1bDfWGqNaqPmMMTzvk82Uv1VK9/k7FVZpZJv+VPmLQB2z\nRvoqpikKt2qgDDXeV6u8PXbmMjtkIchcW/W4jJwzoTU6nRxDOvf4ygZPmQts\nV6byLOJ8ohfcP7wIS6gw1dwxg9Z2Blo7zdF0TkmckRZlxl+U2i7Ii+NSg9Y3\nE826HzKtUm/ffHvXIyV4EwFozLoAYrslvDaopiQ+LqxlSGfBPFJZe7LHjrLp\nnGFivt/YhMJtfTgdeQnFCx9UcW4abi+QvRMYbLPQC/YMuxgvIXbqzSMpT9tJ\ndtsVo9PadI55Hh51Ud5QETD2mKLMp9RPlsZo7mSuv7tvKVWi+JyMDgwra76u\nCYll\r\n=8T1p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-validator-option": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.7", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.12.7_1605906342548_0.16027846932673606", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/preset-typescript", "version": "7.12.13", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "c859c7c075c531d2cc34c2516b214e5d884efe5c", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.12.13.tgz", "fileCount": 5, "integrity": "sha512-gYry7CeXwD2wtw5qHzrtzKaShEhOfTmKb4i0ZxeYBcBosN5VuAudsNbjX7Oj5EAfQ3K4s4HsVMQRRcqGsPvs2A==", "signatures": [{"sig": "MEUCIGbTmzD6HOT2T8KFF4tt9YxFF1ldRJnzHfl0R4DJkLemAiEAlaMgueZ1uUkFSDIbFJ9yrgJ/lqDkNF3/4bg3vW55A6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfh3CRA9TVsSAnZWagAATQgP/iLSX/sy8j9V6YKxgWiT\nJCOqkA25H39Kp2lQZy6LgowE3qtEphcvrjtAJaWM6Rb8cIOvzcrfPWti2LL2\nRqETwHE22zbg5rClFnPbqH3k3WawFQyB2SVbJ4MpZ22EzFitdGQ0u8s0sSMu\n/lAJ60q8B0qYmLCWnWV3g2ejB1sKiuC4xXn+5v6WroyaOKMkk3UVBJNaC8QF\nPUZ5pyzl0aySGgqIgR+D/CeMs7dccZXcv6e9xCoR0QCiy4mvaIHQNt7kASAb\na+0vMg5bvqTREi/XCaPz2G96ih+hJix0gGebI8WtGB5XC7utAAmRLCDNBI+X\na91+0j4U5UtgFyaRRDjt/qoSjTepbVc/OKvbyO5y+RyJ13rIXHuFzafgabtZ\n0kIIW+hc+EynMvff2BcGERXIQzZSuYfcqC6sGXUhgO1zyPdzjl3VNkNZ+kKo\nIKobHxMUb56n1xHlwXMNu3JKy/Fu3b1NDthwByM/lQ/sbUw6kl41SERo78iP\n6Y8noELVu3lToHRER8Q82jm89D11qR5ViLHjRwzRD82PVAWQKWtLkd/jkyKK\nMHTTlW7uHKGX7PX17c/zLs3aHgAm5vclMi/lULKQmvU+1/Z4ZzZnRhSgz3ms\nQ/jysv9alDIsPICJ/kqRrtDF9S9OAy1Ei7SbfA5QDcbaQPRBxSNzMaljpnoR\n2LHG\r\n=KxyD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-validator-option": "^7.12.11", "@babel/plugin-transform-typescript": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.12.13_1612314742649_0.8758887544685747", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/preset-typescript", "version": "7.12.16", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "b2080ce20b7095c049db2a0410f1e39bc892f7ca", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.12.16.tgz", "fileCount": 4, "integrity": "sha512-IrYNrpDSuQfNHeqh7gsJsO35xTGyAyGkI1VxOpBEADFtxCqZ77a1RHbJqM3YJhroj7qMkNMkNtcw0lqeZUrzow==", "signatures": [{"sig": "MEQCIGl0LuFBXkTlkbzc5P4PMosy6Ngqn2HRiyAMn1QK+4zgAiByY1AEfnyPue63E+tgB4EvOxBmdyPQvgTyfIqnDooTiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbP2CRA9TVsSAnZWagAAqp0P/iW3DFo0mbLzf4UI2LIz\nypDujSiMfUsWXOZUIr9qglaTChwLs4G/IGDy97NMi4lnjun17MbOadk56D2/\neWADhoMZKmLX2+EXEUh9Gz/RM6AgrlKGvLy2XQGMBhHWIkYA66qXVgtQF8XT\nEZBZlellurn0ula1V8YD6L3/1YnY4nIasrvsR2+9KBqsX5CMRXYt6TLSKFTU\n7pW3uMIdCda2xixznJqFpPpduCjlA9puyzy7UUlV2sCp0gvxm1y+wZEfQLs7\nIRqHFB4v3+SBOZoeSqkr5m4KnG0lBLzHjJalM0CWi5E4JNGHVpK1A5UTffQ4\nS7dMC4N+6peDaRP2/vZ0eVy6gNysLLogLkfAtcXoEnuq+SA2p113T//hQBNb\nqMhmkV3vtjKBMo5ybiqBGQr9yXZH7TIABMc7zMOddj95fk7/xNDz0ajff0FT\nAd+t4/A97KeAm00auD3ZICeI+faz3wXQWvcWMX2xK251vlYa8th/gDkkiq0K\nugm5QJQBihYpjHNgVEkxln5kHjTGYDI1GtCbddEMv2KlJ+WxGAJjeLANpW1S\nyExJkeYybb3i/llP7//U2bKnZA9jS76tZ/y9XSX+lNaLJs8dXM0BsiBkegeV\njMKZ3y3x5x/SP4nkW/kxOBm7UHczxNpQ2z4KxMj2xzwqyQ/mt1E3UHjfbmsV\n5Vzh\r\n=syif\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-validator-option": "^7.12.16", "@babel/plugin-transform-typescript": "^7.12.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.12.16_1613083637804_0.8259833651476651", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/preset-typescript", "version": "7.12.17", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "8ecf04618956c268359dd9feab775dc14a666eb5", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.12.17.tgz", "fileCount": 5, "integrity": "sha512-T513uT4VSThRcmWeqcLkITKJ1oGQho9wfWuhQm10paClQkp1qyd0Wf8mvC8Se7UYssMyRSj4tZYpVTkCmAK/mA==", "signatures": [{"sig": "MEUCIQDIaO6Ys/F+VD1kKImjbivdk366PBPhqh5r8yHsgXgwFAIgAda2SAAKWhclcmQwIofqQ+gbgadMcuNwpLkOIxqkG2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoQ1CRA9TVsSAnZWagAA54YP/RgpHAo32Jnve3sr9/PT\nJQjax0auXmaVIKWNaLn8s578BYpRc7zsAoCv3tj57kWuom4//RGO6QFpSUug\nqb3JtaGfBNZsP9stKsVvr34eOZ5lhk+AZFAfJpMlHBYSa+5SC0AAZcludmYs\naryi5bFNUuNIRD+ZNX3DiAdw2vwKQhzezGXd4IMTrumjZ4yfbaQ9um+wuusM\nF0aj0x2jiYXXCkTFeDRCo8i7C8d7njg42OjwmB4Gtk8i4kxmbZIgaCiKkUyy\nk0yoE7sn77JnOhyva/Bq5Ltz0Wl0RpPDbsxzak5MaerRvy4uAERITNpsWp9G\nQsr4ZsPPln8kYIzuEyA/bPeSAUKK6I+zFVaoiYJ7HCm/Xz1xkk44U5ST4Rjo\nIDV2DwEc65xXq7SMlFw6y4+NU11vXTz2te3HEeaKw/f1gSFKeyxW6skhNMaq\nh82IcnT0MhYMeRYTJqOhXBiH0Ykx+yi8BdmxyEP06AQarQe4yUR27xU1WjEU\n6gZ/HNClRBoemXKM9s1gcHGjw2IlyWq7yKAiKWhjl7e3utprBAUEtz5dypAO\n6AJu+iFKASpxhZvMIpJ3I9grVxzfKo8F0HcHWJ4V6zhZbtrbuHYG83dW4vnj\nMZlgLcKOzcZXo0kue3uU+5EunXgafyTyKATT6cZdi7Dm/HoQ5+VS+CAtGyUV\np1AR\r\n=MWqQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-validator-option": "^7.12.17", "@babel/plugin-transform-typescript": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.12.17_1613661235454_0.3120719444726794", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/preset-typescript", "version": "7.13.0", "keywords": ["babel-preset", "typescript"], "license": "MIT", "_id": "@babel/preset-typescript@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "ab107e5f050609d806fbb039bec553b33462c60a", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.13.0.tgz", "fileCount": 5, "integrity": "sha512-LXJwxrHy0N3f6gIJlYbLta1D9BDtHpQeqwzM0LIfjDlr6UE/D5Mc7W4iDiQzaE+ks0sTjT26ArcHWnJVt0QiHw==", "signatures": [{"sig": "MEUCIFNbikkTiCWhL8RkZbIQYpto45hgV7rrVK5gWHQWFCriAiEAxKshwjMuT4XZdh36GXLBwCiEat1YHPOnQP6cJnBZh14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDU+CRA9TVsSAnZWagAATzkP+gIG5885Q4euZf+S0odE\nw3Ri0Dfg4g+WjqAgy4mR2DMVdUy7pS3Y7d+X3TCO1iBNGhLVdFIxcmhFxva0\nTTodg2BVfiOWq/EvmHBKAZxn+iN5zk4jTVoIw0G+PwcDFw86o09P/KiItdvm\nHV5wxWfAtSHvjbe6nJDcuQA6Z1xg5hhEAga4MHY20ESf95OD5a+p1UDkPpBj\nBArN3LeQgkU1C+UkLTFqEC/8Qxi03TMOetLOf1ruI7MwZvLeuz2v5XJpmXqS\nFqJZWX8at+B92atnadldPDQv5opKzV4WXVK59swoujRK9iX+wmwhQT9AEWD1\nwmgUL+spRVS8mpLHnfVA33O0IdoYNwKlizjvNKS6yViv3pahD1cBybsRyAHQ\nWyXdTrgLDQTBMH7BUm8aSJFJUvnvzSl2SZhGHAIev64agqYnQLpuC37XnvkO\n7CyyHeRbuka3L1WVoJrPsYPLUgb70tRq0r+Nc4Hf++SRGWs+WxZ0PJKXitfo\n/bgW1BV4TEuLmIptDG8zbi938p3J+LPuf2EyRqJEEqCleeAcIAA6xPlu79sI\noD4VVPrG98tdCTlDspR1TdHAINh+YYkFbQ34FHSUOVPVUxss9ng4NIJmHKf/\np1BBkzkcd03n1F1deZ28HwFwfxyHzg1iQNzSKuUd++yW4OYzxUT3GfT6nkk9\n5JRF\r\n=RmKV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-validator-option": "^7.12.17", "@babel/plugin-transform-typescript": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.13.0_1614034237739_0.8330901389307077", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/preset-typescript", "version": "7.14.5", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "aa98de119cf9852b79511f19e7f44a2d379bcce0", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.14.5.tgz", "fileCount": 5, "integrity": "sha512-u4zO6CdbRKbS9TypMqrlGH7sd2TAJppZwn3c/ZRLeO/wGsbddxgbPDUZVNrie3JWYLQ9vpineKlsrWFvO6Pwkw==", "signatures": [{"sig": "MEUCIBQdoAGHyQl10IwjSjHGmvEYoW3sk48FpsBA2pF0MEbFAiEA+Wlsmx8/8r8ODeWlLCKAFI6rloYjeJ6QEj0UcQxPe6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsZCRA9TVsSAnZWagAAU0oQAJq5ju83gkvqIxm9+QS3\nwICUUbWRloVj24u7JojF6Ft6SwHa2kZzSTnl1pj7bux+PaABZYZ78V3S6+Jf\n3RGryXZSm5cf+JiEnLffSpqex0YW1E68VNGYqxSRk267vhYDFPX6pOQfOVCu\ntwk3duY7XmqUppkw9Si14y4AXr+tHf+N25UFhp9P3H8zcSPosIr6ATP/X646\n0EHlplLZmd0D7n0zm0bRVcy1s5+0INj6k/7YtolhT54IFanQpdi4rpgG+2VN\nS98xMX5VdLR8EL5IO8EfwmT/ig8S52dEH6zBEHszwCSbjifCBlazjRAJH12g\nK6Rr03HUQo7facHaFPw4QuX/AaO3sRu1KSSbI58KV8tmRNgzR7a7VJqHqjal\nEd/t+IBvrDJfp8ySdTf/UuyVrWHszuobGXY5N9/+qYUeTD9x4xr9eWE2zu70\nYfRV1P1Jk06L8n85Fjkv4BwjXvP7kCTygK2t/JfPx5eXGuVNFqgn/PIJP6Wi\nx7uAWeLJ6ZOK8DjAcyHdvKQCCVPhE7LBVdN4Nx2t2i2EjNc0nl0XQS4JDqM1\nCIskKfRrweSA3gaYlHSRM55l9oE2pTytOwVLXmz9iljO+FMIPiwA+4JsYRK4\nmwn4OR7IUA5/qA2pSjVS1w2H8V9cnhq0Rr6zoigAZdUsqlMd+wZQFLX3HfE7\nC+zp\r\n=mxBg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.14.5_1623280409052_0.05052902749423538", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/preset-typescript", "version": "7.15.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "e8fca638a1a0f64f14e1119f7fe4500277840945", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.15.0.tgz", "fileCount": 5, "integrity": "sha512-lt0Y/8V3y06Wq/8H/u0WakrqciZ7Fz7mwPDHWUJAXlABL5hiUG42BNlRXiELNjeWjO5rWmnNKlx+yzJvxezHow==", "signatures": [{"sig": "MEUCIQDNax1mhR6V74fu69DUare9sHTqB+3BMWWyJTOiH5RE0AIgGqRZAFCKxS/pLb7OzrkxtZpaz4ijRrGO1ap6RP/pxTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLuCRA9TVsSAnZWagAAm2oQAJRTnE+J70DDNRl4Vqih\nD7G7S0kMsms3eLojOztnCepNIHChI48xzVzZmo6aWhJvx0OfdTJG8eXTmysW\nDF8YBmBCTjhMlqpdMoD/2wwsZghGwAaU2LoxGmLMkZooz4HSc8ZlFWtFjFPj\n8/eCM/kEjNDU7PqG2UZaxFG4g1eWeFn9UBid9cREkm5NcebLpCg8LAtvjXiN\nuaiqA1yalzkSJyLZ9BC5Z2wntlEJG0rNYzAfL0q+eTQFY8rlAwJn4yQbHDnt\nmB9aO9n8CKTpaaG7AMkduD1eTLoK9yD80CuM5m12bdT6ORin6nbjl8+FVQOp\ncqvrHJsofWMyoJpVNitbrP9LB3VG8FUkdMvlJUHCIcbFVTzChOuYPSajiVVA\naQfJjEa9cRoetCvfwuZcK/TsyV2/4mIY8D5j6v6HFFY86/idufGtcvq4ES7Q\nFtcYmu6+ctmwoxmFcqvcgksvZxQVSUUowltvox4em/0QSKswcYrW8O/31jV8\nigGBQKsk6eSSKJYKM0Kj/IaD84ttdItEZlwyQk9swykzCnXqbD/W62KRF0aO\nxsutNp+f8A9zp5Ce6UFbc1LoQEfoTGStnL8h3smR0yQCzhySizm/Xhugvqb3\notCOne8hjFv9hIxK5f2h0kcXDw7RI8ySgh42s+Z59764a0a79plSzPh7xdNG\nB0yx\r\n=3lpm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.15.0_1628111598183_0.7116029171714056", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/preset-typescript", "version": "7.16.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "b0b4f105b855fb3d631ec036cdc9d1ffd1fa5eac", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-txegdrZYgO9DlPbv+9QOVpMnKbOtezsLHWsnsRF4AjbSIsVaujrq1qg8HK0mxQpWv0jnejt0yEoW1uWpvbrDTg==", "signatures": [{"sig": "MEYCIQDEwIhiBwagOrZV+tlG1wlIE2IVt+/5BMmlaX4wTIBMjQIhALJQEyObJyGzUjMemADdIBnQS+DPd3ak9+7WzDmfDi16", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13628}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.16.0_1635551282593_0.5269271714156214", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/preset-typescript", "version": "7.16.5", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "b86a5b0ae739ba741347d2f58c52f52e63cf1ba1", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.16.5.tgz", "fileCount": 5, "integrity": "sha512-lmAWRoJ9iOSvs3DqOndQpj8XqXkzaiQs50VG/zESiI9D3eoZhGriU675xNCr0UwvsuXrhMAGvyk1w+EVWF3u8Q==", "signatures": [{"sig": "MEUCIAVgCQnWNc7cHLMhFDPt9ayLJeh931PJXYZLDUX1zkY4AiEAqSCNOt9k4it4YJrXWCSxO8lmXg4VlOZiOrVbQ1yXphY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kUCRA9TVsSAnZWagAAbz8P/1Zc3n08mkt41qpE8oia\nYDqKxb5hZkaASbtaV9xENhT/0pIJwzv58t0G22v7qbxgrdb+nSHjY3JAY5w6\nq+46EHSu3UPC7VevQJan+fbpkIlABpPwMXWWWwIBHHPbOR23NvTPGiHBfoE1\nXx4XwAoBqdflg799X2JTQaekFRDXLJ45Vp+v2stU/TRbzeWV/4zJ2Ycmcky5\n/xcQgTpj3fdk2sRq6A1YpZav+mpZBDHOq0y8Q1ivjQ4OZ1JKZ1inOZSIc45k\nt/WFxLdD4fKpZNpG08UGhAyNUY/vcMOaZRFW6MMS9ruDCzy/Hsa0G5idsN2o\nfXXLWoTX2e6sYs/GnVJ1IExCEwWVEeYiqpRs+KIJmKUEjJbS/4LXKorSDGbD\nX+HWJqrkzIpAjhpjiCZmb97M4mJVVT4z6o3ZC27EU4JfEkehk8kS2UNsND+2\nI0LhtwjJAtYBhgJJ22it5whZkeeSN4HmjxsIpvM/Og5tegArERMNqSNiVyGj\n021RQdCmi6Vcw1X9YN4zqeMKLvpGkm6F3m9mAbAcsHhApXBn+ehMmTCoWmMd\njunfLtcr9Cw+90ioLeznZ9XtnT4ALSFvA07Oh+sb8s9qFvUnbDb4PaPVkKBs\nBpgQVWr8KuvBCqbDGjn/toaB2E3QHx3IrFNuV6w5R8pmh2/fZdFudzdYxEaE\n1N0P\r\n=u8hU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.16.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.16.5_1639434516306_0.3434602422305786", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/preset-typescript", "version": "7.16.7", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "ab114d68bb2020afc069cd51b37ff98a046a70b9", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-WbVEmgXdIyvzB77AQjGBEyYPZx+8tTsO50XtfozQrkW8QB2rLJpH2lgx0TRw5EJrBxOZQ+wCcyPVQvS8tjEHpQ==", "signatures": [{"sig": "MEYCIQDBvk28Z42qIt87aN7dXvpsBRfeoN30ZXu0xidDX5Q93gIhAPeDQOG8mE1DrfW9Y6dmH1brkDZonn8zYN65dWpcs18r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk13CRA9TVsSAnZWagAAUnUQAIx4zo5XVlMFZXqZ2mRA\nWPiDa4bWQugs1CeYgCbbtGNZdwE1fahz/zKyrCVwJONvLCdmejlc+vllVoTM\nEfCNLjGW/OIA3KVKwzugMufFDNjA5tYgdE4bQ7tVLv6HmtF9yuTsGyBScki0\n8TA0iSiPTJ8KjVbRF5WgnwIpcuZDefz9dvBwuXo/xhqsaKoJqbHTxARA78Zr\nrxPjjsxvEWPbIbzgqPGGbT4xdiSUAI2ATDuWJXxrUl4ivUOiBMynrUUwJTjB\nSmSdmsAhetFYNz/2QCDe9BircJsoBOaSDJArFOLP/l4ZyYETuA3IVKlljJQ9\n60rPHlc1MhfVi9OjJJMSIj93cMTZ3TPgk3YviJoUFjkmashSVJz9xuhs+LmM\n5lQI8vvk5/nDNurEDuaQtr74gvtvuIyrnMUN3OZDYGsGYL1iIrgAYVQ2MntN\nQLjR6uB+KHh90p/fjwcpb3oFr3WXGT2SfMxCu+0HnxoCBFhSRLd6txRZSYNa\nTarehQj3CDxt/HtuFIewuUVaANxE5yAqdQpkW+ss8ikUOsyQ8sta7YkvDIda\npkjzANEeX0dDJh4ww21w8rc1olM8KEYrBqWkECbvJbJvX5D8XtHH4BENG+wc\nC3OpoSCOWKQT0VwNEkHCc2E3oWM0DBn3wygbsujJpM4u6Ff5G4zDTwABOjKj\ndAJi\r\n=lH84\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-transform-typescript": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.16.7_1640910199502_0.48449615365669674", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/preset-typescript", "version": "7.17.12", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "40269e0a0084d56fc5731b6c40febe1c9a4a3e8c", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.17.12.tgz", "fileCount": 5, "integrity": "sha512-S1ViF8W2QwAKUGJXxP9NAfNaqGDdEBJKpYkxHf5Yy2C4NPPzXGeR3Lhk7G8xJaaLcFTRfNjVbtbVtm8Gb0mqvg==", "signatures": [{"sig": "MEYCIQCDjR0rmKNk4guxm6vnfVpiJ5KCHUm9VHLcD2a96DEdEAIhAI+R3R5AWdn4ztBrGhrEmiqDZ+pLFyMxhPyCDxqRqfDD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrynA//X4NPfYfqyHk+CYRAq/F0td/y7dJvfvHWZk+Kd5xKH1I3zJSW\r\nmKU42RbaXRvbS+ZP5mcqcaLAHOSO2E1g0h8pohg05xajf8hDW6yTlcQPGjSN\r\nYTUe+wQkzDj4G+RFJdgfJqRuYlO93N69CXlp/mpz23OEy0g2JJty9jZ57FYV\r\n4c98X0Id9GApnRJdZRfIL06aVW7TXx74KB3oTpWtcZui9z1Daq0CF5UuWBjw\r\nBoHh0iNHp00lyA0fMFt7CV9pbFuI0cn1+vI0P3YHmFH9dBLRuai9sMKi6pIk\r\n3yR8ONxbpQkpO1nIyw+91qXHRWNwobkoxNVuthD+sXRLaeIGAFvFRonvfAv9\r\n4vLj0mxsL0jEl1CdVQ6WifKLvnQkiNWzUeOgwgA8EHM4CEu12fzkRce/4phf\r\nh1FKXn3NCPSH/We8vWGXMQ1KHdqsOJv6Vjxo2M3U129oz18bmRcHreXEVZRh\r\n5YwKvfbPJTls8srtupofIbmk2wLnk3y2TZ7kU9XweO9AIqnDQxntYBDID34I\r\nhZrVCmNjX2epEdgWxXTHNtBrReFsuTno2W9e9n1TE37ddsGW14fq3k7ldsDK\r\ni+9Eywbj3ozV3Dwm17LsE5cFlpBgUC5ryU3+0h7keXSx5dPO/4ntkdLLtHln\r\nWhGdoWI5kMrW+K7qKAZfquxu/4Jh/Dm0H+0=\r\n=0tJA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-transform-typescript": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.17.12_1652729594253_0.18225224068328716", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/preset-typescript", "version": "7.18.6", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "ce64be3e63eddc44240c6358daefac17b3186399", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.18.6.tgz", "fileCount": 5, "integrity": "sha512-s9ik86kXBAnD760aybBucdpnLsAt0jK1xqJn2juOn9lkOvSHV60os5hxoVJsPzMQxvnUJFAlkont2DvvaYEBtQ==", "signatures": [{"sig": "MEUCIF5RnBa0tDR6yZUeUNOUs0X88PNKLG0DuABuu/4GbmybAiEAyEpzQdgOZnfT06zx75+tO9eHND00DNFOkrurzntjz7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugodACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroyA//T9MEdCqJvG9QLpBodXDZmHTlQbahjqjAsTtPTrYhamcd2A8c\r\nC9nRiZzlnawiS15nWtDZIo1E9iynTkHex2M6sihiQQ9OU39EyHYrQkyO4yo7\r\nrCVMDVxyglUR3sPeG0nIgyZAMaGc39ooLCIIn6pZ/IYGmyusE9MQkgmbCUOI\r\nsvSz4dqoSdnpD9dujitjP3bzE908ld4xYQgMR3F6ko+lWZkeGsQ1uyKl5v9v\r\nI8MoPwY9ZxiC95JEt87oAjVo2G4FR8Skta36l37nI71dML3f/XJqRc382Fgl\r\nQLTN/BdX2fAfqW0+iPloRdtT4Yh9ynheBWCzhI+j2LZJ6SLT8GIxf8vxlHuV\r\njzNQTmGn7K1V5tX9TQLq44Ib27RurwU3mfFwrjNNV7Fru4o4CDq5vd9EjeJj\r\n2r0XRHgSROHv4WBUSmIoylDMUVHv0uuC4MbbcvKNJQLJ2OhyLA25IVwJwb6q\r\narIk2h18fRz2PkIo5gbWM2ygb/f/RX3z45f6zAiIUbR6GcOA015XP9RHwfiX\r\nrFDIonok6XvlcFP2VgZXXHEFuszHpqO3pz2n1xFdPad3Yk6HwdQp5iCWPhtI\r\nyKuWw4L4GnmUeXgnBzo2tWl++KnAwkPjBs1SbfFSCil+sCz+Y6udk3J8gAcd\r\nm+ftsMR08zjxTaURL5gTVq4CccXX2D+ltek=\r\n=Oj7/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-transform-typescript": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.18.6_1656359453687_0.5557091475516398", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/preset-typescript", "version": "7.21.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "bcbbca513e8213691fe5d4b23d9251e01f00ebff", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.0.tgz", "fileCount": 5, "integrity": "sha512-myc9mpoVA5m1rF8K8DgLEatOYFDpwC+RkMkjZ0Du6uI62YvDe8uxIEYVs/VCdSJ097nlALiU/yBC7//3nI+hNg==", "signatures": [{"sig": "MEUCIQCwVQLk5s28cBamq3OkLq0XbnBbjX2aEbPjQqnF+jbEzAIgM/LngGWMMIIoUIbsRGRauROtmqEQLPgxV34nfJkldfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWFw/8DmY7/rB7v5ed6EIuioGWYGKV17E53FqkVWXaTMutHHK+JqjL\r\nO5uR1HE7CXUFcmiHmjKwG3FwsJQVg3GgFIoBrbSd8prUnckBn8t0C+atqbFP\r\nDnmXxdO6O1X4nF75+prA2aYUO+5B3pY/qYgCz7eVrTLMbwnvyFhVfCtRPsoH\r\nnd4Rlt6aAzoixVA1dSzFfBAPDrO/47kZm6vCMwZiYt339LY6PtXL5NWXD4xO\r\nP1OpqQnelV+RCRxCjfqfPL/YIUH305jNqK1fMhBaswGGuHMGXZDD9x+UZPcy\r\nLTbLVywAnvEn0jOFh3/FG0NVlM6+CNLH+fERdpKlgaezJCPxbahN5dOPMa2c\r\nuFq+909yQhb2BP7BKq0n9tugfJbwKutBfK8lfakeEP1/XBtQLMiv4mujaE8J\r\nOq4KMzIE8FqWDwC2JwYpXrPu26dM6LClFyHu8JwBsnDAGImT00Iw0tDJ8Gzm\r\ncruNq/izT6/q4Ji0BB+BGZmB0so56/iVG5bojjvfAoe/fOb8993KO6lxygIm\r\nUNZUDWnZCbHBCjmNUGrc7HHjFkiahpiPmF1mL2s7Rdtgejkc217OzkW7so3+\r\n244+2wJ9MzVO/WoGsvJO+gZcNv3iaL7CesHFWB4pHdfJsRDUMImaEQtCb5fL\r\nHLFquQnU9QxD20WYPufLtSeqJC4/2JZoBOI=\r\n=baK/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.0_1676907082612_0.3288576569368491", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/preset-typescript", "version": "7.21.4", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "b913ac8e6aa8932e47c21b01b4368d8aa239a529", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.4.tgz", "fileCount": 5, "integrity": "sha512-sMLNWY37TCdRH/bJ6ZeeOH1nPuanED7Ai9Y/vH31IPqalioJ6ZNFUWONsakhv4r4n+I6gm5lmoE0olkgib/j/A==", "signatures": [{"sig": "MEUCICt+WRAb4HXaejUqAp6GrI7yMtnIMMlAASQK9L6fqDrPAiEAvMVf6eh2oo0XA8FWWk/EPtgYUdqKJMnudL6frssY3IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeEg//alH3ui7QSy85jfyMbzYzVTStW6ZXD0NpcanuiLJWj2xgjk0e\r\nUlH4PQKaBM4WhOsjYYovcCz6Wi/Z3ddZe3tK1RFg2mw+LRfmsjuoy2sFUAV6\r\nRp6FB1D/SXf5mFGFKhlhJAPpGO2weDPmjeZP66z7cK2d64O+acI9FNZYGKtF\r\nsL3BPCwhL0mOy0s+12CYMKRZSjVCl8M4yWfx3OViQ4wUJHvn3CKQEZCwoUgl\r\n+I5dtGONbvUT7ye3PU3w0vxYjYDd+8D0YjJYz4D9AEJArUtaYewHMR6qxfKu\r\nKjixi5p6X8KjUkcKuJ0a5c7Jj7G/6UItmzHLF5UDE9NkIkdcwqwh04bb5ZbW\r\nlvzF0771vFWyhsE1/LKYmBnXNXPjqwRRn2w16sF77LrM6y8R7fcov/YigeTE\r\naIbfnMz+WaZ+2fRHlFfievmpmWw02Fndig7pKLNVcjmvDsmzyJ6QFZxtfyC5\r\nGWa132MaCb+ViiSxJ5fHKqfx8FDYtaJG6uCIRAjFm2eYV5N6djFmvZkcDdLB\r\nb/aP+7bdvq2r1Ja8uYIHl0/66rodui+04lT4WmJWVr5aCTm7ztdW+K68Mtrs\r\nUXn5jSzyWvMZyUCnxAXgEkNnSANYPj7ZRuJ8brxPU9VNWR7iJRah3q5PL1lf\r\n1S0n4hzabtSX7gHi74V4LBobBlMrKKdcdms=\r\n=k8AD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.21.4", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/plugin-transform-modules-commonjs": "^7.21.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.4_1680253316589_0.016781226260434767", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/preset-typescript", "version": "7.21.4-esm", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "fcf2b8f9707c88c5ad8a4c2e7b905abd1396eefd", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-WPDEV+qH6U0w6NmQMDZDgNDco+k36H14LCZ514G1EaWMhXOFpRzdEh7AEeY7rF2DaJNd9mK5foWpoBoWvWEtTA==", "signatures": [{"sig": "MEUCIFCkW0pOytKK8iBuyoFTH8PMnZ+xWirT9bXk/QSShwF4AiEAjA2AtXBj5mvIDxcyi+SirHBfdDCm6Omrm9FG6eRFpHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4lw/9EtSFwaoWcGxVFHQANSzucDSeceolMOF8OcNNDKLxC4u22uRA\r\nLKdpPEtWA+nmEKFLwJhCB62e8xH+lh/slGDf06RoDHXZFVoruXxeGSTzZpWH\r\nf8l7YHsdL2tXDXbsXCIoQkLfji2sbRcxYZcjh82zv/bkNBul21ycBD9huMG/\r\n4lLCaPXuJVNL6La/uza+n1FpGdMBRVKhYWMAfJ8QT5yzvLmDFZZu4GISld/1\r\n2ybt48ePbvaNfevKaCwunNBcOGtcdC1UDwjZSeS4XkGkLW59m6k5FaoiMoVk\r\nchH+sid2+03JLohW6YvRMfhGqA8kJbTXCoW9JZHh280nw+BHRGkY7rgSPqyz\r\n7Cn/uOKQbGK7yeNLD+0MVs5PLP+vmU7n5kCrmPneCCDw+PxcyMZUnw4x/XtU\r\nrW3QoZpFnnDV/3iBJPp4HMjDQM7nqZYSgUNhr3r4g8T0XbPiUwvGUsJuCodm\r\nOTkjDVj3OmxLPqTUNswpm7FeuBu/fQeVp38+jfTHlQriQ+IgfdliaUhGJZJH\r\nyWFRyZvm6olZUHRR3rnK5leYSXxo0Axj248cTpuVrbWx91FDi/xzu49QXmYw\r\nvmkqfAEg2ql6VIVEiyYjkd5htKxGFUD9GpS4snbKEiAe+8/Lqgi9Vi6l/hGy\r\nchBoc0Tx72SLW68FrsvW3j9RZX4VwPp3eLE=\r\n=l4Xa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.21.4-esm", "@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-validator-option": "^7.21.4-esm", "@babel/plugin-transform-typescript": "^7.21.4-esm", "@babel/plugin-transform-modules-commonjs": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.4-esm_1680617404474_0.4719264079653229", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.1", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "0661065a6445ff2e3cae8e1a93ecd6260e57a38c", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-jfTtOnPlqHgQYWhS5xjyu87JK1M/aox8mqe8Ei+TfkjbJVHeQZTyxBt0PsundX7AOj9cNMjDrzzgM8fMgTe7IQ==", "signatures": [{"sig": "MEQCIGdtwLcrdal/OfARTMeT2IMzy6gQKAx1zAd/bNInQPk/AiB1Mmfdeasj44WhnBFmvmBUxRgTAFsBnK1zXrqI74NxqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosdQ//T4DgM+qPyWw+7o8rxQ4jB1xB1VstK11Giam5v1AatOfooQp8\r\ndinaqu3rJ9e4ioxxsdL59ubufX68IZQueMI3uqDf6tSoUWQNMrGFGs2jn6+2\r\nRsWLNYmPJD2FOhRaawmBjVY2kFLtAXpZdQBEeI/y0Ch+7BIiQawCSFDDupHC\r\nJtH+pTnwXqHU9SvQgktP6tLqD/LbUx+d5spyZqnjRsybJgiID7f/hYeP5RH2\r\nXsABigLaxcjW17+YnFs7N/gIxVJnDYp1nUDkX2BOD2nsB6JR+ap8EcubR4j+\r\nlV2wRKb7+RLamfVTX1/AS7Pghr+TpP3tKAHVslcGvNsDIRm5vwlyUag4KSY4\r\n4CTKlXdzkGv4+6q8ae8754Df0owABm7aUe2/yNjNSM4bRe1lgTjJG6OZxNQZ\r\nq271lYj6CVrRLBUoDwHw/mGePRPqpuUmPDeokk89auuB1CwYe7N72xAf2ZNu\r\nO6b4C6RpU6B8J7DJrFGm6JayRY9L16uVqE2tb4GsF4qoxgQZlv1OzpCTrLsE\r\nZO8omR1pGhOrdQV2UJEhnC4vQVeipaKk+dcPDWKbhbDE4XfcN48tkk0PFSLS\r\nfkpeKsaoxj2Ur9esjbc1pQBx40eKK2fLJWmVu4UeLiox8ovuE9s/xCalsfzB\r\naNuea41r4n5bgq+xlVf6O0MJ7WMaqdRcuxQ=\r\n=iEcA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.21.4-esm.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-validator-option": "^7.21.4-esm.1", "@babel/plugin-transform-typescript": "^7.21.4-esm.1", "@babel/plugin-transform-modules-commonjs": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.4-esm.1_1680618121988_0.7891224818761537", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.2", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "5f9da65edbb3e70702abff25338d23dd10a14b88", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-c9CfcBCfSp/qCBPxvvyGFtEnQIA7fU+MmaTWH2nemLBTdIyig3STx/xUP604sLFYCONwoL9YobWPUtpHB1KYDg==", "signatures": [{"sig": "MEYCIQCUaZDT5teoyUlokkxf256IqdDkli68qTop/rTe0/jWiwIhAIceNv4ea64mo992FxQHoGkDAaMwgOIX4lq5FHyZuI4G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4LBAAkvFpCYLn3z/81/Uj3lvjVG6eJiiNSbJmQ0JoLg2TOM8c0P3L\r\nm1zxcPi6OjBz0ljx6B4c0oBrpvTSK+UEbwvNo38LGF4mf5x5jEjdTI+HCo6J\r\nMZdxiz4lNZNyPEMoEdAcHCYuDu3na4FbCUHZAh29mMGBYcBU9r12Ma1OaMap\r\nJINA2E2s6gJrGysYXJOF9sOMB4uXit+DgPOEgAe+Mth7sxKJht77W13JPQ9J\r\nQ7tKUgu0s5mbdsFtsvxuI8fCjGBUfoJYDHCYVu7dkZklt+jvDjRN72ShRNLR\r\n9z4oVV+TzmEQUMxVvJX5QA9PB82+WQ9KGlIEicsxYaMWUr7eRDVis8hIeuoq\r\nbL43Iim01/YbOY2UqaGi/HfTP0MfXGY2PtidtRO3WCfli03ffPoyWUtDObvb\r\nBmRcKCAlC2DirNPhtWv7HuPSC/DsbuyPd+EVX3gC/RmtdDFdsQ9tZ9bBLvEW\r\nX3PM5HV/R8GmtuinR30RnbYvCsqoLnaCdGmO/V3EDY9/YlVA/w9EyTLsfWWV\r\niIsGxgrC8GEgqYPW1cfyFjrcvhWHg3qsFqYCAbpwrhxXhKq8NlRPQi9mjQ0T\r\nxwFC8jFcgcKoOvciYvCWTrrwv4qCnK9WzXKCMyk96TKb5qEiFyEeDNswtZ8R\r\nckixdjauiSpBqf5rKAhX4U8KlhvYjCxKRVs=\r\n=euz6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.21.4-esm.2", "@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-validator-option": "7.21.4-esm.2", "@babel/plugin-transform-typescript": "7.21.4-esm.2", "@babel/plugin-transform-modules-commonjs": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.4-esm.2_1680619207879_0.38527365679704273", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.3", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "51014d3eef67eb22c4c950b0da94dd534eebc7c1", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-9Nl/iuW0wKeVvXzx6yntM5Yti+9JeIU/877508AQr1alPeLxUX17OCg+kwVrVyOvKvvStA98y5WynAha6CLGOA==", "signatures": [{"sig": "MEUCIQDdqQ2VSetGZIVGgn3xwqa0lRbEzVsLfIbF+h7VJP/lTAIgMvLzKKq1T5QpOE+R0nHv3TvuDpNW+8/MOvmlCpk3XWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprhA//f/oFJf/MWsIpeuXvor31L5Yg5yoE9nqIrhvLeoNsIAWop7pR\r\nm952G7SnoqgnyI+iQZNXmexCO5Z/GApniuBhXzzyzy+yLjYFVSJC/kGPnRTb\r\niHHQ2x11zuR7LVMSHt/SL5Iu10ugBIbQtumPXKw1an81Qq7jgVaBq0Wmz0//\r\nPaTQhNCF0QVdBZguUduy0D3kVp1LroBiB5u5shtvZNeRzCvCUn9MsNlTA7zV\r\n/IZSEQr+sOkcQGNPxZtgWTugD/ozruK3aIwpiO17iZiiRUxBCnmdTtRI1MbP\r\nHG05ygMasOnB2Jud76BXjAftPf5h5G+U9mcrmVxHTydOZN3L5Vhx5JTGbZmn\r\n60yZLr7gB26wtUMnAYEhclYGv7M7/7cHXnMFtAMByZ37uKHdBNnmOlvDU6iT\r\ngEPMwndk8PFOYwCYnmWcFVW6twniwsPePiLnvSCP2wiOGvz2Q1ETPaGSbBRi\r\nTbPPW7xynYCPj16leKzyxODHajhd6of0EGvoDh2Cx6awSJCFcVuZwbmd16xd\r\nVNFHSX52gxb7dp++AqSQjowrfuerrolaihpdyOpF/5uKUYloqB6DLfhSYlka\r\nShLX7/f49uwPozEyZFkiDi00UyC9HeRfQn+7wWjBjuP1JdJ1nLwONlyxTa+d\r\nhqqAkW5JI5I0ckOHs3TZ7jTzzVOHwmXuodE=\r\n=5SXY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.21.4-esm.3", "@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-validator-option": "7.21.4-esm.3", "@babel/plugin-transform-typescript": "7.21.4-esm.3", "@babel/plugin-transform-modules-commonjs": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.4-esm.3_1680620209928_0.37172915820284014", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.4", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "6c3da3324607d858486ee6009ff2e8941d63070d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-zLQMl2GJfMNtd9lbYMX4fWyuq3d3LU6g6nC+sEdyFelh/PWehXLkYP2rMNaeDKhFCeMmTtYBY/s7mlAJeiT6Lw==", "signatures": [{"sig": "MEQCIF8q4P6q4XJNzE0oagp5STJBuZIwqzpDqumwIuoc/rHPAiBF+zm1cTkDepGZCkIExr+r0TegHSt8g3oKPoQiGG9GeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD61ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBLQ/+OdYYArcnljS7yI5qd+C0CuiQgmfQNAnzUsOyPq57vnb8SSLD\r\nE0doIFQAf/jA9zOGOYfJvzBaXWdZixToexGFa9HX37xjBLxrOb0CxKhMltqC\r\nizKXFmRFckuhsWdyyyY38NXzA+ljBI0CXiEY7kjnEJigfczfPWRtpTspAQ44\r\nblrtZ5Dks+8JriYlj5qm+KDXiKNXngDbldIAuVqVuI0UK/MvIAPz30vghY4S\r\nIXh80QhoVHXEtpXdo0yKI71ny7HM5c718/8nYWDJOddEBe9uOxkAAju+qrp4\r\n0k2yE6eXNBCbkLI65gmsyT3ksYAG3rywTd0N3NTxk/0eFJWF37EgUI+sfY06\r\n9dklQQO34CTKPiMyuvYDnvDHMpLacgkOUppiw8niPaL0t2pt+++wWIjY/hZ7\r\nhw6O+zIEoKqecAjyntVJIVMWnxeawM0BoXmaKKXSj0ix0d4XiunD18DlCy77\r\nRTy+ihs/Be7EV1os745wXz0/D6BbvXqBST6anmY/JCthsv11lYzuf5tbOsTU\r\nSfWeWCNSOKIdunyh3TYJeKLb6o1fN4wel8OCZYm2IZrR3gQHUIZ0z6lHig7n\r\n6/qBWnhytv/pNIuD2kKZkGhYBiXBm77+hzsnUAa7guPpGWau/Ox/MUlvksMK\r\n4BdDhbNXnX/QzO83/xcrprfTCQUPqT2D7rA=\r\n=T6uP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.21.4-esm.4", "@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-validator-option": "7.21.4-esm.4", "@babel/plugin-transform-typescript": "7.21.4-esm.4", "@babel/plugin-transform-modules-commonjs": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.4-esm.4_1680621237561_0.3120321186005681", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/preset-typescript", "version": "7.21.5", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "68292c884b0e26070b4d66b202072d391358395f", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-iqe3sETat5EOrORXiQ6rWfoOg2y68Cs75B9wNxdPW4kixJxh7aXQE1KPdWLDniC24T/6dSnguF33W9j/ZZQcmA==", "signatures": [{"sig": "MEUCIQCHkX9rMA0rdsqkc1F/eyzxD5zVzjpms3ibehf2CVpVjQIgB0zykjcwKFJBDGgCOJGInKRys4SQiqco2p3MXcrChmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5fA//UmCGS+wYZVL+d1QdT+CAutQO4SnAleGvlvVMHfrZgXRH5oTU\r\n+ARdtgjTr1ap7tToJdaVn6ujsdyQxmsmNM+GAUEHG79hq+knbiu6XtQtHgwk\r\nC/vgpJ6auelaaPScv/qPDHoG2BAZ8siysPLgeaQb9cnIJsHoIpBrmoNi1A+m\r\nUmPABrRtr/7jtHAlbAWq7HNr6KGS6gSJW8PmdyiJNrdkKUnOwvwPK1Ly12MV\r\nlALS3RjPJB1qb0EC8ul5KciABmQT/U+BB25WCjDQhSbjrw8HZ/B6Kgl5VAIQ\r\n5eHlDfK18qxuypZfaNTUojjtQ7nXhZTgkrNtNqlaf6tBLGb5qb0llNVGUzH+\r\nFJv1EvJHTgnrUDkObo8i4maMZbfeWh6sGcthpn0e9vWtyTWy0un0cvhlUJkt\r\n7wAkeFcgZkYdGQ+CYT2VAtAnTOUdOoDCi5hqp5ZFAX2EzTXjFfAZslKR4g63\r\nX0yzPyOBG+VDB/5Wau+objQAB0jafKvU5JxscGUqvkzCikAU3qSs8iGlJyhT\r\nJky02fLE0v2EJXT1KA2oES+Yo4KMDKQOamRch0mvtvc0lC0+SElWmd4tabEU\r\n/atTFOoE5FBXKhwGlbAeUzxtrGWxSs4C6NJykbMpGHGstCPuewvxbUpPbh8R\r\nviVeb+YdKEmWbN92TUWWbmGHiyW9Je8wLfM=\r\n=8f02\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.21.4", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/plugin-transform-modules-commonjs": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.21.5_1682711431486_0.5592424411296586", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/preset-typescript", "version": "7.22.5", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "16367d8b01d640e9a507577ed4ee54e0101e51c8", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.22.5.tgz", "fileCount": 7, "integrity": "sha512-YbPaal9LxztSGhmndR46FmAbkJ/1fAsw293tSU+I5E5h+cnJ3d4GTwyUgGYmOXJYdGA+uNePle4qbaRzj2NISQ==", "signatures": [{"sig": "MEQCIHC/Q/ob19gKB/E+cLdUYjD6iiWmWeQWXeo7d9vtgJFoAiBfUe+jtl3BCn8wrifu1wOPuOc9MXgKqNcmKUPdlLOSXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25664}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.22.5_1686248511554_0.8408813321125188", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "3726a43fa6850b8da1110ad2d46feb41f1fb62e6", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-VIxZ8H9L0+xdDZ+myfxe9FPJ8vBEV+OS9qZUARkhP5mzIt9oapozebwUl1wE02lhxZq9HQaKlEM3RYHgdOLkzw==", "signatures": [{"sig": "MEUCIB4VwS6Il2xopmNQ3OespYccb9xUwCIP/f2atPXdZ+d0AiEAvUQg874CBFtjjk/HRcL4r649/4WZw06x7cxVNr58BnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26504}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-validator-option": "^8.0.0-alpha.0", "@babel/plugin-transform-typescript": "^8.0.0-alpha.0", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.0_1689861634727_0.5128687995719028", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.1", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "e5a1dd97c5c6e595da841497ee52c6927ac7cec4", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-HG0oOuVOktZ6FW2jqJ3Xa9z9wlnlRMLoBX60RLoHU935KLsTjRESbDdYljDMif226Sep1J1n5NcgcwmN+SPyOQ==", "signatures": [{"sig": "MEUCIBOcpkWqT6Nf/hxJx+BqsR4xNK33dpOw12r4FYBit7A7AiEA7kKtvrL34mK9LYo33rLAXIqx6G/DJo8MhCHsq/WH5NI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26504}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.1", "@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-validator-option": "^8.0.0-alpha.1", "@babel/plugin-transform-typescript": "^8.0.0-alpha.1", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.1_1690221190029_0.8071445701724302", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.2", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "9f07f05809b26b14def62133a970a181d6a14577", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-Q5SIzYjAYFzoGoVZ4pdocveCaeJ+FcALYaa/2yghY2nPbBw9qIiKdgYzzUcb1ZS+yq2oEwZUu67fm7atdWAywA==", "signatures": [{"sig": "MEQCIETb5OCC2mFmH8KdNaUy0tntz6d9lJC9SB1iznoBZPXgAiAjyfQsN/VLDA6AESWZcbPzdl1F5SZQ5VtXCknq/j8U2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-validator-option": "^8.0.0-alpha.2", "@babel/plugin-transform-typescript": "^8.0.0-alpha.2", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.2_1691594129795_0.2179221243581113", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/preset-typescript", "version": "7.22.11", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "f218cd0345524ac888aa3dc32f029de5b064b575", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-tWY5wyCZYBGY7IlalfKI1rLiGlIfnwsRHZqlky0HVv8qviwQ1Uo/05M6+s+TcTCVa6Bmoo2uJW5TMFX6Wa4qVg==", "signatures": [{"sig": "MEUCIQCxonXwWjaQ6r4OHFCBec+97cbOXD2FbOvHqRKA3QnMxQIgb8MGViMdr6emo52ONWVHvGxpqXsYqjf3+HkJh5BOxBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17920}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.11", "@babel/plugin-transform-modules-commonjs": "^7.22.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.22.11_1692882532803_0.8155480649495193", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/preset-typescript", "version": "7.22.15", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "43db30516fae1d417d748105a0bc95f637239d48", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.22.15.tgz", "fileCount": 5, "integrity": "sha512-HblhNmh6yM+cU4VwbBRpxFhxsTdfS1zsvH9W+gEjD0ARV9+8B4sNfpI6GuhePti84nuvhiwKS539jKPFHskA9A==", "signatures": [{"sig": "MEQCIGguFJmAADafNEWcEm9ysRGXdX5URHtwj2ZRhgJOQDI4AiA6UVbeic0yUqXcv5sCYOF8xP4kF69W/DB/ob0R1dMlzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17927}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/plugin-transform-modules-commonjs": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.22.15_1693830328638_0.820424877930394", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/preset-typescript", "version": "7.23.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "cc6602d13e7e5b2087c811912b87cf937a9129d9", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.23.0.tgz", "fileCount": 5, "integrity": "sha512-6P6VVa/NM/VlAYj5s2Aq/gdVg8FSENCg3wlZ6Qau9AcPaoF5LbN1nyGlR9DTRIw9PpxI94e+ReydsJHcjwAweg==", "signatures": [{"sig": "MEYCIQCx3rvlKEazRPRl2uhvNcNkO24sZAbHx651bYmBs+Lc1QIhAI6RbzcC0QwI6X3ojg4mj4+Qvn+R4PIxDCtAKrloGfyn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21346}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/plugin-transform-modules-commonjs": "^7.23.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.23.0_1695629508444_0.3306452505943611", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.3", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "fca964f5d427d21002c371f31b2ba14a611f665e", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-jR96sTd1i3t5PQF45VCGcMoQPd8p6TboxIEcj8m210XUdkxlIf1s9TuadiuxHVO5cgM1Ruo2DfQS0zaAEOVJ4Q==", "signatures": [{"sig": "MEQCICQcQeBxN7+20GSoDwUdhKy8VVAR6YwPvhOO1/ezF6lQAiBbdB1zfx7DsN4NiikxYapwgZBw+pS2YGMTftjqPGJNqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22090}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.3", "@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-validator-option": "^8.0.0-alpha.3", "@babel/plugin-transform-typescript": "^8.0.0-alpha.3", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.3_1695740262018_0.23724186172244077", "host": "s3://npm-registry-packages"}}, "7.23.2": {"name": "@babel/preset-typescript", "version": "7.23.2", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.23.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "c8de488130b7081f7e1482936ad3de5b018beef4", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.23.2.tgz", "fileCount": 5, "integrity": "sha512-u4UJc1XsS1GhIGteM8rnGiIvf9rJpiVgMEeCnwlLA7WJPC+jcXWJAGxYmeqs5hOZD8BbAfnV5ezBOxQbb4OUxA==", "signatures": [{"sig": "MEYCIQCZFqO9OX3OB10BLxsV6IXDV9XapRABBcf4PEUwLhfWLAIhANsfeAiYzQ9Xsr8q7fLJPW2SefaY7NoLccOGaY0yQiep", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21466}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/plugin-transform-modules-commonjs": "^7.23.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.23.2_1697050283526_0.6794712002985703", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.4", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "e714e52103171849e62c0b70a7a5b899d1dad70d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-m7X1i1MDVliNOxotpXRcHzBoani1HvJhazB76ipY7KyDClEiv5S1ZLFkJoyB9lYS6E0MItmsP9RPK8D70Ap3NA==", "signatures": [{"sig": "MEQCIE7NcAHEraVXfucJNaw0YPolvqkLXRvI+vPCODA98BG6AiAthUHLGKakduq2kj92i+nQeJY/nCCOhVdXTVfWucjfnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22210}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.4", "@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-validator-option": "^8.0.0-alpha.4", "@babel/plugin-transform-typescript": "^8.0.0-alpha.4", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.4_1697076415996_0.8511410428465733", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/preset-typescript", "version": "7.23.3", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "14534b34ed5b6d435aa05f1ae1c5e7adcc01d913", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-17oIGVlqz6CchO9RFYn5U6ZpWRZIngayYCtrPRSgANSwC2V1Jb+iP74nVxzzXJte8b8BYxrL1yY96xfhTBrNNQ==", "signatures": [{"sig": "MEQCIFQX6N3AS7ABArGCrTDA4chtIUW83V4V4ykdImKTr/pbAiA1ICqH9P4R84YBtWU2fU8modr2rmiX3vMeLBtb/PkvJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21569}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-transform-typescript": "^7.23.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.23.3_1699513456163_0.10494704125457877", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.5", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "2dd4c5dd915f3d57c113a46026cd860349f64f40", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-CHiMKYNKbsVAacZFvogpaNDVT42697cR4kpzorm4fkTGRYlliyoi0pepJH8I7dMyKG88Ip9JOUVvp3/1S3RwvQ==", "signatures": [{"sig": "MEUCICXxYBHybz4GOQwsQBxuGavBfKxy2gHdtKlzLz/8nwaKAiEA29mwXQfdQfK59qEcEpYfntehNZlMamkg5bLgtgfSTJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22323}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.5", "@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-validator-option": "^8.0.0-alpha.5", "@babel/plugin-transform-typescript": "^8.0.0-alpha.5", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.5_1702307998275_0.4100345546752182", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.6", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "c6645330c6680051f35122c78cb5f8cf2a12c723", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-4MUtbokl1mpNPayy3o+DZ7EawtK3R1pWkvuPKahjKp4TiGx5rMXmjBjRBKzfp0GlhJgwLKK3mzTB0rCzbogXUg==", "signatures": [{"sig": "MEYCIQDO6CRzo3JLIy3w9L/vZWUtTfGn5bE7RDeGZYifENSHzwIhAMuH3mH/UILcMS+uBiM/qfVUq6Hh99oHQT+t0ey/etcl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22323}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.6", "@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-validator-option": "^8.0.0-alpha.6", "@babel/plugin-transform-typescript": "^8.0.0-alpha.6", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.6_1706285696561_0.17625165145460686", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.7", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "6849b6298e9271990f7802c585f123ef3dc4a029", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-qGC9oZp6n/1wqw5wrjKtqtjJ4CZpDA7zc3XMIq0ZmbAUnmP72ek+H//8LDnsPj8Lk58aPsBuSgpu2YldXMN/BQ==", "signatures": [{"sig": "MEUCIF2iqPd+WsI+WIKv9PwcAhjamoetVwFYqQedFz0XhCT8AiEAyyIntj09aUXJ8KYLabMMQD1AnAhDQls4GRlODv8nMFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22323}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.7", "@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-validator-option": "^8.0.0-alpha.7", "@babel/plugin-transform-typescript": "^8.0.0-alpha.7", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.7_1709129153808_0.9951243201454658", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/preset-typescript", "version": "7.24.1", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "89bdf13a3149a17b3b2a2c9c62547f06db8845ec", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-1DBaMmRDpuYQBPWD8Pf/WEwCrtgRHxsZnP4mIy9G/X+hFfbI47Q2G4t1Paakld84+qsk2fSsUPMKg71jkoOOaQ==", "signatures": [{"sig": "MEYCIQDXjsfkBt+jntS6lwF5uoSeSVaAlb33B5HTGYlw1pTfHgIhAJnFAo6eWey3ZTGzMIkfRE6ZOyg81WFH/0UkW+hsNpZ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21483}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.24.1", "@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-validator-option": "^7.23.5", "@babel/plugin-transform-typescript": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.24.1_1710841781627_0.6805584322946918", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.8", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "91e6958679ae5e67eefd4ab861357080f33bbebb", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-oRINlOGVUUT0iIcNTX3QcABbhK2Q6txwJfN/tan+nvQ0cFaXSz8AQ8ylvtqCX60uvXD+5SjFWwwwWQVlTi7x5g==", "signatures": [{"sig": "MEUCIQCok7qG59uPB6wdp2W25PVErBLktrz0IOFSbtfs7KVb6wIgLB31P4a+cGVZMpdz/oUdKrYmLs/BQTGTm+BMPMYC8aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22237}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.8", "@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-validator-option": "^8.0.0-alpha.8", "@babel/plugin-transform-typescript": "^8.0.0-alpha.8", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.8_1712236825432_0.7861934734679772", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/preset-typescript", "version": "7.24.6", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "27057470fb981c31338bdb897fc3d9aa0cb7dab2", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-U10aHPDnokCFRXgyT/MaIRTivUu2K/mu0vJlwRS9LxJmJet+PFQNKpggPyFCUtC6zWSBPjvxjnpNkAn3Uw2m5w==", "signatures": [{"sig": "MEQCIEsLhgFpTPrr7q96OIkBlJL1QWFJQqHDVEiW0j/ZHGqYAiBvLf2d1eq4dnGUlbmcB2Y9PpSi+tWErWNsWbBFM2Qcyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90917}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.24.6", "@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-validator-option": "^7.24.6", "@babel/plugin-transform-typescript": "^7.24.6", "@babel/plugin-transform-modules-commonjs": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.24.6_1716553515895_0.5114624453874381", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.9", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "d34bb74a0ec7cb904797c46992f1d87dd6c356b0", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-UmZpTnrYtF8g4fwUAhJmPe7qhpqdcnG3Bp12M9EB4KIQ4Lu+7c4QCbbMN2krCpTlxEourBs/Dr2RVjvrsvEZPA==", "signatures": [{"sig": "MEUCICF2rw6CPNcKLh3pk2y3V0vYIn7PEJHz9JxzygpkSeBHAiEAvjs3S4XuYGwiuv2pZMewhEi21xuT+Opd+5xXlxwTZoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92254}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.9", "@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-validator-option": "^8.0.0-alpha.9", "@babel/plugin-transform-typescript": "^8.0.0-alpha.9", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.9_1717423556949_0.2602415752694418", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.10", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "6474029c2317249aeb9de1263c01cdf7e00c973a", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-UbfJdPFIhVn2nZAbYroDMKhMDif4OP5ZbVkAdagTkArvQ+nlb6uS51H0xFakNi2OlXAzkh/AHTPugVm78J6ZgQ==", "signatures": [{"sig": "MEQCIA/7zLRJl0JNcvJGiWSejlelMRtL35dV1c0+qIdNmnuKAiANcN/JiQ8S/M/399SSHDRfu1GN4V7NoWKPS/hMp2Pqfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92265}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.10", "@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-validator-option": "^8.0.0-alpha.10", "@babel/plugin-transform-typescript": "^8.0.0-alpha.10", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.10_1717500049797_0.7786720736504327", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/preset-typescript", "version": "7.24.7", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "66cd86ea8f8c014855671d5ea9a737139cbbfef1", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-SyXRe3OdWwIwalxDg5UtJnJQO+YPcTfwiIY2B0Xlddh9o7jpWLvv8X1RthIeDOxQ+O1ML5BLPCONToObyVQVuQ==", "signatures": [{"sig": "MEYCIQCHPSuuumin5h/dufu1IF+s6Rw2VfKKOPgUuyrB7H6K4gIhAN+CvL/1NNdPzUIAYK6T1I2dcQRRLgywmu14KdJAaDjt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90837}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-validator-option": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.24.7_1717593362875_0.23552571473710038", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.11", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "3727b85cb142c51ec1e7b57f3d09cca221116e17", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-slTmIJpABeQbjlUAeXn2/jvaPs913Bj2TfNwxRbTiiNXQmR+GSzjNAjqCQQwheBUwNRBEBtXSTCL54po+ZMGRg==", "signatures": [{"sig": "MEQCIFLq8ZDYaJzC2PJTKPjkDV5UIvCm3NicUgI/YtlUx1BkAiAlsgIq3hRek2dKOzkF7JsJ+vZCU0/YGJqkWqw3hcMtSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92156}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.11", "@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-validator-option": "^8.0.0-alpha.11", "@babel/plugin-transform-typescript": "^8.0.0-alpha.11", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.11_1717751773246_0.02117760584805306", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.12", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "1ab4c3d69c7c3ca0df8644b941d7ef5d09d40827", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-DoUcYIJSk7G9fo5u2beyVZBJVesS/CorST5G0yCQa9QVMTAvU7ftGaDRExSx3zWBk7N+h9ARaRrpwuMmm5YRzA==", "signatures": [{"sig": "MEYCIQD+9CDJSC4AaAR85vGhNyn3wTC9+4cy6CDV0g5cHHvHCwIhAMvNp1tlzCrSfmfGJoTS1kQR2iUxVS8WJOxuRJQstJdr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88721}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.12", "@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-validator-option": "^8.0.0-alpha.12", "@babel/plugin-transform-typescript": "^8.0.0-alpha.12", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.12_1722015248562_0.6057672202067628", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/preset-typescript", "version": "7.25.7", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "43c5b68eccb856ae5b52274b77b1c3c413cde1b7", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-rkkpaXJZOFN45Fb+Gki0c+KMIglk4+zZXOoMJuyEK8y8Kkc8Jd3BDmP7qPsz0zQMJj+UD7EprF+AqAXcILnexw==", "signatures": [{"sig": "MEQCICE6IZkAba5VJKHfnOKTVsNnDB90HG8YFj88Ic+J09n3AiBMgyQRnLOlGKoMpdst6LaL4IPjENnvFpjE8Oh7hrp7gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95229}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-validator-option": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/plugin-transform-modules-commonjs": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.25.7_1727882140084_0.7211433929157611", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/preset-typescript", "version": "7.25.9", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "bb82f26cda46dc2eb1ee10bf72fa994e759a08ba", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-XWxw1AcKk36kgxf4C//fl0ikjLeqGUWn062/Fd8GtpTfDJOX6Ud95FK+4JlDA36BX4bNGndXi3a6Vr4Jo5/61A==", "signatures": [{"sig": "MEUCIQC57GNW3oYWBqwnCws6hvEgYU5vm8QMStpqwHl8W9YJMAIgQ7fpjfYZfd83rShUvWeFQM9/EqxyRjCrQl1vysXr4VM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21441}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.25.9_1729610515170_0.7293983095659742", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/preset-typescript", "version": "7.26.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "4a570f1b8d104a242d923957ffa1eaff142a106d", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-NMk1IGZ5I/oHhoXEElcm+xUnL/szL6xflkFZmoEU9xj1qSJXpiS7rsspYo92B4DRCDvZn2erT5LdsCeXAKNCkg==", "signatures": [{"sig": "MEQCIGWHCXXmLAJZUCZr1sC6q6XjneTaT3qaOFvJGqr9MJP3AiBlF/xUWmGg23n7I4ZvhSnKLjYZCqXzFWbgUcmOIgpqag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23591}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.26.0_1729863009620_0.2856311247583083", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.13", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "7d3b4a9e081ca594886b6c08295b788cd748425a", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-Z4KLrOg34y4E5aDME0Cfn1n2RLK44xjBwa3kQLriNlAgbhmXcRKmhXqVa3yjYS3bZtokHsJKJYtFm9n61QvE9A==", "signatures": [{"sig": "MEUCIQD30GVDr+5cKLikKTDfeMMykQL/EP5zqbHQ3GTi3O1s7gIgCldvZGgmV1OwLcY5kZGV4jKf5OOtn4RunQd0zIrsLsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25017}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.13", "@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-validator-option": "^8.0.0-alpha.13", "@babel/plugin-transform-typescript": "^8.0.0-alpha.13", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.13_1729864495505_0.24295956030207733", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.14", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "8a9a35d6856c1a47463089c0cfdb5cb754ce2f87", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-RpoAHfc6r7kSXzfT4JrX9v9QdGK6OPgSRlAJmaDufCWgnmj4UWTBtNIPa3DwWkkbKKfyV1RIT4LSC7dITAHK4g==", "signatures": [{"sig": "MEUCIQC1zZm3HtPMHIFiezjfLOyOY7y716xK2sn1VCL3YmnjQAIgN/6ICJbWSotZD977pKJ2ltmk9WVwDTfkMaFjzgMW38c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25017}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.14", "@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-validator-option": "^8.0.0-alpha.14", "@babel/plugin-transform-typescript": "^8.0.0-alpha.14", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.14_1733504084416_0.3911134156204532", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.15", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "fbf0afc6e324e213e6c767187145722f4affa731", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-xAGZaLpyBVOtGRtCa7/keqCLDN/O/3dUfLwQd/pWVyWCLKWJBYL8NZnMfed4YeiumWOdOQgQPhV2p+PatXC6NQ==", "signatures": [{"sig": "MEQCIHJ21XzecNUCYOkpc56G/cLfsXTQzsnme1VuAwe85kQTAiBL80Hq8JmmgSSbwxyJFIKyKekDZh8tenFR4rPGA3nc/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25017}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.15", "@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-validator-option": "^8.0.0-alpha.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.15", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.15_1736529914856_0.6788088374445462", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.16", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "0af8ac0cf904ed4c21a8db855f5bde757cf2d0cc", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-385Jknt1SRxQ/6Uh/hAqf/sT5+sN9tCnHT8Fyl3SpOtFrjFJ5yot6nfu5Cx+Sb4Aq4t4k0HcmMGFhEyr3bVUSA==", "signatures": [{"sig": "MEQCIDBUSkxC2bLQW0YKL2GbEBM/yXc1Hjs/TQtphqF0UJzbAiA7d15Sw6E5dtaTgLxhGy0hnYyReuzQWlFHWN5lORz9mA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25017}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.16", "@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-validator-option": "^8.0.0-alpha.16", "@babel/plugin-transform-typescript": "^8.0.0-alpha.16", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.16_1739534388254_0.16983013097711064", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.17", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "7c669ce18321755332bda80f8c6e2cfca183ad8e", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-KtNvbmF9cyu0BD1T3F+e/4lGPIvy4tX1AuVG1P3FvQ67lV63CkdCwlkVhHO8mzz+2fDXVCaA7IpuC4aZp8nyrQ==", "signatures": [{"sig": "MEUCIQCeSuRaPSh+FXhZ8z9Hg2al5gQ6/h9A8JfWofk+6J468wIgNnu62wsjJSkpCdF20Aunfce8vd9qnjCIMdYvVtHMvSk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25017}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-alpha.17", "@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-validator-option": "^8.0.0-alpha.17", "@babel/plugin-transform-typescript": "^8.0.0-alpha.17", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-alpha.17_1741717542847_0.3009011146683447", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/preset-typescript", "version": "7.27.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "4dcb8827225975f4290961b0b089f9c694ca50c7", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.0.tgz", "fileCount": 5, "integrity": "sha512-vxaPFfJtHhgeOVXRKuHpHPAOgymmy8V8I65T1q53R7GCZlefKeCaTyDs3zOPHTTbmquvNlQYC5klEvWsBAtrBQ==", "signatures": [{"sig": "MEUCIQCHEtOB4ksFrajVvoXKlzp9IpNuWMcPH80ftxOUVEJ01wIgDRjWpp3ZL/pdzTbRLjYvssEOIi/1Mz8HhA7uzBBb+Z4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25779}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-typescript": "^7.27.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.27.0_1742838116886_0.6996667041390741", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/preset-typescript", "version": "7.27.1", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "190742a6428d282306648a55b0529b561484f912", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-l7WfQfX0WK4M0v2RudjuQK4u99BS6yLHYEmdtVPP7lKV013zr9DygFuWNlnbvQ9LR+LS0Egz/XAvGx5U9MX0fQ==", "signatures": [{"sig": "MEMCH1tl1vaBcyk3NYGBqRSCRJ/iNAVBMAAFxM5FWtuwkTgCIEuNoJIv0rUenL0i1Km9SmJ2BEIWMC53f89jaZjcRIAQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25778}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.27.1_1746025777082_0.6806300016370468", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/preset-typescript", "version": "8.0.0-beta.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "dd03a8ea10929cc8ad7bb3e8b308e59223c965da", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-OZwHnFE0OP8/571ZdupS5QwSUowCnHHr68oogdVnAQ9tOgzEVuPZUBqaMq13Nl/U2Qv59HiKNSMMzmmiCSq7kw==", "signatures": [{"sig": "MEYCIQDaECJXSiCp6/z6TqHB4AShH8xgqFsGJ+rGxXs0oJ05MwIhANVgPhbWc1dCmBmmxLVzMZMWF+ejBUaKR4F50Y1uEcJw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26401}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^8.0.0-beta.0", "@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-validator-option": "^8.0.0-beta.0", "@babel/plugin-transform-typescript": "^8.0.0-beta.0", "@babel/plugin-transform-modules-commonjs": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_8.0.0-beta.0_1748620313827_0.4886353056848023", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/preset-typescript", "version": "8.0.0-beta.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-validator-option": "^8.0.0-beta.1", "@babel/plugin-syntax-jsx": "^8.0.0-beta.1", "@babel/plugin-transform-modules-commonjs": "^8.0.0-beta.1", "@babel/plugin-transform-typescript": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-beta.1", "dist": {"shasum": "556c5f87cff59cfc5f23cbdb8a0fe5e0463c8821", "integrity": "sha512-wlrteo6GDpNyt6EkDdwnzGy5MpQgRa/T5TaVriYk98ToQSC2eOASMWgARa2owsvnvIwPzyoABouVdB17dxRtsg==", "tarball": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 26401, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD1xbEu8Wxt6MtKKqMjjpwqxWTBzbaOGRXdGGdq3kqhUQIgO+sNFkBJxGmTy6e4f0qJLr7OWMbPGG5BL2kUZ3U5N8w="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_8.0.0-beta.1_1751447095290_0.27917440152660533"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:47.570Z", "modified": "2025-07-02T09:04:55.716Z", "7.0.0-beta.4": "2017-10-30T18:35:47.570Z", "7.0.0-beta.5": "2017-10-30T20:57:26.512Z", "7.0.0-beta.31": "2017-11-03T20:04:10.616Z", "7.0.0-beta.32": "2017-11-12T13:33:52.134Z", "7.0.0-beta.33": "2017-12-01T14:29:08.687Z", "7.0.0-beta.34": "2017-12-02T14:40:05.572Z", "7.0.0-beta.35": "2017-12-14T21:48:19.565Z", "7.0.0-beta.36": "2017-12-25T19:05:27.003Z", "7.0.0-beta.37": "2018-01-08T16:03:34.543Z", "7.0.0-beta.38": "2018-01-17T16:32:32.464Z", "7.0.0-beta.39": "2018-01-30T20:28:39.164Z", "7.0.0-beta.40": "2018-02-12T16:42:24.789Z", "7.0.0-beta.41": "2018-03-14T16:26:41.480Z", "7.0.0-beta.42": "2018-03-15T20:51:55.671Z", "7.0.0-beta.43": "2018-04-02T16:48:50.326Z", "7.0.0-beta.44": "2018-04-02T22:20:29.731Z", "7.0.0-beta.45": "2018-04-23T01:58:11.051Z", "7.0.0-beta.46": "2018-04-23T04:32:28.958Z", "7.0.0-beta.47": "2018-05-15T00:17:27.943Z", "7.0.0-beta.48": "2018-05-24T19:24:25.631Z", "7.0.0-beta.49": "2018-05-25T16:03:58.198Z", "7.0.0-beta.50": "2018-06-12T19:47:51.739Z", "7.0.0-beta.51": "2018-06-12T21:20:33.350Z", "7.0.0-beta.52": "2018-07-06T00:59:42.716Z", "7.0.0-beta.53": "2018-07-11T13:40:41.950Z", "7.0.0-beta.54": "2018-07-16T18:00:23.682Z", "7.0.0-beta.55": "2018-07-28T22:07:48.976Z", "7.0.0-beta.56": "2018-08-04T01:08:05.819Z", "7.0.0-rc.0": "2018-08-09T15:59:38.363Z", "7.0.0-rc.1": "2018-08-09T20:09:35.214Z", "7.0.0-rc.2": "2018-08-21T19:25:40.006Z", "7.0.0-rc.3": "2018-08-24T18:09:21.243Z", "7.0.0-rc.4": "2018-08-27T16:46:04.829Z", "7.0.0": "2018-08-27T21:44:34.201Z", "7.1.0": "2018-09-17T19:30:29.140Z", "7.3.3": "2019-02-15T21:14:34.673Z", "7.6.0": "2019-09-06T17:33:53.484Z", "7.7.0": "2019-11-05T10:54:13.698Z", "7.7.2": "2019-11-06T23:27:30.548Z", "7.7.4": "2019-11-22T23:34:03.925Z", "7.7.7": "2019-12-19T00:53:06.610Z", "7.8.0": "2020-01-12T00:17:45.539Z", "7.8.3": "2020-01-13T21:42:52.101Z", "7.9.0": "2020-03-20T15:39:49.334Z", "7.10.1": "2020-05-27T22:08:42.152Z", "7.10.4": "2020-06-30T13:13:42.519Z", "7.12.0": "2020-10-14T20:03:38.015Z", "7.12.1": "2020-10-15T22:42:18.500Z", "7.12.7": "2020-11-20T21:05:42.681Z", "7.12.13": "2021-02-03T01:12:22.760Z", "7.12.16": "2021-02-11T22:47:17.931Z", "7.12.17": "2021-02-18T15:13:55.630Z", "7.13.0": "2021-02-22T22:50:37.862Z", "7.14.5": "2021-06-09T23:13:29.171Z", "7.15.0": "2021-08-04T21:13:18.347Z", "7.16.0": "2021-10-29T23:48:02.730Z", "7.16.5": "2021-12-13T22:28:36.427Z", "7.16.7": "2021-12-31T00:23:19.631Z", "7.17.12": "2022-05-16T19:33:14.378Z", "7.18.6": "2022-06-27T19:50:53.878Z", "7.21.0": "2023-02-20T15:31:22.789Z", "7.21.4": "2023-03-31T09:01:56.939Z", "7.21.4-esm": "2023-04-04T14:10:04.662Z", "7.21.4-esm.1": "2023-04-04T14:22:02.173Z", "7.21.4-esm.2": "2023-04-04T14:40:08.038Z", "7.21.4-esm.3": "2023-04-04T14:56:50.079Z", "7.21.4-esm.4": "2023-04-04T15:13:57.793Z", "7.21.5": "2023-04-28T19:50:31.699Z", "7.22.5": "2023-06-08T18:21:51.693Z", "8.0.0-alpha.0": "2023-07-20T14:00:34.955Z", "8.0.0-alpha.1": "2023-07-24T17:53:10.215Z", "8.0.0-alpha.2": "2023-08-09T15:15:29.965Z", "7.22.11": "2023-08-24T13:08:53.023Z", "7.22.15": "2023-09-04T12:25:28.839Z", "7.23.0": "2023-09-25T08:11:48.599Z", "8.0.0-alpha.3": "2023-09-26T14:57:42.151Z", "7.23.2": "2023-10-11T18:51:23.785Z", "8.0.0-alpha.4": "2023-10-12T02:06:56.226Z", "7.23.3": "2023-11-09T07:04:16.375Z", "8.0.0-alpha.5": "2023-12-11T15:19:58.436Z", "8.0.0-alpha.6": "2024-01-26T16:14:56.824Z", "8.0.0-alpha.7": "2024-02-28T14:05:53.958Z", "7.24.1": "2024-03-19T09:49:41.778Z", "8.0.0-alpha.8": "2024-04-04T13:20:25.626Z", "7.24.6": "2024-05-24T12:25:16.067Z", "8.0.0-alpha.9": "2024-06-03T14:05:57.125Z", "8.0.0-alpha.10": "2024-06-04T11:20:50.019Z", "7.24.7": "2024-06-05T13:16:03.023Z", "8.0.0-alpha.11": "2024-06-07T09:16:13.411Z", "8.0.0-alpha.12": "2024-07-26T17:34:08.784Z", "7.25.7": "2024-10-02T15:15:40.309Z", "7.25.9": "2024-10-22T15:21:55.394Z", "7.26.0": "2024-10-25T13:30:09.805Z", "8.0.0-alpha.13": "2024-10-25T13:54:55.697Z", "8.0.0-alpha.14": "2024-12-06T16:54:44.599Z", "8.0.0-alpha.15": "2025-01-10T17:25:15.045Z", "8.0.0-alpha.16": "2025-02-14T11:59:48.446Z", "8.0.0-alpha.17": "2025-03-11T18:25:43.043Z", "7.27.0": "2025-03-24T17:41:57.077Z", "7.27.1": "2025-04-30T15:09:37.243Z", "8.0.0-beta.0": "2025-05-30T15:51:53.998Z", "8.0.0-beta.1": "2025-07-02T09:04:55.469Z"}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "keywords": ["babel-preset", "typescript"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}