{"_id": "dom-accessibility-api", "_rev": "35-59532666aa157438c3d245653951c001", "name": "dom-accessibility-api", "dist-tags": {"latest": "0.7.0"}, "versions": {"0.0.1": {"name": "dom-accessibility-api", "version": "0.0.1", "license": "MIT", "_id": "dom-accessibility-api@0.0.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "dist": {"shasum": "d7c363cdfa9f420bfaac5d9cf9771fb87c39af08", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.0.1.tgz", "fileCount": 10, "integrity": "sha512-Vt+2Il9HN/EMKbCiVURY+3aXnp8PwoAfPLbKE8bn4nph3z24P7OxSmpndEUqUv/ROcs1Wv4IrsI8gOroVbYPWQ==", "signatures": [{"sig": "MEUCIQCQ7jGvh2bj/wKD+fNroANvaNKrmhdLuJH7wVBL+pS9WQIgGeQcYz/LXND3+UPDhNuklV3pSSugSHTz5Dywu+HGWw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36050, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnNbnCRA9TVsSAnZWagAAdwkP/2R6mIHGFngeScjZfwlx\n1+yKbAR44RXscp6g+MzTdvAJCN0cUB3OmMsfOTYoDR0FtzIshIR3pYzFwk+B\n+0UXnXCM0QzJN/drenVs2N6uLED7W+aMg5vemLMwHmRWrXL75Wsjl3QYA6w/\n7cPtPnUumbUocnaNXDBfE/PwG0ssqpH3e7PciM913nldxJ2pad6P6o+kda/m\nohvWt3ckDLVnEa356cJoPckf5Mh2Jksl79zCkB4tShke53fhVp57H8kw/HML\nOgkg36hjz2vsNKcorYCSKc7As85QKs47JD8HzjO/4W4w9fVjcdBeuYJ/yVhl\ncBpWIaOoRko/i/z59n5JMEMEb3xGPfOIgVfOQ7hqBGo1mPePwL82WJ6XTks2\nTSXLKseoBXF5qL1btJPdoB6+sPY3SUL1ctNYUiRvK8Sp9BD/4+Q+RYjkTIzv\nwCS3xK1O1Ekqhz7N3w/Uqu9qXPsboQ74wGzMDrzuiw9bGnqQVpNID0bmQ0Tf\n++MI1XpnlJlUdaThvXuF4qvpeZ8JOi/9L77bOrCJZDGLsfZ/xC3IAZwtnptX\nQY8xf+Won+ODD0FCZU01WhuFDxm7RPuFPJsfSTItNcdBuBY68FfkUz/d9/iD\n7obHMvxj7G1muiDuJYfL066dPhNyS+suKZczIRqDDx75Y+lHL2hG9Zw3EdZ/\nF0mT\r\n=sCUH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "d9e99b6525467315f1b44cc4dfdb5655cc2d2761", "scripts": {"test": "jest", "build": "tsc -p tsconfig.json", "format": "prettier \"**/*.{json,js,md,ts}\" --write --ignore-path .prettierignore", "init-wpt": "git submodule update --init --recursive", "test-wpt": "mocha tests/run-wpts.js", "reset-wpt": "rimraf ./tests/wpt && yarn init-wpt", "update-wpt": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-manifest.json"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "_npmVersion": "6.9.0", "description": "https://w3c.github.io/accname/ for jsdom", "directories": {}, "_nodeVersion": "10.16.3", "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^24.9.0", "jsdom": "^15.1.1", "mocha": "^6.2.1", "js-yaml": "^3.13.1", "request": "^2.34", "prettier": "^1.18.2", "jest-diff": "^24.9.0", "minimatch": "^3.0.4", "typescript": "^3.6.3", "@babel/core": "^7.6.2", "@types/jest": "^24.0.18", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.6.2", "@testing-library/dom": "^6.6.0", "request-promise-native": "^1.0.7", "@babel/preset-typescript": "^7.6.0", "jest-environment-jsdom-thirteen": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.0.1_1570559718465_0.8855728704855121", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "dom-accessibility-api", "version": "0.1.0", "license": "MIT", "_id": "dom-accessibility-api@0.1.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "29262b665fee87645c66a93ec7857bc769f95087", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.1.0.tgz", "fileCount": 15, "integrity": "sha512-Z0irTzaH9gxY8kx1CB4lc/6EhusXHRRGHFLAJTrg9Ol4FiJe6zfivvEiISdEoU4BIoIVCRn/kp9my91FwtDdlg==", "signatures": [{"sig": "MEUCIEQ8tpaa5p09XgVSHuH9IX19bIoExucNVnsv5K3aiMBfAiEApYEwl3AJgG6WH3b7GoRSrX3gweyJNkUW4Ui/Pqhnfnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwe1TCRA9TVsSAnZWagAAEocP/2fMruFVsb3GWiOsO2j+\nqyBJQKDJRUBugXewz2YKFgfQ2++9hdOTeYSnGIAlUL4SF/zORGOlD5CjbblO\nYvvoY64xN9L2YAvCv7NC3tCVsC7OqrC9i5l/cWrW7V8P/7rQxB1meqeReL/g\nggNej/8r/qx9rXOIpV2fKUYbjhN3CA+D6KcRDI1oOBmV6mLWkJYD+L72mqxe\nqdUdwi4RYZVTGabdIXXHBBFNsMSdl3pYouJ5rFxoyOkxHRoVlgnlQ5qaVkz3\n6qZ6hzKPhYsQQJFs0bjxAI0JDDK6FRpI6JoIEMb7HrvbinIBT8FD49dxPRHl\nVtVF86K8ux/y64x4zJUn9FtITJlSqBVaYU3F3d3N27474ptAKS6ihcPDFjSH\nbmNmS02biBRPOxoFGKGdoprDMHRTL5hclb4ozCVvfEtQt32U+ZUKzbEYz9vb\nR5n/E05yIN5XlhbKILwl+I0ySlY0ALc6R4e52bBUT0AXAzsG9dHiMQRJEDKs\nvKro+bpTAlJ0ZuN8gSji7RaR1MhvPVBrk1B2LEvPASig7oGQWZEAQ07Pdwxo\n9+hCSLwvC22DsAMBmvOCCMxbeXTZvzIrY+X9jhoLHyg5/KbUO+udFbdYk/mL\nWLqwCpGQw+NjCx7ZqW3kRoPn1P76WNCmo/pWuHaXdan8PA7SY0xDoK/NZGCZ\nHrF8\r\n=xFdT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "afd5d45edafe381b5809e942806ca9183b5405c8", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "tsc -p tsconfig.json", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"@typescript-eslint/parser": "^2.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^24.9.0", "jsdom": "^15.1.1", "mocha": "^6.2.1", "serve": "^11.2.0", "eslint": "^6.6.0", "cypress": "^3.4.1", "js-yaml": "^3.13.1", "request": "^2.34", "prettier": "^1.18.2", "jest-diff": "^24.9.0", "minimatch": "^3.0.4", "jest-junit": "^8.0.0", "typescript": "^3.6.3", "@babel/core": "^7.6.2", "@types/jest": "^24.0.18", "concurrently": "^5.0.0", "mocha-sugar-free": "^1.4.0", "semantic-release": "^15.13.30", "@babel/preset-env": "^7.6.2", "@testing-library/dom": "^6.6.0", "@semantic-release/git": "^7.0.18", "@semantic-release/npm": "^5.3.4", "request-promise-native": "^1.0.7", "@babel/preset-typescript": "^7.6.0", "@semantic-release/changelog": "^3.0.5", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.5.0", "@semantic-release/commit-analyzer": "^6.3.2", "@semantic-release/release-notes-generator": "^7.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.1.0_1572990291212_0.9179212681816262", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "dom-accessibility-api", "version": "0.2.0", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.2.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "2890ce677bd7b2172778ed979ab2ff4967c3085d", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.2.0.tgz", "fileCount": 19, "integrity": "sha512-afrHGxXpS5C2jUC5hquPb3GWytNKHI+wJLKr/jvri95sZpLYpEJi3CtI/yBPEJ+/R9/CXaWXifadz94tsDcotg==", "signatures": [{"sig": "MEQCIBFb6VyIL3eDBFZLb7uv59FaNayVQeulhASkJBwKZDZpAiBRfOScCzlVqgbeSD/JpVCwOggxdsMiHqcnP2IDLXOPCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd35njCRA9TVsSAnZWagAAtpsQAIqqfyoF1JdDR9IKHDHX\nVER/ZVQt7l3yr0V7Z4t3oujc9VkRhjcmdS3PEE93QzLxa8pffLTMEgVkjFOK\nQQ4JTU+GQgUf4wTBAMdDFCCfoq3UIzBfZvXZPP9eGIxCNe1cLwMSg7z/rwXN\nn4ITNcxHnYka1MkU22hW1sYeVQSAgWk8wbw8EZ/kZLS6BlJqWC0Fr+Gcdr5T\n94cdCOuLcNZEzwycytBeWC7vWDPYf/FexCmdYCl3/+b5rncCP2AYuzZiqIB4\nncyrpHo5wVGmHtNWE0enP030XyEO9Qrbc127b5ulhmYsvYUNPs1qow4VFCN3\nYpM10uO7jtdnxba0qaDAWgQY2oKBFaBfjXz0J7dXdrPzdKBzken3TXut0DvC\n7pI/+gBX3zs2f//CnTB2N+UkeaQgy8Yt7AugCoiVUHca5Tmzb5wY/r5vj1WH\nkdrwaIKhtCoHqCij/mUn8oiFmZjtSRgQmKOyfQ/+Wou68ihLUe9fZLuQPog8\nM2rjgeNO9eH0WCrgkA2s2UEgtMv47233uvYI1UtV+YI/PXzfquEjHxFXLt/H\nz0xp+gAknZw78I1XQSzJ1yRQytZalpesTHFO5yPLPUG6CoM2FLIDrTOj4qvT\nw9V/h/dTLwJATqfgFfyEK8qeG3V2ps6bQ2Rw0UGiCAPIRJUcPfK1i4rKVpg2\nOuKU\r\n=Pvn1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "9aad3d49634be4e71457558263641cf39d9f5c10", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "tsc -p tsconfig.json", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn changeset publish", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "release:prepare": "yarn changeset version", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^24.9.0", "jsdom": "^15.2.1", "mocha": "^6.2.2", "serve": "^11.2.0", "eslint": "^6.7.1", "cypress": "^3.6.1", "js-yaml": "^3.13.1", "request": "^2.34", "prettier": "^1.19.1", "jest-diff": "^24.9.0", "minimatch": "^3.0.4", "jest-junit": "^9.0.0", "typescript": "^3.7.2", "@babel/core": "^7.7.4", "@types/jest": "^24.0.23", "concurrently": "^5.0.0", "@changesets/cli": "^2.4.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.7.4", "@testing-library/dom": "^6.10.1", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.7.4", "@typescript-eslint/parser": "^2.9.0", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.2.0_1574935011036_0.8031754999948719", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "dom-accessibility-api", "version": "0.3.0", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.3.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "511e5993dd673b97c87ea47dba0e3892f7e0c983", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.3.0.tgz", "fileCount": 19, "integrity": "sha512-PzwHEmsRP3IGY4gv/Ug+rMeaTIyTJvadCb+ujYXYeIylbHJezIyNToe8KfEgHTCEYyC+/bUghYOGg8yMGlZ6vA==", "signatures": [{"sig": "MEQCIH4lQJOxpuU+3wgIm24Qn602Mol8GMOV8TVicX73dPyMAiB9FcF0ApqTNalDzEzLP0/P7iB+9/zAD/yPuZdD3vbR5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNeIzCRA9TVsSAnZWagAAhGYP+QC4hThuB1gP4Qw4yLML\nduXX52UvoV0pynzsjr8hdIKe94RKSX4Lm7DASeicNiuAZa8YWU81gL29NvPq\nf3D6Rcf1yBCCV/sPEbcAtDAw95Y9/B7s9IqF/V233Z4T0e3Amkdyae5htOx5\n5i7ZhvK360AlW00N7fAJVwUyatEplkd5tK7RV5tMUJ9Lhbu96gYBi4XxrVJi\nmsqk6RcTYWVzMiJ7V5dj6myLq9TKBEL8qSnvOMrwGXQwQNg12XlylMfABFVH\n3cTQwvIvMfEKNmAOrVerEhbMD8Ilq9xuxQE37PtwMXOCQ+J1qtvZRtQVwV+U\nZZ5xJwsJBcXK2FZkne0e1c7KFoQh3L1rmAFwOwww244BqmrwIA+fXZr4CK10\nU45j7bRsrTAhF3bhoeKFk5Kl4iLvKbMPfHaJmeA5wNju7JQ+11MCFgZLco9W\nDjBwsfCunxt+s5TfYQ57N19iwGYvNON8KBTfjIjkomhWJkIKy+LDI0BdkW3y\nOsjG5o3/DfVnaGY6mGig3NCWz1N6D5QdWs2N7TlNegJmKHDJmSeqGFRie4Jf\nrRdFyaQVkTgpqhwO40wqS2aWeuzFtWyKHQ1wl1rWr04ZLecbOFhH3btXq3pF\ng802tkQfuex2V65rnPjTe0rf34CruPWQRH0476Aetm51Aio71Mc0rqzjHmMK\n7I9K\r\n=WT4l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "4187a22b147fb8542d0a20b22bbca1c361c8807c", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "tsc -p tsconfig.json", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn changeset publish", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "release:prepare": "yarn changeset version", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^25.1.0", "jsdom": "^16.0.1", "mocha": "^7.0.1", "serve": "^11.3.0", "eslint": "^6.8.0", "cypress": "^3.8.3", "js-yaml": "^3.13.1", "request": "^2.34", "prettier": "^1.19.1", "jest-diff": "^25.1.0", "minimatch": "^3.0.4", "jest-junit": "^10.0.0", "typescript": "^3.7.5", "@babel/core": "^7.8.3", "@types/jest": "^24.9.1", "concurrently": "^5.0.2", "@changesets/cli": "^2.5.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.8.3", "@testing-library/dom": "^6.11.0", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.8.3", "@typescript-eslint/parser": "^2.17.0", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.3.0_1580589618499_0.26751826791820643", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "dom-accessibility-api", "version": "0.4.0", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "2d351f6fb357641e73b388cdcbbfeb601d905dc3", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.0.tgz", "fileCount": 22, "integrity": "sha512-EVEnn0nGCeLd2iXkaok+/Cjc0nVLMZF7qKOV10jkW77tRClkSiD5CCYjZ8P2Rx9Er6rS7nDreoARDP4UBy5gDQ==", "signatures": [{"sig": "MEYCIQDSRvqtHtBeEz/q7qpZk3BIWtyiZBTgY0WpLHEAiisVuAIhAJoGEkNLibQqCCE/xRCHDqiiExbcEGFfyjKPQGmGTwco", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebkUoCRA9TVsSAnZWagAACE8P+wVLJbwj2zMTzrW8vIBY\nTebCldg3OuzKjlATP8xntwooDI3zhtUbBSLPabSGAXctNx4w4/RISVQa8p8y\n93az2yweNEBHeGDr1oljlgR4Dsav6rL5E+1YpWOSmSp5DHnddc2jKxlO293R\nf4VtfJQAune4oHJ2IeR4h1073WR6k88rUsWxZE70oiiBBzJuBlzMdjeyRIPU\n7uTxQVJ2N0SRqgsfFff7TanNQoGT0QnEBc+WqE7c+THTm3rLyGxgWEk7a3b0\n09zvMtcxewXoMw2oMe2IO04rJuJi+MAAAi1cawtCQAr5I7XYZPeKKkyRBpT0\nmEbq41ht0MYDj/VrpWIMo3sfE59KfdZe9SLunex6krjg6YnHJQA0oNS/eq/O\npgq5bytd5hcQEQCdZ668V+XQJGOMj57wP2QKU6vlNG0pWgsxSRsO7Uaxy5+a\nC10gxbefjv+ZNl4Y8Csf4jh1uyeJDWLFUMfPbmaIhuAOJf+0O//Ey8dKpM27\nKU9TGBLk7jfUC0VTlk+QzrQkQPdKyTD9KoNB28PS9B9pbCNylISxumSve/Yh\nahSu2cTv+EVppXAwaPGqnBkdVJAkGaJOuvYa7mil1R9UN7Mfxa+KQlxK6yrD\nVDKFRYTtGRoyZkwBRVJ15Tc3cYorsBDp4314Sw6PhJRM6YlQUnnSo1Cai1R9\nkydT\r\n=z9zE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "8235d1415a044b8f0a7173e55e688072899039be", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "yarn build:clean && yarn build:source && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn changeset publish", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --source-maps", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "release:prepare": "yarn changeset version", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "dependencies": {"core-js-pure": "^3.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^25.1.0", "jsdom": "^16.2.0", "mocha": "^7.1.0", "serve": "^11.3.0", "eslint": "^6.8.0", "rimraf": "^3.0.2", "cypress": "^4.1.0", "js-yaml": "^3.13.1", "request": "^2.88", "prettier": "^1.19.1", "jest-diff": "^25.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.8.4", "jest-junit": "^10.0.0", "typescript": "^3.8.3", "@babel/core": "^7.8.7", "@types/jest": "^25.1.4", "concurrently": "^5.1.0", "@changesets/cli": "^2.5.2", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.8.7", "@testing-library/dom": "^6.15.0", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.8.3", "@typescript-eslint/parser": "^2.22.0", "@changesets/changelog-github": "^0.2.2", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.0_1584284967722_0.4939009947301185", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "dom-accessibility-api", "version": "0.4.1", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "167a5b5c769a014ff929be808f6ee944968be3a0", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.1.tgz", "fileCount": 22, "integrity": "sha512-JkV8hEagS0NIXI0jjMMxHSI7nuKm9Vsm7/DyGRTnuMU+osT/EJfj6xr0gHEDgzLKINubzAZzcStQ2W2+4JEUtQ==", "signatures": [{"sig": "MEUCIQCHiBqQlyawLU/78YZx5MPZN0SzB6bsGfMjpSNkaORR7AIgEoO1WJTjVH+9YRmbqsQsS3QRmWVatQKLXqI4NI5BBaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebkdaCRA9TVsSAnZWagAAOaEP/iWDJUfYYsBxwjXE0HB2\nxW/qL/M+Q2rVnez6OQzSg+amnnm7BkrfSs8SNc7hqckc3si14wJwCmVNAPRn\ns/H8f1pdl++A1kmeM8Na/V/nTLKSeZhDwlnBLt2mx1186XBAIHzDTf8Obeth\ntBDaBB1L1bXYMZqecqJzjrMYVdGJ/KhM8Qn0YzjZCBYnS4UFQb5TmAAfTyul\n5i+/PMvyzONLfOXtFGPTcozp33FqTJsiHU+eefcxEnLfBh5ok6ReU8hsXOX9\nySA71/dfTyMeKHOZewXeV3BwaqYeQCKL44r6QIFG5mRuOa6d/mOG37jqBNpK\naIi3BfwcgEueYJJh/tsDvaVHsivAeReh1++IyBuA3Kr54YRW6JwPhVu9lj6h\nEZ97Q5OtKBVSlC/NLQug8gHBtnL2TmCoZmIQhZB5pO/MDNknlHNJS1wcFn+D\nKm15wBiuxSuXZ2Qqcnt+EcRg26SsvLZsZkKoQ4KrwJzAO8eOlvuV6vaSAetG\n2SzFn0vqm3PeqK06e+4D1VMZhDeX15zWko99hKkFwL2iNqaLV9M9m/1EIh6W\nd0NlKn72QpqqvqgFbvSxabMzI0fBanrBpsCoiYEi+m04ZofQSbrXoq50F2Bn\njQQVE5b2cROfRMEoewOIj/gag92/aCL1MEYVbzJRFdS87m45NcKdy5Le5b9V\nip5N\r\n=iQE9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "477074c2e54ee5b5f024065a4554c77304349ae3", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "yarn build:clean && yarn build:source && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn changeset publish", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --source-maps", "prepublishOnly": "yarn build:source", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "release:prepare": "yarn changeset version", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "dependencies": {"core-js-pure": "^3.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^25.1.0", "jsdom": "^16.2.0", "mocha": "^7.1.0", "serve": "^11.3.0", "eslint": "^6.8.0", "rimraf": "^3.0.2", "cypress": "^4.1.0", "js-yaml": "^3.13.1", "request": "^2.88", "prettier": "^1.19.1", "jest-diff": "^25.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.8.4", "jest-junit": "^10.0.0", "typescript": "^3.8.3", "@babel/core": "^7.8.7", "@types/jest": "^25.1.4", "concurrently": "^5.1.0", "@changesets/cli": "^2.5.2", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.8.7", "@testing-library/dom": "^6.15.0", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.8.3", "@typescript-eslint/parser": "^2.22.0", "@changesets/changelog-github": "^0.2.2", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.1_1584285530432_0.6320269784699104", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "dom-accessibility-api", "version": "0.4.2", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.2", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "e19ef39da9a5858f6f9af225113f6ed302ccdf70", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.2.tgz", "fileCount": 32, "integrity": "sha512-vKxUcEtM9bVB7eDv7jTAuOLl5L0724Pk6iHxz8KUIgrWl53TTA6MuZ0g/bUrnEx3Gsf9SIJ1LiVtjfo0rg7iMA==", "signatures": [{"sig": "MEUCIQC+O+uZfc+9UwoxaRhh1YVsYXdLVPIMvnznqqKWLt7YGgIgNcnNYv5+XpTIgHX1UPqebJxJ/vZ0xwzMfVNZjpzYhhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJednreCRA9TVsSAnZWagAAEAIP/3r9qJS5jyO+yRSGaPjg\nbhLoCnlkKtsmmk3rbtmvMshdeU5mSKd95/oBC4L6XXrRtehnlCnWymu/ISAf\nLnaRoRa6WBc6of/ilbfbjcziZ2SYCHD/Lv9tzU8PM64ZXrEqs4Z52t9u1nAc\nwd8FHWJ0xMELLj/gXA9b8BY8AAZA+jkDDqMITf1m63bS49F09ZTlZPICRwLb\n7npqZ7bryV9C+cDpIDfyo/BZMjT9qybUphca7dsGIruHHA4FCIEHqtpU8PaU\nEI85n0QCoAmesqfY+rPFzUkH8P9hb1vTDMwoCHPwAGFNLipZ4eaLcu5KE7Az\n5UXK/S4X6CgU2p16nrM3tCvexpDnr3mnisxbtWHi/ksR7Z+NAMcK8SFU8vdG\n62PKt2cES7sqj3veVUoklRiyheMFpLeNbBa8WuKza4mXcdA6mW0/8QUN+zVr\nMUqmMgojNMEsEefdRp0rIol6aUid2ET83A6+flj/DaEbI79Klr80im+RQFtc\n9SZtZfsxpoUkTuF5iHQ4pWdIRF853K01wtbEElGRxMz0CAniRCGnM2A6iy0j\nnHrjDx28RDa356EKgTVBPnfRTQTTEFyakuFwQa1FQAy1rTXwM66bQSZ3e+ji\nMLXYw+0eELxKQGheKWVw5DLQMGYPQT8fFcAJmYZC+FAIxfEUOkmv4kTlcFDD\n0NCG\r\n=s1kI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "exports": {"import": "dist/index.mjs", "require": "dist/index.js"}, "gitHead": "0c4b4de7f39cc53c2959c73f9d2da12efea9afac", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn changeset publish", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "prepublishOnly": "yarn build:source", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "release:prepare": "yarn changeset version", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "dependencies": {"core-js-pure": "^3.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^25.1.0", "jsdom": "^16.2.1", "mocha": "^7.1.0", "serve": "^11.3.0", "eslint": "^6.8.0", "rimraf": "^3.0.2", "cypress": "^4.1.0", "js-yaml": "^3.13.1", "request": "^2.88", "prettier": "^1.19.1", "cross-env": "^7.0.2", "jest-diff": "^25.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.8.4", "jest-junit": "^10.0.0", "typescript": "^3.8.3", "@babel/core": "^7.8.7", "@types/jest": "^25.1.4", "concurrently": "^5.1.0", "@changesets/cli": "^2.6.1", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.8.7", "@testing-library/dom": "^7.0.4", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.8.3", "@typescript-eslint/parser": "^2.23.0", "@changesets/changelog-github": "^0.2.2", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.23.0", "@babel/plugin-transform-modules-commonjs": "^7.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.2_1584823005956_0.6070197393356449", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "dom-accessibility-api", "version": "0.4.3", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.3", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "93ca9002eb222fd5a343b6e5e6b9cf5929411c4c", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.3.tgz", "fileCount": 44, "integrity": "sha512-JZ8iPuEHDQzq6q0k7PKMGbrIdsgBB7TRrtVOUm4nSMCExlg5qQG4KXWTH2k90yggjM4tTumRGwTKJSldMzKyLA==", "signatures": [{"sig": "MEUCIDJfgcQdQJdUHZM1ZFhx8BKVJBMU2U8klsqitEXQILp2AiEAoYdP/UoHspd3rTxGol85ab0YDRUf1mwajKlVMqi4SUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeh0sCRA9TVsSAnZWagAA7m4P/10aDRJA60Z19G2XBkXd\nIRvG1AhchwGVA0nGaL/ZWi71QdjilY7YGI7bazCIh3UiZ98d+IC1AQsbbEof\nT4FgH2SbHwQNpEM6otKVTWTK920XbWe37BAEQv47z7HdADIpNqMVI1wCXgFQ\nviUajpUS/lUzWUrlDME5D7m9eCWDcz5c+FsY672mpMaWXByzH2BR3nJNjeB6\ncZlsEIZQvP65JA1M43RW63UxMnNdVOXvxT5hWTkgXAaWxQr/eLtZKB9V7R2E\nXsuq3nNtG9Kf6DvS8Azubv6tydA0oviYr/WnLyDJ5GVBIs/0BEyoKVqmofGU\nx3gKTlGD9+G17cOPXuqMUFMiYpM+artIhvlvaYk+FmHv6ncXcBEKOncfKxJr\n3GKOcrzuk7tphevHqLmfSnAgnc84g6YNsXCblG/DFuEwrcw6xVuxMkYFyGbQ\nk8GrW80nGCJBKfpmDTva8zTRYvqsKrE7tOZ2EaBr9qkmUwos3ezQp9pN2Pe3\nHxXlqxToR7HK3zhqRjS3abIBID5B4Ffjip1VrbX7Kbk8qFXia4tVlzfaVc3f\ngQaTvXcdEiV1sX3E40fYU7rymRSanGBfISD0xL83BmoEX91scGl7ycY0UUiO\nP7LfwL6zgZaD1SfVQ62EzzTd2WoeGtyCE1thciRpE85vQcJBq8u9GRP49GGG\n+QRS\r\n=NX24\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "5e27c02f1a618a67cebea084ce4975fbea0e1d71", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.ts\"", "test": "jest", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn init-wpt", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/minimist": "^1.2.2"}, "_nodeVersion": "13.11.0", "dependencies": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^25.1.0", "jsdom": "^16.2.1", "mocha": "^7.1.1", "serve": "^11.3.0", "eslint": "^6.8.0", "rimraf": "^3.0.2", "cypress": "^4.2.0", "js-yaml": "^3.13.1", "request": "^2.88", "prettier": "^2.0.1", "cross-env": "^7.0.2", "jest-diff": "^25.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.8.4", "jest-junit": "^10.0.0", "typescript": "^3.8.3", "@babel/core": "^7.9.0", "@types/jest": "^25.1.4", "concurrently": "^5.1.0", "@changesets/cli": "^2.6.1", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.9.0", "@testing-library/dom": "^7.1.0", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.9.0", "@typescript-eslint/parser": "^2.24.0", "@changesets/changelog-github": "^0.2.2", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.24.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.3_1585061164019_0.9508547707697981", "host": "s3://npm-registry-packages"}}, "0.4.4": {"name": "dom-accessibility-api", "version": "0.4.4", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.4", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "c2f9fb8b591bc19581e7ef3e6fe35baf1967c498", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.4.tgz", "fileCount": 44, "integrity": "sha512-XBM62jdDc06IXSujkqw6BugEWiDkp6jphtzVJf1kgPQGvfzaU7/jRtRSF/mxc8DBCIm2LS3bN1dCa5Sfxx982A==", "signatures": [{"sig": "MEUCICPJxySlLOh8pLuASEoxl/oDqC8uxs1eeUwskiI6q7MlAiEAnMgimWe2Zm0fp1hxS7rjptLuGn5DxkpwWjZQxFdL/Tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewl58CRA9TVsSAnZWagAAD+8P/3BAp3Su2JKIyLlXGKg9\nMxCZvU+waAQlFdTsA0cWXiTgP6zCMiE6UkdmKszCzCeR0F3fLIMbrIYQKxQa\njtOefPqHdwvAjT2yov7iJoKWBaOsbvAl9KZV/dZz9spPAwlOAlD/jKD4mbzd\n/aaFgv3fi+CRqr3QSM29XVTNT2Id1wT5ovMycGTUaBVv3InTA5G1fdk8aNLk\n4Je9DrbPbMyDkHDiKpvCYZBdusMzAYEdLoSztuR6fbhCoteEErtCh+10jR6e\n0Xj9eC+1fiwfM2r4sKJXvEO953zKOKvSP45QNIS0FRIYhWkS6rIEnas51eOO\nbepS4YMOjblu2ZdoVJvzh0b1avYDZDCsEafuwSz2JRy5jQwUbQ4deFYlWkcO\nD/w9sekunJ8KWBS3f7ADTzaIMuZ/ZJotdHiRLR/qKSZoRLdx03TRWzAx1Y9B\nZMq553iOo0AX36cphRCFznd/x+rI+SOZm63x/wdXQ0F46DjRDZjx/tDUptsu\nvkeuPg4CvnQK7L+WCv7L6fNZrZFxKXuJYPLEJyV2Ehsuv3jApxQ+ySuMeWB0\nc1/AcoLpqRwqmXBPOPQ/15zdShXIQXS/JJCbIAmt7qs6ruw2vKy+HbIfsNoy\nXE/vvhlIpqyWxWO10qKVVbCL2cJokV+2j63xSvVWEm0e+5nSAlEf0teGbPMZ\nDFUw\r\n=LmtB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "d53ee79e6ccb4d6c3e765c338d10ea01f50f965a", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "14.0.0", "dependencies": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.0.1", "jsdom": "^16.2.2", "mocha": "^7.1.2", "serve": "^11.3.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "cypress": "^4.5.0", "js-yaml": "^3.13.1", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.0.1", "minimatch": "^3.0.4", "@babel/cli": "^7.8.4", "jest-junit": "^10.0.0", "typescript": "^3.9.2", "@babel/core": "^7.9.6", "@types/jest": "^25.2.2", "concurrently": "^5.2.0", "@changesets/cli": "^2.7.1", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-jest": "^23.13.1", "@testing-library/dom": "^7.5.6", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.9.0", "@typescript-eslint/parser": "^3.0.0-alpha.25", "@changesets/changelog-github": "^0.2.6", "jest-environment-jsdom-thirteen": "^1.0.1", "@typescript-eslint/eslint-plugin": "^3.0.0-alpha.25", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.9.6"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.4_1589796475653_0.5712603663474423", "host": "s3://npm-registry-packages"}}, "0.4.5": {"name": "dom-accessibility-api", "version": "0.4.5", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.5", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "d9c1cefa89f509d8cf132ab5d250004d755e76e3", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.5.tgz", "fileCount": 44, "integrity": "sha512-HcPDilI95nKztbVikaN2vzwvmv0sE8Y2ZJFODy/m15n7mGXLeOKGiys9qWVbFbh+aq/KYj2lqMLybBOkYAEXqg==", "signatures": [{"sig": "MEYCIQCCVEtZa1GiOE8yWzEOqjxt0kbS4wgSE8N7buf48/uMJAIhAPn+l7KiZ0J7jUi9csglx/+TmR2G+YHfH/wVaLljorqX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2mffCRA9TVsSAnZWagAAuxAP/2X04BKfeWNBNg/Yy6Ld\nIAG0FEFJt+wNYwW0zOHBXxCT6xeHldC2JfveMnfHwpbmL9IafVtH/sql3kbQ\ncB2VeU5umrT9f0yfGRXe5giWjY+cNiZoPnocpPhCT2GXEteIeBBFcnnxgHhJ\neS1k/eG76L5U2K1twhgYqYC5WwaqViZyiCYqpVEkeWCL77CIurVGONMO5wq9\nZNonUtTRssPaRsTQClTwlOyBi2GH3bn9N9auYXVJh7jcsCQpTkI1SY0mQkC5\nXBvFskHbKN/FnAnybSYGWo/qjsIphaxFRjENr6pQ2lXbs/aoIJjoLrTeM/+J\noO84DgtJvQazLLP7d+bEnIh7OCk/byteBQzSquRa6JmlmNp1RdJK+YCDkCtl\n05H9GtAH30Qi69bE1XIeQotx5WTEpPAKSE6Gy9wu6SosfZB+7zcPHz/pxpPm\nWwO6CAQ48iGnb+3AjEafdCvpDI1/rGzoVM7rWMM9GBIbGa0Sejhndy9fJtsW\nkSLR7ejt+UWTw5F3UOOZEyEpZCBuF/JvfV0OB6j6niegg27uu0KfXDUVdBbK\nC9C9Onid1VPdzRqvApiOpGei16DlFZZysgZg96A58++Zb47FvfuvaShsn3WC\niq5SGdre7cqP98lzw9yMnkCmCsOh9oRJ/hpPO0zSH6FHNhMG/k338lGTAqIz\nVAsM\r\n=rcvF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "909e34dfde00b5116a4e399160f8c0672f24d75d", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.0.1", "jsdom": "^16.2.2", "mocha": "^7.2.0", "serve": "^11.3.1", "eslint": "^7.1.0", "rimraf": "^3.0.2", "cypress": "^4.7.0", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.0.1", "minimatch": "^3.0.4", "@babel/cli": "^7.10.1", "jest-junit": "^10.0.0", "typescript": "^3.9.3", "@babel/core": "^7.10.2", "@types/jest": "^25.2.3", "concurrently": "^5.2.0", "@changesets/cli": "^2.8.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.10.2", "eslint-plugin-jest": "^23.13.2", "@testing-library/dom": "^7.7.3", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.1.0", "@changesets/changelog-github": "^0.2.6", "@typescript-eslint/eslint-plugin": "^3.1.0", "@babel/plugin-proposal-class-properties": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.5_1591371742378_0.19841374335280038", "host": "s3://npm-registry-packages"}}, "0.4.6": {"name": "dom-accessibility-api", "version": "0.4.6", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.6", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "f3f2af68aee01b1c862f37918d41841bb1aaf92a", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.6.tgz", "fileCount": 56, "integrity": "sha512-qxFVFR/ymtfamEQT/AsYLe048sitxFCoCHiM+vuOdR3fE94i3so2SCFJxyz/RxV69PZ+9FgToYWOd7eqJqcbYw==", "signatures": [{"sig": "MEUCIQCfDpsznmt9QRyprD0seiO+u0lWY4OfMPuMo59ybJkCZQIgIJvE6Bp/2brG4/ygJCTJLxCgNs/RwHF+4IBNZP8vzr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDNEeCRA9TVsSAnZWagAARg8QAIdj6VSkSojfl9SVDti+\nvKdf9QaFihIgTeHPXuKLJXtkgSJiQVfLmvOV1eq5OT8dqpW9a/DZK5gAQdft\n8UQ1bfNr3TeOeXidSAEFkyqM25MeB5BbUdwBSTn1NTxMHiS/Dhfpj3N4URRc\nrd5uf762KwuGeLM3jQ95rAgdL5J/lKqnO8mg1FNkcsXyUTgGVKkcdaDv5oTH\nB/7I5qLWY6R4lcrA9i3Y3fSvzthZcDvTKZ14x0te/YgUXLhzAJn0iIKp1kCh\nR0Pdxi54HdsF8mb/3v05c0lRktsOf/Z0GvacdIJZfYyynGjCohuCd9tLJy9n\n/n6klQhfwNkugXYu+d+gFwJRllDIiouIYkQeiKMQaJBHYY1TWcI+P+TViulw\n3y3Ox7OfhqlTK32ugkagmn9QqNwwuHfpdCuPJ6l5aQdKD8jvIUnO+l9ETDWD\nY1dDoEl98wbuxoS3KeXpeW56AahznfGyx/e68H4HXAmsfYn3PVSGeGuz98J7\nY5Wqf1u3TsotXhsPFF+QpUjKORSeziBsc+8tMWzZHxGefYO7P5dard8ZzPTw\n5ENB8SwW126a/33pw8qTPrODTSBdm5meBanBzlM1T/vpseqOl9DjqFuTAICM\nM1c18tmYFua2re9DgOBUWHmwyICNg0TY7CSMNyN2oiUFCH7cdXdZMnDguywN\nRLsk\r\n=uVHl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "03c05acb99c047f70aad2575c82a831ebef99f34", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "14.5.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.1.0", "jsdom": "^16.3.0", "mocha": "^8.0.1", "serve": "^11.3.2", "eslint": "^7.4.0", "rimraf": "^3.0.2", "cypress": "^4.10.0", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.10.4", "jest-junit": "^11.0.1", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "concurrently": "^5.2.0", "@changesets/cli": "^2.9.2", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.10.4", "eslint-plugin-jest": "^23.18.0", "@testing-library/dom": "^7.20.1", "request-promise-native": "^1.0.8", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.6.0", "@changesets/changelog-github": "^0.2.6", "@typescript-eslint/eslint-plugin": "^3.6.0", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.6_1594675486000_0.6551954860665261", "host": "s3://npm-registry-packages"}}, "0.4.7": {"name": "dom-accessibility-api", "version": "0.4.7", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.4.7", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "31d01c113af49f323409b3ed09e56967aba485a8", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.4.7.tgz", "fileCount": 56, "integrity": "sha512-5+GzhTpCQYHz4NjL8loYTDVBnXIjNLBadWQBKxXk+osFEplLt3EsSYBu2YZcdZ8QqrvCHgW6TSMGMbmgfhrn2g==", "signatures": [{"sig": "MEYCIQDuezzfHtTKt6ZfNg6EMFWn4q+VpZJNeDnnR/+ndFmqoAIhAKKJC6urnMNSn4mU+Cfm09oFOBQliWFfrS+gmQkpqAV8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfImv7CRA9TVsSAnZWagAANRcP/3JuTjAPTwItAR/mO2Eo\nV3m9xtkeLE/g08kc7ghGDKpxHoogB6fUtu7DVYJe+sRC83Gq1hmgXdT7Pl0A\nOAoP/ruaQLNMru1FAS30WjETXuYyq9jmEN0BBnwN/I2/UoZls+4QJkXWsaTH\noD7BYrpsgh0dpIin79Y0u8GuwdeGy1SXOgU5SpQvPxO3G7jiRIPBWn9WhMHK\nH9v4895MB+xOcOHb377rNJvuW+qxIx6pStVmWnTdGC/N6xwHoU+0GJh2xe8h\nvsZQuIT5yg/oOCRNTjQX08Nad+D3Dq0Qno6HcjixU+R+x5FZu5A1PEZrGB8n\nXATfE8k8RiE55xe/TB1YGUH9Avhnta3uXXwdsraeVMrd4/78eHf123uR/B58\n5vFLPtlk2H+1I1e8EOjN8FFErd2CETDGENmvUqkOfLyzEIBWN9ekBKGxLWbf\nmhwW9ZeKBjjcCgWCt7kp6Dqm+/6pBiLDmY9OoKK0SeTj7aDE88Sg7fqt+z+P\nUgK0ZG5DSjlcL9UXIPptzpL+prfWfsB6v+x8B0R8tWy0rOMZLAa3NUNw8586\nZy7dHSd37NZoZDvhAT39eP3Ok7k3LRprOMhJSQxLw/5glAsk5I+Upm0l4wt8\nKsi+SL2DuPlHU5s5oREH/JsPz3nLKufqwjo2NtT2b7Dq/Z/7dlLzJCKmidYh\nowad\r\n=75b3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "c3a99fbf615ca2f707ddc4cd4703133e503e5503", "scripts": {"lint": "eslint --report-unused-disable-directives \"sources/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.1.0", "jsdom": "^16.3.0", "mocha": "^8.0.1", "serve": "^11.3.2", "eslint": "^7.5.0", "rimraf": "^3.0.2", "cypress": "^4.11.0", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.10.5", "jest-junit": "^11.1.0", "typescript": "^3.9.7", "@babel/core": "^7.10.5", "@types/jest": "^26.0.7", "concurrently": "^5.2.0", "@changesets/cli": "^2.9.2", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.10.4", "eslint-plugin-jest": "^23.18.2", "@testing-library/dom": "^7.21.5", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.7.0", "@changesets/changelog-github": "^0.2.6", "@typescript-eslint/eslint-plugin": "^3.7.0", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.4.7_1596091387014_0.06756901462233089", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "dom-accessibility-api", "version": "0.5.0", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "fddffd04e178796e241436c3f21be2f89c91afac", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.0.tgz", "fileCount": 56, "integrity": "sha512-eCVf9n4Ni5UQAFc2+fqfMPHdtiX7DA0rLakXgNBZfXNJzEbNo3MQIYd+zdYpFBqAaGYVrkd8leNSLGPrG4ODmA==", "signatures": [{"sig": "MEUCIQDPu1KLk5VK/T6yIa7Bu0CiFni+hSLTENWY87OohpKn+gIgaD1pWPhTzSryMNVimuEDvrjguSTrwskqjF4J8hWOLG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMcCzCRA9TVsSAnZWagAAePMP/R3IG/GHVm10ddeTXsqn\nDF7Fidp6fwD7x8p/B2FYMv1gl/nzlWxYumEztGzvTamnzeo09WPxYrD9HZmL\nMexjnlAw9H0+t5Arj0GEi+zj9UsuU7RRh1J/OMwla19iL5m0SQPbyohyK2/v\nj3i18G8VA5jaj+4Nncu59Ud7daFNYAV1q8VqQVgp12zxADnOkdFxu/JNye1V\nRT8divgJBQtHuTZOxccna6HTUwg/VYQGPxZadm+VkWQVKnLuScUZSUhC9vhN\n4mLbgVc5jqgP/exe6OBYDul5sVOoZIoxCTteHbXSeZmALyYtKU53F+Q2XbBL\nuGZtfsfIsnmFZqZEj4LsgaUogcHUToJTFRL6y9oP4w0jqbI+cZvwNZdJA5UZ\nH7XHpGI/LIlc5lf6ofHxD36bpqvMoGOXUdud/liaNjH0SWb4tiIcJBT26WcD\nRD8LnZUxfTIr/1JSt0eimwFicLNoXyvY2LMswofN5CIGHYyfD6Ys3HKBolrd\nc5KHWNynncSr5yKgMJ+NIxVQrOnIcmqjmCVjIDP+pk4pL78hQ338Dauc9sfZ\nZJqeNnZ4d5sOeSdYpv8jlqE7/8CHPVvJOT/HcyLh3ghg3a3zFlJT+/YbRQKu\nrpWXJWGGS/V/abozj+jxdbU6S5x1I5HQsvlTeST7sJ5qKKsSIRjTdaxTrgw3\npodT\r\n=xh4k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "1715af1ec3c28c9861eabaad2ae6e98179602b2e", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.2.2", "jsdom": "^16.4.0", "mocha": "^8.1.1", "serve": "^11.3.2", "eslint": "^7.6.0", "rimraf": "^3.0.2", "cypress": "^4.12.1", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.1.0", "minimatch": "^3.0.4", "@babel/cli": "^7.10.5", "jest-junit": "^11.1.0", "typescript": "^3.9.7", "@babel/core": "^7.11.1", "@types/jest": "^26.0.9", "concurrently": "^5.3.0", "@changesets/cli": "^2.10.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.11.0", "eslint-plugin-jest": "^23.20.0", "@testing-library/dom": "^7.22.0", "jest-environment-jsdom": "^26.2.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.8.1", "@changesets/changelog-github": "^0.2.6", "@typescript-eslint/eslint-plugin": "^3.8.0", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.0_1597096115354_0.7196334613766873", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "dom-accessibility-api", "version": "0.5.1", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "152f5e88583d900977119223e3e76c2d93d23830", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.1.tgz", "fileCount": 57, "integrity": "sha512-8DhtmKTYWXNpPiL/QOszbnkAbCGuPz9ieVwDrmWM1rNx4KRI3zqmvKANAD1PJdvvov3+eq1BPLXQkYTpqTrWng==", "signatures": [{"sig": "MEQCICfKpW9jrPjX54xN1Svu+RhXujl+SoudFO6OL1/XU9wxAiAGieFx/l6ZanwZLDJJtrUzW7iJvefqgKi/IGPOxOxmGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPp39CRA9TVsSAnZWagAA7YIP/03YRJFypWnEeOr/nm0w\nbsFw8/qjy6aypuxDDy1bhcJr8OXcXXUbsS9dprAcr7HV//sg+EB7l6Y+B1zs\njK8Z8rO6D/ifjs7ZwGR8RxMnVkN0/8Z8MfxG/77nSyzIbiTuwmDC3QSxThdM\n1xBze93rMNSP1+2e+7D/I9e+TQkOTNMw3nYK7ZVtGh3oUr4fGhYg5BE7FaBV\n1mJeHTDSAYWy9Bxv14BjUmlmompJerK8NP/JqSLbazo6VTJcFlVw4A/7VM2s\nB9UyqSl9YEP4/CUZAgpnrIGl9cb/aq+OSQvGhEGL2Ah4TP59OSMmK1c+beyV\n13SoBaFbEif4c5hN/vJFrb6a0yuoQUKE0xhRKPixVJ/TTIHgSAz4bTcuNAlO\n+FDl4aoeRTKZItKFHGoVPKTCj97xrtNk44tkX6wDCYGsPondZiS/qRYrKTQS\nH+arqd1U+eKVp6Ekd3MCO1cRO8QBCCbwCN6UFGLPXPn1ngV8f7tHbAjj3dEB\n1qse+yGelAAaS5NSxQhtph7rMXls5IzVAgNW5VxFTNbxzYz6scWsdoXaX6Uf\njbn+4Z47KW6s1dqNyafZodnACeYC1pKyFJbD0j04GkfEicTjDMxJTRGIbTga\n1IHdWlErEJN5MH/ZPncau/i7c+OEikNA9ETn+subwBHie22ryTS/2KCghZ+3\n47fe\r\n=mO2X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "49e2f7feb3b5dad25215cd18799411870349207e", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.4.0", "jsdom": "^16.4.0", "mocha": "^8.1.1", "serve": "^11.3.2", "eslint": "^7.7.0", "rimraf": "^3.0.2", "cypress": "^4.12.1", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.4.0", "minimatch": "^3.0.4", "@babel/cli": "^7.10.5", "jest-junit": "^11.1.0", "typescript": "^3.9.7", "@babel/core": "^7.11.1", "@types/jest": "^26.0.10", "concurrently": "^5.3.0", "@changesets/cli": "^2.10.1", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.11.0", "eslint-plugin-jest": "^23.20.0", "@testing-library/dom": "^7.22.2", "jest-environment-jsdom": "^26.3.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.8.1", "@changesets/changelog-github": "^0.2.7", "@typescript-eslint/eslint-plugin": "^3.9.0", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.1_1597939196569_0.5497386838085467", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "dom-accessibility-api", "version": "0.5.2", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.2", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "ef3cdb5d3f0d599d8f9c8b18df2fb63c9793739d", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.2.tgz", "fileCount": 57, "integrity": "sha512-k7hRNKAiPJXD2aBqfahSo4/01cTsKWXf+LqJgglnkN2Nz8TsxXKQBXHhKe0Ye9fEfHEZY49uSA5Sr3AqP/sWKA==", "signatures": [{"sig": "MEQCIDToIb5/q97DcmFYSpP5n/Vo7yIWbiSXH1/zFIUdhvOzAiB3C5wcRSKic/2DovWeeiZ6Aq/gQlDIH7r94vJLIRDq9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRrT3CRA9TVsSAnZWagAA62MP/Ry3+CQL4bjymdGSRHu3\n3c9yo5o5VjO5Ke7ZRL2+L4LCb7zDHXlSYeIdMEPVaHCIW9Rq3G6iVyVYxvkg\naK44h1YY2S7kAyZS7vb5C5I44+cvqhyG1mqCGvM8lSq/aTAxeA8/0GR9zJS0\n2CXD5w4UB6Thd0C+WoOzNPBgs7sk0g0BrE7ralCgApywZJXemabrgnhlcjmm\nGjcBG4LVoEiLb+PWj4jt3KEZKeoKCO+KjsErRjugQlOJ7ghj9A9tcvgiZrbt\nKBKUpjtwhgZLJildhfAJP0xZAyfI3zRxPyzHoSyMWaCJ7WbpWro051ey1yBQ\nCxlHByxJaF4x2I9kY3iNINBXZ07Td+JhktihSiZ9ByreHZxaBOeW+cBgJjqo\ntAw0Xey7srOpktI4Dg7TicohujVokzhFVE3qztsABLHk/TCQ+6+xCUN69rQ4\n0SLEJJnR3s5g1mNpWr9UlynrqXejlYXRkgCn91hazXBvdRJSkKxvl6cATEgY\nOO31tqoV/GlRQM6eA2PBC/C+2NOqEaUpvv7LhllpDhsgTpk8RLOQAG47ees0\nP7U8UBNsNNre6/7/HTrwhNqPVC+soy/qWH5J9aCcoZmPszB5EE9CKglg9g/0\nCsxHuciPc3I0U1djemlpXxGhxYmfWxFZicxxZx/e85ZK/2SYRK4amLasuCCI\n6gPd\r\n=ENDW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "c5124c1dd73047f450c75c30cb6908112c5eda67", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.4.2", "jsdom": "^16.4.0", "mocha": "^8.1.1", "serve": "^11.3.2", "eslint": "^7.7.0", "rimraf": "^3.0.2", "cypress": "^5.0.0", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.0.5", "cross-env": "^7.0.2", "jest-diff": "^26.4.2", "minimatch": "^3.0.4", "@babel/cli": "^7.10.5", "jest-junit": "^11.1.0", "typescript": "^4.0.2", "@babel/core": "^7.11.4", "@types/jest": "^26.0.10", "concurrently": "^5.3.0", "@changesets/cli": "^2.10.1", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.11.0", "eslint-plugin-jest": "^23.20.0", "@testing-library/dom": "^7.22.5", "jest-environment-jsdom": "^26.3.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.9.1", "@changesets/changelog-github": "^0.2.7", "@typescript-eslint/eslint-plugin": "^3.10.1", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.2_1598469366580_0.5334231506981808", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "dom-accessibility-api", "version": "0.5.3", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.3", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "0ea493c924d4070dfbf531c4aaca3d7a2c601aab", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.3.tgz", "fileCount": 57, "integrity": "sha512-yfqzAi1GFxK6EoJIZKgxqJyK6j/OjEFEUi2qkNThD/kUhoCFSG1izq31B5xuxzbJBGw9/67uPtkPMYAzWL7L7Q==", "signatures": [{"sig": "MEQCIHJ3EUMv4uNlcDAcNvVcIvQIQUuym5upK7sAZPWrCMoIAiAahPyE8HxE3M6uxqYfM2yaEKQovB/D8my6lNt6aVxOTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcdoyCRA9TVsSAnZWagAAhVQP/2IUDYY/12UEFniv+R3X\nZo9T9tewzKQGG7Z9R/aH4dTSP34I6PJTqi9O3EXwPpsE2YlLRvdWXcTtdvkQ\nfTq01Az3iLFCPzXim8rQOZR2xL0B4CcX1eXXBw7mnoqQVx98ltAY6nMlYRLp\n7+Ycq70WtVD7MmeGVmSo4lQNTKBx5yi4GP5sgvMHP7GJ6CXpyUJ6SbnUR/86\nYRl1MCBGAycM4MXsIg06VGBn9V8DwTwjCNSN/4rps0hIIhU9G4l4oGUjN7Q4\nsqDvYU9BuxuuxHOFvhgkQFv846prmVIOY/8ZUcPlFzitPokxTVD1IDcGzCaA\nPog/ouqH7pRo+QDbTJBBuHwiZbIu1Qs8pGiauf1XMdKmQvAAj/7HAnvQr7Dz\n39c29Ga2rEXZvhtrNEJNuE028uvH9wjGv99Zx1uU1mfUBEUOorw6Dg6Agm4t\nEQnOd043LhWCDPLSyINeaBBFM6U4F6wP4vt7+A7mCXlar+m0y1T/4mCO3pGx\n8p/Omvd2eyWcZt/aNU5BJmHi1cG0kFrtmji0jYhwkuaIpVlOvLIRouku0cQB\nLT1+GEsAjjFB5zmbWb16PgcvjepMizxjOeybDtP0VAQqQMzS5eQV332CMGqx\n1sZx9IYCQuFUYoG7nMfnzk5nrMwJzvqd1MzLruvDGLB/8QN4z1AWua3Kljfi\ncVwT\r\n=2js5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "443e247244f2905b7c2d400ecb3339c59ea677bd", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.4.2", "jsdom": "^16.4.0", "mocha": "^8.1.3", "serve": "^11.3.2", "eslint": "^7.10.0", "rimraf": "^3.0.2", "cypress": "^5.2.0", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.1.2", "cross-env": "^7.0.2", "jest-diff": "^26.4.2", "minimatch": "^3.0.4", "@babel/cli": "^7.11.6", "jest-junit": "^11.1.0", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@types/jest": "^26.0.14", "concurrently": "^5.3.0", "@changesets/cli": "^2.10.3", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.11.5", "eslint-plugin-jest": "^24.0.2", "@testing-library/dom": "^7.24.3", "jest-environment-jsdom": "^26.3.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.9.1", "@changesets/changelog-github": "^0.2.7", "@typescript-eslint/eslint-plugin": "^3.10.1", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.3_1601296946025_0.00009404776268095993", "host": "s3://npm-registry-packages"}}, "0.5.4": {"name": "dom-accessibility-api", "version": "0.5.4", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.4", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "b06d059cdd4a4ad9a79275f9d414a5c126241166", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.4.tgz", "fileCount": 57, "integrity": "sha512-TvrjBckDy2c6v6RLxPv5QXOnU+SmF9nBII5621Ve5fu6Z/BDrENurBEvlC1f44lKEUVqOpK4w9E5Idc5/EgkLQ==", "signatures": [{"sig": "MEUCIFhRwK3Tduf8OFFudu6J9QYfFO3bvrVu0lrLU+y2z18yAiEA6MmmHUylAMT/PQDqzodRhiYubdDqIvdBnjvE+izFGPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 242933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgEZYCRA9TVsSAnZWagAANvYP/js61J7adDd9TAZ5w/Vz\ngjuFSaG0FlrSpfGpqePWKGAVL4iS0rj3cNLlJKlC6ueG/7tHr5O4NGhzvCTQ\nHOkExucaoNXbdlNepCbuL/54EaU2a9KOrH4FdRreHmClJ13jqiAFn3AiIj2M\n+hFrtu8jk2fmykio8n0mYNiU37RxOcmmuii/VmIylZZzTEpgoZo5Hhk3xkwO\nIUar1l0r0Q2CI0H5lpLMuuyiyfjnFwN1pwyQf9fy/Si/Qvr59EuKWFE5YXBp\n8MZiv11NHenoY5mPEp3/2TFBXaRmshUUD432UcqyAJgOdZpm9b3ZVevfqV4J\nxYRdfHW8mCv3xCGHSaQbR7zyHPp+XVTzIhA/Za+wX7Iq03ntTjk+2WGrRF0h\nomCowuPzd/eMgx0ziFmMeXP9ds64s5iCUgxx6Ebjzz2C8cVrjR9+0o75KgH4\nxi4SXSii6zNoBsGmweGnuoMq7m/OE5w6aqNiwhoBl5a6znrQcx9w2de8T+qV\npJ912q7rBRsLH7MsaKjCShzsYCgfj2RGi5xpnZYFykN7o/FTSzW9pHVojeJF\nHx3s7+W1bC5Uk+UqGmH6yrpWXUQqRnuu6PyAivuN/CeE/BMgzvPJRS/O6SwD\nqp+cps00tgWSvXvO7qkMTjYk6JKHsFCsldxPDhj1kVuw3uQRhLR29NeNjIzJ\nK72m\r\n=p0NS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "3f99f4303f5555746584d2de976d56c621366d1c", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api) [![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchNa", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.18.4", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^26.4.2", "jsdom": "^16.4.0", "mocha": "^8.1.3", "serve": "^11.3.2", "eslint": "^7.10.0", "rimraf": "^3.0.2", "cypress": "^5.3.0", "js-yaml": "^3.14.0", "request": "^2.88", "prettier": "^2.1.2", "cross-env": "^7.0.2", "jest-diff": "^26.4.2", "minimatch": "^3.0.4", "@babel/cli": "^7.11.6", "jest-junit": "^12.0.0", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@types/jest": "^26.0.14", "concurrently": "^5.3.0", "@changesets/cli": "^2.10.3", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.11.5", "eslint-plugin-jest": "^24.0.2", "@testing-library/dom": "^7.24.3", "jest-environment-jsdom": "^26.3.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.10.4", "@typescript-eslint/parser": "^3.9.1", "@changesets/changelog-github": "^0.2.7", "@typescript-eslint/eslint-plugin": "^3.10.1", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.4_1602242135498_0.42528815658090724", "host": "s3://npm-registry-packages"}}, "0.5.5": {"name": "dom-accessibility-api", "version": "0.5.5", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.5", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "8546a8f0574f6a3ec9160c610d14449128bd6208", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.5.tgz", "fileCount": 57, "integrity": "sha512-8qdiSHWFg/rzd/oDByhKtBCR+1QtpNPcHDr94m3K+SLkFaPEhdCPA/Rm9hOPN9WUK9mEfNXH1zaM6F5e0yhw/A==", "signatures": [{"sig": "MEUCIHuYNW+OJequV+igKpLYEFpmypC5X31rLZqGEyotyxdoAiEAg5GelNX6CLgXLeJC/q88/rTUw7AA0XhhV6bit0sUxjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtMgsCRA9TVsSAnZWagAA26kQAJ7m9Au6ro6oV5DosiJC\nvwJKNrHdWgNjdTijxU2yb61I3Z1YWMIyc9C36c79f0mE5MuUD9+oDRJkfQrK\nGOGagzdjjDa2QZQR6Bz0OAFMMSyj2JwU8460Ralv7uoOklBbN7vCG0npoB5o\n6xpvPyHk+l3vCcBMXWcmaN8l/GqmCg5BKHOWwKyxvI8dq5l8qRni/lK1pAyD\nCosG7bEtaA6jj6/Sim7YcZunbK4RsOIc/RQPIIvWGtst7t4sF1iHcRH2gDg3\ndLyn9wzkVI2X0GgQRkbZRwx+tNtv57ib4tmh21kmI7uG4I/yVZhy5Z88aEyc\n78HzA+LtJ+7lrPHeydvZjUccjqaeN3cyY6iBVjGZGZyNW7Aig6CHTIYDWGsg\n2m69eg2vsczR+YfCViEMq7nyhsasiPqtqEIISoSYy8g7sNbP+zEHE5AgfU5Y\n4ob50bwnE4zHX9mM2cyttf6rfj7NdXNgrdBlOUv2cCLo4BOGPPLC27p7I2vK\nwaFJVMl3nepZDOMtEgCM2ky6dFK34ogvo38lpT+x70gRENgnU0GfICwfT63q\ncfYfYvyB52Vs+zVNCF9KTzzCV9+if4miiB6QJjb8A9s5/tEisDgrtwK0n02x\nUqhXJ5ApiZXyctIBN5aQmv2dSA4xnxBwy12xBhVErzr9MuYcua7b3PIi/hNJ\nRl6n\r\n=+dlO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "7fea53d594d938234c31a56fca42f99ab8b9fd94", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^16.6.0", "mocha": "^8.4.0", "serve": "^11.3.2", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^7.4.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^12.1.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^26.0.23", "concurrently": "^6.2.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.2", "eslint-plugin-jest": "^24.3.6", "@testing-library/dom": "^7.31.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.24.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^4.24.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.5_1622460459883_0.3649392071562314", "host": "s3://npm-registry-packages"}}, "0.5.6": {"name": "dom-accessibility-api", "version": "0.5.6", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.6", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.6.tgz", "fileCount": 57, "integrity": "sha512-DplGLZd8L1lN64jlT27N9TVSESFR5STaEJvX+thCby7fuCHonfPpAlodYc3vuUYbDuDec5w8AMP7oCM5TWFsqw==", "signatures": [{"sig": "MEQCICW2GsMx8z34vuXNysgF791s+WJbNsvzyQZC+vJp+uVWAiBd92W3ycf5bcdcijRFHh1ZfCkWGED+CwIBTCyNmbzpqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguJ44CRA9TVsSAnZWagAAvyMQAKSdC9sC1qe/wj3Glvht\n14Zdkeat/5vAKY5WdL3QQSfeFZZYgr2yDPMULtfWzX1MtcH7mHtf5WCK8vM4\niEDwoguZL5bljvcCiCeUynMF8LISssBuFJ71SH22BEyOsZpuogt6q/dgif2B\nqhDfAF54vS09JDaP3t+pV+1O7ujO//xszblYgvvd+bXLaI8xZgnPZn1X9ypF\n3rpwjaLxG47Z1X1BUuUigs2s9w0IS8a2/fOuCoZP3JeYUh21P1OVFH7FxcT2\nZlx3bFLivkbWCB9ZJmigpp4E/E9oDQs5JNpaJ3DKH6R5yyJAKhpdGW1MkdY6\n/TWZCiKfN8VakmHiMm7pn1Da0W/pCKs2f2fvonb/IrOrIaQ1cUr8gRhlscjR\nMpOkvtuPz1U0fA2xdhXwv9vAfJUAzI2bNizgGp5OnlX9aYHU/Dnqzq/8HCMe\n4tDMRYTUe33OwmWA2z56yWLCSOd8eDo2HOu9M9fMSm3UXZivtPDWp3MuHlXJ\nrB+WYGf1DXaHp+YjIEJOwlnJzL0QSY0IAFzANLN3iIzHVv6eb1ouw4VoZ1gE\np9IcoA3ir2pye7aUVX/lOQWK+nEhQU5VzZJiHKXMy7ZQR+h+hEkMqv0/WeK6\nNUhJC5Naj8hAnUlWQBcEQMspu8eqPCx8b5M3aJFuCaSLwwVyXXOgx1cp17nE\n43rY\r\n=Uzgi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "2be2716c9ceeefefe6aa909f8b3eb6efb5fb0f53", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^16.6.0", "mocha": "^8.4.0", "serve": "^11.3.2", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^7.4.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^12.1.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^26.0.23", "concurrently": "^6.2.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^24.3.6", "@testing-library/dom": "^7.31.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.25.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^4.25.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.6_1622711864181_0.4209577561972295", "host": "s3://npm-registry-packages"}}, "0.5.7": {"name": "dom-accessibility-api", "version": "0.5.7", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.7", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "8c2aa6325968f2933160a0b7dbb380893ddf3e7d", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.7.tgz", "fileCount": 57, "integrity": "sha512-ml3lJIq9YjUfM9TUnEPvEYWFSwivwIGBPKpewX7tii7fwCazA8yCioGdqQcNsItPpfFvSJ3VIdMQPj60LJhcQA==", "signatures": [{"sig": "MEUCIQD5+f3KYBbyOkNJmYGQahPNUJ3hR9wbyg6hd1HjQd0eNQIgIMQnFwKxCUi3gHCQ2awaAWkEIBR4gHPugabENmyBjco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhC8XBCRA9TVsSAnZWagAADlYP/ijxYArEHBxrDvcjRWQJ\nyLjPSlmFIjVPO8C4SPjb7Z9031zEg836sQvkt4GKR9f1ttp6OCOIloWuES8U\nqQ6OFxR5XfVr06Di7IEQpsnQGt3RVw+QX7jTqlllbTWRAXDdVV2OjnhdrVzC\n6uvYM2U1lac7Kd47ce1lGNe7oEfbCounAcMgT9CDn3rFZIFWiuXc/qtkJquc\nH3r83mkNGSgLb2c54alHd7Meoi6z/eAWjhEQGx3DVVXzaNjhZlPmAL0zD0Cs\ntVsqM4UOisExWxsq6mZD8z5t9rq/Z5W4ujW60u1vsHsdM4aob4Y2hDoUzFFz\nGJfFu5NVzGvFO4euqWO+EE3mh0bsq8zphyziYTYDeKl9jSaQa+/Wg+5FYy+2\nMjaENw0it5UvkbBtM3mL4630fixnpZIDoPKzr0jeW9LgjkwNOWWhj/jxM0gV\n+piMPnJ0VURP9dbbxW74EdESNuyXDCNnujlvqe4yRZGkVqm/zKhrGMqOFUyF\nvBduJcbJl2KFmSanu5O3FsLWW4LE8vEiuknzCY+mlRL5SeFYtocVVocjMhnL\noiJCY1Ggs3HoP/JTiFNDlROQiaXx9/jofgKqSNV5V0t+fpdYljSIlz6IzLWe\nz9F4eQWLK+etd6vct8qrVLQaOtvIbZ3UhbE9/cOvK+d+bsXDFFTEcgYd9Y3W\nnFMQ\r\n=VAkF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "4f7859c37fd7a2f5af6c1d9d64570dcdfcd2b2f4", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.4", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^16.6.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^8.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^12.1.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^26.0.23", "concurrently": "^6.2.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^24.3.6", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.25.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^4.25.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.7_1628161473220_0.10996074966382596", "host": "s3://npm-registry-packages"}}, "0.5.8": {"name": "dom-accessibility-api", "version": "0.5.8", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.8", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "414813012e065b5dfa8998b990460c0af12a5421", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.8.tgz", "fileCount": 63, "integrity": "sha512-rAfghuBPeJldxqsmZQtBbna4TqMgFe4xhYs24vPULNslbmXUdcga+CXiKWzZxyWw0FCkGKPgmizIysIvsAEN8w==", "signatures": [{"sig": "MEUCIQCbIlV1pidqUPUVgXXsPcbbCWKIeTYwnuvKzsWOEc0WmgIgDFH4a5IfO68Z4S6sr+GiW5kz4Cuq4U8yO9525jElokI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261100}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "f84700b31ed416e565b57bd53b56c2099f0f9513", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.6", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^17.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^8.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^6.2.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^24.3.6", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.25.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^4.25.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.8_1633696388753_0.6276227679044553", "host": "s3://npm-registry-packages"}}, "0.5.9": {"name": "dom-accessibility-api", "version": "0.5.9", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.9", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "915f8531ba29a50e5c29389dbfb87a9642fef0d6", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.9.tgz", "fileCount": 63, "integrity": "sha512-+KPF4o71fl6NrdnqIrJc6m44NA+Rhf1h7In2MRznejSQasWkjqmHOBUlk+pXJ77cVOSYyZeNHFwn/sjotB6+Sw==", "signatures": [{"sig": "MEQCICEMmq+fk5I9FXhkY9NGQCxY4KwbKOIUqKJu/tP749dgAiAr8TGcpimDxpFUMMkwKP9FxsBge8Ph9M4Jhsygx6cTDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261604}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "76b9e6dfc1e7127f5cf04a28add8c56a37514dd9", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.6", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^18.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^8.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^6.2.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^25.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.25.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^4.25.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.9_1634564949805_0.7205892844340109", "host": "s3://npm-registry-packages"}}, "0.5.10": {"name": "dom-accessibility-api", "version": "0.5.10", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.10", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "caa6d08f60388d0bb4539dd75fe458a9a1d0014c", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.10.tgz", "fileCount": 63, "integrity": "sha512-Xu9mD0UjrJisTmv7lmVSDMagQcU9R5hwAbxsaAE/35XPnPLJobbuREfV/rraiSaEj/UOvgrzQs66zyTWTlyd+g==", "signatures": [{"sig": "MEQCIHN8RI3aafbORNnkO/UKyBD+1Vs/1IPBfulqPEsqBIo6AiBe9RjZ0hl3gmuZbIMB+Gs3E2hPGDzBLbU0bMexvLJlQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 262031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2ohdCRA9TVsSAnZWagAA4CYP/1J5wdgmzR9Qxi/X9Xq1\nCpEG2PPRBmym347h9YRoJOQJrb3u3z6X/wa8g182r1t97TVqzX3iw7VwK3YM\nIWMIflteaVdvW+ta6vr6QnzTWuGw/Zp4fAgJY1WRsnskdtyBHh9KiJT/Yl10\nA4btT3UiMpntTv9l6bfPj6+ctEnPw5YkxFl8eevCvd68r9Eot1ND38VO4o+P\nmx2HSHxVRocNUeH6Jj1t1Zl7x5TK1vyKiL48TvpJ2ZcXHcxOJpOBmdGjkTRP\neH4vmwXh2aOkYzJF4YOr6V/2V1hkkDy6oAlGCmRNd/ZcTkMUhE06HAjCUAEA\n/HIQMpvFUW/vA7tm6rFf8YZkaFANawV3oZmh8StUMrKGgiqcGYVztrkN7SIN\nxEBFxkeIkZfgbwr5pg70IqfZGjRiI4xsO0tbG5WZebXAw85g8cxgfQ06Q70U\nOrn1DYw62cve2ZXXtKnFmaMlqEmIerM9J2Gd3STTT6xDrCKyeZ0+GN0O0jGl\n6v+aQXt05i7PqrLqaquQfb4uktNXZkekhYwe5Tkm+cTlLBoXEAi9H1sATfAR\nQ/hSXrPIM/EUtDzM4xUAeGyjgLb/Hx4j4BQLGIH8V03eIhPWzdO2qm2cLkSK\nZR1z2XTjJxUMpFdD3dSLJX0x+sOO2nRv2AiU9aZXBtyhrByZCZrkrviSqSqy\nluuD\r\n=N6Uq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "8448cdb1a137ac551d605042e9e630ea8c9c4280", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.7", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^18.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^8.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^6.2.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^25.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.10_1635506714600_0.06681706746229121", "host": "s3://npm-registry-packages"}}, "0.5.11": {"name": "dom-accessibility-api", "version": "0.5.11", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.11", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "79d5846c4f90eba3e617d9031e921de9324f84ed", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.11.tgz", "fileCount": 63, "integrity": "sha512-7X6GvzjYf4yTdRKuCVScV+aA9Fvh5r8WzWrXBH9w82ZWB/eYDMGCnazoC/YAqAzUJWHzLOnZqr46K3iEyUhUvw==", "signatures": [{"sig": "MEUCIQCA/RtVz9+pPjaG30tG7dE1xcaUDsVLZP/JjQLs6a7ltgIgHHndhaFzf5EM9/Ey4IEXm93AstuHZNja8YeeK+NQUYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 262751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8vehCRA9TVsSAnZWagAAiRoP/jXIt1tGxMMlsj8KW3JU\n6ezt3EKpqxFu0j4wDcI6vXbpG7HRactTuIhzZZaEmXWxhRoaN8B3TgFABdlW\nkPJhzCF35QbdsxgEFu5sGRMnRQqEIalrh7J1vooz2deOgJSYEJx048tC8QrH\n58RG2g0KbL1VlItwdGvFa9y5Kp0N5B4jpFvSFIa6LJxI/1mlFpDffSPOMBwA\nYSgZMUoml9DvtHQBYaIBWVdn2oNo7nLPl2N032AAo+JO6hheMl1ddYPvG8Uh\nsApip9gMPJxwRA549KI1C9hkjHc976lCWy/Fl1ju6O+KXpiFJ8GrOcCnCIVD\nevus+aPZWPKBxWZk/jJguicohAmInPGFU/+Nz5/I5AzqzbnLrlg27H6fF8ej\ncZjdpWrGFeLTFZVw7LVNUBeP0hu5v0ls8BDx1QxdVhxtJQcWsx0n8sjsPhV+\nsjyBpLrctIhRBY4JfDlZfodTCpHr4A9wtHhOHGehDEB6jxdF1cc1NlrffLSE\nMtFa/HfSrQum83k6o5Zw2Kee7hhoCfBAJmlsAEwrv7D9KO9I1nu+gDRgSAxe\nJmDlJuE3re64xi8LNFwpsjGRk9gr/ENLxHHEWQi523Np5kKvv5gUpTEjFxz+\nwTfE9JgUtnawoj+8rl0Y27ucw++64yBdHiIlz5eXdfUl9dDBdJjI/yxjvn9Y\nWEDD\r\n=u+Hf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "1791d575ef31e1f5a856fc99208099e59b91f6ca", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.9", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^19.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^9.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^7.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^25.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.11_1643313057149_0.5181565426507462", "host": "s3://npm-registry-packages"}}, "0.5.12": {"name": "dom-accessibility-api", "version": "0.5.12", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.12", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "0fea9b3f28976a52fed7298d2cfdcdff29811cda", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.12.tgz", "fileCount": 63, "integrity": "sha512-gQ2mON6fLWZeM8ubjzL7RtMeHS/g8hb82j4MjHmcQECD7pevWsMlhqwp9BjIRrQvmyJMMyv/XiO1cXzeFlUw4g==", "signatures": [{"sig": "MEQCIG21zpsbFapP24fz9NuNbbvW+J7WwqnENRow2O9ROaovAiARzrKI6iBhE0rlpolawYpYWnsKVsxmi2vHtfyOlaD1bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE3fSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT7Q/9GsmYceN1DTX00GUP4TDA+waKPOyX9x5w2BhTcUI73oP9qOv9\r\nMMvwOXcyJXGgZpRrB9lrrjkKQkmn4VT+U4RZph553BYJZSG/biG4bAe/aH1T\r\nBvyiWozYvscedvns8bg0TpLHpyq4Vn/KMf5IT8xip7MooVcg+r3dwU++DWSn\r\nvJJ+5m70c1qt4tOuExz9FjJ3iXj99AuK/6Q7MHBYnWJfqI1JRruriHrc2Hei\r\nt8Jp0kcREPw6dvtsOel8x5UBIGglvqDi/9Y5UrHEBspyuNnB33Dmw9JtCtTs\r\nsKcemyb4Ls4PInLvOOcyek62Pbkij/8oGWOS+8Q+nzhOs5zz1+gMMiA8Qzn7\r\n+vmh4XV0cuYSJBPtqG7lx4a3txbCfL0AwGVQThw4EjCUSzpWBwFx6Nao2QjL\r\nXyoE92o0RjLuiWIFzfIrzQwLLglAAuqmC9ZGk4oLaiOThDDUebBxEglpCHED\r\nZxAclixs1rOz/lyNe/7WS+Jq5nWCybbfsBtFXgbStVJ2ofL/e1rBZHVvietC\r\n44/OpNb53iNwdanqqRQcZ+rI8ZkMDr0JSQNBJU37+PCLqso7SSClZT5XQ0UZ\r\nRFiUec/buPIL8H4IKTbKPpaLAAcxXzMGY43Dq05FP5FCcPyBu483HXgE+Nea\r\nTawP2nYFzcMSSxQi9d4a1QGrN0zD4ZFiV3M=\r\n=1V1I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "98284fd850f4d0f8622a1f3946cf0f7ae67ad95e", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.10", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^19.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^9.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^7.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^26.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.12_1645443026449_0.25038656002259096", "host": "s3://npm-registry-packages"}}, "0.5.13": {"name": "dom-accessibility-api", "version": "0.5.13", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.13", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "102ee5f25eacce09bdf1cfa5a298f86da473be4b", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.13.tgz", "fileCount": 63, "integrity": "sha512-R305kwb5CcMDIpSHUnLyIAp7SrSPBx6F0VfQFB3M75xVMHhXJJIdePYgbPPh1o57vCHNu5QztokWUPsLjWzFqw==", "signatures": [{"sig": "MEYCIQC7HpE45R5kF+hbv6tmU/1XxXhESkX7ZSscsAbLvHeeAAIhANCBmI7agbk1X6/bSf6mdLwUs6t6+hI8Qk4VYROpVg5F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdkhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvhQ//Z9BXOHqSqGeU2biI2WaE85quZCiveA+8HpJ4/Xj0/WUIxCF2\r\nzQX7vp3YE1uEV2dBqZHnOXBPHkydAz8Toavld12TZ31tfqY/ZMmVcpZr+hjN\r\nwKBJ5QzoVbC3JjYHU3dxR6Qgd7QCAIhMS1jCoHiQWup2SZV7v5//IbsUUSF0\r\nW8Pw4yTR5uTOQ97CvI82hRjlsjtfMEsIrpbyl1v8F96wzbq/gJAf/ZDYL9XH\r\ninab4AU2ePHScl4daoUmuM14QHvJnYVVYpkdBl+hfg4QJKAATys87eN8BeHm\r\naQFPTrRLFMeoxHyA4m/Hmtlw5b2lReq2TBxYw42ev5cNRNbBpoZjTsKxbcux\r\n5mX7xS9TV9mwBSCkAm3BMAb5lSRxwuea3pNkEfi750u03y2xKcIteNHalydP\r\nA9EGsXYwpACFEek7LdJHOEPQbI9wd+nofnYww5sVa/BpNolPdosXPkF8GHwL\r\nXp6ZH4jhx4ApRaqDLKsRpXv2Nh2PI43yPhfTEE6rl18i35Py71ZDrKq/A7IG\r\n43WZFR1K2dtxRJ5cmTwH9eUsUXsCpzMzfrvIE2+jWxwIB+knRF477/0IJSj7\r\nSUeDVFXgUdYDUDs/I8fC1Qu6RnGAKwhkIpwpq4aFckzhG6ILNRaA6beL5f5e\r\nQeqk4EOXEnxIbT2Iq5WnDXab+r4rMexXAWM=\r\n=rB6+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "1c67898de8c581d304a97f2cc85791d297c2b8ae", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.10", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^19.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^9.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^3.0.4", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^7.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^26.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.13_1646123296980_0.6662070781427976", "host": "s3://npm-registry-packages"}}, "0.5.14": {"name": "dom-accessibility-api", "version": "0.5.14", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.14", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "56082f71b1dc7aac69d83c4285eef39c15d93f56", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.14.tgz", "fileCount": 63, "integrity": "sha512-NMt+m9zFMPZe0JcY9gN224Qvk6qLIdqex29clBvc/y75ZBX9YA9wNK3frsYvu2DI1xcCIwxwnX+TlsJ2DSOADg==", "signatures": [{"sig": "MEYCIQDb4AAu77IkM93XOfNFkpQW/Pj9vL+0NNCn+vIk3wG05AIhAOMnQZED4xM/cnGdkbCH28jStRnGFWygsJOi2Ltt4K2Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZkCWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5lA/5ADbIRWH4/52smAdNbVXE0GeUWw0YuD/v0fknDq8mmmerHXje\r\nkiOkTQddh5AA2t2+ul5m94xTk7/dRMdTP1Xwv0fqn+VtNsfbXoaJNrDPTry2\r\nNdCUOQiFnqheTWVeZ/WyvyGLNrVk2Gi7HMdbx0DcnuJ3RhLIlI4TUjyIvW36\r\nMwrIx82A+RkBD12Frsi6m/Hw4Ow8iX7VGpJP6hgooyu77eouTIDfGVfHmtcG\r\nNXM3+xd8nIWwSYtdRZ/eaBIOCXisl9uq90G4vV0SgFOdRYhgztoCq2ZWYlCm\r\nKcSsNoIDT4aH5YDjXD1Of/kcBYom/Kr+ZX0qS5texLHeSKh5jXy6dJ3l7GL5\r\nK+5v+pVL6CQtaicUMmrJZ62dPmMDb0GQ4TPej0rLZCfXPE2nL9tRrkaMAvgI\r\n2OIWmtrwlbtGV46Ksfxi7raPRM3ueY/Y2IZWihvumR10Z+NrUkznljWa0Qcc\r\n7RQOpeNGACbif+melHkPz+bgpaaJg7V80PuypFKGeoUtwMZyJzOcVjwb+zmp\r\n20J9crHRMHdxDzbc6AJ+Ruenfea+GSYOR7QSeUG5wxqd34/DO95CHSbh5bVj\r\nbVwCUbZuzD0iCFZL8anj417UedK1XU7hDMSSv3dbUnXXbyyD9MHrr7Si7mBy\r\nskzw45Nx02TC9w1Vyb/yCDqZ7w1oEdZNodU=\r\n=o0Fy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "ce7b5e3c85db53b0b203731c8749d2d37bb7e36f", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "12.22.12", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^27.0.3", "jsdom": "^19.0.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^9.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^27.0.2", "minimatch": "^5.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^13.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^27.0.0", "concurrently": "^7.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^26.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^27.0.3", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.14_1650868374589_0.21298953865461212", "host": "s3://npm-registry-packages"}}, "0.5.15": {"name": "dom-accessibility-api", "version": "0.5.15", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.15", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "357e74338704f36fada8b2e01a4bfc11ef436ac9", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.15.tgz", "fileCount": 62, "integrity": "sha512-8o+oVqLQZoruQPYy3uAAQtc6YbtSiRq5aPJBhJ82YTJRHvI6ofhYAkC81WmjFTnfUbqg6T3aCglIpU9p/5e7Cw==", "signatures": [{"sig": "MEUCIQDOFSI597f+mIHYZ9pcnEMazwgwmw6EIi72Bf600/nj2AIgNOZUZII32l8lQHnd38ILVzoVhAyqYZD1yYFArVfXF2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsVsbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraOg/+PzU5oqO43ycARK03S5w0tWLZinypFU6KkDFbtC+VquSE2Z03\r\n9bp6ENKmTCFS7S1ZL5h3gV+Q4Ir/0cCvNXaykmgkYTh9auEVCjEMhnZr8SpI\r\nyLUqtw5X0XpdXS32ZUayQ53b02p9RzIasTpgI6OMukcXR1ILvGJqFilq0jBu\r\nIx1hOdUZQU1HMElw1nHeemouW6CXEHITQeg5WOYcD7inHRO/2Ifv07SwTbYr\r\nPY3CIyv8oGSFtqJDyOHV8XHEEFyxJwuaaefe+g7vu811i9ykjXT73BrTKJLr\r\n+VzkHsoTEyUnoiEFM9ollrZikWp7UbgUohpdZ7YNcLP7F0EHoQBa64M3+Ocl\r\nva5/HYiszT0zvLik8IgpU7/ig5aqXqm/6TYf9EjYw5av5jsaysxFWR7Ohllp\r\n8+KnseJZji1Y9s7FMakUXRFf72892cZ8ouzW/sftJ4k1jhFhiyhC2NNcY6zY\r\nAU56N+RoAtlZ+nZ297xo5sIgbBd8ZQDZPMdUhpzUY0H177Wv7e3qTkF5B3zW\r\nSD38857yfOLntDI4lLhfbNi7H89OlvGunCXPsE4WkpsShvl6OVjMGCOZA5O4\r\n+xfaYMZ7remmZcbn8VzhcVIKjv/DFa40AS2ZckcFmBErObSCv0ktVhyVlg0Y\r\nJiRVkjhqzTbt16IZzfsZ1K3p5sU8nNRc/mY=\r\n=RDVD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "c579a0930a50621d78689fd796993cd27139c771", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "16.18.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^29.0.0", "jsdom": "^20.0.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.27.0", "rimraf": "^3.0.2", "cypress": "^12.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^29.0.0", "minimatch": "^5.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^15.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^29.0.0", "concurrently": "^7.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^27.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^29.0.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.15_1672567579397_0.5931202765729806", "host": "s3://npm-registry-packages"}}, "0.5.16": {"name": "dom-accessibility-api", "version": "0.5.16", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.5.16", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "5a7429e6066eb3664d911e33fb0e45de8eb08453", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz", "fileCount": 62, "integrity": "sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg==", "signatures": [{"sig": "MEUCIBih1ClJQNoQ7epRBkWhTldTx0Kr5Y4N9cTArTBFhuh+AiEAop/PA6Zsv3faCRz0HwFxedrXVJ4RchMSboWX8fAE6eg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyFCmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdNhAAhu2fkzx/TuVIq7nPYoIGLucp8Q4sD6W97Yjm6z4P+O8sZeOr\r\npuqaAumMsoG0Ea9gfZeVP8GFoN/5O79yJwNvo7mYspaGKgw1RJq8aDF7E76P\r\nOQPpcK/53erJaMGhlLSMtSRqefC6C66UYGTzTfHLNn1GvbRUD/Cj+qH6UGl+\r\nz2iGo3WsjVwjW+ACcHu1vfVDlb9g7OFabvl2d/0LlH23100GssMBi5i24cKY\r\nDki2YNJecMqWosjcqOcoKGZCcul05KL2tm+iRAVxKeL8J4brDz4AjDFj4b/O\r\nx9I1dJvpqay7ePs/Fs2XtmVQ4uNxhTlPx21bxSYjLjYYIm/IGiw0ipaJjxhn\r\nXFOGTTr+LRAwz+40Kc5vwferjPFEJDgBO4j7gHzDBLNI6Ie/9GAfNACbjRqR\r\nnrE3e3cm2zNdOL3MqLkjILa8Io6XYvRTuc4FKlhH7xXQb6yIsgrJHkT41MpV\r\nBayn72JhmmRXXgB6aJBZ6TFyTbTtCL/juBn3Jdg8UMR20s/huJU1OqyXqIvU\r\nMKmZYQI9YCGkQYyiVadUU82l5aJ7uZdByzAVECv8xO/SpWCgAODl3QGp9GB7\r\nch9i9xeK3/XWXJG2BRikDS/3K1o6xU0xwk3YCqjcmBCUfmzyGZadNEhJ9Z6v\r\nImQTYx3o4kwbgHJneGrA23d3RoPTwa2WsJg=\r\n=tUsy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "386719bd7171e078da3bde7556433c824ccc45d0", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "16.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^29.0.0", "jsdom": "^20.0.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.27.0", "rimraf": "^4.0.0", "cypress": "^12.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^29.0.0", "minimatch": "^6.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^15.0.0", "typescript": "^4.3.2", "@babel/core": "^7.14.3", "@types/jest": "^29.0.0", "concurrently": "^7.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^27.0.0", "@testing-library/dom": "^8.0.0", "jest-environment-jsdom": "^29.0.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.5.16_1674072230671_0.35287161564613245", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "dom-accessibility-api", "version": "0.6.0", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.6.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "e2f179be2f8f8abcd9acfbb10e09d5b6124e46d7", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.6.0.tgz", "fileCount": 68, "integrity": "sha512-4DeUx7GKaZ2jjPAOzBRhI2NHoVjt3YcTOj1aibPLdH9zhdrRsm0ZMqo2HzpTS2Iygt3NJEPFODJpOPqPs3dQtQ==", "signatures": [{"sig": "MEUCIQDfKjd7LheIxWA6Fymb4OU0+YDPGIiVSJfN9+NXLJp7igIgYFu9/45t8GLEjP3+l+1rYUUVN2jsdnp9foamW+viIU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 270975}, "main": "dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "1149a4b4533f257338d9a67fe887fb2f421edc64", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "16.20.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^29.0.0", "jsdom": "^20.0.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.27.0", "rimraf": "^4.0.0", "cypress": "^12.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^29.0.0", "minimatch": "^8.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^16.0.0", "typescript": "^5.0.0", "@babel/core": "^7.14.3", "@types/jest": "^29.0.0", "concurrently": "^8.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^27.0.0", "@testing-library/dom": "^9.0.0", "jest-environment-jsdom": "^29.0.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.6.0_1683490155379_0.5711056292440957", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "dom-accessibility-api", "version": "0.6.1", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.6.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "630cb063717690fa89a7dca96abae328b987c064", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.6.1.tgz", "fileCount": 68, "integrity": "sha512-WbiG8jCZESbcSwxLmbUiv3WZurc6H4opBIbBkBe/I3OSZvWCXXj+wxPueWodM/p4gegM1CqEr0iFY5DqyrncxA==", "signatures": [{"sig": "MEUCIFClnFIu9BikFJc6JmtoLPqBOzAeQKXLN8mBOKcFHGw3AiEA8TdduO41f0Ms9gvOe5NZ2oWvAzXbHHVPCMLJYAlt9tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275050}, "main": "dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "f204af5652e4c894198f68e212e0f9d242d006a7", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"**/kind-of": "^6.0.3", "**/minimist": "^1.2.2"}, "_nodeVersion": "16.20.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^29.0.0", "jsdom": "^20.0.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.27.0", "rimraf": "^4.0.0", "cypress": "^12.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^2.3.0", "cross-env": "^7.0.3", "jest-diff": "^29.0.0", "minimatch": "^9.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^16.0.0", "typescript": "^5.0.0", "@babel/core": "^7.14.3", "@types/jest": "^29.0.0", "concurrently": "^8.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^27.0.0", "@testing-library/dom": "^9.0.0", "jest-environment-jsdom": "^29.0.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^5.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.6.1_1686389686900_0.5825949434179056", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "dom-accessibility-api", "version": "0.6.2", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.6.2", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "7d52d8819e670f440ce96ce0d04fa45c36fcf728", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.6.2.tgz", "fileCount": 67, "integrity": "sha512-BN9ee0ohe3wk6BriBMPH0jPuPf/sytAPzz85TeBivuvTNmfIsFg3nGkwip8IZtOOZo+OxjaN/YcurIhPtWoI5A==", "signatures": [{"sig": "MEQCIEO3oThiDFxNDiOaSISlK9i4ehjwZ6E/RvfvNnsoDfDFAiAWDcgfSvEeReIVedB4Xu8iE4ff/mxF7fy2bOu7dJhqZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274558}, "main": "dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "56941bf36cd2ce3150099fa3dcd9aa7d6d317a55", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"@types/node": "18.17.17"}, "_nodeVersion": "16.20.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^29.0.0", "jsdom": "^20.0.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.27.0", "rimraf": "^5.0.0", "cypress": "^12.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^3.0.0", "cross-env": "^7.0.3", "jest-diff": "^29.0.0", "minimatch": "^9.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^16.0.0", "typescript": "^5.0.0", "@babel/core": "^7.14.3", "@types/jest": "^29.0.0", "@types/node": "18.17.17", "concurrently": "^8.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^27.0.0", "@testing-library/dom": "^9.0.0", "jest-environment-jsdom": "^29.0.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^6.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.6.2_1694979468110_0.1777625196733008", "host": "s3://npm-registry-packages"}}, "0.6.3": {"name": "dom-accessibility-api", "version": "0.6.3", "keywords": ["accessibility", "ARIA", "accname"], "license": "MIT", "_id": "dom-accessibility-api@0.6.3", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "dist": {"shasum": "993e925cc1d73f2c662e7d75dd5a5445259a8fd8", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.6.3.tgz", "fileCount": 67, "integrity": "sha512-7ZgogeTnjuHbo+ct10G9Ffp0mif17idi0IyWNVA/wcwcm7NPOD/WEHVP3n7n3MhXqxoIYm8d6MuZohYWIZ4T3w==", "signatures": [{"sig": "MEYCIQCaXN9FKAjnLEFGbxci8w4bpLZFF2dFvgSciNbgqNsUhAIhAPn+PgRbOa/lV75RNjZCLJK4VO5KscRXjRTPr29IVBXa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274558}, "main": "dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "gitHead": "db10fff33e7af8f975a667a427c01805b8a17541", "scripts": {"lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "test": "jest --config scripts/jest/jest.config.js", "build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "release": "yarn build && yarn changeset publish", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "test:types": "tsc -p tsconfig.json --noEmit", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:open": "cypress open --project tests", "test:wpt:browser:server": "serve tests/wpt"}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "prettier": {"useTabs": true}, "repository": {"url": "git+https://github.com/eps1lon/dom-accessibility-api.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Implements https://w3c.github.io/accname/", "directories": {}, "resolutions": {"@types/node": "18.17.17"}, "_nodeVersion": "16.20.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"q": "^1.5.1", "jest": "^29.0.0", "jsdom": "^20.0.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.27.0", "rimraf": "^5.0.0", "cypress": "^12.0.0", "js-yaml": "^4.1.0", "request": "^2.88", "prettier": "^3.0.0", "cross-env": "^7.0.3", "jest-diff": "^29.0.0", "minimatch": "^9.0.0", "@babel/cli": "^7.14.3", "jest-junit": "^16.0.0", "typescript": "^5.0.0", "@babel/core": "^7.14.3", "@types/jest": "^29.0.0", "@types/node": "18.17.17", "concurrently": "^8.0.0", "@changesets/cli": "^2.16.0", "mocha-sugar-free": "^1.4.0", "@babel/preset-env": "^7.14.4", "eslint-plugin-jest": "^27.0.0", "@testing-library/dom": "^9.0.0", "jest-environment-jsdom": "^29.0.0", "request-promise-native": "^1.0.9", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^6.0.0", "@changesets/changelog-github": "^0.4.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@babel/plugin-proposal-class-properties": "^7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/dom-accessibility-api_0.6.3_1695055102590_0.6315514012820893", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "dom-accessibility-api", "description": "Implements https://w3c.github.io/accname/", "version": "0.7.0", "main": "dist/index.js", "module": "dist/index.mjs", "type": "commonjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/eps1lon/dom-accessibility-api.git"}, "scripts": {"build": "yarn build:clean && yarn build:source && yarn build:source:cjs && yarn build:types", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:source": "cross-env BABEL_ENV=esm babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.mjs --source-maps", "build:source:cjs": "cross-env BABEL_ENV=cjs babel sources --extensions \".ts\" --ignore \"**/__tests__/**/*\" --out-dir dist/ --out-file-extension=.js --source-maps", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly", "format": "prettier \"**/*.{json,js,md,ts,yml}\" --write --ignore-path .prettierignore", "lint": "eslint --report-unused-disable-directives \"{scripts,sources}/**/*.{js,ts}\"", "release": "yarn build && yarn changeset publish", "test": "jest --config scripts/jest/jest.config.js", "test:ci": "jest --ci --config scripts/jest/jest.ci.config.js --runInBand", "test:coverage": "jest --config scripts/jest/jest.coverage.config.js", "test:types": "tsc -p tsconfig.json --noEmit", "test:wpt:jsdom": "mocha tests/wpt-jsdom/run-wpts.js", "test:wpt:browser": "concurrently --success first --kill-others \"yarn test:wpt:browser:run\" \"yarn test:wpt:browser:server\"", "test:wpt:browser:run": "cypress run --project tests", "test:wpt:browser:server": "serve tests/wpt", "test:wpt:browser:open": "cypress open --project tests", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rimraf ./tests/wpt && yarn wpt:init", "wpt:update": "git submodule update --recursive --remote && cd tests/wpt && python wpt.py manifest --path ../wpt-jsdom/wpt-manifest.json"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.3", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/preset-env": "^7.14.4", "@babel/preset-typescript": "^7.13.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.16.0", "@testing-library/dom": "^9.0.0", "@types/jest": "^29.0.0", "@types/node": "20.6.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.0.0", "cross-env": "^7.0.3", "cypress": "^12.0.0", "eslint": "^7.27.0", "eslint-plugin-jest": "^27.0.0", "jest": "^29.0.0", "jest-diff": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "jest-junit": "^16.0.0", "js-yaml": "^4.1.0", "jsdom": "^20.0.0", "minimatch": "^9.0.0", "mocha": "^10.0.0", "mocha-sugar-free": "^1.4.0", "prettier": "^3.0.0", "q": "^1.5.1", "request": "^2.88", "request-promise-native": "^1.0.9", "rimraf": "^5.0.0", "serve": "^14.0.0", "typescript": "^5.0.0"}, "resolutions": {"@types/node": "20.6.3"}, "prettier": {"useTabs": true}, "keywords": ["accessibility", "ARIA", "accname"], "publishConfig": {"access": "public"}, "types": "./dist/index.d.ts", "gitHead": "f6ef9dbb5cef89759445b9c53fec99ad4f73094e", "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "_id": "dom-accessibility-api@0.7.0", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-LjjdFmd9AITAet3Hy6Y6rwB7Sq1+x5NiwbOpnkLHC1bCXJqJKiV9DyppSSWobuSKvjKXt9G2u3hW402MPt6m+g==", "shasum": "f5615cb24f86f7aea90edac878c545f359d3e01c", "tarball": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.7.0.tgz", "fileCount": 73, "unpackedSize": 282447, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGkpBgT4OOsIdziDd3ewUlL+aguA2tEjufzqsq7iFEGVAiBUBgbBYp9nfnlwqN45sTYKvcYuJxRHf0WLLRmG8BpTUg=="}]}, "_npmUser": {"name": "eps1lon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-accessibility-api_0.7.0_1721846005889_0.0033412664045679996"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-10-08T18:35:18.464Z", "modified": "2024-07-24T18:33:26.293Z", "0.0.1": "2019-10-08T18:35:18.657Z", "0.1.0": "2019-11-05T21:44:51.361Z", "0.2.0": "2019-11-28T09:56:51.205Z", "0.3.0": "2020-02-01T20:40:18.925Z", "0.4.0": "2020-03-15T15:09:27.824Z", "0.4.1": "2020-03-15T15:18:50.569Z", "0.4.2": "2020-03-21T20:36:46.075Z", "0.4.3": "2020-03-24T14:46:04.161Z", "0.4.4": "2020-05-18T10:07:55.815Z", "0.4.5": "2020-06-05T15:42:22.552Z", "0.4.6": "2020-07-13T21:24:46.213Z", "0.4.7": "2020-07-30T06:43:07.160Z", "0.5.0": "2020-08-10T21:48:35.461Z", "0.5.1": "2020-08-20T15:59:56.689Z", "0.5.2": "2020-08-26T19:16:06.736Z", "0.5.3": "2020-09-28T12:42:26.202Z", "0.5.4": "2020-10-09T11:15:35.679Z", "0.5.5": "2021-05-31T11:27:40.017Z", "0.5.6": "2021-06-03T09:17:44.374Z", "0.5.7": "2021-08-05T11:04:33.395Z", "0.5.8": "2021-10-08T12:33:08.954Z", "0.5.9": "2021-10-18T13:49:09.972Z", "0.5.10": "2021-10-29T11:25:14.733Z", "0.5.11": "2022-01-27T19:50:57.369Z", "0.5.12": "2022-02-21T11:30:26.590Z", "0.5.13": "2022-03-01T08:28:17.171Z", "0.5.14": "2022-04-25T06:32:54.747Z", "0.5.15": "2023-01-01T10:06:19.669Z", "0.5.16": "2023-01-18T20:03:50.833Z", "0.6.0": "2023-05-07T20:09:15.622Z", "0.6.1": "2023-06-10T09:34:47.081Z", "0.6.2": "2023-09-17T19:37:48.332Z", "0.6.3": "2023-09-18T16:38:22.883Z", "0.7.0": "2024-07-24T18:33:26.115Z"}, "bugs": {"url": "https://github.com/eps1lon/dom-accessibility-api/issues"}, "license": "MIT", "homepage": "https://github.com/eps1lon/dom-accessibility-api#readme", "keywords": ["accessibility", "ARIA", "accname"], "repository": {"type": "git", "url": "git+https://github.com/eps1lon/dom-accessibility-api.git"}, "description": "Implements https://w3c.github.io/accname/", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}], "readme": "# dom-accessibility-api\n\n[![npm version](https://badge.fury.io/js/dom-accessibility-api.svg)](https://badge.fury.io/js/dom-accessibility-api)\n[![Build Status](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_apis/build/status/eps1lon.dom-accessibility-api?branchName=main)](https://dev.azure.com/silbermannsebastian/dom-accessibility-api/_build/latest?definitionId=6&branchName=main)\n![Azure DevOps coverage](https://img.shields.io/azure-devops/coverage/silbermannsebastian/dom-accessibility-api/6)\n\nComputes the accessible name or description of a given DOM Element.\nhttps://w3c.github.io/accname/ implemented in JavaScript for testing.\n\n```bash\n$ yarn add dom-accessibility-api\n```\n\n```js\nimport {\n\tcomputeAccessibleName,\n\tcomputeAccessibleDescription,\n} from \"dom-accessibility-api\";\n```\n\nI'm not an editor of any of the referenced specs (nor very experience with using them) so if you got any insights, something catches\nyour eye please open an issue.\n\n## Supported environments\n\n**WARNING**: Only [active node versions](https://nodejs.org/en/about/releases/) are supported.\nInactive node versions can stop working in a SemVer MINOR release.\n\n```bash\nie 11\nedge >= 14\nfirefox >= 52\nchrome >= 49\nsafari >= 10\nnode 10.0\n```\n\n## progress\n\nUsing https://github.com/web-platform-tests/wpt. Be sure to init submodules when\ncloning. See [the test readme](/tests/README.md) for more info about the test setup.\n\n### browser (Chrome)\n\n153/159\n\n### jsdom\n\n<details>\n<summary>report 138/159 passing of which 15 are due `::before { content }`, one might be a wrong test, 5 are pathological </summary>\n\n```bash\n  web-platform-tests\n    accname\n      ✓ [expected fail] description_1.0_combobox-focusable-manual.html\n      ✓ [expected fail] description_from_content_of_describedby_element-manual.html\n      ✓ description_link-with-label-manual.html\n      ✓ description_test_case_557-manual.html\n      ✓ description_test_case_664-manual.html\n      ✓ description_test_case_665-manual.html\n      ✓ description_test_case_666-manual.html\n      ✓ description_test_case_772-manual.html\n      ✓ description_test_case_773-manual.html\n      ✓ description_test_case_774-manual.html\n      ✓ description_test_case_838-manual.html\n      ✓ description_test_case_broken_reference-manual.html\n      ✓ description_test_case_one_valid_reference-manual.html\n      ✓ description_title-same-element-manual.html\n      ✓ name_1.0_combobox-focusable-alternative-manual.html\n      ✓ name_1.0_combobox-focusable-manual.html\n      ✓ name_checkbox-label-embedded-combobox-manual.html\n      ✓ name_checkbox-label-embedded-listbox-manual.html\n      ✓ name_checkbox-label-embedded-menu-manual.html\n      ✓ name_checkbox-label-embedded-select-manual.html\n      ✓ name_checkbox-label-embedded-slider-manual.html\n      ✓ name_checkbox-label-embedded-spinbutton-manual.html\n      ✓ name_checkbox-label-embedded-textbox-manual.html\n      ✓ name_checkbox-label-multiple-label-alternative-manual.html\n      ✓ name_checkbox-label-multiple-label-manual.html\n      ✓ name_checkbox-title-manual.html\n      ✓ name_file-label-embedded-combobox-manual.html\n      ✓ name_file-label-embedded-menu-manual.html\n      ✓ name_file-label-embedded-select-manual.html\n      ✓ name_file-label-embedded-slider-manual.html\n      ✓ name_file-label-embedded-spinbutton-manual.html\n      ✓ [expected fail] name_file-label-inline-block-elements-manual.html\n      ✓ [expected fail] name_file-label-inline-block-styles-manual.html\n      ✓ name_file-label-inline-hidden-elements-manual.html\n      ✓ name_file-label-owned-combobox-manual.html\n      ✓ name_file-label-owned-combobox-owned-listbox-manual.html\n      ✓ name_file-title-manual.html\n      ✓ name_from_content-manual.html\n      ✓ name_from_content_of_label-manual.html\n      ✓ name_from_content_of_labelledby_element-manual.html\n      ✓ name_from_content_of_labelledby_elements_one_of_which_is_hidden-manual.html\n      ✓ name_heading-combobox-focusable-alternative-manual.html\n      ✓ name_image-title-manual.html\n      ✓ name_link-mixed-content-manual.html\n      ✓ name_link-with-label-manual.html\n      ✓ name_password-label-embedded-combobox-manual.html\n      ✓ name_password-label-embedded-menu-manual.html\n      ✓ name_password-label-embedded-select-manual.html\n      ✓ name_password-label-embedded-slider-manual.html\n      ✓ name_password-label-embedded-spinbutton-manual.html\n      ✓ name_password-title-manual.html\n      ✓ name_radio-label-embedded-combobox-manual.html\n      ✓ name_radio-label-embedded-menu-manual.html\n      ✓ name_radio-label-embedded-select-manual.html\n      ✓ name_radio-label-embedded-slider-manual.html\n      ✓ name_radio-label-embedded-spinbutton-manual.html\n      ✓ name_radio-title-manual.html\n      ✓ name_test_case_539-manual.html\n      ✓ name_test_case_540-manual.html\n      ✓ name_test_case_541-manual.html\n      ✓ name_test_case_543-manual.html\n      ✓ name_test_case_544-manual.html\n      ✓ name_test_case_545-manual.html\n      ✓ name_test_case_546-manual.html\n      ✓ name_test_case_547-manual.html\n      ✓ name_test_case_548-manual.html\n      ✓ name_test_case_549-manual.html\n      ✓ name_test_case_550-manual.html\n      ✓ name_test_case_551-manual.html\n      ✓ [expected fail] name_test_case_552-manual.html\n      ✓ [expected fail] name_test_case_553-manual.html\n      ✓ name_test_case_556-manual.html\n      ✓ name_test_case_557-manual.html\n      ✓ name_test_case_558-manual.html\n      ✓ name_test_case_559-manual.html\n      ✓ name_test_case_560-manual.html\n      ✓ name_test_case_561-manual.html\n      ✓ name_test_case_562-manual.html\n      ✓ name_test_case_563-manual.html\n      ✓ name_test_case_564-manual.html\n      ✓ name_test_case_565-manual.html\n      ✓ name_test_case_566-manual.html\n      ✓ name_test_case_596-manual.html\n      ✓ name_test_case_597-manual.html\n      ✓ name_test_case_598-manual.html\n      ✓ name_test_case_599-manual.html\n      ✓ name_test_case_600-manual.html\n      ✓ name_test_case_601-manual.html\n      ✓ name_test_case_602-manual.html\n      ✓ name_test_case_603-manual.html\n      ✓ name_test_case_604-manual.html\n      ✓ name_test_case_605-manual.html\n      ✓ name_test_case_606-manual.html\n      ✓ name_test_case_607-manual.html\n      ✓ name_test_case_608-manual.html\n      ✓ name_test_case_609-manual.html\n      ✓ name_test_case_610-manual.html\n      ✓ name_test_case_611-manual.html\n      ✓ name_test_case_612-manual.html\n      ✓ name_test_case_613-manual.html\n      ✓ name_test_case_614-manual.html\n      ✓ name_test_case_615-manual.html\n      ✓ name_test_case_616-manual.html\n      ✓ name_test_case_617-manual.html\n      ✓ name_test_case_618-manual.html\n      ✓ name_test_case_619-manual.html\n      ✓ name_test_case_620-manual.html\n      ✓ name_test_case_621-manual.html\n      ✓ [expected fail] name_test_case_659-manual.html\n      ✓ [expected fail] name_test_case_660-manual.html\n      ✓ [expected fail] name_test_case_661-manual.html\n      ✓ [expected fail] name_test_case_662-manual.html\n      ✓ [expected fail] name_test_case_663a-manual.html\n      ✓ name_test_case_721-manual.html\n      ✓ name_test_case_723-manual.html\n      ✓ name_test_case_724-manual.html\n      ✓ name_test_case_725-manual.html\n      ✓ name_test_case_726-manual.html\n      ✓ name_test_case_727-manual.html\n      ✓ name_test_case_728-manual.html\n      ✓ name_test_case_729-manual.html\n      ✓ name_test_case_730-manual.html\n      ✓ name_test_case_731-manual.html\n      ✓ name_test_case_733-manual.html\n      ✓ name_test_case_734-manual.html\n      ✓ name_test_case_735-manual.html\n      ✓ name_test_case_736-manual.html\n      ✓ name_test_case_737-manual.html\n      ✓ name_test_case_738-manual.html\n      ✓ name_test_case_739-manual.html\n      ✓ name_test_case_740-manual.html\n      ✓ name_test_case_741-manual.html\n      ✓ name_test_case_742-manual.html\n      ✓ name_test_case_743-manual.html\n      ✓ name_test_case_744-manual.html\n      ✓ name_test_case_745-manual.html\n      ✓ name_test_case_746-manual.html\n      ✓ name_test_case_747-manual.html\n      ✓ name_test_case_748-manual.html\n      ✓ name_test_case_749-manual.html\n      ✓ name_test_case_750-manual.html\n      ✓ name_test_case_751-manual.html\n      ✓ name_test_case_752-manual.html\n      ✓ [expected fail] name_test_case_753-manual.html\n      ✓ [expected fail] name_test_case_754-manual.html\n      ✓ [expected fail] name_test_case_755-manual.html\n      ✓ [expected fail] name_test_case_756-manual.html\n      ✓ [expected fail] name_test_case_757-manual.html\n      ✓ [expected fail] name_test_case_758-manual.html\n      ✓ [expected fail] name_test_case_759-manual.html\n      ✓ [expected fail] name_test_case_760-manual.html\n      ✓ [expected fail] name_test_case_761-manual.html\n      ✓ [expected fail] name_test_case_762-manual.html\n      ✓ name_text-label-embedded-combobox-manual.html\n      ✓ name_text-label-embedded-menu-manual.html\n      ✓ name_text-label-embedded-select-manual.html\n      ✓ name_text-label-embedded-slider-manual.html\n      ✓ name_text-label-embedded-spinbutton-manual.html\n      ✓ name_text-title-manual.html\n```\n\n</details>\n\n## missing\n\n- visibility context (inherited but can reappear; currently reappearing wont't work)\n", "readmeFilename": "README.md"}