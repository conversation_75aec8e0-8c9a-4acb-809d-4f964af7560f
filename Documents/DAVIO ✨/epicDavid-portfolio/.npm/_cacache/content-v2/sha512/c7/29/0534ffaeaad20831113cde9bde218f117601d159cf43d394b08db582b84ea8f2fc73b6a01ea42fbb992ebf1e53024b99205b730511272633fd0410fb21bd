{"_id": "canvas", "_rev": "425-adde8587c0a57ca563e8b1d168e24681", "name": "canvas", "dist-tags": {"test": "3.0.0-rc1e", "next": "3.0.0-rc3", "latest": "3.1.2"}, "versions": {"0.0.2": {"name": "canvas", "version": "0.0.2", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.2", "dist": {"shasum": "1a4cb265ddddb5238c088f86ce5939058b834b56", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.2.tgz", "integrity": "sha512-vUP86bILe4U1OywZdH3LLZnkuqH88CEQ6CbS7cvJ9WC6MD5sY6IZPiQz/xGOWbHkCLFXC1il+I0+d+67yadaAQ==", "signatures": [{"sig": "MEUCIE1KHSKBxZOuUJW1MtEgmq7Mx4iACq5qi4d7daz+6fyLAiEAzKTMffEWPTw6BfNpfexmDbDAtZlubNye3SECHLBkw5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.3.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.0.5": {"name": "canvas", "version": "0.0.5", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.5", "dist": {"shasum": "e16d1000d35c6bca726700b8f6b8728d59a988be", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.5.tgz", "integrity": "sha512-U1L2vYLDvPODbcZmGHyxJHYwTezyDMnsIdzhXh5kZbs77Pdmo6iUKjZQzxr3sU2NO2KlLPo7GDmiNaosD8P19w==", "signatures": [{"sig": "MEQCIFFWwaF4p+/jAoCSHufHZ4wdlLsjGK0DH/Lx+MkrVl4pAiBrVIDeLA+TTHqKRK+B97oxdyqrVsbBBDWTFqEJS9saQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.4-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.4", "_nodeSupported": true}, "0.0.4": {"name": "canvas", "version": "0.0.4", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.4", "dist": {"shasum": "fc7a1fdf75f5f3e092b5a474cdb7c40a1aebf074", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.4.tgz", "integrity": "sha512-7CYbi8+9eBfy8sLsD7VbrXdeVG5nn2VyPEkCqhkqf38rK0MWg8LcmUVUSxOIjrqNlcQUAiZud5r46oubU4teQQ==", "signatures": [{"sig": "MEYCIQC9tH7bHLc+kKo/Qy8SArDa14SQshMeQaNARLioezVJ9AIhAOmZFhi51vrRXv6Z4qpwbX2KvHYvO6cJSJoxOhYgzEis", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.0.6": {"name": "canvas", "version": "0.0.6", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.6", "dist": {"shasum": "c8789d932698ad3e9006f69ee5656e023adbc595", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.6.tgz", "integrity": "sha512-s86FwcG+WS9rPYPpELRe14Ut4MTvDZfTR7ZW+EwTxBSw7ig657/kjszmeqnGu6lOLfnLDM+Zf32WMIFiJz1BgA==", "signatures": [{"sig": "MEQCIAGyDSeQnyuz4rCD4+VoEDy5EEZJIlZU6J7K0DPnHu2DAiBaU8aIVykarQRObIxOhpsuJBd8w0Ugkm1dSICifYX1Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.4", "_nodeSupported": true}, "0.0.3": {"name": "canvas", "version": "0.0.3", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.3", "dist": {"shasum": "7f03afa366e46d1ebe98f0ba6f381bf31689fa0f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.3.tgz", "integrity": "sha512-rhIq3jR3A+xjR6pf3HI4ma1CLL8rtn4yJfsH9S2yRFHXBv2KV93rAumPzMp+DohM+db8gsGxUwt5HasWkSgEgA==", "signatures": [{"sig": "MEUCIFpbwEtMOJWnJUSXLgaubwLHW7RVAdnfMx6uZxAiOEyCAiEA+Fd9jWdkGDmp2BfmGLHcygGZAjuf/pdHB2p0RkRKa8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.0.7": {"name": "canvas", "version": "0.0.7", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.7", "dist": {"shasum": "0d781bf11ea2c3ca6f532df509ca705a46c5496d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.7.tgz", "integrity": "sha512-lPKJO6mqx7Am7kgP9jg3kiRxqsQOvo83545hpkQCs9Dq6Lhh4YNBrVjA8FPPI3ZIWL4wNM6i+usETQbQnSQU6w==", "signatures": [{"sig": "MEUCIQDZ61tOnULbYEyjAyJCfs4JjRqW7l6a66T/TNzWdPJQDAIgYVwZO4RIzRwXPnOlEotnU4mwS9PQxlAICVMs0+v+toY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.4", "_nodeSupported": true}, "0.0.1": {"name": "canvas", "version": "0.0.1", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.1", "dist": {"shasum": "60f7f6042a333e2b641bfc29430703ba7201ca0c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.1.tgz", "integrity": "sha512-7AaL29SYwkGRnU+OgwwDJq5juMvzMLBkWeklx5vT8Ub06JVUybevm8l/QlEOkFbDyyCJguYff6fke8l4mV2yXA==", "signatures": [{"sig": "MEUCIQCXiSnnbBq++u3mUjNsH2KN8UYY974+PncOauBL/aXB7QIgP1is+w3+8bvCy6CMjXwMyFLcrWpr6tCKgW/qLwMWxfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3 < 0.3.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": false}, "0.3.0": {"name": "canvas", "version": "0.3.0", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.3.0", "dist": {"shasum": "f90a77f2d293dbd301f45fe1b42683636c81a171", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.3.0.tgz", "integrity": "sha512-u2U3K2WT1A08xDTe12R0W/BJ/eVuXrVJha3YH2Ch/AWKoMrZz2+4TG+ifR1SfS2cOdJYZVYgjwg0iR5FkweYwg==", "signatures": [{"sig": "MEUCIQDzliaFfCY9+cqEPXfsOpdtrEkmdfoPe2n7Wgf9lSh1kgIgKFnm135BvsSpgtHTT6jPZMo2nV/IP7IycBFDHVBMXqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.8-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.4.0": {"name": "canvas", "version": "0.4.0", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.4.0", "dist": {"shasum": "2af41056fb6e5542df6cae73cbc2df73c7e9025d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.4.0.tgz", "integrity": "sha512-RBbftw+QEymBAx0e8bDly5hUDJci9UV3tLW69egYNb5OQBX3TSDTY7qHxFpmYWko88NoB1Ek4G1Ew1e42j7YKw==", "signatures": [{"sig": "MEUCIHQhRHnrIKbN8z16AJqIF++MIiRvFmmk3FhmEERPRHJbAiEAsJ0CXUauo5XK6HiO8GuUCQDim+VotL0Lh5MJPZzmYBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.11-5", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.0.8": {"name": "canvas", "version": "0.0.8", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.0.8", "dist": {"shasum": "05b1d6fecfa2abd8ccf45b272bdf54efdcaf37de", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.0.8.tgz", "integrity": "sha512-usMq4m+2yhCJPxQg0LTOJZnsuR2PAbNpWRFbdMjgXXLRMxv+kJuCHUp9IQ4alvZuPNG5/4WAnW/toaJAwi42dg==", "signatures": [{"sig": "MEUCIGwiPQ8gezHtTN2bg8o3EvdcxHqJM37FgRLUOX0c2j2nAiEAmwWnkvPM3etZ+mZi/7F2z4SQKpmfhkwx8egKcq5zaTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.4", "_nodeSupported": true}, "0.2.0": {"name": "canvas", "version": "0.2.0", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.2.0", "dist": {"shasum": "51d9cc1bbf099a899e0c6af0b601eab6218c473d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.2.0.tgz", "integrity": "sha512-lBPSEd4DKW3AfepmKVSRfJ71O2BnWC38IAoWttYwLWStoEdblRaz+dFOB8ikX5TTyo2Csdm+afR6jTM048hweQ==", "signatures": [{"sig": "MEUCIEKLNYj3hjaDOmWPqq4Rgl2IbjPDp7gWUa3zGZc6D8V7AiEAtrj1k7e8+A1bOxI9wqsMHtwBno5x0kN4vf5TBcZafzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.1.0": {"name": "canvas", "version": "0.1.0", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.1.0", "dist": {"shasum": "82ba3ddb10d85da4ea413c612b3869556b0b0d17", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.1.0.tgz", "integrity": "sha512-WQuTWmbKrLCnmKgyQ1aeSMF/xZ5+K+j7cwcXYS/kIQsOGQgY1MfARm+bi17ONhWeXEdfo/M2F8sy/tB2egjMOA==", "signatures": [{"sig": "MEYCIQCgohirLOldsttDpeP33v96apHO6s0QoEJXZUulYUaf2gIhAN3IkzZTwbvQ304e+H2c2DSoHKjljkzet3luKRvmytto", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.3.2": {"name": "canvas", "version": "0.3.2", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.3.2", "dist": {"shasum": "60e7dda8d65a249c7dab6f2db8f8f8cbc0cf3b0f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.3.2.tgz", "integrity": "sha512-HYZns2Jgc/zbu7lRuXllsfMNkRQzRhlpXdu3nRa69XlkWcbU8UKDINZNDizOo2cB253yRUmtIaVVDnMwOh3TKw==", "signatures": [{"sig": "MEUCIQCRRgKI/1OgnjssXgB4Igl5nLf0kZTtQ2AdirJqPSAn/AIgBJzf8btB27JeYjS8vWnkMDUb5579iIrhqOukcLe5zvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.8-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.3.1": {"name": "canvas", "version": "0.3.1", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.3.1", "dist": {"shasum": "f7832f42d7548027ea0b9e426c528c3136ded5db", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.3.1.tgz", "integrity": "sha512-cD5VClQRuorzUAyQnXQrty4LAWzJTTsG9uYRht4klW25Sv1ARzQ0RDQqBry5LX5OHO3aMcoX61hhgm/Up9z0xw==", "signatures": [{"sig": "MEUCIBD9qSmB0bC1qf+9Mx73Ry3IlXTWa7ck0Oz1Whj4mxDCAiEAlfD2bwwHUF/i2Q2cB+eL0CF6Lu1gS9RJnQXJmBjftko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.8-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.3.3": {"name": "canvas", "version": "0.3.3", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.3.3", "dist": {"shasum": "bf30e7e9b5cee350ae7ea02e529f1254652d0bcd", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.3.3.tgz", "integrity": "sha512-TCuAX0j5YJqQJj8RydiIMAs0NE7v1O8wCwB3s+lWX3XvTGb63E+01zAmIHwvChy0GkPEZD58plNGovVwoNLH2w==", "signatures": [{"sig": "MEUCIAKd95U1PK3LTI9uLz7whNwhPTMFSFJ4eTPF3ikYRSfeAiEA5ZsKzlShpUJ2oA3lqW3TJEeBexCCW6/xXWspRwUDUw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.10", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.2.1": {"name": "canvas", "version": "0.2.1", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.2.1", "dist": {"shasum": "10860cab8cd777aff9080e23b4aec1b3a6cec1e4", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.2.1.tgz", "integrity": "sha512-PZCIZL+wT60uSN3NcYPR5dXAiANpY6Dh3IXychAecJfxz3ofq6rl/xNZxkNNYXPlOl8lFRW7VaCIbGQZmNiR5Q==", "signatures": [{"sig": "MEUCIQDNPrNDk1RSTXjSuFfF75tSMzU6zBKkhPlC7nX7ditNfQIgN4dgkRf4tqFq5BJgSeqUVq6pXx4ltl/y17cxgogjvyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.7-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.4.1": {"name": "canvas", "version": "0.4.1", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.4.1", "dist": {"shasum": "eae4e1a9c6962e8957d1ce38ea9dd119bcaffcca", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.4.1.tgz", "integrity": "sha512-FQlScbc9l08Kq4E1fB+65qf4YUOKFDv9U/E1wyoBKi5CAc6tdvgPQIROD5Jd77WHrgKq40gl6p2E4GnzJdnSZg==", "signatures": [{"sig": "MEQCIEdUUiY1bcv9VWpLp2xM40HRNmp0nTUvKXJZj41avScjAiAMJLaD70CJv4aeJoGPq0zcaCM5Bxyim+6eM4mu2dvdZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.12-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.4.2": {"name": "canvas", "version": "0.4.2", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.4.2", "dist": {"shasum": "7c972db3c9a1bb2b6cce9b46cd202a1ba02f60a4", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.4.2.tgz", "integrity": "sha512-3xL4Dd1ZDyQfBuVHH5bfnndwKdkximPpXg5GWNKO2A1jK12v7S1rHhXLcvQRjUj+ohHCRmMSDB1tPfdpUYXEsQ==", "signatures": [{"sig": "MEUCIErioH7i+Vg4I3gvpD7Q6qQX/APL/W7eOOhzXgbU1b58AiEA83rqYbxTJdOEMbupjVgQVYSuqrMG53lyJES+LsRvkPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.13-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.5", "_engineSupported": true}, "0.4.3": {"name": "canvas", "version": "0.4.3", "keywords": ["canvas", "graphic", "graphics"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.4.3", "dist": {"shasum": "a3d2420264ba3955d86fe0cbea91615659cea8aa", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.4.3.tgz", "integrity": "sha512-d0wHGe/MlDhPa0eY6P6Vn/1bZl8/tcdbTn3ntzm6Fw70knAKTnH3Q8UnAn7oGORIGMN60L7ss+LOm41AdoSpwA==", "signatures": [{"sig": "MEQCIFwVdLhfxnNOtOokamj1A6IPksLSe6QWt6MpDoPEuFvqAiAurw3o+V9FNnoIwNhwPwQrIvIZIPKt83i8f1DwOtwUOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas", "engines": {"node": ">= 0.2.3"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.2.13-1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.2.6", "_engineSupported": true}, "0.5.0": {"name": "canvas", "version": "0.5.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.5.0", "dist": {"shasum": "09b2147e647841d215e5531ed4ace8c74a26f464", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.5.0.tgz", "integrity": "sha512-GPCdxe69MscqJDjnZVp7NT8/SyhmmcWGr3zLPgZHrFiuBftVIKnCwJkO7y8wM9ZLlVf7vNWBsu2Hzrh6OgMXKQ==", "signatures": [{"sig": "MEYCIQDe1ZKmkb8en8mf+7bL/SHuCrd2roOpTjp7LGZ3/otruQIhAJKpZcGLvCED422JaPRK3MtC0L6kfnqSN3NNliaxQWF9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "files": [""], "engines": {"node": "0.4.x"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.3.15", "description": "Canvas graphics API backed by Cairo", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "0.5.1": {"name": "canvas", "version": "0.5.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.5.1", "dist": {"shasum": "c4f9cd3e0f3ed247e3f420f09f8b56b09801809b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.5.1.tgz", "integrity": "sha512-7K0SOwY0uax37uy6Df4+1m6LXkAhXZyLeobmjVYhXWiiile+2E5+0LR8+d4iaYlzyWo8XklTuCZ2YTjykNTGDQ==", "signatures": [{"sig": "MEUCICLRNxJzquWOKf9S5Z19pWOt0pQo7/w+PkEPjKNEY1CcAiEAht6+siS8jZ4f0gYaNGYsa9NXF+FW3ri+mnWMDJ4oexE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "files": [""], "engines": {"node": "0.4.x"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmVersion": "0.3.15", "description": "Canvas graphics API backed by Cairo", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "0.5.2": {"name": "canvas", "version": "0.5.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.5.2", "dist": {"shasum": "103b43ecdc6ebc44ecfc3277142dde7bae8d0c04", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.5.2.tgz", "integrity": "sha512-Doy99ZvPXXeJPCgxlyIHEFdF1BXswdEoHE6oty4j3yS/cr24G+e5LhZni50RiMuUBus4VuINoYhK4ek9gurpQw==", "signatures": [{"sig": "MEUCIQDB75gNA6EFoTWTwmPXhHVCThnYvO0IM+TQ+jFFcpzghAIgM2kBtGIBjiBG573ZjSXCFyfkN5gtroZlO7dpdw9IZq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "files": [""], "engines": {"node": "0.4.x"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Canvas graphics API backed by Cairo", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.5", "_defaultsLoaded": true, "_engineSupported": true}, "0.5.3": {"name": "canvas", "version": "0.5.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.5.3", "dist": {"shasum": "c981a657aa7a9310b41e9f9fd699fbe0e4c40808", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.5.3.tgz", "integrity": "sha512-AaU1kOz9Q73wcH9LSLWiKF6mpTZbhA15EDbOgdM0JW1hti9D9WOhn0BYEfFpf3T+NPA0JU9atkdu2gu9qV0ZkA==", "signatures": [{"sig": "MEYCIQDs8NdLIjAuxSJG3EIo9LKWv5w1Kmtk+kUjQL6nkVQsKAIhANY9uBJlCqsIV+gNwBQp63enVwrHbAX/9CzYNePC57tN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "files": [""], "engines": {"node": "0.4.x"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "0.3.15", "description": "Canvas graphics API backed by Cairo", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.5", "_defaultsLoaded": true, "_engineSupported": true}, "0.5.4": {"name": "canvas", "version": "0.5.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.5.4", "dist": {"shasum": "a7426534d04dece04e071bca896b0bcdf191447d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.5.4.tgz", "integrity": "sha512-V92N5sPSX7p94pASNwiCzyjyBsF8ISDjLpKTOf/A+1L8ryYMua93vvcrHvYUkaqtcd7bMMPHT9jCQi6EcgWl4w==", "signatures": [{"sig": "MEQCIGMgDYCY7CzxJgbC9xBlvTBEdzA1urnPiKC1mOsfJuBWAiAx1nf4cragiAO+HJE6azHl5I+PTRyh2hsrc/NO/IgPKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "files": [""], "engines": {"node": "0.4.x"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "0.3.18", "description": "Canvas graphics API backed by Cairo", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.6", "_defaultsLoaded": true, "_engineSupported": true}, "0.6.0": {"name": "canvas", "version": "0.6.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.6.0", "dist": {"shasum": "fb5168b0dcfefc3a32114ebbaed1cb875eb9c998", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.6.0.tgz", "integrity": "sha512-GeX3pX1ICXNanNA0jCNe0BA+hEaXJqInFRjsGCgbmdtYFlFbQawefLi5xhDP+7pJspXvaef8YK891KhbgsMdbQ==", "signatures": [{"sig": "MEUCIFqlQ8/EY5tqGlhpwKWRIWe7PmkIGrl8fm0DnOQtg4HMAiEAjmJYYGufDJbVjSqP5Cfyx1yKCYOpSxwpc0w/fS3N4Ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": "0.4.x"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.7.0": {"name": "canvas", "version": "0.7.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.7.0", "dist": {"shasum": "110610f1046027222d24b149962c6987f841ed87", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.7.0.tgz", "integrity": "sha512-HI56vH4lFu9/8efoNf2rmQcnX5RKV2gZ494OdoqF2+UB4wCGycS24N2aYRYvC7PkR1oz04XXCwUJwONitNYSzw==", "signatures": [{"sig": "MEUCIQCIXSESFUeSCSx/ZLtdiOdXfG/K2bhDdsatodm87jaieQIgXvcJv/tF64qr/KCcG6TTBWq7PEa9ddG+QLzuZXl9NC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.6.0"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.14", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/canvas/0.7.0/package/package.json", "wscript": true, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.7.1": {"name": "canvas", "version": "0.7.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.7.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "38f6e79e02b08e8c444044aa3f1fa4c2a5a1d83b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.7.1.tgz", "integrity": "sha512-WScSSfegDdBDFAGR4ez2g7ynWyRQM9U5bVK/MMtTzKT732L2fE0z5VyY+kY7B7yO7//FpdBJyoLltllQT16I5g==", "signatures": [{"sig": "MEYCIQCpCEwK4aYErQdec8A5x68xZiSj7TGiOlzz2gBtTp/SDQIhAKp3nWv82BZ8b6qcC9hxGjCkr2kbYudRP/lqSpvKmbh5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.6.0"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/canvas/0.7.1/package/package.json", "wscript": true, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.7.2": {"name": "canvas", "version": "0.7.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.7.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9790ce684699b4e45e7e6d664d4f8144819f5174", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.7.2.tgz", "integrity": "sha512-qmnj9fBhh5tQQYnVs6pCDNw9aivEwwdQBurBjiVBx0XubqY3h69wcS+/VXNajF700n7iy+M18wk/wMjxNF46uA==", "signatures": [{"sig": "MEQCIAIcB/6T9PkmB1bt9cK8wiHxccbE/E9glcQkTlxS0oZPAiBsPGpKd+LsutBPai5CEvLoQNKi5p4OUggFUanFzdoIDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.6.0"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/canvas/0.7.2/package/package.json", "wscript": true, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.7.3": {"name": "canvas", "version": "0.7.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.7.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "638e3c5596d2fb90bbfb891674a1bc898a376ff1", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.7.3.tgz", "integrity": "sha512-GW00yFyE0bfM+hhecr1A5Rt5ypud7Nw9rBskQ5k+zZ23gJGDLZ51RJz1FrRt2ghvU2G9VhOZPReFP5XN1G8J7A==", "signatures": [{"sig": "MEUCIGWmAh9oupDNV4MQZx5lcyLzY944GGLQSGFNAexk6sWzAiEAsgIWnc7F598ljtXVvjXAc+DvmRwWzaXZnJcQU/dIc/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.6.0"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/canvas/0.7.3/package/package.json", "wscript": true, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.8.0": {"name": "canvas", "version": "0.8.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.8.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "615232dab91b0b18aa39f37ebe9324d412f36890", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.8.0.tgz", "integrity": "sha512-dZ4ZDWz9r6dVbm8+O50QkxfLWM9kDunS4d1U+ERNhUATK6YO/oTyW3WvnpWB9GEDRbWgHjPlsQDXLSFB/GHV2A==", "signatures": [{"sig": "MEQCIAhB0m3j4APQUonX8cf24NvHm1InZmJm0x39/4VCv2m8AiBDBVRtelxsTLMI/rcKUZNB+7HhlZ3H86dxQabagY/b9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.6.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.8.1": {"name": "canvas", "version": "0.8.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.8.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dec66cb9ec513ca0d3a864cd19e4609b8367a1d7", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.8.1.tgz", "integrity": "sha512-7IdnnNU8QTnoVlbCkWeq2AcTpEb2wSJ/BmqR+0QS3kus7XCzCfKL+XsZ0w5m+QWHEMH6BpHv+DHuhRVnaRlmwA==", "signatures": [{"sig": "MEYCIQCewfyFk+sFPMibaL6qWL4/fhsqYCS7fyFgiST1X5/bcgIhAK/6CRTvIuY3OffygXvplXijDpCIGtirEe3jCEa2X478", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.8.2": {"name": "canvas", "version": "0.8.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.8.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "a5744e8cf203e35310437dfdb30c39a93e5ff323", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.8.2.tgz", "integrity": "sha512-wWFwZYCWrVSK3HqfQ/bJOTutEv3tb0fvRng4a+ti6mNRMGy+C6BoTDmeOKc8ZBlvCpvSltcQsF37ynR1phmJCg==", "signatures": [{"sig": "MEQCIFBFusmPX1tGQ2lAgo6EPBkvbJWLhWhLA1ZrRYFIyvjyAiAYVG1hM9PFroiMIuEyJo3ZjLwHyRAqP2riP3x59b0sJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.8.3": {"name": "canvas", "version": "0.8.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.8.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "b9e87be402c5050bfc8e8fd5a45646d22ca2a7e9", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.8.3.tgz", "integrity": "sha512-rlSGf4iv7EKSTwyg1UxqLaNGWkuucBtK+9z9RAYvci8m7Z0VfP2WtCibtM0voWWntIVHJ098UtvmVgDnGww1NA==", "signatures": [{"sig": "MEQCH0MAoj3gKqgr9TQUO4vfc6MpmLFn0IIJns/RgHgyWxYCIQCahbgMHSI8keFrWVSg3iW5ZbmEXxGYKdrKmy3sNroPhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "express": "2.3.7", "expresso": "0.7.6"}, "_engineSupported": true}, "0.9.0": {"name": "canvas", "version": "0.9.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.9.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "12bd6b56a9b37dff0164a2525fb4cfeed8ac1a0e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.9.0.tgz", "integrity": "sha512-Ymsgb1dSyJNMsz3RvVHieAf09manDwt/iLTjrZvUNlc8SpkpoUhZFHhjAr8jpcOiRbiEahGTeTLwYr79248xOw==", "signatures": [{"sig": "MEUCIDiMtaIWGoVXSNS83Hi3oP5GK0hUaQBPA/gk/i6xTt5mAiEAvcB+AwNaguzfN5eIzmo5Ow6ruFcFGPs4+4jsr95BQhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": "2.3.7"}, "_engineSupported": true}, "0.10.0": {"name": "canvas", "version": "0.10.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.10.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "b9072252d4a0f3ccf8cbaefa8ef5641d2d33d36d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.10.0.tgz", "integrity": "sha512-ichS2S0KnldGw+pQvx7GoLglhwP8XVPBVdkwBpMG+6PeKCrzHVcmSHSe/YqBk4rLwPmABqmxtdkogQXFh5IBDw==", "signatures": [{"sig": "MEQCIGYBUWgxk58QrzMl+A+9b6BmYmdbIy/jvXD8nqy1eOPhAiAsIueGqwtbro1v5Ud+hQYiiynFjYLmQwls+fWt2g8Uog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": "2.3.7"}, "_engineSupported": true, "optionalDependencies": {}}, "0.10.1": {"name": "canvas", "version": "0.10.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.10.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "8a16865c38113229d42951ffdea0c9f1e21a3c85", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.10.1.tgz", "integrity": "sha512-4UYeXDjHFINjiaVe06cbpLD8ZClJPz/scbwz1l3epjho64tiqwBoznLlqI70wfOhXS8ARxv/rD6D51jxia5M4w==", "signatures": [{"sig": "MEQCIFdAFVuHrf7w3kwolF7ZbWgkzbrFxGreu5vaWHkokd5QAiBLyVudlO/R4DR3TXhIeE+Oy8Pg3XEtj9v1H9szJcYDIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": "2.3.7"}, "_engineSupported": true}, "0.10.2": {"name": "canvas", "version": "0.10.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.10.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "64dca9c7f74ed9cd28e194117de113d51d17b35e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.10.2.tgz", "integrity": "sha512-S+kmRW9xzduMeOibOck23qOu164SYV69BwwMqtRBhMUEJ77G4g8Uf5QU9//3DMrwM0Z93Sw9z+EEJebEPBYkVw==", "signatures": [{"sig": "MEQCIARg98lLvyoMWcLzGjQB8VXg0jnwFMP9Az7tsx01sZ85AiBvIVPbNNaET0OKLUnIcJT/HfDpdddDli1FGjdEKwAbqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.7.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true}, "0.10.3": {"name": "canvas", "version": "0.10.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.10.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "48fd10df302a575cdcd55762ee772e4d167b77be", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.10.3.tgz", "integrity": "sha512-ehBxZn2Ezr2XEuh+inTcBfaF6obnSaf2jn13f5mRkQyhcn6kUiQmToCMi2QIrIDbCQkm/IMmq8aMDnNs9h5DHg==", "signatures": [{"sig": "MEYCIQDchN3ANm2zD+8JvGjHRLcpW++X08PS6L71AIfUqlpXFQIhAKsCvCPMaicZEbERUB4Bzh1VX45ry/e/k6NB58TSf0iK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.10", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true}, "0.11.0": {"name": "canvas", "version": "0.11.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.11.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "2be4ba475817396c5922aedd7702e7321f87dd9b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.11.0.tgz", "integrity": "sha512-v3tDLCgfCMIgUrDgBMtc+kK9lwXsv41Xqv7cNuGRbDwURsJrSfle33CVaTsxDbtdm/xAX1NTlFX8h7ZBPFqEoA==", "signatures": [{"sig": "MEUCIQDC9NuGzKu+ExwDFjd7oIhHyuN4s41yFRC7Zt7it6zADQIgTYM3Ssf4LyScYP9IxJ5rx7hdozo4hpH7GStWyiIU7x0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true, "optionalDependencies": {}}, "0.11.1": {"name": "canvas", "version": "0.11.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.11.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "10d871f39eef6ffa5c088f491082906366adb5f2", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.11.1.tgz", "integrity": "sha512-BtSwoAiLwS0OGDpKvFj7qoNSMb1vRP1d/syfhUNuIiZPDk5WID3Zj2qDBtMdzfxsCyh76f0xYAK6AJslJZtsMg==", "signatures": [{"sig": "MEYCIQCu5etlKfLxeWFoY+Qwf+hEP86TwlrCl+3GyAyA6BSQtgIhAKd1X1htat/H6JxpP2/s3BK811vZGDpWh8cKtlTTWXtH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true, "optionalDependencies": {}}, "0.11.2": {"name": "canvas", "version": "0.11.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.11.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "ef008a79bbda452032be968944c45fb49b59964c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.11.2.tgz", "integrity": "sha512-ArTinToxs+EgQexGQlb+6SSAUkmSkva4Zahqs+0d3GpygbRxS8AKIigkFrK2XmX40WiI6xKfRkCtvJcD6dvoog==", "signatures": [{"sig": "MEUCIQD2t61To05No04i6BxrtrfnRVBwDFETCMi1A0g/TCpEfQIgV0ibgOnH5hHotVc+FmavC7ahOPQLCFNBFu00+hYOceI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true, "optionalDependencies": {}}, "0.11.3": {"name": "canvas", "version": "0.11.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.11.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "9b08ab46b83ef15ef8ebeb73331ac9efa7dcfe28", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.11.3.tgz", "integrity": "sha512-2aAZuNr2i7Fwv/M1hUSS1+Ew1jLO1F6ELrS+jBdfMPeL1GIU+RnKZx0FB5q3imOiPXJrvIv61ZyogX/H6owZgg==", "signatures": [{"sig": "MEYCIQCkQTczuPv3kLUW1Mo3QNw0cMtMovMrGxcySC0dSNze+wIhAP/JT6Y/padRvxDpqGIIRHzKl85Y2ufiL4o6JEbtFyiI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true, "optionalDependencies": {}}, "0.12.0": {"name": "canvas", "version": "0.12.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.12.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "b8bc2b8792d35df5c8bf87e9e448e84180100a6f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.12.0.tgz", "integrity": "sha512-67DM0wvwQ/nQXIIeTLIaffuG5Yt/v1k7BlloIK/GgE5vyPwKPUCjHWiVyu9bwY1hpSMaa1wW4XAReWIHuIO+eQ==", "signatures": [{"sig": "MEYCIQCSF/sIqxXqRdmN7ZqBUHuQSw3POdwN4WW+n5yFDOqsXgIhAJDVSU6biASDjlJNWIbcc9NjxePZN3d9Q6UPJYfhu6lD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}, "_engineSupported": true, "optionalDependencies": {}}, "0.12.1": {"name": "canvas", "version": "0.12.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.12.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "fdc4f6550fa0d21acbaa0c5ec626f8069c640695", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.12.1.tgz", "integrity": "sha512-g5YY+XomklZKAsjsoPCzsAPj2MuolexvPDAD+NLAOYX0SYFSL3A/Pf8z25hJBUI7ujE5XZnR888XBzoUfa3mjw==", "signatures": [{"sig": "MEYCIQCpT8vCfJLGpaaVMie6xPNgJvhmsTV2oeAG8Vo6ONVQnQIhAIiSuW5Lqu6UHGfrbuKdTMLJnQxGGhIgTmB/17X8wXUM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0 && < 0.9.0"}, "scripts": {"preinstall": "node-waf configure build"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}}, "0.13.0": {"name": "canvas", "version": "0.13.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.13.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "a2bad8f5c9eda607efb44e0b11ac8b149fbb906b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.13.0.tgz", "integrity": "sha512-RXyXVb2zDvaS4/TDo5HA09qi2Su2M/MGc7ubtRpH9SgoPF60rnYuHaQwZqGDJvZYfBLT3Ys7GZ3JHTBcEMM32A==", "signatures": [{"sig": "MEQCIFU+vFkdJiovfeUx3mcCQvAgYP//SdFl3viL6gRxl6dHAiAMNgKPdT6JBEF8IMPRoKdTU1Z9za0VXv9YflAltUggbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}}, "0.13.1": {"name": "canvas", "version": "0.13.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@0.13.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "97d62dd8f18aa5e8c79496660625434e6785fcff", "tarball": "https://registry.npmjs.org/canvas/-/canvas-0.13.1.tgz", "integrity": "sha512-gztJaortj1Ww5B60EfJfSgIIWbmiKKdc8evVH3/y0g7VIPP3l5ogQVFXL1cOgK1ZCqI0o0VyB4CyBb7HBOEK7Q==", "signatures": [{"sig": "MEYCIQDUSw6cHbSfTnp6oGAruLT4PYZoD6seo3S/vzZ3O+OUWAIhAKo8XRnG30eGQVObhGRlZJjhV3bPaTEWR41pdXqMszFb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.4.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.11.0", "mocha": "*", "should": "*", "express": ">= 2.3.7 && < 3.0.0"}}, "1.0.0": {"name": "canvas", "version": "1.0.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "a7efe92587ff8630388939002abbe7671f47c73a", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.0.0.tgz", "integrity": "sha512-VfA3jXJK75QualpnvCAs4oAKFZORTUvLygmYBhM+aWUiZV8ZYhjILvR36XeRg/kHFa9SzX+RGLXkqZERipTp0w==", "signatures": [{"sig": "MEUCICZwUgSgE6stnnNz/+Z3LtU1koc95EM6slxRVpiq5AzCAiEAinXg1rKDMN2wjSoc8Vq+cJI2L66a3zukkOMravn5sIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.2.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.0.1": {"name": "canvas", "version": "1.0.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "9dcf2acefb6b0d18e014cd2b67327ee9187e7bf2", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.0.1.tgz", "integrity": "sha512-oqxhG81+3rYsXXfnf6dtaBCtBUFQO1623BbqWdZOokhBWnjvH+UHWyx0rSS3BTWZPuYta1yIH/5rdSipZX6Jww==", "signatures": [{"sig": "MEYCIQCsB2JR74eMmZFS9/QkErGmUpixX99axjehA2Pg4bcPLQIhAMsX9XjuakCcXVZB7aM8B84HtVeuXUgcLqhDqa16+6ZD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.2.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.0.2": {"name": "canvas", "version": "1.0.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "1116854805fd7236446602e3c034a469fea65806", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.0.2.tgz", "integrity": "sha512-ggIh3Rs1dLW3fYVIcWkxr3A1CvgiQOtj/x4zeZMD2EpGrWGx6BTQKCzHRPJBizXiaM+4Z448o7KAKCDWAVBtEw==", "signatures": [{"sig": "MEUCIQD1vXzifhMl/cWIBppIcYQazpESIR/s7EEHdj/djZeT1AIgfEQtj8+rZC6wSuk9kK34vAh4GusAFAWdZs0/MRIFIU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.2.14", "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.0.3": {"name": "canvas", "version": "1.0.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "dist": {"shasum": "39aaf1eca76634b3e32546e8cb6084caa4a5ee5b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.0.3.tgz", "integrity": "sha512-Rsw0xW8p/MLxYr7oGFGNV4pEGtU8TMZCkDqh3pgL575+YpwQivZlY53srHTRndXjWOQcjh6l+grHGFDRbx3vpA==", "signatures": [{"sig": "MEYCIQCeBknzbgqE3rWpYS7rAOpR4tctNOc+d26EuEbqiTD+BgIhAPgvmfHDpyrqz5y+Q8uw4nAAo2/f1iOB5/3IPUC3MR99", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.2.14", "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.0.4": {"name": "canvas", "version": "1.0.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "e17c9eef9e7ffc2df0d03e02761e5ef31d694c46", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.0.4.tgz", "integrity": "sha512-gICaj6/FAIEK65fO6YAAnOVbYRDTzJu0F/rSDXWDBNzSKGfs8/UPxf13HGclnoCI3mIVMNOuG0I6hcnZfiyGFQ==", "signatures": [{"sig": "MEUCIQD6S1o70xegWJYiZTdf3aDflxqX6yBti6/h30dM1PenZgIgISraDsD8Bg1rONZ4/QQvHg/3cZR70Ud7BFk1tRB5TGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.2.30", "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.0": {"name": "canvas", "version": "1.1.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "4b2da49b80ca86bf249a02b628a77236b81102be", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.0.tgz", "integrity": "sha512-0c5WtNCNHCo7bdeREkkXgDjVnxVnG9vjwSp8kvOJHli5dFiZzZ1fW18rvndVHX1IkNT/WrMfv95UaoRNK7BY1g==", "signatures": [{"sig": "MEYCIQDbvJ4U19RYXPIQqiGxkkIwdN7X3yifbhACRICDsG/4KQIhAO9I1KdJUmp8ni2reL6VFQQ5C83t0eCsz2YGjdDvyY7k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.2.30", "description": "Canvas graphics API backed by Cairo", "directories": {}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.1": {"name": "canvas", "version": "1.1.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "29011dda5587bdcd6b8b90ecf5d849af1cfac227", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.1.tgz", "integrity": "sha512-Dnax5GrfrpnTH5fU7pBIjYsPbHtFHCHnOjcA6BtpP86uZbAjgY3iO0vy5Nfo1zDFJ+V5ohFEs+Qk00m4qUWi+A==", "signatures": [{"sig": "MEUCIQDTPelKrvl+A3XRU8WzmtTTM402m6UGvLgQh/F4o2EemQIgQ7ygPbwUrZjbCSC0aK35RcgBHoDy5MXzJ7pkHJtiEJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "kangax", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.3.8", "description": "Canvas graphics API backed by Cairo", "directories": {}, "dependencies": {"nan": "~0.3.0"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.2": {"name": "canvas", "version": "1.1.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "59f19f4b998df3c921ff7f6b959d2b3d6518f144", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.2.tgz", "integrity": "sha512-iPjiWflMcF8lmn5OncXZDHnuNsYrp85S/wiXqV1AzfvZ9UcSyOavm8U0JE9zuK7bgdDui19nb7MLX57sCePHEA==", "signatures": [{"sig": "MEUCIFqG0YtxUciGNJGW0OpzuwTlnQPX+m/T4/YdcVVIKgZjAiEA61yk0Nhjp7y0wDmVxOzsiO5v2Baol5OEIfke/mIQcEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "kangax", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.3.11", "description": "Canvas graphics API backed by Cairo", "directories": {}, "dependencies": {"nan": "~0.4.1"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.3": {"name": "canvas", "version": "1.1.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "93e5ba3988b7e6419b3a1bdc6d948ea0eb6a5e51", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.3.tgz", "integrity": "sha512-aj/6qq1Vau+HCey+LbnGm8G7I4vvz9UvgwL6E0opoEY3mTDt6kCJcJlD85801gLZuoYCNE3EMwVRcrMN+VN50Q==", "signatures": [{"sig": "MEUCIQDCoLjdtaDniHDK4gTprPBfLj1N0Dv2hT+ksDg1K5EMNAIgYUJo/uOeQpoND50kTLGCoYD/2NLfl4AALDA8hp8Rh8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "kangax", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.3.11", "description": "Canvas graphics API backed by Cairo", "directories": {}, "dependencies": {"nan": "~0.4.1"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.4": {"name": "canvas", "version": "1.1.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "6f340959f6511320386ed4944515a4ac1ff26524", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.4.tgz", "integrity": "sha512-lxC/HlwY2LKLWsQGAat0dHvUuQ4NKvKCLfniZwufIUjHfAJu0u2+nGjKnLCwB4Og2EP5KMo7dhNztsiUoj9/Rg==", "signatures": [{"sig": "MEUCIQDykvNhYDjiDamz81O943pcF8WSB1QDJOsSvszZCoj/lgIgGT1pF43rmrTjXYQTjYNqFu33Lk4H5LGfPSpYABN2fqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "6f340959f6511320386ed4944515a4ac1ff26524", "engines": {"node": ">= 0.6.0"}, "gitHead": "c9ee4b3975aaa8fc9f6f6e0a6171c0a6cbdef57f", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "kangax", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.4.14", "description": "Canvas graphics API backed by Cairo", "directories": {}, "dependencies": {"nan": "~1.1.2"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.5": {"name": "canvas", "version": "1.1.5", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "b1a6dc736c5e901efafb686cb193d562e49e85a5", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.5.tgz", "integrity": "sha512-nzFaBPZwmoZvc5BnIh+Tp1hQVYEIRTqtHwIIJevV0WI/SR7bU0QpOx3D+0kU8XsS23U8BjeYNV9XHkynR7PyMw==", "signatures": [{"sig": "MEYCIQCI4PANXYN9ng5L13AceIFNleH28lQZ1zjhh086wfvgYgIhAIaXEjwTR6cJB0HJLwldyUbYukWeXwSmgJJKxH5/EQs/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "b1a6dc736c5e901efafb686cb193d562e49e85a5", "engines": {"node": ">= 0.6.0"}, "gitHead": "a1bcaf376a35e926161dbeefcfdb2b20faa8af37", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.4.14", "description": "Canvas graphics API backed by Cairo", "directories": {}, "dependencies": {"nan": "~1.2.0"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.1.6": {"name": "canvas", "version": "1.1.6", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.1.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "d84b088f3bcd3840fe14977cd3625f5fe89f81ee", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.1.6.tgz", "integrity": "sha512-WGxt6UQMO0aVc084DUK2lefWxSEWvNrtmTs4Hyg5FzZLUHCGrSFB9QdUAEmXnt0zNqNG8oGB9JWQUy9KjE2/ug==", "signatures": [{"sig": "MEYCIQCdDKyiUouHfDOmmFNMDBndm1deGYFuLNUJ6bYnE45VygIhANESCRJSYXrLWK49A1iA2EFvYMnXK5ga9ceAi4tMJQrj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "d84b088f3bcd3840fe14977cd3625f5fe89f81ee", "engines": {"node": ">= 0.6.0"}, "gitHead": "138abe9aa30b75e8406ceddd9177bff20ac44ceb", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "1.4.21", "description": "Canvas graphics API backed by Cairo", "directories": {}, "dependencies": {"nan": "~1.2.0"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.0": {"name": "canvas", "version": "1.2.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "5fee021dd3365f5290eaef99eda6a0070e60aae2", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.0.tgz", "integrity": "sha512-a7IF48Oo4nKedwvfTRR4DQT7E0I8qjSytyFyndKXomHWwUGhuUfxP59TM8NXW/FKQCLZNoC5A5nN+/WW1UMLPA==", "signatures": [{"sig": "MEYCIQDmaJ3Hdh8wVSbHzFgEeI1xun05KfAaXUHgJehrJbEQhQIhAKNpkucAvgSgqA0x05xNcyOeP/7lRSSkEcRyDu2bx6pO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "5fee021dd3365f5290eaef99eda6a0070e60aae2", "engines": {"node": ">= 0.6.0"}, "gitHead": "5b175ad61db1f25811d3c628e8105a5d3be4d030", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "2.1.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"nan": "~1.5.1"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.1": {"name": "canvas", "version": "1.2.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.2.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "2287d31fdd17a1e8812e3088bb151b6aab39466f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.1.tgz", "integrity": "sha512-A8w1uswVlpA9bgG7Zy4/owvRD1rRBi9bMDy4Pwipu9b7zPXIRgdbACXik5Emf/PLVuDa8IdSMKGxZbTup6JnvQ==", "signatures": [{"sig": "MEUCIQCJOTNSJ8H1567lg4Hke/7u0DDHDFx4nqaA9vTStHP8AwIgXAVq29OrGa8arMD+PXemkNByqV4Zfel79bTNKgE1ufA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "2287d31fdd17a1e8812e3088bb151b6aab39466f", "engines": {"node": ">= 0.6.0"}, "gitHead": "d40f649a5458f78dde04fe95a729fd55e52ce9f4", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "kangax", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "2.1.11", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"nan": "~1.5.1"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.2": {"name": "canvas", "version": "1.2.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "canvas@1.2.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "127a4941d31ef19c10e203120ec9c6f0bec9349b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.2.tgz", "integrity": "sha512-kjsNVTaBYDiY2pEVL8qEzaNKNbo3HsgTob4MwgVWyk9PfoJ0ZDqOjA0u9i257dDPFD9pyc+C0wwzztkWDh0mNA==", "signatures": [{"sig": "MEUCIQCewW+SUg4oUxcNcYGvEY8Kyr7L+aJqd2KE35etyZhcswIgCF62SxUnQww0ijqgMagfWhOSZXhl1W0CzydRaz1+bA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "127a4941d31ef19c10e203120ec9c6f0bec9349b", "engines": {"node": ">= 0.6.0"}, "gitHead": "9575a8f3df93e7b5aa1f1c4c3a0a36b48a3002a3", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas", "type": "git"}, "_npmVersion": "2.7.4", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"nan": "~1.5.1"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.3": {"name": "canvas", "version": "1.2.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/learnboost/node-canvas", "bugs": {"url": "https://github.com/learnboost/node-canvas/issues"}, "dist": {"shasum": "bf4124cc360854a67486211120106cdd69744a58", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.3.tgz", "integrity": "sha512-wBTtv31Wgf43pwL0ZCPtxD65DSKRl+x7rMuQxqBzanEcFK1blonVKwrc0/6RULlBjJUAxp4o1J9I5MujWAfhTQ==", "signatures": [{"sig": "MEQCICbpC6BPjbwsFhH5cw8lA/5etKeI4LydMYKXPRNmJFN0AiBYg3myYHd9oIV4HVe0wY2+gExLy+cGX+jgeRsN3hvvTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "bf4124cc360854a67486211120106cdd69744a58", "engines": {"node": ">= 0.6.0"}, "gitHead": "a7c9c0ddb556d8a5c5f683e9f41075ad9cda94e8", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/learnboost/node-canvas.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.3", "dependencies": {"nan": "^1.8.4"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.4": {"name": "canvas", "version": "1.2.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "07dec7d8a7f93d0ccd549cd705b9b52140fe3e38", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.4.tgz", "integrity": "sha512-j9lwrjnp9Ecbu3ltnPC2tHsGC+MYuWW/k21A6De8Ps2Gco95k9cJ7zAiwZFsps/EE8COw2CLHioumiWv+KcUZQ==", "signatures": [{"sig": "MEQCIGorNGFRtGZlFxkOs1ecjradBPA2n48SY2VLwlvMr+MyAiBwY6DwobaJGbeene0coY0h4M73T76g6G3tanU7bpaVxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "07dec7d8a7f93d0ccd549cd705b9b52140fe3e38", "engines": {"node": ">= 0.6.0"}, "gitHead": "21383b990e5e666827573a36c4cce737217917b9", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"nan": "^1.8.4"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.5": {"name": "canvas", "version": "1.2.5", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d8b32ca42e249687a076a02a224d928db7f60a5f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.5.tgz", "integrity": "sha512-QJ+hNaDL9sy61jgr9dZuCsrJ4SzAme08i4Z8n2p1HeUEWMiV0Lm5Q+gY5DSIICbJy5ZWc9c9h0zkT4BOjoRr9g==", "signatures": [{"sig": "MEUCIEOgs1mKjXIh15qNlTmgMXBbxNdHe8MnucsgBYQvYvyPAiEAjDIn864coAPbqxn6scSANVj2mL9uCaEf+RFt9Z4fQdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "d8b32ca42e249687a076a02a224d928db7f60a5f", "engines": {"node": ">= 0.6.0"}, "gitHead": "e84f70fd754e66a8381e317c89bacafade8abc6f", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"nan": "^1.8.4"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.6": {"name": "canvas", "version": "1.2.6", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "ba869b85cfbda4060b97b636b779d16fac9036e3", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.6.tgz", "integrity": "sha512-4UIgmUum6ud7gjKjW4/bZTq2BbJIsVCDMEPfsv4JKW+lKYGyoFm+zOSnzYikx71IqmbpSes5XDLHPGw/QZukHQ==", "signatures": [{"sig": "MEQCIDUthJliNTF2dCpnjTofzLVr6I78JhqH79+iZxs6BvArAiB3OOgs/MWQ4Rh+1o31jQNJ3RpBXgZQQHXd67BA95w3RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "ba869b85cfbda4060b97b636b779d16fac9036e3", "engines": {"node": ">= 0.6.0"}, "gitHead": "c54bac47b4d8bf67659079147ca8e9e2d2d983b7", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"nan": "^1.8.4"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.7": {"name": "canvas", "version": "1.2.7", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.7", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "36ea77836fafa968a34950507fc940bdf6bd4940", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.7.tgz", "integrity": "sha512-XpJ58tmWWkUf9DUk60YO0wDvNMCKZ9/AJ2Cmqar3VpQIfVujJ5P2ydgLNLNMkxG2HsVjzRppn11Ae55vdPtOGg==", "signatures": [{"sig": "MEQCIAlrpfu4FeckW57N0kTDoCnhIsagXwBrgPDiRC2aYGzKAiAeij1uKj9W20Lag/eOnlaWjf2nq1AosXhC+9pxd9aFKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "36ea77836fafa968a34950507fc940bdf6bd4940", "engines": {"node": ">= 0.6.0"}, "gitHead": "46a26de488fd6d3784a53d634a1339927ea81e44", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"nan": "^1.8.4"}, "devDependencies": {"jade": "0.28.1", "mocha": "*", "should": "*", "express": "3.0"}}, "1.2.8": {"name": "canvas", "version": "1.2.8", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.8", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "2477fc2199caed749d1a7db0f68cb01c0a1fc0ea", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.8.tgz", "integrity": "sha512-DzU+Nc6h2/AtDNIepQ9Ps6EwH3rghlu/Yk/lO7kIeTPYONL3eFKQNPItokS8CaHDW93F5V23GQYwOwYYP9M15A==", "signatures": [{"sig": "MEQCIBDA/Ol9i659b4oxEipSMrAlk17TFKtwrCYioSvIU9NPAiAb6B9ViSRYrQhouj3HjIMlYo6+wUbebiVmyI6B7GkV6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "2477fc2199caed749d1a7db0f68cb01c0a1fc0ea", "engines": {"node": ">=0.8.0 <3"}, "gitHead": "bb4e192b51a12003d7ecc3292bb2894f58fe3616", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"nan": "^1.8.4"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.2.9": {"name": "canvas", "version": "1.2.9", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.9", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "40f36e8f34bea2283c944511dfb575d168003061", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.9.tgz", "integrity": "sha512-QS01ywyC8x4H4cWY7LHJaYg4Wg9rEK9usWfZFIbiz8odRCAS9tobOBcxg94sFcJklGLH55QrWubBcnQSSnbC+Q==", "signatures": [{"sig": "MEYCIQCTpfJYcey0rrEx53uTqNeDQht5ZMk2dGjnmhvV611aeAIhANeNgKtEsJvHFnSQiWAwB4yJmG/dOReRy7o3efBOv9WL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "40f36e8f34bea2283c944511dfb575d168003061", "engines": {"node": ">=0.8.0"}, "gitHead": "eb97ef838658ee7a9cafb44bb20f3253e044e42b", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.2.10": {"name": "canvas", "version": "1.2.10", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.10", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "46f9fd2faf742bb2b82de279fe587ee3c9bb17e9", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.10.tgz", "integrity": "sha512-D+BUc/+zviReHruFpHCFJhj93mHLJwhCSBfnDUI4ag9/8JPpxChnh5AKueYjNHr/ARinjlyTei6eMGUVFZhdBQ==", "signatures": [{"sig": "MEUCIGFAvjWNYcd1BsPPAPy/AR+8E8HcuYL1sixvCM389Va9AiEAtf6KROdg9M0VcLjhzgRNuijUV6fTaR1SRrDT6dxIzwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "46f9fd2faf742bb2b82de279fe587ee3c9bb17e9", "engines": {"node": ">=0.8.0"}, "gitHead": "0265773fd361d493856585647bdae3410eb1fb4b", "gypfile": true, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.14.6", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.2.11": {"name": "canvas", "version": "1.2.11", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.2.11", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d12455864df5169c2a2be602fa8e0e4013a97140", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.2.11.tgz", "integrity": "sha512-EcTN9bzr4mfCmUvnyTDBMGxEllALKcqTsFrKdiOxPgBKW3Dg5p6ZiLyXXvKFUcM/XgewfmjQh6AvWV7vZwassg==", "signatures": [{"sig": "MEUCIAtzhYwwjINyPW6Tl6/O4vHPuAKHB47GrVCXNwphyKvWAiEA/LVYLRKs2ZNL5+w+kZJJoTiSr6HlLHala9zylTnz0TU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "d12455864df5169c2a2be602fa8e0e4013a97140", "engines": {"node": ">=0.8.0"}, "gitHead": "7a8e6de568d1c04eb633d4539c2fee8c69c4e337", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.14.6", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.0": {"name": "canvas", "version": "1.3.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "5b89aca549d3979ea13c30ed980f7a94f2fd6e55", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.0.tgz", "integrity": "sha512-m2Q8Y6olOrWk8LnomSh7xgi53Mdd4Kdud3fkkAjJ5TsLBRWJRUnr6ehsbAKA4KFVTARVYK+mGC3h4BigeFA7eg==", "signatures": [{"sig": "MEYCIQCehUK5FO1P9i0NZhh8F4SnPaotTKErp6Kz1l7VFe1GBAIhAJKKcRYU7ytgZ9YV+3NHup+xI3PG3+jYBq45OemI2Fb0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "5b89aca549d3979ea13c30ed980f7a94f2fd6e55", "engines": {"node": ">=0.8.0"}, "gitHead": "5da42e82fa6b500590aa8c3f0ec7d7d6a6de199a", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.14.6", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.1": {"name": "canvas", "version": "1.3.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "a1ff9534db9946a416408e59a920453e83ac2a94", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.1.tgz", "integrity": "sha512-f55vzJtY1BlaA1uBNCUFE0CuYjSCuC86yvui5+VudBQBEqN7GHIUQgu1iyJs85D8Vh1nGhWhUl1H7fKKJ4Vr/A==", "signatures": [{"sig": "MEUCIBxeOBlPPoX0ygT2mqXFiDYz/ykZvZ3DBtRZaIV+U1ZRAiEA+v3UVcWLhfFlSmkvvAgBMpNLR137xQKOZu13Ak5/WmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "a1ff9534db9946a416408e59a920453e83ac2a94", "engines": {"node": ">=0.8.0"}, "gitHead": "fabf6777a41c0eefae94f49dec89af3d0602a6fc", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.9", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.2": {"name": "canvas", "version": "1.3.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "9899e6cc605115d074a5761448a7a4a852f9f583", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.2.tgz", "integrity": "sha512-ZScAKNs49HoI6h/8nb5rTbcf+FAPXRgbQkCw1oxVc+ZWJzD5Tu/rcQkwHpyKLbTN/IC8HlkGaQqcsdF9hTif7g==", "signatures": [{"sig": "MEUCIQCzE98BVTXsoCOizHIz+H34rcR6MB8YgFdTQAqLmcpioQIgapQAGeRAugBfdXoXUUgPDbIuAt5QjkAaKz4iW540+QU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "9899e6cc605115d074a5761448a7a4a852f9f583", "engines": {"node": ">=0.8.0"}, "gitHead": "6ea98413075bfec3762ab2d1eee11187bc741271", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.9", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.3": {"name": "canvas", "version": "1.3.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "6fed2089a2b15f111faef56f4eac27ab87232aad", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.3.tgz", "integrity": "sha512-7sPAocENIadmZ44nRzioIdXTFK64Mfv13Becobuab08pPq9bsbRoDafpXwLVhSbpU2Jb5jEv5AkhUIDu5L3KDg==", "signatures": [{"sig": "MEUCIHBUr2RX380LlF4gw/iPSBlUMmjlkvAHZGh+FCQaef71AiEAwVDl4m8etwEQ/wstsixVk9Ux1ocDo0QW8lihGNT+MvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "6fed2089a2b15f111faef56f4eac27ab87232aad", "engines": {"node": ">=0.8.0"}, "gitHead": "f12c54fb392044a819f6c7db9acff8e4b0aad12f", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.9", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"nan": "^2.0.9"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.4": {"name": "canvas", "version": "1.3.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "5640d2eda1ae33b7c7279346dc253ceb6647c71d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.4.tgz", "integrity": "sha512-Mp+QVpdqyZVY0EJ0xdQkfmsEGloJ+UOz7lI1MvgkOreYc/c+LeboPxrrXOMaeurTHOKeP+D8n2hN0gsqJa9g5g==", "signatures": [{"sig": "MEUCIQCHqo+VZhv6NQV0Ml41JXNf79sKFyZtG/0C9zOri2Ts+gIgM4m9InArm7V2B4asfbPyvjHwGXscisg/VZPErXDyzH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "5640d2eda1ae33b7c7279346dc253ceb6647c71d", "engines": {"node": ">=0.8.0"}, "gitHead": "873141d89b89e0891ba7650f728fd0b37d36c924", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.9", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.5": {"name": "canvas", "version": "1.3.5", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d9fb34689fb57791738c1756864016b40b1763f5", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.5.tgz", "integrity": "sha512-2F71K5Myh92TxZ+PEEcD5FjPiaGJpnrSnliynbsZslzdB1Vr9EKHqiU4xZfsz5rQi9onzTZ5w8ThxpAgvPyX4g==", "signatures": [{"sig": "MEUCIANQBO/1SDGqmGpOvgYrhNrYnciNAz2B9hvVVLNf5ptoAiEAkmXIPdn6dudb7yvyyHmr5EYSaGOMeuoN/eFWwziRQ9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "d9fb34689fb57791738c1756864016b40b1763f5", "engines": {"node": ">=0.8.0"}, "gitHead": "c65be2ac4e55cb38464660e13d1f442ac986ffcb", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.6": {"name": "canvas", "version": "1.3.6", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "3cbd0628eb97a1a2feb75c3ddef9ca6a95ddbbf6", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.6.tgz", "integrity": "sha512-3gKO6cP0GGfnQOWoAzhNNuXm5KFehsz3YINub4Gt3rTSpMoV71lO0fYpx3KuKvxM2YVWNXp2Bg7Igtm84cy0bA==", "signatures": [{"sig": "MEUCIQDLGquCcchBax/hEJBMFBRS+l0xZnVElSMZ1N+Z+BegxwIgUtnJde2dK58Ujl+8Fq/L7PHFmnjA705hM4hgwESiMQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "3cbd0628eb97a1a2feb75c3ddef9ca6a95ddbbf6", "engines": {"node": ">=0.8.0"}, "gitHead": "973ba5c3466d8472e136d0da9913e6be74ab7882", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.7": {"name": "canvas", "version": "1.3.7", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.7", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "19253d4ab73024295c2f1cb7bd6bd97f4df0f396", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.7.tgz", "integrity": "sha512-PeqoWzz0PEp7CEZ/4VBFUMJy0yYCQTi8YmVzXcHNsijKVydCRZEClzvSqYWaeCZj3kH/QJhe2a40fwaZQZEiGg==", "signatures": [{"sig": "MEUCIEMzBhCuHpFgenx8CNHlg5bg4CTwWeWLLqBAg2X81sSIAiEAzHHVnLJrA8Yg3JtLngJ4qqH00TLGgwnaD4K3LWalDoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "19253d4ab73024295c2f1cb7bd6bd97f4df0f396", "engines": {"node": ">=0.8.0"}, "gitHead": "32c7944dde8eab7930bb31e7dab07a83b8438caa", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.8": {"name": "canvas", "version": "1.3.8", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.8", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "9e08ab2fd2517931f5d7280aa3cd3a514cc4c6c4", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.8.tgz", "integrity": "sha512-zGodcLA06mfzDrXbcGei7VcpKQDXVAysGQe4onmhRPbkp4NdsEBfm2nW0Uw6zDdY3gysdvlVmSPtvlxZut6tag==", "signatures": [{"sig": "MEUCIQCazr8Opvxqx8QpiGF5napkk6aGPf2EHC3pTdTJFhaz/wIgJjaPmJAhGeBL/GxVzxjD53lHNWIZguJujuPJH/uG+vA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "9e08ab2fd2517931f5d7280aa3cd3a514cc4c6c4", "engines": {"node": ">=0.8.0"}, "gitHead": "db0844e6b0cab3e57a5bd29f2df6b0f7396d252f", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.9": {"name": "canvas", "version": "1.3.9", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.9", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "de96344b347083b1228a5596f0babc2032b84ed1", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.9.tgz", "integrity": "sha512-KdNi+nkvMmfXwxq7yz8SPO5cXLDEO50CVwm5g7xU6ywCu0WGL7xLheUblVd3NDEYRDarwuzz5nCnAlx2JaFQWQ==", "signatures": [{"sig": "MEUCIGy9/yraXjobNQblPAkpIUGKkEEwVhQbsd0GWpG4LaC9AiEA9ewvqMerGwL4LYqN8LVrij4hV5yDhunbL5epeCFrHNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "de96344b347083b1228a5596f0babc2032b84ed1", "engines": {"node": ">=0.8.0"}, "gitHead": "7a4e56e2c53945adc9eca7d1c4cdab85ebe2127e", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}}, "1.3.10": {"name": "canvas", "version": "1.3.10", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.10", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "53641605f9999c106c94774943f8da132acbbe86", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.10.tgz", "integrity": "sha512-+S1UsDJJTHZu9II4+Kv21x8F+ezrzw+64U4Fl6AdP7oY8RPJvXrLlybyCZdq2qzwNvEYpSxkArvcRwvx6KUFlQ==", "signatures": [{"sig": "MEUCIQCrT7dq/athtdJjUWtrpXHxjSnYRFrZP+oTaEc4e51UuwIgSvMWt9pG3TlIS3MkGNamud+QLxScdt8KDckR1doK+Vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "53641605f9999c106c94774943f8da132acbbe86", "engines": {"node": ">=0.8.0"}, "gitHead": "c02a6f1fd1e39644e7e16d4cac672f9550c8a17d", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.10.tgz_1454839071682_0.8974597570486367", "host": "packages-9-west.internal.npmjs.com"}}, "1.3.11": {"name": "canvas", "version": "1.3.11", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.11", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "c7745559b04f21455c25322278bf68e1aba0544c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.11.tgz", "integrity": "sha512-RTY9kwg2O5Vdl8t0YfiT2zvautjc5xmZR3Q8pqJzFKO3Ep0tbh/oWC4laeHyJ9PKkRCgttLBG9attuSom9ijWg==", "signatures": [{"sig": "MEUCIQDAwRlWKwxA/x/Mjp/v6fKM3l8XCJUE/jtFsRNwqtkhHwIgXPZqUdfS56gE1KyYKZRFrInDQyGyEI6iVisfXwOj7Tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "c7745559b04f21455c25322278bf68e1aba0544c", "engines": {"node": ">=0.8.0"}, "gitHead": "e167d94a3f206e7fc9d651e0ab4796a589f89bfa", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.11.tgz_1456821266501_0.262484943959862", "host": "packages-5-east.internal.npmjs.com"}}, "1.3.12": {"name": "canvas", "version": "1.3.12", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.12", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "0e6d7b78d6c9730cf1129d1138b28d6d52667e93", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.12.tgz", "integrity": "sha512-iK5CnNc7VU/Ez/l/7bqtvOuEWEa4JBrM4JDBKF0vsw2bS7qfSC+ue3en3ZgK04uLLNpdw5cW1aUgDoj3Ghtviw==", "signatures": [{"sig": "MEYCIQDavN7pYlTyH+Cqe+H6t24JmO5V/DyDTlqhiME7ZXgc6gIhAOYFMpDcZ4FPegDQJo0OzWIn2iKdsdo6mB+be7sVwh3v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "0e6d7b78d6c9730cf1129d1138b28d6d52667e93", "engines": {"node": ">=0.8.0"}, "gitHead": "a3492e3c3355b13433e1a3ddd5197f8bfa90f26c", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"nan": "^2.1.0"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.12.tgz_1456821769858_0.6099298796616495", "host": "packages-9-west.internal.npmjs.com"}}, "1.3.13": {"name": "canvas", "version": "1.3.13", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.13", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "a030ca37040d60185d17db91acff232d8813d985", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.13.tgz", "integrity": "sha512-pvOrOIbJO2I6NXCt4znvGKPR0/spi1aYbuY4Dk7WQPRWfIXrbolJAwgwOB+WXBNeXa6Z0idxnIEv9OBeWtYGUw==", "signatures": [{"sig": "MEUCIGV3rZHnYqgTDE6rOE2P2HFw0O9y0z97EXjHTrmALjlAAiEAq3tduxVSj763Flhx3nareAeo5FBCOHSXrV6jGeoGKg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "a030ca37040d60185d17db91acff232d8813d985", "engines": {"node": ">=0.8.0"}, "gitHead": "965908e523cd3e778f1ab39a5bd16f309c1eb172", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.13.tgz_1462107021715_0.877737492788583", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.14": {"name": "canvas", "version": "1.3.14", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.14", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "6d00ade290f4922e6db8c663f24c6934386332d0", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.14.tgz", "integrity": "sha512-FdxB+CWcJSeTSSzs9Nv2qAmgK06u374Ua62VHomTnyWz2n0UqKsEovvDYKjNm1fc4aYllzLhdvx4LheqIH7iLw==", "signatures": [{"sig": "MEUCIQDf+4ne1e3q0dQqfEJSGBCQMn/CS+UKZL/X57SK9L7S+gIgN1Z1LYR96iFJDbl6JjRG9AzfatlCm0zVnsWYaW4mcEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "6d00ade290f4922e6db8c663f24c6934386332d0", "engines": {"node": ">=0.8.0"}, "gitHead": "d18713aad4e0635d120c25fad667cace68efca13", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.14.tgz_1462460494413_0.7940986482426524", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.15": {"name": "canvas", "version": "1.3.15", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.15", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "19a6c4284b282d918e9611a454ff86a03d1d71ed", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.15.tgz", "integrity": "sha512-FeuKrDyAENOdC1hYdVTczuv2kQnoF+bnTUKQrAt+uvudx4BNJCsRqu7hdsHux5CKEJ8xgb6tW4GVmR/EXi7jNw==", "signatures": [{"sig": "MEUCIEhMLqJhl726qok4Jx0GOKiTm/pXNWKSqvZmRPs/8nWQAiEA+EJ5JCyQeUiBBQOPlKvwvbdEuSaVGP8AaPqH7Czrs/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "19a6c4284b282d918e9611a454ff86a03d1d71ed", "engines": {"node": ">=0.8.0"}, "gitHead": "258797cf3b29ed72defe74d4d125b2976105f843", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.15.tgz_1462813486072_0.7982383605558425", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.16": {"name": "canvas", "version": "1.3.16", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.3.16", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7ebde8c54d098fb59b9b598f94c908f6e939147d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.3.16.tgz", "integrity": "sha512-+z5/W/ABjg23aI5ftIKGpC6Le8BJesrrzyOn5j5YTwwwSsDAluiBCcIu50Yl8ungir52/5VVTItkEGSj3TJI5A==", "signatures": [{"sig": "MEUCIQC212NZBSmB7rPbU+kjVsZ0qveq3fLm4bNN57KCqVfBLAIgOIkaR2C2BALh0rOZl5ZjFE2PqnSPIKAm7MYgzbFshfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "7ebde8c54d098fb59b9b598f94c908f6e939147d", "engines": {"node": ">=0.8.0"}, "gitHead": "d41d6c72585205e4437eeda24b7e968619f5c29c", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.3.16.tgz_1464536872556_0.05594937806017697", "host": "packages-16-east.internal.npmjs.com"}}, "1.4.0": {"name": "canvas", "version": "1.4.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.4.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "5071559b39f8a21964486e6e36b1c42b02392d51", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.4.0.tgz", "integrity": "sha512-wLF0PODy89iv54HBkz/ZRN7ztA/Od16pVLLbJa0GtctH9SvnY6eKDGmPRIbXI1wRCRrlj2JBz/Q1Mu98rVpxeA==", "signatures": [{"sig": "MEUCIAoIfQhMbtwz0xSk7OCWiGT6Oy12kcU0c/TYSgGJDVYSAiEAwHW07Xhmv52F5Ci828FsYLdx2BHU7d9OOD3Ek9nK3Jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "5071559b39f8a21964486e6e36b1c42b02392d51", "engines": {"node": ">=0.8.0"}, "gitHead": "b470ce81aabe2a78d7cdd53143de2bee46b966a7", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"jade": "^1.11.0", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.4.0.tgz_1466406871722_0.4620345386210829", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.0": {"name": "canvas", "version": "1.5.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.5.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "fa1935b7e9d4fc1509a9402e1de436c7f8cc14c9", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.5.0.tgz", "integrity": "sha512-sEOt13ormvNDwatFVPs2BFy3yzJO6UaiBwiRoTyd421zpcU0N0V4c9GpZ/cyFG5IQfsmjm5sMGxN3I0oikPGug==", "signatures": [{"sig": "MEQCIDzyMllgpWNdC9r7Y4wUnEFaAH2vE53DwzkGhHREwIfmAiAtVCnNK7IAcSySM8Rvbi0SCbZgcPjh1HyGLdrwu4zZyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "fa1935b7e9d4fc1509a9402e1de436c7f8cc14c9", "engines": {"node": ">=0.8.0"}, "gitHead": "5812a8d6b84e1887520e9cdaf5596f9c2089f687", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.5.0.tgz_1473612477339_0.7720053773373365", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.0": {"name": "canvas", "version": "1.6.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7cb6cdd73ebdca7dc6053249626d7e798dfde176", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.0.tgz", "integrity": "sha512-ERHvRVXa9fFiusFLiWhVNO59X1373x/1pHRxQnxGir0uTvNFBlAVltbwXcoIz6MKgaPzcI/J+qmlIk41kc2ROA==", "signatures": [{"sig": "MEQCIHlwSDxX+cfr4BbE0oXRjwL2ju5GuLAdiltogtyRkhb8AiBsxYWmZCpzjWuKxtxeVJhdnQ9jT0RA0fjzHeTvgiB6IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "7cb6cdd73ebdca7dc6053249626d7e798dfde176", "engines": {"node": ">=0.8.0"}, "gitHead": "317a23570895c4a4688b5bdff17c703680df914c", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.0.tgz_1476650784129_0.25153761403635144", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.1": {"name": "canvas", "version": "1.6.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d331f5dbb184a4faf5b0a00f265517165ad6d8ae", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.1.tgz", "integrity": "sha512-Z+GJRzcgOrvFGLnu19TEhRCHqps1MMhupdObutrvB5AoGsYf7uSbqk3fa3pU6Hne7mp10iuVqiksWuNIH9AFTA==", "signatures": [{"sig": "MEQCICva7dW2vw7MhBAB21w9A4ppUNH+2+Oj+OnfwGJ41Kl3AiANseXcoaVHrDadg2z5mjcn7adSOdh9tsai/98nT8e/0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "d331f5dbb184a4faf5b0a00f265517165ad6d8ae", "engines": {"node": ">=0.8.0"}, "gitHead": "fcf0ddcc2878c4bc168e5d08e8764d2c90bd7b3c", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"nan": "^2.3.2"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.1.tgz_1477228668724_0.6072116971481591", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.2": {"name": "canvas", "version": "1.6.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "12b56e3f00e7880aa45e3aae59fe75237720aaa4", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.2.tgz", "integrity": "sha512-kTMn3K2uUWQqRNXGuQF2UA/KiiwockfrVYMO0fKLtWqXX/VGFEEq60zJb04uvKMKPxg3ByJlScW02KlcNzLu8Q==", "signatures": [{"sig": "MEYCIQC6SW4m3K/lHDkBndbrAQJ/Iq1Dv2srUrPgO7rzmTK4GwIhALiJ0fUR33Axcst1C0me2zeF3RruKn2r0rFUk97oP/Mi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "12b56e3f00e7880aa45e3aae59fe75237720aaa4", "engines": {"node": ">=0.8.0"}, "gitHead": "bdc72016423b796c8203fed5f3bbda53536d31c2", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.2.tgz_1477855890935_0.3815066539682448", "host": "packages-18-east.internal.npmjs.com"}}, "1.6.3": {"name": "canvas", "version": "1.6.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "3125054bf08935e6abea12f68d05d27906fc437d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.3.tgz", "integrity": "sha512-KwzBj8fhkvL//7aSBEEviFLcgmMB0wqAt04EI5qneZsAiQZzDArREpv41A403KHB7/kxrmJeTRnELZQ5f2byDA==", "signatures": [{"sig": "MEUCIQD4iOi/AjS25G9OxwCboyY72LbXWLKIlTIhDMSIUpxnCQIgRIRdc7CO6KTzZuBKYdVw4RWUlixqUcH1rmx08zI8ua4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "3125054bf08935e6abea12f68d05d27906fc437d", "engines": {"node": ">=0.8.0"}, "gitHead": "a3b53f161862b61b4a485fab98af73f8ae876ead", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.3.tgz_1487102481324_0.8352769748307765", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.4": {"name": "canvas", "version": "1.6.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "b9f53f7abb12b4f4b21c5c2d56d8cdfd479c0055", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.4.tgz", "integrity": "sha512-IHWSSYwKq1U8w6HXTqIIKeuA3x/xz0iPkup5ZrBoq78xNqh7CjLyvdl612s2IbQBLqkgsPrGMTLoitWQYH626w==", "signatures": [{"sig": "MEUCICUQxpsT4/koTgqsfKvU0APOWUIZkzTEpJivcOsYJxF8AiEAlny8ZiUMX4D2wwLqejUhDlOmDex2lJiBJERO0aqFUMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "b9f53f7abb12b4f4b21c5c2d56d8cdfd479c0055", "engines": {"node": ">=0.8.0"}, "gitHead": "3a930e4d028aa0cd6af1ac6caf34fe585a92909d", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.4.tgz_1488134763111_0.7420994041021913", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.5": {"name": "canvas", "version": "1.6.5", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "557f9988f5d2c95fdc247c61a5ee43de52f6717c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.5.tgz", "integrity": "sha512-3jjViWyM9GgANdc08oGNThfRUB4R/FfkHzuB7RutcDHebJ4jhiiEtR3oQ7QSHuzEZxc3ty7V1BD405RsXdpNLw==", "signatures": [{"sig": "MEUCIQDIc2y0KPpUPt1sFnq+cOanw154uDzPaBpHpWqf0AK0HAIgZQsBJ1gj/NAEqwO9ykx1Tkh0h2A1RqkH/AR4vr+exo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "557f9988f5d2c95fdc247c61a5ee43de52f6717c", "engines": {"node": ">=0.8.0"}, "gitHead": "451e6da9ba30888c08e33f16a7aedbc9425c753a", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"nan": "^2.4.0", "units-css": "^0.4.0", "parse-css-font": "^2.0.2"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.5.tgz_1489832623269_0.8676735064946115", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0-alpha.1": {"name": "canvas", "version": "2.0.0-alpha.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "ea6731156415fc72065d153321153f62f2ea7b7e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.1.tgz", "integrity": "sha512-Wbzpn2lJKtKBbVL2ZODU/aJCHb3o4S6XUQvJ8ISv+4tsQ6NSi7O1k4yxvr/2PqDe/W7QN+WPHx+uVYYYqv95PQ==", "signatures": [{"sig": "MEQCIAOq1niXjC4JD/KV86H6o6I0TE0DN81O32qsg+hEuU4YAiBhehYb72MmAGtFjrdp+CJ5GjhV7Yq0lQnipDWwqL2TNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "ea6731156415fc72065d153321153f62f2ea7b7e", "engines": {"node": ">=4"}, "gitHead": "6b72722ca65013e4303769e5551aa877fd5d92b4", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"nan": "^2.4.0", "units-css": "^0.4.0", "parse-css-font": "^2.0.2"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.1.tgz_1493849200728_0.11173344543203712", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0-alpha.2": {"name": "canvas", "version": "2.0.0-alpha.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "2b88e9afd6e90bece7dc64b50a2da8c7e5aab5d2", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.2.tgz", "integrity": "sha512-X+yEgc0jihNGwTdr/etoGBCvtMA2KVjxC+K6h2j3DbcQIiFrNxTc++GiWhpJUOBKBlyPGHmlIP3ipVMnzl7gzA==", "signatures": [{"sig": "MEUCIQDsLEQLXQjARC4B95Ybxwnn8UhRn6PavIakl0q6O9hdwAIgZHtc9+WsYiPQZ5vP+BOvSo+JyL3HgdJF+fHqwwWaYPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "2b88e9afd6e90bece7dc64b50a2da8c7e5aab5d2", "engines": {"node": ">=4"}, "gitHead": "82aedcdfad0a83a4cf681a7b34dc258138578a0b", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"nan": "^2.4.0", "units-css": "^0.4.0", "parse-css-font": "^2.0.2"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.2.tgz_1494080719434_0.7654420319013298", "host": "packages-18-east.internal.npmjs.com"}}, "1.6.6": {"name": "canvas", "version": "1.6.6", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.6", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "1c7af9e07d003dd082698b4a4e027773281029ca", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.6.tgz", "integrity": "sha512-6E4FStIGsKiVG11nJHo42/zryM3+0/luss/IwGfhzaUlv+lnGsxsqJMSMc1JolR3QxtL+x8x4KlVWbVcCGmFfg==", "signatures": [{"sig": "MEUCIQCtlOjrcn3bzQacerQA4MxKcQjxAt4KSVus46FcMVljnQIgHY+f7T5/HcROM0/DfkUUYe82gErn0TSI09bfMIcO84Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "615f80e18b87c761f8ac91304762d1cf87d244cd", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"nan": "^2.4.0", "units-css": "^0.4.0", "parse-css-font": "^2.0.2"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^7.1.1", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.6.tgz_1501537663970_0.9591554084327072", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.3": {"name": "canvas", "version": "2.0.0-alpha.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.3", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7777e7f4c0c05a6883a083afe43b2ca235a04b0d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.3.tgz", "integrity": "sha512-kzMqmVgSWerY6Gi1OzB81NzztWehlZOFlvftZ+gOv6bNktLXTeTmaPZgvMRKe7yIJa1ezrOn1D6VuAy4OsW+RQ==", "signatures": [{"sig": "MEQCIADImDiVxA3D+bv+4pEFDqLXgFj8+QR/rf0bATCu9q2GAiB9/ryFCrrpFWhSCfAb+wGu01Xrm9KjKJUTRlOyse8yzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "ef28969b2b82ef438c9d267f79925141472a6a38", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.3.tgz_1503914163734_0.6679172322619706", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.4": {"name": "canvas", "version": "2.0.0-alpha.4", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.4", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "a4c8ad09c2f7d5c562a1cebc740c4fee23f08bbd", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.4.tgz", "integrity": "sha512-BIgWFIJbPWBtfglAmXUZKtEOehnjfzvtVo/lzy7kz48bhGWVhuWULUXbJba9D+SjUVFrowKzEcu4Nk3OsbnAAQ==", "signatures": [{"sig": "MEUCID+jNwyv1JFy2QF6BvjasdiqCMnQu/i11y2QFV2nXmazAiEAkPch8idmbNaaVf9fltOeegEE5YUdMI5QqXkwksRBemE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "3370e6471276fc0236a8dea50ecea7ec42d65654", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.4.tgz_1504035415291_0.5784664587117732", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.5": {"name": "canvas", "version": "2.0.0-alpha.5", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.5", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "bd51c5bbf2be070180c42f405e30e330d95982bf", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.5.tgz", "integrity": "sha512-ozqQOXoydM3dg+k/0aJ1IfmogVAIPtTby9bHRkR0LsD+OnRvH/pdYngbkxBxi5FCifnpSXp3YLTVlTJ5sCeFYw==", "signatures": [{"sig": "MEUCIQDekYG2g52zYj2bjhucOFkqQam2rT9qXc7xRPy2f7qBRQIgTbcY7deGkUwabOPD72Bni61WAhgy+TGGaIX9bK1DQak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "d1b9d1933b738d2b0dbe02dc070ec62e375f0a17", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.5.tgz_1504604964912_0.3951511410996318", "host": "s3://npm-registry-packages"}}, "1.6.7": {"name": "canvas", "version": "1.6.7", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.7", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "2d8a04b453ec5d6510727cfc697e236dc4ae85dc", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.7.tgz", "integrity": "sha512-QLFCd9ca/Ww3s4vUf1KxmTX2QoYu8sZkSvXQ36RdbqNETDjn/I1H01ZEtWGGLcY0T7zNO6nsGoHX+0zrMy6JrQ==", "signatures": [{"sig": "MEUCIF4b1lZvf8zBkON9Oz90Hq3YXyok7PyPyERaAlagfgGMAiEAv3AeqzXkGv/dEsWct9u7oDZdPz4+I6k+yEYrqUd2Jt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "db4e48c3936095e3bf191ab7b70d91eaa86db37c", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"nan": "^2.4.0", "units-css": "^0.4.0", "parse-css-font": "^2.0.2"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.7.tgz_1504863402684_0.15115133952349424", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.6": {"name": "canvas", "version": "2.0.0-alpha.6", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.6", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "5538c4d3bb3fffcd23c8e0bf2770c9f5b07b543e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.6.tgz", "integrity": "sha512-HRJWAiC3pwMlc7Xf8RGntrmoaCfc/Jd66TbYXXhUhiOJJUUUYXW97Zn2j8dBiXxpeG4gt3jALceeA9OQUTqNCw==", "signatures": [{"sig": "MEUCIQD5QBXoBVJMINbuGIt+IrshjWRbNDh5edB9Lm6Z5ESW6wIgbohxg4CD/SsWds3JwMpbg2ud8uwde+DZViM5noQq8vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "bd72c0ce85cbb3da3dfd72dd404244411d5dd4eb", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.8.0", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.6.tgz_1509970555835_0.5856152281630784", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.7": {"name": "canvas", "version": "2.0.0-alpha.7", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.7", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "adc20799bb026f73dc27d6020b896d9f514b8708", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.7.tgz", "integrity": "sha512-P+1vJ2JOIpbuWAlBCrGtVViaxU/bouUdMjNcbwgVyBB+jGENeMBHZa7sBptbHozgLAKJWzV5elkEDydjZ6DOfg==", "signatures": [{"sig": "MEQCICc/UjE12A51OJXRoC7ZJm0+WOmgEGtBtLa8IedoltXDAiBVZtBeRuAGzjtNsEmr54p19oQUWCyoR2s7etnhPb90KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "d1bb195528cbbb8c46744bf14a11324e28882186", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.7.tgz_1512405294485_0.1766383689828217", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.8": {"name": "canvas", "version": "2.0.0-alpha.8", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.8", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "b148eb5a215f105091f505227eab1139d9ba2cbd", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.8.tgz", "integrity": "sha512-Dx/i98S9qk42Mb6WN6ibS5Kz89idPbkXHiPKZ7boXdcWYOBtUZK0w3rqJAFMRIjahsv8rLxPFQviDABE+gEYDA==", "signatures": [{"sig": "MEUCIFq7ttOz3QgncIcOo2Q1i8OFDQzPIjCszyBLMdCf3d/lAiEA4xTdRMh0FYvGrAXeOla2XSyg/ICXZRUTJzVIgi1/6c8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "e79f0a9f5f69ecbcf64ed7a27969b9ff72e8a2eb", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.8.tgz_1513091144731_0.24813076225109398", "host": "s3://npm-registry-packages"}}, "1.6.8": {"name": "canvas", "version": "1.6.8", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.8", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "1c9f85793d75022c75572d498e3b23936a75588f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.8.tgz", "integrity": "sha512-8jmheBLwpJ6fJjOoNDk2JSXBv10zBhXINphi8e9B0//upEJD0HWacP53s9c6yWfldhdiS67c9rIoAG5DemsWTA==", "signatures": [{"sig": "MEUCIDJJTG4b3xFyi9dCUIGTEooHuzSQTp0MhrvqQGlSMchzAiEAoP5Z3+xSfxB1YqbFGcn+ik4P9P6cc0wiuNEPTqiEYbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "590d414e2092be62d96a56ac046d6dad23e987d1", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "*", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.8.tgz_1513091305419_0.08175730728544295", "host": "s3://npm-registry-packages"}}, "1.6.9": {"name": "canvas", "version": "1.6.9", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.9", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "e3f95cec7b16bf2d6f3fc725c02d940d3258f69b", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.9.tgz", "integrity": "sha512-fW+6roRnlynJutojs5NigsonZADfCB4A4Vf/PO8S2sVESDCfC/HTLUp7uybg0KVVKBP9HoyojiQ1E1drg7igog==", "signatures": [{"sig": "MEQCIHNHoIUPRYhD1kBl2r5RM78956MoDTh5GWUeXwilwvhYAiAuaxQh50YnCT/Get6UylCq5eKkOutjUtECgl81CeUw9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/canvas.js", "_from": ".", "_shasum": "e3f95cec7b16bf2d6f3fc725c02d940d3258f69b", "engines": {"node": ">=0.8.0"}, "gitHead": "df74e57af4b909eec1db6a3704e96b52ef689ae3", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "0.10.48", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "^2.5.3", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-1.6.9.tgz_1513817564027_0.9688482345081866", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.9": {"name": "canvas", "version": "2.0.0-alpha.9", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.9", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "b0b3d9b1aa00f686e8d260bc172c80f6d06af0f8", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.9.tgz", "integrity": "sha512-H0wOexRlHg1bE6/JYIaNRcDj5B2RpuCDqJrO/J8UTPMjM19T1FbXfqjLYI5sZ1oua2E1Bpl4FL8bqzx0pPBalQ==", "signatures": [{"sig": "MEUCIBfVgpv0WnFqQgcDVyxmP4TYTNgCwV9ZrbijXU9cXLPcAiEA0//2i6IuXgrAKWH9xIbZUzba2Dn6fnd0SQci7PAV/44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "1eb1aa0ecd367b6d2afc33952316d1398e4e39c9", "gypfile": true, "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"nan": "^2.4.0"}, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas-2.0.0-alpha.9.tgz_1514369332031_0.9453196839895099", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.10": {"name": "canvas", "version": "2.0.0-alpha.10", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.10", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "22f0101d52d991d44704d6837ccfaf90ebd18989", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.10.tgz", "fileCount": 55, "integrity": "sha512-MVe5lCwryroXf8a6R7JsAYVppnhCNwchvjJFscJ1MVy5bG7k/XEFNxnIZksZGO4bgbY4iY4TAEd+lyr/LJM8Ug==", "signatures": [{"sig": "MEYCIQCwix4ol3W9tpZI+M6cPIt46Ey/6wNwponjEt4947DyAAIhAKToydbzy5EfNfLGBCRo274CzCqd4fXCJqyF1L5u6boh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 296339}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}"}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "ffcb8f8c1923ec58fe41ad4ed97924aef3ee9ef6", "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-pre-gyp install", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"nan": "^2.4.0", "node-pre-gyp": "^0.6.36"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.10_1520416653172_0.3129132530272096", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.11": {"name": "canvas", "version": "2.0.0-alpha.11", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.11", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "116eab649551f98a5c98e0c9b8440ced9dcf93b7", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.11.tgz", "fileCount": 55, "integrity": "sha512-UAvXNi/S1J9vAQUrCZ/y6dc6/CbDj6GGWh9vUOb4GMlOHHhBxEe1PAVW1VUFg+WVLGEvOK+LHBX51pQCm5BNkA==", "signatures": [{"sig": "MEQCIH1qTWw8cbu/wT6prmuNsmvftPGQOjRMa9xxX3r9TOU6AiAYOli5TE5a+WnOP2Simrv6ajxKsNlDQ2ESe0q01SLUmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 296359}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}"}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "871bc70cbcb15539ca4b0714b5cf511ae385e209", "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"nan": "^2.4.0", "node-pre-gyp": "^0.6.36"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.11_1520595893450_0.4276054106213265", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.12": {"name": "canvas", "version": "2.0.0-alpha.12", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.12", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "01ae4efc037b982be561b17f3c9707f95c544a90", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.12.tgz", "fileCount": 58, "integrity": "sha512-ra8lev8agfQmiowJ2xJCVKA3L79RqwNcd2XboiHYWfRKlVk29NLye0ozK8/4iUHt/TIMe+knQoCEI6Dtlwep8w==", "signatures": [{"sig": "MEYCIQCsnkTDFGcV5A+p08RXaWpD1o9VDacB2QR7fhmEhdHEgAIhAIlkCSskNfnjIWEh6sG3rWFFs/HRzq13o/yIrygA5W/V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305606}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}"}, "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "92b192447e9b9ae98da0f801e4e34afdd1dc1ef8", "scripts": {"test": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"nan": "^2.4.0", "node-pre-gyp": "^0.9.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.12_1521747186902_0.07290251176449614", "host": "s3://npm-registry-packages"}}, "1.6.10": {"name": "canvas", "version": "1.6.10", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.10", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "aa938354642e7d61479ae5a76373b3233d7bbac8", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.10.tgz", "fileCount": 39, "integrity": "sha512-wd97ZrUfLYWjQ0qcJGiM44anXB+RviRu3DETpPIS4Sf7JzhP9PawwHdZOlx8sufcsRSeWuQe93qaCtwvK1jWXQ==", "signatures": [{"sig": "MEQCIEGtf7nsxXAQx2OjPv2ikkB0TjA1F+drTndk5bRNh11XAiBF74MSxf8ni2qxD5dim99dA2AovwcKYzUop1bOMIf3Ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241102}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "265d084311b8c7a11b0d89967bd904f2899358d0", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"nan": "^2.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "^2.5.3", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_1.6.10_1521748494627_0.3495151686498059", "host": "s3://npm-registry-packages"}}, "1.6.11": {"name": "canvas", "version": "1.6.11", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.11", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "c2d8bcf283281f19ded14fa163a111804522330d", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.11.tgz", "fileCount": 39, "integrity": "sha512-ElVw5Uk8PReGpzXfDg6PDa+wntnZLGWWfdSHI0Pc8GyXiFbW13drSTzWU6C4E5QylHe+FnLqI7ngMRlp3eGZIQ==", "signatures": [{"sig": "MEUCIGXLvND8f0nKWcnoGc4tchFTCQ3BavLZR+L9hMrSFebKAiEAmxN7qmd9DmASTJZxbk2UaOFitA26WWcMu5ZtkWdMY30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+siTCRA9TVsSAnZWagAAELMQAJGRosVECPYSBnAWUPxQ\nvDfqVb7VbfF/RStiMIvb+YuZN2QcNwcYIGblZlceqlgojXCZBUWuOupyq4B8\nO0GzS8TixDsT4iHEO0pCPEnx/KjUIIP0dHZeXSF5wQD8goBNWut+J1YVq8Qw\npn/S9fwOa2kZ2VA4QUnnX9I9E6yykSbuIdJmxWLE4S2GAAjzAcRbpCbEsDBh\nYA30h6LxMrD8/sB61E7mLC601CmfRm7aWN61rYRRkFgAjbGkSIvretpxX5H5\nPbaPgxqZYzfXYUIB2dfsNQoeWqpbhPYkvTXsKTtcGFC7X//Z2bM+KS67dr0X\n8veRXuJvzvW/8ISJLYV+iQ6Pt/Y5W7fXhw7ca7y7lIV1gUjBJJsH91YgNgGr\nDOlJBuyqQeoDAEk9L3nGKxtIPjFI0wfFwG0CC451ABvIE/pkmfT/0oA43vcz\nRoF3KWSJ7J6cke5zfJqoCqhogL7tkZ4aF1372hWa/epS9UgCG7wmAM00Cpnf\n1R5LDhw4GDVgji7v5HEArEjDLG4PPvZbE4kXi5EnwpkIo8Olhh4+pnDfK78s\nk0gWcJhC5j/CBjvco1Tx216ub4QNryu1y9picDrc6WE8B43KRLJdHS26sklb\nKePhmlpgZ6PwCXURGxbljSxBSgDtffqWNhkLVClbDBGnzcw04vdbVPhaCFDo\nbUj9\r\n=tuuU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "3db3a211e17f60832b181a713e6b2cf6bec20add", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"nan": "^2.10.0"}, "_hasShrinkwrap": false, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "^2.5.3", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_1.6.11_1526384785734_0.060526974195721506", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.13": {"name": "canvas", "version": "2.0.0-alpha.13", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.13", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "c670de1ab6a33efab9ce5f63663672cc2be70c9f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.13.tgz", "fileCount": 59, "integrity": "sha512-G4cZbYFzR+KrTJaj7BF5sOTMDwdyiKDYfhaTHchRpUOpGu2LFez3qP29B/bDmjQGy72Fi86RaQGH7H/ejrx41g==", "signatures": [{"sig": "MEUCID4Myq5vI6A79OJH4cK3OOQfcRJn3WqeSNr1w5dljJ+5AiEA7iLLFc7ZyLDKnvCb4rab43xUcvNQOt1UoBViQvbkxJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYtD2CRA9TVsSAnZWagAApfsQAIg0cjYMAw6++gbmk9zY\nTLp6qXaNbvwmTNlluaJEtOZJoT4DOFVok0Ml/TNx8iRFSphzItK5XqJMubTr\nJTub9r2OMLqFLwiVwVoggo6M+yS0iDb7+fwAiBMjKAgme1+QnBtQKQovaWNh\nttjVwBiS1HHznthu4vfw0xSTxibhqp2FmGy4453dhS7rrO5DaiXw6VBgpvMe\nURPvSgBv2yNjHbPCBM+KLVFSjLgfKyV3ZZCotQpoHaOJtWLXBY+YIfUGds0f\nY7GhMkKHpOD3BXFdi07VQoStau0KG7e4izmwfDr36jYNBnz1c+OMM/I4b3b0\n9sOViU6lKDO1RI9Qvp9s7Gfy/2t5PNRGtTJxtUG0gjPEbOebwjAkLA9JFCxG\nTUanmxyFFfj3pV5g7AQS/ckG3Wyxruxnhi0NhlOHtkY55AJFo9BCSy3YfWX/\nvwQKg+Js14CcOKXur+p+cMEgGcVk75LNe6/b1lFvbE8pKcQSik6k+OBzn5Di\nfXcj2pJKu7drgP26HpZmL2crKDF/rtb6DaJYXoBdsAf4ZNIcxb0Fi7OMqQrw\nGmSzeoV+YYNKPCjt+hx+eilKGzTOkyjR7FXXgpzpYj8EAuemDLrHWKCGtrge\nZzXCI6bWQTNh78AGh40G1AkheqqGTKhxEp3kwRGOiwU6ORBc6lazEFUkHJva\nx8DI\r\n=Dg8E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "986868f5205e3624e2abb9bf7b4c55ccc1fcc56b", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"nan": "^2.9.2", "node-pre-gyp": "^0.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.13_1533202678294_0.8537743752574114", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.14": {"name": "canvas", "version": "2.0.0-alpha.14", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.14", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "021d81d76966ccfc7c72e7b0ac4c1d55779d92ff", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.14.tgz", "fileCount": 60, "integrity": "sha512-f2sKUAH8CJx6zxwzL29rwgeOo7E6ioPL0SPudEb1tequNfzRolcQtDYEFKtipRpfKPN9foOub2GDihH3KL1X6w==", "signatures": [{"sig": "MEUCIHxrnEYhsZUwjzVp81ZDEjCW8Zo6NjZcTSCGhPLA8FA7AiEA5VvW6pX8EJG6Wll5oJUh3Eq8Wv+EthcZszJv03SzS1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbk73hCRA9TVsSAnZWagAA5ZkQAIkIJPVfBvdLb3WrmpPw\na9pY8ZaGgdbPpTwir43+96oxTf7+1bz8iwgm6qc06UIiQfRVAjv1hnynU+S7\n7rujlTW8t4hHCGf3vyrdIyaJbyVqt59CDxVBVK0uhn1Z/Mgbjn4m18KRQm/r\nhw0IGPJAyBVDvSswMMa9KQgHOcubxSspwn9qrX07CJLeiZRZ8dfPgiX07d/R\n3iTeZqCRlvs17IrCeIcF0TMtN08hOkIPLfYm2kkMafVv+25nLzUIdlvWss5B\nhZCFrA/+tERqK6u8jflS0UvKjAwM0du1+HSNINjlWccm6En0hhy1M/Whd7v/\nKQ+EestWChKITJcdipB4f7BznKY6v+Shel7GqSBUJYPegd/VdOZ7sHLwGQdk\nKGhy32toCS33K80/G3jjWbh85pjKEZqfMnsbwp2SZzvrDrKBcg+B/ccEcBFo\nxKoPaynRYTU3fP6v+8fq/Np8o/eXy0MQ5sr0jbb6FX3R0ezn6NvEdWxMpI3S\n6hl1I+rMXp6GYew2nwTDPG3efKhYO2navbSJ+lGKcbpuDBLKNCORqBh4m51a\nu6qUWsAtwGjvi0W9M9OD/MMZ7pWxHhX9OgoyzeLF/tFyX2+n3lq43k7DNidV\nkc5D7HUBu9BG7kD1s+qC6FruBrTBI/4kuzTGUc3o1ejYYnqvpXDBTSmOw3YY\nHuVs\r\n=nnKO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "5a382dd9f713b7b059576ba4d02c33ac96cc6859", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"nan": "^2.9.2", "node-pre-gyp": "^0.9.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.14_1536409057024_0.9996594925848288", "host": "s3://npm-registry-packages"}}, "1.6.12": {"name": "canvas", "version": "1.6.12", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.12", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "9ab02cdb6b543ae6705f566e33f9407625da901e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.12.tgz", "fileCount": 39, "integrity": "sha512-ssgSavCsdemQYCx2ytzl/KZnFthdMm7OhJw2D8DWiyoSrl5WpY4ghJ/zTghs+4Ck08WFaLqwWUKFdjRO9CJyRg==", "signatures": [{"sig": "MEQCIAHQntF8jDFngL1gnBj4S+07WAkTisqdkGtdBqRBvcsbAiByB+NE0pYKZLZOmnk0cS4XBp5qwz98KfeX1mArLTRJbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbk8JTCRA9TVsSAnZWagAAqlQQAIr9i812fTpaXg3OSPpX\nt7Wjoms1ZV7+9ETrYa0OTPFgMqx5eoskzQJknaEbRKpHevFaap512cYyuJBG\nRUuBPD1awl+AAg2VCp1PnlxmUdgO93nKkrOQnLJMq3BTfnNlySS1l7POAM+h\n7sMNsvSYrvFGi54cmvy1lNedjzmD5RApwl4ddtjr2pcBluc1hQRYZHEEBtV7\n5lUzmx4OckSOX+UDuRNd2WJS/B0Q5bRo2gPSHI7nzBxKtZfCWl/6C6gYA75B\nYd7hXNjnPA4rnTgKGzwoSblqqyl3OAHycW8QuPNOWE2H2FMmtWziU9NJzLVz\ngRUuffgpxmL5Pq/Gcum7+gOAPbfLlnrxVeHbwEndfcgd5admxRybKfxRApTQ\ns0qV55y28yJDgSQDYoolhLK/OQ4jDCChSSkoxSedfdc+QtRIpzW/tZ6NEXC4\nxJkoAf6LZP7IU/XSMfIU/MmSQOk0GyDPhkGeFdIwZxry6slPQe8lEl7KkCoj\nPDqdKiUL90gQphhw0/lh39/n5PZ+vFt+s579VInr6mUaG7Aus9NoRm+FPIYa\nZwcFHs+wQGoLaaAUmhWrXfT7M+3dd83ycbM0jj0TgLN2QijIoZumiw5iu1XR\n/VtG24RmjHh7XpVlEd2irSqlq2R6NZAfjdnkB1v8IaA2q5OgSGpEe1zYUGWy\nm1dR\r\n=DfVK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "1b320fe92b9878605f411ccea3baf64385cf4be9", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"nan": "^2.10.0"}, "_hasShrinkwrap": false, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "^2.5.3", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_1.6.12_1536410195102_0.21077501119476305", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.15": {"name": "canvas", "version": "2.0.0-alpha.15", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.15", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "3d8c51bae0469e7191c2333b6a08d270c627a418", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.15.tgz", "fileCount": 60, "integrity": "sha512-CU+Aj1Sc639ZLI21714JrBfREnr31xrAlCAggOzTsB4k8DWOxngKPKYqNuf+0U1/ISXoRqN6lUw1Byv3OB7Nhg==", "signatures": [{"sig": "MEUCIHgksMbS3Z0G86ANCJrfemfwRdTv4DuwbDxgVREtr60UAiEAoUWibp+5mP+RARoXyTN2RrHqQSsoLezX7KM6+B/1h8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbqninCRA9TVsSAnZWagAARWcQAJfxJ9RgRS43AxkCdqnA\n4pnfvXqORJNfJky8VpJaZ99RMhxsPazOYQHx6oLtBglp/iK+q6uxJtnot4dF\nTvezYn5bVzN7LQm+uWcoY+5OVhMI+etVuvQupIOe4KnZlvxk5BRsdGsyJo+5\n+ULelGgqjbqzvB+casdGF6lJ/WG/Hz4GlTGRGg/T4a4CT7nSvY42UErJAoBI\nq+5r5ilTP3TohDDHMTiL/SFdskh1MoIY0c7p/87FS6W1ZEufHV+DsaGRdP6y\ni8wmCLXKWr5RDTi1xT+xnYjaMNzkaXKQcbVWm7x1/bRpu9eOah4fi6teHDFx\naZHwYtZBDl32FOZBQRKx0F0GknOrDhKDzoN11l+3F/2ze+5nfBgpBZ/wysq4\nQQYV5x1CogSn4UyK7OOB8+aKgZqGfWg8O2J8eYeXhsG9aontgtl6tt30tE1J\ndnj/rsnCfnugibSKzyHJTv/Xcc/nS+i6R1leM17Al2qMcRN0yDojlntWmRsg\niECH7kO8jWaruwegavTLChTN7LKf4qZK8mxufYpTLYv6r0pxy6Pf5p6GFty+\ns16Jar1LXcu/ZU9aCp80nbgd4g/icTmz0FWIS3WAwZAZVNlBQTowZLhxylzC\nN3UldCGwbYCNPFAUKdukJNhJ27aN/dGK0T1Efqq6VMnvkmD65exD6gGPhEMG\nOGBA\r\n=FR46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "9da6ed4ff637c24863d2073788bb25b6d4b01590", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"nan": "^2.9.2", "node-pre-gyp": "^0.9.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.15_1537898663158_0.3810127171013713", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.16": {"name": "canvas", "version": "2.0.0-alpha.16", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.16", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "28b3967a977442a0b0add436fde52d022d36526e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.16.tgz", "fileCount": 58, "integrity": "sha512-Ar1PPgr30TknoBmX2SxMbEXYP4Vvoj5nMIdiWb843dha573kinhYTlVSzaWaP1Yug68tX+h7eJTJ8Mb2CYf7lg==", "signatures": [{"sig": "MEUCIQDFYEpoDKforvz4OSvVy0Y1tLBpQnioDToAijgItUsPYwIgIVTuxufhj2tdF3yhRoOrEMd3MqEol9TNmBmlrwKOb3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbqp9LCRA9TVsSAnZWagAAz+8P/1fDcGChirL1yrjrPR+6\nkSS/DWOuNJStnV+rre3hQdJN2p3g46NhB20SfTLTp/t81IKSIHnUR5aIX717\nnEYQiw5wveRyPRgLY/+Eapn4etSGg2WgyK8/FN4fhHerG86e2uYQN7YwlTOR\nuWSrSVLafVzB6hyGVWfaTMej+b6GDOImpfHUPZn+VgeI0qTYIqT85UcB78PJ\nDtXh39+Qdh/bkLEeZgikuefXLsuUS5s3LedXFSQ2U4VVcsuSD4olSoTvblIB\ndYOhJKcxxVjeTJUQ9bSxl26HF9qTJz027yhJFwVLWV0Reor7pwJ/oyI9LbWs\n3zX+A+jhglPRttRMevXAR2CRKCEAmpbBDgZvFOzJwpQnW0gszX9G53O2YFT9\nd3cRc+yoVC0tulCUkSHp0e0wGh34MBaC2MCXkUb5B5Cek/HY/mhQQRXa4Nbm\nUOrXai4qunyjPyB6iqbUOrs5bl9Er9SzjhkcP3En3/OHKhANV9nDYWu+LUtq\nMbmlJEfn75NKpFSH6KNHCn21ag6rFPUQztHUdrAL4lFch7kg+bRbhAMqery+\nOXK9AJ2YLWYLdI/7H2lpHyqpAsagooNLUMH93lOf3GlxoSmmYsX4EfFY+YJO\n7RpAzYmkGijFPGdYoRkHa9fXHP16cPQHIeaTtecms6cHoSSoWOSowO3v9Lty\n6DCa\r\n=uOsH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "661c5d2e95803c7c432499bbf76e79bd7e333e8b", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"nan": "^2.9.2", "node-pre-gyp": "^0.9.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^3.1.2", "express": "^4.14.0", "standard": "^8.5.0", "assert-rejects": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.16_1537908554537_0.48100702244907234", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.17": {"name": "canvas", "version": "2.0.0-alpha.17", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.17", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "046808987c3bd813cac06c7b0ac8d9df78d7b82f", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.17.tgz", "fileCount": 60, "integrity": "sha512-oilR0HJFjl1Q5DVLTrLAxMVMIwJatsAJmflGpKE+JhSZ3a4RJP6MV113LwZDuySQffyU0Jpn6LOnqfacsVGzig==", "signatures": [{"sig": "MEUCIAnZp4oxtFbRPWP8m/EsJoyE6U7J3Fmt9j/GvKRKPe8mAiEA7iEgLVKOIDglfLMG412qfxNobLk2VYjBe3A5e/tu93A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbr0veCRA9TVsSAnZWagAAhBcQAIJvxnZYGNKKqrgRcb79\nl3+2xSBNgLBiP97vFjeqmof1yEqqz0jKX4Kw4sSmHhbAJJhBWWwKrhFJ797C\ne5o/1XTSIO1xcjdfKjHGqhKnnVHKafY8htC2uwQwMakcrMh6WDlu09xInHXg\nrOzEb7YNHwkAiRONCfAai7zpzExPG3GReyUb4uurx2r+RuASv+/qvZRo0uro\nvYstXCNG62fwDQ+xIFmcnvoIh5kTSN9FtwaNu5XJh6BksaMTGiQz1Mt0ieHO\nQ+6gOIHnPjAR49qsC0p/zLbYkNUA4aB4Q2Cb51KT5BO9iIDTVxI6X8YqhWhs\nyjXNV8zgZ6mK6dUjjPErarCCw9b+ayBsq35J1ncmTVvDQakyTYuB2v69vooL\nkAWUpsbs/BSeBIOqo+Cw7pUm6OEmJTBkZGx82gyyf3wGkKqSTMI08VvFIHCL\n1xr2MXLMFeyLlO1eWRKJ0Awni4c2uhpAR3b4OyxVQZqDAc3krHTg/nI3CsBp\n4umeVUyvXNvVINzOjbEmYhEngzPtKcY+ghKnTwtFshDKDyiBQwI2G7w2GLYt\nUt9pKsiwC6In/SXnpGWk8y+JN20nLn3B+vpvdEmmxU7qVeR+WBg8rN7DqUgh\nI/8VkwYpw1vp3RcNfseSs2EQuSd/J3e6zkHetNyF2XOo8pKxlGErT45Ja05C\nmsHN\r\n=YHuJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "5df91bc172bc55e73efbab8954bd05049a78d3ef", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"nan": "^2.11.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.17_1538214877630_0.31014178925566016", "host": "s3://npm-registry-packages"}}, "1.6.13": {"name": "canvas", "version": "1.6.13", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@1.6.13", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "8cb4e9abbea9e615a377890ffac50277a1167c73", "tarball": "https://registry.npmjs.org/canvas/-/canvas-1.6.13.tgz", "fileCount": 39, "integrity": "sha512-XAfzfEOHZ3JIPjEV+WSI6PpISgUta3dgmndWbsajotz+0TQOX/jDpp2kawjRERatOGv9sMMzk5auB3GKEKA6hg==", "signatures": [{"sig": "MEYCIQDk4A6aTrsvDfly5p/30nQ3FtcUHsCPIGgYFoxmJzQVdAIhAPBHK53WZt2vzgHNz5oI1IPbFAfSDBDUBRPBm8BvFQHg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 242296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvZu2CRA9TVsSAnZWagAAzhcQAIO7IhwPAmLZ/qP13GUJ\nQd7ODE+4g+Mtfdg0U00pllPqRded6D0GJdeNW070rTs0ZrGzK3L0IpJyBy0i\nOHpr8W7u+Z8zjFcStFvDPqUhFUgFo5DW5d6kr+JTZkNuzKZq+wBjo/esIiEH\ngqFNDdSqi3NssTE8DTWJqlPWlE4ftJ3rczXFnMOaJ5jDvY45HT6+gGL5kf+Z\npXjeUniGE2xzDDCcw3Irw1F7/BMhiCRhb0Dm59marC8eTm9+k+BCy4I1s3x7\nN4k3FOdyLeeLCguYhJDz/Q4fjPJTGc6N8pfSBf5Pnpo39QRXthxkT6xXLz15\nddpWmv+R33+M98FYrujWy91fCIFocapWPYKQWK6osGLjKLqnc+48ZVzkzBwd\najNDU6cZLYE8e0Vkbj4Mr2ac6kBRAMwLlkNXQBiq93DCnCAGChLMX4vSiO4Z\nVOrAJ5jx2aGWgJm+/9q3XNOy+JrWzwAcIchA4j0pD+rPVzrXLk8tbTzaSX4l\nx7kdoR3NnHrNgjmdAd+hSq8dy321Wwfy0taw/MsUyvrsjZb/Q/Po0UM1/ioq\nwugH9LdrqNuPb8hyHm+NNMJHqEU32tgzZmwn3LEIBjo/k9kCBReyt6JwMvXE\n+EQnVWvNjO1lusBisoC3Xwy6fXMJS1Xe/++Sx/KEuf3bVaGy+aJ0vzrngHdM\nXnQ+\r\n=6hNR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/canvas.js", "engines": {"node": ">=0.8.0"}, "gitHead": "67e7a955f4742850296bb15b620ea4007db4d5b8", "gypfile": true, "scripts": {"test": "standard examples/*.js && mocha test/*.test.js", "install": "node-gyp rebuild", "pretest": "node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"nan": "^2.10.0"}, "_hasShrinkwrap": false, "devDependencies": {"pug": "^2.0.0-beta3", "mocha": "^2.5.3", "express": "^4.13.2", "standard": "^10.0.3", "body-parser": "^1.13.3"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_1.6.13_1539152821308_0.8126774601067515", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.18": {"name": "canvas", "version": "2.0.0-alpha.18", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.0-alpha.18", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7d895c085ec1e2e5f97125a12cd525895519eeb1", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.0-alpha.18.tgz", "fileCount": 60, "integrity": "sha512-fvuVZcj4QIULTVd1sj0/nKeiCURiwLyf0fYfdq9Au/W5OUIX/DZSa9IzgNBVxmuRwdRFZztBnt3PxL+GJfVP7A==", "signatures": [{"sig": "MEYCIQCHocW/HqmWHRQy2y9ct+Jft/Rumr6+swdhY4tAcgK8kgIhAK/474OgjT2ogKFqZ+fsX4UCRXa+hzZLfSbdn8xY0JdI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvZvhCRA9TVsSAnZWagAAH+QP/0Wh1Pi/l9Sy7Mz+dCoB\n0yWeoOIB/Qfm5A4uSu1PB6GSzVaLmbPpGOyGlvdeGsdq0H8x8mzarPr9dTpq\nUgvVE3G+jJOpQ5LwwaH1SsudflxobQImQKFuqUmIr/zzrHcU+318EUaaTd8g\nzD3DyXPU8sKo21esMaH/ZR/q+NFicd8CLt3aPN6jrJZuijvl+noAz2bFh2UE\nOGTczG9+R5T9a94DsHwjPbFvLSblyd9loLm2pu7NTxQ4FkgyI71x1iu8RDsQ\ngSGminFOv6OomxdJ8bVFXn6G1CBde4vYy8et5SnyzyjArbZxu1a/T4EUDCU4\nolZoNgXOa0G1Ak4TZj155Z5P4iXNhsF0A0L3yPkfXNBn80FMRk01Naf+q/cz\nLSWDKVGbcfUvfMHtW93BEca0DOjn9liMFoLEgpNnmJRQ9cLE8SETaIgMMUuM\nO9N4NymVzKnfPiYySQckkZ3Gf2p8H7xbhrGDiAj5z2Y+BhwGozVRdm6qmKCH\nklhxmYfUufVlV63swnVLmS4Ln8sw4iQbyHGFHH3dDoCdfpivsXqQge3oMP5A\n1np+aNzBu0n5XOIFHMKb6wMlnQsSJpyLGvt9IDx8IdDJRRX/61cUCrMeQnka\nQwXxFGQE8nlW4IzB5vpa76z+6p3vE3SgtotBQWVaPD/SOa+JcZ2pXNwx5JEt\nQrR4\r\n=pLR/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "586b395afb4a7bd5515b28d255118debceb2d139", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"nan": "^2.11.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.0-alpha.18_1539152864519_0.2284398972403756", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "canvas", "version": "2.0.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.0.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "267649ac4c9876de992fb2361252304b599b3e93", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.0.1.tgz", "fileCount": 60, "integrity": "sha512-aVESjDBMXGRL+aZqjFtxMVOg8KzHhNcKIscoeC8OROccmApKOriHsnySxq228Kc+3tzB9Qc6tzD4ukp9Zjwz1Q==", "signatures": [{"sig": "MEQCIGFZVwBmRrDtvjYJAqof3mxLzwws/+sh8LDhtQOQQZWYAiBApTOnbUIuklHlOVOlGIlgmvzG6Q4pgv2QvenOUd+IOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxYinCRA9TVsSAnZWagAAjcAP/RfoqvUMB7ljQmP8Ag/W\nZKeUel4fznmrjnZKW17sdubsKfrbKWLS/qDXhCxwwgYjxuJJ8DI8Dt71ipUt\nCanjvFB4iB0GVq1RrSI6/EOnNwvvRhNBrzAaCFICG1b2TKnwdIBNDoSRE6Zu\nLO0NpKLQsfdXmotcpoi3owQYMIRSuwFENum232/WjhTXQonz1LT9v8SqBSt9\nLSKisuknaW1sPwMXQ2JfkAwT00H3jbuWmom+zzvet6Qzi2QxE2erwvxUdlRo\n/+OGdctzLJ+wlW9Z34TGvco6WiSwWnRJNayk18BMiOq/a4ujwkHm0HNp0TiD\nSR6F5AS0trmZfgGFEzbnQpX5sCFSgCOmRjY2AyYHqARUM09Kj4o+vHbkMU/p\n19uvyb7Eqfyvs+VA3awfFDfZ6xkxrc0wZF79vJdDakopgzQUbYRo+HkCT9k8\ndA2XQ+XhZvVxwTM3fT3dE4s81D6L7sYxutxocKFesnkf5K1LNVUB/BkQH98x\n4lhzcancDdPDsDNU7e9nh44cx4Nk8tEujAiIBSNE64n9crfg8+eR3AO+52e3\nEBLqnX6Tn0sCmT+zAkBslrzoQyyHMRa0DH3OP8Tt2F5UmlEJt8JyA3YXymxm\nNStRwV5DvHkO1XXbVjk3VjnJ1EGBHgpYoZ3vtsBfDvDuBHXSsK1lFetj02Q3\nMGFC\r\n=Rq2u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "fd1713578fe5e1163ed455a1c432ed24f90bdd84", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"nan": "^2.11.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.0.1_1539672230292_0.5121467789405629", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "canvas", "version": "2.1.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.1.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "9abbfee6051b16666332ad0a8a2fd142e29039a1", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.1.0.tgz", "fileCount": 60, "integrity": "sha512-cpQtc8fjVl4Puus/t/X1Zc0r+tVWJcsRAQvZ7pu1mSAqZgkbgoCv1CEnLTCPD0YA358G0G20kvzZMQDUpcvmFg==", "signatures": [{"sig": "MEUCIGpj9Dsubwnp2YfoIX7dRZWoZlpIU8fX7g0I+nPgNaLgAiEAx5EmQaTXdY0GobmXYlMVUFYeN0dkbYFXgwDi1eMLqgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 328397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4y7vCRA9TVsSAnZWagAAKZEP/RBSmM1Irxe37i1f5dON\neA+07rsnh/rHHBr8RU5LSGkplxm3Vyj4paRayi+TjMDXlB0664cdlY+5JD9Y\n0GQ4jm36rprhOkVL8yv1eTXSN59ajK3fnVgQAJWnWF5rkdHCubwe8v6FsnI9\n2P4fT1FysYxroFRoK/u7xWkJLORXLrG1XgSrGMNyIXfDBfQxa782p2Ym12pL\nuegDIZQ5bTc8/HUf5weJNHCkGj6TQLfaYK/Z+Ui5RF2gHYdMaxSe3YfJgUPt\nEgWyz+EMomd0rNKjHTYcUM3E6Anc+PEtMXgAgwt8hguflRUpNRLOwzbCswaU\nIh3D/N29n81+5OHGiolMCMwU3FzjZ/FFXmnaVfrol5rVJaAX3RW5I/jrcAws\nkuk5NpsHliIB1nHIxXNvqhI4qIruLxb+i9/dTbQ0OQNhoGsF13+wOWQlNgl4\ns79s5nYSbAwvvij0Y7xfbbmIrvFuc/hxJJbV+YjuBS/fwxI9g5Zz703tj89G\nkO3LwRZOWSzwJffrYBzmUEDorTYhMwrUrqRTw7ojvyKMNKUNf9o1RVv5bwwV\nS7eW+JXg3Vc8ot1ZLEYmozZ4yW/6x2HdJCElz+ljnQKakYBpHuYSi4FA3Mhw\nWWKwZIus6HdJd+1eYQsPfgZvHso8tOr9U5AHgt25Z9cpNOaW2D5ucp0BBMdv\necnR\r\n=3mpA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "a5921f6185049cdab9f2ee92c4a2700d00e9fcd2", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmark/run.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"nan": "^2.11.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.1.0_1541615342202_0.8144484671318719", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "canvas", "version": "2.2.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.2.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d86ed3a61527de855a8f0d3d7b926f72267c956a", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.2.0.tgz", "fileCount": 63, "integrity": "sha512-4blMi2I2DuHh9UZNrmvU0hyY4dZJFOjNuqaZpI/66pKCyX1HPstvK+f2fIdc+NaF8b6wiuhvwXEFNkm7jIKYSA==", "signatures": [{"sig": "MEYCIQD5/zcz3D18laBOdEEa+z4Hxo/v1YWfWrDjxx2X4CyUwwIhALK11CJCyZpeFJwS9xOEtBkSqkAtcUuSTH8WkzzWYpxm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBUSwCRA9TVsSAnZWagAAO1UP/0Fg5HOaQGYZvCh8ten4\nY1ZJCsu8Hu6YiNuNzEQRfzb26aVa3nz74t8D07x2Lojcd8U2ZyFeIx/jleD6\nGjOfgDpWIWNUEGDU1nogl4GcrD0AgrQg2kTrHlEO1oualC4uAUpp2Q1NbD62\nzJlxTVzvwtzpUqX/RNgxM8Wx/AyFC7pdaRgfh2kjEVdqcEtFfKfYul8GK3Lq\nTQSi1R/jNn4Cf/ayUARMtBDTZ3psg5x/ah7iex856KYXrYghCppxF+bNQz7G\nxeBfEqGIa9VorVzFi5sFoFZmXaorSi4fg+WiZztLfjn3+kCRxhtQYFunM7gc\ngNJvXXJzKuNVeMOBySvzBCQianDafnxcvSKrhEawQDq5sOtZ9ZirOrc/fKPq\ngccGbRYYp36iWxSxV78cQS22UXk3ULdLFpQfGwt6fLDUBrLfdvwH2LW7hJtM\ngOXKcwZbbZxevI1eScA3LesLDhOJvJDIIkQeaHee+tYjCYuxv2lhcAX8LClj\n3WkPz0tNZWh4XRbjdQ6S7ktBU2WBUaFrp1NbQmFamL+9zjIb0Wub5HrtCKGu\nKIvs+YP9d08eiVGfvN2arHJxM1GIdq4Ub7867HZ4HWbOxRjS9RLQVywz1ed3\nUIWdZ239pNnhnRfCUuQEp7ssaq+akOhu98EysxwhMuedcWphEJSCRlCA5Na6\ni/Kv\r\n=d11/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas-prebuilt", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "c9ce7b670f8d1cb8da6ef5bf0e2ff489b1cf0d2b", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"nan": "^2.11.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.2.0_1543849135251_0.8798800997822123", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "canvas", "version": "2.3.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.3.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "ac910bee1e2d4d25ecbd2d3186d2cf64a61f6b95", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.3.0.tgz", "fileCount": 61, "integrity": "sha512-GI6TdbXQiGQ1IGuN0e5Yuwt7IWuZSeUPcms9HokJLNNsIkUxPnPHgElduYllfnFQShD+YmL2997s/P/0SI6EMg==", "signatures": [{"sig": "MEUCIQDOhCc1HRsV4n1CynL9q+bRNgFUFs/N/+KIM2VtStv2agIgBWNev3gjvJuStpCkbXMTxe704qUJbqJi0QnN1znydIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOL/tCRA9TVsSAnZWagAAjtwP+wQmWpzs0+Ummkxpfh9F\nQCZfABIBihytQ3m3AyMIr2qxdwXDD17NXsOh93IEMOSUXSirJN8bItirPzeE\nQeA52L5vO3zFexu4PE9+JgKo5i/6a+tEJd0j2CsY993C64ZFccEsdpca/si5\noE22nROSWpzVHdzqj7j51M0kTNi3FNEJeBqRmzy6vsaZocaPnyM5Q90ZyA0T\nN0AefLF5DgvL5+8iBmqAxWgCxx0J6rzDJrK/EajvxqskA1K8Fm67uRd/tGNX\n+bQcmHGcw9pIKiqIjpjpSJCEqFawY5USTrqUDX7nsk+Oh9fTZXfhgZTUSUij\ns1XqUXWoxQAHF3B1xfX5rFOxWps/HJcZkgCy0qKCoBsjsUywn2yH/RMhxddO\nFSlNdRptXLLEcePYRRpaW8GWOM2uXGKAthrqtSjI4XynHBZQOktqhkjtdmtZ\niVHsWyHgJ+XvtEj/3KjqbYHTory2CSQC+3GzBUInih3OnTRNQGvfPKepIn5y\nufnM903miK9tG+hzjb1Q4j+s1MSEkTsWGH7A3F6qVDr0pQWnVByxDX3tAQsl\nJmrvNf8sf8L53cNChZLlROunkio2PmjJWZx5b8LwSrlVUbc8XeBAzWTpAIFP\n2AAi4S/SFqiMEEDge70A2N1/nxplwHWKoUgiMPZzEDHtyPsuSaBuSjjNeQbz\nakO0\r\n=JzJ6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "b4b8151e4f17927a9a0f181ae4de02acc470672a", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"nan": "^2.12.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.3.0_1547223020744_0.7819136783907534", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "canvas", "version": "2.3.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.3.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "da0c8a916505aa34f9365d6b77d28b969241bfd0", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.3.1.tgz", "fileCount": 61, "integrity": "sha512-jSxwf4V9AGD6t6yBC600xFZKjrfKTR0T0RUNlX/AODs/ifrfJHIQjFEK8iF2euNy6z7K3GNv82DJgTjYZZktqA==", "signatures": [{"sig": "MEQCIG9OuiH+2YfhrWmJkOPadargUVgXI8+AP0YvG2fqiAKRAiBtkeQsHUIgP4GOcysyfIpkeW1Zv58wYY6YKfjQ2D104g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPNOcCRA9TVsSAnZWagAAszwP/0EuZxjz+WZM2XP9aVg9\neV/bKM809WU9tl3EqfGhUc/K5AvpDzchUDTdYCLivw1RjrTBSsuTvQ90fXdM\n6PH8Vj8IjHoEbu+skLGDKvqs0WfZQeYDYUSAaftNpfhAzmODlG0wuX/fgzpK\nsZP8eycB1kxsQQXyCtxu3UpUPIcSgOEp+qnRaUQ8SIOI3VLAPxTFm9ZvuI3s\nOa/hsQf6bk0271WbZj9r8/F+7N+gvy/2EpzDZWCyRqDLEluoPHbRxlamECRM\n2YR5GFY4hSvePeudQUG8ExulAWs3U4+S5C3iZPdV4AQ+k3AQ0AwDKaLDpcLm\nMvzg1xAhh9YB57RG+tx5HiE6V27VLKWYwr+wd1Hu7fpazySIFgWVYKlP/KuB\nxWTafHkX2y+hesCJ4/RRmA7nWfXKgDH2OSU4Lj161QpRMsFPd6MMsC7pgCZy\npjojcynzDAuNtoY1ySEyLOULbikKM6xzlalvw5/iwQ9H1KimqZVCROaT8bB4\nq7/PmmH9e3LWUNOGtfZsmfQlbKOUKu7OKtF49O8UyT0MqF5EvTxXDLq53fsO\nUkgDn+2USXeX8+HhH5moYEtNKJcnsvSUtA+HwPUcqd3mDzPXkqa2LNH5DTay\nj8UgVpoJzM2EEE7zCq0/DbOu79lzq0uPJ92w4b0LHeSj5DA8/DjgcRlK4r2J\nzsyI\r\n=o/No\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "1936282e6fc63f71a8d01401f5e48dd8d1400c75", "scripts": {"test": "mocha test/*.test.js", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"nan": "^2.12.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.3.1_1547490203692_0.23659121393301286", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "canvas", "version": "2.4.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.4.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "987c090c56b6d122e666ab9f471dcab62ae9bff3", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.4.0.tgz", "fileCount": 57, "integrity": "sha512-+xxeaKwQJyXHBdMhVhqFoGqNAAUW5vr2DbhQEusER2RR4H2m+Zn2QLKiwp43BabNhQBITQ3bp54+DYrGrI+wmA==", "signatures": [{"sig": "MEUCIAC5aYsk156k1ZXVfKU28nJEaoU06stKcylzG8O4XnVZAiEA7k05AKmNbMzn/H+0OLDb9UoFZ1D3SgACAH34uqCVFn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcij+jCRA9TVsSAnZWagAA/IwP/10hLoxEyhtNngYV3nGI\nN+iEnTdIaKScYY1Y9D5qGLdSNvwsDEBAPD675salu6ywKFwpYS86hyDRdhsB\nEenzktFivO5VSldRwmyeg6cOM9rWCpLDPntPLhEOWYWU6W3g3AinvN9Kidvd\n+cJeH6EEiCCHaBkQzwyFVcFwD9nkZ9VSQf/BE8how4tDi0cyv4ILQkvxEccS\nn9XPd0YLr7vPIoJMkJQJJhG91vKCyX7x7gcyATkz7VcW3NrnSnRRDw2zOq8P\nZYWnOh+ECQeIcdI36VP18zxaxM9fvrENMkIy70oJ1fxx3176tMSCsbPppstu\nmSR+NgNk5vpkh2z5obtY2H5mzO9yeh4bF031LriNv3xuOlcihLlt7xKmEH4H\n/nWZ0QaQV3cv5fZI94Xc+FwCX0EfV7x2TwXRNbhOylaYqL1IiO/Fuo2a7Q08\n1x+ACT70ZXF+O7FF3NWa7ifNKBdQcQra4/bo5L+ggwoQsch+bCT6NbMA8wkz\nILvFfKCWp/DQ08YJ4gn8AfBoSZ6S93oQNfkbcjnuczd4H6FoaqYclL6vdeuQ\nWtveAgaz18/bbmA/ofn37zYoePmgEYyuBO022MIHIySQr85dyHDCVD22EF/2\nLu/uzBBlYaa7nAQzF/vTfFkXuDR0Ej9oxQAeJOlKgrxLAvIDpZjCCbObXsZK\n6Coa\r\n=C3oB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "bbf8b758b5113e6ec9a3788aa617630a6c594875", "scripts": {"test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "11.11.0", "dependencies": {"nan": "^2.12.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^5.2.0", "dtslint": "^0.5.3", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.4.0_1552564130442_0.16921566226670293", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "canvas", "version": "2.4.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.4.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d3b40efc7c281006ca0ff9cc854aaa8b82abec7a", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.4.1.tgz", "fileCount": 57, "integrity": "sha512-SaFomFqDuuuSTScTHQ7nXc5ea71Ieb8ctvwXjR7vzLsBMfp3euTv2xsTY70zIoC5r4sSQZYXv6tiHiORJ4y1vg==", "signatures": [{"sig": "MEUCIQDQQArDqTVxE4a0E8OvOC66A9sd0op3lGBNNuzuHijY2wIgYxlQF45YpACoEXJ9JopQ4QG29JQkyUPc5syXR3TuPnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckSwrCRA9TVsSAnZWagAA1fwP/iy/1LBsCrbK+pUGtoRw\nb9PlkduWIzW4hq0jKFi7s8M/cTYnkFrmcQvniNHi2+08iYeeZXKeIvj8985z\nahXz8sdzTcOU4ozubAPs2OIqULT19TX68TdtR0BiOiJWGX2MwxC7uRBaWVeU\nVupcE8A6FYKthiVqRbfNB9FrPsl/PVBVk4AJN2S3rCy4VsamWxFE7c5CyPl9\nKerG4iFYv3dL4dVbJKggUdkSK+eEHupAG3wGN2sKueAhlcEuW/SWTdpoNbbR\nmJUehXSTuZ2zpOhWQjjjmSiuvTamvmspgPeEMkBaYXXZCF+SMTW497rANf9w\neshQby+302NqleW+LwbgO6wGoavg4VcVu3WGLxhvTrGgu5BA45xvO0Cx3QZK\nIM0v5nW3X8HtqdiH/X2johJafijZSPhRALtbeToYyGTMnYfC5ZNG/8Y31ee2\nRsZtPJhruK2bWSFyrMbI434wXxi7ZslCdDFtBylsHmyX0/+WxQNmHQdCg5jX\nsEb7H7eA/tLKJNX34CbpppDBJtqldGWkR7DXRHoLkg5oPjZZocA566d8KaRu\nhfv3MXZfCH3pZr6xRxx6o2a1GkW4/7mv2M6F1YES7Z2g3hgMnrDbpcToYC3N\nS10+bASyAWCWBE/kYnLLrIxW89691ilW8ApSGYzBgFpVhFjddrpO4yo35bAj\nR3iN\r\n=pEjx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "fea4c0d0a7a9a9b5d6426a3142f659cdbbcab9a7", "scripts": {"test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "11.11.0", "dependencies": {"nan": "^2.12.1", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^0.5.3", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.4.1_1553017898305_0.3183693108237482", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "canvas", "version": "2.5.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.5.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "cf0ef59d4790575689c0d50e59c7b8023a11f38a", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.5.0.tgz", "fileCount": 57, "integrity": "sha512-wwRz2cLMgb9d+rnotOJCoc04Bzj3aJMpWc6JxAD6lP7bYz0ldcn0sKddoZ0vhD5T8HBxrK+XmRDJb68/2VqARw==", "signatures": [{"sig": "MEQCIFQjpY/PdMWCzCRjP1WqxItBHwEPedCIHILV4fAoLt7gAiAJzhNC8PSmT9vAGJcQHtmlW9ie6o0gOjpmBQAZguvziA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyZlTCRA9TVsSAnZWagAA488QAJ6zdrTCcouzqjmYyq6K\n4W+NGS8h9xNqchMXbTd5OSi2fhO0xsm3W6GLNkIHE4W4bShxlpBKjxxjZkf1\njOu4Q/wWJTFSgjIDEQOf3J1PTsBlTRitevWOmnjlZRHirHy0RZZZoFuMR7sg\nm5EqFMtYjG1D6068d0J0876RoQrqd5J9A16OrJmQY+FN2MyrGuW3Hxn+Y8yt\n2XNj+trIzjGC3dFqLJLIL77YU0S92yp/ZfwTC0vy5wsA7JLr0wfc7bZpNNIA\nyjzYdEcvILloIix78Ucs6rblYFOnb6xNmRzFLFgEmuCqklQ6mNIcSd6vElIJ\nPhIO2Ykg21UPP1hBtwhI2YjTU39cK7YKQcl7ryf8r3fffJ3xZCbhWr3Q+xGd\ndF1wSqP94eB1wqOKDE2EfXIaYNbHhcbf567Cc0y5EiFVcoKu/L/aiblaxrPI\nPZWxpXjrvbPXhldRq2Xw+rtUTOA3pKZHJPTVbpevWYwxUwilsjAyQeOkncb7\niABAp+VNRwfimgnx+fTMeLeiHXdU+ctnWGgCBRcaeO21sz2VS5vMztPCByQf\nKIgvuyq19G7IB60NJYrJ+El66kjp3h4Q324Y3aE+YocTLMxY+q3VBH2q3S59\ny/0wB/6x8IB+lGUtfTvuIjkzC2XugZVKYmGoPUofFcqu6mT17U9EjAaONJRL\n6U2z\r\n=3YsY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "7b36efa7563bf96ae057bd0ffbf6a607632fbdd9", "scripts": {"test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"nan": "^2.13.2", "simple-get": "^3.0.3", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^0.5.3", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.5.0_1556715858936_0.6677444327572868", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "canvas", "version": "2.6.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.6.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7a8f87b6148845d97e6ee30947fba1508bed4941", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.6.0.tgz", "fileCount": 57, "integrity": "sha512-bEO9f1ThmbknLPxCa8Es7obPlN9W3stB1bo7njlhOFKIdUTldeTqXCh9YclCPAi2pSQs84XA0jq/QEZXSzgyMw==", "signatures": [{"sig": "MEQCIDrsYCJd7hKaYGnahdZYGUykbbYbDHDH97RtefL/2FBHAiBrXcgUcqcwBRJju/3UJNegjXc9uB19RWs+7J634vVHVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/7UKCRA9TVsSAnZWagAAaVQP/ip48p/6szyaFFhUPEl7\nLOefgaHRkk1egPMY4qMua86VQeJc29AIkAnDycJ+kfCI4860L0BRln8iD4fh\nfkftZoYQZN9ShRZbhStNkYbsz89e/uLE9YTEA+HmEw1YBBTASAd72EMfNGGW\nTSuYJB50QxPIb9iIFrTYKQ3Z54PEAqvZwHI4d7sRAIJgv0XRskN0SXwyZGvY\nC8P8UNW3+aseRGF/f1vXR5LjnxUtM0B3K20AeKn1zy9CkC2hf0LhpNhw9q4F\n90lxVMRid7CNPLKA9s5UmDrvzH8xOTgRU8+MFC0/bVA4Pc/mm4ZpHJgX9YF5\nSr22EjidzaFuby+95UjXfP7vSYEP+OczUXgMFbWxsRekMFsgfcfg2O7t3LC3\nJ+iq1lY/YOIEGaclwcOgsk7NKdc9cM0SFZkUt5xdDLKh/7l95zJ5mtDGV8Ze\nTodTNQHuCFOBCIj/3iKl/eepXp6FoGfH8UApjBiUkg5HEwJdAOE+6Yycx/wj\nNwOhLXNyY+WQFigY7DONBafclQzFyahX9XQar71lK00bQqSrLVCQkKj8p/BI\nW35YsJAmrl85par84rSEHEtXLRGyiPwiSxovw2BYE8vl9BmiqwWeTw/ixRj4\nJpi1dFvoCc0iXEKTlDD019Ri4CifGnowiA+esf8Fki037ioCX1I7Ir43A+WI\nhB6U\r\n=qFLV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "f5b981483022a1311f4b3850530943b3e0e8aa7f", "scripts": {"test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"nan": "^2.14.0", "simple-get": "^3.0.3", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^0.5.3", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.6.0_1560261897189_0.43565712980576765", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "canvas", "version": "2.6.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.6.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "kangax", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "0d087dd4d60f5a5a9efa202757270abea8bef89e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.6.1.tgz", "fileCount": 57, "integrity": "sha512-S98rKsPcuhfTcYbtF53UIJhcbgIAK533d1kJKMwsMwAIFgfd58MOyxRud3kktlzWiEkFliaJtvyZCBtud/XVEA==", "signatures": [{"sig": "MEUCIQDOAglOB8Yn553XB2jeu7e7fuTYaUHIclZ3jd9mP4XwiwIgIUtomgZprMdpIva5Pe5zE8ughmafBpaoLvgqtvghgDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmiOXCRA9TVsSAnZWagAATL8P/R5B+iXO1WOYxLADUn8y\nBP/BS9xjvn1Iu0m6FO0wD4lFHOvetXHGmiskhbC4//kY7ApaQXX0Pyi6n1YS\nmaKDwWk6aEMV6Fpt7xANivNGx1bApujM9ux8uRnZtacvEtIzV1XLaHWw80SY\nKWH86J8WMWzXPgMFG1ponBm8cZjsRfLiYW/bt4niADWCSlERhaaRxA9DB7il\nrq+tnXtHU15e1OOFCoGFO8kvlcImRmylgcTynvJ8vXDTHT8B2R8xTK3MWMIF\nIqBhHUOHM1ZtJt5WNZNzxgCw34m1nuaT8JD0I8DuPWFE17tvgvOqoV1CR63b\nw0/EzJgsHlJ2WDAx+E7bo2bbZpTGOzHIHHFqg4iBXzUcpW/DjpgJcKesKjcO\n5PnvCx3s7Gxb7vfPzDY5Nwb8FyIDnyn7bjUgYgVBN/z0x72BPrJeWGb9mZiA\n6E4j2APgmBhkTFbYbLYmgVM6bNGVYRDxOnCBt/3Z8zB3Tt2u0zXPbEyQdtNx\nQX969WoQBXMKe88TffgqCKRF2kBxuwJITtabXRZWHGWZrkcw7SX7TULk2I+R\nH6l9hICAtb2CK67pT0nuWF8RJ6Gs1ZKGbQ+aabWa8ujeZW4HEy2qfecibxRL\nWWCTu0PHKBHGrx21IsExMdhe/VQIcGYE65aJtb25Er3eNRhsU4/Pr93QsCI2\nnO9+\r\n=1GcF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/node-gfx/node-canvas-prebuilt/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "bb297d609538e346b6eb4cde03dca95a94ddd3bd", "scripts": {"test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "pretest": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js && node-gyp build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"nan": "^2.14.0", "simple-get": "^3.0.3", "node-pre-gyp": "^0.11.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^5.2.0", "dtslint": "^0.5.3", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.6.1_1570382742176_0.2899852296925791", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "canvas", "version": "2.7.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.7.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "3ce3fe30c69595ccd2bd1232967e681c026be61e", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.7.0.tgz", "fileCount": 56, "integrity": "sha512-pzCxtkHb+5su5MQjTtepMDlIOtaXo277x0C0u3nMOxtkhTyQ+h2yNKhlROAaDllWgRyePAUitC08sXw26Eb6aw==", "signatures": [{"sig": "MEUCIQC1zxra4wmgxI5qIRhT3a78s2CBnuG2INqObTWwLT8/YwIgfRgp5b8cAeiH0wYB6DurgQ4f6mo87z4rLcGtXwuvpq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPLT4CRA9TVsSAnZWagAAyGIP/jafvjPIVmwBCbNmY0XZ\nLskmGb4sXbwJXsETw2Ikoa/nWsBMUwrrPKKI/kK9rfF14kraxdQLwF//C6s7\nlmWwVKwcyL2ecKz1uDmt+Nau35QfD01MREFdZ9eFtSi3GXVBr9tyZTn7/9Zy\nR4EaOHHp+K+E/9UvHdJN021+OfSnns+EAoNvvON8yf2GzVkUAs4I47u02xnL\na/79M7If87qW+ubs+0p9aZcrdRlD0Fe0gkIj2DrcIWuHpMNlzBt4VkxtnBuT\n3mHhj7VUWybqV78NCO+/OcfIJu3QjjX4pLgEfdaFDyWa9RjwwO13l0ralsgi\nTmZM5RQhVzF/PX7gZq3DEwcatGO798+JaI29ZCLhV3sgq2BqjiLFfAOqaHGM\nYPK0GO+6+sB57eAnVnRyuXiL5RS2gdRDSvQ9NMWHNSPr8XbnZbsG2858FJnx\noLhg/BGtqr4GSl5azYA4l6xyc7X8Nrt4pso9ClFHk+jUPHwF54zmTeq7Xnrg\nBvpK5tB7Ryrd/mVuJAZJkl6nC3nyWocsPzI4w41Hnnfm8wTnM4CevDbTkiFe\nAUF3FVOui0tjt6Y3raheS93UR8f85luFLxgoVqGBtb6eXEf7+Jt/csennQhG\nFmiJN6plzOLIdEp5TWjEleZ1hd2xIbmSyRIi+73UbpQIVfqtvKCtlRouOqCs\nTX7b\r\n=CVZx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "58bc728df354c4d9a0e8a25d5b04710bfb8fb7e9", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "7.4.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "15.6.0", "dependencies": {"nan": "^2.14.0", "simple-get": "^3.0.3", "node-pre-gyp": "^0.15.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^0.5.3", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.7.0_1614591224186_0.8549517275221519", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "canvas", "version": "2.8.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.8.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "f99ca7f25e6e26686661ffa4fec1239bbef74461", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.8.0.tgz", "fileCount": 56, "integrity": "sha512-gLTi17X8WY9Cf5GZ2Yns8T5lfBOcGgFehDFb+JQwDqdOoBOcECS9ZWMEAqMSVcMYwXD659J8NyzjRY/2aE+C2Q==", "signatures": [{"sig": "MEQCIEo7DMv+CWHH/TbkpR5A1ZuTfqPUP1vpB5d/rm+lf5X1AiB8mXaE5QHMQaeSQD/2OZ36YW6KB9nFux0wRsAHQ5gVfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgogz1CRA9TVsSAnZWagAAF+gP/iUgqopwCfwEm5ym9AlE\nil86F75+82MrWyFNqD0kRKkCJIxnzfmo2jK7t9TmowRi9MAfCiDPVAXZT0EN\nHqAwqNpd99hzrXdj5kvjI/lDHVv9fu1Y5C00Q3ouyMoFDRnWrZnZZDCJvTif\nzoVU7ZOQmxcIoKHLXT8nha4e7yyLWgQ3q6WUASaqEiCuHGCCUCOckXPERG9M\nmG6KuFG0esqQ/peMuJKffKVcNZA+mpcCQp1T76KpeyTzDQ3w+3S7857uHUqW\n2GXt5eQGN5pSy7i+ArPbGnUlO8GKGib+ZeU4+uizZW+81pGAS+O/DnOE5OVy\naxhFjNsRsVrzeMs0wkaAQJ951H3Km8mRy6i9jdnYGwWVzd9U5ZRRw+T+BV4w\n09ljm1r+5kOXFtC74ojWPzNVgO7mKFXbm45NNn+jpBVPdpIe1+F7vEBi9oxn\n+P+aaTGSBKtG2l1o6QsB7djt6VG51h+3K1URkyG7oQyOtmARsqm4JWVJ2hV/\nB5rRzmO6YRSID26+6xU6q9SgkdcKQS+Mf/VCrnaOaVvBV3AXzQHDNLCva9LT\ne+Ij6e9Qgaxd5j4vR5a6ILvko4sfb6xR83xJOXWX3QYwvVdh4ZAlN2UahPSW\ntS8BeQrx2H3fyb2vXkZNhO6XCg1PFsWEz7KSGIrZHkKespkgkZLQ/WMlxkzv\nTrk9\r\n=YIff\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "8707f3d693366a50e60b028404be606042025c51", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"nan": "^2.14.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.8.0_1621232884955_0.8261527224595391", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "canvas", "version": "2.9.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.9.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7df0400b141a7e42e597824f377935ba96880f2a", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.9.0.tgz", "fileCount": 56, "integrity": "sha512-0l93g7uxp7rMyr7H+XRQ28A3ud0dKIUTIEkUe1Dxh4rjUYN7B93+SjC3r1PDKA18xcQN87OFGgUnyw7LSgNLSQ==", "signatures": [{"sig": "MEYCIQD8tzU8f4nnoDWLb9u0tYOah5FKR6NA/aVY0CvKGObeTgIhAMnyAiy5W4yu+FNHuGWtr184E+xRJS/6JoTr3/rFDIA+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 344122, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5fpkCRA9TVsSAnZWagAAAHMP/06BJx5/C7P0oUGDIBNJ\nA00R/tTv2hSnc1D9mIuAWhc/nBEbOkg/J+Y8kGsfKQbyiFmwP9LeYkWqT9A3\nPy5VqltgOGgQNP8LnierG346oQ7Tg8zUZ+qNbXYODzcdZVKf5VnvXVT/35tG\nwcPHpIF1CzBPoObn8d+mXRx1Q9S0uXw3F0lLq2NO/NhfC5jPpd+GXkUWO2ZJ\nwseFJ9gGGqHzYSvjdKIpIuQelGSDAz1oH1n0Amazd4P6Jbfw8zR4zmZTjawe\nDB9cElAJUOlT5vDqORN/6FdxA/gamaCx/S66FYeep7i438IEARGlLlYI/vUt\nLrR5N4Ule5fCFcT156GA1GF7sMGszrb+JeomZttx3y2ZR1mQVAJ0/5OoHlAB\nARlw9ZmdRzfopSFxL1DsWlyfGS/Rpb/GzIrJscj1mom+n7qrp08kOnRo7n7H\nC/qXKDya5iuP9vFv1Z9yCfXQJWcmAx4vB/51zKCpWwGSeJ9NdahE2Qx3iwsl\neQWU7tdi84oKghqh1TKPn34aOrW3EY//iK4y8j5m+TE2E9pewwqUK27oxumU\nkQUr1R95NWQu1XG1trav4oOKzCku8aWQfqc6iDXz2unS7SZH5cs+ML6We185\nV3c93E7MjJgkk1QLPVwgBc+zlhwvXzn2ejvlmiBSY9wvKn6t5/uLljnLKRu+\n82dh\r\n=NqVb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "d603479a07da8e7a5d27ed1ba7a155988d7c3282", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"nan": "^2.15.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.9.0_1642461796115_0.21844969997522523", "host": "s3://npm-registry-packages"}}, "2.9.1": {"name": "canvas", "version": "2.9.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.9.1", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "58ec841cba36cef0675bc7a74ebd1561f0b476b0", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.9.1.tgz", "fileCount": 56, "integrity": "sha512-vSQti1uG/2gjv3x6QLOZw7TctfufaerTWbVe+NSduHxxLGB+qf3kFgQ6n66DSnuoINtVUjrLLIK2R+lxrBG07A==", "signatures": [{"sig": "MEUCIQDoyJLaOJE/iA5PMS/nGHW3XAgTRE9ETnElU9Nu+z1czwIgDZwrbZPez0MwPbRn8tGie54T3PO9flCOn1/t/L2I39s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNi9mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsEhAAoDnrDZWHnlhusvXveGuoKfJ1s02m0AvrcMldfDseV/o6qgI0\r\nkzZJihoAJb/Wc56UiVRmyDgdxsT9uxwFsAxE8XaTad/GjE60DRpoY78q3Uki\r\n9BqFXv54YMbZQxQ1tFCrB1Btj5K4LhuhKFloov6OLwRfLQOu8/ru04q7Cl2l\r\nOCXFa8hLnAv+3yCRc+ZVWD5Z5E8bfahnIqXfltBwgaAuCqxbijNvF+J+HMIR\r\naRpkU7UMnYVYQpe4l8NbC6fpZgzoF7krU6Va2Yr+ALqQrh4DbF8OqN6DKDVM\r\nOfX2377QeKQYP3gFztT7L7l43+cg/oXsC8ol5mZu2NK7+y9TvB6GjlKD7ltP\r\nuyaxcnps9OUU3DNpqHfb6YsYReawNHV3LQ7R8gfEe5tYh6BHKwTVNKFNB/Sg\r\n8ou5YP0qrz1UoU8M+JPfKTv0U3gadF1LBTPbvx66sGDDbpyFbGtsnxdV9zgU\r\nXIJN6Adan1i3I/Chx84MSZoDMscqVFLqb7I3O7IIfwjnarzAo6TTq2B6bpKZ\r\n4U9QhgTEQkqDLz2qd/zraAcApLKbWvewc0Y7FwhzCJYEYIDZg5w9faZS7+FB\r\niZipBpuPEDn4wm8VPP2UzS/fsTlZ+/o+o4jfG8nCVNRHYgLMbGYfNqPV3tVg\r\n+21G/N/4KI8DsbOX7Uz7/mMboqIkU5UjAjY=\r\n=TOyt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "9d8da5bf1a272ee3e14637feeef545b622822a03", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"nan": "^2.15.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.9.1_1647718246530_0.2419733792949037", "host": "s3://npm-registry-packages"}}, "2.9.2": {"name": "canvas", "version": "2.9.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.9.2", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7a088a548c9832df1a6d9711f8cd935ca1082224", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.9.2.tgz", "fileCount": 56, "integrity": "sha512-ldUKpdUdGp0enZmLbevXBcOqUrcoXHmkjOCpk0kXxqYfjJyVMzeM613j/NHJmzKaDNaMhRBHegqmGInyhgA+Kg==", "signatures": [{"sig": "MEQCIAslAToyhbKGBR5T29FmJjP4pDjC1zTax2d1ulwshGnmAiBjW99g8SEdwksA+i5jLCmesIuqrn1MS90rztlI9QdXxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitO/rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo51w//aWTe26HMeEpP/yBSd4EqqKiFK71xba6waGHWK5nb+F6WYifJ\r\nwkEpreJ8KR7wkoUR92F/w2QHNcuCMKszsWfOPWbWd8jGQIemEvexAS+B9GR5\r\nG8aq9hJALcR6x1/lMmbn+lw1R0tY8u07Zijld5dmdV8gP6yJAqnMCQtqFgF6\r\neW2qFl0mnF3FEnq7F/X32vVfDPH/YqIj4eo2jYBPswjwosVRSUVOunmirxRB\r\n2kGw1b44I5Og2y+QWSG1xKpxOCSF4oT66ouHdLOaBxivEEhfnFT9oCF+k1zr\r\n5VD3F5VlGUOJIr+blM0OMoSH42+P5BczwDNgNLsvalPY8pSYiMXSvCVzZO43\r\nW7EWGzxFjFvVxBKIFwq6uRJFdQIs9BnngNXQV/OWce27PfwI1j+wmMyd1zFA\r\nDeDdPRt9HEFPM8WKY3k3YcqNtfVL6qqDJXK1jwgUtHfl71PxP6GbMgexPwXb\r\n568xp1xI26zAV8dqO6nFUTxERk+SVa493Vv/gJ6zi6M9yObHyjshG7hR4Qzx\r\nrCl8d18PkFNsUWfzjJXteLu8JsqPe6a5UeAkoi1XyvuwJpp4nuZJbkK6Qiy4\r\nLwzEDjeRhV24pOfi45Ze4U/b8/u1BNn+7ZW3CWm3VnB4vQFnTAHVPs7Wm18d\r\nhUgaU4fkbB1vx/N9Ddm/YlfYEBNO2PKLH9E=\r\n=4Qmr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "d4dc2a87c3843b44dfdb8e26c738c5f38e4cadf8", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"nan": "^2.15.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.9.2_1656025067517_0.16720650765499445", "host": "s3://npm-registry-packages"}}, "2.9.3": {"name": "canvas", "version": "2.9.3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.9.3", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "8723c4f970442d4cdcedba5221579f9660a58bdb", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.9.3.tgz", "fileCount": 56, "integrity": "sha512-WOUM7ghii5TV2rbhaZkh1youv/vW1/Canev6Yx6BG2W+1S07w8jKZqKkPnbiPpQEDsnJdN8ouDd7OvQEGXDcUw==", "signatures": [{"sig": "MEUCIDBAuU0RZ52AgtoIPksQWYvVA3JiptgeDXgn+gnvzOlgAiEAxwBoGmjmBhM6pgBeCvyzvdlfj2Vq4exsT26bWR125B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJithVxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Lw/+MUf9/6O5X534kigk7bdtPVb+cTsixT94dOtS/3M7N4ZLRKcD\r\nZE1Z7oJ62F3UX43xVxDjD81sfPPaTyJ1wOFi4FjQTn9xB0Ndosejz6SWsZrF\r\n4FEvrdAzSJNp+rKEzj/aQbhvLwICDfJf47YL8m7bEoQpFJBWhztyIr8lU2eH\r\nUY4Y89sDk49TTktrZqNYnJeLkSyDZxRhj3k4B2yQSTiOaYE1QzS5z29X2QpI\r\nveaQT2fW9lZaof7W+1cLXLRLplM5ddie+tzTZtRh7pOS/2rh6EOgbkQEAzEb\r\nwG+wsp/gIaIB2czuXTaPWv52Ed9GGiJevrbp0VixXUiSi+q1WH8TzTRgEE3Z\r\nlVyDiilsi2NpSR1VF0fZUO5GFPrUU6Ogw5lePlJxT0uWmhTnMR4ISRy1lKm/\r\nu7sNjQ72Pz7C4m35rvBD/0T3RBOlTxCxfqTCEAdnb5W+sHMzQMDtEzw9ZbSr\r\nyA+bd3njCyBUqAW0LX2QRWR6IkQOTNHYklTL1u5HetEghvXBGX/3APQelQZB\r\nwjpUDjiNrQDg5rbRNpb832Tw7ZxhQszqYeXcH8ETs/CGAw6fyqtVgMBiWEw7\r\nfvSx/sdopbDDhS5I7+lqfh7/Voo9H+qkwUMPzVpIa7i/e5ZbIdhD2UpZmz7U\r\n1y1gFwaHIIkUhxNUl0lnaKDBqiFNw3oH0Vw=\r\n=AwOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "7a8a60661ff13c744010996e9b75ff4bcaffb496", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"nan": "^2.15.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.9.3_1656100209097_0.5071532960331342", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "canvas", "version": "2.10.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.10.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "5f48c8d1ff86c96356809097020336c3a1ccce27", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.10.0.tgz", "fileCount": 56, "integrity": "sha512-A0RPxLcH0pPKAY2VN243LdCNcOJXAT8n7nJnN7TZMGv9OuF8we9wfpWgVT/eFMzi+cDYf/384w4BpfjGCD9aKQ==", "signatures": [{"sig": "MEUCIG5WC2rSLjsDplM6aB+ROx/HefABk03OTN4IpkpbT0dxAiEAyhrOAb+7/Xi2Hjveo8iEe5DSevMLgZQubbGvDZhSmWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFBvrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1nQ/8DPjcKRhlVlrgmmvPId8yPgXfVm0Sy3Xm38+QoIgy76aXFiGP\r\nQ0/k+7XNKVsmdb6RHDlphvzau1/648fyk4YS+5OaivF28+Bbgc11glZnAKVV\r\nnL22Gh4zFsI9KHRy2GDthR2Fa5Kqg8IYXq/jS64bkBwJh7D3Gez4fp5lTT9F\r\nGXBfwPeTkO6sVHAh/KrXUpZFD2bZEWOVVUHz6TP5jZCyxMCw3WgTzXS0lwcL\r\nOjuXTz7p3l+d+oIx9OOyjDSZTRh3etk4Sqf9IfEsf3zp0VwtEhZeVc0IpNNw\r\neLh83FzgEaMLMmlvncoDV1zkM8mjE9VfrB1wJSAhB42WLmTLkrTxQbZNGV+9\r\nos3XfNeFhpCCPCMVxI9rOKTAtgMN8q1+owZfEC6loQIVdomBMRjWxb3Un+85\r\nXjiD4cfF4rdnc/qxJ+b468+zG1XWuDCsrfGgZThXVq83EVqpQPLraSggkwHm\r\njtM9RxJHHFz4ReBva15RTaXuRm93pdzR9OopAuCSeVmrUlqbSTnSddmqTxE9\r\n16s7Qhd76zusAx5dXbml+85MVGZe+QKBgA3MSqMnV3VcLBkw3PocHL71aGXh\r\nJzraJuFE8fvhVOC2oKBkZYW3mjYz1SU+AhwbWj925yIYWbA6kSXMQBisnM2k\r\nwoYitkyqSLhouBP7t2F4Iwk8j7jdUTjBwV8=\r\n=9l4X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "561d933fe251c9c9ea28f715dccf496f08667c46", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"nan": "^2.15.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.10.0_1662262251182_0.927962645269274", "host": "s3://npm-registry-packages"}}, "2.10.1": {"name": "canvas", "version": "2.10.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.10.1", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "fbfd4b1b3b106c3454481d79d363ebadf8811c08", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.10.1.tgz", "fileCount": 56, "integrity": "sha512-29pIjn9uwTUsIgJUNd7GXxKk8sg4iyJwLm1wIilNIqX1mVzXSc2nUij9exW1LqNpis1d2ebMYfMqTWcokZ4pdA==", "signatures": [{"sig": "MEYCIQDJPvFDyol7LhUcjsXpXHzMbMI49FrVeUa35ndK4XsvfgIhALBAu6ex2/9D4LOFUSYTaMJyGMrzmQuL4RaL8zB1/QLw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGNxAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSPBAAnURfWEXjcfgwBpEcKHjr65wZWqIchdqMRg++hcm1+27FniYF\r\nEBtHEDrhdOi/iMjI7Sh/wYIic59uF+s3Kfiqjvp1Eoz9+sgzA6eAotIj0lH2\r\nOgc6cvCIaceTYEz4aiXqjxB5+lA7h9a0hMXkc8JkzzXts/znTM98Lx5tv9Rf\r\nYwGcCEkzwhfwB0UAmibpqoaFjW39cXmziDPHjO4PhumM+dUmjLbFKTk/XjO5\r\nQKhr1+t/s5MgC1D3ZW6UfyuZWadocx4vrBQgs+BhPkyMtyDMboOqwB7/rfn1\r\nw0jKFCwIUdgc+UFruqFCGkhJCtDH713kDx7PTV+cSN8cs4MtUZkd7qE5ru/P\r\nB+ufJ82oOBgIpuAkHYS0C0ZPl5ArJAOTzXX8652rdHDimsIWV+qg2j69ujuH\r\nONL5AcAZsUflfb/ch3HQkay9vKRbyi1b8qpzJumQ1tW4XmJVsPLGTbytONdK\r\nCxPCpmvZRTYVeMt859Vpnjv1D2mcP41XPS4QfG/cRvJaUjPNRnMbpRqPaz8P\r\nBEodf1/UCb6mXeLtifHpnPtHZwGz+51DCC0lppWVn1LfZAZSs1vex2HvawUl\r\nZo+cHMzfgMr/T/YHzJVfbEx16vGTozcDUyYQ3ab5xb6dRQO4yuyNkPdWy+RB\r\nJRvzS5QZGrYHIoHmMzJadUUB6ML7iMMG65I=\r\n=3UvK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "b3e7df319c045c1dc74e390f4b3af161304c9c55", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"nan": "^2.15.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.10.1_1662573631842_0.6244105462626308", "host": "s3://npm-registry-packages"}}, "2.10.2": {"name": "canvas", "version": "2.10.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.10.2", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "66d827e24acd6c34667453728e13d337417d4b20", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.10.2.tgz", "fileCount": 56, "integrity": "sha512-FSmlsip0nZ0U4Zcfht0qBJqDhlfGuevTZKE8h+dBOYrJjGvY3iqMGSzzbvkaFhvMXiVxfcMaPHS/kge++T5SKg==", "signatures": [{"sig": "MEUCIDxgvZEZdINmS2O8mN96r2KIqxjTNktfvq6NFOIi3dBfAiEA7yw5/VQqCzPL2XZkLtGy7xtS8sL0/v+uLpfcP93NHFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjXfE2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/NRAAmZahZMT3vVKJgwQCx3oybrOXG2+4d16NJuzlyjtaYeWuII+1\r\nlNwsX6wvznbtdrfmDiyZ/S/WlJYjoCvUAcQBwUF0JsrTBP5opxieTLf24SnA\r\nSUMrxBadXF4JF3oldJm1gMA/hhV7y8L1T0jwK5yU2HTcx+TtOxSUEa72xm0E\r\nSAYuK8GHTi8OPefvei4K6l651mVgRJSJHxKOo5vWo9EQcQAMu1/V3u8FLjnO\r\nj+Qe4ABxYO0I2EaLKUy8Pleyx+4dNHsjoAywUkSpP2FWyfVKFIWXPY5dgXgL\r\nY5786M969LTiZg01ZSINyUJKEUMxi4wyugIj1t4VkXNcMraFu8AdLVx30g3L\r\nIJNJVZ4EH8WXd1p10SZNYG81KjIDENBkygU2bvcV3FPwJYC8FZF9u+xHridT\r\nKJz7zEVXTeeX3PcHQgoWlHBVw+upypkyR3eZsccj/a7PL7ACiUBU8JtDCgGW\r\n4quovJh9puMyFauzGi9P2JJl1/l/KIXzrHbYKed1EOSLTxLxVLH+5hz4M+8R\r\nlvRt3L7WtC0CO/z6EJNhy0sQ45z13u/iBl/ndwGj3YxBzpsC1xo5L/dWvgKu\r\nP1IlDYUnLo55Vs5AJEk6GUfVmtHUJVqwATfvGw9asBa6fmNFvdQ9QUQrIVUS\r\ns7Shdlg55CRsFBie00M3LOQ35JIyRldDBEs=\r\n=S+pf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "672104c1a4bd202e56d8837ef83ebf7aee2dfce2", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"nan": "^2.17.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.10.2_1667100981939_0.201590261418513", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "canvas", "version": "2.11.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.11.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "7f0c3e9ae94cf469269b5d3a7963a7f3a9936434", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.11.0.tgz", "fileCount": 56, "integrity": "sha512-bdTjFexjKJEwtIo0oRx8eD4G2yWoUOXP9lj279jmQ2zMnTQhT8C3512OKz3s+ZOaQlLbE7TuVvRDYDB3Llyy5g==", "signatures": [{"sig": "MEYCIQCsMZsvmIl4m1JYiGLw9UE7TBzxPfAxN6kC7NPqFUjhuwIhALLfnMrK4iRBT8vuJ+PwVAltZkznknkjwribuSa3cypo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpMlrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4kA/8CFvB+9E7Jv6lklCGCJNkvzY/7UsF7/z8p97/cAKhmBh7y/5F\r\nznM90Luj9Lebv4tZZvSP89jalJlzU8DbMsnB1tqrV2BccLNrMaH97aGRpZ/n\r\nHuacJRpc+nclPET+/bZ7gfWEtH+QMZNy+w0ecyzdL/gnbV+n83xQu3YQa0Te\r\n2By7zGJRx/p6qeozc0/66cyWTQa9lm+MT9H7J9Kjn3KtLXH/agQPG41JOuxT\r\nskIBu+AWATJrdK/b4NV9m31UIduuVt9q6LsjIKWMrmy2g+g+FHpqu7ENvdII\r\n1Do5YgCieUMtgTzF0gn2fxOrIII4f8uDL4/hrFA7Bs6peN5u6H47hlUYVelk\r\n78a6GuIHI2x+GJnpUzYdkDyd5Jj3Ow54DhvMqjw6N38gJ/hUfHcWev5g2RjH\r\nhozK+PqWlAjuC/8C7gWe9odI9/S08xMTSfaYAY3PTGnBuqa0+s8icVGVjTbr\r\n4vHzzvhihXyfh1UJEQazVETvvdo8nOTgchtJETT0LcpEVB+eLLK3tfgogkgH\r\nmC54TI9GxB8hSaL85NP8qwox5lCAjbEZfikpxy7qTXuLzLP+ISFyzUJWxvoU\r\n7sKKVgZVo5koKEtEMB5v66rVnzNvqw1qQYzAZ+5yY9jHAAQucV83YdeXu6z8\r\ngKodjcL4ESQQWCw7O+Zth7n7ABC4CDzrw3g=\r\n=IVax\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "fc160f5d3a4bc1171fa012391dda923561fb497e", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"nan": "^2.17.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.11.0_1671743851041_0.771163971930686", "host": "s3://npm-registry-packages"}}, "2.11.2": {"name": "canvas", "version": "2.11.2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.11.2", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "553d87b1e0228c7ac0fc72887c3adbac4abbd860", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.11.2.tgz", "fileCount": 62, "integrity": "sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==", "signatures": [{"sig": "MEUCIQDAJ8IL58ySIDIMtYvIBacy0EFlXiuyAUmdEEvaUvgD+gIgM313DfG46uBfH+kLDfuC+aA3aDHrFpCwoUtXC7HO05E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKfTDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1Ng//Q0aQK0J100P04L1whPDkJO7edH2+JbsAym1VrAKYgyKahGgZ\r\nHANhSq97S+HPHYXHA+uYCm+Uc9CZbZInw5dg02AfVS3C57ruMegZT8XKrZAi\r\n0ngRGzEEC1qBxPthaw8rkz8uX7e3LUBfBvg1RzwkndpbeM2zjF6qWeLKdzIM\r\nqcopRhC7BRYvoZrypNVM7KYc/cAEywUBaTCrmX2KPxNm2u7DfVYOFpsFXXJb\r\nTuDKVWI3mEwTZerY1/LZMOBlgV6nVV6tneHfEs/LFcJHBqzgFKu9QC1zJD26\r\njXYEPM+c+TSsGzgreDXBxBIKUqEppur9UImxjOmxvZ5LcB+6QNiJapyE0Ixp\r\n4cmUcAZgKYdTrJffIgaxD/67e2zr9h450vQrmhbdH7tHsHG5Pxgphhzs+k2C\r\nI2l6uh7SAIphm5b/2OyrIlWnkYMvNSgVuamxViMRmbCCdFfUKgLlfUtZMuUu\r\nDdoOZfttHDy1U2wkq3qhqLz8LQrpJBizSWiCgg+3C7eb6yCpuM75bBDuwNzx\r\nhA52UP8X7G8atmR1ka5hBEgsvnunfpfPO6r6K6zoPhzWXpy0bZFRInWSubN6\r\nmb2ttemRIhf0hXv+XihSBqhIKAg2AAoKTwdUHoe+g8GCSskT3D0tY6/mZpT5\r\n+jxXQxxbOhRSfU4e6SvZWeswr0TqpZfc6Qw=\r\n=FcqH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "38e0a3285a6e005e02a6505f3fc2809d0484e43b", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"nan": "^2.17.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.11.2_1680471235545_0.358649738679113", "host": "s3://npm-registry-packages"}}, "2.11.1": {"name": "canvas", "version": "2.11.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@2.11.1", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "241dc6a13bde1c587788504d533cace08e64d127", "tarball": "https://registry.npmjs.org/canvas/-/canvas-2.11.1.tgz", "fileCount": 62, "integrity": "sha512-N8bJ6l6ARJJGNEYXTtGcR4BLw01vwPd/jIPE4V1/15UKCw5RojuWATK/uDQyqvCz5Tvr8WY72V+lwSVlKzIqWw==", "signatures": [{"sig": "MEQCIAiJj3mRLBd6WPZlU9LVWu/jCesdbTO3NZ0Tn/lVigDcAiAsiYDD9ELdspsE1inS+GKpbxkrxAUj9kJP5Dqw92TVXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKfTlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp75w//U+Fp1pX7wl/RFC7IkV3D0LAGNjd/AYHBqrBjZPA4LoPf0xnI\r\nYBtnPtULuvkUvdk7Y9SuE1znscz/q3kDolCm52CqplII056SYqghGX+YcwUq\r\nnaWLw2GcUonTxHUG7ZMHa+ZTCuXOlPxn/SzBTxM6WKXMJTMjnRuyQ8JrRQXM\r\nDlW3yUW5FAz753ChOIHvf55FRCUYtIj9fS4PKW9pN4rRSaGz0JP0zp2k3gWW\r\nEMgb0wZlb2ewdw1lYe43AGNDLSsU1zgeo53v2VIPlQN0TLe2WeXYFeEIixVn\r\nlXIjg2KRqedS0LlTy3+9VJbp1OzeI67G5rnKrMiZYadhztD91P+1kB6tBJuu\r\n0QgooUY7oXn+kdh7ugXg5rQFuLU1JBV/+S8+3VwYJc8Du/R136uynV4mKBxc\r\nA8X05jXDHCfaJ42Urgs4rPeevUUjSRdsfQb7qKWV5ZKgVxGIa4DF6IR6RiJT\r\nQuf8dZjvCdRmxx9nOc9DsRJqqDB0kBwgjiIdnSauuIUgANqr4IkHrZbg51d9\r\nJ1lhaDfO3IFL/sQnBiBFYC91Ubm5fNfLifQuEkiM0rBLy0hVCHyBB3Gh1nU6\r\nh5k9GENnqC+Mr5yeS1f+zxkoQt14CXaxqyCM9mSykWZDixiprvqnpiUSwJpk\r\njkux0jAVRpbBi735/PVLqZ4V5MdpzdSqNV8=\r\n=Q6+J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "binary": {"host": "https://github.com/Automattic/node-canvas/releases/download/", "module_name": "canvas", "module_path": "build/Release", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "9ecfb70518889735ad61354824c4590403f5edaa", "scripts": {"lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "dtslint": "dtslint types", "install": "node-pre-gyp install --fallback-to-build --update-binary", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"nan": "^2.17.0", "simple-get": "^3.0.3", "@mapbox/node-pre-gyp": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_2.11.1_1680471268909_0.7018187784875445", "host": "s3://npm-registry-packages"}}, "3.0.0-rc1b": {"name": "canvas", "version": "3.0.0-rc1b", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0-rc1b", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "73d6da57b8276acaccd43bd46c31f0f9b9386617", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0-rc1b.tgz", "fileCount": 57, "integrity": "sha512-<PERSON><PERSON>4lY1WuRd9bd1hrU/KF6y4X00H2vh/7EL/cZSGZS89Xwc5z5XHO14u9P5Xzod4ueDn60iZ/OJdKFtIm2/eLQ==", "signatures": [{"sig": "MEUCIQCyJ95gWxKa68BxBO7GxnevSixFDegWyrhCkIfXhz4kCgIgZWAqFGiil6PR0XCBdwqESar5Io7eEwqUKUWveaUNWlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359707}, "main": "index.js", "types": "index.d.ts", "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "8894635ce2c3df10295ad7365670e1a5d0e48a46", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "deprecated": "This version was only for testing and is broken.", "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0-rc1b_1718838889698_0.1219496793419137", "host": "s3://npm-registry-packages"}}, "3.0.0-rc1c": {"name": "canvas", "version": "3.0.0-rc1c", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0-rc1c", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "e29a91c553cfe89f89fa33ce1e1766cdb36e969c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0-rc1c.tgz", "fileCount": 57, "integrity": "sha512-6ase9rq5E+L+yesAsWEtzkZia+47eDisHpRJTpFOd98YJabjhfQ3vuF8zweNl7LIQmocoKZCEdlt7kKj92lF2g==", "signatures": [{"sig": "MEYCIQDDIH5zs0rWPI6Z33om0DBxb2kXZ3mmtNA5rXmv6/Py0AIhAOtibONUJTq/RPNTJv2tKJI/TwQsmwYWywmIiMnhrUUP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359751}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "8894635ce2c3df10295ad7365670e1a5d0e48a46", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "deprecated": "This version was only for testing and is broken.", "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0-rc1c_1718840311338_0.501695916182219", "host": "s3://npm-registry-packages"}}, "3.0.0-rc1d": {"name": "canvas", "version": "3.0.0-rc1d", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0-rc1d", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "9f9fe85579392871f74d1267d1b84d6f052e8351", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0-rc1d.tgz", "fileCount": 57, "integrity": "sha512-VgVH66fwcm6sbQ6JihzL+SnvmupuCpgOBHIqSs1FC9h8bNHp9H0vztqHFF5+tNNUxrecBp4Et+PPUkEy/qveIQ==", "signatures": [{"sig": "MEUCIF4pZth+6+Iq2/JcF2HIysIxeRcL5ctNgudU40iBmvHJAiEAz+KS4E7Eojjb8EEQbDxKfGa8IVNYzZWFinSQGnffXZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359764}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "464b9ec68547befff843f5ae27443112aed65d5a", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi --path build || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "deprecated": "This version was only for testing and is broken.", "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0-rc1d_1718841165697_0.0713161229205701", "host": "s3://npm-registry-packages"}}, "3.0.0-rc1e": {"name": "canvas", "version": "3.0.0-rc1e", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0-rc1e", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "dbff5c4be709a63354874380ae22e95491ba094c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0-rc1e.tgz", "fileCount": 57, "integrity": "sha512-qkMbZlCDE+yKueJimZOLEtfwVdLlkUGI/eOHtcCKqBgPQ5ZjlIU07KGSzU1hlYtV6WCx+0GigFXKK2fMPFhDsQ==", "signatures": [{"sig": "MEQCIDR3fgWyyAJ0P0h0vaEDRItLGPzLA8tl5eRy1pWrAFssAiBXtKWOarEoPlTjXc7SM6ETsjWvJerpPcxbP8gwazO7jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359751}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "1ba7334ed75fcd2bdf2e4b6e5d110d6730c7957c", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "deprecated": "This version was only for testing and is broken.", "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0-rc1e_1718842084224_0.6949140213954283", "host": "s3://npm-registry-packages"}}, "3.0.0-rc2": {"name": "canvas", "version": "3.0.0-rc2", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0-rc2", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "710a91520e98b5f6829120f48980ce3fea985d15", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0-rc2.tgz", "fileCount": 57, "integrity": "sha512-esx4bYDznnqgRX4G8kaEaf0W3q8xIc51WpmrIitDzmcoEgwnv9wSKdzT6UxWZ4wkVu5+ileofppX0TpyviJRdQ==", "signatures": [{"sig": "MEUCIQC09uDfJS07flqVwzzKdAkXoL2QbD4vxIjoZVUB8TluCAIgR6znEtD8wLtDoD2+D77U6+wzWKLTKsEfH2WVVR1XzOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359750}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "130785fa1db9464e558755ff2a3bf60606ec7b8a", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0-rc2_1718843250694_0.5992929287756399", "host": "s3://npm-registry-packages"}}, "3.0.0-rc3": {"name": "canvas", "version": "3.0.0-rc3", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0-rc3", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "0f44aa7b7341e715b21e9b75e21949a78cad2988", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0-rc3.tgz", "fileCount": 57, "integrity": "sha512-LJVkMp4AH7/IRoLvhLS6R09uBt9O3O0mhCYL34AQV/+OC39jmTv22pJTF5Mgfa3V2JnzGl21MVrhEKmtmPtfQA==", "signatures": [{"sig": "MEYCIQDiz+IJhvCNdkBmIKGF88st6vtAvniNuBlDzE0/mWfA2AIhAPUGikXwJQUSfljnGbj+6nXqkelj5YyjgAQlo2IttxnY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375354}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "19a33287c4c571264f1d062e92d311bafb1685aa", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "23.1.0", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0-rc3_1733589222960_0.1882311026481973", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "canvas", "version": "3.0.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "680a5fead33d6535beb5986430d9b3517504ec0c", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.0.tgz", "fileCount": 57, "integrity": "sha512-NtcIBY88FjymQy+g2g5qnuP5IslrbWCQ3A6rSr1PeuYxVRapRZ3BZCrDyAakvI6CuDYidgZaf55ygulFVwROdg==", "signatures": [{"sig": "MEUCIQD3zXp/A0BePUL9AwnjmBy+JI0wj3QwG7Tcr0Rw877RrQIgSvaEvrJeu8iwMrSrYc2em+iBEbjddZnyf3J7bNki3r4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374504}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "834651230003e8ea63d5945f4bd1ef4371ec3c63", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.0_1734977172168_0.01695915819872096", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "canvas", "version": "3.0.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.0.1", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "b536df1d5c8c33c64f7752c499b3ff96a43eaf27", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.0.1.tgz", "fileCount": 57, "integrity": "sha512-PcpVF4f8RubAeN/jCQQ/UymDKzOiLmRPph8fOTzDnlsUihkO/AUlxuhaa7wGRc3vMcCbV1fzuvyu5cWZlIcn1w==", "signatures": [{"sig": "MEQCIE1qJfb8mVUzDiL+Av6u/cgnAgCyn1S2r3ViL8HkFQYVAiAlWTpYuQSHBFk4Bgsu6qcFTWOF7Fo3JIHRsx/77rskXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374852}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "80e94ea7644b8f0c879b6e4ba899e50e6289e09a", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"simple-get": "^3.0.3", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.0.1_1735681944421_0.27536824056737874", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "canvas", "version": "3.1.0", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.1.0", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "6cdf094b859fef8e39b0e2c386728a376f1727b2", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.1.0.tgz", "fileCount": 59, "integrity": "sha512-tTj3CqqukVJ9NgSahykNwtGda7V33VLObwrHfzT0vqJXu7J4d4C/7kQQW3fOEGDfZZoILPut5H00gOjyttPGyg==", "signatures": [{"sig": "MEQCIC6h0IYk/ukgXYR4v6ohq+1iX3XluKR0QS1UYBKryl+hAiB5Om1N5LNoorCtsZvGJAwnxFpx1mq0xjRihoXwrzEhiQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 407982}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "61e474e299b04babd4b5348bc15ba71bee42099e", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.1.0_1737517617314_0.19485150815991314", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "canvas", "version": "3.1.1", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "canvas@3.1.1", "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Automattic/node-canvas", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "dist": {"shasum": "d5399808e5cd19b0a9679248a7f98929e3f96341", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.1.1.tgz", "fileCount": 59, "integrity": "sha512-o8JptL14zrYFYSh5r7ClVzuwG77tvjRBgSJECyIF6AAHpL4MgORhlI+rru3/ZPKtdC7uopl+biESZWz1l/NpFA==", "signatures": [{"sig": "MEUCIQDfe/YjBMlKBOS4rOlP0HXPmJmla/XAlLy7y3sMYkvv/AIgHUy4fGKxv3HtlRTBPTGAAtbJkmUiAKxJ3xPM9kQlFW4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 407583}, "main": "index.js", "types": "index.d.ts", "binary": {"napi_versions": [7]}, "browser": "browser.js", "engines": {"node": "^18.12.0 || >= 20.9.0"}, "gitHead": "7a942d484fe10544432a3a9a21034f3e811e7995", "scripts": {"tsd": "tsd", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "install": "prebuild-install -r napi || node-gyp rebuild", "test-wpt": "mocha test/wpt/generated/*.js", "benchmark": "node benchmarks/run.js", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "prebenchmark": "node-gyp build", "pretest-server": "node-gyp build"}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "actor": {"name": "cale<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Automattic/node-canvas.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "Canvas graphics API backed by Cairo", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"node-addon-api": "^7.0.0", "prebuild-install": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.29.0", "mocha": "^5.2.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "standard": "^12.0.1", "pixelmatch": "^4.0.2", "typescript": "^4.2.2", "@types/node": "^10.12.18", "assert-rejects": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/canvas_3.1.1_1750344742231_0.4816575385453681", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "canvas", "description": "Canvas graphics API backed by Cairo", "version": "3.1.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "browser": "browser.js", "types": "index.d.ts", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "homepage": "https://github.com/Automattic/node-canvas", "repository": {"type": "git", "url": "git://github.com/Automattic/node-canvas.git"}, "scripts": {"prebenchmark": "node-gyp build", "benchmark": "node benchmarks/run.js", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "pretest-server": "node-gyp build", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "test-wpt": "mocha test/wpt/generated/*.js", "install": "prebuild-install -r napi || node-gyp rebuild", "tsd": "tsd"}, "dependencies": {"node-addon-api": "^7.0.0", "prebuild-install": "^7.1.3"}, "devDependencies": {"@types/node": "^10.12.18", "assert-rejects": "^1.0.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "mocha": "^5.2.0", "pixelmatch": "^4.0.2", "standard": "^12.0.1", "tsd": "^0.29.0", "typescript": "^4.2.2"}, "engines": {"node": "^18.12.0 || >= 20.9.0"}, "binary": {"napi_versions": [7]}, "license": "MIT", "_id": "canvas@3.1.2", "gitHead": "a862af8040c03593bd9376fe2464a73867a0924d", "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Z/tzFAcBzoCvJlOSlCnoekh1Gu8YMn0J51+UAuXJAbW1Z6I9l2mZgdD7738MepoeeIcUdDtbMnOg6cC7GJxy/g==", "shasum": "a98406ef6178d31e39eb7dc0a488b1181555b792", "tarball": "https://registry.npmjs.org/canvas/-/canvas-3.1.2.tgz", "fileCount": 59, "unpackedSize": 407653, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQChAWliICDqJUzZSrGT2NCKxfhl16C61flF3xySgzM7dgIgH/KpYkQ3JA6EJYWbwUEHPMkkMDSx//TLArmYAcVaiyk="}]}, "_npmUser": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/canvas_3.1.2_1750907056331_0.23610266884923248"}, "_hasShrinkwrap": false}}, "time": {"created": "2010-12-19T04:03:01.787Z", "modified": "2025-06-26T03:04:16.687Z", "0.0.2": "2010-12-19T04:03:01.787Z", "0.0.5": "2010-12-19T04:03:01.787Z", "0.0.4": "2010-12-19T04:03:01.787Z", "0.0.6": "2010-12-19T04:03:01.787Z", "0.0.3": "2010-12-19T04:03:01.787Z", "0.0.7": "2010-12-19T04:03:01.787Z", "0.0.1": "2010-12-19T04:03:01.787Z", "0.3.0": "2010-12-19T04:03:01.788Z", "0.4.0": "2010-12-19T04:03:01.788Z", "0.0.8": "2010-12-19T04:03:01.788Z", "0.2.0": "2010-12-19T04:03:01.788Z", "0.1.0": "2010-12-19T04:03:01.788Z", "0.3.2": "2010-12-19T04:03:01.788Z", "0.3.1": "2010-12-19T04:03:01.788Z", "0.3.3": "2010-12-19T04:03:01.788Z", "0.2.1": "2010-12-19T04:03:01.788Z", "0.4.1": "2010-12-19T04:03:01.788Z", "0.4.2": "2010-12-28T16:22:51.173Z", "0.4.3": "2011-01-11T17:40:33.576Z", "0.5.0": "2011-03-14T20:38:13.421Z", "0.5.1": "2011-03-16T18:20:03.710Z", "0.5.2": "2011-04-09T16:27:20.704Z", "0.5.3": "2011-04-11T21:42:42.719Z", "0.5.4": "2011-04-20T15:49:20.920Z", "0.6.0": "2011-06-04T17:37:49.060Z", "0.7.0": "2011-07-12T16:21:18.977Z", "0.7.1": "2011-08-25T18:49:25.490Z", "0.7.2": "2011-08-30T18:02:36.835Z", "0.7.3": "2011-09-14T16:51:35.126Z", "0.8.0": "2011-10-28T20:54:37.711Z", "0.8.1": "2011-10-31T20:56:02.388Z", "0.8.2": "2011-12-14T16:50:14.079Z", "0.8.3": "2012-01-04T18:52:11.267Z", "0.9.0": "2012-01-14T00:15:36.670Z", "0.10.0": "2012-01-18T16:55:26.476Z", "0.10.1": "2012-01-31T17:04:57.424Z", "0.10.2": "2012-02-06T16:52:39.874Z", "0.10.3": "2012-02-27T23:03:04.014Z", "0.11.0": "2012-04-10T13:09:21.878Z", "0.11.1": "2012-04-10T13:18:01.582Z", "0.11.2": "2012-04-12T14:16:31.124Z", "0.11.3": "2012-04-26T05:32:17.275Z", "0.12.0": "2012-05-02T21:12:05.546Z", "0.12.1": "2012-06-29T17:54:56.931Z", "0.13.0": "2012-08-13T15:38:00.587Z", "0.13.1": "2012-08-20T22:46:18.071Z", "1.0.0": "2013-01-16T21:51:58.148Z", "1.0.1": "2013-02-25T20:44:48.071Z", "1.0.2": "2013-03-23T00:44:16.623Z", "1.0.3": "2013-06-04T19:32:07.089Z", "1.0.4": "2013-07-24T01:11:52.364Z", "1.1.0": "2013-08-01T15:38:05.772Z", "1.1.1": "2013-10-09T22:28:51.456Z", "1.1.2": "2013-10-31T11:56:31.437Z", "1.1.3": "2014-01-08T13:51:03.951Z", "1.1.4": "2014-06-08T16:29:24.436Z", "1.1.5": "2014-06-26T23:03:57.107Z", "1.1.6": "2014-08-02T00:59:13.778Z", "1.2.0": "2015-01-31T17:41:41.132Z", "1.2.1": "2015-02-10T14:50:14.649Z", "1.2.2": "2015-04-20T22:30:15.487Z", "1.2.3": "2015-05-22T05:10:14.449Z", "1.2.4": "2015-07-23T17:09:38.468Z", "1.2.5": "2015-07-28T15:51:38.978Z", "1.2.6": "2015-07-29T14:03:41.999Z", "1.2.7": "2015-07-29T14:27:08.841Z", "1.2.8": "2015-08-30T10:19:44.536Z", "1.2.9": "2015-09-14T21:01:05.713Z", "1.2.10": "2015-10-12T20:03:09.335Z", "1.2.11": "2015-10-19T23:13:51.043Z", "1.3.0": "2015-10-26T07:41:19.987Z", "1.3.1": "2015-11-09T14:36:43.234Z", "1.3.2": "2015-11-18T03:09:29.400Z", "1.3.3": "2015-11-21T17:04:07.537Z", "1.3.4": "2015-11-21T17:04:58.161Z", "1.3.5": "2015-12-07T22:04:26.812Z", "1.3.6": "2016-01-05T23:57:22.688Z", "1.3.7": "2016-01-13T13:58:09.284Z", "1.3.8": "2016-01-22T12:22:55.987Z", "1.3.9": "2016-01-27T08:14:17.854Z", "1.3.10": "2016-02-07T09:57:54.368Z", "1.3.11": "2016-03-01T08:34:28.459Z", "1.3.12": "2016-03-01T08:42:53.702Z", "1.3.13": "2016-05-01T12:50:24.451Z", "1.3.14": "2016-05-05T15:01:37.327Z", "1.3.15": "2016-05-09T17:04:47.269Z", "1.3.16": "2016-05-29T15:47:54.537Z", "1.4.0": "2016-06-20T07:14:32.993Z", "1.5.0": "2016-09-11T16:47:59.061Z", "1.6.0": "2016-10-16T20:46:26.178Z", "1.6.1": "2016-10-23T13:17:50.860Z", "1.6.2": "2016-10-30T19:31:31.556Z", "1.6.3": "2017-02-14T20:01:23.593Z", "1.6.4": "2017-02-26T18:46:05.408Z", "1.6.5": "2017-03-18T10:23:43.926Z", "2.0.0-alpha.1": "2017-05-03T22:06:42.914Z", "2.0.0-alpha.2": "2017-05-06T14:25:20.786Z", "1.6.6": "2017-07-31T21:47:45.195Z", "2.0.0-alpha.3": "2017-08-28T09:56:04.954Z", "2.0.0-alpha.4": "2017-08-29T19:36:56.508Z", "2.0.0-alpha.5": "2017-09-05T09:49:25.972Z", "1.6.7": "2017-09-08T09:36:43.811Z", "2.0.0-alpha.6": "2017-11-06T12:15:57.064Z", "2.0.0-alpha.7": "2017-12-04T16:34:55.659Z", "2.0.0-alpha.8": "2017-12-12T15:05:44.842Z", "1.6.8": "2017-12-12T15:08:26.595Z", "1.6.9": "2017-12-21T00:52:44.360Z", "2.0.0-alpha.9": "2017-12-27T10:08:53.161Z", "2.0.0-alpha.10": "2018-03-07T09:57:33.240Z", "2.0.0-alpha.11": "2018-03-09T11:44:53.573Z", "2.0.0-alpha.12": "2018-03-22T19:33:07.033Z", "1.6.10": "2018-03-22T19:54:54.736Z", "1.6.11": "2018-05-15T11:46:25.979Z", "2.0.0-alpha.13": "2018-08-02T09:37:58.378Z", "2.0.0-alpha.14": "2018-09-08T12:17:37.156Z", "1.6.12": "2018-09-08T12:36:35.285Z", "2.0.0-alpha.15": "2018-09-25T18:04:23.281Z", "2.0.0-alpha.16": "2018-09-25T20:49:14.676Z", "2.0.0-alpha.17": "2018-09-29T09:54:37.789Z", "1.6.13": "2018-10-10T06:27:01.533Z", "2.0.0-alpha.18": "2018-10-10T06:27:44.699Z", "2.0.0": "2018-10-14T13:01:31.179Z", "2.0.1": "2018-10-16T06:43:50.543Z", "2.1.0": "2018-11-07T18:29:02.326Z", "2.2.0": "2018-12-03T14:58:55.355Z", "2.3.0": "2019-01-11T16:10:20.972Z", "2.3.1": "2019-01-14T18:23:23.825Z", "2.4.0": "2019-03-14T11:48:50.657Z", "2.4.1": "2019-03-19T17:51:38.466Z", "2.5.0": "2019-05-01T13:04:19.179Z", "2.6.0": "2019-06-11T14:04:57.303Z", "2.6.1": "2019-10-06T17:25:42.393Z", "2.7.0": "2021-03-01T09:33:44.472Z", "2.8.0": "2021-05-17T06:28:05.141Z", "2.9.0": "2022-01-17T23:23:16.268Z", "2.9.1": "2022-03-19T19:30:46.651Z", "2.9.2": "2022-06-23T22:57:47.693Z", "2.9.3": "2022-06-24T19:50:09.234Z", "2.10.0": "2022-09-04T03:30:51.352Z", "2.10.1": "2022-09-07T18:00:32.040Z", "2.10.2": "2022-10-30T03:36:22.133Z", "2.11.0": "2022-12-22T21:17:31.220Z", "2.11.2": "2023-04-02T21:33:55.740Z", "2.11.1": "2023-04-02T21:34:29.061Z", "3.0.0-rc1b": "2024-06-19T23:14:49.905Z", "3.0.0-rc1c": "2024-06-19T23:38:31.479Z", "3.0.0-rc1d": "2024-06-19T23:52:45.892Z", "3.0.0-rc1e": "2024-06-20T00:08:04.463Z", "3.0.0-rc2": "2024-06-20T00:27:30.965Z", "3.0.0-rc3": "2024-12-07T16:33:43.135Z", "3.0.0": "2024-12-23T18:06:12.480Z", "3.0.1": "2024-12-31T21:52:24.644Z", "3.1.0": "2025-01-22T03:46:57.498Z", "3.1.1": "2025-06-19T14:52:22.448Z", "3.1.2": "2025-06-26T03:04:16.534Z"}, "bugs": {"url": "https://github.com/Automattic/node-canvas/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/Automattic/node-canvas", "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "repository": {"type": "git", "url": "git://github.com/Automattic/node-canvas.git"}, "description": "Canvas graphics API backed by Cairo", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "kangax", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "zbb<PERSON><PERSON><EMAIL>"}, {"name": "cale<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# node-canvas\n\n![Test](https://github.com/Automattic/node-canvas/workflows/Test/badge.svg)\n[![NPM version](https://badge.fury.io/js/canvas.svg)](http://badge.fury.io/js/canvas)\n\nnode-canvas is a [Cairo](http://cairographics.org/)-backed Canvas implementation for [Node.js](http://nodejs.org).\n\n## Installation\n\n```bash\n$ npm install canvas\n```\n\nBy default, pre-built binaries will be downloaded if you're on one of the following platforms:\n- macOS x86/64\n- macOS aarch64 (aka Apple silicon)\n- Linux x86/64 (glibc only)\n- Windows x86/64\n\nIf you want to build from source, use `npm install --build-from-source` and see the **Compiling** section below.\n\nThe minimum version of Node.js required is **18.12.0**.\n\n### Compiling\n\nIf you don't have a supported OS or processor architecture, or you use `--build-from-source`, the module will be compiled on your system. This requires several dependencies, including Cairo and Pango.\n\nFor detailed installation information, see the [wiki](https://github.com/Automattic/node-canvas/wiki/_pages). One-line installation instructions for common OSes are below. Note that libgif/giflib, librsvg and libjpeg are optional and only required if you need GIF, SVG and JPEG support, respectively. Cairo v1.10.0 or later is required.\n\nOS | Command\n----- | -----\nmacOS | Using [Homebrew](https://brew.sh/):<br/>`brew install pkg-config cairo pango libpng jpeg giflib librsvg pixman python-setuptools`\nUbuntu | `sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev`\nFedora | `sudo yum install gcc-c++ cairo-devel pango-devel libjpeg-turbo-devel giflib-devel`\nSolaris | `pkgin install cairo pango pkg-config xproto renderproto kbproto xextproto`\nOpenBSD | `doas pkg_add cairo pango png jpeg giflib`\nWindows | See the [wiki](https://github.com/Automattic/node-canvas/wiki/Installation:-Windows)\nOthers | See the [wiki](https://github.com/Automattic/node-canvas/wiki)\n\n**Mac OS X v10.11+:** If you have recently updated to Mac OS X v10.11+ and are experiencing trouble when compiling, run the following command: `xcode-select --install`. Read more about the problem [on Stack Overflow](http://stackoverflow.com/a/32929012/148072).\nIf you have xcode 10.0 or higher installed, in order to build from source you need NPM 6.4.1 or higher.\n\n## Quick Example\n\n```javascript\nconst { createCanvas, loadImage } = require('canvas')\nconst canvas = createCanvas(200, 200)\nconst ctx = canvas.getContext('2d')\n\n// Write \"Awesome!\"\nctx.font = '30px Impact'\nctx.rotate(0.1)\nctx.fillText('Awesome!', 50, 100)\n\n// Draw line under text\nvar text = ctx.measureText('Awesome!')\nctx.strokeStyle = 'rgba(0,0,0,0.5)'\nctx.beginPath()\nctx.lineTo(50, 102)\nctx.lineTo(50 + text.width, 102)\nctx.stroke()\n\n// Draw cat with lime helmet\nloadImage('examples/images/lime-cat.jpg').then((image) => {\n  ctx.drawImage(image, 50, 0, 70, 70)\n\n  console.log('<img src=\"' + canvas.toDataURL() + '\" />')\n})\n```\n\n## Upgrading from 1.x to 2.x\n\nSee the [changelog](https://github.com/Automattic/node-canvas/blob/master/CHANGELOG.md) for a guide to upgrading from 1.x to 2.x.\n\nFor version 1.x documentation, see [the v1.x branch](https://github.com/Automattic/node-canvas/tree/v1.x).\n\n## Documentation\n\nThis project is an implementation of the Web Canvas API and implements that API as closely as possible. For API documentation, please visit [Mozilla Web Canvas API](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API). (See [Compatibility Status](https://github.com/Automattic/node-canvas/wiki/Compatibility-Status) for the current API compliance.) All utility methods and non-standard APIs are documented below.\n\n### Utility methods\n\n* [createCanvas()](#createcanvas)\n* [createImageData()](#createimagedata)\n* [loadImage()](#loadimage)\n* [registerFont()](#registerfont)\n* [deregisterAllFonts()](#deregisterAllFonts)\n\n\n### Non-standard APIs\n\n* [Image#src](#imagesrc)\n* [Image#dataMode](#imagedatamode)\n* [Canvas#toBuffer()](#canvastobuffer)\n* [Canvas#createPNGStream()](#canvascreatepngstream)\n* [Canvas#createJPEGStream()](#canvascreatejpegstream)\n* [Canvas#createPDFStream()](#canvascreatepdfstream)\n* [Canvas#toDataURL()](#canvastodataurl)\n* [CanvasRenderingContext2D#patternQuality](#canvasrenderingcontext2dpatternquality)\n* [CanvasRenderingContext2D#quality](#canvasrenderingcontext2dquality)\n* [CanvasRenderingContext2D#textDrawingMode](#canvasrenderingcontext2dtextdrawingmode)\n* [CanvasRenderingContext2D#globalCompositeOperation = 'saturate'](#canvasrenderingcontext2dglobalcompositeoperation--saturate)\n* [CanvasRenderingContext2D#antialias](#canvasrenderingcontext2dantialias)\n\n### createCanvas()\n\n> ```ts\n> createCanvas(width: number, height: number, type?: 'PDF'|'SVG') => Canvas\n> ```\n\nCreates a Canvas instance. This method works in both Node.js and Web browsers, where there is no Canvas constructor. (See `browser.js` for the implementation that runs in browsers.)\n\n```js\nconst { createCanvas } = require('canvas')\nconst mycanvas = createCanvas(200, 200)\nconst myPDFcanvas = createCanvas(600, 800, 'pdf') // see \"PDF Support\" section\n```\n\n### createImageData()\n\n> ```ts\n> createImageData(width: number, height: number) => ImageData\n> createImageData(data: Uint8ClampedArray, width: number, height?: number) => ImageData\n> // for alternative pixel formats:\n> createImageData(data: Uint16Array, width: number, height?: number) => ImageData\n> ```\n\nCreates an ImageData instance. This method works in both Node.js and Web browsers.\n\n```js\nconst { createImageData } = require('canvas')\nconst width = 20, height = 20\nconst arraySize = width * height * 4\nconst mydata = createImageData(new Uint8ClampedArray(arraySize), width)\n```\n\n### loadImage()\n\n> ```ts\n> loadImage() => Promise<Image>\n> ```\n\nConvenience method for loading images. This method works in both Node.js and Web browsers.\n\n```js\nconst { loadImage } = require('canvas')\nconst myimg = loadImage('http://server.com/image.png')\n\nmyimg.then(() => {\n  // do something with image\n}).catch(err => {\n  console.log('oh no!', err)\n})\n\n// or with async/await:\nconst myimg = await loadImage('http://server.com/image.png')\n// do something with image\n```\n\n### registerFont()\n\n> ```ts\n> registerFont(path: string, { family: string, weight?: string, style?: string }) => void\n> ```\n\nTo use a font file that is not installed as a system font, use `registerFont()` to register the font with Canvas.\n\n```js\nconst { registerFont, createCanvas } = require('canvas')\nregisterFont('comicsans.ttf', { family: 'Comic Sans' })\n\nconst canvas = createCanvas(500, 500)\nconst ctx = canvas.getContext('2d')\n\nctx.font = '12px \"Comic Sans\"'\nctx.fillText('Everyone hates this font :(', 250, 10)\n```\n\nThe second argument is an object with properties that resemble the CSS properties that are specified in `@font-face` rules. You must specify at least `family`. `weight`, and `style` are optional and default to `'normal'`.\n\n### deregisterAllFonts()\n\n> ```ts\n> deregisterAllFonts() => void\n> ```\n\nUse `deregisterAllFonts` to unregister all fonts that have been previously registered. This method is useful when you want to remove all registered fonts, such as when using the canvas in tests\n\n```ts\nconst { registerFont, createCanvas, deregisterAllFonts } = require('canvas')\n\ndescribe('text rendering', () => {\n    afterEach(() => {\n        deregisterAllFonts();\n    })\n    it('should render text with Comic Sans', () => {\n        registerFont('comicsans.ttf', { family: 'Comic Sans' })\n\n        const canvas = createCanvas(500, 500)\n        const ctx = canvas.getContext('2d')\n        \n        ctx.font = '12px \"Comic Sans\"'\n        ctx.fillText('Everyone loves this font :)', 250, 10)\n        \n        // assertScreenshot()\n    })\n})\n```\n\n### Image#src\n\n> ```ts\n> img.src: string|Buffer\n> ```\n\nAs in browsers, `img.src` can be set to a `data:` URI or a remote URL. In addition, node-canvas allows setting `src` to a local file path or `Buffer` instance.\n\n```javascript\nconst { Image } = require('canvas')\n\n// From a buffer:\nfs.readFile('images/squid.png', (err, squid) => {\n  if (err) throw err\n  const img = new Image()\n  img.onload = () => ctx.drawImage(img, 0, 0)\n  img.onerror = err => { throw err }\n  img.src = squid\n})\n\n// From a local file path:\nconst img = new Image()\nimg.onload = () => ctx.drawImage(img, 0, 0)\nimg.onerror = err => { throw err }\nimg.src = 'images/squid.png'\n\n// From a remote URL:\nimg.src = 'http://picsum.photos/200/300'\n// ... as above\n\n// From a `data:` URI:\nimg.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=='\n// ... as above\n```\n\n*Note: In some cases, `img.src=` is currently synchronous. However, you should always use `img.onload` and `img.onerror`, as we intend to make `img.src=` always asynchronous as it is in browsers. See https://github.com/Automattic/node-canvas/issues/1007.*\n\n### Image#dataMode\n\n> ```ts\n> img.dataMode: number\n> ```\n\nApplies to JPEG images drawn to PDF canvases only.\n\nSetting `img.dataMode = Image.MODE_MIME` or `Image.MODE_MIME|Image.MODE_IMAGE` enables MIME data tracking of images. When MIME data is tracked, PDF canvases can embed JPEGs directly into the output, rather than re-encoding into PNG. This can drastically reduce filesize and speed up rendering.\n\n```javascript\nconst { Image, createCanvas } = require('canvas')\nconst canvas = createCanvas(w, h, 'pdf')\nconst img = new Image()\nimg.dataMode = Image.MODE_IMAGE // Only image data tracked\nimg.dataMode = Image.MODE_MIME // Only mime data tracked\nimg.dataMode = Image.MODE_MIME | Image.MODE_IMAGE // Both are tracked\n```\n\nIf working with a non-PDF canvas, image data *must* be tracked; otherwise the output will be junk.\n\nEnabling mime data tracking has no benefits (only a slow down) unless you are generating a PDF.\n\n### Canvas#toBuffer()\n\n> ```ts\n> canvas.toBuffer((err: Error|null, result: Buffer) => void, mimeType?: string, config?: any) => void\n> canvas.toBuffer(mimeType?: string, config?: any) => Buffer\n> ```\n\nCreates a [`Buffer`](https://nodejs.org/api/buffer.html) object representing the image contained in the canvas.\n\n* **callback** If provided, the buffer will be provided in the callback instead of being returned by the function. Invoked with an error as the first argument if encoding failed, or the resulting buffer as the second argument if it succeeded. Not supported for mimeType `raw` or for PDF or SVG canvases.\n* **mimeType** A string indicating the image format. Valid options are `image/png`, `image/jpeg` (if node-canvas was built with JPEG support), `raw` (unencoded data in BGRA order on little-endian (most) systems, ARGB on big-endian systems; top-to-bottom), `application/pdf` (for PDF canvases) and `image/svg+xml` (for SVG canvases). Defaults to `image/png` for image canvases, or the corresponding type for PDF or SVG canvas.\n* **config**\n  * For `image/jpeg`, an object specifying the quality (0 to 1), if progressive compression should be used and/or if chroma subsampling should be used: `{quality: 0.75, progressive: false, chromaSubsampling: true}`. All properties are optional.\n\n  * For `image/png`, an object specifying the ZLIB compression level (between 0 and 9), the compression filter(s), the palette (indexed PNGs only), the the background palette index (indexed PNGs only) and/or the resolution (ppi): `{compressionLevel: 6, filters: canvas.PNG_ALL_FILTERS, palette: undefined, backgroundIndex: 0, resolution: undefined}`. All properties are optional.\n\n    Note that the PNG format encodes the resolution in pixels per meter, so if you specify `96`, the file will encode 3780 ppm (~96.01 ppi). The resolution is undefined by default to match common browser behavior.\n\n  * For `application/pdf`, an object specifying optional document metadata: `{title: string, author: string, subject: string, keywords: string, creator: string, creationDate: Date, modDate: Date}`. All properties are optional and default to `undefined`, except for `creationDate`, which defaults to the current date. *Adding metadata requires Cairo 1.16.0 or later.*\n\n    For a description of these properties, see page 550 of [PDF 32000-1:2008](https://www.adobe.com/content/dam/acom/en/devnet/acrobat/pdfs/PDF32000_2008.pdf).\n\n    Note that there is no standard separator for `keywords`. A space is recommended because it is in common use by other applications, and Cairo will enclose the list of keywords in quotes if a comma or semicolon is used.\n\n**Return value**\n\nIf no callback is provided, a [`Buffer`](https://nodejs.org/api/buffer.html). If a callback is provided, none.\n\n#### Examples\n\n```js\n// Default: buf contains a PNG-encoded image\nconst buf = canvas.toBuffer()\n\n// PNG-encoded, zlib compression level 3 for faster compression but bigger files, no filtering\nconst buf2 = canvas.toBuffer('image/png', { compressionLevel: 3, filters: canvas.PNG_FILTER_NONE })\n\n// JPEG-encoded, 50% quality\nconst buf3 = canvas.toBuffer('image/jpeg', { quality: 0.5 })\n\n// Asynchronous PNG\ncanvas.toBuffer((err, buf) => {\n  if (err) throw err // encoding failed\n  // buf is PNG-encoded image\n})\n\ncanvas.toBuffer((err, buf) => {\n  if (err) throw err // encoding failed\n  // buf is JPEG-encoded image at 95% quality\n}, 'image/jpeg', { quality: 0.95 })\n\n// BGRA pixel values, native-endian\nconst buf4 = canvas.toBuffer('raw')\nconst { stride, width } = canvas\n// In memory, this is `canvas.height * canvas.stride` bytes long.\n// The top row of pixels, in BGRA order on little-endian hardware,\n// left-to-right, is:\nconst topPixelsBGRALeftToRight = buf4.slice(0, width * 4)\n// And the third row is:\nconst row3 = buf4.slice(2 * stride, 2 * stride + width * 4)\n\n// SVG and PDF canvases\nconst myCanvas = createCanvas(w, h, 'pdf')\nmyCanvas.toBuffer() // returns a buffer containing a PDF-encoded canvas\n// With optional metadata:\nmyCanvas.toBuffer('application/pdf', {\n  title: 'my picture',\n  keywords: 'node.js demo cairo',\n  creationDate: new Date()\n})\n```\n\n### Canvas#createPNGStream()\n\n> ```ts\n> canvas.createPNGStream(config?: any) => ReadableStream\n> ```\n\nCreates a [`ReadableStream`](https://nodejs.org/api/stream.html#stream_class_stream_readable) that emits PNG-encoded data.\n\n* `config` An object specifying the ZLIB compression level (between 0 and 9), the compression filter(s), the palette (indexed PNGs only) and/or the background palette index (indexed PNGs only): `{compressionLevel: 6, filters: canvas.PNG_ALL_FILTERS, palette: undefined, backgroundIndex: 0, resolution: undefined}`. All properties are optional.\n\n#### Examples\n\n```javascript\nconst fs = require('fs')\nconst out = fs.createWriteStream(__dirname + '/test.png')\nconst stream = canvas.createPNGStream()\nstream.pipe(out)\nout.on('finish', () =>  console.log('The PNG file was created.'))\n```\n\nTo encode indexed PNGs from canvases with `pixelFormat: 'A8'` or `'A1'`, provide an options object:\n\n```js\nconst palette = new Uint8ClampedArray([\n  //r    g    b    a\n    0,  50,  50, 255, // index 1\n   10,  90,  90, 255, // index 2\n  127, 127, 255, 255\n  // ...\n])\ncanvas.createPNGStream({\n  palette: palette,\n  backgroundIndex: 0 // optional, defaults to 0\n})\n```\n\n### Canvas#createJPEGStream()\n\n> ```ts\n> canvas.createJPEGStream(config?: any) => ReadableStream\n> ```\n\nCreates a [`ReadableStream`](https://nodejs.org/api/stream.html#stream_class_stream_readable) that emits JPEG-encoded data.\n\n*Note: At the moment, `createJPEGStream()` is synchronous under the hood. That is, it runs in the main thread, not in the libuv threadpool.*\n\n* `config` an object specifying the quality (0 to 1), if progressive compression should be used and/or if chroma subsampling should be used: `{quality: 0.75, progressive: false, chromaSubsampling: true}`. All properties are optional.\n\n#### Examples\n\n```javascript\nconst fs = require('fs')\nconst out = fs.createWriteStream(__dirname + '/test.jpeg')\nconst stream = canvas.createJPEGStream()\nstream.pipe(out)\nout.on('finish', () =>  console.log('The JPEG file was created.'))\n\n// Disable 2x2 chromaSubsampling for deeper colors and use a higher quality\nconst stream = canvas.createJPEGStream({\n  quality: 0.95,\n  chromaSubsampling: false\n})\n```\n\n### Canvas#createPDFStream()\n\n> ```ts\n> canvas.createPDFStream(config?: any) => ReadableStream\n> ```\n\n* `config` an object specifying optional document metadata: `{title: string, author: string, subject: string, keywords: string, creator: string, creationDate: Date, modDate: Date}`. See `toBuffer()` for more information. *Adding metadata requires Cairo 1.16.0 or later.*\n\nApplies to PDF canvases only. Creates a [`ReadableStream`](https://nodejs.org/api/stream.html#stream_class_stream_readable) that emits the encoded PDF. `canvas.toBuffer()` also produces an encoded PDF, but `createPDFStream()` can be used to reduce memory usage.\n\n### Canvas#toDataURL()\n\nThis is a standard API, but several non-standard calls are supported. The full list of supported calls is:\n\n```js\ndataUrl = canvas.toDataURL() // defaults to PNG\ndataUrl = canvas.toDataURL('image/png')\ndataUrl = canvas.toDataURL('image/jpeg')\ndataUrl = canvas.toDataURL('image/jpeg', quality) // quality from 0 to 1\ncanvas.toDataURL((err, png) => { }) // defaults to PNG\ncanvas.toDataURL('image/png', (err, png) => { })\ncanvas.toDataURL('image/jpeg', (err, jpeg) => { }) // sync JPEG is not supported\ncanvas.toDataURL('image/jpeg', {...opts}, (err, jpeg) => { }) // see Canvas#createJPEGStream for valid options\ncanvas.toDataURL('image/jpeg', quality, (err, jpeg) => { }) // spec-following; quality from 0 to 1\n```\n\n### CanvasRenderingContext2D#patternQuality\n\n> ```ts\n> context.patternQuality: 'fast'|'good'|'best'|'nearest'|'bilinear'\n> ```\n\nDefaults to `'good'`. Affects pattern (gradient, image, etc.) rendering quality.\n\n### CanvasRenderingContext2D#quality\n\n> ```ts\n> context.quality: 'fast'|'good'|'best'|'nearest'|'bilinear'\n> ```\n\nDefaults to `'good'`. Like `patternQuality`, but applies to transformations affecting more than just patterns.\n\n### CanvasRenderingContext2D#textDrawingMode\n\n> ```ts\n> context.textDrawingMode: 'path'|'glyph'\n> ```\n\nDefaults to `'path'`. The effect depends on the canvas type:\n\n* **Standard (image)** `glyph` and `path` both result in rasterized text. Glyph mode is faster than `path`, but may result in lower-quality text, especially when rotated or translated.\n\n* **PDF** `glyph` will embed text instead of paths into the PDF. This is faster to encode, faster to open with PDF viewers, yields a smaller file size and makes the text selectable. The subset of the font needed to render the glyphs will be embedded in the PDF. This is usually the mode you want to use with PDF canvases.\n\n* **SVG** `glyph` does *not* cause `<text>` elements to be produced as one might expect ([cairo bug](https://gitlab.freedesktop.org/cairo/cairo/issues/253)). Rather, `glyph` will create a `<defs>` section with a `<symbol>` for each glyph, then those glyphs be reused via `<use>` elements. `path` mode creates a `<path>` element for each text string. `glyph` mode is faster and yields a smaller file size.\n\nIn `glyph` mode, `ctx.strokeText()` and `ctx.fillText()` behave the same (aside from using the stroke and fill style, respectively).\n\nThis property is tracked as part of the canvas state in save/restore.\n\n### CanvasRenderingContext2D#globalCompositeOperation = 'saturate'\n\nIn addition to all of the standard global composite operations defined by the Canvas specification, the ['saturate'](https://www.cairographics.org/operators/#saturate) operation is also available.\n\n### CanvasRenderingContext2D#antialias\n\n> ```ts\n> context.antialias: 'default'|'none'|'gray'|'subpixel'\n> ```\n\nSets the anti-aliasing mode.\n\n## PDF Output Support\n\nnode-canvas can create PDF documents instead of images. The canvas type must be set when creating the canvas as follows:\n\n```js\nconst canvas = createCanvas(200, 500, 'pdf')\n```\n\nAn additional method `.addPage()` is then available to create multiple page PDFs:\n\n```js\n// On first page\nctx.font = '22px Helvetica'\nctx.fillText('Hello World', 50, 80)\n\nctx.addPage()\n// Now on second page\nctx.font = '22px Helvetica'\nctx.fillText('Hello World 2', 50, 80)\n\ncanvas.toBuffer() // returns a PDF file\ncanvas.createPDFStream() // returns a ReadableStream that emits a PDF\n// With optional document metadata (requires Cairo 1.16.0):\ncanvas.toBuffer('application/pdf', {\n  title: 'my picture',\n  keywords: 'node.js demo cairo',\n  creationDate: new Date()\n})\n```\n\nIt is also possible to create pages with different sizes by passing `width` and `height` to the `.addPage()` method:\n\n```js\nctx.font = '22px Helvetica'\nctx.fillText('Hello World', 50, 80)\nctx.addPage(400, 800)\n\nctx.fillText('Hello World 2', 50, 80)\n```\n\nIt is possible to add hyperlinks using `.beginTag()` and `.endTag()`:\n\n```js\nctx.beginTag('Link', \"uri='https://google.com'\")\nctx.font = '22px Helvetica'\nctx.fillText('Hello World', 50, 80)\nctx.endTag('Link')\n```\n\nOr with a defined rectangle:\n\n```js\nctx.beginTag('Link', \"uri='https://google.com' rect=[50 80 100 20]\")\nctx.endTag('Link')\n```\n\nNote that the syntax for attributes is unique to Cairo. See [cairo_tag_begin](https://www.cairographics.org/manual/cairo-Tags-and-Links.html#cairo-tag-begin) for the full documentation.\n\nYou can create areas on the canvas using the \"cairo.dest\" tag, and then link to them using the \"Link\" tag with the `dest=` attribute. You can also define PDF structure for accessibility by using tag names like \"P\", \"H1\", and \"TABLE\". The standard tags are defined in §14.8.4 of the [PDF 1.7](https://opensource.adobe.com/dc-acrobat-sdk-docs/pdfstandards/PDF32000_2008.pdf) specification.\n\nSee also:\n\n* [Image#dataMode](#imagedatamode) for embedding JPEGs in PDFs\n* [Canvas#createPDFStream()](#canvascreatepdfstream) for creating PDF streams\n* [CanvasRenderingContext2D#textDrawingMode](#canvasrenderingcontext2dtextdrawingmode)\n  for embedding text instead of paths\n\n## SVG Output Support\n\nnode-canvas can create SVG documents instead of images. The canvas type must be set when creating the canvas as follows:\n\n```js\nconst canvas = createCanvas(200, 500, 'svg')\n// Use the normal primitives.\nfs.writeFileSync('out.svg', canvas.toBuffer())\n```\n\n## SVG Image Support\n\nIf librsvg is available when node-canvas is installed, node-canvas can render SVG images to your canvas context. This currently works by rasterizing the SVG image (i.e. drawing an SVG image to an SVG canvas will not preserve the SVG data).\n\n```js\nconst img = new Image()\nimg.onload = () => ctx.drawImage(img, 0, 0)\nimg.onerror = err => { throw err }\nimg.src = './example.svg'\n```\n\n## Image pixel formats (experimental)\n\nnode-canvas has experimental support for additional pixel formats, roughly following the [Canvas color space proposal](https://github.com/WICG/canvas-color-space/blob/master/CanvasColorSpaceProposal.md).\n\n```js\nconst canvas = createCanvas(200, 200)\nconst ctx = canvas.getContext('2d', { pixelFormat: 'A8' })\n```\n\nBy default, canvases are created in the `RGBA32` format, which corresponds to the native HTML Canvas behavior. Each pixel is 32 bits. The JavaScript APIs that involve pixel data (`getImageData`, `putImageData`) store the colors in the order {red, green, blue, alpha} without alpha pre-multiplication. (The C++ API stores the colors in the order {alpha, red, green, blue} in native-[endian](https://en.wikipedia.org/wiki/Endianness) ordering, with alpha pre-multiplication.)\n\nThese additional pixel formats have experimental support:\n\n* `RGB24` Like `RGBA32`, but the 8 alpha bits are always opaque. This format is always used if the `alpha` context attribute is set to false (i.e. `canvas.getContext('2d', {alpha: false})`). This format can be faster than `RGBA32` because transparency does not need to be calculated.\n* `A8` Each pixel is 8 bits. This format can either be used for creating grayscale images (treating each byte as an alpha value), or for creating indexed PNGs (treating each byte as a palette index) (see [the example using alpha values with `fillStyle`](examples/indexed-png-alpha.js) and [the example using `imageData`](examples/indexed-png-image-data.js)).\n* `RGB16_565` Each pixel is 16 bits, with red in the upper 5 bits, green in the middle 6 bits, and blue in the lower 5 bits, in native platform endianness. Some hardware devices and frame buffers use this format. Note that PNG does not support this format; when creating a PNG, the image will be converted to 24-bit RGB. This format is thus suboptimal for generating PNGs. `ImageData` instances for this mode use a `Uint16Array` instead of a `Uint8ClampedArray`.\n* `A1` Each pixel is 1 bit, and pixels are packed together into 32-bit quantities. The ordering of the bits matches the endianness of the\n  platform: on a little-endian machine, the first pixel is the least-significant bit. This format can be used for creating single-color images. *Support for this format is incomplete, see note below.*\n* `RGB30` Each pixel is 30 bits, with red in the upper 10, green in the middle 10, and blue in the lower 10. (Requires Cairo 1.12 or later.) *Support for this format is incomplete, see note below.*\n\nNotes and caveats:\n\n* Using a non-default format can affect the behavior of APIs that involve pixel data:\n\n  * `context2d.createImageData` The size of the array returned depends on the number of bit per pixel for the underlying image data format, per the above descriptions.\n  * `context2d.getImageData` The format of the array returned depends on the underlying image mode, per the above descriptions. Be aware of platform endianness, which can be determined using node.js's [`os.endianness()`](https://nodejs.org/api/os.html#os_os_endianness)\n    function.\n  * `context2d.putImageData` As above.\n\n* `A1` and `RGB30` do not yet support `getImageData` or `putImageData`. Have a use case and/or opinion on working with these formats? Open an issue and let us know! (See #935.)\n\n* `A1`, `A8`, `RGB30` and `RGB16_565` with shadow blurs may crash or not render properly.\n\n* The `ImageData(width, height)` and `ImageData(Uint8ClampedArray, width)` constructors assume 4 bytes per pixel. To create an `ImageData` instance with a different number of bytes per pixel, use `new ImageData(new Uint8ClampedArray(size), width, height)` or `new ImageData(new Uint16ClampedArray(size), width, height)`.\n\n## Testing\n\nFirst make sure you've built the latest version. Get all the deps you need (see [compiling](#compiling) above), and run:\n\n```\nnpm install --build-from-source\n```\n\nFor visual tests: `npm run test-server` and point your browser to http://localhost:4000.\n\nFor unit tests: `npm run test`.\n\n## Benchmarks\n\nBenchmarks live in the `benchmarks` directory.\n\n## Examples\n\nExamples line in the `examples` directory. Most produce a png image of the same name, and others such as *live-clock.js* launch an HTTP server to be viewed in the browser.\n\n## Original Authors\n\n  - TJ Holowaychuk ([tj](http://github.com/tj))\n  - Nathan Rajlich ([TooTallNate](http://github.com/TooTallNate))\n  - Rod Vagg ([rvagg](http://github.com/rvagg))\n  - Juriy Zaytsev ([kangax](http://github.com/kangax))\n\n## License\n\n### node-canvas\n\n(The MIT License)\n\nCopyright (c) 2010 LearnBoost, and contributors &lt;<EMAIL>&gt;\n\nCopyright (c) 2014 Automattic, Inc and contributors &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the 'Software'), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n### BMP parser\n\nSee [license](src/bmp/LICENSE.md)\n", "readmeFilename": "Readme.md", "users": {"285858315": true, "nfd": true, "andr": true, "bret": true, "dodo": true, "edel": true, "foto": true, "kael": true, "ryan": true, "sbrl": true, "conzi": true, "fedor": true, "flozz": true, "jniac": true, "kktam": true, "linus": true, "sky3r": true, "stany": true, "subso": true, "taqa8": true, "wujr5": true, "xrush": true, "456wyc": true, "bumsuk": true, "calary": true, "js3692": true, "krot47": true, "leakon": true, "lonjoy": true, "louist": true, "luoyjx": true, "pachet": true, "phrogz": true, "solodu": true, "songxc": true, "summer": true, "chengen": true, "chriszs": true, "hagb4rd": true, "jez9999": true, "khai96_": true, "megawac": true, "nicomee": true, "nilz3ro": true, "sopepos": true, "stipsan": true, "tsxuehu": true, "vboctor": true, "xngiser": true, "zenrumi": true, "26medias": true, "ahvonenj": true, "coalesce": true, "elmarini": true, "erikvold": true, "faraoman": true, "haperlot": true, "heyderpd": true, "jklassen": true, "licg9999": true, "liwenyao": true, "newmedia": true, "patrickb": true, "sedmonds": true, "xiaochao": true, "xueboren": true, "zuojiang": true, "0x9r3ydu5": true, "alexxnica": true, "apexearth": true, "chrisyipw": true, "codeshrew": true, "devonning": true, "fgribreau": true, "fmakareev": true, "gotmorris": true, "guzgarcia": true, "heraklion": true, "joaocunha": true, "johnnychq": true, "momepukku": true, "nice_body": true, "nickeljew": true, "ninozhang": true, "rgraves90": true, "rubiadias": true, "shakakira": true, "sternelee": true, "zhongyuan": true, "charmander": true, "drmrbrewer": true, "henrytseng": true, "jpfilevich": true, "nate-river": true, "rocket0191": true, "shuoshubao": true, "tomdanvers": true, "cycomachead": true, "fengmiaosen": true, "knownasilya": true, "leelee.echo": true, "zhangskills": true, "davidberneda": true, "mcwhittemore": true, "rethinkflash": true, "sunshine1988": true, "themiddleman": true, "zhangyaochun": true, "chinawolf_wyp": true, "diglididudeng": true, "markthethomas": true, "not-found-404": true, "program247365": true, "sakthiifnotec": true, "shrimpseaweed": true, "classicoldsong": true, "brandonpapworth": true, "omkar.sheral.1989": true, "adrian.arroyocalle": true, "chirag_purohit71085": true}}