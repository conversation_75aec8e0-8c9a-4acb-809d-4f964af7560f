{"_id": "postcss-nested", "_rev": "58-70cbfe58712371e33c5c0e6d1e9f77f5", "name": "postcss-nested", "dist-tags": {"latest": "7.0.2"}, "versions": {"0.1.0": {"name": "postcss-nested", "version": "0.1.0", "keywords": ["postcss", "sass", "css", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "024f7a61cec1df3cea3eaa540a2c16e5453a62b3", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.1.0.tgz", "integrity": "sha512-SBhPUx5Q5ZDYAVNg6xjELYec2MXZExLSLNsBFW5MDsXqAu2Sv3dxyh7PQZCQDAPj5j1ZAmA+lzFIiY4i3Okt7g==", "signatures": [{"sig": "MEYCIQCOtvpxo9Cf2Y71tkcj2xbdE2ko2g3kzyeauEQitDSRDgIhAMnki0DHzBof0X79ZVXoVrYTMAlLy56ViHcsXNAoOlI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "024f7a61cec1df3cea3eaa540a2c16e5453a62b3", "gitHead": "374c234479c97a3f8e5447392d08c042fa97d329", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "PostCSS plugin to unwrap nested rules like it Sass does", "directories": {}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.0.1", "postcss": "3.0.0", "gulp-mocha": "1.1.1", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "peerDependencies": {"postcss": "^3.0.0"}}, "0.2.0": {"name": "postcss-nested", "version": "0.2.0", "keywords": ["postcss", "sass", "css", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "dfb320a97b797307861e5eef4defeec9ca307b1f", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.2.0.tgz", "integrity": "sha512-j9gfRLNIMuLI3FBBJhnRtgXiJQtGaA/0MjGVXkIvKwnudcQci3DX9YOTtakKpvvp8elJSmS2DmRcDfhKAfCKxA==", "signatures": [{"sig": "MEUCIQDdJ4dQMMw+lFJSOxLvMf5m22nAxyotugUFtBLO7/gspwIgST/UxRSOpQWQMI6obL93R3XELoADuZhlCRkeyN2i17A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "dfb320a97b797307861e5eef4defeec9ca307b1f", "gitHead": "11f8c3f405700faa5d06982e354d6b46075aa100", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "PostCSS plugin to unwrap nested rules like it Sass does", "directories": {}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "postcss": "4.0.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "peerDependencies": {"postcss": "^4.0.0"}}, "0.2.1": {"name": "postcss-nested", "version": "0.2.1", "keywords": ["postcss", "sass", "css", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.2.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "485fe7e073e59216bb079a24b12b169a55b1a9ee", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.2.1.tgz", "integrity": "sha512-5uNHPrr0xtyPj2UGHiOGe+xcrtFsWzWLiqRvSndv7sZobJkmWvR3tMUYr6B03d5BTvamlXJxhFs2vvWe5wbYFQ==", "signatures": [{"sig": "MEQCIHcEViwiJrVFLGkqB3XiR5l11MCQPoZiTeSn96YoY2ZfAiBda50x0gENL3Gw/H3oAQalQYGRKl/efq8rLTaP52apRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "485fe7e073e59216bb079a24b12b169a55b1a9ee", "gitHead": "74a157afdc40ac7ccdafe03569a826d0bc56de78", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "postcss": "4.0.3", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.1", "jshint-stylish": "1.0.0"}, "peerDependencies": {"postcss": "^4.0.0"}}, "0.2.2": {"name": "postcss-nested", "version": "0.2.2", "keywords": ["postcss", "sass", "css", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.2.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "9d4b4a071df876d3d46440adb04be183d2022616", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.2.2.tgz", "integrity": "sha512-HQGG28YEJ/bz8EhS4LDE2wPYp1MwL/UgCjxwRXNv8NidsYp4obzUHG15MxTDkwB8XUGaGW42SiqFIwnEWMD4jQ==", "signatures": [{"sig": "MEQCICNwuVNO/XnuoaUqhKpT1724MdwM1FAdPS30hSOnezmsAiAF4YQod9F30eFsAfEaQ/EiaDPrw1zchWiFWg/9RQaxiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9d4b4a071df876d3d46440adb04be183d2022616", "gitHead": "0174644f3df9fb9a3cfc68d8f19376f0a554a9b8", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.6.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "1.3.0", "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "postcss": "4.0.3", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.2", "jshint-stylish": "1.0.0"}, "peerDependencies": {"postcss": "^4.0.0"}}, "0.3.0": {"name": "postcss-nested", "version": "0.3.0", "keywords": ["postcss", "sass", "css", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.3.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "796e0737584a91e765d3710e8b291e6e85414c7a", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.3.0.tgz", "integrity": "sha512-/LyC00HGvg0cma4P4FJL1h8WZIaQbUqgY0Q4XquVXbCC+EKOaXOJhSEIriaWMh0DY/S3b0+BY5ASidJPGpHTOw==", "signatures": [{"sig": "MEUCIQDZnKHmNm0wSIkzq0zB57AAAXN/6/TBMonTw3oCWKjj5AIgStaCUzDui06mcgMlqT7+bOQPC/X4K3fJ0YhJDTq9wYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "796e0737584a91e765d3710e8b291e6e85414c7a", "gitHead": "8f4570946b180451fc3e6e37678f731cf8b09e5e", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"postcss": "^4.1.9"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "gulp-mocha": "2.0.1", "gulp-eslint": "0.11.1"}}, "0.3.1": {"name": "postcss-nested", "version": "0.3.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.3.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "f995a243d089c9c42170b249abb3438f69cfcc94", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.3.1.tgz", "integrity": "sha512-jQrDrC8E8F/UIIDnVtyX7NOU/CHD5YNPfxDvZfjs8u23NqssinvJRgj/2fF4Xsb0e2thvHI+Hnx1kbQhSA/+4A==", "signatures": [{"sig": "MEUCIQCRZghynXA3IBF86W6SdX1B0YvvKaP7Be6dWtT0TlDHGAIgFV5LAyGUec55842dy2wsamhIJZgOPGyX+zcAfYpPCqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f995a243d089c9c42170b249abb3438f69cfcc94", "gitHead": "3625c60c9bd4e0127d9f088d96e7587f397da30e", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"postcss": "^4.1.9"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "gulp-mocha": "2.0.1", "gulp-eslint": "0.11.1"}}, "0.3.2": {"name": "postcss-nested", "version": "0.3.2", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@0.3.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "9cae9b54b0a1824cbfa0c13699d4f21afc3b0e3d", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-0.3.2.tgz", "integrity": "sha512-vK7OrD5S6XRb5tl5RUjBWx5BgA0DQNikzW2PzSdcPLSoJPB+rkY3QSMXxrKl/BmaI0Np4e6gMbSUYr0ROJoqNg==", "signatures": [{"sig": "MEYCIQDDQPcxUBCSbdGWxzB/sxchyeUn4VkArhpJoXaEedsFqAIhAMWd1DE0DpbDTxzJjzxLeXXd37KVkTXth5Xnmq7qcUsd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9cae9b54b0a1824cbfa0c13699d4f21afc3b0e3d", "gitHead": "4c1d6c6fff7d4811cf3239043bc4baa0a8a8910c", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "2.1.0", "dependencies": {"postcss": "^4.1.11"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.5", "gulp-mocha": "2.1.0", "gulp-eslint": "0.12.0"}}, "1.0.0": {"name": "postcss-nested", "version": "1.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@1.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "d136bd4b576bd5632df142c12b2198a9ccf794df", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-1.0.0.tgz", "integrity": "sha512-E+crh9RObEIZPSwSeEC6JuLemSPE23e+4Xif7M5af/qduPmjafP86gopiTYjavOtlmgtZ2BTA9YGb4Py9khcsQ==", "signatures": [{"sig": "MEUCIQC8f1TBsW4bgLCYqxEQCj6ItvVAGowj8g61o+hkwj0iDwIgKrM78hLYWRqOfJ3+DTNc+6IEaFyb8jzF4hWZG11epQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d136bd4b576bd5632df142c12b2198a9ccf794df", "gitHead": "97840af712bbb0a5bba191d56ffbee3657b12716", "scripts": {"test": "gulp"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "2.13.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "2.4.0", "dependencies": {"postcss": "^5.0.2"}, "devDependencies": {"chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.2.5", "gulp-mocha": "2.1.3", "gulp-eslint": "1.0.0"}}, "1.0.1": {"name": "postcss-nested", "version": "1.0.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@1.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "91f28f4e6e23d567241ac154558a0cfab4cc0d8f", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-1.0.1.tgz", "integrity": "sha512-PvN2sbZdhOj0HP4/UjG6EeInqrHiDBrRCbHt65y48AUyv/n8YHO9Y/m/pTsf7CJvn9jGT0yaq6KF+rz73kw+7g==", "signatures": [{"sig": "MEUCIQCfoIX0DvpF2XVVrtJCtRBAoAJXEpqYN+steZQ0k9AOJwIgAqwKR/lbBYP+xAZxKrhc92TEPTtH1AflzC5P4cyncNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "91f28f4e6e23d567241ac154558a0cfab4cc0d8f", "gitHead": "31ddb3aed9a8d95a0a28f431e594ca6f7dfa6a7a", "scripts": {"test": "jest && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "7.8.0", "dependencies": {"postcss": "^5.2.17"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^18.0.0", "eslint": "^3.12.2", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-1.0.1.tgz_1492127851413_0.15649669291451573", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "postcss-nested", "version": "2.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "7aa1a60468eff8d2856650537ca84a214e90732a", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.0.0.tgz", "integrity": "sha512-BJRcFn1rRDcQY3p+EJiDRraBZXG1kZtp23fUKH+o4BoZYP7vTNoOoLi3qc1Y/HC6YoZiA/I4qpas8GVH+KjtAQ==", "signatures": [{"sig": "MEUCIEqTHOaNjdXUJuKTobvgYaBhIXkZBVO340k3KPSxJFW3AiEA6ixr0eQgweMSqJNQGlIisuDnBQ/qYaLHHjplsB5dh4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "7aa1a60468eff8d2856650537ca84a214e90732a", "gitHead": "7f995dacfc9176747b6589c5c2ba8c458b1f4e63", "scripts": {"test": "jest && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"postcss": "^6.0.1"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.0", "eslint": "^3.12.2", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.0.0.tgz_1494254601518_0.8613410571124405", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.1": {"name": "postcss-nested", "version": "2.0.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "4b5ac964ceff501fc4675fb06b7d31548bf67cc8", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.0.1.tgz", "integrity": "sha512-qeW3jXVkcEdUA1oVnYgKhabEu6JVEx8XfA5fzLkr4FOkiVzJ/iX5RMsaX4E+dcEIHYp2VhGvIxgtEqUnOrEcVQ==", "signatures": [{"sig": "MEQCIEnEMSgvR+6YdbELfcYJtWknqJ4tn5Trjkv7k89ULw/bAiAGHskg1soEAi6ea9X055cryq+bfTdp12bHQ2k0nlcOEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "_from": ".", "_shasum": "4b5ac964ceff501fc4675fb06b7d31548bf67cc8", "gitHead": "3007c25a90ea130b6ed60f10d5f95d1ada9e98a4", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"postcss": "^6.0.1"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.0", "eslint": "^3.12.2", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.0.1.tgz_1494278250124_0.2882235355209559", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.2": {"name": "postcss-nested", "version": "2.0.2", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "f38fad547f5c3747160aec3bb34745819252974a", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.0.2.tgz", "integrity": "sha512-GAnfmRwBDFE6LZt37EXjngAbFP7hWfB7UYYoRlBn1UK0kx1BZmDcZswSqnVLmDGp2adqDhNMTMPHOn+sUeZAug==", "signatures": [{"sig": "MEYCIQCDQnoH6aomt4i52yyrislNGFlq/mVC6ySeJBHuUsNTBAIhAMQxOIPlz5DRuoJ/Un+XQTHoeTxRURYm0iAPOUffNq74", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "_from": ".", "_shasum": "f38fad547f5c3747160aec3bb34745819252974a", "gitHead": "eaaf63e088366d32eaf61411ed8cf6af9d5e1388", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"postcss": "^6.0.1"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.0", "eslint": "^3.12.2", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.0.2.tgz_1494342291874_0.6787852696143091", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.3": {"name": "postcss-nested", "version": "2.0.3", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "7ee9dc547e02bc7e4f71dccff12ad4ef4c0f1888", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.0.3.tgz", "integrity": "sha512-pRvY835jQyJMbU6WQORM0Q5tVtw3yznCDyAA5LwTPQWR/CdyFa8dPlSQlnp5smgB8etcHRqmGWjjV7xBXlaXlw==", "signatures": [{"sig": "MEQCIF/eqsf0eYplClOOPTURKL/WUYtEmfLu0ICTZeAbKXMeAiAW4sdgjF5Xt5YwuCAsevfsMGJxyZWN5ARZ1aH4AzKqHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "_from": ".", "_shasum": "7ee9dc547e02bc7e4f71dccff12ad4ef4c0f1888", "gitHead": "fb17e40c18abb0d909640bf140abcc9fc29d7a2c", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"postcss": "^6.0.6", "postcss-selector-parser": "^2.2.3"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.2.0", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.0.3.tgz_1499964599743_0.3124022160191089", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "postcss-nested", "version": "2.0.4", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.0.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "f2becb7c67b9a94fc43b59e4ef194c13d4349027", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.0.4.tgz", "integrity": "sha512-ya9pP80RDLVJqMHXggHL2um/ak08mcMSQ2Wx656LnrYnak3FtujNFuv6LngaNlbBvx2sOhgcu+A03rBvwVSvtQ==", "signatures": [{"sig": "MEYCIQCL/1uYTDlpUIglhfdouWge5MWSEyAP9IKpRPOX8XW+MgIhAOKa+PglnzYF+yskErv2oVbF8QmMr7aVWT0F1VXAdQXy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "_from": ".", "_shasum": "f2becb7c67b9a94fc43b59e4ef194c13d4349027", "gitHead": "bdbb199cf4b5d5fe80447b1c2f8fc3366bfa4efe", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"postcss": "^6.0.6", "postcss-selector-parser": "^2.2.3"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.2.0", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.0.4.tgz_1500088500919_0.49604854616336524", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "postcss-nested", "version": "2.1.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "72661d79463f5894a8c4b890479baec689d1c693", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.1.0.tgz", "integrity": "sha512-0DbCNWZSI8SapZkfY1Ni/3019ZQWTe3wair/5tFLzXMi8u8pNWbc+EJDq+ckakylF0WnSrKIlJAd1mG32jsE/A==", "signatures": [{"sig": "MEUCIQC5zEl5pMbGsZjR3mCupHUWr/l+SZmCV5S/ot+9bUeqrAIgRRArrhKilgohuHegVZhiBbHcGafvZGWyfBZfGT1OVd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "gitHead": "dc6fef840af2d210f2e1295da4c01a69a229d0ca", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"postcss": "^6.0.8", "postcss-selector-parser": "^2.2.3"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.3.0", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.1.0.tgz_1501013960299_0.1769633658695966", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "postcss-nested", "version": "2.1.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "58484f3cc3ed08630e051fe70ee5d31f337bad2c", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.1.1.tgz", "integrity": "sha512-MczYxHT4UbBlsMUHifH+nI7Vx3gFGeEqyz2t2l1DvTxXbelJgP1EaE5lFa8DzyM5WnrsN8kUAPcbD5fTC6U//Q==", "signatures": [{"sig": "MEUCIQDTVEaOsqdS49Gn3wERsuf5qSSLS4S3+MN7BIQ3yzcFWgIgTw+dmU8kikbyA93xbVmU6Q+FO9MM2veAPL/uVwCatUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "gitHead": "27434d2856924751ff095e429b19da652f7b0c55", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"postcss": "^6.0.9", "postcss-selector-parser": "^2.2.3"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.4.1", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.1.1.tgz_1502522989836_0.42112777032889426", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "postcss-nested", "version": "2.1.2", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@2.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "04057281f9631fef684857fb0119bae04ede03c6", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-2.1.2.tgz", "integrity": "sha512-CU7KjbFOZSNrbFwrl8+KJHTj29GjCEhL86kCKyvf+k633fc+FQA6IuhGyPze5e+a4O5d2fP7hDlMOlVDXia1Xg==", "signatures": [{"sig": "MEQCIAhHs3fbn9a/nWwRb3+1JzZmW/YDFE3NcStI7NREm8wlAiBe+L/XAzNA118kZ+09552QXeUDVAqxskn7+QMXXBMW1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "gitHead": "5c3f183bd823e8f882760b2e7364941d04ad0e04", "scripts": {"test": "jest --coverage && eslint *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "8.3.0", "dependencies": {"postcss": "^6.0.9", "postcss-selector-parser": "^2.2.3"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.4.1", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-2.1.2.tgz_1502778349201_0.17728257575072348", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "postcss-nested", "version": "3.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@3.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "cde40bd07a078565f3df72e2dc2665871c724852", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-3.0.0.tgz", "integrity": "sha512-1xxmLHSfubuUi6xZZ0zLsNoiKfk3BWQj6fkNMaBJC529wKKLcdeCxXt6KJmDLva+trNyQNwEaE/ZWMA7cve1fA==", "signatures": [{"sig": "MEYCIQC4lcZuIOxyJT3xs+axCLcHSE+GnTVa4TPPHFp03Kg/MQIhAMRvw5Wl/69Sb/XaChlLWJr52GOchHdD1z4aopG1YLpk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "gitHead": "9726b519a8afd909d821098d1967331d13da537b", "scripts": {"test": "jest --coverage && eslint-ci *.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"postcss": "^6.0.14", "postcss-selector-parser": "^3.1.1"}, "eslintConfig": {"env": {"jest": true}, "extends": "eslint-config-postcss/es5"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.13.0", "eslint-ci": "^0.1.1", "eslint-config-postcss": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested-3.0.0.tgz_1512969242669_0.2727426707278937", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "postcss-nested", "version": "4.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "c5d0f8d04b661d5b4cb041e7f8a668accafdfb4a", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-pO1O4ciVsMb0NUa0Y0PsSsNg7QsVFZmRFMdjkoX1ghmprlz70W+ykZM2RhisTNVVVpTd2ITYX12HMrXVWy2DmA==", "signatures": [{"sig": "MEQCICeq/t+8Nthk26D5EZUkxu+qHrQkYpdZs1b5CAQlhlnWAiAaV4Fi/MCLWLC9FHkPgzQqxG4tyRmOImMvIjjjpkQTMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbknLxCRA9TVsSAnZWagAAFR0P/Rjo+dWcERkwWZZ1+XJi\nblRXc9q1kzEGsgh3VdtPZB2ZcCRzTYNzu7bPG4nasNwFbRbHfZGHcm4kiD3q\nmbgsLIRjfXOnmVNRNWCSgnDaOOvafbfzRIKvHezqo7NPYwiuq4rFATFw7xx7\nzK8ExF2n3HKWj6epDvXMDgjQ/3fcBNPqHM82rrKAnBdpGe7/sy1ihpB5IO5a\nYud1tYxZm44GByyfP6Lf8iAp6CHL0cyU2ggS1HkvflNRIlB6BXKAxTNrz6HQ\nwtsNDcS2qtOiRWWtnb9WeEQJ7ABmxnMDr8iCwXDtSwFHLtKH2w0uEStRZ0H+\nyZk3U22O6s8rs8uxft3DZhvNsaEZo50g44dL3u4nv9L7+U1NZm+SM6FE1ehj\n68JLX8pjaCpc/LMj7aEkZ42TYQa3RD6oRap2EDzLrSmwvrzzgto36bQTont+\nxf86xSw+5TBTjp3k7g5lzywjVvBTEKiRQS4/d7FbJc1yNN/TCmZ7kENToakn\nL7mPIPTixVn+W7N2NcVXKTtlYpbtkJg+it6xwZN7PwEpouFNm/kDOucANEVf\nfM99TGgqCjMpRBBC+UfG86beKd/0IJDVUoVbFGSo4CBU+iXGiYB1XRHtLX7c\ncm6gv+FoYeH8Js0gT8YPKseNizvUf3qP2JDnxTNkeljH15taakMjcHsmEd0W\nbgiR\r\n=AiOx\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"postcss": "^7.0.2", "postcss-selector-parser": "^3.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.0.0_1536324336566_0.36784019710127147", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "postcss-nested", "version": "4.1.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "271da8a047f2ee378139410ae2400b1c67d0bf30", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-owY13v4s3WWTUjsT1H1Cgpa4veHjcBJ/FqbgORe1dJIKpggbFoh6ww+zUP0nzrvy7fXGihcuFhJQj3eXtaWXsw==", "signatures": [{"sig": "MEQCIBz8OSTvUag/hxb2y+JaNwanN3Jb/ZtytkW2k6BMDpFNAiB9WZt1Ma9/u0y/x755HSPBlgFfGFyuGhD12y7YydNq2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblOS5CRA9TVsSAnZWagAAPnkP+wSPeYoxW4MFGgm0Kv62\nPgtPSyXSnqHJOZWMy+dkS8BNCIM4vTjSvIvXcGpMvR0+xWZ6CoUfggsdPKX2\nLF/Ssu3YNywOH9+G918knRAzz+SBrFXFq//aIl+SAueHS22Hg6iPOJEBEqRB\n2RTBOyHxzebfE/ZBRWQr68GP7emWhQxQz8GkU8uQDaMU/rMQQI2gMUXwNhra\nwovQs0dxpPHfcehJcr9tiHfRV+FzTCyysVQ5pWRwxR/LttnFDhB9+6IkVA4x\n85ZCWdj2pT4hjmX4E2G7BbixTKSFn/M4NP5KCSwvuYiUaMCNP/fgBWyMCT5n\nJ52bRr7EUzXMboCZkzzvcfx72rgz/uQLF3H/4TTL5OSfEqw7VO7RJIM2ZpXK\nwGTO8gn/tJs4adtl9iUoW2YvQQRCjHoN+J+gMWu+GtHAzsK91W4xcAb+bR4L\nSxD5Ys4brhcwYFiYewL/VJCBrknWGvdGJMNAYvZZkPQatJMVOvj2p2BcYxR7\n8EQq/wvic7O2uX9TX3eBhAqcz54YNCPn0svSYMHDFH1aZeb6P/PArN8+dRoe\nx52KY+hf1EO8iYt3Ij7LsUIiHFqTcmouAFzSgHblJb0GOmQNb40mVy9fKsA4\n+3A4CdwnrV0tUKIehhh9AT+iVx2LyRrvnu5nD/b8XHWQnVR1DL181jw+uyOA\nh+1A\r\n=BxU1\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"postcss": "^7.0.2", "postcss-selector-parser": "^3.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.1.0_1536484536899_0.31388688364533746", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "postcss-nested", "version": "4.1.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "2074e6641583bf7151d951908d68fa95039fe340", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.1.1.tgz", "fileCount": 5, "integrity": "sha512-3+V8+g+i9zUQ/AADNtBj3DVVvSOhRCV7W8Kzn9n4ViWJtSQrSdtIJnxZaupfdTrnhCkY86sAsuKVxBCuyfJDeA==", "signatures": [{"sig": "MEUCIQDaBedfsUYZ+/T/80Zy4SWIGvvNI0TTe2+yKJSH/OaiugIgScWzc3svCl6JvFWzeYddxSGb7LPcyB7jTtclVbLVHDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFEJcCRA9TVsSAnZWagAAszEP/jjkmcXb1DMoPVdZeozg\n1TbJQtKnaObT6E9QQJTTkwIumb3rNREuUODO1XPXT03jvEdG7UArmkfquOZW\nOUdidkIzCSCY+RsTFoypW6zMWTg0dXBULPdb1Kc17mCnMf/5Cx32KHwW2Qse\n+CLd+GdbvGRLwIDNrl4680k4hC1o2X+/sS+7e2Y/5VdS+2OYwjJC2nsh3c+R\n4WKqZphaftKWELRb8NopEXJICgqp7UXE9DMQRM1g50gD5XeSyIshKbGe+dTQ\n8YeBCdDFpfxVa3d0gf0zqM9rvjZDDYtUmoRM5cdEDMcPyPqJac929/Z09eul\n9NfWoa7m/5CqjxQro6f6cs46l+5jf46VvSm7TwnTv56KZBYAzuLIzPC9Ed66\nYMlR49bfbP5HwFsM0xnoMFTCCkLBYQ0wCvwav7Ks3YdVkcoiocpZgENT/r/C\nA2VohHcGminGbsr+gU+JrUjo6EPpRAJDvP5NuO8Qv45+CpMn+wsmnkvcLxXL\nzBHn8QnOHC1sAeRe3csIluuyucKgyxwQc2nmNLsOnaTwd5Vq2YLyUNIor3a4\n1h2MOx0QB66rgX8lDau2FTDoo7aKobLX6Mlyi06cFp4TqBBi4prYFGMfCffV\nADvHqgp98LXOweht3p0Kjr6XVjIoAsLJ6wpW6HE40RHAaH1FGewKlag6S24Q\n62Vt\r\n=u/Zj\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"postcss": "^7.0.6", "postcss-selector-parser": "^5.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.1.1_1544831579891_0.7059541694988658", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "postcss-nested", "version": "4.1.2", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "8e0570f736bfb4be5136e31901bf2380b819a561", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.1.2.tgz", "fileCount": 5, "integrity": "sha512-9bQFr2TezohU3KRSu9f6sfecXmf/x6RXDedl8CHF6fyuyVW7UqgNMRdWMHZQWuFY6Xqs2NYk+Fj4Z4vSOf7PQg==", "signatures": [{"sig": "MEUCIQDuWZb8iYuxUX32/3pVcf2kCc5r2d0EVAlZ23nmhfpGKgIgSVJP8jfvmcVxjBeCYOkGA0cEcls5n1lmmZNXriVP5/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcc1fkCRA9TVsSAnZWagAAifYP/jL7jdU1L0YN6zvgvY8e\nHeeC1DelM7WYxLG4yjsh2qJSZHdsSV0Ryepr7aLrMguSEIDEwIfzirqLXIcq\nO+89W2WT8rKXLcap8x5F/gmGbUy2iR7C3cJutW7DfFPL1KPIjkrXB4I5PsMv\nsAblZTlYwXf343FAvCVTU7uv3UUlQnxF+1RnV0owbZIpxm04P1X7awZfrMZ7\njp0+Vw58wC9vgy/rR/1p14VoMdXqr1YzraoK0MNadEZDatU2hvDld8powqYT\nPAfuqTPbEoj6z4uWYHGpCASFssTPTnSLRMHJ71Bbdh9o/CNmdkEI/1doABFP\njoGIelI7PTHphdC/s+UqjdGzIhRQ65Ixvs8RBXf+R2XG/71+cOBDpgZSDc6o\ntdMQb9lmJXELQ+mE9oLsJzTJmVTFbmgZyTy2s1g94FjTfYHIroHKmlVxYvLp\n8kqx2NlJ2YrBJ03YzCy/0+19BmNCL4QJeyDgWXW9XGKibrsFKpufCK+MazsP\nMtGjY5ZiRe5tmQsba18DGDaKVPAA5s2f/aqSNftqTreb6cHRf59OzvZWYE/A\n5+a4MVcesz+Y89Psx55l0e63raT0vWJxYpxTr/S4zjV5xNG0WA6d6FCICl/q\n2jT3O5TwSgRdY8UpbUoJrGvBSLYiTJ/L/a3Zwv6254fm4d6PESKyKqHO7uLV\nNqBy\r\n=9teY\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"postcss": "^7.0.14", "postcss-selector-parser": "^5.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.1.2_1551063011367_0.8333201654951237", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "postcss-nested", "version": "4.2.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "e6e6777b05dddd47358c676d983bd7251d0e67a2", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.2.0.tgz", "fileCount": 6, "integrity": "sha512-qRAOC0uJ4TbFXlURB+3Y5sCi9+MLTkXmt7VGpqHw5oAWCnHqeFa4MGf7kb5ceFDthknumJc+E4A0TjV4XEVWBQ==", "signatures": [{"sig": "MEUCIQDBCBliBxfonc/ZRx5lVKHTIOVJ4vx1ZeV4A5+Y9sk5/AIganQYmwxApS+wZ2Yf3znsmat0mMFMhVW0dRWEl1eCY04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwfY2CRA9TVsSAnZWagAAh4YQAJmw+oT5QtPSMreCfoQ5\n9As88GkXY6plonrNKxtizxg8WS2jn8NGZUmnLNdSlq5251v84WiVz1ftPs7Q\ndFhROu/izbrjfgHxsJa2azEJWha8ZF26tTagX0ipns59m6uZnNVG72NHKFKw\nWxBX9hbncZlYFt5l+rDxxLZ9AAGd4adJMtvVZKx6TFAjIrhR3OlFVbhUTCvy\nJZmQqFaB47hDs4P65v6exizXRZ8esgM2B41UfhJx/Y+ZES/hz25g1OAMs7WP\nhcudkXomKWtPt183jVRTHQoeZm/9BUqC+ejbueWREncy+TmbJcApLq6XzB6L\nWIVCr1XPYl/NQIyvJfnmY8BP2P8wgYTIrOlb2DS4eQfVUvkl6N/3glcuti0H\nVpddbAFNgjB9CkyLudePR04IQog3AzW6xbz+V+hNxcbPfCHpXJgrBQ9FvcJ+\niZgk+bHKYE7sFuM88/onURnG55QV3rkk1vSkWK1EY627KR2LFT5Oc5T46zkL\nUDLmoBpHqowk2rMhWbJp6f5wsO2FcchRjXORskIYkCmaHR+fPi8VzoNx2Dvh\ng4M0GUr+fcgQHaJcJhjSmaZHaLnuNk4UaOX8Xqnh9BxyN0S9ZTqGMnDrH+Qv\nO60nnNtm17pQ3JM1CF5U2ub8SMLXM9ReBLhwg/Lovtd1iNN3SMh+swa4Gdes\n4OiH\r\n=Faqw\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"postcss": "^7.0.21", "postcss-selector-parser": "^6.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.2.0_1572992565527_0.08766520896519481", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "postcss-nested", "version": "4.2.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.2.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "4bc2e5b35e3b1e481ff81e23b700da7f82a8b248", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.2.1.tgz", "fileCount": 6, "integrity": "sha512-AMayXX8tS0HCp4O4lolp4ygj9wBn32DJWXvG6gCv+ZvJrEa00GUxJcJEEzMh87BIe6FrWdYkpR2cuyqHKrxmXw==", "signatures": [{"sig": "MEUCIBVlJK87uVOw6p9ewwmuHEh4RNBp5934YUIspl2sCiNcAiEAvvcTdxv5jj78lJbu4eRKd4L1kHlTwZbg2W6ma19wskQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwwX4CRA9TVsSAnZWagAAZh4P/3MgpLsy1rOnTk3BI5Tk\n7szgik306TT4i4NkhfZx5vduFeReCyRfU9Qgm5kVCGOokdqzbl69zm7zNWpB\nJUQuI2aOgNTSp1b4usQkddKuTWc4AU4XzScL/k1smwKJs++ctmotcrQqKi5a\nxBHJG75dHSzpjCy6fL/O5z+IfuwBQjrB+adACV1Y4o9w8lqQtFjPch5stM8D\nRHZicUXKoGdt4aMR/T334XZfMOAFpDIJj2hAQGbSHR13kwkosAfXeEMBUbjX\nQBGX1GudblXZLqUc5bYkyMJkH/HjIaHa28Nu/2RyaqULssJiY2t7hKU1HUj5\nuGxkwZZaNv1ljjGAYMDBqYoKR9TsJeyE6QtTpL9q+ZW5EYCMjYCE7TEVG3M4\nglZKFMok0jW5BHL+8q+9uG9CkxDZnhMj+fknl/0NLlKcH2+ZbtWzAiIAANxL\nDUfsnuap3lt9kPG2XEQ93hImxGgAMIkig79sikeHYCQHKrYBr91cf8JvjvJp\nQL88mFuMdaUC8wEUQYVmjrIS7cNvR33Rx4fe5tzIg2ko1KoUZk44/ZkmuOi2\niXBTAn7EnNQ/lNEhbAA9X8tWtMVZ9bdif7mnr+3TE55qwHPRe45rDSY6d+nh\n8CDjXzGfdxaT5m0ZOlpscTHRYFs8BMjACYUXErtj4mJ6tJMQZGquYHVd54WA\nNiet\r\n=HfhZ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"postcss": "^7.0.21", "postcss-selector-parser": "^6.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.2.1_1573062135948_0.09156004276282559", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "postcss-nested", "version": "4.2.2", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.2.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "68108107f9d49a2a8587625fc8ad2f67eec6dc5b", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.2.2.tgz", "fileCount": 6, "integrity": "sha512-KivGs+ikQlX8VvR9pbaNA/eVmnCN9WcvD8sO9gPqgy6Q6teOH9NqbHHv+czcVJwbBtIdcq/lCzsVgK9daNrhDQ==", "signatures": [{"sig": "MEQCICywWaUTF4IQ7HjhBiSA4p0uwleSko7BtGhE/yypni19AiB6sjOAvYzjz20czpq94YkCFuo9QLsTOGb0Cqf34C5MBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAN5jCRA9TVsSAnZWagAAdrAP/03IAkocpYr9mQya0fJN\nvSMjf6h4ex4x2cCLkHJEHpQHgh4VgB+nvzvXrans67txSH2ynV1ry7DybxDH\nY9793ozqkYx+mpDdv5D0Ez2u4Z1QdLCEVQLJufgrPXDOwrEOc+0CM9eXpRuv\nG6WcObsggod0Z8aQ1fw89ayt9ifJAS+lJm8LH3xLw9ISDUPEoIoCYc7rJFEs\nrFYTr8Mgy71DagAJp7h/HuIKHDXMAUXmXZnqZLqNWczehljiM4pXsfUIjY0T\nxHFLBhbUeihr06tM+ucU4xUzzqxDmD0cwIrQnBZvo2dD2WxJxSZf6IxPdxSc\nfouHWgeaiArnG1jR/Khznn+RR6bIno55RqhCF9pxPzWwFpbl8piHefIUanK1\nUD2AOAJKOOj6pVJT1R1TtCykzsCbovsU92mK8VcxMuMbLh4tVWFvvh7pM0Od\n8akcG1jXWy9aEMiH5h8QvJ4v9gAdSf+y7piEAw2/ekoS2I83+tJ8I4nfxb3K\ngQqfIkxtMv52PNfyFBObkvpAuKumeGFb/syOiovyyrmBKMsfXJh75ByGWHcA\nVIZa5pJve8S71Y6dppOpg010QvtReR2JHZNwtJbl81zO2Ix25CzdWeRkFk1a\nMz/Lk6A+h8F9xOEinF8hdp5/M9YY87uXhpXFkyxYSeBnXoJBMpeIqy1iNN8A\nkV99\r\n=mkJt\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"postcss": "^7.0.32", "postcss-selector-parser": "^6.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.2.2_1593892450813_0.5071002400761937", "host": "s3://npm-registry-packages"}}, "4.2.3": {"name": "postcss-nested", "version": "4.2.3", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@4.2.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "c6f255b0a720549776d220d00c4b70cd244136f6", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-4.2.3.tgz", "fileCount": 6, "integrity": "sha512-rOv0W1HquRCamWy2kFl3QazJMMe1ku6rCFoAAH+9AcxdbpDeBr6k968MLWuLjvjMcGEip01ak09hKOEgpK9hvw==", "signatures": [{"sig": "MEUCIDUZ/aaJAdYvR6UcdWH2vMOT59yDdQI+JFBiMqbr4BP0AiEAz9ytwZh8DjOMKPy+DEOXK4ppGlDAwsMJuigNTY0xdFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfB4a5CRA9TVsSAnZWagAAYAEQAJA/UQP7ggUpQv7BIGS9\n/zePBKWDRX4Vc91GrI8+YcmU2TYxQR1br6Y4qefGhD+Xj6AnLt04rUcFRdmD\nwD1rQExX04EiIj+jcm63iLDWND5t+y0+PyTcSqoy/ptL66Asef9wFLZpEv8z\nwIZbCxZB9VANZSrLrIvArSR86gf8XLHdZ74Rl6hrrH4l1pUtfKewfhGMRzne\nniZNhqXU/TbjBbW0HcS7AIvcvV87sg162Qv6vhk8ZkHQahnB7d3LVv9deM7x\n8DhibNKopkpLuIaYTlNoB+jhKCLmRGkx2uP9HY9rCHB7g6hhQdVbB5+Uojqx\nyZcKv8VxwVkcM/IVk+fEX/duV+YagncQWMkodyfe8ktG9aCkqyFriKZOGqbS\nvjjauAkdF4AXQeP0T05F/kM4BiO+30wpzNUP6PFVX8ClASP+Z2HoT7nQvmEM\nE1D36tegWKAvdi7YCql6XQSP2GW/Dk8frC9vRTo2HlCxoSfYCaWVMpu4BBVe\nFUpx7cYdLDa61zkqeKSmSOee9KUtnBaprf2lQr27d9F4BML+VUrF1t5WybGf\nJRfXUwHeDxVbRXFAqdbMYdYF/vy5SccU72AHJk6bI7sW3x9swAw5Vnyeig3U\nE4rwXxhRsxFHccTbZ4GQi0rWQ1DU2R7DE0rGArYP1Ec4mjcl+NOXnBDs0Rs9\nVz72\r\n=gJLx\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"postcss": "^7.0.32", "postcss-selector-parser": "^6.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_4.2.3_1594328760352_0.17701463723574662", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "postcss-nested", "version": "5.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "0d05bb93578ec42919bdbda7d4eb419d5f58fe07", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-jQVUwMOkDJPHyY1o3bxLErHnxIFsO3yDgdDsL/rjSkUHrWNdtNGhP50O0P+vUjEwPo+ekCRhLXTe8TziARoS0w==", "signatures": [{"sig": "MEYCIQDXkSsdKOHGX6jfivqj4fPTY0GFhy8T6QuzXbkqVJ5tswIhALdFPUz28Crog/yQISfLk9kEGpsh5MuER9OxpYDW3lkS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYRhxCRA9TVsSAnZWagAAsDgQAJ407JF8SMhUJYUdec4k\nZTSOsFNtXv0M+0H1oGiTSKEzIzPxCjue+TAtjR9ZWBpEQNArfxLoVVLwBffn\n08Ed9lpcohTiMfeT+hQKiVQIsZ7koe3TZ3/BmEe+3Z6X2OhSYjDQAcMURPNt\nZQL52qFFWzC7UmSzooHi5XQ6tQB+tSkRsKPME6fsKb+LYXHy1qFx+BAXIl7/\nEG7ACmM1zHKR0D/LRzTWp8YQ/SIIusrlfzM9Pdr6kOjeSueW2esPvhejN/zh\nukvnM7nd41zG2Udg18WK68YW01Tzz24L7hKWaymlmi/CzJX/ipfYSEInwZCu\nNsiciOqN2tElz9QvNButUHG2GTcLMMgbZQLf2X+o92HrATKGIt7R+IRJmJm1\njXvZA8hi+4nKm9q9YtVfHeS798HD4yYcpLWOVBSnb/4mxC+FyBcio0tWhjoM\nfbNlxbbhbQ8MYhqtMyYkKWKa83Il/SkPCzowL2jEAcLdhJKGI4Qs6DtydFlW\nKqbWRHNQVHIag7GsjXSTHaXVS3p+toWr3Pr590OsYUFyf7udE8vfAQosZ9xe\nliu8bYvKNnG7d/ToR/7NdOKkezkDKGeEmC9kjTox9vqD5DWlmDsknp2undth\naqnY2Lqi0+FfFY3ALl7K31KPYYvwB1nfG4B2Uya1+ZVd8o+sr2LnwAMMhVMW\nIvaW\r\n=40BJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "14.10.1", "dependencies": {"postcss-selector-parser": "^6.0.2"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.0_1600198768727_0.7316421015220007", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "postcss-nested", "version": "5.0.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "e7a77f7a806a09c8de0f2c163d8e3d09f00f3139", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.1.tgz", "fileCount": 6, "integrity": "sha512-ZHNSAoHrMtbEzjq+Qs4R0gHijpXc6F1YUv4TGmGaz7rtfMvVJBbu5hMOH+CrhEaljQpEmx5N/P8i1pXTkbVAmg==", "signatures": [{"sig": "MEUCIGc9k4Q0TsJ32ahDB2rl6krD5NbJzs5SpMcSleZ4QzfZAiEAzyosg+xgU53UUoh8brRcF+SoTdQS1Ml7exnvbcpPkvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb+IACRA9TVsSAnZWagAAEc0QAJjXmalApFZkz+Zbam8S\nlIISM+r7UuWl0SHKyvYMqJZg/MS4wwogeCYGfr5JUyV4oJXuegRovM60NZKH\n9dx3SMwjJwphakkpQusx7TI479Tkk/19fTIGBM0fT5WGC0AOlDT9daI7bUxQ\n1nY0mz9g4A+7Q485TdzuMX88QI/uKErDqsoVaYBT3zD/u1+9lg0IP7sM88hn\nO4wVqX9PBYLxB4O7XYKVgD8e4g2Da8uqnA2XUoMCX/cO6Cl0z7yRVS952xyM\nX0nzwV96WqHJ4RT/UVD/fFwClXW4sGNM10bMuq3ElESSK/4b4WIU5t3nI+nv\nMJ2PBfnL1+bS934hE4n+3Ju+3hOYwFLHAtlcnkeQATgyRttP2Ko9rY/yumQ4\nQL1qJxcEqT8wcEkcGmDrVJ8tzPfTq/f05oYo0Yq/vVSZdFyxU+DDL0H2XGBV\nXKMtYD97y4uT5Y2+YDRWue+ebFsLT9G2OFy5BQ1P88nsiaaLmk8nuoTESwZi\nXJvVKS4dpV6RT0/pjYnTYewLWHRw9I3nHxnNUJhceXPWDuGXNWKZFb5qr1EQ\nHzQJD+ynVa65fbL1LbYk9vr6RW3dkrSwhAU/D8I3j9kOVEhn8JdynSyuznn2\nynomCKybhLJunIIztKVEtbw8y/8UDBD/UVSpeE1D8Dlq+l7ScLS8XDO93Oz4\nz3lK\r\n=oHr3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.1_1601167871626_0.7843121145038436", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "postcss-nested", "version": "5.0.2", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "0a5b3865c86435bf0e0bf26d05ac53ba5b9597b9", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.2.tgz", "fileCount": 6, "integrity": "sha512-ddUGffzocy13jyKzl172kd7IY3puSbGLYDh2UgryYoQWHcpHkOecR6lDnIMgnu+TRVOPajIBqIX9si8a2oRfNg==", "signatures": [{"sig": "MEUCIG/1Y2PJiIYpyLyItEoxPHpDZ6KAEMb5P0Ug8jdtEUyVAiEAuj5HoOrcnCOXYEuvTvyF/DisvSB0vX8X7K8fuNkZyQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyFoACRA9TVsSAnZWagAAWLkP/j0/PrAbnEJRP30cr7qW\nGXPCQbNu40m18TtBnho92wSfkK0QImq4H2WuKY6264tgK5KzLm6BBlP+ooNU\nyN8/FluxPsn5HY8HwAFFw1ZFju8j/zppVL8dQkiVqdloq1lsM6NW7G6qCnCW\njkDHNw9xnDO8csUKUM604c1ssLCcJopVnhDFx1T9hjdZ4wrgfo3xqzTbs7K5\niHa9AvxnVPym6xjQEcpjKjWB/g3NW4wlgWwo7wgvozpEc3x1gE99s1fiuTI2\n3oPyPBXt+7G1uzgNWoWySNBmKAYWedVO0kZ8Zc8e/NfFpyrtjBr41Ni8CNWa\nSh2nI02GiWko1JTlhzTk6q5dL65En9bLk9pjAaMHJPcOLQ6zxKBJnuRR0sIR\n4s4LM3V5EmlIeDDLyHu/Wpo0OfeoCB22wKTWEU0CkK2jmWb1Ys4z3MxmFZxF\nA8Si2taEpU7Ypw+UIIg5slNav/2yS+oFI4/NI6ezVu112b5AoG4xF/6PWsCO\nuAe56Ns3upBX3pbQdMbyG2UcS4T54Wg49kUecVVxCa2NwMioYAeMJV4ivjC5\ntZ4aCxmME9l6LR/jRt6c0Qqxq3OGPOOascBekuIbbxb3FPzJCWZAS86MfsC2\nnQtOf+tnfjWzN3X+44q8Mnxo4OGNIjNMIg05uQrfrz2zh6PpLnUq6yjFuwq1\nAab2\r\n=O4h8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "7.0.8", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "15.2.1", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.2_1606965760440_0.7109301324074198", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "postcss-nested", "version": "5.0.3", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "2f46d77a06fc98d9c22344fd097396f5431386db", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.3.tgz", "fileCount": 6, "integrity": "sha512-R2LHPw+u5hFfDgJG748KpGbJyTv7Yr33/2tIMWxquYuHTd9EXu27PYnKi7BxMXLtzKC0a0WVsqHtd7qIluQu/g==", "signatures": [{"sig": "MEUCIA8f7jpQ60Y18GvGMnlJYLr00Dy3E+W5TQ5E9mwNs2nJAiEAlbOR8YpdOtRPUrJeCwAx4TTy1NEb/afHztv5cNTY+Lg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzxPVCRA9TVsSAnZWagAA5bcP/R+5S0AFhbG2IEEpjQuT\nQcOO8uATVIdn/bX6CaRieqQ4U7jS5BlVoQwEgr0c1D5HHcUnin3h1+/iv+EG\n7N5yAE+HVfFUVgbB3+Wzldw8XgbngWaJ2SUQo97hnUXXbQFGghY0ODhIw+U/\nkHMFMTWgInV2I3EPCmJyLSFFI2ukfYyM4HbWyPHuIUDPj2sl1Fx1wfBJayJG\nbVyetjQE+jo2bmNPuDPJZGmbpVeG229p7gN+Ij0bH/+vqnv5bUaR9vcpUSOa\nLIJwdKkHxYKYXS/XlO9PrzI7aoVlnIXN/QP3MSFEQTmNTUG21RgaSCHWOGOo\nJYaOkq3y9siLFC/7OapeC4KKx9UU2iSUVT5IbWKmzZaqCFOIqiHb+Q+TTNGF\nrt51QpKyK1CUGH5sRsESy21Wv9o9qiVMi3p3i2xNzGPo0oLkCAHDS4MOCnAJ\nb3D4QfWRHbzIPAJPGGOCMg2IkdjABdEOjvNwPvWJ1pYHau/K3uPKxD/41a3v\nbIhpUhHa/J/bW38kDA6JixIKwDE2VvHXMaVcZtHzoIKA1I4fsQjZCwtprW+O\n1nRY7HUxI2DtKf11GXWXA17N/Zl7y8OD+lF0tx6Idc6Ukz3B5z0hpTGttxtv\nE1FTA4wxHDchgo9zDTBVogPIMuAZVNAhOfYP6+hs8BTb4M3WUZWX5Mmm1nyZ\nyCAy\r\n=lr14\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "7.0.14", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.1.13"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.3_1607406549193_0.9695323755460077", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "postcss-nested", "version": "5.0.4", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "da5c42a47b5c5955bee37d6c519135c28a558e68", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.4.tgz", "fileCount": 6, "integrity": "sha512-/dimXVqdUAVS2ZiIX0uvyk9UCI825y6LW4TnjG51JTKF89CcorHPAjTUGPF70k2wlQYts5OzfnhYMgfGfHCClQ==", "signatures": [{"sig": "MEQCIGOh7CoNoSk9A21OIqhOqOFVNqvM3MO8FxhoXx0JxZ7gAiBhRD9hu3Pkb/RKiF3OJBuXYd9CbkifrmYa9/tEo5yAjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOUt8CRA9TVsSAnZWagAABaEP/2TKtYkOAhxUBbClgDMb\n6vKKAlsndUEGNuTF6thZoK/b06s/jUcKS1ZVtYORFl2PAZo71pvy0kCzy5s9\nPKKTvRj332Y6AJp7pU6Vk6NOS5Mtq+vaFapxjeNl9ELtvVi04WpDS9yKVqoB\nZwLAN77fhkAE3nlcKDNY8Lzj7UkuW1Oiz72zpz1O9TeybRKIBd9GpY8J9rkb\nC8MyMraWfAA/APkjdSHpzqFwCjNxZC8fomuYdP4mdnhdYlQUkrJnOsWcZIoM\nbgwt3vsTDf3WMygKjPMVOlvO3lw07z/aelvjHi0x4NXRPJhFAkZN/60ozgId\nS80jJJwYZ021I2M7rMYO/vQIE+/XMSl141TCgjWqVxq7i4JPEZKZtu+3vGVk\nB5g2W4FE2opODyIJQbD9KnmashFUzRaJSruenBNA7GM6pHRiFnTmOMDJVNJo\nfxb/6W9LlfnIjymBm505nYNA2c64kHffDstLgBV1c0wfRvZ+ri5iOUSVH6NG\ny1wblxL/aMarwHRarUZf2A2Rp/jBfz8/P4nazQ62vCWPiO+2e69QPjzQtjpM\nET3w63yEI+3xx5TJrDyYf7ZD8/VDvulqDeg/8+YFky+qrlJdwxkxI0syq8Qc\nnLMT1iUycDsppXc6AH2/kiWHzK7mPa00tCL5IxbJH+E+bYiT1FBKUtMhhf7N\nxSm2\r\n=AnOT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "7.5.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "15.8.0", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.1.13"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.4_1614367611700_0.7030089303529334", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "postcss-nested", "version": "5.0.5", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "f0a107d33a9fab11d7637205f5321e27223e3603", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.5.tgz", "fileCount": 6, "integrity": "sha512-GSRXYz5bccobpTzLQZXOnSOfKl6TwVr5CyAQJUPub4nuRJSOECK5AqurxVgmtxP48p0Kc/ndY/YyS1yqldX0Ew==", "signatures": [{"sig": "MEQCIBG1oSpWWa0z4XqVtLG57CmWRXwDaCZfAhqM+299pQvLAiBlXPLmbyFLXPVDKQmiwUkw1Lg1JmuEOQksNNOckO8qrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQaO3CRA9TVsSAnZWagAAc5EP/i3XSDshlzoPBRKAyhve\ndPAZrUvpBdqKQKH0zrO4SpA9ffr9c/BPFDv0O6aIlkXto4yB0TrWFTNJmd3Y\nJaYqTmon5kcTM4eZU2U9D2m+LcjrCH4jmt1+qQLYYjvtfKgWmD05GQgnYUY9\nSw9HPSxfgArOISlesYUfLqhJRyiNZ81xApIUwbFP/WH8IPAH8vjpusTJkHGW\nw0ap4MgpNfqkBtlXzs76lCSk0vmZl+G5W9u50lkRcgrY4zwzhGiSFVappWiT\nSF2OD5Cz6syq1molIzpRlYBKM8nfzZ8xfE9MbYwf9Q8s45wGtJ6LnOti9BAd\ndwlXG8JEDd/3YDztuBrlf/ogkQiC6S/9raz7fI49/B3k+QbnyMX7rCjr1zZI\nG1zlE+yMzG3fHcR5qPo5vc63YA+D6KAwnpFauGCO/MjrRL0967/jVrcXnveK\ndsuyzaUrpYEpyfuUvCqmYRCeNepsLs5ehNdrkeG2JO+LOgood4++lrJ8EVOw\n/NCGZ9QGYLrqGqz1LDPmmJw0YLOxY/GuMlFUcDui3YSLm7nvPqydR/gffMRV\ncwzJJVartIpG5arQHj9BtFon9zYOBoWq9UG9jkvadFU4fDjI+fjTF9xpanRg\nxKgplRNI7j04vWEcldQuNcHmO/NKX+KwCMsg6adTtLZDm0zubeBfiaPvBi6e\nEida\r\n=GmE8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "7.5.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "15.8.0", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.1.13"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.5_1614914486653_0.3676432758920938", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "postcss-nested", "version": "5.0.6", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@5.0.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "466343f7fc8d3d46af3e7dba3fcd47d052a945bc", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.6.tgz", "fileCount": 5, "integrity": "sha512-rKqm2Fk0KbA8Vt3AdGN0FB9OBOMDVajMG6ZCf/GoHgdxUJ4sBFp0A/uMIRm+MJUdo33YXEtjqIz8u7DAp8B7DA==", "signatures": [{"sig": "MEUCIFTVckpa5VXbl4J4DDU91BJwQfXmz2bkcZavu+dH5U38AiEArS/JQm8RA780RNGtH4hzr1eCEjF9WpVVCglIxhriMM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhATExCRA9TVsSAnZWagAASL0P/3hIoV5TbwqN8QlvT9u1\nIadkz3XSq26D2wNi62BvAYEGWqaWW4tojAc7LkxC4z7i+k0DS9Fk5mcht4h/\nhXbAltRQNiLfDciJySUKJl+Tyh7mXz+A7Rc42kz/oQOBTw8Q/k4FP1NO19/v\nuNLeOPHD8r80+nFJKR8iJjoaN/rue2lebRdUg9Y6ZGo9haaC5EnJWqOuLNFp\nGP6sTnU9NOP6zfhR4ENfEAzLIeDDRPEUgK+iGNL7bOHRtSEeFeZLHMz7j/1R\nQq08r8aIotsYVDWyTvEFtIaUEj5xTLbXzBInSEdBOaqrehkclv6+WBFWkIw1\nRS+lmCIO2fdSdYD3CPchWcZKMXHbaAYb9OMMqPdIdAX5XPh0iyehb1XJlOBs\n7aqoE3+kjm7R0lPQIY1e1D3iRvVppfYUMrNh/6hb51q/4jHcn9r8z95Zp/Hq\nxnmlxuvYALf6qHxrBFq2BC0zyYvyG22P813j3RW8GNFwHdorp1A7n1KyYFWe\nzhqQ+0mM2ZyyUB/NZw2BehALFUh4cP3Ez/LKDnLaV0dxjZVMCMPEsFkkLcbO\nOeyFV9J8Szl67bGHiYQDzI1wZgvTApxk4xqGOFiiRoTKiTzAc+vnekuXs10Q\nrPxwBKlw+at08wlmmIdyntTDeDfT6bJ/UMeARvtMZaUKsYR85PLm7Jp6OdP0\n6aD2\r\n=rxmY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"postcss-selector-parser": "^6.0.6"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.2.14"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_5.0.6_1627468081523_0.8792055958996867", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "postcss-nested", "version": "6.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@6.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "1572f1984736578f360cffc7eb7dca69e30d1735", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-0DkamqrPcmkBDsLn+vQDIrtkSbNkv5AD/M322ySo9kqFkCIYklym2xEmWkwo+Y3/qZo34tzEPNUw4y7yMCdv5w==", "signatures": [{"sig": "MEUCIQCq9Xjf0KqCw+k9xI89ZDJGQJ89K8R6M8TT8i/lpDQXtgIgT0fy4DjCuxsJOkLCbAcilOizaIzXda6Z4tNsdpwLTgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPw2iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHsg//f94nsdUYTWnbe+Ge537kypK1h0Qv08GGM9gmcwrKqfyOj8Du\r\nx9vgg1jws4O1ynAUErSn3LIylQjUOiuWXcjK8q62c3MA1Y09jSIX50BdsDEu\r\nOfCCxA+Wuup6kPVSkZFdcUEHcC8TDBzw2FPrd8Z/vOMLjIZypVVeU5+TxMpN\r\nWHrxxuO33LwctMeU8UbYoZCwz3T4Bi/EjTM365KHzFenAF9SwVaqAirq6elZ\r\nS0ePsDcUIzkpI7Us1Px7rsqAmpU6zSVic9VU7EKr+Y1+yj53/E/w4NhCY9FZ\r\nhC3Ky+/p24o8xMCDuryr1gZb8lgzNyNec0/iBvrCTLg0DXl0sDQJjMTaU2JW\r\nrSyx8JuvMFsSPl7n4qe1lfIns+xMXUXknxgDu355JJKpMhMjukqi4kq5D2KV\r\njqPRbTx5CjuTuhK4yLAsgRmCmqo7rGB/ySZxUrtx5axjH6xly674hNW5ac2+\r\nbIFhZC96zg7Zsak54PTXi32I64G3bBzT5MunNaygftpfEhjBzEfURUoMlv2s\r\n92uPOYSj/JUHl0wD7Vc7cQq3bHgSBkwAgaMzxRdYoSo49rl8aQKypXluOlOX\r\ngIydwBJVSt0/T1pB7e3R+ytelTz0WljfRqwOslb3mxCw8Y3/4r6OVI8kDwXN\r\ng6Gmdh6+vxil64KrsfoKOU52ALrDOEshZ+k=\r\n=4/OO\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "gitHead": "bed7d2664bd97d9384e8f9cdb2f7dc85eba8142c", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "18.10.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.2.14"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_6.0.0_1665076641747_0.8465530075190066", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "postcss-nested", "version": "6.0.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@6.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "f83dc9846ca16d2f4fa864f16e9d9f7d0961662c", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==", "signatures": [{"sig": "MEYCIQDBjx5jFNFG0LIR4uvSpjLnbmdHDPVZJIb4SkHRKJDiTgIhAKEg9NuR+KGviNnS97nC6InchBhbXXciGgEvbizCF6sr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8e3yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFBA//Z9tAQkm4XYXYK5L0NdyKbmQ+mLY0L5tgVzOeDdVm9F5b1lMV\r\nSsdfzSEDaqcyQBaD5eZMB4CAF5PJhS9s6cemKfkiWu1EaQrBtBulcErmVg1h\r\nyL9+868DzDXHyS34TI/7Lp/ISl6KoB33M+wwjm6SqYHp6t7TSLOFG9y6JmwM\r\n6IDQomQExJlJkKhroyT+Ladl/jfDx6VtcwNovdmv8qeYwBox7QIFxTcVgcyt\r\n7u4hXW40pdS+GB0ED0PYHav1x7v14XE2x0FNJE6HqMMtf+/IZ0IsVQ9rJCW7\r\nALlIwuTximmjILt/tzPV6/wjDLrOJZOFiYz8YvLqXMWOW1gnyuOfYbCRWWgx\r\nguHsKtJ6LxQobxleS+cwp40/pfeORMXIWDS7Qkc2XXzoEeTAkif+q0SSnvh6\r\nZPWTrL5Q9qtQ5lIZ9Rlp1dVFEfjo1Atav3FuB/oHwB/DzpAJMPWXW+Miv7Kr\r\nCBSgSRWNF/tWR+cIY1daGC3HJKsbeWnxetDlJ57wCMXaz8SQGIsdUzTwhcWN\r\nqDNqPYUwYgRn8tfOk47KYcbSx7w92s79M2KzpMq88npbKwbmwpl5gaxLPnWc\r\n/6Wj4oYQxx8DrTnXI4Xg4GN9X5ux2PdaQhgaOfzRwQrHYaPq925k5z1Wp4IS\r\nsLWyVheuV6WsqNFOCTDpxp+OmC1fbZbaVJo=\r\n=E8Ff\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "gitHead": "69c2ebaa781a3b0188613c73b202510aa529c2d8", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "9.4.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "19.6.1", "dependencies": {"postcss-selector-parser": "^6.0.11"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.2.14"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_6.0.1_1676799474428_0.6969307306912842", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "postcss-nested", "version": "6.2.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@6.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "4c2d22ab5f20b9cb61e2c5c5915950784d068131", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz", "fileCount": 5, "integrity": "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==", "signatures": [{"sig": "MEUCIQC2ZN5wu+EF30g6HMdnL+zSCRTXKqC1q7SdYGGytLoy6wIgOyA56oLzTq0quFBs4n2L47RiotybH6W/1ysZ3XdG3xU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13962}, "types": "./index.d.ts", "engines": {"node": ">=12.0"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "2abd928a713f94dfd48bf957e8a6647a11382450", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"postcss-selector-parser": "^6.1.1"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.2.14"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_6.2.0_1721425846515_0.8663673829917962", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "postcss-nested", "version": "7.0.0", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@7.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "a9ac23a122341aef217c194d3d20c09833459200", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-59P6uI1Y00buIxhUpCqwHujmZNqJhzCBFj5jSWLQWK+L0g70Fxq6XjJpIa7vSdqZ7SEZXrsTLfFvaTcCqjX4bg==", "signatures": [{"sig": "MEUCIQCrGoK5hG6kfFyIC9+XxBE4QpjcSUy0INRkpmS4L9juwAIgLVdPngNj3WYIqa4ajGGhdF9Zgn01qS54Q3pwoO13fH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14663}, "types": "./index.d.ts", "engines": {"node": ">=18.0"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "3a73b521706f38eef0cadf1585070e280c132e9b", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.2.14"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_7.0.0_1730241423644_0.054091347956725766", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "postcss-nested", "version": "7.0.1", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "postcss-nested@7.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/postcss-nested#readme", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "dist": {"shasum": "5fb9417e0438347d0152d607f4a5bc9509318477", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-7.0.1.tgz", "fileCount": 5, "integrity": "sha512-4CDBQTsPHZL+cPpPAeDD5cvHe9mIJKiIffnsHC7Nx4oMGPu27aqrYSuGZyRlp1cv1Ms1koF2lO9QYLRdmUhAFw==", "signatures": [{"sig": "MEUCIBZpnEeHCoKLecnFeme5CbsFk3QfC4fp9VgvYSsDIWE+AiEAuoHseBgLu3tX12fN0VI3thLwnlr16AO3DzqsTPoInx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14627}, "types": "./index.d.ts", "engines": {"node": ">=18.0"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "9f75b3804d67ed80e91fdcb7fa36b113e5ddc93c", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/postcss-nested.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.2.14"}, "_npmOperationalInternal": {"tmp": "tmp/postcss-nested_7.0.1_1730396515642_0.35165855524560685", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "postcss-nested", "version": "7.0.2", "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-nested.git"}, "engines": {"node": ">=18.0"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "peerDependencies": {"postcss": "^8.2.14"}, "dependencies": {"postcss-selector-parser": "^7.0.0"}, "_id": "postcss-nested@7.0.2", "gitHead": "db9c61419d2112b338ec8c51ed69b9b7aa2d5078", "types": "./index.d.ts", "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "homepage": "https://github.com/postcss/postcss-nested#readme", "_nodeVersion": "22.4.1", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-5osppouFc0VR9/VYzYxO03VaDa3e8F23Kfd6/9qcZTUI8P58GIYlArOET2Wq0ywSl2o2PjELhYOFI4W7l5QHKw==", "shasum": "863d83a6b5df0a2894560394be93d5383ea37a65", "tarball": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-7.0.2.tgz", "fileCount": 5, "unpackedSize": 14397, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0HgVZDjrTUw4i3/DAR1eL/urkLsLiaok+o++h0az/ewIgLYtPR8Fsv2uzCcwP5E+8a87h7IAcxbBkFT6LlCCzHuI="}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-nested_7.0.2_1730477746064_0.17859925551627653"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-11-11T22:26:54.575Z", "modified": "2024-11-01T16:15:46.462Z", "0.1.0": "2014-11-11T22:26:54.575Z", "0.2.0": "2014-12-30T12:47:32.445Z", "0.2.1": "2015-02-02T13:28:22.758Z", "0.2.2": "2015-02-25T18:40:07.159Z", "0.3.0": "2015-05-09T00:28:50.189Z", "0.3.1": "2015-05-09T00:36:17.974Z", "0.3.2": "2015-05-29T17:15:22.160Z", "1.0.0": "2015-08-23T21:42:32.458Z", "1.0.1": "2017-04-13T23:57:33.327Z", "2.0.0": "2017-05-08T14:43:22.618Z", "2.0.1": "2017-05-08T21:17:31.913Z", "2.0.2": "2017-05-09T15:04:53.822Z", "2.0.3": "2017-07-13T16:49:59.855Z", "2.0.4": "2017-07-15T03:15:01.633Z", "2.1.0": "2017-07-25T20:19:22.132Z", "2.1.1": "2017-08-12T07:29:49.927Z", "2.1.2": "2017-08-15T06:25:49.287Z", "3.0.0": "2017-12-11T05:14:03.648Z", "4.0.0": "2018-09-07T12:45:36.673Z", "4.1.0": "2018-09-09T09:15:37.037Z", "4.1.1": "2018-12-14T23:52:59.997Z", "4.1.2": "2019-02-25T02:50:11.490Z", "4.2.0": "2019-11-05T22:22:45.685Z", "4.2.1": "2019-11-06T17:42:16.050Z", "4.2.2": "2020-07-04T19:54:10.972Z", "4.2.3": "2020-07-09T21:06:00.492Z", "5.0.0": "2020-09-15T19:39:28.875Z", "5.0.1": "2020-09-27T00:51:11.840Z", "5.0.2": "2020-12-03T03:22:40.604Z", "5.0.3": "2020-12-08T05:49:09.341Z", "5.0.4": "2021-02-26T19:26:51.866Z", "5.0.5": "2021-03-05T03:21:26.838Z", "5.0.6": "2021-07-28T10:28:01.653Z", "6.0.0": "2022-10-06T17:17:21.998Z", "6.0.1": "2023-02-19T09:37:54.587Z", "6.2.0": "2024-07-19T21:50:46.661Z", "7.0.0": "2024-10-29T22:37:03.905Z", "7.0.1": "2024-10-31T17:41:55.793Z", "7.0.2": "2024-11-01T16:15:46.284Z"}, "bugs": {"url": "https://github.com/postcss/postcss-nested/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/postcss/postcss-nested#readme", "keywords": ["postcss", "css", "postcss-plugin", "sass", "nested"], "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-nested.git"}, "description": "PostCSS plugin to unwrap nested rules like how Sass does it", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "readme": "# PostCSS Nested\n\n<img align=\"right\" width=\"135\" height=\"95\"\n     title=\"Philoso<PERSON>’s stone, logo of PostCSS\"\n     src=\"https://postcss.org/logo-leftp.svg\">\n\n[PostCSS] plugin to unwrap nested rules closer to Sass syntax.\n\n```css\n.phone {\n  &_title {\n    width: 500px;\n    @media (max-width: 500px) {\n      width: auto;\n    }\n    body.is_dark & {\n      color: white;\n    }\n  }\n  img {\n    display: block;\n  }\n}\n\n.title {\n  font-size: var(--font);\n\n  @at-root html {\n    --font: 16px;\n  }\n}\n```\n\nwill be processed to:\n\n```css\n.phone_title {\n  width: 500px;\n}\n@media (max-width: 500px) {\n  .phone_title {\n    width: auto;\n  }\n}\nbody.is_dark .phone_title {\n  color: white;\n}\n.phone img {\n  display: block;\n}\n\n.title {\n  font-size: var(--font);\n}\nhtml {\n  --font: 16px;\n}\n```\n\nRelated plugins:\n\n- Use [`postcss-current-selector`] **after** this plugin if you want\n  to use current selector in properties or variables values.\n- Use [`postcss-nested-ancestors`] **before** this plugin if you want\n  to reference any ancestor element directly in your selectors with `^&`.\n\nAlternatives:\n\n- See also [`postcss-nesting`], which implements [CSSWG draft].\n- [`postcss-nested-props`] for nested properties like `font-size`.\n\n<a href=\"https://evilmartians.com/?utm_source=postcss-nested\">\n  <img src=\"https://evilmartians.com/badges/sponsored-by-evil-martians.svg\"\n       alt=\"Sponsored by Evil Martians\" width=\"236\" height=\"54\">\n</a>\n\n[`postcss-current-selector`]: https://github.com/komlev/postcss-current-selector\n[`postcss-nested-ancestors`]: https://github.com/toomuchdesign/postcss-nested-ancestors\n[`postcss-nested-props`]: https://github.com/jedmao/postcss-nested-props\n[`postcss-nesting`]: https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-nesting\n[CSSWG draft]: https://drafts.csswg.org/css-nesting-1/\n[PostCSS]: https://github.com/postcss/postcss\n\n## Docs\nRead full docs **[here](https://github.com/postcss/postcss-nested#readme)**.\n", "readmeFilename": "README.md", "users": {"denji": true, "brecht": true, "vicargo": true, "jmsherry": true, "alexdevero": true, "princetoad": true, "flumpus-dev": true, "runningtalus": true}}