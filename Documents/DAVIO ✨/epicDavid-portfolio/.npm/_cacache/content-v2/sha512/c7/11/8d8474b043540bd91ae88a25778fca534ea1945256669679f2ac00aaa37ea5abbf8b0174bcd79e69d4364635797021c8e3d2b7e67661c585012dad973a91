{"_id": "@babel/helper-compilation-targets", "_rev": "88-ffa2fde7c8d111673944a1fc2c6e2211", "name": "@babel/helper-compilation-targets", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.2", "next": "8.0.0-beta.1"}, "versions": {"7.8.0": {"name": "@babel/helper-compilation-targets", "version": "7.8.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.8.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "63d3924bc04b68b2d24d25b8cc452ee86dcb3a59", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.0.tgz", "fileCount": 10, "integrity": "sha512-VcMSwBCqA2mGqmFFnLYtaC+Zkok5pVMOypeGn76RpSBAoFqc1olWjYoNqTn09YMChTi6rsbPIkkEOAwfsKSqRg==", "signatures": [{"sig": "MEUCIFiuhA/I8PIbl/zeXE9cc/9DxAa4WgAyCjhMA6QJN+uSAiEAhcczIPYkI2rQ8LSk4zKfZA/KBlQDhn4H57dSG3edTmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVrCRA9TVsSAnZWagAABnoP/2ChofB72sqVEHiEpwvm\nzwxNUc1G9nXNxEQq4SsPTN2rvwUkqKMMjLn2IuQKCW9P2R0ryf/veI3BoTaL\nOC4ZJyQ7zDnTVP8PajB50l3HP84eLjcQ/PDdpvzp1hA5KF/UB3yk8Kc0FFup\nA3qo7v1qUtPAYzmMDUZdyQ8iOvMJkbK4uIn36mnVgthE1xXBhVOE0VVNk+Uh\n3C7FtS2s6hnia8wVSj8EuT9Pl4G9Ab9u/fXdqL/P89hwh/uhLEbvM2o21IOf\nF8iXhdgBygKrqT5MQje4n3dUKsrja/G7YOEyhcFp0euWEs72QeBqEp1Xjzvz\n0TbdoKJEqZUg+tfcVn5ULBGReigWFR2BhefYJzHahf3shzF8WoduBJpWUORS\nHCd1NQdoxRqyCvGRf9bc8P2E9MumPKdE8vuQiIxchSDCOgXgNJxxtwpfiKsE\nhj1hKxg2fWMw9n5k7B/4qpDkwSKOxOp5xraDWUrURFNj6Vs3dyWAxJBWLcIr\n2ps0bvM4Ervgbtx93eVicoxMV5dluFNlXb8Sn1ANg2gDfk6yzqPdSKZKJuVr\niXfqovHOWsxxUh2rFa2o8kPngtIwoassNC+LJzq+s6KUuICdQYiAKitmlp6W\npnkeTtJt9KlBzdHUP4AKCsr/iXvGJkwBZwnp0x0Qk1kuPLBFqNRXU8nSmmp6\nTxlJ\r\n=A7fz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "exports": false, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"semver": "^7.1.1", "levenary": "^1.1.0", "invariant": "^2.2.4", "browserslist": "^4.8.2", "@babel/compat-data": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.8.0_1578788202854_0.024554826096487403", "host": "s3://npm-registry-packages"}}, "7.8.1": {"name": "@babel/helper-compilation-targets", "version": "7.8.1", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.8.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "309da97e93e6ca74896393d0c8da7af39cb4cea8", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.1.tgz", "fileCount": 10, "integrity": "sha512-Fsrljg8DHSdnKSzC0YFopX7lseRpVfWMYuC1Dnvf7tw972E2KDjZ4XEaqjO9aJL0sLcG4KNRXxowDxHYIcZ+Cw==", "signatures": [{"sig": "MEQCIFmpJoeqVTP6h+N37/IRuVHIT/ZVon0y8n3XC6enAUxOAiARUFk10+QUxU+71TFNVeUONN+vzkUZkmgTHZkbOhJJ/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGxj3CRA9TVsSAnZWagAASeoP/2YU2I4/OsQtZd6Rf5Y6\nKXY+GK4Vfl0l7yiT3Lplr8HRxFYhcSNVOyO3oOILiJKxlOFHlEeLFxOTaJWS\ne4i+zEmybZRhj3cqxu5T8z5jW8mjhiza4yLIrCO/FJWd++55FCyZrOA5J1R2\nEyfSnF/CT5kpUWMBB8/5zSc8zac1DliEgKuOPab6QWYu68tN1jnZEk0JkHRd\nhnqRxT4IRrEscAmmm6AZTNF16uPntimAv8N3byzf1A1fGFCypyxPBwTJsKXH\n7TyahGoFOszCOZuXyJVXxw+xNyjDdWHhQOGIdnh+XUWreTCa8vyBnRuYjVOT\n0OtD/F+N5efEnwkKjQd91m5gjyBnG+80Afwdjn7SqWBC5qQhrMIz0lKM/z5Z\ndsh51UOad/WNyAjVUB3mGx/vtUoHb42cGpa+79KDbg9BmSXyMY8deFZhjds4\n7xUXMylyPP8j2O18E1fE/keazuHwd1QfznYt2TBHQsYec9lqjBQIOZ0WPXCl\nbiYQQh542VNTmOFzaTK8VgQf2wF90istxXZz+ZecbkdDu8ghIu17W1xRBire\nf9DXioBvG6IVyB+XaewJNOyOZ+xwuQ6p2mXGS+8TGI9Y3DbqkIM/SVW3kIS4\nb+xFYYf0vPlhWfwg4H2KezC7tRJOy03tyQ/ep7kdzF281i/X6Xi7YbalUHYN\nurc4\r\n=JE0W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "exports": false, "gitHead": "575eeb370efd1a234bd7f124dc6b1f9161f0c161", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.0", "invariant": "^2.2.4", "browserslist": "^4.8.2", "@babel/compat-data": "^7.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.8.1_1578834167494_0.9605025350445473", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-compilation-targets", "version": "7.8.3", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2deedc816fd41dca7355ef39fd40c9ea69f0719a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.3.tgz", "fileCount": 10, "integrity": "sha512-JLylPCsFjhLN+6uBSSh3iYdxKdeO9MNmoY96PE/99d8kyBFaXLORtAVhqN6iHa+wtPeqxKLghDOZry0+Aiw9Tw==", "signatures": [{"sig": "MEYCIQCIpu87k4AwU6EJBxqKHW3SJm79TAU/JyH5QdjwGv/szgIhAOgQPme+bAAeRCwIslif4hlx4pw5CeQisYDSEBFjlhT8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOPpCRA9TVsSAnZWagAAeX4QAII5cfRfKZxvz9EfYGBR\nACqtCnDb0jgfzZIjC2Lg+S2Dx7Ng5TidXjfITFnSQjSy4uqKDxcAeciM+8aS\nNfAqhMpOs1qLY1GuWyWqMEzXVko91JROs/akNT/gG8w+Qa108gydcVjhjaHn\nZ0fNaJzZIJXkdhTNolTjLEOsnAJi7do6FPeYFXf6NYe6KztQnSLQHDp5OUIL\np+VX6PJTwUyMH1rlaNnMkWZJpq7VAhVFCZu0kkIjpIzCqPh+RTcdryBM+Q5E\nAASrBkUyAe2EXnpjxFbpkMEBuJdcV9+sFy1l9pe5UxUqA82UlEFa5zZlQgW3\nMzmoaPt5hiFFQleXavfYrOl/mGTekqQIizGbLadL7q13QPjw3ohV55yXECQ8\nDGQfq4urwZNeIzq7XvbnUxbAHLlvz9z2ICc3fogYoLPp5bjdpsTHsOh9PK28\nEIZbGxRtrnHPwg97WWKrDEsNmC2BlhxbW4cbfC4LGX9D26vIBy0aTMKllX5r\ntlq6dT4IiK4EEfFQXePh3GD4ANWuNETxQ4G+I0WeQYDy46vCYM8yTVCBArFa\nIaRppgy4+e2l8ijdblgcNiFsgpQ36b8dv3vTZ7NQPXYoqIuMbH1q6XmpKyy2\nlIw6p2V3E4+YPdZBQnShd33hlBAqjla9SX7NkuDDd4N9sdmfYdMxOANhPZQQ\n1Fs7\r\n=IGZj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": false, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.0", "invariant": "^2.2.4", "browserslist": "^4.8.2", "@babel/compat-data": "^7.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.8.3_1578951656598_0.11689359320991066", "host": "s3://npm-registry-packages"}}, "7.8.4": {"name": "@babel/helper-compilation-targets", "version": "7.8.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.8.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "03d7ecd454b7ebe19a254f76617e61770aed2c88", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.4.tgz", "fileCount": 10, "integrity": "sha512-3k3BsKMvPp5bjxgMdrFyq0UaEO48HciVrOVF0+lon8pp95cyJ2ujAh0TrBHNMnJGT2rr0iKOJPFFbSqjDyf/Pg==", "signatures": [{"sig": "MEUCIBoAhD/0kxNWwM4jyk6b2ZAEw5bDuG4FHqM5oAkuKbtkAiEAr5cg/pnz8/nPsgLAq6YOk7gfXZSNXPyKA+xy3SEii3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs4LCRA9TVsSAnZWagAAHr4P/1snEuuyEFcp6au6TJ16\noG1tLowykFNiqq5rNx/XOroVkFasHCyrsOm76aWE+hfRVVH48wIwPehoSR8J\nS0lHOhWrUdnEfpq3SEn/7QOw/Hfar3YdGQmCz71zOqe3oYElroiV+FDlh+KO\nijQk92MRN8PUxO2oI6yb62nxMmQGvdtzMuCvt4bCQdSdXQnRLH4QW9JQqnWZ\nHPPluz/Xg7dseAtpDC9VihX/3VlKYZCIRhLkoIR/ajaWoZ8Cok5E2HQ7HVmb\nFRj661C5ZWlmW/Em5T6czBfuMdQ7FZKVzT/XnvLTU/IN6gnJuCOs/5V+7Kmf\nozkXQy5Xsiede1Qmhj3oahbnfNlyWE8mzVjy82qDFgBRIwfYlk/L+uj66DYJ\nsCpBbXCSmD9C5lPrxA4FDN7kwc0llTblKsQoKvkp8VUo9Xc61RtgWJzuqKqB\n/PdcZx9dgSXEP94xwcK8b7+2LZY3Vj26vdeMrOABKlk1Lszp+qAUQgyDCdtu\nWDirsn1dHiNryiX0kKu/OgCzX4YiA/kv3HEfbDsuhXMIiVkIjETHkXXoK+rm\nMnCbH5SzN3BolpiSyd8TgkTnZQQxNC4SV3YcIGPeV6HfRBE0o0scyQwl5rL7\nHxSb2O0RGcdw+ua7CrI7DAwescfq6ggPBlHeV5ZvACDt5Fe6HL8R+eWJsrsF\nyFfJ\r\n=sGju\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.8.5", "@babel/compat-data": "^7.8.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.4", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.8.4_1580387851527_0.14066725089017296", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/helper-compilation-targets", "version": "7.8.6", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "015b85db69e3a34240d5c2b761fc53eb9695f09c", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.6.tgz", "fileCount": 10, "integrity": "sha512-UrJdk27hKVJSnibFcUWYLkCL0ZywTUoot8yii1lsHJcvwrypagmYKjHLMWivQPm4s6GdyygCL8fiH5EYLxhQwQ==", "signatures": [{"sig": "MEUCIQDlEi3obX7LVjHZjmj03kSTu3cqIfogbzajwTye1Nm1qAIgUatmkARgmLbM+T3YgiTu85CkcQUSWqef6sVlHl/wFYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RGCRA9TVsSAnZWagAAnMQQAInqVZ+beG4KPbqkU7qd\nqnWK7Wp9p1bWQ7BM9mANH9yWSZ2ppEhziAxWZ1GAec64p1US+i38v5VmdMGv\ne2Ry4oiL1Pq6+FCa33O/j6Yn09gENVkH/FugrFLKqVaVJbmmKY7VfAhBDVRe\nfSwyzkJMLvIdaamna1vtDUkB9YEymhBZ+/vJIJVPbh9VUBw/UQFtLGpPnP5Z\n9lwxkhs31g/KXV2QwsCtQF+WlXQKuFdwHRY2wzjKjjReGSFCFjKKxvijCCND\nvk1Yfk2DUwagezIONFCvGaxWWnzpmCy8gpZ9wcLuHhSaQJYgkNBMmFNJ6Gdd\nEKZfgdHQHeFT2SHOhB0/FNLWrAShl8WOLjLaIEZqVmoqcUly2X89Gdkp7KPH\noeNOIFCoL0wnuc1oNFQDZDW9gENjH2ynf0GCl+Xin8Ftcrmujr8diF+DwIBe\nqL7pkJAxaA/HXJfbM0NXDzhsV/CFZvEbpPDB2zS3jzDiUQWZIFtXH7OR3tWC\nQeoiuRjKn8hkTcEURR1ZEXm8uE2I0MJDCw43roMAd3BvCUAsvBmEery0N6Ay\nfsoRfsDwIBljCwHCdr9aYY5gkjqLnsZlz1dVgetp6BFcLCVv4ke42eudmVH8\nk8uJLdyjh5Cd02CbL3TG7MKEh1ryv9RrCSC8XRKEiZ5Xzm/mqFMyDNfwzeB6\nnCA7\r\n=GBlT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.8.5", "@babel/compat-data": "^7.8.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.8.6_1582806085869_0.8077167705814341", "host": "s3://npm-registry-packages"}}, "7.8.7": {"name": "@babel/helper-compilation-targets", "version": "7.8.7", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.8.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dac1eea159c0e4bd46e309b5a1b04a66b53c1dde", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.7.tgz", "fileCount": 10, "integrity": "sha512-4mWm8DCK2LugIS+p1yArqvG1Pf162upsIsjE7cNBjez+NjliQpVhj20obE520nao0o14DaTnFJv+Fw5a0JpoUw==", "signatures": [{"sig": "MEUCIQCngdqMrfKNmqPdzf8jIwk5bGwAsGGLJ0F7t79ZRyLtRwIgWAYCzr9wYiYi1FYMjski6qh/zT2CGjGYmv65FX+3ohM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFwxCRA9TVsSAnZWagAAtzoQAJanQqpZ8xz0inV5vnm3\n0hF/+ymryQ+9nVEb67OAlCqTidOOVTLo6UqOAsm87rJAQfYIqJqrWmfhUHU/\naKOiPi+N0rUDLKsrMN217gISxjwCD62FqLlDI38zLyPqblZW/sRtgzoLm4un\nmi8L2QXP7yH3V9KLm893or1dNaYwfDzJrhNrs3INUrX61o7txF9FSE+SKdPJ\n1zAaxD8PzpEODsTb1WllBn25g2I+zZdGn7CksIXfr0c7DSIADVc8xU5+s30s\nn0ZbfYrCpqusS44ROaNutx42eEiMwgzQlIIDOo4HBASCe5b+02bwDbjbF79Y\n2FkLX0pqhYvBp41o751IvK/1iLhrB+Jqhc+ydxl+6ulce5was3IP6UqW4+ZZ\ns2uuAPk2oBveOd+t3saTghYlZ8wkJwsJ4t/1ufn0uqU/TlSmmRv0zj0yEbBi\nBN5OdHOzhpS5KRtvt7m48X9OlYACHy3n4Td/EyNRDoHUKjqueASmMDd1gDft\ni/p5pzKVoA2ku0RXkEEU5zAeMLA/DFaxog8ENgwexa1fmJQq6G9Z6FGbHwK/\nkqMUV+J5BZLxxicXY/naEhdg82OpX/O9Qm5sDQlXiYX/L8YHBIwkiCVG59sO\n45SN39lCH9XSmqfglSvclpEJavr2nySoo2+Zmw4DGsISi20+rvXNaXE2BIPP\nOtBd\r\n=ROGL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.9.1", "@babel/compat-data": "^7.8.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.8.7_1583373361111_0.9416845315035198", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/helper-compilation-targets", "version": "7.9.6", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "1e05b7ccc9d38d2f8b40b458b380a04dcfadd38a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.9.6.tgz", "fileCount": 10, "integrity": "sha512-x2Nvu0igO0ejXzx09B/1fGBxY9NXQlBW2kZsSxCJft+KHN8t9XWzIvFxtPHnBOAXpVsdxZKZFbRUC8TsNKajMw==", "signatures": [{"sig": "MEUCIQCgwh4c94OarrvBZ93EHG4Z4f+iko8Uf2aYh2UbwlOUkgIgIFEb996fGpvDuSkxWVcdNH1ce/0mgJ+tK2JfIYdWBp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmNCRA9TVsSAnZWagAAIRoP/2F64nQY7Tel8sXyCMyJ\nI4E+89wQzRLYf5hhzkjIbwRwC6IRFg5cc5/2tOA7+S9LdWFhLU96MC4piByq\nZUJ0d8iEhyMGfrxvxgu0Vb1NqWelj0DVXKQLyRWfX9nEKc0sDR6KkORmFFLr\nafcXNE31SqM/WucptLezx3LoPZvTFI+rmGnQmUbRXk6nVr9swXe/VTCMuBzh\nMsBPlZ112OKXkDWfy0iR2FBd4eEzZrmaNfICyUKEyQ4GpdnCZAxg2rEF/3Ck\nSoyjz4/8aVVjlJUIJrNktjbYfNtS3ETLUjNM91ecHuxrUNfj4rlXC8ELnP6r\nHiAZSYTvXm1QBOyrnPLBFxGcnDY2YL4uKf0s3k0l+K3NDw+Eiq5hE71Em1GE\nPqF4cSMbyLGV5X66XFpoiuvnlW2jRqE6WvHWeGJcC8BrU/biR2xMEyx/dSBJ\n83kSfBGs0RnTGnLkqVJvHGjAhletj4xxwd6e+e4MFYk3C5z2egL0BgPewovC\nEUj+kHL0qdTu7eMdkHYGYBoSwNWV4p2kEmAQ1ww6YbrYVueHVBoEqFbQPAYM\n9OF69G5gdQ9T6HsDrwXrmxEwvE6NmGO9Ulv673URtTvuzAXbPozAFP0M7eXv\nKhWU48F/Zv0gECESYINbnz9uO54SQk2gFq/Kw/i0FHMGyo1eVaQFL0gXObNH\nkp/l\r\n=QLOX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.11.1", "@babel/compat-data": "^7.9.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.9.6_1588185484730_0.36012289872988834", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/helper-compilation-targets", "version": "7.10.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c2734604ddfaa616479759a0cc2593d1928304bd", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.10.0.tgz", "fileCount": 10, "integrity": "sha512-PW5Hlc1cQ8bLzY7YsLJP6PQ7GR6ZD8Av4JlP3DZk6QaZJvptsXNDn4Su64EjKAetLTJhVPDp8AEC+j2O6b/Gpg==", "signatures": [{"sig": "MEUCIQDk2eHZouvYQuObsKq2TfFKxuExd8ECU8E82vvEGfe+wgIgRQcVGjGSijeMB5KuCTsxOhh3OVBoOcrbzGGAfWVUEXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY17CRA9TVsSAnZWagAAI4AQAJyAg9skT857UEgXRhiL\ng590GsgYh4LxhQpAF+ioP2Ee9pTSKJf1YC7jE/WjUAPzdqPzw6Cna1yJnULm\n8Ve1Y+7uUAe3JL6Fhfx/x+JJrWxTR/y0YNjywWngPyRZJQaZGJJfK/Fdn4zz\ntt1mtT3V+3j2l32EGsvu826z1ojpClJ91kgSRQbF1UyUZIM8doJl0F875Crp\nEpeUgPlK/P8nZKKkl8d3CCWJOcjLqtK+QdyzMaeNFiX5ccqbaUV7ZwnUPi4i\nytycl5TbIkH6mwHMr/36W6265Xh+CMpl1ST273GIQQqPYdrJeQfWP5x/uHwx\nsxxkPjaVOEBLQAjuOZY+bskbouatumIEeeJqmcj85NvZ8kl/gmkJMiZSMSo/\nJXDF+lb8V2lDFsG1N9NkEt9dOsW6V0HWaJdtOiwwS6fhxUdZx1QE4WX4vymh\nn1k1CAFnu8IdxkXw7C9R7KxzWUmGw8Y7r97JcY12jbw/cRzGjWB9cpX+/kI4\ncKJGvmTOugTsv5awSPCDRb7dRnM/DSEb5KC3cVGtE2Kv4uAd2e16eG3nfX3n\nWQNzDJ3JPv3jWoTyqHvQ70edBurDIyj5+qBX5qArbYLpPI6g6w56gP/0jDv/\n53R6b04xtIc+XzC2KT5zub28HejHtNQnQtEYDM5bWLroijrf26dPu/niS7gL\n2Ijh\r\n=a9lA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.12.0", "@babel/compat-data": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.10.0_1590529403215_0.9301077547113896", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-compilation-targets", "version": "7.10.1", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ad6f69b4c3bae955081ef914a84e5878ffcaca63", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.10.1.tgz", "fileCount": 10, "integrity": "sha512-YuF8IrgSmX/+MV2plPkjEnzlC2wf+gaok8ehMNN0jodF3/sejZauExqpEVGbJua62oaWoNYIXwz4RmAsVcGyHw==", "signatures": [{"sig": "MEUCIG8GazABrZAhnMI/pEoICVIn2lDwHElbdpo1warD31fsAiEAnNRBR8G/CM1YWWNpIgkE4fY/Ks0NMkO6YR+1FSKUU0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSECRA9TVsSAnZWagAAzWoP/3/1WEwf/LRM+rAuqzU6\n4uJjvdLjWsIPSHnmZ8pF0tSLGp1AJFf0Fljn10UY8bpgpnAiyqn87HXBCTbS\nOsoZKCyq/Gr+Jro8YOEEtQrdONzDt1V2Yzr5a1J7eml/iI/FQElNCmbDho9F\n/G+LG/7H4YmGIk2LnrnNp0xFgwu/DVS8+eXUcEpphmuBHOnCA7gnHnHhFTxV\nT1B7rCdFhkj3k7KVs5wv/X5xnzPmQBbY1jVSkJf3lgYe26+l+sk845GBb5Yi\nCXYDvDl0GwdWPww8FDWu75oxN+Q6Jw0Cjl8/dVlMwoCTt0avz2Xm/GH3HeZs\ndh/3zAnP2g2nAHRrvDyt9dzuQN31Q8BU5ICQdT6+EZJEcxpQQa6bAS3IsR2P\nSjo46yedb7Ck7HSDm+S+3S7/DMzqcqyDYJFsthEKJdHRM8ZHBbZ62/I/I+rK\nohfcKCk33O4qJ752lSZhrqvDVmRZWGzcxqXLz0swCEqUnawM3O/DjuWFAWS3\n837y2FE8puGUM1IZJwkN2OhOiktMiKYjv8Oj0CelVdfSDS8sdwP8uExJxU9S\nGQY0TqsQ7OEWPX+QQiuiJ0fMNBPWWRW7m8Dwy46cKslNTyjJp2alpiSpO2dt\n2xLT/pdgyuxfcwehMbcRec+GdezOpUpLVYOkJfSoEo+1oV7nnp+ctd4CVAw8\nWzqo\r\n=jYdh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.12.0", "@babel/compat-data": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.10.1_1590617220028_0.043029109119161024", "host": "s3://npm-registry-packages"}}, "7.10.2": {"name": "@babel/helper-compilation-targets", "version": "7.10.2", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.10.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a17d9723b6e2c750299d2a14d4637c76936d8285", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.10.2.tgz", "fileCount": 10, "integrity": "sha512-hYgOhF4To2UTB4LTaZepN/4Pl9LD4gfbJx8A34mqoluT8TLbof1mhUlYuNWTEebONa8+UlCC4X0TEXu7AOUyGA==", "signatures": [{"sig": "MEUCIQDtywsVuuNo5vN6IKnJXj5+sWewBMKvloYofmpR7XunBQIgYWQVLBWC0EkuJNvNFV7T6JmP5e8R+GAJYzYGQjfBACk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0rMXCRA9TVsSAnZWagAAOGMP/3JH/I3J8Ks7Ztep+Zf9\nP6/S+Qvv53n8BJmqMvmrvZO2DUWxXAQCpTn1CqpAnLhKrCYIJKIV1Acd7DKA\nGCvusRDdL+YCdph8LSU8uzRGvnpN/XKhxXyPjHurE+IzAQZw1GuOq7ifChlF\nV4vukIS8Aw8/86IoCrkvlppyYNPoHc0YfZK+ul7UmvvuNsk0VeatTlwtFqrn\n/zkrRp+iejVe2qN9m1LIaxV9XEBKb08jB2vA6rjTj9qzTOznnt56KjL1Ux3U\n5W3RJSKsZNPbJ0WeiVkEXcrrlheVgGxAl/tuFUpd+f/o6qEjqp9LMHgYUGwz\ni2D5nVdRusGpnIG0niycveNXmEW+c2ArU16XMUvxusvxtynzmA2O6Ywa5u6z\nh3ugetpUcMTtSzayaNbheuAeABQ8YJ8+0ucm7DruFGFC32jbZ4k0zvWk+t9V\nKAhrHvvPUS24CsqMcWNKj0PPZj+3m8YCysktp2oOk80HsBZT09Ck7SHb+DUT\n+ryhvvWpgMYDTs9c+1eojRez0OfGwCZEyffuA4QrY10GlQ5Q4kirkZwEYg/M\nb+7vxa8IigIcTAMmi5+a4tFxNzuSYli1FGf6hJXHHmADMwTiPGLrZLzEGNeS\nPTeW8O0ruVnZmZgSzEOa9G2Gd7tDfqCiyiTDRqzlIrZkPdCuB7+06tOVHmnK\noLrs\r\n=1Nmo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "b0350e5b1e86bd2d53b4a25705e39eb380ec65a2", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "_npmVersion": "lerna/3.19.0/node@v12.17.0+x64 (linux)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "12.17.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.12.0", "@babel/compat-data": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.2", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.10.2_1590866710602_0.16112127958102174", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-compilation-targets", "version": "7.10.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-compilation-targets@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "804ae8e3f04376607cc791b9d47d540276332bd2", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.10.4.tgz", "fileCount": 10, "integrity": "sha512-a3rYhlsGV0UHNDvrtOXBg8/OpfV0OKTkxKPzIplS1zpx7CygDcWWxckxZeDd3gzPzC4kUT0A4nVFDK0wGMh4MQ==", "signatures": [{"sig": "MEUCIQCFhavuoTw/qzp8smwtKN0ziUJm6kLRFTUIfFtbHQHP7AIgBnTx+v4jtzEgU0cplXx4hJowgjJCUIYDtVlsnH9CLHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoFCRA9TVsSAnZWagAAIeUP/13mPdTV+2EqKBHK3j7b\nFYQohOlx41iRDCdNvkYJXP228NHtgomt5IHRrFzKAH/7hWZPvJr89LBVE+XO\nXbSfwRSe60/ZPenBaB/mb+c19PewkcXg4XeiXvczUTcujyNyVb/wb1mWQWWz\nlOAx7ihhMMYG4w//FqHMT25HEIemI6SEoHD2y0qZlWb1Rg8dQ/9zHDLoxz4I\nFrpmzfPr24DYHRBm9oTYloZnQHYhwMQ7oxrEunuJCTtnqUAo0c6TyYqkvHlM\n7IzbzQOoeMmqToOvsXArQamTqSgM82vI2uwRw552PHgiqLSGfZ3qJe803qM7\noWB2b0RxUivzWiZ659YGb7GNyGb/DOKyC9MHDT3bkd/4Vr8s/JF5goKH71lS\nlMVnMdpbxrw5Qo8gOo3f2RFTp9EXlXoZvgzgYSd25+mEOXrI4sM/JxZ1bw0z\noHCAOYSihJ7C2JnwFXI2Lup1ooMLeWjdPeraDlNI3sv+m+n6giQrFjv8XAgY\nVXj4MliR+n3ct6e72JbQEk1dOtxOloJy1yXL7iEY2PYrd+FooAYHb6GgboYp\nSGuPuYo74xUJ82utYUFMjeVF2y37E6lQq6A+B0tEWOiM0oreExymimMAblOt\nfaPyOCoDWuPEKlrgFJjH0sDgN8r+vdt5tw7Fj/nhzsgPOJRJElfxRfp6ZTNa\n+6md\r\n=fi9i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Engine compat data used in @babel/preset-env", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"semver": "^5.5.0", "levenary": "^1.1.1", "invariant": "^2.2.4", "browserslist": "^4.12.0", "@babel/compat-data": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.10.4_1593522693202_0.9918065327507486", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/helper-compilation-targets", "version": "7.12.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "c477d89a1f4d626c8149b9b88802f78d66d0c99a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.12.0.tgz", "fileCount": 10, "integrity": "sha512-NbDFJNjDgxE7IkrHp5gq2+Tr8bEdCLKYN90YDQEjMiTMUAFAcShNkaH8kydcmU0mEQTiQY0Ydy/+1xfS2OCEnw==", "signatures": [{"sig": "MEQCIHAArus7YIQ5JCJ7+j9af18YrQpfui/7crdsn8i7+VcXAiBlO1xOV15TQ3YPL631f703iAXEnyVxLGHMY9WBu4sxxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mACRA9TVsSAnZWagAAYvUP/jI3vWG2mlU0mTbpNFPJ\nd6vsVh6O2qRlyMg7scId/I+L5mO1Q3n+3ClhBLrF2Sw4iJcyE4mFXN7U0mY6\nFS1HezPmwPTV8QWvB/Yw2IO7B/K2bDbZgaVPzKBwmjPSkXhyoLjFTFTsQXsx\nfcIKhMx5gHJkm7u6oYCJWQ9anNUgn5LEAcWKFdSQqcac5cS2FeUFOmPuO85H\nhqvUXZJySX/xJMuN7eJikyMENGGCKZVmh1ItrhX6C5V+b/+wsKrq8hvwUJt2\nuaIqwY9+zuRHYDHs//23lscdLbfliYO1yAJwzfDjdkdvLtciKrlYEEqQ4Swr\nFZo5OjjDYmLvX8um9HmI1+pN831gMeRds45XUmHSSFd9nhagjP8NawFJzJ4h\no/rBxJ6NA7jpTrdD0ia2kEXe161rkZqAll/N62RgrRLym+/rFjBggVdnVYnr\nE6qxtSFf7AcebSWPCIvKDWWrXKtnncDuYUcoXQn9sh7UDLzWk0ZI20EviDDh\nf6m/YBKKXVd0YY2waRWIIDPlbx/W//+FBAds1Qh6p08EuOzQKIM3HA8AflEp\n2OpeNpHeKhDLq9kTSszUBgwB4lwZ7MNX8nnhUWlyV8ttW3PJgkdycVSZh3P4\n42LI5du1pv+36CCvlzCR2QSKxL2/qSB9GEc6pfap5raCGuchGsRiwTMLWWDZ\nBRVV\r\n=lZgx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^5.5.0", "browserslist": "^4.12.0", "@babel/compat-data": "^7.12.0", "@babel/helper-validator-option": "^7.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.12.0_1602705792371_0.19872995897069412", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-compilation-targets", "version": "7.12.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "310e352888fbdbdd8577be8dfdd2afb9e7adcf50", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.12.1.tgz", "fileCount": 10, "integrity": "sha512-jtBEif7jsPwP27GPHs06v4WBV0KrE8a/P7n0N0sSvHn2hwUCYnolP/CLmz51IzAW4NlN+HuoBtb9QcwnRo9F/g==", "signatures": [{"sig": "MEUCIQCEiae752DEijNinmiIjmfD8bIWdLgxB78iSu+dMyLhwwIgMIe/PaPo8RJEfB68ZN0kf/NSQSJ38/IFIRri5QsUoxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/zCRA9TVsSAnZWagAA6soP/3j1HElvihQcOd6brYdh\nOM+rk9ALUrvZv1+wgnFPJyNwXuJUdUXMM0fWp4IH6Sh82HC8fKqkZc44EIRT\n5v32VMdVvybTMmI9eLyeqJwdqDGkB9qFM3gWHiLUdXTYXOlFUN54NYmEryK1\nOt4GZMcZAqiZ5L/j4jJfJFZFqOrlHukzOd4aYd7HSo+PHtauXZPcduUqYQfV\ndb3Gtda6KgUIcZqzCR3hU5gfRH7dBya+urIYbOIv+O4Ev0EjcuK9Th/Ml8y4\n2lvTGWXdjGwOBhDbLc8zV5fgU9cHr5MhX3YSvMJ6U/tYAD7Qd3RSJKi/dFLi\nf5FduFnvkHbhVwIbJj/02wcUo4P6tuOM8Bx67PKBdDEqjGdlx8mPuetkDcRL\nHML6NEYGZs33G6f3Vsp99Lbptxwl0K9m26tqZnp1Phmy1w2izja232WhIQf4\nKd1GqnfkzbSf/fntb+x5M1hH+4VdWWaVlJIzvHaC+mXuix2lRiSzJQ6csleb\nOVi5cUZaSH2tttpeKD04ux4uJ3oN+TH/JfJZPPRSAYcg3pC2NOdxezDfydZp\nkYicj7xeFIueQ0gZOLbNa1OGxsq8+osUgpJoGKV8qhecWT3yzy8UOhbDITlw\nwAfckV7BU/vZgqndMaXL5xrSxE+0qSZFeEi/GE1pQm2LY+oTVdtbuI6YXF4+\n642Y\r\n=oQSD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^5.5.0", "browserslist": "^4.12.0", "@babel/compat-data": "^7.12.1", "@babel/helper-validator-option": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.12.1_1602801650833_0.5419136049523621", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/helper-compilation-targets", "version": "7.12.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "cb470c76198db6a24e9dbc8987275631e5d29831", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.12.5.tgz", "fileCount": 10, "integrity": "sha512-+qH6NrscMolUlzOYngSBMIOQpKUGPPsc61Bu5W10mg84LxZ7cmvnBHzARKbDoFxVvqqAbj6Tg6N7bSrWSPXMyw==", "signatures": [{"sig": "MEQCIHJfEcCDiMejW67GwalGWKOURrGBXHGrculRl8c1Pw1BAiAI1pQhEt18ZdGN2Cefgysm0g2Hk3MJJOyiTXNDoyi0xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodr0CRA9TVsSAnZWagAANsQQAI2mWiftt2gTiihuUpWd\nIsxmEqwAI9OyGRlf/+PRTocqwdC8k9GDYg7mtcEehGGaN032gzS0q+aUVbpN\nx7xKkg8qlEHWBzaidQ/ppEo2O/z3ewSeqiVEOwQRkuw1OmESz9KAljBKsovz\n525Q2n4QrOOcTMVHRoX+w+PTCFVJDiblkJaOITlQu1qIjyzATJg8MCMtrZ5p\nRjHh4n3txajHhdQFIyaPJqaiW9+r+yapDAwi9Pe15K/yRx84pBFHuNAk7cxx\npBfhqNg8HXH5e9P+JI2X0NYhsAGiml2aHmKSMQJBJuGvGnS1FMH5fEPc/Nol\nZYVa2frIuIhH+5MRvAOAh2DaqB66v7HRW6aOPfUg0pehBiyc7KMvq8wwjU8y\nVOnTVoC1ZArQbvKMyI3TlHcbc8salQjTau8H03z/xKACbI+lpUM8vGC1cjGn\n5gDuKn2rfWfE4m0sgIo4R+3WSC6XP6CWJmZkXZhb9vvVKACUzD6Q2h2W867N\nzBktGRVAHdK2w89dV/+uLUK+9SbkRoCXvuBZ1vAliVEDx2hmUK2RGj/X7CpP\nXrYVgWCDcgwg2oBUetqtdDL1AAFIZGKKy5nSIB7AYa3iIAaJ74//maDj1YDJ\n8xEveLNN5CWSUKGRYvzddkBly0yavJBohe+I9hkGLGem0LnV7H6R1qsWVO/U\nMYRo\r\n=FQhZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^5.5.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.12.5", "@babel/helper-validator-option": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.12.5_1604442868105_0.7379161351654244", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-compilation-targets", "version": "7.12.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d689cdef88810aa74e15a7a94186f26a3d773c98", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.12.13.tgz", "fileCount": 10, "integrity": "sha512-dXof20y/6wB5HnLOGyLh/gobsMvDNoekcC+8MCV2iaTd5JemhFkPD73QB+tK3iFC9P0xJC73B6MvKkyUfS9cCw==", "signatures": [{"sig": "MEUCICjvBkrkq64nXmMeGpT6ycLEF6Opd5KTjWhcuGIiDm78AiEAjmdloezuRKZpPyZyUVUkFucbY1huTMPsx5avoBRI4qY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffxCRA9TVsSAnZWagAA1yEP/i61YslTDBFQqROVOEjt\na8C5ok2qo8hYZmh37/+huAoow8ZrznMWVbp2F88S+wJhXcyzsR1lKaqAqkUu\nPGtXoM8ItktNS7holSRXPspe/OvHHD76xv/3RZBXCdbsodszAYf7aces08R5\nQPag2pn1cvn58JrzoqHZiP3PGsEm7IgDDvpBJ1hdER30OwWWAJE/X/N99Ji5\nuWk9hwSnKcDS3Xlcy86yRRVJfFlcBgaQOLxvsYruggSuVPi9xsneoz8qO+3x\nkFpe3UZTZdLnVmf9hfDZ8wyaPRt6V4wNLECmu02B/E1YJ8wiOsvObmqqgdsD\nToyusTyxB2LRlxUW4dVS/80+1jcT7SHYhZOgNd81aRqbnPyq8miCRI3si25y\nlVumb20VvZylv/+VveOxVoP+PhHi5WUEcA2zIiCyuik7lk2U+aVWP+6ROrHR\nFsAFkrmbXmIvlxFOTL5m/xOXxdCJrUNNdBFJJn0ylYTY4ISjY0IJBMP80Gne\nkXMoGqZOs4W3gxQQNX+dps8uCpAaXsKkw2O5kYKgHpHms3mMUh/DrQ7qAMa3\nOLafohqFV/EYfW8oHxRe8MvxheannmYJsLtmwicqXwE310aC/b/DllvoQF/Z\nczI5qkZIldLuEsmwEhskqRX/FjmLFlgKZ7w3oPSGWTnQHNZ+sRwsip5S23YK\n5QaS\r\n=ndYp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^5.5.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.12.13", "@babel/helper-validator-option": "^7.12.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.12.13_1612314608586_0.1720510186300208", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/helper-compilation-targets", "version": "7.12.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6905238b4a5e02ba2d032c1a49dd1820fe8ce61b", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.12.16.tgz", "fileCount": 9, "integrity": "sha512-dBHNEEaZx7F3KoUYqagIhRIeqyyuI65xMndMZ3WwGwEBI609I4TleYQHcrS627vbKyNTXqShoN+fvYD9HuQxAg==", "signatures": [{"sig": "MEUCIF88BnU4Rn8JdDOwgKMUhC/IkbRfIXEnIKywd7EA/yMiAiEAxhHc0muDV/MKASzkmpO3z49eU6zoZlcJKkdwXgQJOsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPtCRA9TVsSAnZWagAAppwP/0VUXUVyF+ypj6uSsCb4\n8zbf9nBzIffslIO6d1H/pvNyZkHXHdx7KxSD/NHf748uMymPL3JJbv2PMog5\nWVTX300HLBRCONfbjlJ0+v60Gpxw8utNtM0OrolkehEGm8sUyl/THOhSBZf2\nU5agZ4Dnu5QWItEEaMrw6t8HoGq8qvfIsp4XhTxgs1IZGWU1ewE1HOomRWxk\ny/L7jyOI3txKvFrMlYIpL2PvwFXN4OqFJD3RPy69Jn7beFOgKeR3Gv+ISUwp\nmBAkW8jlBq+D/6cRYo1WghBMnxdG7xEVHexpaKqtSDvqggBVSoINR81VCQam\nDtZDFeTppiWDULpm7S5j/LHYV5ojyC8edTbJlSwitVCT+aQtPsC6Vu1S3mMC\nJo+fJiKJn5M1I9dbiHuF5B8H/5gAimIwFXC0MVjd6N4vCZqPXJ4BhbnKDWJ3\nyUwoJU25P26B4JFk+60Po0d5vBillkdbTl8KFu5//ES+WdR4TwZPi5FaIYfj\n463C6m5f4MvZoULN4/ZPx8ByFEG90qsgEO97CiUv8fWj6ypfGnNvd8Jd1yPV\nELrHO8eiqRmGdImCji0OX8iZrKVz5JaWQoIVAYeC+ITEYmp69Ec6orVRUrPA\n0/zb7XN6036d3qTJIj497P0WZRuf0/Wj7ASTbyJyEkD0qDqi67GeARpyhiUj\n7cqR\r\n=Qo4a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^5.5.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.12.13", "@babel/helper-validator-option": "^7.12.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.16"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.12.16_1613083629173_0.681844914057038", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/helper-compilation-targets", "version": "7.12.17", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "91d83fae61ef390d39c3f0507cb83979bab837c7", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.12.17.tgz", "fileCount": 10, "integrity": "sha512-5EkibqLVYOuZ89BSg2lv+GG8feywLuvMXNYgf0Im4MssE0mFWPztSpJbildNnUgw0bLI2EsIN4MpSHC2iUJkQA==", "signatures": [{"sig": "MEUCIQDl5yun9ZHRaLNc0ai2xgULk0xSilsZ9mh8ll9uvNeSBAIgfwbVES3fz2iwI47svql4zavW+roYN6OOZU8V0BlsRqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoP3CRA9TVsSAnZWagAAY5sQAJhtU8lo4uJdZJDy8u7p\njx8hQJWnJpX57QDBAgqO82MopJ8ga9w2eK3hS6P/0ti7qaS+v3iAIIIi6bWt\ndc4PBPg5LjnDaUWp31KBwFI6NelEdLhBdijChoV2E9v32bv+eSh7MV55O7/x\nQzamBcHV3GPNoACIfqO4TNxMsyGp8J0RlKlu0RTuljMMBRJ9IEH/Al5Pp2ZM\negUoIeULhgnUQyesPbyF21aAb+SM0f5NZ7qXnrVJYesa4V/fIjtTtce5YuT7\nTzF65QkngOPUohsIbDyzS0fqFfwPREoSthGtLeAL6NV1JKX+ayjV1UNWrJne\nJEuRUrgggtQrzaev+iPt4WnutEob4W24Sy8Mn0bOsxGMXDXs6ySDlPwlga4R\n+q2WUDfCUNFesvOzrBTSX1ixpKkMfq6K/1hexj91QOvhe9buM41U7vl4H+9q\nY41Y4KWJPT3pDdVrdsSxf9LtHyL4KbIamByYlYLarmYLKcYgS0XgckU9xzEa\nwZvrMWH+hafglS+AcfIhoPMotCQQgs+iLVAFiV7THlrFdBf15IxmLfc3gRxF\n0+FL65qg+aT9KFCjmwMv5GiHedPVVYWOwtBgYZ9kRZCBvXxjUbtVmnso3t+u\ns/ESO3NJl9lHGk71N2BVcy6tMJjP9Lg1SUV3L76O19YlY9LDTqRG0+gpFNNy\nma0D\r\n=Qy8S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^5.5.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.12.13", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.17"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.12.17_1613661175030_0.43859425373473715", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-compilation-targets", "version": "7.13.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c9cf29b82a76fd637f0faa35544c4ace60a155a1", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.13.0.tgz", "fileCount": 10, "integrity": "sha512-SOWD0JK9+MMIhTQiUVd4ng8f3NXhPVQvTv7D3UN4wbp/6cAHnB2EmMaU1zZA2Hh1gwme+THBrVSqTFxHczTh0Q==", "signatures": [{"sig": "MEUCIQD4gS1gKdtfG9SilNPp2Sb9jwhL/12DOOO4QaKWxUrTGQIgeIW/R5OJqS7XtxwO1Pppqv10PLbRdQnxDwJ/muaWQec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUKCRA9TVsSAnZWagAAKYcQAI+3U1EXm2BcbG8WCwPP\nv3MJ2PYOWKmNFMVnaXbgYup9wAw3Gvp60y4Vkg3mYxemDHZVwaQWjkPLzBfg\nRIgcr7H1A08a3vCwZlX8nirxzk6a9EnyqLuI3VvkbxvS6WfIp1I9L41CmQZb\nBkpnloTb0J6tfcaDKFL+y+gaoZ56C2LB9EYLoF21YFuBtChEABGEJDs40yum\nQnTuCzoF3q86821K5gB5RDCtBc+mb3Afs4RekwyXl1SB/NzM8rtoOK5fT949\ndOn0ZpfB59H/M4velrKxG8a7D2saoc5lcKotexezVjfpMtBoXrp4mszPC3Wa\n6KOv0zEU4yOq4NrdiT6Nuei8NWKH2zhMhT2kvFvamwl/d5mju4gwlNy9LqoC\nyzPyGC7a2bmSioxupGlY+ilrJvqRi3sCqQEurV6zNLWMYu4XTbZBokZaL5HN\nz8rxP2ZsiRN5BaxMKZcgeNnCloEE6p6c54+svRgXKQ5QXMvxWz1iaTtOT5Sb\ntbs/q8ShPRALrNy9KNvgpXm9aj5kgioDDiSRWdRd/jOdD0kd49Qw0KFrDLOt\n0mA3e7w5C6hfMJPBvO/uIMiIgaFKsvIHeugdWA0+x4zpM19QqdDj/DR8zaB9\nAhHFuJFifm5+6Ayq4gjEmLV5iOPJEe67Pkry1YE3WmwysVM8FXS1khyTnbS0\n5QVz\r\n=ZUpi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "7.0.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.13.0", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.13.0_1614034185967_0.4996214972177839", "host": "s3://npm-registry-packages"}}, "7.13.8": {"name": "@babel/helper-compilation-targets", "version": "7.13.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.13.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "02bdb22783439afb11b2f009814bdd88384bd468", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.13.8.tgz", "fileCount": 10, "integrity": "sha512-pBljUGC1y3xKLn1nrx2eAhurLMA8OqBtBP/JwG4U8skN7kf8/aqwwxpV1N6T0e7r6+7uNitIa/fUxPFagSXp3A==", "signatures": [{"sig": "MEMCH1i4v4hkc43xkOr9Kyuylvhl2+vLImu/yJ7TSm3xyacCIEWHImIt5ckIfcBfabY7RS4tsjvgs4IL54nGfRHzJu5y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOYafCRA9TVsSAnZWagAA5akQAJb1ExZbevj5KstXDz7U\nHjMOlOV5letQ8u4SdGMNLMz12bDJHqbqm1YuuDDmf5sOgJnNOalKU4zvvFU1\nrlvFAIbPZ4SLgFuR5C6qqlxkj7x8Qr2CPsIYE0KsOhOltHJ0keDSTGYbIA3F\nsNj8d/JJHSnrUm9OK8ygWb5OuRDB5eGZkSSnw741pUbOyj6GgKYjeqkDf/PY\nLtccGdJZf8u+VAVqIwMpJ86vB1nRF+IuH60N5aaWaoPerLSoZao1IB0/SSkD\nOSwo6Cttm/Y6t5V3KM+iAJD3tKTy+Gp9MCIT8zzXlgFoO6LT5DkRlgItqF4R\nUCqYdJU6ljHvNjw9aJRiRqQFK3zQlsz9yA3QnJouonxliowL4aD6GN1pKp3U\nlIpALLiBVOIT8zokaZsSCfrDjxHAbt5BTLwsOv/N046zEv/5BtPhoQQMn33U\nseRQR1aAe63H17Q4d6o1MZzFcOxSp+1E7X5gVp+LJCzrAD2t83Zv+bEZAzFE\n9G4cG1q+8iaThO2dN5tzxtMUenxpoZoZc5bRPjBsAkQwPXGWz/G6NA1wQK+f\nPWhr4loUsBxQv8sR27PV7425U/iHctZx3+Krz2NiYgOoYwcIb7t9Kao65PT6\nr1uiuBBivMFUsBD0UNVD+lbnyRGvUOnVKRM9AW2rtu7/1sdQBTcii0TQUOpp\nx2R1\r\n=gcl2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.13.8", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.8"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.13.8_1614382751007_0.7698755809170845", "host": "s3://npm-registry-packages"}}, "7.13.10": {"name": "@babel/helper-compilation-targets", "version": "7.13.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.13.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1310a1678cb8427c07a753750da4f8ce442bdd0c", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.13.10.tgz", "fileCount": 10, "integrity": "sha512-/Xju7Qg1GQO4mHZ/Kcs6Au7gfafgZnwm+a7sy/ow/tV1sHeraRUHbjdat8/UvDor4Tez+siGKDk6zIKtCPKVJA==", "signatures": [{"sig": "MEQCIFOqgu9KSGkVKdgwfLboAcovIVUkI5DILz7A9ioAO4SvAiA8RlivU9Ebxd9KTcbaAb6YagAXmO9ZYBnMKmcdeu+dFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRqbWCRA9TVsSAnZWagAATE0P/0jqJEBN7neR4+sHV8dh\nIAUNnvoz+nI8er7JH0RW7sFtyCoa5d5CMGdlho3t3CTJc0gQ3+uDYWttIoXF\nizRVm68d6H/J2JM3cqXrVdjzvezTmxjhiaBKm0OVngSXxkBin1RY5+Um9700\nA1xdi5ORJcGRfbxsgoj7gz4JDUofEhgeGVsqZbBg0QH1WGsKEImSnjDgALbW\nAFVgqhOvH61AY2BS+lrcu7+BTIJRDt6F2ZP0gjScG+biny7wxs1/vn5aXpna\nAPaJRo8UfVuu8ycxWHikEdSywsI7OnTnci56cxrJz4tfcxZjyKpbL53GP5hS\nblk+WgQn/ScT4Hjf7UsY1RF4YhfQf7gzmDG5RkDXFBFcAA+XQ9b3XAqBzuMB\necOqtWyvYvNXKrMjqxrI4h/arCHnM6I/IkJV0gE9f7rC8ElZr5aR4QnWxy9A\n197AwPvQAHhctcUm9twK8/n/bgZZijHRx9DSwYIE/d6VMvJ8dYSFEhpRGE9U\nHaoFMrSupsPnJl89TsDKrM5FPqdTb6j94SteBt+YFPPJgaBDfWT6+qpZPXK3\ns0/wne5tzNcsd4xo/3xXYERpJwYnKmUsn+ZAjMR4MXKw+w3r0o3OsYtjicoR\nS+QnJQZozC92hPmA1o7cOswofyJz146p6uh/K6Qz27EEvwnR5q/7G6BJRwcf\nc6P+\r\n=ZBS2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.13.8", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.13.10_1615242966038_0.18822970178240173", "host": "s3://npm-registry-packages"}}, "7.13.13": {"name": "@babel/helper-compilation-targets", "version": "7.13.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.13.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2b2972a0926474853f41e4adbc69338f520600e5", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.13.13.tgz", "fileCount": 10, "integrity": "sha512-q1kcdHNZehBwD9jYPh3WyXcsFERi39X4I59I3NadciWtNDyZ6x+GboOxncFK0kXlKIv6BJm5acncehXWUjWQMQ==", "signatures": [{"sig": "MEQCIBRw23XKqyi9zcg2zJYK5MuijoeOGZxQiboF7BBAy28kAiARYixS+K5nPA8fKFzzitCFgVGd61jTVVbJqZBMz64a1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXlAVCRA9TVsSAnZWagAAd9QP/0HCdi6CaS3bfjYTaBeq\na0Ixq4Sh0SjcmU9pSI3c3rV7oIAXgOumJl+WrY6Z+efaZqO2TBqlAj631FrR\n+ale41Ma5QF8+o80PQobtQ0gYnAs7/XrM9AdOK9MVP9oNCBHTfmKD8//wAIR\n2BEF1KE1hmI2+ZLaGI0PqOX+9fHAX/KtDYHcVagekqbpQ5izE7qcZhJ5jbLC\nhO1D3Kj9hzhnqg633eX1i73ixTfjQH+fKfmW2fUDZeEFQo624XcJ9cpTdOA7\nngjK40V2vHjjrUb3XTNl1QJMMD9enC3SaIR7J4eB/sUwPQAA1xVC1PV/k51z\n3din6A/Nr68nC4z2cco/zLlUbtNT9deEq0gXU2WIdVSpcmiaBRqIa8wWQDti\noTvgMcqF45ayr5MWQV8cmeCRVTdWTpcGc72ePJjszdfc7DmjE59ceQFXLHYC\ndwlSxEoCWz3onbEBXbutjJoxrhkg8euAqryxHuY+ZoZyX2XCPkZcbtl2SH6i\n2Oi0p3HF9ApwNqYx9WWoSVFEN5exn4fSaLt5NHl64mayKG7NyGdDaNCP5ePn\nmfHlsamXT35RD9rWtRIxko4GMIXbRaUCv9btsFHp2RdaYPBzNSfOn0nd8Wim\noQ++c7a/oJJX0hRlIDc3s4OCWIm0Fzq0gJV28rJIFjtw6xAYZPglz/UlLcex\njpMd\r\n=nrhC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Engine compat data used in @babel/preset-env", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.13.12", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.13.13_1616793620924_0.47171041608838493", "host": "s3://npm-registry-packages"}}, "7.13.16": {"name": "@babel/helper-compilation-targets", "version": "7.13.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.13.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6e91dccf15e3f43e5556dffe32d860109887563c", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.13.16.tgz", "fileCount": 11, "integrity": "sha512-3gmkYIrpqsLlieFwjkGgLaSHmhnvlAYzZLlYVjlW+QwI+1zE17kGxuJGmIqDQdYp56XdmGeD+Bswx0UTyG18xA==", "signatures": [{"sig": "MEYCIQDa/Rtmf/aJsynn/Z788bv91urT/yFS7Y1zwnf/Vt5qoQIhANhROOaD+0y2VJ6/WJyhP42FJpQciSSItG/aeTXFeclr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfrkiCRA9TVsSAnZWagAAEBoP/iu/XjJSPenXqhB38poz\nRCQ9ZY7KbrcpmX9x6LmrrNPF/CBRwnvOTjG9i59SVUPiVK3F+utGydMDyh8e\n+0PR6tUwgQyyIjPFSeLxnhNBbu0PqWfDubOTYrQ0vf6jpVhKbj6Bhftmzxn/\n+LM88RE6CMT9NpMYyCpEYHNQsVYC/FU/L9xpXmYCWaT9MhghYgEqYcP7slrU\nQiY0ps+xFeu2+nNePmj9nGktru6X6gsxzrlS7F++qEq1v2vHaPcx4NfC0Izz\n1kv4ZPJ4QSVJfAS8qMMyByv1VivRtt9hksFjaTlOmwsmReO3I0me9vFAnpjC\nmK2Jc1Y0xrnEMaJiHBSR03a1bGA+ZRHsTIpohIUvgCB8caw93uUW18TWXxbr\ntkp5ANyGCTg5qs/DrMgQ7QzEcOqTUhDmNdE4UPJNLMFl0JgODSS10HwNa2Fs\niwJC3tBpsS1rabEz4ORDPcryDiHAVRUVFGdv7nQCagZfVjdgz/YUmeJurtwv\n2MG/MH+QCEJexxmbHpzMPpYzRbS5S6qtFsftml05n2RLS9fDD9BAIuSZG2GH\nI3wf5o0rg0K2rP9ve0sgNZ4OzjYzAs+mnp29WmFJYZ2XuXsUn26HYwWDiGbi\nfd9oZNo+VhZ71ozxZkHe5fL9OSPa2d9TYSV3QN/Uhlq9qmIz2WqjE8RBg9zy\nDgIy\r\n=3WWH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.14.5", "@babel/compat-data": "^7.13.15", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.16"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.13.16_1618917666194_0.007594047949588401", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/helper-compilation-targets", "version": "7.14.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "33ebd0ffc34248051ee2089350a929ab02f2a516", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.14.4.tgz", "fileCount": 11, "integrity": "sha512-JgdzOYZ/qGaKTVkn5qEDV/SXAh8KcyUVkCoSWGN8T3bwrgd6m+/dJa2kVGi6RJYJgEYPBdZ84BZp9dUjNWkBaA==", "signatures": [{"sig": "MEUCIFZiRNHSMJtSrNfnV2ISCklltHP4kxC077juBSj7SoiPAiEAvv9WgmiVbgyjAz3zbDeZPRrBKz0ueqXu60aHOrjEBpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGQCRA9TVsSAnZWagAAfDAP/i7vzeBOok2imWN6VdON\nrW2nQqEJqCFyXHBHlkEId1AKKdHZOqobj9imIX3s/nJivXSS9l1e16to+RSC\nmYmu2FsfdFyO1ebZI9p52KvHF+vzvlNo7pMJzRHVB4XVsNF2Oo0TLctYHedw\nepaRulgkWH2Lcp/6YJOHFqC/j+IsKzpwctbUTxOR9bQrQK3ipsvgEBIxB19T\nHzAZQIICtAONzndRt8rRWRyi/kWotMvTFMdl1FQC6u0tbDVN+tlDKfqSR//0\nI7fea7P1mfztHXUn+KqimpeBpfQH1A182KFvj3bOP2Ju0ceP2w+P++8zDD0t\nUsOaGdyBi6xeMGQnvVqnrFK7XMmbgBwQ0LS+pcMFkI654YmbbKgNf67IxOPB\nOuhJ4ohk9QeoyNkZHQ0BP2gNS/rDbpxFgZSHkPvA0S0/xLJKNfEwgLl+WRIC\nTtsNqIAK+ZNLPf5BPmEuJWJCZqxh+1fUc9oEiJAGzZ0vh37Q9sAl4Ua3h4v3\nAmG7xWqo+zKkSfiI3og5cKL6vJAOD+a86RGsnfSqzwSAYA6PbcAnkjsG3+Xq\nc8BnFHtkhoF9dWTBrtPTnprVxeElZ8BsGiJSXLn9aCa6qk157qo56Mmgdkg8\nrwXHsxVZL2AZSduGiNxPcGCwGqCu8pts3fv0Vudc78oDvpn7ljZDEHbJWFos\n3JVh\r\n=ivx5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.16.6", "@babel/compat-data": "^7.14.4", "@babel/helper-validator-option": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.14.4_1622221200551_0.6438076036171132", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-compilation-targets", "version": "7.14.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7a99c5d0967911e972fe2c3411f7d5b498498ecf", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.14.5.tgz", "fileCount": 11, "integrity": "sha512-v+QtZqXEiOnpO6EYvlImB6zCD2Lel06RzOPzmkz/D/XgQiUu3C/Jb1LOqSt/AIA34TYi/Q+KlT8vTQrgdxkbLw==", "signatures": [{"sig": "MEQCIAcb4FFrOI/7SJhKbfLXAwdoWdv6CmA/h9VIYwb9ntvrAiBrqcvkeAJDJC9hR8fddPkb0mrmkY634LsCUEGa5Q0D8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqjCRA9TVsSAnZWagAAQmEP/jxq6EzBmGdCe9Gwg03b\nAAEdYRQXWOkBaGTCEqsVl707xKaY4LP84j59oE0ns5fZJZ3Y0ACIVmyqhkrP\nu+Qyp6J5P6qSLtFguoOQ/r1YjMPbwZQ9130VK7S6Ve72j3OodIEItqEF8phd\nBdlTby3GVfWCJogFaeciQRaRffFkz9Kw0AdMUQ/Au8lthuyRJ11ATDmQ5Kr+\na8C/55ERlajZhpYr1QK+j/Vg9DgQuVD5ghrc0uI2lmd3q6zU7eqDR72q95Mk\nxlTQIJetPcwKTyCkZhqa/wbWPluwbVByiSfLw66Qje3KPhB7WS4wO6IfHsUp\n1lEdqkiI/27Gj9vkps6SccRLJOK7O1RNI0EVOXshsLnhDbKPWIcmeiw/0s7Z\nWiq13QllT2hSUZ9oNsEhjz6SD8pmrrKsK0A1g2XFjWXya8crpTWUmRJj41lS\n/FdrGXvfBTk/Ao1QHMIjsDc0VuHOIyz08Y0MDyDZFeNrpO2C3XnNS5Je4Kcv\nK9xbKNF4KByDLWYBU8k9/M+/BaOXGDrLpb+UGWQVZAQZ4ut49K0JNeGcS84Y\nI4OrSA8gJNxypsiFg7clyAxIZx3z9lpbmpW0RElivDKQt3hSC5ipvJ8d6Xcb\nRFso4oDXTddL7jcdUzAgdqoc079DHXbl0BsI3tvnZw34grPTtSmNHRntPfOh\ngJl+\r\n=xF/9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.16.6", "@babel/compat-data": "^7.14.5", "@babel/helper-validator-option": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.14.5_1623280291175_0.5300578094237693", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/helper-compilation-targets", "version": "7.15.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "973df8cbd025515f3ff25db0c05efc704fa79818", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.15.0.tgz", "fileCount": 11, "integrity": "sha512-h+/9t0ncd4jfZ8wsdAsoIxSa61qhBYlycXiHWqJaQBCXAhDCMbPRSMTGnZIkkmt1u4ag+UQmuqcILwqKzZ4N2A==", "signatures": [{"sig": "MEQCIFXoSUOquzcUba4MTGd0dKO/OSNqC0+8xFcHqOgZ9zKAAiBMTGI83Kzd21NUKdHp5ZDMKbfDVRzZwle0rpi/2SGeWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLfCRA9TVsSAnZWagAAtuAQAKQvM31p4hFedHaJQE7t\ndiwjv776MNeGt9y8r6AirtriEGbt3ZIVY1PXaPfUZv2A//22v7Kd4IjztvCJ\nlezCeemXz94oFnyxWODzw/feZrGXjcVWse8E4/L2brrA2KMwiBE1mauyxbhW\nXg48fw2mqvzO/mgL1ZMMwExda+K/YOBQKUrLNdP5Pneqh3rFYurIwhvwWDoM\nUEQ7CgK3KLUR0rT1qKl5OsbfoTBTX02AE4Ui69MEAMUMRXW7xo5e+rNPfC/z\n67asFaUGK7P8YGMxYbYJ4j/te90fUXsYmVrtlHrMrAMEyi8gesPrq1/B4piw\nmc/t5WEPJunFlnrgQr8/PWxVFZETkGszq4BKZSAFid/F/QR8r3rPJZ4Rx0so\ndR5xIlwCgonDCXMadN+K/aZouEh/ubP9Nk/pGmkTZB4ZrhFfJPBgcMBUHKEW\nhtTe3PVfFKS2nFQaFZW3Eg/lpMk5DZqyib6pYbFC4pRlAKo5YjMit8IotmcN\nGQwXwC6vCjZuZjlywPYKZB7LJIwniuH8I4uYFvcsjyN4XyBvglx0LII30hXD\nxI3RA8Sn+dKHHORCfaQCYzV6xKRaQ/aQhz2fRHDicFaXy0D4RvOA21NzFBiU\noom/yEeEowdl1stghhwF9IRdOTILT4PxbFXlWaQ4G1VjHvxs9ZaGW/AWn5wr\n7RJk\r\n=TrNX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.16.6", "@babel/compat-data": "^7.15.0", "@babel/helper-validator-option": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.0", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.15.0_1628111583789_0.6651041293550615", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-compilation-targets", "version": "7.15.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "cf6d94f30fbefc139123e27dd6b02f65aeedb7b9", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.15.4.tgz", "fileCount": 11, "integrity": "sha512-rMWPCirulnPSe4d+gwdWXLfAXTTBj8M3guAf5xFQJ0nvFY7tfNAFnWdqaHegHlgDZOCT4qvhF3BYlSJag8yhqQ==", "signatures": [{"sig": "MEUCIQCvZvvaQP7mMdufNytC3jEmDxxgEu91uoovAe0OVMr/BQIgcmrJf7PlO6Oa+kQM4n+7kUVK+o+jrIIFG/6qwVq2vgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSICRA9TVsSAnZWagAAQrAQAKBD7DpJJoSoXN15R1t4\nQrQ73aqNOgtBFTmbI53+3yDdrDIIqqF4BItoIAWwEZ/SbimBvVcEFFT+M+hH\nS3DlhfRY6oIMoXf7GaomH1UzwlM5kLwmIIG39p1o/W1pQOyaT2MXY3LkHWOw\nx7OtIsNLumaz64y6JMyBiXSJWrps9PAUFt+zOHvcGsLYuwM6Ul41l8NNBoio\ne/DAYptt3ce5cHa7Z7s4W/I5/H0mSAFQv9wj92bqIRxY2RZfVTiWl/Bo1Gu+\npXs+DSsw3JggAg3Knt6aw0kGy0Bpk2n5QQEUKrobUl99cvsmOQaCHnLFznE2\n3zV7pcdfGDYmzRwc+tA2FCrfnPEBAe3M0l8zZ1MatVlrhQ9Kfw2w7C5qpzFF\nvAzj7AjxxrkH6Nr5j99U1poNfixelfCKcOa2+ZEnhzgqW1p4Bu8/2yg3ZI0t\nhH4Jg5Jf0YPTb9C8ZloZrWCTa6Z+n8q+uFvUvIipsoDi1XvhY4ADzyy/mWUz\nCkCyV1L5tNjhbIPup87k7GyQg9uVej7I6WgElXa96trv91m/oAZHwiwVp7NQ\nuMWeVn5gdIwXojz7vXFVN9OErHfxPLdSoYoThCos5dH1c3tZEfUVxx+Hrxqw\nolX4XNPX5dhDsM5PoUS8Gd9KsYkWDBIOfKFNi+mqwzo8v8SZtbl0tLDsJiaL\nldzF\r\n=Fv4v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.16.6", "@babel/compat-data": "^7.15.0", "@babel/helper-validator-option": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.15.4_1630618760591_0.5167233983276152", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-compilation-targets", "version": "7.16.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "01d615762e796c17952c29e3ede9d6de07d235a8", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.16.0.tgz", "fileCount": 11, "integrity": "sha512-S7iaOT1SYlqK0sQaCi21RX4+13hmdmnxIEAnQUB/eh7GeAnRjOUgTYpLkUOiRXzD+yog1JxP0qyAQZ7ZxVxLVg==", "signatures": [{"sig": "MEUCIHJICW3r8ivDYrBHwdHb/b/nFtcljeUjmMy3K6n7ponkAiEAvJ4JdqvgNtvMAqfiZtmk8FjwJLeDXRL20okgVjenoBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16792}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.16.6", "@babel/compat-data": "^7.16.0", "@babel/helper-validator-option": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.16.0_1635551260811_0.0671661171245963", "host": "s3://npm-registry-packages"}}, "7.16.3": {"name": "@babel/helper-compilation-targets", "version": "7.16.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.16.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5b480cd13f68363df6ec4dc8ac8e2da11363cbf0", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.16.3.tgz", "fileCount": 11, "integrity": "sha512-vKsoSQAyBmxS35JUOOt+07cLc6Nk/2ljLIHwmq2/NM6hdioUaqEXq/S+nXvbvXbZkNDlWOymPanJGOc4CBjSJA==", "signatures": [{"sig": "MEUCIQDKtDP9gVQJS6h4t88QI/rLC1x/J+R51I0PYnwvdnwP7gIgant6Az9blKtpoBH/3Z6V1AddpWKm1aEF2SAOIeQjsYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDLCRA9TVsSAnZWagAAXCkP/jiJsOEHU+4Lk1ttlWP9\nXhDFy0CwBz/pxLKYgxH7wel16Ft5Idua9cwqvFLj1NUJFH9LUsU8T8JtpeVS\nYkjM1fkO1DDj9j4LWBgcRT4WIjdC0OrafCdHrMhqtvslh7Q640i0O9eGs6VC\nqhjbWKlNJFyOyPpiM75lmuMKNLSa8p090f+hyjXRy3D76dE2JuIS6a8i9BVw\nu//85XvAOVDqmivoFDg3GAgLLADk6gabe46B4qu9pgJ5rgWyhFq14+Jn8YIx\nYn77G8I5OD/nfk9gjYv/8VdeVYUKq8StHc6oHcWT145XN4MhMSU+AIRH5lCB\njWAbqK1zaS2mKf3ulRSoye6FYKHghchbLM8GYyo35Q9q3AILEQfHtHhbmER6\nXiN7sOQlSAjDKC5/pC20HW2Sim3Cz/38xEhOGp4R8lhPaLvHQKnisZCgqq6u\nE5tPrc5n18eAEvoPnNFjArRNuk5+yFAZAtlZUFdyOGFHczhVC9TytT8NKYzS\nx92wwz34ncZjD4l+O0MNN7i4GlSZpIoZ/N8shAneIS86FObD7gtiaY4A8whq\nrTVmOcShk8KMuTQ+ZY0tjRzQVlrO8g//zPinD4J/HN2SqoLRQd3Vy9u2S/3G\nivA5vaQvLogVoCSqOdyL6r8dO0Y4BG5dxKTRoSkM3kiwEps5LWdN2ahvJDOH\neegg\r\n=RwF3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.17.5", "@babel/compat-data": "^7.16.0", "@babel/helper-validator-option": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.16.3_1636494782958_0.5882345464065408", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-compilation-targets", "version": "7.16.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "06e66c5f299601e6c7da350049315e83209d551b", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.16.7.tgz", "fileCount": 11, "integrity": "sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA==", "signatures": [{"sig": "MEUCIQDG5QLUd9TqFwrWEZB84jhqC2La0ROOX10Lhn1plJvCIQIgfmQPRxAlw75mT6qRCpHklHzocs+kgefyyhoX12xhnYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0KCRA9TVsSAnZWagAAmjMP/ilDAG+i5Q+WwS523JCw\nuLVLmuEZjBAGh0zq3gQHc4XV0QpwhYHkk4dvgGzrcHq6B1GyEISzjm9lk8JK\nyeelNMAAHq9BywwNI/QRfrJ6WaqxV4OhcTX0MvxSR8kgTnpe2VqzZ9zvm+ZF\nYxLZb6PSAHo0g8KF3IXZCrgolKGBmlvkBuJomAaY2ueTwHwLIKsJJUSEyTFc\nJEa+GvUUrNapgUUjjtS2+5CEWxShyV7QdGIxwII+wa/qgeOfWeQEKLqNRiTm\nTcqPm2IX3nFhex88XAG42EUKdQETCR197rQ7dxj/ebTTGpM6yZ3zxcArQkFN\nD4ybkLkrhnuaCVqABa8Jp439VvqpYGt6BAgSevA0bDimGcGxEJUxikuYvHx1\nAJ1NyC/EyT1aTQFkQYXBtI5DAyXrNVdluR+EoYFbun8919L53goTO/vXBe0i\nEmQ15/mRDRB88enq+MV7RpmgbuvLu0rdwDM+1fygJYFnkzy87U3uaRaj+LmX\nwoDOterNaxVSC6g5Jk/fTsDPyhZ+bTQsPRva1MFy73zXP7SV4NcmduYGhzcZ\n3oXF/0bvYs2S6qPRDsEKTDQwGxI2jq+rjAny09400ExIGfv2LdnDwe5fCpA3\n3/z3imwf1FMyQ1+QeiDiLNUqTv6EpqyZxGIg9jYgJWxXbQdh12CcPcwKscyB\nbN5p\r\n=1f2/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.17.5", "@babel/compat-data": "^7.16.4", "@babel/helper-validator-option": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.16.7_1640910090374_0.6457577733833824", "host": "s3://npm-registry-packages"}}, "7.17.7": {"name": "@babel/helper-compilation-targets", "version": "7.17.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.17.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a3c2924f5e5f0379b356d4cfb313d1414dc30e46", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.17.7.tgz", "fileCount": 11, "integrity": "sha512-UFzlz2jjd8kroj0hmCFV5zr+tQPi1dpC2cRsDV/3IEW8bJfCPrPpmcSN6ZS8RqIq4LXcmpipCQFPddyFA5Yc7w==", "signatures": [{"sig": "MEUCIEeHT8g+8oe3RsDiRO5NseGJnZCGwFd0mq73znnJlTSwAiEAk0ZKrcIObi8IXaiEyg/qNy3g76/Ku8ny3z4grXWdHyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3ZAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnLA/+Pygcyq4MGwOqiYbubBcbUAsEZqWz78dS6hWu1cD479JDnoYJ\r\njpnrteym0UirQG31jFgH9IKHczM+SLIWKmYSyLA+gcITs7RyxOxvEV2ScUQy\r\nvAhD4buewl1662170QrNifrWybhEJD9LSurX0PwW475uFLkf7AGlkCVOjAmk\r\nBHEIjkS4k89MXKTNz2hD1b0qL7WbWXukql24cans40uGEHI5uxEiCsd1S263\r\npLHNSHxXDreGm2fb80OtF3thEyUItbocsnMIw9+1j/CFinUkY8Fj6jyckpTl\r\nFYUq0iIwjdSLmwqlz0pxz+94S/50zD2DRFWgHoLqB+OtKy+YR3serzrQzfot\r\nbr1B5Om6ulNy+oOx9xonRdlv5wMPLQzCRGqPOCJpBpFeZhW7GVolZmEW2cBo\r\nir3gllUztK06Txu7QjaeZKTxIkqY5WCQGJF3Kk2JIxWJRKxViQ2+kcELAVxT\r\nD9rbBOAHqWbF/4qCFnEDIUnf7qPeLxqdCoToQJRuS58fKFCYLKCIlFdZR4H7\r\nGNKd/HsDYnhy5i3CTSnNggaFmoOa9C8ycSRctz+kayO3itP2B6RvUwWr9Gsf\r\n9ep0us2CFBr0daNqW2DW1jkkfDE7371TB2wIbrVJlzlfPnyUUcuAI4q5PGnW\r\nA/X0EVSpy++f/eh8aVN7HlO/zIFVEhYQzAU=\r\n=wJsk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.17.5", "@babel/compat-data": "^7.17.7", "@babel/helper-validator-option": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.7", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.17.7_1647277632628_0.5521359403628245", "host": "s3://npm-registry-packages"}}, "7.17.10": {"name": "@babel/helper-compilation-targets", "version": "7.17.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.17.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "09c63106d47af93cf31803db6bc49fef354e2ebe", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.17.10.tgz", "fileCount": 11, "integrity": "sha512-gh3RxjWbauw/dFiU/7whjd0qN9K6nPJMqe6+Er7rOavFh0CQUSwhAE3IcTho2rywPJFxej6TUUHDkWcYI6gGqQ==", "signatures": [{"sig": "MEQCIDj53J7o6IMV/5Kz+6HHDzKQ2PVl0rGrkqJ0hQvY7R0OAiBg//cDa40ORfYDW2EGw5UDClgqlbCWp3XTGiVyMiljsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp03A/9Er1NUTc6cKy1jXaVQnjWbf1JybvZok9IEWKD6dhQa7Q6loWh\r\nnw9Ho6LDjTqz7wkgj97aMPlex96SZQf3I1Vv8ceDS04kr+Ap72zSjmGaxorO\r\nzcbRSGpOaPNMSdahVsZq/SEVBtHA9CSHCaFdgbIFM+6j054FcCW5zFoyqbZ8\r\notV9/O1J72892BoiXbo2s6cj19Pb/D42zPhZDnfPLW7360Kgg0LGX9W/Ct7L\r\nv5JkwSx1oIJ7KKa50A+AwfAv5J5TFflNrmiC3L86g77hciAYRLUK3n9slSpa\r\n8txqFHJzSS4C5Ocmrbs71n1JYAWElnTBh2HXDWZF+mKEEg5ctj1RiIHFhJ/U\r\nK+w/qctUndKmdduGIZOokjZHwNewq60aR5/A6tj2O3EdyX2pbhwJe4qjtasE\r\npa0AeSns/x2KUkuJEk0+eTccnl7gguPpzAtKtKtdim+LIvqst7u66Ebh8e+C\r\nTxKzhNcNRuUSCxwVv/a4tipEHAIKPJP4zy9Un5L95Ov8N2Xrse63I3Ani3Ox\r\ns5tw5IPvryQOSdk2KaFvv19rHrILs15v8MtThHgBbzmOhup3MGBeoMO8PilZ\r\n0l+QrRDy5oDTg0SSOMjvYPD075Zqdo+8EJRc2Pt/4t6JNHfwp+Lw9qUcRurl\r\nYGpVXE17lN1wSXy90O0klacipZpdX96BHDc=\r\n=131a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.20.2", "@babel/compat-data": "^7.17.10", "@babel/helper-validator-option": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.10", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.17.10_1651250261636_0.4194918633563709", "host": "s3://npm-registry-packages"}}, "7.18.2": {"name": "@babel/helper-compilation-targets", "version": "7.18.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.18.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "67a85a10cbd5fc7f1457fec2e7f45441dc6c754b", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.18.2.tgz", "fileCount": 11, "integrity": "sha512-s1jnPotJS9uQnzFtiZVBUxe67CuBa679oWFHpxYYnTpRL/1ffhyX44R9uYiXoa/pLXcY9H2moJta0iaanlk/rQ==", "signatures": [{"sig": "MEUCIQD3j4PQze9M9BZFjVMTpCwj4ZR/qYxYj+9GbslwABpj8AIgVjeHBL/DeOx+1ay8tp9I6PzgELI09bQeSOssfoVSNEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijfPpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrilw/+PNtk6Ygar1lreV3h9j8la9gR9o8MgWMncpfKqEFmr+7KoB+r\r\nYxz5N1gmWuBAnfpW7yfSmWTuTzpoABzaq1MznSdPbqeb4CgLeC2TjUJM3hpY\r\ne19LgyMR/eGuXHQhLAVwkfJVWpE24K2aqaJ3hzJ7cSchIdton6+U9MWAXjMY\r\nuhgeUbrXnrfqa5Emq8aNoTkBMO/DcSWdcWO7kEFTbsDh8aLwSA8y8bKdd7JZ\r\npc7wFpbh7uHhaGgR0hkfNCF+niUVYY6k8sKJEj29zUWkwUpnjJHih3iJ7WoX\r\nHqsFbu/lZf6kg5lSoN98dgRUnIvr07sZBZqV6CrR1aARYAsptBKRnJukxwwO\r\nmXlrK2hW2aT/EiL17fjgvd5Z3kt5HqFoTklhOAIXFqhtk3IEWy25XsFvURBq\r\ngTl0id15VFFPN3HUM3vJsJlf1FeeKQ5aZ47+hiNj2DZpFFflcYUvu30Zb4nm\r\nfbZe4qg0H2iQGjJgq0xZyEaiF0omTsQmAb93ppn+feNkfBB6uxurzNvwV5Ix\r\nxMUWNTzLYqBDCAH6Qv8eaQM50MOh1sz9GyRBmhPrRk6prJ+XMIJq1qv9HZMm\r\ncEplkhApD79wVOdQfs+C9bsiribaEgl9RdMlekv7KOBGmP4C4fuhQefSuK2+\r\nxv4H+PCZQZZp8KPFyDq/XZl/dmG36Nqg34A=\r\n=L75D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.20.2", "@babel/compat-data": "^7.17.10", "@babel/helper-validator-option": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.2", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.18.2_1653470185766_0.6716706983618981", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-compilation-targets", "version": "7.18.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "18d35bfb9f83b1293c22c55b3d576c1315b6ed96", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.18.6.tgz", "fileCount": 11, "integrity": "sha512-vFjbfhNCzqdeAtZflUFrG5YIFqGTqsctrtkZ1D/NB0mDW9TwW3GmmUepYY4G9wCET5rY5ugz4OGTcLd614IzQg==", "signatures": [{"sig": "MEYCIQDMV+1WgUtWifAtQCTsn29p3yi0e/YydI+wyEYQ6/WvngIhAIBlyiaLjOiyolDAjFl++kwzpkMd4Yf8dkox6Jc3rd/K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugndACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLlA/9FoSlp01VV3Vy+jsEAQdkSbjMKCnuN68jzP3k+eYiEQauBk/P\r\nz4MfQqN8ZIx4NrH6fGfKgD92ZeLWMCFNHwKXGfMwslfA3jb90HW+JxHXPEsA\r\nVCmbFgSTDyUya53j8aMV4wfR9Ztr70KiHGAwK0ZrcGzak4pa3jNOFD3aownS\r\naGjBl2Ua5g26urXi9kHi7fgmy1J0mm7M75g1apRR3ZxkTiXwuZHGmx/jc8dG\r\no4VziPDRe+xv0PYzf4olQOqyX+z0Py3RkW+QvWjnMoSPluuZAafwiNhVBNV7\r\n15d5oRurEd2/bA80iatKkA6z/rUt4Mh9ZGxBSd0RmMwD3aKchj4eTrCHYe7R\r\ng9Vm63oYa1A0LcVM0RpU6QwR7+I4LDCA/Zbsobt8tK+9Is7PUy83cWJDggv0\r\nI6dtseVSUDfID4tj4oERigZgkMXNpfhcDmxlvI49fWDYQtoxRlFru6Kf+4PE\r\nRdQ1SZQigJaCVA3oH/PtWSvJNCiLY6Vs8MM0W6iqIGkj0kq1N80u9XN8GHa/\r\nWpOszRx037cxdkQlyKeM3EIOvYkam+Xk+q1EYbHkUHixf53fJqGjQ4tZpGzk\r\nTcMWaTqachVfgyWVGpQk616ZfmbJ6IQaFr8kfdDlaM+afxaSy+icDOrvuwHT\r\nfy0T32+tSN2wvNdQ07eglPH3EnsdQA5lwog=\r\n=WcZg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.20.2", "@babel/compat-data": "^7.18.6", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.18.6_1656359389599_0.2092891197687463", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-compilation-targets", "version": "7.18.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "69e64f57b524cde3e5ff6cc5a9f4a387ee5563bf", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.18.9.tgz", "fileCount": 11, "integrity": "sha512-tzLCyVmqUiFlcFoAPLA/gL9TeYrF61VLNtb+hvkuVaB5SUjW7jcfrglBIX1vUIoT7CLP3bBlIMeyEsIl2eFQNg==", "signatures": [{"sig": "MEQCIFg3XRSxxzVSW7VvhwA2P7ZDcvE+uqaoaeZAcEMb93uEAiAEbYqhYUyM55aI4NxO8usr3FRUnwsA4CS6RkrJamz1UQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHxRAAk2w6jlCb9gunAWnMpneT1eyJDzi9wvqvDa9XxYa1f1ckVKCu\r\nv9vMQ9t5/km3/Kdquz1TVuk5g210bX5cMqElG+0xxmX9+x2yKmU7fiL07xOt\r\nFK/EUPaDPZkPtJ9k/BsB+K9DIzLMcI2hT7EHYmJnJl/qD7aPVHlOkDSR/O04\r\nFkZ0u0kgdoylIc8tTpfqHbQV3R8/3av76mue8XsyPyYnDbQcYKirwyF2IYJP\r\nkW/SRbjcSo7SJWKQwqw4s6aQdqKIrlFHjAp9yEzlakbZvmd/Sc0Qld2AZYE3\r\nuiF2v2I3xjUB4/GzA8CGXI7rub70vrBuaHxGRMs9qJUsC9YQrpBAb2L+s2KQ\r\nUjyinB9j5QyHi0DkL4cmqlb2cJaaPMAf2T71svIQJQj2gnUHCXMpybmHq77M\r\ndmpjFYEKngW6QbQhbhSMWsSnIf+SAXBr9kTfeObYc6V26dI6XQfe5u2/wQVq\r\no5RZf7quClCWhSXecoR1GCRV8Xt0y9YnAjng/IBJpIDkvYXIzxJATVekHavH\r\nPLIcdozj5BGE4qIw29z+Uyq/yXOxzHk3DIBkK9U3MzLcWE3IoOEAFvz2VahY\r\nX/LWL1YyvvE+yu9rPHuPDgcsCg5eTaz6L1Z9NgrmlDVssMF+RBNwWqlgXRFy\r\nkRKAnZXDOst5WXp4N3j3ZTyW/n9hvxu/Bec=\r\n=lqfg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.20.2", "@babel/compat-data": "^7.18.8", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.18.9_1658135842643_0.31922513418925313", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/helper-compilation-targets", "version": "7.19.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "537ec8339d53e806ed422f1e06c8f17d55b96bb0", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.19.0.tgz", "fileCount": 19, "integrity": "sha512-Ai5bNWXIvwDvWM7njqsG3feMlL9hCVQsPYXodsZyLwshYkZVJt59Gftau4VrE8S9IT9asd2uSP1hG6wCNw+sXA==", "signatures": [{"sig": "MEYCIQCBXP1qkGYdE4WvMkNIMF6H+JTXAJmjDEti09pAvcCmhAIhANsoeoloPVbAeM9s5rRj3pLkIvI9yp52cdWkVO8Z5kpE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLpw/8Ckc8YxC4bgDDSm0LadrDcr1vfy/Uzq/3huQ1yoC/Emm1lPK+\r\nOIBfsKBRvRejFNs9wAa6xwHO2PZTYCtW5qgTb+2ktQ9QENXd1TzfzAgz56Bf\r\nHx4Q9WKClYsVscMFPd8Ioxr1WKvzmOTijeqaRgpw/bS6OFuWr6lrohJv2eFx\r\nn/0TA4fKT9G5JlvBR4a3gAYkQGEzO9q3x+XuD+ufEl6GFblNoVCJuoak7JCY\r\n1zhlhX/zFJ2pVxsUgiGHYFYX+9LotE/TuTvZdSKK2h0vo3QhhiHSmxmKhHGV\r\nTrbFXSKDz58lQ3c+o7xD81VEN98MVG+LAXQurtcbB2eQi9vBDDZR4q1vBhsC\r\nkPf9+mrTgT1DNiqAiHzNO2c/pVPy/eIYqq7kpiypEl5pPEz4OWgFLfLWTqDF\r\nEWMJ+IhZKpks+lOl9eGFfpx2saZQKrUEiCFl6Z6Yix2D0e40aw92yWWndR9F\r\nQk+ICWSjK/I8RY4E+xO3QdovyhTdEvagGVfxk0PgJKHLJncH59abMcNZrRo2\r\nMWZK9bY7QC19TbPlusfg5N3tft6YXBm5QBDCWM2N8EI0FJPRfF+BqUBI7gwS\r\ngQqDcpUuPTG/CS+p4bcTvCuLOya4ioYiWn45XYxtcPDJorJiBKS3DhxebLME\r\n75+qXNdrk9thw3Ize3FILXUvS2v16VJhqfI=\r\n=QObf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.20.2", "@babel/compat-data": "^7.19.0", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.19.0_1662404536703_0.977849560751437", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/helper-compilation-targets", "version": "7.19.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7f630911d83b408b76fe584831c98e5395d7a17c", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.19.1.tgz", "fileCount": 19, "integrity": "sha512-LlLkkqhCMyz2lkQPvJNdIYU7O5YjWRgC2R4omjCTpZd8u8KMQzZvX4qce+/BluN1rcQiV7BoGUpmQ0LeHerbhg==", "signatures": [{"sig": "MEYCIQCTLqcDrf8fpGDL9E8P7VYniBcUmsx7ag2Fqtczy5azmQIhAIs5aYsQ6eNCw9E+0KiwkbBh5E5gS0QN8bWvjhCw3rod", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj2xAAh9XpMSUpSaIxkWVvS5ifsFh1GanIz8k6lpueGZ6IiHXRA7d+\r\nJK8iN2T+QbpFiHxgAP2Vfr8OqsNukvEJVufCB+FpGbkQAqjXQzf5vVrzr8Yu\r\ng0elOgb3KljFW4Rkc+yFjl5SF6mWDgFpufNSvRT0OonKpzohrtRFje8ZTgUo\r\nrRvezCNMvUBgJdFwRaqvFVdP3usMPU8KCTpKaIVUz0VHTYK3WkwwXAllUNvq\r\nEA8liVKFislFZCdohAL3IVOeT9GKlSG5kGseKKY44hCEE27S0dVUtCtECUTb\r\nYyrUfwD2zZc4vwoOZDr2Mw1iMSMA/P8HFj5Z95joJAbEkVOI31qBuSk07xRE\r\nwHTz+Z46dCvRzpLrqeYTc7U8uugdnrpqMYXQFLckPwqkTODg9tsh+Rk8x16T\r\nrih56kyGM4OPRuS70xWThyze6HpSkiWfhxdUEcHJzzamUBdqH9D8q1Gs1hyr\r\nBL4TelFdz6/gmLX2DavgCjofDk/gyZcrkWaShlsKFSRd8PunstVqXNX0jlXk\r\nDpPXRjSq/V0QHt/3nywnp9IKALMVvo43zE/7V8y/COnvVon39zc5+mHzSD8U\r\nQfcxiVDXultwBg6ab3UN2Z8rlnfy6F7d33G2QekuRm953F1M8G8yj6CmRwhE\r\n3ya05zx9mOPPLbgPpx0w/xZojWo0mAvCj5k=\r\n=GGwA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.21.3", "@babel/compat-data": "^7.19.1", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.1", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.19.1_1663169355131_0.4161391670823169", "host": "s3://npm-registry-packages"}}, "7.19.3": {"name": "@babel/helper-compilation-targets", "version": "7.19.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.19.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a10a04588125675d7c7ae299af86fa1b2ee038ca", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.19.3.tgz", "fileCount": 17, "integrity": "sha512-65ESqLGyGmLvgR0mst5AdW1FkNlj9rQsCKduzEoEPhBCDFGXvz2jW6bXFG6i0/MrV2s7hhXjjb2yAzcPuQlLwg==", "signatures": [{"sig": "MEUCIBWyUjzkG6EJV/ZhzkwilMy+y9xJgeqH3zO6TFsnFimFAiEAj4w7ksKmO5MCuN2gOUG9LoqJD+U0XsNEIB4dF11/o4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0LBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+bg/+MSp8sumYKSTMarCYj+YJLYGMkRQTQat1d7V52Hnp8E6YJ8SF\r\nOZ4ynbrGdbE7zwchhMsrzYjkEDUamtKZbSUQfgjDBEsXV8LhooUQ5zOzH6Om\r\np5DVz08JcjuaRiuy13PY2Bi05e3Y8sPxLCYnFh9/vW7KGdcnpUoynmUjRkR1\r\n15DOYLXonSz3SO4bIyxMBcDGTnoXpb1aSHg6iipugxuTyoUyI8ygLeIIPqbo\r\n+xGfhe1RMwjs2QBYJrlCqMwpDCZDw0AKZEO+AOLNCRqpQAGdQ/qt7U09CDCk\r\n6dtuNKRdU3egbXiBfgPx0RQ5RrXeHoOLdI4n6dpaz4zzXgICBXTJ7PFCd7Cu\r\n5mrenMSI99cqge+n4fD+y92T3UWvzaxd2IsemeBnFVgZBsFhLiscENPaaVyk\r\nqxzlETLlbV5HW6KO+ZWpHy01bq3BcW1tkW2O7d3b4PgW70jo1vPw4n/LCNw1\r\nkR0TOxQ89dRFj8ZbMw9INodrw4vsbls2bdOv8xcxeJHtcKXmwVd3t53CRwIm\r\nn7NegsuK5iB0ooX26Ux3ekv+WN1j8Jll3s9iw/8IOvhl+vajwKoMJhRTA5Pi\r\n/QSCvXhgsGeY2tDH8PAL9pvUCgigCRS7eFS4h7iAM992XnRADb5rbROu8lOH\r\nuGjQcEqbZ5Dgd3BhoCurgQs+UR/u+cvi3hg=\r\n=aKKa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.21.3", "@babel/compat-data": "^7.19.3", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.3", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.19.3_1664303808768_0.9709632261462071", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/helper-compilation-targets", "version": "7.20.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6bf5374d424e1b3922822f1d9bdaa43b1a139d0a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "fileCount": 17, "integrity": "sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==", "signatures": [{"sig": "MEQCICo5R7vcJCvTwssXJfy7d0Vy9spcgJw+oG2WYHiXC8AxAiAooP+D/q0POczplqLcUx0xxOa/JSZd/XkfugpDIHJvQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQwA//RWTuBeYZDec+KbyS9qjx8yHMihVySl2fJ6T07PU7/qh/2/am\r\nxSDHVAa4KwNn5cw72zfCnhB0mPbUuOq4tlslHyCsfxjcrEeX1Sp1rH43DCiW\r\nxiHAtx4FpBUK3OYLjSYe3jVXhH/57DUgV/sJPp/3UwYkkF5kbo/cdwIEIxIp\r\nLWuv86tD/Pi29nlYgWRO7v59OFPhda1PnudmAIzSjH6XlbvkQWkyeGJqxsQj\r\nc81VLFsiYkJ6/S4uiNn09oi92DIS+5XSOM5BN4u39t4dzVgfT1qHbQP2+9zY\r\n53dnOccDKaLJ15hqRAiEi8TMOO/hp2KI+NBjhTk/vn3F7byb+Ej16szSYQO2\r\nINmkyigHzHk9Qnj0Gi+LC+dpixTlApFkAaUMpKqYPGxizj/GpMx/Or44xyIM\r\nFpdvGtUSNcd+vvsmm6GhdamBGjGdVTcrF7Q5bp1dlaz1aTY4ZDrx99b1nqqg\r\nFm6OKn4o3XH8/B7TU1mEwp6cfklhU5JTyJQ3Mu+G8LsPpR8g2ink4wq2yssi\r\nMD0HYocroXfjQxhqhkmrG1Fd+Zue9NtyrF/4KJ71aPqizJTeATkZaEo5f4Fn\r\n2q8nyXs9CGfAYcXnx90Zjyg/1SZW1laAaUOfsCrCUx+J3T9CGwSQOa6GnVf6\r\ncck5I/xydcznu8xlEKd5RuEwPUWsLIom1rU=\r\n=9ykS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "browserslist": "^4.21.3", "@babel/compat-data": "^7.20.0", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@types/semver": "^5.5.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.20.0_1666876759450_0.9427669756717694", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/helper-compilation-targets", "version": "7.20.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a6cd33e93629f5eb473b021aac05df62c4cd09bb", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.7.tgz", "fileCount": 17, "integrity": "sha512-4tGORmfQcrc+bvrjb5y3dG9Mx1IOZjsHqQVUz7XCNHO+iTmqxWnVg3KRygjGmpRLJGdQSKuvFinbIb0CnZwHAQ==", "signatures": [{"sig": "MEUCIGVn6Cm0CwO2GgVSBohGMA0+qpE1ltHc1o+teuR3vs7AAiEAgwIBovER+Y9man5WuiY5ecWCvXLwG5KgRJJxz47e2q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCcvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBYQ//bSV9v2EYMpBL4BK3KTFU04DocC58WRXWDphSpG4Ryjiv0b3o\r\nEjYEUiWP5k0guDH82hu+/vIo8Yc6/BxBhNcJBEzfILWEMyFa7e0MkIAwaciX\r\ngXjAAigYY26q2tRDCwK6is/sRMpwzxbCKTjp3JgTFWz2scJtqO8g/2XWLMyb\r\nQpDEKoNA669Xir306+WezYvphbF+I5mIJ4i34yBkjaS3S5L9Xh4VpIGKLakp\r\n1fnnEI18J/oBRGyKa0UoM7MzSdSfKeIDSneLideK98oCqNe/3hAc1az3JXLZ\r\n6OfqoBX7ESvtOWnTmEnFgeYdHQBqlKNXRAXucsHZqJjPBGIGcDj/KAAdyWOf\r\nd2gZbCvZHKxcvAGIgXO51CS9rlzifxhnbJxgx1BySWk28NJFNgZJ7ZE0b2eA\r\ngpfr8DJ0H7ow8hGP3HruEYIUme1Ke6BlzbcjbdMl11LVPnHNI5UKNxhcn8mB\r\njCsaLJtvrqOzQjGjUK1cV5qZsczAnfqkB5SLMwb0DLpzI8xXluGpPnaGQEbw\r\nA27GXaq/bbLQwcevRpUfeQWTYHOJFGijgyE/ND1nWnbAw5UZz8Ilg7Ppr9a9\r\n6EVwlJD171msdFGRaK9bwz2y8ri5HUaFWtToWtq/lb85C9N/VsoCO/TzSRsA\r\nSsuYdocu5F0rgjMI0f7MyjeJT8utWa3ociI=\r\n=woJL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.20.5", "@babel/helper-validator-option": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.20.7_1671702318996_0.08109473942670342", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/helper-compilation-targets", "version": "7.21.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "770cd1ce0889097ceacb99418ee6934ef0572656", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4.tgz", "fileCount": 17, "integrity": "sha512-Fa0tTuOXZ1iL8IeDFUWCzjZcn+sJGd9RZdH9esYVjEejGmzf+FFYQpMi/kZUk2kPy/q1H3/GPw7np8qar/stfg==", "signatures": [{"sig": "MEUCIQCItlysQeMEWTekrOhrtQQTBNpMLshfyeFNkHzwrgoGJQIgWiJZSiX5BSTvPPK902HCHqlrS8sPYsT/aJO9/YXHLTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpegw/+PPa587jJMnaYRDG9qW7ohY6KulM4mOu91d8OjmjiVsaUqonC\r\nfHntT+HVQm3yViAkrbIs7vNJxSVgwFA3YwOj5ptYKxrE2B1zwjsUZ/huu3/4\r\nsFoYUMgnBLzGLAKUyYsC1j8cx0+k0CKkUYH0RIfjEwBzFCO0abJk0vic0Af6\r\nMYenMQ+gNOuAFOWJYUK8+4qxh/H4jN321nn2xuZTl4grFnGtr2FXfiR8Xqxr\r\nb4AXzI0fC23SHlonCy4FWOscllH+ZErfXbHGHlBn0rfUAX/NuQqV7yj4FMPc\r\n39PJvukmBH1puWcERILG1CwvjoJ5L3dM96Wnke8VrZ6r1NkB4v7qcQBBrY3/\r\nf/9HA0xiHmRlJYvG/hnw7oox+hN1RQdDb7O6pIHO2Z7UMdGC4w4g+a6H66L5\r\n2LpJ9MJ5+OEZdkeRFKP6nCUzsJKVsSqrGRofYxRFYPuhmfZYqYCTeROkw0/o\r\n0tPzddG3qVZdW9JeSR707zA2jnGhLVv04mL0ykjSW3G7nDXNsYC2oYa9d3ln\r\ndgeh7CirrvPIJIb5MOYUmIu94FDzumRtVoRnjnSxftzvvJT5xY3LXFUj6qwz\r\n6Fhn/NGfOPDEsSxemiy4rcaxlDNKSVMh5ob3CwFzgSstE6/2tzz2rwGrdBsS\r\nqBrNH4uEuSkSWB4460LpssZEmTtOS2vCu9A=\r\n=D0lE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.21.4", "@babel/helper-validator-option": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.4_1680253316491_0.4772301241432997", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-compilation-targets", "version": "7.21.4-esm", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "09cddae4035d8e94454d473f907a1d4dd35a3529", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4-esm.tgz", "fileCount": 18, "integrity": "sha512-Kr<PERSON>eJfhDJIR4glDQ9lU6+1wI/TNTDe5Qct3G5jxXEPeTE1Fcl6Hq2/QaSI6RiCynv6IZy0mth7LbQ4caZAc3NA==", "signatures": [{"sig": "MEYCIQCIg/g1XwJuct/uIADFnvpCRQVVOHXThKwAI9FPzv5vhwIhAIW8OjfKQGBZqnmkSZyA/DGj7F9PevkMu04Zt1uqe71/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrd2g/+Lr8wEnTKMUNd1we3Y0huAWg+YG/M4hB5nSxrp+f1EBKrtosS\r\nSnoSLQrabnget8VQWpLHSQRqla0u/qY6Ey56EIfSZZbOASDtmWd/vWX8nee2\r\nOhEe2WSBcL0HUX+5PksZW+m2HK/UTTnA4DKD0+guELMAxB195IALPLFtmTA1\r\nMIP5E5lKhsKfF27JTRGAQ85HEKIEGIc0BY5Ad3cRAfjW2W4urkmgvFUM0i53\r\n+kPA7n3Fme0GyrmsddZoLAHEdgPvSY8ziY8K3p7QKMwPg9EKNEbaHWm6SkmQ\r\nk6lZwiob6zAZM+GlnBU1ty2Q5GHIuXrS/UfuZAGzxTokwZ4piJWhnk2kFbdh\r\ncXrSO4pfJIqM8OTJDburrrFtwi9tpJPXwepXzG4iq1JchW4IbH7Milf73Hji\r\nhP2S0rQ8bYOWfi/kSdwxsQz0TR+jnpM2dzsslaHtdemK86kFtky2wcfiP9Sx\r\nnaS6HBwx98T1921lrzi6I0t2SYmjKW4S10v/hsSCwrJ8Uh9BNAM7dj8cHNYz\r\nZVULLwfqB5Q+ObXZdXY+siiuiHrxBBJaH2EwJgdPHCy1cZpTnI4h0k5h6PfJ\r\nM+SsRk+o97ZU+8pwxbgI9Gr2b2QgTzu9vmcNHcGH1HeTRmoMjGuG8+Re9h9P\r\n5D63E8VAClH6jDMwP8R6Mlc77a/4EPjwNDI=\r\n=Ee1E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.21.4-esm", "@babel/helper-validator-option": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.4-esm_1680617347591_0.5596972159692231", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-compilation-targets", "version": "7.21.4-esm.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8e7b17167ff3d8700ea50f49c05c87742710f27d", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4-esm.1.tgz", "fileCount": 18, "integrity": "sha512-g8+cFdzPMuDLFpG07C7i9wiTeRa4iFz4+omlsd56Pc+C0Wjg0wlKnqNYr3BdSkYbnODhV2/Zcz3IQRPOC0JSqw==", "signatures": [{"sig": "MEQCIFxxscTulKOzC8/O5fbNYrt9EpW0dQ/Xg2zc/R7udIeEAiArofrB+ZitSbMQjuGOjG+dDF+ubhahMl1NihWPR0KNXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9zg/9He1W4xq+d8mzAKNlhxpY6Vf9q0Gh3aTJ7C4mypMV1llV0gMn\r\naGANiip+UkHVuKGHhAX2M3ARgSphX53THSEsffFr5HIxXszqONOj0eOrpCc5\r\nSEWLUbtEH70NOiBYzqBYEpblXoXizWmDZbfmm3r9/MVoQOINSjRfF6XEJHRh\r\n0p3NLyNMB/Q29I3VCtyWw+UWccjlKB0b2CgDjasV7mQoKev+KqTuyPqwzJUN\r\nXG7LGkIOvcOdVrIRNBK52/WSr801XrHQtxLV547Ah1ncUB6HpY6jYptJGgpv\r\nQWJ9uNmDN2B3bAIRqiDDup24l27XAQ/mWXxfrkXwawMpialHScSO8zrwzAIs\r\neIlULPetNdgeDEVtBwNoOg6hV2FOyYQIvu3kAWxjsmC/V2Wwr3P2cqu8xtkz\r\nX0MdpRhPtrHYFoQpDTgPhmvylbd59KgmMilF32l7mnP7lrK/fb11CWLMeW0K\r\nilIATqCH49RZLkRBFTnClc/t6u5x3Z6DiFqrPrzfxJAz0HeOg8oNaPvvohew\r\nIM3uDtu7Gf+KdTBKfodiRbGGgrK1b3DB+amzElD1sdR4bEjVtGD7+M9T9vaZ\r\npC0tMpZmbfshyQVU6zNpMtIiomEfFmD6Jzzs/UKLQd/Oge4OrV1NzxY9+sTk\r\nox9VdRKNS765p5yrCe3bRtJa+ep3bIMVAvo=\r\n=rcSu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.21.4-esm.1", "@babel/helper-validator-option": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.4-esm.1_1680618060050_0.4365147251995227", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-compilation-targets", "version": "7.21.4-esm.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c19a20f78c4e53719d9b62a94f6c960a4b2b51b8", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4-esm.2.tgz", "fileCount": 17, "integrity": "sha512-OirZG/ibVE6wnLY/8SyhqAa86oOHqvaVw6sOHiEd1uiZN8VD+8wpJJrvym2XwZFHZUR2o25EkjIdL9zTvtbLjQ==", "signatures": [{"sig": "MEUCIBZEbXPaOeROBkAkcsKozCerGU7Ya37uFB9NOvFwM405AiEAhHKXONeB9OvCl77H3nHYdl9y6obKkYwKUbrOxFeZvUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCcA//Y6DGFqCJ/QEght+PZVQQ6S/iFnpnmBllnNI8mHcPLP6wEU3D\r\naqtlhFYm+i1DByxfOkBxSSiHGsfmidPVWZ+0juh3eUOItrUwBm3BbOSMoRHb\r\nz/XzpQ6Tu9bgBo78Z0xMnjQ+I/GEROpm31yuTkImzmzghN+4NAuxHDxA32Yg\r\nxM2BUAJUbhTN8XZ/gPLLnHUmGqUK9WqJCNUfOvDjfBWRbd16WLbq8dvqOW/T\r\nNqaAiGzc9bepIx45Zcpq7zrwjjUjADMBgTxwC+TPVIZcUSNIRQlsPa/ONUUB\r\n9E2jP9hzJM7KnphdY2YaGCrUpmfp3jQZGw2UE2Jg+p6RXihwLvdD7ncxKXB7\r\n7O/NCTxk5X7lGDAW9Fj9LVgVMecycNHDgUMUgyCMQZFpZw5nMS1yC71jkYfQ\r\n7BWSL6eRhx4HK8iJ1HznPsebUg9hL2p8CNEIOtAjH9JDeTv93WXFAtVgyR/o\r\niUkJ6VNzzrzQmjPfx+EqEBMGB889MvRQlwd/wPNlNhIbYP7iifYm/ge5zjt7\r\nL9myogouUJ1CEhc7Kzf4/1I6+n3XuP1VnF1dKShf5LaPk8DIGdMffw/3vwJt\r\nImY4UMjsYVHJhnAsFyZeZ6z9RiPj7wRSO+XlCpagmHaHUpj8UjnyPl7dIgD/\r\nkoPBNxufxtue8sRPZmoZpnKSi3GsaUXdhKg=\r\n=PhIu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "7.21.4-esm.2", "@babel/helper-validator-option": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.4-esm.2_1680619129120_0.19667272486921394", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-compilation-targets", "version": "7.21.4-esm.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6115e31d110dd2ba10e982eeb9832fe41b5a4ada", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4-esm.3.tgz", "fileCount": 17, "integrity": "sha512-I4jfZ2QtgfEXFU7Mn91FBRPLljQcxR3YeDxpJH/3LpvoTLmcXLb3tkVs2DS378iFHcLWP5MBkOL9s0FnOy5UcA==", "signatures": [{"sig": "MEQCIGVutEtHXFJu86inLgWF6QLNQmnBRXwIwPopAkCrPr7dAiBiSmBIqi8xaFx5OKbh0SGKlppsPNbfIiP7unCoiL152g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDp6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqulw/8Ceq2DRi/DJx5WQ3rf/BbXPWbwkb/+QCZjWogCnDlkmMCaUuA\r\nAq5DJQrbtApYvawGjxyb9kzt406Os8Rc7T5fNB43faj0kK9LJ8OBjyUX61AB\r\nwT6vsa/sPWsBUaMeBV+LOqAwaydE8BGLrG66RCmV5pEJLvT/fTFIkqExWjq9\r\nw0VvJ+ES7Pl4MAHqXOC8g1Ge5N6efAJ0aaSMYCzpat8gKTCrWPTs2gqhtxLV\r\nv4nsM7KipQ6RMdyu4YVL/3W0traX7bpY2W2b78SzY7VRU14YQMQHTGvWndnx\r\niaMOtCSuS2EcLNiZE7+eHU3Vp+yR2JtSv6DyIvbmSH8MOnnvA0HCuw+jvr/e\r\ngkbDkDWdoeIEeL1llMc25agz1zuNdeBvXmwM+WO9v2fA2bqyuqdlYmxZU2IF\r\ntjAcWYAp6GNfk4Ikeobb/9VPwgzRnMOFbDqzRMBCP03oEOXsdgSTfbUfb1Ex\r\nnTAUgPcshVUlGM81LzqdNJqMtgo+ovw93aPtkYDveenWBKvNDNTXSSY+1NvZ\r\n+kFyUOmmXM29z+OkAk57zcxdYGrbxWEW2GWbpwkMtztGRtXSzNxNZMS2JoBp\r\npHaOrjxd5LNytvb0qw4BiHz5l1yu5PQo9kyElAXOhldFOZwi1aiBoSPU2LMs\r\nWcB3WLR29FLm0ierHsGzBdP77mPlXRbxM6A=\r\n=DoJd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "7.21.4-esm.3", "@babel/helper-validator-option": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.4-esm.3_1680620153705_0.3900058205491559", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-compilation-targets", "version": "7.21.4-esm.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f04f612bb97f9307a0d1b5279d02c8aca66c3c57", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4-esm.4.tgz", "fileCount": 18, "integrity": "sha512-hKTdnTjcKT1bSccmKwU+MI83bErcgi5BPBg/5ccp0zmZfxGiW4JyYlTYr1zunf91Rk+J7k6LU3ZsvwXdO30CEQ==", "signatures": [{"sig": "MEQCIF1AmPaBrXB6VlPLvdj7IfHwYY8TjYGjzlLCC2kq9PqkAiASTeLT1Q+TGpBs0WES8VSRnBf3Qaq84EOPcEVVFBRGLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEsQ//Xymiof8JCbTPRE/3W0zao4r6bTRfpzUfX0WrvltRU0s20ni3\r\nWjFo+3pXnQVHuo5JMbU0nG4uVnnLRqLATHZF2uvHtCAkWASYUUTCKTao9tp3\r\n/XLXzh29+74l2QkkF0yExTOq4kOh8n73l1LJ2DO3k4mZf+acqx61zqeLZCnl\r\n0XoeyBHqPtSPw/nckWRVS+lj7y2iCEqvMljLwKyLudx13geJlyGAUSRNliPb\r\n1hcJ62ICsUQfCZjXJVTtPfA1Cl2nViaHWjF53UfxKXT+p2jOT2iQIpLi7pYS\r\nsmVUZtGXGQkN3cxDZgcJEzHXpLe6IlwEfU4iypIjB54n5hNr7oWETrxo+Ofr\r\nsoY57zruTihb1JymodBEwLQ4DVQiQpukVGU51yVNy4GO6uRxwmN/dmyvDPBg\r\nYg1RmCeLxZUVDjNzurEhyLie/MwgkIwItMrPYIiMoD7Z2pZNUujT5U1YGnbl\r\nABT8dtlSPwy3vESAI7Latsc9naYxmVOsoVrDbFUUNJjMB/DkgXj5DZ+/AgRK\r\nw3dxuVFar6WZG/AEFh2mM0GCksjj+5qlE6xqCIIAqfRsGKSFp3ph/ge3Dr4o\r\nOJ32f9IN1WOoKIuhkqpCeUupnHGocZbiX8NT1bP8lv1jytBtup5i55JMBIo9\r\nKZlGLxjwMcIJskbHMLEN8F1x9Nom+GlQGnM=\r\n=788m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "7.21.4-esm.4", "@babel/helper-validator-option": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.4-esm.4_1680621187699_0.9233807294564171", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-compilation-targets", "version": "7.21.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "631e6cc784c7b660417421349aac304c94115366", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.5.tgz", "fileCount": 17, "integrity": "sha512-1RkbFGUKex4lvsB9yhIfWltJM5cZKUftB2eNajaDv3dCMEp49iBG0K14uH8NnX9IPux2+mK7JGEOB0jn48/J6w==", "signatures": [{"sig": "MEUCIF6OY7IgH5aR9xej4d1vgMPTpWwDljjYPLcoWDdfCj9OAiEA9K0wz0rnzmCQGCDjCrcGiCkOQZ64J26GCvShxZO+6k0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRQA//doVHcp8WHzJZ3lYSyzbWtUDhNd11SNoEbHBL7VWpNmKxd6fR\r\nVoxRu4Pxce2raOKQZ367uLsZ86Hrnex4HALFqFhDLxvnmDrmR2YBpvMk23pp\r\n3Cw0fp9yDZ1dazhd4BzVd3GX/n004j2vcJQzxilgUe0cTjIuoCmQkpzk15x7\r\nBotDuzgJJmCxyxPIlLN6g19jut7JNnuzPPc6hXK1ySdipy78BLZZCiUCvvmp\r\nRQGYDQscrUS+3ndv11fH9L8invPWFuy2azmmIKkVdU6Yllt5EL7C5d/z2J53\r\npwBm8kfNeAVnldBQarxIp+KaqkWdpxcU1s5X42YQoLjJu8HMIfFW+FzsyX88\r\nB7mqGOAtBiXOdgGkQNxwqKRgM0tx2vFu+XCiStpZ35qQk/rLSMAjOCv0wq9n\r\nqC5TZJh3np+9rTJTMkTU/gdMcUAX8ufw3dUsFfNMsoAOotW9C24lv4kwN/iR\r\nza3ysEYlPREo4Hoq9geEh+r6oSHud2QELHpFDVkPamvdtRD2mcea8PEFehpz\r\nror3klSxqS0Vq3SFJNGKckCTPBOZy3Fu05N/bcUriOeFNZQiN8S2rhbAkHDd\r\n7+04I8sNaBKufNG9fQ18xd1cRfFZR3DmDAhGLHX1SwsOLjQ47fNHQtkiSC6a\r\nfqjEWjcBPdgqBmPFwMTd/zdiz9ETMDbfZOE=\r\n=JD7A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.21.5", "@babel/helper-validator-option": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.21.5_1682711420034_0.6824823946485794", "host": "s3://npm-registry-packages"}}, "7.22.1": {"name": "@babel/helper-compilation-targets", "version": "7.22.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.22.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bfcd6b7321ffebe33290d68550e2c9d7eb7c7a58", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.1.tgz", "fileCount": 17, "integrity": "sha512-Rqx13UM3yVB5q0D/KwQ8+SPfX/+Rnsy1Lw1k/UwOC4KC6qrzIQoY3lYnBu5EHKBlEHHcj0M0W8ltPSkD8rqfsQ==", "signatures": [{"sig": "MEQCID0KHMJeGt1ErMS0NhPcQJ2nvIZikYXt0NxdIbilM9H3AiAalu+7KJvt4ALAkCmUIGwfnMkX2ycsvY4PDeX4dFNPkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51580}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.22.0", "@babel/helper-validator-option": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.22.1_1685118889785_0.13647998568461617", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-compilation-targets", "version": "7.22.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fc7319fc54c5e2fa14b2909cf3c5fd3046813e02", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.5.tgz", "fileCount": 17, "integrity": "sha512-Ji+ywpHeuqxB8WDxraCiqR0xfhYjiDE/e6k7FuIaANnoOFxAHskHChz4vA1mJC9Lbm01s1PVAGhQY4FUKSkGZw==", "signatures": [{"sig": "MEUCIQDTGoFzjgmOd/j35/Cu8fRfvVzVpW/oHc2cjCGFG1MFuQIgBcelvZlchdRHZXTMiRob4LtLrKnKzjKLT2Vf/tLzlwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51705}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.0", "lru-cache": "^5.1.1", "browserslist": "^4.21.3", "@babel/compat-data": "^7.22.5", "@babel/helper-validator-option": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.22.5_1686248466808_0.7059733826041787", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/helper-compilation-targets", "version": "7.22.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e30d61abe9480aa5a83232eb31c111be922d2e52", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.6.tgz", "fileCount": 17, "integrity": "sha512-534sYEqWD9VfUm3IPn2SLcH4Q3P86XL+QvqdC7ZsFrzyyPF3T4XGiVghF6PTYNdWg6pXuoqXxNQAhbYeEInTzA==", "signatures": [{"sig": "MEYCIQDN9VbWC7p9Rav0Yz+x0UnJrnSITAHOg81tQCrgcId37wIhAJSmhUm5wExN7TEWuQ0X+wCkerOOUm9n9vYA6SmkYJrj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52539}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"lru-cache": "^5.1.1", "browserslist": "^4.21.9", "@babel/compat-data": "^7.22.6", "@nicolo-ribaudo/semver-v6": "^6.3.3", "@babel/helper-validator-option": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.22.6_1688456938693_0.24457343279994825", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-compilation-targets", "version": "7.22.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f9d0a7aaaa7cd32a3f31c9316a69f5a9bcacb892", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.9.tgz", "fileCount": 17, "integrity": "sha512-7qYrNM6HjpnPHJbopxmb8hSPoZ0gsX8IvUS32JGVoy+pU9e5N0nLr1VjJoR6kA4d9dmGLxNYOjeB8sUDal2WMw==", "signatures": [{"sig": "MEUCIQDzDrFqXAvknlu1TD5ltbUk6DcLC7ym12yUiskwJdSTsAIgXjKOfZRczMTHGjRqje27kjTC35Y5Mv3T9z9btoWBsWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52127}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.21.9", "@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.22.9_1689180827563_0.6658632362062851", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bace39b00be58d12445b922546e3125779188524", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.0.tgz", "fileCount": 17, "integrity": "sha512-UtIToRJh0IZiGZpPMiJP9iXSKzAKoSMBBxxD1AYPbizADclfQW2HeFH5yMGcLfScxrHNtpTUprfsvGuFOJyyeQ==", "signatures": [{"sig": "MEQCIB75u1O3reM3Ib3NX6wxx4QJtBHxjR+CIpw8DKkghO5cAiAMo6q41M6p18ZrDh/k7BtPfsLLlFcbnudteO9Fic8nUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71488}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.21.9", "@babel/compat-data": "^8.0.0-alpha.0", "@babel/helper-validator-option": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.0_1689861580292_0.5806347786317092", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0c2489c407a51cd377afcc9f7f92ccf0db66ee8c", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.1.tgz", "fileCount": 17, "integrity": "sha512-mNNOHBfb6/XJ28tN1Y8P67d0QVAgAIU1o+bg4Tz04EPzDamCwFwzlIf2bndPZW2S+btWM8cfjvbSvWJh2DDN6g==", "signatures": [{"sig": "MEUCIDSn5ra+mLw9cjLKWU+TRByk281THPjrze7S58w13UjCAiEA945W/2+O1YnoU72TvkA21qaOg4IAuyY74waJlhZtags=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71386}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.21.9", "@babel/compat-data": "^8.0.0-alpha.1", "@babel/helper-validator-option": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.1_1690221071318_0.46545837681277424", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/helper-compilation-targets", "version": "7.22.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "01d648bbc25dd88f513d862ee0df27b7d4e67024", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.10.tgz", "fileCount": 17, "integrity": "sha512-JMSwHD4J7SLod0idLq5PKgI+6g/hLD/iuWBq08ZX49xE14VpVEojJ5rHWptpirV2j020MvypRLAXAO50igCJ5Q==", "signatures": [{"sig": "MEQCIGs0Uu99v7m52XkDpWlmwBhS6kgj0p3LEciQvvO81D8oAiA/RwIoZ22Mvbpm7kzOeU80jN6fLkmzs5eqnlXoRRg1bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52041}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.21.9", "@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.22.10_1691429110078_0.2284068899826317", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d007cb354f0fd8046e83480956a67161c3f84892", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.2.tgz", "fileCount": 17, "integrity": "sha512-sF04m1xc2ICLUEdZBbW/ec4+lh673bp4gVBE442EpLlTSo1if2q8lAvY/0/u3DiyCZGFuSd0aQSIC3g46UqQ2Q==", "signatures": [{"sig": "MEUCIQDg/lj37EpWnrEOtHgAgZ2OnfLTYz0LMFkYfLWw4yJtiAIgZkE/oazhMhsSffFQknmtblz45CBF4d0/G60ibpq05i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71386}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.21.9", "@babel/compat-data": "^8.0.0-alpha.2", "@babel/helper-validator-option": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.2_1691594077345_0.0019254246385136398", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-compilation-targets", "version": "7.22.15", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0698fc44551a26cf29f18d4662d5bf545a6cfc52", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.15.tgz", "fileCount": 17, "integrity": "sha512-y6EEzULok0Qvz8yyLkCvVX+02ic+By2UdOhylwUOvOn9dvYc9mKICJuuU1n1XBI02YWsNsnrY1kc6DVbjcXbtw==", "signatures": [{"sig": "MEYCIQDKQkDKcydAv8aHWNxN65fVBaHRWHsCatH2yLbRwI9bagIhAKz3JhVsrcqkz50EOg6SFGQJ0JzRMMc7OIND8+jVzG0r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52126}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.21.9", "@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.22.15_1693830311489_0.81069430151269", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ee46dd4c73fdd8fdfd6c1ae658365883d4373b32", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-wXAuZSsV6cky4LqMTZUZhsUku+KPfoX0Jb7Kyrgm/VfSnwYdhjr77ulEuZcqzTJ7f6ZWWu32T6/0OngD7LJhSA==", "signatures": [{"sig": "MEUCIHr3WMie4DVhGR4s+zBRcrl5kLcBJQO2VObPtB2Zl4cFAiEAg3RDjxIQKTzfceSgnucuKEAS3JoKTI0ciMhuC+s9V7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48310}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.21.9", "@babel/compat-data": "^8.0.0-alpha.3", "@babel/helper-validator-option": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.3_1695740192444_0.6692832874309502", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "373fe4c5771c16083a9e77b404e1075623140a6a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Chvh2yRbnXhrwz4+O8SlihXzfkAbC/he0vJK8dxnPohXG5sNr4BebskrGeBgHPnpLr93erew+J/1QnxP5AsV9Q==", "signatures": [{"sig": "MEUCICaNzTkweUlJOOUcfl27jJnsvuGUln4xXhrJkSMLpIyGAiEAtSCk6Py9nGTLS1YS+S9rhj899cv921CF/ueOVvLc0q0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48310}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.21.9", "@babel/compat-data": "^8.0.0-alpha.4", "@babel/helper-validator-option": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.4_1697076359463_0.35644255764174715", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/helper-compilation-targets", "version": "7.23.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4d79069b16cbcf1461289eccfbbd81501ae39991", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz", "fileCount": 17, "integrity": "sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==", "signatures": [{"sig": "MEQCIHMEgU89oRUsPjHisu35wX+R7X4EPs8RpyfwLGeni2v+AiAvxKNqoiHGJB35VOpww1lkmvktvLf+kgAf0XgfBeLqnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52024}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.22.2", "@babel/compat-data": "^7.23.5", "@babel/helper-validator-option": "^7.23.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.23.6_1702300196564_0.9776377175633344", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "92bafa091fee24c83baf1e4c72027f96deac0cca", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-zTB2wo/xqApzo8qp50uhoOCifaU8YT9CZG7BJ+NPflsXS98/Ih3f+QwfHzrVldxmjL1IfFa+7zXBZoc+LQOMYg==", "signatures": [{"sig": "MEYCIQDmfqLT+Re+qgcyQJaHWbu8Zljbix7nRYCExzvoKPsfRAIhALpl0wItUQ65FDakIsdXgZfBqmmNmz15lTtwIFOLjim0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48310}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.5", "@babel/helper-validator-option": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.5_1702307901007_0.9206321109113382", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "aed29d5b0002b7126ee7b0d58b362a6a64df1d48", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-TWNrF91LNs9/0bIg0xlRy/CzUMfjVmp/J5EzvbVp3Jvow+PzWKs/aACersP/WBu3dom1L+K6NS8+V1Howvo/Hw==", "signatures": [{"sig": "MEYCIQDM1r8DOYGUMiiCTH9IwvGT3wdeQNg4dvD8bfynqElp8AIhAKa6g5u0lHvvFzH+c0iXqVFPuQ4cxnAl08J9/BSKPYKW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48310}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.6", "@babel/helper-validator-option": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.6_1706285626137_0.3307000943090306", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f2dac9690cd1e43207c2bfadc7ceec5d84e73e4e", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-rNBwvIS3rM5XD3gelO8FDc9TOfAFHHwPhdw35WFjt2keIYbvNPPBMXsjbUGe+7Sz0EM5wxMqzuYFBAMKu8rwHA==", "signatures": [{"sig": "MEYCIQCJ+choNgCs8AxBUIUwO/vUzZgTU6xnA+exc1xDitRjQwIhAPJoSxiRMg2vtbAKHanGGx1QJpyxSiC9QcL88kansp1D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48310}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.7", "@babel/helper-validator-option": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.7_1709129068369_0.5673801978527837", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "93403477fb3abc2ff88739fe9f3c416f8c9c2a24", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-r27s1x1omOU5Jd+CpniSFHlDAW13jQlyfdt/xdsQIfQbG3PVZXT5vV8qVIkOy9yszfcC0f4ML66XNxGA5vUPtA==", "signatures": [{"sig": "MEQCIFtCGw08Tzo7HHi08Emr1WqF5C4WMXuS1/tyiaWYuy5BAiAmO8dDjGxLqIWNSmLHpsczRp4Tnz3wad/3eqRd8V9ajQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48310}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.8", "@babel/helper-validator-option": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.8_1712236774295_0.5094354608793876", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-compilation-targets", "version": "7.24.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4a51d681f7680043d38e212715e2a7b1ad29cb51", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.24.6.tgz", "fileCount": 17, "integrity": "sha512-VZQ57UsDGlX/5fFA7GkVPplZhHsVc+vuErWgdOiysI9Ksnw0Pbbd6pnPiR/mmJyKHgyIW0c7KT32gmhiF+cirg==", "signatures": [{"sig": "MEUCIQDX473pG/Yt6rneoXtPeU8z3fu7vZxyyFGqxHqMqd297wIgL+LaUiLraWlfCT+gd8t1v82RL52KeFp8Whu/P1538fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52196}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.22.2", "@babel/compat-data": "^7.24.6", "@babel/helper-validator-option": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.24.6_1716553455618_0.4945713230762694", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4c9d6c9dd214a7b424ad57080417c607e31f2ea4", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-lBMiqoTNqjDpr2EjFoD2oFhfh9ZczUNfUk2XklEAFvrzdMTpbWOnWJc8MMotMyOqXTN/lYam+HcNcYn5p+b10w==", "signatures": [{"sig": "MEYCIQCXMbnDfxyd7xzkPHnvrl0yiLH28pq0HRrtCnIHguxaUgIhALxlHhAvc9R+tGzF2hSB5sNVLCowCS9/zdAuyNQ85pPh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50670}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.9", "@babel/helper-validator-option": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.9_1717423433634_0.561454656510052", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1731f608212d04f7527c9fe730bd5b21c73cbba6", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-CsNC07vRilSOppoLAfta7Vo8sIzAw+0B56U4LycPXk1rKWy2cwDmfVdNro4nMi9vlNtaMcm7+YpBBU/6u0jkkw==", "signatures": [{"sig": "MEQCIFfiu9FDcH3Q04BNU3OOP4BUn5pNa8qIR3PQuo2kq1JSAiBOp5LoXnkRvAMyXXRyMMLkihm8AJfDZj0DAKfCowSJ1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50674}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.10", "@babel/helper-validator-option": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.10_1717499985752_0.5608798805319903", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-compilation-targets", "version": "7.24.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4eb6c4a80d6ffeac25ab8cd9a21b5dfa48d503a9", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.24.7.tgz", "fileCount": 17, "integrity": "sha512-ctSdRHBi20qWOfy27RUb4Fhp07KSJ3sXcuSvTrXrc4aG8NSYDo1ici3Vhg9bg69y5bj0Mr1lh0aeEgTvc12rMg==", "signatures": [{"sig": "MEUCICH/+Vlzekg/BMzFDFB7WP30M1yh9cjbskDmzH5ru1w8AiEAn5CSJetN7mUCwl8hTkb8NjqGGFWsvJMjk/F3wLLFfzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52196}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.22.2", "@babel/compat-data": "^7.24.7", "@babel/helper-validator-option": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.24.7_1717593303328_0.690753208095312", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.11", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fa62342ec5a44aa3a0f3ea8dc711ef6517b4df77", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-TiaNhvZLq8Basa3mgDujOTvY2FQQvdKdQlrUfquSycLa22IIALccGhbTfpbnMmknbZfyr7vx/UuJa5R6zFuimA==", "signatures": [{"sig": "MEUCICs/Nz+UX25dPDhNVLIKAHjMJEtZTtNGPV3KCACLUIxNAiEA9jhpQ4Ex4T2HtxtQLbxft8OioGp7aEU1QQzfawaA4x8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50674}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.22.2", "@babel/compat-data": "^8.0.0-alpha.11", "@babel/helper-validator-option": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.11_1717751715742_0.6126784484637067", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/helper-compilation-targets", "version": "7.24.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b607c3161cd9d1744977d4f97139572fe778c271", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.24.8.tgz", "fileCount": 17, "integrity": "sha512-oU+UoqCHdp+nWVDkpldqIQL/i/bvAv53tRqLG/s+cOXxe66zOYLU7ar/Xs3LdmBihrUMEUhwu6dMZwbNOYDwvw==", "signatures": [{"sig": "MEQCIEfqa48ZyFAaCQd7nl+yW6VSrocseZ8J+cqYX3D0zl9+AiA1dEQx79lah1tsEOa1LxUo641I3kTg5sjAn9BNAthguw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52180}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.23.1", "@babel/compat-data": "^7.24.8", "@babel/helper-validator-option": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.24.8_1720709688564_0.38774080714445525", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.12", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "90c567f52b8a8efc51c1762b37b473c286e16532", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-Meav8fLm6dQ3HXQey3Tg2Hgc2GvsnKDy56D+hdcsuwD2R5l9JCfLBH/WOHNIHBak4d+YmDy/IsFS4KWdb7NbFg==", "signatures": [{"sig": "MEYCIQCZ4DqVUKHGa2Vq5iu6c/5LtJcEnG+pEqsUmEKbyPNDKwIhAMIdynKKvWa4rt15684KcWUrGXhGvzZ87VPz1QOPHoA4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50657}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.23.1", "@babel/compat-data": "^8.0.0-alpha.12", "@babel/helper-validator-option": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.12_1722015190376_0.8008487012505243", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/helper-compilation-targets", "version": "7.25.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e1d9410a90974a3a5a66e84ff55ef62e3c02d06c", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.2.tgz", "fileCount": 17, "integrity": "sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==", "signatures": [{"sig": "MEYCIQCQi+43K607GSZww0R0jYQJ8JPsE8G2aq9v+ve3HTtCIAIhAOl89jjSdyxZtkOlipnIB6fKOpN6AEJ+9GYloo7ApPyy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52324}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.23.1", "@babel/compat-data": "^7.25.2", "@babel/helper-validator-option": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.25.2_1722308091485_0.1861868994585445", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-compilation-targets", "version": "7.25.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "11260ac3322dda0ef53edfae6e97b961449f5fa4", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.7.tgz", "fileCount": 19, "integrity": "sha512-DniTEax0sv6isaw6qSQSfV4gVRNtw2rte8HHM45t9ZR0xILaufBRNkpMifCRiAPyvL4ACD6v0gfCwCmtOQaV4A==", "signatures": [{"sig": "MEUCIQDsecuvIHftIfDG7srv2PwvUGSNSEwXMa2/Af95e8s/sAIgcAsHqL2NyaYtsg8RW8DM6rI7k8Mbq90753jf44FWGi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91524}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.24.0", "@babel/compat-data": "^7.25.7", "@babel/helper-validator-option": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.25.7_1727882068877_0.8903842216163003", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-compilation-targets", "version": "7.25.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "55af025ce365be3cdc0c1c1e56c6af617ce88875", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.9.tgz", "fileCount": 17, "integrity": "sha512-j9Db8Suy6yV/VHa4qzrj9yZfZxhLWQdVnRlXxmKLYlhWUVB1sB2G5sxuWYXk/whHD9iW76PmNzxZ4UCnTQTVEQ==", "signatures": [{"sig": "MEQCIFBXIJM6irNjhWJf8wicyAonaBZiYdgCNG+jRk7B7x2YAiA0ZBgVwtxBrClNtP21/Kk8qOOPAB4EtKE8yRWQKQm6Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53539}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.24.0", "@babel/compat-data": "^7.25.9", "@babel/helper-validator-option": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.25.9_1729610442864_0.9799934033779383", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b5491382189f1ef3ad1a510f70564762127b513b", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-a9Dhjj5zG18x0+9DvDb59dz70y1vuGSXisx9+893Ku5pf0YSFpPOR1m9OiAjcZ2LQJ+xevgCQU7H87SSeb75Gw==", "signatures": [{"sig": "MEUCIAkEvE6NYmCRfG4kLLDQgGkl1YVL8HsPFS6PXhNmr63OAiEAn9XPzmW3QwQ5BM2PMusrVw1dDxRcHEIKZJyM8Ndp/nQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52076}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.24.0", "@babel/compat-data": "^8.0.0-alpha.13", "@babel/helper-validator-option": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.13_1729864428120_0.299639234298698", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.14", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fd4ed1a5599ed33ac82a3be9f21c75c22cb57f3a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-NwbUP23wf/OCncErkQL0VakxA6KBAoM9julNaKRjqwx2FmT7ePCJOS+fjP54toKbJu3i8GU6gDlx6MjFYHJLIQ==", "signatures": [{"sig": "MEYCIQC7c0cfy6Wk56lceSDJmsfCgT3XN5hrAd7CBifxw9bmdAIhAIk0NVQJt/VSAxYLfFrillwMXlr5sKUP+U21caET8UAt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52076}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.24.0", "@babel/compat-data": "^8.0.0-alpha.14", "@babel/helper-validator-option": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.14_1733504020260_0.9470192451069332", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/helper-compilation-targets", "version": "7.26.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "75d92bb8d8d51301c0d49e52a65c9a7fe94514d8", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz", "fileCount": 17, "integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==", "signatures": [{"sig": "MEQCIDGX8lGaGgQIVMZlgkBNZ7cvK6iIGftgO6juV63yTaRsAiAfg/EtF6PJg4A1Lg8+zBY2xWT452O/jDJL9kampehLtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53676}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.24.0", "@babel/compat-data": "^7.26.5", "@babel/helper-validator-option": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.26.5_1736529110209_0.0631232324444293", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.15", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4043ad2a8a56990a63a48fece8147d8a7d94bceb", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-/nQ5TrqCDU5pysp6URvwmooRGzEn0SXVxEU0pW4q5y8vfpiE1fNmedZxUmUTsy8admQ4WcJv3MzgTQysEgHPtQ==", "signatures": [{"sig": "MEUCIAwxKq1Kborz857E4dmC/ASTh/zF26ffbr9mhKDqgVX2AiEA1HncTbih3IaqE5H0jxi9fXL9k48CV+q6cZbNGXj0y4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52216}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.24.0", "@babel/compat-data": "^8.0.0-alpha.15", "@babel/helper-validator-option": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.15_1736529843745_0.6944829593799295", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "028ae86c36d62801abf2ad55855be138332ca36b", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-0yyiWvOUDLgRwNfHMqt30H03oLYv/vDWGVttf1jMJl23J5pBUBPHZ2ME9Qgn59iHwnG53iP7CqKdIsA2Ew3KUA==", "signatures": [{"sig": "MEUCIHQ7ifid4rIIGusRSu35dWQ7orU1oqZNj5+oaG4IQvqzAiEAjP912obPKh0SFJizufurE4mHskD2eFHDELh0bFQxL3M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52216}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.24.0", "@babel/compat-data": "^8.0.0-alpha.16", "@babel/helper-validator-option": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.16_1739534321608_0.3312393604090995", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-alpha.17", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f48537041f1157ef0bc045b862cef7789f8ba108", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-P0KIHxoCkeeYJFYY5WZ220SgZ21KxLBDXb53S82+nnaE6YwT7et3+0w5AYWOeFFfzwjNHB6onfTsa9N3jzsRuw==", "signatures": [{"sig": "MEUCIQCODj3Yh2ogNFBe/j0vBjoSoAwgFrSNtTow4Korvv0qgAIgMgZLpMktq37W9qrmU6lpwk20sX06jcbVHamC6h7igKo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52216}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.24.0", "@babel/compat-data": "^8.0.0-alpha.17", "@babel/helper-validator-option": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-alpha.17_1741717472297_0.775384911501751", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/helper-compilation-targets", "version": "7.27.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "de0c753b1cd1d9ab55d473c5a5cf7170f0a81880", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.0.tgz", "fileCount": 17, "integrity": "sha512-LVk7fbXml0H2xH34dFzKQ7TDZ2G4/rVTOrq9V+icbbadjbVxxeFeDsNHv2SrZeWoA+6ZiTyWYWtScEIW07EAcA==", "signatures": [{"sig": "MEQCIBb6dTFsScUdrlAk25Bpc9D0xs3HgmkRTajV4LFJdrDYAiBROwNCSKlbF9ydKvIizray8setXNdv3ecHQChHoi/RxA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53776}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.24.0", "@babel/compat-data": "^7.26.8", "@babel/helper-validator-option": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.27.0_1742838100084_0.9204730217391128", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-compilation-targets", "version": "7.27.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "eac1096c7374f161e4f33fc8ae38f4ddf122087a", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.1.tgz", "fileCount": 17, "integrity": "sha512-2YaDd/Rd9E598B5+WIc8wJPmWETiiJXFYVE60oX8FDohv7rAUU3CQj+A1MgeEmcsk2+dQuEjIe/GDvig0SqL4g==", "signatures": [{"sig": "MEUCIDxyI8Gqvf3VjHHg8c1AAtQqN8KIj7juzWSw8NE/jPJOAiEAw5juXVGXdyGI7qp3pghqpU/JMRP+YrMArin6Q5lV6Pc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53776}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.24.0", "@babel/compat-data": "^7.27.1", "@babel/helper-validator-option": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.27.1_1746025711847_0.9474351498472309", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.2": {"name": "@babel/helper-compilation-targets", "version": "7.27.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@7.27.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "46a0f6efab808d51d29ce96858dd10ce8732733d", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "fileCount": 17, "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "signatures": [{"sig": "MEYCIQDFn4SnCIHUb8ULCV7+Wf/ys3oj/EoHbHRzbSTESZYnXQIhAIAMZskx2KgL9GRCA7aXAZ4MU364+1NDBBHH0d38AFW5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53866}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^6.3.1", "lru-cache": "^5.1.1", "browserslist": "^4.24.0", "@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_7.27.2_1746545628814_0.7741288690386139", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-beta.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-compilation-targets@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "94877033ae9a4656a2476182a03ceb88fe76f8b8", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-kzMxnTx5PWMPOaHnnnC0Pw7GHfYMTdZRC9TYqlRPZsYKGEW38P1cHzF+53Xba5u/Zo28if6Xg8Q/P+EdFQLYWA==", "signatures": [{"sig": "MEQCIE2uBtq95HAFe3nnO1N+vD7P1ZrheFSJu0jrC5sX0AlcAiA/7ZI3PX4QCc90JrJlq+nHzfmt4pFECiWXksFXcGWxqA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "directories": {}, "dependencies": {"semver": "^7.3.4", "lru-cache": "^7.14.1", "browserslist": "^4.24.0", "@babel/compat-data": "^8.0.0-beta.0", "@babel/helper-validator-option": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/semver": "^5.5.0", "@types/lru-cache": "^5.1.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-compilation-targets_8.0.0-beta.0_1748620244638_0.7183574636097472", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-compilation-targets", "version": "8.0.0-beta.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Helper functions on Babel compilation targets", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-compilation-targets"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/compat-data": "^8.0.0-beta.1", "@babel/helper-validator-option": "^8.0.0-beta.1", "browserslist": "^4.24.0", "lru-cache": "^7.14.1", "semver": "^7.3.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@types/lru-cache": "^5.1.1", "@types/semver": "^5.5.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "type": "module", "_id": "@babel/helper-compilation-targets@8.0.0-beta.1", "dist": {"shasum": "f69829ea112adc62a50f55ecd84bfbdcc52f8235", "integrity": "sha512-4L2FAdDHyzLRbA+un6ob8QPJCby4x4bHjr5A3CZoljcQFEAH8lGfYtMF0TV0S1R40TgFtqVrzUmX8xa7Pvw27A==", "tarball": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 52828, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDEysnXiyE4+oAphdKWFakEElWFA1NUQz7Qy4Z/nCUK6QIgE39sQDNk5qojdf6yBoH8+Fd+LYTzcM81Q11Ig4iHdgY="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-compilation-targets_8.0.0-beta.1_1751447039268_0.3694357525757934"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-01-12T00:16:42.602Z", "modified": "2025-07-02T09:03:59.718Z", "7.8.0": "2020-01-12T00:16:43.020Z", "7.8.1": "2020-01-12T13:02:47.625Z", "7.8.3": "2020-01-13T21:40:56.733Z", "7.8.4": "2020-01-30T12:37:31.618Z", "7.8.6": "2020-02-27T12:21:25.967Z", "7.8.7": "2020-03-05T01:56:01.294Z", "7.9.6": "2020-04-29T18:38:04.921Z", "7.10.0": "2020-05-26T21:43:23.366Z", "7.10.1": "2020-05-27T22:07:00.147Z", "7.10.2": "2020-05-30T19:25:10.741Z", "7.10.4": "2020-06-30T13:11:33.341Z", "7.12.0": "2020-10-14T20:03:12.513Z", "7.12.1": "2020-10-15T22:40:50.960Z", "7.12.5": "2020-11-03T22:34:28.251Z", "7.12.13": "2021-02-03T01:10:08.861Z", "7.12.16": "2021-02-11T22:47:09.299Z", "7.12.17": "2021-02-18T15:12:55.224Z", "7.13.0": "2021-02-22T22:49:46.073Z", "7.13.8": "2021-02-26T23:39:11.147Z", "7.13.10": "2021-03-08T22:36:06.206Z", "7.13.13": "2021-03-26T21:20:21.093Z", "7.13.16": "2021-04-20T11:21:06.319Z", "7.14.4": "2021-05-28T17:00:00.685Z", "7.14.5": "2021-06-09T23:11:31.351Z", "7.15.0": "2021-08-04T21:13:03.921Z", "7.15.4": "2021-09-02T21:39:20.771Z", "7.16.0": "2021-10-29T23:47:40.980Z", "7.16.3": "2021-11-09T21:53:03.096Z", "7.16.7": "2021-12-31T00:21:30.532Z", "7.17.7": "2022-03-14T17:07:12.787Z", "7.17.10": "2022-04-29T16:37:41.754Z", "7.18.2": "2022-05-25T09:16:25.915Z", "7.18.6": "2022-06-27T19:49:49.754Z", "7.18.9": "2022-07-18T09:17:22.832Z", "7.19.0": "2022-09-05T19:02:16.890Z", "7.19.1": "2022-09-14T15:29:15.355Z", "7.19.3": "2022-09-27T18:36:48.969Z", "7.20.0": "2022-10-27T13:19:19.660Z", "7.20.7": "2022-12-22T09:45:19.219Z", "7.21.4": "2023-03-31T09:01:56.758Z", "7.21.4-esm": "2023-04-04T14:09:07.767Z", "7.21.4-esm.1": "2023-04-04T14:21:00.399Z", "7.21.4-esm.2": "2023-04-04T14:38:49.431Z", "7.21.4-esm.3": "2023-04-04T14:55:53.972Z", "7.21.4-esm.4": "2023-04-04T15:13:07.919Z", "7.21.5": "2023-04-28T19:50:20.195Z", "7.22.1": "2023-05-26T16:34:49.930Z", "7.22.5": "2023-06-08T18:21:06.954Z", "7.22.6": "2023-07-04T07:48:58.890Z", "7.22.9": "2023-07-12T16:53:47.741Z", "8.0.0-alpha.0": "2023-07-20T13:59:40.467Z", "8.0.0-alpha.1": "2023-07-24T17:51:11.509Z", "7.22.10": "2023-08-07T17:25:10.224Z", "8.0.0-alpha.2": "2023-08-09T15:14:37.487Z", "7.22.15": "2023-09-04T12:25:11.681Z", "8.0.0-alpha.3": "2023-09-26T14:56:32.648Z", "8.0.0-alpha.4": "2023-10-12T02:05:59.659Z", "7.23.6": "2023-12-11T13:09:56.799Z", "8.0.0-alpha.5": "2023-12-11T15:18:21.199Z", "8.0.0-alpha.6": "2024-01-26T16:13:46.307Z", "8.0.0-alpha.7": "2024-02-28T14:04:28.532Z", "8.0.0-alpha.8": "2024-04-04T13:19:34.503Z", "7.24.6": "2024-05-24T12:24:15.754Z", "8.0.0-alpha.9": "2024-06-03T14:03:53.802Z", "8.0.0-alpha.10": "2024-06-04T11:19:45.924Z", "7.24.7": "2024-06-05T13:15:03.575Z", "8.0.0-alpha.11": "2024-06-07T09:15:15.899Z", "7.24.8": "2024-07-11T14:54:48.738Z", "8.0.0-alpha.12": "2024-07-26T17:33:10.602Z", "7.25.2": "2024-07-30T02:54:51.701Z", "7.25.7": "2024-10-02T15:14:29.207Z", "7.25.9": "2024-10-22T15:20:43.053Z", "8.0.0-alpha.13": "2024-10-25T13:53:48.396Z", "8.0.0-alpha.14": "2024-12-06T16:53:40.405Z", "7.26.5": "2025-01-10T17:11:50.462Z", "8.0.0-alpha.15": "2025-01-10T17:24:03.991Z", "8.0.0-alpha.16": "2025-02-14T11:58:41.786Z", "8.0.0-alpha.17": "2025-03-11T18:24:32.488Z", "7.27.0": "2025-03-24T17:41:40.331Z", "7.27.1": "2025-04-30T15:08:32.123Z", "7.27.2": "2025-05-06T15:33:48.997Z", "8.0.0-beta.0": "2025-05-30T15:50:44.816Z", "8.0.0-beta.1": "2025-07-02T09:03:59.475Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel", "babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-compilation-targets"}, "description": "Helper functions on Babel compilation targets", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}