{"_id": "@jest/fake-timers", "_rev": "148-c3cf202f7eb1734c961ef47677749244", "name": "@jest/fake-timers", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.4"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/fake-timers", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/fake-timers@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6ba610c02319a39e91272373fb52ab0653290a38", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.2.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-4PNjzgZytBLWA+PZLttVmbCaWu4/oPKERBEurimOZ+jPcK9Qb7hgt5GfNWId75TjF6AElBMjV46Qy7nHIq+ADA==", "signatures": [{"sig": "MEUCIGObYciQcUrXIPJFIWFf+bmILuy6WQ2S/bfwqtfolks3AiEA1vc7kgra7PlQakQ218gTiuhaUPe1logBxT6vmcoP57g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfo8ECRA9TVsSAnZWagAA6AQP/j59sZc9nC+gd2R8eGDp\n0z6ivjRgQy7kMnyMBKf9j8GzEAEPp2dUYuYcLQzoRB+PRShuD7/mdy+TJkpt\nrM/KKpewm/mxnb/0Ge8JPHHI9Ww8donLhfOrMU7MWUkooAGjsoYae34qid+z\n9DTzrWBkmtU4x2ISRn3Tt2wX6M6zht0cdN+GWJCNyGhGD9jtedTX0ZJH/vpj\nP9B8Sc1cpGxX4S5kyoq+Nt8W1qUyMkSWyBw6483kCxbGfVT6Z8zrwfxP79I6\nOT5c8lCNpKjrij7Bdz1TH66n+K1QUgV9pHL/X2WVtZ2RXEWk9n9xyUP94prv\nfwiRQwbXaBNKwO3dP4yBUBTjKD1VK1ClFUKkqg5HM21DlvJbhrYLdV7eEov7\nw6M2IJuRw6ZjptZubVny8PddXoicGQtkAD06SlCJZixh6CtdRV/MHFG0OrOu\nhmXdZmMPcWysOFcNlXsfYTfKR4aWBFF4MB0d7SPO/ALwzKPIxedP7xyynO9r\niQ+muayw+50WH7jHMzY7PsjPMkPqDIsn0u/S/1KrYjES7aLY8pLE9gIiAkQL\nbwMpT/xm2Xg9rzvbTHRPScAuEjCdApgqB4sY97DLNO1kzwVFm9rh/Eg4oewE\n5qV7YyMaIZ++pRzw1BGNWZlT/JtM4WYk6nGoT3Ix/e6DoWVwnltruv7Jt5uM\nphw9\r\n=y1B7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.2.0-alpha.0", "@jest/types": "^24.2.0-alpha.0", "@types/node": "*", "jest-message-util": "^24.2.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.2.0-alpha.0_1551798019478_0.6925445895010915", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/fake-timers", "version": "24.3.0", "license": "MIT", "_id": "@jest/fake-timers@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0a7f8b877b78780c3fa5c3f8683cc0aaf9488331", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.3.0.tgz", "fileCount": 9, "integrity": "sha512-rHwVI17dGMHxHzfAhnZ04+wFznjFfZ246QugeBnbiYr7/bDosPD2P1qeNjWnJUUcfl0HpS6kkr+OB/mqSJxQFg==", "signatures": [{"sig": "MEUCIQDpL6EfuO6SNNz1yQmnIlpYF1jKYkNmYA0b63W15bqJwAIgMOKrpDSEeBemOYlI4n/gF4BLeF/fzWQQoBMrsSd3H5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRXOCRA9TVsSAnZWagAANA0P/R+Cu1atHFKD9ko4rod2\nrfltj07dbQxYNTTdTDKZVJ1x5plvH8084KwutUan0coXEEEr5o91chwXRHcY\n5JNTl8zGQ8RyR57odj5RAK/SFmsoRFoex9bTVqkT8U8IOC+rd62tYXBUJHpn\noaE5XaVnn8r+glyRndBjI/LFo06frr551LZOAopPmfecoxPcTC232V7UGcsT\nKJgzjha+R+wR5MChOV5vJGbvpESm9nX/Tvblxc6UvflueewTSd6iLvcZp36z\noYfsg/NYjqakFG0OeA6MDycKa2AuXcTTRxLWX/9tVZiju9N+DGvk3+PEZKZl\nfH5SuskCngteZChNLJ091z2hyfFxyCJfIiZliCsp5vW5QPWh3BRDFMxGONm0\nyo9dHkJv7WNX3yFYv5bHUv4pfhann4IiL99XDIKTCE32NJDYsobUlvwKQJ/q\nLla7DvUmQ/q/fj10Ig3dFg/vgmubN12nhLMJ+ogwAbelFe5YacQw5c+P5TBz\nT0ZtnnyZDGoUr3/m9JTnYfKyCgMgqGf/9xjtcDBtAd5QxtIrVFThzvY7wbUw\nhBRUpHaBfdszUL5ueotmS5m9riUjojqYSMxRxnRUqpsfatQ6tqmYgw/95J+f\n7G4lkENX7+MEq3aChIgk8psS/hZ3Awhb4qcn4lfNH++YNh/n0Kg3mCV2Phx4\nQeLR\r\n=+24n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.3.0", "@jest/types": "^24.3.0", "@types/node": "*", "jest-message-util": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.3.0_1551963598064_0.11045513850168209", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "@jest/fake-timers", "version": "24.5.0", "license": "MIT", "_id": "@jest/fake-timers@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a29678b91fd0876144a58f8d46e6c62de0266f0", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.5.0.tgz", "fileCount": 9, "integrity": "sha512-i59KVt3QBz9d+4Qr4QxsKgsIg+NjfuCjSOWj3RQhjF5JNy+eVJDhANQ4WzulzNCHd72srMAykwtRn5NYDGVraw==", "signatures": [{"sig": "MEUCIBt/xQ8gLx+mopHlvVmCAZxym5tfqMQD9sI3GhrWlQc5AiEAg2rfG1rk09HlIno4w03cBJAqiQ0A65GHt/DaiftDW9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+AdCRA9TVsSAnZWagAASmUQAIzc+fqVH/OowSeE15bW\ncgyWlsT01qXSxGywddOXpLg4Rl8NdSu2D9d+ZNJ0jjNkfXSo0xE0h511s4LR\nPdopiMMXjLe1uTUDzBLsOGTdjLrYpPV/YaFXnv3zz8SCV5JaZUE/pw7upP2g\n8KvTeTFdm3LZY4HTO+HDFNhidrhOGnRaRr+OuXfkW8pSUvSGF6l/kkqntyGt\nZ1ZhEsmsY+cKqMu0JmnvxtBxjojxGjRxNFZg8qpa85n8NTyPxbtG2Qt++cCi\nGHIdE3GQPf56SpRNyK8nLN6Pe56AjM4ucLpTFZcaXB81QwoNxWSMZBtxQ12s\nkngTLuwmj5xxGzS7o9De/bZ8P7hvWSIqoXnwWTByB/ukhsoqjFv2edbBZvin\nccjBYVWS0OVzATRcD0kuw8u36E9MEZ4hHdLCYwNt1T8Cp9ycmhLBkgBpvK0h\nzEZubcJZbGtJbBXZFM7wXL51FGN8qVBklw2DaDffCCBh7gT4BgPV1l1OI2Rd\ne45vJU6EHyJnRW6NisGOv0Dm6tu0Hvljs4v7dhGwUoJjaGpZBFUzYYt5+OBr\nz3W8uO3Xnhc4rjxY9iUofsrxdeilVPOAqchB1BEkKjKcNzXl0/1D9pSjhWSX\nKN83JBDsLNFh2BQ/ljEa+TpYhhmZMoOPBE/K0SjghH7hDKe/EcHgzFZKXmuk\nP3KC\r\n=WQPb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.5.0", "@jest/types": "^24.5.0", "@types/node": "*", "jest-message-util": "^24.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.5.0_1552408604454_0.8603439844506309", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "@jest/fake-timers", "version": "24.6.0", "license": "MIT", "_id": "@jest/fake-timers@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4eb0b47539883742e9f969e983770230f5a57d7b", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.6.0.tgz", "fileCount": 10, "integrity": "sha512-92nYqkZceki6knls7F6/FrPxKXnQl0QjYXbjLk/EFfp6xcg4ETLQSAur7pMZsiAzazAgQag/XDvMmKwMbunAeg==", "signatures": [{"sig": "MEUCIBDidSN1HfkQxVPddDdk3tGvy7fFh6iN2MK2JrBNu/VkAiEA0GX9plkVwxbJBqUG7MB+BpMpTrFVOMAdNpW3bd6++nI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAfCRA9TVsSAnZWagAAnOAP/1x57YhZGUTYIUiMtWeY\nILl+o10nsAwOAoJTRp4nXqkkQdhVAgB58JQmo6s8hXKaMYblvxjwhQ8llYK6\n0UU2M8Z8xaSXQ4/oq09hFFDrhdsL7v2VUx5Fy2P3s+1uyXm81j1txiYMn2R3\n8F4uqFCbdY0F6QsC4YG3UPQdTknFsom8cIN56jeVT3liX4v9wqvqp+KFsG0x\nTqw8Ql8ddaiR8KWHMHaE/J17iuldN+5seylHv7sQyVKox88PYWGFHag13o7A\ntRh8870pT99P5YT8JLkBZeM1y8GpuUcAL2zRRGLpQhuHn+h3ZGqlFsIHTwi/\nMd60XPbmPf/qikAlKnZ+gwoq5/m05f8ShRJFfV+I+NuOI3EUwNk8Q3Fde+Ep\n4ERFBTaVuslM27msEmThukTLEo5TowjhhWDHG3uJmg+/Jfdq2gkpX/oW2379\n8KZnyb2GjrPjlUuxavceklUP693ZbtrJaFF3I4xqAOQL4R8TwaX0ZEw2mJ00\nTUijF9NNBxeGrAHizuSH/10cKNrDJwwukQUKznS9Dv5c/u0qCJ8XTxx1I56X\nzvyToLHiEHvXCueKz+DifdliygpKDj+YCfBVr26CALMflitVR4vEz9LiLcOb\nNZf7WqQBzdZocGeq9iJP8pw59+NhcWyUWxIYYuItyEUYf/7t9nWJk2GomN+h\n5wtb\r\n=xY68\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.6.0", "@jest/types": "^24.6.0", "jest-message-util": "^24.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.6.0_1554157598973_0.038301784229627156", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "@jest/fake-timers", "version": "24.7.0", "license": "MIT", "_id": "@jest/fake-timers@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6735c6d88ee096a6303f369fa5fddef12f79779c", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.7.0.tgz", "fileCount": 10, "integrity": "sha512-wwq54UIqxC0JsKNQcwJyD4JjSkUYV9rZ1qz2lGGG1iMrFgn6ls37GBo/Cay2qCcnmdyVy+kQ5RE1+7Un7Kw4ew==", "signatures": [{"sig": "MEUCIQDGvd8OaUq1cOabINIvJavgOglhn793ZL1nTHU5dgjlNAIgcODdfcVXZ93f/L9uMuYU4gb7GTMHDOltOK6jBtLeHmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC63CRA9TVsSAnZWagAAqYoQAJqM1dyCf7WcISS+ZG1b\nd5jN1wV4QqMANOn0wP/wztm/wwWQLkNjiJ+pbiOqqZZbPiTLJ0vJDzQwDkrQ\n1jzVNUveNoQkK3Rs/bR4AI0OcVI4phznSRnTySQWokqk1VC1lSCbA2e9H7xT\nlxxqFqTDerPVyMwSXUIPd/9H8PjveIL8bc1CsayhzXK5fLAKE9/9Aug8TU2c\nJP0VNLaw5XU6ULAbSAllQak564U6Ks0BY1Ds4cdjkO0p0wjG4+HOXiZiT7Wp\nWZgRvBTKC82hwi/SWFb056Yi+uvC+uHjpQ6MXh46ci0uo/rl5/UfpmLlH3MQ\nmC2nJnr6WrKXz6flH6YftyEmLjlOcbdLHjWsusxLxZvDmsh4gFZb9aUf2Cyr\nf5eEzJa6GWDL+sweMr3KQRoqOuJU+27+pdLocW26v+i1UeCe9kFF+NK1gXhu\np6nmKecMgYlcwq+C58K9nxFkFKFuR7uIPNrckCnpN2AQ/OSxdgvqrpJtTM1n\nNMWJOzBkRtjCinu87QFS0Tf/B8s6T3C1GrSTGGj/vvwJyISRFJc79U7wV8/c\no1feJgg4ErqSU8tXz3l4TJd/oQ4zXPgVzpyB04WOCQKsHWFr+bvC7lL0SkSj\n0qPGSqvQp5FvSlgV8xXHdHPeTomJHx/r0SdSyPnqsOfz8jyR+yXBP2HuKX+M\n05l1\r\n=thlA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.7.0", "@jest/types": "^24.7.0", "jest-message-util": "^24.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.7.0_1554263734822_0.3425294136046826", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "@jest/fake-timers", "version": "24.7.1", "license": "MIT", "_id": "@jest/fake-timers@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56e5d09bdec09ee81050eaff2794b26c71d19db2", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.7.1.tgz", "fileCount": 10, "integrity": "sha512-4vSQJDKfR2jScOe12L9282uiwuwQv9Lk7mgrCSZHA9evB9efB/qx8i0KJxsAKtp8fgJYBJdYY7ZU6u3F4/pyjA==", "signatures": [{"sig": "MEYCIQCt7MieizIxBM5NZ51G4BlfkRb3gklUVOGWDUw4Zh/4+gIhAKwvEpl8MseHsq9zmOEcU/YWpxO0sU9GniYwEy4ZaEDb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVt+CRA9TVsSAnZWagAAk/MP/R9bbJEq+8oNhtlP9cNS\nlSeu1zqDvHYZE75ipST8twhouCSLeDBqbU4kfvQSznZ/3nWJofJVkKgqX1WJ\n6qdVjZxUjeMT2pG8C8HLgSUP3GnrygnV7e6EBfvN/930HWKf5HDEfqv+eHGd\n9pRlBKwqAsFNm4d+eDPDT8a8omm65W7plAVgUQhrZSRTHVG4TlC2R2MynH23\nWS8jdtwlBW6/nFX34NrQQCpGYrXXF67fdgyhMTMNMsLgwfBKmQM/d3PYj8W3\nZ5LHXcqJLXOq6OBJI1E9bGrunwefBxE9PPZsi1amXFaQNIx1Gdruh1fmtwfg\njaIZ+8gWXTxhEqgeg6oEX1Q19q10lQNDnY58AtHnGI1ggp2H9Jby2DaSGj2I\n/IEzAddtwYIFhkuWUzCQxxYMAahOJxmcmzV99Qea1NpPwtC8YT5M/t91JjQh\n1rfk6+uAx4g95EhzhtDJoHxNnfbN/hxbFpsHN0b5dZca2UTaHH6mv7Q6UuMg\nS24ytruMou7/7tIYMb7vz3H70G2kdFr68+3HeGL5UDGBw7Q/viQWLR9NvR+X\nn/hnEsgqDcb3L/GEKOXvdAFTSwf7P+lJZdLMEFV9tJr45Gi4dRFUT1ZFJn2f\nZDNFWnDJ4E+JoeAhd8ez8rbw4dpCgrYZQbBAdqG1eFS/RZUhp42u/DUrKc3J\nl+Tp\r\n=xzDb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.7.0", "@jest/types": "^24.7.0", "jest-message-util": "^24.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.7.1_1554340733338_0.04944648758666759", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "@jest/fake-timers", "version": "24.8.0", "license": "MIT", "_id": "@jest/fake-timers@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e5b80a4f78f284bcb4bd5714b8e10dd36a8d3d1", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.8.0.tgz", "fileCount": 10, "integrity": "sha512-2M4d5MufVXwi6VzZhJ9f5S/wU4ud2ck0kxPof1Iz3zWx6Y+V2eJrES9jEktB6O3o/oEyk+il/uNu9PvASjWXQw==", "signatures": [{"sig": "MEQCIEPW7kfXRUIZvu3IxByodj40pahH7nQC4tMIeU50PYtkAiAmxeAEyV7X1fdSGYvvgGP/L6oT1nieRE+bV70cAT0ZUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkQ6CRA9TVsSAnZWagAA2yUP/RvmqLwaNYUckbA9+zH4\nW6gX+gfF7+f7tiH0SZOhleI4z+057hjm/rIBg6NcnYEduVK6+a66NLftAWuM\nc/eBVOHhF4uKypu6nsynQA+WOkmzHV1QOJh9ii7vVxORvBi7uTXpXvJk36DM\nkaTOwydx5US7s3iqRnVyw/sKSXFwrYnMA15WWN5Ymr6veMvsXUNckNyOE919\nW6rs5TngCiv6bATU3mm0P3GPxJWf+EESLo6gc+KtZj0wbnrRYBMg3FWzTYOq\njB2ZYo0ZceWSHt/bd2QKP484Z6rTGxKMzLKxn4THZPf5oKdEh6Nsm7cW8fy9\nvNuA03OQcTD4nK+wj+wJwWkT54rDz29S1PZcV1ONdwE5aItUnJCsmAfDg2Kw\nY37HXYx1lVOF5QUUKZxThNwVLNSJIlNAYYnkODPUGEbLiYsUVLTQTgbXRg9M\nW7WbEqFsV1W/7W0XkCCxHjBTQtfAebDZ37/Z5rAu1tnbV/9oqewUtElfU9yo\n5LkPwM+ce0fwYS6CrlM5B9wrLpLGw6NloRDBiEvPk2zLa3zGOLITVV/gS0hO\nZZu57jPicz5uNwqD+6csETWsnGs0HHlH8fVdWPXvqlwSeC03uWj14KY/aTOG\nVrfK1Ez4DrRmMLlBJBBT/N/kHy9++rsorHHHGd8WjqBt+MxKMtvQ8gQ8iOem\nDlv6\r\n=Gfj/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.8.0", "@jest/types": "^24.8.0", "jest-message-util": "^24.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.8.0_1557021753441_0.8545235708453591", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/fake-timers", "version": "24.9.0", "license": "MIT", "_id": "@jest/fake-timers@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ba3e6bf0eecd09a636049896434d306636540c93", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-24.9.0.tgz", "fileCount": 8, "integrity": "sha512-eWQcNa2YSwzXWIMC5KufBh3oWRIijrQFROsIqt6v/NS9Io/gknw1jsAC9c+ih/RQX4A3O7SeWAhQeN0goKhT9A==", "signatures": [{"sig": "MEUCIQC23gNYRnvz0JZHYUVZ7n+V/sI/ya54eMATcQ38S5CT5gIgdYEX0za1Jg6AxqbT8U6F98uFu7fmsVLcg2cwM10vAhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkV6CRA9TVsSAnZWagAApuoQAJhz3roXBVTKqjI6AaEu\n1QQPRfez2GQwrYx8JBnImM4iGrOTZNBD9oSJY904Q7FDbpX5w0qbOGWbhaw8\nAk8ugvLAn7btYlubWxXtA7FpimK2owsFvR1f/Obih+KHAqo/VJexBYYF5VQT\nk5Xy5uQl85dDpmSrGXH+dyIN+YoR5yVPMHMS6ruzO+XLjCQ1xyGjHQE3ZhYr\n+Eh+pdMnVU00a/Hppesx3Ocg3jwT+/aYVFeuaFOo0/0BjR7i+WYguMtQTAvn\nvIq12/I8nboiFk8sV95XNgAQiMIjmxwnMelFC1Y4MVLdV3//G8o235L58fDZ\nkIT18DMoC7mKxhAr0RDmznTiIMQB6nc0Wyi3GBB6QOvxkUADwKlv605dEe0l\nCA9P4TjG5qLb9HZscSnRR6SavFcxBGSfEqEs+DgMLaY8bB01sJb+RiS2hS5o\nIvg+uloSpK/KybbvuNxGNn2OVkhfrtYRzOiiBS7/9syUwzsBRxWZQiyy4hOw\n7rPYO7lQeuZSnJ1Fq225oPIRV57dZjdDxXk57vNL12CLTVv+sQV413rFKO1N\nKo5fwTgEksColOxolW9F/2MMRoj6D07bWbcBrnM0VsSSHsMib/+Of6V+54jD\nqIDHZhlDlL4VuoYZkJEougdyzyuDw7gA7MLgNNEIEDm3FOAlodRZ68cVBc8f\nh76p\r\n=v6uy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.9.0", "@jest/types": "^24.9.0", "jest-message-util": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_24.9.0_1565934970160_0.9042037707582158", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/fake-timers", "version": "25.0.0", "license": "MIT", "_id": "@jest/fake-timers@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fb408322ff0798ad6fae94b001e10c76d42d4a38", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.0.0.tgz", "fileCount": 8, "integrity": "sha512-b/v/phUcZtwmQK7ttqsPHn+TmwhS75RNS7AILTkTKTeYOCc+aM0BrPrGrNFJnXglehvUJQ+U5q2axYN/1VFt5A==", "signatures": [{"sig": "MEYCIQDebhXMVcSUCc9joGxOlAR2rx8TRZntl2Qb4lp2uCbSmAIhAKjU+X+Bd5Xi1gTno6ZlXtiQwOrmo9fYyD4EKfhuqhPq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrZCRA9TVsSAnZWagAA/zsQAIuy+wkfwQmRt3MV/l9W\nhi6ieoIC7N+ojzcZDFazUS7Ww07ejgPZQubGdoIiO92Z/RM3kd/BArn2SlyB\nb2oSYJ683U7RP3+GowSbrgTMQhymI4GZvcf/lQQEjvxNoNODKxVY/xVJzZY7\nxzCev8ib7Ma5jLF4LxBxzrcElxDrENyEpvS1xPPuKBq8TKnI4t9jSzvK0BYS\nqfcqEIAvPHoVq1rVj6MZslcXZ9mVvTSlItICJJJ6XVdNSpiuf/0Y49esAW3L\nfzzC3a0ExHm9e0KpFzuSl8dpZaSDCX1CSs8Sv/ON9R3EowVa6KYsP5ZRjLJZ\nmHM7ylAlK79oegKp0JTej68O6AyoZk336CYz2Eevx2GcmvaVsO0lXQPQxPLW\nwABvZA3zjKEO9jksEW8UpepqWPicj345kSKeSS+cqJl9+HKN7zEymRsWLpBI\nhesa9zXpXI8Yr/oq0b63+2fd8WfcQbCC9pufxI8Z0PUPZBaqGL1BubrgemTK\n/yllMDecH0wxMNlifM38NOVmsgBxhOiWy+E7btYM6itDNAyW5Fa4/9UvOF2g\nnv3gv3+mq0iri7+8Sw0frAzMsSMkcopD1zpXPMe6AwBvBvHQFjmu2CMETWSZ\nBZKBqu6Fvaa/sLp52tNSy1XtclsSHurYngwap4KMr/Htk5BN392iAvqGHskE\n4Z2x\r\n=GLR5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^25.0.0", "@jest/types": "^25.0.0", "jest-message-util": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.0.0_1566444249200_0.04234976053198469", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/fake-timers", "version": "25.1.0", "license": "MIT", "_id": "@jest/fake-timers@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a1e0eff51ffdbb13ee81f35b52e0c1c11a350ce8", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.1.0.tgz", "fileCount": 11, "integrity": "sha512-Eu3dysBzSAO1lD7cylZd/CVKdZZ1/43SF35iYBNV1Lvvn2Undp3Grwsv8PrzvbLhqwRzDd4zxrY4gsiHc+wygQ==", "signatures": [{"sig": "MEQCIDwxwZXmYVUdKZDSY+CDFegD79cPyamf7po6BgtHlZbtAiAxQTOCH6jXKYXBRJ62z+5KVxNwurTmPrCoCobmS2QDUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56hCRA9TVsSAnZWagAAGIYQAJSsJpGL0dif6onbaCQl\nOlJ3RCC/zYppbSs4NekMorTp0VbX3mFsCEZWLSG7YGArC7jPcmLou+OrWQpe\ny/OFDa9+rs6YeYRg1WpDe/8ScTno1Tp6LroMEJn5aMbcDY6YsENZK2/AP16r\nvPXVFMgzxifB+b2vhrAf4emX+la0W9aOBk7mn6ARtBdZIJS63L59Ldb71Fvr\nDIPU/gpdRe6ul48srYAo6gXivbr/tkcG6Dn/fKwrpjLLGg6xQbrXPo7ZjKjp\ncbNH0VrzaBZlJAoPoiOmpjSl8kxJ2KedZDNvI7sFbE/2rR3nwu2GAItTvz5u\nn90RnpkJD1dnc5gJrx3781iBF2YDlCYQoBJfubuXIND7YXAxCSRUqnOPcVh5\ndlaAcTJPQRXlEfV+teYKfk2dOQGm4rrFIds/7Xtlky80zsVG9YU8cW0+XV63\nV9p9+vjZ9UMiDUEeIM1NOwM88tWPSIdkYPurSyTAh2b57MfrJqO1/qFt0xW1\nVKcfs7KqLfk85J0up+RNgakLFXXmgDoqvIpt3aBthQbt4VpF2lbCzOen3hkn\nRVtFGtGqrm0vkncvj0kXZghQV9on3OQtP4BpIDCWX2z4Lz0k3u/oJHKaVOEA\ntTEo9i0CdiQ21zmcHASojGbFPe+VvnB6gSsMoZrSrbjnM340/sbK2KiVUH7S\nIGCY\r\n=G4XL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.1.0", "jest-util": "^25.1.0", "@jest/types": "^25.1.0", "jest-message-util": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.1.0_1579654817111_0.5844864997211456", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/fake-timers", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/fake-timers@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1c90bc038c74018dbc1651debffa1604510b00d2", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.0-alpha.86.tgz", "fileCount": 11, "integrity": "sha512-Ca42+ygZLZVtjM+wRYftY0k27M6Hj6qpsXAo/0BnI2kisPbYkQ8XWTRgylAOuaseNwvNZuR30+8WHplLdyHUgw==", "signatures": [{"sig": "MEUCIFyqXfbfJpOQ0vTWMQl/UYwNj/jTNRihsY1l3BqOucD+AiEAmZGwouDoASYyNaen2aFDsORA/OpASJj/Wi0BlAVP0ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5IECRA9TVsSAnZWagAAe6UP+wQ3XENonIKs6hvV3Ku/\nl1/0z3Bk3IoYIskUB8jx9+h+YsHiVSIh0hnE0FhW3nSEfid1kBWPKxIhs3xt\nDzzijw9Ob0q/YZSrL/4wAr00R4XGHwmFyCnB/m+LU8FdukarBMkcqLy9fxVv\nVEXt/MjZFUieMcoTFg54OxV04Plw2UJ7ctDt58AKx0/31dbsvO+tsiDeDOin\nEVWUxQi+XTJ8F+qILa7WxmX5aJbvcn0nEK0pExKZnMaoU0ylrT8U0p/z4aox\n4rY/zrJEDsfRt7ZPi1s2w23SrYS6EeFA+2jFcZoq8vOIRIsTwwwl74snT8pg\n25rX6+AhKEr7trJRcSfqx1qaBd6CFQS4aZoWLo5kO3V9NYM5APpnkRmCvSZF\nvtTtZw+CWEDHAqevmPleM2cwKyD58YCxA0j+uwZeg8GtrJLJlfV3LKRGCeal\nybYxqX0VzDbUtlUgiT8GbzxtBHi50VVsIgIuyWHM4obdKbYPRN5Thtsp8Pta\nymJOSebp9lSs8XAzR2xxEvvLqlEfXigDuUVlu1NH7vyrJ6jkqVhLhYIqjJff\nTYc1jMq8OkRUS/aOekT7edoDqwrg8Wh6r4DM2Q9rSLDaQl996M5ofnQAU7tU\n2zqJKahCAw2BuaE5UoSi7vTJBOrbxnTIb7W8hS1O6bFKMzZR3djlF4F1r7N9\nMGBM\r\n=tZhl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.0-alpha.86+cd98198c9", "jest-util": "^25.2.0-alpha.86+cd98198c9", "@jest/types": "^25.2.0-alpha.86+cd98198c9", "jest-message-util": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.0-alpha.86_1585156611566_0.7475040114132836", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/fake-timers", "version": "25.2.0", "license": "MIT", "_id": "@jest/fake-timers@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9410a2f4db59f09d661005193d1626df22b316bb", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.0.tgz", "fileCount": 11, "integrity": "sha512-IcxxIEHsBspeentekQW5OLJqBWfAb+CSabDo8yyYG13iX5mKq54B2bdqvvPdAZ5LGK7dgBsTsFgteEn0xTtylQ==", "signatures": [{"sig": "MEQCIB15ohrjLZx7r3Bv+QIypYXWi3pR35oqNhh63to+TAJ5AiBs+LP67VZ/4s6CUF4LDx8KOQtSqcABx4zApsRQku7Utw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5vRCRA9TVsSAnZWagAAjQUQAIM54krvNq33ZrvPxhqw\nRTJn7aWCPZfl8xpNh6cmM/9CtLubjQVhbQAhuNkZ1/cmtvBK71/Gc65ZLEZG\nUVZ9UzGqHxtneJeyK6d/ynwraJVYqdONT4bqrQMm908RK7E0DGzn16yfluQX\nhMVo+rItxpy938Fm+EwCcenWP2QaWXmcXzJXpjNkGwb1GS2KDQKlfG3Ut0bE\nIYdNwenPrbwJRa/16u7UbMseLHXD6dMIm917E+b4SQ9m43NLa78LKhNxHV4J\nbH1uHOOTbwrnILhb/6ZOzwAvoGGaZufZOOTMZ8FbaTDkGF8RJOJ83xzmNNxN\n4QeTkASUZ7wvX0TIV3V9W3sE5gG1s4EhmbNc5pC/Wwkb3FcwuK0d0Yey7cUm\nyAu28XbRas8Z2ENPASbDrt6yX5YNLYO5Rivw/OkbPLrPKJoXnVfu4l8fNkFt\nE5Dz7TZKEeCysNPCwJb/kcWSp8OwPr31r0Z6qgAEGhJqfmgRgUVNmhW3nxXH\n+uNxtzwxe23nMByXAnsKOjn1iE5K39DuNRAmOAIQKn9+d+9Tn3bjzgRGjkPM\n35tGuhWcyTN3Xy9DemP7VyK1wvYeBQWhJDm7ypASZkxRd1HB61BF1dSaTKza\n6+Qv3a+YXe51sYDj37H2S8Nl2FXE6XkgrXuJlhIY73tqbc2auWtYZKKRk7CT\n9e8c\r\n=0+de\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.0", "jest-util": "^25.2.0", "@jest/types": "^25.2.0", "jest-message-util": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.0_1585159121408_0.23440587727348694", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/fake-timers", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/fake-timers@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63f494935df9833718d461b8ee172d2f4abd56c7", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.1-alpha.1.tgz", "fileCount": 14, "integrity": "sha512-hFbkhO/c3yqsCYa5EvbZddUVMH51FWw2WMEDlmTheIE0g4EIT5RUBCLnDsoAlYYLdbAX8jFXw1XYe1XPEzVZfQ==", "signatures": [{"sig": "MEUCIGttL56+q+QKBh9o15nXUsCpukZy3+hr9OzmzIq+GP4YAiEA+k2V+5NNMGzNKGHx1xNc7lMFyse2RpbIQPgVsTX9LGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF/BCRA9TVsSAnZWagAAA1UP/36nvTBHlAaOX7OBkkcW\nzhvG7zws1T+ck784gVG5M8tNccpgoJFRVJXP0iwdLqzs71dnqfN/tIXLbcYv\nrZKoJZyKgGBAXI7lVwsh7ArTSyMmp4KQdTNWVi1nouUi5QfUzzmO1uZx4oFv\nzevQZeA85GpOkaIlOtAYjgNvaXMaCqwPikS5rZ+DI24U81epK30fxfnpTKOD\nLA7azCyxGSshssy9wbxelbx/u7+mJh2TMUtdllVB8RRH7Ng6aErX9SSDTbjW\nAoWmfL8iJ8kOr2kY3P+a7pT+mafoIvv+L8ER/2w+QXEf2XQUVDqwRM6YFkZA\nRsrE6b3jafXESVsGSR4UlWdPdw1Q1eyin+0CHzx2S2oNAEKpx/xei2n/ujBN\nZ9fI8lRPUatM6ZWXpc/UsYF1UJqkVzWDeSq4Vf+pNrxpt3cRzPqhkJr27zd4\ndcYD8cNw2c+3E0cnBSXnwaTwjB8Cm3bR6RkeNrwh3q61V/EuF+PYjv/tuqAi\nnTQZhicIu1DdQQxkxbMuhsvOa9vMYM524DNSYPWIEXvwYHNUE5v1muA3neBi\ngVaAEQbZwbXy+l6JzVwHzoidgwr8EHzXu110/6+peL2/rLAuYDKIbvp97uM1\neqfxN2UREPu8yZGgW73xePINeX//vktJqVG8M1X4qYRz2BTpD+SnDx+C7OWg\nkIi2\r\n=qaRE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.1-alpha.1+5cc2ccdac", "jest-util": "^25.2.1-alpha.1+5cc2ccdac", "@jest/types": "^25.2.1-alpha.1+5cc2ccdac", "jest-message-util": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.1-alpha.1_1585209281539_0.7457664329426121", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/fake-timers", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/fake-timers@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "118de731eaeda9dd44d9d659ef36232b057bd00d", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.1-alpha.2.tgz", "fileCount": 20, "integrity": "sha512-vey1cFjx9DlLeYBtTnYnrovDKK3oBCivZqpozL9uuRHO3Ho1FB7piQBcMfDXKGpWNd5Ef0a/w+jW2ZmAIOMSmg==", "signatures": [{"sig": "MEYCIQDtpZQGC02JaflQvmE29pASLzzMWkSoYmx8VbLUiYCHxgIhAJby9BMvPP8WMUoscpcH9xS+JZ7GtutNQZM3RGvI4osc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGOJCRA9TVsSAnZWagAAE04P/iO790N2VSN68zorhR8e\nt3pLWDPx7/lu9Hq7IjHlqkToTc/hgbH6K7alHoujnMvx4hHUZpmb7mBCttsm\nNgWVrABrBIQSZC/Y4lLy+98S9UKe2f2Yi10wrcrahHO6dTY5UxXDAxqJ4bit\nv+dA3mSSU0sAO4O+UeK5WR86nFdnELsGQJcwJ/xDtQSGUXnVm1HZuAjV9wgt\nOO7aZ7EvHYup5QSgAKvIxbol6RqAwF5xo+i8pkgBMXBL2q7J0kTcturQQjj2\nPC1/UGYGyOKdxCNDt2W8CFhWBmEu+Tl5/NDvJ3INI0q0nHXPR9KdXZspj0iF\nMqvqbtaBdGuhb2agYVBPeiW70lsUYpr3PPIZIAMWi+Y50QlLLPY/S2PYHruF\nLBlBgIMkuKFOwq+hO83kvd7IZ61JAV/xihYBPWOZjM6TCFFkzsHQSPJ8VBMX\nYWynfJuLGeplHQOh/2I2f/lDKf3teUqYl9ysFU9YKnG97WewCNepJ0iIsYwl\nVYciK1WQogfM5APFDtlY55iolKkVn3THLXWZwMwAGjmNenF9AC+iI1Z4rcY2\nfZ4gQf7nlnRkAvecnhcaSKHZEQ885Vh9hG6wl8Z3GW9GIxzUhv4t+CgDRb6T\nDr+nS4KeNS5ILvs1zlel1VHpUHnSHwhNnwaWtZMGXjjeXErGgfpO+izysTPF\nqLki\r\n=/b55\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.1-alpha.2+79b7ab67c", "jest-util": "^25.2.1-alpha.2+79b7ab67c", "@jest/types": "^25.2.1-alpha.2+79b7ab67c", "jest-message-util": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.1-alpha.2_1585210249332_0.25480318908741295", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/fake-timers", "version": "25.2.1", "license": "MIT", "_id": "@jest/fake-timers@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "caaaea22e810796d3538a77fdce6e554c864ae72", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.1.tgz", "fileCount": 14, "integrity": "sha512-H1OC8AktrGTD10NHBauICkRCv7VOOrsgI8xokifAsxJMYhqoKBtJZbk2YpbrtnmdTUnk+qoxPUk+Mufwnl44iQ==", "signatures": [{"sig": "MEUCIQCIWqT+r+kB+gcx1Uzhrc4bQrxs+xWkCPLV0Xqz22wtDgIgBac+kZU4ZRFSIUF5uexwqt8qqJ/yx6cs1kWIa+AeWFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9wCRA9TVsSAnZWagAAriMQAJrdhRX6DbuC9NM8S6rU\nrn6kZh6TVAINjAuu2lSk2HwWV5KzQ6wY0zn+UBKb6kB1jTmZZnaxnQqvV7lF\nHrVxDiG3h5iOO1hb/XtGTe9EkykZ03NQEpXBskZl33zh6qjq+NMTo3Utf3Ea\nUHbKupBrTHnOXm4927gz5WdAvzQkmAiRXWQfE+9ZouWQ0gGFScAR6jRQrq/H\nymD5C2t6LyCBVTrgyfnjS+YR9u40rBS8aA/sWSHxQGCJxxV9HQGqNB75jTJl\n2dk+sw2hIzTRF045WVuBhjLvSZvklHFe0bYiBcQy3zuTIqJJjd0PCyWTcDRZ\nYZ/BlJd+g4qeR18SNbA8kFU0sU9AtjZg22ArtIzOzeJ1TmlVFSkNovgOCLKp\naJ26EIwG1p3pAJL3m7SOdm5bVWK7EnIKLIiUouZ+wktUcUIkxDxXWrR05yv0\nLVVPqCnrIBXCKOjErZb4lmGPFQL5VzbOQxspATQXXJioVuba2Krx3gQ0q/s0\n6zmFx940oMGy1+7RpgpX39+YaSJ7uxJLIvQyrqnb1S/Dae+gZypDqWCOF4k3\nJzacYVZBWbTYEEhZB6Cy9PoltKjypLRyBxZPhh5hCYZljA/3ilJfyZv4mAI2\nMyXVXVvucYSvijh+ZWA9kzRSmdviop1OAkQKcpOgzwH392CN3dU9cBUM4KZh\nz+CF\r\n=jDpM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.1", "jest-util": "^25.2.1", "@jest/types": "^25.2.1", "jest-message-util": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.1_1585213295314_0.9532139685059393", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/fake-timers", "version": "25.2.3", "license": "MIT", "_id": "@jest/fake-timers@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "808a8a761be3baac719311f8bde1362bd1861e65", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.3.tgz", "fileCount": 14, "integrity": "sha512-B6Qxm86fl613MV8egfvh1mRTMu23hMNdOUjzPhKl/4Nm5cceHz6nwLn0nP0sJXI/ue1vu71aLbtkgVBCgc2hYA==", "signatures": [{"sig": "MEQCIC28PXFFWxqzkGNStUFqTYzrc18/swq5dy5q3+oizynQAiAM3lZaB9I5gagutbmr7Paw6qXHcZ+TMHmZVAWH8fvoeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+pCRA9TVsSAnZWagAAb2gP/iCWAmTv+zoyxEnYcybZ\nI90oWfyvdnpApA05JO1onILFM4B736NyhR/fWWeWt6spJdwMfKkUae3As6wL\n3NkGOvQNznLl5OGpsE5o0bnHtMFy7zvT/hWzS8dAL5039gfOVNJZg+I7JxCo\nWwTrZDIPVrhmmWAT3XqBANtLke5PtJBItIF4PmrpxS1xs5mwvWuPhxH9OgSU\n08wSEcGGb5JrYrjncjZD45kAZp6V2tsyqno/ZZGBtOWzW+On8z0shZH4WAW6\nzZxqDCWll4h3L0lCT8dFmvKfv6G8OGYnBBk9fwmPXMfmImwY1LnB8WX4v6Ut\nLgH7Rn1YpVNQ9gx+t7zkD1Zi7W4wh1GqDSN9uMkopz59xuY/ltbAp19VM6Aw\nXCJg6S5diIqet/KMUiY4eC8uU2LyivraSzgJzeGUXFzx+Ta/5axkUeR5jHM4\n7Sgf2pjzFmusxmip0qflpKnFGekDJpPmmgh8ZRfEh804jeZx3PU/KZgi11lg\nwloDFY+xGfZcwh6gp/9met+jOM8n2S5pD2Oz5msqY37g1MicRXTljE0cxwL0\nEzr07pbWyZlJP9w2w8JoUsaNF4N7BJ4PkhdiZmDAbK24XUefafWdnuTnH8a4\njGKPER+cmdo5aTT0v36p7vd5jmWHb0HWCbOyXHX8iCBCs4qxZ6vhbTVROTX1\n2v6v\r\n=tPSQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.3", "jest-util": "^25.2.3", "@jest/types": "^25.2.3", "jest-message-util": "^25.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.3_1585254313444_0.654401100325618", "host": "s3://npm-registry-packages"}}, "25.2.4": {"name": "@jest/fake-timers", "version": "25.2.4", "license": "MIT", "_id": "@jest/fake-timers@25.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6821b6edde74fda2a42467ae92cc93095d4c9527", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.4.tgz", "fileCount": 14, "integrity": "sha512-oC1TJiwfMcBttVN7Wz+VZnqEAgYTiEMu0QLOXpypR89nab0uCB31zm/QeBZddhSstn20qe3yqOXygp6OwvKT/Q==", "signatures": [{"sig": "MEUCIQCQw4K7kptx676oq0rucHuZALicwOlO7pqJLOlyqvZHvAIgJ6WRDQkp2BTnEoPLXaOCITKS/UwFHEDuZ3fiJy1VGDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegPlHCRA9TVsSAnZWagAA4rYP/2uB4DAPeYThRt6/gyTf\nVKiRmnHfv61/rap1NLo3nRSa1KfJSOEeYLId1y1NIZIKx9M7HteX0B0ngsC8\n3+YTCHQt+cd0qrji1xfArtpVBdaJ7uwUF2+NoTdPEBGMwdRev9vpk4XhOiQD\nSC9H5ENKN+pDk4pBCjvW8e7nYLynn/CbcXMnuFbTJDOvJRuBvm7qPOJpajgD\nZ6vUPuLTg0it3f6hjZI45E81q9SbxNe5mBjM4dGi3pWe62Nu56GAhFX2GpbB\n2c1PrH+eljmsfeMF8Jl34k5KOoe+R/R5PdaslYnrisVEmuB0mvDWhwPDE9PA\n8/aaLWnGll3EKOREnhmy5RDL2GrtvnlveVAZuh2RsE8opSCy2FWe6+hsLhlS\ncCiJZcixHPvth0OSAcHBpV8TiORbHsy9c+uIPOU0FUEk0VwhbtT42wydWaln\nTnyo0+8kTlA55omkMmZvZZ4E31a0K4zcSxNLbTX+ZW6bOwa/PN5K7Ul3Oc8O\nXF1I+OxUpzkkzR+DP+mD8wqHFkAm8armvBA9L0lgJgmaQCz+ormk17H3HVoB\nvl03YvF4tWtcNZTOWBDvdmugEsYtp4xnBbiOawH7KbHkMUtvH2Kx77fiLaO+\n3QB8R2QMq4+0CV/dYfrrXuWJTbfzr/hYfXQo00k1O2Vb8BOsuxMQMDtME6F1\n9MfC\r\n=K/pq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "324938561c608e0e9dddc008e5dde1589d7abc68", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.3", "jest-util": "^25.2.3", "@jest/types": "^25.2.3", "jest-message-util": "^25.2.4"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.4_1585510726749_0.630777019301122", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/fake-timers", "version": "25.2.6", "license": "MIT", "_id": "@jest/fake-timers@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "239dbde3f56badf7d05bcf888f5d669296077cad", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.2.6.tgz", "fileCount": 14, "integrity": "sha512-A6qtDIA2zg/hVgUJJYzQSHFBIp25vHdSxW/s4XmTJAYxER6eL0NQdQhe4+232uUSviKitubHGXXirt5M7blPiA==", "signatures": [{"sig": "MEUCIGtwgpU9Tfq+rjm1XXNX0RWvPBNg0cNUoiHPlW0cH7TBAiEA7e3BcLPD1MQiMzA4++g9jyoRIQcS55H93j6I5/ZWIu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6DCRA9TVsSAnZWagAAyRsP/iOS6JXKZJonjEWlkxFk\n9DUyV5MWa+J6LheGz1AcACG6+i24zNa7we+B2wbE8XeXovifJOnD0BfCjKo4\nwPvpvwxNHgyaHW13IcrQoKCWEu6Wr61mgm+pZb4PBi5NbZMngqgnNxwi+rEp\n1ORahuJlAtTwZNEblwvuj7LxN91+tZLH9yhEnyo0jenl8ztl4X0/6scxT3Ss\nxlovjnnjMwLRl+gkGGv7mllL49HNvnD1CLD6fLiSC+KcxB3NhJNCa2ut7zuY\nvisz0Q5ONs8pn4as9LnePGZhIYSMTmOumwNpSa9kLLQZtMECpFz8AkEtBE+L\ngYd6H9blL3m6KHYWhd8QT9xEuBTBFhc+qM+xLxorOLogTrJPPd/7LxemXL/N\ndr2pXgOF6x96VFzSdsmLI6rl9AejuKE0WqC4iKeIEMkug1CmHsAnEplVJUL9\nnIAGhbyV4WD+lYo/ljKCRcE8HigagfeOvlI4ltHIkifdNMRpJd0CQsTfZ482\nW1PfheeEK8JgZWAkZPMaR4uYZiCZwSUeduBzd5+YudbtebCbsaPZogfOqhp2\nrdVaYFLr5adMHYBvqdmOMJh+ed0Ndb1xNVTDzFcu0pKzL0NzNgsUx+Ac6FWP\nBjk8c1aeVGbE9G6kr61/e2JkviHa7GfsNBWxjOMc3W5apnOHE2V0uR7XgbJL\nzwIz\r\n=Jhp8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.2.6", "jest-util": "^25.2.6", "@jest/types": "^25.2.6", "jest-message-util": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.2.6_1585823362714_0.9716850573866205", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/fake-timers", "version": "25.3.0", "license": "MIT", "_id": "@jest/fake-timers@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "995aad36d5c8984165ca5db12e740ab8dbf7042a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.3.0.tgz", "fileCount": 14, "integrity": "sha512-NHAj7WbsyR3qBJPpBwSwqaq2WluIvUQsyzpJTN7XDVk7VnlC/y1BAnaYZL3vbPIP8Nhm0Ae5DJe0KExr/SdMJQ==", "signatures": [{"sig": "MEUCIGkzK1mDtA9FGNSNwG44pvF1XI6e5PnzSQDrDEllD64zAiEAhDwi21f9RDzaIlqEvYiBuaMfXPPR/eEbYwEY2d3jPCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/UCRA9TVsSAnZWagAAudwP+gKM3gGr7IEV2YeZ3lU8\nNLpWrfl0bwyE02JLmpna/XZkrqdlCFVxB68bxWLqUPHpvd61GAuELBw9dlZW\n54PeZs+T4KT6QK69k7bDW8dDhzHITntGqSQpjT6l5Cm3/jF/k6tLoNA1ImXL\nlKXA72FUjwy89oVb3wN3BZ4ObTzh/rEi/eZD4aLUdsapLmSqVTXKwMWXUtkA\nWULvLX28pKWJSEYhtMSDVO5hcFXlko4OZfxOnFcNGnFgyTpuu/uVyVuC0SgD\nAuoxzls4E0hoW8wVHaD08sykG+Sq8jwI1OOQd+DuB1X86KLgsQa6ufVbd13C\ntby9Z2EAB5rocUay5ub1Ti2lwi4xcVZ3TGdicC+86fasOgljCmC0sOCnovGt\naa1CCHxO0kx63sEx0Fl4p1BCGSJvYcy57IGJyKQGz/bN/N7dTZrwHfN2kERJ\nWFG+3wP6RioQlRassMWCV2xIFmRwpwBYlvRiauur46zbUVqrgyfYBxicI1pU\nFSJ3X6777M+MK/hmXHyL347rmRkdMDr/ubUE1inKtiHOUZ4dBK7He1aqdsJQ\n1KKPLiqPBiCRmH5v3i5bwDwL3G5vJGYtIYB9TLpYzn/rf3z1W8MrW5tffy6h\naIAwdA6FWQfK9ikj+/klgukp2E3WjMpRv+KgM1kRlFAQDmKV7xH5Btf3aWXV\nEn70\r\n=u+VI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.3.0", "jest-util": "^25.3.0", "@jest/types": "^25.3.0", "jest-message-util": "^25.3.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.3.0_1586352077120_0.9817319839761347", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/fake-timers", "version": "25.4.0", "license": "MIT", "_id": "@jest/fake-timers@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a9a4289ba836abd084953dca406389a57e00fbd", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.4.0.tgz", "fileCount": 11, "integrity": "sha512-lI9z+VOmVX4dPPFzyj0vm+UtaB8dCJJ852lcDnY0uCPRvZAaVGnMwBBc1wxtf+h7Vz6KszoOvKAt4QijDnHDkg==", "signatures": [{"sig": "MEYCIQCsvUoLEjPaql/TDWlgg92HFsPNfCiRsSTuEW2JB6bTzQIhAPhcI3kSCKgM+2AVr/ti/QP3bUT3vimOXY2DlxHVVR4D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMeoCRA9TVsSAnZWagAARlQP/1bkWeCT8e1DhGWs/ni+\nqFrtxGqI3G2KXov2mZ6zNK4j0nuIjqp62WndZ5+wzCM8JLu7dSBFZYhRH3Rp\nKlaBMIo+19Q+u5TmiMAQDO4J3LNsX8uPnnbM4c3ALWEQryQgbE2ZorrVH+sq\n1qWqmB36iKSGnrOskNNu4MleUfz4D4QjvSYirKq7Eo0BkI5P1OgPMyE3NUNs\nYy8KhwwnWGy184yJTzZjNaRiNv6E5g4lBi9BYwtCl0EIUY3p/0arDqA2Pbxh\nPiwrDqvUqWSrVe4A8ghMr+r7woobITDJBuOsn0NQc26hixtJS7clvqDlpwEy\nv4w8HpZKwW/3IMb/NjPb6x6DlgUIOAjizz0SqKloVOr9r1TIfHK9PY7DfRfG\nPTiG7RmAIeOZvsF/X3PUetf5DCbLHzq/Z10plQGknat1C9d1ChqpFGXvnbDG\npFXgC9SCOhVdnzSu1MtdYFjbAaCIksXPgXQ6n50pEXcR8VOm24zc0ZvUlECB\n4HT9pFUZfCF1C2RW/9GXgdO/QOmaPGC2sQjF/JK2XqIkanZjN1hrYJa6MP5H\n/MT0FuPi2GaPwwgBLQCMJo5D1XlViFpxu38Ym1GZ06IxwtYjKSnUJw508S2E\n7dQ694gUiJXqU+QbbCkxhAxchLLZlOr//fUu7ao4OWgTKzbjrjPBwzPw1qpc\nDN9N\r\n=FoFq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.4.0", "jest-util": "^25.4.0", "@jest/types": "^25.4.0", "jest-message-util": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.4.0_1587333032208_0.022896837614224674", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/fake-timers", "version": "25.5.0", "license": "MIT", "_id": "@jest/fake-timers@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "46352e00533c024c90c2bc2ad9f2959f7f114185", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-25.5.0.tgz", "fileCount": 11, "integrity": "sha512-9y2+uGnESw/oyOI3eww9yaxdZyHq7XvprfP/eeoCsjqKYts2yRlsHS/SgjPDV8FyMfn2nbMy8YzUk6nyvdLOpQ==", "signatures": [{"sig": "MEYCIQD/LHHAt2CB4caFeY6fCsjv6PIH54Gb+x7CzUl7dxWi+gIhALIo7kJ1FBeLYAHUGRwkgKXNIb2WykJ29/hJ3D6sX0Vh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfXCRA9TVsSAnZWagAA50IP/jXVOwZPoCzYARf6YPmD\nAGVS9+hB0nTvk2/vkrlLmHHew0iD7dlSddH5lg4KRf9PxUPdkJ4X/SIFj76o\nEZroxLS6oujJ/sb5mRIhnQ6x/3FZj5K4HX267inthw8ox/ga/aP97pUnHHWI\nHs4+goRMgnvC/CuazhSqIuIlzf5kkMFhEABkK+DHsC0hZdfZuBhunJKMbDk0\njr6yx68dru4ZCzS1Z79MRUyVew+qnMs/o/t/MwpRMBWVTjXUapjDpX08a7yE\nlm7iz1+29gf0v5R5T1a5T4fYWwKSoam+x0CKqoKpy7w7K9JLZtkqw2VQFr01\nXZrAqfEBJbOnvO3StGwN9I8PGnfzHWIV5LzlECUq9rfNtLMz5s/Xz99Vnqqm\nid18C7K/E/NKVpq8DajpP//APv4WZdimOMFPnj2PHkwKUmIo9/A0up04nrc7\n10kNnIT62bLl37T5S4czIaGPg49y9Hrs4oLnwuLCrkE2e0V0BJYvNb35yDNS\nkJA5A7E7eO1KrsjMLBzJgM2lkKoJqsPV2J/N0GK8jQ4Kl/OKxlaYf96o80pr\ng0oeP1gixvk0+XERA3NY4Oz+sDe3C2WWmfuSyUzyTowhUFr+KC2QRxxnogJK\nEBV8pmD/DmCY30qom1xOqQfs4y/FWljGTlExxR3KpR2phMAaYEyCwwlfUJ6r\nZsSw\r\n=7qTk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^25.5.0", "jest-util": "^25.5.0", "@jest/types": "^25.5.0", "jest-message-util": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_25.5.0_1588103127339_0.4705717849397393", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/fake-timers", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/fake-timers@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "542d3727cc1df55cac45dbcc5950a5a61c351abc", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-q+PEKmnHvSnkduGOx9nMQYrw2SlKlkMwDmsKl5pd2sMiyyH3UwuB+WatlhJMzw+V2q7F/tDaVEuuEH4ib8eLag==", "signatures": [{"sig": "MEQCIDUFK69Paa8pNSExI/4mZuPdsN1dmNZMRd2LoFYfhKY0AiAKUXOeYIW92JtyrxuVCaxYUqkyaZFhwBJ2W7hnY318Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPWCRA9TVsSAnZWagAA1BkP/A4uAPgS+/oLBzYdRCqb\n2RYwQpwvSbqG+wJRKypGP9AuJbucoESyWP5xj6ibgLh0Qs/5cqDPPji6M+mo\nwKKjZUHPRTBsH4VpBxPOnGoARWZITIocUAV2LvtBbZs85GgV5qZa7zuTy3GU\naCa5QQDL4tOE9aFtQ7L60Q+NlfI5zA3Fou5MyXV/ZiUdktk0l+83520ictIC\nX9ktXu5wuRKMVzZ6//S4Nz1QMeKQSVViCqCTiw6MvdcOupkF4pTrN1kcR8By\n4VwiXa/L3EGy9mVn+IPzLHk5UVf9s6JRRB3XKghrJwF7p1p/Fsa3Sk2jTZf9\nHMZznL6Nm0gBnEsPC+lmaHnAvgxcoK9UJ1XqknHE833WVHBI9Bkvo9+tSa+6\nETG439vjTcjFM41OJnN5ErR4hXDCifG2GkhtzoCQuKwihQfAHn72sHQBQbgt\n6sfFxighIv/AHumGYbH00ythbS8LvddbgxpsOr/loz0h+bLjODbOIve5dByB\nRb9gPrD6BXB4foRAhwq2SO6kQDgmeVJx2bnpcebqHEp4MYTfi8dzboJteIXr\nm3ghGKT10n3bE4cbyBtU5NGStFct6HpEzKot5NZiAyuBFU/130JGxVu9bheF\n/Zd0FjbkCenfC4n4wtCjlEOzWVfzmEpFAiUSc8sp8I4ESsYWkYjnpUrRpoEM\nxcHM\r\n=gaEo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"lolex": "^5.0.0", "jest-mock": "^26.0.0-alpha.0", "jest-util": "^26.0.0-alpha.0", "@jest/types": "^26.0.0-alpha.0", "jest-message-util": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/lolex": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.0.0-alpha.0_1588421589549_0.16555499014793518", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/fake-timers", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/fake-timers@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a3353099235d2df47d4b0203b35c84b094cc3535", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-tiodBC17nIY/ursLlI0Kj2vVB+vslLUK+8CeB0QD8wwHwcNwFliNcbHRrlcHZCgmaDBHhjeP7M0KB6v5fLsJQQ==", "signatures": [{"sig": "MEYCIQDe+w564/o2SUudJKooO/BcI1CDKMoIbrJ67mauJ2hylAIhAMPmIMd6myfZBbI9m7SL41mxhi0yOxWZODACLf2dByhM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHlCRA9TVsSAnZWagAA0gAP/1hadYqLARnBEsEn0qBi\nUiNvyb+rcN5fEmCrz2x/KHfHugkIcY9XdmqcTu1lt15tw4FyGSik+oYIP5rk\nCt6GTfLrPVha4W31NWkNoQ6eN5q3JTM37cQy9+hW1KgpwGEiHvOu6FuvRrEw\nUXJpznUYF8O71YCejeqlBDfjpTNzFJNB0isnpeJ9Cn0V3k/k9UQxmxdzLOTE\nNBapry3KxOLZpLufeQ6/JdKU3HUz6y+ny/lINGI5hg/PrZyVaZUcoMmeUuQF\nfFXVBF8K8rIWGrWrNijn3POyXgo+0Mw+GqQ3Q1t7hefIQEdze3yqAouZSlMK\nY3FjWcfZnrudePEANuvrI2xU+jw26pllTWxEzOUjiaqURBgnrssaaWp7nX4Y\ngfBNCtTyT93uf1mxBViQMizFIdgj54L9WLHH9R6vSPzWB6wocahm40k4nYOS\n9Bf9JipKkiCJYVdEW61IraSLDmOUOTQr7VUbvaTalM9QUNOJsIR+n/g8CHr/\nY86Q0HWzVFJAX54GQAG+MVZM/ANS5N0dN/hmCmCb6VVCkTa1Zf03CGUMIsNs\nDGN1D8l+eJph+gYmHFsNRg+ctahRi4lYmAiRGLtR+kT5auo/olZC+FIO5hYF\nBtRplDLC4MxByh0a2prHgZJ5OjENOzp2RAgaMXrG6AIlsOFOkOcScKSuRtVm\nI2LB\r\n=CvEV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0-alpha.1", "jest-util": "^26.0.0-alpha.1", "@jest/types": "^26.0.0-alpha.1", "jest-message-util": "^26.0.0-alpha.1", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.0.0-alpha.1_1588531685348_0.9851563764073652", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/fake-timers", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/fake-timers@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41bbe65490f621e706fe37edf5db23cca34bdfc0", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-9iGSOetvL/9+TKPhFPSuj5no+qJw+roBpwzK5TJzl84Apm+/2P4ZWXujyReik7XjNlxRAarQONPFC4O+0oE/Jw==", "signatures": [{"sig": "MEQCIHVqx3IDKGojUHVOjoRQma0PSxKxFApkwfjEK6dTJ7lGAiAO+TZURztDfouHI+3Z5q/U/WHWRemkKYCaa4BWj4B/uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1OCRA9TVsSAnZWagAARY0QAIZ2U5lr7uPrze7ZJFvJ\ngZE6unF/kUXSy2hfz0v7FZ3/JjpOW8l6NVwZJIaow9mGjTKdpRaMbwJmRJNc\nE+EQObjjAb/xcak6Qrc8SO3hbHhjxYCerAqe7VO5xOHxxTZstDmT3+uuanw4\nkkhvzzLp2G/my1KJQVGsXUktGFj4iL7t32E1WifSvjpaBzbZYVtvoGh5ZMe/\nPJ59erAYQaDu5/XcAkYFtyThXfwiGLYry+D80OPYTIsngDoCkVjUZjzNHi25\nEM1TWzqrKyiBrn89aj53K/G/3pIdA85YLVD5KaqK8/FLGlKnp6kPwlsVbwAU\npPGxJIO6HPcq1Bg/IGM0JMe0dDdxw0JXjbKbzJyf60ubLzqPqVpfdObDK1fL\nyvyA4kyP3S6ieiu4qSw4/q4BdWgYQ/J62B4lFku0D4K7MJtPgOjk5zrKHotC\nncw62EBrMX02tBDVyMqcSkoBSnNSSOIuatLFPqAfgeDR9Z2laQIcz+9Dqdrm\noQ6F1YDuJxSih0vV8XPwPx9078xyU6KXlYwsXf7sLRLlTxnz+BBj1sRpeS3v\npz31Ekj3CgKk7tyFhomJxBgQ7yZoLu8wERcngdAE2t1ITVDXJvM7DzRGRiQb\nVi1DJPNTP5xOxLN4gQHrXR2m2ip2PtobrPeZEvWVvsJ6N3R5uyibGQAor6Ik\njOje\r\n=4mHr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0-alpha.2", "jest-util": "^26.0.0-alpha.2", "@jest/types": "^26.0.0-alpha.2", "jest-message-util": "^26.0.0-alpha.2", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.0.0-alpha.2_1588608333886_0.5906717249490083", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/fake-timers", "version": "26.0.0", "license": "MIT", "_id": "@jest/fake-timers@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "24a1e2d2f0c1b70c3f77cc3f21de812a5be73e53", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.0.0.tgz", "fileCount": 8, "integrity": "sha512-JzdgTHv5Prer+Xp15yHzowVgseigLyQZ7/krqQX2bZ4Mu/RwgteHYlHbl/SeHW6gSEGmAXm7rt3R3MDSnxcGgQ==", "signatures": [{"sig": "MEUCIQCEnAJVhqCS3jHPY/1eohoMiaxuxgvoMrkzfhPPggGf9wIgIa4avoX/X0jhH1LcHhF900Gq66X9lTwid1quLz2KFF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaICRA9TVsSAnZWagAAreYQAJMPkAP3GbihnkaZa9ek\nEQeTg15BIl6839yZUgAYmE7+ALJ2Ln2AlHDk0NHDjvwyqC6maiiCHRSBNJAq\ngUNxtasbBngxCw4c6xoFkI3//5HvvYDMzY9osIwsQN0FxkmI06KYWwc6hXMv\nw7TU03yk4O9XUhQ8/ycA04AKAwiw2udobnh5t2iHt0EASnvogV+xQtgaq/hE\nm2aWGfSBEM9UsPMIlT0dujMOH3LL1lx3Q44Z8Katx70qw5rDza3nJiNfW72w\nZAW+V04YjQX5TyuXMfvRMIrjE3U9+3cwsI3y/Lf5qIP73JAgkssJHJLM00kF\nYlpkWa/lrE/YC2PCrKPm9F8ljmzsdGRkVRE01hg+w7PMjZwrJsxagpa5BWYR\n/CT60f00L9SPwxBhaH/Aa3JO+VA4pqiZBUFHy7boaRzO6cgIzFkbQ0+XVflp\nCR8Dm6kr+Sk4l3stV2LjzYN83VedOwNnk4g3QxFQhoCszcJ3jHLV/wHEjaa3\nqG/UgwRCxZRGaj77PosQxlxs4Uafm6dWNK/HUHtvWnqw6sbj2/ikCl8nAaVk\nkFf6nk/2L/FessPnGHb2WqfvTFqUQ4DqIQ2+SwPWCi7HqPZlSTMAhMRTdmdr\nC3rsNqQ1S0SL/6byQOC+IJtA3o+QX9UirGv5jJLIFpFFxhz3I/HJy1I4gW8f\nKT1F\r\n=L9p2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0", "jest-util": "^26.0.0", "@jest/types": "^26.0.0", "jest-message-util": "^26.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.0.0_1588614792013_0.2199808903339333", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/fake-timers", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/fake-timers@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a3a2cb8f7565be023af3f2ba3dc76c317bb1b5d", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.0.1-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-FUmoVpHE+pMrZjbUFISL8ZvkKcyXd0InawVXODRUdVwqo0LKvT05f1IXeq18iHWhyPRmhHlU+f0eRalHFvfmmA==", "signatures": [{"sig": "MEUCIQC1JuO1rWe5DSUdzvhkDW0evhE0h6SalBsgGWNRhelFsgIgNYAffDiKVNn1r9iO6/6XZFr0ohCney6zCphfhx3brc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQkCRA9TVsSAnZWagAApVQQAIZFNTllVbr+VkWF/In2\nDMrhO/emF/MblGGQxmKA6JZ+HYMj4jR6IAzMSZjY34eT4tSKpRMvtx/0vd2G\nh97H2+iAtC0X075Cc+g08Hmnw0JC0ce2ddN/ZrdEp5LLOc2v4b/eI9ZSmlq7\np93z5s3GcNNVdpSOxFWj2BbFG/jNFqUMBC5EkxXg7h9zz7QhubLubzHh0iKz\ndtOOZe1W4O6WlVRsPax3WfxHO5K4wsa9ZqFa599bqORrFyMRArE6yGCfPGEq\nzoYS03hJ9I7mz/22j9xxu0QfBRkzKmA0yDQdu3Fvb21XFcFrc9/tfpdiJKv/\n296cSBqRFdOttynh7TnmUpsQk8iZXQjrv/1S/fz/lVOZOANf5a3zHvlpMXDj\nr4RRtQEw8yEwz8/puwTbj+eSDuNHRMj5TStckhOHEsjJOgEUBZB9nikP+FDT\n3CjCH3siOcMq35JBHzMNoR21r60TFSctau584leCrlOOxUezUuPZA1n90eVN\nUO3vrqFb3Y/kJO+OI4+WcjoM2EM3htkcmrdjWVJTMcKx0brZOwZnp6+7a/5w\n21mZSqrwOeSoGs5T1PpCNh089+USN4UGZx1QE3sVw5Th+RS8pYWqqBSlvNt6\n0b7Rbnn3ieHbflmJUdAT01bfss287U0Q4er2rCL21gN+uxFm93qUtBtA+x05\nmARr\r\n=jXCz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.1-alpha.0", "jest-util": "^26.0.1-alpha.0", "@jest/types": "^26.0.1-alpha.0", "jest-message-util": "^26.0.1-alpha.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.0.1-alpha.0_1588630564058_0.9379868033179823", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/fake-timers", "version": "26.0.1", "license": "MIT", "_id": "@jest/fake-timers@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7aeff13b9f387e9d0cac9a8de3bba538d19d796", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.0.1.tgz", "fileCount": 8, "integrity": "sha512-Oj/kCBnTKhm7CR+OJSjZty6N1bRDr9pgiYQr4wY221azLz5PHi08x/U+9+QpceAYOWheauLP8MhtSVFrqXQfhg==", "signatures": [{"sig": "MEUCICcs1GJofopiZuNKlF/EFlJC5WGFaGydzFlhcgOKxGwsAiEA0jJMVE8P68ytP36b2c44DBCAbgVyQ4cJVWxup4xZ3zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUK1CRA9TVsSAnZWagAA1J0P/2CtCdt3fnu+jVjOFe2y\nMPWrCRkNgLJGK4GT+GDdk1ECWr97d44drDxmg0ZPwvDqt9+pvvENRBZM3a1G\nq3Blru6vLAsmeWQcnXBWDO35zMaCzdBYQj0hVNG/AyyOxjn2rBFVOugrcwB1\nA3Q8Z9vgbVh9lI/Zbh1IrcC1fmZf+DDlda3JovNl5Ax1bIvbH52WgotZEcIT\nndUn2kvRcCe7PeEMBXDHnm0BQuJ/himAn26PSKwCoCjKD3SGXOjB+nUXZt7G\nW5TWPNttJdfrD2hCy+Y5wRNvRWC5TgGJ9ZsGKeWAuqZrbdHrn2lSUEooXka9\np5d94NaYZ4QcFQKjoH7IpjaV9g/YJxqCJgk0xaWbhFo1Powr1vOUTXc9c/j2\n+OdQWoUJ7tDityP68me2NWvzmJqFIr4RWPWCKvLGlwUQ+nhw0KH/ELa9bjty\nJAD2aZ8N0hvzmqfm4BNM3q+FTv1qg/GpWpd5uCmBIyOcxKVIvNgUQcYHXUaO\nQv104IA5Q7H0e530hLPu4fz9mpcJbLyuZiZkSgg3lelgEDveu69VGK3xEdAl\n6QaAAIcfrWYLXI1MSUJyiCWw0bR0ioiXC5eai5x8JvjHjzAx9tCbT1YWlG4t\nmh3hkcpwbDt9FZn2n9t48iGQ2MDMtLyjNp1aTeMYxrbzfvtAAKLK/91PeNAu\n4CBA\r\n=gbhn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.1", "jest-util": "^26.0.1", "@jest/types": "^26.0.1", "jest-message-util": "^26.0.1", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.0.1_1588675253177_0.8469608339979293", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/fake-timers", "version": "26.1.0", "license": "MIT", "_id": "@jest/fake-timers@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a76b7a94c351cdbc0ad53e5a748789f819a65fe", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.1.0.tgz", "fileCount": 8, "integrity": "sha512-Y5F3kBVWxhau3TJ825iuWy++BAuQzK/xEa+wD9vDH3RytW9f2DbMVodfUQC54rZDX3POqdxCgcKdgcOL0rYUpA==", "signatures": [{"sig": "MEQCIEOQCBtlMld0RxGnKbdsaGIG6qLCu+fL+gxnrqMSrWioAiB8Ca/qOF/JqfwBHIqrofOHFbfc3jGJvPLv/gCiQzdj+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyFCRA9TVsSAnZWagAApj0P/2XzTzFzoiFVIefhCTvX\nlmszkmq238PBGYYSh5yenIaxneqhpAJFWZKOvXul5S3R4+jBB2RsIfybY3u4\n3srVQn1J+h3OvsfITIaoLaF2I2ol0xfNu5FWWZpSgLBxtG3HoBGurYOJuZ6g\nPKehSSoVDZhQfHfFcIno6/GVpTlEBTXLqbHfo1owvNLmYEpesx1tVgjGdlNB\nrcRFqmMDrpINXe0Ff65kjuzU93z6fWQEQ+HQM5IuwkUHu9tAmHLMXz7VEpR8\ninR4uCrhPyzh7aVaCGqQHvWgD2mIChhGtayKzAvf0cSWG8edj58FrHrrCnxu\n6k0fsbmgWEGTeXNtE1TnVd6xjC3zwe2Tc+jVCi9sza9T+33tbaBkHLGbfEJa\nhQleiTOT00BH+tuFHUO8PQKtntrb+1M+PXOvDMGJ63H0CS5A1lAF6IfxNSJs\nlanHQWrOodrTNVUMEWq2BnH63qPFoCRV9m19T3R7Urx6d71E7HSL0o3J4ale\nY38OdwO6Y9SM8aaMCCgFtniDkfcIk5tcpDTSKKbWM4M2JLU2xzg9mh5tIDKG\nvfD8WeyifmjVMsTqE46vlLUV03KCjVFcbUHj3zvO0e7NEi1vdEUH2FFlUOFF\n2hcNbwik6mT+kMlXNrYE11jEBcMvE+7EKHgbM+BdnHegX77VdroI7EnENif+\nOZq4\r\n=Rxva\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.1.0", "jest-util": "^26.1.0", "@jest/types": "^26.1.0", "jest-message-util": "^26.1.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.1.0_1592925317057_0.14811182429249992", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/fake-timers", "version": "26.2.0", "license": "MIT", "_id": "@jest/fake-timers@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b485c57dc4c74d61406a339807a9af4bac74b75a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.2.0.tgz", "fileCount": 8, "integrity": "sha512-45Gfe7YzYTKqTayBrEdAF0qYyAsNRBzfkV0IyVUm3cx7AsCWlnjilBM4T40w7IXT5VspOgMPikQlV0M6gHwy/g==", "signatures": [{"sig": "MEQCID3gZecMs6IzVKm8C05rnDv2n0FGFW7ZC3/jqbV0wWJ9AiAPpIqtTHUvA+u97A0aUmUsapfjkdGD5ZPcaY0GqwCFgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzmCRA9TVsSAnZWagAAT78QAIOkVMVfS1aqVM5uV3b/\n05RlUmUx+zUTmLkdeYIO4opmB5ltjasDmCYbgYTH3fmQ1QE8XsVIOpuIQUmh\nfzOQtK4kXBzpE1fYQZ/oTmG7ZVDgJVE4EOzbJxwrvl9Jyjns+MHypuaTki0I\nK/YhbnzGf4LQ43+wIKSd6PiTcBx92UMrtPU2CjZ5YvqTwzdM5K5YXH8Mix5Y\nD/AVDzBiJHsS2WnB9H96uTssCJzZHTNU7f/ul7/WlKJOf0QSB/fNTMYaTJDv\ntKda1HQ/PZWNpIbgo3p++Xf6RBlVgyJH1FUpjI9ifXiv52u4vaHQ3py2LGNC\nX5JLP0ikMl8t3MQwiXTA/RHogPq/1lq555fhhoY+9KgOwh0O5qE7kFVstMPf\nDkNlhMxuONo5T0Igemjc0L+yeFKCaiBj6tFKJ9MGW9kMLc1PYLywCq9pIki2\nc1k+W0Ifx/mfYzqrB8uue/bf6O6igCGTrosSqkGcAH2nVVZjb/iWy/NBzj2M\nOsEZDrknJFOW8ZJvW+ArEQy/z1U8hiZt6IDIsNrhaZgWR3hnOyiRcbHQI3t8\nESwwErbT6mnS1EQGMXGW1CWJ22/oNTqZl5z2Ix6kFKOh5qPRl62ouF92pJm/\n4sf7PI9GRT5H7jOlPJJJSwsZyfvCi9pAfL5PDe7KaHJ6t4MxnEAo38pMNbOB\nwpoc\r\n=F+ae\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.2.0", "jest-util": "^26.2.0", "@jest/types": "^26.2.0", "@types/node": "*", "jest-message-util": "^26.2.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.2.0_1596103910187_0.17453168333062097", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/fake-timers", "version": "26.3.0", "license": "MIT", "_id": "@jest/fake-timers@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f515d4667a6770f60ae06ae050f4e001126c666a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.3.0.tgz", "fileCount": 8, "integrity": "sha512-ZL9ytUiRwVP8ujfRepffokBvD2KbxbqMhrXSBhSdAhISCw3gOkuntisiSFv+A6HN0n0fF4cxzICEKZENLmW+1A==", "signatures": [{"sig": "MEUCIQD8Jkv4WfIZoLkV2aoG6KR0nW5Ndg9IOeaZ8kvm+E8eyQIgJW3tq0XZIh7gl6dYaWU/kLnh5m5CjNTTvXqMdTJIXAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTArCRA9TVsSAnZWagAAjP4QAI4lD7c7dX87++WTHt9M\nE2YF9xmKMUEFnw23PKhoLEhPw6KS1l5L7zxaA3Har9QGAdIsxEODhBEZab2i\nXPnv4yww1SloxbltyCz0BOozJJMbCya+C5RAvRv0RFfXVgWyhkiUIIjqtXYe\n1ysXbCy8IKfW1XA6IpwPQv9ZNStj9HPmsBHf9i3QaaL8oVYxGum3zi1hSh1r\nE3XgF+loTiSn1Luk8bzrDEWU5bZwaiIO0JL8O50c64W/RH5oeV71hiV+ZzW8\nDdLKMghL406dU6ALZTq2W3wxoM9r7Bj0VgahzFKi5Xqxgtimg53UzLVkebY3\ndcEFboLB1QJF7W0WqTW2MJZmBHLLGiVGu2eKpLZrr+eMStmxv9lVwQpXeYc6\nmTpnuQ1kbHK5UsDIfCSztgZr8L9hj6O8baSnhJV+QoVGc6fr67PhyNSdRe5u\n20ev8ARfz0o9bcesMZZM9jIO7uftsl2QbYvplK8/9r6sVLMPIcPuCJ46C6Hs\noFPdgGeLyTzzK7Cy7f15lRsJ+2pNqkHnQFKN/P5XAIZCkxk8Cpw9P2lOopbs\n4pXlMzJ2wAUazrBpErTyJ/mT6zPADAazehKIeUg4yQR0qGVDzuGYD6n9sjut\nRUDV78wgt5U8xH+51S0WJP3fZ8XUxCC8+XQhGs6be5tEuEs7sTMLrClfGEcQ\nR3Li\r\n=nU66\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.3.0", "jest-util": "^26.3.0", "@jest/types": "^26.3.0", "@types/node": "*", "jest-message-util": "^26.3.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.3.0_1597059115254_0.7552152488220383", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/fake-timers", "version": "26.5.0", "license": "MIT", "_id": "@jest/fake-timers@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "107ceeb580bc42dd6e0843df5bbc92cb4fe9cb00", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.5.0.tgz", "fileCount": 8, "integrity": "sha512-sQK6xUembaZ0qLnZpSjJJuJiKvyrjCJhaYjbmatFpj5+cM8h2D7YEkeEBC26BMzvF1O3tNM9OL7roqyBmom0KA==", "signatures": [{"sig": "MEUCIAK/NTM+6ZVObX5uFcKtIaDwvqi5T8UwEtaAvUkriy+iAiEAmWTUC4pgV2e2UTObERaQTIcTxrccAWyHeC3NtP9TDdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeuc1CRA9TVsSAnZWagAA24IP/j56np2vgr/acXiaA9p8\n101GeucyTf+rPyEH4hGKWO61CoxFyQ3YD745B0BJJmj4N4QkUMpGxqF1uHE4\n7eowFZAR+FPfc50+7mWrcY5uRjIl1h5RwWKZL3gJYW4H1Cflm5nCnlMa/mwR\nuDmHK3uNSMQJtekKD9GzJPsDpTgxSwYxGRREBLhE8mPFIAyedocny7Pd+IjF\nVuYMdPiuvot9NyTH8W5/WNlCNzWx+yt2gws1wZqoLSawOOUh89L92I4BcKTF\nZ2YWUGQboQdx+ruHu<PERSON>KZxGbaPcRTzxzuAKPf90jrLek5Qv2YFu2QBLBpEEv\nU1OIMrOl4XWqHCE5M/3PXn5wzX1o5ttf9diT/ylxEtly8YrfxnNxAvSRKEt7\nhyh1NNF2I26F32xazPHRFrP99PWYnyXhDquCdfI4eFpHeZ6Se8qPgs3P8Sr9\nsmVRF2DsLJqrqrQp+gCQlCHDHrVgtpwIL1gSaQTsNI0LSqFGB+mNqdMIsCLs\n70Dw4xs5v8GXz7EpMvPpVtMDlxNmUD9vEVXt1I/pPBrwNoz4WOzZuQkmEAt4\nWZTp8eZ/BdH5BfztWe/ex9nYxZFWZCybkcrLiLNPOD3dTJmFc0kFmaSWtFks\np6lVpY3hVGn9PKs0fECvH1p+BG8/logSIjj66xcuOVTT7phs9qtmUKRYORJF\nii8l\r\n=qqBA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.5.0", "jest-util": "^26.5.0", "@jest/types": "^26.5.0", "@types/node": "*", "jest-message-util": "^26.5.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.5.0_1601890101508_0.7457761518704265", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/fake-timers", "version": "26.5.2", "license": "MIT", "_id": "@jest/fake-timers@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1291ac81680ceb0dc7daa1f92c059307eea6400a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.5.2.tgz", "fileCount": 8, "integrity": "sha512-09Hn5Oraqt36V1akxQeWMVL0fR9c6PnEhpgLaYvREXZJAh2H2Y+QLCsl0g7uMoJeoWJAuz4tozk1prbR1Fc1sw==", "signatures": [{"sig": "MEUCIFaqIf6fxLmVBAtZ5alZCGSah/bqEvjEWKOmNNDqa+dYAiEAqydXdl4FminlSj50dj7b9rqznYxk8/LIZ67GeAfkTYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyCCRA9TVsSAnZWagAAChIP/2/F/bnXw/hIPhfq+s4r\n1xUOEgwJeuiytSWfDDijMzqVdBSh4IMFUDBbg+BmfPL0UMamTT7OQcZAExt1\n7OiJ0lbolf/PBq5KgcVqx28Jw/9GbUzC5t74LjqcLnYV8AOk+MPhAYgAbzDF\n46lbgA4/KYicNmioh75CWyDnKsj8UrOFi30Iaf4EwsjgifknMK4zkQitBe2u\nbu03aW1WGRBMDfiFT52tKywa3L95glVcHOmryfpn51dwmafVKwcGtHhZoDVz\nmFkNvweTp3wbNh11NOrj6oDjEChKvj69X+ApGUyeglv6iIPz3j5DCL4Kwimm\ntZLd3Ooyb2CVm3mx8Ka+SvDe5fD4uZ2xc7I//OXbIB3+vmMJw0TOOrsNxIGd\nn3pXPLlfdLsaoCsguhjkSeZcMrPTaueYqVpYpeWNIDIbq0VoaSgwnG4o34Bi\n9Q9olfMkVyTgY4P98bxPPYqjo3Al2zdFtN30HUFU2D+2LbHOQ7fiNHy5zqFJ\n58cyFIfNgKOw2WQzIri26Fjb77gkj0p97n6nz4gG8AMc2jYLV8g4z8Xkkp+b\ngO7c2XncGZZrC0yOHOaQ1AuHr/7rjfJKEkPAjqBK2n0XQ+PV3gb8JtfJF41E\ntDBfVXtPL87rlXrSdLHTIM6tJAeFjZuluCtXdqW2vsl0d/xL/vezFpu7Jjtx\n/b3L\r\n=PQKy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.5.2", "jest-util": "^26.5.2", "@jest/types": "^26.5.2", "@types/node": "*", "jest-message-util": "^26.5.2", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.5.2_1601981569613_0.6015333734830108", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/fake-timers", "version": "26.6.0", "license": "MIT", "_id": "@jest/fake-timers@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b4cc83fab91029963c53e6e2716f02544323b22", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.6.0.tgz", "fileCount": 8, "integrity": "sha512-7VQpjChrwlwvGNysS10lDBLOVLxMvMtpx0Xo6aIotzNVyojYk0NN0CR8R4T6h/eu7Zva/LB3P71jqwGdtADoag==", "signatures": [{"sig": "MEUCIADCFhAgXE16Oecz8ksgAg3d+PAB6Gn6BEyZxXeTn1wnAiEAnKjYFrx+dZRE1pQncs4a8n6UVJ8WVibQiBBaHsomtzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX9yCRA9TVsSAnZWagAAQJYQAKMlQlfbFq1o1nvDVDwW\n2aHDPAmkQPCjv2BPtziDtNDuxj0W3OfVFz3uxWWSwnCkfHmZoQOe3Zxu0dvs\n689YO3e6b9qpzrhMm0ymwT7auBwDR8iLGQuBuO8rxjG3KEzA9Faw3sSxL0Fa\nXSW4+Q0WIxZZV/wbCIYnLkpVIhAkKfEiXLtw3nsp9Lxtjw7FrrdblcAX2oE5\nBVCZVa+euSb0Oqu1kwYXt7OzeIhQnmQOez1G2h67Otf0dY4C0gSkALXBNySe\nYJiYaPeVGI4SLaoy3Fh7Bh/+k6PmxQKye5y6peObbJWXn8aUscjUbH3FBbtN\nl/3kg0s2dkBJzvOOMxX4jvPuPCn6uHKCTJuBN4d04PNDIQALKGFa7X1Zmpir\n8V+Y7gMOx3X1lhK9x6gPkk/aO7C6di4PUW7dbNcZVxlCUkrJsMyKDCR+H5Eu\n+rIw3guxTkTzn+oWY5ryht7w4x3WBJ91EpTse60UzGdi7fm15v2Jz0+GX3tC\nQn+S++ksiF/in4cTA/KQjGBo79BpwqRh8bW4RW9NNOQUY/jVOtb/JWsQeAxA\nqGlGv0WLthtm/Kd9oomKg41F1AebgSvJ9EQfxK/H5PUtm8r7gMGB3w3iPxah\nSLECm7GQfBHIl3Sav3afiJE5UYkPWemrw2xbLwNT/a1vcC8iuiHMH5VKWeKS\nJlj0\r\n=SHgM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"jest-mock": "^26.6.0", "jest-util": "^26.6.0", "@jest/types": "^26.6.0", "@types/node": "*", "jest-message-util": "^26.6.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.6.0_1603108722437_0.3305640308625801", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/fake-timers", "version": "26.6.1", "license": "MIT", "_id": "@jest/fake-timers@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5aafba1822075b7142e702b906094bea15f51acf", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.6.1.tgz", "fileCount": 8, "integrity": "sha512-T/SkMLgOquenw/nIisBRD6XAYpFir0kNuclYLkse5BpzeDUukyBr+K31xgAo9M0hgjU9ORlekAYPSzc0DKfmKg==", "signatures": [{"sig": "MEUCIQDXC84/4aIKhmxKYZbF7KwImBMGgiNkR2cnRs9GkHtcngIgNryWvqWzU1sDXzuAaWPMSjuN6eBRgnl+tFOceMZa8S4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0gCRA9TVsSAnZWagAAqdsP/ioeDZQ5DR9AeZC9zpH/\niMf+sI/9Ta/pivFUWajswbUY7SRFT6DI8KggWiKcq8nH8OQWnxVN2IReNLzl\n/QXSzmZU4kFtH3bBaSuyloYU3vTVLxbCsAV0DpNahOSnMy2Z2InfRxGnkW2L\nLPlyE0PZ47zF9PfRGTpzy04SJ8w9UayoE3XeIlZUwP28ID03tWqu+EB4lmon\n8vZYFDQsdgjJgsoCBNQsrtHt7xmVuKO2D2f7b2Woj+eI4Uq1sJ05dW34Ie3+\nETNLOMxPZup68P298xaJ3GugIZMbPKXMUa3JKu/mrHlX7Jao1cFLAQ4K6Z9D\ncKt31Xp14ZUcRlS1cRq+d3Su6jA4aOLNqgro7uSxUa0TwbW/tie7tv3mjalF\nL3/DVqBV6IkMM1EG5SBP8BGIeTmPDr6wWkRejYaRmHIyYhumRQDZfkdWoWUq\nPBN7j2xJfyW5ntmT9C0SdfKaODcdHmBJgFE8c1j5flhaNQm9ecaf0Mt/xegM\nv0j52XsjqkL8EPEnfuyDI5AQTIzunAgHQPzMny1k9gfAPUKcDt0qM+29PG5g\ni/1QVJHfIhfVfhuVBILAdVFIiY91oBk0b/yVEPUEfzWgLbx8oPqlcjHHBnq5\npOyGY7QtSDJRwZyGL4JlMaGSdHb3pn7LShD39AD7ULkh2Ftd61Xhc2nMy6KF\nBh4V\r\n=ZRzK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"jest-mock": "^26.6.1", "jest-util": "^26.6.1", "@jest/types": "^26.6.1", "@types/node": "*", "jest-message-util": "^26.6.1", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.6.1_1603443994611_0.6429413325447897", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/fake-timers", "version": "26.6.2", "license": "MIT", "_id": "@jest/fake-timers@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "459c329bcf70cee4af4d7e3f3e67848123535aad", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.6.2.tgz", "fileCount": 8, "integrity": "sha512-14<PERSON><PERSON>tt7jdzefLPYM3KLcnUl1ZNikaKq34enpb5XG9i81JpppDb5muZvonvKyrl7ftEHkKS5L5/eB/kxJ+bvA==", "signatures": [{"sig": "MEUCIQCncup85OLPKtJFzDO1d/XvwSxNl58GtluDYcPj3rkfCQIgVGH4CaIgLA/K1key7MaDWb8dd7TTcoK8+sEEIGZ9KUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADgCRA9TVsSAnZWagAAhX4P/3VgtG7o4y321MfTTOVq\nifbD3GrLMHvzuPCycYklEcJVoTYzK7QD6L3+arv3dOnY1fSmtaaczSXBy8Xs\nSq+6RckP7lj8kgQCkFu5mklz6h1R8x5iKI2ie8n7uVIEd+yJgybhKQzc6Slq\n1Xf0iR3vZ+AWUzLYaA5mOXYSYqkWuyjy1sojSUFqzmD22oEPge7qzHIm0Nrw\nsHo3wQ3TqqmSdi3j9tR7xCLKohBE96oBUh7aPAKDUboLpsvMD+9oqiUy2GpM\n6nmhzU2zOsXUgwe5SZBe81AnJV0NPn5pWDYSroLfJYKF69TtPWn9Uqq8cN8l\n8Q/gdWcL8ziogtRoyf6nHLlWseSLP8yWZhFo8RW36pj703hf1pmb2LD0Wobz\n803qkG22TcjH7dBOfptISUMvriswH2vWWqGAYMgZ73sPsl+2zmVlGzyOTvvZ\narKuv7342+5+ro+BYI534fqZMLSCK1guDt2n3cMrXNOppmTicwhiJKTf4Ego\nXXZeGl3NQoH74XE5vXV4TQRa/BIVa3dzPm49X5aaafS1TE2SeYQW6dkhKaBE\nsbeuINGsnhLS+DZlqzqQCVgSRSZ4UxE5cS4pIZQrWzJtRTqJ63+nEGOGjNC5\nZY+gDgpv8Q8tSfBgzVd8SccWpdDXibRcc/NNiRJ0cGgL/uostEdK3ck7Hk4u\nshX9\r\n=VTYt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-mock": "^26.6.2", "jest-util": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-message-util": "^26.6.2", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_26.6.2_1604321503775_0.2460291358084008", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/fake-timers", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2d36e1cfb46d3606b8e15442c065c24d3311d6a6", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.0.tgz", "fileCount": 8, "integrity": "sha512-o8h+MfKXGAO1MQsQlPCznRmPBQgmB4kTvYlykF/2KYIjKq3AsE9GL9vf9v9WCywA7VXVIFvaHfqvJ9uprN0ZVQ==", "signatures": [{"sig": "MEUCIEe3MLB5JI/JYDwk0SqEcG7ZmHxTVXmleuvHpGMY6QBYAiEA3mAaE38XxNYRXjNbO1ehFVLJm1D/+O0OAKcIHIXjGy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KMCRA9TVsSAnZWagAAHjgP+QG3fCKSHKvh4BT+gpbf\n9cRXCqC9M7VQHdeAYEZv4dBnbPSAgNB6C+nBP3l+4fFzdF3CA2ZGNpbhio2f\nebJg53DIKBu4LtoYoukN8IHCmWAQ2kGl08XSIpNp6/9fqbLXy1ytr4J5cfVr\nhCl19V1fGgTDAezeF6O0ELkreR8Po0HjjrImn7RswSewOkzMQQ/tzjINgmbL\nKFZdGdHITQSkFr0QfyB+ltqJTsMDOwpFDQ6fzqJ5DTFUqJ+Xtal+kxw5Rww9\nwnnPlgYUQwNHMWf88j1QHion6D1znYuBPDH5Ks0ysjrnokjsGerjs/A8xdGm\n5Fg2bYI6beATvZw0zyfFvnS+h3ia3nwmLvBOOAd5YDPibOFI7s9Yi92wBd09\nSvLHvJ2umNku8M6eOjOAy5beoa6c/USjK92u1trjwEVvBUfQve1FhcHJQJEm\nzXVZ+cy2rIBlR/Cywzcd/VvlQRsgOorhR1ACIs3cZOi7cjrpIbkTTO+oEsvh\nD9VB4n330OeHEr3rDp8jsBtrRCXzSjZ3FChWRWsGFCB9uwVIHztTGbO4T645\nel7J5TuXnAWzpirzc7wgjvzeOUqwJ3BIKH2ssDZrCz/BEScya2uXLLK0WPkL\nQkj241n9TCfc2B3g5SCrw2ONKIB+xryOyt1cIUF7m4pLDek7lI4L3OHgpekm\nGxLD\r\n=Rlji\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-mock": "^27.0.0-next.0", "jest-util": "^27.0.0-next.0", "@jest/types": "^27.0.0-next.0", "@types/node": "*", "jest-message-util": "^27.0.0-next.0", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.0_1607189132356_0.0003869216173519874", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/fake-timers", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8589db419bee8cbc24a3c6efadc1b6534cc811b5", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.1.tgz", "fileCount": 8, "integrity": "sha512-6KnfmquSmzZvhz6rzaHaTyDmC16W69TQYpHE1cJVQNsG7YnSncuUXUJJLK7OWW/9HxVOhjyQBBftZrFVBhQIuQ==", "signatures": [{"sig": "MEUCIBbGmYIkiOcMGpVQy88nN933O9bspQ/DgUh1Dy2qqeLRAiEAy7b2dUe/S4M/2IG7zA98WhNn/yYCE+X3+zH00VsJmgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziN1CRA9TVsSAnZWagAA4agQAIULanR7edcXymqis/bw\nGpGAOevNyZv8VYaN7Ml4NXoG6B7xBlMNfzioN/vXaNaRXCuD1gutC4Wc7f5N\nB3vK+NYoltXKQATVQbBgxL4/mIde9THJqU+fT50iwA3ZYy95TjvnEDqLqmb0\nzz5wwBlC3Tn7LWHYbOd1bofng3VPGcqvPydU1KCRL5vadKFpt3IOlPpGSEJ8\nODoU8b8cfjxWiM0Ijjv4HIZxR/5DtH8dTV4kA5Gzfts6JFNCMQh2I03Oyr6n\nuZ4WEO4ugE4xTfPRq6+eaBa2lO/8cJ9kktW6xLsvXu/FoF3a3EO0fR7k2fcE\nJimL+RHC5hdEihHfN4iI05d/Pgx/d8O2DEUVFqbra+uVDq1049ouCFg1UW3e\nBYbvygCcxktttAL5Z4ykqaf7SxhlzGvXGJwmtlbZyb+/88o7YGiLclf1ZU62\n3Qd8DFtHmwgEPREzQmZqkybCcBbfV4u8Zz5C3m5uQ3GOyP4tvy5Hlo7ROVo7\nWncHxUFbYffOvTvwfKNbD9ycy9mjkWuyspg6EwXov23hEIRHGU7cNrmq3ays\n+43/m/F6LhwNnq9hh49XDz5iyxUbfGzD3J10B3Qjn1quiSNr59lA5ihaeWIe\ninABiUWlbGpu6xW6ouoZasG1bJrMu6K6lyLoe7xok5hcn0ci4U17qGhvnGPh\nsayd\r\n=NHCs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-mock": "^27.0.0-next.1", "jest-util": "^27.0.0-next.1", "@jest/types": "^27.0.0-next.1", "@types/node": "*", "jest-message-util": "^27.0.0-next.1", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.1_1607345013285_0.47780624703328445", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/fake-timers", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "38cbe291886e5d0cef25b54d107de718550e4df0", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.3.tgz", "fileCount": 8, "integrity": "sha512-68Sua0OVBt/zMNMdGU6eUlliWMch+k+jw5bdQu91KwIXMCDfvaRmJqS1X+RLNEOfLJv13cWwXbUhfHekRnYeYg==", "signatures": [{"sig": "MEQCIDLbQu4jyabPatLx2DuA/bCelreFXXsL7uY5lPs1JNhRAiBnZTnMHem8J90QY6R26IWklXbfKA1MakZaqVz7y2OhLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuW9CRA9TVsSAnZWagAAuLoP/122drDweIDl5VFFHPJb\nEpgettVkVP8F2m9BkZe+8mY9rB9pabk69MDhimNaV5UleIHPxBPXXNdpG2y9\nNOdL4j8xtxJV5T3NUd/jd3ms9loyBim6NTXgdnTtnlmzw4KFgC4o21GuuuqS\nwdQ0vqtGtNZVKkn6y92im8XolbvdyfbYSWYMY1Cx5pbScETKrpU6UV/lbhmA\n7Yv3NDmDq05SjXwKWniAP6g8/eAGHqLm/+H2MbmTZuz9DIQ619EwcSuc6/W7\nXFEepMTjDaL9zvC9gpRmcWXNXiiXKql2Bj9gMhfF5YJT+5B519hgBrjbechk\nqc/A97hNltGbyxDmcunCbY4vaRWvlEd2vaL32XFFuQ9teleXW5VMa9GwwfmT\n3HTaYFaheWHPCVrMwqY7D4WcT6MrBuWtp9fPh+0XajEHkBzVkLJWzw6Esh3C\nmGGVxJNhOJpNEFNCii2peoTUsqg7aRcv4JwSTQxggwqNi2stGPslxdeptLsw\neQ2JJC3pN193DbQYnWMtZawvSO6gUuU8BSsXnqqsat6U31MYl5IbXVaqnzti\nCg9VV2BgXFnVhSzrh+Ecr+6bFAIZkQPD3OI0tGQWbHRvXEaMNPG6DslKPwXW\n47odY7/k8y9U/QqdppyemV4qxI2UAVt5ecSnSXxB29FDne8olZW32UkRc60O\nL9sm\r\n=mNIn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "jest-util": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "jest-message-util": "^27.0.0-next.3", "@sinonjs/fake-timers": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.3_1613686204857_0.6920666684494416", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/fake-timers", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5c55ee3a888e19be5370196c76a8c28fc380c7ac", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.5.tgz", "fileCount": 8, "integrity": "sha512-dSOLDNGgx++xGvVL0Mv1oR7SCl+0pvcVAmb7LMo9gkV+j3tvujS5+OaXMieBes8Kpavh2RsPeGTIfHDZgXxVDw==", "signatures": [{"sig": "MEQCIHk2Xgns5LxZKbatm2I9zwX3rUT0B0EoxKhKqMeL/WHyAiBTakbc5XQGb0loBR97OTNEa5078LlOjnQwKjflJwRQAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1siCRA9TVsSAnZWagAAsGIP/ReGjAMk778cNxrGhvSI\nL/yBL6CzIxCAkNnrmd2IAFeIN7siKsWKwzhikg1GF4PN129iV6aLgPre6VdJ\nfQ3+DSc51SSsij7hZzmJ6kIKivDXQuad/O8Hf/tagyEqFZloMh0bWqd8Fs5R\nvhruT4v86de0spYmmjIZostCVsjhIGeGueWRMPd5mTvYySqjap5iSRJlxYln\npNbiJgyD6tbZ8glz7rr2PdsUrkkDkUoJT1hnn0URDt6T/Kzo4CyzJ34L0BGY\nGWRdufV6Es5UN2TSUALfQMG4jn2Cm7++xMzRSL1KCMOqNVChOtxxGns/PJeK\nYD6QP88sS1iEIvXb3/XiZMzcv1e4YiWVaZSiynzG57FyKakp7ZmIwb9wCddl\nO4rpQiMXz2fWrH9+dUcCEjY6V+3oji9DJm8YusWToLYrmtG3QcJ74ejmitvc\nnRHRqjWIiTCEuOtSs5d+nLMCzSTlWyI9ujYz9H2frQcUCsj5sifTeGhjJczC\njS1eQLomTQlVcvBENOXoHzk87Feqh1P+DOu24SOZWeSlMyKu9ri7amqJ/xZQ\nQptmE4IUvVz68ffGIngJGX730qMXRl42U8tWsdrDPeagRO/ll7Giwn+2a9Bk\n4ozSk79S8Z5dTwJcjaeVsH/va8IMLURDWYwqCJFBgmsXhrjTXOID+OZt+EGg\n+HGk\r\n=Kjw7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "jest-util": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "jest-message-util": "^27.0.0-next.5", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.5_1615813410435_0.07315837441028061", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/fake-timers", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2152a0f7fa0b12db87389533dfa84433a7306907", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.6.tgz", "fileCount": 8, "integrity": "sha512-s7X7ZWAaRQfsHkElRlVYQ0NO7YZGfeXuTj+f/60sL8X7z6n1KohF8WtUeBj7hZkyZIns2WZ8F8xQwytDCVcOjw==", "signatures": [{"sig": "MEQCIEMaLA5plDAneNFCq9wAY0wJl68uoWrinF8MnXiTptpzAiA4QcG05keipg1DHEkkQVuGQRUNjgqTsMWYjKGofrHj4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcaCRA9TVsSAnZWagAAi3cP/2XO1+h9IAK4qF9cy3Cs\ngRfEV3ii3OrctJRPaKJ1drFrf3Z3Wuwwqa8tBT5XVqzAACFIhDOUmxOgTxNy\n5vh6ZpgdgtsVhg+/Rt3ZLcPfwVUHWp1Yv6rUiuvckD3P5gmIbSfOM2zmnttn\nq3J50pdJXa49s9UL9Ath8WimsrPoOrfEilMTImM+xSiHMwTAr0jiX7wHLZ+G\nSjBgiuVLDoreXVQhXz2TM05r65oe8WPzAUnZDJEoel1j+PzOQ1TgVaqiFFbv\nGiM6fXOFwP154IhVjmto1P+O6B8/svDzuGsbF4xbIs3fyzKlcDCb/vQy3jah\njRovDtLnCN3HHu7lc1L14lkmUQCqDOOsVK+2mQ5EEEIZOptJMG6aUkGUq7dG\n1eKv2sFxUhVlbSZ5mANNt+a5bWc62Rwv1UsnqfclEKFRhlgynxvdJm/XIRVg\n4uo1WUZK69usuY4+m0PMxUSe/w792JZNHzFO8X+SGJNLNwcbGZgJMjQ0Ht5g\n8UB8ZQBjbk5U730mvX+CyTIwtdZKp57DbaDpVH2MnasAopkxuRJ8Z0jQKN64\nHZkOZII3IhdqJQViOprodb+bHgxuRWMJl+Q4uR8VpzoraajVZVhRpFHHxLBD\nNp0OzKlEp8cjtW96dz+muuyCXFNIBTsGSHemDC/kUZHETV7Vl1y3HlsVSv1G\n2s2F\r\n=bC4Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "jest-util": "^27.0.0-next.6", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "jest-message-util": "^27.0.0-next.6", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.6_1616701209834_0.8442407578692082", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/fake-timers", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd0b9c198343cd098fe65332c655255d3ad186c6", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.7.tgz", "fileCount": 8, "integrity": "sha512-PX+NpN71Z9JmsvJMo6rgWqQWZkQBstGBxlFoZ5hBUt1iCPRqL5mCkwV6NlBPs0BZ8h0xxbFaOsp3d2NCZxba8A==", "signatures": [{"sig": "MEQCIGAr0Y6Ke7bFyNqORbpCxfCs1sKdMtceOw4hCJc7rdX/AiBoUu6zfCu7W2Sqm0ugOpT26ljJb94h7IyHumOtaJD87Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCTCRA9TVsSAnZWagAAMDAP/3uQuOnBN3icmel1blRP\ngSVunikgNcC2emUZP8sR83698zlbHMpIXvK/K0U72hj32+0ohOuu12ltWq9y\njcx/2LaCYBC/DXzC2ydZNPnGYjT3zo1XM0YVbur5Ef7aFvl007Ha5ItnMxN5\nTiMSUUNw225KFFlrr2IQHtGeMJ6asl0CkFcZRTlDgY/whij+dF14F1CmayDU\nnjC+h1dVzmZC52ALdcu4neFZMUhCyC431Ry20YsNlbJ3cloNu4Ot8i7eLjZD\n+ZmM1uoq7mw5rhv7E7AldcgH0H03mgK46/gvYMjtGFRIsCDbfOa6zzqnjWq0\nD7zVl3itIfN8df3TWunSu4jmPH/ZRVa+ZsuehfjxP8ztG4lccpwts+zqbQ+n\nYt6BFiYu68pMgeN3ChB2f4copM0iygsdPFTbfMwx12sFYXeNYxo81UrLJ4DD\ntd8mO6u0U+SWHOwhDRs4p42ZSxiN30xI23Hf47AX9PCWlo62wthMV3uI2eyP\nfhaVa0Fj/aMVfEiiKn6ma4KwrDD2qFvszdsFgYx3cD8IAcR2D+tRshcQiB7h\n5jDMR24Xid0XPQNBTi7ha3cOjI3URMBgMaHGzq907pxc1OZ5keiJHHaMnX8s\nkLyiEeoCill8OIkChAngTDs5mNuVlQY+7WUW3d9dTl6NeE2+yekGpk4oR8bU\n9Xys\r\n=faOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.7", "jest-util": "^27.0.0-next.7", "@jest/types": "^27.0.0-next.7", "@types/node": "*", "jest-message-util": "^27.0.0-next.7", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.7_1617371283546_0.42904911703682425", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/fake-timers", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c432bc24dbc65337e8c672232d6a3ef08ec12ae2", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.8.tgz", "fileCount": 8, "integrity": "sha512-zj7nNEs8afOk/nD5MFXz0iqOmqFYKu++tELi0UPnRhqUxSSc13HMvqiwnpqsX/DZMS6Ctfh3P29UWFPAnnMJlQ==", "signatures": [{"sig": "MEQCIF9yWzN1xHdMXpwrddnQhkQCiv1C+meIBS3CvXcQJ1RgAiAoaFep2FFJiNblLhcprlIsagdFdPb4MpJg9UJTJ+lnmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzcCRA9TVsSAnZWagAA/HMP/0rAXz13lgcKf4GcyGLc\nMQqYZFkG23/T+wc7Htp67v6VKaUb8JN+VBN1yWwcceTqqnpbU4Yy59eBzEJ3\nP0HPgg4NTmOdKyjqttJPhTWxIDL6sRHu7SfHLCY1axTqqPkoe95/h8RiZCyT\ngxZHTz4J+AYoORjaEHQdsp2RKMONWA3m4Fgn2stg+NYAd/mrULYjB5XdB+Qb\nr+YKbnRV9PaL1Y4I2EPb/flH5IxsriD/aJr/8Icsw99dNqxdeKnKbTrIDL3j\n8qtxGINpfdiTu2jcc32OxEGSetgHwBgkPMPEUKnGcRHndKGns6ACnVDz/VFe\nNJbvxS+bi5hDsAmlNwQMbY3+JH+ckNPmhQH0kPBOn7uW0Td2OTfLZtAABr05\nd5IkbMInGElnE5pXcDTqJ7Orqbds9oe2N41Vtu4107tq+vVSB5GPPIa3zkkD\nxnkxb7g2T2YMDOVJa1XfZb9dfYipUiYaaO4Dg8TAg11EiiUTFbUpy4Ua3kbE\nwi+DM3MLcazYq/hjQuG6/91HWLls451ZtaW6/8KqXENIvcZvvO6hdBVR+cTm\n5LxDbZjMHZXMBO8wMIo24WthLFhaScVAyn6QBthfTmBe8ZWtNKmIzI9KxG2A\nrRHYYdu74Oo+PxM3iViY1ubtHoVOveUriDuAPSGSGxJZQogM+llG9/UJXsDE\nQR+/\r\n=aylX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.8", "jest-util": "^27.0.0-next.8", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "jest-message-util": "^27.0.0-next.8", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.8_1618267356511_0.9569705284757186", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/fake-timers", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cb32a9d11e12b1dc338aa4f81aa456a8d27b2b34", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.9.tgz", "fileCount": 8, "integrity": "sha512-BQa/T746d1O2OpOzcumyjQ/Vu2ZPzpG6Hng126Tmw5R4veYvC7QDWsDjznUkJioQfZkJIJgVuK2UVuKRPO+5Ag==", "signatures": [{"sig": "MEYCIQDeTEGx0drPVc10HfvYObpq+EKaRSElpDsMTP8Nqq57RAIhAKSBdcc31EMgyTlFZh/V/0Tgpbq3vyaR4xMHDryQEUlH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjMCRA9TVsSAnZWagAA9jEQAIkqCgW/BISm9jXBv2oJ\nHCo5Zl7Tj7c7iIk9lydAePiC0J9bAGM+7NgcSNiD2308sj3m6BOlvUjxz/0d\nsai4syt5rhvNYSA4dSBsngg/jn4UZ8vA1gzhvg1AIaHQk7oG2dNhW7De2zsn\nkYhFGccN5vNXI1Unpj0En/VmHCUD0LBYd0amkhgu3qayX7TBcWypNieKntCZ\nE4GEQdS6RpIpS7Fxvwmv/nK2/OYBy/GUysjOYWi9Ltf3oNniiLCLhMRw6v5U\nPtmjPgPCevK1NK+0/kyO64srTfWbb2IPlSlvt1mpnq9VBcChORf4p+YC1KaQ\nML6cQNH6ewnugm0KQpHIe4+3477Epy1/3/aZc3+403ZuCKMaimTOyO95fDW2\nh4T45qEHPIAXbexj9JPlDEdyHxZTBgPxAF4R3u1CF1tnr6g26vFCeme95oIt\nleyYtHxI1T8wFeW8dZ9wqgy35JPE0pth9qantsy88DD8XIqU/Kjr6AhfbwEn\nredYCBDB1Oqlf6/jXxNpMIzMxx60QeaySItm2OuoU9FnJxksZRriinNVghlt\nqAtwxDh8Pv+sG+iDmVnnSRErOb3exH8KWsZm4/M3U2EWqB4YPkdLOlfOeqDX\n6iXHquVWgy2v/K6duGKJNTqn5n4e4PlJYDyn8cbtejvppIrIzq7jj/4Ys1L/\nCjaX\r\n=OzOD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"jest-mock": "^27.0.0-next.8", "jest-util": "^27.0.0-next.9", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "jest-message-util": "^27.0.0-next.9", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.9_1620109515919_0.1796293060011631", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/fake-timers", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8d8a80a4989b93a5b1281ac65046d81d404c411a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.10.tgz", "fileCount": 8, "integrity": "sha512-ld+8RB9dkE/3Qudb/4Pl/iXucJteeNXpVPHHNahHKAFMT+4l/dHKGkLrGb9X5pMMViqQYvPyLw3gvMfs4OfgcA==", "signatures": [{"sig": "MEUCIBN41dCSGuG4Tvsw1qaQLinwRGLTXnZjxncTJjA9iu+5AiEAz/CFZSBghyd7bL+mYO93inGbZ3AOX45EivB2KIGvo08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4QCRA9TVsSAnZWagAAKYkP/2PPmyms8pVz1jWakQE4\nie0fG7ewuQIVYExokRnJ4/fyO8dPIHM+EVdXB2Zwiu5b4w0XoBcUOCE/YNt8\nD/RsoaHzMyJIgrAIj5dX73elmbgSFI0ZE2r9rQWVAWiJEoPt/dUp0O3P33AF\nxT1468LBVSTqYMKXN5SFgpI+fQypkFsa/cDh0t0Z7aShtq+aVyKMHImaHH3p\n2XvKJdGIU3L8w+TmTqFm26zLGHLQiwcN7c2Zq8dECE+Ml0iH6jpTVVppF2JF\nPth47WTK8ffjzr37+zFmQueMlBa0u95nYISjE9u5aJY4MpB+2L3kdPQ0lPah\n5nZYOMZTzzRyhHdiGKZ+JnYiwYU7457vExNZa2N7ejub5+k12UbfdzhNOA3p\nfjKCF/ABVxdKS2Du8vDRqD0CQsEIkQmoPCQxBUfagLMMGZdtTAoReGzTR7uF\ngXAevmhcGdgWuBJqPP/c29735EmUlMDnUi6VSWgUPJdGrfx1rSrARfdci6oo\nQ79FDvi7govqtEYbO91Syt+Tbi7Y4f8yGX5PlT/swaHxeb0qW/V0hGNvVSfz\n1A2DnkdHwLLFL3Bgg4J4+FlVY9IS74j2Tzzr1LOrN4up7joTyVTHzrSbEzGf\nulzEUCJc6AIvvJ2seykP3iFTgqLmthG4Mk5PVjtwdqJOlZi4ojDjlHPandEW\n61pO\r\n=WwKY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.0-next.10", "jest-util": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "jest-message-util": "^27.0.0-next.10", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.10_1621519887777_0.6781232609679306", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/fake-timers", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/fake-timers@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d038082da21270a85677c11a0f796eb92f03412", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0-next.11.tgz", "fileCount": 8, "integrity": "sha512-VdAXF3O0PIIqmjmS9x19TcVPpWu4aXBlIQ1LNj6FHuxO9Ig8/4iEngFw74KpujpgE8BQ5rM5u1LwNwg7GbvyIA==", "signatures": [{"sig": "MEUCIDLyo+eqwOUUTtWXLs24SMJ2UveWtycPSOJ4aaLDIuZUAiEAtv5ZvJ+8DV2UJQgXJwrkFttGTuKhzZDuih4cOdRipRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24674, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKiCRA9TVsSAnZWagAAz+cP/iddH0Q1j3O0wsPg9e9A\nq9ZE2+8CCz72GFudGA4l+wl094OKWXCXLJi6oJrEWbzeLUsfEze69lBNhaqU\nclbgXdNSuzuVnTmXvcSzlyNcZLow/HJrjcIKNxFg/AObtx2G3BZTeMYZOGVH\nuY0/s13l3T1wO6DsvGRLDtRBmOqbxsujBavp3NpEE6S5bK51Dfy+M081Zqfw\nZFA4IfI8y82Vh8iErP/e59kSgiQWhp98gN2ciTvUYsD4anGLWe1Sjgc9dgob\nXTCw84ePfI+Nw3lh+tSQjeCqXdQt4jQyo5XE2FPh+68rEIWGze8yOiX1+Lgw\n24pZHc8X0bdfyOoPnYBm7pOa5lI4KQSmwZhc36Xuob/g4+0S0Ic4990Yv6vM\nYP7wTUcYJei9JpAWYXD9MwMRJpEyCTT6ujeZgbzmkAHJLIwbFHkWPfIMfsFi\nJU0HAkpzLINnXChYUrIue4873zxwv4J7rRpCjrZ+V8XeUghpkzsugFyjH8TL\nm7Gg1GsSGQkxg6/0a89YvkeVHfm+xXfcd4Nenf3vwF7+XJ16x8RiCwMMNrF9\nbmRayS4UDqHWgNpHyO8QxYiteFJpkMWq3fktSpE29aX+vybda6rEWargj/iE\n7NSWOeGQATLtrh7gwFLmU+3VBMqEhbY25R4jr4rmaFU/+cDLzinIDZ2tn7g8\nPfh7\r\n=virN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.0-next.10", "jest-util": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "jest-message-util": "^27.0.0-next.11", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0-next.11_1621549729520_0.3704539250899319", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/fake-timers", "version": "27.0.0", "license": "MIT", "_id": "@jest/fake-timers@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9abde7446420f1b7bfa85ddf15841577ffc92ad2", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.0.tgz", "fileCount": 8, "integrity": "sha512-9ZGZ89FX3V2oTROBQ/HsWujr+A2ZWRPPspU5EbwoS8/R36fImAC9gqnenqiOcDjGv62DOMU0QcXO88DEYl3W8g==", "signatures": [{"sig": "MEQCIGeyFgCSyYnuzudaG+gGfRnyP05a0owj5T2au8NEedEFAiBk+2nUkQkeQr7mb2730If0yo2r2ssaYcNDT3iIieAWaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24658, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIXCRA9TVsSAnZWagAA5NIP/2CW9D18SpONbIikz+B/\nyuW9OU9CuKACCHnwoaCfI5QA8npc3f2yaRf+L8+JIIDmvc2rbt4UqDgos43R\nPNTaOVLJF0rBCWnzjWYEaShXEMJ+sd5tCIlKsrdbpsFRyZu04mv64V6oDmrn\nMR0Pvx4AGlcxDK51MRzYyoaweMdIMLLB9g8mWVjnue0COzHKbIMtGtGEzQXY\ncLDTmr8E61x5sLp86hoPRNGBdDylj4qeSYHU/7lbs8XivfOBUKZKt9h/2mcP\njyuS+u1wKmlDZh4WA7A3K7FRtdIayvDXFBBnZDeS5DcVPpA/PGYYNz1qiwm3\n+ZMqpjOqCWsmgK+UHroqt1ZdxhI8S3dbBe+n04FiLcDaLn3a0Veii/C8cV1y\ns2PmzMkrhhxBb2oTBfH6iTrRQ/3QvRNKdC+BCPQJtk3uhDG9sNcszboUKppF\nJEPPjvoDhheuGnD/sOxsA5EbgKb9D/PgnG6OwEKJzcN5c715QXgCNp4Q0u30\n/Q9xT/zR6g5CNc6DW/SRwZsfy3ODb1Xt9RIdnD4xRq7a6vwgT14VHkYXVIsA\nvHcReALC3gaURWibg+PhUVYtjwpTkWN1EPyegrzJJtW/Vp5kvI137JT8pE8k\nRuNzZZAcDUBBYdzPMsLaGqIlRrgbBf3gnKATgRa4IZuRDqAsO8CWe17nT5fo\nLIdc\r\n=hLyJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.0-next.10", "jest-util": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "jest-message-util": "^27.0.0", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.0_1621930518644_0.07794655185331978", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/fake-timers", "version": "27.0.1", "license": "MIT", "_id": "@jest/fake-timers@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6987a596b0bcf8c07653086076c17058b4c77b5c", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.1.tgz", "fileCount": 8, "integrity": "sha512-3CyLJQnHzKI4TCJSCo+I9TzIHjSK4RrNEk93jFM6Q9+9WlSJ3mpMq/p2YuKMe0SiHKbmZOd5G/Ll5ofF9Xkw9g==", "signatures": [{"sig": "MEQCIGyfqVpYh1jODwxOdqyzh0Xfzol4GoCsDZy+e+181XXXAiBIjxNlc6fvem+8T1wi71o5gDWM/fc3U9/7VztYehTr6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwwCRA9TVsSAnZWagAAt7YP/18nO6vaMRf667oYXu99\nEkl4Jzt4jMs4GVJ+a7+LheEm76eZ9iywdLk4taQCeKTKkdqowXVN9afXVnxS\nfuGodO3kxsLYjMAaZ0jbnPIfybshUTDV5Wb0mFDeQQfGCRXlOBOQl58XvON6\nsppdomIovyx0qYR3PxDIQrUn+KfU7VKc9hsPDpCJWwkEYwaZfCgZEqk6qgVi\nCCQwPULFrB1iy9+6JHuPa+4KYvK23xn6vFM3y/9DkMaHFnd6595Ay3n8EpTr\n7AGaEpPjn21N6i38VmYEmk1ZnK1bey7gCqyNPL178vbsUDbbLvU9nISQMcvW\nJHlvw3H5bg6TiX/tFJbdVHhaycWYDXmdWkyEXFnBNMq9hOy8kv+y3Fr1uJOo\nDTIDYsLzmJiZ4TOvk3IS4IPWnyWntonWbAH8kNAMCaKCPGGJcN15W/2487y0\nXJprAb5QP/1siutxhRpGC7Ce9UCdiWUcPv9k6ktI5n+PLDkjS3iqd1aRjts2\nG3WHSdy/7XxjMEMSDYJH22mbJ4qelFarcI7bOszIQ5046BWSeQqVHaxU6mCr\nGSdHSeYND0LpYX8qh7cIn0cJJ3SwnndGko+n63TMSuS1OwY/ISZZyW8a93jn\nScRHiGRRNaoUSEGN2qpZafPcw9IePcFce7L66tp+93l9AR63FX5htnKMLaR0\n/x9y\r\n=SIWx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.1", "jest-util": "^27.0.1", "@jest/types": "^27.0.1", "@types/node": "*", "jest-message-util": "^27.0.1", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.1_1621937199976_0.7639038655304375", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/fake-timers", "version": "27.0.2", "license": "MIT", "_id": "@jest/fake-timers@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9bd1bad0017e9e9cee8418d8b3d8338814d5d232", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.2.tgz", "fileCount": 8, "integrity": "sha512-Cd6skvIyPVszi8kbz0uMqbQp/WJCsDMb0YdrDCdai/0JJnR0IVIq94Y0fISXcTzI/3vJHoaC7icJaM+TZi9uxQ==", "signatures": [{"sig": "MEUCIQCGUz5yBoEZZJcQMmGe0avG1+pyFQLsR/A6gqRDmwYqGQIgJum4UOW91m9thFXyMHl3OFw35KRGHINl7ZYHqz65Q/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi5+CRA9TVsSAnZWagAAxVwP/01jdGd2+DlvpqNk9z5q\n17JDoBCs9WEFiDWha+KHKaFAFVSMs86VPQRhl8yXbjly22Nk58qhSkLioub4\neZ+d1Ji05OoFRxjC0Ht7kg9XwLLMFg4DelaOH9URKRgDU0vZ1dqqnAC5/Bkt\nPIN/yMNBsktGNBYl+K6bl6oedjcgIDLx+vwBwpSqzCCNm23sfM984zFUBIOc\nGFl9Me09UDmRa6g6wvgZqmaa23Y9X8UGb20Ny8szVOVR6qWB7QJEIKiYvIB6\nYiaNK5GtF6h41794jhdT9tNK5zQe2WcF/hMISqJ0uC0ZX5aABbSwJ9/xiUxs\n3frIwMSNSdBEahrKa9+olBUZfjFKTsyjNffVXQAk9WVy2FwVSG+H1yFKvD0S\nFwEeNqlDRTqXb3rek05LRga/Yz8IMpBuEnRPfLzNaTYSUiDeFWddopiYrwxY\nDuz4JAHNPqxaaSwnmWES/CWIv+Q0tg8nXbbur/ErJeUfzfyYaukhB6qj00yh\n3rzuDKj05g340gXCEeOwA4w9WivtHHxaJ1YqXxLoHphwcjbgvU9vPEBZk5O2\nT7NqOnc8rPCC2/R7La7m74Oh5eRxJecQEjUnhvCPEJQLQx1SehPpRBytATjz\n56tupa/R8z1uV0LgOaFV7KYlHZAtGm0laWFCeh+hs4VDW8YvRT84QWtE11Vw\nr4YD\r\n=QqdI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.2", "jest-util": "^27.0.2", "@jest/types": "^27.0.2", "@types/node": "*", "jest-message-util": "^27.0.2", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.2_1622290045983_0.6205367703297926", "host": "s3://npm-registry-packages"}}, "27.0.3": {"name": "@jest/fake-timers", "version": "27.0.3", "license": "MIT", "_id": "@jest/fake-timers@27.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9899ba6304cc636734c74478df502e18136461dd", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.3.tgz", "fileCount": 8, "integrity": "sha512-fQ+UCKRIYKvTCEOyKPnaPnomLATIhMnHC/xPZ7yT1Uldp7yMgMxoYIFidDbpSTgB79+/U+FgfoD30c6wg3IUjA==", "signatures": [{"sig": "MEYCIQCdALCGpzpb+v9d7CnUgAPvdy1IIKQvLrM+mxTXdHzAdgIhALltKdbLzgeeelqvckRPAc4PFXBbPfBhLppkYvOOZ8Gl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsn46CRA9TVsSAnZWagAAviEQAJ09M4p6Ak3ox9I6pNqa\nZl5F9adCZsPLBvgmxUaGWh0NVK+OnItdxqpJ7O6EwC08ML/O/fsQoS0ZS5gB\nKVJ8eGOzIs13AcYEaee2IRtI37y3s7E2tzAmFeoblhe2r9f5kdfhIWsj5DCj\nXuBX+I3rzVX46nZhLBnR/o6hqgP+e69iLPZ7uM3nZdUplcXksEVwMgxaVnUG\nkZC+BiH7GCEL2aJ8x4T2v6GeoU4vdBZeoMwOO/IRAHlwQ/GwQtkDPKOpC1BK\n9rutrEYqkFS6pzgO9wDLbO9lVXBV+dlJ4g62uP4LcQAyzPqSw3CQUarJAs/d\nx7IVR79ZTxE0qswg72Wt6TkIecnImZQXuqHjcukONmWdNhbApylbmvLu3UM/\nNxWEBtg6jyUDGRW/LkGKLea4415RB9GXxYqFr0e3VW2ucn6xrhBz/n973YQF\noQ0oeSnAHqyJTKmhmqnDqlHtOzZgi2L0wXykrfaW2fwfjAk1QooTREtz9P1e\n3JmFH1/GhzfmVMhMvOXef/E8AvMI0FeowGdYcqnRHIdobRIoXJ+Hg1bxyiub\nSG1v61rQV/kcEFj75LtVtGECIXCIYGbN7IVTjF2vLQkvC1342VKmoN8xE2Ax\nAx/sZp9BTowBMD59BFwRQgM2ywetyrNRDCZ4nQULJfB05MizsjOYhZEOCQx8\negmt\r\n=cplO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d10f645b04ba9febb7308392e635c0351d0f027c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.3", "jest-util": "^27.0.2", "@jest/types": "^27.0.2", "@types/node": "*", "jest-message-util": "^27.0.2", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.3_1622310458187_0.6400455460961783", "host": "s3://npm-registry-packages"}}, "27.0.5": {"name": "@jest/fake-timers", "version": "27.0.5", "license": "MIT", "_id": "@jest/fake-timers@27.0.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "304d5aedadf4c75cff3696995460b39d6c6e72f6", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.5.tgz", "fileCount": 8, "integrity": "sha512-d6Tyf7iDoKqeUdwUKrOBV/GvEZRF67m7lpuWI0+SCD9D3aaejiOQZxAOxwH2EH/W18gnfYaBPLi0VeTGBHtQBg==", "signatures": [{"sig": "MEYCIQDTtV/8TELgiCDV8AznxLKezGqrLKXOhYK6YXadOvWKfgIhAOCA2XLCmAUrnN7sCtZ3mCe4e54JkcslLy4AMmHpM4jK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0cUzCRA9TVsSAnZWagAAxboP/RoRUmRM8j9e6R+13GBQ\nyH6ULvS1m5EFr7yQDNQ0PaWDxdAQTZTcJh0J/tHqxTz1yBQqL/VJuXzLFg+S\nmXnvV68BDe9jYzxMs3utvAYEuPAKEVKLErcA1y6rCcfgGBSZoeaNsPlj+YSi\noS79zyFQNX5zEhYQACGmgok7+nCC0mOK7/1okAHeuH5viX8SpsrewCWIpZTV\nG+JxutKqsREYoe9NbupAsQfF/mg4wD/9yCGryLb/aUovUp97Zu1j/RhtqJ3A\nvaWr3L9pnyrq+L+NwYyGCnRW0MZK6d2o0EcSPRLZnbrHrP0+pPyo9SUZ38dF\nH7cVs7OcTUEpFX6p+97W4o+YNo3/LkdX5dw0vgT1CVX1PeCp6hUtmhQykZG0\nf/qSxWBY6XC5mvFhP2kQEp6GDybxBPGh+VkWq0QGTG0sjvb0JqkcPrWzm+rf\nTrz1/YcGmwyZqXs5CFX3fDZIfnzqWXmklXFr3W5gQENzXDo60K+IhGMuNQJc\n7ow2YJVRAO1/zaf2sFiKDF8d/GlIuxE4AuLmJoB5vOPU6GwaavxxZlTkCYbx\no86FB6FzveD9Rwsqa6Z5shI4sFq7qi8pVX8HKUcx1w9xStZat2jAhsjf27Ye\nitqJQ3otp+QBn+EAR0Sg2ZAnL6MxUF3lgp9IdPFHgGBPKDPv9ySoRQqdRewf\nGV8n\r\n=d9eq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "dafa4737dc02887314fd99e4be92781c9f8f9415", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"jest-mock": "^27.0.3", "jest-util": "^27.0.2", "@jest/types": "^27.0.2", "@types/node": "*", "jest-message-util": "^27.0.2", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.5_1624360242617_0.8747467192458749", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/fake-timers", "version": "27.0.6", "license": "MIT", "_id": "@jest/fake-timers@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cbad52f3fe6abe30e7acb8cd5fa3466b9588e3df", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.0.6.tgz", "fileCount": 8, "integrity": "sha512-sqd+xTWtZ94l3yWDKnRTdvTeZ+A/V7SSKrxsrOKSqdyddb9CeNRF8fbhAU0D7ZJBpTTW2nbp6MftmKJDZfW2LQ==", "signatures": [{"sig": "MEYCIQCpssuF3b7O3sFoTZZFwEgKNI5Jd/lObr9RFNCrN7jnxQIhANdDsVcvG7Lu5O4J3lvIU5kCjk1PNY+0Yiln7kZ224Gz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFsCRA9TVsSAnZWagAAlZMP/RRxnAYgbtfFUH5nZS5e\njfLtGW6PYTV2vEYOyD5VpX8mjMdFv4gtgcv9FX4LTgh1WcPpzH0hHJ8mYQny\nmzbosdIhfd93j/IABzR0ME/A5PMTXi6roL0r7vdJ41EZwqebBXr+BYZgsX+F\nR345UhIZgxO8N9bWhm7Hl+r9z0YkR90qiwL9HGgOFzQYHWydVgCGxvVuYma8\nVfMNaXFav/56OjMu/+8wut1DeN8ug7n4X123QkinjJmsnOsGXhQuxnWsITUm\nrGd4VuX4ojzVcf61bOjz+30rvgsXcnJcqV2UIJPfcejmEW3jKHHqTT5DzD+Y\ntao8oPwarzp0791drKxJaFBcn2AtDLXOIVkwGWVWL34rsqi1VcSe8wzv3+7Y\nkiMM7h67FLSJrkRjKRT0szpOYuumVEy5AsjzwJODLzuP+5lyZ/e5UkHQlpFI\nBfFyp5ib8TvMjQSem3lStsZnsk/nr0HWd2KbHy0WWcmoL9HIIw5J8pWp5wMx\nI3C9HBoixPmYSRhpd0Y1R1sXt5OFAfbG+451wwCwp1OtqOVvlAOfz/mob7rp\nlVrUeeP5WEXC0YVxPU0rxbVQwzE655T3AaMvQVWXvV6mXbPKFuUOF4W6slQf\ng4nUNcbTL3k3QaYFDf7zRnJ9Pjbopl3iLzVQ2H10oRyhYP+G+gezo7C+iuXG\nGSvn\r\n=bV1Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"jest-mock": "^27.0.6", "jest-util": "^27.0.6", "@jest/types": "^27.0.6", "@types/node": "*", "jest-message-util": "^27.0.6", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.0.6_1624899947978_0.3694566064364322", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/fake-timers", "version": "27.1.0", "license": "MIT", "_id": "@jest/fake-timers@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0b343d8a16af17eab2cb6862e319947c0ea2abe", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.1.0.tgz", "fileCount": 8, "integrity": "sha512-22Zyn8il8DzpS+30jJNVbTlm7vAtnfy1aYvNeOEHloMlGy1PCYLHa4PWlSws0hvNsMM5bON6GISjkLoQUV3oMA==", "signatures": [{"sig": "MEYCIQCTVWrCOYyNwlj4PF21IgTfTQCptO3wfltuCPm/XSqlUAIhAIBxF+f5NWIILjjgHcitS+ek7NRLNhfXi3r0Om5F0tTx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLePCRA9TVsSAnZWagAAs68QAJ187lNhvDJH0OXgbYPV\npLWM6YbLqEK/ZmwcwdIdWYZz7JHuIG670Xn5cDgn1WS6ZpXKiKu88u+gyrsg\nezI4LATNWah85o7+GdpJeyJnZf9WN99TLFMxGLhdbA3mb6HRs9vpbMdoZ4rM\n6rddrpdycYf7HNiXs6dV06zs1CRV3H4R+bUl8qF7yPrgDqRo4Fk1WjZNQnx7\ndG2ZpqhLeogt0mI5jA+M6+lO7TRS0T0y/EgjhCyzSXNCNlAwOIq0BKEWOfnu\nyXcaeCJdzSKkM2gZm6aEE7LVRAdiRJDdpIz63Qtsyms35QpoAttu6zZMHpY5\nEllQWkmMjcGN1qAdL3WpFcD+JaC84ZoFMyuCQ10gZuvV7BGqNCipl+TXTdTN\nqCCLmW3AsrMUnrUa1HaERm3psX9b9sGqZiefsnMfBATzZYU/CGKjbn9kPEaC\nODk1CsTy7XanoaFtlAg+m1kyOlwdmz6LrScqjmwNX8SBrSOSoz59JCa+f5BT\n3mIdt9JN+j0CmwNLCROip1vHt2JbXX1M6fywUUnmbuAYXtFFkZ+Iy61mx44R\n67CAwbMgyWPIeLGp5yfT86Ro6fceyp5GvatombCZ/gQKpDv64xAOmQwsjt2M\nyM1cGojnd6a4wVyr4AT6Xt2S1tc9KI4Fqwt/JW5gRxOAlE9c9IqYlWkkU2BZ\ne+jh\r\n=oaW3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-mock": "^27.1.0", "jest-util": "^27.1.0", "@jest/types": "^27.1.0", "@types/node": "*", "jest-message-util": "^27.1.0", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.1.0_1630058383628_0.4753963723647976", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/fake-timers", "version": "27.1.1", "license": "MIT", "_id": "@jest/fake-timers@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "557a1c0d067d33bcda4dfae9a7d8f96a15a954b5", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.1.1.tgz", "fileCount": 8, "integrity": "sha512-u8TJ5VlsVYTsGFatoyIae2l25pku4Bu15QCPTx2Gs5z+R//Ee3tHN85462Vc9yGVcdDvgADbqNkhOLxbEwPjMQ==", "signatures": [{"sig": "MEUCIA/H0VWOVQv7Gw18rXfdZreKpdm/qGCsRznDYsW6Yz3LAiEA0PQBWgFsWrsBGnLaltVcrlu7BqBkCAcsnq+3Z2jqbVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyCCRA9TVsSAnZWagAAIXkP/3BII0p5/Y5//HBUboSz\nRNLENDnTpQKIB8NiVH42LzipwLsOeB88/yuE+RzrTQ7ShVxCSG4KKuWHW0ch\nO5mGxKM11q1a+wgNegOGSMKUro573/CSGB84U3ecHIdo/2+9AL+hnfrU3m34\nDwYF0E326EOLRBilx52gTViIcdDtbCEyl9kNdeDYBdTZdptEw6V2AWFxtUqV\ne08jv2fEeV4iMJVt/WA5eKBt69uFviYNHUtFVmp6Acf6sy6MMeaQfLEwiyYZ\nfkTpzTQXB8nVDXAwyC+XrDTMZ3eiQDPchtvpH/P/55U3jM19nX8l2MqTNuPr\ncvN1nIDqYfh7Av6ZsOegxwzsNzPWuu7sOdXG17IxCv90zlxWKTCcbR3OGdge\nnqUTzJlEw1YOc6Z2Y8WlvnWDlnOk2pHN5I6MMjjq5CgwIw/oOn5wtyB/Eto9\n9E/BPuHzI1J4dXwOqprfWPDld+gGUD2GEhM8tNm4Hl+F5IwrDSGZ2hEDdAqg\nQUI9U8suVZTq1Fdx8AnR3kmrs81kmdqbUcuoZI4jUgq6buBFAM5+/QxE1VaH\nWQf/hC236pzSc4EsOy9HLIf6DdhI19ixbuZ7CLND96nfmdk37t6GwKFRuX+V\nscnEF7ROAy3nM/GJf+DG7KLNBQjplmr4/4KqVjEK4MA5/PPR3jV21VpxOjjZ\noWLI\r\n=6z7y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-mock": "^27.1.1", "jest-util": "^27.1.1", "@jest/types": "^27.1.1", "@types/node": "*", "jest-message-util": "^27.1.1", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.1.1_1631095938228_0.4637137878035795", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/fake-timers", "version": "27.2.0", "license": "MIT", "_id": "@jest/fake-timers@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "560841bc21ae7fbeff0cbff8de8f5cf43ad3561d", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.2.0.tgz", "fileCount": 8, "integrity": "sha512-gSu3YHvQOoVaTWYGgHFB7IYFtcF2HBzX4l7s47VcjvkUgL4/FBnE20x7TNLa3W6ABERtGd5gStSwsA8bcn+c4w==", "signatures": [{"sig": "MEUCID30sTq6RUTI6+uaJhlVN6sXte/ct2oDB/91fa2P7N7YAiEAvs4Gjadheq5Oac4OK7L6O8Ykh0CnpqjcW9KQYkv6jYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaYCRA9TVsSAnZWagAAE4kP/2MtiRWOoQDuIdYV3DRv\n/jh5aM41VMaDMPI8yYgTKukP/2vKikhEi6+TyhSjZBRX79wWkj3gsdu4gks7\ny7Ns59GDdY9VkpcXXr3LONQJ+3/Av1Bf6/wm4AzqBgnBLNUS2qe/NzTpt7qm\nRNp2tf9ROVoEAv4dJltmCew3/XrCk5FHAfdUfxqWT0Wdfkjsm8wNRqxAGsTm\nD70LtEVE10JcY2JSQsGO8TdP8oguE5xhOKg2XWjyDEfWKLkcKsltCDvirdl4\nOtZCRZaXA3SVQSOC9MjgvkxcN9d+ubuSRJKv4Dtab9yu9STKYf7SBeA7Uedz\nu8MhJlylOB7ypvrgLnJ0r0anq3Pv97BGPgywBDWy5ZWbYI01IyM68mfkBjaU\nT1nyqG5qGUpFAWpe4mFk5QEbUBwyJshNfgOIgkzacCcvU/a1lMo857o1K+1K\nemC5BnyJaeRhZwQGRtN8jdO/4tXdOguwHSQRL2/psWvKsej2CgWCNeOEy3qk\nPCIXqNxN3wEC6QYmhtamFSBWd5bXDVo1ny0vDOZEdHFJUGQym+6DETluvco2\nguvB2FPnrsnZO41EcDKwi0OHINxTBVDemEjeInFoq6AD8HqupCS1dmtp9cZ0\nazXAwtEn8fusTYsKJDi8HItyz/kU81ag1YR5vH1NIr/qvf1eppJX4RCGA8/I\nDx9t\r\n=SZJD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-mock": "^27.1.1", "jest-util": "^27.2.0", "@jest/types": "^27.1.1", "@types/node": "*", "jest-message-util": "^27.2.0", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.2.0_1631520408288_0.43844453775486913", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/fake-timers", "version": "27.2.2", "license": "MIT", "_id": "@jest/fake-timers@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "43e6f191c95ae74e95d0ddba2ecb8470b4a288b7", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.2.2.tgz", "fileCount": 8, "integrity": "sha512-gDIIqs0yxyjyxEI9HlJ8SEJ4uCc8qr8BupG1Hcx7tvyk/SLocyXE63rFxL+HQ0ZLMvSyEcJUmYnvvHH1osWiGA==", "signatures": [{"sig": "MEQCIHhTGigA/vQb/EqqMJPExgU5ukpEvEKt2ioeqVRErGNiAiB8QgyNlAsaI7wQgulZ03bEz9vU3VMW5kAoX4UzSfqrvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.1.1", "jest-util": "^27.2.0", "@jest/types": "^27.1.1", "@types/node": "*", "jest-message-util": "^27.2.2", "@sinonjs/fake-timers": "^7.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.2.2_1632576909869_0.6032039875072914", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/fake-timers", "version": "27.2.3", "license": "MIT", "_id": "@jest/fake-timers@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21cdef9cb9edd30c80026a0176eba58f5fbcaa67", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.2.3.tgz", "fileCount": 8, "integrity": "sha512-A8+X35briNiabUPcLqYQY+dsvBUozX9DCa7HgJLdvRK/JPAKUpthYHjnI9y6QUYaDTqGZEo4rLf7LXE51MwP3Q==", "signatures": [{"sig": "MEQCIHPS8ojX77GuXUoit7WrOsukq77TY+zP4NJts579TyWXAiAvzRxdJkmsUIsF0HGpUWyPIPQO8vZ4W+gfc0Z+n1mi/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.2.3", "jest-util": "^27.2.3", "@jest/types": "^27.2.3", "@types/node": "*", "jest-message-util": "^27.2.3", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.2.3_1632823883887_0.008813529291223476", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/fake-timers", "version": "27.2.4", "license": "MIT", "_id": "@jest/fake-timers@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "00df08bd60332bd59503cb5b6db21e4903785f86", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.2.4.tgz", "fileCount": 8, "integrity": "sha512-cs/TzvwWUM7kAA6Qm/890SK6JJ2pD5RfDNM3SSEom6BmdyV6OiWP1qf/pqo6ts6xwpcM36oN0wSEzcZWc6/B6w==", "signatures": [{"sig": "MEQCIBGGb6URSn8+lcLVwrRsemX6m2lINmEEBBczFNj3RRQDAiBvaB25YO2ezZdYwyjsloYfliJ1OCqk+eOMZT3z9cl/qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.2.4", "jest-util": "^27.2.4", "@jest/types": "^27.2.4", "@types/node": "*", "jest-message-util": "^27.2.4", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.2.4_1632924290807_0.8118878122127773", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/fake-timers", "version": "27.2.5", "license": "MIT", "_id": "@jest/fake-timers@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0c7e5762d7bfe6e269e7b49279b097a52a42f0a0", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.2.5.tgz", "fileCount": 8, "integrity": "sha512-ZGUb6jg7BgwY+nmO0TW10bc7z7Hl2G/UTAvmxEyZ/GgNFoa31tY9/cgXmqcxnnZ7o5Xs7RAOz3G1SKIj8IVDlg==", "signatures": [{"sig": "MEYCIQD0t3jAbxKI2H+GWCOU9/cBNbB9a5pnaaV0dbPPD/R/nAIhAJh20dvzHuyUcy6NoCaOIAGV+zvGW6N/ry7BekfTq9we", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.2.5", "jest-util": "^27.2.5", "@jest/types": "^27.2.5", "@types/node": "*", "jest-message-util": "^27.2.5", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.2.5_1633700363940_0.5602765345658829", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/fake-timers", "version": "27.3.0", "license": "MIT", "_id": "@jest/fake-timers@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "716f166f56abc01901b7823da503bf16c8a00ade", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.3.0.tgz", "fileCount": 8, "integrity": "sha512-GCWgnItK6metb75QKflFxcVRlraVGomZonBQ+9B5UPc6wxBB3xzS7dATDWe/73R5P6BfnzCEaiizna771M5r9w==", "signatures": [{"sig": "MEUCIQCQmxsqZyyK4kIHnkkKQdoXXVt4+iuN6Hkf7hnfQb6JBwIgRIUPuvfohNhJ2KDUmcxrPrD6hFJvzw94wAlBTE8eB68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.3.0", "jest-util": "^27.3.0", "@jest/types": "^27.2.5", "@types/node": "*", "jest-message-util": "^27.3.0", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.3.0_1634495687914_0.25912696528650403", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/fake-timers", "version": "27.3.1", "license": "MIT", "_id": "@jest/fake-timers@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1fad860ee9b13034762cdb94266e95609dfce641", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.3.1.tgz", "fileCount": 8, "integrity": "sha512-M3ZFgwwlqJtWZ+QkBG5NmC23A9w+A6ZxNsO5nJxJsKYt4yguBd3i8TpjQz5NfCX91nEve1KqD9RA2Q+Q1uWqoA==", "signatures": [{"sig": "MEYCIQCh/REfhf59X2aSkt+jUbRKf8lRheZ8uCuf0jYTLQWuTgIhAMZEX9LGWwNXV3kL8xG7CTiUa5Lg4EmCuESDEDuP5el8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26464}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.3.0", "jest-util": "^27.3.1", "@jest/types": "^27.2.5", "@types/node": "*", "jest-message-util": "^27.3.1", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.3.1_1634626654902_0.671764168748453", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/fake-timers", "version": "27.4.0", "license": "MIT", "_id": "@jest/fake-timers@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "541638975bad78e90fe2aed9f9e9d43709972410", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.4.0.tgz", "fileCount": 8, "integrity": "sha512-oyMxDKlj/ThRms9eS0xFkxmUvjJ8lHsNS4gNErDRFSruTER1/OQi2L5N0sJav+/AcBoY/Pa313CpB6RgdDacGA==", "signatures": [{"sig": "MEYCIQCprs8xfMfN9DbPWK86z6S94qu8WjDpcUlS/EgPrn8xiQIhAIWmXKdhbZyN5nfH1sQEY61LZAmVBoIApbV98kF7hGJA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNeOCRA9TVsSAnZWagAATGYP/0Indchw7YTQODGoUIyn\nRte/h0c2EezZOAEvBRmXUOD4N626gv18VxQCeNY+8alSwXDqpBkDp5JqXGHO\nFVMoGIz6Z0CMjROTTFI9kQLA49Xmh7WswCSyUV/dOjN+9zh4P5Y72y0wCBRR\nZCK4xgY9wffVW7fPwOpRJ8/zH2kSS7EDyndAE+9wuldgOzsSPjkY4mH5M8Je\n0W3kdI0BYEVLqSA5yKd2M7mlX6njaZdix/UjIQl+AHCf/R2o/NuL9thaKu+g\njzQR8KcY<PERSON>ih8QhG5l6rN/hHg2X42mWGy83lkWTLRv7+huX62fbaHmTX4RAy\nkNjMKHh0dAVTvS8T1OOcsjSd0T+inXURpSS/vYXgibbuE87MZfae6sbl2WI1\nm5iXx7/gVpxaq6pIptEe3i7OitaKp+DFK6XjxN1s0tasigDgmOAUYDLyJV8u\n9LIC3PSthtQOvSx+8pQbbNfJZ9aUi3VmbtesF6ynuZefCb0EyzFl8ggeR0f/\nfiX8ddaQy20GM27i6kobk+QllhTjx2uFwXsAuu4yKaeQNsRZY6364Qy9S+iA\n49vQg0at6Q71fPHGiB3aejPsGt9KSWHdld0ZcyyYg06Nhhh0XCy6+Zyg2lZx\nR9UsyNI6dFxvb2pKv/kCBqZUglyFEdXZiM7pmiUHb+PvG85/ntBzgHXE0EP3\nbfeS\r\n=EHtJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.0", "jest-util": "^27.4.0", "@jest/types": "^27.4.0", "@types/node": "*", "jest-message-util": "^27.4.0", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.4.0_1638193038398_0.5622416453546863", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/fake-timers", "version": "27.4.1", "license": "MIT", "_id": "@jest/fake-timers@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5d38915f684574c4ce7ac591e0aeeffa1b25b8f8", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.4.1.tgz", "fileCount": 8, "integrity": "sha512-b02WM91qZUMEy1CGWf4nUtLf93CMJ3lraZHZpRV3M7CCB9Cp+AkVGIgbFXrea3OjX99PUUIHOpQv9wGtmMIifg==", "signatures": [{"sig": "MEYCIQCaPQu+1jNGQeMMMUvNjIg5IHWM1vzSSxM94JR3TIPbPwIhALQFZF6rD/whKCdhN5l/LXU7ddPlYa14QC6U1wOB+WJe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeK6CRA9TVsSAnZWagAAa0oP/21PH/k1UkKCmBX6oZEc\nvd0OdjB4fxIupdcSpYBj/CJ9JtwVGRK8KizwRVENkxOWPR3s7uYSLQPnbrKv\nkyKbx9yVlGQBRMMvs/j4Yo8H0x5u0GYjWLMfLmihFxObfdtn4Q4pR/TTjKAo\nNW2qq472+DZp0vGnTV9iU66SIdpPsATeWmWr53uHsRhD07YjaU1v/qKAzI1H\nTI0RMiYjqqneM4YrICztoKu7uP8ceOhJeWcJa2MdeIkEvW89ZfP01uMgevfI\nO9ZX1DeH43JIEdb3U7Re9StEJJxdvtM3ePPbHgTuyMvN2BFQ+4Uk4jnp/dk/\np1uZHU/x+EqrDneLW73Sx45c0nuhKIr3deryAJKqN98+PMd1eByc2bZYcHsI\n9kn9PU7bLOX0FwU9EXc4VxQsuigqZkjRG+eW6TkdiBcikTdoKoDyYWg3G/Hw\nAGjAFvMLNtlYix/vmlPq4+IAPdxYcCEoDd1Q0qBG2wi6XkAAwAtiFmHJYm3A\ns6hk+aPDInZPSzaiCMIXmHuYkKsVyZ+LRxJijErF+CVyf6gFk9VXSWElM371\nOwUFl/gsl95fVswclxzReGF+piys0Zuy8bRF1tAHVzGnaB6S+AJJsTBW7qu0\nuHW2GLmNWe+4VZ+6moy1twPi7ZApFg0Kr2nfY/LXC+9klsR62duALfFA+KFQ\nmZsv\r\n=nk+f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.1", "jest-util": "^27.4.1", "@jest/types": "^27.4.1", "@types/node": "*", "jest-message-util": "^27.4.1", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.4.1_1638261434321_0.9994658297682806", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/fake-timers", "version": "27.4.2", "license": "MIT", "_id": "@jest/fake-timers@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d217f86c3ba2027bf29e0b731fd0cb761a72d093", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.4.2.tgz", "fileCount": 8, "integrity": "sha512-f/Xpzn5YQk5adtqBgvw1V6bF8Nx3hY0OIRRpCvWcfPl0EAjdqWPdhH3t/3XpiWZqtjIEHDyMKP9ajpva1l4Zmg==", "signatures": [{"sig": "MEUCIAJPzjJShe0ernxTTedjENCoA7jyVF8jN3pUjArS0xgCAiEAm+92PBGiQTd01NDTOTEb9knNkFp83873flRvvAj2qDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDPCRA9TVsSAnZWagAAJDwP/jDeh7+GHBgM4gRQcGkz\nlDUSeO9RKIz7lhCLNgWWF1WzYBEZPMDZYucsQTnqXZBtrWrANhodA3c8kc4j\n1MXq6OBTq+RMnaYYxTm7Y5msCiAIPjDjV4ojKwNHPGmLtBWkQ8XNSk57iEyY\nAMm8XnFMA8S0TD+ge78ENHD23MM9jtUL2mALTf2D7Vi1iEnEEhPMhZ2o4Q6o\noE3WR2Gtin33uWTHyAj3RqH23J2Ml1w3GP7leuWAYMN5j12YhfUZDAf4FnsW\nUkACko7PJKasFQTOFb7QRZwYyxCLvHSgBihZC0Ph3+7wY+r9UYQlV+EQHHgi\nrWnOI6AvZZJWad4P7jIM0uhBAQaCkpXvwDkOOVroUQc9PilAdwTqTR6x1L/z\nk+5RpM+qMVZpqfG7RRrUcLJ0g1+Eu+nDAjzuft6GhnKJBzOm6ZEusOfhZH19\nVjhLRUsDwgotd2x+H0QPocQ+X4OpZJFqYDaa7e/ib0UX/JRQVBCv333AV0XE\ntCsCYFVHcwucbuk4EIbtNGq4+Sx1JWZmodC9/Zg1rD8JFowwpvjVlnOsZnS3\naM7rDVUL8DcQbyuCK7ew5iQFzUyCysU3StKA13Wl0C/ohkM4u0fXyo7UEr9z\nZ6aW2wb5mkXbWaRk6VM3F52niVZJ9cUKuZtSr1LJcC5CHjcW60J4uRkSnGke\nolfx\r\n=iBnZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.2", "jest-util": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "jest-message-util": "^27.4.2", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.4.2_1638273231301_0.18431342427458164", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/fake-timers", "version": "27.4.6", "license": "MIT", "_id": "@jest/fake-timers@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e026ae1671316dbd04a56945be2fa251204324e8", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.4.6.tgz", "fileCount": 8, "integrity": "sha512-mfaethuYF8scV8ntPpiVGIHQgS0XIALbpY2jt2l7wb/bvq4Q5pDLk4EP4D7SAvYT1QrPOPVZAtbdGAOOyIgs7A==", "signatures": [{"sig": "MEYCIQC7qqdXiCkWPglHnpPCEhEx3Qsn0Jf5YOirtVaeB7745gIhAPalfmj8lm9lTY8Q8ZsQEqdTDnZu04GKZ+DcO9uFmzVC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJMCRA9TVsSAnZWagAAGxkP/AjDGzc7f1Wfu3r5TLb2\nfuXqg1vqWRdTUq2MXhlTInjHQb3lUI+UC2f4pR5bWzcht1JJgTu7P2aT+ZK8\n2cYherpWunqQxo25ld8zIwBLXHvpevaSr8pP70vvc/rMahOzpP/wP/eJ9VPj\nBkMKKhytZ3HUCnVvziz3p6GnDfLIzWStPyX+oSscspqdsG0lubQyQzPO8IBA\nPimbS/2mXNpJsSYg6QhWaZlp7NvKnwno5kwNl5UNjrFbgVWz9lgt9lWZb/vz\n4OWi3iMPXJ+0GDf63u1EWCMxlNvGktyUkglN/Z05tw90HSxA+VGfzmCkxsMR\n6k9loYu0CV7b+xEhP28aZ7/qoUGd1ACmwyRlsHYNTTLHhkjRRbSKV5an8jtt\nN2toEGXjvDHlHd5ooB8qmu6rfYanfj4F9dsRWyqgQz9TQ4hTEkGYVOhhwoIN\nLENvgfYiqocByrUNGInvM3yNi+K5eh7l6H/mFXfE1MOlNzXe+Y0oGwddi2GP\nLD+AB7DzoSkl3a4pBQ1vS8PwhbXq2zKhsB3hHB0apk5oEvVWZ9xk/2RFJN2q\nHPrSbKQgYfdvvau9ztpYCZ6IYLYzJZ4BoqAHVobDyLdAlIDR8Ztnnp4RYQhj\nrRk4wLgK1hmBHHS5Ul+Eak2UMmysm/soneWHHerpteMQl7X6DoUJZSI/aMaI\nkiNS\r\n=n47u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.6", "jest-util": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "jest-message-util": "^27.4.6", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.4.6_1641337419985_0.16618155760432995", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/fake-timers", "version": "27.5.0", "license": "MIT", "_id": "@jest/fake-timers@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f9e07b4c723a535f7c532cfb403394fa40d88c8a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.5.0.tgz", "fileCount": 8, "integrity": "sha512-e3WrlpqSHq3HAQ03JFjTn8YCrsyg640/sr1rjkM2rNv8z1ufjudpv4xq6DvvTJYB6FuUrfg0g+7bSKPet5QfCQ==", "signatures": [{"sig": "MEUCIGb81H9zN6TIaKNYPb+XpujBUjJWf63uTPbf5OP1CyUzAiEAy6SvQmBmlKkamRgI3biYTe6Gok5x/HdPcSDnSVi+i/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp/CRA9TVsSAnZWagAAFgkP/jBmhjgRz39fk+w4FPJ9\n7IUMzuGy2R1yMwDGgjQ+3fAcYEyo8u0Hq3/YTaRauosnUbHuDpakhx6oiTOe\nQgvaDDxgodEouWyqjFxlbFvlbTn6nnl34qNNZhwKG4tvaewBlNCYgcygZr/T\n0UM6MMhFw8zUfAM92mBGYE3We1CjqXRszaWcQ4yjeTT71GZWnAKaxytFj+EM\nQJrI+y3PAOk3ey2pZAM9fnuOMG22NK8rM6GDXXCqpzIZSDe/GirxkDoNnQjw\ncAVnsFaA0tDb93l+hgTX2nUlgjktMWCkH2iuirHFHLj0W6awi0NsXpwOeH+j\nmsAYTpMK3m41HicxXVLwU8vcxm8VLIq7hn2xAj7YOOJGH1mDSD8o/lAXci9S\nz294YRGs21qQlJaqFwZOKJISVcyIK0p7aIkrCfigIRbwjKrggF71BjIoN8Sn\n7IzkyyyWzEhUyni7jT+HDqnSWFT5CcfzyqDuIhAiU4xKii6idORNHaRG15T5\no2MmNINGA+PfP53P7z7HAjWtceFRTg8JEs/tye/zs0LYsuG52T9ysSzHA10S\n06TGHPAkkCao/tCIvUIWU411k7GvGoIZpQ/APMXBCGidxhS+EJ/I/qVWfti/\nPfAxDA6l/mkdOVg4nE9YAvfCPlqimaZurt1hqdspCg+RfiMfkA+F6ERv/lKc\nQ38U\r\n=EF/F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"jest-mock": "^27.5.0", "jest-util": "^27.5.0", "@jest/types": "^27.5.0", "@types/node": "*", "jest-message-util": "^27.5.0", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.5.0_1644055167074_0.2619202822910316", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/fake-timers", "version": "27.5.1", "license": "MIT", "_id": "@jest/fake-timers@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "76979745ce0579c8a94a4678af7a748eda8ada74", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.5.1.tgz", "fileCount": 8, "integrity": "sha512-/aPowoolwa07k7/oM3aASneNeBGCmGQsc3ugN4u6s4C/+s5M64MFo/+djTdiwcbQlRfFElGuDXWzaWj6QgKObQ==", "signatures": [{"sig": "MEQCICl3Txn0rBw7yS/RkIzuu4W43Yt1bjt2EYIPinKhdW57AiBlaw/0zg/BNFsC6qnsN25YeKFejLY/SgY3OBNgbb4Puw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktnCRA9TVsSAnZWagAAdxIP/3DG33jaIRP5GqFKHMgS\nJywUqzkKxctD52XFK6Bkk+bK4eOltst7n9pFGHR7h7TnBEwAyK5bXfBaH1hW\n3GiOHYvdVCxZpiw2DR7M0jlHreLpilnCzHS6n6q+lJtS+6QM7eXX/OvehZ2P\nuIUKup2IVg2h+Raqwn/1J6mdkWgWFWARJwXLFlxQcev/3gkzrRnpdSinSkZv\nSv8veBAesphODuPwiHIHkm7WnkfGRtPSv8W3TE36ohNEmbGcQPDKuAGpIoYJ\nXsIwB1lYc/4Qmr+/kucl+GaVOD0aA5DaVnzUZ71HksUGi7rsHw2tMGG/6CDR\nj43jzqDIh+n85jemCSg2FjEkdO6Fh/INnu8zF9moPjCARyn+IPBpYaZURVfY\nX7YH9Kqm7fvwfFXFYYeHXB8SRGmNeRraOTKO3aCMrf/rZNITxA2NGl1Ps0R5\nx/YsR4+D7/0jIKE2fh+TQa/5UhLEO7zLNl3ggTtsrLT3LLlRSdVvHk42JvxG\n8mA5YIbVBT0hPF0bGneI2mrGlyh3l/xdX81Q+yCsYEccZUnZ8bB5ppQt4osJ\neZsX9STh3Zag26GwtmeIs+Qgz4vgX4L3qWaaUQ6AIT8DwSmo0ngLPmA2N+kr\na03NlNnDsIasTwtPLSV3caebNgniMJSITNEP2c3kX5lC07Wr2xBT54NNhwZJ\nZ9KA\r\n=woXk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"jest-mock": "^27.5.1", "jest-util": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-message-util": "^27.5.1", "@sinonjs/fake-timers": "^8.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_27.5.1_1644317543560_0.4441091483190571", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0c6edc02397ab8170c264b84d05fbf3f231cc46d", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-/L2Smyd9WB6cMcOzNaL2SNtuvS6soxIr9D/rvCUYjg/u4P4f3sWMChEIcUvjBtZ9x7I8sc++uSDRfAvLKvasqw==", "signatures": [{"sig": "MEQCICtYDJESnYFUp4oQN4GG0kW6sHZQx6uoMFC5rupI/w9fAiAibaSNz7oHzd15QxcwNtf69s5JB4v+qTnJP+8i3PvUCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbBCRA9TVsSAnZWagAAHhkP/3lpS6SkkMvls+Q+3klV\nHepXDmrbTFPw2Vb1wGvDEV1Fptw2gqXE8RNu/2AAH0l+342WtydDA7TOmVMc\nqFGNM3O30XiJ3WdVjvj00F07klNtTa1qQm3mVeK1PT8Wvynk8bHYJBvxgFv+\nbKqyEAnQ3aE9RNkoY3pzhdp9DfQHB3GdbjDNYalrBPjlIUY0TNX5jqqxB6xM\n5HzwGAdUEUVGGa6sYZw8QR8i/R4FCWGtWswnCd17ek+XSTY4Zu2vKmGVc0ld\nFtbUgjcRtEledIT9Va2yXgOHgYdKROxzhq328vrvW2WOLfPwJwxKn/fX/rMD\nMXaRWFAh5f98Ly2MJfGAC6fbhzsUPDGzdeZTg+bII0WfphqcBRWMGGDJ4ueX\nleG1BbJ5T+2wWc0C9RnVU4Q2Pacyf9JgBTztIdgz9BtXjNDavHzdGOAeISfF\nkkxrgFfYBeMTyfo1Ygw7TywIqONe605iSfJ+L6OWluW8TpN5PVUUNnGL4IA+\nndG4q/8bo4ace4TO//9Kui3+9L8s3AT7eD8nEugEAy4YzjOqJF9zYekJDSCl\nYcPeefXN2ZUVdrDjdqph95QN0zrXW0gpxbWmqcF0MvC03cj5ppMt0mGazrME\nbmRYRjUffRAvuIi4aVCilBiGVCHgFEa3WpmJWirajaLcKaBapbljVA8NDAq4\nhTax\r\n=1W0x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.0", "jest-util": "^28.0.0-alpha.0", "@jest/types": "^28.0.0-alpha.0", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.0", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.0_1644517057346_0.06135315053049095", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "42ba7849a47db7824e0a625073887b94edbbb47f", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-LTEOtX929IqziiTp2uomXwTI505cQtSq6dYb7KgmUOCeLilKi5BJOB+mWmd2AQa2DNezdztt7kNEN9jkI5vQMg==", "signatures": [{"sig": "MEUCIQDW02HyAT8NFNKGW/kV2J9BLXBO3cLQEs+UNcBDgO4augIgYNlie6+xEoUYmoHMHKQ/08xQ5ueWiwZ/QBpxeLcuzK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqlCRA9TVsSAnZWagAAsfIP/jwPM8+CxtnA8zxGf4GF\nJ5kSYRU9adN5rweVpcqzypCrDHr1epC2q0x+R9uI8J1hYr/EEUyCMvdfCDCo\n0h3dlCzf+HFwMuKvHsuuE9NeGkNU/tLvAaXReMMj1GH3ivYV3vQB+15TNbse\nIa2sLyXYtMi+EnjeN+1uAVi10Ftdi3Y/aetrUnIPd29ofg5xF4BJKRDagl01\nHgnbrL1LIh8apUPvVONLcnV2gMW+iqR33MUtpQflAMppXJdsXI67gHjWRTZy\nqnJy5TVrHtUsJSUbC2p97cBJqNAR7qd+9tHWVkcWGKOHe0JHWYidQgnGXm84\nsCSDJ/MWk+qxt2iLS+X0dFn6d34+lJGZ6yKP9t+NuSR8jp7m5YVAwQOtXMQf\nvI+z1klUv92lUVxRrUnWm589Y9tvHuXE4vYAD0yozcixZB0DsA67bqaNGx6X\nZuHuAv26ge8xUL7zDJYYMmmL+VtX1lLg17/lYc9e9cjfK0p1TSktcICWVmcm\nfT+7uDzYDpliQWtk1D52A7gx5jAgol5J85Cf6g/kRfOc7t/njBvxodKluMqn\naHac4YL/jW6R1SwhFxzQ5jvtnKlFS/qzVA6JGJQUb2pvHv5UAJR3BbcZ0Usc\nG8UBgqQVeKpDZQnl8q1SgQw6TWvXwX7jMDQEPOh0kpe9Xseubt2lClmAHw6W\nmylf\r\n=HCEK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.1", "jest-util": "^28.0.0-alpha.1", "@jest/types": "^28.0.0-alpha.1", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.1", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.1_1644960421623_0.9564859258544449", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9b54a2ecb520d65a8de3057bbe546bc130c2bd1", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-JhQyVhpxrkRPy/HK0X9qQyR3peL3mzMqrsQMZ1C5gDF42OPevSKosvnZQCR3bH5/2UofYChmqPDTeklNQvJrdQ==", "signatures": [{"sig": "MEYCIQDCCuxID9ds1uU/h9yIn2SZKZhPV4c64wdzDxqoBrOCJAIhAJj3rwhg7+7iv/ez8EL3c3IcNNwr6R6ITPvBqdejyJS4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT57CRA9TVsSAnZWagAAmmEP/2fgE2ILO3mpfj8i6fg9\nfEHF9kR/EHJO9Xof7x3cUNcLmrispX8o5tKfETe7RC7iz3/TkOR5VggdPdCm\n1nIvrBJ/CjA8o4bi8JGfkCdv9HJiLZWtrU/DTrgxVLDEwZZGaU2Piho4bEr1\n2aQt02yu/sPshi021hxrX0k67D/L8kfLbPvjOuF2vxrDBRjbnBtI3vpnRkf/\nyI3Ue1zsWh8hjdtIt7NEQgAmPEncsHERPB8JmI/D3zisOH5oHDFhAiGv2x73\n1ePNhLH+RJbkphepiN6uMVuIGxi50pdmFhrlHRIXUrvztlm31syWoliXVpjT\n/keqAR5+Vy08iUMhLPBlAr3TwKZKH7Y6DqisUIcnBSnZOWfuguS/ZU1IIIUq\n+nKLd3woscZZfHh9qoB9sDEBZIrVl7g7P3mtRkawz3fU+Yz6QVPtIBz4Epta\n6WPep2qn54iHcUFai+4wnMgaeU1B14sdhgn88+fyyZ0PH8QMTzW+tX1w6qq/\nVuJ+oCa+6ynWdDpP0s505bnGu2IvLJGDu7cIYRCVf7ldYe8gcLGSSnNOj80+\nysW1/W9xj6kB5DpONOZSY/4a1miCuVdZCw5wLKzGUe8yAMCJiCqe4OHnjir6\ngJEDe7HRGshat2Tu4rufeVuG6gNYpKWNuNjsLZgCqWvVqDIloVT6FJd8AQZF\nTdUO\r\n=W6e3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.2", "jest-util": "^28.0.0-alpha.2", "@jest/types": "^28.0.0-alpha.2", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.2", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.2_1645035130894_0.4015543023830981", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cd4128469fbd722a9d16f237ae620b24169b04d4", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-gtrSsXnI/wwWBB7eV2VTmlwKhV/L4ijQQxf2/A0/gv24OfEp6qYtSOvyd6oGfUhwCP5MrWybkHIf9W9j7lps+g==", "signatures": [{"sig": "MEUCIFq9fQfPtoDW6dDMegy/eOJZESd3cs/YtatKSwgzW4EjAiEAu1c7195sLNZViaYXWR5hMHkwK9W8orR3lZshXdkw0rA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6Lw//WjNhv3JeRkolyp1RvQ1bt95XS6klOPX8o4jIM+CGt7z9QTlw\r\nmc8x0tdXdwb2urxYzPKbRB0gj3lbAbHYE1gjRqkJ72AWz2ayExkwHU8xzUS1\r\n5ta1wVuo17EuRQSHpiU5n9i1e3mxyeo+wocLEdek9SxUJgkVevp18b/FYBvH\r\n2QSOk6CMp6cgr1aTkNdetvUd+KXLiUcgw9618rvdc8fdeiB/f27Bac/AsgK+\r\nL2e0ismwErqo4at3obVYZu5omy1yuB2a6A6r+HSpXQio7yXK6+ouqrO7zlLL\r\n6vx4g259H5eAZO3fyceUFa55zwXUH+cYbeI7glENriUT1kkVfp8s6bLmtyJe\r\ncw7D+fMTbXLqHT9CPhgHdI44WQZDVDHMPcNLqO2P2infxcE1zi7mquxD8Yum\r\nygbLkvTrdJLyke66NiVbIgAtr92xmP0EDYE28GOu2mlk0pCGAZ9yG1e4zeQI\r\nN1KAuEQXiqcvp71SnQRhFbFlm6CXX9a4kRc39WhdfQGwtTgRIC6plQGUG4uo\r\nrlPYFJhtNNR/AyM1QwPDpWvhbJp9zsoi7U9ky8ezWdbiifqqLuabe59Kxt/V\r\nimkBbA/63GdTgTsZY4z4eVsftADYkmVtSz91qif4RrIQb31AnxC72CpFK7S/\r\ngOFhCcLS5I8NyllMP/GcDqFmtYLttCX+mew=\r\n=EUou\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.3", "jest-util": "^28.0.0-alpha.3", "@jest/types": "^28.0.0-alpha.3", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.3", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.3_1645112544687_0.0191512438757635", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41d7b16f97b3e5c4d2993c3a6c18f1deb77621db", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-Q95Y4AGxAGjkfSj+skaHJHpYI6I1EkIJgsc0jrst6MjbDwwishl2+7HW6oNn2cFQ3P9gMeZ0lbcxcUt1e8JfNg==", "signatures": [{"sig": "MEUCICmTxued3C22Xja16fV1OlrAu4iqRgaCBgHYreGz9FazAiEAxlj4NtYgWBxkJavgqDX8jAA5RFzXnadSKYUYq/ncA+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmom/g//a7JJbcRe6onZ43+THXt+alp6ghGODcKEinxBLTgUIv5torKB\r\nAMV0fKWqa25qZ7/ROQ39XlrAEsUBbXlobCGZrY09NfWeRUDZk2iSz7/z93R1\r\neyrw7X/Vym/rS3gReostr6s6K85NQB/Fjeg3tq6WudPS8cEJwHvFyN6AFJUN\r\n1RQw7Sz0qxNRmJ0uq9p8bLAkgSq9QyrEyzOjTFKeYirCi2QsX4I+OLrX056A\r\nxSuIOV2fj7Svvm9IiGzLeH5lNt9UGt+sOtFJGp1m2uSe7Q76WWoasDusJhT2\r\nIPHZaJ7kMMR6iDAxv3xVxDa8DWlAbElkL/S4ruQpqPo3FzWLw7aOvKZO3rLp\r\nGQqM8sk6v0BnXryY6UA3cO6BNaujrXeXeA1v4YinzdlVQaMP+S7B/X8guQIE\r\n8m3dKBZtuuOlmNJn1iG/seaquIr3p6WfFF9KzKvcM5iISdbT/CmCxXGVazV6\r\nS3tsbqLYYbtBcvdxGaxD+REtGOuKO0GDZjdWZeZcnsyfCtq8LCbVF/USGviA\r\n3HRXswXFplmMIWqVuND2kp4ZBRkJIrZrlCferkvBYWOxtur1YqIY9KyLN033\r\no+DVq0X+nUPfMSc50Fg6sbZc+eaAchqlmTco8GdPOt4iBzA2ygqP86JGUv3A\r\nJ0tZKRgL04mvXP7cjkZDw7sVaFQ+mFvx/q0=\r\n=ekUt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.4", "jest-util": "^28.0.0-alpha.4", "@jest/types": "^28.0.0-alpha.4", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.4", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.4_1645532036770_0.8394125911566108", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aaa42c419edbd3189916434da2e2a00195cd5b22", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-3N7csRmImQjeCCFQ9hpXWoaz3PNkOcuR7pHvitGuq9rJR9U80z1ON0Nt+mLRmoHc7ekw9+YNPtw2mLV32fyJMw==", "signatures": [{"sig": "MEQCIBRyzJI33PTbw3pdoqyKsjaldNNWRTaBQoW061Klb1/AAiAGDWxzqVpVKRwPMvBNn9QBzC4bJRJU0jSCCnsC+jqjag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT8g//eCruZOCECLBqXMp7fgqvQuqv4TvrDFp+/irUQM1U0FeGNp+9\r\nooX+x58Xp1UZ0Mh0vfu86zDo1qIp2ZxS8EJSWi7CNXDi6xepj0njKGpwjeHg\r\ndmYir12Db5DDN3zE58NhxHk6gFK54hwqYadyUoqxqa1NHdytmHvap0zUIaDo\r\n4J1d8WSz166Aq3WT4vUaom2/H+U0WSVCBpD9I/2JJUI7PFlZ8Nvjd9P27UP5\r\n1omfneUW/cBJtmimSh+RjMz3McCdGSyYaZ/6rXghLfABukn7NCP6Lkz1753Q\r\nd1wNuHZrSeiHR2mLPpHN8Sst9M82sFrtE4hBLzd5akFf8C/ETQJRDwv6xEvW\r\nKJrRV7MW3mPKMwMK6Q2zrZPU3hpy5ERpoVePcctMnKQYE4+/Fa4pEiYZeP1/\r\nEolJS2FM0U8/Iyayk/LwSRYY6YwMKLPwGc/bFCotsw3EAkXnA4mROhZPa5SZ\r\nIluRITIFv7a5ToXjri0ItEE1bbMmXBUZ0qqNaIqCFcQJkZN31j1Ws0Pa2UK8\r\nIt2kIxAlFpgAR1OysF6dXMUaECrf3rW1UfMmP90hdEnvCvHlxXNbB0XE34Vr\r\nk/EfxxfDr+o3OQ7NqHaTn/n2gN7XiwrKeiJiSvcaceiwyLK2ZFhtItoxcg9s\r\nHIsDQxu64IBWAVdtJipRGqpYeBdjrrd1rAU=\r\n=9kBi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.5", "jest-util": "^28.0.0-alpha.5", "@jest/types": "^28.0.0-alpha.5", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.5", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.5_1645736241941_0.040166198726242275", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "223d0be79d805111b0aa0eb437a96a3fa478f762", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-NkLQhURo4N0TWIf0mgps1PePljveLLBgiGYxzVV4GcCW+7T9pXXKxt2s7tYEf1oerinIOryqm4GcGXc44T2dCg==", "signatures": [{"sig": "MEQCIDFKpbiBxt+fg53G3Uhnfhphdru/9pvpe03ABiDTMXVpAiAK87DcFBvq+ypJFpzRd4o/hVrW4CxcYixAa4cGIUq89w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhlhAAoIlfPxjW5IpdgDvqEGe0VeAJOr0lK/jq596C1Ni2IINsKMHk\r\nbS+LOapSdQVqLfLGtZFf0R/DsHW+yYfaekblznlFOCkjUX4OQiscm/rkHb1K\r\nZRHtBMWrfqzk4LXOZGqMUr+d2McGEnr2g163p8GuD1/ool9MHC7Oqp794ty9\r\nEEVGNa4xcJ1M6j5F7KaNTIKWGo7/fwgGmffCdeOoqXpip/QH7EAwt2QcH08J\r\nwGzYXaS537Mtmdnx5cde9HcGZY0M53psQHSE3oN97ZABRvTWWeOIzqTxfqjE\r\n6x8xpOUrikJoh/gY0yDa48uMV71zOwvDXQtLn+aTPRljkvi8lIgR5Qss5GLY\r\nRaKQjfauNcwY0y+b3Qa8oCyVWr/mPEOMogn4lcWjKDpp4qOkH2hoFZX87rCl\r\n4uy71DSQzHHerVOl9WQA1GS0o1DIQ3/4ER52D/dNnCsZk2zZcGb4zB2+9zf0\r\naZq1xx0ehc6zpd2+op3lLMOY1BSBkXT589qh7gjaxn+Th8/W5bAh2QhKyz+1\r\nWRBlkpszUQ6MMt6sWP8SXwF824JFb3BzKiSU9SlEXvyUuUH5xeI+vpA5yP0+\r\n8zJb3NosiuLGwp8yLouUZ45fcu3yA7ePAeo5+cH+VH3pKkgYEHIaqi0LGtHZ\r\nmC9Us04XwtZIcA1iD4+8CGKslkN2ypJC9r4=\r\n=sSch\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.6", "jest-util": "^28.0.0-alpha.6", "@jest/types": "^28.0.0-alpha.6", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.6", "@sinonjs/fake-timers": "^9.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.6_1646123546395_0.12345648562127187", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5aed0047e316aa8720f06ffb8fdfdd71fbc02e3d", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-+/3HUxZrcegqB6m69AUcya+LZKldcnvPtsfYPx9QMx+MwTa5s7QOfFsinSMVtqmGjJffmk9PukpbMIj+6ek9Uw==", "signatures": [{"sig": "MEUCIQDHRL19LxP5dU2nXVisGYLgd6HpvZktaUyzTwjmeGkoEwIgchKSS1kFcHwq4B2jiIfQy1I8bJMnF9Mf/T6cQmD2yR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/ow/9HUVsY8E0a5ZHVuPJIWSVCHiBN/1v72WxnS/JoyeH8jm/iDPE\r\nHaUZksF/+nblJW7QI7lpuP0sE81nqFNtx1EzV+ZFDf5fgqeHEXKLy7BkfOEb\r\n/bxNxHIXUISov72olHjRmG9VvRsEa57lGKiVf+G9ea9byiLXm09sm6GkShbO\r\ncV9N4g8DiNRDs3ZhNAtO8IBjuY7EuBFY03al9t12b1A+DvUpqMoG5pNtLTAP\r\nVGY4bAEUAfCS2M/M/bkeOxUexy7GIBnpBCNhWP4J9T3rw+7ULQ3VxQaqMaSj\r\n9SNyzE5cF4hAhGZ+by1rI6GW4+tAeC5jQPHAlk047w8hdLkjSKiQ9LuP60z0\r\nPCle9rDWe9sd4XXrxzPhrJYatp72xbSsLKuhnf7RwVuwkIfjDPhP/6pK8VQp\r\n/jYYqHkuxM6TitEP/e0vgiF7uOrhOZqdnVtGS41owTjBe4z3sRoaQ3eexXqo\r\n8p7ej2gY0293VqF0EMljuF96XozOpPeKmzsWOc3OrL+QUM42uFNywnfrrslq\r\nWKrhDZAJl2ujEk4qG3bHuB9iugmzX1eTK+HWJtg++MDSVv/3pdBVzsVfd0f6\r\nRdV6Tk/AAkEg/dszy5RYVtIfvZL/VtxVZjnuc6ZePMV64V9iMK2Z7rkx/ZvN\r\nOeport9vAJhR0TDidRbbzF6ULHqEuIn/wWk=\r\n=Sm8W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.7", "jest-util": "^28.0.0-alpha.7", "@jest/types": "^28.0.0-alpha.7", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.7", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/sinonjs__fake-timers": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.7_1646560962475_0.8617395697652077", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "35aba8f852aad98b5e34d81f88ebfbd196b4ebf8", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.8.tgz", "fileCount": 6, "integrity": "sha512-CgUk005azXMhkD1Ttf5UUnbrN42dGtDyr+rbiLvtBFCmwos+MPkr3Be9z4MO7iV1Trfnx32+RrPi/duraFYOJA==", "signatures": [{"sig": "MEQCICuMTD0XbuKSPZGbVBU1uaujRdv1ouTYztLvInNtt9KWAiBoLZQs05w9qVgMgC9QoY2gjEmzu4Quj7/FvvDn0vuseA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwUA//UXDmVo5HreY0FoOiR21miT28eYc70RQNmBO38dM66Rr2Ilsm\r\nfDEHZ/ZUlakaERyu94bHpjrjB6+hUKWMFDuaZ+4Q2ThFqwd229somP79pvkL\r\ns5LiUtHxYBjifeQyNGB72vsxhhfP2G8nD1vyhoJeUDNHq1TO50vlmPNjhcZT\r\naN++kr/w95FP3eg+YR67TW3V3cpU+RkJl4F133qAuPHYFSpjsG6b+yVb/H+B\r\nzNpmtO8YeZgGZHJ9JcT39Ny35uqmWeJliR9Xn5KT+btwFGRyEGewP10H9W/S\r\nW3SztoaDVDceUItYi38FQDdoC7IUiziMTjda0lmA6odZFOP2GJZv2RF2FCCL\r\nl5m29h8d4/C6gYnH9oSpR7gar/HqxumI0VVclIW0TG0gcvkRc19mRapHXIm4\r\nXsf8Gz+1E+Yk6CFaX4X1e2v+ML1asBaFLIrNU0rQc6pErfyqkMKK1tnuUQI8\r\nkBWJLPVzgWYUOXM1j+tRh8ANtBlTeO6TEahkcY7a/EGre8rc2N4MDmZNwMHM\r\nH4UbJFJjjyZJADKIixCHCM9NDWuHlvym91F3IbBZLmBidC963dB4NIQCRKpO\r\nlZ1OKEbl82vAn3eoWzCg5xDb4G2eolGlI8v2RLbX2VihvqSVFW7zuo1a84pQ\r\nTPYhdczWYJZfJyhgk4NjXH9wD6VAw6Vg9Mc=\r\n=1hMw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0-alpha.8", "jest-util": "^28.0.0-alpha.8", "@jest/types": "^28.0.0-alpha.8", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.8", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.8", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.8_1649170794202_0.5402460569358998", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/fake-timers", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/fake-timers@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b0d1e8b2dd7434d8389a248d5ff90f24dcfb7e68", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-9VdchvUBd1xVlySPHrYfhBEzn8lnNR0elLfDISpPHtRdsGamw/kxKuuK4sbpQTvPGO6aPGbcCoixU0xyF/IfmQ==", "signatures": [{"sig": "MEYCIQCwwjvK0atLmspjbYBaoc6OA3m4mlJvt/7WndpT1ZlEDgIhAJgaLuoEOgDJMVE1YhqJdUkj4BbAkAgUG3645IHsDVmY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqL+hAAnKFpNmPJUqMHLBn63QOGdIEGPdqrWDxZ4Lav5uh3ZzFpmkRr\r\n3iHhQzwf84mxTPe8yNYpSKtU33Uev8vvsm6To7KN7PpUMLJVJfwSYqIyG/xj\r\nbSdN+x/243AkYip5Q4rI4PGNvz9cQ0npYDorPXuOWewqjudQK+IxB2iFWqVW\r\nTKI1RtFHmZIdfvqCjqr9SPbSispXvTFRojsH2xG6srbhl3FuinWi7Id4x8WC\r\n2J/yhaHhscGpRgBjQVrlmUfF37wpxtXzNdFaBUW408zm6RoFV2HN1sNu8LWW\r\ngI7khLjt1brHO5ZBhpwIBqh0+YS56IX08d1hPGGXc+TEyVyYSZwnUk0e1/wg\r\nTKT0Vm4hVgwxdqY9o5y1OU7NqbIwX3V5CFODq4tZudtsEENSLse0FA4FupgP\r\nrb8PXHxl2DY919voc+xpl1TFBkNZgqG6KCreuPNS0cYz1SvKNF3s2Vu8+s+E\r\nTtRxUcU1o1T48Ndam1EMX+I99/VXAGBwRQqvDV2dlOcKXyAa1V0gQmQYLAbc\r\no7C43MCUlTdFxyIL8mqJkPWnkjHuln5PumoefLIDydvs/WFko2b6TnS3gbrN\r\n0ykCzc6GDanESsEkQWHVcg2Gvd21f0UZlhHvdRvgRoXWMdigyGkpfgL1AceR\r\n7/Ns+KTaccEQalV68lDWTY/eweFpKcJX8A4=\r\n=SYgT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0-alpha.9", "jest-util": "^28.0.0-alpha.9", "@jest/types": "^28.0.0-alpha.9", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.9", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.9", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0-alpha.9_1650365956472_0.6659561373122886", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/fake-timers", "version": "28.0.0", "license": "MIT", "_id": "@jest/fake-timers@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a24631d79cb7ac73b7c40ce483f49899003edb5a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.0.tgz", "fileCount": 6, "integrity": "sha512-ph5LQ<PERSON>ueZ5d19fwENuI4+Sdl0VBfVBBL+8aCO6XBRvYQ9tQ5lEiaqOe4OorctRv9xwy6XXsYpV/cdK/4nkMRQ==", "signatures": [{"sig": "MEUCIBlLaS3SU4lS339mJK2d/yidFjmG3793/Vk2KqWWqtXuAiEA3m8X5aNCEI2VXOSosAjQPIIwuXOFlN5oFUZ4XFHW6l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25205, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozbRAAg4C2a8vihljqKjpNgVbG+WKG3gONAhkmASY+UfRxSknmSAQz\r\nRO/lW8MhhNX/+8JVJ/QSMqs++AqH0flZzX0Ts6oTcPDECYR+IQ3xzhWH7/ps\r\nUQnekovYBCHaPnTJcOyXP7+rhuRKpc9puBLmCF3YNhzPtf8K6OnK4wKcHTio\r\n+432fLAF/r7H8F43uIAo6kY+5JcBraV+Fw1pf8WZvoDyWGD/HQYeMKzhR8et\r\ndOcquwVr6OC7Vp3bCBQJCIDxJMGJqzUa6h4j/FpfKUVSC191NblCsd8vOLX2\r\n5PgaWN+uIdJr4/VW6jwyWc4CdAH6WK+vxoBI55gumbxhFTk+3dhpTCc3CwqS\r\nnTHheeiIrdj573db6bu3Z9OXJ6JwypeaJK8fZD26EWI1eGVwj1Z5cnCw0QWw\r\n4y6FlnlHHRPhiFGox9wy8a4XJD5KUBo6+gmVPYWgX5mWtG4Te4dnkeCYHD4E\r\nKp/RgzjUlPojD6e8Tl6/Lu0SeHIj1Olb7hx9/OCBXSX0q81IpkgVml6ZjSlI\r\nlFUfUl60sSG0cfDuWnLfDVCJ83ETCYwa8jc1xUXYkgq5ytlHJDP+qIzbpza1\r\nPVtWJhcrxDldzXMcOUpVo84LG5vc2KC+N0iFaT5D43k4UPMD58/Auucoek/V\r\nFqo31N0CIgF1yJsGVHIot+M2AImTYK4OS3g=\r\n=sTOe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0", "jest-util": "^28.0.0", "@jest/types": "^28.0.0", "@types/node": "*", "jest-message-util": "^28.0.0", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.0_1650888491932_0.5766150261972318", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/fake-timers", "version": "28.0.1", "license": "MIT", "_id": "@jest/fake-timers@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0a95401a06b28224475fe0a124835d6e689869b", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.1.tgz", "fileCount": 6, "integrity": "sha512-w7JleyVymoVWMvsnRRpM/ySM+K6qq+cLwK33VbFAghKTHp14oBiOio1Hh1egUyFdNybmKZxQvBBwB0M/48LgGQ==", "signatures": [{"sig": "MEUCIQDcn0muq3dGVjL+0AImvZKFeUeocmr+kZqYbe1iBXzbYwIgby2BBwXLAVW9WWtz8Kz2rH2o3Uj3WcRrHb8tB0u1K9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjXA/9GbdW8RrDsrK4htpZjJbTXKMqj7/tDxgNbMqv9+0/lGZdldQ6\r\nVTPe8EBFWuiiS63w1WqHIOcFAN/ZozJf2QyUIy9V/wmS6wNlB9YEAqA+CtlQ\r\nCHmqmINicqyPLO5Z1cdo10Svzixf16215wghfJaN8j9OsEhiQ0H79qheX/rI\r\n86OH6xXfmPw2kaB+DIt2URq9NdpEXOnuaxnOqMGIcyHT7q2y8QhmUKEHiyOg\r\nF/vcDGOiwD8UUqWYOERxycz3SU7/qa904mkHe4H80WeSgBllO9ucg6I7nEHx\r\n6SL4x45qxiFUVQeSQnInhUw+mDBCiIIqXSldzG3IuulS7eptIRz7DCOD6NDi\r\ngn8XBUN9ojZ6872ZRlcF4jM0rYPmQ0lre1QDqx24edejWDsCi7CK5M4DufHs\r\naOpTtwVfpqVrvtD1dEcf/zxoXmSaIxcC2VQSkTJx8CYtD212vPVf0L458Us9\r\nFM3/Cv8/FGq80DR++bC0RtCYdB+KOWuZIG/eFBp2tNPEL2jnw2zhKWbnyysX\r\n9iUKKz6vJpKnc3c0taeRTdKfist2o6F4cv8kqQfvUfGNDc+MQ3ZcghmBB+Z0\r\nMlf3H8RsLtj1DrZOUOaok3B+fHYETN/eJ061AESkGapfzI83OoCsums2pZEF\r\noXEPmr2mvSkZj+fSpuJK3bRAcY0i0WN9lU8=\r\n=pgLC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.1", "jest-util": "^28.0.1", "@jest/types": "^28.0.1", "@types/node": "*", "jest-message-util": "^28.0.1", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.1_1650967361380_0.3992975357181223", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/fake-timers", "version": "28.0.2", "license": "MIT", "_id": "@jest/fake-timers@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d36e62bc58f39d65ea6adac1ff7749e63aff05f3", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.0.2.tgz", "fileCount": 6, "integrity": "sha512-R75yUv+WeybPa4ZVhX9C+8XN0TKjUoceUX+/QEaDVQGxZZOK50eD74cs7iMDTtpodh00d8iLlc9197vgF6oZjA==", "signatures": [{"sig": "MEYCIQD2aMmM2VmVtddYL+2kcdHc1R3cpVR0GeQuY/23KgLJHwIhAO90kLCT76sL0n3Tr4NIDtsxzxjaxabXMk8BvrHC9bY4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPREACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKiA/6A2/8NFOToL4NpGypqNmn72VA88mM7ypKE+a2hc/CpyfxNSGJ\r\nrS9DvazP4vqcO4s8+E7drHfaVKdyA+iwM+ESIRkqNziVWBBiKje8aAah1rUN\r\nQzd7uESH1C3LoAgBTP4qlGFYiJjWiB7SoZINgun/B/S5qeUUJcRvR1NIsu+k\r\nvAUgYETZQLnvBGXnaetlCS9icbCq0lZ5mWiqy+Wk2mfMS7XgZaprVu2Rhut3\r\nWh1FyMeRgviXzUoworuqoseEVPJLy+5k65LKS/ympGTKLbhJK8UlhHuBuVqm\r\nQChlegki1kMD424W309wkk2F1I3oivlzVKnIUY6EM2IAcRtexu2g0/LodaLy\r\nGae/LD/x65iokAUWyJ2OufFaNrd4RJft389iZOmN+7hqQvd0OiTAnErg7lOZ\r\nI1kmf/1hiW4MKBSk96iSqk3gC60gJD8yWWcyM07ty72naPsAjeHjGq1ElB8q\r\n9LSdIAcKfaRoGN4LZ8+e/i7f8bWkb2QjAxzxlfUEdS0ASNLjCaSI5oIztHlq\r\np2iZzUqUCDOyKz1hp8v1HE2rB132y5YboO8A8x+dJE1IjD9OlUzSofu5gRac\r\ngrXOwnQj3m0hn0X2er9wun2GATtKaMFEFzZJQLfFkdQQQQ3dmOxImdPmFAGO\r\nOZIv/STNYZFOS9K5xP9FT/p5LviyevQ42n8=\r\n=4pTN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-mock": "^28.0.2", "jest-util": "^28.0.2", "@jest/types": "^28.0.2", "@types/node": "*", "jest-message-util": "^28.0.2", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.0.2_1651045444818_0.9226927015790658", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/fake-timers", "version": "28.1.0", "license": "MIT", "_id": "@jest/fake-timers@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ea77878aabd5c5d50e1fc53e76d3226101e33064", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.1.0.tgz", "fileCount": 6, "integrity": "sha512-Xqsf/6VLeAAq78+GNPzI7FZQRf5cCHj1qgQxCjws9n8rKw8r1UYoeaALwBvyuzOkpU3c1I6emeMySPa96rxtIg==", "signatures": [{"sig": "MEYCIQCha1oetbC67ZDHxtmjdEymxNI5MM0/NSio/dlV62YoTgIhAJfXVKsIukrWbT6Gg3p6lFDiFUDKMQv7Bh8MaPGMDpeb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPpQ/+PHJzyEiZ/pHPbw3rrHXUAXXD2lFffSndjzUbaI2qEwgY2jAB\r\nQ+Td1HNW9cgUbKHzPNZIM8zUHOq5zNvc0V9xd4oh7wru2NWKmHl3oWGZls/l\r\noW0OLb4TggOedXqKMpFoqsq9CARj2Kh7gQ29lI4yNJsIEfQz+Orc3GG66Ah/\r\niv5/ERL81std7a5/mdRZuXFCHG0KDcEDEiEqMazHApQSxB5WyXd9AyoH54TY\r\nShJA6Ev79qJ+eVrfwGDae5ieGCQAooy9ittZ6TdHvCIzFYjghJLPjXb/acxA\r\nx4V+dU6foHJHtM2zkI0NtCL+9WJFVVB83DZhhA3uECe3fyxoUtovEndSjk9W\r\nGNePU8+jAFLR+kbl/REJXFTegX21tYOM/JG9mz6of2pWRb9qjj0T4QMemC8I\r\nAO0SS2h54VU85Oc5kIVBsvl25IafmU/onwew7g4MKpstdSmM1aV3ir8WMjOy\r\nN0blLZMFKs1AQka7IJhZYpA9X8zeSOZwAXdbJS2STQGWIClD/RBXWcpikCzb\r\n45aF4aPdh3iJaTIZCodmxs9j7Tn14ydAYqjjQCsuWTY+CBSq1SSFFQNNQnjr\r\nujL0VZ9btrycqdYsxWeRs4OGmr9BMxtPscA7atMielulp2pPrYDKseOXpcnE\r\nYFxxwVnmw+wNfFR5s+37zj6jWurwJBpS8dM=\r\n=Vt2V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-mock": "^28.1.0", "jest-util": "^28.1.0", "@jest/types": "^28.1.0", "@types/node": "*", "jest-message-util": "^28.1.0", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.1.0_1651834135812_0.2830197729250763", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/fake-timers", "version": "28.1.1", "license": "MIT", "_id": "@jest/fake-timers@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "47ce33296ab9d680c76076d51ddbe65ceb3337f1", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.1.1.tgz", "fileCount": 6, "integrity": "sha512-BY/3+TyLs5+q87rGWrGUY5f8e8uC3LsVHS9Diz8+FV3ARXL4sNnkLlIB8dvDvRrp+LUCGM+DLqlsYubizGUjIA==", "signatures": [{"sig": "MEUCIQDje7llO/aH9RMG8gtAYMUsw63EfqDOqlOjJHMx8QA/wwIgVlhg1LaNux9lqILXh2SjHl9m8ZXJt0yuiVkj3zejBqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuuiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqw8BAAg+FPC1iD1ULPQrjsSz+BFPoJrJYAmXohHTdYsvD+W7neCcm5\r\no8qlsqXaxqeGTW2BKA8GfewYpv/THHQOuT2AmGYBcdcRhu4tSxgHk+M+5kpG\r\nS0T4spvQ3lbF0Js66T9xDdiVKBQ29wd9Sw7UUuhLJKGDagZ9D9c1LazDr831\r\nCkd/VaGiXRlKkujZXfYhd5PlhSy9NSp/BXnAyoFySLl1+ur6kO/8BITnWdJf\r\nlcHz6+VwpE5JL7zDlVn6IwKajE14JxZjf+g+enzIEIZfyDwQgbUs82DziZkL\r\n0UHhId8mdzPEzlSul3Y1BmFbDRbv0yZqkt/NwfY2ljL7YB9FkF6XcMAyDKV7\r\nGi4E+xNkhdJxLTNgLIWbvjZb6E8koo4pK+daYQhyJBzhUwcAtlZNoEycoxiu\r\nFtZY5tEKG4oYrSVrNG+wFe17Oy889QiEiPTYxP5s/F4nvefpT6jKp6IANVWH\r\nJ6x2E3mQMPwDSnq808Q68/fiWzYHfgJlAg6Qmk5X/vEqHJJsJGRxjpM1QLju\r\n2kx2QBVhmeiprmixiMUhvdK38pYFjXDH0qLF2xZEBKkjvhOHp9Muk6zIimjj\r\negetAZQYulf4aPt/j7KtBkUarHUWyQi+WXQpvsgDWcciHl2JnBEztCSMJT7Z\r\n3lWl62a5unYR2/n3T2qcicIVwaPObLcslKA=\r\n=hKKB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^28.1.1", "jest-util": "^28.1.1", "@jest/types": "^28.1.1", "@types/node": "*", "jest-message-util": "^28.1.1", "@sinonjs/fake-timers": "^9.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.1.1_1654582178096_0.6278015072332397", "host": "s3://npm-registry-packages"}}, "28.1.2": {"name": "@jest/fake-timers", "version": "28.1.2", "license": "MIT", "_id": "@jest/fake-timers@28.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d49e8ee4e02ba85a6e844a52a5e7c59c23e3b76f", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.1.2.tgz", "fileCount": 6, "integrity": "sha512-xSYEI7Y0D5FbZN2LsCUj/EKRR1zfQYmGuAUVh6xTqhx7V5JhjgMcK5Pa0iR6WIk0GXiHDe0Ke4A+yERKE9saqg==", "signatures": [{"sig": "MEUCIQCrOYXWfTw2+grt+px3TvFmM8k9WW+fOycoGWxQ2+Y72AIgOlWW601kGZJuaVqSz1tYOCm9Ak7CqX7ICBQgDBtf5+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivCqUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkdg//RRMhUzotlHw/Xdylx8tN7OqnvK1XQc8jUsf2e3V0hfpt/TF6\r\nM6amvlxakuZb6S7nm8yB9XetbPJt0TY2tyoW18ZbnQ9gXVXKRyONrPkro4hj\r\nWnvFH93OMTFEhIMTJgbUpLu2omTvKyvilPsSklhPXjDWCLm1xJdxGs7UfLC7\r\nFHFmpgKm1MyyqNT3Ftxit+65ZAngXzjckb35NxVXWZJv4hLkwm1TeIvbSp6D\r\nNpCDBuaODY1i6zAsI6BqaU9IghauS94oUUEwJ/VfOjXWdFvM9b4FrpbjI38t\r\nXH/HYkeqewawHOiQxoaCQOfZekeQw/AFzjXZCZVpSuHxC4Nhf7A2avN+KSwE\r\nIyi92L4W1Uhb1gcBAwA/hGHpLEua2+v+lJKa5/QOnZfKFkanU14khyTypuat\r\nYMK6QXymMz7bNkL9si6aD5ngSVwOLqqivzX83XnCcLHpSfgME3G62UqQNvb5\r\nt55RosUcDgZmHmzSe1oMp/0Gqm3ECsEZWeeKDh77/aIqOFIjkqmqoboxBFDh\r\n6jJ753IxqCoBONs0rweppfd22DxYYanbJqg7ijNZOa4Hrmmg13INp0eoS6Qc\r\nQw26/wZAmMDCx5JBSYAjs6+rKNze+8L0DSQGZqhQVmlOgxYSaN/l/m7csvW/\r\n4gUujnwNowYHllvnUztg9rx8eqMKNhWrrUo=\r\n=+rsd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "789965efec4253fc54ceb3539711b3a3a6604d94", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^28.1.1", "jest-util": "^28.1.1", "@jest/types": "^28.1.1", "@types/node": "*", "jest-message-util": "^28.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.1.2_1656498836778_0.9944230802914515", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/fake-timers", "version": "28.1.3", "license": "MIT", "_id": "@jest/fake-timers@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "230255b3ad0a3d4978f1d06f70685baea91c640e", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.1.3.tgz", "fileCount": 6, "integrity": "sha512-D/wOkL2POHv52h+ok5Oj/1gOG9HSywdoPtFsRCUmlCILXNn5eIWmcnd3DIiWlJnpGvQtmajqBP95Ei0EimxfLw==", "signatures": [{"sig": "MEYCIQDpz2rVIwf1x1yJyDCqKqxrqC6xwHBXiiD+TDZhHAbrUAIhAN93oX6aKrTPmaPRbQyrUbOczKM7CAlaLzJlWLVhr5dq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqcww/+Mvd89+8iidrApklkk+/1FyGgVEj8ZUaX9vQp3ZFR3d3zS9dE\r\ns28UHjk7b6syYHuF8RJwT3MpLKJ6HsOuquelTJfU8aPGMryremblUHERL+nl\r\nmWL8nXfzC1hnjYLCi8T9dFLmNeN9UWBMMWeXR3cJE6IJ470lVI9fPtpDR7al\r\n8tUI+06rlo2abxt9oQckmnbd/ctDHJPk0PeBzVHxLoPXPBi9MAjZlmNa8b2Q\r\ndv+wlBzXcMpOE/4qCLvG6D1pf6oa3h0q+x/pEKNdrPM4qn+AKZrFZJYUxPQ2\r\nUddRK9k4aeK6K8/ixN87pg6rNMYXMclLtAPBYK6gL57/v6ygS+VeBl31ttVm\r\nPFtzVfovW68m67m7paQQmB2JD1gdGw3SenZpkB/ZWPdZfCjsRISUB2UaLnOg\r\nAp1p5dZz+a9decWL7S7BaRfb9Nb70ZCS+F/mv0Qi4n/SkxW0yhn3MkB6W4N4\r\njC9DavR7zxTu4LAMx3scdN3u+fhe20lhsH9YPSCo8YLYzrWtNHSM7pA6o6a6\r\nTCnBk8MKBBb7pC2BXxmfcbRzCCL4wcincOUzoDEYDOockpnwSCBKNDdGMoGD\r\n21BV8MbINxI0FaiGqLuF6MQXyphBouMmwoey+IIrwKb70mCcIFyZE8hkcxjh\r\ncRFUNF7O0kkISxBw0xd+4EiE9yPkhf50elk=\r\n=NTA3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^28.1.3", "jest-util": "^28.1.3", "@jest/types": "^28.1.3", "@types/node": "*", "jest-message-util": "^28.1.3", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_28.1.3_1657721550246_0.389576299627014", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/fake-timers", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/fake-timers@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a12cafa1cb8474a406489173b416bf8a9b821a34", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-G1mcPHHiWdPKpSZci//N2OTdTCijLdVp2QELJPk3e2mNdiFn88orEkftF78EVvQ/fcGa8LKPJ1owYnDYVJiLUQ==", "signatures": [{"sig": "MEQCIErpbqF3yyDTSs6PZomrlV8BBxLk3BY3UgpWFQjD0z+0AiAwQmn4EGbOilhdYoBiL2qVkUDdJXhR/GuGzASkoprcRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpeQ//Qm8iETpAm2z+uFRb8yVi51q04eeN9ughnvGjmhUE0wNA4Ofq\r\nOe+6uKARvPg+a3TktfrMn9U8lDSJRHGCJ/62vdEIyhlbn0BXXgY8Mjlb9xEZ\r\nIHftVZDLBFYo3KefNdeQGSgEfXySWVNdx7zROqD+yE9Ggwm8gXdUnrMbPIvm\r\nZUgvDH33pOYQVwgnGMbPHlrEm/E4L4keWdTndZ0/ym9p2zSnqes6oLT5WjwX\r\nPQ/4eOpiA5cgmWLvuwbCF36ajalXS/V83XvFoVU7auwg2DTGMX4vR9SvYIcc\r\nSGWBL8TfUgDfoHImf0aTkMDnQwqElwkB0BQ9hWMefv1XhpaiFLf55h1Vt6qt\r\nvplp9D9lyxf31u5BGqkbiVG69jNGbQHFgD86tOfg3gvbY6FltcqHyExjNOxb\r\npFQY2CJccLB1tNjG2QmJ8IqHLU6+dHVY79ZdMo9AmCoO0AaLzRfUSEHyGnxF\r\nrNgt5hRDF3nrV73wAypKcj9WJ+YXBcNL3lcfzD2oCM8lWv60dwIwOxFkubGJ\r\n4chQZ944XsnftqJoWbS1lUv1a5Z6GkVBA2Lhb7n6ENvWgjHz2io04w1yLONa\r\ng1PKVmLSon6Z60+R9sFf/Zn31aoHK9WOSRS0Dh3ieNP1hmFJOGM1757F2OPE\r\nqyC+1VVHD2ECC5KDqaHE9/dWpT7L3v7R+v4=\r\n=EEYS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.0", "jest-util": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.0", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.0-alpha.0_1658095629208_0.4065706508445326", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/fake-timers", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/fake-timers@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1a5e8454e853411f9c970e06ea6c3fa0154d2f90", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-87xxbpSC0T+5fqxt2Wpzd9eEKUBmZBSUCSmoPG49jPudi6z0IhUithjJv3GMogu1o2AKctpb37BjmrhTTZ46zw==", "signatures": [{"sig": "MEYCIQCVaR3GEuJaTSeaK0NaAmnhq+5rRGbHp6/LPG0VTRiGNwIhAOuP8lTSX5qRhCI9btdHzpvay/nJyyAdTEco8Xmib85t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV3w/8DDRr1jC4IciRHt3WcMW0epRWPiC438N+F7bWfL6zPw0pne1N\r\nLDmdceWYGVKvkJTEO9seGFwonZqCd4IsDd+t4IkAkpS9Y++yYhgeGu2WFN76\r\np91AgH+5vWFIo8KpQVeiGNhpMOXOWWaaqP7YKJqyQsqM6xegGquYg8KaDC+p\r\nqoOZtBBgfkaM0t9K3M7pHRM5fdC2cKy9Em2dEIGEPMJoKHfUHBCSyh2u4DOY\r\nHAkANf9L4m8Oh7bDapXy/5N/DEMK8Au44+Uq+i86hrR0BDeCqHcAqrDsJU5W\r\nHIwXnJdzPiirpIt5J4tIxN7IIVh8AJck0Ea4Jd+fSR+GhIL2p40K8nd0Duaj\r\nIQtA/gP9FQauDuXTftzFba31yye2H5pE54A5DggFtnjNxYn8XK8wZR+cDi5n\r\ncLPEgdYFJT3n4zZ6vYf0oGA2FI7LW9FriQw8Or08mWIr+sh3EydP1UTZQJq7\r\neq/j+qkMmqnBqenasMxM+sGzeUXNWh3K/RiP4X214EPdVzpNgM+yCl0MF9s8\r\n3QJgMbmW0xYnyIBCmyy1nd8PVf6utbSNBTKN2MgerbJhNdWzDJnTXXotzWhK\r\nnYKPd80zf2S0OSm+Ev8Xae0UDM+Me8NS4TXQdQBNJcgghNFd4MowCTodhcDj\r\nwAz1nzZmmLXtQdVH3rWFyinRAcrbtc3KeCs=\r\n=+SU8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.0", "jest-util": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.0-alpha.1_1659601411297_0.769175580775856", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/fake-timers", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/fake-timers@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "01024e16c7abbacf2505779134c2b922b3a6e404", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-2itbsEecXeypmY+ZRxMgRSFMld3E3WvKnSJsLHOAg5IQlbrzfbMOlEqrgv4oVgbsJoQuXiUjPOJG2JQoMg4szw==", "signatures": [{"sig": "MEQCIGg705ymIJKTJs+TPZX++kmPpXtR8mMTDtdrhSVTL1aQAiA/ABoRzZ+EkewRvYoDV/rR4yOzrhyahSXwDrnmrgKh9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ERACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaqQ/9HKsDwYYcWDjoa3fy8q0Kly9Jxe3m0WIIX3OEOHsSHpT9eaJX\r\nsdyGZwbLJTHINgdopm/7odLEMRMFhoE0/6i72fxuXoeFmCL8RM2DbliC5V4x\r\npmK2ylZSMssTwZDhFJkRb82ci+Q5bKXotIsvgI4n7zJR4Z8pLleNgq7H0igl\r\ndLpMC2/e5xUMTk+Il3N8x8eWSZ3VkYHKgVEPxH3QqxQ/qOtsjgU0fQznHnvp\r\nSL4qtYnBXEegR/Ofs80xXg4at1Zoc1eDfK+pP9WXsssap4YrxQn1RLV4b4+9\r\nWavMEF7kPKutJG8p2bYB1UBgZNNYhEUOqD+LUei9yUSreLtQ5AZ0PqzzLzbA\r\nebRxZvhhU9UmdzGVbFqjo1+t7B0vzPeZbp2lur0rCfuepxcK1zFOqY8qsbGz\r\n64FmQVwHsxZz6UnFNkOqAAPnDO6GEUtR987ZwO0UY7IHCTdVjUt/5zEg1eLu\r\nyloKkNyq6o9LO4B+6jePNop2AkUf0Hw8tFihEu6dLnGS3m0/IzqlX1xCV0HX\r\nlu7USw2rv7pPP7ZetctZQN4WYlhatzUSfN5HYpgNhzIGz0XvAcqgK4GxpXWD\r\nz2F6EDvIb40yHPQ8q0Mp6CG2DNTIBZKfFyObkgkCKGcKyOA5C8qTpOpjgrlI\r\ns/8jcPIEEyBLmt7bGwMNX1akfKQaT2mJGHY=\r\n=h48m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.3", "jest-util": "^29.0.0-alpha.3", "@jest/types": "^29.0.0-alpha.3", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.3", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.0-alpha.3_1659879697390_0.44954781797784427", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/fake-timers", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/fake-timers@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a30f3484a28b5f70d30df3e0e66e74e2e8259457", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-eiOfl5ZIfXxFoOYAeaQwpFf648vnD/Imw7u+I2WoA/ujIDajrogzuvwbCMmKmnh+bSLuUrFHcWJ18KWqRkYR2g==", "signatures": [{"sig": "MEQCIHSfPr9x+sENs6Cclq819l/+sQufeDWVzBq8KE7bkboqAiAyZ6vgr+/YSv37v/f5No2B/0e/5kPjyOEgcnjNBLGuog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QogACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZkQ//Xzzy3NpdnPVfiJxbqA/LRsGgvXivmAZkOdey0IuGXP2gdu2g\r\n/VswpLyYQSbTzRl31S411giZ7pJieeURaaNgJ9E0m2ifa7yR/2ih3fDaOMpp\r\nl9vOsC3RUVkq+5pFuvAVSHLUc94YZRkAji+/i7Toinp9dgqChVf4nqQLNTUs\r\nbMn6BjJQZJGrTn6Fp8MABQLSaiNs325rWgp88yx6NBATCwBZa8L5zIkP4CjE\r\nirR9EeIK0Jst0rA5WM5R0+fqnmXvseejeR/j6zUCDzkYSjFKumgif2cJfFFd\r\nKLeqchknvPzqc/PPLqfrVL3mZWH0JurorLq5cwXs+U+ssKuS+Mj0+DS1rUdw\r\ncpzltqPW9+fxL3L7b/hXizML7IwYMUR7vqyI7C0zD4A5CmnGBKyN1nnR/aKm\r\nvskZNVOC4HkqAYzUB7IFc1CYfnUy6awQ5vKTcPrPFASEH9tHyuT5m213AjuX\r\nQZKsa/tZPz/f08WbxiFGTmJWL80zvGNxLK4drx18fweIQpsEQ5bbsQfYHKRI\r\nw4QqibydICuhwHY+uSqxTj15Gh5uVOc+nYuTRlVZjcd5fLwjblPCCcxHmA5z\r\nBahRsoZYPzmt0DzsYnohN4RfhEGL21j0FUiu5PYJEI0PbiR2B7tTbHTee7ay\r\nKufeZHh84MFeIiD41osJL+MnLVac55LwLds=\r\n=2Ku2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.4", "jest-util": "^29.0.0-alpha.4", "@jest/types": "^29.0.0-alpha.4", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.4", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.4", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.0-alpha.4_1659963935891_0.7328541697729876", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/fake-timers", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/fake-timers@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "058388f10d81681cac04d0434dcbc3646eec3a42", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.0-alpha.6.tgz", "fileCount": 8, "integrity": "sha512-BZiw/A6DHR75103OS+Zbyakw2Y0Pi7CiR0pwO0CjMsFZtpOeAZ/+9DdWrCImt73cs0C9wQvYQF8a8fzuzCrOPg==", "signatures": [{"sig": "MEUCIQCeqgAgeunXjv50l84ZObTetw94RcuiQyfLuIpQGh6FKAIgN1gbuoxmT8VcNwewgsAhV/vCMk4eUingU/kvZlTo+l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5beACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH7w//U+pMWXzVRnVgOmb4cyn7vTcpfoJjuu+bqV3hRzR6/0XrOPFH\r\nclM0wjm67es65MGls+ZlMQ4uO3DcOB0uSOxRXbxN+AtcBmyhFu2LAAIJRTKc\r\nyYDROKUdV+GrbcHguM1n1HBXpPveoDZTwjJ/BJA08K1g7Dm5tScGVgAasCZZ\r\nNt+6Axpoxm/kR9i/HVls99dyblzfkSmxu9g4Q1X1Z1SeYME1+quuoQYjfZO3\r\nxH6o7R1C38hG27JoPt94dz7ggFkL+kZlmoET8nyoUmKw+qXpn/NDZBAZW4fy\r\n5bcXCD/UCfCoOn2Kc91JsLpMPxdAQe69KrI76Fxi1xhGXLqaVNmDcbU4CDKL\r\nnfti+1OW1p9saDn+UQU89XYd/JS16v4mVE4bEqLqa1hvi+Y07619LhjIPBsZ\r\nWJ3Zqv36fgd8FcMgcoVaWyd3hciMWCbgRxZscAomtCn5W2/RR69A9YoNFj74\r\nIKdMC1DtV1G5KqY341nj20xPwwsaFsOEzWpSMdT4mPTUN57iKo5wCj+gPUhx\r\nUxbcQWC5054+5HwrLePAvx7scylViklOms7B9oRGTRLjpWbjbWE9hnu1Lyyy\r\n2mc28dtS4Hr0+evnLMy240ylJD7Mq7VNtdOG7UwM3cCuV7Q4WTy39hJy2q3v\r\nses9hv0H53pKVYI9615FHLS9xZxQ3ajrzP0=\r\n=fpIv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.6", "jest-util": "^29.0.0-alpha.6", "@jest/types": "^29.0.0-alpha.6", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.6", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.6", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.0-alpha.6_1660917470695_0.4055219623546613", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/fake-timers", "version": "29.0.0", "license": "MIT", "_id": "@jest/fake-timers@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6f9a3a6318af5fd6cbac52e20aa868f5643aba23", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.0.tgz", "fileCount": 8, "integrity": "sha512-4tqH5fT9H0+Ms3Z1HLZ/JfpzJluep2Zo3uuj0KPyu6IIyYSHCDfkXuiBQNWUGvumZDLQ2Si03cC7Gq0r73VgVg==", "signatures": [{"sig": "MEQCIFEMy0/32laT14Zf1mdjdTSo3YEP7Smr7vdt8sBd+/paAiBYBZ3L5lXbUAywEwBNFT1diIOgUlRRr8/SDhrzPhyWMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrdkg/9HABERaAxLz5EyWMAkQIXInoRKK8Ja2gK0HyfQ0+YdLBv6FlF\r\nsQxC6ddTDTJTUn3DLsEBGT7XIUq79T53kU9CebUH7yCjHvlYxy2iHkFhK0q6\r\nI+otV0ga5Gvwf+sfi/IdWQC5pk4RYQSPQIRZL8c8MlWBb/6vVVVNBU+7JUPi\r\n0FcKZ3qUZABQJhSH+e7lY+4Y7Xc4+kM8m9CwztFr5RefQmkUxfN7h4viO4kk\r\nyZjpiT6JSDhkZVOiwsnRt5G9bdlfm1sWcWazpRqvgH7pWR3QPzouj48uu9/O\r\nPTjZU9z2NnbdQ1K9FGTkcgdUAcLtXPXXEIN2DGP/Qo18cuYB9Kx/OepMcecH\r\n46X/yzMZ+W2x79d0beS5SGpnpCXNQxFp9HMIS0z6GySH34blJ0R5X3WvIbI0\r\n8xhk3EY0GwCblaEmCHtOZQtx8NOQ8w9j7cKId8flNc7pzymCEHKQgUm1Brd1\r\nHnjGbwrrvvtAucH5upwRzyEEs6bCmpvnkhUOWqyhLzR+aMgMK8MfYX0OP3xW\r\n1GCN9bF7PDrtHU6UDnRZlQYXqEoi5fHU6KZFR2uS60q635PdtrFYA01Rt3XQ\r\neD58tJZy6kr4D86byMSaAxILF1Xqy+/aGsyeSDXzC91WY/ayce1LWzK5IQa6\r\nEfQZV54gLEU+/LED4lREuJjdMbRvVoaMuKc=\r\n=78Xi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.0", "jest-util": "^29.0.0", "@jest/types": "^29.0.0", "@types/node": "*", "jest-message-util": "^29.0.0", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.0_1661430809442_0.749945231103103", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/fake-timers", "version": "29.0.1", "license": "MIT", "_id": "@jest/fake-timers@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "51ba7a82431db479d4b828576c139c4c0dc5e409", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.1.tgz", "fileCount": 6, "integrity": "sha512-XZ+kAhLChVQ+KJNa5034p7O1Mz3vtWrelxDcMoxhZkgqmWDaEQAW9qJeutaeCfPvwaEwKYVyKDYfWpcyT8RiMw==", "signatures": [{"sig": "MEUCIQDMxS+ee/cs8a7O6AVsuNrx9i2aT4/ekpQ0BhpMhsvJHgIgWsC4eHlfssDhPQQ0/7uZTvCvvyWWwjbx9NbHFQE7YEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMvzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWLQ//TAt7sZ2lENfOeUjh9rMVLIP2E6+19hn3DWVvRkMrMRCKNt14\r\njKAlL800xEo5UFAbmUkWrg7wFkhzerC0Wk6jwyPm/wPMO9wimqbddPFc/E1C\r\niAuYdTMT6zkQY8HlY0GUDMKmGkavG6p4+YLV60HIVhFNtqsPWGOioBt1CZBG\r\nbNIGboRmvvRM0yL6Qj4pXN2JrCPjpnhvFQ9Ixz5gmAFQz3t28Af5d6smRxHO\r\nMkm6ssYqEYvtYmeyH5vWGRg6LPZAaUbb4qVIuO3Oa7loONmsvcxksQhkZ6vO\r\nFKF/IghDhgZ73ROpKttDTvUL5Y23wLcPtULdfAKGicPr4VMzD/jejzBX7CJZ\r\n4lQmqsC4b0Dl0tNPilxr03hzvOARBOrE6HymM/1dcu5wt3KCJmStPsgy0Rzk\r\nn6xisoWSfbnVdoFLz5Wy+tREHtivd++lTDk5TCtPpUn1gepebLaZpIX6D1/S\r\nH6srbIVwkH0TKH0SShmlPj17WKtxuuDnUZ+xpNpzBslqWda5Top0D1qCz0fm\r\naqI5kM8IOMS/9GU1CmR7oExZkKm9tQD6Rs0kp4+3Rnn0GvWShc9KGPsuD30a\r\nF0HVhMzvNV3JOKd5d6SLXo0eNeqptM8099ppVxNhDs71s+seT4SzDZTMoUvA\r\n7DOyuKI12uY5dQ1ht3RF2BPQwrCSPBDc5uQ=\r\n=EvsZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.1", "jest-util": "^29.0.1", "@jest/types": "^29.0.1", "@types/node": "*", "jest-message-util": "^29.0.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.1_1661520883027_0.27259432588978827", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/fake-timers", "version": "29.0.2", "license": "MIT", "_id": "@jest/fake-timers@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6f15f4d8eb1089d445e3f73473ddc434faa2f798", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.2.tgz", "fileCount": 6, "integrity": "sha512-2JhQeWU28fvmM5r33lxg6BxxkTKaVXs6KMaJ6eXSM8ml/MaWkt2BvbIO8G9KWAJFMdBXWbn+2h9OK1/s5urKZA==", "signatures": [{"sig": "MEYCIQCG+xKG4Tb3qaBekSzr8wgf3RxsbcMOwr98jTosOoZ0OQIhAI/9UygqHa+lZ/nr3JPBqTPzIcgyCLvR7PbwzYXuMD8j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNcw/9HYnhLuyGw/x7WC1DQAO3wvfcwmyrIvhuuGWkqLu6mL0r4ESR\r\n8he4CSFrLucLIs0dUTuZ50KQRMRKDChLzwRECRYMXDJkA9Q6LzZQ3xxFGfe4\r\n6npwEbs9mfRyx+q33JJ2wN+C1KKTJ0ZKEFxVImbkGwtDoGypZGNP8JsaE610\r\n0AgknRLVQXgWVx75QK+MG6THbsyogNSssN6QdOWp88g9xT9I8K266Y0gKccD\r\nOVXpmIUGYJ6QLJZs/xLt5+hCTBIxDeXZ3gpNkrNC8gAo3Kq5FoRQjkwSoqDe\r\nXp9ByJ/4+3PSjrmp8bZ7dPU2HNdG3VZSl1OG9BHlLFXH2XH3ZFX+iP4sqZhl\r\nsky81ufFLEE3Ks/mzvCdQuiCXbpp1/GNv+CXkpAzpv9oGf7Fb1dhMjM6Frz1\r\naYr/ePrPRTJIavMhiZL+4ZNlK2v/7tpl91rlqvod6V/VicCDyHEtHoEmAgpV\r\n8NWfEKQp9YoPGIH1ASvKmvgpKxkmxl8+vDNsuhcp2m80jk49aqMnMj3/TCfj\r\nw5xCEcvOAuBsYHLp/QGXUOsxS+B8x9AUtC0BAiAw2sCEZ243OPhgo/Zw0wOG\r\nN683ZaaVk7xOlAriJw+SyJK0eWsl5hx00CNdGSniHlEDOLv5/0fhLXbukC70\r\nw/1kn96BiFH6Sa3Ee64wF0/3gyRzccLD5kg=\r\n=Hq4M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.2", "jest-util": "^29.0.2", "@jest/types": "^29.0.2", "@types/node": "*", "jest-message-util": "^29.0.2", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.2_1662202100878_0.8173527781775216", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/fake-timers", "version": "29.0.3", "license": "MIT", "_id": "@jest/fake-timers@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad5432639b715d45a86a75c47fd75019bc36b22c", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.0.3.tgz", "fileCount": 6, "integrity": "sha512-tmbUIo03x0TdtcZCESQ0oQSakPCpo7+s6+9mU19dd71MptkP4zCwoeZqna23//pgbhtT1Wq02VmA9Z9cNtvtCQ==", "signatures": [{"sig": "MEYCIQCMgQzT+nRmioaOWSuOUyny4BymM6OiQoEro5b2AK1p2wIhAPgIlHC6Gkl9kjNXPJOEQ+tdB3YR9AIrTdwdKzBri3Zx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOlw//Uu9lrTYhdg7u0rqoqxTvi5VzZM6twqUu9n2Nz49DdzDCnpP1\r\nF7JR0NpN623h4fCoeX1sxaTF27WbWxzEZ0X+wkuBZAAaVc66p1BVDfkKpKzy\r\nH7XikEVYA+NT3p+9bqTHzyzDvuknyECcBEApmUKkKnyO4blqCYRFRMWMHMM3\r\nnH+LiIbmc9CQhaf8fqQDJt+lknUkTOE4lZHIrkB9pe3YeTwpyj7KYorohvNB\r\nVCz0qm51VdB5yCIfPO8Wd65SCJ86DSEvStMUEDKvOOnBSENDhcBM5KUV0zZZ\r\nk54J1RnZcMLL/oIwlXYOCaQcM8Q7azmQDzWC+wzPYAA/zZoJZPi5UKdxLZ2E\r\nYLga0nalQhD/e2hg+EFyru/GIBkzb0014vSKlJSeCDhOSyFGSM79powfZoKB\r\nDe9K+JqeafWH6CC9uGN1n8SvnUJdNQk9n/LxK9BfkjPmxPZhmdsq9k3j3TLG\r\nZprWPxNEGBXKf2BWpkmPehXK3DO7tfCwgPhPqHWd56rDRiTT9Opz+3rdf9vR\r\n/9Kh+exstK7g0N08EjSWNCZnmVCY9kMdGtqSeiCCWaAVXFjP9dbUln/90NTL\r\nTu43DpMS3bTOyiSa+72UgZ7j8FhpNmxD6dH3DYJVfGVi4zp8uWAHvEdJKvFO\r\nXzfG0HHRbPEL2kVZSFJKApWhiS+gjFGN/8c=\r\n=jkGg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.3", "jest-util": "^29.0.3", "@jest/types": "^29.0.3", "@types/node": "*", "jest-message-util": "^29.0.3", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.0.3_1662820899311_0.31129747372877103", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/fake-timers", "version": "29.1.0", "license": "MIT", "_id": "@jest/fake-timers@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "914f4a1fb55adeead0fb1e5b11a2a7f0166e9673", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.1.0.tgz", "fileCount": 6, "integrity": "sha512-6B6NFieIn68nMHqehnJYvHc7qAMun7/2dpDG6MmDNdXjgx4IWPIir+xwhMTO2qZYyFKEawiOQCHGab/koxazug==", "signatures": [{"sig": "MEUCIB7QN/yLIC5ep/7Ux+eqqvVYSQXdR1TaAZz0u6rgjpZhAiEA7FhBfLGNQhyrgA/tTLrYmQnZGxuYMsGmHne4o6G7f+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt0hAApNuzKZv3qQSDHrbCOccMPBrQsK+G8Dcuup/Zc/TbO36IECqK\r\nHjh/ae0eoPv8Y/yMtf932uebL8+rtehP0Ga2k2hiQ9REKeEiUmPJyaSX+gMx\r\nVm9Bx4rb8pKMoPWciRKrGhJaeap0jMOq9djWOyif9quboFyH9whG8iejPMzI\r\nys2Xr/4SGOAeh8kBKbBTXESKBH2kCBwgcKQzqwi2tVhUOIuAEviWHts+4q3d\r\nRpFKP308/aaqUwAVgjD1NwxSBg+zvm9UxSm6d+T1vxsOHspXFL1Cktk/g7qo\r\njzoKXSd3dO4OiEoEK1ZzbmcWw68v9XgYN2DqUer7ONAg43DBbcL5silCGUjj\r\n4N9/ZW2NjKNVkpn+CwnVefwovj8QJd+1LtNra4fmrMJ224glVl/xRIJGbX4U\r\nbJU3RJEiNgoMqanHyx6WOZeoUKVCKfchW12a3RfwowLAmE9kiTc7Bw/EyW8y\r\nDUgDgQnONM4Ix+amzz6WtCD3B5REsmhAjRfjk5qgCSJK3ceS3BAhHoIg/FGN\r\ng7M3zgWFULx4gWzTcBoBpi5rO26LlUpnIWBrEZBbqKlPhC1jIjtYEzS4BOs8\r\nTqDRBGdcaRtLhVKyFmlHj32uND0P7fj+frKzFfA8XRZrP6R7JrDFSqfYO+fi\r\nQcAeOeBKhI6jQedli0Q8Gw8AW8DheGkrAhs=\r\n=R5Ir\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.0", "jest-util": "^29.1.0", "@jest/types": "^29.1.0", "@types/node": "*", "jest-message-util": "^29.1.0", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.1.0_1664350662440_0.9854682057880928", "host": "s3://npm-registry-packages"}}, "29.1.1": {"name": "@jest/fake-timers", "version": "29.1.1", "license": "MIT", "_id": "@jest/fake-timers@29.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae345e5ca51e9f72e0d861e17c65d9cb558f407b", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.1.1.tgz", "fileCount": 6, "integrity": "sha512-5wTGObRfL/OjzEz0v2ShXlzeJFJw8mO6ByMBwmPLd6+vkdPcmIpCvASG/PR/g8DpchSIEeDXCxQADojHxuhX8g==", "signatures": [{"sig": "MEYCIQDHnjqwNNlDsQ0VnRALSjDbkFt9SRLgOGS34x6lifxN5QIhAJjmoBS2CYV7UpT9E7aMa7yrfxuN7kNmMp4/cXQUAWNo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNABNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDfg/8COZKjcz9Cp4nfXbtNHhYkMqcky7PRrgzaYFS9yGIHf6oyJVK\r\nnYp0qeNoPv0xBP4O8rTWtPvcHktjntAkW0sim9dHeDdowFt+4PZk4wb94q49\r\nbNo+nax5T2VfPT5kk4Om5rYCQnBdbfarEo8RP8QcvGE056L8sLoC4RpG5hnT\r\nXUaM4leUAz+N9wP2injdgfiWauf6wMpGGN17CPQkKR+cmQOk/TAE8t6W9fUJ\r\nL10PNawo0CLMdIdm7nc2LXspsoQlmMhEE6To0gj46xLTYQCezr0aVIkzkd/6\r\ngeiaqfbXZ9SkJHmd/UsH8hg9q9HoU7km+okfpIbhjMOswXR84ed/SncW+dwh\r\n9GST/ingRjSbBFD76W7oLF4746KeeYAK4LVrBtx9/U8dz4aDX4up5PNWa9y4\r\nmq4zvjcf4CWbTe3Ww0FRBwI9V5IVGpPEejjek5mlpax0EdOx+pEAqMhGl7YH\r\nfzX8Utu1UlkphOT8GVOBaqhF7pwmuUk7vl1fAkmBTwJvVRJ6IGaehE57jbUb\r\n4M8SUqIl+r3+6REeANjdbKoUAqm0BsY0JO/H6TE8EAbA13pLqUGFt3lkdW46\r\nfdF4jxLId66XOGKOCKfeL4rYohzdqMuG+jiojdsVBUzwjEY1t4qnMzXnPwPR\r\nt+GDfUFYXz/w8r3NH1MrHSmmjO35MJhbM1k=\r\n=WLNZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fd9cd900ad0904421a3d97661fdc3337194da1f9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.1", "jest-util": "^29.1.0", "@jest/types": "^29.1.0", "@types/node": "*", "jest-message-util": "^29.1.0", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.1.1_1664352332839_0.4898749600375467", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/fake-timers", "version": "29.1.2", "license": "MIT", "_id": "@jest/fake-timers@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f157cdf23b4da48ce46cb00fea28ed1b57fc271a", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.1.2.tgz", "fileCount": 6, "integrity": "sha512-GppaEqS+QQYegedxVMpCe2xCXxxeYwQ7RsNx55zc8f+1q1qevkZGKequfTASI7ejmg9WwI+SJCrHe9X11bLL9Q==", "signatures": [{"sig": "MEUCIQC1euHYdqoLY+Yhk5pAZkZb7u5W81tUo4+lGSnYqyuRcgIgDuS/pkkQT7KC/TUfuWT8JwdLqMPqtrUn3gqr48IU5Ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHcxAAinuSUMZsRaADIEFG3UuJSPT1btIkhlBcumtwaGfNf2f7PmTf\r\nHn91XBEV5NPLy9WwahOX85XitBSy/PEdHkspbA4dA0laIWLpvtGH61wgj7u1\r\n7Y71Z0QOjYGn4CWL9cPcR2QdBLtI4ynCL+JNvxILEnA34IZ9uQ08xPl7k/6Q\r\nJsJ5buTp+l8ArhyV+hmZVErpHfjc/3GqTfehcvOBG8X4s2ukX+8H6PNOoVs5\r\nR8dSGg3g9FmyB6ym4M+Nr+VBK7dP5ztzDvtJQMcm4Y1velStyR+arcnG8RcP\r\nZPUEPawOIPVsVgVxsonGvJp7jKyuGADtF5v09til71RLsUr+B6ey0lwUfBkG\r\ne/aTb/Iwel+Itfj+SZbw1gdn6sgXdCQX9T4oUhxSrmZyEbGlIhzzYAkDwf/w\r\naEyCmUgxUf8k8DRHFIAE/rfvzJZ81n0e7OEC1v8JnCzzBJethoLMhnoSMoM+\r\nLw/yGabyXk3wzQpnJyH3ZTtSKE+HlNjvIOsS0saYXx6/J83PhiivWqn1N/BS\r\nYDMV7+mxeN4DJIqf+de5kRgqPX4Qi0lbbNmaW4eM1ogqCHPq0ARGn7hdESry\r\nDcau7XIIal13HWJHJFUIMA7mn2VAj7U8y+SKK5+sD1mxyklQaDsj0rih4a9L\r\nbnEpy2H7mIjGncHtyHXsfi5w8TIrq8F3xTw=\r\n=k3fz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.2", "jest-util": "^29.1.2", "@jest/types": "^29.1.2", "@types/node": "*", "jest-message-util": "^29.1.2", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.1.2_1664522570790_0.4379954296555988", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/fake-timers", "version": "29.2.0", "license": "MIT", "_id": "@jest/fake-timers@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e43635c1bd73b23886e80ca12307092ef2ee1929", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.2.0.tgz", "fileCount": 6, "integrity": "sha512-mX0V0uQsgeSLTt0yTqanAhhpeUKMGd2uq+PSLAfO40h72bvfNNQ7pIEl9vIwNMFxRih1ENveEjSBsLjxGGDPSw==", "signatures": [{"sig": "MEYCIQC+a+WdJd3gy5+Nc+TxqHXGq3lxuDuKtk7jIyZnQUdMKQIhANw8jpowXH9aIBFUHtjFAtmVfyY4Z90DxvO2/sDZd+lR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6rg/+IZ7KXt/i6Hd+a7bRLku/a3ZXiyvqe2E3etQk9FgMog2nAfPf\r\n7BfersLv+wffimq9HyL4dPro+E3kopayslB99kLwE497Sc7cZ5R24PzNmXJV\r\n7wOK2MeXAuRcdwbxR7R7mRjeZm6xTo8YfYMqn6qz/h3OYY2D7Ha2NUmVk8AK\r\nGI59an2Q7ehQiZIeJQ8UNpJ6XLxQ0iwuZIALxNQ23SQKtLVOQFI5lj+OISxd\r\nUXtougXV1qWpoBxklhCuC4HI6LHxiNV6qdRObE8X9n16CpCiirWSTvF9o7NT\r\nFQ7UFQ5FsqmJVJYsmvJEOV7GPMJXDkNlJ/4tXYbyoctnGCU+w59WQgays7GX\r\nHTXWAu5f9cae4uLG3NBkl1Mj4DNOMuFDkHfh0jwwz6XOwqTyllxDqa6COLa9\r\nxmfQSJeQfY1PpXhKV8zhOXBLphdjYQa5cynb+789SRDPUW+Q+VZHHKOxzKeJ\r\nCD0HJEjFlHSkyNMZ/X9aflIMsDpn08VvWT5gnvBN83tKubEwCiHAO4hlWt1n\r\nmJHVTcxE9+O4yBpqoxU8m6hTY0rZLjSWG7hy85QLKg5vFy0xM8wxuFgrmW67\r\nap6LYfUhVDN/JXLic6de25wvf1ol19AwZMjywJPzrM5qCK/7gtaDKQKcEZJN\r\nNT5IvdpACjZJxjGJVNzeCWmSxicVOlPyKlg=\r\n=FcOk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.0", "jest-util": "^29.2.0", "@jest/types": "^29.2.0", "@types/node": "*", "jest-message-util": "^29.2.0", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.2.0_1665738831378_0.44185349406251406", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/fake-timers", "version": "29.2.1", "license": "MIT", "_id": "@jest/fake-timers@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "786d60e8cb60ca70c9f913cb49fcc77610c072bb", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.2.1.tgz", "fileCount": 6, "integrity": "sha512-KWil+8fef7Uj/P/PTZlPKk1Pw117wAmr71VWFV8ZDtRtkwmTG8oY4IRf0Ss44J2y5CYRy8d/zLOhxyoGRENjvA==", "signatures": [{"sig": "MEUCIA4ScFHPQigl6x9RItV/Ykfp2s6OzMA//jbxC/U950SjAiEAkMrQDU5A5bK/rpQSRKDQ7smTl3NccAFt/7lWQdbJg+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5yg/9Ep2oRkFGZ7W4rWiW0GeTXQ9kG67ysqiAON67xGUFmpQvMuQc\r\n3UlCCj3/ZGtMR3hwJ9+9cS/tYJKOI3+BOqSz0IX1S77h9Ih0V9Nb31Pngp4s\r\npSiz9tGdp3+XxcyQ4Y3NGGw9LuAp/tWXyYtTsw+uGTJIOHhUCPRjbX7SB8e5\r\nsbHQt6I52KnLBlTXpeMhjzzrLtulFRvRd6HF5nWEyI7QUnj/BoaRBl4U/qd/\r\nQD4qHMiCaMXf5A7EeGTjZ4l42M9y+UhV9Kxe7Qocl3wKneCFhcTgrrOjFy5A\r\na5zIEKgYjx1PD/41g1O2wky8KtNlMWlXPyEovSz/CYDYxj0Qr9kScMN880Qk\r\ng5xUrB9GtvZfaHYX15oZ4YSJhlzFith84Z1EdHLpddQkUA3OwV6905cS2LhZ\r\nXRJLtwibd+vpw2o9oqPUX4OWClOoRRCt00rYBZrf85GFWMtfE7OjOBotjQ6h\r\nGbsdXE9Bxe8bV2CCS7GhGQwNBaA9qbNpgHHOde0GzomDTzKPqVlm9PY/BAdg\r\n6A8f6slsgVeOK8UMsvbzPirzkcnA/rp3N6C5XiwyYP3Lx0skU7Zyse2KEhYx\r\ncmeUTpvat7o8ePcpqvFmjVB9w1JPY5F1gU3jB3vmLk8x6YKu9r5zv5SJwucH\r\nNtjmMZVBnx38nJDOsbMuv07rxxtMHgFobO0=\r\n=PJ7s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.1", "jest-util": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "jest-message-util": "^29.2.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.2.1_1666108814865_0.6310178780916165", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/fake-timers", "version": "29.2.2", "license": "MIT", "_id": "@jest/fake-timers@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d8332e6e3cfa99cde4bc87d04a17d6b699deb340", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.2.2.tgz", "fileCount": 6, "integrity": "sha512-nqaW3y2aSyZDl7zQ7t1XogsxeavNpH6kkdq+EpXncIDvAkjvFD7hmhcIs1nWloengEWUoWqkqSA6MSbf9w6DgA==", "signatures": [{"sig": "MEUCIG2fx5mIgsRpJHWbDEEtPi6V2WIqep9we1nrmAQjwim1AiEA/ohLXcXgnpHosx02FARClafkiJJFz+jJXn6ql7Ql9SM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4Dw//QlKf8fduY7whGQIU3mSq1K64ChAHAiEs0OH+623BzpK0836I\r\nRy2/d4cUSMKggOirwTLAq1rJ04N2EGSLnkJ0/0htjb8kJQCwZ1YT+Cy01C/B\r\n8I0RmYplhIXyMvCilij2l8oSHqdZxbnQuDJJTXKAHTtms1osivh55cX9Zi0y\r\nhjvThcnEoJ+y+PC6S50KDr8/mfl4i8ACVQf3yNZmmzm01o1hxaHZXB1Tb5K6\r\nJb1kioIAAXhQY2lqVpvSVC6Fi3MZTpyilhEQcX++vYgI1JHe0wX0/pn2GTj2\r\nfICs1TDzQyIkOAbSd0XXHb2ydDvEf9SKdAt52ZO++sgqJi5L80EyXTT3tj5I\r\nfe58e0untVkKIqL7qgL3IFf2SRg53tN31p2EImrYGrsInAPE43ZjWhw1K9H4\r\nn3RzYhsQUiohYa7NveQrPq6B4fKGhPWpDL4WJFjoWsYovH3Pt947zq+PUfwi\r\nwh+if26KqfiUnYRNOwvCN7GTrT2sfae/CKYV3YRbnAdGL9abJRYNeQVqn5mg\r\nYaolbkj9I+jruBhSFmK6Lr784Vpvp2WgBhd2F91ulJspeO+qBzt4Sz1uGBfR\r\n0aUDJMj5d6UmVcuI0w5n95bQ43482OUpWglPf/H7y/myBa1zB99aIDH6k0mt\r\nSkPs07EYbtO1D+Cg4BZyTyjutqQS2T+E/gI=\r\n=c8Io\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.2", "jest-util": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "jest-message-util": "^29.2.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.2.2_1666643047142_0.04250596497139747", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "@jest/fake-timers", "version": "29.3.0", "license": "MIT", "_id": "@jest/fake-timers@29.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ffa74e5b2937676849866cac79cac6a742697f00", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.3.0.tgz", "fileCount": 6, "integrity": "sha512-SzmWtN6Rld+xebMRGuWeMGhytc7qHnYfFk1Zd/1QavQWsFOmA9SgtvGHCBue1wXQhdDMaSIm1aPGj2Zmyrr1Zg==", "signatures": [{"sig": "MEUCIFHXvRkCGhHsFeHDgbyFR90fK9N1feqdUTH88te8TFXpAiEA+wvTrmHtaWyvlwHVYAmPpktJFVGIvXtwY9+UegCsEwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUakACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBbg/+NU47mtMQ2lRa9Vyr8Gsps1qKpTBkHhbJk09BzVhqpqnspttb\r\nnWSNU4IGorxdMGr8K/fvuPDpScgCHuBCKlOZdrYz8PP5fQqcfNOS0SeB0fBh\r\nIa7ZQRyxOfE3/gfEJLklUFAqzhu1nKPsLRusRe6hm4f+Vh0UWvbE2Lurj2ml\r\nqTjXu0MaunC/YzYZy0+bMv47NsLROwx/Pr8jI91Sg5IQBz90BJ+2fR4kY62x\r\nQXEwSR7bCxK85wQXJ8gj4i5/pXs55hqjpKJ60cCEIZClWpYW6kph7P2af330\r\nNUSMd3FmuV3ZQc01UT5Kv52lfM7tSRkwcVHTUr31B74mnzxYuKMbai/tfgjJ\r\ngKsn7+aIm1VrjvEPY8j0ePom1pMSRo8zjf959RmDroipyIMLjvgJVXSQ0sca\r\nIcdTnBeWIcX1GCwSOYcrO79cAicQ0sNHUOynlz5/D/4pYBVuJOKxG0739jGZ\r\nRhzMToxFH/PQY372kJXbtZqquufZyDUMUQE9q5Ih/A3FTTHxmMnC6WLBAvWH\r\nKZl20wSHlFbG78tBTEGpJtaVv0Ju9ClXczqsgYJDNLbaMaSSlfNCFqnVfeAD\r\n5YLaAiNDYibcK/2sqwA4qwcbzXMpQPqxZwOuWneENCFxfv6Nt85lRdVo1ivz\r\nGQQf246RfLyTrYQ5PTxHM6LrNMDIta3eqNA=\r\n=+9PI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.3.0", "jest-util": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "jest-message-util": "^29.2.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.3.0_1667843748436_0.828935255834433", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/fake-timers", "version": "29.3.1", "license": "MIT", "_id": "@jest/fake-timers@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b140625095b60a44de820876d4c14da1aa963f67", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.3.1.tgz", "fileCount": 6, "integrity": "sha512-iHTL/XpnDlFki9Tq0Q1GGuVeQ8BHZGIYsvCO5eN/O/oJaRzofG9Xndd9HuSDBI/0ZS79pg0iwn07OMTQ7ngF2A==", "signatures": [{"sig": "MEUCICHHWWhyebYnYXcsPP9kyBd61hGwO49qRd9yBOiL350uAiEAuUETJtMvTho/f+ejRcwob+vDxu4L9q/Gm1GMBss7X1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfbA//aNd9yUS9SlOFeMx3oRznM3FPcTXd79xLBOmbaN7HaUrK8bFy\r\n0w/YgWcz0wbRCy520AERTICS1z10EzuZDARmdLmS0F0mjWTeL36AwE/2sNy7\r\n434FC9rgvi2lHc/7QnTdlufXhWWMjLlOXIj9jpvXHcWZJtUqo616ZcRAd1oD\r\nXjXMQ3YuYxqd+3Z6bmUt35At4/lSsORRPQWc98UTJh9Btt8lBQpiCFm7X6AK\r\nLC2GhYTnpjMIr2ngbo1YOqfFGxoNcZa6Mvzat89zwDb29CGVKwnDWIzaB2em\r\nT3DPbVTzXWEoXb6PMEA8VrWDFbTF7pyQeby0qsTtTRmqPCkrslc+h1TW/loG\r\nAtY6MrlrX3Ksy5/ml5HOUvl61V5Ty8s1bJEf8ui3amUE39eYxxUXN4bzBhgB\r\nCBEdRuhnIPac5YGZDDuG43UzXUdNBLlw/3SY491Qw9obnQkMXWUHgZzPes5R\r\nmmrQxjT/SNO7OwJcT7UvKb7LNYCD2ge2lVWUuONxTsJIOrXjZYrJZ824qQPj\r\nlLuyXqi3fg+VuFf5v0ShpU6yRH9BGVhiLGs5Q0B4DRs5LiUbdYgzQ2P8k9Xt\r\nilEAgqZEcBuCENtkxxJkAVktOtwFh5sC7J25jo/pek0m2bBYUyvKj5ukH4qf\r\ntGhkkl5zEcB+1t1laJwHtLZfbgNTSEw/V4Q=\r\n=y29p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.3.1", "jest-util": "^29.3.1", "@jest/types": "^29.3.1", "@types/node": "*", "jest-message-util": "^29.3.1", "@sinonjs/fake-timers": "^9.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.3.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.3.1_1667948184504_0.9264209547974613", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/fake-timers", "version": "29.4.0", "license": "MIT", "_id": "@jest/fake-timers@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a2409cae63eb1d0122edc21783ef88c67e6face", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.4.0.tgz", "fileCount": 7, "integrity": "sha512-8sitzN2QrhDwEwH3kKcMMgrv/UIkmm9AUgHixmn4L++GQ0CqVTIztm3YmaIQooLmW3O4GhizNTTCyq3iLbWcMw==", "signatures": [{"sig": "MEUCIEalTadvIsL8sbl3M5K2JUQ6aEJeG/vvFEUZLk0w0aPGAiEAtd4VzkgQWnxc5v8G7mB7vPuyqJcbLBcOEcIs4a/s18I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR6A//V8qMfLteJygxHeF0HCFG+v3JfDIsNZHzwihCIfHlP4O9IgLG\r\nfcZdxiNVGBqVvojiyAM1ZqGgqIzQiovNfydf21326gUyiEBa2NAuTXDgC/He\r\ntII9SDRu6im3t+LUZhDRFGquxyzCcTRTbHX1vzdCz2zryTvTl/9SBez5R2uF\r\ngjf7t/MP5QhYgGEaWhy8yFdaj/6pBQ3iJUE/WCjf6ix5iveVbhFbMC7Ih2Mj\r\nGM5vmOj7HoFaC21bZnh8QMRQRLWPPkJ2nnTYiUi1QYHndkRrGLsE9Us2Rkpg\r\n4l1ClCUKXne4a00aDzmWcMcHk/9vPSfTs17BVHNCGhUue6lTp6Aig5QLkBtt\r\nCfC2K4znLD9cQHJ3NXUPHO3E9Gc2qwFQ3PGViOHZniCJc2ijV0lcYb5IXX5e\r\nzf9vpSI4PP+6W8bPuHecJbn6bSAATjBWbGnB1Gym+CxKZcObczHuMcYaISwg\r\nnGJkEAwtWvGtvglsTcJei88LbRG/wN9ytu5/j2F1F8cpiIP5NBBm2o6IIGGB\r\nzLiXgJ3RVT3YYH5HF5Uwlifsi1w2G13onOAGGfM/lrRFSoFomuEs/Ov/c0FX\r\n0QyU80To615wDziwFm+VN2BndO2J5Wi2+aYC1Ecf3ETB7hvFkje0y0HjleR1\r\nDYRzogKTlsgHZzOY26oPhdSufyzbw2sp+A8=\r\n=Ro9m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.0", "jest-util": "^29.4.0", "@jest/types": "^29.4.0", "@types/node": "*", "jest-message-util": "^29.4.0", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.4.0_1674557754007_0.4884767194135775", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/fake-timers", "version": "29.4.1", "license": "MIT", "_id": "@jest/fake-timers@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b673131e8ea2a2045858f08241cace5d518b42b", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.4.1.tgz", "fileCount": 7, "integrity": "sha512-/1joI6rfHFmmm39JxNfmNAO3Nwm6Y0VoL5fJDy7H1AtWrD1CgRtqJbN9Ld6rhAkGO76qqp4cwhhxJ9o9kYjQMw==", "signatures": [{"sig": "MEQCIGPmF0jugpkDLcWp1DixoQBWsUFZsI4PqWGMU3EtHAtCAiBMxROIPq+AbDiEC/QP38jEHt/hqfVaKwFTbF45HAxFDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrU3Q/+LCb5gYWH2J5XQiSrsR8v81Dek1d7EmMxWDTzWq7OSvtkSbBy\r\nvfspH7mVweFtafiG2OQoWJ9Zs/CvGYROCnTYGPhLfrdf9gR6xdcCZDwxKAM9\r\n5OFxviaUrf9+x0S1jF/wPXMb/NUKDABSSGGaAS7vZi5d7DzsnOQVoIcJThpn\r\nP/ikMekjYlKxxI+d5aP0T/Cyk8VG1lbHAIvEVEM5NT962wIXc/Cjf3OnK7XS\r\nc3cHASo11/h3bZd3ut+k/o9qaFT/zz+BJ77pUXBRRwomFTdepAmUZe9aNYk/\r\nDmy9lf+H39beuWH5HMNPWrXFKW3c4DNB1mpprCKTdEqUOZjy1myTbHFsuq7B\r\nh8E4S8kR3LBwkQdoJrxSYmT+oYLuJfDssl3tOuYGBCRe0ma3R1fwdAMlgbon\r\n8SYodWsNtj9k7agz/9cE6EAVIVxoUPHMlnK69wHr9ZsYAyR/Ma5rg4JVv9q2\r\n5D3RMFo3xZDenheMlZ/adM9XilfE1lBwcvgoL9H339TkQeug0S4GMDVkJwsm\r\nTdcC3xkf5H2Y2cYn8l+HnKoOZcmiwQxmQyxkMnMm+/N+BOb7X4ItNGbC9qQk\r\nJo8I09oPpFss9hAwm8tRa1j+LCpzLtjRSb+9+7IzkOqEkV1wTZgjnZ7SzhXM\r\nEvLLKovy20uEy9pNFuLlqgFvTMnFvn+M62A=\r\n=d6Ov\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.1", "jest-util": "^29.4.1", "@jest/types": "^29.4.1", "@types/node": "*", "jest-message-util": "^29.4.1", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.4.1_1674745718402_0.02327944025592643", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/fake-timers", "version": "29.4.2", "license": "MIT", "_id": "@jest/fake-timers@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af43ee1a5720b987d0348f80df98f2cb17d45cd0", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.4.2.tgz", "fileCount": 6, "integrity": "sha512-Ny1u0Wg6kCsHFWq7A/rW/tMhIedq2siiyHyLpHCmIhP7WmcAmd2cx95P+0xtTZlj5ZbJxIRQi4OPydZZUoiSQQ==", "signatures": [{"sig": "MEYCIQC8VGA1NePDMo6Zs5T09vaKR6Dywp/3EU35udl6JW/b8wIhAOLdCGhtEggna92Z8Nhux6NK3DsHClsd4evgCZBt7WAZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lX8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreQxAAkMD1Up0DMz1J81ZW3RVUtkrUUOA52bBdVZ6wXY27BhMa6e2C\r\nRK1ED3/zmc0wubrV40DJ6jZVKo0GVRgSWUXl7SfpZrDbW572uFhZdIN8aWqm\r\nrtVruWcu4OcLSGLo8/pLkgSo0ahjcDdyTifIYHnOSz2B6U8M0nn0jWXBWDcm\r\nmV56LWM2mFb8lQjGi5NZO81GVaqKthanmup2eXnXUJSNQDkjBl/PhOncyOwV\r\njl+3Ya8zjxPeGfXZ+FQkRa64w9IRRncKnmDX4FTX1Z/yK5yPNL8qzIaK3fSr\r\n/i/xRwQmZP3tICir1jW1OhH2yZPcbEUZiiLDNdMjAXT53x8v9iwzHjAbKr1b\r\nbh/YPz/k951hHtP4DnMHDyqdNyptC2sgI8DwocAEZbMSNFZO1N1OTxbR2C/q\r\nSsWQfMNp2+WZhxt5GncSZognJ4PfbNGr8QisfKAZ5dDYW2+nNepcdw0nsBVN\r\nlxpByeIlPIDDBCaEtC4FEn0S6iAkf907QFxFli3MqdF9CyB91Dwwgmg2dofe\r\nYU4QCYY0SNesaAPMXh0vPlQdh5Lrr49uy8BCvbKV+SnEKg2u8ZvrSlyS+fIy\r\n/JAElUjRZx4rhGknh+CaDXO89xIzzr+kGAUpATg95hQXwSwe2rJUk16Lju63\r\nXIxIkgP8j39RhUX2CoGw11mAZtpyoCwZubk=\r\n=EmXp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.2", "jest-util": "^29.4.2", "@jest/types": "^29.4.2", "@types/node": "*", "jest-message-util": "^29.4.2", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.4.2_1675777531800_0.7467750120895449", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/fake-timers", "version": "29.4.3", "license": "MIT", "_id": "@jest/fake-timers@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "31e982638c60fa657d310d4b9d24e023064027b0", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.4.3.tgz", "fileCount": 6, "integrity": "sha512-4Hote2MGcCTWSD2gwl0dwbCpBRHhE6olYEuTj8FMowdg3oQWNKr2YuxenPQYZ7+PfqPY1k98wKDU4Z+Hvd4Tiw==", "signatures": [{"sig": "MEUCIQDbPOoxqopmLL5Jk0K+KSSDgOvKjRSVSmbZ7qHIWVSahAIgVfzOD4owZaxhmb90chialy0599xTsX6bAQOJaEsDrBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MinACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvJBAAgzZnFUbSUZS3+dBT9RBP+5xoANupGHDgG9prcj0Nr4+O8/sN\r\nGV65qMoScSSqQC1Z4tbG1bLbBnGGAIAms7v2GU87sINJNOX1G+l4I487yagm\r\nlQeNC7hAQlbQ9QaIhKwD1iJXiOdbrWUYX3vWRbmPs8BFI8DSj/NcAnaEbTQG\r\nri90fU6TU5IYHvQo+svRMLO3IsWx1LGgLOn2XFs/gRW+uWDAJrbDxLRMxljx\r\n73eIFF07fBv5j4u1NpVkqmOqCPPCdp3umvYLT5D28/bQG/PBRqYbIMIKaMJk\r\nqnjmr1b1xkWnBPacF48jegHOk2qbWzAVPW886l3TqpAA91Y3e/nBy864c87Z\r\nmYAoDu8UWsCVQpeoSbkxOBEYxcHt8bDk3I0qop76OCGShtUpLjh69d8R+akw\r\nr2tVx1q0iIUGPYPbqNv8W5dVB2Rma9w4/9MFvyWUim1HvWtzb0gzIkRsJiJD\r\n+b+7WpStYrhj6mqvb4PN7fORXTX8AfdZforkw5GMc5FyOMv6ECE12lp/JkEe\r\ncLhZxpN7ltglufK09e4hKn4v0ZKcbWmZe0hei0iRRApJkNNBweG4BsS/EXSI\r\nzfIgIzA2XNDaLXu8u2HYzUVkf5QYGu2SvqtSDKiFeX6E9kwtjFMjFHQq8wu4\r\nfdpiiuh1pglknJSjV5uozRe5XlmkaBW4d5Y=\r\n=zdRu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"jest-mock": "^29.4.3", "jest-util": "^29.4.3", "@jest/types": "^29.4.3", "@types/node": "*", "jest-message-util": "^29.4.3", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.4.3_1676462247289_0.6679223770035652", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/fake-timers", "version": "29.5.0", "license": "MIT", "_id": "@jest/fake-timers@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d4d09ec3286b3d90c60bdcd66ed28d35f1b4dc2c", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.5.0.tgz", "fileCount": 6, "integrity": "sha512-9ARvuAAQcBwDAqOnglWq2zwNIRUDtk/SCkp/ToGEhFv5r86K21l+VEs0qNTaXtyiY0lEePl3kylijSYJQqdbDg==", "signatures": [{"sig": "MEUCIHQL+DDlPzTWuUMwolf4IwEE9tlgRelh4s8Z2w6U+/eIAiEA1ewZQWj2al4HNPzXcwCp4UCRPl92CSQoV4/wkMwYLk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeutACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRCg//fGUeUr3/45pI+/aWAiLntD/DJbkIujczd8SLN68JnDSaCImK\r\nNNOEUcgtOF3FslZ/jfcZfQYheup30+45akDemqZXiRc/bgM1edLHV9qp6YCC\r\nKOls2WjBIBDInVnQvXtdicwRqjzvS4gZ0MEpZGCGU/+LRu8nzAiWnsEXJ6PE\r\nG6YvNxGeOLuy9dpkSVsMWpNZ7we4fj+XEJe3w9cnZD2SMamQc6MHwBH0XeoA\r\nEhnAaaLHhgooDQwFTdK//x2TD1VA2f8kQEtjG4YGflAYLp18nX/Tv8ieOLjx\r\nlkTLYYD5sOAs1VsfGXcepueIIXHBh22iE/sGfWYgAuFMtAqgj8IImxFEEUPA\r\n9UfNyNkRV6VoLooAawduhwh5lJ2aHGUSz91NG2h3TB8oEPsb4kQPGg4XQpS0\r\nZZAGvL6843B6EfS3Sy5FiUADc0RGpjDTC2WFgV5oq3MHoXHCQxSqI8ECm1D3\r\nkzUxw9pA9kdcdyNt+lRZHm1GVDp3EAqyzmHli6k5+kpva6xTBCzfueGaUZfE\r\nmdadSXhqeoJkUR+nIzOOr5a4jK0o2aBnXEdQs7W+V5XrgTmEKsVFn5vaC2gr\r\nQEPNSek2cIbDJJptivIGROVErjb5BDjowXLDB6izvEC54q99NbtIukGJk5kc\r\nDeJ8Y0ne/K+agzKJ3Xuoode4ZFk7CzNqebY=\r\n=Z+FE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"jest-mock": "^29.5.0", "jest-util": "^29.5.0", "@jest/types": "^29.5.0", "@types/node": "*", "jest-message-util": "^29.5.0", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.5.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.5.0_1678109613008_0.5775869728654757", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/fake-timers", "version": "29.6.0", "license": "MIT", "_id": "@jest/fake-timers@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9751cbabc86a39a1e6827cfcbabeba0207a63c97", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.6.0.tgz", "fileCount": 6, "integrity": "sha512-nuCU46AsZoskthWSDS2Aj6LARgyNcp5Fjx2qxsO/fPl1Wp1CJ+dBDqs0OkEcJK8FBeV/MbjH5efe79M2sHcV+A==", "signatures": [{"sig": "MEUCIQCl+irUg9mlYBgK9BeloM0Bj5APtkdBPzflC5x1rKcGRwIgKq1gCj5khrw6EE73U02e5K1WvqFUpI6nnNbYkYh7qag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26302}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.0", "jest-util": "^29.6.0", "@jest/types": "^29.6.0", "@types/node": "*", "jest-message-util": "^29.6.0", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.6.0_1688484348396_0.6848604925101363", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/fake-timers", "version": "29.6.1", "license": "MIT", "_id": "@jest/fake-timers@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c773efddbc61e1d2efcccac008139f621de57c69", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.6.1.tgz", "fileCount": 6, "integrity": "sha512-RdgHgbXyosCDMVYmj7lLpUwXA4c69vcNzhrt69dJJdf8azUrpRh3ckFCaTPNjsEeRi27Cig0oKDGxy5j7hOgHg==", "signatures": [{"sig": "MEYCIQC/qdviCNMOTRiHRFc5YT1QpPop1ZqAP+71Swvj3GthPwIhAPIiY5BxbCPC4Ym4SPXQRWIcjlMlMPxPeK4kkzGJpB4D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26302}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.1", "jest-util": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "jest-message-util": "^29.6.1", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.6.1_1688653107590_0.17401596613069215", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/fake-timers", "version": "29.6.2", "license": "MIT", "_id": "@jest/fake-timers@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe9d43c5e4b1b901168fe6f46f861b3e652a2df4", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.6.2.tgz", "fileCount": 6, "integrity": "sha512-euZDmIlWjm1Z0lJ1D0f7a0/y5Kh/koLFMUBE5SUYWrmy8oNhJpbTBDAP6CxKnadcMLDoDf4waRYCe35cH6G6PA==", "signatures": [{"sig": "MEYCIQDRYrWN0Zi9qc+XnwVVd/rMrOgk1n7STOpUtJUSuj3J1wIhAJS5gPiw/hTTJR363l6xmdliO0C1WpjiWAD+rIu1BUNI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26302}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.2", "jest-util": "^29.6.2", "@jest/types": "^29.6.1", "@types/node": "*", "jest-message-util": "^29.6.2", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.6.2_1690449692625_0.6810576906202088", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/fake-timers", "version": "29.6.3", "license": "MIT", "_id": "@jest/fake-timers@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7e780b05b14ad59dca68bdc188f6cf085552a0e8", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.6.3.tgz", "fileCount": 6, "integrity": "sha512-pa1wmqvbj6eX0nMvOM2VDAWvJOI5A/Mk3l8O7n7EsAh71sMZblaKO9iT4GjIj0LwwK3CP/Jp1ypEV0x3m89RvA==", "signatures": [{"sig": "MEUCIB7c57Lg+ZVyndd1Epff9rWrPtCeuUX2+LeTguDvmmxMAiEAuXHe10ZvViVOtp9Zc+lcQU3v9leESs1heBnd4tdfSUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26298}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-mock": "^29.6.3", "jest-util": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "jest-message-util": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.6.3_1692621557985_0.07590079452438281", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/fake-timers", "version": "29.6.4", "license": "MIT", "_id": "@jest/fake-timers@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "45a27f093c43d5d989362a3e7a8c70c83188b4f6", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.6.4.tgz", "fileCount": 6, "integrity": "sha512-6UkCwzoBK60edXIIWb0/KWkuj7R7Qq91vVInOe3De6DSpaEiqjKcJw4F7XUet24Wupahj9J6PlR09JqJ5ySDHw==", "signatures": [{"sig": "MEUCIFaxtOyAV/UEjoUZJTiy3Ls9CFdj6oCIYcRA36WIYyb+AiEA/zzWfgkATDBcDNvNeGK9Z7Ont69W9fQkmefdPKFHJk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26298}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"jest-mock": "^29.6.3", "jest-util": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "jest-message-util": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.4", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.6.4_1692875425914_0.46557401320778347", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/fake-timers", "version": "29.7.0", "license": "MIT", "_id": "@jest/fake-timers@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fd91bf1fffb16d7d0d24a426ab1a47a49881a565", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "fileCount": 6, "integrity": "sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==", "signatures": [{"sig": "MEUCIGdR9G/JqsML7HqRRXyIU64jseeUZDz4Vy1mOuoAtjtrAiEA+jCwBI3X+XL2LvQEJuJezSfhSKnelTiR5dchZevW0X8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26298}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-mock": "^29.7.0", "jest-util": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-message-util": "^29.7.0", "@sinonjs/fake-timers": "^10.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.7.0", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_29.7.0_1694501025228_0.6172697722579703", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f2804b1c9cb8741b6170a67f1c2169545c476dfa", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-u/aIX4utaRDGTa2IML+XHR35f79HHCrQv7au6UAKKc22W/EgkGm4LTAdodg4loVveYHf35xScg9tZkW1ROyCPA==", "signatures": [{"sig": "MEUCIFbfPPdJQ/eIZTN+WATOSLKKCEkba6bF/hVBTSfRxdPiAiEA6IH2MpiTIN0R9jnre5ZvXK3NfyfmwArjzzWJpkAbl9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27760}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-mock": "30.0.0-alpha.1", "jest-util": "30.0.0-alpha.1", "@jest/types": "30.0.0-alpha.1", "@types/node": "*", "jest-message-util": "30.0.0-alpha.1", "@sinonjs/fake-timers": "^11.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.1", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.1_1698672793369_0.8986208799727644", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "93f4d65ccf39a97855a22763dbfe27773424c683", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-jngoA5we8/41JzNK0Vq/C4s9cnjzcVufhMWrawF6EEY6N8O9hgDLn2um2R/3XDj85rvZWCl1dp3ca2PTPH0JLw==", "signatures": [{"sig": "MEYCIQDmATxvLKSajPp0TKQd5c8bfY4zS3XPrsqSbjA7k+zXnwIhALalIuNLGm9OKFpigxkgVV65aYlm/Rc/wXkcU3fztbhN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27751}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-mock": "30.0.0-alpha.2", "jest-util": "30.0.0-alpha.2", "@jest/types": "30.0.0-alpha.2", "@types/node": "*", "jest-message-util": "30.0.0-alpha.2", "@sinonjs/fake-timers": "^11.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.2_1700126909712_0.17069789495382315", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a243002bdd08dc7ed96dd433d966dc1887bb9d8f", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-B9Oov0Bk1YRJaawZEcwXuupHIViX49pZhghdN+m1KFwuZ9smstD9VI9gXFBRtF1fHNu+1HmtD361LuDEHifzIw==", "signatures": [{"sig": "MEUCIGQeBxhfrxdm/1iTLW2J0+w/K/GAiTJyPx5fhDVkS6K5AiEAztaFIGwkD3pVn3YrtASZWdJ25OrEBhofZDHllXPNzrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28034}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.3", "jest-util": "30.0.0-alpha.3", "@jest/types": "30.0.0-alpha.3", "@types/node": "*", "jest-message-util": "30.0.0-alpha.3", "@sinonjs/fake-timers": "^11.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.3_1708427350363_0.6030136461367275", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "1074494d1194713e967511088a04c15c382e4a6e", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-CRpPH235ek0Kgl5/LdmFMrIbRiIgIJtyOaFqsxxTBxqqKETloC9H4UiaWdFfZ3bOdKc4DYoxggxwYbCnwCWRKw==", "signatures": [{"sig": "MEUCIDFBJntk4VOkOX4G9ywVrqoDGvw/8g5gd9dL1XmlNBeDAiEArtNzisM/tHcRGvuyAO3wALGLunJAuw52Pqpc13HUPkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28078}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.4", "jest-util": "30.0.0-alpha.4", "@jest/types": "30.0.0-alpha.4", "@types/node": "*", "jest-message-util": "30.0.0-alpha.4", "@sinonjs/fake-timers": "^11.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.4", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.4_1715550209226_0.8628248683878268", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "c2279a855bbdb0c5da171047bc730835a7f0abf6", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-DuQpJP7zz/vBd6/BweFit7+MJVgIEb4gHfoEd9TKIuvoQ4wcIm05LtMDIJYlPe+g/w/Ew01uMYNJ7acvbwxY0g==", "signatures": [{"sig": "MEUCIQC5fvcGqzxalBEvcxw//MIrFob8YAuLlf74f9uLZHRzVAIgEgI80A9s01pWo7uZS+3eGouPuZpyqaj2SIG3YkZbpOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28078}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.5", "jest-util": "30.0.0-alpha.5", "@jest/types": "30.0.0-alpha.5", "@types/node": "*", "jest-message-util": "30.0.0-alpha.5", "@sinonjs/fake-timers": "^11.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.5", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.5_1717073048525_0.757906506487882", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "400a0b6768d04c13d9b94c0fb6bfb78c508a910f", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-deka0RmhJgEKPJM6cXPd4TJQ6fLczErdMN7Oxzr16UTDFHxtFd79tduJ8uP92dQyO4zy63N/dlFK6d+FHyWUDw==", "signatures": [{"sig": "MEUCIQD+6ch0iI4Fk+SRz31TMblWXgELAyJnOl9isP1e8GvVxQIgHdr/qTUrU7vekR+y+wpph9l2uojdC/eM5N2qnGvS/JE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28064}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.6", "jest-util": "30.0.0-alpha.6", "@jest/types": "30.0.0-alpha.6", "@types/node": "*", "jest-message-util": "30.0.0-alpha.6", "@sinonjs/fake-timers": "^11.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.6", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.6_1723102987269_0.8240416826055541", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/fake-timers", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "93dd3ca28baa9b20757317dd1703f103c7a5d353", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-ct29bG+ebuRk1Re8nzxcMJIul1sE1Se0/PRwQwqbrLbInOw5H31q9DnqrhVhqgUgXosx7dyt1dJD8UvBfHBc4A==", "signatures": [{"sig": "MEQCIDTq+7kFCRqc70v23KqTxxfYP93JK+BPT7a3R/uLyLBGAiAA8AMQbVL/WR5vxyoPZClCvFRbtvqRXC72iao40fLSOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28065}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"jest-mock": "30.0.0-alpha.7", "jest-util": "30.0.0-alpha.7", "@jest/types": "30.0.0-alpha.7", "@types/node": "*", "jest-message-util": "30.0.0-alpha.7", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.7", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-alpha.7_1738225714390_0.735191972828191", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/fake-timers", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "6e67a4db75e63e039eccdf7528cb69613c502852", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-9QSFczxQPLuj/DLHMylUzeWyGar0QoMLwAQSid5KAFL1SnKVosaJzNEyTPSPUbFWAIVTEyIKjaC7FglRs/xlSw==", "signatures": [{"sig": "MEUCIGga7VuZp1PlF0ITfDCaqrJqrodHAy+qK9D09+Z4a+X3AiEAxV+shlgLOui8H5IygJWejIHUGiWxTLkE61miTVQBrk0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28058}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.1", "jest-util": "30.0.0-beta.1", "@jest/types": "30.0.0-beta.1", "@types/node": "*", "jest-message-util": "30.0.0-beta.1", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.2", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.2_1748308993606_0.6738985127037789", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/fake-timers", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "62080daf88ee9faa90f40f979eacb7227b01282d", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-bSYm4Q42Obgzs8tnDcd5zMsgNSZFTtydZJQxEFoaHOSnNuSnC03CvJbZuKs5Gcjqm94eHYoOL7HDvo1W5UMVYw==", "signatures": [{"sig": "MEYCIQCPGD5E+HiVedGEHObrDPKhMxvImRP0aJwb3wjk5wC/rgIhALGJd0XauQb0zPODB+Qd4BhTp3Lu8ARZN9YkvL5Z7eZV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28058}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "jest-message-util": "30.0.0-beta.3", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.3", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.3_1748309268370_0.8328150124455151", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/fake-timers", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "371435d8497a6ce6a373cb3eebb3d7472f791d9f", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-5dHAGsb4eFUCnHSdmjCmgA4KSDY8Tk0wkXrvOgfDegQXzCeCpb8xLL+PK39l3Cm58GCaTFeefNCWRlazKbFcLQ==", "signatures": [{"sig": "MEUCIQC8bXWx8zHNT1UCApIGZscHshcgRcQ6QxgXq16ILLfrvwIgc7wbVPGQM0yDYnTAHD/RPZ7jbjLzDGqxoUX4fjkYwoM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28058}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "jest-message-util": "30.0.0-beta.4", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.4", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.4_1748329467824_0.8314792986986399", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/fake-timers", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "9ad15417fb0284095b5c957f9b186e77ea7b732e", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-m0OyFR1G+SSz6j/tnQxfk4catC9QWxYrQ6czLGTR//H1QERWTff6d09h3iRT37AyDZZ4sP9l6bLCC4rMm5V/bQ==", "signatures": [{"sig": "MEQCIENOrs4jt/VjztO5PQILbLmwaT4roDDIIU77HhObCWkFAiBSnGIf610F03rWyfhXTyDBV2ARk3PzxlB/IJPSSy4dlA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28058}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "jest-message-util": "30.0.0-beta.4", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.5", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.5_1748478610723_0.33094840033887474", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/fake-timers", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "358cda97555a2188757c66abb4421e567233670b", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-Q8qMUbE+DTFXeD6Q6V90cNGMm0TXF57rIAj1wum76Snm963hQzZ/ETZDzfgLW7L4/JT5d8K4FASk73kba9J8YQ==", "signatures": [{"sig": "MEUCIQDmD/uPWN7b5y7xxBipdvF+5Zj8sVwFovTp1Cp6jK0j2gIgX7Nceh6DWpTRfm6ovsfh2SfD8yFGrqcQePYFqhKfhkc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28069}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.6", "jest-util": "30.0.0-beta.6", "@jest/types": "30.0.0-beta.6", "@types/node": "*", "jest-message-util": "30.0.0-beta.6", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.6", "@types/sinonjs__fake-timers": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.6_1748994649580_0.4961946387674181", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/fake-timers", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "09eeb470f88f203e9fe9a175f0352164844c2322", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-UdzyicGH0UmJJx8lSMpLhIX2/ovCl92cwe99qYEXgPbT/fYBZ8Bmfddv1vsfW3vnw0NsiHmkl5dsRiojNli5Zw==", "signatures": [{"sig": "MEUCIQC1x4vd5cTbyzWN2yKWrR2N0cGKDZdfxyopn9vE/f6bUQIgcgXOVhvFSgMbdWHREMIz+M5XEe0D5hJCJkxC7bcuBBs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28069}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.7", "jest-util": "30.0.0-beta.7", "@jest/types": "30.0.0-beta.7", "@types/node": "*", "jest-message-util": "30.0.0-beta.7", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.7", "@types/sinonjs__fake-timers": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.7_1749008143068_0.9916536120676724", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/fake-timers", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a49ff011e3ed25c4a7edfb473ef0ab82d73e8704", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-TyQtOjbx7cS7t+LzZhbhwGG/QsnTcX6IlPRaAXnIChdnB1y8+4I3C3nHQMolqbeIU5U32rxXRD2ipwoAG63kPw==", "signatures": [{"sig": "MEQCIFty1yFazniGoh5NA8FLwUN/D95eQ2RyDLmZGDOs9wD5AiAm7Hj/PXAMTj/Ofa4Db/OlBhOt0EzCY5/zPr5dBzoJEg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28069}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.8", "jest-util": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "jest-message-util": "30.0.0-beta.8", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.8", "@types/sinonjs__fake-timers": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-beta.8_1749023592234_0.11531676615409125", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/fake-timers", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/fake-timers@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "f0cce98d3329b439f80cb7ff462a22e1a01170f5", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-DPHMBmavXOUPUiddjLZtM4mBH14BUb+4r8jjYflUofEQil+BY5V1qMuLP+PUZtWOoMl3AW5JWXUBDWMka8ZehQ==", "signatures": [{"sig": "MEQCICXVyjbe4KfhrqUkY88kPErnu3m6rB4vJBgK6u074aOaAiBUwepPjaJKdmNtUmapGlGaQBLWHIZng0Wi85Sd9CR5XA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28059}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-rc.1", "jest-util": "30.0.0-rc.1", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "jest-message-util": "30.0.0-rc.1", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-rc.1", "@types/sinonjs__fake-timers": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0-rc.1_1749430965544_0.1820537012158372", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/fake-timers", "version": "30.0.0", "license": "MIT", "_id": "@jest/fake-timers@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "4d4ae90695609c1b27795ad1210203d73f30dcfd", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-yzBmJcrMHAMcAEbV2w1kbxmx8WFpEz8Cth3wjLMSkq+LO8VeGKRhpr5+BUp7PPK+x4njq/b6mVnDR8e/tPL5ng==", "signatures": [{"sig": "MEUCIQCGgAJ/pPO8v7f7MvCYjYk7XX3f5qKOPje49taoAO2KRAIgfX+yyvAGuFolKIlLQXMcbkYXrylcYQqvVrmYOH0SwNc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28027}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0", "jest-util": "30.0.0", "@jest/types": "30.0.0", "@types/node": "*", "jest-message-util": "30.0.0", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0", "@types/sinonjs__fake-timers": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.0_1749521752216_0.2758330798842188", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/fake-timers", "version": "30.0.1", "license": "MIT", "_id": "@jest/fake-timers@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "04501440cfb7b3ff1548ceb82d0459fd8a434f2f", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-H/rYdOcSa+vlux7a3aw6bqQ/nMFMGQqmflAl4qFTThidyakO63ATiHSuhHL1yY39IFBCIbIiUpqr8ognXZA54A==", "signatures": [{"sig": "MEUCIQC8ls5rE7RWaxLdQKJ1jUFWDJgUnTSIONP5ti4tlwc3pgIgDtXPPFHq5cIQ+cB/WH8LVh1TdvDmnNY4T/HhqYv+Ri0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28027}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"jest-mock": "30.0.1", "jest-util": "30.0.1", "@jest/types": "30.0.1", "@types/node": "*", "jest-message-util": "30.0.1", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.1", "@types/sinonjs__fake-timers": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.1_1750285890070_0.9061515743132353", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/fake-timers", "version": "30.0.2", "license": "MIT", "_id": "@jest/fake-timers@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ec758b28ae6f63a49eda9e8d6af274d152d37c09", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.2.tgz", "fileCount": 5, "integrity": "sha512-jfx0Xg7l0gmphTY9UKm5RtH12BlLYj/2Plj6wXjVW5Era4FZKfXeIvwC67WX+4q8UCFxYS20IgnMcFBcEU0DtA==", "signatures": [{"sig": "MEUCIFT/jC/t8FmTWyPdFa2BxM7JGDpBECRg+yzURPzj+RHOAiEAvZWL/FUMv2nYT8EEOzBoP/1YLBfExKjpedtB5u01scU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28027}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-fake-timers"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"jest-mock": "30.0.2", "jest-util": "30.0.2", "@jest/types": "30.0.1", "@types/node": "*", "jest-message-util": "30.0.2", "@sinonjs/fake-timers": "^13.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.2", "@types/sinonjs__fake-timers": "^8.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_30.0.2_1750329979575_0.24438957714232168", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.4": {"name": "@jest/fake-timers", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-fake-timers"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/types": "30.0.1", "@sinonjs/fake-timers": "^13.0.0", "@types/node": "*", "jest-message-util": "30.0.2", "jest-mock": "30.0.2", "jest-util": "30.0.2"}, "devDependencies": {"@jest/test-utils": "30.0.4", "@types/sinonjs__fake-timers": "^8.1.5"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/4.3.0/node@v24.3.0+arm64 (darwin)", "_id": "@jest/fake-timers@30.0.4", "dist": {"integrity": "sha512-qZ7nxOcL5+gwBO6LErvwVy5k06VsX/deqo2XnVUSTV0TNC9lrg8FC3dARbi+5lmrr5VyX5drragK+xLcOjvjYw==", "shasum": "fdd4552541a99826e488fc01afdb7626d6ad46cd", "tarball": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.0.4.tgz", "fileCount": 6, "unpackedSize": 30970, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDkAMO65KUIhEX+HRForWX6Sz4AsP3kg1VbIiH6pEeBfAIgHOpAe09eHdz8slPIFzNa2SP+nON11y8zSYdHDdhu59E="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fake-timers_30.0.4_1751499945512_0.41753884254677986"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T15:00:19.298Z", "modified": "2025-07-02T23:45:45.945Z", "24.2.0-alpha.0": "2019-03-05T15:00:19.610Z", "24.3.0": "2019-03-07T12:59:58.290Z", "24.5.0": "2019-03-12T16:36:44.592Z", "24.6.0": "2019-04-01T22:26:39.122Z", "24.7.0": "2019-04-03T03:55:34.915Z", "24.7.1": "2019-04-04T01:18:53.576Z", "24.8.0": "2019-05-05T02:02:33.638Z", "24.9.0": "2019-08-16T05:56:10.407Z", "25.0.0": "2019-08-22T03:24:09.283Z", "25.1.0": "2020-01-22T01:00:17.257Z", "25.2.0-alpha.86": "2020-03-25T17:16:51.833Z", "25.2.0": "2020-03-25T17:58:41.525Z", "25.2.1-alpha.1": "2020-03-26T07:54:41.645Z", "25.2.1-alpha.2": "2020-03-26T08:10:49.606Z", "25.2.1": "2020-03-26T09:01:35.918Z", "25.2.3": "2020-03-26T20:25:13.651Z", "25.2.4": "2020-03-29T19:38:46.924Z", "25.2.6": "2020-04-02T10:29:22.840Z", "25.3.0": "2020-04-08T13:21:17.358Z", "25.4.0": "2020-04-19T21:50:32.342Z", "25.5.0": "2020-04-28T19:45:27.487Z", "26.0.0-alpha.0": "2020-05-02T12:13:09.731Z", "26.0.0-alpha.1": "2020-05-03T18:48:05.441Z", "26.0.0-alpha.2": "2020-05-04T16:05:33.983Z", "26.0.0": "2020-05-04T17:53:12.114Z", "26.0.1-alpha.0": "2020-05-04T22:16:04.165Z", "26.0.1": "2020-05-05T10:40:53.328Z", "26.1.0": "2020-06-23T15:15:17.268Z", "26.2.0": "2020-07-30T10:11:50.330Z", "26.3.0": "2020-08-10T11:31:55.452Z", "26.5.0": "2020-10-05T09:28:21.629Z", "26.5.2": "2020-10-06T10:52:49.767Z", "26.6.0": "2020-10-19T11:58:42.574Z", "26.6.1": "2020-10-23T09:06:34.785Z", "26.6.2": "2020-11-02T12:51:43.897Z", "27.0.0-next.0": "2020-12-05T17:25:32.485Z", "27.0.0-next.1": "2020-12-07T12:43:33.462Z", "27.0.0-next.3": "2021-02-18T22:10:04.979Z", "27.0.0-next.5": "2021-03-15T13:03:30.666Z", "27.0.0-next.6": "2021-03-25T19:40:09.979Z", "27.0.0-next.7": "2021-04-02T13:48:03.664Z", "27.0.0-next.8": "2021-04-12T22:42:36.693Z", "27.0.0-next.9": "2021-05-04T06:25:16.107Z", "27.0.0-next.10": "2021-05-20T14:11:28.019Z", "27.0.0-next.11": "2021-05-20T22:28:49.677Z", "27.0.0": "2021-05-25T08:15:18.797Z", "27.0.1": "2021-05-25T10:06:40.124Z", "27.0.2": "2021-05-29T12:07:26.099Z", "27.0.3": "2021-05-29T17:47:38.323Z", "27.0.5": "2021-06-22T11:10:42.740Z", "27.0.6": "2021-06-28T17:05:48.089Z", "27.1.0": "2021-08-27T09:59:43.770Z", "27.1.1": "2021-09-08T10:12:18.363Z", "27.2.0": "2021-09-13T08:06:48.438Z", "27.2.2": "2021-09-25T13:35:10.011Z", "27.2.3": "2021-09-28T10:11:24.020Z", "27.2.4": "2021-09-29T14:04:50.915Z", "27.2.5": "2021-10-08T13:39:24.082Z", "27.3.0": "2021-10-17T18:34:48.058Z", "27.3.1": "2021-10-19T06:57:35.043Z", "27.4.0": "2021-11-29T13:37:18.552Z", "27.4.1": "2021-11-30T08:37:14.469Z", "27.4.2": "2021-11-30T11:53:51.419Z", "27.4.6": "2022-01-04T23:03:40.125Z", "27.5.0": "2022-02-05T09:59:27.203Z", "27.5.1": "2022-02-08T10:52:23.713Z", "28.0.0-alpha.0": "2022-02-10T18:17:37.572Z", "28.0.0-alpha.1": "2022-02-15T21:27:01.853Z", "28.0.0-alpha.2": "2022-02-16T18:12:11.118Z", "28.0.0-alpha.3": "2022-02-17T15:42:24.813Z", "28.0.0-alpha.4": "2022-02-22T12:13:56.915Z", "28.0.0-alpha.5": "2022-02-24T20:57:22.069Z", "28.0.0-alpha.6": "2022-03-01T08:32:26.529Z", "28.0.0-alpha.7": "2022-03-06T10:02:42.633Z", "28.0.0-alpha.8": "2022-04-05T14:59:54.414Z", "28.0.0-alpha.9": "2022-04-19T10:59:16.863Z", "28.0.0": "2022-04-25T12:08:12.064Z", "28.0.1": "2022-04-26T10:02:41.536Z", "28.0.2": "2022-04-27T07:44:04.917Z", "28.1.0": "2022-05-06T10:48:55.949Z", "28.1.1": "2022-06-07T06:09:38.305Z", "28.1.2": "2022-06-29T10:33:56.954Z", "28.1.3": "2022-07-13T14:12:30.536Z", "29.0.0-alpha.0": "2022-07-17T22:07:09.363Z", "29.0.0-alpha.1": "2022-08-04T08:23:31.431Z", "29.0.0-alpha.3": "2022-08-07T13:41:37.535Z", "29.0.0-alpha.4": "2022-08-08T13:05:36.039Z", "29.0.0-alpha.6": "2022-08-19T13:57:50.870Z", "29.0.0": "2022-08-25T12:33:29.599Z", "29.0.1": "2022-08-26T13:34:43.255Z", "29.0.2": "2022-09-03T10:48:21.071Z", "29.0.3": "2022-09-10T14:41:39.464Z", "29.1.0": "2022-09-28T07:37:42.607Z", "29.1.1": "2022-09-28T08:05:32.994Z", "29.1.2": "2022-09-30T07:22:51.001Z", "29.2.0": "2022-10-14T09:13:51.574Z", "29.2.1": "2022-10-18T16:00:15.062Z", "29.2.2": "2022-10-24T20:24:07.301Z", "29.3.0": "2022-11-07T17:55:48.530Z", "29.3.1": "2022-11-08T22:56:24.660Z", "29.4.0": "2023-01-24T10:55:54.221Z", "29.4.1": "2023-01-26T15:08:38.678Z", "29.4.2": "2023-02-07T13:45:31.969Z", "29.4.3": "2023-02-15T11:57:27.470Z", "29.5.0": "2023-03-06T13:33:33.141Z", "29.6.0": "2023-07-04T15:25:48.602Z", "29.6.1": "2023-07-06T14:18:27.797Z", "29.6.2": "2023-07-27T09:21:32.821Z", "29.6.3": "2023-08-21T12:39:18.191Z", "29.6.4": "2023-08-24T11:10:26.109Z", "29.7.0": "2023-09-12T06:43:45.397Z", "30.0.0-alpha.1": "2023-10-30T13:33:13.513Z", "30.0.0-alpha.2": "2023-11-16T09:28:29.908Z", "30.0.0-alpha.3": "2024-02-20T11:09:10.521Z", "30.0.0-alpha.4": "2024-05-12T21:43:29.384Z", "30.0.0-alpha.5": "2024-05-30T12:44:08.667Z", "30.0.0-alpha.6": "2024-08-08T07:43:07.433Z", "30.0.0-alpha.7": "2025-01-30T08:28:34.556Z", "30.0.0-beta.2": "2025-05-27T01:23:13.831Z", "30.0.0-beta.3": "2025-05-27T01:27:48.549Z", "30.0.0-beta.4": "2025-05-27T07:04:28.017Z", "30.0.0-beta.5": "2025-05-29T00:30:10.906Z", "30.0.0-beta.6": "2025-06-03T23:50:49.761Z", "30.0.0-beta.7": "2025-06-04T03:35:43.433Z", "30.0.0-beta.8": "2025-06-04T07:53:12.402Z", "30.0.0-rc.1": "2025-06-09T01:02:45.733Z", "30.0.0": "2025-06-10T02:15:52.538Z", "30.0.1": "2025-06-18T22:31:30.315Z", "30.0.2": "2025-06-19T10:46:19.716Z", "30.0.4": "2025-07-02T23:45:45.692Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-fake-timers"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}