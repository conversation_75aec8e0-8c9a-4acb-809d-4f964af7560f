{"_id": "@humanfs/core", "_rev": "17-a7b6867243e077e988c854643138a9cd", "name": "@humanfs/core", "dist-tags": {"latest": "0.19.1"}, "versions": {"0.6.0": {"name": "@humanfs/core", "version": "0.6.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.6.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "4add5b64eb443d52be2016471fb64e4830bc070a", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.6.0.tgz", "fileCount": 7, "integrity": "sha512-nQzHE7DX38RFuHAHJadj3hFC3xsevHhH+kotyhd0uwuJZv0CVkfdfntQuTX78ih+KxL9t6Xo2uP0CMrcosyvnw==", "signatures": [{"sig": "MEUCIGVCXNXwmvwV4GCHsGF+u84Whl92rM7VCKW9ag9aFqDVAiEAw3XYgF8uAwS4YcchlHGxvYF5zWelJ2uqlqnwQIwcm/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35005}, "type": "module", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "ac91f0ea54f2f4b5644ff5383fa7cb8bbeda65f3", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.6.0_1706574871801_0.9578446219623902", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "@humanfs/core", "version": "0.7.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.7.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "6ba636c8aac3add152749e2a8af66918dd8bf4a7", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.7.0.tgz", "fileCount": 9, "integrity": "sha512-9PAxAFlf3HgzvD3OCMBTpg3gUyM4GQld2qlQcZzy+hAOkQ3/9W4x32oJrzx4zVGU4uLRMtGmdht1ONti/BXvmQ==", "signatures": [{"sig": "MEUCIQCyDqpU3c4hfdB6k9IRKlmLHF7nwUujjw6mN9xQwJebHAIgDqIiO+2r8sn7rbMDqvRNITzqyWWVBOxGFpuWQ7z6W1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41697}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "fecc5744622a96af6d7689c6338db49241191ccf", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.7.0_1706745087492_0.7550270262132199", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "@humanfs/core", "version": "0.8.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.8.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "951f7b683796ab9e96feba567440d74c3ec98069", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.8.0.tgz", "fileCount": 9, "integrity": "sha512-vhCNS3Zy8zbRetiY5YNFLJEngq8k+92q4qcXU5kI/0Yq912HpE8bSL6KKmXB7xI/5dpVt6BZehEoQIrD/zx4zA==", "signatures": [{"sig": "MEUCIQC47COgVr0l22gUr815T0K42xjCLRhMJos+aai6eHGUMQIgW9vl5Ez9AxQJm5Og2rpovn+sfuU5gWJ2dn+Y7Uq4jcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43013}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "1afb8a9b48ea6eb0e5fe94f88951d60e98b80891", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.8.0_1707264175966_0.14124972356509247", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "@humanfs/core", "version": "0.9.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.9.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "8ab68b02dcb186e6938d40030dbf88f18bc8ea2e", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.9.0.tgz", "fileCount": 9, "integrity": "sha512-mkETiOiOn+s5dvkbxbbYlAefeIQDHCPEqruimB2GHd/R+Kauux9Wv9I9G2NaPrX+rdhvCmgq6Czu1OBpV+DQig==", "signatures": [{"sig": "MEUCIQDwxsY13DpXmUQ8IZ3EmlIjSNeSKI63wxq30hgdg5iV1QIgGlBsXNjCOcfSXv+K0Obkoax++wN6MzA4MN/493VUxhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44043}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "98ad9217bec071f0530de68742771591e004e2f8", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.9.0_1707434224462_0.7202642682693898", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "@humanfs/core", "version": "0.10.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.10.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "ea7a46052dec03e49812a0c229872b72ade15036", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.10.0.tgz", "fileCount": 9, "integrity": "sha512-lzFOygT529JPNpBQrgULYMesm5nqbqFI+Sdv7LdFM4jiTtDY4NIlVWilt5J3z0tEoiBJRUfGaqXZW7IiQHOF4Q==", "signatures": [{"sig": "MEYCIQDpvEHjmI4xFvsitl4JaLROow2FZTeFFRUU+eWlEpxPywIhANTcUxxCI3N20Q75TIMW9AuIK3V6LYEOD9DgKp33F3Dr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45218}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "9ef50654fa4cab2bf4f3104dec49189972df2fdc", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.10.0_1707513470188_0.8441781175468765", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "@humanfs/core", "version": "0.11.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.11.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "e4ec0c8e1f702fa96cbcb628e6ef40ffcf24e3df", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.11.0.tgz", "fileCount": 9, "integrity": "sha512-T8sVP2Xqbm3sfPW1NFaPrsGVKYbHj2tiF5WdaadRv4kCmxsb23Zrv2WUSLhhZWQGPUHnylLt7gtvf0OOy4mopQ==", "signatures": [{"sig": "MEQCIG/PCyFw1hjrPQEH7Xhd7VyT8EEsIO3URfn1fdS83GCmAiACIwhZ+OQzfL8gVnjSvtF6Tdq8MWt2q8hUI+tn0tJ5Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49228}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "ca9bd8a11f2f9a9c3e3cf28a037b2838dcb32e1e", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.11.0_1707870651367_0.4578413221457609", "host": "s3://npm-registry-packages"}}, "0.12.0": {"name": "@humanfs/core", "version": "0.12.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.12.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "80aae3c926d3d3a395081d49572be6864a71b457", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.12.0.tgz", "fileCount": 9, "integrity": "sha512-Vh4EoEItKuZLscUPP4td0X+zo2dlZ/XfjBqEF4UPPRfeXdhzGmpBr+77JfxLerlN/+cVTDMlK8/VKDbMAz1YKA==", "signatures": [{"sig": "MEUCIEIAuGpucuDXOD1URVxuF8FWwLqIarmg2qWMeLei0bvqAiEA0Y4xuNboLGuLLtAeTVeU9UK+sF4bcEKOr7WhjStLa6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50200}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "4d388e438d4432f200e0962fed25a6854d65b743", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.12.0_1708378876045_0.7954678924829361", "host": "s3://npm-registry-packages"}}, "0.13.0": {"name": "@humanfs/core", "version": "0.13.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.13.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "de5102e6b6050ecac85be46850ddf789c86c2a5e", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.13.0.tgz", "fileCount": 11, "integrity": "sha512-r1xkIPoyULrRZrdCK1WOGgGXu2R7AlR7PLIFvGSfsMMnRs2ZfVfIpTUqhJ2/y4i/C6Y3KUCx/lNkGgLMa/7uvA==", "signatures": [{"sig": "MEUCIQDAgnXRZW1AQaCLJTOZuH5JLZYm1LWxuJpvvTzvckiKGQIgME9QfSMmv3SCEkYt8FnRu2N7yv7CHEjmqwXGc9w6EBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55659}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "33a6cd225abbac9fadfb8d2bedb413313a0157ba", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.13.0_1708736305119_0.6693607959551766", "host": "s3://npm-registry-packages"}}, "0.14.0": {"name": "@humanfs/core", "version": "0.14.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.14.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "5fc8dcd165d5f3259c49643b91bd389801b154cf", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.14.0.tgz", "fileCount": 11, "integrity": "sha512-MiEIW6N7UGWbm6BhkLysBVOo/Quc6VQVpkLLrIgjAQD0NjTdaSj3Ja4eQqGnkJdXFVUhcGsTNorYU5lytM6Ipg==", "signatures": [{"sig": "MEUCIH3zz7R9KEXdRMCibpcvTp5yCb++RzT/QkUIhU5E0a9lAiEA2PJ1mWmFWNHRH5m9oVnMDE6fyazBQJvw6A6l6GgFSVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57320}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "548a289733b023f0d30c6ce44d5c9327aabd105f", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.14.0_1708992252627_0.45051304124767433", "host": "s3://npm-registry-packages"}}, "0.15.0": {"name": "@humanfs/core", "version": "0.15.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.15.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "3ae53ee73267777ca44f8eec567af43215a3ea62", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.15.0.tgz", "fileCount": 11, "integrity": "sha512-7CQo6Cjq5nmRFGs5/ooZKxyHd2cyUhbtTnbdTjs9PmMTh0/N2MK/RzhZTvxPqoIYelnmRauqdZYCtZeMvEpmzA==", "signatures": [{"sig": "MEQCIGFO3Nk2R1E5vc0CnJDUOgiMQpCqMTaFvJTJdsAZLKFIAiAX8RZk/1Y/m2XIKc3qSQUb0m7CcOnQoalt+DkstSEHng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59883}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "210e258aae0c35fd1b9232e02bd9243f087eba49", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.15.0_1709166360287_0.10886863865275198", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@humanfs/core", "version": "0.16.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.16.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "21dd45c6d49c63622f416c5cf2ca2cf2f48b51d1", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.16.0.tgz", "fileCount": 11, "integrity": "sha512-270JYadVkvX/TsDG7cqsYXn7ghhCU1n2zeCdFk4Qfjmr7RV/l/ILvPEwN2K4KtLzWvEnFgZjWR9v3M2KqBoe/Q==", "signatures": [{"sig": "MEYCIQDF05RhjUGogJZR/WqBkDgWdNlVnoB3RJORQkLsSuyfSAIhAMBQ3bOZ3q2QuAE/Qfkis4vXWpuYLppT000+ktz6EqO7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59859}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "12f28be00315db42c54ba4c41c2aba729fadc26c", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.16.0_1710356507657_0.6494653273485196", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@humanfs/core", "version": "0.16.1", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.16.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "e8c06bbace6cbfe9b399e5a638027c7f08a20b3a", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.16.1.tgz", "fileCount": 11, "integrity": "sha512-p+8+MT6DZZlvcbDMD9MoMBFG9c1ybP0M/gs90NoC066MY8QlAqGfpIw19fpsvOFONNz7KjxfhHMki1IsrWdo6Q==", "signatures": [{"sig": "MEYCIQDOowTD9IK5wunLAwvMFK4I/xt07D/wEeYotrRf1kARPgIhANsOpT9Inx/NVEc7TN5T2tY7CvYejbDVflPxUTo6zBD3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59859}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "121c3e482f94814f363114fbc3b946d151c6b235", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.16.1_1710442848310_0.08830412343439598", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@humanfs/core", "version": "0.17.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.17.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "8b7b788b2a2f58c482acfedcb7be9d3c623e26ce", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.17.0.tgz", "fileCount": 11, "integrity": "sha512-ZAK7jtcVRwPiRZdT+W2ZaGV1oxVQK8IXvKrdpRkrDCVniKXNmqL+k+r0NhWKk9k9Nwm1yrCqr8utaa24CVK1OQ==", "signatures": [{"sig": "MEUCIQCwm6AXpHu+zjjDFBHdlJXjscMU5hXhuQQOHPcNRy4ogQIgUQSMk70ArSlxeJFJ6EHQ9EqhWKL7MhCX3rab/tkScTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60413}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "08722e974e21766b1f75631f3035f0569b6cffea", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.17.0_1710957511290_0.31717156819892645", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@humanfs/core", "version": "0.18.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.18.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "e062044a6acfab99c772367ecbc9d9c0f3da981e", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.18.0.tgz", "fileCount": 11, "integrity": "sha512-Squx1Xw4MMSu7fCi4c7Gd7Xt4yOliEpUpUNohQobvboEnRZ18ifNHjJwy+6hzJIp83LdxVZ01LpxBRnyf/BLTA==", "signatures": [{"sig": "MEUCID0w8OdFkPQ08qinSaPvK4YwzDxe9dH7BBpxBnKXXJGjAiEA567S/kagUK8r/JnCPZbb8dDRByuAprBP5xZUCMg2bt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64570}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "a4fce8ae31e4a163dd90052d5dc615e1072cd208", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.18.0_1718214547319_0.36190642648496385", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@humanfs/core", "version": "0.18.1", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.18.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "df3f1920819775d8e24ea397d4a8c835df1f95f6", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.18.1.tgz", "fileCount": 11, "integrity": "sha512-cTVva6ZPNZQg+zhqnXjqDJwwXqSo8b0/MeynGRGHbIdd8RTXjeSLumOoLynD+zUvYp7yp4uLgAoslk3ttNl0uw==", "signatures": [{"sig": "MEQCIF1Ho6o++75lxYsMYKB3pIZhrvtLOsrzDPYTDsZSl54mAiBPQXqxRvx08WQlt98cpqBj2z3AIRGT3i7LslbMAS4GJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64597}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "e08b2db62c8739e631dd3c915875d76bbc488ba3", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.18.1_1718215626926_0.19588637507388107", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@humanfs/core", "version": "0.18.2", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.18.2", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "cae8ac37e3249959a928a9b4da4a21b9c1f3fe44", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.18.2.tgz", "fileCount": 11, "integrity": "sha512-2ZFEE4hN5cXBwDgyYUI9M2PMTaB5iP7BsRVl6pHbu+OOOHn6LdO+cjtrm5bBwjKZkJTq6TEpEAH1Tm/7T4aDhA==", "signatures": [{"sig": "MEQCIHzEygG0iiXoLeSAaEBuRbPtgyyy8WOz/Tjr5bB8/MA2AiAL4jQ+sCc7S4617ou18WsCYoPM7BmbfAU2R7ADnnkYbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64329}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "4baeedc39f7665d67e971c2021d699884d6d4e96", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.18.2_1718293898690_0.269901189946824", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@humanfs/core", "version": "0.19.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/core@0.19.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "08db7a8c73bb07673d9ebd925f2dad746411fcec", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.19.0.tgz", "fileCount": 12, "integrity": "sha512-2cbWIHbZVEweE853g8jymffCA+NCMiuqeECeBBLm8dg2oFdjuGJhgN4UAbI+6v0CKbbhvtXA4qV8YR5Ji86nmw==", "signatures": [{"sig": "MEYCIQDpEBcdgNaitAYye0ntpYs6IfwpLEuuBYGhIE8Laq69FgIhALCS0CPmlJDp7dRWFplKbOlaCjALx882ydM4y3JZTzXh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72467}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "85069f193cbacc371c3beb649ce746f8c5a493be", "scripts": {"test": "c8 mocha tests", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.4.0", "description": "The core of the humanfs library.", "directories": {}, "_nodeVersion": "20.13.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2", "@humanfs/types": "^0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.19.0_1725911065001_0.8895859795475796", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@humanfs/core", "version": "0.19.1", "description": "The core of the humanfs library.", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "scripts": {"build": "tsc", "prepare": "npm run build", "pretest": "npm run build", "test": "c8 mocha tests"}, "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/humanfs.git"}, "publishConfig": {"access": "public"}, "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "homepage": "https://github.com/humanwhocodes/humanfs#readme", "engines": {"node": ">=18.18.0"}, "devDependencies": {"@humanfs/types": "^0.15.0", "c8": "^9.0.0", "mocha": "^10.2.0", "typescript": "^5.2.2"}, "_id": "@humanfs/core@0.19.1", "gitHead": "514883417ddb4880179b4000d874d2d764e30f2d", "_nodeVersion": "22.9.0", "_npmVersion": "10.4.0", "dist": {"integrity": "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==", "shasum": "17c55ca7d426733fe3c561906b8173c336b40a77", "tarball": "https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz", "fileCount": 12, "unpackedSize": 72735, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB3b20eOAYRinW1zzvD+RxyGsywllcdBQvBYI0Yp9ZbQIhAOYNzfk58oMYBJVKxugcQ8JlwUCfHvDdRFFJUiMjmO/w"}]}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/core_0.19.1_1730123750762_0.3628290915081207"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-01-30T00:34:31.713Z", "modified": "2024-10-28T13:55:51.149Z", "0.6.0": "2024-01-30T00:34:31.961Z", "0.7.0": "2024-01-31T23:51:27.656Z", "0.8.0": "2024-02-07T00:02:56.201Z", "0.9.0": "2024-02-08T23:17:04.615Z", "0.10.0": "2024-02-09T21:17:50.408Z", "0.11.0": "2024-02-14T00:30:51.519Z", "0.12.0": "2024-02-19T21:41:16.357Z", "0.13.0": "2024-02-24T00:58:25.311Z", "0.14.0": "2024-02-27T00:04:12.794Z", "0.15.0": "2024-02-29T00:26:00.755Z", "0.16.0": "2024-03-13T19:01:47.837Z", "0.16.1": "2024-03-14T19:00:48.492Z", "0.17.0": "2024-03-20T17:58:31.457Z", "0.18.0": "2024-06-12T17:49:07.499Z", "0.18.1": "2024-06-12T18:07:07.124Z", "0.18.2": "2024-06-13T15:51:38.862Z", "0.19.0": "2024-09-09T19:44:25.149Z", "0.19.1": "2024-10-28T13:55:50.943Z"}, "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/humanwhocodes/humanfs#readme", "keywords": ["filesystem", "fs", "hfs", "files"], "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/humanfs.git"}, "description": "The core of the humanfs library.", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "readme": "# `@humanfs/core`\n\nby [<PERSON>](https://humanwhocodes.com)\n\nIf you find this useful, please consider supporting my work with a [donation](https://humanwhocodes.com/donate) or [nominate me](https://stars.github.com/nominate/) for a GitHub Star.\n\n## Description\n\nThe core functionality for humanfs that is shared across all implementations for all runtimes. The contents of this package are intentionally runtime agnostic and are not intended to be used alone.\n\nCurrently, this package simply exports the `Hfs` class, which is an abstract base class intended to be inherited from in runtime-specific hfs packages (like `@humanfs/node`).\n\n> [!WARNING]\n> This project is **experimental** and may change significantly before v1.0.0. Use at your own caution and definitely not in production!\n\n## Installation\n\n### Node.js\n\nInstall using your favorite package manager for Node.js:\n\n```shell\nnpm install @humanfs/core\n\n# or\n\npnpm install @humanfs/core\n\n# or\n\nyarn add @humanfs/core\n\n# or\n\nbun install @humanfs/core\n```\n\nThen you can import the `Hfs` and `Path` classes like this:\n\n```js\nimport { Hfs, Path } from \"@humanfs/core\";\n```\n\n### Deno\n\nInstall using [JSR](https://jsr.io):\n\n```shell\ndeno add @humanfs/core\n\n# or\n\njsr add @humanfs/core\n```\n\nThen you can import the `Hfs` class like this:\n\n```js\nimport { Hfs, Path } from \"@humanfs/core\";\n```\n\n### Browser\n\nIt's recommended to import the minified version to save bandwidth:\n\n```js\nimport { Hfs, Path } from \"https://cdn.skypack.dev/@humanfs/core?min\";\n```\n\nHowever, you can also import the unminified version for debugging purposes:\n\n```js\nimport { Hfs, Path } from \"https://cdn.skypack.dev/@humanfs/core\";\n```\n\n## Usage\n\n### `Hfs` Class\n\nThe `Hfs` class contains all of the basic functionality for an `Hfs` instance *without* a predefined impl. This class is mostly used for creating runtime-specific impls, such as `NodeHfs` and `DenoHfs`.\n\nYou can create your own instance by providing an `impl` directly:\n\n```js\nconst hfs = new Hfs({ impl: { async text() {} }});\n```\n\nThe specified `impl` becomes the base impl for the instance, meaning you can always reset back to it using `resetImpl()`.\n\nYou can also inherit from `Hfs` to create your own class with a preconfigured impl, such as:\n\n```js\nclass MyHfs extends Hfs {\n\tconstructor() {\n\t\tsuper({\n\t\t\timpl: myImpl\n\t\t});\n\t}\n}\n```\n\n### `Path` Class\n\nThe `Path` class represents the path to a directory or file within a file system. It's an abstract representation that can be used even outside of traditional file systems where string paths might not make sense.\n\n```js\nconst myPath = new Path([\"dir\", \"subdir\"]);\nconsole.log(myPath.toString());\t\t// \"dir/subdir\"\n\n// add another step\nmyPath.push(\"file.txt\");\nconsole.log(myPath.toString());\t\t// \"dir/subdir/file.txt\"\n\n// get just the last step\nconsole.log(myPath.name);\t\t\t// \"file.txt\"\n\n// change just the last step\nmyPath.name = \"file.json\";\nconsole.log(myPath.name);\t\t\t// \"file.json\"\nconsole.log(myPath.toString());\t\t// \"dir/subdir/file.json\"\n\n// get the size of the path\nconsole.log(myPath.size);\t\t\t// 3\n\n// remove the last step\nmyPath.pop();\nconsole.log(myPath.toString());\t\t// \"dir/subdir\"\n\n// iterate over the steps\nfor (const step of myPath) {\n\t// do something\n}\n\n// create a new path from a string\nconst newPath = Path.fromString(\"/foo/bar\");\n```\n\n## License\n\nApache 2.0\n", "readmeFilename": "README.md"}