{"_id": "@babel/compat-data", "_rev": "118-57f6a2511d0000d794f13e0f51c376e3", "name": "@babel/compat-data", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.8.0": {"name": "@babel/compat-data", "version": "7.8.0", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.8.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "1d3a9fbda3c58774395cd617d263a196dfa45d92", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.8.0.tgz", "fileCount": 11, "integrity": "sha512-ixPUWJpnd9hHvRkyIE3mJ6PY5DEWmR08UkcpdqI5kV5g/d6knT8Wth1LE5v5sVTIJkm9dGpQsXnhwxcf2/PjAg==", "signatures": [{"sig": "MEQCIF966nSO5UjZgVm8lQrXdWUjawqODHSFmDirlVB3y5wIAiBWQs9HeNKK83lPGugATLwzkLZSjtsL+KC2gRWoIf7mMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmU8CRA9TVsSAnZWagAA1hEP/0i/ZX9OEoZehfYDaRIo\nXKviwn4kFGI3v2jNsOPGz7KxHZM5ZwNqjiWijQ4U2+DzFxPU4RaFNf8ekIEu\nvTo/Zf2eTM+q6nAHEuqngEnS+f7dn+VDIsYaH+rlkqLRWa/E5VkzdfrWyn8B\n8crt32NpM6th8cYPHnVEEpaNp9l2rLg548/G1i6n9jeXIZWThKWXzHhKwnKt\nbAWteiFIyXqM+MSY1N7E21CD7t9MMXz7w2rwj3yZxzaIyda8heN9XwelvUBB\nd3E67VG+4bqIPKgv2IdE/xe8UM0IpIInYhHdODgKaE+oJqcsHgw4j+4LTtm0\nJeYHRQpQtf+iBA5A9tg96TAHWapQJHtyQ8zcJHbCHqnsXUspuVh+a1j9n68r\nmZM2/ICRA4Apo4DIr9ULQbVZYQIQUy8bBQt4XLqPHLqoAgOqDXWMMIKXu5BH\nebTRWT/9TL9Xg4r4ByoSuqixeP7QbdlufhDADAseyVRowoOsbIWNdOMYHaI+\n1ooRT9rXfiA88Qjvf+AXwYwYBq1sm35JhXHn63j6z7Dl04oYpymcrZp2VKMH\nMaSe8w9IA/HwEa3cj6sQ6n9ZLWqy3L0htk0vSRlryVfHpAZOSzc/inuG1CfS\nfsJqLc6ch+0ZlU6FHrDCpgzstCgvmvYxDjE9furKRHUwrBwldoiQaAn8enhL\nkTzX\r\n=CwkK\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-overlapping-plugins.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"semver": "^7.1.1", "invariant": "^2.2.4", "browserslist": "^4.8.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30000969", "electron-to-chromium": "1.3.113", "@babel/helper-compilation-targets": "^7.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.8.0_1578788155674_0.15603541915049468", "host": "s3://npm-registry-packages"}}, "7.8.1": {"name": "@babel/compat-data", "version": "7.8.1", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.8.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "fc0bbbb7991e4fb2b47e168e60f2cc2c41680be9", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.8.1.tgz", "fileCount": 11, "integrity": "sha512-Z+6ZOXvyOWYxJ50BwxzdhRnRsGST8Y3jaZgxYig575lTjVSs3KtJnmESwZegg6e2Dn0td1eDhoWlp1wI4BTCPw==", "signatures": [{"sig": "MEYCIQCcI2ftjGbx/4+PHG86pa3GxHN+30Q962jBzzq1FZCzUwIhAPDne4hSZ05ObivAnyl7VJqSMmXgehm6c21en2Fq9UoM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGxj0CRA9TVsSAnZWagAAZu0P/jXlbwR4qJKWqq3k3vm7\nyUwuV4WGqKTFy0ETSWFA4BRRZcbcneWN2sepWHZeYuOdp/l+s+oNUosJnMA2\nvIXQGSMYM6uxq0O0K4pcIsfmT1llUWj0sYzcLgMphBaYIZkD8/DgsE5enJft\nfa69iHjyfpUcQEPXykt/AymHcjEA8lMlih/dvcs78SSiXPEZl4pEjVBjoz1N\nuvhVCjgc7j18DTlGSC/kC7OAnA/bowq1zUW1zPbA9/ZFBRcq404cUpPMBHJ5\nDDtIZL6R4UVaFGSQpWswYXehEGb3GjgRNTYH/Y84CJuUh1SOOGG38ee3ZbqF\nyWhwQvjPuXkuUiGCTMBeeyYMbshv4GdAN98X2AcoUPJt2DZORwTaDxSxDshy\nRJnLFQeC7tI2PVDkA6VPA7lZx/fyJBmTI5yaZUVzPEGYnrnX6TUXQwi1K/yL\nVxW4XlGRtUmtOcMQrHdNMsjPL14VLvJfrYzz4xPGUnb+xdRHJEQzqSVmkqPK\ncDoVLbXMTcB8jh7HHA747Vr9QpA52lMhwAx3u77K0GlyRqjrrlq4axv4aJGF\nF75jRQNUemQbWeVXKdbeU8JGROMahSXjizN8zQgWSj+sLjipnOt/m0kF4M1/\nvJlbICxRqUCJTPIuVBZ+8gJif5wVmwPYFfVKToTnMOx/9phwnVoxyZYj7Kim\nHHhB\r\n=PiXK\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json"}, "gitHead": "575eeb370efd1a234bd7f124dc6b1f9161f0c161", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-overlapping-plugins.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.8.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30000969", "electron-to-chromium": "1.3.113", "@babel/helper-compilation-targets": "^7.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.8.1_1578834163698_0.010993395842945786", "host": "s3://npm-registry-packages"}}, "7.8.4": {"name": "@babel/compat-data", "version": "7.8.4", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.8.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "bbe65d05a291667a8394fe8a0e0e277ef22b0d2a", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.8.4.tgz", "fileCount": 54, "integrity": "sha512-lHLhlsvFjJAqNU71b7k6Vv9ewjmTXKvqaMv7n0G1etdCabWLw3nEYE8mmgoVOxMIFE07xOvo7H7XBASirX6Rrg==", "signatures": [{"sig": "MEYCIQCVqvTFMSB9HZzeMPGSUAUiS02HdRh/sD8iDalU5RRLfgIhAOyiCtNWdegrXAdRUlNJ8BpQ+5VrxxR/pIjcj8Zip4Ee", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13119336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs4GCRA9TVsSAnZWagAAUMIQAKTzG8xGhHEp53hiGAzS\n8zNEmdMTG8sCDnMHQ14o+GSQMG1LORvzC21xhmsBx33EWUszeFCSCDUy7tBM\nhcpbfQNeIFRLwDlxn/Clv5otjsg5jPjBPBvVBP1A0wndojJMQT2W+XL3AZuu\nexcltOHAbXU3cMUNTOhTRRdKSnW2/OG5E2StwB4dZh8snNcWQKAE5/VrvcD/\naHUe02IwPmfCyWoOsbC8pNxQyhKayEUCvK0d4dWF5Q9bRtSXprvn5klswgH4\nnAfR8vcIo+sfgflwMLKEw+DqbfX5wuhRZq6xvGce2ay/zYv81C9gACRIF7ta\nixP80Q6o6UNNqWf9UuLpwYqxGD05qMcDhOTNEFoyYpBvcB2BvM/bjzvCYfur\nn4ApDYDY5Ki62yRXcyZgeeF1HyRRc0lkEYjt9d2BVPxekRjLChVhBpHPH37e\nEIEAUGFCPEbmJpwRC3c1CjpLSjZHC3n9k92FziS6daiSIn/DY+ORREMI9ffG\nKyAZwCNMK8J7hV4RBSBYfGXiiampxEGMBHiQ49misDwGB0M90eU46Aq/+yiP\nWz8wG3J28oHjdZYUwivtihe/RUdWocVpBpXfPA9nnVtNnrR9e/JDDy7F7fQ2\n+wriUSfrfuPuWEpGE6HIaIQudvMIHXJX8T8qp8hb9EtCRTChQaaDi+RTqQuP\nQTCG\r\n=zTaA\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json"}, "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-overlapping-plugins.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001023", "electron-to-chromium": "1.3.113", "@babel/helper-compilation-targets": "^7.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.8.4_1580387846415_0.8349317887524972", "host": "s3://npm-registry-packages"}}, "7.8.5": {"name": "@babel/compat-data", "version": "7.8.5", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.8.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d28ce872778c23551cbb9432fc68d28495b613b9", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.8.5.tgz", "fileCount": 10, "integrity": "sha512-jWYUqQX/ObOhG1UiEkbH5SANsE/8oKXiQWjj7p7xgj9Zmnt//aUvyz4dBkK0HNsS8/cbyC5NmmH87VekW+mXFg==", "signatures": [{"sig": "MEYCIQDB8WE+82M49TUvNzZnE2mG0gR77n8E9GkStfvb7wY7PQIhAJ9G9GVQ6oV5XXFmohgyTRrP/WdnnK91WMvQdu8CewkS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNHc7CRA9TVsSAnZWagAAbBwP/1M2U4Y+NVj2hnzpk9Sm\n08B5osfK1RDH9QQI5l07BEjNpiCE7PnRMaL96E1wlBWdaT78q2dFfLg7kT4G\nOyn7zECO7S9/qepaFDIfiX574s82AkviwBTBALFNpSflNchAtRb3Qk2DIV+E\nOqGfYbFyC/ePl5m9GwN8JeX/d7rlUUX+eLvSyfM9hWvL6yrD6G9j68Y2l0wO\naOrKpoFo4+L74CDOUP7yLnGmXVRmOqs5JRQvf+tVP3svfOMDTqYfwMUKjGw/\nnBzAtbVXoTKn2EwzKYsGb1V0kzk5CWbiXlUFTyCsAXYgeE//nzr5odwzzq/z\nUXyJAO34zYlE06bowQjY+YuCQXhYNR9rjOBpx5d8kPIvcF5srROYzCMzzi8k\nbMdoi+096m7AgyO08B8eSRi35fGIDXDDii6hqtUupPbwSwS/LGSv0Q1/ARSj\nbY5BxlMnNOfQIqwT23vqNCKuT4FYbqY6+0lvfQdpkP4+dGdng0/A49X9YDmj\nUNlCaX4gCmGv3aMuccBwcLsc1OIlItmIowiY13sRWtyQZWIa50DwGld9JO8G\n6jdVdT5116zrDz0XcnMgsZN4vyFibMU8V0PG1q6neRgr7Pb+4di33G36l2li\n+0MZPeglWQ79lbLa+fllZg+RZhIuE4PKWwBo4kFpHtax2J5UVYZAHjQw1znn\ncrvq\r\n=t+HE\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json"}, "gitHead": "11641b5f70d8f4cb0b165f46c10a961f54b01863", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-overlapping-plugins.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001023", "electron-to-chromium": "1.3.113", "@babel/helper-compilation-targets": "^7.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.8.5_1580496698714_0.2190311580885702", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/compat-data", "version": "7.8.6", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "7eeaa0dfa17e50c7d9c0832515eee09b56f04e35", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.8.6.tgz", "fileCount": 12, "integrity": "sha512-CurCIKPTkS25Mb8mz267vU95vy+TyUpnctEX2lV33xWNmHAfjruztgiPBbXZRh3xZZy1CYvGx6XfxyTVS+sk7Q==", "signatures": [{"sig": "MEQCIE0eL9xkCQEiUVf22wZEh+dDi7WesNBAfDV74vwE4cQnAiBTwvAOSCvvr1x0OUE/BYa8hTuHvg+Bh/zZEyw7aSQBMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RBCRA9TVsSAnZWagAAafQP/35CMKA3C7PhDUYbwDVA\nn4KyleiQBIJc7Vf8On6VSmLNWVGfaP3gZ+FN8vqwUTGDIyZ/lgiAAnJmjmJ/\nDR85olr7mDVM7DpzW4xQxj1qYk4F8u7lxyEw4j8rhapDjWtxLYlnSVhJjzdk\n0fxV+2ln4iBy2FA3rgM+vfDjMUIdSTh02DOdXjkeF5K2NJIpbqocsprQrzzf\n82npttaZ5rUlR9fr1/T7dMrcvZzspkj7WT78zeeAfGdydAjRMUQUOSWehVk9\n23yjIxPiWRQVO72VQ6oMizeQ33cwT+kOkIuWyzKFYuO6TVXuZTo3wVZfDpaE\n5Zcd1qF8pcdzohQiw002jQAbpq29IhMbokzq8fOrrqkEwUN2MSw4AMesYM51\nDy38XSEVUlCY0sWgvmsrlRVfFKSva+UtmTJy97ljQ1Nt4fzasHFdqcnv3Sqv\nEtA2/48LBsMbjX5FPyUYaf2ufDh6CCamqsfVRdDTwIxPRKlTSccz+UBpStJe\n7n7YG80FnWrIqoc9BVjQzMuWjDB/KlF06ck83z69jS1ugtdUMKw3HQ1npoLo\nvXQpV0dUi2rV2YjLiEVbbYGmnaGs8Lg1yEqSCCS1tNdA+v+72wyy97K1OWmu\nNQDej7b1NvYaE2VL1EMA3RT5rAq5ClavawsQobWUukVExXIS7fM9mI2S1zBA\nBtRC\r\n=W1xY\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals"}, "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-overlapping-plugins.js; node ./scripts/build-corejs3-proposals.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001023", "electron-to-chromium": "1.3.113", "@babel/helper-compilation-targets": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.8.6_1582806081364_0.7288649664811546", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/compat-data", "version": "7.9.0", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "04815556fc90b0c174abd2c0c1bb966faa036a6c", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.9.0.tgz", "fileCount": 14, "integrity": "sha512-zeFQrr+284Ekvd9e7KAX954LkapWiOmQtsfHirhxqfdlX6MEC32iRE+pqUGlYIBchdevaCwvzxWGSy/YBNI85g==", "signatures": [{"sig": "MEUCIQC+I5L33UbM3g22anoKhMBKEiUVGu4tMIUg6fMYDTMPiwIgTJnn22big7JXH2jWwydAvJ+BuZS27T7g+BLbph+h+lA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOkCRA9TVsSAnZWagAAujIQAI1zOGqM6dGtBCKyKuA/\nYn0bdHdWPE/wO5fi8JMddO2Gb9PyFaDNVXwH0TvRbCjqkknkAh18WcX9Z8N2\nbd9ul7XyFM5AKFvxbexOQV5Oa1MckHjdLfIjwJwG7KY78r5DyJM7GCqxMd5+\nAvTVhs9Xv+Zu03bcEWxiIrbx2+/JkJLtyI3AQpLCrd4WBHADuijwhRHhTAkD\nN7bkF5thdoijF2ZTp4Kc8WV7yZ87mx+yQcIiEaMXQCzTYxrQg/tWJ7tD7Fhi\nVgshc3j39RehsjJGFLbNFdytajDM+cAR8sTWBu7BNXhN/3geqngJGOQhaJ4f\nw6z/ft/EJg4yLtybkycknFXPleI+lKtNovZfRornRoGbQ+j429m28g6eggF3\nOk7lqiQD+iEPcDIzEcg0EqhvdrwV0VFoFMGGKpzoAOfwWdtIL3IES2EpIdJA\nbXWI4fAIe2e68FdZfwL7vsHAZpeqfVqQWlkUUHiAJWFuuhlGvARRMiszzJzz\n/qwqGw7DRCxDEPVHwgcfr4QQbuDLDh1df4e5GuiBPNHVV15mmME/Rv5wgM/T\nugcioXrhdRjlRrEM1C4DoVA5FkcZo6Oua4gf6hWVYqxsof59XrOTyz8ZARNX\njbhJPD1LmrTZcr5ebNNeZSSWVfZQtP+sj6Brrn1jeBYwzL59HM8uU9YHcmrm\nDQaG\r\n=SwMW\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.9.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.9.0_1584718748030_0.682137205914306", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/compat-data", "version": "7.9.6", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3f604c40e420131affe6f2c8052e9a275ae2049b", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.9.6.tgz", "fileCount": 14, "integrity": "sha512-5QPTrNen2bm7RBc7dsOmcA5hbrS4O2Vhmk5XOL4zWW/zD/hV0iinpefDlkm+tBBy8kDtFaaeEvmAqt+nURAV2g==", "signatures": [{"sig": "MEQCIGwrLhtB9n3DcE//dmrDgHHIsVx0gxY5ZKJFxSJSLUmjAiB7ZOLdc3raXqRAECfJc/dB85TdfwirRHc+QvhUzBuTNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmFCRA9TVsSAnZWagAAxtIP/RyasC+LHNFMqfmVRXaE\nVF3dbJJFo8s6FCecKF1aRovTNTef5cle2KhoR3ptDBFLwzPtwntnqL4gHf8W\nxcUu5cLu/k26iOWCM95UyLa+/xf96zgCsLk3xm3oztvdZlFIdllmXj8hjdn9\nmHL7Ta4bZeC/Scx+VLHGhX5tIPVDI9cuKF5kQbf0pPDZlwT9pORYku0Smo+C\naG9VBtOZUKlOl77lgLzAyqdUlFUF58waDGR1LZV0Vt0hn0WQzGsvmo6rqVCX\nz/DNQvh7RfmQIDQzqMrtciEzOkvvudgcpu7nSXPi7fn+UKJzbwoisYAxDkPA\nENIcsACzZp7bnQaFS4/m3gi6vpP7U0OsGn8y2Ki5zOOvnWbqjS5o1cQreTxt\nJZoHjagIb8Hak572YYECJjN1RSNf0a20eU5MePUq3IEIHDVwtf2krTT2Px3/\nf/hRFYlJjxkcUpUJ6Ihn2uMsWJ/TR8R7iBvF91HhU9qhGatd4Fo3K+c2za9Q\nR039AA5ry81R6OVZBzdOaumj9VdAek1sV2MPb1MYUxThq8C9IBZog0dDwxiM\n9MlLP0I6A+EqevbWVf2wzqAVGSg8MhMBf6q5JOrSvaxl2/KFi9gUVkhBSLB8\nUo36EoS0cxg9w5KQHJt5BSAkDGMwlaeexefna0TyXLFy3U7UsY6hVz/gj3eR\nzwHa\r\n=HeJh\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.11.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.9.6"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.9.6_1588185477438_0.2144351474139219", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/compat-data", "version": "7.10.0", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "1e9129ec36bc7cc5ec202801d8af9529699b8d5e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.10.0.tgz", "fileCount": 14, "integrity": "sha512-H59nKm/7ATMfocMobbSk4PkeAerKqoxk+EYBT0kV5sol0e8GBpGNHseZNNYX0VOItKngIf6GgUpEOAlOLIUvDA==", "signatures": [{"sig": "MEUCIQDSsIBteUpj1YI7GV/LV4mrn27Mwgq7UjzftK7XjLDxrwIgNxihim7dwH2iJWuVZ3sJDJxyaVJo9kLZ3RCSRZ9CUW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY1yCRA9TVsSAnZWagAAWvEP/RlHV+wWntBro1erPT9l\npEEippDrQZ8EIUz56KFWC9C2rZlMbRrH7ntYWnu2SxxTjzz4G0/Y0R/Xg8eT\nmugKoeYWm8T6YnnVQ/hd2sNqDDoVa5kPTv+oo5UwraUzIS58eyTU/GnWgT8W\nPkr6u76KQvnflltQlTBxLH019OAU/Cn30NE+xKTyIn6M0SDHncLg14Ri6Mcs\nqzaT4vjItBjEthAZDGrX44d71j7TtiORAmCxL6ptH8ebowUI7GrVjxwALOdD\nXetNtqWg59npy00tdkv/tcXkO6CaTfA+J8Aj36MZEsJdzLfIPVacKs7oHwkb\nhLx3cL6v1JtZnyGf5wfXPmTNNZjTxhVqCdnA2pb/dM+4ldnPa7Z194baT29X\n1wN+TxSd30nxlFsIdF4ZOJJ4y7vg+ejZJsEoWosrMUsDIOxvjJyjgkNF09XB\nXnQCjfa1KuxRc3qB9zuzEz7pbv8moiXQyD1++a1Ala2jooVNCIdv9mrvOQzF\nbFiyY5CB4fZ2i8TPKU+QDLWitCIZLFLeVaGAw5pjZ6WNalBrZwpHhhugNIxN\n4Bu93+vx/X903KLVlBKhviTQax0vJeCcbVcaYUMK+ROQJA99Gtc6NBobSbse\nqNlyDGpZX72cZV0j1mVqyl9Kdnp41qLfD7cqyeelCIlf/zdFoZ4MRdM5yO5F\nFzjz\r\n=CTBH\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.10.0_1590529393807_0.25487045125883134", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/compat-data", "version": "7.10.1", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b1085ffe72cd17bf2c0ee790fc09f9626011b2db", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.10.1.tgz", "fileCount": 14, "integrity": "sha512-CHvCj7So7iCkGKPRFUfryXIkU2gSBw7VSZFYLsqVhrS47269VK2Hfi9S/YcublPMW8k1u2bQBlbDruoQEm4fgw==", "signatures": [{"sig": "MEUCIQD5/DVFdsGdWQH2Icf//R1Z3hQEdjJjtuqNhPSbpAs5xQIgJ5qD39pbR+YG1aDKhqQ3a7HPn0JgWkHI4ZKMnJBoel0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuR7CRA9TVsSAnZWagAAEbwQAIaFc15CM1g3hT9UR4Q7\nAd5SJbbnWEvZgdOCAHM9ZRwfs9r6auBTrIlShDr9jgncQgU3LJyH/3n0rz/Q\nFiZ20XIbRx+kNWL2KkgbIxK9R8zkGWSSamEFMnpb2TQz6mgHyDBFiYEoQELR\n0fOpnGepsfCPKacFfNj+biiCzq+B3FoJKV1xVM0wT9DDmhIPJc8vZdjgyQhm\nPmmTk190DWUxJgD3aqrj7a5bfw1nfaGoyiogpT+xSC5wLJUNJnUFCjc/U/yw\nZaBtL1Ma0/f9pvPQq3lMJOXy9vrRWZBzqNGNwPuuS6dQvsBQn9nJnfiOmv23\n00T0tvVEJ/H751OOrvEhTKX8TPNrorISUK7gsNGdQ02ZpT0exJ8icpMXUVHW\nfN/vxbYkLKWJT/0Khuxrk4XmUx6EVaUFiH3vvIwRTHamwnjRBfFBnsfRMJWr\nllHt06jrBIwC2dKFC733Q+XocaHE7pxC6qkLenN9JvI5jYH7u1cS2V80DvXa\nAu6YqtYzTaa5vX2CkyrM+n78lyWitVUvenBc6h+trGeWgnxplOy9oG04p3jD\nialkd2+S1acVAvjkkGXTq/vjpSDUpdnBUhwvLrUbKm/ct1O7E3AjQYy7KyQF\npr0OlmodsEWp86DEeg1NfIafccEGXhaVIejC/C1dJE+nfQ1KFNuLF/pxgAwH\n5gQr\r\n=mSac\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.10.1_1590617211243_0.8466794879832402", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/compat-data", "version": "7.10.3", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9af3e033f36e8e2d6e47570db91e64a846f5d382", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.10.3.tgz", "fileCount": 14, "integrity": "sha512-BDIfJ9uNZuI0LajPfoYV28lX8kyCPMHY6uY4WH1lJdcicmAfxCK5ASzaeV0D/wsUaRH/cLk+amuxtC37sZ8TUg==", "signatures": [{"sig": "MEUCIDYtY0U5MIfyBoQJxybpRXVeqnwcEjOlWAn5ov70PPPvAiEAkWZnVlqlN+PZK/TLQ7tmlR7AWFST0Kuu9ceq14Qb4mw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SXwCRA9TVsSAnZWagAA/zcP/1BmbeE1ePeW8TCYF4ZI\nRMty/55Um50nnqDh1atRgg264ZIe1ZX8x+e6dQxoWMulLrB7S7/gsmoqpkG3\nFyInAMydlhBmUDV/NI/YqsVJmrK5b4YEQK2rMLs3XE1LTv06fuiwkCjE4tyY\nBxpf4LM0vepIMHr98C6gEBpAHqrpsVtSDo57Kj9Iuyn6okiojUwzNJkeDwt/\n1+rXBuHSwh7a3azC3uchq5Beyy/aMc5s+w8ye6HfF6kcLYXDqX7C6PmqTECj\nsWtn32cmp5JYRTSH7aQFDOKnXhKuYKN20YXdTEplikABL3OM8zXtqNDbLqH1\nd+QgZMpyR43SWdUlUpaSsSajMY9/YfyWGMpEQqaxzWzUy2jmE4iPI40WkEeQ\na7AgVTNsa2/yTTx6WHkkDkopiXwrE03qOVjhdchMRekDws4FDd5vqGHVD1vz\nOxMVDUHtvk07Ag0VEmrDv/tSC9eN5myoh5wfRUzfpzu8MWoa7qBtL2adL9fl\nMm/QLjsENQRoZasMm2hayFQ4Dq3s7+1A1B9e1UuJIOgolqRbyXKjYJTvvRF+\n+KL+IutrYDxCyRRGcQ3eFIfMYJBY0NeirFPi1LJEfzk0qKEuVv0+jDFByVCm\nbT8gv7GTmTKlX3Wv1mv6jBBe+6geNh9AhKW+Y2SXXecZEuUd1cGw8/6Q+nVP\nqR2I\r\n=4zTN\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.10.3_1592600047714_0.16143699336237893", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/compat-data", "version": "7.10.4", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "706a6484ee6f910b719b696a9194f8da7d7ac241", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.10.4.tgz", "fileCount": 14, "integrity": "sha512-t+rjExOrSVvjQQXNp5zAIYDp00KjdvGl/TpDX5REPr0S9IAIPQMTilcfG6q8c0QFmj9lSTVySV2VTsyggvtNIw==", "signatures": [{"sig": "MEUCIQDbdQoxS06jMo0B3VScEE/8bdlkr5nn+RHwfoijrHLcKQIgHctoPUUHDb3hYIYBrZoZfF9por38FQwAmtB1No2j8vE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zn3CRA9TVsSAnZWagAAZnYQAJ8D+sMAO5dJ0Bm4w/MG\n/ss3XsNrxIuxHkdKAUiv5jpmNj1utAA/Z/UNSo/8l1M05TUTyJkN5iADgA1f\nV6TtD3fj9c+aATZLZNCyVmaor0dQb36vBWcpGoJ/UmKdmgq2gkE/k/bONhh8\nytZG2usCUAfeIEAjbRTEqsy7uR/D7lK7IrrkDgTbSooA3M+H5Q9Gvrbij/1Q\nvTFcHImEVHOJBx7GeDqWG6f3pASyC1fJAtWP4rnmuhhU7O3WNpyQ35GhLh3f\nzIQrQsuZLLgXhMZt1hA/mx537ziaA8g5Ossmu7nXxebuRHOo1jCcZoUTL+fJ\nclebe1K7Clf+5mAncDASAwOxPfxa7/CfJN26lbWVuk0wIVyKBKWmNPCikBVd\ncChicyj/MQTLjWWiat8ZMPQrYH6XEiQI5M0/a2Qapp2SKNNahVt08BpVIZRB\npJwapuud3jFxtdze+l+gEnDuoEso2PZLvrXwATRCIpbeR86jL8VieoWRddhe\nbqgO9GTmNjMvAReOlq6MyMx/2lBFUE5shMRF8WulHY28GKFSJQx6uBfrLPA+\nCtxSv2l/7QzUPv5tzAqcmxH+JHa78n0y2juDtfBEWsBHpWPpWqgBU8qjPD44\nA2qi+Vgs8OqTyfynmefrNFpKfyLiJI6BGx8MMuGRYr/j4cMp38xaUwJUsKNe\n/v2r\r\n=qLzM\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.15", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.10.4_1593522678827_0.6424157207972885", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/compat-data", "version": "7.10.5", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d38425e67ea96b1480a3f50404d1bf85676301a6", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.10.5.tgz", "fileCount": 14, "integrity": "sha512-mPVoWNzIpYJHbWje0if7Ck36bpbtTvIxOi9+6WSK9wjGEXearAqlwBoTQvVjsAY2VIwgcs8V940geY3okzRCEw==", "signatures": [{"sig": "MEYCIQCXcdBGbfWJM81ZsNcAWl0rb5Yk4v00sv2L1AkqsLDf2AIhAMoeOPoMhf/DMJvIAXk1EDChPQRWPO4mz2Pb4Fwo0E3J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbGCRA9TVsSAnZWagAA7xgP/jy9Go7RclzkzGcRUn2f\nT17eDMkPV43/XR2DX2MGPbnQoFGD3Xrkj+zdpYbOyPjW2yoxlFdmYFVv4AyO\n9OnGl9LH2B5+TQuKXaBr7YF+TFF/dI1xHYRk5ULRYp2GE+t60T7OpK3LK7y7\nG0Adiov1hgSRT/a+dZahvwk/iL0qBdciQnY+G2gE66ViYd999OPRJWPYdW9r\nENH3I9l9JYWiM7hdlhNdxAbA+Sc9RZSeZgOVEemfkGG1FMwoAd1rEs6qrboJ\nZ9tycQDHv2RS/rl75ggzVQlLBOV6Os0N/EhsqYeBitv/DG6fFjcQd3QTU5yp\nR1vavHEjdBzW3NSQLOE0Tl5HI6l08s7Bszv2j7lbeXQXQlZ3nQaC0zcC53Dm\n9e3crfY2wgrhUiBJqFjRw6hYJXruTAicgDWqC4W2MCTmkmvkJQ8vhf5Sm/xn\nR5NK5DIIPkWcZSrR6YPtt+wIYwtNFyW6JPDA8154Z2vjqyjOjBNW3ZK8pL/j\nCUjYU++ixg7vd7yZlHz/4fifOQZznubOvXSMw8pC5muwjVO1nJKtZU7MS0u7\ns6ZWRPQU6+ytHYHs5Ez/Oe4RoLToHK0fMjQ0d7XloalFZzkQ1dUJycAa8qyj\nhRqoBGwsz/IXhsALCZqV6gImN+luY6jJXzTt44KN6HYcjr/twYt5vOBilPFG\nctFY\r\n=fbMM\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "@babel/helper-compilation-targets": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.10.5_1594750661636_0.5815778312177902", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "@babel/compat-data", "version": "7.11.0", "keywords": ["babel", "compat-table", "compat-data"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/compat-data@7.11.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e9f73efe09af1355b723a7f39b11bad637d7c99c", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.11.0.tgz", "fileCount": 14, "integrity": "sha512-TPSvJfv73ng0pfnEOh17bYMPQbI95+nGWc71Ss4vZdRBHTDqmM9Z8ZV4rYz8Ks7sfzc95n30k6ODIq5UGnXcYQ==", "signatures": [{"sig": "MEYCIQC5FeHmQu+fw5OL5MNPrysyZJ7v5KpGcwN8M6S1qcnCJAIhAKQG4NbAhDAFmoDTwec3AW7KvYmBb8nlvs+IFcz7MlT9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzQLCRA9TVsSAnZWagAASasP/AnN05lHfVN4xhzWvKwN\nE/iTh06Uk7r/ZPlzHno4B+ML4QPStw5J8HLVa4uj9I0SYXxNrO3GLYIuWjDr\nwx01/+Fo37UJSx8qX+Qf93+nGLZw0wM7TjDSI0uFkwkiPjkGKrxrelOCOqVp\nUJEafl8TYOPIqDB4fVyAgij4/Sm9x8zv1upPx62Ta2OutDa0dKVXuBIpIEwQ\nZTRnDJvjOy5PdbjG2tx+n9pwPy6pQgMNPeWbPQ4QTTIL2j610CeNrp7QYGpa\nRuYGn8R3nDApQdHAMgv7A+TAvmUIdyVvzUIu5dO7IQRS+1MkhlEv4DIhW3JN\nHC5v+EhZXuZZcTU57PV9XG2beYsaS/1HyhRd4nIzoZlUFzaCXQFiaIcGhMaJ\nlKAzB60h1AacZDFzH6r7Dj/jlIePQT++CYM9kwtfAFeeF/R1pBRW4RiU+56E\nQZC1N9nOdqSAyop1MFycVsPoQPE0fse/NbXsHAPIxjj+f36JuH4k5igLzS/m\ncjZkX9P843CAJ8NVX+4+QM7w9PuaEpc/+nE2QHmZxF9JILI+XMg2nJCVMUYS\nyW432uUuqMPfhltTbWhF8q3+lwK8eNp7HqpTqqffwZ3YCz0T8UkmWlTadHe4\nvldHZ7JO6iyc4augkWERU1XvcQF8Di9ehQlykyHi0/doP4TLvxulZ/N1GTfP\ntfNN\r\n=XApc\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "gitHead": "38dda069eeac2e31bce3f56290998d30bee1ed6b", "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "_npmVersion": "lerna/3.19.0/node@v14.6.0+x64 (darwin)", "description": "", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"semver": "^5.5.0", "invariant": "^2.2.4", "browserslist": "^4.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "electron-to-chromium": "1.3.513", "mdn-browser-compat-data": "1.0.31", "@babel/helper-compilation-targets": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.11.0_1596142603402_0.2381080068533139", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/compat-data", "version": "7.12.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/compat-data@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "443aea07a5aeba7942cb067de6b8272f2ab36b9e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.12.0.tgz", "fileCount": 14, "integrity": "sha512-jAbCtMANC9ptXxbSVXIqV/3H0bkh7iyyv6JS5lu10av45bcc2QmDNJXkASZCFwbBt75Q0AEq/BB+bNa3x1QgYQ==", "signatures": [{"sig": "MEYCIQDiJkOwBTjnM+3J8dfeM0hi5/0w7jdCi1pqEzhQ0cGCzwIhAM73En6kJrPkWF9doX5q620pxdv6Z3J6WQtkVM8DEUBK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1lbCRA9TVsSAnZWagAApMgQAJ6Nt2kPh9sR/lEpXQnl\nxb0HG+GdUPWHdv0UaJBnLr72DEw/cO+4A7ghAz86k52gZfmbF+XIOof5XVTm\nUgZxd0O1Bm7Y+Ha9T/c0Bof3JGWaQRh4X9G0UM4f+jTHqKhBmCkNPma8C5ig\nvxOyF4eW0ebsZBJtxsBE7KLHhYPltTsQiGTg16EJGiRMu22Y8n+/xmn2OZLt\neTpHFYpbLBc40PaMeeYOCH9DMUq2FsYn64QwJ3WFJeruPup/HYappShDgR/t\n+pmlIvLnuF0rIazY0u3ZafIofWbDYm85I5ry0P74f+Nio1PMFqoFAmiawyko\npfNr62LXrUa3CbvHM1tpv+Hj7SlD1TZFB3LmC5aIpsaWLrW8pbIRhru5CqNp\nzrH2+5Kbuf2K/ICGHg5q/yHRgeX7GkjQGR1aSBXb1Yzejl6CGVHZnOODS83i\nGiESseNAUu2KNqgMFSmX2FxlDCDHRDx9fqtwWoqAgpzB5mbS1qCFf9xzo+tU\nF011mNE5j0xcEGWBRiN5IFzDu9opk5yMgnEQ2aQyAadm/Lurnj0w4/1mcXH4\ns9ywD+HAGgqOq71aPF7CZl59FmindNGDxUfPkhX2cn2wopC0wMeEu+87xFA6\nlKnEeZbd+naNZASSfl7hxcHEE0A5etRmscf6i8gLbqfqSh8Di8d9LrPZWaTX\nalkj\r\n=f/Gj\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "electron-to-chromium": "1.3.574", "mdn-browser-compat-data": "1.0.38", "@babel/helper-compilation-targets": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.12.0_1602705754761_0.207661082636591", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/compat-data", "version": "7.12.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/compat-data@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "d7386a689aa0ddf06255005b4b991988021101a0", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.12.1.tgz", "fileCount": 14, "integrity": "sha512-725AQupWJZ8ba0jbKceeFblZTY90McUBWMwHhkFQ9q1zKPJ95GUktljFcgcsIVwRnTnRKlcYzfiNImg5G9m6ZQ==", "signatures": [{"sig": "MEYCIQDEzsXS/SqvvcP7BwcDp8J+NmT+VTbQZ8GGw088mtbYkAIhANhIc2yd3ud2cYc0GumOjTlvmyXxJFFHmuGtvMqFzH2e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+SCRA9TVsSAnZWagAA1DIP/Awme+DN7CAJgmtnIuq4\nS2HVe6ajo/Kqog0H3UwaDwfKkRr7tT2Qz9tOtMcKI9PVTZ47dEG8rfTocaN1\nlK89tY2JVnqYIsv+oMULLdpSEnKTYPADO+aLOzEdV+jSLQfZ+fJXKNK88U+n\niDHGNLqoey/Smts889SxYXkzqetpr5XHBwMVxFARvuBJJ6QF+Ou2+JF6CMSG\naEyzPkgFTvqENqqfMwMuKSGNIxCgTdvrmN0Hrl88C8x1kKFrjSc1mWYAFFhK\nSTVnxF0XUByEaJQ27Sh7qobTKUvd9xkX4Pvpi6MICHQkcyoM4mQUI5GT1RgP\n/h2kB+eiHpFmfcBLkFeldrqsuHiBZg4zf62iz44rCrQJC+/3sv1kU/D71udX\nha2diKGSDV9eqQlZVna3gWW47HKLyNc7fK+fJxBrgtloTWmzlZD9Mgwsz/l+\niLJld7fDjdXw7WzqXw+uyYSQQ5/bGkop85huzHTu6ifIjCEbl2VZ9sVIIzGq\nTTWXEDH6K3xcvtemFfnjDC1HAdXRw4HeBzzV6CVLjyQEmEiHtAFVS3YBEONH\nptDaHkEncb6pbhQmcoaNkowkhs4MRklpVj4TvtVrgWJjtazuqC2KsKafCYAd\nogckA7I7xBbJQRv8rmptJp7PnOD4tW6JPs+C21dEZ2u1BGbsWYBN/OeYonkR\nhr2a\r\n=Q/HH\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "electron-to-chromium": "1.3.574", "mdn-browser-compat-data": "1.0.38"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.12.1_1602801553906_0.2814284111378884", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/compat-data", "version": "7.12.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/compat-data@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "f56db0c4bb1bbbf221b4e81345aab4141e7cb0e9", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.12.5.tgz", "fileCount": 14, "integrity": "sha512-DTsS7cxrsH3by8nqQSpFSyjSfSYl57D6Cf4q8dW3LK83tBKBDCkfcay1nYkXq1nIHXnpX8WMMb/O25HOy3h1zg==", "signatures": [{"sig": "MEUCIQD0Iz6TunSwrORRhoQr/0sQpKg5glgGNtH1KEkJvVtiPQIgKVM9EwDg42XMJqdEic2jZXaCAVcYgqlIBkJ7MbXmj9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodrkCRA9TVsSAnZWagAAIo4QAJk+IepC/FoOg9fpmmgj\ncYQQ/OJcswPrltny+iF1jfr8oeXeUnh34oZ4y1Ti+bMyJmtU9+QdmVkMEJSs\na4KpT0OuytvxHThP7n6xbTEt9AZugUiS2SkfSnOmManEa9Ou8nKw7Su1mGv5\n3utz93o8voe8DwxvVxwmaD8p5uxT00sgMza7UhH00ER9qCNRaTxdGjjJTlOQ\neNP6jgcN1HaarA9ovOU9XktpzY4zls3AXAamC34+YjA6cAm0ow1pJKzLosLU\ndd2PkkXCV752MpYyLqpAL2/8qtmioHhPF+8lDLtM2sfo4nESN0HdxJreHhOB\nTGTESvlO+KZNfLx3Z8wi9TLu5ytbkrG5IYV0mRcNTl52VtkLhJa6U2KE44Ct\nw+OjYZoJLWYx8XGri+i+4EEEZDbXGHE8fKDN1Re06fEvT70mtCUx8477h/hx\ngm8tedY6FcGQrC9Nn7RGbsi6BmxLmHIn/XfuJP8AUf853BdNNnPrAMWl0oLI\np+z2rPufr1NNzTZLln/lmZs7kG5M+o260aLIkfz8zGJIHwHQ0cbwLTeBzyY/\nuSIExFibpxhR7RjUthlV4PjVAPTPNcVKDg5ZhjQMAciURkI93741Ir0IFG+W\n/gIcPaHxii+gJAdFq1pbXXXiKsffp8NPBAUfX82/2FxzLKY5OLOVmvLxbqpv\ngj0d\r\n=l3KU\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "electron-to-chromium": "1.3.583", "mdn-browser-compat-data": "1.0.38"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.12.5_1604442852163_0.049174649161234596", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/compat-data", "version": "7.12.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/compat-data@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "9329b4782a7d6bbd7eef57e11addf91ee3ef1e41", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.12.7.tgz", "fileCount": 14, "integrity": "sha512-YaxPMGs/XIWtYqrdEOZOCPsVWfEoriXopnsz3/i7apYPXQ3698UFhS6dVT1KN5qOsWmVgw/FOrmQgpRaZayGsw==", "signatures": [{"sig": "MEYCIQDo+c3kir+6I5FKhB8XuWnGETOAHyrAyh52AoJe4yCmcQIhALSmjDRsy4gA5xaIV5qOyeyrf+zf7ehCdQPta0aqKtff", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+fCRA9TVsSAnZWagAA3qsQAJej9mwDA0yvKsPVEalc\nsGW85t4J+DjRWeczaob7YRevIUCvpE+i8EWaokhkmXeYChS0F1N5s8+ADxyW\niMiPfpybVf3aZWG7RDM03KYx2daFBopBASdbQDNDElSU/raRHfjE2Wu38SZe\niQrJpVhs+MDt8zzJQDZniyyKf9l4ULEdDgP+bBtYLJ0iQX5sura42RR+GYnv\nu2YfzZAQqopsISQUdNNNKIlzMO1OFTfLJDhgqZ5WMIZGMg03g5Irs/Uir2yJ\nY6j/RIEaTmIj/kvohGXQnQ4f9ID9Kp/KzYeKkjvIxRurymKRk2tuGfFoVrra\ntak+t8ZnX3gsaGc6+mHMi6+qPx9WNMLXl1Mgmj9xOWRoayIc63vTP6U50MsZ\nLJ3/EoUnsSxT/1MKtgawldAtQLh+bKSftNLw+7S4Qy6srj/Nh4n7geuf4oq6\naJTYGsIgbiHvTE7GdgTtSLLvHJDgYivlzylRerSKhKrlF6m4Ulb2R0ERdoH8\nPU37bnImk9OxYT1KOmQEHuXJSkMja2vrdzrahMTyIjjAb1In9JtSGi0CWHQY\nw9bSzfTDDB40efAYNzoe7DL/2zjW1Mlqc1VQtrsAsU8O7AyoK29p7v+LFfkl\n5m9qTRND2njMDtF9Pcm2ziS97r2B645xh61L5kMkR9+n5x6X/gdRIcTovi2V\n1/SA\r\n=pD80\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "electron-to-chromium": "1.3.583", "mdn-browser-compat-data": "1.0.38"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.12.7_1605906335143_0.5915228718107175", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/compat-data", "version": "7.12.13", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "27e19e0ed3726ccf54067ced4109501765e7e2e8", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.12.13.tgz", "fileCount": 14, "integrity": "sha512-U/hshG5R+SIoW7HVWIdmy1cB7s3ki+r3FpyEZiCgpi4tFgPnX/vynY80ZGSASOIrUM6O7VxOgCZgdt7h97bUGg==", "signatures": [{"sig": "MEQCIAxIV/E+eSYn1hrTVQ2kVci3xyItt5DKibc+/vqJP6GCAiAbuJTy3HsDX2NnOkMlRS7O4BWISWIDdhGh7WYniY9FHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffbCRA9TVsSAnZWagAARMoQAKLyOFyj//bvdSUXeoVt\npyellBDXjwjHT/cw9uIaMKgoHkQQi8/zrjJbdlCZ2ANWvIRDX14Ysoh+PmPK\nTZYaThFk/p8KUD8GD7T4KgFFOEqIYHINNosZKetMVyHESZ1a49NizLAnFmuR\nY6QPbjTPtYvnIEJ74G8VvP4eydROJPB4Jnfy5xlzmK3NLnDfQHVnozR5RZ2G\nYsxzZCcGYDMbsld3IFvHXKNojJkaY3XjAEi/L1elvezSfi9Uj0j+vnLpyZBF\ngPmU9YSZ/Li1ksl0/Zp1vr6Onajr0JjI2NE0pF5HYQJQ9ImBWmNj4RofY8/j\nKYgHPcNScLaP9GXX/W33qSlepCK9MnqpyIrjbTNq6WT4HCUu+IpTZMvVUmIk\ndTpuTXd7lnK5ctJYapeeqagHhul+DbE9drzalMFkXe1Z5MnaFJDYd3aXWinq\nVLcok9ByQU/Qa7n6cWLMgoNXmM8jPNemPl1++/nLpVk9gGRrhFjf+VEONl4/\n93dFD4ar7xyLcGi14e4zgoj7keRCesztGsJQmqLPhMyPxX0qaHqUeA8+2Io8\nYpdNOYcTrYdwloVmw7ZiElFPnZtOFCr3ZpcijXxbMAvYH0ZiUrlTaO6wzeHb\nzScQHckLEQ3YZ2iUN/DcwcOfh0fcaVusoEjzgP7cc2Xpw3yT3I9Sei8Eu7ze\nRUyf\r\n=3H9N\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "core-js-compat": "^3.8.3", "electron-to-chromium": "1.3.583", "mdn-browser-compat-data": "1.0.38"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.12.13_1612314587491_0.390308969481892", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/compat-data", "version": "7.13.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7889eb7ee6518e2afa5d312b15fd7fd1fe9f3744", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.0.tgz", "fileCount": 14, "integrity": "sha512-mKgFbYQ+23pjwNGBNPNWrBfa3g/EcmrPnwQpjWoNxq9xYf+M8wcLhMlz/wkWimLjzNzGnl3D+C2186gMzk0VuA==", "signatures": [{"sig": "MEQCICkHzgZFmeAiCz8SO0zxD6E8pZZ9gVS8GEGDDV0YqGPZAiBFxaG/yX6Jou+ZN5SAYQTV6s3owjGDGTUoChwRLXaDjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDT2CRA9TVsSAnZWagAAfUMQAIWjpYtqwLO4WpvrIKWL\nMyrTJaH5/cV1KbamPeK3i1PQEATl2eOPwLd7hHVIv+SAwlBQQXFqfxeEDn59\n33wgQVGjIWRVjs722tWeeIOWP9XdvgUhNd3smvyxQUBfsZbpJbHTPfMNRnXh\nXAvcdQuWvBHG/x2Ss3Rpnx/crmRiOUM5nRcvyLMrf2+TjZ6//WFuBPnkSi0S\nL/iVbT43G23ygUWl7+/qH4moS0VnwrtUHd1dWL5KUEs9r31P3pt4QLrIzltw\nTzWV2GDdSHwZbVM+FG1DTCdE8AWlJVma5a7svMm1k+cNJ7nTSbb0P7gB/f+N\n69v55kvw/N3gRyhDh9z8noXqD2D8wUmut44DOYDYf6dehA6Lu1UGBvK0soQR\nffTg8lDoHwTVm+KaPFPDOzJNPCwv3a1dr9+sgkI0fZs9ezay59Xqd1fBwOq2\nR4bP1Pbew34D7mwh+IAm5DAyynHpZeEDeVoIYAFFyBPKWd5eUEIxiEV8rKLv\nIMBysYfZEsFPrJJXjwxiA5CXogIYxm23ZchecNlKBJQBnl7D/JMzlRvYXC+v\nhsEgN/X+/iTFv+oeyKvYlpk4ctGo7Pl8XY9zLm8vw2ldHqG6rNo1OnL6t+Ie\na+/tc5gad4E2mEi7zjjc++tSqRMnGGsSbGCgat8XZHvuUzTEuY2NkZFltqLd\nVcZz\r\n=LyGp\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.583", "mdn-browser-compat-data": "1.0.38"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.0_1614034165860_0.23461497562116707", "host": "s3://npm-registry-packages"}}, "7.13.5": {"name": "@babel/compat-data", "version": "7.13.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1e3b9043df6e3f5cfa750854adeb0c7a222d15a8", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.5.tgz", "fileCount": 14, "integrity": "sha512-+c3G3S8n5ziRyfLybmgm9Y6KgoCni+s0hkV0I5bIpGWhu8uqEQo/VGnETY8YS6PFwedAoXOqfe4FhrvbtsLu+Q==", "signatures": [{"sig": "MEQCIDwmOMP7qaydAHTUUkYa+VlK2cX/BFrysVddwwqCToyEAiA3cVejTrfPN//XRtevNY6kiCvvaBlURaHO6HWq7BMoDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNQrwCRA9TVsSAnZWagAAi14P/i0D+SX3/6PaYhXwsVFK\nZfKK4B3JM8RmjTDmAMeawzkMnQRr3Ic43LxUlAIzctfv4AMw4t977QL/VlHC\nMTYCjK8gUJaw6NWEeAIEXGvc/8t/VO6SkAWecyEjUD/qBxpYVVyyWB2sU2Ez\nn16EBdVc7QljVNFZVtzqOtUk/OU/N/Q1K3MumarwO+HvIM0fAp7uYxJgKkXG\nLIFEPX8mEuW5geZQH0X97JeOdOmZRa5VmwB8z819egNuAAl0R3YtKHl+u6W8\nyxqjrBOZjeou7A0phmHNNcSqxWtp0FSSWElxoUfQm3c2nojjba4IrY3ttfg/\nckbNf3+AKX21CbNxCFd4p6fc/Thqimbv2C+QGnYOkFoP4ylswZszNuWQ0t+g\nXwvDGfNrIUVCyaCoYcyVbHhUeCGaokKlZKDV2TGujgrOQlR+GBN/L9NU8KNP\nSooV4jefNIaaTLhczAsYwgi04zKhkbKAx1S4M6uXLpRPyAY9+Swbk6uiAtiB\n6IZRvGxmLCWuhtvlnXIRhV7+h56CNocjmz5WkYcEWl/p6E1y/3VgLwhksgZp\nyCTU8QmLAm+Se4cKPT8tAjLCc3xFfqiBEdYJ7R48rLLKnh1e9BW8/SQ8uQcO\n1T+OIbWTTuMYNr4Y6Y4w3JzEE/xW+IWeesOeNCxU5feeTUtoGeFAw3mGzY2j\n0dbS\r\n=waSL\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.583", "mdn-browser-compat-data": "1.0.38"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.5_1614088942404_0.7974559771724741", "host": "s3://npm-registry-packages"}}, "7.13.6": {"name": "@babel/compat-data", "version": "7.13.6", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "11972d07db4c2317afdbf41d6feb3a730301ef4e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.6.tgz", "fileCount": 14, "integrity": "sha512-VhgqKOWYVm7lQXlvbJnWOzwfAQATd2nV52koT0HZ/LdDH0m4DUDwkKYsH+IwpXb+bKPyBJzawA4I6nBKqZcpQw==", "signatures": [{"sig": "MEQCIHcs1HFKG2sc2Lcu0So9oBALGJnfWVJWrglCBYELTa7iAiAm2HySv4RANQUmC4E3lyTQn39OZm8AfY0xumSyU6ZK9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNT8VCRA9TVsSAnZWagAAUD0P/22Nmqognd6IJ7guBlkh\nyHnVtaPpGBCx97WcjN+uESTQFpfJA0qWtEMCwjcd4ChpcL6ECOwkZ9C50MMm\nAFToGtalfh7W49QTdzhGsfFLg0P8oYuNNNJPSIKtef2j9NQ6w0KyIorWAAWw\nxYpGxiB1zvD3PEtiuywaTNOBN9XIQG7VRYlKM0Wpgg+T3fIfoSIz8v23KCrF\n0PWZwbK2LVMvGKBvdRHp9eUHDA1VvF8ViBLMHX2FZRsgImTnpquBPG+7WXPE\nY6REiV8JjuPf9k8JSn/v348nTKAAmw95wM3oPXCxe4ZOS/DSPfQhtLSiJnUR\nYNHGR3XdpZcii2oOWvGnYWfdcmPoWi1O+8+isN7TsrEPbihi/rAL7F8vxzPB\nD+dv4YSod/UGbSAVln0KfbbHM2SZm+D/nEASKZ2EZlanw96z8DaEXk94LdZD\ndBz0uF/wRc3ZPkFmdrbQ2vxS+lN7nOjVmvTw4w260Q73UsPdWnqVa4T++Z3O\nhotkLqt4AnBnOPArORHx5iNOqia27zIuNXpAnRtRSWoEVDBL/oEiDGhKWKBR\nzUxHYPbxAe4+fQRb5Il0KR7iAKRd/6116ua3gTUsjfSRuPl2gvrwGMgQfFpw\njuaszG2RvOK7DZH7CeGPUC4LX+mwLcFwsFK/AGAzp0DGjSxwcNNpmSn7pIho\nhkNY\r\n=MKwN\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.672", "@mdn/browser-compat-data": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.6_1614102292978_0.47075914445002787", "host": "s3://npm-registry-packages"}}, "7.13.8": {"name": "@babel/compat-data", "version": "7.13.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5b783b9808f15cef71547f1b691f34f8ff6003a6", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.8.tgz", "fileCount": 14, "integrity": "sha512-EaI33z19T4qN3xLXsGf48M2cDqa6ei9tPZlfLdb2HC+e/cFtREiRd8hdSqDbwdLB0/+gLwqJmCYASH0z2bUdog==", "signatures": [{"sig": "MEUCIQDwK7JRuztLLNwCQGQOE+8C32rRHOkCuT4GXB0xcd//kgIgfHWG8rub8QZ1DzkS4gyKzNDWMgzyJy1NA5Da9yq/ydA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOYaFCRA9TVsSAnZWagAAByAP/ji9TESiiAwKyhcMdGZi\nrt77dqwdDuT76UfnOqMXVuNtLobIL9S5vt7PrDwTHUh4k9QQIpy8NYdlAPJP\nVz5KdzXm7U1Vl+5Lu2nuKlm+ZnXZKpu1eaWKi5hiIqrvQeHyTZ4LDBrqYwgn\nnLpj9kVFN7Ye1vLHYTRbdCZiyStElXVwzOEM+Ix934/7azQVMuZbg5wC1p4d\nAVST0y35gzhfsyZvY+3Ap2AihB+jlHQLfWdbGdsEBuErX8Fr7mtxTB7USfcu\npSc06MqOPgBHO/2Ifawq6022u9WOd//ShaQUmTWlds9Y0Wlsz19PP0haptMY\nXZ8lBGKQ4Sp/pZoAQd0w6nY81V99jhR9SfcIgBplD++dO0YbBum1TLnjeKUl\nLaK4Ph39Nwz5TjJK8nFo0G3Sk7Wt+BBgsox0HJ5prP20PlPEP1gzPtOW3NHd\nKQ/yrTx22hPRV7m4iGUa5If9lSp46jYs+tqipVSyXFtD8kCjFUdSRYglym3E\nNw3LTj7/vezXbFoD6RFYUKtu4g4yOxUszN/GHhdQhFz3kUm9Kkg0xYCNUzBG\np56aEBdTIICtQxR5mKoH/HQzLAUt1HggOrKs8lwbmlmxMabtx0GOUpeNFGa1\nmo1yajvYWTxHR7LQnH55HYioz2yOS8od3Z1/Gq1mbsYj1M8XTiHeLanIJYg9\ndtDg\r\n=KBPR\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.672", "@mdn/browser-compat-data": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.8_1614382725399_0.2855285750104548", "host": "s3://npm-registry-packages"}}, "7.13.11": {"name": "@babel/compat-data", "version": "7.13.11", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9c8fe523c206979c9a81b1e12fe50c1254f1aa35", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.11.tgz", "fileCount": 14, "integrity": "sha512-BwKEkO+2a67DcFeS3RLl0Z3Gs2OvdXewuWjc1Hfokhb5eQWP9YRYH1/+VrVZvql2CfjOiNGqSAFOYt4lsqTHzg==", "signatures": [{"sig": "MEUCIQCmOJH85TBMxlmK2blj7JapgaWJTFDj7yR9qB/QNEg4QAIgDqueyL9fuV+KWA76fKYy7D8sDSU5c84ncL/my8P06Gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTyx6CRA9TVsSAnZWagAABY8QAI7nR08fjmxj/QWEOXQ1\nLfY4QFlMSQcfLPWQ3AffqmgPu1t3QvxQNyilynkyvfjZrg0e2VWYtxxUfDeO\n6PIOZrxqG626sGxEISbOcKh32SmpgUcPKR2yWzfrMaEi7mzo2KqHvl06891Q\n1ulrGIhpB4SSAXK0NQoiL4zvAk14DqBC/ED2xA4umbQBwLyvhAlNUYpp4M/9\n0wrHuS6BBCThcMEmE6fjkpLs4hYF1SYheWjudwSj/l1rMmm688RR9odrZsq7\n4qoNFSz5A7Ad1uhm6rRRJGizy9RfL077WPr0FyhZ07arxxH90Kcl3EAtzkgl\n47urTEFJi4pv90mblLy50xGtEYgy99Ud269mgTocTx21pna0fJbYHLpNd07r\nlbArYfxhJxaXwnE02rYfvmyH+T6xN34WzmlIvociooqj4MRUHFrTbPjnTL+v\nsMsTOVr/sNFOi3FB07IwpjmkniyRMIWpHJ9+rQZ/QozV647G14c9NmPCuc5M\n52HcIzx344ICzyTWutBmXZoZNlGo2M8i1QoNbw+xIDl9rXJ5KqXph02Y584k\n0BcIQRwKdYF144BaIIMlymG6erOgzAXy1g9OA60hMRrPIrR6171LfXQ41kfn\nL/jFmxmwtyqW6VbGQB6h7zjF0hSvz5jlL8T+86T4rwl2LXFnGwBj4Nl3jytY\nbix5\r\n=u4Pf\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"lodash": "^4.17.19", "core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.672", "@mdn/browser-compat-data": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.11_1615801465914_0.5421612999790202", "host": "s3://npm-registry-packages"}}, "7.13.12": {"name": "@babel/compat-data", "version": "7.13.12", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a8a5ccac19c200f9dd49624cac6e19d7be1236a1", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.12.tgz", "fileCount": 14, "integrity": "sha512-3eJJ841uKxeV8dcN/2yGEUy+RfgQspPEgQat85umsE1rotuquQ2AbIub4S6j7c50a2d+4myc+zSlnXeIHrOnhQ==", "signatures": [{"sig": "MEYCIQCHtcPlvxqN+s2zdixqYkXzfX8BYssZP1rkRwVSDDJvwgIhANzcnPB7jVzcl2s709tUgdFziR3urIUHQ3I8u5BEOrMJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLvzCRA9TVsSAnZWagAABs4P/3sQ5yir4zXQ3cFo+Kjs\nKbr4ECkQMVFIqSSiF2uka8hkTBZ0emwRbwxoBwCTAFL5XqcbUrvcg1cZbZTY\n4OUD9GoC6jw6dviehcmfWATfqhJRsfobJWhfI3hg6DKoBtrtw4O9Sl+cP+Ys\nyVQp2k9uuVtvRAYNImSYOhQWSIhl1B2wcUPSmsarEvZIE4aF2Sq0b1MywRgf\nq2usF2OwWSyvim3A6aErwJbOC25Gfzaz7ZPD1uqknnTQ/QlVIBTrYnAXNIcP\nHdSETxDT9dvShKnpss2m8I8zVi9rR0Ng8BVX6RIq/RVaiXwwEtI2OQyguB4s\nbb7rFUatOQQt81m+t4cECGjADvl9wOUdLLf113fVwoRyuk5IpLEctw+RHz39\nLXImKY2lSUok1H0v/ITvJ2tIgAylYigzgi2vmAIAbHSfvIS58DQEvySYgGct\n06tsmYcN8eeFou+PIzJ44vduzCw3/Jpv1uK/S9MHhVEf5yHM3eoznffC6dOh\nV8bUnavChU8Mc54ZyZsrgqUPeb75G3uZNXKP/zEdLa+DEIExuNM82dyooTpj\nGBw8r6ww6pKCOq0+q8TFQZn7sn/lNNoKNIGZ8EQaVwSfuD0ZNUEXKI0Qrd/H\nBdyEU8wVS7kil+FQ57wi7xJ24yA4EEc9HkMLVh5Am/7KhBUIEyh2RnXgdZGt\n21fU\r\n=iSt1\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.672", "@mdn/browser-compat-data": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.12_1616428019211_0.9933873864046476", "host": "s3://npm-registry-packages"}}, "7.13.15": {"name": "@babel/compat-data", "version": "7.13.15", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.13.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7e8eea42d0b64fda2b375b22d06c605222e848f4", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.13.15.tgz", "fileCount": 14, "integrity": "sha512-ltnibHKR1VnrU4ymHyQ/CXtNXI6yZC0oJThyW78Hft8XndANwi+9H+UIklBDraIjFEJzw8wmcM427oDd9KS5wA==", "signatures": [{"sig": "MEUCIFf+geLEru4YiRmEqGp8FUEiwgVpi3OiVJgoDcHZOyeFAiEAiztvRdlTQ0tEhn7EaWORjEU6f/r448hVlBSuKp04r1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbyY8CRA9TVsSAnZWagAAip4QAIe3uk0J3K3CMtvjWBvH\neZPoenTo6JlQDkD8vh3u3HcnTqx/+F6PSFML/dyYK0Dbz4JzmwAR1T8+j2Zl\nAmGJtTW7lkn4sJwMp9BLrSQEGO2xBRqy/ombg7WGWzQZycf5b1tnD54BKaMA\nlVdCIZSvVqeBjx6iV7E3TfEZgfGsmGVKTg0GZCa1PCTEX9a5uhaU1leUaCnI\n1g3RjItshD26MmoNvdzDjNu+JPj1GRlDm+8tnK6OFSkU7VKmuRRtRStBfPzE\nOb6aQetHZw/kSg+O9pIWtHQTW9odbFXOQBY4v7g6Hb9ISYHOAAAnsGsyk56g\nT7Mb6xXfEGgPDoDGqWSkxKwqQVwfAJ/dZvAHyuDdkWF8IH7HsmyDrVcMptpn\nAqy57mhmj5xL+M1COXT5golqlpcDKyenLxNeISgXpOpq8d6ggfWpnzrAgf+w\nBpDNNVQRx0jX7J+beeFgtjPL5GNnnJ57uhPWLcrFr25PwmgMcu4tfs9eVHHz\nCVnaWkeeJk0WkPjGn6b3pMhdql82UQT2QvAc3urZvpHy3mJ4eOcdbPOfJQ7Y\nh6Tq5+qR5mw3D/JyWdt1gaNcJWRbEIQFxDNgcpaqpl3yfgS477tTBoUcc2fT\nFjI4BKJq/Yz44oY6CO2gmtWksj185ieJfaCMWBukNxZ4ISyrl8KpTuPdA0Gk\nJlKF\r\n=YqAP\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.672", "@mdn/browser-compat-data": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.13.15_1617897019745_0.4642736058383934", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/compat-data", "version": "7.14.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a901128bce2ad02565df95e6ecbf195cf9465919", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.14.0.tgz", "fileCount": 14, "integrity": "sha512-vu9V3uMM/1o5Hl5OekMUowo3FqXLJSw+s+66nt0fSWVWTtmosdzn45JHOB3cPtZoe6CTBDzvSw0RdOY85Q37+Q==", "signatures": [{"sig": "MEUCIEsSiCH7sAvD5knUdf0F9YXltOuebeqyhcV9kimibJpjAiEAi791TdyNcWm/0Y8ACdjBsgrrrLfgRuRgqSahdQOtoyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKRCRA9TVsSAnZWagAAomMP/1xLi1j1ynCBBBxyLNwu\n1cTJh+ncB2/Fgrsj7UdfSKttBNd/zvKheIi11iqupEOr1LexP6pq0oPgkDIY\nHh42TmPyW1MhiMmMQjBfrOCAZNqQE8msoyEpUuthqk3xWk9tqU10ztXzMT46\ntcWBQUBmc8ht5w2NeD3TnZLySnETKrgbBrxOrngOGpX24ChvpXs1rW/aowN5\n8WSV4q91zpofU1t9F6bsIlzZKU24YHn6w3/qLoebRwDLKmdlTHx7dG9sIeRH\nNOkoQ6dYgpVXw9kC4dc/lxbdKBvinoThUMttUivf1+mhR/SrgXdSAjFvglj+\n+9SeBQAq3gehaKWxKrGpctbU+8a00GzetPI09GYQ8Hek2v/uc6qGJ1pgcaMc\niLY6tvXAqVfhtj21N4eMI/Nlk6HIlVbntdgZmTVt/4T2Z+vhtfySRWWA6+Nh\nJtrxHFaC9ZoA2HT3B4x2lT+U1XpR3fcV8ZC68HnfBL5RyuycBrw5i+gDpHZH\nINyi1y68mqeVLmb7EPx2/zbZWsC+C4GatDoA0pY59MTIkCsdjtScR8rsyqX9\numlIO4Tu2809xucPCmQMjqIEk7I4onEUthBl/5T4Xfbi8X9jIQixNsyoj69c\nAE1W4xMrBOxN/xOxM57W375a7qxpstyUsfn5VXmXZ3zdapZzRW8g1dbarsC4\n7oHL\r\n=qSI/\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.9.0", "electron-to-chromium": "1.3.672", "@mdn/browser-compat-data": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.14.0_1619726993173_0.33203847220183746", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/compat-data", "version": "7.14.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "45720fe0cecf3fd42019e1d12cc3d27fadc98d58", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.14.4.tgz", "fileCount": 14, "integrity": "sha512-i2wXrWQNkH6JplJQGn3Rd2I4Pij8GdHkXwHMxm+zV5YG/Jci+bCNrWZEWC4o+umiDkRrRs4dVzH3X4GP7vyjQQ==", "signatures": [{"sig": "MEQCIE+s5CCMV6i9uO4pJu0eZ6VFQAfVD2ippzcvFfJlogw7AiAcUH9V93MoaqdsX0FJ9o+LKNsYF59ryFTxeyopqLGaUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGCCRA9TVsSAnZWagAA7JcQAKD3xIOvZnhiruso+Wpu\nWU2eYV4IYAxSS7jPWYzeuC3442KtgPOvfiEG1T9PXseP1eJhI/SV0uZ8sZzD\nQBkpnzyMjV4TydvsINbl+rUmxuBw7qq/5ZyIUvRMcqipWflexlRV/IJ9dFbC\ncLV/HIpTUqRC3ObLX0Y2TF656ojNgZn3rzJwedpgF8p1L8hV/6DhDOp+pmZB\n/N1rYsrNirTaROSoNMDA7k0VwUPO2Oy89HGNUlYAqejN7Eq26t6xMytc2NA/\nJxnVe1Ph1MQahruY0ZF9zIGWdaJgT/KCRQJXfmqJeAFpfSkGV3MRkyuugZ8l\niQbH73BlzpXNrgFRCGYYJvBhjWmY3uDDv5e5xe2pza1C9+ZhvUYhYB6otZvX\nD76DT5gU44kBmNsDsqLRXL/azRlCZvr0FLykwohJnFfqSsVcziZXtOLqc2N4\ngSrak+YyAQTXSdcFT5BKh54Tz8SieDU7q9RQJDRCMJ3+G91+uoSqkTxWb5sQ\n/IPwHbMY1UwXRp6bj915DFKzAcWRq4JODsW0JIJccgbWXrV+th1vuMviDLg9\na1QAygU/55+1UPJcQ/0ujRQxaqraInuFGXq99ViFCVOQOZ5iZqBsQQIaM1n1\ncWeu32I/8uDQ/hShbqXe7334UMRJsediWGVRqidjjO2ESe1IjcIc5bJbyWkR\nmuky\r\n=BOQm\r\n-----END PGP SIGNATURE-----\r\n"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.9.0", "electron-to-chromium": "^1.3.738", "@mdn/browser-compat-data": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.14.4_1622221186102_0.8527848602705614", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/compat-data", "version": "7.14.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8ef4c18e58e801c5c95d3c1c0f2874a2680fadea", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.14.5.tgz", "fileCount": 14, "integrity": "sha512-kixrYn4JwfAVPa0f2yfzc2AWti6WRRyO3XjWW5PJAvtE11qhSayrrcrEnee05KAtNaPC+EwehE8Qt1UedEVB8w==", "signatures": [{"sig": "MEUCIA7es2V67zR20JIMnvraohwAsvy3oD1s+oBdeUhOTvNmAiEAkUcwqzE/6QRAhaXDmlAo/pdbg3tfgLjbDZH28DelJG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqVCRA9TVsSAnZWagAA+1AP/1VhivfY+7PzhDlDkLF3\nuRiigS+MudQgHdaKn+UwMBW+kdV0P4FnoLxAy03U7836sijqcfa4QuetYmsX\nkNqcc/BkkXTbySVx+Jm+a+AejmgSiFCeUJCr3ybuiWl3OD5uago99KuNIYFD\nFLADIFZaeZcp+luJ8FGzFLri4SPInlXhlYxbZ8G+9AQ1fNWRBxg0JlvgDHae\nXtcGFB7BVQ+tbIA/b1zpNYjqhyZGXm53hDr8Soxxpefve52r8IY0gL2pQI7W\ndIJ5UtzEqKqSBKYLK8bDDt41SfIvFLjV5vEx9J9uVQlEgg3dXEOSljqwQi1V\nOGRy3yskvvqHKYIljAT+Jb6/YSe+hPEEg43csYAgT8I+TqWThXTuE9yXpe0k\nJV/KP2dGbIsT8tipWYUAlpQMrHLwlg3xfgzDY9J/P4Hw+MMzRGOJgRZTq8Gf\n5BvcWAmTGtfBxaxspK4XMNzhII+kMUh9dwxC81q5xdZqXX1q9R9Zovbs2HWw\nkmXG5OGjWUlh0Azcm2K2G69lLj1JELVgwvvUg2cltmRbcCc86mfawfREJnDv\nJMBNxLvr2j8CpEKKgNX+qk7oqQXRh0dcsx6ZtpibXXd8HfECJstxWX+yUAke\nNSCQHpcOJuVDWc0HU3Xbo5eJg2EXWvxMwthiMk6rnWTxoapBKPRO8mXDqV5h\n845b\r\n=3Huy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.14.0", "electron-to-chromium": "^1.3.749", "@mdn/browser-compat-data": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.14.5_1623280277039_0.7133567575077329", "host": "s3://npm-registry-packages"}}, "7.14.7": {"name": "@babel/compat-data", "version": "7.14.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.14.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7b047d7a3a89a67d2258dc61f604f098f1bc7e08", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.14.7.tgz", "fileCount": 14, "integrity": "sha512-nS6dZaISCXJ3+518CWiBfEr//gHyMO02uDxBkXTKZDN5POruCnOZ1N4YBRZDCabwF8nZMWBpRxIicmXtBs+fvw==", "signatures": [{"sig": "MEUCIGip+QAn/zY8DX4jovkbVq6GPt6JPOH5KkWsuaj+Iw2iAiEAqTxUehNF42whmLRKMtpw6S6EeZkJKArJJBbAiiDT84w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0Qp1CRA9TVsSAnZWagAAy8kP/18uG1wDsn8t7GJZAg5H\nb24A6k/dnGbNY83zJtE4qVesDVWWFyrL8qzPp7y3OYduhqDCVW9tK7RxWiBd\nt0qJNnObhcrrVwxEoGa05EzdkWH5qQtBtoU0ajHPJRWdVj/oyO26oen6SvzM\n25edFAaz2m8jb7rXPxyDuUR95ckruz/XOjslf7NMFUn4wwFxwKv+q6qnIHEF\nfWrC3JLXMnufNn4ePOA084AzMKzVO8cUIU/1I7DP6sBSI6RcLQT9wbRj+6se\nURrxab7ePxTJj1qFvAAOGke59Iy0jiX24hP9q1+HPc7A6tTqK6GfEOQfnrGD\nHXF8NG5tXA7EEVaRDtTpaNjxqANsZjGps09zVvIqv2uBF3+WaKKq8NO3lmak\ncwZC4BXjyk1RhQ9FYymrYZVLl3ezw5wRvymzqS/t8XyBVszoIB28h1eQA4Ea\nAS0Hs8QdavgH/Hg+jwckPVT6cUQpJh6IldoRw4wRWpl6lIkfOo3zq/LpriPD\nBpWoTEFPJdNQNiuw3Z+ilAwOcVye2ltIqWUQFHcufixEwW718tXrHUzsy1sc\nQE5XJK/6fXN3MCJUDlUreJZTHNbL6y08zWzkBZQ880wjJeR5k3f7zaDQ93xa\nWDa90od/3C80pRhlYh6xc9kET7RlUce9FfVYcQXD9Hx1WaGlbGeWwNu6Hhty\nE4Xz\r\n=N04X\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.15.0", "electron-to-chromium": "^1.3.749", "@mdn/browser-compat-data": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.14.7_1624312436976_0.7067946328071446", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/compat-data", "version": "7.14.9", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ac7996ceaafcf8f410119c8af0d1db4cf914a210", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.14.9.tgz", "fileCount": 14, "integrity": "sha512-p3QjZmMGHDGdpcwEYYWu7i7oJShJvtgMjJeb0W95PPhSm++3lm8YXYOh45Y6iCN9PkZLTZ7CIX5nFrp7pw7TXw==", "signatures": [{"sig": "MEQCIGPnq167MiOkdiWKhNl4GgH1QFdBno6FZCliF/cb9fFdAiBn3OM0vzcAtX2bhmvvHtKAHptcOylYfIVbiJrBH+YyPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLnCRA9TVsSAnZWagAAFMQQAKDtki4EA3QcKEopPpfG\nzNJ5vQafRqIs28KRZP0tyxhIt0k+BJOTL5MTVIFBvMgLVBm7qFJIQPrWbWL+\neIkvszU6Z+d8Li1ebORnIxCVXYVI/uJAwjtImfJfKvFWelDDr8XrckR6upvA\n2aHwJZSWzvK5ufVLk94hwhvk2/uKW6iIoeu6U1ZpeNfuB2xlz84FVhQlM6no\nvnR+p06XhRJRRrOwyHVaWHO3bUOSH3IJTja/Dzbqm7kCn0s1b/avRtKL7w+/\nz7jxOs/r8tYUi+YXl6PkMzBJkDE7rykXn26GYO9iG6Lk0CFScQ91D5BDSEZO\nOSoa8YwVfXL97pt2vQyW93UpjUQK4un3/5gTRvLS/6XKFxeYWlEZk9aU+x3F\neZtn1u3V4GpEA1fxzHoDBgw17Ccih8jEzDMJh2/qYmbJH9d7+NG5k/S/0VAq\nKC5ffzaNkN7j25GMx74ns4BLTANrcCEkoLoh61aaLMLRS/Kt1kGzao6tV6rI\npL2yFS46twsCIq2/K1RpMuCMDgqNLovegGg51rnAgvuTi+ssYUhvzGgIH8Rf\nsZ6Yc8JR/qNacMkGz8pQ7jRkXGuhtdDcJJeLawAvUIdgQ+hlvbdy93xs1dZl\nJdSEJ7CAxrAOZaGhI/9nN8tEt8THK7bJdhTRaduQw1hnD69lu0qTEUoBefcW\n9aqg\r\n=X/Cw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.16.0", "electron-to-chromium": "^1.3.749", "@mdn/browser-compat-data": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.14.9_1627804390886_0.1552638798726833", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/compat-data", "version": "7.15.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2dbaf8b85334796cafbb0f5793a90a2fc010b176", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.15.0.tgz", "fileCount": 14, "integrity": "sha512-0NqAC1IJE0S0+lL1SWFMxMkz1pKCNCjI4tr2Zx4LJSXxCLAdr6KyArnY+sno5m3yH9g737ygOyPABDsnXkpxiA==", "signatures": [{"sig": "MEQCIGf978/kuJ3t5ejTlKV23yetGPsunMRmT0q0GdCV6/VyAiBcjjrFw8M8DWJLUym2X3xY1ySzshYNUIJt3xDij8UQ2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLXCRA9TVsSAnZWagAAdbQQAJLFqLCQGpkyZMtKHsMt\nKGAOTrH9K69xIa0x0o+82GynD7YKYeDsz0B3SCVi+5mYGSyWC7iaC9adESrs\n/Kmij5eUaDE+2JwVi46WwTjLqMz7LatNWgPfJ5CuIy+cDYEhVnqmxLhGY15Q\nMtw94Vt4uXoYxxarGzyoRkeajBYpP7qNLsFpeE9DrGN2cfvyfW3WdT21zlfQ\n6uloqlK493f3qb5DteqPr/lR4tqERG1dbcg4zLITMyOxCjfyKQSty3vaEEuA\nAREl0w9Ziy30DmXOa8mPjqbROjlxNSVTI0zRF0FJwVu3Cm2c0BZEH79yrHwR\n8PqEJnmSaSqUVSyu0G6rEzhvCn8r/AMJ9lwvbbOZ52ta673kTuYDjvDTIohW\nkkwsBuSxpCMxLmyFaRZNJsTdS71kD+6iKPylsSOQ3Ss9SCvYSrLcthM6WKWz\nWQ1jb8hKxjImqhzfDX1A/MPBAxsYMfNtq5OfmnJkCxk8Ez83w5AwIkuzSQdH\nirixsSm84xDyhas0520WmRptp7xpgEHAUPiX9t/7dFLwi/I0tMk5mMktdi8L\n+COYOkzAwLTm8Sm8207vPFeDRRvKZad36G4JeLRDE4gEhE06NhhtVITVtby8\nwCyLOnDDA5fOSM7okccCiIQqYOdaNwujNNgSwWPnw3tNVzrqNYAMHHQqLktv\nvOB7\r\n=AlqG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.16.0", "electron-to-chromium": "^1.3.749", "@mdn/browser-compat-data": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.15.0_1628111574939_0.2793303536205263", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/compat-data", "version": "7.16.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ea269d7f78deb3a7826c39a4048eecda541ebdaa", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.16.0.tgz", "fileCount": 14, "integrity": "sha512-DGjt2QZse5SGd9nfOSqO4WLJ8NN/oHkijbXbPrxuoJO3oIPJL3TciZs9FX+cOHNiY9E9l0opL8g7BmLe3T+9ew==", "signatures": [{"sig": "MEUCIE+oRQXUvbTvumlA2Q1sJIV6kXTixrOL1Go9Dg4w7RXTAiEA+r0ujnsCVoZk9y85rKA+jxPcluqzRAKdLu8+uD+R0kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49529}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.19.0", "electron-to-chromium": "^1.3.749", "@mdn/browser-compat-data": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.16.0_1635551240965_0.1486244933341514", "host": "s3://npm-registry-packages"}}, "7.16.4": {"name": "@babel/compat-data", "version": "7.16.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.16.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "081d6bbc336ec5c2435c6346b2ae1fb98b5ac68e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.16.4.tgz", "fileCount": 14, "integrity": "sha512-1o/jo7D+kC9ZjHX5v+EHrdjl3PhxMrLSOTGsOdHJ+KL8HCaEK6ehrVL2RS6oHDZp+L7xLirLrPmQtEng769J/Q==", "signatures": [{"sig": "MEUCIFuZUwZOgtos/DuCftcosBcxWeBUZ/WaFMblygX1x7maAiEAhQWTl6LB7tuqnIKLvJi0cHi2SWoinmajF4V+qO0r9Nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlDS3CRA9TVsSAnZWagAATpAQAJoXs+PK9AglXsXCRBjy\nas4e6x+fEs5qw52tgXzuX66cxao1YxDjCQjN6bZp+a+pETia1/bgrmPpKzmE\nOzONLz7Yxri3Ah3DjcH2VBaohmjEbJmucTFSs9Deq2bNSHgEoFJqYInJwyJp\nWz/gIDiqFNI8x+LWqroRijalRBF2XN8GDgyWH4Rjj072qobxUu90VSDx0Jl+\nWuZLdENrFGM2nGTCPpjMPmSLmkM9tEn5mwJGG5n4M29K7kcusWRdS4kD83I0\nVbhQfXCWVoIIz1Hh24wPGnDfTlhJXDzRCexw/LpyYNiV2W1dLwKy0ZKe5im5\naHZO3XlVkzK8yZgCOmhpyJNP9lHwKpwAAN3noQAOdqlKK5/z0eoTYkuZQ0BD\n/KQKFvtROcQyPCkT3ZWl9hcM6Imk058mHZWXivJQkliA+5uKr2r5mJ9yn/t3\nDHv/JQ8GbJDr6K85E2Zsqjh+t53fmRGTM6tyDpLK0C6Bf5C7ZXKHmfvZk7vr\nFty3BVkUsFKpBFknZA3LfVNV2ImW0CtKvf/AZl3Bl6vipa13o/eI+CB21ZGZ\n6Yj2FZWCdDgjEbnF5wbWmTppMlKqhgSxro+lrjQhiSQ5PlHCtE6XMxq1kObV\n4Ik1GFkSTSdMHS1eBS+pstDGjIlAYQChzd9pDqK8/UAMdxSTCPhngDDpAz2A\nWDUt\r\n=U99M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.19.1", "electron-to-chromium": "^1.3.893", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.16.4_1637102775736_0.08604128642888687", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/compat-data", "version": "7.16.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "31560f9f29fdf1868de8cb55049538a1b9732a60", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.16.8.tgz", "fileCount": 15, "integrity": "sha512-m7OkX0IdKLKPpBlJtF561YJal5y/jyI5fNfWbPxh2D/nbzzGI4qRyrD8xO2jB24u7l+5I2a43scCG2IrfjC50Q==", "signatures": [{"sig": "MEYCIQDdyEHfkgapJBP+NHELhxcVTehBtauQhoJ6nMBIZVY4RgIhAKkha3/OHvahTlP3/9kbNObrxgFVme7woNBZ9fDm0CRC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKeCRA9TVsSAnZWagAA+zEP/1DALwshxl2OksAF1CZ8\n2xeWg1MNymqs8EXprexaat0PrvUT0Jh0Z4YanBJRHXyPGRWzLB2mdPnmFOnT\nvITqZBQ1Gyjk9/19/daf5JK5T+CtzQhbDZn1RkMhbL/mFWziArxCge0l4bcj\nt++JvQRA0DvM2mvTq4yYhrXU9Uz+dJBxOKxHMQ+uRFUAVPTrsJRmzNKNJob9\nPxMqsEasQ5BVwvd9DqVFC/MGDgyKtnS7kyEWCL2JJ6MlppZjSslUuWVk+b1E\neliGK8oBVF9YKMzhFfdxCBYpRV/C8YBm9TVLFAs/35eJPzP4hEdmChyJLzpF\nrciawKrJ9dy+ng8xNgUXqJUZ1pjHronFdApeoXhrdgii7vz3a+v0VMUtZIKq\n5AYvFf6kX9Ro3tYo8jG22WPJWI2jE4teNThgb70R9+s5TMrTjNNzBn5SSgsA\nAvMH8TKt5tFIGtr4yep69uC/4R4RU/uw9gRbWoq2D6UcusrSYq4OHB3AJv7Z\nkNZGajCWpETvE9sFBKO27LQAfLHoje7A4Ul9v962jhiccUhgjqi35S7r9byd\naJjyqNEXA1uwi2Xa3kfJKYUghk771hdgNT1ELKGPhvwxNQ3WrhXAUNXdoq1m\ng/ecjR5hGhJK9wX7FqqpDwjPm1gbIkRxImOEzRjmJlWlmNLU90pgAFRQbtSt\njPyD\r\n=mg6K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.20.2", "electron-to-chromium": "^1.3.893", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.16.8_1641849501989_0.6876044501283494", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "@babel/compat-data", "version": "7.17.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.17.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "86850b8597ea6962089770952075dcaabb8dba34", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.17.0.tgz", "fileCount": 15, "integrity": "sha512-392byTlpGWXMv4FbyWw3sAZ/FrW/DrwqLGXpy0mbyNe9Taqv1mg9yON5/o0cnr8XYCkFTZbC1eV+c+LAROgrng==", "signatures": [{"sig": "MEYCIQC0QVQARG8jnTKuab/XvGbE7QZShdsEL4diIjVVU5VC9AIhAIYy9LYG9HsuyOf+LTsYLGdc4RCgNLP87iNE1uEjrZpL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4ICRA9TVsSAnZWagAAyDIP/ipSe24P8vzp+63tTGsa\nRFp1uBudm0pOhnFBwjH0czyVl2AAv4L4LsyI31KSw/GRgNtszSgdjwDCu+rS\nuN2th32rk3FqXWf4+MllTb40kTUDh45CGBMlBUgYsez3EpQgyFVz2V8hj7nE\nBypzTbQREKZbFxL8K5zqDtplWDwtEOGh1JqiFcXXnCfHSCX9nhLBaiORAo9m\nJQZpJVtz30jWRJh6HOYuWbl2jbI5RcverJjzFV46Z80et/ZxG62CrlNwjlNy\nyGHAnb1A5t4wL56xp6vsLegZltC0tnckHYYUynOQ9ngcKTcwJrXoi+oxjI10\n+77WV3aL3Bf0MAt8BHAo/CKMDHqLCjdnYUG8A1vpN2jvqXY/ZVXaxiYJ9TDf\nH+pvgNnlqUQ5pTnWCwneXZieW4mJ+IN51SBoJ0K63amob8JzHyFewxaD5pVa\nrNz5uexAoINXbB9H2srZPiu2NZjDPOcLgI9qW/KCcGzu8q5mIMtD13CJ5avF\nH7qp0A30JGCM3rrY9hERfnsbwcFFxVWLILCaSfczL0m0z8bYruRpq8tohwJ/\n++PPpUw0CT9PiDnnQiUVQlcbwSt8Igl75IfYPj31iXndaB9eBVfw46T7vOf3\nnwuGMPsFsVQN0ItVnG3HRVvIjLUjzQUkVm3jfiknPqQUAWnTqBb8hcXrVF7l\nenIR\r\n=sM70\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.20.2", "electron-to-chromium": "^1.3.893", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.17.0_1643843080307_0.052648169681033696", "host": "s3://npm-registry-packages"}}, "7.17.7": {"name": "@babel/compat-data", "version": "7.17.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.17.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "078d8b833fbbcc95286613be8c716cef2b519fa2", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.17.7.tgz", "fileCount": 15, "integrity": "sha512-p8pdE6j0a29TNGebNm7NzYZWB3xVZJBZ7XGs42uAKzQo8VQ3F0By/cQCtUEABwIqw5zo6WA4NbmxsfzADzMKnQ==", "signatures": [{"sig": "MEUCIQD/DcF+R/0RkxbTU76JIzdn1/npzFIrynFjzFXTSSox2gIgZPtRUtueruRpGquC9b9wZz+1m1rVom0IEdKuCtI+HLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50547, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3Y7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTyQ/+PxlQbcd/X0wXJ4x/TTvqdDv55pGtOA58AwQAPeUtQd2mRTo2\r\nSCau0N/BTplLhFNo1+zOqtIdBEmG0J4A07fG64VlxPIPF2V1ODKFDuveY6qW\r\nilY1izxMrgXgzuJ/iSmDtEK0ZnSB8m8Q4px0u08Blp0nk6ufUVc7ex6zirDF\r\nR7F2QWmypsm32iGa0y1XeDWeePPVlelG6H7a+ZC8J52sC0CISrYVExzCwOZR\r\nZafo0OZaCA2j4SrDWlGnUe0EVKbzDFLng6S8/9ajh+8rBujOinUd3CIqlzax\r\nY/biVI7jr4vyqNSyxMXGrjVuJNTxi5DpbGYm3SlO5F330tiED0HZNAWT9Uxq\r\nY9iDu+twQ+nEG8K8AC5HFNCnd6sdjXpTZVteuUL642Rz4NqErCGa++7DRfXV\r\nVAsnzAHemxUHOitWUtctlruFjPWjKd2b2hG44rgcAI9y1vFT3dr5jWBolMCn\r\nEux4cxBYFheSQrkpOhXQkOCaPQPsImedHN9l3ze81YmdqtO97AX42IR0em43\r\n7akBZk6Jn/1cMmOOczh9XSSmSW8BeGIK71oWNDEjzfveYEvbHluXNq8MTTb3\r\nD7KpqH8MDoiUpmMg4E5RfIQnSzsMYHYodCCiYQhI38IHfYP/0RvMQpXen4bp\r\nyKn8P3JtTRXza1ofDWqQ+FIc0h+glzjH4RU=\r\n=oyXT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.20.2", "electron-to-chromium": "^1.3.893", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.17.7_1647277627551_0.5594628822563728", "host": "s3://npm-registry-packages"}}, "7.17.10": {"name": "@babel/compat-data", "version": "7.17.10", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.17.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "711dc726a492dfc8be8220028b1b92482362baab", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.17.10.tgz", "fileCount": 15, "integrity": "sha512-GZt/TCsG70Ms19gfZO1tM4CVnXsPgEPBCpJu+Qz3L0LUDsY5nZqFZglIoPC1kIYOtNBZlrnFT+klg12vFGZXrw==", "signatures": [{"sig": "MEQCIAnvZtzRm3gK1TgBTl3/3aPGcqskG8GX8ynRngsIMEcSAiBG3t7WLrgKzOyoN+OYDN1xel3ZbhyHULkd+15OUfJs7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50572, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo99w/9Fu268bRsBZhTb8DG+w6VSKYHhjRFX8ZoJ1n33Ero7tyRSQqH\r\nQl8i73YwJETQtxnP9h6/uhkiLV7QejS6VwwTuWmoK0BfA+1wiNvv4Xki9bKt\r\n/1LrN8oz5Ak/gemLpeg/FTPC3zHHHg4NYXqoWvFF2qX3z1z7RUGTSeWGCmdL\r\ndv1HQOHDJRdZy9qFbR8WMtOmB/wmu7sRROVNoNcZcWht02V/g3DwZXTuaTvA\r\nFJmbUsHGBP30KYq0nkMv4WoWh3m8IqsF5Ki6ILuDOljvllou/at+rBTzjX7l\r\nlQ3S+CSOyrJYuwRUTqLZkSk3M34IE1LX5XveA3pjfLuVo6NCgnkO1LOQw9g3\r\nVxshlvQgvAkt9Xc1MAY3jbp0PqRtUTXybN7wBR8ZEDEpkk4qDGx9H0DcJe/e\r\n3h60/LpU1/ik8hoGiE1ZZDR7WX/wQIRWdMd6K3GP0MsWe4ibOyDBs/d5P13/\r\nyBN0JiwIkFehbLo5HdT7vRVAURqvY2ijX1YC2RavoL0Vb7E1GFeb1LaKnxU8\r\npJH/2DQssmWXOXFdpgQxW43PrfWbaDWPHYYEQMdDaBCniGwTwPg9WzxRvFVN\r\nsi33AZ5J6IDAhOZB3NizUTEOvvjMyFn/OuyO80w47Iuefup7qq30lEszS+wy\r\nzKPasWk4gBvbZBDXDHFNnRPUvOVV9AeZsKw=\r\n=wxal\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.22.1", "electron-to-chromium": "^1.4.113", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.17.10_1651250257018_0.3664499310546896", "host": "s3://npm-registry-packages"}}, "7.18.5": {"name": "@babel/compat-data", "version": "7.18.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.18.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "acac0c839e317038c73137fbb6ef71a1d6238471", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.18.5.tgz", "fileCount": 15, "integrity": "sha512-BxhE40PVCBxVEJsSBhB6UWyAuqJRxGsAw8BdHMJ3AKGydcwuWW4kOO3HmqBQAdcq/OP+/DlTVxLvsCzRTnZuGg==", "signatures": [{"sig": "MEQCIDWgKpxW4f+nJwsIK2tpYL/6Ft6E3W/JY3gOnlJtSHMDAiA8AJ7v48FttQTNiw+stJZKe75UiG/M7UA/BIP4aokldw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiptvVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOaRAAl9DkNEKnZK3v/vHOnlPPWl1Q7AB6AFmW7FSgxwPWomRChZp6\r\nloYjDvgGwnR3pTz/vRTWKLzUs4AQtypGzK0eeb3UoVk/b3jSKcj9zMHViNOx\r\nwNqDBR3Aim7SJVbOJqU8dww2sztuVPzNJji1p1y9jkw/vU132sQCX0zmnUZE\r\neV3OxXXiUal3hMY15D8ljnArp/Bpf+Pmxqa5v3jluKM+U7HGz+v1+MsVg6Lv\r\nDTjy20l1AyMBr+dYjUr0WIR1gb5raw6lR07WoUGprOX5OdRgS7KbdATntqUo\r\nwL+OnWg9L8IAzap8cH1BkufsB1G3zM2Ygo2n5RfLvfUu/KXZ4b9d1GXzJrwS\r\nWdj6KZAdh7rFgcbwBKvy0J0jUlIqi5t1pvD5CXzTUWg1mhRFS0+xco4tzb13\r\nAmxSkF6wcxmGjP1wxEuZTk2vBmDbXZspbIUYW1BTgwMot8HU7MFY1mlP/oEY\r\nUrLCMBZDzvsSyaFmv5v7VV3v+lFYbVraJC4x9cpQ2LHCpeL51gRal53JOivY\r\nSKZx70xMB696G9KAMVw7pGBy4c2MmSyhaPMpaWz0f/0oXnqTCRZTBmJmL/Do\r\n4qk25LHwgeOCdFoIaisR8tjFd/sVD2x2UQr4TLOs+fZZ6ZXf9gWrGEzR+lRn\r\nIGNXpob4SuYg5dTIuyOobB0SGJxFqGJ6wHc=\r\n=PfBi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.22.1", "electron-to-chromium": "^1.4.113", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.18.5_1655102421416_0.6993254950914487", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/compat-data", "version": "7.18.6", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8b37d24e88e8e21c499d4328db80577d8882fa53", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.18.6.tgz", "fileCount": 15, "integrity": "sha512-tzulrgDT0QD6U7BJ4TKVk2SDDg7wlP39P9yAx1RfLy7vP/7rsDRlWVfbWxElslu56+r7QOhB2NSDsabYYruoZQ==", "signatures": [{"sig": "MEQCIHlNY4Ru4AXD9/KBp5S6Y2xmmIxIcsE9lqqpDYmO8CE4AiAMfXX5PTGnZBHVObSK03tkrV01qct8KFMH1wsiiHQnew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3axAAo9VEvB9DdKWWnVd3ACg/0zhTWpsSdd9UokjmlH5fh0C8Hmr5\r\n/UpQIKA3N8PkAXxSwOueR7fEUXf45/+zX1ik0p46RefiV6pBMmRiRMJRg/TG\r\n7/7EZhzwRZo/YrrzumysNft0THqbnj0/PUCo+MMo1hH7dci4TS1u4Zgvbiw8\r\nb0WKEWwPuHn5nV2JqTLAHmFgswNCxVJbdEBbAcktlnPE3NCWVfAFdkrQH90G\r\nKcEGZZiHkpcbEnBHbcxx2uwTdcmoTECzuyFAUXi3k1PBdsae7YNQsqLxZ0E3\r\nExDi7ZCIEU4dQXCfqowobi7Dh9SCYbcd4U/nNxpWBkUaaCAJ3WhOqhv8KITV\r\nCHQ2MqThRQjkeanOXWc+naLVd/L2sM4+L0hZdMz+Fm0bHMa32eETRofHG19T\r\nwt2oFQLHfH4OmDHf3ckvGwHzLn9iKMsL1Df3FBaEw2EgjCWiCeNQWBx0+ODR\r\nWjLvKOHhW7DXo/uJPkS8T2qS3wGf90yRltZ65vP7hMbktQkWq35FJ+cjwuxP\r\n3iyQWNxe396pPuM9kmmR5kaeh01rIfXuXdsd0mPyB0UrNftAaAqEX/ojfNLK\r\nn3pdVijbkqdPlUyYBlA4QnLYOhJufXKCJsM73AynqJ6d5eavjDIAm5QtFQD4\r\nVn43KJHKTfL1QVKn7rZbiEdJkyJhdGBXWos=\r\n=MrW/\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.22.1", "electron-to-chromium": "^1.4.113", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.18.6_1656359384003_0.5519589117082868", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/compat-data", "version": "7.18.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2483f565faca607b8535590e84e7de323f27764d", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.18.8.tgz", "fileCount": 15, "integrity": "sha512-HSmX4WZPPK3FUxYp7g2T6EyO8j96HlZJlxmKPSh6KAcqwyDrfx7hKjXpAW/0FhFfTJsR0Yt4lAjLI2coMptIHQ==", "signatures": [{"sig": "MEQCIDnJO6kpxp7CY6w00wHItPunJwUHdSD4UHUgiDXKUyBRAiB8wGNz656aNq0Wu5kJYARH2OHJFfd5986SZU72rU8Orw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/myACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0qg//aoNP0DRuIKdTlbzzjQE9z8vZnZEB1ldBweN+yOqsnaUAbG+7\r\near82oglHyIY6JcAN85ZK3EQqapRCmB/h1mvD7fjS8NizsixUEf7I4pYjT13\r\nhEIihDozO7cu2cSk4ytHE87CxWE37tDNR89JStT+idqrd3RLqVivppKVIany\r\nSp1OpfBMC+9+HfrN5NIc4/bhTTxa0iiP9UJmR44KNwUMQb7R/qAzLFxFa7Hc\r\nTGag5VrKrkfCCkeXZITJFTFkFBgfCveUsDDOK01i4fzJftzdQNaUBUxRCtoL\r\nI5fl064ACrEOAzJ0cmKrJhFj27GyYALNl65kIHE248JuW8FlOcZKNH4JWLoK\r\nKvLNXOjxrrf8xjb6KpgY2PHxIwdmn1Qq7vXb61dSlW1htgm0zIwb31HCO6vN\r\nMnsuahxEHVFrOSDXf82OhmKU5pMK1TqTCmChKCfO34d1/KksVf1uA3+TIC/3\r\nmWoxb919KmUgewiOgTRMcuSKluBfcVDUQWzXLw3/FVskH87J2Z2gGurlDBPM\r\nTNj7lD94vOiQvjxn0GXqdwWgpkcmXPZvKt1gg0cQCEA9wO/qpHBmfm/ExCOU\r\n9fSH6wkj256uenxdSswEFEINxaqOhX0W308hjmEzRU9V4lp79zLZijTtX3O7\r\ncIcOQUxyRS2+yjEdXaUYKqj3SpRr03yVuSk=\r\n=lroA\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.22.1", "electron-to-chromium": "^1.4.113", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.18.8_1657272753915_0.9006978695882568", "host": "s3://npm-registry-packages"}}, "7.18.13": {"name": "@babel/compat-data", "version": "7.18.13", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.18.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6aff7b350a1e8c3e40b029e46cbe78e24a913483", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.18.13.tgz", "fileCount": 15, "integrity": "sha512-5yUzC5LqyTFp2HLmDoxGQelcdYgSpP9xsnMWBphAscOdFrHSAVbLNzWiy32sVNDqJRDiJK6klfDnAgu6PAGSHw==", "signatures": [{"sig": "MEUCIHtpN5+YcWLzDpPK+zbwTlUvNG/HNvhUwFvbs0ZTL7IpAiEAnz+Ebf787pPMje9lHW+7roBqFcoXrZHIieojLsJkd0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA6k2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmppig//QqFzwTD8raGBExGNqYhlaU6/ILHSDgqmvdP+PV456+Ol2+z2\r\nnBZX2Ld8/bekmkcZKZkbkoIRQzVoqTMR3fGOsnJjgzWMuYB5X2QA+qXyKYQW\r\n54Vh3GX3kX7MNlTwUHnUFVTC4UP/WmqpYnu4dvE9qpT7CaBMQmKHojuc8srQ\r\nY7FXXr+Scxq+DX25PiscqtLnVA3CYzGqgAzYRH5LL/LwyotYGn7zFevc3CQG\r\nZqdxFwwLXX/7scpMwQTdvcQlii+5eG1T8euBjOaU3beM/J7SyvE//MPBXchN\r\nslk7sgIOgbTE2SLYvdpsSgrka5rJHM9nseF2H4lVWMms78whtZatotS2zXNq\r\nn75fluV1KBrUSnv4j+x5sXG44eEl23BPROmcqV5fAQN7qpcrUu5FZmu6hmNI\r\nXqEK8Lnvu8pi4GpJ9W/pkR5xoYEAqHDqQi7Kf7ouig4WWgifu/Wj9wcw/1Th\r\n/HeHIs5XBMAA7NduD2dtr4PKAGqpsHaMFF0haKNO/g5drkpqcm47HL6pNB2X\r\nCt/h2vKYlrCdvouNGf/8U4xK8r+ECO71w8ww8ljPbqA9iOMt3XLyeQIszqTH\r\nNa8jLu2P12OlkPeiZGZxTJYOn4BdLUCaVuHyMMm4K7+5wsRvOv5Psol8qmxu\r\nyUrhe5Gtdhgy++7XUo6D0vPJv8XZLlsDZJY=\r\n=Ht7J\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.22.1", "electron-to-chromium": "^1.4.113", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.18.13_1661184310587_0.06800520169534052", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/compat-data", "version": "7.19.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2a592fd89bacb1fcde68de31bee4f2f2dacb0e86", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.19.0.tgz", "fileCount": 15, "integrity": "sha512-y5rqgTTPTmaF5e2nVhOxw+Ur9HDJLsWb6U/KpgUzRZEdPfE6VOubXBKLdbcUTijzRptednSBDQbYZBOSqJxpJw==", "signatures": [{"sig": "MEUCIFnpax6IGM716pYYvdU4FwM9fwDIFjSsADy7UxJO+gwvAiEA8DAV5hJOvuyV9oUO4fChLKTZArCQwqa6sJv1skbWLeo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqIA//Zjm9TVHdTxwS7vSRgLqbcMpi1BFlsN2BRWE92q6Rln52CoZO\r\nAO98uJ44fT9G9g1muB0eTf5ukxY2a94liHQ+TY6iQwfWw7rvY3GGmK9E3m97\r\nWAVrE+FZBScAy+tT9KNfNoEn+zowN9pF2V1+4pUUf8484RX9FfR6ywZxhec1\r\nl3Im7SXNBBgluan1p7XRST/7C5JMhZFaOyu4k2l2zA+3LqxnBNYojzkvGWeu\r\nAgA7fg+ClN8EMv4Dq3p28UJMHL3aKQzZU8I6dGF2cRyI+LrFPE4AbZGsTpTI\r\np3iXkU99WFacfjkZK0nHee7Wf7Q3b/X6IkNI4hAz+EYg53ZM7xUmG4upEzmq\r\nRJSMZdKISkqvGt73NiQQmhzPo7oZLcSP8ly6ScKgK3xeMwfhU5+++IrgH6o+\r\nbDIp/JcFiICpyZ/jGSbmc90ZIXdNttm3fGdRjclui/S/3r0weuJaa3q1LLSe\r\nBTqjJPMx5S0wQZkKeWJc671+V/es31E8KUcICXYc/gnmrWBEJMS1d4+LU4/p\r\nrn1ROJDzOGhncCbtAgSTtzv+t/6E1EXrIpP6e5h8lYvYXdiuoNbKzNDKsU1V\r\nABjOK330OgsR3wTaxXcdsmeTZ6ujX0dN52uLIMcVlV0KH6Bq8PBu/FVhHJQ0\r\nBOS6xDOwgNqSZ97kK+dUZKgQaphFQ/T0jIo=\r\n=Kd2W\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.22.1", "electron-to-chromium": "^1.4.113", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.19.0_1662404532745_0.8608209231647288", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/compat-data", "version": "7.19.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "72d647b4ff6a4f82878d184613353af1dd0290f9", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.19.1.tgz", "fileCount": 15, "integrity": "sha512-72a9ghR0gnESIa7jBN53U32FOVCEoztyIlKaNoU05zRhEecduGK9L9c3ww7Mp06JiR+0ls0GBPFJQwwtjn9ksg==", "signatures": [{"sig": "MEQCIFOVbU7Ex2KdM60f/5RirbQLSyVcofZ83WJGmQA4cpLcAiBOyoYQnjLTKQLzeqwiCkTOXk/q52VbYvwo4z01s3+7Ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrgdg//SrMW8GlFezWXVd6VlyfzNt06jvACsZ+B167+Mt8IYnRGaWvc\r\nn8QjsFUDmX2L0D2cFeGLhWnm8gitGHH36Eh0583SsoX+gZ+EnIdS2CCc9TTW\r\nqiBqweddWNMM5P52ImWG1K9rEf+ADGMHM5c3TB4hExn2GLhgkuIqxQfgeJT1\r\nYDKG3OCEu/WA2t6O5jP0tuDxSKykLU3UJSO0TG2lnutUKirvQcCaIY21iqMV\r\nxNnPvhm61yyzR0OVWZtN/r2xpd3hwoAW4EPj4tpG8pn6DronvBQceo+2r24e\r\n3yQIfBStm/sz7iGwPuQmARsw5VrlC7n+srL/1XhKtNVcdOCfCg05gh04whSp\r\nSfnMaIeugvvEoVPYOz/0g4jC019MrdZciVBcYdDU3rD/nuDVBmiji1bcNpo0\r\nVJesD9EpzoUajWsHUH3mC7tEwtfzN9udMW4KMxCYnBA/gEeFb6dfbuahNoqm\r\nd4BDZwS4XgN1Q6PYcvLzciZWNMpGLSikml2vrrMkVY02V/ixk0UtQ0it++oT\r\ndJ02D+f9xhiaUP9xeF373mSN3rQfEfpyF6UJkpETjZ8Py87E//NhXNwTKhco\r\nhL+y7edbb6dTHZKHkfriuS4BaYft+QWBYXw8k9uLmBr/USEtR23uFHhyzDTw\r\nXks5Ow5EG7kM/nOdkmMoDGcBFj6ySBcBR6A=\r\n=XXzc\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.19.1_1663169349399_0.4279672060717754", "host": "s3://npm-registry-packages"}}, "7.19.3": {"name": "@babel/compat-data", "version": "7.19.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.19.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "707b939793f867f5a73b2666e6d9a3396eb03151", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.19.3.tgz", "fileCount": 15, "integrity": "sha512-prBHMK4JYYK+wDjJF1q99KK4JLL+egWS4nmNqdlMUgCExMZ+iZW0hGhyC3VEbsPjvaN0TBhW//VIFwBrk8sEiw==", "signatures": [{"sig": "MEUCIQCKG/i2HFe5QnbrQB+HHih71HhgpqWy//ZcCHnnamD+pwIgNUOUqX9sFyhCzV/QEeiQC0EF7zTy864Fy2M6UbWIEJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0K8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBJQ//WzRSVNH09P2tXTGgZsPcJU/6TUr+OjXThJwPwDh3GpqEMoQ4\r\ns34VQhhGEKwjhFUPME2YUXGZu27cPp3vXfv9Mv157xyMHPcP6ekWJfdbpbU4\r\nGMKRUIWyc60AgL23r11Ge7JSxyoaJHxiU9U/eVLBsYq2t0p1/lSfzyo9mix0\r\nxe1PsDn/znyFH41vcl+C6kvVB7l+qMZlJ9nCtivECJT5HXM7pMJOv+cbxcfw\r\nAAZKhoFOxeWcpR/8C3JfOu010O/95lgMgSFyRwafw1y2Snfd/ujJB6YSBpH9\r\nalRWMTDZJVsKaah8lYP8gaD1rwOvWGqUR94WDtcade374fR6wE6bd0niOMpe\r\npiSarziXx3cKqUSxZqAPva+fJUBvOOVddm/TsM6pyngDkElHF80qGRJt9WhY\r\nkhQXEyJXwx6H6PNLmdcQMWEMya/sA+QgMXQVQU8/T0mW+nYNp1ITuCeDPzTb\r\nruwZPRg11LCHcIn9GqAj2ruisImefAfv25LWtb46p50G1ggbwahD74OXpNut\r\n+BJP10Q2SPaDhKwNHPBWtHNUqtC9hlRDlE3Q41jqEFUwqmqTUD7xyrrrM4vu\r\nrEH0Im6qQvmnZaVIoadrtbrRa8WHSPtCqoDU5duvXFVKyN7KAGnjy8baFqAi\r\noBE9zjs/jGPYYWL6lX5hET1Z7J+BffBE21s=\r\n=mE53\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.19.3_1664303804132_0.5353006268483498", "host": "s3://npm-registry-packages"}}, "7.19.4": {"name": "@babel/compat-data", "version": "7.19.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.19.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "95c86de137bf0317f3a570e1b6e996b427299747", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.19.4.tgz", "fileCount": 15, "integrity": "sha512-CHIGpJcUQ5lU9KrPHTjBMhVwQG6CQjxfg36fGXl3qk/Gik1WwWachaXFuo0uCWJT/mStOKtcbFJCaVLihC1CMw==", "signatures": [{"sig": "MEQCIHzf33iCyxiZbSOM5AmzciKcJRc4yjSFTGWJaxkyIbUIAiBip9KkA1+VigqEfyF2W/fsD4tV3wW6/Zg5enGmGi9WdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2xQ/+JgQkiCPozW9XgoO1bgvBmSwlA4RNoepSJsovXJ/fL8mXiPTR\r\ngVjyO5B3fde6cLCn33x9AFLV7hW9c8FUV17OkwUvwPGzzgKzCEuVCx11gDAN\r\nTSAaJxkH5hTNDUmhDNZzLPZZHc1sQ1vqQFE9TJ3mzH4uuicrzQlBWbUBXGM7\r\nQLbxnMQPxlsMvumVRJ6XznOwHNkGIu3Vski2mH6ElHulbugjvTriue4ufl6U\r\nWpo7QZYgtzG/bnwIYZM2XzMS1utQCIa7CUaNUJ3S2dgcl3f6TlgXQ/OTEybB\r\nP2krGwfDlBgCSel+CUc28M/Bd5e8aLpDxk+K6hSGgC5nG2jIel/CFv0C5UCk\r\nKNZbWcUK1XnmRp6Yhfx9v0VUIx/Enz2wgY3EA9qwHeCsIvyDU3zcasWvUime\r\n/v5UyDwvHw21dUBSfgw/l8wtRcF1uBSUtxWxMEYZal+p/WU33grY9kuZZX6Q\r\nhU11BLRdU+JDdjqBzRYP/z3cBMJGdJmUVGQA2p+3/V+RkKwZX/HZknkZenbD\r\nkggUcc8QICX2ELz5XZE6mpPEa4TEuN1WJfWU56ucFBy3JRBNuj0awPxglEhG\r\n9lNPZoMIx/8eHJnzVQ22lNkKujo0gKL6WqaEQd5t8lPRlfnO3sGYO79VSozL\r\nxDz7TE8x2y3ZDTciDsy8xS0aV1OLpEPqUFw=\r\n=fCyn\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.19.4_1665398839192_0.6609713183316965", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/compat-data", "version": "7.20.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9b61938c5f688212c7b9ae363a819df7d29d4093", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.0.tgz", "fileCount": 15, "integrity": "sha512-Gt9jszFJYq7qzXVK4slhc6NzJXnOVmRECWcVjF/T23rNXD9NtWQ0W3qxdg+p9wWIB+VQw3GYV/U2Ha9bRTfs4w==", "signatures": [{"sig": "MEUCIQCfLZ6+B6Sz1bGFjS5+SNpr6SMC3zkb2YV8KGnSj9Fz7wIgJRps3u7HG4jYAUlrXJXKt36CNftp+3RrTgst37YhQ80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4Bw//Vk0+qMAcGZB2dYaUJhI7See+AUWZLzQpGM+j1Vrt/NXGwOw4\r\n37nMirUyStfA/f7Tq6IO5HQNnbMLZbbz5/XRFqcXyaI3k0VS8L6sN37HmEjl\r\nCtBxhE3xYn2ValGssev4YDYgPaqi4LNMpMzMaPbjkAILQ9CGrvjevIo4W6aw\r\nREUtlmBp9S9+TIpFAls6t3ad5unUImn5DpJdvPCDo7OZjBd52U8r+fx4y64n\r\n4I9QSZq5qzBCzWZbcA7m9B7/osVLvsm/4iBKGMFZjnKccCkMAOz56zKiB7wE\r\nIK0ZAQ3+AxGHxEcPodQRNcrVDQUP9zi3jFFlGJKwvodjxW7BzzKp7CM3C7lw\r\nQ7eHDfnzIdx3DZhZgQMO2bYqVr7AfI3kqNeekiHSAJo6H5RgOsbir6O2eZfs\r\nWBs1smXq6BzwnQpQA7o42nzvuDLy9iVSgBNq+OFiOWMmYqFFY/7H4apZ4h0H\r\nKV03DtdUWosDNP/pngA+99I0kQubv9147BaLh2/WF9r5BSfRAJEzE77VXGWE\r\nklUUNCNs9aZl+Oa+cPsDsR3LQmzSXXF2aS5N9ViP46xP42/o5NBqAT1JLypB\r\nDtJFCjOgBoC1SemMbmi1mnVIxV/jXUvSde97ND4q7E8wKFd+jabA+YhaSOSO\r\nlM1nA4sAf5F7N1pTi1evVGHDPb99QgEgerE=\r\n=N9p4\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.20.0_1666876744804_0.09289591292393395", "host": "s3://npm-registry-packages"}}, "7.20.1": {"name": "@babel/compat-data", "version": "7.20.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.20.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f2e6ef7790d8c8dbf03d379502dcc246dcce0b30", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.1.tgz", "fileCount": 15, "integrity": "sha512-EWZ4mE2diW3QALKvDMiXnbZpRvlj+nayZ112nK93SnhqOtpdsbVD4W+2tEoT3YNBAG9RBR0ISY758ZkOgsn6pQ==", "signatures": [{"sig": "MEUCIQDfiPIifIPVKrh2Kcn5ZD0Ycoj0KMYTPjy5ZGgy73HBgAIgMpkI06wGsJdVrEFT4HUIailNJdTpVeG5gUoBrQBi7wk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYQI0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVqg/9F6jEDDfPjCULBc5Pr6994O+AHsvLpT/1G5MRNEosuAokseeV\r\nxavadRlWy9gpZPYZOzoBYLBD4rfv9R26Ip17OlWsklxlj3z66OqNWc48r5sT\r\n8vlWcm+ca65IxSHVG0r+X+6lvbA8AycZ5wBmmdBSjfpnLpKJMdYICNDb8SpS\r\n48brOMUzuZF6+jyy2CV4xlbhQ1v8IchHUrDWlFX36JZqU8u91aWEyZjrxXp3\r\nCH2iHNXmmFzm0PB04hfnGZpHWNqmy2ddxPdBIe42xHFS1/rnhyaY8o/xcyZ8\r\n8CRaiSxCEHpDCQ4EVk/MgRTErcTq6qL0cCWtXrwAJibwc/jRGTHPSCEfnl/y\r\n7S29AAfi2paP0Ql/jj/bmMVy4R5j8X8Q2sHRKdVZ+xTKxF0BRRE5i0s6P1eu\r\nwc1x8qS8O+O98OOSHYzNbrLfEaxcEWguRKDp7Sr+eG5eUtNa9BllGDbD+/Eq\r\nnhawhxl7Peu48N/gBn4Loziiy7gGd+Wcq11OiUETv2sYRmoBeEPhsqiBXAfI\r\nXqd3Qe0OR/KbUI3nJvxF3IbUzn2MYaz8tT9oOMZl7hB1UJo4c/OUI4MdY2ax\r\nkZUmc2Pd4ra8hUPXG8kcq07QB7elvUqkqkJhhKY7viByf358m06buciP/y4T\r\nn5UY5bXMfnmFBPUTaV3CjKis4dGFmR7Gruk=\r\n=tW0L\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.20.1_1667301940341_0.7955702204508022", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/compat-data", "version": "7.20.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "86f172690b093373a933223b4745deeb6049e733", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.5.tgz", "fileCount": 15, "integrity": "sha512-KZXo2t10+/jxmkhNXc7pZTqRvSOIvVv/+lJwHS+B2rErwOyjuVRh60yVpb7liQ1U5t7lLJ1bz+t8tSypUZdm0g==", "signatures": [{"sig": "MEUCIFlkQF/1szR3JiOb/wWFPiXIG8IccGvP6nDH9qBGSu+qAiEApjtytr/2IRi5vW9O9V+lbnCIBrcrn4ua0y5ZEs3dIYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwAQ/9HcrVthuRqmuxRSuyPD+bwH4vWTORsVAie05DLDLpsxznxwxQ\r\n97NKZYhr0WIZ6lIPDJj3s8kXXOCr+8z6uC+qEpQ/QwbVbReq05kgO7zqe1J7\r\nu6oflBl0UyOsA0tqI6YS0Gb69b327zkhT8/AVsYkd7sHYoD6s+tlaQAF4snF\r\npZu1N2M01JK1MDWOGyi8pgUIMjGrzyxNfwzwBRV5qgGrZARMujiscgo5bkP/\r\nQI5Wlqfffi1g9jj6+SlZDEmI9XcQefw1ajMKny4o5kb9jHL33OEFUKEhYtB7\r\noIDvw7vZ+dWu7MSacelmWSoBVrus8ZF2dqeGagYEyscBsAjUaK8hs7Oeyw2w\r\nOi3CtAUHY1BV6bZTS/d/U1/FcgtolJT2yTsF3tHPrVVuVfHkkGB3koBu0qP/\r\ny8BaNf6yzaR4Va0MZknPg55nx1hS9XHGqcdIZAj6KVtY6DLZwM5qXsiWI8eZ\r\nXgIMcIuS1rTPma6E4jndV+vnFU9m+yE4hWYuRsPgrCdI3tn2DBSNby4ZGrjZ\r\nFCZ+njyGVgA4BlxP4SETGED0nbpKdrKwbbKF3Up95PgU6K18y+Ek7A8iRU4A\r\n3SZsW/EXYzad7AJb/mq3DZEDTxuZYuhehhM2IT7yWAZnfErM0gYoCaQTx6VP\r\nvT4tGnP7IV9JDHj2n+ByAHSwl27vgFRC8BQ=\r\n=sY3g\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.20.5_1669630362567_0.4524469137825373", "host": "s3://npm-registry-packages"}}, "7.20.10": {"name": "@babel/compat-data", "version": "7.20.10", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.20.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9d92fa81b87542fff50e848ed585b4212c1d34ec", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.10.tgz", "fileCount": 15, "integrity": "sha512-sEnuDPpOJR/fcafHMjpcpGN5M2jbUGUHwmuWKM/YdPzeEDJg8bgmbcWQFUfE32MQjti1koACvoPVsDe8Uq+idg==", "signatures": [{"sig": "MEYCIQDnsROyNNORWu3ueDBq0NHI7VoRZUV6e8zpWDu7HJcYsQIhALZL2Ic6kHjMEFPsOD8c7AVifjjLm+36KX5+VgDMZUVK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpXKOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVeQ//aX2YFUY+cXgG0b1BSRBbt7+Hr7KRnpBLt00ojA3P8M5VhkCr\r\nFujJmLDhUdty677MfpC6HF4d+5B9l2kbd4x5gXfkA4fmhl0HMTx4PbU6I45q\r\nBtfDd1f1SdaQMFUQEJnMgTlJHmhq3JNh7bgj3FEaBkU4iRzo2K6Zk9489o/v\r\nwA3tD6KH0EARCuh9d5U53qnSZJptL76oneXK9ovm1ZFny+yPCRYPIfhZ14TB\r\npGTqluErLcCo3oB7K2T+brmzj0Stx5NvJ13wlVrmuleCgoHHshurOUDZ7L9b\r\nGJRPz82YBfu6fsDnnjqhqCjiMWRL3OmDFOmbyWNLczvF7UPGHb9O3S1S3DWb\r\nShjU5+B5beSsgNf8HdJ1uuXqJ7aeSsHcgf4IgOnZJ7gYvYu00vjWScesy2NY\r\neA7pwCN/Oz4LRyCD1Si2vlOv3Cvr9eg2mS96zbGMQKvB6BkbTYBOJVa4Pjew\r\nEJHx1kN8bQDHX2l2+wf128rrEDf9bBojk8jMkKhH1rQycLm/n6Lc/t571uYT\r\nqcFqDcZZJV18PGMhstGply21Sfx8Hjg1Afwn+Clh5UbPgRPGZXLIIP5CbNLg\r\nC06O8ra0Iwvq0oj8AmJjwGwXzAJhK8YUmTs2J7S/8SIzjxkzJ/FLCftV4uzX\r\nOYDKmdn5pUy7jNqR2WCdReHspF8o5/MHqpE=\r\n=995p\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.20.10_1671787150416_0.8970761659326081", "host": "s3://npm-registry-packages"}}, "7.20.14": {"name": "@babel/compat-data", "version": "7.20.14", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.20.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4106fc8b755f3e3ee0a0a7c27dde5de1d2b2baf8", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.14.tgz", "fileCount": 15, "integrity": "sha512-0YpKHD6ImkWMEINCyDAD0HLLUH/lPCefG8ld9it8DJB2wnApraKuhgYTvTY1z7UFIfBTGy5LwncZ+5HWWGbhFw==", "signatures": [{"sig": "MEYCIQCDoxLhuktBAS/WaolpTcV3kkeyiwuF+zEUW8Tz+4ZdPQIhAPaiHI6GItzze7+Je1+uZnwBtqvfYmvIfoNAlypNTOe7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1Dq5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpCQ//ZuNBEboNvjWmG1i81whjZ9yZTDApuLHNxdL/l+zoUAMuWMIq\r\nSIonchQbCRPHpLAfGBgAzRunbzf2kcYbhq0uCU5SV0w/encGKdH4XF0YghCU\r\nuLqtcE3hhyj25qDBBK5uMAnakZNlTr7kHTgtYNiURl37hPk2lwO3hQ31RbJ/\r\nAC3FJrS7eY3VSee2Ro+5d1S7dn3YswmVPPB68IoTXXQJJPaB6tcarBjiqCIS\r\ns/jmTFuEf365GqgOV+kj2ACimqT9rAHAFTepoNKQ8ZNLaZEVd8t5cr7z0cPW\r\nGUA2JfOuoVR0A5UATjIY/a34N400pAcnzyI6nSUT7MC7NtWk9rLblDmd1Uo+\r\nLuJk+GyuYHox7YplJD4zVClXM1G5GCn30PXPhSnmWb6aqSGwbbhQrXTMGZOS\r\nqWQRGXnAmgnDoMwn1C1mx7TqYuDhkivsot09z6OGPzjAp95pAj5mMi4jcEAx\r\nmVfA/kpNUOF1eENIuWFuOSxgrF+5yPZdZjQAhLdhgcMPPZUTD7e0eEqjjj7E\r\n5BjXwifAkzsJcT+m1KhhZDIRHCiLICvhZFdRgLTFM9ewJlW4TwZBIWgpFUY2\r\njx1Tquh2AzbPdJO6UHVVoC9gWAL3QgXj8kptG7r5eXF27RkHQ+bFIoEQBSWl\r\n87HbRqxWparJaTUjzSNLU/h4LbxdvlM9OsU=\r\n=1PB3\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.20.14_1674853048762_0.890999566248867", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/compat-data", "version": "7.21.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c241dc454e5b5917e40d37e525e2f4530c399298", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.0.tgz", "fileCount": 15, "integrity": "sha512-gMuZsmsgxk/ENC3O/fRw5QY8A9/uxQbbCEypnLIiYYc/qVJtEV7ouxC3EllIIwNzMqAQee5tanFabWsUOutS7g==", "signatures": [{"sig": "MEUCIAmMCgSMp543bWOScts/lAmHNPYJ+qOOur9gFIBDuLCJAiEAplIrPqCcGHS9cF+g/QVp4FqFcfQwU9mSblX2DxCXFYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAEBAAm/M1wWxbWFNsgpAN/k8m7DvNhUiaRlIfJvyk+f1ZOtYruF1u\r\nx4gSDO7/ttXk8Uzw6WTDcakzLoFZAx7V1NCTUTUcuIa85HWV8MP9hWsEfB/U\r\n7K0AdOI+PAfbMrtgBJP6GeRcpMpZAM3rSml7xoknn1vL5CXybUU/YM6EC4mS\r\n32EJK9QBAJ7PJUryqS15NzKMk3abzhS3JATO8k5VLQAtMBlwrpjbFe7FLRqF\r\nE8avPauh77cIwEeaIlZyr4E6TT3oQqE+GTYSckEbIy7jiQl9FHey8rw9ZYBN\r\nPoywVnguyzfLEUSDiN4IDQgPojTOHrfdzfN8E8oYpMQwhh6naOYw6jtRxlWG\r\ne+w4bASi46UGW9xSVZROe2PJn9dytpZv2FSsF4U3m0GnVYu9bETc8qe4Xiwq\r\nqwZqfe8WzmAFZXzbc8ZfMiDq0EBE/u9z9itKQWjyt4efHbJ/V79GBBzq7oWU\r\nWAGZKDRKhmT1rHdGmy9itXo7i/aQRjOP3VFGOMCc/MN1xaTkCbyxWTTtQOrS\r\nSK5xkBDNS1nV16FcLRqFfkYfjYgez6SJdUN7L6uz3BGz5uiqtjMnxBAJ7EsT\r\n2J04Fj0urJNtdVyKWBxAa6yYdr3nPYPXo+9ER/CpKk8B1RAKJMMbQ7e9rta6\r\ngXDapIv/ggkiLa4qYpfsnMDIky/g6McENBw=\r\n=5+lv\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.0_1676907052708_0.6941660007265233", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/compat-data", "version": "7.21.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "457ffe647c480dff59c2be092fc3acf71195c87f", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.4.tgz", "fileCount": 15, "integrity": "sha512-/DYyDpeCfaVinT40FPGdkkb+lYSKvsVuMjDAG7jPOWWiM1ibOaB9CXJAlc4d1QpP/U2q2P9jbrSlClKSErd55g==", "signatures": [{"sig": "MEQCIAto+vkaeRw9+H+RYmml3kA4RQHCrIskYTMwMe11akfHAiADNFvQgzYE8mujCNV2AlddiS6r0WIkCRMD3qPVTwQOCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqF+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGRg//aT1sHERA9Ohw4fd3VKvLZY5bwRATesOCld7nuejK+oNZTO2J\r\n+7JtPHOdkpRC6zdWnXtNP1h7Rf+baKKuimGzYDGrQdAGJgAQuk717zW37IT9\r\nKF0H9yqLu5Q6Tj67vnuP9bwV/Gpjb1vFNoUqDTFhKgXbKOaZZZF0MEj2WZbA\r\n4YE3uHDICpU81OTL5F2FXBv4Cgccq5E27VGkjU2O56P5Jy1+YN2JgR8h7vOY\r\ntWXu4GzvZCrLOhUZ0rIjXw/LwGq9gMZqyDuscW1eujcZ2JMHWhKZDKT0e1Zt\r\n/I+OSuO1IN75UYqzDdI6nQKd8NTV+s126hIMy6jR25EHvnYBi5vcwsSacslm\r\nLc2jxF0IeyHcQ3iLCL5dEN1SUofkUh5lPzJNSSHt7rgL7uAXtBBWjQuYn4Ow\r\n3fX6UUnO6jF4tiTlyd38Qp44NvNkmWgzoP0oAhkw41UprUjnu9u3rAnUhfp2\r\no9UYVnOUNqaNhsbhxqX0aWrOVFec36nqve1ZyHjD1fnBMbAiVdfI821+F0fu\r\nhADsvKkUrm4qhdRwFYyk4awP1hJM3evE5zPrs6CASJUehsCzKZ7eyatoHdDh\r\nfXENFP7VzBmTbzO5TZPTNTbCANmAMSZ/rOm8pz33OYxXfjFSvANo59XyTPHh\r\nKN2Bupae7SC6xoEIkd8cf4mtwULYGBknzWM=\r\n=9PAh\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.4_1680253310678_0.057771018661903595", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/compat-data", "version": "7.21.4-esm", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "08fd9f337a4931f90cbc3a31d0dbefc965cc0a98", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.4-esm.tgz", "fileCount": 16, "integrity": "sha512-65qmjFRgp3fC0ouLSsxphe76D8HGQe53FnJe8KqSkFXtVWf8OrKAWOJYV9OKJ46M4z4/5T6uVmwN1fVo6hKONQ==", "signatures": [{"sig": "MEQCICW/KhYvs/XtBdfV4PF1nO76JFfXiMMr8u6+DShofUDcAiAxGGer09qmCU/30XFRd1Ej5vmu7a/Vs1vlPU8n3H3BIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC97ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqX/A//fF2t6s59UYiK/u9OqEbekCRsaYYB30d2b1xlTTTXcFEKN/75\r\n0ajXep7//xT3phMF0OXTsAWyhRhXjM4abChQd/+MpuTBp8tOBdAllvODYCe1\r\nkHOXyuHUUAPEsEpAArReoDJ05sBeHSZ2Cbd9oQqq9EAE1/G/1iB0p+yloDod\r\nTQgUxsDlx8p965CIn11rYtusmPG1U/f9LCIiZ+W7jqtrhKROR55GDFdrppa+\r\nM5C5zmDDfR3U09kOOhZg52FSKCkEFHe0IxODn1cVS1du69Ks8beyWkMB/T+b\r\nNLMdCXDUwCBZOEKEt5MFxpq0N9nNLp8/BXLm142vMz0vqC2isIxAumOg/tHl\r\nRUmQFfQLtoUUh82vDBLaB/nGc8CNxyqUlC7+a4fDOiCaOuou7As//Lu5ZYDe\r\n9orPdm6rjNWB6nfgT2QimqpZso9skRN8rM9cEH0ezB31mySloNo3cjPJnh7O\r\n9SWgwe38KDo2A3NpOuzPce0aiAgWS28wenFmMFQXyYox3KI6MWOFIKMI3RBa\r\ncs0c4zCsOJ0CD98SrlFoxuzX+6thnP2pZyD5kTUVpt4tu8HF08XexM5u90Sn\r\npj1v4Ip4/lSFyl9fUQnL0dndp6JX7PDNW/6tV4Tiu1rwB3wdsBf1mtW80pFa\r\njGncrIc1agzg0S5RhwyWLADzCCuYJ1YMFxk=\r\n=VhF8\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.4-esm_1680617339068_0.316885279333178", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/compat-data", "version": "7.21.4-esm.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2a6e1a74c8944d74a1ff3b98736d352af016debb", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.4-esm.1.tgz", "fileCount": 16, "integrity": "sha512-caYEGn9yPnHlaO2zgE6CO27SnMGTmTt6e6mlneeyWeEImWBfKZUG5XRtbZ3aZ2EqL4orkGTf3O+6DG6NVbNXOw==", "signatures": [{"sig": "MEQCIGz5wFqT5MXwvPrOSV2MFZ/mDkEd/0MYwR1H0F53iV2uAiB7efEPUEE1jraA32p4CGWQoQd+fVAJTOnKRriGeh6HmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpj3w//T8phGrGs0ylTiva7IazIqO//DiqjB8y/kZH2c+BolyklU48q\r\nT7im9qcVB2zrhxGu/jbLgCAV8ckfrxGYXn3JoHVmqZqAaOOX1HCzHlQ5u2gU\r\nLsT2+09cSH9o6aavaYoajIb+I34uEteSW4gkok8oFUbLlazA15u4EqaMIRkm\r\nbEMXfnvxvcFS8cWUuZcBhKZIxqjEjc86QeK5l6uTOxVdEgi6NhzvX0r/vKAO\r\neOpWDdYA8fRcHmXiG+zUWXQANQBaU9qmkdqxP2ruWYAjLCCVm3ZP8pAzPCIx\r\nzojDGh8GZvzSLs0DnDXMOHPVQXrGFhFimV8hf+dLe9JEqL5fZ2R3kKMrStFp\r\nrIBzLv5yo5O6GQ9o/gdozzr6bRhtHB6q5SHojKl3oNcda9VaMS60zrBdGgLu\r\n8o4WdMOvBZvaVh0Oy+UCiSOwMxtg1OBo+9o1AvlC3tHv9dsTTo5QYwHKqcMJ\r\ncbgIkzuBLpsnpmKX3JiWKyexGsPv3dlC+BZhTvrn1YbnUTmr1k9ObNtDQXO7\r\nvzPu9caT8hF9xG5DDXe6IDOCEuB37kWI17zSBqFcXHBJ6eiGwGPBmvgF8Ufl\r\n8TMoaJS2HtyOTM6roy4SzJHwXWAkQBOwmvKnBCK0hmdoX06UFuUhbmoJQKOi\r\nUBLd+ZQlfJ5uPxupaoJBPtUS83wHTXrLO78=\r\n=XdJe\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.4-esm.1_1680618051390_0.5519900599991823", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/compat-data", "version": "7.21.4-esm.2", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "42e827e77e046185c025e75d29d195a319b41ae0", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.4-esm.2.tgz", "fileCount": 15, "integrity": "sha512-PP7ke9bXuAoob0KHvGZI+Pg4grBPRkcXgBqZthrO2zyeUD9wVvDilCZBCt40bsIw/gtQ7vwtjGUhwMx/fhy/6A==", "signatures": [{"sig": "MEQCIDovxiYsces5oWuDyInvezSjE96+Kh0Mm4JSZLLXQFHVAiA7AidaKEu513a4uCrZUVquHLJqnA1Kxs3EbcqeN8EiQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDkA//dgO874ib1sc7LfUa5iuxgdiocgXGVRj8nFNPoOaDq0IA2qr6\r\nvSk/OELiI1HQG5w3rYnEnBRnmKyVmx2M2suZtfm0yeJihtjit/quthAs4o93\r\n/2wCAkdKHvR0BqI4Paqn2Wxz63mclg76XUoz/QqbCFSA8sUlNP6YN+DNfbrL\r\nJZhVbKmkJPEpL1ghEwZ5BYyTgQs/5yaUlOAuAQuJRVxk6a+X+sUtISw7gsAk\r\n6KEWW/5LUthcZx2QdeQ76IeHVG/QOo9vJsXFlzjWDsG2KZ+KS1XrJOkTWnrj\r\nliHT8wa2KuxSOMmxUKEgNbnIprckmd+xFsWP0yp02V4hReblTI4Gb0Iy5oE0\r\np3RDF1l1oEoZEO+EdCKxYwGjn/X2bVYrsBc8QNbyn2jeJs5rl5a0GssCkYLc\r\nBK/1WcweP+B0huCo86JboLKfyga2HPk169tldsO5VjRWwIusB4/9Kgf+Easf\r\nzflXX6NpM/owFKKKk6j0ZLvv004AJOePDH7Uaz8dmC0tTpE+xnWy6EDymcMX\r\n9GJkkiojKI2Uy84cyKtLHUstbxvk1hCiJs87Kj1SEBrcCnIfFhN86kPqXElS\r\nioo2tejr2V8moKd3oOp4z/rWh6wW+Ygebrg9m8Sz9yTFEWL7fHyzPUksUn8t\r\nWs3RHxoCFYT3w7Qtk2MZyz2f9Bz+Nl9Ecck=\r\n=Ffx/\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.4-esm.2_1680619110679_0.848261561782629", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/compat-data", "version": "7.21.4-esm.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0eb946006eff2f7296c14e0c5f6e263708bd1cf9", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.4-esm.3.tgz", "fileCount": 15, "integrity": "sha512-sQNXYiWeLOjD96byQhNnCGcS6mNECxIF4YxXbltvNTI5ukFpkPH8QOXQpoUPoNCYlr4rXAsiunXqY1RbkowY0w==", "signatures": [{"sig": "MEYCIQCPXD8fR0UV67BAtPPisBJQ/pIuwHPAh2kkqxwRsaH0/wIhAJyuhg/t70a0RdLxj0Hse/qzD3p2faK6PPu1xJqLQabc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDpxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJyQ//ekAiK7Ja1bMa8CwJ/axOkXSmZo3tnTUu6/xjtq0aIkvBw/cN\r\nzX+iKEdOFmIMf75FO8Jsg1iMWlYv2ZyqWRSY3ws7XwoPlkjpt/aBr0FLsiCA\r\n4QvhKJDLewisim1mu6u6g/mrF1T8ooLlTayzyNDraTvPxC1vLtTpLwR7rZln\r\nfzIJOghiGHutbdLiLlNxRunLCfMZRcL1C4CEnt27cn9I5aeLdDklR27GtL9I\r\nRT1sltYYPjBkxEx1Hl+V0mw7uUXDMbV9gOS8RNQwVhsaiw0bDXDobRcTIEUs\r\nThndXtZzDW4hb/PVcagXGZX26OKzB7+p8o+eukrIIHehuuRnF57rKYNMW4KJ\r\n3EtwWasL0qmINJv8tDtt5wipnoJhR7oLjTrnMeiWzYlcytUBYq1ysfKg74N4\r\nSPOq/FOoAQbIjyBNSBx2fKHA4WunysQf58rA3O641FrJwKhlCwkEbop90A+N\r\nqR4l3hb1Z6NxZs56MOvhsE19cc/yELylBnO7jaOsfteRsJwLK7ITRfoVt3Ag\r\nzHaWMJWKbpYDX2reeYriLRudivZyRNv2EAPpvDKNiEWuZOAvPeLf2mFSXlDL\r\ngTgRpm5LtTrl7trEzuXxunX+8qdY9rEcFrz/gUPl+uFvFIVV63NQvPsouon+\r\nIrjSnaR0CVwXKOzLSNVrrdYfITqlyHpMqEY=\r\n=Wz80\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.4-esm.3_1680620145426_0.7949743751290723", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/compat-data", "version": "7.21.4-esm.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "03311ca2ff3f284ce7138b8bd03ab97a1ec89dca", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.4-esm.4.tgz", "fileCount": 16, "integrity": "sha512-FhxUiZ9bjGzrZIVAb7qoOh9F+PESaTLtkejd23lULJYLlR+m+ZUPsDvwly82bZR8sesRi7OU6jxgE9aBMixQbw==", "signatures": [{"sig": "MEYCIQD6uB094M+MJpqEkAV/sGY8ecWoVEzoA7aFt7VkKJxvAwIhAM9IUDHlKIF0JXapT2Q8ka6D8MEd/J33Y56S9l4Q/Cou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57896, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD58ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXbQ/+PhivYSNteDRBvdtvzZUBdvtXlnFJ38GlFcZobJBge4MRHLEG\r\nx6KP/dPvmZ4FjEqKcV6887ogfu93+XK+lpIcPxLJDlgbEOdTonhw5aT0KOXM\r\n65zkVLWn2gJtMvhfS9KheaxEqjo/RTcaZASpw75nnZdYxiSWrfoaysofs/+u\r\nQpQWQ9tv6a7oxBjo+BZ3140I0cP7zVHRhRH6GoaIyjVmBAwStKDs+sKisHcy\r\nyHLsrXLzBi/rNsGVfsl6zhtLIgkB7N546fGaCGIxAVU6Ynmsd+/09mJlZa0U\r\npnMqWXpUZUWWa72QCGG75RWwiv0yxEYj6c8k/bIptWkPK1kqezh8It2bnwqL\r\nc1GT6Mzf7pUBWjS0iMKZBYfuwKuqTzyd6QYoVrFsqdjFy2MSo4wo/EH2sLyA\r\n1Ishl8RsE1crPUN87HhU+uwu2MqcZlWxqHHZNLDCXZLe67W72tyW1roEyWKO\r\n3I9nQn5vOHBAmsRy9OEzwmQvf+uwKrtWmM1OZMQFXIrPAXsKPm3+xPNfJqWj\r\nrwsihnRBy7V8ILx/i3zI+6sEQR9ujFIlmAnnnhrOo/6/YnSVMM23+q/y5jmO\r\nPkN3TCTt8wjfsis4ApFrnLqH3cYk/GgZ10/J4QuE2WClfPM0/WcZlJoylPFn\r\nOuhzhzc+vfhkNbn1vfGwfsyP7vPVuXngGK8=\r\n=aLbH\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.4-esm.4_1680621180130_0.6738517916809512", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/compat-data", "version": "7.21.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "887a54ebe20c9f21edf27caf4ffcf8da6fdd5bd2", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.5.tgz", "fileCount": 14, "integrity": "sha512-M+XAiQ7GzQ3FDPf0KOLkugzptnIypt0X0ma0wmlTKPR3IchgNFdx2JXxZdvd18JY5s7QkaFD/qyX0dsMpog/Ug==", "signatures": [{"sig": "MEUCIQD07MUWMa4N47Yb/hbJSVAnSH5SzSN/6G625gC5hv6osAIgSWM6WJw2reaDzrE0ujkyE929RyJJcg93lMEdoKpC4WE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8eQ//ezbUmYpqq/qqAqvqmV+1DK6E+CnvIjTONBhTZqL5WaTcdmjR\r\n15krdIrk2dllmwRleHHGJktTJDmqH3eyH2ZNkdHNuSDV8OatjMkyOxVgiVF7\r\nQPGPpgSIjsMTcvX1VlRm1G57F9jGZAOYEiUvjDMC6nCsHYNyJwfm9FiGe5/c\r\nPRp1QEbkb1XrGKm7GBpZsLj1TMNCxOBda8V/tXgzT8bEbXdbYyb3ddf9gUqI\r\nUa/9lLD4oPIdjJR5CQqr1ucdC3bF6NB5VESHqvDwHVolIb/YzOyvdEjDPTim\r\nViAgGrDO82mhy8cjmSeEDZ96IuXBOZpW3r3LTR+HgQ+Qioplf+f6fM73TRCp\r\n9qiaxeHK1CuGsIZtxzZMI9Nd55Grq/T8zJLXASYHYEPaIrZMQdTujIcjThkA\r\nu7pM7YO6aYZu/4zArtH5yzl6NWVbWQ2fPUuyKbyK5Q32ShL5mCDDmtn+eTzN\r\no54SgV1F3yHjlsnywOes+k4gpyNrz9kBuhX3c3w0eK8fu+kigbHuwms5OvnF\r\nQUuDfYNkkVnPQp91KPn/IYNVyverr14j3txnJhJAGtWYhFAmMxhPgh0TsEDh\r\niRuRRvj3DRqpmMVo74M/Kbep7iqE3xzz0e2D7LCpxI/M9EyPraB2dqvK/O7o\r\nxe7IFuxYUgcN7D6qJiRWJryh7xzltItH4M0=\r\n=gInl\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.5_1682711414260_0.31494844375184283", "host": "s3://npm-registry-packages"}}, "7.21.7": {"name": "@babel/compat-data", "version": "7.21.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "61caffb60776e49a57ba61a88f02bedd8714f6bc", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.7.tgz", "fileCount": 15, "integrity": "sha512-KYMqFYTaenzMK4yUtf4EW9wc4N9ef80FsbMtkwool5zpwl4YrT1SdWYSTRcT94KO4hannogdS+LxY7L+arP3gA==", "signatures": [{"sig": "MEUCIQDtSt6K/RA8Nfbj+4vHK+11SuE8Cwf/+NJ3KbhTN391wQIgbzKSPWLF1glcvw+ZhkkWwdLYBHEbcYMjU43aif9xsNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTSdJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+vg/8DbFs1ty9iHHpLKSvXwATl2zmpi2dmQDybYBFywiGrPCTU00B\r\nJYxbO0e4z/La5wKuiTVGVoneVkWex92AM3c1Ti3WzNdTkoelsjxwhE/0ZdjB\r\n+3r/jTt39S8so/Y8Nn5wHtMCt8NKTEpFAtfvZgfnjzDSZV8msKPy4GK1tYNb\r\nahKdROHWlrHkxIecbQlB81oFsvy8dZDlfaTZgSTMQtbzZrvaKzwlTMwh2zjr\r\npp/5LuFphtKB/pjqgYLUdsruKR0Q4+ktHEe+tpRFMJhLd3g+I58rrj8osgKE\r\nbCa4Sp8N+QhQXTdK0KCCSBMR6dp8iH3+0qxX6tu5ioWzoeNbwa7trW8GzSOf\r\nfRe2cEQOn9fNGz/XvKh5bBcFSL6rB3MqWS8G604hv8ZUuzsOAAEcftWpHnYi\r\nJ+q+TOc9XNU+CVJpXc++q2U3wuajNhYKivMuXrv8G/JXkpnRyvhuIcNTgqQf\r\nIxvnieAvqqfcVoUo2mM4rogBX416ww3VJu3MJIzRaLjF0WXVDEe8VryzMlPx\r\nMwhSN6B2BAnOMWq+MC3LqjxV3bp7RqckyZLMn4xrMChxo1vo+8wW4WRvFIkR\r\nV+gWIUSbvF3T3536jY5hAQ6OYJ/sEoJi+7UdM5bXlSyZJmY+pVwolpn5ga+I\r\nujFxCBGYJm7MeVqU0n8ZW4AV6eGBOmW8R5Q=\r\n=uvDU\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.7_1682777929206_0.45375329495462324", "host": "s3://npm-registry-packages"}}, "7.21.9": {"name": "@babel/compat-data", "version": "7.21.9", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.21.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "10a2e7fda4e51742c907938ac3b7229426515514", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.9.tgz", "fileCount": 15, "integrity": "sha512-FUGed8kfhyWvbYug/Un/VPJD41rDIgoVVcR+FuzhzOYyRz5uED+Gd3SLZml0Uw2l2aHFb7ZgdW5mGA3G2cCCnQ==", "signatures": [{"sig": "MEQCIGeKkS8D214PPkw91LgTI9UckVFconSm9R9NpeCRIOHAAiAJD3FbH+mdJixHYKSLMI8zJML/h2mgT/ssCHfqlPlweA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58034}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.25.1", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.21.9_1684749739268_0.36481155100500695", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/compat-data", "version": "7.22.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bdceda7e6bcbe92475b497e35c868479635affe7", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.0.tgz", "fileCount": 16, "integrity": "sha512-OgCMbbNCD/iA8cjMt+Zhp+nIC7XKaEaTG8zjvZPjGbhkppq1NIMWiZn7EaZRxUDHn4Ul265scRqg94N2WiFaGw==", "signatures": [{"sig": "MEYCIQDT6DQi3arRPrEbVaHHgLFAqIo+bg7CG7PLoUmts6WcvwIhAI5q3TFmqgRS1Am3zBZ5BQSFe4QO9Fy1dSlBfH/Fd4r+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58153}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.30.2", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.22.0_1685108711521_0.20218634480126751", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/compat-data", "version": "7.22.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "cd502a6a0b6e37d7ad72ce7e71a7160a3ae36f7e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.3.tgz", "fileCount": 15, "integrity": "sha512-aNtko9OPOwVESUFp3MZfD8Uzxl7JzSeJpd7npIoxCasU37PFbAQRpKglkaKwlHOyeJdrREpo8TW8ldrkYWwvIQ==", "signatures": [{"sig": "MEUCICeVrWZsQaqmbZV4qLsSqAoKguo58Lsy0DAk7gPvRo7LAiEAhXcF/iv4jx0UklbehGUa6E428bQA/xE+9XvkICepGIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58133}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.30.2", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.22.3_1685182250266_0.6456864131676772", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/compat-data", "version": "7.22.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b1f6c86a02d85d2dd3368a2b67c09add8cd0c255", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.5.tgz", "fileCount": 15, "integrity": "sha512-4Jc/YuIaYqKnDDz892kPIledykKg12Aw1PYX5i/TY28anJtacvM1Rrr8wbieB9GfEJwlzqT0hUEao0CxEebiDA==", "signatures": [{"sig": "MEQCIDSoZELwY2MKlHMdEJI5qVq5vJWSovH8IcyztS9239dYAiBp4ed2rcUkbDu4MpLsjd6cGySiowtxUYGcGS1koTuNsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58133}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.30.2", "electron-to-chromium": "^1.4.248", "@mdn/browser-compat-data": "^4.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.22.5_1686248460751_0.8444004965279936", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/compat-data", "version": "7.22.6", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "15606a20341de59ba02cd2fcc5086fcbe73bf544", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.6.tgz", "fileCount": 15, "integrity": "sha512-29tfsWTq2Ftu7MXmimyC0C5FDZv5DYxOZkh3XD3+QW4V/BYuv/LyEsjj3c0hqedEaDt6DBfDvexMKU8YevdqFg==", "signatures": [{"sig": "MEUCIG5K2T2QZU4vx0GwA3xkZOj+1MiLjgVdLtdPHrWQ3fLWAiEA0bVBPSpWFo5uCF20Wt2wZmS2LpmKLdaYdRLqEsE9heU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63869}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.22.6_1688456927907_0.9751493874967077", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/compat-data", "version": "7.22.9", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "71cdb00a1ce3a329ce4cbec3a44f9fef35669730", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.9.tgz", "fileCount": 15, "integrity": "sha512-5UamI7xkUcJ3i9qVDS+KFDEK8/7oJ55/sJMB1Ge7IEapr7KfdfV/HErR+koZwOfd+SgtFKOKRhRakdg++DcJpQ==", "signatures": [{"sig": "MEUCIFMD856ZUSeHWey3hIzWLXOS/U9tFiXZo9XMcfuZZeHkAiEAyYrEd1zR8KECOTwcS3aYnivrIuiAI5gnp00cFmquvSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64077}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.22.9_1689180811209_0.29463547823304026", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/compat-data", "version": "8.0.0-alpha.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "517dcf43f5a25347587c60c840780b37fc532d7b", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.0.tgz", "fileCount": 15, "integrity": "sha512-VrHQ06dUX6JlC3yCwpaitHqHS5paY2+hPc/glGa+5VONvS7Ib4UMrPvP3GHi4i9hy+yMHDmVbyhtWhz9md/muw==", "signatures": [{"sig": "MEUCIQCXQY0N07zF+fj17v2+6PQtQ1eluRIx3WLOPkKDdu2DtAIgSnwnAuw+sGlmR2OliIro3t1csyRTBFfXpkAZZNHFaZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64287}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.0_1689861572549_0.6681948110256066", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/compat-data", "version": "8.0.0-alpha.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9dff69709e83dddf8fc79630e0e9fd9532214b8e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.1.tgz", "fileCount": 15, "integrity": "sha512-FPxuV1fqkkD+yfMYeXg4U9zQ07zPwyMNe2rQHMHCKYL/u7k2iltcK0hX92jK4l1TWwJcp0J9pN3pg8YKPGQ8Og==", "signatures": [{"sig": "MEUCIQD6SzEOMSyQCt34A84Hr3hA7teIUANVazxv5aAozWLFbgIgemWezRB0kGyGAtfxy4th/lt8a8fRH+NrxNaaQbFDA8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64287}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.1_1690221052188_0.310962416603741", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/compat-data", "version": "8.0.0-alpha.2", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4c94409d5ca3ded1bf145bc984274378bd9a413e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.2.tgz", "fileCount": 15, "integrity": "sha512-qARMxcl9yx9wDTOFCrY9lKJFD9hfxlfPy+/0l+FnPEVv8Gfb9HHQ2Di5ELGmhg+l3wfcOgFjENOTQA9RI/UCWw==", "signatures": [{"sig": "MEUCIDAHCPMNUkAFxbAjXzDtGKYqIdr8UOrXskT5lK7hjZr/AiEAtIr00HlOM7+66/eySNjYQbpHoouToYom9QBZfOJDZN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64287}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.2_1691594068502_0.47776679586216453", "host": "s3://npm-registry-packages"}}, "7.22.20": {"name": "@babel/compat-data", "version": "7.22.20", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.22.20", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8df6e96661209623f1975d66c35ffca66f3306d0", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.20.tgz", "fileCount": 15, "integrity": "sha512-BQYjKbpXjoXwFW5jGqiizJQQT/aC7pFm9Ok1OWssonuguICi264lbgMzRp2ZMmRSlfkX6DsWDDcsrctK8Rwfiw==", "signatures": [{"sig": "MEUCIE6IlHY6OcyeTSsOYy3bYVsq6+bkKDcj/LeyZAH1/zlpAiEAh+7QDyOlt8VNLrH1wG3PFJFrUe5MQBdD3CtE50esjjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64098}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.22.20_1694881716306_0.9862884602070419", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/compat-data", "version": "8.0.0-alpha.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3051baccba186d6f52849622989f5545af7f0581", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.3.tgz", "fileCount": 15, "integrity": "sha512-9/rt7wCPKoVPi65Aduj8omEacZJ785soDyuItpCaD5SRbBq3HWaPNMkqC1JoUH6ZjjUM7uQWCIsBZfCWyKFn3Q==", "signatures": [{"sig": "MEYCIQDVsAhy/egzAGXPnUOZQhUY41lttBMDojRzKOJonewaRgIhALSd4IGzqwJGLoUDZi2YdbQH5VrakgJXNwfZJokak6mx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64307}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.3_1695740183083_0.6348448755971288", "host": "s3://npm-registry-packages"}}, "7.23.2": {"name": "@babel/compat-data", "version": "7.23.2", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.23.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6a12ced93455827037bfb5ed8492820d60fc32cc", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.23.2.tgz", "fileCount": 15, "integrity": "sha512-0S9TQMmDHlqAZ2ITT95irXKfxN9bncq8ZCoJhun3nHL/lLUxd2NKBJYoNGWH7S0hz6fRQwWlAWn/ILM0C70KZQ==", "signatures": [{"sig": "MEQCIA61TITw9WvhE81hyvdqptlPWdZwAWDIfkxwo9uFm1ijAiA1vvBKN9eK5uScVQubbPGF13aoehtk7biG+EQwP0t3xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64097}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.23.2_1697050282151_0.0755118017249845", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/compat-data", "version": "8.0.0-alpha.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "91743237c0e8cdfa15e614a89ba5fd8718f64f12", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.4.tgz", "fileCount": 15, "integrity": "sha512-MAnIqFCEbhc3GARIicTQFU2odqIOk1THybUrqaKrLCjxCnfqBn0OdBMHqlhFLgYxtR8pTKNZ7RnrKDqyGEcF3Q==", "signatures": [{"sig": "MEUCIAl3Hwzyi0Zod2Nu6xb4xUg1gOEFoZPAPQuzj66Tz0cSAiEAqL06jrnG/F4aKhnktwrDmomzaMfQ4xnoM4Z4CNOLPTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64307}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.4_1697076350871_0.03810319767536763", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/compat-data", "version": "7.23.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3febd552541e62b5e883a25eb3effd7c7379db11", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.23.3.tgz", "fileCount": 15, "integrity": "sha512-BmR4bWbDIoFJmJ9z2cZ8Gmm2MXgEDgjdWgpKmKWUt54UGFJdlj31ECtbaDvCG/qVdG3AQ1SfpZEs01lUFbzLOQ==", "signatures": [{"sig": "MEQCICXUHF145V8D/GfokoLmx+9dP9zvwix6doxu7Sooig0lAiB11r1ynKtklHmyl0KxNoHxAA8BzdrMVujxykF/66+J/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64595}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.23.3_1699513416157_0.824851994562338", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/compat-data", "version": "7.23.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ffb878728bb6bdcb6f4510aa51b1be9afb8cfd98", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.23.5.tgz", "fileCount": 15, "integrity": "sha512-uU27kfDRlhfKl+w1U6vp16IuvSLtjAxdArVXPa9BvLkrr7CYIsxH5adpHObeAGY/41+syctUWOZ140a2Rvkgjw==", "signatures": [{"sig": "MEYCIQDVaavQFfyXXsEqxmcttfCELKSbmhmG8ETqvEA5LY+X3gIhAM7iVhAYmhcJRvcScqpgLN8CxB+Lfhhh6MwOoqiy97lb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64595}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.23.5_1701253537645_0.943519674351458", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/compat-data", "version": "8.0.0-alpha.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "900c6ab70fb2df79e0b4a5ed18dcdfe94afd334b", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.5.tgz", "fileCount": 15, "integrity": "sha512-VTcix7hgxKoQyNPdUWrDdMpSk6Q5tW95El42FYyLXsHgNEEUlUoBdOH4vwRQwFrse5OiO5QPdnV4H+fQJwlK3Q==", "signatures": [{"sig": "MEYCIQC6uVM9gfTswpU1Mo96DPX3CEavWVYZaDz8ngc+Ion35wIhAMpuiRT7ysQVZZNoZ8sFPju5EbVGF1aHLipJRjrETaUa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64805}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.5_1702307893648_0.07671283149235109", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/compat-data", "version": "8.0.0-alpha.6", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "619acf0bb8ca27a8ec94067b53b6c0351a88bd68", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.6.tgz", "fileCount": 15, "integrity": "sha512-Oam3YCPXzA5nII8+uqO1nur3PxJF3CcQpFJXHPAXJHhjIKvABCodYacDk8x/FQKBmBiUaMqGlEQ04m3H3rWqlA==", "signatures": [{"sig": "MEYCIQC7RkpLWygHEspTQXwGHsF2lEBxjOSQ/OATUf4GxsOHfgIhAMREGigK9XLwN8Pqhl5dmv33Mszs+tk12LAtVSPNKuaQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64805}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.6_1706285619551_0.7105376029588255", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/compat-data", "version": "8.0.0-alpha.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1e90d9eab47bffdaa0541bf55ddf83a4631bc82b", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.7.tgz", "fileCount": 15, "integrity": "sha512-s5h64709HZfglBuswnXcNv6C4Sawnx8gVTuG3FK1rr0ksGJLYNYNN0hnbT72v50NU5XlqeOEFmq1oP155a80Iw==", "signatures": [{"sig": "MEYCIQCcRYqezjNz21SyVoHDaueCokHKcnVaoyXqp0liIu1ZCAIhALy9akJxzoN6aV2B57j0xQyd19Do0W0h81WzgJrLZvSY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64805}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "./plugins": "./plugins.js", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js", "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.7_1709129043178_0.6118399237663621", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/compat-data", "version": "7.24.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "31c1f66435f2a9c329bb5716a6d6186c516c3742", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.1.tgz", "fileCount": 15, "integrity": "sha512-Pc65opHDliVpRHuKfzI+gSA4zcgr65O4cl64fFJIWEEh8JoHIHh0Oez1Eo8Arz8zq/JhgKodQaxEwUPRtZylVA==", "signatures": [{"sig": "MEUCIQCuWiEo3dW7MQLnfz4PKtjMnKYuHxV0vX1J17qWAupV0QIgd0VV2Soj/yWHC43AkJa4ddaw9tTLUWYL+4jHc8jBbhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64595}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.24.1_1710841650863_0.3154871359696021", "host": "s3://npm-registry-packages"}}, "7.24.4": {"name": "@babel/compat-data", "version": "7.24.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6f102372e9094f25d908ca0d34fc74c74606059a", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.4.tgz", "fileCount": 15, "integrity": "sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==", "signatures": [{"sig": "MEQCIBoDFSN7ujcFA6P967JL+D8xlWZI34okRy5C+cFV+05VAiBjRjH6KcOJdV2y3JWAm6fyFleddRWqLKZUR6slyfRKAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65249}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.24.4_1712163224941_0.007090205861338683", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/compat-data", "version": "8.0.0-alpha.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "444caed21df8dd4b95772c353294a41df72d29d1", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.8.tgz", "fileCount": 15, "integrity": "sha512-VKL35bqASDwNaiRmg43d9SjrXDbCbyFCeY9TqBggn26NmnDjaTAwSYXOxEZEUJJKPgX+Y6MTeXPNmk+vz8R1Xg==", "signatures": [{"sig": "MEYCIQCNvGn2Oo6yKArI/zfK7Kjsa0HhqzW71k0WZ6TQqpf5KAIhAO2fnkEtfQ9jzQyiguzq1cxdbPyfOdwtZqAewKM2D/bw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65159}, "type": "commonjs", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.8_1712236759854_0.4303519722383784", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/compat-data", "version": "7.24.6", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b3600217688cabb26e25f8e467019e66d71b7ae2", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.6.tgz", "fileCount": 15, "integrity": "sha512-aC2DGhBq5eEdyXWqrDInSqQjO0k8xtPRf5YylULqx8MCd6jBtzqfta/3ETMRpuKIc5hyswfO80ObyA1MvkCcUQ==", "signatures": [{"sig": "MEQCIGqLeV4n+E5mJ3UfB0jX6F8TpHf28hxZzfukoLMXaGZ1AiB9VwhwUqzA9cgp5mUZ2ZvXV6p4JOVz+VuWFSjDzTeXoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65249}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.24.6_1716553447884_0.1728212545319554", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/compat-data", "version": "8.0.0-alpha.9", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "733a9ace224566b2a34be38bc633b76733ff9a45", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.9.tgz", "fileCount": 15, "integrity": "sha512-gwwNGR9+iYrOdoOS9SSRwiEUqTNykDbNpv+SDWrtk3RhI8eQAdsCXkatw1rTohxgbTUbh+7b8kh7YZlwdLJRPQ==", "signatures": [{"sig": "MEQCIE3LOKWOwRmXpk5ix3QQP1WnrJGVRr+p/4lWm6HdKwe9AiAwm5y9Od/WkQ1f6KoAO40m6FMEETn81KFvukeKBUx7RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65475}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.9_1717423424155_0.9767821753691956", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/compat-data", "version": "8.0.0-alpha.10", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9ea71d7f00ec6973e7aae04d4de7b50f850a9916", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.10.tgz", "fileCount": 15, "integrity": "sha512-WHs3egsDB64cIv3xB0dYo6+bvGj8U3pANLPO4L70lBe+zZ1GQmOfzCQisgPCfkd7UWy015+lNpyfZduDI8ZwHw==", "signatures": [{"sig": "MEQCIGy50kRJHukA+CxAlW66/6TNij/JDwK6jyuaCs/fXRu3AiB89dWc7ag/+Wdf10f0VyLPSt6tI1CQft/B2CXyb7vvNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65476}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.10_1717499977677_0.5149755490791941", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/compat-data", "version": "7.24.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d23bbea508c3883ba8251fb4164982c36ea577ed", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.7.tgz", "fileCount": 15, "integrity": "sha512-qJzAIcv03PyaWqxRgO4mSU3lihncDT296vnyuE2O8uA4w3UHWI4S3hgeZd1L8W1Bft40w9JxJ2b412iDUFFRhw==", "signatures": [{"sig": "MEUCIQCLu4uGk3aee0DWJr02dYFRqsiSfNV3fidMdGnBlb/ogQIgcnzu4n07m+3T0L4oJUI/u6qk8mTQM2gwOcyshbkcqX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65565}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.24.7_1717593293759_0.262965146438072", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/compat-data", "version": "8.0.0-alpha.11", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fddb69fa8ab644c954faa1eee949aef849267333", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.11.tgz", "fileCount": 15, "integrity": "sha512-KqZXX7rUMOwtTlC2VDnC5soIYHgNErKkQnYyqJX0ZmJEc5uNOd+O5aThX4iN4xH3Isjb3k57FBOq80bIK+u9RA==", "signatures": [{"sig": "MEYCIQCEMIfiVPaA3dHIMrn7yTnBkDkZMSKqt5tlVVpvmBpJVQIhAPV/5OdcUVzABFtIcvoRstx8TQWX6nYhUrxrdBVQ3+NP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65476}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441", "@mdn/browser-compat-data": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.11_1717751709084_0.5808120635363652", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/compat-data", "version": "7.24.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f9196455334c38d059ac8b1a16a51decda9d30d3", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.8.tgz", "fileCount": 15, "integrity": "sha512-c4IM7OTg6k1Q+AJ153e2mc2QVTezTwnb4VzquwcyiEzGnW0Kedv4do/TrkU98qPeC5LNiMt/QXwIjzYXLBpyZg==", "signatures": [{"sig": "MEQCICEujergeiOh8BLIuPTRUZzwifya6LYVsefLkw088tIbAiAHDHksMoNUkTyEgUfHx3TcAMWsdKVOLvO0B4KR5Ysjdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65566}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.24.8_1720709681014_0.27304620283999426", "host": "s3://npm-registry-packages"}}, "7.24.9": {"name": "@babel/compat-data", "version": "7.24.9", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.24.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "53eee4e68f1c1d0282aa0eb05ddb02d033fc43a0", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.9.tgz", "fileCount": 15, "integrity": "sha512-e701mcfApCJqMMueQI0Fb68Amflj83+dvAvHawoBpAz+GDjCIyGHzNwnefjsWJ3xiYAqqiQFoWbspGYBdb2/ng==", "signatures": [{"sig": "MEQCIA6raBv/lLmdg9q0FqvrS6kqH8rkmQd9qyRL0AZhh9JOAiAVY/BGtegpDWLQMzaWGGqWCM/Oiz4WBo9pA8xhY1cnmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65566}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.24.9_1721039669018_0.7464845425462265", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/compat-data", "version": "7.25.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6b226a5da3a686db3c30519750e071dce292ad95", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.25.0.tgz", "fileCount": 15, "integrity": "sha512-P4fwKI2mjEb3ZU5cnMJzvRsRKGBUcs8jvxIoRmr6ufAY9Xk2Bz7JubRTTivkw55c7WQJfTECeqYVa+HZ0FzREg==", "signatures": [{"sig": "MEYCIQDApmg1kB0pzd/vq8Z2Sil/QLUTtwcy+SRPr/8N1Ljo4QIhANHKcm4YXaVQLOk7ezrvEFa0VRDVP58CjK3NNZH5dPJe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66133}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.25.0_1722013158123_0.9159076504539749", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/compat-data", "version": "8.0.0-alpha.12", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "765c4447c3b5a146486b95d270a6646d7bdf6794", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.12.tgz", "fileCount": 15, "integrity": "sha512-0sYmoqApB+hhCm4GMW0VkQRTqUJs7sMJEiJVEwEuY2AaZ6KTepZegb3Q5ELadIC8k9UdYzagLe8WBQ3CxTb0Dg==", "signatures": [{"sig": "MEQCIAyAt5b5RjjD/bprGRvUTqdDmiiF+Go3HukTZIOFqJQZAiA+s5MRxBaRsDGSp9MeP/sgW30s8YCaBksqcb5bXbBePQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66044}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.12_1722015183853_0.251834608038608", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/compat-data", "version": "7.25.2", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e41928bd33475305c586f6acbbb7e3ade7a6f7f5", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.25.2.tgz", "fileCount": 15, "integrity": "sha512-bYcppcpKBvX4znYaPEeFau03bp89ShqNMLs+rmdptMw+heSZh9+z84d2YG+K7cYLbWwzdjtDoW/uqZmPjulClQ==", "signatures": [{"sig": "MEUCIG5pj5t4gXNizSqLDuzvV/W532EWcuaojKaIv/mdj64XAiEA8gzBoVWUpFSrneZb2F6LEFVruK4KuS3jF5KhdFVxf0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66133}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.25.2_1722308087926_0.1322856430613868", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/compat-data", "version": "7.25.4", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7d2a80ce229890edcf4cc259d4d696cb4dae2fcb", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.25.4.tgz", "fileCount": 17, "integrity": "sha512-+LGRog6RAsCJrrrg/IO6LGmpphNe5DiK30dGjCoxxeGv49B10/3XYGxPsAwrDlMFcFEvdAUavDT8r9k/hSyQqQ==", "signatures": [{"sig": "MEYCIQDmwqDN2+7EA4funMxTaYuD21r1HEtDZNYCUCEF+u5dHQIhAMFHYBvECEaC/JkDNHKjKC3j6OIBSf3KkCDaFUX/Dobl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100886}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.25.4_1724319266327_0.7540245601207702", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/compat-data", "version": "7.25.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b8479fe0018ef0ac87b6b7a5c6916fcd67ae2c9c", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.25.7.tgz", "fileCount": 17, "integrity": "sha512-9ickoLz+hcXCeh7jrcin+/SLWm+GkxE2kTvoYyp38p4WkdFXfQJxDFGWp/YHjiKLPx06z2A7W8XKuqbReXDzsw==", "signatures": [{"sig": "MEUCIC2Zbn3EvpJjrOwJHmlrXVZFGDgCBVb/8RVKBoRmRz4nAiEAlrjJYmra0MVUibmXZK9/PReD78NiVMSN/4lSqBXc6TA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108489}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.25.7_1727882062009_0.4530253340806294", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/compat-data", "version": "7.25.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0376e83df5ab0eb0da18885c0140041f0747a402", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.25.8.tgz", "fileCount": 17, "integrity": "sha512-ZsysZyXY4Tlx+Q53XdnOFmqwfB9QDTHYxaZYajWRoBLuLEAwI2UIbtxOjWh/cFaa9IKUlcB+DDuoskLuKu56JA==", "signatures": [{"sig": "MEUCIBGMpKX6oeMu7OEnvjztKObFx/GRknV8jel+G2anZWDYAiEA9A8k66fFz5J+RLcWt9QNkBTob26YTbv0gnnM7Cdq4So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108989}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.25.8_1728566702886_0.4104315358141206", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/compat-data", "version": "7.25.9", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "24b01c5db6a3ebf85661b4fb4a946a9bccc72ac8", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.25.9.tgz", "fileCount": 15, "integrity": "sha512-yD+hEuJ/+wAJ4Ox2/rpNv5HIuPG82x3ZlQvYVn8iYCprdxzE7P1udpGF1jyjQVBU4dgznN+k2h103vxZ7NdPyw==", "signatures": [{"sig": "MEUCIBBL1MFOYF7+XzrwbyndOwXi0C3n9Lz6BDkgTYPfmH+8AiEA6DFr2H7gl3X4PATpbE9ffUHLZRXM+CG7F6dSnk+sh/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66190}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.25.9_1729610434879_0.008966582247259547", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/compat-data", "version": "7.26.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f02ba6d34e88fadd5e8861e8b38902f43cc1c819", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.0.tgz", "fileCount": 15, "integrity": "sha512-qETICbZSLe7uXv9VE8T/RWOdIE5qqyTucOt4zLYMafj2MRO271VGgLd4RACJMeBO37UPWhXiKMBk7YlJ0fOzQA==", "signatures": [{"sig": "MEUCICMlLjYYmqxaT+nqWAlR3/jiphyHnXtlG/AA+6EiIkBbAiEAkJmyAFnx78ZL/TWzmAKTHgnX7dyxhTgLFjTHaonh2rA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66352}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.26.0_1729863006228_0.6288350194765939", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/compat-data", "version": "8.0.0-alpha.13", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3ba0b5d8b5c207926318e09e787dfe1a0d4a3219", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.13.tgz", "fileCount": 15, "integrity": "sha512-a2T4tvQbbyHoeF9NUtc/lA3GcPBfP4WsoNpKjXQopGuXpraKGTM+MFwprLOIj/Nq7acTngy6aqHi+Snh30CP8A==", "signatures": [{"sig": "MEYCIQCTCfvDYz2xOUdb8B/ZMyN41oFc9zyI7r8Ls76aa3XuLgIhAPnqVrekN20CGk/NBnu3s12bUk7BkgTQ+j0miYo6lVxo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66263}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.13_1729864421015_0.9618702471227718", "host": "s3://npm-registry-packages"}}, "7.26.2": {"name": "@babel/compat-data", "version": "7.26.2", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.26.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "278b6b13664557de95b8f35b90d96785850bb56e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.2.tgz", "fileCount": 15, "integrity": "sha512-Z0WgzSEa+aUcdiJuCIqgujCshpMWgUpgOxXotrYPSA53hA3qopNaqcJpyr0hVb1FeWdnqFA35/fUtXgBK8srQg==", "signatures": [{"sig": "MEUCIQDI03KJYpOy0esm14fjamiqWhjngQMI8+B/C+sa5MW7gAIgRbO8EUzFhUeUHP9unbWXMdVUGNKH3s4R7mzhuIjSZw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66352}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.26.2_1730310371520_0.9214199210758467", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/compat-data", "version": "7.26.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "99488264a56b2aded63983abd6a417f03b92ed02", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.3.tgz", "fileCount": 15, "integrity": "sha512-nHIxvKPniQXpmQLb0vhY3VaFb3S0YrTAwpOWJZh1wn3oJPjJk9Asva204PsBdmAE8vpzfHudT8DB0scYvy9q0g==", "signatures": [{"sig": "MEQCIAJJ6UjIwfViq8GiXInUlUOVpXm+sksLe/Ts5DPN598kAiAmUBdMdSvistgX/7vmJ84aV+ea6sjKnXQwIEjMnWcstA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66454}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.26.3_1733315734159_0.32404641666584455", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/compat-data", "version": "8.0.0-alpha.14", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "22d1e86a06415ae8416b456d324462db4492da97", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.14.tgz", "fileCount": 15, "integrity": "sha512-<PERSON>nn4Bgett5wJvZGGuod2gQkgYqw/jR6T/sBbK3OlU0ZU9+iIrNJyR6D3A+gtsQCn9ZwozeosYhKqrxD2ZCqFQ==", "signatures": [{"sig": "MEUCIQD4olyjB1WFtfYMkawOKTlFo7Cw9ga5IRpzwxz6w/R20wIgLwzlzCRl6FvDm8pigbyGM7QA5uNTcinJK5ncw4AMgMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66365}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.14_1733504013003_0.9820613628138657", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/compat-data", "version": "7.26.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "df93ac37f4417854130e21d72c66ff3d4b897fc7", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.5.tgz", "fileCount": 15, "integrity": "sha512-XvcZi1KWf88RVbF9wn8MN6tYFloU5qX8KjuF3E1PVBmJ9eypXfs4GRiJwLuTZL0iSnJUKn1BFPa5BPZZJyFzPg==", "signatures": [{"sig": "MEYCIQDcvI5S3vdZPJtGT2PBNGkxO9r56zUiAtuw8Q2eSTAt8AIhALCfemGKe9RNS/4DdJ9OG0iwj1h1T4vRJL78pD2Fo56Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66428}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.26.5_1736529105828_0.45147994638196587", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/compat-data", "version": "8.0.0-alpha.15", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8d46b8b4a61ce43bd6f6892510630c161ed5d278", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.15.tgz", "fileCount": 15, "integrity": "sha512-u9PnzMgpezw5Dtqg4MgGPVC9epLvRFlRTiwNYgZ3soEcZzvZUdLWEL/YhlZ0DznAfA8yKX5DWkqy9OR8Ub6Z3A==", "signatures": [{"sig": "MEQCIGPyzC+tMCGSGwEH/l5verjMo6koxlLNIgaB+do5l+5FAiB818thVTvGK3Cb2bnEwWPNVLfE0LeXnXvt0rOTXju33Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66339}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.37.1", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.15_1736529836397_0.4293854491661915", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.8": {"name": "@babel/compat-data", "version": "7.26.8", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.26.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "821c1d35641c355284d4a870b8a4a7b0c141e367", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz", "fileCount": 15, "integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==", "signatures": [{"sig": "MEQCIEWXuk09et1bOlwWsIa5VcBW8ICdkes3e5BbSmDZRl7AAiABl5JS2Hf6wCUGW+dEAaoR4+KG4pd1hUjbQkZXfNSfcQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66428}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.40.0", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.26.8_1739008761352_0.1994740424352368", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/compat-data", "version": "8.0.0-alpha.16", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "62bb04301d1a5140a8eafd4aec9e67cfa90d946e", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.16.tgz", "fileCount": 15, "integrity": "sha512-fKP1KLbR3zCKsjJxqsrWiRcEUo8mTjqVTWxC0mfs19YVBJ03TJJL6K31pExzbIAEykxOBGH4qu3KEij1PJBYiA==", "signatures": [{"sig": "MEYCIQCggHX6lJQfosa2/Hawv15SVtEDL7Ovye5mVsaTGI6S4QIhAMariWDCz5QuYuezIPlYwLtdzcvWuP6rLpPSS4HMmZA1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66339}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.40.0", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.16_1739534314635_0.4643395048365977", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/compat-data", "version": "8.0.0-alpha.17", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c4c9c77df23fdcac5c4bbdb3607fa0d027b6d4f5", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-alpha.17.tgz", "fileCount": 15, "integrity": "sha512-EwUwOcZ9e2EKvAKU0Sc+g3OCOG3sVb6BYbZfNafBYGt3vVQCpzt79hy/nSvxVIuXF5BQ3NqWbQ/P362/eXYUDQ==", "signatures": [{"sig": "MEUCIQCsD+FKDO+FbtGDQouRB/s2svytt88t1jAX/anNb4pBtwIgbeuaAKNS2qfNZh3vcWFCFKCjzYlFVqCeLXLCqj2kfLo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66339}, "type": "commonjs", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./overlapping-plugins": "./overlapping-plugins.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.40.0", "electron-to-chromium": "^1.4.816", "@mdn/browser-compat-data": "^5.5.36"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-alpha.17_1741717464963_0.5947493486510893", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/compat-data", "version": "7.27.1", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "db7cf122745e0a332c44e847ddc4f5e5221a43f6", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.1.tgz", "fileCount": 15, "integrity": "sha512-Q+E+rd/yBzNQhXkG+zQnF58e4zoZfBedaxwzPmicKsiK3nt8iJYrSrDbjwFFDGC4f+rPafqRaPH6TsDoSvMf7A==", "signatures": [{"sig": "MEYCIQDe5Cqpz7xCybuoLtxrDRWyoqYKyE3LxWdbu5H7ozy06gIhAJeNExfdicd1+2usRkzbXzoVWzPQYb84eR2PGiWdmQ+v", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66193}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.27.1_1746025705987_0.3204567637785163", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.2": {"name": "@babel/compat-data", "version": "7.27.2", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.27.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4183f9e642fd84e74e3eea7ffa93a412e3b102c9", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.2.tgz", "fileCount": 15, "integrity": "sha512-TUtMJYRPyUb/9aU8f3K0mjmjf6M9N5Woshn2CS6nqJSeJtTtQcpLUXjGt9vbF8ZGff0El99sWkLgzwW3VXnxZQ==", "signatures": [{"sig": "MEQCIH8Wqs5oCUDsBs5YfQoHQKyH6VyB4Ugr2atx1EJxo+/8AiA3xqVI3OK6P54vo/B601stTB+fDlKZCjEdU0kADvchvg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66513}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.27.2_1746545625641_0.4244386095130499", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/compat-data", "version": "7.27.3", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "cc49c2ac222d69b889bf34c795f537c0c6311111", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.3.tgz", "fileCount": 15, "integrity": "sha512-V42wFfx1ymFte+ecf6iXghnnP8kWTO+ZLXIyZq+1LAXHHvTZdVxicn4yiVYdYMGaCO3tmqub11AorKkv+iodqw==", "signatures": [{"sig": "MEUCIQD/3tEqxDlNAJXy5q1w8OIscWmt0+5NRl6i1mkIja8h3gIgMeXO7ZNotFB+0+4K2U8bPkPrCNp3elpZdFM43MZH4rE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66995}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.27.3_1748335154400_0.06822369726480049", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/compat-data", "version": "8.0.0-beta.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b80ef5d4b72e1c3b7baf0ae7356797f66f3e259f", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-beta.0.tgz", "fileCount": 15, "integrity": "sha512-fTYtFXFvXkiUC45ndeKpZRmxGpXi5jSSDCdpXXqjMgMKybYlArJsS3AmL67nuHd91jfSVZj7GQHK/fXcdhYCvg==", "signatures": [{"sig": "MEUCIE8/b7J3Bs81/HD/kjdEkhqYDhH6W7Dd/Rt3CpOi76WwAiEAsaX49aFSox2SFDl2/aesmVm+kzovve5BtRiBwqX9bdE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66921}, "type": "commonjs", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json", "./overlapping-plugins": "./data/overlapping-plugins.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_8.0.0-beta.0_1748620237553_0.5917086217865664", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.5": {"name": "@babel/compat-data", "version": "7.27.5", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.27.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7d0658ec1a8420fc866d1df1b03bea0e79934c82", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "fileCount": 15, "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==", "signatures": [{"sig": "MEQCIC+B1BMmlynQCzf+Z/oixss4mdEwxf17Y8r2+cyyZVDKAiAYsTwnN179QigFNx7krEnsgSpURi5Py5Kgezioexj3pg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66995}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.27.5_1748950023796_0.8464906630454978", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.7": {"name": "@babel/compat-data", "version": "7.27.7", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.27.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7fd698e531050cce432b073ab64857b99e0f3804", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.7.tgz", "fileCount": 15, "integrity": "sha512-xgu/ySj2mTiUFmdE9yCMfBxLp4DHd5DwmbbD05YAuICfodYT3VvRxbrh81LGQ/8UpSdtMdfKMn3KouYDX59DGQ==", "signatures": [{"sig": "MEQCIDo+/7cmdSQ7DT6lYGbpCAbgh6esD3YcICLRUT0aEUXdAiBUDd2bcDF7KztY8gfO1qLFqS0v7GN1gx7Ccz1UDX6xUg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66995}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.27.7_1750946597518_0.820580471407232", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/compat-data", "version": "7.28.0", "keywords": ["babel", "compat-table", "compat-data"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/compat-data@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9fc6fd58c2a6a15243cd13983224968392070790", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz", "fileCount": 15, "integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==", "signatures": [{"sig": "MEUCIQDc6AnfBfIlc6tjssXWZd4X54AsVdKo9umI3UMsxdRgSQIgcY5Lv2gcOSRxqSI9NDG1i8fMhgPtKK0OywCVHaLVfaw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67127}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./plugin-bugfixes": "./plugin-bugfixes.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./overlapping-plugins": "./overlapping-plugins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js-compat": "^3.43.0", "electron-to-chromium": "^1.5.140", "@mdn/browser-compat-data": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/compat-data_7.28.0_1751445492957_0.4824761002524558", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/compat-data", "version": "8.0.0-beta.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "The compat-data to determine required Babel plugins", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "publishConfig": {"access": "public"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "keywords": ["babel", "compat-table", "compat-data"], "devDependencies": {"@mdn/browser-compat-data": "^6.0.8", "core-js-compat": "^3.43.0", "electron-to-chromium": "^1.5.140"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "type": "commonjs", "_id": "@babel/compat-data@8.0.0-beta.1", "dist": {"shasum": "edab3941770e8df184d3f152681630198f1c7d91", "integrity": "sha512-W96pjNs5+ltXb7ynO1bOP1c6F8Od/TzMkgbMafeJunbBeTi1IyjhFHhnuzc2BQH+QcFzdetwH7RbluuylyM4Rg==", "tarball": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-8.0.0-beta.1.tgz", "fileCount": 15, "unpackedSize": 67053, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICyTiAGrmtJBD5yMgBY3oj99QeHWbfnohDK1iAnC3XW+AiEA0gixtC+91mz65IqP3BTdOKhNzd9pZAfm9ALWsYFxtto="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/compat-data_8.0.0-beta.1_1751447032690_0.37373550812867107"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-01-12T00:15:55.066Z", "modified": "2025-07-02T09:03:53.132Z", "7.8.0": "2020-01-12T00:15:55.817Z", "7.8.1": "2020-01-12T13:02:43.824Z", "7.8.4": "2020-01-30T12:37:26.657Z", "7.8.5": "2020-01-31T18:51:38.838Z", "7.8.6": "2020-02-27T12:21:21.461Z", "7.9.0": "2020-03-20T15:39:08.599Z", "7.9.6": "2020-04-29T18:37:57.554Z", "7.10.0": "2020-05-26T21:43:13.941Z", "7.10.1": "2020-05-27T22:06:51.409Z", "7.10.3": "2020-06-19T20:54:07.902Z", "7.10.4": "2020-06-30T13:11:18.926Z", "7.10.5": "2020-07-14T18:17:41.747Z", "7.11.0": "2020-07-30T20:56:43.498Z", "7.12.0": "2020-10-14T20:02:34.895Z", "7.12.1": "2020-10-15T22:39:14.015Z", "7.12.5": "2020-11-03T22:34:12.325Z", "7.12.7": "2020-11-20T21:05:35.277Z", "7.12.13": "2021-02-03T01:09:47.677Z", "7.13.0": "2021-02-22T22:49:26.049Z", "7.13.5": "2021-02-23T14:02:22.572Z", "7.13.6": "2021-02-23T17:44:53.110Z", "7.13.8": "2021-02-26T23:38:45.587Z", "7.13.11": "2021-03-15T09:44:26.147Z", "7.13.12": "2021-03-22T15:46:59.361Z", "7.13.15": "2021-04-08T15:50:19.907Z", "7.14.0": "2021-04-29T20:09:53.317Z", "7.14.4": "2021-05-28T16:59:46.232Z", "7.14.5": "2021-06-09T23:11:17.199Z", "7.14.7": "2021-06-21T21:53:57.187Z", "7.14.9": "2021-08-01T07:53:11.043Z", "7.15.0": "2021-08-04T21:12:55.086Z", "7.16.0": "2021-10-29T23:47:21.096Z", "7.16.4": "2021-11-16T22:46:15.902Z", "7.16.8": "2022-01-10T21:18:22.236Z", "7.17.0": "2022-02-02T23:04:40.535Z", "7.17.7": "2022-03-14T17:07:07.706Z", "7.17.10": "2022-04-29T16:37:37.144Z", "7.18.5": "2022-06-13T06:40:21.635Z", "7.18.6": "2022-06-27T19:49:44.169Z", "7.18.8": "2022-07-08T09:32:34.363Z", "7.18.13": "2022-08-22T16:05:10.758Z", "7.19.0": "2022-09-05T19:02:12.949Z", "7.19.1": "2022-09-14T15:29:09.588Z", "7.19.3": "2022-09-27T18:36:44.294Z", "7.19.4": "2022-10-10T10:47:19.360Z", "7.20.0": "2022-10-27T13:19:04.942Z", "7.20.1": "2022-11-01T11:25:40.512Z", "7.20.5": "2022-11-28T10:12:42.752Z", "7.20.10": "2022-12-23T09:19:10.572Z", "7.20.14": "2023-01-27T20:57:28.961Z", "7.21.0": "2023-02-20T15:30:52.895Z", "7.21.4": "2023-03-31T09:01:50.841Z", "7.21.4-esm": "2023-04-04T14:08:59.250Z", "7.21.4-esm.1": "2023-04-04T14:20:51.557Z", "7.21.4-esm.2": "2023-04-04T14:38:30.843Z", "7.21.4-esm.3": "2023-04-04T14:55:45.595Z", "7.21.4-esm.4": "2023-04-04T15:13:00.321Z", "7.21.5": "2023-04-28T19:50:14.424Z", "7.21.7": "2023-04-29T14:18:49.396Z", "7.21.9": "2023-05-22T10:02:19.451Z", "7.22.0": "2023-05-26T13:45:11.682Z", "7.22.3": "2023-05-27T10:10:50.443Z", "7.22.5": "2023-06-08T18:21:00.971Z", "7.22.6": "2023-07-04T07:48:48.076Z", "7.22.9": "2023-07-12T16:53:31.414Z", "8.0.0-alpha.0": "2023-07-20T13:59:32.713Z", "8.0.0-alpha.1": "2023-07-24T17:50:52.339Z", "8.0.0-alpha.2": "2023-08-09T15:14:28.721Z", "7.22.20": "2023-09-16T16:28:36.476Z", "8.0.0-alpha.3": "2023-09-26T14:56:23.270Z", "7.23.2": "2023-10-11T18:51:22.329Z", "8.0.0-alpha.4": "2023-10-12T02:05:51.129Z", "7.23.3": "2023-11-09T07:03:36.346Z", "7.23.5": "2023-11-29T10:25:37.848Z", "8.0.0-alpha.5": "2023-12-11T15:18:13.918Z", "8.0.0-alpha.6": "2024-01-26T16:13:39.725Z", "8.0.0-alpha.7": "2024-02-28T14:04:03.315Z", "7.24.1": "2024-03-19T09:47:31.064Z", "7.24.4": "2024-04-03T16:53:45.085Z", "8.0.0-alpha.8": "2024-04-04T13:19:20.031Z", "7.24.6": "2024-05-24T12:24:08.064Z", "8.0.0-alpha.9": "2024-06-03T14:03:44.299Z", "8.0.0-alpha.10": "2024-06-04T11:19:37.840Z", "7.24.7": "2024-06-05T13:14:54.031Z", "8.0.0-alpha.11": "2024-06-07T09:15:09.250Z", "7.24.8": "2024-07-11T14:54:41.198Z", "7.24.9": "2024-07-15T10:34:29.179Z", "7.25.0": "2024-07-26T16:59:18.403Z", "8.0.0-alpha.12": "2024-07-26T17:33:04.013Z", "7.25.2": "2024-07-30T02:54:48.101Z", "7.25.4": "2024-08-22T09:34:26.468Z", "7.25.7": "2024-10-02T15:14:22.283Z", "7.25.8": "2024-10-10T13:25:03.030Z", "7.25.9": "2024-10-22T15:20:35.132Z", "7.26.0": "2024-10-25T13:30:06.405Z", "8.0.0-alpha.13": "2024-10-25T13:53:41.220Z", "7.26.2": "2024-10-30T17:46:11.797Z", "7.26.3": "2024-12-04T12:35:34.318Z", "8.0.0-alpha.14": "2024-12-06T16:53:33.392Z", "7.26.5": "2025-01-10T17:11:46.001Z", "8.0.0-alpha.15": "2025-01-10T17:23:56.613Z", "7.26.8": "2025-02-08T09:59:21.539Z", "8.0.0-alpha.16": "2025-02-14T11:58:34.841Z", "8.0.0-alpha.17": "2025-03-11T18:24:25.168Z", "7.27.1": "2025-04-30T15:08:26.181Z", "7.27.2": "2025-05-06T15:33:45.834Z", "7.27.3": "2025-05-27T08:39:14.553Z", "8.0.0-beta.0": "2025-05-30T15:50:37.755Z", "7.27.5": "2025-06-03T11:27:03.984Z", "7.27.7": "2025-06-26T14:03:17.685Z", "7.28.0": "2025-07-02T08:38:13.154Z", "8.0.0-beta.1": "2025-07-02T09:03:52.878Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel", "compat-table", "compat-data"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-compat-data"}, "description": "The compat-data to determine required Babel plugins", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}