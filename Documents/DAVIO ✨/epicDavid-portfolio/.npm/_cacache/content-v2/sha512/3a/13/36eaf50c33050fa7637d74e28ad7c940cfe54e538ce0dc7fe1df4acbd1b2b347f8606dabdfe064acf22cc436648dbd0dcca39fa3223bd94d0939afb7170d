{"_id": "@babel/plugin-transform-numeric-separator", "_rev": "33-7bcc43d0f48a6d1130076e28e0c9e57a", "name": "@babel/plugin-transform-numeric-separator", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "94de4190617b02b73c6bd48249eacd8726af3a3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-dfbXAKlbPlDKXsY7fa/gRBWgI4n537TR4b5AnVCZ3RwQ1aVPxs52Xs3XHFxQMn3j4LmUhn8IL2nAYmNh6z2/Ew==", "signatures": [{"sig": "MEQCIH3Gs8T3N2BqRIO+EEMWKIdYsodxtxAioIYGm0Mwkw+jAiAk4UP1TkEY5Hb7sAIFbT9BvthiGWTY+6K5poWj9M6ihw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5399}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/types": "^7.22.0", "@babel/traverse": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.22.0_1685108716418_0.9300831615812577", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "02493070ca6685884b0eee705363ee4da2132ab0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-+AF88fPDJrnseMh5vD9+SH6wq4ZMvpiTMHh58uLs+giMEyASFVhcT3NkoyO+NebFCNnpHJEq5AXO2txV4AGPDQ==", "signatures": [{"sig": "MEQCIExGVNKrHrS482bM+SijDqLnV2Ljl70H43tfXj9K+g0sAiBG01Tl89L2I5VkEdsQb2V3ia6Jm9LUjEMs63nsdSh8wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5379}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/types": "^7.22.3", "@babel/traverse": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.22.3_1685182258680_0.0027760905294851312", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "57226a2ed9e512b9b446517ab6fa2d17abb83f58", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-NbslED1/6M+sXiwwtcAB/nieypGw02Ejf4KtDeMkCEpP6gWFMX1wI9WKYua+4oBneCCEmulOkRpwywypVZzs/g==", "signatures": [{"sig": "MEQCIHKa0IaC6Y4iB0ektqN7RYU4ejcM/zEyWMxa+Uv8P1A1AiBmyhuVLVf0QBWKgSYY3tyEtdQiMnfF+5DmU3ZLBt/T1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5379}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/types": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.22.5_1686248478484_0.9934993895230229", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "f1cd7c2baba16bc2057b964b7523c210e8f6e195", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-bNJqKQHCBaYF89w3HUreICPqtfLx3Cezvz8GgJ0/Hd7G5eYMS/M5P+vnpNUMmsKVMh6huGKvoHDDpryDIPm+Gg==", "signatures": [{"sig": "MEQCIAnmGTnTddL98Df6+PRxh4oy/UojqamTXifPL4ob3pY8AiAJkvcnOy8jpDU9FZ8pt+mKQ3kshQ4d0aXgkxrPQLlP/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5164}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/types": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.0_1689861592715_0.08678311702986385", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "b57cae09fc4aa73c7258bc867de47173b6cd03c6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-MTbpATfqbWHXOnoVPppLq9sZdvH9cuZS93ococ/JfzJMxAE+OfY1XD/B6t394QhE1uuzWoX4wbB4Ol5R2D4R3Q==", "signatures": [{"sig": "MEUCIQCNTkUB2G24A+jOTRHsJzm6uZ4BrqTLnAO0TZxYuCDaigIgVZ/Reji9qXSQXtfu0Y80BImlvqQPUf0KCIoUzm75ZIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5164}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/types": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.1_1690221112588_0.5979267328674787", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "7903965cd6ed2d926667ba70670362adc107c7ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-3OStAZLA6TgM3u1vBZYBrrzpEjsamjAH6wRmYUW0o3dGsENzyKfYPddMmHj4x7lkWKqQBJPL6YMu6HXH5clWIQ==", "signatures": [{"sig": "MEUCIEIzCMAHzeLsygHVrGh2Gg5dSnYDfzLbdbfQDZzLNzvYAiEAo8dCn8Y0yRrq/Gph/nyA1h+7YPnsgr8pxOMvjp6XOQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5023}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/types": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.2_1691594092953_0.659633315578015", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "498d77dc45a6c6db74bb829c02a01c1d719cbfbd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-3dzU4QGPsILdJbASKhF/V2TVP+gJya1PsueQCxIPCEcerqF21oEcrob4mzjsp2Py/1nLfF5m+xYNMDpmA8vffg==", "signatures": [{"sig": "MEQCIBmtxBrC5v7Nx6iAXcdaGU21Dqj9BYHqOHDhAwUzg6XOAiA4rj/Nk/dUJ/WwMw4ZA/kKplB7jxT8eYkqdyGglGHIeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5328}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/types": "^7.22.11", "@babel/traverse": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.22.11_1692882519292_0.4867060130791141", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "f161d259eaba1ac0686fbbed25e74dcc8896696d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-1WrVD4w4yx3MWDZ96eD10j+0div6xl/GGu78XEAZ60T+8XfVRbQb+3vTSCLL4hqU3oqzWkZzumxx6fngsXdMmg==", "signatures": [{"sig": "MEQCIH/ml90PmyWn4eQffCWFw8k2nr3dIuI6ceKSe8YwACmDAiAxp4nnNygKsbGaDNwEx73WboXc9Pq1SXOQZcxDWkjqTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/types": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.3_1695740209916_0.060444886094425554", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "87e043750a9e821ed020a011e332f92244242733", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-iX0GXdESErjorEK5oF+JTsEl2n79WROwiDA34TQo7gqv9xTc7kQCYWoCvG2F0H4u8sse44mAOHkrpU7p3htvRw==", "signatures": [{"sig": "MEUCIFhtPG6wSX/cjPLj/woCLnG9hHpDK0uXx/AGZVFFEgmyAiEA9nxVlzYtUiioWsnpVA6Cin3cC4lDk4MhT5QNpu2S/jY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/types": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.4_1697076375420_0.942354174946475", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "2f8da42b75ba89e5cfcd677afd0856d52c0c2e68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-s9GO7fIBi/BLsZ0v3Rftr6Oe4t0ctJ8h4CCXfPoEJwmvAPMyNrfkOOJzm6b9PX9YXcCJWWQd/sBF/N26eBiMVw==", "signatures": [{"sig": "MEUCICQLuzu7lN6L83D++4R2dAb/cjF5PMXirbNDNCP2hpTlAiEAtuwsSoVoaqY9rs/BzF71lnH4Mlq8TtG5+TiRJHf3yRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5407}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.23.3_1699513433030_0.627703995243676", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "03d08e3691e405804ecdd19dd278a40cca531f29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-mps6auzgwjRrwKEZA05cOwuDc9FAzoyFS4ZsG/8F43bTLf/TgkJg7QXOrPO1JO599iA3qgK9MXdMGOEC8O1h6Q==", "signatures": [{"sig": "MEUCIAwL0NDEnOgmLtmSHV9Mz/n5oji2npSWe0g5fZONWl07AiEAviD8Za3l6y/z5z/IC0mygKumVWQNqmqvWy5iYvbmXGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.4", "@babel/traverse": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.23.4_1700490128313_0.19962855903762278", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "a512989e3d733d67912b370a80367108762e3978", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-y6lQRszHN6h2Lc+J/wQZ2ZjOPwGT04bPGPY1j+IUWso/1mPhkxVId35DAKJDFXa+ZlF93l1egAQy71HSv5RIyQ==", "signatures": [{"sig": "MEUCIQDh8g6QhFGj0kGG+qfqXnUf1sofcr5vPlQr+28IoTcY7gIgSuEJo3FCRTj8bNNLlSkpsQ4H8OGzDZ5+Zh+G9Wn4xX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5184}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/types": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.5_1702307920490_0.7066390613933047", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "6dd273d704dfcf6733cc0f60bfe90b4c4b7495ea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-HEMknHELb3NQK+6yETQsCQj1Mo9Tiuk6Qwm+vckFMVrNeSNX40ndm1rQkWKBxOtBcj4xAwl3ipf3iamCwbeZpw==", "signatures": [{"sig": "MEQCIDTTsU15OMbyBzh3kWq8bE+kwsSfHQkwBAI3nnSGf3rjAiAkXd+Dne/nG7x9PaFAAITrz7gBBs61dYKhmztrntWBXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5184}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/types": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.6_1706285639138_0.4472616522842081", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "3afdf2e5a9aa6fcc5238ed1026e6671b04d65752", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-neO+uHaUzEtC/vDPt2mVbYAgwuD7iH/gW36SbNY6coyhQ1ef+J1mZ54D1jGKZYYcusyDGvWxQgr1tKADW1oN1g==", "signatures": [{"sig": "MEUCIEM/9PQndYtZhtksKPK+9EBURqTHICcK3vVXaCyHFFiyAiEAvC4b0BPOw0ifI8eA98bk2BFEmuG34lZ0KUyfLkUwa8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5184}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/types": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.7_1709129089758_0.6984113253113691", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "5bc019ce5b3435c1cadf37215e55e433d674d4e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-7GAsGlK4cNL2OExJH1DzmDeKnRv/LXq0eLUSvudrehVA5Rgg4bIrqEUW29FbKMBRT0ztSqisv7kjP+XIC4ZMNw==", "signatures": [{"sig": "MEUCIQDlucgFFn3ImNwudleYyFq7kKDZJg6LBeqDJNyAf5wniQIgaGkzV9qczY9neEC/dBDhBQ0YFPYh8ruvgr78vmyxsZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5474}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/types": "^7.24.0", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.24.1_1710841731231_0.014728579008360665", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "47e07da802c0e4d708336208df7012683cac70bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-XJBFZiwXHAFe8DTW7uxtxNk3DJG/jiIrSQknOzdlWAh2IENMA1zEso7Kr5HklCg7rIvpwYw4p+4OFpPPqE6XSA==", "signatures": [{"sig": "MEUCIFDI6eK0u+jf7dEebrVdbjmVHITmSh1xmPnU9EiPITPlAiEAtGY9NdyG6NXnKyizf7zNf5rgH7Kfd5IJANPLKvWvcAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5108}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/types": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.8_1712236788140_0.3077544572937143", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "d9115669cc85aa91fbfb15f88f2226332cf4946a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-6voawq8T25Jvvnc4/rXcWZQKKxUNZcKMS8ZNrjxQqoRFernJJKjE3s18Qo6VFaatG5aiX5JV1oPD7DbJhn0a4Q==", "signatures": [{"sig": "MEUCIDRRvhtT2HgZHyEo0UDNFHoUkdxQIlK6q0Xg0zpFDhL8AiEAvncbc/D+9gETtwUzahOG63+gld8B696ivdJ4bddlrZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71442}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/types": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.24.6_1716553469791_0.31918228416519057", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "6c8c698841699da0217408ab6d3a6cf5787675e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-giG/CwBskecf3T5AF7qCk6qq++i3imwMsh4BmP+FHXShBjpUkxziFFnKqMf87ohsLrpqYAWmEAZvNQELHpLJzQ==", "signatures": [{"sig": "MEYCIQCwnFZg5R1PP8IXVauqL3OJcijWmdFF9f9HQbfzycyqYQIhAL5O72PShXKPzdV574IrG+FzCocudbt1HuvJ1/pwrqmW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71314}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/types": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.9_1717423454208_0.943710963117671", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "7abad815f86b90f24a9ea2a7bcad984633767e3a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-HhaTKqlmo1P6VraSVKeDbpjhvP1gXyforBULXryGT29204eizkorwFYqVt+eLLXoP+wvGBsvKcMEDULNfR/2Ew==", "signatures": [{"sig": "MEUCIQCwse+kPKE4TgSSZJgmeoDfw6ktuEIhNlDrtcaZsYzrnAIgQwwvzQelKEpAHMHTbawlub36kzKiRSgm5hfF1oTyDuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71323}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/types": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.10_1717500004091_0.8633922122866837", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "bea62b538c80605d8a0fac9b40f48e97efa7de63", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-e6q1TiVUzvH9KRvicuxdBTUj4AdKSRwzIyFFnfnezpCfP2/7Qmbb8qbU2j7GODbl4JMkblitCQjKYUaX/qkkwA==", "signatures": [{"sig": "MEUCIQCXbHPrcmrQOTxwPEHDt5pp6/Pa+vb8lv0uqrR46ad9qgIgHLvTvDDwF2sAzQYpiwyW7LHF8gf4/FUSj8UYSFId8IQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71399}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/types": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.24.7_1717593320463_0.7882354317748925", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "60e63f93a42e1659470a3a02f070711934a7b743", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-bpsN4FS2W3wIK4VeMvVQ08lN/s4Hf4zuvRfgpAfCGMqLgCxgVby+/94wK7UhNwy/DsDhlgt98cZNs4KpD68ZoA==", "signatures": [{"sig": "MEYCIQCTxXylBQkZ+icDb0uGbltZGw/Q9GqZm8Uh2dgzEBo4mgIhAIBi5LAG034LRyR0ldawbaUgHt5K13WOnN58PIW+Cayd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71212}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/types": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.11_1717751729908_0.4869008024915311", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "ba768467c42c31cfc2d1afc633efab6244bd6205", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-HpXABUXVpBJFR6PyTryKjQtVn2AjTjhOO1l7POeXtVX3Wlgw/9mOkTONJ9w8hy9In/0x5rjq3cgDQCCAXF6CIw==", "signatures": [{"sig": "MEUCIQDJMqYO/rpkILwkOVo1V3yF4INqyP5OZwTjKZkwoQaHwAIgIeYUIu08K2lDhRVdL8YlMnMVZrdwVmiV3Bw0ss/AL/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68008}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/types": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.12_1722015206429_0.9431950661542019", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "a516b78f894d1c08283f39d809b2048fd2f29448", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-8CbutzSSh4hmD+jJHIA8vdTNk15kAzOnFLVVgBSMGr28rt85ouT01/rezMecks9pkU939wDInImwCKv4ahU4IA==", "signatures": [{"sig": "MEQCIE0ytTfOEroAVX9v2Dxjff4eHzWMBgXBMqAzFNlmtMmvAiB5IlRmyjq3hf/80GAiH0stGyi8jln8oROkuQmk9L+PZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75937}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/types": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.25.7_1727882086472_0.32035978131025566", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "91e370486371637bd42161052f2602c701386891", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-rm9a5iEFPS4iMIy+/A/PiS0QN0UyjPIeVvbU5EMZFKJZHt8vQnasbpo3T3EFcxzCeYO0BHfc4RqooCZc51J86Q==", "signatures": [{"sig": "MEUCIQD6KIt/pz3LYmlYWxMnXNcpdtzgCIzXHbSfbAys56CFGwIgclZuQ+4wcz5o8nz+8e6qyrlGqafPe8WnRS5i9523WO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76255}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/types": "^7.25.8", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.25.8_1728566711262_0.6642447171439914", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "bfed75866261a8b643468b0ccfd275f2033214a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-TlprrJ1GBZ3r6s96Yq8gEQv82s8/5HnCVHtEJScUj90thHQbwe+E5MLhi2bbNHBEJuzrvltXSru+BUxHDoog7Q==", "signatures": [{"sig": "MEQCIE0Kuo4tVKqh8BcKtTtpJXvaciUomVkqxJhg/bW8VHREAiBHwZMYJSOejFRiX1Vli8cpYUr20EnvQI1hPfag/gfteg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5291}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/types": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.25.9_1729610465166_0.04130654429970271", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "14b34dc9ce78a558bcbaba632e742ab5aea2134f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-++AtbnHyh4+fiz3AwshoTV+M9aHwhrGAZ2bpINJuPUMJlRkcS5i/hgcoG4wY+126e8nwVGSQedojp8BZUQcHHg==", "signatures": [{"sig": "MEQCIApa3CHtCTr5AzArmWHv76RDxjZg3EKEpMrLW2cfum/mAiAKQL+mIhp+9ri78kuR7b8IHO5zRs98/3cajEIwEsjmbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5343}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/types": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.13_1729864448402_0.404163450484605", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "f4eb26c25357391933a523cf98740af334aacfb7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-BMLI4X3pdPuG8G4gdu21QO2QI6zccwkB5EKCoFGdDX2Hw/Zt6s/hSZ+xv3ZQUiNPUgBQDexAH4zuVSozKwjHkQ==", "signatures": [{"sig": "MEUCIEN3LSfAsBCCUVaL0Ku9eFZtI3pt2TWO2Ok/tguum/lKAiEAm/WiKs8jG4plF+/CWNpUl4YypxOQHtBKZOiwGGreun4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5343}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/types": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.14_1733504039016_0.5324409790290163", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "8c25966b36040ebb40281700d15fdcd3d5109a30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-ZWtfJq9/cmhzIMmMcEwLPjEWX66AVI4htPUXL0JzNfIn86+VBBEgoq8cuQWxgEciVfFhJwyFIRQ5QTwmi4lgUQ==", "signatures": [{"sig": "MEUCIQDyMG8VwXBUpACQFgHOKQ8w1O5EyMtUStgA1fK6CunAWwIgKJWK438i0yLM6Is2ZDOdxwlnyobVioYPs4yKXnQ0JCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5343}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/types": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.15_1736529864841_0.3266820972711262", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "ba3105b5bf7fbb147c30c04c2c21862bb47d8d08", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-InN0T8nD+Ts6+qrV6bfJUiNr1As7ByCASEVwvEjVaFHU7N1ODL7I1Az7F7OkWQQXKF9Blx4Fm+DSKO8PbGsqsQ==", "signatures": [{"sig": "MEUCIFpOxyDu9TnQ7o2INIYwDC5Nw1woUwcOfL5ktVy5p6swAiEAiq34n7H7dMX1gI8HgiyjjTZDn0CGEQRb0w7pdLQz6Ps=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5343}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/types": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.16_1739534340682_0.1625000094151834", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "81c0d696ad7f73126cf240c722917e9c1902ab5d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-H70lV6PXVCgsCI5wqcoomWvqMRx4DPfnqoCegkNSsTurfOgECap+OOm3rhFiuC6/2MDkIg9dbwKSCaZrVHqubg==", "signatures": [{"sig": "MEQCIDPQyDS0hsqyJ2f2UkhRPAInPid+eW74ByyslnXPRvqGAiAValRCtWlM+iTude020N2HGcbbE6dP8y3xBDRlcR3S9w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5343}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/types": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-alpha.17_1741717492372_0.16409773659403903", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-numeric-separator", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "614e0b15cc800e5997dadd9bd6ea524ed6c819c6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-fdPKAcujuvEChxDBJ5c+0BTaS6revLV7CJL08e4m3de8qJfNIuCc2nc7XJYOjBoTMJeqSmwXJ0ypE14RCjLwaw==", "signatures": [{"sig": "MEQCID9Ax6QGic8iZS05KLkNhhfA5BJeGuPqnytjzYo0HfOAAiADVo124nIy8vDl7q+Cu0qMTqtLGXDjhFu5Jm9C6vhoxw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5291}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/types": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_7.27.1_1746025730839_0.4199147922802655", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "dist": {"shasum": "ca3bd20380b2693e59afcd9f41d0c14f1e4c4c71", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-Y0NBxMxmS14f+xFtbSLsigr+kuUsNWRLWFcOFae16TdUadBrv9F+OOMTwBLd2PD6zEX12fSiljoGU1n3qAnPOg==", "signatures": [{"sig": "MEUCIFniqBAjfkDR+b86MfixDg1OsJllIOydx28AvN+rAM86AiEAy0aS7VWkYrRqzGhcTz1bFU2noxK+TCyRnqEeW97AwLg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5315}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/types": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-numeric-separator_8.0.0-beta.0_1748620262724_0.01416881803461889", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-numeric-separator", "version": "8.0.0-beta.1", "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-numeric-separator@8.0.0-beta.1", "dist": {"shasum": "3557fcbc471aa4e90e44afd35e50ce2ee06ecf46", "integrity": "sha512-8oxVKnld0vmKlgLYTuEJteT/s3mSk6Bt6zGa1MjXksHr4HRBvt38sbdao4w5MYeIquDPZHftOvqINB9dl9yibw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5315, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDBNsu36KcBzMY/H83UHlrk9oOJOE67SNuzja1BIr9P7AIhAJ4TMTAx0T0FvIcDRNzESkD4eYmIPRJ1mE+LYueaB7jx"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-numeric-separator_8.0.0-beta.1_1751447055435_0.4666952317898858"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:16.366Z", "modified": "2025-07-02T09:04:15.842Z", "7.22.0": "2023-05-26T13:45:16.597Z", "7.22.3": "2023-05-27T10:10:58.874Z", "7.22.5": "2023-06-08T18:21:18.685Z", "8.0.0-alpha.0": "2023-07-20T13:59:52.871Z", "8.0.0-alpha.1": "2023-07-24T17:51:52.745Z", "8.0.0-alpha.2": "2023-08-09T15:14:53.141Z", "7.22.11": "2023-08-24T13:08:39.531Z", "8.0.0-alpha.3": "2023-09-26T14:56:50.137Z", "8.0.0-alpha.4": "2023-10-12T02:06:15.616Z", "7.23.3": "2023-11-09T07:03:53.287Z", "7.23.4": "2023-11-20T14:22:08.451Z", "8.0.0-alpha.5": "2023-12-11T15:18:40.725Z", "8.0.0-alpha.6": "2024-01-26T16:13:59.314Z", "8.0.0-alpha.7": "2024-02-28T14:04:49.923Z", "7.24.1": "2024-03-19T09:48:51.383Z", "8.0.0-alpha.8": "2024-04-04T13:19:48.278Z", "7.24.6": "2024-05-24T12:24:29.961Z", "8.0.0-alpha.9": "2024-06-03T14:04:14.402Z", "8.0.0-alpha.10": "2024-06-04T11:20:04.292Z", "7.24.7": "2024-06-05T13:15:20.621Z", "8.0.0-alpha.11": "2024-06-07T09:15:30.114Z", "8.0.0-alpha.12": "2024-07-26T17:33:26.701Z", "7.25.7": "2024-10-02T15:14:46.684Z", "7.25.8": "2024-10-10T13:25:11.414Z", "7.25.9": "2024-10-22T15:21:05.380Z", "8.0.0-alpha.13": "2024-10-25T13:54:08.653Z", "8.0.0-alpha.14": "2024-12-06T16:53:59.221Z", "8.0.0-alpha.15": "2025-01-10T17:24:25.009Z", "8.0.0-alpha.16": "2025-02-14T11:59:00.947Z", "8.0.0-alpha.17": "2025-03-11T18:24:52.543Z", "7.27.1": "2025-04-30T15:08:51.002Z", "8.0.0-beta.0": "2025-05-30T15:51:02.913Z", "8.0.0-beta.1": "2025-07-02T09:04:15.589Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}