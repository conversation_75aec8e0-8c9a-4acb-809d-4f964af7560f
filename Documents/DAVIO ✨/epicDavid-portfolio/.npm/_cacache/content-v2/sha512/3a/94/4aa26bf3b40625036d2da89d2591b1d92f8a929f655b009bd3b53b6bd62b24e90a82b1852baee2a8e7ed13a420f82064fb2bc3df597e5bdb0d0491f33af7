{"_id": "@eslint/object-schema", "_rev": "3-264867575cda218f072edb485c24298c", "name": "@eslint/object-schema", "dist-tags": {"latest": "2.1.6"}, "versions": {"2.1.3": {"name": "@eslint/object-schema", "version": "2.1.3", "keywords": ["object", "validation", "schema", "merge"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/object-schema@2.1.3", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "e65ae80ee2927b4fd8c5c26b15ecacc2b2a6cc2a", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.3.tgz", "fileCount": 10, "integrity": "sha512-HAbhAYKfsAC2EkTqve00ibWIZlaU74Z1EHwAjYr4PXF0YU2VEA1zSIKSSpKszRLRWwHzzRZXvK632u+uXzvsvw==", "signatures": [{"sig": "MEYCIQCu6KY8smR5L4QDCx9vPWjIgUafFB+awOTpVGxIYt61QgIhALVJpHPwK8J/SwGcCWDEm+lmFf3q/Fk8S5JuiYoYQLtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52807}, "type": "module", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "12012ffea9128c8c5cc035d3806a128308b26186", "scripts": {"test": "mocha tests/", "build": "rollup -c && tsc -p tsconfig.esm.json && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "An object schema merger/validator", "directories": {"test": "tests"}, "_nodeVersion": "20.13.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-schema_2.1.3_1717101623079_0.8190393680536692", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@eslint/object-schema", "version": "2.1.4", "keywords": ["object", "validation", "schema", "merge"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/object-schema@2.1.4", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "9e69f8bb4031e11df79e03db09f9dbbae1740843", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.4.tgz", "fileCount": 10, "integrity": "sha512-BsWiH1yFGjXXS2yvrf5LyuoSIIbPrGUWob917o+BTKuZ7qJdxX8aJLRxs1fS9n6r7vESrq1OUqb68dANcFXuQQ==", "signatures": [{"sig": "MEUCIEvN7PZafbjBuQghL/76RRRPeYJRdB6MAOlRRh6QPhg8AiEAgwf1Pz2DMSBIcyWcfAYXy7nk6Uy2HzkX9AFgsbuPEho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55487}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "2a7bed41976f94495f7ba46f459f1de9d3305664", "scripts": {"test": "mocha tests/", "build": "rollup -c && tsc -p tsconfig.esm.json && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "An object schema merger/validator", "directories": {"test": "tests"}, "_nodeVersion": "20.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-schema_2.1.4_1718202693724_0.9989908493722703", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@eslint/object-schema", "version": "2.1.5", "keywords": ["object", "validation", "schema", "merge"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/object-schema@2.1.5", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "8670a8f6258a2be5b2c620ff314a1d984c23eb2e", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.5.tgz", "fileCount": 10, "integrity": "sha512-o0bhxnL89h5Bae5T318nFoFzGy+YE5i/gGkoPAgkmTVdRKTiv3p8JHevPiPaMwoloKfEiiaHlawCqaZMqRm+XQ==", "signatures": [{"sig": "MEYCIQC5vaJ//+9g4KTFu4l0uhU86QSPOSWEo6aP7yZ7dybUwwIhAJ2sUapPc+0JhYl0eBboUkjlRqGdr8XcjhTxYHGYnC96", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fobject-schema@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 57040}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "dd8d161c635450f3e37109f833737bf69f54db55", "scripts": {"test": "mocha tests/", "build": "rollup -c && tsc -p tsconfig.esm.json && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "An object schema merger/validator", "directories": {"test": "tests"}, "_nodeVersion": "22.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-schema_2.1.5_1733347408751_0.3211997724344855", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@eslint/object-schema", "version": "2.1.6", "description": "An object schema merger/validator", "type": "module", "main": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}}, "publishConfig": {"access": "public"}, "directories": {"test": "tests"}, "scripts": {"build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "build": "rollup -c && tsc -p tsconfig.esm.json && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "test": "mocha tests/", "test:coverage": "c8 npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git"}, "keywords": ["object", "validation", "schema", "merge"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite#readme", "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "rollup-plugin-copy": "^3.5.0", "typescript": "^5.4.5"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "_id": "@eslint/object-schema@2.1.6", "gitHead": "e1cb6037bc237313dbf3f6a7b9f5cd3c3105b668", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==", "shasum": "58369ab5b5b3ca117880c0f6c0b0f32f6950f24f", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz", "fileCount": 10, "unpackedSize": 56960, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fobject-schema@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDDMEh/N3HSCxrAbPFSoR+5b4AiJV0i+ekupaMB70CpMAIgHUnHk9TpbvgxmZMl9XVz8UxJTBtVNLKTewhnONDeCCk="}]}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/object-schema_2.1.6_1738344166529_0.8094857111733287"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-05-30T20:40:22.953Z", "modified": "2025-01-31T17:22:47.181Z", "2.1.3": "2024-05-30T20:40:23.310Z", "2.1.4": "2024-06-12T14:31:33.901Z", "2.1.5": "2024-12-04T21:23:28.907Z", "2.1.6": "2025-01-31T17:22:46.772Z"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/eslint/rewrite#readme", "keywords": ["object", "validation", "schema", "merge"], "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git"}, "description": "An object schema merger/validator", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# ObjectSchema Package\n\n## Overview\n\nA JavaScript object merge/validation utility where you can define a different merge and validation strategy for each key. This is helpful when you need to validate complex data structures and then merge them in a way that is more complex than `Object.assign()`. This is used in the [`@eslint/config-array`](https://npmjs.com/package/@eslint/config-array) package but can also be used on its own.\n\n## Installation\n\nFor Node.js and compatible runtimes:\n\n```shell\nnpm install @eslint/object-schema\n# or\nyarn add @eslint/object-schema\n# or\npnpm install @eslint/object-schema\n# or\nbun install @eslint/object-schema\n```\n\nFor Deno:\n\n```shell\ndeno add @eslint/object-schema\n```\n\n## Usage\n\nImport the `ObjectSchema` constructor:\n\n```js\n// using ESM\nimport { ObjectSchema } from \"@eslint/object-schema\";\n\n// using CommonJS\nconst { ObjectSchema } = require(\"@eslint/object-schema\");\n\nconst schema = new ObjectSchema({\n\t// define a definition for the \"downloads\" key\n\tdownloads: {\n\t\trequired: true,\n\t\tmerge(value1, value2) {\n\t\t\treturn value1 + value2;\n\t\t},\n\t\tvalidate(value) {\n\t\t\tif (typeof value !== \"number\") {\n\t\t\t\tthrow new Error(\"Expected downloads to be a number.\");\n\t\t\t}\n\t\t},\n\t},\n\n\t// define a strategy for the \"versions\" key\n\tversion: {\n\t\trequired: true,\n\t\tmerge(value1, value2) {\n\t\t\treturn value1.concat(value2);\n\t\t},\n\t\tvalidate(value) {\n\t\t\tif (!Array.isArray(value)) {\n\t\t\t\tthrow new Error(\"Expected versions to be an array.\");\n\t\t\t}\n\t\t},\n\t},\n});\n\nconst record1 = {\n\tdownloads: 25,\n\tversions: [\"v1.0.0\", \"v1.1.0\", \"v1.2.0\"],\n};\n\nconst record2 = {\n\tdownloads: 125,\n\tversions: [\"v2.0.0\", \"v2.1.0\", \"v3.0.0\"],\n};\n\n// make sure the records are valid\nschema.validate(record1);\nschema.validate(record2);\n\n// merge together (schema.merge() accepts any number of objects)\nconst result = schema.merge(record1, record2);\n\n// result looks like this:\n\nconst result = {\n\tdownloads: 75,\n\tversions: [\"v1.0.0\", \"v1.1.0\", \"v1.2.0\", \"v2.0.0\", \"v2.1.0\", \"v3.0.0\"],\n};\n```\n\n## Tips and Tricks\n\n### Named merge strategies\n\nInstead of specifying a `merge()` method, you can specify one of the following strings to use a default merge strategy:\n\n- `\"assign\"` - use `Object.assign()` to merge the two values into one object.\n- `\"overwrite\"` - the second value always replaces the first.\n- `\"replace\"` - the second value replaces the first if the second is not `undefined`.\n\nFor example:\n\n```js\nconst schema = new ObjectSchema({\n\tname: {\n\t\tmerge: \"replace\",\n\t\tvalidate() {},\n\t},\n});\n```\n\n### Named validation strategies\n\nInstead of specifying a `validate()` method, you can specify one of the following strings to use a default validation strategy:\n\n- `\"array\"` - value must be an array.\n- `\"boolean\"` - value must be a boolean.\n- `\"number\"` - value must be a number.\n- `\"object\"` - value must be an object.\n- `\"object?\"` - value must be an object or null.\n- `\"string\"` - value must be a string.\n- `\"string!\"` - value must be a non-empty string.\n\nFor example:\n\n```js\nconst schema = new ObjectSchema({\n\tname: {\n\t\tmerge: \"replace\",\n\t\tvalidate: \"string\",\n\t},\n});\n```\n\n### Subschemas\n\nIf you are defining a key that is, itself, an object, you can simplify the process by using a subschema. Instead of defining `merge()` and `validate()`, assign a `schema` key that contains a schema definition, like this:\n\n```js\nconst schema = new ObjectSchema({\n\tname: {\n\t\tschema: {\n\t\t\tfirst: {\n\t\t\t\tmerge: \"replace\",\n\t\t\t\tvalidate: \"string\",\n\t\t\t},\n\t\t\tlast: {\n\t\t\t\tmerge: \"replace\",\n\t\t\t\tvalidate: \"string\",\n\t\t\t},\n\t\t},\n\t},\n});\n\nschema.validate({\n\tname: {\n\t\tfirst: \"n\",\n\t\tlast: \"z\",\n\t},\n});\n```\n\n### Remove Keys During Merge\n\nIf the merge strategy for a key returns `undefined`, then the key will not appear in the final object. For example:\n\n```js\nconst schema = new ObjectSchema({\n\tdate: {\n\t\tmerge() {\n\t\t\treturn undefined;\n\t\t},\n\t\tvalidate(value) {\n\t\t\tDate.parse(value); // throws an error when invalid\n\t\t},\n\t},\n});\n\nconst object1 = { date: \"5/5/2005\" };\nconst object2 = { date: \"6/6/2006\" };\n\nconst result = schema.merge(object1, object2);\n\nconsole.log(\"date\" in result); // false\n```\n\n### Requiring Another Key Be Present\n\nIf you'd like the presence of one key to require the presence of another key, you can use the `requires` property to specify an array of other properties that any key requires. For example:\n\n```js\nconst schema = new ObjectSchema();\n\nconst schema = new ObjectSchema({\n\tdate: {\n\t\tmerge() {\n\t\t\treturn undefined;\n\t\t},\n\t\tvalidate(value) {\n\t\t\tDate.parse(value); // throws an error when invalid\n\t\t},\n\t},\n\ttime: {\n\t\trequires: [\"date\"],\n\t\tmerge(first, second) {\n\t\t\treturn second;\n\t\t},\n\t\tvalidate(value) {\n\t\t\t// ...\n\t\t},\n\t},\n});\n\n// throws error: Key \"time\" requires keys \"date\"\nschema.validate({\n\ttime: \"13:45\",\n});\n```\n\nIn this example, even though `date` is an optional key, it is required to be present whenever `time` is present.\n\n## License\n\nApache 2.0\n\n<!-- NOTE: This section is autogenerated. Do not manually edit.-->\n<!--sponsorsstart-->\n\n## Sponsors\n\nThe following companies, organizations, and individuals support ESLint's ongoing maintenance and development. [Become a Sponsor](https://eslint.org/donate)\nto get your logo on our READMEs and [website](https://eslint.org/sponsors).\n\n<h3>Platinum Sponsors</h3>\n<p><a href=\"https://automattic.com\"><img src=\"https://images.opencollective.com/automattic/d0ef3e1/logo.png\" alt=\"Automattic\" height=\"128\"></a> <a href=\"https://www.airbnb.com/\"><img src=\"https://images.opencollective.com/airbnb/d327d66/logo.png\" alt=\"Airbnb\" height=\"128\"></a></p><h3>Gold Sponsors</h3>\n<p><a href=\"https://qlty.sh/\"><img src=\"https://images.opencollective.com/qltysh/33d157d/logo.png\" alt=\"Qlty Software\" height=\"96\"></a> <a href=\"https://trunk.io/\"><img src=\"https://images.opencollective.com/trunkio/fb92d60/avatar.png\" alt=\"trunk.io\" height=\"96\"></a></p><h3>Silver Sponsors</h3>\n<p><a href=\"https://www.serptriumph.com/\"><img src=\"https://images.opencollective.com/serp-triumph5/fea3074/logo.png\" alt=\"SERP Triumph\" height=\"64\"></a> <a href=\"https://www.jetbrains.com/\"><img src=\"https://images.opencollective.com/jetbrains/fe76f99/logo.png\" alt=\"JetBrains\" height=\"64\"></a> <a href=\"https://liftoff.io/\"><img src=\"https://images.opencollective.com/liftoff/5c4fa84/logo.png\" alt=\"Liftoff\" height=\"64\"></a> <a href=\"https://americanexpress.io\"><img src=\"https://avatars.githubusercontent.com/u/3853301\" alt=\"American Express\" height=\"64\"></a></p><h3>Bronze Sponsors</h3>\n<p><a href=\"https://cybozu.co.jp/\"><img src=\"https://images.opencollective.com/cybozu/933e46d/logo.png\" alt=\"Cybozu\" height=\"32\"></a> <a href=\"https://www.crosswordsolver.org/anagram-solver/\"><img src=\"https://images.opencollective.com/anagram-solver/2666271/logo.png\" alt=\"Anagram Solver\" height=\"32\"></a> <a href=\"https://icons8.com/\"><img src=\"https://images.opencollective.com/icons8/7fa1641/logo.png\" alt=\"Icons8\" height=\"32\"></a> <a href=\"https://discord.com\"><img src=\"https://images.opencollective.com/discordapp/f9645d9/logo.png\" alt=\"Discord\" height=\"32\"></a> <a href=\"https://www.gitbook.com\"><img src=\"https://avatars.githubusercontent.com/u/7111340\" alt=\"GitBook\" height=\"32\"></a> <a href=\"https://nolebase.ayaka.io\"><img src=\"https://avatars.githubusercontent.com/u/11081491\" alt=\"Neko\" height=\"32\"></a> <a href=\"https://nx.dev\"><img src=\"https://avatars.githubusercontent.com/u/23692104\" alt=\"Nx\" height=\"32\"></a> <a href=\"https://opensource.mercedes-benz.com/\"><img src=\"https://avatars.githubusercontent.com/u/34240465\" alt=\"Mercedes-Benz Group\" height=\"32\"></a> <a href=\"https://herocoders.com\"><img src=\"https://avatars.githubusercontent.com/u/37549774\" alt=\"HeroCoders\" height=\"32\"></a></p>\n<h3>Technology Sponsors</h3>\nTechnology sponsors allow us to use their products and services for free as part of a contribution to the open source ecosystem and our work.\n<p><a href=\"https://netlify.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/netlify-icon.svg\" alt=\"Netlify\" height=\"32\"></a> <a href=\"https://algolia.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/algolia-icon.svg\" alt=\"Algolia\" height=\"32\"></a> <a href=\"https://1password.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/1password-icon.svg\" alt=\"1Password\" height=\"32\"></a></p>\n<!--sponsorsend-->\n", "readmeFilename": "README.md"}