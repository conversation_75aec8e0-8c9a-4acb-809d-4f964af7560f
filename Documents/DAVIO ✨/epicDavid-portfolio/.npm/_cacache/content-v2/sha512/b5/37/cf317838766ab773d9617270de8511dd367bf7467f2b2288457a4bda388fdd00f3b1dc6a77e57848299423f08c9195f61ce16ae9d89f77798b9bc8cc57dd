{"_id": "loose-envify", "_rev": "15-4c5536f98845cef63a0f5b21911d420e", "name": "loose-envify", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "dist-tags": {"latest": "1.4.0"}, "versions": {"1.0.0": {"name": "loose-envify", "version": "1.0.0", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^1.0.1"}, "devDependencies": {"browserify": "^11.0.1", "envify": "^3.4.0", "tap": "^1.4.0"}, "gitHead": "93e8785c8b7a1fbf14e01c4b4aed4d102110a328", "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "_id": "loose-envify@1.0.0", "_shasum": "df40073c10a625c178cccb0db23164c0fd058e75", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "4.1.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "df40073c10a625c178cccb0db23164c0fd058e75", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.0.0.tgz", "integrity": "sha512-u/so5faDpWlTZQu3L67RSH6I5ShOyAGdNKJD4ckHNEJs/YoQTqgguXDKg1SVNpfYnbxo49oCSj69mu4Bsol31w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUhZCJ7v+3J74rGeyt/pbpsSFL7ctTMUiKA6QW258UpQIgRkaxAAUiqSPstcjhUShPBEEVZhFmW0vAN77KE7187IA="}]}, "directories": {}}, "1.1.0": {"name": "loose-envify", "version": "1.1.0", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^1.0.1"}, "devDependencies": {"browserify": "^11.0.1", "envify": "^3.4.0", "tap": "^1.4.0"}, "gitHead": "f536f4fdbde317d77827ac8655c6c8473928b9e1", "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "_id": "loose-envify@1.1.0", "_shasum": "527582d62cff4e04da3f9976c7110d3392ec7e0c", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "527582d62cff4e04da3f9976c7110d3392ec7e0c", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.1.0.tgz", "integrity": "sha512-4NSTa8hGGPR5GZqmiBXK5JbxPRzsfMXayx55AvpLGHvhsNiL01bToVMGiFxe4CBhBLJCil3Ek08q9+SNWiKlyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCujhpM3ncLEg8LQHHpsiiu2QEzjXWTihKUpDZzOwGIbgIgI891QrwM9E8IKzzrnanV1Umy9Zr81efTv4c2dTx9X98="}]}, "directories": {}}, "1.2.0": {"name": "loose-envify", "version": "1.2.0", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"loose-envify": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^1.0.1"}, "devDependencies": {"browserify": "^11.0.1", "envify": "^3.4.0", "tap": "^1.4.0"}, "gitHead": "58a4c7641e8c8569df3e7cea72b398a9cb1cf158", "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "_id": "loose-envify@1.2.0", "_shasum": "69a65aad3de542cf4ee0f4fe74e8e33c709ccb0f", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "dist": {"shasum": "69a65aad3de542cf4ee0f4fe74e8e33c709ccb0f", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.2.0.tgz", "integrity": "sha512-KyUZthzTKOK79BVhotg9i7VT6BcBij33rdnPMeZfafKTO5Br6gzDGqTieocXCJW1n6so0rZCiFgoYM5W10tFWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtpoJpeWrPT6X7fvjQGz58kqdG1BJmIXXNA+MrcLaBuAiAMXoqdBFk1jGqdZc5sB+yKV8sqEe6hShPRGiV9FyUTag=="}]}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/loose-envify-1.2.0.tgz_1463284327398_0.7049096294213086"}, "directories": {}}, "1.3.0": {"name": "loose-envify", "version": "1.3.0", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "bin": {"loose-envify": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^2.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "gitHead": "11c1714ff0f25b6046c38eedd909f2ae9fa12446", "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "_id": "loose-envify@1.3.0", "_shasum": "6b26248c42f6d4fa4b0d8542f78edfcde35642a8", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "dist": {"shasum": "6b26248c42f6d4fa4b0d8542f78edfcde35642a8", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.0.tgz", "integrity": "sha512-500ib3eg2h+w0xlKA5mcW1BxpB5EzgfK04rocSxP7JXnOHHAYD68iqObXKPyKvFmkdJT0ptUmm2IOyFSPtrCQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGguqjxYdiGoNZ3fbVChInbkXJuROP1NS9hmnu8nzTeOAiEA9RTY76cXfCr6xlgdVxttsjRRWWVg2wSHH0MyHK9oBsY="}]}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/loose-envify-1.3.0.tgz_1477683270638_0.3732579650823027"}, "directories": {}}, "1.3.1": {"name": "loose-envify", "version": "1.3.1", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "bin": {"loose-envify": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^3.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "gitHead": "7b2d41e61a7ddba5335154b4aba327f6e850f7fd", "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "_id": "loose-envify@1.3.1", "_shasum": "d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.3.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "dist": {"shasum": "d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.1.tgz", "integrity": "sha512-iG/U770U9HaHmy0u+fSyxSIclZ3d9WPFtGjV2drWW0SthBnQ1Fa/SCKIaGLAVwYzrBGEPx9gen047er+MCUgnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeNL8+NvgCXNu+DUYJ8CpC/YpkMx1ayUZESNgzzYsTFgIgcvI3hPULdKI2NvMJwgK389kwbBvn1veK8wA/FxXNQC8="}]}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/loose-envify-1.3.1.tgz_1484486581665_0.5577248032204807"}, "directories": {}}, "1.4.0": {"name": "loose-envify", "version": "1.4.0", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "bin": {"loose-envify": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "gitHead": "a8fdd02e3a435195f526053882d64537d627b3e6", "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "_id": "loose-envify@1.4.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "shasum": "71ee51fa7be4caec1a63839f7e682d8132d30caf", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "fileCount": 8, "unpackedSize": 5814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRJP5CRA9TVsSAnZWagAAQQ4P+wRneDh61dzk3uPX6QCb\nZ97i+VxSY74ti8gVGSCryXFkL8zPsjlBd1/yADnaS5y6InlHuHQZQ2820I3x\ngfSjlhCU9drbF8vXw7FSxIRQfFO5JvF7+L5M1mvsDVmz024M+97DQwFnRjo5\nCvSDVLC/HbnnTRX1Cfj9kQlDVS85RhLaDvYWendfUfivp++TwUizDUTS8nOu\nVIiwXygaef55bBmo+yNeQa+GmyAWET3jAaPl0MNBht6jtnMY9ZkgwXc03zaE\n0wRcOZbBfhUKk1VEk2TCN/TtN+AyktbI5rmyQXoHBGn3HjZntI/LjLmAw9dD\ndetTKPf1O2n8KLT/tGZjIRQjQWNbB51thQ4H3KsMBSfbIhdnZvFHmnDKilug\nIGjE4lKa4px0/ac+SBw6wQuZMy4I3xCbZd59qSpPB3IO0E+Qm+rhLdUiMDjf\n2y49xwMB7dixjRv7sLLqYwiSkXGkLp1N81drN7oNoZaLxOVCbJQKYoHwnsY4\ncoRc7W768FaKwZJPIWfZK2u2MAq5TL4v2+2laxAn25K5Qs6hh8rguSho7iPR\nZcaKA/maVJo8BGO+CEqvaysfUbnYZ06VOFnzG/TbLnLlmZUMMex95z8RloP8\n4kcoh3muuqd75sMUDURgjFCvXbQeORMZ3sa+G8h716pUVjvKLu5xNOF7qCN0\npadk\r\n=rcpJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtEQH8N9PyT4xrBOIczpbwwOu4sf2mIqw6XS4KIwV+WgIgQZ76hRyzJ94tUL0jNVJ8VyAx8MoCeb7p0qmyKQUEka8="}]}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/loose-envify_1.4.0_1531220985830_0.4259877346481684"}, "_hasShrinkwrap": false}}, "readme": "# loose-envify\n\n[![Build Status](https://travis-ci.org/zertosh/loose-envify.svg?branch=master)](https://travis-ci.org/zertosh/loose-envify)\n\nFast (and loose) selective `process.env` replacer using [js-tokens](https://github.com/lydell/js-tokens) instead of an AST. Works just like [envify](https://github.com/hughsk/envify) but much faster.\n\n## Gotchas\n\n* Doesn't handle broken syntax.\n* Doesn't look inside embedded expressions in template strings.\n  - **this won't work:**\n  ```js\n  console.log(`the current env is ${process.env.NODE_ENV}`);\n  ```\n* Doesn't replace oddly-spaced or oddly-commented expressions.\n  - **this won't work:**\n  ```js\n  console.log(process./*won't*/env./*work*/NODE_ENV);\n  ```\n\n## Usage/Options\n\nloose-envify has the exact same interface as [envify](https://github.com/hughsk/envify), including the CLI.\n\n## Benchmark\n\n```\nenvify:\n\n  $ for i in {1..5}; do node bench/bench.js 'envify'; done\n  708ms\n  727ms\n  791ms\n  719ms\n  720ms\n\nloose-envify:\n\n  $ for i in {1..5}; do node bench/bench.js '../'; done\n  51ms\n  52ms\n  52ms\n  52ms\n  52ms\n```\n", "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}], "time": {"modified": "2023-07-23T04:49:38.578Z", "created": "2015-09-21T04:02:42.524Z", "1.0.0": "2015-09-21T04:02:42.524Z", "1.1.0": "2015-10-18T14:42:18.633Z", "1.2.0": "2016-05-15T03:52:10.703Z", "1.3.0": "2016-10-28T19:34:32.580Z", "1.3.1": "2017-01-15T13:23:02.230Z", "1.4.0": "2018-07-10T11:09:45.917Z"}, "homepage": "https://github.com/zertosh/loose-envify", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"chinawolf_wyp": true, "alexxnica": true, "shuoshubao": true, "nraibaud": true, "chaoliu": true, "flumpus-dev": true}}