{"_id": "fast-glob", "_rev": "55-849c73517a7e84ae7d7c31fc0351b605", "name": "fast-glob", "dist-tags": {"next": "2.0.2", "beta": "3.2.1-beta.1", "latest": "3.3.3"}, "versions": {"1.0.0": {"name": "fast-glob", "version": "1.0.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@1.0.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "944c8c21279d7b0144d2f4df0c946dcecaacffe5", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-1.0.0.tgz", "integrity": "sha512-+8e67qn1vteaL9B+//SpiG/ISRhaSPWilRBkyiV9h/hYlPh9d6XxE2omyXxbp6Nf8Zs6wtPSLBgU2X8x127WtA==", "signatures": [{"sig": "MEYCIQD/dEJhySzNcbg2mdOCQJEbyhpj9SbCfCgxU6t2cp0lugIhAI2k3VSrtkVa936RlLRez22RKMnyXJpDDOUZ8SEBtNFG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "944c8c21279d7b0144d2f4df0c946dcecaacffe5", "engines": {"node": ">=4.0.0"}, "scripts": {"lint": "tslint src/**/*.ts", "test": "rimraf .tmp && mocha out/{,**/}*.spec.js -s 0", "bench": "npm run clean && npm run lint && npm run compile && bash benchmark/benchmark.sh", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint && npm run compile -- --sourceMap --watch", "compile": "tsc"}, "typings": "out/fglob.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"bash-glob": "^0.1.1", "micromatch": "^2.3.11", "glob-parent": "^3.1.0", "readdir-enhanced": "^1.4.5"}, "devDependencies": {"glob": "^7.1.1", "mocha": "^3.2.0", "rimraf": "^2.5.4", "tslint": "^4.2.0", "fs-extra": "^1.0.0", "typescript": "^2.1.4", "@types/node": "^6.0.52", "@types/mocha": "^2.2.34", "@types/rimraf": "0.0.28", "tslint-config-xo": "^1.0.0", "@types/micromatch": "^2.3.29"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob-1.0.0.tgz_1482922143891_0.25564247369766235", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.1": {"name": "fast-glob", "version": "1.0.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@1.0.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "30f9b1120fd57a7f172364a6458fbdbd98187b3c", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-1.0.1.tgz", "integrity": "sha512-C2VHbdBwSkaQDyavjQZDflZzmZKrsUK3fTdJtsOnED0L0vtHCw+NL0h8pRcydbpRHlNJLZ4/LbOfEdJKspK91A==", "signatures": [{"sig": "MEQCIB5zKuPCbORSltWHmVnh+o6nPndMfnYOah1A4m+fjh+XAiAyIENEPNSDSC+Z/018nDs9iFmBt7uassVNqiQ+EQfgeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "gitHead": "cdce6ac564a2c08d3347eedf145e975318914ebe", "scripts": {"lint": "tslint src/**/*.ts", "test": "rimraf .tmp && mocha out/{,**/}*.spec.js -s 0", "bench": "npm run clean && npm run lint && npm run compile && bash benchmark/benchmark.sh", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint && npm run compile -- --sourceMap --watch", "compile": "tsc"}, "typings": "out/fglob.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.0.2", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"bash-glob": "^1.0.1", "micromatch": "^3.0.3", "glob-parent": "^3.1.0", "readdir-enhanced": "^1.5.2"}, "devDependencies": {"glob": "^7.1.2", "mocha": "^3.4.2", "rimraf": "^2.6.1", "tslint": "^5.4.2", "fs-extra": "^3.0.1", "typescript": "^2.3.4", "@types/node": "^7.0.27", "@types/mocha": "^2.2.41", "@types/rimraf": "0.0.28", "tslint-config-xo": "^1.2.0", "@types/micromatch": "^2.3.29"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob-1.0.1.tgz_1496479244042_0.014894188614562154", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "fast-glob", "version": "2.0.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.0.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "f2ed03b562ac108705c088d3e23c2eede8fcd5ff", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.0.0.tgz", "integrity": "sha512-9ynWARfZjWZ8b63/rIxtYkLAnoqQzJEZp/2BTZlKyDk/0sO3AqcJgWW1RKUvbtsQ23XW5LQQYrjKmqZHwskj/g==", "signatures": [{"sig": "MEUCIFLMaIqu38kfWWr4NGNOthbXwc+QxreAR1yMNCOHEwIkAiEA9fqhswYZD2CWGnBxejEoipHYwXvt/eO9httqIjoK4Ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "6315ec0b86d28639f7a7297702ca70289542bd62", "scripts": {"lint": "tslint src/{,**/}*.ts -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge2": "1.2.1", "micromatch": "3.1.5", "glob-parent": "3.1.0", "readdir-enhanced": "2.2.0"}, "devDependencies": {"execa": "^0.8.0", "mocha": "^4.1.0", "globby": "^7.1.1", "rimraf": "^2.6.2", "tslint": "^5.8.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^1.0.1", "typescript": "^2.6.2", "@types/glob": "^5.0.34", "@types/node": "^9.3.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.46", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob-2.0.0.tgz_1516550521475_0.3808939950540662", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "fast-glob", "version": "2.0.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.0.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "340e9ea857140d881612ea91debcf4525046eb27", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.0.1.tgz", "integrity": "sha512-MrXqVX9JibHebfgZAz6aB8EXp5v0AD+pusz3Fzsf0KuydGYbbeMJ+WVIyimpCoGsKcZDVOYW6sC+vdtccHKOwA==", "signatures": [{"sig": "MEUCIQDOR4s5JzGbFQyN4v5H2ubRutX8yBTgIrbcKvJ3/iR36AIgHz/xb0SI5KoFnJksB5TEOSl1oOEieCFXcPP/CU2GRmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "edb96eb62592e40c8aa5132877d17467c897eaac", "scripts": {"lint": "tslint src/{,**/}*.ts -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"merge2": "1.2.1", "micromatch": "3.1.5", "glob-parent": "3.1.0", "readdir-enhanced": "2.2.0"}, "devDependencies": {"execa": "^0.8.0", "mocha": "^4.1.0", "globby": "^7.1.1", "rimraf": "^2.6.2", "tslint": "^5.8.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.0.0", "typescript": "^2.6.2", "@types/glob": "^5.0.34", "@types/node": "^9.3.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.46", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0", "@types/readdir-enhanced": "^2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob-2.0.1.tgz_1516551937889_0.9751090079080313", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "fast-glob", "version": "2.0.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.0.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "985f01d251ddb045f42af0ca59ade9b9bff87725", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.0.2.tgz", "integrity": "sha512-hPVN2WzIbhn+IZyTSLQsiPVUdqkyyfVg/bo6RGTCPvj/MXYMVqjqJHNF3UDp0ohg+dwwHKIIupm2rsuPwekajg==", "signatures": [{"sig": "MEYCIQDx7Whf7sykn/ecXPbFf7xBHvYylJJAwmp6tZQVYgVv4AIhAI3+QsXAkx9Nq1+m0vXHjIw4fU86VHlRwoa1q58A5Uu2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "985f01d251ddb045f42af0ca59ade9b9bff87725", "engines": {"node": ">=4.0.0"}, "gitHead": "0777426621826ab9e6e919640282462ec64cee2c", "scripts": {"lint": "tslint src/{,**/}*.ts -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "4.8.6", "dependencies": {"merge2": "1.2.1", "micromatch": "3.1.5", "glob-parent": "3.1.0", "readdir-enhanced": "github:mrmlnc/readdir-enhanced#ISSUE-11_monkey_fix"}, "devDependencies": {"execa": "^0.8.0", "mocha": "^4.1.0", "globby": "^7.1.1", "rimraf": "^2.6.2", "tslint": "^5.8.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.0.0", "typescript": "^2.6.2", "@types/glob": "^5.0.34", "@types/node": "^9.3.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.46", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0", "@types/readdir-enhanced": "^2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob-2.0.2.tgz_1517465455362_0.5497544764075428", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "fast-glob", "version": "2.0.3", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.0.3", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "25a5a6926ac89559a0ff600db21f9c51623b737f", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.0.3.tgz", "fileCount": 35, "integrity": "sha512-H50yGY6hTHyq+8jKULDQHuXnu9db4Z08L26AXJTP+MRHksijJ2Yfi8uddNtQm6oRccjivTVCHOrGyzmyPDhTKg==", "signatures": [{"sig": "MEUCIQDovz3l0nqcGm4n/m9KDHWSaRiIiSNolCv0m/uRa9jR/QIgIDP+4oAVQzjSZkeA9ZVcGb6gOyherpFh0aXzrxwkT14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50639}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "16befe373e975fe6bb6ba9d1ba1006640f31f18a", "scripts": {"lint": "tslint src/{,**/}*.ts -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"merge2": "1.2.1", "micromatch": "3.1.5", "glob-parent": "3.1.0", "readdir-enhanced": "github:mrmlnc/readdir-enhanced#ISSUE-11_monkey_fix"}, "_hasShrinkwrap": false, "devDependencies": {"execa": "^0.9.0", "mocha": "^5.0.0", "globby": "^7.1.1", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.0.1", "typescript": "^2.7.1", "@types/glob": "^5.0.35", "@types/node": "^9.4.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.48", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0", "@types/readdir-enhanced": "^2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.0.3_1518417306393_0.36487944578485676", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "fast-glob", "version": "2.0.4", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.0.4", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "a4b9f49e36175f5ef1a3456f580226a6e7abcc9e", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.0.4.tgz", "fileCount": 35, "integrity": "sha512-JAh0y6ScChRmATdQIsN416LK+bAFiGczD9A4zWBMPcTgkpj9SEOC7DEzpfbqoDKzieZw40dIAKx3PofGxukFqw==", "signatures": [{"sig": "MEQCIEVCm95iPuc3hls43vJ96UT6NqDKB4KuLagDfVOodFD3AiBUeaxletz4LKZJHDiPJtevpzD1o3GsRmgYuFJr7C6xJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50770}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "af7ba6f80a91a5433eb6b13c4505cd4c2e00e10f", "scripts": {"lint": "tslint src/{,**/}*.ts -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"merge2": "1.2.1", "micromatch": "3.1.5", "glob-parent": "3.1.0", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"execa": "^0.9.0", "mocha": "^5.0.0", "globby": "^8.0.0", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.0.1", "typescript": "^2.7.1", "@types/glob": "^5.0.35", "@types/node": "^9.4.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.48", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.0.4_1518550538458_0.28510458475421463", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "fast-glob", "version": "2.1.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.1.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "50ee95e6a112f09e886cfff5215de69ca4a6a861", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.1.0.tgz", "fileCount": 39, "integrity": "sha512-QSSKZwDHLznUXdVtWvsfdbojmYI5igtVwfVbKW/LwNsy0JdM1cZ5yyP1kl5npg2ddugdnOk66QlNhbJ1c1hErg==", "signatures": [{"sig": "MEUCIQD6Ay//9eEfu/gXSDjSllZutl4KS9OURPZRKMkzQ0NKDgIgdvY9vwTVzKhGlBO2JwyrcbQNcZNh30+3qa78rWGhGOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63781}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "1b49f50dc723dbe159e8e3896055c1ef4fb680c5", "scripts": {"lint": "tslint src/{,**/}*.ts -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"merge2": "^1.2.1", "is-glob": "^4.0.0", "micromatch": "^3.1.8", "glob-parent": "^3.1.0", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"execa": "^0.9.0", "mocha": "^5.0.0", "globby": "^8.0.0", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.0.1", "typescript": "^2.7.1", "@types/glob": "^5.0.35", "@types/node": "^9.4.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.48", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.1.0_1519575420512_0.8056921288996692", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "fast-glob", "version": "2.2.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "e9d032a69b86bef46fc03d935408f02fb211d9fc", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.0.tgz", "fileCount": 39, "integrity": "sha512-4F75PTznkNtSKs2pbhtBwRkw8sRwa7LfXx5XaQJOe4IQ6yTjceLDTwM5gj1s80R2t/5WeDC1gVfm3jLE+l39Tw==", "signatures": [{"sig": "MEQCIGcXLXvJX8g37GQhI9gLZqBXaF9fKW58cxfVm1XjqjrOAiAljLnivsVCAITS+8fWGlqxghln2RukpPIqCjHQnwO+Zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67241}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "3b4d9b2a8b9b14b907f8f4f64427303adc84eeb7", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"merge2": "^1.2.1", "is-glob": "^4.0.0", "micromatch": "^3.1.8", "glob-parent": "^3.1.0", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.9.0", "mocha": "^5.0.0", "globby": "^8.0.0", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.0.1", "easy-table": "^1.1.1", "typescript": "^2.7.1", "@types/glob": "^5.0.35", "@types/node": "^9.4.0", "glob-stream": "^6.1.0", "@types/execa": "^0.8.1", "@types/mocha": "^2.2.48", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "0.0.31", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.0_1520786275118_0.3917559938298083", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "fast-glob", "version": "2.2.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "686c2345be88f3741e174add0be6f2e5b6078889", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.1.tgz", "fileCount": 39, "integrity": "sha512-wSyW1TBK3ia5V+te0rGPXudeMHoUQW6O5Y9oATiaGhpENmEifPDlOdhpsnlj5HoG6ttIvGiY1DdCmI9X2xGMhg==", "signatures": [{"sig": "MEUCIQCllnk/scfWa9qSw5k6ljOZrsMGcVGAXbrxJuqRuNWCpgIgSQUwkqUfFL1SslbHsr+kY2sP5OABJ+83aYvmMaMyAJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3MpqCRA9TVsSAnZWagAAXIkP/itB/K23gebX8lso6Jvk\nP6532/PXIvELZswplrFmOu89Bs1dyW0Q0VhMbbxdrONSWeD3nQ6jg7kMMvqE\nZQrbxsOpHkqa6T1V1RGQSLcsvois8RlUhU1fM3Ii6/HYCwLlY5JK+Mzs2Wn+\nqqGjoBATPVepuxyQ7K85hbpC5ifJxRhWcNRTAm/QAS/PFNMVGxfvinoWO18t\neMFJImquXJOvcOKy9hp9rCAu/3e5lpS62AkEAAUg8DYvTyzzd8syMLlQvgSX\nuhFtHT2QIef9+xj/oZ9OJGsN/IMKYja4x4nMT6i2BoeRcNlk9mlhoJWpUN96\nTXNuAzJLd5P3omHnQpbiCqNemZaw2JHZ+VULD1zJ7Y3fmithCO3ejZeefdhV\ny29FyHgMFeybZ0nTL3Qk00/tNN9QYjk2TWfFOT+lykKL1ETLu2nD1yXgMJjq\ndM3fB/B27z7YxBELC9Yc5NstM7RJFf0m+JKvJAL59aVDy5gqMusO9eDsJo9a\nUmFbB9bF23qk8tvT4bxNV/+HrEZzmo3AI7mQGbxlmAG9MyenOL/u36eFFbvD\n/HXiHNYU3rZhaKpL7KujbQHHPAmpo0mfSXshV2qhnLZ/b2padQnFrkeQCZdZ\n4nNFXdbL/LjKzsIeUp9EMfJJ5YfxBoInyzIYJj5Z4oClX2JphVhonnSmRPdo\niSaW\r\n=u5G7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "fdc9c54e15b7ee6ccb2b8f28a801200589fbdff7", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"merge2": "^1.2.1", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.10.0", "mocha": "^5.1.1", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "easy-table": "^1.1.1", "typescript": "^2.8.3", "@types/glob": "^5.0.35", "@types/node": "^9.6.6", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.0", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "0.0.31", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.1_1524419177611_0.7128690700359319", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "fast-glob", "version": "2.2.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "71723338ac9b4e0e2fff1d6748a2a13d5ed352bf", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.2.tgz", "fileCount": 39, "integrity": "sha512-TR6zxCKftDQnUAPvkrCWdBgDq/gbqx8A3ApnBrR5rMvpp6+KMJI0Igw7fkWPgeVK0uhRXTXdvO3O+YP0CaUX2g==", "signatures": [{"sig": "MEQCIBtdJfXpWadcLUCcePBkdNDVupuf1G/USstPZAt2dsryAiBxhN1V0bXDE8A+uoOHyKA0vR490EEgxplCrXweb3eo0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+zq1CRA9TVsSAnZWagAAJW8P/i0PuU6V4envjgUXDU4D\n2SVxwKWeKuybODwFA7q0wX4DM3RRchPejZwGx9mt9lBjz7BSYYf1ZVak+csK\nNHz8jR8rELl5g6bsvjJSS+h0/yizti9LDF52KF6YOTp+MF1FmH/k+bkE109K\nx4CibiJo4DHP6HBm3rTLL3mOyNlq7iHVkPftpjx1ZeT//2KD+oPTqp0Y0PsW\n2bOc0Cy2zhV/OW4o7c3nnKRY/iXYU/eizxLfCWuywFHMPMn/RXsNB2Jnxvmo\narqiLPZQyMoMcTGfuVvwhlyA6/SkkOm+sdMav2T+mSwPie2RnLGlE8net3W+\nC9XP5t3OpOFCZKJkhDS2e+5W6f/yPuW8JdTWi/vAa5yq4KG5jKUtrebhmslS\nFk5M7PhLYM8bA0jT/EB4lMwAEibPdTYetwzXTcDyXAJJnCldSoSIV77YCFoW\nJxm3AI/1lac0/bEX2noo3eclvwD0vMrFObij9XAiJNY+e5xYY/Jf0ZWr6t5c\n553EPfXLSmgttqM9YEsTwj3QghIWMQJE7BggaIb4da54Mg1JTctkoQ7kRFNo\neNF/46vH28tuiGzuez/JV82Zv/pxnfFebUh/AZ7nxUP3VnEcE+w4uqDlUXB7\n2B4mP5ZZq1uo23KnpVXCZ/4ssQiY9tM1qVMt5nC2ZPSZSjl7F9ToKFlkZYlk\nZlsV\r\n=HXwS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "8d340033ffc557ab6a38dd702a68a0cc9ab0aea3", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"merge2": "^1.2.1", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@nodelib/fs.stat": "^1.0.1", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.10.0", "mocha": "^5.1.1", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "easy-table": "^1.1.1", "typescript": "^2.8.3", "@types/glob": "^5.0.35", "@types/node": "^9.6.6", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.0", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "0.0.31", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.2_1526414004029_0.20552151849582811", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "fast-glob", "version": "2.2.3", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.3", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "d09d378e9ef6b0076a0fa1ba7519d9d4d9699c28", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.3.tgz", "fileCount": 39, "integrity": "sha512-NiX+JXjnx43RzvVFwRWfPKo4U+1BrK5pJPsHQdKMlLoFHrrGktXglQhHliSihWAq+m1z6fHk3uwGHrtRbS9vLA==", "signatures": [{"sig": "MEQCIH+ZMU9zV3tj76kBhBZoSYM3+/9xfdGi+Eh3auPdn2/pAiBFswFuP4mPMEzs0NrVJZx9rejgrsqnuyJskVGSl8a2Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsy2HCRA9TVsSAnZWagAArCIP/0DX+zFnhJAOaGBWp7L1\nNwHcUkKrV7BT28pS0602sZVvv2DwZ90rpp498mkne5f8JC8LIwl3uiQfsxmz\nEU/tWEAajehDyU0flYs34cD7VvMMnDrpzi5kfEcRRY4uTJFSaprHYJw0e3zL\ni6Ud9nyQXWswDXRP/zElXIbTRS7XQHQQ6C03dXc2U/cqZ7lt0fi25HyCCxRu\n5vVLZWuZpiDDWwHzUZ07TXVQW2oqTkCUaBrkd2kks2YbFeZufZWp2ozJa3VK\nKU8V2Ecz18sBDEH3Qmey5wdIYy/NvgABBsibdXHD2bPAMLu7BgaERVH9YoGh\nqBAhPnX1TRWGmZkY1eHwDhF+vSM4sEtsX/uVeRIQlRzkfOTzHD7ewfwPjiXA\nHk0ovt0EIMNs6Icb0QndpCIWws8cdjGnEhvCFpX0kMHd4IPWOUw8cZXvKXQe\nwcMwqigzgKt+kC3BL/j1t9kduEoYSmu6WMCRrvniDE0koKt/SoeaN/KByNLM\nLwpo5J4nZwuGlBKmzeIvcGE7cOTUnqei2oq6IQaKmIdIomMjSR92rl7f2kU+\nThQFzfhBMjaLobxwPtojLKMjL+Krz9iT+q6i4kTtSWuycsZZgMgQ1HwO8T3r\nSxwg53UP/oHcNdi8UKAqHxj+gclkvKIoyuqtan1f1NtdD21tjzuPi7Tdr9Np\nW/Y0\r\n=vghg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "cbca9143570de97d5d3874edddb8d01b7e2a61e4", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"merge2": "^1.2.1", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@nodelib/fs.stat": "^1.0.1", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.10.0", "mocha": "^5.1.1", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.9.1", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "easy-table": "^1.1.1", "typescript": "^2.8.3", "@types/glob": "^5.0.35", "@types/node": "^9.6.6", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.0", "@types/globby": "^6.1.0", "@types/merge2": "^1.1.4", "@types/rimraf": "2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "0.0.31", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.3_1538469255285_0.7607298772969651", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "fast-glob", "version": "2.2.4", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.4", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "e54f4b66d378040e0e4d6a68ec36bbc5b04363c0", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.4.tgz", "fileCount": 39, "integrity": "sha512-FjK2nCGI/McyzgNtTESqaWP3trPvHyRyoyY70hxjc3oKPNmDe8taohLZpoVKoUjW85tbU5txaYUZCNtVzygl1g==", "signatures": [{"sig": "MEQCIA4LokOPaWEmqMDilX62VD0AldkeE/90W14J4V48KO5QAiBsSb7hFuB9TMy0WrknQE6zrqdLJUeqnqadg15K9UYsyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6EFTCRA9TVsSAnZWagAAv1AP/AoG1AFSwIMYophFnyq0\nP9eFer8Dt2V/iNN6w7ClcWXXcZoHxzLEsKw9ixzo2v2hzfclGGpFeXNytxEX\njZiw1Pud1UtAcgwwgxY6sj91emyJyppaUV4NMImAiqDdlUlyUeJZ8thyDH+j\nVufDh/na9QE2MrZyyt0LBaU41AyhoXiimz2opww0N8QD40Xm4GVtSDQ804ye\nYK+9FUGGlMl6DU74lh14njLJVxUxj5FIeJLfDHSA86p49eaLQcZX0jc+cS/9\n7+nHH6N+cuAiTQcV1FHvy/KO/fbz0/UYSAqRZYP3TWnHoa2MqoQtFl7Q/ZCE\nNSmnpE+BtPcgb0CT5TW9roDi/rTGGwT3elAgN+gJPNyMw76MLTU2yzRzQwpK\nr+R3U3p8YCmPCrMMdezgHttxcen+khuTuS+9rBrnaLiuo/UHKP5ggvGk+m9Y\nLE9E2+ys8UWV97mzw2hMDBntHLvtpZEJEV4mf+12scMNIJM6YZyOMq2VYORd\n3E4IECcM661dsLzDQ9pQGuQZvzwPl+CraDObeEbpIbaohtjVY9IDQSKo4d0p\nm6ch/cHL7EDOMUDCmtrR3gW46igs8BlfeDaqEbsat4cm44/yCR2Tz5mNutll\n1PMy6wlSwq1a5sKqja+ewr4iUJu3UFGL4MISVknyipIVXdHwDM3uCmMPgo9I\n6Yf9\r\n=TNqB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "cb8ca84fdc4588f03085a214c39dd0b051d03708", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "11.0.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@nodelib/fs.stat": "^1.1.2", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.9.0", "mocha": "^5.2.0", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.11.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.3", "easy-table": "^1.1.1", "typescript": "^3.1.3", "@types/glob": "^7.1.1", "@types/node": "^10.12.0", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.5", "@types/globby": "^8.0.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.4_1541947730946_0.22471125065426256", "host": "s3://npm-registry-packages"}}, "2.2.5": {"name": "fast-glob", "version": "2.2.5", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.5", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "e03dd265921cfb7a11a896048ab9bb818a944d50", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.5.tgz", "fileCount": 41, "integrity": "sha512-8E7F/dTYpMFiZpz8U9h87AMDaAWkkLrNzxAcolwwvlOURzFqeRS0XaiKeUEUcH1kWWPTXGPALq/ue7652YygzA==", "signatures": [{"sig": "MEUCIDbEEMEUXAQDjM3yUU5fkjFaPIlqAcu2aaSl+VC0XuasAiEAuKBFYdS0+szzaw7BFLtgtdcGo4o6EmJJLqVOFrDqAYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcM53wCRA9TVsSAnZWagAAGXMP/jbqsJ5xc9v+EX3MNsiF\nCyNvXdOT4MohTUU7UEautmqcGtY+fQXk9wl2UIr0ZfhQaecNmOvflMRNpx1O\nYBuGNjqvt4jed6lHCmczEI2T4y0P6+uf6GUOp/Oe7wzipqq2maWlcgptRKZ7\nJvZnGWMTjVEH+10lOL8fsITHwFj6GezTQ3XNhlcWv4aIDb7Vvlu22ePY8Y/P\ncrdZ0Z/AiTovCk8ESUWDOUVcRpIvz9C7qM/qFeWtB3JZ5j7X9QpeSLmEX7WI\na1vHkzjRolsDkghMV9YJNxWQ6rkxkFNhWU0x5qhQuOHstHZ3q3c7WT7t9cAZ\nmSs/6lux11imLprkkVtIIQLbU4qL12UIilSPk+watLjvMmcDGhgNAiHbrcTb\nS09boDRh9jag6dwYRpQio61aMVKyhwhfFYqNke9fDJwzodP51oiRtHcNfQjx\nDgVAbKzDcWY5ciRIdwJndvCFs6+CpjKcFA+QhLTs1uNrtZfTAys0wI8XtvLq\nnNaEIcM5GRPNBqkHjpkkNLNyKcexCg+QMoVlbZdeB9btpjcA9+wPos15QO0t\nqvaJvz0Ts8AjzB5TL+DNVGDj19Z4H0Kv8hQJiTtaIPN6D+8bSwJHZHa6qwht\n4t2EhwCmt3QhIibYaKQDMva7IHFJIC3CKQqkRDC7o2I8AvUkInpoYKgPcDhV\n5shw\r\n=byHA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "dc656edf5d9ab18ba286ee620b6067448c6c2f3d", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.5.0-next.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "11.6.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@nodelib/fs.stat": "^1.1.2", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.9.0", "mocha": "^5.2.0", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.12.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.3", "easy-table": "^1.1.1", "typescript": "^3.1.3", "@types/glob": "^7.1.1", "@types/node": "^10.12.0", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.5", "@types/globby": "^8.0.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.5_1546886640434_0.43870107938859526", "host": "s3://npm-registry-packages"}}, "2.2.6": {"name": "fast-glob", "version": "2.2.6", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.6", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "a5d5b697ec8deda468d85a74035290a025a95295", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.6.tgz", "fileCount": 41, "integrity": "sha512-0BvMaZc1k9F+MeWWMe8pL6YltFzZYcJsYU7D4JyDA6PAczaXvxqQQ/z+mDF7/4Mw01DeUc+i3CTKajnkANkV4w==", "signatures": [{"sig": "MEUCICwD0EUQANJ37qlTaX6vE8vCrjYyJCayei88gKAbErpxAiEAwLuDXolNcuT6Z+bQMa2XU2RHR3RQ5bQQWGHfK1HFLH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcM7qZCRA9TVsSAnZWagAAAucP/2xScsxgg+s24D93QSfq\nyFAlOcZ57tKRJwUDIoWbIFsUljMeNAP3QCyTuzcp/HZClsfSFb0WUaIsU7I1\nNiUIZpn5t298/wd4S7BLO9Oto4nTy6TPHvCRGeEEc9kU/H52QsydcPSDE3MH\nzfXDNpN65BFh0I9vF5Lch9QbQoyyLC2Vrwly59z+m3q/WfAHpj1+IV1cuCIW\nnWCjUZciP0AiXt8PXCb7y46QZq95d2SIX2zFBS4R0FSKxwuUsZ+wh//f/3QB\nQ4xh8AnLoqSxylAk2wVwQ5a9ogQecpD95xlpof/rpQ4Tz+ka8hSqvLnM6wEd\nCidrlM+ctHhDuo0CCo9XZsLtqVc9s4b2NXOGsI1GaC2fw3zibYC6CoElTEmn\nE5Bsjkb3QITHKER6TguxJ/Cz03AIBWr+QFAfiy2GUxd7YwaucuzzznLQSpyd\nmt/YBNPRY6/6K9jI/85j8nZG76qCV4OY7N2chXTfokP+dKC/LDv9l1WGtdkk\ndQNTpil4IygRLn1FNbdShLM/xJdHox/p9zfJCWUDEpaDGAOMBtxHIaIXkc5k\nssA66FYdDUwoWusYUwhvyzMbivNeBq1fdiA2QOnhr/JM3c58wHT0wp56p6XG\nAd31xhpbORKPtbJ00c0HBSP5dqEo3OxeKkZ63lDAg0Dd5IUYyuuU6EenugaQ\ncF9L\r\n=JO3G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "b40a6a0e5d7f661be3e4046a0b40e556306546af", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run lint & npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.5.0-next.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "11.6.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@nodelib/fs.stat": "^1.1.2", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.9.0", "mocha": "^5.2.0", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.11.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.3", "easy-table": "^1.1.1", "typescript": "^3.1.3", "@types/glob": "^7.1.1", "@types/node": "^10.12.0", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.5", "@types/globby": "^8.0.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.6_1546893977280_0.2942800272188524", "host": "s3://npm-registry-packages"}}, "2.2.7": {"name": "fast-glob", "version": "2.2.7", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@2.2.7", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "6953857c3afa475fff92ee6015d52da70a4cd39d", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.7.tgz", "fileCount": 77, "integrity": "sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw==", "signatures": [{"sig": "MEUCIQD3SJreVsI7n84TeRlU7kSooNp1TuBXceLjffDTok+FhgIgUySCUmoM7VQOhhA9jV7fFlQpJCt2j9MBEdC8TGZhw/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3+waCRA9TVsSAnZWagAAPuYP+wbfhZjx6Y53+ALny5r1\n/ksRdMHM1H8Ca/3wLP4lyeHMXyhuZofCF2eugJyREWMRh1TvNvMQZHW9e9jf\nYEgNSqJrHR5Mq34i1cWim27pcBhrgmCc2tX10njERpAhSAR+nTYKysN/Gu69\nJ5E0ue35uwyxEHjC4SzszIgMjLIuwvUy5GZvrgKhXv+vUIJVrLleN9yRodgj\nZ8Zl7CbzfEAWPl3quDWRvTO8y835ClhlSPMMQxLVmMpDJOU9SX8Wsjly+26S\n78r8D9lhzC5Ij7frDbqXQOxmOgFVb183mB2h9dGsxqcIkxkHfooYuzgdRQCj\nmOyGTV7MAnWZGRbYS4YhXJTWyI4xCwK8NWsFo+2wLMKT9DL1t7O+tW48PxnC\ncqt5j1LlB8OLSbRGdiwtf6Js355awaaA5Ty4dgiJeVSz3CkvWsmFRc/kc2Ma\ndo+ObenX15XWW5hctrYFLbarcpS/XBdCCpjZyO8p9MxLziMvexTNnYFhPC/Z\n7hDMankmpHIF0+jzELSyKlsUWxos1TYRsRqHrFllK4Z7S60GpujHXpXG20BD\nzbP/h6V70+ggsK1w3vZBGVKeRiesPC7eNJs8xxTK7LhagdRq6LehXcTxzRlZ\nv+PVPG3TEZ2gLpBp7UI0TkvdbyCV2ajiNfleQ6Z3spm53a1FUAVnCG4L5WIL\nUOlW\r\n=lgYZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "85e42c33f8348ff1e544e03a0dd122a3a37cfe8b", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run build && npm run bench-async && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-async-100": "node ./out/benchmark --depth 100"}, "typings": "index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Is a faster `node-glob` alternative", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.0", "micromatch": "^3.1.10", "glob-parent": "^3.1.0", "@nodelib/fs.stat": "^1.1.2", "@mrmlnc/readdir-enhanced": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^0.9.0", "mocha": "^5.2.0", "globby": "^8.0.1", "rimraf": "^2.6.2", "tslint": "^5.12.0", "minimist": "^1.2.0", "bash-glob": "^2.0.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.3", "easy-table": "^1.1.1", "typescript": "^3.1.3", "@types/glob": "^7.1.1", "@types/node": "^11.13.5", "glob-stream": "^6.1.0", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.5", "@types/globby": "^8.0.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.0", "@types/minimist": "^1.2.0", "@types/bash-glob": "^2.0.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_2.2.7_1558178841646_0.5740778491596477", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "fast-glob", "version": "3.0.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.0.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "719bc921f2c37322affd3b6fbfcfbb135e7fc198", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.0.0.tgz", "fileCount": 47, "integrity": "sha512-zyUoA7qfL6sIWD7FYyleqjsOZ4BbpILPrGVKM3inQV4lJ71kOqFHNX7G95PX4iVMojol01LXJ6gQW/usDLNqnQ==", "signatures": [{"sig": "MEYCIQCRVg1p51fqSamfNWpkCs1YcoXK12kPK/QqutltD6lNKAIhAOOvy3/416T9OPs1kh6ab0DXKWbGTbzhAzYdxNp0W6qH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBobFCRA9TVsSAnZWagAA+VkQAIBY7cffcvsbzTgykMYp\nRIF0eIsL2YiTyswmWsMk58hHMl17RT7AJaJyPpS7LPtjLKB6Qjo2beZJeJd5\n9peaF/ix2+sJWSzp3VjEd8JXfM1QRBu9Dj0MR3sSpkEYgo9Y8TqVvWI7Uqf9\nk6UaJdCztWG7OhqQSnAGCbepmO75TnH7+4nfaTwN4knNs/Oig3peAB0h0azr\nG2mEzPHa1WhsOwFQCVE6rbypaJDmlVU7/4R5XmMlSVhst+4/Xy61TJ6Lcjp8\nx3tcMZdAB6Fy+hcwHsvry4H3pQgA3ishuZljysBnLlWtsslQWMI8cHmu6QAu\nr4EGfLMTaZIJ/PKJGzXrNk7xwCdxwtZ+5F62rqrbPABinlcV6n4P6wxhn7v1\nALNpWMbaWW7ndoiGzX940SmLxdeyepLGtO4v9bTtv6Ul7KZ0Gz//2DfX8h16\nMglQSEpLe30y+7DMiOECmuohLSneHNyBSrAp61h3RkgeX5yepv5+UQFCZR+R\nzh98XSd3JXdhck8H9SYntFhbwibnXYlvnLwo7TAyIX5V6H0H2YeyUp/T9S65\nA8Kaxyzw7AOGbjXQEWOf4Mi1WSPqvmzh+w/QGPQjU8Q8+0Awx73zxjzOvq0u\ne9O+cpNCRY8d8tHJAlhc8FjxaZBYplHXGhff+FZJiENlYbPpEZ2+h6Pb0VYf\nNIoV\r\n=ozaQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "03201edcb056c91f81cd356e7983dab254e3ca14", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "bench-sync-deep": "node ./out/benchmark --type sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --type async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --type stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --type sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --type async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --type stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.0", "micromatch": "^4.0.2", "glob-parent": "^5.0.0", "@nodelib/fs.stat": "^2.0.0", "@nodelib/fs.walk": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^1.0.0", "mocha": "^6.1.4", "sinon": "^7.3.2", "rimraf": "^2.6.2", "tslint": "^5.16.0", "minimist": "^1.2.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.3", "easy-table": "^1.1.1", "typescript": "^3.4.4", "@types/glob": "^7.1.1", "@types/node": "^11.13.6", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.5", "@types/sinon": "^7.0.12", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.1", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.1", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.1.0", "@nodelib/fs.macchiato": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.0.0_1560708803957_0.10084597892021696", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "fast-glob", "version": "3.0.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.0.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "71001b8da4a60211aaad2dc3a14fa262f9fe3696", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.0.1.tgz", "fileCount": 47, "integrity": "sha512-JEHYHd9wWKgv5nyUTahP+Wv/F3eOTCFMyCdICKAnxxDRd3rbg3opIzhs6qOOHOvP2hS6jhzPwb/I9sfWCqK84g==", "signatures": [{"sig": "MEQCIDNpb+qJlFt72LMNMmqjlwK+CIkD4SeCWGTrPGh/gy4QAiBRCC/jgKED16BKCsxFqLpzMP4R2j4zwpsu0zr0TUOS9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdB98eCRA9TVsSAnZWagAA8CMP/3KrlMZXHB73OtzUpOix\nwQSYuygo64pSzf4gtZSMy8UNjZQIMu9uwcQWjxeV9pCSHUTQ9ht052QKQ0tZ\nNbOzvBdGKeFoPX1/D3bTqNlX2j4S5A2Qpo9JwipoGrdYjItg+MXsd/5zak7W\n6xqSBhltt2Or9hOIH56B7tfeNTnGvmDlY4kWq/gfvJ3XD6AKvzqKU9pXof6F\nlLwjFC9XLmhn78VbWyHeWJ8Gu1xSQKDQsVxDvJvMvT/tdZzMij1nL/fheGiG\n1C/Wyth29r5B48BQuwm9oV6FiNSJtH29NlAIe1m+j8uSG/3uV24S1ms3rdZX\nyRxCyIIbF8BJnFdQghfMf1l4rONYDZUZTtmADisLv/NJBzXq/2YP2gEw0NTE\nX8WzCJx+2/eBwUrQIIg/AuLdK4mpG3yYojIvvtOhKDfCJeRG2hw48tXGcCl7\n4LEeqpr0jCQoa6RF5P4s65yI9DuNU14SJEehVh/oBOoJyJac0BZ/hrKxbVqK\n+gpdRLezUEl1rcUIkJBwluAaJ17fTg6Gu7IwWUlfXn5g1H8IDCiU3uXUiwU5\nkVtiuh6cJxZMDCir7dOWgASDtnCJKEL3QVjKweTntEJcIX816SHnJ+d9BOm1\nAVsCk2P/2kS6LPHLzjWXK0HSGIR/F3CtCYDFm9ukrJpVRxkeH5uJEHCBX2RB\nneE4\r\n=09C4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "106bd9f4eb81cdd911ddbc490d802696fc818163", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "bench-sync-deep": "node ./out/benchmark --type sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --type async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --type stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --type sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --type async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --type stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.0", "micromatch": "^4.0.2", "glob-parent": "^5.0.0", "@nodelib/fs.stat": "^2.0.0", "@nodelib/fs.walk": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.2", "execa": "^1.0.0", "mocha": "^6.1.4", "sinon": "^7.3.2", "rimraf": "^2.6.2", "tslint": "^5.16.0", "minimist": "^1.2.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.3", "easy-table": "^1.1.1", "typescript": "^3.4.4", "@types/glob": "^7.1.1", "@types/node": "^11.13.6", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.5", "@types/sinon": "^7.0.12", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.1", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.1", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.1.0", "@nodelib/fs.macchiato": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.0.1_1560796958101_0.1404026697903138", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "fast-glob", "version": "3.0.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.0.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "cab7aea5cb438657bbdbb118a33e6921a78f0d7f", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.0.2.tgz", "fileCount": 47, "integrity": "sha512-L5rSdcOWQssiO7D1CWPwL9CT1ZG90PGhYQEfKOL3OC95ChQDA0S6AuP7Zi9dhaOaUihUXZ2oUlW2xeisF0nAaQ==", "signatures": [{"sig": "MEYCIQDayoBcxPsP1soxcvgZ9CI/V43uFpTY1z5PeygpYm4BNAIhAKX7G99BrO1RMAfA5JjSZJ2819e65mv+lemY4F1HcPWb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdD4ARCRA9TVsSAnZWagAAVA4P/07tmFXygBsK1gHBsFZO\nmxB9zF08d4JO8/tIGKrw5joKurzMP7w715pIFvhhi9/KPSTNKrb4pptAhbBd\nEGNnYAzQhYWa5XUrwXK0INocXxQKZTVTnZSWqbSmoI8Takv5/Pm/Qu1DNEFc\nKhtDE7CHBFbKIXy6FLFab53t34iSDFMLhGE02OskVdlZQuTNo5EurlNFtrSn\n/MvU14pMTFPFHOxa5qqtfr8hh4O9gRM+U/yElYV30qzAwW7Dl9TB0zmjYaov\n9W83hh44fLs1nGxzuuysQuODcHIW0ohbzLil2Ml11oAq+OjSco0zChZ/plQ+\nVXA2tT7jihRD8MFeDctXML23Omnm6GDP92UCAwTueU61wFIEdLALVd3Hduut\nn5Vh1Yp+mVba0KS2GTRC6ewqbXplh58xg8qOvp3FLFwnaDGoF616kwQLcAsr\nkfetpm+EAAqhqXslF3SO/fbjWCF4zFvYKfkFYHBKXdtCB08MhOqrOfMTccXa\nnybY2rrhnfhieesf1EOhZ8feCHH07T5htvVBTqBm3rtykUxvcpuk3J8cBoOf\nIe5xV6CizKKTCBPHzKHGe/cM4dN6WdDYcOzdImCtRoJVDSs1tnyyKreqs/rB\nRPPewTLTBbzeAe1Rz0QfRuTi868wtaH58wazaYBYFOIdPE6nb5cEVyZ55530\nbIWU\r\n=qu/+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "8867cbfe4f3ca7dfab6f1f0514f64b59abe9ab11", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "bench-sync-deep": "node ./out/benchmark --type sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --type async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --type stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --type sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --type async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --type stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.1", "micromatch": "^4.0.2", "glob-parent": "^5.0.0", "@nodelib/fs.stat": "^2.0.1", "@nodelib/fs.walk": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^1.0.0", "mocha": "^6.1.4", "sinon": "^7.3.2", "rimraf": "^2.6.3", "tslint": "^5.18.0", "minimist": "^1.2.0", "fast-glob": "^2.2.0", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.5.2", "@types/glob": "^7.1.1", "@types/node": "^12.0.10", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.0.13", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.1", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.1", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.1.0", "@nodelib/fs.macchiato": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.0.2_1561296912183_0.8818351181185591", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "fast-glob", "version": "3.0.3", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.0.3", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "084221f4225d51553bccd5ff4afc17aafa869412", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.0.3.tgz", "fileCount": 47, "integrity": "sha512-scDJbDhN+6S4ELXzzN96Fqm5y1CMRn+Io3C4Go+n/gUKP+LW26Wma6IxLSsX2eAMBUOFmyHKDBrUSuoHsycQ5A==", "signatures": [{"sig": "MEUCIQDW9Tw9h7AZwHMi5wi8nRRgm6nzru3AlaG0WMCcxKCDLAIgIi6HTuZ4pyDLTq8xCMUvKEwORaxee4zADvx+0Dfn6oc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdFRLPCRA9TVsSAnZWagAAaIgP+wYe/X6EalsA4hbUGjh6\nuE97PTUDrPl1iIAQ3vwFiZEPFAMd0komokXBuhulwTInpkGsAkhdIuJhRH+Z\nSUEEcFyasLPUyFK7/wNZvH/atVEkrcJzYDlMUYi4A6EX+QhTnmePb7IxO8Qx\nAF+4Mgrdyx7lh5qt3nlrBreLEFYvZvRlgQwphbSZLQFc5qY4hq6NNi7U+znU\n5cbys0rzrdgfstRr+IXCMmKy5UfM8zkEIRaUfUKrEakQP++9Uauw7OELGAlY\nFP4Nb7kYtSWbs77eqibuliwb0EARLJRHx1nQEL+wuqbgMmynblLxV0k0BOvU\npf8On7trL5CFl1l0zqcBsrl7o+NOZFtpt49SKxzCzCz+HE5ItjigUbuAi7OH\nWOVNoyk9qmS+PMgbHBmWIQ6WoyUWXzugx5VQ7FBoE7lHRoPu6m5AWo4cylex\nCONzn1ngdsSJKRt4QoMZr1S/93BA8DKyL7GsyNJa0Vs1p5ciBzbvTGwjQQIf\no1YAzvFO9PSRWZs5RKxXo2DYn2L3c8muTv9jX4eNKLtGk0QwZ+fmHpwz7n+y\nG7dYLgmCDDdSkCPHOHJyf6E/MZeTsGDbSWF3CW2jLCjjfzY4VFY4PUX7y3wX\nvz7WKemcY9d4h4yNMNc60uPA+omV/yP2EejMnFzd59YsR/zg5QAdlNchCgMu\nyFr9\r\n=lOpW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "57bb5da411cc1649e5fb91480430373931726e7c", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.1", "micromatch": "^4.0.2", "glob-parent": "^5.0.0", "@nodelib/fs.stat": "^2.0.1", "@nodelib/fs.walk": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^1.0.0", "mocha": "^6.1.4", "sinon": "^7.3.2", "rimraf": "^2.6.3", "tslint": "^5.18.0", "minimist": "^1.2.0", "fast-glob": "^3.0.2", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.5.2", "@types/glob": "^7.1.1", "@types/node": "^12.0.10", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.0.13", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.1", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.1", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.1.0", "@nodelib/fs.macchiato": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.0.3_1561662158821_0.4303699248303967", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "fast-glob", "version": "3.0.4", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.0.4", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "d484a41005cb6faeb399b951fd1bd70ddaebb602", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.0.4.tgz", "fileCount": 47, "integrity": "sha512-wkIbV6qg37xTJwqSsdnIphL1e+LaGz4AIQqr00mIubMaEhv1/HEmJ0uuCGZRNRUkZZmOB5mJKO0ZUTVq+SxMQg==", "signatures": [{"sig": "MEUCIAK5R2UpewbD4m6Y0kb3QXciJpC9HDz+a/tPI07v7lBeAiEAnmKzEW2a1MBuwCjKYs1C9M6pvDNZH7LJU4bysmE/9Lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH6HGCRA9TVsSAnZWagAAgu0P/0qZzF25326uWSUGVyfO\n8pcyGuhhltN2s1aLid0gOzUdMZv5Ekg0e5kVZIGS8c6v17a4TxDAV59QfIhQ\nfwa65rGttbrPrn4VZqAbwpnYef/8jXUF9tUnaN9MQEx1lVpj9yeaRooGIc5B\nJNUjtOja+nhtK+u6lKiNkNoIYIEOJ19IKNCu0RBXEnc7RpOPcPnZYkSafuFF\n3NioVgDbvMbmmRYvKutT/lY6SAGjHlTB4IyicYHhQY6CP28aIpME7YeYXf8/\nsIyNj7gVEY+VEtn/Ep54o/7GZQ2vP3Nqs9BofbhJ91S4YOIceghYECG5Zx9k\nwXyoLypkj7JDUQJVUX/QxCFM8U5WBsDBFnivgTiB6XVsX7FcAbPt3UfCCU4G\nuxqEo+PeRkzybbWIxfDe6SRA280jClDQzm1s68Ijp3OaB91PaoIzdF9KF+tm\n0muOK6WPfQRVERO6VO5ExvkOEPoBsMztADPqx+vqXQdLHsp7myO2B6lOEVhf\nupl0EyzXIhhuY4p67D5BY9Z58aOP4k/vRhzm1jvidi27rEoWavsndW6HXpPB\nnMLx3OwwOsk3Pn50gxEItGhnNYS2biK2ErYJn8WTu9foGCNSVbQ/Vtqn5r99\n4KQKaYKab1apS0DuH98tM81ywS/xxi09ZXwFN1Q18q8AL+t8aweZdCDahgXL\nLDGC\r\n=QbiU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "78269e081ed5deb4f89d93b17a639005eaa3291e", "scripts": {"lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"merge2": "^1.2.3", "is-glob": "^4.0.1", "micromatch": "^4.0.2", "glob-parent": "^5.0.0", "@nodelib/fs.stat": "^2.0.1", "@nodelib/fs.walk": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^1.0.0", "mocha": "^6.1.4", "sinon": "^7.3.2", "rimraf": "^2.6.3", "tslint": "^5.18.0", "minimist": "^1.2.0", "fast-glob": "^3.0.2", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.5.2", "@types/glob": "^7.1.1", "@types/node": "^12.0.10", "@types/execa": "^0.9.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.0.13", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/is-glob": "^4.0.1", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^3.1.1", "@types/compute-stdev": "^1.0.0", "tslint-config-mrmlnc": "^2.1.0", "@nodelib/fs.macchiato": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.0.4_1562354117506_0.8612783328999367", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "fast-glob", "version": "3.1.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "canonium.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.1.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "77375a7e3e6f6fc9b18f061cddd28b8d1eec75ae", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.1.0.tgz", "fileCount": 47, "integrity": "sha512-TrUz3THiq2Vy3bjfQUB2wNyPdGBeGmdjbzzBLhfHN4YFurYptCKwGq/TfiRavbGywFRzY6U2CdmQ1zmsY5yYaw==", "signatures": [{"sig": "MEUCIQDVdpoIXOcVGgMClMmb9bhw1E1Uz1cDLn5gwXUL5BydUwIgZ2QEZHmIBdoW+P8JXTbQHgDQZJsazjKDwcw65wjLHhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmbuLCRA9TVsSAnZWagAA1mMQAJWOe4/BJr4QQMTf3NZ9\nI982Il8TK+jWzj0tSYD50zlgKfPyIyLO9NL3TtFWcmNNgX9DaTT4vDiF/ymt\n/cP/g7brQHm2sOK1tyFtmAmnwWFBc7CVd8zrjIfEhiOEVRBpXaTVUAVcQaZq\nlIqQNIMYbDe3SVGNjtZoCFerPsdsR/ijq0b+E9vv8G+jFOHzbh/+UgpQTk8j\nMhcE9gN2+KvkzlD7MZtbnzapwYNBQkC2o9jtKdMkhzCDnBYYXTao/ZNge+vl\nR1LMnU0yuSgHpR8nsL7FiSNAEH9KuCGY0TgCH47gPxZRbh5JquSfQhl/Rt5f\ntJUQluONoETwfGnFY1tHhk/MMm4u0fMWKnfE8CwKMdMqfjguImQCsIZdK/82\nm5VrRE57zDjwU/Bmu5y2/wnENjMGa7vuGe6klefrrAwSKBWXrm/NBu5WqvMt\n8Fp+Zn7BhvZE7E/aa3sdMwpkoBCwgiZ1f9BdQeNk9aPlcDCnmL767PggaAHd\nwICJDsiCs5uFLdGBeLq6T90ErCcmV1y1jl+32VZ1KL8LJwHeCb/1ifuz0bmz\nfW40PRv3RE6fSr3kiabvoW5j+wdD6+SA3Se8fxPNMB0us/7c48rDJwVPZMkc\nm4eRaYDKzyay32m7CXWyz6Bzvw8gvaOf1xuHCXoqgXdxiZEOxwDutE7zbmSd\n6SMJ\r\n=5KKw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "24a29c5c582966a95a9a8cca89b154d101434305", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.0.1", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.1.0_1570356106682_0.7037919829887824", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "fast-glob", "version": "3.1.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.1.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "87ee30e9e9f3eb40d6f254a7997655da753d7c82", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.1.1.tgz", "fileCount": 47, "integrity": "sha512-nTCREpBY8w8r+boyFYAx21iL6faSsQynliPHM4Uf56SbkyohCNxpVPEH9xrF5TXKy+IsjkPUHDKiUkzBVRXn9g==", "signatures": [{"sig": "MEUCIQDbtVjPtbGL1njJyycH+MBu1ijyTJySRXALKBMnUL7pZQIgGO7Jp9s50SuLt2kjMOOJQFOd4BtFZaF/TvhJnzVxMhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd45epCRA9TVsSAnZWagAAX1wP/23MLdmGh1c6dLtTHLtC\nT9odPQRYxWapUpiy/zq1I5LUvPfjF5NsHIJgystiZsZ7VmCOKyCbUv+qCsOJ\n5OOyGyDh50ylup/0tYITvhM8UIV9yYh4GFn9oHanmi8YGxh+OfJlR5+oLweq\n40ck/i3AOB+Gw/zMA2GGD+O3dNNVZYuy+2+pBLqAXS8nTvUlEguMssv/hyaW\nZvfkGLq2+/fExZL47OEfwRvFlDtBApDjD42QWCz0uF5w7tSrz7xOUPxfkNcd\nvE+YSvufduvce2Whx7lVuYJ+9CQvYtB8SLtRJGuYODRSdxt9dwQaIZnunnAo\n4nmoC5GImcZqrpIoy8Ohkx+B1/h4+5sfMNI1xB6JprqqcEX2RjcP7Rr6WF5c\nVg5xq+PJJnlIE9e3uzhd5G/WJq97cZFPXWOOJ2AOsXvYselSv2SKRDsireF+\nNCWul/5m75s2n5mdA1u0T26tGk43B3wpWLvfkpBIdEnwliZdEfhlp699UIld\nBfXG7QFaJPFM54jhqATi1qEfisYI0m2WMK1zxfM1uozNMjzo/CgzJL/Onbm9\nXouDMllokctKf9hSqlbMGYUdY6FpP4Qi6NAqsgPuXsshWXIgp1r8gVYPM8tt\njjnVqcJP3hN6bPolr08ltcFZXtVBdJKXz5jIlhFiTZT6iMJ3L/2S2H64j+yI\nDxsO\r\n=Q4sM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "0f7cdcf895f64b9983e7c17cbff42359df0e5bf1", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^3.1.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.0.1", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.1.1_1575196585014_0.3750953768338663", "host": "s3://npm-registry-packages"}}, "3.2.0-beta": {"name": "fast-glob", "version": "3.2.0-beta", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.0-beta", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "480a15222f573aa9e6cf881a850ccf5bd9e36161", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.0-beta.tgz", "fileCount": 53, "integrity": "sha512-cO2P6ZQ0LyM7r6i+UIUgBI3iTsjjs+4SFM2V4OUbhnigN8/PwDp80/g5rTo1L6C4n58GhyY2cjZ92EORVzJ6bw==", "signatures": [{"sig": "MEYCIQD5Afcdgwa8ojtoiQRx5MMN3hNltuaXpWBlzq/JWxi3qAIhAJXOgNf59s7LbaVXEYPj4iQe91pHLxCKssECwcClp3h3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOUMGCRA9TVsSAnZWagAAHV8P/R7CI6N7r+R9voVTXfKh\nLNTEU7t0ELjgl9DKvJIftBqNfFsuNLy4oEaHgloWZNLmOLUSYn5lI2IxrkEu\nz4NVgpxkvLCIaXTG8iDu4JW28F2CpuLS00ymNbF343Q61S680spBYHQG4jYD\nJEasOHzY0iPGBoXZ8xPHamlE2GJp8rN2FzXEnDVVUH3VOWWnBy8hQgh6H2A+\nvpMgZfx3XkCANudHMykY63DvIf9K4kxKIO+JP/+2Gi/8aT9cunP3N6ynWhZx\nXoYzeoluWvTDfQiPIMCZLjXq+3+pTdWaLDYRwOhHNhdLXuV6Tgr4TQ/fNYp8\nHYbWA3cJSJjymwSAq46Q7rNBlZCAWDAiOpoeHTUVe8GHiYcMywyRF9TrHOjQ\ncLp6+xbvahpEXmvGyNMXdPhTTfepSFcvFHDLxdNgvYWIF3YriaqHoX0PqYLn\nOlMg/1aOkljgcVObteWT7o5pwRkP5ArKbUpIsWi51IiMPOAmqAnDWe8uqxVC\n06xi1b8yrDboh41d2MrJqPxWKeMThEm3iV/ZCAx7dT2H0SFSGrqFw8cGb0df\nTluI8pJXC1A7OagtqVvdgnoQ29MFh/Q9od/Tlf5xO8BoWQBWBPsVooq6NBgX\nlTxORoKZUpfqe3u8o+4DAYJhncCWEG7dL6T35JPbnkoGNiJ9TX1PzjKN51Oq\nLtdl\r\n=VPYm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "0923f9b7f0d5e213adbbc28b8677d308ea3700ed", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.0-beta_1580811013550_0.9997401348495845", "host": "s3://npm-registry-packages"}}, "3.2.0-beta.2": {"name": "fast-glob", "version": "3.2.0-beta.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.0-beta.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "3d459709067989f45288eaf82624d2fcd7b8f744", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.0-beta.2.tgz", "fileCount": 56, "integrity": "sha512-i934n3PvcpeJVuMjQAkKC6z5EkconQ+aCwdY2r5a1l9OVl0BL93SVonWUzr75qzT95CsnV9v0u/JTxZOe8JzOQ==", "signatures": [{"sig": "MEQCIF4P36ydOtz2MO2NUr+PCzmUDRVIiDeW+84xg05T3JPMAiBCou6FFwEsZoRWrpxFzSdXKCqlXT++pnQmNIrjz4IKDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126886, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQCDwCRA9TVsSAnZWagAAAGIP/1oQAN2D9HPVHRwz9tU7\nKZEKo06meHgGHVx/3P9BLvftR+EgZapTDJYqmdI+svkIgJHARV0sBwiCGiDi\nbS3x9fVVds4FKqgjTxbBHZkGnyxPFnjH0azT65GWVKY7AziXOxQsU1CH8POt\nwJpZSveEg25tDVEPsLEbF6+JPRqW5MlvPxLTUbbIggSUTdlhVoexqrGISVDx\n4BaOdrsDrV34sq32EgF3Fqfnud4Mkd4Tgej2U9F3VGpHZK+60MD2lpVhVfSh\n/Hg1pjp/GMznUkvJoSJKZQFCp7C8SvKbpXrFCiVxDwkxnNlyytqKo4Rkl65L\niXIVYc22RdyEmwHz/gPFsOjrMBGztr3WSJhf0tnwXX7m6aF6UTd5xOnXaw7K\nw9s61dsgGbMu3hnWue5G4oM1++UI8dQKfRQameVSVqaF5uUu61h6KFI22Crx\noxSInBR3twkOHU3Rin/dxJxoutFO6+/j7RAeAhR7SDKNc01n8NzDXdw29uEb\nsJb+em3gTfOudaIIvx3pdRzaZwrt1hsgmvVZ094CsqNQ32O5zUqlxioL7tDx\nYEw/rQngljrc66tCRJf880rHn7ojMwq927ZpbjiR+jC7g+iR8rsVefl0IcN3\nO4GPDPcpoCSfcXBgt5CYB85hACwYHHlqzpT1L7d4G5XnIwsxQFJnSRGrcECs\nC8Bo\r\n=ki0l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "27ab96cd4b09c9b2466626201e90824925e36181", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.0-beta.2_1581261039842_0.2644022541339255", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "fast-glob", "version": "3.2.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "8555aac93c882e711ce40f7c24435a2e44c9e56d", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.0.tgz", "fileCount": 53, "integrity": "sha512-x0gdwWaaubNqJ2nQV8YDALCZRiCy9zOEHlF7ScpzalxdxMKh78Qc0lcpZ4w2I0LZUfklA/IQPLlPU36YTgUozA==", "signatures": [{"sig": "MEQCIBKJqJIi7E+z9AdRQW/1RqjgAAnbGeS9vbG9bYqFp/++AiApIQBooiYn59b+otGNwD/KzXFh3VUNnuZ77nvS1Qcsjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeR9RiCRA9TVsSAnZWagAAqFMP/3ZeBEbGndstPS2NsCAF\nA7ZrY2jESW4fsZajgqo7W343e6NDroHw7rEaNmkOQEn+IxEhrMzyjA2Xp4Fq\niD8yADkyYRtztSpiZ/2XGjtqmPaJnvZIhKwAAT6f9m3AxR163+MwLZIkelxg\nLnimF70vn07N4wDlpHFA8PXCx3Nh0H3T0ay2FrdTd9sAbELxeVD4mt1Tjr0A\nGM7lBjR7NSe5oLFISKim+ie1NEaT+vFvMqSIN6M0XKiL09sjxM9e/VOxMO6y\ncdlrb94aBncV4mQy7poOtKwldR4DO8QL+V2Rp01gRzjsZcYTyOC5xRu3dLrH\nOf8aG1cmMyb1Cv7rhnJ/YiCju64tBneeyUoZvcTn3y9LzBFWYQ2m1eSWJCNm\nUunmE6Tja05XGnUtWzO5VOUCThuGZa87uXu7c7ABnoqpW85ocgYU7gFPBsva\nI84tkGRaOPw7JCYa+rZoJ1xdkXUw+TgJYJ+9baKf7QM6ongJYFWCR72iSxHm\nwoLnWouvbHaJ4gzNV5QGJPCmc/tZX4plZDACR3W/28urW1Gpb3oDERS0aWJB\nvsM/n0aMh1gDURZU6ILOzh/2WJfq5M+YuC1B0PVBh34YjyE8Mn/ckkYY9eJf\ndi75y32lyfu81amSkkovANE4MJ3+X11KuVIA/GBiw2+8PiiPI6hDkulCggjA\n8Bc1\r\n=S7ph\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "1757299eeec95c9e03bbf965a9f6903184e68a26", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.0_1581765729309_0.392244878208442", "host": "s3://npm-registry-packages"}}, "3.2.1-beta.0": {"name": "fast-glob", "version": "3.2.1-beta.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.1-beta.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "6f130248bcbef5c97a55b493ab52e0b4ff41a275", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.1-beta.0.tgz", "fileCount": 53, "integrity": "sha512-Omzt5IBNdpIKRsEbTBm10jDIvqJaAP7+K5I1hA1SPTSW3Y+BpZXPphkOmIaFFSy7hDKW/aEgHU1H4XlioFAEEA==", "signatures": [{"sig": "MEUCIQCv0pcVFyHu0/SzbgbT7+Np4Swj3UHS9Fz8NFtZ/i/ZWAIgEIMRt1sk5MJbox97UtCeEggHBtqyShigvnaY2ReGSpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTswwCRA9TVsSAnZWagAAe0MP/jIkZ81mD354PGr2VX8N\neWfdUwKlHHqmdoHLv6fwXNfqWG2ymKINmklZr+Bp3Lm4F1D49SHQCRv5GGmf\nggsrtofzIMJXYxFyyxlJ1nIh1GdGNHNcVWlsSCUM4vLRp8SA8iYbzRVJDcb9\nYQf1KZnVslIj0xKgvIIC2uE73RKl39xX1X7FsdtWQTFTPDdcCw+jqASmx22m\nY0gVX+RJQwB/KWlOqKYY2y28zZ8Xs/fDb6P+j9HFMTyXKUaWN4E1ahmS+CZO\nqRvsXAT9Qe9wK7N7OoJFkwfQQI6kYFWmW5xeecvGlwvC1QQmiMgb5HQw6Y8M\npqWtfsA4hnB7tlciWJKTmyTSoXKnOxis6jdYYV5SwXZDVRwdhuiZFbyh4Sxu\nnuTkbxc+xK0m4wHPo/N/6D4P6rVNssWoSiJtcYgvqF8aiBLrgrnsZEdUcOwU\ngQDovdIXLzS9OHNp0SjxM9kr+tWK79p+z9P26l6ZYML25pgPeMsP35ZxgDrO\n4jROIPfQIttPbTquuLxr6IaWCiFsEaMdIPCQcC9M/gtKYxaK36GcFm/gYs/m\nHqc9O3nm6z4opPi6IxUC2uJN/Q9dgtKLpibhcut33dtN/Bg9p5yPh4YRlSaL\nK7B6a1bffbT5OxlbzAvKWnCFz2uLYNDOccz2keZ01bxXGluW3cZgqitwwfIC\nTnkj\r\n=m7cG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "174c557c930ce941666157aed3208d8af966a3f5", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.1-beta.0_1582222384108_0.5758108524083168", "host": "s3://npm-registry-packages"}}, "3.2.1-beta.1": {"name": "fast-glob", "version": "3.2.1-beta.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.1-beta.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "708ba6186f18fce7d8673d9da5ea5944834e2ecb", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.1-beta.1.tgz", "fileCount": 53, "integrity": "sha512-o9zUOCtCdsGQNApMVqaY2PHgxejB/qbFLQOV9Jd+qf6UHs/9CCfntIlZcxK/tfDdlvPKU+A3FQumjvx7n+pk6g==", "signatures": [{"sig": "MEQCIANURwONQD1ehuYsLm17PuFQefEYP3J9fVjCeSruJlzVAiBZLbz/qAzofYJMe8I177LyvrqfqFlPQpOrrytFLXt9Dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTtJHCRA9TVsSAnZWagAAwBEQAIhYq+tUDB6Y31ELN9Yj\nR5iqfWfTP1LYU9Ikw2h67NDccNSyKnK+ceuYDWIbY8d6xOEQZJ3zP3VmqSv7\nY/LL0l+parOrirw0ibKqWhd4SftX5oCU5Y/FefOn55p2bOE0+noz7UZweSe5\n6awmQV5pazTliQELgVivA+K2HVP6OPjOH5fYF1ELXzpMBlq4E7FqMjOR1sYj\n2PUboCZGgCigfEehFQ5Q3/29tbChkn5xlv81zrtNzrT5V5NlAA/THy6DM9ld\njB3EtCXUT14BPXXhImnERqMwJ9D2OmQpuWTu6I8n7X5WbqbdnxzCSNQlzQiP\ny0eTUL5tMUf4c5ITjcVxubF6s8HypAr/nxgVIi3ykbp0mUVLleDfaW/KJzwl\nMIm5CJnKgs5pucDVuKV35wEQ12mGVP5D++0yLA6EiY+Ugk3GlseiPSEbleBR\n8VcRD8hLQ8Go/LwYx34WekbztZ62/sdMM5ojU1lTgw9reG6bWH4BM8nO3jMP\nM3bPVyTSa70/Ju3fNfaTNjfQKniA/Ey+Oeg8o0O3rIq1d7wV6BjqHsKvKxdL\nYL4a0E11TJ5VLuxvvunhaJqfiaiDo8m+Mrjmvys2MsNvAcIIobm00dqx3Yjt\nWi5N2A4Dlyj5YClQdK4lMagcTV5qoX5M3JzBdfoAydckMuTUvVRn2XP7QCmu\nQtPp\r\n=COta\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "f9af59769d555e85b80c46f5838423006e0b5a7d", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.1-beta.1_1582223942619_0.45384144375815505", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "fast-glob", "version": "3.2.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "c5aaea632f92543b744bdcb19f11efd49e56c7b3", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.1.tgz", "fileCount": 53, "integrity": "sha512-XObtOQLTl4EptWcBbO9O6wd17VlVf9YXYY/zuzuu7nZfTsv4BL3KupMAMUVzH88CUwWkI3uNHBfxtfU8PveVTQ==", "signatures": [{"sig": "MEUCIQCgGg5NX9euM3MuWFoicMqnOyT6a3ZWnTePf1YQFdy5lwIgC3KxcNrrVth/E4uOBz5vMcpLagRq4avtMFcCM8UWfKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTtQwCRA9TVsSAnZWagAAwu4P/06kJfFtk9GKHY6jtJP2\nBfQreTPreHj7IK6mDCFXUTVgAevbCT06L4B8Ug6MFf9N8PVJJVfU415Xg1MC\nMKhWGcz+GJbNCaYyMN+HVGaxRmo1mqxD+FwnCEzSH2ENHUTc2tgf12BjzFuc\niU/oYxnEzZGUJ2tK23ZqqDM8trHGsidUxMaDmxKdu5EyG/+zF15z0Rqr5wNo\n2Isy8NbM1NaTOX1NTLry2fZVO8XoC1L/1YdJaGx/WO6FCyXy3orNJZ7xwuen\nd/iON+zphteW7e6CY6Wa4upSHFzWtUKBpulCbb5BRMlg5Pa9U0KiScoJwXy4\nn/Cnoj6m1NI+le+NgbPivdqR9V7QHZD5aIloP/233vncuLgPu+Bum6d0PX1e\nPvRKeYEbrqxxtR2QEGxSk9NB+RshFJPMZL1GqptnHPUxkq2H/b8T1eLGawz+\ndtPnkQgDlO/ywmyxEtS6s8iIbBp6B+zYDzotNC6yzmKWpfhc8cZw3qtwmObb\nrxKhx8tcwDrkob0d/VLJ6XWFNFKkVV8GZlyACV5DsT3Qzgixg4CYlqC8jXi6\nYi+QoJsWJ0GGxmBiTYwzrtSZ4rqIraNIzk6Vr/c/F7yw+Qkp7vWpLefbk4h5\nDfAyjVUdw2cuxqw9GchEFsITyK2+MdWeDkJOA/c1dKe8/8H3QCb1NUJdL23n\nEbc8\r\n=dohn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "78c7780d78393035db7f3516b04179c7a050fcb8", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.1_1582224431963_0.24135672798174146", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "fast-glob", "version": "3.2.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "ade1a9d91148965d4bf7c51f72e1ca662d32e63d", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.2.tgz", "fileCount": 53, "integrity": "sha512-UDV82o4uQyljznxwMxyVRJgZZt3O5wENYojjzbaGEGZgeOxkLFf+V4cnUD+krzb2F72E18RhamkMZ7AdeggF7A==", "signatures": [{"sig": "MEUCIQDOa80iBiI/XUto1N5UBXJ8R/8xclqPX72VnnI4pmoQVwIgQ40OI5UG20Y4kEAZboCSDg3sxP8uEcOMgJLV7iRoeok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUEJgCRA9TVsSAnZWagAAmu4P/1q1Q6ytSyRh6mDyrOHB\nJffXTH+pkzS7YP6Elaq/u2lY72JEQkNeP8OYel60ueAMM4dKnSHPzjxR8E36\nnaHmknTQEXuyLgu2ryYguTP9Pr6EqSA2FJ+UNGkiC8m6zUJGDb0pBo5LcCRc\n3VAbwSkoNVNz3StrTPPzZHq9OqW9eVqtX4b2tY52e3659zsiuBnls5nAkSYa\nktsry6I+HC3xu+DGEwia6rSuaPPuKRTqqmvHaW5apWi0fHwUHgJVY2md76OG\nmWiEXSq5ffdkFQjY44Q5dXFDsirVikbV2GVPHPrPEjwaz8r8N4RChMNSUNdp\nlumedxVvf4Ai9YcVX1oQ1X1MPvfL1fQdD7xz1HXf/bGXKa02U4tFor3ZRasn\nMIQTue0UyGVH0H5IOdQ0EdHGf7YPB+/jNCUaGI56Fka1A+iGlgbiNAtvtBli\nLMGaC7FupdszR7L/Wh9V2jpG5ZuWDodpDEzfpPG7iozqxDKC7gajm4Ep/obT\njU6pXIJ6AzTnpdyp1C2LZmyu8DfPCy0WMa0SpvW36fCIigaAze4zSVa7CvNs\nzBf4b3UHrVWAiEiWmu7kGL+/K4Z/AIsf5ATo2ztZD27UBolUW89Ox34ZftbN\ndw8YtbpvgEw3U7ZUdIkGMPVNo6dzWZgEysGHug1X5guIbelPkF8kxcmdtowV\nWKbL\r\n=jbJW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "5d1ac289f096387d793000a2ab423cfb15d21f4f", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.2_1582318175677_0.8690768998174851", "host": "s3://npm-registry-packages"}}, "3.2.3": {"name": "fast-glob", "version": "3.2.3", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.3", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "64d6bf0e32f1195ab45ac8896e4adbe267ccd798", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.3.tgz", "fileCount": 53, "integrity": "sha512-fWSEEcoqcYqlFJrpSH5dJTwv6o0r+2bLAmnlne8OQMbFhpSTQXA8Ngp6q1DGA4B+eewHeuH5ndZeiV2qyXXNsA==", "signatures": [{"sig": "MEQCICjtkxgU2jlzNguu2ZEqRxIYpP/3shuSpqvxkt8QVsR1AiBzC60v0mDhUYlrdgqCmDELcKTKSVzlRklWHRwN1e2pWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe56lJCRA9TVsSAnZWagAAXfQP/ip+ybGbVXeRGg3a5b6m\nDacgatxytetvTEREVPRkWYjFMeop9Jo7OSKQAL95DT7hJt563MhNDT+BFMYQ\noU4ouT8Pvxu3OhpNcnHy3t0lAFZG+03tdOmeHOOtXhUBvrSmQZt3SpMHfFNu\nblZHLMEfB/ndRrIU18eP1wKyqkc55/UgUibUbKS3cPp93SIS4eon/qiZ287p\nZpLlNv0zJMeob1TPgpqzeAtfAxOAepqYNVQE2KId4SVe2hOSYxwSSdNnSXvF\nHMck5OJdf82gMFnW1iGRvx8bXtqfcBRJaWzHbHvz7MbOvSSgshRDIyx5Ag0O\n2ZsNPDnXwUGhKoNXYqJBAF6ZK6fwdhIn7ErBHPW1vEEpYD49pnZ9OkLhsQd5\nFSUDs6c7VuprQCQpXX01qmtFYmLJXbiSm5VKUJVO/44J9HNMSHl/AT2kEFPt\nHMcv8Pc/hPUcaNIZxnOdZI02uxlHhc7OaA6T6DfYHhpbk5cxanenB5sFv53J\nZ5R4ud/zChVL00O7uElhIMchopLXgvQIGnsimy5BBkW7K1UNC2CpTiDFe++u\njEa4jHEplkSJ9qxK8+pzderyVnVECQ9iE5ADZIp7io1AqLFnCiX1zegrOXmS\n1D/B+ontOKuVQgKLGnUyx+nFAG18Rnj58QSGAuxp487SVw0+fYZmIEAP93Pt\ny7BU\r\n=5+0S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "98e566c245fd677426294068e8d3de4ffb95ebc4", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.3_1592240457325_0.9361880883477103", "host": "s3://npm-registry-packages"}}, "3.2.4": {"name": "fast-glob", "version": "3.2.4", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.4", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "d20aefbf99579383e7f3cc66529158c9b98554d3", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.4.tgz", "fileCount": 53, "integrity": "sha512-kr/Oo6PX51265qeuCYsyGypiO5uJFgBS0jksyG7FUeCyQzNwYnzrNIMR1NXfkZXsMYXYLRAHgISHBz8gQcxKHQ==", "signatures": [{"sig": "MEYCIQCKK0LzpGxQXNq0pYF9+ZghIdIl08uY/j5KNbOi6P/UfQIhAIXHc7tMpX5ANp3xiT7bN5nYcVkJi9GyFoDDLZ+uyxRj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6HI8CRA9TVsSAnZWagAAJyAP/34x9jB4w8zv1W7n4I1E\n+XIjqxXpfKrvNG90DkEgucyFQ0IaUEhigreXCVw8ZTf220zBQDT/JpRmJngr\nBfuYjfbdk0Cy4zn5NUYZ7iCqpqNLix4D+pt6RG0dRXQ5ssoWDzuXMQtFocBb\naOmmCVDFS4VLXig0QF2gI9zavB8Ue2i5L6DgdLa12doDnB+Ep+aQre/f+IJK\nCfEOCpAoxAoCNXbFEWZTDG+kfBWfXZNnd5e+WsFS4Nn4rH2F2DcoXiFfUofi\nlho/7QF9R8bAv9qz72Gl2om19uejiQIUIi6BW8HVUWcijGyLAtgHFVAf1DXr\nOQnsovFOechHQBVNx80H7xO+iD6dzUAFf5FlnE2xdtTiksQEuEnXEkV/bCXh\nT6V7wRIv7H7nXx9lEUCeNwex8QOYPKj5iWEnPTy7OR/0ey9srFAGdEwpzI5y\ncFzImsNsJOTCKVEawHedp8w8imAgVY/W3ScrHdeJSDv9vIYm8z/lknyCrYEk\nqKzJOYxqLhqhLlhNH3Da3JOqSWQk+0v+LrweKUGZ2pR/HOaxI09TF566rXX8\nlW7O9/07zUQPgAR4mXoJ78K5+sLv1OEP3cF7YZueu9nK8A2JFzSTSfuSpcuV\nDMHh6Ey/cLsDl3VWKngh/+qjfQ4gpZmasj2iroie6oScbWsa3OEMbwddx6R2\nQehD\r\n=gAUh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "a9f7b9510c171afc292956802bc44113b15bc144", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.4_1592291900437_0.9590225592645945", "host": "s3://npm-registry-packages"}}, "3.2.5": {"name": "fast-glob", "version": "3.2.5", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.5", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "7939af2a656de79a4f1901903ee8adcaa7cb9661", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.5.tgz", "fileCount": 53, "integrity": "sha512-2DtFcgT68wiTTiwZ2hNdJfcHNke9XOfnwmBRWXhmeKM8rF0TGwmC/Qto3S7RoZKp5cilZbxzO5iTNTQsJ+EeDg==", "signatures": [{"sig": "MEUCIEUWDI2jqf6SqlJtiVN5kl6gqAPV7flijLpYaA2gLDccAiEArfcHwKbgZYSRy91PXdglW4BrxGYiaFLR/mADdKUalso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84711, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBA3eCRA9TVsSAnZWagAAqTsP/37M/33FSSRLl+cPMs1C\nRQttHZ3zrsRCTmsGFRWFqTQ86Bmkr6toBupxEfRUZ6ZlUop7tBKDyXsjnL28\nymbN7PpDjG+psAJyPOOyHeVMTP9DjeNTrXsEfl+IXHycnCxpgKbNb9UA+wxG\nWoaU97ldlBkscgWeIYzN/Qb47xuK4Y+fEwR4wSqXobKG/IS3G7tF0RqlZmvs\nryVjzMK/95qu3sT2jA8ng8WzAH9N/GyIESwXxQJH4Emh/5E3B3z/iF5iRRTI\n9q5cFXGcZ41LEXUjCkchVd/4S8pFBDZzzQGtc/2K8ooOsdqCEhd1tdcIdx4E\nGLOuUPEq4YzFTPBJ7sxqP2i7dTpUyzGJmuJKurCTDqx2xoyhk9OyaZ3iLYPm\nR/Yk824uBt8pqkTMltWGJFTItKNhOGuqKg2azAlLKYDRsWdB2qfgf/ZzgBlT\nXk+EEwfnL/rKU53Kmm6bOZXctIMAoGTDNUdoV1TCSYRYrgVAB2OvMEFt0P7j\ntvsGOKB6gJbR2j+hLeyUVZqKd6TP1rm+DqXC34BHaLXE7qyEG33a9bxSZuRK\nJ6PfTImsQykgDe9ukmo3Ct9rwZ4TGGtV7PU8TCUHrTnNgpcCLH/eFVcWq3Qq\ngiFw+P5kYUNgyF1Mg8Khr00/u3/FnddIhsQNBntbFYSlC/1h6VI3o3xwNWyf\nOH0Q\r\n=1IQ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "ffff3ecc11d5d752b14386c55affaaaa4a8a2879", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {"merge2": "^1.3.0", "picomatch": "^2.2.1", "micromatch": "^4.0.2", "glob-parent": "^5.1.0", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.5_1610878430347_0.23147003041505898", "host": "s3://npm-registry-packages"}}, "3.2.6": {"name": "fast-glob", "version": "3.2.6", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.6", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "434dd9529845176ea049acc9343e8282765c6e1a", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.6.tgz", "fileCount": 53, "integrity": "sha512-GnLuqj/pvQ7pX8/L4J84nijv6sAnlwvSDpMkJi9i7nPmPxGtRPkBSStfvDW5l6nMdX9VWe+pkKWFTgD+vF2QSQ==", "signatures": [{"sig": "MEUCIQC65JptDlGwI/QvDrkkyfDkNw353zpR65PAbzvYTr1JkgIgOdKlcxIWxjS/FRLBrVyb23JO5iu66JKWHs0BJMOv7Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2LFhCRA9TVsSAnZWagAAwN0P/2QTKlB0Leuytlt+FMNV\n+iA4Rz38iN6qbTMZurfdiMgz2g5omMNxZUUWYCDR2s6kxInqa3dKlI1ahdOe\n/4otUkWuKQqqAAGIwzxPBFYfHVH2SvduBzHT8T7cm5Pg5wFNsj44vKDrSgMK\nJVv4tMd18X+FsXAA9FIja+76G6ZSZDVdr5fFm8H+2iF+Ox19iJLsQGb8cg/P\nPXNF44PxhZxivh6d3vFSJzIlHl6/inr/RrrCaEGcKhIIRCLJnei5EJ69mguo\nGp6hrC5b+1Yk7ASd95h1lU8X/YvAnlQM1dZd3fOrVHPV+RK1G04IOfyidNAl\nGOmHhfpI461FBl0xMWmUD26F/ko+IpaPnO2n5cAyi7cAdIaAbbbZRDwQYOw8\n/DYJd4PD60fx9awngwOsnpILgixSpM96n7ImKF6OghSLn7+RZRj99qRCi4Gq\nWuOuowanLWgWByM29b1cD8mxQZpymsNXNGzDr2iAx64xVdzTQAGPDVCa6JJq\nEzCvzx0A0Kt7wLMI0dW1nKcS/0dDpskSEa/wzJggucEDumHlLpX9I2hWZoU/\n3W1Sy9KK+a0r/jnXGvfNZpL1xKRzFrWE7Yfn4WFXCNlZtPG9Hor8Tgd8/aSp\nM3KUv1NBXkAgMsIIR2E7Pfhodmpk7K71ixCzJ+dejB9cHBUL18IWK8trTxIr\n82hO\r\n=Pr0y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "d22afc5443d2d482fd494d6850939f2d0167e70e", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "16.4.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.6_1624813921054_0.7343440433990809", "host": "s3://npm-registry-packages"}}, "3.2.7": {"name": "fast-glob", "version": "3.2.7", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.7", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "fd6cb7a2d7e9aa7a7846111e85a196d6b2f766a1", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.7.tgz", "fileCount": 53, "integrity": "sha512-rYGMRwip6lUMvYD3BTScMwT1HtAs2d71SMv66Vrxs0IekGZEjhM0pcMfjQPnknBt2zeCwQMEupiN02ZP4DiT1Q==", "signatures": [{"sig": "MEUCIEknQnQ9+pwVQs0ggVNfWk8i6hoCfcPwW0W9s8kbWTO5AiEAwqDzZrUaDOEP3t/aaAKZRF7udKhQPN+PM09jpPM//Ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg51EqCRA9TVsSAnZWagAAmoAP/iWwTTHegZZR3Ifn2rR1\ncFBnztl/TDdKVurGS9DT+iv8JV9GGdVCG2XC5CuH0ZFHYC+q+iAscvFJZV61\nZX+xdSH8Gd2bMGRuUk+AaXrz9yUyj7rSYxzjTaX/newhIAbMMRsunnaj4Ecu\nrw5nYyr0r92Kz8Jo0x0fSHJTPl9yeswhq4Ld8Jo9elqgWREKIWEW0pURFZIw\nj4958q/JX967+5wUKLrhNK0BFcPW3RGPw0MG3HV+7YhMYGpyh5m2RHafZCem\nmfc4dwEW9EVyqR2Tc0oPy9lhVWfYZGPEamBiLv+zWmRPYEmhya9jDxuujygO\nR01qe1nIccdMEHWaDpGBXahP4r5e/rj6YFJlGxCYBgtjwqdAfz/t1JkS2Etd\nGqmo4F8oexgNI3XUzHsrCdD9LCDnbOFZ2OWyV3wNl9cmcUcZ0COEj0obZJ7K\nukR0A1W4DPiNqavJlRzOYZpWFcPGeIt8GyfBuzP7+DDK9o5uyI4npcwlcZ82\n90soGt5DWtXY0DXKJivvnE0TtcMb7K0elxcQKe02ziXLiJyZPIOglrvutbjI\nQOxbZugD3VoNXQEONSSKWjk/4g4Zt7Zx5feGaVKsGgnCBVSF7kG+cTGmW+qA\n1m38zyJ24njdiy9htzc0h4GGpS3vnBu597YptIBf87yEDvCH6tGKiKSiHY2w\nzHl7\r\n=4bpI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "676bbd6d64fef9824456fe1f52e0b4958219a85f", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.7_1625772330130_0.4936682572243438", "host": "s3://npm-registry-packages"}}, "3.2.8": {"name": "fast-glob", "version": "3.2.8", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.8", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "b4c563b4750cee1cbe8d8d41d3abf5cd6e211923", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.8.tgz", "fileCount": 55, "integrity": "sha512-UsiHHXoDbC3iS7vBOFvld7Q9XqBu318xztdHiL10Fjov3AK5GI5bek2ZJkxZcjPguOYH39UL1W4A6w+l7tpNtw==", "signatures": [{"sig": "MEYCIQDdY0zy/DRodBe3YPhJLOxE5L3EOMPeScbAKIXoeC3KawIhAKkgGGvvFNr8nYhUaykuPEKqygko5n7WlXC98PCGwREF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2BLFCRA9TVsSAnZWagAAZ0AP/iq3royWJ5ux9Qm6sJ3Y\nq4VK5LeuRsK6a7xZv6Ds31qHB4NEeGmUWjrLFIDhPUKGJV4Z3HER0nhafOe2\n1sPQzLqECfylrUkvH4+6cZkN/fYKg0mylOvw+ym33nOuNovuqDs/LQ8PXZHS\n7yyKfrpox9dbcK0vZGIFqQKJfxAeAY53Yi5e9AblUnxxnB/eiK+0opgBWCF0\nGZf5cv9e1pGe75HN0D4pBf/81mgP0GpQMrELJWaOpSnRdRH49/QSIVswwZQf\ndlOyBC6PbeZuZCbWAw1C03iioZ7TM/LcTOHzHIh02Q9Sf74hu/ShHolSwNia\nLX7edRnFn3XUd3FPzkCk62daYqkp6bCWfPKZYiV1QaQmU7ei+jXsQRaR0Y7w\nN5tz4FcznfV/6RL94y4FRgnqBreBGgcVG7Uqcu0iIbqFFUgJMhSjSURrYvs0\n3Q8D1GETWkKxpMa7mIywxVwXCQtDNfITUaL5Iy9PrcIRPACIw0L516EPSAK5\np4xavXfGqU3e+fUoVca8gDAiXV9pmkf4LFbaghUBZAcHxY5QYYMvorz8fJMN\n+1vxmmG15/IAY+8xbIn71VDQX727Sd/VAcAAtDiZZBQui7/V6iiqADWl82lQ\n1HIqFALQCbkVWC9E4ykCyG8ECH3vl4Ao9J3K4wT3YEjWzcj0yH+v/G8bAovq\nR8Jp\r\n=mtCU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8"}, "gitHead": "eda8195a92e789e9579eb36cfdb7a7639ba3fd8d", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.8_1641550532810_0.24287372813955788", "host": "s3://npm-registry-packages"}}, "3.2.9": {"name": "fast-glob", "version": "3.2.9", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.9", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "8f55f664b68a236bd29fa165817fc44f2b11faba", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.9.tgz", "fileCount": 55, "integrity": "sha512-MBwILhhD92sziIrMQwpqcuGERF+BH99ei2a3XsGJuqEKcSycAL+w0HWokFenZXona+kjFr82Lf71eTxNRC06XQ==", "signatures": [{"sig": "MEUCID8viKJ+IhzjzD8rSKSqy2eIiFwblTS9icz4Dvi/ox4KAiEAtyum+lJ3GNe7xBOzxNqYh4aJ6cspH4hA2RR67s1gMhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2Kg+CRA9TVsSAnZWagAAQ+EP/2rpZXSmK/oovQL9N3M8\n9sbCnEzRu95PSwhOE1zxlTscQFI8CZFtsjVDoFEX6RrGNODPZXXDp4IelXef\nDl5g3H5Rv6KmGrJB1v1EVfG9Rqp8yaYJf0gRsJMPQOjSwN9Cx6qFhUemK2lx\nsSu8me2u+4p379AWEOcxZHIXFUWzExhltYsmroBMrCBR8LfizPzoZ7jELW6E\nz96ZyfqMvSdLVL92fSHRoNlng2GcZCyZ0G8aR29QIIer5csUXTHpzD7yLUQk\noAf3wHJBxs6lXhE7nYz8gRPM0GaTpBHUIfVUdPe9bwPTrsY2EqIDDL8cn/lD\np0qkGuMa5rt9w6zQErEGipP37yCrU+Kr50S7XTduzBkTz2mpWv+zPcxhL0td\nrf6oUT5Fd5y4sbrKNzvozBqvc34pdAWO0/LOeG6Ljy1yCHaFicE1+mywVSsR\nlb/fIJkpJseEBOUMiW7CwmhGaw1vE74uVYYBdVOB9yY1PLAh002B3KXAkDD7\nHJP1EHqsq7XcENbPIfC1a/AqUIvZpLzvdPjQv9M2m4IBS2plrQB7FrKEfK+k\nXsxnct+jlh707xjMAn3I5XA8/tcZa+8CqNbMYILhrfWuhhbKs1op6Ddqxybk\n56EiBU7MH+txvm/QZMCX+iXF5OEjcuM3oWjv3YJH8KaHIudBvqN895irl0ki\nPOay\r\n=R64q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "a9247f941da7b725948d658d5aa036cd07873518", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.9_1641588798185_0.9938530591459613", "host": "s3://npm-registry-packages"}}, "3.2.10": {"name": "fast-glob", "version": "3.2.10", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.10", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "2734f83baa7f43b7fd41e13bc34438f4ffe284ee", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.10.tgz", "fileCount": 55, "integrity": "sha512-s9nFhFnvR63wls6/kM88kQqDhMu0AfdjqouE2l5GVQPbqLgyFjjU5ry/r2yKsJxpb9Py1EYNqieFrmMaX4v++A==", "signatures": [{"sig": "MEUCICpLVg7nXsadDdMdHoIRRWLOfTUh9+B5DaNQCGpvV89bAiEAssijUptPG+vp3aNFJmC9uBY5ErPOL4LPuvdEcbm7oCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2uXLCRA9TVsSAnZWagAAylgP/Av89M7CDBdcmh9uCMXN\nIse42sM0Ef4wyZmY9/i9l+/VJE4VujPSl45TaVWFySscdQEgFUqbk+VbPb3r\n6U3aMDneGcKKDZxOa22JiuS7ONx03eE2emL1eF7e307+lsezQuuRtuCs2Hjp\niDrsPZQY4VIzzMt3qSPuUE4Pui2jUr3BgKd9sJy+OvV2jd7rtrHybfI8guLT\nfHtsX0M8TuKnpEKIHeWyixGeI8Cw3wpNbCiHwBOclUldudfZAkq4mHngxEv9\nWKn9GHivuR8vaXt+sTE0J9Xtu3cfpkfSG9HwGEDB7vtea7cvtbSMRuH+XvyT\nHw992I3KopLQ7W/JnC3dK61Lpf+8/OQytzukHNY9JnhrqhJe0ofPMCiVgU1o\npx0x6xuOcmVrT+Rl7UYkPkogtD6V/xO17afEI8GfmThxf21G+F7QoAJizXS1\nI9HWOG/6pEIxxK7/FTBHk6lhTpNcdribOpPPKEpTq0Wru+Rko16Y1GWG87eE\nmrJDran/95tG2mMqNjCV/yIpS2q8/HMZnkPVi4yXTCJNXL1MgvicQmdyMKUt\nd94Jqo+Xju9RtMWWhZ9Ux4RRUi0DQdlOPOSPahXmuJvK4BdsRLY0J8cQhcji\n51Rd3mQV03IxpWnQk7+EptA74naqTCL3mUOodfpMIN89mvVE2YbcHigdNBSQ\nPxWt\r\n=vUgR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "06772713061b5f94d49a9e0bbc54d3bdaad93634", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.10_1641735627216_0.2956040708445571", "host": "s3://npm-registry-packages"}}, "3.2.11": {"name": "fast-glob", "version": "3.2.11", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.11", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "a1172ad95ceb8a16e20caa5c5e56480e5129c1d9", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz", "fileCount": 55, "integrity": "sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==", "signatures": [{"sig": "MEQCIGD+bWTgvIIprDHsrxYv8phpUwAekA06jbnMF5Sxb0oOAiBr6ZpIa0ex9T7EcczDAJUNrJjewXaidjNkiAopwt4mxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4dElCRA9TVsSAnZWagAACjUP/0ZF53f0E1dxAcz8/Isa\nVvVUAOi8E+y9iB/NITNRRSv0V2+OuLqdiz0TxbGe8NMppKGDazMMd9zGDDfi\nIK4QUD7iPvmx/uMfzuwsQWJbYeGcHsvGhmBZdMueUXlqZQ1CO0J9WoVWCfEF\nij2cjEVJ1L8Mx/6VRMpeH3IK8q9zac7yJyK4bgprsSLnpG5mX2k6SNWEsXdn\nKju7pAUPeQvgxJguBN1C3pPttXPe8b6mlLHVUjm7dR2XUdFDUQKhAoUyCqyA\n5zoxAZ9m4akQezZOZDTEs4f8nSU5Sm8qChF04Tsx8ybALb70tG5vXpbB3wyq\nmPs7zPby+830YPCj2kHQW59Cl34dcmd5AJTG89Y/nEWE+j5xzJ6qVv/AWFT+\n4TCw0Ugs4guohRzIsCDE80gleSEYEz+rVKdsUDheOT7t8i5xc43DkEviVxa2\nR+Rg0/eNJJfmTJ12t6SprwGEgO+4sUsDOCHRjCwR2MdY02E+WDLLWpPLgq3j\n3f6mrs0CmCM8sEdT3SPy+F0YLgMcm/WkW7JeIbqTNlPAJkpJMqTciRBiH/42\n4xfcqFqFuSKJ6yoLZO+2pFj8NEyoIAJ+SElHxiMpx6xNaJy/u8pP0zCHPctc\nAuJH2Hc4ZsDmrrkNTn4GW5veOQDGn46PB2AwXt3ue63gsu3pUGXwkiW/qr3/\njkXv\r\n=9BLA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "1f82afb8361757eabbc72f869f1066bea2771128", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.11_1642189092921_0.10002788669650409", "host": "s3://npm-registry-packages"}}, "3.2.12": {"name": "fast-glob", "version": "3.2.12", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.2.12", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "7f39ec99c2e6ab030337142da9e0c18f37afae80", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.12.tgz", "fileCount": 57, "integrity": "sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==", "signatures": [{"sig": "MEQCIH++iP+N9TnvIwi4hHQazTC0m5t0jIohg4wP88H+CWKSAiAS2hROv5O1g/iLJGgrAYJJ1nvMsdoOeBfmf4bjY2r5Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGt/bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOlRAAj5yj8Eauc4lQavCQQvorSmiStX0kI7Bl/oc/nQpgFPne7knv\r\n5sYnJypkaBFC8b01cOLHjaIqmDjM33q9Mx6CeqQLJUWO1TW+pXfH5EnRmp2R\r\n22rbOAf5rUHD83vHyMLFxBwAqyvKrfxaHK/OGytdbK6ZVO5sX4EMlK4fjed1\r\n5OaEbhoOlvHokUDGriMO0EvBmM017WF7KpAhZam+OoroKJbdIfVIKkI9rMqv\r\nRcewi0pY5XIzaFylvq/0kpzGMfX2OUxtVVmqFwOdUqR4g58SWa+VpEMkyA6u\r\n0a9BvaDtsH0TxYH0LQUeHMXL4Z13iqvUfrcjSPV6TVgMyXUnk6R1bMHutHti\r\nWWNYZ9jQsAQEY0J6e/Mg/pUGHmuziCSl4LrHb778noTOirXAohQHsDH58UzF\r\nXtQo3gIIph9JA92+M5r+pMxeGfDwBtCfx2sPa/twAqxHPhv3qAY0nkenIr+V\r\njEJu+Cln/akSteLYctU6DE74ALqLkIEdlav+FbB+KnTDMrGWv60VF+uXEN3q\r\nOBXwnLMNBr5LY2YRA2f12d95Zva8bQ7Jc3rXlFRUvWcxymldHSawymtmU8fd\r\nmyHkDUx4OPj3D+upkpuHVn/VUxr//m7/OfXfXjpzzWAfnxKTV48P6/57kZCw\r\nQsabC02zMjcbak/PB35J4S++tFrlLofvNCU=\r\n=vGCs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "a028e3670ba3186a102cb9108e3a6e48c1148a9e", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\""}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "18.8.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^5.1.0", "glob": "^7.1.4", "execa": "^2.0.4", "is-ci": "^2.0.0", "mocha": "^6.2.1", "sinon": "^7.5.0", "eslint": "^6.5.1", "rimraf": "^3.0.0", "minimist": "^1.2.0", "fast-glob": "^3.0.4", "tiny-glob": "^0.2.6", "easy-table": "^1.1.1", "log-update": "^4.0.0", "typescript": "^3.6.3", "@types/glob": "^7.1.1", "@types/node": "^12.7.8", "@types/is-ci": "^2.0.0", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "@types/merge2": "^1.1.4", "@types/rimraf": "^2.0.2", "compute-stdev": "^1.0.0", "@types/minimist": "^1.2.0", "@types/easy-table": "^0.0.32", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "@types/compute-stdev": "^1.0.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.2.12_1662705627568_0.8867071097518946", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "fast-glob", "version": "3.3.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.3.0", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "7c40cb491e1e2ed5664749e87bfb516dbe8727c0", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.0.tgz", "fileCount": 55, "integrity": "sha512-ChDuvbOypPuNjO8yIDf36x7BlZX1smcUMTTcyoIjycexOxd6DFsKsg21qVBzEmr3G7fUKIRy2/psii+CIUt7FA==", "signatures": [{"sig": "MEYCIQCa7cjxEPR0fYp8XzoJwAm7jU9YNvHU5jeeRtDALzNLyAIhAN0SC6HLMwQ+Z4XGsMpj0TBx+SYUoAY5pZJyPm6UBokd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97594}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "af2e35d1970c34d4b899bb08d9147e93649e25d6", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "test:e2e": "mocha \"out/**/*.e2e.js\" -s 0", "bench:sync": "npm run bench:product:sync && npm run bench:regression:sync", "bench:async": "npm run bench:product:async && npm run bench:regression:async", "bench:stream": "npm run bench:product:stream && npm run bench:regression:stream", "bench:product": "npm run bench:product:async && npm run bench:product:sync && npm run bench:product:stream", "test:e2e:sync": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(sync\\)\"", "test:e2e:async": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(async\\)\"", "test:e2e:stream": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(stream\\)\"", "bench:regression": "npm run bench:regression:async && npm run bench:regression:sync && npm run bench:regression:stream", "bench:product:sync": "hereby bench:product:sync", "bench:product:async": "hereby bench:product:async", "bench:product:stream": "hereby bench:product:stream", "bench:regression:sync": "hereby bench:regression:sync", "bench:regression:async": "hereby bench:regression:async", "bench:regression:stream": "hereby bench:regression:stream"}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^6.0.1", "glob": "^10.0.0", "execa": "^7.1.1", "mocha": "^6.2.1", "sinon": "^7.5.0", "bencho": "^0.1.1", "eslint": "^6.5.1", "hereby": "^1.8.1", "rimraf": "^5.0.0", "fast-glob": "^3.0.4", "typescript": "^4.9.5", "@types/node": "^12.7.8", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "snap-shot-it": "^7.9.10", "@types/merge2": "^1.1.4", "@types/picomatch": "^2.3.0", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.3.0_1688130245748_0.3946580578880712", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "fast-glob", "version": "3.3.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.3.1", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "784b4e897340f3dbbef17413b3f11acf03c874c4", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.1.tgz", "fileCount": 55, "integrity": "sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==", "signatures": [{"sig": "MEQCIFI7HBmyQnGeuBcJXU1HxfRBbfjqmLS3Mm2RtZ/AnQkHAiAfXy4dmj/XLCbH318y5eDE6GWxjVkPPfICimz7KThnWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96717}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "4ba3fcd6bd20cf31b8da71f8f42514d103976abf", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "test:e2e": "mocha \"out/**/*.e2e.js\" -s 0", "bench:sync": "npm run bench:product:sync && npm run bench:regression:sync", "bench:async": "npm run bench:product:async && npm run bench:regression:async", "bench:stream": "npm run bench:product:stream && npm run bench:regression:stream", "bench:product": "npm run bench:product:async && npm run bench:product:sync && npm run bench:product:stream", "test:e2e:sync": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(sync\\)\"", "test:e2e:async": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(async\\)\"", "test:e2e:stream": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(stream\\)\"", "bench:regression": "npm run bench:regression:async && npm run bench:regression:sync && npm run bench:regression:stream", "bench:product:sync": "hereby bench:product:sync", "bench:product:async": "hereby bench:product:async", "bench:product:stream": "hereby bench:product:stream", "bench:regression:sync": "hereby bench:regression:sync", "bench:regression:async": "hereby bench:regression:async", "bench:regression:stream": "hereby bench:regression:stream"}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^6.0.1", "glob": "^10.0.0", "execa": "^7.1.1", "mocha": "^6.2.1", "sinon": "^7.5.0", "bencho": "^0.1.1", "eslint": "^6.5.1", "hereby": "^1.8.1", "rimraf": "^5.0.0", "fast-glob": "^3.0.4", "typescript": "^4.9.5", "@types/node": "^14.18.53", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "snap-shot-it": "^7.9.10", "@types/merge2": "^1.1.4", "@types/picomatch": "^2.3.0", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.3.1_1690015526338_0.772162782881564", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "fast-glob", "version": "3.3.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"url": "https://mrmlnc.com", "name": "<PERSON>"}, "license": "MIT", "_id": "fast-glob@3.3.2", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "dist": {"shasum": "a904501e57cfdd2ffcded45e99a54fef55e46129", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz", "fileCount": 55, "integrity": "sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==", "signatures": [{"sig": "MEYCIQCCBKQONscueP27VlojRAa7yPlVtw39JvhkA3gZXC0LnwIhAO6KTrPxBHD2bshSM5V4wjuA9s84wpd0f0hbv0Do5eF7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96746}, "main": "out/index.js", "engines": {"node": ">=8.6.0"}, "gitHead": "e60a9f5f09bc58a3b22b1d7fb767c25f62df0d07", "scripts": {"lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "compile": "tsc", "test:e2e": "mocha \"out/**/*.e2e.js\" -s 0", "bench:sync": "npm run bench:product:sync && npm run bench:regression:sync", "bench:async": "npm run bench:product:async && npm run bench:regression:async", "bench:stream": "npm run bench:product:stream && npm run bench:regression:stream", "bench:product": "npm run bench:product:async && npm run bench:product:sync && npm run bench:product:stream", "test:e2e:sync": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(sync\\)\"", "test:e2e:async": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(async\\)\"", "test:e2e:stream": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(stream\\)\"", "bench:regression": "npm run bench:regression:async && npm run bench:regression:sync && npm run bench:regression:stream", "bench:product:sync": "hereby bench:product:sync", "bench:product:async": "hereby bench:product:async", "bench:product:stream": "hereby bench:product:stream", "bench:regression:sync": "hereby bench:regression:sync", "bench:regression:async": "hereby bench:regression:async", "bench:regression:stream": "hereby bench:regression:stream"}, "typings": "out/index.d.ts", "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "repository": {"url": "git+https://github.com/mrmlnc/fast-glob.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "It's a very fast and efficient glob library for Node.js", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"merge2": "^1.3.0", "micromatch": "^4.0.4", "glob-parent": "^5.1.2", "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"fdir": "^6.0.1", "glob": "^10.0.0", "execa": "^7.1.1", "mocha": "^6.2.1", "sinon": "^7.5.0", "bencho": "^0.1.1", "eslint": "^6.5.1", "hereby": "^1.8.1", "rimraf": "^5.0.0", "fast-glob": "^3.0.4", "typescript": "^4.9.5", "@types/node": "^14.18.53", "@types/mocha": "^5.2.7", "@types/sinon": "^7.5.0", "snap-shot-it": "^7.9.10", "@types/merge2": "^1.1.4", "@types/picomatch": "^2.3.0", "@types/micromatch": "^4.0.0", "@types/glob-parent": "^5.1.0", "eslint-config-mrmlnc": "^1.1.0", "@nodelib/fs.macchiato": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-glob_3.3.2_1699274471033_0.7084235969486619", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "fast-glob", "version": "3.3.3", "description": "It's a very fast and efficient glob library for Node.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mrmlnc/fast-glob.git"}, "author": {"name": "<PERSON>", "url": "https://mrmlnc.com"}, "engines": {"node": ">=8.6.0"}, "main": "out/index.js", "typings": "out/index.d.ts", "keywords": ["glob", "patterns", "fast", "implementation"], "devDependencies": {"@nodelib/fs.macchiato": "^1.0.1", "@types/glob-parent": "^5.1.0", "@types/merge2": "^1.1.4", "@types/micromatch": "^4.0.0", "@types/mocha": "^5.2.7", "@types/node": "^14.18.53", "@types/picomatch": "^2.3.0", "@types/sinon": "^7.5.0", "bencho": "^0.1.1", "eslint": "^6.5.1", "eslint-config-mrmlnc": "^1.1.0", "execa": "^7.1.1", "fast-glob": "^3.0.4", "fdir": "6.0.1", "glob": "^10.0.0", "hereby": "^1.8.1", "mocha": "^6.2.1", "rimraf": "^5.0.0", "sinon": "^7.5.0", "snap-shot-it": "^7.9.10", "typescript": "^4.9.5"}, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> out", "lint": "eslint \"src/**/*.ts\" --cache", "compile": "tsc", "test": "mocha \"out/**/*.spec.js\" -s 0", "test:e2e": "mocha \"out/**/*.e2e.js\" -s 0", "test:e2e:sync": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(sync\\)\"", "test:e2e:async": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(async\\)\"", "test:e2e:stream": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(stream\\)\"", "build": "npm run clean && npm run compile && npm run lint && npm test", "watch": "npm run clean && npm run compile -- -- --sourceMap --watch", "bench:async": "npm run bench:product:async && npm run bench:regression:async", "bench:stream": "npm run bench:product:stream && npm run bench:regression:stream", "bench:sync": "npm run bench:product:sync && npm run bench:regression:sync", "bench:product": "npm run bench:product:async && npm run bench:product:sync && npm run bench:product:stream", "bench:product:async": "hereby bench:product:async", "bench:product:sync": "hereby bench:product:sync", "bench:product:stream": "hereby bench:product:stream", "bench:regression": "npm run bench:regression:async && npm run bench:regression:sync && npm run bench:regression:stream", "bench:regression:async": "hereby bench:regression:async", "bench:regression:sync": "hereby bench:regression:sync", "bench:regression:stream": "hereby bench:regression:stream"}, "_id": "fast-glob@3.3.3", "gitHead": "48687898dd26d4e935a0e5ecf6720e7c5aeac15d", "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "homepage": "https://github.com/mrmlnc/fast-glob#readme", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "shasum": "d06d585ce8dba90a16b0505c543c3ccfb3aeb818", "tarball": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fileCount": 55, "unpackedSize": 98396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICTjokEWb6KQHVjqCGFBsmTVyNXGzhTR6Go+PYGeHq9SAiEApP3aaGSYHbb2YmHZecGkGpQUM0m+IcTsKdciYSSOsso="}]}, "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "directories": {}, "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fast-glob_3.3.3_1736073522044_0.8866060458706542"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-12-28T10:49:04.653Z", "modified": "2025-01-05T10:38:42.437Z", "1.0.0": "2016-12-28T10:49:04.653Z", "1.0.1": "2017-06-03T08:40:45.008Z", "2.0.0": "2018-01-21T16:02:02.611Z", "2.0.1": "2018-01-21T16:25:38.937Z", "2.0.2": "2018-02-01T06:10:56.515Z", "2.0.3": "2018-02-12T06:35:08.980Z", "2.0.4": "2018-02-13T19:35:39.166Z", "2.1.0": "2018-02-25T16:17:00.675Z", "2.2.0": "2018-03-11T16:37:55.183Z", "2.2.1": "2018-04-22T17:46:17.711Z", "2.2.2": "2018-05-15T19:53:24.120Z", "2.2.3": "2018-10-02T08:34:15.452Z", "2.2.4": "2018-11-11T14:48:51.148Z", "2.2.5": "2019-01-07T18:44:00.574Z", "2.2.6": "2019-01-07T20:46:17.389Z", "2.2.7": "2019-05-18T11:27:21.751Z", "3.0.0": "2019-06-16T18:13:24.079Z", "3.0.1": "2019-06-17T18:42:38.279Z", "3.0.2": "2019-06-23T13:35:12.338Z", "3.0.3": "2019-06-27T19:02:38.960Z", "3.0.4": "2019-07-05T19:15:17.666Z", "3.1.0": "2019-10-06T10:01:46.920Z", "3.1.1": "2019-12-01T10:36:25.210Z", "3.2.0-beta": "2020-02-04T10:10:13.694Z", "3.2.0-beta.2": "2020-02-09T15:10:39.946Z", "3.2.0": "2020-02-15T11:22:09.488Z", "3.2.1-beta.0": "2020-02-20T18:13:04.219Z", "3.2.1-beta.1": "2020-02-20T18:39:02.767Z", "3.2.1": "2020-02-20T18:47:12.065Z", "3.2.2": "2020-02-21T20:49:35.804Z", "3.2.3": "2020-06-15T17:00:57.444Z", "3.2.4": "2020-06-16T07:18:20.539Z", "3.2.5": "2021-01-17T10:13:50.463Z", "3.2.6": "2021-06-27T17:12:01.246Z", "3.2.7": "2021-07-08T19:25:30.242Z", "3.2.8": "2022-01-07T10:15:33.000Z", "3.2.9": "2022-01-07T20:53:18.325Z", "3.2.10": "2022-01-09T13:40:27.431Z", "3.2.11": "2022-01-14T19:38:13.065Z", "3.2.12": "2022-09-09T06:40:27.748Z", "3.3.0": "2023-06-30T13:04:05.922Z", "3.3.1": "2023-07-22T08:45:26.553Z", "3.3.2": "2023-11-06T12:41:11.203Z", "3.3.3": "2025-01-05T10:38:42.236Z"}, "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "author": {"name": "<PERSON>", "url": "https://mrmlnc.com"}, "license": "MIT", "homepage": "https://github.com/mrmlnc/fast-glob#readme", "keywords": ["glob", "patterns", "fast", "implementation"], "repository": {"type": "git", "url": "git+https://github.com/mrmlnc/fast-glob.git"}, "description": "It's a very fast and efficient glob library for Node.js", "maintainers": [{"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}], "readme": "# fast-glob\n\n> It's a very fast and efficient [glob][glob_definition] library for [Node.js][node_js].\n\nThis package provides methods for traversing the file system and returning pathnames that matched a defined set of a specified pattern according to the rules used by the Unix Bash shell with some simplifications, meanwhile results are returned in **arbitrary order**. Quick, simple, effective.\n\n## Table of Contents\n\n<details>\n<summary><strong>Details</strong></summary>\n\n* [Highlights](#highlights)\n* [Old and modern mode](#old-and-modern-mode)\n* [Pattern syntax](#pattern-syntax)\n  * [Basic syntax](#basic-syntax)\n  * [Advanced syntax](#advanced-syntax)\n* [Installation](#installation)\n* [API](#api)\n  * [Asynchronous](#asynchronous)\n  * [Synchronous](#synchronous)\n  * [Stream](#stream)\n    * [patterns](#patterns)\n    * [[options]](#options)\n  * [Helpers](#helpers)\n    * [generateTasks](#generatetaskspatterns-options)\n    * [isDynamicPattern](#isdynamicpatternpattern-options)\n    * [escapePath](#escapepathpath)\n\t* [convertPathToPattern](#convertpathtopatternpath)\n* [Options](#options-3)\n  * [Common](#common)\n    * [concurrency](#concurrency)\n    * [cwd](#cwd)\n    * [deep](#deep)\n    * [followSymbolicLinks](#followsymboliclinks)\n    * [fs](#fs)\n    * [ignore](#ignore)\n    * [suppressErrors](#suppresserrors)\n    * [throwErrorOnBrokenSymbolicLink](#throwerroronbrokensymboliclink)\n  * [Output control](#output-control)\n    * [absolute](#absolute)\n    * [markDirectories](#markdirectories)\n    * [objectMode](#objectmode)\n    * [onlyDirectories](#onlydirectories)\n    * [onlyFiles](#onlyfiles)\n    * [stats](#stats)\n    * [unique](#unique)\n  * [Matching control](#matching-control)\n    * [braceExpansion](#braceexpansion)\n    * [caseSensitiveMatch](#casesensitivematch)\n    * [dot](#dot)\n    * [extglob](#extglob)\n    * [globstar](#globstar)\n    * [baseNameMatch](#basenamematch)\n* [FAQ](#faq)\n  * [What is a static or dynamic pattern?](#what-is-a-static-or-dynamic-pattern)\n  * [How to write patterns on Windows?](#how-to-write-patterns-on-windows)\n  * [Why are parentheses match wrong?](#why-are-parentheses-match-wrong)\n  * [How to exclude directory from reading?](#how-to-exclude-directory-from-reading)\n  * [How to use UNC path?](#how-to-use-unc-path)\n  * [Compatible with `node-glob`?](#compatible-with-node-glob)\n* [Benchmarks](#benchmarks)\n  * [Server](#server)\n  * [Nettop](#nettop)\n* [Changelog](#changelog)\n* [License](#license)\n\n</details>\n\n## Highlights\n\n* Fast. Probably the fastest.\n* Supports multiple and negative patterns.\n* Synchronous, Promise and Stream API.\n* Object mode. Can return more than just strings.\n* Error-tolerant.\n\n## Old and modern mode\n\nThis package works in two modes, depending on the environment in which it is used.\n\n* **Old mode**. Node.js below 10.10 or when the [`stats`](#stats) option is *enabled*.\n* **Modern mode**. Node.js 10.10+ and the [`stats`](#stats) option is *disabled*.\n\nThe modern mode is faster. Learn more about the [internal mechanism][nodelib_fs_scandir_old_and_modern_modern].\n\n## Pattern syntax\n\n> :warning: Always use forward-slashes in glob expressions (patterns and [`ignore`](#ignore) option). Use backslashes for escaping characters.\n\nThere is more than one form of syntax: basic and advanced. Below is a brief overview of the supported features. Also pay attention to our [FAQ](#faq).\n\n> :book: This package uses [`micromatch`][micromatch] as a library for pattern matching.\n\n### Basic syntax\n\n* An asterisk (`*`) — matches everything except slashes (path separators), hidden files (names starting with `.`).\n* A double star or globstar (`**`) — matches zero or more directories.\n* Question mark (`?`) – matches any single character except slashes (path separators).\n* Sequence (`[seq]`) — matches any character in sequence.\n\n> :book: A few additional words about the [basic matching behavior][picomatch_matching_behavior].\n\nSome examples:\n\n* `src/**/*.js` — matches all files in the `src` directory (any level of nesting) that have the `.js` extension.\n* `src/*.??` — matches all files in the `src` directory (only first level of nesting) that have a two-character extension.\n* `file-[01].js` — matches files: `file-0.js`, `file-1.js`.\n\n### Advanced syntax\n\n* [Escapes characters][micromatch_backslashes] (`\\\\`) — matching special characters (`$^*+?()[]`) as literals.\n* [POSIX character classes][picomatch_posix_brackets] (`[[:digit:]]`).\n* [Extended globs][micromatch_extglobs] (`?(pattern-list)`).\n* [Bash style brace expansions][micromatch_braces] (`{}`).\n* [Regexp character classes][micromatch_regex_character_classes] (`[1-5]`).\n* [Regex groups][regular_expressions_brackets] (`(a|b)`).\n\n> :book: A few additional words about the [advanced matching behavior][micromatch_extended_globbing].\n\nSome examples:\n\n* `src/**/*.{css,scss}` — matches all files in the `src` directory (any level of nesting) that have the `.css` or `.scss` extension.\n* `file-[[:digit:]].js` — matches files: `file-0.js`, `file-1.js`, …, `file-9.js`.\n* `file-{1..3}.js` — matches files: `file-1.js`, `file-2.js`, `file-3.js`.\n* `file-(1|2)` — matches files: `file-1.js`, `file-2.js`.\n\n## Installation\n\n```console\nnpm install fast-glob\n```\n\n## API\n\n### Asynchronous\n\n```js\nfg(patterns, [options])\nfg.async(patterns, [options])\nfg.glob(patterns, [options])\n```\n\nReturns a `Promise` with an array of matching entries.\n\n```js\nconst fg = require('fast-glob');\n\nconst entries = await fg(['.editorconfig', '**/index.js'], { dot: true });\n\n// ['.editorconfig', 'services/index.js']\n```\n\n### Synchronous\n\n```js\nfg.sync(patterns, [options])\nfg.globSync(patterns, [options])\n```\n\nReturns an array of matching entries.\n\n```js\nconst fg = require('fast-glob');\n\nconst entries = fg.sync(['.editorconfig', '**/index.js'], { dot: true });\n\n// ['.editorconfig', 'services/index.js']\n```\n\n### Stream\n\n```js\nfg.stream(patterns, [options])\nfg.globStream(patterns, [options])\n```\n\nReturns a [`ReadableStream`][node_js_stream_readable_streams] when the `data` event will be emitted with matching entry.\n\n```js\nconst fg = require('fast-glob');\n\nconst stream = fg.stream(['.editorconfig', '**/index.js'], { dot: true });\n\nfor await (const entry of stream) {\n\t// .editorconfig\n\t// services/index.js\n}\n```\n\n#### patterns\n\n* Required: `true`\n* Type: `string | string[]`\n\nAny correct pattern(s).\n\n> :1234: [Pattern syntax](#pattern-syntax)\n>\n> :warning: This package does not respect the order of patterns. First, all the negative patterns are applied, and only then the positive patterns. If you want to get a certain order of records, use sorting or split calls.\n\n#### [options]\n\n* Required: `false`\n* Type: [`Options`](#options-3)\n\nSee [Options](#options-3) section.\n\n### Helpers\n\n#### `generateTasks(patterns, [options])`\n\nReturns the internal representation of patterns ([`Task`](./src/managers/tasks.ts) is a combining patterns by base directory).\n\n```js\nfg.generateTasks('*');\n\n[{\n    base: '.', // Parent directory for all patterns inside this task\n    dynamic: true, // Dynamic or static patterns are in this task\n    patterns: ['*'],\n    positive: ['*'],\n    negative: []\n}]\n```\n\n##### patterns\n\n* Required: `true`\n* Type: `string | string[]`\n\nAny correct pattern(s).\n\n##### [options]\n\n* Required: `false`\n* Type: [`Options`](#options-3)\n\nSee [Options](#options-3) section.\n\n#### `isDynamicPattern(pattern, [options])`\n\nReturns `true` if the passed pattern is a dynamic pattern.\n\n> :1234: [What is a static or dynamic pattern?](#what-is-a-static-or-dynamic-pattern)\n\n```js\nfg.isDynamicPattern('*'); // true\nfg.isDynamicPattern('abc'); // false\n```\n\n##### pattern\n\n* Required: `true`\n* Type: `string`\n\nAny correct pattern.\n\n##### [options]\n\n* Required: `false`\n* Type: [`Options`](#options-3)\n\nSee [Options](#options-3) section.\n\n#### `escapePath(path)`\n\nReturns the path with escaped special characters depending on the platform.\n\n* Posix:\n  * `*?|(){}[]`;\n  * `!` at the beginning of line;\n  * `@+!` before the opening parenthesis;\n  * `\\\\` before non-special characters;\n* Windows:\n  * `(){}[]`\n  * `!` at the beginning of line;\n  * `@+!` before the opening parenthesis;\n  * Characters like `*?|` cannot be used in the path ([windows_naming_conventions][windows_naming_conventions]), so they will not be escaped;\n\n```js\nfg.escapePath('!abc');\n// \\\\!abc\nfg.escapePath('[OpenSource] mrmlnc – fast-glob (Deluxe Edition) 2014') + '/*.flac'\n// \\\\[OpenSource\\\\] mrmlnc – fast-glob \\\\(Deluxe Edition\\\\) 2014/*.flac\n\nfg.posix.escapePath('C:\\\\Program Files (x86)\\\\**\\\\*');\n// C:\\\\\\\\Program Files \\\\(x86\\\\)\\\\*\\\\*\\\\*\nfg.win32.escapePath('C:\\\\Program Files (x86)\\\\**\\\\*');\n// Windows: C:\\\\Program Files \\\\(x86\\\\)\\\\**\\\\*\n```\n\n#### `convertPathToPattern(path)`\n\nConverts a path to a pattern depending on the platform, including special character escaping.\n\n* Posix. Works similarly to the `fg.posix.escapePath` method.\n* Windows. Works similarly to the `fg.win32.escapePath` method, additionally converting backslashes to forward slashes in cases where they are not escape characters (`!()+@{}[]`).\n\n```js\nfg.convertPathToPattern('[OpenSource] mrmlnc – fast-glob (Deluxe Edition) 2014') + '/*.flac';\n// \\\\[OpenSource\\\\] mrmlnc – fast-glob \\\\(Deluxe Edition\\\\) 2014/*.flac\n\nfg.convertPathToPattern('C:/Program Files (x86)/**/*');\n// Posix: C:/Program Files \\\\(x86\\\\)/\\\\*\\\\*/\\\\*\n// Windows: C:/Program Files \\\\(x86\\\\)/**/*\n\nfg.convertPathToPattern('C:\\\\Program Files (x86)\\\\**\\\\*');\n// Posix: C:\\\\\\\\Program Files \\\\(x86\\\\)\\\\*\\\\*\\\\*\n// Windows: C:/Program Files \\\\(x86\\\\)/**/*\n\nfg.posix.convertPathToPattern('\\\\\\\\?\\\\c:\\\\Program Files (x86)') + '/**/*';\n// Posix: \\\\\\\\\\\\?\\\\\\\\c:\\\\\\\\Program Files \\\\(x86\\\\)/**/* (broken pattern)\nfg.win32.convertPathToPattern('\\\\\\\\?\\\\c:\\\\Program Files (x86)') + '/**/*';\n// Windows: //?/c:/Program Files \\\\(x86\\\\)/**/*\n```\n\n## Options\n\n### Common options\n\n#### concurrency\n\n* Type: `number`\n* Default: `os.cpus().length`\n\nSpecifies the maximum number of concurrent requests from a reader to read directories.\n\n> :book: The higher the number, the higher the performance and load on the file system. If you want to read in quiet mode, set the value to a comfortable number or `1`.\n\n<details>\n\n<summary>More details</summary>\n\nIn Node, there are [two types of threads][nodejs_thread_pool]: Event Loop (code) and a Thread Pool (fs, dns, …). The thread pool size controlled by the `UV_THREADPOOL_SIZE` environment variable. Its default size is 4 ([documentation][libuv_thread_pool]). The pool is one for all tasks within a single Node process.\n\nAny code can make 4 real concurrent accesses to the file system. The rest of the FS requests will wait in the queue.\n\n> :book: Each new instance of FG in the same Node process will use the same Thread pool.\n\nBut this package also has the `concurrency` option. This option allows you to control the number of concurrent accesses to the FS at the package level. By default, this package has a value equal to the number of cores available for the current Node process. This allows you to set a value smaller than the pool size (`concurrency: 1`) or, conversely, to prepare tasks for the pool queue more quickly (`concurrency: Number.POSITIVE_INFINITY`).\n\nSo, in fact, this package can **only make 4 concurrent requests to the FS**. You can increase this value by using an environment variable (`UV_THREADPOOL_SIZE`), but in practice this does not give a multiple advantage.\n\n</details>\n\n#### cwd\n\n* Type: `string`\n* Default: `process.cwd()`\n\nThe current working directory in which to search.\n\n#### deep\n\n* Type: `number`\n* Default: `Infinity`\n\nSpecifies the maximum depth of a read directory relative to the start directory.\n\nFor example, you have the following tree:\n\n```js\ndir/\n└── one/            // 1\n    └── two/        // 2\n        └── file.js // 3\n```\n\n```js\n// With base directory\nfg.sync('dir/**', { onlyFiles: false, deep: 1 }); // ['dir/one']\nfg.sync('dir/**', { onlyFiles: false, deep: 2 }); // ['dir/one', 'dir/one/two']\n\n// With cwd option\nfg.sync('**', { onlyFiles: false, cwd: 'dir', deep: 1 }); // ['one']\nfg.sync('**', { onlyFiles: false, cwd: 'dir', deep: 2 }); // ['one', 'one/two']\n```\n\n> :book: If you specify a pattern with some base directory, this directory will not participate in the calculation of the depth of the found directories. Think of it as a [`cwd`](#cwd) option.\n\n#### followSymbolicLinks\n\n* Type: `boolean`\n* Default: `true`\n\nIndicates whether to traverse descendants of symbolic link directories when expanding `**` patterns.\n\n> :book: Note that this option does not affect the base directory of the pattern. For example, if `./a` is a symlink to directory `./b` and you specified `['./a**', './b/**']` patterns, then directory `./a` will still be read.\n\n> :book: If the [`stats`](#stats) option is specified, the information about the symbolic link (`fs.lstat`) will be replaced with information about the entry (`fs.stat`) behind it.\n\n#### fs\n\n* Type: `FileSystemAdapter`\n* Default: `fs.*`\n\nCustom implementation of methods for working with the file system. Supports objects with enumerable properties only.\n\n```ts\nexport interface FileSystemAdapter {\n    lstat?: typeof fs.lstat;\n    stat?: typeof fs.stat;\n    lstatSync?: typeof fs.lstatSync;\n    statSync?: typeof fs.statSync;\n    readdir?: typeof fs.readdir;\n    readdirSync?: typeof fs.readdirSync;\n}\n```\n\n#### ignore\n\n* Type: `string[]`\n* Default: `[]`\n\nAn array of glob patterns to exclude matches. This is an alternative way to use negative patterns.\n\n```js\ndir/\n├── package-lock.json\n└── package.json\n```\n\n```js\nfg.sync(['*.json', '!package-lock.json']);            // ['package.json']\nfg.sync('*.json', { ignore: ['package-lock.json'] }); // ['package.json']\n```\n\n#### suppressErrors\n\n* Type: `boolean`\n* Default: `false`\n\nBy default this package suppress only `ENOENT` errors. Set to `true` to suppress any error.\n\n> :book: Can be useful when the directory has entries with a special level of access.\n\n#### throwErrorOnBrokenSymbolicLink\n\n* Type: `boolean`\n* Default: `false`\n\nThrow an error when symbolic link is broken if `true` or safely return `lstat` call if `false`.\n\n> :book: This option has no effect on errors when reading the symbolic link directory.\n\n### Output control\n\n#### absolute\n\n* Type: `boolean`\n* Default: `false`\n\nReturn the absolute path for entries.\n\n```js\nfg.sync('*.js', { absolute: false }); // ['index.js']\nfg.sync('*.js', { absolute: true });  // ['/home/<USER>/index.js']\n```\n\n> :book: This option is required if you want to use negative patterns with absolute path, for example, `!${__dirname}/*.js`.\n\n#### markDirectories\n\n* Type: `boolean`\n* Default: `false`\n\nMark the directory path with the final slash.\n\n```js\nfg.sync('*', { onlyFiles: false, markDirectories: false }); // ['index.js', 'controllers']\nfg.sync('*', { onlyFiles: false, markDirectories: true });  // ['index.js', 'controllers/']\n```\n\n#### objectMode\n\n* Type: `boolean`\n* Default: `false`\n\nReturns objects (instead of strings) describing entries.\n\n```js\nfg.sync('*', { objectMode: false }); // ['src/index.js']\nfg.sync('*', { objectMode: true });  // [{ name: 'index.js', path: 'src/index.js', dirent: <fs.Dirent> }]\n```\n\nThe object has the following fields:\n\n* name (`string`) — the last part of the path (basename)\n* path (`string`) — full path relative to the pattern base directory\n* dirent ([`fs.Dirent`][node_js_fs_class_fs_dirent]) — instance of `fs.Dirent`\n\n> :book: An object is an internal representation of entry, so getting it does not affect performance.\n\n#### onlyDirectories\n\n* Type: `boolean`\n* Default: `false`\n\nReturn only directories.\n\n```js\nfg.sync('*', { onlyDirectories: false }); // ['index.js', 'src']\nfg.sync('*', { onlyDirectories: true });  // ['src']\n```\n\n> :book: If `true`, the [`onlyFiles`](#onlyfiles) option is automatically `false`.\n\n#### onlyFiles\n\n* Type: `boolean`\n* Default: `true`\n\nReturn only files.\n\n```js\nfg.sync('*', { onlyFiles: false }); // ['index.js', 'src']\nfg.sync('*', { onlyFiles: true });  // ['index.js']\n```\n\n#### stats\n\n* Type: `boolean`\n* Default: `false`\n\nEnables an [object mode](#objectmode) with an additional field:\n\n* stats ([`fs.Stats`][node_js_fs_class_fs_stats]) — instance of `fs.Stats`\n\n```js\nfg.sync('*', { stats: false }); // ['src/index.js']\nfg.sync('*', { stats: true });  // [{ name: 'index.js', path: 'src/index.js', dirent: <fs.Dirent>, stats: <fs.Stats> }]\n```\n\n> :book: Returns `fs.stat` instead of `fs.lstat` for symbolic links when the [`followSymbolicLinks`](#followsymboliclinks) option is specified.\n>\n> :warning: Unlike [object mode](#objectmode) this mode requires additional calls to the file system. On average, this mode is slower at least twice. See [old and modern mode](#old-and-modern-mode) for more details.\n\n#### unique\n\n* Type: `boolean`\n* Default: `true`\n\nEnsures that the returned entries are unique.\n\n```js\nfg.sync(['*.json', 'package.json'], { unique: false }); // ['package.json', 'package.json']\nfg.sync(['*.json', 'package.json'], { unique: true });  // ['package.json']\n```\n\nIf `true` and similar entries are found, the result is the first found.\n\n### Matching control\n\n#### braceExpansion\n\n* Type: `boolean`\n* Default: `true`\n\nEnables Bash-like brace expansion.\n\n> :1234: [Syntax description][bash_hackers_syntax_expansion_brace] or more [detailed description][micromatch_braces].\n\n```js\ndir/\n├── abd\n├── acd\n└── a{b,c}d\n```\n\n```js\nfg.sync('a{b,c}d', { braceExpansion: false }); // ['a{b,c}d']\nfg.sync('a{b,c}d', { braceExpansion: true });  // ['abd', 'acd']\n```\n\n#### caseSensitiveMatch\n\n* Type: `boolean`\n* Default: `true`\n\nEnables a [case-sensitive][wikipedia_case_sensitivity] mode for matching files.\n\n```js\ndir/\n├── file.txt\n└── File.txt\n```\n\n```js\nfg.sync('file.txt', { caseSensitiveMatch: false }); // ['file.txt', 'File.txt']\nfg.sync('file.txt', { caseSensitiveMatch: true });  // ['file.txt']\n```\n\n#### dot\n\n* Type: `boolean`\n* Default: `false`\n\nAllow patterns to match entries that begin with a period (`.`).\n\n> :book: Note that an explicit dot in a portion of the pattern will always match dot files.\n\n```js\ndir/\n├── .editorconfig\n└── package.json\n```\n\n```js\nfg.sync('*', { dot: false }); // ['package.json']\nfg.sync('*', { dot: true });  // ['.editorconfig', 'package.json']\n```\n\n#### extglob\n\n* Type: `boolean`\n* Default: `true`\n\nEnables Bash-like `extglob` functionality.\n\n> :1234: [Syntax description][micromatch_extglobs].\n\n```js\ndir/\n├── README.md\n└── package.json\n```\n\n```js\nfg.sync('*.+(json|md)', { extglob: false }); // []\nfg.sync('*.+(json|md)', { extglob: true });  // ['README.md', 'package.json']\n```\n\n#### globstar\n\n* Type: `boolean`\n* Default: `true`\n\nEnables recursively repeats a pattern containing `**`. If `false`, `**` behaves exactly like `*`.\n\n```js\ndir/\n└── a\n    └── b\n```\n\n```js\nfg.sync('**', { onlyFiles: false, globstar: false }); // ['a']\nfg.sync('**', { onlyFiles: false, globstar: true });  // ['a', 'a/b']\n```\n\n#### baseNameMatch\n\n* Type: `boolean`\n* Default: `false`\n\nIf set to `true`, then patterns without slashes will be matched against the basename of the path if it contains slashes.\n\n```js\ndir/\n└── one/\n    └── file.md\n```\n\n```js\nfg.sync('*.md', { baseNameMatch: false }); // []\nfg.sync('*.md', { baseNameMatch: true });  // ['one/file.md']\n```\n\n## FAQ\n\n## What is a static or dynamic pattern?\n\nAll patterns can be divided into two types:\n\n* **static**. A pattern is considered static if it can be used to get an entry on the file system without using matching mechanisms. For example, the `file.js` pattern is a static pattern because we can just verify that it exists on the file system.\n* **dynamic**. A pattern is considered dynamic if it cannot be used directly to find occurrences without using a matching mechanisms. For example, the `*` pattern is a dynamic pattern because we cannot use this pattern directly.\n\nA pattern is considered dynamic if it contains the following characters (`…` — any characters or their absence) or options:\n\n* The [`caseSensitiveMatch`](#casesensitivematch) option is disabled\n* `\\\\` (the escape character)\n* `*`, `?`, `!` (at the beginning of line)\n* `[…]`\n* `(…|…)`\n* `@(…)`, `!(…)`, `*(…)`, `?(…)`, `+(…)` (respects the [`extglob`](#extglob) option)\n* `{…,…}`, `{…..…}` (respects the [`braceExpansion`](#braceexpansion) option)\n\n## How to write patterns on Windows?\n\nAlways use forward-slashes in glob expressions (patterns and [`ignore`](#ignore) option). Use backslashes for escaping characters. With the [`cwd`](#cwd) option use a convenient format.\n\n**Bad**\n\n```ts\n[\n\t'directory\\\\*',\n\tpath.join(process.cwd(), '**')\n]\n```\n\n**Good**\n\n```ts\n[\n\t'directory/*',\n\tfg.convertPathToPattern(process.cwd()) + '/**'\n]\n```\n\n> :book: Use the [`.convertPathToPattern`](#convertpathtopatternpath) package to convert Windows-style path to a Unix-style path.\n\nRead more about [matching with backslashes][micromatch_backslashes].\n\n## Why are parentheses match wrong?\n\n```js\ndir/\n└── (special-*file).txt\n```\n\n```js\nfg.sync(['(special-*file).txt']) // []\n```\n\nRefers to Bash. You need to escape special characters:\n\n```js\nfg.sync(['\\\\(special-*file\\\\).txt']) // ['(special-*file).txt']\n```\n\nRead more about [matching special characters as literals][picomatch_matching_special_characters_as_literals]. Or use the [`.escapePath`](#escapepathpath).\n\n## How to exclude directory from reading?\n\nYou can use a negative pattern like this: `!**/node_modules` or `!**/node_modules/**`. Also you can use [`ignore`](#ignore) option. Just look at the example below.\n\n```js\nfirst/\n├── file.md\n└── second/\n    └── file.txt\n```\n\nIf you don't want to read the `second` directory, you must write the following pattern: `!**/second` or `!**/second/**`.\n\n```js\nfg.sync(['**/*.md', '!**/second']);                 // ['first/file.md']\nfg.sync(['**/*.md'], { ignore: ['**/second/**'] }); // ['first/file.md']\n```\n\n> :warning: When you write `!**/second/**/*` it means that the directory will be **read**, but all the entries will not be included in the results.\n\nYou have to understand that if you write the pattern to exclude directories, then the directory will not be read under any circumstances.\n\n## How to use UNC path?\n\nYou cannot use [Uniform Naming Convention (UNC)][unc_path] paths as patterns (due to syntax) directly, but you can use them as [`cwd`](#cwd) directory or use the `fg.convertPathToPattern` method.\n\n```ts\n// cwd\nfg.sync('*', { cwd: '\\\\\\\\?\\\\C:\\\\Python27' /* or //?/C:/Python27 */ });\nfg.sync('Python27/*', { cwd: '\\\\\\\\?\\\\C:\\\\' /* or //?/C:/ */ });\n\n// .convertPathToPattern\nfg.sync(fg.convertPathToPattern('\\\\\\\\?\\\\c:\\\\Python27') + '/*');\n```\n\n## Compatible with `node-glob`?\n\n| node-glob    | fast-glob |\n| :----------: | :-------: |\n| `cwd`        | [`cwd`](#cwd) |\n| `root`       | – |\n| `dot`        | [`dot`](#dot) |\n| `nomount`    | – |\n| `mark`       | [`markDirectories`](#markdirectories) |\n| `nosort`     | – |\n| `nounique`   | [`unique`](#unique) |\n| `nobrace`    | [`braceExpansion`](#braceexpansion) |\n| `noglobstar` | [`globstar`](#globstar) |\n| `noext`      | [`extglob`](#extglob) |\n| `nocase`     | [`caseSensitiveMatch`](#casesensitivematch) |\n| `matchBase`  | [`baseNameMatch`](#basenamematch) |\n| `nodir`      | [`onlyFiles`](#onlyfiles) |\n| `ignore`     | [`ignore`](#ignore) |\n| `follow`     | [`followSymbolicLinks`](#followsymboliclinks) |\n| `realpath`   | – |\n| `absolute`   | [`absolute`](#absolute) |\n\n## Benchmarks\n\nYou can see results [here](https://github.com/mrmlnc/fast-glob/actions/workflows/benchmark.yml?query=branch%3Amaster) for every commit into the `main` branch.\n\n* **Product benchmark** – comparison with the main competitors.\n* **Regress benchmark** – regression between the current version and the version from the npm registry.\n\n## Changelog\n\nSee the [Releases section of our GitHub project][github_releases] for changelog for each release version.\n\n## License\n\nThis software is released under the terms of the MIT license.\n\n[bash_hackers_syntax_expansion_brace]: https://wiki.bash-hackers.org/syntax/expansion/brace\n[github_releases]: https://github.com/mrmlnc/fast-glob/releases\n[glob_definition]: https://en.wikipedia.org/wiki/Glob_(programming)\n[glob_linux_man]: http://man7.org/linux/man-pages/man3/glob.3.html\n[micromatch_backslashes]: https://github.com/micromatch/micromatch#backslashes\n[micromatch_braces]: https://github.com/micromatch/braces\n[micromatch_extended_globbing]: https://github.com/micromatch/micromatch#extended-globbing\n[micromatch_extglobs]: https://github.com/micromatch/micromatch#extglobs\n[micromatch_regex_character_classes]: https://github.com/micromatch/micromatch#regex-character-classes\n[micromatch]: https://github.com/micromatch/micromatch\n[node_js_fs_class_fs_dirent]: https://nodejs.org/api/fs.html#fs_class_fs_dirent\n[node_js_fs_class_fs_stats]: https://nodejs.org/api/fs.html#fs_class_fs_stats\n[node_js_stream_readable_streams]: https://nodejs.org/api/stream.html#stream_readable_streams\n[node_js]: https://nodejs.org/en\n[nodelib_fs_scandir_old_and_modern_modern]: https://github.com/nodelib/nodelib/blob/master/packages/fs/fs.scandir/README.md#old-and-modern-mode\n[npm_normalize_path]: https://www.npmjs.com/package/normalize-path\n[npm_unixify]: https://www.npmjs.com/package/unixify\n[picomatch_matching_behavior]: https://github.com/micromatch/picomatch#matching-behavior-vs-bash\n[picomatch_matching_special_characters_as_literals]: https://github.com/micromatch/picomatch#matching-special-characters-as-literals\n[picomatch_posix_brackets]: https://github.com/micromatch/picomatch#posix-brackets\n[regular_expressions_brackets]: https://www.regular-expressions.info/brackets.html\n[unc_path]: https://learn.microsoft.com/openspecs/windows_protocols/ms-dtyp/62e862f4-2a51-452e-8eeb-dc4ff5ee33cc\n[wikipedia_case_sensitivity]: https://en.wikipedia.org/wiki/Case_sensitivity\n[nodejs_thread_pool]: https://nodejs.org/en/docs/guides/dont-block-the-event-loop\n[libuv_thread_pool]: http://docs.libuv.org/en/v1.x/threadpool.html\n[windows_naming_conventions]: https://learn.microsoft.com/en-us/windows/win32/fileio/naming-a-file#naming-conventions\n", "readmeFilename": "README.md", "users": {"gvozd": true, "bluelovers": true, "flumpus-dev": true, "pedromsilva": true, "supan_20220713": true}}