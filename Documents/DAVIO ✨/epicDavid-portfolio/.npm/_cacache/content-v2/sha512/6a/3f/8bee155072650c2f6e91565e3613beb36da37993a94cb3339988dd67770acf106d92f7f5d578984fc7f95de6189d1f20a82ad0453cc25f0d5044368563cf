{"_id": "<PERSON><PERSON><PERSON>", "_rev": "11-6b6a6509961d798450db559dec2ef489", "name": "<PERSON><PERSON><PERSON>", "dist-tags": {"latest": "1.0.5"}, "versions": {"0.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "0.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "_id": "sisteransi@0.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RjuZ4i2HQNAzVxcCmyVnaLhL+aLJru0Qcf7H0f9o23TmJYRaImUXhEbwo4LTqtil+nbUFzAo2vQDsBa/NUwNbw==", "shasum": "4b61a781f8bd47303248afc853166c0d7b034845", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-0.0.0.tgz", "fileCount": 1, "unpackedSize": 206, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiuF1iKNZseSiJ9spnZY7UoHAfpf+kSf7nwd9KAxLAtAiEAhmUSVNCJeGKbjGrnc6WqqJOrELkFMdAVEKV2QvCUYB0="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_0.0.0_1519093673074_0.5977076712274443"}, "_hasShrinkwrap": false}, "0.1.0": {"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "description": "> Ansi escape codes faster than you can say \"[Bam bam](https://www.youtube.com/watch?v=OcaPu9JPenU)\".", "homepage": "https://github.com/terkelg/sisteransi", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "files": ["src"], "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.9.0"}, "gitHead": "d0a5ecabf0fb4f875ca1fb713c29b56656738ed5", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "_id": "sisteransi@0.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kHXcIr0Z9FM6d7pwFDDIMQKGndIEtIF1oBSMXWtItpx4mrH1jhANVNT35GVekBekXl6J+5i7lJMIGq3Gm7pIdA==", "shasum": "2e6706ac427019b84e60f751d588d87920484f25", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-0.1.0.tgz", "fileCount": 4, "unpackedSize": 5244, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHACfTopB40ch2ni7seic+1buh3Y4296IuwpnkFPDvzAIgdX7GG9BUPzDY2q0tWNMEvnBMLJQxALab5Csjynkijzk="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_0.1.0_1519282660869_0.9128469419932141"}, "_hasShrinkwrap": false}, "0.1.1": {"name": "<PERSON><PERSON><PERSON>", "version": "0.1.1", "description": "ANSI escape codes for some terminal swag", "homepage": "https://github.com/terkelg/sisteransi", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "files": ["src"], "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.9.0"}, "gitHead": "2c5ccd96c7dffd469afb5b3634dd3e672df7ab27", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "_id": "sisteransi@0.1.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PmGOd02bM9YO5ifxpw36nrNMBTptEtfRl4qUYl9SndkolplkrZZOW7PGHjrZL53QvMVj9nQ+TKqUnRsw4tJa4g==", "shasum": "5431447d5f7d1675aac667ccd0b865a4994cb3ce", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-0.1.1.tgz", "fileCount": 4, "unpackedSize": 5506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMwQBCRA9TVsSAnZWagAAn+0P/0mzPN9MsSkZffeeJhav\nFsjJi9jlgNOvzB5dsLgAdTksKsKe0ysz3Wzugo0q9Fshn7ZufuZjtKaXEqJo\nWMgMGoVNs9UUtzW9GrSvc7mh2fdaKmk/HCaG8no83u14MH00Yrvijhx8Fx6c\nmzikqgrrItHNQ0K9QmZAcpIpCmD564NrPPQwQpiL0bvlmf6BJYmJXzz2nCPs\ncHwuMj9zBORxdofXwB4w4WhmGoquhQir1ARMyGFAkqZlyW1qzPu4An45h3dl\niGVeVXr8KnsSbi3vVPzyuRt7Y+tSHGK1xSA8vx9aE3AN9I/0pVDuv+8VjpQD\njT/XaLBCyVEKKpDqpD2vyRamWPOBZJG2fc1vz3d/r70wSwVLtMvWtV6uLckv\n+kgsMd7OLTpJU6i6cUid/67Xndznnvs4GEnw0lIdiWyW5v6gxgQaDKcLXhwT\nmlhXIQz8+lJx9ZVTYa3ExUMGDboyAD0ev3WtXV19S2ioZfcQiaMvFWXcfLoh\nJbZRnYn5ahdOj902P9q0czTr55LUWZsc2nlu5yDX3W8I/h90PR4bviIeCtQv\n54hSPz7hm+NKPGrzAZtS2Md0r7civW86THDGogYxKchB16o2evpDVVESGy8e\nuzZ857Zdbkh+MDqpFGWR/NQC+IojYMOwycNYZXXExMxPFFntUsWdgXEY89yO\n8Jf0\r\n=fCXI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgawh2/5hQBBjkDJnLxY2+Ck8lioTCZn9NBWOHmHIaDQIhAKC9dHk5dWUHcPDjuGQ7Nst0jMiLnVXWbKOcEvCASss7"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_0.1.1_1530070017384_0.7328006777591958"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "ANSI escape codes for some terminal swag", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "files": ["src"], "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.0"}, "gitHead": "0eb456ac9c076faa2e45dd2a85ea36f94f9f0c06", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "homepage": "https://github.com/terkelg/sisteransi#readme", "_id": "sisteransi@1.0.0", "_npmVersion": "6.4.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-N+z4pHB4AmUv0SjveWRd6q1Nj5w62m5jodv+GD8lvmbY/83T/rpbJGZOnK5T149OldDj4Db07BSv9xY4K6NTPQ==", "shasum": "77d9622ff909080f1c19e5f4a1df0c1b0a27b88c", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.0.tgz", "fileCount": 4, "unpackedSize": 5545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeAqZCRA9TVsSAnZWagAAXSgP/2gvg35XdD4TGPqW4yvf\na8zTvqRJmNuylKbCXB3WwH9B1s1tqI6nA7Ua9fGSLntG5H/5wT0wK898VRh9\nFcfDJCorTHgmqxKChPMV/jwMonxd73BYk9STrShC1A2SRfKTq5Wq6Bs5KzZy\n2ulAnyQywOnb257elJgKpNbspMyur1ox4plvF3iyYXbAlwboClaJ+2VDN7dO\nQPCrgzFtTEHY9UQNe+b+UZZzS75ehu9FRM6uSPB823b4O/QArNfKjJ1GlZl7\n02B3caVGc5D0SLkAh7Ec8hy92O2C9LrAUbcODh1cphu0t0v17hH5mSM8kqS3\nCQJ2SWdw+do9xuVp/knjI77ag/4ussvN+JIFMtvEiO1qBRu0OOc5Mp2CJaBB\n0Lli/qtmy1wz67umZdpeevJfJ7hMVlP/mx54gS8GCI699GlbRive3vkC+xVV\nz0kg2+rQb2dhUPGsQ7PvSRgrn/w6MTz8GU5rFWyfuVF1oiOOiJB3Jk6Uj2Ll\nthYBH+sDcsqs3s+h5RJnCO4fFRXUvbY8EnYHBvGsNK3P//o/Yf5iqxDZZaEF\nPSCeLiTFgt118mPG+7RPGm8DEQe3EYpTdpOxxxtq471wULApVIlcDJjfIDW3\nnUZ5Z+LVuELQ76RuufFTVxytPwi4i/fpUDgPsyOXqEZroLjOOA0U3g4wRPtD\nuGTN\r\n=GdK4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDf6G1yekXFKXrtyrlYRE5wEKEDlFwuH5xW+3XRd0HKzAiB7eQMSp8YhsHkLjAjXLF5/Pw10NhMLzv3MFaB03oKRgw=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_1.0.0_1534593689332_0.23049807964926483"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.1", "description": "ANSI escape codes for some terminal swag", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "types": "./src/sisteransi.d.ts", "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.0"}, "gitHead": "a13a7e755f6dc657f556e5178489446dc1f5494b", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "homepage": "https://github.com/terkelg/sisteransi#readme", "_id": "sisteransi@1.0.1", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-Qs1o9b0ORUzmh04fZjVzqeQ0L9tmJCL1eyivJcDNCY16gOEvR299INgXwBdy2bq38bVgSpq4DDfhgQx515ToLQ==", "shasum": "208a2443e1fc40b05ccad4de0aa8798b27249ca1", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.1.tgz", "fileCount": 5, "unpackedSize": 6790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdG+SoCRA9TVsSAnZWagAAU80QAJtUN6az60keIu8wMk25\noWcM13sfvJUS9bH2EOEYhYimZ4IipyZvuPAZgvuRlm9rTAMdHeGh8g4asgxP\n94z/EjVGIV4b/6laIPCs1oQPi5Rq1jcxjnYOKErQa/yIDx7arICIoD+ZTL2f\n4YYrTUQ+MaONQN+vux4gTFF/V6DD+EbqaXky8fvpG1+BPJ5Jg57fbwJCAFOA\nAu+1Dcs+2f+0fhPiVltWLHwOuBMfsb9m2hicCXuuwtxUFNu2f1BpEz0HR3at\n4VQG2TkWUci9DjmZ4sLqnLl1aliOzn4qqXqWlIv3Xpmfv42tE8W0Ni0BNnIS\nV7wqM2v11Cl+PD+fDKnJNJTcstQcZCOL7MFq65rVosBxD75BXQ4ithbVJ/BY\nWCKh2/hzZJmOQ7/GDousMry5O3/iLvZ9+Mba5C+qjP3aeviT+/VNhMYaL9v4\nsI/Kb2WHm/bPYR0MznMXZxYbBM8CzTeT4CRcNABRNMuZS4c3u6VKpbX3xDkj\no0M4ARcnZ1QENWrK7ow1GyADQlN7T5RHU2toU6wbAgT+qGMCCNkRTAsFw/6o\nHKwwr7h+qvBn+zQ4vkACSpgiAfaAuujKn9gejMT2Nc+RNdCp1L7JrgJAaPWR\nAieoC3FxDqjnHIUF0GiTP3DJ368Pyxm6te/rs+Yu2h5WWCvIi2v3+zZN8BD0\nhwiS\r\n=yuCk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFlWI/HiDoZWoF0SXfG+yE6ZCsgJ2GT2eeZg9BReJmJNAiEAmijXHTERGAhVzDFwFm2Z7E5xUu8urO94BXP3c2CdJxY="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_1.0.1_1562109095330_0.8929334997368581"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.2", "description": "ANSI escape codes for some terminal swag", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "types": "./src/sisteransi.d.ts", "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.0"}, "gitHead": "00df3d50686819d92999692c916cb9afe843ee2f", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "homepage": "https://github.com/terkelg/sisteransi#readme", "_id": "sisteransi@1.0.2", "_nodeVersion": "12.5.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>ZcT69nSLAR2oLN2JwNmLkJEKGooFMCdvOkFrToUt/WfcRWqhIg4P4KwY4dmLbuyXIx4o4YmPsvMRJYJd/w==", "shasum": "ec57d64b6f25c4f26c0e2c7dd23f2d7f12f7e418", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.2.tgz", "fileCount": 5, "unpackedSize": 6779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHf7wCRA9TVsSAnZWagAA0zYP/infnPpzo2f/iGWdQjWI\niJ4QdYtQj3LwVzKc4RS+JIuciYC88B0cWHpCOyN7cyNPD83D3xxybkjdTgU7\n50N7KvvbSXBxraK3noyUN7d+hhZ6Yb8i683J+ClERL4+vmVwH3KV4Bals2p1\nUaoI+yuGPOkn+bVUOertRL5YY++m3vt7tJXtY9m1cXVHjKPN2t1ksgBwTsYe\nWriW1u8FMr80ue1B5H5huux/g2VyBK2Qhde42qwGu+TUDbfnkug+Ma3woT7t\nPuLMao6ftCMxZqMx3N9NFES+pGbwbwcs/+cVnwWkfHW0KfevMh38Opy/QnNH\n/474FiHIbrIgUGRYXIs/JIJObnQNaDfVH/m9oLhEgipCcQUSZxWrifUZLZfN\nBPBOH6MgtcoqEWE7ZoTpF/qXe6O3FUmazx1KCLqVQbeksv9d9YBtqunoSQ+O\nykSKGMpCklPp3ZDmHwA4txP8pV62Ma6BdeX7setkFhMNBCwOaxFgkkCuoItl\n6u/AZDZqy8HOIt2+GSXavzowIWmzdThtVj5DFRsvfQTC0rXOFI+SPcKY9NTu\ny4uLuX4TmfKi5rKaT21ap8NRNUSW0aaTMwYZIZoBs7Zi6PD4o3HIi4mLyHP4\n5lDGEeO7pg3URd515hVXykZQ2dtfT4CrkvM3R2NNtnRwuPTKiTkU61Z6MhGq\nTtI0\r\n=y3W9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3uo0/QzwV8Skfcqv+ERV69mHT2gZG5DaJfisFUuAp6AiEAq/qDvczIv1E2tXJ52RKePW9o1kytiq684DRCf9wM0A4="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_1.0.2_1562246895287_0.9460189391327065"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.3", "description": "ANSI escape codes for some terminal swag", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "types": "./src/sisteransi.d.ts", "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.11.0"}, "gitHead": "15649cfbf3f4caec0f643e9cc9f64c7994277da5", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "homepage": "https://github.com/terkelg/sisteransi#readme", "_id": "sisteransi@1.0.3", "_nodeVersion": "12.5.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-SbEG75TzH8G7eVXFSN5f9EExILKfly7SUvVY5DhhYLvfhKqhDFY0OzevWa/zwak0RLRfWS5AvfMWpd9gJvr5Yg==", "shasum": "98168d62b79e3a5e758e27ae63c4a053d748f4eb", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.3.tgz", "fileCount": 5, "unpackedSize": 6780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdR98QCRA9TVsSAnZWagAA5icQAItW5JUSj3B0kBMIgfxH\nytX2moUD+biJQn7H4htJAAT75bc1lJTGvJpmezpg19muHS7xXo3etHG3TQiK\n0KaERM1JFqp4EREUorbu88m6z8drOAz9NJTR+8TommIKiD4YoDtqoVQEO6st\nq8l66cj1aeTxD7mBVEYUeb+S3qLBXKXhG89jDAGWUA4bzIl52UNA8pnSUUVv\nryG2szbToo8mqu8Pv1VrjE0lFeTzw7WoERzPI/H8L9u7OeLdEEPxNqzoOXjQ\n7oZ0nAz5Q5v3NrZfSYNV4NtK5H7wF5m2m2wbtlUpk7Xbwmkel9JNJFlad6DC\nVIad8ceHuRAOkWxQS4NJvOzQyLtMXPKsR9NY1OXuVUwOi2MDkOxzwXhrICB8\nGMCfyMRYLAisBa6MYGEl39OJBrqLLD1mIvkxD51Dz3yhXOH4ekEHJQjiQjEG\n61uRyI8xeOR7hWNdo9ECGuDv13YJ+M3mfZUnzhQ5utXCscW6QvW8El/7/imB\nFH4zt7Q7GmQ6VX2C3bzL687tmOoGYvah3P1FMNOe3uz+DEfmj5hFbTPWSrej\nTjjM1HS4carB0KhAgnWGvYQ+Pljoj3/tJhkWVjKQ2i/dVxAMs55tRqqFHZah\nIJiD+csWJY7w7zIG67pZlOJb1VC86HpiSRccT/AwyEhwkKhcYgPYUcJnVpKX\nbd98\r\n=0qHS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH1DHZ7c4uHPpXaqi43STc89TS70OocVd0+MnvrpIchMAiBmaQwf+2lhdFDD/WzdZHB4VPC+SzhqBJs/8l1Ijpju3Q=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_1.0.3_1564991247380_0.19668738890350967"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.4", "description": "ANSI escape codes for some terminal swag", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "types": "./src/sisteransi.d.ts", "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.11.0"}, "gitHead": "d08d16ec5663dfa99bc2ec3c3ccbb09d41c7cd36", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "homepage": "https://github.com/terkelg/sisteransi#readme", "_id": "sisteransi@1.0.4", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-/ekMoM4NJ59ivGSfKapeG+FWtrmWvA1p6FBZwXrqojw90vJu8lBmrTxCMuBCydKtkaUe2zt4PlxeTKpjwMbyig==", "shasum": "386713f1ef688c7c0304dc4c0632898941cad2e3", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.4.tgz", "fileCount": 5, "unpackedSize": 6791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyFdVCRA9TVsSAnZWagAAQ8wP/ifxifhTDdieak2cfE2W\n+/N4JE7K59Z2F3Q5Y6g4FefSk264rG+wPFrC05V8983IOk0mfvFCP36v+ejM\n7o+7K/lEaSuxrEBrpt+yRqaIGL2sNAx6yMMhnTzhpL+IY++UwmXva7kjziSa\nRQRKIZXzc51zxkh6MzC2LsVAuK92YSAltEwPBaR9yjHd+cER5UNb3VI8CP7N\nflyvWG26mKk/epHUGekRKjVv2lHACqy4Gwq3+glyr+nJ/DpaQStVfavL5wsv\nH/orKsj95n+DDhkO/du8Dwlt/me79RSMXpSGxSzpe42NR2J1+kU72pcdjGCB\nWEz3CDCJmsYFV//S0XoWbeMhS8E0Ul6nm6Lhu8yXAnQLaSSYULi5Co/ta5W6\ntbLPkLNWBt0rx84lRkjv+xHzNn/aiYckwRCwgkAG19kvRVXvJykiwzNxrFEU\ni0DAc92Kuz7w/CAgPTdOB6w3Sc5HKb+FPO+sSslZM5014nxVNxKzXHG+du6O\n1x4uvlsnGNSfcFW4o9F2fgR2+00+TcL8tZjvuVyd7G2uk5Su4Azt7u5Ws2tg\n1DKsDVt9NnJk6aiP/r6rCCGn31H0uUrYqqp0CSBGMpe1m4SgPQUthRUBfOYL\nfQhDvjEGZG3INHqCdiLb1CfKDEvEJzlECP9VTLTVcNivuOUbutAlNe7LngcX\nZ4V2\r\n=lrGF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCevMIJnGGWkt8x+x4fZcoae8Img2LCb71JDl+fUyC/kQIhAKHL2LGOxzJMxLOaElCVX+heVRMV9M9TgdM3/Pkq0JOu"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_1.0.4_1573410645036_0.6193041481886665"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.5", "description": "ANSI escape codes for some terminal swag", "main": "src/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "types": "./src/sisteransi.d.ts", "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.13.2"}, "gitHead": "58a3aff12121a7e876c1b47612571bf2757c916a", "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "homepage": "https://github.com/terkelg/sisteransi#readme", "_id": "sisteransi@1.0.5", "_nodeVersion": "13.7.0", "_npmVersion": "6.14.2", "dist": {"integrity": "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==", "shasum": "134d681297756437cc05ca01370d3a7a571075ed", "tarball": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "fileCount": 5, "unpackedSize": 6791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJechnGCRA9TVsSAnZWagAA9YQP/ArJNzkeRNIp5Jpmiycb\nP/43QdysDmwg28S21o+qpu9jmYGmILOtsg+KMKDAjF8XTtuXO3ndGNE4tL1S\nMrfNwgAqYsgvhNrZlmr1riXkQvroVs7ICBlhpT0g3qEYnryihXOzH7twYcgM\n2R5e0KfHUQ7qmH2er15qYzBzHtwx4JVfp+uwFL3Rl0VIgSVjcTzrlPflH8+U\ngfEMFXbw7L1Ap2muDpxVPMD2Y+oyMwQ+2TNtkYzwpRIc1jfwgYWseAWo12tg\nt6MzI0iBPsITgcB6mTj4QlYPasiDd6yBbwEXTUbwBur6kIOyh2cf2XJEoTZi\nA18No+ivhPt8zJY7ukvDnGNqSi700SyOsz7vS33rx6ITMz7dWvvmCS8hkFUx\nb1Y/xC9jSgdIKinS3V3rkHTrXiDk9+hsAGSuLGDf2vOkBzKHgLR8Ejh+OE4R\ngQWQBOmpRQQdilmhHPuZpMhc05o4ffy9RGmVXSnsTx5vnRTKSANrowW/UGBm\nd6SwGBg1ze+/sUxtlACaI5zCBsaicEoZFYBL03HfiF7NZNOwECCZ60BNfE+F\nuOUrEBE4TKKoB0iSsgi1F3PXLok/gNNDB/u9rWczaFq8g4VC16AOvNBa1yQh\nNqpTrwAsZ2YdlDkcpA3ZVsqRQJElYgn5EoWmSmA4ktNU+XXl12OdLVnovvc2\nsdfZ\r\n=sznn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnoA/ZOotLkb4HXSFr40n2KIUj1P42UEGRQ1Ju19roFAIhAMFyh51Ls36i0bGRoYDlLiKwZ15wtPD68+SvlksLTmUB"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sisteransi_1.0.5_1584536006513_0.162565355758854"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-02-20T02:27:53.074Z", "0.0.0": "2018-02-20T02:27:53.147Z", "modified": "2023-07-09T22:07:38.381Z", "0.1.0": "2018-02-22T06:57:40.925Z", "0.1.1": "2018-06-27T03:26:57.468Z", "1.0.0": "2018-08-18T12:01:29.432Z", "1.0.1": "2019-07-02T23:11:35.512Z", "1.0.2": "2019-07-04T13:28:15.420Z", "1.0.3": "2019-08-05T07:47:27.510Z", "1.0.4": "2019-11-10T18:30:45.193Z", "1.0.5": "2020-03-18T12:53:26.602Z"}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "license": "MIT", "readme": "# sister ANSI [![Version](https://img.shields.io/npm/v/sisteransi.svg)](https://www.npmjs.com/package/sisteransi) [![Build Status](https://travis-ci.org/terkelg/sisteransi.svg?branch=master)](https://travis-ci.org/terkelg/sisteransi) [![Downloads](https://img.shields.io/npm/dm/sisteransi.svg)](https://www.npmjs.com/package/sisteransi)\n\n> Ansi escape codes faster than you can say \"[Bam bam](https://www.youtube.com/watch?v=OcaPu9JPenU)\".\n\n## Installation\n\n```\nnpm install sisteransi\n```\n\n\n## Usage\n\n```js\nconst ansi = require('sisteransi');\n// or const { cursor } = require('sisteransi');\n\nconst p = str => process.stdout.write(str);\n\n// move cursor to 2, 1\np(ansi.cursor.to(2, 1));\n\n// to up, one down\np(ansi.cursor.up(2)+ansi.cursor.down(1));\n```\n\n## API\n\n### cursor\n\n#### to(x, y)\nSet the absolute position of the cursor. `x0` `y0` is the top left of the screen.\n\n#### move(x, y)\nSet the position of the cursor relative to its current position.\n\n#### up(count = 1)\nMove cursor up a specific amount of rows. Default is `1`.\n\n#### down(count = 1)\nMove cursor down a specific amount of rows. Default is `1`.\n\n#### forward(count = 1)\nMove cursor forward a specific amount of rows. Default is `1`.\n\n#### backward(count = 1)\nMove cursor backward a specific amount of rows. Default is `1`.\n\n#### nextLine(count = 1)\nMove cursor to the next line a specific amount of lines. Default is `1`.\n\n#### prevLine(count = 1)\nMove cursor to the previous a specific amount of lines. Default is `1`.\n\n#### left\nMove cursor to the left side.\n\n#### hide\nHide cursor.\n\n#### show\nShow cursor.\n\n#### save\n\nSave cursor position.\n\n#### restore\n\nRestore cursor position.\n\n\n### scroll\n\n#### up(count = 1)\nScroll display up a specific amount of lines. Default to `1`.\n\n#### down(count = 1)\nScroll display down a specific amount of lines. Default to `1`.\n\n\n### erase\n\n#### screen\nErase the screen and move the cursor the top left position.\n\n#### up(count = 1)\nErase the screen from the current line up to the top of the screen. Default to `1`.\n\n#### down(count = 2)\nErase the screen from the current line down to the bottom of the screen. Default to `1`.\n\n#### line\nErase the entire current line.\n\n#### lineEnd\nErase from the current cursor position to the end of the current line.\n\n#### lineStart\nErase from the current cursor position to the start of the current line.\n\n#### lines(count)\nErase from the current cursor position up the specified amount of rows.\n\n\n## Credit\n\nThis is a fork of [ansi-escapes](https://github.com/sindresorhus/ansi-escapes).\n\n\n## License\n\nMIT © [Terkel Gjervig](https://terkel.com)\n", "readmeFilename": "readme.md", "description": "ANSI escape codes for some terminal swag", "homepage": "https://github.com/terkelg/sisteransi#readme", "keywords": ["ansi", "escape codes", "escape", "terminal", "style"], "repository": {"type": "git", "url": "git+https://github.com/terkelg/sisteransi.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "bugs": {"url": "https://github.com/terkelg/sisteransi/issues"}, "users": {"flumpus-dev": true}}