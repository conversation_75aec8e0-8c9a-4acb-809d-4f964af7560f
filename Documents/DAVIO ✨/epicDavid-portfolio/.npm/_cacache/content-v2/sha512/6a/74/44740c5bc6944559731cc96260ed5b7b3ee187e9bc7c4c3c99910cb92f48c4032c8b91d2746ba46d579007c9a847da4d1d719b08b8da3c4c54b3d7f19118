{"_id": "redux-thunk", "_rev": "137-13099ee397f4afac60d43026b7ef9926", "name": "redux-thunk", "description": "Thunk middleware for Redux.", "dist-tags": {"latest": "3.1.0", "alpha": "3.0.0-alpha.3", "beta": "3.0.0-beta.0", "next": "3.0.0-rc.0"}, "versions": {"0.1.0": {"name": "redux-thunk", "version": "0.1.0", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "scripts": {"prepublish": "rm -rf lib && babel src --out-dir lib"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel": "^5.6.14", "babel-core": "^5.6.15", "babel-eslint": "^3.1.20", "eslint": "^0.24.0", "eslint-config-airbnb": "0.0.6"}, "gitHead": "9f02789ee290c27656fed5f02402507adb54b5a1", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@0.1.0", "_shasum": "8e347606808b35bf8a927df4616f6fed101826e5", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8e347606808b35bf8a927df4616f6fed101826e5", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-0.1.0.tgz", "integrity": "sha512-NwNruhUJo3BqD2Tkvlizj2t7eHgnBiUpPDIGHM112k4wDwrZTclLCG98zA0JZeCPex7s2VvRryy7FJUOwwz8mQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG+vmNPj2+eO2+CW690EX9EKEvENgbOq6+6kXI69dvA7AiEA5ElGhw3Ft4fWINCfPlR0ApgLoN3qcuD4wTRMEP0T8ZA="}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "redux-thunk", "version": "1.0.0", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "scripts": {"prepublish": "rimraf lib && babel src --out-dir lib", "test": "mocha --compilers js:babel/register --reporter spec test/*.js"}, "repository": {"type": "git", "url": "https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel": "^5.6.14", "babel-core": "^5.6.15", "babel-eslint": "^3.1.20", "chai": "^3.2.0", "eslint": "^0.24.0", "eslint-config-airbnb": "0.0.6", "mocha": "^2.2.5", "rimraf": "^2.4.3"}, "gitHead": "2240b0b389249f6e2ecd1b5385c1dd85ef812c73", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@1.0.0", "_shasum": "e35544a10fcd9c9e3ba96083ac16c702394f4009", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e35544a10fcd9c9e3ba96083ac16c702394f4009", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-1.0.0.tgz", "integrity": "sha512-WuVU7gOPcX7MukrchI8LzkcW2cTRiTMfQU8LP1qLoT9OXpJYpeXPSyNpQMc72pCmw/PEBsMITRBpL3QR3nPDWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRUQ0aoPMOIBy4qyOf00ZkiSua8x7+iq5whzM52UQy4QIhANpU1HO3+EC8/ag/+P+KAEf7C0uXDOFYidQpTcAZpZjw"}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "redux-thunk", "version": "1.0.1", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "files": ["lib", "src"], "scripts": {"compile": "babel src --out-dir lib", "prepublish": "rimraf lib && npm run compile", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel": "^6.1.18", "babel-core": "^6.2.1", "babel-eslint": "^5.0.0-beta4", "babel-cli": "^6.2.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-0": "^6.1.18", "chai": "^3.2.0", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "mocha": "^2.2.5", "rimraf": "^2.4.3"}, "gitHead": "b5707bf4731a85025a4d73f76448764b78b898ba", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@1.0.1", "_shasum": "4d92aad1e63ad5634181d1540f2f2c62c75d6bc4", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "4d92aad1e63ad5634181d1540f2f2c62c75d6bc4", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-1.0.1.tgz", "integrity": "sha512-FifGYtPhPcJ814/WK1beMxgLt9DssWuIzZQLYalddWRcWLm4W6V7n1HkBpMLCDC3Y5b/AzBRhZMLkgwKKfgUUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIRQM544Q08+H/YzKpA+KrcXhyW+K177t3Ge8xf7IvjQIhAMWcLSdImoiX4cR9pLGX/28SMnalJWU9FOt0llEu+NvH"}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "deprecated": "THIS IS A BROKEN RELEASE. USE 1.0.2 INSTEAD.", "directories": {}}, "1.0.2": {"name": "redux-thunk", "version": "1.0.2", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "files": ["lib", "src"], "scripts": {"compile": "babel src --out-dir lib", "prepublish": "rimraf lib && npm run compile", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel": "^6.1.18", "babel-cli": "^6.2.0", "babel-core": "^6.2.1", "babel-eslint": "^5.0.0-beta4", "babel-plugin-add-module-exports": "^0.1.1", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-0": "^6.1.18", "chai": "^3.2.0", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "mocha": "^2.2.5", "rimraf": "^2.4.3"}, "gitHead": "2abb03390fb0b4c1696a3cc165c1f77889d33f43", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@1.0.2", "_shasum": "5911e5a25dab2649d860b0f1eeac261c9d130ece", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "5911e5a25dab2649d860b0f1eeac261c9d130ece", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-1.0.2.tgz", "integrity": "sha512-0dMpTmLbmEpqqBeopEM5LJV7/j9b8U+AVafGFywABPbQaWX1E2Y+mL3dIUP1qZb+dsc3ZF5WXRm5roUWqI+sQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEFki4z02u6Pmi3tlbZbR4huiED9a6b33gD/KlXpiCq8AiEA+3AzYc1kVjJO2mlAfhoV9pzaGZ5nPxqFil7+9KjlNIs="}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "redux-thunk", "version": "1.0.3", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "files": ["lib", "src"], "scripts": {"build": "babel src --out-dir lib", "prepublish": "rimraf lib && npm run build", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js"}, "repository": {"type": "git", "url": "https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel": "^6.1.18", "babel-cli": "^6.2.0", "babel-core": "^6.2.1", "babel-eslint": "^5.0.0-beta4", "babel-plugin-add-module-exports": "^0.1.1", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-0": "^6.1.18", "chai": "^3.2.0", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "mocha": "^2.2.5", "rimraf": "^2.4.3"}, "gitHead": "be45e6bc0f027228f6373f6345c8380e935e8f3d", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@1.0.3", "_shasum": "778aa0099eea0595031ab6b39165f6670d8d26bd", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "778aa0099eea0595031ab6b39165f6670d8d26bd", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-1.0.3.tgz", "integrity": "sha512-A0lWmgIRgfnquBneMyp4eJWs/92e7belQ8p4Y2Y2S6Lqxc/ahJCpERHBsOEXxJG6dsrxxi3lTz2fp1L/YWncDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSTQOwYcXPvPVTaTMpoqxflSJQnD+GQ5O77jntuTfxYgIgFPb3T20avtSAJa5Y86aBqHGfPrHDeYuBtJf2LueVh7Q="}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "redux-thunk", "version": "2.0.1", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "jsnext:main": "es/index.js", "files": ["lib", "es", "src", "dist"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min && npm run build:es", "prepublish": "npm run clean && npm run test && npm run build", "posttest": "npm run lint", "lint": "eslint src test", "test": "cross-env BABEL_ENV=commonjs mocha --compilers js:babel-core/register --reporter spec test/*.js", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=es babel src --out-dir es", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack"}, "repository": {"type": "git", "url": "https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-cli": "^6.6.5", "babel-core": "^6.6.5", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.6.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.6.5", "babel-plugin-transform-es2015-block-scoping": "^6.6.5", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-computed-properties": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.5.0", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-plugin-transform-es2015-object-super": "^6.6.5", "babel-plugin-transform-es2015-parameters": "^6.6.5", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.6.5", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0", "babel-plugin-transform-es2015-template-literals": "^6.6.5", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "chai": "^3.2.0", "cross-env": "^1.0.7", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "eslint-plugin-react": "^4.1.0", "mocha": "^2.2.5", "rimraf": "^2.5.2", "webpack": "^1.12.14"}, "gitHead": "57b2ee21d05439895c7f75814ec86a7b6f868d3d", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@2.0.1", "_shasum": "f0b31070fa1a243a4b19f904befdc2ee439aade9", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "f0b31070fa1a243a4b19f904befdc2ee439aade9", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.0.1.tgz", "integrity": "sha512-qTIikiZ9qIO3VhrJ3kSpG4DKauqtmn9/N76MlQRLc2uhwLWva7m5MKUlrXBL0V64wWzgBCi6bgCbyL5xwQXswg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+tHFlWCqnLOKOaif4XIdPp3xeADzn9YymUe9EaQvEpAiAwcDoeEWfwwhyFMJ3mUeQoC0GMgLaPiKxmkHfjk1duQg=="}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/redux-thunk-2.0.1.tgz_1457269922684_0.06873586401343346"}, "directories": {}}, "2.1.0": {"name": "redux-thunk", "version": "2.1.0", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "jsnext:main": "es/index.js", "files": ["lib", "es", "src", "dist"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min && npm run build:es", "prepublish": "npm run clean && npm run test && npm run build", "posttest": "npm run lint", "lint": "eslint src test", "test": "cross-env BABEL_ENV=commonjs mocha --compilers js:babel-core/register --reporter spec test/*.js", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=es babel src --out-dir es", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-cli": "^6.6.5", "babel-core": "^6.6.5", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.6.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.6.5", "babel-plugin-transform-es2015-block-scoping": "^6.6.5", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-computed-properties": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.5.0", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-plugin-transform-es2015-object-super": "^6.6.5", "babel-plugin-transform-es2015-parameters": "^6.6.5", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.6.5", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0", "babel-plugin-transform-es2015-template-literals": "^6.6.5", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "chai": "^3.2.0", "cross-env": "^1.0.7", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "eslint-plugin-react": "^4.1.0", "mocha": "^2.2.5", "rimraf": "^2.5.2", "webpack": "^1.12.14"}, "gitHead": "7592d43085835959212cda5ae97dab2586f7381f", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@2.1.0", "_shasum": "c724bfee75dbe352da2e3ba9bc14302badd89a98", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "c724bfee75dbe352da2e3ba9bc14302badd89a98", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.1.0.tgz", "integrity": "sha512-7h43ifB5WUDtt+asnymZnrnUvLOQCImJus+OZHKrcwDx1l2Z3DhOACTztCfx4V7ZNwXc9u1TqtxySneJWdspuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB50kgeWOqAsoejcx4TXkOfTm1NuMft5sY/BQYoWAov6AiBAHfrzvvQqZvRtx8kD6Qq9VZfT9gVyYU4kMKYs/1kOEw=="}]}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/redux-thunk-2.1.0.tgz_1462893092551_0.6522019866388291"}, "directories": {}}, "2.1.1": {"name": "redux-thunk", "version": "2.1.1", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "jsnext:main": "es/index.js", "typings": "./index.d.ts", "files": ["lib", "es", "src", "dist", "index.d.ts"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min && npm run build:es", "prepublish": "npm run clean && npm run test && npm run build", "posttest": "npm run lint", "lint": "eslint src test", "test": "cross-env BABEL_ENV=commonjs mocha --compilers js:babel-core/register --reporter spec test/*.js", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=es babel src --out-dir es", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-cli": "^6.6.5", "babel-core": "^6.6.5", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.6.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.6.5", "babel-plugin-transform-es2015-block-scoping": "^6.6.5", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-computed-properties": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.5.0", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-plugin-transform-es2015-object-super": "^6.6.5", "babel-plugin-transform-es2015-parameters": "^6.6.5", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.6.5", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0", "babel-plugin-transform-es2015-template-literals": "^6.6.5", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "chai": "^3.2.0", "cross-env": "^1.0.7", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "eslint-plugin-react": "^4.1.0", "mocha": "^2.2.5", "redux": "^3.4.0", "rimraf": "^2.5.2", "typescript": "^1.8.10", "typescript-definition-tester": "0.0.4", "webpack": "^1.12.14"}, "gitHead": "142097d5cb809a3cc0d31ac58c3c3a6c2bae718a", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@2.1.1", "_shasum": "f4e1adfa164e41637cf6396b13fac130fe5febdf", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f4e1adfa164e41637cf6396b13fac130fe5febdf", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.1.1.tgz", "integrity": "sha512-WxWa4FAjVX8sm1Ij70V7EN27EmA2jtajm3QvB6lyLrAArRhkwfFzbuMS8upvCRZxpJ/QNB/bLCCn41znaxPznQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6QgMEmb+QWQv3UKjT33PrpTaPb6rsKBxoNkdaih0ZfAiEA+VWnciOG6uSVr1L4QKFbLDbDHx9aYPd/T3Y2nji/1XQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/redux-thunk-2.1.1.tgz_1484720141702_0.08023179788142443"}, "directories": {}}, "2.1.2": {"name": "redux-thunk", "version": "2.1.2", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "jsnext:main": "es/index.js", "files": ["lib", "es", "src", "dist", "index.d.ts"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min && npm run build:es", "prepublish": "npm run clean && npm run test && npm run build", "posttest": "npm run lint", "lint": "eslint src test", "test": "cross-env BABEL_ENV=commonjs mocha --compilers js:babel-core/register --reporter spec test/*.js", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=es babel src --out-dir es", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-cli": "^6.6.5", "babel-core": "^6.6.5", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.6.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.6.5", "babel-plugin-transform-es2015-block-scoping": "^6.6.5", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-computed-properties": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.5.0", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-plugin-transform-es2015-object-super": "^6.6.5", "babel-plugin-transform-es2015-parameters": "^6.6.5", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.6.5", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0", "babel-plugin-transform-es2015-template-literals": "^6.6.5", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "chai": "^3.2.0", "cross-env": "^1.0.7", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "eslint-plugin-react": "^4.1.0", "mocha": "^2.2.5", "redux": "^3.4.0", "rimraf": "^2.5.2", "typescript": "^1.8.10", "typescript-definition-tester": "0.0.4", "webpack": "^1.12.14"}, "gitHead": "a679a60da433c1ff09531a9179a609a08d2bb76d", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@2.1.2", "_shasum": "c698ed734d3a7448dd0de9865a0fb41312c2d779", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c698ed734d3a7448dd0de9865a0fb41312c2d779", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.1.2.tgz", "integrity": "sha512-xg///QmviqvX2sj8m1za3V2bU9AEOfgJ8JsjgKms6rZJxKmwO1r6XoKnYsKqZZVDgkp3gHNINVEQnXhhUdUf3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICC4L+kyJyE08toUcACIXSF20yLI1B7HIRyCz0UBjFykAiEAqIzuFgcColbYtMbnVUOmYYzYMqbpFEcmNLjegUQKFHo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/redux-thunk-2.1.2.tgz_1484727071620_0.8678598303813487"}, "directories": {}}, "2.2.0": {"name": "redux-thunk", "version": "2.2.0", "description": "Thunk middleware for Redux.", "main": "lib/index.js", "jsnext:main": "es/index.js", "typings": "./index.d.ts", "files": ["lib", "es", "src", "dist", "index.d.ts"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min && npm run build:es", "prepublish": "npm run clean && npm run test && npm run build", "posttest": "npm run lint", "lint": "eslint src test", "test": "cross-env BABEL_ENV=commonjs mocha --compilers js:babel-core/register --reporter spec test/*.js", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=es babel src --out-dir es", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack"}, "repository": {"type": "git", "url": "git+https://github.com/gaearon/redux-thunk.git"}, "homepage": "https://github.com/gaearon/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-cli": "^6.6.5", "babel-core": "^6.6.5", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.6.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.6.5", "babel-plugin-transform-es2015-block-scoping": "^6.6.5", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-computed-properties": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.5.0", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-plugin-transform-es2015-object-super": "^6.6.5", "babel-plugin-transform-es2015-parameters": "^6.6.5", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.6.5", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0", "babel-plugin-transform-es2015-template-literals": "^6.6.5", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "chai": "^3.2.0", "cross-env": "^1.0.7", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "eslint-plugin-react": "^4.1.0", "mocha": "^2.2.5", "redux": "^3.4.0", "rimraf": "^2.5.2", "typescript": "^1.8.10", "typescript-definition-tester": "0.0.4", "webpack": "^1.12.14"}, "gitHead": "4f96ec0239453623adde857b7e7ad8c4f2897bf1", "bugs": {"url": "https://github.com/gaearon/redux-thunk/issues"}, "_id": "redux-thunk@2.2.0", "_shasum": "e615a16e16b47a19a515766133d1e3e99b7852e5", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e615a16e16b47a19a515766133d1e3e99b7852e5", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.2.0.tgz", "integrity": "sha512-OOFWh9mt/7i94QPq4IAxhSIUyfIJJRnk6pe1IwgXethQik3kyg1wuxVZZlW9QOmL5rP/MrwzV+Cb+/HBKlvM8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH3ByRGMJ0L6F34evCRD1KGYIKjYFnu7Uc5APVYhWaXBAiEA4YMwiltgEH8NCak3DDXn9qLzifKudlDc+Bht3huvM+E="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/redux-thunk-2.2.0.tgz_1484727148461_0.5254623393993825"}, "directories": {}}, "2.3.0": {"name": "redux-thunk", "version": "2.3.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.js", "module": "es/index.js", "typings": "./index.d.ts", "files": ["lib", "es", "src", "dist", "index.d.ts"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepare": "npm run clean && npm run lint && npm run test && npm run build", "lint": "eslint src test", "test": "cross-env BABEL_ENV=commonjs mocha --compilers js:babel-core/register --reporter spec test/*.js", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min && npm run build:es", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=es babel src --out-dir es", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack"}, "devDependencies": {"babel-cli": "^6.6.5", "babel-core": "^6.6.5", "babel-eslint": "^5.0.0-beta4", "babel-loader": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.6.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.6.5", "babel-plugin-transform-es2015-block-scoping": "^6.6.5", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-computed-properties": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.5.0", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-plugin-transform-es2015-object-super": "^6.6.5", "babel-plugin-transform-es2015-parameters": "^6.6.5", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.6.5", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0", "babel-plugin-transform-es2015-template-literals": "^6.6.5", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0", "babel-plugin-transform-es3-member-expression-literals": "^6.5.0", "babel-plugin-transform-es3-property-literals": "^6.5.0", "chai": "^3.2.0", "cross-env": "^1.0.7", "eslint": "^1.10.2", "eslint-config-airbnb": "1.0.2", "eslint-plugin-react": "^4.1.0", "mocha": "^2.2.5", "redux": "~4.0.0", "rimraf": "^2.5.2", "typescript": "~2.6.2", "typings-tester": "^0.3.1", "webpack": "^1.12.14"}, "gitHead": "cb8f88e8ddd637e8fd2a746ac97d29a8f20696c1", "_id": "redux-thunk@2.3.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "timdorr", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-km6dclyFnmcvxhAcrQV2AkZmPQjzPDjgVlQtR0EQjxZPyJ0BnMf3in1ryuR8A2qU0HldVRfxYXbFSKlI3N7Slw==", "shasum": "51c2c19a185ed5187aaa9a2d08b666d0d6467622", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.3.0.tgz", "fileCount": 9, "unpackedSize": 17691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDE35CRA9TVsSAnZWagAAMDEP/j/ijF44bAJIYOvsKtcl\n60sfBItpIKg5fCyKfnhldKpT4rxq9b/ELYi/1o4wZIrBbI0WQO3kIAXX6b4E\n3KPvwu+lQGLBFY1pe3JfTax9BbjdpOV924+fJRCS9WbuhNWncxrsGkO7ye6w\nyQFadlHggcqd0utEjvPg+XD6soWnGhRi4AyExBz1mOIJ5n1zUVNaASetR+lG\nKKhQQ3OQRnxJjNR5NiTwmz+OGBcyjUrAweInAVKf9vDPbXgqBQY89rx3AWk+\nc2H0v+xpAQ104oCun9/P0zhxz5alxPM66oT7sT10WhWXFLijmRlW9ID7XeAH\nUbK1i0YNaNbuuYcgEtxzebDy575lxtUw6kE/9fGioZKewmqSphIdDOZa7+L3\n9rJmWkXWCtIqjvaRiy6H3MPQFLVUCYydj3XWkKafO1E88aZe1TqGwdpLBVFG\nn18nQCPyDxu/0bryJ3jnt6Fp32n8oyRweARkxN/deWG4BeIoYGlCZ4sLcur4\ngKaPGfGJ60894B9l1kN/Fmo7XOay2z4XKCwN/OFIB5j1aWPJ1C8gF0dmsfhS\nWbKXdYSRGI/fc1pwE1L9LCyCPcgnwryTCL92aMGC6LoH8C5AaMVL6XWV4pLg\nZTSLvVNYPFvzhlntg429aES7n04nxRxkcm6XGk/1x/VycyXA9eIJuvwYv2Sg\n7T6W\r\n=Y85/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCH3e/WjmpJ07jJhXl+p5BTsmbmUdkzZ5vLHjPBBzmF5ECIQCLrzLYDRN7XcNpecgZQk+n36QcOnrOooqwmL2Y+vif+w=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_2.3.0_1527533048215_0.45767284653365503"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "redux-thunk", "version": "2.4.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build", "format": "prettier --write {src,test,typescript_test}/**/*.{js,ts}", "format:check": "prettier --check {src,test,typescript_test}/**/*.{js,ts}", "lint": "eslint {src,test,typescript_test}/**/*.{js,ts}", "test": "jest", "test:cov": "jest --coverage", "test:typescript": "npm run test:typescript:main && npm run test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir lib ", "build:es": "babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/redux-thunk.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/redux-thunk.min.js", "build:types": "tsc", "build": "rimraf dist lib es && npm run build:types && npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "api-types": "api-extractor run --local"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@babel/register": "^7.15.3", "@microsoft/api-extractor": "^7.18.16", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.3.1", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "rollup": "^2.58.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "27.0.7", "typescript": "^4.4"}, "gitHead": "ce76464960d5f1236460352fd3f2454e930f3665", "_id": "redux-thunk@2.4.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-/y6ZKQNU/0u8Bm7ROLq9Pt/7lU93cT0IucYMrubo89ENjxPa7i8pqLKu6V4X7/TvYovQ6x01unTeyeZ9lgXiTA==", "shasum": "ac89e1d6b9bdb9ee49ce69a69071be41bbd82d67", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.0.tgz", "fileCount": 12, "unpackedSize": 31906, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGghvPpVaXv8uW8f41EbIU79uixqvGh9wpBwzNKhnO/9AiEApfST+3NrCOFehIZ93wGvX/BgQuqLoNdtLgOd3shZ/E4="}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_2.4.0_1635210071913_0.9375608699123843"}, "_hasShrinkwrap": false}, "2.4.1": {"name": "redux-thunk", "version": "2.4.1", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build", "format": "prettier --write {src,test,typescript_test}/**/*.{js,ts}", "format:check": "prettier --check {src,test,typescript_test}/**/*.{js,ts}", "lint": "eslint {src,test,typescript_test}/**/*.{js,ts}", "test": "jest", "test:cov": "jest --coverage", "test:typescript": "npm run test:typescript:main && npm run test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir lib ", "build:es": "babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/redux-thunk.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/redux-thunk.min.js", "build:types": "tsc", "build": "rimraf dist lib es && npm run build:types && npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "api-types": "api-extractor run --local"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@babel/register": "^7.15.3", "@microsoft/api-extractor": "^7.18.16", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.3.1", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "rollup": "^2.58.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "27.0.7", "typescript": "^4.4"}, "gitHead": "876c893f4ac8fe0d93568dba5001360d7405cb16", "_id": "redux-thunk@2.4.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-OOYGNY5Jy2TWvTL1KgAlVy6dcx3siPJ1wTq741EPyUKfn6W6nChdICjZwCd0p8AZBs5kWpZlbkXW2nE/zjUa+Q==", "shasum": "0dd8042cf47868f4b29699941de03c9301a75714", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.1.tgz", "fileCount": 12, "unpackedSize": 32069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoUF2CRA9TVsSAnZWagAAac4QAI7VWPFTG5XVt1Be0aRo\nw8zdvU5vu0hQ0n1/YgQ7Czl3Bh6M7FlpyhTT4MH3m1RNRs3n5IOHYaWwtAgR\nzFAcC5XijqPFBwRNkRhQRSqwLQoX9hUWSw7tjnncHPiwAfLLXbLRa052loG+\nDSBnsZGZ47VwyyY4XtYanZJUexiN3xV52cavLB6TEwMgnLj1Oo8LM6AoHAKy\nINrCwiUIFtGUlBIa+BIklXi/w/FQdB+nzZceomuKqrLyACZjsnR+jL26FXwY\nExaWVwWW2nyoOveUACZkUvqqLvEb8usFNA6QVKAxBjRH4B2ze6osU6bsLso1\nrq/aD4lcuM1P2A22Mau3KLcf3slyfsWtETGGKU1QkQU+jmQdx+PQlXP0tWVg\nvcyqw4Nd7mVoaZQ8QMcRlH5G6YNGbYFzxibpLC19Ga5DFJgEkjEgbWUZD5Io\nkclQLx+RP1u2AEE59hD3GXV9EK4VBg5QMCWbmPAjbuKlxT0m+61OKOfwGar5\n1yROt9TZktOEVvCIASsOPNTM6g791kzUvFD93+WCIbJEq3jLKUO7UrW96gAV\nmWBEZLPM1Lhc/CMAu0dUBADZhscmwzrdt7fMdDtn0vllAkRSCySe8AVLr125\n4qkrGSGQkXBapnXEyU9KwNDpc+tKVUbEmEM3kb+rpzVJVafASl9kz/LWTmia\nw93X\r\n=EmtI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIGxBKPMiiI0d7lbzOCuwqeFu0l8PipztOM1dFrzSrS7RAh85KONscVCQU6FO9wVtpI2l2SG5qnS+dToHzKHHxFST"}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_2.4.1_1637958006566_0.21689831085264877"}, "_hasShrinkwrap": false}, "2.4.2": {"name": "redux-thunk", "version": "2.4.2", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build", "format": "prettier --write {src,test,typescript_test}/**/*.{js,ts}", "format:check": "prettier --check {src,test,typescript_test}/**/*.{js,ts}", "lint": "eslint {src,test,typescript_test}/**/*.{js,ts}", "test": "jest", "test:cov": "jest --coverage", "test:typescript": "npm run test:typescript:main && npm run test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir lib ", "build:es": "babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/redux-thunk.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/redux-thunk.min.js", "build:types": "tsc", "build": "rimraf dist lib es && npm run build:types && npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "api-types": "api-extractor run --local"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@babel/register": "^7.15.3", "@microsoft/api-extractor": "^7.18.16", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.3.1", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "rollup": "^2.58.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "27.0.7", "typescript": "^4.4"}, "gitHead": "6f392f14787d81671b3ba3000244dbcbc0162628", "_id": "redux-thunk@2.4.2", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-+P3TjtnP0k/FEjcBL5FZpoovtvrTNT/UXd4/sluaSyrURlSlhLSzEdfsTBW7WsKB6yPvgd7q/iZPICFjW4o57Q==", "shasum": "b9d05d11994b99f7a91ea223e8b04cf0afa5ef3b", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.2.tgz", "fileCount": 12, "unpackedSize": 32059, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmseofQ4KiJh54jnfQlbYc6ES5vSVxvFZ0N0+IOI0X9AIhALkK4Fxi0TjSPNFqc2asF27v/ltG6C/040tcvUAGrpT/"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZHFUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcWw/+Jy/62+62ZrjuKbUdgoXLCt+uvAgO3eKUY/3UL3CbwAwticSt\r\nSAuuew89OT9XHmMSz6HbAQqX6UvRnD3SepkaFwh/9k8OC4uwyjIrVfUticch\r\nfcphc8rsuCxiUcWk/LZPTYlRM0+yvOaYv5sYT8jMWT4uBUZIEZy+b51B38Qn\r\n4ofKsqLSsp6MvUuYplVnIdaqjGp9ZGBLouIK7SNlUnuPehLfykgtgW1Xa75l\r\nFlhP4bqcldvE6/r3EQVsmnQhBZ1YcHKTDSydWkouMZ4IXkwJkoOTeovP5Q8p\r\nKzIvQtpj8n8ZoSc6kQhlHeHYsLEmeL/uVqDg27tJwoZsi1yjAcJvB2AHeBFc\r\n2KA0l3pXyssJ3PHTOUsJ6/SmJYsZWRDCAqI/7Lqdq+y3sE5OSWe3gOwcq/GB\r\nzCk+6TkujSmFYZJAA8g7pORVyNeBm7If77y3SrSWSHjYiYWCqT9CTIZH4HyS\r\n4FNUaeQzZf87vACaZL10pI7M55Gp+b7HGtTmJ9EVEMMY1dP13FQ+zj87lt1q\r\nx+RAyJpY2BJTPR9ZHFUXpOlTvjJJG+9SPLHi8BaKxX2rqw2dbH1VhKp9kr+i\r\nv20xr6UCKr5vPeVv3PLUo1N9HWtGXR0Lxd9qAMnUvAKu4w4x7489pB2OxMgP\r\nFCms4YXLbCy93VPqN274IugM7XQQnYfil3Q=\r\n=sFQz\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_2.4.2_1667526996468_0.6171027835648735"}, "_hasShrinkwrap": false}, "3.0.0-alpha.0": {"name": "redux-thunk", "version": "3.0.0-alpha.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./es/index.d.ts", "import": "./es/index.js", "default": "./lib/index.js"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build", "format": "prettier --write {src,test,typescript_test}/**/*.{js,ts}", "format:check": "prettier --check {src,test,typescript_test}/**/*.{js,ts}", "lint": "eslint {src,test,typescript_test}/**/*.{js,ts}", "test": "jest", "test:cov": "jest --coverage", "test:typescript": "npm run test:typescript:main && npm run test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir lib ", "build:es": "babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/redux-thunk.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/redux-thunk.min.js", "build:types": "tsc", "build": "rimraf dist lib es && npm run build:types && npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "api-types": "api-extractor run --local"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@babel/register": "^7.15.3", "@microsoft/api-extractor": "^7.18.16", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.3.1", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "rollup": "^2.58.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "27.0.7", "typescript": "^4.4"}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/workflow/status/reduxjs/redux-thunk/Tests)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the default `thunk` export has an attached `thunk.withExtraArgument()` function that should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(\r\n  reducer,\r\n  applyMiddleware(thunk.withExtraArgument(api))\r\n)\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "gitHead": "0ddba7627daab3b992ef8c7d45047b3de821045b", "_id": "redux-thunk@3.0.0-alpha.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-438+HXQwuKbxsPlgjNgFn+kxv9goFYKIBDprgFHI+VA2R8D8YUcmUobWh7XfvL6FAh/mqygrzf3CmYAEfpUsUQ==", "shasum": "819a6c2f4c72532a8f620a93a723e3f8d48b1717", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0-alpha.0.tgz", "fileCount": 12, "unpackedSize": 32284, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCR1mtHybpy4tCafmN/VOoA6/HPZDsdTcJ4jDdxJAsODAIhALSeMUUKTfXGpJyhVuX44jHEPAaWmnQ9UQxD7FtXXUym"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxe0JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmphAAlpxLqKK1s4g0xo8TNqssURngSRqvi4jyp8XazRa8nntdvIhl\r\nqfTD6g/W/+3OK00xtbkXkubVHIsbx1hNw7nfr9irNS0Hc9EKuwEFaOwPqH8S\r\n8VQKKNx+gF6Su7MgBN9tS0nGGGy8avCv7QtjB9HYYXMm9M7RTvbH0J4OfprU\r\nuPhx0ZRXmh+a2Fq5FOTn5ewDTnBP0E6MWpjlKd+ywxGceJys0LjkowHJiYDY\r\nKSLcouDW+TuLDDXbgNB+HWKspY4ni34wUDdh+tDW0Zh/7eMd5TKb8kO2kgAz\r\noatCPxZ1m6q8FhFLu4FW5tGGbaZOkSKw3RMoaTE9za0X3iHCzWhheiNihGGl\r\nBqSMvqQrWgFuwvetbL3dgYPRmnSTXCuQJOJOwYdTBESp5MrIyc/ClcPxDFqv\r\ntwFNjCDfmlyT/Aly5P6J2YJjdCB8sqv7jl/0SgCwKBl39K5+B6wG0z3o8325\r\nDZp73vvZxS1Tjm33jxZ1WFZQWt4kxAqPK+s6CfbHIYZx2y6CWhsF3nE7Xqpg\r\ncz/EjGgbIEE25c6DeL4io0KpapC6pT9E/c2LT7UsqQintbxLa/8J8eHq3dYp\r\ngqqvPHf7bSGGXHxbOruxVz6LFgupMNZZPjQEzeW/bwicXlTM1SQOp20k0NLV\r\nL5EoEnpPmHcyZLCCs0306ZXZtnlE5iipIwo=\r\n=bZOD\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0-alpha.0_1673915657010_0.5848273346486135"}, "_hasShrinkwrap": false}, "3.0.0-alpha.1": {"name": "redux-thunk", "version": "3.0.0-alpha.1", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./es/index.d.ts", "import": "./es/index.js", "default": "./lib/index.js"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build", "format": "prettier --write {src,test,typescript_test}/**/*.{js,ts}", "format:check": "prettier --check {src,test,typescript_test}/**/*.{js,ts}", "lint": "eslint {src,test,typescript_test}/**/*.{js,ts}", "test": "jest", "test:cov": "jest --coverage", "test:typescript": "npm run test:typescript:main && npm run test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir lib ", "build:es": "babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/redux-thunk.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/redux-thunk.min.js", "build:types": "tsc", "build": "rimraf dist lib es && npm run build:types && npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "api-types": "api-extractor run --local"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@babel/register": "^7.15.3", "@microsoft/api-extractor": "^7.18.16", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.3.1", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "rollup": "^2.58.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "27.0.7", "typescript": "^4.4"}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/workflow/status/reduxjs/redux-thunk/Tests)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the default `thunk` export has an attached `thunk.withExtraArgument()` function that should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(\r\n  reducer,\r\n  applyMiddleware(thunk.withExtraArgument(api))\r\n)\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "gitHead": "587a85b1d908e8b7cf2297bec6e15807d3b7dc62", "_id": "redux-thunk@3.0.0-alpha.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-j547PgblEdRakm9PCUwtZ0C1amoiG8rbSliT8AKUEunVsmtemM22C8UOQ+8EcVP5tgHxwd/L0gUyLGA/JJanjQ==", "shasum": "68fafb65a4caa353a54e189518dffa61d6c03907", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0-alpha.1.tgz", "fileCount": 12, "unpackedSize": 32926, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICihuz4ZIR1POriVlad9LUrnSbtWCgG5ieXCngU8fRNqAiBDDQ63kQdRp/z8dNix6NpajzJy0kqZIP9S8/b+V44wdg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjx1XCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRIhAAhtV4LqeXcl7KJFKuBHS1j7dj7KDZMM/NaZ1h/pOSB7ifurMX\r\nB1Ke2EYb9zHzNvpyRQTKzyitbyBAWRFKs7GoIp0LQf5aBUc7MbavRqz4b4WN\r\nE6eJRNNtGNcE8TUjVxgckQYc3Xnk+9Du4+OsOKaMdchDD2v3rDxaAeBy2ovJ\r\nl8WthF3Vrf4BZhAXy8dutGWDyFQh9cbBO1RGwynPa6fQ1byB1JJ6LFFNqzo0\r\nVRJIw0Co6FG8vAgYbqcbO60MTV5PkhQBBYb++OsNwJrQnDguOyz2YXwRRQ7q\r\nCb2DOSnr05tqM4PFGXT/71SvCWNbuaW/leE5dUsmEjxrQc5KwqztXJML7m9E\r\n78Wz/iVj9FgzE8LnXE4Cwn6qZUlOwPhmNpvZkNPl3uZVUdKf521Zvh0fKd+l\r\nNblFvsvK95XZgdM4JY1Q+vP61PfwYfhUZgUxsU/oXisloP/aW8I1CugS7xWC\r\navs2UkoNA42rtNWDlVOfYn+mppT2QyWrmL81273xc5UNudqm6t6F4p6pORjc\r\nxnuopP4gkZjb4n2YVzTtgvWyHu/ruG+5jf0fa9Ee3RR7Gl4BCRYj3VzFYUV5\r\n3ziyVX+ivwudAXUie9y2gXy4MpQez/9qvvftSk7mZMHI9RvVWy6Z3lkAYKV0\r\nN/Q4jEz+9Ibw0QlU0RO+ofp/sZkSfa7otJs=\r\n=qd1X\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0-alpha.1_1674008002325_0.36289983393436787"}, "_hasShrinkwrap": false}, "3.0.0-alpha.2": {"name": "redux-thunk", "version": "3.0.0-alpha.2", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.mjs", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}, "./extend-redux": {"types": "./extend-redux.d.ts"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "yarn test:typescript:main && yarn test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "tsup": "^6.7.0", "typescript": "^4.4", "vitest": "^0.29.8"}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/workflow/status/reduxjs/redux-thunk/Tests)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the default `thunk` export has an attached `thunk.withExtraArgument()` function that should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(\r\n  reducer,\r\n  applyMiddleware(thunk.withExtraArgument(api))\r\n)\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "gitHead": "6c46a8a42b7e38e0baf5c7f7e80a9c18054f2108", "_id": "redux-thunk@3.0.0-alpha.2", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-+mBSXvn4GvOsipnsbJjRMgM5U9MBgUCdyIAvxkQ+5R1Sekp7OSSynvBZKNWyOIbsFiGEVAG385DDtk4QV0J6ZA==", "shasum": "bc71a21bb38ee8144842ebd2be37ea319b1cb3d0", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0-alpha.2.tgz", "fileCount": 9, "unpackedSize": 28585, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+4Em9BkhNTkyhnjwkTs+gFAGYvbSYklk0dn0bPl+kfwIhALSTs51n/Rlepdi41Xi3dLkSK4QICMAL78lJWBrJvfc9"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKiGyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBIg/8CGZp45LA/aum/tYjsamhvMm6aQyeR+6F4+exwtZqoGQhYDUV\r\n+BKHVaUaF6RkUDKsPsiRrjwV805Xj9LDNboRhAmBgIJ/JE944SAPSF0YOP+/\r\nRU9eqBX8Rxyij3Foub95PKM0XpFBBnxBY3j5Kz8WWFZFJL1yE3gsMCdLJ6iL\r\naI5ZNyz1EnIfzOiGRTqBM+cy6xkDFr96vgiNgQ9QYw75jW0Rzj2uWosoGin5\r\nEiXcz3aOXwmokdTAiWCEV/N7dOQ0k3Ej5YuqLgsTMJhoYNe6tcnkMxLrI7/I\r\nKVTb9kqMfExHMgmmcT8wbX6NgqQerpTDp7+tM6PzGTubLFVrYOlANTdql92W\r\ncZaifjkyczGFp4LyRbWA/BCEkVrC2Pm22LghT9h4/35Q2yeswWSh9y4/H63u\r\npBALAE/ik9BzESp5SOt9cZWsH+cLLIB/5b+AYzXUxxs3kWor1X+ugrSlMX0y\r\nQxIj2TG/XfRp6HGlqLWAzQ+SfZl4iFiKpQ0eY3OdlqjKMyKAKp1uoOovdInB\r\nkNP8rf3YEKIX3nZSU4ZhS7QZpYOdIgB12Nr2E5xHgWRzAYHgNyU1ZU7JAhxX\r\n4kAF3f6FocYyR+kkT7wMCtIHggkqRxU/w0TQa3uo03REWDsrjOOeUfdhojNO\r\nhgMkx7QXCDSxvyufFFlKxFCdq8MPUhBacNM=\r\n=3i0s\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0-alpha.2_1680482738154_0.45058932300508836"}, "_hasShrinkwrap": false}, "3.0.0-alpha.3": {"name": "redux-thunk", "version": "3.0.0-alpha.3", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.legacy-esm.js", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}, "./extend-redux": {"types": "./extend-redux.d.ts"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "yarn test:typescript:main && yarn test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "tsup": "^6.7.0", "typescript": "^4.4", "vitest": "^0.29.8"}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/workflow/status/reduxjs/redux-thunk/Tests)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the default `thunk` export has an attached `thunk.withExtraArgument()` function that should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(\r\n  reducer,\r\n  applyMiddleware(thunk.withExtraArgument(api))\r\n)\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "gitHead": "b6605fb0bfdffa5d50d6727dcb0dfb14bb494007", "_id": "redux-thunk@3.0.0-alpha.3", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-tRoYt//uXllaNHsdla4sFbCPf0/50BYf6NDRNtg66IZXZcLXiQNTH5LKcIISd0+7BZqFq204JgJcxtih4QLTXQ==", "shasum": "b3762c7e6939d1cf56fbacba679d3c7101f115f6", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0-alpha.3.tgz", "fileCount": 10, "unpackedSize": 29012, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1ZKGsPymsuUwz/dXMmQOBRobiidPdNPPkoD+sckzG9gIgXrCRdZ3J4v03a4zEXmgzYyBUBemaYylAZw8Gg+okFdE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKkWNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomEA/8CbDts4mpyYMiZcYgp/d0gpeG4lV5mum3Wilc72ZsFzSaOr3n\r\nQqpye2eALwWm35H4CyzhoIUyeKqio7epBOiPrYTXwuJpcV06YU1QBCSCiDzW\r\nRxMxc2MlZxng9EwBFHeDZcRVivSDuB2GJh89tTznWpHn7YNNe0Yz6WE/wA3m\r\nPkENn+h5XPzTdtWRt0F8sfmMFrvPlf4AorV62S/fVqlEa0ik5Lo2MiCrYH+/\r\nlJuUCNa+5AkL+8AmbLe50Gnttfgva5SemFJMXzBlhsa4UcPRcT8aYSVq/moe\r\nTBr2shjE3ZhPipWpUs8Q9BTkjRt1PbGJrmQU09mQ8KC9CbOjFOZ/FIyoI5p1\r\nYb+asOJkvyXLMT1ENvo6wLmMhj56AEY4rPR3JFozdJLtHcKGmZYhgguVaViW\r\nnVOwHd+V0aCpGxZZrJPK3Vnew30vzOAfe7JIxP3PaqHIu60O1i5Ydr9wh+Cz\r\nqhKPEblqru1x+1B/t09gBdszQqRKwGxl8msvyXtLjciHcObE1Ne2dyfh1SZ/\r\nZooTEbsmQrHjQFy9UOVwtNRjKy01M7KMryja8Hd1WHOQa0Ov3s43DhpbneEk\r\n9weDK9wqxTL64W5HH04PKHEeOa1Xqi1T7WAlNPc1PbI/Ao8JTEGdJsjZ1BtZ\r\nQImMy5pex0xcFEF4deq1AViG67VZLn0XsZM=\r\n=ysB/\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0-alpha.3_1680491916908_0.810006852935889"}, "_hasShrinkwrap": false}, "3.0.0-beta.0": {"name": "redux-thunk", "version": "3.0.0-beta.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.legacy-esm.js", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}, "./extend-redux": {"types": "./extend-redux.d.ts"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "yarn test:typescript:main && yarn test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^4 || ^5.0.0-beta.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "tsup": "^6.7.0", "typescript": "^4.4", "vitest": "^0.29.8"}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/workflow/status/reduxjs/redux-thunk/Tests)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the named export `withExtraArgument()` function should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(reducer, applyMiddleware(withExtraArgument(api)))\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "gitHead": "ca8df60eeaf9d72bbf0e8947097d8cc08ca955ab", "_id": "redux-thunk@3.0.0-beta.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-BLed4FtBhPv52AgqeR7DiOhrDA8z6owqXOkObOqgl1kwq4QQ1T74dy32qxyWsdyAlvq9wAHHW6t4tlxz8XnFhA==", "shasum": "3c8086a00657b12ca9615e57796ed56747eea07a", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0-beta.0.tgz", "fileCount": 10, "unpackedSize": 28556, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBndTcLwpmfopLn5yoZXG8wV01C6eFqOSKD9MI/2nJWjAiEA8x6rLoRb9uYJ0ezT2ukIlADq/yFyZNAI6zhcIsrcxYc="}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0-beta.0_1693085044223_0.36563913558099825"}, "_hasShrinkwrap": false}, "3.0.0-rc.0": {"name": "redux-thunk", "version": "3.0.0-rc.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.legacy-esm.js", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}, "./extend-redux": {"types": "./extend-redux.d.ts"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "yarn test:typescript:main && yarn test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^5.0.0-rc.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "tsup": "7.0.0", "typescript": "^5.0", "vitest": "^0.32.0"}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/reduxjs/redux-thunk/test.yml?branch=master)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the named export `withExtraArgument()` function should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(reducer, applyMiddleware(withExtraArgument(api)))\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "gitHead": "0ad3af14b90cae1b807ac839d5bbca3a1c229c8b", "_id": "redux-thunk@3.0.0-rc.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-2NcCM9cZZT+mrAISy0LWYKX4HjeMg7o8vcs/JgZCx8o+3/sXmzW7tk64Xvc6wTQwIWinpQCTX6eHNGCF4en/nA==", "shasum": "350c1db730757bd64930db43055b506b9e988e42", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0-rc.0.tgz", "fileCount": 10, "unpackedSize": 28517, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDP+QGv6SNWjydz8kn+7prmlqrL8acyqsH69GPTOFPr/QIhAJSL1Py0cFzmed0IPhmk0rCt5FnwR+eFQwclWnnsDimK"}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0-rc.0_1700108563949_0.6275001116405763"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "redux-thunk", "version": "3.0.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.legacy-esm.js", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}, "./extend-redux": {"types": "./extend-redux.d.ts"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "yarn test:typescript:main && yarn test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^5.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^5", "rimraf": "^3.0.2", "tsup": "7.0.0", "typescript": "^5.0", "vitest": "^0.32.0"}, "gitHead": "b419ba6a3e6b57ec30c372c3ad9af73065ae833d", "_id": "redux-thunk@3.0.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-XxuIn2AUITlJbfy29xOg5YVyBLFRJ9BVd8kTsNWXsM7XzpXlbt8hsJ4KCLwj5LvxD0wi7Ut+XwPgIGL/ypKbRg==", "shasum": "136cb4a948e2d5fc4b8f28dd0c25a1769beea1f5", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.0.tgz", "fileCount": 10, "unpackedSize": 28507, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW+inLwkQKL9Nxr1Up738jmFAZH7+p1WZoQVCVgvPhxwIgRk112+WxLrtvBkH5wn/Ck7Q6WqlvlU92XRm6g70cPj4="}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.0_1701663569432_0.8327064004182554"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "redux-thunk", "version": "3.0.1", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.legacy-esm.js", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}, "./extend-redux": {"types": "./extend-redux.d.ts"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "yarn test:typescript:main && yarn test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^5.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^5", "rimraf": "^3.0.2", "tsup": "7.0.0", "typescript": "^5.0", "vitest": "^0.32.0"}, "gitHead": "d4909d3e10f254339799a895ece47ee6b523491d", "_id": "redux-thunk@3.0.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-otjYlnsVhrubBi0OPGpmVVaIRAeF1j3TprEzU+iIPMN2aAnA8S+nMRbGPBmSAfwLXy1j5iUj2Z5wC+BaLMmFBw==", "shasum": "1022e8c21f1f9ecead282fc59b415f7dab82a1c9", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.0.1.tgz", "fileCount": 10, "unpackedSize": 28668, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmWPZrag3idIQa9VqyN5eie5ste5dr0A6I4Omih5nk6AiEAnyLBFe/EEy7JUksqEihY+V3iS7kFX+t+fbHbz6UMM4g="}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.0.1_1701664855778_0.29484647223762583"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "redux-thunk", "version": "3.1.0", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "dist/cjs/redux-thunk.cjs", "module": "dist/redux-thunk.legacy-esm.js", "types": "dist/redux-thunk.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-thunk.d.ts", "import": "./dist/redux-thunk.mjs", "default": "./dist/cjs/redux-thunk.cjs"}}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "yarn clean && yarn lint && yarn test && yarn build", "format": "prettier --write \"{src,test,typescript_test}/**/*.{js,ts}\"", "format:check": "prettier --check \"{src,test,typescript_test}/**/*.{js,ts}\"", "lint": "eslint \"{src,test,typescript_test}/**/*.{js,ts}\"", "test": "vitest run", "test:cov": "vitest run --coverage", "test:typescript": "tsc --noEmit -p typescript_test/tsconfig.json", "build": "tsup", "prepack": "yarn build"}, "peerDependencies": {"redux": "^5.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.4.1", "redux": "^5", "rimraf": "^3.0.2", "tsup": "7.0.0", "typescript": "^5.0", "vitest": "^0.32.0"}, "gitHead": "1de247626f3bc13e18901b451575d1c22e59718a", "_id": "redux-thunk@3.1.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-NW2r5T6ksUKXCabzhL9z+h206HQw/NJkcLm1GPImRQ8IzfXwRGqjVhKJGauHirT0DAuyy6hjdnMZaRoAcy0Klw==", "shasum": "94aa6e04977c30e14e892eae84978c1af6058ff3", "tarball": "https://registry.npmjs.org/redux-thunk/-/redux-thunk-3.1.0.tgz", "fileCount": 9, "unpackedSize": 26757, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGaqgwLDk1Xr9ba8Ar614mhqbljM6212rbmCkm5ic8DAiBqI8YXACzC0ZSfoEeRfLKX1MwqCh6yJYDPgYKU4tsCyQ=="}]}, "_npmUser": {"name": "acemarke", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-thunk_3.1.0_1701697837080_0.4062601656573741"}, "_hasShrinkwrap": false}}, "readme": "# Redux Thunk\r\n\r\nThunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware) for Redux. It allows writing functions with logic inside that can interact with a Redux store's `dispatch` and `getState` methods.\r\n\r\nFor complete usage instructions and useful patterns, see the [Redux docs **Writing Logic with Thunks** page](https://redux.js.org/usage/writing-logic-thunks).\r\n\r\n![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/reduxjs/redux-thunk/test.yml?branch=master)\r\n[![npm version](https://img.shields.io/npm/v/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n[![npm downloads](https://img.shields.io/npm/dm/redux-thunk.svg?style=flat-square)](https://www.npmjs.com/package/redux-thunk)\r\n\r\n## Installation and Setup\r\n\r\n### Redux Toolkit\r\n\r\nIf you're using [our official Redux Toolkit package](https://redux-toolkit.js.org) as recommended, there's nothing to install - RTK's `configureStore` API already adds the thunk middleware by default:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\n\r\nimport todosReducer from './features/todos/todosSlice'\r\nimport filtersReducer from './features/filters/filtersSlice'\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    todos: todosReducer,\r\n    filters: filtersReducer\r\n  }\r\n})\r\n\r\n// The thunk middleware was automatically added\r\n```\r\n\r\n### Manual Setup\r\n\r\nIf you're using the basic Redux `createStore` API and need to set this up manually, first add the `redux-thunk` package:\r\n\r\n```sh\r\nnpm install redux-thunk\r\n\r\nyarn add redux-thunk\r\n```\r\n\r\nThe thunk middleware is the default export.\r\n\r\n<details>\r\n<summary><b>More Details: Importing the thunk middleware</b></summary>\r\n\r\nIf you're using ES modules:\r\n\r\n```js\r\nimport thunk from 'redux-thunk' // no changes here 😀\r\n```\r\n\r\nIf you use Redux Thunk 2.x in a CommonJS environment,\r\n[don’t forget to add `.default` to your import](https://github.com/reduxjs/redux-thunk/releases/tag/v2.0.0):\r\n\r\n```diff\r\n- const thunk = require('redux-thunk')\r\n+ const thunk = require('redux-thunk').default\r\n```\r\n\r\nAdditionally, since 2.x, we also support a\r\n[UMD build](https://unpkg.com/redux-thunk/dist/redux-thunk.min.js) for use as a global script tag:\r\n\r\n```js\r\nconst ReduxThunk = window.ReduxThunk\r\n```\r\n\r\n</details>\r\n\r\nThen, to enable Redux Thunk, use\r\n[`applyMiddleware()`](https://redux.js.org/api/applymiddleware):\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers/index'\r\n\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n```\r\n\r\n### Injecting a Custom Argument\r\n\r\nSince 2.1.0, Redux Thunk supports injecting a custom argument into the thunk middleware. This is typically useful for cases like using an API service layer that could be swapped out for a mock service in tests.\r\n\r\nFor Redux Toolkit, the `getDefaultMiddleware` callback inside of `configureStore` lets you pass in a custom `extraArgument`:\r\n\r\n```js\r\nimport { configureStore } from '@reduxjs/toolkit'\r\nimport rootReducer from './reducer'\r\nimport { myCustomApiService } from './api'\r\n\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: myCustomApiService\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  // The `extraArgument` is the third arg for thunk functions\r\n  return (dispatch, getState, api) => {\r\n    // you can use api here\r\n  }\r\n}\r\n```\r\n\r\nIf you need to pass in multiple values, combine them into a single object:\r\n\r\n```js\r\nconst store = configureStore({\r\n  reducer: rootReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      thunk: {\r\n        extraArgument: {\r\n          api: myCustomApiService,\r\n          otherValue: 42\r\n        }\r\n      }\r\n    })\r\n})\r\n\r\n// later\r\nfunction fetchUser(id) {\r\n  return (dispatch, getState, { api, otherValue }) => {\r\n    // you can use api and something else here\r\n  }\r\n}\r\n```\r\n\r\nIf you're setting up the store by hand, the named export `withExtraArgument()` function should be used to generate the correct thunk middleware:\r\n\r\n```js\r\nconst store = createStore(reducer, applyMiddleware(withExtraArgument(api)))\r\n```\r\n\r\n## Why Do I Need This?\r\n\r\nWith a plain basic Redux store, you can only do simple synchronous updates by\r\ndispatching an action. Middleware extends the store's abilities, and lets you\r\nwrite async logic that interacts with the store.\r\n\r\nThunks are the recommended middleware for basic Redux side effects logic,\r\nincluding complex synchronous logic that needs access to the store, and simple\r\nasync logic like AJAX requests.\r\n\r\nFor more details on why thunks are useful, see:\r\n\r\n- **Redux docs: Writing Logic with Thunks**  \r\n  https://redux.js.org/usage/writing-logic-thunks  \r\n  The official usage guide page on thunks. Covers why they exist, how the thunk middleware works, and useful patterns for using thunks.\r\n\r\n- **Stack Overflow: Dispatching Redux Actions with a Timeout**  \r\n  http://stackoverflow.com/questions/35411423/how-to-dispatch-a-redux-action-with-a-timeout/35415559#35415559  \r\n  Dan Abramov explains the basics of managing async behavior in Redux, walking\r\n  through a progressive series of approaches (inline async calls, async action\r\n  creators, thunk middleware).\r\n\r\n- **Stack Overflow: Why do we need middleware for async flow in Redux?**  \r\n  http://stackoverflow.com/questions/34570758/why-do-we-need-middleware-for-async-flow-in-redux/34599594#34599594  \r\n  Dan Abramov gives reasons for using thunks and async middleware, and some\r\n  useful patterns for using thunks.\r\n\r\n- **What the heck is a \"thunk\"?**  \r\n  https://daveceddia.com/what-is-a-thunk/  \r\n  A quick explanation for what the word \"thunk\" means in general, and for Redux\r\n  specifically.\r\n\r\n- **Thunks in Redux: The Basics**  \r\n  https://medium.com/fullstack-academy/thunks-in-redux-the-basics-85e538a3fe60  \r\n  A detailed look at what thunks are, what they solve, and how to use them.\r\n\r\nYou may also want to read the\r\n**[Redux FAQ entry on choosing which async middleware to use](https://redux.js.org/faq/actions#what-async-middleware-should-i-use-how-do-you-decide-between-thunks-sagas-observables-or-something-else)**.\r\n\r\nWhile the thunk middleware is not directly included with the Redux core library,\r\nit is used by default in our\r\n**[`@reduxjs/toolkit` package](https://github.com/reduxjs/redux-toolkit)**.\r\n\r\n## Motivation\r\n\r\nRedux Thunk [middleware](https://redux.js.org/tutorials/fundamentals/part-4-store#middleware)\r\nallows you to write action creators that return a function instead of an action.\r\nThe thunk can be used to delay the dispatch of an action, or to dispatch only if\r\na certain condition is met. The inner function receives the store methods\r\n`dispatch` and `getState` as parameters.\r\n\r\nAn action creator that returns a function to perform asynchronous dispatch:\r\n\r\n```js\r\nconst INCREMENT_COUNTER = 'INCREMENT_COUNTER'\r\n\r\nfunction increment() {\r\n  return {\r\n    type: INCREMENT_COUNTER\r\n  }\r\n}\r\n\r\nfunction incrementAsync() {\r\n  return dispatch => {\r\n    setTimeout(() => {\r\n      // Yay! Can invoke sync or async actions with `dispatch`\r\n      dispatch(increment())\r\n    }, 1000)\r\n  }\r\n}\r\n```\r\n\r\nAn action creator that returns a function to perform conditional dispatch:\r\n\r\n```js\r\nfunction incrementIfOdd() {\r\n  return (dispatch, getState) => {\r\n    const { counter } = getState()\r\n\r\n    if (counter % 2 === 0) {\r\n      return\r\n    }\r\n\r\n    dispatch(increment())\r\n  }\r\n}\r\n```\r\n\r\n## What’s a thunk?!\r\n\r\nA [thunk](https://en.wikipedia.org/wiki/Thunk) is a function that wraps an\r\nexpression to delay its evaluation.\r\n\r\n```js\r\n// calculation of 1 + 2 is immediate\r\n// x === 3\r\nlet x = 1 + 2\r\n\r\n// calculation of 1 + 2 is delayed\r\n// foo can be called later to perform the calculation\r\n// foo is a thunk!\r\nlet foo = () => 1 + 2\r\n```\r\n\r\nThe term [originated](https://en.wikipedia.org/wiki/Thunk#cite_note-1) as a\r\nhumorous past-tense version of \"think\".\r\n\r\n## Composition\r\n\r\nAny return value from the inner function will be available as the return value\r\nof `dispatch` itself. This is convenient for orchestrating an asynchronous\r\ncontrol flow with thunk action creators dispatching each other and returning\r\nPromises to wait for each other’s completion:\r\n\r\n```js\r\nimport { createStore, applyMiddleware } from 'redux'\r\nimport thunk from 'redux-thunk'\r\nimport rootReducer from './reducers'\r\n\r\n// Note: this API requires redux@>=3.1.0\r\nconst store = createStore(rootReducer, applyMiddleware(thunk))\r\n\r\nfunction fetchSecretSauce() {\r\n  return fetch('https://www.google.com/search?q=secret+sauce')\r\n}\r\n\r\n// These are the normal action creators you have seen so far.\r\n// The actions they return can be dispatched without any middleware.\r\n// However, they only express “facts” and not the “async flow”.\r\n\r\nfunction makeASandwich(forPerson, secretSauce) {\r\n  return {\r\n    type: 'MAKE_SANDWICH',\r\n    forPerson,\r\n    secretSauce\r\n  }\r\n}\r\n\r\nfunction apologize(fromPerson, toPerson, error) {\r\n  return {\r\n    type: 'APOLOGIZE',\r\n    fromPerson,\r\n    toPerson,\r\n    error\r\n  }\r\n}\r\n\r\nfunction withdrawMoney(amount) {\r\n  return {\r\n    type: 'WITHDRAW',\r\n    amount\r\n  }\r\n}\r\n\r\n// Even without middleware, you can dispatch an action:\r\nstore.dispatch(withdrawMoney(100))\r\n\r\n// But what do you do when you need to start an asynchronous action,\r\n// such as an API call, or a router transition?\r\n\r\n// Meet thunks.\r\n// A thunk in this context is a function that can be dispatched to perform async\r\n// activity and can dispatch actions and read state.\r\n// This is an action creator that returns a thunk:\r\nfunction makeASandwichWithSecretSauce(forPerson) {\r\n  // We can invert control here by returning a function - the \"thunk\".\r\n  // When this function is passed to `dispatch`, the thunk middleware will intercept it,\r\n  // and call it with `dispatch` and `getState` as arguments.\r\n  // This gives the thunk function the ability to run some logic, and still interact with the store.\r\n  return function (dispatch) {\r\n    return fetchSecretSauce().then(\r\n      sauce => dispatch(makeASandwich(forPerson, sauce)),\r\n      error => dispatch(apologize('The Sandwich Shop', forPerson, error))\r\n    )\r\n  }\r\n}\r\n\r\n// Thunk middleware lets me dispatch thunk async actions\r\n// as if they were actions!\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('Me'))\r\n\r\n// It even takes care to return the thunk’s return value\r\n// from the dispatch, so I can chain Promises as long as I return them.\r\n\r\nstore.dispatch(makeASandwichWithSecretSauce('My partner')).then(() => {\r\n  console.log('Done!')\r\n})\r\n\r\n// In fact I can write action creators that dispatch\r\n// actions and async actions from other action creators,\r\n// and I can build my control flow with Promises.\r\n\r\nfunction makeSandwichesForEverybody() {\r\n  return function (dispatch, getState) {\r\n    if (!getState().sandwiches.isShopOpen) {\r\n      // You don’t have to return Promises, but it’s a handy convention\r\n      // so the caller can always call .then() on async dispatch result.\r\n\r\n      return Promise.resolve()\r\n    }\r\n\r\n    // We can dispatch both plain object actions and other thunks,\r\n    // which lets us compose the asynchronous actions in a single flow.\r\n\r\n    return dispatch(makeASandwichWithSecretSauce('My Grandma'))\r\n      .then(() =>\r\n        Promise.all([\r\n          dispatch(makeASandwichWithSecretSauce('Me')),\r\n          dispatch(makeASandwichWithSecretSauce('My wife'))\r\n        ])\r\n      )\r\n      .then(() => dispatch(makeASandwichWithSecretSauce('Our kids')))\r\n      .then(() =>\r\n        dispatch(\r\n          getState().myMoney > 42\r\n            ? withdrawMoney(42)\r\n            : apologize('Me', 'The Sandwich Shop')\r\n        )\r\n      )\r\n  }\r\n}\r\n\r\n// This is very useful for server side rendering, because I can wait\r\n// until data is available, then synchronously render the app.\r\n\r\nstore\r\n  .dispatch(makeSandwichesForEverybody())\r\n  .then(() =>\r\n    response.send(ReactDOMServer.renderToString(<MyApp store={store} />))\r\n  )\r\n\r\n// I can also dispatch a thunk async action from a component\r\n// any time its props change to load the missing data.\r\n\r\nimport { connect } from 'react-redux'\r\nimport { Component } from 'react'\r\n\r\nclass SandwichShop extends Component {\r\n  componentDidMount() {\r\n    this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n  }\r\n\r\n  componentDidUpdate(prevProps) {\r\n    if (prevProps.forPerson !== this.props.forPerson) {\r\n      this.props.dispatch(makeASandwichWithSecretSauce(this.props.forPerson))\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return <p>{this.props.sandwiches.join('mustard')}</p>\r\n  }\r\n}\r\n\r\nexport default connect(state => ({\r\n  sandwiches: state.sandwiches\r\n}))(SandwichShop)\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "timdorr", "email": "<EMAIL>"}, {"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "acemarke", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "time": {"modified": "2024-05-06T13:38:44.899Z", "created": "2015-07-13T13:37:49.846Z", "0.1.0": "2015-07-13T13:37:49.846Z", "1.0.0": "2015-09-17T17:36:45.457Z", "1.0.1": "2015-12-13T20:22:23.607Z", "1.0.2": "2015-12-14T08:59:28.515Z", "1.0.3": "2015-12-28T22:31:23.546Z", "2.0.0": "2016-03-06T13:02:42.003Z", "2.0.1": "2016-03-06T13:12:06.001Z", "2.1.0": "2016-05-10T15:11:33.770Z", "2.1.1": "2017-01-18T06:15:43.554Z", "2.1.2": "2017-01-18T08:11:13.223Z", "2.2.0": "2017-01-18T08:12:30.464Z", "2.3.0": "2018-05-28T18:44:08.278Z", "2.4.0": "2021-10-26T01:01:12.141Z", "2.4.1": "2021-11-26T20:20:06.701Z", "2.4.2": "2022-11-04T01:56:36.659Z", "3.0.0-alpha.0": "2023-01-17T00:34:17.190Z", "3.0.0-alpha.1": "2023-01-18T02:13:22.461Z", "3.0.0-alpha.2": "2023-04-03T00:45:38.329Z", "3.0.0-alpha.3": "2023-04-03T03:18:37.086Z", "3.0.0-beta.0": "2023-08-26T21:24:04.409Z", "3.0.0-rc.0": "2023-11-16T04:22:44.180Z", "3.0.0": "2023-12-04T04:19:29.644Z", "3.0.1": "2023-12-04T04:40:55.984Z", "3.1.0": "2023-12-04T13:50:37.326Z"}, "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "repository": {"type": "git", "url": "git+https://github.com/reduxjs/redux-thunk.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/reduxjs/redux-thunk/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"danharper": true, "jlertle": true, "moogus": true, "sammyteahan": true, "kay.sackey": true, "jrnail23": true, "isik": true, "reergymerej": true, "kytart": true, "princetoad": true, "sandeepgy11": true, "dmitryscaletta": true, "dskecse": true, "alexjsdev": true, "jasonwang1888": true, "fsgdez": true, "newswim": true, "lagora": true, "brainpoint": true, "restmount": true, "monolithed": true, "ohcoder": true, "nelix": true, "knoja4": true, "xiaochao": true, "mswanson1524": true, "muroc": true, "itonyyo": true, "holly": true, "norlando": true, "asmattic": true, "tsyue": true, "atulmy": true, "tin-lek": true, "abuelwafa": true, "isa424": true, "ezdub": true, "escapeimagery": true, "serge-nikitin": true, "kaufmo": true, "alimaster": true, "nisimjoseph": true, "philipjc": true, "orenschwartz": true, "agamlarage": true, "dpjayasekara": true, "rethinkflash": true, "erikvold": true, "xhou": true, "flubox": true, "abhijitkalta": true, "nauhil": true, "tiggem1993": true, "npmmurali": true, "hccdj131": true, "tomgao365": true, "meeh": true, "kkho595": true, "ldq-first": true, "sternelee": true, "pwn": true, "coolhector": true, "derrickbeining": true, "hummatli": true, "taylorpzreal": true, "cfleschhut": true, "vjenks": true, "geofftech": true, "ryanlittle": true, "salvationz": true, "hugovila": true, "omkar.sheral.1989": true, "nikovacevic": true, "sherylhohman": true, "alek-s": true, "mluka": true, "hani": true, "vcordero07": true, "gamersdelight": true, "tedyhy": true, "leix3041": true, "andrewlam": true, "morogasper": true, "raciat": true, "aaronwells": true, "washiba": true, "juanf03": true}}