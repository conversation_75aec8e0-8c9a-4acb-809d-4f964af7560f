{"_id": "@vitest/pretty-format", "_rev": "54-d669ae5c087e8ef5e5cedb81f956d81b", "name": "@vitest/pretty-format", "dist-tags": {"latest": "3.2.4", "beta": "4.0.0-beta.2"}, "versions": {"2.0.2": {"name": "@vitest/pretty-format", "version": "2.0.2", "license": "MIT", "_id": "@vitest/pretty-format@2.0.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c2674fef447ad8469144fdc483e859f9b1664133", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.0.2.tgz", "fileCount": 4, "integrity": "sha512-SBCyOXfGVvddRd9r2PwoVR0fonQjh9BMIcBMlSzbcNwFfGr6ZhOhvBzurjvi2F4ryut2HcqiFhNeDVGwru8tLg==", "signatures": [{"sig": "MEQCIHBZ2WdPj/lz/F2gwfMjPPw+R6bFfdmkN4K9u/+w5FIDAiBYm2Ca996s4GgCEj2PFP8cy7MlWJ3WQmFKCq8cGx51AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45764}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c77a6c264bafb3348cf734e103fade86/vitest-pretty-format-2.0.2.tgz", "_integrity": "sha512-SBCyOXfGVvddRd9r2PwoVR0fonQjh9BMIcBMlSzbcNwFfGr6ZhOhvBzurjvi2F4ryut2HcqiFhNeDVGwru8tLg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.7.0", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.0.2_1720626384532_0.7540803717513649", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@vitest/pretty-format", "version": "2.0.3", "license": "MIT", "_id": "@vitest/pretty-format@2.0.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "30af705250cd055890091999e467968e41872c82", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.0.3.tgz", "fileCount": 4, "integrity": "sha512-URM4GLsB2xD37nnTyvf6kfObFafxmycCL8un3OC9gaCs5cti2u+5rJdIflZ2fUJUen4NbvF6jCufwViAFLvz1g==", "signatures": [{"sig": "MEQCIGBV/ksyW+2p85dXYstIFxpdYMjjL5XSmFXNeu2q/NlsAiAIsUdUPHuCi4KsLIJPK7VgJyMnHwqlTATBRl3IdNCrMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45764}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/11e5407a07dc62a9886bd3b212c365cf/vitest-pretty-format-2.0.3.tgz", "_integrity": "sha512-URM4GLsB2xD37nnTyvf6kfObFafxmycCL8un3OC9gaCs5cti2u+5rJdIflZ2fUJUen4NbvF6jCufwViAFLvz1g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.7.0", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.0.3_1721037802413_0.7480783632016919", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@vitest/pretty-format", "version": "2.0.4", "license": "MIT", "_id": "@vitest/pretty-format@2.0.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9a3934932e7f8ddd836b38c34ddaeec91bd0f82e", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.0.4.tgz", "fileCount": 4, "integrity": "sha512-RY<PERSON>l31STbNGqf4l2eQM1nvKPXE0NhC6Eq0suTTePc4mtMQ1Fn8qZmjV4emZdEdG2NOWGKSCrHZjmTqDCDoeFBw==", "signatures": [{"sig": "MEYCIQDrxiElMhWoMrTEiXfQ1Hs7lN1WdP7JRzw1LquDr+KOXgIhAP5cBtICA+zm4AYQbn2h0tHXU+KM6C5xBIc/We99L4s1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c92131fcd599a592c2b6cda5f6dd74b0/vitest-pretty-format-2.0.4.tgz", "_integrity": "sha512-RY<PERSON>l31STbNGqf4l2eQM1nvKPXE0NhC6Eq0suTTePc4mtMQ1Fn8qZmjV4emZdEdG2NOWGKSCrHZjmTqDCDoeFBw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.7.0", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.0.4_1721639597191_0.5639801670676219", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@vitest/pretty-format", "version": "2.0.5", "license": "MIT", "_id": "@vitest/pretty-format@2.0.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "91d2e6d3a7235c742e1a6cc50e7786e2f2979b1e", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.0.5.tgz", "fileCount": 4, "integrity": "sha512-h8k+1oWHfwTkyTkb9egzwNMfJAEx4veaPSnMeKbVSjp4euqGSbQlm5+6VHwTr7u4FJslVVsUG5nopCaAYdOmSQ==", "signatures": [{"sig": "MEUCIAY7DiaEtnHkCiq8xmXRJttZ8uxHSVP/MC/qyDId3VfXAiEArRw8cUz6pB0yQ4FsHCUs5FXs6XY0fjEbWItWV1qkPT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f7354d46aea412f5d39c348c553c36f2/vitest-pretty-format-2.0.5.tgz", "_integrity": "sha512-h8k+1oWHfwTkyTkb9egzwNMfJAEx4veaPSnMeKbVSjp4euqGSbQlm5+6VHwTr7u4FJslVVsUG5nopCaAYdOmSQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.7.0", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.0.5_1722422388597_0.5752567654127261", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.1": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.1", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "588c97b4a199e9b65ed0f4952a817985c0b9c5c3", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-XK4PNt1OLX1nw4KwRadr/0TcH4bSFt7vZV0exZKvHgZYhtawUYOzGerzRqrVoXty4l3f8aIRxhXkPFR0oht/5g==", "signatures": [{"sig": "MEQCIFCTz0ZUm5/lmnD9uDeP/UNGvTwT33yKXhOrMmvjXFMSAiBgO8Sv2jg6FWyMEZkTYa81FpqkEsOSzlSy9Elh+gquVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5b27972836ab4f5cf392e79662ff9975/vitest-pretty-format-2.1.0-beta.1.tgz", "_integrity": "sha512-XK4PNt1OLX1nw4KwRadr/0TcH4bSFt7vZV0exZKvHgZYhtawUYOzGerzRqrVoXty4l3f8aIRxhXkPFR0oht/5g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.1", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.1_1723011676816_0.3463381243340009", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.2": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.2", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "763717b94ce322e3b3eb98ba9ce00b04012711a2", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-QYBD5XgEscjqP094A32aPmVsFCxuWoasYGQr4Rh17zkSKmMmHCXNYoLtgtrfr4ePMfvmrRqMF4NqI7Wir6izEQ==", "signatures": [{"sig": "MEYCIQDmDJKUHKodBXpgOzaLlvsuvu5nzXuTtJxjG6nSWLrWBwIhAJY4MTdwUun4Blf7MBozm0ZWgtOmN/1aA1oajBYTd3si", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ccbad707ee4214deff7685989ac21b65/vitest-pretty-format-2.1.0-beta.2.tgz", "_integrity": "sha512-QYBD5XgEscjqP094A32aPmVsFCxuWoasYGQr4Rh17zkSKmMmHCXNYoLtgtrfr4ePMfvmrRqMF4NqI7Wir6izEQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.1", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.2_1723017401218_0.5867999488422038", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.3": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.3", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "79c53e8b70fc86e67b348fe308e1bddeb0f884bc", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-fiBhK0jFGZyginKX/LnHIMDdPtdWA1D8TIHz/7J1xLzLPQc4w0XiDtFBN1ylNwr2yA0aeo/WFU7kB/asbShwtw==", "signatures": [{"sig": "MEQCIDrbr46CoOf/e+6Y8c736cSZHJomWS1sygboE4eBOPYjAiBJd1HN+Pp4Lu+lxdEUBn1XeyK7dPYSzTGfBdSQefLJ1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/885f57c4354b12065410f9dc61ed878c/vitest-pretty-format-2.1.0-beta.3.tgz", "_integrity": "sha512-fiBhK0jFGZyginKX/LnHIMDdPtdWA1D8TIHz/7J1xLzLPQc4w0XiDtFBN1ylNwr2yA0aeo/WFU7kB/asbShwtw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.1", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.3_1723018613085_0.025214265437191852", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.4": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.4", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3c0464b5d3c2643e46906a8752d47635c5aa9e34", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.4.tgz", "fileCount": 4, "integrity": "sha512-K0r3RvePCnU21/7aYWZnfsGCWGNHakB17qHeGdNpu8sQH94RxFFnFVsKq1qG0Xc1OmjuF/72407rPWWAxwRJUQ==", "signatures": [{"sig": "MEQCIE+N4YlRPTBrGXl7h7dXCY3CLPnCy3+cFuZ11FB+d9CrAiAsMfZYg8PR6U4Y/RvfGE599pmCO24mbT0tvaJ+pJv36A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cb76c1f91a6c9b12646d466072b14d20/vitest-pretty-format-2.1.0-beta.4.tgz", "_integrity": "sha512-K0r3RvePCnU21/7aYWZnfsGCWGNHakB17qHeGdNpu8sQH94RxFFnFVsKq1qG0Xc1OmjuF/72407rPWWAxwRJUQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.1", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.4_1723030962413_0.2964072069969119", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.5": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.5", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d7d19db2db84b1a57febd735f03eafe311150534", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.5.tgz", "fileCount": 4, "integrity": "sha512-lwl3t1gUaMmzpdndqpfj01hDUhFtuewIuBm5Yei3r3EwJX07WGyFtY7yrgGqvzCIH+qiBS9gVPM9CyPT9TeZwA==", "signatures": [{"sig": "MEYCIQCg9+27dLMo+EXcMQCK/R9d6f7y9EaU53xzy4wjeEADsAIhAPBwUSIjOxdFRMux+P9eHJ/Elo8YqAm8KJPbBFTIiuFu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/86fe1a82671cd60fe82b586781da6743/vitest-pretty-format-2.1.0-beta.5.tgz", "_integrity": "sha512-lwl3t1gUaMmzpdndqpfj01hDUhFtuewIuBm5Yei3r3EwJX07WGyFtY7yrgGqvzCIH+qiBS9gVPM9CyPT9TeZwA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.1", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.5_1723462509985_0.7322850104946257", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.6": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.6", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2c432dc503907d1053fbc7a7fb069c71084832b5", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.6.tgz", "fileCount": 4, "integrity": "sha512-fDbjx4A6xNlqpZPssh6KFMqFZVTQUdY3+qbi6B98GKHc/EdVOrbfQfa2uk7Xfz2GPwhq0yc/d+8qUmRoWQNifg==", "signatures": [{"sig": "MEYCIQCluu8znSLpvoTldFeEhpvOfbU54RSMldQZmHfo4PIAUQIhAL5ahb2K+0L1XUAOj9k+0mCYMqg4Kk7Idniv8y2sYN+i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2889a905810accc10879c7a782fd8c6a/vitest-pretty-format-2.1.0-beta.6.tgz", "_integrity": "sha512-fDbjx4A6xNlqpZPssh6KFMqFZVTQUdY3+qbi6B98GKHc/EdVOrbfQfa2uk7Xfz2GPwhq0yc/d+8qUmRoWQNifg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.1", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.6_1724159900513_0.4522476465768286", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.7": {"name": "@vitest/pretty-format", "version": "2.1.0-beta.7", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "05eeec52b34891d479d2b681d40e1dad127b043d", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0-beta.7.tgz", "fileCount": 4, "integrity": "sha512-pZNDncPfgWspEXKgvRcFdJANMBrxuD+EQnS/vqySSHUbuBmv0EdmoZSokP8WKTLa16aSsaWP9gQlb9wxZBwgxQ==", "signatures": [{"sig": "MEUCIQCgOqV8Q660sbe4NG7eU3fZHXxgF4wQIp2BaxkMKnt5xgIgSmE9Q4282DJRRR3cng0un6V6OxkdFyYbTpbYxz1+1rQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/980df235e55e59b7de2ac3c9ff74958c/vitest-pretty-format-2.1.0-beta.7.tgz", "_integrity": "sha512-pZNDncPfgWspEXKgvRcFdJANMBrxuD+EQnS/vqySSHUbuBmv0EdmoZSokP8WKTLa16aSsaWP9gQlb9wxZBwgxQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0-beta.7_1725894783623_0.9407889187463891", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vitest/pretty-format", "version": "2.1.0", "license": "MIT", "_id": "@vitest/pretty-format@2.1.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "22365603822eac3d4b2a7ac465fb603efea53bd6", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.0.tgz", "fileCount": 4, "integrity": "sha512-7sxf2F3DNYatgmzXXcTh6cq+/fxwB47RIQqZJFoSH883wnVAoccSRT6g+dTKemUBo8Q5N4OYYj1EBXLuRKvp3Q==", "signatures": [{"sig": "MEYCIQDomijmnSCD5+VqT2HzpW7iuS7Z+NHIjXrEA5K3tiQf5QIhAOxMNY0wqi64jeNufe0mxPdsULojMmfnqkfjUIFc8US+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/efda03853a94f90d91c0b12967bf90a2/vitest-pretty-format-2.1.0.tgz", "_integrity": "sha512-7sxf2F3DNYatgmzXXcTh6cq+/fxwB47RIQqZJFoSH883wnVAoccSRT6g+dTKemUBo8Q5N4OYYj1EBXLuRKvp3Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.0_1726149791654_0.7274665806707179", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vitest/pretty-format", "version": "2.1.1", "license": "MIT", "_id": "@vitest/pretty-format@2.1.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fea25dd4e88c3c1329fbccd1d16b1d607eb40067", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.1.tgz", "fileCount": 4, "integrity": "sha512-SjxPFOtuINDUW8/UkElJYQSFtnWX7tMksSGW0vfjxMneFqxVr8YJ979QpMbDW7g+BIiq88RAGDjf7en6rvLPPQ==", "signatures": [{"sig": "MEUCIQCOpF45vsC/cw4DUK4H9nw9gTzGhTZHs4QFr8BtzUUcNwIgOGXSS8di4Wx2dOv+kb2Bu6WmPRE8OgmW6p9lXkWyUGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8428bf77e34a3d3cefc2e24c59373cbe/vitest-pretty-format-2.1.1.tgz", "_integrity": "sha512-SjxPFOtuINDUW8/UkElJYQSFtnWX7tMksSGW0vfjxMneFqxVr8YJ979QpMbDW7g+BIiq88RAGDjf7en6rvLPPQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.1_1726241546218_0.7597871436184322", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vitest/pretty-format", "version": "2.1.2", "license": "MIT", "_id": "@vitest/pretty-format@2.1.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "42882ea18c4cd40428e34f74bbac706a82465193", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.2.tgz", "fileCount": 4, "integrity": "sha512-FIoglbHrSUlOJPDGIrh2bjX1sNars5HbxlcsFKCtKzu4+5lpsRhOCVcuzp0fEhAGHkPZRIXVNzPcpSlkoZ3LuA==", "signatures": [{"sig": "MEYCIQCzG8M9OT+/EqNiXC2bXHtHBPiW9bWGnu1HYnhbVthTswIhAIcpad/Alz4n3GdjFU47+AqQJk29STNYRzXoNWNQ6II+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b89f82b40010233f02100e50d998c7a5/vitest-pretty-format-2.1.2.tgz", "_integrity": "sha512-FIoglbHrSUlOJPDGIrh2bjX1sNars5HbxlcsFKCtKzu4+5lpsRhOCVcuzp0fEhAGHkPZRIXVNzPcpSlkoZ3LuA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.2_1727885991844_0.5553084911295525", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vitest/pretty-format", "version": "2.1.3", "license": "MIT", "_id": "@vitest/pretty-format@2.1.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "48b9b03de75507d1d493df7beb48dc39a1946a3e", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.3.tgz", "fileCount": 4, "integrity": "sha512-XH1XdtoLZCpqV59KRbPrIhFCOO0hErxrQCMcvnQete3Vibb9UeIOX02uFPfVn3Z9ZXsq78etlfyhnkmIZSzIwQ==", "signatures": [{"sig": "MEUCIQDC93BfhrRi/TVVJc/StdwDzcFcPF4W9xymdcs9Qc+ZXgIgY3lx0nd6PtkWfJTM90w8nU3z8hx18dkQZrR/g9oOfSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b2995ed92ec89cb8997ab25ebbbcd772/vitest-pretty-format-2.1.3.tgz", "_integrity": "sha512-XH1XdtoLZCpqV59KRbPrIhFCOO0hErxrQCMcvnQete3Vibb9UeIOX02uFPfVn3Z9ZXsq78etlfyhnkmIZSzIwQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.3_1728903917205_0.5882968414168177", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vitest/pretty-format", "version": "2.1.4", "license": "MIT", "_id": "@vitest/pretty-format@2.1.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fc31993bdc1ef5a6c1a4aa6844e7ba55658a4f9f", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.4.tgz", "fileCount": 4, "integrity": "sha512-L95zIAkEuTDbUX1IsjRl+vyBSLh3PwLLgKpghl37aCK9Jvw0iP+wKwIFhfjdUtA2myLgjrG6VU6JCFLv8q/3Ww==", "signatures": [{"sig": "MEYCIQDIj5KbCWXN+9yWWkm+ZyBDftsQ+H8xjTGSTzVWwwQyQgIhANwOVcpOhqeC7a+DK+9bWqBfQLdyIcW5TmSAftamKe+3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3393689037f5a287cb994d8db117c24e/vitest-pretty-format-2.1.4.tgz", "_integrity": "sha512-L95zIAkEuTDbUX1IsjRl+vyBSLh3PwLLgKpghl37aCK9Jvw0iP+wKwIFhfjdUtA2myLgjrG6VU6JCFLv8q/3Ww==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.4_1730118425581_0.7682570661456836", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@vitest/pretty-format", "version": "2.1.5", "license": "MIT", "_id": "@vitest/pretty-format@2.1.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bc79b8826d4a63dc04f2a75d2944694039fa50aa", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.5.tgz", "fileCount": 4, "integrity": "sha512-4ZOwtk2bqG5Y6xRGHcveZVr+6txkH7M2e+nPFd6guSoN638v/1XQ0K06eOpi0ptVU/2tW/pIU4IoPotY/GZ9fw==", "signatures": [{"sig": "MEYCIQCdgyKz48pxyst9KGqJTWhBTvsi8jwZW1hWIgGvRSZnxQIhALUCmA/dJBklQ6hfdJz0lEzDBToQTsZotf3LcFAtEvhA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a12f94dc201ecc967f9d5fab6e51e512/vitest-pretty-format-2.1.5.tgz", "_integrity": "sha512-4ZOwtk2bqG5Y6xRGHcveZVr+6txkH7M2e+nPFd6guSoN638v/1XQ0K06eOpi0ptVU/2tW/pIU4IoPotY/GZ9fw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.5_1731511432689_0.9811948458481168", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@vitest/pretty-format", "version": "2.2.0-beta.1", "license": "MIT", "_id": "@vitest/pretty-format@2.2.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1c87718fc9ccb986273baae71b284b08c161c7e1", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.2.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-MC2vTZYCPzLhbYunX00NP4J5aM15uH860jIQ//MGZHefM/J12IpPjRKNEAG3p7QMk9TTOvZjxyG8kkh8WKWM+A==", "signatures": [{"sig": "MEYCIQD2P0Z6CIiHI67RInSsMqQ3e94KPy4IdOTHtREQHTrfYgIhAJqpsa7i/dEV5IRnoWz/oGUopsb3fXpa6Wi68ewepymO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45504}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b542c243711a6488ea98fc9b26c2439c/vitest-pretty-format-2.2.0-beta.1.tgz", "_integrity": "sha512-MC2vTZYCPzLhbYunX00NP4J5aM15uH860jIQ//MGZHefM/J12IpPjRKNEAG3p7QMk9TTOvZjxyG8kkh8WKWM+A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.2.0-beta.1_1731518225674_0.4953468265442489", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@vitest/pretty-format", "version": "2.2.0-beta.2", "license": "MIT", "_id": "@vitest/pretty-format@2.2.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "13eb92b6aa4d111206ae2f7d1fb09d6445bd9344", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.2.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-EWpdeQ1ooBK61VnWddSlFqr8YsOOEQQL6/fufRowY5xAYbnL5VlzKkUrGiotHg/WYPIOdw8EwmMeftgvvshUGg==", "signatures": [{"sig": "MEYCIQClgiYrCz4jLQs2ArthVQb0PSU7E0hcGXIrXzEZtzf8RwIhAKUPam6lo56K3ADzBKPhQkzQap0bWOON0E7rwbI0OO6h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58033}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dab07a3685ba960ab67c255760cb44e7/vitest-pretty-format-2.2.0-beta.2.tgz", "_integrity": "sha512-EWpdeQ1ooBK61VnWddSlFqr8YsOOEQQL6/fufRowY5xAYbnL5VlzKkUrGiotHg/WYPIOdw8EwmMeftgvvshUGg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "react-is-19": "npm:react-is@19.0.0-rc-b01722d5-20241114", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.2.0-beta.2_1731939475672_0.18291810601508884", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@vitest/pretty-format", "version": "2.1.6", "license": "MIT", "_id": "@vitest/pretty-format@2.1.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9bc642047a3efc637b41492b1f222c43be3822e4", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.6.tgz", "fileCount": 4, "integrity": "sha512-exZyLcEnHgDMKc54TtHca4McV4sKT+NKAe9ix/yhd/qkYb/TP8HTyXRFDijV19qKqTZM0hPL4753zU/U8L/gAA==", "signatures": [{"sig": "MEUCIQC9I2smOusKS0x555h/9SCO43EqsD7RGTxa2VIkFAI9HAIgQHu0N/W1vd/eVwPfOpkvmYTYDDxISbZS0c1mzYmPruk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f89f1a82da5a8ce2a83e1b12a0093a02/vitest-pretty-format-2.1.6.tgz", "_integrity": "sha512-exZyLcEnHgDMKc54TtHca4McV4sKT+NKAe9ix/yhd/qkYb/TP8HTyXRFDijV19qKqTZM0hPL4753zU/U8L/gAA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.6_1732623822373_0.12009594251127709", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "@vitest/pretty-format", "version": "2.1.7", "license": "MIT", "_id": "@vitest/pretty-format@2.1.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e8549976fbe5672942468f5d6aa4615666fd67de", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.7.tgz", "fileCount": 4, "integrity": "sha512-HoqRIyfQlXPrRDB43h0lC8eHPUDPwFweMaD6t+psOvwClCC+oZZim6wPMjuoMnRdiFxXqbybg/QbuewgTwK1vA==", "signatures": [{"sig": "MEUCIQDTm57bBzX0jmoAhoBlP1mw6uWmWnPP4009pzvShka2zwIgZodlhi2E4/U+6u2zjxF5HRePkisr+jRxJA2Y3p8ELJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b505d4472acf66a958015cc9b78c31cf/vitest-pretty-format-2.1.7.tgz", "_integrity": "sha512-HoqRIyfQlXPrRDB43h0lC8eHPUDPwFweMaD6t+psOvwClCC+oZZim6wPMjuoMnRdiFxXqbybg/QbuewgTwK1vA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.7_1733132931959_0.27015510575988766", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "@vitest/pretty-format", "version": "2.1.8", "license": "MIT", "_id": "@vitest/pretty-format@2.1.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "88f47726e5d0cf4ba873d50c135b02e4395e2bca", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.8.tgz", "fileCount": 4, "integrity": "sha512-9HiSZ9zpqNLKlbIDRWOnAWqgcA7xu+8YxXSekhr0Ykab7PAYFkhkwoqVArPOtJhPmYeE2YHgKZlj3CP36z2AJQ==", "signatures": [{"sig": "MEQCIF31kPBazyv/5Vbs9owdX6r6W67XhqVqcAVpgOLoCc8UAiBMemei600luedelrDLc8u1wdArUI7mn30Cx4X/2unQ0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bfe7bc3d40a8fb69d825fa4054d3b5ce/vitest-pretty-format-2.1.8.tgz", "_integrity": "sha512-9HiSZ9zpqNLKlbIDRWOnAWqgcA7xu+8YxXSekhr0Ykab7PAYFkhkwoqVArPOtJhPmYeE2YHgKZlj3CP36z2AJQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.8_1733150767415_0.7008916946453547", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "@vitest/pretty-format", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vitest/pretty-format@3.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7fcfab132d2040dce3d27cb2ee9160703be7b433", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-uDPi6zXNyUBgXDUD+E3E8MzUGxWXffDENbBbek/Jn9pDQb6R5TVAq/pbs+rZ18iNvXAelzhyRjsNX4cbTreENA==", "signatures": [{"sig": "MEUCIQDkiwEXgQ08Fp71oo0BI6yIrWMNObUQe1kBhyvMZNqsXAIgX0qMaEM8Wzcw1kXLNKsMyO2abfamUke1uC+DWVDcow0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58035}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fb45e3056ffc55086d3d628828ab1b96/vitest-pretty-format-3.0.0-beta.1.tgz", "_integrity": "sha512-uDPi6zXNyUBgXDUD+E3E8MzUGxWXffDENbBbek/Jn9pDQb6R5TVAq/pbs+rZ18iNvXAelzhyRjsNX4cbTreENA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "react-is-19": "npm:react-is@19.0.0-rc-fb9a90fa48-20240614", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.0-beta.1_1733420007311_0.5804475434708112", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "@vitest/pretty-format", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vitest/pretty-format@3.0.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7196c02a3e347d853e28f39d4b02ce7c2209b353", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-vMCmIdShOz2vjMCyxk+SoexZxsIbwrRc/weTctKxnQAYv3NubehpwCOaT8nhirmYQtdW+8r079wz1s7cKxNmCA==", "signatures": [{"sig": "MEQCIA4AOER1JeFh0nTxnvhLZShfSnTykBeAhLNm0abzIjXuAiADW/NFi/DBqnzS5FE2d3xYr3fCFaUkyWgSzoRkDYHdwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58832}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3f55e7d79487210375847be2b49ab7dd/vitest-pretty-format-3.0.0-beta.2.tgz", "_integrity": "sha512-vMCmIdShOz2vjMCyxk+SoexZxsIbwrRc/weTctKxnQAYv3NubehpwCOaT8nhirmYQtdW+8r079wz1s7cKxNmCA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.0-beta.2_1733826088540_0.4620755293170715", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.3": {"name": "@vitest/pretty-format", "version": "3.0.0-beta.3", "license": "MIT", "_id": "@vitest/pretty-format@3.0.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "115ae82356da1085726f4bec7d309d6d860097cd", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-d3+IaliPJLndUB/eQ4XP4OEHWhMDUJGhGc8XyLfi803Ad62nkTLC1bwFz80bNghEfKi+sz/oSkvQmW/3lQeCdA==", "signatures": [{"sig": "MEUCIQDJtcqAv1mnznwgw8b9HKzorV9+g+inSl/eybtmAiX0uAIgDT4EC5O8nBNfW53R+/ko4ijH6pptp4Lax6FPUCM+zeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58832}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8a68fb67d170fd0662d243886505b52a/vitest-pretty-format-3.0.0-beta.3.tgz", "_integrity": "sha512-d3+IaliPJLndUB/eQ4XP4OEHWhMDUJGhGc8XyLfi803Ad62nkTLC1bwFz80bNghEfKi+sz/oSkvQmW/3lQeCdA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.0-beta.3_1734712353422_0.7496279128248371", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.4": {"name": "@vitest/pretty-format", "version": "3.0.0-beta.4", "license": "MIT", "_id": "@vitest/pretty-format@3.0.0-beta.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "481677f0b3993d46ce1536ac5d55cec6db07180a", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.0-beta.4.tgz", "fileCount": 4, "integrity": "sha512-uwgWpsHabr96/YnrzNY+NQUgnza8rFq6wYW/yR1FrgRfQtVTS1loOPQSydRkUDk307bUzMVyyh7aVRwtLgHkDg==", "signatures": [{"sig": "MEUCIDz7R0wCMabu3RaU4ncO9NkeNKNZ3lTvRLWQHi6XzNgUAiEA2dyGfLvaNw0SuGKno2RClsPywnEsbnDbbsQj14g3T9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58832}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3ca59749dd73acdad1c60dc4829b7997/vitest-pretty-format-3.0.0-beta.4.tgz", "_integrity": "sha512-uwgWpsHabr96/YnrzNY+NQUgnza8rFq6wYW/yR1FrgRfQtVTS1loOPQSydRkUDk307bUzMVyyh7aVRwtLgHkDg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.0-beta.4_1736346223599_0.2949048716230209", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@vitest/pretty-format", "version": "3.0.0", "license": "MIT", "_id": "@vitest/pretty-format@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "aea7f74cc768b8c8efae1c3bd3b9f9ef98524ac2", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-24y+MS04ZHZbbbfAvfpi9hM2oULePbiL6Dir8r1nFMN97hxuL0gEXKWRGmlLPwzKDtaOKNjtyTx0+GiZcWCxDA==", "signatures": [{"sig": "MEQCIEIMAFRdySFZXW1iKtAjZiDY1+raIRCat488U5bwCAMsAiA/IJ+mF7m2IiHKMyyNpQDkdsA0IqgjtHIdaUJEa/YmHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58825}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1136d0bf5dea6a84d3797f54633907de/vitest-pretty-format-3.0.0.tgz", "_integrity": "sha512-24y+MS04ZHZbbbfAvfpi9hM2oULePbiL6Dir8r1nFMN97hxuL0gEXKWRGmlLPwzKDtaOKNjtyTx0+GiZcWCxDA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.0_1737036446561_0.4725431447113715", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@vitest/pretty-format", "version": "3.0.1", "license": "MIT", "_id": "@vitest/pretty-format@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "440e9d3fca7d3327ca918092c74f194ef889198b", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-FnyGQ9eFJ/Dnqg3jCvq9O6noXtxbZhOlSvNLZsCGJxhsGiZ5LDepmsTCizRfyGJt4Q6pJmZtx7rO/qqr9R9gDA==", "signatures": [{"sig": "MEQCIAUcHXXfK1PhF71AcXX6CNtO+CyjFH4g1IDo40iVYioGAiBajYbibVM3CCQlzVF4/p8s5L83B2VFnq2tDIlfODdSJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b944157a18c33a3f082bfdcce23c903f/vitest-pretty-format-3.0.1.tgz", "_integrity": "sha512-FnyGQ9eFJ/Dnqg3jCvq9O6noXtxbZhOlSvNLZsCGJxhsGiZ5LDepmsTCizRfyGJt4Q6pJmZtx7rO/qqr9R9gDA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.1_1737055955922_0.8484429658347397", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@vitest/pretty-format", "version": "3.0.2", "license": "MIT", "_id": "@vitest/pretty-format@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d1d0ac72a7b9ed6e8421aa54686b925b72a91fab", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.2.tgz", "fileCount": 4, "integrity": "sha512-yBohcBw/T/p0/JRgYD+IYcjCmuHzjC3WLAKsVE4/LwiubzZkE8N49/xIQ/KGQwDRA8PaviF8IRO8JMWMngdVVQ==", "signatures": [{"sig": "MEQCIEYf4nRIRXMMjHuuDRMNCByz0TgiW1b4sKFkjq8qplCVAiAQEleZUsHWi+v/pnCI3ARUamxE1yHOlw6f7ExTykXvfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/11227fcfd89f7bfc424b48357fc1356f/vitest-pretty-format-3.0.2.tgz", "_integrity": "sha512-yBohcBw/T/p0/JRgYD+IYcjCmuHzjC3WLAKsVE4/LwiubzZkE8N49/xIQ/KGQwDRA8PaviF8IRO8JMWMngdVVQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.2_1737123965888_0.8755151185593233", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "@vitest/pretty-format", "version": "3.0.3", "license": "MIT", "_id": "@vitest/pretty-format@3.0.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4bd59463d1c944c22287c3da2060785269098183", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.3.tgz", "fileCount": 4, "integrity": "sha512-gCrM9F7STYdsDoNjGgYXKPq4SkSxwwIU5nkaQvdUxiQ0EcNlez+PdKOVIsUJvh9P9IeIFmjn4IIREWblOBpP2Q==", "signatures": [{"sig": "MEQCIE8D/kPkiFrxyz/1Epemi3VBpDbfVtlwxupMF9fC4rLCAiAMw64X7MF7P2QL/R+SEAyKemDmYcaonz5UfLgeicCYvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dc3e8a8ee80e545823a70e7debff0d07/vitest-pretty-format-3.0.3.tgz", "_integrity": "sha512-gCrM9F7STYdsDoNjGgYXKPq4SkSxwwIU5nkaQvdUxiQ0EcNlez+PdKOVIsUJvh9P9IeIFmjn4IIREWblOBpP2Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.3_1737467916736_0.5561841082100238", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@vitest/pretty-format", "version": "3.0.4", "license": "MIT", "_id": "@vitest/pretty-format@3.0.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "18d5da3bad9a0eebf49f5c5daa84d0d5f7d2bbfa", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.4.tgz", "fileCount": 4, "integrity": "sha512-ts0fba+dEhK2aC9PFuZ9LTpULHpY/nd6jhAQ5IMU7Gaj7crPCTdCFfgvXxruRBLFS+MLraicCuFXxISEq8C93g==", "signatures": [{"sig": "MEYCIQCBWxRacgm3zapQfovAjceVqycKhbCaSisWIs77LuX5MAIhANBuVoYxCxmiUNljjMx8GNE7kU78D7Vkps43hOeoFuS0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/17f2354226ab3227bbddec3921c9194d/vitest-pretty-format-3.0.4.tgz", "_integrity": "sha512-ts0fba+dEhK2aC9PFuZ9LTpULHpY/nd6jhAQ5IMU7Gaj7crPCTdCFfgvXxruRBLFS+MLraicCuFXxISEq8C93g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.4_1737639694988_0.31654957863845556", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.9": {"name": "@vitest/pretty-format", "version": "2.1.9", "license": "MIT", "_id": "@vitest/pretty-format@2.1.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "434ff2f7611689f9ce70cd7d567eceb883653fdf", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-2.1.9.tgz", "fileCount": 4, "integrity": "sha512-KhRIdGV2U9HOUzxfiHmY8IFHTdqtOhIzCpd8WRdJiE7D/HUcZVD0EgQCVjm+Q9gkUXWgBvMmTtZgIG48wq7sOQ==", "signatures": [{"sig": "MEQCICzYjNKQ+N5ZBCkyb9Re2VesnICQY1635mNOIpNthF5YAiBT9bIxBa3ep+FnWC5ouxUXLH3cq1sPOsiuLK0vfsUW2w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45497}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-2.1.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e30b4544d15ea1f5d21d9e2fdfd90345/vitest-pretty-format-2.1.9.tgz", "_integrity": "sha512-KhRIdGV2U9HOUzxfiHmY8IFHTdqtOhIzCpd8WRdJiE7D/HUcZVD0EgQCVjm+Q9gkUXWgBvMmTtZgIG48wq7sOQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyrainbow": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^18.3.1", "@types/react-is": "^18.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_2.1.9_1738590244312_0.5803131504875132", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@vitest/pretty-format", "version": "3.0.5", "license": "MIT", "_id": "@vitest/pretty-format@3.0.5", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "10ae6a83ccc1a866e31b2d0c1a7a977ade02eff9", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.5.tgz", "fileCount": 4, "integrity": "sha512-CjUtdmpOcm4RVtB+up8r2vVDLR16Mgm/bYdkGFe3Yj/scRfCpbSi2W/BDSDcFK7ohw8UXvjMbOp9H4fByd/cOA==", "signatures": [{"sig": "MEQCIFtuvxAajSh1gKabpk8ehM5nUxi9FWJ7TXDrfs04LPjhAiBSp13b5xSW70x2ssjyVmsNOki+Hq4gAB2B4mzewO+68A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5a58a26c973096579552f57e789923d3/vitest-pretty-format-3.0.5.tgz", "_integrity": "sha512-CjUtdmpOcm4RVtB+up8r2vVDLR16Mgm/bYdkGFe3Yj/scRfCpbSi2W/BDSDcFK7ohw8UXvjMbOp9H4fByd/cOA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.5_1738591311283_0.955286405045686", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@vitest/pretty-format", "version": "3.0.6", "license": "MIT", "_id": "@vitest/pretty-format@3.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a828569818b666a6e955c9af8129e6b0d2968ee6", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.6.tgz", "fileCount": 4, "integrity": "sha512-Zyctv3dbNL+67qtHfRnUE/k8qxduOamRfAL1BurEIQSyOEFffoMvx2pnDSSbKAAVxY0Ej2J/GH2dQKI0W2JyVg==", "signatures": [{"sig": "MEYCIQCzWOAfNLfhxrzEqTXRjs4UTrSeuEK0aMeACRpmtJaAkQIhALk+eLLlt4R6lLgyAlD75gZA8aIVFyua1H4jrtDZwHho", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58848}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/28d269c8ce2affd6b87d04a5d799cfef/vitest-pretty-format-3.0.6.tgz", "_integrity": "sha512-Zyctv3dbNL+67qtHfRnUE/k8qxduOamRfAL1BurEIQSyOEFffoMvx2pnDSSbKAAVxY0Ej2J/GH2dQKI0W2JyVg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.6_1739885915048_0.5807252971214094", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@vitest/pretty-format", "version": "3.0.7", "license": "MIT", "_id": "@vitest/pretty-format@3.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1780516ebb4e40dd89e60b9fc7ffcbd9cba0fc22", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.7.tgz", "fileCount": 4, "integrity": "sha512-CiRY0BViD/V8uwuEzz9Yapyao+M9M008/9oMOSQydwbwb+CMokEq3XVaF3XK/VWaOK0Jm9z7ENhybg70Gtxsmg==", "signatures": [{"sig": "MEYCIQC/GKTeS/86a8IeJRYOfluLP3UiTPHW//Uti51Gryq+YQIhALWqEvsF6M4IqvACZzynTwj6eX+cWQkM9IOGYuznSa+1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58848}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3021ef4a86c9a77a7779ed6a7182d14f/vitest-pretty-format-3.0.7.tgz", "_integrity": "sha512-CiRY0BViD/V8uwuEzz9Yapyao+M9M008/9oMOSQydwbwb+CMokEq3XVaF3XK/VWaOK0Jm9z7ENhybg70Gtxsmg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.7_1740419440851_0.43560811915676356", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@vitest/pretty-format", "version": "3.0.8", "license": "MIT", "_id": "@vitest/pretty-format@3.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "89f6111d141142689871f5a4e62ad679bb6b6357", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.8.tgz", "fileCount": 4, "integrity": "sha512-BNqwbEyitFhzYMYHUVbIvepOyeQOSFA/NeJMIP9enMntkkxLgOcgABH6fjyXG85ipTgvero6noreavGIqfJcIg==", "signatures": [{"sig": "MEUCIQCkQKTg4plx7Mhw6vFNHJnC9v+ZPKJ56h6ektC5PL3tlgIgGzsvMni+2hybN5l7JDetzzv0EGEleEQvx6A4BYD8y/M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58848}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/379108dca6d669db8f0aab25a9c40c4a/vitest-pretty-format-3.0.8.tgz", "_integrity": "sha512-BNqwbEyitFhzYMYHUVbIvepOyeQOSFA/NeJMIP9enMntkkxLgOcgABH6fjyXG85ipTgvero6noreavGIqfJcIg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.8_1741274168464_0.009137309694409623", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@vitest/pretty-format", "version": "3.0.9", "license": "MIT", "_id": "@vitest/pretty-format@3.0.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d9c88fe64b4edcdbc88e5bd92c39f9cc8d40930d", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.0.9.tgz", "fileCount": 4, "integrity": "sha512-OW9F8t2J3AwFEwENg3yMyKWweF7oRJlMyHOMIhO5F3n0+cgQAJZBjNgrF8dLwFTEXl5jUqBLXd9QyyKv8zEcmA==", "signatures": [{"sig": "MEUCIHR0P2FJxnZ9QWHn08hfZciC9pOR8um9I7IHuKKUSb6oAiEAweRHAqJ2jEOspGWZtwWGl8PEB64rowfH5elO/ceGXQc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58581}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.0.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1ca8d8429a690f055d271213b0503ada/vitest-pretty-format-3.0.9.tgz", "_integrity": "sha512-OW9F8t2J3AwFEwENg3yMyKWweF7oRJlMyHOMIhO5F3n0+cgQAJZBjNgrF8dLwFTEXl5jUqBLXd9QyyKv8zEcmA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.0.9_1742212746872_0.0948122561843987", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.1": {"name": "@vitest/pretty-format", "version": "3.1.0-beta.1", "license": "MIT", "_id": "@vitest/pretty-format@3.1.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2624279f70df72eb16cfe1e6f1f7f36267005a81", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-ElN4L6VwcR1fPY2n4gH4kLNaawEIITJMzvHCWdN4oC90/xiFpX3p0WIhuHO0XrIwDHsUrC8kBw5pNoPz/axz2w==", "signatures": [{"sig": "MEQCIGRx9oZeIqQ3c2oS30DRKbc9EGDebqwWZ98Hy6+RJhjoAiANQXFPyzQnrgvTJAlKFXI3ZZKejJW582/qYLY108CnFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58588}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/870365c6d834f8852e2b6065b465ea57/vitest-pretty-format-3.1.0-beta.1.tgz", "_integrity": "sha512-ElN4L6VwcR1fPY2n4gH4kLNaawEIITJMzvHCWdN4oC90/xiFpX3p0WIhuHO0XrIwDHsUrC8kBw5pNoPz/axz2w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.0-beta.1_1742213841614_0.20463122324065952", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.2": {"name": "@vitest/pretty-format", "version": "3.1.0-beta.2", "license": "MIT", "_id": "@vitest/pretty-format@3.1.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f833afd36388824ba1fa248447a97d4e07dd73bc", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-Y8R+GXZNXYkuJjRu3lGn3xr8u6vsi+N2+dNxJ54P/wWBogbpCqbLbQbmUCOK218QV6nz+hWfjqQCvVfNo/XNWA==", "signatures": [{"sig": "MEUCIAP26aTbYcnfHT3Z97On+YcoUjlCoP0xhUY3HiajuFp7AiEA4UkSzfMc97+zCkfiogkfFg5ycEQ/hgXBjry84/D8naU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58041}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/19b65d04f70758394bf136ecad1d4f84/vitest-pretty-format-3.1.0-beta.2.tgz", "_integrity": "sha512-Y8R+GXZNXYkuJjRu3lGn3xr8u6vsi+N2+dNxJ54P/wWBogbpCqbLbQbmUCOK218QV6nz+hWfjqQCvVfNo/XNWA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.0-beta.2_1742545686799_0.5021365220650016", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "@vitest/pretty-format", "version": "3.1.0", "license": "MIT", "_id": "@vitest/pretty-format@3.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3223b2f7c6bffff04d6e3559a70363d56d63c3e7", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.0.tgz", "fileCount": 4, "integrity": "sha512-TB3ahEy3D+yA2yE0yJST6YkJItOqG8JAZoUNlrNz+uvH2Rvs3GE8NOvTU1nXffXJhNR+t5BHKJqWDtcuPdJYjQ==", "signatures": [{"sig": "MEQCIAklnYax9JcLbUDypa09o66xQLAvOhNJZT86URpi4qgzAiA1hVGFbHNiXvw5Z8QK/cHCQs1tNZ0U1G72ccJCnuGn8w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 57985}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/037fa877db2278ffe0849951c41f6e3d/vitest-pretty-format-3.1.0.tgz", "_integrity": "sha512-TB3ahEy3D+yA2yE0yJST6YkJItOqG8JAZoUNlrNz+uvH2Rvs3GE8NOvTU1nXffXJhNR+t5BHKJqWDtcuPdJYjQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.0_1743414336516_0.9751084614366716", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@vitest/pretty-format", "version": "3.1.1", "license": "MIT", "_id": "@vitest/pretty-format@3.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5b4d577771daccfced47baf3bf026ad59b52c283", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.1.tgz", "fileCount": 4, "integrity": "sha512-dg0CIzNx+hMMYfNmSqJlLSXEmnNhMswcn3sXO7Tpldr0LiGmg3eXdLLhwkv2ZqgHb/d5xg5F7ezNFRA1fA13yA==", "signatures": [{"sig": "MEUCIQCqW7RM5Y6Zxw7xqTxV4j8k0FaD7aUS3Dp3aLyoNXTU5gIgFUO60YyO1khECK0u31YIK1+1T6yPRRtvtaSRyb1aLN0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 57985}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/24fc12a80ef49599568bdf59a0b80167/vitest-pretty-format-3.1.1.tgz", "_integrity": "sha512-dg0CIzNx+hMMYfNmSqJlLSXEmnNhMswcn3sXO7Tpldr0LiGmg3eXdLLhwkv2ZqgHb/d5xg5F7ezNFRA1fA13yA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.0.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.1_1743416331224_0.08864166035204368", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@vitest/pretty-format", "version": "3.1.2", "license": "MIT", "_id": "@vitest/pretty-format@3.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "689b0604c0b73fdccb144f11b64d70c9233b23b8", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.2.tgz", "fileCount": 4, "integrity": "sha512-R0xAiHuWeDjTSB3kQ3OQpT8Rx3yhdOAIm/JM4axXxnG7Q/fS8XUwggv/A4xzbQA+drYRjzkMnpYnOGAc4oeq8w==", "signatures": [{"sig": "MEYCIQDjea8nUAXRVhm2rLxFxifITnaTSu4ro07uA8QSx0/YYQIhAMPLFP5XH76xwMZBwHgUXkYprhTKS3zp9rvox47eBED0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58021}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7a8f096fe2791e9df8ce7b70b2e7877e/vitest-pretty-format-3.1.2.tgz", "_integrity": "sha512-R0xAiHuWeDjTSB3kQ3OQpT8Rx3yhdOAIm/JM4axXxnG7Q/fS8XUwggv/A4xzbQA+drYRjzkMnpYnOGAc4oeq8w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.2_1745225905195_0.852661318485443", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@vitest/pretty-format", "version": "3.1.3", "license": "MIT", "_id": "@vitest/pretty-format@3.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "760b9eab5f253d7d2e7dcd28ef34570f584023d4", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.3.tgz", "fileCount": 4, "integrity": "sha512-i6FDiBeJUGLDKADw2Gb01UtUNb12yyXAqC/mmRWuYl+m/U9GS7s8us5ONmGkGpUUo7/iAYzI2ePVfOZTYvUifA==", "signatures": [{"sig": "MEUCIGxYxWlcDAJKb5GmDe6bzDg2V2zgNInffQe+HE6X/EEkAiEAh1IF9k8wJEv/X7WvnSTFSLApKWpY0UCEbnSCAFjxLC8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58021}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ca2826b284205716009a130eb392cb19/vitest-pretty-format-3.1.3.tgz", "_integrity": "sha512-i6FDiBeJUGLDKADw2Gb01UtUNb12yyXAqC/mmRWuYl+m/U9GS7s8us5ONmGkGpUUo7/iAYzI2ePVfOZTYvUifA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.3_1746452693241_0.46193916243471045", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.1": {"name": "@vitest/pretty-format", "version": "3.2.0-beta.1", "license": "MIT", "_id": "@vitest/pretty-format@3.2.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c0a21312dd642509d15617258c4466d3a79163a6", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-agBt8xGFeVxxNe53xR6/V6DzEa5wcF4PrqcbM3mRZrLIEbpqyoxiW6ctPjPv0kqsiXxKMp5OZX0RziIasdjCYQ==", "signatures": [{"sig": "MEQCIGgzKQWo6Hht2fjU/DdEa48ikTajCEOR3JwXuBoCYYDoAiBwkxSjR2JhATtwmkiDLpizw2WX8QCLU+49oWh0KsOMZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58028}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b7ac85602b04141b2029344123881f96/vitest-pretty-format-3.2.0-beta.1.tgz", "_integrity": "sha512-agBt8xGFeVxxNe53xR6/V6DzEa5wcF4PrqcbM3mRZrLIEbpqyoxiW6ctPjPv0kqsiXxKMp5OZX0RziIasdjCYQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.0-beta.1_1746463867216_0.23523596790939427", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.2": {"name": "@vitest/pretty-format", "version": "3.2.0-beta.2", "license": "MIT", "_id": "@vitest/pretty-format@3.2.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6d8fe2eab759b63ed2ec290fdf8221731c40a410", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-v/UTkqlCaLv8pq7Pcv3Fi6tPDs60Ft8aA/5gO3PbsyrlwUK45TiahYVlO7IWVOkMMm/4Y74LnCCbzEiy+b8ZKA==", "signatures": [{"sig": "MEUCIQDo+7zcX5dI9jEKZU1W7PHqKe5eU/LftGdYMJbV8Eqa7AIgdIQ5G/nwMSIJ4R+TYcF/OrjQnRI2M4/bqnrvGAtALDs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58028}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/03e4d5730f6ddad470f9506988bdbd63/vitest-pretty-format-3.2.0-beta.2.tgz", "_integrity": "sha512-v/UTkqlCaLv8pq7Pcv3Fi6tPDs60Ft8aA/5gO3PbsyrlwUK45TiahYVlO7IWVOkMMm/4Y74LnCCbzEiy+b8ZKA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.0-beta.2_1747658315302_0.12541604150874708", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@vitest/pretty-format", "version": "3.1.4", "license": "MIT", "_id": "@vitest/pretty-format@3.1.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "da3e98c250cde3ce39fe8e709339814607b185e8", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.4.tgz", "fileCount": 4, "integrity": "sha512-cqv9H9GvAEoTaoq+cYqUTCGscUjKqlJZC7PRwY5FMySVj5J+xOm1KQcCiYHJOEzOKRUhLH4R2pTwvFlWCEScsg==", "signatures": [{"sig": "MEUCIQDXO0FPL5sJrCeQJRHIhUxQKLkqYZlr5M+wAC2eCJBZdgIgAUEOxAm5eYkf9ReKIXipcXPtg2m17L3ziD7eZpjldgw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58021}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/acc4073d8fb6e2a255d901680f40ab2e/vitest-pretty-format-3.1.4.tgz", "_integrity": "sha512-cqv9H9GvAEoTaoq+cYqUTCGscUjKqlJZC7PRwY5FMySVj5J+xOm1KQcCiYHJOEzOKRUhLH4R2pTwvFlWCEScsg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.1.4_1747671825880_0.8556780563208846", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.3": {"name": "@vitest/pretty-format", "version": "3.2.0-beta.3", "license": "MIT", "_id": "@vitest/pretty-format@3.2.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "75d3a974eb354cc4fff40cb1c4b0a7f329f10fdb", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-Q6S0xqxK9I6rHlDoWEKWWjzElguQdSdCXeD/0YcI6vZgPsDwYkhKWvh1yOYciEYfDt5x/tzZXVi1FVuoWlIbEg==", "signatures": [{"sig": "MEYCIQDNcJt2KHFnt/dSAnVgGOVw3LkN68KETZHBvv4PB8RGcAIhAM9KUyhHHklIKTK4ajPiMCkI3auPCCyLw2p900ZiW8Fs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59346}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5ef5eb423e038a018f3d2df9e22c1eb3/vitest-pretty-format-3.2.0-beta.3.tgz", "_integrity": "sha512-Q6S0xqxK9I6rHlDoWEKWWjzElguQdSdCXeD/0YcI6vZgPsDwYkhKWvh1yOYciEYfDt5x/tzZXVi1FVuoWlIbEg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.0-beta.3_1748442505495_0.890504457931087", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@vitest/pretty-format", "version": "3.2.0", "license": "MIT", "_id": "@vitest/pretty-format@3.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "81728ccebae16f1421600d305d526bdce08783ad", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.0.tgz", "fileCount": 4, "integrity": "sha512-gUUhaUmPBHFkrqnOokmfMGRBMHhgpICud9nrz/xpNV3/4OXCn35oG+Pl8rYYsKaTNd/FAIrqRHnwpDpmYxCYZw==", "signatures": [{"sig": "MEUCIH7zYuCEQdy5EeRacvdBhu2VIrKT2hHQzucg4Hjl+5oLAiEAv4ZLhbSKs9omU27wWmxCXgiylxqZkR+g3yBT4XFrsSQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59339}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/88ca02d2c9f8c3cd95b37fe9b3379aa7/vitest-pretty-format-3.2.0.tgz", "_integrity": "sha512-gUUhaUmPBHFkrqnOokmfMGRBMHhgpICud9nrz/xpNV3/4OXCn35oG+Pl8rYYsKaTNd/FAIrqRHnwpDpmYxCYZw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.0_1748862636593_0.959015506779209", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.1": {"name": "@vitest/pretty-format", "version": "3.2.1", "license": "MIT", "_id": "@vitest/pretty-format@3.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b43aeb52bc26715c394805127ad87b6ecc3c4336", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.1.tgz", "fileCount": 4, "integrity": "sha512-xBh1X2GPlOGBupp6E1RcUQWIxw0w/hRLd3XyBS6H+dMdKTAqHDNsIR2AnJwPA3yYe9DFy3VUKTe3VRTrAiQ01g==", "signatures": [{"sig": "MEUCIQDAxKUrpNvnyE4YmFRCKg392Ax++9t+hkkmQ74GqIlxaAIgdlA6h6TPO6YnMSGyUrDsb11kJk/jtxd4+U1IGoUNA70=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59339}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/413f4d77a9775a0cfa1aea68bf2bced0/vitest-pretty-format-3.2.1.tgz", "_integrity": "sha512-xBh1X2GPlOGBupp6E1RcUQWIxw0w/hRLd3XyBS6H+dMdKTAqHDNsIR2AnJwPA3yYe9DFy3VUKTe3VRTrAiQ01g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.1_1748970432367_0.6115140579750549", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.2": {"name": "@vitest/pretty-format", "version": "3.2.2", "license": "MIT", "_id": "@vitest/pretty-format@3.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f89611e5cf7988709a80c679a6e12bff12c81d18", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.2.tgz", "fileCount": 4, "integrity": "sha512-FY4o4U1UDhO9KMd2Wee5vumwcaHw7Vg4V7yR4Oq6uK34nhEJOmdRYrk3ClburPRUA09lXD/oXWZ8y/Sdma0aUQ==", "signatures": [{"sig": "MEYCIQDOGOuQ7MwEYs9wubfsoCqDbwgq3OA7OgVY6eHu3HRwZgIhAPfOqyeJuM+juXf329BzVI5So/zd+U+lyR4fhdUwuR6W", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59339}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3f34bdfa2027eff7651b770912367164/vitest-pretty-format-3.2.2.tgz", "_integrity": "sha512-FY4o4U1UDhO9KMd2Wee5vumwcaHw7Vg4V7yR4Oq6uK34nhEJOmdRYrk3ClburPRUA09lXD/oXWZ8y/Sdma0aUQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.2_1749130926834_0.6487153071260892", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.3": {"name": "@vitest/pretty-format", "version": "3.2.3", "license": "MIT", "_id": "@vitest/pretty-format@3.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ddd30f689fdd8191dbfd0cce8ae769e5de6b7f23", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.3.tgz", "fileCount": 4, "integrity": "sha512-yFglXGkr9hW/yEXngO+IKMhP0jxyFw2/qys/CK4fFUZnSltD+MU7dVYGrH8rvPcK/O6feXQA+EU33gjaBBbAng==", "signatures": [{"sig": "MEQCIDR2Cc2xNAVwUuXprAfrKkVeiY44dnr9IvqK+RBLem+XAiAyNDXvsuOb3Ik41//hDfGdvWbf13OYDZld9crS7PcnOg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59339}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/117b9c3e597b2a1d5e9153340019744f/vitest-pretty-format-3.2.3.tgz", "_integrity": "sha512-yFglXGkr9hW/yEXngO+IKMhP0jxyFw2/qys/CK4fFUZnSltD+MU7dVYGrH8rvPcK/O6feXQA+EU33gjaBBbAng==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.3_1749468731140_0.5533605484852633", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.4": {"name": "@vitest/pretty-format", "version": "3.2.4", "license": "MIT", "_id": "@vitest/pretty-format@3.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3c102f79e82b204a26c7a5921bf47d534919d3b4", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.4.tgz", "fileCount": 4, "integrity": "sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==", "signatures": [{"sig": "MEYCIQDZiw/MDLWcvtUnsggVSOjsFTthRa3KNdkXuqfOJHZkHgIhAJUjiEK1B9SGUXU6N9lArhcC3MaHKhaz6Kz/0cg2PPF3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@3.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59339}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-3.2.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/f232e40cd4962115977ee9813091b23c/vitest-pretty-format-3.2.4.tgz", "_integrity": "sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_3.2.4_1750182832355_0.8847666361257915", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.1": {"name": "@vitest/pretty-format", "version": "4.0.0-beta.1", "license": "MIT", "_id": "@vitest/pretty-format@4.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "78d28a79c52b62bbcca7d65ab7032fc306b3bcf4", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-4.0.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-GZHtBaRjGHVfSxzur9qDngaF7lYzh41+xlabzCsbQuJD7Evmgk+lKy+zZznB9lhAKfhhIG10uUawQfmj/qMYgQ==", "signatures": [{"sig": "MEUCICjStHI5LQlkql8s/Aq9wGzdNxDkV8LSQDUKn/hpUWgXAiEA2CLWk+MJ/MFDRAmF/kG+IKg8N1FQMGFTlTqCdz8Hy70=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59346}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-pretty-format-4.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/e5754a02f6dc7e8605ee7975e329f6a9/vitest-pretty-format-4.0.0-beta.1.tgz", "_integrity": "sha512-GZHtBaRjGHVfSxzur9qDngaF7lYzh41+xlabzCsbQuJD7Evmgk+lKy+zZznB9lhAKfhhIG10uUawQfmj/qMYgQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "_npmVersion": "10.8.2", "description": "Fork of pretty-format with support for ESM", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"tinyrainbow": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1", "@types/react-is": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pretty-format_4.0.0-beta.1_1750433306011_0.2769969378332571", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.2": {"name": "@vitest/pretty-format", "type": "module", "version": "4.0.0-beta.2", "description": "Fork of pretty-format with support for ESM", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/pretty-format"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"tinyrainbow": "^2.0.0"}, "devDependencies": {"@types/react-is": "^19.0.0", "react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}, "_id": "@vitest/pretty-format@4.0.0-beta.2", "_integrity": "sha512-7UxHosFLnzcKNCZFPKJCeWekV2skUb3z/gvZ2Uf57KRcFfF6Gp90HvkCl+sfDGgELf9RhjRC412al6gQPOwA6g==", "_resolved": "/tmp/afcd0799ae295b40b1bbcf902047e6a6/vitest-pretty-format-4.0.0-beta.2.tgz", "_from": "file:vitest-pretty-format-4.0.0-beta.2.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-7UxHosFLnzcKNCZFPKJCeWekV2skUb3z/gvZ2Uf57KRcFfF6Gp90HvkCl+sfDGgELf9RhjRC412al6gQPOwA6g==", "shasum": "066dac1f4c260f400320963ca61cb7ffe33129fa", "tarball": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-4.0.0-beta.2.tgz", "fileCount": 4, "unpackedSize": 59346, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fpretty-format@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDsJw9Z9wFMYetHn2+njURttTV4G1ah6c/zavTWUHPkbAiEAkF+6xn6GY7iJVtrG7G4EmclQF6n3iBkEjFMWoW+UFD0="}]}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>", "actor": {"name": "vitestbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/pretty-format_4.0.0-beta.2_1750775084747_0.6819804869975186"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-07-10T15:46:24.434Z", "modified": "2025-06-24T14:24:45.451Z", "2.0.2": "2024-07-10T15:46:24.710Z", "2.0.3": "2024-07-15T10:03:22.571Z", "2.0.4": "2024-07-22T09:13:17.332Z", "2.0.5": "2024-07-31T10:39:48.720Z", "2.1.0-beta.1": "2024-08-07T06:21:17.117Z", "2.1.0-beta.2": "2024-08-07T07:56:41.338Z", "2.1.0-beta.3": "2024-08-07T08:16:53.261Z", "2.1.0-beta.4": "2024-08-07T11:42:42.568Z", "2.1.0-beta.5": "2024-08-12T11:35:10.133Z", "2.1.0-beta.6": "2024-08-20T13:18:20.671Z", "2.1.0-beta.7": "2024-09-09T15:13:03.792Z", "2.1.0": "2024-09-12T14:03:11.899Z", "2.1.1": "2024-09-13T15:32:26.410Z", "2.1.2": "2024-10-02T16:19:52.065Z", "2.1.3": "2024-10-14T11:05:17.414Z", "2.1.4": "2024-10-28T12:27:05.800Z", "2.1.5": "2024-11-13T15:23:52.904Z", "2.2.0-beta.1": "2024-11-13T17:17:05.866Z", "2.2.0-beta.2": "2024-11-18T14:17:55.854Z", "2.1.6": "2024-11-26T12:23:42.525Z", "2.1.7": "2024-12-02T09:48:52.132Z", "2.1.8": "2024-12-02T14:46:07.579Z", "3.0.0-beta.1": "2024-12-05T17:33:27.486Z", "3.0.0-beta.2": "2024-12-10T10:21:28.828Z", "3.0.0-beta.3": "2024-12-20T16:32:33.606Z", "3.0.0-beta.4": "2025-01-08T14:23:43.769Z", "3.0.0": "2025-01-16T14:07:26.773Z", "3.0.1": "2025-01-16T19:32:36.149Z", "3.0.2": "2025-01-17T14:26:06.104Z", "3.0.3": "2025-01-21T13:58:36.920Z", "3.0.4": "2025-01-23T13:41:35.214Z", "2.1.9": "2025-02-03T13:44:04.531Z", "3.0.5": "2025-02-03T14:01:51.462Z", "3.0.6": "2025-02-18T13:38:35.258Z", "3.0.7": "2025-02-24T17:50:41.013Z", "3.0.8": "2025-03-06T15:16:08.759Z", "3.0.9": "2025-03-17T11:59:07.128Z", "3.1.0-beta.1": "2025-03-17T12:17:21.789Z", "3.1.0-beta.2": "2025-03-21T08:28:07.049Z", "3.1.0": "2025-03-31T09:45:36.690Z", "3.1.1": "2025-03-31T10:18:51.461Z", "3.1.2": "2025-04-21T08:58:25.396Z", "3.1.3": "2025-05-05T13:44:53.429Z", "3.2.0-beta.1": "2025-05-05T16:51:07.421Z", "3.2.0-beta.2": "2025-05-19T12:38:35.483Z", "3.1.4": "2025-05-19T16:23:46.117Z", "3.2.0-beta.3": "2025-05-28T14:28:25.664Z", "3.2.0": "2025-06-02T11:10:36.792Z", "3.2.1": "2025-06-03T17:07:12.616Z", "3.2.2": "2025-06-05T13:42:07.036Z", "3.2.3": "2025-06-09T11:32:11.322Z", "3.2.4": "2025-06-17T17:53:52.531Z", "4.0.0-beta.1": "2025-06-20T15:28:26.185Z", "4.0.0-beta.2": "2025-06-24T14:24:44.993Z"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "license": "MIT", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/pretty-format"}, "description": "Fork of pretty-format with support for ESM", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}