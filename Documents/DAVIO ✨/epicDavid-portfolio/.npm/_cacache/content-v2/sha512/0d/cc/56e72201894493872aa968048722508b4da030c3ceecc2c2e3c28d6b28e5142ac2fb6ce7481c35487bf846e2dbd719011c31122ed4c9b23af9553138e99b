{"_id": "tiny<PERSON>bow", "_rev": "5-e002cc6ddeb81d25790c89c0b63f4cc2", "name": "tiny<PERSON>bow", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "tiny<PERSON>bow", "version": "1.0.0", "keywords": ["colors", "tty"], "license": "MIT", "_id": "tinyrainbow@1.0.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "dist": {"shasum": "cd3c134dfc8189c68b00540d387bb76010bcbfaa", "tarball": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-qUAdWpaJR3XpOv+avqOZpEdpI4QVx7KqzLLIQAepnSdn0ShVtuhTLlBY+SUrVb7X3loc4yTv+AAKFPHM/OcTWw==", "signatures": [{"sig": "MEUCIBOvxhEdn4hFFU8x/9nDIsPVfcPcXoUGtlIxGYu3dguwAiEAxUCrk7NuazhOgORHlhNNBS0idtCOvy4vVz6JyNrnPeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkN9W8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3tw//an0Ohf78HEIGWECkFOG1UUwUpdx+FiHSuI6usxyh32nPouql\r\n4qLmmat6S1dCCsh6VZBL+BlsUBj7XZ15OLyabuuFSfKifDt2Ge19Zjak4hlS\r\nNBcQbDzYQN2HUl0cUUhwUZAQNQ6pmPhKSD+F9UUWy/DjqwQrQDyIfnOKWiLS\r\nzpsqi/fTemFUvVOAqeGhdUP1evrvs/pEbnxuDmB/SRj2ya2sZBUJKVh5ZcHD\r\notloDP6mmSC0dNZOMibqh91hc6llX4XGqiUiHhUTdA4Qt1EOlCYTdI3klCC7\r\nmIj611NtCT4ejOU0vnSf2XAjkks82t3XVZO2OWh0CTRp8G+WRBZLRRABVyY3\r\nBPEPZCoDXdBJCes4s5g6ssayAHnd0VuC6t0FsvNSVJPdH35ZbfOF97J7Ni4J\r\nCwNCWDNBiyCnfPEFIEIHbMfDEEWZEahOdObrf/sF3ig3MM/cCCaRK/PsvixC\r\nUSAh283ejfQmS7EkurVHww+dOSUP7KfbPGOvqqiVyGdAD5cEda69MCrqgdvM\r\nPHioTBJ3zHXgO8zktj1NJV7rtt1MNagAUVlyIQ1PbzU77ya/L/CzX1+U8GIe\r\nP3lUXu2kQVmUvArZgC27AFZbo0v6OzavMSuEs1c6SxWYEQ+eihjFivH/ooGI\r\nGyA2WXMarft8h58NQe9jbxvLa+ukghKrdvM=\r\n=U0AZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "gitHead": "f4009831a70e7800046e57fee59499474d192322", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyrainbow.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "> a fork of [picocolors](https://www.npmjs.com/package/picocolors), but browser friendly", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyrainbow_1.0.0_1681380796649_0.8521227938129485", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "tiny<PERSON>bow", "version": "1.1.0", "keywords": ["colors", "tty"], "license": "MIT", "_id": "tinyrainbow@1.1.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "dist": {"shasum": "93c4e2f4fd31fa823e8ce2475d3cdb49e71f71d8", "tarball": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-1.1.0.tgz", "fileCount": 9, "integrity": "sha512-lbGDClRszdt+k0uOgBzQq7TSkr/lXQ/vifpyC/fASkAV9P/VVbdnLCH0x0yXlpIeXaPSPEoc13eECHvQjnqB5A==", "signatures": [{"sig": "MEUCIBwJkirvFf9fTiU74QKfTMaAcJsmMaX9YJPRUxb7B2vJAiEAsUKsoKetCKzpJ9ybD7XF2nGboS6KH+1iLNaUSXixT6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkN+IlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp53A//Xe8btjfnYQZusT1ngJMJ8OQabZzun8EqAxTbuWf7FNMMdwbt\r\n3n+rXl4clN8D95I3T+fxItJCyHOxQxzLAJ7Ey1K9+WXe0Z9lRo4KPvggfStv\r\n7Qz7SWaUdOR6EM6mopMYuK1wiGpLoOthe20UxHQ01iwmBleyjSnjZp5zm1u+\r\nakmrm4FAu86HwVwf4vL8YjJpJG52efh3cWh7gsF4fVCLuZM/w0I9+/CVTBIR\r\nBvTK0OAlG0bgdRfnPPzKraDnLRN8MGu88jR2D3X8p8IO/Q82z15NtPVHXYAl\r\n7lpPveQMk+sdVOG7wSqJjfd+shPd27lLHiEguVJOxyosQvStPeeRknOb9gBo\r\nXI6hBUXfwWB3rLpvmWWxmisPW//l7SXJcp01xklleEYVZYwtYrSVNTfg5AxL\r\nzoSv9DLX7fkTPJagIqsV0KzAneg+ybLoReauuO2P7o42gmuHI5jT8ughBpOH\r\ncstERsQLuPFvoH134gVNUc4rW7lwkY/hdvFuD2dzx2KYxTja+W3CUIsxjxaf\r\nHlJRPAcNuBsUN77jQne1jCPnDQiPeZcUitSUNNDC1UPm8sA+2Xcvm/joZPPo\r\nvp71QlToKpI8lUg1bKpCiUI9ZlKQES8orx75co6i/4Wouo9TJ+UpN5qzTDcf\r\npwvmDrQiZTIDlRpvJgiGSXcqSlpCt9iHPek=\r\n=BuyO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/node.js", "type": "module", "types": "./dist/node.d.ts", "module": "./dist/browser.js", "browser": "./dist/browser.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"node": "./dist/node.js", "types": "./dist/node.d.ts", "import": "./dist/browser.js", "browser": "./dist/browser.js", "default": "./dist/browser.js"}}, "gitHead": "12cdd8da8b16eeec2d56336abfbfd327d9edabb9", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyrainbow.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "> a fork of [picocolors](https://www.npmjs.com/package/picocolors), but browser friendly", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyrainbow_1.1.0_1681383973747_0.4519752070540337", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "tiny<PERSON>bow", "version": "1.1.1", "keywords": ["colors", "tty"], "license": "MIT", "_id": "tinyrainbow@1.1.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "dist": {"shasum": "08dff5d4b86ff253c0b1087b0554c1f4d119ebf0", "tarball": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-1.1.1.tgz", "fileCount": 9, "integrity": "sha512-3yBkLfmrG/x2fkhcDAg4vyS4/GLSCOXtXa9bIfzEN1Y3geldnA7aEPsHfSsKqsvdEsPnESzkV9aU/9T0IjwWFg==", "signatures": [{"sig": "MEUCIQCGqhcfsLjnTKTz/8c0zrqnM0XpKFlCqdkIXTNDwdleFgIgGGeDmVM3C3InYMgBSaQ3ZYx9UQ5MJID0Kz3zJY0dTQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkORjvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZkBAAjzdPubl03ztJOMSts753DtYnu+tKrCyQYp4aWSI25WrPl/bE\r\nuYrYkHTdZKsPoSwCmsH5qpglM5vcfQlFRXo5U1KgX1KCsvmH1bBIMZnEobtl\r\nUne2w/Y+5LMnVe2EPlvDf46fcRlYxp8d4xzfOHbclx0kfPxoaphewVbsBTce\r\n/+ZpEluyugjTsy+mFDSNxFjK2K5v/*******************************\r\nmxVtUs4kqMo/H1x3nDk1iZbQS+PI0E4Vrpr9YkORa1gVRwz6egqMcfnzPDpf\r\n+LocRnTb8MQKJ3JmauqXvpCDbfEk5xUo4jqRbmwvubti8onst3t4yWgskwIa\r\ny1UHJTCiSanjChh6doq1g8ncBSOD0L+I6fzvl4Sd2O81/keTPCsst77GQTrm\r\nZ+KNlxgpt/NHMLk4xrVBuHtcXZifYipSz0sdtBjDxL21e+SmEgeN+z6dMD7S\r\ngyGuIrV+rJmNayCjquIplFc0GN9mkRe8sw2hXkPM7mxyWDq837t/Ko93uX3X\r\n3fnlarSElDRwjcibDiOgmfMojesDJE0MRZ/HjuB2Ue0aAc9jfEhwmvq1rX7W\r\nNX842VqsfvbKaYIo9/0/zhq6AZdvBwjoIfZ/DLnUNOoIcTmMImEJT2jVj+C1\r\nXVqDyDfSFXUVKM1rt11SrlEx11xQVW/iPbk=\r\n=jMTe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/node.js", "type": "module", "types": "./dist/node.d.ts", "module": "./dist/browser.js", "browser": "./dist/browser.js", "engines": {"node": ">=14.0.0"}, "exports": {"node": "./dist/node.js", "types": "./dist/node.d.ts", "import": "./dist/browser.js", "browser": "./dist/browser.js", "default": "./dist/browser.js"}, "gitHead": "142a08471673db2109eab25507a0bd5e53c53885", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyrainbow.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A small library to print colourful messages.", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyrainbow_1.1.1_1681463534832_0.8988294733391742", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "tiny<PERSON>bow", "version": "1.1.2", "keywords": ["colors", "tty"], "license": "MIT", "_id": "tinyrainbow@1.1.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "dist": {"shasum": "d7011c6ea10b8010a7c37b2b6520855152df7283", "tarball": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-1.1.2.tgz", "fileCount": 9, "integrity": "sha512-oMttVNy8VdB46XUh054C63bGS2t8WhjTWbtdJNewItd0/eZZSDLxf76eUvkysWtYAJDRgcfWBuxOq9qRn0Dm3w==", "signatures": [{"sig": "MEUCIGMlj3cQmjCq/xVR8AfvP96KL+lDbdD9yeiBwAhjSNV/AiEA01cy9CH3Scb/hewn7FwovQswCocLXl+PwCaQojscZtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6771}, "main": "./dist/node.js", "type": "module", "types": "./dist/node.d.ts", "module": "./dist/browser.js", "browser": "./dist/browser.js", "engines": {"node": ">=14.0.0"}, "exports": {"node": "./dist/node.js", "types": "./dist/node.d.ts", "import": "./dist/browser.js", "browser": "./dist/browser.js", "default": "./dist/browser.js"}, "gitHead": "e77c61621ae3456fa0b6d2e572234247967e1d57", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyrainbow.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A small library to print colourful messages.", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyrainbow_1.1.2_1720601317970_0.9348358334470026", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "tiny<PERSON>bow", "version": "1.2.0", "keywords": ["colors", "tty"], "license": "MIT", "_id": "tinyrainbow@1.2.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "dist": {"shasum": "5c57d2fc0fb3d1afd78465c33ca885d04f02abb5", "tarball": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-weEDEq7Z5eTHPDh4xjX789+fHfF+P8boiFB+0vbWzpbnbsEr/GRaohi/uMKxg8RZMXnl1ItAi/IUHWMsjDV7kQ==", "signatures": [{"sig": "MEUCIQC0oo/gJ87LHXEAkUfhSwnhwlXtBrz5vc4Z8oU7MtmELwIgWMVru6Fj1ApurSd94WXhqosJqmYSxSyCVoVdXneCoYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7907}, "main": "./dist/node.js", "type": "module", "types": "./dist/node.d.ts", "module": "./dist/browser.js", "browser": "./dist/browser.js", "engines": {"node": ">=14.0.0"}, "exports": {"node": "./dist/node.js", "types": "./dist/node.d.ts", "import": "./dist/browser.js", "browser": "./dist/browser.js", "default": "./dist/browser.js"}, "gitHead": "c8202ffc00d9f7f039ebdc2af52d08c6376fb6c0", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyrainbow.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A small library to print colourful messages.", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyrainbow_1.2.0_1720602956365_0.9100375049002805", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "tiny<PERSON>bow", "version": "2.0.0", "packageManager": "pnpm@9.15.1", "description": "A small library to print colourful messages.", "type": "module", "main": "./dist/node.js", "module": "./dist/browser.js", "browser": "./dist/browser.js", "types": "./dist/node.d.ts", "exports": {"types": "./dist/node.d.ts", "node": "./dist/node.js", "browser": "./dist/browser.js", "import": "./dist/browser.js", "default": "./dist/browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinyrainbow.git"}, "license": "MIT", "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "keywords": ["colors", "tty"], "engines": {"node": ">=14.0.0"}, "_id": "tinyrainbow@2.0.0", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/aslemammad"}, {"name": "Vladimir", "email": "<EMAIL>", "url": "https://github.com/sheremet-va"}], "gitHead": "550fbcdc56b9b2d69230e73b48e3d7ba0dc2e60c", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==", "shasum": "9509b2162436315e80e3eee0fcce4474d2444294", "tarball": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-2.0.0.tgz", "fileCount": 9, "unpackedSize": 8108, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA33t3cjaJODhr95mr1geGz65EpoUKBxfo5c+yvyhKObAiEAn6uQdYK0NKXwSZZupMKpnuhcBrYcR7P7sroG48eyqGQ="}]}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tinyrainbow_2.0.0_1736789745051_0.6158614653867567"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-04-13T10:13:16.649Z", "modified": "2025-01-13T17:35:45.393Z", "1.0.0": "2023-04-13T10:13:16.854Z", "1.1.0": "2023-04-13T11:06:13.919Z", "1.1.1": "2023-04-14T09:12:14.975Z", "1.1.2": "2024-07-10T08:48:38.098Z", "1.2.0": "2024-07-10T09:15:56.543Z", "2.0.0": "2025-01-13T17:35:45.218Z"}, "bugs": {"url": "https://github.com/tinylibs/tinyrainbow/issues"}, "license": "MIT", "homepage": "https://github.com/tinylibs/tinyrainbow#readme", "keywords": ["colors", "tty"], "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinyrainbow.git"}, "description": "A small library to print colourful messages.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/aslemammad"}, {"name": "Vladimir", "email": "<EMAIL>", "url": "https://github.com/sheremet-va"}], "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}], "readme": "# tinyrainbow\n\nOutput your colorful messages in the terminal or browser console that support ANSI colors (Chrome engines).\n\nA small (`~ 6 kB` unpacked) fork of [picocolors](https://www.npmjs.com/package/picocolors) with support for `exports` field.\n\nSupports only ESM.\n\n## Installing\n\n```bash\n# with npm\n$ npm install -D tinyrainbow\n\n# with pnpm\n$ pnpm add -D tinyrainbow\n\n# with yarn\n$ yarn add -D tinyrainbow\n```\n\n## Usage\n\n```js\nimport c from 'tinyrainbow'\n\nconsole.log(c.red(c.bold('Hello World!')))\n```\n", "readmeFilename": "README.md"}