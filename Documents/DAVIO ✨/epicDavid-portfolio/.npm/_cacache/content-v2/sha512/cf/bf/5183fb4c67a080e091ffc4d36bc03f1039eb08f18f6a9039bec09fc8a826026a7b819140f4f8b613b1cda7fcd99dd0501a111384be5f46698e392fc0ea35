{"_id": "@babel/plugin-proposal-decorators", "_rev": "167-78efb1a3800bbaff549b843be3d3deae", "name": "@babel/plugin-proposal-decorators", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "593dbcf12bd945bd3143017d1a1183c80ca13991", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.4.tgz", "integrity": "sha512-cwQQ1LrUETAPCYNcH8fUuE1NaUWw7BgcRa86uS8SMuqcAIfKLOpvXC1wisUp9A3sHIHZ0BNcDSPvP1SvmiLC0A==", "signatures": [{"sig": "MEYCIQDbmE3AYZpGdZnrkMcyLAc2PCIdS/o6rxUKRXMfqRG0KAIhAKy4rVBFY/S9t6uA6Tq+A3szMWKIswyKyCeo3RjbJLes", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.4", "@babel/plugin-syntax-decorators": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.4.tgz_1509388538709_0.27185378782451153", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9d820fe36db3b7c83d84e0b3690ce5c00cb6a30b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.5.tgz", "integrity": "sha512-WvkQF4fVimdTFMIeTfhWE+97eWgMXESUcwr7RhHYPB7R/vXrx0u2C4dumhiKnp/HoNgSsiuvsB4w6UPbDtdu3Q==", "signatures": [{"sig": "MEYCIQDB7taFh5fCVQu/Cf2DHwBN1GXafiz/g8Wa3Lg5Y90SpwIhAJXJnR/e3yzVMGXM53pcOMrCcbTuWBVM/1z2PGif+bK+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.5", "@babel/plugin-syntax-decorators": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.5.tgz_1509397037571_0.895800095750019", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.31", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fe27560a49068223b5667c2ce9976e931ca5ed16", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.31.tgz", "integrity": "sha512-YL7lhKiS7x810n89ZnqslM+ja7XeK8A1JxvQOwhKfvwQoAzWNNNOsz0sfl/GiUqKCEnEEr/SDQdJRoCP9g1EPg==", "signatures": [{"sig": "MEUCIHH/fv6i6KsIRvpNe9w8N1PUhmnlB2EENf16KrqZAk18AiEA7Xm+o1jqWHRw88YTzQintXjT/PKUr3IWluJS9W/7yUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.31", "@babel/plugin-syntax-decorators": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.31.tgz_1509739443796_0.49817291693761945", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.32", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "479d77f5e3a80ac38a41e01110110d0b7c32f426", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.32.tgz", "integrity": "sha512-LaWowucWMjdJ2X0SsGaAfjkx+K5tcoh2Cm97keGGl9xefg4eHkFtjTUS/x19TgaN1MXfkVK5aHpGCg5VFsa9iA==", "signatures": [{"sig": "MEUCIBVd3HLk61SkVv8iyglTcO5W6CQdWyO85g/NuFU30kGSAiEApTJ+Xq4qwBj0JJApeizrEfsxtrXoP6k9c4LEWTj6Jk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.32.tgz_1510493614584_0.5629228118341416", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.33", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "815af6e357339dfc20ddcc916453237b028e9ead", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.33.tgz", "integrity": "sha512-cyRE+iffIq583AyjqCcBiwLdmvD57mIRbaeu+1/jTn8Zc+qf2S2wDHqbATKjPf1PiSYdh3RmvuDWhFmxqBp0EA==", "signatures": [{"sig": "MEQCIEO8+0Xla+PjwKZnH8i3usc7C6QjlAstTe3Bz+cCYCDsAiBU+8CS5gEfYM9QYy3OrKc0Y7eZyQ4alJcElkDQKR8cvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.33.tgz_1512138522693_0.7966404142789543", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.34", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9498f6c33a39ff52762a0f35485bee1a2661fb5e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.34.tgz", "integrity": "sha512-N0zwcFy7fYJVsvU2I8L49W1JLTKClQCdW0Q3j7rj4WrZnWFRY8ZaoDXFuGGpEvuiqoG55pBIYafz6oSRnss5jQ==", "signatures": [{"sig": "MEQCH13DTnSnlmSQiUwiyGSU5nXnJKaJvFQ8KQB0qg8afLICIQDZfoStXjMMaGqRfyKOrwD3iZYv8yFJu60isTyW1TUwvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.34.tgz_1512225581717_0.15042259474284947", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.35", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ce88a70be0c12c824622c8025b9d58d3d26ae522", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.35.tgz", "integrity": "sha512-Eg43726cJ6ArcNDv9cb8Uc0De9TLU2cGjLHQjNW0cGbVnv1vy/kIKlj1HYZ9nlxkfMnNMeJ4bPb5NHLv6nZyUQ==", "signatures": [{"sig": "MEUCIQDR6RAZis07nsNB+S1FJcFzA7I9+8liz3vjUCJKbDGTqAIgdhA/ToL8nkfOCBiRqpux5S9iz7hvA/uiIgUNIEWzYno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.35.tgz_1513288084040_0.428831193363294", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.36", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "bc803134ad6274535d03d16c6ec53dd2b1621603", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.36.tgz", "integrity": "sha512-3tCIefmjv5q8t/2uycGNMSBGLvdT0S12/AuxrVsLNkl3yO54a0zP5arm2TJUwa5Cg3Iy7r72xQAaaydzy6eI7A==", "signatures": [{"sig": "MEQCID2CclHhUZGPs1PsuPepNQToKC84g+sydCbWJOyljGGFAiA6tPacWcEeHAvX/4IopTzehxYIlRKm0MX4+uvhnY85Ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.36.tgz_1514228702072_0.053294151090085506", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.37", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8c5325ab2f2483bf35d651fe2ff8d7054f7770c8", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.37.tgz", "integrity": "sha512-v62wP+RS+AGlVXYCo2uFQaoEBHN4346QNTi7D9+upsMbHghpsPRvjHh7z6o/i/UFPyJEFVxzL71ptBnBrEQH9Q==", "signatures": [{"sig": "MEUCIQCEHrpErKYccX1dhpNOmhxslYTrXUbUfN7aqwV7CaY6/QIgFcO2HZe05ouVVNYpJkFm4mDvuVScr5Tg8hVi9VhQoSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.37.tgz_1515427364641_0.029738532146438956", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.38", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "888903d395d902b763c4833e2bbf2f3d67e8155b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.38.tgz", "integrity": "sha512-VXo0r675R3wxmTCl5jH7vuY03pfmQTe1pA6v85lZkPdtcBCYA5/0VwQeE1IKqcRbcQIh8+hX2V+8jCxgy3DF3A==", "signatures": [{"sig": "MEYCIQDZUEYydFzrbH32Nk3/n0bqfmY4R2iPhbPtEWcLtff+SQIhAJBeVMIV/0mv4P7Inj9nbSa87Bno80ryGIr72gyllFlV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.38.tgz_1516206733763_0.002383406972512603", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.39", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cfd96774bdeabfaca2055b3009d63423eb461259", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.39.tgz", "integrity": "sha512-9pciVGU8A+piVT+aQbaRbqaLWGVwRQKeZe1r8mBpbMjvKvXC3Fy3GLKP17o5y9OKRbi44POORgrXRycpfkRCRA==", "signatures": [{"sig": "MEYCIQDrLxLrmZPdmy6pOkNPgzfTP3s0JerdsWoGH0C07t5t7wIhAOKxS+P3ELfjjkKZqiCylaTy1ipBPjBMWBHSXQMfdnB9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators-7.0.0-beta.39.tgz_1517344065786_0.28475603205151856", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.40", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d2c033bfa55719ddea681c241c9609b346c7cd9c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-Jf6M6wIGC0vsqBVm1vFTPM0I+1/+wuBq+k8xwG2s51CR43PWkJ+5KMCF4eIfRw94QmWHyLDvV3RnYYdBi0+2sQ==", "signatures": [{"sig": "MEUCIBa/oybYXw0lxwfQ0GENu/D99y910TxjxKvc3fW4nGODAiEA/tLtpmtb7CqShBtOf+xwpsgKmjxjn8W+d/XvDqh6nnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10303}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-decorators": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.40_1518453719541_0.6974137935491194", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.41", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "850ad5a7ef321d24d498483da351c4eecc496e88", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-+kGpg/JiZMq2QPUYjYTm1IawefEMpOGP9s8zSeE+7evyklm9l21fUT2rLtaw5Peyws69FXCt7VWIZssRs0PySQ==", "signatures": [{"sig": "MEUCIAgePKlUE9uRlYI1PJW3gMERQGUAueQgBWTw6DBauB4vAiEA4A7tC11exP/8aAjJ/zmcQnOnMycy1BHq5jbhS0laYOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10514}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-syntax-decorators": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.41_1521044782201_0.7113618330961773", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.42", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b38ed569a72ae016f034168936b17973f7f9c6e2", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-Xj0Khj+tnXFEuOfxYnJ4XWKT4nn5U4BiUEtu6eAu9qAAfT7t2ssFE5trVosCSR7+MqRc/2rhmfzqyErcF9xHJQ==", "signatures": [{"sig": "MEQCICIv7R/3RSWKHM/oxwgR5DYgY7XRAMoTXuu/H3UAbAa+AiBuJW2ttUwV3NaRiwVQaK7pze5oHeSQRJo0FUvHAlfrgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10514}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-syntax-decorators": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.42_1521147096143_0.7057056372272601", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.43", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "70cf9fd099fc9beeb9159c276da5c9d4c1967107", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-FCQsviqBZ4uLS2SgOIKCZkA6zxUtlleXGAZSi1tXPDIWIt8L8Uq2WYikL9u8Z0Hl8iIzA9U7hVo8KIypzj1YOw==", "signatures": [{"sig": "MEUCIA9K/0j226HezfUI6DpX8aINEzZg+VsHWng1t0Ta0g9cAiEA4NYgJ+oIJjOA/wNyYdWf837Ioc+OF7eOlaGDahOaYsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10699}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-syntax-decorators": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.43_1522687713544_0.6622893236283203", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.44", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "287ba7d320f58b45e8fb7148b0d06fc6692c8c0d", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-Ts8qG11dM1jhkDj+qBSwuowlBImajJNlKM8VoENHOrSGUAqyrv1fFtM1VfmsO+GZmegwDFe7LGVMDB+lS04ZeA==", "signatures": [{"sig": "MEUCIQCKTESvUjq2X53ASX8g83xUd4W7I+QzHiXiVwnETCg4uAIgSYX+h0yXy2BK8UhPWsDfLmlwBAkDzksW6AoltTqplpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11008}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-syntax-decorators": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.44_1522707615115_0.0878075257037716", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.45", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b2887ec06d97559cf212c335457a732b15cc9b39", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.45.tgz", "fileCount": 5, "integrity": "sha512-nqwF3j9lRa0LmBUae3Sh4TCzajpdOIYXLsJg/KcvmGhHTGKYoQdO8DRdmlwjofvuPKe1nanYcYd5xG3GGm0RTg==", "signatures": [{"sig": "MEYCIQC7KFB4pb8FtBfxVbQc2TeSc3HmMAjqrrnj8LhhJgy82AIhAO5/G0M8D2FTpQTPMLuQwKl83f7fxks94/G8JBcLj8qE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1+CRA9TVsSAnZWagAAnJIP/0mP6zUhUK7kPm8SU4cG\nHRcNaSmsdax/v3/pOmktVs3bXtUIkLkOA2UDSgRug1Mf0LPlE60sxWT4jpmd\njrLVJhq4OtcFO3vpNsx7Zz4jivXZ5uTo5cWPTMH3Yh+tpyhL/K3BHiAwFNEJ\nqX6J05VO0iS2nAb/sNPtX3l1/BLwu+xhHOjqhRbobP8YCDoY7tSYTgdtLFaw\nP7knjd8v/hYqI0hDC9m8O1WhBKGZ3CxsfxBb96pk4cQNW6zAjSC7xSXukQIY\nlN5dopoAwPpxniv8wdnZbJVMnSR9GS1QKdf4908SRp3fuyzULVQ4aOpcCYfO\n75TryTVxqLd4LJ1QF4DmXCFFTNrpL8I7VnPMAZAq7ycOFjMRm+xu/9i+xa3b\nugIvg2gnekssuKggqiu7JuOSf2Zx8xUfOdjkWo85joT2RoxKkpcjJ70DfH+w\nU2hSxWZ5vAIoqWkSJNztIf55V2dor282O8wK+uPTyKO/x8/EhNUFLIkyAmT7\nsgNaskLkFqo7MEeFa3qqfhGiXV5zPfoQOWxK1lC1BtBfdR+VOGpKkLWD+55u\nkopS5mordbrju7Rg0R6LD6ZBtCSyBVEsjr/7Y4Lg4rNickWAqYDK2PObzUJW\npjBdkJJWtDoc90BhyszIwsr77FWEwbFMASBZSPQfy9QLRk0M6MTAXhjr+Huf\nmvbj\r\n=Xr55\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/plugin-syntax-decorators": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.45_1524448637186_0.15676836291252805", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.46", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "48c89fc48dc461db03f1260675c73f98afeeddcc", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.46.tgz", "fileCount": 5, "integrity": "sha512-2z+ayU1saRWfvHGE9uU+275EGSDQlGf37U2Vs2F9BjN0eUC+JAC96ncq2FuwLLRR8d6rfckzBJNqDNuW8Azaeg==", "signatures": [{"sig": "MEYCIQDo/r0QB2ij0sUrQs87DyOO0WoL5tWwDBDtiXPjjZ8VOwIhANmApHFThmZnkUERyY1FtlUdU+U0Rb3Iiw17r2pMZX6/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGtCRA9TVsSAnZWagAAKTsP/j78ZnW9e0HJ5CRuGIKO\nq/QtKJTS/K0fkEZOkvuNKdBYOB/Z+ua08qZCQAla4AQWRp2Y7maH5/aec8cy\nPTM0psfd87Y+2PmEBiGLQPbalhjQ6KpSUKbfKEhzk2yAWHRjlE4eMOiB5s0T\nMAIn5l+NUieaIovSKsdM0ClM5o92y7HLlce3eHBNoce5x/BFx32JWJkoN32l\nJKtnYQeJeZr1KTMTLzjABSUX9CWlxy4UDNNY/zmBRYDXIgMCy1OeHk3GZo/B\nIGaplbdWmS4Rvg05HDzn3klGxeesjM+YBvlCl55runcvbFA+PNRuSzMNodV3\n/rFzHddAXBWumQBafpDohalaV3ZeFUMhVS3eNJTVZpGnAxEOSRxflkoMYvo6\nXGnafxCG6u4UYL7/pGEwSVH4ZAQvB8dcYd+yjsPXnZ/ze2lvMktCH0CnQt9d\nFXHe76nPzf9ZM9dwxJIsgcqgEdjc/1eZfMjSsGHzmc6XxV48wZPPrjvigKk2\np5B9ppDIKCrG0vpArjt730MPArQv9l2h0iTlVYHLCbrhq7GE0UNBPB1esQIG\n+F2BmoguztHkqwAw1OxTKfZ9XIfxZ7QGv0G/ocpcYgblYZHHqnWOSOqFkWAA\nx5as6DX3IeS9BEhTVbQXTENrQ0Lv7urbejEY2EUIBA0XAodUMEj03eLEz8H3\ncfye\r\n=a3eo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/plugin-syntax-decorators": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.46_1524457901445_0.17731149299613147", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.47", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5e8943c8f8eb3301f911ef0dcd3ed64cf28c723e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.47.tgz", "fileCount": 5, "integrity": "sha512-sI+cO1okrlOyV4I63HDXf/SFsCr492HLjzNsMsd7Lk9WrViA+eQIboIiI9wHicozdgD1WrpZGJTjz7Z3xwl2Qw==", "signatures": [{"sig": "MEUCIQC56LdMKQe+tdK25gGT/vvP0/PEZ0FoCevX/XOSOzziDAIgUjSnzJvlBFAqmT25vyiRrIV1XdDVBYFhZZJ8QzKGaSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iU9CRA9TVsSAnZWagAASHIP/jYcBKBYFLN01xJkAFTo\n9Xte05DtrkJhqGt3TBtkHG6GpmzuUW4rRy82d6Z8tkqZ4uyhahENSRqcnctG\nmXO4qKyYbr58Z7r1sSZhGEnqSsYuHR6Z1G1zjC0TyZY1dFENLsMnDmcge+c0\nW1otV6/2fqJhIvN0/bCvQYnoeeyblhTV24d7IqfDyOpRzr2BtvHbJ8cChEP4\nNG2W/YPBCk8cZP2fZXRRqZEM9lcOimFOFNp3FRYWnhikhhox8R2Sey3uqfaP\nfRWt0iHLmoAaWWuyhIxi26Zhzba8roHxB/ztztL6sGFYLvy8Lr4SizI7Fo6h\n3RKEs5JEdqI2+p0fGo/+Pzzr7WQ543ZkCYFyxHfw8D/eSJ5OKfOjqiF4j1ew\nf5kUT4xylAB6jmnviNwxqcszIaZ8RZzzFSAF/8wzkX3vZ7/wInCEFEkG2GUz\npxeGmQeBbdL5aBvwjhT5/cSAcEzQp4ygxNL70wH9dvo/f7R4RtKeXZGM8wQX\ncYTEIAIPnNYtPZjSN/e9lMv5f3mEmxGfB17iiS1NAM625xtAjviNi1HerW4f\nFAM2JjWyr2TWGXWtz5C3QepUgDASBwFoSi/5DGREzEV/RO4UBDWEhIX5iFaD\naHDY7oIjVuw60Ph2XalA0BQOgKA7/+2XfZThIL1hd0ucW23lKhdE1uv5HYNM\nSZPh\r\n=BcHF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/plugin-syntax-decorators": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.47_1526342972291_0.20176697590111448", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.48", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "36e7f4435c17627f3ef111c33c07b4667e9be584", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.48.tgz", "fileCount": 5, "integrity": "sha512-jonZBgyNqMxD88WLx/pAtQxq3iC6tlYeQNxG0v2xz5R8C0k+wiWjzkTN/0LU3R8ykiMaQIMOhJJ665HYiA/WVQ==", "signatures": [{"sig": "MEUCIHd3gbhkHG0cY0KQ9UPoQYQW+RBLXcQMY7vlEDiolpiSAiEA7xLDqmT7JZVx1YuI9omiFhruo0iddh77tvL2oGi9wgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxEpCRA9TVsSAnZWagAATksP/3FoSkXi0IPsM7/AsDAS\n+kHtAEcx7Mj+s0QvZo0sNJFpku0XPmuhCmSLyHNVZbixQg9ahPNAxW5L90hb\nYvGSxpu1N338iQx2gzMxwEe7bAYeaOw4psaC8Wbhgvx9dC+50yjN6bEnfymw\nFtoklp1BGEEgFhnN9eav/wlSQNxQtYCDiXJqbQSGMHPeanhRa8IAwQOlGnIu\nNwLckZDkvA3rguWQp6lAlC1tWQPuWkcUwMIIr5x7Rqvyd28WIscZXjJ2MXr+\nvEKEEMHAdKyOqZfyKcGexT3UMM7oenMJyJ/zJi93H9rDwD97Ge/a05i4CV9p\nVQtFgjvcc5TSLqBeqhWLtQlcmsgulpYCHnSEwQN9o4Lt9WS6i6AoW2OWfkVx\nHHKRq4DnMNvWpXk8Cs3FcLKxSefp6XPSTrjygHVXJHbJ+gohcOQBJ24LBo5b\nrLRLIt6LxSyhrHHxl/hn5Iibi1yUibinwBgPNtr1ca8bdbxHpx/CHeAaQWBA\nM/CktdHJkWfMKJQvPZIKH9e6nu7TmSc+vr5UHKm+Ll2cVGTlnU2XBsE3tgiB\nMz+hNGQFpn2lmCH/n2hun+of766D2DQGaD5xIaJrCka95gWMtjr6KOWmgJiZ\nWvbMnK9aio73gCVjmi7HruZrUVOZgn9mHPZ+QYr6T3pbpqu9ys6Wk3P8CtZq\nF90S\r\n=NA9a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/plugin-syntax-decorators": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.48_1527189801098_0.8658783690457894", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.49", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5760ab683174e60ad670dc9c5ef39f1d7d349c7b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.49.tgz", "fileCount": 6, "integrity": "sha512-fhanmnO3mCctpDxBwooXiiBYz5WtS1CA5zPM6vZN4F/dUOHUslDIp4c83mUbrqhw1IntS29f/vFBFFtXZFaTEA==", "signatures": [{"sig": "MEQCIHR3TraHIAetNuXeqtMnRdmu427V5yqCj+XCD487eokdAiAzpFWgqDaDj19Zre1jcryJfMl55fHJXjd46ZO3/kW4iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOmCRA9TVsSAnZWagAAMr8P/jAha4UJzsmoS/LsE5/f\nZvbBXzc8iE7KfaOoJZENidkScMM9vALEldIxKdij84cDLaKUmNtawOWHCIgB\nkdcJlHYj7C0H2INq7ro9hWXKeMi94hik79M+o7tZaTLsx8udf+uml9Zsvl1M\n1X6L7f5bWjUryqrKqENhzgXxC8XBFpJYRn1uA9dndBKQGFhPlWEQZRW1E7tN\nWrOr8SB9NL/LcgqKL3IsyQjfw6gAsne62fdXNmYiEW38LZnfKlDht84meCEA\nBn/yWbP2AlEYHSWJxi6otMfj3Mwm+dSa4YJlMRzh6m5b3EBwV0sovLKiUP+G\njfch11y7KsXm/xKZoffg/2UkYfbxdvgy9OAvWp9AEofmPtBNzlRS/4ZSqVLO\ncK8a0J1m0BXpXmQyYWtFRZHWkK6qLVRMCq/MrzNSkqPNfibXsRKz/1Gw9EpJ\nWy9TvdBjwrx8VH3bD6egXaVIFpZa88MKPloUb4sKG7WlAJ+SzW6zOwKw6ns8\nY9IKtQXkM1ObMr/eNmZSmTSPIMsYIeWTcKrDSstfadq9iVzEjPA/uM1jI1ap\n2+EWIMKcgwSyFF3b4b2c5eU+tlzxyo0yLbAfZT8mcswUagBS1BE/1rQwAfQ2\nDZS1PomKUN3KwMmN9oX5ArsWyVwUxbffSilozn/BYR1UUHI6xaTcFZvSU0Ef\nbJC5\r\n=fhf9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "5760ab683174e60ad670dc9c5ef39f1d7d349c7b", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/plugin-syntax-decorators": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.49_1527264164989_0.5522965160607516", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.50", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "42426061df8a001c0765fa42688c4291786c529a", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.50.tgz", "fileCount": 7, "integrity": "sha512-96B4mJL9wOZPm7kwn+CM8GqNZwHEe2fSZ+uvzie/lW7Iw0BiCTqbRY4SLgI9FKwu836+/Fq3SHvAxuAzUpJurw==", "signatures": [{"sig": "MEQCICkqC+hdjZ/vTaRypDXRYkm/sovsyRixV7+IUMFITSglAiBtFmFsP+IShkAG+hAVuapAKiyJEdTN/+bI/unwW94ijQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10363}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/plugin-syntax-decorators": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.50_1528832850758_0.8260133427201874", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.51", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7e903a57edbb2d98f7ae5ead71306e3d7b4487fe", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.51.tgz", "fileCount": 7, "integrity": "sha512-hBIe8e/WKcRzuOvHFr01kCrJmPB+QJZB8+TreM0MMlTgPf8al384WFZvogTj5SXJyTlDjuvJIbvtO8lwqAIy6Q==", "signatures": [{"sig": "MEUCIEH6ysBokCD7/LEvi7qqOsDhZ9/EyI5ds6FkWM0zc/BqAiEApXPzGa6xglqaR1SfzrVPyYQUHzURtQ0vMrjsx8AGcSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10377}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/plugin-syntax-decorators": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.51_1528838406445_0.46728884007959115", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.52", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7188142ecb3ab678d26ee610d12ae33488911225", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.52.tgz", "fileCount": 7, "integrity": "sha512-B<PERSON><PERSON><PERSON>ON1aEEb1KeTyI8XV2MjGUF3DPX+ZrrMQnvKAe6lq2Tm+JwU6n7bKiqrSAcqC1sMflc48lAw1cy2IwNyLA==", "signatures": [{"sig": "MEUCIA5uSkZhmeWGvjV3ADFH+7f+RogbsdMb9tQT6yeT4xiqAiEA6kpZGMkM0y+iMWCeVS4K76EgMfTjAQDQMZrLa21aX8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10376}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/plugin-syntax-decorators": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.52_1530838771316_0.9375827955110296", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.53", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8e828d370af316ed74a21890cbdb7d02e6d532fa", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.53.tgz", "fileCount": 7, "integrity": "sha512-4W4vEhxrzGbK+t0CF7fzGyFieHKtMl/LLjyX2BpmLkr6if1AHbgQbW/b1OqMH6PW3lUBqZgmx+/Y72z5zwzi7g==", "signatures": [{"sig": "MEQCIAH7P2VCc2WluSrJ27s7TcsMQkWaHE9EQCrA5/+ZcTX4AiBuUMqYJR9va3sr/97NngsxxrBA3q/00D5WC+qwtUrYRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10376}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/plugin-syntax-decorators": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.53_1531316429816_0.2657111014614921", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.54", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "eb14bd5223ced8ccec8e6b900ba025e04f49578d", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.54.tgz", "fileCount": 7, "integrity": "sha512-whVGYD8Jh9Y+zQfrAe+SALzxmxaxv1zyP8E1vo9z7DTxBXmkT0ASgikbCvupK72wiHUVc1lN+YYE1ZqU4T0Htw==", "signatures": [{"sig": "MEUCIBo8uATmlSMkfDjD3hJlaTD9TNG9WoGinMFh1UexFr9cAiEA6dkYepAAb7XmJX9XD5yj0U1mAZURIa+YnJ1Zz/x+5bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10376}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/plugin-syntax-decorators": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.54_1531764012563_0.19354581850442698", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.55", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8a78315b650da8c128d6f1bf4b6bbd607cc37a29", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.55.tgz", "fileCount": 7, "integrity": "sha512-vtkX2GJMjvRdvXvSsbYQ0uem05lRVi/LHVInIFsnVd5QRBlYwoqjtrO0BUOnuIQsEfRFzKu+sU0VqQXy5/YZUg==", "signatures": [{"sig": "MEYCIQDGJonSpjLWtnMtjk9R4lC6roUfADptIkKpjd+z3QxJsAIhALXRZGk4OqyDVB2FuURhHk53Ik8s0F39aEF26vCSybtm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10376}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/plugin-syntax-decorators": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.55_1532815656098_0.6976024737768611", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-beta.56", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cfd22a591c1c6dde8b1c9e7b60ba0bdb8b1e85e7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-beta.56.tgz", "fileCount": 7, "integrity": "sha512-dBa089ag2Wn94o/m6ztUBL+Kb2YFrCzonp9G+rtgdVP3N4NWgfv38ImXwEVAQA+rh0OgZMfDVp/B6OWCIc2SDA==", "signatures": [{"sig": "MEYCIQDLZVzTZ+S3PSt3ZH6c2i221BDGJCx1OHjoHWWHxVk1dwIhALYlA2gTDYGKfhKXh7X3XrUshmOMrxokXP0NJiPbU4xe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPwUCRA9TVsSAnZWagAAlr4P/1kngEqtgXAkdyIxX29V\ngaRJSd+p05r4+m5EyL1yRo6K0qviiylZnQMPl1wIakQw7xCRql19fGNl5gBN\ngV9XA04exwRZMXWZ8ElFF+vwREY9bVoDN0gCtAF1pV7B1TrGWx3lW/cm6yW2\nzAggm/HReo/K2gUiJRv3Mm1PIwMbI8gKgM+DpsN7bn5/DTLcPr6qmfw9RrWW\nY22HQyJIrtR7Z9cy5J+HGFECjGZSmEEOZ9z+7fuRhSf6JB10u5nUDtfV5+8z\nik5UMlMGTWKEE6MPBiX6EfU10n/GbLdq567IW5s7T7JcivD24bz/ySlFDPwu\nemS+RxxKqRJ5CvH6PgkO5IrN9uvWELvT2i3U/jIjEqPp+yejCbWTukJNP0sf\n/NZlRpjGRKhthoLUdv77889qB36+a39gugWogRk6zPI2aJzBWE5X8OStze3s\nPMj+fODoE3An60wWJh59UEF2+kGOCPKx8OnER4QVw2SnCGnCDQVGOfJH7srk\nsasyAVlWw2DQUYU4yHtbRa2vttvhURZ64PCGWQ/6ceZC9ZUN3rF+ld8UtgMc\nHwXsoiy/N7bvPu/xPJB/W//H2QBSmvJu3xmRgPBLNvY8K8yfPxYtETT5l292\nWsd6KOZIrFCLd6Gq+oSkszL6nG+PtrJUJKkZKhGJHoP2gFU6E0NB1YvYOHay\nGu7F\r\n=u89H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/plugin-syntax-decorators": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-beta.56_1533344787902_0.5748688484215401", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-rc.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5bb9df53eff0bf46e448ef344a6ec52999554d77", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-rc.0.tgz", "fileCount": 7, "integrity": "sha512-z90w3xHuHrMFZtGngR+a7WJIoO8DDuBVH14EQzS/zOEyeiwsfysBhxvIZAAtafeWyQRW4AhAIhDeaH+D/jCweQ==", "signatures": [{"sig": "MEUCIA7IYKQlZePaHMYExjtYKB5z+byWvO896P/5lHhDgWH1AiEAt6GIzo6qMkuNpWxiDBtHJpgMrbMS2K0xQB9z55V2Q9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGS3CRA9TVsSAnZWagAAeSAP/1an2sJlEfAfZFt+wY9T\ngOEGhkbCECuTkJzg5YnGlEXJyVEJ80It6pCSpc8KHzwJz36cKUC/3xN1Bn2d\nBiSds2h598Vcx+D4zCnUtG4xAev1VuL4wPTmeSsSsxVTEybjt8JqAEkLiXd7\ncagLUBui80zKzZppVnRlkSbD3IghswKXoUxQJ4NxTGZKPFOUlpLbDR6fcoVo\ns0olEwIc8AgFAxfcRXlYglBn4f+eWNOgVcSUDsD+QTb376oGF45y3VuN1jJl\na0dVSSCme0EJWoLVe8Rik9eJhMfzzV0Db6s0niu4OtTx4zo7ejRuEU3b5Fan\nOm2skZX2pLS4FT003EtgkWS30E2pE9/QAlywrQQMmtCvZyOIUek60uHR5BgU\ncX+jSBQlzhBPU3RLio9VN30w8Wqy/rOBD9Ie4rrSXLzNlE/F2nhAqatetbuj\n09yy6Q1cXP/NIBHTo6QyhViKQ2b4nhZvCwqwVc9RM+oTMleemMq0TNqZe0GL\nZjU4EoOCYRJiEtE6CLguPPYoEJotroXYpyxLlGCUEzRVlMU05Ph9uc4RwRMH\nQcxpUlQDgxnPen/ipgTTk7JDQ7zK8FvZ4n2HHRp/qH0NwzvdY7yTzJZtMl6P\nrRsq9q9TgrA3qwnh5nZPDcvASjYzJhcMspGXnN8fkYzuuGwnr4bPmgU/nvKh\nXPIp\r\n=G4Uz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/plugin-syntax-decorators": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-rc.0_1533830327011_0.8169661960808312", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-rc.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2cb2a8f8707280b41865fcebd48792cc24f83b9e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-rc.1.tgz", "fileCount": 7, "integrity": "sha512-NRB6aPviuJ82hHkB9tcFFdvl78/ha7yK56E5mesUInmZyvk63TmSyum43s86gq3EFBwZ5tX9FNyT+3xBwHP59Q==", "signatures": [{"sig": "MEUCIGVHBqrHPFNGmTuM1N3+W8nJYTVHIagjDgvcXD+ebtLYAiEA944UYk6fAzlRlYJNPVs/1VR/p2p76NeEILgCc+jS3i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ88CRA9TVsSAnZWagAAEa0P/2gsPj3DkDhx5JF/f6bI\n57JYn0obVgTIPRWqDVW864xalwFvwOmgzY2APgEObhHwES3LuOGUDZJu5CpA\n9uy2M2az43PE8WTiLWA5qH3XYv8dAtuuwtv4PknVutEWByj5YjbE/SUvr26G\n3rO9fm9Q+WIB41oSdt90oeq4mdBm+I17+2iKPE1IDPADosoKuaxWZ6nFc8YP\nvka5rHoA0sQ2qBEthP86jXG1Pqcb4P+GwTMRRCfy+FqEjBxWj3nfBWEAl/MV\noKcfTT137oynmV5YeXIddac5naSKlNxhC2U5DXVDG51FtIYUj8eyXrKmRlDR\n+yP0jKMGIu7ypV7R7cCcyIY5FFULyuVRjHBjfg6AYdQowm09vVvT16uqj6Ai\n50VJat9dAHOatIapJ2/vx1iZwvRjLpbp6G+5WoY/eCy8eqKuwmJrJyizVeKZ\nimCphB2RIK9/9ww/kjYGrTq0vdLQYWlxq/GBlqhvrhaL9fW/5vMx62IdE+LK\nRrokdLgrTPU/fwHk3gSkgkmRdO+5a7gx4Ns6j2JdJP0YwK8GjbWlsdNixinv\nhxgbSBcL0Gd9IcUJp4+wLWIH3xApyzAtvurVQOib5iYK2Y13NFlmsXORHb8H\n/L1Mwx3nbMICmKdfqZXPSUAjWvPT8uTV3IM4C/8tP9sGMVWqhIRg1o3EuR+6\n/O9z\r\n=40tP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/plugin-syntax-decorators": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-rc.1_1533845308126_0.7021059167414259", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-rc.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6bf30195bb154c3a72022126d8f0ede588d1bc54", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-rc.2.tgz", "fileCount": 7, "integrity": "sha512-BRhb+FQ7Svom4Jj5zSd+2KbyGXoQAYpz5nCF8aq68G7TNfu9Xo09kCo8fxxS/JljxhBy/pXNri46dxM7lXH7sQ==", "signatures": [{"sig": "MEUCIQCGrPRK618Fmb0WWCmIw4w2MyJmo1+I93wIUXxYPSqiqgIgQfVk+OzgQwov6YygSgmthBIVJ+L2rTfflZ3irylRhwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbyCRA9TVsSAnZWagAAvC8P/AwcQhR8b34UdzI3C7al\nq5QA2COWFJGfmhkBJzFPVkHj4z3i5UyUxoGmEYwFCy0NJKhq5BMHCnvYvFSH\nxS6mDRUwN3d2C5ugloMrAEkHWANGemDl3BXibpsaVHkN/jOGt0uJppAhmUEr\n9UJfiyesBTutnJtl+Rx1l6mrKgNYgDC0q5eIVFn4fWDlwv9DlTFC9M1bSjy+\nPyNblWuI4gNS6breTEnwLeR2ruoaBKIBQSeGKAoGrYkVDp/zODWt94p2x7NV\nPpyrbKdEZC3dlyhCOmQB6E9glATdFKjOI29gtAxfSyoxk1D5laX3WwOTi64K\nvVbU3X4lRpJq4OgylAWns4mfdhLZpRxOwNmv2fOdUZmKGC679RsnfLLsp6Td\nXLF+1ckUxApKC4W6s1bkbztax9c2F14euIN79rpFSzdq7On12O3iboEyGZ2X\nnTk1H/4HyDPay6F03jeM1271XT02BuDpinOABhsXHw5y6vLsWrSwyeYlEFA7\nvxwJui3MuMNZouf+Td/jq9KIjnwVueaH1KdHdbC0S0NonuCX9IwdRKKeLYIp\nd6HJB7Ern/BSsnqAWdFZagaHkVEbSksoPJ1wSKMRNm6DYvEAwE9PG7978xgA\n5SS2wdO71Fg9LR+IM1CMMUJzjib7tgOr2+IsEvmzn+lbitI03n0HjL37hNZH\nWqc6\r\n=syJh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/plugin-syntax-decorators": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-rc.2_1534879473962_0.3917219346128227", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-rc.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "42fd21110fcbf1c0c2d23f9409e22f0cb0ee15ba", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Y3c5zv5OwLDImQfPFhrz2F9onQTmt+X9wkUDhejNbpklhHReoimq5+Sx27Oarc4HLGzoykAJJMcEwIHSDdYbGw==", "signatures": [{"sig": "MEQCICKHt7ppJwSAJMHrrbQUlDduhk5Bkch7dOSEFrVcw9UlAiBrdy+vF9bLwpUAZtJ1UfpVmlivP1ISU8aTsSeqzoDqow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmbCRA9TVsSAnZWagAAsTkP/RcrLwFADIKL5lwKTw+a\nXjD+x/ZEyv6Mji1KJCyDc4gaQO55gU5V4hRqOVzg5m5bWaTW8JdCJDvvIQjq\nBedKMYO5Fi3EKpdbNSDIurLOYuUwhzF0/T3Ztm5JpdLTm5ILqf7lptum9YBT\n6nPa8lBkwZx4glqXUxrnkDTr48x1n1KNwVVzYs2mHJFd/7UyHxhZLtkqInQp\n+liCpjp4q/XdnnQLuxf1C4Iiy44kMFhsXDIUfmpvRaL0skp4Nnxzc+J4p3+E\nFH50juIbuuTYMJGmTuNsyjeTGo6/t3cP6N7c9k/bqIOR1Pilzk6rzTzdqi4T\nQps/BrCi4z9Fofqx8gKo9AcTcoWXROYy4uU66Linig+i0znu82u6gDoXj5TW\nONv65PQ/iOpDl/vG3eQRnanuCC34YA4FZGPnGwxxkQ9/1VNM0vGADtuKenAj\nkenvbC1wquvQJ2+0zfS1yMsqcCb/eZHkXE9FVL7qn7KdTPk260W6zADKBlUo\nrqJm9kmag723N8OXK1JTVCZ2oIvQH08/6kuBSrp95/pwclPsdQDMbnU1ASdK\noAcdXJATyvFzxHTHnY4JlgUqPGgFDxFToQDok2We1giUrbLNrJovykQGC5PX\n+1MwJ3QeK60FeOUJiSid4OiGglzkxPEbHrmwgo6fAHaQpxgJE4o1Ff11IGm6\nIYLy\r\n=6H5w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/plugin-syntax-decorators": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-rc.3_1535134106907_0.02518024635900029", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0-rc.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ea12172cbfbe057f683de14cae2bceaff70cdf8c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-9FFkR3nReRTepS4yAGZ795H1ks4Ek1tbjIkPanZJOoglzS/U9vC1KcQOSoy05gAn+T6W3Zzz5kuf6sRBEKv2FQ==", "signatures": [{"sig": "MEUCIQDtBlLBtWi5hbtr6XIsdxaMVtRNdYNpv/A7PrPpbLEr5QIgSmkVHmiG7rhJ+/agGjVUcybDN2xgWVjsQ8PDaaooRiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCp6CRA9TVsSAnZWagAADfoP/R1R90em8YA0XNRtHVSz\nfltqIEhsobtSVmYuePQu01cWAOaTwOpu0vWug3jXZqBsArvWexKZXZgEgqI0\nl2kC8R/o4C8ML3Naf7aV7D9Bz9kd+IbvoalCbQtm/04xpJ/nrmQnVNU17ifT\nepvbz8MG0tJlF0Z3+ZN56tMU/8/RQHNmAaK0crcCQIpP2KJcgx02q/q+1G2u\ngnpvP7i9FJWsb09k1v9p3q71XmCeafkmDJze7uBB0t38xHYpIx5zsDsQQHEU\ngBTlpXEX34DI3EuFuhntNU/BTNZ4thitLLsXvKcrwNlpYUlcY3WLyt4eoe4r\nxz96xuCZT+QQMSXxECVW33M18V+/NtuFbMNAwGyXWPBH+OoQj5yXWLi78EEc\npCTMd213aq4N8A0a/UhL2ORQRIL0z+EfptrmgFHmhMKIPzejq01BZBJS1JSW\nHboMuZBKOHpYRxb6DTOH/ca6x5XP7DwZbjyPOZ8pAG8P+icmgkGBw8yqh/EL\nt9nDwkub1raxk1AOra829AjWw9tXA8O5IxfJ5qYkYI949hOT3iRd+6f4dPij\nqIdUxLLNQsrclkrOTR3emX3M6AUAV2y15z12EVHvICN/Tz8nF/YxCvjI2rPZ\n8Lq64Y/6v3Q0GXkSjOhf6HokR10h7XZzFbEvMxBYPnEQbFp4cC1PlvqUlMQm\n/tFn\r\n=P9ur\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/plugin-syntax-decorators": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0-rc.4_1535388282273_0.8659090986112685", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.0.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "33e7e683ca9f8ec3f72104ed11096839d48df502", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-td2C+9oBYg4b70VD9Qp/Nzmktb558D4JlOyaDS/dFoHa7plIxt5dvUM+mIJn3Wh1Z6GXItygbOTrJxEgX19kcA==", "signatures": [{"sig": "MEYCIQDaJZDuLyiXMzcPLabNOQihLva6gPZrQjoXjXzCs3naaAIhAKlGMvNSMC4mx/KNsfmhv3D3JwuPW/NHP/0Gokqg1euF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCKCRA9TVsSAnZWagAAfUUP/1Z0NaPgvS+4IowMxvK/\ny30UC0kBhDlZkSeuk6OX9cpWqL1ayCCAKqpLwMs2/MZE50qsdK1/SEqRtcLW\njuoQ0ttfydYfoIuVjNK08uIehvMxGxaqPrOD8J4URIzUl250116C2EJwqpkT\nyUOEg25E8BPWyv9MhlnXxDONraxaLXKoX07itBfuGgol0LWWA7FqAq6sTNb6\nHKVYOfWRzUU/+t+j7asL8mxYxaM0UDDaply0l/6aVMy53LUR2UKzDvJqc+FW\nId9kcmDFleNNtAEyeXwSKdxK/gY+w7C0WTiGRz/f5swh1Xe+0RdfI4FyhXVQ\nuqt9mYQoFKcG6LtJVB0F8QZ14Ru7KeW4uEzv8R3tIZIKycWW8XVgd7XRJBtp\nKw4pYbNdV7KGVoFovw7vyjlacR+koXFoB22Z/GQq5siwxboHVzyL7Kh8Mh+u\nYzg+xwZ06USAPVTOMktnB2K0NeAe+m08hkQpkFhSE8h7Rq5fyrV3+XggvRVO\n8ULOvQumr9DyYjlwvCuKB5AyFiZKqOb20cuzD+m5eI/WW/AnJNsjMkYrZX0C\neMycnVr1qi/acIDasEwABDUENpSRSG3GQITKypa0gEWz4ndOQ03Ci5d5xFsf\nwt2B9kAjxFATENtu7r/uN81v9b98GrX6BNQgg1ng3/n5OQ1qnnbFa5eL2uGH\nkjKG\r\n=QqlS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.0.0_1535406218213_0.4948284106751093", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.1.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bb39ae934318e4560db2d724b0fca8da0299b120", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.1.0.tgz", "fileCount": 8, "integrity": "sha512-/ljbdikH1lPjylMlvq9yQcrwFI9Z0zstHoihZ4/4BKuv+Fl9uQoY9E3wxv12wkQ9FPBg7JYNt7uRl3Kiz0Wnrw==", "signatures": [{"sig": "MEQCICU9cQQFCz3SAlWq40KKcOn/iMokJGMmR35ZXjEh1VZOAiAL6G3D4eHG1Ds9xAyUwGvF0VlA+/oMC1v3rVpYjD1ddg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAEKCRA9TVsSAnZWagAAOnwP/j3kOXRy6ze+bGMNmO7C\nr16hDvU+K2fFpbVDbIEZWT0ONm5dFhpsNuXM42/IdMpQaykXhImhf+JmDYgb\n0kEqmR6jZfgJS6WfHcjPRY9PB8DtIC9win35A2hg2ILhvrzBqzsTQSYtOJMr\nrwd8at/KCZhKvIpfe4R/WkXjVt9/tHEaqxIY9e3TLl1UQ+A1Mo1w7HywDKvk\nSSJ/tva2NzljFWgKEX9POd3FTtiEkhjK4J/hnd6lPRofyYgK1Fx6wrN4aHKr\n9afYbRak0Vdr1XTDvAnkLrdQU5uSzVQuOkf6cJmwMWe9jJoCtAIPDmNRmHB2\nzpkFGvRd65i+dos2iYatN3i5PGVBUa3bAmtDFhIVfob7yHWijTOgNDDnlfz3\nIaSQNJZMWNMsWL+5lcWGRhEccCGPsuB8atwridkXJpdQOA5LKgXGDSmdaWnX\nBm0396BjKYbAxlyUi8xhm6DG5gkRX7GyRgiaw/9ktjRUuJ8ZXi93g+xe8N1c\nQxThsQEAohecjhx8VeF1t/qqd/R5a+3lGjk/WS3I2JTqP4jxgU/xFj/xJPdF\n39wue/F+cjl5JVsfhPMvXrcGN3AZPOUw/QsYFizFDXTq9BvILIkzqGR1Kvaj\nrKGxJmEaHlD0eiB11ZqsqSHA8PnKVrm6fpLerv7TbvPRmPdf0mjwLAy0nciE\nebmO\r\n=GJq9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/plugin-syntax-decorators": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.1.0_1537212681870_0.5907415751706209", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.1.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.1.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3ba51fa121f1694308483655843e19e6f86337a4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.1.1.tgz", "fileCount": 8, "integrity": "sha512-29EHvdF6KbAm7syKEMbObDU6I1DwauObRJubsmTcCBt23fW0V+dp7uF6wf8fmwRDjXUupwickFNetAnqnY/CXg==", "signatures": [{"sig": "MEUCIQCBjjT5PZ8T3i0eNV2l58+HZt9K7+KD3SY/tHCgjCapxwIgd74rp4+jPGJ1NFf0gUSNfJpHjZBtDHFajM4FJ2D/oOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrojmCRA9TVsSAnZWagAA6YgP/0FDqo1FVslOpz/9l5C0\nmsCj1nS/qhQFxxtTO/TQEFWxwtP8+5kS65kLykfdq33RarWIO2vZvS2cCRkA\nDAjkmIRZHuHnwDeo7agoLZsbTmZb/6f2RuSTUc9TG2YSMjx3TaXjHrEIuwe9\nvnXT3tHgInMKKsVxzjxsert5fG/Dgm4WFGlN7kmkBJZNxvB016ZULw7ffvPz\n67hp3JluKEeSZ0r3+irChdwyJJyL9JCObxrAH/HAg/jw6IGB4pWr9Ydo4ld1\nKzvsjGM+xTCwS2Ykn4w3hlJEkp2C3RH9CHLDs1x6KuBcOVTzNSBLRoICnlGZ\nWGPPUPWX2DvV6i3dqOMolnLpCIx+/Q/oVYmiZuE9NGVNJFDBi2aVUROVgr63\n8CsnJCim9u+Hx7OkmJeTjBrsudRNxAILwiPl6BB/j6vUREA1346uqretc8qI\ntgG0ZNiWmp9k69CoJwAiS8MoadccONE9UZbk7ZAtQrWVYpv29JCQxoc2FooP\nLYVBKo1JmEuMtseZcvt5LQSxfVaoDyGqp106SxnzWCVvQakDBmtazXlFlx1T\nwpe/7sqXrAtAXZryfV3qD7GortOyfhjVTf1u29B2Y28VKsPodm3/YtQyB3An\nm54K0zK6aHASjxe4V2TzJu9xVUy2GTW6ukyrhu4VwVoHBEo+7qteNhxpJqxo\nDVLz\r\n=G4MN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/plugin-syntax-decorators": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.1.1_1538164965294_0.8415523215968732", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.1.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.1.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "79829bd75fced6581ec6c7ab1930e8d738e892e7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.1.2.tgz", "fileCount": 8, "integrity": "sha512-YooynBO6PmBgHvAd0fl5e5Tq/a0pEC6RqF62ouafme8FzdIVH41Mz/u1dn8fFVm4jzEJ+g/MsOxouwybJPuP8Q==", "signatures": [{"sig": "MEUCIQD4eKzC40rGROqIYbVR+UjW8+lzt4Sg/X1Jb5CzZngS9QIga6WjzVo4cG12iByUHHAMAu3Mm0KfckzlPE2frZYgWsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrqj8CRA9TVsSAnZWagAAo50P/Rj6IBoXSxHTwLp6Vqbm\nix5cHkYfuGifNtpdJ7wBUuUmemYvlg5O+1mOksa9QWAzfipArBvb4WZ8DpNO\nwsYL1oCa3Y4pfxbm0NJynaK31sIKSzEafgT2ms/OtCTtCerd3PZjDggD6LMV\n7Ba1Jhs/ypQOPXOaCRVyF9qmq4GHe+dxMItt14fKTh5iM15g0g2H6cTshWwV\njqQEFO2Lr7nXMm52r1ATM78Imm8ZZUgYSLfpzwhsE/hzkwTDQxb+fOC7xyYi\nQ5Jyy1PZa+bYUy5np06L4kQjzPbGkxfK7SsgCIwSK1t9kJbzmqo7vARJssn7\n6L5xR6AZlhjaCAf9IRQquA3cHxyP3NAuQWNWonlDbBdOJNG0pVwbGXe5fb0a\n8xoDF5/E21KesCiZX0ab5zUrX6RYyA4ugr+sZS40SL2k0FVva9HJyFcHzFNq\ngB1nzhMKiWhCykBTflw7S09jxQ6ePXfV+9fB7vAC61JD4ZoIyYabbk8+TCzZ\nE4H+zQpPMvsocaI5uYphsx4Mf58pT2RmkB+06N2Qd7eqCMXjqPoVfFhkayMo\nE9W0URuy1t1V2FUWk52Z9QWM2cVz2gybQKZcC5CPOor5F6YpjsO0O3mfXCcv\no4qGktb5nz53VL6zEirwySVKdPiPDJT+uXmIhCthnm8qpd9tgd2/ysoAZIJ5\nvbK5\r\n=fYqL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/plugin-syntax-decorators": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.1.2_1538173180157_0.38473649196931703", "host": "s3://npm-registry-packages"}}, "7.1.6": {"name": "@babel/plugin-proposal-decorators", "version": "7.1.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.1.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "460c31edbd01953efe45d491583b3ec31661d689", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.1.6.tgz", "fileCount": 8, "integrity": "sha512-U42f8KhUbtlhUDyV/wK4Rq/wWh8vWyttYABckG/v0vVnMPvayOewZC/83CbVdmyP+UhEqI368FEQ7hHMfhBpQA==", "signatures": [{"sig": "MEUCIQCuAkEgMUlZwUJmkbfxcJ/hA6ANJ4tIwjKLEqj7F43OoAIgc7PP9dafyOOkwSJpXKsqaMJukoOX2WcCI59/WCddtV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6z3ICRA9TVsSAnZWagAAmVcQAJ+9DmrmntyDMJOOgRsv\nhDHeeIVPCCPjWFL3OMbZDxM2HkWK7dl/Y66isG3XerWuVSTW3XKbePtBsUcR\nUF2iZ+PBzFEafoLfSzs/untSCYVTIbSKtdemmgZVcBwqL8VXX1e4YzRuzdV2\nBeQYKwEq7qP3jvgvFUZnYyFRP2mwsH9H5WZKsMzSxaid35F6RTJPy7AEfuDQ\nKhCQCdSbJnqplwwYY1d7/cwBfjbRqBzXkij3RLL+AUdwDudwLatF6CqnYHZx\nqMU0ezH+kE6Z3MybNzSEPEpTguwcbQdzNSIpkrmQnZyTaxh5pay5915A54h3\nJDoKsvzHYbTi/42JpAs2IQ7O0ZL6PTpEFjaAPwUoZu7QcIqaLMEG9j6Pmchu\nqxR+UZjv21yLZhfbDH2istvL+FsE7Bo3Bl7czLgKecZYyrkTY8hCxcg9bdnb\n+B7tZOA4r9Q25w72yCPkOASEsrctlHD3+HRWiQQFdnonFP2hSSElSE0Be/qr\nAzzPPOhC0QCBEDFrdSlKK4B/XtBbVBfnccqlHxIaQ9gF/74mKCEdGMBv8l27\n5Vg25/NkBUo5VIjWmo437jYNjynEokJJZvxezwfCgafLW5F0c1RtGdR6PlxA\n102t8FupcfYiuNAUpIsBh0VQpfAwY4zpqlXI725+bnoZBcENlS/zuhhWlExQ\neQzj\r\n=/fVg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/plugin-syntax-decorators": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.1.6", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.1.6_1542143431715_0.9782690317408418", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.2.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6b4278282a6f5dd08b5d89b94f21aa1671fea071", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.2.0.tgz", "fileCount": 8, "integrity": "sha512-yrDmvCsOMvNPpjCC6HMseiac2rUuQdeNqUyPU+3QbW7gLg/APX0c/7l9i/aulSICJQOkP6/4EHxkcB4d4DqZhg==", "signatures": [{"sig": "MEYCIQCe8oZcwwrnt/HtISO+mS0k3dMZZpVt1rhQy4utp8LnewIhAONaUYYTaMDzWTWMqZL8AtZQisDvzgMuKV/TGrPYZgmG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2+CRA9TVsSAnZWagAA350P/iOccyQedPXjEjzbrSfR\n+w+Iie144jreV7emR6Z3fU5UKgSBmFJEAMkDBXeif0oIyQreOXedKcsB4itc\nrVOAn9Bi3UtM9VFMARXUzUNHriMLY80TUVAJw885sR/ucQSMskeTKPOBfrFV\ns1X2uX8ulA/cr8YEKvNA1JxIiui7fdgStaAeKuV27L9SvByYStC2ZEiT1Tt/\nm8ILyYZk5OpsvsDzIOC9I4MZCWsuYYgjZqjmsSL/2MyaJeVj1UDA8xFNe5Lo\nr0d2YvyODES1+Nh8LPKL6ZOFzAiOXtWkRY6XPqjdJ8xtyw/ggPPFpemzEcWu\nKaPEELWNRJmf7ophhnfqiLC0sk3Af5SgWzD8ejqt1A5umTTk8gk/zcrHCgf8\ndIt6RYV5Y0DK9LUwxNQ9lIDu+jTz/90FRIfgpHshKGEAAtokuizot3KUblHG\njar+E48zH+oYQzDLelKrahRSzt0pq6u9Jkd6B8Bh1sOuG3QfBrFTNkkfTPQ/\ndNwhG97PLLDIlmk8bBzWwIugvzulYrME0AVnPNXH9lu5nkJ/roJfMfggUMsv\noY8QKVn0Yzsg8vkuCvSWazooz9ZULDAOdy1X9lLMJRbfbmnNJvVQjxW+Zvzr\nkDAd4K5cJPLd8AZf2qtWBTZbABuQ+w55LQi18WaJEUT1ivD+p9xO8ZE7WKlI\nTocM\r\n=AXnn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.2.0_1543863741537_0.6198974233245051", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.2.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e7ce762732760542a5268a49bd19251f06dccd08", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.2.2.tgz", "fileCount": 7, "integrity": "sha512-ylkKcbbS5IUocCti0gk762LMiMODWmlwRzyj1OCbPa83vSBlTKLmeBo9KMhoNpydibPcVwrP5rYJd0cKTVu7hw==", "signatures": [{"sig": "MEUCIEt7ZZFVnxI8GVktbEDeDX2Ti7M7NauJEDZGoF+AzSLoAiEAnBH0JmMNs0emkwaYMFKRFzrhXRzXgdU8LqavlBm1PoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHzCRA9TVsSAnZWagAA1V8P/2T7scU44mXcXPXo6X/M\ntMxlOg3Uew/Sy/pp2/9CeoYGnAYB3VH8Yywmqwue8WYUHdgraFp726onVHsf\n4kNDj0shx5ETCMdptssGble9x0NhX9HBaz5QktGGaFu1gr6PY9PVYd42Cl/0\nOcFqraUi99R+A/QiPP17dwVzcSF+YdaGRCmj7ln86kmgT78sgVQs72rbbQNO\nuMZlRAPFyzZEIUQgex/jYz+9SXYs7oPOsX7biHMm4bOdcWeG9eCulhrPe+0z\nRhaPJUBi2UnVkmJb3vBJGkqWEarJN2/Gvfvm4SUrWgMMbE+R1xwsTBLheGVq\n+3Ym9Q3zDrfKSzhooX1nfaYp8XTsYjyRXqoosRB+YHB3lt5dtsblJLmQdNzH\n4gd7xX+RsZVfB/EtguX0Nvzy/4p6QFam6jwZwZjmU1X9yU15UmhlrT+UrRlQ\nLcuQqLIzTzEudpLT5RQ1c5PnQflpnyEEyHab8B2DYV5D2pzqCcq1CaaZ1/ql\nGVOzQWum9dWMVYfbCmnHmETK+7kA6c+F/Ore3aN92kJqhB1NeG4aNkdawWwA\nu18qc5dXUZ0N1a136FXD6MY+0iMBV5we+D3hOgF8yNn/TsmALlr7wza3Eclm\njtkOXVGIeaCrcipsQ40gvX6Qz30HveH+QtU6cASPL3vni0jmg3wS57fGr3HA\nVPqg\r\n=tDAu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.2.2_1544868338814_0.7861810682956965", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.2.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.2.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "1fe5b0d22ce0c4418f225474ebd40267430364c0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.2.3.tgz", "fileCount": 5, "integrity": "sha512-jhCFm7ftmue02EWIYqbhzP0iConEPsgVQeDriOs/Qc2lgr6MDtHTTrv3hE2GOOQDFjQ9tjP7nWQq0ad0JhIsQg==", "signatures": [{"sig": "MEUCIQDjt1BHrc5V7ueWHcTxhOBIWs1UTk7FnPEDcvJv8XAgSAIgD2dngh+7DSextFqG+pPU/iuYggL1CGWfcj8DV9VaELw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3lXCRA9TVsSAnZWagAAch4P/1OdlEBxuT7fSpfUOpe+\nkO1C6b1yhUHn0+IhHUs0bajKt9hwVLwj+3YQFRBm4O0zcsvq+9ZE8wTzIelj\ntmzmGiPlqwsOno3gR2zgiFmwMM8Fi21Hpgiytv3wuYpdls1po6v7vOLkRLs8\n1jq7CHpTDu/1A6RldJJmCXQsMcwvE6tVe8rvoewe75MHyUMtUcZVd81gGCNA\ndWVU9A6GFr+zgC2cvSPdRf1bp3+Ixe9ou9BhD7ofyLI+b9uFI6jPAz0BlYgb\nHT1cQFemMuEYeZbwlxlyg0IIVU22ZFh5enW4DRjxoavxO66YOlJsyx+khEtR\nz5rl/EAK79K5l/+3fRWdMHKq8EtX9rJAM/yoirn630LWEeVmyRz4MjK1t/gD\n/HUGkMe4ikRmRqheY92UGfqtVDzwZQDlkNf9qixP56VutTOH6Z3CsN8f7qL9\nzrvWCmmQ4//1lgXRAbCZkNNDqOwmjNe33tWvB9wqKTXlNi3TO6GiOftdP7a7\ntDtoJpmd3BFetsYwFkISOUK4eeNCgr50IzhS0q5W3tqM962G+FrOKJnFCqJ9\n5hr3o9A+AxEbtwFrbP0uulj3iz50IaLMowJ9SsQ0OJrRBtxw3Zi2dt9FyaNQ\nzAOHOeZ+ESW1snefeP0hAYV6oPaoojCCz60IN0lnC7rDC9dq0M/9apnKouo0\nFPWm\r\n=pgz+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d35f2ad92b322c3bb9c6792e2683807afae0b105", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.2.3_1545304406734_0.08193573800390297", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.3.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.3.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "637ba075fa780b1f75d08186e8fb4357d03a72a7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.3.0.tgz", "fileCount": 5, "integrity": "sha512-3W/oCUmsO43FmZIqermmq6TKaRSYhmh/vybPfVFwQWdSb8xwki38uAIvknCRzuyHRuYfCYmJzL9or1v0AffPjg==", "signatures": [{"sig": "MEUCIF0CaeCIGzEcd8uP5AKM85dWDWS/b6lkU+CP9v2NV8ocAiEAn5SrWxoTdjgB5mlaCJ4Ye+eUzyFk9v16uxNKdt9PnhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjviCRA9TVsSAnZWagAAwvAP/0dWfikYKi3B+wziwBHc\n72HDiIhmcSNhPzRQLSBOo+K0YqAA3ZgqijGIi8/hfbbccPLhdIIvdhTKSLmu\nER/sHsTR0asBdlcYY3YemD9KAz7bGqkzwizJcH4N57o0jcGvcykvaL5ApsMu\nutk1BRoIdnv0UlVQ9kgDk1LMrbPxKhzKG57zpUcIM3VMS4DdFwoeiEMp7F+j\nP5f+Eaha4jRRJeVqkgJVhO111PZmR3ueQig7Zy253PAp27nNMqOtuRWkz6cA\noSIGhIGXOpfZ/gk4ORUe6W1foavouTxqzlL3QQr4qTH8iJEhAcQ3oAXiInpR\nFis5qD54YdYxUuo/hzt1SXZLuWc3s5UBA41DiNd5LWga9ooGQMq7yfwMeC7X\n/Y3Ej0cKZ8EzxiOb3DqSKbiDSqc3FCDAYlsqAxTR9h3WGYH69oaZBq4Xy6/B\na7NUD7GcWRHmCtB3dBCzrWUZ28EKME19Eupw+L7fldG7hKcC5Rva5iBxFqbN\nEA3vzjhOfSu5s4OF2s63Q7oapudff0rk8S0onr+V4umpHzBrUvHa+8brYRed\ntxyn+UKvYjHK02NbQ51uEQsYA5A/svtEmBp8TWd2ZjdLjL8YV9/u/hyyAPyU\nU2djONyypwxxOjPKSuhoeRc5YGrbRNXLRh9MTEft4ZKoQQmToi0TiAScenK0\n7g/F\r\n=EQRn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "6.4.1", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.3.0_1548106722282_0.6393652804471182", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.4.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "8e1bfd83efa54a5f662033afcc2b8e701f4bb3a9", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.4.0.tgz", "fileCount": 5, "integrity": "sha512-d08TLmXeK/XbgCo7ZeZ+JaeZDtDai/2ctapTRsWWkkmy7G/cqz8DQN/HlWG7RR4YmfXxmExsbU3SuCjlM7AtUg==", "signatures": [{"sig": "MEYCIQDZ3rE1SRXO6zY80r/7coKhv4Q9mgQiiGSZqA2OJ+wKtwIhAP/dzrPwWgvfaZ/B0e17W2PofP3bxfimLtY4UZVvCNFM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTqCRA9TVsSAnZWagAAa6wP/11VQ7vVZYuZARjpTMK1\n+FdonAIueFlzHsnqQHTm/WmLmWXBjMjgr4ZxmUMh6XRYD3g1Uszqpi0HlbsI\nCGgwvlEh0Z5YYLiQUTneJ78C/6Q7OZuFScb5WXwaoDGGodG9w1PbwOIrBDqh\niUP2x0FwdLl8YdvY4trKGtTv3ndB5SWGLhKyKPV1B7Egzobn9BXZslcrrO9i\n6ng2SozEWKilNr2vvU320u5NDPFrY5ABJa2i3NpdplaCKw8DlxyimHNd2nrR\nLabyc6X5PEIuz9hxjDCNPVYbv9lAB5tF4b8lb0ZAVlENbgoL26vJ5w0kOojm\nn0PnF3slr6thcXRqQEFMdmAce9M9mzuMcRm/qsQuGuZNpVP+SAMjhI1/JEqO\nRsH5UJ5GWTMlCm8UiLjLl6mHy2qxQOouQy2Vt49nGU4cKcYeUVe0NPAXdfn0\ntXoS6kh8ffUh/ezdD2ishUEeNnzmnYViSAXlQjTNr839zW2Jkx0o/JNqpt+3\niN+We5WBQyTfvA09Iv9vCsu8Z5AllGlWPneZi3QnSR/M6LIfgNaBzwrpuwrr\nEMIcQ6T6GN1SuZbIBkVFAyZjQ1YCl8Yg+OwLg66HldAZDBXQfdj2TaQXPz8k\ncIqdeAJVTznR4kyR9gv8kJMAX1m6Ez/+xoZe+niiDYsxqFmU4ybEAkNwcEuM\nv4X/\r\n=O3Og\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.4.0_1553028329933_0.943814106649375", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.4.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "de9b2a1a8ab0196f378e2a82f10b6e2a36f21cc0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.4.4.tgz", "fileCount": 5, "integrity": "sha512-z7MpQz3XC/iQJWXH9y+MaWcLPNSMY9RQSthrLzak8R8hCj0fuyNk+Dzi9kfNe/JxxlWQ2g7wkABbgWjW36MTcw==", "signatures": [{"sig": "MEYCIQD6EWk4h9N5167uZTx1MAuiR1HUNgDkLW5VOkUegDZtkAIhAJMKonjNHebOiTtb0hrSVUIwWIItva++bBuWjCo6ptmN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3KBCRA9TVsSAnZWagAAluMP/j5jTe4YfCREDMDaPpBq\n/jFcOqSQ5HZnasLytcKQWRNXQoZqqsjgKXsfvJwrQ4Nr0YMu/ISVMXEQUW3w\nVDmWv4pFcahJkbLFtFlSyffOOOvbJcKT09d+HeueXy+iL94Upwjvd2p6Atoz\nZAxP4mLyuD8r2BqNOLFh+lZl+ABpnY7dFvc32xVR2gJoh5/uShZn6OQOK73s\ncIq0IY8XIOcKKiKG66y/PUpx+DkTPBpw2vCI+KA8NWfkGSlwfSsKPadEqnHv\nr2faQqTfu5zIXMFbp8/nR/dEjFXv2xGmuJqCL8fNKV8UZsXsRIIKhYscw0CG\nopmebsvmUH+MpCkUMQ3tNpYuW/kQJq9L1IOOkxb8dvJA7en2pxr3LgiMqsyo\n0ZK8eRLSeDYkBDSvY9MQ0mPZzAGMsJvx3pMdgfVS0hzUEsHqXSj2D6P5D8v2\nqHgcGzR64SV06+SbuQbu4dH1QuqVSFtNOmFPY04BdlwyPA9YixD2gXMh9wyJ\n4Bt1h9C4D7peKW7ijuuTt1WnUTqyg3a5SX8nLeoVyqjwjwiwKThAheHRZTvq\nPht/r5+vFEzKzjY+X1f6shGLK5WAIZsacjWKMQkC9gWzaWcRBcWZecon6Esc\nPhghTuMK60ljVmgfgbQrYCeevRXkPOnqbi/fcTbEjmhwyEbAzJ1RUok+QlF1\nSYF7\r\n=pEUQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "description": "Compile class and object decorators to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.4.4_1556312704579_0.377217134756628", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.6.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6659d2572a17d70abd68123e89a12a43d90aa30c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.6.0.tgz", "fileCount": 5, "integrity": "sha512-ZSyYw9trQI50sES6YxREXKu+4b7MAg6Qx2cvyDDYjP2Hpzd3FleOUwC9cqn1+za8d0A2ZU8SHujxFao956efUg==", "signatures": [{"sig": "MEUCIDJICggvBjayCPXxpxc+B0WzX3IgiQJyPP4sO8yJkdpmAiEAwrWK9fqEDqPpdw9SldGDdqLpCcXWuEjzNpmf9coRHgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcph6CRA9TVsSAnZWagAAs4gP/RWmaDXcv7z+hEDtwYkr\ndbyWN2NFtfrvMst3rM70vVzENVz8sDJy/jtJDpCLHJkA8wmFawFwvCWfH7Um\nfQq9Jzk6Dhr0bKshg06yzBJT16Yk27tC+hr5dTOOJ4gWd8vf2DkfN6QzR4iz\n0KP+OF24fBUGahzRmjVbDc+fli1Pe2fRNh3F26Jso5qnguwpTBPYtxxCtx20\nvM2MMcZN7RBvtbAqERFei2jDkqEh2bcrsrCf4hSH8wmadx7c31KwWhE5i17D\nFmBWG7MD7+Am63mu/TU1i2jxWU72Br/YYiNdtY4pWGFpNfAQ070qUKD/ISNO\nT1J7Qd3458hsD4NV1HrBGwa7mO0hpS1mFYENdHMntS7ewz51BG3vqoW3FaX+\nEDHpD8E8unZOmrSRFiDv2es+/CksxR7QBwvJR7gw5bhRBPOk05KkAbagPhH7\nEFCnYNVJXD4HGk3cx8f2DxgeExs8vNjUgQ4cXhBUkjduXPCfyLehv9r4mISn\nwyZBdOsWsDzcCwfLqWf8Do8xBQ4iTMrHrBl5T4SD1RnoZdR5aYbPovDEsj8k\n6VfevL2X0qFxH+Dxn++8OydwZxw/6PeaFBlp0X96gNs8e+Ox49nUCCz4wVSS\nhdyn+33Yh2hAd+taZzEzBK05kZn1pxFNDxbX0nDW2fX/TnSAUpzChnJvWxe4\n2T/z\r\n=8Gei\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.6.0_1567791225875_0.7798310960927279", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.7.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d386a45730a4eb8c03e23a80b6d3dbefd761c9c9", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.7.0.tgz", "fileCount": 5, "integrity": "sha512-dMCDKmbYFQQTn1+VJjl5hbqlweuHl5oDeMU9B1Q7oAWi0mHxjQQDHdJIK6iW76NE1KJT3zA6dDU3weR1WT5D4A==", "signatures": [{"sig": "MEUCIQC6+hiZYrBsFBM+MdiWB/vHP8GF8bqsgGurs4GpIwmbWQIgcIv6ZcX7LqPykSdYiUvIIS86Z8B85yJndBDz89QCF7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTRCRA9TVsSAnZWagAA0oMP/iwJfyRnveTMB8dt7uiG\nj4IqPY8xGEMuNH7kVedXRlqdc8wy3932uFJ8bINVWiFHD+gjjeQ67ETRro8T\nughkIWnIsZs1eY3lqo1bjR2lPFVheqb2BxqdqSC1j3QFXNvKwlY39VHxepQm\n4tUfY//EI4L4JpzFoL4F4puglQ/GDAVpjwPJfqXQsofbg2dT0dd6OxJTPTRS\n5QBkrbKBOTqOjg12JKQ68aU9i856KntqsSsJGTAPRPXKAL3LjsMJQQtKnkDh\nqQ7COtWKE6wapFeE/MaMVmMRzbEQI+QaPwGji1n1mH5wzBSku1dDRXa5pWjv\nIov0zPlJNCinUMtMVJE07Od9QiJaEJHU4SoDK3mTQMpZ8SZE8T4dgdVXt1+X\nc8BXbeB3mjm/gAOlmfQLsrjYYOr9J+bqsTdyZtP8JSNjq8r5ZmwtPvdDI+dD\nH+vhtIuU/j/WYY7MWEyjsNOWaQo7q5gikC9r6m32GaWiEvDNHrlMMfXVLjgb\n+pWfOnPoIXemMxF2g/7F0eWkjg+hozuB15ag+gb4yFYscZu31r/5EShGOmed\nZ43Cgw3e/M7Ig86CPJJvihVwg00ssUvR4iGhvJ0K3TGIF0JbcDVB0ZQ/I2ID\nA03mq4VHkJ4uRQApo6cXBqJgfMtEojZ7eyp7+fU/x6eWCNRFcgP5u5djiKeT\nReP8\r\n=xaa3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0", "@babel/helper-create-class-features-plugin": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.7.0_1572951248562_0.3875184186815397", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.7.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "58c1e21d21ea12f9f5f0a757e46e687b94a7ab2b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.7.4.tgz", "fileCount": 5, "integrity": "sha512-GftcVDcLCwVdzKmwOBDjATd548+IE+mBo7ttgatqNDR7VG7GqIuZPtRWlMLHbhTXhcnFZiGER8iIYl1n/imtsg==", "signatures": [{"sig": "MEQCIEhw0++kPzwQ6vquqvnCvrq6Fhy/Iwaz6CkfB/nW7Te3AiAlHh2SgLi2zBbYVoS0Tp7tBdtqimLRWblSsh8jHxyYmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBvCRA9TVsSAnZWagAA4JEQAISMw0EN/PLgZe0E/jJ2\nuKPIYlDLXDhibZOLIdc9oLbmdyo3HwKmNa0nlvouekB1lR4Nl06c/S15cZEa\nvB8RwNgbyt52lI3ym4AAS9EW+JdHsKODUB1zpLqm4DIDnkZkeNT5RfVLb413\n4vkhai79SRc5mLI0xpIWA26zbOPSE8gwgF2mPgOw0asMnbm0X6CEJR9VIWzH\nBN+8qSCGfxeqILFBduO5ItsAM8QPmrppIXahgSV9PrAAhBidH6LndsaxYXTL\nslJA1DY0/MvcTKV43GVK+0guztCffot5e0+BO/vlntvBKCGOs4MVjucObCtl\niXfGY2icXcKpXr9cJU276/3zc49foKmSheBNEtQpG3652fIWrB8rsVksh3vq\nAdRuD6PXE7M55b+jzRXPECCXep75wKNHCWb8vJx9bANhSyMLIt0IpQ0FD1LR\nsYehlCcgy2DM6yPbNLS+Vgk7RjCNJImwxVwKf+mwoAgXmlm8MRBWNC8GqKVO\nTYx6WYEQFrXbSFhIZV87GoJfZVqtFMIgAK8D/zEk5IRG+epvUEX77OGFyh41\nQfnLulCANgAUI0tOE1pxlCU88Onb1H/1ZSY/ZRsFWfqWh8MF53wDh8vtDLff\nwgY0LDsGwvM67r8rfIgYp3EDJyNNPqrriKdhK8K2CISa20civmAFKy2jiT5n\nMoJO\r\n=6IeU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.7.4", "@babel/helper-create-class-features-plugin": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.7.4_1574465642255_0.9628456322322594", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.8.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2f1661712c557de8bc65c0b58876a2b47ff886d5", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.8.0.tgz", "fileCount": 5, "integrity": "sha512-HKltBhhrRigrHYkDrO/9rg+ZerXSAtZjepm8URUrNxgzTzEfuOb06fUU311chMkahZHSfASNUxWVwRzIwGt/uw==", "signatures": [{"sig": "MEUCIQCN04rVao+t5r9pkrZ9uVDWPU+KrLh666qwzmTxDcwOBwIgVKFlYg8foueOsRbxnyC7+cdyovBT144WYFbIYTHlmvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWkCRA9TVsSAnZWagAA+ogP/iMk3/EV3TcJ6+96J/FR\ndPviyBF+pA5Yx5W4izhRg6RUaNKJAbV0saRWFUgHYgmUfwW8ygjfxU3jhYWn\ni7ZDyrJjBnYxRorDP8pi01HyUUXBDAi12HxUBTW3P7Uubqf9ezxFIBZucqWg\n+zHrWcvTcVIKRG0vm5i0rPI+diHhUuyfoYMyOj/HWrMrFKwWs49fiNOC+c3C\nXBb1rIaEnOpxTSSjV+WR8ttX07Jg16GDT8mv7pmHSb4RFtGcNj67KjEGfGwJ\noqKNjKZCpbw1hwxSytAYg521itPT/dodxOuK5SefS6kyT0OxoP+n/5x9dom4\nXyHQv7IudmNfiz/q5qMsVtfdNsEKkN3A1V+VK6Mn4bq8jY5FrOYehYzdXcuy\npbg7AD75Iy9OVZ+IgOqoT8d8CU2loxiqrkejQGaBAecMKgQwe7qHbr/L5/I6\n12Zi0ovHXA8EZB+PlcRKlYWNA69Qf5p9jRnOuet3wr6kIHVJAsZ2/Ivs3tBR\nAO++/bZnC5HpQ94XMSzTdOo0vZfBsX9ESWmK2yV2FGQxeq8ubcG+dSRE14sP\n4KkYVnG28JeOsNhEOjoZMm9hX8ce6QNP83GsmHqCS/0En6ZVZpauWafLOGX/\nbzkzckm0YJrS4ueNn5S0XmFNN1YXGmgY0022WkIygNfpQOpLBhlWRuzOvLax\n3yXq\r\n=lDCL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/plugin-syntax-decorators": "^7.8.0", "@babel/helper-create-class-features-plugin": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.8.0_1578788260512_0.5861227422269049", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.8.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2156860ab65c5abf068c3f67042184041066543e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.8.3.tgz", "fileCount": 5, "integrity": "sha512-e3RvdvS4qPJVTe288DlXjwKflpfy1hr0j5dz5WpIYYeP7vQZg2WfAEIp8k5/Lwis/m5REXEteIz6rrcDtXXG7w==", "signatures": [{"sig": "MEUCIQDxN2qasYAitZ/SkR19UcViTpBZveiH2x2D5HxOZ9hZxgIgYjPYqXF8F7+v109zrFLvqa7zXBbiSSmFIa1gQpOcsp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORVCRA9TVsSAnZWagAAsUgP/iF0vkru7UTO8/84JmtU\nuXSJFidSD72te1y385t99+e85IXS1OWo3PtXlg3GBMY8jZc78tGjdhGOwdmM\nN1ugtbkgFAwI1+BJt/A1gxFJLfa+NOBv0RJlq6rcrBpkvfIgBENqHeImVNGM\nh8aNMK4iXelSN79+CNsSKic+h2AD1ie3nRGBBlKz3PPYEMhenc/BUnC5d3aU\novhY80D1vYKqkz2vQJUta0eAZlOYpZnR5WP+WZowj7fcgLZeGRjsDWZ2rSVa\nOmKUNHNSFTBLUZvEGyypWFBFJd0zPgfHWfl+z3eKz59Y9fNXBuV+UO9s/eJu\nwEwZoToCkSpUr9nWULIIb7qVrQgXiYCEzL5DtVA/qm1jBXgQ+GfCxRnCwGR1\nAAH8tJMlCJBebQmku9wrminBnkhmDKctF3lWCYTdNFUykm45+20JewjymMnF\n7DANdL0kHcFL9roe5ZKd4xX2z+FcyoHIJfbsjj6RCB31ZBE+ioM1rYUfx+d/\ncQr02V5F63ikN+3/OkC/SO72asA7e8OxMwsbg/N5hnlnxkCYsOakTZNT85Nl\nQBGVS3A1kDWCtqXycWv62duIcfFpflkAq1t9Fzzbrp2d73FNRTFfxa8YpjTV\n7hQnO1dM2FOJxdhlE47yJZr1595/RZev15En1FYyTbFnXYkEaE4PLUSeEoUD\n22zN\r\n=qv2m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-decorators": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.8.3_1578951765365_0.10607075108569175", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.10.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aa1c82288d9af1f2a5bc759e5dffbca8f8d01ea1", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.10.0.tgz", "fileCount": 5, "integrity": "sha512-PTlxQfx0fZjOYlLe+gAhpb6Lph3zr03lpzqnzI8bWtcxDo/98rhO2adxe87F7OHg1G65nXxQ9ChPvB/0A3qSAg==", "signatures": [{"sig": "MEQCIEuZm3iGEjyMOYr0I8OJ77bSiiO0zKqgJ93n/8GNoYLLAiBsG01I8Xxk1yn4CedeU1MozwH5k/x2YyYghF06oOJ8NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2ZCRA9TVsSAnZWagAA4E4P/1hjK5c8pfWOUqQiMCsO\nsECKGj0clVPR0FBg5Bux8TUyCMlMWTS+3A+k2sVAtpDhni945xReL5X0rz6/\nwqgwOH9UBOSjeyI85ThvQW3Bf9hcOEIKFm1cw2bXs5ELIBVChi7teVqz+FQa\nYJKW8PJYAlvR9U9ZZhpT9sbskeG2rwA8nmyy4oIr6FPn2aCdChlNUnyvxpeG\ngvVxGDOOwWLI+CcFopAOpHFYgBL/Kyuw1NIQojwsiG8HmmB1m0sYSC+M7h4V\nYyL0VDtg3E5C7OG8eGfzmNLqO8tluWQZNEZyaJQpRJQHWrvgLRXi4tWW/6IW\nvQ44Pp9D1YD6wbxvNCigZYOLzE6SEBM92f9QL8fAGxGPCRg11xmBbJXXJj0Y\nP348Y5+wUqSKGhw8w4P1dP+eEUWvU/+cNz/TxXkySYekM1X4x9OlWgczgc0W\nl+etrv33TUVp2rRgtGOaQ/0aYiYcaMhMnfvOB2IdJiSO/2PRRFoc4TPnxLin\nLfzoPSMNWrUH49FF+EIIOoY0Pc1SS0m9kL5rGtzbps5phdsaiLM+CVEva5m0\naO5QXnKGJDdn7nKfeFl+Wb8QwIAITcG0SYjtWeEavqKKGJUvn7kGeViDd8cz\nTUIPSep/gXqCaOTWW4eEubS+y/UnEajoufArGn9B74bPh+Lv8/wyGdeQOd3U\nhWAd\r\n=Ewy0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-decorators": "^7.8.3", "@babel/helper-create-class-features-plugin": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.10.0_1590529432749_0.6480932594732565", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.10.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9373c2d8db45345c6e30452ad77b469758e5c8f7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.10.1.tgz", "fileCount": 5, "integrity": "sha512-xBfteh352MTke2U1NpclzMDmAmCdQ2fBZjhZQQfGTjXw6qcRYMkt528sA1U8o0ThDCSeuETXIj5bOGdxN+5gkw==", "signatures": [{"sig": "MEYCIQDtnOMnl6QpRhJXpr1M1pqmCBy/OzyWfqTJS81sL0sgPwIhAOL667qE/0oAPksC51yie2lXGNTF02sR/GPoWvsMJlEC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTiCRA9TVsSAnZWagAA9CkP/ipZwaLBgcP01KvHXsr5\nf3W9pxPmQXXwVxZkJgaGZ6ve0QBEIKqZ8MmcCLKORWqcgMZfr9DmB1Dq4e7J\nDhzyhB5y0tEBOtUA1Ztl2x9+u/xGIgEWPBKs06ojDkOLx4q+l1YLvQp9YJgF\nAUxhwnjTaTeQRktJ2Pem2k9ACvQmeptzsqDQ3437cW6qvgoqPNNfhCJAHvqx\nqkFECDVrFKn38xlzbxHCip2HKZB7G0C16ucU/GHMv6wYjR2sZxOTblVtO2lN\nV6L2jql8MDgMrJY3fUHEeZRk3LkCJ9YsKt0/Is6Tf9T0TGLuT6OIam/mxMHN\ndqsHtUFfxzVSqd+s5LmudywWc9DpSLBvyy3XxHTjbPvT9V0j1yFqKLcVGAzY\n33l2VjJk60zPCIlmKJoFyT86jgw6Gv+p+xVr1hhuEE5ra9W8Z3B9HRH0rjQJ\nAxNSBZ/auaEyT37m71UNGSqi7+K/sHJImzPTt+Nu8AiND3mV8MjVpDkhojxv\nny+rGUXF5kvNJ4iEoymAWVId1/6rifWbr10ybg1re3NXl6e8MmpmzOkaIjb8\nxyc+eCwyWC//iGj4EsCtJVbTpishcGVP6cq5fu2/qOq8I7xOi6+FE5cRsUBl\nnvlOYqw5UGwxklsLXYDx99tj4JLAVWqggdLU8J5zeFoi/AdikOcGC0t4V7Q7\nXj4N\r\n=UgD6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/plugin-syntax-decorators": "^7.10.1", "@babel/helper-create-class-features-plugin": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.10.1_1590617314134_0.6127176445527038", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.10.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2fc6b5696028adccfcd14bc826c184c578b857f8", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.10.3.tgz", "fileCount": 5, "integrity": "sha512-Rzwn5tcYFTdWWK3IrhMZkMDjzFQLIGYqHvv9XuzNnEB91Y6gHr/JjazYV1Yec9g0yMLhy1p/21eiW1P7f5UN4A==", "signatures": [{"sig": "MEUCIGKm1koYpynsx8ru2Q3KgNVoeu9vuimei02bimHwXq4QAiEAo52jVLwEqkQwLMdwRGIhpemK5sQP/U7wYNmhi7Okjvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYVCRA9TVsSAnZWagAAMqEP/RGYB3huG+VtjXvdnQWW\neIewvQ2pNatzYwa8m4wz7Zc6eGxgfKa4eGRKabq/bH+OCmQ9rWa7l15fxj9P\nBn2J4m6GiM14g1FWlZM+AvljGpQT2Tj132JIdrGWiu55dbPH5PpjD5FiJlLW\nb9NQuxUVEg6cubAQa70jiSFl/D8YI/7pDI8rAxWU/BJ+/Xg0WNhJIclxh0ff\n38TzJkAHquoEY3vsZGilar04qpZVvYNao0ZpTIK4KGBLRw+93xWKDqAek2Ja\nP1JM7FScTj8xtXq3rqftuNbVSkCSfi453fYEdRTqKZfRBObZjWjjkkPPO7zN\nbhLPOzR8FupIruFY2owD7S527gJeK4rtI56XMCHLW/0Vd+2vSx2eP2t8tVPm\nIYgA53OpnEnF0iyi7DpvmrGRu6RyAkWcTzZ+i3e/tWamLYL7lTtu4szqQRsE\nMh1HLmHBwKQoj81fAMHz/22dqJ0PkNDy2x+HWIpcolPdqjciCM7yPlUZItBA\nnhHJVQVYQ8rvm1iDJv2YHCL5QOQLY6Bp1+gkCFUTUJGrU2nH/RkW298vukie\neTwfYco0VDG+gYE2vccCwCoZm3yO59zSAHQMclVuFkYndlanMe4j+/Q+Uz1L\n3QsLyLlh4SnLYgFiua8w4XU3R+fzMZ8StrtiRYgCJnf9eSawk276DxqqBIS6\n4Gyz\r\n=UHQX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.3", "@babel/plugin-syntax-decorators": "^7.10.1", "@babel/helper-create-class-features-plugin": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.10.3_1592600085005_0.8398123482274547", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.10.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "fe20ef10cc73f386f70910fca48798041cd357c7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.10.4.tgz", "fileCount": 5, "integrity": "sha512-JHTWjQngOPv+ZQQqOGv2x6sCCr4IYWy7S1/VH6BE9ZfkoLrdQ2GpEP3tfb5M++G9PwvqjhY8VC/C3tXm+/eHvA==", "signatures": [{"sig": "MEUCIBL3NJcex1Wq3Vnus5ougkMJRGvGRK1s/4ZftECAaDVjAiEAzfLEUFn0JA+mvs32Q0QKBhCoGRpK80TPwm7797qAnpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp9CRA9TVsSAnZWagAAv3kQAJYPYNdXmcsLd2CmCSKE\nNzpkqXlsW5qFdITigd4gP2+DFCbS6TyLhIjDyztC9fEN79U1yGcbOzPURosl\nQKFNhbt5qmKkG7k0GURqceTSvO1uIGzdcc0zR9lEKjsfrDpHoilP4ERb2Qu0\n1JNEh5E/cA856uH1du7c9FFLw0y2wVYtpLDjP/31ebez1lEyRS+wiUf0tQYX\na8wxIg1t9oWnHUX/y2++kWzi+Xw+PfgzDW/kircmk1ZRwLF+SpOcK7PhJm3k\nAaxHeOODVo/Br3Qtz//QvV2wkmADEkScts2KVJBosbOa9cykFcPqBkWgGD2S\n5vNtQlefB/SsY2903vNy4JWJqsUh9jWeFPNiKc5cz7aaLOapIO9QohOb1naR\nqrdlGg/8Lk+XMJ4WRbqyPVj1NBvn81fUxfsRXJk/EB4xYofv0Npq4kyLzCA0\nzAQRcr4tRhMhQEcmCWUelm5aa4pM+b6Pb9g+93xDfb25nPRFIvc9DP8OrLRN\nn6bxJyT9o3A7Pdb7IH7+yOh5fNFCjeFZTXTOjoCyHU6tJ+i5TBLS+/nZT2uh\nQ5Pe2VKOI3vvbS1oUA//5GJmvIQyDDCrUXcjaPspmBVQPPrto2AMEZvlBgo+\ntGnbKRlDfXagizSyxMcvRo5mwZsEI+BbRNOkIz+X7yXcIUda7bau1X/IDZ34\nxiVl\r\n=Q6EE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-decorators": "^7.10.4", "@babel/helper-create-class-features-plugin": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.10.4_1593522812961_0.9610279775519914", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.10.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "42898bba478bc4b1ae242a703a953a7ad350ffb4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.10.5.tgz", "fileCount": 5, "integrity": "sha512-Sc5TAQSZuLzgY0664mMDn24Vw2P8g/VhyLyGPaWiHahhgLqeZvcGeyBZOrJW0oSKIK2mvQ22a1ENXBIQLhrEiQ==", "signatures": [{"sig": "MEYCIQDdKq6A1TUCwZ1IrMBp2/E0P2f6WDRr/eTswp+mTKSQxAIhANFTGa8tcZ0WormmSSvAPbvgOLZfVMu5KR+NyIiaWkKc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbuCRA9TVsSAnZWagAAnXIP/1Mzy/suoARXHa4CWmol\n0+hkfjKdzlGRM3w/nWGfY/9m9Ojxh3HsF/ZCYeXa4HuP0m3Y80OaMUsQwYlo\n8Ul3/D0RgTKjniMtLOt10vtIeZEuX3PJnobE7A+kfj3HCeWVMoiDhUduxoeu\ncru1mGUjr+Mr1F9bFrawjAnPiJUsG+dMy8wikwgz5i6sdR9BVSioQIjTvMGa\n2DlBymZ44W459ET2TebKrq3OJtOXNznT0ijU4GYgHpBm9NiF3icToCjk7GDq\nLvO9j/v61KoFX2+8ZkZnn/qvjJbG68Jp3PPTAG5nY4rFbXldyZandStzb79A\nXPqeuwqb0kEvA+feQOT1iL+xOoQa8N5i2leK0W/Nb42+3/USfLtJHWZJWedV\nrbjS2UtRq3QAh2FPo7qclpx3O6HXx7aGUCJhWp4vSTgmfFniUIe28MNXe2cs\nalE5KegA5ruJ55FtMt41FVZvS1nuV5WHMGBMZOCOdmkyq3oEj+iHEnZ6ZbI3\nagu4iPNJnSnxlyNYVCXKdGedzxQVF2QMuJkHxl+zNRETVo6u2jARqOqfr/kb\nceti92HKuViiVs0xV2cY5RVq9mPvDksUAM7+SE1aYWDxfCHyCBKeot/OTZIp\n396D6j72/q6X2b6QoDcZknHS3H5uEZath4VdD77q5kPFapAFzfUJmX2g9lIi\nlb08\r\n=1GkV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Compile class and object decorators to ES5", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-decorators": "^7.10.4", "@babel/helper-create-class-features-plugin": "^7.10.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.10.5_1594750702013_0.30593479405928026", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.12.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "59271439fed4145456c41067450543aee332d15f", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.12.1.tgz", "fileCount": 5, "integrity": "sha512-knNIuusychgYN8fGJHONL0RbFxLGawhXOJNLBk75TniTsZZeA+wdkDuv6wp4lGwzQEKjZi6/WYtnb3udNPmQmQ==", "signatures": [{"sig": "MEUCIB16XCEa05XRZZGaGJfKLux+mc4InXhcrd1fLnx+53jEAiEA4N75fXSwJEVo4U+DFCCPdHnRjxOBQG01vqUuVeTsL1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNA7CRA9TVsSAnZWagAABjwQAJkVX2umxgpPWTR3BSgl\nE5Jz65UTyDIOR7NhmV0bghOUfiX7j6iESLSt6yVQYLSyxkD8AAa2OGzG+QX1\nha9sa8KBleXFPt97h8Ixu59L+atV8PTvwQijucyQtz0peKyTR4j+fV57p5Qa\nQkwKgbauk7f/+MqDqDoYXgMLapHDBrF1YzbW8D6tLtLE3vwn9zXP8cubBlwV\nprsLqrjF7QbmcjH43+c5fsn6abQwv6aWwRuw9CrtZTkpsiHsaSH3Uburg26t\nYLrVqv07H6FEQyJ0zgQ0rh2B0/Vj6NZZHvMMI1B/t+4H0/W6xNDD9ZhrQ7Hn\nXohDDJcWRIRluMzWiwvKi9gDVBw4VEUTqg/ARcA+gyB8xss2pej3X3sTNliU\nQcs4pXnKXuSsnlS46Qbn63nFR1hUl4oQs2BfCcl4GVw9OxWE1SxR1Ka6aJKo\nQ+Gm75n59FPpRNc11eql2ZkH29ZDLRRJcbOMhrHnhpd0PsxpFkH5J5akRYvj\nyULX/C806YRUet+IaHI5OWHRk+T7dYkf70OkL6hebI1GA3dQktIUOlXrd3Rh\n7pzFSKBuJhPhsZij1fGy4/MimvQT5S+mQwWbp47+6G3SaydqcWO3WrSQGkqE\nhHslbpYouJMXK4zKawKyKMKnitQQbmvPwrya0/QlWq7Fi7SZ1yjUXFxhLRqY\ndqrN\r\n=UQaW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-decorators": "^7.12.1", "@babel/helper-create-class-features-plugin": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.12.1_1602801723241_0.5778342965824701", "host": "s3://npm-registry-packages"}}, "7.12.12": {"name": "@babel/plugin-proposal-decorators", "version": "7.12.12", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.12.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "067a6d3d6ca86d54cf56bb183239199c20daeafe", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.12.12.tgz", "fileCount": 5, "integrity": "sha512-fhkE9lJYpw2mjHelBpM2zCbaA11aov2GJs7q4cFaXNrWx0H3bW58H9Esy2rdtYOghFBEYUDRIpvlgi+ZD+AvvQ==", "signatures": [{"sig": "MEQCIHFDPYAUS3eWOvl4PAQwTt4Rk9ReAQFLewud3LUGIzU+AiBIxN7hMW2HxN+SpKnHSt2VRrtwlsnya7N/I0p5mI0xgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf406eCRA9TVsSAnZWagAAeHwQAIAnipxLajRk2BJIh/YD\nuu5vl639bIwnnVkem6h4y0wOC/TsIyC//NkJcF6v97fpRnScoRozuXqxJFdt\nHWaWcsL7rKPqg3KpWoAUe81Kw+eKgdLGFMCYQitOybjEebioDz2Sj2ltEZ9U\n0v2lGRtnOblt3ojuuv8xgfYkQjppomvW9E/pVvUIDaFmlqadA07Q+P9VYKrU\n7m3HvlAwVBm0x+/6ZTIajnbKg+xQiE39VAzIYAMrOtX/AAqVzGIwXsIeqz/7\n1DCXeAmby1AYvFMDkgAlEWQrC5OQUw32TwmLx7QUZ430idHfCNCfVocb16RQ\n3S07g3oyqiQjNtZ9nj5hrS2xP6zsxiMbrgjoPg3Z/sqOZLc8Ujl1aU51gagk\nRwGzMtjK0zDHTRQxF6eiadPw6kI0o+B4OuFdIKoHcsANK6EyLTnrrHy7GBzT\nztSFd88HB5NuPrw8+Vj/437UZVoIasYIpxd4Sd+D8haWzK97X8xy0QgNpo7O\nKGio+27KCasAiCp7XaQSHBIVkNBA8WzSS2imPpEk5PktzXEOwu47lPHbWFYE\ndZVXys3HrnfYT+SKdMLp1FBXcIn1X9vIHBe6wlmgPpq12yn4JjkX6IHF76jn\nxnO3XSTZzfgwSDS25yzf2u5XHV0i4kUCEJbhbG23a3EkexjpuMJ4fyi1mIS2\nB9pN\r\n=FRbo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-decorators": "^7.12.1", "@babel/helper-create-class-features-plugin": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "babel-plugin-polyfill-es-shims": "0.0.10", "@babel/helper-plugin-test-runner": "7.10.4", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.12.12_1608732317982_0.25511828719142327", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-proposal-decorators", "version": "7.12.13", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "d4c89b40c2b7a526b0d394de4f4def36191e413e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.12.13.tgz", "fileCount": 5, "integrity": "sha512-x2aOr5w4ARJoYHFKoG2iEUL/Xe99JAJXjAasHijXp3/KgaetJXGE62SmHgsW3Tia/XUT5AxF2YC0F+JyhPY/0Q==", "signatures": [{"sig": "MEQCIEM0jWiRdy+4n3DKQ+K9JZITdmdw68eGy3rPBIn7BniWAiAnOFDBxHDP2g+YscKhdAhoDcHZ36rQYZu1HJFMIgme7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhoCRA9TVsSAnZWagAAuXgP/jzgEYc/zPaza4j4iCUh\nxNaHNMPIMG3jzDFu7RYIq0tetYFIGwpQuZbLgPPVXXX+CWb8dGvAIjwUd6mY\nNZwanSxl6qVDSHWbAtkPzpWw8DyoVTOwO8axOuUWrveKyY2eA2pmYHVJRiJv\n5l9zvRptmBKrtTrhncGL+gPDtLbgTHA1PF2UeGOJUQGdgrLY1Y4adkyi0bE1\njr56WsANJuhhGxc8vCAMDRyxVcByizL6PCKhwEdU6GSrfvk21/uR5JiFJl2R\nRGWEYWJ8agGJ44iD4iflk5OYa1/HB/bljQyv8+x/+KVk6hgG+FZLrRyKZ1n/\n+e2TVmO/tQN8Ueqt/APcH3cD+YP0tI/p9jXCRxP4GDkvWWMNB5WG2dsR8xh4\nQk7DZstg9Bzqxo7MhjHHOfPmbHWxbnhci94huHHEG0qZvdaD0evs8hnGR9Mk\nprqjxI1NEcPQoGuL8Uu3cjOYH28xxuNlbqm4B3u9NjqhOCpN8EWP0+Hjq4AD\nSDH6HsYyNCAMARr9rCuwlGpDb+EAkUCKKurq2p0JMZq+NIupc2Bmffa5PFlr\nGWpoVmDflbK79RRdyvgeA7zSRhfrvl/lmqT/gA6NegdDIQPCNs3pkktiI/3b\nA2JHspWBnb8q9IrYafG3PFn51AstMgepQqnyqCpa2SFYLzIEjTagJMBiY6A5\n3wy0\r\n=3RKP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "babel-plugin-polyfill-es-shims": "0.0.10", "@babel/helper-plugin-test-runner": "7.12.13", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.12.13_1612314728089_0.3478499607442953", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.13.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "b15f86a3099314df75cc4f791b9767a08a6e5797", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.13.0.tgz", "fileCount": 5, "integrity": "sha512-OZxK2b8WuKEgxToXS83i3zQEW6DcqbQEyrLxn63wQkUrT3fDLgMdM58kUX7780zeqIBlqIqPgU5brZoC7Hfrlw==", "signatures": [{"sig": "MEQCIGKtsCB2K3/iMIxbTXxmY6WMIjC8ZTrI3S4VLuAgpdjTAiA7X8y+xs6FTdAYAtj1qIv2Jx3wZBumWCagOoEYVsi+5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUwCRA9TVsSAnZWagAAkCgP/3Exhe9f5SvV7PNpbzDr\n4IWjdDghLltOMtby1Lx3LlRBjkjnsXH567y7aDxPeqjGoLl4BrIXWEBLqVLo\nI2ehEL9Bmygt5yN99+lHvL6xbro/jVZcsvTEjJSWUClTPuQvxHhuLnWOOSHC\nFZI8v0O6BQMI9Ea0cKgayeljly0dc0rDF/nAuFaBHxu0cKpMIPqPOTPqMe+B\nK2r5LsD7Hrtn+4XkXPtB3hZr5uaEGXGvSdE2UK+1Kyvq6iPyVbvOWF1RzN55\nAGtJ5koxIzBYJtP1CIQX6W8oiXhNO3Ia+J3bUdl4zJjqQKRgYHZ6jE6bkHwt\n4xOTwYdaCQAEDC32U5+p6hjZRYFfxZ3KjOlVAh3vApxRGjayJzN5GOC3FPAO\n0aCOxcw63FPvtL+3qQ+bdePd7iDMm4jGo/r0BnmxGECI854UeJOGVhpejs09\nFZ9F+6MMZEix5KVwarEarufeGoyGDQEPDtjOpc7KrmyvubCQqk9A0XrsSTPy\nJ1oDesRQye9QzLVKpKShn5qDrVDZiLOr9j3xaKeXyoEJ+nJ5yPoAssncNF4W\nU38fPsU/7hmQaozrrkmOtXTMSe57sEoMInEvHygo4K1rBN9ipv9IUDppBBNp\nTiG1dOm1vcm+/lS+dp8ud5iBoOr+g3SETPxQnJ3HfeAbHqs5g1/Uy1QnAyfx\nKILG\r\n=qO+e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "babel-plugin-polyfill-es-shims": "^0.1.1", "@babel/helper-plugin-test-runner": "7.12.13", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.13.0_1614034223447_0.2305975477862725", "host": "s3://npm-registry-packages"}}, "7.13.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.13.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.13.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "d28071457a5ba8ee1394b23e38d5dcf32ea20ef7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.13.5.tgz", "fileCount": 5, "integrity": "sha512-i0GDfVNuoapwiheevUOuSW67mInqJ8qw7uWfpjNVeHMn143kXblEy/bmL9AdZ/0yf/4BMQeWXezK0tQIvNPqag==", "signatures": [{"sig": "MEQCIGkqJeNLfLbUQFcVPjTTGAxBxkrM1bsubuCfXixQ6Y4oAiAiCLwO7htIdB4hY62h+9i9hwd/6pCeVtt3tOwsUceHBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNQrwCRA9TVsSAnZWagAAwX8P/iaxOplBfXEIqm0c0ytl\nabs9ST9HgKRz8hH5xPevuAnPvpwMkYZCnJadD46DTBHoVlUUMnextcziKPg0\nhUHU/sdZdBBYD2BgSIhLHXSZXJv+CI/rgFRncdEM9C4Zd/gF3iJAMeeb2Dlc\nGIyNPkyxsuAhFD9xh+RVJ3LffgWYaD6F99AX4DF/oCGvmoHxNMtemxYzp5WS\n/IA6VWh0KltHKFhBrVob6CbdF42ZHXTuD67pIYop3Jdni3j88NXMvxlYqKWn\nSBCkSNmFphrypBT6ZRzDEFwHa0hGFBFsC5UjG711hBYHfDheooqCM0NatNVt\n//tvyd8ZCbHSv+mhENoDrt7/WhuCrGbIB9a7yjPAIp2sno5TDSaEp/TRlVxf\nNnSfH0XL12mD+fNQqq6u3Ld5Wi2q3rN+2PZG+/hSa+Y3k0Ixb7UE2UzfgFrm\neQdAPPq8GtuNC3ADhtj1e3/2QzLX06GEetWslp3Pj8m9FRfQYsklZT6P69e+\nEgjVj7RD7m+304CR3f9lq4V7GLo7BXyY85Vgmyi3YI6D0ZAcrGNXZUvYMEmS\ncz377tq5AhvPbUHAsNaFfB9ZPsAgR/V+4lNkEAKMZGt3e63umbhY7Fo4TFnI\noHaHvQ0wOW8JC3e7qc0aPtDOgSOjPea968boG+dM60dzaQG7xcVo5w0nbKUw\n/oPX\r\n=+aNn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.1", "babel-plugin-polyfill-es-shims": "^0.1.2", "@babel/helper-plugin-test-runner": "7.12.13", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.13.5_1614088942438_0.14589834406204494", "host": "s3://npm-registry-packages"}}, "7.13.15": {"name": "@babel/plugin-proposal-decorators", "version": "7.13.15", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.13.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "e91ccfef2dc24dd5bd5dcc9fc9e2557c684ecfb8", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.13.15.tgz", "fileCount": 5, "integrity": "sha512-ibAMAqUm97yzi+LPgdr5Nqb9CMkeieGHvwPg1ywSGjZrZHQEGqE01HmOio8kxRpA/+VtOHouIVy2FMpBbtltjA==", "signatures": [{"sig": "MEQCICJzQcslNzEkCNdcr6SyxlaOg81pmvIv6mIyutpWlDtCAiAJ/lNSQDlWvEctc0EVmaLVLOIHgUeFUyJPTa2m+NothA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbyY/CRA9TVsSAnZWagAAIFcP/2k3ptlvbSUQHTSDt9Ci\njrh9iWPXak/FFPXdhIFlbyWmMiSP1qL+2ciY84VFM+hah4K38IvfRQLonCiu\nqmhrcOeNx5tQCvb9mDhYit/xmRTFzs+ckTRjdXAqS4GqGuk+vLhlaAv02XK9\nQT28zpNoX4wQBzfDPC1gHpGkmlVCLlH/KSsJwijFSWiKAaouXOTlRBKl/b2+\ncnWHqlpEXiRUVIO46zu/7zidorv8GSvMGMaZoeFcEyerKgLHYouL2c5UbG19\nYIXVIARW8to+77Kj5XjMFtthcfebp2Gb0qMab/PNjHujvTUYkyCmOxXkv2fI\nVmw3wa8sUIbfWv0bNpDLqPJkSdbRNK9S0/o5UrlUDvEA4HUiNWNOYSauyHe3\nblJikALitTXctdGzMy1q/4QBpo1ht58GEIA49848hE9HOeuhVeVWsYbZRsOM\nFdOKKRQmumMMr/SgxFp6SczmMLRsBJQFXwoC0DVPA3P/dFQ3qJQxpdexJyU8\n2WJsr8lISGiPJuNsfvEAChxhBAQwKUoIiWurId0XWMpIQfw6kUeDRYmCIBAK\niQWosX4xaiMLiCXZPboHjNnH7lY/MF263FKBKv4evixr51YNwF6ROeSdDOjv\naAPRASKNZgJekbDCka6RkTcMRjThLUTmJtlOFFjVYQfbbFDTKw/E+zDCTVzT\nf9cz\r\n=v25R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.13.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.15", "babel-plugin-polyfill-es-shims": "^0.2.0", "@babel/helper-plugin-test-runner": "7.13.10", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.13.15_1617897022638_0.309925177230828", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.14.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "e68c3c5e4a6a08834456568256fc3e71b93590cf", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.14.2.tgz", "fileCount": 5, "integrity": "sha512-LauAqDd/VjQDtae58QgBcEOE42NNP+jB2OE+XeC3KBI/E+BhhRjtr5viCIrj1hmu1YvrguLipIPRJZmS5yUcFw==", "signatures": [{"sig": "MEUCIFhK08GKFmmSjMN86HKI5zQdtDynGTmJUIGEHyixWVHeAiEAodx/oNeXp3zicXc4EA4r7medjQI9OEiGxeohuA2/ZiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvUCRA9TVsSAnZWagAAT4gP/jxgJ7OgBe7P/5HCuzUA\nmWDIgzBbpLEmbwoYFdegNhEhQXOgQjaGBPHDlFFXxhi6ezXbskpkCqgvvAlG\n+AmN+bJAPQ++6LexXhH7jZxGR9fyry5FkTINjIAkvgwmiixDLRwQv9afTvkD\n9Ywt6kWYN9aLFSNLo6wroOSV09FJL47zeLHVs4gjTmanabSWl/dja4MduPPZ\nDnF52p8qum/DOVyhjNApP1FgoYW5xHHjDLGKlcorZEthSbRYNWEmeiFfLwYn\nQgr/QMe/IhohsjSee5EJc7tuJt4XdM0OKlfgYW438F9RChMXUkn1GmUYJkSo\nRM2OS/ohwk+Sid/DZqEZtYsmYtNHPMufC1xDDnqJDBCN4ksrpCWJveS3tWvo\ntddFDpJhWcV8IqKre7Gw17z2XJbXBEKHCmE+qIqtibzyT28r5rxjsqE2bCQN\nSrPjedMFnGyR97gGUx8E2HoLnqYpk7TUxLQz1gNP3Nfl+Utg2aIiHc3svvgT\nS8VUzmSgaBvY8YeDhRZ8zgcBd3Orfk2KH5RHtFGr0wcrpaQ14RbeH5bzJLY4\nL5BoAcIWO63+jE35UnokVgb2wzHlxTKM+zTlpxa4ms3PyOakzZqnkrTaP/HH\nshPHnvdUflj2+ecLEdyQge3zBaT+U2ztj8sgiQK3gocq4SnL5kr4A3ngmq/t\nZmRq\r\n=K8pK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.2", "babel-plugin-polyfill-es-shims": "^0.2.0", "@babel/helper-plugin-test-runner": "7.13.10", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.14.2_1620839379876_0.7314574012270376", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.14.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "59bc4dfc1d665b5a6749cf798ff42297ed1b2c1d", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.14.5.tgz", "fileCount": 5, "integrity": "sha512-LYz5nvQcvYeRVjui1Ykn28i+3aUiXwQ/3MGoEy0InTaz1pJo/lAzmIDXX+BQny/oufgHzJ6vnEEiXQ8KZjEVFg==", "signatures": [{"sig": "MEQCIC5fYwK+IxB4rSIP+xfStWrGwK5WegcqV8oLYBmvXeYxAiBpeWU9AJpgY3JNruFQkAMl8NDuUDLnpgLEalajceaB+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsOCRA9TVsSAnZWagAAI7wQAIg/Z0fKtS9Ad5X1SBav\nl8xCbjFGY50chOxfz89i33v0E6T+f5rZQ0hh/6PEcFoBRBVagraatLPR8Cn1\nzx9CC3U3Kc7ebNP7ythVd49Pg1QcNE+QuupaKl8DJTq5Y9aTRmprWaZPvfVp\nSvVySlH8GmjWU6rXBn1u/1aVCbo4ur8Q+YZWZiwuZ0q8JNjf/ceuCbLURerk\nnA3bsJzMkth0Mt8F6moM6/i2l0wslyN5YpeKJf7qlssnoQO4JOpjJCU8+KDt\nyqa94yZrSHIK2PBkaK9WRtRN2SjndcCRjvgDz2IeKWzKd4o7YUe635xPoe68\nXjSNjDZDheNqCBMecDpypdNazWbl19gLxAfMaiqNe9lQs9YvAAyjoRtdxGG2\nSe4OzGh/EzQKDP1UXiCENcxON3ffbBc6o2N/1AykGQVGACr9of/WhuMJxzXo\nSLdq+OAsYdLSrsj0CjHTPsu23yg9+AsUDk4Ysdvw8cePK4aANfm6pdOVgRxo\nWH7XC2BmHhRlDU7xFs/aBwCJJwmdeTF3/jqxWOZbQe/YtpQ+ULFnUlHOAVu+\n+hAZmfpzMgvY0fYq9LJKAFH9lLRiu25zSmsr1a35CVOxXjjyW5Aut6cKRxLy\ngEn8f5wHFjWT6LZyB6/tEkaItqu1B5TOu1wX4SjWKuzgfhZCHLwMn06RObOH\nkEOu\r\n=2WmB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-decorators": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "babel-plugin-polyfill-es-shims": "^0.2.2", "@babel/helper-plugin-test-runner": "7.14.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.14.5_1623280397866_0.9741503838633188", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.15.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "fb55442bc83ab4d45dda76b91949706bf22881d2", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.15.4.tgz", "fileCount": 5, "integrity": "sha512-WNER+YLs7avvRukEddhu5PSfSaMMimX2xBFgLQS7Bw16yrUxJGWidO9nQp+yLy9MVybg5Ba3BlhAw+BkdhpDmg==", "signatures": [{"sig": "MEQCIEhoZD7FSV0wfal5HoHV3Ayc0glXfpnZGXkNbKdO5tSvAiAfbaUkbgLgtmXow9Tk8UCit6Y/wB2hsUP7bf1c8iC9gA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12699, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSuCRA9TVsSAnZWagAAvEcP/1tnUm2V8n0HMIV3QOSz\nw6oXWJTSdmci2BSRD8Zx0SmJgrnMBueXCPLMdGqY9HUkMk5Noh9zeRWiLxRV\nLfAx7bKsK9EaTKb0mS4KW1Ae5toCYSt/94oNxmbRBAfPuC1nsXi5gkVjQtCJ\nASs2d9Mtx2N+O8BHcFWaMoZ+EwdGTERlr0S093hOrHvknxiuPvMkuUS3uvER\nGUNYp47nKUDEwPS4iqYTBgrYfR17/I9TGB+I/ZwvXK0dogvRO6MrcTsFT1Cb\n5UcMDxY7TZtvr75TdZT2lmEo0w6TqBN2dPo+h/jT9JTNOCZ+EFb/stvWVvxf\nm6sPTAokgzK8cM6e9Bv2e1m3ET5tgTur5M3cEqEhg4tc6PkviUmibLhYRvjm\nokOXdgVSxClRya1WCDelAQVp7s5xv82C6jiXhrmH+j1iOxQgEqDBsVUOg4gg\nvmmToKMQ6INoTai+V0XsfTo+EMBG3OsUyW4C+PEBnWbN1Ex3/atnOw5sCa26\nB5LOcxuN4lw5Qw7N4Pfo0ybd8EPl93EycB4gXqUxBGzm/sCwwd88T1nJBX78\nDZOknQphTm2XHXHi8vSbf6ByZvuwq7xjsioojsyCWHRLZ62LwVOMdf/5VLVl\nBPKuSvbs/eZgJjwiYudxpfmZyegyLXmV/8t2t0Ji6i3Gy8DG3qfbQupnO+9j\nPn+L\r\n=ds3P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-decorators": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "babel-plugin-polyfill-es-shims": "^0.2.2", "@babel/helper-plugin-test-runner": "7.14.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.15.4_1630618798065_0.3340104912663806", "host": "s3://npm-registry-packages"}}, "7.15.8": {"name": "@babel/plugin-proposal-decorators", "version": "7.15.8", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.15.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "eb2969abf8993f15289f09fed762bb1df1521bd5", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.15.8.tgz", "fileCount": 5, "integrity": "sha512-5n8+xGK7YDrXF+WAORg3P7LlCCdiaAyKLZi22eP2BwTy4kJ0kFUMMDCj4nQ8YrKyNZgjhU/9eRVqONnjB3us8g==", "signatures": [{"sig": "MEQCIFWfQRWtW/vzUwYNZ17jEv0/WoJhc9riCdkNpgbI1zMWAiA+DLOfQ1j9FIOGYN9SdBfVmA5QZHfWwMThPiI3g4QF7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12699}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-decorators": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.8", "babel-plugin-polyfill-es-shims": "^0.4.0", "@babel/helper-plugin-test-runner": "7.14.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.15.8_1633553696009_0.4666419996627673", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.16.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "515db5f6891611c0d176b63ede0844fbd9be797b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-ttvhKuVnQwoNQrcTd1oe6o49ahaZ1kns1fsJKzTVOaS/FJDJoK4qzgVS68xzJhYUMgTnbXW6z/T6rlP3lL7tJw==", "signatures": [{"sig": "MEQCIG+5mehkWaQ7h/QhslUs4djOC6iPLgsC9fIwKNiDsRbiAiBP7ZHtA3CTHy8f0W9oJ1eYrLtOPJEn3rSChW3wsvv5XQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12701}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/helper-create-class-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "babel-plugin-polyfill-es-shims": "^0.5.0", "@babel/helper-plugin-test-runner": "^7.16.0", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.16.0_1635551279438_0.08629519446797018", "host": "s3://npm-registry-packages"}}, "7.16.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.16.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.16.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "9b35ce0716425a93b978e79099e5f7ba217c1364", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.16.4.tgz", "fileCount": 5, "integrity": "sha512-RESBNX16eNqnBeEVR5sCJpnW0mHiNLNNvGA8PrRuK/4ZJ4TO+6bHleRUuGQYDERVySOKtOhSya/C4MIhwAMAgg==", "signatures": [{"sig": "MEQCIAYJPlUacOPfOve3TCz1TEN6UuSIZRsXyzqtxsgMQBz5AiBmwrNXHMYCA9LRX/DWz0TXdy9JNPt+l6XO2CXkpHNDdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlDS3CRA9TVsSAnZWagAA6t0QAJckA/xYDoNe+/PapPdr\n8jYGPkD53RTdH4/w+itsaYwFlSMGTbMVLIK31E3DhAxnnqr1KTlHEWB475id\nriXWAVieZxb+PKVH6rJI99siL+S02gIGZj0MN6wk4xe8MtlvzqYRYZNI1pLb\nOvFk73QEZFtTbfuSf/HvEAR5Mr76BmXwV7S7Z1045siOLF4Ym3ylkmEClCiY\n16jpcnQR7zrB41tiswhDw+mNwulvS3G/F27tKzIeqg9q/ZwNnBpgPUTygeCG\n+8vDYQLfAc/o56l29+CwQ/KBG9bww4JOL5u6Gczr2GgyvfgOyvuVgVGRJgTo\nV6bq7w0oNAdLJXDAQ9nppZ5Ws75I0CpfQKm7tgl4MK9vRoWTx7szqoBgNuna\ndda0o4oWd00VZJiLSQGI0cY+IJBzQmZrrIyIqlBjkinrBa+ypK3PoRU+gEyS\nEy9JBS9McLuZXkYp915sBpW+mFuaQHY+Y0TswrIN/rybygDgPYZSubK0kHbs\nGukQk2g27IhR8dqZ/2426fZ6kcav+2kAF/dzcZ9je5m7MchWAske+67dKD+/\nvnwcpbJFOEkmrmqtxLrEuP1cGfqsnpGHveM6TQxD4YpNvBcO7zPwNY/4fYrC\ny92bjvzRHGye0Kp5IRHwDp1V/rTTkEz0sBFAESCYuQ5qw4PX6bv/aQb0BzKO\nx40Q\r\n=JkLS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/helper-create-class-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.0", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.16.4_1637102775739_0.2826082983892284", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.16.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "4617420d3685078dfab8f68f859dca1448bbb3c7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.16.5.tgz", "fileCount": 5, "integrity": "sha512-XAiZll5oCdp2Dd2RbXA3LVPlFyIRhhcQy+G34p9ePpl6mjFkbqHAYHovyw2j5mqUrlBf0/+MtOIJ3JGYtz8qaw==", "signatures": [{"sig": "MEYCIQD68ueatS+CJr3fxampyd2NfEjGvRB9dqUnC6rO21wbfwIhAKtLGJZDoU24YsEoU3uyzpoEbFFfRxfzkOEunnbt8qDC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8krCRA9TVsSAnZWagAA3gAP/jkzBBWZ9U4hCYcMPHaB\nYloIDLTs+EZMsJbHnx5axI0mDPunyTIK/B2c8HXnpFlHE/B9FAN/V+noxFsc\nyR8tOWrzbxQvgqMAMe7kyfsQjbiuxR9uhtUp6/Zn/HIJAsMUYrxI1uJazuL2\nHEggtWQBIX1mC30+x6nQuib9HGDEvWI6ZDISIGkjzrjNlWlPcNi6753YoDWM\n+gMxeMnX6aiS60NDn3fWpNkTv8sx8CN1uv5LrjrpPhxkqiU/+dc+34lFuSUl\nMk0F3bUUV3hYLxDH6wGLKQ4/xi83bpMLJ0PwSKbISmmGujdHkz8dAENk4XYT\np+Xeu9N26Gt2uYXBw/vCZJXNOx27xF+BdpGXssZUY/6NaRQ+jPnmAvLO8rln\nhsqXAcWXJHPVKw43sc1Fw6dpq7UaU0aPETORC5Ot+ryKNW9oPEBI7EBjKuwe\nvLXkwNQiVc9Ao2qFm7WzRnGGGvZ7KsjcuXCl4Fdow7dDwhbEjVZXT7rs11IM\nXeqDTpivy9SKCiFHMd44A9p3wW9kDuMYdcbNsrMhQDBDxhD/h9QGzamgT2NB\n/29x8dYYWKp1+qhGxn1Dna/3Uusu1thwDtH/pfIve5qvLxmiQmDCn3RzZx8k\nsuu/icKo9yj5A2W92PifSV+rfzxnlWO1We1LWUDw090dL+J60MejVQBunXlZ\nDJUc\r\n=i1uv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/plugin-syntax-decorators": "^7.16.5", "@babel/helper-create-class-features-plugin": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/traverse": "^7.16.5", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.16.5_1639434539264_0.6806161590743856", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-proposal-decorators", "version": "7.16.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "922907d2e3e327f5b07d2246bcfc0bd438f360d2", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-DoEpnuXK14XV9btI1k8tzNGCutMclpj4yru8aXKoHlVmbO1s+2A+g2+h4JhcjrxkFJqzbymnLG6j/niOf3iFXQ==", "signatures": [{"sig": "MEUCIFkpGle4jo6wZi2gO4CMe/WzhKisnsq0rO/qoGahxE6MAiEA3/qx6oYVc4zDoKMQWGPFkzHn0SoAMFZ4OCj5YVZphDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1yCRA9TVsSAnZWagAA+AMP/23U8Q/TLkkznarYAWf/\nGcMfbloJyvX+FZ1u3RYq8hAIO+qZDLKuAZFVeql3Y9EdYaEIqz2NbaN4iTCa\n3bU9mRU3UjPLxvsw3zKoJ3l9KSR0QCryu4kWXOZe/6X151sAIthRZ3kXhdwf\nRkStQLZievhV98S+Zdnw//7Vw2kN8alq1c0JW4E4Qvw3T55jb48D/J9sLI1L\nZMNx766KGpjzEuTFbcbghRBC77i14t2c0VVYrCi5YT5WtV4MV1rKbp9fx<PERSON>el\nmDtgygb3cG3HaM0zIU+qPsiTcdjv6wDfRVCk/sFog9L5dWdXzewX8IHyqzee\nA5eRPZSHOFmZlguCU8YXCq7gsOnoPOIaVvXR8QdTqRmyiFfZPnl/dtfl7xaZ\n6WxkGOu+OPYrTaKO65czt4DF49hS6/sJHIHBMlwXYeeLEu1hPdveJfoPQz0P\nU8+uo/oW/FDJcxATtDeQIu7zUEmaZ0thPYdy5ZMDgzx9NjJHnHF4lykqAQni\nh7Rn3taCpTPMZw+LMCRQvTWKnkh2q6E/JCeviwgr2j7YFTdqo4WTwY/+kNqY\nk5X+9pgnq+dBwhjg6oUZKzlEIrTxZrJ7i0l+Ozle5rt0JOY0tsGFof9OYH/g\n93U5LUXFjhar2UbPlTD8CRA/WQk+NuEGm74JBynzQAPcZ+x+hRjkVfOdPXqQ\naOTC\r\n=UT0j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/plugin-syntax-decorators": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/traverse": "^7.16.7", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.16.7_1640910193860_0.5498042075738847", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.17.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.17.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "fc0f689fe2535075056c587bc10c176fa9990443", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.0.tgz", "fileCount": 7, "integrity": "sha512-JR8HTf3T1CsdMqfENrZ9pqncwsH4sPcvsyDLpvmv8iIbpDmeyBD7HPfGAIqkQph2j5d3B84hTm+m3qHPAedaPw==", "signatures": [{"sig": "MEUCIQDwDqy70+qN6EAnSX0RYAmBWJIXWhCkEMyqHJZNvYewHwIgbIPSnxfUvJLe+UdTpeiBHfQX1tTtgGn/bNxRLOsResg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4YCRA9TVsSAnZWagAAv4EP/0se8STxomtvEufumi57\nC49Q0+VF87Wv5vdq+1v1F+9QN7iPSazGFqOgN6lseU4+eTgt09pKv76uDDEr\ngY4V6ssO98gSNz9nzM8bp02aDysRXdAwdXHU53NVoK34j3WIoLFd9JvRrCjP\ncZgJ49PoWNastPeaxtJH8pR6U9p4Hdlibsa+FzbWiMF6VsutKcLQIw8yO3uz\nh4HO+IKUCPHcE4+2HfZ3tbyvSHFU8RL4WYT6PPTDGKKQLBu69Gc3lg5id5Cp\nnRADAlfO+PlZ16xuUBM/jkCvlgnQyiTyy+cwzCmf3aTvlRQiibpjbT5SSWeS\nt3Q3u/DcuL7XxmTEEqLZoCNsZVI60TUj4Q8gA79QApLAB4zf8yNl/VFLrleI\n2tekaN+6EcPaPIzPIVtcl7z3Sfx10USd4lQdn4tGIqfDLzPgCneExfYsflZG\nbNi4iANYHZubSFpKzptkGT8J4ihI5Yk/uBhZec0x7OZ/tBIKowUh6+2ML//6\nNfHSb6pVG0cSuAAs+rBffPSGBqyYXeTdgv4hhMLc/Qn5scmlG5An2Ma60VlX\nE2ZftYcZRXbxzivqXfuOUl/OrXOD5dsaxyIOnIC/ITxzebMdIqxg2CMmyOQ1\nHbHyqXXMQ98bf5X6q+GXL2IKCOUwNrUyGEQ5bStPKKG/1kogyvCQfECAM+MV\nHu0y\r\n=YSol\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"charcodes": "^0.2.0", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/plugin-syntax-decorators": "^7.17.0", "@babel/helper-create-class-features-plugin": "^7.17.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.0", "@babel/traverse": "^7.17.0", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.17.0_1643843096810_0.9327273277957531", "host": "s3://npm-registry-packages"}}, "7.17.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.17.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.17.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "c36372ddfe0360cac1ee331a238310bddca11493", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.2.tgz", "fileCount": 7, "integrity": "sha512-WH8Z95CwTq/W8rFbMqb9p3hicpt4RX4f0K659ax2VHxgOyT6qQmUaEVEjIh4WR9Eh9NymkVn5vwsrE68fAQNUw==", "signatures": [{"sig": "MEQCIEPzV5nAkpQfKoVnV9DtRXE7jmZfUCYBdvv+Upl9tcV4AiA/LxT711AMgZDNRjlWTuiWOVKV2dIb5ZTacd2Cfuu6Dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAo2yCRA9TVsSAnZWagAADh0P/0eiccRJ/8TMN0ZG11OR\n2hc3quc/gOZsBOW70TEQrdgtX8bzLPi5QyL5DfEe6KGaPCZ/0cKO6T8RqraI\negYgdKvJluFTG4CnWaG9xdvDFVU0nuaLN3mIwHBN0Ji7rKq+mKB7/grZ3kqx\nLhJZbzyQFaA1BuuoaU2G1pvMbiGjhKJIVi7P1rKV2cHH6aUcXFLDay/hN230\nPNbwaozMac1Q/5/YG0FwgjogSJHOpy8i25CnSKZQbUrYNgSy/Rb2YYoEIT3x\nEBXugho7r7JJ8/Q3jCCJSCv+3IRfYB2+zjZbfHGuuj2qvVranAQPw8Sbwvvx\n6gNgxkwTrd4Qrnqwpt3RVlAnHtNNFYlPtfUT8paANjgtl5y38IzXa55jEyRE\nIC5J2mFTI/NJlhc0VgcN4RyKTqPBmaI7pDQlWP0lzRkMFy5DFiFN3AFdNSrI\nn9a33f/4dH5gXMdflC8RqPNn58I3puE01k29X3wSzxzPWZ+V2PWxbiSH8+4i\nrWKavCeqIl1MJcyrbH8VBbJiRP5pudTrlRl3b5cg9dhSV3on1Z48xjLin65l\nwICcp7xxgqz1TptDM4kU4AUAbHYj7HoId+qNrbSB/xlhxxMrA0rwRN8OZ5wl\nrqh46L7FGrsqEZ1n71wcxMlSdgfOYFV4s0UXHKovY/AsFo5Cb77/SOEAGXrC\n1z+c\r\n=p90f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"charcodes": "^0.2.0", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/plugin-syntax-decorators": "^7.17.0", "@babel/helper-create-class-features-plugin": "^7.17.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.2", "@babel/traverse": "^7.17.0", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.17.2_1644334514552_0.6086644230724259", "host": "s3://npm-registry-packages"}}, "7.17.8": {"name": "@babel/plugin-proposal-decorators", "version": "7.17.8", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.17.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "4f0444e896bee85d35cf714a006fc5418f87ff00", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.8.tgz", "fileCount": 7, "integrity": "sha512-U69odN4Umyyx1xO1rTII0IDkAEC+RNlcKXtqOblfpzqy1C+aOplb76BQNq0+XdpVkOaPlpEDwd++joY8FNFJKA==", "signatures": [{"sig": "MEUCICqV9SHOqTH2UAqkFRVVSbdpNwm2nrHny+unyeId7bDEAiEA3JNNU0wy4FECvxeZDG9forOk0m9eBdZZY8WmjHrAWI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNOwNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo57g//eawH47P1Rc9Lx3dxEPkGNE6O5wjm4T2p7zLgnJkvajIagXkT\r\nABjcD5LGS55PeQWC8kEgMdXr7Lux5q//EASRgqrL2QfD0pdsqeqf4M7/WBy3\r\n4a2Y3WQaxMFnAMv6F5l8TFXfc+m4HweGSSq+yMOfY6+rrOmF5tAGJd2q9mST\r\n4IIG6sspjtvYXnKc5CO73fmkdsIIvUivWaL8TzgC0XrcW4y0exyJSTDInXBJ\r\nKM2WinuwoN7V1nN5z5vzoS1ZssPTp1guBNFguXpdQHQ/JoORkHpWJrZVBlBm\r\nZPIStLUus18NTwX+l7A9188MTnCyCZtgcJ/Ed1CI1Iq2jOC/xFLnKfHW3vNv\r\ns4LZdK+14f4B2qILNm8mQCpuUe076ZWIwc757OkSQcNUmXtEH/3+5zZgpzNE\r\n7TdlgjY0Pgfp9R4/6filBrOEb+ozuhAmlHjNKjD00lOs5DnBsg5aP08R2jVr\r\ne1DA+RsHYrjt6++Pp7hIGY+kP8Oz1IxJkR3HYzJQLYvcOgqUfutbZxeJM/Du\r\nGN25U2YmS1Kl53bRpdXenHPeABxlfJxFJt4U0UxdQWldiOH/+wb+xcq5LLhw\r\nh7J88fMJ+IhXLsEsKsowSw+j3vHfmdzAkx4QBy6jlVaYToj4petDKi5jzigh\r\nlG0qbGVzx1yBUTup+p+qt+DDh9WAotkLyYs=\r\n=mdfB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"charcodes": "^0.2.0", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/plugin-syntax-decorators": "^7.17.0", "@babel/helper-create-class-features-plugin": "^7.17.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/traverse": "^7.17.3", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.17.8_1647635469358_0.2562921957313724", "host": "s3://npm-registry-packages"}}, "7.17.9": {"name": "@babel/plugin-proposal-decorators", "version": "7.17.9", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.17.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "67a1653be9c77ce5b6c318aa90c8287b87831619", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.9.tgz", "fileCount": 7, "integrity": "sha512-EfH2LZ/vPa2wuPwJ26j+kYRkaubf89UlwxKXtxqEm57HrgSEYDB8t4swFP+p8LcI9yiP9ZRJJjo/58hS6BnaDA==", "signatures": [{"sig": "MEUCIBMGTuLxGxLKOSuZp70Ap6YEWyKrOE/u0meCAIOG8npPAiEAvYKxE/Xbn0IAGkurAvCS5UtQLXBbkVMS2iQyvT/6b1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTbftACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGUA/+KR883ADUmJ51yfPRwhxowQfNYH9ae+CJjivWM0WUJYStCmvq\r\n5hSgJnbktxNILGKoRtmas8OcL4v1K97Q3fab0TPbZ32I8PmAz5MWlDFw/TWQ\r\n48RWtYMYvskEJl+rgQTX5hITUYDdq+rpauZ4hHWuQxxfWWvS9bQszb5aVHan\r\n3Hcq0Ds0UfHwj353rhTD49zAokpoPntCDNcbDP8d2orMIJtI0odkZ0eZLCo6\r\n9/ngocpL4wfOCfpxPHwZQ+97wuaGBqfVg1g/abmMn71BcY1GhcSc4UjXQf1D\r\nQcdA2KTtmQJIBFzO9R99HHnXFysGAm7sVDZIYmaAw/2hSjIRjZFiT8jp2Tvr\r\nkUj1ogxOhfkPPiFYtbWKWeSlTDZxy54i4YG3Yr8r8xF7nlAI7sFI0w6jBU+K\r\nwvEntK9uzRIBQUUgmfNhZobc87XAeQQjLNWyGwdGr2o3XB2LNpnWy8Qmzu/T\r\nO6m0dyraywBLI8udmxirzHXUdPjHNWaEqussSpe+4itugFCcqRkFphIyUKx5\r\nBo8PpL8YfztvqsaX80DTbIFfFvC+riFv2aH9XZrws828uFiFwQPG7hUM7uB8\r\nouvQpa0aQCdYxgmET3w9pXuQEtU3GsRLC7W/XjYyh/7vCfjMvwM273NI2Jzh\r\nUmH3mKlG8eDeI0mRW8U1jJr7DCtf6pmJc0s=\r\n=xjus\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"charcodes": "^0.2.0", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/plugin-syntax-decorators": "^7.17.0", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.17.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.9", "@babel/traverse": "^7.17.9", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.17.9_1649260525718_0.3799237002218627", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-proposal-decorators", "version": "7.17.12", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "26a6a605f271a6703abf97f8fafd1368834c131c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.12.tgz", "fileCount": 7, "integrity": "sha512-gL0qSSeIk/VRfTDgtQg/EtejENssN/r3p5gJsPie1UacwiHibprpr19Z0pcK3XKuqQvjGVxsQ37Tl1MGfXzonA==", "signatures": [{"sig": "MEYCIQCF6hllM8Dv398KXmOEIq8SNEhl5HUZ22YmXrJLw0v/VAIhAJro6hpFVm2DYjvnHyKNDwkLuTAqyETSDcSRWG+8gaGP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqpcg/8DfmOkkXZQAahjveKcNn9qfk65pPDrNGZqobdKpfF4jREKT0H\r\ng4gZr0L5ijBOozn+hR7O/1onzzSJE6hsasU7X2svipqt4vtdj2jgc7+T+6dg\r\nxven6MheX2UpZOucxwIP4D1OeFn7eDyxCefBdWuBQU1hwN9AXCr8er7hmZwX\r\n6HApQ0yWfil+ptJPYtZBSpLFL6Fw8kXjY6GXSz8Ed31IrLDz4Ym0QmkArrUr\r\nmS4y8WmtRwdcEWx34g933h+1LfxedXKPCNTjpSUpB6X2mwFcUuMwJfeiQb4i\r\nVS+4NPhvYtXPNmW59aFkZDOHGZSDr83Ep/LDgkAwY0ofUa0xU8HUIRY1Md9U\r\nkHuLjPs8ggVqLg7SSfS6NFMMuvC8qiaeWJgftA0g5stmhYnpXnGBc9lSBTx5\r\na11mX/OwuUOslO2gJ+QDKDz2VD6UnQo4eb27P9zuSu2/3qc5JY9QxbW7viIR\r\nLNOq2NI1i4XyyYCFjgwHVAAqOiAGC2yPHoV/Bn9XciFnT+RJZLEZPM0nqjew\r\ntHdZNmca8T2QhNaVrNSK05tQswPYlepT9RYdVaMk/xwud+DHjkF8HyG79GwT\r\nowKVERqsvHYIKeLeJVI/I99uAOp9Ag1t4tqN/Y8Rbt0ZtUdlVAlHkRDRN+a3\r\n3J2TEpvnFJoAW6up+0d08LkQYtmDWTY0Na0=\r\n=7kpB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"charcodes": "^0.2.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-replace-supers": "^7.16.7", "@babel/plugin-syntax-decorators": "^7.17.12", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/traverse": "^7.17.12", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.17.12_1652729590176_0.20568964648138666", "host": "s3://npm-registry-packages"}}, "7.18.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.18.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.18.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "dbe4086d2d42db489399783c3aa9272e9700afd4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.18.2.tgz", "fileCount": 7, "integrity": "sha512-kbDISufFOxeczi0v4NQP3p5kIeW6izn/6klfWBrIIdGZZe4UpHR+QU03FAoWjGGd9SUXAwbw2pup1kaL4OQsJQ==", "signatures": [{"sig": "MEUCIQD0O8O2lj3FEzUrBpau4fUFnAfXk8o6wE7FI6WqLdroYwIgTfDSApDI8oxFOKwOP+ou2CAdCzQktPmYB9R0M5OmaDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijfP1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBFA/+ONOm/9l2XYvVfZUfABkMR6bRqy5cyR7Yl4GuXdS+T811j191\r\neQ75LIdSWCsWTSYaF5Vs+30FQ26PDG/g1frPquX73Bt2NuQtpT/e4XwAc0Xn\r\nwJnkvjhV9O4EFBwAfrNyS8Vx853P4jx+LK7o1bc/VoJkvwgHuGBaRLKDNoYH\r\nfOa1xhgWk05loybDX9X1nnUGfEmAChE2wmCVuYJJcf89hJZSnDneuFJdvQmD\r\nTe0nwSeGIod114X8dXjtZYNSHXsmZMICv3lO6iLYYZhOAeD/vno9SUTdSKji\r\n7kTCGLBJlnvhReAdn25D36Gg7b3X/cUln2Ima9J3pOs9QX8xTwdh3wMBtIIQ\r\nU+kSJhAfRmww1gyUH2xgmDLL1lpRyX+riOtHoDp6wJ6e6JrLEp33MaqmkaVA\r\ntWEu5vgNc6plCqzBjtB76GnuIwnI06wY1+i2gBgxIe+KWjo1D41Zc6obMUEd\r\nrGM8xyVQWx69NdqakobkkepAtXuEoI6rqo9DdFnAdrIS1WlUlZU9V8pq/D6i\r\ndkYzp1GO4cjcQ08iWuswoarV9gYwktDMgMJ3Yl0FtalPV9ItJMHt/E15Gqzs\r\nrGJBMZRRrDqsnRDT9HRKb3Ii7kMrobzXvNz1sl8oxqZT7E2OM8LrX+FfuGmB\r\nmvSUo6FiD2dLPI8IiqDMbuNOX5trrcP3F1Q=\r\n=VDUB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"charcodes": "^0.2.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-replace-supers": "^7.18.2", "@babel/plugin-syntax-decorators": "^7.17.12", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.18.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.2", "@babel/traverse": "^7.18.2", "babel-plugin-polyfill-es-shims": "^0.6.0", "@babel/helper-plugin-test-runner": "^7.16.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.18.2_1653470197538_0.2786941550162627", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-proposal-decorators", "version": "7.18.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "68e9fd0f022b944f84a8824bb28bfaee724d2595", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.18.6.tgz", "fileCount": 7, "integrity": "sha512-gAdhsjaYmiZVxx5vTMiRfj31nB7LhwBJFMSLzeDxc7X4tKLixup0+k9ughn0RcpBrv9E3PBaXJW7jF5TCihAOg==", "signatures": [{"sig": "MEUCIQDet4qH3y4LReMOmHtUhCaCEbWCRx/1C4MnW3KWuROBzwIgTNdLmKphwQ6xZ+HjJmPrI68L4l7zThZ2Sj5Oe0OzYic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4CBAApJK1Kyb963AZrTbxFJAQr5p0B/GM0xFNJ0ymBUjU1/4wR7mB\r\no6aH/ISvOVxAGF+KMkG30obEhwfmr9E+qYqQ33GumPy0p++TK4uMAw5fZgnu\r\nn74wc2z01yFAqHO71ajuaBWOc6Ml6LA2zo4x15gzjP2M+o9Wx7Zp+PafW7dY\r\nXUuv2WcLYgOwGcXCY//5pVXN8QSJIF5oSiN2puHL67PCjelYD35ip0RD1X6L\r\nyj4c6k5p/uWxroYUWCoPXivnK5Wf2Ro1BXk+C/Bhd1JM3YdAFmO6H8EDDRVO\r\nfQuxsXhvI7z9hwYpijxOMNZhZvj48TyKJYng56Cj/KXA8TyiKBPPoGDSwCN+\r\nkO/pIZ4iwFWREAMNGC0M6bOC3LE5WUsGe1Uulm5gjYnpRxhwyjO0h2RZcmkH\r\nj/+nAjlW86/jx6LuJf6PZW+zOD7+tGaTu+X8OIbjKBV3KduoZ6RmyL587hgj\r\nfueN3i55RznEjcKSAyGClg2rC3JRmY76KNDrCzY2Y4XFCBEaWw85oLFkTKxb\r\nGyhKYeSMzlgbfwB+PILe7I4be322ljVKfER58twPl89fTtkbvdc3sOh2wnNb\r\nVDq5rgnWmzohOizXo2vEqLy2hT3jX5KnaQkeBN57gUby2YUHJG7HECTRTeHQ\r\nlrQRV5ynKAjbIpGG1CkK5JHjK5S4rA/4psY=\r\n=Z3Lu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6", "@babel/plugin-syntax-decorators": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.18.6", "@babel/traverse": "^7.18.6", "@types/charcodes": "^0.2.0", "babel-plugin-polyfill-es-shims": "^0.6.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.18.6_1656359448294_0.5478351377589528", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-proposal-decorators", "version": "7.18.9", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "d09d41ffc74af8499d2ac706ed0dbd5474711665", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.18.9.tgz", "fileCount": 7, "integrity": "sha512-KD7zDNaD14CRpjQjVbV4EnH9lsKYlcpUrhZH37ei2IY+AlXrfAPy5pTmRUE4X6X1k8EsKXPraykxeaogqQvSGA==", "signatures": [{"sig": "MEYCIQC+s4N1vfaafFhDKvmNGQKr05cIFcBzCJYUtRn4yYv6LAIhANo4RpTYdmskkTmAHJupTmr/tuD+k4wUEImn4nAjkDsI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpriBAAg4PTWgcovgZS58vKCSwYUC38Z4myNzNdWw6qcBLV9EsrU36v\r\niBGGFr2QC4GUBo8D/IbqLR/gEsC0xQ5jdfk8nz0kg9UsDjGN0oSP9J1G9U5z\r\nVxiKBOP9ph6764T6LBvdOUlZnPXydhrMgaa3rNnSuRt63XVX2m5bcNtVT3Dc\r\ne2nzOli12cek95oNvYYr2tmaSaUgFkBhdmQBQw6S6tzpqlQDWrqgfwrmW4Kz\r\ntvWamR5V/kfq8qgdo+57gvvK2Uv1aYjlndZIibUJ/dvIKJMriZQsK71y6RBk\r\nEl+jf5UcXvNlqbJ/qFH+rzUrxfNzdqVWvDv8ouZjLG7KAH9L0Cjtl5+JXIGa\r\nB/8pwmiMDOhxn/LQx1POMlD4lmdmBBvtM7FGqXaLMCZMCAhImyo8xRLBKkvq\r\ncXz+Cttoavoqc5bw1aYoThoN4M0xsA9jWCr65BWGnGa9Gbobub9/e7HHsVQf\r\ncSZYhx0MTHq/EDp+2mL6Uibjidq5rpD0ObXGMAchhfyGi5m6KrGGlMA4FeC6\r\namc7SZCHTlT+/A+c0UhD+6mh2Z1JQ4AsbpGK4fV6l+GYoWgd30CZRr60VrmM\r\nBW1tiKmq7RMqTeBZnmH3fq6ySUTple2NR2u1aCy6/LeGC1mkD9SZfqHzCkVK\r\nWdpSICwM7ySCRST4LdPxmDWipIVR4fr//GE=\r\n=N4AR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-replace-supers": "^7.18.9", "@babel/plugin-syntax-decorators": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.18.9", "@babel/traverse": "^7.18.9", "@types/charcodes": "^0.2.0", "babel-plugin-polyfill-es-shims": "^0.6.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.18.9_1658135864225_0.136945631557976", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/plugin-proposal-decorators", "version": "7.18.10", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "788650d01e518a8a722eb8b3055dd9d73ecb7a35", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.18.10.tgz", "fileCount": 7, "integrity": "sha512-wdGTwWF5QtpTY/gbBtQLAiCnoxfD4qMbN87NYZle1dOZ9Os8Y6zXcKrIaOU8W+TIvFUWVGG9tUgNww3CjXRVVw==", "signatures": [{"sig": "MEUCIGYSbCj7xM4ZluqCjLd1cxFY/riQHNqfRjF2dPMWZmsnAiEA2+KvgSXMtW4wVsWAlDrKIUwvZlgIX9fa7BxnIe65lcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYzg//cTXyY69OHFEZ6hedYPDG2vL8CDoVHqGpUTcw2gmS7qCxKQh5\r\nVgnYoaAWIPRPeFFP95KdGGqhVGVcqBaZA6mbWGzmxaEHB8b/Q6iTTQEW7+A8\r\nxu4TmaRnJ9pfQZNsKfLvlSYb+MZ6FVeFfouH8u43Z8lvd6MHfV6E++v5Vde3\r\nU+vdZjDuUhfjMdI2zzhPFpFfczmTi6H48+QM1/y3ft0QGAoMNS7ZcuW1ajPQ\r\nr3nT+sua1Hse/6VRPvx2EW0Jwj1ve5m4OJUd4uDk65jytnWA5gu8u8XSRRkd\r\n9UuRguRhI9RUPMlMHXKk3NCRUGt8WiU6+LzPfdUDJGgMh6h+w/AX3ErQHVZy\r\nzLGpUvDH5GDxNowJ2m18Oa3fGe3CQ04gxcscqhAsw3Pd6XE68qNFpYjy1jzA\r\n7PXw/1m1XsWTotyiIKFbcZKYhMgR3gvDJDEqIKqihxsi0Pk1nFwF2vwIvdye\r\n4Jqx8Dvb+XcuyQfXGyLPZZpnVZTb6aPS+l4BfUajj0UkrleEYFkaHJdN9X1l\r\n3i+b6++xjSP8R86bE9Cfd0vox/Usoo5iH56G06pOHYUwpqantgVDCUNgIo7M\r\nKFCBjxCuGmSG1cG7vdqYlaOVd3CBC4q44wjJcZoNV78+6CjWgv2ghY3rgqK2\r\niGZUjAQHRS0yp0Dpidd07m/UJL7owrcD+rg=\r\n=bdmu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-replace-supers": "^7.18.9", "@babel/plugin-syntax-decorators": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.18.10", "@babel/traverse": "^7.18.10", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.0", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.18.10_1659379600832_0.7389430296041692", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.19.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "5a3bc0699ee34117c73c960a5396ffce104c4eaa", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.19.0.tgz", "fileCount": 10, "integrity": "sha512-Bo5nOSjiJccjv00+BrDkmfeBLBi2B0qe8ygj24KdL8VdwtZz+710NCwehF+x/Ng+0mkHx5za2eAofmvVFLF4Fg==", "signatures": [{"sig": "MEUCID0pszhRverTRDeoI6PXn5uKGC9cRuhuWgEkD/h2C6suAiEAhYzUXhm5w9xQTcs4J8NptlMseEkHvZH9yxXRweXl9c4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmripg//ZFbBk5JuA1IC4cuqdM0qHqkkf+uSRPhCmLgFqrOUiMMTAYyQ\r\ns7ocxAGvCwpPrn1SPEYPNjTtgIXMQsJFyYk6NVFmZUZ/ybUfxpPUWxqGJeLK\r\nG80LXoKWd4sSgfg4RmD9u8kB/S8Ny34CPBIoymkvWDexKEZupL+oPyKXOi2S\r\ndPqBmF6QqIVzB7KPDKXXMIw/pnAazguMhiv0cIm5Ukaidwwed8Y4ZDoTr+vc\r\nis7KaE5lmh3YbGHmUHQYWks+AO/RBMo955uods+UntamB89CeCGfhgWxp4TJ\r\ne195bvSKiTGrJTIv63Uw3U6Js5/wuMbiSMqzOlsJHNmdLFJ5a1YdOpB4TLoo\r\nvUw4j2xkvz4SwOZU/X5ikACClhE4dC/Jwt3YiMfSMaXyoJFXPPsOLIt5tXRX\r\n3qo8ltRq6jodoP8cIYvefBktVvUzvJr8izf1x7RtgWFSKz1+8EflmkfymQY5\r\n4q9I/EtTXLwbYwzepGScrlkeiZJNlSODylfljb0r5j/od0otV06hH79Nu+qz\r\noHFqt0bPn/f6E/TogYUI0WgmTqI6WVFMExuzkZwssWIj7vkyt5sDj93hFKUU\r\nQa6GtbfCSfbO1K7WMtZRh9QXcrzEwrWKznW4UzfDA1zjVSbWk3gZQgG+ctgQ\r\nuj0xJBofNjGYq+Mo+1wLlytXbh6BlQbiSaM=\r\n=UhKA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-replace-supers": "^7.18.9", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.19.0", "@babel/traverse": "^7.19.0", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.0", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.19.0_1662404541022_0.621322298533957", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.19.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "4bab3e7afe894fdbf47ffa86701266104fcb6ecc", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.19.1.tgz", "fileCount": 10, "integrity": "sha512-LfIKNBBY7Q1OX5C4xAgRQffOg2OnhAo9fnbcOHgOC9Yytm2Sw+4XqHufRYU86tHomzepxtvuVaNO+3EVKR4ivw==", "signatures": [{"sig": "MEUCIQCOVxGfWqNsbXNDAJz6VRC2GYN3rx8cqjrcER0gNubNZAIgaW2HMSBrLk5qc/2zwS7+524V9H5g8XWWL0vPhX+FSc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolzA/+KsGD+j+gGFtuHPDh6dOCLyKDi/ZpoZIP5Hatf/RowSswbfv/\r\n8CImDjExp/v9hwAvzvGdoR+vL6hwSpQCbbKi+wpVyBvKxKbIh2kNBYlPcxPA\r\nuwvexshS5K+k2EDQe5UKsZaDHSbO1pO0f17lMzKqNdUXYd5GVW+usB323C9z\r\nZMUvBSa8UnW0A6m560W7fEyyFW9xrBeSS0rQe0hJGpCyT30aepUdGCHuw28W\r\n5bA60y1fqeKnyqi+QTw2nKfY2xFYjo903Lx6NI+oVt5TzzRSOJGxeaGZFeiO\r\n6/tNCyD2K0IEXhfVi0My2lIEA3SPFUgNMREZkAwW+20UuNfvbSwm4+MGtP6c\r\nrYPKHYYjb47IEKK3hWSncjfUbYA5gyZfZ3lYehQ2BL/ynVatIpujAuMCyip8\r\nek03Sp1pOY23iTL+S1COkl1+XF6wLlPuJw/EG5RFOEovtVFaTweKwTjuQf01\r\nEjfZZy+jN+X356KGtgZLykmsH0TgvHeBQKJHOuDRWLTC/lxgOHg3bySMLi2p\r\nCJ9sEtvZjJ5IHWVetd5qLAzMGArINRYNOE9VP28/hpzUOujtGaoDBmsIAHkW\r\nwP8Flo5/oR8wlWFT41SsNNF92sgu23amzUPFmlyxu90SsODbY9pbtUuPm25m\r\najGCJeQWNKYV4Rdf37lGSW54SM0cO9sRF0E=\r\n=fLXm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.19.1", "@babel/traverse": "^7.19.1", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.19.1_1663169357404_0.19872640109779494", "host": "s3://npm-registry-packages"}}, "7.19.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.19.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.19.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "c1977e4902a18cdf9051bf7bf08d97db2fd8b110", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.19.3.tgz", "fileCount": 10, "integrity": "sha512-MbgXtNXqo7RTKYIXVchVJGPvaVufQH3pxvQyfbGvNw1DObIhph+PesYXJTcd8J4DdWibvf6Z2eanOyItX8WnJg==", "signatures": [{"sig": "MEYCIQDMg6V529ZXKNeV3e1+DCOrvPTPkhHgDiNFS7PnJER6IwIhAPFDDKsBFrowEuor6YuvFzY2b6jfM7yWC6kgAXoT4Zo4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0K8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOUw//ZCv4bNgEjS+CtflRpH6j0sdzLYpfA4dNtYv3Vpv3P9UGk4ei\r\nUaEHS4uy/lq1GYVSX9mRzjBAq87UofAcYRTXF4Vo1Ad7PNcFvEi3ym7wPpRe\r\nfC5avlQcsVQWbwJV7P11LaCpQd5tmBRyqKFy4hZJCF9ufHDCtzfZrbKqIfbT\r\nvWgxfXR0AQ00sSFspRtV6J2Y6R2AVb6jOKTbVgqNQDQYtwwOIkUpP35yYzEP\r\n+6QoQg6kP5su80ZqiDUbpm4QcI2jqwHbAHzIekHbNdjTD7GA4/5ZoUpJ3sbD\r\nVlQCv6iyeys8ejq6gArVJMD+OBnFBOC382bCYtY1GcM6j8ZY4lMQGhl7ayGs\r\n36AhcEKw3MrxAvT4TZ66yuyyW8R688MyfzHTQe32kYEYr8QlTAYGKDDPyQxZ\r\nE0yidxu1gIaYhJ4dKD41a9ArBRErzR05WtY1e84QngC57Omt5WD/ztPCG6d3\r\nHiwJZQYid5wXN/I1mQe3f8YvHc+44HY0erlEkT3fULb692n7bAoWK4TlDsdn\r\nHL21op1YpSUHuHssvihI4YRKXiyFmyQmZjPg96Gb5W4N8T2dYIHifctpNGX9\r\nJhKiZtWjjTfHrRlBGq/kRuqDimoCG5mfNx0P0m4yjK13w9IBa2xv7Utu2zNL\r\nZBDw/govzB18/TyMG1MI6u4y47+dayUYHQE=\r\n=AXMM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.19.3", "@babel/traverse": "^7.19.3", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.19.3_1664303804167_0.29225295702371357", "host": "s3://npm-registry-packages"}}, "7.19.6": {"name": "@babel/plugin-proposal-decorators", "version": "7.19.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.19.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "0f1af5c21957e9a438cc1d08d2a6a6858af127b7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.19.6.tgz", "fileCount": 10, "integrity": "sha512-PKWforYpkVkogpOW0RaPuh7eQ7AoFgBJP+d87tQCRY2LVbvyGtfRM7RtrhCBsNgZb+2EY28SeWB6p2xe1Z5oAw==", "signatures": [{"sig": "MEQCIGOuw16uworE+Uow4ud8DMp72upBbkjIojoXB1O6CjdTAiApk9DWsL5bqNPunbSoeV2BHUoPcbBKZFgUE8gzoT7yhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn6A//UKP9gkp6L4Ei0sy2lOYez8xeYncCGPD6X4KFlGGmHEPFUTtr\r\niShtF1NqgWO9due4IThprZS/b1HDygL2umuld1NDq3ciurmEvaWyVUTTUvRV\r\nr4abBGOCzZpbcE426MaSgY8PwTjAlacziWtD4quwR6kk2p3bv8V+JQULlrjf\r\n7kriihTcceDAshy1AOduMMinMHnbWdbW1AIVqFiZUjF0RZmbkQPXZZT0fLMk\r\nQ0oSLaQSrDU1VDeMrJEcDLuuEauoO4b9ZLSG095zPlrwm7QsnKiDSNUYgLee\r\ntU/4r7xvS/HnGTJYUHwE6oeJL6Q/OgeLmfps/0g95FOVGLEQhfOhcODu9r5t\r\nuJcKusppAbZuPEVdMgApJmuF32TG65JHOZ/D0vhrglFet6zDmthoje7YaW7z\r\nSSkPMu7Z0zIhh7gBMCeqlVZoAGihnNlqrA3FuMol55UnBToDHRN5+iV/j+K/\r\nMWDv9Jmt7S7fxgWSpVzSonEt1mf6WceuT2aBoBhhbu9RG4FXNhW5/9Q+NCl4\r\nf563+K24LAOrPBZvU1Myo9fwDRfWUiG7FsAFdA0+hcKVW2gYG7JOD0jbpUfX\r\nLUpobDggzkT+7CZGhD5kfqfw9SAkMOZhWnPpQEbtskIu/07Md/1EUO/2ZE/4\r\n1ZIBXp1CjweJniY5L+FcAZeY+pwHP4v1/2A=\r\n=Hfh5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.19.6", "@babel/traverse": "^7.19.6", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.19.6_1666256609779_0.5308486438191606", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.20.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "3acef1f1206d7a6a1436aa6ccf9ed7b1bd06aff7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.20.0.tgz", "fileCount": 9, "integrity": "sha512-vnuRRS20ygSxclEYikHzVrP9nZDFXaSzvJxGLQNAiBX041TmhS4hOUHWNIpq/q4muENuEP9XPJFXTNFejhemkg==", "signatures": [{"sig": "MEYCIQD+FMnIjDmHX3yd99gpPiJzZIR+bSyuc8oCgZgIL5OsCAIhAIO0oIbehHlPEfhSPPNg26dISHfrlcTcrX876rexkFTn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSGw//UYHNKGVzY9pM8LTn2BesGP0JEwDW5fIuSuWVB2U9h4b7bCFD\r\nA48iDu6s1c6pYweZ3ZGo+iRg18Is+C+ORYwawvvG9mwVTInAQ2lCxYSKl8bO\r\nUHMtI06GBQSxU3AFoUe+6pkrw5bTkQwIiumcvFBs4TIXw0N16ozdAfJj7Zch\r\nrhcHpVM0uSgPtjlV0Px6Ev4cDTn2RScMNojlE/F20uPT8S5Huy0IVbrjMPgt\r\n1KU2dV3QVaor0jK2E9QpXwh7OtTf4qJCqnkqFE7jvf8gkC0fnuNEGENHNjGg\r\nw3Ik2AQoChAP5geCF2jniuLISRNVtTiQUzZXkZfdeaIKSN9zDajZRrU3rItU\r\nDqQpYcbeJqAcSgKZd6lcQ9spV4BxHg54elSPE5yWxBtPMQ3BpTG/hstvUtWF\r\nOq1Q2e4006XvnIGgdALYoqZTAs80RJGf5hVGJ+sVeGqrrcZifENUUvPVpl0g\r\n2AGWErsXmwigv4w+hBCdLusOh21tu5Dh7r8f3po3ixRXUTMmjDscbpUPL0pq\r\nmkHOd1UxH+zhSIam/2P9N89phpI8TWg/rT7y/znAq1Xxc7NFe21jlkh12sdU\r\n2zRK9np+tmJf+2jJbXBb/xdVEHZmF9xWCfZ02zCJyMCW14d8RaYdMSFObLW2\r\nWf8DKw3nEXuqQlRqK5m0vG9l44k4GgzF2Pg=\r\n=bH2l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.19.6", "@babel/traverse": "^7.20.0", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.20.0_1666876744835_0.7896562654982491", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.20.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "1c6c32b2a44b154ebeec2bb534f9eaebdb541fb6", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.20.2.tgz", "fileCount": 9, "integrity": "sha512-nkBH96IBmgKnbHQ5gXFrcmez+Z9S2EIDKDQGp005ROqBigc88Tky4rzCnlP/lnlj245dCEQl4/YyV0V1kYh5dw==", "signatures": [{"sig": "MEUCIDV4n0Y0SbfqDmIBAyHFQfk5qULLr2neNka7MEdVkBrNAiEA258BKHeH4g9QBT4VX9fbC5rgF/zj5s15HgiF862ocWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV4BAAnxAZQWe9K0MFo0Ki2+mHXEqVBlg07N7zGDHcuZDGc+BmOXDA\r\nQkRjDOqNiGmPIf4l20/fJ+vqVNDLwvIcUyMSxXsMgjzMVYO3I+8YO/Rq6PMh\r\nEliM7Q/77e2KbAxfctIz8rw9hn+Be8hqVYi4uf+ctPpIagiPWKnDrwPQWYTV\r\nPv/jqOCfFFYXLRI5HDv6KiMSdsvR7Vjky87Xh08elxYIHMwbR0HTc/hubUdO\r\naSi9jno1yLru0I7LQQ8yBbnns84XuosUw2fLkAwZMSRGRPQtgR1hDwr/84xw\r\no8H9jmJoouK0gDUBY0/DWTmdWoBUltkwxRBQU2uyGMS/jxgiXEvJ8CgZxTG9\r\nm7QrPNRKTh0QzBAan0B0cd1hsdPYsfnu8VS87u1FSH/bq56nJlbkDs2eQe9n\r\ns7+tEg5yohT8KDOAB+LRDLnOSU01jbnsBYTTLah4pSD5YwXenxWiqCmlrfqR\r\ntWe6DRcNtGbNrQJZZ6Ht9gx/Rf8eY7Zp1TyhHPZauht6AD0eG7aYNBtU1MxW\r\nbeixYZgWN2VuoRGmoYffPTN2YTAECQJzUghmXamaPnogPKte+MsN4jF5bKJm\r\nQM68zTaJzSPytNz66CmwgHSZ5K/M0EXKIo/nXqngukf/dnUawCY3gy1aWtOJ\r\nQDwBP+6fBsmqaA8zCrIB6xeBDU9u2mBW3aE=\r\n=ZNLA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.20.2", "@babel/traverse": "^7.20.1", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.20.2_1667587866650_0.24684083282373614", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.20.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "28ba1a0e5044664a512967a19407d7fc26925394", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.20.5.tgz", "fileCount": 9, "integrity": "sha512-Lac7PpRJXcC3s9cKsBfl+uc+DYXU5FD06BrTFunQO6QIQT+DwyzDPURAowI3bcvD1dZF/ank1Z5rstUJn3Hn4Q==", "signatures": [{"sig": "MEQCIE1HyrHnAdhG47rVQ3QuCFYRFrp9IQ0iql0lzMeJ86lkAiBX+TU9/C0Rip1aRLIisAoSkHlGdJAW/AgZQ6+04x9n6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjkBAAioe0b2teUZzk2ZCs5sP2LIa7cAxCnae8uie2ZC8tEu5x03Vt\r\ndU9rXRtq3utH47HTIKdWIr7rP5Rvh84nWDiE2iS587H6yLAo8sW/l4kJJn91\r\nHs+gwxGl8Kr7LaWxf7frJR0CYnLxFgsKDHMWbeMttEaCglaxOLQS0sI1exn2\r\nIfYz/G1xWawqyV2FZeD8EhTTaVf9T6RSYvzjk057KQq8KhvkJGGiYMC8yfCj\r\n/mw+3FjpwzRwzXgOc9jlzD81adh+jctfF5vPwvoeKVFLUnMuHmXm8fHYlFyq\r\nV8EAFxhdJ7ilkCY6ulSSm2qofvxAMuNufmiH9V4YreHwibFoB10GWF0rU2ya\r\n5SwEUu6KpKLSM/P47wHQR9V1A9ADhVLecQD61etm/aV8EDjHBuGfIpvsPm7H\r\nQRHD2uwOfIaQ/gb2QgkeP1SlamXheaXH5bpigVfvpRnft4pMPyakVRebQBeT\r\nvJH0/wP6DUni7w7FnntwJInaIKJUAPCoTQ+XJA5h7khMDv8B9E9wMdCc7tBg\r\nHY42g38fh4Rbwm9naO2xsNw4GstmN/QMVVdD1Q7ftqScruKRxX24iHHQ0p1v\r\nQO2sLS9zZiUcSwL+Fyq2jv28NuBQftgClh7SB8w/zEtLXV/Uk1dkGLYe2coo\r\nj6MacKcAZ4KXzlCuuQ6Lm2jefbXcSPCj6h8=\r\n=VHT9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.20.5", "@babel/traverse": "^7.20.5", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.20.5_1669630367433_0.6020265014012598", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-proposal-decorators", "version": "7.20.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "05d37453c2ce818f3e47bbeda9468c8de947eecc", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.20.7.tgz", "fileCount": 9, "integrity": "sha512-JB45hbUweYpwAGjkiM7uCyXMENH2lG+9r3G2E+ttc2PRXAoEkpfd/KW5jDg4j8RS6tLtTG1jZi9LbHZVSfs1/A==", "signatures": [{"sig": "MEUCIQDvBgtDB5oqjhw+wP2rKr2OYX3pJ8mW3ruS0elatAMnewIgVMmvx+wKa0m2N1UXYZDEgyY/AJMM151yTNX4Mcptklc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraaxAAmC1CvpIGoF5/ODmsxPOacNwijg8hf/v0Uj10rOr6aVVX8rE6\r\nGuwqzD84H4IkQ/Z408RhHy7C9LM8bjPO+nPaUjQRQn0MXfDEm05nhBp4iu3V\r\neibwSzQa5RKyg8CLxWxXVgLxURYI9H6BqrH7VrJSdLm/5P0PbSc0BXKoPDyU\r\njqMy9ZPWOv/B+1o083YSM3XQaOaxrxE5N+EuxDHKsv5fcX8lSfJFT80B0ZoM\r\nyxtH/pVr9wVMtT07hTAu40QFDqp4hrfngL3PFkLb56SjI+6YGbyhcYB6SgUj\r\nI7/nK/DGKtXRCUDUPg+Pg60AGBeqkXNTdmK2rjcHdEFkMWlJyXqEVVgHMN43\r\nMOKfYKQtgJDMUOJuG5JGKyt/kuB0L7YDM65hMWxlBr+xBml3O0qvathPXZ2X\r\nOAghKSwxHjMIj4KNip9aMQdLzo8X7O/SRpq5NhIjOkzE31OSl1T0GWTGT/ob\r\nZHeA1QNQ0iSoTz2hNi3JRSqDC20ZN+AEG8rO3relOW9blexdWQ+HdYkAnLw3\r\npZTdIgvmA6xPKckrIxS3LkuTkzeMzhgCk9WTPBUNNWtH47tqfAF7w5RK2Fr0\r\ni434j8BX85zSQ2NlL1GNfPHVlOivtAiO8GBq6UgzKEFesyCbTQ2YACbUHtSV\r\nwLg2CNwsuEqvcA2n6xIziNfHfi8sT/bPcWQ=\r\n=itLS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.20.7", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.20.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.20.7", "@babel/traverse": "^7.20.7", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.20.7_1671702343414_0.3051684347954571", "host": "s3://npm-registry-packages"}}, "7.20.13": {"name": "@babel/plugin-proposal-decorators", "version": "7.20.13", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.20.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "b6bea3b18e88443688fa7ed2cc06d2c60da9f4a7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.20.13.tgz", "fileCount": 9, "integrity": "sha512-7T6BKHa9Cpd7lCueHBBzP0nkXNina+h5giOZw+a8ZpMfPFY19VjJAjIxyFHuWkhCWgL6QMqRiY/wB1fLXzm6Mw==", "signatures": [{"sig": "MEUCIQDILZXk+zyq8dQiR3IFnSkkQvS/3NkxMPzgXrDvw0/n5wIgHyDChVch155d7DDUA8pwHd1yOBbcaGoZzzqP9QjUdgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy/cQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrq1hAAiufOFr87SQBf2jRxVdVVepTDCMc6EMAmLYwMdWuSlDkVSZRl\r\nf5zxZyKlBMWmqQ+J17LazsE7ODKOGSucJFQ30kyRWnhZQLKJMxVXbH+21HC2\r\njZ37/txY6urtPNCOYIvzS365OIDW7ESIu5ltVbMSJXN27idtAiivLRMcb4sW\r\n3oM+iB/tAVWCwx+hjeQYSZS1pWoSrFFG3Y9hJY7uvAsjuM5/RamxxatneDU2\r\nMhE60X4144Hjr6Ir+hQUUiVgXpvPwhoD5Sg+0dcZHqovTmaFcjm0juLS6iF5\r\nxG+PTz6wOaxTCe34nahXeSKN/zAwiwApoOuM4gwXbdgKHscYJLf70YRAyKpr\r\n+e/mvTxmvwkHCbQitNpXyAPnKH5LOjXVQpBZQ6RB/IZ9xwSvu8hkiVYthLvU\r\nXQL5kSR0InA0rmB7emdD5zER8tG2dNfdKAxKOsSwfsvcDZ0t5t9rbixoKiZS\r\ntIy39EDKIrcr5cuDQyLA064b9GiU7yIkMj2gV7NJ1seemaPyKJ7DziMi5sm9\r\nIn+DGb48DdrEDHCN8FnwnqXc0qEP2Fm7k+LnlfeebHpYzIB+7o+5gSdZYnsl\r\nUi49Z1h5ruKpQGsc+OV3T3TlTy/kaRCoXkrAeY+RaZFh5W2xIQW4H665kG8S\r\nnNVIhURKWQBVk1p9fMz36BE4Jx+d/hqapL8=\r\n=cPaU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.20.7", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.20.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.20.12", "@babel/traverse": "^7.20.13", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.20.13_1674311440279_0.8246626208822327", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.21.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "70e0c89fdcd7465c97593edb8f628ba6e4199d63", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-MfgX49uRrFUTL/HvWtmx3zmpyzMMr4MTj3d527MLlr/4RTT9G/ytFFP7qet2uM2Ve03b+BkpWUpK+lRXnQ+v9w==", "signatures": [{"sig": "MEUCIFn62dJXcDyz1eben3huzW/oMvY8Imm83xpBYZN5AV9EAiEA5HNTrTUsn3gZopW/QkwRxf7fq+Xb79oshEkx4mVIOtw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEpA//YFRwjZkD6K8+e/V9sx0BLoXG8OgnadrkUCu9YINMVi0UEE2G\r\nsBb8FwnAL56yMAeLe2NxxHx+dNMehwR3T/XL+ivVABBM5NGdawZylePgctAV\r\nT4RYVL5++REnA8YYbE9aOAF1gDIlp7s8ASWz7BI+vA0kMJugnqdo+kMX0SfG\r\nYMMJzbwef9NZiEAmRtBfH8W4qcrQOL6adW/Rp+kJCHuJXXj0x08IYDVZp12v\r\n3elInBhywd7vbFE+clscxqjrLAl0N5TZz1xkrCfyy26zkra93x1m0t1Gwe3y\r\nr8smEFPXfsumMe6++flwjXLTfOBA2pd+LmBBQzOKv1gsK79GezGa1rJ7FdGO\r\nrIOToV3emBaFBUG+93/HxQtazUsgyq9DtIpfcQ5N5tI/J1YOMeqvUx/ospTp\r\nNnQy82jZ7T8X2visnDSEJ7Wel18SqBA8Purf0pEXR/juetvaLnOfUQG3BcEG\r\nbrQM8ohMokG6jogdDC2JPv3tvKRYUrMnaCjVILx15Frcu02muNfvwZ7q11lV\r\nRR27z0/c8gfwSwoZ6RGaTN6aEmCSw1smNN1tB1tx+4zWfE5P4UlKmB/T9KI0\r\nIQti1rFEigODgAgGT8GBTEHje07JFRM9mKYutOko512R5eEuTtZQmBWlTeFJ\r\npWEp9+aRqcG8mEpcRVj+djZ0aJqFtKtEcdE=\r\n=7+aR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.20.7", "@babel/plugin-syntax-decorators": "^7.21.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.21.0", "@babel/traverse": "^7.21.0", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.21.0_1676907078921_0.7972582152993368", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-proposal-decorators", "version": "7.21.4-esm", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "93ed3bd209453992e8e5eb6940cb5a805036bf16", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-ed8DFK2v83tEIhjCA7l+GnhjB2JR2mOFzoggCwpZEns8tq7zeAKziHN+MuM7M3PgFir1nxIwnsiWb+TvmbnYGw==", "signatures": [{"sig": "MEYCIQD4aCT7Q1mZLyBOq4LmYeMCWBckqbeUWvAmppL2w5IgxwIhAL9TJnuPFi72Dr0aaVqy87OyJ0IPy3oBXvDLyy8joA92", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC2xAAhFj3pemKsKf2ZbpMnmrZjnI/0BIuAKZLogmM+K/1CW7/fWD7\r\nWmaKS53xHV+L8RQ7G0RUWRjnS2IXAeM3Q3K0ABZWRjHnUawifxz2D2lpEaof\r\nbGaiTP0ZrFglO8fAbdcKHd1zV86+k9ndBxwixM040fRfHfAcs2WofjPUYMeH\r\nLkuHVgUejdhN8qAZWfubbOk5kxDb2evIBQmqhph9w+5JbQD76Rd+OcqeIBjU\r\nnFOOrxog5ld71A46LaYu5kNhDKy/TaS01Xi7L/LqGBDfhTrf7/2OisG/wTti\r\n9Bjbd3e+13y8iwyk2rrWSBDh8IefG16Xy/KN8PhaQGOWMjHQeNz8r2reFAZH\r\ndo+ZyDul8YMcqPK2NYkTt/QL/Bz3fcLSrjV0m+HB45vcV1C3BfH/7gJPmzKh\r\ntKdP3pAMbJTFL2rtyDj/U8F9RhTdz2SchE28p5IN7nzVJbh/VcuZuZts3Q0h\r\ncsfcnwIsCdTxA8ZZCy6fIVBbr6P4XOnqGjI2wUbsJ2pRKkAPNp6rEA7yYFDG\r\nJ7h3Qi2mV4veOiIl+ptmQrgADW8dff2kvoIRIHMPhPyeN3WXKex1Lawm5hPh\r\nbapdJxKhD/qkhof6+asf7G89aiHS12atYXkeQx2vH4N5erq+kvCZhFUAR+7O\r\nXn3yvQk5D1ndI/P/GwCV2v0d5GYfT/lTEvQ=\r\n=yqLG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-replace-supers": "^7.21.4-esm", "@babel/plugin-syntax-decorators": "^7.21.4-esm", "@babel/helper-split-export-declaration": "^7.21.4-esm", "@babel/helper-create-class-features-plugin": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.21.4-esm_1680617401571_0.18380441296855765", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.21.4-esm.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "6a4fc6ffb43a6062b8f4533ed50ba6cd2736701e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.4-esm.1.tgz", "fileCount": 10, "integrity": "sha512-uYoGXWBVAhQOkL+ZQC9o3DOo4LNf12kAeZaADRDPjDu1nNLqd3W+fAm1C6W2b2CZG0io3zRCsbqMg5GODUnGBg==", "signatures": [{"sig": "MEYCIQCgmT+LQzVVJ6pVNiuthQJduAFPZpeCDezltkH1C7EwMgIhAPk1f4VHA4/YsbvzCL8sQUiCTP5n2A6nPMVFZf2EItvz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsfQ//fquuz5gPQ/u7q7dBHEhO4CpbSGxlrdl/Nk/QuR/eE9LmPqdy\r\nq1t5BUuydFx2HloxMerVkapZouetBRpFN965NyGVED/1leQsGuu3TYNke1E3\r\nmsUbJi5KoN8tqBvc/hl2L1SY0mIj810jK4U9pAAewXptUGjWgCI06Ofzoa4x\r\nXJu/LVNHW6ytec389eIMavk1IXjEzyB+ZMzreA6m/mWTljuXh2b7hkqY28aF\r\ntqQ+ENfVgluBNmGSk4ySB9I6f8cD4D7s8BTX0GuRgBO5U7KdKv65Z1oh+9vw\r\nvFjZ5vlKZ9b6m/BZT+ja++JASCfGBR0RGN50CHo6Hkns3f6l+HgJVcgQ/xaU\r\nCytevO3iAqbvwNmaVBz7SDB9IheoQJY8Cembx9xGrMqlJo1nukMLT3B4oPc5\r\n/MJ8OisQoA50zypDYLF0TEciBi0Yu5ab5yJghOqy0y8TJrIGVCnVVtSiyo97\r\njK1Sm/fpk90/ilkZoK18PgMegvhyJnCt1rrcuTEAQHSvtrylUSpathUAux9v\r\nfuFULj9RcEhfNRkccCbBKEyTdc5O/vlbTRrzDS5f/j7lplSDfLFb3iWo9EAT\r\np/pvlIv0GwNN9priNM3FGnxJeerOgQ3um9pT027ef/R1H1y6sj/p5ceuF6Nn\r\nHePXTGvLh4XE0oXr2PMeuhT5sUWthR51hn8=\r\n=DwlG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-replace-supers": "^7.21.4-esm.1", "@babel/plugin-syntax-decorators": "^7.21.4-esm.1", "@babel/helper-split-export-declaration": "^7.21.4-esm.1", "@babel/helper-create-class-features-plugin": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.21.4-esm.1_1680618118925_0.5751863872678715", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.21.4-esm.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "64ba6313ef128e78376f9d68d7ab227675ed99e0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.4-esm.2.tgz", "fileCount": 9, "integrity": "sha512-RiyJVVwIVtHZPgTg0Xi107ek39Q5K6RZmECi4u2OFDlzhUCCViqPyF/Wv1xlxor1s7ui8rogBvTJoKkHoHLRqg==", "signatures": [{"sig": "MEQCIGRJRdn6+UniIjtECg4p/q5iTZu4DUcRpxM7TqXynq1tAiB2RPXJQq0mEZcreBILMsL4KDPAAPFRvPqXuI83cS6qng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4mg//fPgdim0JwuPO/rzoIOp8XZHGRrUoWJ30pyA26E4JTXvhalg2\r\n741/Y0qj9lj6IK7aSsXmbLXaM83ZIIhAhTWJNWDyfBQw7IKsuvxexYsz9lF5\r\n7/tVgIE5tajE0WVrj8/SiHCaxvBfu5V0AUFTHACxG6Inqkx+3Jcf4EEp+nBG\r\n/MenvrbPF+RcvKxgGOqzZHKJjxudvk1XZuYCovuCd+BgEPJh/2DRei2leZHJ\r\nbxbIlR5Xm1rtzEm3jwUNXCSzTaWtgyf60RNVG+tPIbBZciNuMmhUHPDYc7FF\r\naO10M/Lw/pZmWIY/DHsTi38CCkqhA+Q5pg/Lyebo8lVLWTlW91KWZMgnnJCu\r\nkQhIQ8mpTlsqkqxtXhzqlEAbDklGsrpJGOCl9tqE2EQoCB4rnmKgl/tU5xRi\r\nPlmBwbwTlSjf4ScIs1AV5dyFU1onkoGNAiC+UAdDvez/u0CAQyfnX51BaLu7\r\nDXulP95+4H8mR7phJD1YC97cOxTvuXvVT0YbgIlwvOZaLaxvhI1T6uH/pRmq\r\nnd+489eLhBWC/SfO5k00REqBMyafnA/6PMDbjfbxcjOISQ//iigEKbc08FO8\r\nCZmwxhmFygymHSCBzPEe5hL+w7Q+0wuf2v3pJx/hHL6JkbJHujP/B5nPKSaY\r\n86tM/oysuHASgWm4cPfTt5MI0v+PfbukmJQ=\r\n=hhGH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-replace-supers": "7.21.4-esm.2", "@babel/plugin-syntax-decorators": "7.21.4-esm.2", "@babel/helper-split-export-declaration": "7.21.4-esm.2", "@babel/helper-create-class-features-plugin": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "7.21.4-esm.2", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.21.4-esm.2_1680619204946_0.8776443069386228", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.21.4-esm.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "022490feabd6fd694f24441f1baa5a50c5deb3ea", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-tjkLVDhn6oogwqMdXUVNVkpglgg3gMRyH+E91RrCHPoKyczDfvtWpOLX8yTYB9Thdl/TC7VydEsfZnlK55Fang==", "signatures": [{"sig": "MEUCICFJS1ip1giC5vYVTFzCCcRTbn99pOUMVjXOq+lqFNZCAiEA/B82x3QkIODxfK6l2lqYNPHJmZlD/U+DY5kwXFgRkaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDquACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU9hAAnllXWrkqaqal4YYsXeiOrblAAh7Z7Tp+F2T+CnIsH/WIVZC4\r\necsNyR/I/x8ojwWw3oBJlQGkq2WgMn/6KYna2JjWSOrw1T0SUlXmkNwCzQ6b\r\n+zrLYnSpqIc38zgjQ4PiFcftmnDzNbm0xYNE9GkJuJ2WiVVy93GW1kqe8Jgj\r\nZiUVkTh252g9lFIjCSvytDVEkQFBmA+Yi1YHPVj8HmSzoNl3XVaGPyxjHPC9\r\nLsqxbgD3xF6v4LS+rUUQy+XmKdVbCeOUjraRCalAuEMLjcIjN3erlOwxdp6h\r\nqdclc6L5UZxP/G3rVJD0ylbhM2XNAIZFo30FZXnQKWgc1DjzC3ZE4hBZK541\r\nRTp4ozzYFyZj8SI7h/6eo7T+/jbcmoAu/EHujvZ+ty9DPIYq7v7h/FoWXE50\r\nP+UIbS5BgIx+Z0GrzM2eqQ3pbKgxzM6RdKss0MMVMzC4tAt+N8F7wfY2MVtP\r\nHt7iHUoz5y9ISkkbF1wdy1MeDEhGKO3+/5Cj8NC9+RvK2jsFC175xK5Gkf1P\r\nQIs2SGA4knP/4BPmE/2JU5XEA5rYRgYQyYrLL6REVmxlLcOrLHRvI3pNm5si\r\ncwjyday7nApE0m3HgOzJMicy0WA2H2t1JnFPawGckW66sqLokD3DQ0nvpk5N\r\n7n31c2Bj1QmcEfcLVU231s9fQ7zlJq8qfXU=\r\n=kLet\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-replace-supers": "7.21.4-esm.3", "@babel/plugin-syntax-decorators": "7.21.4-esm.3", "@babel/helper-split-export-declaration": "7.21.4-esm.3", "@babel/helper-create-class-features-plugin": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "7.21.4-esm.3", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.21.4-esm.3_1680620206689_0.5515961863868366", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-proposal-decorators", "version": "7.21.4-esm.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "527389706db4b60a02e8fb7de3b69794622b36ef", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.4-esm.4.tgz", "fileCount": 10, "integrity": "sha512-2YijnEugqLbfHYTqvHPK/dUFeDCQHHvWyXM6gNlRlY0iMDXLHK0NDlqREUG4za3kY29eq/t9aZxY+uuDYGv5Qw==", "signatures": [{"sig": "MEUCIAmPIXZ9rx96gLck1YcBQFudGk10OJRqmrLoszudJaTQAiEA2XtzeKdXJqQSRXchT+r9eei2Bj8wxCxtghJDHrO89Q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHFA//SALi16owy7Xedd6mfpIi/qlYHVCPn7BDTtNdicENZ54vFuYL\r\nvSq1Ax+L3aDSKOiVY+HDlHoYSIYKs1mzpEfrI6K7WzlQzVhpsqMDN2xH5jbU\r\nbwbJI6/JdSYJBlOrgxrn9jUj0F6UmwCNTxSUwecWeJqnn0JQcaRl3wiYksFd\r\nzNfLuNMI/1d7xTXA2JYdkS4EZzCrt+kPPLIwlr0JfiU91IGpejqV/wqngo/4\r\n9+oYBnstizXJ8QTFr9ueZmS5RlIc8GXKZQumityUCTEOFVFhdDqKsPJo7Rnh\r\nzzP82aWNvqEQLIYyoMyvj7bEp6TPn+R0iHSPWOBatIPbnTLv515lKwJuk6Xr\r\nEkD+TjF9JuHiH94k4R+lyLogRCLnoT7yMWyElAxNjzJnvxGiPTw7WmOTPmaO\r\nKbA2t4twtYhcGHVwTKMAYp1YowC9mHOL5eAZxMdFNSkRzaxkZTwn8irmnfRx\r\nMCihvnI1NrNhD5A9p6QyHBoEPPUwlRiaktFTmQWr50zaKSb7t1NWtK8iVUfp\r\ngCJ1rycGaOkcCsEeEQtwSXXwhDNVWY4PsVnWBeXr2gWlZKnIIJ/laQR2YKwU\r\n5mvC1Uqv4h1qqvyQEkmxd+kka1EBXZ7SgxM67SkHZxG0iEgq7qjcHJv5+ci3\r\nrCF1PrZ1g/rA4cY77fMqFp458vKWnc6n70c=\r\n=yxzG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-replace-supers": "7.21.4-esm.4", "@babel/plugin-syntax-decorators": "7.21.4-esm.4", "@babel/helper-split-export-declaration": "7.21.4-esm.4", "@babel/helper-create-class-features-plugin": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.7.1", "@babel/helper-plugin-test-runner": "7.21.4-esm.4", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.21.4-esm.4_1680621234449_0.6841458488020777", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "de0b001bd6b896e7c439c56483c2c8f62ca5a68b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.0.tgz", "fileCount": 10, "integrity": "sha512-LPmF3WrlMtI1xeccz9+ea0obboOxABDCd694sks+zysz5mYe8/4x35nu0xNyjDHdWH3dAjGhPZeUNOabg2824g==", "signatures": [{"sig": "MEUCIEWg7NhuP0890xd17xA2nKHZlcVy930Du//f1kb88v31AiEArVMtB6FRfhI92n9BcQD4zwVlhv2YbFJEeAsGof4dtn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136618}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-replace-supers": "^7.21.5", "@babel/plugin-syntax-decorators": "^7.22.0", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.0", "@babel/traverse": "^7.22.0", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.0", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.0_1685108748684_0.8433881298748447", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "3502c0f8cfe0cdb79b62102c9c9b111309d942b7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.3.tgz", "fileCount": 9, "integrity": "sha512-XjTKH3sHr6pPqG+hR1NCdVupwiosfdKM2oSMyKQVQ5Bym9l/p7BuLAqT5U32zZzRCfPq/TPRPzMiiTE9bOXU4w==", "signatures": [{"sig": "MEQCIEvBKjFWuSEi3gUzlZfqpx3Kx/cHcX+/XY2SFGCkdcMQAiAukXT6QnsCIQfxzf9ozDDTMiAT0fac6A4xrgkYXIzaWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136800}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-replace-supers": "^7.22.1", "@babel/plugin-syntax-decorators": "^7.22.3", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.1", "@babel/traverse": "^7.22.1", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.0", "@babel/helper-plugin-test-runner": "^7.18.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.3_1685182268815_0.48287008477893045", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "dc8cdda048e5aea947efda920e030199806b868d", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-h8hlezQ4dl6ixodgXkH8lUfcD7x+WAuIqPUjwGoItynrXOAv4a4Tci1zA/qjzQjjcl0v3QpLdc2LM6ZACQuY7A==", "signatures": [{"sig": "MEQCIGG3axk+EDdvt6rGRGb7C6uLD028i8dvEAMsEyy4xTOZAiALR8HOfo3YGf33t8TH8Derf658VXhPj0aaxxbvFwpcfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136800}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/plugin-syntax-decorators": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.0", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.5_1686248508069_0.4739738659105166", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "47fd392abe90f715708dc9aaeafc1f09d8db3fc4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.6.tgz", "fileCount": 9, "integrity": "sha512-cgskJ9W7kxTk/wBM16JNHhlTkeyDK6slMJg1peaI4LM3y2HtTv+6I85sW9UXSUZilndIBvDBETA1BRoOYdxWKw==", "signatures": [{"sig": "MEUCIQDHvUZZuZsB7rErPEmovR3+v2SBx56uoUaJWgraO4DD/wIgFxTHyPObHQtKQ73u6vBphoowx1ppaTyludYcGZzohXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137064}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/plugin-syntax-decorators": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.6", "@babel/traverse": "^7.22.6", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.0", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.6_1688456940421_0.9426050567024526", "host": "s3://npm-registry-packages"}}, "7.22.7": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "9b5b73c2e404f0869ef8a8a53765f8203c5467a7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.7.tgz", "fileCount": 9, "integrity": "sha512-omXqPF7Onq4Bb7wHxXjM3jSMSJvUUbvDvmmds7KI5n9Cq6Ln5I05I1W2nRlRof1rGdiUxJrxwe285WF96XlBXQ==", "signatures": [{"sig": "MEYCIQCoFzP/8TXadMRoArRtoz7NATkOPXqrnFPNVKlnIoDDFAIhAPLMXRdifrTChhvzKidZZnSbuAX9iC3q3I9lHm8BU1e8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137064}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/plugin-syntax-decorators": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.7", "@babel/traverse": "^7.22.7", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.1", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.7_1688634236469_0.9468441440381326", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "7463532eb0e7c63946e11d7b25102a766350f626", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-THJ1ZlklJ9Sz8sMOQDiJlAAidelYZjwd6MNo9Lrqbsy79ngQogZY34RpfvUyJm01PLbKX/RA5gCqN18yxNKT7g==", "signatures": [{"sig": "MEYCIQCEQVuhhOMtKCKfV5bxa7YJoeJGHKmYcITtubNO1R5/VAIhANUeQhoXestyVF4fu5Y70pN09wKYU4Xc1iIjQNZi9W4N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-replace-supers": "^8.0.0-alpha.0", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.0", "@babel/helper-split-export-declaration": "^8.0.0-alpha.0", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.0_1689861630365_0.456068413021135", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "9a81913d5d3a36d701622def14eb988df7e6d24c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-jqFsaviy21BPoj9XiEdg99zUdoee42DCetKpCd01o05pPFk99MB6QQ6+Sa3nfO12A9eCFFmtMvl/CNHk8eErYQ==", "signatures": [{"sig": "MEYCIQDc/QNn0qxZstVjkJhWDNjhoxr5RQR21EZ/cRxet2+9+QIhAMjKmbDad4GXs36ewL97TJZB/maTEqfzyP537GoENERU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-replace-supers": "^8.0.0-alpha.1", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.1", "@babel/helper-split-export-declaration": "^8.0.0-alpha.1", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.1_1690221183821_0.18004672489684426", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.10", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "d6a8c3a9018e1b13e6647f869c5ea56ff2b585d4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.10.tgz", "fileCount": 9, "integrity": "sha512-KxN6TqZzcFi4uD3UifqXElBTBNLAEH1l3vzMQj6JwJZbL2sZlThxSViOKCYY+4Ah4V4JhQ95IVB7s/Y6SJSlMQ==", "signatures": [{"sig": "MEYCIQDBTKn3Y6mfjzl2gI/IdmEajL46QaT5baYHzZwv8COKRAIhAO14e9YKqHDEVwOsJnyPPPcPDFyQGwvZt1j8XG86mdex", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137148}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/plugin-syntax-decorators": "^7.22.10", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.10", "@babel/traverse": "^7.22.10", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.2", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.10_1691429122019_0.773297871155556", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "53237d4639b1ed7ad1ed119ced61c565168ec110", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-WbmrPlBKkbNtwE37P30OvUB8uxyc4q1lVfacCrmyqvuZInPsHd/3MNF++sMMeFPbfBb8V4T3/I20T7PdhkpTlw==", "signatures": [{"sig": "MEUCIAQDIybdNIEuc5ZnY6k24kVxpDY604SB1tSMMQOzWp4PAiEAjfHK3xuEl6T4z8hsW1JEJzIPGgtGrUcwv4v0471xsyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-replace-supers": "^8.0.0-alpha.2", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.2", "@babel/helper-split-export-declaration": "^8.0.0-alpha.2", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.2_1691594124940_0.0821019993755927", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-proposal-decorators", "version": "7.22.15", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "dc774eae73ab8c28a644d490b45aa47a85bb0bf5", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-kc0VvbbUyKelvzcKOSyQUSVVXS5pT3UhRB0e3c9An86MvLqs+gx0dN4asllrDluqSa3m9YyooXKGOFVomnyFkg==", "signatures": [{"sig": "MEUCIQCYFdGvCKTAPJelMqCMhZ3OaxWhYuj3wUPLxKNH23BjHgIgNCvYCE4hDrAoDNkXQ3F2DZtzXKUmQunKHez223uvMlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137163}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/plugin-syntax-decorators": "^7.22.10", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.2", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.22.15_1693830323580_0.6972581697511602", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "66d9014173b3267a9ced3e69935138bc64ffb5c8", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.0.tgz", "fileCount": 9, "integrity": "sha512-kYsT+f5ARWF6AdFmqoEEp+hpqxEB8vGmRWfw2aj78M2vTwS2uHW91EF58iFm1Z9U8Y/RrLu2XKJn46P9ca1b0w==", "signatures": [{"sig": "MEUCIQCzdebNPfFsJb16mhnVwuysfPda2dgHYIOFPhiVMdXf5gIgPg8s7QZRxnt3dF8BWFNvHliyoFi8Qju/JFEl0rvunCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138836}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/plugin-syntax-decorators": "^7.22.10", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.0", "@babel/traverse": "^7.23.0", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.2", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.0_1695629431566_0.20640184413453122", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "ec422548144cbfcbbaa9ee7a025e620753d91ebe", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-TADya8v7t+Ub5H/uUSbPtBsusFpC8KjoZCCQUz+t6Ss1mgPqEiSUwHTpfdtiwfUYfHAVrTvxWhp37ic8TatA8g==", "signatures": [{"sig": "MEYCIQC+etUzC79gBjMib2OO76cWsKZR2+fD2/xuY1Ngu7JzYgIhALil+Jl8/VXTJn0PrR8q57kZ6EPdp1aCRbpbxrzpX9BG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136380}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-replace-supers": "^8.0.0-alpha.3", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.3", "@babel/helper-split-export-declaration": "^8.0.0-alpha.3", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.9.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.3_1695740258270_0.6015821313947567", "host": "s3://npm-registry-packages"}}, "7.23.2": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.2", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "0b345a5754f48309fa50b7cd99075ef0295b12c8", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.2.tgz", "fileCount": 9, "integrity": "sha512-eR0gJQc830fJVGz37oKLvt9W9uUIQSAovUl0e9sJ3YeO09dlcoBVYD3CLrjCj4qHdXmfiyTyFt8yeQYSN5fxLg==", "signatures": [{"sig": "MEUCIQDdaMF9fXsyTxbyAn/FQ6sk/KGcUMHq0kOcnkUj88mpsQIgewOsSpBmZlUIUk9r4MIeOz9nzsj25F3S906F29/ZA88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138837}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/plugin-syntax-decorators": "^7.22.10", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.0", "@babel/traverse": "^7.23.2", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.0", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.2_1697050282104_0.2713959763268041", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.4", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "c8b69a116d14ec544ea237c36c3e78f43e9912a6", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-d3IYYC6ShFIR1YBRTGQoSzfoIdBg9lesXxZqbMkYTBm2bV97ddvnw/FMhSYMnM9My1XzMMf5pEAO+R5byQi5Ug==", "signatures": [{"sig": "MEYCIQDPUM7x799JVtpa6i4XQAZ4ZH3Y/bMYEcnTLAbyla5RrwIhALdjtSqt3F4UlMPOQa+Wb4JxPYmkSCP7wquzK38XVEV6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136381}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-replace-supers": "^8.0.0-alpha.4", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.4", "@babel/helper-split-export-declaration": "^8.0.0-alpha.4", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.4_1697076411853_0.008603973633197537", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.3", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "c609ca70be908d187ee36ff49f1250c56cc98f15", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.3.tgz", "fileCount": 9, "integrity": "sha512-u8SwzOcP0DYSsa++nHd/9exlHb0NAlHCb890qtZZbSwPX2bFv8LBEztxwN7Xg/dS8oAFFidhrI9PBcLBJSkGRQ==", "signatures": [{"sig": "MEUCIAUTRrzXjmtq1MLF4K3mlGxfro+PuUSxlLvYdAr/XvIYAiEA45zK7QQKY3uor1lIUVaMWyJluDOQIPpnFPLwjvu5wYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139058}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/plugin-syntax-decorators": "^7.23.3", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.3", "@babel/traverse": "^7.23.3", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.0", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.3_1699513446806_0.44377408040006694", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "eeaa49d0dc9229aec4d23378653738cdc5a3ea0a", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.5.tgz", "fileCount": 9, "integrity": "sha512-6IsY8jOeWibsengGlWIezp7cuZEFzNlAghFpzh9wiZwhQ42/hRcPnY/QV9HJoKTlujupinSlnQPiEy/u2C1ZfQ==", "signatures": [{"sig": "MEQCIHmhZB6OA2jpwWbnnS6M0nR/yIXUb7KqAEB88NRnliuWAiAY+gi+uMOTqKtdKW+j7r69J1hkAtZGJLV0PCsTiM2N8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139777}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/plugin-syntax-decorators": "^7.23.3", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.23.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.5", "@babel/traverse": "^7.23.5", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.0", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.5_1701253545123_0.32746483125890236", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "b34e9837c4fb0277c6d571581c76595521cf2db4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.6.tgz", "fileCount": 9, "integrity": "sha512-D7Ccq9LfkBFnow3azZGJvZYgcfeqAw3I1e5LoTpj6UKIFQilh8yqXsIGcRIqbBdsPWIz+Ze7ZZfggSj62Qp+Fg==", "signatures": [{"sig": "MEUCIQCrflIgDl6ZOfPFLuP59kIr99wA36EEuKJm4gEOb32CfwIgLUskNOxIxUaETyfZV59VH12+JtOcTBbtTDTWlZREIZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166580}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/plugin-syntax-decorators": "^7.23.3", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-create-class-features-plugin": "^7.23.6", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.6", "@babel/traverse": "^7.23.6", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.0", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.6_1702300202703_0.772697698403185", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.5", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "59ed514cd3a3d0cf3bc6cb70fc10929604c850ae", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-/nnCz7tkNq8NV0LISMvXe7xuFj835LExEH/1Rsx9G/2YIFAkmJj+xdnJH2zflPZP0fn6w7k9cotb9Hjmms6UHw==", "signatures": [{"sig": "MEUCIGQ8Oa1VnN1GWF6paC/A/bM2mLrQ2sMkn+c08DV2Hsq0AiEAjfOtgjl1h424jIKKjcFwSvuLu/Z0cByImvIb9DY7iBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164714}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-replace-supers": "^8.0.0-alpha.5", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.5", "@babel/helper-split-export-declaration": "^8.0.0-alpha.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.5", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@types/charcodes": "^0.2.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.5_1702307986021_0.516798913877663", "host": "s3://npm-registry-packages"}}, "7.23.7": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "1d827902cbd3d9054e54fb2f2056cdd1eaa0e368", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.7.tgz", "fileCount": 7, "integrity": "sha512-b1s5JyeMvqj7d9m9KhJNHKc18gEJiSyVzVX3bwbiPalQBQpuvfPh6lA9F7Kk/dWH0TIiXRpB9yicwijY6buPng==", "signatures": [{"sig": "MEUCIQDFIvdrbYyJI2JHmyYdKu4rbZtFZL9zIWkkuhoVDIUv/wIgGMx39rFiPLzL0mS/2WhjSiQ7Jx6bqmaa2pEr4KfCeQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40003}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-decorators": "^7.23.3", "@babel/helper-create-class-features-plugin": "^7.23.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.7", "@babel/traverse": "^7.23.7", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.1", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.7_1703880094573_0.006921428748402958", "host": "s3://npm-registry-packages"}}, "7.23.9": {"name": "@babel/plugin-proposal-decorators", "version": "7.23.9", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.23.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "126d947d62ee72022ec46813983c6dd861456fa3", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.23.9.tgz", "fileCount": 7, "integrity": "sha512-hJhBCb0+NnTWybvWq2WpbCYDOcflSbx0t+BYP65e5R9GVnukiDTi+on5bFkk4p7QGuv190H6KfNiV9Knf/3cZA==", "signatures": [{"sig": "MEUCICIJ9FZPG9ifdPB9jG2T6eWFIYV65byK09ozeLvku9cWAiEA0HQK03IoiXG6zowk+Lpdg+A91BT4YsuJSUddxmMEDmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40003}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-decorators": "^7.23.3", "@babel/helper-create-class-features-plugin": "^7.23.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.9", "@babel/traverse": "^7.23.9", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.2", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.23.9_1706201873321_0.10061722999901712", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "6e10d70a16e5a0e56f6251ecc0e78e3b4f0a495b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-xQ6/FWSto2VBYLSDHWfOiZg2f3YcilDZNxTICFhmSilzwnRd3FR6w+oBA8xLXYMBTuIvhbSqB/n4bqZZLgoeCQ==", "signatures": [{"sig": "MEQCICMi9ZoCsBu1919QHgmkf7IcLwTcrohQOpq1++DvxmVSAiBqbM9VAG1Vvb0juGu3MZWn5LJQuOZbD3H0ZDmjX9jKKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39047}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.6", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.6_1706285689457_0.2722263972835137", "host": "s3://npm-registry-packages"}}, "7.24.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.24.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.24.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "845b42189e7441faa60a37682de1038eae97c382", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.0.tgz", "fileCount": 7, "integrity": "sha512-LiT1RqZWeij7X+wGxCoYh3/3b8nVOX6/7BZ9wiQgAIyjoeQWdROaodJCgT+dwtbjHaz0r7bEbHJzjSbVfcOyjQ==", "signatures": [{"sig": "MEQCIGx20o/I8T7aiGSOxRyvo2I9tR/v5ONa9DHj3+1ccZ8TAiA7nBKbIBIKU4QTbsnQPJwVFCmafJ6pWh+5qb8I1QZy+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-decorators": "^7.24.0", "@babel/helper-create-class-features-plugin": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.0", "@babel/traverse": "^7.24.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.2", "@babel/helper-plugin-test-runner": "^7.22.5", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.24.0_1709120865872_0.8642414772394342", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "812438173d00fccfdbae91d728f86955d662c7b1", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-wKwIb82iN79o4XKCguKefuriP/qAU8U+Ef5qt7R0Y5jjjukMqTqawjZ708hG6bb+R9lcdsOz0Vs8A0pXaOMJCQ==", "signatures": [{"sig": "MEQCIGpefwWdS2TJf+gLU1y++NUDHR1MoRkjXwNFmrR41t1LAiBCXF7dSji8mWrKNWxnmwdpWOzd85/Jjch9qgrOOp2e2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39153}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.7", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.7_1709129147199_0.9840369667864002", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.24.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "bab2b9e174a2680f0a80f341f3ec70f809f8bb4b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.1.tgz", "fileCount": 7, "integrity": "sha512-zPEvzFijn+hRvJuX2Vu3KbEBN39LN3f7tW3MQO2LsIs57B26KU+kUc82BdAktS1VCM6libzh45eKGI65lg0cpA==", "signatures": [{"sig": "MEUCIQCOls3uTSR/7nrCVt7P64dkVAYW8vPIYRKGVoCh3SMCZgIgPSV+3DCWi5750D7Fd9MAWYvPz9qttSd2Ksstfwss12E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39956}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-decorators": "^7.24.1", "@babel/helper-create-class-features-plugin": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/traverse": "^7.24.1", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.1", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.24.1_1710841773325_0.04081051522172885", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.8", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "3cd8351d373821eca473642c72b7c0abba0a680e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-RvOMhntT2c7axX7AR9YPQeM60vQGsUCTuYgIp94HFEAzu41aLjMRyIyyxYVBpGlAwUWq73fRYOjH+Ky6WzqhbQ==", "signatures": [{"sig": "MEUCIA4JYQJQGor+5Hb5i9QPS9wmayRLIuHWFRu4LYzvZ/+UAiEA2t0Eidm//sYDYyutl8uebEcTvAkIgvN3sGfg3zJTiBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38973}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.8", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.8_1712236821535_0.7406861366654087", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-proposal-decorators", "version": "7.24.6", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "20e7ed41c24d3f6a2d94af7b44ddd06d1f8a71a3", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.6.tgz", "fileCount": 9, "integrity": "sha512-8DjR0/DzlBhz2SVi9a19/N2U5+C3y3rseXuyoKL9SP8vnbewscj1eHZtL6kpEn4UCuUmqEo0mvqyDYRFoN2gpA==", "signatures": [{"sig": "MEYCIQDN4qIq7qKXinGfOS7TzvAVtGPEbO8jwTx0yjsYjVe0pAIhAMGChAs8did8OLjH0gzfS0S6Y9CcTVWI485QN65CegiL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107296}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-decorators": "^7.24.6", "@babel/helper-create-class-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.6", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.24.6_1716553512066_0.885581336262838", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.9", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "b247431b225ce5ceab995fb83c90e772dccbdf81", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-yxn/y7A54h8Nx9WriWmkjMEgXrL/5EozHmi2Vd55wMbb/2AHocoTYflXckXp+L+/DET6lMiiofHR+3c873byFg==", "signatures": [{"sig": "MEQCIAxRMTaCycEPffaT9JIZAOF9ajBSmgZUwfv8nENy1TO9AiAnq+bOCFEdN0phfa/1RsuoOgy7ZrpSYFOTdLmiNUTCIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106853}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.9", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.9_1717423548591_0.33975637532584035", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.10", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "025def8f88024223cd622cf3636004ae345557ee", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-Kjj3q7HvfSlyjzB8Bih0+vKU18JkQ44Cn3ZcaUT3h5pnYm/rwKHry9gw2ffJWfRuSDN5bVcgRt9lcmNUvgocdA==", "signatures": [{"sig": "MEUCIQCgGgOzgQ1MGa4Hv/nRb+HaaJMg0gMFbYLUEcePc3eKcAIgdPGjFU0ThLczD1p3WLW2BnwkkiH+J32RLRTGMglqaOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106865}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.10", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.10_1717500046083_0.01683924646131807", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-proposal-decorators", "version": "7.24.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "7e2dcfeda4a42596b57c4c9de1f5176bbfc532e3", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.7.tgz", "fileCount": 9, "integrity": "sha512-RL9GR0pUG5Kc8BUWLNDm2T5OpYwSX15r98I0IkgmRQTXuELq/OynH8xtMTMvTJFjXbMWFVTKtYkTaYQsuAwQlQ==", "signatures": [{"sig": "MEQCIBfU7g6mXit8EEBx1oEmebhHoBwmDBgMf1DGsSk51Ud/AiBkMBNxF2GPNMzIcPmOpmgX8OZjMnXU4h6VRTmghHA7gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107271}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-decorators": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.24.7_1717593358716_0.35694054882005366", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.11", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "375b18021248c90e34c58ed102bc6bc7a8a44fef", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-jID4bz31YP5KX8AxwpbKPH5ZYrU9LMN5la24f2JhF9qGZ+8J4OUQ0ywYdsE71od3J6pwoWKNNF52ZoJWfvfIAw==", "signatures": [{"sig": "MEQCIH1w5Lh/wfE80UDpi+GiOa9xn4QBMg4hUeNJrWinK3DfAiANJ2aBmfKMcbinKyDIYAM9lAC0KYCAzdWmS0x4oBJzgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106756}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.11", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.11_1717751769221_0.17832990030138474", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.12", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "1ee0e4416b977d555dceb72e6d16ab89d597db84", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-uRYgEVIZRyUZ3uZnyluy3gT87jInDrwU0M++7J75bkYQPwVEezv0F5ORFq5KXEUhHnFNn0qhjsAWxuXCNk3Eig==", "signatures": [{"sig": "MEUCIQCtkGHLWKkm3fQLIm8KNWEotooXXxH+zZkmYUQnMTBCsAIgUBuqfuy36wzKbyZAX8vBvfDpCFl/+sdnDBXY0Lsiar4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103446}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.12", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.12_1722015244523_0.5909216164825222", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-proposal-decorators", "version": "7.25.7", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "dabfd82df5dff3a8fc61a434233bf8227c88402c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.25.7.tgz", "fileCount": 9, "integrity": "sha512-q1mqqqH0e1lhmsEQHV5U8OmdueBC2y0RFr2oUzZoFRtN3MvPmt2fsFRcNQAoGLTSNdHBFUYGnlgcRFhkBbKjPw==", "signatures": [{"sig": "MEUCIA8ju2CALMknlayRE/sO5OTWX4/ZztbEaurutjd6GAxZAiEAh+jgmB6HxBpHvLW82YSB6EHqk65YIpCAZhmeQkm3TBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111709}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-decorators": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/traverse": "^7.25.7", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.25.7", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.25.7_1727882134922_0.1440454225332426", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-proposal-decorators", "version": "7.25.9", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "8680707f943d1a3da2cd66b948179920f097e254", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.25.9.tgz", "fileCount": 7, "integrity": "sha512-smkNLL/O1ezy9Nhy4CNosc4Va+1wo5w4gzSZeLe6y6dM4mmHfYOCPolXQPHQxonZCF+ZyebxN9vqOolkYrSn5g==", "signatures": [{"sig": "MEUCIQDCLRPcPkK/iqhpdv/Im1o2gUsXCzK4l9RBzsj2N/ov/gIgVcDmJvcKgn5jpWPa4gYgpTFLjWlEvlD2U1XRNpSbYmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39947}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/traverse": "^7.25.9", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.25.9", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.25.9_1729610510244_0.3313449707325089", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.13", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "640faa58f526cff23fce4f202b4a74eb18548f82", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-CKxLxKRUM139BelS51HIxQl+YfWWYaj9qRBJ9C57WdPZxvb4LZkVB7hnFgOkabZeLnqLVTzTgpsYZCYhLNrQ+g==", "signatures": [{"sig": "MEQCIDztRZcNoCLVJBVmqZwXUp2j12QQWFE7hqQtU08i2G1GAiAR7T9NAv+qjgyzuxpkROJRIlQQlAj0rkLgVf8b1FRCgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39554}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.13", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.13_1729864490764_0.7817872550648397", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.14", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "f14db4ea26be539fa323eb8729bc821c121992c9", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-1oitVnwHg1cxPXZtwzJjSA2WW/qP8sn8NfkOlgB8r0SydebdX7/6j27/0X84GU8rXJWTLD0xot10TAY3Gp1WKQ==", "signatures": [{"sig": "MEQCIAxNvERQWhtHtz0DLijm4FodkoSU7Eu+ERcpn2WNuLKDAiBPV2LAcS4lMUMhQqKgFbnx/AyifFasl8nnRe59eGMBnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39554}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.14", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.14_1733504079594_0.8733362830230789", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.15", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "e217924f96bd77f3b391094668ef0b20e95b1346", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-yKEMDNd6FN3nJN6T8ZxK3riUBWZDewrWhjDXJYFSesCK5u0xcfcFvPlQh/vSL5093vJpVz4c0O9UIKBT0l/DWg==", "signatures": [{"sig": "MEUCIQD7Hix1fB5QHk5hYWbL7/BXOkPPxQ0orGHnOdGs1a4aiQIgObX2nVVYzhAl852Wp7TAVO9vdYGGwi91NaNbtlrUuag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39554}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.15", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.15_1736529909602_0.18377070624092595", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.16", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "e032b50dab4b5301fd64a69314eed5ed715b6e49", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-Gd4HWHI3In0E8h3960kgCJTCu797+ekU634mPPj03QxkLChgMhMwc5gJfw+LtEwenI9njvpZiZI5S5Km8fJ3Mw==", "signatures": [{"sig": "MEYCIQDJx1xMsxpOoYzGyXAusZABLlcNdijqiUfMO91VjqT2LgIhALoRzy+BcuzQEaupVq1+oC5Rvr3yfhNmlpU7B/Vwk7ua", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39554}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.16", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.16_1739534383434_0.5236245104334136", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-alpha.17", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "1ec1a62f2d74972aee6432f1d5f0f0fd73886d94", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-+equiRVmfbsBB9kWA+b24xdtWmwVkE+gUdZdN29HxQlhtYNpAnyzzGkpgPQkP2FR7DPG320y3gQy4f5TkJnZjQ==", "signatures": [{"sig": "MEUCIQC6KBqsPwoScFNWRqiF6YPTrQ4sRhnBZeqH5feMBT0lUwIgfFnxgshfZJV0ArNGX4cYYkI8wzRyJ0ZKcqGjZmtAhDQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39554}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/plugin-syntax-decorators": "^8.0.0-alpha.17", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-alpha.17_1741717538112_0.7345191240002766", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-proposal-decorators", "version": "7.27.1", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "3686f424b2f8b2fee7579aa4df133a4f5244a596", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.27.1.tgz", "fileCount": 7, "integrity": "sha512-DTxe4LBPrtFdsWzgpmbBKevg3e9PBy+dXRt19kSbucbZvL2uqtdqwwpluL1jfxYE0wIDTFp1nTy/q6gNLsxXrg==", "signatures": [{"sig": "MEUCICK2fGN1dSgmaUwm5Q8iIx79YSE+2y8bwgjY39QC4lsxAiEApApTbYNvIOKXu+tno+B3GiY+iP/aSM887PraRQqiLYs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39954}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/traverse": "^7.27.1", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.27.1", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.27.1_1746025772622_0.13591057967821807", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-beta.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "0aadce1042d73336174a9a3d2fe17c154abf7d37", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-ufbWA5/AAdLP9awDbBVuCLBnDpIpwlVuvM7VapdSP3p3/H/TNzxLmdTM0/XYx16LSEIR0EwPHJkenniJBnEf2A==", "signatures": [{"sig": "MEQCIHaWY9IBktjQO9p6wDw6t17oPXtjtAv932GL+c2BWm7CAiBbNAVXclzb7UAuQQEpy1u5wCzBrKlK/69HyxZkRDFE1Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39521}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/plugin-syntax-decorators": "^8.0.0-beta.0", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_8.0.0-beta.0_1748620309336_0.9329157771021728", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-proposal-decorators", "version": "7.28.0", "keywords": ["babel", "babel-plugin", "decorators"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-proposal-decorators@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "dist": {"shasum": "419c8acc31088e05a774344c021800f7ddc39bf0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.28.0.tgz", "fileCount": 7, "integrity": "sha512-zOiZqvANjWDUaUS9xMxbMcK/Zccztbe/6ikvUXaG9nsPH3w6qh5UaPGAnirI/WhIbZ8m3OHU0ReyPrknG+ZKeg==", "signatures": [{"sig": "MEQCIAGChlH+uGSaiiQB3bFLoQ6+FDB39DhACjePQ+Exufi6AiA63FxPtE+5OTMQXKdeGIzWj2koxZoAEkQBvU+5UDsi8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39954}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.28.0", "@babel/traverse": "^7.28.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.8", "@babel/helper-plugin-test-runner": "^7.27.1", "object.getownpropertydescriptors": "^2.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-proposal-decorators_7.28.0_1751445494619_0.25790841384317353", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-proposal-decorators", "version": "8.0.0-beta.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "publishConfig": {"access": "public"}, "description": "Compile class and object decorators to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-decorators"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "main": "./lib/index.js", "keywords": ["babel", "babel-plugin", "decorators"], "dependencies": {"@babel/helper-create-class-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/plugin-syntax-decorators": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.8", "object.getownpropertydescriptors": "^2.1.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-proposal-decorators@8.0.0-beta.1", "dist": {"shasum": "e2e174bab9119b2fd694bb5500f6ad4f73811538", "integrity": "sha512-mDLQAxCQH6QBfnelX/tzExjratjfFjU1ijQXtyZab+aUlq4fSkf3HRcSJsd8IU3pm4aG7o2Ia0dqNzHy221Asw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 39521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD5cyXNyI0dYsZMGG8SYMHeObCzyUmrAD5zLCrzEylcdgIhAK2ZBEOGtR3Wq6OYxCGLVzCpD5Q0NBjHaQ4fkk/Hqh72"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-proposal-decorators_8.0.0-beta.1_1751447091633_0.23999425106014183"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:38.918Z", "modified": "2025-07-02T09:04:52.082Z", "7.0.0-beta.4": "2017-10-30T18:35:38.918Z", "7.0.0-beta.5": "2017-10-30T20:57:17.646Z", "7.0.0-beta.31": "2017-11-03T20:04:03.862Z", "7.0.0-beta.32": "2017-11-12T13:33:35.439Z", "7.0.0-beta.33": "2017-12-01T14:28:43.583Z", "7.0.0-beta.34": "2017-12-02T14:39:42.663Z", "7.0.0-beta.35": "2017-12-14T21:48:04.096Z", "7.0.0-beta.36": "2017-12-25T19:05:02.944Z", "7.0.0-beta.37": "2018-01-08T16:02:44.708Z", "7.0.0-beta.38": "2018-01-17T16:32:13.827Z", "7.0.0-beta.39": "2018-01-30T20:27:45.907Z", "7.0.0-beta.40": "2018-02-12T16:41:59.586Z", "7.0.0-beta.41": "2018-03-14T16:26:22.257Z", "7.0.0-beta.42": "2018-03-15T20:51:36.193Z", "7.0.0-beta.43": "2018-04-02T16:48:33.699Z", "7.0.0-beta.44": "2018-04-02T22:20:15.172Z", "7.0.0-beta.45": "2018-04-23T01:57:17.259Z", "7.0.0-beta.46": "2018-04-23T04:31:41.526Z", "7.0.0-beta.47": "2018-05-15T00:09:32.363Z", "7.0.0-beta.48": "2018-05-24T19:23:21.159Z", "7.0.0-beta.49": "2018-05-25T16:02:46.059Z", "7.0.0-beta.50": "2018-06-12T19:47:30.832Z", "7.0.0-beta.51": "2018-06-12T21:20:06.514Z", "7.0.0-beta.52": "2018-07-06T00:59:31.358Z", "7.0.0-beta.53": "2018-07-11T13:40:29.917Z", "7.0.0-beta.54": "2018-07-16T18:00:12.619Z", "7.0.0-beta.55": "2018-07-28T22:07:36.189Z", "7.0.0-beta.56": "2018-08-04T01:06:28.124Z", "7.0.0-rc.0": "2018-08-09T15:58:47.520Z", "7.0.0-rc.1": "2018-08-09T20:08:28.184Z", "7.0.0-rc.2": "2018-08-21T19:24:34.055Z", "7.0.0-rc.3": "2018-08-24T18:08:26.994Z", "7.0.0-rc.4": "2018-08-27T16:44:42.363Z", "7.0.0": "2018-08-27T21:43:38.307Z", "7.1.0": "2018-09-17T19:31:22.000Z", "7.1.1": "2018-09-28T20:02:45.425Z", "7.1.2": "2018-09-28T22:19:40.285Z", "7.1.6": "2018-11-13T21:10:31.828Z", "7.2.0": "2018-12-03T19:02:21.637Z", "7.2.2": "2018-12-15T10:05:39.054Z", "7.2.3": "2018-12-20T11:13:26.891Z", "7.3.0": "2019-01-21T21:38:42.356Z", "7.4.0": "2019-03-19T20:45:30.056Z", "7.4.4": "2019-04-26T21:05:04.726Z", "7.6.0": "2019-09-06T17:33:46.014Z", "7.7.0": "2019-11-05T10:54:08.757Z", "7.7.4": "2019-11-22T23:34:02.397Z", "7.8.0": "2020-01-12T00:17:40.639Z", "7.8.3": "2020-01-13T21:42:45.480Z", "7.10.0": "2020-05-26T21:43:52.877Z", "7.10.1": "2020-05-27T22:08:34.301Z", "7.10.3": "2020-06-19T20:54:45.128Z", "7.10.4": "2020-06-30T13:13:33.153Z", "7.10.5": "2020-07-14T18:18:22.108Z", "7.12.1": "2020-10-15T22:42:03.384Z", "7.12.12": "2020-12-23T14:05:18.115Z", "7.12.13": "2021-02-03T01:12:08.278Z", "7.13.0": "2021-02-22T22:50:23.609Z", "7.13.5": "2021-02-23T14:02:22.559Z", "7.13.15": "2021-04-08T15:50:22.752Z", "7.14.2": "2021-05-12T17:09:40.006Z", "7.14.5": "2021-06-09T23:13:18.013Z", "7.15.4": "2021-09-02T21:39:58.235Z", "7.15.8": "2021-10-06T20:54:56.145Z", "7.16.0": "2021-10-29T23:47:59.567Z", "7.16.4": "2021-11-16T22:46:15.974Z", "7.16.5": "2021-12-13T22:28:59.396Z", "7.16.7": "2021-12-31T00:23:14.096Z", "7.17.0": "2022-02-02T23:04:56.930Z", "7.17.2": "2022-02-08T15:35:14.693Z", "7.17.8": "2022-03-18T20:31:09.500Z", "7.17.9": "2022-04-06T15:55:25.864Z", "7.17.12": "2022-05-16T19:33:10.312Z", "7.18.2": "2022-05-25T09:16:37.743Z", "7.18.6": "2022-06-27T19:50:48.467Z", "7.18.9": "2022-07-18T09:17:44.442Z", "7.18.10": "2022-08-01T18:46:41.097Z", "7.19.0": "2022-09-05T19:02:21.212Z", "7.19.1": "2022-09-14T15:29:17.598Z", "7.19.3": "2022-09-27T18:36:44.385Z", "7.19.6": "2022-10-20T09:03:30.053Z", "7.20.0": "2022-10-27T13:19:05.019Z", "7.20.2": "2022-11-04T18:51:06.838Z", "7.20.5": "2022-11-28T10:12:47.615Z", "7.20.7": "2022-12-22T09:45:43.649Z", "7.20.13": "2023-01-21T14:30:40.475Z", "7.21.0": "2023-02-20T15:31:19.105Z", "7.21.4-esm": "2023-04-04T14:10:01.818Z", "7.21.4-esm.1": "2023-04-04T14:21:59.110Z", "7.21.4-esm.2": "2023-04-04T14:40:05.235Z", "7.21.4-esm.3": "2023-04-04T14:56:46.905Z", "7.21.4-esm.4": "2023-04-04T15:13:54.727Z", "7.22.0": "2023-05-26T13:45:48.943Z", "7.22.3": "2023-05-27T10:11:08.967Z", "7.22.5": "2023-06-08T18:21:48.353Z", "7.22.6": "2023-07-04T07:49:00.656Z", "7.22.7": "2023-07-06T09:03:56.679Z", "8.0.0-alpha.0": "2023-07-20T14:00:30.533Z", "8.0.0-alpha.1": "2023-07-24T17:53:04.010Z", "7.22.10": "2023-08-07T17:25:22.178Z", "8.0.0-alpha.2": "2023-08-09T15:15:25.171Z", "7.22.15": "2023-09-04T12:25:23.793Z", "7.23.0": "2023-09-25T08:10:31.803Z", "8.0.0-alpha.3": "2023-09-26T14:57:38.448Z", "7.23.2": "2023-10-11T18:51:22.427Z", "8.0.0-alpha.4": "2023-10-12T02:06:52.150Z", "7.23.3": "2023-11-09T07:04:06.949Z", "7.23.5": "2023-11-29T10:25:45.326Z", "7.23.6": "2023-12-11T13:10:02.960Z", "8.0.0-alpha.5": "2023-12-11T15:19:46.187Z", "7.23.7": "2023-12-29T20:01:34.722Z", "7.23.9": "2024-01-25T16:57:53.470Z", "8.0.0-alpha.6": "2024-01-26T16:14:49.643Z", "7.24.0": "2024-02-28T11:47:46.023Z", "8.0.0-alpha.7": "2024-02-28T14:05:47.364Z", "7.24.1": "2024-03-19T09:49:33.460Z", "8.0.0-alpha.8": "2024-04-04T13:20:21.729Z", "7.24.6": "2024-05-24T12:25:12.230Z", "8.0.0-alpha.9": "2024-06-03T14:05:48.776Z", "8.0.0-alpha.10": "2024-06-04T11:20:46.300Z", "7.24.7": "2024-06-05T13:15:58.932Z", "8.0.0-alpha.11": "2024-06-07T09:16:09.387Z", "8.0.0-alpha.12": "2024-07-26T17:34:04.727Z", "7.25.7": "2024-10-02T15:15:35.135Z", "7.25.9": "2024-10-22T15:21:50.473Z", "8.0.0-alpha.13": "2024-10-25T13:54:50.974Z", "8.0.0-alpha.14": "2024-12-06T16:54:39.811Z", "8.0.0-alpha.15": "2025-01-10T17:25:09.804Z", "8.0.0-alpha.16": "2025-02-14T11:59:43.594Z", "8.0.0-alpha.17": "2025-03-11T18:25:38.294Z", "7.27.1": "2025-04-30T15:09:32.830Z", "8.0.0-beta.0": "2025-05-30T15:51:49.542Z", "7.28.0": "2025-07-02T08:38:14.811Z", "8.0.0-beta.1": "2025-07-02T09:04:51.816Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "keywords": ["babel", "babel-plugin", "decorators"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-proposal-decorators"}, "description": "Compile class and object decorators to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}