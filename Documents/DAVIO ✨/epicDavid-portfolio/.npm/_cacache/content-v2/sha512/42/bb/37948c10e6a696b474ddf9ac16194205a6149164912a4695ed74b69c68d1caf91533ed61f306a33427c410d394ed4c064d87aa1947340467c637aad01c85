{"_id": "@babel/plugin-transform-optional-chaining", "_rev": "40-cdddd473556ef564f5b427a2bca3a8f2", "name": "@babel/plugin-transform-optional-chaining", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "fac7deb35099936c9c7cbca5b0a433463887f789", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.0.tgz", "fileCount": 10, "integrity": "sha512-p5BZinFj92iSErcstoPK+e+MHJUEZ6Gmlu0EkP3DJ0Y/1XPNvlXxfAzuh8KkN+3wCsYRKLAxAsF6Sn8b/bfWaA==", "signatures": [{"sig": "MEUCIA8xNEfgJgniRF+KTspBZ8Up4+HjfMtuv2ZSnRqUvnkRAiEA2Mq7hjdpnBBS2LS5VubHKeOw4rs/XeRNU5ufYIHy/6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61131}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/traverse": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.21.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.0_1685108717132_0.4361792495101431", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "5fd24a4a7843b76da6aeec23c7f551da5d365290", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.3.tgz", "fileCount": 9, "integrity": "sha512-63v3/UFFxhPKT8j8u1jTTGVyITxl7/7AfOqK8C5gz1rHURPUGe3y5mvIf68eYKGoBNahtJnTxBKug4BQOnzeJg==", "signatures": [{"sig": "MEQCIBtoEPS+9pRPsU4t+ixVVWy9EdsreWmEJdYQFQa3tYbQAiB9NJxPYnI+SP33UpLyn/bwY+UqjhEa1Rcqijwpvgg3Ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61178}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/traverse": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.21.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.3_1685182258731_0.22543142890450985", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "1003762b9c14295501beb41be72426736bedd1e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-AconbMKOMkyG+xCng2JogMCDcqW8wedQAqpVIL4cOSescZ7+iW8utC6YDZLMCSUIReEA733gzRSaOSXMAt/4WQ==", "signatures": [{"sig": "MEYCIQDKO6EHYJf7jHpqts38OtNXMGW3vIBLdCb9UIMFIu84jQIhAMiGUQiMGm8MTF3PZfm9G1/uqy85zF2w2id5o5NHo3DL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61178}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.5_1686248493498_0.03916112003447991", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "4bacfe37001fe1901117672875e931d439811564", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.6.tgz", "fileCount": 9, "integrity": "sha512-Vd5HiWml0mDVtcLHIoEU5sw6HOUW/Zk0acLs/SAeuLzkGNOPc9DB4nkUajemhCmTIz3eiaKREZn2hQQqF79YTg==", "signatures": [{"sig": "MEYCIQC8EOqSs/0gkmDf/Buc6F4n5XpCZ1kkPTMPagnWrwmAJgIhANRhQkdDnTDAgxokt5kU8cRLKza8nPtAjO76bvvCn0v8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66677}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/traverse": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.6_1688456930597_0.3309211552796858", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "08a59257592e649f2589d0be23d840b381a85a04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-6zEWDg/sLGQ3PJXfjiIShbhy2yhnO15JfYFkb0Y+YCuuX6+gGopZi3dvf0jmNgFJvUuUz5Okl+WHI2lTX6filg==", "signatures": [{"sig": "MEUCIQC9HFnEmo1MKiQU9F2RxZIL5MB0CfcDcnf6GaBb1InwzQIgY7BcVMcBgDjtxWERbTcGRBWrU1spvHP6hCOO/xDvk1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.0_1689861616224_0.39794035611721457", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "7f9025e29b91d7aadfc3e3d71732724ca6b50f32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-4161rMwiRNKdcREmOuiTyxF/KgbXEfm2favDGKWQFjJ5vOrY52yFqlYMl5KXEfU/at8gqAIm1mJocIBLSL8YcQ==", "signatures": [{"sig": "MEYCIQDv6LS+iYjHMQGs7FL8lLOq2ztGryCS3TYi1+iJnh/rVQIhAMrMonPFY4C5CoBfUdxc0+XTRPBpYx5H4HBNzwoaxh7W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.1_1690221166121_0.7572921496705181", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "076d28a7e074392e840d4ae587d83445bac0372a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.10.tgz", "fileCount": 9, "integrity": "sha512-MMkQqZAZ+MGj+jGTG3OTuhKeBpNcO+0oCEbrGNEaOmiEn+1MzRyQlYsruGiU8RTK3zV6XwrVJTmwiDOyYK6J9g==", "signatures": [{"sig": "MEUCIQCFHPb3YeF4UvYoKmLKsCOD9aiHqUTm1KDkT0AEe/XbNgIgdqmqZ7R8KiJNBvo4WvjjXyN54XoltizbV3I8xiQXElk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66679}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "@babel/traverse": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.10_1691429113687_0.9721377146765087", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "e9717035abd4502aedc5217bad1ea62cebcfebca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-wvIoVuSIwVp1FOUunMrG3BEcV/G2UUkGxmkzXPCW/2F0pcrVtCFIhZQMbhSHbKgQnlpzSRjpGFEFWvQiZ1gisA==", "signatures": [{"sig": "MEUCIQCsC33JVb7DBtXwY1gTp2+NDjj0tQRDakppmaVyfhuJCwIgUhCMPcxk6/mxPOJ+C5lpk8ipNQRTH9HMfsEQPo1N6is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64607}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.2_1691594112651_0.555931806364302", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "062f0071f777aa06b31332cd90318d6b76444b74", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-7X2vGqH2ZKu7Imx0C+o5OysRwtF/wzdCAqmcD1N1v2Ww8CtOSC+p+VoV76skm47DLvBZ8kBFic+egqxM9S/p4g==", "signatures": [{"sig": "MEQCIAoBpApTEynZCvMot1nIJ4TZgf+FC3lGkl6kcvGdY4PzAiBpZT6pBytxUkQoFZg8eN6Pm6w0JaAOs+Os2WoLzosWog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37261}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/traverse": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.11_1692882520230_0.39783928710954997", "host": "s3://npm-registry-packages"}}, "7.22.12": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "d7ebf6a88cd2f4d307b0e000ab630acd8124b333", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.12.tgz", "fileCount": 5, "integrity": "sha512-7XXCVqZtyFWqjDsYDY4T45w4mlx1rf7aOgkc/Ww76xkgBiOlmjPkx36PBLHa1k1rwWvVgYMPsbuVnIamx2ZQJw==", "signatures": [{"sig": "MEUCIB0XBJeFtnk+28xtmHuFY8E/ucmW7NzqdOFx0XQyMzBPAiEAmvrvzpa1brCi8WhtbmiYiEPvMDIqQ0EBwNhA/4KXs0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37705}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/traverse": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.12_1692952116336_0.7170304185601042", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "d7a5996c2f7ca4ad2ad16dbb74444e5c4385b1ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.22.15.tgz", "fileCount": 5, "integrity": "sha512-ngQ2tBhq5vvSJw2Q2Z9i7ealNkpDMU0rGWnHPKqRZO0tzZ5tlaoz4hDvhXioOoaE0X2vfNss1djwg0DXlfu30A==", "signatures": [{"sig": "MEUCIBfC+derIPuKP/k2/paSGhBMMljl5yq75DIKvOR0rSCQAiEAraS7nyYFv14v9NGVNcJgu4dWZeiDovI9GpRHHDCh4mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37719}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.15"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.22.15_1693830305912_0.7707563016545735", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.23.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "73ff5fc1cf98f542f09f29c0631647d8ad0be158", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.23.0.tgz", "fileCount": 5, "integrity": "sha512-sBBGXbLJjxTzLBF5rFWaikMnOGOk/BmK6vVByIdEggZ7Vn6CvWXZyRkkLFK6WE0IF8jSliyOkUN6SScFgzCM0g==", "signatures": [{"sig": "MEQCIA5fbRFCzbzCOg2sDKzAwmTstTHtMyCiJ495BSbKHkgpAiBZfkaRWyZx8S6Ww774xWktPLbyLVdPXsFzMGAyxXyWYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37821}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/traverse": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.23.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.23.0_1695629447624_0.12774323164924217", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "6cb5c7f1bab2b30ccc72c7d51daa27519ba8dfcd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-LvFu2Meo0QxSnx6GkvGa/Xs076e3p3QgUOOuKifu+fOyNqIfv1sIufDCxhcLoRoQy6L/ehoOGtirVb/YVEKjxQ==", "signatures": [{"sig": "MEUCIQDu/gCxWEmkvv256fqbVezIISSL8bvTWjku9YjaG4Bj7AIgUdOm7lM547i8Y2OOgek80A3vsBIQ17dauR9PjfvhF3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36859}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.3_1695740243379_0.89922327641869", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "54ae9c70df662b02cfa3c61ba6e9dcd9f246636b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-vJvXjH1IY8VeEO1k/idcFENVM9aGnuCXlB+nh/oYqYLV4WUoQHkgwkJj9CM31cXM6HNoPgaYXg6+uTrX1gEevA==", "signatures": [{"sig": "MEUCIQCs8ZJtmoVKyt3qr2cu4IouCf7jsi5B0cpL97lD7Sg7/gIgYIDvsB/6jk63F6AQt91rZ8z7+ZKbr3c7Zvs5G1be1HA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36859}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.4_1697076397348_0.5379075545252694", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "92fc83f54aa3adc34288933fa27e54c13113f4be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-zvL8vIfIUgMccIAK1lxjvNv572JHFJIKb4MWBz5OGdBQA0fB0Xluix5rmOby48exiJc987neOmP/m9Fnpkz3Tg==", "signatures": [{"sig": "MEUCIQD1bYd7K5eCDiCiY1Zd62pl+57VRQ7HuxvxwPJhGFR4mwIgGcaamRNbTSM2kOgTkCK/DY3vsM77vaaKh9NMcpSNklE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37925}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.23.3_1699513434836_0.866533061838938", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "6acf61203bdfc4de9d4e52e64490aeb3e52bd017", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-ZU8y5zWOfjM5vZ+asjgAPwDaBjJzgufjES89Rs4Lpq63O300R/kOz30WCLo6BxxX6QVEilwSlpClnG5cZaikTA==", "signatures": [{"sig": "MEUCIQDkp4NkdZtHc/8a5DCi0ouqGQ/kVrmgNy9lfGXTlc41dgIgbAfAYUYRF7AB1vGnVIpohOqenCjrtNqsOw2nL8W9ghM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37931}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.23.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.23.4_1700490128861_0.7652502268393904", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "dce7f785bdc9dbae2f11f4c4f946e19aced9e310", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-<PERSON>j<PERSON>5utO9hU0p2sWQjSfucBHixsstCO49ZAZCB7uv3KFF9gx5gDPyGpFdHlYM45TPyYPcAF9wIZZ+PkmzBBDzRA==", "signatures": [{"sig": "MEYCIQDQi5RDGhOmeCT8LlRf2sB8tbPOKUeqVNpr0fVtSTmG7AIhAIsbz+WpxSVDBMf+dmx9sPNANF9RklgDy1kUZAjcI+jI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36978}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.5_1702307966584_0.33638647425289436", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "adc4493cef829e4a30b22ebdb1a0ae0bb344c5eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-74I2rlWlb8nO82ZRGrPoIyRUc7As+ypLYp4fvtnitt7DnYnqDo7QkcaGIb5Bo4fu7SQytf3Zvl0NCl/SUnwWdw==", "signatures": [{"sig": "MEYCIQCh7xZZkuVcibnDjO3gfMEx5/+CdqSm+BXsmLCMomM0mgIhAMfLxXVdowfIozjd7qI+kjqTBZi/jHiRmgCsrgdP2+tH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36978}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.6_1706285665401_0.29793982389310014", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "f5c5dbe9c98b474a125c0bff409650580b9dee2c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-YNRDrYzzDUyKwuNt1aTvIb5hN+tBP+L+eKoQ5nXqCu04FN5ceKpBg8q2JlL2ryUWqZmlO+SiwlQP8HQtdRgR4Q==", "signatures": [{"sig": "MEQCIDBrgZOwC/ixyw4pOJTpXWNNJNTjliHqqg28c578KGoJAiAroUlNBEv9jZOPKttG7oAVvlgyqP9sVImO5atG++VJeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36978}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.7_1709129124670_0.6705070169254399", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "26e588acbedce1ab3519ac40cc748e380c5291e6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-n03wmDt+987qXwAgcBlnUUivrZBPZ8z1plL0YvgQalLm+ZE5BMhGm94jhxXtA1wzv1Cu2aaOv1BM9vbVttrzSg==", "signatures": [{"sig": "MEUCIQDDeGFO66dL8z7q12L+nsCOJKPij5Lk8TwSJk93jH03ewIgFuvkJkQEAPob5Yww1UDjMInIqr+LHHHd8mVG5dhaQj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37975}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/plugin-transform-block-scoping": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.24.1_1710841734490_0.8877359747097895", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "6f32dde5908375c3db8e0ce5a9d8efdd607948ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-pm5AMDaJSdn3BUCWu5WPPL8rdGNxehvn4e5kbsxgXMSSeArY5SB9VZ5kYDiD9gVIDrakYTvr0zYWHFhXKoDHgw==", "signatures": [{"sig": "MEUCIH9dNsojuz+TjnuA7QXWe98pK/2mXCTx5s6j3lnUJyTHAiEAg3bl2O2DYFgf6j7Zl/lyqdXjryxdJOYm7bCNHOLm3Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36902}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.8_1712236807075_0.7370147573936201", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "a6334bebd7f9dd3df37447880d0bd64b778e600f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-xWCkmwKT+ihmA6l7SSTpk8e4qQl/274iNbSKRRS8mpqFR32ksy36+a+LWY8OXCCEefF8WFlnOHVsaDI2231wBg==", "signatures": [{"sig": "MEQCIBKn5Lctz/UnzUvXsf507orbSNoMKkNrgo4GSDrrhr2BAiBdsG7PynUfRIb2fQmWUj1dw7csfSFvF6nS/nnwJSYKAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104342}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/traverse": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/plugin-transform-block-scoping": "^7.24.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.24.5_1714415658816_0.5173069832699126", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "3d636b3ed8b5a506f93e4d4675fc95754d7594f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-cHbqF6l1QP11OkYTYQ+hhVx1E017O5ZcSPXk9oODpqhcAD1htsWG2NpHrrhthEO2qZomLK0FXS+u7NfrkF5aOQ==", "signatures": [{"sig": "MEUCIQCFEkE8CjkwvsRMg8ltULw60SsE/1Cd29gzTAioCkvoIwIgbZpltcv7RKFORStdVky8u/nJACuOjTA4nPV3Ep88Meo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104509}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6", "@babel/plugin-transform-block-scoping": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.24.6_1716553497069_0.2562287030352757", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "9ebf9f33bd220522a064b9a78f80a3a9231b8517", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-8xHCEc42w4ZbZZcDeVyzmidC0sz1E+K6JNaRCzdVKaGswfc5a2jjgtKE6sR2YutYxGIyyNJsGnMQM/1Ngmb0cw==", "signatures": [{"sig": "MEQCICrhZ50IeL7UilELtTHYzeA1Dt4tzLzJKaIt8aUZzSyOAiA38kbuDrLakEEedYHIJ+QUycqCRUcAz+abxFd6r94Eew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104282}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.9_1717423536283_0.49375424346737473", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "3327dd70f0ad91d6dbb3154f7332a55f8c5a790e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-dfKhmS8FNQYulTBM9BQjQFO0dPkyNuxIrp2OQFggeKeGfCa8Q28o18LhBo4OLEwaBwCxcJzf66diS+XBBEvizQ==", "signatures": [{"sig": "MEYCIQC0LWKhJh7HAq/eZp2cK6Oe6ibjvggciWPQVLQ794SnpQIhAMiDSTKHTfzL1BZ2i05RTBVmtI1VjenXwpCc/xhRfloq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104292}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.10_1717500036788_0.4676575380502266", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "b8f6848a80cf2da98a8a204429bec04756c6d454", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-tK+0N9yd4j+x/4hxF3F0e0fu/VdcxU18y5SevtyM/PCFlQvXbR0Zmlo2eBrKtVipGNFzpq56o8WsIIKcJFUCRQ==", "signatures": [{"sig": "MEUCIQCo8KQGpxF9l6OQLWUlqFBemrFuTvTQye0utPaEUjUvKAIgcLSQZZF2zTZmi1zbm4pjSbPX7mmHDZavsLKm5BZFjPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104403}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/plugin-transform-block-scoping": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.24.7_1717593351066_0.9401116014059392", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "178f2ce1cb1cabb19bb51ab80bb23af375a9f7c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-O9BpZMtdLLViWLNiBGpEdJK6Q/UJNI/L1L1EI6x9fa7vevlA7Sb0lYBas/YIE/s+exTxHGIgPOGt1/kuVaf4gw==", "signatures": [{"sig": "MEQCICxqrHBcEZBBkyWMCV4nu3yjG2O5kXn5uuTSkuQbxbn6AiBfOfogkwkQg1ip1jqybPXYpcv7X+5dD4DnZB8G0+NjCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104183}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.11_1717751761727_0.09325050217397046", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.24.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "bb02a67b60ff0406085c13d104c99a835cdf365d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.8.tgz", "fileCount": 7, "integrity": "sha512-5c<PERSON><PERSON>Ms9eypEy8JUVvIKOu6NgvbJMnpG62VpIHrTmROdQ+L5mDAaI40g25k5vXti55JWNX5jCkq3HZxXBQANw==", "signatures": [{"sig": "MEYCIQD74KN+JHNItwO33kH6nRp6Eq4NaZ7apDhuDLeBeTzZAAIhAI+JT69xv3NrryDdeeQz7eSkdeWxIWzrhuiyS1K4qlcd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100856}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.8", "@babel/traverse": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/plugin-transform-block-scoping": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.24.8_1720709688545_0.6433388317102087", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "f10dc59c228e7b8f1b3e241605629f88b528f34d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-Lry4BIJmwJoZAT8Lv3UDuXiqKmSp/8UupvneH5ZEbbnZk+4HVRk2T/puvH+i+EFjuZALZ+RA/6+MnExpx5+9BQ==", "signatures": [{"sig": "MEUCIQCMAAmLuQ2P/ecNqHyxTPtDQPi4bVAMZFfstDvLLchc7gIgVFIYK7m/biZLnlOjaWMfmjAgIh6aRQrNwAeyjiCUhl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101057}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.12_1722015235786_0.3785067094106487", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "b7f7c9321aa1d8414e67799c28d87c23682e4d68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-h39agClImgPWg4H8mYVAbD1qP9vClFbEjqoJmt87Zen8pjqK8FTPUwrOXAvqu5soytwxrLMd2fx2KSCp2CHcNg==", "signatures": [{"sig": "MEUCIQC5f8n/UHpzrZIX5XuXZKINXAlaW5nHHDnanncIRGUPygIgbpxcy03CeSc8MyltXbzkDckED5Al1g/RapYESJAPZm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109025}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/plugin-transform-block-scoping": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.25.7_1727882124762_0.47214719349773393", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "f46283b78adcc5b6ab988a952f989e7dce70653f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-q05Bk7gXOxpTHoQ8RSzGSh/LHVB9JEIkKnk3myAWwZHnYiTGYtbdrYkIsS8Xyh4ltKf7GNUSgzs/6P2bJtBAQg==", "signatures": [{"sig": "MEYCIQDhuJf2E3CHUKFaT1HdcK5cks7c75Tscx943iQOBl18DwIhAMYVDXC3eTOZIZ98zNdbOsIQZxYa22tv0jWLw1D3ioqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109320}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/plugin-transform-block-scoping": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.25.8_1728566712213_0.09295685844806201", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "e142eb899d26ef715435f201ab6e139541eee7dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-6AvV0FsLULbpnXeBjrY4dmWF8F7gf8QnvTEoO/wX/5xm/xE1Xo8oPuD3MPS+KS9f9XBEAWN7X1aWr4z9HdOr7A==", "signatures": [{"sig": "MEYCIQDQ/+toUKHD0YHQsU+Q8BscY2+ZeVKPoeukNSvstxtExAIhAMUcmXbBpypO154rYNg55/lHFObO0G3qJBnxXYg5+L6Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37685}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/plugin-transform-block-scoping": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.25.9_1729610501211_0.7974217102949384", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "b5a96447da2767ff03d8b07f5792cf08307c456c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-xF5T6wGIxEsbN6jFiH0md+TpBYP6fgwo01TYGU8T8X6Iv92DMO6G3Kn7Fwjnk0LUTSNqmhAcwhRxnKwBxlTG8A==", "signatures": [{"sig": "MEYCIQCwUK4g+T7PWstD0ZWfXf4CvyMyCLhuk4DGOO4tFwU3TQIhAPLCAVu5bkEYLUuRz7UrBl5like8s58zZjZiRPWFisOt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37721}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.13_1729864481127_0.938950007485932", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "dbd09d1a5aa1261a9bd3fbf62386691498fddab8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-DTDP6ynxz6JAAdsCBTfQuny62u9N5gLv0wU3p6uKh7VCfjMZnKoQ+/vGU0TLocWZC7RQH9PLPKnDFST827+8RA==", "signatures": [{"sig": "MEQCIBHJ6xttMcG11Zmlllv/NJlb7V0rh1DwV2bm3UJnT1xcAiA7v1Wq87vv7IQfG5Si+2obyd7TmgcO34/G+1r1ohzmNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37721}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.14_1733504069489_0.6159386795018882", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "27e2c59944788e130bc0dee46a38bf1f6979b94b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-OluDBMU5bbATSqmrxn+ER+kjACSOpt/su9K1uQwcbNcidc6+kbkrv0SUw6+2wKt71h/VvTHVzk6xHHqsdawvzw==", "signatures": [{"sig": "MEUCIB+I+ujyD+IzOFeyNmQ1uLhKputGZ+VTknWkTaSafuJMAiEA5GO2gbJ76RjZWTDnkuTSNu1RyVS+m96O7eFOXlUkJoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37721}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.15_1736529897674_0.9655283276824547", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "42a3bcd8eb9122b37987d26a7e2ed9c99611bfd6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-QPlQFS8AlIK2jxD2sNmyEpOq1T4Aw1uj5y2L3hgBWcSNvwKEYSwdRUYObI3NadVO0MXU+fPp7heVoaeViMj2tg==", "signatures": [{"sig": "MEQCIEKC7ObjjHoWHu6kSNfaR4ijx9GoDOKvFkYxowsBlJDNAiAuu34UhZHoPtEZV1r2sPVofYS3Gt7uZSXJfAyrSnElzA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37721}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.16_1739534374195_0.716824140289702", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "f4163d650aeba3376c5f300bb181d2eff622a3f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-6V6MaoktXg42Y20f2Z8LXsQao1gH1ohgybuDvkqvaKQ2P0bbW65deUkxXgR713WfI6Xe0IleYs8vSurJZoGjaw==", "signatures": [{"sig": "MEUCIQCVx2EAvFN9hUs+gru9WSBbVRlpeHSTxrbRYYwnqHMdPAIgfEHWjnSesnl4OYeTw0zZTOFTAI2rf3WBHSzvZZJcKz8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37721}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "@babel/plugin-transform-block-scoping": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-alpha.17_1741717527373_0.4057161309660917", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-optional-chaining", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "874ce3c4f06b7780592e946026eb76a32830454f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-B<PERSON>mKPPIuc8EkZgNKsv0X4bPmOoayeu4F1YCwx2/CfmDSXDbp7GnzlUH+/ul5VGfRg1AoFPsrIThlEBj2xb4CAg==", "signatures": [{"sig": "MEUCIQDjMQsgWqzt2FK6b1/l8BLrqki+gIXNsVvnoTj3ZE90PAIgMrRiE+UWtCxj7+LHh6F9j1J3ADdL1kMBrYK/svivXtA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37601}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_7.27.1_1746025762225_0.12103152469661271", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "dist": {"shasum": "a17fba49d1a0a80d6343a0788a5b19a230140a66", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-wTOcZn067F5Npj3+cY41LcCM0T52fitgvTFK7OFVwpBr676BZ9sMYpYe6fJiav7YwM61qABwgFHp7IhcedNjHQ==", "signatures": [{"sig": "MEQCIEF+5uLbO17P1GqeCgLu/PC6ABvIG9k/xVuFF+5AcVAtAiBBZljpyfgImsgbE0+g5gdxEbHrvFkAMQP+Jy1atet0bA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37691}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "@babel/plugin-transform-block-scoping": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-chaining_8.0.0-beta.0_1748620298775_0.6809942144979197", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-optional-chaining", "version": "8.0.0-beta.1", "description": "Transform optional chaining operators into a series of nil checks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-transform-block-scoping": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-optional-chaining@8.0.0-beta.1", "dist": {"shasum": "d8cb78eeb35824928902458f2cb97e27ba4aba24", "integrity": "sha512-ifw5SEKLxjH9Z7yEdI+sBBjgHFoRGgJlq1KODaDv5MSGqxCPikuBxsf02aTOJjFyc0/fr7WZbZpNAd8VbTyyhA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 37691, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDwCRAKyjl/JVTOY4V4JL/ZmxJEApiQWdY443QHU0m9dQIgUyrUHoNiq6ZOthhdz6mYoCW3fICjuuwaDipqP/BkYbQ="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-optional-chaining_8.0.0-beta.1_1751447084460_0.84900643211294"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:17.065Z", "modified": "2025-07-02T09:04:44.892Z", "7.22.0": "2023-05-26T13:45:17.314Z", "7.22.3": "2023-05-27T10:10:58.904Z", "7.22.5": "2023-06-08T18:21:33.682Z", "7.22.6": "2023-07-04T07:48:50.729Z", "8.0.0-alpha.0": "2023-07-20T14:00:16.397Z", "8.0.0-alpha.1": "2023-07-24T17:52:46.298Z", "7.22.10": "2023-08-07T17:25:13.840Z", "8.0.0-alpha.2": "2023-08-09T15:15:12.879Z", "7.22.11": "2023-08-24T13:08:40.468Z", "7.22.12": "2023-08-25T08:28:36.489Z", "7.22.15": "2023-09-04T12:25:06.099Z", "7.23.0": "2023-09-25T08:10:47.853Z", "8.0.0-alpha.3": "2023-09-26T14:57:23.579Z", "8.0.0-alpha.4": "2023-10-12T02:06:37.541Z", "7.23.3": "2023-11-09T07:03:54.993Z", "7.23.4": "2023-11-20T14:22:09.019Z", "8.0.0-alpha.5": "2023-12-11T15:19:26.866Z", "8.0.0-alpha.6": "2024-01-26T16:14:25.618Z", "8.0.0-alpha.7": "2024-02-28T14:05:25.041Z", "7.24.1": "2024-03-19T09:48:54.683Z", "8.0.0-alpha.8": "2024-04-04T13:20:07.237Z", "7.24.5": "2024-04-29T18:34:19.002Z", "7.24.6": "2024-05-24T12:24:57.250Z", "8.0.0-alpha.9": "2024-06-03T14:05:36.422Z", "8.0.0-alpha.10": "2024-06-04T11:20:36.973Z", "7.24.7": "2024-06-05T13:15:51.289Z", "8.0.0-alpha.11": "2024-06-07T09:16:01.966Z", "7.24.8": "2024-07-11T14:54:48.742Z", "8.0.0-alpha.12": "2024-07-26T17:33:56.062Z", "7.25.7": "2024-10-02T15:15:25.022Z", "7.25.8": "2024-10-10T13:25:12.416Z", "7.25.9": "2024-10-22T15:21:41.383Z", "8.0.0-alpha.13": "2024-10-25T13:54:41.289Z", "8.0.0-alpha.14": "2024-12-06T16:54:29.685Z", "8.0.0-alpha.15": "2025-01-10T17:24:57.892Z", "8.0.0-alpha.16": "2025-02-14T11:59:34.404Z", "8.0.0-alpha.17": "2025-03-11T18:25:27.582Z", "7.27.1": "2025-04-30T15:09:22.437Z", "8.0.0-beta.0": "2025-05-30T15:51:38.949Z", "8.0.0-beta.1": "2025-07-02T09:04:44.614Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "description": "Transform optional chaining operators into a series of nil checks", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}