{"_id": "@babel/plugin-transform-spread", "_rev": "123-7cb1c2371f79cef1ce40be9bf486b916", "name": "@babel/plugin-transform-spread", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2fcfa492747e9a54a9e54a869498d6f73bb50a18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.4.tgz", "integrity": "sha512-16CFBb7U2zzKFGlw1fqA2f5wXVdFILuZaOe/RMUQvoYkeF+YAhjKEjpdMGfzd5Y7eyl2Tj4WNEnWC7I9hew8lQ==", "signatures": [{"sig": "MEUCIG/lqKht1x8PKiwnwIF0YsRNHysK6IBEdhJv9HRX/djWAiEAyE/ZvLAtKIygTDu0zj+CjYIz+9TCCzCIU6Wc+1vLa1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.4.tgz_1509388489930_0.5474197459407151", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "274d96dde88e38d621246496c79b343fc6d841ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.5.tgz", "integrity": "sha512-fruIdqOXqG+6OJH+4fqmgh2XnwohyOmShQv8aLqs18xDsi5lPqbEmVZLcoAXtLvJdWIUGfiva3MI0WDlSG0lOA==", "signatures": [{"sig": "MEQCIAr1ZSjyRuokm7J7705SAa4X/PEUY1nc46rej+HE2jNNAiBE6UbyArdnrrB6BLU4WL8ldTnyBofF9DnC4SV3dsd40w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.5.tgz_1509396988385_0.5083407659549266", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e183c28d674b3766e7e893cc53d58fd871ab4bbc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.31.tgz", "integrity": "sha512-GGytZpV5U0I7cHjAb2GrZeelzzXwCbyneaqZRlFwEY03OxEWk94hZC84Dd6a0a9Osgwu9USUUGTnicJElHqZEw==", "signatures": [{"sig": "MEUCIQDReZtEgYYTBwzZH/Hyxh7fGDa0i8m7IVFN1XGbdVIgxQIgfhiFnaNyZHKIyBdH6E+gygspQZ9dCwFogeljcnG0e2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.31.tgz_1509739411483_0.39239908033050597", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "05e1eb1a0393d41b5a643681255848c2d1852bda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.32.tgz", "integrity": "sha512-e2d+Ee/mn5Kcj/okt+WqyLswuXs3QIgEdXwtN5M9d609y1mCWZN3Bdz7F6HiFoFF5R24dG8nqymRUzbR+Pn2UA==", "signatures": [{"sig": "MEYCIQC9oyRi05pBhJC+oXvCRlI3sWBslCFR74B9HlkGHJ2QkwIhALpnd+2j7oGfHN1OohxzYgKmGBMFSRDT5cIRbK5UTC0u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.32.tgz_1510493603015_0.5243120489176363", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4d7e7a530d60a664e0ce307b72df7a3eaa21d26a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.33.tgz", "integrity": "sha512-arxQdvCEhxM9jGyZsqjFhVpvImxswL8s9XwnIX/3LqGJU/0/OEhNP30KbreX7j8nGMv4BO+seoDK56Qej2GK/g==", "signatures": [{"sig": "MEUCIQCHUOyGuMgxr5zY0zLeHP7lkIeBjujGFfi/O0CVMrKIlgIgSedcHMgWW6bCI+cZPEgibEQeRa1PpdulL0LAipOO3K0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.33.tgz_1512138506853_0.3142339577898383", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5bb69cb0d20e87f2f4d752938d654569edeb0378", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.34.tgz", "integrity": "sha512-loj8b4CTpgOjmGCODMCZ6Nbn+CZWcgU9MsFBWomfliOyQpDu/9WqNuRdiGcMlUmbnIoVpiNUAf5ZKcd3wAgJnw==", "signatures": [{"sig": "MEQCIFxSwHzwxGTnUErfPtY/pSFzXeuS2yafkQiQYqYEbWJxAiBVttwutO6TzPHuRVwj4MDPl4/csKc0iySGLflKMWMYRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.34.tgz_1512225567756_0.8974902802146971", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "aa5c0fa12c01d23b8f02d367e66b49715d4b77a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.35.tgz", "integrity": "sha512-AaAtOXICT58RER/pgbVR8OlpNAp5JZ90hzUUNqpdg53XFom/Mfb/+JYk9amaPmiEUA+eJtr102PqvclrW5ckWw==", "signatures": [{"sig": "MEYCIQDHvj1zwmw0ZeugwPbSaPiVPnXcA6ykacHJQ6Y2sf4w8wIhAOMKP9U4L+UC2MvV2YqDAWTugFksj7lr5CzEqUS9FyG8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.35.tgz_1513288072988_0.22385093686170876", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "432a51a26a5a9a9a574c6d9de3668dd24deffb0d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.36.tgz", "integrity": "sha512-mGCjhmLrpIzCQ+WAJnQzoGDd8/rv709Bce7IXPJLsw3pseHurVhHCGU9x5YzERnVHMh7zE9RVqyvARrp9XnS9A==", "signatures": [{"sig": "MEUCIQCpXQdYGlL5lHsyvei5G4/dxPWP5PPUDfWoCV/T/P3ULQIgOUjLF20HATITmnqOARH0CN2BYEG30u56hvah0m3k4/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.36.tgz_1514228685275_0.5469743586145341", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1ce979ce9d336576611e2edec10cd590ff13164c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.37.tgz", "integrity": "sha512-scWhOSqdk0blc12Tj7YAnZtJCC6ByIXe1Z/3h0ZqjVTvxxfYNRc6qsi+mUEC3K6mAv3ScKHEqrB0LeJgum9s5g==", "signatures": [{"sig": "MEQCIFMCgIKhWuoPLyiQU9PRU+tSNLSKTkX+Zm32FEThvdj/AiAqfR65HIpT+oK1Mnvc7JOcPoycvyvaLUJCKWkIr6B0NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.37.tgz_1515427355158_0.22638393193483353", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1a6e325b224a96d149fa83409b0a5daa3e33e868", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.38.tgz", "integrity": "sha512-KQItm2CD8JthjpcmccTEoi4db9AJ0C4vPBy5Ds/AuIBqmFKBkWVanzyciUsX5yupNrZRYcbnCQ2hflDUpFIN8Q==", "signatures": [{"sig": "MEUCIQDYqH10MT6DDLsgVUWKSCvFmg8wqf1qr9vQq8LUACD91wIgD3gvr4flK6wJFTfTBU7/cg9fCS6KVKdc+H70uiVi/8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.38.tgz_1516206720930_0.14296282618306577", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "75358c67a52cc2cc266ea598dc0447e0f4e239d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.39.tgz", "integrity": "sha512-lrJM9eZRUgHqfr6x97bBJbyLQk9qnpVLhMB331sVLP4pB3jucxNtun65GrogJRJ0cD7fNa7AyBDtle3AL0zBDg==", "signatures": [{"sig": "MEUCIHUVxlwJM523BlalcQc2WrOBvTAYloaHczjVUWpt8y1oAiEAzdgmI0cfWzwlqUzKQS96MdQH8HY3WZ0F2SQNwcfd8mE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread-7.0.0-beta.39.tgz_1517344057475_0.6690137283876538", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "881578938e5750137301750bef7fdd0e01be76be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-RPrIpV+h8OqoqyMic7CNeM8TdSDk7ec+T6jM97vMb9XQQrRInAUWlwWvG6d36v72xobFtHoPA28VN/0aVsbQDg==", "signatures": [{"sig": "MEUCIC6MVIfeRH7zAdao0U+CrvQLIU0hfdy0SDFiWfOwRad0AiEAm1lwpmoGmYFcsWUD24Auswddb4HSHMQ7Ov1Scblp/h0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5888}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.40_1518453703588_0.307257039546883", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "88c11a4854c0e274d74a091c5605721b6b05fc45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-/hW1c6WAHIdW8wBAU+I//VAiNyQDABoEjV1TBBygHmakJ2X/BsVTco/v2VQ654626Ow2r9n5VKltdy3eMpT8Cw==", "signatures": [{"sig": "MEQCIAJrVU2kDwyd9b1DQhDh920JMY6tVIEqihLQ//tRjhPNAiAubF5gdk6LndGyLYSmlTLDmUXH/LtPYapo00F3gCjaKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6120}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.41_1521044777071_0.6696137543240479", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4d7dde45c95e55d418477e1ea95dd6d9b71f15e4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-ZzWt7RvGcV+9DcOTBwE6ArNqDpUMpzZhCToj3UNtULol9gGBbrGgUK/LdGwGInj+Z2aIdOjbAMFtEuC6626lJg==", "signatures": [{"sig": "MEUCICFBH1Eci0Cj73QOdRcs+XkGgmA//OFPK/6H8x7rbg9tAiEA4mxKW6cIJx3UUq6gidk5WQbXoNUy0dtwuk980FOnO64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6120}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.42_1521147049818_0.7215097182079493", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6922dadb718b78fc644ba345c58264b2be3c0b74", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-cwVN+0PYEIpMeU1k3w2voucRZS7+ZoAB5ux6Osqxsybd+DDY2Hn4OQao/0gNSZnIw1ykYMTHtcbX0NHYoAoQkQ==", "signatures": [{"sig": "MEQCIBRKVrzIjvlgft5rz83tCpAq68phHITSUwUZOiwwLSD8AiAh8QwUA6NbyVbje8tuGrnCRlWEWKuRyVkcHfSDp1fJ/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6011}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.43_1522687710005_0.16636924880065007", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "94cacc3317cb8e2227b543c25b8046d7635d4114", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-8fEte+nRW4OSdC23P0KBpiE7U4j0GpupMchnsjySYYNimKFnzPP5UYacZ6Lti5kS0XAnVfs09iHLXG1ccsWQsA==", "signatures": [{"sig": "MEUCIFV9HG3nSfLQde+J06ZYVBIInHKt1ILY9mF2/UaqBJlnAiEA2n/UZL1yUPqnMnqclJ1w8GXDDjnPD6i7XtYimIhGIHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6428}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.44_1522707611812_0.01052863919541025", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c052d3dd4f9edd9885a3100dbdfbf32cc2766c23", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-nuo+6mjWohP8pmDDd8HDlHb5Xf9M7jI48oKObP8sayr3w9TPW6GkblUPsE8NCa/vLZUO44S6IX3IOeA+ZXxfwA==", "signatures": [{"sig": "MEUCIEZMp1TC1zFICDOgeD22A6ouwla9iLCdUepXCO6R0WOrAiEA0CHRNQSfVwuYFfzu8twDYQkUDzVqKi4DyB+B4rtvxhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1yCRA9TVsSAnZWagAAA/YP/1yXGYzQa30bMHemSkmq\nchUNvRNCurgz3XSYJhmGYoFJAROrsPx8hEqySZEfwzREdcvjnWypDaBVFWdx\nmeeJZlshjHyN0FFFmb4tev17PSsqrM+2eItHjavdklqVblu0aMme1R4HQZZQ\nXYzI0iQsOgOvLwiRSRZCjsHoXVstQS6GdK0o1c9AicxTnuWqh+5TUIba3sTZ\n4GgI6uHy4NfkGiMvS7GEJhte4DfpnmAMlouD5ffvcEZBWkSoQn0tBEn53QxT\n48rQTwgOHpFc4lqyKDM+CpG3vxPBfBxKL27q1uxMjBaKGrwMQ/vsg97Thbwd\nGJm58+HQx2ma/KolYOl5OMHiy5PvwCBwoZrNjUnV9t9oo0nheqfxQls6Y96F\nY+PqTOn859WBz4zTMEHjoA2ra2k/Af/c9mcNiPJQFNVSuN8ysSu+x9rVMxmg\nLvYu0PK0dthMjqA3e7lLxdvW7xmCQWn5qjc17MldBzEUUvl692a3lI/W5NLM\ndrMCIJXguIkLogIcdhKsDXxQ/bQXD4Qiv0yian//hdl4HQjoJKd5DcB8XXMH\n0Ey8u9ADE4pKEVYeOrnLmvyM5UD6n1zYO3gj45uNDUXRIaFDpPLTdBaIZ5jv\nFSsJGRfu2aLKfULO9sDf08biMS27a/OakgFfCTtXVCjm8HwMkUgxuI4KaPmn\ncN+R\r\n=dB0V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.45_1524448625549_0.27913907084723477", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "48eabb219f1e0c16e9b0a6166072ae9d4c7cd397", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-R0GvFdJnFrgTlmZfFtCXk81uvq5S3FuY38FnRsxDt6Yx/sE8jCmmrRe7XHZOnXXGP3ZWY9icILUmzWHOf91jbA==", "signatures": [{"sig": "MEQCIALluL7r92kj0Gz7RE5Fg4KXTL6lmB8BWhGMwHtkbt4BAiBWd26elntSPjDWer1eMFpa6osURuySomIs8maR5N5oXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGfCRA9TVsSAnZWagAA35QP/2oY+Vg1xH+NcneGO4+M\nW3eEWaWp9aUY5/xtvP+Q0izGeurpMHw2ksZpkEZcXSujbs/rbCopSn+gH0M0\ndq57I1a8voSRB7cMzzIUhr4lnCHCo+wfqh1Kpp1il7goLohDCmBCOoOvAbwn\n88ggc2xTbVRne4dTNE1Vs51tWk10yJ8ZS/1CZC0Nyj0/VGy5o2wzjj3uczsM\nXralbLh2/KvnIZImMvz48jN/vLmoYF2gBSFabrJ3rslnyEGnfZgEz+kmjpPu\nHhC0IfZx2bXUlceJ454aTYrQBm6Qg7U5cd4YvpZxKoyzD0gtGCVpP7KFhGM8\nsnRnA9B3IPFhK/W0+bPq5NueklWhODgScLwa8CmiYIjNaRSDNYyyEhLhkcyJ\nNs/A8nXw2agLRZCG4nIBcTAkBy8bcwgPncR20Fpf2WTBWYbBlGv+Xyk48mTp\npvolbLBF8jl33POA4HzW/Jf0VTmLB7e56ztDalXAYutZo/nf0p7Y1nrmjIX/\n/G88ccgdKQ+Bdom6p8uUgec3AMNZHzFrlfilonqh6lC6+D4Iml1tZo8SpJvv\ny0Eu0wBZ4t9Svi4nV9b3Fy/8ONb6cJqztjEOAmWDJaxIaHsKL7Hr8g+PaXm8\nWEFzjYQ2HCTdjjONBMlciDnBlfkhDRSyyH5+eSbxxN/VfBxO54LmHmMDujf4\n30as\r\n=To01\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.46_1524457887094_0.9082956127320241", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3feadb02292ed1e9b75090d651b9df88a7ab5c50", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-LFAozFdfT4bE2AQw2BnjzLufTX4GBsTUHUGRhT8XNoDYuGnV+7k9Yj6JU3/7csJc9u6W91PArYgoO+D56CMw6Q==", "signatures": [{"sig": "MEUCIGjzYi066XKGgYU6Vc8QgVDzJ3VZXfec4TEY/3SCkDljAiEAgkqsYHBO19JpouOJNA9/smHC8+bQ5KZyYMBIIySRw8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUvCRA9TVsSAnZWagAAz0MP/1E/j6xEfliUsjZ/jI53\nUhwIyHaRI1L8Vd75kTZxoGIkPSZMuytGudlxMXZWWhbybxmBOd3JqArKjnwu\n/u8mdq/mUxqWPRC6YasdV7WhKsLUgbiaujiqlEZMgMwG+tVkM7lR6zzZngQ0\nnUL3ZRrigPzsRkE6Lm2J6E3VPtu63da+WZWBnPHdmgZRlo0yHs2CmUu/Y/ob\n+GewMaFSXO/GlkjjtEF5af/hbdWsVJTP7knytDtc/oXSK4C0howvMgaB6qzV\n5rNJkmTax4rKJVGgn6SObXFbxKsQK7t7Y+MkLK70MSfa4AXiEJ+YEf34S5Kq\ngEaXJGiN8zv9CyqRCQUykj8DsCIoJoQfaCO3pryGHzaXehTIdXNdkPg+jitE\nw9HmheStSbzcjAPpECXiwbsNYnCqHSu5bK8s2praUgCL3HdJHJG21s9os8wJ\ndTKK7JCPdL9VaQ1GzlzNpwtlv9ux1P0tsC4SbKEDKNEcs/WhyjszFKnnBQ7V\nQfQ9aERWU6WA7xYfoi9hmzswfS9XW2ERgBDOhnBBSOUiNkhJ3vfi1Oa3xr65\nq6XwRALYr7ck3NZ3GolSxa6dEqCaHX2w9Awap2P2ox24WZJHmixCz+LGRIX+\nrM10MDdKi4ymFU9I/oAH54UFyj/qNvONwwthfyPU8ybu879cKginFNasyxj/\n3iA/\r\n=aM73\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.47_1526342958616_0.1883676247089363", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f6c7a09d4e85a39adff5123cbc8fa3ae3019e60c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-RFCSLFlxKCo40Yu69gGZqj4Ky6Tea6hPeqwCeGFu9XEdODGvA+u1tk8u7XQXbnWfaZ09e9BBSeJUUGPC69J35A==", "signatures": [{"sig": "MEYCIQCk/cZ1tlPaYK0NQGFoCJshl1qwijMUW6P/UhXrubnhQgIhAO/qnuKP7fpccb8RCWNZw3InqY9JZF0MwbRBrsH/Viiv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxENCRA9TVsSAnZWagAAt7EP/RcU8E+CEbuQJOz2GEEZ\n4mxNSVHgorhZJGx4BEf9eNR9atZQIPwv5296AFUlaZ4RUUiW498rp4E4C0XC\nxb9gF+AUQt+9D6IHWSGpWKXEl7+f2gpdH/rDS5TnZDo7WoRxApB5a7/RvbmT\nLb95VPwuk19OmELWtoXd/JBxst3OaspFltoa3zQ57CBsxjp2IQL7dxVgmRMT\nRmczAKeEDDWSZeGqxe3XWNbh1gLrBs4Xgefn4I5VHa4UZvrHfki4LPmIPnNQ\nGp/hQBRGLkQVuhb5p5FgD9ykftY8CtzJKLiFp43jdu2jZplC5l1pCxmBV8vC\ngQhHztFdIrZG5eiGU7LSHtB6FryuWTz8mdrFHvrXLkh/bXFH47LUIy3YKiwp\no3NLpt0kAd6ilNz+Ux+Dk3b7Jg7/1KjIcAwWxMAl9pxU4vMJ62Z/ctpQ+eKa\ngD0lzJdDKXKJaouqG4HTXOfrv1ceRBGQ1xHzFs1xLT2YmSgipgqizVxf+9Dd\n7F/mHZF+kWDQfeqgfC5bXH10RgTp/AzAzJcVHydX02DQBffU6CLN0UKJqfsk\niOUR2OWm8FNXudA2C0zRxILsTjgpJpMX03SEwYHb+LuXxsCSeS+vbC2oizPd\nm4uNuv7s4JXifgt8D3l5H4kF29jGLIi3nJYUfdRpjvQYM7dSvYDs88zY7/Ro\nBZVk\r\n=0SCO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.48_1527189773263_0.017700176405348378", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6abab05fc0cca829aaf9e2a85044b79763e681ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-P195ar96Ig/MBPpfpiHfYu1yefyAn3FQmn9ieDxUjzTXP3u5Qd5nm5ZoGQm6EBrV685FLlkTtcv5PembdMFVCg==", "signatures": [{"sig": "MEQCIHzLg09LVKZt/79V9YxCuZfr8dl5OrnqMtOQX/HxaTt8AiBgnCWRMd2AdiFOc+2qHzE2AC8PPYgAmtpRtysdJXlIhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOXCRA9TVsSAnZWagAAkXIP/j1b7WmujChbOUMcymlf\nQZgmofuM/P5y/h8YAB+TNx1EZ37Bon4787lQzuLXM0mdYPHqjX1DeleqV44q\nKACPqERI2mZ0S4/NzjrTkMoBFUnNUlCJ+5FPSl1hCbxdmm6sPIKJ/t5F/l37\n0mk65WCKwrLAes5xbX/jqHmlCCKEtswZ6YYSWOsGYA0QOquRdvqR7wbrbUyE\nQPc1YN7VZUDVJOKGNhTAb+DBm/QwZfoDJWEjpbQ2ZX/lnimKcY9Spiix0oKD\nB3ndfZB4YDCMCsUaVNjnAptch/Ri0QuYzFwtVRf3Ob+qviuSRFoyZhTmOb0h\nEx9kmv3PZ/i43nntrFO8clj+6gWAzL/vRNIwDbhV2NVlnBk6S/4Q7TTAr61x\nQX+J83PP/oLEAEQDBgpz3lbEFkJI8UX8XLqtEWKUa41FnD3YVY0WyMrsVp0o\n8Qm9bb7CNLexbJcp9GGfrVRXuU9cL6Xcj6PuHqWb6oZge3p/xxBwtSAKc8od\nD1GCLs3um/7cZbl8tqU/GiPCh9o+jkZKNxyNWeovwL9RnqUh6C+FFvI0VOQu\nJyYuUXzI1eAkTUqHaAUGlooP0vOMW6oJL8iy+VUFMETAF6lyZrOMLe80IuTg\nSiNiNQH/06XoQFQLmuWNbmzB9usQyZpDRvQh5SniWKG7dY0P33pIL3sP1N2F\nWelf\r\n=IvaV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "6abab05fc0cca829aaf9e2a85044b79763e681ca", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.49_1527264151059_0.7218777465465194", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3fc6e2513da8efea62f4090d4c039616e42eafeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-vfuRQV8K0+Y743TsGC+0GpXPqL1Vtel9J52/04ppB8GTh1uZJQhRsoGO03CNW83NlW7HmAZt4NsH9h+jZ63UfQ==", "signatures": [{"sig": "MEUCIQCZnxw1GTROxCR2s/ZA7g1g/R9DU5ntIPC9fgGr5FFFhgIgeO6Ja5D1M9+xC03vR5RS6WsYpBs+Z4A1wiujimmMcbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5239}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.50_1528832848685_0.4137022888077857", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "100129bc8d7dcf4bc79adcd6129a4214259d8a50", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-L7jtBZ3KPFG1xV4pdC8A3jKudAIPekjlJuKyQ6Dq1WxMEkoE3bkqDsIWv5MRG9OnbJAgiF7OR78E6lDWtdChOg==", "signatures": [{"sig": "MEUCIQDq2RhebWmqL/Lq84EVDnsFnMcodOb6gQ0bibbQRmOl7QIgNh0rXodRV28YKnHYFlH8niPPz4ySzqmXM0YXYXP4TRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5253}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.51_1528838396824_0.950618552328605", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "343709a6dd33c0b5ceff49f267ae96c922596522", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-9mYOgZ76wuQPWPIgte/nyV+xNH7g/p9nhX7DMiwrflWe2kt0Tu4WNfwR8JUgvHFcd+iY3/4jbEyvcyBHSRXfJg==", "signatures": [{"sig": "MEUCIE8Fbuxm1J8s3ZWLpqGXM60pAejve98htfh6b7K6bAeeAiEAoZfWNyPv9oowwEKFn1Rouy2TMueL6/Cy2GpYGBzqRm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5252}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.52_1530838768801_0.5480197617610743", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "83e8f646ca24f1c98228f9f1444cf60cbd4938bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-pkK2dpGXiblw+OojZnyMdJAD/qUs0bDnhqqmN4WLdbvsbQW0wC/nWD7YmGUwl9B8kJ4cFmrvT1l5idh4d9Az3Q==", "signatures": [{"sig": "MEYCIQC/N5FVS+5oOQx3AoOsewb/0cbqSYkL3+sOc8KpD5ezVAIhAOrEgF09zQyYLfbMx7lOpRq4yLkcUYqgP+svacMO9Fjo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5252}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.53_1531316428246_0.8986857018797865", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4f0852df0f4b1db2426c40facd8fe5f028a3dbc9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-DV0tOxuXh9bYrU9bW4OmJkdJs3j9ccVChZmCHQ7m9HvgUT/KOB5DowahdjjZMg67DrmTBVXv1fntpIqJqqd1MQ==", "signatures": [{"sig": "MEUCIQCsU9eIfqJ0/PF0F7Gh1BtHBKoIW4O4MwS1So5qIWHLvwIgB2QM2FQEtUaEq4hd7NZufYzDKSO/VUW+Fxhm0Cdztkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5252}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.54_1531764009911_0.4890503279571774", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d5a1c320aac86469d6d311e136a89fb5a1f65600", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-f4esyWM8ljc5uJxIQowc2GMD3Ojwl2UYTxBlypOq7OEBWP7U3IWEYTQi/H1imk0FKrSeqBGge9VEFxK9Xwn71w==", "signatures": [{"sig": "MEUCIGUBWuSJI3uXjX9RcnvKypnY8JxQLzAlBCFZ3Xif+JyXAiEA/WMbm7x26nLpjeDSmKRtqvkSdLArsEcA0wxVGVdxD+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5252}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.55_1532815643514_0.45951773438970434", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a8681e7d78b648b7170a211068bea31aaedf0ea2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-C0EUKGVSU5ttbRe+ATn54oR2jdGzGIA8Uo/0jwtMeTU+6lewpTX8nu+M36JZDIcq2sSu7zxfLiRkErb/PX/Q0g==", "signatures": [{"sig": "MEUCIALBFruJ4+m4E/OEtPAtacS5qjcFLv6g+n694ygxh0XLAiEArGUROv0CZTbJCMWevPPQqVxA1yxDyrPtB7FCIOUhV84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPwGCRA9TVsSAnZWagAAavEP/2dipDYmijsHRaAnu2oV\nEF49ZYVgKHRO35XdHExTGkh3pmdrGVUlRFqKv2sdTXTb/Juv8fyCsuLh2QBN\nEhSHXIPoKN33XfLYujQGRMh4jrWF21UePXZQ1MeH7FHQlvpeCUEzhiSQ6lsr\n9Dw2Y9XDDy0K8p3oOA3uAw3R+Mnsgz5jL1efyb3FTprex4gmOVzBK/JLIU6+\nMTZADyoUI3MmVUsnz8zvAnStdALYj9TXgXnHI8igY6g7lANG3jJb95I8xlj/\no3ESBc67GW/FwWw1PD/pS0r0MON3VZVTFoEOBT5/f83eVaFEEp9R/rHQ8kwG\n/zOTUcvRNzErCE2fH1v+qxMfF7hJoljpFjc43NqATObf6OOOt8qCDjY/jh9F\n716q29r/yG5WBKyZkxyYgZ7p6wKOjso+MOeMcAgpLeodY1zuqmCAAxTV8q7Q\n3ASsv22wL+Tidd1dp+Id4NA0KrfrPFbkIBMp/tCPR1Mo6C+qjj3kws/pjuMt\neTdoE0oa+CyfaOIFD3095i57sV7WGiZavbflAtH7ZgA4hZfOOsyUlh7v1qOw\nGqdcku2NytX4zlrabyGPeaUJoEepAn8CSzG3CA1qYuRW0we4TvRXwEZPr5aB\nrRdbhAdcsTxuuXgdhThDAmf2r/eMJqarYFLcwnx/IdZVAIFQb7OnkPRv/O6i\nA9ep\r\n=lzO5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-beta.56_1533344773991_0.6762928129378867", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3e420250cb6796bd00c3b7b5da2efcfeb175d110", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-RzxqKNTt+gcf6KaQCQBID9zeYVm3AwCs1Yb1EGmdy6J1FryuvCZR5UM1gQTZQhdLr7SWA8sI/rdNMJe4TNkHHg==", "signatures": [{"sig": "MEUCICfpMDsyFFJQAL8YIJBTE4TuVOstKW1Qy+oK6qbTLl/oAiEAhZ8jgT8FeXCk92YbFM3xnAVXb97OpoeXr5Q2PExemSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSqCRA9TVsSAnZWagAAn/sP/iH4bo/r39H/K8RGw7S7\nN3LfDLKxEeFQqR3GLXZQWwyP0li7j2VqFjCp3yaqX5OjqfhqYuTK1Rtg2P1E\n5DFavjh9ayimHl4Z85yeNcwqOw/+mc7ZOo+mtbocqpKSKxeafdlSPvfwyg/E\nrVpjUUfkBZPUUzxVak+2+CFOsMJu/gRVHNdeQgjVbp6XSgP8VgZN1J1eGo0p\n47XK3FQCC3sV43ljcZ7vd9mrHCRt3LnBy7EvBcn4rpWnQIe26DFUWHPSYM7a\n/R3FiqEoP2/PC/3Y8fmRoAIX2XuK9gtU9C+dXml/BBAU9vOTjxFXxIc2npQ+\nKmFIc8NGwvsV9puzh8V0+3Eg2IxnOu4Z+BmpuCjhuW0r6SRX5yMXMM7q2pWz\ndtooTwOHAceJF8Nl7dRwoWXDt1yYD+x72dNAQ1N9Bpk+2bjhQCmPLDJcvHpM\nV1M4WSdxEGVE1v09TWiT1SU7Gbc1j206R0WHCBk1S3oWbDBOw4ta2GjiXei0\nGBHbc5MN6WPLGNv+uCwHHP3UA5oCSrCs3hNQdKNGWkJF+NkZ9XvMcWUFh4Vx\n42RALfyPG0IEsW1uCfFWfKthvCshHYV6odjgeDQuLtDcysfimeCgUtiS19uc\nPByK6b3AgtQm0zj9JoxPyAsXukyWZnfUpSBv3opb+x51Z9O1GC4f9r3byAyi\niapo\r\n=2GNR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-rc.0_1533830314434_0.8898450655049355", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3ad6d96f42175ecf7c03d92313fa1f5c24a69637", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-/3EkUVVi55i/JCbL2CxXTaoCXCopj3qQMTZ0lvgtpepx1yAMpoHYFBNWLIuQmjG7JhDauOwEdBg8TRsneYRmmw==", "signatures": [{"sig": "MEUCIQC9GM5bCas6GXI/LHcDrl4BUxi4Zxm8fvz7aeAcen6baAIgB76oxWufVNe4MR70BJlUP8Uicro3W38QTKqLUGdMf1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5221, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8zCRA9TVsSAnZWagAAfWwQAIep9TWqA13Ml6d3nsAx\nXx8lHELQhrSAFj7H6JCMraVawODWFZQWUmj+g4XlmcgqwvC915eUS3UaLZfd\nX/++NLNEWRrIpBDKLGHPkAOuokChGhA9BzLV2T1WCnY4WCOBzStFsewcCrDH\nr+Rjd0O9Lh0S3c0+3777yJMjE9RkI8Lp+nuawBdxLuyeZ4vrBJowF3OLLc6m\npUMyL5RzPQjKfXBEFOaLUDau+K2On9KKZo/44XN+JWoj54Za9yuG6wLQmxmD\nENBoi/LIaMPpbPxSFFRCd46se0WrILM5Ak0mVYZQZRsp2Fl5VrL0jiajpmZ6\ntydI6iR7gIlQEmoCG9lM/nm2CzYBQMZo8pPNC0dpDhiY8CubJnMjnccs8mGc\ndNtI/LfSH5wfcStEjWsaSqu4A80fiscGNo7nazbA6jpM1s81PJ3gSkGIg/b/\nXgZiPynZa1202FUDVOsR8NK+beTJ1l1JV1th4D9gkgQbPttNjw3Xa4n+T2GK\noH9KtpqzAukjrHrgFURwMw2FZuj+pCb2/+GE9bw/yjtABQ1wtQpg77p7uE3O\nP896stVQNK5P6UPGzzM0tsrQj4+d0DP076sfk7zNIXbh89ETP7aSLEUB4rN4\nyI5VskBS/kvSPriUsmpRLUtqYzdeWB4+qDvPeUWYy+aJsqE2I3M9P8xJcTJd\nRULQ\r\n=iYJq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-rc.1_1533845298646_0.02768422920724012", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "827e032c206c9f08d01d3d43bb8800e573b3a501", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-Xmw5FxsvrxHv94j9i54IfIpbNbaGC4TpokMmMjuqdgeezCEZkCFUolqydhgDKgxCg5pqKDh6oDistJIJ/8RLMA==", "signatures": [{"sig": "MEQCIE7ud8Tt1eqrvvlFqHA+qGLcLnMrHX0ry2J3TCab5Ku7AiAG0Smx40YqTqcZi8/0WXe7o1m0SEzRcmvnLmkIZ0x0uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbjCRA9TVsSAnZWagAAoyMP/1b0bKCvsmwjgmDjeZZX\nEZ4oQhtJR3Qqzs8zzqGXoMuefkpzuzmENFj/F62XA27PUAaVUxNas5ens4tp\nmQoVvaGkQAqHXbb3/3C7iQsewLDATbHKFU2Ue/OckdK60FlQlWxcH0bTlhGS\n3S6BgrHj0DRaxYdl9wotkxn0x015cc1KkZwqzpIvVC9fG02Z7a7uN6glWYsr\nHvarLSjbXMEoeOi1Lgd0O3MvyRgs2QRD6QDu/CKa4bWWp4TNtPdoFLtwe06Z\nYfT0WaLRrEPA8U5ZKWGaIjvJtbiL8fl9t3WfijL6OoBgyOmu9Zp3lzZgkLH3\nbWzhtjeEJwdtJhl9/xjj1LMXtzv0ixDD9IqrBFUuvkdbnQ9gwx19zFjlDfxA\n9Ab+kDBcgsDSvooWiKW+YY4kn0h0tAdqn2jicLYQuwnUCPd1IabcUa5kqMDn\n6bSZmCMkmeBejqaOdW53SNuTvy2I/7GHrBnqfwoSDk6lgv3q0KgJo3S1nTOb\nhXNuj6JsMR6rPAWkP2XhL4HYvfMtolWR2GT2OUnkeK8+VnstAHUB7FQMTVTU\nlIe2poRAQjEr4TGS5NJ2iWosxfXSd5eRJgTnOOnIHCBlb+uPL6EXMr4+36Cx\nxQl9MLobf1F2UvxBiWrYH6vu8wMUqbJFQCIpY+QtwbD8OG48FrffVog/sVZm\n67PG\r\n=Bigk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-rc.2_1534879458570_0.9831874755374397", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5479b400ec2401327af90d881c04ce450589d402", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-+MVQPPpaU5OSbn0iTnHH/DZGKdIupyxKk3hlRFSaCOcqE4vrndt3ZdFlNefiRapht1TGyBFV7UqvsjRwep9kmQ==", "signatures": [{"sig": "MEQCIBrqn/aNxFpTV4o2vp2vZbO4K/W5V2eRYBFAeBf0bby6AiB3/nJt0B3psC0CwIZn1TDOM2wJcxhKF6BiUR5i6nJEGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmNCRA9TVsSAnZWagAA5MMP/3L4vhBZaFJQGF0ibe2G\nz9WF8pmXn8rJ6Q8nWLpKcA6C2M/yQcJc9rX+HU5ccGhVFjkscNw0/X1Rc6Bm\nEGZDggJcKhD0Yo8+pbZ/ao4mlkdJx5XuFT9eksldXOHjBATJJVN1Fy4EP8zC\nbtN4jTEBJN/c5LH8ImLZ0uH9fXyiAuokTb6agLEI46OPHPsXl2hQt/Hmzwj3\nEmCFSSvT8ERyKGwtzk4xUoWSZTEXtEUK21am/P4scvnhD3JgH7nlsU+3Fxzp\nOFnZH2YeAkuLkMKOH9fPGjkUCMvF8TcOqeWcczthSmwH7LnE2c/l8WHBRvLO\nTA5M5Ko3wNm42FnqLtFbvURNj7PPUTUFSYSAo+rggIvxMHA0vgdtqIM25lh7\n/ddK2mR4BQYkfU4dLRT6H+8usH+ymmgZ/FX52bW1seJgN27b8UwMvhOuV4cB\nHw0Z1dnrsUbVmPPfyLhODTcRvkYj+Ra7WF+Fx94PVHtfp6/bwKBwmrhUmvrl\ntIJIXn6XqmbKorLwu+NLKufq5lD1Ii1CEq2VZs0dSR1J1nNaXWL5DumlmyMf\nPKxpJGamRf4K+gtirZ7vnD3Oi+FHO7w40Ut+/ZL7xNO0OaZ2DwrMnCkj87jR\niR8xlElGWt31rR0C43G+Vd+7hBH8kP2DR//NQr+BQSUuGAmSqQN8ub29GHO1\nnO9N\r\n=NcT5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-rc.3_1535134093380_0.36893157652900976", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-spread", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fef6377811191b6bc54bbcd61030b7f5652fff0c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-nFUvUDsUbh+e/pgGGUFlc2lfOLWzBA8Xj1zqKl8WWe4LrRCAEp6ojUMMHtfwwCXyiT+3PArBGGQzNUzQZdlK2w==", "signatures": [{"sig": "MEYCIQCUFQpinEb0lXwecsgOJhnIXoqxhFnnReSpQQLcjSbg+QIhANfP4ZqEaEiGAMfaF4KcjaQdud4SwzK+NToLXgTvRJ62", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpsCRA9TVsSAnZWagAAaCcQAJz9hCUDRtlOJQwiOUqq\nFZZAn/KGrkN9ukQTfJ8Qhfh35e7f+XF2TneSkvOTP31hzjI95H+zvZSOlFcz\n6UTrGuBT8GezvCGk+RvwHrY+J5w8SNiuYUuxxsLvm9vwaBHXEkK0I4aWiIQT\nGw5CUBrypYs8qsa7/SzEeH+8GzBKKPG2qJswz0m9eRRXT/YBb7bWAhtRUeoN\n7UduFnrZ6C6oMQwDAMatU5xN8eXQ5+2Xd+/deVO4lsU/+/eEibnSL1XfdtX5\nmwNZF95vXCeCkvtRlWR1et6BIGWB2SDxiw1fNpX24CFs9kHa0jX1L+fyzc1C\n5vixnDXXu82N3dOHOxRMB5VfyjEbJqu8FUPn0z0xzXumTJoCwsFfoOK03S8N\nDp5FanVq0AtmxENjSmin4rTu48GQYPToI7UkVdHjbvyanZ0egwi13D5zXO5M\nbmxT7CEDcLfLe9rNG4NOg6bDeGnMmxTSkn/Ddy02gUGoJrqqtHRe8JkhPpN/\ng5EUBwQjvz7bTzVibzeh+J5k4kxhrJS5HmRZxxos9pD1ZngMumv/KRIONl3V\nrurLoNR8+WTSnyrdGFNxkllv0ShGHaTkRrqt9DbBqZUEk+sqCbqd2MzvkBl/\nnx4UaCMIZiLjAA9J0imgTSzbeRrSZGrGX7wLTXsFLa42OIieziFRbnRXb5Vx\nkzzA\r\n=kRvf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0-rc.4_1535388268384_0.5205204445413503", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-spread", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "93583ce48dd8c85e53f3a46056c856e4af30b49b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-L702YFy2EvirrR4shTj0g2xQp7aNwZoWNCkNu2mcoU0uyzMl0XRwDSwzB/xp6DSUFiBmEXuyAyEN16LsgVqGGQ==", "signatures": [{"sig": "MEYCIQDR3sAKBpOgbBtIBKfKelyv5JJgwH65wX6YS9GWOcLk7QIhAIs+v4sHid34ZfGdzhbqoM/8YGL3qQpl2XOXma0+/Dci", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHB9CRA9TVsSAnZWagAArGwP/3P823fkDQ6t+sxWBEly\nskPdtJj8Os0VZ3XcW1k6S7smj+ZHEz8fE77mklB12QAKTSlumYIPzdcw3S6p\nQoyRgZYOuxGp038h4q1BkNwvT/L1PgD/SQENH5l7EQNc4ysO8fzf1MeFFLk2\nmlXuj4cPeDAQY5WghDNWmbzxYU4TPtsUI3EDSMx0nLz27TJQI9Zwp7XU74il\nUyu00xOb7ooOWgCVNGaNRWZy2O4pV0cylatzW+R7ebyFhbuSnXpcGPpGMURS\nsjorVvWkz1Qncuh+bTSCRZNTUSSUyvnYmOHAAhzIfFA0FCwHN4h0lobzJhuc\nt/mUnLBUeyZQd9SES2R1s7z++sAsAsm8wsqPu02M7N7Xo8PRbTXFv5jrBD+s\ncS10GumljwM9OoJ108P3+CjlRNHJNu7JAPGChJfJIe2TgbFd7umFH2Hs3U3C\njsH4qa5SFEsye+S7qZrMrZDoYsoNVrVjjEdAfQd+jtOCqlDL2ZmCIbyne3fn\na9Kfpf5mlerGnTmQMfqXFxZ89kZi2guqX8MsIId1Kgrre7lFRssKTlLAkILo\nTT/woTplG0TnSx8u84ckcJX2EcV7q6saxcVxrLhsQ44EVoCc8RloE9YhjbTn\nVP/NGLxMxoqpi7MqyK168TLHK3fsSF1U3GFx/jmLOTorxUQa+JAxJ3YFscwu\n9X3T\r\n=ZmX+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.0.0_1535406205258_0.840279867675404", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-spread", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0c76c12a3b5826130078ee8ec84a7a8e4afd79c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-7TtPIdwjS/i5ZBlNiQePQCovDh9pAhVbp/nGVRBZuUdBiVRThyyLend3OHobc0G+RLCPPAN70+z/MAMhsgJd/A==", "signatures": [{"sig": "MEUCIQCpb+IwgRs22MBzgcq1mRAZlTtAbxAOG2ujUvSFPHiCAAIgNuFAgr0EoVkrgkDmPW1PllYtcxB0BYUxUI1EBZAvS/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2eCRA9TVsSAnZWagAA61IQAKDD8bk/UNJWQKbQFFAp\nNCe48+mEqAxicXsNtzt7lLUB+RI159xGzSKeyr6CNXeFIjDdCpelTlFa8DKv\n7DMH/lyIZ5XRuJS9u2sKgIGkBWEmQLs8BIKgjNjMfKsfq050rlerNGie+i47\n6fGfSj6L9F37UyPnSXfBf0mUELrUz8rN1/QLaaI5UFRtIoN6WNGyvTcrCf8f\nerBgNWQLu3rHxAnpobRUNKnPd8QYqnuy3QfGK+8wghRmSRqRb3ssDNZK9dzv\nP7Q1vBSgw+R69YPIi8hmxA0mQKrnE9fgQ8Nwbg98OdCzyYbtlTZ4degpHNYS\nNQH9bi3qwCeztFQhWDa0FsVOVHBU5MFVpjuueF+ODyEmxv6/6K/wMt/h/Wa3\nSi9olWgFzwY2zQz+xaJiZBWsq2jnsVhVzyzVHvXpqDa71W+XCXF7VDRItQe7\n1CsIWk82cFLqc2n3ly2odO6QS+7V5hd/F6J9nzMvyZlMvuCfm2H0Q4/D150X\nzZDKbUOL+6ONRvp9nskLRJ7B1/xRLNc8eQLglXQX1r4YjaMwEpFy8sZluQr6\nIjMXkCoLAtJUJ4EPAI2xycL9SgSs0o9GWBv2tN3NBRU2zLJGwKW7aCoVCwTX\nVOTdQ0AUHXtsK4ZkW/251ZKnJ+x4VnvmbwPKnXY73ypodRziUAdiC/JCJtCx\nUpbv\r\n=6MPE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.2.0_1543863710088_0.7227780950293687", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/plugin-transform-spread", "version": "7.2.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3103a9abe22f742b6d406ecd3cd49b774919b406", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.2.2.tgz", "fileCount": 6, "integrity": "sha512-KWfky/58vubwtS0hLqEnrWJjsMGaOeSBn90Ezn5Jeg9Z8KKHmELbP1yGylMlm5N6TPKeY9A2+UaSYLdxahg01w==", "signatures": [{"sig": "MEUCIQCfUERsybOoa2KndeRqM0a98lDLKxkb9wCqEQZ+x37hjgIgOjdonCiwtXad3rYpA2RcM6oFZPOZjgnq2WPzkPyNXtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHgCRA9TVsSAnZWagAAP1wP/2Q3tz5/Mu7OvC8kiZNT\nvTfKjvP1UYBKFF6NJx0TsGEGc13G/0lhNdhjVDi6sWjxfgbvYkSyHfcVdYy+\n+P0vW9LMJdmBOJ6ILu4iF3XmhiScSZQYC6n/LxoIucJHD597g77daYN/xkWD\nWJz4GmhePANG5fmYaQGxQYs7JEN9o79qTRUnDNVl0rUdcZ45tG7NU8pP1JvT\n2BWXw3cAM8jDZVAXLli+ekMPA3QwvadKNW4mKO06RZuJEfqGMkLC7uIJETG3\nlqtKBXdx9xyfX4tQrH1oyZ2xeBA1tCIZLbjtMOBbfA/T3ZqWyBkXV80vbaTA\nRiaWO0baxSHZsGKr5SWj2QaqqvhzQpPasWn/8sayz2rowKQjY3OUUBGAUkkY\nhy15uTdlCwiW5rZ4/x17amt8DKChuKmsBcrGA6FyakMt5boquxbHIjD2Crle\nh6ey5hvQvD6bb11JZcZ4PFIBs7n5cupRGIcubgD/AP88sP0THvTkowp+8G2k\n+1AoSv8D7F2lWwqyLq7k5C9bPClT4NKxXji7FHO5wqVuems61BiKuyX/3gcs\nJ0o7fBT1NWp1do/GXVdPAygsJ+HmD5uZKWgKLAmv43ZN2sIByhUIOG2HCWiM\nQR8wimwpaxzNhC/zexx6m9Py+r95ZbipckWvbPnFbLr+Ff4n1VITg4n8TZVc\nmDmW\r\n=uLHC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.2.2_1544868320054_0.5589972976030093", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "@babel/plugin-transform-spread", "version": "7.6.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.6.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "fc77cf798b24b10c46e1b51b1b88c2bf661bb8dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.6.2.tgz", "fileCount": 4, "integrity": "sha512-DpSvPFryKdK1x+EDJYCy28nmAaIMdxmhot62jAXF/o99iA33Zj2Lmcp3vDmz+MUh0LNYVPvfj5iC3feb3/+PFg==", "signatures": [{"sig": "MEYCIQD5DdpePFzNtZMr4yXQds3p2isjm80I+sZbOc/udQRmYQIhAIsAFYPloh9kFETwak3ChbToSzQjebIMMo6cvp5pHFps", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdfCRA9TVsSAnZWagAAhMcP/RZ5c5JMy+30fjS2fcuH\nbs47ow/+4IhW7NdIeNO0WZfHQsvVNA+uoWC+pZxouJm3W04w27Gs6YJY4mab\nrYT32OU8JwLqDlLKDkh0Dlfe3RKst3WRUFMRifiDg3gkm53HJeHz8y7c6gPS\nJiiYuYwWL1/uPUmxVhC5TVx1LtQoo58KtTt2oVKQ+H9SVRtX/kHaTsrH5Vzj\n2y0+P+l1c+kXj1x+9fmDGkjDrdnG3E8TSG4mMta9XFSShbkCoSakU1aDKJ+o\nbMcyyfqF3KJgQ7imLUTaodeh4/GIgscN6veAk+b1eKFqaHF7t7TzLnOMSfJt\nl3CNATPalgc5iirNw3k16VjBUHnYbOyWqLpurW1EYds2z7lqgTPygG6t2FcQ\nq62/9Ki/pif0dNy2sFvVW5+lDY8cdJE2zLN92MH9aoDqubsBSQGtFmS4xRPU\n2FmRisgucttaxknxGzFlR2FzMNMUwD7+Gj3pfnvTLpSd0qJ6rMvV/5GYW3Ef\nXs4WIimxmhcfKng4r5vNEMOaQ4SNoLf7Va09gpJgY6z8XYbE9M7gMeB2vmaC\n9A07jGsKWSy7WetRFIvNRw2IlPbXQ9ZRWG71fSE6nafThGT94ulv4sr71bKg\nh1Q8qxBIwCo3t9jQ9kVUc1fq34RPZ+RxTOfCbm4mtn87+ud3KpGu7QisSoPR\npufO\r\n=7akp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b9cb4af953afb1a5aeed9b18526192ab15bb45c1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.6.2_1569273695158_0.6795290430720435", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-spread", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aa673b356fe6b7e70d69b6e33a17fef641008578", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-8OSs0FLe5/80cndziPlg4R0K6HcWSM0zyNhHhLsmw/Nc5MaA49cAsnoJ/t/YZf8qkG7fD+UjTRaApVDB526d7Q==", "signatures": [{"sig": "MEUCICebif17JDp0zFoRkLWV8KAFTxXg3o8E4u26M0rQ+7qNAiEAmTfAudrvZJBdpU9MtoxWbkSfqsZei6CVHJT870ELI1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HADCRA9TVsSAnZWagAAqh0P/3hAA/RmWqJ6EUnZs0df\n2t76A2uC0k1bA9L/O6b48utfuDPNPZx0D5cqENul40Vrwt8ml9/K8YydP7HK\nzfJny6AAwEWddep1TvuOjC6agpWwGRaukL7sh05PssMi/LibvlZuFTzTIKaS\nEDVhf8aQx1wd+gwUfTpvh7gdTTJrOQ+nVNSoxxcWRTgDFx1NO3WMWqd5HbCU\nM4ufALGZIXv6G+VqxAC47lrJQBxACrII7AGc5RVFp5jPyjLyPAls4VR1uWbt\nTsrb7ufGAkI8WiBhnvINNF5z08Y1N3J+y8DUJky6TAQBqi0zpsLdyhgNKEGq\nUlzqjiFGtRvuj7qwXLOkUK9TXnym7u+r/+wCs77Gl34NIfUFmbo3tL49JiAp\nHqjJ+mJ8ntAO3Uj/wvLn1V3+cyh0OBpn3uPpgU2Z+m/cPYRhaLvO7Nd9o+8B\nhsUlMsNny88sEpSVsjMFOI70SrfQMPbb4Yqc1sR/PS9LcRecgWli5idZzOFd\nkhwmSziVlveL7kWdudj2H0VAgbHYVXCUkIeHGEUzpNoOxg+UN2qWb7M++V88\nYoqR6ax1w73TfMT0bhGYKiuNMTLKyaKqHmjLB7K+4xcmiMXa9h149csYbws1\nzcfWoNF5G1FMzCcP8zmOOvKhEJwOoJrIfHtxGMMkm2E6OXAUkdg+awpZEIkb\nmb6F\r\n=ILkm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.7.4_1574465539179_0.795510886357625", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-spread", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "939e17585b1f24535fdeafc5e11a439520f4b3ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-6Zjl0pv6x10YmFVRI0VhwJ/rE++geVHNJ9xwd+UIt3ON2VMRO7qI2lPsyLnzidR5HYNd/JXj47kdU9Rrn4YcnQ==", "signatures": [{"sig": "MEUCIQDi3o7haPlTBgKNzBHEJTSxYKvmDbI/FoAMOZQPh9MpowIgdazWC1UkDT1vFzYHZax+vpbhM4aPwAZd5w+oklmoHNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVnCRA9TVsSAnZWagAAi5QP/iPjdu7KvfqDpt3mNt2W\nSAtgyhKQPBVAGGvK37WcVeQSgOztR/iznZ70yABYNjZfKpUK8kltGejOXXZ5\nY/SQNZsc4gJc0kYb0Hs2GE1xzwff9qRU3MXJLaPAK6tgur7G55qb+Vi0skp1\nTmxw8w3tIc5HXSRelpiH7JlUKb6gXQJj72ATM9R+8sNaOwBA5aXQ52CnQ9i1\nfBjMXHCDlu18cNbwysFgkjArv9upnrHLgxOCOfIb/NKjltAN43JGvZpwwt6a\ntk1vP/w2WM0yE+YgcjTaIvIB/5KSKdLUwWIJKzF3/bx/QHPIDUFFKvBNpLAV\nhqPkeRUlPbctCO5rJ98XHUtgb1M6+0yNmVWrsxfwUcUW8T3kx3rJth+dcXbW\neuL8elf+ZwE7eRHCQRi7ExBLfN4rEH3N4EK66Jk6X0I5YOtnQGOhyqJCzJwO\nmEEDJxKSQFpjeU303XHRWrnFJuY4nMdIp0gwDODa+7pMEP8e2jsOmo+jaqmJ\nQcs+RMOA11HIXu+WGfnDGSiJARJrqzm+c8LDsgh11V3+SzShvBGGo91YVyxC\n316zEZvnirpcS9k7VuvG6BsFmrtiZW+ejZrUsrZMGgkd4jvKcLLtbXah0sQs\nxVu/MD9h+fLl+bJu0CIPhly6tGmyTNACFcFn+HgqnGAXyG4tDqcjFGJ56xfL\n2Ih6\r\n=9OSW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.8.0_1578788199425_0.015319154589901629", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-spread", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9c8ffe8170fdfb88b114ecb920b82fb6e95fe5e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-CkuTU9mbmAoFOI1tklFWYYbzX5qCIZVXPVy0jpXgGwkplCndQAa58s2jr66fTeQnA64bDox0HL4U56CFYoyC7g==", "signatures": [{"sig": "MEUCIQDeQ9LPX3cSbZ6e87d6cWtKaf2cQ+ZTlDlSYWNgf4RpNgIgaFC47OVzpnOC974fR5J8rN+YkyPaa92Au5imeJOxPgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQUCRA9TVsSAnZWagAA8hoP/ihgixir7oWaK6P9vnmC\nWDWKgE28LDVzDx1e3ffdeAHIv0lqEX4EpfFtWsoLGFQV376uSVno3j+k1C3H\n8BPI2O8lwqKhVHunQt6pORQvu9ONeedW0JuRlaOuPHarOG24rV8/41QLjl2H\nBIUFy+0IerbaWni54hJjCfLRhCEy9dMQdNSEYkm99HS7MdRwpbUlX8gVbDaf\nZGKYpGz40iVSV6eEJqwS1+Blew04cJcigVOS2TrZ8CRE4GjBlOjZr/yM85dw\ntD0+K8X0eKST68tDXzs7Djedg/QHzpgJgDt09Rqp2CKexObW9xgJYVxSIgTW\nzyZfcXsvqFdslgH2P/1RyS1BWQrRLNlAw11t3b6yxRSUmsb1MrWKEoFZMWaG\nMGmf3yRsXh3Qi3if9FemUeC9FnQWf+MS2KOSb+CIA38OtUBu8SNx7md/8YpK\n61QHnYUuxmRbQ8sQNhu5CNmSfyOMIIqn0iYAnRfYZMInaw1F1FHFE3A/N/h+\nIoK5KqASL5mCoCEquO2bm9Iiwe+OK5CwgZ32A0j1yydbE7d/4tpCIv+Y083/\n1Hd3tzQMf3kpZo+ww7TCGoyPcjW1mIqU7qwItsRXNAmpFcohpMtfWLW/4K5p\nLXeiAVz9vIaMOYlw1f+K6X0Q8vfxfIsIqKEBptebKziFeVSkTTVMvAzgLziX\n3ldk\r\n=5u51\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.8.3_1578951699950_0.6900507589815408", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/plugin-transform-spread", "version": "7.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6918d9b2b52c604802bd50a5f22b649efddf9af6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-P3Zj04ylqumJBjmjylNl05ZHRo4j4gFNG7P70loys0//q5BTe30E8xIj6PnqEWAfsPYu2sdIPcJeeQdclqlM6A==", "signatures": [{"sig": "MEUCIHYK9FxD3rU9j1kwwSvPVHXbXd4yqgshIINEgEgClferAiEAoshuqE74hKHqagXRRYODR97uoAnmoZDUa256SZ8aTBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY1zCRA9TVsSAnZWagAAZ9kP/1yvuHIzPEjpWiOe0BEx\nk+tY/3fveXvdE4JNQBk18oY+P8QD1dPxTYsRdFpltdiK2GTOgehAZQKpisrS\n0nL2aZlFPlSSu9YepWfDwX4qI2mAd3VWzU0UpDVNSS6ixQYHzoIn3uygMpW1\n5lM2EO6EY5tk60BR5JPrDBtBn0yXbyj3cCDZpJkoWStybzTRiv3lhDOMuvri\ngXonHCdJoDAgN/B+s7/7CGhO7xPnRjcJpv2knQY84tN2gtLOoLD95+Uyx2h3\nwByIUjaPVIMY3UKOLE8LXSa0Ti5l9FldcNrkuHb1FzIPad2SL7qLjqtxumlq\ncRztRqU0mn7dgzxyILrpRC8VWNN+t+p55f/kxiFCMWqlxLOqUqVaFI9pWGb2\n4+1VD8mtABhoc2YjbZxPuPxNNcC/bbtz8QEL5ZKdeROhEcF49CsdLmJqO1Lr\nrbd6nKRo1E9bO/9pdSV+BgsBGBYvgT5YCSmu8zj0ZWzbcuZAYb5twD6xjeYA\nrSyII6L/zx+3mghwYSIRYoso6b45AtF8vPnatHVA4kuKPG3JycyRxBV7G12F\n+K2waH/rymJiJL0Iypl2O+gBrMuzzigZ5/Y9h2xYGwKhUq+qbPrmho74vIEw\nkMu+oXrA7YGw7h1WI4/s/rTAJst5isNQk949c/G2fefiGJYD9pqor1R9ed/L\nfdHq\r\n=QN9X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-spread", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.10.0_1590529395126_0.6816264017157738", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-spread", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "0c6d618a0c4461a274418460a28c9ccf5239a7c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-8wTPym6edIrClW8FI2IoaePB91ETOtg36dOkj3bYcNe7aDMN2FXEoUa+WrmPc4xa1u2PQK46fUX2aCb+zo9rfw==", "signatures": [{"sig": "MEUCIH+8oQge2qs42/RIKXxO4FEGQwgBWMWy6lRcXPGAoGnqAiEA+UGJRBDj0KjNPuNZmntWTJeWw7xruoZskS5NGr99+h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuShCRA9TVsSAnZWagAAxvcP/2kYeteqCsbwY7srBAMT\nbEKYyZWzVF4MJ3n0y3O0UC2OfrtsPcZSKHzBhnDRpvFlfhEJUIN+zdpyi+6D\nFfp+EI/SpnmlkWJ4M8A1pChhrIigdPFXXAtpMRD4GC79Er9TVIuZj95//C8M\nlP9aeMzDBSzGcy1BLhazIEcy8QsGfZXu3u+KighnZuelCqZRN+wTGx/K+8FY\nL7Wa+8TRPnYiXsGtzeztqfPe4o4XvumWmCFH7QKcy6Pec7uGXjwtjeeULwy5\n2BMv4J8v9FAetAdq9oFV1vY+h+MLRkU3V5v10anwi01MtsfhVUytANJo6SCn\nenYAS5dsJzuonJiydiiSn0KHWSS+DsU4z3vPMSsDnTfs2CjVK8Epdrj6Nc8p\nsPh/SlcGIGmJP8OX7ot9SwQ3DHWCjAATnElXkvSm9ubyR6j6eci4dSwzagLU\nVcn3lihWTG0kRn0aGuozXaZ+/wlosWvg8LbQixYnV+vC8p3u7bmhcwCP91vR\nwF8aQZS/s2OPis9jduq69YeHSQF3kfd+ylQEvMQAgoFM9u5jtYir/jR1rFaa\n9x0sEQQCYNB6didyxcr/VL8ImKzmZQOJS2CibtJZUBx8dzNQu57hEUMHqlsH\n5ZiGpr8p9oqL3sAGteRpg+OsQZ42kgfM/NyA1NvihBtCkOxpujyZgS13CNUL\nFi1p\r\n=VfIu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.10.1_1590617249522_0.17127632934707004", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-spread", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4e2c85ea0d6abaee1b24dcfbbae426fe8d674cff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-1e/51G/Ni+7uH5gktbWv+eCED9pP8ZpRhZB3jOaI3mmzfvJTWHkuyYTv0Z5PYtyM+Tr2Ccr9kUdQxn60fI5WuQ==", "signatures": [{"sig": "MEYCIQDcD7027w7Fy3nijKxZhJiafjKiBlnBMm2H95GIiV9oKgIhAOuYi3RXrJ8usLEoZOrxxiGyCyYCqqHrZem6bD5o+AfW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zotCRA9TVsSAnZWagAAN6UQAI6UYWW9GqgjLBI3eARC\nxtbdN/pLaB3T0PQs/t8k2IHHST0UVUV98YbSBJB7euQrZsj+HX7KzakGm54s\nrqcELSDR4SFVuzsjvqXAYXqXEFsA4yZxZjZs8a/LmWsRZmxerJLUKKiRAAqi\nIyNa/ktDoBnkzvtYffEVmxMEUwX1xidErfJrFUUBGERp/CQc9lQ6BQbDVY4u\naXW0Nagn/i9pMXrGHNun3Z7s8nPsCsW1ynsj6VKwREb1Y4pwGHvA7jRJHerc\nFJNKvvMUo8jBDW8qT+dgkd1j5nQmtq/OFobs1OXcN84/0KIb+K8Yl1h5x4wL\nK3c854wvgVCvwKWB50/Uh0RqtLv+I9565VRQEUUO0CfsACq3Jp8sdQ8+1+Xo\nhsty3/ilB0wPObYZOwUOJShexRLkiU9bLxMfPaB0oe4ZB56hUI/NB0Ejh5wP\n8RhsPyFzy0dTEY44QH9gZeL3phmt+KYgyGfzgPVa+ubQijZWv7C69u8VnTo8\nF4NuiuZE+4avQ2jrkwgVneyZ9cn+oXZjTmjoqL1NofjnlJV/ded3IJZDEgqC\nX8Oac4IfADj8V4BTg/5ZR71mjyV+9ngsID5P7blokoOGlchyBsXT2i4t1kSW\n5DhfiWJ+igRPf/wd8jth5/VrhpwxfX1zB5v8Pww83f9agjYehiRQayzt4eS0\nkkxg\r\n=iZCu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.10.4_1593522733028_0.42723464880463347", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "@babel/plugin-transform-spread", "version": "7.11.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.11.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "fa84d300f5e4f57752fe41a6d1b3c554f13f17cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.11.0.tgz", "fileCount": 4, "integrity": "sha512-UwQYGOqIdQJe4aWNyS7noqAnN2VbaczPLiEtln+zPowRNlD+79w3oi2TWfYe0eZgd+gjZCbsydN7lzWysDt+gw==", "signatures": [{"sig": "MEUCIQCRFHQuRXjWsxUfdCYF1zyrHj85ddHP2xScKkeLynPQWAIgWk2CZfpolmCpc6EwNcMI+chB5PSLZNdA31vRn9JLkQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIztrCRA9TVsSAnZWagAAVCYP/0jBTiajueieSXmkBQWC\nzw8vI+VtITfLOKR1JikDRVPW9sMGasjasEqtpQke4CUOLR1tbCLE+2RISllC\nu1ZDfnmh4LyM4i+8IkvsGc5SSreR7cKbfxQ6c22hG8kD1MAQ0lApw+1WVBPS\nlwh10T8XZwXDeVfzjzRARuqm8UzZ7s4hpP/W/vOENaw2Er35CDwjLm+poHW3\nz7wHSxPCwMQ48PTsTMKMFcVfgtNXqZfTR7RmfhVPD5J//YMuaeqVgmE536qk\ndFtrox7j87YJvcC7/7rsifFJbULZlCk4+R+Sqq3ouLChTE+dUhyzmnTcDJZO\nMBVQZ47x0j1kj6kEgDi3MiFGf1JT7GyEeaE15+gNeBlkUYA6xYGpt26ptndN\nYnAgbFzLVq8jtZm+Dt1TnPGJLRMPGNOJJSXK8wXzUMVCvuMXFkljlNJuHOMP\nA279OgLmCP46rzad1v0lvROMGe4eckv/DHhxInF8dy+MFLAK8Tr/mmsitPfu\nCtPh9YskT/Eern2rljBF3FqDs6JHQTISogFKUyAyCF2A2P3WtZE8nX4UenVd\n64v50txUOnfH++fpRwRVu2D8TC+yhGQlQ7UOxwqC57ekbNezCS5ahe9bs+Ja\nt60j9KBP16RHe7SjMuOYjdP0xPhQlz1GhUQBwCDWCbS7Tqz5rKgUYREGPdEz\nmXev\r\n=uIUk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "_npmVersion": "6.14.7", "description": "Compile ES2015 spread to ES5", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-skip-transparent-expression-wrappers": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.11.0_1596144491375_0.6161860034678239", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-spread", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "527f9f311be4ec7fdc2b79bb89f7bf884b3e1e1e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-vuLp8CP0BE18zVYjsEBZ5xoCecMK6LBMMxYzJnh01rxQRvhNhH1csMMmBfNo5tGpGO+NhdSNW2mzIvBu3K1fng==", "signatures": [{"sig": "MEQCIBbdELN9BG3ZLTDV4o8oHjgfqd6OOoE4YJXdvpDg7BG4AiBdMlyahDek0LK+kKzsn+UroE6aVELxbhIk/59QSbxOyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAYCRA9TVsSAnZWagAA218P/1u9Kvwxz4weABdUunox\nh1Mdh+AlOf94qZU8Ws1ysRN97N80Sgt4x65DUPKCbiZpT/aUcpz6az9AgRke\nEeZyiQAKDfD1G6t7f2z4pt402Hq+ZEFvZkfBZizivLnNKxM1x7SmdgmSQaHZ\nB0bQvrHl9AI7AqavzREEUzvJuY0QFk2RmmABiT6qZcWBweA1TnUp6WFe0JSg\nX6gGobwcr5DIZl6WVNqw7KSAn5qcPA8Z38F10JcYP610JF+WzX7AiCcoAq85\nFYLLv9J7dfKTxzLLsUrJulej0iFpJQtK3BJIh0/YFp5Dkh7Meu90dhHuTCp2\nDT4CHWmo0rjz00J0P0+QhUVQ0RB1IvsRsGhf/RZWysh4xztiYMI0fF1d9eZ6\nWOwJ6Zdz+Ws3b/ceUafwb9S9bw3tTUMquqEcHw3CR9Sdz4ha9TdWCpsyKIPz\nSoaSzO+V69xW7b483c5NSq+fQWZn/WrtE1MPg4P/Sm5Nk3pXaOKq8kbfkHLy\nLlXO9eprZcrVjd7aQk3ZjihWHYI6dyhl9Cu/Y6XOEJyReLWt6PVOanPpAcnT\nCnn/8LV4mxjBSeESDXtAgv8/mzZGFSJUiVo1pDS6oGryQ7YIgk37nnuvElI8\nH8p3IFoWIhlGImqU8tLyQSXlrQ98NrxcH9523FLoozN1+XITNz+Aon4fJZYV\nxhud\r\n=d2k1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-skip-transparent-expression-wrappers": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.12.1_1602801688217_0.5720390474467605", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-spread", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "ca0d5645abbd560719c354451b849f14df4a7949", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-dUCrqPIowjqk5pXsx1zPftSq4sT0aCeZVAxhdgs3AMgyaDmoUT0G+5h3Dzja27t76aUEIJWlFgPJqJ/d4dbTtg==", "signatures": [{"sig": "MEQCIF4K1Jo0/LoPeeCKu/QwDh/UlW6Xy/+Zfs/8n3AVikX2AiBydql+p4Rh6qeZgdThg8OuYwnRT84qTz0cEeHauRbPEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgsCRA9TVsSAnZWagAAdHIQAIpMF1lGUpjaFXnAUl/m\ngFP2mruOYPZVLRocmKy22cF06mKXa23RhwcppqkyEeO96/qRT2HldEX/Syhf\nylmBgI8qe5YvHLoopWDMEqnmGn8ZQOAftmTbmhYmgMHxXMJOCmEb79DYy/fs\nrg2jP0s09q1Vl32neyJEAonOTVpFBr8M1cwr+XphkVzXhbj5RSaYTVpNW8Gx\nrzjExJ6utJzY1xFmHp7Uu0F+o9tjXRfoD/NL9v7zia5ftOfx6h7EhXc7Sji/\nDm7NGczWtXxtjQAV+Q5xG+kx4SQb3f9YdQsa1AR8qpD2yoocj4VynEuFPheu\nmxM7tA4AR2tIS4k2WVLasjD6IDZ88csqYPJB1aFIZ/g5bQTJISpcmSgsLrIu\nXs2lhSFV30jJxIvzg9xIrJHeulrIqI8S/Q3jUtf19dQ8VvKFHDamwhR8KuQK\ne2QN/zkJvYAuHbsOt1IPlBlvKGStx3cpmSRDAoTzjbD3UOAm77zsAH5JQZT5\n5zf9876jhjng2mzreM5cMTw6Y3e1ZdL/KygUJ9mzDWvkyazl5chjVlLcAjX6\nV1IwXYFWbc3EoYj/QeuXPTEvAcu8dDF1bCAXP2r68/rWngJQQCnBq+ta7qgv\n0cUFqipQYn6Hi58FsAoWEPBjy33GrZu/ATEEoyYchrnvzuznYa/tA8cpRtjp\nhf7v\r\n=xZbi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-skip-transparent-expression-wrappers": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.12.13_1612314667720_0.09566799981147578", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-spread", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-spread@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "84887710e273c1815ace7ae459f6f42a5d31d5fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-V6vkiXijjzYeFmQTr3dBxPtZYLPcUfY34DebOU27jIl2M/Y8Egm52Hw82CSjjPqd54GTlJs5x+CR7HeNr24ckg==", "signatures": [{"sig": "MEMCHyS06Vz5Gb7jWkaF8qrn4yneeE3JVm2cqFqwDklkSdMCIHeFPtPx/BhuqCLiuuNdrFCbO7VKUL5IzIyGiKu6pc4F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUZCRA9TVsSAnZWagAAeGUQAI90SURgIQxQv/cXzvCT\nTfk+qM4lQCKucy1b3x0O9RhG97VhR+5fbSE1y91yWPcHWxT73KSQIVNA/H7a\nnB16dKidcah/qPc13sOd6wAiVNxcbktzCS07JAAyl/Qamf6BTGSxchUA/t+E\n9N4oeTr4KDZnsOBDdcukVnXd08oucLP87Zyxo7neNK3MEPqPhq6Chg1YEpPW\ngJYnE9yeJ03NXeDldukdy4bUBltH3tXe9OFHsMSYGFajXyCiHakr2zDiYGfw\nWlzWF8OtcV7ol+/3jiwj+za4o9xaWnoIchgRXxPe03vgJugA0vFalNG5lqvw\nO0LsBFjDYM3Cu/B3X+v4uf4UHj4vHq+u4TWNpVXWH/kfwSW7ocz8zkaKtRfl\nskgxb0VchULR5MkukxQVDOO0F3Pjz4/NNbDxvMRqf5Ys1Zj8BUr+2M+u2XtL\nV4xbKXB++IIvJJ2V69g7WVJYt+QT/MpxNfrW0Meu0P8m46uqvo87VeQCBSZZ\nBYsSg+48HUDVRJ6F6qsSbuhWsG3aIVfXp1H7sSVyaQGxs+GHDXa9ySiHaQ2E\nhVqJH/ttbtOQI9wgET0oIN4PLQKnVKGa1cEDy20Z7bczN0TpM/VCpPC4z7Pz\nMQUYiy1uG/COJYDCZgXdKyYVWvHuK2gg73G64+7M3oOOIy+jLY6EzTn8hJwV\nAYpr\r\n=D2kt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.13.0_1614034201119_0.728890832644157", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-spread", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "bd269fb4119754d2ce7f4cc39a96b4f71baae356", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-/3iqoQdiWergnShZYl0xACb4ADeYCJ7X/RgmwtXshn6cIvautRPAFzhd58frQlokLO6Jb4/3JXvmm6WNTPtiTw==", "signatures": [{"sig": "MEQCIH+9xoHBXcxIqKLTAED0hyK+Z5162/PmL4XQMQu24UwtAiArb39EIpLjiA/Q4+rA480fbluu2klQCBwlhMn4uibSMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrwCRA9TVsSAnZWagAAu6EP/06r48uXJKQegMJKiVrA\nOnXg05xNg5cdQmWzUmdCdk93w5i4cPHjO9CRxYW6fTSmgBevYhkyWJGqyK64\ncXT7Mgh3OXaAj9NIGlnDEYpTPr2IPXGifeg53rSMjsah1o4MNrmlORxSEq/5\nxlT81DeS0oN4M/3lMJ8ldqhnCuASg+6M1oERZP99CMnxCfDeFL7OK1vyB//4\n7OZU8ZcvwpU7wZhjvCiikAeT201CshfBMeDk66i9GoxS033dV6ykwSVwOPhf\n7K27rKae7ub1k5JljGvsMR28fp2z1f3jNkuUC7aFy51hJoXdvA+2l6wYtfY1\npPoz9AHNquaPWIL/dl8lsoUbd1iEyt/HIfG2vUKC5lGFbhtb1RpAJJYgkIyG\nkmNaBeRB1Y3I3glTyJUhkdOfUGSVpWbE4ru2WInUYKjjjVwKXNAc5ME9bB2r\nLZEBC5xk+yRmhDmr0GzA7sUAGGkKpxO+ihr+0G6kd2U8ExVHlCkZ193FBDvB\nxZQKdxDLoIFgvLa5CBJKF89lFbsExHiU5/iOk8SDyywjQQpnCHqWGfcwRbcd\n4maqEB0shHjULgwCBiSn/mAfBCYGzPLxGFGrenloICqgCS4l+YXyEiFgL4jB\nqKNnTl0X1EyOue0iFXjt+14uKbLmZoewxLePWxU5qZGTgBHJdMpMIOwJNR70\ndfqA\r\n=lqO2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.14.5_1623280367894_0.965392459683956", "host": "s3://npm-registry-packages"}}, "7.14.6": {"name": "@babel/plugin-transform-spread", "version": "7.14.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.14.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "6bd40e57fe7de94aa904851963b5616652f73144", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.14.6.tgz", "fileCount": 4, "integrity": "sha512-Zr0x0YroFJku7n7+/HH3A2eIrGMjbmAIbJSVv0IZ+t3U2WUQUA64S/oeied2e+MaGSjmt4alzBCsK9E8gh+fag==", "signatures": [{"sig": "MEYCIQCauhCW5LkHkFMquxdIfhmHN+mKnW49cs4TJpYbgnyg6gIhAPNrGFQiUnhFPmXNAevGlHFwBff4EauK0lbCiBwe9/Zb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx9CqCRA9TVsSAnZWagAAmxYP/1pCCpB6jrIZUKerB0is\nVAre/oIt/2jth2D4bunRjrAelOYmtErZ6Wi7NhrV3ugHQBbb5PyGeZ8Wm35r\nv5DGZXs97509YxVOyw9C9rX9etcwZMkBhAG2iq89cH1opR1OzhIRUtA4o1Q6\nhu9fcpHlTvMNCAyag88wbx76RttREIz5IUplNBT5D+SSLqDm5UazeloqcuBT\naL5IcMElg2XIeF/eaQ4XHNJ5sX0SD9YyttHCgcwkq6TvzKFEadwmzWBn7Y9b\nzywjb6K5u1PzMSBUVAZbRYK7EZaMSz1kVzTSTzcD+Owg2/gw11IEkFEvtak7\ncGGYHh4dBHICE2pFaPsea1TvyioNToep0Ezcb+xr12HS+jO3fOkD2NeycfWM\n+06RISO2OZLKJbVW9bDqip/KqvcerPPZc0ezYV3LnTbZspVIj4/f53HGLuOj\nKp3KBB7cUfwsz8IBnpjPU6YGFdLiDEarZ1HM0Mohmz6xHwZ2CHWCdAHYveil\n8F7VRp3Qg9lm2fqV2ztg1wxH7J0L1n/OYJhOQtMTFFOeze3eL0ruCjIJnBQh\n1VU2TojmuZRmQ1qMxW/Mi8vrMhh12UGAif4qK1AUpmcyDotjqeWeloMV3Sfs\nKMDchJYnsjYpm5fHKAAsjXOtEPAJvf8mW/4q5oB6G29lXW3HM97YB5CyKJ9W\nFj/2\r\n=/876\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.6", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.14.6_1623707817876_0.24256196713229228", "host": "s3://npm-registry-packages"}}, "7.15.8": {"name": "@babel/plugin-transform-spread", "version": "7.15.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.15.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "79d5aa27f68d700449b2da07691dfa32d2f6d468", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.15.8.tgz", "fileCount": 4, "integrity": "sha512-/daZ8s2tNaRekl9YJa9X4bzjpeRZLt122cpgFnQPLGUe61PH8zMEBmYqKkW5xF5JUEh5buEGXJoQpqBmIbpmEQ==", "signatures": [{"sig": "MEQCIAwOZfc+giWX946TrwHuh0i3LmWaD297U62yY3lDuhw+AiA1qFTEc0rPP9ERQ6zsCCikkW1uhHRGGwXWD5ohRkcL1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7609}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.8", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.15.8_1633553696660_0.4825524686047784", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-spread", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "d21ca099bbd53ab307a8621e019a7bd0f40cdcfb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-Ao4MSYRaLAQczZVp9/7E7QHsCuK92yHRrmVNRe/SlEJjhzivq0BSn8mEraimL8wizHZ3fuaHxKH0iwzI13GyGg==", "signatures": [{"sig": "MEUCIQCx9/qBTHWSRYKz4YSHX/Jgo95tXF5ENZ1FZStymoC7+gIgC1ziYgn7z+d5n5vSQAnyUVGXlmyiGSHBbFNeGVw0el8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7611}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.16.0_1635551268367_0.5462390482504045", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-spread", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "912b06cff482c233025d3e69cf56d3e8fa166c29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-5d6l/cnG7Lw4tGHEoga4xSkYp1euP7LAtrah1h1PgJ3JY7yNsjybsxQAnVK4JbtReZ/8z6ASVmd3QhYYKLaKZw==", "signatures": [{"sig": "MEYCIQCuOZncCoc4Ld0XSGMzj5uMkB5etQviNxP9LSCxBjPIRAIhAJ/65U/UuNvmViY88nU6cSrfDvxCzwm+wBkel/QhndA7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kPCRA9TVsSAnZWagAArXYQAJHSf2lvys/VMtsDlBBz\nVy4WBHrb605hUhVkOtiwNOXgpL+ohB92wCVBshdp9sdou/i5UUIqsIkDVwEJ\nJyL/fImno1WL6StDcdxjulF8dRCKI/RYhA1uJF7gjgf/Xx2BhbADj419tHS8\nNI1J7BR+adNEmEJOfP0bI/R/Kd4NXyVFOYuv0/8/GL2Dw7S42KUcGgGvWsBH\noHRA+N1wlbVnQyaseWXa6g6V7UpJR3GzhM8+hFCTwv/YVz6q3ahMANKkJB/a\n61/6/7Ff7jq5eOC0Nbh95RhAhraxwlvT888C+XCpNNv/tuwiYIo627Y+R7xi\nARTBp944Tu9hwR07Tzo2r+05Yh4G7k17P+bBpECsIxKCBXPyHd6vTIYIjZN3\neg/AQlMRFO6MqEHqsHsesTDiu/ssyk4golifZP0lpSPOCMsMQoHXjgMdW8Hp\n6OatYeilYVL+EBVJZgXkuHvcDhUIeikyKzcsdP2gDhBCngSRxTajRxH6uEYp\nlR25uCpo+juP7CUxyNCC/DhUIjsQmMpflkLIvNQXmegBLuUKFgUP7jy1Y4LH\n1FXPU+j59xiLsnzbJ/P/A/goKXagMycG8TQtBa99FwuA03m01mLdFaMYY4nA\nxslONWwz3LDbjn0+BD10TDS/ZEQVodwcfL9mGsXr6ZiF1/sg4qwEUFX7tbMO\nCcKD\r\n=yJdx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.16.5_1639434511452_0.870121092183846", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-spread", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "a303e2122f9f12e0105daeedd0f30fb197d8ff44", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-+pjJpgAngb53L0iaA5gU/1MLXJIfXcYepLgXB3esVRf4fqmj8f2cxM3/FKaHsZms08hFQJkFccEWuIpm429TXg==", "signatures": [{"sig": "MEYCIQDYm3cf0lzEPhx47kJr/p+lHH1khZuS+JF13hXsRFnKPgIhALhswXV9BNCPDIuDU9UEDHJdD4m4GZ1XMd4xh3kNhxWS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0yCRA9TVsSAnZWagAA+PcQAIEufxjH2yce5BgWD2Zg\nTotEW9lSIBkdWNSK7uA2i1a+oO1153cPrrxsTLkQav31AxEqZRvXgSN8dWrL\nbWfMfNCcss8eKM2NkQcqfr/kN4m1Q9PGaJLVeBUDjDgVj8oI2SDrG/n2kftR\nkiq7hdraPNz7nYLFK6HWUcSven/50mDOXHUKFRE+rXqs3eJBXEgjdz9WvryA\nhCL/Y3FSyoMY4eWxdGuTFhTM87lpkPzqZG2EFLNG0k5I7WXv/QtbIYNboecm\nPZeJz5gHDax7RVeYU+T/yoIBogqLzVx4EOAJ1FaHJTc2rFBMKMhw4D5snENW\nOR494+un3GFAIJogLOcrqpfQA4B52WvHMBLaFHyjkCigIRFyK3G/Ng1G83pf\nwNlrA8wvpd0wLmB9yYDUNHygevD+xFUpmNnyFPMrfXS3mKJvBn9yFnl03fe6\nVYa4FYzpQ26ILg1UFwut3dK/emKoY1902CumKWIILtIRlrM+B2oDCeQ0fLoH\ndjvj8qbU8U/mzyjbhdLoAFX8Xuq7LMP7XHbQMQWjAPb8pI33jCPfnIKFfqf4\ne24LnwbN9hVcCDLolF22tYU/zcnWqhjRQI+7O7GtcXEj0eNG9UP6tRV57ss/\nS19qUaXU0UsiKSzrfMBtnDGdCqhfyk1wMnDLqPC5Z/Au8NPZ8RTIeG5XzkI1\nZNK6\r\n=YZ2q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.16.7_1640910130132_0.058766425304678904", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-spread", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "c112cad3064299f03ea32afed1d659223935d1f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-9pgmuQAtFi3lpNUstvG9nGfk9DkrdmWNp9KeKPFmuZCpEnxRzYlS8JgwPjYj+1AWDOSvoGN0H30p1cBOmT/Svg==", "signatures": [{"sig": "MEUCIH0bmXv1JYSVX3OUbz/44Pul87Mxm8Zk4QSkWn4W0NQRAiEAiMLL++6bZ55phTV0kptZJcd/qI1pTPqRqEHQBpuZ9u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCXBAAgpFfqu5XCQe7l43I5G40+NxocpFaeZ3myQCYiUsILR010fje\r\nfGAxqIxOxGLTxj07S0jSRF84FO5ljR/dZmH8c8nyksnSl/dGBxOS5E66kAGq\r\nQ+2X/IfENknCF+zGcFNAO1hByn8IiiIXoMOFsJa3GjtiNDVpOpPTrUhvDFW2\r\nrw7p2sfeb3sNQCVSltpVfUg0G7/Au2tRxcsy4f4qiLEwTBpxVgAUQJxV/gLs\r\noKDag81PFnjW4ZIziqXE7SYIcmRETQMSOXjqy2hwXgcOVHuXEdklZ9LJZouq\r\nQ2noOl5oXgDMX48mbWpK5MdynR99m5TV0mqCToPiUb4y6SOxoY099wloS24M\r\nF/hNX3qKDX9b0PUXt3ClCigtpTpuq78DCEXpDpIX/ZByiUTdYSl5WrcKbhi6\r\nbYM2reUFCF/pxe15uxTpvER6IynppnoepnPJKw04dQtsE18a0r343yp5b5sG\r\n/SdUSVJQ4romMn7EwAKu5PPnPYD/WUmnzJJ2oKCvK0nD/fB42dDW2uXtHqBN\r\n5/1fLR6KKH7n1PejWSM/EhN+B/1erCezNYh+TdBr9s48QTKeiXROKSIooz6O\r\n7EkAKwBiKWxRwYa3wlX3RuCVmSlBEHgnElVzl27AnLQn2SAIEwnuOqiYszQW\r\nXCJbqVYum00iNz3ZrjwmV0R1LDFDtpYn2cE=\r\n=2T4n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-skip-transparent-expression-wrappers": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.17.12_1652729588365_0.6127171143270178", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-spread", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "82b080241965f1689f0a60ecc6f1f6575dbdb9d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-ayT53rT/ENF8WWexIRg9AiV9h0aIteyWn5ptfZTZQrjk/+f3WdrJGCY4c9wcgl2+MKkKPhzbYp97FTsquZpDCw==", "signatures": [{"sig": "MEUCID+9SiTWQkg6thG4HDI3DKkz9Ok3hqVcvFmKNRhCPkj7AiEA9L9JK2t091tJgvdsbcseFwuz4ULabGx0j4tvKeSWbLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugn9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAag//Te7pZ3/KPMBTQ7YuzV2igRj6/DKAHgsBWVL4f1lPkhdrgGXE\r\n7DzIb3LlNdt/i53+xLSdUlD6aQsfU9b7vp6To9f+ysUgcZd34r8nIiPET18i\r\nudOO6YObCfydj1d8bfEYXJSXbHGiW07K95SdJv2HkWa9ikDYYHWRhPE/NaE5\r\n2lIFIamdlHVBZFe+6z0RQ/01bjB6hpkDBQ9DhwNVYdDVWeq4hvCFRkq4C3Rk\r\nW9xKDjwTaiHE9fPcTXa2uTeHG73e30rlxx+Bjn2P8EtIigcnRH5SmAcad4vC\r\niUX0IDFKXDt6i7MPb9bnUW9tBJqjxT+vvsUm6J2gA4rK5ADXin59e5VVzUcC\r\nN3i/+8Lcw7FQfzq23x9xk9nywuHzM+gqKBc+bYZKY4/RqKWoYbupEw2PYe/l\r\nDz3zJ9M4pLInpCW0E4LYYrcZdTJYAje2KAYYNz6xupRq5kQgwliuIhrmHL0o\r\nB2eIdTyR4+RXatU4hMUtXXdy/S/01O8ZIGcdvJuLBRbVlram/aou/Tf9PQSZ\r\nE2hsLpCWOtX2YYftDAiotE2ngc6KcuUbuV78uNlX0pHSPmIQVJzeBf/mCN9p\r\nJfDsVDCrF4irKvaHqwPOM74o0BX/jLCe9QZtxo4j2kmCHhcEGfJ9yEAGDM2r\r\nb3diwcprd90do3fnNjmsx3nUkX507fFZVd8=\r\n=bJpn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-skip-transparent-expression-wrappers": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.18.6_1656359421474_0.7163730270799711", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-spread", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "6ea7a6297740f381c540ac56caf75b05b74fb664", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-39Q814wyoOPtIB/qGopNIL9xDChOE1pNU0ZY5dO0owhiVt/5kFm4li+/bBtwc7QotG0u5EPzqhZdjMtmqBqyQA==", "signatures": [{"sig": "MEUCIQD+dtGV5kyf+WJS0e1L0iheb6zjezkrcFhlc3mO5ZGDNgIgd7/LsXHkbAVbcXKoeXyEfkDRXW3gbPBvH4C88ZU9sg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriZw//bQFBh7zB82N3XssH3lnPSXbwzUov1tMIzn2/ZTacd/WV7EzB\r\nHse9kTLkG1WQQ+DRtByN8x32xBwJ7EjLeBh5UKp7EYxZaLJzZCWmEnO7Z5v5\r\nK0aknb5W/ITLiNiH8/p55x2OMXV2RiEujqcBKKA++KaCv269Lj7Jh67YRxWg\r\n1kXySKQiw1u+aNcBTSOi/F0xtFQDUA2R7ynZPcaHQxdumM6I+ii2GTbEjFD/\r\nsGc3KF5VmSbwECKa9jjF8q95G80dIZWqCqHQogwd2k9IX1SmqotJJaMM080g\r\nmblTOrChL34dadXgtjVEs4x34F5UxjHSTU53eST26VKKSQIUr9gG/xGMlATM\r\nLo/v3S5QopJNWeX6byY2OKc/thw37gRVfK46vQq0tQG7bEqcRU9NuzwYHoN7\r\nIOXPEPoqfby8TCd3mSWp2phkrENl4h+Phv5h3487OhzzkygN+dzbvbnGGdXW\r\nYOX+xHSleinLNYMCZCfKGFx8G0k4QelDGSjfE7gqdFR48RMkv0Rva8ihAJ2v\r\nx/vOapvlOPYca5R8EjiqPFGBhM6pfDwRbC/RRF9kthJPoKgULfTqxDIK8qEg\r\nXYDZKmN0j3pj/Tx2RaZj1YjHtemIDfDDHKd52ZpmQIPHBzz7zDm8AlgT0Kc5\r\n6IRITTU2NaL5hqr+ZNV2w8Hpq0AtK6LeWuM=\r\n=O898\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.18.9_1658135858225_0.5246617349609841", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-transform-spread", "version": "7.19.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "dd60b4620c2fec806d60cfaae364ec2188d593b6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.19.0.tgz", "fileCount": 5, "integrity": "sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==", "signatures": [{"sig": "MEUCIBOEOnnQ+lUyfC9CneWwPje8e1qmUQDumbCAp6SZot4OAiEA7G9IjGDqWaXfjWTJ6CYBzZYShNGPTv8Eo16dcto66/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqt/hAAlMAU6xONA6tFDRa/ZUjzVfZyefb/X28kMLm1nrVRNtx7GJcf\r\ncvDToygn89GYQXIqEi3LK0L4qGnkeGkachJALgcaznAYwhzNETQcoZkgm0cn\r\ntbVSe35gqrZrHwkZfrEoGvXfm7sV8sQZFA/V+aY/HSGIy2qBrxwReDrDZ1rU\r\ntcaERI/jBUhBnHmu6YThZWCI3tv8i5eDZSQfuEAdze3pHYLbMsfLOYccfipF\r\nGP8flTdN2RoYF++yNmho62SMhsKV6aguw1qbAneVkTEAzn/A76qItoemlpk9\r\nCQSLM0BLTiN86Ed3sp/Ny9+lAdWqH63btHTK5IH3hQbu18fcUoQ3U3QBrrVu\r\nvUWPxHGpaj6f2G68OBSLa23u8k93w3D5YPAG2nwwajHeS48vHpwlUFTBBo+Q\r\nE7OH6vSXZPyCSclBigfZxcZoN0sR3jdOQowBb29V9oBLW2TMpKNTPv0Z6mjy\r\nO4WVQQeMPOLXK6mVdAa25aJvF0hMgqqy4K8Kcv/uWeb2Y1qbj431NUR77uey\r\nOPR1s/DErRid69XPkocd0S29P8qnJoNSp92tFcY+QsVEHkDsDRj9sHKlOhcm\r\nJjYeXugQcOx8wt76owQQE8NDgMslwPg4VfFcvs0f0JZkw3if9B5ojXK6FO3j\r\nvdcKDCDZ8zDfKk7TZMAia1NEKW3cdJqlv+0=\r\n=Xyob\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.19.0_1662404538837_0.09547985017082672", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-spread", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "c2d83e0b99d3bf83e07b11995ee24bf7ca09401e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.20.7.tgz", "fileCount": 5, "integrity": "sha512-ewBbHQ+1U/VnH1fxltbJqDeWBU1oNLG8Dj11uIv3xVf7nrQu0bPGe5Rf716r7K5Qz+SqtAOVswoVunoiBtGhxw==", "signatures": [{"sig": "MEUCIQCMPfP2scSQiiDS8kHfII6waElYJqngIkytj9AC8REcSwIgFMyRvANtrCG4BR3lMJeQ2aZrGwVqv+t9kUoqiC/vmyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCczACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokrQ/+My++7tujTE9FqGP2YWCLmcI3okiHzP9KBCp1W/+Oujh8tzVb\r\nWJKOrZDOVhHK7r+czSLCGiQF2iFJKqhQdm3qRQ/F5P3QrCD2hLJfLQ1BmcVp\r\nlwdOyhI43KV0pzVObY/FYa4oP8mw4XppJaJH7QU9HYqNcQzaZqp2c72opPHu\r\nsGLYPKSleWqWkCZvk2xBgvo1crU7C/hcveFkVmrwjqrDVqq4INR/sNTcWwNl\r\njn66uBSTWlPQOnjGeKOskv+7lLM4a8NZrPh59QWlkDzB2a1LttgJso2X3fZ/\r\nHQPYN4MfoWj9lDt+X1T3SStwD0dg1CIdShrM7Rwb5cs7EOPId07bMCLAwpf+\r\nWG2XDAKJI0KNqrxqbGfy1i9lg6iSHR4ocsoVLiiGtirZuU3wGt6LqQPiyzMW\r\nPy1wQTblEVS9uA2uGsvNFVX/YkQUQXk68sDL71bruwuTIygVP/yjp2bnTSIv\r\n8SdV1JgyaYeqDngmky4gGdMY19MLguWoLXzDIKOmJGTx5Vc9AEd+uh3i/WCp\r\nBWeReQFsDzSFB+W4BfygWHQ7xv2DVnBq5J6N6tWKhIuIW3zvJBW7LQO2csNd\r\nx3D2u1S7tM1SYlwCwAx5MtQFA4sgrY76LDrLvm2tv/cT+5vu5G2J9Y/96E7D\r\nUClAzD8HljtTP10c0GmTsH/C8ZlRypGoELk=\r\n=UZQg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.20.7_1671702322830_0.5684945297946622", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-spread", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "1796206ed666c6e5214e95e6cd20cc507d6e0bb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-E5fb/wl23zRpnAv2wT1hL20M2CEgxPqLAJQeF0TylTQza50HwxQMzumVKjqBiZvxXNHBFFyxWOerXp+RNUO07Q==", "signatures": [{"sig": "MEUCIQC47qir4LNKv3FPP5mcCt2TJIkxscny9tGgxhvEuAMIWgIgI3cNhhnyiQpr40lBsmM3zEx9wVlILkXlPFdtuQALm5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrizxAAoXzZWbVZY4zlFCeghq5cZT81w+W+1FgDWfMc5VL/IP8/bTdq\r\nq9KMNR2LhkyuGWwCE1xM8fj8pH3YllTR2mS8eOLlEc6bhXCw2YRuiahCbSbF\r\nVNUJwIfA4I4Y46xU7yGNEdVoLn4NcSefl/1xnloUp0gZWOKP0YbOkxH8ThFn\r\n+7UMBIxkkPyw0ulheHLTzzgfdX2Csp6+2q1ZPas9tZCSVVn6YfwkCrFAmAbw\r\nyX1dhtdWQ2CLB4aRWmX6kPUJ3+w3nOPP7eXkFCriTJ7UyBW4y+WN1CB1nWGH\r\nOAuwNuPwjcJQiBmlTneJtkmKjvtDvL4qVYrEYU2HypHFSayR3veVlqgmC58F\r\npgAvGMOVawP4thp3NWhyCsBIGPn8uBnkIumaZlDcetz2jx8+ZJ5cXbfh3Dx0\r\nEmDwuaqGBWh5tl2SCoTjqDpLgvMR8FRSQvUsyllJJ6h8oFcWWYrqBDnANusD\r\nM7HDtUS5Q02Bro1WZj/2NDc3DdBAHm3vxKTAVUhzbWZBwh8dXgMGnm+kNyjx\r\nE4kYq7Q9KznmdL+DgI2iZ8UWiizsiCqm2u0Buh6LyBY7gmHBdl7lNd2lfpHL\r\np04exODQ4Xn25mrz/EnN6KlzazeIlBq7yIaOZ5aWQY/Zq7ersWOlPe6g8SCS\r\ne6i4u50RDvtk2biYtETIfosM4AD1CM/Db0Q=\r\n=hJMM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-skip-transparent-expression-wrappers": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.21.4-esm_1680617385197_0.5896048603856716", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-spread", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "249e5c05a20013c5148473d4f2d787eaadd8508d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-iGAOjiZp49t1CU0lqxajK9FGIyvCTDDH4A5nzbOR4s7zAWV4JA4L+e69+ESy95fE9RFbgKt+fsQw3GWIGVeqgQ==", "signatures": [{"sig": "MEUCIAvncbSSgQrD813xCc+dizf6/qgSESCDPk2BIMsehcONAiEA7LHnEIRVqGlTuYWCbnogSRa1hEPYEtNOSF3VHBrexCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyRA//YJsm8+0/A4GbpNuO9vUkCOmWym3v+hb0JLx7S8StzQKw7orh\r\ncqEBpoMHUMIU0HzcbIW32jvd1iDvoRBfG41zZUWwUncRp0VPzPLMEnALVA9x\r\neCvoX9SfYEiJ6yjWrw9Mn3v/Jouv5VWEb26+eOfL+4WQYe2MChSaULVWd7Mr\r\nqnLvJVTHk3AKUuJijHuyCfD9xuSjLWRnkAAyh+ce7/J1hot4rbGL2fz3Pn+5\r\n7HpG0rHORl1XgG34wU9seeqWDb3Ld0y7cA+45LJ2hY+LhsU/8nYYC9Wb3eMP\r\n1/zTWmYMMLEtL1jSZb6gvT8/RwgY70Ka2VkI7glWZS8oYYujOlCUr85vmuxh\r\nobKOBq4ctgalIAfO9n4kEmAHAxv+rfAQauc5d+a0b9IlQWG7096BtZG7yV4K\r\n8tpsoW2DJ0s4OTzb9JaTR4fTb9Q6hXtKHhypO2cjbNZ3gJmc8KSdzvRIKmnO\r\nNDthjnbTmrIEMpnt0xcRxqR5764hRBIPBf7zYeUzFFo0QDikZFVwe2/PZ6xo\r\nF1LVvVyp7FcH6fjOwCa4sjDM7+jswxNVm4GlS3e9BpquHCaYFBdYSh9wf9lu\r\nxJyHfzMeyCHHEAAuvYkb0hMSls1AmtuZnEWp0WhN7azpOfMwd/B58zwC728c\r\n375SO1cpV/veqziTy0rxlpqrfoQdj97eCms=\r\n=R8DB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.21.4-esm.1_1680618102369_0.7229970678049866", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-spread", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "ecf65de86f478e2c8eb3ae0bdcb5078911d09a07", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-R7QSsrSriEgTHijvplWAHFxW+9Q9AoBh4bF726OoJ/WP1LvcaNRii08kjqTxCi2TRLgrW1EWcNJzPUCKe4CxEQ==", "signatures": [{"sig": "MEQCIBO3Um4aGSUXbneB7mxuHrPkJeNuwcLtR1N0kg12cH0/AiBbmnoaTtqViF2YGyucWw/qj4hyd0nldkIq5yHWajhtEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDarACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBxxAAmuRbevIuE+I+WIZpRZrNWJ+P3bVD2J1AdyZhPumHsLUKpPxI\r\nC2pUvMotdgKeqyU8hsI2X2a7fkx6PNDhUtl+YV8GQbE8mvCWN2ss1BAzt/vq\r\nc64uMUytu8EoN/DkFnsQj7nBeB9OKD5Rjrk3DbNhVE4bZfZjhFkoMaFGuPsL\r\nYwsJmp8021LfUSQXL+95R6l4q2IkcoGEC8lyzLn5p0yCRDbZbwPQuEXxIPaq\r\nMQXVI3HaP7jG5pOPLOj+tAciiTtBsss/O6T7LfYEbioQ6O7bRYbdW0VqPcdd\r\n6JKQM+E/PyjX4O5jKhAX8PgJFrbPLLet0f+G3gAhL3AXfGHAyovxXHJQ4ED6\r\nOwYJ183ZmqlgwKR8nTUIwrRsXcYKxi+YXjgb+90vPLV+Ihm8qLOx0ICQ3lfZ\r\n/577MEKYGCmorOBHSFRRyhLyQFgr6Ws4yv9XdlrnSBXKRWXoIIZK0T5yM/Qp\r\njc20VtdTkTwO5RWFmdvncKebuUdypy6nlwlg5mthAo0z1VWPLIg0KiPHSwA5\r\nhh5/bEU4zZwd8ibAiAjo7bvRTrjdiGtZf7IzutECkQxq1+Dmps8NwvRd8kUs\r\nnJB5X+2NCO25RNkKTk7+McWiCVnUzRHfsBdo0+m044kMXdXVf4L20TYDRFr6\r\nmtyh151OgKkXlY6aD9DzpVEW76iqPU8nLko=\r\n=5iFl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-skip-transparent-expression-wrappers": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.21.4-esm.2_1680619179224_0.1374423632621764", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-spread", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "60e8c0a365cfb32af5a81b02f1132a7875983e07", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-8GYwyVlpNREy0w10C3V6wi2Gz5AkU4ZprZtwCrISso42rNwf3fy5g2HajTt4PNBONvx/re00SEMRgitbtACl7w==", "signatures": [{"sig": "MEUCIA9cmCw2KQfKNsXnjRFVBixVmp+5pltfK1Z0EkkK5sBRAiEA+TfAWnlhn/1nhi1fzrHLdJwc9e7xGPrnR9RaJWWteNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofqQ/9HPhxpZbizZT0ow9MQbgLCvNAMAvfvgtXU+B1xYdEAquYzOGJ\r\nKSzGkJIbAavn6oj1KYfqVYPtogxljq0UuBdf9O8n0Fndb2O/M621LcdVLJRA\r\nOLInPS+07aQsaRJQy5ay6QwilAUnhE39naDGPgGs9XHaFpbCYIomnxqVrT9e\r\nDsrfvsAfG74Ne5TI5hkkBqWbTVZ9bBjPgfjrxwLqkQt0W7cIPmMe/HM4w/y1\r\n5F7E2wbtLXMEhr1zBoKjqAnkCOy6JzLVsa2l83d0JnJKm3pIYHLd2KW+k1OT\r\nu38HDmv5avKtw9GWsUlXBW2zT93GTzIU/AVrV+cM57an/DwpsTR55mBulheO\r\nF4kHOkt7YFrsDQ7JHpQXLfz0iY2NG88mUB/uwvjEFq+Rzd1FUh9eAEbqSc3X\r\n0TuWseiyEdNHcTqe4FyOzBb0fYFWQtXldRAnBg8xgF/jRfA2HWcIuxd9lRXX\r\nzRA9C/nNf4/kFOgCjCOnY8uL0in7rR1CeHzRwhnSi4vg8fUEAKgDYI358tiw\r\nFLpVIliW5/VmX6k0firX7ROMe7atViplRqFXP+j5VzIIWiUkfmvMLZ/wkxxA\r\nUQVOtYcuSa+DhU+JoJw6Yxv5MQ7acwOa8sNfY42N+3RUA2a6x4L7eTHEUoh7\r\nOZUcTtvjRxMxT9BPm1d8nNMXoO3WA4zqGqQ=\r\n=5RPG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-skip-transparent-expression-wrappers": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.21.4-esm.3_1680620187281_0.29234814944419396", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-spread", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "400cbd60fcff3aeadfcc08816a9d65fd3db93aa2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-aS6xk6AKcgPIGJCiB49+3ulQBN9ox9rlihsz3RgaqLcyTEAwYRDTJfhEdO0OAbBgU9aDp+ilEqacFAPzhd0emw==", "signatures": [{"sig": "MEQCIEbJ5NB/jGEAtciZ2u9l2ayzYVslomcV75ukSMwQABzRAiB1YTfH3fplRCDHNR4QsTdNTwV/axGIfiUhRrhTN3i+mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0DA/9G0uJIF7yvLqnhTr5P0ESLUKMED9fkbWnZyrq5pTts9ureo77\r\niXMytzyMSiGi6OHUTsCywfw2qBRe7NOZp+8UoTsTLSf3EjnGnnU3lX5e/tRk\r\nJJSrAU1ptlTSv1JdoN9YrxjN/+DPw1ontjndyYkk9OeMiIQu+nWPYosIlBFk\r\nB87oaEk6l/Y+zrcyf2iXIEdf1W/ytSsfMhcKZoqTidJBeONcrXaa2plZE+P2\r\nMwo3ghkb8SF7Dw9Nkk4Qf73ha+PhsqvsKZICqO+mT7MpQSxyQrkCtGiGB8sR\r\ndLry4RE1OHKvnBf8vqqp1eaUUslct2K72v+GKS07s5HzZbdG26EYtdMd8oMW\r\nr/Ba7g16on3oA1EZHTrVmgE4C8rElxiKCrobE1PeGdgMW3LvkcbM22zcp7e1\r\nYhtUxkamlNFu/UEr0ToIzNNniucNhDAMcF6iByrvFqHog3dQUMPKzNkhB0Eu\r\ngb7cahignKs+lBF8tJgDqAOz1XRPim/4lJStr55VbiFbpl52jG9P22uNKWvF\r\n3Zn2NwB88x1/slqG37Jjvj0m0acjst8vahvjgUm02Uxs+WCLZwC2EDySx97V\r\n27mrUrii/A+5PiJrC1CkEmMRUJue6hVTpctXFtVQyzySeQLd8nIgZDcpd1Jx\r\nnUqNzX4rgT7StpmI5QiDuX/mMjLzjwt9clc=\r\n=vWNG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-skip-transparent-expression-wrappers": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.21.4-esm.4_1680621217469_0.39422780772887234", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-spread", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "6487fd29f229c95e284ba6c98d65eafb893fea6b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-5ZzDQIGyvN4w8+dMmpohL6MBo+l2G7tfC/O2Dg7/hjpgeWvUx8FzfeOKxGog9IimPa4YekaQ9PlDqTLOljkcxg==", "signatures": [{"sig": "MEYCIQDLURSG9Hojpi/J8SV8WKKtCvGRBmBoyXuAeP05rRO23AIhANmhSuN+Sqg9kMdATzUeIDndDgM/CmtHeVHanZdMzU9N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21178}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.22.5_1686248494445_0.5480515636286758", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "bb45e6a64fa182f67a7e587174b7d245d2465c41", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-PIx256yM4ombOWxy7HbkvYwzPAr3tM6WJIbpjLxpkqTjwwqZB4PrL7qAeCax0lqG5LHRUoWU+kJr71jKh8kTjA==", "signatures": [{"sig": "MEYCIQCal2+kUBjdxizLaGk+U5aZF8IHEAzdlxZvRB8mNi/PEAIhAIqoutXvzsOTdChCz3aJlCP2EUkFEfFhs6rnscbsxe3j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21154}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.0_1689861617307_0.6062490122577326", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "be613904c4f0cedbc81c5a05ecdcc2e0e19396c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-QNoORYr+ch8CWZrsmXvPlC1QLSNluPSUumMWvjbMpGFcfj2y/19mYhqvNGFBI0E82sGjyAebmubbL6XLi+toIA==", "signatures": [{"sig": "MEQCIGyxcFToVFXTBEkRMkudWfxcMyZErraNfM+WWLE1VQKyAiAmNwgnDpTWjqWX28auaeUjnf1GfihehLaRknVLlyGPWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21154}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.1_1690221170059_0.1686019840154358", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "89833d0cc7fcaae223a5d47400411fe33b8babb2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-LAU4bENgpN1mLAg0HykfrVeVgjRaiFR0RC5luVtuREbJdoCXqlSk2wmMKs0R140tgibl1GANbT0WL1fPOsmflw==", "signatures": [{"sig": "MEUCIC6vnexzGZ5ZzhDeMN1vaGlzjH6WUEHAJtqrrSIRus6pAiEAsTEgesEig+Cojd1pRDFhr4kd30bvKMaGHQ2jyHrEvmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21154}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.2_1691594113818_0.7567964794761524", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "ad48627967c00ff4db809ffea1ccc482370ea40c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-pwKRJ4Eb1AgD+jHEfkAlbDhjSThbIe5hQABrbX4WIM+TsaZxQ4LrWiTcr4dBMp2cJqAWwf7xyZNuSn1iXfiCaQ==", "signatures": [{"sig": "MEQCIEOZtx+/SFTHtGbdqUnQwgT1GI/40eXc0svRdHoAHccPAiBAjnrA7/aomDkdLAlGH5mxOmJbgpiP/H0sTzVS2ZyIFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21154}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.3_1695740244637_0.43931178529363946", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "60839c58213c2e7fbf44d53c03a44cc9802c8876", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-iQlUu5dSAiwfNGOwzGZqxylpbgC8IDw1HTtt+7+PKPm9lxcSxC8RnPrSdUP1K6DZ4OaICjtPLBNbGk3BdnS1QQ==", "signatures": [{"sig": "MEQCIANBZO4+H5UsI0b9o29D0fjVc/8OLb9R/SMieWMjflqRAiANqtBh9b9v1UQTEKB6w+CYYcIUEqt4VutowI7kj3pWTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21154}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.4_1697076398843_0.038456755108436935", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-spread", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "41d17aacb12bde55168403c6f2d6bdca563d362c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-VvfVYlrlBVu+77xVTOAoxQ6mZbnIq5FM0aGBSFEcIh03qHf+zNqA4DC/3XMUozTg7bZV3e3mZQ0i13VB6v5yUg==", "signatures": [{"sig": "MEYCIQCaooP4tXlqZPaBTTm0peTGbq+S/52fY/g21x4+D3cQhgIhAPxNa7fUUgxbnxlP4SOpvqwI2obbV0HC1mDmwT3muqAW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21256}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.23.3_1699513441451_0.10441359050677623", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "3017c0e04147f42b3ddeb7dc806e2169cd6f9e2c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-1h84xBG9b0c+R9PuWKV+l0NhgSMLPWceiG1zRxyIyQHiLmgRNcITme8UnvA2M6jdwrhkiSWMU7sWiceyu5i5RA==", "signatures": [{"sig": "MEUCIQDhdXBX3eQDORxwV88DkvkKUTaPnicUzjIrYsqSyU4NngIgdhr6XEn2ofv///bPgB+Jf5rxw2ZMdA2Gfru9ZDuUqPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21267}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.5_1702307967855_0.3426500124170382", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "d678a28599f1c5ef984e33dbd1e0cc956d340a39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-xE3U+KmcGLWqLBkvUCw2zjOubpZ8vy5cWaYVxiEiT9+LA6dJk+ASllzFS4gstiww+ETeMcd86uwzyi3o0ayIxg==", "signatures": [{"sig": "MEUCIQCzzy+Yq/0kjY6se/I0iWVb2urci42FxR5wLY2Xc6rd0gIgYN25vBPvnv1omptKr+fheQsX59QS1aHVXaHOsi0EOPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21267}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.6_1706285668385_0.7488569972907306", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "d3214902a5df5120f042d675f1c9ae8817a183ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-m/ZBNfho0Y8sh26lbtVZ6IBmbfsymaRB5wshZn9tjiZveD/t4F3YAJ2GHhzYBEmAYTQHtVLjsLpJ+yqSOS/IPQ==", "signatures": [{"sig": "MEUCIQDkjxJXGJpQGdF5iPk0OaTi63u3VkkyfRIhKkOFteaIegIgRLfl98SD7BvW+K+dkFIY5MgcIHlMjUI8AwFwIBha9QE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21267}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.7_1709129126812_0.07031440603916583", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-spread", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "a1acf9152cbf690e4da0ba10790b3ac7d2b2b391", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-KjmcIM+fxgY+KxPVbjelJC6hrH1CgtPmTvdXAfn3/a9CnWGSTY7nH4zm5+cjmWJybdcPSsD0++QssDsjcpe47g==", "signatures": [{"sig": "MEQCIERJLaipW+zz8jXHJrMxJ2te/WTxN4Zza9TdBuDlG6IxAiAXJlLsiHQ1VjcCr4J89OZUqq40HXhb8iEalI7EzFnUkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21187}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.24.1_1710841744474_0.6669653226294157", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "132de87c83c269c288a9c6ec3cd92a3a25fc2891", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-OJtCKdm3IoO3Rx+KSzjUAwqKWdwWbmUnQHK0tafL7WQUDWProtD5lINGBZtcqm7Gmm0eEgrZP/913c4n1hy80g==", "signatures": [{"sig": "MEYCIQCrAqYMH+5wUdUPBIwHyTCVEYxVmexhaTVAgjWkVp6zzwIhAM4f6Zd5StvcLVhjF3TEFRv2pFdgvxWmf73i1afWmFAz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21181}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.8_1712236808322_0.20659432050918158", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-spread", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "a56cecbd8617675531d1b79f5b755b7613aa0822", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-h/2j7oIUDjS+ULsIrNZ6/TKG97FgmEk1PXryk/HQq6op4XUUUwif2f69fJrzK0wza2zjCS1xhXmouACaWV5uPA==", "signatures": [{"sig": "MEYCIQCgr8JDyfsN7bExdqXTEM8LCnPaO8Kpevk/P6s6lbSTOAIhAJUb6O1N9pu2YK0A4aOpd81zTf4FAIP8NJaoy5hDTtz8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87385}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.24.6_1716553498641_0.6307509777364868", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "8a92c51c04d01e75d328b2605d8144eb3f9d56c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-t819s+J5+q/E+VJboBPf7aRioCHAjuygJrpxJ/kZqhE/mkju51dc0fAbxu3InwOdsPveE4U0wGqp2+Y39UgoQg==", "signatures": [{"sig": "MEYCIQDN3vJe1xMtCgdR9tJOnIguRbgycLsW5WGCscedjs0dyQIhALW6s/Enm0bOdZE4v6JGQYJ56ICycbu1OL1BP5UWFuAg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87699}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.9_1717423536278_0.6100874553210613", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "301a897df386b20df6aea32f014fb3d6b3fec76f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-1jOBXkY6vKP9aWRU8GGtKifyfoUhUWL6YInkGLsBbLc61jwZW9nbAIdd05w/fRTUSiNsa2OgEu954SQ5k7o5XA==", "signatures": [{"sig": "MEUCIBt4QsQav/85flv4ea9MLBfhN9lo+D/rjs4P7IyXK9sAAiEA9JFO0WYW3sp1Ufm1MQk+vQJdWJGmI7PPlmOjDQDn0gU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87707}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.10_1717500037248_0.9387411055312411", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-spread", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "e8a38c0fde7882e0fb8f160378f74bd885cc7bb3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-x96oO0I09dgMDxJaANcRyD4ellXFLLiWhuwDxKZX5g2rWP1bTPkBSwCYv96VDXVT1bD9aPj8tppr5ITIh8hBng==", "signatures": [{"sig": "MEUCIQC1sRx1FG9fxIYBDHiAWvjGYlxAzyQBqPIjgPp+vDSj7AIgCgslYLIphFW7leQSUwZoni8iizbx2YHQtx6pjAbxtfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87335}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.24.7_1717593351665_0.16408869092531075", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "2435727075ef0a6bb3a2bfef85f34104524adce3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-uQpAr+ZbHBa9aTy7LMrdTFkXT9m7l/36FsdZy7ndLj36dGq45ctvNRT/cXfStoHomTDLRh+e7vOzJ1REG9o5AA==", "signatures": [{"sig": "MEUCICfmr0N/zoqg35THIK87btfjZhqn6bR5SoU0FlwvbjrnAiEA92U063Ez19GxXO3S5IH+DGjda9TxeR5fX/oju1TTnUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87596}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.11_1717751762085_0.11975941086015007", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "512f96b19a96eb12dd265d436ee2c2c71f17fcfb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-75apDBg/M9YStS0WBCaa/CB2l4lfk7yC/wNaf2elSvEnROrXhnkj96Z9uh+YQXCzU7x231cVnp5DiPynUbNaww==", "signatures": [{"sig": "MEYCIQCL4hkMKXVlLBSNfqVCW0lzVZaJfPfkV3Xym08GaGoNtwIhAP/zfSgHP0LxtP11EcF7zip+1wAHahlhupcHB8FGB4mK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84375}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.12_1722015236606_0.744866874220306", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-spread", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "df83e899a9fc66284ee601a7b738568435b92998", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-Mm6aeymI0PBh44xNIv/qvo8nmbkpZze1KvR8MkEqbIREDxoiWTi18Zr2jryfRMwDfVZF9foKh060fWgni44luw==", "signatures": [{"sig": "MEYCIQDLKPs4upmKPprxOuylTPcwNSPd3/zsziNUcwLdCNXsYAIhANlNBeoRBQW3nCm3ZJudbN9bT2IP3Bj6CWC0lJK1wZ2a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94137}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.25.7_1727882125741_0.41899310497921083", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-spread", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "24a35153931b4ba3d13cec4a7748c21ab5514ef9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-oNknIB0TbURU5pqJFVbOOFspVlrpVwo2H1+HUIsVDvp5VauGGDP1ZEvO8Nn5xyMEs3dakajOxlmkNW7kNgSm6A==", "signatures": [{"sig": "MEYCIQD3YT4CL8Ttb2h5fhdI8kUAcK1U0zBPx1FfpE8ZNdIgxAIhAJ7R32FVOyd3fY/jldHwSScu5Q+pCusR9aJpOq+qWN6I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23422}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.25.9_1729610501114_0.5661758516947044", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "6c6576d15e74ee5a5b4ba9e85218dc995c4cab93", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-rQQ2qRtevcAKldwggFphJU5u4fhwGkZ2xbB1T7LnlZqwfzsHzycm7i9MksW6TuyWhly1Y+C90vu1PVjTWMzyHQ==", "signatures": [{"sig": "MEUCIHs4DR11dhdGRnxliZOpp7HrbSQ7OjnMhY1+SUhmt38AAiEAvK2oMjxlWwl1WkZcEzC8AFlv0Kz9vQLTyQje/zr7dHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.13_1729864481948_0.2822510794088553", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "4d5138868f99d3a2241c0a81c1ead8d68031afb7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-7d96Gxx2P86pFBxWShKP4tx71pKfy0JQkqUNpjtjGFVx6bRNe+gHvoo2GEMTmKbBgbmAARp6+Qj8aU65IqMJ4g==", "signatures": [{"sig": "MEYCIQDy5StfaREEglW2z041QeHIB5QD4YwD6XRSmCoLEcGtLAIhALlztLlkdVv43ly3CCvC65mzJOsNcY93yD9Esauz1Nu2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.14_1733504071240_0.830994181667511", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "e0f0224781bf32c41f05ae344593657310b773b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-jwWvexkSI1IaSij14ClFBZw/UbkVJIGKYvbaH5yHcGA+ZB1wWxvdphWkwlvAiXCCfT9Zgl+jjlT7khmTDS4Hqg==", "signatures": [{"sig": "MEUCIF0wiciQQ0/EW8zyNHcZF9qGFwsIAC4b5VLsnrm2S1WMAiEA8PhuEsXoum4UxqFE1HQxnooF5x0DVFJ4/2pQIM2oTfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.15_1736529899787_0.5978828550442608", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "de57c69718348d8859b2a3c0c794e61b3498251f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-U/4/4dRqX/tJz8l3XS+v5slbFKotIBqtD/NZ6IvK8akR8PrzM8gwjhOONADJk+8cfn4o4aY918I4Pfg5IMvCdQ==", "signatures": [{"sig": "MEYCIQCHHo2W7RmJG+D4KzKnlHhAtTrOwu9FcO/mzd8UwuPhSAIhALwibixrRjhTxXTG6i229pDm3/zfyIohWSlfO4s2Jf+6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.16_1739534375516_0.7262632859910831", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "8197f5f2291697eb0533a5569e0d3b0b0899db7d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-m5b/sWhQuqGvKfRf2f28OvtCx2Zjb9Ov1NIIQXtNeqLS8BQT07eCUzx7oMsE5RcbWqutvvqqvcmcq1JrS0Ex2A==", "signatures": [{"sig": "MEUCIQCyHo1y4IKeUSKI+BycAU7jzHi21wR0WTbutYsTx17iEQIgQqsDsqrTCLN+ndH3CBbcOSMHxVJliWmdUd2i8b0bkdM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-alpha.17_1741717528685_0.7694029385690346", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-spread", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "1a264d5fc12750918f50e3fe3e24e437178abb08", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==", "signatures": [{"sig": "MEQCIBzjUFJSP//NwaJzUOt4BGk9NuR4DTvql6LaskejFwTTAiBvP8Nkd8K/eK2V2INh6btISS5KGZg5W//pdQWQPtC3Lw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23422}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_7.27.1_1746025763903_0.8752254805847004", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-spread@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "dist": {"shasum": "45277656731d23cc1e91385952f89375d9ff9ce5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-rBAe8aELZTh9o21E77htfqR4W8a+fp6Qey5zLL3aRql8PnCGBPCDvzAr7wkrfUYKaULSy82mAQTsYhiliEP/OA==", "signatures": [{"sig": "MEQCIEnJpRHbxk6DeiSSvAXMWySJBXPmLSfs5xXwxXQtLhzCAiAL/2PIsdiHlezJdT2C6/FtKzUXKraQdTuZS70T1/ZSRA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23905}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-spread_8.0.0-beta.0_1748620300200_0.41436731849854436", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-spread", "version": "8.0.0-beta.1", "description": "Compile ES2015 spread to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-spread"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-spread@8.0.0-beta.1", "dist": {"shasum": "542f1f1dd49af9d486c34fb447fbcb503566f688", "integrity": "sha512-0EfC5BQ25vApuxMXpSRK6X8VtQJqizlyU3clPgNxm05u714xS2C0H6OzNYuommTe2gvOs0VHn1lDnmXOeUA85Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 23905, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDXPWBsT5WWn+1AjQeJDnklfsi/c9E9MJcJ5c8BC927UAIgQi2FnADDlu8QEaOaWH+mWrKXH6ONaxsRsPBE/mfuWs0="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-spread_8.0.0-beta.1_1751447084391_0.8170271160078217"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:50.007Z", "modified": "2025-07-02T09:04:44.763Z", "7.0.0-beta.4": "2017-10-30T18:34:50.007Z", "7.0.0-beta.5": "2017-10-30T20:56:28.456Z", "7.0.0-beta.31": "2017-11-03T20:03:31.566Z", "7.0.0-beta.32": "2017-11-12T13:33:24.054Z", "7.0.0-beta.33": "2017-12-01T14:28:27.776Z", "7.0.0-beta.34": "2017-12-02T14:39:28.775Z", "7.0.0-beta.35": "2017-12-14T21:47:53.051Z", "7.0.0-beta.36": "2017-12-25T19:04:46.148Z", "7.0.0-beta.37": "2018-01-08T16:02:35.229Z", "7.0.0-beta.38": "2018-01-17T16:32:01.021Z", "7.0.0-beta.39": "2018-01-30T20:27:37.552Z", "7.0.0-beta.40": "2018-02-12T16:41:43.767Z", "7.0.0-beta.41": "2018-03-14T16:26:17.135Z", "7.0.0-beta.42": "2018-03-15T20:50:49.894Z", "7.0.0-beta.43": "2018-04-02T16:48:30.070Z", "7.0.0-beta.44": "2018-04-02T22:20:12.546Z", "7.0.0-beta.45": "2018-04-23T01:57:05.610Z", "7.0.0-beta.46": "2018-04-23T04:31:27.200Z", "7.0.0-beta.47": "2018-05-15T00:09:18.710Z", "7.0.0-beta.48": "2018-05-24T19:22:53.315Z", "7.0.0-beta.49": "2018-05-25T16:02:31.122Z", "7.0.0-beta.50": "2018-06-12T19:47:28.756Z", "7.0.0-beta.51": "2018-06-12T21:19:56.868Z", "7.0.0-beta.52": "2018-07-06T00:59:28.873Z", "7.0.0-beta.53": "2018-07-11T13:40:28.392Z", "7.0.0-beta.54": "2018-07-16T18:00:09.950Z", "7.0.0-beta.55": "2018-07-28T22:07:23.552Z", "7.0.0-beta.56": "2018-08-04T01:06:14.083Z", "7.0.0-rc.0": "2018-08-09T15:58:34.532Z", "7.0.0-rc.1": "2018-08-09T20:08:18.740Z", "7.0.0-rc.2": "2018-08-21T19:24:18.651Z", "7.0.0-rc.3": "2018-08-24T18:08:13.452Z", "7.0.0-rc.4": "2018-08-27T16:44:28.500Z", "7.0.0": "2018-08-27T21:43:25.367Z", "7.2.0": "2018-12-03T19:01:50.220Z", "7.2.2": "2018-12-15T10:05:20.355Z", "7.6.2": "2019-09-23T21:21:35.298Z", "7.7.4": "2019-11-22T23:32:19.287Z", "7.8.0": "2020-01-12T00:16:39.666Z", "7.8.3": "2020-01-13T21:41:40.064Z", "7.10.0": "2020-05-26T21:43:15.291Z", "7.10.1": "2020-05-27T22:07:29.621Z", "7.10.4": "2020-06-30T13:12:13.129Z", "7.11.0": "2020-07-30T21:28:11.468Z", "7.12.1": "2020-10-15T22:41:28.590Z", "7.12.13": "2021-02-03T01:11:07.846Z", "7.13.0": "2021-02-22T22:50:01.270Z", "7.14.5": "2021-06-09T23:12:48.019Z", "7.14.6": "2021-06-14T21:56:58.005Z", "7.15.8": "2021-10-06T20:54:56.805Z", "7.16.0": "2021-10-29T23:47:48.531Z", "7.16.5": "2021-12-13T22:28:31.590Z", "7.16.7": "2021-12-31T00:22:10.502Z", "7.17.12": "2022-05-16T19:33:08.605Z", "7.18.6": "2022-06-27T19:50:21.634Z", "7.18.9": "2022-07-18T09:17:38.374Z", "7.19.0": "2022-09-05T19:02:19.003Z", "7.20.7": "2022-12-22T09:45:22.983Z", "7.21.4-esm": "2023-04-04T14:09:45.364Z", "7.21.4-esm.1": "2023-04-04T14:21:42.556Z", "7.21.4-esm.2": "2023-04-04T14:39:39.383Z", "7.21.4-esm.3": "2023-04-04T14:56:27.432Z", "7.21.4-esm.4": "2023-04-04T15:13:37.703Z", "7.22.5": "2023-06-08T18:21:34.654Z", "8.0.0-alpha.0": "2023-07-20T14:00:17.513Z", "8.0.0-alpha.1": "2023-07-24T17:52:50.194Z", "8.0.0-alpha.2": "2023-08-09T15:15:14.023Z", "8.0.0-alpha.3": "2023-09-26T14:57:24.825Z", "8.0.0-alpha.4": "2023-10-12T02:06:39.120Z", "7.23.3": "2023-11-09T07:04:01.723Z", "8.0.0-alpha.5": "2023-12-11T15:19:28.006Z", "8.0.0-alpha.6": "2024-01-26T16:14:28.535Z", "8.0.0-alpha.7": "2024-02-28T14:05:26.991Z", "7.24.1": "2024-03-19T09:49:04.651Z", "8.0.0-alpha.8": "2024-04-04T13:20:08.485Z", "7.24.6": "2024-05-24T12:24:58.813Z", "8.0.0-alpha.9": "2024-06-03T14:05:36.519Z", "8.0.0-alpha.10": "2024-06-04T11:20:37.509Z", "7.24.7": "2024-06-05T13:15:51.811Z", "8.0.0-alpha.11": "2024-06-07T09:16:02.289Z", "8.0.0-alpha.12": "2024-07-26T17:33:56.815Z", "7.25.7": "2024-10-02T15:15:25.981Z", "7.25.9": "2024-10-22T15:21:41.553Z", "8.0.0-alpha.13": "2024-10-25T13:54:42.177Z", "8.0.0-alpha.14": "2024-12-06T16:54:31.382Z", "8.0.0-alpha.15": "2025-01-10T17:24:59.968Z", "8.0.0-alpha.16": "2025-02-14T11:59:35.721Z", "8.0.0-alpha.17": "2025-03-11T18:25:28.905Z", "7.27.1": "2025-04-30T15:09:24.092Z", "8.0.0-beta.0": "2025-05-30T15:51:40.371Z", "8.0.0-beta.1": "2025-07-02T09:04:44.541Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-spread", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-spread"}, "description": "Compile ES2015 spread to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}