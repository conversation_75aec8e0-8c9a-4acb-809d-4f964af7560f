{"_id": "@babel/plugin-proposal-private-property-in-object", "_rev": "33-052d95e393e9596cfa05ee0b089dc76c", "name": "@babel/plugin-proposal-private-property-in-object", "dist-tags": {"latest": "7.21.11", "esm": "7.21.4-esm.4"}, "versions": {"7.10.0": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.10.0", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-private-property-in-object"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.0", "@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_id": "@babel/plugin-proposal-private-property-in-object@7.10.0", "_nodeVersion": "14.3.0", "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "dist": {"integrity": "sha512-0SXjlxeFiivLaZgJEuOZJT0CHa+JinMGPLuCJS9+sh/1MRpdAMq5EAbV7AHXDkyz9ASTNNoDxRUxIzRBCr9oug==", "shasum": "ccc92f1462c1db4b0ee50c4026873dfb359f32cd", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.10.0.tgz", "fileCount": 4, "unpackedSize": 3082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2ZCRA9TVsSAnZWagAANbUP/A01SexfhR2/LUbOvpe9\n2h0+KUHox2zu0Zz+UETsicf4wlB7OjOvDZROpx3oC0gPKOs/IuuKgbT4fUbf\nuB/VyJFL0+AdQrL39JAwP3lahrULX4Czmvk2rOcpgRb1oRCP0ndgnArds9UZ\nodjpF8c2wfSkdNtyy/f4OFZdlyPdAhNwvKTo//PfCB8InGi5Cy31qZ+gQT63\nR7LqOq+RTtH1uwu3eIC+BpisL+tPVozh1HaeRLcTea3K2AgOPKd5Ql6EcQGH\nvoKu2iOzIQJn0verjewZfhPW8u763ZIgeLm5h5hE4EkCC3EHbyxcA60RwpFs\nefOGg6cX9aEJG9e8SIq1f4wAucaTV7sq43DqzxcyWfNpty8VQ+Mwz7QnI9/B\nPE+Pv5YoFao0DlSBZpZthritrVyW0Ckam06Eq12Z0NUyHZ6TFIwfoBAsDTTv\nAwv73zBWOsS4IORvRBPQ0pqpGtIHdHO0X+Hs2HGG/0gZGc5qCtMOz3I2xGBz\nLPy1Js6PkfGo/tXKYw+tynH81PGBlin8x9pklF0ABVnQv64iRzx/wqdmlsLi\nKpslzMzVB72j4aX/UV2TvpahjlwL6d5xL5EQhhPNj7cErw3gOlizsgh4g+/S\nTdVCR+tpassSGeADkGpRa82t04xOQTptAm3lus1xS9dluJVVovs+MeI2rHyj\nEajM\r\n=ShjP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9cRGvjeXW07eKSqiKnxskVp7L3JBGituBM9QzUUNn1AiBF9V2T+WsMShi3j6GXaMB0gYXJeZKmYbySnxfyjeGdVg=="}]}, "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.10.0_1590529432972_0.7377971293376318"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.10.1": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.10.1", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-proposal-private-property-in-object@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-c4SfqvkXWB9Aw0+S/V6qxO0RF85/UTAiwVC6LYM5an+3GJDGyMqvGKQyzpdXGmjyImizRqkQs3vg6Fe0gtXcNg==", "shasum": "1ae6b19dc971fc38cf5adb76330becc5bfe6f729", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.10.1.tgz", "fileCount": 4, "unpackedSize": 3132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTiCRA9TVsSAnZWagAAoIgP/1vyPY0Uow+M8G7oNwdp\nVEccImpzfCvWDG71BnvcG+2VbKs10z2uUXodw6fUVhBGsmV5D6N5mBpomrvL\nllB5XOOm/+VTS8/Yercec6WH8LSqPBV0AflsANbOzFSLzVnOE7IC0CXmw3Md\nshoC6b0KsUFSSKZ40ZF6zkvqKs7Ph2QRjy50tZy49BIdbGvrTYOSx84zwSRS\n/QyYD/KmJhrmLY/skj38eOeF/gzDUzCAH9ONF+fNYmFvA2fO+qbVOR+Oafzu\nS2N+EBo+ReuxFaEzmwYzDZOTiiuGI7SsDsC5WBlZ9Y2Ov9x2/IzuMqeRcVLZ\nS6DJFMFbZ4MG/OGW+LWM41jwZv8BthliAnFl0HY+gGECK97M7ToCqVSC2LdD\nSjkMwnu8Fsdz7MiVJoXUErU5JqNYAEFfd2kLyZS8HItw5zF02qqnYUE/2AXp\n6uUIwrU1Kg9P6QHzV4Wp3WNRDRB58xK6r4mVk1Fk/ObqvAl4rvpjYyzEE+8d\nwLyp47DfdzNTFMX5Dl7o7jz0iGNNwquRQUhNKaF/CuXEBNzV64+RR6VkeFPB\nI7JvkU3ydBLNwecuM0RzrFAHPLFcRkEYa43KLBzMa0Q47WK+Tj3YiKIRiFMO\nidxVKzeouqWKPlHfXdnV9kB5jBPURSK/CPZN2DpOijCdvmu84eNIE34uWTCc\nSKQa\r\n=S04J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBAMkIC7SYHcrRzlMuPglRtuTwjvwvzc0LC7w/+TLW4pAiAE3m/ltlxOOeN1yLQ/+cKspyfbMsAAiuEyuLJBwB8FFA=="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.10.1_1590617314164_0.740191870652289"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.10.4": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.10.4", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-proposal-private-property-in-object@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-7PqUXlH+U57oHjfi7h+q+LF3C3kQRgPiOFGHLj5lduPO0Ih7R0MbqcXwe1n9gGafePI61N5ivqWfqYvck28F4Q==", "shasum": "8ac0f9785bdf2f86e48222a2217e8af98d6ba2c0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.10.4.tgz", "fileCount": 4, "unpackedSize": 3132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp/CRA9TVsSAnZWagAAayAP/1zKQ5e+jhBuOVnzvGox\n6YjgPEClyFHvuiRxsy5Bg49uRuGJWG70ykOz6zdfdYjRnvG3VZV2zSsX0EiM\nTbyStAZvjYSsP4g0o3a+jJhbxiYW3QCudhRIiwDtEXV6+3fyKgqNPHoUj05g\na+Io3kHN1b+ZktfgKTYaO80aa+Ua/MEQA5cVosG+w0YMgP1xwlfqn7uTGrbC\ntb2+15XEocDESOw/UTkGSctTUxXmlsC7ujPAcpj0jhKXR1XEZK15OiSOVcKd\nQgVBDvAgkStT6Imr/Ftc0j+fkS3XbgIBBhz9f7tmH7iVNwfL/j8aV02O7TO7\nAIf22TxEWe0onbIyX/X2LVfGzrqxvqLnUSjvbUxuV4WVz91U6kTAj5hrP5ju\nrolziqispqAPOqDlX7pTKVhNww167XwZM36x+64iKlqi0pvCXvi+riwHT8ul\noH0tL7hFSgtCFQIIAUrFrokGgi3myrecQS38EXEfdCbwRJN2/2jW6GWGjr2x\n2OxkJqGWxE4mmqmCuv+p9R6gpH648qc/fSRebA3gY3FhYVBefi1OpsUK6XNS\n53GQ6qjk7ZlZKqu5GbdUmqOcy7NTHYYPTUVo7B3NjTllKANFRc4vWVlcNkF3\n1PyNdBUWs1DFtTt+xzrnXSTsr1nOgxfkSSFf6dT5nEB2p0xl2CdWqoQhjT82\n3ckZ\r\n=5zIK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmNLR0l8vynmvKhqeavqv+FwrcUP+/1k7bvsP99tJbwwIgMPKvwmexmY6FzOSTXbjbjCpxjlSca4N6RPgTrOcgA/E="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.10.4_1593522814907_0.10468451472742224"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.12.1": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.12.1", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.12.1", "dist": {"shasum": "687c5fddd797e1f59ba9285f9f5e1d002402026d", "integrity": "sha512-8VyNGS2EfjNLzTui7AKBl03I2i91pXa8qlN06ROCE/SSWX5nyVlQ7c17Rg6EZ+GIi3PSOLN14ZMYZPmnUWX5+w==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.12.1.tgz", "fileCount": 4, "unpackedSize": 3073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNA/CRA9TVsSAnZWagAAeHcP/Ay9uj9gVgJVQA3cs7WW\nGwDD13Isr6j5/RJFw0xSPkPBaeqny8GmoUuuDQLdHZ7EV8EG7TaBXnPca/jJ\nmdaoG1cj3clpD5lcY8K1UhlJLHflkWMmvB8Niu6zN7EP0ULZfXSvRgcHhT80\nOouhjHKSBOPBz7woOBxHUKdYuLNoabUrkiWi86Tggf7k7MWkWhTKtp2+rzfa\nuVVH92JzuUcwv4F9C5xoSd5CrkcKHVgsrwRi+iByIzebPoK1RvzliZeiGz42\nqXP4lvzTEu/crSgvLrBeBi4+glTae0sS3KMRoFmX5sFLpkvHVdmiUyZR7E9O\n7DPhEcRGEs2PBPDY3cfFbErqU4qeIVPpBFNKTSGfFqdrxBeoDXpqtN3+/4HV\nT7BUXHX/ZbM5RxPdc8Fqr97SluVlwW3nzVOJwdK3vFX+HqeVor22D3xBMVky\nFqGYP8DanQ4CLNSYYYevzTtiBAjtyR+5eXK4hvd1/ZRr0ZfMXcftWCiRc1Lf\ngGqBtvW9PXZnDAjE3wIqRCMmn2gRsC5mlmvD7gknkjd3j5bxW9CS2H5CES29\n29T3U/mZ5yYJEOybY11i6nNYU1zlSMhTDKwqGQD18GT6DXXU3iSt0rbtpZTy\nh3ONlGIDfxDzIOJLjub8qKT7EfYqgW4tZsn4iNd+wLbyb6Gvrwajck4yKVK+\nmKIg\r\n=cuGX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICDNB58PRRfMLtqGDybX5g3mrpX/EKYwIpGjyvq0VFaIAiBnH917UZwfU0+j0kqAZYiDHPFUWjKgkHfBwuxhJdJG9Q=="}]}, "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.12.1_1602801726561_0.6725754416917458"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.12.13": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.12.13", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.13", "@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.12.13", "dist": {"shasum": "1a4d93464a17bffa95e4255105744ce495ce0213", "integrity": "sha512-q4iFf4XPoI66MZ5ZV4pf2A/dDBqpL96pLvFG6LEGzMAMBfw9+OD4gaEgDKgicPUFUySrqfOey8yU33wlgUrVgg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.12.13.tgz", "fileCount": 4, "unpackedSize": 3164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhuCRA9TVsSAnZWagAABM0P/A1xHyG4VYkOXeXcLSyD\n9rzM1hT5evksR4HHk3+4AGTnHYf6MNREHfK1610cyswlKjot65dy5qW+bmVp\nKA/YQJXMDKXj3Di5fzDIRcCa8EisguwqyBfXvygZkUj0JQ3j0CEcLmCOaRtY\ncDMdzcoMLWRcUiRpltpsKbmZOEGFVkT/gF80yVkQQ8WLYmVKgw0PcS8j1kcv\nBqnSWamKUaX/ZvTDmKWWG7nJhxCSoPZt6ehP2mLnSbMYtgK5mrOXTqiyj4xV\nd0vUOwo/arQiFViWtVtZrSQOtGeGE4hVOBvHVsiHBHUZl5mx3Sn3rDMoGRv6\nAWiPdppPjGcDiY+3EtNSb1QI0UkZLQMGJ7Yp0agnWY/XW8raXjVJO/iv76ei\n1E04ACfdmjcCGgFu49nYI9jwcNtKxvPWcIFUaboBHH70AqmBipAcqcUUhFju\nAqQrmzofVMjARd65Xuh2Q9vjtp8WrCl2ivHmup+tbtqeBG2PFybYZIj0shSI\nPd8lKTzOAKpltiSp1NQZ7t6q2ElXUNgUwvnnvCV8NjKHPSE7DYpqn/aSxVfR\nFxMYx+PuHhW9EU4lGi9eHO84OoSnPXCD4Uc9AlzpOa3jinqXWqne2PCL/Hek\npAbbUg3BubxExiM1em2LpGIJaQYKnxZ7euT7E+U0c7xnkMMMHDqOHVPu6gZN\nOPzT\r\n=/zZe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCTnV2h9pW6dMbSKKSEH9RWBNJoMn/pinoULQ7YQiFjAIgFCRCZFEUKfvjSpw32j64VXKhsCJLMHElW3INO/jqyOc="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.12.13_1612314734201_0.8807175194245023"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.13.0": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.13.0", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.13.0", "@babel/helper-plugin-utils": "^7.13.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.13.0", "dist": {"shasum": "b773145853db6ed65dc1a34aff8cde8e889988f2", "integrity": "sha512-QRxF5YgayMvdSGTVHzNCCEz1FNXUP4twUGenKTsZNKOVs9Y0AU6/LfQAyoGmyKpEFi6mGcBKORI4BRn+Nph+ew==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.13.0.tgz", "fileCount": 4, "unpackedSize": 3169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDU3CRA9TVsSAnZWagAAFooP/3lB4jYv/o0F2OD//NzT\nI0COuUHqmugHFXRXw5ynTp4lDa//g6vIbJZBx9UB7hOJtBx8wffprzz51U21\n9TtSuJ1dm0q6iERA/ZgCZwSSPi7hVg520/sMLi2w9ypXmGzY6dGB0lwtm7JS\n093xFztARN6R6D2OTb8SkCGh8A9dXPqJnoDIk2BR92Eqq2NuvYlTeQ6TqEZD\nZnUUfkJn/2iG5/UR8Y9HRxIU//l5sa6Ou4An2CGi+lPlsvFg+Gl95gJSjdzx\nLpKXn+6JDHkRQLcLtTYrgDDnniEwzyGdlCNFiLzqn37QuLKtDNydSssrlSBr\n9xE6fDTWfTKTUmH+R+vpvMoKKfDCMm262ZmeegiiwvgTKx77Xkjb6hNexPcz\nsxID3fL4wYBi5tixDRFk1w/HtknW1EZHRy2pMMZqMqy5iOEPaAudccuzCeD2\npNZB1LCohGXEX2GQGwlb3/34Szw2Hxat5GuIqDe9GO+p0hLZmf9Iv2MyV78R\n40hR+6wkyRHl476ix2SsPMr2qWYCdWH5iY1V3E0AOECtwHdBL90J6rr+lj/0\n5Put64YhphRkzZAlCUCDeX+wZGyGhcM6VnQvH9ykLxQDzAOvOdEQ48Yg3Pvx\n//AJv9Taqz/pdapsw8o6SaRQ/eJ9d5Okof+YFhzoPo9qW1SsAlz7effvD18H\ns4YO\r\n=OLlE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHDXDhB1Yfoc7FPqWT3+REQfVrT7mLI9kTebIVgODEJQIgGQH7/c+9Knq+UfAyeDSxYE9mzFXKkC+aLvU2cvKxkyI="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.13.0_1614034230910_0.38921215217837335"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.14.0": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.14.0", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-create-class-features-plugin": "^7.14.0", "@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-private-property-in-object": "^7.14.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.0", "@babel/helper-plugin-test-runner": "7.13.10"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.14.0", "dist": {"shasum": "b1a1f2030586b9d3489cc26179d2eb5883277636", "integrity": "sha512-59ANdmEwwRUkLjB7CRtwJxxwtjESw+X2IePItA+RGQh+oy5RmpCh/EvVVvh5XQc3yxsm5gtv0+i9oBZhaDNVTg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.14.0.tgz", "fileCount": 4, "unpackedSize": 7208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKdCRA9TVsSAnZWagAAVpIP/27zr/tjYN4UZgevhisL\nrupymQYBdO3qH2VXzpj9f6+8GqnjJg57QEW3mgxoMOkGxbR9El3S79Ia9KRh\nB87DSXMOOHsr/fb8uej8QSBNLJZF8EfFSeRY5b48UUkZtbDHLhTyVS12OJN1\nhrg52eDiCEe7k0t0LoYIp3dkwryAJw+Obv0ak8v72qPXKxo9CqpOfIhfk6Ah\nFr0/0NzsKzvB9v6sf3sfphKGpofrb9V0XhhRpSbJfzfnS9YWCDdTUmAW2bhV\nRYxjOf2a68Uo8w8CCzK5kMKejRBCdEY0dwnHKai8alHGiTDyGG4ji9IaWNTg\nRXHGqy/rjegcuXfCgQfp+aGl1fEvWF7fbxOMa82qjg/ahPawOPz5CElxuX3/\n3QiLVm6ngQXfxSUoGhWdtyOHmibxzw/ezciTsW66PLX8Ceo7PdJbh/cd/MQm\njt9RwyMlqhQRe3GjL9+PsPeGN83/rddCUPulsp7f+T0t2lUPgJNzhwCCBXlj\nOqG8bE3zBff/W9iVH0lwGvmpXQtQP7leAmKpE5PVgPxfytTCV+lWbmCKawAc\nOb68RZ9eHIkoDSjiwxvNrMsNTMkhl3ploDQcqQCsNrOwS2RaV6nHQxjGMAUB\nEl+RKE4lQW0DoZb/FDT/l44ePa29ioFnuT45pL2sEFUpbQpF+npRBrbqQPPf\n2yjV\r\n=Aojv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHWRsVzeQEbYZs/Z6KsCKGimUsIg5V8uBtFCYxpgDBJmAiAq6YPZpdYgLhy2jeWM9dRRGgCoGjssZgeEqJ8FZJRFdw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.14.0_1619727005166_0.9573111233592566"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.14.5": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.14.5", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-private-property-in-object@7.14.5", "dist": {"shasum": "9f65a4d0493a940b4c01f8aa9d3f1894a587f636", "integrity": "sha512-62EyfyA3WA0mZiF2e2IV9mc9Ghwxcg8YTu8BS4Wss4Y3PY725OmS9M0qLORbJwLqFtGh+jiE4wAmocK2CTUK2Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.14.5.tgz", "fileCount": 4, "unpackedSize": 7160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsOCRA9TVsSAnZWagAA8N4P/3utTAXTiIfR+PcP7ZsB\nLUeac6SDP691LVPIr8X6unDQzNEBggZ9ssI6CQc/1mneopAlVUNBaX4R1yqz\nZ2YRfcy0mN2mVcxT+5uxxXsiOgYojBK2ITnBMpXfyoAE+rMX1lAmgczXI8MZ\n6/jjkDfW79fxtQ/Eog9mBmc/UPgt2Y+t/s0o7Sz0TS202QxiBlkz50E+UmE8\nVCrgZLFa6PjQzKtjOye6/uu8SXR5Yz18VOr0iOYf3hojovWSMgf+c6Zb8e19\njAIo70XmYYgO2boPgn1AMRxo5Qe8g7ST4wpg9kxKGhl3QW9CxjpyXYe8O+IJ\nROWCiBDeeDMmDwRGnLL1vrjdKB0FyfBi4gFrOlGAYmWAzVyfo2MS+sYlKGOZ\nLy1sIQhTmYwnEW56qkprFOUYI6Cy/0JbSBV5g0bVlLRx3SEkSZLQaEPVKcRW\nyAOKr067ZVcnvUTLQ0HsdDamtzy+BpgatpHsU8SmXNUI/xuxr7w1Rg9ssNKw\nuhotDRklshtutvfwcD7Qt7KuPfNv0O1QECLBF7s5BGyOt5z3ojNGgErgUf4G\nAqqj+TNNpijO9D4EjNWYy6mFspgXKw3UwHrRHhHzsLWLWbmKiyJcc7ID4k/+\nNL/JGow3M4bNRKh5vpF/0Ib1D2X1lUvFFhh8CdUN6k9o5lok89xnN3pbR1B2\n95HY\r\n=Ra/Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBVihr85vqKEQTdd+hvZCu8VBTyQIiT/ER5jTJ1nr90hAiEAhyO1MZ/+sXhcBijkyXtaBgXBjhyKn1uJG+cXqeDH6dM="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.14.5_1623280398124_0.12668850062492476"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.15.4": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.15.4", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-create-class-features-plugin": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-private-property-in-object@7.15.4", "dist": {"shasum": "55c5e3b4d0261fd44fe637e3f624cfb0f484e3e5", "integrity": "sha512-X0UTixkLf0PCCffxgu5/1RQyGGbgZuKoI+vXP4iSbJSYwPb7hu06omsFGBvQ9lJEvwgrxHdS8B5nbfcd8GyUNA==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.15.4.tgz", "fileCount": 4, "unpackedSize": 7160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSwCRA9TVsSAnZWagAACYEP/33VlXa6plLx/rZZZf2w\nEIq+Dh5iQpfnwu6sUuKHQP1qf2S2Bfm75vapz8+uCQSGpmiF54xEMK1l0uYy\nLT/ypOJcr8LkDtUgKROVHEIYpPd3nRsL4hFTvayFx1aO/aZ5xBp5AngDqgnB\n+ID5lQbzMOyxfMh1MCp318B3bH0tq3yfXZ2oX9wnlytRTc8o1Q59zQO4r7Hw\nRetBVvKf5KNiiNIYYiXEpEKOHYXIXSylrSt0oIRpg1WGzyPhtYw7ci521EYe\n9NA66j52yhXw+TWyLO68KAX+xE6snpyp6Veey2QbacdLgkJhxqD9kR0bndud\nJdwz2lsgSiYYV9D0DtSwaysWbY0L7HXr80gPNyRs4O8HN+klAOnbA0BuBD+9\nnO8GAjpP+XFOuWWGiZRCsIFZrCtE/XFFYQxnx84g7MGelhGQrhuxNSe0ES6L\nisKVy5zws812RvUXLEP/2dAkpafTSEXgEunDa32bAlZI6vOrYD7laACzowM9\nyaGH+nlUUd6Gl2FL9K0Hrw2kg/Y+cevXe6AOHid7YFqhJGk5oGA/fDyMzw1Q\nSBtym3V6KV82EFyQI/49XMqgBEsUsKPl0F6HcohxbmLjWKoFZDOrYPgirfUO\nQcZLEm9OXZo3Kvtiak5en187hGeTSTH5ro/1caPbGis8DwcqGrhioPdL5NAZ\nYhpW\r\n=fgAo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFzcZfUBBJw68PZtfyKUSXHLJ3s0ThlfImoR0aTBYsUsAiEAzKqUiDq+iBF8ikdmvgRFeEizsvRNHElW/d9f0iuRT9A="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.15.4_1630618800867_0.865185733342358"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.16.0": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.16.0", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.16.0", "@babel/helper-create-class-features-plugin": "^7.16.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-private-property-in-object@7.16.0", "dist": {"shasum": "69e935b2c5c79d2488112d886f0c4e2790fee76f", "integrity": "sha512-3jQUr/HBbMVZmi72LpjQwlZ55i1queL8KcDTQEkAHihttJnAPrcvG9ZNXIfsd2ugpizZo595egYV6xy+pv4Ofw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.0.tgz", "fileCount": 4, "unpackedSize": 7170, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEeEA/aBlKORUmr848PxS4WiCmVkbTEacm1XW/wmd1zAIgI30g/+1N7W4VzsXZSixAsybUkN7GSk+mwcq0R1uSHXk="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.16.0_1635551279034_0.6230209230926398"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.16.5": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.16.5", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.16.0", "@babel/helper-create-class-features-plugin": "^7.16.5", "@babel/helper-plugin-utils": "^7.16.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-private-property-in-object@7.16.5", "dist": {"shasum": "a42d4b56005db3d405b12841309dbca647e7a21b", "integrity": "sha512-+YGh5Wbw0NH3y/E5YMu6ci5qTDmAEVNoZ3I54aB6nVEOZ5BQ7QJlwKq5pYVucQilMByGn/bvX0af+uNaPRCabA==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.5.tgz", "fileCount": 4, "unpackedSize": 7162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8ktCRA9TVsSAnZWagAANaYQAJdlfLVzMjqtQQSbRXXE\nBTmO4Bfiqb6ITKZuK9ZgCJl/lnOuJ7BZJvrtk88aQKueGLrPhjxDPl8bnOHs\nw/2KGoB218jzEuehSE2dltKBtKvgKxW3fwTpgN1Mh1c9Oy5XMX1OCIPp0uXr\ntX22gVUVH9iJVtmZ6CHvaj7yr+aQo90QxKbKVwJlPkmugfCguxHWKRBpqv65\nZcMRuv5bHA5n4HQP4X4XQ/fBKNGspHv2IGsZCrMPGIkfa0n0lDD2Yxhu2Ye2\nVo2SGqRWqptk2Mx1srnQdq9B/EujYnY+rImfJ5Hu3TMpxEghq/3JtRBqRqAJ\npTaIrBm7ct3jKd8o4WpFNMBMYAfQSQUGHx5cr3p3/QoaDJxJp1b/9wpbzRL/\nK2ug28CuXai88cqg7MNvPUO1iFjN+mdRtqYMZwS/7T5C89j/TCDHwALaIolk\njmhQQPBEUZM/X5P8fneJG05nodViNflFubq1JV34SDC5tug0ODwTKvPQURBf\nmPnzW+HmNgQ0oUfWBE46uKMDUh35Ey/uqCXbAvtgwFTXtcH72qL4s7SgNyxb\nVzRBCJpE2AsbdWozzhOxYzD1zdTzkKfn/oz4K/GkPqbJMfhVgPZBb05GJbxT\n6oXBMjGvxHW6V6beY6iekL1jOy84r8d/hYwvTPhVxp8FvFH8l0aVpCIiZ/x2\nqck/\r\n=desu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCssavXv7fEPll/xXw/tpHyNSolNVktgbefDZZbQEoHvgIhALZm+xKV9apATCBpg4FqLS47fT5G5sZBqbNEoCUWNRWw"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.16.5_1639434541822_0.12727952121933916"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.16.7": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.16.7", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-private-property-in-object@7.16.7", "dist": {"shasum": "b0b8cef543c2c3d57e59e2c611994861d46a3fce", "integrity": "sha512-rMQkjcOFbm+ufe3bTZLyOfsOUOxyvLXZJCTARhJr+8UMSoZmqTe1K1BgkFcrW37rAchWg57yI69ORxiWvUINuQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.7.tgz", "fileCount": 4, "unpackedSize": 7162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1yCRA9TVsSAnZWagAAkQ4P/3nsfDh9U3kxUGBZ3eB9\nd5iTqdxlweLx2g6wCaEkyI6xAB7TAbNKNPvaD7ygPq9n4hCpdEcvNFRgSKk/\nk5hB+u8B6yJGOC616a0ABk32VNFnt+xnjhrw0soDPqag34SfY/jzxiHC4JS8\ncDyJaPgIwrugI5OFf2ZuZRwsTKUcn5zepcSYYCrjux1zkHDYsxCxGep24QlL\nLH1GStEp5dX4ujtZe0ALSmTPtqXEgfiawilK7hWhh4ZGmeHJBkhYAbgNCOHY\nfcauPQIATVX7lAAiVlszzG5K/hTNQjk8hsadOBtYk05qHxIzb9C9cHxgqNCV\n2EA6gdrpjNxEiTD/yPqPR/VDkDqWnhsZnlcpbTHCfGC7k1paQC30Hw2fgBhu\n6WPxQ4/Zk6/+pDVJTaAXUnkZEA3n681j6yHWpHcYIXaGWEvpf3OoMubGlPvB\new6st/78RvyoGQzwu20AlrSTBo/wCZKcGIjQQ/GWnlmazVr08Z9dqlaj66Lj\noVQTzTYG2rp2mIH4OXFJTeYUrom7rUiMkEV9YcDh6e5Rn5LZvXppdx4SrMOd\nzQ9j9s82EkiXdMkAQh+RTTP3dRU8XSqzUe+vKLdPk4oyssBzcvUUKQ3QT8f6\nBjzkcyLnXnDqx3ipQi4eY6e0TDBec38KXLtiU6jr1Q9Zkdip4riaT7N/QDBW\n+0Yp\r\n=rH0h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBup6ghQ4FiJuJPM9Xr0YtOGRhn6TrLTNFrIrU3Pq6b6AiEAsnBGtPY+dBNculIjmGUuAVB9DbxEQBBqnYWjv4JZ52c="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.16.7_1640910194072_0.10577583986214911"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.17.12": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.17.12", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-create-class-features-plugin": "^7.17.12", "@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-private-property-in-object@7.17.12", "dist": {"shasum": "b02efb7f106d544667d91ae97405a9fd8c93952d", "integrity": "sha512-/6BtVi57CJfrtDNKfK5b66ydK2J5pXUKBKSPD2G1whamMuEnZWgoOIfO8Vf9F/DoD4izBLD/Au4NMQfruzzykg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.17.12.tgz", "fileCount": 4, "unpackedSize": 7201, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDve+kUsEHOaWXIHz3QoRy8h5k47PecLQ1NKoiY5Lg8oAIgQInl4Wiav6MwP+eEpvlkD2HtqomXmsUZ1KA9r7b56kU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSLw//Ra5RPaEtUrLHW9FdwlhvvwxmoFKu3RRTtdwTBsn33+fib9j5\r\n+jOcywomRyzmisA2zW27WqwjBk8U9fab9OfOg7Ttco84RPH0t39H6+9WJpMv\r\nFfXejJemZDyTH4gPyabltAedM02Shk3dEmAGyDCwtdTc565kTR3WOKwtvQ5i\r\n7v9Ndbt0U4duFSqcOsEvR1ZQwkHwESx0V8gW+wcfgT1yDQeuAKUu3cbgeFKv\r\nLq/e6WiJxdYvMuTrFfb4CvPeG3mJCUURt0PLt1+2L5VKKhH3GjkrbuCt+Pw+\r\np6a/AiJcp0vmDiDd/tvUbMaZ4WSeMBZNYoofbXp6qrRaty9GFFzlpXmoVWIW\r\njUGGIqvVaTTxJITkoxwgkoNHaTF6Zx6diclCgX8U1rnJrrhBF/g8qV7NQFR3\r\naLCtuhM/FbRAGbpbsJskLB4LYIzNH3qFYLWFwtbRo2xUm2eHW64Yb9towg8W\r\n7Se/t2migfG/pav3vPGrQWOQVxgvUhIAbv6cAo063h/R++qAVCzoLbVP2LhD\r\n28K8CZJkzKD88dhg2uYWBeBFk2rvGcyKkPLS3/3wSmjlaUh33rBvTobROaPL\r\ndg3j+0Axl6JsvMiku/134aaRgJYXxsNhNEZ7xfLlT0FvMCmVOCamm4JlKsK+\r\n8h76KZYnpPgIG/kzdtpV5/zIr5MSTG5/ePg=\r\n=Ypcu\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.17.12_1652729555572_0.8980173890190011"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.18.6": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.18.6", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-proposal-private-property-in-object@7.18.6", "dist": {"shasum": "a64137b232f0aca3733a67eb1a144c192389c503", "integrity": "sha512-9Rysx7FOctvT5ouj5JODjAFAkgGoudQuLPamZb0v1TGLpapdNaftzifU8NTWQm0IRjqoYypdrSmyWgkocDQ8Dw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.18.6.tgz", "fileCount": 4, "unpackedSize": 7244, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBiyRiCqlXIpxtcvfWz6WyvQFVsWrynI6Tv4HaeV+YjLAiAWQOQCgmPoD3HaQCcDSmUZxWpTw3a64ODtP+AjDTRDBQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmom1w//QTXPA/51UvwNizXPyZ3611nbav8gBr7LSRb1xxFxBfGJfBUV\r\nXgAX5VyoI5Vu1fmOya90TfFyiX/twqodyDvD/kWgW1ikC5TGtX3BhVfS5QKa\r\nnCRWklcdXPQ9XcUfaFC7gxkdj8cq+lSdtVz6pbZ/tbXVOmk0oWBaN6rrG9iu\r\ngdnIwrgrsv0Gavh20eHSTMDkDaHbFWVFlsUsBQ5EINJ8C095OnotNts9CHn2\r\n6IW35CE4Jd77tGr2b2Bog8ZOzplhXj+7P9+3NPhhan2bixGjJXewIxkukmew\r\nTeyGvif2MYonj7CSJ8DvZXsA989hDL7fKxmaFgeJn1LowBFZz3kVUmu+ysMb\r\nudmxy6bY9URsmkN+dMTnKDvJLCrfgHHHG88tBU8jnP0rIUl8vmVTmdgHAwSj\r\nf14qzrsBU4i5N+PYQK1hHdq6sGt/n8LQKPzsB8ypgIONENg013eZZPxQjip2\r\nQBsHqyazMo60ivRRZp7hnnuT8XDY3RXsXP8GxmYgaG+SzlecaYbIex+zZffT\r\n2ZVn+i5JMKi/0q4CTui7GA/Jey51mdoAn/lq4Gv3EXt5I3hqUgNriPjwDVif\r\nHxKitmBLdmfar2Hs2DjpP6EAtOneYTWjbYsssne/Acu4aR24WIRVs9D/hV97\r\nSb0LRL7e+jPwJGsiattBqDbLxnHEWR2xUCU=\r\n=1j5s\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.18.6_1656359448358_0.08191699078355574"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.20.5": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.20.5", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.20.5", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-proposal-private-property-in-object@7.20.5", "dist": {"shasum": "309c7668f2263f1c711aa399b5a9a6291eef6135", "integrity": "sha512-Vq7b9dUA12ByzB4EjQTPo25sFhY+08pQDBSZRtUAkj7lb7jahaHR5igera16QZ+3my1nYR4dKsNdYj5IjPHilQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.20.5.tgz", "fileCount": 5, "unpackedSize": 19550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNHNdLRa9SZ3tP2cwSNmFt0fsgxgPQrOxYRSgsQ1ea2AIhAJsakFUeoF6HRnXNawHCVrXLesVHKmzUjAafELvSrbim"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIXA/+PePGXcMkQiKtEmicGwTHa2YzeRjf4V9JMG6oGYjtrqFyoW6M\r\nEoGL2tfm8Ft6HjSnUXzZm67gv9wFc86/mVYs9VwVE3RNuQfRT3fErzW+qgxv\r\nSdsGQtBbfMnSYTAgfSeo5Hy8OgGfSDJOZR9mSpYA1YJ9kAqwxqECn5cX7FWh\r\nl6IFcJKaT8Z98YTolNQLQ3h/dp1vv2ce1WsqbKju9nFHkwN4SmcLkDf9s5j3\r\nvobMl9jj0YWlX9KU8IN9uADB1W0bGUUgUnaxKU1wte2v9vkbmsN/2Na7GNiH\r\nzG4PgC6ENqwrbE6uM/0bdEwVarGyWeOlwos4/WdOjj/GAhWZNw0e1WUUGvrg\r\nnPYekdPVVG6VKlLoX7pmMi3PRp6D/R/HxbEAE98NF1nBCV3DcC+Rp/0Wu0FV\r\nylBzRq08PbvEW2CDqSDfjkkG9jl3yl4T76w8v87mEBEmk7K2TPWUk54o9CHZ\r\ndPo7HTCGOeGVaHS+78rOo5Pb3VaDkSTwJY8YtOBlbuTGC9eUPgiMFryotK1w\r\n0ZJ+tMcaK9VIM84DPhRVrWul16W+fEBnqiTO9tysuxaZGZBkBjHLxHKT+Z2K\r\n9vMb4qiQ9syxyQWG3ggd+ADUYuUpraILVyq67SROZ2xaQRbGp738aQ/nnR1X\r\nYX9nfM4KS4uyBwHnL1hjwMS6Hq8v+8nZtyg=\r\n=0HoP\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.20.5_1669630367424_0.315676641379089"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.0": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.0", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.0", "dist": {"shasum": "19496bd9883dd83c23c7d7fc45dcd9ad02dfa1dc", "integrity": "sha512-ha4<PERSON><PERSON>hbJjc5MmXBlHec1igel5TJXXLDDRbuJ4+XT2TJcyD9/V1919BA8gMvsdHcNMBy4WBUBiRb3nw/EQUtBw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0.tgz", "fileCount": 5, "unpackedSize": 19536, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE05l1HcfIyszhAzbalLDcb3e4m7dDcioZpM0y4guOA8AiAcPfsb1PAEchB9PacbbefiCYcF70H7P3M5jBu/K3j0XQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ1BAAoiUYtb+L7gEAzX+MvUR+U8C9yWAyOPQAkTL/ldE1k693GdKd\r\n3rINjNkYqJi08h7v+LyULS5bHOPNyWJRcZzwIgL7fHrzqz0BldpNA5EraU6k\r\njBH9Df7/XTgcIPxbTa1BlgVjbr/x/VKV6QZBha0PKdnZcFNnw5ApYeWjvwga\r\n2ff+8E55Rp37k4xjd3oBDbBiOGvLKVQhouaPC1lwoy9be5yVBhYznRE0DLry\r\nnnRYJo2E8dfnHuIJig5PWBlbTsBF2yk/Yd3IF9ynYoVnJp1Juij0aSNXuRAJ\r\ntokK0GHAM15fE8JIiuvGbAtbv5o6K/jU7GOmqHuoIbMD3MkDLRWlfVusPbVM\r\ny3/wnSuiO2CvRWCAFnP8GXDdrt/BaZ+18mr/QaJKrrkK/0M/LiEtpuHRPX0e\r\nGSJgglxNNooLjWQ10mwsSDAmqd+kTczFMmM0XeFiLKLRUw+/Y+947x9uMW/6\r\nLnfGIFxURtmM4o33MLeP4VjKl6sgndpT8IsAGobhiHciOwpDmkggRtKLO4DI\r\nmLKBNf5NgE2ULe0V83bjphdmxWpRrG0bHY2sU6/IhzKtJS0SQKT1Pr62OByv\r\n8ilP4CFYzHNVDu+AN3goZx2Kaw4rkcIKr0/abRnPluPcClyd9PLECpJ6sdxG\r\nmXZ22ULbYSRn3ZvTj7+RwXRw9AFCVprwJ58=\r\n=VFxt\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.0_1676907079262_0.05736574963736851"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.4-esm": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.4-esm", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.21.4-esm", "@babel/helper-create-class-features-plugin": "^7.21.4-esm", "@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.4-esm", "dist": {"shasum": "4a85da4ea74d5128676e113407199274fe19901f", "integrity": "sha512-t0WwXC8Bl95x5M9f5A4XV2E8g81hkZlzPLogcoZCKeb+BsIuSfS3TIpTJZ8bMl1R0vrvTZrHs36GkJPPqp5D0Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.4-esm.tgz", "fileCount": 6, "unpackedSize": 19945, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqhQDs6r+xFGZlWd9UQxcN5kWs1flzJsXECx7vkd1P8AiEAtpEcSfyF9ZSYaKsiAw/Qpd63dtWtJqvjhPH0SiwJUVg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHew/7BOPQDY/8tChKaP+xk4FxSV3Huv8tbKnpQ50ahYRW9rNELjV1\r\nvX8lAnvDVNNYh5ehTYGtypAf/1Zr2NKGMhdFEJIz4CszJAcVpXEwx3c7Hb99\r\nj0kdW4tZ3DZQBn9q1Mxo51m0I5iB6B9HXR/q51kFZHbeEHU3h7P/boSc5Bec\r\ntv4YJoqtgmnz3r1xjmShlfT85NRto59L8ZQGu3Uv/sDAiVLxSmdPVVL3zGSz\r\nwI3kFj+FZvOpprm2mtzBioSxX49yW3Boa9UA3Y5/irFfS2HvKGiNO9R2dOkY\r\njFy+0bfFHnA5yGCjKAJ9Uj5hNXo1CsEhm+n8IJvOR2z7P9FW920h63YhRbGX\r\nAbtud8v/5C/SW8p8zcDU30mUy3heAmT2C3Ee/MM4myjgL4U6KIsHeTBVyllC\r\nGCiD2iRmN/yKaEnAcwN4/3ESzts/VM/rClI1e09rGI/vtPEcxKqj5relCoSj\r\ni56o5ecCRzI2qS1US9ZCr2ORMPKVdvNsPl3/XZmL6h40ezkqToUTMRvNOGWf\r\nUMxW8WZJMNpmLNp8vjOcP67926crGoc+aL9wrWtN2dH4NxGDmaC6h0n/gher\r\nWj4hBsA+H8RnA5jfBtXZdBaPTCsz28faMT2zINYEQ9Z6IXHoEp6Uwj1va1tG\r\nl9Hs7GgBMs49rsP8lOD6mTrFZmPti87E4OE=\r\n=qJa1\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.4-esm_1680617401689_0.788514462044261"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.4-esm.1": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.4-esm.1", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.21.4-esm.1", "@babel/helper-create-class-features-plugin": "^7.21.4-esm.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.4-esm.1", "dist": {"shasum": "77e3ca9686b624e2866cbc171c67509e585c05e5", "integrity": "sha512-TQz1RkwbFuXIUBg3dLD6JtRCKVpTEk0e9TMlzB22MWl/4xpH/Pb0/h85vrQ7C54HbDgDK5FCFLJeSBqd2i7/hg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.4-esm.1.tgz", "fileCount": 6, "unpackedSize": 19355, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIElQjdTp6sojZjzeCHhCVquPznqL9bpMb/nen13kG1auAiAuF1nSHEbwz3igaLojiyNem59Q+Mks0UWrrDQX9UbO3A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxNA/7BhVHoikZTaz2QTxhACKtTynzGSJq+7hVB11tir24fee4ZsPY\r\nZ3vp8ilBX5DHrkSdoOHj7nliol9rhhm7ScYfmWY2YM7BG2Q1cefaBH8Z1SJZ\r\n7NoLxMq2+/J/JtFF4S/E1rgRonNcPcUgz0fdAxZ/9Tu/OMYq7oOaoFgaR7z8\r\nYCoVr7cd6p7qwDEEP02sdP0aBYUTiaN7VOh327GHqOdjExgAqg4SNuq6L9iZ\r\noa1u8pM5oKozY8vf1n10sIQ810xXNhpKLybsc70iMF99tlw3cjUjwWQ7+aOa\r\n98Scz5O1PljCQHe1oQWIq3vVQE6HNOUDoYqzQo+PmiyYVuUicsndpRt9gOEf\r\nmkcZFUhSkKDEjx3vM/r2N+Xd8Ufou9UOSiCGJYBfKOVhmqX8IfM5du71AaAA\r\n3q0tFYRaPPELK3IErvzFYf1KV9Lv1v3QqjDvHlb9vqXHSNZxOIscufOUtbkn\r\nTPKsTsB+KvtjFq9ps9ND3wgJQaQz8KIlzHFeufUaubvasT/RZijRbXcMl7SV\r\n0dObBb9+hdWrS0GWpy6Kc7Rah6nb6aOguuingAs00E2VDMu/pAcBydd9P5vl\r\nc0qhE6xAR7/XKNXMYftTyK/pHUS+3L4+EW9Z9Hoh0i4ThamFhBZfR4L1LvB5\r\naQ5f/rkzsKCeFZzc5pRkYYCWz1si5Z6bC9E=\r\n=Rzm5\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.4-esm.1_1680618119255_0.5945934939168886"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.4-esm.2": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.4-esm.2", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "7.21.4-esm.2", "@babel/helper-create-class-features-plugin": "7.21.4-esm.2", "@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.4-esm.2", "dist": {"shasum": "6b8f2b47c4989eea7bef9119f8abcf1494f8c6cb", "integrity": "sha512-rzLBP5W6r97nQrv+WIKiHDVRNVJdkaRefE3T6XKH0Mhs965Zx7iaAgeI7XLUkNbLC2THeofS2sX3kEN6lzeFtw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.4-esm.2.tgz", "fileCount": 5, "unpackedSize": 19331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG4PCvEf5Oe3exgbLYZuqm+7RbV5u051cjIJfdvN/bnBAiBDqPolYDl6Uxwvoby6B6tY0g3Epb/H2eV+DzHOHv7E1Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLWQ//aMLjPKiLVykWbjcaF2nSXSsRmSWAzLg9jsISsYLqJF+RL70w\r\n/lGnPSRVofNh0v9i6Z/nJ4SapgfzliDCcwTQiyTYLPcGYbIplB2UbZjvXm0t\r\n43ZineSsn6YP5mWeZ2MHyRrBG9zo9YdQiIIB2Y0QHcawuodyuJEX/+pnLo0K\r\nlRg1Uk/+Cvb4gy2dWJgLycKxvbBdbv1hyJ145DMafyUN7UkGZRzSntbdSjkC\r\nWVXeQ08dhsKwhxvXrywsq44HB5TtWaijHkjLBch8Tkh9hoUVKHk2/craBAMx\r\nejpVJTM88n953OS0wlk2rRpcH9dDsmw30eg6urHYPJaT+6of5X6Qk4T6MOBN\r\nPbAfyyaFjbywogR4U2w/p5RHmaJpWlj1RIeaTEv5MtvcyV8Z234hleVcEmkg\r\ndmUJRULyN1Gh815ekwi5eSzW/oxhEMhNeihJs5AG5ST9aH9MDnbnxb9jXhuZ\r\nMWN7Vcul/MvU2j09lNbKA9MYA0xBC34dCbH2sHuWoiScaR6GPfIhlITkr1nx\r\n4inav+i+klsoGJZQo2hB4IGX6TQQpMc+K141SW4Imx4bhAYQiIyBH11V5/EC\r\nh5eubI8oLp5f9PNlApKTKSt0vJIw5WYYWMzQpMKGHhcLocyjUV84pDKhJcGs\r\nBEaNZSfIwCywciuft0tSAdmlAEuyD6YvBQ0=\r\n=cFR2\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.4-esm.2_1680619204897_0.160441730147713"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.4-esm.3": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.4-esm.3", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "7.21.4-esm.3", "@babel/helper-create-class-features-plugin": "7.21.4-esm.3", "@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.4-esm.3", "dist": {"shasum": "f46b52409770ccc165d64bbe2e536103e201f74c", "integrity": "sha512-tvgYMPSRG86NhN03WdmNQGsIhYkLAISOB8OWyBzrvLATilWy2RIXR4AjdezFKWMJGYNxCZoio6Temy6+lOd2GQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.4-esm.3.tgz", "fileCount": 5, "unpackedSize": 19933, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF9Gdol/0njuNoQiCWvxC6hDYYU7Mhfb9U8pc2f4gXS7AiB0sWGr6G6kReCtKYZ/tlbA7F3psYDXUijVXqjvToAXAQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDquACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLxxAAmWcUNclKmUZ9+Dkhy+IsWmvrMe3j4l/XgguEH2bDYVWFbWYQ\r\nMwlpEMisP94sn7BGNPuCGizhG2ejf8c9/JLV0yUwIZAA6a6EaEbdZeFWzrPU\r\njtngjDZ28VHeBxBbjdcctvNHO0vmzHsuQWpdDS631JiJTtTTV9n7O6ECeyD4\r\n3OO84XascSU0pg4dnSbHHSRYBGr6i2Wc4bCaE3480Oa63oCHKHaY4ddPpHaS\r\nY51dwUpp3jIQfWZQVEIED0rpkzo/QvRvtonv8UzVluEwcGgsSIQeHsMNj3d3\r\nkHpnMkfoLUyjdYqmiafJ3Hq5btZe1cC/z5xIJJeWfjNJYQU+0TS4k8dfZi3M\r\nFUzyYDPCLjyn9wQ7g9PoNO9xB3+4W+W7NLqoVsIoo8H/pJVpS+3qpscNLbff\r\nD4jnFuyUMQXwg+ZKxbjPpRRVSYRQKvqbrt8HvlXMeYMtGqz+SCxSOvW7Xt+0\r\nmBYgVWlm9Iu4oMTJGnwecMclojCgyKcZxpVTJWXaIkwJBLQY+q8Oj13Fc14X\r\n28m3qV+bqOWy2fWkbG6EgEu1/4XZK5GCIgghOV8PG71XAUCL7F8aWd3cVkU9\r\ngXU3GAGP3PIhfVI1wAjPfNRAFSZVVrQYV7DTZ4cdYpkv8Ns1r4GaXcHun0GA\r\nU3B3GDTq4KuPCmcz1Zsz+xixgzC60WotTIM=\r\n=4Qiy\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.4-esm.3_1680620206677_0.17427783211727554"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.4-esm.4": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.4-esm.4", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "7.21.4-esm.4", "@babel/helper-create-class-features-plugin": "7.21.4-esm.4", "@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.4-esm.4", "dist": {"shasum": "1dc251cf4ababd702ad60480e7fe6807c2fa72e6", "integrity": "sha512-XOm+RZ4FUxnwLn/UXxDa/b25wJjo5hNC8fV7gDZsV7sLOc41Xs094o4kPHLNJux5s8DJSCVVqXLKRDgWrEQabA==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.4-esm.4.tgz", "fileCount": 6, "unpackedSize": 19351, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDa3PyhmX+QDLFxrQJd1T7XZ/mxv+K7T4mBs11h2fV+rwIhAJcWdqA3Ituzv9yiCaLZldwRzpvs4g7sXFBXcLPXt3tO"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXAQ/9EaA466gVvsX6WCc22s5QBoLgasfvUlv8FItawMFXa8E6HxJ/\r\nH8ieX4Hidnuh75ftjBnCwVv3uVQwwR8mxesZ35PjoJr/e1YRPAjyU3AnmOdK\r\nrzg9VysXLTS84KnCorOfrgULjNKQcWqg6D7S46DGIEc8JIMZTpnae9pOzQKX\r\nKKfXCoSRnu+D23xv6XPshP1bM2Pr6aiXia4AiWZ/LtvCB4GR1k1CGU730zwU\r\nXSzZ/PgCpJSm3FVO0d1mT1GbP2sCusuUAHzoILQtxWP4KJhKFMad/Xd8nrt+\r\n6+6PgKSs0rbAMV3JTx37OQPKRBBQQol3N432lrbDv1Jt93ZHvHEv5jIrffv0\r\nJ5mWh7k5M5VV+kS2vaiX+uO8ViZtZEjg80M29IsSwl5twcan+qzeOyNk1gUG\r\nqJV2SrKY49LCACV5jKmDaD+7Mm7PjtoB7/u66+449JRjEn9mhizVtiRy8vWm\r\nTu+c5iBNh/IOSoMJjnZPC2INELCeEHM+JVB7rWEWU6WpjWzaZzF4yx8LweVy\r\nbXnrN33llYeiSDM59/LeIlIKIwge+YDkiJFlSgrk975vM/AWAtxeeJ79cLmn\r\n/TfK3SJWeI2+KoTZ/HO2jvxZa/WwwdhwY0uCUzSMGU5plH0c5i0A0cqhHwP5\r\n1J/NsNRk61fzTVgzuZQU9EhCgLHzk2XnFzI=\r\n=4ehn\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.4-esm.4_1680621235538_0.07206781085868141"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.10": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.10", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "type": "commonjs", "gitHead": "82f65de27ac9c415598bbfbfd6fe1c7af7bc201d", "bugs": {"url": "https://github.com/babel/babel/issues"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.21.10", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-3YybmT8FN4sZFXp0kTr9Gbu90wAIhC3feNung+qcRQ1wALGoSHgOz1c+fR3ZLGZ0LXqIpYmtE6Faua6tMDarUg==", "shasum": "861ab9c7d152291c47d27838867f27c560f562c4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.10.tgz", "fileCount": 5, "unpackedSize": 21251, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCljSMJ0pdDOtx1rnizGG6dFO6/UpG8YygXc7NqA3rPvwIgIYiUsKzd5+srtLH/XgAfI6x3qcuTsn9zRhynls6q52Y="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.10_1685776409808_0.09540473745413802"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.11": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.11", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "type": "commonjs", "gitHead": "32c3c574b5d41de969fe92f2ed60bd8cfa9862e7", "bugs": {"url": "https://github.com/babel/babel/issues"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.21.11", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-0QZ8qP/3RLDVBwBFoWAwCtgcDZJVwA5LUJRZU8x2YFfKNuFq161wK3cuGrALu5yiPu+vzwTAg/sMWVNeWeNyaw==", "shasum": "69d597086b6760c4126525cfa154f34631ff272c", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.11.tgz", "fileCount": 5, "unpackedSize": 19538, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDN6PETexbetqd4NSqyAfdNa2ZHStZsFwipSR45DGCsDwIhANo9hAHpCDI1rgy0UcmzRUOlmBwGua0J1F1pwy116e5b"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.11_1685999513774_0.8980386378347169"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.0-placeholder-for-preset-env": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.0-placeholder-for-preset-env", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-plugin-proposal-private-property-in-object.git"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {}, "engines": {"node": ">=6.9.0"}, "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "type": "commonjs", "gitHead": "e3c68f6b96b99b7b1883098488440361911d28fd", "bugs": {"url": "https://github.com/babel/babel-plugin-proposal-private-property-in-object/issues"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-KFpydVGD/eLcdG/ADYybqhoi3GlznvdKhEXsw+5rvbajHALTl195FNKxOiYvmA+sKSl5aq522/8DTHy0RBkc4w==", "shasum": "0e7937b81d2ee1b96f63d90dd8fd52268959fdeb", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.tgz", "fileCount": 5, "unpackedSize": 16322, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgmS7imBgucoxDPt3H2XOsGmVlvKZAJXBJbfPVFJ4JwAIhAJsene7474AO2WXzHCLlW1izAVMNPFiLp4hNp9dcMxJ6"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.0-placeholder-for-preset-env_1686039762941_0.2588144756896473"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.0-placeholder-for-preset-env.1": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.0-placeholder-for-preset-env.1", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-plugin-proposal-private-property-in-object.git"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {}, "engines": {"node": ">=6.9.0"}, "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "type": "commonjs", "gitHead": "8c273371ca47db32b2819e1fa83a93757c859ad9", "bugs": {"url": "https://github.com/babel/babel-plugin-proposal-private-property-in-object/issues"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.1", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-eOe/e3QFUABnY0vOu/L4INsNTqWFW0U1HtqRIXvYcD+uFW4gFLa8x9QXTSlVSlulqG40sclIr+gjpcNO6FUCKg==", "shasum": "5248d4941a67975dda2a32a59fd179011e74a953", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.1.tgz", "fileCount": 5, "unpackedSize": 16337, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkjvtSK2SuvHb4SHHix5r2Rr6YmVKlb+L7QSX5w7vUHAiEAtJ0m1wQNznxPcsbHC0C3OWeyj/OhNT1x4q3yQmPXm1M="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.0-placeholder-for-preset-env.1_1686043575327_0.5597884412563545"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead."}, "7.21.0-placeholder-for-preset-env.2": {"name": "@babel/plugin-proposal-private-property-in-object", "version": "7.21.0-placeholder-for-preset-env.2", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-plugin-proposal-private-property-in-object.git"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {}, "engines": {"node": ">=6.9.0"}, "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "type": "commonjs", "gitHead": "816ae56354a79b1e420836dae9179bd3b9e9a9e4", "bugs": {"url": "https://github.com/babel/babel-plugin-proposal-private-property-in-object/issues"}, "_id": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==", "shasum": "7844f9289546efa9febac2de4cfe358a050bd703", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "fileCount": 5, "unpackedSize": 16339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIACSImkhwZludyDX16/XT3a/8HRUBG9aOhs++VouUFsXAiEA7LVYQw/aAWUaQZwlrC41CWfxMv5YbRs59zBlOZn+/OY="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-private-property-in-object_7.21.0-placeholder-for-preset-env.2_1686222840410_0.672251831862914"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-05-26T21:43:52.680Z", "7.10.0": "2020-05-26T21:43:53.069Z", "modified": "2023-09-02T10:41:15.257Z", "7.10.1": "2020-05-27T22:08:34.265Z", "7.10.4": "2020-06-30T13:13:34.989Z", "7.12.1": "2020-10-15T22:42:06.785Z", "7.12.13": "2021-02-03T01:12:14.335Z", "7.13.0": "2021-02-22T22:50:31.040Z", "7.14.0": "2021-04-29T20:10:05.333Z", "7.14.5": "2021-06-09T23:13:18.269Z", "7.15.4": "2021-09-02T21:40:00.988Z", "7.16.0": "2021-10-29T23:47:59.220Z", "7.16.5": "2021-12-13T22:29:01.981Z", "7.16.7": "2021-12-31T00:23:14.221Z", "7.17.12": "2022-05-16T19:32:35.753Z", "7.18.6": "2022-06-27T19:50:48.600Z", "7.20.5": "2022-11-28T10:12:47.618Z", "7.21.0": "2023-02-20T15:31:19.421Z", "7.21.4-esm": "2023-04-04T14:10:01.994Z", "7.21.4-esm.1": "2023-04-04T14:21:59.478Z", "7.21.4-esm.2": "2023-04-04T14:40:05.126Z", "7.21.4-esm.3": "2023-04-04T14:56:46.843Z", "7.21.4-esm.4": "2023-04-04T15:13:55.765Z", "7.21.10": "2023-06-03T07:13:29.994Z", "7.21.11": "2023-06-05T21:11:53.928Z", "7.21.0-placeholder-for-preset-env": "2023-06-06T08:22:43.127Z", "7.21.0-placeholder-for-preset-env.1": "2023-06-06T09:26:15.482Z", "7.21.0-placeholder-for-preset-env.2": "2023-06-08T11:14:00.618Z"}, "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "description": "This plugin transforms checks for a private property in an object", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel-plugin-proposal-private-property-in-object.git"}, "license": "MIT", "readme": "# @babel/plugin-proposal-private-property-in-object\n\n> ⚠️ This version of the package (`v7.21.0-placeholder-for-preset-env.1`) is not meant to\n> be imported. Use any other version of this plugin or, even better, the\n> [@babel/plugin-transform-private-property-in-object](https://babeljs.io/docs/en/babel-plugin-transform-private-property-in-object) package.\n\n> This plugin transforms checks for a private property in an object\n\nSee our website [@babel/plugin-proposal-private-property-in-object](https://babeljs.io/docs/en/babel-plugin-proposal-private-property-in-object) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-proposal-private-property-in-object\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-proposal-private-property-in-object --dev\n```\n", "readmeFilename": "README.md", "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel-plugin-proposal-private-property-in-object/issues"}}