{"_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "_rev": "12-63f9a243e3b9e43cb769c447d994d621", "name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.25.0": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "7.25.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "809af7e3339466b49c034c683964ee8afb3e2604", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-YLpb4LlYSc3sCUa35un84poXoraOiQucUTTu8X1j18JV+gNa8E0nyUf/CjZ171IRGr4jEguF+vzJU66QZhn29g==", "signatures": [{"sig": "MEUCIBF2D/hcvKlUBIyWVIxqLM/TVHfU7rGqtGuQhwV4fQDjAiEAsYLv0bFt+TotpO/HnATGfdkoHdrPoMQI7SzXJAYn/Rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68386}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-create-regexp-features-plugin": "^7.25.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_7.25.0_1722013165902_0.5880723411099735", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "fe2da9d09db65d548ced37706609f0cbc0938589", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-cY1Gx7HyTuH8TQPid4GqlbVbsSmOxhyrzxk8fSS3n5u426dsXJl+V4UqVO20zkWzBAUDbcK24yti1ZWkITZqZg==", "signatures": [{"sig": "MEUCIFrBrlZO57IFWRjovMmOEuiQ6HYeX6qgwTCcZ5fQc2qHAiEAl/cCLLV1s9dr4WvLtDYhty4krAz9JAs874XL3aVn0B0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68437}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-alpha.12_1722015225518_0.23810004596761947", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "7.25.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "102b31608dcc22c08fbca1894e104686029dc141", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-HvS6JF66xSS5rNKXLqkk7L9c/jZ/cdIVIcoPVrnl8IsVpLggTjXs8OWekbLHs/VtYDDh5WXnQyeE3PPUGm22MA==", "signatures": [{"sig": "MEQCIBM1/5AuIdoJXc4S9wYNQKQ3K76KgAetOtW4aMKtfbnCAiB+xlL/MjejN90japp/gkZEbfYHJCvJRl4raXpA/87Mrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76248}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-regexp-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_7.25.7_1727882111678_0.0974929809175975", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "7.25.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "6f7259b4de127721a08f1e5165b852fcaa696d31", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-0UfuJS0EsXbRvKnwcLjFtJy/Sxc5J5jhLHnFhy7u4zih97Hz6tJkLU+O+FMMrNZrosUPxDi6sYxJ/EA8jDiAog==", "signatures": [{"sig": "MEUCIQCwvhwOChQvQBBp37DV9zR8hPmy7g1L0PO4ogxzmke8dgIgQmlhLNWNiuurZvLvBzF0IhsQwIzSmZfdBuygQEVbMHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5338}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_7.25.9_1729610487834_0.5099309782936421", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "837e2fdd531dc2d1e7237e634c08d1b16662c8c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-TqobxYBVhxsifbgdMJNL7V8G2IV5kPUXLMg59JumlpMII0wDiVx6ymb/+VKqGNVfp+HoglwlysuSpbIWtHp1Kg==", "signatures": [{"sig": "MEUCIQDea80tyTQac+oLZVNPmlePILi8OJHFfCyslB4wMP5wtAIgfcJtTHR6npEeWbADCvP8IQaAwZR+zgZRspP7pDzCnoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5397}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-alpha.13_1729864466687_0.20156078625629914", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f6b7a4afe48afc9abef592eabcf3ad5f42ee43b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-QVgNnV4/RvFmL+JErJV626O4fIyiZ04j5YisA3Pg5kOCf+ra2EruXdU+z0+8IO//BdxASAuvKhTilq/kqsOSMA==", "signatures": [{"sig": "MEQCICJSwGXam8ogbRnKhSLoGTFPz2ujR2ynwaWH49m8KnHaAiBvUZmTWDdaW/evq2y4PftTwRRqtZQJsvMJ5CdMjh7Y7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5397}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-alpha.14_1733504056793_0.8585709407400182", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "683d0da4a817f5d720c6049806263be1156b13ef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-XgCS+xcK8AjD8X+V5toCC828Kw4ABcfu8eN5i81zfHVNxl79pUQpF2+T29sX7xDTcOR/HiHJMA38LvpITiqzlQ==", "signatures": [{"sig": "MEUCIQDkscdpJyvtAu6yrJNf/r8rLpfRdyp2EyttAl6oNRa0IQIgZVJ5dTya+yHkrrWCJmHMCa4UQCnqz1rlQCUmfA0Hz8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5397}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-alpha.15_1736529884140_0.16265090073015576", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "823a7f33e3e91739bb44f43853db8367451cedaa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-HvAJuFE8B+VXF3+1ZFumGxhC/Vr2mDVAOjMHFiibCSLMvzydpn7A8yg/AEHFfD43nf88phGch7YJgCi4XeESLA==", "signatures": [{"sig": "MEUCIHAg2yEkjG8xbS6044iUtPGjQdchdL/KI2ORG3Fyzsv+AiEAhNp9yeeS8/QF2zN6nQP2tgdurWH5uzL1Sfq+pOx9pQI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5397}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-alpha.16_1739534360107_0.9379703387433571", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "638adcbaf25fa4e3b5a2dcc0819036239250beef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-5aM7W7a212oBD8J2vdBN1vg/JTHLaaEoiiq+4+hHFDJfV5iIek25XpsFbvJYIbTFVKCfd5X4EGh8/f64tILLsQ==", "signatures": [{"sig": "MEQCIG+OT0ZsGqp5nLN7mcf3TdHVU3SoWtYa38CYY2E0HMLqAiAlb+hbNZ+S25WSiDPTzcjseBH8hXJV994+u6YiKoPBNQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5397}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-alpha.17_1741717513060_0.7958526834734172", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "7.27.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5043854ca620a94149372e69030ff8cb6a9eb0ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-hkGcueTEzuhB30B3eJCbCYeCaaEQOmQR0AdvzpD4LoN0GXMWzzGSuRrxR2xTnCrvNbVwK9N6/jQ92GSLfiZWoQ==", "signatures": [{"sig": "MEYCIQCYPuhRcXjFFyPkRPUCrg1GKHKlhUoq2m1fiY2Wum5d1QIhAIccXQHOcPiNi+jSToLa0WJ0j4IWNtoggY8NCwOd2ImN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5338}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_7.27.1_1746025749376_0.34240493051980914", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "1276c9a0dcf9d257b1c5c50dd119936dd8287bd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-emomZyyVjG7BXj75ICYz4HHXZTZQo/7VvMSS4+0GaAZppqmVTSJjQTJy7VC9cqvdLtw2/5aXx71OJh/dBZmgPQ==", "signatures": [{"sig": "MEQCIDKDiZRT0TZ1R04UIh8RGQY8hxDa/GoDzRgMeSpmNb5KAiB7qLjtLQoyugz/5yDguyFVfgXYehbWWtYRXpmcbtCXew==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5371}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-beta.0_1748620285892_0.4819826649896448", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-duplicate-named-capturing-groups-regex", "version": "8.0.0-beta.1", "description": "Compile regular expressions using duplicate named groups to index-based groups.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "core-js": "^3.30.2"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-duplicate-named-capturing-groups-regex@8.0.0-beta.1", "dist": {"shasum": "e047be0dbfa3b9f89bba5b1ebb6c46ee704e7d99", "integrity": "sha512-SGpXNzloR+A7HY6GV3fjrMEDRJFpaMfaVNVzuYKKKmqKbSrAMexQsXaSoRJbMCctjvaWTAzziW6AADWGHmcptQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5371, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICdMMgHe1gmd6FLK2VhCpvhYU0qXdFHPtihXTHZK+WlMAiAnauDCCUfyxULus2El2OKlKBUclVGK7u5QLRbn8f+lVw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-duplicate-named-capturing-groups-regex_8.0.0-beta.1_1751447070637_0.9060659555734139"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-07-26T16:59:25.758Z", "modified": "2025-07-02T09:04:31.042Z", "7.25.0": "2024-07-26T16:59:26.081Z", "8.0.0-alpha.12": "2024-07-26T17:33:45.693Z", "7.25.7": "2024-10-02T15:15:11.966Z", "7.25.9": "2024-10-22T15:21:28.065Z", "8.0.0-alpha.13": "2024-10-25T13:54:26.876Z", "8.0.0-alpha.14": "2024-12-06T16:54:17.005Z", "8.0.0-alpha.15": "2025-01-10T17:24:44.325Z", "8.0.0-alpha.16": "2025-02-14T11:59:20.267Z", "8.0.0-alpha.17": "2025-03-11T18:25:13.225Z", "7.27.1": "2025-04-30T15:09:09.566Z", "8.0.0-beta.0": "2025-05-30T15:51:26.074Z", "8.0.0-beta.1": "2025-07-02T09:04:30.809Z"}, "bugs": "https://github.com/babel/babel/issues", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-duplicate-named-capturing-groups-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-duplicate-named-capturing-groups-regex"}, "description": "Compile regular expressions using duplicate named groups to index-based groups.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}