{"_id": "foreground-child", "_rev": "39-3e5000c660eb6770884a8d95d44f51f6", "name": "foreground-child", "dist-tags": {"latest": "3.3.1"}, "versions": {"1.0.0": {"name": "foreground-child", "version": "1.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/foreground-child#readme", "bugs": {"url": "https://github.com/isaacs/foreground-child/issues"}, "dist": {"shasum": "df68705a9eb83698aa73d167dfc4cfb074981c57", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.0.0.tgz", "integrity": "sha512-pfIMpX0oMnnj5fzgleO3lgNDwMXPoT3GdDp+AXn4bvS+ZqHyseaojZI3XTUdxYO9ZHs4T0OBNCiOVi+1ZwuQ5Q==", "signatures": [{"sig": "MEUCIFEaCYwSXPsiUj6DND5GyNeB0DOVVL5an3driKccCdAxAiEA4tb649Dt/EM3OliIdE4IT9uFSE2ozLqOydO0ZyANH1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "df68705a9eb83698aa73d167dfc4cfb074981c57", "gitHead": "6839c88c90c0e1c60183694e3213e31a67a678fc", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/foreground-child.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "2.0.1", "dependencies": {}, "devDependencies": {"tap": "^1.0.4"}}, "1.1.0": {"name": "foreground-child", "version": "1.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/foreground-child#readme", "bugs": {"url": "https://github.com/isaacs/foreground-child/issues"}, "dist": {"shasum": "db3cdb31f3220aeda95bf6b7a1d0c7b399e615ff", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.1.0.tgz", "integrity": "sha512-HKjKlsl4epRY+odn8fbZrWk17CSZl6Jneu+xUb1eitYIQCXvBL7WAs0QGx8ypVjvEuAM0HYZhRUG3KL2CO/yLA==", "signatures": [{"sig": "MEYCIQCKqKhuKnqJQnoxcA62BwTLTwX6Mle4f6j8Q/6xyhr/pwIhAK0Ccyzncip2wg57ysawskO5lQBa/ElR9zgTibfc2kC6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "db3cdb31f3220aeda95bf6b7a1d0c7b399e615ff", "gitHead": "bf882691eebaad6cb4031e7b7189ad412021f4fd", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/foreground-child.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "2.0.1", "dependencies": {}, "devDependencies": {"tap": "^1.0.4"}}, "1.2.0": {"name": "foreground-child", "version": "1.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/foreground-child#readme", "bugs": {"url": "https://github.com/isaacs/foreground-child/issues"}, "dist": {"shasum": "6849d460f4ef5db854bb67777613a696226d8d75", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.2.0.tgz", "integrity": "sha512-V9xtp+aqgC95xg5Hu1+g+X6ol2d/njTRys7+Cd7k3u6qF3koiw+McQZjwzjteLCGmnhGR1EuPKB2VqSQloBj1w==", "signatures": [{"sig": "MEUCIQDidLqviiUMQyRSF/3v+fkrG0Rqhp2EILxsWeBDsg1M3gIgIhcqsfh8+LYuo5iq3Ul7b4fHbZGfmI8+G4yqNjgLRlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6849d460f4ef5db854bb67777613a696226d8d75", "gitHead": "0e62277b558b28d48a6f35a9ca72956bdeb9d092", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/foreground-child.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "2.0.1", "dependencies": {"signal-exit": "^2.0.0"}, "devDependencies": {"tap": "^1.0.4"}}, "1.3.0": {"name": "foreground-child", "version": "1.3.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/foreground-child#readme", "bugs": {"url": "https://github.com/isaacs/foreground-child/issues"}, "dist": {"shasum": "a69927df5c0273fdaf2535bf16c42a7fd711bb8b", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.3.0.tgz", "integrity": "sha512-niqR0KemJvjhOYPRszWejDNJ0+PT5mL0FsYsuq7LVAbodK63pm028O5cw+E9wEj3qEmHSUBnUeEIk93etSPjcg==", "signatures": [{"sig": "MEUCIF38XbaMCVeB6iIUc2cMaYqS8UwMt2UCZtZXc75t8f5eAiEAh7SEjTcecSI0K6FzqFznnl9khwv1wE0q1F/EeeRIToQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a69927df5c0273fdaf2535bf16c42a7fd711bb8b", "gitHead": "a7be4e335e8878882e88ee28aea268a0e59f5a78", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/foreground-child.git", "type": "git"}, "_npmVersion": "3.0.0", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "2.2.1", "dependencies": {"signal-exit": "^2.0.0"}, "devDependencies": {"tap": "^1.2.1"}}, "1.3.1": {"name": "foreground-child", "version": "1.3.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/foreground-child#readme", "bugs": {"url": "https://github.com/isaacs/foreground-child/issues"}, "dist": {"shasum": "f4c6d32b4e9d9e620c0cc3fab6f525c7ac1ce93a", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.3.1.tgz", "integrity": "sha512-vin6lgvFM/IU4YOBsNFAWNPHkkaYEG5uQlM9fJVK28DAM9A8tegcw5afc80D0LFB7G2Ra0++8k5rog5cz5BjOw==", "signatures": [{"sig": "MEUCIQCwvwAVn+d6VoWuHJnb36qocPOz44UrksC+nHS3fksNZAIgV9a0uz8M7AVdWZbIGliaPIX8OFu7KsIq8Pe3/i/+7G0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f4c6d32b4e9d9e620c0cc3fab6f525c7ac1ce93a", "gitHead": "df14154165a033d5c7a5158cb1eb36e05e58c331", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/foreground-child.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.0.0", "dependencies": {"win-spawn": "^2.0.0", "signal-exit": "^2.0.0"}, "devDependencies": {"tap": "^1.2.1"}}, "1.3.3": {"name": "foreground-child", "version": "1.3.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.3.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/foreground-child#readme", "bugs": {"url": "https://github.com/isaacs/foreground-child/issues"}, "dist": {"shasum": "dedd2d520f742383e71c58b4b3972b921d586abf", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.3.3.tgz", "integrity": "sha512-r9KtyAK6hG2ErfX+gd1tNcKIFdegI6q9bTqj9JW8D5py3Qce6oHytTcm/mNrdJKe1sESyf2GE4oj5VqrX6kIDg==", "signatures": [{"sig": "MEUCIQCg3HEWUaqr5w+frM0DTyFWfpnulbYKhnxCqSJB/yaOQQIgOzJZlSuEdXxMPvYHdfJW6ILVDz1TOmPWwvvdDPufwvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dedd2d520f742383e71c58b4b3972b921d586abf", "gitHead": "6b9997ead4352a0403db72069061f729be7252da", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/foreground-child.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.0.0", "dependencies": {"signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^4.0.0"}}, "1.3.4": {"name": "foreground-child", "version": "1.3.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.3.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "f499cbb1c2c1713a5e70a51b913328fc018cf607", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.3.4.tgz", "integrity": "sha512-LRNq0KxPUTH5J+5RWhPoGIf9ptVbm+Me9kSjRj1JD3RDKWLWl8gIWbU5RzT1LaGaYcD1ibCNmznaVzDVRIVcbw==", "signatures": [{"sig": "MEYCIQCXDQSgt3t1IIry+nCKtrHJ4lMJ29H9Mb5E7VkZYhiDkAIhAKUfpisVTFeQesehK0QFv/o5Ey2N2/S4VPeEMMqcysEK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f499cbb1c2c1713a5e70a51b913328fc018cf607", "gitHead": "259e3d954babae2400af15d58c106095d815cb5b", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.0.0", "dependencies": {"signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^4.0.0"}}, "1.3.5-beta.0": {"name": "foreground-child", "version": "1.3.5-beta.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.3.5-beta.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "07f7a424c4b7f08624f2e75386aaa66848c72416", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.3.5-beta.0.tgz", "integrity": "sha512-v+CVc7lh9+8X62jyepY7kI6hwFFFnYuStVN5pv90xbIQOSnqs9mjHKbEeP6hmnm9duHSaQKWUIackK6JflKQHg==", "signatures": [{"sig": "MEQCIEJaTwestXk7KWwBLAfeL4iQ+3PKcM8f9fbeCb6GF7BrAiB6fOdtf3oeLznr0lqrVBAdUDCn+tKNm0qHeVi2kbVVkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "07f7a424c4b7f08624f2e75386aaa66848c72416", "gitHead": "0d1dc1dc81e4fdee263ddd8935443d4397b9e3fe", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.0.0", "dependencies": {"which": "^1.2.1", "signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^5.1.1"}}, "1.3.5": {"name": "foreground-child", "version": "1.3.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.3.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "6d069bb520a1ac0eac00eb02a15c4d0d48f8440a", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.3.5.tgz", "integrity": "sha512-iYtsIhVstHHJqXc+dwkeRsLQbdq/4T8ydFoQVYCcJ7CekMBX+TBE1F5D3Jq/ENEAJp2oOC4wHGYfI7ZF7XjrWg==", "signatures": [{"sig": "MEYCIQDmHzUYzpvDwnLOU37hJC93VyTxJCd1RUtLJNyMXimMCQIhANTJibMvGqv3V0TbThD2ur64I7uLM/wuiAJFIirGgcy7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6d069bb520a1ac0eac00eb02a15c4d0d48f8440a", "gitHead": "a99d11484a01bb6a769fc6faa1729a2eb95d7134", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.0.0", "dependencies": {"which": "^1.2.1", "signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^5.1.1"}}, "1.4.0": {"name": "foreground-child", "version": "1.4.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "511fe23e3c8f80e2d28807908a98492972eaca7e", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.4.0.tgz", "integrity": "sha512-2sNT9ypiv2VNLv+XsbgsiVDlQoahSdBhb2ZX/dKudOxFzN5uyyCfH9V3TbqdxAGJXwSM/xPhmVMSYiJRCThoJg==", "signatures": [{"sig": "MEUCIQCzGmh+a89nwaVgX8tSpJTCYKOVVQFTOv1ex2zoowsV/gIgQA+1d/GIvRcQAOOAD3v1kn1Lp+K1YHS7OHLuEO/LAFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "511fe23e3c8f80e2d28807908a98492972eaca7e", "gitHead": "26a7e9935a6272001f5c8829982f4eed0cf93e4b", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "5.6.0", "dependencies": {"which": "^1.2.1", "signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^5.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.4.0.tgz_1459829853872_0.0696491978596896", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.0": {"name": "foreground-child", "version": "1.5.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "bb1e616664ac013b3bac8f52e47af581f208d531", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.0.tgz", "integrity": "sha512-EvCu5A+97eY2+o/MWQRqilsckFhEpBMvNEjvu17kqcYh5LPUmS77JL6DHyptoQGJZbs08X+9+RsdY4cy+yaeWA==", "signatures": [{"sig": "MEUCICMLnoCrEV2x8pFM/hNSRY+zGKLFfehCKJQl3B2P075rAiEA6X4IGTRpWdY7C+gjRbp23UQByyEf2AKguj5K5yqSL+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bb1e616664ac013b3bac8f52e47af581f208d531", "gitHead": "76f79e856bfd20eea9351c0ea3f28034de3e17c8", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.4.4", "dependencies": {"which": "^1.2.1", "signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^5.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.0.tgz_1465838266660_0.12312125531025231", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.1": {"name": "foreground-child", "version": "1.5.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "efa34d9780d257c750b11e296e2e1edc14fffaaa", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.1.tgz", "integrity": "sha512-TH3IAP3xY9a398jqR2ZU0qaHLXLOpuotwrfwbA4TgV7i7d6kiN9eZybK4vJYw3/QxqdfU0s2KPR7AeBlyt8tGA==", "signatures": [{"sig": "MEQCIH9kvlaOYSaPzTRoNVRKwehtR8eVluSXQm0B9BnTEDSyAiAgAPu1BlRI4tbx9jK6Pujdl21XaHfkzqWclAp70l19JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "efa34d9780d257c750b11e296e2e1edc14fffaaa", "gitHead": "bd6b02277b89ed18310f3638beb378973c1570e6", "scripts": {"test": "tap --coverage test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.4.4", "dependencies": {"which": "^1.2.1", "signal-exit": "^2.0.0", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"tap": "^5.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.1.tgz_1465852029190_0.36495648440904915", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.2": {"name": "foreground-child", "version": "1.5.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "a531130bf97d0903640281dcfbd9762906ae9e61", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.2.tgz", "integrity": "sha512-5AkN1e6EM3EqY8c9JNntNTasIV384BOCAQXIHqkMyU1gmvBSOBmMYlHRV2jmD09UdzPtXtr4nvePEfQSnjnNtw==", "signatures": [{"sig": "MEUCIGMKmlN3W3jvRjq9U8yg8c+rwcDdZLq4lYYm+SnZv/sZAiEArk5sv1FCHfSfL0wrPh0kgl7Y+sn6lddoxpgEWbsGY44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a531130bf97d0903640281dcfbd9762906ae9e61", "gitHead": "ffa5ceb9abe848385f73879fad03f6a70355c27a", "scripts": {"test": "tap --coverage test/*.js", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.4.4", "dependencies": {"cross-spawn": "^4", "signal-exit": "^2.0.0"}, "devDependencies": {"tap": "^5.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.2.tgz_1465971715761_0.9870206655468792", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.3": {"name": "foreground-child", "version": "1.5.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "94dd6aba671389867de8e57e99f1c2ecfb15c01a", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.3.tgz", "integrity": "sha512-ueRQdj42M2ewySFv5f3E+MGrpdx29LjiqRCfj5unguUoXK1iZA8DYwaBvHxl9p3YTKvt3Hda1BHKjD574LIklA==", "signatures": [{"sig": "MEYCIQCB3s4hXxnY6Cl0QwYFnh70IsI5ntB3vd/4SsaqFDRRogIhAOyG/e5lp1g+nuXFFy7sRyzyi81tih3v8Os5TCweSB29", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "94dd6aba671389867de8e57e99f1c2ecfb15c01a", "gitHead": "27729f8b0795d78864ddd1b3a5f0b7e07d48ddf8", "scripts": {"test": "tap --coverage test/*.js", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.10.2", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "4.4.4", "dependencies": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}, "devDependencies": {"tap": "^6.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.3.tgz_1467393732948_0.8209910723380744", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.4": {"name": "foreground-child", "version": "1.5.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "31183c8558a759f76989e97ebe77b8eb3f48ee8d", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.4.tgz", "integrity": "sha512-G93c8saNeK/nN6kzhTf+uYDRdwmyUWoWjEFtsod+D7K5sQsx00eC4Ccx/Z5TFfbKTYk1q+gh9me+glZNjFpG3A==", "signatures": [{"sig": "MEUCIQCKbv/qiWhj1ZWJt/hXFKXsQUPLGRAcIMoGu/8ubZMBzAIgcxjcTTKWH6M+eSIWY3N5CArtsVV43Wdv9OKRftTG/Aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "31183c8558a759f76989e97ebe77b8eb3f48ee8d", "gitHead": "4b4e44c5a9096bf4fee61261df466dd351fab75e", "scripts": {"test": "tap --coverage test/*.js", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "6.5.0", "dependencies": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}, "devDependencies": {"tap": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.4.tgz_1481847706151_0.7246038292068988", "host": "packages-18-east.internal.npmjs.com"}}, "1.5.5": {"name": "foreground-child", "version": "1.5.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "a289a87a35d9b3d6b6a1abe2a5525d08bfbab3e0", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.5.tgz", "integrity": "sha512-Frp3JScUFLg9ndylS3Oa8YX+RNV6NDOXmk1udMK7DoeIqEXBYFS+eJ8UedKZsUrkU1nYD3oSKGs3P37Rmy1wHw==", "signatures": [{"sig": "MEYCIQC4YUIZ2QNop+vswskZlGo0gjZdI9m40kc4qUW8GLKToQIhAN3VSMI0FKG/S8Pld+tqiSK8zUz+EyOVrPEPKseZr2xU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "a289a87a35d9b3d6b6a1abe2a5525d08bfbab3e0", "gitHead": "8503a5caa1c752c5f4ed4bd7e1d4c3735d541ddd", "scripts": {"test": "tap --coverage test/*.js", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "6.5.0", "dependencies": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}, "devDependencies": {"tap": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.5.tgz_1481847792473_0.3132688661571592", "host": "packages-18-east.internal.npmjs.com"}}, "1.5.6": {"name": "foreground-child", "version": "1.5.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@1.5.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "dist": {"shasum": "4fd71ad2dfde96789b980a5c0a295937cb2f5ce9", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-1.5.6.tgz", "integrity": "sha512-3TOY+4TKV0Ml83PXJQY+JFQaHNV38lzQDIzzXYg1kWdBLenGgoZhAs0CKgzI31vi2pWEpQMq/Yi4bpKwCPkw7g==", "signatures": [{"sig": "MEYCIQCHjkLXtY6yWtTXydtl3lcSB69RFqfHgcbfnmuQqgmbyQIhAI1a/IRq/a6KKg3ianpjw8jYRgciQpOEalpbTFhGIvKk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "4fd71ad2dfde96789b980a5c0a295937cb2f5ce9", "gitHead": "821919b77066e2f1e29cd55add1dfeb71ae19bd2", "scripts": {"test": "tap --coverage test/*.js", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "6.5.0", "dependencies": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}, "devDependencies": {"tap": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child-1.5.6.tgz_1481871968881_0.5189436199143529", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "foreground-child", "version": "2.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"jobs": 1}, "dist": {"shasum": "71b32800c9f15aa8f2f83f4a6bd9bff35d861a53", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-dCIq9FpEcyQyXKCkyzmlPTFNgrCzPudOe+mhvJU5zAtlBnGVy2yKxtfsxK2tQBThwq225jcvBjpw1Gr40uzZCA==", "signatures": [{"sig": "MEUCIQCHw/iCIBVFJDAPjy84StyWipr0ZSbSYFsUy4cZBMmsmAIgUeyBXW3C0VHhFVYfmkxdSbFNCJadFVaBG1sF8nIj8C0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcoWVCRA9TVsSAnZWagAAu6oP/07d40FyIy16bEEAxN19\nahx5OekjhOTUq+Z+b2P9Sgzd81fXXQkEZsKquZhR8yP0wJ51z6mGWehV90n0\nFvYmdSDFyTHfa/5Noye65REwVPx9QEWz77rtKg4kodjqsUlwtF9ThXoLgBAN\nO3N3e5F/97PbQ6iS2n8cagoEARVXwKj4YfvYzP8OJAkadhr/d+i64tCVYvQw\nK7t9VbHgKrtH6/vbpyJmc+22lpkHvVildO1sw5Es1xMRBwIloU/TkXzW62Bl\nk0mcKS5n+w0WHER2gml7kHt45vkt4ynw9X1hJkgtL/aaQEgGVD1LjPoIxkkd\ncPZfj0ATe7iVnwda2dhRO8xyMVz1svXXxldo3f8ee0kpa2yYCMU0OPHJiFlt\nRwZBEvRjL8qEC0YuJvp6YT+PdicaU4pCBtjJsrLb88wuRcKpEUcrhup5Sibd\nWfLI6+Drf06KjMWmCKb6J6dgXFrrmFYm+sH65lc9SucpoA6PEjgKtz50evh2\nqP08CFyTQ0cwNI651nXyB0yNe1vcctOWiAlu8Ji2041Jl03ZPtinR680D2o6\nd2d+aaa8y6MhFOlahx0VPwSzhb69Q13iocb4PAlU3hzHDEF9j1U/iHxaM8J7\np9ufFY3m7RP4+WYr7QsZAMAirm4Wdcjj36r2eOETBxUZkcbTvUgli5Su5NA5\nXutO\r\n=Hi24\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.0.0"}, "gitHead": "efba8bb6d7607b5c3b0d3e3897c73727577c8b45", "scripts": {"test": "tap", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "directories": {"test": "test"}, "_nodeVersion": "12.9.1", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_2.0.0_1567786388980_0.2837496426977206", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "foreground-child", "version": "3.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"ts": false, "jobs": 1, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "b74330d9c8bd46e1de3fc4193b5ab7a1fa445698", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.0.0.tgz", "fileCount": 29, "integrity": "sha512-5EAbYPWprm7OfA3rP+L4DilhPX3uS/DVMbEwnRDDJbmw3m8qgvp/SqDy3WJKl4O7740/knR4ahwzAQDQNpispA==", "signatures": [{"sig": "MEYCIQCaUACnkTgiufvfLBJE2WSWS2TkNT6Zgj0cFKi/4wOQZwIhAKk1B8Ceh/3Y5l8Z9ZubJY4o/9J3cuSfWfb2o6qc/TwU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPaayACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0eQ//SUJ3NAI8R7aFzulnfAJDExUkN6y/EV5jHAWLpCsUQeIEv5qK\r\nUojtRETGXb6NltJnqy1E17GBzraFb5+zllhvZNVrSdx3zMDMMxqfd7EYBdzC\r\napaAvWEJyF5qMkRQkaPBiE8TLLeK09Q4EY/imj8czBsH5HHynmIKCAWLkUlF\r\n/TiPWyfZ5trsFeo57BksEQWHdWqDKo/4Y0uDA7eiIsV8VaniAo8WcQe4oDZ2\r\ngsaBTeQIRc78JLVuPGqgSCgW38SOtfO7bfDfCAgs2NOmsvQSdTk/t1Ou/tZX\r\nW7WLovH4blE+I39KZvy59AslTONyE6WA+DRGIdY1HMcNnB8bRcxw4JAh0bLB\r\nL3MEc4e+ur7xewoMkij47mVIGFShpDYt+M3Hrq2aJKI8lMTvZ5JsRbs57U8j\r\nFo/ktc7l9DnlZcW5dHkOF2MKEz3RyjjveX5yeUi18OI6MamtOPrZMi/HsbUs\r\nRahKrEhIoGmrUrv1EjWnnC265qEuBj60GPIzLaCbqf5sYZWYPbT/Zpy+RZ+t\r\n8PQH6GOzOR0fMyRgB1XjAbOPpgvNN54wttI1ZjszIp3RWwYD6GQXpFVCfwxy\r\ng/IQSS+5R7BrHC8NSuXSq6qFonD69XY+2PFRj/HvetlZjWbgS7REaFruUOBY\r\nydJiYi4a4eksuNsQUa+i6Reem6tO5dV06Pk=\r\n=kCPJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "320149bc0b914bc22e0bf167a637c0f4473a98c6", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.13.0", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.24.2", "prettier": "^2.8.6", "@types/tap": "^15.0.8", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/cross-spawn": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_3.0.0_1681761970511_0.6692645626013878", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "foreground-child", "version": "3.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@3.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"ts": false, "jobs": 1, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "8eb5f65161c7972ef8784ee42831d41549b8ef64", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.1.0.tgz", "fileCount": 29, "integrity": "sha512-lXeSPRCndWPaipZbtI4CkvTZpF6OPsy19dkvf7+5AHeJD+w+iAKPc9Q78xWBmX4SdR+8xrtY9jTXs/YDv8q+Ug==", "signatures": [{"sig": "MEUCIQC6pvmSV4CKd+VzxFGVC8WfUph3mDPL+V8U7Z2akEC3eAIgbGFADhlItGh0PmogJFZ/7AQRJeckLoeySMT6xK57vkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPb/AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnWg/9HVYusrB9MtLoiX/zTfbrAF611VRvMKS533tKqbnvVhQLkZnn\r\nxalOLy498vsxzFJzncCecHpFhKsn6AwmgeDjc52hVQ32lg286J7VApSuNkYX\r\nk6d/q7oYX7/2sZ44BF0PMoQIG2VETGIX+oLKGMNFLZ2g1gMVwnG6N4OcfLpP\r\nM4t1o+ll9ZcDTqOcwT4AJBWt8oPhp7eo9dwLxXBZPfaBAsYisKyKtDe2FrnI\r\nR5/+0w7xtOflw9INMTaqTk1zHni3aS4kWGyQ5m3aXer94KLPU6uQwO6FAAYN\r\nH1wTNdOKHIhrgxuD1fjwb/Vtm9T8ToDTNBdfyn3J4xnFW1u2TozB9UEeKunm\r\n3iLUnRFBv0yQQbs/knrVeAENNWlSZrZM2G98n617gtarww0JaJKHJYHdQSi/\r\nlJGNE9w9cdN702p375Ujha3uBqNUdPl1KgCEvN8DnNaP0O8ypTyKgozed2Rl\r\nzJCmbvZBrEvdgil7sNXpwfm+Iia4i+UCIV9rrb5TzHzb9VEA4jroZAQeNbUg\r\nSNphclqUN86GkKXX1BzQ9lknHy6mbhJigYqdBCcPONNXi58hb1y9b5QxGpw6\r\nOKp1JMNiZXwGeaUEz6ZK5NLKGHw4UpkbV+0cXzpWaIpI5SM5umCdJ1fkBrJ7\r\nzi2j73E11ZPW1E5NXz9EO626EVF/byv5Oko=\r\n=4KtB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "d402af9b812ee51825867dcebe38435d0ebaa79b", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.13.0", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.24.2", "prettier": "^2.8.6", "@types/tap": "^15.0.8", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/cross-spawn": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_3.1.0_1681768383959_0.6631186653672749", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "foreground-child", "version": "3.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@3.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"ts": false, "jobs": 1, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "1d173e776d75d2772fed08efe4a0de1ea1b12d0d", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.1.1.tgz", "fileCount": 29, "integrity": "sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg==", "signatures": [{"sig": "MEYCIQDu0rfmDE2X8JcbxGl1oMiUorCOflTxuQf34AiE+BrGYAIhAN1oTlG+geZZKyeSB7kkGlQ6ZFNQlMcHZfWbFrEJNXe+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPePjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtHQ//fPRhpbQIsMuk60yAACNQDrlO71Qc6V/WiFm8k3eEx43HiW/V\r\nQNmixnflZvaKgjEqQL6GVnJqcVEgvsWTrOdGkWPxhbzhqGr876rCLQ4PzbgY\r\n3/t3Xws5GFY/devSSbQyoJAaVQFEG/I+x9GiGLxat/ieLG5t0/kC1PamaYw3\r\nac7n62jLYA9pXZkgjyf0YDFgVaecq7S+6vsfmsMlTzLS62evYT4G9q6dXWZR\r\nfc0bix3NbYK2GzMz7rOSt3BAxS3QpxzGnF4dg+o5Bo6+biKAsdF0ehxKuUId\r\nbTy/T0Yxr43rsVTCBbQLWv6dFIZ2PL5telb2cw/ZinP5iur2KHcvhiQuWL8D\r\np5aRfGcnmtIbCu2RTOuiC4krle92XNJgutYbJrJ4NrSH2JeMsQoMcPdeYJnq\r\nG5GaZ+Pfhru/Zh+r9t0EByUeF2uZqRV8ce/Hb/4k0PMhBWeCaqbhMKCMJbXo\r\nKsAfjPediPCKRHOaGZ73zze6DiOJYEpoqcPKR4hsvIQnyV7tV+VgMdrGyCJM\r\nvZj5T0seTbtOxJal6Slp6jEipIp/EVyW9+ZhnwmslHs5THnySmTljCNRZ5RQ\r\nq26/q6zuWXEGOfruaSCN4q4lfeZhY75vQjlGx2PIi5Uxu12gBLWKlLL7b5Nc\r\n/hb98mq7rJ76MtaC5SajfBuPImLhWG4JUOo=\r\n=rWsL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "b375faa093d34fed38baaaedd166c871ff160a9f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.13.0", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.24.2", "prettier": "^2.8.6", "@types/tap": "^15.0.8", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/cross-spawn": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_3.1.1_1681777634766_0.38641479376821875", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "foreground-child", "version": "3.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@3.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"typecheck": true}, "dist": {"shasum": "5eb496c4ebf3bcc4572e8908a45a72f5a1d2d658", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.2.0.tgz", "fileCount": 37, "integrity": "sha512-CrWQNaEl1/6WeZoarcM9LHupTo3RpZO2Pdk1vktwzPiQTsJnAKJmm3TACKeG5UZbWDfaH2AbvYxzP96y0MT7fA==", "signatures": [{"sig": "MEUCIQDA7nRrh/Gn/vNgmfw79LtEaYpJTXY6R0jeg11dJz1QywIgdXlsXQ7xXDgrK+yFmfQYr5kzAc48RZz9eMFaNQotlk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67262}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./watchdog": "./src/watchdog.ts", "./package.json": "./package.json", "./proxy-signals": "./src/proxy-signals.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./watchdog": {"import": {"types": "./dist/esm/watchdog.d.ts", "source": "./src/watchdog.ts", "default": "./dist/esm/watchdog.js"}, "require": {"types": "./dist/commonjs/watchdog.d.ts", "source": "./src/watchdog.ts", "default": "./dist/commonjs/watchdog.js"}}, "./package.json": "./package.json", "./proxy-signals": {"import": {"types": "./dist/esm/proxy-signals.d.ts", "source": "./src/proxy-signals.ts", "default": "./dist/esm/proxy-signals.js"}, "require": {"types": "./dist/commonjs/proxy-signals.d.ts", "source": "./src/proxy-signals.ts", "default": "./dist/commonjs/proxy-signals.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "359ebef795e11b479dd9fbac96b1d627863ae81b", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.5", "tshy": "^1.15.1", "typedoc": "^0.24.2", "prettier": "^3.3.2", "@types/tap": "^15.0.8", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/cross-spawn": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_3.2.0_1718169695308_0.5150492107039017", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "foreground-child", "version": "3.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@3.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"typecheck": true}, "dist": {"shasum": "767004ccf3a5b30df39bed90718bab43fe0a59f7", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.2.1.tgz", "fileCount": 37, "integrity": "sha512-PXUUyLqrR2XCWICfv6ukppP96sdFwWbNEnfEMt7jNsISjMsvaLNinAHNDYyvkyU+SZG2BTSbT5NjG+vZslfGTA==", "signatures": [{"sig": "MEQCICh3UF+VAJBE3PR4nCLEMutI04CojoFdJIieW6ffZVutAiBpPfgu74fUsaEfaA39HHpkoMqf9ICY3vOGIdNkiz3TWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68184}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./watchdog": "./src/watchdog.ts", "./package.json": "./package.json", "./proxy-signals": "./src/proxy-signals.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./watchdog": {"import": {"types": "./dist/esm/watchdog.d.ts", "source": "./src/watchdog.ts", "default": "./dist/esm/watchdog.js"}, "require": {"types": "./dist/commonjs/watchdog.d.ts", "source": "./src/watchdog.ts", "default": "./dist/commonjs/watchdog.js"}}, "./package.json": "./package.json", "./proxy-signals": {"import": {"types": "./dist/esm/proxy-signals.d.ts", "source": "./src/proxy-signals.ts", "default": "./dist/esm/proxy-signals.js"}, "require": {"types": "./dist/commonjs/proxy-signals.d.ts", "source": "./src/proxy-signals.ts", "default": "./dist/commonjs/proxy-signals.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "132a0178990aa4f53a6208bc3bdb90369b35e434", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.5", "tshy": "^1.15.1", "typedoc": "^0.24.2", "prettier": "^3.3.2", "@types/tap": "^15.0.8", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/cross-spawn": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_3.2.1_1718402170699_0.970361734322567", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "foreground-child", "version": "3.3.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "foreground-child@3.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://github.com/tapjs/foreground-child#readme", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "tap": {"typecheck": true}, "dist": {"shasum": "0ac8644c06e431439f8561db8ecf29a7b5519c77", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.0.tgz", "fileCount": 37, "integrity": "sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==", "signatures": [{"sig": "MEUCIG2l+FZMwxYIqqQGjpRh1N/qB5x1d1x89kMSCvYnU+YjAiEAvz0mL63vMV7ARidb4UDiJitlZftiltOXhGp5tfSQHoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68972}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./watchdog": "./src/watchdog.ts", "./package.json": "./package.json", "./proxy-signals": "./src/proxy-signals.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./watchdog": {"import": {"types": "./dist/esm/watchdog.d.ts", "source": "./src/watchdog.ts", "default": "./dist/esm/watchdog.js"}, "require": {"types": "./dist/commonjs/watchdog.d.ts", "source": "./src/watchdog.ts", "default": "./dist/commonjs/watchdog.js"}}, "./package.json": "./package.json", "./proxy-signals": {"import": {"types": "./dist/esm/proxy-signals.d.ts", "source": "./src/proxy-signals.ts", "default": "./dist/esm/proxy-signals.js"}, "require": {"types": "./dist/commonjs/proxy-signals.d.ts", "source": "./src/proxy-signals.ts", "default": "./dist/commonjs/proxy-signals.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "f2e72ffc2a8077d26e1cacfc6944f16befaeafc8", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/tapjs/foreground-child.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.5", "tshy": "^1.15.1", "typedoc": "^0.24.2", "prettier": "^3.3.2", "@types/tap": "^15.0.8", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/cross-spawn": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/foreground-child_3.3.0_1723054770742_0.3032445959495287", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "foreground-child", "version": "3.3.1", "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./watchdog": {"import": {"types": "./dist/esm/watchdog.d.ts", "default": "./dist/esm/watchdog.js"}, "require": {"types": "./dist/commonjs/watchdog.d.ts", "default": "./dist/commonjs/watchdog.js"}}, "./proxy-signals": {"import": {"types": "./dist/esm/proxy-signals.d.ts", "default": "./dist/esm/proxy-signals.js"}, "require": {"types": "./dist/commonjs/proxy-signals.d.ts", "default": "./dist/commonjs/proxy-signals.js"}}, "./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "engines": {"node": ">=14"}, "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"typecheck": true}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/foreground-child.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/tap": "^15.0.8", "prettier": "^3.3.2", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.24.2", "typescript": "^5.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "tshy": {"exports": {"./watchdog": "./src/watchdog.ts", "./proxy-signals": "./src/proxy-signals.ts", "./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "module": "./dist/esm/index.js", "_id": "foreground-child@3.3.1", "gitHead": "e0e54390679d5d955a5d26ce26ee52813c561e6e", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "homepage": "https://github.com/tapjs/foreground-child#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==", "shasum": "32e8e9ed1b68a3497befb9ac2b6adf92a638576f", "tarball": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz", "fileCount": 37, "unpackedSize": 68302, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCVUDfy0L4zYxWzaolkgh7QfaTDuGG1By/eCa1kCS9L0gIhAP45zuaUlA+rAJ/yqyIhcpgBDI2n9e+P1knTM6qabrG/"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/foreground-child_3.3.1_1740415672213_0.6961207565369152"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-05-15T06:23:22.908Z", "modified": "2025-02-24T16:47:52.609Z", "1.0.0": "2015-05-15T06:23:22.908Z", "1.1.0": "2015-05-15T22:23:11.179Z", "1.2.0": "2015-05-23T20:15:19.209Z", "1.3.0": "2015-06-27T21:24:58.970Z", "1.3.1": "2015-09-18T18:21:41.281Z", "1.3.2": "2016-01-02T02:04:57.354Z", "1.3.3": "2016-01-02T04:45:59.193Z", "1.3.4": "2016-01-04T01:20:08.785Z", "1.3.5-beta.0": "2016-01-22T21:43:06.826Z", "1.3.5": "2016-01-24T19:33:37.282Z", "1.4.0": "2016-04-05T04:17:34.419Z", "1.5.0": "2016-06-13T17:17:47.259Z", "1.5.1": "2016-06-13T21:07:09.728Z", "1.5.2": "2016-06-15T06:22:00.895Z", "1.5.3": "2016-07-01T17:22:16.380Z", "1.5.4": "2016-12-16T00:21:48.106Z", "1.5.5": "2016-12-16T00:23:14.389Z", "1.5.6": "2016-12-16T07:06:09.106Z", "2.0.0": "2019-09-06T16:13:09.114Z", "3.0.0": "2023-04-17T20:06:10.683Z", "3.1.0": "2023-04-17T21:53:04.124Z", "3.1.1": "2023-04-18T00:27:14.995Z", "3.2.0": "2024-06-12T05:21:35.449Z", "3.2.1": "2024-06-14T21:56:10.883Z", "3.3.0": "2024-08-07T18:19:30.937Z", "3.3.1": "2025-02-24T16:47:52.413Z"}, "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "homepage": "https://github.com/tapjs/foreground-child#readme", "repository": {"type": "git", "url": "git+https://github.com/tapjs/foreground-child.git"}, "description": "Run a child as if it's the foreground process. Give it stdio. Exit when it exits.", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "readme": "# foreground-child\n\nRun a child as if it's the foreground process. Give it stdio. Exit\nwhen it exits.\n\nMostly this module is here to support some use cases around\nwrapping child processes for test coverage and such. But it's\nalso generally useful any time you want one program to execute\nanother as if it's the \"main\" process, for example, if a program\ntakes a `--cmd` argument to execute in some way.\n\n## USAGE\n\n```js\nimport { foregroundChild } from 'foreground-child'\n// hybrid module, this also works:\n// const { foregroundChild } = require('foreground-child')\n\n// cats out this file\nconst child = foregroundChild('cat', [__filename])\n\n// At this point, it's best to just do nothing else.\n// return or whatever.\n// If the child gets a signal, or just exits, then this\n// parent process will exit in the same way.\n```\n\nYou can provide custom spawn options by passing an object after\nthe program and arguments:\n\n```js\nconst child = foregroundChild(`cat ${__filename}`, { shell: true })\n```\n\nA callback can optionally be provided, if you want to perform an\naction before your foreground-child exits:\n\n```js\nconst child = foregroundChild('cat', [__filename], spawnOptions, () => {\n  doSomeActions()\n})\n```\n\nThe callback can return a Promise in order to perform\nasynchronous actions. If the callback does not return a promise,\nthen it must complete its actions within a single JavaScript\ntick.\n\n```js\nconst child = foregroundChild('cat', [__filename], async () => {\n  await doSomeAsyncActions()\n})\n```\n\nIf the callback throws or rejects, then it will be unhandled, and\nnode will exit in error.\n\nIf the callback returns a string value, then that will be used as\nthe signal to exit the parent process. If it returns a number,\nthen that number will be used as the parent exit status code. If\nit returns boolean `false`, then the parent process will not be\nterminated. If it returns `undefined`, then it will exit with the\nsame signal/code as the child process.\n\n## Caveats\n\nThe \"normal\" standard IO file descriptors (0, 1, and 2 for stdin,\nstdout, and stderr respectively) are shared with the child process.\nAdditionally, if there is an IPC channel set up in the parent, then\nmessages are proxied to the child on file descriptor 3.\n\nIn Node, it's possible to also map arbitrary file descriptors\ninto a child process. In these cases, foreground-child will not\nmap the file descriptors into the child. If file descriptors 0,\n1, or 2 are used for the IPC channel, then strange behavior may\nhappen (like printing IPC messages to stderr, for example).\n\nNote that a SIGKILL will always kill the parent process, but\nwill not proxy the signal to the child process, because SIGKILL\ncannot be caught. In order to address this, a special \"watchdog\"\nchild process is spawned which will send a SIGKILL to the child\nprocess if it does not terminate within half a second after the\nwatchdog receives a SIGHUP due to its parent terminating.\n\nOn Windows, issuing a `process.kill(process.pid, signal)` with a\nfatal termination signal may cause the process to exit with a `1`\nstatus code rather than reporting the signal properly. This\nmodule tries to do the right thing, but on Windows systems, you\nmay see that incorrect result. There is as far as I'm aware no\nworkaround for this.\n\n## util: `foreground-child/proxy-signals`\n\nIf you just want to proxy the signals to a child process that the\nmain process receives, you can use the `proxy-signals` export\nfrom this package.\n\n```js\nimport { proxySignals } from 'foreground-child/proxy-signals'\n\nconst childProcess = spawn('command', ['some', 'args'])\nproxySignals(childProcess)\n```\n\nNow, any fatal signal received by the current process will be\nproxied to the child process.\n\nIt doesn't go in the other direction; ie, signals sent to the\nchild process will not affect the parent. For that, listen to the\nchild `exit` or `close` events, and handle them appropriately.\n\n## util: `foreground-child/watchdog`\n\nIf you are spawning a child process, and want to ensure that it\nisn't left dangling if the parent process exits, you can use the\nwatchdog utility exported by this module.\n\n```js\nimport { watchdog } from 'foreground-child/watchdog'\n\nconst childProcess = spawn('command', ['some', 'args'])\nconst watchdogProcess = watchdog(childProcess)\n\n// watchdogProcess is a reference to the process monitoring the\n// parent and child. There's usually no reason to do anything\n// with it, as it's silent and will terminate\n// automatically when it's no longer needed.\n```\n", "readmeFilename": "README.md", "users": {"cef62": true, "johnnyscript": true}}