{"_id": "regenerate", "_rev": "54-bc33d84f5bcc444ce3f7a2aa19fc7a89", "name": "regenerate", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "dist-tags": {"latest": "1.4.2"}, "versions": {"0.1.0": {"name": "regenerate", "version": "0.1.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"istanbul": "~0.1.35", "grunt": "~0.4.1", "grunt-shell": "~0.2.2", "qunitjs": "~1.11.0", "qunit-clib": "~1.3.0", "requirejs": "~2.1.5"}, "_id": "regenerate@0.1.0", "dist": {"shasum": "209beb2deb550c42a5139f234e01207a18baf6a5", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.1.0.tgz", "integrity": "sha512-D2g/2wHhwmYdGeYg8bX1sKZRu679bU/zNkQsFxzIKoEdLJ6q9lXR/CprkduJYJoOw4MzwYO3+KNGYw92xtvTlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFPHvKyB21Re/KeGr1f2XMsccTXr1zuN+u1ZKu+GE6zlAiBdvBBV+Hgm/2Ra4GbnHc5t3bVPXzDeMIGr8Ey1ZDq45A=="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.2.0": {"name": "regenerate", "version": "0.2.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"istanbul": "~0.1.35", "grunt": "~0.4.1", "grunt-shell": "~0.2.2", "qunitjs": "~1.11.0", "qunit-clib": "~1.3.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.2.0", "dist": {"shasum": "64b5a265023b34b97526ad64436c0a013ba637cc", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.2.0.tgz", "integrity": "sha512-8BCdjeyGntfD+j1Ym1J3QQynjLUBtX9qhb2Ffs5r1xrzG9NNeKzKC+qQYDooHEzUlBpbVucGIzZZ+kskHNxGvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUjum65rhaP5bFFt7YRDeRvY7gbOJRUDVbB+1WvNGmxAiAE8L/xpvhEmDRlWR4HQor2/3MoTwas7XDymTD61Nj7Jg=="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.0": {"name": "regenerate", "version": "0.3.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"istanbul": "~0.1.35", "grunt": "~0.4.1", "grunt-shell": "~0.2.2", "qunitjs": "~1.11.0", "qunit-clib": "~1.3.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.3.0", "dist": {"shasum": "b8a44b669f8864624dcb46a1c0b13c0714c65bc6", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.3.0.tgz", "integrity": "sha512-gSmDWN87pGLN1h9B1n0dF7Hr3BxZ/2J1f90gFonO8xJfix/gm9tJtwwS6/lyX+i0Zic4e08TQc8e3HzOyYprdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICi5Sc1tyDZUxDj8QvePD0+S9nI6ANXJD9bcsoEdg8seAiEA3RZLgDH3hFDNOLdnoGcShvpSekFSOt316f6BoL2ox8s="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.4.0": {"name": "regenerate", "version": "0.4.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"istanbul": "~0.1.35", "grunt": "~0.4.1", "grunt-shell": "~0.2.2", "qunitjs": "~1.11.0", "qunit-clib": "~1.3.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.4.0", "dist": {"shasum": "2f7ea04833e619235f5db1474dc51b13a961c356", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.4.0.tgz", "integrity": "sha512-bjRgAx0AZB8bQEu+MYXq2i+omMtdNmklNgu0UbvWdOdU+l43+G0EpkMOjBXS6c4PdGV8tfAsZbRQmzjRi9oAlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9phL3GDuoIxgO/DKpWtLqWGLEznQ2hTjHNCCOGdM5RgIhALdk+hRiLRFCj7A3f8zMfEKpt6pz2Yup/H7SbEDI2/Hu"}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.5.0": {"name": "regenerate", "version": "0.5.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.5.0", "dist": {"shasum": "ea888f44613a9bbae24bfe4571e82ee6b677eb63", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.5.0.tgz", "integrity": "sha512-7F/8vLkb3ppuNl4omj8CNx323Ep4GCihxqJf8x8FNLg2sXNabdY9YyacSDTTpodsX4oL+ZXEHmTsorIBo1jTQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAne6DgAWoGkeolobcJneeGykidHw9tFrK07xx3iYhUHAiAfQJZdAdxddiNoqN4z2J8EDtje3oWrD0VkfpPhhozCeQ=="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.5.1": {"name": "regenerate", "version": "0.5.1", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.5.1", "dist": {"shasum": "2cfe959fc04e525f2b41e0599e08084a0a1a3be2", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.5.1.tgz", "integrity": "sha512-ZlgEV0TiKgG4m73Jo+7pBTR5DOy44h0HrHDqP5Rj4ksjw3tSosjRvMDq62b+TJnQa6uQi7ENiYoKn9ZB6Vmqeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmGFKmbc4aKiZocS/jStWzgocqXyXITlIgJEt4mR2ftwIgNCRxZmde9YZvqHyx3qyVswHIrg6lzYUpxIcc/GIt6CQ="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.5.2": {"name": "regenerate", "version": "0.5.2", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.5.2", "dist": {"shasum": "b0b2457703046d6bb000b74f735b2f2684345c40", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.5.2.tgz", "integrity": "sha512-tytqymOJpT+fNrOjYAzXEy2A3V1gsvBx62kwoOdRfHHbtex6eQY2VP3tPVFG8TNYgoxfXO72hG7PQm4qVRB2gg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBnSqJ84ErbG3hW0/fd390hwtlQtpup7WFMuqNKpaoByAiEA5mgthm7dx+svwD3978Nu0xlWgtrcnDbV4OeMFPIdjjE="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.5.3": {"name": "regenerate", "version": "0.5.3", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.5.3", "dist": {"shasum": "f46d513ff8a5e2bc7e170e126cb40a39e7186a29", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.5.3.tgz", "integrity": "sha512-8pT6BlO0neWHYqIGQC2B1kpTW5LjJWSNQPjcVJpoIcy1sTYSZ9s/y/lBxvrbyUlyNAzC3MnHtADqSFwJDBPr5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHeGSxWKnQ4nIXn/xejkMDO0K+IH9sCxfMOewY1cI1jQIgHzSquryi+02SxEdQ8B1ujYzHsF9Nb9yzfldTsMYSJ4Y="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.5.4": {"name": "regenerate", "version": "0.5.4", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.5.4", "dist": {"shasum": "e738624b39583ca7dfedbc20bdbf0b732db833b1", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.5.4.tgz", "integrity": "sha512-mQ0UrNfWWmh1isIvV1XKZYmNSU7YCcUPNZ6XfeVdWS1P0PZv7Ky+leq8MCFdSnYlAx9L617hj+O7n9eZXpCk6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDY6GPatZQTqyOrQf14XgYrnhUEZaI6zgaJsh/WnYKdDAiBExGQ659PnF2Xwu05MWjdzpVd/XRmfwU6Is8DGoZYTxA=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.6.0": {"name": "regenerate", "version": "0.6.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.1", "istanbul": "~0.1.36", "qunit-extras": "~1.0.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "regenerate@0.6.0", "dist": {"shasum": "b8ee2d49f3bb318c2d70ad2964e40881907eccea", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.6.0.tgz", "integrity": "sha512-71BqER4ix9ON8w5/PPaURSUrEiwMUU3i2h+Y5aYBbOU+YxzYth3MAIZ6xz5Tiqpm4OJWLTToxVsyMncszb0SKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDos6xbD8FvZGLw1dDidznNwrtQ/CM4ROEcl476uAqsNwIgFs/6HceKAcR3qZNlz1qVnOtBL/NsrI6lipb6pJpbaBI="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.6.1": {"name": "regenerate", "version": "0.6.1", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.6.4", "istanbul": "~0.2.7", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11"}, "_id": "regenerate@0.6.1", "dist": {"shasum": "9c2566b808191b5b4f1e7ba9ea757047a05131a9", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.6.1.tgz", "integrity": "sha512-qBT5Xf0Lw5i/5fS+GG8hLPXE4Tl8NA4M26Ca4LZb9sN2eIADjzEI1UmuTQwrh/9kO1dqDkf5vhm8hSKT+yF30Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFmdUAZLjPGKZScDKXjxGoTkEy9Z2XFSGCnXu6fIGLjsAiArbBeAxMtgYHIYJFGSPgwyx+CYS3Jl3jRpgR85StwoPw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.6.2": {"name": "regenerate", "version": "0.6.2", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "unicode", "generator", "tool"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.5", "grunt-shell": "~0.7.0", "istanbul": "~0.2.10", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11"}, "gitHead": "cb3c7f15941dee343e90607eb32c1330223d0a3c", "_id": "regenerate@0.6.2", "_shasum": "1424e600fc5994b7abfb83f3bb1d703ddd59f881", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "1424e600fc5994b7abfb83f3bb1d703ddd59f881", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.6.2.tgz", "integrity": "sha512-VTI1FJjGKHjgWtJSDxzWm5gIxkMbrzQbByCRj1UjLsSTwD0A/tA+CFXCsDbnb6UHi7tDRqI/Vvcl9pdgZ+iDqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEaOM0AOq1bxQ3sjzsbiT2IIbWx7I74gvf+N0KDaflEwIhALr4U8HzFXPZqYAsOx8aDSEnaDvW1LBMFnq8RubNnDIJ"}]}}, "0.6.3": {"name": "regenerate", "version": "0.6.3", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.1", "grunt": "^0.4.5", "grunt-shell": "^1.0.1", "istanbul": "^0.3.0", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.11"}, "_id": "regenerate@0.6.3", "_shasum": "778b44a558de69b9a7a031caaabfc3b878d97725", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "778b44a558de69b9a7a031caaabfc3b878d97725", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.6.3.tgz", "integrity": "sha512-Tdt4yIt5/Mdzr34bxBvX9W3/qn3FMAKCIn0fNOwp0snILeo7DlvAw08MqNaS0ajzzEDlk5rMWHYjP+8wjeRZtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIANho6J8ahcbu0ux9vZdAMU/zBTDUqb72TOln/kP1n9YAiBTR+Gi34gUQrdtWh33WkxAuPvgQcbNp8eNw/MmOhB/Xw=="}]}, "directories": {}}, "0.6.4": {"name": "regenerate", "version": "0.6.4", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "http://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.1", "grunt": "^0.4.5", "grunt-shell": "^1.0.1", "istanbul": "^0.3.0", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.11"}, "_id": "regenerate@0.6.4", "_shasum": "050668fd4ddafb2e75c7978b88c2245c0c821423", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "050668fd4ddafb2e75c7978b88c2245c0c821423", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-0.6.4.tgz", "integrity": "sha512-uNsp8PD/Ws8K0haNCVIrdkFDYaOiEOvSXf6/hD9iiki755AqxuAJpTFmo3O2o6UMplw6lxEuw4kllpxr1y2NlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC347Q0YTFexYDnIHmgTpFg2iPSwHj6660nx8s6szsI8AIhAM9Wm/DWs6XM7OkhWjzyjgGRGmw3ccsnt7JGz/RbhMnC"}]}, "directories": {}}, "1.0.0": {"name": "regenerate", "version": "1.0.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "c0e771dd3e9bc3cebfe8e2d881a5e0b1f4831ff9", "_id": "regenerate@1.0.0", "_shasum": "acff6610ae930674705cb899a025270f96b73ea1", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "acff6610ae930674705cb899a025270f96b73ea1", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.0.0.tgz", "integrity": "sha512-InvY1SoF3kIBqaC+n46auWELl6SGZ0ZDzhny8sEvQPm74hUMukAzFNlGlnLQQo4tZyfW+X7rwiiU0sado4JsKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCB+jgpmb2A4B3YiHdl7xq5Ho9ct6sGDnuR/BZr/Qo+SgIgCTplktcfpnwLr4QnHvP1XxDDFn8pANHKgrA/L9ZUD4g="}]}, "directories": {}}, "1.0.1": {"name": "regenerate", "version": "1.0.1", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "913bbfbf71ff2a4b4cf74f8779a9e256b8c6a9df", "_id": "regenerate@1.0.1", "_shasum": "3905476f2a28044ebe70c47e0a4d11d9d17b7424", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "3905476f2a28044ebe70c47e0a4d11d9d17b7424", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.0.1.tgz", "integrity": "sha512-R+Un3vdU+ZjcoLGquih0tgwnMu4B1/+1cAhrry11KmSo8o35zKjKcQwkm2cOfGCLpgyCBiKsGjOAO47Jqu8/Kw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9xVFxBMmfCVLTj69p3gI4aP6WPkHbbkqMbvq6OSeRwAiEAvShc3hvKQ5LvSPGrKjatVwPbYe2/z2cF51xy+4qm7sE="}]}, "directories": {}}, "1.1.0": {"name": "regenerate", "version": "1.1.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "e3b77df67e6cdc0e76f28e0e5520bf6f2f364bd9", "_id": "regenerate@1.1.0", "_shasum": "4d03565fdb4ed5487c3750b4ccfa76d5a346525d", "_from": ".", "_npmVersion": "2.1.18", "_nodeVersion": "1.0.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "4d03565fdb4ed5487c3750b4ccfa76d5a346525d", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.1.0.tgz", "integrity": "sha512-DTIXTyJgAoH8xH5yWla4yOuXgIqUtFIHkEb7CoVbMRcFERjFso2fZHycRwl0nlGKDg1a3VXT0VDjPJZm2pZg4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIUsFKln+qJiEy7+hIKNPgPQCsvGNZgwVms2IlzwdCLAiEA6bco/rP662QA0GD9kxA3cRwRxx4IWxWYfOSQg0wy6gc="}]}, "directories": {}}, "1.2.0": {"name": "regenerate", "version": "1.2.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "0be62841c660ac94dd7e91e01771e8a2ee03b286", "_id": "regenerate@1.2.0", "_shasum": "0a36d08b5e11bc82e6f1592fe804b6576a7bd355", "_from": ".", "_npmVersion": "2.1.18", "_nodeVersion": "1.0.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "0a36d08b5e11bc82e6f1592fe804b6576a7bd355", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.2.0.tgz", "integrity": "sha512-mYZu6N4x9KRSFCHjlkUqheUgk6VbgtFEqzRSTSBnk2+exJ6KZQMCGBnEVS7dn8SXJinhCYH7PdxgmNxKG0+7uQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQiXhaOgIxBdvsBAyvjDDcSbu+1Fs2FgCgrA8Db8btzQIhAP8tN0lO/0mhD93PGnML7JNSoIttuo6Ibjs3S5Nrhqj1"}]}, "directories": {}}, "1.2.1": {"name": "regenerate", "version": "1.2.1", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "50d081b309fa694cfffc5c22852469c7e9b110de", "_id": "regenerate@1.2.1", "_shasum": "9e30ba68a6bd96ac3dcba62ab09d55d4b2fcbe04", "_from": ".", "_npmVersion": "2.1.18", "_nodeVersion": "1.0.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "9e30ba68a6bd96ac3dcba62ab09d55d4b2fcbe04", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.2.1.tgz", "integrity": "sha512-L6TRV/of1sYKUWtc0K0lkJ/Rp3DMyBB5L4ShbRFuuc3GssyM1ne4D7XTbrS4GoDhyIFeqDCtvuB+I4R+S9mEkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClrcG4eOYqxot6UgvyFbUJ4vdGMNfq7Y2HlYCD/GwBYAIhAN9eDL7snoL1hVU3fzXtaJX2jBPgIDBAhSGlKMmIBM5V"}]}, "directories": {}}, "1.3.0": {"name": "regenerate", "version": "1.3.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "44e36b2e4d05150437849cebd7f09f4711bea113", "_id": "regenerate@1.3.0", "_shasum": "4d2ced0ae5e25330768e687e720f542df7425881", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "4d2ced0ae5e25330768e687e720f542df7425881", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.3.0.tgz", "integrity": "sha512-IjaOZHaIzleQJB5Zgu0APvOoki/AaBpoIQ8HqzW3R3C+VcuKycO9X2JrHV2wWHEZdMUy73a4mj3dS4ZoziszHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICZenjWYC9+oBlXJ6pmX0UMbmmxJxnSgc1hSNbB2MESaAiEAytGrl8lWBtL7NU8L39NA4Y5r8/oRwas/59AVJDJ9+Uw="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regenerate-1.3.0.tgz_1464170264703_0.21450195997022092"}, "directories": {}}, "1.3.1": {"name": "regenerate", "version": "1.3.1", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.2", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.3.2", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "b842e5af68e07d8cb020fd1f03c12d31d9304999", "_id": "regenerate@1.3.1", "_shasum": "0300203a5d2fdcf89116dce84275d011f5903f33", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "0300203a5d2fdcf89116dce84275d011f5903f33", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.3.1.tgz", "integrity": "sha512-cQTnjquHzlYfRq8HAbBlmohR24o6mdhFt4mJv3PqLJa6WBSS53SIOfASxUcgsYc+Qlc0QOu9IDaJUw0fP6hpbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGqxTtVek1HByl36wr3+NnQo9Y3OCTIpQhIHa8PUZ1VDAiEAt9mWJFcVtyTSaOWLBWfmE3Bp4rde8X3uBLVPXkRO9r4="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regenerate-1.3.1.tgz_1464792438757_0.7200901845935732"}, "directories": {}}, "1.3.2": {"name": "regenerate", "version": "1.3.2", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "test": "node tests/tests.js"}, "devDependencies": {"codecov": "^1.0.1", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.4.3", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "c37fdc9cabf03610c0f6f8bfc4f7da822cfa5a89", "_id": "regenerate@1.3.2", "_shasum": "d1941c67bad437e1be76433add5b385f95b19260", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "d1941c67bad437e1be76433add5b385f95b19260", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.3.2.tgz", "integrity": "sha512-ZjGdBdKBADWnb6oF2uE/OjY3k8Nm4yY4nXhY+cq7NqheN7x23bcm/obALbqev4Kd3bOvWIvYLmUacnc8CI07oA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+p7kXu1bSDOMatXcE4UOkjb9RMwXrB+iSoaLkHhQeBQIgbA5QSggBATSJ1eL5/FoiMYyV2T5/3ThRSbzHHUEQyao="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/regenerate-1.3.2.tgz_1478868344403_0.2891066016163677"}, "directories": {}}, "1.3.3": {"name": "regenerate", "version": "1.3.3", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "test": "node tests/tests.js"}, "devDependencies": {"codecov": "^1.0.1", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.4.3", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "d34ae9be511306a872b742d8421ae18c4ffdc0bf", "_id": "regenerate@1.3.3", "_npmVersion": "5.4.1", "_nodeVersion": "8.1.2", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jVpo1GadrDAK59t/0jRx5VxYWQEDkkEKi6+HjE3joFVLfDOh9Xrdh0dF1eSq+BI/SwvTQ44gSscJ8N5zYL61sg==", "shasum": "0c336d3980553d755c39b586ae3b20aa49c82b7f", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.3.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA5rJNcfE5Z6HTj0zww+GC4SVipGAoYaFvJ8U353GGQCAiEAvQyPgNvFofv2bsTKOyi1fr7prM50XWmoyMZCh5zu9aY="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerate-1.3.3.tgz_1505744276299_0.4259878061711788"}, "directories": {}}, "1.4.0": {"name": "regenerate", "version": "1.4.0", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "test": "node tests/tests.js"}, "devDependencies": {"codecov": "^1.0.1", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.4.3", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "7a1fa1a719898914b27f8b7a5a673af09021aa22", "_id": "regenerate@1.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg==", "shasum": "4a856ec4b56e4077c557589cae85e7a4c8869a11", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.0.tgz", "fileCount": 4, "unpackedSize": 49188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa93VeCRA9TVsSAnZWagAA2NEP/R5hZiIm5CINinkaET0q\nJbzVDNOPvbMass//HsxO2QpmlIoYk0nJh+Ga9QL85GnjtIA/J+dRWV0QR/eP\n25xRSdYD5Chzi1w+Xo/ruHVbGE8kmUlFdI32VumtnOctkmXgJ6WvjcT/VBip\n15KxSYDgbiT1bW9Bp8CZBwtBD2crqHaEMXFxzYz3Kppb63Y/zC+j942IMwyI\n0dMBhT/k2JFHZ29AG7hqWDakoLg8TI0DvnhV03QqxGw8r4QGo7/QTyFngkgT\nlUChRKVJztbnDLoXUS9VF6fPht8iKY0Y25ykVy/BTpF/aaX+8x6LI/DwyiqB\n0nTe+2YqVer2pV5xURCKzKvD6j8czG61k2tYkJ/J5ivzjXkU7k9dZnXvBokX\nBih6/zBS9PQ4NsljdSRtG8ZUyGUSoe+sNC7jr4i3IDeBKk9svMrWBDk0dplu\nJvkKlNv9jbhBWWqkTiaxtIoPdJSlE2cNWLm0L08RzHtExiEDkAuO+//n79+9\nqZGKdpSDjSVgxka9tqes7iJFizE8JTesNZOqaXO+1ZXvTdnmktSB4hFNKwr4\n8Ahk3hGzSF/4xl9LCF1rnfOuecgWV+4nFc7YTBCJB2JfsPUQCq9gtFY2OYnJ\nSaeePeaJaMQOHjj8WXxVPLucLEohxEeD8/hLieEmR0YyqO223McT+uVUsvDy\nrFAw\r\n=/hzb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHwKFmWhsZuA+O9PxVh/uJQzR+0m2+wCI4JXigT7KZhGAiEAlxj78Vzu4sCQ3fCPomKEQKlq7jjhUgR6t4lZ/FRXHNQ="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerate_1.4.0_1526166876919_0.054054282723104974"}, "_hasShrinkwrap": false}, "1.4.1": {"name": "regenerate", "version": "1.4.1", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "test": "node tests/tests.js"}, "devDependencies": {"codecov": "^1.0.1", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.4.3", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "4aee39dc05d4a683dfff648a85b3326a6fc1be5f", "_id": "regenerate@1.4.1", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-j2+C8+NtXQgEKWk49MMP5P/u2GhnahTtVkRIHr5R5lVRlbKvmQ+oS+A5aLKWp2ma5VkT8sh6v+v4hbH0YHR66A==", "shasum": "cad92ad8e6b591773485fbe05a485caf4f457e6f", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.1.tgz", "fileCount": 4, "unpackedSize": 49230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2VIYCRA9TVsSAnZWagAAuv8P/0BHBrrWkHzd1UUP1nm3\nQgya93iBylmvFJCtGvxk+bLX/q8zQ43FBfI/2RmRkadstO18vAE67EQ+4b8v\nYb/vE9l7p7zeIhmqSO/FVTAHlxjQdlZulBh6GkNZT4lGQc8rkVAbg3Z+WgtN\nZ135jDj5JgaEfWo7AEkfcyfdWvYLIy1BK8OezXNzInZq/7j7t8LoeOh9XCzq\nAP1wjbyuOhBnYCWQKnRVtpg5aMnfYMh97T9Lm5nLQRkS2KVxoxkWO7j58LYr\nVkzxxs1Nq7IQR8yUi4pQ+wTxstXNR/0gqynQJ8qGdzGkffTcG3T1okBC65WI\no/STNmseLVClD7Sr0vG8Lli+89GfD6tr+Sd1yKhttPMPDJG3lKoq1BPyHzPA\n1x0XgqEtkETo+ijzYWQ/f+BP8zoIv/QYnuaOWjczPy3EW0HgbTsOanGgfZEI\n6xlqfZMq9xRfXBbrwssQutxWorPKujVbEA0bCV2lUUBNRewgLy1jymHpYOWC\nnVDyQGawNzApO8/pSw0rtyvVj/ZIThADKI1Jm+Rm17pdjNRwAEIAi56WSqCq\naVsPwBeDkz+SfCr+541sPsLI35xey6cjtV4jAkbkGJTvvgYcyBH0uWYue8oC\nvkT+f6jQ9JaZ8JYDXweIKjYo5rCzDZL+ZMsdUPPJmYoahp8wAIZtpIF/Wgqx\nIZVV\r\n=+TJn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFnNpRmttDnS66nv91WGuvRc7FFwbFn7nY0fQtj2cXG5AiA++c1fjbgHPfbKaNAUHYcCA4rmil0zND3XoDO4rox+wQ=="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerate_1.4.1_1591300632033_0.7200153295976652"}, "_hasShrinkwrap": false}, "1.4.2": {"name": "regenerate", "version": "1.4.2", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "scripts": {"cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "test": "node tests/tests.js"}, "devDependencies": {"codecov": "^1.0.1", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.4.3", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}, "gitHead": "11567339f40fd262435934d544885bc047cb4220", "_id": "regenerate@1.4.2", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==", "shasum": "b9346d8827e8f5a32f7ba29637d398b69014848a", "tarball": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz", "fileCount": 4, "unpackedSize": 49156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnnTkCRA9TVsSAnZWagAA+MUP/3tY37jpv4BqtJPa25Rq\nx2NVSD5b4AX3m0sXachibApJP3qW7V6NESX9fPvENqIV7XUC8tYTCZUVnL5T\noyCHpnctquPs+IeWmy6eBcb2fFNnanMAzmiXRZi4zBT+E1XVZdqwmL/m13iq\nWA0q4LYC6HkDOflhHU44GTiDh1QitEcKVfLGV+grvn+UwFSAaAqcx3IJUikE\nnBG8eei9gHEJtLDcAUz6PFhxZUrdLuldesCeA5lH8bvrGoIypGPwoVYTrIxx\nhVOCOFujiR2ZXNzrDNdmh9gieNuc94qkiuCc+mz5KBf9B6NxJDvpi80XuX1h\nv0eiZMVY+B9h8qS4+7oAtW/2qVBef6IIhYc6wpba+1s7N/khWMeb55YLaPy5\n3HBoh5IjqYOmYdOyanZ47g16lfSYmbbZzx0d9zkqX2Zf1F7RJOw3GpNaco/k\newBEMbpdwD5jhJSoewrOCUrm7/OwgVDIBhByj0P614ed3VlSQOdfMkHw0jFl\ndBfQmxCQ06hIew5PTrIOVMwGYxTy7K6SA5McMM2AAygJ7J++AW+82WiuMPn9\ngjg2knJdf7dGTDrxM4TbRWB1QCFHz94lsAjbBDQ6iCZhS8h7+VLXrmFu7boX\n4O0wz/uq9R6+nVJgL3qX2hwS3kixBbKJIHRA6c0jR5bTHuTzacpvXhoBa6O8\nPBBf\r\n=Z7PK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChSkzlucolBBODvqiAXXxv7LCY/t/o++lrupi8PL0jvgIga5KfVRsZRsLCvz6Q4/uKkJQUJ54lFIRfhFFGcUSCQHw="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerate_1.4.2_1604220132373_0.07393084977206899"}, "_hasShrinkwrap": false}}, "readme": "# Regenerate [![Build status](https://travis-ci.org/mathiasbynens/regenerate.svg?branch=master)](https://travis-ci.org/mathiasbynens/regenerate) [![Code coverage status](https://img.shields.io/codecov/c/github/mathiasbynens/regenerate.svg)](https://codecov.io/gh/mathiasbynens/regenerate)\n\n_Regenerate_ is a Unicode-aware regex generator for JavaScript. It allows you to easily generate ES5-compatible regular expressions based on a given set of Unicode symbols or code points. (This is trickier than you might think, because of [how JavaScript deals with astral symbols](https://mathiasbynens.be/notes/javascript-unicode).)\n\n## Installation\n\nVia [npm](https://npmjs.org/):\n\n```bash\nnpm install regenerate\n```\n\nVia [Bower](http://bower.io/):\n\n```bash\nbower install regenerate\n```\n\nIn a browser:\n\n```html\n<script src=\"regenerate.js\"></script>\n```\n\nIn [Node.js](https://nodejs.org/), [io.js](https://iojs.org/), and [RingoJS ≥ v0.8.0](http://ringojs.org/):\n\n```js\nvar regenerate = require('regenerate');\n```\n\nIn [Narwhal](http://narwhaljs.org/) and [RingoJS ≤ v0.7.0](http://ringojs.org/):\n\n```js\nvar regenerate = require('regenerate').regenerate;\n```\n\nIn [Rhino](http://www.mozilla.org/rhino/):\n\n```js\nload('regenerate.js');\n```\n\nUsing an AMD loader like [RequireJS](http://requirejs.org/):\n\n```js\nrequire(\n  {\n    'paths': {\n      'regenerate': 'path/to/regenerate'\n    }\n  },\n  ['regenerate'],\n  function(regenerate) {\n    console.log(regenerate);\n  }\n);\n```\n\n## API\n\n### `regenerate(value1, value2, value3, ...)`\n\nThe main Regenerate function. Calling this function creates a new set that gets a chainable API.\n\n```js\nvar set = regenerate()\n  .addRange(0x60, 0x69) // add U+0060 to U+0069\n  .remove(0x62, 0x64) // remove U+0062 and U+0064\n  .add(0x1D306); // add U+1D306\nset.valueOf();\n// → [0x60, 0x61, 0x63, 0x65, 0x66, 0x67, 0x68, 0x69, 0x1D306]\nset.toString();\n// → '[`ace-i]|\\\\uD834\\\\uDF06'\nset.toRegExp();\n// → /[`ace-i]|\\uD834\\uDF06/\n```\n\nAny arguments passed to `regenerate()` will be added to the set right away. Both code points (numbers) and symbols (strings consisting of a single Unicode symbol) are accepted, as well as arrays containing values of these types.\n\n```js\nregenerate(0x1D306, 'A', '©', 0x2603).toString();\n// → '[A\\\\xA9\\\\u2603]|\\\\uD834\\\\uDF06'\n\nvar items = [0x1D306, 'A', '©', 0x2603];\nregenerate(items).toString();\n// → '[A\\\\xA9\\\\u2603]|\\\\uD834\\\\uDF06'\n```\n\n### `regenerate.prototype.add(value1, value2, value3, ...)`\n\nAny arguments passed to `add()` are added to the set. Both code points (numbers) and symbols (strings consisting of a single Unicode symbol) are accepted, as well as arrays containing values of these types.\n\n```js\nregenerate().add(0x1D306, 'A', '©', 0x2603).toString();\n// → '[A\\\\xA9\\\\u2603]|\\\\uD834\\\\uDF06'\n\nvar items = [0x1D306, 'A', '©', 0x2603];\nregenerate().add(items).toString();\n// → '[A\\\\xA9\\\\u2603]|\\\\uD834\\\\uDF06'\n```\n\nIt’s also possible to pass in a Regenerate instance. Doing so adds all code points in that instance to the current set.\n\n```js\nvar set = regenerate(0x1D306, 'A');\nregenerate().add('©', 0x2603).add(set).toString();\n// → '[A\\\\xA9\\\\u2603]|\\\\uD834\\\\uDF06'\n```\n\nNote that the initial call to `regenerate()` acts like `add()`. This allows you to create a new Regenerate instance and add some code points to it in one go:\n\n```js\nregenerate(0x1D306, 'A', '©', 0x2603).toString();\n// → '[A\\\\xA9\\\\u2603]|\\\\uD834\\\\uDF06'\n```\n\n### `regenerate.prototype.remove(value1, value2, value3, ...)`\n\nAny arguments passed to `remove()` are removed from the set. Both code points (numbers) and symbols (strings consisting of a single Unicode symbol) are accepted, as well as arrays containing values of these types.\n\n```js\nregenerate(0x1D306, 'A', '©', 0x2603).remove('☃').toString();\n// → '[A\\\\xA9]|\\\\uD834\\\\uDF06'\n```\n\nIt’s also possible to pass in a Regenerate instance. Doing so removes all code points in that instance from the current set.\n\n```js\nvar set = regenerate('☃');\nregenerate(0x1D306, 'A', '©', 0x2603).remove(set).toString();\n// → '[A\\\\xA9]|\\\\uD834\\\\uDF06'\n```\n\n### `regenerate.prototype.addRange(start, end)`\n\nAdds a range of code points from `start` to `end` (inclusive) to the set. Both code points (numbers) and symbols (strings consisting of a single Unicode symbol) are accepted.\n\n```js\nregenerate(0x1D306).addRange(0x00, 0xFF).toString(16);\n// → '[\\\\0-\\\\xFF]|\\\\uD834\\\\uDF06'\n\nregenerate().addRange('A', 'z').toString();\n// → '[A-z]'\n```\n\n### `regenerate.prototype.removeRange(start, end)`\n\nRemoves a range of code points from `start` to `end` (inclusive) from the set. Both code points (numbers) and symbols (strings consisting of a single Unicode symbol) are accepted.\n\n```js\nregenerate()\n  .addRange(0x000000, 0x10FFFF) // add all Unicode code points\n  .removeRange('A', 'z') // remove all symbols from `A` to `z`\n  .toString();\n// → '[\\\\0-@\\\\{-\\\\uD7FF\\\\uE000-\\\\uFFFF]|[\\\\uD800-\\\\uDBFF][\\\\uDC00-\\\\uDFFF]|[\\\\uD800-\\\\uDBFF](?![\\\\uDC00-\\\\uDFFF])|(?:[^\\\\uD800-\\\\uDBFF]|^)[\\\\uDC00-\\\\uDFFF]'\n\nregenerate()\n  .addRange(0x000000, 0x10FFFF) // add all Unicode code points\n  .removeRange(0x0041, 0x007A) // remove all code points from U+0041 to U+007A\n  .toString();\n// → '[\\\\0-@\\\\{-\\\\uD7FF\\\\uE000-\\\\uFFFF]|[\\\\uD800-\\\\uDBFF][\\\\uDC00-\\\\uDFFF]|[\\\\uD800-\\\\uDBFF](?![\\\\uDC00-\\\\uDFFF])|(?:[^\\\\uD800-\\\\uDBFF]|^)[\\\\uDC00-\\\\uDFFF]'\n```\n\n### `regenerate.prototype.intersection(codePoints)`\n\nRemoves any code points from the set that are not present in both the set and the given `codePoints` array. `codePoints` must be an array of numeric code point values, i.e. numbers.\n\n```js\nregenerate()\n  .addRange(0x00, 0xFF) // add extended ASCII code points\n  .intersection([0x61, 0x69]) // remove all code points from the set except for these\n  .toString();\n// → '[ai]'\n```\n\nInstead of the `codePoints` array, it’s also possible to pass in a Regenerate instance.\n\n```js\nvar whitelist = regenerate(0x61, 0x69);\n\nregenerate()\n  .addRange(0x00, 0xFF) // add extended ASCII code points\n  .intersection(whitelist) // remove all code points from the set except for those in the `whitelist` set\n  .toString();\n// → '[ai]'\n```\n\n### `regenerate.prototype.contains(value)`\n\nReturns `true` if the given value is part of the set, and `false` otherwise. Both code points (numbers) and symbols (strings consisting of a single Unicode symbol) are accepted.\n\n```js\nvar set = regenerate().addRange(0x00, 0xFF);\nset.contains('A');\n// → true\nset.contains(0x1D306);\n// → false\n```\n\n### `regenerate.prototype.clone()`\n\nReturns a clone of the current code point set. Any actions performed on the clone won’t mutate the original set.\n\n```js\nvar setA = regenerate(0x1D306);\nvar setB = setA.clone().add(0x1F4A9);\nsetA.toArray();\n// → [0x1D306]\nsetB.toArray();\n// → [0x1D306, 0x1F4A9]\n```\n\n### `regenerate.prototype.toString(options)`\n\nReturns a string representing (part of) a regular expression that matches all the symbols mapped to the code points within the set.\n\n```js\nregenerate(0x1D306, 0x1F4A9).toString();\n// → '\\\\uD834\\\\uDF06|\\\\uD83D\\\\uDCA9'\n```\n\nIf the `bmpOnly` property of the optional `options` object is set to `true`, the output matches surrogates individually, regardless of whether they’re lone surrogates or just part of a surrogate pair. This simplifies the output, but it can only be used in case you’re certain the strings it will be used on don’t contain any astral symbols.\n\n```js\nvar highSurrogates = regenerate().addRange(0xD800, 0xDBFF);\nhighSurrogates.toString();\n// → '[\\\\uD800-\\\\uDBFF](?![\\\\uDC00-\\\\uDFFF])'\nhighSurrogates.toString({ 'bmpOnly': true });\n// → '[\\\\uD800-\\\\uDBFF]'\n\nvar lowSurrogates = regenerate().addRange(0xDC00, 0xDFFF);\nlowSurrogates.toString();\n// → '(?:[^\\\\uD800-\\\\uDBFF]|^)[\\\\uDC00-\\\\uDFFF]'\nlowSurrogates.toString({ 'bmpOnly': true });\n// → '[\\\\uDC00-\\\\uDFFF]'\n```\n\nNote that lone low surrogates cannot be matched accurately using regular expressions in JavaScript without the use of [lookbehind assertions](https://mathiasbynens.be/notes/es-regexp-proposals#lookbehinds), which aren't yet widely supported. Regenerate’s output makes a best-effort approach but [there can be false negatives in this regard](https://github.com/mathiasbynens/regenerate/issues/28#issuecomment-72224808).\n\nIf the `hasUnicodeFlag` property of the optional `options` object is set to `true`, the output makes use of Unicode code point escapes (`\\u{…}`) where applicable. This simplifies the output at the cost of compatibility and portability, since it means the output can only be used as a pattern in a regular expression with [the ES6 `u` flag](https://mathiasbynens.be/notes/es6-unicode-regex) enabled.\n\n```js\nvar set = regenerate().addRange(0x0, 0x10FFFF);\n\nset.toString();\n// → '[\\\\0-\\\\uD7FF\\\\uE000-\\\\uFFFF]|[\\\\uD800-\\\\uDBFF][\\\\uDC00-\\\\uDFFF]|[\\\\uD800-\\\\uDBFF](?![\\\\uDC00-\\\\uDFFF])|(?:[^\\\\uD800-\\\\uDBFF]|^)[\\\\uDC00-\\\\uDFFF]''\n\nset.toString({ 'hasUnicodeFlag': true });\n// → '[\\\\0-\\\\u{10FFFF}]'\n```\n\n### `regenerate.prototype.toRegExp(flags = '')`\n\nReturns a regular expression that matches all the symbols mapped to the code points within the set. Optionally, you can pass [flags](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#Parameters) to be added to the regular expression.\n\n```js\nvar regex = regenerate(0x1D306, 0x1F4A9).toRegExp();\n// → /\\uD834\\uDF06|\\uD83D\\uDCA9/\nregex.test('𝌆');\n// → true\nregex.test('A');\n// → false\n\n// With flags:\nvar regex = regenerate(0x1D306, 0x1F4A9).toRegExp('g');\n// → /\\uD834\\uDF06|\\uD83D\\uDCA9/g\n```\n\n**Note:** This probably shouldn’t be used. Regenerate is intended as a tool that is used as part of a build process, not at runtime.\n\n### `regenerate.prototype.valueOf()` or `regenerate.prototype.toArray()`\n\nReturns a sorted array of unique code points in the set.\n\n```js\nregenerate(0x1D306)\n  .addRange(0x60, 0x65)\n  .add(0x59, 0x60) // note: 0x59 is added after 0x65, and 0x60 is a duplicate\n  .valueOf();\n// → [0x59, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x1D306]\n```\n\n### `regenerate.version`\n\nA string representing the semantic version number.\n\n## Combine Regenerate with other libraries\n\nRegenerate gets even better when combined with other libraries such as [Punycode.js](https://mths.be/punycode). Here’s an example where [Punycode.js](https://mths.be/punycode) is used to convert a string into an array of code points, that is then passed on to Regenerate:\n\n```js\nvar regenerate = require('regenerate');\nvar punycode = require('punycode');\n\nvar string = 'Lorem ipsum dolor sit amet.';\n// Get an array of all code points used in the string:\nvar codePoints = punycode.ucs2.decode(string);\n\n// Generate a regular expression that matches any of the symbols used in the string:\nregenerate(codePoints).toString();\n// → '[ \\\\.Ladeilmopr-u]'\n```\n\nIn ES6 you can do something similar with [`Array.from`](https://mths.be/array-from) which uses [the string’s iterator](https://mathiasbynens.be/notes/javascript-unicode#iterating-over-symbols) to split the given string into an array of strings that each contain a single symbol. [`regenerate()`](#regenerateprototypeaddvalue1-value2-value3-) accepts both strings and code points, remember?\n\n```js\nvar regenerate = require('regenerate');\n\nvar string = 'Lorem ipsum dolor sit amet.';\n// Get an array of all symbols used in the string:\nvar symbols = Array.from(string);\n\n// Generate a regular expression that matches any of the symbols used in the string:\nregenerate(symbols).toString();\n// → '[ \\\\.Ladeilmopr-u]'\n```\n\n## Support\n\nRegenerate supports at least Chrome 27+, Firefox 3+, Safari 4+, Opera 10+, IE 6+, Node.js v0.10.0+, io.js v1.0.0+, Narwhal 0.3.2+, RingoJS 0.8+, PhantomJS 1.9.0+, and Rhino 1.7RC4+.\n\n## Unit tests & code coverage\n\nAfter cloning this repository, run `npm install` to install the dependencies needed for Regenerate development and testing. You may want to install Istanbul _globally_ using `npm install istanbul -g`.\n\nOnce that’s done, you can run the unit tests in Node using `npm test` or `node tests/tests.js`. To run the tests in Rhino, Ringo, Narwhal, and web browsers as well, use `grunt test`.\n\nTo generate the code coverage report, use `grunt cover`.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\nRegenerate is available under the [MIT](https://mths.be/mit) license.\n", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T10:41:50.704Z", "created": "2013-05-20T22:26:40.611Z", "0.1.0": "2013-05-20T22:26:43.564Z", "0.2.0": "2013-05-22T17:17:08.104Z", "0.3.0": "2013-05-22T19:52:07.744Z", "0.4.0": "2013-05-27T11:22:40.417Z", "0.5.0": "2013-06-02T16:38:17.998Z", "0.5.1": "2013-06-05T09:55:14.845Z", "0.5.2": "2013-06-17T13:12:20.520Z", "0.5.3": "2013-08-18T20:00:48.741Z", "0.5.4": "2013-08-19T20:57:22.112Z", "0.6.0": "2014-04-23T14:01:07.621Z", "0.6.1": "2014-04-25T12:30:52.894Z", "0.6.2": "2014-05-24T12:08:56.325Z", "0.6.3": "2014-08-24T16:57:11.130Z", "0.6.4": "2014-08-24T18:39:56.018Z", "1.0.0": "2014-09-23T08:27:42.776Z", "1.0.1": "2014-10-12T05:46:30.795Z", "1.1.0": "2015-01-24T16:07:43.798Z", "1.2.0": "2015-01-26T09:10:34.991Z", "1.2.1": "2015-01-26T09:22:52.398Z", "1.3.0": "2016-05-25T09:57:46.026Z", "1.3.1": "2016-06-01T14:47:19.994Z", "1.3.2": "2016-11-11T12:45:46.670Z", "1.3.3": "2017-09-18T14:17:57.385Z", "1.4.0": "2018-05-12T23:14:37.010Z", "1.4.1": "2020-06-04T19:57:12.171Z", "1.4.2": "2020-11-01T08:42:12.525Z"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate.git"}, "homepage": "https://mths.be/regenerate", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "bugs": {"url": "https://github.com/mathiasbynens/regenerate/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"vkarpov15": true, "yaycmyk": true, "bluelovers": true}}