{"_id": "html-parse-stringify", "_rev": "26-2685d6dddffebd0620730e4a01095a33", "name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "dist-tags": {"latest": "3.0.1"}, "versions": {"1.0.0": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "hen<PERSON>@andyet.net"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "devDependencies": {"jshint": "^2.5.10", "precommit-hook": "^1.0.7", "tape": "^3.0.3"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["html", "parse", "stringify", "ast"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/henrikjoreteg/html-parse-stringify"}, "scripts": {"test": "node test/index.js | tap-spec"}, "gitHead": "34f2b329e7c372ce156f2a669d9fcc16c307932e", "_id": "html-parse-stringify@1.0.0", "_shasum": "8f64826d6d76056aee32263f367f80e363e9f331", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.30", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "hen<PERSON>@andyet.net"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "hen<PERSON>@andyet.net"}], "dist": {"shasum": "8f64826d6d76056aee32263f367f80e363e9f331", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-1.0.0.tgz", "integrity": "sha512-SwaxPHJHeG4ixXn38OLy6GkxOg3Df9DprzzHb2KgZJQBTi7zaUPJONJzjrY6Zkt0YUlYlkICtCfvHK1545va4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNTIM6jVCw7KJIefRA7FtvfTzNfXRD61WYuyQDm9ywMAIhAJDdKF4K+zM2G4VdE08qxSDJZFH5xJz8CGqBK4CRCmWs"}]}, "directories": {}}, "1.0.1": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "hen<PERSON>@andyet.net"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "devDependencies": {"jshint": "^2.5.10", "precommit-hook": "^1.0.7", "tape": "^3.0.3"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["html", "parse", "stringify", "ast"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/henrikjoreteg/html-parse-stringify"}, "scripts": {"test": "node test/index.js | tap-spec"}, "gitHead": "8e6ba4fbf6dcbf36b84b41ee18974d89a672fa33", "_id": "html-parse-stringify@1.0.1", "_shasum": "5423a4a136ec966c5319818d7b8930c4c24bfee6", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.30", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "hen<PERSON>@andyet.net"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "hen<PERSON>@andyet.net"}, {"name": "latentflip", "email": "<EMAIL>"}], "dist": {"shasum": "5423a4a136ec966c5319818d7b8930c4c24bfee6", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-1.0.1.tgz", "integrity": "sha512-lHPPd83+N1yxQE+3IIWOGf1TZrK+3owQMI4i1F3m44ekqzXvECeEdZ7N4hqA/KjFwnjt6na1FgxeU2A+WD06Fw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDAEexCOOXnUQFDR59ZqnTqMleYvql1xFmF7JvDAU9EgAiAhcu4RuJCmyClNOoR/vSFp1AYOgD7kYyl+QnAMKdaRPw=="}]}, "directories": {}}, "1.0.2": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "hen<PERSON>@andyet.net"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "^1.0.0"}, "devDependencies": {"jshint": "^2.5.10", "phantomjs": "^1.9.13", "precommit-hook": "^1.0.7", "run-browser": "^2.0.1", "tap-spec": "^2.1.2", "tape": "^3.0.3", "zuul": "^1.16.5"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["html", "parse", "stringify", "ast"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/henrikjoreteg/html-parse-stringify"}, "scripts": {"start": "run-browser test/*", "test": "zuul --phantom -- test/index.js", "test-ci": "zuul -- test/index.js"}, "gitHead": "9435f258dc063933585be305af2954d229d46e4a", "_id": "html-parse-stringify@1.0.2", "_shasum": "1208e98cd7430ad7efb12e1e7acee9214fd22283", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "0.10.36", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "hen<PERSON>@andyet.net"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "hen<PERSON>@andyet.net"}, {"name": "latentflip", "email": "<EMAIL>"}], "dist": {"shasum": "1208e98cd7430ad7efb12e1e7acee9214fd22283", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-1.0.2.tgz", "integrity": "sha512-4RzCiLUcRMt/p0YUcyXIt51qmP6f+TG8wX4yThrK6UkOX3bfy9DE1VHWD9mr7mNgr1BtYTMJ6EweNwj24D546w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6vp839F9oADyHDyU4fcy91MSMD88C+KgbO9nGlmTDJAiAs5SGUCQqeN5MJOwM4uAB1KsCTLPf2fnWmVpsvt7IcOw=="}]}, "directories": {}}, "1.0.3": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "hen<PERSON>@andyet.net"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"phantomjs-prebuilt": "^2.1.16", "void-elements": "^1.0.0"}, "devDependencies": {"jshint": "^2.5.10", "phantomjs": "^1.9.13", "precommit-hook": "^1.0.7", "run-browser": "^2.0.1", "tap-spec": "^2.1.2", "tape": "^3.0.3", "zuul": "^1.16.5"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["html", "parse", "stringify", "ast"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"start": "run-browser test/*", "test": "zuul --phantom -- test/index.js", "test-ci": "zuul -- test/index.js"}, "gitHead": "d63d865e52b65672ae4f0d3a4922ca1b09932f6c", "_id": "html-parse-stringify@1.0.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-4zl0XSaJ+PZfTRWM2T3kRB/46iU/TqA/qj5ZZRfS6bea9cbri44M8GvDHscjPT2MKTXgzvE9zHIXatyX/LNMrw==", "shasum": "517d3c6818635781e544e83ed9245237601ea7c7", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-1.0.3.tgz", "fileCount": 14, "unpackedSize": 25356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtJLpCRA9TVsSAnZWagAA0+AQAIpq2udhHafxRU9kzIZ/\nghDsZ6duSV8TfKjLAOsFYGeZ69j8fqVm6BIkZq/hJ7bnsKD0BWHWGRgNuFfH\nQnMOeBGzieWV5CkhkgltldzEiRh5k4+WcJ9rgkBjO1m9QufrRwsl0MNk8ihS\nFJyfzM40+mEW45JEJZPdByUaMjr93tJZUzHODF+3MZCDxJBnf1QVkTmlplbL\nKBfzEVk02WwyqBtwGFQ5sCSDW6EyANPwOV+z+EH214h/7xwzh51QDCXKG+QG\nO+QzfQZseJf3dO2Jmu0WnjrwyfF38tmCtj1qky1oP2QVi6Ax/+8NJzolg+cQ\n8dmdZqnRYj7T4nM44JcEFUNa+m5WvT0oXYrxUPkxpO3e7sZZ0HNNpHSfKsBB\nCIZD2HRrd69VeDtnI+zjM9rwMb0kgwXaggL75cIiGUE6AR92TbYc8w31vDSr\nLK3SnpH50GSbLIySEOcIWwKUD3GV008iZQqR5PWJ/ydVcYzFLyohuF+aLiEV\n6GthzdKDqNMsnaqOFAbAgzc+XW45/hSKFC6DB46mO4EmjfKpRIIIMmMcIDqI\nPP5Cid1OpF91QEQekM3c4Dl0YNPqyoXi8An1P2Ms95xRgN04OgbjKddTWvco\nNZvIHgyYyZ1VLYqsVKj0gxvXjD6ldHc/7uXZ+LGhzPwOPNGI1fxjxrUtGsHa\nnQO2\r\n=9s4+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEJ5JV/t1HVwSqom9cvpVbHKGMAcQiOTSVBvYP/LvtoOAiBNMEs4x/yPywxL5Wq8mzllIA31a83UtdhSbG2QEIWqEw=="}]}, "maintainers": [{"email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com", "name": "frontmesh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hug<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "latentflip"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_1.0.3_1538560744603_0.4962335732610743"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "3a3ed62bf98381ac49de58afaad1515a857864cb", "_id": "html-parse-stringify@2.0.0", "_nodeVersion": "10.21.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-CW6e38VbOU/lRtPnPu2N0iAcFrM6RrNGO7L32Hrh0tVo698UuJweEdlLuCtPjoUOtoW1ojZdImIv+0SFlWmmPQ==", "shasum": "d461b53fce26acc3be07e0b98c355c003c4fc8d1", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-2.0.0.tgz", "fileCount": 10, "unpackedSize": 50963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCke7CRA9TVsSAnZWagAAA7AP/3+wzeVyP8/ckBNKjiNG\nkQxsgs3tz5adPKJD05hLCg3pzTuEiSg0z/RurWjgxynpaFNXE7h0VACjaaIS\nTcWRIVWvCNKchYGaTbL5DKywqRwnFD494wyuw3cEILzD3+H76QRLfG5rMo1I\nwiTgnIJNlYumj/6b81VuybpO84xdCUUwUOZRhAM+T0JFPL29cHHN/rvH0ivD\nDHZczcf3NXRU3Zt30XjpxfGzWVdYhBRSTfyidVjAUu/gxjWiWGsRkGssbCdH\ne+DL51NUeSoq6I+w2mrsd16sz6d3DHMi7vG60mn8tdKFXm3XrWgCSOwDEreA\nWoFgcGcQZTOdqUyXp/LLUlBCZjDJ9n1vZvSqhO9GBEfCqhzADMF0wa0gtrPa\nAtJF4le6tBLgRwpePEc2qF4QDJJvpYBrWLXUch+FnJDYo1ikE+zjaDoCnq2h\nDPBN4B3dJj/UaizAD/EYCnmY1z3sDj5jV+zDbtwIpe7z0P/JuqDYAeQWgDY0\nqa5HmllBWtrrcpQcYZrmhmMvqcyQ/WyBsM4TkboIyJbezlvCK1zTYaLdb8GG\nSCG4LQsMnFzWqxsnXUPxK/WRIR/yydPdGame9jh1Vh+xISOoDNgixpaLOUHf\nRl4RIxQIJfG6tVH9Ut9MxlkuPCvHNx5HckPz6tU1ER8By1/CfeIvlry2O/1Y\nytba\r\n=vCGq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAMs6g6kqpjVFJFzrKKeGybNezLQMzZJNz2Iez70SBvKAiBMMh8b7jXjlYKFAietH29oqCwYO7KVXuP3dxLUwexBTQ=="}]}, "maintainers": [{"email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com", "name": "frontmesh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hug<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "latentflip"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_2.0.0_1594509243056_0.44872002726626103"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "317b7f07bb0189074e39a05dd0eb06e85befff8c", "_id": "html-parse-stringify@2.0.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-eJBedD1fo2+TIMSiLFPOnrhK5dLExLQsmrrfmxCL8bBtSoH9kElg5lr3ekJMDoqLjSSPeT7vVQO07cRu28vp4w==", "shasum": "fe9e670d4d5ec104b1ec13a9dc4bb8e4bbec811f", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-2.0.1.tgz", "fileCount": 2, "unpackedSize": 7816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOT/pCRA9TVsSAnZWagAAVfsQAJbVFCPHNNjU//Ca5w7x\n3N0VfI+JqIRPGM7vHyLWcAq3YN1HtshILqQQh3gb/W/bzW4sTDWmLeQTuxQK\nQ3Y7BXk4h3r5IXzzSocvc8KjGEVLs7hG90Ae0DEMoyEAZwnBjOij6wX6d/SK\nBJsBsUpoo0rApmdyucv8M8RXHcAdx83J68WOdCHgi5gvduggYO6KgJkGlS9y\nMBCmGGzN/2OvE6nA557xrjGBuqx0QAF3bfcZaeIP3Q7KGWrxKAHOtKZoaocq\nPwN3aV34I+wDM89SBZcn3c10MJqmdCFoYQLx3siOcFNNBdmdzQMfbfCZRlrb\n+APuWaOs+76XnDYqW75BJnkh1H40BKmrgmQfeir+2/0SfmEaTWY+4hSE0kIC\nomYll9S3vQW4EuZFH5MVUQrCS8ziOFulliJsFFG/7NKb76AT/PRoOdMPIYyo\nghkNpQdH1VVr3PCSbHnl/OIn2RdO9vbPZSmZ29qOuBkV/o5zsRl8W69SyzPc\nLq35y1fvejKnWayeFPE7FTStBzfTKaglWgeDux9eBKIdKdtQxUrD5Swc5doR\nDLj/omLKJlHBoqWamad1EvxubXyQfQ0l99dYIFNlWjPuT2u5eizByfRXx/OD\nt0hiZhNV88LrIzdFW+h0quReKjyknF24tA4aZtBHLfowdLDNduBJNT3IbW3R\nPCnX\r\n=LJ2e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfkhDudNUuF6owSup8cgtGYxRyXdU0Igqxql3XZk71DgIgF6VWbZuE7ReG0ePKF34EtOkIWZNsr1luNzDb//vQTD4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_2.0.1_1614364649245_0.7141298806821161"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "2.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "99b7b602dd8a26b141ae2f2d43daaaadd03385c8", "_id": "html-parse-stringify@2.0.2", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-jcyzMGN3C/aO8fLf0Yjz6uBtP0diUNDG9g0rgVdE51mo8b2O3UOlar8Epc/Z7gE6jPGAvU41v9uE+g+R8izhtA==", "shasum": "0c27d83d7cb9f749654b6044bf82da8dcb34b882", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-2.0.2.tgz", "fileCount": 2, "unpackedSize": 8046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP6k6CRA9TVsSAnZWagAApBYP/3JSRM71xvGLogzQ8AS3\nEEDp6SoDgZ3XZjaPcNBJdjaf+ZK+sOURo//aI5Wn/w9vAy3y7bJCJJsFMcfM\nniVSF4FHyPvo7rvWRhJF5FuvzwNHCIMrjmluVmL+f9ZreIZC1wa8B2giLGyy\n0F0gSO9vGaW1cgIx8N5smL6HxwGCAWL9krrAbs/qAjs4lNmSnzeN6PIEF3YZ\nRsCNmtFJLSBw9UdKs0WOmdkIBrxJD4kHy3GwzC9jDM7SnJ/YmKbTVx0I5ZYX\nYkSuY1tMYL5AnZXSD+1VSVCSn36DYRLXk/g8wIIGmPKFLQL3Ts8VL3h7i/GP\nkBQ1wkN6TCcPFyLxsy65yk+oRhmsMnbcAVUFJdXi7BITi+GadHxU14C5PfZ5\nidPkEFaxQ+CFGJJzMlC3Iw5xmgOdpUWq21s1HNnbBsx64mDnZN5+/p91FP6C\n1OVtEobKaM/IReyvLak41CXETI6OxTU0wZvartQxxnXDECc7jQORserXKwmB\nY7Gx2g9GmDSZdPFvAQNrbllQaWvDj/bd5Ozm/g8dcR04ufV1RojGB6wVojZb\n8uwrVusbIsbyCA9GX1j9aFXznkAlDQuD9kmcf5540EHxBjSpar3ZM+/9UJay\nW0Wx1C6mxCjFF7ZGwgdrYzw+D6eJ/JZOO+/32BPrGSO8968/pm8qD8LGiUGV\navtT\r\n=guJa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDX/hgO8qWY5HgIBuMFUqod44d1Td9sghaUrRRoYgWVjwIgUN0W96xckp9aLlmppdD5MRUlsHLOEQkHjOZBlSHLvw4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_2.0.2_1614784825836_0.4971178549562496"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "2.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "prepublish": "npm run build", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "3bba8444c9142d61d44902c8f92b33c4ccb45699", "_id": "html-parse-stringify@2.0.3", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-a0Uj07o4HaLPZ46KaEyR3VDwBYIUEdH9dv/iHs2T5ZsD1zwdvfGBg2zhQclGtCF9MoZ5fUi3RIkWqM5VRO/uOw==", "shasum": "472257540e2865cb7217c5289cc1db9a2a2d31f3", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-2.0.3.tgz", "fileCount": 10, "unpackedSize": 51702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVQl5CRA9TVsSAnZWagAArCcP/Ap66AB5HQp299lgLBAu\njpDJFh6MysL9s2DBGBt6a4qw9OFW2eS1rzP9sWCXyZnpGhXVbPth+BWxtQfN\nWQs9ECh6tWfaCmUi3aYKnm+V6f/ogqMPadff5Qfs4wiD9THtZXGLDQ7dtMIt\n33No/s4o3xbP3QwBYaqfwG2dHScoZh/a3qHAWbOb6dxNkw9kMIBUy0cZpMFl\nljWJBa5alKh3+3UAu14BioztmROGJrIqn1qibA/dNp/9oL7SY6YryiQ2tMsv\n55CdB4u+FczmK89UJMvBiKDryZn3GKonkB6x0kK23NKds+H8yRQ4EZC/oagf\nxMKvDvY3+1jr1SM+c4JQjDSI5O+Y3EyePEzbdheXI30E+82QoJM7XsF0ejrb\n6HQrsLaRo9mQthTShBsHiiI4hJaEBiUkLZzuDpXytP3FmU256A1fsawOz0hy\ng/WEaei8ZaLdOI31SB85o9ha8mEu8UOzrEfM32cM+vMsuuK6TCmuEC+n0siU\n2kei6BsHNpoCXTwLcDMkyaPUKX9OiSTDpdFIw4fgohTQ5ZqXWyr4O6UK3jtS\nfUUd4Xs8Hvv4vxuwK3+K5op96NfZyhUifs5M1u1jqb5NHqXn1D9IJa2+psse\nLk0n1bDECJCKjvbvxux7Mzbw8d/pna8sQeAB7iMnlJw1F968gBRjzv9waHKz\nTDsO\r\n=gAop\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaIGnx+oRJnI7X/yUrrksE7nJ609FIgqg77U9sBT04WgIhAM5MTkiCj8QLo5jaufLwqHNsG2B74bGekC31PX6gPjLM"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_2.0.3_1616185720987_0.8395211516282541"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "prepublish": "npm run build", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "866742dfa227f2a6c355726343b2fe15d0177511", "_id": "html-parse-stringify@2.1.0", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-FM3A8wjIn3yfIApDTjykVcq0A/3EOrTPA+6rO59P32oVk4++AIW9CdZGFGs+sHXrXbsop07eG7pukNMC+qRKSQ==", "shasum": "e67b15e67e1c5574cb8e3c19b3fc225f3a8be129", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-2.1.0.tgz", "fileCount": 10, "unpackedSize": 51928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZgHmCRA9TVsSAnZWagAA/aQP/AlHLAouA/vO5wq7lE7v\n9bsyB1igAw8p7sLbEtArwthkHGKI892ToOsWsMTgPeBIs99TYm1pCuerxLV4\nxm97+jGpctM/I7FrLpkU6R2MCNHq1IO4SH9E23M9nKB9/6ONeXBQ40fIYrm4\n0DvQ6YTU9xvb/gPnntkXUkyaHQHt+LIHmDdu8XaHfiXwz0lY384e7Nkr45jx\nccM33sHKnh/qzdgwnyPKKMzLOGHPXHGybhoVs46/QarTFQpf9EsoJZy9RIvi\napEnFQ7Vo2jRmXQhBxzDwBJnI4e1VguIxVhhU41b1xKZLuhT2TzZ+wcwN2X8\nVZY8eZ8+KCBMRVl9Pyq7ayqwbKIJ7uT51CLKFnqKdBjpC9ZHndxPXRXJwvYC\nk7HztDoHH5e4PBanu8twwyv/iVLmMvEq7KOk+OIzIxi9ofdnPDKzCAUfpiEK\nXdB6zfWSntMAIZmNqmhyRNI8nHaCcHhvbO2Koh8RpIkGqEaf4TEFSWWMJXp3\nY6ouHkA8Uh4R5cdvi9v1bA12b6ALQAdE2AzOXnPO28wnrfAHn68hkwIsn7RG\nQdupJlNR1JrJoxedAfJ9aNvTCCdn4Xshf6MKx0BmmhSTH6QfMxv8nPnW0sEZ\n8nQdrHmoidRk2XR1xixYSDxVbKTr4V5azsGaxQ3n89v6kcqUIoc49XvzMtYv\nFPNu\r\n=l3iB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjZmnT3MWRJOGzxvsWTw83ryW18DaVPYFcNiiw9+J0IwIhAIWZoDvZfQi96Hz3ZyKaUP+XAOQGd0YCHgG9n6zDhBLE"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_2.1.0_1617297893617_0.6500755391797899"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "2.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "prepublish": "npm run build", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "86daf067115ac8080bdf1897ebbd8440e8267997", "_id": "html-parse-stringify@2.1.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-w+9Q9DdT1RoiM95SscK8c04IxHaf07RbORlMNvaXXOtLrCm3tm73P/cCbJoWNKRbLvmQfEDNjRhAu8yfWYsxxg==", "shasum": "2fbf3c2c6101b137bea826819380c4a6b6514d59", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-2.1.1.tgz", "fileCount": 10, "unpackedSize": 52692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZiVVCRA9TVsSAnZWagAATJ4P/ROQnTW1jfTrBmRaylah\n7Qla7fM8LwGoYr4yE/k46CbZB1Q3xpSwmkdPiJqNfNR1UGN0EwZQBF9h3mc4\n487W+Y092RqoRt8/Pse20TBts6P0s414FS1JSHw+9T4DvKt0AGWDXi6zM3HP\nahmUX/zWCeCtQzos4nHmg2og0gpkysmRtyKadMOKJSbDK2VBNOnuthbK5rHD\ntyzioqJ57vKO5QXVHdWz8LQxURNmjtc+9kObVm2ixyZ85wL59xttNKpb8MK7\n6EuTOjA1xcB0lyC7//FCtchN4qBt8VSL1PI2w2NzMFIVivHVLRMAGP2GsqBv\nJFotUu12qeGmSaT6Ac+NXRYXGjbuC0Q1tmHLQf1sz48pg3zLdokxLabp+G94\nQiCf7bgJ6RNbNnDjLXoxWc1LMbdnpM2QrRgQXf0/c/1rS1fDVGLE5fjLMa89\naiP5MOtXOqp5LBLOUcXD/GeX8cZ2tHo0bAeURkJC+WwXSRFZ8J7Kl9UT+hr2\n3lksrtCtSGPASqMEgjBw9Vz3t8GqsGPO18OqyKJ8J5YSvi1nWH+CQ9z9eWhk\nkF9kUWAbL8aImb+DYlEs3fRgWbhfYveKyWhk7eDxYXUaOPQramVlqPn0my1M\nFaHJOG71fUbgefZzwb0Ug/muksjShyVk53fc3PEi1ljbYfybzrYl0u/XgZxG\nrcSO\r\n=oZvB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgbUweVU4g8cJOAtqGsxYWr7/7dE+artTNa3I2fj177wIgNXM097ioeM+W5weaxSd0qLevlOTEOgkU/PjViXCZ5i0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_2.1.1_1617306965001_0.869567194997972"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "3.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "prepublish": "npm run build", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "d1f2a0f83f82e6978d304af26376ce3d5315df34", "_id": "html-parse-stringify@3.0.0", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-TrTKp/U0tACrpqalte/VhxepqMLii2mOfC8iuOt4+VA7Zdi6BUKKqNJvEsO17Cr3T3E7PpqLe3NdLII6bcYJgg==", "shasum": "28f10753e190af211d2c3c9a1191a4502e112c96", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.0.tgz", "fileCount": 10, "unpackedSize": 55577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdEwHCRA9TVsSAnZWagAA6SUP/36NS2OwL6wy+ueaJsGX\nKDy21XudyiqxYE+LETvtViKHJ/ywg7CxbONXNqvkgbpGNvu3UELWIGQjLUQQ\n7nr4oKNm/tV3UU5JlRbI0NDx6up677vNgLReOSZKhSHkC/yGcRqm53+etqL6\nyWzHFBnBdvikzj+EmRvj+nnnJvpjHfIZp6kKAgzP84ettkEmdltZGh9QcL1Z\nQZTD9FI5WtTCCZa2I0UQsAfI2MP65A0oReAZU2C/wl9cgB5Zmd7yTGu1cmr7\ndn1L84SnAhXT1h1qM3qG53LBDekQ3xr5bKS2eocMTo3MU8j/krPPQE0C4IZ0\nXS+djmVXv7W0l4PUmYK2Iel3eTzHa2NgosNFMMzvkdEwxfnAl0HRSA9RNUqm\nn3o4LCBFKUZg/oiA4LJ196nHYbi8BMJKwn1NDm76VcJoRXbmq2woSs1T7gxm\n1zEEKbtcS5uMTpDyHb280gDiK5siQv9V6BQwyVLotpPm5Jhhp8si7H7wP9ry\nyte+/TTZq2BH4PkeRffW1wLR81p3OYASeQae0WE7mi4gXFOVZAsC0L9ax1RQ\nDLl1C8hiYGvRCEY40dkC/p94LvZ8w4zZCn7cJIuOOKql92yeWvlTZktolRFv\n4PyWN6wWmFz/4M3WxFMzr9+7RE3WvSAg5qwWuLrPz1mFB8M+30CtJgzuNzen\nU084\r\n=6eWf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCagoujJv2Yv1Pr6r3JY2wqu2TrgfBCSr48VpnDGLbpPwIhANTPhNlN0Xk6Mx4oJ8BxUyGlpJ7Z8PZRyersK36Gdip7"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_3.0.0_1618234375254_0.4943165182850202"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "html-parse-stringify", "description": "Parses well-formed HTML (meaning all tags closed) into an AST and back. quickly.", "version": "3.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "dependencies": {"void-elements": "3.1.0"}, "devDependencies": {"esm": "3.2.25", "microbundle": "0.12.2", "prettier": "2.0.5", "tap-spec": "2.1.2", "tape": "5.0.1"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "license": "MIT", "main": "dist/html-parse-stringify.js", "module": "dist/html-parse-stringify.module.js", "source": "src/index.js", "unpkg": "dist/html-parse-stringify.umd.js", "prettier": {"arrowParens": "avoid", "singleQuote": true, "semi": false}, "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "scripts": {"build": "microbundle", "format": "prettier --write .", "prebuild": "rm -rf dist", "prepublish": "npm run build", "test": "tape -r esm test/* | tap-spec"}, "gitHead": "ce46022f537ef9b050fac592f9fcc30bf838e5ba", "_id": "html-parse-stringify@3.0.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==", "shasum": "dfc1017347ce9f77c8141a507f233040c59c55d2", "tarball": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz", "fileCount": 10, "unpackedSize": 55524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiOzJCRA9TVsSAnZWagAAhwUP/3HsooDtcQgrh45kOQLN\n5DAPPy02lz7gXXwSJsgcvheFGK2oWNhTk8v3l8plLLpU2y1qF30bvCPw2Wl9\nvpzPsZ6hDBf/5SCc3d/cu7aX1jx8oqz/vXxFo9LYEEorC7C30cfuNZTBTBCM\ndH49ZVu6C10+v92ajUEubaDpLbGUQoUb18vM7Ys9hsfibVvpQag4w4CyNjQP\nZX+6mCFpk1MamYZ+Y9N08yypwNn61y/tz2VQSXSMm6Yx3bFrk9NzA3SzakMG\nCYkAJzDC5xgUI2CGumfzZ8e/UBPLxHRKaLJHZWrFLQAena7GZTSfG6fmhw1Q\npGV3lAiZhWUQ5zBsal2adfc+qDa6yxMYPJ9ne6HJb24EnHVF2efQx6CBtAE0\nPX8nkssvkA6cWnLSGABFbyD85LEar81iQW7my5WgLiOIrzlawJyvk5WzJe3g\nSDpAqBqppkHCY9liDYZD86uECRV16KN+RsYdKEeyaF3k1zSnAyYkuMOO50KE\nsSaNBNSZakiDgOV8K1xtRXRp+jdmRadQZQh8JExan368j8lgafZb87XkSAYu\nN0wSZahiWlylSqwD/6+gqNc3dQWCUugsvX4cPQsQ5mrSkCJUYkHe+Co0+vPg\nhcnOtAvllQltC2NKAko1lRt/GVKCsq3OYTs112oGCg4hLqANw0llkfTpe14Q\ngGGG\r\n=rvQQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHQdYfcJ1F7QfzG/gprhwVjzxpMNQl3BoY7y54UM0UyMAiAh7ZqZd1qmJcUsBHq3KivKN0uAjbOLKKsyv3YN+DJ2eQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hug<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "latentflip", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "frontmesh", "email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-parse-stringify_3.0.1_1619586249049_0.2594953876836754"}, "_hasShrinkwrap": false}}, "readme": "# html-parse-stringify\n\nThis is an _experimental lightweight approach_ to enable quickly parsing HTML into an AST and stringify'ing it back to the original string.\n\nAs it turns out, if you can make a the simplifying assumptions about HTML that all tags must be closed or self-closing. Which is OK for _this_ particular application. You can write a super light/fast parser in JS with regex.\n\n\"Why on earth would you do this?! Haven't you read: http://stackoverflow.com/questions/1732348/regex-match-open-tags-except-xhtml-self-contained-tags ?!?!\"\n\nWhy yes, yes I have :)\n\nBut the truth is. If you _could_ do this in a whopping grand total of ~600 bytes (min+gzip) as this repo shows. It potentially enables DOM diffing based on a HTML strings to be super light and fast in a browser. What is that you say? DOM-diffing?\n\nYes.\n\nReact.js essentially pioneered the approach. With React you render to a \"virtual DOM\" whenever you want to, and the virtual DOM can then diff against the real DOM (or the last virtual DOM) and then turn that diff into whatever transformations are necessary to get the _real_ DOM to match what you rendered as efficiently as possible.\n\nAs a result, when you're building a single page app, you don't have to worry so much about bindings. Instead, you simple re-render to the virtual DOM whenever you know something's changed. All of a sudden being able to have `change` events for individual properties becomes less important, instead you can just reference those values in your template whenever you think something changed.\n\nCool idea, right?!\n\n## So why this?\n\nWell, there are other things React expects me to do if I use it that I don't like. Such as the custom templating and syntax you have to use.\n\nIf, hypothetically, you could instead diff an HTML string (generated by _whatever_ templating language of your choice) against the DOM, then you'd get the same benefit, sans React's impositions.\n\nThis may all turn out to be a bad idea altogether, but initial results seem promising when paired with [virtual-dom](https://github.com/Matt-Esch/virtual-dom).\n\nBut you can't just diff HTML strings, as simple strings, very easily, in order to diff two HTML node trees you have to first turn that string into a tree structure of some sort. Typically, the thing you generate from parsing something like this is called an AST (abstract syntax tree).\n\nThis lib does exactly that.\n\nIt has two methods:\n\n1. parse\n2. stringify\n\n## `.parse(htmlString, options)`\n\nTakes a string of HTML and turns it into an AST, the only option you can currently pass is an object of registered `components` whose children will be ignored when generating the AST.\n\n## `.stringify(AST)`\n\nTakes an AST and turns it back into a string of HTML.\n\n## What does the AST look like?\n\nSee comments in the following example:\n\n```js\nvar HTML = require('html-parse-stringify')\n\n// this html:\nvar html = '<div class=\"oh\"><p>hi</p></div>'\n\n// becomes this AST:\nvar ast = HTML.parse(html)\n\nconsole.log(ast)\n/*\n{\n    // can be `tag`, `text` or `component`\n    type: 'tag',\n\n    // name of tag if relevant\n    name: 'div',\n    \n    // parsed attribute object\n    attrs: {\n        class: 'oh'\n    },\n\n    // whether this is a self-closing tag\n    // such as <img/>\n    voidElement: false,\n\n    // an array of child nodes\n    // we see the same structure\n    // repeated in each of these\n    children: [\n        {\n            type: 'tag',\n            name: 'p',\n            attrs: {},\n            voidElement: false,\n            children: [\n                // this is a text node\n                // it also has a `type`\n                // but nothing other than\n                // a `content` containing\n                // its text.\n                {\n                    type: 'text',\n                    content: 'hi'\n                }\n            ]\n        }\n    ]\n}\n*/\n```\n\n## the AST node types\n\n### 1. tag\n\nproperties:\n\n- `type` - will always be `tag` for this type of node\n- `name` - tag name, such as 'div'\n- `attrs` - an object of key/value pairs. If an attribute has multiple space-separated items such as classes, they'll still be in a single string, for example: `class: \"class1 class2\"`\n- `voidElement` - `true` or `false`. Whether this tag is a known void element as defined by [spec](http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements).\n- `children` - array of child nodes. Note that any continuous string of text is a text node child, see below.\n\n### 2. text\n\nproperties:\n\n- `type` - will always be `text` for this type of node\n- `content` - text content of the node\n\n### 3. component\n\nIf you pass an object of `components` as part of the `options` object passed as the second argument to `.parse()` then the AST won't keep parsing that branch of the DOM tree when it one of those registered components.\n\nThis is so that it's possible to ignore sections of the tree that you may want to handle by another \"subview\" in your application that handles it's own DOM diffing.\n\nproperties:\n\n- `type` - will always be `component` for this type of node\n- `name` - tag name, such as 'div'\n- `attrs` - an object of key/value pairs. If an attribute has multiple space-separated items such as classes, they'll still be in a single string, for example: `class: \"class1 class2\"`\n- `voidElement` - `true` or `false`. Whether this tag is a known void element as defined by [spec](http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements).\n- `children` - it will still have a `children` array, but it will always be empty.\n\n## changelog\n\n- `3.0.1` Merged #47 which makes void elements check case insensitive. Thanks again, [@adrai](https://github.com/adrai) for this contribution!\n- `3.0.0` Merged #46 which fixed an issue with handling of whitespace. Doing major version bump since this changes behavior if you have whitespace only nodes (see merged PR and #45 for more details). Thanks [@adrai](https://github.com/adrai) for this contribution!\n- `2.1.1` Merged #41 which fixed an issue with tag nesting. Thanks [@ericponto](https://github.com/ericponto).\n- `2.1.0` Merged support for numeric tags. This allows a use case described in [this PR](https://github.com/HenrikJoreteg/html-parse-stringify/pull/43). Thanks [@kachkaev](https://github.com/kachkaev).\n- `2.0.3` Fixed failed publish. Accidentally published an empty package :sweat_smile:\n- `2.0.2` Fixed incorrect attribution for vulnerability disclosure. The vulnerability was discovered by Yeting Li. Sam Sanoop was the one who reached out to me about it.\n- `2.0.1` Addressing a reported regular expression denial of service issue found by [Yeting Li](https://github.com/yetingli) and reported to me by [Sam Sanoop](https://twitter.com/snoopysecurity) of [Snyk](https://snyk.io/) THANK YOU!. The issue was that sending certain input would cause one of the regular expressions we used to lock up and not finish, freezing the process. See the test that was added for details. To be clear, this lib wasn't meant for parsing non-well formed HTML. But, better safe than sorry! So we're fixing it.\n- `2.0.0` updated to more modern dependencies/build system. Switched to prettier, etc. No big feature differences, just new build system/project structure. Added support for top level text nodes thanks to @jperl. Added support for comments thanks to @pconerly.\n- `1.0.0 - 1.0.3` no big changes, bug fixes and speed improvements.\n\n## credits\n\nIf this sounds interesting you should probably follow [@HenrikJoreteg](https://twitter.com/henrikjoreteg) and [@Philip_Roberts](https://twitter.com/philip_roberts) on twitter to see how this all turns out.\n\n## license\n\nMIT\n", "maintainers": [{"email": "<EMAIL>", "name": "hug<PERSON><PERSON><PERSON>"}, {"email": "ad<PERSON><EMAIL>", "name": "adrai"}, {"email": "<EMAIL>", "name": "latentflip"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "vlad<PERSON><PERSON><PERSON><PERSON>@gmail.com", "name": "frontmesh"}], "time": {"modified": "2022-06-18T21:55:06.488Z", "created": "2014-12-12T06:18:42.808Z", "1.0.0": "2014-12-12T06:18:42.808Z", "1.0.1": "2014-12-12T06:44:21.780Z", "1.0.2": "2015-03-17T04:50:55.973Z", "1.0.3": "2018-10-03T09:59:04.735Z", "2.0.0": "2020-07-11T23:14:03.202Z", "2.0.1": "2021-02-26T18:37:29.366Z", "2.0.2": "2021-03-03T15:20:25.964Z", "2.0.3": "2021-03-19T20:28:41.126Z", "2.1.0": "2021-04-01T17:24:53.757Z", "2.1.1": "2021-04-01T19:56:05.230Z", "3.0.0": "2021-04-12T13:32:55.387Z", "3.0.1": "2021-04-28T05:04:09.194Z"}, "homepage": "https://github.com/henrikjoreteg/html-parse-stringify", "keywords": ["ast", "html", "parse", "stringify"], "repository": {"type": "git", "url": "git+https://github.com/henrikjoreteg/html-parse-stringify.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/henrikjoreteg/html-parse-stringify/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"j.su": true, "xueboren": true}}