{"_id": "siginfo", "_rev": "5-086057908cb7a8d91f6a0ea88fd17233", "name": "siginfo", "description": "Utility module to print pretty messages on SIGINFO/SIGUSR1", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "siginfo", "version": "1.0.0", "description": "Utility module to print pretty messages on SIGINFO/SIGUSR1", "main": "index.js", "dependencies": {"standard": "^10.0.3"}, "devDependencies": {}, "scripts": {"test": "standard"}, "repository": {"type": "git", "url": "git+https://github.com/emilbayes/siginfo.git"}, "keywords": ["siginfo", "sigusr1", "ctrl", "t", "info", "progress", "inspect"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/emilbayes/siginfo/issues"}, "homepage": "https://github.com/emilbayes/siginfo#readme", "gitHead": "63bfe91c061fe830ad737fa07005718b05edfdfb", "_id": "siginfo@1.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "emilbayes", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GnjDnvzwMPly28bGOUccsI7n8Gw7lB4DQmz0WDpN3fTh4e7jnw46QipGM3kS8/YN48wGtbAHYbzW18sXv4YMKw==", "shasum": "629471a40c0c926d6090119faed6dbb276aa1853", "tarball": "https://registry.npmjs.org/siginfo/-/siginfo-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrGkzGved6G7VqZC1KLbdFQPlS9RIqqC066CZ+ZO5jngIhAIIp4Fqwqdlgaasa5V6rkA0VIRdomBR1zuUhFRCtlO4/"}]}, "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/siginfo-1.0.0.tgz_1507010502106_0.7428120442200452"}, "directories": {}}, "1.0.1": {"name": "siginfo", "version": "1.0.1", "description": "Utility module to print pretty messages on SIGINFO/SIGUSR1", "main": "index.js", "dependencies": {"standard": "^10.0.3"}, "devDependencies": {}, "scripts": {"test": "standard"}, "repository": {"type": "git", "url": "git+https://github.com/emilbayes/siginfo.git"}, "keywords": ["siginfo", "sigusr1", "ctrl", "t", "info", "progress", "inspect"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/emilbayes/siginfo/issues"}, "homepage": "https://github.com/emilbayes/siginfo#readme", "gitHead": "9009514e0c1d2ddebf902566f5cb732cda067bed", "_id": "siginfo@1.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "emilbayes", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PqfTBOuaL3Snp+ElGxgL5ZxOa9ofSW63djksmwRoZps7G1ZDRn49ZiKBXLxqFArH/9Cz8jORwGQzNaRYW8ETPw==", "shasum": "656269ae87ebe97fb759757ce9aee4ee33065063", "tarball": "https://registry.npmjs.org/siginfo/-/siginfo-1.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlpDV1ppv1ofqDm+0c3ZIDZ/ljkC6cP+Go/+BEJZFT0AIgNytz6uW/MU8X7MTwggaAMFOLfSc97GARCXN55AIfqFE="}]}, "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/siginfo-1.0.1.tgz_1507010539352_0.6699091654736549"}, "directories": {}}, "1.0.2": {"name": "siginfo", "version": "1.0.2", "description": "Utility module to print pretty messages on SIGINFO/SIGUSR1", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^10.0.3"}, "scripts": {"test": "standard"}, "repository": {"type": "git", "url": "git+https://github.com/emilbayes/siginfo.git"}, "keywords": ["siginfo", "sigusr1", "ctrl", "t", "info", "progress", "inspect"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/emilbayes/siginfo/issues"}, "homepage": "https://github.com/emilbayes/siginfo#readme", "gitHead": "5ccb6c27411f1f0c4da498fce6410e53fbe5c8fc", "_id": "siginfo@1.0.2", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "emilbayes", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OQ2lq7sIf+OWdit8+N2QyBbKj2PH0kUsHpIdKH3nhG62t3G5LOof1MNrc/z30TTB+XNvDh5grC3f8XGo5W54ng==", "shasum": "ea4652cfd78978acf2be88c6d62e9dbe741125d3", "tarball": "https://registry.npmjs.org/siginfo/-/siginfo-1.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMlWrcWNDuhHFqGk6iKLxxS3bjbnUYT+1C823AeiSIcQIgK8l9/gD77bayjro99uLwKCvc9p8adSCPeyxxQgWxKC0="}]}, "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/siginfo-1.0.2.tgz_1511271703525_0.4088845541700721"}, "directories": {}}, "2.0.0": {"name": "siginfo", "version": "2.0.0", "description": "Utility module to print pretty messages on SIGINFO/SIGUSR1", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^14.3.4"}, "scripts": {"test": "standard"}, "repository": {"type": "git", "url": "git+https://github.com/emilbayes/siginfo.git"}, "keywords": ["siginfo", "sigusr1", "ctrl", "t", "info", "progress", "inspect"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/emilbayes/siginfo/issues"}, "homepage": "https://github.com/emilbayes/siginfo#readme", "gitHead": "d2a7721a36967f4e477ee6161a0331837aa41b91", "_id": "siginfo@2.0.0", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==", "shasum": "32e76c70b79724e3bb567cb9d543eb858ccfaf30", "tarball": "https://registry.npmjs.org/siginfo/-/siginfo-2.0.0.tgz", "fileCount": 6, "unpackedSize": 4789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6SvfCRA9TVsSAnZWagAAShUP/j8ni2VNpgjxq2q3Ry86\nk9QnS641l0BU+jHsVdypqczmudXNNfP+iPk4Yy3LaaOxq40tRNbOzWAX7LVW\nxJooO74uCBBTONsRPCHAawLD4yUGbugPFnypCysJilrfggJk/X4xoLw9jshS\nF1gYfXe7APUrlg5OF1+NdCv0723O9k6/9mc8vzVDNnNFL1rdWorIO0dt2Qk8\n9d4JD0XmzgLVJXBVpJ19PTNNLrBgC6AvJs3+4dMttPVVr2ivL3Yvo2z3HowV\nRkLlORmiUmpEGCXhRHeuN7NMqHEgB0Lo8vUI0DXH13/CQeYfcPX8+U19Fyny\nDiTOPyIAD+KjGPERqMu7Fc7rgSkvABNIbFj9RWQkeEbtha9kHRBYccq8wgEG\nTwCDnByafXqbSwe4u5YRxMydb0G19MgJZS2gI18K0hHx/YA41afSGBE4jgjC\nKNbvhc4thSFxPTTVNzDjYFWfTVlFEyDYTFFL097+iaySEocnnscYbBY0W0bC\np/iT4Vueohr4l8E7t8Xxe0ja6vTPA+QSiubiIRFAQE0qba99qbX2+udEqQPx\n5sxbQ1Upt8uzA0sBJqft6Umr7aDGHFw3H5+LQAoC+4wmblE92AhY9cyYwLpp\nCVdQrYdWBav3YhZkMFRkS3xiuD46AlKphopq//F2KNifVqT6L4qCULzRcXVj\nD5Mj\r\n=Ly1n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIESx1jaeRNXU6Brv4TJdgVLxQzwhxHiLBo8uRf5LsCWXAiBgYUBFGNIpFCLsgmEgVOkTjRw2htQtdMuzOPoyWMH71g=="}]}, "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}], "_npmUser": {"name": "emilbayes", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/siginfo_2.0.0_1592339423404_0.8940020086292513"}, "_hasShrinkwrap": false}}, "readme": "# `siginfo`\n\n[![Build Status](https://travis-ci.org/emilbayes/siginfo.svg?branch=master)](https://travis-ci.org/eemilbayes/siginfo)\n\n> Utility module to print pretty messages on SIGINFO/SIGUSR1\n\n`SIGINFO` on BSD / macOS and `SIGUSR1` on Linux, usually triggered by\n`Ctrl + T`, are by convention used to print information about\na long running process internal state. Eg. `dd` will tell you how many blocks it\nhas written and at what speed, while `xz` will tell you progress, compression\nratio and estimated time remaining.\n\nThis module wraps both signals, checks if the process is connected to TTY and\nlets you do whatever you want.\n\n## Usage\n\n```js\nvar siginfo = require('siginfo')\nvar pkg = require('./package.json')\n\nsiginfo(function () {\n  console.dir({\n    version: pkg.version,\n    uptime: process.uptime()\n  })\n})\n\n```\n\n## API\n\n### `var removeListener = siginfo(queryFn, [force])`\n\n`queryFn` can be used for whatever you want (logging, sending a UDP message, etc.).\nSetting `force = true` will attach the event handlers whether a TTY is present\nor not.\n\n## Install\n\n```sh\nnpm install siginfo\n```\n\n## License\n\n[ISC](LICENSE)\n", "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}], "time": {"modified": "2022-05-17T19:31:42.589Z", "created": "2017-10-03T06:01:43.045Z", "1.0.0": "2017-10-03T06:01:43.045Z", "1.0.1": "2017-10-03T06:02:20.448Z", "1.0.2": "2017-11-21T13:41:45.181Z", "2.0.0": "2020-06-16T20:30:23.515Z"}, "homepage": "https://github.com/emilbayes/siginfo#readme", "keywords": ["siginfo", "sigusr1", "ctrl", "t", "info", "progress", "inspect"], "repository": {"type": "git", "url": "git+https://github.com/emilbayes/siginfo.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/emilbayes/siginfo/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"emilbayes": true}}