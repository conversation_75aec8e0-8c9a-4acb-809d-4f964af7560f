{"_id": "cliui", "_rev": "49-d1fdfa98803e021f0de9131222799bd5", "name": "cliui", "dist-tags": {"latest": "9.0.1"}, "versions": {"1.0.0": {"name": "cliui", "version": "1.0.0", "keywords": ["cli", "command-line", "layout", "design"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "dist": {"shasum": "7627be516b8c95408f2074b17de7b518c8d55316", "tarball": "https://registry.npmjs.org/cliui/-/cliui-1.0.0.tgz", "integrity": "sha512-og77v9S8mlRlUhnRePL6mC+Ie4ImAPbZZ34ZDk2tGsXxGJnMW/jiOhIZCksS4Y/4LJhYTlHOWUGmVcfy2YRcng==", "signatures": [{"sig": "MEUCIBD2QtLAuzFkPKEcAG1NI+ebEoEWfo6dh61miRr/3DiMAiEA/aBrDjPezUaGPpQZzlVR3BmTBBsDrijRKgxQ3Tk9KHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "7627be516b8c95408f2074b17de7b518c8d55316", "gitHead": "f1b8e08186e8f58286b0784ec0b4706709f4597f", "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "_npmVersion": "2.7.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"wordwrap": "0.0.2", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4", "blanket": "^1.1.6", "standard": "^3.6.1", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1"}}, "1.1.0": {"name": "cliui", "version": "1.1.0", "keywords": ["cli", "command-line", "layout", "design"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "dist": {"shasum": "69da6cd1d14e5142f5d8066a22de433ae268caf3", "tarball": "https://registry.npmjs.org/cliui/-/cliui-1.1.0.tgz", "integrity": "sha512-qLkPJVBCnUjJz9UaixLK07qTmIw1MV/d1p8dciugHccuer3P3lWQE7OWExfuPxgHGtQGX8HmAJcab4TRatF6yg==", "signatures": [{"sig": "MEUCIQDU5LniFjrxcUJRYRLe2e2mVIISIruonZ6+psO1aLif2wIgBHFpxSL2M0b9pYwCAy9be1hl7qE6cTDqDAjf+bdsmII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "69da6cd1d14e5142f5d8066a22de433ae268caf3", "gitHead": "77ca2a00cfbe71440d2bc6357f5e0ae12f32fb86", "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "_npmVersion": "2.7.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"wordwrap": "0.0.2", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4", "blanket": "^1.1.6", "standard": "^3.6.1", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1"}}, "1.3.0": {"name": "cliui", "version": "1.3.0", "keywords": ["cli", "command-line", "layout", "design"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@1.3.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "4d5d2838ef9d491ff01739c6612b6e2b8f435abd", "tarball": "https://registry.npmjs.org/cliui/-/cliui-1.3.0.tgz", "integrity": "sha512-9FmdHF7nRlIF23DAyUcGVo2a0w9ngQNk+y57gV/L+vDmeegtwpLyPTPq7P1RqfK6OfrWC7tqF49eevCPCUtrUA==", "signatures": [{"sig": "MEUCIQDHWmYsgSNiLVuZNElqn7f1kp3nSidrwqh/SKQtrfwoHwIgcUVyOOTfTFjN6BJpyxGZvNnHqE3kvBJKzfDs4Z3DAW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "4d5d2838ef9d491ff01739c6612b6e2b8f435abd", "gitHead": "e5b866b98c6816a16ef62ffc09078da95becfd5a", "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "http://github.com/bcoe/cliui.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"wordwrap": "0.0.2", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4", "blanket": "^1.1.6", "standard": "^3.6.1", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1"}}, "1.4.0": {"name": "cliui", "version": "1.4.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@1.4.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "073126b5b0eecea92ea71eea4e6ecf376db60c3e", "tarball": "https://registry.npmjs.org/cliui/-/cliui-1.4.0.tgz", "integrity": "sha512-fmlNEdn+tNoExnsAEECfCINie0IfjtOqUJShueuos0CIr3SrF3qcrXgFE5hbNWEyLztZAvbfXqlXHKSXBdLkzw==", "signatures": [{"sig": "MEYCIQCeMsf2yvC6dRphdcCQ+CJsb+dFIzK8G3rRui6fLeZazwIhALOJ69OZYWaL/B2rjEg3K7R8FM4WTBbcWwFLYQU9ZMBM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "073126b5b0eecea92ea71eea4e6ecf376db60c3e", "gitHead": "0085cbfa61e04b4833f15ea2ba1d64021be2b2b2", "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "http://github.com/bcoe/cliui.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"wordwrap": "0.0.2", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4", "blanket": "^1.1.6", "standard": "^3.6.1", "coveralls": "^2.11.2", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1", "mocha-lcov-reporter": "0.0.2"}}, "2.0.0": {"name": "cliui", "version": "2.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "58f2f6b8bee87285a62fb9c8a9d6885670f730d6", "tarball": "https://registry.npmjs.org/cliui/-/cliui-2.0.0.tgz", "integrity": "sha512-U1+CGz8w1k1CwIyL4fuI7GX1seBVBB0z/zgi/YMx/jkz45NXJZyNRgLkAWQQyXj9Y2Pb7VmNT+miOrdrkUilqg==", "signatures": [{"sig": "MEQCIBaagsuN9gd9VzYg9FqqinMgWvIrFnU1ahGSP65US4LlAiBfHZGe0YQZY+NCbQ8ClMLnPxmsI8JoJ5yoftGmLYFneQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "58f2f6b8bee87285a62fb9c8a9d6885670f730d6", "gitHead": "a306c24b12a345320b37339e9ccc7a36bc4bdde5", "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "http://github.com/bcoe/cliui.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"wordwrap": "0.0.2", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4", "blanket": "^1.1.6", "standard": "^3.6.1", "coveralls": "^2.11.2", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1", "mocha-lcov-reporter": "0.0.2"}}, "2.1.0": {"name": "cliui", "version": "2.1.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@2.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "4b475760ff80264c762c3a1719032e91c7fea0d1", "tarball": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "integrity": "sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==", "signatures": [{"sig": "MEUCIBQekXuEdzEmUn6qoKCPuMaBAmtHhKwUnzsfd/l/kcFSAiEAh+zWkLFJk2wKm3AYRP5Z1EoTbbmZB2a3itIBQsJ24hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "4b475760ff80264c762c3a1719032e91c7fea0d1", "gitHead": "5d6ce466b144db62abefc4b2252c8aa70a741695", "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "http://github.com/bcoe/cliui.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"wordwrap": "0.0.2", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.4", "blanket": "^1.1.6", "standard": "^3.6.1", "coveralls": "^2.11.2", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1", "mocha-lcov-reporter": "0.0.2"}}, "3.0.0": {"name": "cliui", "version": "3.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui#readme", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "7603633d8ae7f54a524d49cb0aabe26ab8c3100f", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.0.0.tgz", "integrity": "sha512-N7dR5zF5Dt7BSUZk4uaajJSczUowAjoa8aj0k07b2ujLTMF9MGIU7xZlwB8Dv/P31dK0Spc7FpnPMLuzn/jUaA==", "signatures": [{"sig": "MEUCIHSYcMWIFUxV39xXBLYdIfuUIQmNFyxab8lKYdRi09D+AiEAwpUJxV/nh0x94+sr0Yo2Xm8wq7Di8QeT+JDOlqJUK3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "7603633d8ae7f54a524d49cb0aabe26ab8c3100f", "gitHead": "5eac0ae5d4bceaa1906a1a226bb1599f925d587f", "scripts": {"test": "standard && nyc mocha", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/bcoe/cliui.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {"wrap-ansi": "^1.0.0", "right-align": "^0.1.1", "center-align": "^0.1.1"}, "devDependencies": {"nyc": "^3.2.2", "chai": "^3.3.0", "mocha": "^2.3.3", "standard": "^5.3.1", "coveralls": "^2.11.4"}}, "3.0.1": {"name": "cliui", "version": "3.0.1", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui#readme", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "b986163d7612ce20eb0b44af41807008af74f5e5", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.0.1.tgz", "integrity": "sha512-QlaHJ4h387PNj85GIhfAXhV+bPou/jJJYt4/QriqNQydLeQbFP3oDJ/t/GrVvdFtqKfw/Z9WpAOy2REY+jqTZA==", "signatures": [{"sig": "MEQCIHVMd4MBc2/td85/JV9oIHBqwrxcAhfnzKKYwGke9M6JAiBCDDkHDE2VCworTWVVXNH8FDrtT/VMo/bSu0LsPOlCCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "b986163d7612ce20eb0b44af41807008af74f5e5", "gitHead": "81412395a61a88d02022dcc1c649b7b07483f036", "scripts": {"test": "standard && nyc mocha", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/bcoe/cliui.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {"wrap-ansi": "^1.0.0", "right-align": "^0.1.1", "center-align": "^0.1.1", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^3.2.2", "chai": "^3.3.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "standard": "^5.3.1", "coveralls": "^2.11.4", "strip-ansi": "^3.0.0"}}, "3.0.2": {"name": "cliui", "version": "3.0.2", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui#readme", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "221d5871727c4db19440ddcfabc7a0a3ed1c16a6", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.0.2.tgz", "integrity": "sha512-QlE4/y98F41XsTxYMeve6J2h16wZeNKpOPAl0KNAdkr3pa65DG7Pg/20qyGVi5zHaR9Jr1P8z+/PUxlG8Js7AA==", "signatures": [{"sig": "MEUCICb4HVrTeuagnOKxS8fZypFvK9CPl/O3fhXQAUtIpq/aAiEAoU7dqadG5/e0oTtrR8CSwCLixEvavdhBz065TNVHl2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "221d5871727c4db19440ddcfabc7a0a3ed1c16a6", "gitHead": "17df90845f94623c05aa10ce7e20cf42efcb8d16", "scripts": {"test": "standard && nyc mocha", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/bcoe/cliui.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {"wrap-ansi": "^1.0.0", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^3.2.2", "chai": "^3.3.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "standard": "^5.3.1", "coveralls": "^2.11.4", "strip-ansi": "^3.0.0"}}, "3.0.3": {"name": "cliui", "version": "3.0.3", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui#readme", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "476de5e3442c921b165bcf8556aee9c30e7e144e", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.0.3.tgz", "integrity": "sha512-eRpRP1iDJm3hH4Uc6YWUvd5ObjAE1t2dMCVCzETh/Lb9lN8YM1ASc5qCl2c77Sz3BackWckKTsCZSCBv4FfnRQ==", "signatures": [{"sig": "MEQCID8tbdRpgoTqgceecHdMeUVCfShs6+VNDRW0VO0qmUs8AiBy8g6fODFe5I+QNQEUhnb8owo7Ll6qTx0lgdy3U+/19A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "476de5e3442c921b165bcf8556aee9c30e7e144e", "gitHead": "c7baa8a9096b11263aaadde7fda2cbc1f1d5683e", "scripts": {"test": "standard && nyc mocha", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/bcoe/cliui.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {"wrap-ansi": "^1.0.0", "strip-ansi": "^3.0.0", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^3.2.2", "chai": "^3.3.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "standard": "^5.3.1", "coveralls": "^2.11.4"}}, "3.1.0": {"name": "cliui", "version": "3.1.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/cliui", "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "dist": {"shasum": "a3b880308e0a8191721e0081a8c2a8b506d0abf9", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.1.0.tgz", "integrity": "sha512-fQrEUL1SEndbiuQzKAPwx3oNYXTCMMbtpBtPiWG7hw0UPP5W535TcxeWlI/ViW9sm5sE9cahzWd8BBCYnSZWJw==", "signatures": [{"sig": "MEUCIHkRNrZAfwY0c9DK4KRaUwPe7Q8lrq7mGkh0ny7cn0c6AiEAwQqTOgYyxsvts16DayONdpTZFLTVcHyL72DgQHylVBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "a3b880308e0a8191721e0081a8c2a8b506d0abf9", "gitHead": "c8eb1b4aa6d2efdb5bc4903fb1fa6e9f7f066a72", "scripts": {"test": "standard && nyc mocha", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "http://github.com/bcoe/cliui.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"wrap-ansi": "^1.0.0", "strip-ansi": "^3.0.0", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^3.2.2", "chai": "^3.3.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "standard": "^5.3.1", "coveralls": "^2.11.4"}}, "3.1.1": {"name": "cliui", "version": "3.1.1", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "d74eb4b5c2d55e7ccc90e5cdca0e8d2813502b98", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.1.1.tgz", "integrity": "sha512-FoeGLzKi6Z0pftJUJZqacfXT4nVqCNGndHm4iW6eBqjDMk7A/UANiygb14D/sLHmk7j7Vb37u9mWWjv1zzOqHA==", "signatures": [{"sig": "MEQCICux/rfDrC1GWYUzX6BXCR2UTxaXw6mBTysdiCUCdrW7AiANg+sSAW+K5RNYGUs08WpGpk+DbnHt2u/dv0G/xYbY7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "d74eb4b5c2d55e7ccc90e5cdca0e8d2813502b98", "gitHead": "e8c78f7244a563c500118580458f11cce3d2e00d", "scripts": {"test": "nyc mocha", "pretest": "standard", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "3.3.0", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"wrap-ansi": "^2.0.0", "strip-ansi": "^3.0.1", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^6.1.1", "chai": "^3.5.0", "chalk": "^1.1.1", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8"}, "_npmOperationalInternal": {"tmp": "tmp/cliui-3.1.1.tgz_1458192164183_0.9397966971155256", "host": "packages-13-west.internal.npmjs.com"}}, "3.1.2": {"name": "cliui", "version": "3.1.2", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "5ebdc752ce6740ca0df470a3b215e82a5da0277c", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.1.2.tgz", "integrity": "sha512-cqX12AgNKOCOSCTkfoijrxXSaEI0r2ApwK3mOtOhEduUyFHad2UG4caAP4MOUoletDntIBxe5BMRUsbESk1GPA==", "signatures": [{"sig": "MEUCIQDxB4RWsrFsRVocrSHBbyX24EOjnLMFP8d7tYALnPmGGQIga1/ATaP45ktIIxL4xEKHPK9aM1EugzV4jm9ChhVurwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "5ebdc752ce6740ca0df470a3b215e82a5da0277c", "gitHead": "d7efa2c021ce3ecd61348018386d7fa4ddc50e6c", "scripts": {"test": "nyc mocha", "pretest": "standard", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"wrap-ansi": "^2.0.0", "strip-ansi": "^3.0.1", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^6.1.1", "chai": "^3.5.0", "chalk": "^1.1.2", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8"}, "_npmOperationalInternal": {"tmp": "tmp/cliui-3.1.2.tgz_1459318695264_0.8533641444519162", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.0": {"name": "cliui", "version": "3.2.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@3.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "120601537a916d29940f934da3b48d585a39213d", "tarball": "https://registry.npmjs.org/cliui/-/cliui-3.2.0.tgz", "integrity": "sha512-0yayqDxWQbqk3ojkYqUKqaAQ6AfNKeKWRNA8kR0WXzAsdHpP4BIaOmMAG87JGuO6qcobyW4GjxHd9PmhEd+T9w==", "signatures": [{"sig": "MEQCIH0LWf2xKES1CqKBXevnGriHorn0w0SO9U/HbTenxeO8AiBANrdfiiI9vkc8AwYR4rTCn8twxnQvVEosenN1nrcaBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "_shasum": "120601537a916d29940f934da3b48d585a39213d", "gitHead": "75d62e9dfa77a0e0a9c3ac3b96b02baa294142ce", "scripts": {"test": "nyc mocha", "pretest": "standard", "version": "standard-version", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"wrap-ansi": "^2.0.0", "strip-ansi": "^3.0.1", "string-width": "^1.0.1"}, "devDependencies": {"nyc": "^6.4.0", "chai": "^3.5.0", "chalk": "^1.1.2", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/cliui-3.2.0.tgz_1460342854008_0.8861493801232427", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "cliui", "version": "4.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "743d4650e05f36d1ed2575b59638d87322bfbbcc", "tarball": "https://registry.npmjs.org/cliui/-/cliui-4.0.0.tgz", "integrity": "sha512-nY3W5Gu2racvdDk//ELReY+dHjb9PlIcVDFXP72nVIhq2Gy3LuVXYwJoPVudwQnv1shtohpgkdCKT2YaKY0CKw==", "signatures": [{"sig": "MEYCIQDwy7pSh4yoDUEVl6lQWqc6IJIsCo5DsOiFXE+ilOcnTwIhAM0sU9yIpeF1izWY8Qco2QawZB1pxXZfQtPOxHioOH5b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "engine": {"node": ">=4"}, "gitHead": "a76e646d3522475820f6fd289d0109c44078aef0", "scripts": {"test": "nyc mocha", "pretest": "standard", "release": "standard-version", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"wrap-ansi": "^2.0.0", "strip-ansi": "^4.0.0", "string-width": "^2.1.1"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "chalk": "^1.1.2", "mocha": "^3.0.0", "standard": "^8.0.0", "coveralls": "^2.11.8", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui-4.0.0.tgz_1513573951475_0.5902562558185309", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "cliui", "version": "4.1.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@4.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "348422dbe82d800b3022eef4f6ac10bf2e4d1b49", "tarball": "https://registry.npmjs.org/cliui/-/cliui-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ==", "signatures": [{"sig": "MEUCIGFS64jhgr8FsYYXkrq1XTTj+M4gbKNQ2zqK/JsPzuoZAiEA+vH08wsoc1wfSjrLPZTtLSsr4gt8DX4tniXpxqE6dXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3UTUCRA9TVsSAnZWagAAQBYP/21PAh+wIPQktq2XDuQ+\ntMjuDek2ebHLK5M4RyNM6TAJioGf5uHwUmqV7rHAhm9gRJn+dIIqkIukvLZy\ntln2x5GkDmYIxOC2wsj8Lbyc0k1cxoynJt8v8F/oE6EEaa6mRHuk3SJQU5Yy\nim1ofDj95s/mWcHeyrjdcfyvKJ4e6GZwylinGspAk1hR6UO33RoiWL4c2YIY\n7jYvi4HNVwv52cqGyoI78QZ8Js90rOCyZQz8QP6tZ1NMo1SwUC3jPauspGsn\npMgHslY1s0fZWp70Ac/MziJdx2XTFzYJZKkiuyJCxsyBuX7f1kUTD/Za235A\nDlpUmgj5a9UKpVtptlJ3nMRmgqXBClnIjvmm9NG2nJ8Egt3QxFdBZaxgJNbM\nMFss+LSZNnxBvmgnAC37sMvie06xwV2UOB/+eiK1EWBdVILV7END0hyRSyuM\naFsVZpqDAaXSVS4hwUFQvPA4biDGncxwDmktuOuJHSGVbIqRJJ7EEfVVBbFl\nH2KZQ8gxFT93Gg6x3tFuXlXQdPaHKvtZTkKJCLtigINk1Rr9PlUzstLRayay\nG55YvsSlDm1P2FMYosbMUrNHe0f8SVgyLsZk/tQ40htjK7zMthyAamxq+JpT\nl63y6KDYrtAwAE41206ry1QyNvlCyp+ZHv5KSZdxcUW25ZK5kwJDCMs1Sbkw\nn7bO\r\n=CsbE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js"], "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "engine": {"node": ">=4"}, "gitHead": "83ada4a595ad60fff4fb962a3288eccbe163bccf", "scripts": {"test": "nyc mocha", "pretest": "standard", "release": "standard-version", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"wrap-ansi": "^2.0.0", "strip-ansi": "^4.0.0", "string-width": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "chalk": "^1.1.2", "mocha": "^3.0.0", "standard": "^8.0.0", "coveralls": "^2.11.8", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_4.1.0_1524450515410_0.021997836619508382", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "cliui", "version": "5.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@5.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "deefcfdb2e800784aa34f46fa08e06851c7bbbc5", "tarball": "https://registry.npmjs.org/cliui/-/cliui-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==", "signatures": [{"sig": "MEUCIFQtWvDgatoxPWGQ7kjuy7XUykMPxhuCO6YQwdsHL9pyAiEAqenZinACsPp/Q2MAtDJGJEk5tKmICe+VYEwE92buukg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrkgBCRA9TVsSAnZWagAAczAP/1CvnEEVO9J2V7I/sYi1\nIvaAQ+QsUGqTNu/YwYHaPHgD257aDRDeQSH8TJlAfB4ffTvGhkZ2zQsTbVxz\na02hMSva5X7K50jyHj6c0JTqdJBOlXzy+grLRCRTwyjyqN6cS+45ke2SKzKK\n3EkizlsMrT33CYfMOBwSlycZHimlzELEF0zI3LM9ywKSiFB3ZbHpxna6ChGr\nHGtUkzX9dE9oTyjd6fFaUUZ57/2FSTKAN2LtdNAEYrN757dhXuxr1spNuH/M\nMIHEqB/622zjIw1XXsNHSguJkbf3whJA74JkJo1iRV2ToVy7WtK202ElH/fN\nw4+b46pI2fRdcEcA/VAQQmji1CiCjryWrDGZox6Bh9mgrKwayVLo4K0UWziK\n28bTnakRHHDkgWo9E7cO5DomFoc7u0rP+mlmgHU3M43DqfNpUOF+QdEc2eM4\nWVFnpAf0FfM5rHRwqIKSLjQ4aajmK0+DiLxiH+uw6yHLQw0zs4rHJ7yNTDIt\nIq6jZ7AUztKqQQPXk8ebYprhDETGz3G5kVVG51j8IGc9e5pAH5JLvSKGjFht\nkWranRXx+/gAxe+2Ng3QiuSC9Q2yMjg4C+AiFBaOdu3fEvnMac4b9oZY/UrQ\nrkkHIqADfSIPTYJg+Eb/oXIC59GWIIy7vGD+k0GIYLt+INki5vjmZJ0ALQTZ\nYgKS\r\n=qYhm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "engine": {"node": ">=6"}, "gitHead": "e49b32f3358d0269663f80d4e2a81c9936af3ba9", "scripts": {"test": "nyc mocha", "pretest": "standard", "release": "standard-version", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"wrap-ansi": "^5.1.0", "strip-ansi": "^5.2.0", "string-width": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "chai": "^4.2.0", "chalk": "^2.4.2", "mocha": "^6.0.2", "standard": "^12.0.1", "coveralls": "^3.0.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_5.0.0_1554925568876_0.43484399290392983", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "cliui", "version": "6.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@6.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "511d702c0c4e41ca156d7d0e96021f23e13225b1", "tarball": "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==", "signatures": [{"sig": "MEQCIBjblVIv0dQgLMhp3niugCvEUB2Wf4FvUIlwqN3BleYDAiAceF7BadnCX2aA7FUOW8bbCnz5Q6uGO5zD1F8bBHDvhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0HOpCRA9TVsSAnZWagAAbIQP/ijBtX9rdZGp+liNKOdI\nHXXvoC5bwDcEItg6CyPsDbeQWLQVt2NFe+x4UwECNYdk2VEYx5w9+6O+Yw/U\nRdQtEV6Ia9aHuo7EGwCuuuLs0F58t+976O89msU6F0MwSVCHe75FnVA8Jw6M\nJcBqAy14vlqKngXp1KtkZrt3+pmd4siwOlglhWZ3bRUQAiqpkcLBx/OaT4xl\npXgp7wwMJmdQ7yKz//ncJsiBs6yt21ukvh3MHXd2xeznBP6KIO5kuZ+aXwfe\nT2R/cBcH7A+MjlZr2lYHMfBcGTnHZ9Hwls2xqsXOXnAae/8sSVAjWwZHrA1K\nk+8cxhUHvyxcD+JahSJ3J5XXX5ToEZrYZ52NK8pwOHQANj2Md3qr5uj1C+pw\ng2ZKgMpZCWHsWAI9LK1qCsnkQRq0K59NgfynrFB4X75wqTAzbPMbRVCX1H72\nYFx6L9LQLlRF7YKCkwNjYIIFHKV2NNlgZTDQWhckD7enz4JuXm70Dti87ntp\nL9OTuABvAJmwcHebbh5ELfhszSAof/FgYOGD1r4zrWZSPBFlBVKLp0JzjHtK\n1pykG6YhVxzKIePK7ksHPIbZSwYf9O+NqTo1yw02EVk5GlRyJADhbn80mI5n\nIc3TjnKaSHCnLAs1Hr+Xbx0TlpzCTtk8OteZ8luS8cJCmfDmtTF/CpTag3Bo\n8Ieq\r\n=pcrZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "config": {"blanket": {"pattern": ["index.js"], "output-reporter": "spec", "data-cover-never": ["node_modules", "test"]}}, "engine": {"node": ">=8"}, "gitHead": "7761da3e8cddd1f49024252a6b0195a94565b357", "scripts": {"test": "nyc mocha", "pretest": "standard", "coverage": "nyc --reporter=text-lcov mocha | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+ssh://**************/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "13.0.0", "dependencies": {"wrap-ansi": "^6.2.0", "strip-ansi": "^6.0.0", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "chalk": "^3.0.0", "mocha": "^6.2.2", "standard": "^12.0.1", "coveralls": "^3.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_6.0.0_1573942185481_0.7800938266208284", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "cliui", "version": "7.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@7.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "93a7f84583bbf794a94da7644eb2a816a3b2d662", "tarball": "https://registry.npmjs.org/cliui/-/cliui-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-uF09d2p7MMaGNhLiQ4Vc1I6S0ZP3S0Y9VGo2E0JcIvSxwTCv17pPmS8ImbAH0EsjL/fETLXdvdhbMJYsws7ShQ==", "signatures": [{"sig": "MEYCIQCinAsh7lPwZwXabr7fnLU3jS1AI/mP+fpfqD6gII5n1QIhAIyt2Tcl+KygzjkdSAVXZf3aI9SEqBB9gM6+Zg+DGXlU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOYXZCRA9TVsSAnZWagAARKkP/1YcRhDyIC3vSkWg+Muf\nHSLpSOpicudXR90gKRrOMJU0DZ/KwnTkzedG6NyqbVQOsJGgRoA7DHfp8j42\nGoch2SpuqGQ5JsvqzmCamWqcSJ1CCZnjeeGetM1ElIMxxF3zksPwiOk6sN2D\n/8C208C7aMwbfHAvi3F/JJnm5czdn1ea3uhhSsV/9SA6+m5IvaKvNRLKld3D\nVw/+5RLLuPk1Z1plq1iq5HHZLQ6ncr2FZwmQNDm/UPAh4SAsI6NSMtmfUf9C\nGWIUs2WH2Qhvw/XvO50RGqbnOKiaWpYo+l9vAJkDtptIBXjEdUPo3V8BwMrZ\nOi9FiV+QOw8Ud7/VfjIuk3JIvs/khJJotKyJHNsH0SWmk4crXOlm4vhazviW\nMb2IpuPfykIMBKs/cHCgb3BWwQD4Q0gNI4DmneF3fijR+2bsJ8CPaA7S62Mu\nY9hPZ/jXNkKxPYoPg98ZL/7tH+YxJFxHHjTNVhvfMVoD6shtlJF+r03Pf73j\nSK0LhCnxeqAagBfCj8xAPqcXEzymj9NRR4t51UC8+T4wAiOi0ty/d3aI2kQt\nvvPH+FLxRczk8ciUSIrkG/7iWII42/OZ3xpsCwLAF4VaQ6ND2dPOtWhID6BK\nvSkMvN7s8daOET3V7mMGjbILUlb63vTW4TabLN1V/Yrfbl4VNC942DBqur6v\nAiN4\r\n=VJ14\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engine": {"node": ">=10"}, "module": "./index.mjs", "exports": {"import": "./index.mjs", "require": "./build/index.cjs"}, "gitHead": "cf6295a5ca10f4e0a4e9f2369c469db0daca9d85", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.0", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.2", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^5.0.0", "typescript": "^3.9.7", "@types/node": "^14.0.27", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^3.8.0", "@wessberg/rollup-plugin-ts": "^1.3.2", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_7.0.0_1597605336450_0.8823627683448991", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "cliui", "version": "7.0.1", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@7.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "a4cb67aad45cd83d8d05128fc9f4d8fbb887e6b3", "tarball": "https://registry.npmjs.org/cliui/-/cliui-7.0.1.tgz", "fileCount": 8, "integrity": "sha512-rcvHOWyGyid6I1WjT/3NatKj2kDt9OdSHSXpyLXaMWFbKpGACNW8pRhhdPUq9MWUOdwn8Rz9AVETjF4105rZZQ==", "signatures": [{"sig": "MEYCIQDTA49t8mO6mPO3k7pka50kjbyhGA944mmOOlhg+BQwuQIhAO0VhCIAUmW6JpKfxRMybtmuoc1OkooeDa/mDJOcpdwL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOYq9CRA9TVsSAnZWagAANLUP/0qkqj8fkKMClIMP3q1R\nuUJQUXmGFJ4gLB3ueV3LhSo/EfHcstW3KiL8bEZ4UkGOwjRur5HtVdsHqGeC\nyNGpLHs+XefgneztbBzWHK5Es5yS1/BJzJDXqdipSdY62firTsdHGUNbXPfL\nPFWFR9fK0A1IfTNtiO4Lr0DJPzcXfbqBOP4ooJwRpRN+Z5wvalevSTyvT5x3\npOe3F5KWf900CzQFGAU/DqG2pWPDkppJ6t/2haBln5k1BjvfhLDi5fCXS130\nux5Oh6AwV4kValcn3QbzLSoc4dRptVPO2ofuIkgBrNhJs4GiD0+Y+vl2RMHM\nXSEHdIVOgY/cRJzyR+oOhA/nhpz0M5/I0d0Q0UHLJtlZU5ixrlsGIDrho1bR\n+NzC9BTiD80NjP9KHVh+IcrltgA7XysVeWDc5+ITtmM6bWFvAFxSvfXLdTb8\nOG2GewQT+njemRKotOYUdOKB9U8l4hfO9fzBG0V+5v7nWlHofosXLDJpb/Y3\npe94HUC0NR6gx+oAudydoqv+n+gZiVm5e/MNZfJVFa4wAkytehIz3yslJqW+\nPdoYG6AqESptfcDjZy4j/cmsBadW1CkD5OCdq5HIWCHaSHETmIlvlBb+8iEe\nXz/8+9a8TSS2+DL//XCFEorIH277TigI+UFe7BJZ2twW/stOuDY8ZC6Eink7\nMHUc\r\n=k6yB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "engine": {"node": ">=10"}, "module": "./index.mjs", "exports": {"import": "./index.mjs", "require": "./build/index.cjs"}, "gitHead": "50bfe19776d4ecf86251ba91edfc2a0b7d7e1bb1", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.0", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.2", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^5.0.0", "typescript": "^3.9.7", "@types/node": "^14.0.27", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^3.8.0", "@wessberg/rollup-plugin-ts": "^1.3.2", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_7.0.1_1597606588986_0.6986629180320552", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "cliui", "version": "7.0.2", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cli<PERSON>@7.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "e3a412e1d5ec0ccbe50d1b4120fc8164e97881f4", "tarball": "https://registry.npmjs.org/cliui/-/cliui-7.0.2.tgz", "fileCount": 8, "integrity": "sha512-lhpKkuUj67j5JgZIPZxLe7nSa4MQoojzRVWQyzMqBp2hBg6gwRjUDAwC1YDeBaC3APDBKNnjWbv2mlDF4XgOSA==", "signatures": [{"sig": "MEQCIHitiB/Pi6cHSACFULN10lnvXr4MSXMKqQlAFNlNkAXkAiAexmMVNefX5OVltXUkC+IsJIkUcGd+oYoRTHPkOUfXtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh5FzCRA9TVsSAnZWagAADjAP/iiAnT2fRMNiSHrjcJHV\ndCqFsoR1U9r3b1kU2GUalR11GvqEsDgKiLuYsw2AFAPjWaQquN+5l+rNdO4U\nEuvzpv2yyPd3quJQ5BGDKLaqoGnNmCksbTwT8Yd+A3qnymtIeul8ynyxH0tx\nNBRrAIuo81Relyt9I7HJ3CTD7a1N4XaQUc0o4BLYqDbY60uAvT5zyMmQ3rG1\ntXsTovWL3CYvM5URkHE42a8NwXbBxU0paCsbFBoohSW7FFxkQxJSRlKZ3pZi\nzPOdEs3AnDwcfkeRPcp6PZrhB/yM0vsb3OGyho38bvuFNmfC7uWeJq4jffrS\nEFGUW2/lBsRLES3vUxHIkZNSgebKHOgzxzbX1+oYIY2TrdLxDjEGrmnAzzSi\noSFT5X7iVaXQK3jlBruU9d5sO/EB55hAkKIHNCQK/CudYzo1/zAOIf1sxHXg\niB7jqRHlNUamv8OrT/jJth4I5KeXU9kElpuyVQUf/rV6w9EJS1pQH2wzh6SC\nQypyHb+3QtCTyLu8teFCvUcKqQVisDAqTHRY4YcCS6G/Q1My2/Wcau+la1uO\nJa8LZ+uOf7pfiOnlhft0vONNpDyOtqJyvN3FLFIV5e7bPEGDE3s/K2ND495K\n9snY6td41Tn+NIciHtS+8I/HNgQ78UsFQ4dAFaaEqE7wmuJNjTDGypW+Mxna\nMi8U\r\n=RjoD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "engine": {"node": ">=10"}, "module": "./index.mjs", "exports": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"], "gitHead": "8222cbffb5c966c192868d2a0a008709fdd1f60f", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.13.1", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.0", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.2", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/node": "^14.0.27", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^4.0.0", "@wessberg/rollup-plugin-ts": "^1.3.2", "@typescript-eslint/eslint-plugin": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_7.0.2_1602720114656_0.9906874813260971", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "cliui", "version": "7.0.3", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cli<PERSON>@7.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "ef180f26c8d9bff3927ee52428bfec2090427981", "tarball": "https://registry.npmjs.org/cliui/-/cliui-7.0.3.tgz", "fileCount": 8, "integrity": "sha512-Gj3QHTkVMPKqwP3f7B4KPkBZRMR9r4rfi5bXFpg1a+Svvj8l7q5CnkBkVQzfxT5DFSsGk2+PascOgL0JYkL2kw==", "signatures": [{"sig": "MEYCIQDloCV6c9LHUe5Oe95fzqTadY/ulMI7D6pE+6PeeSsWXwIhAMgM5w3+K2iCiriGP2eo1Z3Hxs3KN5lG48qvrcqCD4RJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJficAHCRA9TVsSAnZWagAAAU0P/1HuyWeVV13hFcYKTVjn\ntgN9w9d2HaFCx/D+qZwYgc06NAJX/SbC4wWA69lAtee+e2WKUNvnDEycQGOF\nKxP1XjMOendpmJWpd6hXAhn8MD9KAlFQnOzkljuLKZyfXkT97ofZpmCz1jrM\nyjfJGj0FXwBCx/o9+baK9UanF2etSyt/aONGmcUbOsy/a39Pe8Ey6WuhJegV\nhVGcmUhzN1vWK9U5EpYqzsK0oRcEdvmMTkHyM8bFy2wEa5l2+PU39FaEdyV5\nyKUNHGl6RlA/qUOEUi18evrO0maoT3P6a0BzXTPKGcDy+lm7KPo9Jbcee2JA\n04CkcK86mk2Zv/KRerGKsh2w6B9BlXIvVEFlPxrLH/CX6XydjtQSlCa2iBMI\neMB8PJS7BqhQvwDGaB11DN+Re3xZzu2NQK29ivCF83yc38cF+2b5ukd+mQHk\n6P6raMudoNNqX/aMLJWxocxu4YfiiAB5c7OBM44G8aWbgNfF33ZLYUEO51P7\n6vpQk34WO0REQn8/Qhh/Fr+/HfpUathsxs7Op6quaHGezPQJ1V5x23QpoDqA\nFzW2xyETs9YuXxB8UvMZmrYOnD0oLHQEee9jac8jUAXxo+QIBZTwUYw9F+Tj\nkU/W+VcxLxMpq6qQrXj0Hg5Jsz/QZWbgUClRQklHgLoIWUWbWoROGv638I61\nfCSW\r\n=Ufog\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "engine": {"node": ">=10"}, "module": "./index.mjs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "529ab01ffc8133379439c059246f6b6301754939", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.13.1", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.0", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/node": "^14.0.27", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^4.0.0", "@wessberg/rollup-plugin-ts": "^1.3.2", "@typescript-eslint/eslint-plugin": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_7.0.3_1602863110729_0.8534412865221799", "host": "s3://npm-registry-packages"}}, "7.0.4": {"name": "cliui", "version": "7.0.4", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@7.0.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "a0265ee655476fc807aea9df3df8df7783808b4f", "tarball": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "fileCount": 8, "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "signatures": [{"sig": "MEUCIHv6dAUDoqdr/92SOyP7K1lthtVFi2H075pXwx/Y9xRDAiEAzbPvjP+WQlw+O4t1iVHlbgUCYuGfMBTUMvmvN41CM3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqIaACRA9TVsSAnZWagAADdoQAJN1/+6xYOsg99Bi0/GJ\nhnIHHNTyRrAeeXJLD35YL5fvziHrbLztsYncc8Tb6d1GQ871rjyYyhyC4CCy\nqeHA15MD4NBRwgJuQzl1gQDi2xydhLEhu1vlNMawtAr77utbTmUDP45LzsFY\ne1aliwKBVCOsvOhs1krKOmKOf3tSu3wedlpewLMWvABSO0wIkEyycqQp6r9/\ngwMb2wgOCf/hZ9dTFn0VuRGY4KU76LUO2r+aKiwUHXcLnSstFgSjrYUwYO+W\nXPbqxBNIyZiKnq2IylCR5iNmoXueWPg8+XKFSAKgZSnU4LE8cfzSzvmQSVdd\nMZ6ML639HlIJK4cZfnCjhtP3hywQ/Sm7IDPF/UDzkPSPoMZ0PjOngTjIfzLn\nKT2PJHpfLX0/VcStdOuqvUTP0PARbVehqMAPfWjToib+ABWW2WyPKOkARxfF\ncPzH4lBpnqXtLwMD/gG0gkTb0aAvZ2nXbC6yy5+ypvh+91KDZ9WufTHL9Dfv\nM+1R3ND0XempD0ZU1BC3LVuitvWjCIhIFtKu05XGruss6ZP/2RQfQzNDRFUl\n/Gl9cL9gl6ps+BMDSVqGQBzV92oWAfbuvgtYBxWhSB3Bw1AlfDmHTxO08Y/t\nKN9RSn9PA+16L7J2tp9o+gj74DQYouoUKQyQsyZMSzlPO58Jaex5L4us8PK7\n/Ee3\r\n=tbXB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "engine": {"node": ">=10"}, "module": "./index.mjs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "dd9f59f24fc1c4cd5c912266a597f02cdc51d681", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.0", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/node": "^14.0.27", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^4.0.0", "@wessberg/rollup-plugin-ts": "^1.3.2", "@typescript-eslint/eslint-plugin": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_7.0.4_1604880000241_0.07762027867910559", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "cliui", "version": "8.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@8.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "6fbfe158b7499c1814ce84b8eab124ebacdf9455", "tarball": "https://registry.npmjs.org/cliui/-/cliui-8.0.0.tgz", "fileCount": 9, "integrity": "sha512-5XiMtQlIUjUrbqkIVuhjB0Y3JSxYE125PBeJ8RRPqfvUn7wmIVpZWn5RMe/8nIPII4vm0A38eRzAe/uakU8oCQ==", "signatures": [{"sig": "MEUCIAR9SIYPRL3cmeEDAGdvNhbhFj+6acaQ59ladrIleiA+AiEAr6AocDO/M2CLvlkVphAWjhynETMLwU9cSmQbT9prNTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjN3/1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHAw//VWiGek00X6Ly2zSguUfK/aLw1qRLGQwoq7FtWwenM4mzm1Tz\r\nQ+5CE9MEa/3PRuiAF/bPxFnTC2F7UdS3QWgyKmWwW20gaiB6s8Ml7r6JtDyL\r\nf7/Lvn7Pn9k03YK5wHq8tLUNA8XXu9wFoplB11bZcYK9rleJbsN/IBHBGdIw\r\nBiE/LVNcmBBM1rms8oOJuriQZ7dd4q055avLzCaCRvKrByjmqUbUJxxejy8H\r\n44qYcA3xm14ns2cux5m9P85SBPtpjM7l0XTQm0kRXXOdDOCKHeKJa+KjXVDq\r\niWZqQwH9kBnTghR1v/6fTeZ+r7CNyN1FzPHxo/9kR+O/6VG0akZherGMQaJ1\r\ny3rnecpbWAmBNEv3o1RN0TULxNNqgjmi99onDdpujXxDguNandmzbuPf8YAY\r\n+Wu2AekSgv1FyskSMhyR0qbAkR/Q0YkI/pVEQVj5XOIMYe20iPN3IOCR9AUz\r\nYKqg6oqyBVsYAE8ld0PT0wWFsIJ4JmWcPFNZjpNDMOVsoL/mGrbxmcJGzmzy\r\nyfS8HtVdRYTbGK+qiWsmh9PtI6X5HYHPxrUpHB16V4B6136HskEG+SazRI11\r\ng1Q3UL9UgsV7KNSwLvQWqYU0C+u8uSGlw6Iv+LAou9TyP9BwdY2NmfwM6Rvu\r\ndyIHBJ0fKk3CncCVpZtBqeYuYdjkC9l51T0=\r\n=FI2X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "20ae6d81a7a12eff19cf86599c744db3670da128", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.1", "string-width": "^4.2.0", "rollup-plugin-ts": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^9.1.3", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/node": "^14.0.27", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^4.0.0", "@typescript-eslint/eslint-plugin": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_8.0.0_1664581620848_0.9754478706016363", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "cliui", "version": "8.0.1", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@8.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa", "tarball": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "fileCount": 9, "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "signatures": [{"sig": "MEUCICT+AAAXpsVi4LR7GH5hWc1QTzEJ1oCNdMIodJiaYYZFAiEA9MAqtt97TEVIRcSwAjTFSQefwq1bvan76N13TJIozkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjN5TeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXVBAAmb/TPsuVLnMmZldkJvo4bVf9s+GGNjwD4N5VyCs4Ppm60mXb\r\n5eC7P0kO8KWkDoWX+KBCE6fC5/JsYYqgh20qI5ApUL8nC9bkX99XoScLPe4A\r\nyAsAr6XKj3QUaUJNj0jHcmOaoKzLc1NXDTYL6eZNgxzYrIOa47HF5rSdUFRU\r\nTNmBcs8pp7/QDYJI/bk5YJjRe9u7eRNjAnsxby/Y11TaF2zs7QO++wWey1eG\r\nsaOErz/hZz2icHldvIpesRUHyCqUtKVB2AT/9sRxHLK0tWh02OKF8rhEAoWd\r\n9rM4Q0rntz1/hAEHp7nZDH3cugnnv8P4Efr83a4Kg58W16V4PU3ltsvPL8o7\r\nejLqRcUnJM3ex7stMRuCszgksu/Pa5gYo7GlihnIJEXB1RNXm8ddfXo6Y+kU\r\nKRGLfB7ZFTLqrffJw+IB1tq66Owij3gnPRTRHGDPADuRQZhGQv2z6SEG85Zy\r\nI/C++8te/RLHRXW/3uUxc7SoOpJLH64hd3iVWQqtcC8I6kDivSTbwWKzZ4F+\r\nUhAUtTCVASYTVHQqWf4pd3G9dm3sBr9jd1a5GhdeDPA7vDG/MIhOAWIxH1Ju\r\nrS/aPAW/FlC2sjDxS2flx3M7J+4f3ePzY5IRiWvk26yAKSQw72bbkKI7Jwec\r\nUdcn7tHhYeVnRNC+3PYTBz2DdnA7YNChxlk=\r\n=dyMc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "a214f7eedf23feba70519ec7d389baa9dc40682b", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 mocha ./test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.1", "string-width": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "chalk": "^4.1.0", "mocha": "^10.0.0", "eslint": "^7.6.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "cross-env": "^7.0.2", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/node": "^14.0.27", "rollup-plugin-ts": "^3.0.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^4.0.0", "@typescript-eslint/eslint-plugin": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_8.0.1_1664586974630_0.8014987526748925", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "cliui", "version": "9.0.0", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "cliui@9.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/cliui#readme", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "dist": {"shasum": "97949d7dd4c8d937e4e9f4df4dc83267129bb4f9", "tarball": "https://registry.npmjs.org/cliui/-/cliui-9.0.0.tgz", "fileCount": 7, "integrity": "sha512-V6GF3Ws4irBF4YEURZ8yoCPmuN+Bqey/11qKmVMRfwN1YAHg33Mcm0Wn9DhD77/OC1UQ7JEYTZ1W5zHcvGgBTw==", "signatures": [{"sig": "MEUCIQC1WxFYyZcgCob3tb0xL1UR2Qw79bsESBO0EpTGKyuBYgIgdq8S/5y6E9U3fthyjMiA8rxiecxg2Dd9IpJ3TIYiJaw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56721}, "main": "build/index.mjs", "type": "module", "module": "./index.mjs", "engines": {"node": ">=20"}, "exports": {".": "./index.mjs"}, "gitHead": "c0785d8f40cab8ca6053946dfc893d424584ba1b", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js'", "test": "c8 mocha ./test/*.mjs", "check": "standardx '**/*.ts' && standardx '**/*.js'", "compile": "tsc", "postest": "check", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "repository": {"url": "git+https://github.com/yargs/cliui.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "easily create complex multi-column command-line-interfaces", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"wrap-ansi": "^9.0.0", "strip-ansi": "^7.1.0", "string-width": "^7.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "gts": "^6.0.2", "chai": "^5.2.0", "chalk": "^5.4.1", "mocha": "^11.1.0", "eslint": "^7.6.0", "rimraf": "^6.0.1", "cross-env": "^7.0.2", "standardx": "^7.0.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "eslint-plugin-n": "^14.0.0", "eslint-plugin-import": "^2.22.0", "@typescript-eslint/parser": "^4.0.0", "@typescript-eslint/eslint-plugin": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cliui_9.0.0_1742137024945_0.21411966868997356", "host": "s3://npm-registry-packages-npm-production"}}, "9.0.1": {"name": "cliui", "version": "9.0.1", "description": "easily create complex multi-column command-line-interfaces", "main": "build/index.mjs", "exports": {".": "./index.mjs"}, "type": "module", "module": "./index.mjs", "scripts": {"check": "standardx '**/*.ts' && standardx '**/*.js'", "fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js'", "pretest": "rimraf build && tsc -p tsconfig.test.json", "test": "c8 mocha ./test/*.mjs", "postest": "check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "prepare": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/cliui.git"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "dependencies": {"string-width": "^7.2.0", "strip-ansi": "^7.1.0", "wrap-ansi": "^9.0.0"}, "devDependencies": {"@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "c8": "^10.1.3", "chai": "^5.2.0", "chalk": "^5.4.1", "cross-env": "^7.0.2", "eslint": "^7.6.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-n": "^14.0.0", "gts": "^6.0.2", "mocha": "^11.1.0", "rimraf": "^6.0.1", "standardx": "^7.0.0", "typescript": "^5.8.2"}, "engines": {"node": ">=20"}, "gitHead": "89aaf48ce9653fd68b59ec4479680d1c22f21030", "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "homepage": "https://github.com/yargs/cliui#readme", "_id": "cliui@9.0.1", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-k7ndgKhwoQveBL+/1tqGJYNz097I7WOvwbmmU2AR5+magtbjPWQTS1C5vzGkBC8Ym8UWRzfKUzUUqFLypY4Q+w==", "shasum": "6f7890f386f6f1f79953adc1f78dec46fcc2d291", "tarball": "https://registry.npmjs.org/cliui/-/cliui-9.0.1.tgz", "fileCount": 7, "unpackedSize": 57360, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEkb6l5Cm7COfd6bHdeeQov5QKVEzn8t3aGbyq+jsPANAiEApFkEriDZDhnQVHjnYhQdTSm89pDD71MVKEGNGpS97oY="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cliui_9.0.1_1742178985295_0.07198612049330677"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-04-20T21:30:56.852Z", "modified": "2025-03-17T02:36:25.675Z", "1.0.0": "2015-04-20T21:30:56.852Z", "1.1.0": "2015-04-21T00:10:18.586Z", "1.3.0": "2015-04-21T01:16:02.549Z", "1.4.0": "2015-04-21T17:34:17.418Z", "2.0.0": "2015-04-22T22:00:16.933Z", "2.1.0": "2015-04-24T22:35:03.777Z", "3.0.0": "2015-10-13T05:48:49.134Z", "3.0.1": "2015-10-16T05:39:54.745Z", "3.0.2": "2015-10-16T16:32:26.146Z", "3.0.3": "2015-10-21T04:05:21.086Z", "3.1.0": "2015-11-29T07:08:55.070Z", "3.1.1": "2016-03-17T05:22:44.735Z", "3.1.2": "2016-03-30T06:18:15.713Z", "3.2.0": "2016-04-11T02:47:36.139Z", "4.0.0": "2017-12-18T05:12:31.588Z", "4.1.0": "2018-04-23T02:28:35.496Z", "5.0.0": "2019-04-10T19:46:09.048Z", "6.0.0": "2019-11-16T22:09:45.650Z", "7.0.0": "2020-08-16T19:15:36.596Z", "7.0.1": "2020-08-16T19:36:29.174Z", "7.0.2": "2020-10-15T00:01:54.788Z", "7.0.3": "2020-10-16T15:45:10.873Z", "7.0.4": "2020-11-09T00:00:00.351Z", "8.0.0": "2022-09-30T23:47:01.033Z", "8.0.1": "2022-10-01T01:16:14.782Z", "9.0.0": "2025-03-16T14:57:05.166Z", "9.0.1": "2025-03-17T02:36:25.499Z"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "repository": {"type": "git", "url": "git+https://github.com/yargs/cliui.git"}, "description": "easily create complex multi-column command-line-interfaces", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "readme": "# cliui\n\n![ci](https://github.com/yargs/cliui/workflows/ci/badge.svg)\n[![NPM version](https://img.shields.io/npm/v/cliui.svg)](https://www.npmjs.com/package/cliui)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/yargs/cliui)\n\neasily create complex multi-column command-line-interfaces.\n\n## Example\n\n```bash\nnpm i cliui@latest chalk@latest\n```\n\n```js\nconst ui = require('cliui')()\nconst {Chalk} = require('chalk');\nconst chalk = new Chalk();\n\nui.div('Usage: $0 [command] [options]')\n\nui.div({\n  text: 'Options:',\n  padding: [2, 0, 1, 0]\n})\n\nui.div(\n  {\n    text: \"-f, --file\",\n    width: 20,\n    padding: [0, 4, 0, 4]\n  },\n  {\n    text: \"the file to load.\" +\n      chalk.green(\"(if this description is long it wraps).\")\n    ,\n    width: 20\n  },\n  {\n    text: chalk.red(\"[required]\"),\n    align: 'right'\n  }\n)\n\nconsole.log(ui.toString())\n```\n\n## Deno/ESM Support\n\nAs of `v7` `cliui` supports [Deno](https://github.com/denoland/deno) and\n[ESM](https://nodejs.org/api/esm.html#esm_ecmascript_modules):\n\n```typescript\nimport cliui from \"cliui\";\nimport chalk from \"chalk\";\n// Deno: import cliui from \"https://deno.land/x/cliui/deno.ts\";\n\nconst ui = cliui({})\n\nui.div('Usage: $0 [command] [options]')\n\nui.div({\n  text: 'Options:',\n  padding: [2, 0, 1, 0]\n})\n\nui.div(\n  {\n    text: \"-f, --file\",\n    width: 20,\n    padding: [0, 4, 0, 4]\n  },\n  {\n    text: \"the file to load.\" +\n      chalk.green(\"(if this description is long it wraps).\")\n    ,\n    width: 20\n  },\n  {\n    text: chalk.red(\"[required]\"),\n    align: 'right'\n  }\n)\n\nconsole.log(ui.toString())\n```\n\n<img width=\"500\" src=\"screenshot.png\">\n\n## Layout DSL\n\ncliui exposes a simple layout DSL:\n\nIf you create a single `ui.div`, passing a string rather than an\nobject:\n\n* `\\n`: characters will be interpreted as new rows.\n* `\\t`: characters will be interpreted as new columns.\n* `\\s`: characters will be interpreted as padding.\n\n**as an example...**\n\n```js\nvar ui = require('./')({\n  width: 60\n})\n\nui.div(\n  'Usage: node ./bin/foo.js\\n' +\n  '  <regex>\\t  provide a regex\\n' +\n  '  <glob>\\t  provide a glob\\t [required]'\n)\n\nconsole.log(ui.toString())\n```\n\n**will output:**\n\n```shell\nUsage: node ./bin/foo.js\n  <regex>  provide a regex\n  <glob>   provide a glob          [required]\n```\n\n## Methods\n\n```js\ncliui = require('cliui')\n```\n\n### cliui({width: integer})\n\nSpecify the maximum width of the UI being generated.\nIf no width is provided, cliui will try to get the current window's width and use it, and if that doesn't work, width will be set to `80`.\n\n### cliui({wrap: boolean})\n\nEnable or disable the wrapping of text in a column.\n\n### cliui.div(column, column, column)\n\nCreate a row with any number of columns, a column\ncan either be a string, or an object with the following\noptions:\n\n* **text:** some text to place in the column.\n* **width:** the width of a column.\n* **align:** alignment, `right` or `center`.\n* **padding:** `[top, right, bottom, left]`.\n* **border:** should a border be placed around the div?\n\n### cliui.span(column, column, column)\n\nSimilar to `div`, except the next row will be appended without\na new line being created.\n\n### cliui.resetOutput()\n\nResets the UI elements of the current cliui instance, maintaining the values\nset for `width` and `wrap`.\n", "readmeFilename": "README.md", "users": {"bcoe": true, "siyb": true, "mxjxn": true, "mrzmmr": true, "goliatone": true, "lemonleon": true, "reyronald": true, "snowdream": true, "yakun.cyk": true, "dozierjack": true, "shuoshubao": true, "bsdprojects": true, "django_wong": true, "flumpus-dev": true, "danielknaust": true, "sundaycrafts": true}}