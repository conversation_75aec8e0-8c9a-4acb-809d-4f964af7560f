{"_id": "fastq", "_rev": "37-08b22bf77ff7f88f9f1c66dfef1cedf0", "name": "fastq", "dist-tags": {"latest": "1.19.1"}, "versions": {"1.0.1": {"name": "fastq", "version": "1.0.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastqueue#readme", "bugs": {"url": "https://github.com/mcollina/fastqueue/issues"}, "dist": {"shasum": "d9e2aacca8d232b690c57f9026b6b07872acd4b3", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.0.1.tgz", "integrity": "sha512-eAdqxFsxbc1N3W/HiU6jNpR0vvSja608TZraQfJbHb1USzKWEyKTuFrMnlIogrbGcaLa/J3cqm3dMLVItRqytQ==", "signatures": [{"sig": "MEQCIElwIBE8z1ipx+ErdrQRIuJnw/a33kwNTniwceVRC6gzAiBJoUBbnWY/Llh/gSO//OYbkC8nO4iKpkBc1VYZXTjJag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "d9e2aacca8d232b690c57f9026b6b07872acd4b3", "gitHead": "adc7feb56229c874a2d541f02e1644a9387d6f82", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastqueue.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "0.12.3", "devDependencies": {"tape": "^4.0.0", "async": "^1.2.1", "standard": "^4.2.1", "tap-spec": "^4.0.0", "pre-commit": "^1.0.10"}}, "1.0.2": {"name": "fastq", "version": "1.0.2", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.0.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastqueue#readme", "bugs": {"url": "https://github.com/mcollina/fastqueue/issues"}, "dist": {"shasum": "1e04a6e448e9260ef35452f1d53211637f81f16b", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.0.2.tgz", "integrity": "sha512-6GZQ7DjIy+kJ2RXc5LIlT1DlIemQP/e8urcJXugdSI5aohOSSsgRMb94ncEHnPZDsOxPnj3Y7cw4XDnMpLJxCA==", "signatures": [{"sig": "MEQCICT5dLSOmjKa1gNkW2HL1FF3Db7wKbT2xwPO8JtnozfwAiAgX97TzcpcfECg6VzZ8tJ0E/GgZjwBKI5g++Z5IZnPFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "1e04a6e448e9260ef35452f1d53211637f81f16b", "gitHead": "e8a82dec16a8767a893c14f0e8cbefb00c64da6f", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastqueue.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"tape": "^4.0.0", "async": "^1.2.1", "standard": "^4.2.1", "tap-spec": "^4.0.0", "pre-commit": "^1.0.10"}}, "1.1.0": {"name": "fastq", "version": "1.1.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.1.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastqueue#readme", "bugs": {"url": "https://github.com/mcollina/fastqueue/issues"}, "dist": {"shasum": "67c04791b586aa15bea0979815745f5876cf1bb2", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.1.0.tgz", "integrity": "sha512-WJ<PERSON>+/SaJZErq9R7eS/qkuSqYI9xUcp59MqvKEdts7B6dXounBfhFvCqg4LSVtjt/m4Bs6bG1WeKlQKt9psBbGQ==", "signatures": [{"sig": "MEUCIQCCzFrdFtACjI1D2Lr55ko1zYAcxMUWVs0XEllw10A1nwIgN0dupw8yx1Oj9VP8q9G2uOg5kIv3GLwpFO2rodtAXFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "67c04791b586aa15bea0979815745f5876cf1bb2", "gitHead": "6c9b42cb67997c2f810cbd1b9a655147d75b426f", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastqueue.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"tape": "^4.0.0", "async": "^1.2.1", "standard": "^4.2.1", "tap-spec": "^4.0.0", "pre-commit": "^1.0.10"}}, "1.1.1": {"name": "fastq", "version": "1.1.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.1.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastqueue#readme", "bugs": {"url": "https://github.com/mcollina/fastqueue/issues"}, "dist": {"shasum": "381622d4da4c7f770cb17c437e2b092fd6b54f87", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.1.1.tgz", "integrity": "sha512-iHP7H18TTX4JiIE9+VuL6O+pogs2nAAY53Oa66yhEYlgMDrZtHD2gDaFBvvCVyHJk1isxkRr1JIe4S+2xbUqqA==", "signatures": [{"sig": "MEYCIQCyM9gjsPSoSnuq3KLurpzXPgvhmGcGP0SHt/iNqenskgIhAK7WW9+83VYm2GskO4PhzNgYHaEDXadVHq3B3KlFw+w3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "381622d4da4c7f770cb17c437e2b092fd6b54f87", "gitHead": "00de09eb10d703c32f67ae44ba8dcb57c2b35963", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastqueue.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "0.12.3", "devDependencies": {"tape": "^4.0.0", "async": "^1.2.1", "standard": "^4.2.1", "tap-spec": "^4.0.0", "pre-commit": "^1.0.10"}}, "1.2.0": {"name": "fastq", "version": "1.2.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.2.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastqueue#readme", "bugs": {"url": "https://github.com/mcollina/fastqueue/issues"}, "dist": {"shasum": "a4273ef78756c81280aa1b9d667d51290d7863d3", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.2.0.tgz", "integrity": "sha512-MiOuhhlAEqy2nmyzsyilLfx8s/xGfTpO31S/r+3/FNyMlfKD+YnYDzQVhYmvEE7rZy/o1jIMW/bkOgweukp58A==", "signatures": [{"sig": "MEYCIQDlvwK0QZWYQHZtVBKWtzz5CYkfNVwjwbQSu39E3T2qRQIhAMB8yAIF2s3gVcNuO8Oim6DC5XO3e9kpqj+ErBvNEWSs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "a4273ef78756c81280aa1b9d667d51290d7863d3", "gitHead": "782b5dab2e41209e722e6d10a1c39c735625d9ca", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastqueue.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"reusify": "^1.0.0"}, "devDependencies": {"tape": "^4.0.0", "async": "^1.2.1", "standard": "^5.2.0", "tap-spec": "^4.0.0", "pre-commit": "^1.0.10"}}, "1.3.0": {"name": "fastq", "version": "1.3.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.3.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "88c3aa32bd18c82d268239508d19734cc541ff62", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.3.0.tgz", "integrity": "sha512-R1iAQq4Odg9cCb9PiBgdjW7mb6yaQYHoZmWpPpvfDiXAYqe6Q/yNSdDETTtBT8ctVLt4BdsIaVZHfJIlxfLLjA==", "signatures": [{"sig": "MEQCICQd3sDokNrPsVC4nYNrw1skuvJO2/mTRLox/F8MQ+XBAiArY+32Ram7QCORCaTVDqL7dq00iaeCJGZynkmfuXqf7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "88c3aa32bd18c82d268239508d19734cc541ff62", "gitHead": "ed1c6078f7ae0bc0af44ee45728b0b065ec7dc9b", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "4.2.0", "dependencies": {"reusify": "^1.0.0"}, "devDependencies": {"tape": "^4.0.0", "async": "^1.2.1", "standard": "^5.2.0", "tap-spec": "^4.0.0", "pre-commit": "^1.0.10"}}, "1.4.0": {"name": "fastq", "version": "1.4.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.4.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "8ac6e75855262f0edf54a1ece7ffcedd1f5ac184", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.4.0.tgz", "integrity": "sha512-5e2qOyaSOftZSlo7SX5zjD8B90n4uv0IdXcdclhNc/OVuHqLJytrBg/ujE+DUnd1e+7vqf3CYAX4fxhjQxVXmA==", "signatures": [{"sig": "MEUCIQCdN71+l9w74Rrc4PMdCHKpOakQpRRLNzB7RSJOG5wrTAIgE1kdVHhRyjKAIYWWh3KBLvoIjP79cNhySU2bp9yB5/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "8ac6e75855262f0edf54a1ece7ffcedd1f5ac184", "gitHead": "f2a03c95c90657c0f52920ff6c7d1d71760abc91", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"reusify": "^1.0.0"}, "devDependencies": {"tape": "^4.0.0", "async": "^2.0.0-rc.3", "standard": "^6.0.0", "tap-spec": "^4.0.0", "neo-async": "^1.7.0", "pre-commit": "^1.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/fastq-1.4.0.tgz_1461837945770_0.35579590615816414", "host": "packages-16-east.internal.npmjs.com"}}, "1.4.1": {"name": "fastq", "version": "1.4.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.4.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "90f55859fa3087832a1c33e2708b95ff92e3695e", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.4.1.tgz", "integrity": "sha512-gkRiOqFKGAN+WS9mh7JhUiyGA1wu/y3fv2/02fmLV8+2hui4qb3RxzmfnHFYi3TGYabJ35xt/9SFZIeQQMbgVg==", "signatures": [{"sig": "MEQCIHm0mmz3/pyQxNgTeQiQ/98b2VxnrvOxPbLIfx/iobcBAiAbUDvkHLMToFl0umRgq3c0taScV2S8igJSa17O3oQ/bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "90f55859fa3087832a1c33e2708b95ff92e3695e", "gitHead": "a799020bddd7b437af907a5c2178c50da6cf6339", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"reusify": "^1.0.0"}, "devDependencies": {"tape": "^4.0.0", "async": "^2.0.0-rc.3", "standard": "^6.0.0", "tap-spec": "^4.0.0", "neo-async": "^1.7.0", "pre-commit": "^1.0.10"}, "_npmOperationalInternal": {"tmp": "tmp/fastq-1.4.1.tgz_1461876976362_0.5632361315656453", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.0": {"name": "fastq", "version": "1.5.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.5.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "05e32ffb999ec2d945dda27461bf08941436448b", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.5.0.tgz", "integrity": "sha512-2DWRZd5ovYh57B6DxkDcnCDhKp6TP3NQHHSJh6igAs1/xYhjBI53pgWk77BVlOzc6YEzCPjFHBRj6a7QaZRrNw==", "signatures": [{"sig": "MEQCIGTADDWYpOZmgHVObKlTZw7qOCEJfn0W/+I09FkY1AsuAiBqe1AXYg0WhYJWvhBebbiQmwuJ6E0aJN1sPeaj8Ior4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "queue.js", "_from": ".", "_shasum": "05e32ffb999ec2d945dda27461bf08941436448b", "gitHead": "ee7f5f52a8cb6b8958c230c64ca4807e1507f2e1", "scripts": {"lint": "standard", "test": "tape test.js | tap-spec"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"reusify": "^1.0.0"}, "devDependencies": {"tape": "^4.0.0", "async": "^2.1.0", "standard": "^8.6.0", "tap-spec": "^4.0.0", "neo-async": "^2.0.1", "pre-commit": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq-1.5.0.tgz_1483910211658_0.9198376317508519", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.0": {"name": "fastq", "version": "1.6.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.6.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "4ec8a38f4ac25f21492673adb7eae9cfef47d1c2", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.6.0.tgz", "fileCount": 11, "integrity": "sha512-jmxqQ3Z/nXoeyDmWAzF9kH1aGZSis6e/SbfPmJpUnyZ0ogr6iscHQaml4wsEepEWSdtmpy+eVXmCRIMpxaXqOA==", "signatures": [{"sig": "MEUCIQCg3eHuoq0ILUUnDMczbtBzTWWo45rDlaqfWwl8YUirKAIgVml62vu6iPi+Cu5+18YCLwVOz3QkdlU2btdaAulZ6cM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbE5TXCRA9TVsSAnZWagAAWewP/1VQx14Y7iekLOKEUxzE\nNlAh1rAnyR9pfDJN37G7AeDNHwjgPaRBirAUjNdCPEd1dT8TwHwPe1pBEx6G\nVERH8wuz3hhIHJLGytWEcprSjXwp7JqCbssCruQY+brW9cN3yjiKg8ikntJl\niL6sWikg8jDJ3z/RBAGzoiaac+yCUuKbMTRDfNkUbxfqvwX27Ta6HrwuB73M\nwT0FCaiw9elqEHL3es+hdrp0aVTUs7T6gFSmt9kx6U2LYU8HuITUEC2gnkhy\ne2+gdNPF/mXG/G+EK+JclEKEaJmskV15OnzKRR017mBRnqBgygv4qUUSCJdB\nlWCRbNB4zxQpcejTuohCQBcpRVYvSUn2oKO00gjeBSa7QsUi5/FuA6Pd48q4\nFU79a71xxmPMB7g89iFXRVA1sq3rnJFCB7rWUWEorHXtu/5+/zfUiqE0sDpC\n681ptGvwpNHdSEqp6iYwHwvSrnTdVFwsFzaOdDrIxJG0sIzk556UT2Eae6WH\nEAoc1SNYvM68QGfPb7uKzp5hJER6bjU3Q2u/42pWX/3fBrUsx+2iPELfKBo1\np66BSTJwp4nM73tTDeQs/ImlWEYOi9SiWPByBcW4W/PTAX+vAeullfPaarkP\nYfxJozooeVLBO6cKWENC1ypaXSJFHM9+myFgqvTqzFPcOBDgFZCezPmhC/Qz\nkL2K\r\n=KaR9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "664fbb2ca75aaae14dd3f83d3043302fb67417b0", "scripts": {"lint": "standard", "test": "standard && tape test/test.js | tap-spec && npm run typescript", "typescript": "tsc --project ./test/tsconfig.json"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {"reusify": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.0", "async": "^2.6.1", "standard": "^11.0.0", "tap-spec": "^4.1.2", "neo-async": "^2.5.1", "pre-commit": "^1.2.2", "typescript": "^2.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.6.0_1528009941921_0.18227264572935398", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "fastq", "version": "1.6.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.6.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "4570c74f2ded173e71cf0beb08ac70bb85826791", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.6.1.tgz", "fileCount": 11, "integrity": "sha512-mpIH5sKYueh3YyeJwqtVo8sORi0CgtmkVbK6kZStpQlZBYQuTzG2CZ7idSiJuA7bY0SFCWUc5WIs+oYumGCQNw==", "signatures": [{"sig": "MEUCIQCOecS+3eHmtDAFpobSBvwjUpxk7l4YNPXaRtnBbe2BmgIgUMOLsxt+fF2O93h9UEVsdbGu/Twt2AXMqG2MPGw/uMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWCJ2CRA9TVsSAnZWagAAsMgP/0xyDnpeCChHwkVp/gUJ\nHJ6YHUDLFuda9CI55B6IoFpeUhBRNdNR5WGLT1zhKJNwMEOxFYiu/nDSzpOq\ns1t9H8G0rtwCt9OqpqYRaj4tTogSpkuJ/eV7n+y+M+EeQnA8CQQaNSYrGqbE\nOdRMsIaWobRTwpwrbySXzlvQeew/5N9k9PyRCWwC5YiqMNK+12cDn0rEfgYi\n6xrJcAndK5/HeuV4Os59C16kn5ijWvsXGmFLKAN/NDORwiHQSOFPRcN6J2DC\nrZX7stcPtPX0PBc6KaNF8yldS1N4+gzjY/c07IJ3HhVqWL7DYI/MmIJ1Ks8Q\n3vpCJ2EzeYdm0WDDPM8ixQjiJSkOg227cMl4MYvflsfUrsZvrWBfEI4/GMGH\nhXRtjCNyJthuICVIiBC4D/rim02KfbuiXlgx8HNVVpsCO7+Md/i/eMusSYIg\nUi2mlRAD1iMFTaSxJwATTqHuDfz4SLgUy0p2ouJve1yiiMBDj9vDSIeY1WK3\nOG/khJOhrk2Q/GGhMeaqkq3Vfoo9Fr9i+F/lCxcjg/kQVOxg9K4LEdmQiVih\nAyaazTj8P+TFkuCdRcvBoJ91SwkEzfvYcHezu2Gb0OzASyRPltpXOdbhFFnL\nPw//WRA+Id41fnyot4ynUYGXgsFlW9qyDmSgmZcFudihPjc9a9FMf3vtHHwv\nrRSQ\r\n=69b6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "19771feef6694c51213a4bd6b4bc07192fd0830c", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^4.13.0", "async": "^3.1.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^3.5.3"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.6.1_1582834293634_0.8592684038623051", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "fastq", "version": "1.7.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.7.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "fcd79a08c5bd7ec5b55cd3f5c4720db551929801", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.7.0.tgz", "fileCount": 11, "integrity": "sha512-YOadQRnHd5q6PogvAR/x62BGituF2ufiEA6s8aavQANw5YKHERI4AREboX6KotzP8oX2klxYF2wcV/7bn1clfQ==", "signatures": [{"sig": "MEUCIEndhXMxCmy3hdxRLn67DWu9JDwlQ1lErX75cIop/UbcAiEA0ehwHb/CCr4PIZ79zEHDf0Agv5qkdAPau7WuaLnch0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehRzgCRA9TVsSAnZWagAABtcQAKH63dyKEoV3cLI1WzXm\ntXV9aRe1jVGz+GH4sSKFR1B6H8vjFx10t7EWXUqxzvEL5vOjnUYwUmz8wij+\ngUNEb//3dJTOrGYs9PXfqcdnKoWjWRQlI35i+Ey+K9rbACUR28y6H6t/WJ+u\nfAp2uPN7yJUzgJCFQLLlOLeH02D4rRayUChMQTghyS4EElF2Gog/+udVR9nz\nji2Wi5qn0qsCNB6cmT+9eYhja6UHHhP8/YRPAaQgfz/uD5JpA1mLvQoELYLw\naH5uw1T5VDcuSeybDf0Ha2qfIK431HBHixRDbfU8Mf6vWQGvBbIF2HE3nocF\nOvjr+XzeflnSslbI0OzMo6/isi9bPy/LCGk4lwn6ZxKQ3U5+Z68e+kT8O3pO\nIHiAvpyB5fSTSWOsDg4voZrEk5bgsvrbCa33Jz8h7J/DpqcvRocDsyz9NoXa\naUVNY0W1DNF3gFJWdj3KGk+2Nq4sBV1bluld2H1hGeUP6lUnGXEWjrqNDYtT\nMb1hFYLvl3amqennJOjiijMmr7KezDAMcYhdFFFfJ0eCJapvisQ+b5UJeXIg\n1UGd9lD6GEhzIWiy2HXsI4L3q7Z9vYbNWe/uOTbIsIiZZ0oEqJRSLYJVE1qF\npSOQANekpbfvbjs/7IMxOks7lp01W06j0mMoaoFk6OaU6n1srU/IBQaQMRcC\nOYDU\r\n=+Cp9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "b3fc271726e7b1e7a215c3d853e91ae73330ca64", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^4.13.2", "async": "^3.1.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^3.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.7.0_1585781983713_0.5438727801187897", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "fastq", "version": "1.8.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.8.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "550e1f9f59bbc65fe185cb6a9b4d95357107f481", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.8.0.tgz", "fileCount": 11, "integrity": "sha512-SMIZoZdLh/fgofivvIkmknUXyPnvxRE3DhtZ5Me3Mrsk5gyPL42F0xr51TdRXskBxHfMp+07bcYzfsYEsSQA9Q==", "signatures": [{"sig": "MEUCIDDhJNSsL3nFYXVBJOdV+zuR7fWYRCuDteA3tAsQ7QjAAiEApDoSiwV0b+oc/MOgw1LOAqYO5LzWG9gybJzhfge8zb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeun35CRA9TVsSAnZWagAAvrsP+wbowdoiZFfTQkZexCdu\nw+NGkGTfO6olDo69xaKHyUZK2miJCiiqjpqetZinY5/xjmIEpII/1I5AqMlN\nOerHN4uO6tqVrI7M0Jv6UCRdavWj7SqtAdjJc0vFClEvCuGPKObzx844Y3sp\njLKNqGW3EJqg04gmAJWZdcNCaotyM9KMIl/4kd6FJHmTsCz5G7TXivzyCqZs\nj5Jd9stTw8eFWZUKxOC24jazaMQD0D0BuK7vyy0aQQhBVYciL/qiVYR8HB9n\nDKjHRJnTDyW7NO8rXnWW096hJHcLen/B6PiU6Sv7aacHT+fsxAEpjaaSad9v\nM/+ZMKi6FxsU05UBOPWHgiEjh3L4s5zScQlZDUrnWAYUlFAN6CdsMXTycpKm\nk0daBRFh9m1ISls//YaasJbzkvXLH4ndxaBfCSL2U431nP3z9VvrTjwgeLDV\nuenBl3lyzoAlhLQw94vol9TSj8NXLk/+jaYUY2EK20PDRo93G8rrAeOenr/V\n4NameZH5UmAPV+BAH0nsnQNYNDXlwsmO+PFyiAgHTjnVmDGPVRrjqS34cahf\n99d71fRlE2RcDgi1OfvSiCK44WAeeO2Q6haM1Nx13B7BF2c9kM8qqcfNMmKb\nLQijkVEnq2xuzWaVHTXbolRp9YH5LMnlzrzEkA/lJKh160qWiqIr5eOb9n6E\nlNNV\r\n=xzl/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "e2920cafefc9cd4f579de7028f48956cf9b8fee9", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^4.13.2", "async": "^3.1.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^3.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.8.0_1589280248999_0.6097184822888224", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "fastq", "version": "1.9.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.9.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "e16a72f338eaca48e91b5c23593bcc2ef66b7947", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.9.0.tgz", "fileCount": 11, "integrity": "sha512-i7FVWL8HhVY+CTkwFxkN2mk3h+787ixS5S63eb78diVRc1MCssarHq3W5cj0av7YDSwmaV928RNag+U1etRQ7w==", "signatures": [{"sig": "MEQCIDNoR+oeZBJFCIaF735ffBt0V8JvhEreD6xe8hbCWSHeAiAkdLtaES8Ogy6zcyVFW0lTH/vdsbHyN6cP0fxftXDVaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmUA1CRA9TVsSAnZWagAAlmgP/36RdTsPg/G5AZD4T7WW\ntNAxU5oK+4FBwCHDdR7kyGalb84GFkjlYV201lJ6vUDUEmr/20hOsgvn82gZ\n93SdVs3F/UhXqUVBh5uHxyHuEsCN7Vc8Pm/yaZn//Kbt1D1vt/VkQwH9JFVd\nWLNRQduUTZjd8bLEAxP6g05IhRlMN7lbBVk4oy8AUd9hge6WnTyVJ9gurzAP\nuVK07RzIsmM96FaX25iifi0OAhq3pv7R+YPRdGEAqAMcAjzZEMRKp+obkPV/\nzaUOoVGs4ZExbQC9K9SEYucAdqO7xoAT6eGkdtLTtYkiYWBWt9FcfpGzyn5w\nChZwdFmtHf0O1Il7XpXFxnkYrF5A+qt4RzBEulsM0wYxTSWVTNMVBx4UX7XY\nTsbZ3vfsDB4n6b0vF0/2DPZUj5mgTfKnr22yQvvicjnkQ7EGk6UT5E0dc5jB\nsriRx03Xf9KsvniIsRLLYGQtypN380ms5xaf75wjzBZ3+vUoGTEMnt+Ri8MX\nVBvwaQXd063iz0CsszZI30tTB9ayY0E0Hsu15FYArbngvRS8WfpbJxyKLb76\nugWsW5FaHQRX8cttZwhKe6EP8AhjxEcX5+jJTp/1Eu7lIhMe4ICJJvYYJTdK\nr242qwmc/GiUM8pbM+9a+GAgQG8LjkQ379DzaBTHNm/FYJS0z8gvL8P21Yn3\na3hb\r\n=Zor8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "96d9e617e84a76abbf96125afbb3b21faed507cc", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^8.0.0", "standard": "^15.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.9.0_1603878964570_0.5213992325045711", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "fastq", "version": "1.10.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.10.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "74dbefccade964932cdf500473ef302719c652bb", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.10.0.tgz", "fileCount": 11, "integrity": "sha512-NL2Qc5L3iQEsyYzweq7qfgy5OtXCmGzGvhElGEd/SoFWEMOEczNh5s5ocaF01HDetxz+p8ecjNPA6cZxxIHmzA==", "signatures": [{"sig": "MEUCIExZLUpFdinnrN4ZabyMfR1ilyro8rP/vH7lOcdyI+jxAiEAjsb7leFOw7D9QmfVDz1gTpwxLnemjIFhiflcJpMI2x8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3zlqCRA9TVsSAnZWagAAs7EP/3pdyEfLq/7UARh0yHvh\nV/Yzg24/PD9vHTkqUrjEimi0jekE5kSzz9iXKA5WegvtT2IJlAXjB81PP70Q\nweAjic6ifVLcllQawfH7ORwlGeTZ+Av+kwK105+KrB4KfACEuGrcGNCp6bkk\nErg1Y1AUqN1QkwTew9YQjZPWI8VL2QOdwhfKUeRT9xwwNY4ZFxTWy5/aTRPV\nAJgMsY88bcQvZfHP1FaALVOC/7OzOOyrShu7VoFz0jsyPjTp69wRKsnoiyJG\nk1eHZpXmqeSAK+IhphbdCAWOM42YG3vdhiwrhS+20uePXpziNXjvsXk9gBIQ\ncaJ4Qo8hAlZq4K3LWzeCI11+q3dkaLi5LeXeb9VbhhC3gt3XRsqbGDQ4BMl0\nYDnGpGczL734fzR7/hItMWCgVeLG/99bKnLLgapMs8ixNLW46+NJSykbz5eA\ngHKFo9/18a0haV8OuDgpfPmWEVfCbJDRoPacg73O1iWuIvL2+JTtWzPZBVch\nfNqIGQBAmAuMwYTpuCvqUq04J7hw7F2krEXnsxWUuZzrKMIgc1X+Jdn5fZdq\nKWQdzmYOHSYqfCELNOfc2+KseT6ZH9iX5tIKkdpvxIyLKMHzxyH0Jiu+0Iv+\nhJ4UUohUJU6VjTutIoTzFrWBi8alI0wEZRUKdaKUgL+AgQWHQPMO0YRUxHYr\noT8i\r\n=04wd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "9b929c0ac40fe6b1ea0d2c4560dddf0d4be4851e", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^15.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.10.0_1608464745853_0.8510020560979323", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "fastq", "version": "1.10.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.10.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "8b8f2ac8bf3632d67afcd65dac248d5fdc45385e", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.10.1.tgz", "fileCount": 11, "integrity": "sha512-AWuv6Ery3pM+dY7LYS8YIaCiQvUaos9OB1RyNgaOWnaX+Tik7Onvcsf8x8c+YtDeT0maYLniBip2hox5KtEXXA==", "signatures": [{"sig": "MEQCIAhHhVAqkMTdCeUnNVSRWc3uAcxI5G+qdgL/raWs1PKgAiAGqQkq0Nhbowh6JnsNcMfMNP8XhKktMj9xGDD0Bgm5/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEqYfCRA9TVsSAnZWagAAdFYP/1rfzeXPEc6RoQfR9gvI\nAMCMbuaPmMajMBFTzNIkWoeyseYsDbhs8SKt9kmmDMu4OyuqVG0Qe4Q9Ftqq\n0G+sQIVCV06gEzk3+c67j9sV07cBUQSoMghdUC1hPLk/qogF5mQoPMsOZQlL\n8d5iflHaJ1ZPJxRPONvLnVvd2gd9L7O9sZcqshGQPcvz23DoA4i3liBf+6dF\nKplTjenNgINasKjYGCIj0joCkgj1IBL0gqMdYzKGgIGWziD0TA0G2lORqUki\nAKOS0ZWnuamvTfijSRfldg4J9mv6sO+KHtf+I+gWW68fDQJAV2O5vJsAuEY2\neb8tgPEu+6FDW04N0PlLDkz/lk5uZQ6h1YKq2YXgdyIt/Ro3RX6VjC1EXwde\nkFM0GMgqY02DqI49SWGvoYb/yvfs5q0sphXs3tgSvBZwaZL3hKHghpSuouh2\nQoiiEGpsipGOdmcCJWC4vCtnoQGJstVjbJaXbZZRqUA6PUbmAsKkQzhC2M7N\nQHRZJjRPsZtRdL0Iz1qFoMWM91kQjijx0RlW3ABXJIW/P7d7oSwNYDEOq2zO\nRb+9SVdM3B72q4F7/9GUxu0e+OAm+eTgAn7+keuE7bIHBXhj0Uz02ffv+U46\nKdkaevbTgVlhFUmKoBbvLdVUFaDs2NWSs0zGFpl7Ajk912p3cXPmfQf926Cx\neQKZ\r\n=DHga\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "7ab77e57949e619f7488da656c3331bf84ce2e5a", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^15.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.10.1_1611834911394_0.25106229912687983", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "fastq", "version": "1.11.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.11.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "bb9fb955a07130a918eb63c1f5161cc32a5d0858", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.11.0.tgz", "fileCount": 12, "integrity": "sha512-7Eczs8gIPDrVzT+EksYBcupqMyxSHXXrHOLRRxU2/DicV8789MRBRR8+Hc2uWzUupOs4YS4JzBmBxjjCVBxD/g==", "signatures": [{"sig": "MEUCIQDsp0lLb7QFpRraGmFOLAEdaAWy0V0q+S9We3+SS1cGtAIgBPTm1U1TQARlHb03Qr/TGusOeLXX/a0WJx/GNtH4lzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNnxyCRA9TVsSAnZWagAAsqIQAJMPGKpxgBRfOUsZETLk\n1BKkDAgUlMXJ2IU0RNCIAOiLTo9d8pJ1JdVr/pGowpNkcQyVP7x3DeLq5vIO\no28aIfkMg2UBDbWAy30+S77n9uRGqIc/FFjUi2SCC/WQeTWI6IS+v5Vie6A8\nk1mLaOX023gx5xBPeS3xOUSQSe14HL+TVHbiv2vkLb/mOeKSRWwI0j+L2d/3\nHrI2Ay2aANE7+uGm9Gf/cCILSDVuTSPGoBYVNeAPY7+kxhBC44SkWHVyKa+w\n9z6jvpVT39qSmM92KprPG4qIfU/3mDwgWya0j4NXM1W5zjuxGEW+Zzvw9zOh\n1EI3An1mvbFlwtJBvDriK8EdFCJ8TbZumj1AE8tw5fjtxRGKde6/BbbwQGhV\nqucThJirgM2s2SnhghmuYHqPOSaPmp7GsyPW/97X8TCLNZstfOjYJXcbKSEU\nY2zGuWqNUT45lSmLn/otSMOP2RmvEwoQ+t6d8hIdprro10DCSiVQdL0/Mczx\nbte0Q0v97fV+34vOBmC9SZw5CKcphCWiEntqr4BT5Peo0XBKSJWePsMDNHtW\nahFQnX5GEIUG1se1tzqDdohFNxSo0pfHadYPEkJwCsetxO89L9IoMzHqjeLh\n6WhnFSJx3GTviJm5F0aGh28JJjLk6VNsty8r/WYIy2Kt909CZmQ5lHHcfwg6\n0G1q\r\n=20hV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "1c937b1aa5f5727165ccb04eaa86a09b79fdd65e", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^15.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.11.0_1614183537524_0.42100755850713134", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "fastq", "version": "1.11.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.11.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "5d8175aae17db61947f8b162cfc7f63264d22807", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.11.1.tgz", "fileCount": 14, "integrity": "sha512-HOnr8Mc60eNYl1gzwp6r5RoUyAn5/glBolUzP/Ez6IFVPMPirxn/9phgL6zhOtaTy7ISwPvQ+wT+hfcRZh/bzw==", "signatures": [{"sig": "MEQCIG/9cYhWkkSo3Jo/xC+rValTkZ6o0wbFRCi+4UkmVQiaAiB2SUmozMi0eQ0jka5IamgOh4HCE+3JZDl4IYevztjtUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4YbnCRA9TVsSAnZWagAA8/wQAJZDUrqEMu0Whr+9ixQq\ntKWO+gcgXs/fy20AS+7EhMNvg82wg34YETZ36tkAloPMaEp18t73oBLbTvCF\ngpXXm016wPYoFpwiyln2J1hJFWfJNA/nSrFFNRmPf7FkvJajIZkvsze3Qjca\nMRcdB3P6B4qfkYYwhSQeKNoW5RFa+tb3sHL9oTYi3Muy8jAJivQJ6DE3Oy0n\nOyQQULpiOaHqwvK0sK0jeDUSOodWYwSV20TVSCbKT2IH99lASFwGiO98cVdg\nYi87Lu07kS2wbHoT8X5drx9pxhyAFJB0S5p/z9CkwvX4+oqMvbeUL7iA9vsE\nJ6Z3nmQh/GGnd/M6x3PI5osMVVqXmvlJW92RIEwI4BYsvZ+rmuWdNEiuxo4s\nGtXWL7CEGwxdu83MLOsQp6pNg0lZ0+AzLkyengOpb7eGJWXa/cMKQqDdnhaY\nRvLXuGJ+QFo1rFXdx6uBHX4/NfxNUCpuXlUovrjIvWdx9WgKbXwNRgPFWFsZ\niwXOigV4xdyyRiSARSDx6cP/j+e4Gm0R7nKE3dldp2ezsRDh5GrMJk9kdLGE\nNcWt2KG9XLMfQEsfGXaeiy+B8MeOVj9GJeYrxhWzUIZQkuoxezO6b/Nh2m8h\nctsvJO+/fLdi9/Z4JuXJ5J1l67qfWs9MzOlKWANIBigMuZRZwKaAsIrSMo1m\n1r8p\r\n=fHID\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "0fbb3baecc302cd8146322cc947384dc42dfbddd", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "14.17.2", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.11.1_1625392871010_0.1454803963643081", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "fastq", "version": "1.12.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.12.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "ed7b6ab5d62393fb2cc591c853652a5c318bf794", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.12.0.tgz", "fileCount": 14, "integrity": "sha512-VNX0QkHK3RsXVKr9KrlUv/FoTa0NdbYoHHl7uXHv2rzyHSlxjdNAKug2twd9luJxpcyNeAgf5iPPMutJO67Dfg==", "signatures": [{"sig": "MEYCIQDENM23tgnZUD0se0GdoPs+yogzNrPHQ8MH50kSZqDhEQIhAOMeqtvGH1BqgR0HSp0Ua8VS6r92BCzJFMMtproSuG26", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHhMXCRA9TVsSAnZWagAAEQAQAJNFA5s8F0wiVgFOdxDf\nmxMYZZTZcme+iw6xv2KUGlpQv80gWFvr0LBSndfXDyM639yY6NrQkCwYAqaP\nBWyUKNUHJnkdaw+JWhf2TWq73X1g2BnVJvLav3oLcpX3QbAkYkQdLHY02mn+\ntyPFHVsSzxjWs2hPFgUQei1FKgWNZJwcrQzCkat7RdvlvTivpH+wA3ha+NVo\nv2MvIasvdIfWe9Fp6z9mBY7QS1yOM2zMXz4xWw/XuYPKpV7joU7ZVtzdm4hw\nPOU42FFf0cNgWk0iYnj8h0TqV11FiX3M55BDXSPKp2YnUBIKgZ22lZHnhEFS\naeRnJZ+oGNhCpgTMuMZlwJfqzoWB4a1l8GybtSP0nLYnEwn55Pbv2E0HvU7s\nwUz02HFwL+ZIoMpFFwiT2HQ+bXTN8CCauWuZQeyudQiPiETfYyHeavxIJd2w\nnFdO4SN4CYcz8wWQ/HSkLv1ZQtRQecUrYyWls1XhEzRo8P0DDjaJLZ+Lo6mH\nRB3MH73E7uVC9TY/oJOTaIP4SNSEwzDwMVoivEwv4Dfh0JzexcmG47OKtlDi\nZQraCYm1EYP2rf7eJ+JShNgfc5f7pe2dawrlO/CzsB3HKOa+jPzlbcLSIEJL\n4P/pN+EnxYxpe4/ta9dKp9DWOuhSDT+6Gcl9bSzvcC4M3XzCDDyL599b1e22\nPyAV\r\n=fsoV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "062709319250c435d381c27a7bf49a0c1cbef150", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.12.0_1629360919458_0.4592257392177084", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "fastq", "version": "1.13.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.13.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "616760f88a7526bdfc596b7cab8c18938c36b98c", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz", "fileCount": 14, "integrity": "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==", "signatures": [{"sig": "MEYCIQDGKAjBl0bUtj7vV6+c+bBx2OY9mi1L1+0PZlAPDnvfuAIhANj9qqMEgsNtiWMHR+7qjnpz+deii9uLVpPVO9fVxNoi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhO2bbCRA9TVsSAnZWagAAqQwP/1OHkIBWKenqz+zPbMeL\n1FbDKrKqz+Iykld6pDUX7oonw34Kb+46QUKND1teZUlKiPXaGuw3AiVppniN\nlXuwx/jhUNIuD/crlcU9spKc0l0mKVf1sVSsbxOj9BkftdWWVugihkhxPjeY\nmFIKuL5eyN3peL61N58pzDgMQQAZtXMuRCpXNY+f+aLH4v7ECo9+2E2BRWEP\nXGpjDUtQ56+MFL6yg523WgDSl67u7ybMtJdA52s5nP6cA6+AkRGkrmEyDp9+\n+RgCLxPc13ImpC6FsUld7cVTrXSEYERXQTEe3/q7u2GHtloa1kcJ7mj3RYtT\n/yRQQTpOjZAV1opu6Df1G4Oxk914Ww+Kk4WnzWFeCASpXo9ZATOluisLP7ad\nGVqcr7BVRLyfEZ2bSkSLvhlQFC7WG8d6u3d2SV19EXgN+B29gefC5yAidrWD\nAVCWvoGogH51FLb2ZlIwjewH8VpjNr+7L9IaW0dK3vpOVVE+zQHQQR34BAhg\nFn/vQYg3SHisDYSu3rpWPyU1kTL8aYZKNH4486nHMQApiSQQTI/U1DQG3HyJ\nrZXPJKJ19o2z+02D8CP/i8c1A64RmhNO8i2iYT5kY0kSpP/KESKiHHJmjqXe\nfnPXi4kWmtyCCtGF6x9A5xwnyO6CktyMnt3dYMcr4dF4Jqopk1JdkKpFdbFk\ni+kO\r\n=FT6u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "3b9f23d8bcf2d9399fc2095d77ded7285c35128a", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.13.0_1631282907095_0.9253452030272613", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "fastq", "version": "1.14.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.14.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "107f69d7295b11e0fccc264e1fc6389f623731ce", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.14.0.tgz", "fileCount": 14, "integrity": "sha512-eR2D+V9/ExcbF9ls441yIuN6TI2ED1Y2ZcA5BmMtJsOkWOFRJQ0Jt0g1UwqXJJVAb+V+umH5Dfr8oh4EVP7VVg==", "signatures": [{"sig": "MEUCIAoSvCe2YA6BnEfuQWjM6bsrQN48rb3jgBL4ZkJasMagAiEAwjJsLpvfs850F76p7/4thdVZ8q2TvvwxYQLLKD14gJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38270, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjioXPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJeQ//RW3Xv2xrb9Sh6q30b/hRT5Ai8PRy06vRffytdfF7frYzeJdS\r\nFy5rkEMOSc5WjHQ16ypmIxHJp1R/bJUDWQ05j5xCYULHk49lIICG2Pmlxqj0\r\nsFK7n/yRatnCxnpz2eO9/7SjClAYR67/RMzwpQ7++QfhfA251dXl29fO9asb\r\nDLkRCIaBQnuB5TVjmi9YKdO/x4kHgYyHlhrlGfIZCp/hxjQ4ZwwO4lpVUVCH\r\nXtKCmfq+FXVAFHai2JcaYJxuqG0Nrz2+7vIIhMBqWmSg5TbhStygg7rZe38z\r\nOfaTTI8i+jjxSMg4+AMS6x0CxW72pGCEygZr8d+A+wnBWlVXH75ccGzHA7pL\r\n9PCPkIAgX8qGlKQ0GOG06ccXIv0Ux1n/uGPk7IBVm58cBPnV9OCVL+H+nCsq\r\nwxHEJ/4mQGm0HFRGPbU3LDT2ttQFg0W73CMIr/xxu7nxQ2DEbz+lCrFdyVRD\r\nso59emhPpfqYRcTFO/pz2uBe5r5XEVuOGgDy4eHtlo0xWSQ96RuzmzZuMwP7\r\nkpMy8PSGlyKkk03ne3q9COHMnuNhpK9l9kfYXvEvbgxRbSLzQodGOPJD39B8\r\nqsuo63FkfZ7dvDLqgIIr7TOK3+DTGjkQcvQRljTZFxBYbzZ2pPxLeA1uihDN\r\n6Q4630ezsIvBxzSk3tkK5KxgXAcNeEDqkLg=\r\n=A/gc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "a864e24338f6aea1b47dc7e36e9bceebbf3af90a", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.14.0_1670022607787_0.25152099145056317", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "fastq", "version": "1.15.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.15.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "d04d07c6a2a68fe4599fea8d2e103a937fae6b3a", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.15.0.tgz", "fileCount": 14, "integrity": "sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==", "signatures": [{"sig": "MEQCIHZxLRtn5CTNdJHq0UnonJA2ay7PnZmDyhSW2Dz1PLIvAiA1H9KIXxsAVNfM+lXgQz3o/Jwd5qHUmbUGOzKu/Og/qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsgZuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Zw//WG5uyKF91/+4rCbJVr6VaydY3FKPFkBeSBkuLGv/TCNYzCA/\r\nb8//uj4vcmKn6ocL5ej3xxaRg+bO/PEVxKlKGfvqs+LnXwN1FyCVLaJ4ga8b\r\n1VeYTfzmHRpb1rrEp3CQKILI1g2UkUPcbPnU0Y12KqOgiKdmnzNHFA1m8U0m\r\nPfWfHz7Gl0rJcSK1AVe5FUMjUJIaqw2tkWqrlElqQ11abB428NC4wE+UGKXP\r\n8Crb+fI/3AX+NKAZSrgm2LS+U7zL9fk1Ey96eDrPdLMMdNjNv8uJXQmmaA/B\r\n9DSPBAYQNO3lFD0a6RcuFHIYrxvN6urtgNDwhxq7G/pp3jrCf1dhxPnQ7y4G\r\nKf3B5tjVd2ri77xnBinxNV0TUpABYgFw5pFFVW2w/cRFZxpSCfT8kyAXXz4b\r\n7ZSpoFtZ3b2K8r3uxgq7aT9NF1moLe560NF/JmdpYcH950JW9o0UIBiMS/pL\r\nErWqT85oeATKG/L2C/sECI/i4ICGTz4Xp1zeVcBSwZoKZkSbUyzZ1UfqCs9h\r\n7exxMP94oREYcPQkP0bJnp+7r+0UP5KhKTaltfFUNCBQ1PwHtZanx1uSYumV\r\nNuF8M2JM2BETL/ys3OebBQeYediGe8Seki8CChMJ8UM5MfdeVB5OoWfDO0z7\r\n94okf+XrzzWR8TB6lQCDKnxKeOgNZVtHtSU=\r\n=UdAm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "queue.js", "gitHead": "776e916fd7e60c4af03f979f5dc1c550e7734087", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.15.0_1672611438208_0.7918599138458442", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "fastq", "version": "1.16.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.16.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "83b9a9375692db77a822df081edb6a9cf6839320", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.16.0.tgz", "fileCount": 14, "integrity": "sha512-ifCoaXsDrsdkWTtiNJX5uzHDsrck5TzfKKDcuFFTIrrc/BS076qgEIfoIy1VeZqViznfKiysPYTh/QeHtnIsYA==", "signatures": [{"sig": "MEYCIQCLtt1SUN6Q+wMKJvkinyX/88diksJa/uyBQpSHY12jyQIhAI2XuUPex8wlXm/zWi4wkhcVqjfw4dmPVLX+5QaXRzzS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40056}, "main": "queue.js", "gitHead": "b8d99205b36f9a0e8063ab9c84f6a92757d59ced", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test", "typescript"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.16.0_1702914772935_0.857091102373557", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "fastq", "version": "1.17.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.17.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "ca5e1a90b5e68f97fc8b61330d5819b82f5fab03", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.17.0.tgz", "fileCount": 14, "integrity": "sha512-zGygtijUMT7jnk3h26kUms3BkSDp4IfIKjmnqI2tvx6nuBfiF1UqOxbnLfzdv+apBy+53oaImsKtMw/xYbW+1w==", "signatures": [{"sig": "MEQCIFynEAWPETkrwhZuttzJHUAYKnNV9FPXIB4bGGW7EAODAiADFnEwJEyK6eRWINta+yzQcLCgXZQXVE0FqGyT3g2DsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41721}, "main": "queue.js", "gitHead": "22d8dc1e66a0211ed1e17a1bc183bdd422e74362", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test", "typescript"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.17.0_1706293324578_0.8504124721909834", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "fastq", "version": "1.17.1", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.17.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "2a523f07a4e7b1e81a42b91b8bf2254107753b47", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz", "fileCount": 14, "integrity": "sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==", "signatures": [{"sig": "MEUCIQCJMEDYIznFFoDTKiZAH6JzVvot6td5rBA3QkIMVTztUwIgYKpe5F9YMEV2yAfWDr4sGMXIJvQ93OJfDJgEXmH3WC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41927}, "main": "queue.js", "gitHead": "5520cdb996439fc978648642a2a2a28ccbd90f61", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test", "typescript"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.17.1_1707143413476_0.2743892538811106", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "fastq", "version": "1.18.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.18.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "d631d7e25faffea81887fe5ea8c9010e1b36fee0", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.18.0.tgz", "fileCount": 15, "integrity": "sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==", "signatures": [{"sig": "MEYCIQCkvXCHwnvuNvXI+bkoEPJLV2iVsaqhCFYuJA+9cb3PdQIhALnIqgmoaD+rhw/VUe1YAjTOIV8+VMIb5hVY2XM3w/3F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43454}, "main": "queue.js", "gitHead": "571b25b0ed3a7a39f033f2fdb96b995a62414dc7", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test", "typescript"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.18.0_1734948740983_0.9094686710169715", "host": "s3://npm-registry-packages-npm-production"}}, "1.19.0": {"name": "fastq", "version": "1.19.0", "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "fastq@1.19.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/fastq#readme", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "dist": {"shasum": "a82c6b7c2bb4e44766d865f07997785fecfdcb89", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.19.0.tgz", "fileCount": 15, "integrity": "sha512-7SFSRCNjBQIZH/xZR3iy5iQYR8aGBE0h3VG6/cwlbrpdciNYBMotQav8c1XI3HjHH+NikUpP53nPdlZSdWmFzA==", "signatures": [{"sig": "MEYCIQCnHK6M0OpWqD7p2EXg3C/ACAMFmjt8xojIf9PDkX03awIhAMbJHv6/oJ1CGVbvux4CNl7DJOWLRVr0ivdsi0w+1wre", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43854}, "main": "queue.js", "gitHead": "f8dc91ac2781cca5956f591ce53ca7d09bf61fc0", "scripts": {"lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "typescript": "tsc --project ./test/tsconfig.json", "test:report": "npm run lint && npm run unit:report"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "standard": {"ignore": ["example.mjs"]}, "pre-commit": ["test", "typescript"], "repository": {"url": "git+https://github.com/mcollina/fastq.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Fast, in memory work queue", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"reusify": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "tape": "^5.0.0", "async": "^3.1.0", "snazzy": "^9.0.0", "standard": "^16.0.0", "neo-async": "^2.6.1", "pre-commit": "^1.2.2", "typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/fastq_1.19.0_1738253619438_0.17916112451712163", "host": "s3://npm-registry-packages-npm-production"}}, "1.19.1": {"name": "fastq", "version": "1.19.1", "description": "Fast, in memory work queue", "main": "queue.js", "scripts": {"lint": "standard --verbose | snazzy", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js", "coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "test:report": "npm run lint && npm run unit:report", "test": "npm run lint && npm run unit", "typescript": "tsc --project ./test/tsconfig.json", "legacy": "tape test/test.js"}, "pre-commit": ["test", "typescript"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/fastq.git"}, "keywords": ["fast", "queue", "async", "worker"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "homepage": "https://github.com/mcollina/fastq#readme", "devDependencies": {"async": "^3.1.0", "neo-async": "^2.6.1", "nyc": "^17.0.0", "pre-commit": "^1.2.2", "snazzy": "^9.0.0", "standard": "^16.0.0", "tape": "^5.0.0", "typescript": "^5.0.4"}, "dependencies": {"reusify": "^1.0.4"}, "standard": {"ignore": ["example.mjs"]}, "_id": "fastq@1.19.1", "gitHead": "5a7e5668c9d20535945a5a406775609fd9f07352", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "shasum": "d50eaba803c8846a883c16492821ebcd2cda55f5", "tarball": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "fileCount": 15, "unpackedSize": 45922, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA6FghASVb6zj5B2h0OQKUlx5GAzAz/ZtEYdxSoB9TJAAiEAm9x7r3ktzXhHZdLAyos1gMb4zrczP822dEY5OyHaerE="}]}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fastq_1.19.1_1740561430248_0.24172342230383248"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-06-14T18:25:18.741Z", "modified": "2025-02-26T09:17:10.722Z", "1.0.1": "2015-06-14T18:25:18.741Z", "1.0.2": "2015-06-16T05:32:42.248Z", "1.1.0": "2015-06-16T15:26:12.208Z", "1.1.1": "2015-08-19T07:56:33.004Z", "1.2.0": "2015-09-13T15:41:20.250Z", "1.3.0": "2015-11-23T08:28:07.680Z", "1.4.0": "2016-04-28T10:05:46.892Z", "1.4.1": "2016-04-28T20:56:17.344Z", "1.5.0": "2017-01-08T21:16:53.813Z", "1.6.0": "2018-06-03T07:12:22.036Z", "1.6.1": "2020-02-27T20:11:33.808Z", "1.7.0": "2020-04-01T22:59:43.868Z", "1.8.0": "2020-05-12T10:44:09.089Z", "1.9.0": "2020-10-28T09:56:04.765Z", "1.10.0": "2020-12-20T11:45:45.985Z", "1.10.1": "2021-01-28T11:55:11.569Z", "1.11.0": "2021-02-24T16:18:57.661Z", "1.11.1": "2021-07-04T10:01:11.130Z", "1.12.0": "2021-08-19T08:15:19.582Z", "1.13.0": "2021-09-10T14:08:27.237Z", "1.14.0": "2022-12-02T23:10:07.947Z", "1.15.0": "2023-01-01T22:17:18.371Z", "1.16.0": "2023-12-18T15:52:53.129Z", "1.17.0": "2024-01-26T18:22:04.963Z", "1.17.1": "2024-02-05T14:30:13.737Z", "1.18.0": "2024-12-23T10:12:21.235Z", "1.19.0": "2025-01-30T16:13:39.614Z", "1.19.1": "2025-02-26T09:17:10.544Z"}, "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "homepage": "https://github.com/mcollina/fastq#readme", "keywords": ["fast", "queue", "async", "worker"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/fastq.git"}, "description": "Fast, in memory work queue", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "readme": "# fastq\n\n![ci][ci-url]\n[![npm version][npm-badge]][npm-url]\n\nFast, in memory work queue.\n\nBenchmarks (1 million tasks):\n\n* setImmediate: 812ms\n* fastq: 854ms\n* async.queue: 1298ms\n* neoAsync.queue: 1249ms\n\nObtained on node 12.16.1, on a dedicated server.\n\nIf you need zero-overhead series function call, check out\n[fastseries](http://npm.im/fastseries). For zero-overhead parallel\nfunction call, check out [fastparallel](http://npm.im/fastparallel).\n\n[![js-standard-style](https://raw.githubusercontent.com/feross/standard/master/badge.png)](https://github.com/feross/standard)\n\n  * <a href=\"#install\">Installation</a>\n  * <a href=\"#usage\">Usage</a>\n  * <a href=\"#api\">API</a>\n  * <a href=\"#license\">Licence &amp; copyright</a>\n\n## Install\n\n`npm i fastq --save`\n\n## Usage (callback API)\n\n```js\n'use strict'\n\nconst queue = require('fastq')(worker, 1)\n\nqueue.push(42, function (err, result) {\n  if (err) { throw err }\n  console.log('the result is', result)\n})\n\nfunction worker (arg, cb) {\n  cb(null, arg * 2)\n}\n```\n\n## Usage (promise API)\n\n```js\nconst queue = require('fastq').promise(worker, 1)\n\nasync function worker (arg) {\n  return arg * 2\n}\n\nasync function run () {\n  const result = await queue.push(42)\n  console.log('the result is', result)\n}\n\nrun()\n```\n\n### Setting \"this\"\n\n```js\n'use strict'\n\nconst that = { hello: 'world' }\nconst queue = require('fastq')(that, worker, 1)\n\nqueue.push(42, function (err, result) {\n  if (err) { throw err }\n  console.log(this)\n  console.log('the result is', result)\n})\n\nfunction worker (arg, cb) {\n  console.log(this)\n  cb(null, arg * 2)\n}\n```\n\n### Using with TypeScript (callback API)\n\n```ts\n'use strict'\n\nimport * as fastq from \"fastq\";\nimport type { queue, done } from \"fastq\";\n\ntype Task = {\n  id: number\n}\n\nconst q: queue<Task> = fastq(worker, 1)\n\nq.push({ id: 42})\n\nfunction worker (arg: Task, cb: done) {\n  console.log(arg.id)\n  cb(null)\n}\n```\n\n### Using with TypeScript (promise API)\n\n```ts\n'use strict'\n\nimport * as fastq from \"fastq\";\nimport type { queueAsPromised } from \"fastq\";\n\ntype Task = {\n  id: number\n}\n\nconst q: queueAsPromised<Task> = fastq.promise(asyncWorker, 1)\n\nq.push({ id: 42}).catch((err) => console.error(err))\n\nasync function asyncWorker (arg: Task): Promise<void> {\n  // No need for a try-catch block, fastq handles errors automatically\n  console.log(arg.id)\n}\n```\n\n## API\n\n* <a href=\"#fastqueue\"><code>fastqueue()</code></a>\n* <a href=\"#push\"><code>queue#<b>push()</b></code></a>\n* <a href=\"#unshift\"><code>queue#<b>unshift()</b></code></a>\n* <a href=\"#pause\"><code>queue#<b>pause()</b></code></a>\n* <a href=\"#resume\"><code>queue#<b>resume()</b></code></a>\n* <a href=\"#idle\"><code>queue#<b>idle()</b></code></a>\n* <a href=\"#length\"><code>queue#<b>length()</b></code></a>\n* <a href=\"#getQueue\"><code>queue#<b>getQueue()</b></code></a>\n* <a href=\"#kill\"><code>queue#<b>kill()</b></code></a>\n* <a href=\"#killAndDrain\"><code>queue#<b>killAndDrain()</b></code></a>\n* <a href=\"#error\"><code>queue#<b>error()</b></code></a>\n* <a href=\"#concurrency\"><code>queue#<b>concurrency</b></code></a>\n* <a href=\"#drain\"><code>queue#<b>drain</b></code></a>\n* <a href=\"#empty\"><code>queue#<b>empty</b></code></a>\n* <a href=\"#saturated\"><code>queue#<b>saturated</b></code></a>\n* <a href=\"#promise\"><code>fastqueue.promise()</code></a>\n\n-------------------------------------------------------\n<a name=\"fastqueue\"></a>\n### fastqueue([that], worker, concurrency)\n\nCreates a new queue.\n\nArguments:\n\n* `that`, optional context of the `worker` function.\n* `worker`, worker function, it would be called with `that` as `this`,\n  if that is specified.\n* `concurrency`, number of concurrent tasks that could be executed in\n  parallel.\n\n-------------------------------------------------------\n<a name=\"push\"></a>\n### queue.push(task, done)\n\nAdd a task at the end of the queue. `done(err, result)` will be called\nwhen the task was processed.\n\n-------------------------------------------------------\n<a name=\"unshift\"></a>\n### queue.unshift(task, done)\n\nAdd a task at the beginning of the queue. `done(err, result)` will be called\nwhen the task was processed.\n\n-------------------------------------------------------\n<a name=\"pause\"></a>\n### queue.pause()\n\nPause the processing of tasks. Currently worked tasks are not\nstopped.\n\n-------------------------------------------------------\n<a name=\"resume\"></a>\n### queue.resume()\n\nResume the processing of tasks.\n\n-------------------------------------------------------\n<a name=\"idle\"></a>\n### queue.idle()\n\nReturns `false` if there are tasks being processed or waiting to be processed.\n`true` otherwise.\n\n-------------------------------------------------------\n<a name=\"length\"></a>\n### queue.length()\n\nReturns the number of tasks waiting to be processed (in the queue).\n\n-------------------------------------------------------\n<a name=\"getQueue\"></a>\n### queue.getQueue()\n\nReturns all the tasks be processed (in the queue). Returns empty array when there are no tasks\n\n-------------------------------------------------------\n<a name=\"kill\"></a>\n### queue.kill()\n\nRemoves all tasks waiting to be processed, and reset `drain` to an empty\nfunction.\n\n-------------------------------------------------------\n<a name=\"killAndDrain\"></a>\n### queue.killAndDrain()\n\nSame than `kill` but the `drain` function will be called before reset to empty.\n\n-------------------------------------------------------\n<a name=\"error\"></a>\n### queue.error(handler)\n\nSet a global error handler. `handler(err, task)` will be called\neach time a task is completed, `err` will be not null if the task has thrown an error.\n\n-------------------------------------------------------\n<a name=\"concurrency\"></a>\n### queue.concurrency\n\nProperty that returns the number of concurrent tasks that could be executed in\nparallel. It can be altered at runtime.\n\n-------------------------------------------------------\n<a name=\"paused\"></a>\n### queue.paused\n\nProperty (Read-Only) that returns `true` when the queue is in a paused state.\n\n-------------------------------------------------------\n<a name=\"drain\"></a>\n### queue.drain\n\nFunction that will be called when the last\nitem from the queue has been processed by a worker.\nIt can be altered at runtime.\n\n-------------------------------------------------------\n<a name=\"empty\"></a>\n### queue.empty\n\nFunction that will be called when the last\nitem from the queue has been assigned to a worker.\nIt can be altered at runtime.\n\n-------------------------------------------------------\n<a name=\"saturated\"></a>\n### queue.saturated\n\nFunction that will be called when the queue hits the concurrency\nlimit.\nIt can be altered at runtime.\n\n-------------------------------------------------------\n<a name=\"promise\"></a>\n### fastqueue.promise([that], worker(arg), concurrency)\n\nCreates a new queue with `Promise` apis. It also offers all the methods\nand properties of the object returned by [`fastqueue`](#fastqueue) with the modified\n[`push`](#pushPromise) and [`unshift`](#unshiftPromise) methods.\n\nNode v10+ is required to use the promisified version.\n\nArguments:\n* `that`, optional context of the `worker` function.\n* `worker`, worker function, it would be called with `that` as `this`,\n  if that is specified. It MUST return a `Promise`.\n* `concurrency`, number of concurrent tasks that could be executed in\n  parallel.\n\n<a name=\"pushPromise\"></a>\n#### queue.push(task) => Promise\n\nAdd a task at the end of the queue. The returned `Promise`  will be fulfilled (rejected)\nwhen the task is completed successfully (unsuccessfully).\n\nThis promise could be ignored as it will not lead to a `'unhandledRejection'`.\n\n<a name=\"unshiftPromise\"></a>\n#### queue.unshift(task) => Promise\n\nAdd a task at the beginning of the queue. The returned `Promise`  will be fulfilled (rejected)\nwhen the task is completed successfully (unsuccessfully).\n\nThis promise could be ignored as it will not lead to a `'unhandledRejection'`.\n\n<a name=\"drained\"></a>\n#### queue.drained() => Promise\n\nWait for the queue to be drained. The returned `Promise` will be resolved when all tasks in the queue have been processed by a worker.\n\nThis promise could be ignored as it will not lead to a `'unhandledRejection'`.\n\n## License\n\nISC\n\n[ci-url]: https://github.com/mcollina/fastq/workflows/ci/badge.svg\n[npm-badge]: https://badge.fury.io/js/fastq.svg\n[npm-url]: https://badge.fury.io/js/fastq\n", "readmeFilename": "README.md", "users": {"nicocube": true, "farskipper": true, "shylesh107": true, "flumpus-dev": true}}