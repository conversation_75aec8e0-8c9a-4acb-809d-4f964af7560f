{"_id": "deepmerge", "_rev": "102-3e965024192ab57c82dd04a13db6ea2c", "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "dist-tags": {"latest": "4.3.1"}, "versions": {"0.0.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.0.1", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": "~0.6.6"}, "dependencies": {}, "devDependencies": {"jasmine-node": "~1.0.20"}, "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "_id": "deepmerge@0.0.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8", "_defaultsLoaded": true, "dist": {"shasum": "33d477567bb38686961fc33eb5987db79f123f7e", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.0.1.tgz", "integrity": "sha512-KQVkzRL2ZsHVdfuTBQClTN6AWmSmIUcLbNOe+DWFp/yyC/PAPG3q5Ql0C+nElqCTiqDgiY0PCE3WB3AFIk8M6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMP+3EXIGseaQ+6zwB69mYRz60TIiC42oaWlOyhwrssAiEA7CKPu6SU/2Ihg6P9pGJvKnMsH+jEf1nJHDRW/L7GnOw="}]}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.1.0", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": "~0.6.6"}, "dependencies": {}, "devDependencies": {"mocha": "1.0.0", "coffee-script": "1.2.0", "should": "0.6.0"}, "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "_id": "deepmerge@0.1.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8", "_defaultsLoaded": true, "dist": {"shasum": "268f3b16833e6399e7095bfa52e564e847af737f", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.1.0.tgz", "integrity": "sha512-ubEqzSxTgTxNm+LHOmLDYyNeyD1J2hN3L7OSb+SLC4uV097ba/kyz9P4GS4mes7XN5m+j86svEzr0Kx7RS2LYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/FOov3EpZTihzw0HYsXsSQZ1iP11qofQxo6GpGbvsOgIhAMUTuKWvf6zo34SbKf0p0tB/F4zX2tyhCs192LYMHUbB"}]}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.2.1", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}, "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "_id": "deepmerge@0.2.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8", "_defaultsLoaded": true, "dist": {"shasum": "ce1599fc2e01fc3d2f36ca37a7b1e0c7bf74592d", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.2.1.tgz", "integrity": "sha512-X8aDmNlm1eF4nw4f8tRS1nWld5qREC1pQ49h2+/suk8BcJGT/gcpylbviiimm5YBL2zijKd/9J5ulOT3gaAc1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7XeBYHWWlvYxXZ4ni1LRlmOhlycs5N+ripejRPN8CwgIhAIobmB5exVfK+7qmpYGEw/BmDYigDzrqhkVlTJEvFu9X"}]}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.2.4", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}, "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "_id": "deepmerge@0.2.4", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8", "_defaultsLoaded": true, "dist": {"shasum": "e7c3a77d1027d8105f480eacb6f92bc8e68ab079", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.2.4.tgz", "integrity": "sha512-G3nHi62hVE+MIjZ69Ni0W7XALPg3ml74OkxQ6Qc8gxQu/go3jdzenXF4pN2nnPqUHnhvwbtF8GhhDm2YVZa06g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwKSVIRqvy3TlD0YPIRnLeIYZ3AJ28XdckAsFmI80dqwIhAKed8DyHxI1SuO2uZZ0itljNq4tnIrqpp1BkSPm3N36l"}]}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.2.5", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}, "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "_id": "deepmerge@0.2.5", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"shasum": "00e09b1a27b993d2e0cad87eca0b9d0acbd7fa87", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.2.5.tgz", "integrity": "sha512-V8D0eUTfAA7L+eu8nzZ0ouKJoX7JywlZjx0KUtH+Y9amYn5m7Oi6qoeVOyy40i2lPIexsyS4dDtAFu9PgdEL/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGfO7kfh0cmE61kH2CSSrNTag6uanAHAsIW+fcXkoAE6AiAYRmJwkGWvBApXUpeZI4E67fGrq9Lyxey379Wmfo4+iA=="}]}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.2.6": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.2.6", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}, "_id": "deepmerge@0.2.6", "dist": {"shasum": "98932ed1a3f55f66629b2dad69982137552743c8", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.2.6.tgz", "integrity": "sha512-WgZxp0SOdbc2ktpcbEJVLLP5Pv0aRo/ocZP3dr85VVILiGyCLIDyiJ6kq5r2KkflLw9c5DlOljI5EfhzM6vzIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDuFDnXn1Yztlzt3PjHYZKEVKH6AynxnTOPI/RHlrqWwIgIv/ghFFTJX9Ps9a73NaopS92zcPHG3tLFIFwlLVxIFY="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.2.7": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.2.7", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}, "_id": "deepmerge@0.2.7", "dist": {"shasum": "3a5ab8d37311c4d1aefb22209693afe0a91a0563", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.2.7.tgz", "integrity": "sha512-LA8OX9LCCryJvvxKC86te1GLoUcJVsL3qiFo0rSzXeRx/h9ge0WjhtasAXX1DPgzXOkZG1K3utuRomNVAr4B2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICMFIZEJHCTA539a8JTXojZuZE2TrdLLsqxsMm9kk4qPAiBAMbnFco0mBwBqTCBZDyO9c14VFEufG/3ctMnMZ+MWYw=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "nfisher", "email": "<EMAIL>"}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}], "directories": {}}, "0.2.10": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "0.2.10", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "license": "MIT", "gitHead": "2383571036c1d2f3b6db0b4342859867920a0084", "bugs": {"url": "https://github.com/nrf110/deepmerge/issues"}, "_id": "deepmerge@0.2.10", "_shasum": "8906bf9e525a4fbf1b203b2afcb4640249821219", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "kylemathews", "email": "<EMAIL>"}, "maintainers": [{"name": "nfisher", "email": "<EMAIL>"}, {"name": "kylemathews", "email": "<EMAIL>"}], "dist": {"shasum": "8906bf9e525a4fbf1b203b2afcb4640249821219", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-0.2.10.tgz", "integrity": "sha512-u75aE0uAUogoo2u7PIN44vUfITrmtABDgy58XY1U9JZpr/2Ghhib8Y/w0Btot3tgSXNZL4iy9ROMsQ3P0dhYvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4QnrJOrUfKdO5C+JHJqKqHs9eAOi3s5e1mu+XlHlj1gIhALcX5RFniRv0aw+EiV1yw5SIudke5f6IwkCrdU5kVN1O"}]}, "directories": {}}, "1.0.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "1.0.0", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "license": "MIT", "gitHead": "0989bc45a7b9d9b8976fcdb74c6dd42bd1e6a7e6", "bugs": {"url": "https://github.com/nrf110/deepmerge/issues"}, "_id": "deepmerge@1.0.0", "_shasum": "38e04ecde5e27162a868fdd710d9970dcf021119", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "38e04ecde5e27162a868fdd710d9970dcf021119", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.0.0.tgz", "integrity": "sha512-0XGGuosMa2E2Qjut6+3QlzD1xus5IAVAddOxC3rZLs/hY/MVSFOnZWuCvNPS63p1hk3Mom6zsGeXDRwk3qpVIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtN1d07dhRFSPUuCe7zaK6xy+/TZTJ/Cb4LZbIf/9zTgIhAN+9J8/52sesm7pYeUMapTFoYT/m7gVLWEyLUsjHrxgl"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/deepmerge-1.0.0.tgz_1474939489924_0.6470265761017799"}, "directories": {}}, "1.0.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "1.0.1", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "license": "MIT", "gitHead": "3c6738f916bf7650f6dfa2eab10018faa4cdcb6b", "bugs": {"url": "https://github.com/nrf110/deepmerge/issues"}, "_id": "deepmerge@1.0.1", "_shasum": "dfa6fd845b9988cd70c86c9c5f01e7b7daa4f854", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "dfa6fd845b9988cd70c86c9c5f01e7b7daa4f854", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.0.1.tgz", "integrity": "sha512-cVlWLKsQdy5EZwQf1Q1UeOEFifaDuE4Tw6ySPm8qyQsxf6LTadBLJGiVq4nBnseoVlXYC2SJ/XI0Y08TPCmSSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCU8aSYh3sPsMLVgFaFGoXaU0fwxul/imFoAK4XZQ9gTAIhAJPxRtFkE1smaM7v5I97lfABqfE1NmzHwQBsu70GzJHR"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/deepmerge-1.0.1.tgz_1474940905770_0.07698093447834253"}, "directories": {}}, "1.0.2": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "version": "1.0.2", "homepage": "https://github.com/nrf110/deepmerge", "repository": {"type": "git", "url": "git://github.com/nrf110/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~0.4.8"}, "license": "MIT", "gitHead": "6c02da42d894c76fe280ac55817b56f42a33f6f2", "bugs": {"url": "https://github.com/nrf110/deepmerge/issues"}, "_id": "deepmerge@1.0.2", "_shasum": "33089afae45b461dbeba4dd8eadf95731a6085c4", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "33089afae45b461dbeba4dd8eadf95731a6085c4", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.0.2.tgz", "integrity": "sha512-3L8IIUdn7gZR6k0Ne4GJzj/183MakLwEF0McY97NFxkO8yV/2bbSdyobVHweuyAqTKJRPv99ncMwjItmyGxfGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICuZ2rffoZjCAx5IVp3m9wGkf6mkMDGb6BPBFJsYJLzRAiAb4Ha1MIX31v8U7b2zrYirffrs4fsiNF0rTGaRzMg87A=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/deepmerge-1.0.2.tgz_1474941716353_0.7079257883597165"}, "directories": {}}, "1.0.3": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.0.3", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "9e8e757d199c8e09963210e70082c9208380721f", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.0.3", "_shasum": "a6c8f17f0322b28afb9c97be2a6877e46a998cf0", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "a6c8f17f0322b28afb9c97be2a6877e46a998cf0", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.0.3.tgz", "integrity": "sha512-QFGT12z/JeWbJCK08FbnDW3spMpFNdK4RobhRNbQvhhnKrcc68cVULSWHQopXKg9h03VPBElykvggW9YVivyHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnZy6ByI7ezvC8xviPrYZ8C35iKWkVXJ2G1bfZJwNyjgIhAMs1EQlqalueoS6WwaFUx+hQfVYFcFmFiK6x0oJe1zHg"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/deepmerge-1.0.3.tgz_1475118273506_0.02876303088851273"}, "directories": {}}, "1.1.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.1.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "4edd3c75808ddfb4225a050cdf3e9cb359ee1ace", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.1.0", "_shasum": "23680ab55a758aecbde24faa62af6f906701a592", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "23680ab55a758aecbde24faa62af6f906701a592", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.1.0.tgz", "integrity": "sha512-20hv9jUFaJrUPXoW3AAM247i2ot3Tj6B8hk+rqdat6UDFNYjjOB/phvJybS6jan2VxHnaG863uRRTX36WlAIqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDC3mX+LA89KEMlId3UldQ71ggWa+FnRi993IF0A62lzAiEA0FQj6CNsDn/x8mKdmgHEtERRQ2kpkxsskLSC3CMLAu8="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/deepmerge-1.1.0.tgz_1475123395894_0.8471433757804334"}, "directories": {}}, "1.1.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.1.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "a8853cde1651f0500368020411ea63514e11e99c", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.1.1", "_shasum": "80f551fa556972c02ebc21f29a7377dd9e1b9693", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "80f551fa556972c02ebc21f29a7377dd9e1b9693", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.1.1.tgz", "integrity": "sha512-/gC0gmcUVsCCCT5+Af2FHk6wSS9ZvGtS9+f8EYATpGIGtbA3qO7c9AEHuXQKFiImDLL+vkxRe1rCy6eA3hVtsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDq5asRCWe7L4IUzEKdUvdqdgzGVne1MdQLP0V9DlD8KwIgDnMvxin0XycX2guYGU62x04yqhOGQK4egfvfEsXivnk="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/deepmerge-1.1.1.tgz_1476238981447_0.6945114159025252"}, "directories": {}}, "1.2.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.2.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "65fe6df6118f393315f1b74e9e6acf51c20dd5ad", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.2.0", "_shasum": "c36bf76bc1995b85d83e5b0362c97511562bfea8", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "c36bf76bc1995b85d83e5b0362c97511562bfea8", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.2.0.tgz", "integrity": "sha512-ALEY23XQdxOhVStGPUkM8YN0Xm7haF1Jj6QyAxanRllxcOPGtgWpQvNXBs+SdJrVQt9/j60L+KoJ3O96yW27tQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBX75UX71WerzV80MfQb8TBjBsG//ZcCGrtpBLcplUqsAiBM+0iDGVC9UJCyRvzTf7WrFOgC8SMm4U4l9fduUDTFvg=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/deepmerge-1.2.0.tgz_1476485483554_0.33558479277417064"}, "directories": {}}, "1.3.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.3.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "ca9c3d94d9452de576482ff013da915b706f54a9", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.3.0", "_shasum": "768379894ab39d79c9d7d82ca7150a5f03c09a0a", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "768379894ab39d79c9d7d82ca7150a5f03c09a0a", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.3.0.tgz", "integrity": "sha512-VFJsty7M/V/m4GLw0/x73VmHkkuaKwEZfoQVFP9f+4QYIgG3M7wuDgVYE5SKLkLeeIRMlCB6Duw3km+C9ja53g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTVSTi6/BahkQn+DgfjCEjs0ji8Ej1LaQUhCEX+hzDPQIgJsacCT+nfpvGFX9P6P0ZOMJfdVd8b0nEnzCDAjHAoHo="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/deepmerge-1.3.0.tgz_1478961836201_0.9682544632814825"}, "directories": {}}, "1.3.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.3.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "bfc85d8795ef397521353691c0ba23da34710bd0", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.3.1", "_shasum": "682ba92402574115b865edce525665814296a39b", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "682ba92402574115b865edce525665814296a39b", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.3.1.tgz", "integrity": "sha512-swG/03pbh+H9hDXgz1jFuM+Zb+B7GiASFhCbQQE868nVmiSdot+18WMBYlf3HFCQaPcqy8RPJZxqrcSV8VPwUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICxZwmpwoKa+uTD5iAeSQPSFKoA24dnvPI1GnyPxkc3BAiEA538llDG3v1eNJsTxEMy7xloXDNmDvekR3I8evTl7pbQ="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/deepmerge-1.3.1.tgz_1480747510835_0.4912809596862644"}, "directories": {}}, "1.3.2": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.3.2", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "index", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {}, "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "bac0e9ffe72e3fda82608527a463bda5e2eae4b5", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.3.2", "_shasum": "1663691629d4dbfe364fa12a2a4f0aa86aa3a050", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.2.1", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"shasum": "1663691629d4dbfe364fa12a2a4f0aa86aa3a050", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.3.2.tgz", "integrity": "sha512-qjMjTrk+RKv/sp4RPDpV5CnKhxjFI9p+GkLBOls5A8EEElldYWCWA9zceAkmfd0xIo2aU1nxiaLFoiya2sb6Cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAVSeuDQgMAKJlcooj8oVeIfLaLExKBW7lOxeKLWEYZIAiEAlnJAh/QfeZfExLjZdlWWzDSKZ8hKs6KiDY4KgmKrXAk="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/deepmerge-1.3.2.tgz_1485533636702_0.6398952228482813"}, "directories": {}}, "1.4.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.4.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "npm run build:umd && npm run build:es", "build:umd": "rollup -c --format umd --output dist/umd.js", "build:es": "rollup -c --format es --output dist/es.js", "test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {"is-mergeable-object": "~1.0.0"}, "devDependencies": {"jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "c946f0eed727b20efc545838b8082cb5319ee2aa", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.4.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-5BPhchVlI1riNX3KtZRWjSVGA8oTh/Fg+eZrp5MzLQFm/yYDP2Gepf77TPYoZWbnrv+Qcbi9ny3037tDVim/5A==", "shasum": "7d347f6bb4ce6d808e3cb943500d2235ce65d37a", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.4.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICb7I6c/A4uROgggfiw56vNJHJVyXchz8VLRp6yUASevAiEA0tB/W1ZbpsFI1NOMT3fGrpUV/OBf1WdBRGa4960itPs="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.4.0.tgz_1497370369778_0.06101951305754483"}, "directories": {}}, "1.4.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.4.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "npm run build:umd && npm run build:es", "build:umd": "rollup -c --format umd --output dist/umd.js", "build:es": "rollup -c --format es --output dist/es.js", "test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {"is-mergeable-object": "~1.0.0"}, "devDependencies": {"jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "ff54d84d80a3947b7dbe439b748663e76592991c", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.4.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-U8I+Q5Px3IZGigkHV8oVtXozbEkitf3Xio3/gG0FUrfZkkLjDzeNUr0UciIaPypiWTmwn+Jf9i17NgD0CALSOg==", "shasum": "543ddbccb68b67a8bad43f50f680c48fcba75f71", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.4.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8SpoKBAqof2832erDvZJe6ALlmNN4CGKeIjwqqmS6NQIhAOF+tA+S4oWacPbmiVJsj8y7deOoYK6+UISy44Ntl1tD"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.4.1.tgz_1497370628715_0.4203500777948648"}, "directories": {}}, "1.4.2": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.4.2", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "npm run build:umd && npm run build:es", "build:umd": "rollup -c --format umd --output dist/umd.js", "build:es": "rollup -c --format es --output dist/es.js", "test": "tap test/*.js && jsmd README.markdown"}, "dependencies": {"is-mergeable-object": "~1.0.3"}, "devDependencies": {"jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "1e016af897b2f9067fff506130890bbff59a7197", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.4.2", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-6nBV4+/HglUpCu9vkuG/dufqT/9rOQpiT5cqi7yTdF/1iAKuXdnBUZWh906U/jlrco6ALWuGmlkURAMJudKH1w==", "shasum": "e94266482bc9dc236ed63615fbf6743a5906b429", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.4.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDdTZHUDAo88Rw/vo2Z1JyshHmuQO+peEfTvFvz0YL1wAiAKmcPxJ0uNpAeMeQHM2d0bZWD6JuLmCcVwDzSS0DaL+w=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.4.2.tgz_1497461482598_0.5367941686417907"}, "directories": {}}, "1.4.3": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.4.3", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "devDependencies": {"is-mergeable-object": "~1.0.3", "jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "9ec020d24237b5fdeb6d5d0e0139df716325d4dd", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.4.3", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-UrRUpZKUXhj8OJU3OOcmfRuZB8h2dFy63xPUpCVTw/CvCukhykRYmb3XoXzkaGQaPOLmWSPDlqlx/oNHyw11Rw==", "shasum": "f8c9ecb11c176b3dbfc8167b58cc5674c5e658bb", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.4.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBnvI6yN6EkgENT7y6LfX48Al2apbCFffemHJ/oTyeuVAiBv7TpOxZbGhWvC+r6AVFQh/sK8kLiLNhSKSq45uAkiYg=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.4.3.tgz_1497462290383_0.6498819182161242"}, "directories": {}}, "1.4.4": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.4.4", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "devDependencies": {"is-mergeable-object": "~1.0.3", "jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "9cf79450e2213a812777fbced357ee6f03b59676", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.4.4", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-k2HDUmUNygBF82hCu9RStwgIBtdckDFsrxSxqAujuAIctxR+C1z6qDrYXpjKpVy2NrpNzFGDFdZAJ6E+L3xGWw==", "shasum": "40ef393c91af09d16a887e755337844230ad14c9", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.4.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBxR6LWeUC2uAVWfBuiGmsvMVfREq3vnGzMT4W0uN267AiEAhbdGrIOzwOemZcN1KvwuLQhyxqq0yyHV8HyiRAKgIOo="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.4.4.tgz_1497872545474_0.30614170525223017"}, "directories": {}}, "1.5.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.5.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "devDependencies": {"is-mergeable-object": "~1.0.3", "jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "67974d9230bdbfe84508d34e2fb369f035cb28ab", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.5.0", "_npmVersion": "5.0.4", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-Hm4+NyDQGgH3oYhKqR0gd99veBBZpnEUNoEfFl+3PRkQL+LKGJEBgqimeofAWzUn6aBzcaYPJrRigto/WfDzTg==", "shasum": "00bc5b88fd23b8130f9f5049071c3420e07a5465", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHgBXUSTZXNy3eqFqUVaHteI08PhTp/Cmj3jcCVKtnGyAiBg7z3VQt6gmb97L6Rg6JkH+yGjt+L6kVg3xtvqyVicJA=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.5.0.tgz_1499348799435_0.26901536574587226"}, "directories": {}}, "1.5.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.5.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "devDependencies": {"is-mergeable-object": "~1.0.3", "jsmd": "0.3.1", "rollup": "0.41.6", "rollup-plugin-commonjs": "8.0.2", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "e66e9b3345eb9a94774cbae8105ca5a019a13d91", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.5.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"name": "kylemathews", "email": "<EMAIL>"}, {"name": "macdja38", "email": "<EMAIL>"}, {"name": "nfisher", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-Ndl8eeOHB9dQkmT1HWCgY3t0odl4bmWKFzjQZBYAxVTNs2B3nn5b6orimRYHKZ4FI8psvZkA1INRCW6l7vc9lQ==", "shasum": "c053bf06fd7276f1994f70c09a0760cb61a56237", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/67dV/tt95c8rjPmvyQ7o1GIhFplSdCyQjBSJcAH14wIgc5kIhqb/YfJ6ytgkHvLN7Uuddrn9QVSHea3IV6FmXm4="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.5.1.tgz_1502834289546_0.7921665881294757"}, "directories": {}}, "1.5.2": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.5.2", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "devDependencies": {"is-mergeable-object": "1.1.0", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "94a59c4a11e21462a6d281bf8b1b13d2d5512c4f", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@1.5.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.2.1", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}, {"email": "<EMAIL>", "name": "kylemathews"}], "dist": {"integrity": "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==", "shasum": "10499d868844cdad4fee0842df8c7f6f0c95a753", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcDTpRQlcjUu362YXDHtsLQmd0q0BnYwzAsOpiE9Hl0wIhAKZ9FgwRcvPjgkLG3PiS29S52hTROFMiwjqg3sj7IeGd"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-1.5.2.tgz_1506004387614_0.020388633012771606"}, "directories": {}}, "2.0.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.0.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md"}, "devDependencies": {"is-mergeable-object": "1.1.0", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "db34d2f781d6385ab77cf3791a43942188d8c582", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@2.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}, {"email": "<EMAIL>", "name": "kylemathews"}], "dist": {"integrity": "sha512-YiTeIE16rJ/BCCbWYueeQoKkhoolGHqynDZmB03gPqMt6y3duJG6+po7zdMm7UK5o3njDm4Yyuqr4082G4M4Zw==", "shasum": "35f7ee08e8bde1173b3a529f732dcda67ce82e29", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDbFsQyIiQ/kev/5q4x0nw1e/G1bDOB4jdM57CJk8jDbAiBeJVIrp4ChurJyz8gJ2mzcQqeOq2+HTmQFzYELYF9TNA=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-2.0.0.tgz_1507561976747_0.8735625620465726"}, "directories": {}}, "2.0.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.0.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md"}, "devDependencies": {"is-mergeable-object": "1.1.0", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT", "gitHead": "3ab89f2d2c938fc2e045c4ba822da0ffb81e4891", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@2.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.6.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}, {"email": "<EMAIL>", "name": "kylemathews"}], "dist": {"integrity": "sha512-VIPwiMJqJ13ZQfaCsIFnp5Me9tnjURiaIFxfz7EH0Ci0dTSQpZtSLrqOicXqEd/z2r+z+Klk9GzmnRsgpgbOsQ==", "shasum": "25c1c24f110fb914f80001b925264dd77f3f4312", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwWa94gmYbw5+4N2ZPGxUXuVnHX7klfTKugHKni7KruwIgTX1xLHUEulnPCqBY6QCxSlhGw0gIaO+FfCxHVD5nhRk="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge-2.0.1.tgz_1509561453146_0.15830875374376774"}, "directories": {}}, "2.1.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.1.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2", "uglify-js": "^3.3.12"}, "license": "MIT", "gitHead": "74bbcca2b08d8526a2d019087a1612262d34fcc9", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@2.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Q89Z26KAfA3lpPGhbF6XMfYAm3jIV3avViy6KOJ2JLzFbeWHOvPQUu5aSJIWXap3gDZC2y1eF5HXEPI2wGqgvw==", "shasum": "511a54fff405fc346f0240bb270a3e9533a31102", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-2.1.0.tgz", "fileCount": 8, "unpackedSize": 21333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnrYZS6HYgrEixkW7VE6cJnqQZkgw3zuEZzadYMUSlpQIhAOHlLWAjJ7y1ijPHv4efAB9/cuhBmq3lfHmOm8heTyhK"}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_2.1.0_1520452071744_0.5410748798488334"}, "_hasShrinkwrap": false}, "2.1.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.1.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2", "uglify-js": "^3.3.12"}, "license": "MIT", "gitHead": "dfdb7239fff13385d44b5257f17a7423b8421678", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@2.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-urQxA1smbLZ2cBbXbaYObM1dJ82aJ2H57A1C/Kklfh/ZN1bgH4G/n5KWhdNfOK11W98gqZfyYj7W4frJJRwA2w==", "shasum": "e862b4e45ea0555072bf51e7fd0d9845170ae768", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-2.1.1.tgz", "fileCount": 9, "unpackedSize": 25281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDB+eCRA9TVsSAnZWagAAc7IP/2g5F44nfVLjqRqNvHmH\nckBWOWk/AHBZRwdZYI3zdS83oOIeBq2NWV/AugQZJIVb43VM1Yhftwaf+4ib\nfE4uWH9Dkoy18nNL4Fzo+yFsSE8R6DtLH99aTFp7xBx5mUS6IX/SnjwXrcZb\nt3BSsflZQQsdowaOkY0XbUblKqJYlnk/ltA1brX2FHsb7VfgZz/qrur34xzs\nE1ZCLIOwoEF3rlTcbm8jdJHsrs+gQuy/5UCMEsYlru87iRQuDw+osy2WoK74\nHn14Pyvc4LlgJzNDx2NLvtEFHRJkV4Q6t9mOXnzF2GpUM80TpolaJvSJI6LW\nxBcLGEl3QOf2ex+Gte+xBFUvGUMhZkTve2H8PByR1QoSslHSfdvBnFEzAmCr\n/FYU0HGwm+WJbPmgxlO1jq+b33YMLOlv5zFAOq5cKh5rK3vIZ8kj8jBdVyWv\nN9Jl3iePENUnA88yAT7VQWVVmZnJGi0JV6l+i7XoNkQ/+DhJ8kaqljrsQt+i\nlrzAieTcv4S4lAKB11JbDBkLDVGolq0L+L72G1ZabHjh2MnIXbd3NxJUbr5h\n+O+Vk3ku0guqQpqwubZ4amo4ojDgw4paIgLBZn2nq86z6qcbPDsR17CfuS89\nFI7gSFfzy8L52uhbbQUGYVjUxUgGmrOC65OmoA7x+q1M9YncdpbnKdRG8K7L\nzN+5\r\n=3m21\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDktIMgIrIGWW+oqL7yIk4pvWufBTbQjylSjOrKGpX9yQIgMRJRLkBg3UAXRKEV7Orhy6PyF9zbP4aGbYip6Le9ruU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_2.1.1_1527521181902_0.7506255634979491"}, "_hasShrinkwrap": false}, "2.2.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.2.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md && tsc --noEmit test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2", "typescript": "=2.2.2", "uglify-js": "^3.3.12"}, "license": "MIT", "gitHead": "1d6689800c35d20a4f2a1b4eb872ef4abcd07646", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@2.2.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7iuEZ5j20aoFhiiaFZiSipk23nPl+UGKsglMJ+dy27HTpQ3wm2tj2esD/UHXlrqh5o6p6MW7m+fYSUz4JvuqVQ==", "shasum": "17087b22e1dccf14310ec892e696269e85374b45", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-2.2.0.tgz", "fileCount": 10, "unpackedSize": 25715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtNK+CRA9TVsSAnZWagAA2k8P/ROLwVPDOP80OxNb+82J\nOvy2o6P1pcDiUBEhha2jeztFrnH/sP9OMiuwM/2Jb44ZXKZu9Rs5AnlaJSnc\nHnL96jzJ7KVUyiU5mzOf5rEa0LWPeztrNzILyKxI6i5J6i/XpFITRVoo4CL1\n3BhCFfCu94welg0m0os0E9BpvNp2VEEfmpr4ZSg2DP4ODhKw1dY9n9IEuKBb\nwc9h8tUMuD/TFvv5Bd3G9jXKbEQw61AZmS8LBT1E57tekG3WRlB63PWA76qY\nDDGrzXzSbPbeqMsL3vsK7p3VMWYuDL6PTVaqc0Flq1ZrCnULPXzRgvx/Dvj9\nyWxPbc8pmyjEFUjZyQvaNaThTNj7/CRlOoQJdVoc9jMO1zsbFbO8NY+qS4YD\nRYFJ3OSpXkur3eBFI73MZdgkU+pyW3Rd0Z+30DiIk/ZkMJzmg6iqR0Gt5YiM\nMc/zHgUrdAIhjT30uM6fYfyWFjcWv1N37U8YK0xpYCXIUu0j99GXiSTMU50G\naCpJ64vQ7vwMYDnVdYbatCYfX/TvZJTb04RFks5jzxHqlxAg9WFkui05aMIw\nxNR7V6vFUij0ZMsGvRgA/xvxA2ajtWcOx9irN62Up6JNwhoMusKH+ptxHfeE\n8WP9nyW8mlpsFVfJcD1e4zzq+p3CPV8ZRe5pZfIXPWJrBeMzw51T2PxPV0w/\nTZNp\r\n=c9uu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAoYorim5S6YwinX5gvNV8uXwUrG+hsw2X89zsw4ZDDOAiEAr0/k5EUJqqiSvhhKtTimK+B1S18wQdgY9MGhCzr8w4k="}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_2.2.0_1538577086054_0.4777044877811836"}, "_hasShrinkwrap": false}, "2.2.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.2.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "12.0.1", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.3.12"}, "license": "MIT", "dependencies": {}, "gitHead": "0f5efc82da76c6aed892131a11bb967e86cd9717", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@2.2.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-R9hc1Xa/NOBi9WRVUWg19rl1UB7Tt4kuPd+thNJgFZoxXsTz7ncaPaeIm+40oSGuP33DfMb4sZt1QIGiJzC4EA==", "shasum": "5d3ff22a01c00f645405a2fbc17d0778a1801170", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-2.2.1.tgz", "fileCount": 10, "unpackedSize": 26007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtQM9CRA9TVsSAnZWagAAFX8P/04GdsyTfaWKiXO0iIh5\nHSj4nVxpSTueZN2A+ZO1vCQizXpuFEV6VecM9a2jKuGf/YsYwpLOCACIl1nF\nvcAbd7B2atDypBxFKiD10T93MZU4H7flt8iYpDZJuMiqf7TCRMXgXHoLMuJn\n3z2ZBnaNHkz2eAG93geyfRbhkulMYY4Fzp97Eyk7Zz2ppSFGzvNzDxbeBSEF\n1oE5Gi1eBwafaPwd/neyhmB+UDLDa/vABkg2iWuakaMCqmwTFratL4OjjSLT\nPkfPdgEcfCpQ4dANNa2HB0apydtZflQZBlt6Kh2jRv+A5+u/xizvmdtaTj9z\ng53Kfi2XsuG72YHFXTd3fKE97UKBqp5yYAxn1i4azGxk7QHwyC9dNkwrTIkx\nzJGMyDUOhv6RtGNLiKsFVmdnakaE4sZ5WSi7BEDiQdH4IAOBTee3w1/raNka\nFaY0oHxo0S6szFvBb4A1lCUiM2m735IJ+pm6nDtq9SNYdwJ/Idk13KPTRSGP\nQrF/PRjWT6bDx96M3lnchMwHVtNqWhRbgSUeadbEEGNtDWjSA2rXsfsh7x7q\nSfQtnGUMdC+eExzQa8LbXONWw993fyGCtw0u4xNWug6fKMjHLjqgOJE9KGAa\nRWJ9YQB4tA8jt7V1WU5E7n/QTuOUhQmAZIwO35STdnXA/3gmkcAIoT0WSddj\niQ4a\r\n=MFf5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDty7lHxiAs/FYE6FkgfLBUWwC49IaMjnhZCoZtZe0SFAiBy8cdZWmRvoxHF5gv/lza1zQrDvm51SD39X5BG8b55bA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_2.2.1_1538589500981_0.6621314915792524"}, "_hasShrinkwrap": false}, "3.0.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "3.0.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "12.0.1", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.3.12"}, "license": "MIT", "dependencies": {}, "gitHead": "155afde81937952385fdb72c6fd237b634c3971a", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@3.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-a8z8bkgHsAML+uHLqmMS83HHlpy3PvZOOuiTQqaa3wu8ZVg3h0hqHk6aCsGdOnZV2XMM/FRimNGjUh0KCcmHBw==", "shasum": "ca7903b34bfa1f8c2eab6779280775a411bfc6ba", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-3.0.0.tgz", "fileCount": 9, "unpackedSize": 22563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBucOCRA9TVsSAnZWagAA7AEP/Rg5zmJ1nrq4XGUeSvwc\nMBxAcjVJCKRyI6JgcraH/S7n82qqloFIltzo0vn3jR1AsupqHuAP8kE/z2i8\nWF8ACK6bkpDNZ3Evsi/Do1OFgd6ZpN8dSUBPBsqOCKCqr2qnu7hVBpWyZWgU\nJnb4sDS+pfBMByYabsotJ7sOimPAVABvIjxFjmyjPWiK/lET3ybfSoHQeesI\nQ+zdXekxgv/QJ3fLNbUMp2HuRr7ittju5YnI9lEjvQ9e1UrxLTn7sQWIT+Dd\nEOf4UF/JvoDr/qbP6e5EW6GLTB9aSZFQ+W1wupNt5DEeRLRikynCsRC25z3k\nMcsdDf0jNC/aewrYYVCoV4pSs6Spsg8vel4bBw6ki4qT56rMpWqShmgK7ZZS\nIUJRRgbdr1qz4AAvZeTBRurkZ1UWCNjuctIsp4L2ZL6SsdI8FzE7vrW2V2NT\naWBAnSahcVULBF6NBhR+cwzCyndolW7PG6YdbpDxyaXpQk955aGwkJmHIoBS\n7I14X38M1LuVHXU+izVDDSYaEarb54882IrdLkzkNwEmhEdbzPSOwMQQB3Xb\nOiGLMsFW8FSSdD+179c6nFuJ8F9uqo+OA9c/mhSAIohEBW6UJHmHW2ahgcXy\nGhNICQFmge3kmEsVXq3HYv7g4jlvIIIZQgtCiB669J3pGvFr+c/W6OTlKC0G\ni4Im\r\n=KQlA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqyqQXrLSr7iYVVXqyWeCePM5T8Bat48PJm4EyyS6apgIhAMNrfge7gp1yiStJF027U5ClTFv3IKHkqt67QF9m2ZiP"}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_3.0.0_1543956237076_0.12593050866302158"}, "_hasShrinkwrap": false}, "3.1.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "3.1.0", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "12.0.1", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.3.12"}, "license": "MIT", "dependencies": {}, "gitHead": "3123c4ce2cd11f48a3e19e5b8804a3d77272d027", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "_id": "deepmerge@3.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/TnecbwXEdycfbsM2++O3eGiatEFHjjNciHEwJclM+T5Kd94qD1AP+2elP/Mq0L5b9VZJao5znR01Mz6eX8Seg==", "shasum": "a612626ce4803da410d77554bfd80361599c034d", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-3.1.0.tgz", "fileCount": 8, "unpackedSize": 20185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcP7W/CRA9TVsSAnZWagAAFGAP+wWaI44pz+Ju3IZQ7nFX\nqYInf8oVAP/ZUAY3138DRYLQloxAquLviHFhpsOlzL3UAdT6V8SmE02rphqs\nZsVFoE9M/DVZc2Hs8W6yM8CzGMxHNa/sG8xsyk/HkwHe6dEe3ujH96VN+uYO\n5ZVQsRPo5ZZwwz368qJe67DHQ46KUx1x6e/HMpvnjsvUISACGyq72Zx7u1gL\nu3wJqR6Jmcs4agareQHg8V67A2bRsdF6L/Fw1xhEQPS7U7eey2VpTSr0N5wp\nArRx9KJeAmT3QZuxTKeCRMC2XwIK2V1ASPwu0hn/TrRJYkmq1ktuelN3z9Wp\niOjqjU9kS/43gsG8k0mk1ZTGTNBVAf4F/89ociGcOgHDAp37DAWRMOSpKUNj\nUNoatFLHpQl4XPnLvVIbfahIOS44G+mTwUp0F6H2/afiZOtNzky0nK9X2MyL\n19+fvGRyAElZg/PRUStA54sO5Q1C6GKvcbg70t+TNmiYUJUAqv87bRkcdGEm\n5r1TcVWWvFjOqwn0pQKToTnaks8OkV5I0P1j7nQSwxgmE/S/hNL/WbL3P5kj\nSyipYMq0EOMnkkae6Z0TLao/FuCYIlADsuCXxd5N0JFg5yHzpWwP12vcz8iz\nmGxn5eq8frokM9B55XUkZVrvLxom5a1cc2/5va/+6NZP1Hvywx++PfTdO97W\nylMQ\r\n=gYas\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCitFZLFdwyGmhPPB9JSLzw93jqV+Ffosme7AVDBhuSlAIgICzsXVwbBuEUjif+rNhltapc0iqGVqviskWkcpErVqU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_3.1.0_1547679167125_0.8424081721966308"}, "_hasShrinkwrap": false}, "3.2.0": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "3.2.0", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/umd.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "12.0.1", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.3.12"}, "license": "MIT", "dependencies": {}, "gitHead": "5f5ff83af19640d616cc2513a344dd825453430f", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@3.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.0", "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6+Lu<PERSON>G<PERSON>7QCNUnAJyX8cIrlzoEgggTM6B7mm+znKOX4t5ltluT9KLjN6g61ECMS0LTsLW7yDpNoxhix5FZcrIow==", "shasum": "58ef463a57c08d376547f8869fdc5bcee957f44e", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-3.2.0.tgz", "fileCount": 8, "unpackedSize": 21701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZvWSCRA9TVsSAnZWagAA/aQP/iWn4HDQWQjqDOAynomm\nfFtCRWYCgWcchNGxynZBMDghQGd/0evev7WvDxk/FRGiYRkfYpsk3FW0jb64\nCNd/EYmh9g+srUWRxYKIk1BMAlDt8GMOMPYTdS6+tuKCNpGxaI5R879d2Qdi\nUrNbySLT5gXZYDRakl8m/N0uWZxEKS3oykTkyhqIUh0R2L3XqJCWRrkJx+Q7\nkFvT2iVDanHPEvgytnWM0QobmiD87EXDIXwRD33nsymmhehQBCa8vWtYBFS6\nhqrN8BBri7dCf6VTI5Vf2IkPEzSGBXod/b8fo4KgqXlMbc56agHP2TTensWO\n/6n2DvjXGrmOLZfDfYmCabYVvd3LiSd38F6KNX59XuJygqOSqp0cgCXRXEEh\notiv3mSCSVcU75Erty3kgfnY0TV0G8o1pi7r1bMiS0AG78rAI3gKFl0au7Vq\njh/c7X2gXIDRtEbMIVG0sgQpAmggY0pF7KtGR67qJHl9APk88eQLJpAgPnNm\nvV+ev87SMqyWw24vU0+CA4V3UM92g8bY9klxmbyQrLMI4EEewEtUmd/k/wvH\na26UWWi1XmYRoZk9bxc+BYtQTcVh9GXCWPoNzxTEranEmpd+TxhNHxzfC/qu\nu3o+xPTClWK5ElQtu5LM5H8zha/mgB6CpGchLOHiQxCUPD7R/sPriuiWpoIo\nyH5k\r\n=JcoH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCS9yNohPxhZ7Z+MXa4DuoKdQRualbOpEMK72FW+4+CLAIgLMaYy0DEIW+4eQVbimJZ3xCF8v/295nHnIZidRykUQs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "kylemathews"}, {"email": "<EMAIL>", "name": "tehshrike"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_3.2.0_1550251409807_0.07249680803627911"}, "_hasShrinkwrap": false}, "3.2.1": {"author": {"name": "<PERSON>"}, "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "3.2.1", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/umd.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.49", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.1", "rollup": "^1.15.5", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.2", "tape": "^4.10.2", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.0"}, "license": "MIT", "dependencies": {}, "gitHead": "92aa630d75c2431b87cbfa69eff11a49cd5aabff", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@3.2.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-+hbDSzTqEW0fWgnlKksg7XAOtT+ddZS5lHZJ6f6MdixRs9wQy+50fm1uUCVb1IkvjLUYX/SfFO021ZNwriURTw==", "shasum": "76a1f47854bcfcd66ee9a948d110540a8e12b261", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-3.2.1.tgz", "fileCount": 8, "unpackedSize": 22015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBABgCRA9TVsSAnZWagAAJfcP/j1OOP7Reb5FhFJp63ye\nzp4ab4kTW/LYvm+SOijzsE/IbLx0louw13QlmX49Ic/wDZR3foidxjkJJ7Ey\nLVNfUhle4yAiZJNVIvo5q8ETEV1zGIS3Hy5FprmfBU33MpQjG9MrtxoetPzM\nkNzDDxocHjQJMSmNyG0bXAnDDt3+0BjhHpvzGx9oZtLVFshzZzavWtip/zVB\nBrjdrTX9KQGrZTFOHfSOVC0Xydbwrvir0IROwF9Xi5r5rJPZtMYR2TnjOC+B\n63sERvPQWOs9WiSSIUuGgN15Kx9mWXiVLZtpkj+9B4O714lIMW8jwpIvq92b\n32CkviiF4yIFUNllYKtn3G9CiYz1tcS0dlCnwOkopjNSXhUHycl4/z4zNAGe\n7I8SFjbgNB3yl9LXfHzXCkpL/SllCUFLg89F7bOUxEb4wL9V3V09IcIjpkJ6\nNAUkaclkDfqZUXJeNMfocbv2BlY3Mda+41y+PCKOTzXWD89QGmpVjaiAnH/8\nCEc6WWR2WqTOsCNEQDIv/0VmsAjzU0k5OkzPyYIDCyxw5uXLzJHk9xhZYNYX\nyB7e/kSdEweQOPamOUDCIIrHIV3QvUOdYcxRl0MDLC2bzxDDtbUAvkDvQpzP\nm9gpXXyOYZf7WYSufPEpIb/naa3KthDUMFB+4mTPtNZYF2zGB2FOHDhirTTG\nCbY6\r\n=CU+S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2ubAAfM55R3WoEsPTU83X74U7FpKLRzItNtax9lz02QIgCL7hZcS0hLkKJ+Szp7rM4AplBtjdbbvHAVyFEznG3ZI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_3.2.1_1560543327599_0.5767263875788669"}, "_hasShrinkwrap": false}, "3.3.0": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "3.3.0", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/umd.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.49", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.1", "rollup": "^1.15.5", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.2", "tape": "^4.10.2", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.0"}, "license": "MIT", "dependencies": {}, "gitHead": "58cc468e61431bdf074a8af1b7f145f2a94208e1", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@3.3.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-GRQOafGHwMHpjPx9iCvTgpu9NojZ49q794EEL94JVEw6VaeA8XTUyBKvAkOOjBX9oJNiV6G3P+T+tihFjo2TqA==", "shasum": "d3c47fd6f3a93d517b14426b0628a17b0125f5f7", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-3.3.0.tgz", "fileCount": 8, "unpackedSize": 22627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdC6reCRA9TVsSAnZWagAAiosP/RKjcxtKMsyprGt3Q6VH\nB4wPoFzy4gJoZPLX+hQwRKkOjFchh6ejSjsvnWTSbLNJ4Y9dBBOprko3h3qf\nfvq4PbrQI3O7Lmaouh9/uY5N5t2JcIb4b8KbORh0oJbSnxYpsioVVXkkLBWP\ncsaq2E5ZKapdcop8V/al4GtDVjKxKsTz9j+f/FOevGgFoWa/i75Zsi7mDZ2q\nYUknwipGhUvyKPEB30rDHevDxgd1WVAtkbDZr83lXM8QLriVT2PtLZD7EhkE\nCnozzlCoGI4lZqsBH187j90650q0APKnW0LuWiogZN1zB5jo4JGxT17DBIHU\nls0Cb5VIwCgXUf59PwGhph4klsqv2T/d5+WM/GuruRirbw719cyxpo5FS2tK\n73nqp5tNG8Pi/c5ILH0xXtojYdhAoIgb2M1az5Z8AUcLvyhkHOh1ifAWNHa4\naPxcoscLqWYbSMeg7nCkuoUi3B/tq/ml2VTsocN6ST7WjEuAGYvAjIYkTP9a\nShrtI/QPSIXpMUfEOEMHa0I3YVNRkDL9tnQAYeeKqa16udmbFpqdBERrBGe5\nQK+Us3a2Cww42UKqk+rYhIeM2Km3k9wNXpB7XUf+1x7Xhr8OJVwEbOu/3D56\nkRe9d3zpDnKx35tBeIIJrF3I37r8luFUjiJmhHGv4QIsUL8wAdTGhtr+88zm\nY0zB\r\n=F+Z4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB8tXZgqjCmMOHA/SNSrFnYQVazGDZYbdV5aVIm+lWlbAiEA7cINUgkdqy5uIb1HXZL9+l+rHh+SC6osjvrp64KOoVY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_3.3.0_1561045725574_0.38999368394339506"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.0.0", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.49", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.1", "rollup": "^1.15.5", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.2", "tape": "^4.10.2", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.0"}, "license": "MIT", "dependencies": {}, "gitHead": "ad8cfddaa8f4b79f5e2c6943445a8b42377ec495", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-YZ1rOP5+kHor4hMAH+HRQnBQHg+wvS1un1hAOuIcxcBy0hzcUf6Jg2a1w65kpoOUnurOfZbERwjI1TfZxNjcww==", "shasum": "3e3110ca29205f120d7cb064960a39c3d2087c09", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.0.0.tgz", "fileCount": 9, "unpackedSize": 26085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI2YVCRA9TVsSAnZWagAAyWoP/Az4I6yePMGLXVfjLptu\nbNcBhq1MzZhdtCzRjgVhc3/mleJ67mutdCF7uLl9SjO71wS74vg6RhWrYa9l\nQXvch6Tvgy519rdGS6hLCAYgVsAZZ1+5ZW+0V4BsHeVgc+cR+5oC+0ArL0Ty\njsA+CJu+rYPngOGw4m3Il4ZtXAdOdPKPhv/A4Axm/ewe7DL5/NLQ9a+XwTxG\ni3c+/ThzmYJnFAnkcHrc028NRDUlJlP13DxCAF2rFIF5Abc5G0rYpkpIrZQU\nIyD6opWKGhNRSRw2pJNcIe0J77CYkh7Wj2gcHnep9NHy2j0AT5Rj6eRcMRqf\njTtnw8Y6x9wZ+kHxlR7CzbPbfeXOjOq9OiW9ErK/kVSh75KzmjeVhz/6kjLG\nLAqCZr9LrYuFR1OrzYk+OzTltyKZ8g/aqvEoQ80wDfW5hF2ZuOsvRaCNUbuh\ncBtx0j9/B21dHH3GAoFPhD/rTPsztEwjokcnSwMTcwUotG68x3hxZOV8D60o\ngwI8uOZHh98Os0BUY+oAoGI8DZN9Mzt5Tld7WkMI1e2BTQ3NgBs2g0yQKlcQ\nY0wnEYXDXj6hRbdaT/2AgoYp4nzOmKZ0DxNKlqQpQgjednxGadZqjIucSrpT\nECDDwH6xrKsNJqfGPJvxZzPKRst2I0HaD3zfpTsPDYswDOsl5IYsj76dc+tM\nWxUe\r\n=fsQm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1fvwe7jZ1j83CLBQpCnab9yQ4QyyOQIW+VXdjOTGCJgIgaIz3sYeL6h8e58LCbvO4mO9OO3EuMEZKFyHtc+eFCOg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.0.0_1562600980369_0.5611755420200399"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.1.0", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.49", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.1", "rollup": "^1.15.5", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.2", "tape": "^4.10.2", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.0"}, "license": "MIT", "dependencies": {}, "gitHead": "01d27e2fb92fb7d6b278c091336894c02a38769f", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.1.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-tJlmKE535XRVBrzMRMqlm4V2S/l3yO/qr9Ad0MUqgR5jh9epGrALs8Q6+pROMa6ntDieAlnmYM/cDFCtNjp4+Q==", "shasum": "f4647b378c0a9d32965470e477c01ae55451b19a", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.1.0.tgz", "fileCount": 9, "unpackedSize": 26908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm1BSCRA9TVsSAnZWagAAoMoP/0fL7onZ4p7kGbbmVG+Z\nAajO+b6YdcEvqC3Rbk08YtK4XYnbW34V0tUtnMpu2VsGBeVxhAAI1GTcTOBr\njU/sirWP4WJxeOT1qMcaZVT1zG4NCPsp/PuW05+ATxSvBCyFZJInduZAKmZ1\nR5YpQQ7FuYu5lIQ6ZCOG7j1DD77G9Nrq5BNjGvaln3OmCU6BbBvMzmSMPjAr\nwpXRA/zYelRQwImaqUBrfmkKmdSQi7VOn1s5Br8h8GrRetwlIHL8LFrymVG5\ni/jNDL8kHfEvnNaCZwlcq5T50FVV4KzHbdh0TV4idcNtdcM7whMNOC2i+VvZ\n9zWFeBROpk6g797UHR08Z0mzHhtZcVW9O5oD69sboNYvdhAMPC3ezvS5zNON\nXC2fEdXA5KK8xpd7SdYEl0BzUW8ZNsTVZ0bN95sHZcdeHqzhniieBIzdUufC\nfuUiWpjF4AlQecg/QphVrOhs9iXgDRUW03n3paaXtR/yJSHBqXXsb7OhDRqA\nyO6K7a8VEuJZ3OvrGYXD8f9Sb8N9r5gYXlgNsbiHq6Jt8e1a9/AD3hoJcFgV\nlqFViQil/simuZ2+TP/SVfDtGnjovGUlcptFbNjiuW7Dmu733PwJThC5mtJe\n6PS43/LCiXOOmCa1271eQi4d+6GhG87OtM5s5RmMvpmFa8YixClgWoynCL+f\nK+9D\r\n=v8ev\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICODuEpISfe/315z0jiZ4vYw/fIvDtRvSs47MtrbCKVGAiEAp6i7rZHtRalJTbRSP1YDuWoxcXrbX0WxNoVeX6M7bJ8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.1.0_1570459730257_0.9985114500498591"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.1.1", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.49", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.1", "rollup": "^1.15.5", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.2", "tape": "^4.10.2", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.0"}, "license": "MIT", "dependencies": {}, "gitHead": "426f21deea938ff42fb8a3113bb0fa3d09774231", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.1.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-+qO5WbNBKBaZez95TffdUDnGIo4+r5kmsX8aOb7PDHvXsTbghAmleuxjs6ytNaf5Eg4FGBXDS5vqO61TRi6BMg==", "shasum": "ee0866e4019fe62c1276b9062d4c4803d9aea14c", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.1.1.tgz", "fileCount": 9, "unpackedSize": 26476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnKXdCRA9TVsSAnZWagAA42cP/1BlGDqeV7rcvyNIPeKq\nZoZGyuflSmoPHB1stnDAZpohv3l2qLWVLnx1k5o8WEWh8YaViVsEw8HN7Nog\n3IXLBCdIudJyXfShhoEsw7lT7Om9l4UN9Ky+ltSo+iOjSNxHF2dRvGjs3Eom\ne5wtM3tW75fd630uQtLASNrlAef4zA/v//ejYkUaj46Wzv5wz6LC+UL1KEj+\nSmtrFivmxDyUTvkqJuZZ8bmpYWXuz7dwNEzsTNv/VzLKA+lTE2UyIPLCfOLW\nMln+CiJSC3QTGkFW8pTo8CF/DapWY4Z3jMpfDtB4xUqhDyGBpZWJpDXM0HI0\nGfaI54hd3bX9R4jW6SSXrQVP/4HIqr1H9MSUF1FadQg/c+SMXyuLBBvk3KCS\nf9tOw9PM4jenDlgOCfwFxG5dmkw2Beab0509SWLMWMiGVZZ/tkzLC012gXqY\n8P9nob5/3bDGYInCrHeNDrKvv6RgkDUWoMhCEF1baW/cyg3QFPeOnb4izaq1\nJwrUklQFDi4DRj0PRrI2aYnHQkuvm5LPPy+NjUWXWDt+z4UNGJ/f0WZ8cNVj\noV8Con8lOTcXxq1iuXLfItWnToEu/b1KdpU7wDQ/l/8QOEQM+/vLnEDs6mQ2\niX4gZ9VLpUfHKio/wisOHda1v9Kk9wBRwtFlZbzjb5vKoQrXbpgqcOObCbF6\ngwVp\r\n=crt5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+r0oNGh/rAy3LzPVLtklxQ1SRuOuNAq1gNspGwrrUPgIgGe3zA/w5LOV045pPYkrQojJc9WapWWiNxGrNKSnBInw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.1.1_1570547164676_0.4132408746391898"}, "_hasShrinkwrap": false}, "4.1.2": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.1.2", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "license": "MIT", "dependencies": {}, "gitHead": "68ccfb2a0aeb1bb7daa3844df4de674f8ccf0763", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.1.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-9ZXh/8Fp+ggcscoRg324S1aNdGSCl2X1P8F1FPTHbtq3RAxy9ECbU2Wgy2J12r6x/O0BStDlkzcpKK/HptX+Kg==", "shasum": "73ab0dac658d5e79baabd8b81ce93f9fc9026e89", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.1.2.tgz", "fileCount": 9, "unpackedSize": 27082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrceSCRA9TVsSAnZWagAAJ+AQAIwvv/AlSYq7snuDxZ4H\nfPUrbYACgBliVaYou9xpdY153KQzNhe6ViLzxH1PZTGra/a1bJt6H58ceCV7\nh0Gpd4qMjSnowxJjrL71fmUfaFByQPrFcvT2Oh+oyGP35n1Vgz3eHDp4VqMt\nsledp/zEpRT6AGLjHwzXl/HOLuBkk0WrjCiRa+cRcUHnvI/XQPZaWhOVhwhn\nPiIZXcmwONsrZeTMugaq4oCy5sFN35hZTHS+ncCeVLJ9BMgmHWOMXla9gZrH\naO16TMNH0eVXTOFWub8+LWIHPFQFLegW6lzCRe+eVIVtsf0uLc6e1r4PKX7U\n4UzHvV8/zhbbXu7+ilFHuN114C3LXGQNWA5vKmkfWfU25ugGXCyySjnuawTX\nMsRUiD/qFlKNlYUZfk/p8IQ8mCfHibl0OGZ49V7Q+edH2vwMxTu1N1l5VESE\n2fB2fMj1s2OE3r1BNqq2u8TT6+OrBX4GM/AF/18dfONF/NblLR9vnXb4heyi\nn8BQKeGpbZ0+USu4ETekDPBmg4Q0tMd37evGJ+BZ6rdTQskLEETQzA21Fu2K\n9byNvdxYYtY8JQKgvWjumZLRwgXW8jBT4CdDqs0vyalbKO3p9z6rncqrFH7Z\nZ3CaquSn7/Y1P+nvGXvOkwjwRVJdOosaZtNAe9IIurWRwxRdIw2fWewxTl30\noP1p\r\n=mL47\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDP2kVzYv6a2+2zZK/DOpknItsKkGtUUX4/YpsV0OUk0wIgHcXLF1T5E0EowmUI5pcaAW2CU49b5rhddiC6gNvVp7c="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.1.2_1571669905598_0.09036994181442237"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.2.0", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "license": "MIT", "dependencies": {}, "gitHead": "5063a964e5dfa6cf8d23fd5fb446b60d6c735742", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.2.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-/pED+kD8V9n15L1lon8DXEiWLQMW4tTiegn1kIWIQ+DBudOkFitz1cfjWQiSeKMPBQOknT3LpueyAmMVJ1Ho2g==", "shasum": "77a97af6746882cd1ed85d4b925be8cd4120b630", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.0.tgz", "fileCount": 9, "unpackedSize": 30037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrdFPCRA9TVsSAnZWagAAqREP/1s5KrTsDSLYNHqLWYZh\nJlI6dboaPbhmPaQ1hQE2Jw7RijrGuRIzcgMxylYnEVaslt4/sPWPXMdD4DXn\nvGnNL+q9WMqsSHBeZMWzqoV39veayHdB5tE8lxuy4xAEJKSDEeozuMGFM2D7\no6dIlajxcteyh9sVBBLckTxL+dGmeZIgyDGlZGqyfVzVfj7uK8L2q4Md35WU\n2VoJ+syKFEZNcVxSvtB/8a8i6U3jsz9sk1E3HppMjZzZKp76RWJaaRL2+k2I\n28zAcR9Y5UA8SWnT46zZap+gROmMGvKTOW926GeJkNGiGE/jA19LkCmfia5A\ntTczuxgWg0EEoKE4TOmJQk459uuA3uLDXpDmmVJfA20iP35vpIGnooBcD5Lu\na/j3v22vD91heK+jPg6U8jtZmFuQAghI+mqasLRJTMjGX/No7efJLZ/Q5um5\nd1ph4oIxaaDcx1rtn/Em+Xfv1hRShyuVuvQDeMnCHVtrmQoo44QdY0f22Fbo\nW2JcfHBEt9NzXuRJG0FFEL94iydI7g/xt75qOQh18mPomHKyGNxLzDpr75C7\n0BzG2NgdM131/Q7vjUq4C/cM+OUy/Xa5e8wWoYnEpTO5MEQrWBOXlc0CZ/2w\nKYvbQ7gtjR1aAYhRCOzrn8rERB+kk+hEIQeekwnNRvRLBdcEPEhXxeJ/pIH2\n30gT\r\n=4xKd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA+MkhV+UpKo9AAm1YPGXZeYjNzX0bGOpyh6Hu4manLaAiA1VmgrJuiP/LQr+xeE9br7RmVbmPJGRfF2rMEmXthDow=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.2.0_1571672398232_0.8112551674030462"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.2.1", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "license": "MIT", "dependencies": {}, "gitHead": "0a890fc9723bb07f1b72ed17360e11bf4bbd0b71", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.2.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-32P7FIV6JKt0hPMFNlWFytzVGpppYHFKdnhFUEMXheWc8Lw4HnHEzJa5yxhaQedDAXv2SI6zD7+UbqnC5k9g9Q==", "shasum": "018a3e5dfe82b95e35e36a9a29ba15ddb194e40f", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.1.tgz", "fileCount": 9, "unpackedSize": 29909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdr33ICRA9TVsSAnZWagAASZIP/1GFRKL/yMliPzbOkLpg\nU6trVZxiFMZ4Ny6RUWCNXMduIk3m1ToEzh9Ho+Ncg51gns+xThVmlNltnSeU\nSmsWQjYC6GtAdY9qcPsgZvfcA2RaMJLBE6CVqS5gUI5o54BfgXVvYyTRAajF\nyqvjLS1e2afqyXhLwRoR9Dx0nIMIZpmhVXHry7qjPnArSjHLzqSUpGo9UOqr\nXpQeVvW77PVntAC6st26QLWvn22PHDIWWBeFjxPQk9ls4Q8mAgvbWtn3oE4I\ncxWut33SNKhfaCZb7crg6eZOwiqUNP9U3oL1xDoJJ8tsqAByPJ5Mw0A7qBN+\nMeEGVduv7xGZSluR5YuvoTNfUsT+ldMfKjNI4aq+cZTsYopCNDWUPjfj6hdI\ni4wYh9xbvMvqEAGR/KU+5jT4LTsSdfW/sL0PWLF8FpgvhHabJKIBP/xpcsqz\nue6biEA1kXg/SxgemAepfJ7lQOBouqaIJHuE+YcX9HoQQhdtMjQLB4qqta+h\naUkj8QS7VeLlG5k8Z0Zdtls7J/LDQd5/c/2dUcinNt7oi2mym27X65kWbDH7\nqAbR0l303Lu4LWQz6tieyo8jeNiuuADVWFwqvyOcVq2z5fALL30q05mSbl1s\neTvDQAm02JCXQhWmAry2EFAWQSza2I9hM5T+dGu6bDyRpxxWqGT8xcOBEyKe\n/LrS\r\n=SerG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFt56yfuycj23GN0dZyamaEMr4WmoH3KfMf1oBGYniI1AiEA22EGgBBcwuEQ4pilAOws/77ZwHHjErtj5sGkvyUPras="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.2.1_1571782087892_0.9235726712095524"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.2.2", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "license": "MIT", "dependencies": {}, "gitHead": "e9c9fec24764837dd1ca178f86e8a5125cb93653", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.2.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==", "shasum": "44d2ea3679b8f4d4ffba33f03d865fc1e7bf4955", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz", "fileCount": 9, "unpackedSize": 30093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdtxUUCRA9TVsSAnZWagAAF+oP/jkrqsORd0QEkk1qJybH\nygSuyv55QwsaGj7IM+bG+xFVKgY8+tm6j7dQwLyLwsU1ej43wFqtkHWalQVp\n5WAzhkFCq83Dy/VvLncOcJhz7+siR21OgS07mszRgAQr5JUxe4qiXmWhJlOr\nLA/8diD+9DW0x3PuvtZOubxSVVXYCY08k2780NfxGVlQxTeWC/6Uk6KWWrWt\nDFRpjxZiJj90+Ym4mEjfZdUih1EzFYmhBwM1qWvUapFijTGsomb4GcZ+ZEcM\n9lMxFCotOmXo4LSZ0s1RDUUtxcNaiDG665B7Is1lC47fHj+vkQClnSDGnOdH\noLknvLyOLGSAqLxh3xXDlZxYtTxsw0c54QbyGmS3Ml2yx1aB1GtAK3+iyjgr\ne8dAOcfM9mF9M31/MhTzGHve5/6M2PddEOORnFm+tSfzlVD6WC5+muCaI4CC\n1fbvjXHteqNS8b+9M8ZZisPYTJ+48OlEGKD5Axq0pEzoUAc5+yuV8qgBnJQE\nim/s3YjMgo0TrSQflgSHmhmiEHH6TBy+ehW50tcq8FcQBod0wxM4Dhd3ePvf\nhp37Z4eI2tiI63wT3VNyVaQXDPw8gLAAurzaiFQnF2YfOcOw9OXMDpGPUdwE\n2rl7s8BvpdUcWGeGFGI4GlfQA6JJuyfBy6jxhwANU0xreZL9xA14A/HDiIUF\nesm+\r\n=l1Mm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAslWvwhWktWHcWkWfZwgIdFYinrBBQ4CAzBI1NF65/LAiEAnQ2Wtq1Wr+KLrLK81jW5OclcI1Xbh66ehGQQqJ2rV64="}]}, "maintainers": [{"email": "<EMAIL>", "name": "tehshrike"}], "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.2.2_1572279571933_0.6083630202992556"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.3.0", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "license": "MIT", "dependencies": {}, "gitHead": "234ecd86d98cc3fc0b82ea70a991cc44ad751b09", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.3.0", "_nodeVersion": "18.2.0", "_npmVersion": "8.1.4", "dist": {"integrity": "sha512-z2wJZXrmeHdvYJp/Ux55wIjqo81G5Bp4c+oELTW+7ar6SogWHajt5a9gO3s3IDaGSAXjDk0vlQKN3rms8ab3og==", "shasum": "65491893ec47756d44719ae520e0e2609233b59b", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.0.tgz", "fileCount": 10, "unpackedSize": 30447, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBppFSAYksp8jhLJpr28GbHvpfNEhx1IlNpCkyDyDxVmAiBAe0n7SRdrqTLWemt4Op23HuneE3mfCRIii37QJiWvcQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1wnmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDLxAAoNw4NRS8liVWf0c/oisphskF7nnCaj8GRIB0z+yMjgouE5AY\r\nM3eLlGh4ixvDPqVJOSSICCVEwc3C6OCvgvirZgzdyTQllrThHzenZJS1VbXB\r\nbAdu4cqr+A5XLWQMtJe/thaWteuoUyR0R+D+ANuu91OVT6c5paQEctbt/Inq\r\nKsWDX37SoTW3WnRJXKg9MOH4fgOyVf2zzx8a2gywKziyrPAU7oKgi6G4NFRn\r\nZTVXPTTLgMf8sP4ohZAQbrLXVHmShIYhkQecLBkP9qUtunTAd4ELgYfdp8rv\r\nwUHPE4WvB+Ws2KJyHX0T12GS4p0zLflvPItSV/+r27BpXj18Xb3tJTkREMJT\r\nrQdU/LzLP4mxmrxeyE00pFxgAJFx05J4HGqXKtwtbgNupcUPNUSO1R/LS4DE\r\nVzkTZLD52i52qs5x532VrgC45JqlMekuo8zz1kHiQzL6ZYAJDAYgRnGW6sWi\r\nch0c0SOVUCUnXf6TF0BJKrLez0NzoO4tlqPo+stICzd/ZYa8hRP7AH2aSObt\r\nK4EJUci67pheK5rJG1mGMDUW0qa6fwTWnCuq4wWFyxtOyyQMAFGV3y7uwXfe\r\nlOURhMK3Og9erpsPezZ6OWYEg+y8OJopampWPcs/lY01WVQ/KBgADEuJKHC4\r\nmE+0HpNk9W7sOW7cpXyVzwnAiCdcuXSrh64=\r\n=5G+K\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tehshrike", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.3.0_1675037157937_0.18723890001505605"}, "_hasShrinkwrap": false}, "4.3.1": {"name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "4.3.1", "homepage": "https://github.com/TehShrike/deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "main": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^5.0.0", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "license": "MIT", "gitHead": "5b87756a5671635679001cbac72aa42f23472c81", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "_id": "deepmerge@4.3.1", "_nodeVersion": "16.8.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "shasum": "44b5f2147cd3b00d4b56137685966f26fd25dd4a", "tarball": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "fileCount": 11, "unpackedSize": 31157, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN993WZ4bZkA9ji9rC0yXDb/eFJLDqMXftQaUQp3hA9AIgOqjV7vLLdNtIfYCLlcugHmhK6t3rgYR+AbGZf0GzGPI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkE58wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwgA//SUBIsWdXabo9mcwaFaK0sg1UJMFXp+Z1PLA0FB98BZ7Kw+23\r\nXByEFVHf5SH2X7EG8VHMMCa4mdsqWUNP6Qz7UGa1oQebTXnTN20VhkBaAdUT\r\n2Hr61AwJHMNGaK098Vx4rn8OBLh1SDN9Q/FKIRU1M3PDo8p8dfsCs98WhLmj\r\nUtQzfb8AXRUmYf6A6vJaIwmShw3pJVU8a7JLFcl9Rgx7L5L6h/xjz2oNEb42\r\nwBYz+K1R/PvbhF2Ejkjmj5R0NSr8wSMmizK29uGXvtJYvon2kIqNPyPfaCaV\r\nVBCiHvtJdV4xCRUduPkXlHPMrgC0Xp1G+AoJn/Banx1OZFtLDjZa/ZBX86Am\r\nwPRAqr5ZiCaWzgC3Q8o1/qRsSrpdAO7OWRfzGVBqf10H4HdCvcnZzeyeSXQw\r\nCEnNT+zEsoe08upOGW5CCijrr8hq1Pj7LEtfyZr7nGmkWl1+bG2f6XB3VbdK\r\nRzWM5rcNRKJcDaCNMRXrzyKZ7eutn9Mzj70fRfUR1s5oqz5ruGAMHD8D8kx7\r\nsR/9P1u1a8E00oiQGMKHMIyaSVEvEtGGgLoJ8MXIJ+qArhasiWIzMY3eFcAf\r\n6z4MRfo0Nf6/m6WM+mMdwsitug9dVzhPa4+FFWWNNuhO0aVkXr9X2+A4Fjeb\r\nOUwt3H4PdpOegx1dVu7/IEYNFFEN0HmuZ1w=\r\n=A4WO\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tehshrike", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tehshrike", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deepmerge_4.3.1_1679007536333_0.9033742187586644"}, "_hasShrinkwrap": false}}, "readme": "# deepmerge\n\nMerges the enumerable properties of two or more objects deeply.\n\n> UMD bundle is 723B minified+gzipped\n\n## Getting Started\n\n### Example Usage\n<!--js\nconst merge = require('./')\n-->\n\n```js\nconst x = {\n\tfoo: { bar: 3 },\n\tarray: [{\n\t\tdoes: 'work',\n\t\ttoo: [ 1, 2, 3 ]\n\t}]\n}\n\nconst y = {\n\tfoo: { baz: 4 },\n\tquux: 5,\n\tarray: [{\n\t\tdoes: 'work',\n\t\ttoo: [ 4, 5, 6 ]\n\t}, {\n\t\treally: 'yes'\n\t}]\n}\n\nconst output = {\n\tfoo: {\n\t\tbar: 3,\n\t\tbaz: 4\n\t},\n\tarray: [{\n\t\tdoes: 'work',\n\t\ttoo: [ 1, 2, 3 ]\n\t}, {\n\t\tdoes: 'work',\n\t\ttoo: [ 4, 5, 6 ]\n\t}, {\n\t\treally: 'yes'\n\t}],\n\tquux: 5\n}\n\nmerge(x, y) // => output\n```\n\n\n### Installation\n\nWith [npm](http://npmjs.org) do:\n\n```sh\nnpm install deepmerge\n```\n\ndeepmerge can be used directly in the browser without the use of package managers/bundlers as well:  [UMD version from unpkg.com](https://unpkg.com/deepmerge/dist/umd.js).\n\n\n### Include\n\ndeepmerge exposes a CommonJS entry point:\n\n```\nconst merge = require('deepmerge')\n```\n\nThe ESM entry point was dropped due to a [Webpack bug](https://github.com/webpack/webpack/issues/6584).\n\n# API\n\n\n## `merge(x, y, [options])`\n\nMerge two objects `x` and `y` deeply, returning a new merged object with the\nelements from both `x` and `y`.\n\nIf an element at the same key is present for both `x` and `y`, the value from\n`y` will appear in the result.\n\nMerging creates a new object, so that neither `x` or `y` is modified.\n\n**Note:** By default, arrays are merged by concatenating them.\n\n## `merge.all(arrayOfObjects, [options])`\n\nMerges any number of objects into a single result object.\n\n```js\nconst foobar = { foo: { bar: 3 } }\nconst foobaz = { foo: { baz: 4 } }\nconst bar = { bar: 'yay!' }\n\nmerge.all([ foobar, foobaz, bar ]) // => { foo: { bar: 3, baz: 4 }, bar: 'yay!' }\n```\n\n\n## Options\n\n### `arrayMerge`\n\nThere are multiple ways to merge two arrays, below are a few examples but you can also create your own custom function.\n\nYour `arrayMerge` function will be called with three arguments: a `target` array, the `source` array, and an `options` object with these properties:\n\n- `isMergeableObject(value)`\n- `cloneUnlessOtherwiseSpecified(value, options)`\n\n#### `arrayMerge` example: overwrite target array\n\nOverwrites the existing array values completely rather than concatenating them:\n\n```js\nconst overwriteMerge = (destinationArray, sourceArray, options) => sourceArray\n\nmerge(\n\t[1, 2, 3],\n\t[3, 2, 1],\n\t{ arrayMerge: overwriteMerge }\n) // => [3, 2, 1]\n```\n\n#### `arrayMerge` example: combine arrays\n\nCombines objects at the same index in the two arrays.\n\nThis was the default array merging algorithm pre-version-2.0.0.\n\n```js\nconst combineMerge = (target, source, options) => {\n\tconst destination = target.slice()\n\n\tsource.forEach((item, index) => {\n\t\tif (typeof destination[index] === 'undefined') {\n\t\t\tdestination[index] = options.cloneUnlessOtherwiseSpecified(item, options)\n\t\t} else if (options.isMergeableObject(item)) {\n\t\t\tdestination[index] = merge(target[index], item, options)\n\t\t} else if (target.indexOf(item) === -1) {\n\t\t\tdestination.push(item)\n\t\t}\n\t})\n\treturn destination\n}\n\nmerge(\n\t[{ a: true }],\n\t[{ b: true }, 'ah yup'],\n\t{ arrayMerge: combineMerge }\n) // => [{ a: true, b: true }, 'ah yup']\n```\n\n### `isMergeableObject`\n\nBy default, deepmerge clones every property from almost every kind of object.\n\nYou may not want this, if your objects are of special types, and you want to copy the whole object instead of just copying its properties.\n\nYou can accomplish this by passing in a function for the `isMergeableObject` option.\n\nIf you only want to clone properties of plain objects, and ignore all \"special\" kinds of instantiated objects, you probably want to drop in [`is-plain-object`](https://github.com/jonschlinkert/is-plain-object).\n\n```js\nconst { isPlainObject } = require('is-plain-object')\n\nfunction SuperSpecial() {\n\tthis.special = 'oh yeah man totally'\n}\n\nconst instantiatedSpecialObject = new SuperSpecial()\n\nconst target = {\n\tsomeProperty: {\n\t\tcool: 'oh for sure'\n\t}\n}\n\nconst source = {\n\tsomeProperty: instantiatedSpecialObject\n}\n\nconst defaultOutput = merge(target, source)\n\ndefaultOutput.someProperty.cool // => 'oh for sure'\ndefaultOutput.someProperty.special // => 'oh yeah man totally'\ndefaultOutput.someProperty instanceof SuperSpecial // => false\n\nconst customMergeOutput = merge(target, source, {\n\tisMergeableObject: isPlainObject\n})\n\ncustomMergeOutput.someProperty.cool // => undefined\ncustomMergeOutput.someProperty.special // => 'oh yeah man totally'\ncustomMergeOutput.someProperty instanceof SuperSpecial // => true\n```\n\n### `customMerge`\n\nSpecifies a function which can be used to override the default merge behavior for a property, based on the property name.\n\nThe `customMerge` function will be passed the key for each property, and should return the function which should be used to merge the values for that property.\n\nIt may also return undefined, in which case the default merge behaviour will be used.\n\n```js\nconst alex = {\n\tname: {\n\t\tfirst: 'Alex',\n\t\tlast: 'Alexson'\n\t},\n\tpets: ['Cat', 'Parrot']\n}\n\nconst tony = {\n\tname: {\n\t\tfirst: 'Tony',\n\t\tlast: 'Tonison'\n\t},\n\tpets: ['Dog']\n}\n\nconst mergeNames = (nameA, nameB) => `${nameA.first} and ${nameB.first}`\n\nconst options = {\n\tcustomMerge: (key) => {\n\t\tif (key === 'name') {\n\t\t\treturn mergeNames\n\t\t}\n\t}\n}\n\nconst result = merge(alex, tony, options)\n\nresult.name // => 'Alex and Tony'\nresult.pets // => ['Cat', 'Parrot', 'Dog']\n```\n\n\n### `clone`\n\n*Deprecated.*\n\nDefaults to `true`.\n\nIf `clone` is `false` then child objects will be copied directly instead of being cloned.  This was the default behavior before version 2.x.\n\n\n# Testing\n\nWith [npm](http://npmjs.org) do:\n\n```sh\nnpm test\n```\n\n\n# License\n\nMIT\n", "maintainers": [{"name": "tehshrike", "email": "<EMAIL>"}], "time": {"modified": "2023-03-16T22:58:56.644Z", "created": "2012-01-31T17:35:02.203Z", "0.0.1": "2012-01-31T17:35:02.503Z", "0.1.0": "2012-03-29T00:41:29.640Z", "0.2.1": "2012-03-29T02:27:50.379Z", "0.2.4": "2012-04-15T22:54:18.210Z", "0.2.5": "2012-06-02T16:50:40.276Z", "0.2.6": "2013-05-31T02:24:46.895Z", "0.2.7": "2013-06-03T01:51:01.732Z", "0.2.10": "2015-05-20T03:20:39.542Z", "1.0.0": "2016-09-27T01:24:51.328Z", "1.0.1": "2016-09-27T01:48:27.207Z", "1.0.2": "2016-09-27T02:01:58.396Z", "1.0.3": "2016-09-29T03:04:34.265Z", "1.1.0": "2016-09-29T04:29:56.647Z", "1.1.1": "2016-10-12T02:23:03.134Z", "1.2.0": "2016-10-14T22:51:25.528Z", "1.3.0": "2016-11-12T14:43:56.759Z", "1.3.1": "2016-12-03T06:45:12.645Z", "1.3.2": "2017-01-27T16:13:58.650Z", "1.4.0": "2017-06-13T16:12:51.057Z", "1.4.1": "2017-06-13T16:17:09.617Z", "1.4.2": "2017-06-14T17:31:23.540Z", "1.4.3": "2017-06-14T17:44:51.546Z", "1.4.4": "2017-06-19T11:42:26.362Z", "1.5.0": "2017-07-06T13:46:40.399Z", "1.5.1": "2017-08-15T21:58:10.546Z", "1.5.2": "2017-09-21T14:33:08.531Z", "2.0.0": "2017-10-09T15:12:57.650Z", "2.0.1": "2017-11-01T18:37:34.318Z", "2.1.0": "2018-03-07T19:47:51.831Z", "2.1.1": "2018-05-28T15:26:21.970Z", "2.2.0": "2018-10-03T14:31:26.185Z", "2.2.1": "2018-10-03T17:58:21.098Z", "3.0.0": "2018-12-04T20:43:57.245Z", "3.1.0": "2019-01-16T22:52:47.213Z", "3.2.0": "2019-02-15T17:23:29.901Z", "3.2.1": "2019-06-14T20:15:27.757Z", "3.3.0": "2019-06-20T15:48:45.749Z", "4.0.0": "2019-07-08T15:49:40.478Z", "4.1.0": "2019-10-07T14:48:50.398Z", "4.1.1": "2019-10-08T15:06:04.798Z", "4.1.2": "2019-10-21T14:58:25.753Z", "4.2.0": "2019-10-21T15:39:58.387Z", "4.2.1": "2019-10-22T22:08:08.043Z", "4.2.2": "2019-10-28T16:19:32.062Z", "4.3.0": "2023-01-30T00:05:58.082Z", "4.3.1": "2023-03-16T22:58:56.511Z"}, "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "users": {"hij1nx": true, "matmar10": true, "lxlang": true, "eserozvataf": true, "majgis": true, "sergiodxa": true, "makaretu": true, "shavyg2": true, "jyounce": true, "waylonflinn": true, "iusfof": true, "adius": true, "ukrbublik": true, "fly19890211": true, "stjohn3d": true, "autarchprinceps": true, "xudaolong": true, "nisimjoseph": true, "joe223": true, "aaamitsingh": true, "mxl": true, "dwqs": true, "tdmalone": true, "gilbarbara": true, "gormus": true, "ssljivic": true, "drewigg": true, "lius971125": true}, "readmeFilename": "readme.md", "homepage": "https://github.com/TehShrike/deepmerge", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "license": "MIT", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"]}